﻿#include "stdafx.h"
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerSetCard.h>
#include <Backend/zone/Action/ActionEventProxy.h>
#include <Backend/zone/Action/ActionPlayer.h>
#include <Backend/zone/Transaction/ItemTransaction/ItemSetCardTransaction.h>
#include <Backend/zone/Action/ActionPassivity.h>
#include <Backend/zone/EntityFactory/EntityPlayer.h>
#include <Backend/zone/Element/Item/InvenSlot.h>
#include <Backend/zone/Element/Item/InvenEquip.h>


#ifdef __Patch_SetItem_Renewal_by_kangms_2019_4_10

namespace mu2
{	
	ActionPlayerSetCard::ActionPlayerSetCard(EntityPlayer* owner)
		: Action4Player(owner, ID)
		, reservedReviveDetach(0)
		, reservedReviveAttach(0)
	{
		CounterInc("ActionPlayerSetCard");

		this->BindEventHandlerAll();
		
		equipSetCardList.fill(0);
	}

	ActionPlayerSetCard::~ActionPlayerSetCard()
	{
		CounterDec("ActionPlayerSetCard");
	}

	void ActionPlayerSetCard::BindEventHandlerAll()
	{
		auto& eventProxy = this->GetOwnerPlayer()->GetAction<ActionEventProxy>()->GetEventProxy();
		
		DELEGATE_MEMBER_FUNC_REGISTER(eventProxy, EVENT_ID::SET_CARD_LOAD_ALL,		&ActionPlayerSetCard::OnInitSetCardList,		this);
		DELEGATE_MEMBER_FUNC_REGISTER(eventProxy, EVENT_ID::SET_CARD_OPEN,			&ActionPlayerSetCard::OnOpenSetCard,			this);
		DELEGATE_MEMBER_FUNC_REGISTER(eventProxy, EVENT_ID::SET_CARD_CHANGE,		&ActionPlayerSetCard::OnChangeSetCardEquipPos,	this);
		DELEGATE_MEMBER_FUNC_REGISTER(eventProxy, EVENT_ID::SET_CARD_EFFECT_ATTACH,	&ActionPlayerSetCard::OnEffectAttach,			this);
		DELEGATE_MEMBER_FUNC_REGISTER(eventProxy, EVENT_ID::SET_CARD_EFFECT_DETACH, &ActionPlayerSetCard::OnEffectDetach,			this);
		DELEGATE_MEMBER_FUNC_REGISTER(eventProxy, EVENT_ID::SET_CARD_OPEN_BY_LEVEL, &ActionPlayerSetCard::OnOpenSetCardByLevel,		this);
		DELEGATE_MEMBER_FUNC_REGISTER(eventProxy, EVENT_ID::SET_CARD_REVIVE_EFFECT, &ActionPlayerSetCard::OnReviveEffect,			this);
		
	}

	void ActionPlayerSetCard::OnInitSetCardList(std::vector<SetCard>& setCardList)
	{
		equipSetCardList.fill(0);

		auto actEventProxy = this->GetOwnerPlayer()->GetAction<ActionEventProxy>();

		const SetCardInfoScript* script = SCRIPTS.GetScript<SetCardInfoScript>();

		EReqDbSetCardUpdateList* reqDb = NEW EReqDbSetCardUpdateList;
		EventPtr ntfToEquipPosReset(reqDb);

		for (auto& setCard : setCardList) {

			auto elem = script->Get(setCard.index);
			if (elem == nullptr)
			{
				MU2_ERROR_LOG(LogCategory::CONTENTS
					, "invalid script data !!! : SetCardIndex:%d"
					, setCard.GetIndex());
				continue;
			}


			auto found = hadSetCardList.find(setCard.GetIndex());
			if (hadSetCardList.end() != found) {
				MU2_ERROR_LOG( LogCategory::CONTENTS
							 , "Already registered SetCard !!! : SetCardIndex:%d"
							 , setCard.GetIndex());
				continue;
			}

			if (setCard.GetSlotNo() != SetCardEquipPos::None) {
				//이미 장착된 슬롯일 경우
				if (0 != equipSetCardList[setCard.GetSlotNo()]) {

					MU2_ERROR_LOG( LogCategory::CONTENTS
								 , "Already equiped Slot !!! : EquipPos(%d) - SetCardIndex:%d"
							     , setCard.GetSlotNo(), setCard.GetIndex());

					//비장착 위치로 보정
					setCard.SetSlotNo(SetCardEquipPos::None);
					reqDb->reqSetCardList.emplace_back(setCard);
				}
			}

			auto newSetCardInfo = std::make_shared<SetCard>(setCard);

			hadSetCardList.emplace(setCard.GetIndex(), newSetCardInfo);

			//장착된 카드는 카드 장착 이벤트 통지
			if (setCard.GetSlotNo() != SetCardEquipPos::None) {

				equipSetCardList[setCard.GetSlotNo()] = setCard.GetIndex();

				actEventProxy->BroadcastMessage<void, SetCard&>(EVENT_ID::SET_CARD_EFFECT_ATTACH, setCard);
			}
		}

		if (true != reqDb->reqSetCardList.empty()) {
			reqDb->noResponse = true;
			SERVER.SendToDb(this->GetOwnerPlayer(), ntfToEquipPosReset);
		}

		this->Send_SetCardAll();
	}

	void ActionPlayerSetCard::Send_SetCardAll()
	{
		auto player = this->GetOwnerPlayer();

		EzcNtfSetCardList* ntf = NEW EzcNtfSetCardList;

		for (auto itPair : hadSetCardList) {
			SetCard& setCardInfo = (*itPair.second);
			ntf->hadSetCardList.emplace_back(setCardInfo);
		}

		SERVER.SendToClient(player, EventPtr(ntf));
	}

	void ActionPlayerSetCard::Send_SetPointList()
	{
		auto player = this->GetOwnerPlayer();

		EzcNtfSetPointList* ntf = NEW EzcNtfSetPointList;

		for (auto itPair : setPointTotalList) {
			ntf->setPointList.emplace(static_cast<UInt16>(itPair.first), itPair.second.setPoint);
		}

		SERVER.SendToClient(player, EventPtr(ntf));
	}

	void ActionPlayerSetCard::OnOpenSetCardByLevel(int) // 인자가 없으면 안되서 사용하지 않는 인자 추가
	{
		auto player = GetOwnerPlayer();
		Sector* sector = player->GetSector();
		VALID_RETURN(sector&&sector->GetInstanceDungeonInfo().IsTutorial() == false, );
		
		const SetCardInfoScript* script = SCRIPTS.GetScript<SetCardInfoScript>();
		VERIFY_RETURN(script, );
		
		auto canAutomeicOpenSetCard = [&](SetCardIndex targetSetCardIndex)->Bool
		{
			auto found = hadSetCardList.find(targetSetCardIndex);
			VALID_RETURN(hadSetCardList.end() == found, false);

			const SetCardInfoElem* elem = script->Get(targetSetCardIndex);
			if (nullptr == elem) {
				MU2_ERROR_LOG(LogCategory::CONTENTS
					, "Not found SetCardInfoElem in SetCardInfoScript !!! : SetCardIndex:%d"
					, targetSetCardIndex);
				return false;
			}
			VERIFY_RETURN(elem->materialItemList4SetCardOpen.size() == 0, false);
			// 기본 오픈 조건 체크
			VERIFY_RETURN(ClassType::COMMON == elem->classType || player->GetClassType() == elem->classType, false);
			VALID_RETURN(0 == elem->GetReqCharLevel() || player->GetLevel() >= elem->GetReqCharLevel(), false);

			return true;
		};

		const std::set<SetCardIndex>* defaultSetCardOpenList = script->GetDefaultSetCardOpenList(GetOwnerPlayer()->GetClassType());
		VERIFY_RETURN(defaultSetCardOpenList, );

		std::vector<SetCard> insertSetCards;
		
				
		for (auto defaultSetCardIndex : *defaultSetCardOpenList) {
			if (canAutomeicOpenSetCard(defaultSetCardIndex))
			{
				insertSetCards.emplace_back(SetCard(defaultSetCardIndex, SetCardEquipPos::None));
			}
		}

		if (insertSetCards.size() > 0)
		{
			EReqDbDefaultOpenSetCardListInsert* dbReq = new EReqDbDefaultOpenSetCardListInsert;
			dbReq->insertSetCards = std::move(insertSetCards);
			SERVER.SendToDb(player, EventPtr(dbReq));
		}
	}

	void ActionPlayerSetCard::OnReviveEffect(int)
	{
		auto actEventProxy = GetOwnerPlayer()->GetAction<ActionEventProxy>();

		if (reservedReviveDetach > 0)
		{
			actEventProxy->BroadcastMessage<void, SetCardIndex>(EVENT_ID::SET_CARD_EFFECT_DETACH, reservedReviveDetach);
		}

		if (reservedReviveAttach > 0)
		{
			auto found = hadSetCardList.find(reservedReviveAttach);
			if (found != hadSetCardList.end() && found->second->GetSlotNo() != SetCardEquipPos::None)
			{
				actEventProxy->BroadcastMessage<void, SetCard&>(EVENT_ID::SET_CARD_EFFECT_ATTACH, *(found->second.get()));
			}
		}
	}

	void ActionPlayerSetCard::OnOpenSetCard(SetCardIndex targetSetCardIndex)
	{
		auto player = this->GetOwnerPlayer();

		ErrorSetCard::Error error = this->checkAndOpenSetCard(targetSetCardIndex);
		if (ErrorSetCard::SUCCESS != error) {

			EzcResSetCardOpen* res = NEW EzcResSetCardOpen;
			res->eError = error;
			SERVER.SendToClient(player, EventPtr(res));
		}
	}

	ErrorSetCard::Error ActionPlayerSetCard::checkAndOpenSetCard(SetCardIndex targetSetCardIndex)
	{
		Sector* sector = GetOwnerPlayer()->GetSector();
		if (sector->GetInstanceDungeonInfo().IsTutorial())
		{
			return ErrorSetCard::MustPassTutoralState;
		}

		ErrorSetCard::Error error = ErrorSetCard::SUCCESS;

		std::map<IndexItem, UInt16> needMaterialItemList;

		error = this->checkOpenSetCard( targetSetCardIndex
			                          , __out needMaterialItemList );
		VALID_RETURN(ErrorTalisman::SUCCESS == error, error);

		error = this->reqOpenSetCard( targetSetCardIndex
			                        , needMaterialItemList );
		VERIFY_RETURN(ErrorSetCard::SUCCESS == error, error);

		return ErrorSetCard::SUCCESS;
	}

	ErrorSetCard::Error ActionPlayerSetCard::checkOpenSetCard( SetCardIndex targetSetCardIndex
		                                                     , __out std::map<IndexItem, UInt16>& needMaterialItemList )
	{
		auto player = this->GetOwnerPlayer();

		auto found = hadSetCardList.find(targetSetCardIndex);
		if (hadSetCardList.end() != found) {
			MU2_ERROR_LOG( LogCategory::CONTENTS
						 , "Already Opened SetCard !!! : SetCardIndex:%d"
						 , targetSetCardIndex);
			return ErrorSetCard::AlreadyRegisteredSetCard;
		}

		const SetCardInfoScript* script = SCRIPTS.GetScript<SetCardInfoScript>();

		const SetCardInfoElem* elem = script->Get(targetSetCardIndex);
		if (nullptr == elem) {
			MU2_ERROR_LOG( LogCategory::CONTENTS
						 , "Not found SetCardInfoElem in SetCardInfoScript !!! : SetCardIndex:%d"
						 , targetSetCardIndex);
			return ErrorSetCard::NotFoundSetCardInfoElem;
		}

		if (true != elem->IsDefaultOpen()) {

			//캐릭터 체크
			if (   ClassType::COMMON != elem->classType
				&& player->GetClassType() != elem->classType ) {
				MU2_INFO_LOG( LogCategory::CONTENTS
							, "Not matched ClassType !!! : CurrClassType(%d) < ReqClassType(%d) - SetCardIndex:%d"
							, player->GetClassType(), elem->classType
						    , targetSetCardIndex );
				return ErrorSetCard::NotMatchedClassType;
			}

			//캐릭터 레벨 체크
			if (0 < elem->GetReqCharLevel()) {
				if (player->GetLevel() < elem->GetReqCharLevel()) {
					MU2_INFO_LOG( LogCategory::CONTENTS
								, "Not enough Character Level !!! : CurrLV(%d) < ReqLV(%d) - SetCardIndex:%d"
								, player->GetLevel(), elem->GetReqCharLevel()
						        , targetSetCardIndex );
					return ErrorSetCard::NotEnoughCharLevel;
				}
			}

			//재료 아이템 체크
			if (0 < elem->GetReqMaterialItemList().size()) {

				ItemBag bag(*player);

				std::map<IndexItem, UInt16> newItemList;

				for (auto& itPairMaterialItem : elem->GetReqMaterialItemList()) {

					if (ErrorItem::SUCCESS != player->GetInventoryAction().RemoveByIndex( InvenWithPreminumsStorages
																						, __inout bag
																						, itPairMaterialItem.first, itPairMaterialItem.second )) {
						MU2_INFO_LOG( LogCategory::CONTENTS
									, "Not enough Material Item !!! : ItemIndex(%d), ItemCount(%d) - SetCardIndex:%d"
									, itPairMaterialItem.first, itPairMaterialItem.second
							        , targetSetCardIndex );

						return ErrorSetCard::NotEnoughMatrialItem;
					}

					needMaterialItemList.emplace(itPairMaterialItem.first, itPairMaterialItem.second);
				}
			}
		}

		return ErrorSetCard::SUCCESS;
	}

	ErrorSetCard::Error ActionPlayerSetCard::reqOpenSetCard( SetCardIndex targetSetCardIndex
		                                                   , std::map<IndexItem, UInt16>& needMaterialItemList )
	{
		SetCard reqOpenSetCard(targetSetCardIndex, SetCardEquipPos::None);

		ItemSetCardTransaction* trans = NEW ItemSetCardTransaction(__FUNCTION__, __LINE__, this->GetOwnerPlayer()
																  , LogCode::E_SET_CARD_OPEN
																  , reqOpenSetCard, needMaterialItemList );
		TransactionPtr transPtr(trans);
		{
			if (true != TransactionSystem::Register(transPtr)) {
				return ErrorSetCard::E_FAIL_TRANSACTION;
			}
		}

		return ErrorSetCard::SUCCESS;
	}

	void ActionPlayerSetCard::SetOpenSetCard(SetCard& setCard)
	{
		auto itFound = hadSetCardList.find(setCard.GetIndex());
		if (hadSetCardList.end() != itFound) {
			MU2_ERROR_LOG( LogCategory::CONTENTS
						 , "Already registered SetCard !!! : SetCardIndex:%d"
					     , setCard.GetIndex() );
			return;
		}

		SetCard* newSetCard = NEW SetCard(setCard);
		
		hadSetCardList.emplace(setCard.GetIndex(), SetCardInfoPtr(newSetCard));

		MU2_INFO_LOG( LogCategory::CONTENTS
					, "SetCard Open Complete !!! : SetCardIndex:%d"
					, setCard.GetIndex() );

		EzcResSetCardOpen* res = NEW EzcResSetCardOpen;
		res->eError = ErrorSetCard::SUCCESS;
		res->setCard = setCard;

		SERVER.SendToClient(this->GetOwnerPlayer(), EventPtr(res));
	}

	ErrorSetCard::Error ActionPlayerSetCard::reqCloseSetCardList(std::vector<SetCardIndex>& targetSetCardIndexList)
	{
		EReqDbSetCardDeleteList* reqDb = NEW EReqDbSetCardDeleteList;

		reqDb->reqSetCardIndexList = targetSetCardIndexList;

		SERVER.SendToDb(this->GetOwnerPlayer(), EventPtr(reqDb));

		this->SetDBProcessing(true);

		return ErrorSetCard::SUCCESS;
	}

	void ActionPlayerSetCard::CloseSetCardList(std::vector<SetCardIndex>& closedSetCardIndexList)
	{
		for (auto closedSetCardIndex : closedSetCardIndexList) {
			auto itFound = hadSetCardList.find(closedSetCardIndex);
			if (hadSetCardList.end() != itFound) {
				hadSetCardList.erase(itFound);
			}
		}
	}

	void ActionPlayerSetCard::OnChangeSetCardEquipPos(SetCardIndex sourceSetCardIndex, SetCardEquipPos::Enum targetPos)
	{
		auto player = this->GetOwnerPlayer();

		ErrorSetCard::Error error = this->checkAndChangeSetCardEquipPos(sourceSetCardIndex, targetPos);
		if (ErrorSetCard::SUCCESS != error) {

			EzcResSetCardEquip* res = NEW EzcResSetCardEquip;
			res->eError = error;
			SERVER.SendToClient(player, EventPtr(res));
		}
	}

	ErrorSetCard::Error ActionPlayerSetCard::checkAndChangeSetCardEquipPos( SetCardIndex sourceSetCardIndex, SetCardEquipPos::Enum targetPos )
	{
		ErrorSetCard::Error error = ErrorSetCard::SUCCESS;

		std::vector<SetCard> updateSetCardList;

		error = this->checkSetCardEquipPos( sourceSetCardIndex, targetPos
										  , __out updateSetCardList );
		VALID_RETURN(ErrorSetCard::SUCCESS == error, error);

		error = this->reqChangeSetCardEquipPos( updateSetCardList );
		VALID_RETURN(ErrorSetCard::SUCCESS == error, error);

		return ErrorSetCard::SUCCESS;
	}

	ErrorSetCard::Error ActionPlayerSetCard::checkSetCardEquipPos( SetCardIndex sourceSetCardIndex
		                                                         , SetCardEquipPos::Enum targetPos
	                                                             , __out std::vector<SetCard>& updateSetCardList )
	{
		if (true == this->IsDBProcessing()) {
			MU2_ERROR_LOG( LogCategory::CONTENTS
						 , "DB Processing of SetCard !!! - SetCardIndex:%d"
						 , sourceSetCardIndex );
			return ErrorSetCard::DBProcessingOfSetCard;
		}

		const SetCardInfoScript* script = SCRIPTS.GetScript<SetCardInfoScript>();

		const SetCardInfoElem* elem = script->Get(sourceSetCardIndex);
		if (nullptr == elem) {
			MU2_ERROR_LOG( LogCategory::CONTENTS
						 , "Not found SetCardInfoElem in SetCardInfoScript !!! : SetCardIndex:%d"
						 , sourceSetCardIndex );
			return ErrorSetCard::NotFoundSetCardInfoElem;
		}

		auto itFound = hadSetCardList.find(sourceSetCardIndex);
		if (hadSetCardList.end() == itFound) {
			MU2_ERROR_LOG( LogCategory::CONTENTS
						 , "Not found SetCard !!! : SetCardIndex:%d"
						 , sourceSetCardIndex );
			return ErrorSetCard::NotFoundSetCard;
		}

		SetCardInfoPtr oneSetCard = itFound->second;

		//현재 장착 위치 체크
		if (oneSetCard->GetSlotNo() == targetPos) {
			return ErrorSetCard::SameEquipPos;
		}
		else {
			//요청한 장착 위치 정보 체크
			if (SetCardEquipPos::None != targetPos) {
				if (SetPointType::EquipPosTo(elem->setPointType) != targetPos) {
					return ErrorSetCard::InvalidEquipPos;
				}
			}
		}

		//대상 지점이 장착 슬롯일 경우
		if ( SetCardEquipPos::None != targetPos ) {
			// 대상 지점에 장착 세트카드가 있는 경우
			if( 0 < equipSetCardList[targetPos] ) {

				//장착된 세트 카드가 있는 경우는 Swap 처리 해준다 !!!, SetCardEquipPos::None <-> SetCardEquipPos::?
				SetCardIndex twoSetCardIndex = equipSetCardList[targetPos];
			
				auto itFoundOther = hadSetCardList.find(twoSetCardIndex);

				SetCardInfoPtr twoSetCard = itFoundOther->second;
				SetCardEquipPos::Enum twoSetCardPos = twoSetCard->GetSlotNo();

				if (twoSetCardPos != targetPos) {
					MU2_ERROR_LOG( LogCategory::CONTENTS
								 , "Not matched Equip Pos of SetCard !!! : TwoSetCardEquip(%d) != TargetEquip(%d) - TwoSetCardIndex:%d"
								 , twoSetCardPos, targetPos, twoSetCardIndex);
					return ErrorSetCard::NotMatchedEquipPos;
				}

				//대상 위치의 세트 카드 장착 위치를 One 세트 카드의 위치로 요청 설정
				SetCard moveSetCard(twoSetCard->GetIndex(), oneSetCard->GetSlotNo());
				updateSetCardList.emplace_back(moveSetCard);
			}
		}
		
		SetCard updateSetCard(oneSetCard->GetIndex(), targetPos);
		updateSetCardList.emplace_back(updateSetCard);

		return ErrorSetCard::SUCCESS;
	}

	ErrorSetCard::Error ActionPlayerSetCard::reqChangeSetCardEquipPos( std::vector<SetCard>& reqSetCardList )
	{
		EReqDbSetCardUpdateList* reqDb = NEW EReqDbSetCardUpdateList;

		reqDb->reqSetCardList = reqSetCardList;

		SERVER.SendToDb(this->GetOwnerPlayer(), EventPtr(reqDb));

		this->SetDBProcessing(true);

		return ErrorSetCard::SUCCESS;
	}

	void ActionPlayerSetCard::SetSetCardEquipPosList(std::vector<SetCard>& updatedSetCardList)
	{
		auto actEventProxy = this->GetOwnerPlayer()->GetAction<ActionEventProxy>();

		for (auto& setCard : updatedSetCardList) {

			auto itFound = hadSetCardList.find(setCard.GetIndex());
			if (hadSetCardList.end() == itFound) {
				MU2_ERROR_LOG( LogCategory::CONTENTS
							 , "Not found SetCard !!! : SetCardIndex:%d"
							 , setCard.GetIndex() );
				continue;
			}
			
			SetCardInfoPtr beforeSetCard = itFound->second;

			
			//장착 해제 처리
			if (   SetCardEquipPos::None != beforeSetCard->GetSlotNo()
				&& SetCardEquipPos::None == setCard.GetSlotNo() ) {

				equipSetCardList[beforeSetCard->GetSlotNo()] = 0;

				actEventProxy->BroadcastMessage<void, SetCardIndex>(EVENT_ID::SET_CARD_EFFECT_DETACH, beforeSetCard->GetIndex());
			}

			//장착 처리
			if (    SetCardEquipPos::None == beforeSetCard->GetSlotNo()
				 && SetCardEquipPos::None != setCard.GetSlotNo()) {

				equipSetCardList[setCard.GetSlotNo()] = setCard.GetIndex();

				actEventProxy->BroadcastMessage<void, SetCard&>(EVENT_ID::SET_CARD_EFFECT_ATTACH, setCard);
			}

			MU2_INFO_LOG( LogCategory::CONTENTS
						, "SetCard Equip change Complete !!! : PrevEquip(%d) -> NewEquip(%d) - SetCardIndex:%d"
						, beforeSetCard->GetSlotNo(), setCard.GetSlotNo(), setCard.GetIndex() );

			//장착 위치 갱신 !!!
			itFound->second->SetSlotNo(setCard.GetSlotNo());

			auto log = NEW EReqLogDb2;
			EventSetter::FillLogForEntity(LogCode::E_SET_CARD_EQUIP_POS, GetOwnerPlayer(), log->action);
			log->action.var32s.push_back(setCard.GetIndex());
			log->action.var32s.push_back(setCard.GetSlotNo());
			SendDataCenter(EventPtr(log));
		}

		EzcResSetCardEquip* resToClient = NEW EzcResSetCardEquip;
		resToClient->eError = ErrorSetCard::SUCCESS;
		resToClient->updatedSetCardList = updatedSetCardList;

		SERVER.SendToClient(this->GetOwnerPlayer(), EventPtr(resToClient));
	}

	void ActionPlayerSetCard::OnEffectAttach(SetCard& setCard)
	{
		VERIFY_RETURN(setCard.GetIndex() > 0 && setCard.GetSlotNo() != SetCardEquipPos::None, );
		if (GetOwnerPlayer()->IsDead())
		{
			VERIFY_DO(reservedReviveAttach == 0, ); // 이미 예약되어 있으면 2개가 들어온 경우인데..
			reservedReviveAttach = setCard.GetIndex();
			return;
		}

		// 예약된것이 있는 경우 요청한 것과 동일해야 정상이다.
		VERIFY_DO(reservedReviveAttach == 0 || reservedReviveAttach == setCard.GetIndex(), );
		reservedReviveAttach = 0;

		const SetCardInfoScript* script = SCRIPTS.GetScript<SetCardInfoScript>();

		const SetCardInfoElem* elem = script->Get(setCard.GetIndex());
		if (nullptr == elem) {
			MU2_ERROR_LOG( LogCategory::CONTENTS
						 , "Not found SetCardInfoElem in SetCardInfoScript !!! : SetCardIndex:%d"
						 , setCard.GetIndex() );
			return;
		}

		UInt32 setPointTotal = this->GetSetPoint(elem->setPointType);

		const SetCardEffectScript* effectScript = SCRIPTS.GetScript<SetCardEffectScript>();

		const std::vector<UInt16>* divisionList = effectScript->GetDivisionList(setCard.GetIndex());
		if (nullptr == divisionList) {
			MU2_ERROR_LOG( LogCategory::CONTENTS
						 , "Not found DivisionList in SetCardEffectScript !!! : SetCardIndex:%d"
						 , setCard.GetIndex() );
			return;
		}

		Bool bCalculate = false;

		auto player = this->GetOwnerPlayer();
		
		for (auto divisionNo : *divisionList) {

			SetCardEffectKey key(setCard.GetIndex(), divisionNo);

			const SetCardEffectElem* effectElem = effectScript->Get(key);
			if (nullptr == effectElem) {
				MU2_ERROR_LOG( LogCategory::CONTENTS
							 , "Not found SetCardEffectElem in SetCardEffectScript !!! : SetCardIndex(%d), DivisionNo(%d)"
							 , setCard.GetIndex(), divisionNo );
				continue;
			}

			if (setPointTotal >= effectElem->setPoint) {

				if (true != this->IsHadEffect(SetPointType::EquipPosTo(elem->setPointType), divisionNo)) {

					if (true == this->AttachEffect(setCard.GetIndex(), divisionNo)) {
						bCalculate = true;
					}
				}
			}
		}

		if (true == bCalculate) {
			player->GetAbilityAction().Calculate();
		}
	}

	void ActionPlayerSetCard::OnEffectDetach(SetCardIndex setCardIndex)
	{
		VERIFY_RETURN(0 < setCardIndex, );

		if (GetOwnerPlayer()->IsDead())
		{
			VERIFY_DO(reservedReviveDetach == 0, ); // 이미 예약되어 있으면 2개가 들어온 경우인데..

			auto itFound = hadSetCardList.find(setCardIndex);
			if (hadSetCardList.end() == itFound) {
				MU2_ERROR_LOG(LogCategory::CONTENTS
					, "Not found SetCard !!! : SetCardIndex(%d)"
					, setCardIndex);
				return;
			}

			SetCardEquipPos::Enum detachPos = itFound->second->GetSlotNo();
			if (detachPos == SetCardEquipPos::None) {
				MU2_ERROR_LOG(LogCategory::CONTENTS
					, "Not Equip Pos of SetCard !!! - SetCardIndex:%d"
					, setCardIndex);
				return;
			}

			reservedReviveDetach = setCardIndex;
			reservedReviveDetachPos = detachPos;
			return;
		}

		// 예약된것이 있는 경우 요청한 것과 동일해야 정상이다.
		VERIFY_DO(reservedReviveDetach == 0 || reservedReviveDetach == setCardIndex, );
		
		const SetCardInfoScript* script = SCRIPTS.GetScript<SetCardInfoScript>();

		const SetCardInfoElem* elem = script->Get(setCardIndex);
		if (nullptr == elem) {
			MU2_ERROR_LOG( LogCategory::CONTENTS
						 , "Not found SetCardInfoElem in SetCardInfoScript !!! : SetCardIndex(%d)"
						 , setCardIndex );
			return;
		}

		auto itFound = hadSetCardList.find(setCardIndex);
		if (hadSetCardList.end() == itFound) {
			MU2_ERROR_LOG( LogCategory::CONTENTS
						 , "Not found SetCard !!! : SetCardIndex(%d)"
						 , setCardIndex );
			return;
		}
		
		SetCardEquipPos::Enum detachPos = (reservedReviveDetach > 0 && reservedReviveDetachPos != SetCardEquipPos::None) ? reservedReviveDetachPos : itFound->second->GetSlotNo();		
		if (detachPos == SetCardEquipPos::None) {
			MU2_ERROR_LOG( LogCategory::CONTENTS
						 , "Not Equip Pos of SetCard !!! - SetCardIndex:%d"
						 , setCardIndex );
			return;
		}

		const SetCardEffectScript* effectScript = SCRIPTS.GetScript<SetCardEffectScript>();

		const std::vector<UInt16>* divisionList = effectScript->GetDivisionList(setCardIndex);
		if (nullptr == divisionList) {
			MU2_ERROR_LOG( LogCategory::CONTENTS
						 , "Not found DivisionList in SetCardEffectScript !!! : SetCardIndex(%d)"
						 , setCardIndex );
			return;
		}

		Bool bCalculate = false;

		auto player = this->GetOwnerPlayer();
		
		for (auto divisionNo : *divisionList) {

			if (true == this->DetachEffect(setCardIndex, divisionNo)) {
				bCalculate = true;
			}
		}

		if (true == bCalculate) {
			player->GetAbilityAction().Calculate();
		}
	}

	void ActionPlayerSetCard::incSetPoint(SetPointType::Enum type, Int32 value)
	{
		VALID_RETURN(true == SetPointType::IsValid(type, false), );
		VALID_RETURN(0 < value, );

		auto itFound = setPointTotalList.find(type);
		if (setPointTotalList.end() != itFound) {
			itFound->second.setPoint += value;
			itFound->second.isUpdate = true;
		}
		else {
			SetPointInfo setPointInfo( value, true );
			setPointTotalList.emplace(type, setPointInfo);
		}

		auto itPos = setPointTotalList.find(type);

		auto log = NEW EReqLogDb2;
		EventSetter::FillLogForEntity(LogCode::E_SET_POINT_UPDATE, GetOwnerPlayer(), log->action);
		log->action.var32s.push_back(static_cast<UInt32>(itPos->first));
		log->action.var32s.push_back(itPos->second.setPoint);
		log->action.var32s.push_back(value);
		SendDataCenter(EventPtr(log));

		this->Send_SetPointList();
	}

	void ActionPlayerSetCard::decSetPoint(SetPointType::Enum type, Int32 value)
	{
		VALID_RETURN(true == SetPointType::IsValid(type, false), );
		VALID_RETURN(0 < value, );

		auto itFound = setPointTotalList.find(type);
		if (setPointTotalList.end() != itFound) {
			itFound->second.setPoint -= value;
			itFound->second.isUpdate = true;
		}
		else {
			SetPointInfo setPointInfo(value, true);
			setPointTotalList.emplace(type, setPointInfo);
		}

		auto itPos = setPointTotalList.find(type);

		auto log = NEW EReqLogDb2;
		EventSetter::FillLogForEntity(LogCode::E_SET_POINT_UPDATE, GetOwnerPlayer(), log->action);
		log->action.var32s.push_back(static_cast<UInt32>(itPos->first));
		log->action.var32s.push_back(itPos->second.setPoint);
		log->action.var32s.push_back(-value);
		SendDataCenter(EventPtr(log));

		this->Send_SetPointList();
	}

	void ActionPlayerSetCard::ResetSetPointAll()
	{
		for (auto& itPair : setPointTotalList) {
			itPair.second.setPoint = 0;
		}
	}

	UInt32 ActionPlayerSetCard::GetSetPoint(SetPointType::Enum type)
	{
		auto itFound = setPointTotalList.find(type);
		if (setPointTotalList.end() == itFound) {
			return 0;
		}

		return itFound->second.setPoint;
	}

	void ActionPlayerSetCard::SetSetPoint(SetPointType::Enum type, Int32 value)
	{
		auto itFound = setPointTotalList.find(type);
		if (setPointTotalList.end() == itFound) {
			return;
		}

		itFound->second.setPoint = value;
	}

	Bool ActionPlayerSetCard::IsHadEffect(SetCardEquipPos::Enum equipPos, UInt16 divisionNo)
	{
		auto itDivisionFound = appliedEffectDivisionList.equal_range(equipPos);
		for (auto itPos = itDivisionFound.first; itPos != itDivisionFound.second; ++itPos) {
			if (itPos->second == divisionNo) {
				return true;
			}
		}

		return false;
	}

	void ActionPlayerSetCard::UpdateEffectList()
	{
		auto player = this->GetOwnerPlayer();

		bool bCalculate = false;

		for (auto itPair : setPointTotalList) {
			
			if (true != itPair.second.isUpdate) {
				continue;
			}

			itPair.second.isUpdate = false; //세트카드 효과 갱신 완료 설정

			SetPointType::Enum type = itPair.first;

			VERIFY_DO(SetPointType::None != type, continue);

			SetCardEquipPos::Enum equipPos = SetPointType::EquipPosTo(type);

			SetCardIndex equipedSetCardIndex = equipSetCardList[equipPos];
			if (0 >= equipedSetCardIndex) {
				continue;
			}

			UInt32 setPointTotal = this->GetSetPoint(type);

			const SetCardEffectScript* effectScript = SCRIPTS.GetScript<SetCardEffectScript>();

			const std::vector<UInt16>* divisionList = effectScript->GetDivisionList(equipedSetCardIndex);
			if (nullptr == divisionList) {
				MU2_ERROR_LOG( LogCategory::CONTENTS
							 , "Not found DivisionList in SetCardEffectScript !!! : SetCardIndex:%d"
							 , equipedSetCardIndex);
				continue;;
			}

			for (auto divisionNo : *divisionList) {

				SetCardEffectKey key(equipedSetCardIndex, divisionNo);

				const SetCardEffectElem* effectElem = effectScript->Get(key);
				if (nullptr == effectElem) {
					MU2_ERROR_LOG( LogCategory::CONTENTS
								 , "Not found SetCardEffectElem in SetCardEffectScript !!! : SetCardIndex(%d), DivisionNo(%d)"
						         , equipedSetCardIndex, divisionNo);
					return;
				}

				//효과를 추가할 경우
				if (setPointTotal >= effectElem->setPoint) {
					if (true != this->IsHadEffect(equipPos, divisionNo)) {

						if (true == this->AttachEffect(equipedSetCardIndex, divisionNo)) {
							bCalculate = true;
						}
					}
				}
				//효과를 제거할 경우
				else {
					if (true == this->IsHadEffect(equipPos, divisionNo)) {

						if (true == this->DetachEffect(equipedSetCardIndex, divisionNo)) {
							bCalculate = true;
						}
					}
				}
			}
		}

		if (true == bCalculate) {
			player->GetAbilityAction().Calculate();
		}
	}

	Bool ActionPlayerSetCard::TestCloseSetCard(SetCardIndex setCardIndex)
	{
		if (true == this->IsDBProcessing()) {
			MU2_ERROR_LOG( LogCategory::CONTENTS
				         , "DB Processing of SetCard !!! - SetCardIndex:%d"
				         , setCardIndex );
			return false;
		}

		auto itFound = hadSetCardList.find(setCardIndex);
		if (hadSetCardList.end() == itFound) {
			MU2_ERROR_LOG( LogCategory::CONTENTS
				         , "Not found SetCard !!! - SetCardIndex:%d"
				         , setCardIndex );
			return false;
		}

		SetCardEquipPos::Enum currPos = itFound->second->GetSlotNo();
		if (currPos != SetCardEquipPos::None) {
			MU2_ERROR_LOG( LogCategory::CONTENTS
				         , "Equiped SetCard !!! : CurrPos(%d) - SetCardIndex:%d"
				         , currPos, setCardIndex );
			return false;
		}
		
		std::vector<SetCardIndex> reqCloseSetCardIndexList;
		reqCloseSetCardIndexList.emplace_back(setCardIndex);

		return ErrorSetCard::SUCCESS == this->reqCloseSetCardList(reqCloseSetCardIndexList);
	}

	Bool ActionPlayerSetCard::TestCloseSetCardAll()
	{
		if (true == IsDBProcessing()) {
			MU2_ERROR_LOG( LogCategory::CONTENTS
				         , "DB Processing of SetCard !!!" );
			return false;
		}
		
		std::vector<SetCardIndex> reqCloseSetCardIndexList;

		for (auto& itPair : hadSetCardList) {

			SetCardEquipPos::Enum currPos = itPair.second->GetSlotNo();
			if (currPos != SetCardEquipPos::None) {
				MU2_WARN_LOG( LogCategory::CONTENTS
					        , "Equiped SetCard !!! : CurrPos(%d) - SetCardIndex:%d"
					        , currPos, itPair.second->GetIndex());
				continue;
			}

			reqCloseSetCardIndexList.emplace_back(itPair.second->GetIndex());
		}

		return ErrorSetCard::SUCCESS == this->reqCloseSetCardList(reqCloseSetCardIndexList);
	}

	Bool ActionPlayerSetCard::TestChangeSetPoint(SetPointType::Enum setPointType, UInt32 setPointValue)
	{
		this->SetSetPoint(setPointType, setPointValue);

		this->UpdateEffectList();

		return true;
	}

	Bool ActionPlayerSetCard::TestCalculateSetPoint()
	{
		auto player = this->GetOwnerPlayer();

		this->ResetSetPointAll();

		Items equips;
		player->GetEquipInven().GetItemAll(__out equips);

		for (auto& equipedItemInfo : equips) {
			if (!equipedItemInfo->IsBroken())
			{
				incSetPoint(equipedItemInfo->GetSetPointType(), equipedItemInfo->GetSetPointValue());
			}
		}

		this->UpdateEffectList();

		return true;
	}

	Bool ActionPlayerSetCard::AttachEffect(SetCardIndex setCardIndex, UInt16 divisionNo)
	{
		Bool bResult = false;

		auto player = this->GetOwnerPlayer();

		const SetCardInfoScript* infoScript = SCRIPTS.GetScript<SetCardInfoScript>();
		const SetCardInfoElem* infoElem = infoScript->Get(setCardIndex);
		if (nullptr == infoElem) {
			MU2_ERROR_LOG( LogCategory::CONTENTS
						 , "Not found SetCardInfoElem in SetCardInfoScript !!! : SetCardIndex(%d)"
						 , setCardIndex);
			return false;
		}

		SetCardEffectKey key(setCardIndex, divisionNo);

		const SetCardEffectScript* effectScript = SCRIPTS.GetScript<SetCardEffectScript>();
		const SetCardEffectElem* effectElem = effectScript->Get(key);
		if (nullptr == effectElem) {
			MU2_ERROR_LOG( LogCategory::CONTENTS
						 , "Not found SetCardEffectElem in SetCardEffectScript !!! : SetCardIndex(%d), DivisionNo(%d)"
						 , setCardIndex, divisionNo );
			return false;
		}

		for (auto itPairEffect : effectElem->effectAbilityList) {

			EAT::Enum abilityType = itPairEffect.first;
			SetCardEffectElem::AbilityInfo& abilityInfo = itPairEffect.second;

			if (EAT::PASSIVE_SKILL == abilityType) {

				if (true == player->GetPassivityAction().IsExistPassivity(abilityInfo.value)) {
					continue;
				}
				player->GetPassivityAction().Apply(abilityInfo.value);
			}
			else {

				AbilityParam param;
				param.isOnOff = true;
				param.type = abilityInfo.entityAbilityType;
				param.section = abilityInfo.effectSectionType;
				param.value = abilityInfo.value;

				player->GetAbilityAction().ChangedSkillAbility(param);
			}

			bResult = true;
		}

		appliedEffectDivisionList.emplace(SetPointType::EquipPosTo(infoElem->setPointType), key.divisionNo);

		auto log = NEW EReqLogDb2;
		EventSetter::FillLogForEntity(LogCode::E_SET_POINT_EFFECT_ATTACH, player, log->action);
		log->action.var32s.push_back(key.index);
		log->action.var32s.push_back(key.divisionNo);
		log->action.var32s.push_back(infoElem->setPointType);
		log->action.var32s.push_back(effectElem->setPoint);
		log->action.var32s.push_back(this->GetSetPoint(infoElem->setPointType));
		SendDataCenter(EventPtr(log));

		return bResult;
	}

	Bool ActionPlayerSetCard::DetachEffect(SetCardIndex setCardIndex, UInt16 divisionNo)
	{
		Bool bResult = false;

		auto player = this->GetOwnerPlayer();

		const SetCardInfoScript* infoScript = SCRIPTS.GetScript<SetCardInfoScript>();
		const SetCardInfoElem* infoElem = infoScript->Get(setCardIndex);
		if (nullptr == infoElem) {
			MU2_ERROR_LOG( LogCategory::CONTENTS
						 , "Not found SetCardInfoElem in SetCardInfoScript !!! : SetCardIndex(%d)"
						 , setCardIndex);
			return false;
		}

		auto found = appliedEffectDivisionList.find(SetPointType::EquipPosTo(infoElem->setPointType));
		if (found == appliedEffectDivisionList.end())
			return false;

		SetCardEffectKey key(setCardIndex, divisionNo);

		const SetCardEffectScript* effectScript = SCRIPTS.GetScript<SetCardEffectScript>();
		const SetCardEffectElem* effectElem = effectScript->Get(key);
		if (nullptr == effectElem) {
			MU2_ERROR_LOG( LogCategory::CONTENTS
						 , "Not found SetCardEffectElem in SetCardEffectScript !!! : SetCardIndex(%d), DivisionNo(%d)"
				         , setCardIndex, divisionNo );
			return false;
		}

		for (auto itPairEffect : effectElem->effectAbilityList) {

			EAT::Enum abilityType = itPairEffect.first;
			SetCardEffectElem::AbilityInfo& abilityInfo = itPairEffect.second;

			if (EAT::PASSIVE_SKILL == abilityType) {
				if (true != player->GetPassivityAction().IsExistPassivity(abilityInfo.value)) {
					continue;
				}

				player->GetPassivityAction().Cancel(abilityInfo.value, false);
			}
			else {
				AbilityParam param;
				param.isOnOff = false;
				param.type = abilityInfo.entityAbilityType;
				param.section = abilityInfo.effectSectionType;
				param.value = abilityInfo.value;

				player->GetAbilityAction().ChangedSkillAbility(param);
			}

			bResult = true;
		}

		auto itDivisionFound = appliedEffectDivisionList.equal_range(SetPointType::EquipPosTo(infoElem->setPointType));
		for (auto itPos = itDivisionFound.first; itPos != itDivisionFound.second; ) {
			if (itPos->second == divisionNo) {
				itPos = appliedEffectDivisionList.erase(itPos);
			}
			else {
				++itPos;
			}
		}

		auto log = NEW EReqLogDb2;
		EventSetter::FillLogForEntity(LogCode::E_SET_POINT_EFFECT_DETACH, player, log->action);
		log->action.var32s.push_back(key.index);
		log->action.var32s.push_back(key.divisionNo);
		log->action.var32s.push_back(infoElem->setPointType);
		log->action.var32s.push_back(effectElem->setPoint);
		log->action.var32s.push_back(this->GetSetPoint(infoElem->setPointType));
		SendDataCenter(EventPtr(log));

		return bResult;
	}

} // namespace mu2

#endif//__Patch_SetItem_Renewal_by_kangms_2019_4_10


