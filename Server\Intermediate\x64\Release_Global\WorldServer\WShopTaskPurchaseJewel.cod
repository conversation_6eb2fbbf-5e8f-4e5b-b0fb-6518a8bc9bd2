; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	??0exception@std@@QEAA@AEBV01@@Z		; std::exception::exception
PUBLIC	?what@exception@std@@UEBAPEBDXZ			; std::exception::what
PUBLIC	??_Gexception@std@@UEAAPEAXI@Z			; std::exception::`scalar deleting destructor'
PUBLIC	??0bad_alloc@std@@QEAA@AEBV01@@Z		; std::bad_alloc::bad_alloc
PUBLIC	??_Gbad_alloc@std@@UEAAPEAXI@Z			; std::bad_alloc::`scalar deleting destructor'
PUBL<PERSON>	??0bad_array_new_length@std@@QEAA@XZ		; std::bad_array_new_length::bad_array_new_length
PUBLIC	??1bad_array_new_length@std@@UEAA@XZ		; std::bad_array_new_length::~bad_array_new_length
PUBLIC	??0bad_array_new_length@std@@QEAA@AEBV01@@Z	; std::bad_array_new_length::bad_array_new_length
PUBLIC	??_Gbad_array_new_length@std@@UEAAPEAXI@Z	; std::bad_array_new_length::`scalar deleting destructor'
PUBLIC	?_Throw_bad_array_new_length@std@@YAXXZ		; std::_Throw_bad_array_new_length
PUBLIC	?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
PUBLIC	?_Xlen_string@std@@YAXXZ			; std::_Xlen_string
PUBLIC	?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z	; std::allocator<wchar_t>::allocate
PUBLIC	?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEAAPEA_WXZ ; std::_String_val<std::_Simple_types<wchar_t> >::_Myptr
PUBLIC	?_Xran@?$_String_val@U?$_Simple_types@_W@std@@@std@@SAXXZ ; std::_String_val<std::_Simple_types<wchar_t> >::_Xran
PUBLIC	?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_empty
PUBLIC	??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@$$QEAV01@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
PUBLIC	?_Take_contents@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXAEAV12@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Take_contents
PUBLIC	??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
PUBLIC	?insert@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV12@_KQEB_W0@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::insert
PUBLIC	?c_str@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBAPEB_WXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::c_str
PUBLIC	?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
PUBLIC	?_Calculate_growth@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@CA_K_K00@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Calculate_growth
PUBLIC	?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate
PUBLIC	??1?$_Compressed_pair@V?$allocator@_W@std@@V?$_String_val@U?$_Simple_types@_W@std@@@2@$00@std@@QEAA@XZ ; std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>::~_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>
PUBLIC	??$_Integral_to_string@_W_K@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@0@_K@Z ; std::_Integral_to_string<wchar_t,unsigned __int64>
PUBLIC	??$?H_WU?$char_traits@_W@std@@V?$allocator@_W@1@@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@0@QEB_W$$QEAV10@@Z ; std::operator+<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
PUBLIC	?GetFcsGameLvel@EntityPlayer@mu2@@QEBAHXZ	; mu2::EntityPlayer::GetFcsGameLvel
PUBLIC	?fillItemResult@WShopTask@mu2@@MEAAXXZ		; mu2::WShopTask::fillItemResult
PUBLIC	??$?0PEA_W$0A@@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@PEA_W0AEBV?$allocator@_W@1@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> ><wchar_t *,0>
PUBLIC	??$_UIntegral_to_buff@_W_K@std@@YAPEA_WPEA_W_K@Z ; std::_UIntegral_to_buff<wchar_t,unsigned __int64>
PUBLIC	??$_Construct@$00PEA_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEA_W_K@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<1,wchar_t *>
PUBLIC	??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
PUBLIC	??$_Reallocate_grow_by@V<lambda_967c2ed818824c5314a20ec3af46b793>@@_KPEB_W_K@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_967c2ed818824c5314a20ec3af46b793>@@_KPEB_W2@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Reallocate_grow_by<<lambda_967c2ed818824c5314a20ec3af46b793>,unsigned __int64,wchar_t const *,unsigned __int64>
PUBLIC	??0WShopTaskPurchaseJewel@mu2@@QEAA@PEAVWShopJob@1@@Z ; mu2::WShopTaskPurchaseJewel::WShopTaskPurchaseJewel
PUBLIC	??1WShopTaskPurchaseJewel@mu2@@UEAA@XZ		; mu2::WShopTaskPurchaseJewel::~WShopTaskPurchaseJewel
PUBLIC	?Response@WShopTaskPurchaseJewel@mu2@@UEAA?AW4Error@ErrorWShop@2@PEAVIResponseParent@fcsa@@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ; mu2::WShopTaskPurchaseJewel::Response
PUBLIC	?SetResultAndReturn@WShopTaskPurchaseJewel@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@W4342@@Z ; mu2::WShopTaskPurchaseJewel::SetResultAndReturn
PUBLIC	?Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z ; mu2::WShopTaskPurchaseJewel::Setup
PUBLIC	?sendBeginRequest@WShopTaskPurchaseJewel@mu2@@MEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ; mu2::WShopTaskPurchaseJewel::sendBeginRequest
PUBLIC	?sendConfirmRequest@WShopTaskPurchaseJewel@mu2@@MEAA?AW4Error@ErrorWShop@2@XZ ; mu2::WShopTaskPurchaseJewel::sendConfirmRequest
PUBLIC	?recv@WShopTaskPurchaseJewel@mu2@@AEAA?AW4Error@ErrorWShop@2@PEAVIResponsePurchaseJewelItem@fcsa@@@Z ; mu2::WShopTaskPurchaseJewel::recv
PUBLIC	?recv@WShopTaskPurchaseJewel@mu2@@AEAA?AW4Error@ErrorWShop@2@PEAVIResponseConfirmJewelPurchase@fcsa@@@Z ; mu2::WShopTaskPurchaseJewel::recv
PUBLIC	??_GWShopTaskPurchaseJewel@mu2@@UEAAPEAXI@Z	; mu2::WShopTaskPurchaseJewel::`scalar deleting destructor'
PUBLIC	??_7exception@std@@6B@				; std::exception::`vftable'
PUBLIC	??_C@_0BC@EOODALEL@Unknown?5exception@		; `string'
PUBLIC	??_7bad_alloc@std@@6B@				; std::bad_alloc::`vftable'
PUBLIC	??_7bad_array_new_length@std@@6B@		; std::bad_array_new_length::`vftable'
PUBLIC	??_C@_0BF@KINCDENJ@bad?5array?5new?5length@	; `string'
PUBLIC	??_R0?AVexception@std@@@8			; std::exception `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
PUBLIC	_TI3?AVbad_array_new_length@std@@
PUBLIC	_CTA3?AVbad_array_new_length@std@@
PUBLIC	??_R0?AVbad_array_new_length@std@@@8		; std::bad_array_new_length `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
PUBLIC	??_R0?AVbad_alloc@std@@@8			; std::bad_alloc `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
PUBLIC	??_C@_0BA@JFNIOLAK@string?5too?5long@		; `string'
PUBLIC	??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@ ; `string'
PUBLIC	??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@		; `string'
PUBLIC	??_C@_09OLBBGJEO@Assertion@			; `string'
PUBLIC	?inst@?$ISingleton@VGameMsg@mu2@@@mu2@@1VGameMsg@2@A ; mu2::ISingleton<mu2::GameMsg>::inst
PUBLIC	??_C@_0BI@CFPLBAOH@invalid?5string?5position@	; `string'
PUBLIC	??_R4exception@std@@6B@				; std::exception::`RTTI Complete Object Locator'
PUBLIC	??_R3exception@std@@8				; std::exception::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2exception@std@@8				; std::exception::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@exception@std@@8			; std::exception::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4bad_array_new_length@std@@6B@		; std::bad_array_new_length::`RTTI Complete Object Locator'
PUBLIC	??_R3bad_array_new_length@std@@8		; std::bad_array_new_length::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2bad_array_new_length@std@@8		; std::bad_array_new_length::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@bad_array_new_length@std@@8	; std::bad_array_new_length::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@bad_alloc@std@@8			; std::bad_alloc::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R3bad_alloc@std@@8				; std::bad_alloc::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2bad_alloc@std@@8				; std::bad_alloc::`RTTI Base Class Array'
PUBLIC	??_R4bad_alloc@std@@6B@				; std::bad_alloc::`RTTI Complete Object Locator'
PUBLIC	??_7WShopTaskPurchaseJewel@mu2@@6B@		; mu2::WShopTaskPurchaseJewel::`vftable'
PUBLIC	?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A ; mu2::ISingleton<mu2::WShop>::inst
PUBLIC	??_C@_0FL@OJHBMBAH@F?3?2Release_Branch?2Server?2Develo@ ; `string'
PUBLIC	??_C@_05HEIBENID@owner@				; `string'
PUBLIC	??_C@_0CD@FPCNIDPM@mu2?3?3WShopTaskPurchaseJewel?3?3Se@ ; `string'
PUBLIC	??_C@_0CF@IGILGBB@owner?9?$DOGetFcsAuthInfo?$CI?$CJ?4fcsAcco@ ; `string'
PUBLIC	??_C@_0L@JNPHBBNK@wShopJewel@			; `string'
PUBLIC	??_C@_13CKBCJILK@?$AAJ@				; `string'
PUBLIC	??_C@_1CE@KMFGEGPK@?$AAd?$AAe?$AAv?$AA?9?$AAp?$AAu?$AAr?$AAc?$AAh?$AAa?$AAs?$AAe?$AAJ?$AAe?$AAw@ ; `string'
PUBLIC	??_C@_1BK@JKMOFEE@?$AAd?$AAe?$AAv?$AA?9?$AAd?$AAe?$AAc?$AAr?$AAe?$AAa?$AAs?$AAe@ ; `string'
PUBLIC	??_C@_0DC@PGFKHDPB@WShopTaskPurchaseJewel?3?3Respons@ ; `string'
PUBLIC	??_C@_0DF@HOBIDHFN@?$CB?$CCWShopTaskPurchaseJewel?3?3Respo@ ; `string'
PUBLIC	??_C@_0CC@EFIHDMCI@WSHOP?$CI?$CFu?$CJ?0?$CFllu?5?3?5sendAsync?5?$CI?$CFd?$CJ@ ; `string'
PUBLIC	??_C@_0CO@LOJFDACN@mu2?3?3WShopTaskPurchaseJewel?3?3se@ ; `string'
PUBLIC	??_C@_0N@MALEHAKP@beginRequest@			; `string'
PUBLIC	??_C@_0CC@LGOLGFIH@mu2?3?3WShopTaskPurchaseJewel?3?3re@ ; `string'
PUBLIC	??_C@_1II@HMCPJHON@?$AAW?$AAS?$AAH?$AAO?$AAP?$AA?$CI?$AA?$CF?$AAu?$AA?$CJ?$AA?0?$AA?$CF?$AAl?$AAl?$AAu?$AA?5@ ; `string'
PUBLIC	??_C@_1HI@IDJCKHMJ@?$AAP?$AAu?$AAr?$AAc?$AAh?$AAa?$AAs?$AAe?$AAJ?$AAe?$AAw?$AAe?$AAl?$AA?5?$AA0@ ; `string'
PUBLIC	??_C@_0DA@LHOBOCCA@mu2?3?3WShopTaskPurchaseJewel?3?3se@ ; `string'
PUBLIC	??_C@_1FO@BAMCKIJN@?$AAP?$AAu?$AAr?$AAc?$AAh?$AAa?$AAs?$AAe?$AAJ?$AAe?$AAw?$AAe?$AAl?$AA?5?$AA1@ ; `string'
PUBLIC	??_C@_1LE@LEGCOMOK@?$AAP?$AAu?$AAr?$AAc?$AAh?$AAa?$AAs?$AAe?$AAJ?$AAe?$AAw?$AAe?$AAl?$AA?5?$AA0@ ; `string'
PUBLIC	??_R4WShopTaskPurchaseJewel@mu2@@6B@		; mu2::WShopTaskPurchaseJewel::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVWShopTaskPurchaseJewel@mu2@@@8		; mu2::WShopTaskPurchaseJewel `RTTI Type Descriptor'
PUBLIC	??_R3WShopTaskPurchaseJewel@mu2@@8		; mu2::WShopTaskPurchaseJewel::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2WShopTaskPurchaseJewel@mu2@@8		; mu2::WShopTaskPurchaseJewel::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@WShopTaskPurchaseJewel@mu2@@8	; mu2::WShopTaskPurchaseJewel::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@WShopTask@mu2@@8			; mu2::WShopTask::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R0?AVWShopTask@mu2@@@8			; mu2::WShopTask `RTTI Type Descriptor'
PUBLIC	??_R3WShopTask@mu2@@8				; mu2::WShopTask::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2WShopTask@mu2@@8				; mu2::WShopTask::`RTTI Base Class Array'
EXTRN	??2@YAPEAX_K@Z:PROC				; operator new
EXTRN	??3@YAXPEAX_K@Z:PROC				; operator delete
EXTRN	atexit:PROC
EXTRN	__std_terminate:PROC
EXTRN	_invoke_watson:PROC
EXTRN	memcpy:PROC
EXTRN	memmove:PROC
EXTRN	wcscmp:PROC
EXTRN	wcslen:PROC
EXTRN	__imp_GetStdHandle:PROC
EXTRN	__imp_SetConsoleTextAttribute:PROC
EXTRN	__std_exception_copy:PROC
EXTRN	__std_exception_destroy:PROC
EXTRN	??_Eexception@std@@UEAAPEAXI@Z:PROC		; std::exception::`vector deleting destructor'
EXTRN	??_Ebad_alloc@std@@UEAAPEAXI@Z:PROC		; std::bad_alloc::`vector deleting destructor'
EXTRN	??_Ebad_array_new_length@std@@UEAAPEAXI@Z:PROC	; std::bad_array_new_length::`vector deleting destructor'
EXTRN	?_Xlength_error@std@@YAXPEBD@Z:PROC		; std::_Xlength_error
EXTRN	?_Xout_of_range@std@@YAXPEBD@Z:PROC		; std::_Xout_of_range
EXTRN	?LogRuntimeAssertionFailure@mu2@@YAXPEBD00_K@Z:PROC ; mu2::LogRuntimeAssertionFailure
EXTRN	?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ:PROC	; mu2::Logger::Logging
EXTRN	?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H0ZZ:PROC	; mu2::Logger::Logging
EXTRN	?GetAction@Entity@mu2@@QEBAPEAVAction@2@I@Z:PROC ; mu2::Entity::GetAction
EXTRN	??1GameMsg@mu2@@UEAA@XZ:PROC			; mu2::GameMsg::~GameMsg
EXTRN	?SendGameDebugMsg@GameMsg@mu2@@QEAA_NPEAVEntity@2@PEA_WZZ:PROC ; mu2::GameMsg::SendGameDebugMsg
EXTRN	??0GameMsg@mu2@@AEAA@XZ:PROC			; mu2::GameMsg::GameMsg
EXTRN	?GetCharId@EntityPlayer@mu2@@QEBAIXZ:PROC	; mu2::EntityPlayer::GetCharId
EXTRN	?GetFcsAuthInfo@EntityPlayer@mu2@@QEBAAEBUFcsAuthInfo@2@XZ:PROC ; mu2::EntityPlayer::GetFcsAuthInfo
EXTRN	?GetSoulLevelEx@EntityPlayer@mu2@@QEBAAEBUSoulLevelEx@2@XZ:PROC ; mu2::EntityPlayer::GetSoulLevelEx
EXTRN	??0WShopTask@mu2@@QEAA@PEAVWShopJob@1@@Z:PROC	; mu2::WShopTask::WShopTask
EXTRN	??1WShopTask@mu2@@UEAA@XZ:PROC			; mu2::WShopTask::~WShopTask
EXTRN	?SetBeginRequest@WShopTask@mu2@@QEAAXPEAVIRequestParent@fcsa@@@Z:PROC ; mu2::WShopTask::SetBeginRequest
EXTRN	?reqWShopItemJobBeginZone@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@XZ:PROC ; mu2::WShopTask::reqWShopItemJobBeginZone
EXTRN	?onResWShopItemJobBeginZone@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z:PROC ; mu2::WShopTask::onResWShopItemJobBeginZone
EXTRN	?reqWShopItemJobEndZone@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@_N@Z:PROC ; mu2::WShopTask::reqWShopItemJobEndZone
EXTRN	?onResWShopItemJobEndZone@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z:PROC ; mu2::WShopTask::onResWShopItemJobEndZone
EXTRN	?reqDbInsertWShopJob@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@XZ:PROC ; mu2::WShopTask::reqDbInsertWShopJob
EXTRN	?onResDbInsertWShopJob@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z:PROC ; mu2::WShopTask::onResDbInsertWShopJob
EXTRN	?reqDbRemoveWShopJob@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@XZ:PROC ; mu2::WShopTask::reqDbRemoveWShopJob
EXTRN	?onResDbRemoveWShopJob@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z:PROC ; mu2::WShopTask::onResDbRemoveWShopJob
EXTRN	?onResDbWShopItemJobEnd@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z:PROC ; mu2::WShopTask::onResDbWShopItemJobEnd
EXTRN	?onResDbWShopItemTranscation@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z:PROC ; mu2::WShopTask::onResDbWShopItemTranscation
EXTRN	?onResDbItemComfirmTransaction@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z:PROC ; mu2::WShopTask::onResDbItemComfirmTransaction
EXTRN	?TimeoutPorcess@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@XZ:PROC ; mu2::WShopTask::TimeoutPorcess
EXTRN	?GetParentJobNo@WShopTask@mu2@@QEAA_KXZ:PROC	; mu2::WShopTask::GetParentJobNo
EXTRN	?GetParentFcsAccountId@WShopTask@mu2@@QEAAIXZ:PROC ; mu2::WShopTask::GetParentFcsAccountId
EXTRN	?sendBeginCheck@WShopTask@mu2@@MEAA_NAEAUWShopDbJob@2@AEAV?$vector@UItemData@mu2@@V?$allocator@UItemData@mu2@@@std@@@std@@@Z:PROC ; mu2::WShopTask::sendBeginCheck
EXTRN	?sendCancelRequest@WShopTask@mu2@@MEAA?AW4Error@ErrorWShop@2@XZ:PROC ; mu2::WShopTask::sendCancelRequest
EXTRN	??_EWShopTaskPurchaseJewel@mu2@@UEAAPEAXI@Z:PROC ; mu2::WShopTaskPurchaseJewel::`vector deleting destructor'
EXTRN	??1WShop@mu2@@UEAA@XZ:PROC			; mu2::WShop::~WShop
EXTRN	?MakeOrderNo@WShop@mu2@@QEAA_KXZ:PROC		; mu2::WShop::MakeOrderNo
EXTRN	??0WShop@mu2@@AEAA@XZ:PROC			; mu2::WShop::WShop
EXTRN	_CxxThrowException:PROC
EXTRN	__CxxFrameHandler4:PROC
EXTRN	__GSHandlerCheck:PROC
EXTRN	__GSHandlerCheck_EH4:PROC
EXTRN	__security_check_cookie:PROC
EXTRN	??_7type_info@@6B@:BYTE				; type_info::`vftable'
EXTRN	?Instance@Server@mu2@@2PEAV12@EA:QWORD		; mu2::Server::Instance
EXTRN	__security_cookie:QWORD
;	COMDAT ?inst@?$ISingleton@VGameMsg@mu2@@@mu2@@1VGameMsg@2@A
_BSS	SEGMENT
?inst@?$ISingleton@VGameMsg@mu2@@@mu2@@1VGameMsg@2@A DB 010H DUP (?) ; mu2::ISingleton<mu2::GameMsg>::inst
_BSS	ENDS
;	COMDAT ?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A
_BSS	SEGMENT
?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A DB 068H DUP (?) ; mu2::ISingleton<mu2::WShop>::inst
_BSS	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0exception@std@@QEAA@AEBV01@@Z DD imagerel $LN4
	DD	imagerel $LN4+89
	DD	imagerel $unwind$??0exception@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?what@exception@std@@UEBAPEBDXZ DD imagerel $LN5
	DD	imagerel $LN5+56
	DD	imagerel $unwind$?what@exception@std@@UEBAPEBDXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gexception@std@@UEAAPEAXI@Z DD imagerel $LN10
	DD	imagerel $LN10+83
	DD	imagerel $unwind$??_Gexception@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z DD imagerel $LN9
	DD	imagerel $LN9+104
	DD	imagerel $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+83
	DD	imagerel $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_array_new_length@std@@QEAA@XZ DD imagerel $LN14
	DD	imagerel $LN14+95
	DD	imagerel $unwind$??0bad_array_new_length@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1bad_array_new_length@std@@UEAA@XZ DD imagerel $LN14
	DD	imagerel $LN14+47
	DD	imagerel $unwind$??1bad_array_new_length@std@@UEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z DD imagerel $LN14
	DD	imagerel $LN14+54
	DD	imagerel $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z DD imagerel $LN20
	DD	imagerel $LN20+83
	DD	imagerel $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Throw_bad_array_new_length@std@@YAXXZ DD imagerel $LN3
	DD	imagerel $LN3+37
	DD	imagerel $unwind$?_Throw_bad_array_new_length@std@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z DD imagerel $LN5
	DD	imagerel $LN5+159
	DD	imagerel $unwind$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Xlen_string@std@@YAXXZ DD imagerel $LN3
	DD	imagerel $LN3+22
	DD	imagerel $unwind$?_Xlen_string@std@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z DD imagerel $LN13
	DD	imagerel $LN13+162
	DD	imagerel $unwind$?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEAAPEA_WXZ DD imagerel $LN17
	DD	imagerel $LN17+111
	DD	imagerel $unwind$?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEAAPEA_WXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Xran@?$_String_val@U?$_Simple_types@_W@std@@@std@@SAXXZ DD imagerel $LN3
	DD	imagerel $LN3+22
	DD	imagerel $unwind$?_Xran@?$_String_val@U?$_Simple_types@_W@std@@@std@@SAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DD imagerel $LN18
	DD	imagerel $LN18+98
	DD	imagerel $unwind$?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@$$QEAV01@@Z DD imagerel $LN138
	DD	imagerel $LN138+164
	DD	imagerel $unwind$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@$$QEAV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Take_contents@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXAEAV12@@Z DD imagerel $LN93
	DD	imagerel $LN93+449
	DD	imagerel $unwind$?_Take_contents@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXAEAV12@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ DD imagerel $LN82
	DD	imagerel $LN82+25
	DD	imagerel $unwind$??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?insert@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV12@_KQEB_W0@Z DD imagerel $LN251
	DD	imagerel $LN251+603
	DD	imagerel $unwind$?insert@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV12@_KQEB_W0@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?c_str@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBAPEB_WXZ DD imagerel $LN22
	DD	imagerel $LN22+131
	DD	imagerel $unwind$?c_str@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBAPEB_WXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ DD imagerel $LN38
	DD	imagerel $LN38+250
	DD	imagerel $unwind$?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Calculate_growth@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@CA_K_K00@Z DD imagerel $LN13
	DD	imagerel $LN13+189
	DD	imagerel $unwind$?_Calculate_growth@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@CA_K_K00@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DD imagerel $LN62
	DD	imagerel $LN62+266
	DD	imagerel $unwind$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Integral_to_string@_W_K@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@0@_K@Z DD imagerel $LN261
	DD	imagerel $LN261+174
	DD	imagerel $unwind$??$_Integral_to_string@_W_K@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@0@_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$?H_WU?$char_traits@_W@std@@V?$allocator@_W@1@@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@0@QEB_W$$QEAV10@@Z DD imagerel $LN208
	DD	imagerel $LN208+131
	DD	imagerel $unwind$??$?H_WU?$char_traits@_W@std@@V?$allocator@_W@1@@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@0@QEB_W$$QEAV10@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?GetFcsGameLvel@EntityPlayer@mu2@@QEBAHXZ DD imagerel $LN7
	DD	imagerel $LN7+95
	DD	imagerel $unwind$?GetFcsGameLvel@EntityPlayer@mu2@@QEBAHXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$?0PEA_W$0A@@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@PEA_W0AEBV?$allocator@_W@1@@Z DD imagerel $LN247
	DD	imagerel $LN247+227
	DD	imagerel $unwind$??$?0PEA_W$0A@@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@PEA_W0AEBV?$allocator@_W@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???$?0PEA_W$0A@@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@PEA_W0AEBV?$allocator@_W@1@@Z@4HA DD imagerel ?dtor$0@?0???$?0PEA_W$0A@@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@PEA_W0AEBV?$allocator@_W@1@@Z@4HA
	DD	imagerel ?dtor$0@?0???$?0PEA_W$0A@@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@PEA_W0AEBV?$allocator@_W@1@@Z@4HA+24
	DD	imagerel $unwind$?dtor$0@?0???$?0PEA_W$0A@@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@PEA_W0AEBV?$allocator@_W@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_UIntegral_to_buff@_W_K@std@@YAPEA_WPEA_W_K@Z DD imagerel $LN6
	DD	imagerel $LN6+101
	DD	imagerel $unwind$??$_UIntegral_to_buff@_W_K@std@@YAPEA_WPEA_W_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Construct@$00PEA_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEA_W_K@Z DD imagerel $LN188
	DD	imagerel $LN188+836
	DD	imagerel $unwind$??$_Construct@$00PEA_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEA_W_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z DD imagerel $LN7
	DD	imagerel $LN7+150
	DD	imagerel $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Reallocate_grow_by@V<lambda_967c2ed818824c5314a20ec3af46b793>@@_KPEB_W_K@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_967c2ed818824c5314a20ec3af46b793>@@_KPEB_W2@Z DD imagerel $LN205
	DD	imagerel $LN205+1033
	DD	imagerel $unwind$??$_Reallocate_grow_by@V<lambda_967c2ed818824c5314a20ec3af46b793>@@_KPEB_W_K@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_967c2ed818824c5314a20ec3af46b793>@@_KPEB_W2@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0WShopTaskPurchaseJewel@mu2@@QEAA@PEAVWShopJob@1@@Z DD imagerel $LN4
	DD	imagerel $LN4+54
	DD	imagerel $unwind$??0WShopTaskPurchaseJewel@mu2@@QEAA@PEAVWShopJob@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1WShopTaskPurchaseJewel@mu2@@UEAA@XZ DD imagerel $LN4
	DD	imagerel $LN4+53
	DD	imagerel $unwind$??1WShopTaskPurchaseJewel@mu2@@UEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Response@WShopTaskPurchaseJewel@mu2@@UEAA?AW4Error@ErrorWShop@2@PEAVIResponseParent@fcsa@@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z DD imagerel $LN8
	DD	imagerel $LN8+136
	DD	imagerel $unwind$?Response@WShopTaskPurchaseJewel@mu2@@UEAA?AW4Error@ErrorWShop@2@PEAVIResponseParent@fcsa@@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?SetResultAndReturn@WShopTaskPurchaseJewel@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@W4342@@Z DD imagerel $LN10
	DD	imagerel $LN10+71
	DD	imagerel $unwind$?SetResultAndReturn@WShopTaskPurchaseJewel@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@W4342@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z DD imagerel $LN456
	DD	imagerel $LN456+2110
	DD	imagerel $unwind$?Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0??Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z@4HA DD imagerel ?dtor$0@?0??Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z@4HA
	DD	imagerel ?dtor$0@?0??Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z@4HA+27
	DD	imagerel $unwind$?dtor$0@?0??Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$1@?0??Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z@4HA DD imagerel ?dtor$1@?0??Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z@4HA
	DD	imagerel ?dtor$1@?0??Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z@4HA+27
	DD	imagerel $unwind$?dtor$1@?0??Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$2@?0??Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z@4HA DD imagerel ?dtor$2@?0??Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z@4HA
	DD	imagerel ?dtor$2@?0??Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z@4HA+27
	DD	imagerel $unwind$?dtor$2@?0??Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?sendBeginRequest@WShopTaskPurchaseJewel@mu2@@MEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z DD imagerel $LN9
	DD	imagerel $LN9+302
	DD	imagerel $unwind$?sendBeginRequest@WShopTaskPurchaseJewel@mu2@@MEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?sendConfirmRequest@WShopTaskPurchaseJewel@mu2@@MEAA?AW4Error@ErrorWShop@2@XZ DD imagerel $LN14
	DD	imagerel $LN14+774
	DD	imagerel $unwind$?sendConfirmRequest@WShopTaskPurchaseJewel@mu2@@MEAA?AW4Error@ErrorWShop@2@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?recv@WShopTaskPurchaseJewel@mu2@@AEAA?AW4Error@ErrorWShop@2@PEAVIResponsePurchaseJewelItem@fcsa@@@Z DD imagerel $LN40
	DD	imagerel $LN40+1196
	DD	imagerel $unwind$?recv@WShopTaskPurchaseJewel@mu2@@AEAA?AW4Error@ErrorWShop@2@PEAVIResponsePurchaseJewelItem@fcsa@@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?recv@WShopTaskPurchaseJewel@mu2@@AEAA?AW4Error@ErrorWShop@2@PEAVIResponseConfirmJewelPurchase@fcsa@@@Z DD imagerel $LN43
	DD	imagerel $LN43+1342
	DD	imagerel $unwind$?recv@WShopTaskPurchaseJewel@mu2@@AEAA?AW4Error@ErrorWShop@2@PEAVIResponseConfirmJewelPurchase@fcsa@@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GWShopTaskPurchaseJewel@mu2@@UEAAPEAXI@Z DD imagerel $LN5
	DD	imagerel $LN5+60
	DD	imagerel $unwind$??_GWShopTaskPurchaseJewel@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__E?inst@?$ISingleton@VGameMsg@mu2@@@mu2@@1VGameMsg@2@A@@YAXXZ DD imagerel ??__E?inst@?$ISingleton@VGameMsg@mu2@@@mu2@@1VGameMsg@2@A@@YAXXZ
	DD	imagerel ??__E?inst@?$ISingleton@VGameMsg@mu2@@@mu2@@1VGameMsg@2@A@@YAXXZ+34
	DD	imagerel $unwind$??__E?inst@?$ISingleton@VGameMsg@mu2@@@mu2@@1VGameMsg@2@A@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__F?inst@?$ISingleton@VGameMsg@mu2@@@mu2@@1VGameMsg@2@A@@YAXXZ DD imagerel ??__F?inst@?$ISingleton@VGameMsg@mu2@@@mu2@@1VGameMsg@2@A@@YAXXZ
	DD	imagerel ??__F?inst@?$ISingleton@VGameMsg@mu2@@@mu2@@1VGameMsg@2@A@@YAXXZ+22
	DD	imagerel $unwind$??__F?inst@?$ISingleton@VGameMsg@mu2@@@mu2@@1VGameMsg@2@A@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__E?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A@@YAXXZ DD imagerel ??__E?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A@@YAXXZ
	DD	imagerel ??__E?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A@@YAXXZ+34
	DD	imagerel $unwind$??__E?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__F?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A@@YAXXZ DD imagerel ??__F?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A@@YAXXZ
	DD	imagerel ??__F?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A@@YAXXZ+22
	DD	imagerel $unwind$??__F?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A@@YAXXZ
pdata	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
??inst$initializer$@?$ISingleton@VGameMsg@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA DQ FLAT:??__E?inst@?$ISingleton@VGameMsg@mu2@@@mu2@@1VGameMsg@2@A@@YAXXZ ; ??inst$initializer$@?$ISingleton@VGameMsg@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA
CRT$XCU	ENDS
;	COMDAT ??_R2WShopTask@mu2@@8
rdata$r	SEGMENT
??_R2WShopTask@mu2@@8 DD imagerel ??_R1A@?0A@EA@WShopTask@mu2@@8 ; mu2::WShopTask::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3WShopTask@mu2@@8
rdata$r	SEGMENT
??_R3WShopTask@mu2@@8 DD 00H				; mu2::WShopTask::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2WShopTask@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVWShopTask@mu2@@@8
data$rs	SEGMENT
??_R0?AVWShopTask@mu2@@@8 DQ FLAT:??_7type_info@@6B@	; mu2::WShopTask `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVWShopTask@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R1A@?0A@EA@WShopTask@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@WShopTask@mu2@@8 DD imagerel ??_R0?AVWShopTask@mu2@@@8 ; mu2::WShopTask::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3WShopTask@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@WShopTaskPurchaseJewel@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@WShopTaskPurchaseJewel@mu2@@8 DD imagerel ??_R0?AVWShopTaskPurchaseJewel@mu2@@@8 ; mu2::WShopTaskPurchaseJewel::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3WShopTaskPurchaseJewel@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2WShopTaskPurchaseJewel@mu2@@8
rdata$r	SEGMENT
??_R2WShopTaskPurchaseJewel@mu2@@8 DD imagerel ??_R1A@?0A@EA@WShopTaskPurchaseJewel@mu2@@8 ; mu2::WShopTaskPurchaseJewel::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@WShopTask@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3WShopTaskPurchaseJewel@mu2@@8
rdata$r	SEGMENT
??_R3WShopTaskPurchaseJewel@mu2@@8 DD 00H		; mu2::WShopTaskPurchaseJewel::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2WShopTaskPurchaseJewel@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVWShopTaskPurchaseJewel@mu2@@@8
data$rs	SEGMENT
??_R0?AVWShopTaskPurchaseJewel@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::WShopTaskPurchaseJewel `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVWShopTaskPurchaseJewel@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4WShopTaskPurchaseJewel@mu2@@6B@
rdata$r	SEGMENT
??_R4WShopTaskPurchaseJewel@mu2@@6B@ DD 01H		; mu2::WShopTaskPurchaseJewel::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVWShopTaskPurchaseJewel@mu2@@@8
	DD	imagerel ??_R3WShopTaskPurchaseJewel@mu2@@8
	DD	imagerel ??_R4WShopTaskPurchaseJewel@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_C@_1LE@LEGCOMOK@?$AAP?$AAu?$AAr?$AAc?$AAh?$AAa?$AAs?$AAe?$AAJ?$AAe?$AAw?$AAe?$AAl?$AA?5?$AA0@
CONST	SEGMENT
??_C@_1LE@LEGCOMOK@?$AAP?$AAu?$AAr?$AAc?$AAh?$AAa?$AAs?$AAe?$AAJ?$AAe?$AAw?$AAe?$AAl?$AA?5?$AA0@ DB 'P'
	DB	00H, 'u', 00H, 'r', 00H, 'c', 00H, 'h', 00H, 'a', 00H, 's', 00H
	DB	'e', 00H, 'J', 00H, 'e', 00H, 'w', 00H, 'e', 00H, 'l', 00H, ' '
	DB	00H, '0', 00H, ' ', 00H, 'I', 00H, 'R', 00H, 'e', 00H, 's', 00H
	DB	'p', 00H, 'o', 00H, 'n', 00H, 's', 00H, 'e', 00H, 'C', 00H, 'o'
	DB	00H, 'n', 00H, 'f', 00H, 'i', 00H, 'r', 00H, 'm', 00H, 'J', 00H
	DB	'e', 00H, 'w', 00H, 'e', 00H, 'l', 00H, 'P', 00H, 'u', 00H, 'r'
	DB	00H, 'c', 00H, 'h', 00H, 'a', 00H, 's', 00H, 'e', 00H, '>', 00H
	DB	' ', 00H, 'F', 00H, 'a', 00H, 'i', 00H, 'l', 00H, ' ', 00H, ':'
	DB	00H, ' ', 00H, 't', 00H, 'y', 00H, 'p', 00H, 'e', 00H, '(', 00H
	DB	'%', 00H, 'd', 00H, ')', 00H, ',', 00H, ' ', 00H, 'r', 00H, 'e'
	DB	00H, 's', 00H, 'u', 00H, 'l', 00H, 't', 00H, '(', 00H, '%', 00H
	DB	'd', 00H, ')', 00H, ',', 00H, ' ', 00H, 'c', 00H, 'o', 00H, 'n'
	DB	00H, 'd', 00H, 'i', 00H, 't', 00H, 'i', 00H, 'o', 00H, 'n', 00H
	DB	'(', 00H, '%', 00H, 'd', 00H, ')', 00H, 00H, 00H ; `string'
CONST	ENDS
;	COMDAT ??_C@_1FO@BAMCKIJN@?$AAP?$AAu?$AAr?$AAc?$AAh?$AAa?$AAs?$AAe?$AAJ?$AAe?$AAw?$AAe?$AAl?$AA?5?$AA1@
CONST	SEGMENT
??_C@_1FO@BAMCKIJN@?$AAP?$AAu?$AAr?$AAc?$AAh?$AAa?$AAs?$AAe?$AAJ?$AAe?$AAw?$AAe?$AAl?$AA?5?$AA1@ DB 'P'
	DB	00H, 'u', 00H, 'r', 00H, 'c', 00H, 'h', 00H, 'a', 00H, 's', 00H
	DB	'e', 00H, 'J', 00H, 'e', 00H, 'w', 00H, 'e', 00H, 'l', 00H, ' '
	DB	00H, '1', 00H, ' ', 00H, 's', 00H, 'u', 00H, 'c', 00H, 'c', 00H
	DB	'e', 00H, 's', 00H, 's', 00H, ' ', 00H, 'c', 00H, 'a', 00H, 's'
	DB	00H, 'h', 00H, '_', 00H, 't', 00H, 'y', 00H, 'p', 00H, 'e', 00H
	DB	' ', 00H, '%', 00H, 'd', 00H, ' ', 00H, 'a', 00H, 'm', 00H, 'o'
	DB	00H, 'u', 00H, 'n', 00H, 't', 00H, ' ', 00H, '%', 00H, 'd', 00H
	DB	00H, 00H					; `string'
CONST	ENDS
;	COMDAT ??_C@_0DA@LHOBOCCA@mu2?3?3WShopTaskPurchaseJewel?3?3se@
CONST	SEGMENT
??_C@_0DA@LHOBOCCA@mu2?3?3WShopTaskPurchaseJewel?3?3se@ DB 'mu2::WShopTas'
	DB	'kPurchaseJewel::sendConfirmRequest', 00H	; `string'
CONST	ENDS
;	COMDAT ??_C@_1HI@IDJCKHMJ@?$AAP?$AAu?$AAr?$AAc?$AAh?$AAa?$AAs?$AAe?$AAJ?$AAe?$AAw?$AAe?$AAl?$AA?5?$AA0@
CONST	SEGMENT
??_C@_1HI@IDJCKHMJ@?$AAP?$AAu?$AAr?$AAc?$AAh?$AAa?$AAs?$AAe?$AAJ?$AAe?$AAw?$AAe?$AAl?$AA?5?$AA0@ DB 'P'
	DB	00H, 'u', 00H, 'r', 00H, 'c', 00H, 'h', 00H, 'a', 00H, 's', 00H
	DB	'e', 00H, 'J', 00H, 'e', 00H, 'w', 00H, 'e', 00H, 'l', 00H, ' '
	DB	00H, '0', 00H, ' ', 00H, 's', 00H, 'p', 00H, 'e', 00H, 'n', 00H
	DB	't', 00H, ' ', 00H, 'm', 00H, 'o', 00H, 'n', 00H, 'e', 00H, 'y'
	DB	00H, ' ', 00H, 'f', 00H, 'a', 00H, 'i', 00H, 'l', 00H, 'e', 00H
	DB	'd', 00H, ' ', 00H, ':', 00H, ' ', 00H, 't', 00H, 'y', 00H, 'p'
	DB	00H, 'e', 00H, ' ', 00H, '=', 00H, ' ', 00H, '%', 00H, 'd', 00H
	DB	',', 00H, ' ', 00H, 'a', 00H, 'm', 00H, 'o', 00H, 'u', 00H, 'n'
	DB	00H, 't', 00H, ' ', 00H, '=', 00H, ' ', 00H, '%', 00H, 'd', 00H
	DB	00H, 00H					; `string'
CONST	ENDS
;	COMDAT ??_C@_1II@HMCPJHON@?$AAW?$AAS?$AAH?$AAO?$AAP?$AA?$CI?$AA?$CF?$AAu?$AA?$CJ?$AA?0?$AA?$CF?$AAl?$AAl?$AAu?$AA?5@
CONST	SEGMENT
??_C@_1II@HMCPJHON@?$AAW?$AAS?$AAH?$AAO?$AAP?$AA?$CI?$AA?$CF?$AAu?$AA?$CJ?$AA?0?$AA?$CF?$AAl?$AAl?$AAu?$AA?5@ DB 'W'
	DB	00H, 'S', 00H, 'H', 00H, 'O', 00H, 'P', 00H, '(', 00H, '%', 00H
	DB	'u', 00H, ')', 00H, ',', 00H, '%', 00H, 'l', 00H, 'l', 00H, 'u'
	DB	00H, ' ', 00H, ':', 00H, ' ', 00H, 'I', 00H, 'R', 00H, 'e', 00H
	DB	's', 00H, 'p', 00H, 'o', 00H, 'n', 00H, 's', 00H, 'e', 00H, 'P'
	DB	00H, 'u', 00H, 'r', 00H, 'c', 00H, 'h', 00H, 'a', 00H, 's', 00H
	DB	'e', 00H, 'J', 00H, 'e', 00H, 'w', 00H, 'e', 00H, 'l', 00H, 'I'
	DB	00H, 't', 00H, 'e', 00H, 'm', 00H, '>', 00H, ' ', 00H, 'm', 00H
	DB	'i', 00H, 's', 00H, 'm', 00H, 'a', 00H, 't', 00H, 'c', 00H, 'h'
	DB	00H, ' ', 00H, 'o', 00H, 'r', 00H, 'd', 00H, 'e', 00H, 'r', 00H
	DB	' ', 00H, 'i', 00H, 'd', 00H, ' ', 00H, '(', 00H, '%', 00H, 's'
	DB	00H, ')', 00H, 00H, 00H			; `string'
CONST	ENDS
;	COMDAT ??_C@_0CC@LGOLGFIH@mu2?3?3WShopTaskPurchaseJewel?3?3re@
CONST	SEGMENT
??_C@_0CC@LGOLGFIH@mu2?3?3WShopTaskPurchaseJewel?3?3re@ DB 'mu2::WShopTas'
	DB	'kPurchaseJewel::recv', 00H			; `string'
CONST	ENDS
;	COMDAT ??_C@_0N@MALEHAKP@beginRequest@
CONST	SEGMENT
??_C@_0N@MALEHAKP@beginRequest@ DB 'beginRequest', 00H	; `string'
CONST	ENDS
;	COMDAT ??_C@_0CO@LOJFDACN@mu2?3?3WShopTaskPurchaseJewel?3?3se@
CONST	SEGMENT
??_C@_0CO@LOJFDACN@mu2?3?3WShopTaskPurchaseJewel?3?3se@ DB 'mu2::WShopTas'
	DB	'kPurchaseJewel::sendBeginRequest', 00H	; `string'
CONST	ENDS
;	COMDAT ??_C@_0CC@EFIHDMCI@WSHOP?$CI?$CFu?$CJ?0?$CFllu?5?3?5sendAsync?5?$CI?$CFd?$CJ@
CONST	SEGMENT
??_C@_0CC@EFIHDMCI@WSHOP?$CI?$CFu?$CJ?0?$CFllu?5?3?5sendAsync?5?$CI?$CFd?$CJ@ DB 'W'
	DB	'SHOP(%u),%llu : sendAsync (%d)> ', 00H	; `string'
CONST	ENDS
;	COMDAT ??_C@_0DF@HOBIDHFN@?$CB?$CCWShopTaskPurchaseJewel?3?3Respo@
CONST	SEGMENT
??_C@_0DF@HOBIDHFN@?$CB?$CCWShopTaskPurchaseJewel?3?3Respo@ DB '!"WShopTa'
	DB	'skPurchaseJewel::Response> Unhandled state"', 00H ; `string'
CONST	ENDS
;	COMDAT ??_C@_0DC@PGFKHDPB@WShopTaskPurchaseJewel?3?3Respons@
CONST	SEGMENT
??_C@_0DC@PGFKHDPB@WShopTaskPurchaseJewel?3?3Respons@ DB 'WShopTaskPurcha'
	DB	'seJewel::Response> Unhandled state', 00H	; `string'
CONST	ENDS
;	COMDAT ??_C@_1BK@JKMOFEE@?$AAd?$AAe?$AAv?$AA?9?$AAd?$AAe?$AAc?$AAr?$AAe?$AAa?$AAs?$AAe@
CONST	SEGMENT
??_C@_1BK@JKMOFEE@?$AAd?$AAe?$AAv?$AA?9?$AAd?$AAe?$AAc?$AAr?$AAe?$AAa?$AAs?$AAe@ DB 'd'
	DB	00H, 'e', 00H, 'v', 00H, '-', 00H, 'd', 00H, 'e', 00H, 'c', 00H
	DB	'r', 00H, 'e', 00H, 'a', 00H, 's', 00H, 'e', 00H, 00H, 00H ; `string'
CONST	ENDS
;	COMDAT ??_C@_1CE@KMFGEGPK@?$AAd?$AAe?$AAv?$AA?9?$AAp?$AAu?$AAr?$AAc?$AAh?$AAa?$AAs?$AAe?$AAJ?$AAe?$AAw@
CONST	SEGMENT
??_C@_1CE@KMFGEGPK@?$AAd?$AAe?$AAv?$AA?9?$AAp?$AAu?$AAr?$AAc?$AAh?$AAa?$AAs?$AAe?$AAJ?$AAe?$AAw@ DB 'd'
	DB	00H, 'e', 00H, 'v', 00H, '-', 00H, 'p', 00H, 'u', 00H, 'r', 00H
	DB	'c', 00H, 'h', 00H, 'a', 00H, 's', 00H, 'e', 00H, 'J', 00H, 'e'
	DB	00H, 'w', 00H, 'e', 00H, 'l', 00H, 00H, 00H	; `string'
CONST	ENDS
;	COMDAT ??_C@_13CKBCJILK@?$AAJ@
CONST	SEGMENT
??_C@_13CKBCJILK@?$AAJ@ DB 'J', 00H, 00H, 00H		; `string'
CONST	ENDS
;	COMDAT ??_C@_0L@JNPHBBNK@wShopJewel@
CONST	SEGMENT
??_C@_0L@JNPHBBNK@wShopJewel@ DB 'wShopJewel', 00H	; `string'
CONST	ENDS
;	COMDAT ??_C@_0CF@IGILGBB@owner?9?$DOGetFcsAuthInfo?$CI?$CJ?4fcsAcco@
CONST	SEGMENT
??_C@_0CF@IGILGBB@owner?9?$DOGetFcsAuthInfo?$CI?$CJ?4fcsAcco@ DB 'owner->'
	DB	'GetFcsAuthInfo().fcsAccountId', 00H		; `string'
CONST	ENDS
;	COMDAT ??_C@_0CD@FPCNIDPM@mu2?3?3WShopTaskPurchaseJewel?3?3Se@
CONST	SEGMENT
??_C@_0CD@FPCNIDPM@mu2?3?3WShopTaskPurchaseJewel?3?3Se@ DB 'mu2::WShopTas'
	DB	'kPurchaseJewel::Setup', 00H			; `string'
CONST	ENDS
;	COMDAT ??_C@_05HEIBENID@owner@
CONST	SEGMENT
??_C@_05HEIBENID@owner@ DB 'owner', 00H			; `string'
CONST	ENDS
;	COMDAT ??_C@_0FL@OJHBMBAH@F?3?2Release_Branch?2Server?2Develo@
CONST	SEGMENT
??_C@_0FL@OJHBMBAH@F?3?2Release_Branch?2Server?2Develo@ DB 'F:\Release_Br'
	DB	'anch\Server\Development\Frontend\WorldServer\WShop\WShopTaskP'
	DB	'urchaseJewel.cpp', 00H			; `string'
CONST	ENDS
;	COMDAT ??_7WShopTaskPurchaseJewel@mu2@@6B@
CONST	SEGMENT
??_7WShopTaskPurchaseJewel@mu2@@6B@ DQ FLAT:??_R4WShopTaskPurchaseJewel@mu2@@6B@ ; mu2::WShopTaskPurchaseJewel::`vftable'
	DQ	FLAT:??_EWShopTaskPurchaseJewel@mu2@@UEAAPEAXI@Z
	DQ	FLAT:?Response@WShopTaskPurchaseJewel@mu2@@UEAA?AW4Error@ErrorWShop@2@PEAVIResponseParent@fcsa@@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?reqWShopItemJobBeginZone@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@XZ
	DQ	FLAT:?onResWShopItemJobBeginZone@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z
	DQ	FLAT:?reqWShopItemJobEndZone@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@_N@Z
	DQ	FLAT:?onResWShopItemJobEndZone@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z
	DQ	FLAT:?reqDbInsertWShopJob@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@XZ
	DQ	FLAT:?onResDbInsertWShopJob@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z
	DQ	FLAT:?reqDbRemoveWShopJob@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@XZ
	DQ	FLAT:?onResDbRemoveWShopJob@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z
	DQ	FLAT:?onResDbWShopItemJobEnd@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z
	DQ	FLAT:?onResDbWShopItemTranscation@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z
	DQ	FLAT:?onResDbItemComfirmTransaction@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z
	DQ	FLAT:?SetResultAndReturn@WShopTaskPurchaseJewel@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@W4342@@Z
	DQ	FLAT:?TimeoutPorcess@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@XZ
	DQ	FLAT:?sendBeginRequest@WShopTaskPurchaseJewel@mu2@@MEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?sendBeginCheck@WShopTask@mu2@@MEAA_NAEAUWShopDbJob@2@AEAV?$vector@UItemData@mu2@@V?$allocator@UItemData@mu2@@@std@@@std@@@Z
	DQ	FLAT:?sendConfirmRequest@WShopTaskPurchaseJewel@mu2@@MEAA?AW4Error@ErrorWShop@2@XZ
	DQ	FLAT:?sendCancelRequest@WShopTask@mu2@@MEAA?AW4Error@ErrorWShop@2@XZ
	DQ	FLAT:?fillItemResult@WShopTask@mu2@@MEAAXXZ
CONST	ENDS
;	COMDAT ??_R4bad_alloc@std@@6B@
rdata$r	SEGMENT
??_R4bad_alloc@std@@6B@ DD 01H				; std::bad_alloc::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVbad_alloc@std@@@8
	DD	imagerel ??_R3bad_alloc@std@@8
	DD	imagerel ??_R4bad_alloc@std@@6B@
rdata$r	ENDS
;	COMDAT ??_R2bad_alloc@std@@8
rdata$r	SEGMENT
??_R2bad_alloc@std@@8 DD imagerel ??_R1A@?0A@EA@bad_alloc@std@@8 ; std::bad_alloc::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@exception@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3bad_alloc@std@@8
rdata$r	SEGMENT
??_R3bad_alloc@std@@8 DD 00H				; std::bad_alloc::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2bad_alloc@std@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@bad_alloc@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@bad_alloc@std@@8 DD imagerel ??_R0?AVbad_alloc@std@@@8 ; std::bad_alloc::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3bad_alloc@std@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@bad_array_new_length@std@@8 DD imagerel ??_R0?AVbad_array_new_length@std@@@8 ; std::bad_array_new_length::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	02H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3bad_array_new_length@std@@8
rdata$r	ENDS
;	COMDAT ??_R2bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R2bad_array_new_length@std@@8 DD imagerel ??_R1A@?0A@EA@bad_array_new_length@std@@8 ; std::bad_array_new_length::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@bad_alloc@std@@8
	DD	imagerel ??_R1A@?0A@EA@exception@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R3bad_array_new_length@std@@8 DD 00H			; std::bad_array_new_length::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	03H
	DD	imagerel ??_R2bad_array_new_length@std@@8
rdata$r	ENDS
;	COMDAT ??_R4bad_array_new_length@std@@6B@
rdata$r	SEGMENT
??_R4bad_array_new_length@std@@6B@ DD 01H		; std::bad_array_new_length::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVbad_array_new_length@std@@@8
	DD	imagerel ??_R3bad_array_new_length@std@@8
	DD	imagerel ??_R4bad_array_new_length@std@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@exception@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@exception@std@@8 DD imagerel ??_R0?AVexception@std@@@8 ; std::exception::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3exception@std@@8
rdata$r	ENDS
;	COMDAT ??_R2exception@std@@8
rdata$r	SEGMENT
??_R2exception@std@@8 DD imagerel ??_R1A@?0A@EA@exception@std@@8 ; std::exception::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3exception@std@@8
rdata$r	SEGMENT
??_R3exception@std@@8 DD 00H				; std::exception::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2exception@std@@8
rdata$r	ENDS
;	COMDAT ??_R4exception@std@@6B@
rdata$r	SEGMENT
??_R4exception@std@@6B@ DD 01H				; std::exception::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVexception@std@@@8
	DD	imagerel ??_R3exception@std@@8
	DD	imagerel ??_R4exception@std@@6B@
rdata$r	ENDS
;	COMDAT ??_C@_0BI@CFPLBAOH@invalid?5string?5position@
CONST	SEGMENT
??_C@_0BI@CFPLBAOH@invalid?5string?5position@ DB 'invalid string position'
	DB	00H						; `string'
CONST	ENDS
;	COMDAT ??_C@_09OLBBGJEO@Assertion@
CONST	SEGMENT
??_C@_09OLBBGJEO@Assertion@ DB 'Assertion', 00H		; `string'
CONST	ENDS
;	COMDAT ??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
CONST	SEGMENT
??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@ DB 'g', 00H, 'a', 00H, 'm', 00H, 'e'
	DB	00H, 00H, 00H				; `string'
CONST	ENDS
;	COMDAT ??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
CONST	SEGMENT
??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@ DB '%'
	DB	's> ASSERT - %s, %s(%d)', 00H		; `string'
CONST	ENDS
;	COMDAT ??_C@_0BA@JFNIOLAK@string?5too?5long@
CONST	SEGMENT
??_C@_0BA@JFNIOLAK@string?5too?5long@ DB 'string too long', 00H ; `string'
CONST	ENDS
;	COMDAT _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 DD 010H
	DD	imagerel ??_R0?AVbad_alloc@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0bad_alloc@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVbad_alloc@std@@@8
data$r	SEGMENT
??_R0?AVbad_alloc@std@@@8 DQ FLAT:??_7type_info@@6B@	; std::bad_alloc `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVbad_alloc@std@@', 00H
data$r	ENDS
;	COMDAT _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 DD 00H
	DD	imagerel ??_R0?AVbad_array_new_length@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0bad_array_new_length@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVbad_array_new_length@std@@@8
data$r	SEGMENT
??_R0?AVbad_array_new_length@std@@@8 DQ FLAT:??_7type_info@@6B@ ; std::bad_array_new_length `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVbad_array_new_length@std@@', 00H
data$r	ENDS
;	COMDAT _CTA3?AVbad_array_new_length@std@@
xdata$x	SEGMENT
_CTA3?AVbad_array_new_length@std@@ DD 03H
	DD	imagerel _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
	DD	imagerel _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
	DD	imagerel _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
xdata$x	ENDS
;	COMDAT _TI3?AVbad_array_new_length@std@@
xdata$x	SEGMENT
_TI3?AVbad_array_new_length@std@@ DD 00H
	DD	imagerel ??1bad_array_new_length@std@@UEAA@XZ
	DD	00H
	DD	imagerel _CTA3?AVbad_array_new_length@std@@
xdata$x	ENDS
;	COMDAT _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 DD 00H
	DD	imagerel ??_R0?AVexception@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0exception@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVexception@std@@@8
data$r	SEGMENT
??_R0?AVexception@std@@@8 DQ FLAT:??_7type_info@@6B@	; std::exception `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVexception@std@@', 00H
data$r	ENDS
;	COMDAT ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@
CONST	SEGMENT
??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ DB 'bad array new length', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7bad_array_new_length@std@@6B@
CONST	SEGMENT
??_7bad_array_new_length@std@@6B@ DQ FLAT:??_R4bad_array_new_length@std@@6B@ ; std::bad_array_new_length::`vftable'
	DQ	FLAT:??_Ebad_array_new_length@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT ??_7bad_alloc@std@@6B@
CONST	SEGMENT
??_7bad_alloc@std@@6B@ DQ FLAT:??_R4bad_alloc@std@@6B@	; std::bad_alloc::`vftable'
	DQ	FLAT:??_Ebad_alloc@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT ??_C@_0BC@EOODALEL@Unknown?5exception@
CONST	SEGMENT
??_C@_0BC@EOODALEL@Unknown?5exception@ DB 'Unknown exception', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7exception@std@@6B@
CONST	SEGMENT
??_7exception@std@@6B@ DQ FLAT:??_R4exception@std@@6B@	; std::exception::`vftable'
	DQ	FLAT:??_Eexception@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__F?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__E?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__F?inst@?$ISingleton@VGameMsg@mu2@@@mu2@@1VGameMsg@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__E?inst@?$ISingleton@VGameMsg@mu2@@@mu2@@1VGameMsg@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GWShopTaskPurchaseJewel@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?recv@WShopTaskPurchaseJewel@mu2@@AEAA?AW4Error@ErrorWShop@2@PEAVIResponseConfirmJewelPurchase@fcsa@@@Z DD 021101H
	DD	02b0111H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?recv@WShopTaskPurchaseJewel@mu2@@AEAA?AW4Error@ErrorWShop@2@PEAVIResponsePurchaseJewelItem@fcsa@@@Z DD 021101H
	DD	0270111H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?sendConfirmRequest@WShopTaskPurchaseJewel@mu2@@MEAA?AW4Error@ErrorWShop@2@XZ DD 020c01H
	DD	01b010cH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?sendBeginRequest@WShopTaskPurchaseJewel@mu2@@MEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z DD 021101H
	DD	0110111H
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DW	020H
	DW	0826H
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$2@?0??Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$1@?0??Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0??Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z DB 0cH
	DB	00H
	DB	00H
	DB	05H, 0eH
	DB	02H
	DB	':'
	DB	06H
	DB	'e', 010H
	DB	08H
	DB	'h'
	DB	06H
	DB	'R'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z DB 08H
	DB	0eH
	DD	imagerel ?dtor$0@?0??Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z@4HA
	DB	02eH
	DD	imagerel ?dtor$1@?0??Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z@4HA
	DB	05eH
	DD	imagerel ?dtor$1@?0??Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z@4HA
	DB	02eH
	DD	imagerel ?dtor$2@?0??Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z DB 028H
	DD	imagerel $stateUnwindMap$?Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z
	DD	imagerel $ip2state$?Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z DD 022819H
	DD	0430116H
	DD	imagerel __GSHandlerCheck_EH4
	DD	imagerel $cppxdata$?Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z
	DD	0202H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?SetResultAndReturn@WShopTaskPurchaseJewel@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@W4342@@Z DD 011301H
	DD	04213H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Response@WShopTaskPurchaseJewel@mu2@@UEAA?AW4Error@ErrorWShop@2@PEAVIResponseParent@fcsa@@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z DD 011301H
	DD	06213H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??1WShopTaskPurchaseJewel@mu2@@UEAA@XZ DB 02H
	DB	00H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??1WShopTaskPurchaseJewel@mu2@@UEAA@XZ DB 060H
	DD	imagerel $ip2state$??1WShopTaskPurchaseJewel@mu2@@UEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1WShopTaskPurchaseJewel@mu2@@UEAA@XZ DD 010919H
	DD	04209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??1WShopTaskPurchaseJewel@mu2@@UEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0WShopTaskPurchaseJewel@mu2@@QEAA@PEAVWShopJob@1@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$_Reallocate_grow_by@V<lambda_967c2ed818824c5314a20ec3af46b793>@@_KPEB_W_K@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_967c2ed818824c5314a20ec3af46b793>@@_KPEB_W2@Z DB 06H
	DB	00H
	DB	00H
	DB	'q', 0aH
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$_Reallocate_grow_by@V<lambda_967c2ed818824c5314a20ec3af46b793>@@_KPEB_W_K@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_967c2ed818824c5314a20ec3af46b793>@@_KPEB_W2@Z DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$_Reallocate_grow_by@V<lambda_967c2ed818824c5314a20ec3af46b793>@@_KPEB_W_K@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_967c2ed818824c5314a20ec3af46b793>@@_KPEB_W2@Z DB 028H
	DD	imagerel $stateUnwindMap$??$_Reallocate_grow_by@V<lambda_967c2ed818824c5314a20ec3af46b793>@@_KPEB_W_K@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_967c2ed818824c5314a20ec3af46b793>@@_KPEB_W2@Z
	DD	imagerel $ip2state$??$_Reallocate_grow_by@V<lambda_967c2ed818824c5314a20ec3af46b793>@@_KPEB_W_K@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_967c2ed818824c5314a20ec3af46b793>@@_KPEB_W2@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Reallocate_grow_by@V<lambda_967c2ed818824c5314a20ec3af46b793>@@_KPEB_W_K@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_967c2ed818824c5314a20ec3af46b793>@@_KPEB_W2@Z DD 021b11H
	DD	023011bH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$_Reallocate_grow_by@V<lambda_967c2ed818824c5314a20ec3af46b793>@@_KPEB_W_K@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_967c2ed818824c5314a20ec3af46b793>@@_KPEB_W2@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z DD 010901H
	DD	0a209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Construct@$00PEA_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEA_W_K@Z DD 031701H
	DD	0200117H
	DD	07010H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_UIntegral_to_buff@_W_K@std@@YAPEA_WPEA_W_K@Z DD 010e01H
	DD	0220eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???$?0PEA_W$0A@@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@PEA_W0AEBV?$allocator@_W@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$?0PEA_W$0A@@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@PEA_W0AEBV?$allocator@_W@1@@Z DB 06H
	DB	00H
	DB	00H
	DB	0c8H
	DB	02H
	DB	0e8H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$?0PEA_W$0A@@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@PEA_W0AEBV?$allocator@_W@1@@Z DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???$?0PEA_W$0A@@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@PEA_W0AEBV?$allocator@_W@1@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$?0PEA_W$0A@@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@PEA_W0AEBV?$allocator@_W@1@@Z DB 028H
	DD	imagerel $stateUnwindMap$??$?0PEA_W$0A@@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@PEA_W0AEBV?$allocator@_W@1@@Z
	DD	imagerel $ip2state$??$?0PEA_W$0A@@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@PEA_W0AEBV?$allocator@_W@1@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$?0PEA_W$0A@@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@PEA_W0AEBV?$allocator@_W@1@@Z DD 021911H
	DD	07015b219H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$?0PEA_W$0A@@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@PEA_W0AEBV?$allocator@_W@1@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?GetFcsGameLvel@EntityPlayer@mu2@@QEBAHXZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$?H_WU?$char_traits@_W@std@@V?$allocator@_W@1@@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@0@QEB_W$$QEAV10@@Z DD 011301H
	DD	0a213H
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DB	01bH
	DB	099H
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Integral_to_string@_W_K@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@0@_K@Z DD 022019H
	DD	0110111H
	DD	imagerel __GSHandlerCheck
	DD	078H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DB 06H
	DB	00H
	DB	00H
	DB	089H, 02H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DB 068H
	DD	imagerel $stateUnwindMap$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
	DD	imagerel $ip2state$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DD 010919H
	DD	0e209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Calculate_growth@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@CA_K_K00@Z DD 011301H
	DD	06213H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ DD 020c01H
	DD	011010cH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?c_str@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBAPEB_WXZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?insert@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV12@_KQEB_W0@Z DD 021901H
	DD	07015d219H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Take_contents@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXAEAV12@@Z DD 021101H
	DD	0130111H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@$$QEAV01@@Z DD 020f01H
	DD	0700bb20fH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DD 020a01H
	DD	07006120aH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Xran@?$_String_val@U?$_Simple_types@_W@std@@@std@@SAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEAAPEA_WXZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Xlen_string@std@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Throw_bad_array_new_length@std@@YAXXZ DD 010401H
	DD	08204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1bad_array_new_length@std@@UEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_array_new_length@std@@QEAA@XZ DD 010601H
	DD	07006H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z DD 020f01H
	DD	0700b320fH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gexception@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?what@exception@std@@UEBAPEBDXZ DD 010901H
	DD	02209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0exception@std@@QEAA@AEBV01@@Z DD 020f01H
	DD	0700b320fH
xdata	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
??inst$initializer$@?$ISingleton@VWShop@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA DQ FLAT:??__E?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A@@YAXXZ ; ??inst$initializer$@?$ISingleton@VWShop@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA
CRT$XCU	ENDS
; Function compile flags: /Odtp
;	COMDAT ??__F?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A@@YAXXZ
text$yd	SEGMENT
??__F?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A@@YAXXZ PROC ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::WShop>::inst'', COMDAT
  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A ; mu2::ISingleton<mu2::WShop>::inst
  0000b	e8 00 00 00 00	 call	 ??1WShop@mu2@@UEAA@XZ	; mu2::WShop::~WShop
  00010	90		 npad	 1
  00011	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00015	c3		 ret	 0
??__F?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A@@YAXXZ ENDP ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::WShop>::inst''
text$yd	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??__E?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A@@YAXXZ
text$di	SEGMENT
??__E?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A@@YAXXZ PROC ; `dynamic initializer for 'mu2::ISingleton<mu2::WShop>::inst'', COMDAT

; 90   : template<typename T> T ISingleton<T>::inst;

  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A ; mu2::ISingleton<mu2::WShop>::inst
  0000b	e8 00 00 00 00	 call	 ??0WShop@mu2@@AEAA@XZ	; mu2::WShop::WShop
  00010	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??__F?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A@@YAXXZ ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::WShop>::inst''
  00017	e8 00 00 00 00	 call	 atexit
  0001c	90		 npad	 1
  0001d	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00021	c3		 ret	 0
??__E?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A@@YAXXZ ENDP ; `dynamic initializer for 'mu2::ISingleton<mu2::WShop>::inst''
text$di	ENDS
; Function compile flags: /Odtp
;	COMDAT ??__F?inst@?$ISingleton@VGameMsg@mu2@@@mu2@@1VGameMsg@2@A@@YAXXZ
text$yd	SEGMENT
??__F?inst@?$ISingleton@VGameMsg@mu2@@@mu2@@1VGameMsg@2@A@@YAXXZ PROC ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::GameMsg>::inst'', COMDAT
  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?inst@?$ISingleton@VGameMsg@mu2@@@mu2@@1VGameMsg@2@A ; mu2::ISingleton<mu2::GameMsg>::inst
  0000b	e8 00 00 00 00	 call	 ??1GameMsg@mu2@@UEAA@XZ	; mu2::GameMsg::~GameMsg
  00010	90		 npad	 1
  00011	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00015	c3		 ret	 0
??__F?inst@?$ISingleton@VGameMsg@mu2@@@mu2@@1VGameMsg@2@A@@YAXXZ ENDP ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::GameMsg>::inst''
text$yd	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??__E?inst@?$ISingleton@VGameMsg@mu2@@@mu2@@1VGameMsg@2@A@@YAXXZ
text$di	SEGMENT
??__E?inst@?$ISingleton@VGameMsg@mu2@@@mu2@@1VGameMsg@2@A@@YAXXZ PROC ; `dynamic initializer for 'mu2::ISingleton<mu2::GameMsg>::inst'', COMDAT

; 90   : template<typename T> T ISingleton<T>::inst;

  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?inst@?$ISingleton@VGameMsg@mu2@@@mu2@@1VGameMsg@2@A ; mu2::ISingleton<mu2::GameMsg>::inst
  0000b	e8 00 00 00 00	 call	 ??0GameMsg@mu2@@AEAA@XZ	; mu2::GameMsg::GameMsg
  00010	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??__F?inst@?$ISingleton@VGameMsg@mu2@@@mu2@@1VGameMsg@2@A@@YAXXZ ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::GameMsg>::inst''
  00017	e8 00 00 00 00	 call	 atexit
  0001c	90		 npad	 1
  0001d	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00021	c3		 ret	 0
??__E?inst@?$ISingleton@VGameMsg@mu2@@@mu2@@1VGameMsg@2@A@@YAXXZ ENDP ; `dynamic initializer for 'mu2::ISingleton<mu2::GameMsg>::inst''
text$di	ENDS
; Function compile flags: /Odtp
;	COMDAT ??_GWShopTaskPurchaseJewel@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GWShopTaskPurchaseJewel@mu2@@UEAAPEAXI@Z PROC	; mu2::WShopTaskPurchaseJewel::`scalar deleting destructor', COMDAT
$LN5:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00012	e8 00 00 00 00	 call	 ??1WShopTaskPurchaseJewel@mu2@@UEAA@XZ ; mu2::WShopTaskPurchaseJewel::~WShopTaskPurchaseJewel
  00017	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  0001b	83 e0 01	 and	 eax, 1
  0001e	85 c0		 test	 eax, eax
  00020	74 10		 je	 SHORT $LN2@scalar
  00022	ba f0 02 00 00	 mov	 edx, 752		; 000002f0H
  00027	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0002c	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00031	90		 npad	 1
$LN2@scalar:
  00032	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00037	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0003b	c3		 ret	 0
??_GWShopTaskPurchaseJewel@mu2@@UEAAPEAXI@Z ENDP	; mu2::WShopTaskPurchaseJewel::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File F:\Release_Branch\Server\Development\Framework\Entity\Action.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File F:\Release_Branch\Server\Development\Framework\Entity\Action.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\PlayerWShopAction.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File F:\Release_Branch\Shared\Protocol\Common\StructuresWShop.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\array
; File F:\Release_Branch\Shared\Protocol\Common\StructuresWShop.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
;	COMDAT ?recv@WShopTaskPurchaseJewel@mu2@@AEAA?AW4Error@ErrorWShop@2@PEAVIResponseConfirmJewelPurchase@fcsa@@@Z
_TEXT	SEGMENT
beginRequest$ = 96
type$ = 104
owner$ = 112
tv203 = 120
tv242 = 124
tv251 = 128
$T1 = 136
wShopAction$ = 144
hConsole$2 = 152
this$ = 160
elem$3 = 168
hConsole$4 = 176
tv297 = 184
this$ = 192
$T5 = 200
$T6 = 208
$T7 = 216
$T8 = 224
$T9 = 232
tv308 = 240
tv144 = 248
tv159 = 256
$T10 = 264
tv303 = 272
$T11 = 280
$T12 = 288
$T13 = 296
value$ = 304
tv230 = 312
tv225 = 320
$T14 = 328
this$ = 352
fcsRes$ = 360
?recv@WShopTaskPurchaseJewel@mu2@@AEAA?AW4Error@ErrorWShop@2@PEAVIResponseConfirmJewelPurchase@fcsa@@@Z PROC ; mu2::WShopTaskPurchaseJewel::recv, COMDAT

; 181  : {

$LN43:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 81 ec 58 01
	00 00		 sub	 rsp, 344		; 00000158H

; 182  : 	fcsa::IRequestPurchaseJewelItem* beginRequest = static_cast<fcsa::IRequestPurchaseJewelItem*>(m_beginRequest);

  00011	48 8b 84 24 60
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00019	48 8b 80 b0 02
	00 00		 mov	 rax, QWORD PTR [rax+688]
  00020	48 89 44 24 60	 mov	 QWORD PTR beginRequest$[rsp], rax

; 183  : 	VERIFY_RETURN(beginRequest, ErrorWShop::E_FAILED);

  00025	48 83 7c 24 60
	00		 cmp	 QWORD PTR beginRequest$[rsp], 0
  0002b	0f 85 ac 00 00
	00		 jne	 $LN2@recv
  00031	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  00036	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  0003c	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR hConsole$2[rsp], rax
  00044	66 ba 0d 00	 mov	 dx, 13
  00048	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR hConsole$2[rsp]
  00050	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  00056	c7 44 24 50 b7
	00 00 00	 mov	 DWORD PTR [rsp+80], 183	; 000000b7H
  0005e	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FL@OJHBMBAH@F?3?2Release_Branch?2Server?2Develo@
  00065	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  0006a	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0N@MALEHAKP@beginRequest@
  00071	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  00076	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CC@LGOLGFIH@mu2?3?3WShopTaskPurchaseJewel?3?3re@
  0007d	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  00082	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  00089	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  0008e	c7 44 24 28 b7
	00 00 00	 mov	 DWORD PTR [rsp+40], 183	; 000000b7H
  00096	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FL@OJHBMBAH@F?3?2Release_Branch?2Server?2Develo@
  0009d	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  000a2	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CC@LGOLGFIH@mu2?3?3WShopTaskPurchaseJewel?3?3re@
  000a9	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  000af	ba 02 00 00 00	 mov	 edx, 2
  000b4	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  000bb	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  000c0	66 ba 07 00	 mov	 dx, 7
  000c4	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR hConsole$2[rsp]
  000cc	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000d2	90		 npad	 1
  000d3	b8 63 cc 05 00	 mov	 eax, 380003		; 0005cc63H
  000d8	e9 59 04 00 00	 jmp	 $LN1@recv
$LN2@recv:

; 184  : 
; 185  : 	PlayerWShopAction* wShopAction = nullptr;

  000dd	48 c7 84 24 90
	00 00 00 00 00
	00 00		 mov	 QWORD PTR wShopAction$[rsp], 0

; 186  : 	EntityPlayer* owner = m_parentJob->GetOwnerPlayer();

  000e9	48 8b 84 24 60
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000f1	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  000f5	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1309 :         return _Ptr;

  000fd	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00105	48 8b 80 f0 00
	00 00		 mov	 rax, QWORD PTR [rax+240]
  0010c	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T5[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h

; 191  : 		return ptr.get();

  00114	48 8b 84 24 c8
	00 00 00	 mov	 rax, QWORD PTR $T5[rsp]
  0011c	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR $T6[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h

; 33   : 	EntityPlayer* GetOwnerPlayer() { return m_playerPtr.RawPtr(); }

  00124	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR $T6[rsp]
  0012c	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR $T7[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 186  : 	EntityPlayer* owner = m_parentJob->GetOwnerPlayer();

  00134	48 8b 84 24 d8
	00 00 00	 mov	 rax, QWORD PTR $T7[rsp]
  0013c	48 89 44 24 70	 mov	 QWORD PTR owner$[rsp], rax

; 187  : 	if (owner != nullptr)

  00141	48 83 7c 24 70
	00		 cmp	 QWORD PTR owner$[rsp], 0
  00147	0f 84 9f 00 00
	00		 je	 $LN3@recv
; File F:\Release_Branch\Server\Development\Framework\Entity\Action.h

; 43   : 	GetEntityAction(Entity* owner) : owner_(owner) {}

  0014d	48 8b 44 24 70	 mov	 rax, QWORD PTR owner$[rsp]
  00152	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR $T8[rsp], rax
  0015a	48 8d 84 24 e0
	00 00 00	 lea	 rax, QWORD PTR $T8[rsp]
  00162	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 189  : 		wShopAction = GetEntityAction(owner);

  0016a	48 8b 84 24 e8
	00 00 00	 mov	 rax, QWORD PTR $T9[rsp]
  00172	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR this$[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Entity\Action.h

; 49   : 		if ( nullptr == owner_ )

  0017a	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00182	48 83 38 00	 cmp	 QWORD PTR [rax], 0
  00186	75 0e		 jne	 SHORT $LN23@recv

; 50   : 		{
; 51   : 			return nullptr;

  00188	48 c7 84 24 88
	00 00 00 00 00
	00 00		 mov	 QWORD PTR $T1[rsp], 0
  00194	eb 46		 jmp	 SHORT $LN22@recv
$LN23@recv:

; 52   : 		}
; 53   : 
; 54   : 		Action* elem = owner_->GetAction( ActionType::ID );

  00196	ba 12 00 00 00	 mov	 edx, 18
  0019b	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  001a3	48 8b 08	 mov	 rcx, QWORD PTR [rax]
  001a6	e8 00 00 00 00	 call	 ?GetAction@Entity@mu2@@QEBAPEAVAction@2@I@Z ; mu2::Entity::GetAction
  001ab	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR elem$3[rsp], rax

; 55   : 		if ( elem == nullptr )

  001b3	48 83 bc 24 a8
	00 00 00 00	 cmp	 QWORD PTR elem$3[rsp], 0
  001bc	75 0e		 jne	 SHORT $LN24@recv

; 56   : 		{
; 57   : 			return nullptr;

  001be	48 c7 84 24 88
	00 00 00 00 00
	00 00		 mov	 QWORD PTR $T1[rsp], 0
  001ca	eb 10		 jmp	 SHORT $LN22@recv
$LN24@recv:

; 58   : 		}
; 59   : 
; 60   : 		assert( dynamic_cast<ActionType*>( elem ) != nullptr );
; 61   : 		return static_cast<ActionType*>( elem );

  001cc	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR elem$3[rsp]
  001d4	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T1[rsp], rax
$LN22@recv:
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 189  : 		wShopAction = GetEntityAction(owner);

  001dc	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T1[rsp]
  001e4	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR wShopAction$[rsp], rax
$LN3@recv:

; 190  : 	}
; 191  : 
; 192  : 	if (std::wcscmp(beginRequest->order_id(), fcsRes->order_id()) != 0)

  001ec	48 8b 84 24 68
	01 00 00	 mov	 rax, QWORD PTR fcsRes$[rsp]
  001f4	48 8b 00	 mov	 rax, QWORD PTR [rax]
  001f7	48 8b 8c 24 68
	01 00 00	 mov	 rcx, QWORD PTR fcsRes$[rsp]
  001ff	ff 90 a8 00 00
	00		 call	 QWORD PTR [rax+168]
  00205	48 89 84 24 f8
	00 00 00	 mov	 QWORD PTR tv144[rsp], rax
  0020d	48 8b 4c 24 60	 mov	 rcx, QWORD PTR beginRequest$[rsp]
  00212	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00215	48 89 8c 24 f0
	00 00 00	 mov	 QWORD PTR tv308[rsp], rcx
  0021d	48 8b 4c 24 60	 mov	 rcx, QWORD PTR beginRequest$[rsp]
  00222	48 8b 94 24 f0
	00 00 00	 mov	 rdx, QWORD PTR tv308[rsp]
  0022a	ff 92 f8 00 00
	00		 call	 QWORD PTR [rdx+248]
  00230	48 8b 8c 24 f8
	00 00 00	 mov	 rcx, QWORD PTR tv144[rsp]
  00238	48 8b d1	 mov	 rdx, rcx
  0023b	48 8b c8	 mov	 rcx, rax
  0023e	e8 00 00 00 00	 call	 wcscmp
  00243	85 c0		 test	 eax, eax
  00245	0f 84 d4 00 00
	00		 je	 $LN4@recv

; 194  : 		MU2_ERROR_LOG(LogCategory::WSHOP, L"WSHOP(%u),%llu : IResponsePurchaseJewelItem> mismatch order id (%s)", 

  0024b	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  00250	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  00256	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR hConsole$4[rsp], rax
  0025e	66 ba 0d 00	 mov	 dx, 13
  00262	48 8b 8c 24 b0
	00 00 00	 mov	 rcx, QWORD PTR hConsole$4[rsp]
  0026a	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h

; 30   : 	WShopDbUser& GetOwnerInfo() { return m_ownerInfo; }

  00270	48 8b 84 24 60
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00278	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0027c	48 83 c0 38	 add	 rax, 56			; 00000038H
  00280	48 89 84 24 08
	01 00 00	 mov	 QWORD PTR $T10[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 194  : 		MU2_ERROR_LOG(LogCategory::WSHOP, L"WSHOP(%u),%llu : IResponsePurchaseJewelItem> mismatch order id (%s)", 

  00288	48 8b 44 24 60	 mov	 rax, QWORD PTR beginRequest$[rsp]
  0028d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00290	48 8b 4c 24 60	 mov	 rcx, QWORD PTR beginRequest$[rsp]
  00295	ff 50 58	 call	 QWORD PTR [rax+88]
  00298	48 89 84 24 00
	01 00 00	 mov	 QWORD PTR tv159[rsp], rax
  002a0	48 8b 8c 24 60
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  002a8	e8 00 00 00 00	 call	 ?GetParentJobNo@WShopTask@mu2@@QEAA_KXZ ; mu2::WShopTask::GetParentJobNo
  002ad	48 8b 8c 24 00
	01 00 00	 mov	 rcx, QWORD PTR tv159[rsp]
  002b5	48 89 4c 24 48	 mov	 QWORD PTR [rsp+72], rcx
  002ba	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  002bf	48 8b 84 24 08
	01 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  002c7	8b 40 0c	 mov	 eax, DWORD PTR [rax+12]
  002ca	89 44 24 38	 mov	 DWORD PTR [rsp+56], eax
  002ce	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_1II@HMCPJHON@?$AAW?$AAS?$AAH?$AAO?$AAP?$AA?$CI?$AA?$CF?$AAu?$AA?$CJ?$AA?0?$AA?$CF?$AAl?$AAl?$AAu?$AA?5@
  002d5	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  002da	c7 44 24 28 c3
	00 00 00	 mov	 DWORD PTR [rsp+40], 195	; 000000c3H
  002e2	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FL@OJHBMBAH@F?3?2Release_Branch?2Server?2Develo@
  002e9	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  002ee	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CC@LGOLGFIH@mu2?3?3WShopTaskPurchaseJewel?3?3re@
  002f5	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  002fb	ba 14 00 00 00	 mov	 edx, 20
  00300	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  00307	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H0ZZ ; mu2::Logger::Logging
  0030c	66 ba 07 00	 mov	 dx, 7
  00310	48 8b 8c 24 b0
	00 00 00	 mov	 rcx, QWORD PTR hConsole$4[rsp]
  00318	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  0031e	90		 npad	 1
$LN4@recv:

; 195  : 			m_parentJob->GetOwnerInfo().fcsAccountId, GetParentJobNo(), beginRequest->callback_attribute());
; 196  : 	}
; 197  : 
; 198  : 	if (fcsRes->isSuccess())

  0031f	48 8b 84 24 68
	01 00 00	 mov	 rax, QWORD PTR fcsRes$[rsp]
  00327	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0032a	48 8b 8c 24 68
	01 00 00	 mov	 rcx, QWORD PTR fcsRes$[rsp]
  00332	ff 50 68	 call	 QWORD PTR [rax+104]
  00335	0f b6 c0	 movzx	 eax, al
  00338	85 c0		 test	 eax, eax
  0033a	74 7b		 je	 SHORT $LN5@recv

; 199  : 	{
; 200  : 		if (owner != nullptr)

  0033c	48 83 7c 24 70
	00		 cmp	 QWORD PTR owner$[rsp], 0
  00342	74 6e		 je	 SHORT $LN7@recv
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  00344	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VGameMsg@mu2@@@mu2@@1VGameMsg@2@A ; mu2::ISingleton<mu2::GameMsg>::inst
  0034b	48 89 84 24 18
	01 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 202  : 			theGameMsg.SendGameDebugMsg(owner, L"PurchaseJewel 1 success cash_type %d amount %d",

  00353	48 8b 44 24 60	 mov	 rax, QWORD PTR beginRequest$[rsp]
  00358	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0035b	48 8b 4c 24 60	 mov	 rcx, QWORD PTR beginRequest$[rsp]
  00360	ff 90 58 01 00
	00		 call	 QWORD PTR [rax+344]
  00366	89 44 24 78	 mov	 DWORD PTR tv203[rsp], eax
  0036a	48 8b 4c 24 60	 mov	 rcx, QWORD PTR beginRequest$[rsp]
  0036f	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00372	48 89 8c 24 10
	01 00 00	 mov	 QWORD PTR tv303[rsp], rcx
  0037a	48 8b 4c 24 60	 mov	 rcx, QWORD PTR beginRequest$[rsp]
  0037f	48 8b 94 24 10
	01 00 00	 mov	 rdx, QWORD PTR tv303[rsp]
  00387	ff 92 28 01 00
	00		 call	 QWORD PTR [rdx+296]
  0038d	48 8b 8c 24 18
	01 00 00	 mov	 rcx, QWORD PTR $T11[rsp]
  00395	8b 54 24 78	 mov	 edx, DWORD PTR tv203[rsp]
  00399	89 54 24 20	 mov	 DWORD PTR [rsp+32], edx
  0039d	44 8b c8	 mov	 r9d, eax
  003a0	4c 8d 05 00 00
	00 00		 lea	 r8, OFFSET FLAT:??_C@_1FO@BAMCKIJN@?$AAP?$AAu?$AAr?$AAc?$AAh?$AAa?$AAs?$AAe?$AAJ?$AAe?$AAw?$AAe?$AAl?$AA?5?$AA1@
  003a7	48 8b 54 24 70	 mov	 rdx, QWORD PTR owner$[rsp]
  003ac	e8 00 00 00 00	 call	 ?SendGameDebugMsg@GameMsg@mu2@@QEAA_NPEAVEntity@2@PEA_WZZ ; mu2::GameMsg::SendGameDebugMsg
  003b1	90		 npad	 1
$LN7@recv:

; 203  : 				beginRequest->cash_type(), beginRequest->purchase_amount());
; 204  : 		}
; 205  : 	}

  003b2	e9 6e 01 00 00	 jmp	 $LN6@recv
$LN5@recv:

; 206  : 	else
; 207  : 	{
; 208  : 		if (wShopAction != nullptr)

  003b7	48 83 bc 24 90
	00 00 00 00	 cmp	 QWORD PTR wShopAction$[rsp], 0
  003c0	0f 84 a1 00 00
	00		 je	 $LN8@recv
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\PlayerWShopAction.h

; 36   : 	WShopWallet& GetWallet() { return m_wallet; }

  003c6	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR wShopAction$[rsp]
  003ce	48 05 b0 01 00
	00		 add	 rax, 432		; 000001b0H
  003d4	48 89 84 24 20
	01 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 210  : 			wShopAction->GetWallet().AddMoney(beginRequest->cash_type(), beginRequest->purchase_amount());

  003dc	48 8b 44 24 60	 mov	 rax, QWORD PTR beginRequest$[rsp]
  003e1	48 8b 00	 mov	 rax, QWORD PTR [rax]
  003e4	48 8b 4c 24 60	 mov	 rcx, QWORD PTR beginRequest$[rsp]
  003e9	ff 90 58 01 00
	00		 call	 QWORD PTR [rax+344]
  003ef	48 98		 cdqe
  003f1	48 89 84 24 30
	01 00 00	 mov	 QWORD PTR value$[rsp], rax
  003f9	48 8b 44 24 60	 mov	 rax, QWORD PTR beginRequest$[rsp]
  003fe	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00401	48 8b 4c 24 60	 mov	 rcx, QWORD PTR beginRequest$[rsp]
  00406	ff 90 28 01 00
	00		 call	 QWORD PTR [rax+296]
  0040c	89 44 24 68	 mov	 DWORD PTR type$[rsp], eax
; File F:\Release_Branch\Shared\Protocol\Common\StructuresWShop.h

; 193  : 		if (type <= fcsa::saleCoinType::kSCT_UNKNOWN ||

  00410	83 7c 24 68 00	 cmp	 DWORD PTR type$[rsp], 0
  00415	7e 07		 jle	 SHORT $LN34@recv
  00417	83 7c 24 68 03	 cmp	 DWORD PTR type$[rsp], 3
  0041c	7e 02		 jle	 SHORT $LN33@recv
$LN34@recv:

; 194  : 			type > fcsa::saleCoinType::kSCT_IMPUTATION_REDGEN)
; 195  : 		{
; 196  : 			return false;

  0041e	eb 47		 jmp	 SHORT $LN8@recv
$LN33@recv:

; 199  : 		coins[ type ] += value;

  00420	48 63 44 24 68	 movsxd	 rax, DWORD PTR type$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\array

; 534  :         return _Elems[_Pos];

  00425	48 8b 8c 24 20
	01 00 00	 mov	 rcx, QWORD PTR $T12[rsp]
  0042d	48 8d 04 c1	 lea	 rax, QWORD PTR [rcx+rax*8]
  00431	48 89 84 24 28
	01 00 00	 mov	 QWORD PTR $T13[rsp], rax
; File F:\Release_Branch\Shared\Protocol\Common\StructuresWShop.h

; 199  : 		coins[ type ] += value;

  00439	48 8b 84 24 28
	01 00 00	 mov	 rax, QWORD PTR $T13[rsp]
  00441	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR tv297[rsp], rax
  00449	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR tv297[rsp]
  00451	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00454	48 03 84 24 30
	01 00 00	 add	 rax, QWORD PTR value$[rsp]
  0045c	48 8b 8c 24 b8
	00 00 00	 mov	 rcx, QWORD PTR tv297[rsp]
  00464	48 89 01	 mov	 QWORD PTR [rcx], rax
$LN8@recv:
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 213  : 		if (owner != nullptr)

  00467	48 83 7c 24 70
	00		 cmp	 QWORD PTR owner$[rsp], 0
  0046d	0f 84 b2 00 00
	00		 je	 $LN6@recv
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  00473	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VGameMsg@mu2@@@mu2@@1VGameMsg@2@A ; mu2::ISingleton<mu2::GameMsg>::inst
  0047a	48 89 84 24 48
	01 00 00	 mov	 QWORD PTR $T14[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 215  : 			theGameMsg.SendGameDebugMsg(owner, L"PurchaseJewel 0 IResponseConfirmJewelPurchase> Fail : type(%d), result(%d), condition(%d)",

  00482	48 8b 84 24 68
	01 00 00	 mov	 rax, QWORD PTR fcsRes$[rsp]
  0048a	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0048d	48 8b 8c 24 68
	01 00 00	 mov	 rcx, QWORD PTR fcsRes$[rsp]
  00495	ff 50 60	 call	 QWORD PTR [rax+96]
  00498	0f b6 c0	 movzx	 eax, al
  0049b	89 44 24 7c	 mov	 DWORD PTR tv242[rsp], eax
  0049f	48 8b 8c 24 68
	01 00 00	 mov	 rcx, QWORD PTR fcsRes$[rsp]
  004a7	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  004aa	48 89 8c 24 38
	01 00 00	 mov	 QWORD PTR tv230[rsp], rcx
  004b2	48 8b 8c 24 68
	01 00 00	 mov	 rcx, QWORD PTR fcsRes$[rsp]
  004ba	48 8b 94 24 38
	01 00 00	 mov	 rdx, QWORD PTR tv230[rsp]
  004c2	ff 52 50	 call	 QWORD PTR [rdx+80]
  004c5	89 84 24 80 00
	00 00		 mov	 DWORD PTR tv251[rsp], eax
  004cc	48 8b 8c 24 68
	01 00 00	 mov	 rcx, QWORD PTR fcsRes$[rsp]
  004d4	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  004d7	48 89 8c 24 40
	01 00 00	 mov	 QWORD PTR tv225[rsp], rcx
  004df	48 8b 8c 24 68
	01 00 00	 mov	 rcx, QWORD PTR fcsRes$[rsp]
  004e7	48 8b 94 24 40
	01 00 00	 mov	 rdx, QWORD PTR tv225[rsp]
  004ef	ff 52 28	 call	 QWORD PTR [rdx+40]
  004f2	0f b6 c0	 movzx	 eax, al
  004f5	48 8b 8c 24 48
	01 00 00	 mov	 rcx, QWORD PTR $T14[rsp]
  004fd	8b 54 24 7c	 mov	 edx, DWORD PTR tv242[rsp]
  00501	89 54 24 28	 mov	 DWORD PTR [rsp+40], edx
  00505	8b 94 24 80 00
	00 00		 mov	 edx, DWORD PTR tv251[rsp]
  0050c	89 54 24 20	 mov	 DWORD PTR [rsp+32], edx
  00510	44 8b c8	 mov	 r9d, eax
  00513	4c 8d 05 00 00
	00 00		 lea	 r8, OFFSET FLAT:??_C@_1LE@LEGCOMOK@?$AAP?$AAu?$AAr?$AAc?$AAh?$AAa?$AAs?$AAe?$AAJ?$AAe?$AAw?$AAe?$AAl?$AA?5?$AA0@
  0051a	48 8b 54 24 70	 mov	 rdx, QWORD PTR owner$[rsp]
  0051f	e8 00 00 00 00	 call	 ?SendGameDebugMsg@GameMsg@mu2@@QEAA_NPEAVEntity@2@PEA_WZZ ; mu2::GameMsg::SendGameDebugMsg
  00524	90		 npad	 1
$LN6@recv:

; 216  : 				fcsRes->packet_type(), fcsRes->result_code(), fcsRes->condition_type());
; 217  : 		}
; 218  : 	}
; 219  : 
; 220  : 	// 최종 종료된 경우
; 221  : 	m_state = WShopTask::FINISHED;

  00525	48 8b 84 24 60
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0052d	c7 40 08 08 00
	00 00		 mov	 DWORD PTR [rax+8], 8

; 222  : 
; 223  : 	return ErrorWShop::SUCCESS;

  00534	33 c0		 xor	 eax, eax
$LN1@recv:

; 224  : }

  00536	48 81 c4 58 01
	00 00		 add	 rsp, 344		; 00000158H
  0053d	c3		 ret	 0
?recv@WShopTaskPurchaseJewel@mu2@@AEAA?AW4Error@ErrorWShop@2@PEAVIResponseConfirmJewelPurchase@fcsa@@@Z ENDP ; mu2::WShopTaskPurchaseJewel::recv
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File F:\Release_Branch\Server\Development\Framework\Entity\Action.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File F:\Release_Branch\Server\Development\Framework\Entity\Action.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\PlayerWShopAction.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File F:\Release_Branch\Shared\Protocol\Common\StructuresWShop.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\array
; File F:\Release_Branch\Shared\Protocol\Common\StructuresWShop.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
;	COMDAT ?recv@WShopTaskPurchaseJewel@mu2@@AEAA?AW4Error@ErrorWShop@2@PEAVIResponsePurchaseJewelItem@fcsa@@@Z
_TEXT	SEGMENT
beginRequest$ = 96
type$ = 104
owner$ = 112
tv226 = 120
$T1 = 128
wShopAction$ = 136
hConsole$2 = 144
this$ = 152
elem$3 = 160
hConsole$4 = 168
tv267 = 176
this$ = 184
$T5 = 192
$T6 = 200
$T7 = 208
$T8 = 216
$T9 = 224
tv275 = 232
tv144 = 240
tv159 = 248
$T10 = 256
$T11 = 264
$T12 = 272
value$ = 280
tv206 = 288
$T13 = 296
this$ = 320
fcsRes$ = 328
?recv@WShopTaskPurchaseJewel@mu2@@AEAA?AW4Error@ErrorWShop@2@PEAVIResponsePurchaseJewelItem@fcsa@@@Z PROC ; mu2::WShopTaskPurchaseJewel::recv, COMDAT

; 113  : {

$LN40:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 81 ec 38 01
	00 00		 sub	 rsp, 312		; 00000138H

; 114  : 	fcsa::IRequestPurchaseJewelItem* beginRequest = static_cast<fcsa::IRequestPurchaseJewelItem*>(m_beginRequest);

  00011	48 8b 84 24 40
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00019	48 8b 80 b0 02
	00 00		 mov	 rax, QWORD PTR [rax+688]
  00020	48 89 44 24 60	 mov	 QWORD PTR beginRequest$[rsp], rax

; 115  : 	VERIFY_RETURN(beginRequest, ErrorWShop::E_SYSTEM_NULL_POINTER);

  00025	48 83 7c 24 60
	00		 cmp	 QWORD PTR beginRequest$[rsp], 0
  0002b	0f 85 ac 00 00
	00		 jne	 $LN2@recv
  00031	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  00036	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  0003c	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR hConsole$2[rsp], rax
  00044	66 ba 0d 00	 mov	 dx, 13
  00048	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR hConsole$2[rsp]
  00050	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  00056	c7 44 24 50 73
	00 00 00	 mov	 DWORD PTR [rsp+80], 115	; 00000073H
  0005e	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FL@OJHBMBAH@F?3?2Release_Branch?2Server?2Develo@
  00065	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  0006a	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0N@MALEHAKP@beginRequest@
  00071	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  00076	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CC@LGOLGFIH@mu2?3?3WShopTaskPurchaseJewel?3?3re@
  0007d	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  00082	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  00089	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  0008e	c7 44 24 28 73
	00 00 00	 mov	 DWORD PTR [rsp+40], 115	; 00000073H
  00096	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FL@OJHBMBAH@F?3?2Release_Branch?2Server?2Develo@
  0009d	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  000a2	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CC@LGOLGFIH@mu2?3?3WShopTaskPurchaseJewel?3?3re@
  000a9	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  000af	ba 02 00 00 00	 mov	 edx, 2
  000b4	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  000bb	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  000c0	66 ba 07 00	 mov	 dx, 7
  000c4	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR hConsole$2[rsp]
  000cc	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000d2	90		 npad	 1
  000d3	b8 65 cc 05 00	 mov	 eax, 380005		; 0005cc65H
  000d8	e9 c7 03 00 00	 jmp	 $LN1@recv
$LN2@recv:

; 116  : 	
; 117  : 	PlayerWShopAction* wShopAction = nullptr;

  000dd	48 c7 84 24 88
	00 00 00 00 00
	00 00		 mov	 QWORD PTR wShopAction$[rsp], 0

; 118  : 	EntityPlayer* owner = m_parentJob->GetOwnerPlayer();

  000e9	48 8b 84 24 40
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000f1	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  000f5	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1309 :         return _Ptr;

  000fd	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00105	48 8b 80 f0 00
	00 00		 mov	 rax, QWORD PTR [rax+240]
  0010c	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T5[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h

; 191  : 		return ptr.get();

  00114	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR $T5[rsp]
  0011c	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T6[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h

; 33   : 	EntityPlayer* GetOwnerPlayer() { return m_playerPtr.RawPtr(); }

  00124	48 8b 84 24 c8
	00 00 00	 mov	 rax, QWORD PTR $T6[rsp]
  0012c	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR $T7[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 118  : 	EntityPlayer* owner = m_parentJob->GetOwnerPlayer();

  00134	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR $T7[rsp]
  0013c	48 89 44 24 70	 mov	 QWORD PTR owner$[rsp], rax

; 119  : 	if (owner != nullptr)

  00141	48 83 7c 24 70
	00		 cmp	 QWORD PTR owner$[rsp], 0
  00147	0f 84 9f 00 00
	00		 je	 $LN3@recv
; File F:\Release_Branch\Server\Development\Framework\Entity\Action.h

; 43   : 	GetEntityAction(Entity* owner) : owner_(owner) {}

  0014d	48 8b 44 24 70	 mov	 rax, QWORD PTR owner$[rsp]
  00152	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR $T8[rsp], rax
  0015a	48 8d 84 24 d8
	00 00 00	 lea	 rax, QWORD PTR $T8[rsp]
  00162	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 121  : 		wShopAction = GetEntityAction(owner);

  0016a	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR $T9[rsp]
  00172	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR this$[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Entity\Action.h

; 49   : 		if ( nullptr == owner_ )

  0017a	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00182	48 83 38 00	 cmp	 QWORD PTR [rax], 0
  00186	75 0e		 jne	 SHORT $LN22@recv

; 50   : 		{
; 51   : 			return nullptr;

  00188	48 c7 84 24 80
	00 00 00 00 00
	00 00		 mov	 QWORD PTR $T1[rsp], 0
  00194	eb 46		 jmp	 SHORT $LN21@recv
$LN22@recv:

; 52   : 		}
; 53   : 
; 54   : 		Action* elem = owner_->GetAction( ActionType::ID );

  00196	ba 12 00 00 00	 mov	 edx, 18
  0019b	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  001a3	48 8b 08	 mov	 rcx, QWORD PTR [rax]
  001a6	e8 00 00 00 00	 call	 ?GetAction@Entity@mu2@@QEBAPEAVAction@2@I@Z ; mu2::Entity::GetAction
  001ab	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR elem$3[rsp], rax

; 55   : 		if ( elem == nullptr )

  001b3	48 83 bc 24 a0
	00 00 00 00	 cmp	 QWORD PTR elem$3[rsp], 0
  001bc	75 0e		 jne	 SHORT $LN23@recv

; 56   : 		{
; 57   : 			return nullptr;

  001be	48 c7 84 24 80
	00 00 00 00 00
	00 00		 mov	 QWORD PTR $T1[rsp], 0
  001ca	eb 10		 jmp	 SHORT $LN21@recv
$LN23@recv:

; 58   : 		}
; 59   : 
; 60   : 		assert( dynamic_cast<ActionType*>( elem ) != nullptr );
; 61   : 		return static_cast<ActionType*>( elem );

  001cc	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR elem$3[rsp]
  001d4	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T1[rsp], rax
$LN21@recv:
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 121  : 		wShopAction = GetEntityAction(owner);

  001dc	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR $T1[rsp]
  001e4	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR wShopAction$[rsp], rax
$LN3@recv:

; 122  : 	}
; 123  : 
; 124  : 	if (std::wcscmp(beginRequest->order_id(), fcsRes->order_id()) != 0)

  001ec	48 8b 84 24 48
	01 00 00	 mov	 rax, QWORD PTR fcsRes$[rsp]
  001f4	48 8b 00	 mov	 rax, QWORD PTR [rax]
  001f7	48 8b 8c 24 48
	01 00 00	 mov	 rcx, QWORD PTR fcsRes$[rsp]
  001ff	ff 90 a8 00 00
	00		 call	 QWORD PTR [rax+168]
  00205	48 89 84 24 f0
	00 00 00	 mov	 QWORD PTR tv144[rsp], rax
  0020d	48 8b 4c 24 60	 mov	 rcx, QWORD PTR beginRequest$[rsp]
  00212	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00215	48 89 8c 24 e8
	00 00 00	 mov	 QWORD PTR tv275[rsp], rcx
  0021d	48 8b 4c 24 60	 mov	 rcx, QWORD PTR beginRequest$[rsp]
  00222	48 8b 94 24 e8
	00 00 00	 mov	 rdx, QWORD PTR tv275[rsp]
  0022a	ff 92 f8 00 00
	00		 call	 QWORD PTR [rdx+248]
  00230	48 8b 8c 24 f0
	00 00 00	 mov	 rcx, QWORD PTR tv144[rsp]
  00238	48 8b d1	 mov	 rdx, rcx
  0023b	48 8b c8	 mov	 rcx, rax
  0023e	e8 00 00 00 00	 call	 wcscmp
  00243	85 c0		 test	 eax, eax
  00245	0f 84 d4 00 00
	00		 je	 $LN4@recv

; 126  : 		MU2_ERROR_LOG(LogCategory::WSHOP, L"WSHOP(%u),%llu : IResponsePurchaseJewelItem> mismatch order id (%s)", 

  0024b	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  00250	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  00256	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR hConsole$4[rsp], rax
  0025e	66 ba 0d 00	 mov	 dx, 13
  00262	48 8b 8c 24 a8
	00 00 00	 mov	 rcx, QWORD PTR hConsole$4[rsp]
  0026a	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h

; 30   : 	WShopDbUser& GetOwnerInfo() { return m_ownerInfo; }

  00270	48 8b 84 24 40
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00278	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0027c	48 83 c0 38	 add	 rax, 56			; 00000038H
  00280	48 89 84 24 00
	01 00 00	 mov	 QWORD PTR $T10[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 126  : 		MU2_ERROR_LOG(LogCategory::WSHOP, L"WSHOP(%u),%llu : IResponsePurchaseJewelItem> mismatch order id (%s)", 

  00288	48 8b 44 24 60	 mov	 rax, QWORD PTR beginRequest$[rsp]
  0028d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00290	48 8b 4c 24 60	 mov	 rcx, QWORD PTR beginRequest$[rsp]
  00295	ff 50 58	 call	 QWORD PTR [rax+88]
  00298	48 89 84 24 f8
	00 00 00	 mov	 QWORD PTR tv159[rsp], rax
  002a0	48 8b 8c 24 40
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  002a8	e8 00 00 00 00	 call	 ?GetParentJobNo@WShopTask@mu2@@QEAA_KXZ ; mu2::WShopTask::GetParentJobNo
  002ad	48 8b 8c 24 f8
	00 00 00	 mov	 rcx, QWORD PTR tv159[rsp]
  002b5	48 89 4c 24 48	 mov	 QWORD PTR [rsp+72], rcx
  002ba	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  002bf	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  002c7	8b 40 0c	 mov	 eax, DWORD PTR [rax+12]
  002ca	89 44 24 38	 mov	 DWORD PTR [rsp+56], eax
  002ce	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_1II@HMCPJHON@?$AAW?$AAS?$AAH?$AAO?$AAP?$AA?$CI?$AA?$CF?$AAu?$AA?$CJ?$AA?0?$AA?$CF?$AAl?$AAl?$AAu?$AA?5@
  002d5	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  002da	c7 44 24 28 7f
	00 00 00	 mov	 DWORD PTR [rsp+40], 127	; 0000007fH
  002e2	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FL@OJHBMBAH@F?3?2Release_Branch?2Server?2Develo@
  002e9	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  002ee	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CC@LGOLGFIH@mu2?3?3WShopTaskPurchaseJewel?3?3re@
  002f5	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  002fb	ba 14 00 00 00	 mov	 edx, 20
  00300	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  00307	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H0ZZ ; mu2::Logger::Logging
  0030c	66 ba 07 00	 mov	 dx, 7
  00310	48 8b 8c 24 a8
	00 00 00	 mov	 rcx, QWORD PTR hConsole$4[rsp]
  00318	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  0031e	90		 npad	 1
$LN4@recv:

; 127  : 			m_parentJob->GetOwnerInfo().fcsAccountId, GetParentJobNo(), beginRequest->callback_attribute());
; 128  : 	}
; 129  : 		
; 130  : 	if (fcsRes->isSuccess())

  0031f	48 8b 84 24 48
	01 00 00	 mov	 rax, QWORD PTR fcsRes$[rsp]
  00327	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0032a	48 8b 8c 24 48
	01 00 00	 mov	 rcx, QWORD PTR fcsRes$[rsp]
  00332	ff 50 68	 call	 QWORD PTR [rax+104]
  00335	0f b6 c0	 movzx	 eax, al
  00338	85 c0		 test	 eax, eax
  0033a	0f 84 d6 00 00
	00		 je	 $LN5@recv

; 131  : 	{
; 132  : 		if(wShopAction != nullptr)

  00340	48 83 bc 24 88
	00 00 00 00	 cmp	 QWORD PTR wShopAction$[rsp], 0
  00349	0f 84 c2 00 00
	00		 je	 $LN7@recv
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\PlayerWShopAction.h

; 36   : 	WShopWallet& GetWallet() { return m_wallet; }

  0034f	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR wShopAction$[rsp]
  00357	48 05 b0 01 00
	00		 add	 rax, 432		; 000001b0H
  0035d	48 89 84 24 08
	01 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 134  : 			wShopAction->GetWallet().SubMoney(beginRequest->cash_type(), beginRequest->purchase_amount());

  00365	48 8b 44 24 60	 mov	 rax, QWORD PTR beginRequest$[rsp]
  0036a	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0036d	48 8b 4c 24 60	 mov	 rcx, QWORD PTR beginRequest$[rsp]
  00372	ff 90 58 01 00
	00		 call	 QWORD PTR [rax+344]
  00378	48 98		 cdqe
  0037a	48 89 84 24 18
	01 00 00	 mov	 QWORD PTR value$[rsp], rax
  00382	48 8b 44 24 60	 mov	 rax, QWORD PTR beginRequest$[rsp]
  00387	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0038a	48 8b 4c 24 60	 mov	 rcx, QWORD PTR beginRequest$[rsp]
  0038f	ff 90 28 01 00
	00		 call	 QWORD PTR [rax+296]
  00395	89 44 24 68	 mov	 DWORD PTR type$[rsp], eax
; File F:\Release_Branch\Shared\Protocol\Common\StructuresWShop.h

; 205  : 		if (type <= fcsa::saleCoinType::kSCT_UNKNOWN ||

  00399	83 7c 24 68 00	 cmp	 DWORD PTR type$[rsp], 0
  0039e	7e 07		 jle	 SHORT $LN31@recv
  003a0	83 7c 24 68 03	 cmp	 DWORD PTR type$[rsp], 3
  003a5	7e 02		 jle	 SHORT $LN30@recv
$LN31@recv:

; 206  : 			type > fcsa::saleCoinType::kSCT_IMPUTATION_REDGEN)
; 207  : 		{
; 208  : 			return false;

  003a7	eb 4a		 jmp	 SHORT $LN29@recv
$LN30@recv:

; 211  : 		coins[ type ] -= value;

  003a9	48 63 44 24 68	 movsxd	 rax, DWORD PTR type$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\array

; 534  :         return _Elems[_Pos];

  003ae	48 8b 8c 24 08
	01 00 00	 mov	 rcx, QWORD PTR $T11[rsp]
  003b6	48 8d 04 c1	 lea	 rax, QWORD PTR [rcx+rax*8]
  003ba	48 89 84 24 10
	01 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File F:\Release_Branch\Shared\Protocol\Common\StructuresWShop.h

; 211  : 		coins[ type ] -= value;

  003c2	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR $T12[rsp]
  003ca	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR tv267[rsp], rax
  003d2	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR tv267[rsp]
  003da	48 8b 8c 24 18
	01 00 00	 mov	 rcx, QWORD PTR value$[rsp]
  003e2	48 8b 00	 mov	 rax, QWORD PTR [rax]
  003e5	48 2b c1	 sub	 rax, rcx
  003e8	48 8b 8c 24 b0
	00 00 00	 mov	 rcx, QWORD PTR tv267[rsp]
  003f0	48 89 01	 mov	 QWORD PTR [rcx], rax
$LN29@recv:
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 136  : 			return sendConfirmRequest();

  003f3	48 8b 84 24 40
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  003fb	48 8b 00	 mov	 rax, QWORD PTR [rax]
  003fe	48 8b 8c 24 40
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00406	ff 90 88 00 00
	00		 call	 QWORD PTR [rax+136]
  0040c	e9 93 00 00 00	 jmp	 $LN1@recv
$LN7@recv:

; 137  : 		}
; 138  : 		// 유저가 없다면 자동 롤백
; 139  : 	}

  00411	e9 8c 00 00 00	 jmp	 $LN6@recv
$LN5@recv:

; 140  : 	else
; 141  : 	{
; 142  : 		if (owner != nullptr)

  00416	48 83 7c 24 70
	00		 cmp	 QWORD PTR owner$[rsp], 0
  0041c	74 6e		 je	 SHORT $LN8@recv
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  0041e	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VGameMsg@mu2@@@mu2@@1VGameMsg@2@A ; mu2::ISingleton<mu2::GameMsg>::inst
  00425	48 89 84 24 28
	01 00 00	 mov	 QWORD PTR $T13[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 144  : 			theGameMsg.SendGameDebugMsg(owner, L"PurchaseJewel 0 spent money failed : type = %d, amount = %d", beginRequest->cash_type(), beginRequest->purchase_amount());

  0042d	48 8b 44 24 60	 mov	 rax, QWORD PTR beginRequest$[rsp]
  00432	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00435	48 8b 4c 24 60	 mov	 rcx, QWORD PTR beginRequest$[rsp]
  0043a	ff 90 58 01 00
	00		 call	 QWORD PTR [rax+344]
  00440	89 44 24 78	 mov	 DWORD PTR tv226[rsp], eax
  00444	48 8b 4c 24 60	 mov	 rcx, QWORD PTR beginRequest$[rsp]
  00449	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0044c	48 89 8c 24 20
	01 00 00	 mov	 QWORD PTR tv206[rsp], rcx
  00454	48 8b 4c 24 60	 mov	 rcx, QWORD PTR beginRequest$[rsp]
  00459	48 8b 94 24 20
	01 00 00	 mov	 rdx, QWORD PTR tv206[rsp]
  00461	ff 92 28 01 00
	00		 call	 QWORD PTR [rdx+296]
  00467	48 8b 8c 24 28
	01 00 00	 mov	 rcx, QWORD PTR $T13[rsp]
  0046f	8b 54 24 78	 mov	 edx, DWORD PTR tv226[rsp]
  00473	89 54 24 20	 mov	 DWORD PTR [rsp+32], edx
  00477	44 8b c8	 mov	 r9d, eax
  0047a	4c 8d 05 00 00
	00 00		 lea	 r8, OFFSET FLAT:??_C@_1HI@IDJCKHMJ@?$AAP?$AAu?$AAr?$AAc?$AAh?$AAa?$AAs?$AAe?$AAJ?$AAe?$AAw?$AAe?$AAl?$AA?5?$AA0@
  00481	48 8b 54 24 70	 mov	 rdx, QWORD PTR owner$[rsp]
  00486	e8 00 00 00 00	 call	 ?SendGameDebugMsg@GameMsg@mu2@@QEAA_NPEAVEntity@2@PEA_WZZ ; mu2::GameMsg::SendGameDebugMsg
  0048b	90		 npad	 1
$LN8@recv:

; 145  : 		}
; 146  : 		m_state = WShopTask::FINISHED;

  0048c	48 8b 84 24 40
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00494	c7 40 08 08 00
	00 00		 mov	 DWORD PTR [rax+8], 8

; 147  : 		return ErrorWShop::E_RETURN_FAIL_ON_FCSA;

  0049b	b8 68 cc 05 00	 mov	 eax, 380008		; 0005cc68H
  004a0	eb 02		 jmp	 SHORT $LN1@recv
$LN6@recv:

; 148  : 	}
; 149  : 
; 150  : 	return ErrorWShop::SUCCESS;

  004a2	33 c0		 xor	 eax, eax
$LN1@recv:

; 151  : }

  004a4	48 81 c4 38 01
	00 00		 add	 rsp, 312		; 00000138H
  004ab	c3		 ret	 0
?recv@WShopTaskPurchaseJewel@mu2@@AEAA?AW4Error@ErrorWShop@2@PEAVIResponsePurchaseJewelItem@fcsa@@@Z ENDP ; mu2::WShopTaskPurchaseJewel::recv
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
;	COMDAT ?sendConfirmRequest@WShopTaskPurchaseJewel@mu2@@MEAA?AW4Error@ErrorWShop@2@XZ
_TEXT	SEGMENT
fcsNextReq$ = 96
beginRequest$ = 104
result$ = 112
tv191 = 116
hConsole$1 = 120
tv95 = 128
tv164 = 136
$T2 = 144
$T3 = 152
tv230 = 160
tv228 = 168
tv226 = 176
$T4 = 184
$T5 = 192
tv198 = 200
this$ = 224
?sendConfirmRequest@WShopTaskPurchaseJewel@mu2@@MEAA?AW4Error@ErrorWShop@2@XZ PROC ; mu2::WShopTaskPurchaseJewel::sendConfirmRequest, COMDAT

; 154  : {

$LN14:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec d8 00
	00 00		 sub	 rsp, 216		; 000000d8H

; 155  : 	fcsa::IRequestPurchaseJewelItem* beginRequest = static_cast<fcsa::IRequestPurchaseJewelItem*>(m_beginRequest);

  0000c	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 8b 80 b0 02
	00 00		 mov	 rax, QWORD PTR [rax+688]
  0001b	48 89 44 24 68	 mov	 QWORD PTR beginRequest$[rsp], rax

; 156  : 	VERIFY_RETURN(beginRequest, ErrorWShop::E_SYSTEM_NULL_POINTER);

  00020	48 83 7c 24 68
	00		 cmp	 QWORD PTR beginRequest$[rsp], 0
  00026	0f 85 a3 00 00
	00		 jne	 $LN2@sendConfir
  0002c	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  00031	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  00037	48 89 44 24 78	 mov	 QWORD PTR hConsole$1[rsp], rax
  0003c	66 ba 0d 00	 mov	 dx, 13
  00040	48 8b 4c 24 78	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  00045	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  0004b	c7 44 24 50 9c
	00 00 00	 mov	 DWORD PTR [rsp+80], 156	; 0000009cH
  00053	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FL@OJHBMBAH@F?3?2Release_Branch?2Server?2Develo@
  0005a	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  0005f	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0N@MALEHAKP@beginRequest@
  00066	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  0006b	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0DA@LHOBOCCA@mu2?3?3WShopTaskPurchaseJewel?3?3se@
  00072	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  00077	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  0007e	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  00083	c7 44 24 28 9c
	00 00 00	 mov	 DWORD PTR [rsp+40], 156	; 0000009cH
  0008b	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FL@OJHBMBAH@F?3?2Release_Branch?2Server?2Develo@
  00092	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  00097	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0DA@LHOBOCCA@mu2?3?3WShopTaskPurchaseJewel?3?3se@
  0009e	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  000a4	ba 02 00 00 00	 mov	 edx, 2
  000a9	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  000b0	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  000b5	66 ba 07 00	 mov	 dx, 7
  000b9	48 8b 4c 24 78	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  000be	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000c4	90		 npad	 1
  000c5	b8 65 cc 05 00	 mov	 eax, 380005		; 0005cc65H
  000ca	e9 2f 02 00 00	 jmp	 $LN1@sendConfir
$LN2@sendConfir:
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  000cf	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A ; mu2::ISingleton<mu2::WShop>::inst
  000d6	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T2[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.h

; 35   : 	fcsa::IJewel*			GetJewel() { return m_jewel; }

  000de	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR $T2[rsp]
  000e6	48 8b 40 30	 mov	 rax, QWORD PTR [rax+48]
  000ea	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR $T3[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 159  : 	fcsa::IRequestConfirmJewelPurchase* fcsNextReq = theWShop.GetJewel()->createRequestConfirmJewelPurchase();

  000f2	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR $T3[rsp]
  000fa	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR tv95[rsp], rax
  00102	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR tv95[rsp]
  0010a	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0010d	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR tv95[rsp]
  00115	ff 90 88 01 00
	00		 call	 QWORD PTR [rax+392]
  0011b	48 89 44 24 60	 mov	 QWORD PTR fcsNextReq$[rsp], rax

; 160  : 	fcsNextReq->callback_attribute(beginRequest->callback_attribute());

  00120	48 8b 44 24 68	 mov	 rax, QWORD PTR beginRequest$[rsp]
  00125	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00128	48 8b 4c 24 68	 mov	 rcx, QWORD PTR beginRequest$[rsp]
  0012d	ff 50 58	 call	 QWORD PTR [rax+88]
  00130	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsNextReq$[rsp]
  00135	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00138	48 89 8c 24 a0
	00 00 00	 mov	 QWORD PTR tv230[rsp], rcx
  00140	48 8b d0	 mov	 rdx, rax
  00143	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsNextReq$[rsp]
  00148	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR tv230[rsp]
  00150	ff 50 50	 call	 QWORD PTR [rax+80]

; 161  : 	fcsNextReq->client_ip(beginRequest->client_ip());

  00153	48 8b 44 24 68	 mov	 rax, QWORD PTR beginRequest$[rsp]
  00158	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0015b	48 8b 4c 24 68	 mov	 rcx, QWORD PTR beginRequest$[rsp]
  00160	ff 90 78 01 00
	00		 call	 QWORD PTR [rax+376]
  00166	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsNextReq$[rsp]
  0016b	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0016e	48 89 8c 24 a8
	00 00 00	 mov	 QWORD PTR tv228[rsp], rcx
  00176	48 8b d0	 mov	 rdx, rax
  00179	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsNextReq$[rsp]
  0017e	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR tv228[rsp]
  00186	ff 90 80 00 00
	00		 call	 QWORD PTR [rax+128]

; 162  : 	fcsNextReq->order_id(beginRequest->order_id());

  0018c	48 8b 44 24 68	 mov	 rax, QWORD PTR beginRequest$[rsp]
  00191	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00194	48 8b 4c 24 68	 mov	 rcx, QWORD PTR beginRequest$[rsp]
  00199	ff 90 f8 00 00
	00		 call	 QWORD PTR [rax+248]
  0019f	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsNextReq$[rsp]
  001a4	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  001a7	48 89 8c 24 b0
	00 00 00	 mov	 QWORD PTR tv226[rsp], rcx
  001af	48 8b d0	 mov	 rdx, rax
  001b2	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsNextReq$[rsp]
  001b7	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR tv226[rsp]
  001bf	ff 50 60	 call	 QWORD PTR [rax+96]

; 163  : 
; 164  : 	m_state = WShopTask::CONFIRM;

  001c2	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  001ca	c7 40 08 04 00
	00 00		 mov	 DWORD PTR [rax+8], 4
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  001d1	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A ; mu2::ISingleton<mu2::WShop>::inst
  001d8	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T4[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.h

; 35   : 	fcsa::IJewel*			GetJewel() { return m_jewel; }

  001e0	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T4[rsp]
  001e8	48 8b 40 30	 mov	 rax, QWORD PTR [rax+48]
  001ec	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T5[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 165  : 	UInt32 result = theWShop.GetJewel()->sendAsync(fcsNextReq);

  001f4	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR $T5[rsp]
  001fc	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR tv164[rsp], rax
  00204	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR tv164[rsp]
  0020c	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0020f	48 8b 54 24 60	 mov	 rdx, QWORD PTR fcsNextReq$[rsp]
  00214	48 8b 8c 24 88
	00 00 00	 mov	 rcx, QWORD PTR tv164[rsp]
  0021c	ff 90 e8 00 00
	00		 call	 QWORD PTR [rax+232]
  00222	0f b6 c0	 movzx	 eax, al
  00225	89 44 24 70	 mov	 DWORD PTR result$[rsp], eax

; 166  : 	if (result == false)

  00229	83 7c 24 70 00	 cmp	 DWORD PTR result$[rsp], 0
  0022e	75 2e		 jne	 SHORT $LN3@sendConfir

; 167  : 	{
; 168  : 		m_state = WShopTask::FINISHED;

  00230	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00238	c7 40 08 08 00
	00 00		 mov	 DWORD PTR [rax+8], 8

; 169  : 		fcsNextReq->release();

  0023f	48 8b 44 24 60	 mov	 rax, QWORD PTR fcsNextReq$[rsp]
  00244	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00247	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsNextReq$[rsp]
  0024c	ff 50 08	 call	 QWORD PTR [rax+8]

; 170  : 		return ErrorWShop::E_REQUEST_FAILED_ON_FCSA;

  0024f	b8 67 cc 05 00	 mov	 eax, 380007		; 0005cc67H
  00254	e9 a5 00 00 00	 jmp	 $LN1@sendConfir

; 171  : 	}

  00259	e9 9e 00 00 00	 jmp	 $LN4@sendConfir
$LN3@sendConfir:

; 172  : 	else
; 173  : 	{
; 174  : 		MU2_TRACE_LOG(LogCategory::WSHOP, "WSHOP(%u),%llu : sendAsync (%d)> ", GetParentFcsAccountId(), GetParentJobNo(), fcsNextReq->packet_type());

  0025e	48 8b 44 24 60	 mov	 rax, QWORD PTR fcsNextReq$[rsp]
  00263	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00266	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsNextReq$[rsp]
  0026b	ff 50 28	 call	 QWORD PTR [rax+40]
  0026e	0f b6 c0	 movzx	 eax, al
  00271	89 44 24 74	 mov	 DWORD PTR tv191[rsp], eax
  00275	48 8b 8c 24 e0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0027d	e8 00 00 00 00	 call	 ?GetParentJobNo@WShopTask@mu2@@QEAA_KXZ ; mu2::WShopTask::GetParentJobNo
  00282	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR tv198[rsp], rax
  0028a	48 8b 8c 24 e0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00292	e8 00 00 00 00	 call	 ?GetParentFcsAccountId@WShopTask@mu2@@QEAAIXZ ; mu2::WShopTask::GetParentFcsAccountId
  00297	8b 4c 24 74	 mov	 ecx, DWORD PTR tv191[rsp]
  0029b	89 4c 24 48	 mov	 DWORD PTR [rsp+72], ecx
  0029f	48 8b 8c 24 c8
	00 00 00	 mov	 rcx, QWORD PTR tv198[rsp]
  002a7	48 89 4c 24 40	 mov	 QWORD PTR [rsp+64], rcx
  002ac	89 44 24 38	 mov	 DWORD PTR [rsp+56], eax
  002b0	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CC@EFIHDMCI@WSHOP?$CI?$CFu?$CJ?0?$CFllu?5?3?5sendAsync?5?$CI?$CFd?$CJ@
  002b7	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  002bc	c7 44 24 28 ae
	00 00 00	 mov	 DWORD PTR [rsp+40], 174	; 000000aeH
  002c4	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FL@OJHBMBAH@F?3?2Release_Branch?2Server?2Develo@
  002cb	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  002d0	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0DA@LHOBOCCA@mu2?3?3WShopTaskPurchaseJewel?3?3se@
  002d7	45 33 c0	 xor	 r8d, r8d
  002da	ba 14 00 00 00	 mov	 edx, 20
  002df	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  002e6	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging

; 175  : 		fcsNextReq->release();

  002eb	48 8b 44 24 60	 mov	 rax, QWORD PTR fcsNextReq$[rsp]
  002f0	48 8b 00	 mov	 rax, QWORD PTR [rax]
  002f3	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsNextReq$[rsp]
  002f8	ff 50 08	 call	 QWORD PTR [rax+8]
  002fb	90		 npad	 1
$LN4@sendConfir:

; 176  : 	}
; 177  : 	return ErrorWShop::SUCCESS;

  002fc	33 c0		 xor	 eax, eax
$LN1@sendConfir:

; 178  : }

  002fe	48 81 c4 d8 00
	00 00		 add	 rsp, 216		; 000000d8H
  00305	c3		 ret	 0
?sendConfirmRequest@WShopTaskPurchaseJewel@mu2@@MEAA?AW4Error@ErrorWShop@2@XZ ENDP ; mu2::WShopTaskPurchaseJewel::sendConfirmRequest
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
;	COMDAT ?sendBeginRequest@WShopTaskPurchaseJewel@mu2@@MEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
_TEXT	SEGMENT
tv142 = 80
tv73 = 88
$T1 = 96
$T2 = 104
tv149 = 112
this$ = 144
resPtr$ = 152
?sendBeginRequest@WShopTaskPurchaseJewel@mu2@@MEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z PROC ; mu2::WShopTaskPurchaseJewel::sendBeginRequest, COMDAT

; 92   : {

$LN9:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H

; 93   : 	UNREFERENCED_PARAMETER(resPtr);
; 94   : 
; 95   : 	m_state = WShopTask::REQUEST;

  00011	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00019	c7 40 08 02 00
	00 00		 mov	 DWORD PTR [rax+8], 2
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  00020	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A ; mu2::ISingleton<mu2::WShop>::inst
  00027	48 89 44 24 60	 mov	 QWORD PTR $T1[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.h

; 35   : 	fcsa::IJewel*			GetJewel() { return m_jewel; }

  0002c	48 8b 44 24 60	 mov	 rax, QWORD PTR $T1[rsp]
  00031	48 8b 40 30	 mov	 rax, QWORD PTR [rax+48]
  00035	48 89 44 24 68	 mov	 QWORD PTR $T2[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 97   : 	if (!theWShop.GetJewel()->sendAsync(static_cast<fcsa::IRequestPurchaseJewelItem*>(m_beginRequest)))

  0003a	48 8b 44 24 68	 mov	 rax, QWORD PTR $T2[rsp]
  0003f	48 89 44 24 58	 mov	 QWORD PTR tv73[rsp], rax
  00044	48 8b 44 24 58	 mov	 rax, QWORD PTR tv73[rsp]
  00049	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0004c	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00054	48 8b 91 b0 02
	00 00		 mov	 rdx, QWORD PTR [rcx+688]
  0005b	48 8b 4c 24 58	 mov	 rcx, QWORD PTR tv73[rsp]
  00060	ff 90 00 01 00
	00		 call	 QWORD PTR [rax+256]
  00066	0f b6 c0	 movzx	 eax, al
  00069	85 c0		 test	 eax, eax
  0006b	75 1b		 jne	 SHORT $LN2@sendBeginR

; 98   : 	{
; 99   : 		m_state = WShopTask::FINISHED;

  0006d	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00075	c7 40 08 08 00
	00 00		 mov	 DWORD PTR [rax+8], 8

; 100  : 		return false;

  0007c	32 c0		 xor	 al, al
  0007e	e9 a3 00 00 00	 jmp	 $LN1@sendBeginR

; 101  : 	}

  00083	e9 9c 00 00 00	 jmp	 $LN3@sendBeginR
$LN2@sendBeginR:

; 102  : 	else
; 103  : 	{
; 104  : 		MU2_TRACE_LOG(LogCategory::WSHOP, "WSHOP(%u),%llu : sendAsync (%d)> ", GetParentFcsAccountId(), GetParentJobNo(), m_beginRequest->packet_type());

  00088	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00090	48 8b 80 b0 02
	00 00		 mov	 rax, QWORD PTR [rax+688]
  00097	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0009f	48 8b 89 b0 02
	00 00		 mov	 rcx, QWORD PTR [rcx+688]
  000a6	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000a9	ff 50 28	 call	 QWORD PTR [rax+40]
  000ac	0f b6 c0	 movzx	 eax, al
  000af	89 44 24 50	 mov	 DWORD PTR tv142[rsp], eax
  000b3	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  000bb	e8 00 00 00 00	 call	 ?GetParentJobNo@WShopTask@mu2@@QEAA_KXZ ; mu2::WShopTask::GetParentJobNo
  000c0	48 89 44 24 70	 mov	 QWORD PTR tv149[rsp], rax
  000c5	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  000cd	e8 00 00 00 00	 call	 ?GetParentFcsAccountId@WShopTask@mu2@@QEAAIXZ ; mu2::WShopTask::GetParentFcsAccountId
  000d2	8b 4c 24 50	 mov	 ecx, DWORD PTR tv142[rsp]
  000d6	89 4c 24 48	 mov	 DWORD PTR [rsp+72], ecx
  000da	48 8b 4c 24 70	 mov	 rcx, QWORD PTR tv149[rsp]
  000df	48 89 4c 24 40	 mov	 QWORD PTR [rsp+64], rcx
  000e4	89 44 24 38	 mov	 DWORD PTR [rsp+56], eax
  000e8	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CC@EFIHDMCI@WSHOP?$CI?$CFu?$CJ?0?$CFllu?5?3?5sendAsync?5?$CI?$CFd?$CJ@
  000ef	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  000f4	c7 44 24 28 68
	00 00 00	 mov	 DWORD PTR [rsp+40], 104	; 00000068H
  000fc	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FL@OJHBMBAH@F?3?2Release_Branch?2Server?2Develo@
  00103	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  00108	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CO@LOJFDACN@mu2?3?3WShopTaskPurchaseJewel?3?3se@
  0010f	45 33 c0	 xor	 r8d, r8d
  00112	ba 14 00 00 00	 mov	 edx, 20
  00117	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  0011e	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  00123	90		 npad	 1
$LN3@sendBeginR:

; 105  : 	}
; 106  : 
; 107  : 	
; 108  : 	return true;

  00124	b0 01		 mov	 al, 1
$LN1@sendBeginR:

; 109  : }

  00126	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  0012d	c3		 ret	 0
?sendBeginRequest@WShopTaskPurchaseJewel@mu2@@MEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ENDP ; mu2::WShopTaskPurchaseJewel::sendBeginRequest
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\string
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldServer.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\string
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
;	COMDAT ?Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z
_TEXT	SEGMENT
fcsReq$ = 96
$T1 = 104
$T2 = 108
wShopJewel$ = 112
owner$ = 120
req$ = 128
hConsole$3 = 136
hConsole$4 = 144
hConsole$5 = 152
$T6 = 160
$T7 = 168
this$ = 176
$T8 = 184
$T9 = 192
$T10 = 200
$T11 = 208
$T12 = 216
$T13 = 224
tv377 = 232
tv465 = 240
$T14 = 248
tv490 = 256
$T15 = 264
$T16 = 272
tv478 = 280
$T17 = 288
$T18 = 296
tv463 = 304
$T19 = 312
tv504 = 320
$T20 = 328
tv500 = 336
$T21 = 344
tv498 = 352
$T22 = 360
tv230 = 368
tv477 = 376
$T23 = 384
orderNo$ = 392
tv380 = 400
tv173 = 408
$T24 = 416
$T25 = 448
callback_string$ = 480
__$ArrayPad$ = 512
this$ = 544
reqPtr$ = 552
resPtr$ = 560
?Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z PROC ; mu2::WShopTaskPurchaseJewel::Setup, COMDAT

; 31   : {

$LN456:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 81 ec 18 02
	00 00		 sub	 rsp, 536		; 00000218H
  00016	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR __security_cookie
  0001d	48 33 c4	 xor	 rax, rsp
  00020	48 89 84 24 00
	02 00 00	 mov	 QWORD PTR __$ArrayPad$[rsp], rax
  00028	c7 44 24 68 00
	00 00 00	 mov	 DWORD PTR $T1[rsp], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1309 :         return _Ptr;

  00030	48 8b 84 24 28
	02 00 00	 mov	 rax, QWORD PTR reqPtr$[rsp]
  00038	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0003b	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T6[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h

; 191  : 		return ptr.get();

  00043	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR $T6[rsp]
  0004b	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T7[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 33   : 	EAppGmCommandWShopPurchaseJewel* req = static_cast<EAppGmCommandWShopPurchaseJewel*>(reqPtr.RawPtr());

  00053	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T7[rsp]
  0005b	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR req$[rsp], rax

; 35   : 	EntityPlayer* owner = m_parentJob->GetOwnerPlayer();

  00063	48 8b 84 24 20
	02 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0006b	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0006f	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1309 :         return _Ptr;

  00077	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0007f	48 8b 80 f0 00
	00 00		 mov	 rax, QWORD PTR [rax+240]
  00086	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T8[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h

; 191  : 		return ptr.get();

  0008e	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T8[rsp]
  00096	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h

; 33   : 	EntityPlayer* GetOwnerPlayer() { return m_playerPtr.RawPtr(); }

  0009e	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR $T9[rsp]
  000a6	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 35   : 	EntityPlayer* owner = m_parentJob->GetOwnerPlayer();

  000ae	48 8b 84 24 c8
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  000b6	48 89 44 24 78	 mov	 QWORD PTR owner$[rsp], rax

; 36   : 	VERIFY_RETURN(owner, ErrorWShop::E_SYSTEM_NULL_POINTER);	

  000bb	48 83 7c 24 78
	00		 cmp	 QWORD PTR owner$[rsp], 0
  000c1	0f 85 ac 00 00
	00		 jne	 $LN2@Setup
  000c7	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  000cc	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  000d2	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR hConsole$3[rsp], rax
  000da	66 ba 0d 00	 mov	 dx, 13
  000de	48 8b 8c 24 88
	00 00 00	 mov	 rcx, QWORD PTR hConsole$3[rsp]
  000e6	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000ec	c7 44 24 50 24
	00 00 00	 mov	 DWORD PTR [rsp+80], 36	; 00000024H
  000f4	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FL@OJHBMBAH@F?3?2Release_Branch?2Server?2Develo@
  000fb	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  00100	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_05HEIBENID@owner@
  00107	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  0010c	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CD@FPCNIDPM@mu2?3?3WShopTaskPurchaseJewel?3?3Se@
  00113	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  00118	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  0011f	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  00124	c7 44 24 28 24
	00 00 00	 mov	 DWORD PTR [rsp+40], 36	; 00000024H
  0012c	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FL@OJHBMBAH@F?3?2Release_Branch?2Server?2Develo@
  00133	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  00138	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CD@FPCNIDPM@mu2?3?3WShopTaskPurchaseJewel?3?3Se@
  0013f	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  00145	ba 02 00 00 00	 mov	 edx, 2
  0014a	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  00151	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  00156	66 ba 07 00	 mov	 dx, 7
  0015a	48 8b 8c 24 88
	00 00 00	 mov	 rcx, QWORD PTR hConsole$3[rsp]
  00162	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  00168	90		 npad	 1
  00169	b8 65 cc 05 00	 mov	 eax, 380005		; 0005cc65H
  0016e	e9 b3 06 00 00	 jmp	 $LN1@Setup
$LN2@Setup:

; 37   : 	VERIFY_RETURN(owner->GetFcsAuthInfo().fcsAccountId, ErrorWShop::E_INVALID_FCS_ACCOUNT_NO);

  00173	48 8b 4c 24 78	 mov	 rcx, QWORD PTR owner$[rsp]
  00178	e8 00 00 00 00	 call	 ?GetFcsAuthInfo@EntityPlayer@mu2@@QEBAAEBUFcsAuthInfo@2@XZ ; mu2::EntityPlayer::GetFcsAuthInfo
  0017d	83 78 08 00	 cmp	 DWORD PTR [rax+8], 0
  00181	0f 85 ac 00 00
	00		 jne	 $LN3@Setup
  00187	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  0018c	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  00192	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR hConsole$4[rsp], rax
  0019a	66 ba 0d 00	 mov	 dx, 13
  0019e	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR hConsole$4[rsp]
  001a6	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  001ac	c7 44 24 50 25
	00 00 00	 mov	 DWORD PTR [rsp+80], 37	; 00000025H
  001b4	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FL@OJHBMBAH@F?3?2Release_Branch?2Server?2Develo@
  001bb	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  001c0	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CF@IGILGBB@owner?9?$DOGetFcsAuthInfo?$CI?$CJ?4fcsAcco@
  001c7	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  001cc	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CD@FPCNIDPM@mu2?3?3WShopTaskPurchaseJewel?3?3Se@
  001d3	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  001d8	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  001df	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  001e4	c7 44 24 28 25
	00 00 00	 mov	 DWORD PTR [rsp+40], 37	; 00000025H
  001ec	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FL@OJHBMBAH@F?3?2Release_Branch?2Server?2Develo@
  001f3	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  001f8	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CD@FPCNIDPM@mu2?3?3WShopTaskPurchaseJewel?3?3Se@
  001ff	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  00205	ba 02 00 00 00	 mov	 edx, 2
  0020a	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  00211	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  00216	66 ba 07 00	 mov	 dx, 7
  0021a	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR hConsole$4[rsp]
  00222	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  00228	90		 npad	 1
  00229	b8 66 cc 05 00	 mov	 eax, 380006		; 0005cc66H
  0022e	e9 f3 05 00 00	 jmp	 $LN1@Setup
$LN3@Setup:
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  00233	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A ; mu2::ISingleton<mu2::WShop>::inst
  0023a	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.h

; 35   : 	fcsa::IJewel*			GetJewel() { return m_jewel; }

  00242	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  0024a	48 8b 40 30	 mov	 rax, QWORD PTR [rax+48]
  0024e	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 39   : 	auto wShopJewel = theWShop.GetJewel();

  00256	48 8b 84 24 d8
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
  0025e	48 89 44 24 70	 mov	 QWORD PTR wShopJewel$[rsp], rax

; 40   : 	VERIFY_RETURN(wShopJewel, ErrorWShop::E_SYSTEM_NULL_POINTER);

  00263	48 83 7c 24 70
	00		 cmp	 QWORD PTR wShopJewel$[rsp], 0
  00269	0f 85 ac 00 00
	00		 jne	 $LN4@Setup
  0026f	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  00274	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  0027a	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR hConsole$5[rsp], rax
  00282	66 ba 0d 00	 mov	 dx, 13
  00286	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR hConsole$5[rsp]
  0028e	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  00294	c7 44 24 50 28
	00 00 00	 mov	 DWORD PTR [rsp+80], 40	; 00000028H
  0029c	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FL@OJHBMBAH@F?3?2Release_Branch?2Server?2Develo@
  002a3	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  002a8	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0L@JNPHBBNK@wShopJewel@
  002af	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  002b4	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CD@FPCNIDPM@mu2?3?3WShopTaskPurchaseJewel?3?3Se@
  002bb	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  002c0	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  002c7	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  002cc	c7 44 24 28 28
	00 00 00	 mov	 DWORD PTR [rsp+40], 40	; 00000028H
  002d4	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FL@OJHBMBAH@F?3?2Release_Branch?2Server?2Develo@
  002db	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  002e0	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CD@FPCNIDPM@mu2?3?3WShopTaskPurchaseJewel?3?3Se@
  002e7	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  002ed	ba 02 00 00 00	 mov	 edx, 2
  002f2	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  002f9	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  002fe	66 ba 07 00	 mov	 dx, 7
  00302	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR hConsole$5[rsp]
  0030a	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  00310	90		 npad	 1
  00311	b8 65 cc 05 00	 mov	 eax, 380005		; 0005cc65H
  00316	e9 0b 05 00 00	 jmp	 $LN1@Setup
$LN4@Setup:

; 41   : 
; 42   : 	fcsa::IRequestPurchaseJewelItem* fcsReq = wShopJewel->createRequestPurchaseJewelItem();

  0031b	48 8b 44 24 70	 mov	 rax, QWORD PTR wShopJewel$[rsp]
  00320	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00323	48 8b 4c 24 70	 mov	 rcx, QWORD PTR wShopJewel$[rsp]
  00328	ff 90 58 01 00
	00		 call	 QWORD PTR [rax+344]
  0032e	48 89 44 24 60	 mov	 QWORD PTR fcsReq$[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h

; 19   : 	UInt64 GetJobNo() { return m_jobNo; }

  00333	48 8b 84 24 20
	02 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0033b	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0033f	48 8b 80 00 01
	00 00		 mov	 rax, QWORD PTR [rax+256]
  00346	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 44   : 	std::wstring callback_string = L"J" + std::to_wstring(m_parentJob->GetJobNo());

  0034e	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR $T13[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\string

; 523  :     return _Integral_to_string<wchar_t>(_Val);

  00356	48 8b d0	 mov	 rdx, rax
  00359	48 8d 8c 24 a0
	01 00 00	 lea	 rcx, QWORD PTR $T24[rsp]
  00361	e8 00 00 00 00	 call	 ??$_Integral_to_string@_W_K@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@0@_K@Z ; std::_Integral_to_string<wchar_t,unsigned __int64>
  00366	8b 44 24 68	 mov	 eax, DWORD PTR $T1[rsp]
  0036a	83 c8 01	 or	 eax, 1
  0036d	89 44 24 68	 mov	 DWORD PTR $T1[rsp], eax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 44   : 	std::wstring callback_string = L"J" + std::to_wstring(m_parentJob->GetJobNo());

  00371	48 8d 84 24 a0
	01 00 00	 lea	 rax, QWORD PTR $T24[rsp]
  00379	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR tv377[rsp], rax
  00381	4c 8b 84 24 e8
	00 00 00	 mov	 r8, QWORD PTR tv377[rsp]
  00389	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:??_C@_13CKBCJILK@?$AAJ@
  00390	48 8d 8c 24 e0
	01 00 00	 lea	 rcx, QWORD PTR callback_string$[rsp]
  00398	e8 00 00 00 00	 call	 ??$?H_WU?$char_traits@_W@std@@V?$allocator@_W@1@@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@0@QEB_W$$QEAV10@@Z ; std::operator+<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  0039d	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1383 :         _Tidy_deallocate();

  0039e	48 8d 8c 24 a0
	01 00 00	 lea	 rcx, QWORD PTR $T24[rsp]
  003a6	e8 00 00 00 00	 call	 ?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate
  003ab	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 45   : 	fcsReq->callback_attribute(callback_string.c_str());

  003ac	48 8d 8c 24 e0
	01 00 00	 lea	 rcx, QWORD PTR callback_string$[rsp]
  003b4	e8 00 00 00 00	 call	 ?c_str@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBAPEB_WXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::c_str
  003b9	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsReq$[rsp]
  003be	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  003c1	48 89 8c 24 f0
	00 00 00	 mov	 QWORD PTR tv465[rsp], rcx
  003c9	48 8b d0	 mov	 rdx, rax
  003cc	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsReq$[rsp]
  003d1	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR tv465[rsp]
  003d9	ff 50 50	 call	 QWORD PTR [rax+80]
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h

; 30   : 	WShopDbUser& GetOwnerInfo() { return m_ownerInfo; }

  003dc	48 8b 84 24 20
	02 00 00	 mov	 rax, QWORD PTR this$[rsp]
  003e4	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  003e8	48 83 c0 38	 add	 rax, 56			; 00000038H
  003ec	48 89 84 24 f8
	00 00 00	 mov	 QWORD PTR $T14[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 46   : 	fcsReq->provider_code(m_parentJob->GetOwnerInfo().providerCode.c_str());

  003f4	48 8b 84 24 f8
	00 00 00	 mov	 rax, QWORD PTR $T14[rsp]
  003fc	48 83 c0 18	 add	 rax, 24
  00400	48 8b c8	 mov	 rcx, rax
  00403	e8 00 00 00 00	 call	 ?c_str@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBAPEB_WXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::c_str
  00408	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsReq$[rsp]
  0040d	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00410	48 89 8c 24 00
	01 00 00	 mov	 QWORD PTR tv490[rsp], rcx
  00418	48 8b d0	 mov	 rdx, rax
  0041b	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsReq$[rsp]
  00420	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR tv490[rsp]
  00428	ff 50 60	 call	 QWORD PTR [rax+96]
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h

; 30   : 	WShopDbUser& GetOwnerInfo() { return m_ownerInfo; }

  0042b	48 8b 84 24 20
	02 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00433	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00437	48 83 c0 38	 add	 rax, 56			; 00000038H
  0043b	48 89 84 24 08
	01 00 00	 mov	 QWORD PTR $T15[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 47   : 	fcsReq->user_no(m_parentJob->GetOwnerInfo().fcsUserId);

  00443	48 8b 44 24 60	 mov	 rax, QWORD PTR fcsReq$[rsp]
  00448	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0044b	48 8b 8c 24 08
	01 00 00	 mov	 rcx, QWORD PTR $T15[rsp]
  00453	8b 51 08	 mov	 edx, DWORD PTR [rcx+8]
  00456	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsReq$[rsp]
  0045b	ff 50 70	 call	 QWORD PTR [rax+112]
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h

; 30   : 	WShopDbUser& GetOwnerInfo() { return m_ownerInfo; }

  0045e	48 8b 84 24 20
	02 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00466	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0046a	48 83 c0 38	 add	 rax, 56			; 00000038H
  0046e	48 89 84 24 10
	01 00 00	 mov	 QWORD PTR $T16[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 48   : 	fcsReq->user_id(m_parentJob->GetOwnerInfo().fcsUserName.c_str());

  00476	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR $T16[rsp]
  0047e	48 83 c0 38	 add	 rax, 56			; 00000038H
  00482	48 8b c8	 mov	 rcx, rax
  00485	e8 00 00 00 00	 call	 ?c_str@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBAPEB_WXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::c_str
  0048a	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsReq$[rsp]
  0048f	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00492	48 89 8c 24 18
	01 00 00	 mov	 QWORD PTR tv478[rsp], rcx
  0049a	48 8b d0	 mov	 rdx, rax
  0049d	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsReq$[rsp]
  004a2	48 8b 84 24 18
	01 00 00	 mov	 rax, QWORD PTR tv478[rsp]
  004aa	ff 90 80 00 00
	00		 call	 QWORD PTR [rax+128]
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h

; 30   : 	WShopDbUser& GetOwnerInfo() { return m_ownerInfo; }

  004b0	48 8b 84 24 20
	02 00 00	 mov	 rax, QWORD PTR this$[rsp]
  004b8	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  004bc	48 83 c0 38	 add	 rax, 56			; 00000038H
  004c0	48 89 84 24 20
	01 00 00	 mov	 QWORD PTR $T17[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 49   : 	fcsReq->account_no(m_parentJob->GetOwnerInfo().fcsAccountId);

  004c8	48 8b 44 24 60	 mov	 rax, QWORD PTR fcsReq$[rsp]
  004cd	48 8b 00	 mov	 rax, QWORD PTR [rax]
  004d0	48 8b 8c 24 20
	01 00 00	 mov	 rcx, QWORD PTR $T17[rsp]
  004d8	8b 51 0c	 mov	 edx, DWORD PTR [rcx+12]
  004db	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsReq$[rsp]
  004e0	ff 90 90 00 00
	00		 call	 QWORD PTR [rax+144]
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h

; 30   : 	WShopDbUser& GetOwnerInfo() { return m_ownerInfo; }

  004e6	48 8b 84 24 20
	02 00 00	 mov	 rax, QWORD PTR this$[rsp]
  004ee	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  004f2	48 83 c0 38	 add	 rax, 56			; 00000038H
  004f6	48 89 84 24 28
	01 00 00	 mov	 QWORD PTR $T18[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 50   : 	fcsReq->account_id(m_parentJob->GetOwnerInfo().fcsAccountName.c_str());

  004fe	48 8b 84 24 28
	01 00 00	 mov	 rax, QWORD PTR $T18[rsp]
  00506	48 83 c0 58	 add	 rax, 88			; 00000058H
  0050a	48 8b c8	 mov	 rcx, rax
  0050d	e8 00 00 00 00	 call	 ?c_str@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBAPEB_WXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::c_str
  00512	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsReq$[rsp]
  00517	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0051a	48 89 8c 24 30
	01 00 00	 mov	 QWORD PTR tv463[rsp], rcx
  00522	48 8b d0	 mov	 rdx, rax
  00525	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsReq$[rsp]
  0052a	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR tv463[rsp]
  00532	ff 90 a0 00 00
	00		 call	 QWORD PTR [rax+160]
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldServer.h

; 106  : 	std::wstring& GetWorldKey() { return m_worldInfo.WorldKey; }

  00538	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR ?Instance@Server@mu2@@2PEAV12@EA ; mu2::Server::Instance
  0053f	48 05 20 05 00
	00		 add	 rax, 1312		; 00000520H
  00545	48 89 84 24 38
	01 00 00	 mov	 QWORD PTR $T19[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 51   : 	fcsReq->world_key(SERVER.GetWorldKey().c_str());

  0054d	48 8b 84 24 38
	01 00 00	 mov	 rax, QWORD PTR $T19[rsp]
  00555	48 8b c8	 mov	 rcx, rax
  00558	e8 00 00 00 00	 call	 ?c_str@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBAPEB_WXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::c_str
  0055d	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsReq$[rsp]
  00562	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00565	48 89 8c 24 40
	01 00 00	 mov	 QWORD PTR tv504[rsp], rcx
  0056d	48 8b d0	 mov	 rdx, rax
  00570	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsReq$[rsp]
  00575	48 8b 84 24 40
	01 00 00	 mov	 rax, QWORD PTR tv504[rsp]
  0057d	ff 90 b0 00 00
	00		 call	 QWORD PTR [rax+176]
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h

; 30   : 	WShopDbUser& GetOwnerInfo() { return m_ownerInfo; }

  00583	48 8b 84 24 20
	02 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0058b	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0058f	48 83 c0 38	 add	 rax, 56			; 00000038H
  00593	48 89 84 24 48
	01 00 00	 mov	 QWORD PTR $T20[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 52   : 	fcsReq->character_no(m_parentJob->GetOwnerInfo().charId);

  0059b	48 8b 84 24 48
	01 00 00	 mov	 rax, QWORD PTR $T20[rsp]
  005a3	8b 40 10	 mov	 eax, DWORD PTR [rax+16]
  005a6	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsReq$[rsp]
  005ab	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  005ae	48 89 8c 24 50
	01 00 00	 mov	 QWORD PTR tv500[rsp], rcx
  005b6	8b d0		 mov	 edx, eax
  005b8	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsReq$[rsp]
  005bd	48 8b 84 24 50
	01 00 00	 mov	 rax, QWORD PTR tv500[rsp]
  005c5	ff 90 c0 00 00
	00		 call	 QWORD PTR [rax+192]
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h

; 30   : 	WShopDbUser& GetOwnerInfo() { return m_ownerInfo; }

  005cb	48 8b 84 24 20
	02 00 00	 mov	 rax, QWORD PTR this$[rsp]
  005d3	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  005d7	48 83 c0 38	 add	 rax, 56			; 00000038H
  005db	48 89 84 24 58
	01 00 00	 mov	 QWORD PTR $T21[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 53   : 	fcsReq->character_id(m_parentJob->GetOwnerInfo().charName.c_str());

  005e3	48 8b 84 24 58
	01 00 00	 mov	 rax, QWORD PTR $T21[rsp]
  005eb	48 83 c0 78	 add	 rax, 120		; 00000078H
  005ef	48 8b c8	 mov	 rcx, rax
  005f2	e8 00 00 00 00	 call	 ?c_str@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBAPEB_WXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::c_str
  005f7	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsReq$[rsp]
  005fc	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  005ff	48 89 8c 24 60
	01 00 00	 mov	 QWORD PTR tv498[rsp], rcx
  00607	48 8b d0	 mov	 rdx, rax
  0060a	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsReq$[rsp]
  0060f	48 8b 84 24 60
	01 00 00	 mov	 rax, QWORD PTR tv498[rsp]
  00617	ff 90 d0 00 00
	00		 call	 QWORD PTR [rax+208]
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h

; 30   : 	WShopDbUser& GetOwnerInfo() { return m_ownerInfo; }

  0061d	48 8b 84 24 20
	02 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00625	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00629	48 83 c0 38	 add	 rax, 56			; 00000038H
  0062d	48 89 84 24 68
	01 00 00	 mov	 QWORD PTR $T22[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 54   : 	fcsReq->client_ip(m_parentJob->GetOwnerInfo().clientIp.c_str());

  00635	48 8b 84 24 68
	01 00 00	 mov	 rax, QWORD PTR $T22[rsp]
  0063d	48 05 98 00 00
	00		 add	 rax, 152		; 00000098H
  00643	48 8b c8	 mov	 rcx, rax
  00646	e8 00 00 00 00	 call	 ?c_str@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBAPEB_WXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::c_str
  0064b	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsReq$[rsp]
  00650	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00653	48 89 8c 24 70
	01 00 00	 mov	 QWORD PTR tv230[rsp], rcx
  0065b	48 8b d0	 mov	 rdx, rax
  0065e	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsReq$[rsp]
  00663	48 8b 84 24 70
	01 00 00	 mov	 rax, QWORD PTR tv230[rsp]
  0066b	ff 90 70 01 00
	00		 call	 QWORD PTR [rax+368]

; 55   : 
; 56   : 	fcsReq->game_level(owner->GetFcsGameLvel());

  00671	48 8b 4c 24 78	 mov	 rcx, QWORD PTR owner$[rsp]
  00676	e8 00 00 00 00	 call	 ?GetFcsGameLvel@EntityPlayer@mu2@@QEBAHXZ ; mu2::EntityPlayer::GetFcsGameLvel
  0067b	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsReq$[rsp]
  00680	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00683	48 89 8c 24 78
	01 00 00	 mov	 QWORD PTR tv477[rsp], rcx
  0068b	8b d0		 mov	 edx, eax
  0068d	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsReq$[rsp]
  00692	48 8b 84 24 78
	01 00 00	 mov	 rax, QWORD PTR tv477[rsp]
  0069a	ff 90 e0 00 00
	00		 call	 QWORD PTR [rax+224]

; 57   : 
; 58   : 	fcsReq->item_code(L"dev-purchaseJewel");

  006a0	48 8b 44 24 60	 mov	 rax, QWORD PTR fcsReq$[rsp]
  006a5	48 8b 00	 mov	 rax, QWORD PTR [rax]
  006a8	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:??_C@_1CE@KMFGEGPK@?$AAd?$AAe?$AAv?$AA?9?$AAp?$AAu?$AAr?$AAc?$AAh?$AAa?$AAs?$AAe?$AAJ?$AAe?$AAw@
  006af	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsReq$[rsp]
  006b4	ff 90 00 01 00
	00		 call	 QWORD PTR [rax+256]

; 59   : 	fcsReq->item_name(L"dev-purchaseJewel");

  006ba	48 8b 44 24 60	 mov	 rax, QWORD PTR fcsReq$[rsp]
  006bf	48 8b 00	 mov	 rax, QWORD PTR [rax]
  006c2	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:??_C@_1CE@KMFGEGPK@?$AAd?$AAe?$AAv?$AA?9?$AAp?$AAu?$AAr?$AAc?$AAh?$AAa?$AAs?$AAe?$AAJ?$AAe?$AAw@
  006c9	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsReq$[rsp]
  006ce	ff 90 10 01 00
	00		 call	 QWORD PTR [rax+272]

; 60   : 	fcsReq->cash_type(req->cashType);

  006d4	48 8b 44 24 60	 mov	 rax, QWORD PTR fcsReq$[rsp]
  006d9	48 8b 00	 mov	 rax, QWORD PTR [rax]
  006dc	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR req$[rsp]
  006e4	8b 51 28	 mov	 edx, DWORD PTR [rcx+40]
  006e7	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsReq$[rsp]
  006ec	ff 90 20 01 00
	00		 call	 QWORD PTR [rax+288]

; 61   : 	fcsReq->purchase_cost(req->cashValue);

  006f2	48 8b 44 24 60	 mov	 rax, QWORD PTR fcsReq$[rsp]
  006f7	48 8b 00	 mov	 rax, QWORD PTR [rax]
  006fa	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR req$[rsp]
  00702	8b 51 2c	 mov	 edx, DWORD PTR [rcx+44]
  00705	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsReq$[rsp]
  0070a	ff 90 30 01 00
	00		 call	 QWORD PTR [rax+304]

; 62   : 	fcsReq->purchase_amount(req->cashValue);

  00710	48 8b 44 24 60	 mov	 rax, QWORD PTR fcsReq$[rsp]
  00715	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00718	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR req$[rsp]
  00720	8b 51 2c	 mov	 edx, DWORD PTR [rcx+44]
  00723	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsReq$[rsp]
  00728	ff 90 50 01 00
	00		 call	 QWORD PTR [rax+336]

; 63   : 	fcsReq->purchase_quantity(1);

  0072e	48 8b 44 24 60	 mov	 rax, QWORD PTR fcsReq$[rsp]
  00733	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00736	ba 01 00 00 00	 mov	 edx, 1
  0073b	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsReq$[rsp]
  00740	ff 90 40 01 00
	00		 call	 QWORD PTR [rax+320]

; 64   : 	fcsReq->description(L"dev-decrease");

  00746	48 8b 44 24 60	 mov	 rax, QWORD PTR fcsReq$[rsp]
  0074b	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0074e	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:??_C@_1BK@JKMOFEE@?$AAd?$AAe?$AAv?$AA?9?$AAd?$AAe?$AAc?$AAr?$AAe?$AAa?$AAs?$AAe@
  00755	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsReq$[rsp]
  0075a	ff 90 60 01 00
	00		 call	 QWORD PTR [rax+352]
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  00760	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A ; mu2::ISingleton<mu2::WShop>::inst
  00767	48 89 84 24 80
	01 00 00	 mov	 QWORD PTR $T23[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 66   : 	UInt64 orderNo = theWShop.MakeOrderNo();

  0076f	48 8b 84 24 80
	01 00 00	 mov	 rax, QWORD PTR $T23[rsp]
  00777	48 8b c8	 mov	 rcx, rax
  0077a	e8 00 00 00 00	 call	 ?MakeOrderNo@WShop@mu2@@QEAA_KXZ ; mu2::WShop::MakeOrderNo
  0077f	48 89 84 24 88
	01 00 00	 mov	 QWORD PTR orderNo$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\string

; 523  :     return _Integral_to_string<wchar_t>(_Val);

  00787	48 8b 94 24 88
	01 00 00	 mov	 rdx, QWORD PTR orderNo$[rsp]
  0078f	48 8d 8c 24 c0
	01 00 00	 lea	 rcx, QWORD PTR $T25[rsp]
  00797	e8 00 00 00 00	 call	 ??$_Integral_to_string@_W_K@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@0@_K@Z ; std::_Integral_to_string<wchar_t,unsigned __int64>
  0079c	8b 44 24 68	 mov	 eax, DWORD PTR $T1[rsp]
  007a0	83 c8 04	 or	 eax, 4
  007a3	89 44 24 68	 mov	 DWORD PTR $T1[rsp], eax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 67   : 	fcsReq->order_id(std::to_wstring(orderNo).c_str());

  007a7	48 8d 84 24 c0
	01 00 00	 lea	 rax, QWORD PTR $T25[rsp]
  007af	48 89 84 24 90
	01 00 00	 mov	 QWORD PTR tv380[rsp], rax
  007b7	48 8b 8c 24 90
	01 00 00	 mov	 rcx, QWORD PTR tv380[rsp]
  007bf	e8 00 00 00 00	 call	 ?c_str@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBAPEB_WXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::c_str
  007c4	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsReq$[rsp]
  007c9	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  007cc	48 89 8c 24 98
	01 00 00	 mov	 QWORD PTR tv173[rsp], rcx
  007d4	48 8b d0	 mov	 rdx, rax
  007d7	48 8b 4c 24 60	 mov	 rcx, QWORD PTR fcsReq$[rsp]
  007dc	48 8b 84 24 98
	01 00 00	 mov	 rax, QWORD PTR tv173[rsp]
  007e4	ff 90 f0 00 00
	00		 call	 QWORD PTR [rax+240]
  007ea	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1383 :         _Tidy_deallocate();

  007eb	48 8d 8c 24 c0
	01 00 00	 lea	 rcx, QWORD PTR $T25[rsp]
  007f3	e8 00 00 00 00	 call	 ?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate
  007f8	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 69   : 	SetBeginRequest(fcsReq);

  007f9	48 8b 54 24 60	 mov	 rdx, QWORD PTR fcsReq$[rsp]
  007fe	48 8b 8c 24 20
	02 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00806	e8 00 00 00 00	 call	 ?SetBeginRequest@WShopTask@mu2@@QEAAXPEAVIRequestParent@fcsa@@@Z ; mu2::WShopTask::SetBeginRequest
  0080b	90		 npad	 1

; 71   : 	return ErrorWShop::SUCCESS;

  0080c	c7 44 24 6c 00
	00 00 00	 mov	 DWORD PTR $T2[rsp], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1383 :         _Tidy_deallocate();

  00814	48 8d 8c 24 e0
	01 00 00	 lea	 rcx, QWORD PTR callback_string$[rsp]
  0081c	e8 00 00 00 00	 call	 ?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate
  00821	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 71   : 	return ErrorWShop::SUCCESS;

  00822	8b 44 24 6c	 mov	 eax, DWORD PTR $T2[rsp]
$LN1@Setup:

; 72   : }

  00826	48 8b 8c 24 00
	02 00 00	 mov	 rcx, QWORD PTR __$ArrayPad$[rsp]
  0082e	48 33 cc	 xor	 rcx, rsp
  00831	e8 00 00 00 00	 call	 __security_check_cookie
  00836	48 81 c4 18 02
	00 00		 add	 rsp, 536		; 00000218H
  0083d	c3		 ret	 0
?Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z ENDP ; mu2::WShopTaskPurchaseJewel::Setup
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
fcsReq$ = 96
$T1 = 104
$T2 = 108
wShopJewel$ = 112
owner$ = 120
req$ = 128
hConsole$3 = 136
hConsole$4 = 144
hConsole$5 = 152
$T6 = 160
$T7 = 168
this$ = 176
$T8 = 184
$T9 = 192
$T10 = 200
$T11 = 208
$T12 = 216
$T13 = 224
tv377 = 232
tv465 = 240
$T14 = 248
tv490 = 256
$T15 = 264
$T16 = 272
tv478 = 280
$T17 = 288
$T18 = 296
tv463 = 304
$T19 = 312
tv504 = 320
$T20 = 328
tv500 = 336
$T21 = 344
tv498 = 352
$T22 = 360
tv230 = 368
tv477 = 376
$T23 = 384
orderNo$ = 392
tv380 = 400
tv173 = 408
$T24 = 416
$T25 = 448
callback_string$ = 480
__$ArrayPad$ = 512
this$ = 544
reqPtr$ = 552
resPtr$ = 560
?dtor$0@?0??Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z@4HA PROC ; `mu2::WShopTaskPurchaseJewel::Setup'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8d 8d a0 01
	00 00		 lea	 rcx, QWORD PTR $T24[rbp]
  00010	e8 00 00 00 00	 call	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$0@?0??Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z@4HA ENDP ; `mu2::WShopTaskPurchaseJewel::Setup'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
fcsReq$ = 96
$T1 = 104
$T2 = 108
wShopJewel$ = 112
owner$ = 120
req$ = 128
hConsole$3 = 136
hConsole$4 = 144
hConsole$5 = 152
$T6 = 160
$T7 = 168
this$ = 176
$T8 = 184
$T9 = 192
$T10 = 200
$T11 = 208
$T12 = 216
$T13 = 224
tv377 = 232
tv465 = 240
$T14 = 248
tv490 = 256
$T15 = 264
$T16 = 272
tv478 = 280
$T17 = 288
$T18 = 296
tv463 = 304
$T19 = 312
tv504 = 320
$T20 = 328
tv500 = 336
$T21 = 344
tv498 = 352
$T22 = 360
tv230 = 368
tv477 = 376
$T23 = 384
orderNo$ = 392
tv380 = 400
tv173 = 408
$T24 = 416
$T25 = 448
callback_string$ = 480
__$ArrayPad$ = 512
this$ = 544
reqPtr$ = 552
resPtr$ = 560
?dtor$1@?0??Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z@4HA PROC ; `mu2::WShopTaskPurchaseJewel::Setup'::`1'::dtor$1
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8d 8d e0 01
	00 00		 lea	 rcx, QWORD PTR callback_string$[rbp]
  00010	e8 00 00 00 00	 call	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$1@?0??Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z@4HA ENDP ; `mu2::WShopTaskPurchaseJewel::Setup'::`1'::dtor$1
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
fcsReq$ = 96
$T1 = 104
$T2 = 108
wShopJewel$ = 112
owner$ = 120
req$ = 128
hConsole$3 = 136
hConsole$4 = 144
hConsole$5 = 152
$T6 = 160
$T7 = 168
this$ = 176
$T8 = 184
$T9 = 192
$T10 = 200
$T11 = 208
$T12 = 216
$T13 = 224
tv377 = 232
tv465 = 240
$T14 = 248
tv490 = 256
$T15 = 264
$T16 = 272
tv478 = 280
$T17 = 288
$T18 = 296
tv463 = 304
$T19 = 312
tv504 = 320
$T20 = 328
tv500 = 336
$T21 = 344
tv498 = 352
$T22 = 360
tv230 = 368
tv477 = 376
$T23 = 384
orderNo$ = 392
tv380 = 400
tv173 = 408
$T24 = 416
$T25 = 448
callback_string$ = 480
__$ArrayPad$ = 512
this$ = 544
reqPtr$ = 552
resPtr$ = 560
?dtor$2@?0??Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z@4HA PROC ; `mu2::WShopTaskPurchaseJewel::Setup'::`1'::dtor$2
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8d 8d c0 01
	00 00		 lea	 rcx, QWORD PTR $T25[rbp]
  00010	e8 00 00 00 00	 call	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$2@?0??Setup@WShopTaskPurchaseJewel@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z@4HA ENDP ; `mu2::WShopTaskPurchaseJewel::Setup'::`1'::dtor$2
text$x	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
;	COMDAT ?SetResultAndReturn@WShopTaskPurchaseJewel@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@W4342@@Z
_TEXT	SEGMENT
$T1 = 0
$T2 = 8
res$ = 16
this$ = 48
e$ = 56
error$ = 64
?SetResultAndReturn@WShopTaskPurchaseJewel@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@W4342@@Z PROC ; mu2::WShopTaskPurchaseJewel::SetResultAndReturn, COMDAT

; 24   : {

$LN10:
  00000	44 89 44 24 18	 mov	 DWORD PTR [rsp+24], r8d
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 28	 sub	 rsp, 40			; 00000028H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1309 :         return _Ptr;

  00013	48 8b 44 24 38	 mov	 rax, QWORD PTR e$[rsp]
  00018	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0001b	48 89 04 24	 mov	 QWORD PTR $T1[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h

; 191  : 		return ptr.get();

  0001f	48 8b 04 24	 mov	 rax, QWORD PTR $T1[rsp]
  00023	48 89 44 24 08	 mov	 QWORD PTR $T2[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp

; 25   : 	auto res = static_cast<EResWShopPurchase*>(e.RawPtr());

  00028	48 8b 44 24 08	 mov	 rax, QWORD PTR $T2[rsp]
  0002d	48 89 44 24 10	 mov	 QWORD PTR res$[rsp], rax

; 26   : 	res->result = error;

  00032	48 8b 44 24 10	 mov	 rax, QWORD PTR res$[rsp]
  00037	8b 4c 24 40	 mov	 ecx, DWORD PTR error$[rsp]
  0003b	89 48 28	 mov	 DWORD PTR [rax+40], ecx

; 27   : 	return error;

  0003e	8b 44 24 40	 mov	 eax, DWORD PTR error$[rsp]

; 28   : }

  00042	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00046	c3		 ret	 0
?SetResultAndReturn@WShopTaskPurchaseJewel@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@W4342@@Z ENDP ; mu2::WShopTaskPurchaseJewel::SetResultAndReturn
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
;	COMDAT ?Response@WShopTaskPurchaseJewel@mu2@@UEAA?AW4Error@ErrorWShop@2@PEAVIResponseParent@fcsa@@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
_TEXT	SEGMENT
tv69 = 32
this$ = 64
fcsRes$ = 72
resPtr$ = 80
?Response@WShopTaskPurchaseJewel@mu2@@UEAA?AW4Error@ErrorWShop@2@PEAVIResponseParent@fcsa@@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z PROC ; mu2::WShopTaskPurchaseJewel::Response, COMDAT

; 75   : {

$LN8:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 76   : 	UNREFERENCED_PARAMETER(resPtr);
; 77   : 
; 78   : 	switch (m_state)

  00013	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00018	8b 40 08	 mov	 eax, DWORD PTR [rax+8]
  0001b	89 44 24 20	 mov	 DWORD PTR tv69[rsp], eax
  0001f	83 7c 24 20 02	 cmp	 DWORD PTR tv69[rsp], 2
  00024	74 09		 je	 SHORT $LN4@Response
  00026	83 7c 24 20 04	 cmp	 DWORD PTR tv69[rsp], 4
  0002b	74 13		 je	 SHORT $LN5@Response
  0002d	eb 22		 jmp	 SHORT $LN2@Response
$LN4@Response:

; 79   : 	{
; 80   : 	case WShopTask::REQUEST:
; 81   : 		return recv(static_cast<fcsa::IResponsePurchaseJewelItem*>(fcsRes));

  0002f	48 8b 54 24 48	 mov	 rdx, QWORD PTR fcsRes$[rsp]
  00034	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00039	e8 00 00 00 00	 call	 ?recv@WShopTaskPurchaseJewel@mu2@@AEAA?AW4Error@ErrorWShop@2@PEAVIResponsePurchaseJewelItem@fcsa@@@Z ; mu2::WShopTaskPurchaseJewel::recv
  0003e	eb 43		 jmp	 SHORT $LN1@Response
$LN5@Response:

; 82   : 
; 83   : 	case WShopTask::CONFIRM:
; 84   : 		return recv(static_cast<fcsa::IResponseConfirmJewelPurchase*>(fcsRes));

  00040	48 8b 54 24 48	 mov	 rdx, QWORD PTR fcsRes$[rsp]
  00045	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0004a	e8 00 00 00 00	 call	 ?recv@WShopTaskPurchaseJewel@mu2@@AEAA?AW4Error@ErrorWShop@2@PEAVIResponseConfirmJewelPurchase@fcsa@@@Z ; mu2::WShopTaskPurchaseJewel::recv
  0004f	eb 32		 jmp	 SHORT $LN1@Response
$LN2@Response:

; 85   : 	}
; 86   : 
; 87   : 	MU2_ASSERT(!"WShopTaskPurchaseJewel::Response> Unhandled state");

  00051	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0DC@PGFKHDPB@WShopTaskPurchaseJewel?3?3Respons@
  00058	48 85 c0	 test	 rax, rax
  0005b	74 21		 je	 SHORT $LN6@Response
  0005d	41 b9 57 00 00
	00		 mov	 r9d, 87			; 00000057H
  00063	4c 8d 05 00 00
	00 00		 lea	 r8, OFFSET FLAT:??_C@_0FL@OJHBMBAH@F?3?2Release_Branch?2Server?2Develo@
  0006a	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:??_C@_0DF@HOBIDHFN@?$CB?$CCWShopTaskPurchaseJewel?3?3Respo@
  00071	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_09OLBBGJEO@Assertion@
  00078	e8 00 00 00 00	 call	 ?LogRuntimeAssertionFailure@mu2@@YAXPEBD00_K@Z ; mu2::LogRuntimeAssertionFailure
  0007d	90		 npad	 1
$LN6@Response:

; 88   : 	return ErrorWShop::E_UNHANDLED_EVENT;

  0007e	b8 6e cc 05 00	 mov	 eax, 380014		; 0005cc6eH
$LN1@Response:

; 89   : }

  00083	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00087	c3		 ret	 0
?Response@WShopTaskPurchaseJewel@mu2@@UEAA?AW4Error@ErrorWShop@2@PEAVIResponseParent@fcsa@@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ENDP ; mu2::WShopTaskPurchaseJewel::Response
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
;	COMDAT ??1WShopTaskPurchaseJewel@mu2@@UEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1WShopTaskPurchaseJewel@mu2@@UEAA@XZ PROC		; mu2::WShopTaskPurchaseJewel::~WShopTaskPurchaseJewel, COMDAT

; 19   : {

$LN4:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7WShopTaskPurchaseJewel@mu2@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx

; 20   : 	SetBeginRequest(nullptr);

  00018	33 d2		 xor	 edx, edx
  0001a	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0001f	e8 00 00 00 00	 call	 ?SetBeginRequest@WShopTask@mu2@@QEAAXPEAVIRequestParent@fcsa@@@Z ; mu2::WShopTask::SetBeginRequest
  00024	90		 npad	 1

; 21   : }

  00025	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0002a	e8 00 00 00 00	 call	 ??1WShopTask@mu2@@UEAA@XZ ; mu2::WShopTask::~WShopTask
  0002f	90		 npad	 1
  00030	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00034	c3		 ret	 0
??1WShopTaskPurchaseJewel@mu2@@UEAA@XZ ENDP		; mu2::WShopTaskPurchaseJewel::~WShopTaskPurchaseJewel
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
;	COMDAT ??0WShopTaskPurchaseJewel@mu2@@QEAA@PEAVWShopJob@1@@Z
_TEXT	SEGMENT
this$ = 48
parent$ = 56
??0WShopTaskPurchaseJewel@mu2@@QEAA@PEAVWShopJob@1@@Z PROC ; mu2::WShopTaskPurchaseJewel::WShopTaskPurchaseJewel, COMDAT

; 14   : {

$LN4:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 13   : 	: WShopTask(parent)

  0000e	48 8b 54 24 38	 mov	 rdx, QWORD PTR parent$[rsp]
  00013	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00018	e8 00 00 00 00	 call	 ??0WShopTask@mu2@@QEAA@PEAVWShopJob@1@@Z ; mu2::WShopTask::WShopTask

; 14   : {

  0001d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00022	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7WShopTaskPurchaseJewel@mu2@@6B@
  00029	48 89 08	 mov	 QWORD PTR [rax], rcx

; 15   : }

  0002c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00031	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00035	c3		 ret	 0
??0WShopTaskPurchaseJewel@mu2@@QEAA@PEAVWShopJob@1@@Z ENDP ; mu2::WShopTaskPurchaseJewel::WShopTaskPurchaseJewel
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??$_Reallocate_grow_by@V<lambda_967c2ed818824c5314a20ec3af46b793>@@_KPEB_W_K@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_967c2ed818824c5314a20ec3af46b793>@@_KPEB_W2@Z
_TEXT	SEGMENT
_My_data$ = 32
_New_capacity$ = 40
_Raw_new$ = 48
_Old_size$ = 56
_Bytes$ = 64
_New_ptr$ = 72
_Fancy_ptr$1 = 80
_New_size$ = 88
_Old_ptr$ = 96
_Old_capacity$ = 104
_Old_ptr$2 = 112
_Ptr$ = 120
_Old_ptr$ = 128
$T3 = 136
$T4 = 144
$T5 = 152
_Al$ = 160
$T6 = 168
$T7 = 176
_Ptr$ = 184
$T8 = 192
$T9 = 200
_First1$ = 208
_First1$ = 216
_First1$ = 224
_First1$ = 232
$T10 = 240
$T11 = 248
$T12 = 256
$T13 = 264
this$ = 288
_Size_increase$ = 296
_Fn$ = 304
<_Args_0>$ = 312
<_Args_1>$ = 320
<_Args_2>$ = 328
??$_Reallocate_grow_by@V<lambda_967c2ed818824c5314a20ec3af46b793>@@_KPEB_W_K@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_967c2ed818824c5314a20ec3af46b793>@@_KPEB_W2@Z PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Reallocate_grow_by<<lambda_967c2ed818824c5314a20ec3af46b793>,unsigned __int64,wchar_t const *,unsigned __int64>, COMDAT

; 3024 :     _CONSTEXPR20 basic_string& _Reallocate_grow_by(const size_type _Size_increase, _Fty _Fn, _ArgTys... _Args) {

$LN205:
  00000	4c 89 4c 24 20	 mov	 QWORD PTR [rsp+32], r9
  00005	44 88 44 24 18	 mov	 BYTE PTR [rsp+24], r8b
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	48 81 ec 18 01
	00 00		 sub	 rsp, 280		; 00000118H

; 3025 :         // reallocate to increase size by _Size_increase elements, new buffer prepared by
; 3026 :         // _Fn(_New_ptr, _Old_ptr, _Old_size, _Args...)
; 3027 :         auto& _My_data            = _Mypair._Myval2;

  0001b	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 89 44 24 20	 mov	 QWORD PTR _My_data$[rsp], rax

; 3028 :         const size_type _Old_size = _My_data._Mysize;

  00028	48 8b 44 24 20	 mov	 rax, QWORD PTR _My_data$[rsp]
  0002d	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00031	48 89 44 24 38	 mov	 QWORD PTR _Old_size$[rsp], rax

; 3029 :         if (max_size() - _Old_size < _Size_increase) {

  00036	48 8b 8c 24 20
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0003e	e8 00 00 00 00	 call	 ?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
  00043	48 2b 44 24 38	 sub	 rax, QWORD PTR _Old_size$[rsp]
  00048	48 3b 84 24 28
	01 00 00	 cmp	 rax, QWORD PTR _Size_increase$[rsp]
  00050	73 06		 jae	 SHORT $LN2@Reallocate

; 3030 :             _Xlen_string(); // result too long

  00052	e8 00 00 00 00	 call	 ?_Xlen_string@std@@YAXXZ ; std::_Xlen_string
  00057	90		 npad	 1
$LN2@Reallocate:

; 3031 :         }
; 3032 : 
; 3033 :         const size_type _New_size     = _Old_size + _Size_increase;

  00058	48 8b 84 24 28
	01 00 00	 mov	 rax, QWORD PTR _Size_increase$[rsp]
  00060	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Old_size$[rsp]
  00065	48 03 c8	 add	 rcx, rax
  00068	48 8b c1	 mov	 rax, rcx
  0006b	48 89 44 24 58	 mov	 QWORD PTR _New_size$[rsp], rax

; 3034 :         const size_type _Old_capacity = _My_data._Myres;

  00070	48 8b 44 24 20	 mov	 rax, QWORD PTR _My_data$[rsp]
  00075	48 8b 40 18	 mov	 rax, QWORD PTR [rax+24]
  00079	48 89 44 24 68	 mov	 QWORD PTR _Old_capacity$[rsp], rax

; 2991 :         return _Calculate_growth(_Requested, _Mypair._Myval2._Myres, max_size());

  0007e	48 8b 8c 24 20
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00086	e8 00 00 00 00	 call	 ?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
  0008b	4c 8b c0	 mov	 r8, rax
  0008e	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00096	48 8b 50 18	 mov	 rdx, QWORD PTR [rax+24]
  0009a	48 8b 4c 24 58	 mov	 rcx, QWORD PTR _New_size$[rsp]
  0009f	e8 00 00 00 00	 call	 ?_Calculate_growth@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@CA_K_K00@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Calculate_growth
  000a4	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T3[rsp], rax

; 3035 :         size_type _New_capacity       = _Calculate_growth(_New_size);

  000ac	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T3[rsp]
  000b4	48 89 44 24 28	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 3107 :         return _Mypair._Get_first();

  000b9	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  000c1	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3107 :         return _Mypair._Get_first();

  000c9	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR $T4[rsp]
  000d1	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR $T5[rsp], rax

; 3036 :         auto& _Al                     = _Getal();

  000d9	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR $T5[rsp]
  000e1	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR _Al$[rsp], rax

; 825  :         ++_Capacity; // Take null terminator into consideration

  000e9	48 8b 44 24 28	 mov	 rax, QWORD PTR _New_capacity$[rsp]
  000ee	48 ff c0	 inc	 rax
  000f1	48 89 44 24 28	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 826  : 
; 827  :         pointer _Fancy_ptr = nullptr;

  000f6	48 c7 44 24 50
	00 00 00 00	 mov	 QWORD PTR _Fancy_ptr$1[rsp], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 2303 :         return _Al.allocate(_Count);

  000ff	48 8b 54 24 28	 mov	 rdx, QWORD PTR _New_capacity$[rsp]
  00104	48 8b 8c 24 a0
	00 00 00	 mov	 rcx, QWORD PTR _Al$[rsp]
  0010c	e8 00 00 00 00	 call	 ?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z ; std::allocator<wchar_t>::allocate
  00111	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 829  :             _Fancy_ptr = _Allocate_at_least_helper(_Al, _Capacity);

  00119	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T6[rsp]
  00121	48 89 44 24 50	 mov	 QWORD PTR _Fancy_ptr$1[rsp], rax

; 830  :         } else {
; 831  :             _STL_INTERNAL_STATIC_ASSERT(_Policy == _Allocation_policy::_Exactly);
; 832  :             _Fancy_ptr = _Al.allocate(_Capacity);
; 833  :         }
; 834  : 
; 835  : #if _HAS_CXX20
; 836  :         // Start element lifetimes to avoid UB. This is a more general mechanism than _String_val::_Activate_SSO_buffer,
; 837  :         // but likely more impactful to throughput.
; 838  :         if (_STD is_constant_evaluated()) {
; 839  :             _Elem* const _Ptr = _Unfancy(_Fancy_ptr);
; 840  :             for (size_type _Idx = 0; _Idx < _Capacity; ++_Idx) {
; 841  :                 _STD construct_at(_Ptr + _Idx);
; 842  :             }
; 843  :         }
; 844  : #endif // _HAS_CXX20
; 845  :         --_Capacity;

  00126	48 8b 44 24 28	 mov	 rax, QWORD PTR _New_capacity$[rsp]
  0012b	48 ff c8	 dec	 rax
  0012e	48 89 44 24 28	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 846  :         return _Fancy_ptr;

  00133	48 8b 44 24 50	 mov	 rax, QWORD PTR _Fancy_ptr$1[rsp]
  00138	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T7[rsp], rax

; 3037 :         const pointer _New_ptr        = _Allocate_for_capacity(_Al, _New_capacity); // throws

  00140	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR $T7[rsp]
  00148	48 89 44 24 48	 mov	 QWORD PTR _New_ptr$[rsp], rax

; 3038 : 
; 3039 :         _My_data._Orphan_all();
; 3040 :         _ASAN_STRING_REMOVE(*this);
; 3041 :         _My_data._Mysize      = _New_size;

  0014d	48 8b 44 24 20	 mov	 rax, QWORD PTR _My_data$[rsp]
  00152	48 8b 4c 24 58	 mov	 rcx, QWORD PTR _New_size$[rsp]
  00157	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 3042 :         _My_data._Myres       = _New_capacity;

  0015b	48 8b 44 24 20	 mov	 rax, QWORD PTR _My_data$[rsp]
  00160	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _New_capacity$[rsp]
  00165	48 89 48 18	 mov	 QWORD PTR [rax+24], rcx

; 3043 :         _Elem* const _Raw_new = _Unfancy(_New_ptr);

  00169	48 8b 44 24 48	 mov	 rax, QWORD PTR _New_ptr$[rsp]
  0016e	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  00176	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR _Ptr$[rsp]
  0017e	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3043 :         _Elem* const _Raw_new = _Unfancy(_New_ptr);

  00186	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR $T8[rsp]
  0018e	48 89 44 24 30	 mov	 QWORD PTR _Raw_new$[rsp], rax

; 3044 :         if (_Old_capacity > _Small_string_capacity) {

  00193	48 83 7c 24 68
	07		 cmp	 QWORD PTR _Old_capacity$[rsp], 7
  00199	0f 86 3a 01 00
	00		 jbe	 $LN3@Reallocate

; 3045 :             const pointer _Old_ptr = _My_data._Bx._Ptr;

  0019f	48 8b 44 24 20	 mov	 rax, QWORD PTR _My_data$[rsp]
  001a4	48 8b 00	 mov	 rax, QWORD PTR [rax]
  001a7	48 89 44 24 70	 mov	 QWORD PTR _Old_ptr$2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  001ac	48 8b 44 24 70	 mov	 rax, QWORD PTR _Old_ptr$2[rsp]
  001b1	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3046 :             _Fn(_Raw_new, _Unfancy(_Old_ptr), _Old_size, _Args...);

  001b9	48 8b 84 24 c8
	00 00 00	 mov	 rax, QWORD PTR $T9[rsp]
  001c1	48 89 44 24 60	 mov	 QWORD PTR _Old_ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  001c6	48 8b 84 24 38
	01 00 00	 mov	 rax, QWORD PTR <_Args_0>$[rsp]
  001ce	48 03 c0	 add	 rax, rax
  001d1	4c 8b c0	 mov	 r8, rax
  001d4	48 8b 54 24 60	 mov	 rdx, QWORD PTR _Old_ptr$[rsp]
  001d9	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Raw_new$[rsp]
  001de	e8 00 00 00 00	 call	 memcpy
  001e3	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1772 :                 _Traits::copy(_New_ptr + _Off, _Ptr, _Count);

  001e4	48 8b 44 24 30	 mov	 rax, QWORD PTR _Raw_new$[rsp]
  001e9	48 8b 8c 24 38
	01 00 00	 mov	 rcx, QWORD PTR <_Args_0>$[rsp]
  001f1	48 8d 04 48	 lea	 rax, QWORD PTR [rax+rcx*2]
  001f5	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR _First1$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  001fd	48 8b 84 24 48
	01 00 00	 mov	 rax, QWORD PTR <_Args_2>$[rsp]
  00205	48 03 c0	 add	 rax, rax
  00208	4c 8b c0	 mov	 r8, rax
  0020b	48 8b 94 24 40
	01 00 00	 mov	 rdx, QWORD PTR <_Args_1>$[rsp]
  00213	48 8b 8c 24 d0
	00 00 00	 mov	 rcx, QWORD PTR _First1$[rsp]
  0021b	e8 00 00 00 00	 call	 memcpy
  00220	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1773 :                 _Traits::copy(_New_ptr + _Off + _Count, _Old_ptr + _Off, _Old_size - _Off + 1);

  00221	48 8b 44 24 30	 mov	 rax, QWORD PTR _Raw_new$[rsp]
  00226	48 8b 8c 24 38
	01 00 00	 mov	 rcx, QWORD PTR <_Args_0>$[rsp]
  0022e	48 8d 04 48	 lea	 rax, QWORD PTR [rax+rcx*2]
  00232	48 8b 8c 24 48
	01 00 00	 mov	 rcx, QWORD PTR <_Args_2>$[rsp]
  0023a	48 8d 04 48	 lea	 rax, QWORD PTR [rax+rcx*2]
  0023e	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR _First1$[rsp], rax
  00246	48 8b 84 24 38
	01 00 00	 mov	 rax, QWORD PTR <_Args_0>$[rsp]
  0024e	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Old_size$[rsp]
  00253	48 2b c8	 sub	 rcx, rax
  00256	48 8b c1	 mov	 rax, rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  00259	48 8d 44 00 02	 lea	 rax, QWORD PTR [rax+rax+2]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1773 :                 _Traits::copy(_New_ptr + _Off + _Count, _Old_ptr + _Off, _Old_size - _Off + 1);

  0025e	48 8b 4c 24 60	 mov	 rcx, QWORD PTR _Old_ptr$[rsp]
  00263	48 8b 94 24 38
	01 00 00	 mov	 rdx, QWORD PTR <_Args_0>$[rsp]
  0026b	48 8d 0c 51	 lea	 rcx, QWORD PTR [rcx+rdx*2]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  0026f	4c 8b c0	 mov	 r8, rax
  00272	48 8b d1	 mov	 rdx, rcx
  00275	48 8b 8c 24 d8
	00 00 00	 mov	 rcx, QWORD PTR _First1$[rsp]
  0027d	e8 00 00 00 00	 call	 memcpy
  00282	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  00283	48 8b 44 24 68	 mov	 rax, QWORD PTR _Old_capacity$[rsp]
  00288	48 8d 44 00 02	 lea	 rax, QWORD PTR [rax+rax+2]
  0028d	48 89 44 24 40	 mov	 QWORD PTR _Bytes$[rsp], rax
  00292	48 8b 44 24 70	 mov	 rax, QWORD PTR _Old_ptr$2[rsp]
  00297	48 89 44 24 78	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  0029c	48 81 7c 24 40
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  002a5	72 10		 jb	 SHORT $LN158@Reallocate

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  002a7	48 8d 54 24 40	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  002ac	48 8d 4c 24 78	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  002b1	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  002b6	90		 npad	 1
$LN158@Reallocate:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  002b7	48 8b 54 24 40	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  002bc	48 8b 4c 24 78	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  002c1	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  002c6	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3048 :             _My_data._Bx._Ptr = _New_ptr;

  002c7	48 8b 44 24 20	 mov	 rax, QWORD PTR _My_data$[rsp]
  002cc	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _New_ptr$[rsp]
  002d1	48 89 08	 mov	 QWORD PTR [rax], rcx

; 3049 :         } else {

  002d4	e9 20 01 00 00	 jmp	 $LN4@Reallocate
$LN3@Reallocate:

; 3050 :             _Fn(_Raw_new, _My_data._Bx._Buf, _Old_size, _Args...);

  002d9	48 8b 44 24 20	 mov	 rax, QWORD PTR _My_data$[rsp]
  002de	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _Old_ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  002e6	48 8b 84 24 38
	01 00 00	 mov	 rax, QWORD PTR <_Args_0>$[rsp]
  002ee	48 03 c0	 add	 rax, rax
  002f1	4c 8b c0	 mov	 r8, rax
  002f4	48 8b 94 24 80
	00 00 00	 mov	 rdx, QWORD PTR _Old_ptr$[rsp]
  002fc	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Raw_new$[rsp]
  00301	e8 00 00 00 00	 call	 memcpy
  00306	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1772 :                 _Traits::copy(_New_ptr + _Off, _Ptr, _Count);

  00307	48 8b 44 24 30	 mov	 rax, QWORD PTR _Raw_new$[rsp]
  0030c	48 8b 8c 24 38
	01 00 00	 mov	 rcx, QWORD PTR <_Args_0>$[rsp]
  00314	48 8d 04 48	 lea	 rax, QWORD PTR [rax+rcx*2]
  00318	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR _First1$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  00320	48 8b 84 24 48
	01 00 00	 mov	 rax, QWORD PTR <_Args_2>$[rsp]
  00328	48 03 c0	 add	 rax, rax
  0032b	4c 8b c0	 mov	 r8, rax
  0032e	48 8b 94 24 40
	01 00 00	 mov	 rdx, QWORD PTR <_Args_1>$[rsp]
  00336	48 8b 8c 24 e0
	00 00 00	 mov	 rcx, QWORD PTR _First1$[rsp]
  0033e	e8 00 00 00 00	 call	 memcpy
  00343	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1773 :                 _Traits::copy(_New_ptr + _Off + _Count, _Old_ptr + _Off, _Old_size - _Off + 1);

  00344	48 8b 44 24 30	 mov	 rax, QWORD PTR _Raw_new$[rsp]
  00349	48 8b 8c 24 38
	01 00 00	 mov	 rcx, QWORD PTR <_Args_0>$[rsp]
  00351	48 8d 04 48	 lea	 rax, QWORD PTR [rax+rcx*2]
  00355	48 8b 8c 24 48
	01 00 00	 mov	 rcx, QWORD PTR <_Args_2>$[rsp]
  0035d	48 8d 04 48	 lea	 rax, QWORD PTR [rax+rcx*2]
  00361	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR _First1$[rsp], rax
  00369	48 8b 84 24 38
	01 00 00	 mov	 rax, QWORD PTR <_Args_0>$[rsp]
  00371	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Old_size$[rsp]
  00376	48 2b c8	 sub	 rcx, rax
  00379	48 8b c1	 mov	 rax, rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  0037c	48 8d 44 00 02	 lea	 rax, QWORD PTR [rax+rax+2]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1773 :                 _Traits::copy(_New_ptr + _Off + _Count, _Old_ptr + _Off, _Old_size - _Off + 1);

  00381	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR _Old_ptr$[rsp]
  00389	48 8b 94 24 38
	01 00 00	 mov	 rdx, QWORD PTR <_Args_0>$[rsp]
  00391	48 8d 0c 51	 lea	 rcx, QWORD PTR [rcx+rdx*2]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  00395	4c 8b c0	 mov	 r8, rax
  00398	48 8b d1	 mov	 rdx, rcx
  0039b	48 8b 8c 24 e8
	00 00 00	 mov	 rcx, QWORD PTR _First1$[rsp]
  003a3	e8 00 00 00 00	 call	 memcpy
  003a8	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3051 :             _Construct_in_place(_My_data._Bx._Ptr, _New_ptr);

  003a9	48 8b 44 24 20	 mov	 rax, QWORD PTR _My_data$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  003ae	48 89 84 24 f0
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  003b6	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  003be	48 89 84 24 f8
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  003c6	48 8b 84 24 f8
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  003ce	48 89 84 24 00
	01 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  003d6	48 8d 44 24 48	 lea	 rax, QWORD PTR _New_ptr$[rsp]
  003db	48 89 84 24 08
	01 00 00	 mov	 QWORD PTR $T13[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  003e3	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR $T12[rsp]
  003eb	48 8b 8c 24 08
	01 00 00	 mov	 rcx, QWORD PTR $T13[rsp]
  003f3	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  003f6	48 89 08	 mov	 QWORD PTR [rax], rcx
$LN4@Reallocate:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3055 :         return *this;

  003f9	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]

; 3056 :     }

  00401	48 81 c4 18 01
	00 00		 add	 rsp, 280		; 00000118H
  00408	c3		 ret	 0
$LN204@Reallocate:
??$_Reallocate_grow_by@V<lambda_967c2ed818824c5314a20ec3af46b793>@@_KPEB_W_K@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_967c2ed818824c5314a20ec3af46b793>@@_KPEB_W2@Z ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Reallocate_grow_by<<lambda_967c2ed818824c5314a20ec3af46b793>,unsigned __int64,wchar_t const *,unsigned __int64>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
_TEXT	SEGMENT
_Ptr_container$ = 48
_Block_size$ = 56
_Ptr$ = 64
$T1 = 72
_Bytes$ = 96
??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z PROC ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>, COMDAT

; 182  : __declspec(allocator) void* _Allocate_manually_vector_aligned(const size_t _Bytes) {

$LN7:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 183  :     // allocate _Bytes manually aligned to at least _Big_allocation_alignment
; 184  :     const size_t _Block_size = _Non_user_size + _Bytes;

  00009	48 8b 44 24 60	 mov	 rax, QWORD PTR _Bytes$[rsp]
  0000e	48 83 c0 27	 add	 rax, 39			; 00000027H
  00012	48 89 44 24 38	 mov	 QWORD PTR _Block_size$[rsp], rax

; 185  :     if (_Block_size <= _Bytes) {

  00017	48 8b 44 24 60	 mov	 rax, QWORD PTR _Bytes$[rsp]
  0001c	48 39 44 24 38	 cmp	 QWORD PTR _Block_size$[rsp], rax
  00021	77 06		 ja	 SHORT $LN2@Allocate_m

; 186  :         _Throw_bad_array_new_length(); // add overflow

  00023	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00028	90		 npad	 1
$LN2@Allocate_m:

; 136  :         return ::operator new(_Bytes);

  00029	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Block_size$[rsp]
  0002e	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00033	48 89 44 24 48	 mov	 QWORD PTR $T1[rsp], rax

; 187  :     }
; 188  : 
; 189  :     const uintptr_t _Ptr_container = reinterpret_cast<uintptr_t>(_Traits::_Allocate(_Block_size));

  00038	48 8b 44 24 48	 mov	 rax, QWORD PTR $T1[rsp]
  0003d	48 89 44 24 30	 mov	 QWORD PTR _Ptr_container$[rsp], rax

; 190  :     _STL_VERIFY(_Ptr_container != 0, "invalid argument"); // validate even in release since we're doing p[-1]

  00042	48 83 7c 24 30
	00		 cmp	 QWORD PTR _Ptr_container$[rsp], 0
  00048	75 19		 jne	 SHORT $LN3@Allocate_m
  0004a	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR [rsp+32], 0
  00053	45 33 c9	 xor	 r9d, r9d
  00056	45 33 c0	 xor	 r8d, r8d
  00059	33 d2		 xor	 edx, edx
  0005b	33 c9		 xor	 ecx, ecx
  0005d	e8 00 00 00 00	 call	 _invoke_watson
  00062	90		 npad	 1
$LN3@Allocate_m:

; 191  :     void* const _Ptr = reinterpret_cast<void*>((_Ptr_container + _Non_user_size) & ~(_Big_allocation_alignment - 1));

  00063	48 8b 44 24 30	 mov	 rax, QWORD PTR _Ptr_container$[rsp]
  00068	48 83 c0 27	 add	 rax, 39			; 00000027H
  0006c	48 83 e0 e0	 and	 rax, -32		; ffffffffffffffe0H
  00070	48 89 44 24 40	 mov	 QWORD PTR _Ptr$[rsp], rax

; 192  :     static_cast<uintptr_t*>(_Ptr)[-1] = _Ptr_container;

  00075	b8 08 00 00 00	 mov	 eax, 8
  0007a	48 6b c0 ff	 imul	 rax, rax, -1
  0007e	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  00083	48 8b 54 24 30	 mov	 rdx, QWORD PTR _Ptr_container$[rsp]
  00088	48 89 14 01	 mov	 QWORD PTR [rcx+rax], rdx

; 193  : 
; 194  : #ifdef _DEBUG
; 195  :     static_cast<uintptr_t*>(_Ptr)[-2] = _Big_allocation_sentinel;
; 196  : #endif // defined(_DEBUG)
; 197  :     return _Ptr;

  0008c	48 8b 44 24 40	 mov	 rax, QWORD PTR _Ptr$[rsp]
$LN4@Allocate_m:

; 198  : }

  00091	48 83 c4 58	 add	 rsp, 88			; 00000058H
  00095	c3		 ret	 0
??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ENDP ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??$_Construct@$00PEA_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEA_W_K@Z
_TEXT	SEGMENT
$T1 = 32
$S44$ = 33
$T2 = 34
$T3 = 36
_My_data$ = 40
_New_capacity$ = 48
_Max$ = 56
_Masked$4 = 64
$T5 = 72
_New_ptr$ = 80
$T6 = 88
tv170 = 96
_Fancy_ptr$7 = 104
$T8 = 112
$T9 = 120
_First1$ = 128
$T10 = 136
$T11 = 144
_Al$ = 152
$T12 = 160
$T13 = 168
$T14 = 176
$T15 = 184
$T16 = 192
$T17 = 200
_Ptr$ = 208
$T18 = 216
_First1$ = 224
_Ptr$ = 232
$T19 = 240
_Alproxy$ = 248
this$ = 272
_Arg$ = 280
_Count$ = 288
??$_Construct@$00PEA_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEA_W_K@Z PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<1,wchar_t *>, COMDAT

; 871  :     _CONSTEXPR20 void _Construct(const _Char_or_ptr _Arg, _CRT_GUARDOVERFLOW const size_type _Count) {

$LN188:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	57		 push	 rdi
  00010	48 81 ec 00 01
	00 00		 sub	 rsp, 256		; 00000100H

; 872  :         auto& _My_data = _Mypair._Myval2;

  00017	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0001f	48 89 44 24 28	 mov	 QWORD PTR _My_data$[rsp], rax

; 873  :         _STL_INTERNAL_CHECK(!_My_data._Large_mode_engaged());
; 874  : 
; 875  :         if constexpr (_Strat == _Construct_strategy::_From_char) {
; 876  :             _STL_INTERNAL_STATIC_ASSERT(is_same_v<_Char_or_ptr, _Elem>);
; 877  :         } else {
; 878  :             _STL_INTERNAL_STATIC_ASSERT(_Is_elem_cptr<_Char_or_ptr>::value);
; 879  :         }
; 880  : 
; 881  :         if (_Count > max_size()) {

  00024	48 8b 8c 24 10
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0002c	e8 00 00 00 00	 call	 ?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
  00031	48 39 84 24 20
	01 00 00	 cmp	 QWORD PTR _Count$[rsp], rax
  00039	76 06		 jbe	 SHORT $LN2@Construct

; 882  :             _Xlen_string(); // result too long

  0003b	e8 00 00 00 00	 call	 ?_Xlen_string@std@@YAXXZ ; std::_Xlen_string
  00040	90		 npad	 1
$LN2@Construct:

; 3107 :         return _Mypair._Get_first();

  00041	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00049	48 89 44 24 70	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3107 :         return _Mypair._Get_first();

  0004e	48 8b 44 24 70	 mov	 rax, QWORD PTR $T8[rsp]
  00053	48 89 44 24 78	 mov	 QWORD PTR $T9[rsp], rax

; 883  :         }
; 884  : 
; 885  :         auto& _Al       = _Getal();

  00058	48 8b 44 24 78	 mov	 rax, QWORD PTR $T9[rsp]
  0005d	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR _Al$[rsp], rax

; 886  :         auto&& _Alproxy = _GET_PROXY_ALLOCATOR(_Alty, _Al);

  00065	48 8d 44 24 20	 lea	 rax, QWORD PTR $T1[rsp]
  0006a	48 8b f8	 mov	 rdi, rax
  0006d	33 c0		 xor	 eax, eax
  0006f	b9 01 00 00 00	 mov	 ecx, 1
  00074	f3 aa		 rep stosb
  00076	48 8d 44 24 21	 lea	 rax, QWORD PTR $S44$[rsp]
  0007b	48 89 84 24 f8
	00 00 00	 mov	 QWORD PTR _Alproxy$[rsp], rax

; 887  :         _Container_proxy_ptr<_Alty> _Proxy(_Alproxy, _My_data);
; 888  : 
; 889  :         if (_Count <= _Small_string_capacity) {

  00083	48 83 bc 24 20
	01 00 00 07	 cmp	 QWORD PTR _Count$[rsp], 7
  0008c	77 71		 ja	 SHORT $LN3@Construct

; 890  :             _My_data._Mysize = _Count;

  0008e	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00093	48 8b 8c 24 20
	01 00 00	 mov	 rcx, QWORD PTR _Count$[rsp]
  0009b	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 891  :             _My_data._Myres  = _Small_string_capacity;

  0009f	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000a4	48 c7 40 18 07
	00 00 00	 mov	 QWORD PTR [rax+24], 7

; 892  : 
; 893  :             if constexpr (_Strat == _Construct_strategy::_From_char) {
; 894  :                 _Traits::assign(_My_data._Bx._Buf, _Count, _Arg);
; 895  :                 _Traits::assign(_My_data._Bx._Buf[_Count], _Elem());
; 896  :             } else if constexpr (_Strat == _Construct_strategy::_From_ptr) {
; 897  :                 _Traits::copy(_My_data._Bx._Buf, _Arg, _Count);

  000ac	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000b1	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _First1$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  000b9	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  000c1	48 03 c0	 add	 rax, rax
  000c4	4c 8b c0	 mov	 r8, rax
  000c7	48 8b 94 24 18
	01 00 00	 mov	 rdx, QWORD PTR _Arg$[rsp]
  000cf	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR _First1$[rsp]
  000d7	e8 00 00 00 00	 call	 memcpy
  000dc	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 898  :                 _Traits::assign(_My_data._Bx._Buf[_Count], _Elem());

  000dd	33 c0		 xor	 eax, eax
  000df	66 89 44 24 22	 mov	 WORD PTR $T2[rsp], ax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 347  :         _Left = _Right;

  000e4	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000e9	48 8b 8c 24 20
	01 00 00	 mov	 rcx, QWORD PTR _Count$[rsp]
  000f1	0f b7 54 24 22	 movzx	 edx, WORD PTR $T2[rsp]
  000f6	66 89 14 48	 mov	 WORD PTR [rax+rcx*2], dx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 908  :             return;

  000fa	e9 3c 02 00 00	 jmp	 $LN4@Construct
$LN3@Construct:

; 909  :         }
; 910  : 
; 911  :         size_type _New_capacity = _Calculate_growth(_Count, _Small_string_capacity, max_size());

  000ff	48 8b 8c 24 10
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00107	e8 00 00 00 00	 call	 ?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
  0010c	48 89 44 24 38	 mov	 QWORD PTR _Max$[rsp], rax

; 2978 :         const size_type _Masked = _Requested | _Alloc_mask;

  00111	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  00119	48 83 c8 07	 or	 rax, 7
  0011d	48 89 44 24 40	 mov	 QWORD PTR _Masked$4[rsp], rax

; 2979 :         if (_Masked > _Max) { // the mask overflows, settle for max_size()

  00122	48 8b 44 24 38	 mov	 rax, QWORD PTR _Max$[rsp]
  00127	48 39 44 24 40	 cmp	 QWORD PTR _Masked$4[rsp], rax
  0012c	76 0f		 jbe	 SHORT $LN114@Construct

; 2980 :             return _Max;

  0012e	48 8b 44 24 38	 mov	 rax, QWORD PTR _Max$[rsp]
  00133	48 89 44 24 48	 mov	 QWORD PTR $T5[rsp], rax
  00138	e9 93 00 00 00	 jmp	 $LN113@Construct
$LN114@Construct:

; 2981 :         }
; 2982 : 
; 2983 :         if (_Old > _Max - _Old / 2) { // similarly, geometric overflows

  0013d	33 d2		 xor	 edx, edx
  0013f	b8 07 00 00 00	 mov	 eax, 7
  00144	b9 02 00 00 00	 mov	 ecx, 2
  00149	48 f7 f1	 div	 rcx
  0014c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Max$[rsp]
  00151	48 2b c8	 sub	 rcx, rax
  00154	48 8b c1	 mov	 rax, rcx
  00157	48 83 f8 07	 cmp	 rax, 7
  0015b	73 0c		 jae	 SHORT $LN115@Construct

; 2984 :             return _Max;

  0015d	48 8b 44 24 38	 mov	 rax, QWORD PTR _Max$[rsp]
  00162	48 89 44 24 48	 mov	 QWORD PTR $T5[rsp], rax
  00167	eb 67		 jmp	 SHORT $LN113@Construct
$LN115@Construct:

; 2987 :         return (_STD max)(_Masked, _Old + _Old / 2);

  00169	33 d2		 xor	 edx, edx
  0016b	b8 07 00 00 00	 mov	 eax, 7
  00170	b9 02 00 00 00	 mov	 ecx, 2
  00175	48 f7 f1	 div	 rcx
  00178	48 83 c0 07	 add	 rax, 7
  0017c	48 89 44 24 58	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 77   :     return _Left < _Right ? _Right : _Left;

  00181	48 8b 44 24 58	 mov	 rax, QWORD PTR $T6[rsp]
  00186	48 39 44 24 40	 cmp	 QWORD PTR _Masked$4[rsp], rax
  0018b	73 0c		 jae	 SHORT $LN122@Construct
  0018d	48 8d 44 24 58	 lea	 rax, QWORD PTR $T6[rsp]
  00192	48 89 44 24 60	 mov	 QWORD PTR tv170[rsp], rax
  00197	eb 0a		 jmp	 SHORT $LN123@Construct
$LN122@Construct:
  00199	48 8d 44 24 40	 lea	 rax, QWORD PTR _Masked$4[rsp]
  0019e	48 89 44 24 60	 mov	 QWORD PTR tv170[rsp], rax
$LN123@Construct:
  001a3	48 8b 44 24 60	 mov	 rax, QWORD PTR tv170[rsp]
  001a8	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
  001b0	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  001b8	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2987 :         return (_STD max)(_Masked, _Old + _Old / 2);

  001c0	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  001c8	48 8b 00	 mov	 rax, QWORD PTR [rax]
  001cb	48 89 44 24 48	 mov	 QWORD PTR $T5[rsp], rax
$LN113@Construct:

; 909  :         }
; 910  : 
; 911  :         size_type _New_capacity = _Calculate_growth(_Count, _Small_string_capacity, max_size());

  001d0	48 8b 44 24 48	 mov	 rax, QWORD PTR $T5[rsp]
  001d5	48 89 44 24 30	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 825  :         ++_Capacity; // Take null terminator into consideration

  001da	48 8b 44 24 30	 mov	 rax, QWORD PTR _New_capacity$[rsp]
  001df	48 ff c0	 inc	 rax
  001e2	48 89 44 24 30	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 826  : 
; 827  :         pointer _Fancy_ptr = nullptr;

  001e7	48 c7 44 24 68
	00 00 00 00	 mov	 QWORD PTR _Fancy_ptr$7[rsp], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 2303 :         return _Al.allocate(_Count);

  001f0	48 8b 54 24 30	 mov	 rdx, QWORD PTR _New_capacity$[rsp]
  001f5	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR _Al$[rsp]
  001fd	e8 00 00 00 00	 call	 ?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z ; std::allocator<wchar_t>::allocate
  00202	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 829  :             _Fancy_ptr = _Allocate_at_least_helper(_Al, _Capacity);

  0020a	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
  00212	48 89 44 24 68	 mov	 QWORD PTR _Fancy_ptr$7[rsp], rax

; 830  :         } else {
; 831  :             _STL_INTERNAL_STATIC_ASSERT(_Policy == _Allocation_policy::_Exactly);
; 832  :             _Fancy_ptr = _Al.allocate(_Capacity);
; 833  :         }
; 834  : 
; 835  : #if _HAS_CXX20
; 836  :         // Start element lifetimes to avoid UB. This is a more general mechanism than _String_val::_Activate_SSO_buffer,
; 837  :         // but likely more impactful to throughput.
; 838  :         if (_STD is_constant_evaluated()) {
; 839  :             _Elem* const _Ptr = _Unfancy(_Fancy_ptr);
; 840  :             for (size_type _Idx = 0; _Idx < _Capacity; ++_Idx) {
; 841  :                 _STD construct_at(_Ptr + _Idx);
; 842  :             }
; 843  :         }
; 844  : #endif // _HAS_CXX20
; 845  :         --_Capacity;

  00217	48 8b 44 24 30	 mov	 rax, QWORD PTR _New_capacity$[rsp]
  0021c	48 ff c8	 dec	 rax
  0021f	48 89 44 24 30	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 846  :         return _Fancy_ptr;

  00224	48 8b 44 24 68	 mov	 rax, QWORD PTR _Fancy_ptr$7[rsp]
  00229	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax

; 912  :         const pointer _New_ptr  = _Allocate_for_capacity(_Al, _New_capacity); // throws

  00231	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T13[rsp]
  00239	48 89 44 24 50	 mov	 QWORD PTR _New_ptr$[rsp], rax

; 913  :         _Construct_in_place(_My_data._Bx._Ptr, _New_ptr);

  0023e	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00243	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T14[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0024b	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR $T14[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  00253	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T15[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0025b	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T15[rsp]
  00263	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T16[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  0026b	48 8d 44 24 50	 lea	 rax, QWORD PTR _New_ptr$[rsp]
  00270	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T17[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00278	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR $T16[rsp]
  00280	48 8b 8c 24 c8
	00 00 00	 mov	 rcx, QWORD PTR $T17[rsp]
  00288	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0028b	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 915  :         _My_data._Mysize = _Count;

  0028e	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00293	48 8b 8c 24 20
	01 00 00	 mov	 rcx, QWORD PTR _Count$[rsp]
  0029b	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 916  :         _My_data._Myres  = _New_capacity;

  0029f	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  002a4	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _New_capacity$[rsp]
  002a9	48 89 48 18	 mov	 QWORD PTR [rax+24], rcx

; 921  :             _Traits::copy(_Unfancy(_New_ptr), _Arg, _Count);

  002ad	48 8b 44 24 50	 mov	 rax, QWORD PTR _New_ptr$[rsp]
  002b2	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  002ba	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR _Ptr$[rsp]
  002c2	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR $T18[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 921  :             _Traits::copy(_Unfancy(_New_ptr), _Arg, _Count);

  002ca	48 8b 84 24 d8
	00 00 00	 mov	 rax, QWORD PTR $T18[rsp]
  002d2	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR _First1$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  002da	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  002e2	48 03 c0	 add	 rax, rax
  002e5	4c 8b c0	 mov	 r8, rax
  002e8	48 8b 94 24 18
	01 00 00	 mov	 rdx, QWORD PTR _Arg$[rsp]
  002f0	48 8b 8c 24 e0
	00 00 00	 mov	 rcx, QWORD PTR _First1$[rsp]
  002f8	e8 00 00 00 00	 call	 memcpy
  002fd	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 922  :             _Traits::assign(_Unfancy(_New_ptr)[_Count], _Elem());

  002fe	33 c0		 xor	 eax, eax
  00300	66 89 44 24 24	 mov	 WORD PTR $T3[rsp], ax
  00305	48 8b 44 24 50	 mov	 rax, QWORD PTR _New_ptr$[rsp]
  0030a	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  00312	48 8b 84 24 e8
	00 00 00	 mov	 rax, QWORD PTR _Ptr$[rsp]
  0031a	48 89 84 24 f0
	00 00 00	 mov	 QWORD PTR $T19[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 347  :         _Left = _Right;

  00322	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR $T19[rsp]
  0032a	48 8b 8c 24 20
	01 00 00	 mov	 rcx, QWORD PTR _Count$[rsp]
  00332	0f b7 54 24 24	 movzx	 edx, WORD PTR $T3[rsp]
  00337	66 89 14 48	 mov	 WORD PTR [rax+rcx*2], dx
$LN4@Construct:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 929  :     }

  0033b	48 81 c4 00 01
	00 00		 add	 rsp, 256		; 00000100H
  00342	5f		 pop	 rdi
  00343	c3		 ret	 0
??$_Construct@$00PEA_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEA_W_K@Z ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<1,wchar_t *>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??$_UIntegral_to_buff@_W_K@std@@YAPEA_WPEA_W_K@Z
_TEXT	SEGMENT
_UVal_trunc$ = 0
_RNext$ = 32
_UVal$ = 40
??$_UIntegral_to_buff@_W_K@std@@YAPEA_WPEA_W_K@Z PROC	; std::_UIntegral_to_buff<wchar_t,unsigned __int64>, COMDAT

; 2754 : _NODISCARD _Elem* _UIntegral_to_buff(_Elem* _RNext, _UTy _UVal) { // used by both to_string and thread::id output

$LN6:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 18	 sub	 rsp, 24

; 2755 :     // format _UVal into buffer *ending at* _RNext
; 2756 :     static_assert(is_unsigned_v<_UTy>, "_UTy must be unsigned");
; 2757 : 
; 2758 : #ifdef _WIN64
; 2759 :     auto _UVal_trunc = _UVal;

  0000e	48 8b 44 24 28	 mov	 rax, QWORD PTR _UVal$[rsp]
  00013	48 89 04 24	 mov	 QWORD PTR _UVal_trunc$[rsp], rax
$LN4@UIntegral_:

; 2760 : #else // ^^^ defined(_WIN64) / !defined(_WIN64) vvv
; 2761 : 
; 2762 :     constexpr bool _Big_uty = sizeof(_UTy) > 4;
; 2763 :     if constexpr (_Big_uty) { // For 64-bit numbers, work in chunks to avoid 64-bit divisions.
; 2764 :         while (_UVal > 0xFFFFFFFFU) {
; 2765 :             auto _UVal_chunk = static_cast<unsigned long>(_UVal % 1000000000);
; 2766 :             _UVal /= 1000000000;
; 2767 : 
; 2768 :             for (int _Idx = 0; _Idx != 9; ++_Idx) {
; 2769 :                 *--_RNext = static_cast<_Elem>('0' + _UVal_chunk % 10);
; 2770 :                 _UVal_chunk /= 10;
; 2771 :             }
; 2772 :         }
; 2773 :     }
; 2774 : 
; 2775 :     auto _UVal_trunc = static_cast<unsigned long>(_UVal);
; 2776 : #endif // ^^^ !defined(_WIN64) ^^^
; 2777 : 
; 2778 :     do {
; 2779 :         *--_RNext = static_cast<_Elem>('0' + _UVal_trunc % 10);

  00017	48 8b 44 24 20	 mov	 rax, QWORD PTR _RNext$[rsp]
  0001c	48 83 e8 02	 sub	 rax, 2
  00020	48 89 44 24 20	 mov	 QWORD PTR _RNext$[rsp], rax
  00025	33 d2		 xor	 edx, edx
  00027	48 8b 04 24	 mov	 rax, QWORD PTR _UVal_trunc$[rsp]
  0002b	b9 0a 00 00 00	 mov	 ecx, 10
  00030	48 f7 f1	 div	 rcx
  00033	48 8b c2	 mov	 rax, rdx
  00036	48 83 c0 30	 add	 rax, 48			; 00000030H
  0003a	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _RNext$[rsp]
  0003f	66 89 01	 mov	 WORD PTR [rcx], ax

; 2780 :         _UVal_trunc /= 10;

  00042	33 d2		 xor	 edx, edx
  00044	48 8b 04 24	 mov	 rax, QWORD PTR _UVal_trunc$[rsp]
  00048	b9 0a 00 00 00	 mov	 ecx, 10
  0004d	48 f7 f1	 div	 rcx
  00050	48 89 04 24	 mov	 QWORD PTR _UVal_trunc$[rsp], rax

; 2781 :     } while (_UVal_trunc != 0);

  00054	48 83 3c 24 00	 cmp	 QWORD PTR _UVal_trunc$[rsp], 0
  00059	75 bc		 jne	 SHORT $LN4@UIntegral_

; 2782 :     return _RNext;

  0005b	48 8b 44 24 20	 mov	 rax, QWORD PTR _RNext$[rsp]

; 2783 : }

  00060	48 83 c4 18	 add	 rsp, 24
  00064	c3		 ret	 0
??$_UIntegral_to_buff@_W_K@std@@YAPEA_WPEA_W_K@Z ENDP	; std::_UIntegral_to_buff<wchar_t,unsigned __int64>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??$?0PEA_W$0A@@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@PEA_W0AEBV?$allocator@_W@1@@Z
_TEXT	SEGMENT
this$ = 32
_UFirst$ = 40
this$ = 48
_ULast$ = 56
this$ = 64
$T1 = 72
$T2 = 80
$T3 = 88
this$ = 112
_First$ = 120
_Last$ = 128
_Al$ = 136
??$?0PEA_W$0A@@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@PEA_W0AEBV?$allocator@_W@1@@Z PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> ><wchar_t *,0>, COMDAT

; 799  :         : _Mypair(_One_then_variadic_args_t{}, _Al) {

$LN247:
  00000	4c 89 4c 24 20	 mov	 QWORD PTR [rsp+32], r9
  00005	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	57		 push	 rdi
  00015	48 83 ec 60	 sub	 rsp, 96			; 00000060H
  00019	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0001e	48 89 44 24 40	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1536 :         : _Ty1(_STD forward<_Other1>(_Val1)), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  00023	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00028	48 89 44 24 20	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 402  :     _CONSTEXPR20 _String_val() noexcept : _Bx() {}

  0002d	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00032	48 89 44 24 30	 mov	 QWORD PTR this$[rsp], rax

; 493  :         _CONSTEXPR20 _Bxty() noexcept : _Buf() {} // user-provided, for fancy pointers

  00037	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003c	48 8b 7c 24 30	 mov	 rdi, QWORD PTR this$[rsp]
  00041	33 c0		 xor	 eax, eax
  00043	b9 10 00 00 00	 mov	 ecx, 16
  00048	f3 aa		 rep stosb

; 517  :     size_type _Mysize = 0; // current length of string (size)

  0004a	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0004f	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 518  :     size_type _Myres  = 0; // current storage reserved for string (capacity)

  00057	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0005c	48 c7 40 18 00
	00 00 00	 mov	 QWORD PTR [rax+24], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 1382 :         return _It + 0;

  00064	48 8b 44 24 78	 mov	 rax, QWORD PTR _First$[rsp]
  00069	48 89 44 24 48	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 801  :         auto _UFirst = _STD _Get_unwrapped(_First);

  0006e	48 8b 44 24 48	 mov	 rax, QWORD PTR $T1[rsp]
  00073	48 89 44 24 28	 mov	 QWORD PTR _UFirst$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 1382 :         return _It + 0;

  00078	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _Last$[rsp]
  00080	48 89 44 24 50	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 802  :         auto _ULast  = _STD _Get_unwrapped(_Last);

  00085	48 8b 44 24 50	 mov	 rax, QWORD PTR $T2[rsp]
  0008a	48 89 44 24 38	 mov	 QWORD PTR _ULast$[rsp], rax

; 803  :         if (_UFirst == _ULast) {

  0008f	48 8b 44 24 38	 mov	 rax, QWORD PTR _ULast$[rsp]
  00094	48 39 44 24 28	 cmp	 QWORD PTR _UFirst$[rsp], rax
  00099	75 0d		 jne	 SHORT $LN2@allocator

; 804  :             _Construct_empty();

  0009b	48 8b 4c 24 70	 mov	 rcx, QWORD PTR this$[rsp]
  000a0	e8 00 00 00 00	 call	 ?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_empty
  000a5	90		 npad	 1

; 805  :         } else {

  000a6	eb 30		 jmp	 SHORT $LN3@allocator
$LN2@allocator:

; 807  :                 _Construct<_Construct_strategy::_From_ptr>(

  000a8	48 8b 44 24 28	 mov	 rax, QWORD PTR _UFirst$[rsp]
  000ad	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _ULast$[rsp]
  000b2	48 2b c8	 sub	 rcx, rax
  000b5	48 8b c1	 mov	 rax, rcx
  000b8	48 d1 f8	 sar	 rax, 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1131 :     return static_cast<_Size_type>(_Len);

  000bb	48 89 44 24 58	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 807  :                 _Construct<_Construct_strategy::_From_ptr>(

  000c0	48 8b 44 24 58	 mov	 rax, QWORD PTR $T3[rsp]
  000c5	4c 8b c0	 mov	 r8, rax
  000c8	48 8b 54 24 28	 mov	 rdx, QWORD PTR _UFirst$[rsp]
  000cd	48 8b 4c 24 70	 mov	 rcx, QWORD PTR this$[rsp]
  000d2	e8 00 00 00 00	 call	 ??$_Construct@$00PEA_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEA_W_K@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<1,wchar_t *>
  000d7	90		 npad	 1
$LN3@allocator:

; 808  :                     _UFirst, _STD _Convert_size<size_type>(static_cast<size_t>(_ULast - _UFirst)));
; 809  :             } else if constexpr (_Is_cpp17_fwd_iter_v<decltype(_UFirst)>) {
; 810  :                 const auto _Length = static_cast<size_t>(_STD distance(_UFirst, _ULast));
; 811  :                 const auto _Count  = _STD _Convert_size<size_type>(_Length);
; 812  :                 _Construct_from_iter(_STD move(_UFirst), _STD move(_ULast), _Count);
; 813  :             } else {
; 814  :                 _Construct_from_iter(_STD move(_UFirst), _STD move(_ULast));
; 815  :             }
; 816  :         }
; 817  :     }

  000d8	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  000dd	48 83 c4 60	 add	 rsp, 96			; 00000060H
  000e1	5f		 pop	 rdi
  000e2	c3		 ret	 0
??$?0PEA_W$0A@@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@PEA_W0AEBV?$allocator@_W@1@@Z ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> ><wchar_t *,0>
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
this$ = 32
_UFirst$ = 40
this$ = 48
_ULast$ = 56
this$ = 64
$T1 = 72
$T2 = 80
$T3 = 88
this$ = 112
_First$ = 120
_Last$ = 128
_Al$ = 136
?dtor$0@?0???$?0PEA_W$0A@@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@PEA_W0AEBV?$allocator@_W@1@@Z@4HA PROC ; `std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> ><wchar_t *,0>'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 70	 mov	 rcx, QWORD PTR this$[rbp]
  0000d	e8 00 00 00 00	 call	 ??1?$_Compressed_pair@V?$allocator@_W@std@@V?$_String_val@U?$_Simple_types@_W@std@@@2@$00@std@@QEAA@XZ
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$0@?0???$?0PEA_W$0A@@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@PEA_W0AEBV?$allocator@_W@1@@Z@4HA ENDP ; `std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> ><wchar_t *,0>'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTask.h
;	COMDAT ?fillItemResult@WShopTask@mu2@@MEAAXXZ
_TEXT	SEGMENT
this$ = 8
?fillItemResult@WShopTask@mu2@@MEAAXXZ PROC		; mu2::WShopTask::fillItemResult, COMDAT

; 78   : 	virtual void fillItemResult() {}	//	아이템 구매 성공을 

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?fillItemResult@WShopTask@mu2@@MEAAXXZ ENDP		; mu2::WShopTask::fillItemResult
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EntityPlayer.h
; File F:\Release_Branch\Shared\SharedSoul.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EntityPlayer.h
;	COMDAT ?GetFcsGameLvel@EntityPlayer@mu2@@QEBAHXZ
_TEXT	SEGMENT
tv71 = 32
$T1 = 36
this$ = 40
this$ = 64
?GetFcsGameLvel@EntityPlayer@mu2@@QEBAHXZ PROC		; mu2::EntityPlayer::GetFcsGameLvel, COMDAT

; 434  : 	{

$LN7:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 436  : 		return GetCharId() == 0 ? -1 : GetSoulLevelEx().GetLogKey();

  00009	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0000e	e8 00 00 00 00	 call	 ?GetCharId@EntityPlayer@mu2@@QEBAIXZ ; mu2::EntityPlayer::GetCharId
  00013	85 c0		 test	 eax, eax
  00015	75 0a		 jne	 SHORT $LN3@GetFcsGame
  00017	c7 44 24 20 ff
	ff ff ff	 mov	 DWORD PTR tv71[rsp], -1
  0001f	eb 35		 jmp	 SHORT $LN4@GetFcsGame
$LN3@GetFcsGame:
  00021	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00026	e8 00 00 00 00	 call	 ?GetSoulLevelEx@EntityPlayer@mu2@@QEBAAEBUSoulLevelEx@2@XZ ; mu2::EntityPlayer::GetSoulLevelEx
  0002b	48 89 44 24 28	 mov	 QWORD PTR this$[rsp], rax
; File F:\Release_Branch\Shared\SharedSoul.h

; 46   : 		Int32				GetLogKey() const { return m_reincarnatedLevel * contReincarnatedLevelMuliflyNumber + m_rawLevel; }

  00030	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00035	0f b6 40 08	 movzx	 eax, BYTE PTR [rax+8]
  00039	69 c0 10 27 00
	00		 imul	 eax, eax, 10000		; 00002710H
  0003f	48 8b 4c 24 28	 mov	 rcx, QWORD PTR this$[rsp]
  00044	0f bf 49 0a	 movsx	 ecx, WORD PTR [rcx+10]
  00048	03 c1		 add	 eax, ecx
  0004a	89 44 24 24	 mov	 DWORD PTR $T1[rsp], eax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EntityPlayer.h

; 436  : 		return GetCharId() == 0 ? -1 : GetSoulLevelEx().GetLogKey();

  0004e	8b 44 24 24	 mov	 eax, DWORD PTR $T1[rsp]
  00052	89 44 24 20	 mov	 DWORD PTR tv71[rsp], eax
$LN4@GetFcsGame:
  00056	8b 44 24 20	 mov	 eax, DWORD PTR tv71[rsp]

; 437  : #else
; 438  : 		return GetCharId() == 0 ? -1 : GetRawSoulLevel();
; 439  : #endif		
; 440  : 	}

  0005a	48 83 c4 38	 add	 rsp, 56			; 00000038H
  0005e	c3		 ret	 0
?GetFcsGameLvel@EntityPlayer@mu2@@QEBAHXZ ENDP		; mu2::EntityPlayer::GetFcsGameLvel
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??$?H_WU?$char_traits@_W@std@@V?$allocator@_W@1@@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@0@QEB_W$$QEAV10@@Z
_TEXT	SEGMENT
$T1 = 32
$T2 = 40
$T3 = 48
$T4 = 56
$T5 = 64
__$ReturnUdt$ = 96
_Left$ = 104
_Right$ = 112
??$?H_WU?$char_traits@_W@std@@V?$allocator@_W@1@@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@0@QEB_W$$QEAV10@@Z PROC ; std::operator+<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >, COMDAT

; 3233 :     _In_z_ const _Elem* const _Left, basic_string<_Elem, _Traits, _Alloc>&& _Right) {

$LN208:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 58	 sub	 rsp, 88			; 00000058H
  00013	c7 44 24 20 00
	00 00 00	 mov	 DWORD PTR $T1[rsp], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 310  :         return _CSTD wcslen(reinterpret_cast<const wchar_t*>(_First));

  0001b	48 8b 4c 24 68	 mov	 rcx, QWORD PTR _Left$[rsp]
  00020	e8 00 00 00 00	 call	 wcslen
  00025	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1780 :         return insert(_Off, _Ptr, _Convert_size<size_type>(_Traits::length(_Ptr)));

  0002a	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1131 :     return static_cast<_Size_type>(_Len);

  0002f	48 89 44 24 30	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1780 :         return insert(_Off, _Ptr, _Convert_size<size_type>(_Traits::length(_Ptr)));

  00034	48 8b 44 24 30	 mov	 rax, QWORD PTR $T3[rsp]
  00039	4c 8b c8	 mov	 r9, rax
  0003c	4c 8b 44 24 68	 mov	 r8, QWORD PTR _Left$[rsp]
  00041	33 d2		 xor	 edx, edx
  00043	48 8b 4c 24 70	 mov	 rcx, QWORD PTR _Right$[rsp]
  00048	e8 00 00 00 00	 call	 ?insert@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV12@_KQEB_W0@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::insert
  0004d	48 89 44 24 38	 mov	 QWORD PTR $T4[rsp], rax

; 3234 :     return _STD move(_Right.insert(0, _Left));

  00052	48 8b 44 24 38	 mov	 rax, QWORD PTR $T4[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00057	48 89 44 24 40	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3234 :     return _STD move(_Right.insert(0, _Left));

  0005c	48 8b 44 24 40	 mov	 rax, QWORD PTR $T5[rsp]
  00061	48 8b d0	 mov	 rdx, rax
  00064	48 8b 4c 24 60	 mov	 rcx, QWORD PTR __$ReturnUdt$[rsp]
  00069	e8 00 00 00 00	 call	 ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@$$QEAV01@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  0006e	8b 44 24 20	 mov	 eax, DWORD PTR $T1[rsp]
  00072	83 c8 01	 or	 eax, 1
  00075	89 44 24 20	 mov	 DWORD PTR $T1[rsp], eax
  00079	48 8b 44 24 60	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]

; 3235 : }

  0007e	48 83 c4 58	 add	 rsp, 88			; 00000058H
  00082	c3		 ret	 0
??$?H_WU?$char_traits@_W@std@@V?$allocator@_W@1@@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@0@QEB_W$$QEAV10@@Z ENDP ; std::operator+<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\string
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\string
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\string
;	COMDAT ??$_Integral_to_string@_W_K@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@0@_K@Z
_TEXT	SEGMENT
$T1 = 32
$T2 = 36
_RNext$ = 40
_Buff_end$ = 48
$T3 = 56
$T4 = 64
_Buff$ = 72
__$ArrayPad$ = 120
__$ReturnUdt$ = 144
_Val$ = 152
??$_Integral_to_string@_W_K@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@0@_K@Z PROC ; std::_Integral_to_string<wchar_t,unsigned __int64>, COMDAT

; 441  : _NODISCARD basic_string<_Elem> _Integral_to_string(const _Ty _Val) {

$LN261:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H
  00011	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR __security_cookie
  00018	48 33 c4	 xor	 rax, rsp
  0001b	48 89 44 24 78	 mov	 QWORD PTR __$ArrayPad$[rsp], rax
  00020	c7 44 24 24 00
	00 00 00	 mov	 DWORD PTR $T2[rsp], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 2032 :     return _Array + _Size;

  00028	48 8d 44 24 72	 lea	 rax, QWORD PTR _Buff$[rsp+42]
  0002d	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\string

; 445  :     _Elem* const _Buff_end = _STD end(_Buff);

  00032	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
  00037	48 89 44 24 30	 mov	 QWORD PTR _Buff_end$[rsp], rax

; 446  :     _Elem* _RNext          = _Buff_end;

  0003c	48 8b 44 24 30	 mov	 rax, QWORD PTR _Buff_end$[rsp]
  00041	48 89 44 24 28	 mov	 QWORD PTR _RNext$[rsp], rax

; 447  : 
; 448  :     if constexpr (is_signed_v<_Ty>) {
; 449  :         const auto _UVal = static_cast<make_unsigned_t<_Ty>>(_Val);
; 450  :         if (_Val < 0) {
; 451  :             _RNext    = _UIntegral_to_buff(_RNext, 0 - _UVal);
; 452  :             *--_RNext = '-';
; 453  :         } else {
; 454  :             _RNext = _UIntegral_to_buff(_RNext, _UVal);
; 455  :         }
; 456  :     } else {
; 457  :         _RNext = _UIntegral_to_buff(_RNext, _Val);

  00046	48 8b 94 24 98
	00 00 00	 mov	 rdx, QWORD PTR _Val$[rsp]
  0004e	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _RNext$[rsp]
  00053	e8 00 00 00 00	 call	 ??$_UIntegral_to_buff@_W_K@std@@YAPEA_WPEA_W_K@Z ; std::_UIntegral_to_buff<wchar_t,unsigned __int64>
  00058	48 89 44 24 28	 mov	 QWORD PTR _RNext$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 974  :     constexpr allocator() noexcept {}

  0005d	48 8d 44 24 20	 lea	 rax, QWORD PTR $T1[rsp]
  00062	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\string

; 460  :     return basic_string<_Elem>(_RNext, _Buff_end);

  00067	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  0006c	4c 8b c8	 mov	 r9, rax
  0006f	4c 8b 44 24 30	 mov	 r8, QWORD PTR _Buff_end$[rsp]
  00074	48 8b 54 24 28	 mov	 rdx, QWORD PTR _RNext$[rsp]
  00079	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR __$ReturnUdt$[rsp]
  00081	e8 00 00 00 00	 call	 ??$?0PEA_W$0A@@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@PEA_W0AEBV?$allocator@_W@1@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> ><wchar_t *,0>
  00086	8b 44 24 24	 mov	 eax, DWORD PTR $T2[rsp]
  0008a	83 c8 01	 or	 eax, 1
  0008d	89 44 24 24	 mov	 DWORD PTR $T2[rsp], eax
  00091	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]

; 461  : }

  00099	48 8b 4c 24 78	 mov	 rcx, QWORD PTR __$ArrayPad$[rsp]
  0009e	48 33 cc	 xor	 rcx, rsp
  000a1	e8 00 00 00 00	 call	 __security_check_cookie
  000a6	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  000ad	c3		 ret	 0
??$_Integral_to_string@_W_K@std@@YA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@0@_K@Z ENDP ; std::_Integral_to_string<wchar_t,unsigned __int64>
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??1?$_Compressed_pair@V?$allocator@_W@std@@V?$_String_val@U?$_Simple_types@_W@std@@@2@$00@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 8
??1?$_Compressed_pair@V?$allocator@_W@std@@V?$_String_val@U?$_Simple_types@_W@std@@@2@$00@std@@QEAA@XZ PROC ; std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>::~_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>, COMDAT
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
??1?$_Compressed_pair@V?$allocator@_W@std@@V?$_String_val@U?$_Simple_types@_W@std@@@2@$00@std@@QEAA@XZ ENDP ; std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>::~_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
_TEXT	SEGMENT
$T1 = 32
$T2 = 34
_My_data$ = 40
tv94 = 48
_Bytes$ = 56
_Ptr$ = 64
$T3 = 72
$T4 = 80
_Capacity$ = 88
_Old_ptr$ = 96
_Al$5 = 104
this$ = 128
?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate, COMDAT

; 3080 :     _CONSTEXPR20 void _Tidy_deallocate() noexcept { // initialize buffer, deallocating any storage

$LN62:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 78	 sub	 rsp, 120		; 00000078H

; 3081 :         auto& _My_data = _Mypair._Myval2;

  00009	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00011	48 89 44 24 28	 mov	 QWORD PTR _My_data$[rsp], rax

; 453  :         return _Myres > _Small_string_capacity;

  00016	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  0001b	48 83 78 18 07	 cmp	 QWORD PTR [rax+24], 7
  00020	76 0a		 jbe	 SHORT $LN12@Tidy_deall
  00022	c7 44 24 30 01
	00 00 00	 mov	 DWORD PTR tv94[rsp], 1
  0002a	eb 08		 jmp	 SHORT $LN13@Tidy_deall
$LN12@Tidy_deall:
  0002c	c7 44 24 30 00
	00 00 00	 mov	 DWORD PTR tv94[rsp], 0
$LN13@Tidy_deall:
  00034	0f b6 44 24 30	 movzx	 eax, BYTE PTR tv94[rsp]
  00039	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al

; 3082 :         _My_data._Orphan_all();
; 3083 :         if (_My_data._Large_mode_engaged()) {

  0003d	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  00042	0f b6 c0	 movzx	 eax, al
  00045	85 c0		 test	 eax, eax
  00047	0f 84 80 00 00
	00		 je	 $LN2@Tidy_deall

; 3107 :         return _Mypair._Get_first();

  0004d	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00055	48 89 44 24 48	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3107 :         return _Mypair._Get_first();

  0005a	48 8b 44 24 48	 mov	 rax, QWORD PTR $T3[rsp]
  0005f	48 89 44 24 50	 mov	 QWORD PTR $T4[rsp], rax

; 3084 :             _ASAN_STRING_REMOVE(*this);
; 3085 :             auto& _Al = _Getal();

  00064	48 8b 44 24 50	 mov	 rax, QWORD PTR $T4[rsp]
  00069	48 89 44 24 68	 mov	 QWORD PTR _Al$5[rsp], rax

; 3086 :             _Deallocate_for_capacity(_Al, _My_data._Bx._Ptr, _My_data._Myres);

  0006e	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00073	48 8b 40 18	 mov	 rax, QWORD PTR [rax+24]
  00077	48 89 44 24 58	 mov	 QWORD PTR _Capacity$[rsp], rax
  0007c	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00081	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00084	48 89 44 24 60	 mov	 QWORD PTR _Old_ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  00089	48 8b 44 24 58	 mov	 rax, QWORD PTR _Capacity$[rsp]
  0008e	48 8d 44 00 02	 lea	 rax, QWORD PTR [rax+rax+2]
  00093	48 89 44 24 38	 mov	 QWORD PTR _Bytes$[rsp], rax
  00098	48 8b 44 24 60	 mov	 rax, QWORD PTR _Old_ptr$[rsp]
  0009d	48 89 44 24 40	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  000a2	48 81 7c 24 38
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  000ab	72 10		 jb	 SHORT $LN38@Tidy_deall

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  000ad	48 8d 54 24 38	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  000b2	48 8d 4c 24 40	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  000b7	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  000bc	90		 npad	 1
$LN38@Tidy_deall:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  000bd	48 8b 54 24 38	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  000c2	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  000c7	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  000cc	90		 npad	 1
$LN2@Tidy_deall:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3090 :         _My_data._Mysize = 0;

  000cd	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000d2	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 3091 :         _My_data._Myres  = _Small_string_capacity;

  000da	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000df	48 c7 40 18 07
	00 00 00	 mov	 QWORD PTR [rax+24], 7

; 3092 :         // the _Traits::assign is last so the codegen doesn't think the char write can alias this
; 3093 :         _Traits::assign(_My_data._Bx._Buf[0], _Elem());

  000e7	33 c0		 xor	 eax, eax
  000e9	66 89 44 24 22	 mov	 WORD PTR $T2[rsp], ax
  000ee	b8 02 00 00 00	 mov	 eax, 2
  000f3	48 6b c0 00	 imul	 rax, rax, 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 347  :         _Left = _Right;

  000f7	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _My_data$[rsp]
  000fc	0f b7 54 24 22	 movzx	 edx, WORD PTR $T2[rsp]
  00101	66 89 14 01	 mov	 WORD PTR [rcx+rax], dx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3094 :     }

  00105	48 83 c4 78	 add	 rsp, 120		; 00000078H
  00109	c3		 ret	 0
?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Calculate_growth@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@CA_K_K00@Z
_TEXT	SEGMENT
_Masked$ = 0
$T1 = 8
tv75 = 16
$T2 = 24
$T3 = 32
_Requested$ = 64
_Old$ = 72
_Max$ = 80
?_Calculate_growth@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@CA_K_K00@Z PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Calculate_growth, COMDAT

; 2977 :         const size_type _Requested, const size_type _Old, const size_type _Max) noexcept {

$LN13:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 2978 :         const size_type _Masked = _Requested | _Alloc_mask;

  00013	48 8b 44 24 40	 mov	 rax, QWORD PTR _Requested$[rsp]
  00018	48 83 c8 07	 or	 rax, 7
  0001c	48 89 04 24	 mov	 QWORD PTR _Masked$[rsp], rax

; 2979 :         if (_Masked > _Max) { // the mask overflows, settle for max_size()

  00020	48 8b 44 24 50	 mov	 rax, QWORD PTR _Max$[rsp]
  00025	48 39 04 24	 cmp	 QWORD PTR _Masked$[rsp], rax
  00029	76 0a		 jbe	 SHORT $LN2@Calculate_

; 2980 :             return _Max;

  0002b	48 8b 44 24 50	 mov	 rax, QWORD PTR _Max$[rsp]
  00030	e9 83 00 00 00	 jmp	 $LN1@Calculate_
$LN2@Calculate_:

; 2981 :         }
; 2982 : 
; 2983 :         if (_Old > _Max - _Old / 2) { // similarly, geometric overflows

  00035	33 d2		 xor	 edx, edx
  00037	48 8b 44 24 48	 mov	 rax, QWORD PTR _Old$[rsp]
  0003c	b9 02 00 00 00	 mov	 ecx, 2
  00041	48 f7 f1	 div	 rcx
  00044	48 8b 4c 24 50	 mov	 rcx, QWORD PTR _Max$[rsp]
  00049	48 2b c8	 sub	 rcx, rax
  0004c	48 8b c1	 mov	 rax, rcx
  0004f	48 39 44 24 48	 cmp	 QWORD PTR _Old$[rsp], rax
  00054	76 07		 jbe	 SHORT $LN3@Calculate_

; 2984 :             return _Max;

  00056	48 8b 44 24 50	 mov	 rax, QWORD PTR _Max$[rsp]
  0005b	eb 5b		 jmp	 SHORT $LN1@Calculate_
$LN3@Calculate_:

; 2987 :         return (_STD max)(_Masked, _Old + _Old / 2);

  0005d	33 d2		 xor	 edx, edx
  0005f	48 8b 44 24 48	 mov	 rax, QWORD PTR _Old$[rsp]
  00064	b9 02 00 00 00	 mov	 ecx, 2
  00069	48 f7 f1	 div	 rcx
  0006c	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _Old$[rsp]
  00071	48 03 c8	 add	 rcx, rax
  00074	48 8b c1	 mov	 rax, rcx
  00077	48 89 44 24 08	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 77   :     return _Left < _Right ? _Right : _Left;

  0007c	48 8b 44 24 08	 mov	 rax, QWORD PTR $T1[rsp]
  00081	48 39 04 24	 cmp	 QWORD PTR _Masked$[rsp], rax
  00085	73 0c		 jae	 SHORT $LN8@Calculate_
  00087	48 8d 44 24 08	 lea	 rax, QWORD PTR $T1[rsp]
  0008c	48 89 44 24 10	 mov	 QWORD PTR tv75[rsp], rax
  00091	eb 09		 jmp	 SHORT $LN9@Calculate_
$LN8@Calculate_:
  00093	48 8d 04 24	 lea	 rax, QWORD PTR _Masked$[rsp]
  00097	48 89 44 24 10	 mov	 QWORD PTR tv75[rsp], rax
$LN9@Calculate_:
  0009c	48 8b 44 24 10	 mov	 rax, QWORD PTR tv75[rsp]
  000a1	48 89 44 24 18	 mov	 QWORD PTR $T2[rsp], rax
  000a6	48 8b 44 24 18	 mov	 rax, QWORD PTR $T2[rsp]
  000ab	48 89 44 24 20	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2987 :         return (_STD max)(_Masked, _Old + _Old / 2);

  000b0	48 8b 44 24 20	 mov	 rax, QWORD PTR $T3[rsp]
  000b5	48 8b 00	 mov	 rax, QWORD PTR [rax]
$LN1@Calculate_:

; 2988 :     }

  000b8	48 83 c4 38	 add	 rsp, 56			; 00000038H
  000bc	c3		 ret	 0
?_Calculate_growth@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@CA_K_K00@Z ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Calculate_growth
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ
_TEXT	SEGMENT
$T1 = 0
_Alloc_max$ = 8
tv66 = 16
$T2 = 24
$T3 = 32
tv70 = 40
$T4 = 48
$T5 = 56
$T6 = 64
$T7 = 72
_Storage_max$ = 80
$T8 = 88
$T9 = 96
$T10 = 104
$T11 = 112
_Unsigned_max$12 = 120
this$ = 144
?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size, COMDAT

; 2377 :     _NODISCARD _CONSTEXPR20 size_type max_size() const noexcept {

$LN38:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H

; 3111 :         return _Mypair._Get_first();

  0000c	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1543 :         return *this;

  00014	48 89 44 24 30	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3111 :         return _Mypair._Get_first();

  00019	48 8b 44 24 30	 mov	 rax, QWORD PTR $T4[rsp]
  0001e	48 89 44 24 70	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 746  :         return static_cast<size_t>(-1) / sizeof(value_type);

  00023	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  0002d	48 89 44 24 38	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2378 :         const size_type _Alloc_max   = _Alty_traits::max_size(_Getal());

  00032	48 8b 44 24 38	 mov	 rax, QWORD PTR $T5[rsp]
  00037	48 89 44 24 08	 mov	 QWORD PTR _Alloc_max$[rsp], rax

; 2379 :         const size_type _Storage_max = // can always store small string

  0003c	48 c7 04 24 08
	00 00 00	 mov	 QWORD PTR $T1[rsp], 8
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 77   :     return _Left < _Right ? _Right : _Left;

  00044	48 8b 04 24	 mov	 rax, QWORD PTR $T1[rsp]
  00048	48 39 44 24 08	 cmp	 QWORD PTR _Alloc_max$[rsp], rax
  0004d	73 0b		 jae	 SHORT $LN21@max_size
  0004f	48 8d 04 24	 lea	 rax, QWORD PTR $T1[rsp]
  00053	48 89 44 24 10	 mov	 QWORD PTR tv66[rsp], rax
  00058	eb 0a		 jmp	 SHORT $LN22@max_size
$LN21@max_size:
  0005a	48 8d 44 24 08	 lea	 rax, QWORD PTR _Alloc_max$[rsp]
  0005f	48 89 44 24 10	 mov	 QWORD PTR tv66[rsp], rax
$LN22@max_size:
  00064	48 8b 44 24 10	 mov	 rax, QWORD PTR tv66[rsp]
  00069	48 89 44 24 40	 mov	 QWORD PTR $T6[rsp], rax
  0006e	48 8b 44 24 40	 mov	 rax, QWORD PTR $T6[rsp]
  00073	48 89 44 24 48	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2379 :         const size_type _Storage_max = // can always store small string

  00078	48 8b 44 24 48	 mov	 rax, QWORD PTR $T7[rsp]
  0007d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00080	48 89 44 24 50	 mov	 QWORD PTR _Storage_max$[rsp], rax

; 2381 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()),

  00085	48 8b 44 24 50	 mov	 rax, QWORD PTR _Storage_max$[rsp]
  0008a	48 ff c8	 dec	 rax
  0008d	48 89 44 24 18	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 866  :         constexpr auto _Unsigned_max = static_cast<make_unsigned_t<_Ty>>(-1);

  00092	48 c7 44 24 78
	ff ff ff ff	 mov	 QWORD PTR _Unsigned_max$12[rsp], -1

; 867  :         return static_cast<_Ty>(_Unsigned_max >> 1);

  0009b	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  000a5	48 89 44 24 58	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2381 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()),

  000aa	48 8b 44 24 58	 mov	 rax, QWORD PTR $T8[rsp]
  000af	48 89 44 24 20	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 101  :     return _Right < _Left ? _Right : _Left;

  000b4	48 8b 44 24 20	 mov	 rax, QWORD PTR $T3[rsp]
  000b9	48 39 44 24 18	 cmp	 QWORD PTR $T2[rsp], rax
  000be	73 0c		 jae	 SHORT $LN33@max_size
  000c0	48 8d 44 24 18	 lea	 rax, QWORD PTR $T2[rsp]
  000c5	48 89 44 24 28	 mov	 QWORD PTR tv70[rsp], rax
  000ca	eb 0a		 jmp	 SHORT $LN34@max_size
$LN33@max_size:
  000cc	48 8d 44 24 20	 lea	 rax, QWORD PTR $T3[rsp]
  000d1	48 89 44 24 28	 mov	 QWORD PTR tv70[rsp], rax
$LN34@max_size:
  000d6	48 8b 44 24 28	 mov	 rax, QWORD PTR tv70[rsp]
  000db	48 89 44 24 60	 mov	 QWORD PTR $T9[rsp], rax
  000e0	48 8b 44 24 60	 mov	 rax, QWORD PTR $T9[rsp]
  000e5	48 89 44 24 68	 mov	 QWORD PTR $T10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2381 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()),

  000ea	48 8b 44 24 68	 mov	 rax, QWORD PTR $T10[rsp]
  000ef	48 8b 00	 mov	 rax, QWORD PTR [rax]

; 2382 :             _Storage_max - 1 // -1 is for null terminator and/or npos
; 2383 :         );
; 2384 :     }

  000f2	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  000f9	c3		 ret	 0
?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?c_str@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBAPEB_WXZ
_TEXT	SEGMENT
$T1 = 0
tv80 = 4
this$ = 8
_Result$2 = 16
_Ptr$ = 24
$T3 = 32
$T4 = 40
this$ = 64
?c_str@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBAPEB_WXZ PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::c_str, COMDAT

; 2355 :     _NODISCARD _CONSTEXPR20 _Ret_z_ const _Elem* c_str() const noexcept {

$LN22:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 2356 :         return _Mypair._Myval2._Myptr();

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 89 44 24 08	 mov	 QWORD PTR this$[rsp], rax

; 444  :         const value_type* _Result = _Bx._Buf;

  00013	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00018	48 89 44 24 10	 mov	 QWORD PTR _Result$2[rsp], rax

; 453  :         return _Myres > _Small_string_capacity;

  0001d	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00022	48 83 78 18 07	 cmp	 QWORD PTR [rax+24], 7
  00027	76 0a		 jbe	 SHORT $LN12@c_str
  00029	c7 44 24 04 01
	00 00 00	 mov	 DWORD PTR tv80[rsp], 1
  00031	eb 08		 jmp	 SHORT $LN13@c_str
$LN12@c_str:
  00033	c7 44 24 04 00
	00 00 00	 mov	 DWORD PTR tv80[rsp], 0
$LN13@c_str:
  0003b	0f b6 44 24 04	 movzx	 eax, BYTE PTR tv80[rsp]
  00040	88 04 24	 mov	 BYTE PTR $T1[rsp], al

; 445  :         if (_Large_mode_engaged()) {

  00043	0f b6 04 24	 movzx	 eax, BYTE PTR $T1[rsp]
  00047	0f b6 c0	 movzx	 eax, al
  0004a	85 c0		 test	 eax, eax
  0004c	74 21		 je	 SHORT $LN5@c_str

; 446  :             _Result = _Unfancy(_Bx._Ptr);

  0004e	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00056	48 89 44 24 18	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  0005b	48 8b 44 24 18	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00060	48 89 44 24 20	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 446  :             _Result = _Unfancy(_Bx._Ptr);

  00065	48 8b 44 24 20	 mov	 rax, QWORD PTR $T3[rsp]
  0006a	48 89 44 24 10	 mov	 QWORD PTR _Result$2[rsp], rax
$LN5@c_str:

; 447  :         }
; 448  : 
; 449  :         return _Result;

  0006f	48 8b 44 24 10	 mov	 rax, QWORD PTR _Result$2[rsp]
  00074	48 89 44 24 28	 mov	 QWORD PTR $T4[rsp], rax

; 2356 :         return _Mypair._Myval2._Myptr();

  00079	48 8b 44 24 28	 mov	 rax, QWORD PTR $T4[rsp]

; 2357 :     }

  0007e	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00082	c3		 ret	 0
?c_str@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBAPEB_WXZ ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::c_str
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?insert@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV12@_KQEB_W0@Z
_TEXT	SEGMENT
tv76 = 48
_Check_overlap$ = 49
$T1 = 50
_Insert_at$2 = 56
_Ptr_shifted_after$3 = 64
_Old_size$ = 72
_Old_ptr$4 = 80
_First1$ = 88
_First1$ = 96
this$ = 128
_Off$ = 136
_Ptr$ = 144
_Count$ = 152
?insert@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV12@_KQEB_W0@Z PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::insert, COMDAT

; 1730 :         const size_type _Off, _In_reads_(_Count) const _Elem* const _Ptr, _CRT_GUARDOVERFLOW const size_type _Count) {

$LN251:
  00000	4c 89 4c 24 20	 mov	 QWORD PTR [rsp+32], r9
  00005	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	57		 push	 rdi
  00015	48 83 ec 70	 sub	 rsp, 112		; 00000070H

; 469  :         if (_Mysize < _Off) {

  00019	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 8b 8c 24 88
	00 00 00	 mov	 rcx, QWORD PTR _Off$[rsp]
  00029	48 39 48 10	 cmp	 QWORD PTR [rax+16], rcx
  0002d	73 06		 jae	 SHORT $LN12@insert

; 470  :             _Xran();

  0002f	e8 00 00 00 00	 call	 ?_Xran@?$_String_val@U?$_Simple_types@_W@std@@@std@@SAXXZ ; std::_String_val<std::_Simple_types<wchar_t> >::_Xran
  00034	90		 npad	 1
$LN12@insert:

; 1731 :         // insert [_Ptr, _Ptr + _Count) at _Off
; 1732 :         _Mypair._Myval2._Check_offset(_Off);
; 1733 :         const size_type _Old_size = _Mypair._Myval2._Mysize;

  00035	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0003d	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00041	48 89 44 24 48	 mov	 QWORD PTR _Old_size$[rsp], rax

; 1734 : 
; 1735 :         // We can't check for overlapping ranges when constant evaluated since comparison of pointers into string
; 1736 :         // literals is unspecified, so always reallocate and copy to the new buffer if constant evaluated.
; 1737 : #if _HAS_CXX20
; 1738 :         const bool _Check_overlap = _Count <= _Mypair._Myval2._Myres - _Old_size && !_STD is_constant_evaluated();
; 1739 : #else // ^^^ _HAS_CXX20 / !_HAS_CXX20 vvv
; 1740 :         const bool _Check_overlap = _Count <= _Mypair._Myval2._Myres - _Old_size;

  00046	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _Old_size$[rsp]
  00053	48 8b 40 18	 mov	 rax, QWORD PTR [rax+24]
  00057	48 2b c1	 sub	 rax, rcx
  0005a	48 39 84 24 98
	00 00 00	 cmp	 QWORD PTR _Count$[rsp], rax
  00062	77 07		 ja	 SHORT $LN9@insert
  00064	c6 44 24 30 01	 mov	 BYTE PTR tv76[rsp], 1
  00069	eb 05		 jmp	 SHORT $LN10@insert
$LN9@insert:
  0006b	c6 44 24 30 00	 mov	 BYTE PTR tv76[rsp], 0
$LN10@insert:
  00070	0f b6 44 24 30	 movzx	 eax, BYTE PTR tv76[rsp]
  00075	88 44 24 31	 mov	 BYTE PTR _Check_overlap$[rsp], al

; 1741 : #endif // ^^^ !_HAS_CXX20 ^^^
; 1742 : 
; 1743 :         if (_Check_overlap) {

  00079	0f b6 44 24 31	 movzx	 eax, BYTE PTR _Check_overlap$[rsp]
  0007e	85 c0		 test	 eax, eax
  00080	0f 84 81 01 00
	00		 je	 $LN2@insert

; 1744 :             _ASAN_STRING_MODIFY(*this, _Old_size, _Old_size + _Count);
; 1745 :             _Mypair._Myval2._Mysize = _Old_size + _Count;

  00086	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  0008e	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _Old_size$[rsp]
  00093	48 03 c8	 add	 rcx, rax
  00096	48 8b c1	 mov	 rax, rcx
  00099	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  000a1	48 89 41 10	 mov	 QWORD PTR [rcx+16], rax

; 1746 :             _Elem* const _Old_ptr   = _Mypair._Myval2._Myptr();

  000a5	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000ad	48 8b c8	 mov	 rcx, rax
  000b0	e8 00 00 00 00	 call	 ?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEAAPEA_WXZ ; std::_String_val<std::_Simple_types<wchar_t> >::_Myptr
  000b5	48 89 44 24 50	 mov	 QWORD PTR _Old_ptr$4[rsp], rax

; 1747 :             _Elem* const _Insert_at = _Old_ptr + _Off;

  000ba	48 8b 44 24 50	 mov	 rax, QWORD PTR _Old_ptr$4[rsp]
  000bf	48 8b 8c 24 88
	00 00 00	 mov	 rcx, QWORD PTR _Off$[rsp]
  000c7	48 8d 04 48	 lea	 rax, QWORD PTR [rax+rcx*2]
  000cb	48 89 44 24 38	 mov	 QWORD PTR _Insert_at$2[rsp], rax

; 1748 :             // the range [_Ptr, _Ptr + _Ptr_shifted_after) is left alone by moving the suffix out,
; 1749 :             // while the range [_Ptr + _Ptr_shifted_after, _Ptr + _Count) shifts down by _Count
; 1750 :             size_type _Ptr_shifted_after;
; 1751 :             if (_Ptr + _Count <= _Insert_at || _Ptr > _Old_ptr + _Old_size) {

  000d0	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR _Ptr$[rsp]
  000d8	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR _Count$[rsp]
  000e0	48 8d 04 48	 lea	 rax, QWORD PTR [rax+rcx*2]
  000e4	48 3b 44 24 38	 cmp	 rax, QWORD PTR _Insert_at$2[rsp]
  000e9	76 18		 jbe	 SHORT $LN5@insert
  000eb	48 8b 44 24 50	 mov	 rax, QWORD PTR _Old_ptr$4[rsp]
  000f0	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _Old_size$[rsp]
  000f5	48 8d 04 48	 lea	 rax, QWORD PTR [rax+rcx*2]
  000f9	48 39 84 24 90
	00 00 00	 cmp	 QWORD PTR _Ptr$[rsp], rax
  00101	76 0f		 jbe	 SHORT $LN3@insert
$LN5@insert:

; 1752 :                 // inserted content is before the shifted region, or does not alias
; 1753 :                 _Ptr_shifted_after = _Count; // none of _Ptr's data shifts

  00103	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  0010b	48 89 44 24 40	 mov	 QWORD PTR _Ptr_shifted_after$3[rsp], rax
  00110	eb 35		 jmp	 SHORT $LN4@insert
$LN3@insert:

; 1754 :             } else if (_Insert_at <= _Ptr) { // all of [_Ptr, _Ptr + _Count) shifts

  00112	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR _Ptr$[rsp]
  0011a	48 39 44 24 38	 cmp	 QWORD PTR _Insert_at$2[rsp], rax
  0011f	77 0b		 ja	 SHORT $LN6@insert

; 1755 :                 _Ptr_shifted_after = 0;

  00121	48 c7 44 24 40
	00 00 00 00	 mov	 QWORD PTR _Ptr_shifted_after$3[rsp], 0

; 1756 :             } else { // [_Ptr, _Ptr + _Count) contains _Insert_at, so only the part after _Insert_at shifts

  0012a	eb 1b		 jmp	 SHORT $LN4@insert
$LN6@insert:

; 1757 :                 _Ptr_shifted_after = static_cast<size_type>(_Insert_at - _Ptr);

  0012c	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00134	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Insert_at$2[rsp]
  00139	48 2b c8	 sub	 rcx, rax
  0013c	48 8b c1	 mov	 rax, rcx
  0013f	48 d1 f8	 sar	 rax, 1
  00142	48 89 44 24 40	 mov	 QWORD PTR _Ptr_shifted_after$3[rsp], rax
$LN4@insert:

; 1758 :             }
; 1759 : 
; 1760 :             _Traits::move(_Insert_at + _Count, _Insert_at, _Old_size - _Off + 1); // move suffix + null down

  00147	48 8b 44 24 38	 mov	 rax, QWORD PTR _Insert_at$2[rsp]
  0014c	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR _Count$[rsp]
  00154	48 8d 04 48	 lea	 rax, QWORD PTR [rax+rcx*2]
  00158	48 89 44 24 58	 mov	 QWORD PTR _First1$[rsp], rax
  0015d	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR _Off$[rsp]
  00165	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _Old_size$[rsp]
  0016a	48 2b c8	 sub	 rcx, rax
  0016d	48 8b c1	 mov	 rax, rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 174  :         _CSTD memmove(_First1, _First2, _Count * sizeof(_Elem));

  00170	48 8d 44 00 02	 lea	 rax, QWORD PTR [rax+rax+2]
  00175	4c 8b c0	 mov	 r8, rax
  00178	48 8b 54 24 38	 mov	 rdx, QWORD PTR _Insert_at$2[rsp]
  0017d	48 8b 4c 24 58	 mov	 rcx, QWORD PTR _First1$[rsp]
  00182	e8 00 00 00 00	 call	 memmove
  00187	90		 npad	 1

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  00188	48 8b 44 24 40	 mov	 rax, QWORD PTR _Ptr_shifted_after$3[rsp]
  0018d	48 03 c0	 add	 rax, rax
  00190	4c 8b c0	 mov	 r8, rax
  00193	48 8b 94 24 90
	00 00 00	 mov	 rdx, QWORD PTR _Ptr$[rsp]
  0019b	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Insert_at$2[rsp]
  001a0	e8 00 00 00 00	 call	 memcpy
  001a5	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1762 :             _Traits::copy(

  001a6	48 8b 44 24 38	 mov	 rax, QWORD PTR _Insert_at$2[rsp]
  001ab	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr_shifted_after$3[rsp]
  001b0	48 8d 04 48	 lea	 rax, QWORD PTR [rax+rcx*2]
  001b4	48 89 44 24 60	 mov	 QWORD PTR _First1$[rsp], rax
  001b9	48 8b 44 24 40	 mov	 rax, QWORD PTR _Ptr_shifted_after$3[rsp]
  001be	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR _Count$[rsp]
  001c6	48 2b c8	 sub	 rcx, rax
  001c9	48 8b c1	 mov	 rax, rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  001cc	48 d1 e0	 shl	 rax, 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1762 :             _Traits::copy(

  001cf	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  001d7	48 8b 94 24 98
	00 00 00	 mov	 rdx, QWORD PTR _Count$[rsp]
  001df	48 8d 0c 51	 lea	 rcx, QWORD PTR [rcx+rdx*2]
  001e3	48 8b 54 24 40	 mov	 rdx, QWORD PTR _Ptr_shifted_after$3[rsp]
  001e8	48 8d 0c 51	 lea	 rcx, QWORD PTR [rcx+rdx*2]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  001ec	4c 8b c0	 mov	 r8, rax
  001ef	48 8b d1	 mov	 rdx, rcx
  001f2	48 8b 4c 24 60	 mov	 rcx, QWORD PTR _First1$[rsp]
  001f7	e8 00 00 00 00	 call	 memcpy
  001fc	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1764 :             return *this;

  001fd	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00205	eb 4e		 jmp	 SHORT $LN14@insert
$LN2@insert:

; 1765 :         }
; 1766 : 
; 1767 :         return _Reallocate_grow_by(

  00207	48 8d 44 24 32	 lea	 rax, QWORD PTR $T1[rsp]
  0020c	48 8b f8	 mov	 rdi, rax
  0020f	33 c0		 xor	 eax, eax
  00211	b9 01 00 00 00	 mov	 ecx, 1
  00216	f3 aa		 rep stosb
  00218	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  00220	48 89 44 24 28	 mov	 QWORD PTR [rsp+40], rax
  00225	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR _Ptr$[rsp]
  0022d	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  00232	4c 8b 8c 24 88
	00 00 00	 mov	 r9, QWORD PTR _Off$[rsp]
  0023a	44 0f b6 44 24
	32		 movzx	 r8d, BYTE PTR $T1[rsp]
  00240	48 8b 94 24 98
	00 00 00	 mov	 rdx, QWORD PTR _Count$[rsp]
  00248	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00250	e8 00 00 00 00	 call	 ??$_Reallocate_grow_by@V<lambda_967c2ed818824c5314a20ec3af46b793>@@_KPEB_W_K@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_967c2ed818824c5314a20ec3af46b793>@@_KPEB_W2@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Reallocate_grow_by<<lambda_967c2ed818824c5314a20ec3af46b793>,unsigned __int64,wchar_t const *,unsigned __int64>
$LN14@insert:

; 1768 :             _Count,
; 1769 :             [](_Elem* const _New_ptr, const _Elem* const _Old_ptr, const size_type _Old_size, const size_type _Off,
; 1770 :                 const _Elem* const _Ptr, const size_type _Count) _STATIC_LAMBDA {
; 1771 :                 _Traits::copy(_New_ptr, _Old_ptr, _Off);
; 1772 :                 _Traits::copy(_New_ptr + _Off, _Ptr, _Count);
; 1773 :                 _Traits::copy(_New_ptr + _Off + _Count, _Old_ptr + _Off, _Old_size - _Off + 1);
; 1774 :             },
; 1775 :             _Off, _Ptr, _Count);
; 1776 :     }

  00255	48 83 c4 70	 add	 rsp, 112		; 00000070H
  00259	5f		 pop	 rdi
  0025a	c3		 ret	 0
?insert@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV12@_KQEB_W0@Z ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::insert
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >, COMDAT

; 1382 :     _CONSTEXPR20 ~basic_string() noexcept {

$LN82:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 1383 :         _Tidy_deallocate();

  00009	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0000e	e8 00 00 00 00	 call	 ?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate
  00013	90		 npad	 1

; 1384 : #if _ITERATOR_DEBUG_LEVEL != 0
; 1385 :         auto&& _Alproxy          = _GET_PROXY_ALLOCATOR(_Alty, _Getal());
; 1386 :         const auto _To_delete    = _Mypair._Myval2._Myproxy;
; 1387 :         _Mypair._Myval2._Myproxy = nullptr;
; 1388 :         _Delete_plain_internal(_Alproxy, _To_delete);
; 1389 : #endif // _ITERATOR_DEBUG_LEVEL != 0
; 1390 :     }

  00014	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00018	c3		 ret	 0
??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Take_contents@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXAEAV12@@Z
_TEXT	SEGMENT
$T1 = 32
_Right_data$ = 40
$T2 = 48
$T3 = 50
tv81 = 52
_My_data$ = 56
$T4 = 64
$T5 = 72
_Right_data_mem$6 = 80
_My_data_mem$7 = 88
$T8 = 96
$T9 = 104
$T10 = 112
$T11 = 120
_Count$ = 128
_First1$ = 136
this$ = 160
_Right$ = 168
?_Take_contents@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXAEAV12@@Z PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Take_contents, COMDAT

; 1258 :     _CONSTEXPR20 void _Take_contents(basic_string& _Right) noexcept {

$LN93:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 81 ec 98 00
	00 00		 sub	 rsp, 152		; 00000098H

; 1259 :         // assign by stealing _Right's buffer
; 1260 :         // pre: this != &_Right
; 1261 :         // pre: allocator propagation (POCMA) from _Right, if necessary, is complete
; 1262 :         // pre: *this owns no memory, iterators orphaned
; 1263 :         // (note: _Buf/_Ptr/_Mysize/_Myres may be garbage init)
; 1264 :         auto& _My_data    = _Mypair._Myval2;

  00011	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00019	48 89 44 24 38	 mov	 QWORD PTR _My_data$[rsp], rax

; 1265 :         auto& _Right_data = _Right._Mypair._Myval2;

  0001e	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR _Right$[rsp]
  00026	48 89 44 24 28	 mov	 QWORD PTR _Right_data$[rsp], rax

; 1282 :                 const auto _My_data_mem =

  0002b	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00033	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1282 :                 const auto _My_data_mem =

  00038	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  0003d	48 89 44 24 58	 mov	 QWORD PTR _My_data_mem$7[rsp], rax

; 1284 :                 const auto _Right_data_mem =

  00042	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR _Right$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0004a	48 89 44 24 48	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1284 :                 const auto _Right_data_mem =

  0004f	48 8b 44 24 48	 mov	 rax, QWORD PTR $T5[rsp]
  00054	48 89 44 24 50	 mov	 QWORD PTR _Right_data_mem$6[rsp], rax

; 1285 :                     reinterpret_cast<const unsigned char*>(_STD addressof(_Right._Mypair._Myval2)) + _Memcpy_val_offset;
; 1286 :                 _CSTD memcpy(_My_data_mem, _Right_data_mem, _Memcpy_val_size);

  00059	41 b8 20 00 00
	00		 mov	 r8d, 32			; 00000020H
  0005f	48 8b 54 24 50	 mov	 rdx, QWORD PTR _Right_data_mem$6[rsp]
  00064	48 8b 4c 24 58	 mov	 rcx, QWORD PTR _My_data_mem$7[rsp]
  00069	e8 00 00 00 00	 call	 memcpy

; 1287 : 
; 1288 :                 _Right_data._Mysize = 0;

  0006e	48 8b 44 24 28	 mov	 rax, QWORD PTR _Right_data$[rsp]
  00073	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 1289 :                 _Right_data._Myres  = _Small_string_capacity;

  0007b	48 8b 44 24 28	 mov	 rax, QWORD PTR _Right_data$[rsp]
  00080	48 c7 40 18 07
	00 00 00	 mov	 QWORD PTR [rax+24], 7

; 1290 :                 _Right_data._Activate_SSO_buffer();
; 1291 :                 _Traits::assign(_Right_data._Bx._Buf[0], _Elem());

  00088	33 c0		 xor	 eax, eax
  0008a	66 89 44 24 30	 mov	 WORD PTR $T2[rsp], ax
  0008f	b8 02 00 00 00	 mov	 eax, 2
  00094	48 6b c0 00	 imul	 rax, rax, 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 347  :         _Left = _Right;

  00098	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Right_data$[rsp]
  0009d	0f b7 54 24 30	 movzx	 edx, WORD PTR $T2[rsp]
  000a2	66 89 14 01	 mov	 WORD PTR [rcx+rax], dx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1292 :                 return;

  000a6	e9 0e 01 00 00	 jmp	 $LN1@Take_conte

; 453  :         return _Myres > _Small_string_capacity;

  000ab	48 8b 44 24 28	 mov	 rax, QWORD PTR _Right_data$[rsp]
  000b0	48 83 78 18 07	 cmp	 QWORD PTR [rax+24], 7
  000b5	76 0a		 jbe	 SHORT $LN28@Take_conte
  000b7	c7 44 24 34 01
	00 00 00	 mov	 DWORD PTR tv81[rsp], 1
  000bf	eb 08		 jmp	 SHORT $LN29@Take_conte
$LN28@Take_conte:
  000c1	c7 44 24 34 00
	00 00 00	 mov	 DWORD PTR tv81[rsp], 0
$LN29@Take_conte:
  000c9	0f b6 44 24 34	 movzx	 eax, BYTE PTR tv81[rsp]
  000ce	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al

; 1293 :             }
; 1294 :         }
; 1295 : #endif // !defined(_INSERT_STRING_ANNOTATION)
; 1296 : 
; 1297 :         if (_Right_data._Large_mode_engaged()) { // steal buffer

  000d2	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  000d7	0f b6 c0	 movzx	 eax, al
  000da	85 c0		 test	 eax, eax
  000dc	74 3a		 je	 SHORT $LN2@Take_conte

; 1300 :             _Construct_in_place(_My_data._Bx._Ptr, _Right_data._Bx._Ptr);

  000de	48 8b 44 24 38	 mov	 rax, QWORD PTR _My_data$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  000e3	48 89 44 24 60	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  000e8	48 8b 44 24 60	 mov	 rax, QWORD PTR $T8[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  000ed	48 89 44 24 68	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  000f2	48 8b 44 24 68	 mov	 rax, QWORD PTR $T9[rsp]
  000f7	48 89 44 24 70	 mov	 QWORD PTR $T10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1300 :             _Construct_in_place(_My_data._Bx._Ptr, _Right_data._Bx._Ptr);

  000fc	48 8b 44 24 28	 mov	 rax, QWORD PTR _Right_data$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00101	48 89 44 24 78	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00106	48 8b 44 24 70	 mov	 rax, QWORD PTR $T10[rsp]
  0010b	48 8b 4c 24 78	 mov	 rcx, QWORD PTR $T11[rsp]
  00110	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00113	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1302 :         } else { // copy small string buffer

  00116	eb 45		 jmp	 SHORT $LN3@Take_conte
$LN2@Take_conte:

; 1306 :             _Traits::copy(_My_data._Bx._Buf, _Right_data._Bx._Buf, _Right_data._Mysize + 1);

  00118	48 8b 44 24 28	 mov	 rax, QWORD PTR _Right_data$[rsp]
  0011d	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00121	48 ff c0	 inc	 rax
  00124	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _Count$[rsp], rax
  0012c	48 8b 44 24 38	 mov	 rax, QWORD PTR _My_data$[rsp]
  00131	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR _First1$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  00139	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  00141	48 d1 e0	 shl	 rax, 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1306 :             _Traits::copy(_My_data._Bx._Buf, _Right_data._Bx._Buf, _Right_data._Mysize + 1);

  00144	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Right_data$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  00149	4c 8b c0	 mov	 r8, rax
  0014c	48 8b d1	 mov	 rdx, rcx
  0014f	48 8b 8c 24 88
	00 00 00	 mov	 rcx, QWORD PTR _First1$[rsp]
  00157	e8 00 00 00 00	 call	 memcpy
  0015c	90		 npad	 1
$LN3@Take_conte:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1309 :         _My_data._Myres  = _Right_data._Myres;

  0015d	48 8b 44 24 38	 mov	 rax, QWORD PTR _My_data$[rsp]
  00162	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Right_data$[rsp]
  00167	48 8b 49 18	 mov	 rcx, QWORD PTR [rcx+24]
  0016b	48 89 48 18	 mov	 QWORD PTR [rax+24], rcx

; 1310 :         _My_data._Mysize = _Right_data._Mysize;

  0016f	48 8b 44 24 38	 mov	 rax, QWORD PTR _My_data$[rsp]
  00174	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Right_data$[rsp]
  00179	48 8b 49 10	 mov	 rcx, QWORD PTR [rcx+16]
  0017d	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 1311 : 
; 1312 :         _Right_data._Mysize = 0;

  00181	48 8b 44 24 28	 mov	 rax, QWORD PTR _Right_data$[rsp]
  00186	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 1313 :         _Right_data._Myres  = _Small_string_capacity;

  0018e	48 8b 44 24 28	 mov	 rax, QWORD PTR _Right_data$[rsp]
  00193	48 c7 40 18 07
	00 00 00	 mov	 QWORD PTR [rax+24], 7

; 1314 :         _Traits::assign(_Right_data._Bx._Buf[0], _Elem());

  0019b	33 c0		 xor	 eax, eax
  0019d	66 89 44 24 32	 mov	 WORD PTR $T3[rsp], ax
  001a2	b8 02 00 00 00	 mov	 eax, 2
  001a7	48 6b c0 00	 imul	 rax, rax, 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 347  :         _Left = _Right;

  001ab	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Right_data$[rsp]
  001b0	0f b7 54 24 32	 movzx	 edx, WORD PTR $T3[rsp]
  001b5	66 89 14 01	 mov	 WORD PTR [rcx+rax], dx
$LN1@Take_conte:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1315 :     }

  001b9	48 81 c4 98 00
	00 00		 add	 rsp, 152		; 00000098H
  001c0	c3		 ret	 0
?_Take_contents@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXAEAV12@@Z ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Take_contents
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@$$QEAV01@@Z
_TEXT	SEGMENT
$T1 = 32
this$ = 40
this$ = 48
$T2 = 56
$T3 = 64
this$ = 72
$T4 = 80
this$ = 112
_Right$ = 120
??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@$$QEAV01@@Z PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >, COMDAT

; 1028 :         : _Mypair(_One_then_variadic_args_t{}, _STD move(_Right._Getal())) {

$LN138:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 60	 sub	 rsp, 96			; 00000060H

; 3107 :         return _Mypair._Get_first();

  0000f	48 8b 44 24 78	 mov	 rax, QWORD PTR _Right$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00014	48 89 44 24 38	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3107 :         return _Mypair._Get_first();

  00019	48 8b 44 24 38	 mov	 rax, QWORD PTR $T2[rsp]
  0001e	48 89 44 24 40	 mov	 QWORD PTR $T3[rsp], rax

; 1028 :         : _Mypair(_One_then_variadic_args_t{}, _STD move(_Right._Getal())) {

  00023	48 8b 44 24 40	 mov	 rax, QWORD PTR $T3[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00028	48 89 44 24 50	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1028 :         : _Mypair(_One_then_variadic_args_t{}, _STD move(_Right._Getal())) {

  0002d	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  00032	48 89 44 24 48	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1536 :         : _Ty1(_STD forward<_Other1>(_Val1)), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  00037	48 8b 44 24 48	 mov	 rax, QWORD PTR this$[rsp]
  0003c	48 89 44 24 28	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 402  :     _CONSTEXPR20 _String_val() noexcept : _Bx() {}

  00041	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00046	48 89 44 24 30	 mov	 QWORD PTR this$[rsp], rax

; 493  :         _CONSTEXPR20 _Bxty() noexcept : _Buf() {} // user-provided, for fancy pointers

  0004b	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00050	48 8b 7c 24 30	 mov	 rdi, QWORD PTR this$[rsp]
  00055	33 c0		 xor	 eax, eax
  00057	b9 10 00 00 00	 mov	 ecx, 16
  0005c	f3 aa		 rep stosb

; 517  :     size_type _Mysize = 0; // current length of string (size)

  0005e	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00063	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 518  :     size_type _Myres  = 0; // current storage reserved for string (capacity)

  0006b	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00070	48 c7 40 18 00
	00 00 00	 mov	 QWORD PTR [rax+24], 0

; 1029 :         _Mypair._Myval2._Alloc_proxy(_GET_PROXY_ALLOCATOR(_Alty, _Getal()));

  00078	48 8d 44 24 20	 lea	 rax, QWORD PTR $T1[rsp]
  0007d	48 8b f8	 mov	 rdi, rax
  00080	33 c0		 xor	 eax, eax
  00082	b9 01 00 00 00	 mov	 ecx, 1
  00087	f3 aa		 rep stosb

; 1030 :         _Take_contents(_Right);

  00089	48 8b 54 24 78	 mov	 rdx, QWORD PTR _Right$[rsp]
  0008e	48 8b 4c 24 70	 mov	 rcx, QWORD PTR this$[rsp]
  00093	e8 00 00 00 00	 call	 ?_Take_contents@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXAEAV12@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Take_contents
  00098	90		 npad	 1

; 1031 :     }

  00099	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0009e	48 83 c4 60	 add	 rsp, 96			; 00000060H
  000a2	5f		 pop	 rdi
  000a3	c3		 ret	 0
??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@$$QEAV01@@Z ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
_TEXT	SEGMENT
$T1 = 0
$T2 = 2
_My_data$ = 8
this$ = 32
?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_empty, COMDAT

; 855  :     _CONSTEXPR20 void _Construct_empty() {

$LN18:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi
  00006	48 83 ec 10	 sub	 rsp, 16

; 856  :         auto& _My_data = _Mypair._Myval2;

  0000a	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0000f	48 89 44 24 08	 mov	 QWORD PTR _My_data$[rsp], rax

; 857  :         _My_data._Alloc_proxy(_GET_PROXY_ALLOCATOR(_Alty, _Getal()));

  00014	48 8d 04 24	 lea	 rax, QWORD PTR $T1[rsp]
  00018	48 8b f8	 mov	 rdi, rax
  0001b	33 c0		 xor	 eax, eax
  0001d	b9 01 00 00 00	 mov	 ecx, 1
  00022	f3 aa		 rep stosb

; 858  : 
; 859  :         // initialize basic_string data members
; 860  :         _My_data._Mysize = 0;

  00024	48 8b 44 24 08	 mov	 rax, QWORD PTR _My_data$[rsp]
  00029	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 861  :         _My_data._Myres  = _Small_string_capacity;

  00031	48 8b 44 24 08	 mov	 rax, QWORD PTR _My_data$[rsp]
  00036	48 c7 40 18 07
	00 00 00	 mov	 QWORD PTR [rax+24], 7

; 862  :         _My_data._Activate_SSO_buffer();
; 863  : 
; 864  :         // the _Traits::assign is last so the codegen doesn't think the char write can alias this
; 865  :         _Traits::assign(_My_data._Bx._Buf[0], _Elem());

  0003e	33 c0		 xor	 eax, eax
  00040	66 89 44 24 02	 mov	 WORD PTR $T2[rsp], ax
  00045	b8 02 00 00 00	 mov	 eax, 2
  0004a	48 6b c0 00	 imul	 rax, rax, 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 347  :         _Left = _Right;

  0004e	48 8b 4c 24 08	 mov	 rcx, QWORD PTR _My_data$[rsp]
  00053	0f b7 54 24 02	 movzx	 edx, WORD PTR $T2[rsp]
  00058	66 89 14 01	 mov	 WORD PTR [rcx+rax], dx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 866  :     }

  0005c	48 83 c4 10	 add	 rsp, 16
  00060	5f		 pop	 rdi
  00061	c3		 ret	 0
?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_empty
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Xran@?$_String_val@U?$_Simple_types@_W@std@@@std@@SAXXZ
_TEXT	SEGMENT
?_Xran@?$_String_val@U?$_Simple_types@_W@std@@@std@@SAXXZ PROC ; std::_String_val<std::_Simple_types<wchar_t> >::_Xran, COMDAT

; 481  :     [[noreturn]] static void _Xran() {

$LN3:
  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 482  :         _Xout_of_range("invalid string position");

  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_0BI@CFPLBAOH@invalid?5string?5position@
  0000b	e8 00 00 00 00	 call	 ?_Xout_of_range@std@@YAXPEBD@Z ; std::_Xout_of_range
  00010	90		 npad	 1
$LN2@Xran:

; 483  :     }

  00011	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00015	c3		 ret	 0
?_Xran@?$_String_val@U?$_Simple_types@_W@std@@@std@@SAXXZ ENDP ; std::_String_val<std::_Simple_types<wchar_t> >::_Xran
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEAAPEA_WXZ
_TEXT	SEGMENT
$T1 = 0
tv76 = 4
_Result$ = 8
_Ptr$ = 16
$T2 = 24
this$ = 48
?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEAAPEA_WXZ PROC ; std::_String_val<std::_Simple_types<wchar_t> >::_Myptr, COMDAT

; 434  :     _NODISCARD _CONSTEXPR20 value_type* _Myptr() noexcept {

$LN17:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 435  :         value_type* _Result = _Bx._Buf;

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 89 44 24 08	 mov	 QWORD PTR _Result$[rsp], rax

; 453  :         return _Myres > _Small_string_capacity;

  00013	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00018	48 83 78 18 07	 cmp	 QWORD PTR [rax+24], 7
  0001d	76 0a		 jbe	 SHORT $LN7@Myptr
  0001f	c7 44 24 04 01
	00 00 00	 mov	 DWORD PTR tv76[rsp], 1
  00027	eb 08		 jmp	 SHORT $LN8@Myptr
$LN7@Myptr:
  00029	c7 44 24 04 00
	00 00 00	 mov	 DWORD PTR tv76[rsp], 0
$LN8@Myptr:
  00031	0f b6 44 24 04	 movzx	 eax, BYTE PTR tv76[rsp]
  00036	88 04 24	 mov	 BYTE PTR $T1[rsp], al

; 436  :         if (_Large_mode_engaged()) {

  00039	0f b6 04 24	 movzx	 eax, BYTE PTR $T1[rsp]
  0003d	0f b6 c0	 movzx	 eax, al
  00040	85 c0		 test	 eax, eax
  00042	74 21		 je	 SHORT $LN2@Myptr

; 437  :             _Result = _Unfancy(_Bx._Ptr);

  00044	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00049	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0004c	48 89 44 24 10	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  00051	48 8b 44 24 10	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00056	48 89 44 24 18	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 437  :             _Result = _Unfancy(_Bx._Ptr);

  0005b	48 8b 44 24 18	 mov	 rax, QWORD PTR $T2[rsp]
  00060	48 89 44 24 08	 mov	 QWORD PTR _Result$[rsp], rax
$LN2@Myptr:

; 438  :         }
; 439  : 
; 440  :         return _Result;

  00065	48 8b 44 24 08	 mov	 rax, QWORD PTR _Result$[rsp]

; 441  :     }

  0006a	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0006e	c3		 ret	 0
?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEAAPEA_WXZ ENDP ; std::_String_val<std::_Simple_types<wchar_t> >::_Myptr
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z
_TEXT	SEGMENT
_Overflow_is_possible$1 = 32
_Bytes$ = 40
$T2 = 48
$T3 = 56
$T4 = 64
_Max_possible$5 = 72
this$ = 96
_Count$ = 104
?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z PROC	; std::allocator<wchar_t>::allocate, COMDAT

; 988  :     _NODISCARD_RAW_PTR_ALLOC _CONSTEXPR20 __declspec(allocator) _Ty* allocate(_CRT_GUARDOVERFLOW const size_t _Count) {

$LN13:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 113  :     constexpr bool _Overflow_is_possible = _Ty_size > 1;

  0000e	c6 44 24 20 01	 mov	 BYTE PTR _Overflow_is_possible$1[rsp], 1

; 114  : 
; 115  :     if constexpr (_Overflow_is_possible) {
; 116  :         constexpr size_t _Max_possible = static_cast<size_t>(-1) / _Ty_size;

  00013	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  0001d	48 89 44 24 48	 mov	 QWORD PTR _Max_possible$5[rsp], rax

; 117  :         if (_Count > _Max_possible) {

  00022	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  0002c	48 39 44 24 68	 cmp	 QWORD PTR _Count$[rsp], rax
  00031	76 06		 jbe	 SHORT $LN4@allocate

; 118  :             _Throw_bad_array_new_length(); // multiply overflow

  00033	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00038	90		 npad	 1
$LN4@allocate:

; 119  :         }
; 120  :     }
; 121  : 
; 122  :     return _Count * _Ty_size;

  00039	48 8b 44 24 68	 mov	 rax, QWORD PTR _Count$[rsp]
  0003e	48 d1 e0	 shl	 rax, 1
  00041	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00046	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
  0004b	48 89 44 24 28	 mov	 QWORD PTR _Bytes$[rsp], rax

; 227  :     if (_Bytes == 0) {

  00050	48 83 7c 24 28
	00		 cmp	 QWORD PTR _Bytes$[rsp], 0
  00056	75 0b		 jne	 SHORT $LN8@allocate

; 228  :         return nullptr;

  00058	48 c7 44 24 30
	00 00 00 00	 mov	 QWORD PTR $T2[rsp], 0
  00061	eb 35		 jmp	 SHORT $LN7@allocate
$LN8@allocate:

; 229  :     }
; 230  : 
; 231  : #if _HAS_CXX20 // TRANSITION, GH-1532
; 232  :     if (_STD is_constant_evaluated()) {
; 233  :         return _Traits::_Allocate(_Bytes);
; 234  :     }
; 235  : #endif // _HAS_CXX20
; 236  : 
; 237  : #ifdef __cpp_aligned_new
; 238  :     if constexpr (_Align > __STDCPP_DEFAULT_NEW_ALIGNMENT__) {
; 239  :         size_t _Passed_align = _Align;
; 240  : #if defined(_M_IX86) || defined(_M_X64)
; 241  :         if (_Bytes >= _Big_allocation_threshold) {
; 242  :             // boost the alignment of big allocations to help autovectorization
; 243  :             _Passed_align = (_STD max)(_Align, _Big_allocation_alignment);
; 244  :         }
; 245  : #endif // defined(_M_IX86) || defined(_M_X64)
; 246  :         return _Traits::_Allocate_aligned(_Bytes, _Passed_align);
; 247  :     } else
; 248  : #endif // defined(__cpp_aligned_new)
; 249  :     {
; 250  : #if defined(_M_IX86) || defined(_M_X64)
; 251  :         if (_Bytes >= _Big_allocation_threshold) {

  00063	48 81 7c 24 28
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  0006c	72 11		 jb	 SHORT $LN9@allocate

; 252  :             // boost the alignment of big allocations to help autovectorization
; 253  :             return _Allocate_manually_vector_aligned<_Traits>(_Bytes);

  0006e	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00073	e8 00 00 00 00	 call	 ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
  00078	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
  0007d	eb 19		 jmp	 SHORT $LN7@allocate
$LN9@allocate:

; 136  :         return ::operator new(_Bytes);

  0007f	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00084	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00089	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax

; 256  :         return _Traits::_Allocate(_Bytes);

  0008e	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  00093	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
$LN7@allocate:

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00098	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
$LN6@allocate:

; 991  :     }

  0009d	48 83 c4 58	 add	 rsp, 88			; 00000058H
  000a1	c3		 ret	 0
?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z ENDP	; std::allocator<wchar_t>::allocate
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Xlen_string@std@@YAXXZ
_TEXT	SEGMENT
?_Xlen_string@std@@YAXXZ PROC				; std::_Xlen_string, COMDAT

; 530  : [[noreturn]] inline void _Xlen_string() {

$LN3:
  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 531  :     _Xlength_error("string too long");

  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_0BA@JFNIOLAK@string?5too?5long@
  0000b	e8 00 00 00 00	 call	 ?_Xlength_error@std@@YAXPEBD@Z ; std::_Xlength_error
  00010	90		 npad	 1
$LN2@Xlen_strin:

; 532  : }

  00011	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00015	c3		 ret	 0
?_Xlen_string@std@@YAXXZ ENDP				; std::_Xlen_string
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z
_TEXT	SEGMENT
_Back_shift$ = 48
_Ptr_container$ = 56
_Ptr_user$ = 64
_Min_back_shift$ = 72
_Ptr$ = 96
_Bytes$ = 104
?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z PROC ; std::_Adjust_manually_vector_aligned, COMDAT

; 200  : inline void _Adjust_manually_vector_aligned(void*& _Ptr, size_t& _Bytes) {

$LN5:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 201  :     // adjust parameters from _Allocate_manually_vector_aligned to pass to operator delete
; 202  :     _Bytes += _Non_user_size;

  0000e	48 8b 44 24 68	 mov	 rax, QWORD PTR _Bytes$[rsp]
  00013	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00016	48 83 c0 27	 add	 rax, 39			; 00000027H
  0001a	48 8b 4c 24 68	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  0001f	48 89 01	 mov	 QWORD PTR [rcx], rax

; 203  : 
; 204  :     const uintptr_t* const _Ptr_user = static_cast<uintptr_t*>(_Ptr);

  00022	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00027	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0002a	48 89 44 24 40	 mov	 QWORD PTR _Ptr_user$[rsp], rax

; 205  :     const uintptr_t _Ptr_container   = _Ptr_user[-1];

  0002f	b8 08 00 00 00	 mov	 eax, 8
  00034	48 6b c0 ff	 imul	 rax, rax, -1
  00038	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr_user$[rsp]
  0003d	48 8b 04 01	 mov	 rax, QWORD PTR [rcx+rax]
  00041	48 89 44 24 38	 mov	 QWORD PTR _Ptr_container$[rsp], rax

; 206  : 
; 207  :     // If the following asserts, it likely means that we are performing
; 208  :     // an aligned delete on memory coming from an unaligned allocation.
; 209  :     _STL_ASSERT(_Ptr_user[-2] == _Big_allocation_sentinel, "invalid argument");
; 210  : 
; 211  :     // Extra paranoia on aligned allocation/deallocation; ensure _Ptr_container is
; 212  :     // in range [_Min_back_shift, _Non_user_size]
; 213  : #ifdef _DEBUG
; 214  :     constexpr uintptr_t _Min_back_shift = 2 * sizeof(void*);
; 215  : #else // ^^^ defined(_DEBUG) / !defined(_DEBUG) vvv
; 216  :     constexpr uintptr_t _Min_back_shift = sizeof(void*);

  00046	48 c7 44 24 48
	08 00 00 00	 mov	 QWORD PTR _Min_back_shift$[rsp], 8

; 217  : #endif // ^^^ !defined(_DEBUG) ^^^
; 218  :     const uintptr_t _Back_shift = reinterpret_cast<uintptr_t>(_Ptr) - _Ptr_container;

  0004f	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00054	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr_container$[rsp]
  00059	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0005c	48 2b c1	 sub	 rax, rcx
  0005f	48 89 44 24 30	 mov	 QWORD PTR _Back_shift$[rsp], rax

; 219  :     _STL_VERIFY(_Back_shift >= _Min_back_shift && _Back_shift <= _Non_user_size, "invalid argument");

  00064	48 83 7c 24 30
	08		 cmp	 QWORD PTR _Back_shift$[rsp], 8
  0006a	72 08		 jb	 SHORT $LN3@Adjust_man
  0006c	48 83 7c 24 30
	27		 cmp	 QWORD PTR _Back_shift$[rsp], 39 ; 00000027H
  00072	76 19		 jbe	 SHORT $LN2@Adjust_man
$LN3@Adjust_man:
  00074	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR [rsp+32], 0
  0007d	45 33 c9	 xor	 r9d, r9d
  00080	45 33 c0	 xor	 r8d, r8d
  00083	33 d2		 xor	 edx, edx
  00085	33 c9		 xor	 ecx, ecx
  00087	e8 00 00 00 00	 call	 _invoke_watson
  0008c	90		 npad	 1
$LN2@Adjust_man:

; 220  :     _Ptr = reinterpret_cast<void*>(_Ptr_container);

  0008d	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00092	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr_container$[rsp]
  00097	48 89 08	 mov	 QWORD PTR [rax], rcx
$LN4@Adjust_man:

; 221  : }

  0009a	48 83 c4 58	 add	 rsp, 88			; 00000058H
  0009e	c3		 ret	 0
?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ENDP ; std::_Adjust_manually_vector_aligned
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?_Throw_bad_array_new_length@std@@YAXXZ
_TEXT	SEGMENT
$T1 = 32
?_Throw_bad_array_new_length@std@@YAXXZ PROC		; std::_Throw_bad_array_new_length, COMDAT

; 107  : [[noreturn]] inline void _Throw_bad_array_new_length() {

$LN3:
  00000	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 108  :     _THROW(bad_array_new_length{});

  00004	48 8d 4c 24 20	 lea	 rcx, QWORD PTR $T1[rsp]
  00009	e8 00 00 00 00	 call	 ??0bad_array_new_length@std@@QEAA@XZ ; std::bad_array_new_length::bad_array_new_length
  0000e	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:_TI3?AVbad_array_new_length@std@@
  00015	48 8d 4c 24 20	 lea	 rcx, QWORD PTR $T1[rsp]
  0001a	e8 00 00 00 00	 call	 _CxxThrowException
  0001f	90		 npad	 1
$LN2@Throw_bad_:

; 109  : }

  00020	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00024	c3		 ret	 0
?_Throw_bad_array_new_length@std@@YAXXZ ENDP		; std::_Throw_bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gbad_array_new_length@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gbad_array_new_length@std@@UEAAPEAXI@Z PROC		; std::bad_array_new_length::`scalar deleting destructor', COMDAT
$LN20:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gbad_array_new_length@std@@UEAAPEAXI@Z ENDP		; std::bad_array_new_length::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??0bad_array_new_length@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
__that$ = 56
??0bad_array_new_length@std@@QEAA@AEBV01@@Z PROC	; std::bad_array_new_length::bad_array_new_length, COMDAT
$LN14:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000e	48 8b 54 24 38	 mov	 rdx, QWORD PTR __that$[rsp]
  00013	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00018	e8 00 00 00 00	 call	 ??0bad_alloc@std@@QEAA@AEBV01@@Z
  0001d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00022	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_array_new_length@std@@6B@
  00029	48 89 08	 mov	 QWORD PTR [rax], rcx
  0002c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00031	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00035	c3		 ret	 0
??0bad_array_new_length@std@@QEAA@AEBV01@@Z ENDP	; std::bad_array_new_length::bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??1bad_array_new_length@std@@UEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1bad_array_new_length@std@@UEAA@XZ PROC		; std::bad_array_new_length::~bad_array_new_length, COMDAT
$LN14:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  00018	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 83 c0 08	 add	 rax, 8
  00021	48 8b c8	 mov	 rcx, rax
  00024	e8 00 00 00 00	 call	 __std_exception_destroy
  00029	90		 npad	 1
  0002a	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0002e	c3		 ret	 0
??1bad_array_new_length@std@@UEAA@XZ ENDP		; std::bad_array_new_length::~bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0bad_array_new_length@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 16
??0bad_array_new_length@std@@QEAA@XZ PROC		; std::bad_array_new_length::bad_array_new_length, COMDAT

; 144  :     {

$LN14:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi

; 67   :     {

  00006	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0000b	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00012	48 89 08	 mov	 QWORD PTR [rax], rcx

; 66   :         : _Data()

  00015	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 83 c0 08	 add	 rax, 8
  0001e	48 8b f8	 mov	 rdi, rax
  00021	33 c0		 xor	 eax, eax
  00023	b9 10 00 00 00	 mov	 ecx, 16
  00028	f3 aa		 rep stosb

; 68   :         _Data._What = _Message;

  0002a	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0002f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_0BF@KINCDENJ@bad?5array?5new?5length@
  00036	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 133  :     {

  0003a	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0003f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_alloc@std@@6B@
  00046	48 89 08	 mov	 QWORD PTR [rax], rcx

; 144  :     {

  00049	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_array_new_length@std@@6B@
  00055	48 89 08	 mov	 QWORD PTR [rax], rcx

; 145  :     }

  00058	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0005d	5f		 pop	 rdi
  0005e	c3		 ret	 0
??0bad_array_new_length@std@@QEAA@XZ ENDP		; std::bad_array_new_length::bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gbad_alloc@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gbad_alloc@std@@UEAAPEAXI@Z PROC			; std::bad_alloc::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gbad_alloc@std@@UEAAPEAXI@Z ENDP			; std::bad_alloc::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0bad_alloc@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
__that$ = 56
??0bad_alloc@std@@QEAA@AEBV01@@Z PROC			; std::bad_alloc::bad_alloc, COMDAT
$LN9:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 20	 sub	 rsp, 32			; 00000020H

; 73   :     {

  0000f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  0001b	48 89 08	 mov	 QWORD PTR [rax], rcx

; 72   :         : _Data()

  0001e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 83 c0 08	 add	 rax, 8
  00027	48 8b f8	 mov	 rdi, rax
  0002a	33 c0		 xor	 eax, eax
  0002c	b9 10 00 00 00	 mov	 ecx, 16
  00031	f3 aa		 rep stosb

; 74   :         __std_exception_copy(&_Other._Data, &_Data);

  00033	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 83 c0 08	 add	 rax, 8
  0003c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR __that$[rsp]
  00041	48 83 c1 08	 add	 rcx, 8
  00045	48 8b d0	 mov	 rdx, rax
  00048	e8 00 00 00 00	 call	 __std_exception_copy
  0004d	90		 npad	 1
  0004e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_alloc@std@@6B@
  0005a	48 89 08	 mov	 QWORD PTR [rax], rcx
  0005d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00062	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00066	5f		 pop	 rdi
  00067	c3		 ret	 0
??0bad_alloc@std@@QEAA@AEBV01@@Z ENDP			; std::bad_alloc::bad_alloc
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gexception@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gexception@std@@UEAAPEAXI@Z PROC			; std::exception::`scalar deleting destructor', COMDAT
$LN10:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gexception@std@@UEAAPEAXI@Z ENDP			; std::exception::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ?what@exception@std@@UEBAPEBDXZ
_TEXT	SEGMENT
tv69 = 0
this$ = 32
?what@exception@std@@UEBAPEBDXZ PROC			; std::exception::what, COMDAT

; 95   :     {

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 18	 sub	 rsp, 24

; 96   :         return _Data._What ? _Data._What : "Unknown exception";

  00009	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00013	74 0f		 je	 SHORT $LN3@what
  00015	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0001e	48 89 04 24	 mov	 QWORD PTR tv69[rsp], rax
  00022	eb 0b		 jmp	 SHORT $LN4@what
$LN3@what:
  00024	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BC@EOODALEL@Unknown?5exception@
  0002b	48 89 04 24	 mov	 QWORD PTR tv69[rsp], rax
$LN4@what:
  0002f	48 8b 04 24	 mov	 rax, QWORD PTR tv69[rsp]

; 97   :     }

  00033	48 83 c4 18	 add	 rsp, 24
  00037	c3		 ret	 0
?what@exception@std@@UEBAPEBDXZ ENDP			; std::exception::what
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0exception@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
_Other$ = 56
??0exception@std@@QEAA@AEBV01@@Z PROC			; std::exception::exception, COMDAT

; 73   :     {

$LN4:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  0000f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  0001b	48 89 08	 mov	 QWORD PTR [rax], rcx

; 72   :         : _Data()

  0001e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 83 c0 08	 add	 rax, 8
  00027	48 8b f8	 mov	 rdi, rax
  0002a	33 c0		 xor	 eax, eax
  0002c	b9 10 00 00 00	 mov	 ecx, 16
  00031	f3 aa		 rep stosb

; 74   :         __std_exception_copy(&_Other._Data, &_Data);

  00033	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 83 c0 08	 add	 rax, 8
  0003c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Other$[rsp]
  00041	48 83 c1 08	 add	 rcx, 8
  00045	48 8b d0	 mov	 rdx, rax
  00048	e8 00 00 00 00	 call	 __std_exception_copy
  0004d	90		 npad	 1

; 75   :     }

  0004e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00057	5f		 pop	 rdi
  00058	c3		 ret	 0
??0exception@std@@QEAA@AEBV01@@Z ENDP			; std::exception::exception
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
