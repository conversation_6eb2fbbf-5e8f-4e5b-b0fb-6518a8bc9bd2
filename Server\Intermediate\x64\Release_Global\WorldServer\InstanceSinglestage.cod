; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	?Update@InstanceSinglestage@mu2@@UEAAXXZ	; mu2::InstanceSinglestage::Update
PUBLIC	?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A ; mu2::ISingleton<mu2::ExecutionManager>::inst
EXTRN	atexit:PROC
EXTRN	?Update@Execution@mu2@@UEAAXXZ:PROC		; mu2::Execution::Update
EXTRN	?GetExecId@Execution@mu2@@QEBAAEBVExecutionZoneId@2@XZ:PROC ; mu2::Execution::GetExecId
EXTRN	??1ExecutionManager@mu2@@UEAA@XZ:PROC		; mu2::ExecutionManager::~ExecutionManager
EXTRN	?DestroyExecution@ExecutionManager@mu2@@QEAA_NAEBVExecutionZoneId@2@W4Enum@DestroyReason@2@@Z:PROC ; mu2::ExecutionManager::DestroyExecution
EXTRN	??0ExecutionManager@mu2@@AEAA@XZ:PROC		; mu2::ExecutionManager::ExecutionManager
;	COMDAT ?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A
_BSS	SEGMENT
?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A DB 01e0H DUP (?) ; mu2::ISingleton<mu2::ExecutionManager>::inst
_BSS	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Update@InstanceSinglestage@mu2@@UEAAXXZ DD imagerel $LN7
	DD	imagerel $LN7+119
	DD	imagerel $unwind$?Update@InstanceSinglestage@mu2@@UEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__E?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ DD imagerel ??__E?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ
	DD	imagerel ??__E?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ+34
	DD	imagerel $unwind$??__E?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__F?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ DD imagerel ??__F?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ
	DD	imagerel ??__F?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ+22
	DD	imagerel $unwind$??__F?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ
pdata	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
??inst$initializer$@?$ISingleton@VExecutionManager@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA DQ FLAT:??__E?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ ; ??inst$initializer$@?$ISingleton@VExecutionManager@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA
CRT$XCU	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__F?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__E?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Update@InstanceSinglestage@mu2@@UEAAXXZ DD 010901H
	DD	06209H
xdata	ENDS
; Function compile flags: /Odtp
;	COMDAT ??__F?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ
text$yd	SEGMENT
??__F?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ PROC ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::ExecutionManager>::inst'', COMDAT
  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A ; mu2::ISingleton<mu2::ExecutionManager>::inst
  0000b	e8 00 00 00 00	 call	 ??1ExecutionManager@mu2@@UEAA@XZ ; mu2::ExecutionManager::~ExecutionManager
  00010	90		 npad	 1
  00011	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00015	c3		 ret	 0
??__F?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ ENDP ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::ExecutionManager>::inst''
text$yd	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??__E?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ
text$di	SEGMENT
??__E?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ PROC ; `dynamic initializer for 'mu2::ISingleton<mu2::ExecutionManager>::inst'', COMDAT

; 90   : template<typename T> T ISingleton<T>::inst;

  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A ; mu2::ISingleton<mu2::ExecutionManager>::inst
  0000b	e8 00 00 00 00	 call	 ??0ExecutionManager@mu2@@AEAA@XZ ; mu2::ExecutionManager::ExecutionManager
  00010	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??__F?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::ExecutionManager>::inst''
  00017	e8 00 00 00 00	 call	 atexit
  0001c	90		 npad	 1
  0001d	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00021	c3		 ret	 0
??__E?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ ENDP ; `dynamic initializer for 'mu2::ISingleton<mu2::ExecutionManager>::inst''
text$di	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceSinglestage.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceSinglestage.cpp
;	COMDAT ?Update@InstanceSinglestage@mu2@@UEAAXXZ
_TEXT	SEGMENT
$T1 = 32
this$ = 64
?Update@InstanceSinglestage@mu2@@UEAAXXZ PROC		; mu2::InstanceSinglestage::Update, COMDAT

; 8    : 	{

$LN7:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 9    : 		__super::Update();

  00009	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0000e	e8 00 00 00 00	 call	 ?Update@Execution@mu2@@UEAAXXZ ; mu2::Execution::Update
  00013	90		 npad	 1

; 10   : 
; 11   : 		if (IsRunning())

  00014	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00019	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0001c	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00021	ff 90 b8 00 00
	00		 call	 QWORD PTR [rax+184]
  00027	0f b6 c0	 movzx	 eax, al
  0002a	85 c0		 test	 eax, eax
  0002c	74 44		 je	 SHORT $LN2@Update

; 12   : 		{
; 13   : 			//=============================================================
; 14   : 			// ´ÜÃþ°Ë»ç
; 15   : 			//=============================================================
; 16   : 						
; 17   : 			if( IsDestroyable() )

  0002e	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00033	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00036	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0003b	ff 90 c8 00 00
	00		 call	 QWORD PTR [rax+200]
  00041	0f b6 c0	 movzx	 eax, al
  00044	85 c0		 test	 eax, eax
  00046	74 2a		 je	 SHORT $LN3@Update
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  00048	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A ; mu2::ISingleton<mu2::ExecutionManager>::inst
  0004f	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceSinglestage.cpp

; 19   : 				theExecutionManager.DestroyExecution(GetExecId(), DestroyReason::InstanceExpired);

  00054	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00059	e8 00 00 00 00	 call	 ?GetExecId@Execution@mu2@@QEBAAEBVExecutionZoneId@2@XZ ; mu2::Execution::GetExecId
  0005e	48 8b 4c 24 20	 mov	 rcx, QWORD PTR $T1[rsp]
  00063	41 b8 03 00 00
	00		 mov	 r8d, 3
  00069	48 8b d0	 mov	 rdx, rax
  0006c	e8 00 00 00 00	 call	 ?DestroyExecution@ExecutionManager@mu2@@QEAA_NAEBVExecutionZoneId@2@W4Enum@DestroyReason@2@@Z ; mu2::ExecutionManager::DestroyExecution
  00071	90		 npad	 1
$LN3@Update:
$LN2@Update:

; 20   : 			}
; 21   : 		}
; 22   : 	}

  00072	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00076	c3		 ret	 0
?Update@InstanceSinglestage@mu2@@UEAAXXZ ENDP		; mu2::InstanceSinglestage::Update
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceSinglestage.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceSinglestage.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
