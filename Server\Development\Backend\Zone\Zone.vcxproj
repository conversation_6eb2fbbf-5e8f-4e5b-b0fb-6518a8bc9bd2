<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release_Build|Win32">
      <Configuration>Release_Build</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release_Build|x64">
      <Configuration>Release_Build</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release_Global|Win32">
      <Configuration>Release_Global</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release_Global|x64">
      <Configuration>Release_Global</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release_Japan|Win32">
      <Configuration>Release_Japan</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release_Japan|x64">
      <Configuration>Release_Japan</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Shipping_Global|Win32">
      <Configuration>Shipping_Global</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Shipping_Global|x64">
      <Configuration>Shipping_Global</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Shipping_Japan|Win32">
      <Configuration>Shipping_Japan</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Shipping_Japan|x64">
      <Configuration>Shipping_Japan</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Shipping|Win32">
      <Configuration>Shipping</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Shipping|x64">
      <Configuration>Shipping</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{4A1E3457-41BD-4CFD-8FEB-2B37B2E35D0D}</ProjectGuid>
    <RootNamespace>Zone</RootNamespace>
    <SccProjectName>
    </SccProjectName>
    <SccAuxPath>
    </SccAuxPath>
    <SccLocalPath>
    </SccLocalPath>
    <SccProvider>
    </SccProvider>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release_Build|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release_Japan|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release_Global|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Shipping_Global|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Shipping_Japan|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Shipping|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release_Build|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release_Japan|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release_Global|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Shipping_Global|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>false</WholeProgramOptimization>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Shipping_Japan|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>false</WholeProgramOptimization>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Shipping|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>false</WholeProgramOptimization>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\..\_Build\Mu2_property_$(Platform)_$(Configuration).props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release_Build|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\..\_Build\Mu2_property_$(Platform)_$(Configuration).props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release_Japan|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\..\_Build\Mu2_property_$(Platform)_$(Configuration).props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release_Global|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\..\_Build\Mu2_property_$(Platform)_$(Configuration).props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Shipping_Global|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\..\_Build\Mu2_property_$(Platform)_$(Configuration).props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Shipping_Japan|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\..\_Build\Mu2_property_$(Platform)_$(Configuration).props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Shipping|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\..\_Build\Mu2_property_$(Platform)_$(Configuration).props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\..\_Build\Mu2_property_$(Platform)_$(Configuration).props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release_Build|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\..\_Build\Mu2_property_$(Platform)_$(Configuration).props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release_Japan|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\..\_Build\Mu2_property_$(Platform)_$(Configuration).props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release_Global|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\..\_Build\Mu2_property_$(Platform)_$(Configuration).props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Shipping_Global|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\..\_Build\Mu2_property_$(Platform)_$(Configuration).props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Shipping_Japan|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\..\_Build\Mu2_property_$(Platform)_$(Configuration).props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Shipping|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\..\_Build\Mu2_property_$(Platform)_$(Configuration).props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\..\_Build\Mu2_property_$(Platform)_$(Configuration).props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\..\_Build\Mu2_property_$(Platform)_$(Configuration).props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.40219.1</_ProjectFileVersion>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Mu2_$(ProjectName)D</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Mu2_$(ProjectName)D</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Mu2_$(ProjectName)</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release_Build|Win32'">Mu2_$(ProjectName)</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release_Japan|Win32'">Mu2_$(ProjectName)</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release_Global|Win32'">Mu2_$(ProjectName)</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Shipping_Global|Win32'">Mu2_$(ProjectName)</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Shipping_Japan|Win32'">Mu2_$(ProjectName)</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Shipping|Win32'">Mu2_$(ProjectName)</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Mu2_$(ProjectName)R</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release_Build|x64'">Mu2_$(ProjectName)</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release_Japan|x64'">Mu2_$(ProjectName)R</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release_Global|x64'">Mu2_$(ProjectName)R</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Shipping_Global|x64'">Mu2_$(ProjectName)</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Shipping_Japan|x64'">Mu2_$(ProjectName)</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Shipping|x64'">Mu2_$(ProjectName)</TargetName>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(SolutionDir)..\..\Vendor\log4cplus\include;$(SolutionDir)..\..\Vendor\boost\;$(SolutionDir)..\..\Vendor\Cryptopp563\;$(SolutionDir)..\..\Vendor\DirectX\Include;$(SolutionDir)..\..\Vendor\Microsoft SQL Server 2008 R2 Native Client SDK\Include;$(SolutionDir)..\..\Vendor\yaml-cpp\include;$(SolutionDir)..\..\Vendor\jsoncpp\include;C:\Program Files (x86)\Visual Leak Detector\include;$(IncludePath)</IncludePath>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(SolutionDir)..\..\Vendor\log4cplus\include;$(SolutionDir)..\..\Vendor\boost\;$(SolutionDir)..\..\Vendor\Cryptopp563\;$(SolutionDir)..\..\Vendor\DirectX\Include;$(SolutionDir)..\..\Vendor\Microsoft SQL Server 2008 R2 Native Client SDK\Include;$(SolutionDir)..\..\Vendor\vld\include;$(SolutionDir)..\..\Vendor\jsoncpp\include;$(SolutionDir)..\..\Vendor\yaml-cpp\include;$(SolutionDir)..\..\Vendor\boost\libcapt;$(IncludePath)</IncludePath>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='Release_Build|x64'">$(SolutionDir)..\..\Vendor\log4cplus\include;$(SolutionDir)..\..\Vendor\boost\;$(SolutionDir)..\..\Vendor\Cryptopp563\;$(SolutionDir)..\..\Vendor\DirectX\Include;$(SolutionDir)..\..\Vendor\Microsoft SQL Server 2008 R2 Native Client SDK\Include;$(SolutionDir)..\..\Vendor\vld\include;$(SolutionDir)..\..\Vendor\jsoncpp\include;$(SolutionDir)..\..\Vendor\yaml-cpp\include;$(SolutionDir)..\..\Vendor\boost\libcapt;$(IncludePath)</IncludePath>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='Release_Japan|x64'">$(SolutionDir)..\..\Vendor\log4cplus\include;$(SolutionDir)..\..\Vendor\boost\;$(SolutionDir)..\..\Vendor\Cryptopp563\;$(SolutionDir)..\..\Vendor\DirectX\Include;$(SolutionDir)..\..\Vendor\Microsoft SQL Server 2008 R2 Native Client SDK\Include;$(SolutionDir)..\..\Vendor\vld\include;$(SolutionDir)..\..\Vendor\jsoncpp\include;$(SolutionDir)..\..\Vendor\yaml-cpp\include;$(IncludePath)</IncludePath>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='Release_Global|x64'">$(SolutionDir)..\..\Vendor\log4cplus\include;$(SolutionDir)..\..\Vendor\boost\;$(SolutionDir)..\..\Vendor\Cryptopp563\;$(SolutionDir)..\..\Vendor\DirectX\Include;$(SolutionDir)..\..\Vendor\Microsoft SQL Server 2008 R2 Native Client SDK\Include;$(SolutionDir)..\..\Vendor\vld\include;$(SolutionDir)..\..\Vendor\jsoncpp\include;$(SolutionDir)..\..\Vendor\yaml-cpp\include;$(IncludePath)</IncludePath>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='Shipping_Global|x64'">$(SolutionDir)..\..\Vendor\log4cplus\include;$(SolutionDir)..\..\Vendor\boost\;$(SolutionDir)..\..\Vendor\Cryptopp563\;$(SolutionDir)..\..\Vendor\DirectX\Include;$(SolutionDir)..\..\Vendor\Microsoft SQL Server 2008 R2 Native Client SDK\Include;$(SolutionDir)..\..\Vendor\vld\include;$(SolutionDir)..\..\Vendor\jsoncpp\include;$(SolutionDir)..\..\Vendor\yaml-cpp\include;$(IncludePath)</IncludePath>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='Shipping_Japan|x64'">$(SolutionDir)..\..\Vendor\log4cplus\include;$(SolutionDir)..\..\Vendor\boost\;$(SolutionDir)..\..\Vendor\Cryptopp563\;$(SolutionDir)..\..\Vendor\DirectX\Include;$(SolutionDir)..\..\Vendor\Microsoft SQL Server 2008 R2 Native Client SDK\Include;$(SolutionDir)..\..\Vendor\vld\include;$(SolutionDir)..\..\Vendor\jsoncpp\include;$(SolutionDir)..\..\Vendor\yaml-cpp\include;$(IncludePath)</IncludePath>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='Shipping|x64'">$(SolutionDir)..\..\Vendor\log4cplus\include;$(SolutionDir)..\..\Vendor\boost\;$(SolutionDir)..\..\Vendor\Cryptopp563\;$(SolutionDir)..\..\Vendor\DirectX\Include;$(SolutionDir)..\..\Vendor\Microsoft SQL Server 2008 R2 Native Client SDK\Include;$(SolutionDir)..\..\Vendor\vld\include;$(SolutionDir)..\..\Vendor\jsoncpp\include;$(SolutionDir)..\..\Vendor\yaml-cpp\include;$(IncludePath)</IncludePath>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">$(SolutionDir)..\..\Vendor\log4cplus\include;$(SolutionDir)..\..;$(SolutionDir)..\..\Vendor\boost\;$(SolutionDir)..\..\Vendor\Cryptopp563\;$(SolutionDir)..\..\Vendor\DirectX\Include;$(SolutionDir)..\..\Vendor\Microsoft SQL Server 2008 R2 Native Client SDK\Include;$(SolutionDir)..\..\Vendor\vld\include;$(IncludePath)</IncludePath>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">$(SolutionDir)..\..\Vendor\log4cplus\include;$(SolutionDir)..\..;$(SolutionDir)..\..\Vendor\boost\;$(SolutionDir)..\..\Vendor\Cryptopp563\;$(SolutionDir)..\..\Vendor\DirectX\Include;$(SolutionDir)..\..\Vendor\Microsoft SQL Server 2008 R2 Native Client SDK\Include;$(SolutionDir)..\..\Vendor\vld\include;$(IncludePath)</IncludePath>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='Release_Build|Win32'">$(SolutionDir)..\..\Vendor\log4cplus\include;$(SolutionDir)..\..;$(SolutionDir)..\..\Vendor\boost\;$(SolutionDir)..\..\Vendor\Cryptopp563\;$(SolutionDir)..\..\Vendor\DirectX\Include;$(SolutionDir)..\..\Vendor\Microsoft SQL Server 2008 R2 Native Client SDK\Include;$(SolutionDir)..\..\Vendor\vld\include;$(IncludePath)</IncludePath>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='Release_Japan|Win32'">$(SolutionDir)..\..\Vendor\log4cplus\include;$(SolutionDir)..\..;$(SolutionDir)..\..\Vendor\boost\;$(SolutionDir)..\..\Vendor\Cryptopp563\;$(SolutionDir)..\..\Vendor\DirectX\Include;$(SolutionDir)..\..\Vendor\Microsoft SQL Server 2008 R2 Native Client SDK\Include;$(SolutionDir)..\..\Vendor\vld\include;$(IncludePath)</IncludePath>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='Release_Global|Win32'">$(SolutionDir)..\..\Vendor\log4cplus\include;$(SolutionDir)..\..;$(SolutionDir)..\..\Vendor\boost\;$(SolutionDir)..\..\Vendor\Cryptopp563\;$(SolutionDir)..\..\Vendor\DirectX\Include;$(SolutionDir)..\..\Vendor\Microsoft SQL Server 2008 R2 Native Client SDK\Include;$(SolutionDir)..\..\Vendor\vld\include;$(IncludePath)</IncludePath>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='Shipping_Global|Win32'">$(SolutionDir)..\..\Vendor\log4cplus\include;$(SolutionDir)..\..;$(SolutionDir)..\..\Vendor\boost\;$(SolutionDir)..\..\Vendor\Cryptopp563\;$(SolutionDir)..\..\Vendor\DirectX\Include;$(SolutionDir)..\..\Vendor\Microsoft SQL Server 2008 R2 Native Client SDK\Include;$(SolutionDir)..\..\Vendor\vld\include;$(IncludePath)</IncludePath>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='Shipping_Japan|Win32'">$(SolutionDir)..\..\Vendor\log4cplus\include;$(SolutionDir)..\..;$(SolutionDir)..\..\Vendor\boost\;$(SolutionDir)..\..\Vendor\Cryptopp563\;$(SolutionDir)..\..\Vendor\DirectX\Include;$(SolutionDir)..\..\Vendor\Microsoft SQL Server 2008 R2 Native Client SDK\Include;$(SolutionDir)..\..\Vendor\vld\include;$(IncludePath)</IncludePath>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='Shipping|Win32'">$(SolutionDir)..\..\Vendor\log4cplus\include;$(SolutionDir)..\..;$(SolutionDir)..\..\Vendor\boost\;$(SolutionDir)..\..\Vendor\Cryptopp563\;$(SolutionDir)..\..\Vendor\DirectX\Include;$(SolutionDir)..\..\Vendor\Microsoft SQL Server 2008 R2 Native Client SDK\Include;$(SolutionDir)..\..\Vendor\vld\include;$(IncludePath)</IncludePath>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release_Build|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release_Japan|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release_Global|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Shipping_Global|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Shipping_Japan|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Shipping|x64'">false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LibraryPath>$(SolutionDir)..\..\Vendor\boost\stage\lib\x64;$(SolutionDir)..\..\Vendor\lib\x64;$(SolutionDir)..\Lib\x64;$(SolutionDir)..\..\Vendor\DirectX\Lib\x64;$(SolutionDir)..\..\Vendor\Microsoft SQL Server 2008 R2 Native Client SDK\Lib\x64;C:\Program Files (x86)\Visual Leak Detector\lib\Win64;$(LibraryPath)</LibraryPath>
    <LinkIncremental>
    </LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Shipping|x64'">
    <OutDir>$(SolutionDir)\..\bin\x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release_Global|x64'">
    <OutDir>$(SolutionDir)\..\bin\x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Shipping_Global|x64'">
    <OutDir>$(SolutionDir)\..\bin\x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Shipping_Japan|x64'">
    <OutDir>$(SolutionDir)\..\bin\x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release_Japan|x64'">
    <OutDir>$(SolutionDir)\..\bin\x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release_Build|x64'">
    <OutDir>$(SolutionDir)\..\bin\x64\</OutDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <AdditionalOptions>/D "DICE_IS_SERVER" -Zm120 %(AdditionalOptions)</AdditionalOptions>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>./;../../;../../External/Cryptopp563;../../../../Vendor/mongo/client;../../../../Vendor/wxWidgets/include;../../../../Vendor/wxWidgets/lib/vc_lib_mt/mswud;$(SolutionDir)..\..\Vendor\gwnav\sdk\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_CRT_SECURE_NO_WARNINGS;_CONSOLE;ATTACK_LOG;KYDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <TreatWarningAsError>false</TreatWarningAsError>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
    </ClCompile>
    <Link>
      <AdditionalDependencies>yamlD.lib;cryptlib_mt_d.lib;Winmm.lib;shlwapi.lib;Dbghelp.lib;Psapi.lib;ws2_32.lib;mswsock.lib;opengl32.lib;glu32.lib;CoreD.lib;DataBaseD.lib;EntityD.lib;MathD.lib;Mu2CommonD.lib;RealWorldD.lib;ServerNetworkD.lib;SharedD.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>../../../../Vendor/wxWidgets/lib/vc_lib_mt;../../../../Vendor/gwnav/lib/win32_vc_10.debug;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <TargetMachine>MachineX86</TargetMachine>
      <Profile>true</Profile>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalOptions>/D "DICE_IS_SERVER" /bigobj %(AdditionalOptions)</AdditionalOptions>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>./;../../;../../External/Cryptopp563;../../../../Vendor/mongo/client;../../../../Vendor/wxWidgets/lib/vc_amd64_lib_mt/mswud;../../../../Vendor/lua/include;../../../../Vendor/wxWidgets/include;$(SolutionDir)..\..\Vendor\gwnav\sdk\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>USING_CACHE_UPDATE_DB;_ONLY_SERVER_CORE;_CONSOLE;ATTACK_LOG;USE_KYNAPSE;_DEBUG;%(PreprocessorDefinitions);_SCL_SECURE_NO_WARNINGS;MU2_DEBUG;MU2_ZONE</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <TreatWarningAsError>true</TreatWarningAsError>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <EnableParallelCodeGeneration>true</EnableParallelCodeGeneration>
      <IntrinsicFunctions>false</IntrinsicFunctions>
      <OmitFramePointers>false</OmitFramePointers>
      <MultiProcessorCompilation>false</MultiProcessorCompilation>
      <OpenMPSupport>true</OpenMPSupport>
    </ClCompile>
    <Link>
      <AdditionalDependencies>Core_64D.lib;DataBaseD.lib;EntityD.lib;MathD.lib;Mu2CommonD.lib;RealWorldD.lib;ServerNetworkD.lib;SharedD.lib;yamlD.lib;cryptlib_mt_d.lib;Winmm.lib;Dbghelp.lib;Psapi.lib;ws2_32.lib;mswsock.lib;opengl32.lib;glu32.lib;lua53.lib;lib_json_mt_d.lib;libcapt_D.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>../../../../Vendor/wxWidgets/lib/vc_amd64_lib_mt;../../../../Vendor/gwnav/lib/win64_vc_10.debug;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <Profile>true</Profile>
      <ForceSymbolReferences>
      </ForceSymbolReferences>
      <LinkTimeCodeGeneration>
      </LinkTimeCodeGeneration>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <AdditionalOptions>/D "DICE_IS_SERVER" /Zm1000 %(AdditionalOptions)</AdditionalOptions>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <AdditionalIncludeDirectories>./;../../;../../External/Cryptopp563;../../External/FCollada/FCollada;../../External/FCollada/FCollada/LibXML/include;../../External/pathengine/interface;../../../../Vendor/LuaJIT/src;../../../../Vendor/mongo/client;../../../../Vendor/wxWidgets/include;../../../../Vendor/wxWidgets/lib/vc_lib_mt/mswu;$(SolutionDir)..\..\Vendor\gwnav\sdk\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>MU2_USE_RUNTIME_ASSERTION;_CONSOLE;DEVEL_AI_SCRIPT;USE_KYNAPSE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <TreatWarningAsError>false</TreatWarningAsError>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
    <Link>
      <AdditionalOptions>/fixed:no %(AdditionalOptions)</AdditionalOptions>
      <AdditionalDependencies>yaml.lib;cryptlib_mt.lib;Winmm.lib;Dbghelp.lib;Psapi.lib;lua51.lib;Core.lib;DataBase.lib;Entity.lib;Math.lib;Mu2Common.lib;RealWorld.lib;ServerNetwork.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>../../../../Vendor/wxWidgets/lib/vc_lib_mt;../../../../Vendor/gwnav/lib/win32_vc_10.release;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <TargetMachine>MachineX86</TargetMachine>
      <ImageHasSafeExceptionHandlers>false</ImageHasSafeExceptionHandlers>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release_Build|Win32'">
    <ClCompile>
      <AdditionalOptions>/D "DICE_IS_SERVER" /Zm1000 %(AdditionalOptions)</AdditionalOptions>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <AdditionalIncludeDirectories>./;../../;../../External/Cryptopp563;../../External/FCollada/FCollada;../../External/FCollada/FCollada/LibXML/include;../../External/pathengine/interface;../../../../Vendor/LuaJIT/src;../../../../Vendor/mongo/client;../../../../Vendor/wxWidgets/include;../../../../Vendor/wxWidgets/lib/vc_lib_mt/mswu;$(SolutionDir)..\..\Vendor\gwnav\sdk\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>MU2_USE_RUNTIME_ASSERTION;_CONSOLE;DEVEL_AI_SCRIPT;USE_KYNAPSE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <TreatWarningAsError>false</TreatWarningAsError>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
    <Link>
      <AdditionalOptions>/fixed:no %(AdditionalOptions)</AdditionalOptions>
      <AdditionalDependencies>yaml.lib;cryptlib_mt.lib;Winmm.lib;Dbghelp.lib;Psapi.lib;lua51.lib;Core.lib;DataBase.lib;Entity.lib;Math.lib;Mu2Common.lib;RealWorld.lib;ServerNetwork.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>../../../../Vendor/wxWidgets/lib/vc_lib_mt;../../../../Vendor/gwnav/lib/win32_vc_10.release;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <TargetMachine>MachineX86</TargetMachine>
      <ImageHasSafeExceptionHandlers>false</ImageHasSafeExceptionHandlers>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release_Japan|Win32'">
    <ClCompile>
      <AdditionalOptions>/D "DICE_IS_SERVER" /Zm1000 %(AdditionalOptions)</AdditionalOptions>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <AdditionalIncludeDirectories>./;../../;../../External/Cryptopp563;../../External/FCollada/FCollada;../../External/FCollada/FCollada/LibXML/include;../../External/pathengine/interface;../../../../Vendor/LuaJIT/src;../../../../Vendor/mongo/client;../../../../Vendor/wxWidgets/include;../../../../Vendor/wxWidgets/lib/vc_lib_mt/mswu;$(SolutionDir)..\..\Vendor\gwnav\sdk\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>MU2_USE_RUNTIME_ASSERTION;_CONSOLE;DEVEL_AI_SCRIPT;USE_KYNAPSE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <TreatWarningAsError>false</TreatWarningAsError>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
    <Link>
      <AdditionalOptions>/fixed:no %(AdditionalOptions)</AdditionalOptions>
      <AdditionalDependencies>yaml.lib;cryptlib_mt.lib;Winmm.lib;Dbghelp.lib;Psapi.lib;lua51.lib;Core.lib;DataBase.lib;Entity.lib;Math.lib;Mu2Common.lib;RealWorld.lib;ServerNetwork.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>../../../../Vendor/wxWidgets/lib/vc_lib_mt;../../../../Vendor/gwnav/lib/win32_vc_10.release;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <TargetMachine>MachineX86</TargetMachine>
      <ImageHasSafeExceptionHandlers>false</ImageHasSafeExceptionHandlers>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release_Global|Win32'">
    <ClCompile>
      <AdditionalOptions>/D "DICE_IS_SERVER" /Zm1000 %(AdditionalOptions)</AdditionalOptions>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <AdditionalIncludeDirectories>./;../../;../../External/Cryptopp563;../../External/FCollada/FCollada;../../External/FCollada/FCollada/LibXML/include;../../External/pathengine/interface;../../../../Vendor/LuaJIT/src;../../../../Vendor/mongo/client;../../../../Vendor/wxWidgets/include;../../../../Vendor/wxWidgets/lib/vc_lib_mt/mswu;$(SolutionDir)..\..\Vendor\gwnav\sdk\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>MU2_USE_RUNTIME_ASSERTION;_CONSOLE;DEVEL_AI_SCRIPT;USE_KYNAPSE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <TreatWarningAsError>false</TreatWarningAsError>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
    <Link>
      <AdditionalOptions>/fixed:no %(AdditionalOptions)</AdditionalOptions>
      <AdditionalDependencies>yaml.lib;cryptlib_mt.lib;Winmm.lib;Dbghelp.lib;Psapi.lib;lua51.lib;Core.lib;DataBase.lib;Entity.lib;Math.lib;Mu2Common.lib;RealWorld.lib;ServerNetwork.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>../../../../Vendor/wxWidgets/lib/vc_lib_mt;../../../../Vendor/gwnav/lib/win32_vc_10.release;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <TargetMachine>MachineX86</TargetMachine>
      <ImageHasSafeExceptionHandlers>false</ImageHasSafeExceptionHandlers>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Shipping_Global|Win32'">
    <ClCompile>
      <AdditionalOptions>/D "DICE_IS_SERVER" /Zm1000 %(AdditionalOptions)</AdditionalOptions>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <AdditionalIncludeDirectories>./;../../;../../External/Cryptopp563;../../External/FCollada/FCollada;../../External/FCollada/FCollada/LibXML/include;../../External/pathengine/interface;../../../../Vendor/LuaJIT/src;../../../../Vendor/mongo/client;../../../../Vendor/wxWidgets/include;../../../../Vendor/wxWidgets/lib/vc_lib_mt/mswu;$(SolutionDir)..\..\Vendor\gwnav\sdk\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>MU2_USE_RUNTIME_ASSERTION;_CONSOLE;DEVEL_AI_SCRIPT;USE_KYNAPSE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <TreatWarningAsError>false</TreatWarningAsError>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
    <Link>
      <AdditionalOptions>/fixed:no %(AdditionalOptions)</AdditionalOptions>
      <AdditionalDependencies>yaml.lib;cryptlib_mt.lib;Winmm.lib;Dbghelp.lib;Psapi.lib;lua51.lib;Core.lib;DataBase.lib;Entity.lib;Math.lib;Mu2Common.lib;RealWorld.lib;ServerNetwork.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>../../../../Vendor/wxWidgets/lib/vc_lib_mt;../../../../Vendor/gwnav/lib/win32_vc_10.release;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <TargetMachine>MachineX86</TargetMachine>
      <ImageHasSafeExceptionHandlers>false</ImageHasSafeExceptionHandlers>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Shipping_Japan|Win32'">
    <ClCompile>
      <AdditionalOptions>/D "DICE_IS_SERVER" /Zm1000 %(AdditionalOptions)</AdditionalOptions>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <AdditionalIncludeDirectories>./;../../;../../External/Cryptopp563;../../External/FCollada/FCollada;../../External/FCollada/FCollada/LibXML/include;../../External/pathengine/interface;../../../../Vendor/LuaJIT/src;../../../../Vendor/mongo/client;../../../../Vendor/wxWidgets/include;../../../../Vendor/wxWidgets/lib/vc_lib_mt/mswu;$(SolutionDir)..\..\Vendor\gwnav\sdk\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>MU2_USE_RUNTIME_ASSERTION;_CONSOLE;DEVEL_AI_SCRIPT;USE_KYNAPSE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <TreatWarningAsError>false</TreatWarningAsError>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
    <Link>
      <AdditionalOptions>/fixed:no %(AdditionalOptions)</AdditionalOptions>
      <AdditionalDependencies>yaml.lib;cryptlib_mt.lib;Winmm.lib;Dbghelp.lib;Psapi.lib;lua51.lib;Core.lib;DataBase.lib;Entity.lib;Math.lib;Mu2Common.lib;RealWorld.lib;ServerNetwork.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>../../../../Vendor/wxWidgets/lib/vc_lib_mt;../../../../Vendor/gwnav/lib/win32_vc_10.release;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <TargetMachine>MachineX86</TargetMachine>
      <ImageHasSafeExceptionHandlers>false</ImageHasSafeExceptionHandlers>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Shipping|Win32'">
    <ClCompile>
      <AdditionalOptions>/D "DICE_IS_SERVER" /Zm1000 %(AdditionalOptions)</AdditionalOptions>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <AdditionalIncludeDirectories>./;../../;../../External/Cryptopp563;../../External/FCollada/FCollada;../../External/FCollada/FCollada/LibXML/include;../../External/pathengine/interface;../../../../Vendor/LuaJIT/src;../../../../Vendor/mongo/client;../../../../Vendor/wxWidgets/include;../../../../Vendor/wxWidgets/lib/vc_lib_mt/mswu;$(SolutionDir)..\..\Vendor\gwnav\sdk\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>MU2_USE_RUNTIME_ASSERTION;_CONSOLE;DEVEL_AI_SCRIPT;USE_KYNAPSE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <TreatWarningAsError>false</TreatWarningAsError>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
    <Link>
      <AdditionalOptions>/fixed:no %(AdditionalOptions)</AdditionalOptions>
      <AdditionalDependencies>yaml.lib;cryptlib_mt.lib;Winmm.lib;Dbghelp.lib;Psapi.lib;lua51.lib;Core.lib;DataBase.lib;Entity.lib;Math.lib;Mu2Common.lib;RealWorld.lib;ServerNetwork.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>../../../../Vendor/wxWidgets/lib/vc_lib_mt;../../../../Vendor/gwnav/lib/win32_vc_10.release;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <TargetMachine>MachineX86</TargetMachine>
      <ImageHasSafeExceptionHandlers>false</ImageHasSafeExceptionHandlers>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <AdditionalIncludeDirectories>./;../../;../../External/Cryptopp563;../../External/FCollada/FCollada;../../External/FCollada/FCollada/LibXML/include;../../External/pathengine/interface;../../../../Vendor/mongo/client;../../../../Vendor/wxWidgets/lib/vc_amd64_lib_mt/mswu;../../../../Vendor/wxWidgets/include;$(SolutionDir)..\..\Vendor\gwnav\sdk\include;../../../../Vendor/Lua/include;../../../../Vendor/libcapt;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>USING_CACHE_UPDATE_DB;_ONLY_SERVER_CORE;MU2_USE_RUNTIME_ASSERTION;_CONSOLE;USE_KYNAPSE;%(PreprocessorDefinitions);_SCL_SECURE_NO_WARNINGS;MU2_RELEASE;MU2_ZONE</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <TreatWarningAsError>true</TreatWarningAsError>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
      <AdditionalOptions>/homeparams  /Zm800 %(AdditionalOptions)</AdditionalOptions>
      <Optimization>Disabled</Optimization>
      <FavorSizeOrSpeed>Neither</FavorSizeOrSpeed>
      <OmitFramePointers>true</OmitFramePointers>
      <EnableParallelCodeGeneration>true</EnableParallelCodeGeneration>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <OpenMPSupport>
      </OpenMPSupport>
    </ClCompile>
    <Link>
      <AdditionalOptions>/fixed:no %(AdditionalOptions)</AdditionalOptions>
      <AdditionalDependencies>Core_64.lib;DataBase.lib;Entity.lib;Math.lib;RealWorld.lib;Mu2CommonR.lib;SharedR.lib;ServerNetwork.lib;yaml.lib;cryptlib_mt.lib;Winmm.lib;Dbghelp.lib;Psapi.lib;lua53.lib;ws2_32.lib;mswsock.lib;opengl32.lib;glu32.lib;lib_json_mt.lib;libcapt_R.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>../../../../Vendor/wxWidgets/lib/vc_amd64_lib_mt;../../../../Vendor/gwnav/lib/win64_vc_10.shipping;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <GenerateMapFile>true</GenerateMapFile>
      <MapFileName>$(TargetDir)$(TargetName).map</MapFileName>
      <MapExports>true</MapExports>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release_Build|x64'">
    <ClCompile>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <AdditionalIncludeDirectories>./;../../;../../External/Cryptopp563;../../External/FCollada/FCollada;../../External/FCollada/FCollada/LibXML/include;../../External/pathengine/interface;../../../../Vendor/mongo/client;../../../../Vendor/wxWidgets/lib/vc_amd64_lib_mt/mswu;../../../../Vendor/wxWidgets/include;$(SolutionDir)..\..\Vendor\gwnav\sdk\include;../../../../Vendor/Lua/include;../../../../Vendor/libcapt;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>USING_CACHE_UPDATE_DB;_ONLY_SERVER_CORE;MU2_USE_RUNTIME_ASSERTION;_CONSOLE;USE_KYNAPSE;%(PreprocessorDefinitions);_SCL_SECURE_NO_WARNINGS;MU2_RELEASE;WIN32;_WIN64;NDEBUG;MU2_SERVER;MU2_ZONE</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <TreatWarningAsError>true</TreatWarningAsError>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
      <AdditionalOptions>/homeparams %(AdditionalOptions)</AdditionalOptions>
      <Optimization>Disabled</Optimization>
      <FavorSizeOrSpeed>Neither</FavorSizeOrSpeed>
      <OmitFramePointers>true</OmitFramePointers>
      <EnableParallelCodeGeneration>true</EnableParallelCodeGeneration>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <OpenMPSupport>
      </OpenMPSupport>
    </ClCompile>
    <Link>
      <AdditionalOptions>/fixed:no %(AdditionalOptions)</AdditionalOptions>
      <AdditionalDependencies>Core_64.lib;DataBase.lib;Entity.lib;Math.lib;RealWorld.lib;Mu2CommonR.lib;SharedR.lib;ServerNetwork.lib;yaml.lib;cryptlib_mt.lib;Winmm.lib;Dbghelp.lib;Psapi.lib;lua53.lib;ws2_32.lib;mswsock.lib;opengl32.lib;glu32.lib;lib_json_mt.lib;libcapt_R.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>../../../../Vendor/wxWidgets/lib/vc_amd64_lib_mt;../../../../Vendor/gwnav/lib/win64_vc_10.shipping;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <GenerateMapFile>true</GenerateMapFile>
      <MapFileName>$(TargetDir)$(TargetName).map</MapFileName>
      <MapExports>true</MapExports>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release_Japan|x64'">
    <ClCompile>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <AdditionalIncludeDirectories>./;../../;../../External/Cryptopp563;../../External/FCollada/FCollada;../../External/FCollada/FCollada/LibXML/include;../../External/pathengine/interface;../../../../Vendor/mongo/client;../../../../Vendor/wxWidgets/lib/vc_amd64_lib_mt/mswu;../../../../Vendor/wxWidgets/include;$(SolutionDir)..\..\Vendor\gwnav\sdk\include;../../../../Vendor/Lua/include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>MU2_ZONE;MU2_RELEASE;NDEBUG;MU2_SERVER;_ONLY_SERVER_CORE;MU2_USE_RUNTIME_ASSERTION;%(PreprocessorDefinitions);USING_CACHE_UPDATE_DB;FOR_GLOBAL_JAPAN;FOR_JAPAN</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <TreatWarningAsError>true</TreatWarningAsError>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
      <AdditionalOptions>/homeparams %(AdditionalOptions)</AdditionalOptions>
      <Optimization>MaxSpeed</Optimization>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <OmitFramePointers>true</OmitFramePointers>
      <EnableParallelCodeGeneration>true</EnableParallelCodeGeneration>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
    </ClCompile>
    <Link>
      <AdditionalOptions>/fixed:no %(AdditionalOptions)</AdditionalOptions>
      <AdditionalDependencies>Core_64.lib;DataBase.lib;Entity.lib;Math.lib;RealWorld.lib;Mu2CommonR.lib;SharedR.lib;ServerNetwork.lib;yaml.lib;cryptlib_mt.lib;Winmm.lib;Dbghelp.lib;Psapi.lib;lua53.lib;ws2_32.lib;mswsock.lib;opengl32.lib;glu32.lib;lib_json_mt.lib;libcapt_R.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>../../../../Vendor/wxWidgets/lib/vc_amd64_lib_mt;../../../../Vendor/gwnav/lib/win64_vc_10.shipping;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <GenerateMapFile>true</GenerateMapFile>
      <MapFileName>$(TargetDir)$(TargetName).map</MapFileName>
      <MapExports>true</MapExports>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release_Global|x64'">
    <ClCompile>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <AdditionalIncludeDirectories>./;../../;../../External/Cryptopp563;../../External/FCollada/FCollada;../../External/FCollada/FCollada/LibXML/include;../../External/pathengine/interface;../../../../Vendor/mongo/client;../../../../Vendor/wxWidgets/lib/vc_amd64_lib_mt/mswu;../../../../Vendor/wxWidgets/include;$(SolutionDir)..\..\Vendor\gwnav\sdk\include;../../../../Vendor/Lua/include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>MU2_ZONE;MU2_RELEASE;NDEBUG;MU2_SERVER;_ONLY_SERVER_CORE;MU2_USE_RUNTIME_ASSERTION;USING_CACHE_UPDATE_DB;FOR_GLOBAL_JAPAN;FOR_GLOBAL;FOR_JAPAN_SHOP;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <TreatWarningAsError>true</TreatWarningAsError>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
      <AdditionalOptions>/homeparams %(AdditionalOptions)</AdditionalOptions>
      <Optimization>MaxSpeed</Optimization>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <OmitFramePointers>true</OmitFramePointers>
      <EnableParallelCodeGeneration>true</EnableParallelCodeGeneration>
      <DisableSpecificWarnings>4566;4189;4091</DisableSpecificWarnings>
    </ClCompile>
    <Link>
      <AdditionalOptions>/fixed:no %(AdditionalOptions)</AdditionalOptions>
      <AdditionalDependencies>Core_64.lib;DataBase.lib;Entity.lib;Math.lib;RealWorld.lib;Mu2CommonR.lib;SharedR.lib;ServerNetwork.lib;yaml.lib;cryptlib_mt.lib;Winmm.lib;Dbghelp.lib;Psapi.lib;lua53.lib;ws2_32.lib;mswsock.lib;opengl32.lib;glu32.lib;lib_json_mt.lib;libcapt_R.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>../../../../Vendor/wxWidgets/lib/vc_amd64_lib_mt;../../../../Vendor/gwnav/lib/win64_vc_10.shipping;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <GenerateMapFile>true</GenerateMapFile>
      <MapFileName>$(TargetDir)$(TargetName).map</MapFileName>
      <MapExports>true</MapExports>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Shipping_Global|x64'">
    <ClCompile>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <AdditionalIncludeDirectories>./;../../;../../External/Cryptopp563;../../External/FCollada/FCollada;../../External/FCollada/FCollada/LibXML/include;../../External/pathengine/interface;../../../../Vendor/mongo/client;../../../../Vendor/wxWidgets/lib/vc_amd64_lib_mt/mswu;../../../../Vendor/wxWidgets/include;$(SolutionDir)..\..\Vendor\gwnav\sdk\include;../../../../Vendor/Lua/include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>MU2_ZONE;MU2_SHIPPING;WIN32;_WIN64;NDEBUG;MU2_SERVER;USING_CACHE_UPDATE_DB;_ONLY_SERVER_CORE;MU2_USE_RUNTIME_ASSERTION;_CONSOLE;USE_KYNAPSE;_SCL_SECURE_NO_WARNINGS;FOR_GLOBAL_JAPAN;FOR_GLOBAL;FOR_JAPAN_SHOP;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <TreatWarningAsError>true</TreatWarningAsError>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
      <AdditionalOptions>/homeparams /utf8 %(AdditionalOptions)</AdditionalOptions>
      <Optimization>MaxSpeed</Optimization>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <OmitFramePointers>true</OmitFramePointers>
      <EnableParallelCodeGeneration>true</EnableParallelCodeGeneration>
    </ClCompile>
    <Link>
      <AdditionalOptions>/fixed:no %(AdditionalOptions)</AdditionalOptions>
      <AdditionalDependencies>Core_64.lib;DataBase.lib;Entity.lib;Math.lib;Mu2Common.lib;RealWorld.lib;ServerNetwork.lib;Shared.lib;yaml.lib;cryptlib_mt.lib;Winmm.lib;Dbghelp.lib;Psapi.lib;lua53.lib;ws2_32.lib;mswsock.lib;opengl32.lib;glu32.lib;lib_json_mt.lib;libcapt_R.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>../../../../Vendor/wxWidgets/lib/vc_amd64_lib_mt;../../../../Vendor/gwnav/lib/win64_vc_10.shipping;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <GenerateMapFile>true</GenerateMapFile>
      <MapFileName>$(TargetDir)$(TargetName).map</MapFileName>
      <MapExports>true</MapExports>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Shipping_Japan|x64'">
    <ClCompile>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <AdditionalIncludeDirectories>./;../../;../../External/Cryptopp563;../../External/FCollada/FCollada;../../External/FCollada/FCollada/LibXML/include;../../External/pathengine/interface;../../../../Vendor/mongo/client;../../../../Vendor/wxWidgets/lib/vc_amd64_lib_mt/mswu;../../../../Vendor/wxWidgets/include;$(SolutionDir)..\..\Vendor\gwnav\sdk\include;../../../../Vendor/Lua/include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>MU2_ZONE;MU2_SHIPPING;WIN32;_WIN64;NDEBUG;MU2_SERVER;USING_CACHE_UPDATE_DB;_ONLY_SERVER_CORE;MU2_USE_RUNTIME_ASSERTION;_CONSOLE;USE_KYNAPSE;_SCL_SECURE_NO_WARNINGS;FOR_GLOBAL_JAPAN;FOR_JAPAN;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <TreatWarningAsError>true</TreatWarningAsError>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
      <AdditionalOptions>/homeparams %(AdditionalOptions)</AdditionalOptions>
      <Optimization>MaxSpeed</Optimization>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <OmitFramePointers>true</OmitFramePointers>
      <EnableParallelCodeGeneration>true</EnableParallelCodeGeneration>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
    </ClCompile>
    <Link>
      <AdditionalOptions>/fixed:no %(AdditionalOptions)</AdditionalOptions>
      <AdditionalDependencies>Core_64.lib;DataBase.lib;Entity.lib;Math.lib;Mu2Common.lib;RealWorld.lib;ServerNetwork.lib;Shared.lib;yaml.lib;cryptlib_mt.lib;Winmm.lib;Dbghelp.lib;Psapi.lib;lua53.lib;ws2_32.lib;mswsock.lib;opengl32.lib;glu32.lib;lib_json_mt.lib;libcapt_R.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>../../../../Vendor/wxWidgets/lib/vc_amd64_lib_mt;../../../../Vendor/gwnav/lib/win64_vc_10.shipping;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <GenerateMapFile>true</GenerateMapFile>
      <MapFileName>$(TargetDir)$(TargetName).map</MapFileName>
      <MapExports>true</MapExports>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Shipping|x64'">
    <ClCompile>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <AdditionalIncludeDirectories>./;../../;../../External/Cryptopp563;../../External/FCollada/FCollada;../../External/FCollada/FCollada/LibXML/include;../../External/pathengine/interface;../../../../Vendor/mongo/client;../../../../Vendor/wxWidgets/lib/vc_amd64_lib_mt/mswu;../../../../Vendor/wxWidgets/include;$(SolutionDir)..\..\Vendor\gwnav\sdk\include;../../../../Vendor/Lua/include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>MU2_SHIPPING;WIN32;_WIN64;NDEBUG;MU2_SERVER;USING_CACHE_UPDATE_DB;_ONLY_SERVER_CORE;MU2_USE_RUNTIME_ASSERTION;_CONSOLE;USE_KYNAPSE;_SCL_SECURE_NO_WARNINGS;MU2_ZONE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <TreatWarningAsError>true</TreatWarningAsError>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <EnableEnhancedInstructionSet>NotSet</EnableEnhancedInstructionSet>
      <AdditionalOptions>/homeparams -Zm1220 %(AdditionalOptions)</AdditionalOptions>
      <Optimization>MaxSpeed</Optimization>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <OmitFramePointers>false</OmitFramePointers>
      <EnableParallelCodeGeneration>true</EnableParallelCodeGeneration>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <EnableFiberSafeOptimizations>true</EnableFiberSafeOptimizations>
    </ClCompile>
    <Link>
      <AdditionalOptions>/fixed:no %(AdditionalOptions)</AdditionalOptions>
      <AdditionalDependencies>Core_64.lib;DataBase.lib;Entity.lib;Math.lib;Mu2Common.lib;RealWorld.lib;ServerNetwork.lib;Shared.lib;yaml.lib;cryptlib_mt.lib;Winmm.lib;Dbghelp.lib;Psapi.lib;lua53.lib;ws2_32.lib;mswsock.lib;opengl32.lib;glu32.lib;lib_json_mt.lib;libcapt_R.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>../../../../Vendor/wxWidgets/lib/vc_amd64_lib_mt;../../../../Vendor/gwnav/lib/win64_vc_10.shipping;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <GenerateMapFile>true</GenerateMapFile>
      <MapFileName>$(TargetDir)$(TargetName).map</MapFileName>
      <MapExports>true</MapExports>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="Action\ActionAchievement.cpp" />
    <ClCompile Include="Action\ActionAggro.cpp" />
    <ClCompile Include="Action\ActionArtifact.cpp" />
    <ClCompile Include="Action\ActionAutoHackChecker.cpp" />
    <ClCompile Include="Action\ActionBoundControl.cpp" />
    <ClCompile Include="Action\ActionBuff.cpp" />
    <ClCompile Include="Action\ActionCommon\Action4Zone.cpp" />
    <ClCompile Include="Action\ActionCommon\ActionCognition.cpp" />
    <ClCompile Include="Action\ActionCommon\ActionSkillControl.cpp" />
    <ClCompile Include="Action\ActionDailyMission.cpp" />
    <ClCompile Include="Action\ActionDroppedItem\ActionDroppedCognition.cpp" />
    <ClCompile Include="Action\ActionDynamicTagVolume\ActionDynamicTagVolumeCognition.cpp" />
    <ClCompile Include="Action\ActionEffectVolume\ActionEffectVolume.cpp" />
    <ClCompile Include="Action\ActionEffectVolume\ActionEffectVolumeCognition.cpp" />
    <ClCompile Include="Action\ActionEloPoint.cpp" />
    <ClCompile Include="Action\ActionEventProxy.cpp" />
    <ClCompile Include="Action\ActionFieldPoint.cpp" />
    <ClCompile Include="Action\ActionNpcBlackMarket.cpp" />
    <ClCompile Include="Action\ActionNpcColosseum.cpp" />
    <ClCompile Include="Action\ActionNpcCooperation.cpp" />
    <ClCompile Include="Action\ActionNpc\ActionNpcCognition.cpp" />
    <ClCompile Include="Action\ActionNpc\ActionNpcCommonTrigger.cpp" />
    <ClCompile Include="Action\ActionNpc\ActionNpcOperate.cpp" />
    <ClCompile Include="Action\ActionPassivity.cpp" />
    <ClCompile Include="Action\ActionDurability.cpp" />
    <ClCompile Include="Action\ActionDynamicTagVolume.cpp" />
    <ClCompile Include="Action\ActionExpiryItem.cpp" />
    <ClCompile Include="Action\ActionFunctional.cpp" />
    <ClCompile Include="Action\ActionMissionDungeon.cpp" />
    <ClCompile Include="Action\ActionMissionHero.cpp" />
    <ClCompile Include="Action\ActionNpcAggro.cpp" />
    <ClCompile Include="Action\ActionDamage.cpp" />
    <ClCompile Include="Action\ActionDroppedItem.cpp" />
    <ClCompile Include="Action\ActionEntity.cpp" />
    <ClCompile Include="Action\ActionNpc.cpp" />
    <ClCompile Include="Action\ActionNpcDamage.cpp" />
    <ClCompile Include="Action\ActionNpcMove.cpp" />
    <ClCompile Include="Action\ActionNpcPatrol.cpp" />
    <ClCompile Include="Action\ActionNpcScript.cpp" />
    <ClCompile Include="Action\ActionNpcSector.cpp" />
    <ClCompile Include="Action\ActionNpcSkill.cpp" />
    <ClCompile Include="Action\ActionNpc\ActionNpcAbility.cpp" />
    <ClCompile Include="Action\ActionParty.cpp" />
    <ClCompile Include="Action\ActionPetManage.cpp" />
    <ClCompile Include="Action\ActionPlayer.cpp" />
    <ClCompile Include="Action\ActionPlayerAggro.cpp" />
    <ClCompile Include="Action\ActionPlayerBlackMarket.cpp" />
    <ClCompile Include="Action\ActionPlayerDamage.cpp" />
    <ClCompile Include="Action\ActionPlayerGloryRank.cpp" />
    <ClCompile Include="Action\ActionPlayerInventory.cpp" />
    <ClCompile Include="Action\ActionPlayerItemTrade.cpp" />
    <ClCompile Include="Action\ActionPlayerLoad.cpp" />
    <ClCompile Include="Action\ActionPlayerMailBox.cpp" />
    <ClCompile Include="Action\ActionPlayerMaking.cpp" />
    <ClCompile Include="Action\ActionPlayerScript.cpp" />
    <ClCompile Include="Action\ActionPlayerSpeedHack.cpp" />
    <ClCompile Include="Action\ActionPlayerStorage.cpp" />
    <ClCompile Include="Action\ActionPlayer\ActionContact.cpp" />
    <ClCompile Include="Action\ActionPlayer\ActionJoin.cpp" />
    <ClCompile Include="Action\ActionPlayer\ActionPlayerAbility.cpp" />
    <ClCompile Include="Action\ActionPlayer\ActionPlayerAccountLevel.cpp" />
    <ClCompile Include="Action\ActionPlayer\ActionPlayerChaosCastleHistory.cpp" />
    <ClCompile Include="Action\ActionPlayer\ActionPlayerCognition.cpp" />
    <ClCompile Include="Action\ActionPlayer\ActionPlayerColosseum.cpp" />
    <ClCompile Include="Action\ActionPlayer\ActionPlayerCombatPower.cpp" />
    <ClCompile Include="Action\ActionPlayer\ActionPlayerDamageMeter.cpp" />
    <ClCompile Include="Action\ActionPlayer\ActionPlayerEscape.cpp" />
    <ClCompile Include="Action\ActionPlayer\ActionPlayerEvent.cpp" />
    <ClCompile Include="Action\ActionPlayer\ActionPlayerEventBuff.cpp" />
    <ClCompile Include="Action\ActionPlayer\ActionPlayerEventInventory.cpp" />
    <ClCompile Include="Action\ActionPlayer\ActionPlayerExchange.cpp" />
    <ClCompile Include="Action\ActionPlayer\ActionPlayerGuideSystem.cpp" />
    <ClCompile Include="Action\ActionPlayer\ActionPlayerKillCombo.cpp" />
    <ClCompile Include="Action\ActionPlayer\ActionPlayerKnightage.cpp" />
    <ClCompile Include="Action\ActionPlayer\ActionPlayerKnightageStorage.cpp" />
    <ClCompile Include="Action\ActionPlayer\ActionPlayerLimitedBannerGoods.cpp" />
    <ClCompile Include="Action\ActionPlayer\ActionPlayerMarble.cpp" />
    <ClCompile Include="Action\ActionPlayer\ActionPlayerMembership.cpp" />
    <ClCompile Include="Action\ActionPlayer\ActionPlayerMissionMapTier.cpp" />
    <ClCompile Include="Action\ActionPlayer\ActionPlayerNoticePopUp.cpp" />
    <ClCompile Include="Action\ActionPlayer\ActionPlayerReturnUser.cpp" />
    <ClCompile Include="Action\ActionPlayer\ActionPlayerRevive.cpp" />
    <ClCompile Include="Action\ActionPlayer\ActionPlayerSector.cpp" />
    <ClCompile Include="Action\ActionPlayer\ActionPlayerSetCard.cpp" />
    <ClCompile Include="Action\ActionPlayer\ActionPlayerSprout.cpp" />
    <ClCompile Include="Action\ActionPlayer\ActionPlayerSurvey.cpp" />
    <ClCompile Include="Action\ActionPlayer\ActionPlayerTalisman.cpp" />
    <ClCompile Include="Action\ActionPlayer\ActionPlayerTalismanUnit.cpp" />
    <ClCompile Include="Action\ActionPlayer\ActionQuickSlot.cpp" />
    <ClCompile Include="Action\ActionPlayer\ActionPlayerSeason.cpp" />
    <ClCompile Include="Action\ActionPlayer\ActionSkillManage.cpp" />
    <ClCompile Include="Action\ActionPlayer\ActionSkillMastery.cpp" />
    <ClCompile Include="Action\ActionPlayer\ActionSocket.cpp" />
    <ClCompile Include="Action\ActionPlayer\ActionSoulSkillManage.cpp" />
    <ClCompile Include="Action\ActionPortal.cpp" />
    <ClCompile Include="Action\ActionQuest.cpp" />
    <ClCompile Include="Action\ActionSector.cpp" />
    <ClCompile Include="Action\ActionSectorController.cpp" />
    <ClCompile Include="Action\ActionSetItemAbility.cpp" />
    <ClCompile Include="Action\ActionSkillChanger.cpp" />
    <ClCompile Include="Action\ActionSkillRune.cpp" />
    <ClCompile Include="Action\ActionSuppliedItemEachPortal.cpp" />
    <ClCompile Include="Action\ActionSuppliedSkillEachPortal.cpp" />
    <ClCompile Include="Action\ActionTutorial.cpp" />
    <ClCompile Include="Action\ActionUseItem.cpp" />
    <ClCompile Include="Action\ActionVolumeSpawn.cpp" />
    <ClCompile Include="Action\SectorItemUseChecker.cpp" />
    <ClCompile Include="AIScriptLogic\LuaAllocator.cpp" />
    <ClCompile Include="AIScriptLogic\LuaMemoryDebugger.cpp" />
    <ClCompile Include="AIScriptLogic\LuaStateCache.cpp" />
    <ClCompile Include="AIScriptLogic\LuaStatePool.cpp" />
    <ClCompile Include="AIScriptLogic\LuaUtil.cpp" />
    <ClCompile Include="AIScriptLogic\SectorLuaScript.cpp" />
    <ClCompile Include="Attribute\AttributeNpc.cpp" />
    <ClCompile Include="Attribute\AttributeSkill.cpp" />
    <ClCompile Include="Common\CommonCondition.cpp" />
    <ClCompile Include="Common\CommonTrigger.cpp" />
    <ClCompile Include="Common\CommonTriggerAction.cpp" />
    <ClCompile Include="Common\NpcConditionManager.cpp" />
    <ClCompile Include="Configure.cpp" />
    <ClCompile Include="ContentsDeactivator.cpp" />
    <ClCompile Include="Element\Ability\AbilityCalculator.cpp" />
    <ClCompile Include="Element\Ability\AbilityCalculator\AddDamageCalc.cpp" />
    <ClCompile Include="Element\Ability\AbilityCalculator\AddDefenceCalc.cpp" />
    <ClCompile Include="Element\Ability\AbilityCalculator\DamageCalc.cpp" />
    <ClCompile Include="Element\Ability\AbilityCalculator\DefenseCalc.cpp" />
    <ClCompile Include="Element\Ability\AbilityCalculator\ElementCalc.cpp" />
    <ClCompile Include="Element\Ability\AbilityCalculator\ObtainCalc.cpp" />
    <ClCompile Include="Element\Ability\AbilityCalculator\ResourceCalc.cpp" />
    <ClCompile Include="Element\Ability\AbilityCalculator\SpeedCalc.cpp" />
    <ClCompile Include="Element\Ability\NpcAbilityCalculator.cpp" />
    <ClCompile Include="Element\Achievement\Achievement.cpp" />
    <ClCompile Include="Element\Achievement\AchievementConditionChecker.cpp" />
    <ClCompile Include="Element\Achievement\ConditionGroup.cpp" />
    <ClCompile Include="Element\Achievement\AchievementConditionGroup.cpp" />
    <ClCompile Include="Element\Achievement\AchievementEvent.cpp" />
    <ClCompile Include="Element\Achievement\GuideCategory.cpp" />
    <ClCompile Include="Element\Achievement\GuideDivisionConditionGroup.cpp" />
    <ClCompile Include="Element\Achievement\GuideTrigger.cpp" />
    <ClCompile Include="Element\Item\EquipItemAbilityModifier.cpp" />
    <ClCompile Include="Element\Item\ExchangeShopInfo.cpp" />
    <ClCompile Include="Element\Item\InvenAccountStorage.cpp" />
    <ClCompile Include="Element\Item\InvenEquip.cpp" />
    <ClCompile Include="Element\Item\InvenKnightageStorage.cpp" />
    <ClCompile Include="Element\Item\InvenNonSlotable.cpp" />
    <ClCompile Include="Element\Item\InvenPrivateStorage.cpp" />
    <ClCompile Include="Element\Item\InvenSlot.cpp" />
    <ClCompile Include="Element\Item\InvenSlotable.cpp" />
    <ClCompile Include="Element\Item\Item.cpp" />
    <ClCompile Include="Element\Item\ItemAmplifaction.cpp" />
    <ClCompile Include="Element\Item\ItemBag.cpp" />
    <ClCompile Include="Element\Item\ItemCube.cpp" />
    <ClCompile Include="Element\Item\ItemEnchant.cpp" />
    <ClCompile Include="Element\Item\ItemFactory.cpp" />
    <ClCompile Include="Element\Item\ItemFinder.cpp" />
    <ClCompile Include="Element\Item\RueryTreasure.cpp" />
    <ClCompile Include="Element\Item\ItemRune.cpp" />
    <ClCompile Include="Element\Item\TradeItem.cpp" />
    <ClCompile Include="Element\Item\UseItem.cpp" />
    <ClCompile Include="Element\Item\UseItemLimit.cpp" />
    <ClCompile Include="Element\Mail\Mail.cpp" />
    <ClCompile Include="Element\Mail\MailBox.cpp" />
    <ClCompile Include="Element\Pet\Pet.cpp" />
    <ClCompile Include="Element\Pet\PetManager.cpp" />
    <ClCompile Include="Element\Quest\CheckMissionCompleteChecker.cpp" />
    <ClCompile Include="Element\Season\SeasonMission.cpp" />
    <ClCompile Include="Element\Season\SeasonMissionEvent.cpp" />
    <ClCompile Include="Element\Skill\PassiveCondition.cpp" />
    <ClCompile Include="Element\Skill\Passive\PassiveCondCastSkill.cpp" />
    <ClCompile Include="Element\Skill\Passive\PassiveCondCheckRangeEnemy.cpp" />
    <ClCompile Include="Element\Skill\Passive\PassiveCondEssenceChange.cpp" />
    <ClCompile Include="Element\Skill\Passive\PassiveCondHasBuff.cpp" />
    <ClCompile Include="Element\Skill\Passive\PassiveCondHitMe.cpp" />
    <ClCompile Include="Element\Skill\Passive\PassiveCondGetProperty.cpp" />
    <ClCompile Include="Element\Skill\Passive\PassiveCondHitTarget.cpp" />
    <ClCompile Include="Element\Skill\Passive\PassiveCondAbilityUpdate.cpp" />
    <ClCompile Include="Element\Skill\Passive\PassiveCondLevelUp.cpp" />
    <ClCompile Include="Element\Skill\Passive\PassiveCondNonBattleState.cpp" />
    <ClCompile Include="Element\Skill\Passive\PassiveCondQuestComplete.cpp" />
    <ClCompile Include="Element\Skill\Passive\PassiveCondRiding.cpp" />
    <ClCompile Include="Element\Skill\Passive\PassiveCondShieldBlock.cpp" />
    <ClCompile Include="Element\Skill\Passivity.cpp" />
    <ClCompile Include="Element\Skill\Skill.cpp" />
    <ClCompile Include="Element\Skill\SkillCastChecker.cpp" />
    <ClCompile Include="Element\Skill\SkillControl.cpp" />
    <ClCompile Include="Element\Skill\SkillCoolLineManager.cpp" />
    <ClCompile Include="Element\Skill\SkillLinker.cpp" />
    <ClCompile Include="Element\Skill\SpecialSkill.cpp" />
    <ClCompile Include="Element\Skill\TimeCorrector.cpp" />
    <ClCompile Include="Element\Skill\SpecialBlock.cpp" />
    <ClCompile Include="Element\Soul\SoulInfo.cpp" />
    <ClCompile Include="Element\Survey\Survey.cpp" />
    <ClCompile Include="Element\Survey\SurveyEvent.cpp" />
    <ClCompile Include="EntityFactory\Entity4Zone.cpp" />
    <ClCompile Include="EntityFactory\EntityControlVolume.cpp" />
    <ClCompile Include="EntityFactory\EntityDropped.cpp" />
    <ClCompile Include="EntityFactory\EntityDynamicTagVolume.cpp" />
    <ClCompile Include="EntityFactory\EntityEffectVolume.cpp" />
    <ClCompile Include="EntityFactory\EntityFunctional.cpp" />
    <ClCompile Include="EntityFactory\EntityNpc.cpp" />
    <ClCompile Include="EntityFactory\EntityPlayer.cpp" />
    <ClCompile Include="EntityFactory\EntitySectorController.cpp" />
    <ClCompile Include="EscortFollowingNpcQuestMissionDecorator.cpp" />
    <ClCompile Include="EscortMovingNpcQuestMissionDecorator.cpp" />
    <ClCompile Include="EventSetter.cpp" />
    <ClCompile Include="ExecutionManager.cpp" />
    <ClCompile Include="FieldKnightage\FieldKnightage.cpp" />
    <ClCompile Include="FieldKnightage\FieldKnightageManager.cpp" />
    <ClCompile Include="MonitorZone.cpp" />
    <ClCompile Include="ObjectSkillQuestMissionDecorator.cpp" />
    <ClCompile Include="ProtectNpcQuestMissionDecorator.cpp" />
    <ClCompile Include="QuestMissionDecoratorBase.cpp" />
    <ClCompile Include="SectorQuestMissionDecoratorManager.cpp" />
    <ClCompile Include="SpawnChainQuestMissionDecorator.cpp" />
    <ClCompile Include="SpawnPositionQuestMissionDecorator.cpp" />
    <ClCompile Include="SpawnQuestMissionDecorator.cpp" />
    <ClCompile Include="StatePlayerExchange.cpp" />
    <ClCompile Include="State\Bound\StateBoundHallucination.cpp" />
    <ClCompile Include="State\EffectVolume\StateEffectVolumeDead.cpp" />
    <ClCompile Include="State\StateNpcChargeSkillFire.cpp" />
    <ClCompile Include="State\StateNpcChargeWhileSkillFire.cpp" />
    <ClCompile Include="State\StateNpcProwl.cpp" />
    <ClCompile Include="State\StatePlayerBase_WebOfGod.cpp" />
    <ClCompile Include="State\statePlayerBase_LegendOfBingo.cpp" />
    <ClCompile Include="State\StatePlayerRevive.cpp" />
    <ClCompile Include="SurvivalQuestMissionDecorator.cpp" />
    <ClCompile Include="System\CaptureSystem.cpp" />
    <ClCompile Include="System\GameEventSystem.cpp" />
    <ClCompile Include="System\LegendOfBingoSystem.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\AutoUserDebuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\BoundStateCondBuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\BuffAbilityIgnoreEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\BuffChangeEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\BuffCheckEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\BuffNPCAbilityEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\ChaseBuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\CheckEntityAddBuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\CheckEntityPlusDamageBuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\ControlBuffDurationEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\CriticalAlways.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\CriticalRateFix.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\DamageDebuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\DebuffCheckBuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\DecayBuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\DefenseHpPerBuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\EntityCountCheckBuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\EssenceBuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\HitCountLogicEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\HPRecoveryControlBuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\KnightageBuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\NPCPointActionEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\PCMasterAbilityReferEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\PartyDamageBuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\RandSelectTargetBuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\SkillCastingTimeCheckEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\SystemCheckBuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\TalismanBuffAbillityEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\TransformBuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\WeaponCheckEffect.cpp" />
    <ClCompile Include="System\MarbleSystem.cpp" />
    <ClCompile Include="System\NotifierGameContents.cpp" />
    <ClCompile Include="System\SeasonSystem.cpp" />
    <ClCompile Include="System\ShopDailyItemSystem.cpp" />
    <ClCompile Include="System\ItemRewardSystem.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\AddExpEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\AddEffectBuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\AggroEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\ApplyPassiveBuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\ApplyPureBuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\BuffChangeStackEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\CrtRateBuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\DefenseBuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\DragBuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\DropShineEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\ExecuteRandomLogicEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\IgnitionEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\IgnoreImmuneBuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\MissionObjectLockEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\ProtectEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\SkillRuneBuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\SkillUseNoMpEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\SpiritLinkEffect.cpp" />
    <ClCompile Include="System\LuckyMonsterSpawnSystem.cpp" />
    <ClCompile Include="System\StatueSystem.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\AccountStorageTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ArtifactLevelTransferTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\BingoSlotExchangeTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\FreeMopupTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemArtifactTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemLimitedBannerGoodsTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemCustomizingTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemExchangeShopTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemGameEventTranscation.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemKnightageTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemMegaphoneTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemMembershipTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemNameChangeTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemOptionChangeTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemPortalStoneTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemRecoveryEntranceTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemRefineTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemSetCardTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemTalismanTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemUseTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\PetTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\PrivateStorageTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\TreasureTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\WShopItemTransaction.cpp" />
    <ClCompile Include="Transaction\MoneyProcedure.cpp" />
    <ClCompile Include="World\ColosseumProcessor.cpp" />
    <ClCompile Include="World\FieldRaidProcessor.cpp" />
    <ClCompile Include="World\GameConfig\ZoneGameConfig.cpp" />
    <ClCompile Include="World\DominionProcessor.cpp" />
    <ClCompile Include="World\InstanceSectorGroup.cpp" />
    <ClCompile Include="World\MazeInDarknessProcessor.cpp" />
    <ClCompile Include="World\MazeProcessor.cpp" />
    <ClCompile Include="World\MissionMap\AirshipDefenceProcessor.cpp" />
    <ClCompile Include="World\MissionMap\AltarVolume.cpp" />
    <ClCompile Include="World\MissionMap\ColosseumPVP33Processor.cpp" />
    <ClCompile Include="World\MissionMap\FieldTeamManager.cpp" />
    <ClCompile Include="World\MissionMap\NetherWorldProcessor.cpp" />
    <ClCompile Include="World\MissionMap\EndlessTowerStage.cpp" />
    <ClCompile Include="EntityFactory\ControlVolumeEntityfactoryImpl.cpp" />
    <ClCompile Include="EntityFactory\DroppedEntityFactoryImpl.cpp" />
    <ClCompile Include="EntityFactory\DynamicTagVolumeEntityFactoryImpl.cpp" />
    <ClCompile Include="EntityFactory\EffectVolumeEntityFactoryImpl.cpp" />
    <ClCompile Include="EntityFactory\FunctionalEntityFactoryImpl.cpp" />
    <ClCompile Include="EntityFactory\NpcEntityFactoryImpl.cpp" />
    <ClCompile Include="EntityFactory\NpcStateFactory.cpp" />
    <ClCompile Include="EntityFactory\PlayerEntityFactoryImpl.cpp" />
    <ClCompile Include="EntityFactory\SectorControllerFactoryImpl.cpp" />
    <ClCompile Include="main.cpp" />
    <ClCompile Include="NpcDebugger.cpp" />
    <ClCompile Include="Report\ZoneCastReporter.cpp" />
    <ClCompile Include="SectorCollidedHelper.cpp" />
    <ClCompile Include="SectorProcessor.cpp" />
    <ClCompile Include="Sender.cpp" />
    <ClCompile Include="StateSector.cpp" />
    <ClCompile Include="State\Bound\StateBound.cpp" />
    <ClCompile Include="State\Bound\StateBoundFlee.cpp" />
    <ClCompile Include="State\Bound\StateBoundPanic.cpp" />
    <ClCompile Include="State\Bound\StateBoundPanicChase.cpp" />
    <ClCompile Include="State\Dropped\StateDroppedItemSpawn.cpp" />
    <ClCompile Include="State\EffectVolume\StateEffectVolumeIdle.cpp" />
    <ClCompile Include="State\EffectVolume\StateEffectVolumeFire.cpp" />
    <ClCompile Include="State\Handler\PcMoveHandler.cpp" />
    <ClCompile Include="State\Handler\StateDispatcher.cpp" />
    <ClCompile Include="State\Npc\StateNpcDuelEnd.cpp" />
    <ClCompile Include="State\Npc\StateNpcDuelFight.cpp" />
    <ClCompile Include="State\Npc\StateNpcDuelMatch.cpp" />
    <ClCompile Include="State\Npc\StateNpcDuelRequest.cpp" />
    <ClCompile Include="State\StateNpc.cpp" />
    <ClCompile Include="State\StateNpcAlive.cpp" />
    <ClCompile Include="State\StateNpcBattleChase.cpp" />
    <ClCompile Include="State\StateNpcBattleMove.cpp" />
    <ClCompile Include="State\StateNpcChase.cpp" />
    <ClCompile Include="State\StateNpcDead.cpp" />
    <ClCompile Include="State\StateNpcEngage.cpp" />
    <ClCompile Include="State\StateNpcEscape.cpp" />
    <ClCompile Include="State\StateNpcFollow.cpp" />
    <ClCompile Include="State\StateNpcFreeWalk.cpp" />
    <ClCompile Include="State\StateNpcIdle.cpp" />
    <ClCompile Include="State\StateNpcIRSpawn.cpp" />
    <ClCompile Include="State\StateNpcLife.cpp" />
    <ClCompile Include="State\StateNpcMove.cpp" />
    <ClCompile Include="State\StateNpcMoveToEventually.cpp" />
    <ClCompile Include="State\StateNpcMoveToRightNow.cpp" />
    <ClCompile Include="State\StateNpcMoveWhileSkillFire.cpp" />
    <ClCompile Include="State\StateNpcOnce.cpp" />
    <ClCompile Include="State\StateNpcPatrol.cpp" />
    <ClCompile Include="State\StateNpcPeace.cpp" />
    <ClCompile Include="State\StateNpcPlayAni.cpp" />
    <ClCompile Include="State\StateNpcReturn.cpp" />
    <ClCompile Include="State\StateNpcSkillFire.cpp" />
    <ClCompile Include="State\StateNpcSpawn.cpp" />
    <ClCompile Include="State\StateNpcThink.cpp" />
    <ClCompile Include="State\StateNpcVolumeSpawn.cpp" />
    <ClCompile Include="State\StateNpcWait.cpp" />
    <ClCompile Include="State\StatePlayerAlive.cpp" />
    <ClCompile Include="State\StatePlayerBase.cpp" />
    <ClCompile Include="State\StatePlayerDead.cpp" />
    <ClCompile Include="State\StatePlayerIdle.cpp" />
    <ClCompile Include="State\StatePlayerJoin.cpp" />
    <ClCompile Include="State\StatePlayerLeave.cpp" />
    <ClCompile Include="State\StatePlayerMaking.cpp" />
    <ClCompile Include="State\StatePlayerMove.cpp" />
    <ClCompile Include="State\StatePlayerOperate.cpp" />
    <ClCompile Include="stdafx.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release_Build|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release_Japan|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release_Global|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Shipping_Global|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Shipping_Japan|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Shipping|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release_Build|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release_Japan|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release_Global|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Shipping_Global|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Shipping_Japan|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Shipping|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Create</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="System\DropSystem.cpp" />
    <ClCompile Include="System\GMCommandSystem.cpp" />
    <ClCompile Include="System\InstanceDungeonSystem.cpp" />
    <ClCompile Include="System\LogicEffectSystem.cpp" />
    <ClCompile Include="System\LogicEffectSystem\Command\ActiveEffectCommand.cpp" />
    <ClCompile Include="System\LogicEffectSystem\Command\BuffEffectCommand.cpp" />
    <ClCompile Include="System\LogicEffectSystem\Command\TriggerEffectCommand.cpp" />
    <ClCompile Include="System\LogicEffectSystem\EntityCollector.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffectProcessor.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\AbilityBuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\AddDamageEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\AnonymousBuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\AuraBuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\AuraAbilityBuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\BoundControlBuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\BoundStateBuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\BuffStackEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\CancelBuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\CleanseEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\CombineEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\ConvertAbilityBuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\CoolingEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\CraftRecipeEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\DamageControlBuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\DamageEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\DeathBuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\DisappearEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\ExchangeItemEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\ExecuteBuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\ExecuteBuffFireEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\ImmuneBuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\KnockBackEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\LongDisplacementCall.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\PassMoveEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\PullingEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\RechargeObjectEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\RecoveryDurability.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\RecoveryEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\RegisterPetEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\RegistSkillBuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\RemoveSocketJewelEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\ReviveEntityEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\RidingBuffEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\SkillCustomEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\SummonEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\TeleportEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\ThrowTargetEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\TriggerEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicEffect\UnusualCasterSkillCastEffect.cpp" />
    <ClCompile Include="System\LogicEffectSystem\LogicResultProcessor.cpp" />
    <ClCompile Include="System\QuestEvent.cpp" />
    <ClCompile Include="System\QuestSystem.cpp" />
    <ClCompile Include="System\SpawnSystem.cpp" />
    <ClCompile Include="System\PortalSystem.cpp" />
    <ClCompile Include="System\ReviveSystem.cpp" />
    <ClCompile Include="System\TagValueCheckSystem.cpp" />
    <ClCompile Include="System\TransactionSystem.cpp" />
    <ClCompile Include="Test\TestAiServer.cpp" />
    <ClCompile Include="Transaction\DBTransaction.cpp" />
    <ClCompile Include="Transaction\ItemProcedure.cpp" />
    <ClCompile Include="Transaction\ItemTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemBaseTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemBuyTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemChangeSkillRuneTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemChangeTextureTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemDurabilityTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemEnchantTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemEquipExchangeTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemEquipTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemExchangeTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemExtractTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemGambleTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemMailTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemMakingTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemMoveTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemOverlapTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemPetTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemPushTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemRebuyTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemRemoveTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemSellTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemSendTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemSeperateTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemSocketCreateTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemSkillRuneTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemSocketTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemSortTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemStorageTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemSupplyTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemTradeTransaction.cpp" />
    <ClCompile Include="Transaction\ItemTransaction\ItemUnequipTransaction.cpp" />
    <ClCompile Include="View\ViewDroppedItem.cpp" />
    <ClCompile Include="View\ViewEntity.cpp" />
    <ClCompile Include="View\ViewNpc.cpp" />
    <ClCompile Include="View\ViewPlayer.cpp" />
    <ClCompile Include="View\ViewPlayerInventory.cpp" />
    <ClCompile Include="View\ViewPosition.cpp" />
    <ClCompile Include="View\ViewShop.cpp" />
    <ClCompile Include="World\BossProcessor.cpp" />
    <ClCompile Include="World\ExplorerProcessor.cpp" />
    <ClCompile Include="World\Gcell.cpp" />
    <ClCompile Include="World\Grid.cpp" />
    <ClCompile Include="World\Guild\GuildhallProcessor.cpp" />
    <ClCompile Include="World\Helper\InitializeSpawner.cpp" />
    <ClCompile Include="World\Helper\MonsterWave.cpp" />
    <ClCompile Include="World\MapEventScript.cpp" />
    <ClCompile Include="World\MapEventScriptData.cpp" />
    <ClCompile Include="World\MissionMap\AltarOfElementsProcessor.cpp" />
    <ClCompile Include="World\MissionMap\BloodCastleProcessor.cpp" />
    <ClCompile Include="World\MissionMap\ChaosCastleProcessor.cpp" />
    <ClCompile Include="World\MissionMap\DeathMatchProcessor.cpp" />
    <ClCompile Include="World\MissionMap\EloCalcurator.cpp" />
    <ClCompile Include="World\MissionMap\EndlessTowerProcessor.cpp" />
    <ClCompile Include="World\MissionMap\DeathMatchItemReward.cpp" />
    <ClCompile Include="World\MissionMap\MissionMapReward.cpp" />
    <ClCompile Include="World\MissionMap\PVPChaosCastleProcessor.cpp" />
    <ClCompile Include="World\MissionMap\PVPFieldProcessor.cpp" />
    <ClCompile Include="World\MissionMap\PVPMissionMapProcessorBase.cpp" />
    <ClCompile Include="World\MissionMap\PVPTeam.cpp" />
    <ClCompile Include="World\MissionMap\PVPTeamManager.cpp" />
    <ClCompile Include="World\MissionMap\TeamProcessorHelper.cpp" />
    <ClCompile Include="World\MissionMap\TournamentAltarOfElementsProcessor.cpp" />
    <ClCompile Include="World\MissionMap\BattleFieldInTheSkyProcessor.cpp" />
    <ClCompile Include="World\MissionMap\TowerOfDawnProcessor.cpp" />
    <ClCompile Include="World\OhrdorSewerageProcessor.cpp" />
    <ClCompile Include="World\PerformanceLodDirector.cpp" />
    <ClCompile Include="World\PVEMissionMap\WebOfGodProcessor.cpp" />
    <ClCompile Include="World\PVEMissionMap\WebOfGodState.cpp" />
    <ClCompile Include="World\PVEMissionMap\WebOfGodStateWaves.cpp" />
    <ClCompile Include="World\Script\DynamicTagvolumeScript.cpp" />
    <ClCompile Include="World\Script\IRTouchVolumeScript.cpp" />
    <ClCompile Include="World\Script\ObjectVolumeScript.cpp" />
    <ClCompile Include="World\Script\PathVolumeScript.cpp" />
    <ClCompile Include="World\Script\QuestHelpVolumeScript.cpp" />
    <ClCompile Include="World\Script\QuestVolumeScript.cpp" />
    <ClCompile Include="World\Script\VolumeScript.cpp" />
    <ClCompile Include="World\Script\VolumeSpawnScript.cpp" />
    <ClCompile Include="World\Script\ZoneLevelScriptTable.cpp" />
    <ClCompile Include="World\Sector.cpp" />
    <ClCompile Include="World\SectorGrid.cpp" />
    <ClCompile Include="World\SectorGridManager.cpp" />
    <ClCompile Include="World\SectorInitializer.cpp" />
    <ClCompile Include="World\SectorRunner.cpp" />
    <ClCompile Include="World\SectorSequencer.cpp" />
    <ClCompile Include="World\SectorSystem\FieldPointSector.cpp" />
    <ClCompile Include="World\SectorSystem\SharedQuestSystem.cpp" />
    <ClCompile Include="World\ZoneHandler.cpp" />
    <ClCompile Include="World\ZoneHandlerRegEvent.cpp" />
    <ClCompile Include="ZoneServer.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="Action\ActionAchievement.h" />
    <ClInclude Include="Action\ActionAggro.h" />
    <ClInclude Include="Action\ActionArtifact.h" />
    <ClInclude Include="Action\ActionAutoHackChecker.h" />
    <ClInclude Include="Action\ActionBoundControl.h" />
    <ClInclude Include="Action\ActionBuff.h" />
    <ClInclude Include="Action\ActionCommon\Action4Zone.h" />
    <ClInclude Include="Action\ActionCommon\ActionAbility.h" />
    <ClInclude Include="Action\ActionCommon\ActionCognition.h" />
    <ClInclude Include="Action\ActionCommon\ActionSkillControl.h" />
    <ClInclude Include="Action\ActionDailyMission.h" />
    <ClInclude Include="Action\ActionDroppedItem\ActionDroppedCognition.h" />
    <ClInclude Include="Action\ActionDynamicTagVolume\ActionDynamicTagVolumeCognition.h" />
    <ClInclude Include="Action\ActionEffectVolume\ActionEffectVolume.h" />
    <ClInclude Include="Action\ActionEffectVolume\ActionEffectVolumeCognition.h" />
    <ClInclude Include="Action\ActionEloPoint.h" />
    <ClInclude Include="Action\ActionEventProxy.h" />
    <ClInclude Include="Action\ActionFieldPoint.h" />
    <ClInclude Include="Action\ActionNpcBlackMarket.h" />
    <ClInclude Include="Action\ActionNpcColosseum.h" />
    <ClInclude Include="Action\ActionNpcCooperation.h" />
    <ClInclude Include="Action\ActionNpc\ActionNpcCognition.h" />
    <ClInclude Include="Action\ActionNpc\ActionNpcCommonTrigger.h" />
    <ClInclude Include="Action\ActionNpc\ActionNpcOperate.h" />
    <ClInclude Include="Action\ActionPassivity.h" />
    <ClInclude Include="Action\ActionDurability.h" />
    <ClInclude Include="Action\ActionDynamicTagVolume.h" />
    <ClInclude Include="Action\ActionExpiryItem.h" />
    <ClInclude Include="Action\ActionFunctional.h" />
    <ClInclude Include="Action\ActionMissionDungeon.h" />
    <ClInclude Include="Action\ActionMissionHero.h" />
    <ClInclude Include="Action\ActionNpcAggro.h" />
    <ClInclude Include="Action\ActionDamage.h" />
    <ClInclude Include="Action\ActionDroppedItem.h" />
    <ClInclude Include="Action\ActionEntity.h" />
    <ClInclude Include="Action\ActionNpc.h" />
    <ClInclude Include="Action\ActionNpcDamage.h" />
    <ClInclude Include="Action\ActionNpcMove.h" />
    <ClInclude Include="Action\ActionNpcPatrol.h" />
    <ClInclude Include="Action\ActionNpcScript.h" />
    <ClInclude Include="Action\ActionNpcSector.h" />
    <ClInclude Include="Action\ActionNpcSkill.h" />
    <ClInclude Include="Action\ActionNpc\ActionNpcAbility.h" />
    <ClInclude Include="Action\ActionParty.h" />
    <ClInclude Include="Action\ActionPetManage.h" />
    <ClInclude Include="Action\ActionPlayer.h" />
    <ClInclude Include="Action\ActionPlayerAggro.h" />
    <ClInclude Include="Action\ActionPlayerBlackMarket.h" />
    <ClInclude Include="Action\ActionPlayerDamage.h" />
    <ClInclude Include="Action\ActionPlayerGloryRank.h" />
    <ClInclude Include="Action\ActionPlayerInventory.h" />
    <ClInclude Include="Action\ActionPlayerItemTrade.h" />
    <ClInclude Include="Action\ActionPlayerLoad.h" />
    <ClInclude Include="Action\ActionPlayerMailBox.h" />
    <ClInclude Include="Action\ActionPlayerMaking.h" />
    <ClInclude Include="Action\ActionPlayerScript.h" />
    <ClInclude Include="Action\ActionPlayerSpeedHack.h" />
    <ClInclude Include="Action\ActionPlayerStorage.h" />
    <ClInclude Include="Action\ActionPlayer\ActionContact.h" />
    <ClInclude Include="Action\ActionPlayer\ActionPlayerAbility.h" />
    <ClInclude Include="Action\ActionPlayer\ActionPlayerAccountLevel.h" />
    <ClInclude Include="Action\ActionPlayer\ActionPlayerChaosCastleHistory.h" />
    <ClInclude Include="Action\ActionPlayer\ActionPlayerCognition.h" />
    <ClInclude Include="Action\ActionPlayer\ActionJoin.h" />
    <ClInclude Include="Action\ActionPlayer\ActionPlayerColosseum.h" />
    <ClInclude Include="Action\ActionPlayer\ActionPlayerCombatPower.h" />
    <ClInclude Include="Action\ActionPlayer\ActionPlayerDamageMeter.h" />
    <ClInclude Include="Action\ActionPlayer\ActionPlayerEscape.h" />
    <ClInclude Include="Action\ActionPlayer\ActionPlayerEvent.h" />
    <ClInclude Include="Action\ActionPlayer\ActionPlayerEventBuff.h" />
    <ClInclude Include="Action\ActionPlayer\ActionPlayerEventInventory.h" />
    <ClInclude Include="Action\ActionPlayer\ActionPlayerExchange.h" />
    <ClInclude Include="Action\ActionPlayer\ActionPlayerGuideSystem.h" />
    <ClInclude Include="Action\ActionPlayer\ActionPlayerKillCombo.h" />
    <ClInclude Include="Action\ActionPlayer\ActionPlayerKnightage.h" />
    <ClInclude Include="Action\ActionPlayer\ActionPlayerKnightageStorage.h" />
    <ClInclude Include="Action\ActionPlayer\ActionPlayerLimitedBannerGoods.h" />
    <ClInclude Include="Action\ActionPlayer\ActionPlayerMarble.h" />
    <ClInclude Include="Action\ActionPlayer\ActionPlayerMembership.h" />
    <ClInclude Include="Action\ActionPlayer\ActionPlayerMissionMapTier.h" />
    <ClInclude Include="Action\ActionPlayer\ActionPlayerNoticePopUp.h" />
    <ClInclude Include="Action\ActionPlayer\ActionPlayerReturnUser.h" />
    <ClInclude Include="Action\ActionPlayer\ActionPlayerRevive.h" />
    <ClInclude Include="Action\ActionPlayer\ActionPlayerSector.h" />
    <ClInclude Include="Action\ActionPlayer\ActionPlayerSetCard.h" />
    <ClInclude Include="Action\ActionPlayer\ActionPlayerSprout.h" />
    <ClInclude Include="Action\ActionPlayer\ActionPlayerSurvey.h" />
    <ClInclude Include="Action\ActionPlayer\ActionPlayerTalisman.h" />
    <ClInclude Include="Action\ActionPlayer\ActionPlayerTalismanUnit.h" />
    <ClInclude Include="Action\ActionPlayer\ActionQuickSlot.h" />
    <ClInclude Include="Action\ActionPlayer\ActionPlayerSeason.h" />
    <ClInclude Include="Action\ActionPlayer\ActionSkillManage.h" />
    <ClInclude Include="Action\ActionPlayer\ActionSkillMastery.h" />
    <ClInclude Include="Action\ActionPlayer\ActionSocket.h" />
    <ClInclude Include="Action\ActionPlayer\ActionSoulSkillManage.h" />
    <ClInclude Include="Action\ActionPortal.h" />
    <ClInclude Include="Action\ActionQuest.h" />
    <ClInclude Include="Action\ActionSector.h" />
    <ClInclude Include="Action\ActionSectorController.h" />
    <ClInclude Include="Action\ActionSetItemAbility.h" />
    <ClInclude Include="Action\ActionSkillChanger.h" />
    <ClInclude Include="Action\ActionSkillRune.h" />
    <ClInclude Include="Action\ActionSuppliedItemEachPortal.h" />
    <ClInclude Include="Action\ActionSuppliedSkillEachPortal.h" />
    <ClInclude Include="Action\ActionTutorial.h" />
    <ClInclude Include="Action\ActionUseItem.h" />
    <ClInclude Include="Action\ActionVolumeSpawn.h" />
    <ClInclude Include="Action\SectorItemUseChecker.h" />
    <ClInclude Include="AIScriptLogic\LuaAllocator.h" />
    <ClInclude Include="AIScriptLogic\LuaMemoryDebugger.h" />
    <ClInclude Include="AIScriptLogic\LuaStateCache.h" />
    <ClInclude Include="AIScriptLogic\LuaStatePool.h" />
    <ClInclude Include="AIScriptLogic\LuaUtil.h" />
    <ClInclude Include="AIScriptLogic\SectorLuaScript.h" />
    <ClInclude Include="Attribute\AttributeAchievement.h" />
    <ClInclude Include="Attribute\AttributeDroppedItem.h" />
    <ClInclude Include="Attribute\AttributeDroppedMagicBox.h" />
    <ClInclude Include="Attribute\AttributeEffectVolume.h" />
    <ClInclude Include="Attribute\AttributeEntity.h" />
    <ClInclude Include="Attribute\AttributeFunctional.h" />
    <ClInclude Include="Attribute\AttributeIRSpawn.h" />
    <ClInclude Include="Attribute\AttributeMapObject.h" />
    <ClInclude Include="Attribute\AttributeMarble.h" />
    <ClInclude Include="Attribute\AttributeNpc.h" />
    <ClInclude Include="Attribute\AttributeNpcScript.h" />
    <ClInclude Include="Attribute\AttributeParty.h" />
    <ClInclude Include="Attribute\AttributePlayer.h" />
    <ClInclude Include="Attribute\AttributePortal.h" />
    <ClInclude Include="Attribute\AttributePosition.h" />
    <ClInclude Include="Attribute\AttributeSkill.h" />
    <ClInclude Include="Attribute\AttributeVolumeSpawn.h" />
    <ClInclude Include="Common.h" />
    <ClInclude Include="Common\CommonCondition.h" />
    <ClInclude Include="Common\CommonTrigger.h" />
    <ClInclude Include="Common\CommonTriggerAction.h" />
    <ClInclude Include="Common\Constants\ConstantsMissionMap.h" />
    <ClInclude Include="Common\Constants\ConstantsSAVE.h" />
    <ClInclude Include="Common\Constants\ConstantsVolume.h" />
    <ClInclude Include="Common\Constants\ConstantsWorld.h" />
    <ClInclude Include="Common\NpcConditionManager.h" />
    <ClInclude Include="Common\Structures\StructuresAbility.h" />
    <ClInclude Include="Common\Structures\StructuresLogicEffect.h" />
    <ClInclude Include="Common\Structures\StructuresMissionMap.h" />
    <ClInclude Include="Common\Structures\StructuresNPC.h" />
    <ClInclude Include="Common\Structures\StructuresSkill.h" />
    <ClInclude Include="Common\Structures\StructuresWorld.h" />
    <ClInclude Include="Configure.h" />
    <ClInclude Include="ContentsDeactivator.h" />
    <ClInclude Include="Element\Ability\AbilityCalculator.h" />
    <ClInclude Include="Element\Ability\NpcAbilityCalculator.h" />
    <ClInclude Include="Element\Achievement\Achievement.h" />
    <ClInclude Include="Element\Achievement\AchievementConditionChecker.h" />
    <ClInclude Include="Element\Achievement\ConditionGroup.h" />
    <ClInclude Include="Element\Achievement\AchievementConditionGroup.h" />
    <ClInclude Include="Element\Achievement\AchievementEvent.h" />
    <ClInclude Include="Element\Achievement\GuideCategory.h" />
    <ClInclude Include="Element\Achievement\GuideDivisionConditionGroup.h" />
    <ClInclude Include="Element\Achievement\GuideTrigger.h" />
    <ClInclude Include="Element\Achievement\LimitedBannerEvent.h" />
    <ClInclude Include="Element\Item\EquipItemAbilityModifier.h" />
    <ClInclude Include="Element\Item\ExchangeShopInfo.h" />
    <ClInclude Include="Element\Item\InvenAccountStorage.h" />
    <ClInclude Include="Element\Item\InvenEquip.h" />
    <ClInclude Include="Element\Item\InvenKnightageStorage.h" />
    <ClInclude Include="Element\Item\InvenNonSlotable.h" />
    <ClInclude Include="Element\Item\InvenPrivateStorage.h" />
    <ClInclude Include="Element\Item\InvenSlot.h" />
    <ClInclude Include="Element\Item\InvenSlotable.h" />
    <ClInclude Include="Element\Item\Item.h" />
    <ClInclude Include="Element\Item\ItemAmplifaction.h" />
    <ClInclude Include="Element\Item\ItemBag.h" />
    <ClInclude Include="Element\Item\ItemCube.h" />
    <ClInclude Include="Element\Item\ItemEnchant.h" />
    <ClInclude Include="Element\Item\ItemFactory.h" />
    <ClInclude Include="Element\Item\ItemFinder.h" />
    <ClInclude Include="Element\Item\RueryTreasure.h" />
    <ClInclude Include="Element\Item\ItemRune.h" />
    <ClInclude Include="Element\Item\TradeItem.h" />
    <ClInclude Include="Element\Item\UseItem.h" />
    <ClInclude Include="Element\Item\UseItemLimit.h" />
    <ClInclude Include="Element\Mail\Mail.h" />
    <ClInclude Include="Element\Mail\MailBox.h" />
    <ClInclude Include="Element\Pet\Pet.h" />
    <ClInclude Include="Element\Pet\PetManager.h" />
    <ClInclude Include="Element\Quest\CheckMissionCompleteChecker.h" />
    <ClInclude Include="Element\Season\SeasonMission.h" />
    <ClInclude Include="Element\Season\SeasonMissionEvent.h" />
    <ClInclude Include="Element\Skill\PassiveCondition.h" />
    <ClInclude Include="Element\Skill\Passive\PassiveCondCastSkill.h" />
    <ClInclude Include="Element\Skill\Passive\PassiveCondCheckRangeEnemy.h" />
    <ClInclude Include="Element\Skill\Passive\PassiveCondEssenceChange.h" />
    <ClInclude Include="Element\Skill\Passive\PassiveCondHasBuff.h" />
    <ClInclude Include="Element\Skill\Passive\PassiveCondHitMe.h" />
    <ClInclude Include="Element\Skill\Passive\PassiveCondGetProperty.h" />
    <ClInclude Include="Element\Skill\Passive\PassiveCondHitTarget.h" />
    <ClInclude Include="Element\Skill\Passive\PassiveCondAbilityUpdate.h" />
    <ClInclude Include="Element\Skill\Passive\PassiveCondLevelUp.h" />
    <ClInclude Include="Element\Skill\Passive\PassiveCondNonBattleState.h" />
    <ClInclude Include="Element\Skill\Passive\PassiveCondQuestComplete.h" />
    <ClInclude Include="Element\Skill\Passive\PassiveCondRiding.h" />
    <ClInclude Include="Element\Skill\Passive\PassiveCondShieldBlock.h" />
    <ClInclude Include="Element\Skill\Passivity.h" />
    <ClInclude Include="Element\Skill\Skill.h" />
    <ClInclude Include="Element\Skill\SkillCastChecker.h" />
    <ClInclude Include="Element\Skill\SkillControl.h" />
    <ClInclude Include="Element\Skill\SkillCoolLineManager.h" />
    <ClInclude Include="Element\Skill\SpecialSkill.h" />
    <ClInclude Include="Element\Skill\TimeCorrector.h" />
    <ClInclude Include="Element\Skill\SkillLinker.h" />
    <ClInclude Include="Element\Skill\SpecialBlock.h" />
    <ClInclude Include="Element\Soul\SoulInfo.h" />
    <ClInclude Include="Element\Survey\Survey.h" />
    <ClInclude Include="Element\Survey\SurveyEvent.h" />
    <ClInclude Include="EntityFactory\Entity4Zone.h" />
    <ClInclude Include="EntityFactory\EntityCommon.h" />
    <ClInclude Include="EntityFactory\EntityControlVolume.h" />
    <ClInclude Include="EntityFactory\EntityDropped.h" />
    <ClInclude Include="EntityFactory\EntityDynamicTagVolume.h" />
    <ClInclude Include="EntityFactory\EntityEffectVolume.h" />
    <ClInclude Include="EntityFactory\EntityFunctional.h" />
    <ClInclude Include="EntityFactory\EntityNpc.h" />
    <ClInclude Include="EntityFactory\EntityPlayer.h" />
    <ClInclude Include="EntityFactory\EntitySectorController.h" />
    <ClInclude Include="EscortFollowingNpcQuestMissionDecorator.h" />
    <ClInclude Include="EscortMovingNpcQuestMissionDecorator.h" />
    <ClInclude Include="EventSetter.h" />
    <ClInclude Include="ExecutionManager.h" />
    <ClInclude Include="FieldKnightage\FieldKnightage.h" />
    <ClInclude Include="FieldKnightage\FieldKnightageManager.h" />
    <ClInclude Include="LoopbackEvent\EventLoopback.h" />
    <ClInclude Include="MonitorZone.h" />
    <ClInclude Include="ObjectSkillQuestMissionDecorator.h" />
    <ClInclude Include="ProtectNpcQuestMissionDecorator.h" />
    <ClInclude Include="QuestMissionDecoratorBase.h" />
    <ClInclude Include="SectorQuestMissionDecoratorManager.h" />
    <ClInclude Include="SpawnChainQuestMissionDecorator.h" />
    <ClInclude Include="SpawnPositionQuestMissionDecorator.h" />
    <ClInclude Include="SpawnQuestMissionDecorator.h" />
    <ClInclude Include="State\Bound\StateBoundHallucination.h" />
    <ClInclude Include="State\EffectVolume\StateEffectVolumeDead.h" />
    <ClInclude Include="State\State4Zone.h" />
    <ClInclude Include="State\StateNpcChargeSkillFire.h" />
    <ClInclude Include="State\StateNpcChargeWhileSkillFire.h" />
    <ClInclude Include="State\StateNpcProwl.h" />
    <ClInclude Include="State\StatePlayerExchange.h" />
    <ClInclude Include="State\StatePlayerRevive.h" />
    <ClInclude Include="SurvivalQuestMissionDecorator.h" />
    <ClInclude Include="System\CaptureSystem.h" />
    <ClInclude Include="System\GameEventSystem.h" />
    <ClInclude Include="System\LegendOfBingoSytem.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\AutoUserDebuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\BoundStateCondBuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\BuffAbilityIgnoreEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\BuffChangeEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\BuffCheckEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\BuffNPCAbilityEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\ChaseBuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\CheckEntityAddBuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\CheckEntityPlusDamageBuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\ControlBuffDurationEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\CriticalAlways.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\CriticalRateFix.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\DamageDebuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\DebuffCheckBuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\DecayBuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\DefenseHpPerBuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\EntityCountCheckBuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\EssenceBuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\HitCountLogicEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\HPRecoveryControlBuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\KnightageBuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\NPCPointActionEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\PCMasterAbilityReferEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\PartyDamageBuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\RandSelectTargetBuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\SkillCastingTimeCheckEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\SystemCheckBuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\TalismanBuffAbillityEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\TransformBuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\WeaponCheckEffect.h" />
    <ClInclude Include="System\MarbleSystem.h" />
    <ClInclude Include="System\NotifierGameContents.h" />
    <ClInclude Include="System\SeasonSystem.h" />
    <ClInclude Include="System\ShopDailyItemSystem.h" />
    <ClInclude Include="System\ItemRewardSystem.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\AddExpEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\AddEffectBuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\AggroEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\ApplyPassiveBuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\ApplyPureBuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\BuffChangeStackEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\CrtRateBuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\DefenseBuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\DragBuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\DropShineEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\ExecuteRandomLogicEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\IgnitionEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\IgnoreImmuneBuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\MissionObjectLockEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\ProtectEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\SkillRuneBuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\SkillUseNoMpEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\SpiritLinkEffect.h" />
    <ClInclude Include="System\LuckyMonsterSpawnSystem.h" />
    <ClInclude Include="System\StatueSystem.h" />
    <ClInclude Include="Transaction\ItemTransaction\AccountStorageTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ArtifactLevelTransferTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\BingoSlotExchangeTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\FreeMopupTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemArtifactTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemLimitedBannerGoodsTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemCustomizingTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemExchangeShopTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemGameEventTranscation.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemKnightageTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemMegaphoneTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemMembershipTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemNameChangeTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemOptionChangeTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemPortalStoneTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemRecoveryEntranceTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemRefineTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemSetCardTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemTalismanTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemUseTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\PetTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\PrivateStorageTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\TreasureTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\WShopItemTransaction.h" />
    <ClInclude Include="Transaction\MoneyProcedure.h" />
    <ClInclude Include="World\ColosseumProcessor.h" />
    <ClInclude Include="World\FieldRaidProcessor.h" />
    <ClInclude Include="World\GameConfig\ZoneGameConfig.h" />
    <ClInclude Include="World\DominionProcessor.h" />
    <ClInclude Include="World\InstanceSectorGroup.h" />
    <ClInclude Include="World\MazeInDarknessProcessor.h" />
    <ClInclude Include="World\MazeProcessor.h" />
    <ClInclude Include="World\MissionMap\AirshipDefenceProcessor.h" />
    <ClInclude Include="World\MissionMap\AltarVolume.h" />
    <ClInclude Include="World\MissionMap\ColosseumPVP33Processor.h" />
    <ClInclude Include="World\MissionMap\FieldTeamManager.h" />
    <ClInclude Include="World\MissionMap\NetherWorldProcessor.h" />
    <ClInclude Include="World\MissionMap\EndlessTowerStage.h" />
    <ClInclude Include="EntityFactory\ControlVolumeEntityfactoryImpl.h" />
    <ClInclude Include="EntityFactory\DroppedEntityFactoryImpl.h" />
    <ClInclude Include="EntityFactory\DynamicTagVolumeEntityFactoryImpl.h" />
    <ClInclude Include="EntityFactory\EffectVolumeEntityFactoryImpl.h" />
    <ClInclude Include="EntityFactory\FunctionalEntityFactoryImpl.h" />
    <ClInclude Include="EntityFactory\NpcEntityFactoryImpl.h" />
    <ClInclude Include="EntityFactory\NpcStateFactory.h" />
    <ClInclude Include="EntityFactory\PlayerEntityFactoryImpl.h" />
    <ClInclude Include="EntityFactory\SectorControllerFactoryImpl.h" />
    <ClInclude Include="NpcDebugger.h" />
    <ClInclude Include="Report\ZoneCastReporter.h" />
    <ClInclude Include="SectorCollidedHelper.h" />
    <ClInclude Include="SectorProcessor.h" />
    <ClInclude Include="Sender.h" />
    <ClInclude Include="State\Bound\StateBound.h" />
    <ClInclude Include="State\Bound\StateBoundFlee.h" />
    <ClInclude Include="State\Bound\StateBoundPanic.h" />
    <ClInclude Include="State\Bound\StateBoundPanicChase.h" />
    <ClInclude Include="State\Dropped\StateDroppedItemSpawn.h" />
    <ClInclude Include="State\EffectVolume\StateEffectVolumeIdle.h" />
    <ClInclude Include="State\EffectVolume\StateEffectVolumeFire.h" />
    <ClInclude Include="State\Handler\PcMoveHandler.h" />
    <ClInclude Include="State\Handler\StateDispatcher.h" />
    <ClInclude Include="State\Npc\StateNpcDuel.h" />
    <ClInclude Include="State\Npc\StateNpcDuelEnd.h" />
    <ClInclude Include="State\Npc\StateNpcDuelFight.h" />
    <ClInclude Include="State\Npc\StateNpcDuelMatch.h" />
    <ClInclude Include="State\Npc\StateNpcDuelRequest.h" />
    <ClInclude Include="State\StateMagicStone.h" />
    <ClInclude Include="State\StateNpc.h" />
    <ClInclude Include="State\StateNpcAlive.h" />
    <ClInclude Include="State\StateNpcBattleChase.h" />
    <ClInclude Include="State\StateNpcBattleMove.h" />
    <ClInclude Include="State\StateNpcChase.h" />
    <ClInclude Include="State\StateNpcDead.h" />
    <ClInclude Include="State\StateNpcEngage.h" />
    <ClInclude Include="State\StateNpcEscape.h" />
    <ClInclude Include="State\StateNpcFollow.h" />
    <ClInclude Include="State\StateNpcFreeWalk.h" />
    <ClInclude Include="State\StateNpcIdle.h" />
    <ClInclude Include="State\StateNpcIRSpawn.h" />
    <ClInclude Include="State\StateNpcLife.h" />
    <ClInclude Include="State\StateNpcMove.h" />
    <ClInclude Include="State\StateNpcMoveToEventually.h" />
    <ClInclude Include="State\StateNpcMoveToRightNow.h" />
    <ClInclude Include="State\StateNpcMoveWhileSkillFire.h" />
    <ClInclude Include="State\StateNpcOnce.h" />
    <ClInclude Include="State\StateNpcPatrol.h" />
    <ClInclude Include="State\StateNpcPeace.h" />
    <ClInclude Include="State\StateNpcPlayAni.h" />
    <ClInclude Include="State\StateNpcReturn.h" />
    <ClInclude Include="State\StateNpcSkillFire.h" />
    <ClInclude Include="State\StateNpcSpawn.h" />
    <ClInclude Include="State\StateNpcThink.h" />
    <ClInclude Include="State\StateNpcVolumeSpawn.h" />
    <ClInclude Include="State\StateNpcWait.h" />
    <ClInclude Include="State\StatePlayerAlive.h" />
    <ClInclude Include="State\StatePlayerBase.h" />
    <ClInclude Include="State\StatePlayerDead.h" />
    <ClInclude Include="State\StatePlayerIdle.h" />
    <ClInclude Include="State\StatePlayerJoin.h" />
    <ClInclude Include="State\StatePlayerLeave.h" />
    <ClInclude Include="State\StatePlayerMaking.h" />
    <ClInclude Include="State\StatePlayerMove.h" />
    <ClInclude Include="State\StatePlayerOperate.h" />
    <ClInclude Include="State\StateSector.h" />
    <ClInclude Include="stdafx.h" />
    <ClInclude Include="System\DropSystem.h" />
    <ClInclude Include="System\GameSystems.h" />
    <ClInclude Include="System\GMCommandSystem.h" />
    <ClInclude Include="System\InstanceDungeonSystem.h" />
    <ClInclude Include="System\LogicEffectSystem.h" />
    <ClInclude Include="System\LogicEffectSystem\Command\ActiveEffectCommand.h" />
    <ClInclude Include="System\LogicEffectSystem\Command\BuffEffectCommand.h" />
    <ClInclude Include="System\LogicEffectSystem\Command\TriggerEffectCommand.h" />
    <ClInclude Include="System\LogicEffectSystem\EntityCollector.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffectProcessor.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\AbilityBuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\AddDamageEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\AnonymousBuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\AuraBuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\AuraAbilityBuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\BoundControlBuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\BoundStateBuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\BuffStackEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\CancelBuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\CleanseEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\CombineEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\ConvertAbilityBuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\CoolingEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\CraftRecipeEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\DamageControlBuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\DamageEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\DeathBuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\DisappearEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\ExchangeItemEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\ExecuteBuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\ExecuteBuffFireEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\ImmuneBuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\KnockBackEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\LongDisplacementCall.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\PassMoveEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\PullingEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\RechargeObjectEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\RecoveryDurability.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\RecoveryEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\RegisterPetEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\RegistSkillBuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\RemoveSocketJewelEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\ReviveEntityEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\RidingBuffEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\SkillCustomEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\SummonEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\TeleportEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\ThrowTargetEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\TriggerEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicEffect\UnusualCasterSkillCastEffect.h" />
    <ClInclude Include="System\LogicEffectSystem\LogicResultProcessor.h" />
    <ClInclude Include="System\QuestEvent.h" />
    <ClInclude Include="System\QuestSystem.h" />
    <ClInclude Include="System\SpawnSystem.h" />
    <ClInclude Include="System\PortalSystem.h" />
    <ClInclude Include="System\ReviveSystem.h" />
    <ClInclude Include="System\TagValueCheckSystem.h" />
    <ClInclude Include="System\TransactionSystem.h" />
    <ClInclude Include="targetver.h" />
    <ClInclude Include="Test\TestAiServer.h" />
    <ClInclude Include="Test\TestSample.h" />
    <ClInclude Include="Transaction\DBProcedure.h" />
    <ClInclude Include="Transaction\DBTransaction.h" />
    <ClInclude Include="Transaction\ItemProcedure.h" />
    <ClInclude Include="Transaction\ItemTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemBaseTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemBuyTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemChangeSkillRuneTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemChangeTextureTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemDurabilityTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemEnchantTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemEquipExchangeTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemEquipTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemExchangeTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemExtractTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemGambleTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemMailTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemMakingTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemMoveTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemOverlapTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemPetTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemPushTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemRebuyTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemRemoveTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemSellTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemSendTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemSeperateTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemSocketCreateTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemSkillRuneTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemSocketTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemSortTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemStorageTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemSupplyTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemTradeTransaction.h" />
    <ClInclude Include="Transaction\ItemTransaction\ItemUnequipTransaction.h" />
    <ClInclude Include="View\ViewDroppedItem.h" />
    <ClInclude Include="View\ViewEntity.h" />
    <ClInclude Include="View\ViewNpc.h" />
    <ClInclude Include="View\ViewPlayer.h" />
    <ClInclude Include="View\ViewPlayerInventory.h" />
    <ClInclude Include="View\ViewPosition.h" />
    <ClInclude Include="View\ViewShop.h" />
    <ClInclude Include="World\BossProcessor.h" />
    <ClInclude Include="World\ExplorerProcessor.h" />
    <ClInclude Include="World\Gcell.h" />
    <ClInclude Include="World\Grid.h" />
    <ClInclude Include="World\Guild\GuildhallProcessor.h" />
    <ClInclude Include="World\Helper\InitializeSpawner.h" />
    <ClInclude Include="World\Helper\MonsterWave.h" />
    <ClInclude Include="World\MapEventScript.h" />
    <ClInclude Include="World\MapEventScriptData.h" />
    <ClInclude Include="World\MissionMap\AltarOfElementsProcessor.h" />
    <ClInclude Include="World\MissionMap\BloodCastleProcessor.h" />
    <ClInclude Include="World\MissionMap\ChaosCastleProcessor.h" />
    <ClInclude Include="World\MissionMap\DeathMatchProcessor.h" />
    <ClInclude Include="World\MissionMap\EloCalcurator.h" />
    <ClInclude Include="World\MissionMap\EndlessTowerProcessor.h" />
    <ClInclude Include="World\MissionMap\DeathMatchItemReward.h" />
    <ClInclude Include="World\MissionMap\MissionMapReward.h" />
    <ClInclude Include="World\MissionMap\MissionMapScheduler.h" />
    <ClInclude Include="World\MissionMap\PVPChaosCastleProcessor.h" />
    <ClInclude Include="World\MissionMap\PVPFieldProcessor.h" />
    <ClInclude Include="World\MissionMap\PVPMissionMapProcessorBase.h" />
    <ClInclude Include="World\MissionMap\PVPTeam.h" />
    <ClInclude Include="World\MissionMap\PVPTeamManager.h" />
    <ClInclude Include="World\MissionMap\TeamProcessorHelper.h" />
    <ClInclude Include="World\MissionMap\TournamentAltarOfElementsProcessor.h" />
    <ClInclude Include="World\MissionMap\BattleFieldInTheSkyProcessor.h" />
    <ClInclude Include="World\MissionMap\TowerOfDawnProcessor.h" />
    <ClInclude Include="World\OhrdorSewerageProcessor.h" />
    <ClInclude Include="World\PerformanceLodDirector.h" />
    <ClInclude Include="World\PVEMissionMap\WebOfGodProcessor.h" />
    <ClInclude Include="World\PVEMissionMap\WebOfGodState.h" />
    <ClInclude Include="World\PVEMissionMap\WebOfGodStateWaves.h" />
    <ClInclude Include="World\Script\DynamicTagvolumeScript.h" />
    <ClInclude Include="World\Script\IRTouchVolumeScript.h" />
    <ClInclude Include="World\Script\ObjectVolumeScript.h" />
    <ClInclude Include="World\Script\PathVolumeScript.h" />
    <ClInclude Include="World\Script\QuestHelpVolumeScript.h" />
    <ClInclude Include="World\Script\QuestVolumeScript.h" />
    <ClInclude Include="World\Script\VolumeScript.h" />
    <ClInclude Include="World\Script\VolumeSpawnScript.h" />
    <ClInclude Include="World\Script\ZoneLevelScript.h" />
    <ClInclude Include="World\Script\ZoneLevelScriptTable.h" />
    <ClInclude Include="World\Sector.h" />
    <ClInclude Include="World\SectorGrid.h" />
    <ClInclude Include="World\SectorGridManager.h" />
    <ClInclude Include="World\SectorRunner.h" />
    <ClInclude Include="World\SectorSequencer.h" />
    <ClInclude Include="World\SectorSystem\FieldPointSector.h" />
    <ClInclude Include="World\SectorSystem\SharedQuestSystem.h" />
    <ClInclude Include="World\Telegram.h" />
    <ClInclude Include="World\ZoneHandler.h" />
    <ClInclude Include="ZoneBuildSetting.h" />
    <ClInclude Include="ZoneServer.h" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\..\Shared\Shared.vcxproj">
      <Project>{33ac5b3d-1dc9-40a6-aa37-ecce1e059d43}</Project>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="..\..\..\..\SharedData\aiscript\npcscript.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_battlefield.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_boss_anilata.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_boss_batistan.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_boss_breezecu.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_boss_darkknight.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_boss_deseulran.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_boss_eyeofdeath.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_boss_freaker.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_boss_frenzying_specimens.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_boss_frost_queen.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_boss_gagant.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_boss_gainox.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_boss_grande.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_boss_grande2.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_boss_hero_mission.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_boss_ilak.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_boss_kaizel.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_boss_kantur_gadiun.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_boss_khculsazu.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_boss_kissle_rose.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_boss_kraemten.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_boss_larbaKing.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_boss_lupa.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_boss_mrpumpkin.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_boss_orman.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_boss_poldgan.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_boss_salkres.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_boss_sezak.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_boss_targashi.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_boss_venti.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_boss_walkriger.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_boss_zrataken.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_chaos_castle.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_colosseum.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_default_battle_function.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_default_battle_state.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_default_nonbattle.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_endless_tower.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_escort.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_gloden_goblin.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_golden_buzy_dragon.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_hero_battle.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_initialize.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_interrupt.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_invasion_stone.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_mission_adela.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_mission_bradrice.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_mission_corps.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_mission_forgotten.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_mission_frozenlake.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_mission_goldenfield.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_mission_gracier.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_mission_Guild_Territory.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_mission_karina.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_mission_shaperock.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_mission_silvery.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_mission_weiss.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_named.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_named_aynus.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_named_boobalus.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_named_lenavis.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_non_general_attack.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_non_prowl.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_ordinary.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_prowl.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_recharge.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_servant.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_summoner.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_ai_template.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_state_tree.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\npc_util.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\pc_quest.lua" />
    <None Include="..\..\..\..\SharedData\aiscript\pc_script.lua" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>