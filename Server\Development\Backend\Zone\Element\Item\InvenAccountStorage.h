﻿#pragma once

#include <Backend/Zone/Element/Item/InvenSlotable.h>

namespace mu2
{
	//====================================================================================================
	//
	//====================================================================================================

	class alignas(32) InvenStorageAccount : public InvenStorage
	{
	private:
		static StorageInfo	NullStorageInfo;
		StorageInfo			m_storageInfo;	// 디비에 저장된 계정창고 값

	public:
		explicit InvenStorageAccount(EntityPlayer& owner, eInvenType eType, const SlotSizeType maxSlot, Bool isActiveAdd, Bool isActiveSub)
			: InvenStorage(owner, eType, maxSlot, isActiveAdd, isActiveSub)
		{
			m_direction[SOURCE] = true;
			m_direction[TARGET] = true;

			m_emergencyRange = AccountStorages;
		}
		virtual ~InvenStorageAccount() override {}

		virtual Bool		IsStorageAccount() const { return true; }		
		virtual Bool		IsPushable(const eSlotType& toSlot, const ItemConstPtr& item, Bool isAllowBlockNExpired = false) const override;
		virtual Bool		IsUsableRemoteNpcFunction() const override;	

	public://전용
		void				RemoveStorageItemAll(ItemBag& bag); // 시스템 상에서 아이템 제거		
		void				SetStorageInfo(const StorageInfo& info) { m_storageInfo = info; }
		const StorageInfo&	GetStorageInfo() const { return m_storageInfo; }
		ErrorItem::Error	ExpandPeriod(const UInt32 addPeriod, __out StorageInfo& storageInfo);
		ErrorItem::Error	AppendTab4AccountStorage();
		//void				ExpandSlot(const Byte maxSlot);		

	public:
		static Byte						GetCurrActivatedStorageCount(const EntityPlayer& player);
		static const StorageInfo&		GetLatestActivatedStorageInfo(const EntityPlayer& player);
		static StorageInfoList			GetStorageInfos(const EntityPlayer& player);
		static void						ClearStorageList(const EntityPlayer& player);
		static const StorageInfoElem*	GetNextTabInfo(const EntityPlayer& player, __out StorageInfo& info, __out Bool& isAddedBag);
		static ErrorItem::Error			IsCanExpandTab(const EntityPlayer& player, const StorageInfo& info, const StorageInfoElem& elem);
		static eInvenType				CastBagIndexToInvenType(const Byte bagIndex) { return static_cast<eInvenType>(bagIndex + InvenType::ACCOUNT_STORAGE_START); }
		static void						SendStorageSysMsg(const EntityPlayer& player, ErrorItem::Error error);

	protected:
		virtual Bool		isValidSlot(const eSlotType& eSlot) const override;

	};

}
