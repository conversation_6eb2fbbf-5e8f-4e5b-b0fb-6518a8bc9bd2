﻿#include "stdafx.h"

#ifdef __Reincarnation__jason_180628__

#include "NotifierGameContents.h"
#include <Backend/Zone/EntityFactory/EntityPlayer.h>
#include <Backend/Zone/Action/ActionQuest.h>
#include <Backend/Zone/Action/ActionAutoHackChecker.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerTalisman.h>
#ifdef __Patch_ImprintedMemory_by_raylee_181128
#include <Backend/Zone/Action/ActionAchievement.h>
#endif
#include <Backend/Zone/Action/ActionPlayerMailBox.h>

namespace mu2
{
	//=============================================================================
	// 캐릭터 레벨업을 하였다.
	//=============================================================================

	void NotifierGameContents::OnPlayer_CharLevelUp(EntityPlayer& player) const
	{
		UNREFERENCED_PARAMETER(player);
	}

	//=============================================================================
	// 영혼 레벨업을 하였다.
	//=============================================================================

	void NotifierGameContents::OnSoul_SoulLevelUp(EntityPlayer& player, const SoulLevelEx& oldLevel) const
	{
		UNREFERENCED_PARAMETER(oldLevel);
		player.GetQuestAction().OccurQuestBySoulLevel(player.GetSoulLevelEx(), true);

#ifdef	__Patch_Limited_Banner_Shop_robinhwp
		LimitedBannerEvent limitedBannerEvent(LimitedBannerConditionType::SOUL_LEVEL, LimitedBannerConditionValue(player.GetSoulLevelEx()));
		player.GetPlayerLimitedBannerGoodsAction().CheckAndAddToDB(limitedBannerEvent);
#endif	__Patch_Limited_Banner_Shop_robinhwp

#ifdef __Patch_ImprintedMemory_by_raylee_181128
		player.GetAchievementAction().ImprintMemoryNextMission(false);
#endif __Patch_ImprintedMemory_by_raylee_181128
	}

	//=============================================================================
	//유저가 접속(STATE_PLAYER_BASE 상태로 진입했다)을 했다
	//=============================================================================

	void NotifierGameContents::OnPlayer_EnteredStatePlayerBase(EntityPlayer& player, JoinContext::Enum eContext) const
	{
		switch(eContext)
		{
		case JoinContext::JC_CHECKIN:
			{
				player.GetQuestAction().OccurQuestBySoulLevel(player.GetSoulLevelEx(), false);				

#ifdef	__Patch_Limited_Banner_Shop_robinhwp
				LimitedBannerEvent limitedBannerEvent(LimitedBannerConditionType::SOUL_LEVEL, LimitedBannerConditionValue(player.GetSoulLevelEx()));
				player.GetPlayerLimitedBannerGoodsAction().CheckAndAddToDB(limitedBannerEvent);
#endif	__Patch_Limited_Banner_Shop_robinhwp

#ifdef __Patch_Talisman_ChangeOwner_by_jason_180906
				player.GetTalismanAction().InvokeAchievementByTalismanCollectionLevel();
#endif __Patch_Talisman_ChangeOwner_by_jason_180906
			}
			break;
		}

		if (eContext != JoinContext::JC_WARP_MAP)
		{
			player.GetCharSoulInfo().InvokePassiveBuff(SoulLevelEx(0, 0));
#ifdef __Patch_Tower_of_dawn_eunseok_2018_11_05
			player.GetTalismanAction().AddTalismanPointWhenEnterZone();
#endif //__Patch_Tower_of_dawn_eunseok_2018_11_05
#ifdef __Patch_WebOfGod_by_jason_20181213
			player.GetMailBoxAction().LoadReservedSytemMailFromDB();
#endif
#ifdef __Patch_Lua_InstanceGroup_Param_by_jason_20190109
			InstanceSectorGroupEx* group = player.GetSector()->GetInstanceGroup();
			if (group)
			{
				group->OnEnter(player);
			}
#endif
		}
		

	}

	//=============================================================================
	// 환생이 되었다.
	//=============================================================================

	void NotifierGameContents::OnSoul_ReincarnatedLevelUp(EntityPlayer& player) const
	{
		Sector* sector = player.GetSector();
		VERIFY_RETURN(sector, );

		{
			// 환생했다고 주변에 나와 나의 주변에 알려준다.
			ENtfGameReincarnatedLevelUp* ntf = new ENtfGameReincarnatedLevelUp;
			ntf->entityId = player.GetId();
			ntf->soulInfo = player.GetCharSoulInfo();
			sector->Broadcast(EventPtr(ntf));
		}

		{
			// 헤드라인에 메세지 출력			
			SystemMessage msg(L"sys", L"Msg_Reincarnation_Notice");
			msg.SetParam(L"str", player.GetCharName());
			msg.SetParam(L"str", player.GetCharSoulInfo().GetReincarnatedLevel());
			theGameMsg.WorldcastSystemMessage(msg);
		}

		{
#ifdef __Patch_ImprintedMemory_by_raylee_181128
			player.GetAchievementAction().ImprintMemoryNextMission(false);
#endif __Patch_ImprintedMemory_by_raylee_181128
		}

		// 액션로그 요청이 있을 수 있다.
		
	}

	//=============================================================================
	// 퀘스트 보상을 받고 종료되었다.
	//=============================================================================

	void NotifierGameContents::OnQuest_Finished(EntityPlayer& player, const QuestElem& finishedQuestElem) const
	{
		// 하드코딩, 환생관련 사전퀘스트가 완료되었는지 검사
		player.GetCharSoulInfo().OnFinishedPreviousQuest(finishedQuestElem);
	}

	//=============================================================================
	// 서버로딩과 클라이언트로딩이 완료되었고, 아직 STATE_PLAYER_JOIN 상태이다
	//=============================================================================

	void NotifierGameContents::OnPlayer_LoadCompleted(EntityPlayer& player, JoinContext::Enum eContext) const
	{
		if (eContext != JoinContext::JC_WARP_MAP)
		{
			player.GetAutoHackCheckerAction().SetupEnterZone();
		}		
	}

	//=============================================================================
	//	calculatedDamaged : hp를 깍기위한 최종 데미지
	//	calculatedDamaged를 핸들링하면 핸들링된 값으로 HP가 깍인다	
	//	최종피해량을 조정하고 싶을때 여기서 하자
	//=============================================================================

	void NotifierGameContents::OnFinalCalculated_Damaged(const EntityUnit& caster, EntityUnit& target, __inout UInt32& finalDamaged) const
	{

		//========================================================================
		// step 1 : 원본최종 데미지를 저장시킨다.
		//========================================================================

		const UInt32 originFinalDamaged = finalDamaged;


		//========================================================================
		// step 2 : 적중률에 의한 피해감소 계산(적중률 비율만)
		//========================================================================

		if (caster.IsPlayer())
		{	
			AbilityValue decreasedDamage = 0;

			AbilityFloat fDecDmgRate = (1.f - caster.GetAbilityAction().GetAccurateRateVsDodge(target) * DEFAULT_FLOATIZED_RATE);
			fDecDmgRate = std::min<AbilityFloat>(1.f, std::max(0.f, fDecDmgRate));

			decreasedDamage += static_cast<Int32>(originFinalDamaged * fDecDmgRate);

			// 중간정산
			finalDamaged -= decreasedDamage;
		}

		//========================================================================
		// step N : to do here............
		//========================================================================
		
		


		//======================================================================================
		// step final : 유리벽, 받는 최종 대미지 감소(최종적으로 받게될 대미지를 한번 감소시킴)
		// 최종단계이므로 더이상 건들지 말자. 추가하려면 위쪽에 알아서 잘....
		//======================================================================================

#ifdef  __Patch_Affect_final_dmg_once_dec_rate_Option_for_MonsterAbility_by_robinhwp_20190429
#else	__Patch_Affect_final_dmg_once_dec_rate_Option_for_MonsterAbility_by_robinhwp_20190429
		if (target.IsPlayer())
#endif __Patch_Affect_final_dmg_once_dec_rate_Option_for_MonsterAbility_by_robinhwp_20190429
		{
			AbilityValue decreasedDamage = 0;

			decreasedDamage += static_cast<Int32>(finalDamaged * target.GetFloatizedAbilityRate(EAT::FINAL_DMG_ONCE_DEC_RATE));
			decreasedDamage = std::max(0, decreasedDamage);

			finalDamaged -= decreasedDamage;
			decreasedDamage = std::max(0, decreasedDamage);
		}
		
	}

	//=============================================================================
	//	최종 치명타명중률이 계산되었다.
	//	치명타명중률이 최종계산된 직후에 호출됨
	//	casterFinalCrtRate을 변경하면, 변경된 값으로 이후에 적용된다.
	//=============================================================================

	void NotifierGameContents::OnFinalCalculated_CRT_RATE(const EntityUnit& caster, EntityUnit& target, __inout AbilityValue& casterFinalCrtRate) const
	{
		// 공격자의 최종치명타명중률 = 공격자의 치명타명중률 - 방어자의 치명타명중률저항

		casterFinalCrtRate -= target.GetAbilityValue(EAT::RESIST_CRT_RATE);
		casterFinalCrtRate = std::max(0, casterFinalCrtRate);
	}
	
	//=============================================================================
	//	최종 치명타파괴률이 계산되었다.
	//	치명타파괴률이 최종계산된 직후에 호출됨
	//	casterFinalCrtMulRate을 변경하면, 변경된 값으로 이후에 적용된다.
	//=============================================================================

	void NotifierGameContents::OnFinalCalculated_CRT_MUL_RATE(const EntityUnit& caster, EntityUnit& target, __inout AbilityValue& casterFinalCrtMulRate) const
	{
		casterFinalCrtMulRate -= target.GetAbilityValue(EAT::RESIST_CRT_MUL_RATE);
		casterFinalCrtMulRate = std::max(0, casterFinalCrtMulRate);
	}

	//=============================================================================
	//	최종 방어력무시율이 계산되었다.
	//	방어력무시율이 최종계산된 직후에 호출됨
	//	casterFinalIgnoreDefenseRate을 변경하면, 변경된 값으로 이후에 적용된다.
	//=============================================================================

	void NotifierGameContents::OnFinalCalculated_IGNORE_DEFENSE_RATE(const EntityUnit& caster, EntityUnit& target, __inout AbilityValue& casterFinalIgnoreDefenseRate) const
	{
		casterFinalIgnoreDefenseRate -= target.GetAbilityValue(EAT::RESIST_IGNORE_DEFENSE_RATE);
		casterFinalIgnoreDefenseRate = std::max(0, casterFinalIgnoreDefenseRate);
	}

	//=============================================================================
	// 플레이어가 죽어서 StatePlayerDead 상태로 넘어가는 순간 발생
	//=============================================================================

	void NotifierGameContents::OnBattle_PlayerDead(EntityPlayer& dier) const
	{
		UNREFERENCED_PARAMETER(dier);
	}

	void	NotifierGameContents::OnChatting_SentNormal(EntityPlayer& player, const std::wstring& msg) const
	{
		UNREFERENCED_PARAMETER(player);
		UNREFERENCED_PARAMETER(msg);
		wstring temp ;
		temp = msg;
	}

	void	NotifierGameContents::OnChatting_SentParty(EntityPlayer& player, const std::wstring& msg) const
	{
		UNREFERENCED_PARAMETER(player);
		UNREFERENCED_PARAMETER(msg);
		wstring temp;
		temp = msg;
	}
	void	NotifierGameContents::OnAchievement_Completed(EntityPlayer& player, const IndexAchievement& completedAchievementIndex) const
	{
		UNREFERENCED_PARAMETER(player);
		UNREFERENCED_PARAMETER(completedAchievementIndex);
		IndexAchievement temp;
		temp = completedAchievementIndex;
	}

	void	NotifierGameContents::OnItem_Received(EntityPlayer& player, const IndexItem& receivedItemIndex) const
	{
		UNREFERENCED_PARAMETER(player);
		UNREFERENCED_PARAMETER(receivedItemIndex);
		IndexItem temp;
		temp = receivedItemIndex;
	}

	void	NotifierGameContents::OnItem_Used(EntityPlayer& player, const IndexItem& usedItemIndex) const
	{
		UNREFERENCED_PARAMETER(player);
		UNREFERENCED_PARAMETER(usedItemIndex);
	}

	void	NotifierGameContents::OnItem_Equiped(EntityPlayer& player, const ItemConstPtr& equipItem) const
	{
		UNREFERENCED_PARAMETER(player);
		UNREFERENCED_PARAMETER(equipItem);
	}

	void	NotifierGameContents::OnItem_UnEquiped(EntityPlayer& player, const ItemConstPtr& unequipItem) const
	{
		UNREFERENCED_PARAMETER(player);
		UNREFERENCED_PARAMETER(unequipItem);
	}

	void	NotifierGameContents::OnItem_EquipExchanged(EntityPlayer& player, const ItemConstPtr& equipItem, const ItemConstPtr& unquipItem) const
	{
		UNREFERENCED_PARAMETER(player);
		UNREFERENCED_PARAMETER(equipItem);
		UNREFERENCED_PARAMETER(unquipItem);
	}

	void	NotifierGameContents::OnBattle_KilledNpc(EntityPlayer& killer, const EntityNpc& targetNpc) const
	{
		UNREFERENCED_PARAMETER(killer);
		UNREFERENCED_PARAMETER(targetNpc);
	}

	void	NotifierGameContents::OnBattle_KilledPlayer(EntityPlayer& killer, const EntityPlayer& targetPlayer) const
	{
		UNREFERENCED_PARAMETER(killer);
		UNREFERENCED_PARAMETER(targetPlayer);
	}
	void	NotifierGameContents::OnBattle_CastedSkill(EntityPlayer& player, const IndexSkill& indexSkill) const
	{
		UNREFERENCED_PARAMETER(player);
		UNREFERENCED_PARAMETER(indexSkill);
	}

	void	NotifierGameContents::OnParty_Joned(EntityPlayer& player) const
	{
		UNREFERENCED_PARAMETER(player);
	}
	void	NotifierGameContents::OnParty_Leaved(EntityPlayer& player) const
	{
		UNREFERENCED_PARAMETER(player);
	}
	void	NotifierGameContents::OnItem_Bought(EntityPlayer& player, const ItemDatas& vecBoughtItem) const
	{
		UNREFERENCED_PARAMETER(player);
		UNREFERENCED_PARAMETER(vecBoughtItem);
	}

	void	NotifierGameContents::OnItem_Sold(EntityPlayer& player, const std::vector< ItemSlotInfo > soldItems) const
	{
		UNREFERENCED_PARAMETER(player);
		UNREFERENCED_PARAMETER(soldItems);
	}

	void	NotifierGameContents::OnPet_Registed(EntityPlayer& player, const PetData& data) const
	{
		UNREFERENCED_PARAMETER(player);
		UNREFERENCED_PARAMETER(data);
	}

	void	NotifierGameContents::OnPet_Grew(EntityPlayer& player, const PetData& data) const
	{
		UNREFERENCED_PARAMETER(player);
		UNREFERENCED_PARAMETER(data);
	}

	//=============================================================================
	// 무한의 탑 종료시 호출.
	// 모든 층을 클리어할 경우 isSuccess는 true로 전달된다.
	//=============================================================================
	void	NotifierGameContents::OnDungeon_EndlessTowerFinished(EntityPlayer& player, const Bool isSuccess) const
	{
		UNREFERENCED_PARAMETER(player);
		UNREFERENCED_PARAMETER(isSuccess);
	}

	//=============================================================================
	// 블러드캐슬 종료시 호출.
	// 블러드캐슬을 클리어 했을 경우 isSuccess는 true로 전달된다.
	//=============================================================================
	void	NotifierGameContents::OnDungeon_BloodCastleFinished(EntityPlayer& player, const Bool isSuccess) const
	{
		UNREFERENCED_PARAMETER(player);
		UNREFERENCED_PARAMETER(isSuccess);
	}

	//=============================================================================
	// 루파의 미궁  클리어시 호출.
	// 차징포인트가 최대치로 클리어할 경우 isMaximumPoint는 true로 전달된다.
	//=============================================================================
	void	NotifierGameContents::OnDungeon_MazeCleared(EntityPlayer& player, const Bool isMaximumPoint) const
	{
		UNREFERENCED_PARAMETER(player);
		UNREFERENCED_PARAMETER(isMaximumPoint);
	}

	//=============================================================================
	// npc가 player로부터 공격을 당했을때 계산되는 최종 어그로포인트
	// 최종포인트를 조정하고 싶을때 여기서 하자
	//=============================================================================

	void NotifierGameContents::OnFinalCalculated_AggroPoint(EntityNpc& npc, const EntityPlayer& attacker, __inout UInt32& finalAggroPoint) const
	{
		finalAggroPoint = static_cast<UInt32>(finalAggroPoint * attacker.GetNormalizedAbilityRate(EAT::AGGRO_INC_RATE));
	}

	void NotifierGameContents::OnBurningStamina_Enable(const IndexContinent& continent) const
	{
		UNREFERENCED_PARAMETER(continent);
	}

	void NotifierGameContents::OnBurningStamina_Disable(const IndexContinent& continent) const
	{
		UNREFERENCED_PARAMETER(continent);
	}
}

#endif
