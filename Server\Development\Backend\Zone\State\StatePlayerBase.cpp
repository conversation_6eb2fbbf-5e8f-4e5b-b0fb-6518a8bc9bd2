﻿#include "stdafx.h"
#include <Backend/Zone/ZoneServer.h>
#include <Backend/Zone/State/StatePlayerBase.h>
#include <Backend/Zone/Action/ActionAchievement.h>
#include <Backend/Zone/Action/ActionDroppedItem.h>
#include <Backend/Zone/Action/ActionAchievement.h>
#include <Backend/Zone/Action/ActionNpc/ActionNpcOperate.h>
#include <Backend/Zone/Action/ActionNpcSkill.h>
#include <Backend/Zone/Action/ActionParty.h>
#include <Backend/Zone/Action/ActionPlayer.h>

#include <Backend/Zone/Action/ActionPlayerInventory.h>
#include <Backend/Zone/Action/ActionPlayerMailBox.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerCognition.h>
#include <Backend/Zone/Action/ActionUseItem.h>
#include <Backend/Zone/Action/ActionQuest.h>
#include <Backend/Zone/Action/ActionPlayer/ActionSkillManage.h>
#include <Backend/Zone/Action/ActionPlayerItemTrade.h>
#include <Backend/Zone/Action/ActionPlayerStorage.h>
#include <Backend/Zone/Action/ActionAutoHackChecker.h>

#include <Backend/Zone/Action/ActionPlayerMaking.h>
#include <Backend/Zone/Action/ActionPortal.h>
#include <Backend/Zone/Action/ActionPlayer/ActionContact.h>
#include <Backend/Zone/Action/ActionDurability.h>
#include <Backend/Zone/Action/ActionExpiryItem.h>
#include <Backend/Zone/Action/ActionMissionDunGeon.h>
#include <Backend/Zone/Action/ActionPassivity.h>
#include <Backend/Zone/Action/ActionTutorial.h>
#include <Backend/Zone/Action/ActionEloPoint.h>

#include <Backend/Zone/Action/ActionBuff.h>
#include <Backend/Zone/Action/ActionSkillRune.h>

#include <Backend/Zone/Action/ActionNpcBlackMarket.h>
#ifndef __Reincarnation__jason_180628__
#include <Backend/Zone/Action/ActionPlayer/ActionSoulSkillManage.h>
#endif
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerDamageMeter.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerColosseum.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerSurvey.h>
#include <Backend/Zone/Action/ActionPlayer/ActionQuickslot.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerKnightage.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerKnightageStorage.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerMissionMapTier.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerEvent.h>
#include <Backend/Zone/Action/ActionTutorial.h>
#include <Backend/Zone/Action/ActionPlayer/ActionSkillMastery.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerMembership.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerEscape.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerExchange.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerEventInventory.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerSeason.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerReturnUser.h>

#include <Backend/Zone/Action/ActionPlayer/ActionPlayerChaosCastleHistory.h>
#include <Backend/Zone/Action/ActionSkillChanger.h>
#include <Backend/Zone/Action/ActionPassivity.h>

#include <Backend/Zone/Action/ActionPlayer/ActionPlayerTalisman.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerTalismanUnit.h>

#include <Backend/Zone/Action/ActionPlayer/ActionPlayerGuideSystem.h>
#include <Backend/Zone/Action/ActionPlayerLoad.h>
#include <Backend/Zone/Action/ActionPlayer/ActionJoin.h>


#include <Backend/Zone/Action/ActionDailyMission.h>
#include <Backend/Zone/System/LogicEffectSystem/EntityCollector.h>
#include <Backend/Zone/State/Handler/StateDispatcher.h>
#include <Backend/Zone/State/Handler/PcMoveHandler.h>
#include <Backend/Zone/System/DropSystem.h>

#include <Backend/Zone/System/GMCommandSystem.h>
#include <Backend/Zone/System/PortalSystem.h>
#include <Backend/Zone/System/LogicEffectSystem.h>
#include <Backend/Zone/System/ReviveSystem.h>
#include <Backend/Zone/System/SpawnSystem.h>
#include <Backend/Zone/System/QuestSystem.h>
#include <Backend/Zone/System/TransactionSystem.h>
#include <Backend/Zone/State/Npc/StateNpcDuelRequest.h>
#include <Backend/Zone/Transaction/ItemTransaction/ItemPushTransaction.h>
#include <Backend/Zone/Transaction/ItemTransaction/ItemSellTransaction.h>
#include <Backend/Zone/Transaction/ItemTransaction/ItemRebuyTransaction.h>
#include <Backend/Zone/Transaction/ItemTransaction/ItemBuyTransaction.h>
#include <Backend/Zone/Transaction/ItemTransaction/ItemEquipTransaction.h>
#include <Backend/Zone/Transaction/ItemTransaction/ItemRemoveTransaction.h>
#include <Backend/Zone/FieldKnightage/FieldKnightage.h>
#include <Backend/Zone/SectorQuestMissionDecoratorManager.h>
#include <Backend/Zone/View/ViewPosition.h>
#include <Backend/Zone/Transaction/ItemTransaction/AccountStorageTransaction.h>
#include <Backend/Zone/Transaction/ItemTransaction/PetTransaction.h>
#include <Backend/Zone/Element/Item/InvenNonSlotable.h>
#include <Backend/Zone/Element/Item/InvenPrivateStorage.h>
#include <Backend/Zone/Element/Item/InvenAccountStorage.h>
#include <Backend/Zone/Element/Item/InvenEquip.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerNoticePopUp.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerSprout.h>
#ifdef __Reincarnation__jason_180628__
#include <Backend/Zone/System/NotifierGameContents.h>
#endif
#ifdef __Patch_Event_Buff_Add_Benefit_by_jjangmo_20181122
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerEventBuff.h>
#endif
#ifdef __Patch_Legend_Of_Bingo_by_jaekwan_20181126
#include <Backend/Zone/Transaction/ItemTransaction/BingoSlotExchangeTransaction.h>
#endif // __Patch_Legend_Of_Bingo_by_jaekwan_20181126
#ifdef __Patch_SetItem_Renewal_by_kangms_2019_4_10
#include <Backend/zone/Action/ActionPlayer/ActionPlayerSetCard.h>
#include <Backend/zone/Action/ActionEventProxy.h>
#endif//__Patch_SetItem_Renewal_by_kangms_2019_4_10

namespace mu2
{

struct StatePlayerBase::StatePlayerBaseImpl : public GeneralAlloc
{
	StatePlayerBaseImpl() : saveCharInfoTick(0), checkUseItemTick( 0 ), lastUpdatedPingTick(0), reqPingTick(0){}
	Tick saveCharInfoTick;	
	Tick checkUseItemTick;
	Tick lastUpdatedPingTick;
	Tick reqPingTick;
	std::vector< UInt32 > latencyArr;
	PcMoveHandler	moveHandler;
};

StatePlayerBase::StatePlayerBase()
: State4Player(STATE_PLAYER_BASE, "STATE_PLAYER_BASE")
, m_dispatcher(this)
, m_impl( NEW StatePlayerBaseImpl )
{
	CounterInc("StatePlayerBase");

	// 진입 / 연결 관련 

	SetHandlerLoopback (NULL, EwzReqPlayerHeartbeat							,	StatePlayerBase::onEwzReqPlayerHeartbeat					, false);
	SetHandlerLoopback( NULL, EczReqCharMoveGoodPosition				    ,	StatePlayerBase::onEczReqCharMoveGoodPosition				, false );
	SetHandlerLoopback( NULL, EReqEquip										,	StatePlayerBase::onReqEquipItem								, false);
	SetHandlerLoopback( NULL, EReqUnequip								    ,	StatePlayerBase::onReqUnequipItem							, false);
	SetHandlerLoopback( NULL, EReqEquipExchange							    ,	StatePlayerBase::onReqEquipExchangeItem						, false);
	SetHandlerLoopback( NULL, EReqMove									    ,	StatePlayerBase::onReqMoveItem								, false);
	SetHandlerLoopback( NULL, EReqRemove								    ,	StatePlayerBase::onReqRemoveItem							, false);
	SetHandlerLoopback( NULL, EczReqPickUp								    ,	StatePlayerBase::onReqPickupItem							, false);
	SetHandlerLoopback( NULL, EReqExchange								    ,	StatePlayerBase::onReqExchangeItem							, false);
	SetHandlerLoopback( NULL, EReqSeparate								    ,	StatePlayerBase::onReqSeparateItem							, false);
	SetHandlerLoopback( NULL, EReqOverlap								    ,	StatePlayerBase::onReqOverlapItem							, false);
	SetHandlerLoopback( NULL, EReqBuy									    ,	StatePlayerBase::onReqBuyItem								, false);
	SetHandlerLoopback( NULL, EReqSell									    ,	StatePlayerBase::onReqSellItem								, false);
	SetHandlerLoopback( NULL, EReqRebuy									    ,	StatePlayerBase::onReqRebuyItem								, false);
	SetHandlerLoopback( NULL, EReqRebuyItemList							    ,	StatePlayerBase::onReqRebuyItemList							, false);
	SetHandlerLoopback( NULL, EReqCastingCancel							    ,	StatePlayerBase::onReqCastingCancel							, false);
	SetHandlerLoopback( NULL, EReqSortBagItems								,	StatePlayerBase::onReqSortBagItem							, false);
	SetHandlerLoopback( NULL, EReqChangeCharName							,	StatePlayerBase::onReqCharChangeName						, false);
	SetHandlerLoopback( NULL, EReqChangeKnightageName						,	StatePlayerBase::onReqKnightageChangeName					, false);
	SetHandlerLoopback( NULL, EReqUseGambleItem							    ,	StatePlayerBase::onReqUseGambleItem							, false);	
	SetHandlerLoopback( NULL, EReqSelectGambleItem						    ,	StatePlayerBase::onReqSelectGambleItem						, false);	
	SetHandlerLoopback( NULL, EReqStorageMove							    ,	StatePlayerBase::onReqStorageMove							, false);
	SetHandlerLoopback( NULL, EReqUseRecoveryEntranceItem				    ,	StatePlayerBase::onReqUseRecoveryEntranceItem				, false);
	SetHandlerLoopback( NULL, EResItemRecoveryEntranceCheck				    ,	StatePlayerBase::onResItemRecoveryEntranceCheck				, false);
	SetHandlerLoopback( NULL, EReqSuggestTrade							    ,	StatePlayerBase::onReqSuggestTrade							, false);
	SetHandlerLoopback( NULL, EReqAnswerTradeSuggested					    ,	StatePlayerBase::onReqAnswerTradeSuggested					, false);
	SetHandlerLoopback( NULL, EReqChangeTradeInfo						    ,	StatePlayerBase::onReqChangeTradeInfo						, false);
	SetHandlerLoopback( NULL, EReqReadyTrade							    ,	StatePlayerBase::onReqReadyTrade							, false);
	SetHandlerLoopback( NULL, EReqStartTrade							    ,	StatePlayerBase::onReqStartTrade							, false);
	SetHandlerLoopback( NULL, EReqCancelTrade							    ,	StatePlayerBase::onReqCancelTrade							, false);
	SetHandlerLoopback( NULL, ENtfCancelTrade							    ,	StatePlayerBase::onNtfCancelTrade							, false);
	SetHandlerLoopback( NULL, ENtfTradeCompleted						    ,	StatePlayerBase::onNtfTradeCompleted						, false);
	SetHandlerLoopback( NULL, ENtfCommPartyCreate						    ,	StatePlayerBase::onNtfCommPartyCreate						, false);
	SetHandlerLoopback( NULL, ENtfCommPartyDestroy						    ,	StatePlayerBase::onNtfCommPartyDestroy						, false);
	SetHandlerLoopback( NULL, ENtfCommPartyAddMember					    ,	StatePlayerBase::onNtfCommPartyAddMember					, false);
	SetHandlerLoopback( NULL, ENtfCommPartyLeaveMember					    ,	StatePlayerBase::onNtfCommPartyLeaveMember					, false);
	SetHandlerLoopback( NULL, EReqCommPartySyncMember					    ,	StatePlayerBase::onReqCommPartySyncMember					, false);
	SetHandlerLoopback( NULL, ENtfCommPartyChangeLeader					    ,	StatePlayerBase::onNtfCommPartyChangeLeader					, false);
	SetHandlerLoopback( NULL, ENtfCommPartySyncMemberInfo				    ,	StatePlayerBase::onNtfCommPartySyncMemberInfo				, false);
	SetHandlerLoopback( NULL, ENtfCommPartyMemberEnterExit				    ,	StatePlayerBase::onNtfCommPartyMemberEnterExit				, false);
	SetHandlerLoopback( NULL, EReqCommPartySyncCandidateAdvDungeon		    ,	StatePlayerBase::onReqCommPartySyncCandidateAdvDungeon		, false);
	SetHandlerLoopback( NULL, EReqPartyDungeonCheckRequirement			    ,	StatePlayerBase::onReqPartyDungeonCheckRequirement			, false);	
	SetHandlerLoopback( NULL, EReqCommPartySyncLocationWhenCheckIn			,	StatePlayerBase::onReqCommPartySyncLocationWhenCheckIn		, false);
	SetHandlerLoopback( NULL, Ecw2zReqRegistFootHold					    ,	StatePlayerBase::onEcw2zReqRegistFootHold					, false);
	SetHandlerLoopback( NULL, EReqGameSyncDirection						    ,	StatePlayerBase::onReqGameSyncDirection						, false);
	SetHandlerLoopback( NULL, EReqGameCheat								    ,	StatePlayerBase::onReqGameCheat								, false);	
	SetHandlerLoopback( NULL, EReqGameGotoServerList					    ,	StatePlayerBase::onReqGameGotoServerList					, false);	
	SetHandlerLoopback( NULL, ENtfGameChatNormalToZone					    ,	StatePlayerBase::onNtfGameChatNormalToZone					, false);
	SetHandlerLoopback( NULL, ENtfCommChatParty							    ,	StatePlayerBase::onNtfCommChatParty							, false);
	SetHandlerLoopback( NULL, EReqGameFlyReady							    ,	StatePlayerBase::onReqGameFlyReady							, false);
	SetHandlerLoopback( NULL, EReqGameFlyCancel							    ,	StatePlayerBase::onReqGameFlyCancel							, false);
	SetHandlerLoopback( NULL, EReqContactEntity							    ,	StatePlayerBase::onReqContactEntity							, false);	
#ifdef __Reincarnation__jason_180628__
#else
	SetHandlerLoopback( NULL, EResDbInsertSpecialSkill					    ,	StatePlayerBase::onResDbInsertSpecialSkill					, false);
#endif
	SetHandlerLoopback( NULL, EReqQuest									    ,	StatePlayerBase::onReqQuest									, false);
	SetHandlerLoopback( NULL, EReqQuestReward							    ,	StatePlayerBase::onReqQuestReward							, false);
	SetHandlerLoopback( NULL, EReqQuestVolumeCheck							,	StatePlayerBase::onReqQuestVolumeCheck						, false);
	SetHandlerLoopback( NULL, EReqAdvantureEventInfo						,	StatePlayerBase::onReqAdvantureEventInfo					, false);
	SetHandlerLoopback( NULL, EReqAdvanturePageReward						,   StatePlayerBase::onReqAdvanturePageReward					, false);
	SetHandlerLoopback( NULL, EReqAdvantureNextReport						,   StatePlayerBase::onReqAdvantureNextReport					, false);
	SetHandlerLoopback( NULL, EReqGameExploredPortals					    ,	StatePlayerBase::onReqGameExploredPortals					, false);	
	SetHandlerLoopback( NULL, EResDbInsertMail							    ,	StatePlayerBase::onResDbInsertMail							, false);
	SetHandlerLoopback( NULL, EResDbRemoveMail							    ,	StatePlayerBase::onResDbRemoveMail							, false);
	SetHandlerLoopback( NULL, EResDbUpdateMail							    ,	StatePlayerBase::onResDbUpdateMail							, false);
	SetHandlerLoopback( NULL, EResDbGetMails							    ,	StatePlayerBase::onResDbGetMails							, false);
	SetHandlerLoopback( NULL, EResDbGetMailCount						    ,	StatePlayerBase::onResDbGetMailCount						, false);
	SetHandlerLoopback( NULL, EResDbGetReturnMails						    ,	StatePlayerBase::onResDbGetReturnMails						, false);
	SetHandlerLoopback( NULL, EReqGameSendMail							    ,	StatePlayerBase::onReqGameSendMail							, false);
 	SetHandlerLoopback( NULL, EczReqGameSyncMailList					    ,	StatePlayerBase::onReqGameSyncMailList						, false);
 	SetHandlerLoopback( NULL, EReqGameGetMailList						    ,	StatePlayerBase::onReqGameGetMailList						, false);
 	SetHandlerLoopback( NULL, EReqGameOpenMail							    ,	StatePlayerBase::onReqGameOpenMail							, false);
 	SetHandlerLoopback( NULL, EReqGameGetMailItems						    ,	StatePlayerBase::onReqGameGetMailItems						, false);
 	SetHandlerLoopback( NULL, EwzNtfGameGotNewMail						    ,	StatePlayerBase::onNtfGameGotNewMail						, false);
	SetHandlerLoopback( NULL, EReqGameGetMailMoney						    ,	StatePlayerBase::onReqGameGetMailMoney						, false);
	SetHandlerLoopback( NULL, EReqGameDelMail							    ,	StatePlayerBase::onReqGameDelMail							, false);
	SetHandlerLoopback( NULL, EResDbInsertItem							    ,	StatePlayerBase::onResDbInsertItem							, false);
	SetHandlerLoopback( NULL, EResDbRemoveItem							    ,	StatePlayerBase::onResDbRemoveItem							, false);
	SetHandlerLoopback( NULL, EResDbUpdateItem							    ,	StatePlayerBase::onResDbUpdateItem							, false);
	SetHandlerLoopback( NULL, EResDbClearBag							    ,	StatePlayerBase::onResDbClearBag							, false);
	SetHandlerLoopback( NULL, EResDbItemTransaction						    ,	StatePlayerBase::onResDbItemTransaction						, false);
	SetHandlerLoopback( NULL, EReqItemExtract							    ,	StatePlayerBase::onReqItemExtract							, false);
	SetHandlerLoopback( NULL, EReqExpandStorageTab						    ,	StatePlayerBase::onReqExpandStorageTab						, false);
	SetHandlerLoopback( NULL, EReqExpandStoragePeriod 					    ,	StatePlayerBase::onReqExpandStoragePeriod					, false);
	SetHandlerLoopback( NULL, EReqBuyStorageBag							    ,	StatePlayerBase::onReqBuyStorageBag							, false);
	SetHandlerLoopback( NULL, EResDbInsMakingFormula					    ,	StatePlayerBase::onResDbInsMakingFormula					, false);
	SetHandlerLoopback( NULL, EResDbDelAllMakingFormulaList				    ,	StatePlayerBase::onResDbDelAllMakingFormulaList				, false);
	SetHandlerLoopback( NULL, EReqGameMakingCastStart					    ,	StatePlayerBase::onReqGameMakingCastStart					, false);
	SetHandlerLoopback( NULL, EReqGameMakingFormulaAdd					    ,	StatePlayerBase::onReqGameMakingFormulaAdd					, false);
	SetHandlerLoopback( NULL, EReqGameMakingCastCancel					    ,	StatePlayerBase::onReqGameMakingCastCancel					, false);
	SetHandlerLoopback( NULL, EReqGameMaking							    ,	StatePlayerBase::onReqGameMaking							, false);
	SetHandlerLoopback( NULL, EczReqGamePortal							    ,	StatePlayerBase::onEczReqGamePortal							, false);
	SetHandlerLoopback( NULL, EwzNtfCommPortalInfoSync					    ,	StatePlayerBase::onNtfCommPortalInfoSync					, false);	
	SetHandlerLoopback( NULL, EResDbDeleteAchieve						    ,	StatePlayerBase::onResDbDeleteAchieve						, false); 
	SetHandlerLoopback( NULL, EResDbDeleteAllAchieve					    ,	StatePlayerBase::onResDbDeleteAllAchieve					, false); 
	SetHandlerLoopback( NULL, EReqSealItem								    ,	StatePlayerBase::onReqSealItem								, false);
	SetHandlerLoopback( NULL, EReqSaveEnvironmentSetting				    ,	StatePlayerBase::onReqSaveEnvironmentSetting				, false);
	SetHandlerLoopback( NULL, EResDbSaveEnvironmentSetting				    ,	StatePlayerBase::onResDbSaveEnvironmentSetting				, false);
	SetHandlerLoopback( NULL, EReqItemRepairAll							    ,	StatePlayerBase::onReqItemRepairAll							, false);
	SetHandlerLoopback( NULL, EReqItemRepair							    ,	StatePlayerBase::onReqItemRepair							, false);
	SetHandlerLoopback( NULL, EReqMissionmapCandidateC					    ,	StatePlayerBase::onReqMissionmapCandidateC					, false);	
	SetHandlerLoopback( NULL, EReqGameMMMemberScoreInfos					,	StatePlayerBase::onMissionMapMemberScoreInfos				, false);
	SetHandlerLoopback( NULL, EReqMissionmapConfirmC					    ,	StatePlayerBase::onReqMissionmapConfirmC					, false);	
	SetHandlerLoopback( NULL, EResDbSetCharEloPoint						    ,	StatePlayerBase::onResDbSetCharEloPoint						, false);
	SetHandlerLoopback( NULL, ENtfMissionmapCandidateToZone					,	StatePlayerBase::onNtfMissionmapCandidateToZone				, false);
	SetHandlerLoopback( NULL, EReqMissionRewardCardSelect				    ,	StatePlayerBase::onReqMissionRewardCardSelect				, false);
	SetHandlerLoopback( NULL, EResCommFriendInsert							,	StatePlayerBase::onResFriendInsert							, false);
	SetHandlerLoopback( NULL, EResCommFriendRemove							,	StatePlayerBase::onResFriendRemove							, false);
	SetHandlerLoopback( NULL, EReqGamePlayerCharInfo					    ,	StatePlayerBase::onReqGamePlayerCharInfo					, false);
	SetHandlerLoopback( NULL, EReqSubsribeTestAi							,	StatePlayerBase::onReqSubscribeTest							, false);
	SetHandlerLoopback( NULL, EReqUnsubsribeTestAi							,	StatePlayerBase::onReqUnsubscribeTest						, false);
	SetHandlerLoopback( NULL, EReqGameChaosCastleRingout					,	StatePlayerBase::onReqChaosCastleRingout					, false);
	SetHandlerLoopback( NULL, EReqGameNpcPortal							    ,	StatePlayerBase::onReqGameNpcPortal							, false);
	SetHandlerLoopback( NULL, EReqGameSaveCoachMark						    ,	StatePlayerBase::onReqGameSaveCoachMark						, false);
	SetHandlerLoopback( NULL, EResDbSaveCoachMarks							,	StatePlayerBase::onResDbSaveCoachMark						, false);
	SetHandlerLoopback( NULL, EReqDungeonGuideMove						    ,	StatePlayerBase::onReqDungeonGuideMove						, false);
	SetHandlerLoopback( NULL, EReqGameShineSkillCast					    ,	StatePlayerBase::onReqGameShineSkillCast					, false);
	SetHandlerLoopback( NULL, EReqEndlessSelectStage					    ,	StatePlayerBase::onReqEndlessSelectStage					, false);
	SetHandlerLoopback( NULL, EReqPVEMissionMapReward						,	StatePlayerBase::onReqPVEMissionmapReward					, false);
	SetHandlerLoopback( NULL, EReqPetRegister							    ,	StatePlayerBase::onReqPetRegister							, false);
	SetHandlerLoopback( NULL, EReqPetUpdateSpawnState					    ,	StatePlayerBase::onReqPetUpdateSpawnState					, false);
	SetHandlerLoopback( NULL, EReqPetRename								    ,	StatePlayerBase::onReqPetRename								, false);
	SetHandlerLoopback( NULL, EReqGamePetChangeFellow					    ,	StatePlayerBase::onReqGamePetChangeFellow					, false);
	SetHandlerLoopback( NULL, EReqGamePetExpandFellowSlot				    ,	StatePlayerBase::onReqGamePetExpandFellowSlot				, false);

	SetHandlerLoopback( NULL, EczReqPetGrowthRegister						,	StatePlayerBase::onReqGamePetGrowthRegister					, false);
	SetHandlerLoopback( NULL, EczReqPetBoosterStart							,	StatePlayerBase::onReqPetBoosterStart						, false);
	SetHandlerLoopback( NULL, EczReqPetQuestInfo							,	StatePlayerBase::onReqPetQuestInfo							, false);
	SetHandlerLoopback( NULL, EczReqPetQuestInfoChange						,	StatePlayerBase::onReqPetQuestInfoChange					, false);
	SetHandlerLoopback( NULL, EczReqPetQuestStart							,	StatePlayerBase::onReqPetQuestStart							, false);
	
	SetHandlerLoopback( NULL, EReqPetUpgrade							    ,	StatePlayerBase::onReqPetUpgrade							, false);
	SetHandlerLoopback( NULL, EReqGamePetGrow							    ,	StatePlayerBase::onReqGamePetGrow							, false);
	SetHandlerLoopback( NULL, EReqSoulSkillDistributePoint				    ,	StatePlayerBase::onReqSoulSkillDistributePoint				, false);
	SetHandlerLoopback( NULL, EReqSoulSkillResetPoint					    ,	StatePlayerBase::onReqSoulSkillResetPoint					, false);
	SetHandlerLoopback( NULL, EReqPortalEtcInfo							    ,	StatePlayerBase::onReqPortalEtcInfo							, false);
	SetHandlerLoopback( NULL, EReqGameWhoLeaveNeedGroupMemberDamageReport	,	StatePlayerBase::onReqWhoLeaveNeedGroupMemberDamageReport	, false);
	SetHandlerLoopback( NULL, EwzNtfInitYourDamageReport						,	StatePlayerBase::onNtfInitYourDamageReport					, false);
	//SetHandlerLoopback( NULL, EResGameCheckAchievementCondition				,	StatePlayerBase::onResGameCheckAchievementCondition			, false);
	SetHandlerLoopback( NULL, EReqGameCharChangeTitle						,	StatePlayerBase::onReqGameCharChangeTitle					, false);
	SetHandlerLoopback( NULL, EReqUseTownPortalItem						    ,	StatePlayerBase::onReqUseTownPortalItem						, false);
	SetHandlerLoopback( NULL, EReqGameForceMoveStartComplete			    ,	StatePlayerBase::onReqGameForceMoveStartComplete			, false);
	SetHandlerLoopback( NULL, ENtfGameAchievementBurningStamina				,	StatePlayerBase::onNtfGameAchievementBurningStamina			, false);
	SetHandlerLoopback( NULL, ENtfGameAchievementDpsPoint					,	StatePlayerBase::onNtfGameAchievementDpsPoint				, false);
	SetHandlerLoopback( NULL, EResDbSurveyDelete						    ,	StatePlayerBase::onResDbSurveyDelete						, false);
	SetHandlerLoopback( NULL, EReqGameRequestSurvey						    ,	StatePlayerBase::onReqGameRequestSurvey						, false);
	SetHandlerLoopback( NULL, EReqGameSubmitSurvey						    ,	StatePlayerBase::onReqGameSubmitSurvey						, false);
	SetHandlerLoopback( NULL, EResDbInsertKnightage						    ,	StatePlayerBase::onResDbInsertKnightage						, false);
	SetHandlerLoopback( NULL, EResDbCheckExistKnightageName				    ,	StatePlayerBase::onResDbCheckExistKnightageName				, false);
	SetHandlerLoopback( NULL, EResDbLoadKnightageItems					    ,	StatePlayerBase::onResDbLoadKnightageItems					, false);
	SetHandlerLoopback( NULL, EResDbAddKnightageContributionPoint		    ,	StatePlayerBase::onResDbAddKnightageContributionPoint		, false);
	SetHandlerLoopback( NULL, EReqKnightageGetCreationRequirement		    ,	StatePlayerBase::onReqKnightageGetCreationRequirement		, false);
	SetHandlerLoopback( NULL, EReqKnightageCheckKnightageName			    ,	StatePlayerBase::onReqKnightageCheckKnightageName			, false);
	SetHandlerLoopback( NULL, EReqKnightageCreate						    ,	StatePlayerBase::onReqKnightageCreate						, false);
	SetHandlerLoopback( NULL, EReqKnightageSpendItemsAltar				    ,	StatePlayerBase::onReqKnightageSpendItemsAltar				, false);
	SetHandlerLoopback( NULL, EReqKnightageOpenStorage					    ,	StatePlayerBase::onReqKnightageOpenStorage					, false);
	SetHandlerLoopback( NULL, EReqKnightageCloseStorage					    ,	StatePlayerBase::onReqKnightageCloseStorage					, false);
	SetHandlerLoopback( NULL, EResKnightageStorageUsingStart			    ,	StatePlayerBase::onResKnightageStorageUsingStart			, false);
	SetHandlerLoopback( NULL, ENtfKnightageStorageClosed				    ,	StatePlayerBase::onNtfKnightageStorageClosed				, false);
	SetHandlerLoopback( NULL, ENtfKnightageAddContributionPoint			    ,	StatePlayerBase::onNtfKnightageAddContributionPoint			, false);
	SetHandlerLoopback( NULL, ENtfKnightageUpdateMember					    ,	StatePlayerBase::onNtfKnightageUpdateMember					, false);
	SetHandlerLoopback( NULL, EReqGameFindPartyPromotion				    ,	StatePlayerBase::onReqGamePublicityFindParty				, false);
	SetHandlerLoopback( NULL, EResGameFindPartyPromotionWorld				,	StatePlayerBase::onResGameFindPartyPromotionWorld			, false);
	SetHandlerLoopback( NULL, EReqGamePersonalPlayScene					    ,	StatePlayerBase::onReqGamePersonalPlayScene					, false);
	SetHandlerLoopback( NULL, EReqDungeonMatchPortalRequirementCheck	    ,	StatePlayerBase::onReqDungeonMatchPortalRequirementCheck	, false);
	SetHandlerLoopback( NULL, EReqDungeonMatchPartySyncMember			    ,	StatePlayerBase::onReqDungeonMatchPartySyncMember			, false);
	SetHandlerLoopback( NULL, EReqChangeTutorialGuide					    ,	StatePlayerBase::onReqChangeTutorialGuide					, false);
	SetHandlerLoopback( NULL, ELoopbackBeginPortalWhenInvalidRequirement	,	StatePlayerBase::onReqBeginPortalWhenInvalidRequirement		, false);
	SetHandlerLoopback( NULL, EReqGameDuelRequest						    ,	StatePlayerBase::onReqGameDuelRequest						, false);
	SetHandlerLoopback( NULL, ENtfGameDuelMatch							    ,	StatePlayerBase::onNtfGameDuelMatch							, false);
	SetHandlerLoopback( NULL, EReqGetDailyLoginRewardResult					,	StatePlayerBase::onReqGetDailyLoginRewardResult				, false);
	SetHandlerLoopback( NULL, EReqGameGetAchievementRewardItem			    ,	StatePlayerBase::onReqGameGetAchievementRewardItem			, false);
	SetHandlerLoopback( NULL, EReqColosseum33PVPDevMSG						,	StatePlayerBase::onReqSectorController						, false);
	SetHandlerLoopback( NULL, EReqColosseum33PVPGiveUp						,	StatePlayerBase::onReqSectorController						, false);
	SetHandlerLoopback( NULL, EReqGameColosseumBattleTargetSelect			,	StatePlayerBase::onReqSectorController						, false);
	SetHandlerLoopback( NULL, EReqGameColosseumConsecutiveBattle			,	StatePlayerBase::onReqSectorController						, false);
	SetHandlerLoopback( NULL, EReqGameColosseumBattleLeave					,	StatePlayerBase::onReqSectorController						, false);
	SetHandlerLoopback( NULL, EReqGameColosseumReSearchTarget				,	StatePlayerBase::onReqSectorController						, false);
	SetHandlerLoopback( NULL, EReqGameColosseumReSearchPopup				,	StatePlayerBase::onReqSectorController						, false);
	SetHandlerLoopback( NULL, EReqSelectMazeChangePopup						,	StatePlayerBase::onReqSectorController						, false);
	SetHandlerLoopback( NULL, EReqGotoMazeBossBlock							,	StatePlayerBase::onReqSectorController						, false);
	SetHandlerLoopback( NULL, EReqQuickReviveToPartyMember					,	StatePlayerBase::onReqGameQuickReviveToPartyMember			, false);
	SetHandlerLoopback( NULL, EResDbSaveChangeCharMoney						,	StatePlayerBase::onResDbSaveChangeCharMoney					, false );
	SetHandlerLoopback( NULL, EReqExchangeItemOpenUI						,	StatePlayerBase::onReqExchangeItemOpenUI					, false);
	SetHandlerLoopback( NULL, EwzNtfExchangeItemSold						,   StatePlayerBase::onEwzNtfExchangeItemSold					, false);
	SetHandlerLoopback( NULL, ENtfUseMegaphoneItem							,	StatePlayerBase::onNtfUseMegaphoneItem						, false);
	SetHandlerLoopback( NULL, ENtfWorldDailyMission							,	StatePlayerBase::onNtfWorldDailyMission						, false);
	SetHandlerLoopback( NULL, ENtfWorldChangeDailyMission					,	StatePlayerBase::onNtfWorldChangeDailyMission				, false);
	SetHandlerLoopback( NULL, EReqDailyMission								,	StatePlayerBase::onReqDailyMission							, false);
	SetHandlerLoopback( NULL, EReqGetDailyMissionReward						,	StatePlayerBase::onReqGetDailyMissionReward					, false);
	SetHandlerLoopback( NULL, EResDbGetCharDailyMission						,	StatePlayerBase::onResDbGetCharDailyMission					, false);	
	SetHandlerLoopback( NULL, EResDbInsertAchievementReward					,	StatePlayerBase::onResDbInsertAchievementReward				, false);
	SetHandlerLoopback( NULL, EResDbInsertAchievementGradeReward			,	StatePlayerBase::onResDbInsertAchievementGradeReward		, false);
	SetHandlerLoopback( NULL, EReqGameStartEmergencyEscape					,	StatePlayerBase::onReqGameStartEmergencyEscape				, false);
	SetHandlerLoopback( NULL, EReqGameEmergencyEscape						,	StatePlayerBase::onReqGameEmergencyEscape					, false);
	SetHandlerLoopback( NULL, EReqGameSkipPlayScene							,	StatePlayerBase::onReqGameSkipPlayScene						, false);
	SetHandlerLoopback( NULL, ELoopbackDebugMsg								,	StatePlayerBase::onNtfDebugMsg								, false);
	SetHandlerLoopback( NULL, EReqRankingStoneBlessToZone					,	StatePlayerBase::onReqRankingStoneBlessToZone				, false);
	SetHandlerLoopback( NULL, EReqRevivePreInfo								,	StatePlayerBase::OnReqRevivePreInfo							, false);
	SetHandlerLoopback( NULL, EReqRaidApplyRaid								,	StatePlayerBase::OnReqRaidApplyRaid							, false );
	SetHandlerLoopback( NULL, EReqItemIdentity								,	StatePlayerBase::onReqItemIdentity							, false );
	SetHandlerLoopback( NULL, EReqWShopItemJobBeginZone						,	StatePlayerBase::onReqWShopItemJobBeginZone					, false);
	SetHandlerLoopback( NULL, EResDbWShopPickUpItemBeginTransaction			,	StatePlayerBase::onResDbWShopPickUpItemBeginTransaction		, false);
	SetHandlerLoopback( NULL, EReqWShopItemJobEndZone						,	StatePlayerBase::onReqWShopItemJobEndZone					, false);
	SetHandlerLoopback( NULL, EReqItemExpandPeriod							,	StatePlayerBase::onReqUseExpanndItem						, false );
	SetHandlerLoopback( NULL, EReqItemCubeOpen								,	StatePlayerBase::onReqItemCubeEvent							, false );	
	SetHandlerLoopback( NULL, EReqItemCubeCheck								,	StatePlayerBase::onReqItemCubeEvent							, false );
	SetHandlerLoopback( NULL, EReqItemCubeClose								,	StatePlayerBase::onReqItemCubeEvent							, false );
	SetHandlerLoopback( NULL, EReqCreateAmplifcationStone					,	StatePlayerBase::onReqCreateAmplifactionStone				, false );
	SetHandlerLoopback( NULL, EReqAccelerationAmplifcationStone				,	StatePlayerBase::onReqAccelerationAmplifactionStone			, false );
	SetHandlerLoopback( NULL, EReqGetAmplifcationStoneList					,	StatePlayerBase::onReqGetAmplifactionStoneList				, false );
	SetHandlerLoopback( NULL, EReqUpgradeTranscendStone						,	StatePlayerBase::onUpgradeTranscedStone						, false );
	SetHandlerLoopback( NULL, EReqEquipTranscendStone						,	StatePlayerBase::onReqEquipTranscendStone					, false );
	SetHandlerLoopback( NULL, EReqReciveAmplifcationStone					,	StatePlayerBase::onReqReceiveAmplifactionStone				, false );
	SetHandlerLoopback( NULL, EReqUseMembershipServiceItem					,	StatePlayerBase::onReqUseMembershipServiceItem				, false );
	SetHandlerLoopback( NULL, EReqGameMembershipDailyReward					,	StatePlayerBase::onReqGameMembershipDailyReward				, false);
	SetHandlerLoopback( NULL, EwzNtfResetMembership							,	StatePlayerBase::onNtfResetMembership						, false);
	SetHandlerLoopback( NULL, EReqMembershipActionFlag						,	StatePlayerBase::onReqMembershipActionFlag					, false);
	SetHandlerLoopback( NULL, EReqResetTrascendStone						,	StatePlayerBase::onReqResetTrascendStone					, false );
	

	SetHandlerLoopback( NULL, EResDbInsertEventItem							,	StatePlayerBase::onResDbInsertEventItem						, false );
	SetHandlerLoopback( NULL, EReqEventItemPage								,	StatePlayerBase::onReqEventItemPage							, false );
	SetHandlerLoopback( NULL, EResDbLoadEventItemPage						,	StatePlayerBase::onResDbLoadEventItemPage					, false );
	SetHandlerLoopback( NULL, EReqEventItemPickUp							,	StatePlayerBase::onReqEventItemPickUp						, false );
	SetHandlerLoopback( NULL, EResDbGetNewEventItemCount					,	StatePlayerBase::onResDbGetNewEventItemCount				, false );
	SetHandlerLoopback( NULL, EwzNtfUpdatePcRoomInfo						,	StatePlayerBase::onNtfUpdatePcRoomInfo						, false );
	
	SetHandlerLoopback( NULL, EwzNtfWShopUpdateInventoryInquiry				,	StatePlayerBase::onRwzNtfWShopUpdateInventoryInquiry		, false );
	SetHandlerLoopback( NULL, EReqItemWeaponFusion							,	StatePlayerBase::onReqItemWeaponFusion						, false );
	SetHandlerLoopback( NULL, EReqDailyBuyList								,	StatePlayerBase::onReqShopDailyBuyList						, false );
	SetHandlerLoopback( NULL, EReqKnightageFollowing						,	StatePlayerBase::onReqKnightageFollowing					, false );
	SetHandlerLoopback( NULL, EReqKnightageGiveTrophy						,	StatePlayerBase::onReqKnightageGiveTrophy					, false );
	SetHandlerLoopback( NULL, EReqGameChatLinkInfo							,	StatePlayerBase::onReqGameChatInfoInfo						, false );
	SetHandlerLoopback( NULL, ENtfZoneKnightageChangeCapitalDominion		,	StatePlayerBase::onNtfKnightageChangeCapitalDominion		, false );	
	SetHandlerLoopback( NULL, EReqJoinKnightageDominion						,	StatePlayerBase::onReqJoinKnightageDominion					, false );
	SetHandlerLoopback( NULL, EReqRemoveTrascendStoneFailCount				,	StatePlayerBase::onReqTrascendStoneRemoveFailCount			, false );

	SetHandlerLoopback( NULL, EResDbUpdateSoulSkill						    ,	StatePlayerBase::onResDbUpdateSoulSkill						, false);
	SetHandlerLoopback( NULL, EResDbUpdateArtifact						    ,	StatePlayerBase::onResDbUpdateArtifact						, false);

	SetHandlerLoopback( NULL, EResDbAddCharTitleInfo					    ,	StatePlayerBase::onResDbAddCharTitleInfo					, false);
	SetHandlerLoopback(NULL, EResInsertExploredPortal						,	StatePlayerBase::onResInsertExploredPortal					, false);
	SetHandlerLoopback(NULL, ENtfZoneFollowerChanged						,	StatePlayerBase::onNtfZoneFollowerChanged					, false);

	SetHandlerLoopback(NULL, EReqBlackMarketItemBuyList, StatePlayerBase::onReqBlackMarketItemBuyList, false);

	SetHandlerLoopback(NULL, EReqAutoHackQuestionAnswer						, StatePlayerBase::onReqAutoHackQuestionAnswer						, false);
	SetHandlerLoopback(NULL, EReqChangeAutoHackQuestion						, StatePlayerBase::onReqChangeAutoHackQuestion						, false);
	SetHandlerLoopback(NULL, ENtfDevResetAutoHackPenalty					, StatePlayerBase::onNtfDevResetAutoHackPenalty						, false);
	SetHandlerLoopback(NULL, ENtfDevOpenPenaltyPopup						, StatePlayerBase::onNtfDevOpenPenaltyPopup							, false);

	SetHandlerLoopback(NULL, EReqEtherExtract								, StatePlayerBase::onReqEtherExtract							, false);
	SetHandlerLoopback(NULL, EReqEmotionBoxInfo								, StatePlayerBase::onReqEmotionBoxInfo							, false);
	SetHandlerLoopback(NULL, EReqEmotionBoxOpen								, StatePlayerBase::onReqEmotionBoxOpen							, false);
	SetHandlerLoopback(NULL, EReqEmotionBoxRearrange						, StatePlayerBase::onReqEmotionBoxRearrange						, false);
	SetHandlerLoopback(NULL, EwzResEmotionBoxReset							, StatePlayerBase::onResEmotionBoxReset							, false);


	SetHandlerLoopback(NULL, EReqMarbleInfo									, StatePlayerBase::onReqMarbleInfo								, false);
	SetHandlerLoopback(NULL, EReqMarbleThrowDice							, StatePlayerBase::onReqMarbleThrowDice							, false);
	SetHandlerLoopback(NULL, EReqMarbleArriveArea							, StatePlayerBase::onReqMarbleArriveArea						, false);
	SetHandlerLoopback(NULL, EReqMarbleSelectPos							, StatePlayerBase::onReqMarbleSelectPos							, false);
	SetHandlerLoopback(NULL, EReqMarbleOpenRewardBox						, StatePlayerBase::onReqMarbleOpenRewardBox						, false);
	SetHandlerLoopback(NULL, EwzResDicePlayRedzen							, StatePlayerBase::onResDicePlayRedzen							, false);

	
#ifdef NOT_HANDLE_CLIENT
#endif	
	SetHandlerLoopback( NULL, EReqGetExchangeEventInfo						, StatePlayerBase::oReqGetExchangeEventInfo						, false );
	SetHandlerLoopback( NULL, EReqGetExchangePuzzleEventInfo				, StatePlayerBase::oReqGetExchangePuzzleEventInfo				, false );
	SetHandlerLoopback( NULL, EReqExchangeEventItem							, StatePlayerBase::onReqExchangeEventItem						, false );
	SetHandlerLoopback( NULL, EReqGetStampEventInfo							, StatePlayerBase::onReqGetStampEventInfo						, false );
	SetHandlerLoopback( NULL, EReqStampPickUpItem							, StatePlayerBase::onReqStampPickUpItem							, false );
	SetHandlerLoopback( NULL, EReqStampPickUpItemByPuzzleEvent				, StatePlayerBase::onReqStampPickUpItemByPuzzleEvent			, false );
	SetHandlerLoopback( NULL, EReqJigsawPuzzleEvent							, StatePlayerBase::onReqJigsawPuzzleEvent						, false );
	SetHandlerLoopback( NULL, EReqResetPuzzle								, StatePlayerBase::onReqResetPuzzle								, false );
	SetHandlerLoopback( NULL, EResDbGetCharPuzzleInfo						, StatePlayerBase::onResDBGetCharPuzzleInfo						, false );
	SetHandlerLoopback( NULL, EResDbUpdatePuzzleBackground					, StatePlayerBase::onResDbUpdatePuzzleBackground				, false );
	SetHandlerLoopback( NULL, EReqWUEventStampInfo							, StatePlayerBase::onReqWUEventStampInfo						, false );
	SetHandlerLoopback( NULL, EReqWUEventStampPickup						, StatePlayerBase::onReqWUEventStampPickup						, false );
	SetHandlerLoopback( NULL, EResWUAccCountUpdate							, StatePlayerBase::onResWUAccCountUpdate						, false );
	SetHandlerLoopback( NULL, EReqChristmasEventInfo						, StatePlayerBase::onReqChristmasEventInfo						, false );
	SetHandlerLoopback( NULL, EReqChristmasEventBatteryCharge				, StatePlayerBase::onReqChristmasEventBatteryCharge				, false );
	SetHandlerLoopback( NULL, EReqChristmasEventTreeReward					, StatePlayerBase::onReqChristmasEventTreeReward				, false );
	SetHandlerLoopback( NULL, EReqChristmasEventTreePointReset				, StatePlayerBase::onReqChristmasEventTreePointReset			, false );
	SetHandlerLoopback( NULL, EReqSeasonInfo								, StatePlayerBase::onReqSeasonInfo								, false );
	SetHandlerLoopback( NULL, EReqSeasonMissionInfo							, StatePlayerBase::onReqSeasonMissionInfo						, false );
	SetHandlerLoopback( NULL, EReqSeasonMissionDetailInfo					, StatePlayerBase::onReqSeasonMissionDetailInfo					, false );
#ifdef SEASON_RENEWAL_by_jjangmo_180710
#else
	SetHandlerLoopback( NULL, EReqSeasonTierInfo							, StatePlayerBase::onReqSeasonTierInfo							, false );
#endif
	SetHandlerLoopback( NULL, EReqSeasonRewardInfo							, StatePlayerBase::onReqSeasonRewardInfo						, false );
	SetHandlerLoopback( NULL, EResDbSeasonMissionRewardAdd					, StatePlayerBase::onResDbSeasonMissionRewardAdd				, false );

	SetHandlerLoopback( NULL, EReqReturnUserReward							, StatePlayerBase::onReqReturnUserReward						, false );
	SetHandlerLoopback( NULL, ENtfReturnEvent								, StatePlayerBase::onNtfReturnEvent								, false );
	
	SetHandlerLoopback(NULL, EReqPrivateStorageTabUnlock, StatePlayerBase::onReqPrivateStorageTabUnlock, false);
	SetHandlerLoopback( NULL, EReqCharPosForCheat							, StatePlayerBase::onReqCharPosForCheat							, false );

	SetHandlerLoopback( NULL, EReqEventShopItemBuyList						, StatePlayerBase::onReqEventShopItemBuyList					, false );
	
	SetHandlerLoopback( NULL, EReqPSROpen									, StatePlayerBase::onReqPSROpen									, false );
	SetHandlerLoopback( NULL, EReqPSREvent									, StatePlayerBase::onReqPSREvent								, false );
	SetHandlerLoopback( NULL, EReqPSRCurInfo								, StatePlayerBase::onReqPSRCurInfo								, false );

	SetHandlerLoopback( NULL, EReqChangeTalismanDriverSlot					, StatePlayerBase::onReqChangeTalismanDriverSlot				, false );
	SetHandlerLoopback( NULL, EReqChangeTalismanDriverSlotPage				, StatePlayerBase::onReqChangeTalismanDriverSlotPage			, false );
	SetHandlerLoopback( NULL, EResDbUpdateTalismanDriver					, StatePlayerBase::onResDbUpdateTalismanDriver					, false );
	SetHandlerLoopback( NULL, EReqBookmarkTalisman							, StatePlayerBase::onReqBookmarkTalisman						, false );
	SetHandlerLoopback( NULL, EResDbUpdateTalismanBookmark					, StatePlayerBase::onResDbUpdateTalismanBookmark				, false );
	SetHandlerLoopback( NULL, EResDbDeleteTalismans							, StatePlayerBase::onResDbDeleteTalismans						, false );
	SetHandlerLoopback( NULL, EReqRewardTalismanUnit						, StatePlayerBase::onReqRewardTalismanUnit						, false );

#ifdef __Patch_Talisman_V2_Growth_by_kangms_20181207
	SetHandlerLoopback( NULL, EczReqTalismanAwakenAction					, StatePlayerBase::onEczReqTalismanAwakenAction					, false );
	SetHandlerLoopback( NULL, EczReqTalismanCoreConversion					, StatePlayerBase::onEczReqTalismanCoreConversion				, false );
	SetHandlerLoopback( NULL, EczReqTalismanGrowthReward					, StatePlayerBase::onEczReqTalismanGrowthReward					, false );
	SetHandlerLoopback( NULL, EResDbTalismanGrowthRewardDeleteAll			, StatePlayerBase::onResDbTalismanGrowthRewardDeleteAll			, false );
#endif//__Patch_Talisman_V2_Growth_by_kangms_20181207

	SetHandlerLoopback( NULL, EReqCharacterSlotExtend						, StatePlayerBase::onReqCharacterSlotExtend						, false );
	SetHandlerLoopback( NULL, EReqAirshipDefenceGameGiveUp					, StatePlayerBase::onReqSectorController						, false);

	SetHandlerLoopback( NULL, EReqBuffEventOpen								, StatePlayerBase::onReqBuffEventOpen							, false );
	SetHandlerLoopback( NULL, EReqBuffEventEffect							, StatePlayerBase::onReqBuffEventEffect							, false );
	SetHandlerLoopback( NULL, EResDbUpdateMakingHistory						, StatePlayerBase::onResDbUpdateMakingHistory					, false );

#pragma region GuideSystem
	SetHandlerLoopback(NULL, EResDbUpdateCharGuideSystem					, StatePlayerBase::onResDbUpdateCharGuideSystem					, false );
	SetHandlerLoopback(NULL, EResDbDeleteCharGuideSystem					, StatePlayerBase::onResDbDeleteCharGuideSystem					, false );
	SetHandlerLoopback(NULL, EReqGameGuideSystemGetReward					, StatePlayerBase::onReqGameGuideSystemGetReward				, false );
#pragma endregion GuideSystem

	SetHandlerLoopback( NULL, EReqFreeMopup									, StatePlayerBase::onReqFreeMopup								, false );
	SetHandlerLoopback( NULL, EReqGameSystemReturnUserGetReward				, StatePlayerBase::onReqGameSystemReturnUserGetReward			, false );
	SetHandlerLoopback( NULL, EReqGameSetItemSellFalg						, StatePlayerBase::onReqGameShopSettingOpenOrClase				, false );

	SetHandlerLoopback( NULL, EResDbExchangeItemRegisteredList				, StatePlayerBase::onResDbExchangeItemRegisteredList			, false );	
	SetHandlerLoopback( NULL, EResDbExchangeItemSoldList					, StatePlayerBase::onResDbExchangeItemSoldList					, false );
	SetHandlerLoopback(NULL, EczReqNoticePopUpInfo,		StatePlayerBase::onEczReqNoticePopUpInfo, false);
	SetHandlerLoopback(NULL, EResDbNoticePopUpInfoLoad, StatePlayerBase::onEResDbNoticePopUpInfoLoad, false);
	SetHandlerLoopback(NULL, EczNtfPetInfoUpdateAll,		StatePlayerBase::onEczNtfPetInfoUpdateAll, false);
#ifdef __Patch_Talisman_ChangeOwner_by_jason_180906
	SetHandlerLoopback(NULL, EResDBTalismanChangeOwner,						StatePlayerBase::onResDbItemTransaction, false);
#endif

	SetHandlerLoopback(NULL, EczReqTreasureOpen,							StatePlayerBase::onEventTreasureCommon, false);
	SetHandlerLoopback(NULL, EczReqTreasureClose,							StatePlayerBase::onEventTreasureCommon, false);
	SetHandlerLoopback(NULL, EczReqTreasurePickup,							StatePlayerBase::onEventTreasureCommon, false);
	SetHandlerLoopback(NULL, EczReqTreasureRefreshSlot,						StatePlayerBase::onEventTreasureCommon, false);
	SetHandlerLoopback(NULL, EczReqTreasureItemUseRefillRefreshCount,		StatePlayerBase::onEventTreasureCommon, false);
	SetHandlerLoopback(NULL, EResDbTreasureLoad,							StatePlayerBase::onEventTreasureCommon, false);	
	SetHandlerLoopback(NULL, EResDbTreasureRefreshSlot,						StatePlayerBase::onEventTreasureCommon, false);	
	SetHandlerLoopback(NULL, EResDbTreasureWeeklyReset,						StatePlayerBase::onEventTreasureCommon, false);
	SetHandlerLoopback(NULL, EResDbTreasureRefillAllUserCountGmCommand,		StatePlayerBase::onEventTreasureCommon, false);	

	SetHandlerLoopback(NULL, EResDbTreasureItemUseRefillRefreshCount,		StatePlayerBase::onResDbItemTransaction, false);
	SetHandlerLoopback(NULL, EResDbTreasurePickup,							StatePlayerBase::onResDbItemTransaction, false);
	SetHandlerLoopback(NULL, EczItemRuneFusion,								StatePlayerBase::onEczItemRuneFusion, false);

	SetHandlerLoopback(NULL, EwzReqCheckMatchJoinPopup, StatePlayerBase::onEwzReqCheckMatchJoinPopup, false);
#ifdef ABILITY_RENEWAL_20180508
	SetHandlerLoopback(NULL, EczOpenBraceletSlot,							StatePlayerBase::onReqOpenBraceletSlot, false);
	SetHandlerLoopback(NULL, EczUseOptionScroll,							StatePlayerBase::onReqUseOpenScroll, false);
#endif
#ifdef TUROTIAL_SKIP_by_cheolhoon_180605
	SetHandlerLoopback(NULL, EczReqTutorialSkip,							StatePlayerBase::onReqTutorialSkip, false);
#endif

#ifdef	__Patch_Limited_Banner_Shop_robinhwp
	SetHandlerLoopback(NULL, EResDBInsertLimitedBannerGoods					, StatePlayerBase::onResDBInsertLimitedBannerGoods, false);
	SetHandlerLoopback(NULL, EReqBuyLimitedBannerGoods						, StatePlayerBase::onReqBuyLimitedBannerGoods, false);
#endif	__Patch_Limited_Banner_Shop_robinhwp
#ifdef __lockable_item_181001
	SetHandlerLoopback(NULL, EczLockableItem								, StatePlayerBase::onLockableItem, false);

#endif

#ifdef __Renewal_Artifact_Level_Transfer_by_kangms_181001
	SetHandlerLoopback(NULL, EResDbArtifactLevelTransfer					, StatePlayerBase::onResDbItemTransaction, false);
#endif//__Renewal_Artifact_Level_Transfer_by_kangms_181001

#ifdef __Patch_Tower_of_dawn_eunseok_2018_11_05
	SetHandlerLoopback(NULL, EReqTowerOfDawnUsePlayChance, StatePlayerBase::onReqTowerOfDawnUsePlayChance, false);
	SetHandlerLoopback(NULL, EResDbGetTowerOfDawnRewardPoint, StatePlayerBase::onResDBGetTowerOfDawnRewardPoint, false);
#endif //__Patch_Tower_of_dawn_eunseok_2018_11_05
	
#ifdef __Patch_Event_Buff_Add_Benefit_by_jjangmo_20181122
	SetHandlerLoopback( NULL, EczReqEventBuffGiftGetReward, StatePlayerBase::onReqEventBuffGiftGetReward, false );
#endif

#ifdef __Patch_Daily_Time_Connection_eunseok_2018_12_03
	SetHandlerLoopback(NULL, EReqGetDailyTimeConnectionRewardToZone, StatePlayerBase::onEReqGetDailyTimeConnectionRewardToZone, false);
	SetHandlerLoopback(NULL, EReqGetDailyTimeConnectionStampRewardToZone, StatePlayerBase::onEReqGetDailyTimeConnectionStampRewardToZone, false);
#endif //__Patch_Daily_Time_Connection_eunseok_2018_12_03

#ifdef __Patch_WebOfGod_by_jason_20181213
	setHandler_WobOfGod();	
	SetHandlerLoopback(NULL, EResDbUspSystemMailReservationLoad, StatePlayerBase::onEResDbUspSystemMailReservationLoad, false);
#endif
	
#ifdef __Patch_Legend_Of_Bingo_by_jaekwan_20181126
	setHandler_LegendOfBingo();
#endif // __Patch_Legend_Of_Bingo_by_jaekwan_20181126

#ifdef __Patch_Talismanbox_Ticket_Reward_System_by_ch_181224
	SetHandlerLoopback(NULL, EReqTalismanboxTicketRewardInfo, StatePlayerBase::onReqTalismanboxTicketRewardInfo, false);
	SetHandlerLoopback(NULL, EReqProvideTalismanboxTicketReward, StatePlayerBase::onReqProvideTalismanboxTicketReward, false);
	SetHandlerLoopback(NULL, EResDbUspGetTalismanboxTicketRewardInfo, StatePlayerBase::onResDbUspGetTalismanboxTicketRewardInfo, false);
#endif
#ifdef __Patch_Mail_HwaYeong_20190117
	SetHandlerLoopback( NULL, EReqGameMailReadAndGetAll, StatePlayerBase::onReqGameMailReadAndGetAll, false );
#endif
	


#ifdef __Patch_SetItem_Renewal_by_kangms_2019_4_10
	SetHandlerLoopback(NULL, EczReqSetCardOpen,						StatePlayerBase::onEczReqSetCardOpen,					false);
	SetHandlerLoopback(NULL, EczReqSetCardEquip,					StatePlayerBase::onEczReqSetCardEquip,					false);
	SetHandlerLoopback(NULL, EResDbSetCardUpdateList,				StatePlayerBase::onResDbSetCardUpdateList,				false);
	SetHandlerLoopback(NULL, EResDbSetCardDeleteList,				StatePlayerBase::onResDbSetCardDeleteList,				false);
	SetHandlerLoopback(NULL, EResDbDefaultOpenSetCardListInsert,	StatePlayerBase::onResDbDefaultOpenSetCardListInsert,	false);
#endif//__Patch_SetItem_Renewal_by_kangms_2019_4_10

#ifdef __Patch_Mercler_Secret_Shop_by_ch_2019_04_24	
	SetHandlerLoopback(NULL, EReqMerclerSecretShopInfo, StatePlayerBase::onReqMerclerSecretShopInfo, false);
	SetHandlerLoopback(NULL, EReqBuyMerclerSecretShop, StatePlayerBase::onReqBuyMerclerSecretShop, false);
#endif // __Patch_Mercler_Secret_Shop_by_ch_2019_04_24
}

StatePlayerBase::~StatePlayerBase()
{ 
	CounterDec("StatePlayerBase");
}

Bool StatePlayerBase::init()
{
	setupMoveDispatch();	

	return true;
}

State* StatePlayerBase::onEvent( EventPtr& e )
{
	if (m_dispatcher.Dispatch(e))
		return NULL;

#ifdef BES_SPEED_HACK_TEST 
	
	if (e->GetEvent() == ProtocolGame::EREQ_GAME_MOVE_START ||
		e->GetEvent() == ProtocolGame::EREQ_GAME_MOVE_STOP)
	{
		if (SERVER.GetMovePacketDealyMax() > 0)
		{
			
			DelayEvent event;
			event.StartTick = GetTickCount();
			event.delay = mu2::GetRandBetween(SERVER.GetMovePacketDealyMin(), SERVER.GetMovePacketDealyMax()) * 0.001f;
			e->SetKey(static_cast<UInt32>(event.delay* 1000.0f)  );
			event.event = e;
			m_Eventqueue.push(event);
			e->SetHandled(true);
			return NULL;
		}
	}
#endif 
	switch ( e->GetEvent() )
	{
	case ProtocolGame::EREQ_GAME_MOVE_START:
		m_impl->moveHandler.OnMoveStart( e );	
		e->SetHandled(true);
		break;

	case ProtocolGame::EREQ_GAME_MOVE_STOP:
		m_impl->moveHandler.OnMoveStop( e );			
		e->SetHandled(true);
		break;

	case ProtocolGame::EREQ_GAME_MOVE_TO_POSITION:
		m_impl->moveHandler.OnMoveToPosition( e );			
		break;

	case ProtocolGame::ENTF_CHANGE_SPEED:
		m_impl->moveHandler.OnChangeSpeed(e);
		break;
	default:
		//e->SetHandled(false);		
		return GetParent();
	}

	return NULL;
}

void StatePlayerBase::onEnter()
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );
		
	if (player->GetJoinContext() == JoinContext::JC_CHECKIN)
	{
		ActionPlayerLoad* playerLoad = GetEntityAction(GetOwnerPlayer());
		playerLoad->CompleteLoadingActionForGuideSystemAndAchievement();
	}

#ifdef	__Patch_Limited_Banner_Shop_robinhwp
	player->GetPlayerLimitedBannerGoodsAction().SendListToClient();
#endif	__Patch_Limited_Banner_Shop_robinhwp
	

#ifdef __Reincarnation__jason_180628__
	theNotifierGameContents.OnPlayer_EnteredStatePlayerBase(*player, player->GetJoinContext());
#endif
	player->OnEnteredStatePlayerBase();
}

State* StatePlayerBase::onTick()
{
	static const Tick SAVE_CHAR_INFO_TICK = 300*1000;//120000;

	Tick currentTick = GetTickCount();

	EntityPlayer* owner = GetOwnerPlayer();
	
	if ( currentTick - m_impl->saveCharInfoTick > SAVE_CHAR_INFO_TICK )
	{
		m_impl->saveCharInfoTick = currentTick;
		saveCharInfo();
	}

	if ( currentTick - m_impl->checkUseItemTick > 100 )
	{
		owner->GetAction< ActionPlayerUseItem >()->Update( currentTick );
		m_impl->checkUseItemTick = currentTick;
	}

	//!
	owner->GetAction<ActionPlayerDurability>()->Update( currentTick );
	owner->GetAction<ActionPlayerExpiryItem>()->Update( currentTick );
	owner->UpdatePlayTime();
	owner->GetAction<ActionPlayerMembership>()->Update();
	owner->GetAction<ActionPlayerMissionMapTier>()->Update();
	owner->GetAction<ActionPlayerAutoHackChecker>()->Update();
	owner->GetAction<ActionPlayerInventory>()->Update();
#ifdef BES_SPEED_HACK_TEST 
	if (m_Eventqueue.empty() == false)
	{
		DelayEvent event = m_Eventqueue.front();
		if (event.delay < (GetTickCount() - event.StartTick) * 0.001f)
		{
			switch (event.event->GetEvent())
			{
			case ProtocolGame::EREQ_GAME_MOVE_START:
				//MU2_INFO_LOG(LogCategory::CONTENTS, "이동중 딜레이 t:%0.3f", event.delay);
				m_impl->moveHandler.OnMoveStart(event.event);
				break;
			case ProtocolGame::EREQ_GAME_MOVE_STOP:
				//MU2_INFO_LOG(LogCategory::CONTENTS, "정지 딜레이 t:%0.3f", event.delay);
				m_impl->moveHandler.OnMoveStop(event.event);
				break;
			}
			m_Eventqueue.pop();
		}
	}
#endif 

	owner->OnUpdatedStatePlayerBase();

	return GetParent();
}

void StatePlayerBase::onExit()
{
	EntityPlayer* owner = GetOwnerPlayer();
	if (nullptr == owner)
	{
		return;
	}
	ActionSkillControl* actControl = GetEntityAction(owner);
	if (nullptr == actControl)
	{
		return;
	}

	if (0 < actControl->GetServantNpcCount())
	{
		//sendMyServantInfo();
		actControl->RemoveAllServantNpc();
	}

	owner->GetAction<ActionPlayerPetManage>()->OnExitZone();

	m_impl->moveHandler.ResetSpeedHackChecker();
}

void StatePlayerBase::saveCharInfo()
{	
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );
	VALID_RETURN(player->GetPlayerAction().IsModifiedCharInfo(), );

	player->SaveCharInfoToDb();
}

void StatePlayerBase::onReqGameSyncDirection( EventPtr& e )
{
	EReqGameSyncDirection* req = static_cast<EReqGameSyncDirection*>( e.RawPtr() );

	EntityPlayer* player = GetOwnerPlayer();
	

	player->SetRotation(req->direction);

	ENtfGameSyncDirection* ntf = NEW ENtfGameSyncDirection;
	ntf->entityId = player->GetId();
	ntf->direction = req->direction;
	ntf->castLocation = req->castLocation;
	player->GetSectorAction().NearGridCast(EventPtr(ntf));
}

void StatePlayerBase::onReqEquipItem( EventPtr& e )
{
	EntityPlayer* owner = GetOwnerPlayer();

	std::wstring sysMsg;
	ErrorItem::Error eError = reqEquipItem( owner, e, sysMsg );
	if ( eError != ErrorItem::SUCCESS )
	{
		if( ErrorItem::E_REFER_SYSTEM_MSG == eError)
		{
			SendSystemMsg( owner, SystemMessage( L"sys", sysMsg) );			
		}
		else if ( ErrorItem::E_TRADING == eError )
		{
			SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_UserTrade_Invalid_Not_Work" ) );
		}	
		else if ( ErrorItem::E_UNIQUE_ITEM_OPTION_ERROR == eError )
		{
			SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_ItemSkill_Err_Duplication" ) );

		}

		EReqEquip* req = static_cast< EReqEquip* >( e.RawPtr() );

		EResEquip* res = NEW EResEquip;
		res->result = eError;		
		res->item.itemId = req->item.itemId;
		res->item.indexItem = req->item.indexItem;
		res->item.SetPos(req->item.GetPos());
		res->item.SetCurStackCount(req->item.GetCurStackCount());
		res->item.enchant = req->item.enchant;
		res->item.effectId = req->item.effectId;

		SERVER.SendToClient( owner, EventPtr(res) );
	}
}

ErrorItem::Error StatePlayerBase::reqEquipItem( EntityPlayer* player, EventPtr& e, std::wstring& sysMsg )
{
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound );
	VERIFY_RETURN(player->IsAlive(), ErrorItem::playerNotAlive);
	VERIFY_RETURN(player->IsNotTrading(), ErrorItem::E_TRADING);

	EReqEquip* req = static_cast< EReqEquip* >( e.RawPtr() );

	const ItemPlace& srcPlace = req->item.GetPlace();

	if(player->GetSectorAction().IsDisabledEquipItem() )
	{
		sysMsg.assign( L"Msg_Cannot_Change_Equipment" );
		return ErrorItem::E_REFER_SYSTEM_MSG;
	}

	ActionPlayerInventory& invenAction = player->GetInventoryAction();

	InvenEquip& equipInven = player->GetEquipInven();
		
	const ItemConstPtr srcItem = invenAction.GetItemByPlace(srcPlace);
	VERIFY_RETURN( srcItem, ErrorItem::itemNotFound);
	VALID_RETURN( srcItem->period.IsExpiredItem() == false, ErrorItem::E_EXPIRED_ITEM );

	ItemPos toPos = ItemPos(InvenType::EQUIP, req->eEquipType);

	ErrorItem::Error eError = ErrorItem::SUCCESS;

	if ( false == invenAction.IsPushable(toPos, srcItem))
	{	
		if ( srcItem->IsOneHandWeapon() && toPos.GetSlot() == SlotType::LWEAPON )
		{
			toPos.SetPos(toPos.GetInven(), SlotType::RWEAPON);
			VERIFY_RETURN(invenAction.IsPushable(toPos, srcItem), ErrorItem::IsPushableFailed);
		}
	}	

	const ItemConstPtr existedItem = invenAction.GetItem(toPos);
	if ( existedItem != nullptr )
	{
		ItemPlace source, target;
		srcItem->FillUp( source );
		existedItem->FillUp( target );

		eError = invenAction.EquipExchange( target, source );
		VERIFY_RETURN( eError == ErrorItem::SUCCESS, eError );
	}
	else
	{
		if ( toPos.GetSlot() == SlotType::LWEAPON && srcItem->GetDivision() != ItemDivisionType::SLAVE_WEAPON )
		{
			const ItemConstPtr rWeapon = equipInven.GetItemBySlot(SlotType::RWEAPON );
			if ( rWeapon )
			{
				if ( rWeapon->IsTwoHandWeapon() || srcItem->IsMasterWeapon() )
				{
					ItemPlace source, target;
					srcItem->FillUp( source );
					rWeapon->FillUp( target );

					eError = player->GetInventoryAction().EquipExchange( target, source );
					return eError;
				}				
			}
		}
		TransactionPtr transPtr( NEW ItemUserEquipTransaction(__FUNCTION__, __LINE__, player, LogCode::E_ITEM_EQUIP, srcItem->GetItemId(), srcItem->GetPos(), toPos.GetSlot()) );
		
		if ( !TransactionSystem::Register( transPtr ) )
		{	
			//sysMsg.assign( player->GetInventoryAction().GetLastSystemMessage() );
			return ErrorItem::E_TRANSACION_FAILED;
		}
	}	
	
	return ErrorItem::SUCCESS;
}

void StatePlayerBase::onReqUnequipItem( EventPtr& e )
{
	EReqUnequip* req = static_cast< EReqUnequip* >( e.RawPtr() );
	EntityPlayer* owner = GetOwnerPlayer();

	ErrorItem::Error eError = reqUnequipItem( owner, req );

	if ( eError != ErrorItem::SUCCESS )
	{
		if (ErrorItem::E_TRADING == eError)
		{
			SendSystemMsg(owner, SystemMessage(L"sys", L"Msg_UserTrade_Invalid_Not_Work"));
		}

		EResUnequip* res = NEW EResUnequip;
		res->eError = eError;
		SERVER.SendToClient( owner, EventPtr(res) );
	}
}

ErrorItem::Error StatePlayerBase::reqUnequipItem(EntityPlayer* player, EReqUnequip* req )
{
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);	
	VERIFY_RETURN(player->IsAlive(), ErrorItem::playerNotAlive );
	VERIFY_RETURN(player->IsNotTrading(), ErrorItem::E_TRADING);
	VERIFY_RETURN(player->GetTutorialState() == TutorialState::COMPLETED, ErrorItem::MustTutoralStateIsCompleted);
		
	if( player->GetSectorAction().IsDisabledEquipItem() )
	{
		SendSystemMsg( player, SystemMessage( L"sys", L"Msg_Cannot_Change_Equipment" ) );
		return ErrorItem::E_FAILED;
	}	
	
	ErrorItem::Error eError = player->GetInventoryAction().UnEquip( req->item.GetPlace(), req->pos );
 	VERIFY_RETURN( eError == ErrorItem::SUCCESS, eError);
	
	return ErrorItem::SUCCESS;
}

void StatePlayerBase::onReqEquipExchangeItem( EventPtr& e )
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	EReqEquipExchange* req = static_cast< EReqEquipExchange* >( e.RawPtr() );	

	// Source가 Equip 기준
	if ( false == req->source.IsEquipBag() && req->target.IsEquipBag())
	{
		std::swap( req->source, req->target );
	}

	ErrorItem::Error eError = reqEquipExchangeItem( owner, req );
	if ( eError != ErrorItem::SUCCESS )
	{
		if ( ErrorItem::E_TRADING == eError )
		{
			SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_UserTrade_Invalid_Not_Work" ) );
		}	
		else if ( ErrorItem::E_UNIQUE_ITEM_OPTION_ERROR == eError )
		{
			SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_ItemSkill_Err_Duplication" ) );
		}

		EResEquipExchange* res = NEW EResEquipExchange;
		res->eError = eError;
		SERVER.SendToClient( owner, EventPtr(res) );
	}
}

ErrorItem::Error StatePlayerBase::reqEquipExchangeItem(EntityPlayer* player, EReqEquipExchange* req )
{
	VERIFY_RETURN(player && player->IsValid() && player->IsAlive(), ErrorItem::playerNotAlive);	
	VERIFY_RETURN(player->IsNotTrading(), ErrorItem::E_TRADING);
	VERIFY_RETURN(player->IsTutorialCompleted(), ErrorItem::MustTutoralStateIsCompleted);

	ActionPlayerInventory& invenAction = player->GetInventoryAction();

	Inven* srcInven = invenAction.GetInven(req->source);
	Inven* tarInven = invenAction.GetInven(req->target);
	
	VERIFY_RETURN(srcInven && tarInven, ErrorItem::inventoryNotFound);
	VERIFY_RETURN(srcInven->IsEquipBag() && false == tarInven->IsEquipBag(), ErrorItem::E_NOT_EQUIP_ITEM);

	const ItemConstPtr target = invenAction.GetItem( req->target);
	VERIFY_RETURN(target, ErrorItem::itemNotFound);

	VERIFY_RETURN(invenAction.IsPushable(req->source, target), ErrorItem::IsPushableFailed);

	ErrorItem::Error eError = invenAction.EquipExchange( req->source, req->target );
	VERIFY_RETURN( eError == ErrorItem::SUCCESS, eError );

	return ErrorItem::SUCCESS;
}

void StatePlayerBase::onReqMoveItem( EventPtr& e )
{
	EReqMove* req = static_cast< EReqMove* >( e.RawPtr() );

	EntityPlayer* owner = GetOwnerPlayer();

	ActionPlayerInventory* actInven = GetEntityAction( owner );

	ErrorItem::Error eError = actInven->Move( req->item, req->pos );
	
	if ( eError != ErrorItem::SUCCESS )
	{
		if ( ErrorItem::E_TRADING == eError )
			SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_UserTrade_Invalid_Not_Work") );

		EResMove* res = NEW EResMove;
		res->eError = eError;
		const ItemConstPtr item = actInven->GetItem(req->item);
		if (item)
		{
			item->FillUpItemData( res->item );
			res->pos = ItemPos( item->GetPos() );
		}
		SERVER.SendToClient( owner, EventPtr(res) );
	}
}

void StatePlayerBase::onReqRemoveItem( EventPtr& e )
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ErrorItem::Error eError = reqRemoveItem( owner, e );
	if ( eError != ErrorItem::SUCCESS)
	{
		if ( eError == ErrorItem::E_TRADING )
		{
			SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_UserTrade_Invalid_Not_Work") );
		}

		EResRemove* res = NEW EResRemove;
		res->eError = eError;
		SERVER.SendToClient( owner, EventPtr(res) );
	}
}

ErrorItem::Error StatePlayerBase::reqRemoveItem( EntityPlayer* owner, EventPtr& e )
{
	VERIFY_RETURN(owner && owner->IsValid(), ErrorItem::playerNotFound);
	VERIFY_RETURN(owner->IsTutorialCompleted(), ErrorItem::MustTutoralStateIsCompleted);
	VERIFY_RETURN(owner->IsNotTrading(), ErrorItem::E_TRADING);
	
	EReqRemove* req = static_cast< EReqRemove* >( e.RawPtr() );	
	
	const ItemConstPtr item = owner->GetInventoryAction().GetItem( req->item);
	VERIFY_RETURN( item, ErrorItem::itemNotFound );
	VERIFY_DO(item->IsDroppable(), SendSystemMsg(owner, SystemMessage(L"sys", L"Msg_Item_Unable_Drop")); return ErrorItem::IsDroppableFailed; );
	//VERIFY_DO(item->IsNotActivate(), SendSystemMsg(owner, SystemMessage(L"sys", L"Msg_Item_Invalid_Process")); return ErrorItem::IsActivate; );
#ifdef __lockable_item_181001
	VALID_DO(item->IsUnLocakble(), SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Item_lock_discard" ) ); return ErrorItem::E_ITEM_LOCKED; );
#endif

	ItemUserRemoveTransaction * removeTr = NEW ItemUserRemoveTransaction(__FUNCTION__, __LINE__, owner, LogCode::E_ITEM_REMOVE, true);
	TransactionPtr transPtr = TransactionPtr( removeTr );
	{
		ItemBag bag(*owner);

		removeTr->SetLogItem(*item);

		ErrorItem::Error eError = owner->GetInventoryAction().RemoveByPlace(req->item.GetPlace(), bag, req->item.GetCurStackCount());
		VERIFY_RETURN(eError == ErrorItem::SUCCESS, eError);

		VERIFY_RETURN(bag.Write(transPtr), ErrorItem::BagWriteFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::TranRegisterFailed);
	}

	return ErrorItem::SUCCESS;
}

 void StatePlayerBase::onReqPickupItem( EventPtr& e )
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	EczReqPickUp* req = static_cast< EczReqPickUp* >( e.RawPtr() );
		
	ErrorItem::Error eError = owner->GetInventoryAction().ReqPickupItem( req->entityIds );
	if ( eError != ErrorItem::SUCCESS )
	{
		EzcResPickUp* res = NEW EzcResPickUp;
		res->eError = eError;
		SERVER.SendToClient( owner, EventPtr( res ) );

	}
}

void StatePlayerBase::onReqExchangeItem( EventPtr& e )
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	EReqExchange* req = static_cast< EReqExchange* >( e.RawPtr() );
	
	ErrorItem::Error eError = owner->GetInventoryAction().SwapSlot( req->source, req->target );
	if ( eError != ErrorItem::SUCCESS )
	{
		if ( ErrorItem::E_TRADING == eError )
			SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_UserTrade_Invalid_Not_Work") );

		EResExchange* res = NEW EResExchange;
		res->result = eError;
		SERVER.SendToClient( owner, EventPtr(res) );
	}
}

void StatePlayerBase::onReqSeparateItem( EventPtr& e )
{
	EntityPlayer* owner = GetOwnerPlayer();

	ErrorItem::Error eError = reqSeparateItem( e );
	if ( eError != ErrorItem::SUCCESS)
	{
		if ( eError == ErrorItem::E_TRADING )
			SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_UserTrade_Invalid_Not_Work") );

		EResSeparate* res = NEW EResSeparate;
		res->eError = eError;
		SERVER.SendToClient( owner, EventPtr(res) );
	}	
}

ErrorItem::Error StatePlayerBase::reqSeparateItem( EventPtr& e )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);
	VERIFY_RETURN(player->IsNotTrading(), ErrorItem::E_TRADING);

	EReqSeparate* req = static_cast< EReqSeparate* >( e.RawPtr() );

	ErrorItem::Error eError = player->GetInventoryAction().Seperate( req->source, req->pos, req->count, NULL);
	VERIFY_RETURN( eError == ErrorItem::SUCCESS , eError );

	return ErrorItem::SUCCESS;
}

void StatePlayerBase::onReqOverlapItem( EventPtr& e )
{
	EntityPlayer* owner = GetOwnerPlayer();

	ErrorItem::Error eError = reqOverlapItem( e );
	if ( eError != ErrorItem::SUCCESS  )
	{
		if ( eError == ErrorItem::E_TRADING )
			SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_UserTrade_Invalid_Not_Work") );

		EResOverlap* res = NEW EResOverlap;
		res->eError = eError;
		owner->SendToClient(EventPtr(res) );
	}
}

ErrorItem::Error StatePlayerBase::reqOverlapItem( EventPtr& e )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);
	VERIFY_RETURN(player->IsNotTrading(), ErrorItem::E_TRADING);

	EReqOverlap* req = static_cast< EReqOverlap* >( e.RawPtr() );
	
	ErrorItem::Error eErrorItem = player->GetInventoryAction().Overlap( req->source, req->target );
	if( eErrorItem != ErrorItem::SUCCESS)
	{
		MU2_WARN_LOG(LogCategory::ITEM, "Overlap failed - player(%s), source(%s), target(%s)", 
			player->ToString().c_str(), req->source.ToString().c_str(), req->target.ToString().c_str());
	}
	return eErrorItem;
}

void StatePlayerBase::onReqGameCheat( EventPtr& e )
{
	EReqGameCheat* req = static_cast< EReqGameCheat* >( e.RawPtr() );
	VERIFY_RETURN(req, );
	if( req->cmd.empty() )
	{
		return ;
	}

	std::string out;

	StringUtil::Convert( req->cmd, out );	

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	//GMCommandSystem* gmCommandSystem = GetGMCommandSystem();
	theGMCommandSystem.Execute(owner, req->cmd);
}

void StatePlayerBase::onReqCastingCancel( EventPtr& e )
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	EReqCastingCancel* req = static_cast< EReqCastingCancel* >( e.RawPtr() );

	EResCastingCancel* res = NEW EResCastingCancel;
	res->itemId = req->itemId;
	res->eError = owner->GetUseItemAction().CastingCancel( req->itemId );
	owner->SendToClient(EventPtr(res) );

	theGameMsg.SendGameDebugMsg(owner, L"아이템 사용이 취소 되었습니다.\n");
}

void StatePlayerBase::onReqCharChangeName(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	EReqChangeCharName* req = static_cast<EReqChangeCharName*>(e.RawPtr());

	ErrorItem::Error eError = owner->GetUseItemAction().UseChangeCharName(req->targetItem, req->newName);
	if (eError != ErrorPlayer::SUCCESS)
	{
		EResChangeCharName* res = NEW EResChangeCharName;
		res->eError = eError;
		owner->SendToClient(EventPtr(res));
	}
}

void StatePlayerBase::onReqKnightageChangeName(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner, );

	EReqChangeKnightageName* req = static_cast<EReqChangeKnightageName*>(e.RawPtr());
	
	ErrorItem::Error eError = owner->GetUseItemAction().UseChangeKnightName(req->targetItem, req->newName);

	if (eError != ErrorKnightage::SUCCESS)
	{
		EResChangeKnightageName* res = NEW EResChangeKnightageName;
		res->eError = eError;
		SERVER.SendToClient(owner, EventPtr(res));
	}
}

void StatePlayerBase::onReqSortBagItem( EventPtr& e )
{
	EReqSortBagItems* req = static_cast< EReqSortBagItems* >( e.RawPtr() );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), )	

	ErrorItem::Error eError = owner->GetInventoryAction().SortAllBagItem( req->eInvenBegin, req->eInvenEnd );
	if( ErrorItem::SUCCESS != eError )
	{
		EResSortBagItems* res = NEW EResSortBagItems;
		res->eError = eError;
		SERVER.SendToClient( owner, EventPtr(res) );	
		return;
	}
}

void StatePlayerBase::onReqUseGambleItem( EventPtr& e )
{
	EReqUseGambleItem* req = static_cast<EReqUseGambleItem*>( e.RawPtr() );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ErrorItem::Error eError = owner->GetUseItemAction().UseGambleItem(req->itemPos, req->useItemCount);

	if (ErrorItem::E_REFER_SYSTEM_MSG == eError || req->useItemCount > 0)
	{
		SystemMessage msg( L"sys", owner->GetUseItemAction().GetLastSystemMsg() );

		if (true == owner->GetUseItemAction().IsExistLastSystemMsgParam() )
			msg.SetParam( L"str", owner->GetUseItemAction().GetLastSystemMsgParam() );

		SendSystemMsg( owner, msg );
	}

	if( ErrorItem::SUCCESS != eError ) 
	{
		EResUseGambleItem* res = NEW EResUseGambleItem;
		res->eError = eError;
		owner->SendToClient(EventPtr( res ) );
	}
}

void StatePlayerBase::onReqSelectGambleItem( EventPtr& e )
{
	EReqSelectGambleItem* req = static_cast<EReqSelectGambleItem*>(e.RawPtr());
	req;
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN( owner && owner->IsValid(), );

	ActionPlayerUseItem* actionUseItem = GetEntityAction( owner );
	VERIFY_RETURN(actionUseItem, );

	ErrorItem::Error eError = actionUseItem->UseSelectGambleItem( req->gambleBagIndex, req->selectIndex );

	if (ErrorItem::SUCCESS != eError) 
	{
		if (ErrorItem::E_REFER_SYSTEM_MSG == eError) 
		{
			SendSystemMsg( owner, SystemMessage( L"sys", actionUseItem->GetLastSystemMsg() ) );
		}

		EResSelectGambleItem* res = NEW EResSelectGambleItem;
		res->eError = eError;
		SERVER.SendToClient( owner, EventPtr( res ) );
	}

}


void StatePlayerBase::onReqUseRecoveryEntranceItem( EventPtr& e )
{	
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	EReqUseRecoveryEntranceItem* req = static_cast< EReqUseRecoveryEntranceItem* >(e.RawPtr());

	const ItemConstPtr item = owner->GetInventoryAction().GetItem(req->item);
	VERIFY_RETURN(item, );
	VERIFY_RETURN(item->GetDivision() == ItemDivisionType::RECOVERY_ENTRANCE_COUNT, );

	const ItemRecoveryEntranceCountElem* itemRecoveryEntranceElem = SCRIPTS.GetItemRecoveryEntranceCountElem(item->GetItemIndex());
	VERIFY_RETURN(itemRecoveryEntranceElem, );

	// 실제 데이터는 월드서버에 있으므로 사용가능한지 체크 요청한다.
	EReqItemRecoveryEntranceCheck* reqToWorld = NEW EReqItemRecoveryEntranceCheck;
	reqToWorld->item = req->item;
	reqToWorld->recoveryIndex	= itemRecoveryEntranceElem->recoveryIndex;
	reqToWorld->recoveryCount	= itemRecoveryEntranceElem->recoveryCount;
	owner->SendToClient(EventPtr( reqToWorld ) );
}

void StatePlayerBase::onResItemRecoveryEntranceCheck( EventPtr& e )
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	EResItemRecoveryEntranceCheck* res = static_cast< EResItemRecoveryEntranceCheck* >( e.RawPtr() );

	if( false == res->isOk )
	{
		SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_RestoreEntranceCount_Err_Max" ) );
		return;
	}

	ErrorItem::Error eError = owner->GetUseItemAction().UseItemRecoveryEntrance( res->item );
	if( ErrorItem::SUCCESS != eError )
	{
		if( ErrorItem::E_REFER_SYSTEM_MSG == eError )
		{
			SendSystemMsg( owner, SystemMessage( L"sys", owner->GetUseItemAction().GetLastSystemMsg() ) );
		}
		else
		{
			EResUseRecoveryEntranceItem* resUseRecoveryEntranceItem = NEW EResUseRecoveryEntranceItem;
			resUseRecoveryEntranceItem->result = eError;
			owner->SendToClient(EventPtr( resUseRecoveryEntranceItem ) );
		}
	}
}

void StatePlayerBase::onResDbInsertItem( EventPtr& e )
{
	EResDbInsertItem* res = static_cast< EResDbInsertItem* >( e.RawPtr() );
	if(ErrorDB::SUCCESS != res->eError)
	{
		MU2_WARN_LOG( LogCategory::CONTENTS, L"StatePlayerBase::onResDbInsertItem > request failed. event = %s", e->ToString().c_str());
	}
}

void StatePlayerBase::onResDbRemoveItem( EventPtr& e )
{
	EResDbRemoveItem* res = static_cast< EResDbRemoveItem* >( e.RawPtr() );
	if(ErrorDB::SUCCESS != res->eError)
	{
		MU2_WARN_LOG( LogCategory::CONTENTS, L"StatePlayerBase::onResDbRemoveItem > request failed. event = %s", e->ToString().c_str());
	}
}

void StatePlayerBase::onResDbClearBag( EventPtr& e )
{
	EResDbClearBag* res = static_cast< EResDbClearBag* >( e.RawPtr() );
	if(ErrorDB::SUCCESS != res->eError)
	{
		MU2_WARN_LOG( LogCategory::CONTENTS, L"StatePlayerBase::onResDbClearBag > request failed. event = %s", e->ToString().c_str());
	}
}

void StatePlayerBase::onResDbUpdateItem( EventPtr& e )
{
	EResDbUpdateItem* res = static_cast< EResDbUpdateItem* >( e.RawPtr() );
	if(ErrorDB::SUCCESS != res->eError)
	{
		MU2_WARN_LOG( LogCategory::CONTENTS, L"StatePlayerBase::onResDbUpdateItem > request failed. event = %s", e->ToString().c_str());
	}
}

void StatePlayerBase::onResDbRemoveMail( EventPtr& e )
{
	EResDbRemoveMail* res = static_cast< EResDbRemoveMail* >( e.RawPtr() );
	if(ErrorDB::SUCCESS != res->eError)
	{
		MU2_WARN_LOG( LogCategory::CONTENTS, L"StatePlayerBase::onResDbRemoveMail > request failed. event = %s", e->ToString().c_str());
	}
}

void StatePlayerBase::onResDbUpdateMail( EventPtr& e )
{
	EResDbUpdateMail* res = static_cast< EResDbUpdateMail* >( e.RawPtr() );
	if(ErrorDB::SUCCESS != res->eError)
	{
		MU2_WARN_LOG( LogCategory::CONTENTS, L"StatePlayerBase::onResDbUpdateMail > request failed. event = %s", e->ToString().c_str());
	}
}

void StatePlayerBase::onReqGameGotoServerList( EventPtr& e )
{
	// 이 패킷은 존 서버 수준에서 정리할 수 있는 건 아니다. 
	// 월드 서버에서만 처리해서 처리가 가능해야 한다. 
	// 존 서버에서는 Execution에서 나가는 흐름만 처리하면 됨

	EReqGameGotoServerList* req = static_cast< EReqGameGotoServerList* >( e.RawPtr() );
	VERIFY_RETURN(req, );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ErrorLogin::Error eError = reqGameGotoServerList( req );
	if ( eError != ErrorLogin::SUCCESS)
	{
		EResGameGotoServerList* res = NEW EResGameGotoServerList;
		res->eError = eError;

		SERVER.SendToClient( owner, EventPtr(res) );
	}
}

ErrorLogin::Error StatePlayerBase::reqGameGotoServerList( EReqGameGotoServerList* /* req */)
{
	// 주석 제거 : rev 105

	return ErrorLogin::SUCCESS;	
}

void StatePlayerBase::onReqGameAddMoney( EventPtr& e )
{
	UNREFERENCED_PARAMETER(e);
}

void StatePlayerBase::onReqGameSubMoney( EventPtr& e )
{
	UNREFERENCED_PARAMETER(e);
}

void StatePlayerBase::onNtfGameChatNormalToZone( EventPtr& e )
{
	ENtfGameChatNormalToZone* ntfToZone = static_cast<ENtfGameChatNormalToZone*>( e.RawPtr() );
	EntityPlayer* ownerPlayer = GetOwnerPlayer();

	ENtfGameChatNormal* ntf = NEW ENtfGameChatNormal;
	ntf->nickName = ownerPlayer->GetCharName();
	ntf->charId = ownerPlayer->GetCharId();	
	ntf->msg = ntfToZone->msg;
	ntf->languageCode = ntfToZone->languageCode;
	ntf->accountGrade = ownerPlayer->GetAccountGrade();
	ownerPlayer->GetSectorAction().WideGridCast( EventPtr( ntf ) );

	ENtfGameChatPenaltyCheck* ntfCheck = NEW ENtfGameChatPenaltyCheck;
	ntfCheck->charId = ownerPlayer->GetCharId();
	SERVER.SendToClient( ownerPlayer, EventPtr( ntfCheck ) );

	
	theNotifierGameContents.OnChatting_SentNormal(*ownerPlayer, ntfToZone->msg);

}

void StatePlayerBase::onNtfCommChatParty( EventPtr& e )
{
	ENtfCommChatParty* commNtf = static_cast<ENtfCommChatParty*>( e.RawPtr() );
	EntityPlayer* owner = GetOwnerPlayer();

	ActionPlayerParty* actParty = GetEntityAction( owner );

	std::wstring fromName;

	VecPartyMember members;
	if( actParty->GetMembers( members ) )
	{
		for( auto it = members.begin() ; it != members.end() ; ++it )
		{
			if( it->charId == commNtf->fromCharId )
			{
				fromName = it->name;
				break;
			}
		}
	}

	ENtfGameChatParty* ntf = NEW ENtfGameChatParty;	
	ntf->charId = commNtf->fromCharId;
	ntf->from = fromName;
	ntf->msg = commNtf->msg;
	ntf->languageCode = commNtf->languageCode;
	SERVER.SendToClient( owner, EventPtr( ntf ) );

	ENtfGameChatPenaltyCheck* ntfCheck = NEW ENtfGameChatPenaltyCheck;
	ntfCheck->charId = owner->GetCharId();
	SERVER.SendToClient( owner, EventPtr( ntfCheck ) );

	if (owner->GetCharId() == commNtf->fromCharId)
	{
		theNotifierGameContents.OnChatting_SentParty(*owner, commNtf->msg);
	}
	
}

void StatePlayerBase::onReqGameChatInfoInfo( EventPtr& e )
{
	EReqGameChatLinkInfo *req = static_cast<EReqGameChatLinkInfo*>(e.RawPtr());

	VERIFY_RETURN(req->itemDatas.size() <= Limits::MAX_CHAT_LINK_COUNT, );
	VERIFY_RETURN(req->achievements.size() <= Limits::MAX_CHAT_LINK_COUNT, );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ENtfGameChatLinkInfo *ntfGame = Event::AllocEvent<ENtfGameChatLinkInfo>( e );
		
	for (const auto& itemData : req->itemDatas)
	{
		const ItemConstPtr found = owner->GetInventoryAction().GetItem(itemData.GetPos());
		VALID_DO(found && found->GetItemId() == itemData.itemId, continue);

		ItemData bufferData;
		found->FillUpItemData( bufferData );

		ntfGame->itemDatas.push_back( bufferData );
	}

	ntfGame->achievements = req->achievements;
	owner->SendToWorld(EventPtr( ntfGame ) );
}

void StatePlayerBase::onReqBuyItem( EventPtr& e )
{
	EReqBuy* req = static_cast< EReqBuy* >( e.RawPtr() );

	EntityPlayer* owner = GetOwnerPlayer();
	const ErrorItem::Error eError = reqBuyItem( req );
	if ( eError != ErrorItem::SUCCESS)
	{
		EResBuy* res = NEW EResBuy;
		res->eError = eError;
		SERVER.SendToClient( owner, EventPtr( res ) );
	}
}

ErrorItem::Error StatePlayerBase::reqBuyItem(EReqBuy* req)
{
	EntityPlayer* ownerPlayer = GetOwnerPlayer();

	auto discountFunc = []( EntityPlayer* player, IndexNpc npcIndex) -> UInt32
	{	
		const ShopDiscountElem * discountElem = SCRIPTS.GetShopDiscountElem( npcIndex );
		if (discountElem)
		{
			ActionPlayerKnightage& knightage = player->GetKnightageAction();

			UInt16 bonusMember = BonusMember::COMMON;
			if (player->GetFollowerId() != 0)
			{
				bonusMember = BonusMember::FOLLOWER;
			}
			else if (player->GetKnightageId())
			{
				bonusMember = BonusMember::KNIGHTAGE;
			}

			auto discountRate = discountElem->discountMap.find( ShopDiscountElem::Key( knightage.GetAdjacentLv(), bonusMember ) );
			if (discountRate != discountElem->discountMap.end())
			{
				if (discountRate->second > 0 &&
					discountRate->second <= Limits::MAX_DEFAULT_RAND)
					return discountRate->second;
			}
		}

		return 0;
	};

	if (req->npcId)
	{
		auto actSector = ownerPlayer->GetAction< ActionSector >();
		
		EntityNpc* npc = actSector->GetNpcInMySector(req->npcId);
		VERIFY_RETURN(npc && npc->HasView(VIEW_SHOP), ErrorItem::E_NOT_NPC);

		ActionNpcBlackMarket* actNpcBlackMarket = GetEntityAction(npc);
		VERIFY_RETURN(actNpcBlackMarket, ErrorItem::E_NOT_NPC);

		ActionPlayerKnightage *actKnight = GetEntityAction( ownerPlayer );

		VALID_RETURN(actKnight->IsMyDominionWisdom(), ErrorItem::E_NOT_DOMINION);

		auto viewShop = npc->GetView<ViewShop>();
		ErrorItem::Error eError = viewShop->CanUse(ownerPlayer);
		VALID_RETURN(eError == ErrorItem::SUCCESS, eError);


		UInt32 discount = discountFunc( ownerPlayer, npc->GetNpcIndex());

		ShopPack shopPack;
		for (UShort i = 0; i < req->items.size(); ++i)
		{
			const ItemBuyNode& buyNode = req->items[i];

			if (actNpcBlackMarket->IsBlackMarketNpc() == true)
			{
				ErrorBlackMarket::Error ret = actNpcBlackMarket->CanBuyBlackMarketItem(ownerPlayer, buyNode.itemIndex, buyNode.stack);
				if (ret != ErrorBlackMarket::SUCCESS)
				{
					if (ret == ErrorBlackMarket::E_DAILY_BUY_COUNT_OVER)
					{
						//SendSystemMsg(ownerPlayer, SystemMessage(L"sys", L"Msg_ContributionShop_DailyLimit"));
					}

					return ErrorItem::E_NOT_BUY_ITEM;
				}
			}
			else
			{
				if (checkItem(npc->GetNpcIndex(), buyNode.itemIndex) == false)
					return ErrorItem::E_NOT_BUY_ITEM;
			}
			

			if (!ItemFactory::GetShopPack(ShopType::BUY, npc, buyNode.itemIndex, buyNode.stack, ItemIdentify::UNIDENTIFIED, discount, shopPack))
			{
				SERVER.SendToError(ownerPlayer, ErrorType::E_ITEM, ErrorItem::E_INVALID_TYPE);
				return ErrorItem::E_INVALID_TYPE;
			}
		}

		return buyItem(npc->GetNpcIndex(), req->items, shopPack);
	}
	else
	{
		ActionPlayerMembership* actMembership = GetEntityAction( ownerPlayer );
		VERIFY_RETURN(actMembership, ErrorItem::E_FAILED );

		IndexNpc npcIndex = actMembership->GetValueAtBenefitType(EMembershipBenefitType::eValue_NpcShop);
		VALID_RETURN(npcIndex, ErrorItem::E_HAVE_NO_QUALIFICATION_FOR_NPC_SHOP);

		const NpcInfoElem* elem = SCRIPTS.GetNpcInfoScript(npcIndex);
		VALID_RETURN(elem, ErrorItem::E_HAVE_NO_QUALIFICATION_FOR_NPC_SHOP);

		UInt32 discount = discountFunc( ownerPlayer, npcIndex );

		ShopPack shopPack;
		UInt32 value = 0;
		for (UShort i = 0; i < req->items.size(); ++i)
		{
			const ItemBuyNode& buyNode = req->items[i];

			if (checkItem(elem->index, buyNode.itemIndex) == false)
				return ErrorItem::E_NOT_BUY_ITEM;

			const ShopPack* buyPack = SCRIPTS.GetShopPack(elem->index, buyNode.itemIndex);
			VALID_RETURN(buyPack, ErrorItem::E_NOT_BUY_ITEM);
			
			if (shopPack.type == ShopPack::Type::NONE)
			{
				shopPack.type = buyPack->type;
				shopPack.index = buyPack->index;
			}
			else
			{
				VALID_RETURN(shopPack == (*buyPack), ErrorItem::E_NOT_BUY_ITEM);
			}

			value = buyPack->quentity - ((buyPack->quentity * discount) / Limits::MAX_DEFAULT_RAND);
			shopPack.Add( value * buyNode.stack);
		}

		return buyItem(npcIndex, req->items, shopPack);
	}
}

Bool StatePlayerBase::checkItem( IndexNpc npcindex, IndexItem itemIdx )
{
	EntityPlayer* owner = GetOwnerPlayer();

	const ShopPack* buyPack = SCRIPTS.GetShopPack( npcindex, itemIdx );
	if (buyPack == nullptr)
		return false;

	ActionSector *actSector = GetEntityAction( owner );
	VERIFY_RETURN( actSector, false );

	if ( buyPack->dominionBonus == TRUE &&
		actSector->GetSector()->GetDominionKnightageId() != 0 )
	{
		const ShopBonusElem *bonus = SCRIPTS.GetShopBonusElem( npcindex );

		ActionPlayerKnightage *knightage = GetEntityAction( owner );

		auto iter = bonus->sellIndexs.find( itemIdx );
		if (iter == bonus->sellIndexs.end())
			return false;

		if (owner->GetKnightageId() == actSector->GetSector()->GetDominionKnightageId())
		{
			if (iter->second.end() != iter->second.find( ShopBonusElem::Key( knightage->GetAdjacentLv(), BonusMember::KNIGHTAGE ) ))
				return true;
		}

		return false;
	}

	return true;
}

ErrorItem::Error StatePlayerBase::buyItem(IndexNpc npcIndex, const ItemBuyNodes& items, ShopPack& shopPack )
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), ErrorItem::playerNotFound);

	Sector* sector = owner->GetSector();
	VERIFY_RETURN(sector, ErrorItem::sectorNotFound);		
	
	NpcFunctionType::Enum eNpcFunctionType = NpcFunctionType::NPC_FUNCTION_SELL;

	if ( true == SCRIPTS.HasFunctionThisMonster(npcIndex, NpcFunctionType::NPC_FUNCTION_BLACK_MARKET_SELL) )
	{
		eNpcFunctionType = NpcFunctionType::NPC_FUNCTION_BLACK_MARKET_SELL;
	}
	else if ( true == SCRIPTS.HasFunctionThisMonster( npcIndex, NpcFunctionType::NPC_FUNCTION_CONTRIBUTION_SHOP ) )
	{
		eNpcFunctionType = NpcFunctionType::NPC_FUNCTION_CONTRIBUTION_SHOP;
	}
	else if ( true == SCRIPTS.HasFunctionThisMonster(npcIndex, NpcFunctionType::NPC_FUNCTION_EVENT_SHOP))
	{
		eNpcFunctionType = NpcFunctionType::NPC_FUNCTION_EVENT_SHOP;
	}

	VERIFY_RETURN(owner->GetInventoryAction().CheckShopPack(ShopType::BUY, shopPack), ErrorItem::E_NOT_ENOUGH_MONEY);

	UInt32 subMoney = shopPack.quentity;
	

	ItemBag bag( *owner );

	VecDailyShopInfo dailyShopItemVec;
	BlackMarketItemVec blackMarketItemVec;

	Int32 buyCount = 0;

	for (const auto& i : items)
	{
		DailyShopInfo info;
		VERIFY_RETURN(owner->GetInventoryAction().CheckDailyBuyItem(npcIndex, i.itemIndex, static_cast<Int16>(i.stack), OUT info), ErrorItem::CheckDailyBuyItemFailed);

		if( info.itemIndex != 0 )
			dailyShopItemVec.push_back( info );

		if (eNpcFunctionType == NpcFunctionType::NPC_FUNCTION_BLACK_MARKET_SELL)
		{
			blackMarketItemVec.emplace_back(BlackMarketItem(i.itemIndex, i.stack));
		}

		VERIFY_RETURN(bag.Insert(ItemData(i.itemIndex, i.stack)), ErrorItem::BagInsertFailed);

		buyCount += i.stack;
	}
	ChangedMoney::MoneyType moneyType = ShopPack::ChangeEnum(shopPack.type);


	LogCode::Enum logCode = LogCode::E_ITEM_BUY;
	switch (eNpcFunctionType)
	{
		case NpcFunctionType::NPC_FUNCTION_SELL:
		case NpcFunctionType::NPC_FUNCTION_BLACK_MARKET_SELL:
		case NpcFunctionType::NPC_FUNCTION_EVENT_SHOP:
			logCode = LogCode::E_ITEM_BUY;
			break;
		case NpcFunctionType::NPC_FUNCTION_CONTRIBUTION_SHOP:
			logCode = LogCode::E_CONTRIBUTION_SHOP;
			break;
	}

	ItemBuyTransaction *trans = NEW ItemBuyTransaction(__FUNCTION__, __LINE__, owner, logCode, npcIndex, dailyShopItemVec, blackMarketItemVec, shopPack, eNpcFunctionType, buyCount );
	TransactionPtr transPtr = TransactionPtr( trans );
	{
		switch (moneyType)
		{
		case ChangedMoney::ZEN:
		case ChangedMoney::GREENZEN:
		case ChangedMoney::RIFTPOINT:
		case ChangedMoney::CONTRIBUTION:
			transPtr->BindMoney(owner->GetCharId(), subMoney, ChangedMoney::SUB, moneyType);
			break;
		case ChangedMoney::TOKEN:
			VERIFY_RETURN(bag.Remove(ItemData(shopPack.index, subMoney)), ErrorItem::BagRemoveFailed);
			break;
		default:
			break;
		}

		switch (eNpcFunctionType)
		{
		case NpcFunctionType::NPC_FUNCTION_SELL:
		case NpcFunctionType::NPC_FUNCTION_BLACK_MARKET_SELL:
		case NpcFunctionType::NPC_FUNCTION_EVENT_SHOP:
		case NpcFunctionType::NPC_FUNCTION_CONTRIBUTION_SHOP:
			trans->SetContributionLog(items, sector->GetDominionKnightageId());
			break;
		}

		VERIFY_RETURN(bag.Write(transPtr), ErrorItem::BagWriteFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::TranRegisterFailed);
	}

	return ErrorItem::SUCCESS;
}


void StatePlayerBase::onReqSellItem( EventPtr& e )
{
	EReqSell* req = static_cast< EReqSell* >( e.RawPtr() );

	EntityPlayer* owner = GetOwnerPlayer();
	const ErrorItem::Error eError = reqSellItem( req );
	if ( eError != ErrorItem::SUCCESS )
	{
		EResSell* res = NEW EResSell;
		res->eError = eError;
		SERVER.SendToClient( owner, EventPtr( res ) );
	}
}


ErrorItem::Error StatePlayerBase::reqSellItem( EReqSell* req )
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), ErrorItem::playerNotFound);
	VERIFY_RETURN(req && req->items.size(), ErrorItem::itemNotFound);

	auto actInven = owner->GetAction< ActionPlayerInventory >();
	VERIFY_RETURN(actInven->CanBeSoldItem(), ErrorItem::E_NOT_OPEN_ITEM_SELL_FLAG);

	ErrorItem::Error eError = ErrorItem::SUCCESS;	

	if (req->npcId)
	{
		EntityNpc* npc = owner->GetSectorAction().GetNpcInMySector(req->npcId);
		VERIFY_RETURN(npc && npc->IsValid(), ErrorItem::npcNotFound);
		VERIFY_RETURN(npc->GetShopViewNullable(), ErrorItem::npcHasNotShop);
				
		eError = npc->GetShopViewNullable()->CanUse(owner);
		VERIFY_RETURN(eError == ErrorItem::SUCCESS, eError);
	}
	else
	{
		IndexNpc npcIndex = owner->GetMembershipAction().GetValueAtBenefitType(EMembershipBenefitType::eValue_NpcShop);
		VERIFY_RETURN(npcIndex, ErrorItem::E_HAVE_NO_QUALIFICATION_FOR_NPC_SHOP);
	}

	std::vector<ItemSlotInfo> vtItemList;

	ShopPack shopPack;
	for (const ItemSlotInfo& wishSellSlot : req->items)
	{
		// 활성화 상태인 아이템은 팔 수 없다.

		const ItemConstPtr foundItem = owner->GetInventoryAction().GetItemByPlace(wishSellSlot);
		VERIFY_RETURN(foundItem, ErrorItem::itemNotFound);
		VERIFY_RETURN(foundItem->IsSellable(), ErrorItem::E_NOT_SELLABLE);

		// 깊은복사
		ItemSlotInfo itemSlotInfoCloned = foundItem->GetSlotInfo();
		itemSlotInfoCloned.SetCurStackCount(std::min(wishSellSlot.GetCurStackCount(), itemSlotInfoCloned.GetCurStackCount()));

		if (false == ItemFactory::GetShopPack(ShopType::SELL, owner, foundItem->GetItemIndex(), itemSlotInfoCloned.GetCurStackCount(), foundItem->GetIdentifyOption(), 0, shopPack))
		{
			return ErrorItem::E_INVALID_TYPE;
		}

		vtItemList.emplace_back(itemSlotInfoCloned);

		// 클라이언트에서 판매를 요청한 아이템 정보를 로그로 남긴다.
		MU2_INFO_LOG(LogCategory::ITEM, "EReqSell - charId(%u), ClientItem(%s)", owner->GetCharId(), wishSellSlot.ToString().c_str());
	}

	VERIFY_RETURN(owner->GetInventoryAction().CheckShopPack(ShopType::SELL, shopPack), ErrorItem::E_NOT_ENOUGH_MONEY);

	// [COMMENT BY MOOK] 2018.01.03 
	// 한꺼번에 팔기가 복수슬롯이면 되사기 인벤에 넣지 않고, 단수일때만 넣는다.
	//Bool isPushRebuyBag = vtItemList.size() > 1 ? false : true;

	TransactionPtr transPtr( NEW ItemSellTransaction(__FUNCTION__, __LINE__, owner, LogCode::E_ITEM_SELL, vtItemList, shopPack ) );
	{
		for (const ItemSlotInfo& slotInfo : vtItemList)
		{
			eError = owner->GetInventoryAction().Sell(slotInfo, transPtr, req->isPushRebuyBag);

			VERIFY_DO(	eError == ErrorItem::SUCCESS, 
						SERVER.SendToError(owner, ErrorType::E_ITEM, eError);
						return eError;);
		}

		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::TranRegisterFailed);
	}

	return ErrorItem::SUCCESS;
}

void StatePlayerBase::onReqRebuyItem( EventPtr& e )
{
	EReqRebuy* req = static_cast< EReqRebuy* >( e.RawPtr() );

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	const ErrorItem::Error eError = reqRebuyItem( req );
	if ( eError != ErrorItem::SUCCESS )
	{
		MU2_WARN_LOG(LogCategory::ITEM, "player(%s), error(%u)", player->ToString().c_str(), eError);

		EResBuy* res = NEW EResBuy;
		res->eError = eError;
		player->SendToClient(EventPtr( res ) );
	}
}

ErrorItem::Error StatePlayerBase::reqRebuyItem( EReqRebuy* req )
{
	VERIFY_RETURN(req, ErrorItem::E_FAILED);

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	ErrorItem::Error eError = ErrorItem::SUCCESS;

	if (req->npcId)
	{
		EntityNpc* npc = player->GetSectorAction().GetNpcInMySector(req->npcId);
		VERIFY_RETURN(npc && npc->IsValid(), ErrorItem::npcNotFound);
		VERIFY_RETURN(npc->GetShopViewNullable(), ErrorItem::npcHasNotShop);
				
		eError = npc->GetShopViewNullable()->CanUse(player);
		VERIFY_RETURN(eError == ErrorItem::SUCCESS, eError);
	}
	else
	{
		IndexNpc npcIndex = player->GetMembershipAction().GetValueAtBenefitType(EMembershipBenefitType::eValue_NpcShop);
		VERIFY_RETURN(npcIndex, ErrorItem::E_HAVE_NO_QUALIFICATION_FOR_NPC_SHOP);
	}

	InvenReBuy* rebuyInven = player->GetInventoryAction().GetInven<InvenReBuy>(InvenType::REBUY);
	VERIFY_RETURN(rebuyInven, ErrorItem::inventoryNotFound);

	ItemDatas rebuyItemDatas;

	for (const ItemSlotInfo& slotInfo : req->items)
	{
		VERIFY_RETURN(slotInfo.IsRebuyBag(), ErrorItem::E_NOT_EXIST_ITEM);

		const ItemConstPtr reBuyableItem = rebuyInven->GetItemByPlace(slotInfo);
		VERIFY_RETURN(reBuyableItem, ErrorItem::itemNotFound);

		VERIFY_RETURN(
			reBuyableItem->GetItemIndex() == slotInfo.indexItem &&
			reBuyableItem->GetItemId() == slotInfo.itemId &&
			reBuyableItem->GetCurStackCount() >= slotInfo.GetCurStackCount(),
			ErrorItem::E_DIFFERENT_ITEM);

		ItemData data;
		reBuyableItem->FillUpItemData( data );

		rebuyItemDatas.push_back( data );
	}

	ShopPack shopPack;
	for(const ItemData& rebuyItemData : rebuyItemDatas)
	{
		if ( !ItemFactory::GetShopPack( ShopType::REBUY, player, rebuyItemData.indexItem, rebuyItemData.GetCurStackCount(), rebuyItemData.identifyOption, 0, shopPack ) )
		{
			SERVER.SendToError( player, ErrorType::E_ITEM, ErrorItem::E_INVALID_TYPE );
			return ErrorItem::E_INVALID_TYPE;
		}
	}

	VERIFY_RETURN(player->GetInventoryAction().CheckShopPack(ShopType::REBUY, shopPack), ErrorItem::E_NOT_ENOUGH_MONEY);		
	
	ItemRebuyTransaction * rebuyTr = NEW ItemRebuyTransaction(__FUNCTION__, __LINE__, player, LogCode::E_ITEM_REBUY, shopPack);
	TransactionPtr transPtr(rebuyTr);
	{
		rebuyTr->SetLogItems(rebuyItemDatas);
		for (const ItemData& itemData : rebuyItemDatas)
		{
			eError = rebuyInven->AcquireItemFromRebuyInven(itemData.itemId, transPtr);
			if (eError != ErrorItem::SUCCESS)
			{
				SendSystemMsg(player, SystemMessage(L"sys", L"Msg_Itembuy_Err_Inventory_Full"));
				return eError;
			}
		}

		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::E_TRANSACION_FAILED);
	}

	return ErrorItem::SUCCESS;
}

void StatePlayerBase::onReqRebuyItemList( EventPtr& e )
{
	EReqRebuyItemList* req = static_cast<EReqRebuyItemList*>( e.RawPtr() );
	UNREFERENCED_PARAMETER(req);

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), );

	InvenReBuy* rebuyInven = player->GetInventoryAction().GetInven<InvenReBuy>(InvenType::REBUY);

	ErrorItem::Error eError = rebuyInven->SyncRebuyItemList();
	if (eError != ErrorItem::SUCCESS)
	{
		MU2_WARN_LOG(LogCategory::ITEM, "player(%s), error(%u)", player->ToString().c_str(), eError);
	}
}

void StatePlayerBase::onResDbBuyItem( EventPtr& /*e*/ )
{

}

void StatePlayerBase::onResDbSellItem( EventPtr& /*e*/ )
{

}

void StatePlayerBase::onResDbRebuyItem( EventPtr& /*e*/ )
{

}

void StatePlayerBase::onResGamePing( EventPtr& e )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	e;
	const Tick currentTick = GetTickCount();
	const Tick latency = currentTick - m_impl->reqPingTick;

	m_impl->latencyArr.push_back(latency);
	if( 10 /*Do not use the magic number!*/ < m_impl->latencyArr.size() )
	{
		const UInt32 sum = std::accumulate( m_impl->latencyArr.begin(), m_impl->latencyArr.end(), 0 );
		m_impl->latencyArr.clear();

		player->SetLatency(sum / 10 /*Do not use the magic number!*/);
	}
}

void StatePlayerBase::onNtfCommPartyCreate( EventPtr& e )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );
	
	ActionPlayerParty& actParty = player->GetPartyAction();

	ENtfCommPartyCreate* ntf = static_cast<ENtfCommPartyCreate*>(e.RawPtr());
	VERIFY_RETURN(ntf, );

	if ( ntf->groupId > 0 )
	{
		actParty.SetPartyId( ntf->groupId );

		for ( UInt32 i = 0; i < ntf->members.size(); ++i )
		{
			if ( ntf->members[i].permission == EPartyPermission::LEADER )
			{
				actParty.SetLeader( ntf->members[i].charId);
			}

			actParty.AddMember( ntf->members[i] );
		}

		actParty.SetAutoMatch(ntf->isAutoMatch);

		if ( ntf->isSyncMember )
		{
			ENtfGamePartySyncMemberInfo* ntfToClient = NEW ENtfGamePartySyncMemberInfo;
			actParty.GetMembers( ntfToClient->members );
			SERVER.SendToClient( player, EventPtr( ntfToClient ) );
		}

		if ( ntf->notify )
		{
			ENtfGamePartyCreate* ntfToCli = NEW ENtfGamePartyCreate;
			ntfToCli->partyId = ntf->groupId;
			ntfToCli->members = ntf->members;
			ntfToCli->notify = ntf->notify;
			ntfToCli->zoneIndex = ntf->zoneIndex;
			ntfToCli->minLevel = ntf->minLevel;
			ntfToCli->maxLevel = ntf->maxLevel;
			ntfToCli->maxMemberCount = ntf->maxMemberCount;
			ntfToCli->description = ntf->description;
			ntfToCli->isAdvertising = ntf->isAdvertising;

			SERVER.SendToClient( player, EventPtr( ntfToCli ) );

			actParty.SyncMyPosition();
			
			//actParty->SyncBuff();
			ActionBuff* actionBuff = player->GetAction<ActionBuff>();
			if ( nullptr == actionBuff )
				return;
			actionBuff->SendDbBuffInfo();
			actParty.SyncAllBuffFromOther();

			AchievementEnjoyPartyEvent achievementEvent( player );
			player->GetAction< ActionPlayerAchievement >()->OnEvent( &achievementEvent );
		}

		// 파티 초창기 생성멤버가 아니어도 create를 탄다.. 
		// 일단은 bool 변수로 create인지 join인지 분간시켜서 log를 남기기로..
		EReqLogDb2* log = NEW EReqLogDb2;
		EventPtr logEventPtr( log );
		if ( ntf->isCreate )
		{
			EventSetter::FillLogForEntity( LogCode::E_PARTY_CREATE, player, log->action );
		}
		else
		{
			EventSetter::FillLogForEntity( LogCode::E_PARTY_JOIN, player, log->action );
		}

		log->action.var32s.push_back( actParty.GetPartyId() );
		if (ntf->isAutoMatch)
			log->action.var32s.push_back(1);
		else
			log->action.var32s.push_back(0);
		SendDataCenter( logEventPtr );
	
	}

	actParty.OnCheckInParty();
}

void StatePlayerBase::onNtfCommPartyDestroy( EventPtr& e )
{
	ENtfCommPartyDestroy* ntf = static_cast< ENtfCommPartyDestroy* >( e.RawPtr() );
	VERIFY_RETURN( ntf, );
	
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ActionPlayerParty* actParty = static_cast< ActionPlayerParty* >( owner->GetAction( ACTION_PARTY ) );
	VERIFY_RETURN(actParty, );

	if ( !actParty->IsParty() )
	{
		return;
	}

	actParty->DestroyParty(owner, ntf->groupId, ntf->hideMsg );
}

void StatePlayerBase::onNtfCommPartyAddMember( EventPtr& e )
{
	ENtfCommPartyAddMember* ntf = static_cast< ENtfCommPartyAddMember* >( e.RawPtr() );
	VERIFY_RETURN(ntf, );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );
		
	ActionPlayerParty* actParty = static_cast< ActionPlayerParty* >( owner->GetAction( ACTION_PARTY ) );
	VERIFY_RETURN(actParty, );

	if ( actParty->AddMember( ntf->newMember ) )
	{
		ENtfGamePartyAddMember* ntfToCli = NEW ENtfGamePartyAddMember;
		ntfToCli->newMember = ntf->newMember;
		ntfToCli->partyId = ntf->groupId;
		SERVER.SendToClient( owner, EventPtr( ntfToCli ) );
	}
}

void StatePlayerBase::onReqCommPartySyncLocationWhenCheckIn( EventPtr& )
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	auto actParty = owner->GetAction<ActionPlayerParty>();
	actParty->SyncMyPosition();
}

void StatePlayerBase::onNtfCommPartyLeaveMember( EventPtr& e )
{
	ENtfCommPartyLeaveMember* ntf = static_cast< ENtfCommPartyLeaveMember* >( e.RawPtr() );
	VERIFY_RETURN(ntf, );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );
			
	ActionPlayerParty* actParty = GetEntityAction( owner );
	VERIFY_RETURN(actParty, );

	actParty->LeaveParty(owner, ntf->leaveMemberCharId, ntf->leaveType, ntf->groupId);	
}

void StatePlayerBase::onReqCommPartySyncMember( EventPtr& e )
{
	EReqCommPartySyncMember* req = static_cast< EReqCommPartySyncMember* >( e.RawPtr() );
	VERIFY_RETURN(req, );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	auto actParty = owner->GetAction<ActionPlayerParty>();

	EResCommPartySyncMember* res = NEW EResCommPartySyncMember;	
	res->sessionKey = req->sessionKey;
	res->groupId = req->groupId;
	actParty->Fillup( res->member );
	SERVER.SendToClient( owner, EventPtr( res ) );
}

void StatePlayerBase::onNtfCommPartyChangeLeader( EventPtr& e )
{
	ENtfCommPartyChangeLeader* ntf = static_cast< ENtfCommPartyChangeLeader* >( e.RawPtr() );
	VERIFY_RETURN(ntf, );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ActionPlayerParty* actParty = static_cast< ActionPlayerParty* >( owner->GetAction( ACTION_PARTY ) );
	VERIFY_RETURN(actParty, );

	if ( ! actParty->ChangeLeader( ntf->leader, ntf->permission ) )
	{
	//	MU2_ASSERT( !"무조건 리더변경" );
		return;
	}

	ENtfGamePartyChangeLeader* evt = NEW ENtfGamePartyChangeLeader;
	evt->partyId = ntf->groupId;
	evt->leaderId = ntf->leader;

	SERVER.SendToClient( owner, EventPtr( evt ) );
}

void StatePlayerBase::onNtfCommPartySyncMemberInfo( EventPtr& e )
{
	ENtfCommPartySyncMemberInfo* ntf = static_cast< ENtfCommPartySyncMemberInfo* >( e.RawPtr() );
	VERIFY_RETURN(ntf, );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ActionPlayerParty* actParty = GetEntityAction( owner );
	VERIFY_RETURN(actParty, );

	actParty->UpdateMember( ntf->member );

	// 업데이트 된 멤버 정보만 전송
	ENtfGamePartySyncMemberInfo* ntfToClient = NEW ENtfGamePartySyncMemberInfo;
	ntfToClient->members.push_back( ntf->member );

	SERVER.SendToClient( owner, EventPtr( ntfToClient ) );
}

void StatePlayerBase::onNtfCommPartyMemberEnterExit( EventPtr& e )
{
	ENtfCommPartyMemberEnterExit* ntf = static_cast< ENtfCommPartyMemberEnterExit* >( e.RawPtr() );
	VERIFY_RETURN(ntf, );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ActionPlayerParty* actParty = GetEntityAction( owner );
	VERIFY_RETURN(actParty, );

	actParty->UpdateMember( ntf->member );

	// 파티원 정보 모두 전송
	ENtfGamePartySyncMemberInfo* ntfToClient = NEW ENtfGamePartySyncMemberInfo;
	actParty->GetMembers( ntfToClient->members );
	SERVER.SendToClient( owner, EventPtr( ntfToClient ) );
}

void StatePlayerBase::onReqCommPartySyncCandidateAdvDungeon( EventPtr& e )
{
	EReqCommPartySyncCandidateAdvDungeon* req = static_cast< EReqCommPartySyncCandidateAdvDungeon* >( e.RawPtr() );
	VERIFY_RETURN(req, );

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	ActionPlayerParty* actParty = static_cast< ActionPlayerParty* >( player->GetAction( ACTION_PARTY ) );
	VERIFY_RETURN(actParty, );

	ActionSector* actSector = GetEntityAction( player );
	VERIFY_RETURN(actSector, );
	
	Sector* sector = actSector->GetSector();
	
	if( req->cancelEnterDungeon )
	{
		if( sector->DeleteAllAdvCandidatesInMyVolume( player, actParty->GetPartyId() ) )
		{
			actParty->SyncCandidateEnterAdvDungeon();		
		}
	}
	else
	{
		if( sector->ChangeAdvCandidateInfo( player, actParty->GetPartyId(), req->volumeId, req->stempId, req->isInVolume ) )
		{
			actParty->SyncCandidateEnterAdvDungeon();		
		}	
	}	
}

void StatePlayerBase::onReqPartyDungeonCheckRequirement( EventPtr& e )
{
	EReqPartyDungeonCheckRequirement* req = static_cast< EReqPartyDungeonCheckRequirement* >( e.RawPtr() );
	VERIFY_RETURN(req, );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ActionPlayer* actPlayer = GetEntityAction( owner );
	VERIFY_RETURN(actPlayer, );

	ActionPlayerInventory* actInven = GetEntityAction( owner );
	VERIFY_RETURN(actInven, );

	const PortalInfoElem* elem = SCRIPTS.GetPortalInfoScript( req->portalIndex );
	VERIFY_RETURN(elem, );

	PortalUseItemList portalUseItemList;
	if (req->isCreator)
	{
		for (size_t i = 0; i < req->creatorUseItemList.size(); i++)
		{
			PortalUseItemInfo info;
			info.isAll = false;
			info.itemInfo = req->creatorUseItemList[i];
			portalUseItemList.push_back(info);
		}
	}
	else
	{
		for (size_t i = 0; i < req->memberUseItemList.size(); i++)
		{
			PortalUseItemInfo info;
			info.isAll = true;
			info.itemInfo = req->memberUseItemList[i];
			portalUseItemList.push_back(info);
		}
	}
	
	ErrorJoin::Error eError = thePortalSystem.CheckRequirement( owner, elem, portalUseItemList, req->difficulty,!req->isCreator, req->isMatching );

	// 상황에 맞게 에러메시지를 출력해야한다. 이런방식은.ㅠㅠ
	std::wstring error;
	if(ErrorJoin::SUCCESS != eError )
	{
		switch ( eError )
		{		
		case ErrorJoin::E_REQUIRE_ITEM:					error = L"Msg_Don_have_Item";							break;
		case ErrorJoin::E_LEVEL_LIMIT:					error = L"Msg_Cannot_Enter_Dungeon_Level";				break;
		case ErrorJoin::E_REQUIRE_ZEN:					error = L"Msg_Not_Enough_Zen";							break;
		case ErrorJoin::E_REQUIRE_GREEN_ZEN:			error = L"Msg_Not_Enough_Green_Zen";					break;
		case ErrorJoin::E_COMBAT_POWER_LIMIT:			error = L"Msg_DungeonEntrance_Err_LowItemLevel";		break;
		case ErrorJoin::E_MAZE_TICKET:					error = L"Msg_Donnot_have_ticket";						break;
		case ErrorJoin::E_MAZE_MEMBER_TICKET:			error = L"Msg_Party_Donnot_have_ticket";				break;
		case ErrorJoin::E_REQUIRE_PROGRESS_QUEST:		error = L"Msg_DungeonEntrance_Err_Admission_Quest";		break;
		case ErrorJoin::E_INVALID_SOUL_LEVEL:			error = L"Msg_Missionmap_Enter_SoulLevel_Err";			break;
		default:										error = L"Msg_Cannot_Enter_Dungeon_Common";				break;


		}
	}

	auto actPortal = owner->GetAction<ActionPlayerPortal>();
	auto viewPosition = owner->GetView<ViewPosition>();

	EResPartyDungeonCheckRequirement* res = NEW EResPartyDungeonCheckRequirement;
	res->partyId		= actPlayer->GetPartyId();
	res->charId			= actPlayer->GetCharId();
	res->isCheckSucceed = ErrorJoin::SUCCESS == eError;
	res->isJoin			= req->isJoin;
	res->error			= std::move( error );

	res->beginPositionIndex = actPortal->SetBeginPositionIndexFromZoneIndex(owner->GetIndexZone());
	viewPosition->GetPosition( res->position );

	SERVER.SendToClient( owner, EventPtr( res ) );
}

void StatePlayerBase::onReqQuest( EventPtr& e )
{
	EReqQuest* req =  static_cast< EReqQuest* >( e.RawPtr() );
	VERIFY_RETURN(req, );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ActionPlayerQuest* actionQuest = GetEntityAction( owner );
	VERIFY_RETURN(actionQuest, );

	actionQuest->SetTalkingNpc( req->entityId );

	ActionPlayer* actPlayer = GetEntityAction( owner );
	VERIFY_RETURN(actPlayer, );

	ErrorQuest::Error err = ErrorQuest::SUCCESS;

	switch ( req->type )
	{
	case RequestQuestFlag::REQUEST :	// 요청
		{
			err = actionQuest->GetQuest( req->questId, req->entityId );
			if ( err == ErrorQuest::SUCCESS)
			{
				const QuestElem* quest = SCRIPTS.GetQuest( req->questId );
				if ( quest == NULL )
				{
					actionQuest->SendQuestError( owner, ErrorQuest::E_QUEST_NOT_FOUND );
					return;
				}				

				if ( quest->progress_Type == QuestProgressType::Mission && false == actionQuest->CheckHeroMissionState( req->questId ) )
				{
					actionQuest->SendQuestError( owner, ErrorQuest::E_QUEST_SAME_HEROMISSION );
					return;
					
				}

				if ( quest->GetOccurType() == QuestOccurType::FORCEVOLUME)
				{
					err = actionQuest->AcceptQuest( req->questId );
				}
				else if ( quest->GetOccurType() == QuestOccurType::VOLUME )
				{
					err = actionQuest->ReserveQuest( req->questId );
				}
				else
				{
					actionQuest->SendGiveQuest( owner, req->questId );
				}
			}
		}
		break;
	case RequestQuestFlag::ACCEPT :	// 수락
		{
			const QuestElem* quest = SCRIPTS.GetQuest( req->questId );
			if( quest == nullptr)
			{
				actionQuest->SendQuestError( owner, ErrorQuest::E_QUEST_NOT_FOUND );
				return;
			}

			err = actionQuest->AcceptQuest( req->questId, req->entityId ); // entityId는 대상 NPC entity 
		}
		break;
	case RequestQuestFlag::GIVEUP :	// 포기
		{
			err = actionQuest->GiveUpQuest( req->questId );

			if ( err == ErrorQuest::SUCCESS )
			{
				actionQuest->SendGiveupQuest( owner, req->questId );	
				actionQuest->NotifyHeroMissionState( req->questId );
			}
		}
		break;
	case RequestQuestFlag::FAILED :	// 실패
		{
			err = actionQuest->FailQuest( req->questId );
			if ( err == ErrorQuest::SUCCESS )
			{
				actionQuest->SendFailQuest( owner, req->questId );	
				// FailQuest에서 업적 확인.
			}

		}
		break;
	case RequestQuestFlag::REFUSE :	// 거부
		{
			err = actionQuest->RefuseQuest( req->questId );
			if ( err == ErrorQuest::SUCCESS )
			{
				actionQuest->SendRefuseQuest( owner, req->questId );				
			}

		}
		break;

	case RequestQuestFlag::DISABLEUI :
		{
			actionQuest->UpdateEnableQuestUI( req->questId, false );
		}
		break;
	case RequestQuestFlag::ENABLEUI :
		{
			actionQuest->UpdateEnableQuestUI( req->questId, true );
		}
		break;
	}

	if ( err != ErrorQuest::SUCCESS )
	{
		actionQuest->SendQuestError( owner, err );
	}
}

void StatePlayerBase::onReqQuestReward(EventPtr& e)
{
	EReqQuestReward* req =  static_cast< EReqQuestReward* >( e.RawPtr() );
	EntityPlayer* ownerPlayer = GetOwnerPlayer();

	ActionPlayerQuest* actionQuest = GetEntityAction( ownerPlayer );
		
	
	actionQuest->SetTalkingNpc( req->entityId );

	Int32 err = actionQuest->RewardQuest( req->questId, req->rewardIndex, false, false );
	if ( err != ErrorQuest::SUCCESS )
	{
		switch (err)
		{
			case ErrorQuest::E_QUEST_INVEN_FULL:
				SendSystemMsg( ownerPlayer, SystemMessage( L"sys", L"Msg_Quest_Error_NeedSlot" ) );
				break;
			case ErrorQuest::E_QUEST_LIMIT_ZEN:
				SendSystemMsg( ownerPlayer, SystemMessage( L"sys", L"Msg_Quest_Err_ZenFull" ) );
				break;
			case ErrorQuest::E_QUEST_LIMIT_GREENZEN:
				SendSystemMsg( ownerPlayer, SystemMessage( L"sys", L"Msg_Quest_Err_GreenZenFull" ) );
				break;
			case ErrorQuest::E_QUEST_LIMIT_CONTRIBUTION_POINT:
				SendSystemMsg( ownerPlayer, SystemMessage( L"sys", L"Msg_Quest_Err_ContributionFull" ) );
				break;
#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
			case ErrorQuest::timeOverPlayQuest:
				{
					SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
					sysMsg.SetParam(L"quest", req->questId);
					SendSystemMsg(ownerPlayer, sysMsg);					
				}
				break;
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313
		}
		actionQuest->SendQuestError( ownerPlayer, err );
	}
	else 
	{
		actionQuest->FinishQuest( req->questId );
	}
}

void StatePlayerBase::onReqQuestVolumeCheck( EventPtr& e )
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	EReqQuestVolumeCheck* req = static_cast< EReqQuestVolumeCheck* >( e.RawPtr() );
	VERIFY_RETURN(req, );

	auto actSector = owner->GetAction<ActionSector>();
	auto sector = actSector->GetSector();
	auto manager = sector->GetQuestMissionDecoratorManager();
 	manager->OnEvent( QuestMissionDecoratorCondition::MISSION_VOLUME_IN, owner, nullptr, { req->volumeId } );

	theQuestSystem.OnVolumeIn( owner, req->volumeId );
}




void StatePlayerBase::onReqAdvantureEventInfo(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ActionPlayerQuest* actionQuest = GetEntityAction(owner);
	VERIFY_RETURN(actionQuest, );

	actionQuest->SendAdvantureInfo();
}


void StatePlayerBase::onReqAdvanturePageReward(EventPtr& e)
{
	//! 페이지 보상 요청
	EReqAdvanturePageReward * req = static_cast<EReqAdvanturePageReward *>(e.RawPtr());

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );	
	
	ErrorItem::Error eError = player->GetQuestAction().ProcAdvanturePageReward(req->advanturePageIndex);
	if (eError != ErrorGame::SUCCESS)
	{
		MU2_WARN_LOG(LogCategory::QUEST, "ProcAdvanturePageReward failed - player(%s), error(%u)", player->ToString().c_str(), eError);

		EResAdvanturePageReward * res = NEW EResAdvanturePageReward;
		res->nextContents = AdvantureContentType::PAGE;
		res->eError = eError;
		player->SendToClient(EventPtr(res));
	}
}

void StatePlayerBase::onReqAdvantureNextReport(EventPtr& e)
{
	EReqAdvantureNextReport * req = static_cast<EReqAdvantureNextReport *>(e.RawPtr());
	req;

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ActionPlayerQuest* actionQuest = GetEntityAction(owner);
	VERIFY_RETURN(actionQuest, );

	ErrorItem::Error eError = actionQuest->ProcAdvantureNextReport();
	if (eError != ErrorItem::SUCCESS)
	{
		EResAdvantureNextReport * res = NEW EResAdvantureNextReport;
		res->advantureNextReportIndex = actionQuest->GetAdvantureCurReport();
		res->advanturePageIndex = actionQuest->GetAdvantureCurPage();
		res->mission1Index = actionQuest->GetAdvantureMission1Index();
		res->mission2Index = actionQuest->GetAdvantureMission2Index();
		res->eError = eError;

		SERVER.SendToClient(owner, EventPtr(res));
	}
}


#ifdef __Reincarnation__jason_180628__
#else
void StatePlayerBase::onResDbInsertSpecialSkill( EventPtr& e )
{
	EResDbInsertSpecialSkill* res = static_cast< EResDbInsertSpecialSkill* >( e.RawPtr() );
	if ( ErrorDB::SUCCESS != res->eError )
	{
		MU2_WARN_LOG(LogCategory::CONTENTS, L"[EResDbInsertSpecialSkill][result:%d][event:%s]", res->eError, e->ToString().c_str() );
	}	
}
#endif

void StatePlayerBase::onReqGameFlyReady( EventPtr& e )
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ActionSector* actSector = GetEntityAction( owner );
	VERIFY_RETURN(actSector, );

	ENtfGameFlyReady* ntf = NEW ENtfGameFlyReady;
	ntf->entityId = owner->GetId();
	actSector->NearGridCast( EventPtr( ntf ) );
}

void StatePlayerBase::onReqGameFlyCancel( EventPtr& e )
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN( owner && owner->IsValid(), );

	ActionSector* actSector = GetEntityAction( owner );
	VERIFY_RETURN(actSector, );

	ENtfGameFlyCancel* ntf = NEW ENtfGameFlyCancel;
	ntf->entityId = owner->GetId();
	actSector->NearGridCast( EventPtr( ntf ) );
}

//클라이언트 또는 월드서버의 요청에 의한 저장
void StatePlayerBase::onEcw2zReqRegistFootHold( EventPtr& e )
{
	Ecw2zReqRegistFootHold *req = static_cast< Ecw2zReqRegistFootHold* >( e.RawPtr() );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );	

	owner->RegistFootHold( req->positionIndex );
}

void StatePlayerBase::onReqGameExploredPortals( EventPtr& e )
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN( owner && owner->IsValid(), );

	EResGameExploredPortals* resp = NEW EResGameExploredPortals;
	resp->exploredPortals = owner->GetExploredPortals();
	SERVER.SendToClient( owner, EventPtr( resp ) );
}

Bool StatePlayerBase::moveToFootholdPosition(EczReqCharMoveGoodPosition::MoveType eType)
{
	VERIFY_RETURN(eType != EczReqCharMoveGoodPosition::None, false);

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), false );

	Sector* currSector = player->GetSector();
	VERIFY_RETURN(currSector, false);		

	const PositionElem* foundFoothold = NULL;

	if (eType == EczReqCharMoveGoodPosition::DungeonFoothold)
	{	
		if ( currSector->IsInstance() )
		{
			// 현재 던전내부에 있고, 이동할 곳이 던전이면, 던전 내부 거점 사용

			foundFoothold = SCRIPTS.GetFoothold(player->GetFootholdAtInstance());

			if (foundFoothold && foundFoothold->pos == player->GetCurrPos() && foundFoothold->GetDestZoneIndex() == player->GetCurrZoneIndex() )
			{
				// 현재위치와 같다면 필드 거점을 찾을 수 있도록 거점을 지운다.
				foundFoothold = NULL;
			}
		}
	}
	
	if( foundFoothold == NULL)
	{
		// 던전 내부로 세팅이 되지 않았다면,

		foundFoothold = SCRIPTS.GetFoothold(player->GetFootholdAtChannel());
		if (foundFoothold && foundFoothold->pos == player->GetCurrPos() && foundFoothold->GetDestZoneIndex() == player->GetCurrZoneIndex())
		{
			foundFoothold = NULL;
		}
	}

	VERIFY_RETURN(foundFoothold, false);

	//=============================================================
	// 월드서버로 존이동 요청
	//=============================================================
	{
		const WorldElem* worldElem = SCRIPTS.GetWorldScript(foundFoothold->GetDestZoneIndex());
		VERIFY_RETURN(worldElem, false);

		EzwReqChangeMap* reqToWorld = NEW EzwReqChangeMap;
		reqToWorld->toIndexPositionMovable = foundFoothold->indexPosition;

		InstanceDungeonInfo info;
		info.SetDungeonType(worldElem->ToDungeonType());
		
		if (worldElem->IsInstance() )
		{	
			info.moveUpPositionIndex = foundFoothold->indexPosition;			
		}

		reqToWorld->toInsInfoList.push_back(info);
		SERVER.SendChangemapToWorld(player, EzwReqChangeMap::moveToFootholdPosition, EventPtr(reqToWorld), true);
	}
	

	return true;
}

void StatePlayerBase::moveToStartPosition()
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player,);

	const PositionElem* positionElem = SCRIPTS.GetPositionElem(20182); // 최종 듀드린 항구로 보낸다.
	VERIFY_RETURN(positionElem, );
	
	// foothold와 동일한 정보
	EzwReqChangeMap* reqToWorld = NEW EzwReqChangeMap;
	reqToWorld->toIndexPositionMovable = positionElem->indexPosition;
	SERVER.SendChangemapToWorld(player, EzwReqChangeMap::moveToStartPosition, EventPtr(reqToWorld), true);
}

void StatePlayerBase::onEczReqCharMoveGoodPosition( EventPtr& e )
{
	EczReqCharMoveGoodPosition* req = static_cast<EczReqCharMoveGoodPosition*>( e.RawPtr() );

	if ( false == moveToFootholdPosition(req->eMoveType) )
	{
		moveToStartPosition();
	}
}

void StatePlayerBase::onResDbGetReturnMails(EventPtr& e)
{
	// 본인이 보낸 메일 중에서 반송 받을 메일이 있는지 찾아서 반송 받는다.
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	ActionPlayerMailBox* actMailBox = GetEntityAction(player);
	VERIFY_RETURN(actMailBox, );

	EResDbGetReturnMails* res = static_cast<EResDbGetReturnMails*>(e.RawPtr());
	if (ErrorDB::SUCCESS != res->eError)
	{
		MU2_WARN_LOG(LogCategory::CONTENTS, L"StatePlayerBase::onResDbGetReturnMails > request failed. event = %s", e->ToString().c_str());
		EReqDbGetMailCount* reqToDb = NEW EReqDbGetMailCount;
		reqToDb->curDate = actMailBox->GetAdjustedSysTime();
		reqToDb->charId = player->GetCharId();
		reqToDb->accId = player->GetAccountId();
		SERVER.SendToDb(player, EventPtr(reqToDb));
		return;
	}

	// 	ActionPlayerInventory* actPlayerInven = GetEntityAction( owner );
	// 	MU2_ASSERT( actPlayerInven );
	// 
	// 	actPlayerInven->SetUpItems( res->items );

	for (Byte index = 0; index < res->mails.size(); ++index)
	{
		if (actMailBox->IsMailTimeOver(res->mails[index]))
		{
			Int32 ret = actMailBox->RemoveMail(res->mails[index]);
			if (ret != ErrorMail::SUCCESS)
			{
				continue;
			}
		}
	}

	if (res->mails.size() == Limits::MAX_ZONE_SEND_MAIL_COUNT)
	{
		AttributePlayer* attrPlayer = GetEntityAttribute(player);
		VERIFY_RETURN(attrPlayer, );

		EReqDbGetReturnMails* reqRtrnMail = NEW EReqDbGetReturnMails;
		UInt32 mailIndex = static_cast<UInt32>(res->mails.size() - 1);
		reqRtrnMail->lastId = res->mails[mailIndex].id;
		reqRtrnMail->curDate = actMailBox->GetAdjustedSysTime();
		reqRtrnMail->charId = attrPlayer->charId;
		SERVER.SendToDb(player, EventPtr(reqRtrnMail));
	}
	else
	{
		EReqDbGetMailCount* reqToDb = NEW EReqDbGetMailCount;
		reqToDb->curDate = actMailBox->GetAdjustedSysTime();
		reqToDb->charId = player->GetCharId();
		reqToDb->accId = player->GetAccountId();
		SERVER.SendToDb(player, EventPtr(reqToDb));
	}
}

void StatePlayerBase::onReqGameSyncMailList( EventPtr& )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	ActionPlayerMailBox& actMailBox = player->GetMailBoxAction();

	VERIFY_RETURN(player->GetMembershipAction().CanUseRemoteNpcFunction(NpcFunctionType::NPC_FUNCTION_POST_BOX, 0), );

	EReqDbGetReturnMails* reqRtrnMail = NEW EReqDbGetReturnMails;
	reqRtrnMail->lastId = 0;
	reqRtrnMail->curDate = actMailBox.GetAdjustedSysTime();
	reqRtrnMail->charId = player->GetCharId();
	SERVER.SendToDb( player, EventPtr( reqRtrnMail ) );

	// 새로운 메일이 있을때나 로딩이 한번도 안됬을떄만 db요청
	if ( actMailBox.GetNewMailCnt() > 0 || false == actMailBox.IsMailLoaded() )
	{
		// 메모리 클리어
		actMailBox.Init();
		InvenMail* mailInven = player->GetInventoryAction().GetInven<InvenMail>(InvenType::MAIL_BAG);
		if (false == player->GetInventoryAction().isWorking())
		{
			mailInven->ClearSlot();
		}
		
		//VERIFY_DO(mailInven->ClearBagInMemory(), MustErrorCheck);

		// 로드
		theGameMsg.SendGameDebugMsg( player, L"메일을 로드합니다..\n");
		actMailBox.ReqDbLoadMails( MailBoxType::SPAM );
		actMailBox.ReqDbLoadMails( MailBoxType::INBOX );
		actMailBox.ClearNewMailCnt();
	}
}

void StatePlayerBase::onResDbGetMailCount( EventPtr& e )
{
	EResDbGetMailCount* res = static_cast< EResDbGetMailCount* >( e.RawPtr() );
	
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	if ( ErrorDB::SUCCESS != res->eError )
	{
		MU2_WARN_LOG( LogCategory::CONTENTS, L"StatePlayerBase::onResDbGetMailCount > request failed. event = %s", e->ToString().c_str());
		EzcResGameSyncMailList* resToCli = NEW EzcResGameSyncMailList;
		SERVER.SendToClient( owner, EventPtr(resToCli) );
		return;
	}


	ActionPlayerMailBox* actMailBox = GetEntityAction( owner );
	VERIFY_RETURN(actMailBox, );

	actMailBox->SyncMailBox( res->mailCount );

	EzcResGameSyncMailList* resToCli = NEW EzcResGameSyncMailList;
	for( Byte idx = 0; idx < MailBoxType::MAX_MAIL_BOX_COUNT; ++idx)
	{
		resToCli->mailCount[idx] = res->mailCount[idx];
	}
	SERVER.SendToClient( owner, EventPtr(resToCli) );
}

void StatePlayerBase::onResDbGetMails( EventPtr& e )
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	EResDbGetMails* resDb = static_cast< EResDbGetMails* >( e.RawPtr() );
	if(ErrorDB::SUCCESS != resDb->eError )
	{
		MU2_WARN_LOG( LogCategory::CONTENTS, L"StatePlayerBase::onResDbGetMails > request failed. event = %s", e->ToString().c_str());
		return;
	}
		

	size_t size = resDb->mails.size();
	MU2_ASSERT( size <= Limits::MAX_ZONE_SEND_MAIL_COUNT ); // 20개

	ActionPlayerMailBox& actMailBox = owner->GetMailBoxAction();	

	std::set<UInt64> badItems; // 메일에서 따로 처리할게 있을까 모르겠다.
	Inven::LoadItemsFromDb(owner, resDb->items, badItems);

	for ( Byte index = 0; index < size; ++index )
	{
		if (actMailBox.IsMailTimeOver( resDb->mails[index] ) )
		{
			continue;
		}

		actMailBox.RecvMail( resDb->mails[index] );
	}
}

void StatePlayerBase::onReqGameGetMailList( EventPtr& e )
{
	EReqGameGetMailList* req = static_cast< EReqGameGetMailList* >( e.RawPtr() );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN( owner && owner->IsValid(), );
	

	MailBoxType::Type mailBoxType = static_cast<MailBoxType::Type>(req->mailBoxType);
	UInt32 mailBoxCnt = 0;
	if ( false == owner->GetMailBoxAction().GetMailBoxCnt(mailBoxType, mailBoxCnt) )
	{
		MU2_ASSERT( mailBoxCnt );
		EResGameGetMailList* res = NEW EResGameGetMailList;
		res->eError = ErrorMail::E_FAILED;
		SERVER.SendToClient( owner, EventPtr(res) );
		return;
	}

	MailBox* mailBox = owner->GetMailBoxAction().GetMailBox( mailBoxType );
	VERIFY_RETURN(mailBox, );

	if ( mailBoxCnt < mailBox->GetMaxSize() )
	{
		owner->GetMailBoxAction().ReqDbLoadMails( mailBoxType );
	}	

	EResGameGetMailList* res = NEW EResGameGetMailList;
	EventPtr resPtr(res);
	
	ErrorMail::Error eError = owner->GetMailBoxAction().GetMailList(req->lastMailId, mailBoxType, res->mailList);	
	res->eError = eError;
	res->mailBoxType = req->mailBoxType;	
	SERVER.SendToClient( owner, resPtr );
}

void StatePlayerBase::onReqGameSendMail( EventPtr& e )
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ErrorMail::Error eError = reqGameSendMail( e );
 	if ( eError != ErrorMail::SUCCESS)
 	{
		if( eError == ErrorItem::E_REFER_SYSTEM_MSG )
		{
			ActionPlayerMailBox* actMail = GetEntityAction( owner );
			VERIFY_RETURN(actMail, );

			std::wstring sysMsg;
			sysMsg.assign( actMail->GetLastSystemMessage() );
			SendSystemMsg( owner, SystemMessage( L"sys", sysMsg) );
			return;
		}

		EResGameSendMail* res = NEW EResGameSendMail;
		res->result = eError;
		SERVER.SendToClient( owner, EventPtr(res) );
	}
}

ErrorMail::Error StatePlayerBase::reqGameSendMail( EventPtr& e )
{
	EReqGameSendMail* req = static_cast< EReqGameSendMail* >( e.RawPtr() );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN( owner, ErrorMail::E_FAILED );

	ActionPlayerMailBox* actMail = GetEntityAction( owner );
	VERIFY_RETURN( actMail, ErrorMail::E_FAILED );

	if ( actMail->IsSendingMail())
	{
		return ErrorMail::E_FAILED;
	}

	if ( owner->IsTrading() )
	{
		SystemMessage sysMsg( L"sys", L"Msg_UserTrade_Invalid_Not_Work" );
		SendSystemMsg( owner, sysMsg );
		return ErrorMail::E_TRADING;
	}

	//! 클라에서 게정 메일을 보낼 수 없도록 한다.
	req->mail.accMailAccId = 0;

	ActionPlayerMembership* actMembership = GetEntityAction(owner);
	if (false == actMembership->CanUseRemoteNpcFunction(NpcFunctionType::NPC_FUNCTION_POST_BOX, 0))
	{
		return ErrorMail::E_HAVE_NO_MEMBERSHIP_QUALIFICATION;	// 멤버쉽 자격을 갖고 있지 않다.
	}
	
	ErrorMail::Error eError = ErrorMail::SUCCESS;

	eError = actMail->Check( req->mail );
	VALID_RETURN( eError == ErrorMail::SUCCESS, eError );

	eError = actMail->SendMailPreProcess( req->mail );
	VALID_RETURN( eError == ErrorMail::SUCCESS, eError );

	actMail->SetSendingState( true );
	return eError;
}

void StatePlayerBase::onResDbInsertMail( EventPtr& e )
{
	EResDbInsertMail* res = static_cast< EResDbInsertMail* >( e.RawPtr() );
	
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ActionPlayerMailBox* actMailBox = GetEntityAction( owner );
	VERIFY_RETURN(actMailBox, );

	actMailBox->SetSendingState( false );

	ErrorMail::Error eError = ErrorMail::SUCCESS;
	switch( res->eError )
	{
	case ErrorDB::SUCCESS:
	{
		ELoopbackNtfGameGotNewMail* ntfNewMail = NEW ELoopbackNtfGameGotNewMail;
		ntfNewMail->receiverName = res->mail.recverName;
		ntfNewMail->mailType = res->mail.type;
		ntfNewMail->receiverAccId = res->mail.accMailAccId;
		SERVER.SendToWorldServer( owner, EventPtr( ntfNewMail ) );
		break;
	}

	case ErrorDB::E_RECORD_NOT_FOUND:
		if ( res->mail.IsOn( MailFlagType::RETURN ) )
		{
			// 메일 반송 불가능 이므로 메일 삭제
			actMailBox->RemoveMailItems( res->mail );
			eError = ErrorMail::SUCCESS;
		}
		else
		{
			eError = ErrorMail::E_INVAILD_CHARID;
		}
		break;

	default:
		eError = ErrorMail::E_FAILED;
		break;
	}

	if(ErrorDB::SUCCESS != res->eError )
	{
		MU2_WARN_LOG( LogCategory::CONTENTS, L"StatePlayerBase::onResDbInsertMail > request failed. event = %s", e->ToString().c_str());
	}

	{
		EResGameSendMail* resToCli = NEW EResGameSendMail;
		resToCli->result = eError;
		SERVER.SendToClient( owner, EventPtr(resToCli) );
	}
}

void StatePlayerBase::onReqGameDelMail( EventPtr& e )
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ErrorMail::Error eError = reqGameDelMail( e );
	if ( eError != ErrorMail::SUCCESS)
	{
		EResGameDelMail* res = NEW EResGameDelMail;
		res->result = eError;
		SERVER.SendToClient( owner, EventPtr(res) );
	}
}

ErrorMail::Error StatePlayerBase::reqGameDelMail( EventPtr& e )
{
	EReqGameDelMail* req = static_cast< EReqGameDelMail* >( e.RawPtr() );

	EntityPlayer* ownPlayer = GetOwnerPlayer();
	VERIFY_RETURN( ownPlayer, ErrorMail::E_FAILED);

#ifdef __Patch_Mail_HwaYeong_20190117
	ActionPlayerMailBox& actMailBox = ownPlayer->GetMailBoxAction();
	ErrorMail::Error error = actMailBox.RemoveMails(req->removeMailIds);

	return error;
#else
	ActionPlayerMailBox* actMailBox = GetEntityAction( ownPlayer  );
	VERIFY_RETURN( actMailBox, ErrorMail::E_FAILED );

	Mail* mail = actMailBox->GetMail( req->mailId );
	if( !mail )
	{
		return ErrorMail::E_INVALID_MAILID;
	}

	// 동기화를 위해 보관기간 지난 메일은 유저가 직접 삭제할 수 없다. (시스템이 삭제 해야함)
	if ( actMailBox->IsMailTimeOver( mail->GetMailData() ) )
	{
		return ErrorMail::E_EXCEED_PERIOD;
	}

	auto actPlayerInventory = ownPlayer->GetAction<ActionPlayerInventory>();
	if (mail->GetMailData().IsExistItem())
	{
		return ErrorMail::E_HAVE_ITEM;
	}
	
	//! 기존 코드의 && 은 잘못된 듯.
	if(actPlayerInventory->IsWorking())
	{
		return ErrorMail::E_TRANSACTION_FAILED;
	}

	// 아이템 포함된 편지는 반송시킨다. ( But, 이미 반송된 편지는 삭제 )
	MailData mailData = mail->GetMailData();
	ErrorMail::Error eError = actMailBox->RemoveMail( mailData );
	VALID_RETURN(eError == ErrorMail::SUCCESS, eError);

	EResGameDelMail* res = NEW EResGameDelMail;
#ifdef __Patch_Mail_HwaYeong_20190117
	res->removeMails = req->mailId;
#else
	res->mailId = req->mailId;
#endif
	res->result = ErrorMail::SUCCESS;
	SERVER.SendToClient( ownPlayer, EventPtr(res) );

	EReqLogDb2* log = NEW EReqLogDb2;
	EventPtr logEventPtr(log);
	EventSetter::FillLogForEntity(LogCode::E_DELETE_MAIL, ownPlayer, log->action);

	log->action.subInfo = mailData.senderName;
	log->action.var32s.push_back(0);
	log->action.var64s.push_back(mailData.id);
	
	SendDataCenter(logEventPtr);
	return ErrorMail::SUCCESS;
#endif
}

// 새 메일 도착 알림
void StatePlayerBase::onNtfGameGotNewMail( EventPtr& e )
{
	EwzNtfGameGotNewMail* ntf = static_cast<EwzNtfGameGotNewMail*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	if ( ntf->receiverAccId != owner->GetAccountId() && ntf->receiverName != owner->GetCharName() )
	{
		return;
	}

	owner->GetMailBoxAction().IncNewMailCnt();
}

void StatePlayerBase::onReqGameOpenMail( EventPtr& e )
{
	EReqGameOpenMail* req = static_cast< EReqGameOpenMail* >( e.RawPtr() );

	EntityPlayer* ownPlayer = GetOwnerPlayer();
	VERIFY_RETURN( ownPlayer && ownPlayer->IsValid(), );

	ActionPlayerMailBox* actMailBox = GetEntityAction( ownPlayer  );
	VERIFY_RETURN(actMailBox, );

	MailData sendMailData;
	ItemDatas itemLst;
	ErrorMail::Error eError = actMailBox->ReadMail( req->mailId, sendMailData, itemLst );
	if ( eError != ErrorMail::SUCCESS)
	{
		EResGameOpenMail* res = NEW EResGameOpenMail;
		res->result = eError;
		SERVER.SendToClient( ownPlayer, EventPtr(res) );
		return;
	}

	EResGameOpenMail* res = NEW EResGameOpenMail;
	res->items = itemLst;
	res->mail = sendMailData;
	res->result = ErrorMail::SUCCESS;
	SERVER.SendToClient( ownPlayer, EventPtr(res) );

	EReqLogDb2* log = NEW EReqLogDb2;
	EventPtr logEventPtr(log);
	EventSetter::FillLogForEntity(LogCode::E_READ_MAIL, ownPlayer, log->action);

	if(MailType::SYSTEM == sendMailData.type)
		log->action.subInfo = L"System"; 
	else
		log->action.subInfo = sendMailData.senderName;
	Int32 deldate = sendMailData.date.wMonth * 100 + sendMailData.date.wDay;
	log->action.var32s.push_back(deldate);
	log->action.var64s.push_back(req->mailId);
	
	SendDataCenter(logEventPtr);
}

void StatePlayerBase::onReqGameGetMailMoney( EventPtr& e )
{
	EReqGameGetMailMoney* req = static_cast< EReqGameGetMailMoney* >( e.RawPtr() );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN( owner && owner->IsValid(), );

	ActionPlayerMailBox* actMailBox = GetEntityAction( owner );
	VERIFY_RETURN(actMailBox, );

	Mail* mail = actMailBox->GetMail( req->mailId );
	VALID_DO(mail, AllowNull);

	ErrorMail::Error eError = actMailBox->AcquireMailMoney( mail );
	if ( eError != ErrorMail::SUCCESS)
	{
		EResGameGetMailMoney* res = NEW EResGameGetMailMoney;
		res->result = eError;
		SERVER.SendToClient( owner, EventPtr( res ) );
		return;
	}

	EResGameGetMailMoney* res = NEW EResGameGetMailMoney;
	res->mail = mail->GetMailData();
	res->result = ErrorMail::SUCCESS;
	SERVER.SendToClient( owner, EventPtr( res ) );
	mail->SetMoney(0);
}

void StatePlayerBase::onReqGameGetMailItems( EventPtr& e )
{
	EReqGameGetMailItems* req = static_cast< EReqGameGetMailItems* >( e.RawPtr() );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN( owner && owner->IsValid(), );

	ActionPlayerMembership* actMembership = GetEntityAction(owner);
	if (false == actMembership->CanUseRemoteNpcFunction(NpcFunctionType::NPC_FUNCTION_POST_BOX, 0))
	{
		EResGameGetMailItems* res = NEW EResGameGetMailItems;
		res->result = ErrorMail::E_HAVE_NO_MEMBERSHIP_QUALIFICATION;
		SERVER.SendToClient(owner, EventPtr(res));
		return;
	}

	ActionPlayerMailBox* actMailBox = GetEntityAction( owner );
	VERIFY_RETURN(actMailBox, );

	Mail* mail = actMailBox->GetMail( req->mailId );

	ErrorMail::Error eError = actMailBox->AcquireMailItems( mail );
	if ( eError != ErrorMail::SUCCESS)
	{
		auto attr = owner->GetAttribute<AttributePlayer>();
		MU2_WARN_LOG(core::LogCategory::DEFAULT, "onReqGameGetMailItems> Failed. [CharId: %d], [Error: %d]", attr->charId, eError );

		EResGameGetMailItems* res = NEW EResGameGetMailItems;
		res->result = eError;
		SERVER.SendToClient( owner, EventPtr( res ) );
		return;
	}
}

void StatePlayerBase::onReqSuggestTrade( EventPtr& e )
{
	EReqSuggestTrade* req = static_cast< EReqSuggestTrade* > ( e.RawPtr() );

	ErrorItem::Error eError = reqSuggestTrade( e );
	if ( eError != ErrorItem::SUCCESS)
	{
		EntityPlayer* owner = GetOwnerPlayer();
		VERIFY_RETURN(owner && owner->IsValid(), );
		
		ActionPlayerItemTrade* actTrade = GetEntityAction( owner );
		VERIFY_RETURN(actTrade, );

		actTrade->SendItemTradeSysMsg( eError );

		ENtfSuggestTrade* ntf = NEW ENtfSuggestTrade;
		ntf->result = eError;
		ntf->inviteeName = req->inviteeName;
		SERVER.SendToClient( owner, EventPtr(ntf) );
		return;
	}
}

ErrorItem::Error StatePlayerBase::reqSuggestTrade( EventPtr& e )
{
	EReqSuggestTrade* req = static_cast< EReqSuggestTrade* > ( e.RawPtr() );
		
	EntityPlayer* inviter = GetOwnerPlayer();
	VERIFY_RETURN(inviter, ErrorItem::playerNotFound );

	Sector* sector = inviter->GetSector();
	VERIFY_RETURN(sector, ErrorItem::sectorNotFound );

	// 본인에게 거래신청 불가
	if (inviter->GetCharName() == req->inviteeName )
	{
		return ErrorItem::E_TRADE_INVALID_USER;
	}

	// inviter 거래 가능 위치 검사
	if ( inviter->GetSectorAction().IsTradable() == false )
	{
		return ErrorItem::E_TRADE_INVALID_AREA;
	}

	// 캐릭터 이름 없음
	EntityPlayer* invitee = sector->FindPlayerByName( req->inviteeName );
	if ( invitee == nullptr || invitee->isRemove() )
	{
		return ErrorItem::E_TRADE_INVALID_USER;
	}

	// 이미 거래중 or 거래 신청중
	//ActionPlayerItemTrade& actTradeInviter = inviter->GetItemTradeAction();
	//ActionPlayerItemTrade& actTradeInvitee = invitee->GetItemTradeAction();

	if ( inviter->IsWaitingTrade() || inviter->IsTrading() ||
		invitee->IsWaitingTrade() || invitee->IsTrading() )
	{
		return ErrorItem::E_TRADE_ALREADY_TRADING;
	}

	//AttributePlayer* attrPlayerInvitee = GetEntityAttribute( invitee );
	//MU2_ASSERT( attrPlayerInvitee );

	// pvp상태
	if ( inviter->GetDuelPlayerId() != 0 || invitee->GetDuelPlayerId() != 0 )
	{
		return ErrorItem::E_TRADE_USER_STATE;
	}

	// 거래소 진행 중
	if (inviter->GetCurrStateId() == EntityState::STATE_PLAYER_EXCHANGE ||
		invitee->GetCurrStateId() == EntityState::STATE_PLAYER_EXCHANGE )
	{
		return ErrorItem::E_TRADE_REJECTED;
	}

	// 죽은 상태
	if ( inviter->IsDead() || invitee->IsDead() )
	{
		return ErrorItem::E_TRADE_NOT_ALIVE;
	}

	// 전투중 거래 신청 불가
	if (inviter->GetSkillControlAction().IsBattleMode() || invitee->GetSkillControlAction().IsBattleMode() )
	{
		return ErrorItem::E_TRADE_INVALID_COMBAT;
	}
	// TODO 적으로 분류되는 캐릭터 불가( WvW에서 타서버 유저, 이벤트 맵 또는 대전 모드의 적 )

	// 거리체크
	const Float distance = Distance( inviter->GetCurrPos() , invitee->GetCurrPos() );
	if ( distance > ActionPlayerItemTrade::MAX_DISTANCE )
	{
		return ErrorItem::E_INVALID_DISTANCE;
	}

	inviter->GetItemTradeAction().WaitTrade( invitee->GetId() );
	invitee->GetItemTradeAction().WaitTrade( inviter->GetId() );

	theGameMsg.SendGameDebugMsg( inviter, L"%s에게 거래를 신청했습니다.\n", req->inviteeName.c_str() );
	theGameMsg.SendGameDebugMsg( invitee, L"%s로부터 거래신청이 왔습니다. charId:%d\n", inviter->GetCharName().c_str(), inviter->GetCharId() );

	// 상대방에게 거래 신청 
	ENtfTradeSuggested* ntf = NEW ENtfTradeSuggested;
	ntf->inviter     = inviter->GetCharId();
	ntf->inviterName = inviter->GetCharName();
	ntf->waitTime    = ActionPlayerItemTrade::TIME_OUT;
	SERVER.SendToClient( invitee, EventPtr(ntf) );

	return ErrorItem::SUCCESS;
}

void StatePlayerBase::onReqAnswerTradeSuggested( EventPtr& e )
{
 	EReqAnswerTradeSuggested* req = static_cast< EReqAnswerTradeSuggested* > ( e.RawPtr() );
	ErrorItem::Error eError = reqAnswerTradeSuggested( e );
	if ( eError != ErrorItem::SUCCESS)
	{		
		EntityPlayer* invitee = GetOwnerPlayer();
		VERIFY_RETURN( invitee && invitee->IsValid(), );

		EntityPlayer* inviter = invitee->GetSectorAction().GetPlayerInMySectorByCharId( req->inviter );
		if ( inviter && inviter->IsValid() )
		{
			inviter->GetItemTradeAction().SendItemTradeSysMsg( eError );
			ENtfSuggestTrade* ntf = NEW ENtfSuggestTrade;
			ntf->result		 = eError;
			ntf->inviteeName = invitee->GetCharName();
			ntf->response	 = req->response;
			SERVER.SendToClient( inviter, EventPtr(ntf) );
		}

		if ( req->response == true )
		{			
			invitee->GetItemTradeAction().SendItemTradeSysMsg( eError );
			ENtfCancelTrade* ntfInvitee = NEW ENtfCancelTrade;
			ntfInvitee->result = eError;
			SERVER.SendToClient( invitee, EventPtr(ntfInvitee) );
		}
	}
}

ErrorItem::Error StatePlayerBase::reqAnswerTradeSuggested( EventPtr& e )
{
	EReqAnswerTradeSuggested* req = static_cast< EReqAnswerTradeSuggested* > ( e.RawPtr() );

	EntityPlayer* invitee = GetOwnerPlayer();
	VERIFY_RETURN( invitee && invitee->IsValid(), ErrorItem::E_TRADE_INVALID_USER );

	EntityPlayer* inviter = invitee->GetSectorAction().GetPlayerInMySectorByCharId( req->inviter );
	VERIFY_DO(	inviter && inviter->IsValid(),	
				invitee->GetItemTradeAction().Clear();
				return ErrorItem::E_TRADE_INVALID_USER;);

	VALID_RETURN(invitee->IsNotTrading() && invitee->IsNotTrading(), ErrorItem::E_TRADE_ALREADY_TRADING);
	VALID_RETURN(invitee->GetItemTradeAction().IsWaitingTrade(), ErrorItem::E_TRADE_TIME_OUT);

	if (false == inviter->GetItemTradeAction().IsWaitingTrade() )
	{
		invitee->GetItemTradeAction().Clear();
		return ErrorItem::E_TRADE_TIME_OUT;
	}

	if (inviter->GetItemTradeAction().GetWaitingEntityId() != invitee->GetId() ||
		invitee->GetItemTradeAction().GetWaitingEntityId() != inviter->GetId())
	{
		return ErrorItem::E_TRADE_INVALID_USER;
	}	

	ErrorItem::Error eError = ErrorItem::SUCCESS;

	do
	{
		// pvp상태
		VALID_DO(inviter->GetDuelPlayerId() == 0 && invitee->GetDuelPlayerId() == 0, eError = ErrorItem::E_TRADE_USER_STATE; break);

		// 죽은 상태
		VALID_DO(inviter->IsAlive() && invitee->IsAlive(), eError = ErrorItem::E_TRADE_NOT_ALIVE; break);

		// 전투중 거래 불가
		VALID_DO(	false == inviter->GetSkillControlAction().IsBattleMode() && 
					false == invitee->GetSkillControlAction().IsBattleMode(), 
					eError = ErrorItem::E_TRADE_INVALID_COMBAT; break);

		VALID_DO(	req->response == true &&
					inviter->GetCurrStateId() != EntityState::STATE_PLAYER_EXCHANGE &&
					invitee->GetCurrStateId() != EntityState::STATE_PLAYER_EXCHANGE, 
					eError = ErrorItem::E_TRADE_REJECTED; break);

		// 거래신청자가 만든 누가만드든 상관없음
		TradeItemPtr trade = inviter->GetItemTradeAction().CreateTrade(invitee->GetId());
		VERIFY_DO(trade, eError = ErrorItem::E_FAILED; break);

		// 거래받은사람 거래에 조인
		eError = invitee->GetItemTradeAction().JoinTrade(inviter->GetId(), trade);
		VERIFY_DO(eError == ErrorItem::SUCCESS, break);

	}
	while (false);


	if (eError != ErrorItem::SUCCESS)
	{
		inviter->GetItemTradeAction().Clear();
		invitee->GetItemTradeAction().Clear();
		return eError;
	}

	return ErrorItem::SUCCESS;
}

void StatePlayerBase::onReqChangeTradeInfo( EventPtr& e )
{
	EReqChangeTradeInfo* req = static_cast< EReqChangeTradeInfo* > ( e.RawPtr() );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN( owner &&  owner->IsValid(), );
		
	ErrorItem::Error eError = owner->GetItemTradeAction().UpdateTradeInfo( req->items, req->money );
	theGameMsg.SendGameDebugMsg( owner, L"거래 아이템 등록 결과:%d 아이템 갯수:%d\n", eError, req->items.size() );
	if ( eError != ErrorItem::SUCCESS)
	{
		owner->GetItemTradeAction().SendItemTradeSysMsg( eError );
		return; 
	}
}


void StatePlayerBase::onReqReadyTrade( EventPtr& e )
{
	EReqReadyTrade* req = static_cast< EReqReadyTrade* > ( e.RawPtr() );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner &&  owner->IsValid(), );

	ErrorItem::Error eError = owner->GetItemTradeAction().SetReady( req->ready );
	if ( eError != ErrorItem::SUCCESS )
	{
		owner->GetItemTradeAction().SendItemTradeSysMsg( eError );
		return; 
	}
}

void StatePlayerBase::onReqStartTrade( EventPtr& e )
{
	EReqStartTrade* req = static_cast< EReqStartTrade* > ( e.RawPtr() );
	UNREFERENCED_PARAMETER( req );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner &&  owner->IsValid(), );

	ErrorItem::Error ret = owner->GetItemTradeAction().SetStart();
	if ( ret != ErrorItem::SUCCESS )
	{
		owner->GetItemTradeAction().SendItemTradeSysMsg( ret );
		return; 
	}
}

void StatePlayerBase::onReqCancelTrade( EventPtr& e )
{
	EReqCancelTrade* req = static_cast< EReqCancelTrade* > ( e.RawPtr() );
	UNREFERENCED_PARAMETER(req);

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner &&  owner->IsValid(), );

	EResCancelTrade* res = NEW EResCancelTrade;
	EventPtr evt( res );
	{
		res->result = ErrorItem::SUCCESS;
	}
	
	ActionPlayerItemTrade& act = owner->GetItemTradeAction();

	if ( owner->IsTrading() )
	{
		ErrorItem::Error ret = act.CancelTrade();
		if ( ret != ErrorItem::SUCCESS )
		{
			act.SendItemTradeSysMsg( ret );
			res->result = ret;
			SERVER.SendToClient( owner, evt );
			return; 
		}
	}
	else if (act.IsWaitingTrade())
	{
		act.Clear();
	}

	SERVER.SendToClient( owner, evt );
}

void StatePlayerBase::onNtfCancelTrade( EventPtr& e )
{
	ENtfCancelTrade* ntf = static_cast< ENtfCancelTrade* > ( e.RawPtr() );

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player &&  player->IsValid(), );

	ActionPlayerItemTrade& act = player->GetItemTradeAction();
	act.Clear();

	ErrorItem::Error ret = ErrorItem::E_TRADE_FORCE_CANCEL;
	if ( ntf->result != ErrorItem::SUCCESS )
	{
		ret = ntf->result;
	}
	
	act.SendItemTradeSysMsg( ret );
	
	ENtfCancelTrade* ntfCli = NEW ENtfCancelTrade;
	ntfCli->result = ErrorItem::SUCCESS;
	SERVER.SendToClient( player, EventPtr(ntfCli) );
}

void StatePlayerBase::onNtfTradeCompleted( EventPtr& e )
{
	ENtfTradeCompleted* ntf = static_cast< ENtfTradeCompleted* > ( e.RawPtr() );

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), ); 
	
	player->GetItemTradeAction().Clear();

	SystemMessage sysMsg(L"sys", L"Msg_UserTrade_Success_Trading" );
	SendSystemMsg( player, sysMsg );

	ENtfTradeCompleted* ntfCli = NEW ENtfTradeCompleted;
	ntfCli->eError = ntf->eError;
	ntfCli->money = ntf->money;
	ntfCli->items = ntf->items;
	SERVER.SendToClient( player, EventPtr(ntfCli) );
}

void StatePlayerBase::onReqItemExtract( EventPtr& e )
{
	EReqItemExtract* req = static_cast<EReqItemExtract*>(e.RawPtr());

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), );

	Slots extraceSlots;
	std::copy(req->list.begin(), req->list.end(), std::back_inserter(extraceSlots));

	ErrorItem::Error eError = player->GetInventoryAction().Extracts(extraceSlots);

	if (eError != ErrorItem::SUCCESS)
	{
		MU2_WARN_LOG(LogCategory::ITEM, "Extracts failed - player(%s), error(%u) ", player->ToString().c_str(), eError);

		EResItemExtract* res = new EResItemExtract;
		res->result = eError;
		player->SendToClient(EventPtr(res));
	}
}

void StatePlayerBase::onReqBuyStorageBag( EventPtr&)
{
}

void StatePlayerBase::onReqExpandStorageTab( EventPtr& e )
{
	EReqExpandStorageTab* req = static_cast<EReqExpandStorageTab*>( e.RawPtr() );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ErrorItem::Error eError = reqExpandStorageTab( req );
	if ( eError != ErrorItem::SUCCESS) 
	{
		EResExpandStorageSlot* res = NEW EResExpandStorageSlot;
		res->result  = eError;
		SERVER.SendToClient( owner, EventPtr(res) );
		theGameMsg.SendGameDebugMsg( owner, L"슬롯확장 실패:%d\n", eError);
	}
}

ErrorItem::Error StatePlayerBase::reqExpandStorageTab(EReqExpandStorageTab* req )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	Bool isAddBag;
	StorageInfo nextInfo;
	const StorageInfoElem* nextElem = InvenStorageAccount::GetNextTabInfo(*player, __out nextInfo, __out isAddBag);
	VERIFY_RETURN(nextElem, ErrorItem::scriptNotFound);

	ErrorItem::Error eError = InvenStorageAccount::IsCanExpandTab(*player, nextInfo, *nextElem);
	VALID_RETURN(ErrorItem::SUCCESS == eError, eError);
	
	AccountStorageTransaction* transaction = NEW AccountStorageTransaction(
		__FUNCTION__, __LINE__
		, player
		, ProtocolItem::EREQ_EXPAND_STORAGE_SLOT
		, isAddBag
		, nextInfo
		, LogCode::E_PRIVATE_STORAGE_EXPAND );

	TransactionPtr transPtr(transaction);
	{
		StorageInfoScript::CostType::Type costType = static_cast<StorageInfoScript::CostType::Type>(nextElem->priceType);
		switch (costType) 
		{
		case StorageInfoScript::CostType::GreenZen:
			transaction->AddGreenZen(nextElem->price);
			break;
		case StorageInfoScript::CostType::RedZen:
			transaction->AddRedZen(nextElem->price);
			break;
		}

		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::TranRegisterFailed);
	}

	return ErrorItem::SUCCESS;
}

void StatePlayerBase::onReqExpandStoragePeriod( EventPtr& e )
{
	EReqExpandStoragePeriod* req = static_cast<EReqExpandStoragePeriod*>( e.RawPtr() );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ErrorItem::Error eError = reqExpandStoragePeriod( req );
	if ( eError != ErrorItem::SUCCESS)
	{
		EResExpandStorageSlot* res = NEW EResExpandStorageSlot;
		res->result  = eError;
		SERVER.SendToClient( owner, EventPtr(res) );

		theGameMsg.SendGameDebugMsg( owner, L"창고기한추가 실패:%d\n", eError);
	}
}

ErrorItem::Error StatePlayerBase::reqExpandStoragePeriod( EReqExpandStoragePeriod* req )
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), ErrorItem::playerNotFound );

	InvenStorageAccount* foundInven = owner->GetInventoryAction().GetInven<InvenStorageAccount>(req->eInven);
	VERIFY_RETURN(foundInven, ErrorItem::inventoryNotFound);

	StorageInfo storaageInfo;
	ErrorItem::Error eError = foundInven->ExpandPeriod(req->period, __out storaageInfo);
	if ( eError != ErrorItem::SUCCESS )
	{
		InvenStorageAccount::SendStorageSysMsg(*owner,eError );
		return eError;
	}

	EResExpandStoragePeriod* res = NEW EResExpandStoragePeriod;
	res->result  = eError;
	res->bagInfo = storaageInfo;
	SERVER.SendToClient( owner, EventPtr(res) );
	return ErrorItem::SUCCESS;
}

void StatePlayerBase::onReqStorageMove( EventPtr& e )
{
	EReqStorageMove* req = static_cast< EReqStorageMove* >( e.RawPtr() );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN( owner && owner->IsValid(), );
	
	ErrorItem::Error eError = owner->GetInventoryAction().StorageMove( req->src, req->tar );
	if (eError != ErrorItem::SUCCESS)
	{
		EResMove* res = NEW EResMove;
		res->eError = eError;
		SERVER.SendToClient( owner, EventPtr( res ) );
	}
}

void StatePlayerBase::setupMoveDispatch()
{
	m_impl->moveHandler.Initialize( this );
}

//void StatePlayerBase::onResDbGetMakingFormulaList( EventPtr& e )
//{
//	EResDbGetMakingFormulaList* res = static_cast< EResDbGetMakingFormulaList* >( e.RawPtr() );
//	ActionPlayerMaking* actMaking = GetEntityAction( static_cast<Entity4Zone*>( m_hsm->GetOwner() ) );
//	MU2_ASSERT( actMaking );
//	actMaking->SetupDb( res->formulas );
//}
void StatePlayerBase::onResDbInsMakingFormula( EventPtr& e )
{
	EResDbInsMakingFormula* res = static_cast< EResDbInsMakingFormula* >( e.RawPtr() );
	res;
}

void StatePlayerBase::onResDbDelAllMakingFormulaList( EventPtr& e )
{
	EResDbDelAllMakingFormulaList* res = static_cast< EResDbDelAllMakingFormulaList* >( e.RawPtr() );
	res;
}

void StatePlayerBase::onReqGameMakingCastStart( EventPtr& e )
{
	EntityPlayer* player = GetOwnerPlayer();

	EResGameMakingCastStart* res = NEW EResGameMakingCastStart;
	res->eError = ErrorMaking::E_WRONG_PLAYER_STATE;
	SERVER.SendToClient(player, EventPtr( res ) );

	ActionPlayerMaking* actMaking = GetEntityAction(player);
	actMaking->DebugMsg(L"\nEResGameMakingCastStart failed(%d).", ErrorMaking::E_WRONG_PLAYER_STATE);
}

void StatePlayerBase::onReqGameMakingFormulaAdd( EventPtr& e )
{
	EntityPlayer* player = GetOwnerPlayer();

	EResGameMakingFormulaAdd* res = NEW EResGameMakingFormulaAdd;
	res->eError = ErrorMaking::E_WRONG_PLAYER_STATE;
	SERVER.SendToClient( player, EventPtr( res ) );

	ActionPlayerMaking* actMaking = GetEntityAction(player);
	actMaking->DebugMsg(L"\nEResGameMakingFormulaAdd failed(%d).", ErrorMaking::E_WRONG_PLAYER_STATE );
}

void StatePlayerBase::onReqGameMakingCastCancel( EventPtr& e )
{
	EntityPlayer* player = GetOwnerPlayer();

	EResGameMakingCastCancel* res = NEW EResGameMakingCastCancel;
	res->eError = ErrorMaking::E_NOT_CASTING;
	SERVER.SendToClient( player, EventPtr( res ) );

	ActionPlayerMaking* actMaking = GetEntityAction(player);
	actMaking->DebugMsg(L"\nEResGameMakingCastCancel failed(%d).", ErrorMaking::E_NOT_CASTING );
}

void StatePlayerBase::onReqGameMaking( EventPtr& e )
{
	EntityPlayer* player = GetOwnerPlayer();

	EResGameMaking* res = NEW EResGameMaking;
	res->eError = ErrorMaking::E_NOT_CASTING;
	SERVER.SendToClient(player, EventPtr( res ) );

	ActionPlayerMaking* actMaking = GetEntityAction(player);
	actMaking->DebugMsg(L"\nEResGameMaking failed(%d).", ErrorMaking::E_NOT_CASTING );
}

void StatePlayerBase::onEczReqGamePortal( EventPtr& e )
{
	EczReqGamePortal* req = static_cast< EczReqGamePortal* >( e.RawPtr() );

	EntityPlayer* ownPlayer = GetOwnerPlayer();
	VERIFY_RETURN( ownPlayer && ownPlayer->IsValid(), );

	IndexPortal portalIndex = 0;
	UInt8 difficulty = 0;
	IndexItem itemIndex = 0;
	UInt32 useItemUserLevel = 0;
	if ( 0 != req->usedItemIndex )
	{
		auto porystalInfo = SCRIPTS.GetPorystalInfoElem( req->usedItemIndex );
		VERIFY_RETURN(porystalInfo, );

		portalIndex = porystalInfo->portalIndex;
		difficulty = porystalInfo->dungeon_Difficulty;
		itemIndex = req->usedItemIndex;

		useItemUserLevel = ownPlayer->GetLevel();
	}
	else
	{
		portalIndex = req->portalIndex;
		difficulty = req->difficulty;
	}
#ifdef PORTAL_NEED_QUEST_by_jjangmo_180521
	IndexQuest failQuest = 0;
	ErrorJoin::Error eError = thePortalSystem.Request( ownPlayer, portalIndex, req->advAdMissionItemIndex, difficulty, req->isMatching, failQuest, itemIndex, useItemUserLevel );
	if ( eError != ErrorJoin::SUCCESS )
	{
		MU2_WARN_LOG( LogCategory::JOIN, "onEczReqGamePortal> thePortalSystem.Request failed - player(%s), error(%d)", ownPlayer->ToString().c_str(), eError );

		EwcResGamePortal* res = NEW EwcResGamePortal;
		res->eError = eError;
		res->failQuest = failQuest;
#else
	ErrorJoin::Error eError = thePortalSystem.Request( ownPlayer, portalIndex, req->advAdMissionItemIndex, difficulty, req->isMatching, itemIndex, useItemUserLevel );
	if ( eError != ErrorJoin::SUCCESS )
	{
		MU2_WARN_LOG(LogCategory::JOIN, "onEczReqGamePortal> thePortalSystem.Request failed - player(%s), error(%d)", ownPlayer->ToString().c_str(), eError );

		EwcResGamePortal* res = NEW EwcResGamePortal;
		res->eError = eError;
#endif
		SERVER.SendToClient( ownPlayer, EventPtr( res ) );
		return;
	}
}


void StatePlayerBase::onNtfCommPortalInfoSync( EventPtr& e )
{
	EwzNtfCommPortalInfoSync* sync = static_cast< EwzNtfCommPortalInfoSync* >( e.RawPtr() );

	EntityPlayer* ownPlayer = GetOwnerPlayer();
	VERIFY_RETURN( ownPlayer && ownPlayer->IsValid(), );

	VERIFY_RETURN( ownPlayer->GetPortalAction().PortalInfoSync( sync ), );
}

void StatePlayerBase::onResDbDeleteAllAchieve( EventPtr& e )
{
	EResDbDeleteAllAchieve* res = static_cast< EResDbDeleteAllAchieve* >( e.RawPtr() );
	if ( res->eError != ErrorDB::SUCCESS )
	{		
		MU2_ASSERT( 0 );
		MU2_WARN_LOG( LogCategory::CONTENTS, L"StatePlayerBase::onResDbDeleteAllAchieve > request failed. event = %s", e->ToString().c_str());
		return;
	}
	
	EntityPlayer* owner = GetOwnerPlayer();
	
	theGameMsg.SendGameDebugMsg( owner, L"해당 플레이어의 모든 업적 제거 완료\n" );
}

void StatePlayerBase::onResDbDeleteAchieve( EventPtr& e )
{
	EResDbDeleteAchieve* res = static_cast< EResDbDeleteAchieve* >( e.RawPtr() );
	if ( res->eError != ErrorDB::SUCCESS)
	{		
		MU2_ASSERT( 0 );
		MU2_WARN_LOG( LogCategory::CONTENTS, L"StatePlayerBase::onResDbDeleteAchieve > request failed. event = %s", e->ToString().c_str());
		return;
	}
	
	EntityPlayer* owner = GetOwnerPlayer();
	
	theGameMsg.SendGameDebugMsg( owner, L"해당 플레이어의 특정 업적 제거 완료\n" );
}

void StatePlayerBase::onReqContactEntity( EventPtr& e )
{
	EReqContactEntity* req = static_cast<EReqContactEntity*>( e.RawPtr() );
	EntityPlayer* ownPlayer = GetOwnerPlayer();
	VERIFY_RETURN( ownPlayer && ownPlayer->IsValid(), );

	ActionPlayerContact& contactAction = ownPlayer->GetContactAction();

	if (contactAction.CheckNpcDistance( req->entityId ) == FALSE)
	{
		return;
	}	
	
	if (ownPlayer->IsTrading())
	{
		SendSystemMsg( ownPlayer, SystemMessage( L"sys", L"Msg_UserTrade_Trading" ) );
		return;
	}

	if ( contactAction.IsAbleToContact(req->entityId) )
	{
		Tran(EntityState::STATE_PLAYER_OPERATE );

		// 업적 체크 추가
		EntityNpc* targetNpc = ownPlayer->GetSectorAction().GetNpcInMySector(req->entityId);
		if (targetNpc &&
			targetNpc->IsValid() &&
			SCRIPTS.GetAchievementConditionLoader()->IsTalkEventNpc(targetNpc->GetNpcIndex()))
		{
			AchievementNpcTalkIndexEvent achievementNpcTalkIndexEvent(ownPlayer, targetNpc->GetNpcIndex());
			ownPlayer->GetAction< ActionPlayerAchievement >()->OnEvent(&achievementNpcTalkIndexEvent);
		}
	}	

	return;
}

void StatePlayerBase::onReqSealItem( EventPtr& e )
{
	EReqSealItem* req = static_cast< EReqSealItem* >(e.RawPtr());

	EntityPlayer* ownPlayer = GetOwnerPlayer();
	VERIFY_RETURN(ownPlayer && ownPlayer->IsValid(), );

	std::wstring systemMessage;	
	ErrorItem::Error eError = ownPlayer->GetInventoryAction().Seal( req->toBeSealItem, req->byItem, systemMessage );
	if(eError != ErrorItem::SUCCESS)
	{
		switch (eError)
		{
		case ErrorItem::E_REFER_SYSTEM_MSG:
			SystemMessage sysMsg(L"sys", systemMessage);
			SendSystemMsg( ownPlayer, sysMsg );
			break;
		}
		EResSealItem *res = NEW EResSealItem;
		res->result = eError;
		SERVER.SendToClient( ownPlayer, EventPtr( res ) );

		return;
	}
}

void StatePlayerBase::onReqSaveEnvironmentSetting( EventPtr& e )
{
	EReqSaveEnvironmentSetting* req = static_cast<EReqSaveEnvironmentSetting*>(e.RawPtr());

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

#ifdef __Patch_EquipItem_ShowType_Change_HwaYeong_20181123
	Bool isShowEquipChanged = false;
#else
	Bool ntfShowingOption = false;
#endif

	EReqDbSaveEnvironmentSetting* reqdb = NEW EReqDbSaveEnvironmentSetting;
	reqdb->charId = player->GetCharId();
	reqdb->settingType = req->settingType;
	if ( SetSettingType::OPTION == ( req->settingType & SetSettingType::OPTION ) )
	{
		
#ifdef __Patch_EquipItem_ShowType_Change_HwaYeong_20181123
		if ((player->GetOptionSetting( OptionType::B_SHOW_PLAYER_HELMET ) != req->option[OptionType::B_SHOW_PLAYER_HELMET]) ||
			(player->GetOptionSetting( OptionType::B_SHOW_PLAYER_COSTUME ) != req->option[OptionType::B_SHOW_PLAYER_COSTUME]) ||
			(player->GetOptionSetting( OptionType::B_SHOW_PLAYER_COSTUME_WING ) != req->option[OptionType::B_SHOW_PLAYER_COSTUME_WING]) ||
			(player->GetOptionSetting( OptionType::B_SHOW_PLAYER_WING ) != req->option[OptionType::B_SHOW_PLAYER_WING]))
			isShowEquipChanged = true;
#else
		if ((player->GetOptionSetting( OptionType::B_SHOW_PLAYER_HELMET ) != req->option[OptionType::B_SHOW_PLAYER_HELMET]) ||
			(player->GetOptionSetting( OptionType::B_SHOW_PLAYER_COSTUME ) != req->option[OptionType::B_SHOW_PLAYER_COSTUME]))
			ntfShowingOption = true;
#endif

		player->SetOptionsetting(req->option, sizeof(req->option));
		memcpy_s(reqdb->option, sizeof(reqdb->option), req->option, sizeof(req->option));
	}
	else
	{
		ResetHelper::Reset(reqdb->option);
	}

	if ( SetSettingType::ENVIRONMENT == ( req->settingType & SetSettingType::ENVIRONMENT ) )
	{
		memcpy_s(reqdb->environment, Limits::MAX_ENVIRONMENT_SETTING, req->environment, sizeof(req->environment));
	}
	else
	{
		ResetHelper::Reset(reqdb->environment);
	}
	SERVER.SendToDb( player, EventPtr( reqdb ) );

#ifdef __Patch_EquipItem_ShowType_Change_HwaYeong_20181123
	if (isShowEquipChanged)
	{
		InvenEquip& equipInven = player->GetEquipInven();

		const ItemConstPtr costume = equipInven.GetItemBySlot( SlotType::COSTUME );
		const ItemConstPtr costumeWing = equipInven.GetItemBySlot( SlotType::COSTUME_WING );
		const ItemConstPtr wing = equipInven.GetItemBySlot( SlotType::WING );

		ENtfEquipmentShowingOption* ntf = NEW ENtfEquipmentShowingOption;
		EventPtr ntfPtr( ntf );
		{
			ntf->entityId = player->GetId();
			ntf->showHelmet = req->option[OptionType::B_SHOW_PLAYER_HELMET];
			ntf->showCostume = req->option[OptionType::B_SHOW_PLAYER_COSTUME];
			ntf->showCostumeWing = req->option[OptionType::B_SHOW_PLAYER_COSTUME_WING];
			ntf->showWing = req->option[OptionType::B_SHOW_PLAYER_WING];

			if (costume)
				costume->FillUpItemData( ntf->costumeData );
			if (costumeWing)
				costumeWing->FillUpItemData( ntf->costumeWingData );
			if (wing)
				wing->FillUpItemData( ntf->wingData );

			player->GetSectorAction().NearGridCast( ntfPtr );
		}
	}
#else
	if ( ntfShowingOption )
	{
		InvenEquip& equipInven = player->GetEquipInven();

		ItemPtr costume = equipInven.GetItemBySlot(SlotType::COSTUME );
		ItemPtr costumeWing = equipInven.GetItemBySlot(SlotType::COSTUME_WING );
		

		ENtfEquipmentShowingOption* ntf = NEW ENtfEquipmentShowingOption;
		EventPtr ntfPtr(ntf);
		{
			ntf->entityId = player->GetId();
			ntf->showHelmet = req->option[OptionType::B_SHOW_PLAYER_HELMET];
			ntf->showCostume = req->option[OptionType::B_SHOW_PLAYER_COSTUME];
			
			if (costume)
				costume->FillUpItemData(ntf->costumeData);
			if (costumeWing)
			{
				costumeWing->FillUpItemData(ntf->costumeWingData);
				ntf->showCostumeWing = costumeWing->period.IsExpiredItem();
			}
			else
			{
				ntf->showCostumeWing = false;
			}

			player->GetSectorAction().NearGridCast(ntfPtr);
		}		
	}
#endif
}

void StatePlayerBase::onResDbSaveEnvironmentSetting( EventPtr& e )
{
	EResDbSaveEnvironmentSetting* req = static_cast<EResDbSaveEnvironmentSetting*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	if ( req->eError != ErrorDB::SUCCESS )
	{
		MU2_WARN_LOG(core::LogCategory::DEFAULT, L"StatePlayerBase::onResDbSaveEnvironmentSetting > Response DB Error > [Response Info:%s]", req->ToString().c_str() );
	}

	EResSaveEnvironmentSetting* res = NEW EResSaveEnvironmentSetting;
	res->result = req->eError;
	SERVER.SendToClient( owner, EventPtr( res ) );
}

void StatePlayerBase::onReqItemRepair( EventPtr& e )
{
	EntityPlayer* ownPlayer = GetOwnerPlayer();
	VERIFY_RETURN(ownPlayer && ownPlayer->IsValid(), );

	EReqItemRepair* req = static_cast<EReqItemRepair*>(e.RawPtr());
	
	ActionPlayerDurability& adurability = ownPlayer->GetDurabilityAction();

	if (false == adurability.CheckCooltime())
	{
		SendSystemMsg(ownPlayer, SystemMessage(L"sys", L"Msg_Repair_Cool"));
		return;
	}

	adurability.Repair(req->pos, 0, true, req->npcIndex);
		
}

void StatePlayerBase::onReqItemRepairAll( EventPtr&  e )
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	EReqItemRepairAll* req = static_cast< EReqItemRepairAll* >( e.RawPtr() );
	
	if ( false == owner->GetDurabilityAction().CheckCooltime() )
	{
		SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Repair_Cool" ) );
		return;
	}

	// 0 : 모두 수리,  true : 비용지불
	ErrorItem::Error eError = owner->GetDurabilityAction().RepairEquipItem( 0, true, req->npcIndex );
	if (eError == ErrorItem::SUCCESS)
	{
		MU2_WARN_LOG(LogCategory::ITEM, "player(%s), error(%u)", owner->ToString().c_str(), eError);
	}	
}

void StatePlayerBase::onReqMissionmapCandidateC( EventPtr& e )
{
	//	다른 월드의 유저에게 보낼 수 있는지 모르겠지만..

	EReqMissionmapCandidateC* req = static_cast< EReqMissionmapCandidateC* >( e.RawPtr() );

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	EzwReqMissionmapCandidate* request = NEW EzwReqMissionmapCandidate;
	EventPtr requestPtr( request );
	request->matchType = req->matchType;

	ActionPlayerParty* actionPlayerParty = GetEntityAction(player);
	if (actionPlayerParty)
	{
		if (request->matchType == DungeonType::PVP_CHAOS_CASTLE)
		{
			if (actionPlayerParty->IsParty() == true)
			{
				EResMissionmapCandidateC* res = NEW EResMissionmapCandidateC;
				res->matchType = req->matchType;
				res->result = ErrorMissionmap::E_FAILED_CANDIDATE;
				SERVER.Send(e->GetIdxAsSessionKey(), EventPtr(res));

				SendSystemMsg(player, SystemMessage(L"sys", L"Msg_ChaosCastle_PartyStateTryMatching_ErrMsg"));
				return;
			}
		}
	}
	if(player->GetTutorialState() == TutorialState::PLAYING )
	{
		EResMissionmapCandidateC* res = NEW EResMissionmapCandidateC;
		res->matchType = req->matchType;
		res->result = ErrorMissionmap::E_FAILED_CANDIDATE;
		SERVER.Send( e->GetIdxAsSessionKey(), EventPtr( res ) );

		SendSystemMsg( player, SystemMessage( L"sys", L"Msg_Missionmap_Tutorial_Err" ) );
		
		MU2_WARN_LOG(LogCategory::CONTENTS, "tutorial : %d", player->GetTutorialState() );

		return;
	}

	const MissionMapMatchingScript* matchingScript = SCRIPTS.GetScript<MissionMapMatchingScript>();
	if ( matchingScript == nullptr )
	{
		EResMissionmapCandidateC* res = NEW EResMissionmapCandidateC;
		res->matchType = req->matchType;
		res->result = ErrorMissionmap::E_SYS_NOT_FOUND_SCRIPT_DATA;
		SERVER.Send( e->GetIdxAsSessionKey(), EventPtr( res ) );
		
		SendSystemMsg( player, SystemMessage( L"sys", L"Msg_Missionmap_Candidate_Err" ) );
		return;
	}

	const MatchinfoMap& matchingDatas = matchingScript->Get();
	auto itr = matchingDatas.find( req->matchType );
	if ( itr == matchingDatas.end() )
	{
		EResMissionmapCandidateC* res = NEW EResMissionmapCandidateC;
		res->matchType = req->matchType;
		res->result = ErrorMissionmap::E_SYS_NOT_FOUND_SCRIPT_DATA;
		SERVER.Send( e->GetIdxAsSessionKey(), EventPtr( res ) );

		SendSystemMsg( player, SystemMessage( L"sys", L"Msg_Missionmap_Candidate_Err" ) );
		return;
	}

	auto actBuff = player->GetAction<ActionBuff>();
	if ( nullptr == actBuff )
	{
		return;
	}

	auto found = actBuff->FindMissionMapLeavePenaltyBuff( itr->second.leaveBuffIndex );
	if ( true == found.first )
	{
		BuffInfoPtr buffInfo = found.second;

		EResMissionmapCandidateC* res = NEW EResMissionmapCandidateC;
		res->matchType = req->matchType;
		res->result = ErrorMissionmap::E_HAS_LEAVE_BUFF;
		SERVER.Send( e->GetIdxAsSessionKey(), EventPtr( res ) );

		SystemMessage sysMsg( L"sys", L"Msg_Missionmap_Cooltime_Penalty" );
#ifdef __Hotfix_Renew_FixedEndTimeBuff_by_jjangmo_181219
		sysMsg.SetParam( L"str", (Int32)( ( buffInfo->buffDurationPtr->GetRemainTick( GetTickCount() ) ) / 1000 ) ); // tick -> sec
#else
		sysMsg.SetParam( L"str", (Int32)(( buffInfo->endTick - GetTickCount() ) / 1000 )); // tick -> sec
#endif
		SendSystemMsg( player, sysMsg );
		return;
	}
	
	//	요청한 패킷을 월드서버에 보낸다.
	ActionPlayerEloPoint* actEloPoint = GetEntityAction( player );	
	request->eloPoint = actEloPoint->GetPlayPoint( req->matchType );
	request->isParty = false;
	request->isIntrusion = req->isIntrusion;
	
	AttributePlayer * attrPlayer = GetEntityAttribute(player);
	request->combatPower = attrPlayer->combatPower;


	ActionPlayerMissionMapTier* actionTier = GetEntityAction(player);
	ActionPlayerChaosCastleHistory* actionChaosCastle = GetEntityAction(player);

	if (req->matchType == DungeonType::DT_PVP_COLOSSEUM_33)
	{
		const TierInfo* tierInfo = actionTier->GetTierInfo(TierType::COLOSSEUM33_PVP);
		if (tierInfo == nullptr)
		{
			MU2_ASSERT(0);

			SendSystemMsg(player, SystemMessage(L"sys", L"Msg_Missionmap_Candidate_Err"));

			MU2_ERROR_LOG(LogCategory::CONTENTS, "[E][onReqMissionmapCandidateC] cant'find TierInfo. matchType(%d)", req->matchType);
			return;
		}
		request->charLevel	= player->GetLevel();
		request->eTierType	= tierInfo->tierType;
		request->tier		= tierInfo->tier;
		request->tierPoint	= tierInfo->point;
		request->winCnt		= tierInfo->winCount;
		request->loseCnt	= tierInfo->loseCount;
	}
	else if (req->matchType == DungeonType::PVP_CHAOS_CASTLE)
	{
		request->charLevel = player->GetLevel();
		request->chaosCastleplayCount		= actionChaosCastle->GetPlayCount();
		request->chaosCastlevictoryCount	= actionChaosCastle->GetVictoryCount();
		request->chaosCastlekillCount		= actionChaosCastle->GetKillCount();
	}
	
	SERVER.SendToClient( player, requestPtr );

}

void StatePlayerBase::onReqMissionmapConfirmC( EventPtr& e )
{
	EReqMissionmapConfirmC* req = static_cast<EReqMissionmapConfirmC*>( e.RawPtr() );

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	EzwReqMissionmapConfirm* request = NEW EzwReqMissionmapConfirm;
	EventPtr reqPtr(request);
	{
		request->charLevel = player->GetLevel();
		request->confirm = req->confirm;
		request->matchType = req->matchType;
		SERVER.SendToWorldServer(player, reqPtr );
	}
	
}

void StatePlayerBase::onResDbSetCharEloPoint( EventPtr& e)
{
	EResDbSetCharEloPoint* req = static_cast<EResDbSetCharEloPoint*>( e.RawPtr() );
	MU2_ASSERT( req->eError == ErrorDB::SUCCESS );
	if ( req->eError != ErrorDB::SUCCESS )
	{
		MU2_WARN_LOG(core::LogCategory::DEFAULT, L"StatePlayerBase::onResDbSetCharEloPoint > Response DB Error > [Response Info:%s]", req->ToString().c_str() );
	}
}

void StatePlayerBase::onNtfMissionmapCandidateToZone(EventPtr& e)
{
	ENtfMissionmapCandidateToZone* req = static_cast<ENtfMissionmapCandidateToZone*>(e.RawPtr());

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player, );

	ActionPlayerMissionMapTier* actionMissionMap = GetEntityAction(player);
	actionMissionMap->SetCandidate(req->matchType, req->isCandidating);
}

void StatePlayerBase::onMissionMapMemberScoreInfos( EventPtr& e )
{
	EReqGameMMMemberScoreInfos* req = static_cast< EReqGameMMMemberScoreInfos* >(e.RawPtr());
	UNREFERENCED_PARAMETER(req);
	
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player, );
	
	Sector* sector = player->GetSector();
	VERIFY_RETURN( sector, );	
	
	sector->GetSectorController().GetControllerAction().OnEvent( DungeonEventType::MISSION_MAP_RESULT, player, nullptr );
}

void StatePlayerBase::onReqMissionRewardCardSelect( EventPtr& e )
{
	EReqMissionRewardCardSelect* req = static_cast< EReqMissionRewardCardSelect* >( e.RawPtr() );
	UNREFERENCED_PARAMETER(req);

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player, );

	Sector* sector = player->GetSector();
	VERIFY_RETURN(sector, );

	sector->GetSectorController().GetMissionDungeonAction().SelectCardReward(player);
}

void StatePlayerBase::onResFriendInsert( EventPtr& e )
{
	EResCommFriendInsert* ntf= static_cast< EResCommFriendInsert* >( e.RawPtr() );
	EntityPlayer* player = GetOwnerPlayer();

	AchievementRegisterFriendEvent achievementEvent(player, ntf->count );
	player->GetAction< ActionPlayerAchievement >()->OnEvent( &achievementEvent );

	EResCommFriendInsert* res = NEW EResCommFriendInsert;
	res->result		= ntf->result;
	res->friendData = ntf->friendData;	

	SERVER.SendToClient(player, EventPtr( res ) );
}

void StatePlayerBase::onResFriendRemove( EventPtr& e )
{
	EResCommFriendRemove* ntf= static_cast< EResCommFriendRemove* >( e.RawPtr() );
	EntityPlayer* player = GetOwnerPlayer();

	EResCommFriendRemove* res = NEW EResCommFriendRemove;
	res->result = ntf->result;
	res->charId = ntf->charId;	
	res->entityId = ntf->entityId;

	SERVER.SendToClient(player, EventPtr( res ) );
}

void StatePlayerBase::onReqSubscribeTest( EventPtr& e )
{
	EReqSubsribeTestAi* req = static_cast<EReqSubsribeTestAi*>( e.RawPtr() );
	req;

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );
	ActionSector* sectorAction = static_cast< ActionSector* >(player->GetAction(ACTION_SECTOR));
	VERIFY_RETURN(sectorAction, );

	sectorAction->GetSector()->SubsribeTestAi( player->GetId() );
}

void StatePlayerBase::onReqUnsubscribeTest( EventPtr& e )
{
	EReqUnsubsribeTestAi* req = static_cast<EReqUnsubsribeTestAi*>( e.RawPtr() );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ActionSector* sectorAction = static_cast< ActionSector* >(owner->GetAction(ACTION_SECTOR));
	VERIFY_RETURN(sectorAction, );

	sectorAction->GetSector()->UnsubscribeTestAi( req->selfId );
}

void StatePlayerBase::onReqGamePlayerCharInfo( EventPtr& e )
{
	EwzReqGamePlayerCharInfo* req = static_cast<EwzReqGamePlayerCharInfo*>( e.RawPtr() );
	
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	auto act = owner->GetAction<ActionPlayerCognition>();	

	EzwResGamePlayerCharInfo* res = NEW EzwResGamePlayerCharInfo;
	res->result = ErrorPlayer::SUCCESS;
	res->requester  = req->requester;
	res->charEntityId = owner->GetId();
	act->GetCharInfo( res->info );
	SERVER.SendToWorldServer( owner, EventPtr( res ) );
}

void StatePlayerBase::onReqChaosCastleRingout( EventPtr& e )
{
	EReqGameChaosCastleRingout* req = static_cast<EReqGameChaosCastleRingout*>( e.RawPtr() );
	req; 

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player, );

	Sector* sector = player->GetSector();
	VERIFY_RETURN(sector, );
	
	sector->GetSectorController().GetControllerAction().OnEvent( DungeonEventType::CLIENT_RING_OUT, player, nullptr );	
}

void StatePlayerBase::onReqGameNpcPortal( EventPtr& e )
{
	EReqGameNpcPortal* req = static_cast<EReqGameNpcPortal*>( e.RawPtr() );
	UNREFERENCED_PARAMETER(req);
	VERIFY_RETURN(!"EReqGameNpcPortal", );

	//EntityPlayer* owner = GetOwnerPlayer();
	//ActionSector* actSector = GetEntityAction( owner );
	//Sector* sector = actSector->GetSector();

	//Entity4Zone* npc = sector->FindEntity( EntityId( req->npcEntityId ) );
	//if ( npc == nullptr )
	//{
	//	return;
	//}

	//PortalUseItemList itemList;

	//Int32 ret = thePortalSystem.RequestNpc( owner, npc , itemList );
	//if ( ret != 0 )
	//{
	//	theGameMsg.SendGameDebugMsg( owner, L"onReqGameNpcPortal: %I64d npc portal 이동 실패 / error:%d\n", req->npcEntityId, ret );

	//	EResGameNpcPortal* res = NEW EResGameNpcPortal;
	//	res->result = ret;
	//	SERVER.Send(req->GetIdxAsSessionKey(), EventPtr( res ) );
	//	return;
	//}

	//// zoneServer에서 owner->GetId().Seq는 charId와 동일한다.

	//// invasion 절차를 시작해야한다.
	//EReqGameInvasionExcutionJump* request = NEW EReqGameInvasionExcutionJump;
	//request->targetWorldId = sector->GetInvasionInfo().worldId;
	//request->targetExecZoneId = sector->GetInvasionInfo().execZoneId;
	//request->targetPortalIndex = sector->GetInvasionInfo().portalIndex;
	//AttributeEntity* entityAttr = GetEntityAttribute( owner );

	//request->isGo = ( SERVER.GetWorldId() == GetWorldIdFromCharId( owner->GetId().Seq ));
	//EventSetter::SetInteralIdxByWorldSessionKey(request, entityAttr->sessionInWorldServer);
	////request->internal_index = entityAttr->worldsession;

	//SERVER.SendToClient( owner, EventPtr( request ) );
}

void StatePlayerBase::onReqGameSaveCoachMark( EventPtr& e )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	EReqGameSaveCoachMark* req = static_cast<EReqGameSaveCoachMark*>( e.RawPtr() );
	EReqDbSaveCoachMarks * reqdb = NEW EReqDbSaveCoachMarks;
		
	reqdb->charId = player->GetCharId();
	reqdb->coachId = req->coachId;
	reqdb->coachValue = req->coachValue;

	SERVER.SendToDb( player, EventPtr( reqdb ) );
}

void StatePlayerBase::onResDbSaveCoachMark( EventPtr& e )
{
	EntityPlayer* owner = GetOwnerPlayer();

	EResDbSaveCoachMarks* resdb = static_cast<EResDbSaveCoachMarks*>( e.RawPtr() );
	if ( resdb->eError != ErrorDB::SUCCESS )
	{
		AttributePlayer* attrPlayer = GetEntityAttribute(owner );
		MU2_WARN_LOG(core::LogCategory::DEFAULT, L"StatePlayerBast::onResDbSaveCoachMark > Response DB Error > [CharId:%d] [Response Info:%s]",
			attrPlayer->charId, resdb->ToString().c_str() );

		return;
	}

	EResGameSaveCoachMark* res = NEW EResGameSaveCoachMark;

	res->coachId = resdb->coachId;
	res->coachValue = resdb->coachValue;
	res->result = resdb->eError;

	SERVER.SendToClient(owner, EventPtr( res ) );
}

void StatePlayerBase::onResDbItemTransaction(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	EResDbItemTransaction* res = static_cast<EResDbItemTransaction*>(e.RawPtr());
	VERIFY_RETURN(res, );

	if (ErrorDB::SUCCESS != res->eError)
	{
		CharId charId = 0;

		AttributePlayer *player = owner->GetAttribute<AttributePlayer>();
		if (player)
			charId = player->charId;

		MU2_WARN_LOG(LogCategory::CONTENTS, L"StatePlayerBase::onResDbItemTransaction > requst failed. event = %s] [CharId:%d][ErrorCode:%d]", e->ToString().c_str(), charId, res->eError);
	}

	auto act = owner->GetAction<ActionPlayerInventory>();
	act->ProcessTranaction(e, res->eError, res->fcsResult);
}

void StatePlayerBase::onReqDungeonGuideMove(EventPtr& e)
{
	EReqDungeonGuideMove* req = static_cast< EReqDungeonGuideMove* >( e.RawPtr() );
	
	EntityPlayer* ownerPlayer = GetOwnerPlayer();
	VERIFY_RETURN( ownerPlayer && ownerPlayer->IsValid(), );

	const DungeonGuideElem* elem = SCRIPTS.GetDungeonGuideElem( req->type, req->index );
	if( nullptr == elem || !elem->IsValidPositionIndex() )
	{
		SendSystemMsg( ownerPlayer, SystemMessage( L"sys", L"Msg_Error_Enter_Dungeon_Guide" ) );
		return;
	}
		
	const PositionElem* positionElem = SCRIPTS.GetPositionElem( elem->positionIndex );
	if( nullptr == positionElem )
	{
		SendSystemMsg( ownerPlayer, SystemMessage( L"sys", L"Msg_Error_Enter_Dungeon_Guide" ) );
		return;
	}

	const WorldElem* worldElem = SCRIPTS.GetWorldScript( positionElem->GetDestZoneIndex());
	if ( nullptr == worldElem )
	{ 
		SendSystemMsg( ownerPlayer, SystemMessage( L"sys", L"Msg_Error_Enter_Dungeon_Guide" ) );
		return;
	}

	// 제약조건은 체크
	// 1 : 인던은 불가능
	VERIFY_RETURN( worldElem->IsChannel(), );

	// 2 : 레벨제한
	VERIFY_RETURN(ownerPlayer->GetLevel() >= elem->levelLimit, );

	EzwReqChangeMap* reqToWorld = NEW EzwReqChangeMap;	
	reqToWorld->toIndexPositionMovable = elem->positionIndex;		
	SERVER.SendChangemapToWorld( ownerPlayer, EzwReqChangeMap::onReqDungeonGuideMove,EventPtr( reqToWorld ) );
}

//
//void StatePlayerBase::onNtfGuildJoinToZone( EventPtr& e )
//{
//	ENtfGuildJoinToZone* ntf = static_cast<ENtfGuildJoinToZone*>( e.RawPtr() );
//	// 캐릭터 정보에 세팅, DB 저장, 다른 유저에게 알림
//	EntityPlayer* owner = GetOwnerPlayer();
//
////	AttributePlayer* attrPlayer = GetEntityAttribute( owner );
////	attrPlayer->guildId = ntf->guildId;
//
//	EReqDbSaveCharInfo* req = NEW EReqDbSaveCharInfo;
//	ViewPlayer* viewPlayer = GetEntityView( owner );
//	viewPlayer->FillUpEvent( req );
//	SERVER.SendToDb( owner, EventPtr( req ) );
//
//	ActionSector* actSector = GetEntityAction( owner );
//	ENtfGuildChangedOfPlayer* cast = NEW ENtfGuildChangedOfPlayer;
//	cast->entityId = owner->GetId();
//	cast->guildId = ntf->guildId;
//	actSector->NearGridCast( EventPtr( cast ) );
//
//	if ( ntf->setupBuffIndex > 0 )
//	{
//		theLogicEffectSystem.OnSingleCast( owner, ntf->setupBuffIndex );
//	}
//}
//
//void StatePlayerBase::onNtfGuildLeaveToZone( EventPtr& e )
//{
//	ENtfGuildLeaveToZone* ntf = static_cast<ENtfGuildLeaveToZone*>( e.RawPtr() );
//	// 캐릭터 정보에 세팅, DB 저장, 다른 유저에게 알림
//	EntityPlayer* owner = GetOwnerPlayer();
//
////	AttributePlayer* attrPlayer = GetEntityAttribute( owner );
//
////	attrPlayer->guildId = 0;
//
//	EReqDbSaveCharInfo* req = NEW EReqDbSaveCharInfo;
//	ViewPlayer* viewPlayer = GetEntityView( owner );
//	viewPlayer->FillUpEvent( req );
//	SERVER.SendToDb( owner, EventPtr( req ) );
//
//	if ( ntf->resetBuffIndex > 0 )
//	{
//		theLogicEffectSystem.OnCancelBuffCast( owner, ntf->resetBuffIndex );
//	}
//
//	ActionSector* actSector = GetEntityAction( owner );
//	ENtfGuildChangedOfPlayer* cast = NEW ENtfGuildChangedOfPlayer;
//	cast->entityId = owner->GetId();
//	cast->guildId = 0;
//	actSector->NearGridCast( EventPtr( cast ) );
//}

void StatePlayerBase::onNtfChangeGuildBuff( EventPtr&e )
{
//	ENtfChangeGuildBuff* ntf = static_cast<ENtfChangeGuildBuff*>( e.RawPtr() );
	
	//EntityPlayer* owner = GetOwnerPlayer();

	//AttributePlayer* attrPlayer = GetEntityAttribute( owner );
	//if ( ntf->guildId != attrPlayer->guildId )
	//{
	//	return;
	//}

	//if ( ntf->resetBuff > 0 )
	//{
	//	theLogicEffectSystem.OnCancelBuffCast( owner, ntf->resetBuff );
	//}
	//if ( ntf->setupBuff > 0 )
	//{
	//	theLogicEffectSystem.OnSingleCast( owner, ntf->setupBuff );
	//}	
}


void StatePlayerBase::onNtfDiedNpcOfGuild( EventPtr& e )
{
	//ENtfDiedNpcOfGuild* ntf = static_cast<ENtfDiedNpcOfGuild*>( e.RawPtr() );

	//EntityPlayer* owner = GetOwnerPlayer();

	//AttributePlayer* attrPlayer = GetEntityAttribute( owner );
//	if ( ntf->guildId != attrPlayer->guildId )
	//{
	//	return;
	//}

	//const NpcInfoElem* elemNpc = SCRIPTS.GetNpcInfoScript( ntf->npcId );
	//const WorldElem* elemWorld = SCRIPTS.GetWorldScript( ntf->indexZone );
	//if ( elemNpc == nullptr || elemWorld == nullptr )
	//{
	//	return;
	//}

	//if ( ntf->msgType == 1 )
	//{
	//	SystemMessage sysMsg( L"sys", L"Msg_Knights_NpcDead" );
	//	sysMsg.SetParam( L"str", elemWorld->zoneName );
	//	sysMsg.SetParam( L"str", elemNpc->nameKr );
	//	SendSystemMsg( owner, sysMsg );
	//}
	//else if ( ntf->msgType == 2 )
	//{
	//	SystemMessage sysMsg( L"sys", L"Msg_Knights_NpcDestroy" );
	//	sysMsg.SetParam( L"str", elemWorld->zoneName );
	//	sysMsg.SetParam( L"str", elemNpc->nameKr );
	//	SendSystemMsg( owner, sysMsg );
	//}

}

void StatePlayerBase::onReqGameShineSkillCast( EventPtr& e )
{
	EReqGameShineSkillCast* req = static_cast< EReqGameShineSkillCast* >( e.RawPtr() );

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );
	VALID_RETURN(!player->IsDead(), );


	EResGameShineSkillCast* res = NEW EResGameShineSkillCast;
	EventPtr resEventPtr( res );
	res->shineEntityId = req->shineEntityId;

	// req->shineEntityId 검증1 - owner 주위 섹터에 존재하는지 확인
	EntityNpc* shineNpc = player->GetSectorAction().GetNpcInMyNearGrid( req->shineEntityId );
	if ( nullptr == shineNpc || shineNpc->IsDead() )
	{
		res->rootingResult = false;
		SERVER.SendToClient( player, resEventPtr );
		return;
	}
	
	Vector3 shinePosition = shineNpc->GetCurrPos();
	Vector3 playerCurPos = player->GetRealPosition();

	// req->shineEntityId 검증2 - 플레이어와 거리 체크
	auto diffDist = Distance(playerCurPos, shinePosition);
	UInt32 autoRootRange = player->GetAbilityAction().GetAbilityValue(EAT::ITEM_AUTO_ROOTING_RANGE );
	if ( diffDist > autoRootRange + 100 )
	{
		res->rootingResult = false;
		SERVER.SendToClient( player, resEventPtr );
		return;
	}
	else
	{
		res->rootingResult = true;
		SERVER.SendToClient( player, resEventPtr );
	}
	
	
	auto shineSkillElem = SCRIPTS.GetShineSkillElem(shineNpc->GetNpcIndex());
	if (nullptr == shineSkillElem)
	{
		return;
	}

	TargetPosition targetPos;
	targetPos.position = player->GetCurrPos();
	
	VectorUnit vecUnit;
	for ( const auto& skillId : shineSkillElem->skillIndices )
	{
		if ( 0 == skillId )
		{
			continue;
		}
		
		auto infoElem = SCRIPTS.GetSkillInfoScript( skillId );
		if ( nullptr == infoElem )
		{
			continue;
		}

		auto effectElem = SCRIPTS.GetSkillEffectScript( infoElem->effectIndex[ 0 ] );
		if ( nullptr == effectElem )
		{
			continue;
		}

		vecUnit.clear();
		theEntityCollector.ExtractTargets( player, targetPos, effectElem, vecUnit );

		for ( const auto& target : vecUnit )
		{
			theLogicEffectSystem.OnSingleCast( player, target, skillId );
		}
	}	
		
	ENtfGameShineRooted* ntf = NEW ENtfGameShineRooted;
	ntf->shineEntityId = shineNpc->GetId();
	ntf->rootedPlayerEntityId = player->GetId();
	player->GetSectorAction().NearGridCast( EventPtr( ntf ) );

	shineNpc->GetHsm().Tran(EntityState::STATE_NPC_DEAD );

	Sector* sector = player->GetSector();
	if (sector  && sector->GetDungeonType() == DungeonType::PVP_CHAOS_CASTLE)
	{
		
		EReqLogDb2* log = NEW EReqLogDb2;
		EventPtr logEventPtr(log);
		EventSetter::FillLogForEntity(LogCode::E_CHAOSCASTLE_GET_SHINE, player, log->action);
		log->action.var32s.emplace_back(sector->GetExecId().GetUnique());		
		log->action.var32s.emplace_back(shineNpc->GetId().GetSeq());
		SendDataCenter(logEventPtr);
	}
}

void StatePlayerBase::onReqEndlessSelectStage( EventPtr& e )
{
	EntityPlayer* ownPlayer = GetOwnerPlayer();
	VERIFY_RETURN(ownPlayer && ownPlayer->IsValid(), );
		
	Sector* sector = ownPlayer->GetSector();
	VERIFY_RETURN(sector, );

	sector->GetSectorController().GetControllerAction().OnEndlessSelectStage( ownPlayer );
}

#ifdef __Patch_Tower_of_dawn_eunseok_2018_11_05
void StatePlayerBase::onReqTowerOfDawnUsePlayChance(EventPtr& e)
{
	EReqTowerOfDawnUsePlayChance* req = static_cast< EReqTowerOfDawnUsePlayChance* >(e.RawPtr());

	EntityPlayer* ownPlayer = GetOwnerPlayer();
	VERIFY_RETURN(ownPlayer && ownPlayer->IsValid(), );

	Sector* sector = ownPlayer->GetSector();
	VERIFY_RETURN(sector, );
	
	sector->GetSectorController().GetControllerAction().OnTowerOfDawnUsePlayChance(ownPlayer, req->chanceIndex);
}

void StatePlayerBase::onResDBGetTowerOfDawnRewardPoint(EventPtr& e)
{
	EResDbGetTowerOfDawnRewardPoint* res = static_cast<EResDbGetTowerOfDawnRewardPoint*>(e.RawPtr());

	EntityPlayer* ownPlayer = GetOwnerPlayer();
	VERIFY_RETURN(ownPlayer && ownPlayer->IsValid(), );

	ownPlayer->GetPlayerAction().SetTowerOfDawnRewardPoint(res->point);
	
}
#endif //__Patch_Tower_of_dawn_eunseok_2018_11_05
void StatePlayerBase::onReqPVEMissionmapReward( EventPtr& e )
{
	EntityPlayer* ownPlayer = GetOwnerPlayer();
	VERIFY_RETURN(ownPlayer && ownPlayer->IsValid(), );

	EReqPVEMissionMapReward* req = static_cast< EReqPVEMissionMapReward* >( e.RawPtr() );

	Sector* sector = ownPlayer->GetSector();
	VERIFY_RETURN(sector, );
	
	sector->GetSectorController().GetControllerAction().OnEvent( DungeonEventType::REWARD_RECEIVE, ownPlayer, nullptr, req->receiveState );	
}

void StatePlayerBase::onReqPetUpdateSpawnState( EventPtr& e )
{
	EntityPlayer* ownPlayer = GetOwnerPlayer();
	VERIFY_RETURN(ownPlayer && ownPlayer->IsValid(), );

	EReqPetUpdateSpawnState* req = static_cast< EReqPetUpdateSpawnState* >( e.RawPtr() );

	
	if (ownPlayer->GetSectorAction().IsDisabledPetSpawn() == true)
	{
		EResPetUpdateSpawnState* res = NEW EResPetUpdateSpawnState;
		res->eError = ErrorPet::E_FAILED;
		SERVER.SendToClient(ownPlayer, EventPtr(res));
		return;
	}
		
	ErrorPet::Error eError = ownPlayer->GetPetManageAction().UpdateSpawnState(req->spawnState);
	if ( eError != ErrorPet::SUCCESS  )
	{
		EResPetUpdateSpawnState* res = NEW EResPetUpdateSpawnState;
		res->eError = eError;
		SERVER.SendToClient( ownPlayer, EventPtr( res ) );
	}
}

void StatePlayerBase::onReqPetRegister( EventPtr& e )
{
	EntityPlayer* owner = GetOwnerPlayer();
	EReqPetRegister* req = static_cast< EReqPetRegister* >( e.RawPtr() );
		
	ErrorItem::Error eErrorItem = owner->GetUseItemAction().UsePetRegisterItem( req->item);

	if ( eErrorItem != ErrorItem::SUCCESS)
	{
		EResPetRegister* res = NEW EResPetRegister;
		res->eError = eErrorItem;
		SERVER.SendToClient( owner, EventPtr( res ) );
	}
}

void StatePlayerBase::onReqPetRename( EventPtr& e )
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	EReqPetRename* req = static_cast< EReqPetRename* >( e.RawPtr() );
		
	ErrorPet::Error eError = owner->GetPetManageAction().Rename(PetId(req->petUnique), req->name);
	if ( eError != ErrorPet::SUCCESS  )
	{
		EResPetRename* res = NEW EResPetRename;
		res->eError = eError;
		SERVER.SendToClient( owner, EventPtr( res ) );
	}
}

void StatePlayerBase::onReqPetUpgrade(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	EReqPetUpgrade* req = static_cast<EReqPetUpgrade*>(e.RawPtr());
	
	ErrorPet::Error eError = owner->GetPetManageAction().Upgrade(PetId(req->petUnique), req->materialPets);
	if (eError != ErrorPet::SUCCESS)
	{
		EResPetUpgrade* res = NEW EResPetUpgrade;
		res->eError = eError;
		SERVER.SendToClient(owner, EventPtr(res));
	}
}

void StatePlayerBase::onReqGamePetChangeFellow(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	EReqGamePetChangeFellow* req = static_cast<EReqGamePetChangeFellow*>(e.RawPtr());

	ErrorPet::Error eError = ErrorPet::SUCCESS;
	
	if (req->fellowSlotNumber == 0)
	{
		eError = owner->GetPetManageAction().DelFellow(PetId(req->petUnique));
		if (eError != ErrorPet::SUCCESS )
		{
			EzcResGamePetChangeFellow* res = NEW EzcResGamePetChangeFellow;
			res->result = eError;
			SERVER.SendToClient(owner, EventPtr(res));
		}
	}
	else
	{
		eError = owner->GetPetManageAction().AddFellow(PetId(req->petUnique), req->fellowSlotNumber);
		if (eError != ErrorPet::SUCCESS)
		{
			EzcResGamePetChangeFellow* res = NEW EzcResGamePetChangeFellow;
			res->result = eError;
			SERVER.SendToClient(owner, EventPtr(res));
		}
	}
}

void StatePlayerBase::onReqGamePetExpandFellowSlot(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );
	
	ErrorPet::Error eError = owner->GetPetManageAction().ExpandFellowSlot();
	if (eError != ErrorPet::SUCCESS)
	{
		EzcResGamePetExpandFellowSlot* res = NEW EzcResGamePetExpandFellowSlot;
		res->result = eError;
		SERVER.SendToClient(owner, EventPtr(res));
	}
}

void StatePlayerBase::onReqGamePetGrowthRegister(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	EczReqPetGrowthRegister* req = static_cast<EczReqPetGrowthRegister*>(e.RawPtr());

	ErrorPet::Error eError = ErrorPet::SUCCESS;

	auto actPet = owner->GetAction<ActionPlayerPetManage>();
	if (req->slotIndex == 0)
	{
		eError = actPet->DeleteGrowthPet(PetId(req->petID));
		if (eError != ErrorPet::SUCCESS)
		{
			EzcResPetGrowthRegister* res = NEW EzcResPetGrowthRegister;
			res->result = eError;
			SERVER.SendToClient(owner, EventPtr(res));
		}
	}
	else
	{
		eError = actPet->AddGrowthPet(PetId(req->petID), req->slotIndex);
		if (eError != ErrorPet::SUCCESS)
		{
			EzcResPetGrowthRegister* res = NEW EzcResPetGrowthRegister;
			res->result = eError;
			SERVER.SendToClient(owner, EventPtr(res));
		}
	}
}

void StatePlayerBase::onReqPetBoosterStart(EventPtr& e)
{
	EczReqPetBoosterStart* req = static_cast<EczReqPetBoosterStart*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VALID_RETURN(owner && owner->IsValid(), );

	auto actPetManager = owner->GetAction<ActionPlayerPetManage>();

	// Booster Slot 체크
	if ( req->petBoosterSlotNum < PetGrowthBoosterElem::BOOSTER_BEGIN || PetGrowthBoosterElem::BOOSTER_END < req->petBoosterSlotNum ) {
		UInt32 error = ErrorPet::E_INVALID_BOOSTER_SLOT_NUMBER;

		MU2_ERROR_LOG( LogCategory::CONTENTS
			         , "Failed to request EREQ_PET_BOOSTER_START cause invalid PetBooster Slot Number !!! : BoosterSlotNo:%d, (Begin:%d ~ End:%d) - EC:%d, CharID%d"
			         , req->petBoosterSlotNum, PetGrowthBoosterElem::BOOSTER_BEGIN, PetGrowthBoosterElem::BOOSTER_END
					 , error
					 , owner->GetCharId() );

		EzcResPetBoosterStart* resFail = NEW EzcResPetBoosterStart;
		resFail->result = error;
		SERVER.SendToClient(owner, EventPtr(resFail));
		return;
	}

	// Booster Slot 스킬 체크
	IndexSkill usingSkillID = actPetManager->m_playerPet.m_petBoosterSkillIDList[req->petBoosterSlotNum - 1];
	if (0 != usingSkillID) {
		UInt32 error = ErrorPet::E_USING_BOOSTER_SLOT;

		MU2_ERROR_LOG( LogCategory::CONTENTS
					 , "Failed to request EREQ_PET_BOOSTER_START cause using PetBooster Slot!!! : BoosterSlotNo:%d, UsingSkillID:%d - EC:%d, CharID%d"
					 , req->petBoosterSlotNum, usingSkillID
					 , error
					 , owner->GetCharId());

		EzcResPetBoosterStart* resFail = NEW EzcResPetBoosterStart;
		resFail->result = error;
		SERVER.SendToClient(owner, EventPtr(resFail));
		return;
	}

	// Booster Skill 읽기
	const PetGrowthBoosterScript* petGrowthBoosterScript = SCRIPTS.GetScript<PetGrowthBoosterScript>();
	PetGrowthBoosterElem petGrowthBoosterElem;
	if (true != petGrowthBoosterScript->RandPetGrowthBoosterElem(req->petBoosterSlotNum, OUT petGrowthBoosterElem)) {
		UInt32 error = ErrorPet::E_PET_BOOSTER_SKILL_RAND_FAILURE;

		MU2_ERROR_LOG( LogCategory::CONTENTS
					 , "Failed to request EREQ_PET_BOOSTER_START cause Skill rand failure !!! : SkillGroupID:%d - EC:%d, CharID%d"
					 , req->petBoosterSlotNum
					 , error
					 , owner->GetCharId());

		EzcResPetBoosterStart* resFail = NEW EzcResPetBoosterStart;
		resFail->result = error;
		SERVER.SendToClient(owner, EventPtr(resFail));
		return;
	}

	// Redzen 비용 읽기
	const PetXMLScript* petXMLScript = SCRIPTS.GetScript<PetXMLScript>();
	if (nullptr == petXMLScript) {
		UInt32 error = ErrorPet::E_NOT_FOUND_PET_SCRIPT;

		MU2_ERROR_LOG( LogCategory::CONTENTS
					 , "Failed to request EREQ_PET_BOOSTER_START cause not found PetXMLScript !!! - EC:%d, CharID%d"
					 , error
					 , owner->GetCharId());

		EzcResPetBoosterStart* resFail = NEW EzcResPetBoosterStart;
		resFail->result = error;
		SERVER.SendToClient(owner, EventPtr(resFail));
		return;
	}
	RedZen petBoosterUseRedzenCost = static_cast<RedZen>(petXMLScript->redzenForPetBoosterCost);

	// DB 업데이트 정보 설정
	PlayerPetData playerPet = actPetManager->m_playerPet;
	playerPet.m_petBoosterSkillIDList[req->petBoosterSlotNum - 1] = petGrowthBoosterElem.m_skillIndex;

	// 펫 부스터 사용 트렌젝트 생성
	PetTransaction* transaction = NEW PetTransaction(__FUNCTION__, __LINE__
													, owner
													, ProtocolPet::EREQ_PET_BOOSTER_START
													, playerPet
		                                            , req->petBoosterSlotNum, petBoosterUseRedzenCost
		                                            , petGrowthBoosterElem.m_petGrowthBoosterIndex, petGrowthBoosterElem.m_skillIndex
													, LogCode::E_PET_BOOSTER_USE
	);
	TransactionPtr transPtr(transaction);

	// 펫 부스터 사용 트렌젝션 등록
	if (!TransactionSystem::Register(transPtr)) {
		ErrorPet::Error error = ErrorPet::E_PET_BOOSTER_TRANS_REGISTER_FAILURE;

		MU2_ERROR_LOG( LogCategory::CONTENTS
					 , "Failed to reqest EREQ_PET_BOOSTER_START cause PetTransaction register failure !!! - EC:%d, CharID%d"
					 , error
					 , owner->GetCharId());

		EzcResPetBoosterStart* resFail = NEW EzcResPetBoosterStart;
		resFail->result = error;
		SERVER.SendToClient(owner, EventPtr(resFail));
		return;
	}
}

void StatePlayerBase::onReqPetQuestInfo(EventPtr& e)
{
	//EczReqPetQuestInfo* req = static_cast<EczReqPetQuestInfo*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VALID_RETURN(owner && owner->IsValid(), );

	auto actPetManager = owner->GetAction<ActionPlayerPetManage>();

	// 펫 모험 정보 초기화 시간 경과한 경우 초기화 & DB 갱신
	if (true != actPetManager->CheckAndUpdatePetQuestInfoReset()) {

		PetQuestData petQuest;
		actPetManager->GetPetQuestInfo(OUT petQuest);

		EzcResPetQuestInfo* resSuccess = NEW EzcResPetQuestInfo;
		resSuccess->result = ErrorPet::SUCCESS;
		resSuccess->petQuestData = petQuest;

		SERVER.SendToClient(owner, EventPtr(resSuccess));
	}
}

void StatePlayerBase::onReqPetQuestInfoChange(EventPtr& e)
{
	EczReqPetQuestInfoChange* req = static_cast<EczReqPetQuestInfoChange*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ActionPlayerPetManage& actPetManager = owner->GetPetManageAction();

	// 펫 모험 정보 초기화 시간 경과한 경우 초기화 & DB 갱신
	actPetManager.CheckAndUpdatePetQuestInfoReset();

	PetQuestElem::QuestOpenType petQuestOpenType = static_cast<PetQuestElem::QuestOpenType>(req->openType);

	// 펫 모험 정보 변경 가능 여부 체크
	if(false == actPetManager.IsChangeablePetQuestInfo(petQuestOpenType) ) 
	{
		ErrorPet::Error eError = ErrorPet::E_INVALID_PET_QUEST_OPEN_TYPE;

		MU2_ERROR_LOG( LogCategory::CONTENTS
					 , "Failed to reqest EREQ_PET_QUEST_INFO_CHANGE cause Pet QuestOpenType invalid !!! : reqOpenType:%d - EC:%d, CharID%d"
			         , petQuestOpenType
					 , eError
					 , owner->GetCharId() );

		EzcResPetQuestInfoChange* resFail = NEW EzcResPetQuestInfoChange;
		resFail->result = eError;
		SERVER.SendToClient(owner, EventPtr(resFail));
		return;
	}

	PetQuestElem petQuestElem;

	// 펫 모험 정보를 랜덤 방식으로 읽기
	const PetQuestScript* petQuestScript = SCRIPTS.GetScript<PetQuestScript>();
	if (true != petQuestScript->RandPetQuestElem(petQuestOpenType, OUT petQuestElem)) 
	{
		ErrorPet::Error eError = ErrorPet::E_PET_QUEST_INFO_LOAD_FAILURE_FROM_SCRIPT;

		MU2_ERROR_LOG( LogCategory::CONTENTS
					 , "Failed to reqest EREQ_PET_QUEST_INFO_CHANGE cause PetQuestElem load failure !!! - EC:%d, PetQuestOpenType:%d, CharID%d"
					 , eError
			         , petQuestOpenType, owner->GetCharId() );

		EzcResPetQuestInfoChange* resFail = NEW EzcResPetQuestInfoChange;
		resFail->result = eError;
		SERVER.SendToClient(owner, EventPtr(resFail));
		return;
	}

	// 펫 모험 정보 초기화 후 오픈
	if (static_cast<Byte>(PetQuestElem::QuestOpenType::First) == req->openType) 
	{		
		// 펫 모험 정보 변경 & DB 갱신
		actPetManager.UpdatePetQuestInfo(petQuestElem.m_petQuestIndex, PetQuestElem::QuestResultType::None);

		PetQuestData petQuest;
		actPetManager.GetPetQuestInfo(OUT petQuest);

		EzcResPetQuestInfoChange* resSuccess = NEW EzcResPetQuestInfoChange;
		resSuccess->result = ErrorPet::SUCCESS;
		resSuccess->petQuestData = petQuest;
		SERVER.SendToClient(owner, EventPtr(resSuccess));
		return;
	}
	// 펫 모험 성공후 무료 오픈
	else if (static_cast<Byte>(PetQuestElem::QuestOpenType::Free) == req->openType) 
	{
		PetQuestData petQuest;
		actPetManager.GetPetQuestInfo(OUT petQuest);

		if (static_cast<Byte>(PetQuestElem::QuestResultType::Failure) == petQuest.lastPetQuestState) {
			ErrorPet::Error error = ErrorPet::E_PET_QUEST_LAST_PLAY_FAILED;

			MU2_ERROR_LOG( LogCategory::CONTENTS
						 , "Failed to reqest EREQ_PET_QUEST_INFO_CHANGE cause Pet Quest Last Play failed !!! - EC:%d, CharID%d"
						 , error
						 , owner->GetCharId());

			EzcResPetQuestInfoChange* resFail = NEW EzcResPetQuestInfoChange;
			resFail->result = error;
			SERVER.SendToClient(owner, EventPtr(resFail));
			return;
		}

		// 펫 모험 정보 변경 & DB 갱신
		actPetManager.UpdatePetQuestInfo(petQuestElem.m_petQuestIndex, PetQuestElem::QuestResultType::None);

		actPetManager.GetPetQuestInfo(OUT petQuest);

		EzcResPetQuestInfoChange* resSuccess = NEW EzcResPetQuestInfoChange;
		resSuccess->result = ErrorPet::SUCCESS;
		resSuccess->petQuestData = petQuest;
		SERVER.SendToClient(owner, EventPtr(resSuccess));
		return;
	}
	// 펫 모험 마정석 사용 오픈
	else if ( static_cast<Byte>(PetQuestElem::QuestOpenType::Greenzen) == req->openType) 
	{
		// DB 업데이트 정보 설정
		PlayerPetData playerPet = actPetManager.m_playerPet;
		playerPet.m_petLastQuestIndex = petQuestElem.m_petQuestIndex;
		playerPet.m_petLastQuestState = static_cast<Byte>(PetQuestElem::QuestResultType::None);

		const PetXMLScript* petXMLScript = SCRIPTS.GetScript<PetXMLScript>();
		// Greenzen 비용 읽기
		GreenZen petQuestChangeCostGreenzen = static_cast<RedZen>(petXMLScript->greenzenForPetQuestChangeCost);

		if (owner->GetGreenZen() < petQuestChangeCostGreenzen) {
			UInt32 error = ErrorItem::E_NOT_ENOUGH_GREEN_ZEN;
			MU2_ERROR_LOG( LogCategory::CONTENTS
						 , "Failed to reqest EREQ_PET_QUEST_INFO_CHANGE cause Not enough GreenZen !!! : reqGreenZen:%I64d < hasGreenZen:%I64d - EC:%d, CharID%d"
				         , petQuestChangeCostGreenzen, owner->GetGreenZen()
						 , error
						 , owner->GetCharId());

			EzcResPetQuestInfoChange* resFail = NEW EzcResPetQuestInfoChange;
			resFail->result = static_cast<ErrorPet::Error>(error);
			SERVER.SendToClient(owner, EventPtr(resFail));
			return;
		}

		PetQuestData petQuest;
		actPetManager.GetPetQuestInfo(OUT petQuest);

		// 펫 모험 정보 변경 트렌젝트 생성
		PetTransaction* transaction = nullptr;
		
		// 펫 모험 정보 변경 - 마정석 사용
		if (static_cast<Byte>(PetQuestElem::QuestOpenType::Greenzen) == req->openType) 
		{
			transaction = NEW PetTransaction( __FUNCTION__, __LINE__, owner
											, ProtocolPet::EREQ_PET_QUEST_INFO_CHANGE
											, playerPet
											, PetTransaction::greenZen
											, petQuestChangeCostGreenzen
											, petQuest
											, LogCode::E_PET_QUEST_CHANGE);
		}
		// 펫 모험 정보 변경 - 레드젠 사용 - 추후 적용

		TransactionPtr transPtr(transaction);

		// 펫 모험 정보 변경 트렌젝션 등록
		if (!TransactionSystem::Register(transPtr)) 
		{
			ErrorPet::Error error = ErrorPet::E_PET_QUEST_TRANS_REGISTER_FAILURE;

			MU2_ERROR_LOG( LogCategory::CONTENTS
						 , "Failed to reqest EREQ_PET_QUEST_INFO_CHANGE cause PetTransaction register failure !!! - EC:%d, CharID%d"
						 , error
						 , owner->GetCharId());

			EzcResPetQuestInfoChange* resFail = NEW EzcResPetQuestInfoChange;
			resFail->result = error;
			SERVER.SendToClient(owner, EventPtr(resFail));
			return;
		}
	}
}

void StatePlayerBase::onReqPetQuestStart(EventPtr& e)
{
	EczReqPetQuestStart* req = static_cast<EczReqPetQuestStart*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VALID_RETURN(owner && owner->IsValid(), );

	auto actPetManager = owner->GetAction<ActionPlayerPetManage>();

	// 펫 모험 정보 초기화 시간 경과한 경우 초기화 & DB 갱신
	actPetManager->CheckAndUpdatePetQuestInfoReset();

	PetList summonPetList;

	// 펫 모험 시작 가능 체크
	ErrorPet::Error error = actPetManager->CheckStartablePetQuest(req->summonPetList, OUT summonPetList);
	if (ErrorPet::SUCCESS != error) {
		MU2_ERROR_LOG( LogCategory::CONTENTS
					 , "Failed to reqest EREQ_PET_QUEST_START cause PetQuest starable check failure !!! - EC:%d, CharID%d"
					 , error
			         , owner->GetCharId() );

		EzcResPetQuestStart* resFail = NEW EzcResPetQuestStart;
		resFail->result = error;
		SERVER.SendToClient(owner, EventPtr(resFail));
		return;
	}

	error = actPetManager->StartPetQuest(summonPetList);
	if (ErrorPet::SUCCESS != error) {
		MU2_ERROR_LOG( LogCategory::CONTENTS
					 , "Failed to reqest EREQ_PET_QUEST_START cause PetQuest start failure !!! - EC:%d, CharID%d"
					 , error
			         , owner->GetCharId() );

		EzcResPetQuestStart* resFail = NEW EzcResPetQuestStart;
		resFail->result = error;
		SERVER.SendToClient(owner, EventPtr(resFail));
		return;
	}
}

void StatePlayerBase::onReqGamePetGrow(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	EReqGamePetGrow* req = static_cast<EReqGamePetGrow*>(e.RawPtr());
		
	ErrorPet::Error eError = owner->GetPetManageAction().Grow(PetId(req->petUnique), req->materialPetIds);
	if (eError != ErrorPet::SUCCESS )
	{
		EResPetUpgrade* res = NEW EResPetUpgrade;
		res->eError = eError;
		SERVER.SendToClient(owner, EventPtr(res));
	}
}

void StatePlayerBase::onReqSoulSkillDistributePoint( EventPtr& e )
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );
	
#ifdef __Reincarnation__jason_180628__
	owner->GetCharSoulInfo().OnReqSoulSkillDistributePoint(e);
#else
	EReqSoulSkillDistributePoint* req = static_cast< EReqSoulSkillDistributePoint* >(e.RawPtr());

	auto actSoulSkill = owner->GetAction<ActionPlayerSoulSkillManage>();
	if ( false == actSoulSkill->DistrPoint( req->infos ) )
	{
		EResSoulSkillDistributePoint* res = NEW EResSoulSkillDistributePoint;
		res->eError = ErrorSkill::E_NOT_ENOUGH_SOUL_POINT;//actSoulSkill->GetError();
		SERVER.SendToClient( owner, EventPtr( res ) );
	}
#endif
}

void StatePlayerBase::onReqSoulSkillResetPoint( EventPtr& e )
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );
		
#ifdef __Reincarnation__jason_180628__
	owner->GetCharSoulInfo().OnReqSoulSkillResetPoint(e);
#else
	EReqSoulSkillResetPoint* req = static_cast<EReqSoulSkillResetPoint*>(e.RawPtr());
	UNREFERENCED_PARAMETER(req);

	if ( false == owner->GetSoulSkillManageAction().ResetPoint() )
	{
		EResSoulSkillResetPoint* res = NEW EResSoulSkillResetPoint;
		res->eError = ErrorSkill::E_NOT_ENOUGH_SOUL_POINT;
		SERVER.SendToClient( owner, EventPtr( res ) );
	}
#endif
}

void StatePlayerBase::onReqPortalEtcInfo( EventPtr& e )
{
	EReqPortalEtcInfo* req = static_cast< EReqPortalEtcInfo* >( e.RawPtr() );
	
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	auto portalElem = SCRIPTS.GetPortalInfoScript( req->portalIndex );
	VERIFY_RETURN( portalElem, );
	
	//퀘스트가 있는지 확인.	

	CharLevel titrationLevel = 0;

	if ( portalElem->doingQuest )
	{
		switch ( portalElem->type )
		{
		case PortalType::ADV_ENTER:
			if ( nullptr != portalElem->doingAdvQuest )
			{
				for ( auto& iter : *portalElem->doingAdvQuest )
				{
					if (owner->GetQuestAction().IsProgressOrComplete( iter.questIndex ) )
					{
						auto questElem = SCRIPTS.GetQuest( iter.questIndex );
						titrationLevel = questElem->level_Titration;
						break;
					}
				}
			}
			break;
		default:
			if ( nullptr != portalElem->doingAdvQuest )
			{
				for ( auto& iter : *portalElem->doingNormalQuest )
				{
					if (owner->GetQuestAction().IsProgressOrComplete( iter.questIndex ) )
					{
						auto questElem = SCRIPTS.GetQuest( iter.questIndex );
						titrationLevel = questElem->level_Titration;
						break;
					}
				}
			}
			break;
		}
	}

	EResPortalEtcInfo* res = NEW EResPortalEtcInfo;
	res->portalIndex = req->portalIndex;

	if ( owner->GetPartyId() > 0 )
	{
		owner->GetPartyAction().GetMemberLevel( res->minLevel, res->maxLevel );
	}
	else
	{
		res->myLevel = owner->GetLevel();
	}


	// 있다면 퀘스트 권장레벨로 던전레벨을 바꿔서 전송.
	if ( 0 < titrationLevel )
	{
		res->minLevel = titrationLevel;
		res->myLevel = titrationLevel;
	}
	
	if (PortalType::IsMazeDungeonEnterPortalType(portalElem->type))
	{
		if (owner->GetPlayerAttr().mazeCompleteLevel >= SCRIPTS.GetMaxMazeDifficult())
		{
			res->maxLevel = SCRIPTS.GetMaxMazeDifficult();
		}
		else
		{
			res->maxLevel = owner->GetPlayerAttr().mazeCompleteLevel + 1;
			
		}

		res->minLevel = 1;
		res->myLevel = owner->GetPlayerAttr().lastSelectedMazeLevel;
	}

	owner->SendToClient(EventPtr( res ) );
}

void StatePlayerBase::onReqWhoLeaveNeedGroupMemberDamageReport( EventPtr& e )
{
	EReqGameWhoLeaveNeedGroupMemberDamageReport* req = static_cast< EReqGameWhoLeaveNeedGroupMemberDamageReport* >( e.RawPtr() );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	auto actPlayerDamageMeter = owner->GetAction<ActionPlayerDamageMeter>();
	VERIFY_RETURN(actPlayerDamageMeter, );

	DamageMeterReport damageReport;
	actPlayerDamageMeter->GetMyDamageReport( damageReport );

	if ( damageReport.damagemeterKey == 0 || damageReport.damagemeterKey != req->damagemeterKey )
	{
		return;
	}

	EResGameDamageReport* res = NEW EResGameDamageReport;
	res->myReport = damageReport;
	SERVER.SendToClient( owner, EventPtr( res ) );
}

void StatePlayerBase::onNtfInitYourDamageReport( EventPtr& e )
{
	EwzNtfInitYourDamageReport* ntf = static_cast< EwzNtfInitYourDamageReport* >( e.RawPtr() );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	auto actPlayerDamageMeter = owner->GetAction<ActionPlayerDamageMeter>();
	VERIFY_RETURN(actPlayerDamageMeter, );

	actPlayerDamageMeter->SetMyDamageReport( ntf->yourReport );
}

//void StatePlayerBase::onResGameCheckAchievementCondition( EventPtr& e )
//{
//	EResGameCheckAchievementCondition* res = static_cast< EResGameCheckAchievementCondition* >( e.RawPtr() );
//	EntityPlayer* owner = GetOwnerPlayer();
//
//	ActionPlayerAchievement* aa = GetEntityAction( owner );
//	aa->OnResponseCheckCondition( res->achievementIndex, res->groupIndex, res->event );
//}

void StatePlayerBase::onReqGameCharChangeTitle( EventPtr& e )
{
	EReqGameCharChangeTitle* req = static_cast< EReqGameCharChangeTitle* >( e.RawPtr() );
	EntityPlayer* owner = GetOwnerPlayer();

	ActionPlayerAchievement* aa = GetEntityAction( owner );
	aa->ChangeTitle( req->titleId );
}

void StatePlayerBase::onReqUseTownPortalItem( EventPtr& e )
{
	EReqUseTownPortalItem* req = static_cast< EReqUseTownPortalItem* >( e.RawPtr() );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ActionPlayerUseItem* actionUseItem = GetEntityAction( owner );
	VERIFY_RETURN(actionUseItem, );

	ErrorItem::Error eErrorItem = actionUseItem->UsePortalStoneItem( req->slotInfo, req->selectDefault );
	if ( ErrorItem::SUCCESS != eErrorItem )
	{
		if ( ErrorItem::E_REFER_SYSTEM_MSG == eErrorItem )
		{
			SendSystemMsg( owner, SystemMessage( L"sys", actionUseItem->GetLastSystemMsg() ) );
		}
		else
		{
			EResUseTownPortalItem* res = NEW EResUseTownPortalItem;
			res->eError = eErrorItem;
			SERVER.SendToClient( owner, EventPtr( res ) );
		}
	}
}

void StatePlayerBase::onReqUseExpanndItem( EventPtr& e )
{
	EReqItemExpandPeriod *req = static_cast<EReqItemExpandPeriod*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ActionPlayerUseItem* actionUseItem = GetEntityAction( owner );
	VERIFY_RETURN(actionUseItem, );

	ErrorItem::Error eErrorItem = actionUseItem->UsePeriodExpandItem( req->sourceItem, req->targetItem );

	if (ErrorItem::SUCCESS != eErrorItem)
	{
		if (ErrorItem::E_REFER_SYSTEM_MSG == eErrorItem)
		{
			SendSystemMsg( owner, SystemMessage( L"sys", actionUseItem->GetLastSystemMsg() ) );
		}
		else
		{
			EResItemExpandPeriod* res = NEW EResItemExpandPeriod;
			res->result = static_cast< UInt32 >(eErrorItem);
			SERVER.SendToClient( owner, EventPtr( res ) );
		}
	}
}

void StatePlayerBase::onReqItemCubeEvent(EventPtr &e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );
	owner->GetManagerItemCube().OnEvent(e);
}

void StatePlayerBase::onReqGameForceMoveStartComplete( EventPtr& e )
{
	e;

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), )

	owner->GetPlayerAttr().moveState = NpcAnimation::MOVE_RUN;

	Int32 speed = owner->GetAction< ActionAbility >()->GetAbilityValue(EAT::RUN_SPEED );

	ActionPlayer* playerAction = GetEntityAction( owner );
	playerAction->SetupSpeed( static_cast<Float>( speed) );

	EResGameForceMoveStartComplete* res = NEW EResGameForceMoveStartComplete;
	res->speed = speed;
	res->aniType = NpcAnimation::MOVE_RUN;
	SERVER.SendToClient( owner, EventPtr( res ) );
}

void StatePlayerBase::onReqSectorController( EventPtr& e )
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), )

	ActionSector* as = GetEntityAction( owner );
	auto controller = as->GetActionSectorController();
	controller->OnEvent( e );
}

void StatePlayerBase::onNtfGameAchievementBurningStamina( EventPtr& e )
{
	ENtfGameAchievementBurningStamina* ntf = static_cast< ENtfGameAchievementBurningStamina* >( e.RawPtr() );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), )

	ActionPlayerAchievement* aa = GetEntityAction( owner );
	AchievementGetAwakenBuffEvent achievement( owner, ntf->burningLevel, ntf->continentType );

	aa->OnEvent( &achievement );
}

void StatePlayerBase::onNtfGameAchievementDpsPoint( EventPtr& e )
{
	ENtfGameAchievementDpsPoint* ntf = static_cast< ENtfGameAchievementDpsPoint* >( e.RawPtr() );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), )

	ActionPlayerAchievement* aa = GetEntityAction( owner );
	AchievementDpsPointEvent achievement( owner, ntf->myDpsPoint );
	aa->OnEvent( &achievement );
}

void StatePlayerBase::onReqGameRequestSurvey( EventPtr& e )
{
	EReqGameRequestSurvey* req = static_cast< EReqGameRequestSurvey* >( e.RawPtr() );
	UNREFERENCED_PARAMETER( req );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), )

	EResGameRequestSurvey* res = NEW EResGameRequestSurvey;
	owner->GetSurveyAction().GetFrontSurveyInfo( res->info );
	SERVER.SendToClient( owner, EventPtr( res ) );
}

void StatePlayerBase::onReqGameSubmitSurvey( EventPtr& e )
{
	EReqGameSubmitSurvey* req = static_cast< EReqGameSubmitSurvey* >( e.RawPtr() );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), )

	if ( req->isSubmit == false )  
	{
		// 해당 인덱스의 설문을 참여하지 않음
		// 강제 완료 시킨다.
		owner->GetSurveyAction().NotSubmitSurveyProcess( req->submit.surveyIndex );
	}
	else
	{
		owner->GetSurveyAction().CheckComplete( req->submit );
	}
}

void StatePlayerBase::onResDbSurveyDelete( EventPtr& e )
{
	EResDbSurveyDelete* res = static_cast< EResDbSurveyDelete* >( e.RawPtr() );
	if ( res->eError != ErrorDB::SUCCESS )
	{
		MU2_WARN_LOG(core::LogCategory::DEFAULT, L"StatePlayerBase::onResDbSurveyDelete > Response DB Error > [Response Info:%s]", res->ToString().c_str() );
		return;
	}

	EntityPlayer* owner = GetOwnerPlayer();

	theGameMsg.SendGameDebugMsg( owner, L"해당 플레이어의 모든 설문조사 제거 완료\n" );
}

void StatePlayerBase::onReqGameSkipPlayScene( EventPtr& e )
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	EReqGameSkipPlayScene* req = static_cast< EReqGameSkipPlayScene* >( e.RawPtr() );	

	Sector* sector = owner->GetSector();
	VERIFY_RETURN(sector, );	

	sector->GetSectorController().GetControllerAction().OnSkipPlayScene( req->id, owner );
}

void StatePlayerBase::onReqGamePublicityFindParty(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	auto findPartyPromotionConfig = SCRIPTS.GetFindPartyPromotionConfig();
	
	if (owner->SubMoney(findPartyPromotionConfig.promotionPay))
	{
		owner->SendChangeMoney( findPartyPromotionConfig.promotionPay, ChangedMoney::SUB /* sub type */ );

		SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_FindParty_Promotion_Start" ) );

		EReqGameFindPartyPromotionWorld* req = NEW EReqGameFindPartyPromotionWorld;
		req->duration = findPartyPromotionConfig.promotionDuration;
		SERVER.SendToClient(owner, EventPtr(req));
		return;
	}

	SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_FindParty_Err_Promotion_Cost" ) );

	EResGameFindPartyPromotion* res = NEW EResGameFindPartyPromotion;
	res->eError = ErrorPartySearch::E_NOT_ENOUGH_MONEY;
	SERVER.SendToClient(owner, EventPtr(res));
}

void StatePlayerBase::onResGameFindPartyPromotionWorld(EventPtr& e)
{
	auto resFromWorld = static_cast<EResGameFindPartyPromotionWorld*>(e.RawPtr());
	EntityPlayer* owner = GetOwnerPlayer();

	EResGameFindPartyPromotion* res = NEW EResGameFindPartyPromotion;
	res->isAblePromotion = resFromWorld->isAblePromotion;
	res->eError = resFromWorld->eError;
	SERVER.SendToClient(owner, EventPtr(res));
}

void StatePlayerBase::onReqKnightageGetCreationRequirement(EventPtr& e)
{
//	EReqKnightageGetCreationRequirement* req = static_cast<EReqKnightageGetCreationRequirement*>(e.RawPtr());
	EResKnightageGetCreationRequirement* res = NEW EResKnightageGetCreationRequirement;
	
	EntityPlayer* owner = GetOwnerPlayer();
	res->zen = SCRIPTS.GetKnightageSetupInfo().creation.requirementZen;

	SERVER.SendToClient(owner, EventPtr(res));
}

void StatePlayerBase::onReqKnightageCheckKnightageName(EventPtr& e)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	EReqKnightageCheckKnightageName* req = static_cast<EReqKnightageCheckKnightageName*>(e.RawPtr()); 	
	
	const FilterData & filter = SCRIPTS.GetFilterDatas();
	
	if (req->knightageName.length() < 2 || req->knightageName.length() > Limits::MAX_KNIGHTAGE_NAME_CHECK
		|| FALSE == filter.Check( FilterData::IMPOSSIBLE_NAME, req->knightageName ))
	{
		EResKnightageCheckKnightageName* res = NEW EResKnightageCheckKnightageName;
		res->result = ErrorKnightage::E_INVALID_KNIGHTAGE_NAME;
		SERVER.SendToClient(player, EventPtr(res));
		return;
	}
	
	// db에 존재하는지 체크하고 알려준다.
	EReqDbCheckExistKnightageName* reqDb = NEW EReqDbCheckExistKnightageName;
	reqDb->knightageName = req->knightageName;
	SERVER.SendToDb(player, EventPtr(reqDb));
}

void StatePlayerBase::onResDbCheckExistKnightageName(EventPtr& e)
{	
	EResDbCheckExistKnightageName* resDb = static_cast<EResDbCheckExistKnightageName*>(e.RawPtr());
	EntityPlayer* owner = GetOwnerPlayer();

	EResKnightageCheckKnightageName* res = NEW EResKnightageCheckKnightageName;
	res->knightageName = resDb->knightageName;
	res->result = (resDb->eError == 0) ? ErrorDB::SUCCESS : ErrorDB::E_ALREADY_EXIST_KNIGHTAG_NAME;

	SERVER.SendToClient(owner, EventPtr(res));
}



void StatePlayerBase::onReqKnightageCreate(EventPtr& e)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	EReqKnightageCreate* req = static_cast<EReqKnightageCreate*>(e.RawPtr());	

	ActionPlayerContact *contact = GetEntityAction( player );
	if (contact->CheckNpcDistance() == false ||
		contact->HasNpcFunction( NpcFunctionType::NPC_FUNCTION_CREATE_KNIGHT_AGE ) == false)
	{
		EResKnightageCreate* res = NEW EResKnightageCreate;
		res->knightageName = req->knightageName;
		res->result = ErrorKnightage::E_NPC_RANGE_CHECK;
		SERVER.SendToClient( player, EventPtr( res ) );
		return;
	}

	const FilterData & filter = SCRIPTS.GetFilterDatas();

	if (req->knightageName.length() < 2 || req->knightageName.length() > Limits::MAX_KNIGHTAGE_NAME_CHECK
		|| FALSE == filter.Check( FilterData::IMPOSSIBLE_NAME, req->knightageName ))
	{
		EResKnightageCreate* res = NEW EResKnightageCreate;
		res->knightageName = req->knightageName;
		res->result = ErrorKnightage::E_INVALID_KNIGHTAGE_NAME;
		SERVER.SendToClient(player, EventPtr(res));
		return;
	}
	
	const KnightageSetupInfo& setupInfo = SCRIPTS.GetKnightageSetupInfo();	
	
	if (player->SubMoney(setupInfo.creation.requirementZen))
	{	
		EReqDbInsertKnightage* reqDb = NEW EReqDbInsertKnightage;

		reqDb->charId = player->GetCharId();
		reqDb->subZen = setupInfo.creation.requirementZen;
		reqDb->masterGrade = static_cast<Byte>(knightage::EMemberGrade::MASTER);

		DateTime present = DateTime::GetPresentTime();

		reqDb->info.id = 0;
		reqDb->info.name = req->knightageName;
		reqDb->info.regTime = present.GetTime();
		reqDb->info.options.searchTarget = setupInfo.creation.option.searchTarget;
		reqDb->info.options.joinType = setupInfo.creation.option.joinType;
		reqDb->info.options.purposeType = setupInfo.creation.option.purposeType;
		reqDb->info.emblem.shape = setupInfo.creation.emblem.shape;
		reqDb->info.emblem.edge = setupInfo.creation.emblem.edge;
		reqDb->info.emblem.background = setupInfo.creation.emblem.background;
		reqDb->info.emblem.backColor = setupInfo.creation.emblem.backgroundColor;
		// 금주의 시작 시간을 구한다.
		DateTimeSpan payTimeSpan;// (setupInfo.maintenance.dayOfWeek, setupInfo.maintenance.hour, setupInfo.maintenance.minute, 0);
		DateTimeSpan passTime(present.GetDayOfWeek() - 1, present.GetHour(), present.GetMinute(), present.GetSecond(),
								present.GetMicroSecond(), present.GetMicroSecond(), present.GetNanoSecond());		
		DateTime weekStart(present - passTime);
		DateTime thisWeekPaymentTime(weekStart + payTimeSpan);
		if (thisWeekPaymentTime < present)
		{
			thisWeekPaymentTime += DateTimeSpan(7, 0, 0, 0);
		}
		reqDb->info.lastContributionPaymentTime = thisWeekPaymentTime.GetTime();
		reqDb->info.level = 1;
		reqDb->info.exp = 0;
		reqDb->info.notice = L"";
		reqDb->info.altars.fill(0);
		SERVER.SendToDb(player, EventPtr(reqDb));
	}
	else
	{
		EResKnightageCreate* res = NEW EResKnightageCreate;
		res->knightageName = req->knightageName;
		res->result = ErrorKnightage::E_NOT_ENOUGH_ZEN;
		SERVER.SendToClient(player, EventPtr(res));
	}
}


void StatePlayerBase::onResDbInsertKnightage(EventPtr& e)
{
	EntityPlayer* ownPlayer = GetOwnerPlayer();
	VERIFY_RETURN(ownPlayer && ownPlayer->IsValid(), );

	const KnightageSetupInfo& setupInfo = SCRIPTS.GetKnightageSetupInfo();	

	EResDbInsertKnightage* dbRes = static_cast<EResDbInsertKnightage*>(e.RawPtr());	
	if (dbRes->eError == ErrorDB::SUCCESS)
	{
		ENtfKnightageCreated* ntf = NEW ENtfKnightageCreated;
		EventPtr ntfPtr(ntf);

		ntf->knightageId = dbRes->knightageId;
		SERVER.SendToClient(ownPlayer, ntfPtr);

		ownPlayer->SendChangeMoney(setupInfo.creation.requirementZen, ChangedMoney::SUB );

		EReqLogDb2* log = NEW EReqLogDb2;
		EventPtr logEventPtr( log );
		EventSetter::FillLogForEntity(LogCode::E_KNIGHT_CREATE, ownPlayer, log->action);
		
		log->action.subInfo = dbRes->knightageName;
		log->action.var32s.push_back(ntf->knightageId);
		log->action.var64s.push_back(setupInfo.creation.requirementZen);		
		log->action.var64s.push_back(ownPlayer->GetPlayerAttr().money);

		SendDataCenter(logEventPtr );
	}
	else
	{	
		if (!ownPlayer->AddMoney(setupInfo.creation.requirementZen))
		{	
			MU2_WARN_LOG( LogCategory::CONTENTS, L"StatePlayerBase::onResDbInsertKnightage > charId : %d , add money failed ( %I64d )", ownPlayer->GetCharId(), setupInfo.creation.requirementZen);
		}
	}

	EResKnightageCreate* res = NEW EResKnightageCreate;
	switch ((Int32)dbRes->eError)
	{
	case 0: 
		res->result = ErrorKnightage::SUCCESS;
		break;
	case 1:
		res->result = ErrorKnightage::E_ALREADY_EXIST_KNIGHTAG_NAME;
		break;
	case 3:
		res->result = ErrorKnightage::E_ALREADY_MEMBER;
		break;
	default:
		res->result = ErrorKnightage::E_ERROR;
	}
	res->knightageId = dbRes->knightageId;
	res->knightageName = dbRes->knightageName;
	SERVER.SendToClient(ownPlayer, EventPtr(res));
}

void StatePlayerBase::onReqKnightageSpendItemsAltar(EventPtr & e)
{
	EReqKnightageSpendItemsAltar* req = static_cast<EReqKnightageSpendItemsAltar*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	ActionPlayerKnightageStorage* actPlayerKnightageStorage = GetEntityAction(owner);

	// 아직은 없으니 추후 처리하도록 한다.
	ErrorKnightage::Error eError = actPlayerKnightageStorage->UseStorageForUpgradeAltar(e);

	if (eError != ErrorKnightage::SUCCESS)
	{
		// 아직은 명확히 처리되지 않아서 우선은 에러 처리
		EResKnightageSpendItemsAltar* res = NEW EResKnightageSpendItemsAltar;
		res->knightageId = req->knightageId;
		res->commanderId = req->commanderId;
		res->eError = eError;
		res->altarIndex = req->altarIndex;
		res->altarSlot = req->altarSlot;
		SERVER.SendToClient(owner, EventPtr(res));

	}
}

void StatePlayerBase::onNtfKnightageAddContributionPoint(EventPtr & e)
{
	ENtfKnightageAddContributionPoint* ntf = static_cast<ENtfKnightageAddContributionPoint*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	if (owner->AddContributionPoint(ntf->contributionPoint, false))
	{
		owner->SendChangedContributionPoint(ntf->contributionPoint, ChangedMoney::ADD);

		ENtfKnightageReceiveContributionPoint* ntf2 = NEW ENtfKnightageReceiveContributionPoint;
		ntf2->contributionPoint = ntf->contributionPoint;
		SERVER.SendToClient(owner, EventPtr(ntf2));
	}
}

void StatePlayerBase::onResDbAddKnightageContributionPoint(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	EResDbAddKnightageContributionPoint* dbRes = static_cast<EResDbAddKnightageContributionPoint*>(e.RawPtr());

	if (dbRes->eError != ErrorDB::SUCCESS)
	{
		MU2_ERROR_LOG(LogCategory::CONTENTS, "EResDbAddKnightageContributionPoint failed : harid = %u, contribution = %I64d",
			dbRes->charId, dbRes->contributionPoint);
			
		if (dbRes->contributionPoint > 0)
		{
			if (owner->SubContributionPoint(dbRes->contributionPoint, false))
			{
				owner->SendChangedContributionPoint(dbRes->contributionPoint, ChangedMoney::SUB);
			}
		}
		else
		{
			if (owner->AddContributionPoint(-dbRes->contributionPoint, false))
			{
				owner->SendChangedContributionPoint(dbRes->contributionPoint, ChangedMoney::ADD);
			}
		}
		
	}
}

void StatePlayerBase::onReqKnightageOpenStorage(EventPtr & e)
{
	//EReqKnightageOpenStorage* ntf = static_cast<EReqKnightageOpenStorage*>(e.RawPtr());
	//DBG_UNREFERENCED_LOCAL_VARIABLE(ntf);

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );	

	ErrorKnightage::Error eError = owner->GetKnightageStorageAction().RequestUsingStorage();
	if (eError != ErrorKnightage::SUCCESS)
	{
		EResKnightageOpenStorage* res = NEW EResKnightageOpenStorage;
		res->eError = eError;
		SERVER.SendToClient(owner, EventPtr(res));
	}
}

void StatePlayerBase::onReqKnightageCloseStorage(EventPtr & e)
{
	//EReqKnightageCloseStorage* req = static_cast<EReqKnightageCloseStorage*>(e.RawPtr());
	//DBG_UNREFERENCED_LOCAL_VARIABLE(req);
	EntityPlayer* owner = GetOwnerPlayer();
	ActionPlayerKnightageStorage* actPlayerKnightageStorage = GetEntityAction(owner);

	actPlayerKnightageStorage->CloseStorage();
}

void StatePlayerBase::onResKnightageStorageUsingStart(EventPtr & e)
{
	EResKnightageStorageUsingStart* res = static_cast<EResKnightageStorageUsingStart*>(e.RawPtr());
	EntityPlayer* owner = GetOwnerPlayer();
	if (res->eError != ErrorKnightage::SUCCESS)
	{
		EResKnightageOpenStorage* response = NEW EResKnightageOpenStorage;
		response->eError = res->eError;
		SERVER.SendToClient(owner, EventPtr(response));
	}
	else
	{
		ActionPlayerKnightageStorage* actPlayerKnightageStorage = GetEntityAction(owner);

		ErrorKnightage::Error eError = actPlayerKnightageStorage->ResponseUsingStorage(res->eError, res->knightageId, res->inventoryCount, res->byClient);
		if (eError != ErrorKnightage::SUCCESS)
		{
			EResKnightageOpenStorage* response = NEW EResKnightageOpenStorage;
			response->eError = res->eError;
			SERVER.SendToClient(owner, EventPtr(response));

			actPlayerKnightageStorage->CloseStorage();
		}
	}
}

void StatePlayerBase::onNtfKnightageStorageClosed(EventPtr & e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	ActionPlayerKnightageStorage* actPlayerKnightageStorage = GetEntityAction(owner);

	actPlayerKnightageStorage->FinishedUsingStorage();
}

void StatePlayerBase::onResDbLoadKnightageItems(EventPtr& e)
{
	EResDbLoadKnightageItems* dbRes = static_cast<EResDbLoadKnightageItems*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	ActionPlayerKnightageStorage* actPlayerKnightageStorage = GetEntityAction(owner);

	MU2_ASSERT(dbRes->eError == ErrorDB::SUCCESS);
	ErrorKnightage::Error eErrorKnightage = dbRes->eError == ErrorDB::SUCCESS ? ErrorKnightage::SUCCESS : ErrorKnightage::E_ERROR;
	if (eErrorKnightage == ErrorKnightage::SUCCESS)
	{
		eErrorKnightage = actPlayerKnightageStorage->LoadKnightageItems(dbRes->knightageId, dbRes->items);
	}

	if (eErrorKnightage != ErrorKnightage::SUCCESS)
	{
		if (actPlayerKnightageStorage->IsClient())
		{
			EResKnightageOpenStorage* res = NEW EResKnightageOpenStorage;
			res->eError = eErrorKnightage;
			SERVER.SendToClient(owner, EventPtr(res));

			actPlayerKnightageStorage->CloseStorage();
		}
		else
		{
			// 신단 업그레이드가 실패하였다.
			actPlayerKnightageStorage->SendAltarUpgradeResult(eErrorKnightage);
		}
	}
}

void StatePlayerBase::onNtfKnightageUpdateMember(EventPtr& e)
{
	ENtfKnightageUpdateMember* ntf = static_cast<ENtfKnightageUpdateMember*>(e.RawPtr());

	// 개인의 기사단 정보를 변경해준다.
	EntityPlayer* owner = GetOwnerPlayer();
	ActionPlayerKnightage* actKnightage = GetEntityAction(owner);
	if (actKnightage != nullptr)
	{
		actKnightage->ApplyKnightageMember(ntf->knightageId);
		actKnightage->UpdateMyMemberInfo();
	}
}

void StatePlayerBase::onReqKnightageFollowing( EventPtr& e )
{
	EReqKnightageFollowing* req = static_cast<EReqKnightageFollowing*>(e.RawPtr());
	req;

	EntityPlayer* owner = GetOwnerPlayer();
	ActionPlayerKnightage* actKnightage = GetEntityAction( owner );
	if (actKnightage != nullptr)
	{
		ErrorItem::Error error = actKnightage->KnightageFollowing( req->knightageId );
		if (error != ErrorItem::SUCCESS)
		{
			EResKnightageFollowing *res = NEW EResKnightageFollowing;
			res->eError = error;
			SERVER.SendToClient( owner, EventPtr(res) );
		}
	}

}

void StatePlayerBase::onReqKnightageGiveTrophy( EventPtr& e )
{
	EReqKnightageGiveTrophy* req = static_cast<EReqKnightageGiveTrophy*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	ActionPlayerKnightage* actKnightage = GetEntityAction( owner );
	if (actKnightage != nullptr)
	{
		ErrorItem::Error error = actKnightage->GiveTrophyPoint(req->donationCount);
		if (error != ErrorItem::SUCCESS)
		{
			EResKnightageGiveTrophy *res = NEW EResKnightageGiveTrophy;
			res->eError = error;
			SERVER.SendToClient( owner, EventPtr( res ) );
		}
	}
}

void StatePlayerBase::onNtfKnightageChangeCapitalDominion( EventPtr& e )
{
	ENtfZoneKnightageChangeCapitalDominion* ntf = static_cast<ENtfZoneKnightageChangeCapitalDominion*>(e.RawPtr());
	ntf;

	EntityPlayer* owner = GetOwnerPlayer();
	ActionPlayerKnightage* actKnightage = GetEntityAction( owner );
	if (actKnightage == nullptr)
		return;

	ActionSector *actSector = GetEntityAction( owner );
	if (actSector == nullptr)
		return;

	if (actSector->GetSector()->GetDominionId() == 0)
		return;

	actKnightage->SetupDominion( ntf->capitalDominionId, ntf->adjecentLevel );

	ActionPlayerInventory *actInven = GetEntityAction( owner );
	if (actInven)
	{
		actInven->ClearAmplifactionInfo( actKnightage->GetCapitalGrade(), actKnightage->GetAdjacentLv() );
	}

}

void StatePlayerBase::onReqGamePersonalPlayScene(EventPtr& e)
{
	EReqGamePersonalPlayScene* req = static_cast<EReqGamePersonalPlayScene*>(e.RawPtr());
	EntityPlayer* owner = GetOwnerPlayer();
	UInt32 personalPlaySceneSkill = SCRIPTS.GetCommonSkillConfigScript().personalPlayeScene;
	
	if (req->isPlayScene)
	{
		// 키즈맷 버프 활성화		
		theLogicEffectSystem.OnSingleCast(owner, personalPlaySceneSkill);
	}
	else
	{
		// 키즈맷 버프 비활성화
		theLogicEffectSystem.OnCancelBuffCast(owner, personalPlaySceneSkill);
	}
}


void StatePlayerBase::onReqDungeonMatchPortalRequirementCheck(EventPtr& e)
{
	// 꼭 로딩이 완료된 플레이어에 한해서만 신청해야만 한다.
	EReqDungeonMatchPortalRequirementCheck* req = static_cast<EReqDungeonMatchPortalRequirementCheck*>(e.RawPtr());

	EntityPlayer* ownerPlayer = GetOwnerPlayer();
	VERIFY_RETURN(ownerPlayer, );
	
	const PortalInfoElem* elem = SCRIPTS.GetPortalInfoScript(req->portalIndex);
	VERIFY_RETURN(elem, );	

	EResDungeonMatchPortalRequirementCheck* res = NEW EResDungeonMatchPortalRequirementCheck;
	EventPtr resPtr(res);
	res->requesterCharId = req->requesterCharId;
	res->checkCharId = ownerPlayer->GetCharId();
	res->portalIndex = req->portalIndex;
	res->difficulty = req->difficulty;
	res->combatPower = ownerPlayer->GetCombatPower();
#ifdef __Reincarnation__jason_180628__
	res->soulLevelEx = ownerPlayer->GetSoulLevelEx();
#else
	res->soulLevel = ownerPlayer->GetRawSoulLevel();
#endif
	

	if ( (req->requesterCharId == ownerPlayer->GetCharId()) && PortalType::IsDistanceCheckPortalType(elem->type) )
	{
		if (false == thePortalSystem.CheckDistance(ownerPlayer, req->portalIndex))
		{
			SendSystemMsg(ownerPlayer, SystemMessage(L"sys", L"Msg_Invalied_Distance"));
			res->result = ErrorJoin::E_INVALID_DISTANCE;
			ownerPlayer->SendToClient(resPtr);
			return;
		}
	}

	if (ownerPlayer->IsDead())
	{
		SendSystemMsg( ownerPlayer, SystemMessage( L"sys", L"Msg_DungeonMatching_Err_NotAlive" ) );
		res->result = ErrorJoin::E_NOT_ALIVE;
		res->error = ownerPlayer->GetCharId() != req->requesterCharId ? L"Msg_DungeonMatching_Err_NotAlive" : L"";
		ownerPlayer->SendToClient(resPtr );
		return;
	}

	ErrorJoin::Error eError = thePortalSystem.CheckRequirement(ownerPlayer, elem, PortalUseItemList(), req->difficulty, true, true );

	std::wstring strError;
	if (ErrorJoin::SUCCESS != eError)
	{
		switch (eError)
		{
		case ErrorJoin::E_REQUIRE_ITEM:					strError = L"Msg_Don_have_Item";							break;
		case ErrorJoin::E_LEVEL_LIMIT:					strError = ownerPlayer->GetCharId() != req->requesterCharId ? L"Msg_DungeonMatching_Err_PartyLevel" : L"Msg_DungeonMatching_Err_Level";				break;
		case ErrorJoin::E_REQUIRE_ZEN:					strError = L"Msg_Not_Enough_Zen";							break;
		case ErrorJoin::E_REQUIRE_GREEN_ZEN:			strError = L"Msg_Not_Enough_Green_Zen";					break;
		case ErrorJoin::E_COMBAT_POWER_LIMIT:			strError = ownerPlayer->GetCharId() != req->requesterCharId ? L"Msg_DungeonMatching_Err_PartyCombatPower" : L"Msg_DungeonMatching_Err_CombatPower";		break;
		case ErrorJoin::E_INVALID_SOUL_LEVEL:			strError = L"Msg_Missionmap_Enter_SoulLevel_Err";			break;
		case ErrorJoin::E_MAZE_TICKET:					strError = L"Msg_Donnot_have_ticket";						break;
		case ErrorJoin::E_MAZE_MEMBER_TICKET:			strError = L"Msg_Party_Donnot_have_ticket";				break;
		case ErrorJoin::E_REQUIRE_PROGRESS_QUEST:		strError = L"Msg_DungeonEntrance_Err_Admission_Quest";		break;
		default:										strError = ownerPlayer->GetCharId() != req->requesterCharId ? L"Msg_DungeonMatching_Err_PartyQuest" : L"Msg_DungeonMatching_Err_Quest";				break;
		}
	}   
	res->error = strError;
	res->result = eError;
	ownerPlayer->SendToClient(resPtr);
}

void StatePlayerBase::onReqDungeonMatchPartySyncMember(EventPtr& e)
{
	EReqDungeonMatchPartySyncMember* req = static_cast<EReqDungeonMatchPartySyncMember*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	auto actParty = owner->GetAction<ActionPlayerParty>();

	EResDungeonMatchPartySyncMember* res = NEW EResDungeonMatchPartySyncMember;
	res->eError = 0;
	res->sessionKey = req->sessionKey;
	res->groupId = req->groupId;
	actParty->Fillup(res->member);
	SERVER.SendToClient(owner, EventPtr(res));
}


void StatePlayerBase::onReqBeginPortalWhenInvalidRequirement( EventPtr&  )
{
	EntityPlayer* owner = GetOwnerPlayer();
	auto actPortal = owner->GetAction<ActionPlayerPortal>();

	theGameMsg.SendGameDebugMsg( owner, L"어뷰징으로 인해 강퇴함\n" );
	actPortal->WarpBeginPortal();
}

void StatePlayerBase::onReqChangeTutorialGuide( EventPtr& e )
{
	EReqChangeTutorialGuide* req = static_cast<EReqChangeTutorialGuide*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner, );
	
	const TutorialScript* tutorialScript = SCRIPTS.GetScript<TutorialScript>();
	VERIFY_RETURN(tutorialScript, );

	const TutorialElem* tutorialElem = tutorialScript->Get(owner->GetRaceType());
	VERIFY_RETURN(tutorialElem, );
	VERIFY_RETURN(tutorialElem->zonedId == owner->GetIndexZone(), );

	TutorialState::Enum tutorialState = owner->GetTutorialState();
	VERIFY_RETURN(tutorialState == TutorialState::PLAYING, );
	
	// 최초 진입 후 가이드변경일 경우에는 포탈이동을 안한다. 
	if (req->isChangeMap == false)
	{
		owner->GetTutorialAction().SelectGuide(req->type);
	}
	else
	{
		// 최초 가이드 변경이 아닐경우 던전을 다시 시작한다. (재시작시 해당 가이드의 아이템 / 스킬 세팅)
		Sector* sector = owner->GetSector();
		VERIFY_RETURN(sector, );

		const InstanceDungeonInfo& dungeonInfo = sector->GetInstanceDungeonInfo();

		EzwReqChangeMap* reqToWorld = NEW EzwReqChangeMap;				
		reqToWorld->toIndexPositionMovable = tutorialElem->positionIndex;
		reqToWorld->clickPortal.indexPosition4BackChannel = tutorialElem->positionIndex;
		reqToWorld->toInsInfoList.push_back(dungeonInfo);
		reqToWorld->logNpcPortalIndex = 0;
		SERVER.SendChangemapToWorld(owner, EzwReqChangeMap::onReqBeginPortalWhenInvalidRequirement, EventPtr(reqToWorld));
	}

}

void StatePlayerBase::onReqGameDuelRequest( EventPtr& e )
{
	EReqGameDuelRequest* req = static_cast<EReqGameDuelRequest*>(e.RawPtr());

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	auto ownerActSector = player->GetAction< ActionSector >();
	auto ownerTagValue = ownerActSector->GetNavTagValueOnNavTriangle();
	if (TagValueFunction::DEFAULT_TAGVALUE != ownerTagValue && TagValueFunction::ABLE_ANYTHING != ownerTagValue)
	{
		// owner가 위치한 곳의 tagValue 값으로 PVP가 가능한 지역인지 체크 한다.
		if (TagValueFunction::NON_PVP & ownerTagValue)
		{
			SendSystemMsg(player, SystemMessage(L"sys", L"Msg_Duel_Invalid_Place"));
			return;
		}
	}
	
	EntityPlayer* target = ownerActSector->GetPlayerInMySector( req->targetId );
	if (target == nullptr)
	{
		SendSystemMsg( player, SystemMessage(L"sys", L"Msg_Duel_Target_Distance") );
		return;
	}

	VALID_RETURN(player->IsAlive(), );
	VALID_RETURN(target->IsAlive(), );
	

	auto targetActSector = target->GetAction<ActionSector>();
	auto targetTagValue = targetActSector->GetNavTagValueOnNavTriangle();
	if (TagValueFunction::DEFAULT_TAGVALUE != targetTagValue && TagValueFunction::ABLE_ANYTHING != targetTagValue)
	{
		// target이 위치한 곳의 tagValue 값으로 PVP가 가능한 지역인지 체크 한다.
		if (TagValueFunction::NON_PVP & targetTagValue)
		{
			SendSystemMsg(player, SystemMessage(L"sys", L"Msg_Duel_Invalid_Place"));
			return;
		}
	}

	Sector* sector = ownerActSector->GetSector();
	InstanceSectorType::Enum instanceType = sector->GetInstanceType();
	if (instanceType >= InstanceSectorType::MISSIONMAP_BEGIN &&
		instanceType < InstanceSectorType::MISSIONMAP_END)
	{
		// 결투 깃발 소환을 할 수 없는 장소이다
		SendSystemMsg( player, SystemMessage(L"sys", L"Msg_Duel_Invalid_Place") );
		return;
	}

	const WorldElem* elem = SCRIPTS.GetScript<WorldScript>()->Get( sector->GetId().GetZoneIndex() );
	if (elem->isDuelRequest == false)
	{
		// 결투 깃발 소환을 할 수 없는 장소이다
		SendSystemMsg( player, SystemMessage(L"sys", L"Msg_Duel_Invalid_Place") );
		return;
	}

	const DuelSystemData& duelSystem = SCRIPTS.GetDuelSystemScript();

	auto ownerAttrPos = player->GetAttribute< AttributePosition >();
	auto targetAttrPos = target->GetAttribute< AttributePosition >();
	Float distance = Distance( ownerAttrPos->pos, targetAttrPos->pos );
	if (duelSystem.requestRange < distance)
	{
		// 신청 대상의 거리가 멀다
		SendSystemMsg( player, SystemMessage(L"sys", L"Msg_Duel_Target_Distance") );
		this->sendDuelRequestFail();
		return;
	}

	auto targetActPlayer = target->GetAction< ActionPlayer >();
	EntityNpc* flagNpc = targetActPlayer->GetDuelFlagNpc();
	if (flagNpc != nullptr)
	{
		SendSystemMsg( player, SystemMessage(L"sys", L"Msg_Duel_Target_Already_Duel") );
		this->sendDuelRequestFail();
		return;
	}
	else
	{
		auto actPlayer = player->GetAction< ActionPlayer >();
		flagNpc = actPlayer->GetDuelFlagNpc();
		if (flagNpc != nullptr)
		{
			// 내가 이미 결투 진행 중이다
			SendSystemMsg( player, SystemMessage(L"sys", L"Msg_Duel_Already") );
			this->sendDuelRequestFail();
			return;
		}

		Vector3 pos = ownerAttrPos->pos;

		if (theSpawnSystem.GetRandomPostion( pos, 0, 100, sector ) == false)
		{
			// 결투 깃발 소환을 할 수 없는 장소이다
			SendSystemMsg( player, SystemMessage(L"sys", L"Msg_Duel_Invalid_Place") );
			this->sendDuelRequestFail();
			return;
		}

		SpawnSystem::BaseSpawnInfo baseSpawnInfo;
		baseSpawnInfo.npcIndex = duelSystem.objectId;
		baseSpawnInfo.sector = sector;
		baseSpawnInfo.spawnPos = pos;

		if (false == theSpawnSystem.SpawnGeneralNpc( &baseSpawnInfo, &flagNpc ))
		{
			// 결투 깃발 소환을 할 수 없는 장소이다
			SendSystemMsg( player, SystemMessage(L"sys", L"Msg_Duel_Invalid_Place") );
			this->sendDuelRequestFail();
			return;
		}

		ActionNpcOperate* actObject = flagNpc->GetAction< ActionNpcOperate >();
		if (nullptr == actObject)
		{
			sector->RemoveEntity( flagNpc );
			return;
		}

		if (actObject->SetFlagObject( player->GetId() ) == false)
		{
			sector->RemoveEntity( flagNpc );
			return;
		}

		if (actObject->SetFlagObject( target->GetId() ) == false)
		{
			sector->RemoveEntity( flagNpc );
			return;
		}

		if (actPlayer->SetDuelFlagNpc( flagNpc ) == false)
		{
			actPlayer->ResetDuelFlagNpc();
			sector->RemoveEntity( flagNpc );
			return;
		}

		if (targetActPlayer->SetDuelFlagNpc( flagNpc ) == false)
		{
			actPlayer->ResetDuelFlagNpc();
			targetActPlayer->ResetDuelFlagNpc();
			sector->RemoveEntity( flagNpc );
			return;
		}
	}
}

void StatePlayerBase::onNtfGameDuelMatch( EventPtr& e )
{
	ENtfGameDuelMatch* ntf = static_cast<ENtfGameDuelMatch*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	auto actPlayer = owner->GetAction< ActionPlayer >();

	ActionSector* actSector = GetEntityAction( owner );
	VERIFY_RETURN(actSector, )

	EntityNpc* flagObject = actPlayer->GetDuelFlagNpc();
	if (flagObject == nullptr)
	{
		return;
	}

	State* state = flagObject->GetHsm().GetCurrent();
	if (STATE_NPC_DUEL_REQUEST != state->GetId())
	{
		return;
	}

	StateNpcDuelRequest* npcState = static_cast<StateNpcDuelRequest*>(state);
	if (ntf->isSubmit == true)
	{
		SystemMessage sysMsg( L"sys", L"Msg_Duel_Start" );

		auto actObject = flagObject->GetAction< ActionNpcOperate >();
		const DuelPlayerList& duelPlayerList = actObject->GetDuelPlayers();
		for (auto &it : duelPlayerList)
		{
			EntityPlayer* player = actSector->GetPlayerInMySector( it );
			if (nullptr != player)
			{
				SendSystemMsg( player, sysMsg );
			}
		}

		npcState->Submit();
	}
	else
	{
		auto attrPlayer = owner->GetAttribute< AttributePlayer >();

		ENtfGameDuelCancel* ntfDuelCancel = NEW ENtfGameDuelCancel;
		EventPtr eventPtr( ntfDuelCancel );

		SystemMessage sysMsg( L"sys", L"Msg_Duel_Request_Cancel" );
		sysMsg.SetParam( L"str", attrPlayer->charName );

		auto actObject = flagObject->GetAction< ActionNpcOperate >();
		const DuelPlayerList& duelPlayerList = actObject->GetDuelPlayers();
		for (auto &it : duelPlayerList)
		{
			EntityPlayer* player = actSector->GetPlayerInMySector( it );
			if (nullptr == player || owner == player)
			{
				continue;
			}

			SendSystemMsg( player, sysMsg );

			SERVER.SendToClient( player, eventPtr );
		}

		npcState->Cancel();
	}
}

void StatePlayerBase::sendDuelRequestFail()
{
	EntityPlayer* owner = GetOwnerPlayer();

	EResGameDuelRequest* res = NEW EResGameDuelRequest;
	res->eError = ErrorGame::E_FAILED;
	SERVER.SendToClient( owner, EventPtr( res ) );
}

void StatePlayerBase::onReqGetDailyLoginRewardResult(EventPtr& e)
{
	EReqGetDailyLoginRewardResult* req = static_cast<EReqGetDailyLoginRewardResult*>(e.RawPtr());
	
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ItemDailyLoginRewardPushTransaction* trans = NEW ItemDailyLoginRewardPushTransaction(__FUNCTION__, __LINE__, owner, LogCode::E_ITEM_DAILY_LOGIN_REWARD, req->selectDay, req->charid, req->classtype);
	TransactionPtr transPtr(trans);
	{
		for (const auto& i : req->dailyLoginRewardItemInfos)
		{
			ItemData itemdata(i.indexItem, i.itemCount);
			trans->Push(itemdata);
		}

		VERIFY_DO(	TransactionSystem::Register(transPtr),

					EResGetDailyLoginRewardResult* res = NEW EResGetDailyLoginRewardResult;
					res->result = EResGetDailyLoginRewardResult::RESULT_SYSTEM_FAIL;
					res->accountguid = owner->GetAccountId();
					res->day = req->selectDay;
					res->charid = req->charid;
					res->classtype = req->classtype;
					owner->SendToClient(EventPtr(res));
					return;);

	}

}

void StatePlayerBase::onReqGameGetAchievementRewardItem(EventPtr& e)
{
	EReqGameGetAchievementRewardItem* req = static_cast<EReqGameGetAchievementRewardItem*>(e.RawPtr());
	VALID_RETURN(req->achievementRewardItemInfos.size(), );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	AchievementRewardItemInfoExs successRewardItemInfoExs;
	Slots freeSlots;

	// 실패 시 결과 전송 함수 정의
	auto sendResult = [&](AchievementRewardResult resultType)
	{
		EResGameGetAchievementRewardItem* res = NEW EResGameGetAchievementRewardItem;
		res->result = resultType;
		SERVER.SendToClient(owner, EventPtr(res));

		successRewardItemInfoExs.clear();
	};
	
	
	owner->GetInventoryAction().GetFreeSlots(InvenBags, freeSlots, static_cast<UInt32>(req->achievementRewardItemInfos.size()));
	if (0 == freeSlots.size())
	{
		sendResult(AchievementRewardResult::E_FULL_INVEN_SLOT);
		return;
	}

	ItemBag bag(*owner);
	UInt32 checkCount = 0;
	AchievementRewardItemInfoEx rewardEx;
	for (const auto& rewardInfo : req->achievementRewardItemInfos)
	{
		rewardEx.achievementRewardItemInfo = rewardInfo;
#ifdef	__Patch_Change_Achievement_Reward_Item_Count_Formant_by_robinhwp_2019_04_08
		rewardEx.achievementRewardItemInfo.itemInfos = rewardInfo.itemInfos;
#else	__Patch_Change_Achievement_Reward_Item_Count_Formant_by_robinhwp_2019_04_08
		rewardEx.itemRewardCnt = 1;
#endif	__Patch_Change_Achievement_Reward_Item_Count_Formant_by_robinhwp_2019_04_08

		if (checkCount == static_cast<UInt32>(freeSlots.size()))
		{
			// 아이템 수량이 들어갔는데 종류별로 몇개인지를 체크해야하나 모르겠다.
			break;
		}

		if (false == owner->GetAchievementAction().CheckAchievementReward(rewardEx.achievementRewardItemInfo))
		{
			sendResult(AchievementRewardResult::E_NOT_COMPLETE_ACHIVEMENT_GRADE);
			return;
		}

		if (false == owner->GetAchievementAction().IsGetableAchievementReward(rewardEx.achievementRewardItemInfo))
		{
			sendResult(AchievementRewardResult::E_NOT_COMPLETE_ACHIVEMENT_GRADE);
			return;
		}

#ifdef	__Patch_Change_Achievement_Reward_Item_Count_Formant_by_robinhwp_2019_04_08
		for (auto& itemInfo : rewardEx.achievementRewardItemInfo.itemInfos)
		{
			if (false == bag.Insert(ItemData(itemInfo.itemIndex, itemInfo.itemCount)))
			{
				sendResult(AchievementRewardResult::E_SYSTEM_ERROR);
				return;
			}
		}
#else	__Patch_Change_Achievement_Reward_Item_Count_Formant_by_robinhwp_2019_04_08
		if (false == bag.Insert(ItemData(rewardEx.achievementRewardItemInfo.itemIndex, rewardEx.itemRewardCnt)))
		{
			sendResult(AchievementRewardResult::E_SYSTEM_ERROR);
			return;
		}
#endif	__Patch_Change_Achievement_Reward_Item_Count_Formant_by_robinhwp_2019_04_08

		successRewardItemInfoExs.push_back(rewardEx);
		++checkCount;
	}

	AchievementRewardResult result = (freeSlots.size() < req->achievementRewardItemInfos.size())
		? AchievementRewardResult::E_FULL_INVEN_SLOT : AchievementRewardResult::SUCCESS;

	TransactionPtr transPtr = TransactionPtr(NEW ItemAchievementRewardPushTransaction(__FUNCTION__, __LINE__, owner, LogCode::E_ITEM_ACHIEVEMENT_REWARD, successRewardItemInfoExs, result));
	if (false == bag.Write(transPtr))
	{
		sendResult(AchievementRewardResult::E_SYSTEM_ERROR);
		return;
	}

	if (false == TransactionSystem::Register(transPtr))
	{
		sendResult(AchievementRewardResult::E_SYSTEM_ERROR);
		return;
	}

	
	
}

void StatePlayerBase::onReqGameQuickReviveToPartyMember(EventPtr& e)
{
	EReqQuickReviveToPartyMember* req = static_cast<EReqQuickReviveToPartyMember*>(e.RawPtr());

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	Sector* sector = player->GetSector();
	VERIFY_RETURN(sector, );

	VectorPlayer vecPlayer;
	player->GetPartyAction().GetPartyPlayersInMySector(vecPlayer);

	for (EntityPlayer* target : vecPlayer)
	{
		VALID_DO(target->GetId() == req->partyMemberEntityId, continue);
		
		ReviveErrorType::Error eError = theReviveSystem.RequestReviveToTarget(player, target);
		theReviveSystem.SendSystemMessage(player, eError);		
		return;		
	}

	// 파티원이 없다.
}

void StatePlayerBase::onResDbSaveChangeCharMoney( EventPtr& e )
{
	EResDbSaveChangeCharMoney* res = static_cast< EResDbSaveChangeCharMoney* >( e.RawPtr() );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(),  );

	// db에 돈이 업데이트 되지 않았기 때문에 메모리도 이전상태로 되돌린다.
	if (res->result != ErrorDB::SUCCESS)
	{		
		if (res->changedMoney.addSub == ChangedMoney::NONE)
		{
			MU2_ASSERT( false );
			return;
		}

		ChangedMoney::CalcType addSub = res->changedMoney.addSub == ChangedMoney::ADD ? ChangedMoney::SUB : ChangedMoney::ADD;

		if (res->changedMoney.moneyType == ChangedMoney::ZEN)
		{
			if (addSub == ChangedMoney::ADD)
			{
				if (!owner->AddMoney( res->changedMoney.money, false ))
				{
					MU2_ASSERT( false );
					MU2_FATAL_LOG( LogCategory::CONTENTS, L" onResDbSaveChangeCharMoney()> add money failed [charId:%d] ( %I64d )",
						res->charId, res->changedMoney.money );
					return;
				}

				owner->SendChangeMoney( res->changedMoney.money, ChangedMoney::ADD );
			}
			else if (addSub == ChangedMoney::SUB)
			{
				if (!owner->SubMoney( res->changedMoney.money, false ))
				{
					MU2_ASSERT( false );
					MU2_FATAL_LOG( LogCategory::CONTENTS, L" onResDbSaveChangeCharMoney()> add money failed [charId:%d] ( %I64d )",
						res->charId, res->changedMoney.money );
					return;
				}

				owner->SendChangeMoney( res->changedMoney.money, ChangedMoney::SUB );
			}
		}
		else if (res->changedMoney.moneyType == ChangedMoney::GREENZEN)
		{
			if (addSub == ChangedMoney::ADD)
			{
				if (!owner->AddGreenZen( res->changedMoney.money, false ))
				{
					MU2_ASSERT( false );
					MU2_FATAL_LOG( LogCategory::CONTENTS, L" onResDbSaveChangeCharMoney()> add money failed [charId:%d] ( %I64d )",
						res->charId, res->changedMoney.money );
					return;
				}

				owner->SendChangeGreenZen( res->changedMoney.money, ChangedMoney::ADD );
			}
			else if (addSub == ChangedMoney::SUB)
			{
				if (!owner->SubGreenZen( res->changedMoney.money, false ))
				{
					MU2_ASSERT( false );
					MU2_FATAL_LOG( LogCategory::CONTENTS, L" onResDbSaveChangeCharMoney()> add money failed [charId:%d] ( %I64d )",
						res->charId, res->changedMoney.money );
					return;
				}

				owner->SendChangeGreenZen( res->changedMoney.money, ChangedMoney::SUB );
			}
		}
		
	}
}

//void StatePlayerBase::onNtfPartyMemberKickoutInDungeon(EventPtr& e)
//{
//	EntityPlayer* owner = GetOwnerPlayer();
//	MU2_ASSERT(NULL != owner);
//
//	auto actSector = owner->GetAction< ActionSector >();
//	Sector* sector = actSector->GetSector();
//
//	InstanceSectorType::Enum type = sector->GetInstanceType();
//
//	if (true == InstanceSectorType::IsCanPartyKickOutType(type))
//	{
//		ActionPlayerPortal* actPortal = GetEntityAction(owner);
//		actPortal->WarpBeginPosition();
//	}
//}

void StatePlayerBase::onNtfUseMegaphoneItem(EventPtr& e)
{
	ENtfUseMegaphoneItem* req = static_cast< ENtfUseMegaphoneItem* >(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner, );

	ActionPlayerUseItem* actionUseItem = GetEntityAction(owner);
	VERIFY_RETURN(actionUseItem, );

	ErrorItem::Error eError = actionUseItem->UseMegaphoneItem( req->itemPos, req->msg, req->languageCode, req->IconType );
	if (ErrorItem::SUCCESS != eError)
	{
		if (ErrorItem::E_REFER_SYSTEM_MSG == eError)
		{
			SendSystemMsg(owner, SystemMessage(L"sys", actionUseItem->GetLastSystemMsg()));
		}
		else
		{
			EResUseMegaphoneItem* res = NEW EResUseMegaphoneItem;
			res->result = static_cast< UInt32 >(eError);
			SERVER.SendToClient(owner, EventPtr(res));
		}
	}
}

void StatePlayerBase::onReqExchangeItemOpenUI( EventPtr& e )
{
	//EReqExchangeItemOpenUI* req = static_cast< EReqExchangeItemOpenUI* >( e.RawPtr() );
	Tran(EntityState::STATE_PLAYER_EXCHANGE );
}

void StatePlayerBase::onEwzNtfExchangeItemSold( EventPtr& e )
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );	
	owner->GetExchangeShopInfo().OnEwzNtfExchangeItemSold(e);
}

void StatePlayerBase::onNtfWorldDailyMission(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ENtfWorldDailyMission* ntf = static_cast<ENtfWorldDailyMission*>(e.RawPtr());
	
	owner->GetDailyMissionAction().SetupWorldExtractDailyMission(ntf->missionId, ntf->divisionDate, ntf->missionIndexs, true);
	
}

void StatePlayerBase::onNtfWorldChangeDailyMission(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ENtfWorldChangeDailyMission* ntf = static_cast<ENtfWorldChangeDailyMission*>(e.RawPtr());

	owner->GetDailyMissionAction().SetupWorldExtractDailyMission(ntf->missionId, ntf->divisionDate, ntf->missionIndexs, false);
	
}
void StatePlayerBase::onReqDailyMission(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );
	
	owner->GetDailyMissionAction().RequestDailyMission();
}
void StatePlayerBase::onResDbGetCharDailyMission(EventPtr& e)
{
	EResDbGetCharDailyMission* res = static_cast<EResDbGetCharDailyMission*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );
	
	owner->GetDailyMissionAction().SetupDbDailyMission(res->missionId, res->dailyMissionDBInfoVector);
}

void StatePlayerBase::onReqGetDailyMissionReward(EventPtr& e)
{
	EReqGetDailyMissionReward* req = static_cast<EReqGetDailyMissionReward*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ErrorDailyMission::Error eError = owner->GetDailyMissionAction().RequestReward(req->dailyMissionIndex, req->missionNo);

	if (eError != ErrorDailyMission::SUCCESS)
	{
		EResGetDailyMissionReward* res = NEW EResGetDailyMissionReward;
		res->eError = ErrorDailyMission::SUCCESS;
		owner->SendToClient(EventPtr(res));
	}

	
}

void StatePlayerBase::onResDbInsertAchievementReward(EventPtr& e)
{
	EResDbInsertAchievementReward* res = static_cast< EResDbInsertAchievementReward* >(e.RawPtr());
	VERIFY_RETURN(ErrorDB::SUCCESS == res->eError, );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ENtfGameUpdateAchievementRewardInfo* ntf = NEW ENtfGameUpdateAchievementRewardInfo;
	ntf->achievementRewardItemInfo = res->insertRewardItemInfo;
	SERVER.SendToClient(owner, EventPtr(ntf));
}

void StatePlayerBase::onResDbInsertAchievementGradeReward(EventPtr& e)
{
	EResDbInsertAchievementGradeReward* res = static_cast< EResDbInsertAchievementGradeReward* >(e.RawPtr());
	VERIFY_RETURN(ErrorDB::SUCCESS == res->eError, );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );	

	ENtfGameUpdateAchievementRewardInfo* ntf = NEW ENtfGameUpdateAchievementRewardInfo;
	ntf->achievementRewardItemInfo = res->insertRewardItemInfo;
	SERVER.SendToClient(owner, EventPtr(ntf));
}

void StatePlayerBase::onReqGameStartEmergencyEscape(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	owner->GetEscapeAction().StartEscapeWaitTimer();
}

void StatePlayerBase::onReqGameEmergencyEscape(EventPtr& e)
{
	EReqGameEmergencyEscape* req = static_cast<EReqGameEmergencyEscape*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );
		
	VERIFY_RETURN(owner->GetEscapeAction().IsEscapeable(), );

	Vector3 escapePosition;
	if (false == owner->GetEscapeAction().GetEmergencyEscapePosition(req->escapePositionIndex, escapePosition))
	{
		SendSystemMsg(owner, SystemMessage(L"sys", L"Msg_Escape_Err_Position"));
		return;
	}

	if (false == owner->GetSectorAction().Place(escapePosition))
	{
		SendSystemMsg(owner, SystemMessage(L"sys", L"Msg_Escape_Err_Position"));
		return;
	}

	if (true == owner->IsDead())
	{
		// 죽었을 경우에는 부활처리 먼저 한다.
		auto actAbility = owner->GetAction<ActionAbility>();
		actAbility->SetPercentHp(10);

		theReviveSystem.ProcessReviveResult(GameReviveType::InPlaceRevive, owner);
	}

	ENtfGameForceChangePos* ntf = NEW ENtfGameForceChangePos;
	ntf->entityId = owner->GetId();
	ntf->stopPosition = escapePosition;
	ntf->dir = 0;
	owner->GetSectorAction().NearGridCast(EventPtr(ntf));

	owner->GetEscapeAction().ResetEscapeable();
}

void StatePlayerBase::onNtfDebugMsg(EventPtr& e)
{
	ELoopbackDebugMsg *ntf = static_cast<ELoopbackDebugMsg*>(e.RawPtr());
	EntityPlayer* owner = GetOwnerPlayer();

	EResGameDebugMsg* res = NEW EResGameDebugMsg;
	res->msg = ntf->msg;
	SERVER.SendToClient(owner, EventPtr(res));
}

// 부활 정보 요청
void StatePlayerBase::OnReqRevivePreInfo(EventPtr& e)
{
	EReqRevivePreInfo* req = static_cast<EReqRevivePreInfo*>(e.RawPtr());

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	theReviveSystem.RequestReviveInfo(player, req->reviveType);
}

void StatePlayerBase::OnReqRaidApplyRaid( EventPtr& e )
{
	EReqRaidApplyRaid* req = static_cast<EReqRaidApplyRaid*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	req->combatPowerSettedByZone = owner->GetCombatPower();
	SERVER.SendToOnlyWorldServer( e );
}

void StatePlayerBase::onReqWShopItemJobBeginZone(EventPtr& e)
{
	EReqWShopItemJobBeginZone* req = static_cast<EReqWShopItemJobBeginZone*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );
		
	if (false == owner->GetInventoryAction().BeginPickUpItemFromWShop(req->dbJob))
	{
		EResWShopItemJobBeginZone* res = NEW EResWShopItemJobBeginZone;
		res->jobNo = req->dbJob.jobNo;
		res->result = ErrorWShop::E_FAIL_TO_CREATE_ITEM_ZONE;
		res->jobType = req->dbJob.jobType;
		res->mopupUseInfos = req->dbJob.mopupUseInfos;
		SERVER.SendToClient(owner, EventPtr(res));
	}
}

void StatePlayerBase::onResDbWShopPickUpItemBeginTransaction(EventPtr& e)
{
	EResDbWShopPickUpItemBeginTransaction* dbRes = static_cast<EResDbWShopPickUpItemBeginTransaction*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	EResWShopItemJobBeginZone* res = NEW EResWShopItemJobBeginZone;
	res->jobNo = dbRes->jobNo;
	res->result = ErrorWShop::SUCCESS;
	res->fcsAccountId = dbRes->fcsAccountId;
	res->jobType = dbRes->dbJobType;
	res->transId = dbRes->transId;
	res->addr = dbRes->addr;

	ActionPlayerInventory* actPlayerInventory = GetEntityAction(owner);
	if (actPlayerInventory != nullptr)
	{
		for (auto& gameItem : dbRes->gameItems)
		{
			const ItemConstPtr item = actPlayerInventory->GetItem(gameItem.GetPos());
			if (item != nullptr)
			{
				gameItem.updated = true;
				res->updateItemDatas.push_back(*item);
			}
			else
			{
				gameItem.updated = false;
			}
			res->gameItems.push_back(std::move(gameItem));
		}
	}

	SERVER.SendToClient(owner, EventPtr(res));
}

void StatePlayerBase::onReqWShopItemJobEndZone(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	EReqWShopItemJobEndZone* req = static_cast<EReqWShopItemJobEndZone*>(e.RawPtr());

	if (false == owner->GetInventoryAction().EndPickUpItemFromWShop(static_cast<ItemTran>(req->transId), req->addr, req->fcsAccountId, req->jobNo, req->gameItems, req->confirm, req->updateItemDatas, req->money))
	{
		EResWShopItemJobEndZone* res = NEW EResWShopItemJobEndZone;
		res->result = ErrorWShop::E_FAIL_TO_CREATE_ITEM_ZONE;
		res->fcsAccountId = req->fcsAccountId;
		res->jobNo = req->jobNo;
		res->jobType = req->jobType;
		res->confirm = req->confirm;
		SERVER.SendToClient(owner, EventPtr(res));
	}
}

void StatePlayerBase::onRwzNtfWShopUpdateInventoryInquiry(EventPtr& e)
{
	// player 정보에 업데이트만 시키면 된다.
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	EwzNtfWShopUpdateInventoryInquiry* ntf = static_cast<EwzNtfWShopUpdateInventoryInquiry*>(e.RawPtr());
	
	owner->GetPlayerAttr().wShopInventoryLastCheckTime = ntf->wShopInventoryLastCheckTime;
}


void StatePlayerBase::onReqWShopMopupItemJobBeginZone( EventPtr& e )
{

}

void StatePlayerBase::onResDbWShopMopupBeginTransaction( EventPtr& e )
{

}

void StatePlayerBase::onReqWShopMopupItemJobEndZone( EventPtr& e )
{

}

void StatePlayerBase::onReqRankingStoneBlessToZone(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

#ifdef __Reincarnation__jason_180628__
	ErrorRankingStone::Error eError = owner->GetCharSoulInfo().OnReqRankingStoneBlessToZone(e);
	EResRankingStoneBlessToZone* res = NEW EResRankingStoneBlessToZone;
	res->result = eError;
	owner->SendToClient(EventPtr(res));
#else
	EReqRankingStoneBlessToZone* req = static_cast<EReqRankingStoneBlessToZone*>(e.RawPtr());
	req;

	EResRankingStoneBlessToZone* res = NEW EResRankingStoneBlessToZone;
	EventPtr packetPtr(res);

	if (owner->GetSoulSkillManageAction().IsMaxSoulLevelAndSoulExp() == true)
	{
		res->result = ErrorRankingStone::E_ERROR_MAXIMUM_SOUL_LEVEL;
		SERVER.SendToClient(owner, packetPtr);
		return;
	}

	const SoulLevelInfoElem* soulLevelInfoElem = SCRIPTS.GetSoulLevelInfoElem(owner->GetRawSoulLevel());
	if (soulLevelInfoElem == nullptr)
	{
		res->result = ErrorRankingStone::E_ERROR;
		SERVER.SendToClient(owner, packetPtr);
		return;
	}
		
	RankingBless rankingBless;
	if (SCRIPTS.GetRankingBlessNearrestSoulLevel(owner->GetRawSoulLevel(), rankingBless) == false)
	{
		res->result = ErrorRankingStone::E_FAILED;
		SERVER.SendToClient(owner, packetPtr);
		return;
	}	
	
	if (false == owner->IsEnoughZen(rankingBless.payZenAmount) )
	{
		res->result = ErrorRankingStone::E_ERROR_LACK_ZEN;
		SERVER.SendToClient(owner, packetPtr);
		return;
	}

	owner->SubMoney(rankingBless.payZenAmount);
	owner->SendChangeMoney(rankingBless.payZenAmount, ChangedMoney::SUB);
	SoulExp addExp = static_cast<SoulExp>(soulLevelInfoElem->soulExp * rankingBless.soulExpPercent / DEFAULT_RATE);
	owner->GetSoulSkillManageAction().AddExp(addExp);


	EventPtr eventLog = EventPtr( NEW EReqLogDb2 );
	EReqLogDb2* log = static_cast<EReqLogDb2*>(eventLog.RawPtr());
	EventSetter::FillLogForEntity( LogCode::E_RANKING_STONE_BLESS, owner, log->action );

	log->action.var64s.push_back( owner->GetZen() );
	log->action.var64s.push_back( rankingBless.payZenAmount );

	SendDataCenter( eventLog );

	res->result = ErrorRankingStone::SUCCESS;
	SERVER.SendToClient(owner, packetPtr);

#endif
		
}

void StatePlayerBase::onReqItemIdentity( EventPtr& e )
{
	EReqItemIdentity* req = static_cast<EReqItemIdentity*>(e.RawPtr());
	req;

	EResItemIdentity* res = NEW EResItemIdentity;
	EventPtr packetPtr( res );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ActionPlayerInventory *actInven = GetEntityAction( owner );

	res->result = actInven->ItemIdentity( req->targetItem );

	if (res->result != ErrorItem::SUCCESS)
	{
		switch (res->result)
		{
		case ErrorItem::E_NOT_ENOUGH_GREEN_ZEN:
			SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Item_Err_Enough_Greenzen" ) );
			break;
		case ErrorItem::E_NOT_ENOUGH_MONEY:
			SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Item_Enough_Money" ) );
			break;
		default:
			SERVER.SendToClient( owner, packetPtr );
		}

	}

}

void StatePlayerBase::onReqCreateAmplifactionStone( EventPtr& e )
{
	EReqCreateAmplifcationStone *req = static_cast<EReqCreateAmplifcationStone*>(e.RawPtr());
	req;

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ActionPlayerInventory *actInven = GetEntityAction( owner );

	ErrorItem::Error eError = actInven->CreateAmplifactionStone(req->dominionType, req->slot, req->productionIndex);

	if (eError != ErrorGame::SUCCESS)
	{
		EResGetAmplifcationStoneList * res = NEW EResGetAmplifcationStoneList;
		EventPtr packetPtr( res );
		res->eError = eError;
		SERVER.SendToClient( owner, packetPtr );
	}
}

void StatePlayerBase::onReqAccelerationAmplifactionStone( EventPtr& e )
{
	EReqAccelerationAmplifcationStone *req = static_cast<EReqAccelerationAmplifcationStone*>(e.RawPtr());
	req;

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ActionPlayerInventory *actInven = GetEntityAction( owner );

	ErrorItem::Error eError = actInven->AccelerationAmplifactionStone( req->dominionType, req->slot );

	if (eError != ErrorGame::SUCCESS)
	{
		EResAccelerationAmplifcationStone * res = NEW EResAccelerationAmplifcationStone;
		EventPtr packetPtr( res );

		res->result = eError;
		CreateAmplificationDbInfo info;
		actInven->GetAmplifactionStone( req->dominionType, req->slot, info );
		res->info = info;

		SERVER.SendToClient( owner, packetPtr );
	}
}

void StatePlayerBase::onReqGetAmplifactionStoneList( EventPtr& e )
{
	EReqGetAmplifcationStoneList *req = static_cast<EReqGetAmplifcationStoneList*>(e.RawPtr());
	req;
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ActionPlayerInventory *actInven = GetEntityAction( owner );

	ErrorItem::Error eError = actInven->GetAmplifactionStoneList();

	if (eError != ErrorGame::SUCCESS)
	{
		EResGetAmplifcationStoneList *res = NEW EResGetAmplifcationStoneList;
		EventPtr packetPtr( res );

		res->eError = eError;

		SERVER.SendToClient( owner, packetPtr );
	}
}

void StatePlayerBase::onUpgradeTranscedStone( EventPtr& e )
{
	EReqUpgradeTranscendStone *req = static_cast<EReqUpgradeTranscendStone*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ActionPlayerInventory *actInven = GetEntityAction( owner );

	ErrorItem::Error eError = actInven->UpgradeTranscendStone( req->sourceItem, req->targetItem );

	if (eError != ErrorGame::SUCCESS)
	{
		EResUpgradeTranscendStone * res = NEW EResUpgradeTranscendStone;
		EventPtr packetPtr( res );
		res->result = eError;
		SERVER.SendToClient( owner, packetPtr );
	}
}

void StatePlayerBase::onReqReceiveAmplifactionStone( EventPtr& e )
{	
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	EReqReciveAmplifcationStone *req = static_cast<EReqReciveAmplifcationStone*>(e.RawPtr());

	ErrorItem::Error eError = owner->GetInventoryAction().ReceiveAmplifactionStone( req->dominionType, req->slot );

	if (eError != ErrorGame::SUCCESS)
	{
		EResReciveAmplifcationStone * res = NEW EResReciveAmplifcationStone;
		EventPtr packetPtr( res );
		res->result = eError;

		CreateAmplificationDbInfo info;
		owner->GetInventoryAction().GetAmplifactionStone( req->dominionType, req->slot, info );
		res->info = info;

		SERVER.SendToClient( owner, packetPtr );
	}
}

void StatePlayerBase::onReqTrascendStoneRemoveFailCount( EventPtr& e )
{
	EReqRemoveTrascendStoneFailCount * req = static_cast<EReqRemoveTrascendStoneFailCount*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ActionPlayerUseItem *actUseItem = GetEntityAction( owner );

	ErrorItem::Error eError = actUseItem->UseTrascendStoneRemoveFailCount( req->sourceItem, req->targetItem );

	if (eError != ErrorGame::SUCCESS)
	{
		EResRemoveTrascendStoneFailCount * res = NEW EResRemoveTrascendStoneFailCount;
		EventPtr packetPtr( res );
		res->result = eError;

		SERVER.SendToClient( owner, packetPtr );
	}
}

void StatePlayerBase::onReqResetTrascendStone( EventPtr& e )
{
	EReqResetTrascendStone *req = static_cast<EReqResetTrascendStone*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ActionPlayerUseItem *actUseItem = GetEntityAction( owner );
	ErrorItem::Error eError = actUseItem->UseResetTrascendStone( req->sourceItem, req->targetItem );

	if (eError != ErrorGame::SUCCESS)
	{
		EResRemoveTrascendStoneFailCount * res = NEW EResRemoveTrascendStoneFailCount;
		EventPtr packetPtr( res );
		res->result = eError;

		SERVER.SendToClient( owner, packetPtr );
	}
}
void StatePlayerBase::onReqItemWeaponFusion( EventPtr& e )
{
	EReqItemWeaponFusion * req = static_cast<EReqItemWeaponFusion*>(e.RawPtr());
	req;
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ActionPlayerInventory *actInven = GetEntityAction( owner );

	ErrorItem::Error eError = actInven->WeaponFusion( req->materiallist, req->keepEnchant, req->wiseStoneCount );

	if (eError != ErrorGame::SUCCESS)
	{
		EResItemWeaponFusion * res = NEW EResItemWeaponFusion;
		EventPtr packetPtr( res );
		res->result = eError;

		SERVER.SendToClient( owner, packetPtr );
	}
}

void StatePlayerBase::onReqShopDailyBuyList( EventPtr& e )
{
	EReqDailyBuyList *req = static_cast<EReqDailyBuyList*>(e.RawPtr());
	req;
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ActionPlayerInventory *actInven = GetEntityAction( owner );

	actInven->GetDailyBuyList( req->npcIndex);
}

void StatePlayerBase::onReqEquipTranscendStone( EventPtr& e )
{
	EReqEquipTranscendStone * req = static_cast<EReqEquipTranscendStone*>(e.RawPtr());
	
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN( owner && owner->IsValid(), );

	ErrorItem::Error eError = owner->GetUseItemAction().UseEquipTranscendStone( req->sourceItem, req->targetItem, req->equipSlot );

	if (eError != ErrorGame::SUCCESS)
	{
		EResEquipTranscendStone * res = NEW EResEquipTranscendStone;
		res->result = eError;
		owner->SendToClient( EventPtr(res) );
	}
}

void StatePlayerBase::onReqUseMembershipServiceItem(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	EReqUseMembershipServiceItem* req = static_cast<EReqUseMembershipServiceItem*>(e.RawPtr());

	ActionPlayerUseItem *actUseItem = GetEntityAction(owner);

	ErrorItem::Error eErrorItem = actUseItem->UseMembershipItem(req->pos); 

	if (eErrorItem != ErrorItem::SUCCESS)
	{
		EResUseMembershipServiceItem* res = NEW EResUseMembershipServiceItem;
		res->eError = eErrorItem;
		SERVER.SendToClient(owner, EventPtr(res));
	}
}

void StatePlayerBase::onReqGameMembershipDailyReward(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	EReqGameMembershipDailyReward* req = static_cast<EReqGameMembershipDailyReward*>(e.RawPtr());

	ActionPlayerMembership* actMembership = GetEntityAction(owner);

	ErrorMembership::Error eError = actMembership->RequestMembershipDailyReward(req->eBenefitType);

	if (eError != ErrorMembership::SUCCESS)
	{
		EResGameMembershipDailyReward* res = NEW EResGameMembershipDailyReward;
		res->eError = eError;
		SERVER.SendToClient(owner, EventPtr(res));
	}
}

void StatePlayerBase::onReqMembershipActionFlag(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	EReqMembershipActionFlag* req = static_cast<EReqMembershipActionFlag*>(e.RawPtr());

	ActionPlayerMembership* actMembership = GetEntityAction(owner);

	
	EResMembershipActionFlag* res = NEW EResMembershipActionFlag;
	res->eBenefitType = req->eBenefitType;
	res->flag = actMembership->FlagBenefitType(req->eBenefitType);
	SERVER.SendToClient(owner, EventPtr(res));
}

void StatePlayerBase::onReqCharacterSlotExtend( EventPtr& e )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), );

	EReqCharacterSlotExtend* req = static_cast<EReqCharacterSlotExtend*>(e.RawPtr());

	ErrorItem::Error eError = player->GetUseItemAction().UseCharacterSlotExtend( req->sourceItem );

	if (eError != ErrorItem::SUCCESS)
	{
		MU2_WARN_LOG(LogCategory::DEBUG_INVENTORY, "onReqCharacterSlotExtend> player(%s), error(%u)", player->ToString().c_str(), eError);

		EResCharacterSlotExtend* res = NEW EResCharacterSlotExtend;
		res->result = eError;
		SERVER.SendToClient( player, EventPtr( res ) );
	}
}

void StatePlayerBase::onNtfResetMembership(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );


	EwzNtfResetMembership* ntf = static_cast<EwzNtfResetMembership*>(e.RawPtr());

	ActionPlayerMembership* actMembership = GetEntityAction(owner);
	if (ntf->resetService)
	{
		actMembership->Setup(MembershipInfo());
	}

	if (ntf->resetBenefits)
	{
		actMembership->Dev_ResetDailyBenefits();
	}
}


void StatePlayerBase::onNtfUpdatePcRoomInfo(EventPtr& e)
{
	EwzNtfUpdatePcRoomInfo* ntf = static_cast<EwzNtfUpdatePcRoomInfo*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );
		
	owner->SetPcRoomInfo(ntf->billingGuid, ntf->pcRoomKeepInfo);
	owner->GetMembershipAction().SendMembershipSettingInfo(true);
}

void StatePlayerBase::onResDbInsertEventItem(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	EResDbInsertEventItem* dbRes = static_cast<EResDbInsertEventItem*>(e.RawPtr());

	if (dbRes->eError != ErrorDB::SUCCESS)
	{
		theGameMsg.SendGameDebugMsg(owner, L"insert TlbEventItem failed.!");
	}
}

void StatePlayerBase::onReqEventItemPage(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	EReqEventItemPage* req = static_cast<EReqEventItemPage*>(e.RawPtr());

	EReqDbLoadEventItemPage* dbReq = NEW EReqDbLoadEventItemPage;
	dbReq->charId = owner->GetCharId();
	dbReq->rowPerPage = req->rowPerPage;
	dbReq->pageNumber = req->pageNumber;
	SERVER.SendToDb(owner, EventPtr(dbReq));
}

void StatePlayerBase::onResDbLoadEventItemPage(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	EResDbLoadEventItemPage* dbRes = static_cast<EResDbLoadEventItemPage*>(e.RawPtr());

	EResEventItemPage* res = NEW EResEventItemPage;
	res->pageNumber = dbRes->pageNumber;

	if (dbRes->eError == ErrorDB::SUCCESS)
	{
		ActionPlayerEventInventory* actEventInventory = GetEntityAction(owner);
		if (actEventInventory->Replace(dbRes->eventInvenItems))
		{
			res->totalEventItemCount = dbRes->totalItemCount;
			res->eventInvenItems = dbRes->eventInvenItems;
		}
	}

	SERVER.SendToClient(owner, EventPtr(res));
}

void StatePlayerBase::onReqEventItemPickUp(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );	

	EReqEventItemPickUp* req = static_cast<EReqEventItemPickUp*>(e.RawPtr());
	
	ErrorItem::Error eError = owner->GetEventInventoryAction().PickUp(req->eventItemIndexes);
	
	// 성공한 경우 트랜젝션이 완료되면 응답 패킷을 보내준다.
	if (eError != ErrorItem::SUCCESS)
	{
		EResEventItemPickUp* res = NEW EResEventItemPickUp;
		res->error = eError;
		SERVER.SendToClient(owner, EventPtr(res));
	}
}

void StatePlayerBase::onResDbGetNewEventItemCount(EventPtr& e)
{
	EResDbGetNewEventItemCount* dbRes = static_cast<EResDbGetNewEventItemCount*>(e.RawPtr());

	if (dbRes->newCount > 0)
	{
		ENtfEventItemNewArrival* ntf = NEW ENtfEventItemNewArrival;
		ntf->newItemCount = dbRes->newCount;
		SERVER.SendToClient(GetOwnerPlayer(), EventPtr(ntf));
	}
}

void StatePlayerBase::onResDbUpdateSoulSkill(EventPtr& e)
{
	EResDbUpdateSoulSkill* dbRes = static_cast<EResDbUpdateSoulSkill*>(e.RawPtr());

	if (dbRes->eError != ErrorDB::SUCCESS)
	{
		MU2_WARN_LOG(LogCategory::CONTENTS, L"StatePlayerBase::onResDbUpdateSoulSkill> request failed. event = %s", e->ToString().c_str());
	}
}

void StatePlayerBase::onResDbUpdateArtifact(EventPtr& e)
{
	EResDbUpdateArtifact* dbRes = static_cast<EResDbUpdateArtifact*>(e.RawPtr());

	if (dbRes->eError != ErrorDB::SUCCESS)
	{
		MU2_WARN_LOG(LogCategory::CONTENTS, L"StatePlayerBase::onResDbUpdateArtifact> request failed. event = %s", e->ToString().c_str());
	}
}

void StatePlayerBase::onResDbAddCharTitleInfo(EventPtr& e)
{
	EResDbAddCharTitleInfo* dbRes = static_cast<EResDbAddCharTitleInfo*>(e.RawPtr());

	if (dbRes->eError != ErrorDB::SUCCESS)
	{
		MU2_WARN_LOG(LogCategory::CONTENTS, L"StatePlayerBase::onResDbAddCharTitleInfo> request failed. event = %s", e->ToString().c_str());
	}
}



void StatePlayerBase::onResInsertExploredPortal(EventPtr& e)
{
	EResInsertExploredPortal* dbRes = static_cast<EResInsertExploredPortal*>(e.RawPtr());

	if (dbRes->eError != ErrorDB::SUCCESS)
	{
		MU2_WARN_LOG(LogCategory::CONTENTS, L"StatePlayerBase::onResInsertExploredPortal> request failed. event = %s", e->ToString().c_str());
	}
}

void StatePlayerBase::onReqJoinKnightageDominion( EventPtr& e )
{
	EReqJoinKnightageDominion *req = static_cast<EReqJoinKnightageDominion*>(e.RawPtr());
	req;

	EntityPlayer* player = GetOwnerPlayer();
	if (nullptr == player)
		return;

	ActionSector* actSector = GetEntityAction(player);
	Sector* sector = actSector->GetSector();
	
	if (actSector->GetSector()->GetDominionId())
	{
		SendSystemMsg(player, SystemMessage( L"sys", L"Msg_Dominion_Move_Fail_0" ) );
		return;
	}

	if (InstanceSectorType::IsFieldOrTown( sector->GetInstanceType() ) == FALSE)
	{
		SendSystemMsg( player, SystemMessage( L"sys", L"Msg_Dominion_Enter_Fail_0" ) );
		return;
	}

	if (sector->GetFieldPointSector() && sector->GetFieldPointSector()->IsEnable())
	{
		SendSystemMsg( player, SystemMessage( L"sys", L"Msg_Dominion_Enter_Fail_0" ) );
		return;
	}

	PortalUseItemList itemList;

	const DominionInfoElem *elem = SCRIPTS.GetDominionInfo( req->dominionIndex );
	if (elem == nullptr)
	{
		EResJoinKnightageDominion *res = NEW EResJoinKnightageDominion;
		res->result = ErrorGame::E_PORTAL_SCRIPT_NOT_FOUND;
		SERVER.SendToClient(player, EventPtr( res ) );
		return;
	}

	ActionPlayerPortal* actPortal = GetEntityAction(player);
	VALID_RETURN(actPortal, );

	EzwReqChangeMap* reqToWorld = NEW EzwReqChangeMap;		
	reqToWorld->toIndexPositionMovable = elem->dominionPlainZonePos;	
	reqToWorld->dominionId = req->dominionIndex;
	reqToWorld->logNpcPortalIndex = 0;
	SERVER.SendChangemapToWorld(player, EzwReqChangeMap::onReqJoinKnightageDominion, EventPtr( reqToWorld ) );
}

void StatePlayerBase::onNtfZoneFollowerChanged( EventPtr& e )
{
	ENtfZoneFollowerChanged* ntf = static_cast<ENtfZoneFollowerChanged*>(e.RawPtr());
	ntf;

	EntityPlayer* owner = GetOwnerPlayer();
	VALID_RETURN(owner && owner->IsValid(), );

	owner->GetPlayerAttr().followerKnightageId = ntf->knighageId;
}

void StatePlayerBase::onReqBlackMarketItemBuyList(EventPtr& e)
{
	EReqBlackMarketItemBuyList* req = static_cast<EReqBlackMarketItemBuyList*>(e.RawPtr());
	VALID_RETURN(req->npcId, );

	EntityPlayer* owner = GetOwnerPlayer();
	VALID_RETURN(owner && owner->IsValid(), );

	EntityNpc* npc = owner->GetSectorAction().GetNpcInMySector(req->npcId);
	VALID_RETURN(npc && npc->IsValid() && npc->HasView(VIEW_SHOP), );
	
	auto viewShop = npc->GetView<ViewShop>();
	ErrorItem::Error eError = viewShop->CanUse(owner);
	VERIFY_RETURN(eError == ErrorItem::SUCCESS, );

	npc->GetBlackMarketAction().SendBlackMarketItemList(owner);

}

void StatePlayerBase::onReqAutoHackQuestionAnswer(EventPtr& e)
{
	EReqAutoHackQuestionAnswer* req = static_cast<EReqAutoHackQuestionAnswer*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VALID_RETURN(owner && owner->IsValid(), );

	owner->GetAutoHackCheckerAction().ReqAutoDefenderResult(req->answerNo);
	
}


void StatePlayerBase::onReqChangeAutoHackQuestion(EventPtr& e)
{
	e;
	GetOwnerPlayer()->GetAutoHackCheckerAction().ReqChangeAutoHackQuestion();

}

void StatePlayerBase::onNtfDevResetAutoHackPenalty(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VALID_RETURN(owner && owner->IsValid(), );

	owner->GetAutoHackCheckerAction().DevPenaltyReset();
}


void StatePlayerBase::onNtfDevOpenPenaltyPopup(EventPtr& e)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	player->GetAutoHackCheckerAction().GM_OpenPenaltyPopup();
}

void StatePlayerBase::onReqSeasonInfo( EventPtr& e )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	player->GetSeasonAction().ReqSeasonInfo();
}

void StatePlayerBase::onReqSeasonMissionInfo( EventPtr& e )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	player->GetSeasonAction().ReqSeasonMissionInfo();
}

void StatePlayerBase::onReqSeasonMissionDetailInfo( EventPtr& e )
{
	EReqSeasonMissionDetailInfo* req = static_cast<EReqSeasonMissionDetailInfo*>( e.RawPtr() );

	EntityPlayer* owner = GetOwnerPlayer();
	VALID_RETURN(owner && owner->IsValid(), );

	owner->GetSeasonAction().ReqSeasonMissionDetailInfo( req->seasonMissionCategory, req->seasonMissionGroup );
}
#ifdef SEASON_RENEWAL_by_jjangmo_180710
#else
void StatePlayerBase::onReqSeasonTierInfo( EventPtr& e )
{
	EntityPlayer* owner = GetOwnerPlayer();
	VALID_RETURN(owner && owner->IsValid(), );

	ActionPlayerMissionMapTier* actionTier = GetEntityAction( owner );

	const TierInfo* tierInfo = actionTier->GetTierInfo( TierType::COLOSSEUM33_PVP );
	VALID_RETURN(tierInfo, );
	EResSeasonTierInfo* res = NEW EResSeasonTierInfo;
	EventPtr resPtr( res );
	res->tierInfo = *tierInfo;
	res->isParticipation = tierInfo->winCount > 0 || tierInfo->loseCount > 0;

	SERVER.SendToClient( owner, resPtr );
}
#endif
void StatePlayerBase::onReqSeasonRewardInfo( EventPtr& e )
{
	EntityPlayer* owner = GetOwnerPlayer();
	VALID_RETURN(owner && owner->IsValid(), );

	owner->GetSeasonAction().ReqReward();
}

void StatePlayerBase::onResDbSeasonMissionRewardAdd( EventPtr& e )
{
	EntityPlayer* owner = GetOwnerPlayer();
	VALID_RETURN(owner && owner->IsValid(), );

	owner->GetSeasonAction().ResDbSeasonMissionReward(e);
}

void StatePlayerBase::oReqGetExchangeEventInfo( EventPtr& e )
{
	EReqGetExchangeEventInfo *req = static_cast<EReqGetExchangeEventInfo*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VALID_RETURN(owner && owner->IsValid(), );

	ActionPlayerEvent *actEvent = GetEntityAction( owner );
	VERIFY_RETURN(actEvent, );

	ErrorItem::Error error = actEvent->GetExchangeEventInfo( req->eventIndex );

	if (error != ErrorItem::SUCCESS)
	{
		EResExchangeEventItem* res = NEW EResExchangeEventItem;
		res->result = error;
		SERVER.SendToClient( owner, EventPtr( res ) );
	}
}

void StatePlayerBase::oReqGetExchangePuzzleEventInfo(EventPtr& e)
{
	EReqGetExchangePuzzleEventInfo*req = static_cast<EReqGetExchangePuzzleEventInfo*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VALID_RETURN(owner && owner->IsValid(), );

	ActionPlayerEvent *actEvent = GetEntityAction(owner);
	VERIFY_RETURN(actEvent, );

	ErrorPuzzleEvent::Error result =  actEvent->GetPuzzleEventInfo(req->eventIndex);
	if (result != ErrorPuzzleEvent::SUCCESS)
	{
		actEvent->DevPuzzleMsg(result);

		EResGetExchangePuzzleEventInfo *res = NEW EResGetExchangePuzzleEventInfo;
		res->result = result;
		SERVER.SendToClient(owner, EventPtr(res));
	}
}

void StatePlayerBase::onReqExchangeEventItem( EventPtr& e )
{
	EReqExchangeEventItem *req = static_cast<EReqExchangeEventItem*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VALID_RETURN(owner && owner->IsValid(), );

	ActionPlayerEvent *actEvent = GetEntityAction( owner );
	VERIFY_RETURN(actEvent, );

	ErrorItem::Error error = actEvent->ExchangeEventItem( req->eventIndex, req->exchangeEventIndex,req->count );

	if (error != ErrorItem::SUCCESS)
	{
		EResExchangeEventItem* res = NEW EResExchangeEventItem;
		res->result = error;
		SERVER.SendToClient( owner, EventPtr( res ) );
	}
}

void StatePlayerBase::onReqGetStampEventInfo( EventPtr& e )
{
	EReqGetStampEventInfo *req = static_cast<EReqGetStampEventInfo*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VALID_RETURN(owner && owner->IsValid(), );

	ActionPlayerEvent *actEvent = GetEntityAction( owner );
	VERIFY_RETURN(actEvent, );

	ErrorItem::Error error = actEvent->GetStampEventInfo( req->eventIndex );

	if (error != ErrorItem::SUCCESS)
	{
		EResExchangeEventItem* res = NEW EResExchangeEventItem;
		res->result = error;
		SERVER.SendToClient( owner, EventPtr( res ) );
	}
}

void StatePlayerBase::onReqStampPickUpItem( EventPtr& e )
{
	EReqStampPickUpItem *req = static_cast<EReqStampPickUpItem*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VALID_RETURN(owner && owner->IsValid(), );

	ActionPlayerEvent *actEvent = GetEntityAction( owner );
	VERIFY_RETURN(actEvent, );

	ErrorItem::Error error = actEvent->StampPickUpItem( req->eventIndex, req->stampEventIndex, req->stampStep );

	if (error != ErrorItem::SUCCESS)
	{
		EResStampPickUpItem* res = NEW EResStampPickUpItem;
		res->result = error;
		SERVER.SendToClient( owner, EventPtr( res ) );
	}
}

void StatePlayerBase::onReqStampPickUpItemByPuzzleEvent(EventPtr& e)
{
	EReqStampPickUpItemByPuzzleEvent*req = static_cast<EReqStampPickUpItemByPuzzleEvent*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VALID_RETURN(owner && owner->IsValid(), );

	ActionPlayerEvent *actEvent = GetEntityAction(owner);
	VERIFY_RETURN(actEvent, );

	ErrorPuzzleEvent::Error result = actEvent->StampPickUpItemByPuzzleEvent(req->eventIndex, req->stampNo);
	if (result != ErrorPuzzleEvent::SUCCESS)
	{
		actEvent->DevPuzzleMsg(result);
		
		EResStampPickUpItemByPuzzleEvent* res = NEW EResStampPickUpItemByPuzzleEvent;
		res->result = result;
		SERVER.SendToClient(owner, EventPtr(res));
	}
}

void StatePlayerBase::onReqJigsawPuzzleEvent(EventPtr& e)
{
	EReqJigsawPuzzleEvent*req = static_cast<EReqJigsawPuzzleEvent*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VALID_RETURN(owner && owner->IsValid(), );

	ActionPlayerEvent *actEvent = GetEntityAction(owner);
	VERIFY_RETURN(actEvent, );

	ErrorPuzzleEvent::Error result = actEvent->ReqJigsawPuzzle(req->eventIndex,req->puzzleSlot);
	if (result != ErrorPuzzleEvent::SUCCESS)
	{
		actEvent->DevPuzzleMsg(result);

		EResJigsawPuzzleEvent *res = NEW EResJigsawPuzzleEvent;
		res->result = result;
		SERVER.SendToClient(owner, EventPtr(res));
	}
}
void StatePlayerBase::onReqResetPuzzle(EventPtr& e)
{
	EReqResetPuzzle*req = static_cast<EReqResetPuzzle*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VALID_RETURN(owner && owner->IsValid(), );

	ActionPlayerEvent *actEvent = GetEntityAction(owner);
	VERIFY_RETURN(actEvent, );
		
	ErrorPuzzleEvent::Error result = actEvent->ReqResetPuzzle(req->eventIndex, req->backgroundId);
	if (result != ErrorPuzzleEvent::SUCCESS)
	{
		actEvent->DevPuzzleMsg(result);

		EResResetPuzzle *res = NEW EResResetPuzzle;
		res->result = result;
		SERVER.SendToClient(owner, EventPtr(res));
	}
}

void StatePlayerBase::onResDbUpdatePuzzleBackground(EventPtr& e)
{
	EResDbUpdatePuzzleBackground*res = static_cast<EResDbUpdatePuzzleBackground*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VALID_RETURN(owner && owner->IsValid(), );

	ActionPlayerEvent *actEvent = GetEntityAction(owner);
	VERIFY_RETURN(actEvent, );
	actEvent->ResDBResetPuzzleComplete(res->eventIndex, res->backgroundId);
}

void StatePlayerBase::onResDBGetCharPuzzleInfo(EventPtr& e)
{
	EResDbGetCharPuzzleInfo*res = static_cast<EResDbGetCharPuzzleInfo*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VALID_RETURN(owner && owner->IsValid(), );

	ActionPlayerEvent *actEvent = GetEntityAction(owner);
	VERIFY_RETURN(actEvent, );
	PlayerPuzzleInfo playerPuzzleInfo;

	actEvent->LoadPuzzleInfo(res->eventIndex, res->playerPuzzleInfo);
}

void StatePlayerBase::onResDbUpdatePuzzleSlot(EventPtr& e)
{
	EResDbUpdatePuzzleSlot*res = static_cast<EResDbUpdatePuzzleSlot*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VALID_RETURN(owner && owner->IsValid(), );

	ActionPlayerEvent *actEvent = GetEntityAction(owner);
	VERIFY_RETURN(actEvent, );
	res;
}

void StatePlayerBase::onReqWUEventStampInfo(EventPtr& e)
{
	EReqWUEventStampInfo *req = static_cast<EReqWUEventStampInfo*>(e.RawPtr());
	EResWUEventStampInfo * res = NEW EResWUEventStampInfo;

	EntityPlayer* owner = GetOwnerPlayer();
	VALID_RETURN(owner && owner->IsValid(), );

	ActionPlayerEvent *actEvent = GetEntityAction(owner);
	VERIFY_RETURN(actEvent, );

	// Int64 wuCount;		// 통합 이벤트 카운트
	// Int64 accCount;		// 본인의 카운트
	// VecStampEventData stampEvent;
	res->result = actEvent->GetWUEventStampInfo(req->eventIndex, res->accCount, res->stampEvent);

	SERVER.SendToClient(owner, EventPtr(res));
#ifdef __RAY_DEBUG_CODE__
	MU2_INFO_LOG(LogCategory::CONTENTS, "[WAccStamp] E:%d / Value:%d", req->eventIndex, res->accCount);
#endif
}

void StatePlayerBase::onReqWUEventStampPickup(EventPtr& e)
{
	EReqWUEventStampPickup *req = static_cast<EReqWUEventStampPickup*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VALID_RETURN(owner && owner->IsValid(), );

	ActionPlayerEvent *actEvent = GetEntityAction(owner);
	VERIFY_RETURN(actEvent, );

	ErrorItem::Error error = actEvent->UEventStampPickUpItem(req->eventIndex, req->stampStep);
	if (error != ErrorItem::SUCCESS)
	{
		EResWUEventStampPickUp* res = NEW EResWUEventStampPickUp;
		res->result = error;
		SERVER.SendToClient(owner, EventPtr(res));
	}
}


void StatePlayerBase::onResWUAccCountUpdate(EventPtr& e)
{
	//! EResWUAccCountUpdate 패킷은 불완전한 상태로 오므로, 존에서 업데이트하지 않는다.
	EResWUAccCountUpdate * res = static_cast<EResWUAccCountUpdate*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VALID_RETURN(owner && owner->IsValid(), );

	ActionPlayerEvent *actEvent = GetEntityAction(owner);
	VERIFY_RETURN(actEvent, );

	res;
}


void StatePlayerBase::onReqReturnUserReward( EventPtr& e )
{
	EReqReturnUserReward* req = static_cast<EReqReturnUserReward*>(e.RawPtr());
	UNREFERENCED_PARAMETER(req);

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(),  );

	ErrorItem::Error eError = player->GetReturnUserAction().ReqReturnUserReward();

	if (eError == ErrorItem::SUCCESS)
	{
		EResReturnUserReward* res = NEW EResReturnUserReward;
		res->result = eError;
		player->SendToClient(EventPtr(res));
	}
}

void StatePlayerBase::onNtfReturnEvent( EventPtr& e )
{
	e;
	GetOwnerPlayer()->GetReturnUserAction().CheckReturnUserReward();
}


void StatePlayerBase::onReqPrivateStorageTabUnlock(EventPtr& e)
{
	EReqPrivateStorageTabUnlock *req = static_cast<EReqPrivateStorageTabUnlock*>(e.RawPtr());

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	ActionPlayerInventory& actInven = player->GetInventoryAction();

	Inven* foundInven = actInven.GetInven(req->targetInvenType);
	VERIFY_RETURN(foundInven && foundInven->IsStoragePrivate(), );

	InvenStoragePrivate* openableStorage = static_cast<InvenStoragePrivate*>(foundInven);
	VERIFY_RETURN(openableStorage == InvenStoragePrivate::GetNextOpenablePrivateStorage(player), );


	ErrorItem::Error eError = openableStorage->CheckUnlockPrivateStorageTab();
	if (eError == ErrorGame::SUCCESS) 
	{
		eError = openableStorage->UnlockPrivateStorageTab();
	}

	if (eError != ErrorGame::SUCCESS) 
	{
		EResPrivateStorageTabUnlock *res = NEW EResPrivateStorageTabUnlock;
		EventPtr packetPtr(res);
		res->result = eError;

		SERVER.SendToClient(player, packetPtr);
	}
}



void StatePlayerBase::onReqCharPosForCheat( EventPtr& e )
{
	EReqCharPosForCheat *req = static_cast<EReqCharPosForCheat*>( e.RawPtr() );

	EResCharPosForCheat* res = NEW EResCharPosForCheat;
	EventPtr packetPtr( res );

	EntityPlayer* owner = GetOwnerPlayer();
	VALID_RETURN(owner && owner->IsValid(), );

	res->reqCharId = req->reqCharId;
	AttributePosition* attr = GetEntityAttribute( owner );
	VALID_RETURN(attr, );

	res->targetPos = attr->pos;

	SERVER.SendToWorldServer( owner, packetPtr );
}

void StatePlayerBase::onReqEventShopItemBuyList(EventPtr& e)
{
	EReqEventShopItemBuyList* req = static_cast<EReqEventShopItemBuyList*>(e.RawPtr());

	EntityPlayer* ownerPlayer = GetOwnerPlayer();
	VERIFY_RETURN(ownerPlayer && ownerPlayer->IsValid(), );	

	EntityNpc* npc = ownerPlayer->GetSectorAction().GetNpcInMySector(req->npcId);
	VERIFY_RETURN( npc && npc->IsValid() && npc->HasView(VIEW_SHOP), );

	ViewShop* viewShop = npc->GetView<ViewShop>();
	ErrorItem::Error eError = viewShop->CanUse(ownerPlayer);
	VALID_RETURN(ErrorItem::SUCCESS == eError, );

	ownerPlayer->GetInventoryAction().SendEventShopItemBuyList(npc->GetNpcIndex());
}

void StatePlayerBase::onEwzReqPlayerHeartbeat(EventPtr& e)
{
	EwzReqPlayerHeartbeat* req = static_cast<EwzReqPlayerHeartbeat*>(e.RawPtr());

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player, );

	ErrorJoin::Error eError = ErrorJoin::SUCCESS;

	if (false == player->IsValid())
	{
		eError = ErrorJoin::playerNotFound;
	}

	EzwResPlayerHeartbeat* res = Event::AllocEvent<EzwResPlayerHeartbeat>(e);
	EventPtr resPtr(res);
	{
		res->currExecId = req->currExecId;		
		res->eError = eError;
		SERVER.SendToWorldServer(player, resPtr);
	}
	
}

void StatePlayerBase::onReqPSROpen( EventPtr& e )
{
	EReqPSROpen* req = static_cast<EReqPSROpen*>( e.RawPtr() );

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), );

	ActionPlayerEvent* actEvent = GetEntityAction( player );
	VERIFY_RETURN(actEvent, );

	actEvent->ReqPSROpen( req->eventIndex );
}

void StatePlayerBase::onReqPSREvent( EventPtr& e )
{
	EReqPSREvent* req = static_cast<EReqPSREvent*>( e.RawPtr() );

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), );

	ActionPlayerEvent* actEvent = GetEntityAction( player );
	VERIFY_RETURN(actEvent, );

	actEvent->ReqPSREvent( req->eventIndex, req->eventType, req->psrType );
}

void StatePlayerBase::onReqPSRCurInfo( EventPtr& e )
{
	EReqPSRCurInfo* req = static_cast<EReqPSRCurInfo*>( e.RawPtr() );

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), );

	ActionPlayerEvent* actEvent = GetEntityAction( player );
	VERIFY_RETURN(actEvent, );

	actEvent->ReqPSRCurInfo( req->eventIndex );
}

void StatePlayerBase::onReqChangeTalismanDriverSlot(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );
	
	ErrorTalisman::Error eError = owner->GetTalismanAction().OnReqChangeTalismanDriverSlot(e);
	VERIFY_RETURN(eError == ErrorTalisman::SUCCESS, );
}

void StatePlayerBase::onReqChangeTalismanDriverSlotPage(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ErrorTalisman::Error eError = owner->GetTalismanAction().OnReqChangeTalismanDriverSlotPage(e);
	VERIFY_RETURN(eError == ErrorTalisman::SUCCESS, );
}

void StatePlayerBase::onResDbUpdateTalismanDriver(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ErrorTalisman::Error eError = owner->GetTalismanAction().OnResDbUpdateTalismanDriver(e);
	VERIFY_RETURN(eError == ErrorTalisman::SUCCESS, );
}

void StatePlayerBase::onReqBookmarkTalisman(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ErrorTalisman::Error eError = owner->GetTalismanAction().OnReqBookmarkTalisman(e);
	VERIFY_RETURN(eError == ErrorTalisman::SUCCESS, );
}

void StatePlayerBase::onResDbUpdateTalismanBookmark(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ErrorTalisman::Error eError = owner->GetTalismanAction().OnResDbUpdateTalismanBookmark(e);
	VERIFY_RETURN(eError == ErrorTalisman::SUCCESS, );
}

void StatePlayerBase::onResDbDeleteTalismans(EventPtr& e)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );
	
	player->GetTalismanAction().OnResDbDeleteTalismans(e);
}

void StatePlayerBase::onReqRewardTalismanUnit(EventPtr& e)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	EReqRewardTalismanUnit* req = static_cast<EReqRewardTalismanUnit*>(e.RawPtr());

	std::vector<Byte> rewardableIndexList;
	ErrorTalisman::Error eError = player->GetPlayerTalismanUnitAction().GetRewardableIndexList(req->unitIndex, req->missionRewardIndexList, __out rewardableIndexList);
	if (ErrorTalisman::SUCCESS == eError) {
		eError = player->GetPlayerTalismanUnitAction().ReqReward(req->unitIndex, rewardableIndexList);
	}
	
	if (ErrorTalisman::SUCCESS != eError) {
		EResRewardTalismanUnit* res = NEW EResRewardTalismanUnit;
		res->eError = eError;
		SERVER.SendToClient(player, EventPtr(res));
	}
}

#ifdef __Patch_Talisman_V2_Awaken_by_kangms_20181105
void StatePlayerBase::onEczReqTalismanAwakenAction(EventPtr& e)
{
	// 탈리스만 각성 실행
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ErrorTalisman::Error eError = owner->GetTalismanAction().OnReqTalismanAwakenAction(e);
	VERIFY_RETURN(eError == ErrorTalisman::SUCCESS, );
}

#endif//__Patch_Talisman_V2_Awaken_by_kangms_20181105

#ifdef __Patch_Talisman_V2_Growth_by_kangms_20181207
void StatePlayerBase::onEczReqTalismanCoreConversion(EventPtr& e)
{
	// 탈리스만 코어 전환
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ErrorTalisman::Error eError = owner->GetTalismanAction().OnReqTalismanCoreConversion(e);
	VERIFY_RETURN(eError == ErrorTalisman::SUCCESS, );
}

void StatePlayerBase::onEczReqTalismanGrowthReward(EventPtr& e)
{
	// 탈리스만 성장 보상
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ErrorTalisman::Error eError = owner->GetTalismanAction().OnReqTalismanGrowthReward(e);
	VERIFY_RETURN(eError == ErrorTalisman::SUCCESS, );
}

void StatePlayerBase::onResDbTalismanGrowthRewardDeleteAll(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ErrorTalisman::Error eError = owner->GetTalismanAction().OnResDbTalismanGrowthRewardDeleteAll(e);
	VERIFY_RETURN(eError == ErrorTalisman::SUCCESS, );
}

#endif//__Patch_Talisman_V2_Growth_by_kangms_20181207


void StatePlayerBase::onReqEtherExtract(EventPtr& e)
{
	EReqEtherExtract * req = static_cast<EReqEtherExtract *>(e.RawPtr());
	
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	ErrorItem::Error eError = player->GetInventoryAction().EtherExtracts(req->list);

	if(eError != ErrorItem::SUCCESS)
	{
		MU2_WARN_LOG(LogCategory::ITEM, "EtherExtracts failed - player(%s), error(%u)", player->ToString().c_str(), eError);

		EResEtherExtract* res = new EResEtherExtract;
		res->result = eError;
		player->SendToClient(EventPtr(res));
	}
}

void StatePlayerBase::onReqEmotionBoxInfo(EventPtr& e)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	if (false == player->CheckAccountGrade(static_cast<AccountGrade::Enum>(AccountGrade::DEV | AccountGrade::GM)))
	{
		ActionPlayerMembership* actMembership = GetEntityAction(GetOwnerPlayer());
		if (false == actMembership->CanUseRemoteNpcFunction(NpcFunctionType::NPC_FUNCTION_EMOTION_BOX, 0))
		{
			MU2_INFO_LOG(LogCategory::CONTENTS, "[WARN] NPC Function - NPC_FUNCTION_EMOTION_BOX");
			return;
		}
	}

	player->GetInventoryAction().SendEmotionBoxInfo();
}

void StatePlayerBase::onReqEmotionBoxOpen(EventPtr& e)
{
	EReqEmotionBoxOpen * req = static_cast<EReqEmotionBoxOpen *>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	if (false == owner->CheckAccountGrade(static_cast<AccountGrade::Enum>(AccountGrade::DEV | AccountGrade::GM)))
	{
		ActionPlayerMembership* actMembership = GetEntityAction(GetOwnerPlayer());
		if (false == actMembership->CanUseRemoteNpcFunction(NpcFunctionType::NPC_FUNCTION_EMOTION_BOX, 0))
		{
			MU2_INFO_LOG(LogCategory::CONTENTS, "[WARN] NPC Function - NPC_FUNCTION_EMOTION_BOX");
			return;
		}
	}

	ActionPlayerUseItem* actionUseItem = GetEntityAction(owner);
	VERIFY_RETURN(actionUseItem, );

	ErrorItem::Error eErrorItem = actionUseItem->EmotionBoxOpen(req->pos);

	if (ErrorItem::SUCCESS != eErrorItem)
	{
		EResEmotionBoxOpen* res = NEW EResEmotionBoxOpen;

		res->result = eErrorItem;
		res->pos = req->pos;

		SERVER.SendToClient(owner, EventPtr(res));
	}
}

void StatePlayerBase::onReqEmotionBoxRearrange(EventPtr& e)
{
	EReqEmotionBoxRearrange * req = static_cast<EReqEmotionBoxRearrange *>(e.RawPtr());
	req;

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	if (false == owner->CheckAccountGrade(static_cast<AccountGrade::Enum>(AccountGrade::DEV | AccountGrade::GM)))
	{
		ActionPlayerMembership* actMembership = GetEntityAction(GetOwnerPlayer());
		if (false == actMembership->CanUseRemoteNpcFunction(NpcFunctionType::NPC_FUNCTION_EMOTION_BOX, 0))
		{
			MU2_INFO_LOG(LogCategory::CONTENTS, "[WARN] NPC Function - NPC_FUNCTION_EMOTION_BOX");
			return;
		}
	}

	ActionPlayerInventory* actInven = GetEntityAction(GetOwnerPlayer());
	VALID_RETURN(actInven, );

	ErrorItem::Error eErrorItem = actInven->RearrangeEmotionBox();
	if (ErrorItem::SUCCESS != eErrorItem)
	{
		EResEmotionBoxRearrange * res = NEW EResEmotionBoxRearrange;
		EventPtr ptr(res);

		res->result = eErrorItem;
		res->resetCount = actInven->GetEmotionBoxInfo().resetCount;

		SERVER.SendToClient(GetOwnerPlayer(), ptr);
	}
}

void StatePlayerBase::onResEmotionBoxReset(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ActionPlayerInventory * api = GetEntityAction(GetOwnerPlayer());
	VERIFY_RETURN(api, );

	if (false == owner->CheckAccountGrade(static_cast<AccountGrade::Enum>(AccountGrade::DEV | AccountGrade::GM)))
	{
		ActionPlayerMembership* actMembership = GetEntityAction(GetOwnerPlayer());
		if (false == actMembership->CanUseRemoteNpcFunction(NpcFunctionType::NPC_FUNCTION_EMOTION_BOX, 0))
		{
			MU2_INFO_LOG(LogCategory::CONTENTS, "[WARN] NPC Function - NPC_FUNCTION_EMOTION_BOX");
			return;
		}
	}

	EwzResEmotionBoxReset * wzRes = static_cast<EwzResEmotionBoxReset *>(e.RawPtr());

	MU2_INFO_LOG(LogCategory::CONTENTS, "[EMOTION] Recv Reset Result(R : %u / %I64d / %I64d )", wzRes->result, wzRes->fee, wzRes->leftRedzen);

	if (ErrorItem::SUCCESS == wzRes->result)
	{
		//! 리셋하고 결과를 알려준다.
		api->CompleteEmotionBoxReset(wzRes->fee, wzRes->leftRedzen);
	}
	else
	{
		//! 결과를 알려준다.
		EResEmotionBoxRearrange * res = NEW EResEmotionBoxRearrange;
		CharEmotionBoxes &binfo = api->GetEmotionBoxInfo();

		res->resetCount = binfo.resetCount;
		res->emotionBoxes = binfo.emotionBoxes;
		res->result = wzRes->result;

		SERVER.SendToClient(GetOwnerPlayer(), EventPtr(res));
	}
}





void StatePlayerBase::onReqMarbleInfo(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ActionPlayerInventory * api = GetEntityAction(GetOwnerPlayer());
	VERIFY_RETURN(api, );

	api->MarbleSendInfo(MarbleInfoResult::UIOpen);
}

void StatePlayerBase::onReqMarbleThrowDice(EventPtr& e)
{
	e; 
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ActionPlayerInventory * api = GetEntityAction(GetOwnerPlayer());
	VERIFY_RETURN(api, );

	EReqMarbleThrowDice * req = static_cast<EReqMarbleThrowDice *>(e.RawPtr());
#ifdef	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320
	api->MarbleThrowDice(0, req->costPack);
#else	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320
	api->MarbleThrowDice(0, req->useRedzen); 
#endif	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320

}


void StatePlayerBase::onReqMarbleArriveArea(EventPtr& e)
{
	e;
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ActionPlayerInventory * api = GetEntityAction(GetOwnerPlayer());
	VERIFY_RETURN(api, );

	api->MarbleArriveArea(true);
}


void StatePlayerBase::onReqMarbleSelectPos(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	EReqMarbleSelectPos * posReq = static_cast<EReqMarbleSelectPos *>(e.RawPtr());

	ErrorMarbleSystem::Error eError = owner->GetInventoryAction().MarbleSelectPos(posReq->selectSequence);
	if (eError != ErrorMarbleSystem::SUCCESS)
	{
		MU2_WARN_LOG(LogCategory::ITEM, "MarbleSelectPos failed - player(%s), selectSequence(%d), error(%u) ", owner->ToString().c_str(), posReq->selectSequence, eError);
	}
}

void StatePlayerBase::onReqMarbleOpenRewardBox(EventPtr& e)
{
	e;
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ActionPlayerInventory * api = GetEntityAction(GetOwnerPlayer());
	VERIFY_RETURN(api, );

	api->MarbleOpenBoxReq();
}



void StatePlayerBase::onResDicePlayRedzen(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ActionPlayerInventory * api = GetEntityAction(GetOwnerPlayer());
	VERIFY_RETURN(api, );

	EwzResDicePlayRedzen * resultRedzenPlay = static_cast<EwzResDicePlayRedzen *>(e.RawPtr());
#ifdef	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320
	api->MarbleRedzenPlay(resultRedzenPlay->result, resultRedzenPlay->costPack);
#else	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320
	api->MarbleRedzenPlay(resultRedzenPlay->result, resultRedzenPlay->fee);
#endif	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320
}

void StatePlayerBase::onReqChristmasEventInfo(EventPtr& e)
{
	auto reqEventInfo = static_cast<EReqChristmasEventInfo*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VALID_RETURN(owner && owner->IsValid(), );

	ActionPlayerEvent *actionPlayerEvent = GetEntityAction(owner);
	VERIFY_RETURN(actionPlayerEvent, );

	EResChristmasEventInfo* resEventInfo = NEW EResChristmasEventInfo;
	EventPtr resPtr(resEventInfo);

	ActionPlayerContact *actContact = GetEntityAction(GetOwnerPlayer());
	if (actContact == nullptr) {
		resEventInfo->error = ChristmasEventError::E_INVALID_PLAYER;
	}
	if (actContact->HasNpcFunction(NpcFunctionType::NPC_FUNCTION_CHRISTMAS_EVENT) == false) {
		resEventInfo->error = ChristmasEventError::E_NOT_FOUND_NPC;
	}
	if (actContact->CheckNpcDistance() == false) {
		resEventInfo->error = ChristmasEventError::E_OUT_OF_RANGE_NPC;
	}

	if (ChristmasEventError::SUCCESS == resEventInfo->error) {
		ChristmasTreeEventDBInfo eventDbInfo;
		resEventInfo->error = actionPlayerEvent->GetChristmasTreeEvent(reqEventInfo->eventIndex, OUT eventDbInfo);
		if (resEventInfo->error == ChristmasEventError::SUCCESS) {
			resEventInfo->treePoint = eventDbInfo.treePoint;
			resEventInfo->rewardedCount = eventDbInfo.rewardedCount;
			resEventInfo->unrewardCount = eventDbInfo.unrewardCount;
		}
	}

	SERVER.SendToClient(owner, resPtr);
}

void StatePlayerBase::onReqChristmasEventBatteryCharge(EventPtr& e)
{
	auto reqBatteryCharge = static_cast<EReqChristmasEventBatteryCharge*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VALID_RETURN(owner && owner->IsValid(), );

	ActionPlayerEvent *actionPlayerEvent = GetEntityAction(owner);
	VERIFY_RETURN(actionPlayerEvent, );

	ChristmasEventError::Enum error = actionPlayerEvent->reqChristmasTreeCharge(reqBatteryCharge->eventIndex);
	if (error != ChristmasEventError::SUCCESS)
	{
		EResChristmasEventBatteryCharge* resChristmasEventBatteryCharge = NEW EResChristmasEventBatteryCharge;
		resChristmasEventBatteryCharge->error = error;

		SERVER.SendToClient(owner, EventPtr(resChristmasEventBatteryCharge));
	}
}

void StatePlayerBase::onReqChristmasEventTreeReward(EventPtr& e)
{
	auto reqTreeReward = static_cast<EReqChristmasEventTreeReward*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VALID_RETURN(owner && owner->IsValid(), );

	ActionPlayerEvent *actionPlayerEvent = GetEntityAction(owner);
	VERIFY_RETURN(actionPlayerEvent, );

	ChristmasEventError::Enum error = actionPlayerEvent->reqChristmasTreeReward(reqTreeReward->eventIndex);
	if (error != ChristmasEventError::SUCCESS)
	{
		EResChristmasEventTreeReward* resTreeReward = NEW EResChristmasEventTreeReward;
		resTreeReward->error = error;

		SERVER.SendToClient(owner, EventPtr(resTreeReward));
	}
}

void StatePlayerBase::onReqChristmasEventTreePointReset(EventPtr& e)
{
	auto reqTreePointReset = static_cast<EReqChristmasEventTreePointReset*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VALID_RETURN(owner && owner->IsValid(), );

	ActionPlayerEvent *actionPlayerEvent = GetEntityAction(owner);
	VERIFY_RETURN(actionPlayerEvent, );

	ChristmasEventError::Enum error = actionPlayerEvent->reqChristmasTreePointReset(reqTreePointReset->eventIndex);
	if (error != ChristmasEventError::SUCCESS)
	{
		EResChristmasEventTreePointReset* resTreePointReset = NEW EResChristmasEventTreePointReset;
		resTreePointReset->error = error;

		SERVER.SendToClient(owner, EventPtr(resTreePointReset));
	}
}

void StatePlayerBase::onReqBuffEventOpen(EventPtr& e)
{
	auto req = static_cast<EReqBuffEventOpen*>(e.RawPtr());

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	ActionPlayerEvent& actPlayerEvent = player->GetEventAction();
	auto error = actPlayerEvent.ReqBuffEventOpen(req->eventIndex);

	if (BuffEventError::SUCCESS != error)
	{
		auto res = NEW EResBuffEventOpen;
		res->result = error;
		SERVER.SendToClient(player, EventPtr(res));
	}
}

void StatePlayerBase::onReqBuffEventEffect(EventPtr& e)
{
	auto req = static_cast<EReqBuffEventEffect*>(e.RawPtr());

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	ActionPlayerEvent& actPlayerEvent = player->GetEventAction();
	auto error = actPlayerEvent.ReqBuffEventEffect(req->eventIndex, req->grade);

	if (BuffEventError::SUCCESS != error)
	{
		auto res = NEW EResBuffEventEffect;
		res->result = error;
		SERVER.SendToClient(player, EventPtr(res));
	}
}

void StatePlayerBase::onResDbUpdateMakingHistory( EventPtr& e )
{
	EResDbUpdateMakingHistory * res = static_cast<EResDbUpdateMakingHistory*>(e.RawPtr());

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), );

	ActionPlayerMaking& actMaking = player->GetMakingAction();

	actMaking.ResultMakingHistory( res );
}


void StatePlayerBase::onResDbUpdateCharGuideSystem(EventPtr& e)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	EResDbUpdateCharGuideSystem* dbRes = static_cast<EResDbUpdateCharGuideSystem*>(e.RawPtr());

	if (dbRes->GetResult() != ErrorDB::SUCCESS)
	{
		MU2_WARN_LOG(LogCategory::CONTENTS, L"EResDbUpdateCharGuideSystem query result failed - %s, result(%d) : charid(%u), category(%u), division(%d)", 
			dbRes->strQuery.c_str(), dbRes->eError, player->GetCharId(), dbRes->info.guideIndex, dbRes->info.division);
	}
}

void StatePlayerBase::onResDbDeleteCharGuideSystem(EventPtr& e)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	EResDbDeleteCharGuideSystem* dbRes = static_cast<EResDbDeleteCharGuideSystem*>(e.RawPtr());

	if (dbRes->GetResult() != ErrorDB::SUCCESS)
	{
		std::string strIndexes;

		for (auto idx : dbRes->failedIndexes)
		{
			if (strIndexes.length() > 0)
			{
				strIndexes += ", ";
			}
			strIndexes += std::to_string(idx);
		}

		MU2_WARN_LOG(LogCategory::CONTENTS, L"EResDbDeleteCharGuideSystem query result failed - %s, result(%d) : charid(%u), categories(%s)",
			dbRes->strQuery.c_str(), dbRes->eError, player->GetCharId(), strIndexes.c_str());
	}
}

void StatePlayerBase::onReqGameGuideSystemGetReward(EventPtr& e)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	EReqGameGuideSystemGetReward* req = static_cast<EReqGameGuideSystemGetReward*>(e.RawPtr());
	
	ErrorGuideSystem result = player->GetGuideSystemAction().SendReward(req->category, req->division);
	if (result != ErrorGuideSystem::SUCCESS)
	{
		EResGameGuideSystemGetReward* res = new EResGameGuideSystemGetReward;
		res->category = req->category;
		res->division = req->division;
		res->result = result;
		SERVER.SendToClient(player, EventPtr(res));
	}
}

void StatePlayerBase::onReqFreeMopup( EventPtr& e )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), );

	EReqFreeMopup* req = static_cast<EReqFreeMopup*>( e.RawPtr() );

	ErrorMOPUP::Error eError = player->GetInventoryAction().ReqFreeMopup(req);

	if (eError != ErrorMOPUP::SUCCESS )
	{
		EResFreeMopup* res = NEW EResFreeMopup;
		EventPtr ptrRes( res );
		res->mopupList = req->mopupList;
		res->error = eError;
		SERVER.SendToWorldServer( player, ptrRes );
	}

}

ErrorItem::Error StatePlayerBase::giveRewardGameSystemReturnUserTrans()
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::E_FAILED);
		
	const AccountReturnUserElem&  elem = SCRIPTS.GetScript<LoginCheckScript>()->GetAccountReturnUserElem();

	VERIFY_RETURN(player->GetLongestReturnUserDays() >= elem.returnDays, ErrorItem::E_NOT_QUALIFIED_FOR_THE_REWARD);	

	TransactionPtr transPtr(NEW ReturnUserRewardItemPushTransaction(__FUNCTION__, __LINE__, player, LogCode::E_RETURN_USER_REWARD_BY_SYSTEM, elem));
	{
		ItemBag bag(*player);

		VERIFY_RETURN(bag.Insert(ItemData(elem.rewardItemId, elem.rewardItemCount)), ErrorItem::BagInsertFailed);
		VERIFY_RETURN(bag.Write(transPtr), ErrorItem::BagWriteFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::TranRegisterFailed);
	}

	return ErrorItem::SUCCESS;
}

void StatePlayerBase::onReqGameSystemReturnUserGetReward(EventPtr& )
{
	// EReqGameSystemReturnUserGetReward* req = static_cast<EReqGameSystemReturnUserGetReward*>(e.RawPtr());

	ErrorItem::Error result = giveRewardGameSystemReturnUserTrans();
	if (result != ErrorItem::SUCCESS)
	{
		EResGameSystemReturnUserGetReward* res = new EResGameSystemReturnUserGetReward;
		res->result = result;
		SERVER.SendToClient(GetOwnerPlayer(), EventPtr(res));
	}
}

void StatePlayerBase::onResDbExchangeItemRegisteredList(EventPtr& e)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	EResDbExchangeItemRegisteredList* resFromDb = static_cast< EResDbExchangeItemRegisteredList* >(e.RawPtr());
	if (resFromDb->eError != ErrorDB::SUCCESS)
	{
		EResExchangeItemRegisteredList* res = NEW EResExchangeItemRegisteredList;
		res->result = ErrorItem::E_DB_ERROR;
		player->SendToClient(EventPtr(res));
		return;
	}

	ErrorItem::Error eError = player->GetExchangeShopInfo().OnDbRegisteredList(resFromDb->viewList, resFromDb->isNotifyClient);
	if (eError != ErrorItem::SUCCESS)
	{
		EResExchangeItemRegisteredList* res = NEW EResExchangeItemRegisteredList;
		res->result = eError;
		player->SendToClient(EventPtr(res));
		return;
	}
}

void StatePlayerBase::onResDbExchangeItemSoldList(EventPtr& e)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	EResDbExchangeItemSoldList* resFromDb = static_cast< EResDbExchangeItemSoldList* >(e.RawPtr());
	if (resFromDb->eError != ErrorDB::SUCCESS)
	{
		EResExchangeItemSoldList* res = NEW EResExchangeItemSoldList;
		res->result = ErrorItem::E_DB_ERROR;
		player->SendToClient(EventPtr(res));
		return;
	}

	ErrorItem::Error eError = player->GetExchangeShopInfo().OnDbSoldList(resFromDb->soldList, resFromDb->isNotifyClient);
	if (eError != ErrorItem::SUCCESS)
	{
		EResExchangeItemSoldList* res = NEW EResExchangeItemSoldList;
		res->result = eError;
		player->SendToClient(EventPtr(res));
		return;
	}
}

void StatePlayerBase::onReqGameShopSettingOpenOrClase(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	// 클라이언트에서 보내준 플래그로 세팅한다.
	EReqGameSetItemSellFalg* req = static_cast<EReqGameSetItemSellFalg*>(e.RawPtr());

	VERIFY_DO(owner->GetInventoryAction().GetItemSellFlag() != req->flag,	MustErrorCheck);	// 같은 경우 클라이언트에서 잘못 호출한 경우

	owner->GetInventoryAction().SetItemSellFlag(req->flag);

	EResGameSetItemSellFalg* res = new EResGameSetItemSellFalg;
	res->flag = owner->GetInventoryAction().GetItemSellFlag();
	SERVER.SendToClient(GetOwnerPlayer(), EventPtr(res));
}

void StatePlayerBase::onEczReqNoticePopUpInfo(EventPtr& e)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	ActionPlayerNoticePopUp *apNoticePopUp = GetEntityAction(owner);
	VALID_RETURN(apNoticePopUp, );

	apNoticePopUp->ReqNoticePopupInfo();
}

void StatePlayerBase::onEResDbNoticePopUpInfoLoad(EventPtr& e)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	ActionPlayerNoticePopUp *apNoticePopUp = GetEntityAction(player);
	VALID_RETURN(apNoticePopUp, );

	EResDbNoticePopUpInfoLoad* res = static_cast<EResDbNoticePopUpInfoLoad*>(e.RawPtr());
	if (res->eError != ErrorDB::SUCCESS)
	{
		EzcResNoticePopUpInfo* resToClient = NEW EzcResNoticePopUpInfo;
		resToClient->result = static_cast<ErrorNoticePopUp::Error>(res->eError);
		player->SendToClient(EventPtr(resToClient));
		return;
	}

	apNoticePopUp->UpdateNoticePopUpInfo(res->noticePopUpDate);
}

void StatePlayerBase::onEczNtfPetInfoUpdateAll(EventPtr& e)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	ActionPlayerPetManage *apPetManager = GetEntityAction(player);
	VALID_RETURN(apPetManager, );
	
	apPetManager->UpdatePetInfoAll();
}

void StatePlayerBase::onEventTreasureCommon(EventPtr& e)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );	
	player->GetTreasure().OnEvent(e);
}

void StatePlayerBase::onEczItemRuneFusion( EventPtr& e )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), );
	player->GetInventoryAction().GetItemRune().OnReqItemRuneFusion( e );
}

#ifdef ABILITY_RENEWAL_20180508
void StatePlayerBase::onReqOpenBraceletSlot( EventPtr& e )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), );

	EczOpenBraceletSlot* req = static_cast<EczOpenBraceletSlot*>(e.RawPtr());

	ErrorItem::Error error = player->GetUseItemAction().UseBraceletSlotOpen( req->useItem );
	if (error != ErrorItem::SUCCESS)
	{
		EzcOpenBraceletSlot* res = new EzcOpenBraceletSlot;
		res->result = error;
		player->SendToClient( EventPtr( res ) );
	}
}

void StatePlayerBase::onReqUseOpenScroll( EventPtr& e )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), );

	EczUseOptionScroll* req = static_cast<EczUseOptionScroll*>(e.RawPtr());

	ErrorItem::Error error = player->GetUseItemAction().UseOptionScroll( req->useItem, req->targetItem, req->selectAbilityIndex );
	if (error != ErrorItem::SUCCESS)
	{
		EzcUseOptionScroll* res = new EzcUseOptionScroll;
		res->result = error;
		player->SendToClient( EventPtr( res ) );
	}
}

#endif // ABILITY_RENEWAL_20180508

void StatePlayerBase::onEwzReqCheckMatchJoinPopup(EventPtr& e)
{
	EwzReqCheckMatchJoinPopup* req = static_cast<EwzReqCheckMatchJoinPopup*>(e.RawPtr());

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	EzwAckCheckMatchJoinPopup* ack = NEW EzwAckCheckMatchJoinPopup;
	EventPtr eventPtr(ack);

	ack->eDungeonType = req->eDungeonType;
	ack->playerCntPerTeam = req->playerCntPerTeam;
	ack->remainedTime	  = req->remainedTime;

	if (player->IsAlive() == true)
	{
		ack->result = 0;
	}
	else
	{
		ack->result = 1;
	}

	SERVER.SendToWorldServer(player, eventPtr);
	
}

#ifdef TUROTIAL_SKIP_by_cheolhoon_180605
void StatePlayerBase::onReqTutorialSkip(EventPtr& )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	ActionPlayerTutorial* atutorial = GetEntityAction(player);
	VALID_RETURN(atutorial, );

	Sector* sector = player->GetSector();
	VERIFY_RETURN(sector, );
	
	if(false == sector->GetInstanceDungeonInfo().IsTutorial() )
	{
		MU2_WARN_LOG(LogCategory::CONTENTS, L"EczReqTutorialSkip DungeonType is not TUTORIAL, charid(%u), zoneid(%d)", player->GetCharId(), sector->GetIndexZone());
		return;
	}
	
	TutorialState::Enum tutorialState = player->GetTutorialState();
	if (tutorialState != TutorialState::PLAYING)
	{
		MU2_WARN_LOG(LogCategory::CONTENTS, L"EczReqTutorialSkip State is not TUTORIAL::PLAYING, charid(%u), zoneid(%d), state(%d)", player->GetCharId(), sector->GetIndexZone(), tutorialState);
		return;
	}

	UINT32 endPosition = SCRIPTS.GetScript<TutorialSkipScript>()->Get(player->GetClassType());
	
	const PositionElem* positionElem = SCRIPTS.GetPositionElem(endPosition);
	if (NULL == positionElem)
	{
		MU2_WARN_LOG(LogCategory::CONTENTS, L"EczReqTutorialSkip Can not find positionElem, charid(%u), classType(%d), endPosition(%d)", player->GetCharId(), player->GetClassType(), endPosition);
		return;
	}

	EzwReqChangeMap* reqChangeMap = NEW EzwReqChangeMap;	
	reqChangeMap->toIndexPositionMovable = endPosition;

	SERVER.SendChangemapToWorld(player, EzwReqChangeMap::onReqTutorialSkip, EventPtr(reqChangeMap));
}
#endif

#ifdef	__Patch_Limited_Banner_Shop_robinhwp
void StatePlayerBase::onResDBInsertLimitedBannerGoods(EventPtr& e)
{
	GetOwnerPlayer()->GetPlayerLimitedBannerGoodsAction().OnResult(static_cast<EResDBInsertLimitedBannerGoods*>(e.RawPtr()));
}

void StatePlayerBase::onReqBuyLimitedBannerGoods(EventPtr& e)
{
	EReqBuyLimitedBannerGoods* req = static_cast<EReqBuyLimitedBannerGoods*>(e.RawPtr());

#ifdef	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320
	ErrorItem::Error result = GetOwnerPlayer()->GetPlayerLimitedBannerGoodsAction()
		.BuyLimitedBannerGoods(req->bannerIndex, req->costPack);
#else	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320
	ErrorItem::Error result = GetOwnerPlayer()->GetPlayerLimitedBannerGoodsAction()
		.BuyLimitedBannerGoods(req->bannerIndex, req->costType, req->costValue);
#endif	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320

	if (result != ErrorItem::SUCCESS)
	{
		EResBuyLimitedBannerGoods* res = new EResBuyLimitedBannerGoods;
		res->result = result;
		res->bannerIndex = req->bannerIndex;
		GetOwnerPlayer()->SendToClient(EventPtr(res));
	}
}
#endif	__Patch_Limited_Banner_Shop_robinhwp

#ifdef __lockable_item_181001
void StatePlayerBase::onLockableItem( EventPtr& e )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), );

	EczLockableItem *req = static_cast<EczLockableItem*>(e.RawPtr());
	req;

	ErrorItem::Error result = player->GetInventoryAction().UseLockableItem( req->selectItem, req->isLockable );
	if (result != ErrorItem::SUCCESS)
	{
		EzcLockableItem* res = new EzcLockableItem;
		res->result = result;
		player->SendToClient( EventPtr( res ));
	}
}
#endif

#ifdef __Patch_Event_Buff_Add_Benefit_by_jjangmo_20181122
void StatePlayerBase::onReqEventBuffGiftGetReward( EventPtr& e )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), );

	ErrorEventBuff::Error error = player->GetPlayerEventBuffAction().ReqGetGift();
	if ( ErrorEventBuff::SUCCESS != error  )
	{
		EzcResEventBuffGiftGetReward* res = NEW EzcResEventBuffGiftGetReward;
		res->error = error;
		SERVER.SendToClient( GetOwnerPlayer(), EventPtr( res ) );
	}
}
#endif

#ifdef __Patch_Daily_Time_Connection_eunseok_2018_12_03
void StatePlayerBase::onEReqGetDailyTimeConnectionRewardToZone(EventPtr& e)
{
	EReqGetDailyTimeConnectionRewardToZone* req = static_cast<EReqGetDailyTimeConnectionRewardToZone*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );


	ItemDailyTimeConnectionRewardPushTransaction* trans = NEW ItemDailyTimeConnectionRewardPushTransaction(
		__FUNCTION__, __LINE__, owner, LogCode::E_ITEM_DAILY_TIME_CONNECTION_REWARD, 
		req->charId, req->slotNo, req->divisionDate, req->submitStampSlotNo);

	TransactionPtr transPtr(trans);
	{
		for (const auto& i : req->rewardItems)
		{
			ItemData itemdata(i.indexItem, i.itemCount);
			trans->Push(itemdata);
		}


		VERIFY_DO(TransactionSystem::Register(transPtr),

		EResGetDailyTimeConnectionRewardToZone* res = NEW EResGetDailyTimeConnectionRewardToZone;
		res->result		  = EResGetDailyTimeConnectionRewardToZone::RESULT_SYSTEM_FAIL;
		res->charId		  = req->charId;
		res->slotNo		  = req->slotNo;
		res->divisionDate = req->divisionDate;
		res->submitStampSlotNo = 0;
		
		owner->SendToWorld(EventPtr(res));
		return;		);
	}
}
void StatePlayerBase::onEReqGetDailyTimeConnectionStampRewardToZone(EventPtr& e)
{
	EReqGetDailyTimeConnectionStampRewardToZone* req = static_cast<EReqGetDailyTimeConnectionStampRewardToZone*>(e.RawPtr());

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );


	ItemDailyTimeConnectionStampRewardPushTransaction* trans = NEW ItemDailyTimeConnectionStampRewardPushTransaction(
		__FUNCTION__, __LINE__, owner, LogCode::E_ITEM_DAILY_TIME_CONNECTION_STAMP_REWARD,
		req->charId, req->slotNo, req->divisionDate);

	TransactionPtr transPtr(trans);
	{
		for (const auto& i : req->rewardItems)
		{
			ItemData itemdata(i.indexItem, i.itemCount);
			trans->Push(itemdata);
		}

		VERIFY_DO(TransactionSystem::Register(transPtr),
		EResGetDailyTimeConnectionStampRewardToZone* res = NEW EResGetDailyTimeConnectionStampRewardToZone;
		res->result			= EResGetDailyTimeConnectionStampRewardToZone::RESULT_SYSTEM_FAIL;
		res->charId			= req->charId;
		res->slotNo			= req->slotNo;
		res->divisionDate	= req->divisionDate;
		owner->SendToWorld(EventPtr(res));
		return; );
	}
}
#endif //__Patch_Daily_Time_Connection_eunseok_2018_12_03
#ifdef __Patch_Talismanbox_Ticket_Reward_System_by_ch_181224	
void StatePlayerBase::onReqTalismanboxTicketRewardInfo(EventPtr& e)
{
	EReqTalismanboxTicketRewardInfo* req = static_cast<EReqTalismanboxTicketRewardInfo*>(e.RawPtr());

	EntityPlayer* ownerPlayer = GetOwnerPlayer();
	VERIFY_RETURN(ownerPlayer && ownerPlayer->IsValid(), );

	ActionPlayerInventory& invenAction = ownerPlayer->GetInventoryAction();
#ifdef __Patch_Add_Imputed_Talismanbox_Ticket_Reward_by_ch_20190513
	ErrorItem::Error error = invenAction.CheckTalismanboxTicketRewardInfo(req->npcId, req->isImputedRedZen);
#else
	ErrorItem::Error error = invenAction.CheckTalismanboxTicketRewardInfo(req->npcId);
#endif // __Patch_Add_Imputed_Talismanbox_Ticket_Reward_by_ch_20190513
	if(ErrorItem::SUCCESS != error)
	{
		EResTalismanboxTicketRewardInfo* res = NEW EResTalismanboxTicketRewardInfo;
		res->eError = error;		
#ifdef __Patch_Add_Imputed_Talismanbox_Ticket_Reward_by_ch_20190513		
		res->rewardTime = invenAction.GetProvideTalismanboxTicketRewardTime(req->isImputedRedZen);
#else
		res->rewardTime = invenAction.GetProvideTalismanboxTicketRewardTime();
#endif // __Patch_Add_Imputed_Talismanbox_Ticket_Reward_by_ch_20190513
		SERVER.SendToClient(ownerPlayer, EventPtr(res));
	}
}

void StatePlayerBase::onReqProvideTalismanboxTicketReward(EventPtr& e)
{
	EReqProvideTalismanboxTicketReward* req = static_cast<EReqProvideTalismanboxTicketReward*>(e.RawPtr());

	EntityPlayer* ownerPlayer = GetOwnerPlayer();
	VERIFY_RETURN(ownerPlayer && ownerPlayer->IsValid(), );

	// 환급 받았는지 확인.
	ActionPlayerInventory& invenAction = ownerPlayer->GetInventoryAction();
#ifdef __Patch_Add_Imputed_Talismanbox_Ticket_Reward_by_ch_20190513
	ErrorItem::Error error = invenAction.ProvideTalismanboxTicketReward(req->npcId, req->isImputedRedZen);
#else
	ErrorItem::Error error = invenAction.ProvideTalismanboxTicketReward(req->npcId);
#endif // __Patch_Add_Imputed_Talismanbox_Ticket_Reward_by_ch_20190513
	if(ErrorItem::SUCCESS != error)
	{
		EResProvideTalismanboxTicketReward* res = NEW EResProvideTalismanboxTicketReward;
		res->eError = error;
#ifdef __Patch_Add_Imputed_Talismanbox_Ticket_Reward_by_ch_20190513		
		res->rewardTime = invenAction.GetProvideTalismanboxTicketRewardTime(req->isImputedRedZen);
#else
		res->rewardTime = invenAction.GetProvideTalismanboxTicketRewardTime();
#endif // __Patch_Add_Imputed_Talismanbox_Ticket_Reward_by_ch_20190513		
		SERVER.SendToClient(ownerPlayer, EventPtr(res));
	}
}

#ifdef __Patch_Add_Imputed_Talismanbox_Ticket_Reward_by_ch_20190513
void StatePlayerBase::onResDbUspGetTalismanboxTicketRewardInfo(EventPtr& e)
{
	EResDbUspGetTalismanboxTicketRewardInfo* resDB = static_cast<EResDbUspGetTalismanboxTicketRewardInfo*>(e.RawPtr());

	EntityPlayer* ownerPlayer = GetOwnerPlayer();
	VERIFY_RETURN(ownerPlayer && ownerPlayer->IsValid(), );

	ActionPlayerInventory& invenAction = ownerPlayer->GetInventoryAction();

	EResTalismanboxTicketRewardInfo* res = NEW EResTalismanboxTicketRewardInfo;

	// DB에서 읽음 체크.
	invenAction.SetLoadDBTalismanboxTicketRewardTime(true);

	if(resDB->eError == ErrorItem::SUCCESS)
	{
		// DB에 보상기록이 있는 경우.		
		if(TalismanboxTicketRewardCondition::RECIEVE_IMPUTED_REDZEN_REWARD & resDB->rewardFlag)
		{
			invenAction.SetProvideTalismanboxTicketRewardTime(resDB->imputedRewardTime, true);
		}

		if(TalismanboxTicketRewardCondition::RECIEVE_REDZEN_REWARD & resDB->rewardFlag)
		{
			invenAction.SetProvideTalismanboxTicketRewardTime(resDB->rewardTime, false);
		}

		invenAction.SetTalismanboxTicketRewardCondition(static_cast<TalismanboxTicketRewardCondition::Enum>(resDB->rewardFlag));
	}

	if(resDB->imputedRedZen)
	{
		if(TalismanboxTicketRewardCondition::RECIEVE_IMPUTED_REDZEN_REWARD & resDB->rewardFlag)
		{
			res->eError = ErrorItem::AlreadyProvideTalismanboxTicket;
			res->rewardTime = resDB->imputedRewardTime;
			SERVER.SendToClient(ownerPlayer, EventPtr(res));

			return;
		}
	}
	else
	{
		if(TalismanboxTicketRewardCondition::RECIEVE_REDZEN_REWARD & resDB->rewardFlag)
		{
			res->eError = ErrorItem::AlreadyProvideTalismanboxTicket;
			res->rewardTime = resDB->rewardTime;
			SERVER.SendToClient(ownerPlayer, EventPtr(res));

			return;
		}
	}

	// 스크립트 가져와서 대상인지 확인.
	const TaismanboxTicketRewardScript* script = SCRIPTS.GetScript<TaismanboxTicketRewardScript>();
	VERIFY_RETURN(script, );
	const TalismanboxTicketRewardElem* elem = script->Get(ownerPlayer->GetWorldId(), ownerPlayer->GetAccountId(), resDB->imputedRedZen);
	if(elem != nullptr)
	{
		res->eError = ErrorItem::SUCCESS;
		res->talismanBoxIndex1 = elem->talismanbox1Index;
		res->talismanBoxIndex2 = elem->talismanbox2Index;
		res->talismanTicketIndex = elem->totalTicketIndex;
		res->talismanBoxCount1 = elem->talismanbox1Count;
		res->talismanBoxCount2 = elem->talismanbox2Count;
		res->talismanBoxRewardCount1 = elem->talismanbox1TicketCount;
		res->talismanBoxRewardCount2 = elem->talismanbox2TicketCount;
		res->talismanTicketCount = elem->totalTicketCount;
	}
	else
	{
		res->eError = ErrorItem::NotFoundTalismanboxTicketInfo;		
	}

	SERVER.SendToClient(ownerPlayer, EventPtr(res));
}
#else
void StatePlayerBase::onResDbUspGetTalismanboxTicketRewardInfo(EventPtr& e)
{
	EResDbUspGetTalismanboxTicketRewardInfo* resDB = static_cast<EResDbUspGetTalismanboxTicketRewardInfo*>(e.RawPtr());

	EntityPlayer* ownerPlayer = GetOwnerPlayer();
	VERIFY_RETURN(ownerPlayer && ownerPlayer->IsValid(), );

	ActionPlayerInventory& invenAction = ownerPlayer->GetInventoryAction();

	EResTalismanboxTicketRewardInfo* res = NEW EResTalismanboxTicketRewardInfo;

	if(resDB->eError == ErrorItem::SUCCESS)
	{
		// DB에 보상기록이 있는 경우.		
		res->eError = ErrorItem::AlreadyProvideTalismanboxTicket;
		res->rewardTime = resDB->rewardTime;
		invenAction.SetProvideTalismanboxTicketRewardTime(resDB->rewardTime);
		invenAction.SetTalismanboxTicketRewardCondition(TalismanboxTicketRewardCondition::COMPLETE_REWARD);
	}
	else
	{
		// 스크립트 가져와서 대상인지 확인.
		const TaismanboxTicketRewardScript* script = SCRIPTS.GetScript<TaismanboxTicketRewardScript>();
		VERIFY_RETURN(script, );

#ifdef __Patch_Talismanbox_Ticket_Reward_Add_WorldId_by_ch_190405
		const TalismanboxTicketRewardElem* elem = script->Get(ownerPlayer->GetWorldId(), ownerPlayer->GetAccountId());
#else
		const TalismanboxTicketRewardElem* elem = script->Get(ownerPlayer->GetAccountId());
#endif // __Patch_Talismanbox_Ticket_Reward_Add_WorldId_by_ch_190405
		if(elem != nullptr)
		{			
			res->eError = ErrorItem::SUCCESS;
			res->talismanBoxIndex1 = elem->talismanbox1Index;
			res->talismanBoxIndex2 = elem->talismanbox2Index;
			res->talismanTicketIndex = elem->totalTicketIndex;
			res->talismanBoxCount1 = elem->talismanbox1Count;
			res->talismanBoxCount2 = elem->talismanbox2Count;	
			res->talismanBoxRewardCount1 = elem->talismanbox1TicketCount;
			res->talismanBoxRewardCount2 = elem->talismanbox2TicketCount;
			res->talismanTicketCount = elem->totalTicketCount;
			
			invenAction.SetTalismanboxTicketRewardCondition(TalismanboxTicketRewardCondition::NOT_RECIEVE_REWARD);
		}
		else
		{
			res->eError = ErrorItem::NotFoundTalismanboxTicketInfo;			
			invenAction.SetTalismanboxTicketRewardCondition(TalismanboxTicketRewardCondition::NONE);
		}
	}

	SERVER.SendToClient(ownerPlayer, EventPtr(res));
}
#endif // __Patch_Add_Imputed_Talismanbox_Ticket_Reward_by_ch_20190513
#endif

#ifdef __Patch_Mail_HwaYeong_20190117
void StatePlayerBase::onReqGameMailReadAndGetAll( EventPtr& e )
{
	EReqGameMailReadAndGetAll* req = static_cast<EReqGameMailReadAndGetAll*>(e.RawPtr());

	EntityPlayer* ownPlayer = GetOwnerPlayer();
	VERIFY_RETURN( ownPlayer && ownPlayer->IsValid(), );

	ActionPlayerMailBox& actMailBox = ownPlayer->GetMailBoxAction();

	ErrorMail::Error eError = actMailBox.ReadAndGetAll( req->mailId );
	if (eError != ErrorMail::SUCCESS)
	{
		EResGameMailReadAndGetAll* res = NEW EResGameMailReadAndGetAll;
		res->result = eError;
		SERVER.SendToClient( ownPlayer, EventPtr( res ) );
		return;
	}
}
#endif


#ifdef __Patch_Mercler_Secret_Shop_by_ch_2019_04_24
void StatePlayerBase::onReqMerclerSecretShopInfo(EventPtr& e)
{
	UNREFERENCED_PARAMETER(e);
	VERIFY_RETURN(SCRIPTS.IsActivateMerclerSecretShop(), );

	EntityPlayer* player = this->GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	if(SCRIPTS.GetMerclerSecretShopUsableLevel() > player->GetLevel())
	{
		SystemMessage sysMsg = SystemMessage(L"sys", L"Msg_SecretShop_CannotUseShop");
		sysMsg.SetParam(L"str", SCRIPTS.GetMerclerSecretShopUsableLevel());
		SendSystemMsg(player, sysMsg);

		return;
	}

	ActionPlayerInventory& invenAction = player->GetInventoryAction();
	invenAction.CheckAndUpdateMerclerSecretShopInfo(true);

	EResMerclerSecretShopInfo* res = NEW EResMerclerSecretShopInfo;
	ErrorItem::Error eError = ErrorItem::SUCCESS;

	auto sellElem = SCRIPTS.GetScript<MerclerSecretShopSellScript>()->Get();
	if(nullptr != sellElem)
	{
		res->curProductIndex = sellElem->productIndex;
		res->curProductStartTime = sellElem->startTime;
		res->curProductEndTime = sellElem->endTime;
	}
	else
	{
		eError = ErrorItem::itemNotFound;
	}

	auto bonusElem = SCRIPTS.GetScript<MerclerSecretShopBonusScript>()->Get();
	if(nullptr != bonusElem)
	{
		res->curBonusIndex = bonusElem->index;
		res->curBonusStartTime = bonusElem->startTime;
		res->curBonusEndTime = bonusElem->endTime;
	}
	else
	{
		eError = ErrorItem::itemNotFound;
	}

	res->bonusCount = invenAction.GetMerclerSecretShopInfo().bonusCount;
	res->lastBuyProductIndex = invenAction.GetMerclerSecretShopInfo().lastBuyIndex;
	res->eError = eError;

	SERVER.SendToClient(player, EventPtr(res));
}

void StatePlayerBase::onReqBuyMerclerSecretShop(EventPtr& e)
{
	VERIFY_RETURN(SCRIPTS.IsActivateMerclerSecretShop(), );

	EReqBuyMerclerSecretShop* req = static_cast<EReqBuyMerclerSecretShop*>(e.RawPtr());	

	EntityPlayer* player = this->GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );
	VERIFY_RETURN(SCRIPTS.GetMerclerSecretShopUsableLevel() <= player->GetLevel(), );

	ActionPlayerInventory& invenAction = player->GetInventoryAction();
	ErrorItem::Error eError = invenAction.BuyMerclerSecretShop(req->productIndex, req->costPack);
	if(eError != ErrorItem::SUCCESS)
	{
		EResMerclerSecretShopInfo* res = NEW EResMerclerSecretShopInfo;
		res->eError = eError;

		SERVER.SendToClient(player, EventPtr(res));
	}
}
#endif

#ifdef __Patch_SetItem_Renewal_by_kangms_2019_4_10
void StatePlayerBase::onEczReqSetCardOpen(EventPtr& e)
{
	EczReqSetCardOpen* req = static_cast<EczReqSetCardOpen*>(e.RawPtr());

	EntityPlayer* ownerPlayer = GetOwnerPlayer();
	VERIFY_RETURN(ownerPlayer && ownerPlayer->IsValid(), );

	auto actEventProxy = this->GetOwnerPlayer()->GetAction<ActionEventProxy>();

	actEventProxy->BroadcastMessage<void, SetCardIndex>(EVENT_ID::SET_CARD_OPEN, req->reqOpenSetCardIndex);
}

void StatePlayerBase::onEczReqSetCardEquip(EventPtr& e)
{
	EczReqSetCardEquip* req = static_cast<EczReqSetCardEquip*>(e.RawPtr());

	EntityPlayer* ownerPlayer = GetOwnerPlayer();
	VERIFY_RETURN(ownerPlayer && ownerPlayer->IsValid(), );

	auto actEventProxy = this->GetOwnerPlayer()->GetAction<ActionEventProxy>();

	actEventProxy->BroadcastMessage<void, SetCardIndex, SetCardEquipPos::Enum>( EVENT_ID::SET_CARD_CHANGE
		                                                                      , req->sourceSetCardIndex
		                                                                      , static_cast<SetCardEquipPos::Enum>(req->targetPos) );
}

void StatePlayerBase::onResDbSetCardUpdateList(EventPtr& e)
{
	EResDbSetCardUpdateList* res = static_cast<EResDbSetCardUpdateList*>(e.RawPtr());

	EntityPlayer* player = this->GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	auto actPlayerSetCard = player->GetAction<ActionPlayerSetCard>();

	actPlayerSetCard->SetDBProcessing(false);

	if (res->GetResult() != ErrorDB::SUCCESS) {

		EzcResSetCardEquip* resToClient = NEW EzcResSetCardEquip;
		resToClient->eError = ErrorSetCard::E_DB;
		SERVER.SendToClient(player, EventPtr(resToClient));
		return;
	}

	EzcResSetCardEquip* resToClient = NEW EzcResSetCardEquip;

	actPlayerSetCard->SetSetCardEquipPosList( res->updatedSetCardList );

	resToClient->eError = ErrorSetCard::SUCCESS;
	resToClient->updatedSetCardList = res->updatedSetCardList;
	SERVER.SendToClient(player, EventPtr(resToClient));
}

void StatePlayerBase::onResDbSetCardDeleteList(EventPtr& e)
{
	EResDbSetCardDeleteList* res = static_cast<EResDbSetCardDeleteList*>(e.RawPtr());

	EntityPlayer* player = this->GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	auto actPlayerSetCard = player->GetAction<ActionPlayerSetCard>();

	actPlayerSetCard->SetDBProcessing(false);

	if (res->GetResult() != ErrorDB::SUCCESS) {
		return;
	}

	EzcNtfSetCardCloseList* resToClient = NEW EzcNtfSetCardCloseList;

	actPlayerSetCard->CloseSetCardList(res->deletedSetCardIndexList);
	resToClient->closedSetCardList = res->deletedSetCardIndexList;
	SERVER.SendToClient(player, EventPtr(resToClient));
}

#endif//__Patch_SetItem_Renewal_by_kangms_2019_4_10


#ifdef __Patch_SetItem_Renewal_by_kangms_2019_4_10
void StatePlayerBase::onResDbDefaultOpenSetCardListInsert(EventPtr& e)
{
	EResDbDefaultOpenSetCardListInsert* dbRes = static_cast<EResDbDefaultOpenSetCardListInsert*>(e.RawPtr());

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	auto actPlayerSetCard = player->GetAction<ActionPlayerSetCard>();

	for (auto& setCard : dbRes->insertSetCards)
	{
		actPlayerSetCard->SetOpenSetCard(setCard);

		// 클라이언트 전달
		EzcResSetCardOpen* res = NEW EzcResSetCardOpen;
		res->eError = ErrorSetCard::SUCCESS;
		res->setCard = res->setCard;
		SERVER.SendToClient(GetOwnerPlayer(), EventPtr(res));

		// 오픈로그 저장
		EReqLogDb2* log = new EReqLogDb2;
		EventSetter::FillLogForEntity(LogCode::E_SET_CARD_OPEN, player, log->action);
		log->action.var32s.push_back(setCard.GetIndex());
		SendDataCenter(EventPtr(log));
	}
}

#endif//__Patch_SetItem_Renewal_by_kangms_2019_4_10
} // mu2
