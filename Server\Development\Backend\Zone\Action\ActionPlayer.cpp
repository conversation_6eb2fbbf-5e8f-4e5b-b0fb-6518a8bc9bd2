﻿#include "stdafx.h"
#include <Backend/Zone/ZoneServer.h>
#include <Backend/Zone/Action/ActionAchievement.h>
#include <Backend/Zone/Action/ActionPlayer.h>
#include <Backend/Zone/Action/ActionPlayer/ActionSkillManage.h>
#include <Backend/Zone/Action/ActionPlayer/ActionQuickSlot.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerCognition.h>
#include <Backend/Zone/Action/ActionSuppliedSkillEachPortal.h>	
#include <Backend/Zone/Action/ActionTutorial.h>	
#include <Backend/Zone/Action/ActionPlayerScript.h>	
#include <Backend/Zone/Action/ActionParty.h>	
#include <Backend/Zone/Action/ActionNpc/ActionNpcOperate.h>	
#include <Backend/Zone/Action/ActionPlayerMaking.h>	
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerCombatPower.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerSurvey.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerAccountLevel.h>
#include <Backend/Zone/Action/ActionPassivity.h>
#include <Backend/Zone/Action/ActionFieldPoint.h>
#include <Backend/Zone/Action/ActionAutoHackChecker.h>
#include <Backend/Zone/Action/ActionUseItem.h>
#include <Backend/Zone/Action/ActionQuest.h>
#ifndef __Reincarnation__jason_180628__
#include <Backend/Zone/Action/ActionPlayer/ActionSoulSkillManage.h>
#endif
#include <Backend/Zone/System/ReviveSystem.h>
#include <Backend/Zone/System/PortalSystem.h>
#include <Backend/Zone/System/LogicEffectSystem.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerKnightage.h>
#include <Backend/Zone/FieldKnightage/FieldKnightageManager.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerMembership.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerSector.h>
#include <Backend/Zone/World/SectorRunner.h>
#include <Backend/Zone/View/ViewPosition.h>
#include <Backend/Zone/Action/ActionPlayerSpeedHack.h>
#include <Backend/Zone/Action/actionPlayer/ActionPlayerGuideSystem.h>
#include <Backend/Zone/Action/ActionBuff.h>
#include <Backend/Zone/System/NotifierGameContents.h>

#ifdef __Renewal_Battle_IFF_by_kangms_180829
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerCognition.h>
#endif//__Renewal_Battle_IFF_by_kangms_180829
#ifdef __Patch_SetItem_Renewal_by_kangms_2019_4_10
#include <Backend/Zone/Action/ActionEventProxy.h>
#endif __Patch_SetItem_Renewal_by_kangms_2019_4_10

namespace mu2
{

ActionPlayer::ActionPlayer( EntityPlayer* owner )
: ActionAgent(owner)
//, m_joinRunner( nullptr )
, m_flagNpcId()
, m_rechargeObject( nullptr )
, m_HpMpLaskTick( 0 )
, m_syncHp( 0 )
, m_syncMp( 0 )
, m_isRaidType( false )
, m_cheatAllowedMoveDist( 0.f ) 
, m_skillLogTimer(5 * 60 * 1000)
, m_syncLogTimer(1 * 60 * 1000)
, m_knightageRejoinTimer( 60 * 1000)
, m_isChangingMap(false)
, m_checkChangeMapTimer(5 * 1000)
, m_killerUnitId(0)
#ifdef __Patch_Tower_of_dawn_eunseok_2018_11_05
, m_curTowerOfDawnRewardPoint(0)
#endif //__Patch_Tower_of_dawn_eunseok_2018_11_05
#ifdef __Patch_Add_User_Emotion_Quest_Mission_Type_ch_20190117	
, m_emotionStartTick(0)
#endif
{
	CounterInc("ActionPlayer");

	m_AttackSpeedTestSkillId = { 22101, 12001, 42801, 12101, 32101, 42001 };
}

ActionPlayer::~ActionPlayer()
{
	CounterDec("ActionPlayer");
}

//void ActionPlayer::SaveCharPosition()
//{
//	EntityPlayer* player = GetOwnerPlayer();
//	VERIFY_RETURN(player && player->IsValid(), );
//
//	EReqDbSaveCharPosition* req = NEW EReqDbSaveCharPosition;
//	
//	auto attrPlayer = player->GetAttribute<AttributePlayer>();
//	AttributePosition* attrPos = GetEntityAttribute( player );
//		
//	req->charId = attrPlayer->charId;	
//	req->posX = attrPos->pos.x;
//	req->posY = attrPos->pos.y;
//	req->posZ = attrPos->pos.z;
//	req->indexZone = attrPos->GetExecId().GetZoneIndex();
//	SERVER.SendToDb( player, EventPtr( req ) );
//}


/*
	캐릭터 정보를 주기적(30초)으로 저장하는 곳에서, 좌표를 임시로 저장하여
	다음 저장시 변경 여부를 검사한다.
	
	리턴값 : 
			true - 변경되어서 디비에 저장해야한다.
			false - 변경되지 않았다. 디비저장 불필요
*/

Bool ActionPlayer::IsModifiedCharInfo()
{
	EntityPlayer* owner = GetOwnerPlayer();

	AttributePosition* attrPos = GetEntityAttribute(owner);
	VERIFY_RETURN(attrPos, false);

	AttributePlayer* attrPlayer = GetEntityAttribute(owner);
	VERIFY_RETURN(attrPlayer, false);

	AttributeParty* attrParty = GetEntityAttribute(owner);
	VERIFY_RETURN(attrParty, false);

	Bool isDiffer = attrPos->lastSavedPos != attrPos->pos || attrPos->lastSavedIndexZone != attrPos->GetExecId().GetZoneIndex();

	if (false == isDiffer)
	{
		isDiffer = attrPlayer->containerModifiables.IsModified();
	}

	if (false == isDiffer)
	{	
		isDiffer = attrParty->containerModifiables.IsModified();
	}

#ifdef __Reincarnation__jason_180628__
	if (false == isDiffer)
	{
		isDiffer = owner->GetCharSoulInfo().IsModified();
	}
#endif

	if (true == isDiffer)
	{
		attrPos->lastSavedPos = attrPos->pos;
		attrPos->lastSavedIndexZone = attrPos->GetExecId().GetZoneIndex();

		attrPlayer->containerModifiables.ResetModified();
		attrParty->containerModifiables.ResetModified();
#ifdef __Reincarnation__jason_180628__
		owner->GetCharSoulInfo().ResetModified();
#endif

	}
	
	return isDiffer;
}


Bool ActionPlayer::IsUseFullAttackSpeedSkill(IndexSkill skillId )
{
	auto found = std::find_if( m_AttackSpeedTestSkillId.begin(), m_AttackSpeedTestSkillId.end(), 
							[skillId]( const IndexSkill& testSkillId )
							{ 
								return skillId == testSkillId; 
							} );

	VALID_RETURN(found != m_AttackSpeedTestSkillId.end(), false )
	return true;
}

void ActionPlayer::SaveCharSnapAbility()
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	auto actPlayerCognition = player->GetAction<ActionPlayerCognition>();
	auto attrPlayer = player->GetAttribute<AttributePlayer>();


	EReqDbUpdateCharSnapAbility* req = NEW EReqDbUpdateCharSnapAbility;
	req->accountGuid = attrPlayer->accountId;
	req->charId		 = attrPlayer->charId;
	actPlayerCognition->GetDbSaveCharAbility( req->ability );
	SERVER.SendToDb(GetOwnerPlayer(), EventPtr( req ) );
}

void ActionPlayer::SetupCharNames(std::unordered_map< CharId, std::wstring >& mapCharNames)
{
	auto attr = GetOwnerPlayer()->GetAttribute< AttributePlayer >();
	attr->characters = mapCharNames;
}

Bool ActionPlayer::CheckIn(JoinContext::Enum context)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), false);

	ActionPlayerSector& sectorAction = player->GetPlayerSectorAction();	
	AttributePosition& attrPos = player->GetPositionAttr();

	Sector* sector = sectorAction.GetSector();
	if (!sector)
	{
		return false;
	}	

	Vector3 goal;
	if ( !sectorAction.GetValidPosition( attrPos.pos, goal ) )
	{
		thePortalSystem.GetCharPosByNearPortal( attrPos.GetExecId().GetZoneIndex(), attrPos.pos );
	}
	else
	{
		attrPos.pos = goal;
	}

	if (sectorAction.AddAgentToRealworld() == false)
	{
		return false;
	}

	if (!sectorAction.AddToMyGrid())
	{
		return false;
	}

	// MyTeamInfo는 원래 섹터의 Processor Event로 처리를 했었으나,
	// 이미 섹터에 들어간 상태에서 내 정보를 받게되면 다른 플레이어의 팀 정보를 받은 후에 
	// 내 팀 정보를 받게된 후라서 팀 정보가 제대로 되지 않는다.
	if ( sector->GetDungeonType() == DungeonType::DT_ALTAR_OF_ELEMENTS ||
		//sector->GetDungeonType() == DungeonType::DT_DEATH_MATCH_20 ||
		//sector->GetDungeonType() == DungeonType::DT_DEATH_MATCH_40 ||
		sector->GetDungeonType() == DungeonType::DT_TOURNAMENT_ALTAR_OF_ELEMENTS ||
		sector->GetDungeonType() == DungeonType::DT_BATTLEFIELD_IN_THE_SKY ||
		sector->GetDungeonType() == DungeonType::DT_PVP_COLOSSEUM_33||
		sector->GetDungeonType() == DungeonType::PVP_CHAOS_CASTLE)
	{
		ENtfMyTeamInfo* ntf = NEW ENtfMyTeamInfo;
		ntf->myTeamInfo.entityId = player->GetId();		
		ntf->myTeamInfo.eMyTeam = player->GetMyTeam();

		if (player->GetMyTeam() == TeamType::TEAM_A )
		{
			ntf->myTeamInfo.eOtherTeam = TeamType::TEAM_B;
		}
		else if(player->GetMyTeam() == TeamType::TEAM_B)
		{
			ntf->myTeamInfo.eOtherTeam = TeamType::TEAM_A;
		}
		else
		{
			ntf->myTeamInfo.eOtherTeam = TeamType::TEAM_NONE;
		}
		ntf->myTeamInfo.duelId = 0;
		ntf->myTeamInfo.eAppearanceTeam = player->GetMyTeam();

		SERVER.SendToClient( player, EventPtr( ntf ) );
	}


 	sector->AddEntity( player );

	PostAffectContentsBuff(context);

	player->GetSpeedHackAction().StartSyncTick();
	
	sectorAction.NotifyEntitiesInfoInNearGridToSelf();
	sectorAction.NotifySelfInfoToEntitiesInNearGrid();	
	sectorAction.NotifyHeroNpcsInfoToSelf();	
	sectorAction.NotifyMyTeamPositionToSector();
	sectorAction.NotifyMyTeamPositionToSelf();

	ActionPlayerScript* aps = GetEntityAction( player ); 
	aps->Setup( sector );

	ActionPlayerFieldPoint* actionFieldPoint = GetEntityAction(player);
	if (nullptr != actionFieldPoint)
	{
		actionFieldPoint->Setup(sector);
	}

	{
		AttributePlayer* attrPlayer = GetEntityAttribute(player);
		if (attrPlayer)
		{
			EzcNtfGameLoadCoachMark* sendCoachMark = NEW EzcNtfGameLoadCoachMark;
			sendCoachMark->marks = attrPlayer->mapLoadCoachMarks;
			SERVER.SendToClient(player, EventPtr(sendCoachMark));
		}
	}
	m_isRaidType = sectorAction.IsRaidType();

	SyncHpMpInTheSectorToSelf();
	
	return true;
}

Bool ActionPlayer::ReCheckIn()
{
	EntityPlayer* ownerPlayer = GetOwnerPlayer();
	VERIFY_RETURN(ownerPlayer && ownerPlayer->IsValid(), false);

	ActionPlayerSector& sectorAction = ownerPlayer->GetPlayerSectorAction();
	AttributePosition& attrPos = ownerPlayer->GetPositionAttr();

	Sector* sector = sectorAction.GetSector();
	VERIFY_RETURN(sector, false);

	Vector3 goal;
	if ( !sectorAction.GetValidPosition( attrPos.pos, goal ) )
	{
		thePortalSystem.GetCharPosByNearPortal( attrPos.GetExecId().GetZoneIndex(), attrPos.pos );
	}
	else
	{
		attrPos.pos = goal;
	}

	if ( sectorAction.AddAgentToRealworld() == false )
	{
		return false;
	}

	if (!sectorAction.AddToMyGrid())
	{
		return false;
	}
 
 	sector->AddEntity( ownerPlayer );
	PostAffectContentsBuff();
	
	sectorAction.NotifyEntitiesInfoInNearGridToSelf();
	sectorAction.NotifySelfInfoToEntitiesInNearGrid();
	sectorAction.NotifyHeroNpcsInfoToSelf();

	return true;
}

void ActionPlayer::OnTick()
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	AttributePlayer& attrPlayer = player->GetPlayerAttr();

	if (m_skillLogTimer.IsOn())
	{
		ActionPlayerSkillManage * skillmgr = GetEntityAction(player);
		if (skillmgr)
			skillmgr->WriteSkillLog();
	}

	if (m_syncLogTimer.IsOn())
	{
		EReqLogDb2* log = NEW EReqLogDb2;
		EventPtr logEventPtr(log);
		{
			EventSetter::FillLogForEntity(LogCode::E_CHAR_SYNC, GetOwnerPlayer(), log->action);
			log->action.var32s.push_back(static_cast<Int32>(attrPlayer.contributionPoint));	// ray : obt 용 로그 
			log->action.var64s.push_back(attrPlayer.money);
			log->action.var64s.push_back(attrPlayer.greenZen);
			SendDataCenter(logEventPtr);
		}		
	}
	__super::OnTick(); 
}

Bool ActionPlayer::SetupValidPositionBeforeCheckIn( Sector* sector )
{
	VERIFY_RETURN(sector, false);

	EntityPlayer* player = GetOwnerPlayer();
	AttributePosition* attrPos = GetEntityAttribute( player );
	if ( nullptr == attrPos )
	{
		return false;
	}	
	
	ActionSector* sectorAction = GetEntityAction( player );
	if( nullptr == sectorAction )
	{
		return false;
	}	

	Vector3 goal;
	if ( false == sectorAction->GetValidPosition( attrPos->pos, goal ) )
	{
		thePortalSystem.GetCharPosByNearPortal( attrPos->GetExecId().GetZoneIndex(), goal );
	}
	else
	{
		attrPos->pos = goal;
	}	

	return true;
}

Bool ActionPlayer::FindValidLoadPosition( Sector* sector, const Vector3& current, const Int32 footholdIndex, Vector3& out )
{
	if( NULL == sector )
	{
		return false;
	}	

	EntityPlayer* owner = GetOwnerPlayer();
	ActionSector* sectorAction = GetEntityAction( owner );
	if( nullptr == sectorAction )
	{
		return false;
	}		

	if ( sectorAction->GetValidPosition( current, out ) == false )
	{
		// 거점 등록 위치가 현재 존이라면 거점 위치로 이동
		// 아닐 경우 가까운 포탈
		AttributePosition* attrPos = GetEntityAttribute( owner );
		IndexZone indexZone = attrPos->GetExecId().GetZoneIndex();
		if (thePortalSystem.GetFootholdPos(indexZone, footholdIndex, out ) )
		{
			return true;
		}

		return thePortalSystem.GetCharPosByNearPortal(indexZone, out );
	}

	return true;
}

Int32 ActionPlayer::Send( EventPtr& e )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), 0);

	SERVER.Send( player->GetWorldServerSessionKey(), e );

	return 0;
}

//void ActionPlayer::SetupLastState( const UInt32 lastState )
//{
//	getPlayerAttribute()->lastState = lastState;
//}

//void ActionPlayer::SetRunnerId( UInt32 Id )
//{
//	GetOwnerPlayer()->GetPlayerAttr().runnerId = Id;
//	//getEntityAttribute()->runnerId = Id;
//}

#ifdef __CharPositionSave__jason_180622__
#else
void ActionPlayer::SetupLeaveIndexZone(const IndexZone indexZone )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );
	
	AttributePosition* attrPos = GetEntityAttribute( player );	
	VERIFY_RETURN(attrPos->GetExecId().GetZoneIndex() != indexZone, );
	attrPos->SetZoneIndex(indexZone);
}
#endif

//void ActionPlayer::SetupExecutionZoneId( const ExecutionZoneId& execZoneId )
//{
//	AttributePosition* attrPos = getPositionAttribute();
//
//	attrPos->SetExecId(execZoneId);
//}

//UInt16 ActionPlayer::GetIndexZone()
//{
//	const AttributePosition* attrPos = getPositionAttribute();
//	return attrPos->GetExecId().GetZoneIndex();
//}
//
//const ExecutionZoneId& ActionPlayer::GetExecZoneId()
//{
//	const AttributePosition* attrPos = getPositionAttribute();
//	return attrPos->GetExecId();
//}

//void ActionPlayer::SetupLatency( const UInt32 averLatency )
//{
//	getPlayerAttribute()->latencyTick = averLatency;
//}
//
//void ActionPlayer::SetupAttributeWhenDead( const Tick& time )
//{
//	getPlayerAttribute()->upStateTime = time;
//}


//void ActionPlayer::SetupJoinContext(JoinContext::Enum eJoinContext)
//{
//	AttributePlayer* attrPlayer = GetEntityAttribute( GetOwnerPlayer() );
//	attrPlayer->eJoinContext = eJoinContext;
//}

Sector* ActionPlayer::FindJoinSector()
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid() && owner->GetMySectorRunner(), nullptr);

	return owner->GetMySectorRunner()->FindSector( owner->GetCurrExecId());
}

Sector* ActionPlayer::FindLeaveSector( const ExecutionZoneId& executionZoneId)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid() && owner->GetMySectorRunner(), nullptr);

	return owner->GetMySectorRunner()->FindSector(executionZoneId);
}

void ActionPlayer::AddCharacterExp(const CharExp baseExp, const CharExp addedExp)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	CharExp addableExp = baseExp + addedExp;
	VALID_RETURN(addableExp, );
	VALID_RETURN(player->GetLevel() < Limits::MAX_LEVEL, );

	CharLevel oldLevel = player->GetLevel();

	Bool isLevelUp = player->IncExp(addableExp);
	if (isLevelUp)
	{
		player->OnCharLevelUp();
		EReqLogDb2* log = NEW EReqLogDb2;
		EventPtr logEventPtr( log );
		EventSetter::FillLogForEntity( LogCode::E_CHAR_LEVEL_UP, player, log->action );
		SendDataCenter(logEventPtr );
	}

	const LevelInfoElem* currLevelElem = SCRIPTS.GetLevelScript( player->GetLevel() );
	VERIFY_RETURN(currLevelElem, );

	//! 레벨업일때는 즉시 디비에 업데이트 한다, 그외에는 주기적으로 업데이트 - StatePlayerBase::saveCharInfo()
	if (isLevelUp)
	{
		EReqDbSaveCharExp* reqDb = NEW EReqDbSaveCharExp;
		reqDb->charId = player->GetCharId();
		reqDb->exp = player->GetExp();
		reqDb->totalExp = player->GetTotalExp();
		reqDb->level = player->GetLevel();
		player->SendToDb(EventPtr(reqDb));

		MakingHistory_Updater_LevelUp updater;
		updater.Setup( oldLevel, player->GetLevel() );
		player->GetMakingAction().MakingHistoryUpdate( updater );

	}

	ENtfGameAddExp* ntf = NEW ENtfGameAddExp;
	ntf->currentExp = player->GetExp();
	ntf->maxExp = currLevelElem->exp;
	ntf->totalExp = player->GetTotalExp();
	ntf->baseExp = baseExp;
	ntf->addedExp = addedExp;
	player->SendToClient(EventPtr( ntf ) );
}

void ActionPlayer::AddExp( const CharExp charExp, const SoulExp soulExp, const PetExp petExp /*= 0*/, const CharExp addedCharExp /*= 0*/, const SoulExp addedSoulExp /*= 0 */, const PetExp addedPetExp /*= 0*/ )
{	
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	AddCharacterExp( charExp, addedCharExp );
#ifdef __Reincarnation__jason_180628__
	player->GetCharSoulInfo().AddExp(soulExp, addedSoulExp);
#else
	getSoulSkillmanageAction()->AddExp( soulExp, addedSoulExp );
#endif
	
	ActionPlayerPetManage * pet = player->GetAction<ActionPlayerPetManage>();
	VERIFY_RETURN(pet, );

	pet->AddExpGrowthPet(addedPetExp);
}

//void ActionPlayer::ChangeStateMode( const UInt32 newStateMode )
//{
//	getPlayerAttribute()->stateMode = newStateMode;
//}
  

//AttributePlayer* ActionPlayer::getPlayerAttribute()
//{
//	return static_cast< AttributePlayer* >(GetOwnerPlayer()->GetAttribute(ATTRIBUTE_PLAYER));
//}
//
//AttributePosition* ActionPlayer::getPositionAttribute()
//{
//	return static_cast< AttributePosition* >(GetOwnerPlayer()->GetAttribute(ATTRIBUTE_POSITION));
//}

AttributeParty*	ActionPlayer::getPartyAttribute()
{
	return static_cast< AttributeParty* >(GetOwnerPlayer()->GetAttribute(ATTRIBUTE_PARTY));
}

ActionPlayerPetManage* ActionPlayer::getPetManageAction()
{
	return GetOwnerPlayer()->GetAction<ActionPlayerPetManage>();
}

#ifndef __Reincarnation__jason_180628__
ActionPlayerSoulSkillManage* ActionPlayer::getSoulSkillmanageAction()
{
	return GetOwnerPlayer()->GetAction<ActionPlayerSoulSkillManage>();
}
#endif

//Bool ActionPlayer::isLevelUp()
//{	
//	if ( getPlayerAttribute()->level >= Limits::MAX_LEVEL)
//	{
//		return false;
//	}
//	const LevelInfoElem* levelInfoScript = SCRIPTS.GetLevelScript( getPlayerAttribute()->level);
//
//	if (!levelInfoScript)
//	{
//		MU2_ASSERT( !"LevelInfo Script를 가져올수 없습니다.");
//		return false;
//	}
//	
//	if ( getPlayerAttribute()->exp >= static_cast<CharExp>(levelInfoScript->exp) )
//	{
//		return true;
//	}
//
//	return false;
//}

////Bool ActionPlayer::setupDefaultPosition()
////{
////	AttributePosition* attrPos = getPositionAttribute();
////	MU2_ASSERT( attrPos );
////
////	ClassType::Enum eClassType = getPlayerAttribute()->eClassType;
////
////	MapStartingPointElem startingPointScript;
////	if( ! SCRIPTS.GetMapStartingPoint( eClassType, startingPointScript ) )
////	{
////		return false;
////	}
////
////	attrPos->pos = startingPointScript.starting;
////	attrPos->dir = static_cast< Float >( startingPointScript.startingDir );
////
////	return true;
////}

//Bool ActionPlayer::SetRotation( const Float radian )
//{
//	AttributePosition* attrPos = getPositionAttribute();
//	if ( !attrPos )
//	{
//		return false;
//	}
//
//	attrPos->dir = radian;
//
//	return false;
//
//}

//void ActionPlayer::SetUpLoginTime( const DateTime& time )
//{
//	getPlayerAttribute()->loginTime = time;
//	EntityPlayer* player = GetOwnerPlayer();
//	VERIFY_RETURN(player && player->IsValid(), );
//
//	ActionPlayerInventory * api = GetEntityAction(player);
//	VERIFY_RETURN(api, );
//
//	api->MarbleSetLoginTime(time);
//	
//}

//void ActionPlayer::SetUpLastSyncTime( const DateTime& time )
//{
//	getPlayerAttribute()->syncPlayTime = time;
//}

//void ActionPlayer::UpdatePlayTime()
//{
//	DateTime present  = DateTime::GetPresentTime();
//	DateTime syncTime =  getPlayerAttribute()->syncPlayTime;
//
//	if ( syncTime.GetStatus() != DateTime::valid )
//	{
//		getPlayerAttribute()->syncPlayTime = getPlayerAttribute()->loginTime;
//	}
//
//	if ( syncTime.GetMinute() != present.GetMinute() )
//	{
//		// update
//		getPlayerAttribute()->syncPlayTime = present;
//	}
//}

Bool ActionPlayer::SetDuelFlagNpc(EntityNpc* flagNpc)
{
	VERIFY_RETURN(flagNpc, false);

	m_flagNpcId = flagNpc->GetId();

#ifdef __Renewal_Battle_IFF_by_kangms_180829
	auto actPlayerCognition = GetOwnerPlayer()->GetAction<ActionPlayerCognition>();
	actPlayerCognition->AttachFriendlyByContents(EntityRelationshipType::Friendly_Id, m_flagNpcId);
#endif//__Renewal_Battle_IFF_by_kangms_180829

	return true;
}

Bool ActionPlayer::SetRechargeObject( EntityNpc* obj )
{
	VERIFY_RETURN(obj && obj->IsValid(), false);

	if ( false == obj->IsRechargeType() == false )
	{
		return false;
	}

	const EntityNpc* curObj = m_rechargeObject;

	m_rechargeObject = static_cast<EntityNpc*>(obj);

	if ( curObj != obj )
	{
		onEnterRechargeObject();
	}
	return true;
}

EntityNpc* ActionPlayer::GetRechargeObject()
{
	return m_rechargeObject;
}

void ActionPlayer::ClearRechargeObject()
{
	if ( m_rechargeObject != nullptr )
	{
		onExitRechargeObject();
	}

	m_rechargeObject = nullptr;
}

void ActionPlayer::onEnterRechargeObject()
{
	VERIFY_RETURN(m_rechargeObject && m_rechargeObject->IsValid() && m_rechargeObject->GetObjectElem(), );

	ActionNpcOperate* actObject = m_rechargeObject->GetAction<ActionNpcOperate>();
	actObject->OnEnterObject(GetOwnerPlayer() );

	SystemMessage sysMsg( L"sys", L"Msg_Event_Buff_Effect" );
	sysMsg.SetParam( L"str", m_rechargeObject->GetObjectElem()->name );
	SendSystemMsg( GetOwnerPlayer(), sysMsg );

	theLogicEffectSystem.OnEnterObjectCast( m_rechargeObject, GetOwnerPlayer() );
}

void ActionPlayer::onExitRechargeObject()
{
	theLogicEffectSystem.OnExitObjectCast(GetOwnerPlayer() );
}

void ActionPlayer::syncActionLog()
{
	Tick curTick = GetTickCount();
	
	if ( curTick - m_PrevSyncLogTick > m_SyncLogTick )
	{
		EReqLogDb2* log = NEW EReqLogDb2;
		EventPtr logEventPtr( log );
		EventSetter::FillLogForEntity( LogCode::E_CHAR_SYNC, GetOwnerPlayer(), log->action );
		SendDataCenter( logEventPtr );
		m_PrevSyncLogTick = curTick;
	}
}

//void ActionPlayer::SetIpAddress(const std::string& ip)
//{
//	EntityPlayer* owner = GetOwnerPlayer();
//	AttributePlayer* attrPlayer = GetEntityAttribute(owner);
//	std::wstring ipAddr(ip.cbegin(), ip.cend());
//
//	attrPlayer->ipAddr = ipAddr;
//}

//void ActionPlayer::SetupCharName( const std::string& name )
//{
//	AttributePlayer* attrPlayer = GetEntityAttribute( GetOwnerPlayer() );
//	StringUtil::Convert( name.c_str(), attrPlayer->charName );
//}

void ActionPlayer::ResetDuelFlagNpc()
{
#ifdef __Renewal_Battle_IFF_by_kangms_180829
	auto actPlayerCognition = GetOwnerPlayer()->GetAction<ActionPlayerCognition>();
	actPlayerCognition->DetachFriendlyByContents(EntityRelationshipType::Friendly_Id, m_flagNpcId);
#endif//__Renewal_Battle_IFF_by_kangms_180829

	m_flagNpcId = 0;
}

EntityNpc* ActionPlayer::GetDuelFlagNpc()
{
	if (0 == m_flagNpcId)
	{
		return nullptr;
	}

	Sector* pSector = GetOwnerPlayer()->GetSector();
	VERIFY_RETURN(pSector, NULL);

	return pSector->FindNpc( m_flagNpcId );

}

PartyId ActionPlayer::GetPartyId() const
{
	const AttributeParty* attrParty = GetEntityAttribute( GetOwnerPlayer() );
	return attrParty->partyId;
}

CharId ActionPlayer::GetCharId() const
{
	const AttributePlayer* attrPlayer = GetEntityAttribute( GetOwnerPlayer() );
	return attrPlayer->charId;
}

Vector3 ActionPlayer::GetValidArroundPosition(EntityId unique )
{
	for ( UInt32 i = 0; i < PLAYER_AROUND_POSITION_NUM; i = i++ )
	{
		UInt32 index = i * 4;
		index = index % 11;
		if ( i == 11 )
		{
			index = 11;
		}

		if ( m_aroundInfo[index].npcEntityId == 0 )
		{
			m_aroundInfo[index].npcEntityId = unique;
			return m_aroundInfo[index].pos;
		}
	}
	
	return Vector3( 0, 0, 0 );
};

void ActionPlayer::SetValidArroundPosition( Vector3 curPos )
{
	static UInt32 distance = 220;

	for ( UInt32 i = 0; i < PLAYER_AROUND_POSITION_NUM; i++ )
	{
		m_aroundInfo[i].npcEntityId = 0;

		Float raidanVAlue = mu2::DegreeToRadian( static_cast<float>( 30 * i ) );

		m_aroundInfo[i].pos.x = curPos.x + ( cos( raidanVAlue ) * distance );
		m_aroundInfo[i].pos.y = curPos.y + ( sin( raidanVAlue ) * distance );
		m_aroundInfo[i].pos.z = curPos.z;
	}	
}
//
//AccountId ActionPlayer::GetAccountId() const
//{
//	const AttributePlayer* attr = GetEntityAttribute( GetOwnerPlayer() );
//	return attr->accountId;
//}
//
//const std::wstring&	ActionPlayer::GetAccountName() const
//{
//	const AttributePlayer* attr = GetEntityAttribute( GetOwnerPlayer() );
//	return attr->accountName;
//}
//
//const std::wstring& ActionPlayer::GetCharName() const
//{
//	const AttributePlayer* attr = GetEntityAttribute( GetOwnerPlayer() );
//	return attr->charName;
//}

const IndexNpc ActionPlayer::GetRechargeIndex()
{
	if ( m_rechargeObject == nullptr )
		return 0;
	return m_rechargeObject->GetNpcIndex();
}

//void ActionPlayer::SetWhoRessurectMe( Bool isRessurect )
//{
//	getPlayerAttribute()->isRevive = isRessurect;
//}

Bool ActionPlayer::ChangeTownPortalContinent()
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), false);	
	
	IndexContinent continent = SCRIPTS.CastZoneToContinent(player->GetCurrZoneIndex());
	VALID_RETURN(continent, false);
	VALID_RETURN(continent != player->GetPlayerAttr().currentContinent, false);

	const TownPortalElem* elem = SCRIPTS.GetTownPortalElem( continent );
	VALID_RETURN(elem && false == elem->isDefault, false);
	
	player->GetPlayerAttr().currentContinent = continent;	
	player->SaveCharInfoToDb();
	player->GetPetManageAction().SaveSpawnePetDb();

	return true;
}

void ActionPlayer::FillUpTownPortalInfo(EzcNtfGameTownPortalInfo* ntf) const
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	ntf->defaultPositionIndex = SCRIPTS.GetTownPortalDefaultPosition();	
	IndexContinent continent = SCRIPTS.CastPositionToContinentWithNoCheck(ntf->defaultPositionIndex);

	auto attrPlayer = player->GetAttribute<AttributePlayer>();
	auto iter = std::find_if( attrPlayer->exploredPortals.begin(), attrPlayer->exploredPortals.end(), [continent]( const IndexPortal& value )
		{
			return (SCRIPTS.CastPositionToContinentWithNoCheck(value) == continent );
		} );

	if ( iter == attrPlayer->exploredPortals.end() )
	{
		ntf->defaultPositionEnable = false;
	}
	else
	{
		ntf->defaultPositionEnable = true;
	}

	auto elem = SCRIPTS.GetTownPortalElem( attrPlayer->currentContinent );
	if ( elem == nullptr )
	{
		MU2_WARN_LOG(LogCategory::CONTENTS, "[E] Can't find current continent info [%d]", static_cast<int>(attrPlayer->currentContinent) );
		return;
	}

	ntf->currentPositionIndex = elem->characterPositionIndex;
}

void ActionPlayer::SetupSpeed( Float speed )
{
	auto actSector = GetOwnerPlayer()->GetAction< ActionSector >();
	realworld::IAgent* agent = actSector->GetRealWordAgent();
	if ( agent != nullptr )
	{
		agent->SetSpeed( speed );
	}
}

void ActionPlayer::Send2ClientTownPortalInfo()
{
	EzcNtfGameTownPortalInfo* ntf = NEW EzcNtfGameTownPortalInfo;
	this->FillUpTownPortalInfo( ntf );
	SERVER.SendToClient( GetOwnerPlayer(), EventPtr( ntf ) );
}

void ActionPlayer::OnPostDeadNotfiyToSectorController()
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	if (false == player->IsDead())
	{
		// 죽은게 아니라면 이벤트를 안 받는다.
		return;
	}

	
	auto sector = player->GetSector();

	EntityUnit* killer = player->GetSectorAction().GetUnitInMySector(m_killerUnitId);
	sector->GetSectorController().GetAction< ActionSectorController >()->OnEvent(DungeonEventType::KILL_USER_POST, GetOwnerPlayer(), killer);
	
}

void ActionPlayer::OnDeadNotfiyToSectorController()
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	if (false == player->IsDead())
	{
		// 죽은게 아니라면 이벤트를 안 받는다.
		return;
	}

	auto func = [this, player]
	{	
		auto sector = player->GetSector();

		EntityUnit* killer = player->GetSectorAction().GetUnitInMySector( m_killerUnitId );
		sector->GetSectorController().GetAction< ActionSectorController >()->OnEvent( DungeonEventType::KILL_USER, GetOwnerPlayer(), killer );
	};

	func();
	m_killerUnitId = 0;
}
void ActionPlayer::UpdateLastSelectedMazeLevel(DifficultyLevel level)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	auto attrPlayer = player->GetAttribute<AttributePlayer>();

	attrPlayer->lastSelectedMazeLevel = level;

	EReqDbUpdateLastSelectedMazeLevel* req = NEW EReqDbUpdateLastSelectedMazeLevel;
	req->charId			= attrPlayer->charId;
	req->selectedLevel = attrPlayer->lastSelectedMazeLevel;
	SERVER.SendToDb(GetOwnerPlayer(), EventPtr(req));
}
void ActionPlayer::UpdateMazeLevel(DifficultyLevel level)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	auto attrPlayer = player->GetAttribute<AttributePlayer>();

	attrPlayer->mazeCompleteLevel = level;

	EReqDbUpdateMazeLevel* req = NEW EReqDbUpdateMazeLevel;
	req->charId = attrPlayer->charId;
	req->mazeLevel = attrPlayer->mazeCompleteLevel;
	SERVER.SendToDb(GetOwnerPlayer(), EventPtr(req));
}

//KnightageId ActionPlayer::GetKnightageId()
//{
//	auto attrPlayer = GetOwnerPlayer()->GetAttribute<AttributePlayer>();
//	return attrPlayer->knightageId;
//}
//
//KnightageId ActionPlayer::GetFollowerId()
//{
//	auto attrPlayer = GetOwnerPlayer()->GetAttribute<AttributePlayer>();
//
//	if (attrPlayer->followerKnightageId == knightage::FOLLOWER_JOIN_KNIGHTAGE)
//		return 0;
//
//	return attrPlayer->followerKnightageId;
//}

//void ActionPlayer::SetFcsAuthInfo( const FcsAuthInfo& fcsAuthInfo )
//{
//	auto attrPlayer = GetOwnerPlayer()->GetAttribute<AttributePlayer>();
//	attrPlayer->fcsAuthInfo = fcsAuthInfo;
//}
//
//const FcsAuthInfo& ActionPlayer::GetFcsAuthInfo() const
//{
//	auto attrPlayer = GetOwnerPlayer()->GetAttribute<AttributePlayer>();
//	return attrPlayer->fcsAuthInfo;
//}

//void ActionPlayer::SetReviveCount(const UInt8 reviveCount)
//{
//	auto attrPlayer = getPlayerAttribute();
//	attrPlayer->inPlaceReviveCount = reviveCount;
//}
//
//UInt8 ActionPlayer::GetReviveCount()
//{
//	const auto attrPlayer = getPlayerAttribute();
//	return attrPlayer->inPlaceReviveCount;
//}
//
//void ActionPlayer::IncreaseReviveCount()
//{
//	const auto attrPlayer = getPlayerAttribute();
//	attrPlayer->inPlaceReviveCount++;
//}

//void ActionPlayer::SetPcRoomInfo(Int32 billingGuid, PcRoomKeepInfo& pcRoomKeepInfo)
//{
//	const auto attrPlayer = getPlayerAttribute();
//	attrPlayer->SetPcBillingGuid(billingGuid);	
//	attrPlayer->SetMembershipPcRoomKeepInfo(pcRoomKeepInfo);
//	if (billingGuid != 0)
//	{
//		attrPlayer->pcRoomLastUpdateTickTime = GetTickCount();
//	}
//}

//void ActionPlayer::SetReturnUserInfo( IndexItem rewarditem, DateTimeEx endDate )
//{
//	const auto attrPlayer = getPlayerAttribute();
//	attrPlayer->returnUserRewardItem = rewarditem;
//	attrPlayer->returnUserEventEndDate = endDate.ToDateTime();
//}


//Int32 ActionPlayer::GetLongestReturnUserDays()
//{
//	const auto attrPlayer = getPlayerAttribute();
//	return attrPlayer->longestReturnUserDays;
//}
//
//void ActionPlayer::SetLongestReturnUserDays(Int32 longestDays)
//{
//	const auto attrPlayer = getPlayerAttribute();
//	attrPlayer->longestReturnUserDays = longestDays;
//}


//Int32 ActionPlayer::GetLongestReturnCharacterDays()
//{
//	const auto attrPlayer = getPlayerAttribute();
//	return attrPlayer->longestReturnCharacterDays;
//}
//
//void ActionPlayer::SetLongestReturnCharacterDays(Int32 longestDays)
//{
//	const auto attrPlayer = getPlayerAttribute();
//	attrPlayer->longestReturnCharacterDays = longestDays;
//}

void ActionPlayer::SyncHpMp( UInt32 hp, UInt32 mp, UInt32 maxHp )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	if (hp == m_syncHp && mp == m_syncMp)
	{
		return;
	}

	Tick tick = ::GetTickCount();
	if (( tick - m_HpMpLaskTick ) < HPMP_SYNC_TICK)
	{
		return;
	}

	PartyId partyId = player->GetAttribute<AttributeParty>()->partyId;
	if (partyId > 0)
	{
		auto actParty = player->GetAction<ActionPlayerParty>();

		ENtfGamePartySyncHpMp* ntf = NEW ENtfGamePartySyncHpMp;
		ntf->entityId	= player->GetId();
		ntf->partyId	= partyId;
		ntf->hp			= hp;
		ntf->mp			= mp;
		actParty->SendToMembersInMySector( EventPtr( ntf ) );
	}

	if ( m_isRaidType && hp != m_syncHp)
	{
		auto sectorAction = player->GetAction<ActionSector>();
		
		EzcNtfRaidSyncHp* ntf = NEW EzcNtfRaidSyncHp;
		ntf->charId		= player->GetCharId();
		ntf->hp			= hp;
		ntf->maxHp		= maxHp;
		sectorAction->BroadCast( EventPtr( ntf ) );
	}

	m_HpMpLaskTick		= tick;
	m_syncHp			= hp;
	m_syncMp			= mp;
}

void ActionPlayer::SyncHpMpInTheSectorToSelf() const
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	if (false == m_isRaidType)
	{
		return;
	}

	auto sectorAction = player->GetAction<ActionSector>();

	auto sector = sectorAction->GetSector();
	if (nullptr == sector)
	{
		return;
	}
	auto actOwnerAbility = player->GetAction<ActionPlayerAbility>();

	EzcNtfRaidSyncHp* ntfOwner = NEW EzcNtfRaidSyncHp;
	ntfOwner->charId = player->GetCharId();
	ntfOwner->hp = actOwnerAbility->GetCurrentHp();
	ntfOwner->maxHp = actOwnerAbility->GetMaxHp();
	EventPtr evt( ntfOwner );

	sector->ForEachByCond( EntityTypes::PLAYER_ZONE, [ & ]( const MapEntity::value_type&  value )
	{
		EntityPlayer* player = static_cast<EntityPlayer*>(value.second);
		VALID_RETURN( player && player->IsValid(), );

		// 내 정보 전송
		SERVER.SendToClient( player, evt );

		// 플레이어 정보 나에게 전송
		auto actAbility = player->GetAction<ActionPlayerAbility>();
		EzcNtfRaidSyncHp* ntf = NEW EzcNtfRaidSyncHp;
		ntf->charId = player->GetCharId();
		ntf->hp = actAbility->GetCurrentHp();
		ntf->maxHp = actAbility->GetMaxHp();
		SERVER.SendToClient( player, EventPtr( ntf ) );

	} );
}
//
//void ActionPlayer::SetupWorldServerId(ServerId worldServerId)
//{ 
//	const auto attrPlayer = getPlayerAttribute();
//	attrPlayer->m_worldServerId = worldServerId;
//}
//
//void ActionPlayer::SetupWorldSession(const SessionKey sessionKey)
//{
//	GetOwnerPlayer()->GetPlayerAttr().sessionInWorldServer = sessionKey;
//}
//
//void ActionPlayer::SetupSessionKey4World(const SessionKey sessionKey)
//{
//	GetOwnerPlayer()->GetPlayerAttr().session4WorldServer = sessionKey;
//}
//
//void ActionPlayer::SetupDestination(const ServerId id)
//{
//	GetOwnerPlayer()->GetPlayerAttr().dst = id;
//}
//
//void ActionPlayer::SetupDirectSession(const SessionKey sessionKey)
//{
//	GetOwnerPlayer()->GetPlayerAttr().session4Client = sessionKey;
//}

//void ActionPlayer::SetupAccountId(const AccountId accountId)
//{
//	EntityPlayer* owner = GetOwnerPlayer();
//	owner->GetPlayerAttr().accountId = accountId;
//
//}

//void ActionPlayer::SetupAccountName(const std::wstring& accountName)
//{
//	AttributePlayer* aplayer = GetEntityAttribute(GetOwnerPlayer());
//	MU2_ASSERT(aplayer);
//	aplayer->accountName = accountName;
//}

//void ActionPlayer::SetupCharId(const CharId charId)
//{
//	EntityPlayer* owner = GetOwnerPlayer();
//	AttributePlayer* attrPlayer = GetEntityAttribute(owner);
//	attrPlayer->charId = charId;
//
//}

//void ActionPlayer::SetupAccountGrade(const AccountGrade::Enum grade)
//{
//	EntityPlayer* owner = GetOwnerPlayer();	
//	AttributePlayer* attrPlayer = GetEntityAttribute(owner);
//	attrPlayer->accountGrade = grade;
//}


//void ActionPlayer::SetBlacklist(const Bool blacklist)
//{
//	EntityPlayer* owner = GetOwnerPlayer();
//	AttributePlayer* attrPlayer = GetEntityAttribute(owner);
//	attrPlayer->blacklist = blacklist;
//}

Bool ActionPlayer::IsChangingMap()
{
	if (true == m_isChangingMap)
	{
		if (true == m_checkChangeMapTimer.IsOn())
		{
			m_isChangingMap = false;
			MU2_WARN_LOG(LogCategory::CONTENTS, "[E] ActionPlayer> m_isChangingMap return by timer. [charId:%d]", GetCharId());
		}
	}

	return m_isChangingMap;
}

void ActionPlayer::SetChangingMap(const Bool set)
{
	m_isChangingMap = set;
	
	if (true == set)
	{
		m_checkChangeMapTimer.Reset();
	}
}

void ActionPlayer::PrevAffectContentsBuff(CastBuffType castBuffType)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player, );
	if (castBuffType == CastBuffType::CHECK_IN)
	{
		if (player->IsDead())
		{
			// 맵 이동 시 dead 상태인 경우는 부활 시 버프를 걸어줘야한다.
			m_engagedCheckInBuff = true;
			return;
		}
	}
	else if (castBuffType == CastBuffType::REVIVE)
	{
		// 부활시 버프 예약이 되어 있지 않으면 버프를 걸지 않는다.
		VALID_RETURN(m_engagedCheckInBuff, );
	}

	theLogicEffectSystem.OnPrepareCheckIn(player);

	player->GetCharLevelBuff().CheckIn();

}

void ActionPlayer::PostAffectContentsBuff(JoinContext::Enum joinContext)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player, );
	VALID_RETURN(player->IsAlive(), );

	Sector* sector = player->GetSector();
	VALID_RETURN(sector, );

	ActionPlayerKnightage* actPlayerKnightage = GetEntityAction(player);
	MU2_ASSERT(nullptr != actPlayerKnightage);

	actPlayerKnightage->ApplySkills();
	
	FieldWorldBuffInfoList worldBuffList;
	theFieldKnightageManager.GetFieldWorldBuffInfoList(worldBuffList);

	#ifdef __Patch_Knightage_Renewal_by_eunseok_20190128
	std::vector<EwcNtfKnightageWorldBuffAlarm::WorldBuffInfo> worldBuffs;
	#endif

	// 시간이 얼마 남지 않았으면 아예 처리하지 않는다.
	DateTime present = DateTime::GetPresentTime();
	for (auto& worldBuff : worldBuffList)
	{
		VALID_DO(worldBuff.bufffInfo.GetBuffEndTime() > present, continue);

		Tick remainTick = static_cast<Tick>((worldBuff.bufffInfo.GetBuffEndTime() - present).GetTotalMilliSeconds());

		ActionBuff* actBuff = GetEntityAction(player);
		BuffInfoPtr buff = actBuff->GetAffectedBuffInfo(worldBuff.bufffInfo.buffId);
		if (buff == nullptr)
		{
			// 버프가 없다면 버프를 걸어준다.
			theLogicEffectSystem.OnSingleCast(player, worldBuff.bufffInfo.buffId);
			buff = actBuff->GetAffectedBuffInfo(worldBuff.bufffInfo.buffId);
		}

		if (buff != nullptr)
		{
#ifdef __Hotfix_Renew_FixedEndTimeBuff_by_jjangmo_181219
			buff->buffDurationPtr->UpdateDuration(remainTick);
#else
			buff->endTick = buff->startTick + remainTick;
#endif
			actBuff->SendCastBuff( buff, true, 0, remainTick );

			if (joinContext == JoinContext::JC_CHECKIN && worldBuff.knightagePtr != nullptr)
			{
				ENtfKnightageWorldBuffApplyInfo* ntf = new ENtfKnightageWorldBuffApplyInfo;
				ntf->knightageName = worldBuff.knightagePtr->GetName();
				ntf->skillId = worldBuff.bufffInfo.buffId;
				SERVER.SendToClient(player, EventPtr(ntf));
			}
#ifdef __Patch_Knightage_Renewal_by_eunseok_20190128
			if (worldBuff.knightagePtr != nullptr)
			{

				EwcNtfKnightageWorldBuffAlarm::WorldBuffInfo buffinfo;
				buffinfo.knightageName = worldBuff.knightagePtr->GetName();
				buffinfo.buffId = worldBuff.bufffInfo.buffId;
				worldBuff.bufffInfo.GetBuffEndTime().GetAsSystemTime(buffinfo.endDate);
				worldBuffs.emplace_back(buffinfo);
			}
#endif 
		}
	}
	#ifdef __Patch_Knightage_Renewal_by_eunseok_20190128
	if (worldBuffs.empty() == false)
	{
		EwcNtfKnightageWorldBuffAlarm*  alarm_ntf = new EwcNtfKnightageWorldBuffAlarm;
		alarm_ntf->worldBuffs = std::move(worldBuffs);
		SERVER.SendToClient(player, EventPtr(alarm_ntf));
	}
	#endif 

#ifdef __Patch_ImprintedMemory_by_raylee_181128
		player->GetAction<ActionPlayerAchievement>()->ImprintMemoryOnEnter();
#endif

#ifdef __Patch_SetItem_Renewal_by_kangms_2019_4_10
	// 레벨에 맞는 것으로 체크
	auto actEventProxy = GetOwnerPlayer()->GetAction<ActionEventProxy>();

	mu2::design_pattern::EventProxy::ErrorCapture errCapture(&actEventProxy->GetEventProxy());

	actEventProxy->BroadcastMessage<void, int>(EVENT_ID::SET_CARD_REVIVE_EFFECT, 0);
	if (true != errCapture.IsEmpty()) {
		for (auto& strDelegate : errCapture.GetErrorStringList()) {
			MU2_ERROR_LOG(LogCategory::CONTENTS
				, "Not matched Parameter !!! : EventID:%d, ErrFunc(%s)"
				, EVENT_ID::SET_CARD_REVIVE_EFFECT, strDelegate.c_str());
		}
	}
#endif __Patch_SetItem_Renewal_by_kangms_2019_4_10

#ifdef __Patch_Teacher_And_Student_System_by_ch_20190604
	EzwReqTeacherAndStudentCheckState* zwReq = NEW EzwReqTeacherAndStudentCheckState;
	zwReq->charId = player->GetCharId();
	SERVER.SendToWorldServer(player, EventPtr(zwReq));
#endif // __Patch_Teacher_And_Student_System_by_ch_20190604
}

#ifdef __Patch_Tower_of_dawn_eunseok_2018_11_05
RewardPoint ActionPlayer::GetTowerOfDawnRewardPoint()
{
	return m_curTowerOfDawnRewardPoint;
}

void ActionPlayer::SetTowerOfDawnRewardPoint(RewardPoint point)
{
	m_curTowerOfDawnRewardPoint = point;
}

void ActionPlayer::UpdateTowerOfDawnRewardPoint(RewardPoint point)
{
	m_curTowerOfDawnRewardPoint = point;
	// DB전달 한다

	EReqDbUpdateTowerOfDawnRewardPoint* req = NEW EReqDbUpdateTowerOfDawnRewardPoint;
	req->charId = GetCharId();
	req->point  = m_curTowerOfDawnRewardPoint;
	SERVER.SendToDb(GetOwnerPlayer(), EventPtr(req));
}
#endif //__Patch_Tower_of_dawn_eunseok_2018_11_05
} // namespace mu2
