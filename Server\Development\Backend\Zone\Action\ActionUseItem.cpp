﻿#include "stdafx.h"
#include <Backend/Zone/ZoneServer.h>
#include <Backend/Zone/Action/ActionAchievement.h>
#include <Backend/Zone/Action/ActionUseItem.h>
#include <Backend/Zone/Action/ActionPlayer.h>
#include <Backend/Zone/Action/ActionPlayerInventory.h>
#include <Backend/Zone/Action/ActionDurability.h>
#include <Backend/Zone/Action/ActionPlayer/ActionSocket.h>
#include <Backend/Zone/Action/ActionPlayerMaking.h>
#include <Backend/Zone/Action/ActionBuff.h>
#include <Backend/Zone/Action/ActionSkillRune.h>
#include <Backend/Zone/Action/ActionParty.h>
#include <Backend/Zone/Action/ActionPlayerItemTrade.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerKnightage.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerMembership.h>
#include <Backend/Zone/Action/ActionArtifact.h>
#include <Backend/Zone/System/DropSystem.h>
#include <Backend/Zone/System/LogicEffectSystem.h>
#include <Backend/Zone/System/ReviveSystem.h>
#include <Backend/Zone/View/ViewPosition.h>
#include <Backend/Zone/Transaction/ItemTransaction/ItemPushTransaction.h>
#include <Shared/Scripts/EmotionBoxScript.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerSeason.h>

#include <Backend/Zone/Element/Item/ItemFinder.h>
#include <Backend/Zone/System/NotifierGameContents.h>

#ifdef __Renewal_Additional_Grade_Transfer_by_kangms_181001
#include <Backend/Zone/Action/ActionPlayer/ActionContact.h>
#endif//__Renewal_Additional_Grade_Transfer_by_kangms_181001

namespace mu2
{

typedef	ConstItemSelector< GambleBagInfo >::ItemSelectorType	GambleConstItemSelector;
typedef	MutableItemSelector< GambleBagInfo >::ItemSelectorType	GambleMutableItemSelector;

ActionPlayerUseItem::ActionPlayerUseItem( EntityPlayer* owner )
	: Action4Player( owner, ID )
	, m_lastSystemMsg(L"")
	, m_systemMsgParam( -1 )
	, m_useItemLimit( owner )
{
	m_gambleOutputs.Clear();

	CounterInc("ActionUseItem");
	MU2_ASSERT( NULL != owner );
}

ActionPlayerUseItem::~ActionPlayerUseItem()
{
	CounterDec("ActionUseItem");
}

void ActionPlayerUseItem::OnTick()
{
}

void ActionPlayerUseItem::SetupFromDbAndSendToClient( const DbUseItemLimitInfoList& list )
{
	m_useItemLimit.SetupFromDbAndSendToClient( list );
}

const std::wstring ActionPlayerUseItem::GetLastSystemMsg()
{
	std::wstring tmp(m_lastSystemMsg);
	m_lastSystemMsg.clear();
	return tmp;
}

Bool ActionPlayerUseItem::IsExistLastSystemMsgParam()
{
	return m_systemMsgParam > -1;
}

const Int32 ActionPlayerUseItem::GetLastSystemMsgParam()
{
	Int32 retValue = 0;
	retValue = m_systemMsgParam;
	m_systemMsgParam = -1;

	return retValue;
}

ErrorItem::Error ActionPlayerUseItem::Use( const ItemPos& itemPos, const EntityId& targetId, const Vector3& point )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);	

	const ItemConstPtr item = player->GetInventoryAction().GetItem(itemPos);
	VERIFY_RETURN(item, ErrorItem::E_FAILED );

	UseItem::InputValue value( GetOwnerPlayer() );
	value.SetTarget(targetId);
	value.targetPos  = point;

	return use( item, value);
}

ErrorItem::Error ActionPlayerUseItem::Use( const IndexItem indexItem, const EntityId& targetId, const Vector3& point )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), ErrorItem::playerNotFound );

	ActionPlayerInventory* actInven = player->GetAction< ActionPlayerInventory >();
	VERIFY_RETURN( actInven, ErrorItem::E_FAILED );

	const ItemConstPtr item = actInven->GetItemByIndex(InvenWithPreminums, indexItem );
	VALID_RETURN( item, ErrorItem::E_FAILED );

	UseItem::InputValue value( player );
	value.SetTarget( targetId );
	value.targetPos  = point;
	value.SetSourceItemPos( item->GetPos());

	return use( item, value );
}

ErrorItem::Error ActionPlayerUseItem::UseGambleItem(const ItemPos& itemPos, UInt16 useItemCount)
{
	EntityPlayer* ownerPlayer = GetOwnerPlayer();
	VERIFY_RETURN(ownerPlayer && ownerPlayer->IsValid(), ErrorItem::E_FAILED);

	const ItemConstPtr item = ownerPlayer->GetInventoryAction().GetItem(itemPos);
	VERIFY_RETURN(item, ErrorItem::itemNotFound);

	// 겜블형 아이템만 가능
	VERIFY_RETURN(ItemDivisionType::IsGambleItemDivisionType(item->GetDivision()), ErrorItem::E_FAILED);

	ErrorItem::Error eError = checkAndSet(item);
	VERIFY_RETURN( ErrorItem::SUCCESS == eError, eError );

	if (1 < useItemCount)
	{
		VERIFY_RETURN(ItemDivisionType::IsOpenableAtOnceDivisionType(item->GetDivision()), ErrorItem::E_INVALID_TYPE);
	}

	switch(item->GetDivision())
	{
	case IDT::SELECT_GAMBLE_ITEM:
		{
			eError = selectGambleItem(item);
		}
		break;
	default:
		{
			eError = baseGambleItem( item, useItemCount );
			if (eError == ErrorItem::SUCCESS)
				processAfterUseItem( item );
		}
		break;
	}

	return eError;
}

Bool ActionPlayerUseItem::checkMinimumNeedFreeSlotCount(const GambleItemElem* gambleItemElem, GambleBagElem::GroupType eGroupType)
{
	EntityPlayer* ownerPlayer = GetOwnerPlayer();
	VERIFY_RETURN(ownerPlayer && ownerPlayer->IsValid(), false);

	Int32 needSlots4Normal = gambleItemElem->GetMaxNeedNormalSlotCount(ownerPlayer->GetClassType(), eGroupType);
	Int32 needSlots4Premium = gambleItemElem->GetMaxNeedPremiumSlotCount(ownerPlayer->GetClassType(), eGroupType);

	ActionPlayerInventory& invenAction = ownerPlayer->GetInventoryAction();

	// 큐브 아이템 보상에 대한 슬롯 체크 (기획 요청으로 클래스별 제외)			
	if (needSlots4Normal > 0 && false == invenAction.IsEnoughFreeSlot(InvenBags, needSlots4Normal))
	{
		m_lastSystemMsg.assign(L"Msg_BoxOpen_Err_NormalBag");
		m_systemMsgParam = needSlots4Normal;
		return false;
	}

	// 큐브 아이템 보상에 대한 슬롯 체크 (기획 요청으로 클래스별 제외)
	if (needSlots4Premium > 0 && false == invenAction.IsEnoughFreeSlot(PremiumBags, needSlots4Premium))
	{
		m_lastSystemMsg.assign(L"Msg_BoxOpen_Err_CashBag");
		m_systemMsgParam = needSlots4Premium;
		return false;
	}

	return true;
}


ErrorItem::Error ActionPlayerUseItem::baseGambleItem(const ItemConstPtr& item, UInt16 useItemCount)
{
	VERIFY_RETURN( item, ErrorItem::E_FAILED );
	VERIFY_RETURN( useItemCount, ErrorItem::E_FAILED );

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	const GambleItemElem* gambleItemElem = SCRIPTS.GetGambleItemElem( item->GetItemIndex() );
	VERIFY_RETURN( gambleItemElem, ErrorItem::elemNotFound );
	
	VERIFY_RETURN(useItemCount <= item->GetCurStackCount(), ErrorItem::E_INVALID_STACK_VALUE );
	VALID_RETURN(checkMinimumNeedFreeSlotCount(gambleItemElem, GambleBagElem::StaticGroup), ErrorItem::E_REFER_SYSTEM_MSG);

	TotalGambleList clientMsg;
	GambleOutputs gambleOutputs;
	UInt16 usedItemCount = 0;
	ErrorItem::Error eError = checkandSetGambleData( gambleItemElem, useItemCount, usedItemCount, gambleOutputs, clientMsg);
	VERIFY_RETURN( ErrorItem::SUCCESS == eError, eError );

	SimpleItems sitems;
	SimpleItem sitem;
	sitem.itemIndex = item->GetItemIndex();
	sitem.itemCount = usedItemCount;
	sitems.push_back(sitem);

	for (auto &i : gambleOutputs.bagList)
	{
		sitem.itemIndex = i.itemIndex;
		sitem.itemCount = i.itemCount;
		sitems.push_back(sitem);
	}

	ItemGambleTransaction* itemGambleTransaction = NEW ItemGambleTransaction(__FUNCTION__, __LINE__, player, sitems, LogCode::E_ITEM_USE_GAMBLE);
	TransactionPtr transPtr( itemGambleTransaction );
	{
		ItemBag bag(*player);

		eError = supplyGambleDatas(item, gambleOutputs, usedItemCount, bag, transPtr);
		VERIFY_RETURN(eError == ErrorItem::SUCCESS, eError);

		VERIFY_RETURN(bag.Write(transPtr), ErrorItem::BagWriteFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::E_TRANSACION_FAILED);
	}

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerUseItem::selectGambleItem(const ItemConstPtr& item )
{
	VERIFY_RETURN(item, ErrorItem::itemNotFound );

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	const GambleItemElem* gambleItemElem = SCRIPTS.GetGambleItemElem( item->GetItemIndex() );
	VERIFY_RETURN(gambleItemElem, ErrorItem::elemNotFound );

	VALID_RETURN(checkMinimumNeedFreeSlotCount(gambleItemElem, GambleBagElem::GroupAll), ErrorItem::E_REFER_SYSTEM_MSG);

	VERIFY_RETURN(item->GetCurStackCount() > 0, ErrorItem::E_INVALID_STACK_VALUE);
	
	UInt16 usableItemCount = 1; // 선택형 아이템은 한번에 한개만 사용이 가능하다.
	UInt16 usedItemCount = 0;

	ErrorItem::Error eError = ErrorItem::SUCCESS;

	GambleOutputs& gambleOutputs = m_gambleOutputs;

	TotalGambleList clientMsg;	

	eError = checkandSetGambleData( gambleItemElem, usableItemCount, usedItemCount, gambleOutputs, clientMsg);
	VERIFY_RETURN( eError == ErrorItem::SUCCESS, eError );

	VERIFY_RETURN(gambleOutputs.selectGambleLists.size(), ErrorItem::E_NOT_FIND_SELECT_GAMBLE );

	EResUseGambleItem* res = NEW EResUseGambleItem;
	{
		res->eError = ErrorItem::SUCCESS;
		res->money = clientMsg.money;
		res->greenZen = clientMsg.greenZen;
		res->redZen = clientMsg.redZen;
		res->imputedRedZen = clientMsg.imputedRedZen;
		res->petList = clientMsg.petList;
		res->items = clientMsg.items;
		getSelectGambleBag(res->selectGambleInfos);

		player->SendToClient(EventPtr(res));
	}

	gambleOutputs.useItemId = item->GetItemId();


	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerUseItem::UseSelectGambleItem( const Index32 bagIndex, const Index32 selectIndex )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	const GambleOutputs& gambleOutputs = m_gambleOutputs;

	const ItemConstPtr item = player->GetInventoryAction().GetItemById(AllBags, gambleOutputs.useItemId );
	VERIFY_RETURN( item, ErrorItem::itemNotFound );

	const GambleBagInfo* bagInfo = getGambleBagInfo(gambleOutputs, bagIndex, selectIndex);
	VERIFY_RETURN(bagInfo, ErrorItem::E_NOT_EXIST);
		
	ErrorItem::Error eError = checkSelectGambleOutputData(gambleOutputs, bagInfo);
	VERIFY_RETURN( ErrorItem::SUCCESS == eError, eError );

	const ItemOptionScript* scriptItemOption = SCRIPTS.GetScript<ItemOptionScript>();
	VERIFY_RETURN(scriptItemOption, ErrorItem::scriptNotFound);

	EnchantLevel enchantLv = std::min< EnchantLevel >(bagInfo->itemEnchantLv, scriptItemOption->MaxEnchantLevel( bagInfo->itemIndex ) );
	ItemData data(bagInfo->itemIndex, bagInfo->itemCount );
	data.enchant = enchantLv; 

	SimpleItems sitems;

	ItemSelectGambleTransaction* trans = NEW ItemSelectGambleTransaction(__FUNCTION__, __LINE__, player, sitems, LogCode::E_SELECT_GAMBLE_ITEM );
	TransactionPtr transPtr( trans );
	{
		ItemBag bag(*player);

		eError = supplyGambleDatas(item, gambleOutputs, 1, bag, transPtr);
		VERIFY_RETURN(ErrorItem::SUCCESS == eError, eError);

		VERIFY_RETURN(bag.Insert(data), ErrorItem::BagInsertFailed);
		VERIFY_RETURN(bag.Write(transPtr), ErrorItem::BagWriteFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::E_TRANSACION_FAILED);
	}

	return ErrorItem::SUCCESS;
}


ErrorItem::Error CheckTotalGambleList(const EntityPlayer& player, const TotalGambleList& clientMsg, OUT std::wstring& errorMsg)
{
	if (false == player.CheckZen(clientMsg.money, ChangedMoney::ADD))
	{	
		errorMsg.assign(L"Msg_Zen_Err_Max");
		return ErrorItem::E_REFER_SYSTEM_MSG;
	}

	if (false == player.CheckGreenZen(clientMsg.greenZen, ChangedMoney::ADD))
	{
		errorMsg.assign(L"Msg_GreenZen_Err_Max");
		return ErrorItem::E_REFER_SYSTEM_MSG;
	}
	
	if (false == player.GetPetManageAction().CanRegister((Int32)clientMsg.petList.size()))
	{	
		errorMsg.assign(L"Msg_ItemUse_err_inventory_full");
		return ErrorItem::E_REFER_SYSTEM_MSG;
	}

	return ErrorItem::SUCCESS;
}

// 겜블을 useTimes 만큼 한꺼번에 실행한다.
ErrorItem::Error ActionPlayerUseItem::checkandSetGambleData( const GambleItemElem* gambleItemElem, UInt16 useTimes, OUT UInt16& usedItemCount, OUT GambleOutputs& outputs, TotalGambleList& clientMsg )
{
	VERIFY_RETURN(useTimes, ErrorItem::E_FAILED);
	VERIFY_RETURN(gambleItemElem, ErrorItem::scriptNotFound);

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), ErrorItem::playerNotFound);

	
	auto itemInfoElem = SCRIPTS.GetItemInfoScript(gambleItemElem->gambleItemIndex);
	VERIFY_RETURN(itemInfoElem, ErrorItem::scriptNotFound);
	
	if (useTimes > 1)
	{
		VERIFY_RETURN(itemInfoElem->eDivision != ItemDivisionType::SELECT_GAMBLE_ITEM, ErrorItem::E_FAILED);
	}

	// 인벤에서 사용할 수 있는 빈 슬롯의 갯수를 구한다.

	clientMsg.Clear();
	outputs.Clear();

//	std::vector<Index32> gambleSelectItems;
	ErrorItem::Error error = ErrorItem::SUCCESS;

	Int32 needNormalFreeSlot = gambleItemElem->GetMaxNeedNormalSlotCount(owner->GetClassType(), GambleBagElem::StaticGroup);
	Int32 needPremiumFreeSlot = gambleItemElem->GetMaxNeedPremiumSlotCount(owner->GetClassType(), GambleBagElem::StaticGroup);

	SlotFinderByStackInfo::IndexsContainer resultItems;		// 인벤 추가 가능한 아이템 리스트	
	SlotFinderByStackInfo slotFinder(*owner);

	for (int i = 0; i < useTimes; ++i)
	{
		//=======================================================================================
		// 1회 사용가능 빈슬롯 체크
		//=======================================================================================
		Int32 usableNormalFreeSlotCount = slotFinder.GetFreeCount(InvenBags);
		Int32 usablePremiumFreeSlotCount = slotFinder.GetFreeCount(PremiumBags);

		if (usableNormalFreeSlotCount < needNormalFreeSlot || usablePremiumFreeSlotCount < needPremiumFreeSlot)
		{
			break;
		}

		std::vector<const GambleItemInfo*> gambleInfoList;
		gambleItemElem->GetGambleBagInfoList(owner->GetClassType(), OUT gambleInfoList);

		//=======================================================================================
		// 일반보상 - 겜블상자를 까면서 무조건 지급
		//=======================================================================================

		GambleBagInfoList vecOutItem;
		for (const GambleItemInfo* gambleInfo : gambleInfoList)
		{
			selectMutableGambleItemInfo(gambleInfo->gambleBagElem, gambleInfo->pickCount, gambleInfo->eClassType, vecOutItem);

			if (itemInfoElem->eDivision == ItemDivisionType::SELECT_GAMBLE_ITEM)
			{
				outputs.selectGambleLists.emplace_back(gambleInfo->gambleBagElem->gambleBagIndex);
			}
		}

		//=====================================================================================
		// 일반보상 - 겜블결과를 정보를 수집
		//=====================================================================================
		SlotFinderByStackInfo::IndexsContainer defaultSelectedItems;
		for (const auto& outItem : vecOutItem)
		{
			clientMsg.money += outItem.zen;;
			clientMsg.greenZen += outItem.greenZen;
			clientMsg.redZen += outItem.redZen;
			clientMsg.imputedRedZen += outItem.imputedRedZen;

			if (outItem.IsExistItem())
			{
				defaultSelectedItems.AppendOrUpdate(ItemStackInfo(outItem.itemIndex, outItem.itemEnchantLv), outItem.itemCount);
				resultItems.AppendOrUpdate(ItemStackInfo(outItem.itemIndex, outItem.itemEnchantLv), outItem.itemCount);
			}

			if (outItem.IsExistPetIndex())
			{
				PetData pet;
				pet.type = outItem.petIndex;
				clientMsg.petList.push_back(pet);
			}
		}

		VERIFY_RETURN(defaultSelectedItems.IsValid(), ErrorItem::E_INVALID_SCRIPT);

		ErrorItem::Error eError = CheckTotalGambleList(*owner, clientMsg, m_lastSystemMsg);
		VERIFY_RETURN(eError == ErrorItem::SUCCESS, eError);

		// 보상 아이템들을 대상 인벤토리내의 슬롯에 캐싱 한다. - 이런 경우는 나올 수 없지만 예외 처리해 준다 !!! - kangms
		if (false == slotFinder.Find(defaultSelectedItems))
		{
			m_lastSystemMsg.assign(L"Msg_ItemUse_err_inventory_full");
			return ErrorItem::E_REFER_SYSTEM_MSG;
		}

		++usedItemCount;
	}

	// 클라이언트에 보낼 아이템 기본 정보와 서버의 아이템 정보를 이용하여 아이템 Bag과 정보를 만든다.

	size_t itemBagCount = std::max<size_t>(resultItems.GetWishFindIndexs().size(), clientMsg.petList.size());
	auto& iterItem = resultItems.GetWishFindIndexs().begin();
	auto& iterPet = clientMsg.petList.begin();
	for (auto i = 0; i < itemBagCount; ++i)
	{
		GambleBagInfo info;
		if (iterItem != resultItems.GetWishFindIndexs().end())
		{
			info.itemIndex = iterItem->second.key.GetItemIndex();
			info.itemEnchantLv = iterItem->second.key.GetItemEnchantLevel();
			info.itemCount = iterItem->second.itemCount;

			// 클라이언트를 위한 아이템 정보 생성
			ItemData itemData;
			itemData.indexItem = info.itemIndex;
			itemData.enchant = info.itemEnchantLv;
			itemData.SetCurStackCount(info.itemCount);			
			clientMsg.items.emplace_back(itemData);

			++iterItem;
		}

		if (iterPet != clientMsg.petList.end())
		{
			info.petIndex = (*iterPet).petId.index;
			++iterPet;
		}
		
		outputs.bagList.emplace_back(info);
	}

	// 돈을 넣기위한 아이템 bag 생성
	if (clientMsg.money > 0 || clientMsg.greenZen > 0 || clientMsg.imputedRedZen > 0 || clientMsg.redZen > 0)
	{
		if (outputs.bagList.empty())
		{
			outputs.bagList.emplace_back(GambleBagInfo());
		}
		auto& mainBag = outputs.bagList.begin();
		mainBag->zen = clientMsg.money;
		mainBag->greenZen = clientMsg.greenZen;
		mainBag->redZen = clientMsg.redZen;
		mainBag->imputedRedZen = clientMsg.imputedRedZen;
	}


	if (1 < useTimes)
	{
		m_lastSystemMsg.assign(L"Msg_BoxMultiOpen_notice");
		m_systemMsgParam = usedItemCount;
	}

	return error;
}

ErrorItem::Error ActionPlayerUseItem::checkSelectGambleOutputData( const GambleOutputs &outputs, const GambleBagInfo* bagInfo )
{
	VALID_RETURN(bagInfo!=nullptr, ErrorItem::E_NOT_EXIST);

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), ErrorItem::playerNotFound);

	Zen totalZen = 0;
	GreenZen totalGreenZen = 0;
	ErrorItem::Error eError =  ErrorItem::SUCCESS;
	UInt32 needPetSlot = 0;

	ActionPlayerPetManage* actionPet = GetEntityAction(owner);
	VERIFY_RETURN(actionPet, ErrorItem::E_FAILED );

	const ItemInfoElem* itemElem = SCRIPTS.GetItemInfoScript( bagInfo->itemIndex );
	VALID_RETURN(itemElem, ErrorItem::E_NOT_EXIST_ITEM );

	
	SlotFinderByStackInfo::IndexsContainer itemIndexs;
	SlotFinderByStackInfo slotFinder(*owner);	//슬롯인벤이면서, 여유스택과 빈슬롯을 같이 검색한다.

	itemIndexs.AppendOrUpdate(ItemStackInfo(bagInfo->itemIndex, bagInfo->itemEnchantLv), bagInfo->itemCount);
	
	for (auto gi : outputs.bagList)
	{
		if (gi.IsExistZen())
		{
			Zen checkTotalZen = totalZen + gi.zen;
			if (false == owner->CheckZen( checkTotalZen, ChangedMoney::ADD )) {
				eError = ErrorItem::E_REFER_SYSTEM_MSG;
				m_lastSystemMsg.assign( L"Msg_Zen_Err_Max" );
				break;
			}

			totalZen += gi.zen;
		}

		if (gi.IsExistGreenZen())
		{
			GreenZen checkTotalGreenZen = totalGreenZen + gi.greenZen;
			if (false == owner->CheckGreenZen( checkTotalGreenZen, ChangedMoney::ADD )) {
				eError = ErrorItem::E_REFER_SYSTEM_MSG;
				m_lastSystemMsg.assign( L"Msg_GreenZen_Err_Max" );
				break;
			}

			totalGreenZen += gi.greenZen;
		}

		if (gi.IsExistItem())
		{
			itemIndexs.AppendOrUpdate(ItemStackInfo(gi.itemIndex, gi.itemEnchantLv), gi.itemCount);
		}

		if (gi.IsExistPetIndex())
		{
			auto checkNeedPetSlot = needPetSlot + 1;
			if (false == actionPet->CanRegister( checkNeedPetSlot ))
			{
				eError = ErrorItem::E_REFER_SYSTEM_MSG;
				m_lastSystemMsg.assign( L"Msg_ItemUse_err_inventory_full" );
				break;
			}
			needPetSlot++;
		}
	}

	if (eError == ErrorItem::SUCCESS &&
		true != slotFinder.Find(itemIndexs))
	{
		eError = ErrorItem::E_REFER_SYSTEM_MSG;
		m_lastSystemMsg.assign( L"Msg_ItemUse_err_inventory_full" );
	}

	return eError;
}

ErrorItem::Error ActionPlayerUseItem::supplyGambleDatas(const ItemConstPtr& item, const GambleOutputs& addItems, UInt16 useItemCount, ItemBag &bag, TransactionPtr &trans ) const
{
	VERIFY_RETURN(item, ErrorItem::E_NOT_EXIST_ITEM );
	VERIFY_RETURN(useItemCount, ErrorItem::E_CHECK_GAMBLE_OPEN_COUNT );

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	ItemGambleTransaction *itemGambleTransaction = static_cast<ItemGambleTransaction*>(trans.get());
	VERIFY_RETURN(itemGambleTransaction, ErrorItem::invalidTran);

	const ItemOptionScript* scriptItemOption = SCRIPTS.GetScript<ItemOptionScript>();
	VERIFY_RETURN(scriptItemOption, ErrorItem::scriptNotFound );

	ErrorItem::Error eError = ErrorItem::SUCCESS;

	// 사용될 아이템 갯수가 있을 경우
	if (0 < useItemCount)
	{
		eError = player->GetInventoryAction().RemoveByPlace(item->GetPlace(), bag, useItemCount);

		if (ErrorItem::SUCCESS != eError)
		{
			MU2_WARN_LOG( LogCategory::DEBUG_GAMBLE, "Failed to remove gamble item by Item count !!! : error(%u), itemCount(%d), item(%s), player(%s)"
				, eError, useItemCount, item->ToString().c_str(), player->ToString().c_str() );

			return eError;
		}
	}

	// 뽑은 아이템을 추가한다.
	for (const auto& gi : addItems.bagList)
	{
		if (gi.IsExistZen())
		{
			itemGambleTransaction->AddMoney( gi.zen );
		}

		if (gi.IsExistGreenZen())
		{
			itemGambleTransaction->AddGreenZen( gi.greenZen );
		}

		if (gi.IsExistItem()) 
		{
			EnchantLevel enchantLv = std::min<EnchantLevel>( gi.itemEnchantLv, scriptItemOption->MaxEnchantLevel( gi.itemIndex ) );
			ItemData data( gi.itemIndex, gi.itemCount );
			data.enchant = enchantLv;

			if (false == bag.Insert( data ) ) 
			{
				MU2_WARN_LOG( LogCategory::DEBUG_GAMBLE, "UseGambleItem > inventory push failed. user(%d:%s), itemIndex(%u), overlap(%u), error(%d)"
					, player->GetCharId(), player->GetCharName().c_str(), gi.itemIndex, gi.itemCount, eError );
				continue;
			}
		}

		if (gi.IsExistPetIndex())
		{
			PetData pet;

			player->GetPetManageAction().CreateData( gi.petIndex, pet );
			itemGambleTransaction->AddPet( pet );
		}

		if (gi.IsExistRedZen())
		{
			itemGambleTransaction->AddRedZen( gi.redZen );
		}

		if (gi.IsExistImputedRedZen())
		{
			itemGambleTransaction->AddImputedRedZen( gi.imputedRedZen );
		}
	}
	
	return eError;
}

void ActionPlayerUseItem::getSelectGambleBag( VecSelectGambleInfo &infos ) const
{
	infos.clear();

	const GambleOutputs& gambleOutputs = m_gambleOutputs;

	for (auto gambleBagIter : gambleOutputs.selectGambleLists)
	{
		const GambleBagElem* elem = SCRIPTS.GetGambleBagElem( gambleBagIter );
		VERIFY_DO( elem, continue);
		VALID_DO( elem->selectPackage.size(), continue );
		
		Index32 selectIndex = 0;
		for (auto iter : elem->selectPackage)
		{
			SelectGambleInfo info;

			info.gambleBagIndex = elem->gambleBagIndex;
			info.itemIndex = iter.itemIndex;
			info.stackCount = iter.itemCount;
			info.itemEnchantLv = iter.itemEnchantLv;
			info.selectIndex = ++ selectIndex;

			infos.emplace_back( info );
		}
	}
}

const GambleBagInfo* ActionPlayerUseItem::getGambleBagInfo(const GambleOutputs& outputs, const Index32 bagIndex, const Index32 selectIndex)
{
	auto bagIter = std::find_if(outputs.selectGambleLists.begin(), outputs.selectGambleLists.end(),
		[bagIndex](const Index32& index) { return bagIndex == index; });

	VERIFY_RETURN(bagIter != outputs.selectGambleLists.end(), nullptr);

	const GambleBagElem* elem = SCRIPTS.GetGambleBagElem(*bagIter);
	VERIFY_RETURN(elem, nullptr);

	Index32 sequence = 0;
	auto found = elem->selectPackage.begin();
	for (; found != elem->selectPackage.end(); ++found)
	{
		if (++sequence == selectIndex)
		{
			return (&*found);
		}
	}

	return nullptr;
}


ErrorItem::Error ActionPlayerUseItem::UseItemRecoveryEntrance( const ItemPos& pos )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	const ItemConstPtr item = player->GetInventoryAction().GetItem( pos);
	VERIFY_RETURN( item, ErrorItem::itemNotFound );

	// 입장 회복용 아이템만 가능
	VERIFY_RETURN( ItemDivisionType::RECOVERY_ENTRANCE_COUNT == item->GetDivision(), ErrorItem::E_FAILED );

	ErrorItem::Error eError = checkAndSet( item );
	VERIFY_RETURN( ErrorItem::SUCCESS == eError, eError );

	// 월드 서버에 회복을 요청한다.
	auto elem = SCRIPTS.GetItemRecoveryEntranceCountElem( item->GetItemIndex());
	VERIFY_RETURN( elem && elem->IsValid(), ErrorItem::E_INVALID_TYPE);

	TransactionPtr transPtr( NEW ItemRecoveryEntranceTransaction(__FUNCTION__, __LINE__, player, LogCode::E_ITEM_USE_RECOVERY_ENTRANCE, elem ) );
	{
		ItemBag bag(*player);

		eError = player->GetInventoryAction().RemoveByPlace(item->GetPlace(), bag, 1);
		VERIFY_RETURN(eError == ErrorItem::SUCCESS, eError);

		VERIFY_RETURN(bag.Write(transPtr), ErrorItem::BagWriteFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::E_TRANSACION_FAILED);
	}

	processAfterUseItem( item );
	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerUseItem::UsePetRegisterItem( const ItemPos& itemPos )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);
	
	const ItemConstPtr item = player->GetInventoryAction().GetItem( itemPos );
	VERIFY_RETURN( item, ErrorItem::E_FAILED );
	VERIFY_RETURN( ItemDivisionType::PET == item->GetDivision(), ErrorItem::E_FAILED );

	UseItem::InputValue value( player );
	ErrorItem::Error eError = checkAndSet( item, &value );
	VERIFY_RETURN( ErrorItem::SUCCESS == eError, eError );
	
	VERIFY_RETURN( player->GetPetManageAction().CanRegister( 1 ), ErrorItem::E_FAILED_INSERT );
	VERIFY_RETURN( value.elem && value.elem->skillEffectElems[0], ErrorItem::E_FAILED );

	ItemPetRegisterTransaction* transaction = NEW ItemPetRegisterTransaction(__FUNCTION__, __LINE__, player, LogCode::E_ITEM_USE_REGISTER_PET, value.elem->skillEffectElems[0]->value[0] );
	TransactionPtr transPtr( transaction );
	{
		ItemBag bag(*player);

		eError = player->GetInventoryAction().RemoveByPlace(item->GetPlace(), bag, 1);
		VERIFY_RETURN(eError == ErrorItem::SUCCESS, eError);

		VERIFY_RETURN(bag.Write(transPtr), ErrorItem::BagWriteFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::E_TRANSACION_FAILED);
	}

	processAfterUseItem( item );

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerUseItem::UseSkillRuneItem( const ItemPos& itemPos, Bool isConfirm )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	const ItemConstPtr item = player->GetInventoryAction().GetItem( itemPos );
	VERIFY_RETURN( item, ErrorItem::itemNotFound );
	VERIFY_RETURN( ItemDivisionType::SKILL_RUNE == item->GetDivision(), ErrorItem::E_INVALID_DIVISION );// 스킬 룬 아이템만 가능

	ErrorItem::Error eError = checkAndSet( item );
	VERIFY_RETURN( ErrorItem::SUCCESS == eError, eError );

	ItemSkillRuneTransaction* transaction = NEW ItemSkillRuneTransaction(__FUNCTION__, __LINE__, player, LogCode::E_ITEM_USE_SKILL_RUNE, item->GetInfoElem(), isConfirm );
	TransactionPtr transPtr( transaction );
	{
		ItemBag bag(*player);

		eError = player->GetInventoryAction().RemoveByPlace(item->GetPlace(), bag, 1);
		VERIFY_RETURN(eError == ErrorItem::SUCCESS, eError);

		VERIFY_RETURN(bag.Write(transPtr), ErrorItem::BagWriteFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::E_TRANSACION_FAILED);
	}

	processAfterUseItem( item );

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerUseItem::UsePortalStoneItem( const ItemPos& itemPos, const Bool selectDefault )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	if ( player->GetSkillControlAction().IsBattleMode() )
	{
		m_lastSystemMsg = L"Msg_Town_Portal_Cannot_Use";
		return ErrorItem::E_REFER_SYSTEM_MSG;
	}	

	const ItemConstPtr item = player->GetInventoryAction().GetItem( itemPos );
	VERIFY_RETURN( item, ErrorItem::itemNotFound );
	VERIFY_RETURN( ItemDivisionType::PORTAL_STONE == item->GetDivision(), ErrorItem::E_FAILED );

	ErrorItem::Error eError = checkAndSet( item );
	VERIFY_RETURN( ErrorItem::SUCCESS == eError, eError );

	IndexPosition characterPositionIndex;
	if ( selectDefault )
	{
		characterPositionIndex = SCRIPTS.GetTownPortalDefaultPosition();
		IndexContinent continent = SCRIPTS.CastPositionToContinentWithNoCheck(characterPositionIndex);

		const IndexPortals& vecExploredPortals = player->GetPlayerAttr().exploredPortals;

		auto iter = std::find_if(vecExploredPortals.begin(), vecExploredPortals.end(), 
			[continent]( const IndexPortal& value ) { return (SCRIPTS.CastPositionToContinentWithNoCheck(value) == continent );} );

		VERIFY_RETURN( iter != vecExploredPortals.end(), ErrorItem::E_FAILED );
	}
	else
	{
		const TownPortalElem* townPortalElem = SCRIPTS.GetTownPortalElem( player->GetPlayerAttr().currentContinent );
		VERIFY_RETURN( townPortalElem, ErrorItem::E_INVALID_SCRIPT );

		characterPositionIndex = townPortalElem->characterPositionIndex;
	}

	const PositionElem* charPositionElem = SCRIPTS.GetPositionElem(characterPositionIndex);
	VERIFY_RETURN(charPositionElem, ErrorItem::scriptNotFound);

	ItemPortalStoneTransaction* transaction = NEW ItemPortalStoneTransaction(__FUNCTION__, __LINE__, player, LogCode::E_ITEM_USE_PORTAL_STONE, characterPositionIndex );
	TransactionPtr transPtr( transaction );
	{
		ItemBag bag(*player);

		if (item->IsConsumable())
		{
			eError = player->GetInventoryAction().RemoveByPlace(item->GetPlace(), bag, 1);
			VERIFY_RETURN(eError == ErrorItem::SUCCESS, eError);
		}

		VERIFY_RETURN(bag.Write(transPtr), ErrorItem::BagWriteFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::E_TRANSACION_FAILED);
	}
	
	processAfterUseItem( item );

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerUseItem::UseChangeSkillRuneItem( const ItemPos& itemPos, IndexSkill skillId, UInt8 slot, const SkillRuneStorageKey& key )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);
		
	const ItemConstPtr item = player->GetInventoryAction().GetItem( itemPos );
	VERIFY_RETURN( item, ErrorItem::itemNotFound );
	VERIFY_RETURN( ItemDivisionType::SKILL_RUNE_EXTRACT ==  item->GetDivision(), ErrorItem::E_INVALID_DIVISION );
	
	const ActionPlayerSkillRune::RuneInfo* info = player->GetSkillRuneAction().GetSlot( skillId );
	VERIFY_RETURN( info, ErrorItem::runeInfoNotFound );
	VERIFY_RETURN( slot < info->runes.size(), ErrorItem::runeInfoNotFound);
	VERIFY_RETURN( info->runes[ slot ].elem, ErrorItem::runeInfoNotFound);
	VERIFY_RETURN( player->GetSkillRuneAction().IsEquipable( skillId, slot, key.runeId, key.indexItem ), ErrorItem::E_FAILED );
	VERIFY_RETURN( player->GetSkillRuneAction().HasSkillRune( skillId, key ), ErrorItem::E_FAILED );

	ErrorItem::Error eError = checkAndSet( item );
	VERIFY_RETURN( ErrorItem::SUCCESS == eError, eError );
	
	ItemChangeSkillRuneTransaction* transaction = NEW ItemChangeSkillRuneTransaction(__FUNCTION__, __LINE__, player, LogCode::E_ITEM_USE_CHANGE_SKILL_RUNE, skillId, slot, key );
	TransactionPtr transPtr( transaction );
	{
		ItemBag bag(*player);

		eError = player->GetInventoryAction().RemoveByPlace(item->GetPlace(), bag, 1);
		VERIFY_RETURN(eError == ErrorItem::SUCCESS, eError);

		VERIFY_RETURN(bag.Write(transPtr), ErrorItem::BagWriteFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::E_TRANSACION_FAILED);
	}

	processAfterUseItem( item );

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerUseItem::Use( UseItem::InputValue& iValue )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);		

	const ItemConstPtr item = player->GetInventoryAction().GetItem( iValue.GetSourceItemPos() );
	VERIFY_RETURN(item, ErrorItem::itemNotFound);

	return use( item, iValue );
}

ErrorItem::Error ActionPlayerUseItem::UseMegaphoneItem( const ItemPos& itemPos, const std::wstring &msg, const UInt16 languageCode, const ChatIconType::Enum iconType )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);
	
	const ItemConstPtr item = player->GetInventoryAction().GetItem(itemPos);
	VERIFY_RETURN(item, ErrorItem::itemNotFound);
	VERIFY_RETURN(ItemDivisionType::MEGAPHONE == item->GetDivision(), ErrorItem::E_FAILED);
	
	VALID_RETURN( CheckCoolTime( item ), ErrorItem::E_COOL );

	UseItem::InputValue value(GetOwnerPlayer());

	ErrorItem::Error eError = use( item, value, true );
	VERIFY_RETURN(ErrorItem::SUCCESS == eError, eError);

	ItemMegaphoneTransaction* transaction = NEW ItemMegaphoneTransaction( __FUNCTION__, __LINE__, player, LogCode::E_ITEM_USE_MEGAPHONE, itemPos, msg, languageCode, iconType );
	TransactionPtr transPtr(transaction);
	{
		ItemBag bag(*player);

		eError = player->GetInventoryAction().RemoveByPlace(item->GetPlace(), bag, 1);
		VERIFY_RETURN(eError == ErrorItem::SUCCESS, eError);

		VERIFY_RETURN(bag.Write(transPtr), ErrorItem::BagWriteFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::E_TRANSACION_FAILED);
	}

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerUseItem::UseChangeCharName(const ItemPlace& itemPlace, const std::wstring newName)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	Sector* sector = player->GetSector();
	VERIFY_RETURN(sector, ErrorItem::sectorNotFound);

	VERIFY_RETURN( sector->GetInstanceType() == InstanceSectorType::TOWN, ErrorItem::E_NOT_USE_ZONE );
	VERIFY_RETURN( newName.length() <= Limits::MAX_CHAR_NAME_LENGTH, ErrorItem::E_OVERLENGTH_NAME );
	VERIFY_RETURN( newName.length() >= Limits::MIN_CHAR_NAME_LENGTH , ErrorItem::E_INVALID_NAME );

	const FilterData & filter = SCRIPTS.GetFilterDatas();
	VERIFY_RETURN( filter.Check( FilterData::IMPOSSIBLE_NAME, newName ), ErrorItem::E_INVALID_NAME );

	const ItemConstPtr item = player->GetInventoryAction().GetItemByPlace(itemPlace);
	VERIFY_RETURN( item, ErrorItem::itemNotFound );

	ErrorItem::Error eError = checkAndSet( item, NULL );
	VERIFY_RETURN( ErrorItem::SUCCESS == eError, eError );

	VERIFY_RETURN( item->GetDivision() == ItemDivisionType::CHAR_CHANGE_NAME, ErrorItem::E_NOT_EXIST );

	ItemCharNameChangeTransaction* transaction = NEW ItemCharNameChangeTransaction(__FUNCTION__, __LINE__, player, LogCode::E_ITEM_USE_CHAR_NAME_CHANGE, player->GetAccountId(), player->GetCharId(), player->GetCharName(), newName);
	TransactionPtr transPtr(transaction);
	{
		ItemBag bag(*player);

		if (item->IsConsumable() )
		{
			eError = player->GetInventoryAction().RemoveByPlace(item->GetPlace(), bag, 1);
			VERIFY_RETURN(eError == ErrorItem::SUCCESS, eError);
		}

		VERIFY_RETURN(bag.Write(transPtr), ErrorItem::BagWriteFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::TranRegisterFailed);
	}

	processAfterUseItem( item );

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerUseItem::UseChangeKnightName(const ItemPlace& itemPlace, const std::wstring newName)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	Sector* sector = player->GetSector();
	VERIFY_RETURN(sector, ErrorItem::sectorNotFound);
	VERIFY_RETURN(sector->GetInstanceType() == InstanceSectorType::TOWN, ErrorItem::E_NOT_USE_ZONE);
		
	VERIFY_RETURN(player->GetKnightageAction().IsMaster(), ErrorItem::KnightInvalidMaster);
	VERIFY_RETURN(newName.length() <= Limits::MAX_KNIGHTAGE_NAME_LENGTH, ErrorItem::E_OVERLENGTH_NAME);

	// 계정 권한에 따라서 이름 수정 제한 풀기
	const FilterData & filter = SCRIPTS.GetFilterDatas();
	VALID_RETURN(filter.Check(FilterData::IMPOSSIBLE_NAME, newName), ErrorItem::E_INVALID_NAME);
		
	const ItemConstPtr item = player->GetInventoryAction().GetItemByPlace(itemPlace);
	VERIFY_RETURN(item, ErrorItem::itemNotFound);
	VERIFY_RETURN(item->GetDivision() == ItemDivisionType::KNIGHT_CHANGE_NAME, ErrorItem::E_NOT_EXIST);

	ErrorItem::Error eError = checkAndSet( item, NULL );
	VERIFY_RETURN( ErrorItem::SUCCESS == eError, eError );	

	ItemKnightageNameChangeTransaction* transaction = NEW ItemKnightageNameChangeTransaction(__FUNCTION__, __LINE__, player, LogCode::E_ITEM_USE_KNIGHT_NAME_CHANGE, player->GetKnightageId(), newName);
	TransactionPtr transPtr(transaction);
	{
		ItemBag bag(*player);

		if (item->IsConsumable())
		{
			eError = player->GetInventoryAction().RemoveByPlace(item->GetPlace(), bag, 1);
			VERIFY_RETURN(eError == ErrorItem::SUCCESS, eError);;
		}

		VERIFY_RETURN(bag.Write(transPtr), ErrorItem::BagWriteFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::TranRegisterFailed);
	}

	processAfterUseItem( item );

	return ErrorItem::SUCCESS;
}



ErrorItem::Error ActionPlayerUseItem::UsePeriodExpandItem( const ItemPos& sourcePos, const ItemPos &targetpos )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);
	
	const ItemConstPtr srcItem = player->GetInventoryAction().GetItem( sourcePos);
	VERIFY_RETURN( srcItem, ErrorItem::itemNotFound );

	ErrorItem::Error eError = checkAndSet( srcItem );
	VERIFY_RETURN( ErrorItem::SUCCESS == eError, eError );

	const ItemConstPtr targetItem = player->GetInventoryAction().GetItem( targetpos);
	VERIFY_RETURN( targetItem, ErrorItem::itemNotFound );
	VERIFY_RETURN( targetItem->GetPeriodType() != PeriodType::NONE, ErrorItem::E_NOT_PERIOD_ITEM );
	VERIFY_RETURN( false == targetItem->IsBlockLimitedExtension(), ErrorItem::E_NOT_PERIOD_ITEM);
#ifdef __lockable_item_181001
	VALID_DO( targetItem->IsUnLocakble(), SendSystemMsg( player, SystemMessage( L"sys", L"Msg_Item_locked_not_usable" ) ); return ErrorItem::E_ITEM_LOCKED );
#endif
	const LimitedextensionItemInfo *info = SCRIPTS.GetLimitedextendsionItemInfo(srcItem->GetItemIndex());
	VERIFY_RETURN(info, ErrorItem::E_NOT_PERIOD_ITEM);

	DateTime expirableDate = targetItem->period.SimulateExpirableDate() + DateTimeSpan(0, 0, info->addMinute, 0);
	VERIFY_RETURN( expirableDate <= DateTime::GetPresentTime() + DateTimeSpan(365, 0, 0, 0), ErrorItem::E_PERIOD_OVER);

	if (srcItem->GetDivision() == ItemDivisionType::MAGIC_CLOTH)
	{
		VERIFY_RETURN(targetItem->GetDivision() == ItemDivisionType::COSTUME, ErrorItem::E_NOT_EXIST);
	}
	else if (srcItem->GetDivision() == ItemDivisionType::MAGIC_FEATHER)
	{
		VERIFY_RETURN(targetItem->GetDivision() == ItemDivisionType::COSTUME_WING, ErrorItem::E_NOT_EXIST);
	}
	else
	{
		return ErrorItem::E_NOT_EXIST;
	}

	ItemPeriodExpandTransaction* transaction = NEW ItemPeriodExpandTransaction(__FUNCTION__, __LINE__, player, LogCode::E_ITEM_USE_PERIOD_EXPAND);
	TransactionPtr transPtr(transaction);
	{
		ItemBag bag(*player);

		if (srcItem->IsConsumable())
		{
			eError = player->GetInventoryAction().RemoveByPlace(srcItem->GetPlace(), bag, 1);
			VERIFY_RETURN(eError == ErrorItem::SUCCESS, eError);
		}

		ItemData data;
		targetItem->FillUpItemData(data);
		data.period.AddPeriodByMinute(info->addMinute);

		VERIFY_RETURN(bag.Update(targetItem, data), ErrorItem::BagUpdateFailed);
		VERIFY_RETURN(bag.Write(transPtr), ErrorItem::BagWriteFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::TranRegisterFailed);
	}

	processAfterUseItem(srcItem);
	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerUseItem::UseEquipTranscendStone( const ItemPos& sourcePos, const ItemPos &targetPos, const TStoneSlot equipSlot )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	ActionPlayerInventory& invenAction = player->GetInventoryAction();
	
	const ItemConstPtr sourceItem = invenAction.GetItem(sourcePos);
	VERIFY_RETURN( sourceItem, ErrorItem::itemNotFound );
	VERIFY_RETURN( ItemDivisionType(sourceItem->GetDivision()).IsTstone(), ErrorItem::E_INVALID_DIVISION );	
	
	ErrorItem::Error eError = checkAndSet( sourceItem, NULL );
	VERIFY_RETURN( ErrorItem::SUCCESS == eError, eError );

	ItemData sourceItemCloned = sourceItem->Clone();
	ItemDivisionType srcDivision(sourceItem->GetDivision());

	if (sourceItemCloned.enchant == 0)
	{
		SendSystemMsg( GetOwnerPlayer(), SystemMessage( L"sys", L"Msg_Item_Err_default_t_stone" ) );
		return ErrorItem::E_UNKNOWN;
	}

	if (sourceItemCloned.abilityPackMap.size() <= 0)
	{
		const TStoneElem * tStoneElem = SCRIPTS.GetTStoneElem( sourceItemCloned.indexItem );
		VERIFY_RETURN( tStoneElem, ErrorItem::scriptNotFound );

#ifdef ABILITY_RENEWAL_20180508
		SelectAbilityPack pack;
		pack.type = tStoneElem->optionType;
#else
		AbilityPack pack;
		pack.type = tStoneElem->optionType;
#endif
		sourceItemCloned.abilityPackMap.emplace( tStoneElem->optionType, pack );
	}

	const ItemConstPtr targetItem = invenAction.GetItem( targetPos);
	VERIFY_RETURN( targetItem, ErrorItem::itemNotFound );
	VERIFY_RETURN( targetItem->GetMaxTranscendCount() && equipSlot < targetItem->GetMaxTranscendCount(), ErrorItem::invalidTranscednSocket );
	VERIFY_RETURN( targetItem->GetOptionElem(), ErrorItem::OptionElemNotFound );
#ifdef __lockable_item_181001
	VALID_DO( targetItem->IsUnLocakble(), SendSystemMsg( player, SystemMessage( L"sys", L"Msg_Item_locked_not_usable" ) ); return ErrorItem::E_ITEM_LOCKED );
#endif
	const TstoneAttachItemElem *attachItemElem = nullptr;

		 if(srcDivision.IsTstoneRed())		attachItemElem = SCRIPTS.GetTStoneAttatchItem( TstoneAttachItemElem::Red );
	else if(srcDivision.IsTstoneBlue())		attachItemElem = SCRIPTS.GetTStoneAttatchItem( TstoneAttachItemElem::Blue );
	else if(srcDivision.IsTstoneGreen())	attachItemElem = SCRIPTS.GetTStoneAttatchItem( TstoneAttachItemElem::Greeen );
	
	VERIFY_RETURN( attachItemElem, ErrorItem::E_NOT_EQUIP_ITEM );
	VERIFY_RETURN( attachItemElem->weaponType.find( targetItem->GetOptionElem()->eWeaponType ) != attachItemElem->weaponType.end(), ErrorItem::E_NOT_EQUIP_ITEM );

	const TStoneFusionElem* fusionElem = SCRIPTS.GetTStoneFusionElem( sourceItemCloned.indexItem );
	VERIFY_RETURN( fusionElem, ErrorItem::elemNotFound );

	ItemEquipTranscendStone* transaction = NEW ItemEquipTranscendStone(__FUNCTION__, __LINE__, player, LogCode::E_EQUIP_TRANSCEND_STONE, targetPos, *sourceItem );
	TransactionPtr transPtr( transaction );
	{
		ItemBag bag(*player);

		switch (fusionElem->costType)
		{
		case TStoneElem::CostType::Zen:
			VERIFY_RETURN(player->IsEnoughZen (fusionElem->costValue), ErrorItem::E_NOT_ENOUGH_ZEN);
			transPtr->BindMoney(player->GetCharId(), fusionElem->costValue, ChangedMoney::SUB, ChangedMoney::ZEN);
			break;
		case TStoneElem::CostType::GreenZen:
			VERIFY_RETURN(player->IsEnoughGreenZen(fusionElem->costValue), ErrorItem::E_NOT_ENOUGH_GREEN_ZEN);
			transPtr->BindMoney(player->GetCharId(), fusionElem->costValue, ChangedMoney::SUB, ChangedMoney::GREENZEN);
			break;
		case TStoneElem::CostType::RedZen:
			break;
		}

		eError = invenAction.RemoveByPlace(sourceItem->GetPlace(), bag, 1);
		VERIFY_RETURN(eError == ErrorItem::SUCCESS, eError);

		ItemData data;
		AbilityPack abilityValue;
		{
			targetItem->FillUpItemData(data);

			ItemFactory::CheckAndBind(player, targetItem, EquipBindState::EQUIP_TRANCEND_STONE, data.bindProperty);			

			auto iter = data.transcendStoneInfoMap.find(equipSlot);
			if (iter != data.transcendStoneInfoMap.end())
			{
				iter->second.stoneItemIndex = sourceItemCloned.indexItem;
				iter->second.enchantCount = sourceItemCloned.enchant;
				iter->second.enchantFailCount = sourceItemCloned.enchantFailCount;
#ifdef ABILITY_RENEWAL_20180508
				iter->second.type = sourceItemCloned.abilityPackMap.begin()->second.type;
				iter->second.point = sourceItemCloned.abilityPackMap.begin()->second.point;
				iter->second.percent = sourceItemCloned.abilityPackMap.begin()->second.percent;
#else
				iter->second.ability = sourceItemCloned.abilityPackMap.begin()->second;
				abilityValue = sourceItemCloned.abilityPackMap.begin()->second;
#endif
			}
			else
			{
#ifdef ABILITY_RENEWAL_20180508
				TranscendStoneInfo info;
#else
				ItemData::TranscendStoneInfo info;
#endif
				info.stoneItemIndex = sourceItemCloned.indexItem;
				info.enchantCount = sourceItemCloned.enchant;
				info.enchantFailCount = sourceItemCloned.enchantFailCount;
#ifdef ABILITY_RENEWAL_20180508
				info.type = sourceItemCloned.abilityPackMap.begin()->second.type;
				info.point = sourceItemCloned.abilityPackMap.begin()->second.point;
				info.percent = sourceItemCloned.abilityPackMap.begin()->second.percent;
#else
				info.ability = sourceItemCloned.abilityPackMap.begin()->second;
#endif
				abilityValue = sourceItemCloned.abilityPackMap.begin()->second;

				data.transcendStoneInfoMap.emplace(equipSlot, info);
			}
		}

		transaction->SetLogInfo(fusionElem->costType, fusionElem->costValue, targetItem->GetItemIndex(), targetItem->GetItemId(), abilityValue);

		VERIFY_RETURN(bag.Update(targetItem, data), ErrorItem::BagUpdateFailed);
		VERIFY_RETURN(bag.Write(transPtr), ErrorItem::BagWriteFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::TranRegisterFailed);
	}

	processAfterUseItem( sourceItem );

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerUseItem::use(const ItemConstPtr& item, UseItem::InputValue& value, Bool IsOnlyCoolCheck /*= false*/ )
{
	VERIFY_RETURN( item, ErrorItem::itemNotFound );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), ErrorItem::playerNotFound);
	
	ErrorItem::Error eError = checkAndSet( item, &value );
	VERIFY_RETURN( ErrorItem::SUCCESS == eError, eError );

	const SkillInfoElem* skillScript = SCRIPTS.GetSkillInfoScript( item->GetLinkedSkillIndex());
	if ( nullptr == skillScript )
	{
		MU2_WARN_LOG( core::LogCategory::SYSTEM, "invalid skillindex %d", item->GetLinkedSkillIndex() );
		return ErrorItem::elemNotFound;
	}

	UseItemPtr useItemPtr( createUseItem( skillScript->attackCastStyle, value, IsOnlyCoolCheck ) );
	VERIFY_RETURN( useItemPtr, ErrorItem::createUseItemFailed );

	// 일반 탈 것은 아이템 사용시 내려야 한다.
	if ( RidingSubType::NORMAL_RIDING == owner->GetBuffAction().GetRidingSubType() )
	{
		VERIFY_RETURN(owner->GetBuffAction().RidingCancel(), ErrorItem::RidingCancelFailed);
	}

	useItemPtr->GetOsm()->Start();
	registerUseItem( useItemPtr );

	ENtfGameSkillCoolTime* ntfCoolTime = NEW ENtfGameSkillCoolTime;
	ntfCoolTime->info.coolLineType = skillScript->coolLineType;
	ntfCoolTime->info.totalTime = value.coolTime;
	ntfCoolTime->info.remainTime = value.coolTime;
	SERVER.SendToClient( owner, EventPtr( ntfCoolTime ) );

	processAfterUseItem( item );

	return ErrorItem::SUCCESS;
}

void ActionPlayerUseItem::registerUseItem( const UseItemPtr& useItem )
{
	m_useItems.push_back( useItem );
}

Bool ActionPlayerUseItem::Update( const UInt32 age )
{
	VALID_RETURN( false == m_useItems.empty(), true );

	for ( auto i = m_useItems.begin() ; i != m_useItems.end(); )
	{
		if ( (*i)->IsRemovable() )
		{
			i = m_useItems.erase(i);
		}
		else
		{
			++i;
		}
	}

	for (auto &iter : m_useItems)
	{
		iter->OnTick( age );
	}

	return true;
}

Bool ActionPlayerUseItem::CheckCoolTime(const ItemConstPtr& item )
{
	VALID_RETURN( item, false );
	VALID_RETURN( false == m_useItems.empty(), true );
	VALID_RETURN( item->GetLinkedSkillIndex(), true );

	for(auto &iter : m_useItems)
	{
		VALID_RETURN( iter->CheckCoolTime( item ), false );
	}

	return true;
}

ErrorItem::Error ActionPlayerUseItem::CastingCancel( const ItemId& itemId )
{
	VALID_RETURN( false == m_useItems.empty(), ErrorItem::UseItemIsEmpty );

	for ( auto i = m_useItems.begin() ; i != m_useItems.end(); ++i )
	{
		if ( (*i)->Get().itemId == itemId )
		{
			(*i)->GetOsm()->End();
			return ErrorItem::SUCCESS;
		}
	}

	return ErrorItem::E_FAILED;
}

ErrorItem::Error ActionPlayerUseItem::CheckCastingnCancel()
{
	VALID_RETURN( false == m_useItems.empty(), ErrorItem::UseItemIsEmpty );

	for (  auto i = m_useItems.begin() ; i != m_useItems.end(); ++i )
	{
		if ( (*i)->GetOsm()->GetCurrnet()->GetId() == STATE_USEITEM_CAST )
		{	
			EResCastingCancel* res = NEW EResCastingCancel;
			res->itemId = (*i)->Get().itemId;			
			SERVER.SendToClient( GetOwnerPlayer(), EventPtr( res ) );

			(*i)->GetOsm()->End();
			return ErrorItem::SUCCESS;
		}
	}
	
	return ErrorItem::E_FAILED;
}

Bool ActionPlayerUseItem::checkClass( const ClassType::Enum classIdx ) const
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN( owner && owner->IsValid(), false );

	AttributePlayer* attr = GetEntityAttribute( owner );
	VALID_RETURN (attr, false);

	VALID_RETURN( classIdx != ClassType::NONE, true );
	return (classIdx & attr->eClassType) > 0;
}

Bool ActionPlayerUseItem::checkTargetRange( const SkillTargetType::Enum eTargetType, const Int32 range,
							 const EntityId& targetId, const Vector3& pos )
{
	EntityPlayer* ownPlayer = GetOwnerPlayer();
	VERIFY_RETURN(ownPlayer && ownPlayer->IsValid(), false);

	switch( eTargetType )
	{
	case SkillTargetType::NONE:
		return true;
	case SkillTargetType::MYSELF:
		return true;
	case SkillTargetType::TARGET:
		{
			VALID_RETURN( 0 < targetId, false );

			Entity4Zone* target = ownPlayer->GetSectorAction().GetEntityInMySector( targetId );
			VALID_RETURN( target, false );

			ViewPosition* viewTargetPos = static_cast< ViewPosition* >( target->GetView( VIEW_POSITION ) );
			VALID_RETURN( viewTargetPos, false);

			ViewPosition* viewOwnerPos = static_cast< ViewPosition* >( GetOwnerPlayer()->GetView( VIEW_POSITION ) );
			VALID_RETURN( viewOwnerPos, false );

			Vector3 targetPos, ownerPos;
			VALID_RETURN( viewTargetPos->GetPosition( targetPos ), false );
			VALID_RETURN( viewOwnerPos->GetPosition( ownerPos ), false );
			VALID_RETURN( ((Int32)(targetPos - ownerPos).Length()) <= (range + 100), false );	// +100 은 보정값임

			return true;
		}
		break;
	case SkillTargetType::POINT:
	case SkillTargetType::TARGET_POINT:	
		{
			if ( pos.x == 0 && pos.y == 0 && pos.z == 0 )
			{
				return false;
			}

			ViewPosition* viewOwnerPos = static_cast< ViewPosition* >( GetOwnerPlayer()->GetView( VIEW_POSITION ) );
			VALID_RETURN( viewOwnerPos, false );
			
			Vector3 ownerPos;
			VALID_RETURN( viewOwnerPos->GetPosition( ownerPos ), false );
			VALID_RETURN( (ownerPos - pos).Length() <= range, false );

			return true;
		}
		break;
	case SkillTargetType::DIRECTION:
	case SkillTargetType::TARGET_DIRECTION:
		return true;
	default:
		return false;
	}
}

Bool ActionPlayerUseItem::checkVolume( UInt32 index )
{
	VALID_RETURN( 0 < index, true );

	const QuestVolumeMatchElem* elem = SCRIPTS.GetQuestVolume( index );
	VERIFY_RETURN( elem, false );

	ActionSector* sectorAct = GetEntityAction( GetOwnerPlayer() );
	VERIFY_RETURN( sectorAct, false );
	VALID_RETURN( sectorAct->CheckInsideVolume( elem->indexZone, elem->volumeId ), false );

	return true;
}

Bool  ActionPlayerUseItem::checkUnusableType( IDT::Enum divisionType )
{
	ViewPosition* posView = GetEntityView( GetOwnerPlayer() );
	VERIFY_RETURN( posView, true );

	ExecutionZoneId execZoneId;
	posView->GetExecZoneId( execZoneId );

	const WorldElem* worldElem = SCRIPTS.GetWorldScript( execZoneId.GetZoneIndex() );
	VALID_RETURN( worldElem, true );

	auto it = std::find( worldElem->disableItemList.begin(), worldElem->disableItemList.end(), divisionType );
	if ( it != worldElem->disableItemList.end() )
	{
		SendSystemMsg( GetOwnerPlayer(), SystemMessage( L"sys", L"Msg_Item_Invalid_Set_Item" ) );
		return true;
	}
	
	return false;
}

Bool ActionPlayerUseItem::checkColosseum(const ItemConstPtr& item)
{
	VERIFY_RETURN( item, false );
	VALID_RETURN( item->IsUsable(), true );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), false);
			
	const WorldElem* worldElem = SCRIPTS.GetWorldScript(owner->GetCurrZoneIndex());
	VERIFY_RETURN(worldElem, false);

	if (worldElem->instanceType == InstanceSectorType::COLOSSEUM)
	{
		SendSystemMsg(owner, SystemMessage(L"sys", L"Msg_Item_Invalid_Set_Item"));
		return false;
	}

	return true;
}

UseItem* ActionPlayerUseItem::createUseItem( const AttackCastStyle::Enum style, UseItem::InputValue& value, Bool isOnlyCoolCheck /*= false*/)
{
	UseItem* useItem = nullptr;

	if ( value.GetTargetId() == 0
		|| value.GetOwnerId() == 0 )
	{
		return useItem;
	}
	if ( isOnlyCoolCheck )
	{
		useItem = NEW UseItemOnlyCheckCool(GetOwnerPlayer());
	}
	else
	{
		switch ( style )
		{
			case AttackCastStyle::IMMEDIATELY_CAST:
			{
				useItem = NEW UseItemInstant( GetOwnerPlayer() );
			}
			break;
			case AttackCastStyle::AFTER_CAST:
			{
				useItem = NEW UseItemCast( GetOwnerPlayer() );
			}
			break;
			default:
			{
				MU2_ASSERT( !"未知的样式" );
			}
			break;
		}
	}

	if ( useItem )
	{
		useItem->Set( value );
		useItem->SetStartTime(GetTickCount() );
	}

	return useItem;
}

ErrorItem::Error ActionPlayerUseItem::checkAndSet(const ItemConstPtr& item, UseItem::InputValue* value /* = nullptr */ )
{
	VERIFY_RETURN(item, ErrorItem::itemNotFound);

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	if (player->IsTrading())
	{
		SendSystemMsg(player, SystemMessage( L"sys", L"Msg_UserTrade_Invalid_UseItem" ) );
		return ErrorItem::E_TRADING;
	}

	if( true == m_useItemLimit.IsLimit( item->GetUseLimitGroup() ) )
	{
		m_lastSystemMsg.assign( L"Msg_Item_Err_NeedMaxUseCnt" );
		return ErrorItem::E_REFER_SYSTEM_MSG;
	}
	
	if (false == player->CheckRequestLevel(item) )
	{
		m_lastSystemMsg.assign( L"Msg_ItemUse_Err_Level" );
		return ErrorItem::E_REFER_SYSTEM_MSG;
	}

#ifdef	__Hotfix_CheckAndSet_Reclass_Check_by_robinhwp_2019_0412
	if (false == player->CheckRequestClass(item))
	{
		m_lastSystemMsg.assign(L"Msg_Item_Err_CantUse_Class");
		return ErrorItem::E_INVALID_CLASS;
	}
#else	__Hotfix_CheckAndSet_Reclass_Check_by_robinhwp_2019_0412
#endif	__Hotfix_CheckAndSet_Reclass_Check_by_robinhwp_2019_0412


	if ( true == checkUnusableType( item->GetDivision() ) )
	{
		return ErrorItem::E_FAILED_USE_AREA;
	}

	if ( false == checkColosseum(item) )
	{
		return ErrorItem::E_FAILED_USE_AREA;
	}

	if ( false == item->IsValidItem() )
	{
		m_lastSystemMsg.assign( L"Msg_Item_Blocked" );
		SendSystemMsg(player, SystemMessage(L"sys", L"Msg_Item_Err_UseTime"));
		return ErrorItem::E_REFER_SYSTEM_MSG;
	}

#ifdef __lockable_item_181001
	VALID_DO( item->GetInfoElem().consumable == false || item->IsUnLocakble(), SendSystemMsg( player, SystemMessage( L"sys", L"Msg_Item_locked_not_usable" ) );
									return ErrorItem::E_ITEM_LOCKED; );
#endif

#ifdef __Patch_ItemRestrict_hwaYeong_2018_12_04

	IndexZone indexZone = 0;

	if (player->GetSector()->GetWorldElem().groupId)	indexZone = player->GetSector()->GetWorldElem().groupId;
	else								indexZone = player->GetSector()->GetWorldElem().index;

	const SetItemIndex *setRestrict = SCRIPTS.GetItemRestrictInfo( indexZone );

	if (setRestrict && setRestrict->size())
	{
		VALID_RETURN( setRestrict->find( item->GetItemIndex() ) == setRestrict->end(), ErrorItem::E_NOT_USE_ZONE );
	}

#endif
	const SkillInfoElem* skillElem = SCRIPTS.GetSkillInfoScript( item->GetLinkedSkillIndex() );
	VALID_DO(skillElem, AllowNull);

	if ( nullptr == skillElem && isNeedSkillInfoItem(item) )
	{
		return ErrorItem::E_FAILED;
	}
		
	EntityUnit* targetUnit = player;
	
	auto castTime = 0, coolTime = 0;
	if( nullptr != skillElem && nullptr != value)
	{
		if ( skillElem->reMaxRange != 0  
			&& false == checkTargetRange( skillElem->eSkillTargetType, skillElem->reMaxRange, value->GetTargetId(), value->targetPos ) )
		{
			return ErrorItem::E_FAILED;
		}	

		if ( skillElem->eSkillTargetType == SkillTargetType::TARGET )
		{	
			targetUnit = player->GetSectorAction().GetUnitInMySector( value->GetTargetId() );
			VERIFY_RETURN(targetUnit && targetUnit->IsValid(), ErrorItem::unitNotFound);
		}

		castTime = skillElem->skillTime;
		if ( ItemDivisionType::RIDING_CAPSULE == item->GetDivision() )
		{
			const Int32 timeRate = player->GetAbilityAction().GetAbilityValue( EAT::RIDING_CAST_TIME_RATE );
			castTime -= static_cast< UInt32 >( castTime * timeRate * DEFAULT_FLOATIZED_RATE);
		}
		
		coolTime = player->GetSkillControlAction().GetCorrectSkillCoolTime( skillElem->coolLineType, skillElem->coolTime );
	}

	// 정확한 타겟정보를 구한 후 Check 류 함수를 진행한다.
	// 탈것 관련으로 CheckItemDivisionType 이 CheckSkillEffect 보다 선행되어야한다.
	if (nullptr != value)
	{
		ErrorItem::Error eError = CheckItemDivisionType( item, targetUnit, value->GetTargetItemPos(), value->socketPlace );
		VERIFY_RETURN(eError == ErrorItem::SUCCESS, eError);

		if (nullptr != skillElem)
		{
			ErrorItem::Error resultCheckSkillEffect = CheckSkillEffect( skillElem, value->GetTargetItemPos() );
			if (resultCheckSkillEffect != ErrorItem::SUCCESS)
			{
				return resultCheckSkillEffect;
			}
		}
	}
	
	AchievementUseItemDivisionSuccessEvent achievement(player, item->GetDivision() );
	player->GetAchievementAction().OnEvent( &achievement );
	
	if (value != nullptr)
	{
		// set info
		value->SetTarget( targetUnit );
		value->itemId = item->GetItemId();
		value->itemType = item->GetItemIndex();
		value->elem = skillElem;
		value->castingTime = castTime;
		value->coolTime = coolTime;
	}

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerUseItem::CheckSkillEffect( const SkillInfoElem* skillScript, const ItemPos& targetPos )
{
	VALID_RETURN( skillScript, ErrorItem::E_INVALID_ID );

	if ( skillScript->effectIndex[0] == 0 )
	{
		return ErrorItem::SUCCESS;
	}

	if( skillScript->IsCastingSkill() )
	{
		auto actBuff = GetOwnerPlayer()->GetAction<ActionBuff>();
		auto found = actBuff->FindBuffInfo([](const BuffInfoPtr buff)
		{
			return buff->IsEqualKeyOperator(EffectOperatorType::RIDING_BUFF, RidingSubType::NORMAL_RIDING);
		});

		IndexSkill skillId = 0;
		if ( nullptr != found )
		{
			skillId = found->infoElem->index;
		}

		if ( 0 != skillId )
		{
			theLogicEffectSystem.OnCancelBuffCast( GetOwnerPlayer(), skillId );
		}
	}

	const SkillEffectElem* effectScript = skillScript->skillEffectElems[0];
	VALID_RETURN( effectScript, ErrorItem::E_FAILED );

	if ( effectScript->keyOperator == EffectOperatorType::EXCHANGE_ITEM )
	{
		ActionPlayerInventory* actInven = GetEntityAction( GetOwnerPlayer() );
		VERIFY_RETURN( actInven, ErrorItem::E_FAILED );
		VALID_RETURN( actInven->IsEnoughFreeSlot( InvenBags, 1 ), ErrorItem::E_FAILED );

		for ( Int32 loop = 0; loop < 3; ++loop )
		{
			IndexItem itemIndex = effectScript->value[loop];
			if ( itemIndex != 0 && actInven->IsEnoughItem(InvenBags, itemIndex, 1) == false )
			{
				return ErrorItem::E_FAILED;
			}
		}
	}
	else if ( effectScript->keyOperator == EffectOperatorType::RECOVERY_DURABILITY )
	{
		ActionPlayerDurability* adurability = GetEntityAction( GetOwnerPlayer() );
		VALID_RETURN( adurability, ErrorItem::E_FAILED );
		VALID_RETURN( adurability->CheckRepair( static_cast<Byte>(effectScript->value[0]), targetPos ), ErrorItem::E_FAILED)
	}
	else if ( effectScript->keyOperator == EffectOperatorType::TRIGGER )
	{
		if ( effectScript->subKey == static_cast<Int32>(TriggerType::OBJECT) )
		{
			ActionPlayer* actPlayer = GetOwnerPlayer()->GetAction<ActionPlayer>();
			EntityNpc* rechargeObj = actPlayer->GetRechargeObject();
			if ( rechargeObj == nullptr )
			{
				SendSystemMsg( GetOwnerPlayer(), SystemMessage( L"sys", L"Msg_RechargeStone_Item_Err_Area" ) );
				return ErrorItem::E_FAILED;
			}
						
			const IndexNpc objIndex = static_cast<IndexNpc>(effectScript->value[2]);
			if ( objIndex != 0 && objIndex != rechargeObj->GetNpcIndex() )
			{
				SendSystemMsg( GetOwnerPlayer(), SystemMessage( L"sys", L"Msg_Item_Err_Area" ) );
				return ErrorItem::E_FAILED;
			}
		}
	}
	else if ( effectScript->keyOperator == EffectOperatorType::REGISTER_PET )
	{
		ActionPlayerPetManage* actPet = GetOwnerPlayer()->GetAction<ActionPlayerPetManage>();
		VALID_RETURN( actPet->CanRegister( 1 ), ErrorItem::E_FAILED );
	}

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerUseItem::CheckItemDivisionType(const ItemConstPtr& item, EntityUnit* target, const ItemPos& targetPos, const SocketPlace& slotPlace )
{
	VERIFY_RETURN(item, ErrorItem::itemNotFound);
	VALID_DO(target, AllowNull);

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	if ( ItemDivisionType::MAGIC_SOCKET_DELETE == item->GetDivision())
	{	
		VALID_RETURN( 0 < slotPlace.size(), ErrorItem::E_EXIST_ITEM );

		// source = 마석 제거 템, targetPos = 마석 박힌 템
		VALID_RETURN( player->GetSocketAction().CheckRemoveSocket( slotPlace[0], item->GetPos(), targetPos ), ErrorItem::E_EXIST_ITEM );
	}
	else if ( ItemDivisionType::FORMULA == item->GetDivision())
	{
		// 등록가능한 도안이 하나라도 있다면 진행한다.

		const std::vector<Int32>& vecFormulaIndices = item->GetFormulaIndices();
		auto it = std::find_if_not(vecFormulaIndices.begin(), vecFormulaIndices.end(), [player]( Index32 index )
		{
			return 0 < player->GetMakingAction().FindFormula( index );
		});

		if(vecFormulaIndices.end() == it )
		{
			SendSystemMsg(player, SystemMessage( L"sys", L"Msg_ItemCreate_GainFormula_Err_AlreadyGain" ) );
			return ErrorItem::E_EXIST_ITEM;
		}

		for( auto& i : vecFormulaIndices)
		{
			const MakingFormulaElem* formulaElem = SCRIPTS.GetMakingFormulaScript( i );
			VERIFY_RETURN(formulaElem, ErrorItem::elemNotFound);

			// 레피시 획득을 통한 레시피만 얻을 수 있다.
			VERIFY_RETURN(formulaElem->activeList_Condition == MakingActiveList::Add_ReadRecipe, ErrorItem::IsNotAdd_ReadRecipe);

			// 해당 레시피의 값과 동일 해야 한다.
			VERIFY_RETURN( static_cast<IndexItem>(formulaElem->activeList_Value) == item->GetItemIndex(), ErrorItem::MustSameItemIndex );
		}
		
	}
	else if ( ItemDivisionType::RIDING_CAPSULE == item->GetDivision())
	{
		VERIFY_RETURN(target && target->IsValid(), ErrorItem::unitNotFound);		

		BuffInfoPtr found = target->GetBuffAction().FindBuffInfo([](const BuffInfoPtr buff)
		{
			return buff->IsEqualKeyOperator(EffectOperatorType::RIDING_BUFF, RidingSubType::NORMAL_RIDING);
		});

		IndexSkill skillId = 0;
		if ( nullptr != found )
		{
			skillId = found->infoElem->index;
		}

		if ( 0 != skillId )
		{
			theLogicEffectSystem.OnCancelBuffCast(player, skillId );
			if ( skillId == item->GetLinkedSkillIndex() )
			{
				return ErrorItem::E_FAILED;
			}
		}
				
		if ( nullptr != target->GetJoinedWorldElem())
		{
			if ( false == target->GetJoinedWorldElem()->specialItem_Enable)
			{
				m_lastSystemMsg.assign( L"Msg_Riding_Invalid_Use" );
				return ErrorItem::E_REFER_SYSTEM_MSG;
			}
		}

		if ( true == player->GetSkillControlAction().IsBattleMode() )
		{
			SendSystemMsg(player, SystemMessage( L"sys", L"Msg_Riding_Invalid_Type" ) );
			return ErrorItem::IsBattleMode;
		}
	}
	else if ( ItemDivisionType::SKILL_RUNE == item->GetDivision() )
	{
		VERIFY_RETURN( player->GetSkillRuneAction().GetActiveSkill(item->GetLinkedRuneSkillId() ), ErrorItem::E_FAILED );
		VERIFY_RETURN( SCRIPTS.GetSkillRuneScript(item->GetLinkedRuneSkillId(), item->GetLinkedRuneSkillCategory()), ErrorItem::E_FAILED );
	}
	else if ( ItemDivisionType::MAP_ITEM == item->GetDivision() )
	{
		Sector* pSector = player->GetSector();
		VERIFY_RETURN(pSector, ErrorItem::sectorNotFound);

		if (pSector->IsInstance() )
		{
			SendSystemMsg(player, SystemMessage( L"sys", L"Msg_Cannot_Enter_porystal" ) );
			return ErrorItem::E_FAILED;
		}

		const AdvPorystalInfoElem* poryElem = SCRIPTS.GetPorystalInfoElem( item->GetItemIndex());
		VERIFY_RETURN(poryElem, ErrorItem::elemNotFound );

		const AdvPorystalPaymentElem* poryPayListElem = SCRIPTS.GetPorystalPaymentInfoList(item->GetItemIndex(), player->GetLevel() );
		VERIFY_RETURN(poryPayListElem, ErrorItem::elemNotFound);

		ENtfGamePorystalInfo* ntf = NEW ENtfGamePorystalInfo;
		ntf->dungeonInfo.advType = poryElem->advType;
		ntf->dungeonInfo.difficulty = poryElem->dungeon_Difficulty;
		ntf->dungeonInfo.level = player->GetLevel();
		ntf->payMents.list = poryPayListElem->list;
		ntf->partyMemberCount = poryElem->recommendPartyMemberCount;
		ntf->portalIndex = poryElem->portalIndex;
		ntf->recommendPower = 0;
		ntf->itemIndex = item->GetItemIndex();

		SERVER.SendToClient(player, EventPtr( ntf ) );
	}
	else if( ItemDivisionType::RECOVERY_ENTRANCE_COUNT == item->GetDivision() )
	{
		const ItemRecoveryEntranceCountElem* elem = SCRIPTS.GetItemRecoveryEntranceCountElem(item->GetItemIndex());
		VERIFY_RETURN(elem, ErrorItem::elemNotFound );
	}
	else if( ItemDivisionType::IsGrowArtifactItem(item->GetDivision()) )
	{
		Sector* pSector = player->GetSector();
		VERIFY_RETURN( pSector, ErrorItem::sectorNotFound );

		if (pSector->IsInstance())
		{
			SendSystemMsg( player, SystemMessage( L"sys", L"Msg_Cannot_Enter_Item" ) );
			return ErrorItem::E_FAILED;
		}

		// 유물 성장 아이템을 사용하려면, 성장 가능한 유물이 장착되어 있어야 한다.
		ArtifactPtr& growingArtifact = player->GetArtifactAction().GetGrowingArtifact();
		VALID_RETURN( growingArtifact, ErrorItem::E_NOT_EXIST_GROWABLE_ARTIFACT);

		const ArtifactInfoElem* artifactElem = growingArtifact->InfoElem();
		VALID_RETURN(artifactElem->maxLevel != growingArtifact->Data().level, ErrorItem::E_NOT_EXIST_GROWABLE_ARTIFACT);
		
		if ((item->GetDivision() == IDT::GROW_ARTIFACT_ITEM_GENERAL && artifactElem->artifactType != ArtifactType::GENERAL) ||
			(item->GetDivision() == IDT::GROW_ARTIFACT_ITEM_AWAKEN && artifactElem->artifactType != ArtifactType::AWAKEN) )
		{
			return ErrorItem::E_NOT_EXIST_GROWABLE_ARTIFACT;
		}
	}
	
	return ErrorItem::SUCCESS;
}

void ActionPlayerUseItem::processAfterUseItem(const ItemConstPtr& item )
{
	VERIFY_RETURN( item, );

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	Sector* sector = player->GetSector();
	VERIFY_RETURN( sector, );	

	sector->GetSectorController().GetControllerAction().OnEvent( DungeonEventType::USE_ITEM, GetOwnerPlayer(), nullptr, item->GetItemIndex());
	
	updateUseItemLimit( item );

	theNotifierGameContents.OnItem_Used(*player, item->GetItemIndex());
}

Bool ActionPlayerUseItem::isNeedSkillInfoItem(const ItemConstPtr& item ) const
{
	VERIFY_RETURN(item, false);

	switch (item->GetDivision())
	{
	case ItemDivisionType::NONE:
	case ItemDivisionType::POTION:
	case ItemDivisionType::HEAL_POTION:
	case ItemDivisionType::MANA_POTION:
	case ItemDivisionType::TELEPORT_SCROLL:
	case ItemDivisionType::RECOVERY_DURATION:
	case ItemDivisionType::MAGIC_SOCKET_DELETE:
	case ItemDivisionType::MEGAPHONE:
	case ItemDivisionType::RIDING_CAPSULE:
	case ItemDivisionType::PET:
		return true;
	}

	return false;
}

void ActionPlayerUseItem::selectConstGambleItemInfo( const GambleBagElem* gambleBagInfo, UInt32 pickCount, ClassType::Enum classIdx, GambleBagInfoList& out ) const
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	VALID_RETURN( gambleBagInfo, );
	VALID_RETURN( checkClass( classIdx ), );

	GambleConstItemSelector selector( gambleBagInfo->staticPackage );
	
	for( UInt32 i = 0; i < pickCount; ++ i )
	{
		GambleBagInfo info;

		if( selector.SelectOne( info ) )
		{
			out.push_back( info );
		}
		else
		{
			MU2_WARN_LOG( LogCategory::DEBUG_GAMBLE, "selectConstGambleItemInfo > SelectOne is failed. user(%d:%s), list size(%u), pickCount(%u), curPickCount(%u)",
				owner->GetCharId(), owner->GetCharName().c_str(), gambleBagInfo->staticPackage.size(), pickCount, i );
		}
	}
}

void ActionPlayerUseItem::selectMutableGambleItemInfo( const GambleBagElem* gambleBagInfo, UInt32 pickCount, ClassType::Enum classIdx, GambleBagInfoList& out ) const
{
	VALID_RETURN( gambleBagInfo, );
	VALID_RETURN( checkClass( classIdx ), );
	
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	GambleMutableItemSelector selector( gambleBagInfo->staticPackage );

	for( UInt32 i = 0; i < pickCount; ++ i )
	{
		GambleBagInfo info;
		if( selector.SelectOneAndRemove( info ) )
		{
			out.push_back( info );
		}
		else
		{
			MU2_WARN_LOG( LogCategory::DEBUG_GAMBLE, L"selectMutableGambleItemInfo > SelectOneAndRemove is failed. user(%d:%s), list size(%u), pickCount(%u), curPickCount(%u)",
						player->GetCharId(), player->GetCharName().c_str(), gambleBagInfo->staticPackage.size(), pickCount, i );
		}
	}
}

Bool ActionPlayerUseItem::checkUseSkillType( IDT::Enum eDivision )
{
	switch ( eDivision )
	{
	case ItemDivisionType::MAP_ITEM:
		return false;
	}

	return true;
}

void ActionPlayerUseItem::updateUseItemLimit(const ItemConstPtr& item )
{
	VERIFY_RETURN(item, );

	const ItemConfigScript* script =  SCRIPTS.GetScript<ItemConfigScript>();
	VERIFY_RETURN(script, );

	const ItemUseLimitGroupInfo* group = script->GetItemUseLimitGroupInfo( item->GetUseLimitGroup());
	VALID_RETURN(group, );

	const ItemUseLimitResetInfo* reset = script->GetItemUseLimitResetInfo( group->type );
	VERIFY_RETURN(group, );

	m_useItemLimit.UseItem( group, reset );
}

void ActionPlayerUseItem::CorrectCoolTime()
{
	std::for_each( m_useItems.begin(), m_useItems.end(), []( UseItemPtr useItem)
														{useItem->CorrectCoolTime();}
	);
}

void ActionPlayerUseItem::CheatRemoveLimit( UInt8 group )
{
	m_useItemLimit.CheatRemove( group );
}

ErrorItem::Error ActionPlayerUseItem::UseMembershipItem(const ItemPos& pos)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	const ItemConstPtr membershipItem = player->GetInventoryAction().GetItem(pos);
	VERIFY_RETURN(membershipItem, ErrorItem::itemNotFound);

	ErrorItem::Error eError = checkAndSet( membershipItem );
	VERIFY_RETURN(ErrorItem::SUCCESS == eError, eError);
	VERIFY_RETURN(membershipItem->GetDivision() == ItemDivisionType::MEMBERSHIP_SERVICE || 
				  membershipItem->GetDivision() == ItemDivisionType::MEMBERSHIP_EXP_GAIN, ErrorItem::E_INVALID_DIVISION);
		
	VERIFY_RETURN(player->GetMembershipService().info.level > 0 || membershipItem->GetDivision() != ItemDivisionType::MEMBERSHIP_EXP_GAIN, ErrorItem::MustNotMembershipExpGain);

	ItemData itemData;		
	membershipItem->FillUpItemData(itemData);
	VERIFY_RETURN(itemData.GetCurStackCount() > 0, ErrorItem::E_NOT_EXIST);
	itemData.DecCurStackCount(1);

	ItemBag bag(*player);

	if (itemData.GetCurStackCount() > 0)
	{
		VERIFY_RETURN(bag.Update(membershipItem, itemData), ErrorItem::BagUpdateFailed);
	}
	else
	{
		VERIFY_RETURN(bag.Remove(itemData), ErrorItem::BagRemoveFailed);
	}

	TransactionPtr transPtr(NEW UseMembershipSerivceItemTransaction(__FUNCTION__, __LINE__, player, LogCode::E_USE_MEMBERSHIP_ITEM, membershipItem->GetItemIndex()));
	{
		VERIFY_RETURN(bag.Write(transPtr), ErrorItem::BagWriteFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::TranRegisterFailed);
	}

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerUseItem::UseAdditionalOptionItem( const ItemPos& sourcePos, const ItemPos& targetPos )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::InvalidPlayer);

	if (player->IsTrading())
	{
		SystemMessage sysMsg( L"sys", L"Msg_UserTrade_Invalid_Not_Work" );
		SendSystemMsg( player, sysMsg );
		return ErrorItem::E_TRADING;
	}

	const ItemPtr targetItem = player->GetInventoryAction().GetItem( targetPos);
	VERIFY_RETURN( targetItem, ErrorItem::itemNotFound );
	VERIFY_RETURN( targetItem->IsIdentified(), ErrorItem::E_UNIDENTIFIED_ITEM );
#ifdef __lockable_item_181001
	VALID_DO( targetItem->IsUnLocakble(), SendSystemMsg( player, SystemMessage( L"sys", L"Msg_Item_locked_not_usable" ) ); return ErrorItem::E_ITEM_LOCKED );
#endif
	const ItemConstPtr sourceItem = player->GetInventoryAction().GetItem( sourcePos);
	VERIFY_RETURN( sourceItem, ErrorItem::itemNotFound );
	VERIFY_RETURN( sourceItem->GetDivision() == ItemDivisionType::MAGIC_SCROLL ||
				   sourceItem->GetDivision() == ItemDivisionType::FIXED_MAGIC_SCROLL ||
				   sourceItem->GetDivision() == ItemDivisionType::SELECT_MAGIC_SCROLL, ErrorItem::E_INVALID_DIVISION );
#ifdef __lockable_item_181001
	VALID_DO( sourceItem->IsUnLocakble(), SendSystemMsg( player, SystemMessage( L"sys", L"Msg_Item_locked_not_usable" ) );
											return ErrorItem::E_ITEM_LOCKED; );
#endif
	AdditionalResult::Enum eAdditionalResult = AdditionalResult::FAILED;
	AdditionalAbilityPackVector aAddOption;
	AdditionalOptionGrade::Enum eNewGrade = targetItem->GetAdditionalOptionGrade();
	
	ErrorItem::Error eErrorEnchant = ErrorItem::SUCCESS;
	ErrorItem::Error eErrorItem = ErrorItem::SUCCESS;

	switch (sourceItem->GetDivision())
	{
	case ItemDivisionType::MAGIC_SCROLL:
		{
			eErrorEnchant = ItemFactory::AdditionalOption(sourceItem, targetItem, eAdditionalResult, aAddOption);

			if (eAdditionalResult == AdditionalResult::GREAT_SUCCESS)
			{
				eNewGrade = static_cast<AdditionalOptionGrade::Enum>(eNewGrade + 1);
			}
		}
		break;
	case ItemDivisionType::FIXED_MAGIC_SCROLL:
		{
			AdditionalOptionGrade::Enum eOldGrade = targetItem->GetAdditionalOptionGrade();

			eErrorEnchant = ItemFactory::FixedAdditionalOption( sourceItem, targetItem, eNewGrade, aAddOption );

			if (eNewGrade == eOldGrade)
				eAdditionalResult = AdditionalResult::FAILED;
			else
				eAdditionalResult = AdditionalResult::GREAT_SUCCESS;
		}
		break;
	case ItemDivisionType::SELECT_MAGIC_SCROLL:
		{
			// 각인옵션 결과를 확인해서 유저가 옵션을 유지할지 변경할지 선택해야 하기 때문에
			// 빛나는 붉은 각인서 개수 감소 및 삭제 트랜잭션 처리만 하고 각인옵션 결과를 전송한다.

			ItemData resultItemData;

			eErrorItem = ItemFactory::GetSelectAdditionalOptionResult(sourceItem, targetItem, resultItemData);
			VERIFY_RETURN(ErrorItem::SUCCESS == eErrorItem, eErrorItem);
			
			// 빛나는 붉은 각인서 개수 감소 및 삭제 트랜잭션 처리
			TransactionPtr transPtr = std::make_shared<ItemUseSelectAdditionalOptionTransaction>(__FUNCTION__, __LINE__, player, LogCode::E_ITEM_USE_SELECT_ADDITIONAL_OPTION, sourceItem->GetItemId(), resultItemData);
			{
				ItemBag bag(*player);

				if (sourceItem->IsConsumable())
				{
					eErrorItem = player->GetInventoryAction().RemoveByPlace(sourceItem->GetPlace(), bag, 1);
					VERIFY_RETURN(eErrorItem == ErrorItem::SUCCESS, eErrorItem);
				}

				VERIFY_RETURN(bag.Write(transPtr), ErrorItem::BagWriteFailed);
				VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::TranRegisterFailed);
			}

			processAfterUseItem(sourceItem);

			return ErrorItem::SUCCESS;
		}
		break;
	default:
		return ErrorItem::E_INVALID_DIVISION;
	}

	if (eErrorEnchant != ErrorItem::SUCCESS)
	{
		SendSystemMsg( player, SystemMessage( L"sys", L"Msg_Additionaloption_Scroll_Err_MaxGrade" ) );
		return ErrorItem::AdditionalOptionFailed;
	}

	ItemAdditionalOptionTransaction* trans = NEW ItemAdditionalOptionTransaction(__FUNCTION__, __LINE__, player, LogCode::E_ITEM_USE_ADDITIONAL_OPTION, targetItem->GetItemId(), eAdditionalResult );
	TransactionPtr transPtr( trans );
	{
		ItemBag bag(*player);

		if (sourceItem->IsConsumable() )
		{
			eErrorItem = player->GetInventoryAction().RemoveByPlace(sourceItem->GetPlace(), bag, 1);
			VERIFY_RETURN(eErrorItem == ErrorItem::SUCCESS, eErrorItem);
		}

		ItemData targetItemCloned = targetItem->Clone();
		{
			trans->SetBeforeInfo(targetItemCloned);
			trans->SetLogUseType(sourceItem->GetDivision());
			trans->SetUseItem(sourceItem->GetItemIndex(), sourceItem->GetItemId(), sourceItem->GetCurStackCount() - 1);			

			ItemFactory::CheckAndBind(player, targetItem, EquipBindState::ADDITIONAL_OPTION, targetItemCloned.bindProperty);

			if (eAdditionalResult != AdditionalResult::FAILED)
			{
				targetItemCloned.additionalOptionGrade = eNewGrade;
				targetItemCloned.additionalOptionMap.clear();

				for (auto& iter : aAddOption)
					targetItemCloned.AppendOptionAbility<AdditionalAbilityPack>(iter, targetItemCloned.additionalOptionMap);
			}

			trans->SetAfterInfo(targetItemCloned);
		}		

		VERIFY_RETURN(bag.Update(targetItem, targetItemCloned), ErrorItem::BagUpdateFailed);
		VERIFY_RETURN(bag.Write(transPtr), ErrorItem::BagWriteFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::TranRegisterFailed);
	}

	processAfterUseItem( sourceItem );

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerUseItem::SelectAdditonalOption(const ItemPos& targetPos, const Bool isChange )
{
	// 빛나는 붉은 각인서를 사용해서 각인옵션을 추출한 결과를 통해 변경할지 기존 옵션을 유지할지에 대한 처리

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::InvalidPlayer);
	
	if (true == player->IsTrading())
	{
		SystemMessage sysMsg( L"sys", L"Msg_UserTrade_Invalid_Not_Work" );
		SendSystemMsg( player, sysMsg );
		return ErrorItem::E_TRADING;
	}

	const ItemPtr targetItem = player->GetInventoryAction().GetItem(targetPos);
	VERIFY_RETURN(targetItem, ErrorItem::E_NOT_EXIST_ITEM);
	VERIFY_RETURN(targetItem->IsIdentified(), ErrorItem::E_UNIDENTIFIED_ITEM);
#ifdef __lockable_item_181001
	VALID_DO( targetItem->IsUnLocakble(), SendSystemMsg( player, SystemMessage( L"sys", L"Msg_Item_locked_not_usable" ) );
											return ErrorItem::E_ITEM_LOCKED; );
#endif
	SelectAdditionalOptionPtr selectAdditionalOptionPtr = targetItem->GetSelectAdditionalOptionPtr();
	VERIFY_RETURN(selectAdditionalOptionPtr, ErrorItem::E_INVALID_ID);

	if (false == isChange)
	{
		// 기존 옵션을 유지 하기 때문에 트랜잭션 처리가 필요 없다.
		EResSelectAdditionalOption* res = NEW EResSelectAdditionalOption;
		res->result = ErrorItem::SUCCESS;
		targetItem->FillUpItemData(res->targetItemData);
		player->SendToClient(EventPtr(res));

		targetItem->SetSelectAdditionalOptionPtr(nullptr);

		return ErrorItem::SUCCESS;
	}

	// 트랜잭션 처리
	ItemAdditionalOptionTransaction* transaction = NEW ItemAdditionalOptionTransaction(__FUNCTION__, __LINE__, player, LogCode::E_ITEM_USE_ADDITIONAL_OPTION, targetItem->GetItemId(), AdditionalResult::SELECT_ADDITIONAL_SUCCESS);
	TransactionPtr transPtr(transaction);
	{
		ItemBag bag(*player);
		ItemData targetItemCloned = targetItem->Clone();

		transaction->SetLogUseType(ItemDivisionType::SELECT_MAGIC_SCROLL);
		transaction->SetUseItem(0, 0, 0);
		transaction->SetBeforeInfo(targetItemCloned);

		targetItemCloned.additionalOptionGrade = selectAdditionalOptionPtr->additionalOptionGrade;
		targetItemCloned.additionalOptionMap.clear();
		targetItemCloned.additionalOptionMap = selectAdditionalOptionPtr->additionalOptionMap;

		transaction->SetAfterInfo(targetItemCloned);

		targetItem->SetSelectAdditionalOptionPtr(nullptr);

		VERIFY_RETURN(bag.Update(targetItem, targetItemCloned), ErrorItem::BagUpdateFailed);
		VERIFY_RETURN(bag.Write(transPtr), ErrorItem::BagWriteFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::TranRegisterFailed);
	}

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerUseItem::UseTrascendStoneRemoveFailCount( const ItemPos& sourcePos, const ItemPos& targetPos )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);
		
	if (player->IsTrading())
	{
		SystemMessage sysMsg( L"sys", L"Msg_UserTrade_Invalid_Not_Work" );
		SendSystemMsg( player, sysMsg );
		return ErrorItem::E_TRADING;
	}

	const ItemConstPtr targetItem = player->GetInventoryAction().GetItem( targetPos);
	VERIFY_RETURN( targetItem, ErrorItem::E_NOT_EXIST_ITEM );
	VERIFY_RETURN( targetItem->IsIdentified(), ErrorItem::E_UNIDENTIFIED_ITEM );
	VERIFY_RETURN( targetItem->GetEnchantFailCount(), ErrorItem::E_ERROR );
	VERIFY_RETURN( targetItem->GetEnchantRawLevel(), ErrorItem::E_ERROR );
	VERIFY_RETURN( ItemDivisionType(targetItem->GetDivision()).IsTstone(), ErrorItem::E_INVALID_DIVISION );
#ifdef __lockable_item_181001
	VALID_DO( targetItem->IsUnLocakble(), SendSystemMsg( player, SystemMessage( L"sys", L"Msg_Item_locked_not_usable" ) );
											return ErrorItem::E_ITEM_LOCKED; );
#endif
	const ItemConstPtr sourceItem = player->GetInventoryAction().GetItem( sourcePos);
	VERIFY_RETURN( sourceItem, ErrorItem::E_NOT_EXIST_ITEM );
	VERIFY_RETURN( sourceItem->GetDivision() == IDT::WHILE_SCROLL, ErrorItem::E_INVALID_DIVISION );

	ErrorItem::Error eError = checkAndSet( sourceItem, NULL );
	VERIFY_RETURN(ErrorItem::SUCCESS == eError, eError);

	const TrascendStoneEnchantRestoreRateElem *elem = SCRIPTS.GetTStonenchantRestoreRateElem( sourceItem->GetItemIndex());
	VERIFY_RETURN( elem, ErrorItem::scriptNotFound );

	auto iter = elem->gradeRate.find( targetItem->GetGrade() );
	VERIFY_RETURN( iter != elem->gradeRate.end(), ErrorItem::E_LIMIT_LEVEL );
	

	ItemData targetItemCloned = targetItem->Clone();

	UInt32 random = GetRandBetween( 0, Limits::MAX_DEFAULT_RAND );
	Bool upgrade = false;
	if (iter->second > random)
	{
		targetItemCloned.enchantFailCount -= 1;
		targetItemCloned.enchant -= 1;
		upgrade = true;
	}
	
	ItemTrascendStoneRemoveFailCount* transaction = NEW ItemTrascendStoneRemoveFailCount(__FUNCTION__, __LINE__, player, LogCode::E_ITEM_USE_REMOVE_TRASCEND_STONE_REMOVE_FAIL_COUNT, upgrade );
	TransactionPtr transPtr( transaction );
	{
		ItemBag bag(*player);

		if (sourceItem->IsConsumable() )			
		{
			eError = player->GetInventoryAction().RemoveByPlace(sourceItem->GetPlace(), bag, 1);
			VERIFY_RETURN(eError == ErrorItem::SUCCESS, eError);
		}

		transaction->SetTargetItem(targetItemCloned);

		VERIFY_RETURN(bag.Update(targetItem, targetItemCloned), ErrorItem::BagUpdateFailed);
		VERIFY_RETURN(bag.Write(transPtr), ErrorItem::BagWriteFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::TranRegisterFailed);
	}

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerUseItem::UseResetTrascendStone( const ItemPos& sourcePos, const ItemPos& targetPos )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);
		
	if (player->IsTrading())
	{
		SystemMessage sysMsg( L"sys", L"Msg_UserTrade_Invalid_Not_Work" );
		SendSystemMsg( player, sysMsg );
		return ErrorItem::E_TRADING;
	}


	const ItemConstPtr targetItem = player->GetInventoryAction().GetItem( targetPos);
	VERIFY_RETURN( targetItem, ErrorItem::E_NOT_EXIST_ITEM );
	VERIFY_RETURN( targetItem->IsIdentified(), ErrorItem::E_UNIDENTIFIED_ITEM );
	VERIFY_RETURN( targetItem->GetEnchantRawLevel(), ErrorItem::InvalidEnchantRawLevel );
	VERIFY_RETURN( ItemDivisionType(targetItem->GetDivision()).IsTstone(), ErrorItem::E_INVALID_DIVISION );
// #ifdef __LogMod_181017_by_ray_181018
	Byte resetLimit = SCRIPTS.GetTStoneResetLimit(targetItem->GetItemIndex());
// #endif
	VERIFY_RETURN( targetItem->transcendStoneResetCount < SCRIPTS.GetTStoneResetLimit(targetItem->GetItemIndex()), ErrorItem::TStoneResetLimit);
#ifdef __lockable_item_181001
	VALID_DO( targetItem->IsUnLocakble(), SendSystemMsg( player, SystemMessage( L"sys", L"Msg_Item_locked_not_usable" ) );
											return ErrorItem::E_ITEM_LOCKED; );
#endif
	const ItemConstPtr sourceItem = player->GetInventoryAction().GetItem( sourcePos);
	VERIFY_RETURN( sourceItem, ErrorItem::E_NOT_EXIST_ITEM );
	VERIFY_RETURN( sourceItem->GetDivision() == IDT::RESET_TRASCEND_SCROLL, ErrorItem::E_INVALID_DIVISION );

	ErrorItem::Error eError = checkAndSet( sourceItem );
	VERIFY_RETURN(ErrorItem::SUCCESS == eError, eError);

	const TStoneResetItemIndexs* targetElem = SCRIPTS.GetTStoneResetItem( sourceItem->GetItemIndex());
	VERIFY_RETURN( targetElem, ErrorItem::E_ERROR );
	VERIFY_RETURN( targetElem->find(targetItem->GetItemIndex()) != targetElem->end(), ErrorItem::E_ERROR );
	
	ItemResetTrascendStone* transaction = NEW ItemResetTrascendStone(__FUNCTION__, __LINE__, player, LogCode::E_ITEM_USE_RESET_TRASCEND_STONE );
	TransactionPtr transPtr( transaction );
	{
		ItemBag bag(*player);

		ItemData targetItemCloned = targetItem->Clone();
		targetItemCloned.enchant = 0;
		targetItemCloned.enchantFailCount = 0;
		targetItemCloned.transcendStoneResetCount += 1;
		targetItemCloned.abilityPackMap.clear();

		if (sourceItem->IsConsumable() )			
		{
			eError = player->GetInventoryAction().RemoveByPlace(sourceItem->GetPlace(), bag, 1);
			VERIFY_RETURN(eError == ErrorItem::SUCCESS, eError);
		}

		transaction->SetTargetItem(targetItemCloned);
#ifdef __LogMod_181017_by_ray_181018
		transaction->SetLeftResetCountForLog(resetLimit - targetItemCloned.transcendStoneResetCount);
#endif

		VERIFY_RETURN(bag.Update(targetItem, targetItemCloned), ErrorItem::BagUpdateFailed);
		VERIFY_RETURN(bag.Write(transPtr), ErrorItem::BagWriteFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::TranRegisterFailed);
	}

	return ErrorItem::SUCCESS;
}


ErrorItem::Error ActionPlayerUseItem::UseTransferAdditionalOptionItem(const ItemPos& materialPos, const ItemPos& sourcePos, const ItemPos& targetPos)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);
		
	if (true == player->IsTrading())
	{
		SystemMessage sysMsg(L"sys", L"Msg_UserTrade_Invalid_Not_Work");
		SendSystemMsg(player, sysMsg);

		return ErrorItem::E_TRADING;
	}	

	// 각인 이전 아이템
	const ItemConstPtr materialItem = player->GetInventoryAction().GetItem(materialPos);
	VERIFY_RETURN(materialItem, ErrorItem::E_NOT_EXIST_ITEM);
#ifdef __lockable_item_181001
	VALID_DO( materialItem->IsUnLocakble(), SendSystemMsg( player, SystemMessage( L"sys", L"Msg_Item_locked_not_usable" ) );
												return ErrorItem::E_ITEM_LOCKED; );
#endif
	// 각인 추출 아이템
	const ItemConstPtr sourceItem = player->GetInventoryAction().GetItem(sourcePos);
	VERIFY_RETURN(sourceItem, ErrorItem::E_NOT_EXIST_ITEM);
#ifdef __lockable_item_181001
	VALID_DO( sourceItem->IsUnLocakble(), SendSystemMsg( player, SystemMessage( L"sys", L"Msg_Item_locked_not_usable" ) );
											return ErrorItem::E_ITEM_LOCKED; );
#endif
	// 각인 이전 대상 아이템
	const ItemConstPtr targetItem = player->GetInventoryAction().GetItem(targetPos);
	VERIFY_RETURN(targetItem, ErrorItem::E_NOT_EXIST_ITEM);
#ifdef __lockable_item_181001
	VALID_DO( targetItem->IsUnLocakble(), SendSystemMsg( player, SystemMessage( L"sys", L"Msg_Item_locked_not_usable" ) );
											return ErrorItem::E_ITEM_LOCKED; );
#endif
	AdditionalOptionGrade::Enum newGrade = AdditionalOptionGrade::NORMAL;
	AdditionalOptionGrade::Enum oldGrade = AdditionalOptionGrade::NORMAL;
	AdditionalAbilityPackVector additionalAbilityPack;
	
	ErrorItem::Error eError = ItemFactory::TransferAdditionalOption(materialItem, sourceItem, targetItem, newGrade, oldGrade, additionalAbilityPack);
	VERIFY_RETURN(ErrorItem::SUCCESS == eError, eError);
	
	// 트랜잭션 처리
	TransactionPtr transPtr = std::make_shared<ItemTransferAdditionalOptionTransaction>( __FUNCTION__, __LINE__
																					   , player, LogCode::E_ITEM_TRANSFER_ADDITIONAL_OPTION
#ifdef __Renewal_Additional_Grade_Transfer_by_kangms_181001
																					   , ProtocolItem::EREQ_TRANSFER_ADDITIONAL_OPTION
#endif//__Renewal_Additional_Grade_Transfer_by_kangms_181001
																					   , targetItem->GetItemId()
																					   , sourceItem->GetItemIndex(), oldGrade
#ifdef __LogMod_181017_by_ray_181018
																						, materialItem->GetItemIndex()
#endif
																					   , additionalAbilityPack );
	{
		ItemBag bag(*player);

		// 각인 이전 아이템 삭제
		eError = player->GetInventoryAction().RemoveByPlace(materialItem->GetPlace(), bag, 1);
		if (ErrorItem::SUCCESS != eError)
		{
			MU2_WARN_LOG(LogCategory::DEBUG_INVENTORY, "ActionUseItem > src item remove failed. user(%d:%s), itemindex(%u), error(%d)", player->GetCharId(), player->GetCharName().c_str(), materialItem->GetItemIndex(), eError);
			return eError;
		}

		// 각인 추출 아이템 삭제
		eError = player->GetInventoryAction().RemoveByPlace(sourceItem->GetPlace(), bag, 1);
		if (ErrorItem::SUCCESS != eError)
		{
			MU2_WARN_LOG(LogCategory::DEBUG_INVENTORY, "ActionUseItem > src item remove failed. user(%d:%s), itemindex(%u), error(%d)", player->GetCharId(), player->GetCharName().c_str(), sourceItem->GetItemIndex(), eError);
			return eError;
		}

		// 각인 이전 대상 아이템 정보 업데이트
		ItemData targetItemCloned = targetItem->Clone();
		targetItemCloned.additionalOptionGrade = newGrade;
		targetItemCloned.additionalOptionMap.clear();

		for (auto& iter : additionalAbilityPack)
		{
			targetItemCloned.AppendOptionAbility<AdditionalAbilityPack>(iter, targetItemCloned.additionalOptionMap);
		}

		VERIFY_RETURN(bag.Update(targetItem, targetItemCloned), ErrorItem::BagUpdateFailed);
		VERIFY_RETURN(bag.Write(transPtr), ErrorItem::BagWriteFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::TranRegisterFailed);
	}

	return ErrorItem::SUCCESS;
}

#ifdef __Renewal_Additional_Grade_Transfer_by_kangms_181001
ErrorItem::Error ActionPlayerUseItem::UseTransferAdditionalOptionItemByNpc(const ItemPos& sourcePos, const ItemPos& targetPos)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	if (true == player->IsTrading()) {
		SystemMessage sysMsg(L"sys", L"Msg_UserTrade_Invalid_Not_Work");
		SendSystemMsg(player, sysMsg);
		return ErrorItem::E_TRADING;
	}

	//check npc function
	if (true != player->GetContactAction().HasNpcFunction(NpcFunctionType::NPC_FUNCTION_ADDITIONAL_GRADE_TRANSFER)) {
		ErrorItem::Error error = ErrorItem::E_NOT_NPC;
		MU2_ERROR_LOG( shared::LogCategory::ITEM
					 , "Failed to transfer AdditionalOption Grade because not registered NPC function !!! : NpcFuncID:%d - EC:%d, CharID:%d"
					 , NpcFunctionType::NPC_FUNCTION_ADDITIONAL_GRADE_TRANSFER
					 , error
					 , GetOwnerPlayer()->GetCharId() );
		theGameMsg.SendGameDebugMsg( GetOwnerPlayer(), L"Failed to transfer AdditionalOption Grade, not registered NPC function !!!\n" );
		return error;
	}
	//check npc distance
	if (true != player->GetContactAction().CheckNpcDistance()) {
		ErrorItem::Error error = ErrorItem::E_NPC_DISTANCE_CHECK;
		MU2_ERROR_LOG( shared::LogCategory::ITEM
			         , "Failed to transfer AdditionalOption Grade because NPC distance check !!! - EC:%d, CharID:%d"
			         , error
			         , GetOwnerPlayer()->GetCharId() );
		theGameMsg.SendGameDebugMsg( GetOwnerPlayer(), L"Failed to transfer AdditionalOption Grade, NPC distance check !!!\n" );
		return error;
	}

	const ItemConstPtr targetItem = player->GetInventoryAction().GetItem(targetPos);
	VERIFY_RETURN(targetItem, ErrorItem::itemNotFound);
	VERIFY_RETURN(targetItem->IsIdentified(), ErrorItem::E_UNIDENTIFIED_ITEM);
#ifdef __lockable_item_181001
	VALID_DO( targetItem->IsUnLocakble()
		    , SendSystemMsg(player, SystemMessage(L"sys", L"Msg_Item_locked_not_usable")); return ErrorItem::E_ITEM_LOCKED );
#endif

	const ItemConstPtr sourceItem = player->GetInventoryAction().GetItem(sourcePos);
	VERIFY_RETURN(sourceItem, ErrorItem::itemNotFound);
	VERIFY_RETURN(ItemIdentify::UNIDENTIFIED != sourceItem->GetIdentifyOption(), ErrorItem::E_UNIDENTIFIED_ITEM);
#ifdef __lockable_item_181001
	VALID_DO( sourceItem->IsUnLocakble()
		    , SendSystemMsg(player, SystemMessage(L"sys", L"Msg_Item_locked_not_usable")); return ErrorItem::E_ITEM_LOCKED; );
#endif

	AdditionalOptionGrade::Enum sourceGrade = sourceItem->GetAdditionalOptionGrade();
	AdditionalOptionGrade::Enum targetGrade = targetItem->GetAdditionalOptionGrade();
	if (    ( sourceGrade <= 0 && sourceItem->additionalOptionMap.size() <= 0 )
		 || ( sourceGrade <= targetGrade && sourceItem->additionalOptionMap.size() < targetItem->additionalOptionMap.size() ) ) {
		ErrorItem::Error error = ErrorItem::E_Additional_Grade_Greater_Than;
		MU2_ERROR_LOG( shared::LogCategory::ITEM
					 , "Failed to transfer AdditionalOption Grade because source & target Item AdditionalOption Grade check !!! : TgtGrade:%d >= SrcGrade:%d, TgtItemID:%I64d, SrcItemID:%I64d - EC:%d, CharID:%d"
					 , targetGrade, sourceGrade
					 , targetItem->GetItemId(), sourceItem->GetItemId()
					 , error
					 , GetOwnerPlayer()->GetCharId());
		theGameMsg.SendGameDebugMsg(GetOwnerPlayer(), L"Failed to transfer AdditionalOption Grade, source & target Item AdditionalOption Grade check !!!\n");
		return error;
	}
	
	InvenRange ranges(TalismanBags);
	AdditionalAbilityPackVector additionalAbilityPack;
	ErrorItem::Error eError = ItemFactory::TransferAdditionalOptionByNpc( player
		                                                                , sourceItem, targetItem
		                                                                , ranges
		                                                                , __out additionalAbilityPack );
	VERIFY_RETURN(ErrorItem::SUCCESS == eError, eError);

	// 트랜잭션 처리
	auto dbTransaction = std::make_shared<ItemTransferAdditionalOptionTransaction>( __FUNCTION__, __LINE__
																			      , player, LogCode::E_ITEM_TRANSFER_ADDITIONAL_OPTION
																				  , ProtocolItem::EREQ_TRANSFER_ADDITIONAL_OPTION_BY_NPC
																				  , targetItem->GetItemId()
																				  , sourceItem->GetItemIndex(), sourceGrade
#ifdef __LogMod_181017_by_ray_181018
																					, 0
#endif
		                                                                          , additionalAbilityPack );
	{
		TransactionPtr transPtr(dbTransaction);

		ItemBag bag(*player);

		ErrorItem::Error error = ErrorItem::SUCCESS;

		//각인 이전 재료 아이템 제거
		error = dbTransaction->RemoveMaterialItems(ranges, bag);
		VERIFY_RETURN(ErrorItem::SUCCESS == error, error);

		//각인 이전 추출 아이템 각인 옵션 제거
		ItemData sourceItemCloned = sourceItem->Clone();
		sourceItemCloned.additionalOptionGrade = AdditionalOptionGrade::NORMAL;
		sourceItemCloned.additionalOptionMap.clear();

		//각인 이전 대상 아이템 정보 업데이트
		ItemData targetItemCloned = targetItem->Clone();
		targetItemCloned.additionalOptionGrade = sourceGrade;
		targetItemCloned.additionalOptionMap.clear();
		//각인 이전 대상 아이템 각인 옵션 추가
		for (auto& iter : additionalAbilityPack) {
			targetItemCloned.AppendOptionAbility<AdditionalAbilityPack>(iter, targetItemCloned.additionalOptionMap);
		}

#ifdef	__Patch_Item_Own_Case_Imprint_Movement_Option_by_robinhwp_2019_04_02
		ItemFactory::CheckAndBind(player, targetItem, EquipBindState::ADDITIONAL_OPTION_MOVEMENT, targetItemCloned.bindProperty);
#else	__Patch_Item_Own_Case_Imprint_Movement_Option_by_robinhwp_2019_04_02
#endif	__Patch_Item_Own_Case_Imprint_Movement_Option_by_robinhwp_2019_04_02

		VERIFY_RETURN(bag.Update(sourceItem, sourceItemCloned), ErrorItem::BagUpdateFailed);
		VERIFY_RETURN(bag.Update(targetItem, targetItemCloned), ErrorItem::BagUpdateFailed);
		VERIFY_RETURN(bag.Write(transPtr), ErrorItem::BagWriteFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::TranRegisterFailed);
	}

	return ErrorItem::SUCCESS;
}
#endif//__Renewal_Additional_Grade_Transfer_by_kangms_181001

ErrorItem::Error ActionPlayerUseItem::UseCharacterSlotExtend( const ItemPos & sourcePos )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), ErrorItem::playerNotFound );

	if ( true == player->IsTrading())
	{
		SystemMessage sysMsg( L"sys", L"Msg_UserTrade_Invalid_Not_Work" );
		SendSystemMsg( player, sysMsg );
		return ErrorItem::E_TRADING;
	}

	const WorldElem* elem = player->GetJoinedWorldElem();
	VERIFY_RETURN(elem, ErrorItem::scriptNotFound);		
	VALID_RETURN(elem->specialItem_Enable, ErrorItem::E_REFER_SYSTEM_MSG);
	
	// 캐릭터 슬롯 확장 아이템
	const ItemConstPtr sourceItem = player->GetInventoryAction().GetItem(sourcePos);
	VERIFY_RETURN(sourceItem, ErrorItem::E_NOT_EXIST_ITEM);
	VERIFY_RETURN(sourceItem->GetDivision() == IDT::CHARACTER_SLOT_EXTEND, ErrorItem::E_INVALID_DIVISION);
#ifdef __lockable_item_181001
	VALID_DO( sourceItem->IsUnLocakble(), SendSystemMsg( player, SystemMessage( L"sys", L"Msg_Item_locked_not_usable" ) );
											return ErrorItem::E_ITEM_LOCKED; );
#endif
	TransactionPtr transPtr = std::make_shared<ItemCharacterSlotExtend>(__FUNCTION__, __LINE__, player, LogCode::E_CHARACTER_SLOT_EXTEND);
	{
		ItemBag bag(*player);

		// 캐릭터 확장 슬롯 아이템 삭제.
		ErrorItem::Error eError = player->GetInventoryAction().RemoveByPlace(sourceItem->GetPlace(), bag, 1);
		VERIFY_RETURN(ErrorItem::SUCCESS == eError, eError);

		VERIFY_RETURN(bag.Write(transPtr), ErrorItem::BagWriteFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::TranRegisterFailed);
	}

	return ErrorItem::SUCCESS;
}



ErrorItem::Error ActionPlayerUseItem::EmotionBoxOpen(const UInt8 etherBoxPos)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), ErrorItem::playerNotFound);
		
	if (owner->IsTrading())
	{
		SendSystemMsg(GetOwnerPlayer(), SystemMessage(L"sys", L"Msg_UserTrade_Invalid_UseItem"));
		return ErrorItem::E_TRADING;
	}

	if (owner->GetInventoryAction().EmotionGetBusy())
	{
		return ErrorItem::E_EMOTION_BOX_PROCESSING;
	}

	CharEmotionBoxes& boxes = owner->GetInventoryAction().GetEmotionBoxInfo();
	if (false == boxes.IsValidPos(etherBoxPos) || boxes.IsOpen(etherBoxPos))
	{
		// SendSystemMsg(owner, SystemMessage(L"sys", L"Msg_UserTrade_Invalid_UseItem"));
		return ErrorItem::E_EMOTION_BOX_STATE_OPEN;
	}

	auto xmlScript = SCRIPTS.GetScript<EmotionBoxXmlScript>();
	VERIFY_RETURN(xmlScript, ErrorItem::E_EMOTION_BOX_RESET_INFO);

	Bool needRefresh = !UseItemLimitInfo::IsDailyReset(boxes.resetDate, xmlScript->GetResetTime());
	if (needRefresh)
	{
		ENtfEmotionBoxInit * ntf = NEW ENtfEmotionBoxInit;
		SERVER.SendToClient(owner, EventPtr(ntf));

		return ErrorItem::E_EMOTION_BOX_RESET;
	}

	VALID_RETURN(boxes.CanOpen(etherBoxPos), ErrorItem::E_EMOTION_BOX_CENTER_NOT_READY);

	const IndexEmotionBox boxIndex = boxes.GetBoxIndex(etherBoxPos);
	if (boxIndex == 0 || boxIndex == EmotionBoxEnum::Unknown)
	{
		// SendSystemMsg(owner, SystemMessage(L"sys", L"Msg_UserTrade_Invalid_UseItem"));
		return ErrorItem::E_EMOTION_BOX_NOT_EXIST;
	}
	const EmotionBoxElem * elem = SCRIPTS.GetEmotionBoxElem(boxes.resetCount, boxIndex);
	VERIFY_RETURN(elem, ErrorItem::E_EMOTION_BOX_NOT_EXIST);

	VALID_RETURN(owner->GetEmotionPoint() >= elem->fee, ErrorItem::E_EMOTION_BOX_NEED_MORE_POINT);

	VALID_RETURN(owner->SubEmotionPoint(elem->fee), ErrorItem::E_EMOTION_BOX_NEED_MORE_POINT);

	ErrorItem::Error error = ErrorItem::SUCCESS;

	DropItemInfo oneItem;
	if (GetRandBetween(1, DEFAULT_RATE) <= elem->openRatio) // , ErrorItem::E_EMOTION_BOX_OPEN_FAIL);
	{
		DROPINFO_VECTOR dropItem;
		if (false == theDropSystem.getDropItem(elem->dropIndex, 1, dropItem) || dropItem.size() < 1)
		{
			error = ErrorItem::E_EMOTION_BOX_NO_ITEM;
		}
		else
		{
			oneItem = dropItem.at(0);

			owner->GetInventoryAction().SetEmotionBoxState(etherBoxPos, oneItem.Item_index, oneItem.Drop_number);

			ItemPushOrMailTransaction * trans = NEW ItemPushOrMailTransaction(__FUNCTION__, __LINE__, owner, LogCode::None);
			trans->Push(ItemData(oneItem.Item_index, oneItem.Drop_number));

			TransactionSystem::Register(TransactionPtr(trans));
		}
	}
	else
	{
		error = ErrorItem::E_EMOTION_BOX_OPEN_FAIL;
	}

	EReqLogDb2* log = NEW EReqLogDb2;
	EventPtr logEventPtr(log);
	EventSetter::FillLogForEntity(LogCode::E_EMOTION_BOX_OPEN, owner, log->action);

	log->action.var32s.push_back(etherBoxPos);
	log->action.var32s.push_back(oneItem.Item_index);
	log->action.var32s.push_back(oneItem.Drop_number);
	log->action.var32s.push_back(static_cast<Int32>(elem->fee));
	SendDataCenter(logEventPtr);


	EReqDbEmotionBoxUpdateState * reqDb = NEW EReqDbEmotionBoxUpdateState;
	reqDb->charId = owner->GetCharId();
	reqDb->pos = etherBoxPos;
	if (ErrorItem::SUCCESS == error)
		reqDb->openState = EmotionBoxOpenState::Open;
	else
		reqDb->openState = EmotionBoxOpenState::Closed;
	reqDb->rewardItem = oneItem.Item_index;
	reqDb->rewardCount = oneItem.Drop_number;

	SERVER.SendToDb(owner, EventPtr(reqDb));

	EResEmotionBoxOpen * res = NEW EResEmotionBoxOpen;
	EventPtr resPtf(res);

	res->result = error;
	res->itemIndex = oneItem.Item_index;
	res->itemCount = oneItem.Drop_number;
	res->pos = etherBoxPos;	

	SERVER.SendToClient(owner, resPtf);

	return ErrorItem::SUCCESS;
}




#ifdef __ItemEnchant_Renew_Jason_180508
#else
ErrorEnchantTicket::Error ActionPlayerUseItem::UseEnchantTicket(const ItemPos& slotEnchantTicket, const ItemPos& slotTarget, const ItemPos& slotProtectTicket)
{	
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), ErrorEnchantTicket::playerNotFound);
	
	if (owner->IsTrading())
	{
		SendSystemMsg(owner, SystemMessage(L"sys", L"Msg_UserTrade_Invalid_Not_Work"));
		return ErrorEnchantTicket::E_TRADING;
	}

	const ItemConstPtr targetItem = owner->GetInventoryAction().GetItem(slotTarget);
	VERIFY_RETURN(targetItem, ErrorEnchantTicket::E_NOT_EXIST_ITEM);

	const ItemConstPtr enchantTicket = owner->GetInventoryAction().GetItem(slotEnchantTicket);
	VERIFY_RETURN(enchantTicket, ErrorEnchantTicket::E_NOT_EXIST_ENCHANT_TICKET_ITEM);
	VERIFY_RETURN(IDT::ENCHANT_TICKET == enchantTicket->GetDivision(), ErrorEnchantTicket::InvalidDivisionType);

	if (enchantTicket->period.IsExpiredItem())
	{
		SendSystemMsg(owner, SystemMessage(L"sys", L"Msg_Item_Err_UseTime"));
		return ErrorEnchantTicket::E_EXPIRED_ITEM;
	}

	const ItemConstPtr protectTicket = owner->GetInventoryAction().GetItem(slotProtectTicket);
	VERIFY_DO(protectTicket, AllowNull);

	if (nullptr != protectTicket)
	{
		VERIFY_RETURN(ItemDivisionType::ENCHANT_PROTECT_TICKET == protectTicket->GetDivision(), ErrorEnchantTicket::E_NOT_EXIST_PROTECT_TICKET_ITEM);
	}

	const EnchantTicketScript* enchantTicketScript = SCRIPTS.GetScript<EnchantTicketScript>();
	VERIFY_RETURN(enchantTicketScript, ErrorEnchantTicket::scriptNotFound);

	Byte newEnchant = 0;
	ErrorEnchantTicket::Error eErrorEnchantTicket = enchantTicketScript->Enchant(
		enchantTicket->GetItemIndex(), targetItem->GetEnchantRawLevel(), targetItem->GetDivision(), targetItem->GetGrade(), OUT newEnchant);


	ItemBag bag(*owner);
	//Bool removeResult = true;

	switch (eErrorEnchantTicket)
	{
	case mu2::ErrorEnchantTicket::SUCCESS:
		{
			ItemData targetItemCloned = targetItem->Clone();
			targetItemCloned.enchant = newEnchant;			

			if (BindTo::CLASS_ON_USE == targetItem->GetBindTo().type)
			{
				targetItemCloned.bindProperty.charId = owner->GetCharId();
				targetItemCloned.bindProperty.isSealed = false;
			}

			VERIFY_RETURN(bag.Update(targetItem, targetItemCloned), ErrorEnchantTicket::bagUpdateFailed);

			AchievementEnchantItemSuccesEvent achievementEvent(owner, *targetItem, newEnchant);
			owner->GetAchievementAction().OnEvent(&achievementEvent);

			SeasonMissionEventEnchantItem seasonEvent(targetItem->GetItemIndex(), targetItem->GetDivision(), targetItem->GetGrade());
			owner->GetSeasonAction().Event(&seasonEvent);

		}
		break;

	case mu2::ErrorEnchantTicket::E_ENCHANT_FAIL:
		{
			ItemData targetItemCloned = targetItem->Clone();
			VERIFY_RETURN(bag.Update(targetItem, targetItemCloned), ErrorEnchantTicket::bagUpdateFailed);
		}
		break;

	default:
		return eErrorEnchantTicket;
	}

	ErrorItem::Error eErrorItemRemove = ErrorItem::SUCCESS;
	IndexItem protectitemIndex = 0;
	if (nullptr != protectTicket)
	{
		Int32 removeCount = 1;
		eErrorItemRemove = owner->GetInventoryAction().RemoveByPlace(protectTicket->GetPlace(), bag, removeCount);
		VERIFY_RETURN(ErrorItem::SUCCESS == eErrorItemRemove, static_cast<ErrorEnchantTicket::Error>(eErrorItemRemove));

		protectitemIndex = protectTicket->GetItemIndex();
	}

	Int32 removeCount = 1;
	eErrorItemRemove = owner->GetInventoryAction().RemoveByPlace(enchantTicket->GetPlace(), bag, removeCount);
	VERIFY_RETURN(ErrorItem::SUCCESS == eErrorItemRemove, static_cast<ErrorEnchantTicket::Error>(eErrorItemRemove));

	std::wstring logSubInfo;
	ItemEnchantTicketTransaction* trans = NEW ItemEnchantTicketTransaction(__FUNCTION__, __LINE__, owner, LogCode::E_USE_ENCHANT_TICKET, targetItem->GetItemId(), protectitemIndex);
	TransactionPtr transPtr(trans);
	{
		VERIFY_RETURN(bag.Write(transPtr), ErrorEnchantTicket::bagWriteFailed);

		trans->SetResult(eErrorEnchantTicket);
		trans->SetLogSubInfo(logSubInfo);

		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorEnchantTicket::TranRegisterFailed);
	}

	return ErrorEnchantTicket::SUCCESS;
}
#endif

#ifdef ABILITY_RENEWAL_20180508
ErrorItem::Error ActionPlayerUseItem::UseBraceletSlotOpen( const ItemPlace& sourcePos )
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN( owner && owner->IsValid(), ErrorItem::playerNotFound );

	if (owner->IsTrading())
	{
		SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_UserTrade_Invalid_Not_Work" ) );
		return ErrorItem::E_TRADING;
	}

	VALID_RETURN( owner->HasBracelet() == false, ErrorItem::E_CAN_NOT_UPGRADE );

	const ItemConstPtr sourceItem = owner->GetInventoryAction().GetItem( sourcePos );
	VALID_RETURN( sourceItem, ErrorItem::E_ERROR );
	VALID_RETURN( sourceItem->GetPlace() == sourcePos, ErrorItem::E_INVALID_ID );
#ifdef __lockable_item_181001
	VALID_DO( sourceItem->IsUnLocakble(), SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Item_locked_not_usable" ) );
											return ErrorItem::E_ITEM_LOCKED; );
#endif
	const BraceltOpenSlot& elem = SCRIPTS.GetBraceltOpenSlotInfo();

	ItemBraceletSlotOpenTranscation* transaction = NEW ItemBraceletSlotOpenTranscation( __FUNCTION__, __LINE__, owner, LogCode::E_BRACELT_SLOT_OPEN );
	TransactionPtr transPtr( transaction );
	{
		ItemBag bag( *owner );
		transaction->SetState( true );

		VALID_DO(owner->IsEnoughZen( elem.costZen ), SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Item_Enough_Money" ) );
													 return ErrorItem::E_NOT_ENOUGH_ZEN; );
		transPtr->BindMoney( owner->GetCharId(), elem.costZen, ChangedMoney::SUB, ChangedMoney::ZEN );

		VALID_DO(owner->IsEnoughGreenZen( elem.costGreenZen), SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Item_Err_Enough_Greenzen" ) );
																return ErrorItem::E_NOT_ENOUGH_GREEN_ZEN; );
		transPtr->BindMoney( owner->GetCharId(), elem.costGreenZen, ChangedMoney::SUB, ChangedMoney::GREENZEN );

		InvenRange range = InvenWithStorages;
		bag.PushTargetInvenRange(range);

		ErrorItem::Error eError = owner->GetInventoryAction().RemoveByPlace( sourceItem->GetPlace(), bag, 1 );
		VERIFY_RETURN( ErrorItem::SUCCESS == eError, eError );

		for (auto &costItem : elem.costInfos)
		{
			VALID_RETURN( owner->GetInventoryAction().IsEnoughItem( range, costItem.index, costItem.count ), ErrorItem::E_CHECK_MATERIAL_COUNT );

			VERIFY_RETURN( bag.Remove( ItemData( costItem.index, costItem.count ) ), ErrorItem::BagRemoveFailed );
		}

		VERIFY_RETURN( bag.Write( transPtr ), ErrorItem::BagWriteFailed );
		VERIFY_RETURN( TransactionSystem::Register( transPtr ), ErrorItem::TranBindFailed );
	}

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerUseItem::UseOptionScroll( const ItemPlace& usePos, const ItemPlace &targetPos, const SelectAbilityIndex selectIndex )
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN( owner && owner->IsValid(), ErrorItem::playerNotFound );

	if (owner->IsTrading())
	{
		SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_UserTrade_Invalid_Not_Work" ) );
		return ErrorItem::E_TRADING;
	}

	const ItemConstPtr useItem = owner->GetInventoryAction().GetItem( usePos );
	VALID_RETURN( useItem, ErrorItem::E_ERROR );
	VALID_RETURN( useItem->GetPlace() == usePos, ErrorItem::E_INVALID_ID );
#ifdef __lockable_item_181001
	VALID_DO( useItem->IsUnLocakble(), SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Item_locked_not_usable" ) );
										return ErrorItem::E_ITEM_LOCKED; );
#endif
	VALID_RETURN( useItem->GetDivision() == IDT::ITEM_OPTION_SCROLL, ErrorItem::E_INVALID_DIVISION );

	const ItemConstPtr targetItem = owner->GetInventoryAction().GetItem( targetPos );
	VALID_RETURN( targetItem, ErrorItem::E_ERROR );
	VALID_RETURN( targetItem->GetPlace() == targetPos, ErrorItem::E_INVALID_ID );
#ifdef __lockable_item_181001
	VALID_DO( targetItem->IsUnLocakble(), SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Item_locked_not_usable" ) );
									 	  return ErrorItem::E_ITEM_LOCKED; );
#endif
	ItemData resultItem = targetItem->Clone();

	auto selectAbility = resultItem.selectAbilityPackMap.find( selectIndex );
	VALID_RETURN( selectAbility != resultItem.selectAbilityPackMap.end(), ErrorItem::E_FAILED );
	VALID_RETURN( selectAbility->second.selectCount < selectAbility->second.maxSelectCount, ErrorItem::E_FAILED );

	auto elem = SCRIPTS.GetEmptyOptionElem( useItem->GetItemIndex() );
	VALID_RETURN( elem, ErrorItem::E_FAILED );

	ProbAbility maxRate = std::min( static_cast<ProbAbility>(Limits::MAX_DEFAULT_RAND), elem->successRate );
	ProbAbility rate = GetRandBetween( 0, Limits::MAX_DEFAULT_RAND );
	Bool isSuccess = rate <= maxRate;

	// 성공
	AbilityType prevType   = EAT::NONE; 
	AbilityValue prevValue = 0;
	BYTE sectionType = EffectSection::NONE;
	LogCode::Enum logCode;
	if (isSuccess)
	{
		logCode = LogCode::E_USE_OPTION_SCROLL_SUCCESS;

		// 중복 옵션 체크
		for (auto iter : resultItem.selectAbilityPackMap)
		{
			VALID_DO( iter.second.type == elem->abilityTypeSection.type, continue );
			VALID_DO( iter.first != selectIndex, continue );

			switch (elem->abilityTypeSection.section)
			{
			case EffectSection::PERCENT:
				if (iter.second.percent)
					return ErrorItem::E_FAILED;
				break;
			case EffectSection::POINT:
				if (iter.second.point)
					return ErrorItem::E_FAILED;
				break;
			}
		}
		
		AbilityValue value = GetRandBetween( elem->minValue, elem->maxValue );

		prevType = selectAbility->second.type;
		

		selectAbility->second.type = EAT::NONE;
		selectAbility->second.percent = 0;
		selectAbility->second.point = 0;

		selectAbility->second.type = elem->abilityTypeSection.type;
		switch (elem->abilityTypeSection.section)
		{
		case EffectSection::PERCENT:
			sectionType = EffectSection::PERCENT;
			prevValue   = selectAbility->second.percent;
			selectAbility->second.percent = value;
			
			break;
		case EffectSection::POINT:
			sectionType = EffectSection::POINT;
			prevValue	= selectAbility->second.point;
			selectAbility->second.point = value;
			break;
		}
	}
	else // 실패
	{
		logCode = LogCode::E_USE_OPTION_SCROLL_FAIL;
	}

	selectAbility->second.selectCount++;

	

	ItemUseOptionScrollTranscation* transaction = NEW ItemUseOptionScrollTranscation( __FUNCTION__, __LINE__, owner, logCode);
	TransactionPtr transPtr( transaction );
	{
		ItemBag bag( *owner );

		ErrorItem::Error eError = owner->GetInventoryAction().RemoveByPlace( useItem->GetPlace(), bag, 1 );
		VERIFY_RETURN( ErrorItem::SUCCESS == eError, eError );

		VERIFY_RETURN( bag.Update( targetItem, resultItem ), ErrorItem::BagUpdateFailed );

		transaction->SetTargetItem(resultItem);
		transaction->SetSelectAbility(selectIndex);
		transaction->SetSuccess(isSuccess);

		if (isSuccess)
		{
			
			transaction->SetSection(sectionType);
			transaction->SetPrevType(prevType);
			transaction->SetPrevValue(prevValue);
		}
		else
		{
			transaction->SetUseScrollCnt(1);
			transaction->SetUseScrollIndex(useItem->indexItem);
		}

		VERIFY_RETURN( bag.Write( transPtr ), ErrorItem::BagWriteFailed );
		VERIFY_RETURN( TransactionSystem::Register( transPtr ), ErrorItem::TranBindFailed );
	}

	return ErrorItem::SUCCESS;
}

#endif // ABILITY_RENEWAL_20180508

} // mu2
