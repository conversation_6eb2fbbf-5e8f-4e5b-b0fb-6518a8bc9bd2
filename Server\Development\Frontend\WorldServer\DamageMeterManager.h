﻿#pragma once
#include <Frontend/WorldServer/EntityManager.h>


namespace mu2
{
	class DamageMeterManager : public ISingleton<DamageMeterManager>
	{
		friend ISingleton<DamageMeterManager>;
	public:
		typedef std::map< DamageMeterKey, DmgMembers > DamageMeterList;
		typedef std::map< DamageMeterKey, GroupDamageReport > DamageMeterReportMap;

	private:
		DamageMeterList m_dmgMap;	// 멤버리스트
		DamageMeterReportMap m_dmgReportMap; // 데미지 리포트맵

	public:		
		virtual~DamageMeterManager() override;

		virtual Bool Init(void*param = NULL) override { UNREFERENCED_PARAMETER(param); return true; }
		virtual Bool UnInit() override { return true; }

		DamageMeterInfoType::Enum CreateDamageMeterInfo(DamageMeterKey damagemeterKey, PlayerPtr entityPtr);
		void GetMyDamageReport(DamageMeterKey damagemeterKey, CharId charId, DamageMeterReport*& out);

		Bool IsExistMember(DamageMeterKey damagemeterKey, CharId charId);
		void LeaveExecution(DamageMeterKey damagemeterKey, CharId charId);
		void SendToZoneDamageMemberLeave(DamageMeterKey damagemeterKey, CharId charId);
		void GetDmgMembers(DamageMeterKey damagemeterKey, DmgMembers& out);
		void GetGroupDamageReports(DamageMeterKey damagemeterKey, GroupDamageReport*& out);

	private:
		DamageMeterManager();
		DmgMembers* getDamageMeterMembers(DamageMeterKey damagemeterKey);
		GroupDamageReport* getDamageMeterReports(DamageMeterKey damagemeterKey);
		void sendGroupDamageReport(DamageMeterKey damagemeterKey, DamageMeterReport*& myReport, PlayerPtr enityPtr);
		void calculateMyDamageReportInfo(DamageMeterReport*& myReport);

	};

}// end of namespace