; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	??0KnightageSaveChecker@mu2@@QEAA@XZ		; mu2::KnightageSaveChecker::KnightageSaveChecker
PUBLIC	??1KnightageSaveChecker@mu2@@QEAA@XZ		; mu2::<PERSON><PERSON><PERSON>ave<PERSON>hecker::~<PERSON><PERSON><PERSON>ave<PERSON>hecker
PUBLIC	?Reset@<PERSON>age<PERSON>ave<PERSON>he<PERSON>@mu2@@QEAAXXZ	; mu2::KnightageSaveChecker::Reset
PUBLIC	?CheckAndChange@KnightageSaveChecker@mu2@@QEAA_N_K_J@Z ; mu2::KnightageSaveChecker::CheckAndChange
PUBLIC	?CheckSaveTimer@Knightage<PERSON><PERSON><PERSON><PERSON><PERSON>@mu2@@QEAA_NXZ ; mu2::KnightageSaveChecker::CheckSaveTimer
EXTRN	__imp_GetTickCount64:PROC
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0KnightageSaveChecker@mu2@@QEAA@XZ DD imagerel $LN5
	DD	imagerel $LN5+115
	DD	imagerel $unwind$??0KnightageSaveChecker@mu2@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Reset@KnightageSaveChecker@mu2@@QEAAXXZ DD imagerel $LN5
	DD	imagerel $LN5+76
	DD	imagerel $unwind$?Reset@KnightageSaveChecker@mu2@@QEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?CheckAndChange@KnightageSaveChecker@mu2@@QEAA_N_K_J@Z DD imagerel $LN5
	DD	imagerel $LN5+123
	DD	imagerel $unwind$?CheckAndChange@KnightageSaveChecker@mu2@@QEAA_N_K_J@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?CheckSaveTimer@KnightageSaveChecker@mu2@@QEAA_NXZ DD imagerel $LN8
	DD	imagerel $LN8+100
	DD	imagerel $unwind$?CheckSaveTimer@KnightageSaveChecker@mu2@@QEAA_NXZ
pdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?CheckSaveTimer@KnightageSaveChecker@mu2@@QEAA_NXZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?CheckAndChange@KnightageSaveChecker@mu2@@QEAA_N_K_J@Z DD 011301H
	DD	04213H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Reset@KnightageSaveChecker@mu2@@QEAAXXZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0KnightageSaveChecker@mu2@@QEAA@XZ DD 010901H
	DD	06209H
xdata	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Knightage\KnightageSaveChecker.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\SimpleTimer.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Knightage\KnightageSaveChecker.cpp
;	COMDAT ?CheckSaveTimer@KnightageSaveChecker@mu2@@QEAA_NXZ
_TEXT	SEGMENT
tv73 = 32
$T1 = 36
this$ = 64
?CheckSaveTimer@KnightageSaveChecker@mu2@@QEAA_NXZ PROC	; mu2::KnightageSaveChecker::CheckSaveTimer, COMDAT

; 39   : {

$LN8:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 40   : 	if (m_changedZen == 0 && m_changedExp == 0)

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 78 28 00	 cmp	 QWORD PTR [rax+40], 0
  00013	75 10		 jne	 SHORT $LN2@CheckSaveT
  00015	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 83 78 20 00	 cmp	 QWORD PTR [rax+32], 0
  0001f	75 04		 jne	 SHORT $LN2@CheckSaveT

; 41   : 	{
; 42   : 		return false;

  00021	32 c0		 xor	 al, al
  00023	eb 3a		 jmp	 SHORT $LN1@CheckSaveT
$LN2@CheckSaveT:
; File F:\Release_Branch\Server\Development\Framework\Core\SimpleTimer.h

; 33   : 		return static_cast<Tick>(::GetTickCount64() - m_start);

  00025	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetTickCount64
  0002b	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00030	8b 49 14	 mov	 ecx, DWORD PTR [rcx+20]
  00033	48 2b c1	 sub	 rax, rcx
  00036	89 44 24 24	 mov	 DWORD PTR $T1[rsp], eax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Knightage\KnightageSaveChecker.cpp

; 45   : 	return m_timer.Elapsed() > this->SAVE_CYCLE_TICK;

  0003a	8b 44 24 24	 mov	 eax, DWORD PTR $T1[rsp]
  0003e	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00043	3b 41 10	 cmp	 eax, DWORD PTR [rcx+16]
  00046	76 0a		 jbe	 SHORT $LN4@CheckSaveT
  00048	c7 44 24 20 01
	00 00 00	 mov	 DWORD PTR tv73[rsp], 1
  00050	eb 08		 jmp	 SHORT $LN5@CheckSaveT
$LN4@CheckSaveT:
  00052	c7 44 24 20 00
	00 00 00	 mov	 DWORD PTR tv73[rsp], 0
$LN5@CheckSaveT:
  0005a	0f b6 44 24 20	 movzx	 eax, BYTE PTR tv73[rsp]
$LN1@CheckSaveT:

; 46   : }

  0005f	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00063	c3		 ret	 0
?CheckSaveTimer@KnightageSaveChecker@mu2@@QEAA_NXZ ENDP	; mu2::KnightageSaveChecker::CheckSaveTimer
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Knightage\KnightageSaveChecker.cpp
;	COMDAT ?CheckAndChange@KnightageSaveChecker@mu2@@QEAA_N_K_J@Z
_TEXT	SEGMENT
this$ = 48
addExp$ = 56
addZen$ = 64
?CheckAndChange@KnightageSaveChecker@mu2@@QEAA_N_K_J@Z PROC ; mu2::KnightageSaveChecker::CheckAndChange, COMDAT

; 25   : {

$LN5:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 26   : 
; 27   : 	m_changedExp += addExp;

  00013	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00018	48 8b 40 20	 mov	 rax, QWORD PTR [rax+32]
  0001c	48 03 44 24 38	 add	 rax, QWORD PTR addExp$[rsp]
  00021	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00026	48 89 41 20	 mov	 QWORD PTR [rcx+32], rax

; 28   : 	m_changedZen += addZen;

  0002a	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0002f	48 8b 40 28	 mov	 rax, QWORD PTR [rax+40]
  00033	48 03 44 24 40	 add	 rax, QWORD PTR addZen$[rsp]
  00038	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0003d	48 89 41 28	 mov	 QWORD PTR [rcx+40], rax

; 29   : 	
; 30   : 	if (m_changedExp >= this->CHANGE_LIMIT_FOR_EXP ||

  00041	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00046	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0004b	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0004e	48 39 48 20	 cmp	 QWORD PTR [rax+32], rcx
  00052	73 14		 jae	 SHORT $LN3@CheckAndCh
  00054	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00059	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0005e	48 8b 49 08	 mov	 rcx, QWORD PTR [rcx+8]
  00062	48 39 48 28	 cmp	 QWORD PTR [rax+40], rcx
  00066	7c 04		 jl	 SHORT $LN2@CheckAndCh
$LN3@CheckAndCh:

; 31   : 		m_changedZen >= this->CHANGE_LIMIT_FOR_ZEN)
; 32   : 	{
; 33   : 		return true;

  00068	b0 01		 mov	 al, 1
  0006a	eb 0a		 jmp	 SHORT $LN1@CheckAndCh
$LN2@CheckAndCh:

; 34   : 	}
; 35   : 	return CheckSaveTimer();

  0006c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00071	e8 00 00 00 00	 call	 ?CheckSaveTimer@KnightageSaveChecker@mu2@@QEAA_NXZ ; mu2::KnightageSaveChecker::CheckSaveTimer
$LN1@CheckAndCh:

; 36   : }

  00076	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0007a	c3		 ret	 0
?CheckAndChange@KnightageSaveChecker@mu2@@QEAA_N_K_J@Z ENDP ; mu2::KnightageSaveChecker::CheckAndChange
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Knightage\KnightageSaveChecker.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\SimpleTimer.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Knightage\KnightageSaveChecker.cpp
;	COMDAT ?Reset@KnightageSaveChecker@mu2@@QEAAXXZ
_TEXT	SEGMENT
this$ = 32
this$ = 64
?Reset@KnightageSaveChecker@mu2@@QEAAXXZ PROC		; mu2::KnightageSaveChecker::Reset, COMDAT

; 18   : {

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 19   : 	m_timer.Reset();

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 c0 14	 add	 rax, 20
  00012	48 89 44 24 20	 mov	 QWORD PTR this$[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\SimpleTimer.h

; 38   : 		m_start = static_cast<Tick>(::GetTickCount64());

  00017	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetTickCount64
  0001d	48 8b 4c 24 20	 mov	 rcx, QWORD PTR this$[rsp]
  00022	89 01		 mov	 DWORD PTR [rcx], eax

; 39   : 		m_isInvoked = false;

  00024	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00029	c6 40 08 00	 mov	 BYTE PTR [rax+8], 0
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Knightage\KnightageSaveChecker.cpp

; 20   : 	m_changedExp = 0;

  0002d	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00032	48 c7 40 20 00
	00 00 00	 mov	 QWORD PTR [rax+32], 0

; 21   : 	m_changedZen = 0;

  0003a	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0003f	48 c7 40 28 00
	00 00 00	 mov	 QWORD PTR [rax+40], 0

; 22   : }

  00047	48 83 c4 38	 add	 rsp, 56			; 00000038H
  0004b	c3		 ret	 0
?Reset@KnightageSaveChecker@mu2@@QEAAXXZ ENDP		; mu2::KnightageSaveChecker::Reset
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Knightage\KnightageSaveChecker.cpp
;	COMDAT ??1KnightageSaveChecker@mu2@@QEAA@XZ
_TEXT	SEGMENT
this$ = 8
??1KnightageSaveChecker@mu2@@QEAA@XZ PROC		; mu2::KnightageSaveChecker::~KnightageSaveChecker, COMDAT

; 14   : {

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 15   : }

  00005	c3		 ret	 0
??1KnightageSaveChecker@mu2@@QEAA@XZ ENDP		; mu2::KnightageSaveChecker::~KnightageSaveChecker
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Knightage\KnightageSaveChecker.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Knightage\KnightageSaveChecker.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Knightage\KnightageSaveChecker.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\SimpleTimer.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Knightage\KnightageSaveChecker.cpp
;	COMDAT ??0KnightageSaveChecker@mu2@@QEAA@XZ
_TEXT	SEGMENT
this$ = 32
this$ = 64
??0KnightageSaveChecker@mu2@@QEAA@XZ PROC		; mu2::KnightageSaveChecker::KnightageSaveChecker, COMDAT

; 8    : {

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Knightage\KnightageSaveChecker.h

; 18   : 	const UInt64 CHANGE_LIMIT_FOR_EXP = 1000;

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 c7 00 e8 03
	00 00		 mov	 QWORD PTR [rax], 1000	; 000003e8H

; 19   : 	const Zen CHANGE_LIMIT_FOR_ZEN = 1000;

  00015	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 c7 40 08 e8
	03 00 00	 mov	 QWORD PTR [rax+8], 1000	; 000003e8H

; 20   : 	const Tick SAVE_CYCLE_TICK = 3000;

  00022	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00027	c7 40 10 b8 0b
	00 00		 mov	 DWORD PTR [rax+16], 3000 ; 00000bb8H
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Knightage\KnightageSaveChecker.cpp

; 8    : {

  0002e	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00033	48 83 c0 14	 add	 rax, 20
  00037	48 89 44 24 20	 mov	 QWORD PTR this$[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\SimpleTimer.h

; 19   : 		, m_start(static_cast<Tick>(::GetTickCount64()))

  0003c	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetTickCount64
  00042	48 8b 4c 24 20	 mov	 rcx, QWORD PTR this$[rsp]
  00047	89 01		 mov	 DWORD PTR [rcx], eax

; 17   : 		: m_msInterval(msInterval)

  00049	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0004e	c7 40 04 00 00
	00 00		 mov	 DWORD PTR [rax+4], 0

; 18   : 		, m_isInvoked(0)

  00055	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0005a	c6 40 08 00	 mov	 BYTE PTR [rax+8], 0
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Knightage\KnightageSaveChecker.cpp

; 9    : 	Reset();

  0005e	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00063	e8 00 00 00 00	 call	 ?Reset@KnightageSaveChecker@mu2@@QEAAXXZ ; mu2::KnightageSaveChecker::Reset
  00068	90		 npad	 1

; 10   : }

  00069	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0006e	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00072	c3		 ret	 0
??0KnightageSaveChecker@mu2@@QEAA@XZ ENDP		; mu2::KnightageSaveChecker::KnightageSaveChecker
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Knightage\KnightageSaveChecker.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Knightage\KnightageSaveChecker.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
