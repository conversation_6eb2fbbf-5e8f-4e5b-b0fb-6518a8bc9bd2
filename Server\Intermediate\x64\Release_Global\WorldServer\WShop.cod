; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
PUBLIC	??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
PUBLIC	?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_empty
PUBLIC	?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate
PUBLIC	??_G?$ISingleton@VWShop@mu2@@@mu2@@UEAAPEAXI@Z	; mu2::ISingleton<mu2::WShop>::`scalar deleting destructor'
PUBLIC	??1WShop@mu2@@UEAA@XZ				; mu2::WShop::~WShop
PUBLIC	?Init@WShop@mu2@@UEAA_NPEAX@Z			; mu2::WShop::Init
PUBLIC	?UnInit@WShop@mu2@@UEAA_NXZ			; mu2::WShop::UnInit
PUBLIC	?Initialize@WShop@mu2@@QEAA_NXZ			; mu2::WShop::Initialize
PUBLIC	?Release@WShop@mu2@@QEAAXXZ			; mu2::WShop::Release
PUBLIC	?MakeOrderNo@WShop@mu2@@QEAA_KXZ		; mu2::WShop::MakeOrderNo
PUBLIC	??0WShop@mu2@@AEAA@XZ				; mu2::WShop::WShop
PUBLIC	??_GWShop@mu2@@UEAAPEAXI@Z			; mu2::WShop::`scalar deleting destructor'
PUBLIC	??_7?$ISingleton@VWShop@mu2@@@mu2@@6B@		; mu2::ISingleton<mu2::WShop>::`vftable'
PUBLIC	??_7WShop@mu2@@6B@				; mu2::WShop::`vftable'
PUBLIC	??_R4WShop@mu2@@6B@				; mu2::WShop::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVWShop@mu2@@@8				; mu2::WShop `RTTI Type Descriptor'
PUBLIC	??_R3WShop@mu2@@8				; mu2::WShop::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2WShop@mu2@@8				; mu2::WShop::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@WShop@mu2@@8			; mu2::WShop::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@?$ISingleton@VWShop@mu2@@@mu2@@8	; mu2::ISingleton<mu2::WShop>::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R0?AV?$ISingleton@VWShop@mu2@@@mu2@@@8	; mu2::ISingleton<mu2::WShop> `RTTI Type Descriptor'
PUBLIC	??_R3?$ISingleton@VWShop@mu2@@@mu2@@8		; mu2::ISingleton<mu2::WShop>::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2?$ISingleton@VWShop@mu2@@@mu2@@8		; mu2::ISingleton<mu2::WShop>::`RTTI Base Class Array'
PUBLIC	??_R4?$ISingleton@VWShop@mu2@@@mu2@@6B@		; mu2::ISingleton<mu2::WShop>::`RTTI Complete Object Locator'
EXTRN	_purecall:PROC
EXTRN	??2@YAPEAX_K@Z:PROC				; operator new
EXTRN	??3@YAXPEAX_K@Z:PROC				; operator delete
EXTRN	__std_terminate:PROC
EXTRN	_invoke_watson:PROC
EXTRN	_time64:PROC
EXTRN	??0WShopBillingEventHandler@mu2@@QEAA@XZ:PROC	; mu2::WShopBillingEventHandler::WShopBillingEventHandler
EXTRN	??0WShopJewelEventHandler@mu2@@QEAA@XZ:PROC	; mu2::WShopJewelEventHandler::WShopJewelEventHandler
EXTRN	??0WShopStatusEventHandler@mu2@@QEAA@XZ:PROC	; mu2::WShopStatusEventHandler::WShopStatusEventHandler
EXTRN	??0WShopInventoryEventHandler@mu2@@QEAA@XZ:PROC	; mu2::WShopInventoryEventHandler::WShopInventoryEventHandler
EXTRN	??_E?$ISingleton@VWShop@mu2@@@mu2@@UEAAPEAXI@Z:PROC ; mu2::ISingleton<mu2::WShop>::`vector deleting destructor'
EXTRN	??_EWShop@mu2@@UEAAPEAXI@Z:PROC			; mu2::WShop::`vector deleting destructor'
EXTRN	__CxxFrameHandler4:PROC
EXTRN	??_7type_info@@6B@:BYTE				; type_info::`vftable'
EXTRN	?Instance@Server@mu2@@2PEAV12@EA:QWORD		; mu2::Server::Instance
;	COMDAT ?sequence@?1??MakeOrderNo@WShop@mu2@@QEAA_KXZ@4FA
_BSS	SEGMENT
?sequence@?1??MakeOrderNo@WShop@mu2@@QEAA_KXZ@4FA DW 01H DUP (?) ; `mu2::WShop::MakeOrderNo'::`2'::sequence
_BSS	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z DD imagerel $LN5
	DD	imagerel $LN5+159
	DD	imagerel $unwind$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ DD imagerel $LN41
	DD	imagerel $LN41+107
	DD	imagerel $unwind$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DD imagerel $LN18
	DD	imagerel $LN18+98
	DD	imagerel $unwind$?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DD imagerel $LN62
	DD	imagerel $LN62+266
	DD	imagerel $unwind$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_G?$ISingleton@VWShop@mu2@@@mu2@@UEAAPEAXI@Z DD imagerel $LN10
	DD	imagerel $LN10+65
	DD	imagerel $unwind$??_G?$ISingleton@VWShop@mu2@@@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1WShop@mu2@@UEAA@XZ DD imagerel $LN92
	DD	imagerel $LN92+62
	DD	imagerel $unwind$??1WShop@mu2@@UEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Initialize@WShop@mu2@@QEAA_NXZ DD imagerel $LN54
	DD	imagerel $LN54+1204
	DD	imagerel $unwind$?Initialize@WShop@mu2@@QEAA_NXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0??Initialize@WShop@mu2@@QEAA_NXZ@4HA DD imagerel ?dtor$0@?0??Initialize@WShop@mu2@@QEAA_NXZ@4HA
	DD	imagerel ?dtor$0@?0??Initialize@WShop@mu2@@QEAA_NXZ@4HA+29
	DD	imagerel $unwind$?dtor$0@?0??Initialize@WShop@mu2@@QEAA_NXZ@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$1@?0??Initialize@WShop@mu2@@QEAA_NXZ@4HA DD imagerel ?dtor$1@?0??Initialize@WShop@mu2@@QEAA_NXZ@4HA
	DD	imagerel ?dtor$1@?0??Initialize@WShop@mu2@@QEAA_NXZ@4HA+29
	DD	imagerel $unwind$?dtor$1@?0??Initialize@WShop@mu2@@QEAA_NXZ@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$2@?0??Initialize@WShop@mu2@@QEAA_NXZ@4HA DD imagerel ?dtor$2@?0??Initialize@WShop@mu2@@QEAA_NXZ@4HA
	DD	imagerel ?dtor$2@?0??Initialize@WShop@mu2@@QEAA_NXZ@4HA+29
	DD	imagerel $unwind$?dtor$2@?0??Initialize@WShop@mu2@@QEAA_NXZ@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$3@?0??Initialize@WShop@mu2@@QEAA_NXZ@4HA DD imagerel ?dtor$3@?0??Initialize@WShop@mu2@@QEAA_NXZ@4HA
	DD	imagerel ?dtor$3@?0??Initialize@WShop@mu2@@QEAA_NXZ@4HA+32
	DD	imagerel $unwind$?dtor$3@?0??Initialize@WShop@mu2@@QEAA_NXZ@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Release@WShop@mu2@@QEAAXXZ DD imagerel $LN34
	DD	imagerel $LN34+891
	DD	imagerel $unwind$?Release@WShop@mu2@@QEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?MakeOrderNo@WShop@mu2@@QEAA_KXZ DD imagerel $LN7
	DD	imagerel $LN7+104
	DD	imagerel $unwind$?MakeOrderNo@WShop@mu2@@QEAA_KXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0WShop@mu2@@AEAA@XZ DD imagerel $LN49
	DD	imagerel $LN49+170
	DD	imagerel $unwind$??0WShop@mu2@@AEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GWShop@mu2@@UEAAPEAXI@Z DD imagerel $LN5
	DD	imagerel $LN5+60
	DD	imagerel $unwind$??_GWShop@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT ??_R4?$ISingleton@VWShop@mu2@@@mu2@@6B@
rdata$r	SEGMENT
??_R4?$ISingleton@VWShop@mu2@@@mu2@@6B@ DD 01H		; mu2::ISingleton<mu2::WShop>::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AV?$ISingleton@VWShop@mu2@@@mu2@@@8
	DD	imagerel ??_R3?$ISingleton@VWShop@mu2@@@mu2@@8
	DD	imagerel ??_R4?$ISingleton@VWShop@mu2@@@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R2?$ISingleton@VWShop@mu2@@@mu2@@8
rdata$r	SEGMENT
??_R2?$ISingleton@VWShop@mu2@@@mu2@@8 DD imagerel ??_R1A@?0A@EA@?$ISingleton@VWShop@mu2@@@mu2@@8 ; mu2::ISingleton<mu2::WShop>::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3?$ISingleton@VWShop@mu2@@@mu2@@8
rdata$r	SEGMENT
??_R3?$ISingleton@VWShop@mu2@@@mu2@@8 DD 00H		; mu2::ISingleton<mu2::WShop>::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2?$ISingleton@VWShop@mu2@@@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AV?$ISingleton@VWShop@mu2@@@mu2@@@8
data$rs	SEGMENT
??_R0?AV?$ISingleton@VWShop@mu2@@@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::ISingleton<mu2::WShop> `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AV?$ISingleton@VWShop@mu2@@@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R1A@?0A@EA@?$ISingleton@VWShop@mu2@@@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@?$ISingleton@VWShop@mu2@@@mu2@@8 DD imagerel ??_R0?AV?$ISingleton@VWShop@mu2@@@mu2@@@8 ; mu2::ISingleton<mu2::WShop>::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3?$ISingleton@VWShop@mu2@@@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@WShop@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@WShop@mu2@@8 DD imagerel ??_R0?AVWShop@mu2@@@8 ; mu2::WShop::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3WShop@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2WShop@mu2@@8
rdata$r	SEGMENT
??_R2WShop@mu2@@8 DD imagerel ??_R1A@?0A@EA@WShop@mu2@@8 ; mu2::WShop::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@?$ISingleton@VWShop@mu2@@@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3WShop@mu2@@8
rdata$r	SEGMENT
??_R3WShop@mu2@@8 DD 00H				; mu2::WShop::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2WShop@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVWShop@mu2@@@8
data$rs	SEGMENT
??_R0?AVWShop@mu2@@@8 DQ FLAT:??_7type_info@@6B@	; mu2::WShop `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVWShop@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4WShop@mu2@@6B@
rdata$r	SEGMENT
??_R4WShop@mu2@@6B@ DD 01H				; mu2::WShop::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVWShop@mu2@@@8
	DD	imagerel ??_R3WShop@mu2@@8
	DD	imagerel ??_R4WShop@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_7WShop@mu2@@6B@
CONST	SEGMENT
??_7WShop@mu2@@6B@ DQ FLAT:??_R4WShop@mu2@@6B@		; mu2::WShop::`vftable'
	DQ	FLAT:?Init@WShop@mu2@@UEAA_NPEAX@Z
	DQ	FLAT:?UnInit@WShop@mu2@@UEAA_NXZ
	DQ	FLAT:??_EWShop@mu2@@UEAAPEAXI@Z
CONST	ENDS
;	COMDAT ??_7?$ISingleton@VWShop@mu2@@@mu2@@6B@
CONST	SEGMENT
??_7?$ISingleton@VWShop@mu2@@@mu2@@6B@ DQ FLAT:??_R4?$ISingleton@VWShop@mu2@@@mu2@@6B@ ; mu2::ISingleton<mu2::WShop>::`vftable'
	DQ	FLAT:_purecall
	DQ	FLAT:_purecall
	DQ	FLAT:??_E?$ISingleton@VWShop@mu2@@@mu2@@UEAAPEAXI@Z
CONST	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GWShop@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0WShop@mu2@@AEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DB	014H
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?MakeOrderNo@WShop@mu2@@QEAA_KXZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Release@WShop@mu2@@QEAAXXZ DD 020c01H
	DD	019010cH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$3@?0??Initialize@WShop@mu2@@QEAA_NXZ@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$2@?0??Initialize@WShop@mu2@@QEAA_NXZ@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$1@?0??Initialize@WShop@mu2@@QEAA_NXZ@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0??Initialize@WShop@mu2@@QEAA_NXZ@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?Initialize@WShop@mu2@@QEAA_NXZ DB 012H
	DB	00H
	DB	00H
	DB	015H, 06H
	DB	02H
	DB	'^'
	DB	00H
	DB	'1', 02H
	DB	04H
	DB	'^'
	DB	00H
	DB	'1', 02H
	DB	06H
	DB	'^'
	DB	00H
	DB	'=', 02H
	DB	08H
	DB	'|'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?Initialize@WShop@mu2@@QEAA_NXZ DB 08H
	DB	0eH
	DD	imagerel ?dtor$0@?0??Initialize@WShop@mu2@@QEAA_NXZ@4HA
	DB	036H
	DD	imagerel ?dtor$1@?0??Initialize@WShop@mu2@@QEAA_NXZ@4HA
	DB	05eH
	DD	imagerel ?dtor$2@?0??Initialize@WShop@mu2@@QEAA_NXZ@4HA
	DB	086H
	DD	imagerel ?dtor$3@?0??Initialize@WShop@mu2@@QEAA_NXZ@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?Initialize@WShop@mu2@@QEAA_NXZ DB 028H
	DD	imagerel $stateUnwindMap$?Initialize@WShop@mu2@@QEAA_NXZ
	DD	imagerel $ip2state$?Initialize@WShop@mu2@@QEAA_NXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Initialize@WShop@mu2@@QEAA_NXZ DD 020c11H
	DD	027010cH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?Initialize@WShop@mu2@@QEAA_NXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1WShop@mu2@@UEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_G?$ISingleton@VWShop@mu2@@@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DB 06H
	DB	00H
	DB	00H
	DB	089H, 02H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DB 068H
	DD	imagerel $stateUnwindMap$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
	DD	imagerel $ip2state$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DD 010919H
	DD	0e209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DD 020a01H
	DD	07006120aH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ DB 02H
	DB	00H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ DB 060H
	DD	imagerel $ip2state$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ DD 020a19H
	DD	07006720aH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
; Function compile flags: /Odtp
;	COMDAT ??_GWShop@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GWShop@mu2@@UEAAPEAXI@Z PROC				; mu2::WShop::`scalar deleting destructor', COMDAT
$LN5:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00012	e8 00 00 00 00	 call	 ??1WShop@mu2@@UEAA@XZ	; mu2::WShop::~WShop
  00017	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  0001b	83 e0 01	 and	 eax, 1
  0001e	85 c0		 test	 eax, eax
  00020	74 10		 je	 SHORT $LN2@scalar
  00022	ba 68 00 00 00	 mov	 edx, 104		; 00000068H
  00027	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0002c	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00031	90		 npad	 1
$LN2@scalar:
  00032	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00037	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0003b	c3		 ret	 0
??_GWShop@mu2@@UEAAPEAXI@Z ENDP				; mu2::WShop::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp
;	COMDAT ??0WShop@mu2@@AEAA@XZ
_TEXT	SEGMENT
this$ = 48
??0WShop@mu2@@AEAA@XZ PROC				; mu2::WShop::WShop, COMDAT

; 19   : {

$LN49:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 84   : 	ISingleton() {}	

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$ISingleton@VWShop@mu2@@@mu2@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp

; 19   : {

  00018	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7WShop@mu2@@6B@
  00024	48 89 08	 mov	 QWORD PTR [rax], rcx

; 10   : : m_statusHandler(nullptr)

  00027	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0002c	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0

; 11   : , m_billingHandler(nullptr)

  00034	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00039	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 12   : , m_jewelHandler(nullptr)

  00041	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00046	48 c7 40 18 00
	00 00 00	 mov	 QWORD PTR [rax+24], 0

; 13   : , m_invenHandler(nullptr)

  0004e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 c7 40 20 00
	00 00 00	 mov	 QWORD PTR [rax+32], 0

; 14   : , m_billing(nullptr)

  0005b	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00060	48 c7 40 28 00
	00 00 00	 mov	 QWORD PTR [rax+40], 0

; 15   : , m_jewel(nullptr)

  00068	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0006d	48 c7 40 30 00
	00 00 00	 mov	 QWORD PTR [rax+48], 0

; 16   : , m_wShopInventory(nullptr)

  00075	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0007a	48 c7 40 38 00
	00 00 00	 mov	 QWORD PTR [rax+56], 0

; 17   : , m_worldKey()

  00082	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00087	48 83 c0 40	 add	 rax, 64			; 00000040H
  0008b	48 8b c8	 mov	 rcx, rax
  0008e	e8 00 00 00 00	 call	 ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >

; 18   : , m_version(0)

  00093	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00098	48 c7 40 60 00
	00 00 00	 mov	 QWORD PTR [rax+96], 0

; 20   : }

  000a0	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  000a5	48 83 c4 28	 add	 rsp, 40			; 00000028H
  000a9	c3		 ret	 0
??0WShop@mu2@@AEAA@XZ ENDP				; mu2::WShop::WShop
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldServer.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp
;	COMDAT ?MakeOrderNo@WShop@mu2@@QEAA_KXZ
_TEXT	SEGMENT
$T1 = 32
temp$ = 36
$T2 = 40
this$ = 64
?MakeOrderNo@WShop@mu2@@QEAA_KXZ PROC			; mu2::WShop::MakeOrderNo, COMDAT

; 39   : {

$LN7:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 40   : 	static Short sequence = 0;
; 41   : 	UInt16 temp = ::InterlockedIncrement16(&sequence);

  00009	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?sequence@?1??MakeOrderNo@WShop@mu2@@QEAA_KXZ@4FA
  00010	66 b9 01 00	 mov	 cx, 1
  00014	66 f0 0f c1 08	 lock xadd WORD PTR [rax], cx
  00019	66 ff c1	 inc	 cx
  0001c	0f b7 c1	 movzx	 eax, cx
  0001f	66 89 44 24 24	 mov	 WORD PTR temp$[rsp], ax
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h

; 552  :             return _time64(_Time);

  00024	33 c9		 xor	 ecx, ecx
  00026	e8 00 00 00 00	 call	 _time64
  0002b	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldServer.h

; 90   : 	WorldId	GetWorldId() { return m_worldInfo.worldId; }	

  00030	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR ?Instance@Server@mu2@@2PEAV12@EA ; mu2::Server::Instance
  00037	0f b6 80 08 04
	00 00		 movzx	 eax, BYTE PTR [rax+1032]
  0003e	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp

; 42   : 	return static_cast<UInt64>(time(NULL)) << 32 | static_cast<UInt32>(SERVER.GetWorldId()) << 16 | temp;

  00042	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
  00047	48 c1 e0 20	 shl	 rax, 32			; 00000020H
  0004b	0f b6 4c 24 20	 movzx	 ecx, BYTE PTR $T1[rsp]
  00050	0f b6 c9	 movzx	 ecx, cl
  00053	c1 e1 10	 shl	 ecx, 16
  00056	8b c9		 mov	 ecx, ecx
  00058	48 0b c1	 or	 rax, rcx
  0005b	0f b7 4c 24 24	 movzx	 ecx, WORD PTR temp$[rsp]
  00060	48 0b c1	 or	 rax, rcx

; 43   : }

  00063	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00067	c3		 ret	 0
?MakeOrderNo@WShop@mu2@@QEAA_KXZ ENDP			; mu2::WShop::MakeOrderNo
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldServer.h
; File F:\Release_Branch\Server\Development\Framework\Net\Server\FcsLinkage.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldServer.h
; File F:\Release_Branch\Server\Development\Framework\Net\Server\FcsLinkage.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldServer.h
; File F:\Release_Branch\Server\Development\Framework\Net\Server\FcsLinkage.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldServer.h
; File F:\Release_Branch\Server\Development\Framework\Net\Server\FcsLinkage.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp
;	COMDAT ?Release@WShop@mu2@@QEAAXXZ
_TEXT	SEGMENT
$T1 = 32
$T2 = 40
$T3 = 48
$T4 = 56
tv75 = 64
tv135 = 72
tv163 = 80
tv191 = 88
$T5 = 96
$T6 = 104
tv90 = 112
$T7 = 120
$T8 = 128
tv150 = 136
$T9 = 144
$T10 = 152
tv178 = 160
$T11 = 168
$T12 = 176
tv206 = 184
this$ = 208
?Release@WShop@mu2@@QEAAXXZ PROC			; mu2::WShop::Release, COMDAT

; 100  : {

$LN34:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec c8 00
	00 00		 sub	 rsp, 200		; 000000c8H

; 101  : 	if (m_statusHandler != nullptr)

  0000c	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00019	0f 84 8d 00 00
	00		 je	 $LN2@Release
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldServer.h

; 120  : 	FcsLinkage&					GetFcsLinkage() { return m_fcs; }

  0001f	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR ?Instance@Server@mu2@@2PEAV12@EA ; mu2::Server::Instance
  00026	48 05 b8 05 00
	00		 add	 rax, 1464		; 000005b8H
  0002c	48 89 44 24 60	 mov	 QWORD PTR $T5[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Net\Server\FcsLinkage.h

; 18   : 	fcsa::IManager* GetManager() { return m_manager; }

  00031	48 8b 44 24 60	 mov	 rax, QWORD PTR $T5[rsp]
  00036	48 8b 40 20	 mov	 rax, QWORD PTR [rax+32]
  0003a	48 89 44 24 68	 mov	 QWORD PTR $T6[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp

; 103  : 		SERVER.GetFcsLinkage().GetManager()->resetAsyncReceiverStatus();

  0003f	48 8b 44 24 68	 mov	 rax, QWORD PTR $T6[rsp]
  00044	48 89 44 24 40	 mov	 QWORD PTR tv75[rsp], rax
  00049	48 8b 44 24 40	 mov	 rax, QWORD PTR tv75[rsp]
  0004e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00051	33 d2		 xor	 edx, edx
  00053	48 8b 4c 24 40	 mov	 rcx, QWORD PTR tv75[rsp]
  00058	ff 90 b8 00 00
	00		 call	 QWORD PTR [rax+184]
  0005e	90		 npad	 1

; 104  : 		delete m_statusHandler;

  0005f	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00067	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0006b	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
  00070	48 83 7c 24 20
	00		 cmp	 QWORD PTR $T1[rsp], 0
  00076	74 1b		 je	 SHORT $LN10@Release
  00078	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  0007d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00080	ba 01 00 00 00	 mov	 edx, 1
  00085	48 8b 4c 24 20	 mov	 rcx, QWORD PTR $T1[rsp]
  0008a	ff 10		 call	 QWORD PTR [rax]
  0008c	48 89 44 24 70	 mov	 QWORD PTR tv90[rsp], rax
  00091	eb 09		 jmp	 SHORT $LN11@Release
$LN10@Release:
  00093	48 c7 44 24 70
	00 00 00 00	 mov	 QWORD PTR tv90[rsp], 0
$LN11@Release:

; 105  : 		m_statusHandler = nullptr;

  0009c	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000a4	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0
$LN2@Release:

; 106  : 	}
; 107  : 
; 108  : 	if (m_billingHandler != nullptr)

  000ac	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000b4	48 83 78 10 00	 cmp	 QWORD PTR [rax+16], 0
  000b9	0f 84 99 00 00
	00		 je	 $LN3@Release
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldServer.h

; 120  : 	FcsLinkage&					GetFcsLinkage() { return m_fcs; }

  000bf	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR ?Instance@Server@mu2@@2PEAV12@EA ; mu2::Server::Instance
  000c6	48 05 b8 05 00
	00		 add	 rax, 1464		; 000005b8H
  000cc	48 89 44 24 78	 mov	 QWORD PTR $T7[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Net\Server\FcsLinkage.h

; 18   : 	fcsa::IManager* GetManager() { return m_manager; }

  000d1	48 8b 44 24 78	 mov	 rax, QWORD PTR $T7[rsp]
  000d6	48 8b 40 20	 mov	 rax, QWORD PTR [rax+32]
  000da	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T8[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp

; 110  : 		SERVER.GetFcsLinkage().GetManager()->resetAsyncReceiverWShopBilling();

  000e2	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR $T8[rsp]
  000ea	48 89 44 24 48	 mov	 QWORD PTR tv135[rsp], rax
  000ef	48 8b 44 24 48	 mov	 rax, QWORD PTR tv135[rsp]
  000f4	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000f7	33 d2		 xor	 edx, edx
  000f9	48 8b 4c 24 48	 mov	 rcx, QWORD PTR tv135[rsp]
  000fe	ff 90 98 00 00
	00		 call	 QWORD PTR [rax+152]
  00104	90		 npad	 1

; 111  : 		delete m_billingHandler;

  00105	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0010d	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00111	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax
  00116	48 83 7c 24 28
	00		 cmp	 QWORD PTR $T2[rsp], 0
  0011c	74 1e		 je	 SHORT $LN12@Release
  0011e	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
  00123	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00126	ba 01 00 00 00	 mov	 edx, 1
  0012b	48 8b 4c 24 28	 mov	 rcx, QWORD PTR $T2[rsp]
  00130	ff 10		 call	 QWORD PTR [rax]
  00132	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR tv150[rsp], rax
  0013a	eb 0c		 jmp	 SHORT $LN13@Release
$LN12@Release:
  0013c	48 c7 84 24 88
	00 00 00 00 00
	00 00		 mov	 QWORD PTR tv150[rsp], 0
$LN13@Release:

; 112  : 		m_billingHandler = nullptr;

  00148	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00150	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0
$LN3@Release:

; 113  : 	}
; 114  : 
; 115  : 	if (m_jewelHandler != nullptr)

  00158	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00160	48 83 78 18 00	 cmp	 QWORD PTR [rax+24], 0
  00165	0f 84 9f 00 00
	00		 je	 $LN4@Release
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldServer.h

; 120  : 	FcsLinkage&					GetFcsLinkage() { return m_fcs; }

  0016b	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR ?Instance@Server@mu2@@2PEAV12@EA ; mu2::Server::Instance
  00172	48 05 b8 05 00
	00		 add	 rax, 1464		; 000005b8H
  00178	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Net\Server\FcsLinkage.h

; 18   : 	fcsa::IManager* GetManager() { return m_manager; }

  00180	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR $T9[rsp]
  00188	48 8b 40 20	 mov	 rax, QWORD PTR [rax+32]
  0018c	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp

; 117  : 		SERVER.GetFcsLinkage().GetManager()->resetAsyncReceiverJewel();

  00194	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  0019c	48 89 44 24 50	 mov	 QWORD PTR tv163[rsp], rax
  001a1	48 8b 44 24 50	 mov	 rax, QWORD PTR tv163[rsp]
  001a6	48 8b 00	 mov	 rax, QWORD PTR [rax]
  001a9	33 d2		 xor	 edx, edx
  001ab	48 8b 4c 24 50	 mov	 rcx, QWORD PTR tv163[rsp]
  001b0	ff 90 a8 00 00
	00		 call	 QWORD PTR [rax+168]
  001b6	90		 npad	 1

; 118  : 		delete m_jewelHandler;

  001b7	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  001bf	48 8b 40 18	 mov	 rax, QWORD PTR [rax+24]
  001c3	48 89 44 24 30	 mov	 QWORD PTR $T3[rsp], rax
  001c8	48 83 7c 24 30
	00		 cmp	 QWORD PTR $T3[rsp], 0
  001ce	74 1e		 je	 SHORT $LN14@Release
  001d0	48 8b 44 24 30	 mov	 rax, QWORD PTR $T3[rsp]
  001d5	48 8b 00	 mov	 rax, QWORD PTR [rax]
  001d8	ba 01 00 00 00	 mov	 edx, 1
  001dd	48 8b 4c 24 30	 mov	 rcx, QWORD PTR $T3[rsp]
  001e2	ff 10		 call	 QWORD PTR [rax]
  001e4	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR tv178[rsp], rax
  001ec	eb 0c		 jmp	 SHORT $LN15@Release
$LN14@Release:
  001ee	48 c7 84 24 a0
	00 00 00 00 00
	00 00		 mov	 QWORD PTR tv178[rsp], 0
$LN15@Release:

; 119  : 		m_jewelHandler = nullptr;

  001fa	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00202	48 c7 40 18 00
	00 00 00	 mov	 QWORD PTR [rax+24], 0
$LN4@Release:

; 120  : 	}
; 121  : 
; 122  : 	if (m_invenHandler != nullptr)

  0020a	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00212	48 83 78 20 00	 cmp	 QWORD PTR [rax+32], 0
  00217	0f 84 9f 00 00
	00		 je	 $LN5@Release
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldServer.h

; 120  : 	FcsLinkage&					GetFcsLinkage() { return m_fcs; }

  0021d	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR ?Instance@Server@mu2@@2PEAV12@EA ; mu2::Server::Instance
  00224	48 05 b8 05 00
	00		 add	 rax, 1464		; 000005b8H
  0022a	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Net\Server\FcsLinkage.h

; 18   : 	fcsa::IManager* GetManager() { return m_manager; }

  00232	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  0023a	48 8b 40 20	 mov	 rax, QWORD PTR [rax+32]
  0023e	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp

; 124  : 		SERVER.GetFcsLinkage().GetManager()->resetAsyncReceiverWShopInventory();

  00246	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
  0024e	48 89 44 24 58	 mov	 QWORD PTR tv191[rsp], rax
  00253	48 8b 44 24 58	 mov	 rax, QWORD PTR tv191[rsp]
  00258	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0025b	33 d2		 xor	 edx, edx
  0025d	48 8b 4c 24 58	 mov	 rcx, QWORD PTR tv191[rsp]
  00262	ff 90 a0 00 00
	00		 call	 QWORD PTR [rax+160]
  00268	90		 npad	 1

; 125  : 		delete m_invenHandler;

  00269	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00271	48 8b 40 20	 mov	 rax, QWORD PTR [rax+32]
  00275	48 89 44 24 38	 mov	 QWORD PTR $T4[rsp], rax
  0027a	48 83 7c 24 38
	00		 cmp	 QWORD PTR $T4[rsp], 0
  00280	74 1e		 je	 SHORT $LN16@Release
  00282	48 8b 44 24 38	 mov	 rax, QWORD PTR $T4[rsp]
  00287	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0028a	ba 01 00 00 00	 mov	 edx, 1
  0028f	48 8b 4c 24 38	 mov	 rcx, QWORD PTR $T4[rsp]
  00294	ff 10		 call	 QWORD PTR [rax]
  00296	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR tv206[rsp], rax
  0029e	eb 0c		 jmp	 SHORT $LN17@Release
$LN16@Release:
  002a0	48 c7 84 24 b8
	00 00 00 00 00
	00 00		 mov	 QWORD PTR tv206[rsp], 0
$LN17@Release:

; 126  : 		m_invenHandler = nullptr;

  002ac	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  002b4	48 c7 40 20 00
	00 00 00	 mov	 QWORD PTR [rax+32], 0
$LN5@Release:

; 127  : 	}
; 128  : 
; 129  : 	if (m_wShopInventory != nullptr)

  002bc	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  002c4	48 83 78 38 00	 cmp	 QWORD PTR [rax+56], 0
  002c9	74 2e		 je	 SHORT $LN6@Release

; 130  : 	{
; 131  : 		m_wShopInventory->release();

  002cb	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  002d3	48 8b 40 38	 mov	 rax, QWORD PTR [rax+56]
  002d7	48 8b 8c 24 d0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  002df	48 8b 49 38	 mov	 rcx, QWORD PTR [rcx+56]
  002e3	48 8b 00	 mov	 rax, QWORD PTR [rax]
  002e6	ff 50 08	 call	 QWORD PTR [rax+8]

; 132  : 		m_wShopInventory = nullptr;

  002e9	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  002f1	48 c7 40 38 00
	00 00 00	 mov	 QWORD PTR [rax+56], 0
$LN6@Release:

; 133  : 	}
; 134  : 
; 135  : 	if (m_jewel != nullptr)

  002f9	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00301	48 83 78 30 00	 cmp	 QWORD PTR [rax+48], 0
  00306	74 2e		 je	 SHORT $LN7@Release

; 136  : 	{
; 137  : 		m_jewel->release();

  00308	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00310	48 8b 40 30	 mov	 rax, QWORD PTR [rax+48]
  00314	48 8b 8c 24 d0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0031c	48 8b 49 30	 mov	 rcx, QWORD PTR [rcx+48]
  00320	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00323	ff 50 08	 call	 QWORD PTR [rax+8]

; 138  : 		m_jewel = nullptr;

  00326	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0032e	48 c7 40 30 00
	00 00 00	 mov	 QWORD PTR [rax+48], 0
$LN7@Release:

; 139  : 	}
; 140  : 
; 141  : 	if (m_billing != nullptr)

  00336	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0033e	48 83 78 28 00	 cmp	 QWORD PTR [rax+40], 0
  00343	74 2e		 je	 SHORT $LN8@Release

; 142  : 	{
; 143  : 		m_billing->release();

  00345	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0034d	48 8b 40 28	 mov	 rax, QWORD PTR [rax+40]
  00351	48 8b 8c 24 d0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00359	48 8b 49 28	 mov	 rcx, QWORD PTR [rcx+40]
  0035d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00360	ff 50 08	 call	 QWORD PTR [rax+8]

; 144  : 		m_billing = nullptr;

  00363	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0036b	48 c7 40 28 00
	00 00 00	 mov	 QWORD PTR [rax+40], 0
$LN8@Release:

; 145  : 	}
; 146  : }

  00373	48 81 c4 c8 00
	00 00		 add	 rsp, 200		; 000000c8H
  0037a	c3		 ret	 0
?Release@WShop@mu2@@QEAAXXZ ENDP			; mu2::WShop::Release
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldServer.h
; File F:\Release_Branch\Server\Development\Framework\Net\Server\FcsLinkage.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldServer.h
; File F:\Release_Branch\Server\Development\Framework\Net\Server\FcsLinkage.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldServer.h
; File F:\Release_Branch\Server\Development\Framework\Net\Server\FcsLinkage.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldServer.h
; File F:\Release_Branch\Server\Development\Framework\Net\Server\FcsLinkage.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldServer.h
; File F:\Release_Branch\Server\Development\Framework\Net\Server\FcsLinkage.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldServer.h
; File F:\Release_Branch\Server\Development\Framework\Net\Server\FcsLinkage.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldServer.h
; File F:\Release_Branch\Server\Development\Framework\Net\Server\FcsLinkage.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp
;	COMDAT ?Initialize@WShop@mu2@@QEAA_NXZ
_TEXT	SEGMENT
tv76 = 32
tv92 = 40
tv140 = 48
$T1 = 56
tv164 = 64
tv173 = 72
$T2 = 80
tv196 = 88
tv205 = 96
$T3 = 104
tv228 = 112
tv237 = 120
$T4 = 128
tv260 = 136
tv269 = 144
$T5 = 152
$T6 = 160
$T7 = 168
$T8 = 176
$T9 = 184
$T10 = 192
$T11 = 200
$T12 = 208
$T13 = 216
$T14 = 224
$T15 = 232
$T16 = 240
$T17 = 248
$T18 = 256
$T19 = 264
$T20 = 272
$T21 = 280
$T22 = 288
this$ = 320
?Initialize@WShop@mu2@@QEAA_NXZ PROC			; mu2::WShop::Initialize, COMDAT

; 46   : {

$LN54:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec 38 01
	00 00		 sub	 rsp, 312		; 00000138H

; 47   : 	if (m_billing == nullptr)

  0000c	48 8b 84 24 40
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 83 78 28 00	 cmp	 QWORD PTR [rax+40], 0
  00019	75 52		 jne	 SHORT $LN2@Initialize
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldServer.h

; 120  : 	FcsLinkage&					GetFcsLinkage() { return m_fcs; }

  0001b	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR ?Instance@Server@mu2@@2PEAV12@EA ; mu2::Server::Instance
  00022	48 05 b8 05 00
	00		 add	 rax, 1464		; 000005b8H
  00028	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR $T5[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Net\Server\FcsLinkage.h

; 18   : 	fcsa::IManager* GetManager() { return m_manager; }

  00030	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR $T5[rsp]
  00038	48 8b 40 20	 mov	 rax, QWORD PTR [rax+32]
  0003c	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T6[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp

; 49   : 		m_billing = SERVER.GetFcsLinkage().GetManager()->createInstanceWShopBilling();

  00044	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR $T6[rsp]
  0004c	48 89 44 24 20	 mov	 QWORD PTR tv76[rsp], rax
  00051	48 8b 44 24 20	 mov	 rax, QWORD PTR tv76[rsp]
  00056	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00059	48 8b 4c 24 20	 mov	 rcx, QWORD PTR tv76[rsp]
  0005e	ff 50 48	 call	 QWORD PTR [rax+72]
  00061	48 8b 8c 24 40
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00069	48 89 41 28	 mov	 QWORD PTR [rcx+40], rax
$LN2@Initialize:

; 50   : 	}
; 51   : 
; 52   : 	if (m_jewel == nullptr)

  0006d	48 8b 84 24 40
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00075	48 83 78 30 00	 cmp	 QWORD PTR [rax+48], 0
  0007a	75 52		 jne	 SHORT $LN3@Initialize
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldServer.h

; 120  : 	FcsLinkage&					GetFcsLinkage() { return m_fcs; }

  0007c	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR ?Instance@Server@mu2@@2PEAV12@EA ; mu2::Server::Instance
  00083	48 05 b8 05 00
	00		 add	 rax, 1464		; 000005b8H
  00089	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T7[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Net\Server\FcsLinkage.h

; 18   : 	fcsa::IManager* GetManager() { return m_manager; }

  00091	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T7[rsp]
  00099	48 8b 40 20	 mov	 rax, QWORD PTR [rax+32]
  0009d	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T8[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp

; 54   : 		m_jewel = SERVER.GetFcsLinkage().GetManager()->createInstanceJewel();

  000a5	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR $T8[rsp]
  000ad	48 89 44 24 28	 mov	 QWORD PTR tv92[rsp], rax
  000b2	48 8b 44 24 28	 mov	 rax, QWORD PTR tv92[rsp]
  000b7	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000ba	48 8b 4c 24 28	 mov	 rcx, QWORD PTR tv92[rsp]
  000bf	ff 50 58	 call	 QWORD PTR [rax+88]
  000c2	48 8b 8c 24 40
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  000ca	48 89 41 30	 mov	 QWORD PTR [rcx+48], rax
$LN3@Initialize:

; 55   : 	}
; 56   : 
; 57   : 	if (m_wShopInventory == nullptr)

  000ce	48 8b 84 24 40
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000d6	48 83 78 38 00	 cmp	 QWORD PTR [rax+56], 0
  000db	75 52		 jne	 SHORT $LN4@Initialize
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldServer.h

; 120  : 	FcsLinkage&					GetFcsLinkage() { return m_fcs; }

  000dd	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR ?Instance@Server@mu2@@2PEAV12@EA ; mu2::Server::Instance
  000e4	48 05 b8 05 00
	00		 add	 rax, 1464		; 000005b8H
  000ea	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Net\Server\FcsLinkage.h

; 18   : 	fcsa::IManager* GetManager() { return m_manager; }

  000f2	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T9[rsp]
  000fa	48 8b 40 20	 mov	 rax, QWORD PTR [rax+32]
  000fe	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp

; 59   : 		m_wShopInventory = SERVER.GetFcsLinkage().GetManager()->createInstanceWShopInventory();

  00106	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  0010e	48 89 44 24 30	 mov	 QWORD PTR tv140[rsp], rax
  00113	48 8b 44 24 30	 mov	 rax, QWORD PTR tv140[rsp]
  00118	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0011b	48 8b 4c 24 30	 mov	 rcx, QWORD PTR tv140[rsp]
  00120	ff 50 50	 call	 QWORD PTR [rax+80]
  00123	48 8b 8c 24 40
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0012b	48 89 41 38	 mov	 QWORD PTR [rcx+56], rax
$LN4@Initialize:

; 60   : 	}
; 61   : 
; 62   : 	if (m_billing == nullptr || m_jewel == nullptr || m_wShopInventory == nullptr)

  0012f	48 8b 84 24 40
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00137	48 83 78 28 00	 cmp	 QWORD PTR [rax+40], 0
  0013c	74 1e		 je	 SHORT $LN6@Initialize
  0013e	48 8b 84 24 40
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00146	48 83 78 30 00	 cmp	 QWORD PTR [rax+48], 0
  0014b	74 0f		 je	 SHORT $LN6@Initialize
  0014d	48 8b 84 24 40
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00155	48 83 78 38 00	 cmp	 QWORD PTR [rax+56], 0
  0015a	75 07		 jne	 SHORT $LN5@Initialize
$LN6@Initialize:

; 63   : 	{
; 64   : 		return false;

  0015c	32 c0		 xor	 al, al
  0015e	e9 49 03 00 00	 jmp	 $LN1@Initialize
$LN5@Initialize:

; 65   : 	}
; 66   : 
; 67   : 	if (m_statusHandler == nullptr)

  00163	48 8b 84 24 40
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0016b	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00170	0f 85 a8 00 00
	00		 jne	 $LN7@Initialize

; 68   : 	{
; 69   : 		m_statusHandler = new WShopStatusEventHandler;

  00176	b9 10 00 00 00	 mov	 ecx, 16
  0017b	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00180	48 89 44 24 38	 mov	 QWORD PTR $T1[rsp], rax
  00185	48 83 7c 24 38
	00		 cmp	 QWORD PTR $T1[rsp], 0
  0018b	74 11		 je	 SHORT $LN14@Initialize
  0018d	48 8b 4c 24 38	 mov	 rcx, QWORD PTR $T1[rsp]
  00192	e8 00 00 00 00	 call	 ??0WShopStatusEventHandler@mu2@@QEAA@XZ ; mu2::WShopStatusEventHandler::WShopStatusEventHandler
  00197	48 89 44 24 40	 mov	 QWORD PTR tv164[rsp], rax
  0019c	eb 09		 jmp	 SHORT $LN15@Initialize
$LN14@Initialize:
  0019e	48 c7 44 24 40
	00 00 00 00	 mov	 QWORD PTR tv164[rsp], 0
$LN15@Initialize:
  001a7	48 8b 44 24 40	 mov	 rax, QWORD PTR tv164[rsp]
  001ac	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
  001b4	48 8b 84 24 40
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  001bc	48 8b 8c 24 c8
	00 00 00	 mov	 rcx, QWORD PTR $T11[rsp]
  001c4	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldServer.h

; 120  : 	FcsLinkage&					GetFcsLinkage() { return m_fcs; }

  001c8	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR ?Instance@Server@mu2@@2PEAV12@EA ; mu2::Server::Instance
  001cf	48 05 b8 05 00
	00		 add	 rax, 1464		; 000005b8H
  001d5	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Net\Server\FcsLinkage.h

; 18   : 	fcsa::IManager* GetManager() { return m_manager; }

  001dd	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
  001e5	48 8b 40 20	 mov	 rax, QWORD PTR [rax+32]
  001e9	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp

; 70   : 		SERVER.GetFcsLinkage().GetManager()->resetAsyncReceiverStatus(static_cast<fcsa::IAsyncReceiverStatus*>(m_statusHandler));

  001f1	48 8b 84 24 d8
	00 00 00	 mov	 rax, QWORD PTR $T13[rsp]
  001f9	48 89 44 24 48	 mov	 QWORD PTR tv173[rsp], rax
  001fe	48 8b 44 24 48	 mov	 rax, QWORD PTR tv173[rsp]
  00203	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00206	48 8b 8c 24 40
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0020e	48 8b 51 08	 mov	 rdx, QWORD PTR [rcx+8]
  00212	48 8b 4c 24 48	 mov	 rcx, QWORD PTR tv173[rsp]
  00217	ff 90 b8 00 00
	00		 call	 QWORD PTR [rax+184]
  0021d	90		 npad	 1
$LN7@Initialize:

; 71   : 	}
; 72   : 
; 73   : 	if (m_billingHandler == nullptr)

  0021e	48 8b 84 24 40
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00226	48 83 78 10 00	 cmp	 QWORD PTR [rax+16], 0
  0022b	0f 85 a8 00 00
	00		 jne	 $LN8@Initialize

; 74   : 	{
; 75   : 		m_billingHandler = new WShopBillingEventHandler;

  00231	b9 08 00 00 00	 mov	 ecx, 8
  00236	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  0023b	48 89 44 24 50	 mov	 QWORD PTR $T2[rsp], rax
  00240	48 83 7c 24 50
	00		 cmp	 QWORD PTR $T2[rsp], 0
  00246	74 11		 je	 SHORT $LN16@Initialize
  00248	48 8b 4c 24 50	 mov	 rcx, QWORD PTR $T2[rsp]
  0024d	e8 00 00 00 00	 call	 ??0WShopBillingEventHandler@mu2@@QEAA@XZ ; mu2::WShopBillingEventHandler::WShopBillingEventHandler
  00252	48 89 44 24 58	 mov	 QWORD PTR tv196[rsp], rax
  00257	eb 09		 jmp	 SHORT $LN17@Initialize
$LN16@Initialize:
  00259	48 c7 44 24 58
	00 00 00 00	 mov	 QWORD PTR tv196[rsp], 0
$LN17@Initialize:
  00262	48 8b 44 24 58	 mov	 rax, QWORD PTR tv196[rsp]
  00267	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR $T14[rsp], rax
  0026f	48 8b 84 24 40
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00277	48 8b 8c 24 e0
	00 00 00	 mov	 rcx, QWORD PTR $T14[rsp]
  0027f	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldServer.h

; 120  : 	FcsLinkage&					GetFcsLinkage() { return m_fcs; }

  00283	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR ?Instance@Server@mu2@@2PEAV12@EA ; mu2::Server::Instance
  0028a	48 05 b8 05 00
	00		 add	 rax, 1464		; 000005b8H
  00290	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR $T15[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Net\Server\FcsLinkage.h

; 18   : 	fcsa::IManager* GetManager() { return m_manager; }

  00298	48 8b 84 24 e8
	00 00 00	 mov	 rax, QWORD PTR $T15[rsp]
  002a0	48 8b 40 20	 mov	 rax, QWORD PTR [rax+32]
  002a4	48 89 84 24 f0
	00 00 00	 mov	 QWORD PTR $T16[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp

; 76   : 		SERVER.GetFcsLinkage().GetManager()->resetAsyncReceiverWShopBilling(static_cast<fcsa::IAsyncReceiverWShopBilling*>(m_billingHandler));

  002ac	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR $T16[rsp]
  002b4	48 89 44 24 60	 mov	 QWORD PTR tv205[rsp], rax
  002b9	48 8b 44 24 60	 mov	 rax, QWORD PTR tv205[rsp]
  002be	48 8b 00	 mov	 rax, QWORD PTR [rax]
  002c1	48 8b 8c 24 40
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  002c9	48 8b 51 10	 mov	 rdx, QWORD PTR [rcx+16]
  002cd	48 8b 4c 24 60	 mov	 rcx, QWORD PTR tv205[rsp]
  002d2	ff 90 98 00 00
	00		 call	 QWORD PTR [rax+152]
  002d8	90		 npad	 1
$LN8@Initialize:

; 77   : 	}
; 78   : 
; 79   : 	if (m_jewelHandler == nullptr)

  002d9	48 8b 84 24 40
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  002e1	48 83 78 18 00	 cmp	 QWORD PTR [rax+24], 0
  002e6	0f 85 a8 00 00
	00		 jne	 $LN9@Initialize

; 80   : 	{
; 81   : 		m_jewelHandler = new WShopJewelEventHandler;

  002ec	b9 08 00 00 00	 mov	 ecx, 8
  002f1	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  002f6	48 89 44 24 68	 mov	 QWORD PTR $T3[rsp], rax
  002fb	48 83 7c 24 68
	00		 cmp	 QWORD PTR $T3[rsp], 0
  00301	74 11		 je	 SHORT $LN18@Initialize
  00303	48 8b 4c 24 68	 mov	 rcx, QWORD PTR $T3[rsp]
  00308	e8 00 00 00 00	 call	 ??0WShopJewelEventHandler@mu2@@QEAA@XZ ; mu2::WShopJewelEventHandler::WShopJewelEventHandler
  0030d	48 89 44 24 70	 mov	 QWORD PTR tv228[rsp], rax
  00312	eb 09		 jmp	 SHORT $LN19@Initialize
$LN18@Initialize:
  00314	48 c7 44 24 70
	00 00 00 00	 mov	 QWORD PTR tv228[rsp], 0
$LN19@Initialize:
  0031d	48 8b 44 24 70	 mov	 rax, QWORD PTR tv228[rsp]
  00322	48 89 84 24 f8
	00 00 00	 mov	 QWORD PTR $T17[rsp], rax
  0032a	48 8b 84 24 40
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00332	48 8b 8c 24 f8
	00 00 00	 mov	 rcx, QWORD PTR $T17[rsp]
  0033a	48 89 48 18	 mov	 QWORD PTR [rax+24], rcx
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldServer.h

; 120  : 	FcsLinkage&					GetFcsLinkage() { return m_fcs; }

  0033e	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR ?Instance@Server@mu2@@2PEAV12@EA ; mu2::Server::Instance
  00345	48 05 b8 05 00
	00		 add	 rax, 1464		; 000005b8H
  0034b	48 89 84 24 00
	01 00 00	 mov	 QWORD PTR $T18[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Net\Server\FcsLinkage.h

; 18   : 	fcsa::IManager* GetManager() { return m_manager; }

  00353	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR $T18[rsp]
  0035b	48 8b 40 20	 mov	 rax, QWORD PTR [rax+32]
  0035f	48 89 84 24 08
	01 00 00	 mov	 QWORD PTR $T19[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp

; 82   : 		SERVER.GetFcsLinkage().GetManager()->resetAsyncReceiverJewel(static_cast<fcsa::IAsyncReceiverJewel*>(m_jewelHandler));

  00367	48 8b 84 24 08
	01 00 00	 mov	 rax, QWORD PTR $T19[rsp]
  0036f	48 89 44 24 78	 mov	 QWORD PTR tv237[rsp], rax
  00374	48 8b 44 24 78	 mov	 rax, QWORD PTR tv237[rsp]
  00379	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0037c	48 8b 8c 24 40
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00384	48 8b 51 18	 mov	 rdx, QWORD PTR [rcx+24]
  00388	48 8b 4c 24 78	 mov	 rcx, QWORD PTR tv237[rsp]
  0038d	ff 90 a8 00 00
	00		 call	 QWORD PTR [rax+168]
  00393	90		 npad	 1
$LN9@Initialize:

; 83   : 	}
; 84   : 
; 85   : 	if (m_invenHandler == nullptr)

  00394	48 8b 84 24 40
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0039c	48 83 78 20 00	 cmp	 QWORD PTR [rax+32], 0
  003a1	0f 85 c3 00 00
	00		 jne	 $LN10@Initialize

; 86   : 	{
; 87   : 		m_invenHandler = new WShopInventoryEventHandler;

  003a7	b9 08 00 00 00	 mov	 ecx, 8
  003ac	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  003b1	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T4[rsp], rax
  003b9	48 83 bc 24 80
	00 00 00 00	 cmp	 QWORD PTR $T4[rsp], 0
  003c2	74 17		 je	 SHORT $LN20@Initialize
  003c4	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR $T4[rsp]
  003cc	e8 00 00 00 00	 call	 ??0WShopInventoryEventHandler@mu2@@QEAA@XZ ; mu2::WShopInventoryEventHandler::WShopInventoryEventHandler
  003d1	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR tv260[rsp], rax
  003d9	eb 0c		 jmp	 SHORT $LN21@Initialize
$LN20@Initialize:
  003db	48 c7 84 24 88
	00 00 00 00 00
	00 00		 mov	 QWORD PTR tv260[rsp], 0
$LN21@Initialize:
  003e7	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR tv260[rsp]
  003ef	48 89 84 24 10
	01 00 00	 mov	 QWORD PTR $T20[rsp], rax
  003f7	48 8b 84 24 40
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  003ff	48 8b 8c 24 10
	01 00 00	 mov	 rcx, QWORD PTR $T20[rsp]
  00407	48 89 48 20	 mov	 QWORD PTR [rax+32], rcx
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldServer.h

; 120  : 	FcsLinkage&					GetFcsLinkage() { return m_fcs; }

  0040b	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR ?Instance@Server@mu2@@2PEAV12@EA ; mu2::Server::Instance
  00412	48 05 b8 05 00
	00		 add	 rax, 1464		; 000005b8H
  00418	48 89 84 24 18
	01 00 00	 mov	 QWORD PTR $T21[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Net\Server\FcsLinkage.h

; 18   : 	fcsa::IManager* GetManager() { return m_manager; }

  00420	48 8b 84 24 18
	01 00 00	 mov	 rax, QWORD PTR $T21[rsp]
  00428	48 8b 40 20	 mov	 rax, QWORD PTR [rax+32]
  0042c	48 89 84 24 20
	01 00 00	 mov	 QWORD PTR $T22[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp

; 88   : 		SERVER.GetFcsLinkage().GetManager()->resetAsyncReceiverWShopInventory(static_cast<fcsa::IAsyncReceiverWShopInventory*>(m_invenHandler));

  00434	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR $T22[rsp]
  0043c	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR tv269[rsp], rax
  00444	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR tv269[rsp]
  0044c	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0044f	48 8b 8c 24 40
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00457	48 8b 51 20	 mov	 rdx, QWORD PTR [rcx+32]
  0045b	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR tv269[rsp]
  00463	ff 90 a0 00 00
	00		 call	 QWORD PTR [rax+160]
  00469	90		 npad	 1
$LN10@Initialize:

; 89   : 	}
; 90   : 
; 91   : 	if (m_statusHandler == nullptr || m_billingHandler == nullptr || m_jewelHandler == nullptr || m_invenHandler == nullptr)

  0046a	48 8b 84 24 40
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00472	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00477	74 2d		 je	 SHORT $LN12@Initialize
  00479	48 8b 84 24 40
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00481	48 83 78 10 00	 cmp	 QWORD PTR [rax+16], 0
  00486	74 1e		 je	 SHORT $LN12@Initialize
  00488	48 8b 84 24 40
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00490	48 83 78 18 00	 cmp	 QWORD PTR [rax+24], 0
  00495	74 0f		 je	 SHORT $LN12@Initialize
  00497	48 8b 84 24 40
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0049f	48 83 78 20 00	 cmp	 QWORD PTR [rax+32], 0
  004a4	75 04		 jne	 SHORT $LN11@Initialize
$LN12@Initialize:

; 92   : 	{
; 93   : 		return false;

  004a6	32 c0		 xor	 al, al
  004a8	eb 02		 jmp	 SHORT $LN1@Initialize
$LN11@Initialize:

; 94   : 	}
; 95   : 
; 96   : 	return true;

  004aa	b0 01		 mov	 al, 1
$LN1@Initialize:

; 97   : }

  004ac	48 81 c4 38 01
	00 00		 add	 rsp, 312		; 00000138H
  004b3	c3		 ret	 0
?Initialize@WShop@mu2@@QEAA_NXZ ENDP			; mu2::WShop::Initialize
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
tv76 = 32
tv92 = 40
tv140 = 48
$T1 = 56
tv164 = 64
tv173 = 72
$T2 = 80
tv196 = 88
tv205 = 96
$T3 = 104
tv228 = 112
tv237 = 120
$T4 = 128
tv260 = 136
tv269 = 144
$T5 = 152
$T6 = 160
$T7 = 168
$T8 = 176
$T9 = 184
$T10 = 192
$T11 = 200
$T12 = 208
$T13 = 216
$T14 = 224
$T15 = 232
$T16 = 240
$T17 = 248
$T18 = 256
$T19 = 264
$T20 = 272
$T21 = 280
$T22 = 288
this$ = 320
?dtor$0@?0??Initialize@WShop@mu2@@QEAA_NXZ@4HA PROC	; `mu2::WShop::Initialize'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	ba 10 00 00 00	 mov	 edx, 16
  0000e	48 8b 4d 38	 mov	 rcx, QWORD PTR $T1[rbp]
  00012	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00017	48 83 c4 20	 add	 rsp, 32			; 00000020H
  0001b	5d		 pop	 rbp
  0001c	c3		 ret	 0
?dtor$0@?0??Initialize@WShop@mu2@@QEAA_NXZ@4HA ENDP	; `mu2::WShop::Initialize'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
tv76 = 32
tv92 = 40
tv140 = 48
$T1 = 56
tv164 = 64
tv173 = 72
$T2 = 80
tv196 = 88
tv205 = 96
$T3 = 104
tv228 = 112
tv237 = 120
$T4 = 128
tv260 = 136
tv269 = 144
$T5 = 152
$T6 = 160
$T7 = 168
$T8 = 176
$T9 = 184
$T10 = 192
$T11 = 200
$T12 = 208
$T13 = 216
$T14 = 224
$T15 = 232
$T16 = 240
$T17 = 248
$T18 = 256
$T19 = 264
$T20 = 272
$T21 = 280
$T22 = 288
this$ = 320
?dtor$1@?0??Initialize@WShop@mu2@@QEAA_NXZ@4HA PROC	; `mu2::WShop::Initialize'::`1'::dtor$1
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	ba 08 00 00 00	 mov	 edx, 8
  0000e	48 8b 4d 50	 mov	 rcx, QWORD PTR $T2[rbp]
  00012	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00017	48 83 c4 20	 add	 rsp, 32			; 00000020H
  0001b	5d		 pop	 rbp
  0001c	c3		 ret	 0
?dtor$1@?0??Initialize@WShop@mu2@@QEAA_NXZ@4HA ENDP	; `mu2::WShop::Initialize'::`1'::dtor$1
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
tv76 = 32
tv92 = 40
tv140 = 48
$T1 = 56
tv164 = 64
tv173 = 72
$T2 = 80
tv196 = 88
tv205 = 96
$T3 = 104
tv228 = 112
tv237 = 120
$T4 = 128
tv260 = 136
tv269 = 144
$T5 = 152
$T6 = 160
$T7 = 168
$T8 = 176
$T9 = 184
$T10 = 192
$T11 = 200
$T12 = 208
$T13 = 216
$T14 = 224
$T15 = 232
$T16 = 240
$T17 = 248
$T18 = 256
$T19 = 264
$T20 = 272
$T21 = 280
$T22 = 288
this$ = 320
?dtor$2@?0??Initialize@WShop@mu2@@QEAA_NXZ@4HA PROC	; `mu2::WShop::Initialize'::`1'::dtor$2
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	ba 08 00 00 00	 mov	 edx, 8
  0000e	48 8b 4d 68	 mov	 rcx, QWORD PTR $T3[rbp]
  00012	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00017	48 83 c4 20	 add	 rsp, 32			; 00000020H
  0001b	5d		 pop	 rbp
  0001c	c3		 ret	 0
?dtor$2@?0??Initialize@WShop@mu2@@QEAA_NXZ@4HA ENDP	; `mu2::WShop::Initialize'::`1'::dtor$2
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
tv76 = 32
tv92 = 40
tv140 = 48
$T1 = 56
tv164 = 64
tv173 = 72
$T2 = 80
tv196 = 88
tv205 = 96
$T3 = 104
tv228 = 112
tv237 = 120
$T4 = 128
tv260 = 136
tv269 = 144
$T5 = 152
$T6 = 160
$T7 = 168
$T8 = 176
$T9 = 184
$T10 = 192
$T11 = 200
$T12 = 208
$T13 = 216
$T14 = 224
$T15 = 232
$T16 = 240
$T17 = 248
$T18 = 256
$T19 = 264
$T20 = 272
$T21 = 280
$T22 = 288
this$ = 320
?dtor$3@?0??Initialize@WShop@mu2@@QEAA_NXZ@4HA PROC	; `mu2::WShop::Initialize'::`1'::dtor$3
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	ba 08 00 00 00	 mov	 edx, 8
  0000e	48 8b 8d 80 00
	00 00		 mov	 rcx, QWORD PTR $T4[rbp]
  00015	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  0001a	48 83 c4 20	 add	 rsp, 32			; 00000020H
  0001e	5d		 pop	 rbp
  0001f	c3		 ret	 0
?dtor$3@?0??Initialize@WShop@mu2@@QEAA_NXZ@4HA ENDP	; `mu2::WShop::Initialize'::`1'::dtor$3
text$x	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp
;	COMDAT ?UnInit@WShop@mu2@@UEAA_NXZ
_TEXT	SEGMENT
this$ = 8
?UnInit@WShop@mu2@@UEAA_NXZ PROC			; mu2::WShop::UnInit, COMDAT

; 34   : {	

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 35   : 	return true;

  00005	b0 01		 mov	 al, 1

; 36   : }

  00007	c3		 ret	 0
?UnInit@WShop@mu2@@UEAA_NXZ ENDP			; mu2::WShop::UnInit
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp
;	COMDAT ?Init@WShop@mu2@@UEAA_NPEAX@Z
_TEXT	SEGMENT
this$ = 8
param$ = 16
?Init@WShop@mu2@@UEAA_NPEAX@Z PROC			; mu2::WShop::Init, COMDAT

; 28   : {

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 29   : 	UNREFERENCED_PARAMETER(param);
; 30   : 	return true;

  0000a	b0 01		 mov	 al, 1

; 31   : }

  0000c	c3		 ret	 0
?Init@WShop@mu2@@UEAA_NPEAX@Z ENDP			; mu2::WShop::Init
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp
;	COMDAT ??1WShop@mu2@@UEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1WShop@mu2@@UEAA@XZ PROC				; mu2::WShop::~WShop, COMDAT

; 24   : {

$LN92:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7WShop@mu2@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx

; 25   : }

  00018	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 83 c0 40	 add	 rax, 64			; 00000040H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1383 :         _Tidy_deallocate();

  00021	48 8b c8	 mov	 rcx, rax
  00024	e8 00 00 00 00	 call	 ?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate
  00029	90		 npad	 1
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 80   : 	virtual~ISingleton() {}

  0002a	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0002f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$ISingleton@VWShop@mu2@@@mu2@@6B@
  00036	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp

; 25   : }

  00039	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0003d	c3		 ret	 0
??1WShop@mu2@@UEAA@XZ ENDP				; mu2::WShop::~WShop
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??_G?$ISingleton@VWShop@mu2@@@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_G?$ISingleton@VWShop@mu2@@@mu2@@UEAAPEAXI@Z PROC	; mu2::ISingleton<mu2::WShop>::`scalar deleting destructor', COMDAT
$LN10:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 80   : 	virtual~ISingleton() {}

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$ISingleton@VWShop@mu2@@@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx
  0001c	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00020	83 e0 01	 and	 eax, 1
  00023	85 c0		 test	 eax, eax
  00025	74 10		 je	 SHORT $LN2@scalar
  00027	ba 08 00 00 00	 mov	 edx, 8
  0002c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00031	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00036	90		 npad	 1
$LN2@scalar:
  00037	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003c	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00040	c3		 ret	 0
??_G?$ISingleton@VWShop@mu2@@@mu2@@UEAAPEAXI@Z ENDP	; mu2::ISingleton<mu2::WShop>::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
_TEXT	SEGMENT
$T1 = 32
$T2 = 34
_My_data$ = 40
tv94 = 48
_Bytes$ = 56
_Ptr$ = 64
$T3 = 72
$T4 = 80
_Capacity$ = 88
_Old_ptr$ = 96
_Al$5 = 104
this$ = 128
?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate, COMDAT

; 3080 :     _CONSTEXPR20 void _Tidy_deallocate() noexcept { // initialize buffer, deallocating any storage

$LN62:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 78	 sub	 rsp, 120		; 00000078H

; 3081 :         auto& _My_data = _Mypair._Myval2;

  00009	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00011	48 89 44 24 28	 mov	 QWORD PTR _My_data$[rsp], rax

; 453  :         return _Myres > _Small_string_capacity;

  00016	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  0001b	48 83 78 18 07	 cmp	 QWORD PTR [rax+24], 7
  00020	76 0a		 jbe	 SHORT $LN12@Tidy_deall
  00022	c7 44 24 30 01
	00 00 00	 mov	 DWORD PTR tv94[rsp], 1
  0002a	eb 08		 jmp	 SHORT $LN13@Tidy_deall
$LN12@Tidy_deall:
  0002c	c7 44 24 30 00
	00 00 00	 mov	 DWORD PTR tv94[rsp], 0
$LN13@Tidy_deall:
  00034	0f b6 44 24 30	 movzx	 eax, BYTE PTR tv94[rsp]
  00039	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al

; 3082 :         _My_data._Orphan_all();
; 3083 :         if (_My_data._Large_mode_engaged()) {

  0003d	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  00042	0f b6 c0	 movzx	 eax, al
  00045	85 c0		 test	 eax, eax
  00047	0f 84 80 00 00
	00		 je	 $LN2@Tidy_deall

; 3107 :         return _Mypair._Get_first();

  0004d	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00055	48 89 44 24 48	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3107 :         return _Mypair._Get_first();

  0005a	48 8b 44 24 48	 mov	 rax, QWORD PTR $T3[rsp]
  0005f	48 89 44 24 50	 mov	 QWORD PTR $T4[rsp], rax

; 3084 :             _ASAN_STRING_REMOVE(*this);
; 3085 :             auto& _Al = _Getal();

  00064	48 8b 44 24 50	 mov	 rax, QWORD PTR $T4[rsp]
  00069	48 89 44 24 68	 mov	 QWORD PTR _Al$5[rsp], rax

; 3086 :             _Deallocate_for_capacity(_Al, _My_data._Bx._Ptr, _My_data._Myres);

  0006e	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00073	48 8b 40 18	 mov	 rax, QWORD PTR [rax+24]
  00077	48 89 44 24 58	 mov	 QWORD PTR _Capacity$[rsp], rax
  0007c	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00081	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00084	48 89 44 24 60	 mov	 QWORD PTR _Old_ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  00089	48 8b 44 24 58	 mov	 rax, QWORD PTR _Capacity$[rsp]
  0008e	48 8d 44 00 02	 lea	 rax, QWORD PTR [rax+rax+2]
  00093	48 89 44 24 38	 mov	 QWORD PTR _Bytes$[rsp], rax
  00098	48 8b 44 24 60	 mov	 rax, QWORD PTR _Old_ptr$[rsp]
  0009d	48 89 44 24 40	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  000a2	48 81 7c 24 38
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  000ab	72 10		 jb	 SHORT $LN38@Tidy_deall

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  000ad	48 8d 54 24 38	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  000b2	48 8d 4c 24 40	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  000b7	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  000bc	90		 npad	 1
$LN38@Tidy_deall:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  000bd	48 8b 54 24 38	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  000c2	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  000c7	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  000cc	90		 npad	 1
$LN2@Tidy_deall:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3090 :         _My_data._Mysize = 0;

  000cd	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000d2	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 3091 :         _My_data._Myres  = _Small_string_capacity;

  000da	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000df	48 c7 40 18 07
	00 00 00	 mov	 QWORD PTR [rax+24], 7

; 3092 :         // the _Traits::assign is last so the codegen doesn't think the char write can alias this
; 3093 :         _Traits::assign(_My_data._Bx._Buf[0], _Elem());

  000e7	33 c0		 xor	 eax, eax
  000e9	66 89 44 24 22	 mov	 WORD PTR $T2[rsp], ax
  000ee	b8 02 00 00 00	 mov	 eax, 2
  000f3	48 6b c0 00	 imul	 rax, rax, 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 347  :         _Left = _Right;

  000f7	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _My_data$[rsp]
  000fc	0f b7 54 24 22	 movzx	 edx, WORD PTR $T2[rsp]
  00101	66 89 14 01	 mov	 WORD PTR [rcx+rax], dx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3094 :     }

  00105	48 83 c4 78	 add	 rsp, 120		; 00000078H
  00109	c3		 ret	 0
?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
_TEXT	SEGMENT
$T1 = 0
$T2 = 2
_My_data$ = 8
this$ = 32
?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_empty, COMDAT

; 855  :     _CONSTEXPR20 void _Construct_empty() {

$LN18:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi
  00006	48 83 ec 10	 sub	 rsp, 16

; 856  :         auto& _My_data = _Mypair._Myval2;

  0000a	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0000f	48 89 44 24 08	 mov	 QWORD PTR _My_data$[rsp], rax

; 857  :         _My_data._Alloc_proxy(_GET_PROXY_ALLOCATOR(_Alty, _Getal()));

  00014	48 8d 04 24	 lea	 rax, QWORD PTR $T1[rsp]
  00018	48 8b f8	 mov	 rdi, rax
  0001b	33 c0		 xor	 eax, eax
  0001d	b9 01 00 00 00	 mov	 ecx, 1
  00022	f3 aa		 rep stosb

; 858  : 
; 859  :         // initialize basic_string data members
; 860  :         _My_data._Mysize = 0;

  00024	48 8b 44 24 08	 mov	 rax, QWORD PTR _My_data$[rsp]
  00029	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 861  :         _My_data._Myres  = _Small_string_capacity;

  00031	48 8b 44 24 08	 mov	 rax, QWORD PTR _My_data$[rsp]
  00036	48 c7 40 18 07
	00 00 00	 mov	 QWORD PTR [rax+24], 7

; 862  :         _My_data._Activate_SSO_buffer();
; 863  : 
; 864  :         // the _Traits::assign is last so the codegen doesn't think the char write can alias this
; 865  :         _Traits::assign(_My_data._Bx._Buf[0], _Elem());

  0003e	33 c0		 xor	 eax, eax
  00040	66 89 44 24 02	 mov	 WORD PTR $T2[rsp], ax
  00045	b8 02 00 00 00	 mov	 eax, 2
  0004a	48 6b c0 00	 imul	 rax, rax, 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 347  :         _Left = _Right;

  0004e	48 8b 4c 24 08	 mov	 rcx, QWORD PTR _My_data$[rsp]
  00053	0f b7 54 24 02	 movzx	 edx, WORD PTR $T2[rsp]
  00058	66 89 14 01	 mov	 WORD PTR [rcx+rax], dx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 866  :     }

  0005c	48 83 c4 10	 add	 rsp, 16
  00060	5f		 pop	 rdi
  00061	c3		 ret	 0
?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_empty
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 32
this$ = 40
this$ = 48
this$ = 80
??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >, COMDAT

; 708  :     basic_string() noexcept(is_nothrow_default_constructible_v<_Alty>) : _Mypair(_Zero_then_variadic_args_t{}) {

$LN41:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi
  00006	48 83 ec 40	 sub	 rsp, 64			; 00000040H
  0000a	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0000f	48 89 44 24 30	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1531 :         : _Ty1(), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  00014	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00019	48 89 44 24 20	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 402  :     _CONSTEXPR20 _String_val() noexcept : _Bx() {}

  0001e	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 89 44 24 28	 mov	 QWORD PTR this$[rsp], rax

; 493  :         _CONSTEXPR20 _Bxty() noexcept : _Buf() {} // user-provided, for fancy pointers

  00028	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  0002d	48 8b 7c 24 28	 mov	 rdi, QWORD PTR this$[rsp]
  00032	33 c0		 xor	 eax, eax
  00034	b9 10 00 00 00	 mov	 ecx, 16
  00039	f3 aa		 rep stosb

; 517  :     size_type _Mysize = 0; // current length of string (size)

  0003b	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00040	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 518  :     size_type _Myres  = 0; // current storage reserved for string (capacity)

  00048	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0004d	48 c7 40 18 00
	00 00 00	 mov	 QWORD PTR [rax+24], 0

; 709  :         _Construct_empty();

  00055	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  0005a	e8 00 00 00 00	 call	 ?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_empty
  0005f	90		 npad	 1

; 710  :     }

  00060	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00065	48 83 c4 40	 add	 rsp, 64			; 00000040H
  00069	5f		 pop	 rdi
  0006a	c3		 ret	 0
??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z
_TEXT	SEGMENT
_Back_shift$ = 48
_Ptr_container$ = 56
_Ptr_user$ = 64
_Min_back_shift$ = 72
_Ptr$ = 96
_Bytes$ = 104
?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z PROC ; std::_Adjust_manually_vector_aligned, COMDAT

; 200  : inline void _Adjust_manually_vector_aligned(void*& _Ptr, size_t& _Bytes) {

$LN5:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 201  :     // adjust parameters from _Allocate_manually_vector_aligned to pass to operator delete
; 202  :     _Bytes += _Non_user_size;

  0000e	48 8b 44 24 68	 mov	 rax, QWORD PTR _Bytes$[rsp]
  00013	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00016	48 83 c0 27	 add	 rax, 39			; 00000027H
  0001a	48 8b 4c 24 68	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  0001f	48 89 01	 mov	 QWORD PTR [rcx], rax

; 203  : 
; 204  :     const uintptr_t* const _Ptr_user = static_cast<uintptr_t*>(_Ptr);

  00022	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00027	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0002a	48 89 44 24 40	 mov	 QWORD PTR _Ptr_user$[rsp], rax

; 205  :     const uintptr_t _Ptr_container   = _Ptr_user[-1];

  0002f	b8 08 00 00 00	 mov	 eax, 8
  00034	48 6b c0 ff	 imul	 rax, rax, -1
  00038	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr_user$[rsp]
  0003d	48 8b 04 01	 mov	 rax, QWORD PTR [rcx+rax]
  00041	48 89 44 24 38	 mov	 QWORD PTR _Ptr_container$[rsp], rax

; 206  : 
; 207  :     // If the following asserts, it likely means that we are performing
; 208  :     // an aligned delete on memory coming from an unaligned allocation.
; 209  :     _STL_ASSERT(_Ptr_user[-2] == _Big_allocation_sentinel, "invalid argument");
; 210  : 
; 211  :     // Extra paranoia on aligned allocation/deallocation; ensure _Ptr_container is
; 212  :     // in range [_Min_back_shift, _Non_user_size]
; 213  : #ifdef _DEBUG
; 214  :     constexpr uintptr_t _Min_back_shift = 2 * sizeof(void*);
; 215  : #else // ^^^ defined(_DEBUG) / !defined(_DEBUG) vvv
; 216  :     constexpr uintptr_t _Min_back_shift = sizeof(void*);

  00046	48 c7 44 24 48
	08 00 00 00	 mov	 QWORD PTR _Min_back_shift$[rsp], 8

; 217  : #endif // ^^^ !defined(_DEBUG) ^^^
; 218  :     const uintptr_t _Back_shift = reinterpret_cast<uintptr_t>(_Ptr) - _Ptr_container;

  0004f	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00054	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr_container$[rsp]
  00059	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0005c	48 2b c1	 sub	 rax, rcx
  0005f	48 89 44 24 30	 mov	 QWORD PTR _Back_shift$[rsp], rax

; 219  :     _STL_VERIFY(_Back_shift >= _Min_back_shift && _Back_shift <= _Non_user_size, "invalid argument");

  00064	48 83 7c 24 30
	08		 cmp	 QWORD PTR _Back_shift$[rsp], 8
  0006a	72 08		 jb	 SHORT $LN3@Adjust_man
  0006c	48 83 7c 24 30
	27		 cmp	 QWORD PTR _Back_shift$[rsp], 39 ; 00000027H
  00072	76 19		 jbe	 SHORT $LN2@Adjust_man
$LN3@Adjust_man:
  00074	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR [rsp+32], 0
  0007d	45 33 c9	 xor	 r9d, r9d
  00080	45 33 c0	 xor	 r8d, r8d
  00083	33 d2		 xor	 edx, edx
  00085	33 c9		 xor	 ecx, ecx
  00087	e8 00 00 00 00	 call	 _invoke_watson
  0008c	90		 npad	 1
$LN2@Adjust_man:

; 220  :     _Ptr = reinterpret_cast<void*>(_Ptr_container);

  0008d	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00092	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr_container$[rsp]
  00097	48 89 08	 mov	 QWORD PTR [rax], rcx
$LN4@Adjust_man:

; 221  : }

  0009a	48 83 c4 58	 add	 rsp, 88			; 00000058H
  0009e	c3		 ret	 0
?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ENDP ; std::_Adjust_manually_vector_aligned
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
