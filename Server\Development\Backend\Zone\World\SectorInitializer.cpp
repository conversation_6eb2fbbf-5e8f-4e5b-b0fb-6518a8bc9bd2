﻿#include "stdafx.h"

#include <Backend/Zone/ZoneServer.h>
#include <Backend/Zone/Action/ActionNpc.h>
#include <Backend/Zone/Action/ActionPlayer.h>
#include <Backend/Zone/Action/ActionPlayerInventory.h>
#include <Backend/Zone/EntityFactory/NpcEntityFactoryImpl.h>
#include <Backend/Zone/World/Helper/IRSpawnHelper.h>
#include <Backend/Zone/World/Helper/InitializeSpawner.h>
#include <Backend/Zone/World/Helper/VolumeSpawnHelper.h>
#include <Backend/Zone/World/Helper/InitializeSpawner.h>


// Sector에서 초기화 관련 부분만 처리

namespace mu2 
{

Bool Sector::initializeRealworld( const LevelConfiguration& config )
{	
	m_realworldSector = theRealworld.CreateSector( config.indexZone);

	if ( m_realworldSector == NULL )
	{
		MU2_ERROR_LOG( LogCategory::CONTENTS, "Zone::Create> Failed to create realworld for level %d", config.indexZone);

		return false;
	}

	return true;
}


void Sector::initialzeSpawn( const LevelConfiguration& config )
{
#ifndef BES_DIFFICULTY_LAYER_2017_07_13
	const Int32 selectLayer = SCRIPTS.GetDifficultyLayerRandomExtract(GetIndexZone(), GetInstanceDungeonInfo().dungeonDifficultyLevel);
	if (selectLayer == 0)
	{
		InstanceDungeonInfo dungeonInfo = GetInstanceDungeonInfo();
		InitializeSpawner::InitializeVolumeSpawn(this, dungeonInfo.layer);
		InitializeSpawner::InitializeIRSpawn(this, dungeonInfo.layer);
	}
	else
	{
		InitializeSpawner::InitializeVolumeSpawn(this, selectLayer);
		InitializeSpawner::InitializeIRSpawn(this, selectLayer);
	}
	
	InitializeSpawner::InitializeDynamicTagVolumeSpawn(this);
	InitializeSpawner::InitializeEvent( this );
	InitializeSpawner::InitializeMission( this ); 
#else 
	InitializeSpawner::InitializeVolumeSpawn(this);
	InitializeSpawner::InitializeIRSpawn(this);
	InitializeSpawner::InitializeDynamicTagVolumeSpawn(this);
	InitializeSpawner::InitializeEvent(this);
	InitializeSpawner::InitializeMission(this);
#endif 
}

// 섹터의 루아 스크립트 로딩, NPC 스크립트 로딩
ErrorJoin::Error Sector::loadSectorLuaScript( const std::wstring& file )
{	
	lua_State* L = m_sectorLuaScript->Setup( this, file );

	if ( L == nullptr )
	{
		MU2_WARN_LOG( LogCategory::CONTENTS, "[E] Failed to setup SectorLuaScript(%s)", file.c_str() );
		return ErrorJoin::SectorLuaScriptSetupFailed;
	}

	return initializeSectorController();
}

ErrorJoin::Error Sector::initializeSectorController()
{
	/*m_sectorController = static_cast<EntitySectorController*>( theEntityFactory.Create(EntityTypes::SECTOR_CONTROLLER, GetNextSeq(EntityTypes::SECTOR_CONTROLLER)	));
	VERIFY_RETURN(m_sectorController, ErrorJoin::AllocFailedEntity);*/

	ActionSectorController& asc = GetSectorController().GetControllerAction();
		
	if (asc.Setup(this) == false)
	{
		MU2_WARN_LOG(LogCategory::CONTENTS, "[E] Failed to setup ActionSectorController");

		//delete m_sectorController;
		//m_sectorController = nullptr;
		return ErrorJoin::ActionSectorControllerSetupFailed;
	}

	ErrorJoin::Error eError = asc.InstallProcessor();
	if (ErrorJoin::SUCCESS != eError)
	{
		MU2_WARN_LOG(LogCategory::CONTENTS, "[E] Failed to InstallProcessor");

		//delete m_sectorController;
		//m_sectorController = nullptr;
		return eError;
	}

	asc.Tran(EntityState::STATE_SECTOR_START);

	return ErrorJoin::SUCCESS;
}

Bool Sector::initializeBurningStaminaInfo( const VecBurningStaminaZoneServerInfo& list )
{
	MU2_ASSERT( m_burningStaminaInfos.empty() );
	
	for( auto& i : list )
	{
		auto inserted = m_burningStaminaInfos.emplace( i.continent, i );
		if( !inserted.second )
		{
			MU2_ASSERT(0);
			MU2_ERROR_LOG( LogCategory::DEBUG_BURNING_STAMINA, "Sector::initializeBurningStaminaInfo > insert falied. continent(%u)", i.continent );
			return false;
		}
	}

	return true;
}

} // namespace mu2
