﻿#include "stdafx.h"
#include <Backend/Zone/ZoneServer.h>
#include <Backend/Zone/Action/ActionParty.h>
#include <Backend/Zone/Action/ActionAchievement.h>
#include <Backend/Zone/Action/ActionPlayer.h>
#include <Backend/Zone/Action/ActionBuff.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerDamageMeter.h>
#include <Backend/Zone/Action/ActionQuest.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerRevive.h>
#include <Backend/Zone/System/NotifierGameContents.h>
namespace mu2
{

//---------------------------------------------------------------------------------------------------------------
// Constructor / Destructor
//---------------------------------------------------------------------------------------------------------------

ActionPlayerParty::ActionPlayerParty( EntityPlayer* owner )
	: Action4Player( owner, ID )
	, m_lastTick( 0 )

{ 
	CounterInc("ActionParty");
	/*Nothing!*/ 
}

//---------------------------------------------------------------------------------------------------------------

ActionPlayerParty::~ActionPlayerParty()
{ 
	CounterDec("ActionParty");
	/*Nothing!*/ 
}

//---------------------------------------------------------------------------------------------------------------
// Interface
//---------------------------------------------------------------------------------------------------------------

Bool ActionPlayerParty::IsParty( )
{
	return ( getAttributeParty().partyId > 0 ) ? true : false;
}


Double ActionPlayerParty::GetExpRateByFirstAttack(Bool isFirst)
{
	return isFirst ? 1.1f : 1.0f;
}


void ActionPlayerParty::SetPartyId( const PartyId partyId )
{
	if (getAttributeParty().partyId == partyId)
		return;

	getAttributeParty().partyId = partyId;

	SectorProcessor* processor = GetOwnerPlayer()->GetSectorAction().GetActionSectorController()->GetSectorProcess();
	if (processor)
	{
		if (getAttributeParty().partyId > 0)
		{
			processor->ProcessEvent(DungeonEventType::USER_PARTY_JOIN, GetOwnerPlayer(), nullptr, getAttributeParty().partyId, 0);
			theNotifierGameContents.OnParty_Joned(*GetOwnerPlayer());
		}
		else
		{
			processor->ProcessEvent(DungeonEventType::USER_PARTY_LEAVE, GetOwnerPlayer(), nullptr, 0, 0);
			theNotifierGameContents.OnParty_Leaved(*GetOwnerPlayer());
		}
	}
}

void ActionPlayerParty::SetLeader( const CharId leaderId )
{
	getAttributeParty().partyLeader = leaderId;
}

const CharId ActionPlayerParty::GetLeader()
{
	return getAttributeParty().partyLeader;
}

const PartyId ActionPlayerParty::GetPartyId()
{
	return getAttributeParty().partyId;
}

UInt32 ActionPlayerParty::GetMemberCount()
{
	return static_cast<UInt32>(getAttributeParty().members.size() );
}

const PartyId ActionPlayerParty::GetDestroyedPartyId()
{
	return getAttributeParty().destroyedPartyId;
}

AttributeParty& ActionPlayerParty::getAttributeParty()
{
	return GetOwnerPlayer()->GetPartyAttr();
}

Bool ActionPlayerParty::AddMember( const PartyMember& member )
{
	AttributeParty* attr = GetEntityAttribute( GetOwnerPlayer() );
	PartyMemberMap::iterator i = attr->members.find( member.charId);
	if ( i != attr->members.end() )
	{
		UpdateMember( member );
		return false;
	}

	attr->members.insert( PartyMemberMap::value_type( member.charId, member ) );

	return true;
}

Bool ActionPlayerParty::UpdateMember( const PartyMember& member )
{
	AttributeParty* attr = GetEntityAttribute( GetOwnerPlayer() );
	PartyMemberMap::iterator i = attr->members.find( member.charId );
	if ( i != attr->members.end() )
	{
		i->second = member;
		return true;
	}

	return false;
}

Bool ActionPlayerParty::DelMember( const CharId charId )
{
	AttributeParty* attr = GetEntityAttribute( GetOwnerPlayer() );
	attr->members.erase( charId );
	return true;
}

void ActionPlayerParty::ClearMember()
{
	AttributeParty* attr = GetEntityAttribute( GetOwnerPlayer() );
	SetPartyId(0);
	
	attr->members.clear();
}

Bool ActionPlayerParty::GetValidMember(EntityPlayer* entity, const Vector3& pos, std::vector< PartyMember >& validMember )
{	
	Sector* sector = entity->GetSector();
	VERIFY_RETURN(sector, false);

	AttributeParty* attr = GetEntityAttribute(GetOwnerPlayer());
	VERIFY_RETURN(attr, false);

	for(auto &iter : attr->members)
	{
		const PartyMember& member = iter.second;

		EntityPlayer* memberEntity = entity->GetSectorAction().GetPlayerInMySector( member.entityId );
		VALID_DO( memberEntity, continue );

		//if(sector->GetDungeonType() > DungeonType::FIELD_TYPE )	// 던전 일 경우 ( 던전타입이 0보다 크면 인스턴스 던전)
		if (sector->IsInstance())	// 던전 일 경우
		{
			// 던전일 경우 몬스터와의 거리가 필요 없다.
			validMember.push_back( member );
		}
		else					// 던전이 아닐 경우
		{
			const Float distance = Distance(memberEntity->GetCurrPos() , pos );
			if ( distance <= 5000.0f ) 
			{
				validMember.push_back( member );
			}
		}
	}
	
	VALID_RETURN( false == validMember.empty(), false );

	return true;
}

UInt32 ActionPlayerParty::GetValidMemberCount(EntityPlayer* entity, const Vector3& pos)
{
	std::vector< PartyMember > validMember;
	if( GetValidMember(entity, pos, validMember) )
	{
		return static_cast<UInt32>(validMember.size());
	}

	return 0;	
}

Byte ActionPlayerParty::GetValidMemberAverageLevel(EntityPlayer* entity, const Vector3& pos)
{
	std::vector< PartyMember > validMember;
	VALID_RETURN( GetValidMember( entity, pos, validMember ), 0 );
	VALID_RETURN( false == validMember.empty(), 0 );

	Byte level = 0;

	for(auto &itMember : validMember)
	{
		level += static_cast<Byte>(itMember.level);
	}
		
	return ( level / static_cast<Byte>(validMember.size()) );
}

Bool ActionPlayerParty::ChangeLeader( const CharId leaderId, const UShort permission )
{
	AttributeParty* attr = GetEntityAttribute( GetOwnerPlayer() );
	PartyMemberMap::iterator iBefore = attr->members.find( attr->partyLeader );
	if ( iBefore == attr->members.end() )
	{
		//! 기존 리더가 없는 경우 
		return false;
	}

	PartyMemberMap::iterator iAfter = attr->members.find( leaderId );
	if ( iAfter == attr->members.end() )
	{
		//! 새 리더가 없는 경우 
		return false;
	}

	iBefore->second.permission = EPartyPermission::MEMBER;
	iAfter->second.permission  = permission;
	attr->partyLeader = leaderId;

	return true;
}

Bool ActionPlayerParty::UpdateSyncMember( const UInt32 age )
{
	static const Tick PARTY_SYNC_TICK = 15000;
	
	if ( PARTY_SYNC_TICK <= age - m_lastTick )
	{
		m_lastTick = age;
		SyncMember();
	}

	return true;
}

void ActionPlayerParty::SyncMember()
{
	AttributeParty* attr = GetEntityAttribute( GetOwnerPlayer() );

	if(attr->partyId > 0)
	{
		ENtfCommPartySyncMember* ntf = NEW ENtfCommPartySyncMember;			
		ntf->groupId = attr->partyId;
		fillup( ntf->member );
		SERVER.SendToClient( GetOwnerPlayer(), EventPtr( ntf ) );
		
//		SyncBuff();
	}
}

void ActionPlayerParty::Fillup( PartyMember& member )
{
	fillup( member );
}

void ActionPlayerParty::SetTempId( const PartyId partyId )
{	
	getAttributeParty().tempPartyId = partyId;
}

const UInt32 ActionPlayerParty::GetTempId()
{
	return getAttributeParty().tempPartyId;
}

const UInt32 ActionPlayerParty::GetTempIdAndReset()
{
	AttributeParty& attrParty = getAttributeParty();
	auto partyId = attrParty.destroyedPartyId > 0 ? 0 : attrParty.tempPartyId ;
	attrParty.tempPartyId = 0;
	return partyId;
}

void ActionPlayerParty::fillup( PartyMember& member )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	member.entityId = player->GetId();
	member.charId = player->GetCharId();
	member.name = player->GetCharName();

	(member.charId == player->GetPartyAttr().partyLeader) ? member.permission = EPartyPermission::LEADER : member.permission = 0;
	
	member.level = player->GetLevel();
#ifdef __Reincarnation__jason_180628__
	member.soulLevelEx = player->GetSoulLevelEx();
#else
	member.soulLevel = player->GetRawSoulLevel();
#endif
	member.execId = player->GetCurrExecId();
	member.eRaceType = player->GetRaceType();
	member.eClassType = player->GetClassType();
	member.maxHp = player->GetMaxHp();
	member.maxMp = player->GetMaxMp();
	member.curHp = player->GetCurrentHp();
	member.curMp = player->GetCurrentMp();

	DateTime present = DateTime::GetPresentTime();
	member.platinumPlusService = (player->GetMembershipService().isPlatinumPlus) ? true : false;
	member.platinumServiceLevel = (player->GetMembershipService().isPlatinum) ? player->GetMembershipService().info.level : 0;
	member.logoutState = PartyMemberConnectState::Login;
	if (player->GetPlayerAttr().isRevive )
	{
		// 부활중
		member.state = PartyMemberState::REVIVE;
	}
	else
	{
		member.state = ( player->IsDead() ) ? PartyMemberState::DEAD : PartyMemberState::ALIVE;
	}

	
	if (player->IsDead())
	{	
		member.reviveCoolTime = player->GetReviveAction().GetReviveCoolTime();
		member.elapsedReviveCoolTime = player->GetReviveAction().GetElpasedReviveCoolTime();
		if (member.elapsedReviveCoolTime >= member.reviveCoolTime)
		{
			member.elapsedReviveCoolTime = member.reviveCoolTime;
		}
	}
	else
	{
		member.reviveCoolTime = 0;
		member.elapsedReviveCoolTime = 0;
	}
	
	member.execId	= player->GetCurrExecId();
	member.combatPower	= player->GetCombatPower();

	// 사용 가능한 즉시 부활 횟수 정보 세팅
	auto worldElem = SCRIPTS.GetWorldScript(member.execId.GetZoneIndex());
	if (nullptr != worldElem)
	{
		member.maxReviveCount = worldElem->revivalCnt;
		member.remainReviveCount = (0 != worldElem->revivalCnt) ? (worldElem->revivalCnt - player->GetReviveCount()) : 0;
	}
}

//inline AttributePosition& ActionPlayerParty::getPositionAttribute() 
//{
//	return GetOwnerPlayer()->GetPositionAttr();
//}

//inline AttributeUnit* ActionPlayerParty::getEntityAttribute() 
//{
//	return static_cast< AttributeUnit* >( GetOwnerPlayer()->GetAttribute( ATTRIBUTE_ENTITY ) );
//}

//inline AttributePlayer& ActionPlayerParty::getPlayerAttribute()
//{
//	return GetOwnerPlayer()->GetPlayerAttr();
//}

void ActionPlayerParty::GetPartyPlayersInMySector(__out VectorPlayer& vecPlayer )
{
	EntityPlayer* player = GetOwnerPlayer();
	vecPlayer.push_back( player );

	auto actSector = player->GetAction< ActionSector >();
	auto attrParty = player->GetAttribute< AttributeParty >();

	Sector* sector = actSector->GetSector();
	VERIFY_RETURN(sector, );

	for ( auto itr = attrParty->members.begin(); itr != attrParty->members.end(); ++itr )
	{
		const PartyMember& member = itr->second;

		EntityPlayer* memberPlayer = sector->FindPlayer(member.entityId);
		VERIFY_DO(memberPlayer && memberPlayer->IsValid(), continue);

		vecPlayer.push_back(memberPlayer);
	}
}

Bool ActionPlayerParty::IsAuthMatch()
{	
	return getAttributeParty().isAutoMatch;
}

void ActionPlayerParty::SyncMyPosition()
{
	EntityPlayer* entity = GetOwnerPlayer();
	AttributeParty* attrParty = GetEntityAttribute( entity );
	AttributePosition* attrPos = GetEntityAttribute( entity );

	if(attrParty->partyId > 0)
	{
		ENtfCommPartySyncLocation* ntf = NEW ENtfCommPartySyncLocation;		
		ntf->groupId = attrParty->partyId;
		ntf->myLocation.entityId = entity->GetId();
		ntf->myLocation.partyId = attrParty->partyId;
		ntf->myLocation.pos = attrPos->pos;		
		
		//SERVER.SendToClient( entity,  EventPtr( ntf ) );
		SendToMembersInMySector( EventPtr( ntf ) );
	}
}

void ActionPlayerParty::SyncBuff()
{
	auto actBuff = GetOwnerPlayer()->GetAction<ActionBuff>();
	VALID_RETURN( actBuff, );

	//actBuff->SendPartySyncBuff();
}

void ActionPlayerParty::SyncBuffInSector( EventPtr& ntf )
{
	SendToMembersInMySector( ntf );
	return;
}

void ActionPlayerParty::SendToMembersInMySector( EventPtr& ntf )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	Sector* sector = player->GetSector();
	VERIFY_RETURN(sector, );

	for (const auto& iter : player->GetPartyAttr().members)
	{
		EntityPlayer* member = sector->FindPlayerByCharId( iter.second.charId );
		if (nullptr != member)
		{
			member->SendToClient(ntf);
		}
	}
}

void ActionPlayerParty::SyncAllBuffFromOther()
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	//AttributeParty* attrParty = GetOwnerPlayer()->GetAttribute<AttributeParty>();
	//if ( nullptr == attrParty )
	//	return;

	//ActionSector* actionSector = GetOwnerPlayer()->GetAction<ActionSector>();
	//if ( nullptr == actionSector )
	//	return;

	Sector* sector = player->GetSector();
	VERIFY_RETURN(sector, );

	for ( const auto& iter : player->GetPartyAttr().members )
	{
		EntityPlayer* member = sector->FindPlayerByCharId( iter.second.charId );
		VALID_DO(member && member != player, continue);

		member->GetBuffAction().SendDbBuffInfo();
	}
}

void ActionPlayerParty::SyncCandidateEnterAdvDungeon()
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	if(player->GetPartyId() > 0)
	{	
		Sector* sector = player->GetSector();
		VERIFY_RETURN( sector, );

		// 후보자가 0인 정보는 삭제한다.
		sector->CheckAndEraseCandidateInfo(player, player->GetPartyId());	
	}
}

void ActionPlayerParty::SyncAdvEnterEventVolumeList(UInt32 volumeIndex, UInt32 updateType)
{
#if _DEL_
	EntityPlayer* entity = GetOwnerPlayer();
	AttributeParty* attrParty = GetEntityAttribute( entity );	
	if( nullptr == attrParty )
	{
		return;
	}

	EReqUpdateEnterEventVolumeIndex* req = NEW EReqUpdateEnterEventVolumeIndex;
	req->groupId = attrParty->partyId;
	req->eventVolumeIndex = volumeIndex;
	req->updateType = updateType;
	SERVER.SendToClient( entity,  EventPtr(req) );
#endif
}

void ActionPlayerParty::GetMemberLevel(CharLevel& minLevel, CharLevel& maxLevel)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	minLevel = maxLevel = owner->GetLevel();

	for( auto itr = owner->GetPartyAttr().members.begin(); itr != owner->GetPartyAttr().members.end(); ++ itr )
	{
		if( minLevel > itr->second.level ) 
		{
			minLevel = itr->second.level;
		}

		if( maxLevel < itr->second.level )
		{
			maxLevel = itr->second.level;
		}
	}
}

void ActionPlayerParty::LeaveParty(EntityPlayer* player, CharId leaveMemberCharId, UInt32 leaveType, UInt32 groupId)
{
	VERIFY_RETURN(player && player->IsValid(), );
	
	Sector* sector = player->GetSector();
	VERIFY_RETURN(sector, );

	EntityPlayer* leaveUser = sector->FindPlayerByCharId(leaveMemberCharId);
	if( nullptr != leaveUser )
	{
		if(sector->DeleteAdvCandidateInfo(leaveUser, groupId))
		{
			SyncCandidateEnterAdvDungeon();
		}
	}			

	//ViewPlayer* viewPlayer = GetEntityView( player );
	//MU2_ASSERT( viewPlayer );

	CharId ownerCharId = player->GetCharId();

	// 나? 로그를 남긴다.
	if ( leaveMemberCharId == ownerCharId )
	{	
		std::vector<CharId> memberIds;
		GetMemberId(memberIds);

		ClearMember();
		EReqLogDb2* log = NEW EReqLogDb2;
		EventPtr logEventPtr( log );
		EventSetter::FillLogForEntity( LogCode::E_PARTY_LEAVE, player, log->action );
		log->action.var32s.push_back( groupId );
		log->action.var32s.push_back(IsAuthMatch());
		for (CharId cid : memberIds)
			log->action.var32s.push_back(cid);

		SendDataCenter( logEventPtr );
		return;		
	}

	if ( leaveType == 1 )
	{
		DelMember( leaveMemberCharId );		
	}

	ENtfGamePartyLeaveMember* ntfToCli = NEW ENtfGamePartyLeaveMember;
	ntfToCli->partyId = groupId;
	ntfToCli->leaveMemberCharId = leaveMemberCharId;

	SERVER.SendToClient( player, EventPtr( ntfToCli ) );

	return;
}

void ActionPlayerParty::DestroyParty(EntityPlayer* player, UInt32 groupId, Bool hideMsg)
{
	VERIFY_RETURN( player && player->IsValid(), );

	ActionPlayerParty* actParty = static_cast<ActionPlayerParty*>(player->GetAction(ACTION_PARTY));
	VERIFY_RETURN(actParty, );

	if (actParty->GetDestroyedPartyId() <= 0 && actParty->IsParty() == false)
	{
		MU2_ASSERT(!"你当前没有加入队伍!!");
		return;
	}

	ActionSector* actSector = GetEntityAction(player);
	VERIFY_RETURN(actSector, );

	Sector* sector = actSector->GetSector();
	VERIFY_RETURN(sector, );

	sector->DeleteAdvCandidateInfoByDestroyParty(groupId);

	SetPartyId(0);

	std::vector<CharId> memberIds;
	GetMemberId(memberIds);
	ClearMember();

	ENtfGamePartyDestroy* ntfToCli = NEW ENtfGamePartyDestroy;
	ntfToCli->partyId = groupId;
	ntfToCli->hideMsg = hideMsg;
	SERVER.SendToClient(player, EventPtr(ntfToCli));

	EReqLogDb2* log = NEW EReqLogDb2;
	EventPtr logEventPtr( log );
	EventSetter::FillLogForEntity(LogCode::E_PARTY_DESTROY, player, log->action);
	log->action.var32s.push_back(groupId);
	log->action.var32s.push_back(IsAuthMatch());
	if (memberIds.size() > 0)
	{
		for (CharId id : memberIds)
			log->action.var32s.push_back(id);
	}
	SendDataCenter( logEventPtr );

	return;
}

void ActionPlayerParty::PartyCheckIn()
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	EReqPartyCheckIn* req = NEW EReqPartyCheckIn;
	EventPtr reqPtr(req);

	AttributeParty& attrParty = getAttributeParty();	

	attrParty.checkInParty = false;

	PartyMember member;
	member.channelId = owner->GetChannelId();
	member.eClassType = owner->GetClassType();
	member.curHp = owner->GetCurrentHp();
	member.curMp = owner->GetCurrentMp();
	member.entityId = owner->GetId();
	member.charId = owner->GetCharId();
	member.level = owner->GetLevel();
	member.logoutState = PartyMemberConnectState::Login;
	member.execId = owner->GetCurrExecId();
	member.maxHp = owner->GetMaxHp();
	member.maxMp = owner->GetMaxMp();
	member.eRaceType = owner->GetRaceType();
	member.name = owner->GetCharName();
	member.state = PartyMemberState::ALIVE;

	DateTime present = DateTime::GetPresentTime();
	member.platinumPlusService = owner->GetMembershipService().isPlatinumPlus;
	member.platinumServiceLevel = (owner->GetMembershipService().isPlatinum) ? owner->GetMembershipService().info.level : 0;

	// 사용 가능한 즉시 부활 횟수 정보 세팅
	auto worldElem = SCRIPTS.GetWorldScript(member.execId.GetZoneIndex());
	if (nullptr != worldElem)
	{
		member.maxReviveCount = worldElem->revivalCnt;
		member.remainReviveCount = (0 != worldElem->revivalCnt) ? (worldElem->revivalCnt - owner->GetReviveCount()) : 0;
	}

	req->charId = owner->GetCharId();
	req->member = std::move(member);

	SERVER.SendToClient(owner, reqPtr );
}

void ActionPlayerParty::SendEzwReqSyncLoadCompleted()
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	EzwReqSyncLoadCompleted* req = new EzwReqSyncLoadCompleted;
	req->executionZoneId = player->GetCurrExecId();
	player->SendToWorld(EventPtr(req));
}

void ActionPlayerParty::OnCheckInParty()
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	AttributeParty& attrParty = getAttributeParty();
	VALID_RETURN( false == attrParty.checkInParty, );

	attrParty.checkInParty = true;

	if ( IsParty() )
	{
		auto actSector = player->GetAction< ActionSector >();
		Sector* sector = actSector->GetSector();

		if ( sector->CheckEnterVolumeByJoinExecution(player, GetPartyId() ) )
		{
			SyncCandidateEnterAdvDungeon();
		}
	}

	// 퀘스트 미션 시작
	auto actQuest = player->GetAction<ActionPlayerQuest>();
	actQuest->StartProgressQuestMissions();
}

void ActionPlayerParty::SendSystemMessage(SystemMessage &msg)
{
	auto ntf = NEW ENtfPartySystemMsg;
	ntf->partyId = GetPartyId();
	ntf->msg = msg.GetString();

	SERVER.SendToOnlyWorldServer(EventPtr(ntf));
}

} // mu2
