; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	??0exception@std@@QEAA@AEBV01@@Z		; std::exception::exception
PUBLIC	?what@exception@std@@UEBAPEBDXZ			; std::exception::what
PUBLIC	??_Gexception@std@@UEAAPEAXI@Z			; std::exception::`scalar deleting destructor'
PUBLIC	??0bad_alloc@std@@QEAA@AEBV01@@Z		; std::bad_alloc::bad_alloc
PUBLIC	??_Gbad_alloc@std@@UEAAPEAXI@Z			; std::bad_alloc::`scalar deleting destructor'
PUBL<PERSON>	??0bad_array_new_length@std@@QEAA@XZ		; std::bad_array_new_length::bad_array_new_length
PUBLIC	??1bad_array_new_length@std@@UEAA@XZ		; std::bad_array_new_length::~bad_array_new_length
PUBLIC	??0bad_array_new_length@std@@QEAA@AEBV01@@Z	; std::bad_array_new_length::bad_array_new_length
PUBLIC	??_Gbad_array_new_length@std@@UEAAPEAXI@Z	; std::bad_array_new_length::`scalar deleting destructor'
PUBLIC	?_Throw_bad_array_new_length@std@@YAXXZ		; std::_Throw_bad_array_new_length
PUBLIC	?_Xlen_string@std@@YAXXZ			; std::_Xlen_string
PUBLIC	?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z	; std::allocator<wchar_t>::allocate
PUBLIC	??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
PUBLIC	?c_str@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBAPEB_WXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::c_str
PUBLIC	?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
PUBLIC	??1?$_Compressed_pair@V?$allocator@_W@std@@V?$_String_val@U?$_Simple_types@_W@std@@@2@$00@std@@QEAA@XZ ; std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>::~_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>
PUBLIC	?_Decref@_Ref_count_base@std@@QEAAXXZ		; std::_Ref_count_base::_Decref
PUBLIC	?getValue@stWOPSCommand@mu2@@AEBAAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@H@Z ; mu2::stWOPSCommand::getValue
PUBLIC	??1WOPSCommand@mu2@@UEAA@XZ			; mu2::WOPSCommand::~WOPSCommand
PUBLIC	?OnBegin@WOPSCommand@mu2@@UEBAXXZ		; mu2::WOPSCommand::OnBegin
PUBLIC	?OnEnd@WOPSCommand@mu2@@UEBAXXZ			; mu2::WOPSCommand::OnEnd
PUBLIC	?IsOverlapable@WOPSCommand@mu2@@UEBA_NXZ	; mu2::WOPSCommand::IsOverlapable
PUBLIC	?ToString@WOPSCommand@mu2@@UEBA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ ; mu2::WOPSCommand::ToString
PUBLIC	??$_Construct@$00PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<1,wchar_t const *>
PUBLIC	??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
PUBLIC	??0WOPSCommandEditKnightageTrophyPoint@mu2@@QEAA@XZ ; mu2::WOPSCommandEditKnightageTrophyPoint::WOPSCommandEditKnightageTrophyPoint
PUBLIC	??1WOPSCommandEditKnightageTrophyPoint@mu2@@UEAA@XZ ; mu2::WOPSCommandEditKnightageTrophyPoint::~WOPSCommandEditKnightageTrophyPoint
PUBLIC	?OnEnter@WOPSCommandEditKnightageTrophyPoint@mu2@@UEBAXPEAVEntityPlayer@2@@Z ; mu2::WOPSCommandEditKnightageTrophyPoint::OnEnter
PUBLIC	?parse@WOPSCommandEditKnightageTrophyPoint@mu2@@MEAA_NAEBUstWOPSCommand@2@@Z ; mu2::WOPSCommandEditKnightageTrophyPoint::parse
PUBLIC	?executeCommand@WOPSCommandEditKnightageTrophyPoint@mu2@@MEAAXXZ ; mu2::WOPSCommandEditKnightageTrophyPoint::executeCommand
PUBLIC	??1?$unique_ptr@UWOPSEditKnightageTrophyPointImpl@mu2@@U?$default_delete@UWOPSEditKnightageTrophyPointImpl@mu2@@@std@@@std@@QEAA@XZ ; std::unique_ptr<mu2::WOPSEditKnightageTrophyPointImpl,std::default_delete<mu2::WOPSEditKnightageTrophyPointImpl> >::~unique_ptr<mu2::WOPSEditKnightageTrophyPointImpl,std::default_delete<mu2::WOPSEditKnightageTrophyPointImpl> >
PUBLIC	??_GWOPSCommandEditKnightageTrophyPoint@mu2@@UEAAPEAXI@Z ; mu2::WOPSCommandEditKnightageTrophyPoint::`scalar deleting destructor'
PUBLIC	??1?$shared_ptr@VKnightage@mu2@@@std@@QEAA@XZ	; std::shared_ptr<mu2::Knightage>::~shared_ptr<mu2::Knightage>
PUBLIC	??_7exception@std@@6B@				; std::exception::`vftable'
PUBLIC	??_C@_0BC@EOODALEL@Unknown?5exception@		; `string'
PUBLIC	??_7bad_alloc@std@@6B@				; std::bad_alloc::`vftable'
PUBLIC	??_7bad_array_new_length@std@@6B@		; std::bad_array_new_length::`vftable'
PUBLIC	??_C@_0BF@KINCDENJ@bad?5array?5new?5length@	; `string'
PUBLIC	??_R0?AVexception@std@@@8			; std::exception `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
PUBLIC	_TI3?AVbad_array_new_length@std@@
PUBLIC	_CTA3?AVbad_array_new_length@std@@
PUBLIC	??_R0?AVbad_array_new_length@std@@@8		; std::bad_array_new_length `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
PUBLIC	??_R0?AVbad_alloc@std@@@8			; std::bad_alloc `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
PUBLIC	??_C@_0BA@JFNIOLAK@string?5too?5long@		; `string'
PUBLIC	??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@ ; `string'
PUBLIC	??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@		; `string'
PUBLIC	??_C@_11LOCGONAA@@				; `string'
PUBLIC	?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A ; mu2::ISingleton<mu2::GlobalLoadScript>::inst
PUBLIC	??_C@_0EB@JGAFMFJE@F?3?2Release_Branch?2Server?2Develo@ ; `string'
PUBLIC	??_C@_0BK@FMPHEHPK@arryIndex?5?$DM?5values?4size?$CI?$CJ@ ; `string'
PUBLIC	??_C@_0BN@FHEEOEA@mu2?3?3stWOPSCommand?3?3getValue@ ; `string'
PUBLIC	??_R4exception@std@@6B@				; std::exception::`RTTI Complete Object Locator'
PUBLIC	??_R3exception@std@@8				; std::exception::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2exception@std@@8				; std::exception::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@exception@std@@8			; std::exception::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4bad_array_new_length@std@@6B@		; std::bad_array_new_length::`RTTI Complete Object Locator'
PUBLIC	??_R3bad_array_new_length@std@@8		; std::bad_array_new_length::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2bad_array_new_length@std@@8		; std::bad_array_new_length::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@bad_array_new_length@std@@8	; std::bad_array_new_length::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@bad_alloc@std@@8			; std::bad_alloc::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R3bad_alloc@std@@8				; std::bad_alloc::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2bad_alloc@std@@8				; std::bad_alloc::`RTTI Base Class Array'
PUBLIC	??_R4bad_alloc@std@@6B@				; std::bad_alloc::`RTTI Complete Object Locator'
PUBLIC	?inst@?$ISingleton@VKnightageManager@mu2@@@mu2@@1VKnightageManager@2@A ; mu2::ISingleton<mu2::KnightageManager>::inst
PUBLIC	??_7WOPSCommandEditKnightageTrophyPoint@mu2@@6B@ ; mu2::WOPSCommandEditKnightageTrophyPoint::`vftable'
PUBLIC	??_C@_0CL@BCIDDGBP@?$FLWOPS?$FN?5Set?5Knightage?5Trophy?5?3?5K@ ; `string'
PUBLIC	??_C@_0GG@MDKIIJBC@F?3?2Release_Branch?2Server?2Develo@ ; `string'
PUBLIC	??_C@_0DJ@DCKFMAGJ@mu2?3?3WOPSCommandEditKnightageTr@ ; `string'
PUBLIC	??_C@_0BN@ICLBKJID@2?5?$DM?$DN?5command?4GetValueCount?$CI?$CJ@ ; `string'
PUBLIC	??_C@_0DA@JAKJJCN@mu2?3?3WOPSCommandEditKnightageTr@ ; `string'
PUBLIC	??_R4WOPSCommandEditKnightageTrophyPoint@mu2@@6B@ ; mu2::WOPSCommandEditKnightageTrophyPoint::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVWOPSCommandEditKnightageTrophyPoint@mu2@@@8 ; mu2::WOPSCommandEditKnightageTrophyPoint `RTTI Type Descriptor'
PUBLIC	??_R3WOPSCommandEditKnightageTrophyPoint@mu2@@8	; mu2::WOPSCommandEditKnightageTrophyPoint::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2WOPSCommandEditKnightageTrophyPoint@mu2@@8	; mu2::WOPSCommandEditKnightageTrophyPoint::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@WOPSCommandEditKnightageTrophyPoint@mu2@@8 ; mu2::WOPSCommandEditKnightageTrophyPoint::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@WOPSCommand@mu2@@8		; mu2::WOPSCommand::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R0?AVWOPSCommand@mu2@@@8			; mu2::WOPSCommand `RTTI Type Descriptor'
PUBLIC	??_R3WOPSCommand@mu2@@8				; mu2::WOPSCommand::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2WOPSCommand@mu2@@8				; mu2::WOPSCommand::`RTTI Base Class Array'
EXTRN	??2@YAPEAX_K@Z:PROC				; operator new
EXTRN	??3@YAXPEAX_K@Z:PROC				; operator delete
EXTRN	atexit:PROC
EXTRN	_invoke_watson:PROC
EXTRN	memcpy:PROC
EXTRN	wcslen:PROC
EXTRN	__imp_GetStdHandle:PROC
EXTRN	__imp_SetConsoleTextAttribute:PROC
EXTRN	_wtoi64:PROC
EXTRN	__std_exception_copy:PROC
EXTRN	__std_exception_destroy:PROC
EXTRN	??_Eexception@std@@UEAAPEAXI@Z:PROC		; std::exception::`vector deleting destructor'
EXTRN	??_Ebad_alloc@std@@UEAAPEAXI@Z:PROC		; std::bad_alloc::`vector deleting destructor'
EXTRN	??_Ebad_array_new_length@std@@UEAAPEAXI@Z:PROC	; std::bad_array_new_length::`vector deleting destructor'
EXTRN	?_Xlength_error@std@@YAXPEBD@Z:PROC		; std::_Xlength_error
EXTRN	?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ:PROC	; mu2::Logger::Logging
EXTRN	??1GlobalLoadScript@mu2@@UEAA@XZ:PROC		; mu2::GlobalLoadScript::~GlobalLoadScript
EXTRN	?GetKnightageTrophyInfo@GlobalLoadScript@mu2@@QEBAAEBUKnightTropyInfo@2@XZ:PROC ; mu2::GlobalLoadScript::GetKnightageTrophyInfo
EXTRN	??0GlobalLoadScript@mu2@@AEAA@XZ:PROC		; mu2::GlobalLoadScript::GlobalLoadScript
EXTRN	??0WOPSCommand@mu2@@IEAA@W4Enum@WOPSCommandCode@1@@Z:PROC ; mu2::WOPSCommand::WOPSCommand
EXTRN	?isAvailablePeriod@WOPSCommand@mu2@@MEBA_NXZ:PROC ; mu2::WOPSCommand::isAvailablePeriod
EXTRN	?releaseCommand@WOPSCommand@mu2@@MEAAX_N@Z:PROC	; mu2::WOPSCommand::releaseCommand
EXTRN	?checkRelease@WOPSCommand@mu2@@MEAA_NXZ:PROC	; mu2::WOPSCommand::checkRelease
EXTRN	?isApplied@WOPSCommand@mu2@@QEBA_NXZ:PROC	; mu2::WOPSCommand::isApplied
EXTRN	?updateStateAndSaveDb@WOPSCommand@mu2@@IEAAXW4Enum@WOPSCommandState@2@@Z:PROC ; mu2::WOPSCommand::updateStateAndSaveDb
EXTRN	?AddTrophyPoint@Knightage@mu2@@QEAA_NHAEAH0_N@Z:PROC ; mu2::Knightage::AddTrophyPoint
EXTRN	?SubTrophyPoint@Knightage@mu2@@QEAA_NHAEAH_N@Z:PROC ; mu2::Knightage::SubTrophyPoint
EXTRN	??1KnightageManager@mu2@@UEAA@XZ:PROC		; mu2::KnightageManager::~KnightageManager
EXTRN	?GetKnightage@KnightageManager@mu2@@QEAA?AV?$shared_ptr@VKnightage@mu2@@@std@@I@Z:PROC ; mu2::KnightageManager::GetKnightage
EXTRN	??0KnightageManager@mu2@@AEAA@XZ:PROC		; mu2::KnightageManager::KnightageManager
EXTRN	??_EWOPSCommandEditKnightageTrophyPoint@mu2@@UEAAPEAXI@Z:PROC ; mu2::WOPSCommandEditKnightageTrophyPoint::`vector deleting destructor'
EXTRN	_CxxThrowException:PROC
EXTRN	__CxxFrameHandler4:PROC
EXTRN	??_7type_info@@6B@:BYTE				; type_info::`vftable'
EXTRN	?InvaldValue@stWOPSCommand@mu2@@0V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@B:BYTE ; mu2::stWOPSCommand::InvaldValue
;	COMDAT ?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A
_BSS	SEGMENT
?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A DB 0d0H DUP (?) ; mu2::ISingleton<mu2::GlobalLoadScript>::inst
_BSS	ENDS
;	COMDAT ?inst@?$ISingleton@VKnightageManager@mu2@@@mu2@@1VKnightageManager@2@A
_BSS	SEGMENT
?inst@?$ISingleton@VKnightageManager@mu2@@@mu2@@1VKnightageManager@2@A DB 0d8H DUP (?) ; mu2::ISingleton<mu2::KnightageManager>::inst
_BSS	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0exception@std@@QEAA@AEBV01@@Z DD imagerel $LN4
	DD	imagerel $LN4+89
	DD	imagerel $unwind$??0exception@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?what@exception@std@@UEBAPEBDXZ DD imagerel $LN5
	DD	imagerel $LN5+56
	DD	imagerel $unwind$?what@exception@std@@UEBAPEBDXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gexception@std@@UEAAPEAXI@Z DD imagerel $LN10
	DD	imagerel $LN10+83
	DD	imagerel $unwind$??_Gexception@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z DD imagerel $LN9
	DD	imagerel $LN9+104
	DD	imagerel $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+83
	DD	imagerel $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_array_new_length@std@@QEAA@XZ DD imagerel $LN14
	DD	imagerel $LN14+95
	DD	imagerel $unwind$??0bad_array_new_length@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1bad_array_new_length@std@@UEAA@XZ DD imagerel $LN14
	DD	imagerel $LN14+47
	DD	imagerel $unwind$??1bad_array_new_length@std@@UEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z DD imagerel $LN14
	DD	imagerel $LN14+54
	DD	imagerel $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z DD imagerel $LN20
	DD	imagerel $LN20+83
	DD	imagerel $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Throw_bad_array_new_length@std@@YAXXZ DD imagerel $LN3
	DD	imagerel $LN3+37
	DD	imagerel $unwind$?_Throw_bad_array_new_length@std@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Xlen_string@std@@YAXXZ DD imagerel $LN3
	DD	imagerel $LN3+22
	DD	imagerel $unwind$?_Xlen_string@std@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z DD imagerel $LN13
	DD	imagerel $LN13+162
	DD	imagerel $unwind$?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z DD imagerel $LN221
	DD	imagerel $LN221+150
	DD	imagerel $unwind$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z@4HA DD imagerel ?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z@4HA
	DD	imagerel ?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z@4HA+24
	DD	imagerel $unwind$?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?c_str@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBAPEB_WXZ DD imagerel $LN22
	DD	imagerel $LN22+131
	DD	imagerel $unwind$?c_str@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBAPEB_WXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ DD imagerel $LN38
	DD	imagerel $LN38+250
	DD	imagerel $unwind$?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Decref@_Ref_count_base@std@@QEAAXXZ DD imagerel $LN11
	DD	imagerel $LN11+98
	DD	imagerel $unwind$?_Decref@_Ref_count_base@std@@QEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?getValue@stWOPSCommand@mu2@@AEBAAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@H@Z DD imagerel $LN14
	DD	imagerel $LN14+307
	DD	imagerel $unwind$?getValue@stWOPSCommand@mu2@@AEBAAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@H@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?ToString@WOPSCommand@mu2@@UEBA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ DD imagerel $LN225
	DD	imagerel $LN225+60
	DD	imagerel $unwind$?ToString@WOPSCommand@mu2@@UEBA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Construct@$00PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z DD imagerel $LN188
	DD	imagerel $LN188+836
	DD	imagerel $unwind$??$_Construct@$00PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z DD imagerel $LN7
	DD	imagerel $LN7+150
	DD	imagerel $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0WOPSCommandEditKnightageTrophyPoint@mu2@@QEAA@XZ DD imagerel $LN20
	DD	imagerel $LN20+124
	DD	imagerel $unwind$??0WOPSCommandEditKnightageTrophyPoint@mu2@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???0WOPSCommandEditKnightageTrophyPoint@mu2@@QEAA@XZ@4HA DD imagerel ?dtor$0@?0???0WOPSCommandEditKnightageTrophyPoint@mu2@@QEAA@XZ@4HA
	DD	imagerel ?dtor$0@?0???0WOPSCommandEditKnightageTrophyPoint@mu2@@QEAA@XZ@4HA+24
	DD	imagerel $unwind$?dtor$0@?0???0WOPSCommandEditKnightageTrophyPoint@mu2@@QEAA@XZ@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1WOPSCommandEditKnightageTrophyPoint@mu2@@UEAA@XZ DD imagerel $LN25
	DD	imagerel $LN25+47
	DD	imagerel $unwind$??1WOPSCommandEditKnightageTrophyPoint@mu2@@UEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?parse@WOPSCommandEditKnightageTrophyPoint@mu2@@MEAA_NAEBUstWOPSCommand@2@@Z DD imagerel $LN101
	DD	imagerel $LN101+438
	DD	imagerel $unwind$?parse@WOPSCommandEditKnightageTrophyPoint@mu2@@MEAA_NAEBUstWOPSCommand@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?executeCommand@WOPSCommandEditKnightageTrophyPoint@mu2@@MEAAXXZ DD imagerel $LN126
	DD	imagerel $LN126+819
	DD	imagerel $unwind$?executeCommand@WOPSCommandEditKnightageTrophyPoint@mu2@@MEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0??executeCommand@WOPSCommandEditKnightageTrophyPoint@mu2@@MEAAXXZ@4HA DD imagerel ?dtor$0@?0??executeCommand@WOPSCommandEditKnightageTrophyPoint@mu2@@MEAAXXZ@4HA
	DD	imagerel ?dtor$0@?0??executeCommand@WOPSCommandEditKnightageTrophyPoint@mu2@@MEAAXXZ@4HA+24
	DD	imagerel $unwind$?dtor$0@?0??executeCommand@WOPSCommandEditKnightageTrophyPoint@mu2@@MEAAXXZ@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$unique_ptr@UWOPSEditKnightageTrophyPointImpl@mu2@@U?$default_delete@UWOPSEditKnightageTrophyPointImpl@mu2@@@std@@@std@@QEAA@XZ DD imagerel $LN15
	DD	imagerel $LN15+74
	DD	imagerel $unwind$??1?$unique_ptr@UWOPSEditKnightageTrophyPointImpl@mu2@@U?$default_delete@UWOPSEditKnightageTrophyPointImpl@mu2@@@std@@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GWOPSCommandEditKnightageTrophyPoint@mu2@@UEAAPEAXI@Z DD imagerel $LN5
	DD	imagerel $LN5+60
	DD	imagerel $unwind$??_GWOPSCommandEditKnightageTrophyPoint@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$shared_ptr@VKnightage@mu2@@@std@@QEAA@XZ DD imagerel $LN22
	DD	imagerel $LN22+41
	DD	imagerel $unwind$??1?$shared_ptr@VKnightage@mu2@@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__E?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ DD imagerel ??__E?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ
	DD	imagerel ??__E?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ+34
	DD	imagerel $unwind$??__E?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__F?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ DD imagerel ??__F?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ
	DD	imagerel ??__F?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ+22
	DD	imagerel $unwind$??__F?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__E?inst@?$ISingleton@VKnightageManager@mu2@@@mu2@@1VKnightageManager@2@A@@YAXXZ DD imagerel ??__E?inst@?$ISingleton@VKnightageManager@mu2@@@mu2@@1VKnightageManager@2@A@@YAXXZ
	DD	imagerel ??__E?inst@?$ISingleton@VKnightageManager@mu2@@@mu2@@1VKnightageManager@2@A@@YAXXZ+34
	DD	imagerel $unwind$??__E?inst@?$ISingleton@VKnightageManager@mu2@@@mu2@@1VKnightageManager@2@A@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__F?inst@?$ISingleton@VKnightageManager@mu2@@@mu2@@1VKnightageManager@2@A@@YAXXZ DD imagerel ??__F?inst@?$ISingleton@VKnightageManager@mu2@@@mu2@@1VKnightageManager@2@A@@YAXXZ
	DD	imagerel ??__F?inst@?$ISingleton@VKnightageManager@mu2@@@mu2@@1VKnightageManager@2@A@@YAXXZ+22
	DD	imagerel $unwind$??__F?inst@?$ISingleton@VKnightageManager@mu2@@@mu2@@1VKnightageManager@2@A@@YAXXZ
pdata	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
??inst$initializer$@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA DQ FLAT:??__E?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ ; ??inst$initializer$@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA
CRT$XCU	ENDS
;	COMDAT ??_R2WOPSCommand@mu2@@8
rdata$r	SEGMENT
??_R2WOPSCommand@mu2@@8 DD imagerel ??_R1A@?0A@EA@WOPSCommand@mu2@@8 ; mu2::WOPSCommand::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3WOPSCommand@mu2@@8
rdata$r	SEGMENT
??_R3WOPSCommand@mu2@@8 DD 00H				; mu2::WOPSCommand::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2WOPSCommand@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVWOPSCommand@mu2@@@8
data$rs	SEGMENT
??_R0?AVWOPSCommand@mu2@@@8 DQ FLAT:??_7type_info@@6B@	; mu2::WOPSCommand `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVWOPSCommand@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R1A@?0A@EA@WOPSCommand@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@WOPSCommand@mu2@@8 DD imagerel ??_R0?AVWOPSCommand@mu2@@@8 ; mu2::WOPSCommand::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3WOPSCommand@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@WOPSCommandEditKnightageTrophyPoint@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@WOPSCommandEditKnightageTrophyPoint@mu2@@8 DD imagerel ??_R0?AVWOPSCommandEditKnightageTrophyPoint@mu2@@@8 ; mu2::WOPSCommandEditKnightageTrophyPoint::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3WOPSCommandEditKnightageTrophyPoint@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2WOPSCommandEditKnightageTrophyPoint@mu2@@8
rdata$r	SEGMENT
??_R2WOPSCommandEditKnightageTrophyPoint@mu2@@8 DD imagerel ??_R1A@?0A@EA@WOPSCommandEditKnightageTrophyPoint@mu2@@8 ; mu2::WOPSCommandEditKnightageTrophyPoint::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@WOPSCommand@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3WOPSCommandEditKnightageTrophyPoint@mu2@@8
rdata$r	SEGMENT
??_R3WOPSCommandEditKnightageTrophyPoint@mu2@@8 DD 00H	; mu2::WOPSCommandEditKnightageTrophyPoint::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2WOPSCommandEditKnightageTrophyPoint@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVWOPSCommandEditKnightageTrophyPoint@mu2@@@8
data$rs	SEGMENT
??_R0?AVWOPSCommandEditKnightageTrophyPoint@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::WOPSCommandEditKnightageTrophyPoint `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVWOPSCommandEditKnightageTrophyPoint@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4WOPSCommandEditKnightageTrophyPoint@mu2@@6B@
rdata$r	SEGMENT
??_R4WOPSCommandEditKnightageTrophyPoint@mu2@@6B@ DD 01H ; mu2::WOPSCommandEditKnightageTrophyPoint::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVWOPSCommandEditKnightageTrophyPoint@mu2@@@8
	DD	imagerel ??_R3WOPSCommandEditKnightageTrophyPoint@mu2@@8
	DD	imagerel ??_R4WOPSCommandEditKnightageTrophyPoint@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_C@_0DA@JAKJJCN@mu2?3?3WOPSCommandEditKnightageTr@
CONST	SEGMENT
??_C@_0DA@JAKJJCN@mu2?3?3WOPSCommandEditKnightageTr@ DB 'mu2::WOPSCommand'
	DB	'EditKnightageTrophyPoint::parse', 00H	; `string'
CONST	ENDS
;	COMDAT ??_C@_0BN@ICLBKJID@2?5?$DM?$DN?5command?4GetValueCount?$CI?$CJ@
CONST	SEGMENT
??_C@_0BN@ICLBKJID@2?5?$DM?$DN?5command?4GetValueCount?$CI?$CJ@ DB '2 <= '
	DB	'command.GetValueCount()', 00H		; `string'
CONST	ENDS
;	COMDAT ??_C@_0DJ@DCKFMAGJ@mu2?3?3WOPSCommandEditKnightageTr@
CONST	SEGMENT
??_C@_0DJ@DCKFMAGJ@mu2?3?3WOPSCommandEditKnightageTr@ DB 'mu2::WOPSComman'
	DB	'dEditKnightageTrophyPoint::executeCommand', 00H ; `string'
CONST	ENDS
;	COMDAT ??_C@_0GG@MDKIIJBC@F?3?2Release_Branch?2Server?2Develo@
CONST	SEGMENT
??_C@_0GG@MDKIIJBC@F?3?2Release_Branch?2Server?2Develo@ DB 'F:\Release_Br'
	DB	'anch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSE'
	DB	'ditKnightageTrophyPoint.cpp', 00H		; `string'
CONST	ENDS
;	COMDAT ??_C@_0CL@BCIDDGBP@?$FLWOPS?$FN?5Set?5Knightage?5Trophy?5?3?5K@
CONST	SEGMENT
??_C@_0CL@BCIDDGBP@?$FLWOPS?$FN?5Set?5Knightage?5Trophy?5?3?5K@ DB '[WOPS'
	DB	'] Set Knightage Trophy : K(%d), P(%d)', 00H	; `string'
CONST	ENDS
;	COMDAT ??_7WOPSCommandEditKnightageTrophyPoint@mu2@@6B@
CONST	SEGMENT
??_7WOPSCommandEditKnightageTrophyPoint@mu2@@6B@ DQ FLAT:??_R4WOPSCommandEditKnightageTrophyPoint@mu2@@6B@ ; mu2::WOPSCommandEditKnightageTrophyPoint::`vftable'
	DQ	FLAT:??_EWOPSCommandEditKnightageTrophyPoint@mu2@@UEAAPEAXI@Z
	DQ	FLAT:?OnEnter@WOPSCommandEditKnightageTrophyPoint@mu2@@UEBAXPEAVEntityPlayer@2@@Z
	DQ	FLAT:?OnBegin@WOPSCommand@mu2@@UEBAXXZ
	DQ	FLAT:?OnEnd@WOPSCommand@mu2@@UEBAXXZ
	DQ	FLAT:?IsOverlapable@WOPSCommand@mu2@@UEBA_NXZ
	DQ	FLAT:?ToString@WOPSCommand@mu2@@UEBA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ
	DQ	FLAT:?parse@WOPSCommandEditKnightageTrophyPoint@mu2@@MEAA_NAEBUstWOPSCommand@2@@Z
	DQ	FLAT:?isAvailablePeriod@WOPSCommand@mu2@@MEBA_NXZ
	DQ	FLAT:?executeCommand@WOPSCommandEditKnightageTrophyPoint@mu2@@MEAAXXZ
	DQ	FLAT:?releaseCommand@WOPSCommand@mu2@@MEAAX_N@Z
	DQ	FLAT:?checkRelease@WOPSCommand@mu2@@MEAA_NXZ
CONST	ENDS
;	COMDAT ??_R4bad_alloc@std@@6B@
rdata$r	SEGMENT
??_R4bad_alloc@std@@6B@ DD 01H				; std::bad_alloc::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVbad_alloc@std@@@8
	DD	imagerel ??_R3bad_alloc@std@@8
	DD	imagerel ??_R4bad_alloc@std@@6B@
rdata$r	ENDS
;	COMDAT ??_R2bad_alloc@std@@8
rdata$r	SEGMENT
??_R2bad_alloc@std@@8 DD imagerel ??_R1A@?0A@EA@bad_alloc@std@@8 ; std::bad_alloc::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@exception@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3bad_alloc@std@@8
rdata$r	SEGMENT
??_R3bad_alloc@std@@8 DD 00H				; std::bad_alloc::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2bad_alloc@std@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@bad_alloc@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@bad_alloc@std@@8 DD imagerel ??_R0?AVbad_alloc@std@@@8 ; std::bad_alloc::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3bad_alloc@std@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@bad_array_new_length@std@@8 DD imagerel ??_R0?AVbad_array_new_length@std@@@8 ; std::bad_array_new_length::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	02H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3bad_array_new_length@std@@8
rdata$r	ENDS
;	COMDAT ??_R2bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R2bad_array_new_length@std@@8 DD imagerel ??_R1A@?0A@EA@bad_array_new_length@std@@8 ; std::bad_array_new_length::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@bad_alloc@std@@8
	DD	imagerel ??_R1A@?0A@EA@exception@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R3bad_array_new_length@std@@8 DD 00H			; std::bad_array_new_length::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	03H
	DD	imagerel ??_R2bad_array_new_length@std@@8
rdata$r	ENDS
;	COMDAT ??_R4bad_array_new_length@std@@6B@
rdata$r	SEGMENT
??_R4bad_array_new_length@std@@6B@ DD 01H		; std::bad_array_new_length::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVbad_array_new_length@std@@@8
	DD	imagerel ??_R3bad_array_new_length@std@@8
	DD	imagerel ??_R4bad_array_new_length@std@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@exception@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@exception@std@@8 DD imagerel ??_R0?AVexception@std@@@8 ; std::exception::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3exception@std@@8
rdata$r	ENDS
;	COMDAT ??_R2exception@std@@8
rdata$r	SEGMENT
??_R2exception@std@@8 DD imagerel ??_R1A@?0A@EA@exception@std@@8 ; std::exception::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3exception@std@@8
rdata$r	SEGMENT
??_R3exception@std@@8 DD 00H				; std::exception::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2exception@std@@8
rdata$r	ENDS
;	COMDAT ??_R4exception@std@@6B@
rdata$r	SEGMENT
??_R4exception@std@@6B@ DD 01H				; std::exception::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVexception@std@@@8
	DD	imagerel ??_R3exception@std@@8
	DD	imagerel ??_R4exception@std@@6B@
rdata$r	ENDS
;	COMDAT ??_C@_0BN@FHEEOEA@mu2?3?3stWOPSCommand?3?3getValue@
CONST	SEGMENT
??_C@_0BN@FHEEOEA@mu2?3?3stWOPSCommand?3?3getValue@ DB 'mu2::stWOPSComman'
	DB	'd::getValue', 00H				; `string'
CONST	ENDS
;	COMDAT ??_C@_0BK@FMPHEHPK@arryIndex?5?$DM?5values?4size?$CI?$CJ@
CONST	SEGMENT
??_C@_0BK@FMPHEHPK@arryIndex?5?$DM?5values?4size?$CI?$CJ@ DB 'arryIndex <'
	DB	' values.size()', 00H			; `string'
CONST	ENDS
;	COMDAT ??_C@_0EB@JGAFMFJE@F?3?2Release_Branch?2Server?2Develo@
CONST	SEGMENT
??_C@_0EB@JGAFMFJE@F?3?2Release_Branch?2Server?2Develo@ DB 'F:\Release_Br'
	DB	'anch\Server\Development\Mu2Common\Protocol\Common.h', 00H ; `string'
CONST	ENDS
;	COMDAT ??_C@_11LOCGONAA@@
CONST	SEGMENT
??_C@_11LOCGONAA@@ DB 00H, 00H				; `string'
CONST	ENDS
;	COMDAT ??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
CONST	SEGMENT
??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@ DB 'g', 00H, 'a', 00H, 'm', 00H, 'e'
	DB	00H, 00H, 00H				; `string'
CONST	ENDS
;	COMDAT ??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
CONST	SEGMENT
??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@ DB '%'
	DB	's> ASSERT - %s, %s(%d)', 00H		; `string'
CONST	ENDS
;	COMDAT ??_C@_0BA@JFNIOLAK@string?5too?5long@
CONST	SEGMENT
??_C@_0BA@JFNIOLAK@string?5too?5long@ DB 'string too long', 00H ; `string'
CONST	ENDS
;	COMDAT _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 DD 010H
	DD	imagerel ??_R0?AVbad_alloc@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0bad_alloc@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVbad_alloc@std@@@8
data$r	SEGMENT
??_R0?AVbad_alloc@std@@@8 DQ FLAT:??_7type_info@@6B@	; std::bad_alloc `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVbad_alloc@std@@', 00H
data$r	ENDS
;	COMDAT _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 DD 00H
	DD	imagerel ??_R0?AVbad_array_new_length@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0bad_array_new_length@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVbad_array_new_length@std@@@8
data$r	SEGMENT
??_R0?AVbad_array_new_length@std@@@8 DQ FLAT:??_7type_info@@6B@ ; std::bad_array_new_length `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVbad_array_new_length@std@@', 00H
data$r	ENDS
;	COMDAT _CTA3?AVbad_array_new_length@std@@
xdata$x	SEGMENT
_CTA3?AVbad_array_new_length@std@@ DD 03H
	DD	imagerel _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
	DD	imagerel _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
	DD	imagerel _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
xdata$x	ENDS
;	COMDAT _TI3?AVbad_array_new_length@std@@
xdata$x	SEGMENT
_TI3?AVbad_array_new_length@std@@ DD 00H
	DD	imagerel ??1bad_array_new_length@std@@UEAA@XZ
	DD	00H
	DD	imagerel _CTA3?AVbad_array_new_length@std@@
xdata$x	ENDS
;	COMDAT _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 DD 00H
	DD	imagerel ??_R0?AVexception@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0exception@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVexception@std@@@8
data$r	SEGMENT
??_R0?AVexception@std@@@8 DQ FLAT:??_7type_info@@6B@	; std::exception `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVexception@std@@', 00H
data$r	ENDS
;	COMDAT ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@
CONST	SEGMENT
??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ DB 'bad array new length', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7bad_array_new_length@std@@6B@
CONST	SEGMENT
??_7bad_array_new_length@std@@6B@ DQ FLAT:??_R4bad_array_new_length@std@@6B@ ; std::bad_array_new_length::`vftable'
	DQ	FLAT:??_Ebad_array_new_length@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT ??_7bad_alloc@std@@6B@
CONST	SEGMENT
??_7bad_alloc@std@@6B@ DQ FLAT:??_R4bad_alloc@std@@6B@	; std::bad_alloc::`vftable'
	DQ	FLAT:??_Ebad_alloc@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT ??_C@_0BC@EOODALEL@Unknown?5exception@
CONST	SEGMENT
??_C@_0BC@EOODALEL@Unknown?5exception@ DB 'Unknown exception', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7exception@std@@6B@
CONST	SEGMENT
??_7exception@std@@6B@ DQ FLAT:??_R4exception@std@@6B@	; std::exception::`vftable'
	DQ	FLAT:??_Eexception@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__F?inst@?$ISingleton@VKnightageManager@mu2@@@mu2@@1VKnightageManager@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__E?inst@?$ISingleton@VKnightageManager@mu2@@@mu2@@1VKnightageManager@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__F?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__E?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$shared_ptr@VKnightage@mu2@@@std@@QEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GWOPSCommandEditKnightageTrophyPoint@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$unique_ptr@UWOPSEditKnightageTrophyPointImpl@mu2@@U?$default_delete@UWOPSEditKnightageTrophyPointImpl@mu2@@@std@@@std@@QEAA@XZ DD 010901H
	DD	08209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0??executeCommand@WOPSCommandEditKnightageTrophyPoint@mu2@@MEAAXXZ@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?executeCommand@WOPSCommandEditKnightageTrophyPoint@mu2@@MEAAXXZ DB 06H
	DB	00H
	DB	00H
	DB	0eeH
	DB	02H
	DB	085H, 0aH
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?executeCommand@WOPSCommandEditKnightageTrophyPoint@mu2@@MEAAXXZ DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0??executeCommand@WOPSCommandEditKnightageTrophyPoint@mu2@@MEAAXXZ@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?executeCommand@WOPSCommandEditKnightageTrophyPoint@mu2@@MEAAXXZ DB 028H
	DD	imagerel $stateUnwindMap$?executeCommand@WOPSCommandEditKnightageTrophyPoint@mu2@@MEAAXXZ
	DD	imagerel $ip2state$?executeCommand@WOPSCommandEditKnightageTrophyPoint@mu2@@MEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?executeCommand@WOPSCommandEditKnightageTrophyPoint@mu2@@MEAAXXZ DD 020c11H
	DD	025010cH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?executeCommand@WOPSCommandEditKnightageTrophyPoint@mu2@@MEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?parse@WOPSCommandEditKnightageTrophyPoint@mu2@@MEAA_NAEBUstWOPSCommand@2@@Z DD 021101H
	DD	0170111H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1WOPSCommandEditKnightageTrophyPoint@mu2@@UEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???0WOPSCommandEditKnightageTrophyPoint@mu2@@QEAA@XZ@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??0WOPSCommandEditKnightageTrophyPoint@mu2@@QEAA@XZ DB 06H
	DB	00H
	DB	00H
	DB	'0'
	DB	02H
	DB	0b4H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??0WOPSCommandEditKnightageTrophyPoint@mu2@@QEAA@XZ DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???0WOPSCommandEditKnightageTrophyPoint@mu2@@QEAA@XZ@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??0WOPSCommandEditKnightageTrophyPoint@mu2@@QEAA@XZ DB 028H
	DD	imagerel $stateUnwindMap$??0WOPSCommandEditKnightageTrophyPoint@mu2@@QEAA@XZ
	DD	imagerel $ip2state$??0WOPSCommandEditKnightageTrophyPoint@mu2@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0WOPSCommandEditKnightageTrophyPoint@mu2@@QEAA@XZ DD 010911H
	DD	0a209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??0WOPSCommandEditKnightageTrophyPoint@mu2@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z DD 010901H
	DD	0a209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Construct@$00PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z DD 031701H
	DD	0200117H
	DD	07010H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?ToString@WOPSCommand@mu2@@UEBA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ DD 010e01H
	DD	0620eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?getValue@stWOPSCommand@mu2@@AEBAAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@H@Z DD 021001H
	DD	0130110H
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DB	017H
	DB	040H
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Decref@_Ref_count_base@std@@QEAAXXZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ DD 020c01H
	DD	011010cH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?c_str@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBAPEB_WXZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z DB 06H
	DB	00H
	DB	00H
	DB	0b4H
	DB	02H
	DB	'b'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z DB 028H
	DD	imagerel $stateUnwindMap$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z
	DD	imagerel $ip2state$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z DD 020f11H
	DD	0700b920fH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Xlen_string@std@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Throw_bad_array_new_length@std@@YAXXZ DD 010401H
	DD	08204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1bad_array_new_length@std@@UEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_array_new_length@std@@QEAA@XZ DD 010601H
	DD	07006H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z DD 020f01H
	DD	0700b320fH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gexception@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?what@exception@std@@UEBAPEBDXZ DD 010901H
	DD	02209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0exception@std@@QEAA@AEBV01@@Z DD 020f01H
	DD	0700b320fH
xdata	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
??inst$initializer$@?$ISingleton@VKnightageManager@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA DQ FLAT:??__E?inst@?$ISingleton@VKnightageManager@mu2@@@mu2@@1VKnightageManager@2@A@@YAXXZ ; ??inst$initializer$@?$ISingleton@VKnightageManager@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA
CRT$XCU	ENDS
; Function compile flags: /Odtp
;	COMDAT ??__F?inst@?$ISingleton@VKnightageManager@mu2@@@mu2@@1VKnightageManager@2@A@@YAXXZ
text$yd	SEGMENT
??__F?inst@?$ISingleton@VKnightageManager@mu2@@@mu2@@1VKnightageManager@2@A@@YAXXZ PROC ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::KnightageManager>::inst'', COMDAT
  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?inst@?$ISingleton@VKnightageManager@mu2@@@mu2@@1VKnightageManager@2@A ; mu2::ISingleton<mu2::KnightageManager>::inst
  0000b	e8 00 00 00 00	 call	 ??1KnightageManager@mu2@@UEAA@XZ ; mu2::KnightageManager::~KnightageManager
  00010	90		 npad	 1
  00011	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00015	c3		 ret	 0
??__F?inst@?$ISingleton@VKnightageManager@mu2@@@mu2@@1VKnightageManager@2@A@@YAXXZ ENDP ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::KnightageManager>::inst''
text$yd	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??__E?inst@?$ISingleton@VKnightageManager@mu2@@@mu2@@1VKnightageManager@2@A@@YAXXZ
text$di	SEGMENT
??__E?inst@?$ISingleton@VKnightageManager@mu2@@@mu2@@1VKnightageManager@2@A@@YAXXZ PROC ; `dynamic initializer for 'mu2::ISingleton<mu2::KnightageManager>::inst'', COMDAT

; 90   : template<typename T> T ISingleton<T>::inst;

  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?inst@?$ISingleton@VKnightageManager@mu2@@@mu2@@1VKnightageManager@2@A ; mu2::ISingleton<mu2::KnightageManager>::inst
  0000b	e8 00 00 00 00	 call	 ??0KnightageManager@mu2@@AEAA@XZ ; mu2::KnightageManager::KnightageManager
  00010	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??__F?inst@?$ISingleton@VKnightageManager@mu2@@@mu2@@1VKnightageManager@2@A@@YAXXZ ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::KnightageManager>::inst''
  00017	e8 00 00 00 00	 call	 atexit
  0001c	90		 npad	 1
  0001d	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00021	c3		 ret	 0
??__E?inst@?$ISingleton@VKnightageManager@mu2@@@mu2@@1VKnightageManager@2@A@@YAXXZ ENDP ; `dynamic initializer for 'mu2::ISingleton<mu2::KnightageManager>::inst''
text$di	ENDS
; Function compile flags: /Odtp
;	COMDAT ??__F?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ
text$yd	SEGMENT
??__F?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ PROC ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::GlobalLoadScript>::inst'', COMDAT
  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A ; mu2::ISingleton<mu2::GlobalLoadScript>::inst
  0000b	e8 00 00 00 00	 call	 ??1GlobalLoadScript@mu2@@UEAA@XZ ; mu2::GlobalLoadScript::~GlobalLoadScript
  00010	90		 npad	 1
  00011	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00015	c3		 ret	 0
??__F?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ ENDP ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::GlobalLoadScript>::inst''
text$yd	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??__E?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ
text$di	SEGMENT
??__E?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ PROC ; `dynamic initializer for 'mu2::ISingleton<mu2::GlobalLoadScript>::inst'', COMDAT

; 90   : template<typename T> T ISingleton<T>::inst;

  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A ; mu2::ISingleton<mu2::GlobalLoadScript>::inst
  0000b	e8 00 00 00 00	 call	 ??0GlobalLoadScript@mu2@@AEAA@XZ ; mu2::GlobalLoadScript::GlobalLoadScript
  00010	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??__F?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::GlobalLoadScript>::inst''
  00017	e8 00 00 00 00	 call	 atexit
  0001c	90		 npad	 1
  0001d	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00021	c3		 ret	 0
??__E?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ ENDP ; `dynamic initializer for 'mu2::ISingleton<mu2::GlobalLoadScript>::inst''
text$di	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ??1?$shared_ptr@VKnightage@mu2@@@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1?$shared_ptr@VKnightage@mu2@@@std@@QEAA@XZ PROC	; std::shared_ptr<mu2::Knightage>::~shared_ptr<mu2::Knightage>, COMDAT

; 1689 :     ~shared_ptr() noexcept { // release resource

$LN22:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 1384 :         if (_Rep) {

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00013	74 0f		 je	 SHORT $LN5@shared_ptr

; 1385 :             _Rep->_Decref();

  00015	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 8b 48 08	 mov	 rcx, QWORD PTR [rax+8]
  0001e	e8 00 00 00 00	 call	 ?_Decref@_Ref_count_base@std@@QEAAXXZ ; std::_Ref_count_base::_Decref
  00023	90		 npad	 1
$LN5@shared_ptr:

; 1690 :         this->_Decref();
; 1691 :     }

  00024	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00028	c3		 ret	 0
??1?$shared_ptr@VKnightage@mu2@@@std@@QEAA@XZ ENDP	; std::shared_ptr<mu2::Knightage>::~shared_ptr<mu2::Knightage>
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??_GWOPSCommandEditKnightageTrophyPoint@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GWOPSCommandEditKnightageTrophyPoint@mu2@@UEAAPEAXI@Z PROC ; mu2::WOPSCommandEditKnightageTrophyPoint::`scalar deleting destructor', COMDAT
$LN5:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00012	e8 00 00 00 00	 call	 ??1WOPSCommandEditKnightageTrophyPoint@mu2@@UEAA@XZ ; mu2::WOPSCommandEditKnightageTrophyPoint::~WOPSCommandEditKnightageTrophyPoint
  00017	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  0001b	83 e0 01	 and	 eax, 1
  0001e	85 c0		 test	 eax, eax
  00020	74 10		 je	 SHORT $LN2@scalar
  00022	ba 48 00 00 00	 mov	 edx, 72			; 00000048H
  00027	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0002c	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00031	90		 npad	 1
$LN2@scalar:
  00032	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00037	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0003b	c3		 ret	 0
??_GWOPSCommandEditKnightageTrophyPoint@mu2@@UEAAPEAXI@Z ENDP ; mu2::WOPSCommandEditKnightageTrophyPoint::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ??1?$unique_ptr@UWOPSEditKnightageTrophyPointImpl@mu2@@U?$default_delete@UWOPSEditKnightageTrophyPointImpl@mu2@@@std@@@std@@QEAA@XZ
_TEXT	SEGMENT
_Ptr$ = 32
$T1 = 40
$T2 = 48
this$ = 80
??1?$unique_ptr@UWOPSEditKnightageTrophyPointImpl@mu2@@U?$default_delete@UWOPSEditKnightageTrophyPointImpl@mu2@@@std@@@std@@QEAA@XZ PROC ; std::unique_ptr<mu2::WOPSEditKnightageTrophyPointImpl,std::default_delete<mu2::WOPSEditKnightageTrophyPointImpl> >::~unique_ptr<mu2::WOPSEditKnightageTrophyPointImpl,std::default_delete<mu2::WOPSEditKnightageTrophyPointImpl> >, COMDAT

; 3425 :     _CONSTEXPR23 ~unique_ptr() noexcept {

$LN15:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 3426 :         if (_Mypair._Myval2) {

  00009	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 38 00	 cmp	 QWORD PTR [rax], 0
  00012	74 31		 je	 SHORT $LN2@unique_ptr

; 3427 :             _Mypair._Get_first()(_Mypair._Myval2);

  00014	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00019	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3427 :             _Mypair._Get_first()(_Mypair._Myval2);

  0001e	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00026	48 89 44 24 20	 mov	 QWORD PTR _Ptr$[rsp], rax

; 3309 :         delete _Ptr;

  0002b	48 8b 44 24 20	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00030	48 89 44 24 28	 mov	 QWORD PTR $T1[rsp], rax
  00035	ba 08 00 00 00	 mov	 edx, 8
  0003a	48 8b 4c 24 28	 mov	 rcx, QWORD PTR $T1[rsp]
  0003f	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00044	90		 npad	 1
$LN2@unique_ptr:

; 3428 :         }
; 3429 : 
; 3430 : #if _MSVC_STL_DESTRUCTOR_TOMBSTONES
; 3431 :         if constexpr (is_pointer_v<pointer>) {
; 3432 :             if (!_STD _Is_constant_evaluated()) {
; 3433 :                 const auto _Tombstone{reinterpret_cast<pointer>(_MSVC_STL_UINTPTR_TOMBSTONE_VALUE)};
; 3434 :                 _Mypair._Myval2 = _Tombstone;
; 3435 :             }
; 3436 :         }
; 3437 : #endif // _MSVC_STL_DESTRUCTOR_TOMBSTONES
; 3438 :     }

  00045	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00049	c3		 ret	 0
??1?$unique_ptr@UWOPSEditKnightageTrophyPointImpl@mu2@@U?$default_delete@UWOPSEditKnightageTrophyPointImpl@mu2@@@std@@@std@@QEAA@XZ ENDP ; std::unique_ptr<mu2::WOPSEditKnightageTrophyPointImpl,std::default_delete<mu2::WOPSEditKnightageTrophyPointImpl> >::~unique_ptr<mu2::WOPSEditKnightageTrophyPointImpl,std::default_delete<mu2::WOPSEditKnightageTrophyPointImpl> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Knightage\Knightage.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp
;	COMDAT ?executeCommand@WOPSCommandEditKnightageTrophyPoint@mu2@@MEAAXXZ
_TEXT	SEGMENT
$T1 = 80
cur$2 = 84
tv201 = 88
$T3 = 92
recountTrophyPoint$4 = 96
temp$5 = 100
temp$6 = 104
knightagePtr$7 = 112
info$8 = 128
$T9 = 136
$T10 = 144
$T11 = 152
$T12 = 160
$T13 = 168
$T14 = 176
$T15 = 184
$T16 = 192
$T17 = 200
$T18 = 208
$T19 = 216
$T20 = 224
$T21 = 232
$T22 = 240
$T23 = 248
$T24 = 256
$T25 = 264
$T26 = 272
this$ = 304
?executeCommand@WOPSCommandEditKnightageTrophyPoint@mu2@@MEAAXXZ PROC ; mu2::WOPSCommandEditKnightageTrophyPoint::executeCommand, COMDAT

; 35   : {

$LN126:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec 28 01
	00 00		 sub	 rsp, 296		; 00000128H

; 36   : 	if (false == isApplied())

  0000c	48 8b 8c 24 30
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00014	e8 00 00 00 00	 call	 ?isApplied@WOPSCommand@mu2@@QEBA_NXZ ; mu2::WOPSCommand::isApplied
  00019	0f b6 c0	 movzx	 eax, al
  0001c	85 c0		 test	 eax, eax
  0001e	0f 85 07 03 00
	00		 jne	 $LN2@executeCom

; 37   : 	{
; 38   : 		updateStateAndSaveDb(WOPSCommandState::APPLIED);

  00024	b2 02		 mov	 dl, 2
  00026	48 8b 8c 24 30
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0002e	e8 00 00 00 00	 call	 ?updateStateAndSaveDb@WOPSCommand@mu2@@IEAAXW4Enum@WOPSCommandState@2@@Z ; mu2::WOPSCommand::updateStateAndSaveDb
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3453 :         return _Mypair._Myval2;

  00033	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0003b	48 8b 40 40	 mov	 rax, QWORD PTR [rax+64]
  0003f	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  00047	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VKnightageManager@mu2@@@mu2@@1VKnightageManager@2@A ; mu2::ISingleton<mu2::KnightageManager>::inst
  0004e	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp

; 41   : 		KnightagePtr knightagePtr = theKnightageManager.GetKnightage(m_impl->knightageId);

  00056	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T9[rsp]
  0005e	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR $T10[rsp]
  00066	44 8b 01	 mov	 r8d, DWORD PTR [rcx]
  00069	48 8d 54 24 70	 lea	 rdx, QWORD PTR knightagePtr$7[rsp]
  0006e	48 8b c8	 mov	 rcx, rax
  00071	e8 00 00 00 00	 call	 ?GetKnightage@KnightageManager@mu2@@QEAA?AV?$shared_ptr@VKnightage@mu2@@@std@@I@Z ; mu2::KnightageManager::GetKnightage
  00076	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1309 :         return _Ptr;

  00077	48 8b 44 24 70	 mov	 rax, QWORD PTR knightagePtr$7[rsp]
  0007c	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax

; 1789 :         return get() != nullptr;

  00084	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  0008c	48 85 c0	 test	 rax, rax
  0008f	74 0a		 je	 SHORT $LN19@executeCom
  00091	c7 44 24 58 01
	00 00 00	 mov	 DWORD PTR tv201[rsp], 1
  00099	eb 08		 jmp	 SHORT $LN20@executeCom
$LN19@executeCom:
  0009b	c7 44 24 58 00
	00 00 00	 mov	 DWORD PTR tv201[rsp], 0
$LN20@executeCom:
  000a3	0f b6 44 24 58	 movzx	 eax, BYTE PTR tv201[rsp]
  000a8	88 44 24 50	 mov	 BYTE PTR $T1[rsp], al
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp

; 42   : 		if (knightagePtr)

  000ac	0f b6 44 24 50	 movzx	 eax, BYTE PTR $T1[rsp]
  000b1	0f b6 c0	 movzx	 eax, al
  000b4	85 c0		 test	 eax, eax
  000b6	0f 84 5c 02 00
	00		 je	 $LN3@executeCom
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  000bc	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A ; mu2::ISingleton<mu2::GlobalLoadScript>::inst
  000c3	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp

; 44   : 			const KnightTropyInfo &info = SCRIPTS.GetKnightageTrophyInfo();

  000cb	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
  000d3	48 8b c8	 mov	 rcx, rax
  000d6	e8 00 00 00 00	 call	 ?GetKnightageTrophyInfo@GlobalLoadScript@mu2@@QEBAAEBUKnightTropyInfo@2@XZ ; mu2::GlobalLoadScript::GetKnightageTrophyInfo
  000db	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR info$8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3453 :         return _Mypair._Myval2;

  000e3	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000eb	48 8b 40 40	 mov	 rax, QWORD PTR [rax+64]
  000ef	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp

; 46   : 			if (m_impl->lastPoint > info.maxTrophy)

  000f7	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T13[rsp]
  000ff	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR info$8[rsp]
  00107	8b 49 0c	 mov	 ecx, DWORD PTR [rcx+12]
  0010a	39 48 04	 cmp	 DWORD PTR [rax+4], ecx
  0010d	7e 2a		 jle	 SHORT $LN4@executeCom
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3453 :         return _Mypair._Myval2;

  0010f	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00117	48 8b 40 40	 mov	 rax, QWORD PTR [rax+64]
  0011b	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T14[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp

; 47   : 				m_impl->lastPoint = info.maxTrophy;

  00123	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR $T14[rsp]
  0012b	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR info$8[rsp]
  00133	8b 49 0c	 mov	 ecx, DWORD PTR [rcx+12]
  00136	89 48 04	 mov	 DWORD PTR [rax+4], ecx
$LN4@executeCom:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1309 :         return _Ptr;

  00139	48 8b 44 24 70	 mov	 rax, QWORD PTR knightagePtr$7[rsp]
  0013e	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T15[rsp], rax

; 1773 :         return get();

  00146	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T15[rsp]
  0014e	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T16[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Knightage\Knightage.h

; 157  : 	TrophyPoint	GetTrophyPoint() { return  m_trophyPoint; }	// 기사단 트로피 포인트 

  00156	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR $T16[rsp]
  0015e	8b 80 9c 02 00
	00		 mov	 eax, DWORD PTR [rax+668]
  00164	89 44 24 5c	 mov	 DWORD PTR $T3[rsp], eax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp

; 49   : 			TrophyPoint cur = knightagePtr->GetTrophyPoint();

  00168	8b 44 24 5c	 mov	 eax, DWORD PTR $T3[rsp]
  0016c	89 44 24 54	 mov	 DWORD PTR cur$2[rsp], eax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3453 :         return _Mypair._Myval2;

  00170	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00178	48 8b 40 40	 mov	 rax, QWORD PTR [rax+64]
  0017c	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T17[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp

; 50   : 			if (cur < m_impl->lastPoint)

  00184	48 8b 84 24 c8
	00 00 00	 mov	 rax, QWORD PTR $T17[rsp]
  0018c	8b 40 04	 mov	 eax, DWORD PTR [rax+4]
  0018f	39 44 24 54	 cmp	 DWORD PTR cur$2[rsp], eax
  00193	7d 76		 jge	 SHORT $LN5@executeCom

; 51   : 			{
; 52   : 				TrophyPoint temp = 0, recountTrophyPoint = 0;

  00195	c7 44 24 64 00
	00 00 00	 mov	 DWORD PTR temp$5[rsp], 0
  0019d	c7 44 24 60 00
	00 00 00	 mov	 DWORD PTR recountTrophyPoint$4[rsp], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3453 :         return _Mypair._Myval2;

  001a5	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  001ad	48 8b 40 40	 mov	 rax, QWORD PTR [rax+64]
  001b1	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR $T19[rsp], rax

; 1309 :         return _Ptr;

  001b9	48 8b 44 24 70	 mov	 rax, QWORD PTR knightagePtr$7[rsp]
  001be	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR $T18[rsp], rax

; 1773 :         return get();

  001c6	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR $T18[rsp]
  001ce	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR $T20[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp

; 53   : 				knightagePtr->AddTrophyPoint(m_impl->lastPoint - cur, temp, recountTrophyPoint);

  001d6	48 8b 84 24 d8
	00 00 00	 mov	 rax, QWORD PTR $T19[rsp]
  001de	8b 4c 24 54	 mov	 ecx, DWORD PTR cur$2[rsp]
  001e2	8b 40 04	 mov	 eax, DWORD PTR [rax+4]
  001e5	2b c1		 sub	 eax, ecx
  001e7	48 8b 8c 24 e0
	00 00 00	 mov	 rcx, QWORD PTR $T20[rsp]
  001ef	c6 44 24 20 01	 mov	 BYTE PTR [rsp+32], 1
  001f4	4c 8d 4c 24 60	 lea	 r9, QWORD PTR recountTrophyPoint$4[rsp]
  001f9	4c 8d 44 24 64	 lea	 r8, QWORD PTR temp$5[rsp]
  001fe	8b d0		 mov	 edx, eax
  00200	e8 00 00 00 00	 call	 ?AddTrophyPoint@Knightage@mu2@@QEAA_NHAEAH0_N@Z ; mu2::Knightage::AddTrophyPoint
  00205	90		 npad	 1

; 54   : 			}

  00206	e9 89 00 00 00	 jmp	 $LN6@executeCom
$LN5@executeCom:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3453 :         return _Mypair._Myval2;

  0020b	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00213	48 8b 40 40	 mov	 rax, QWORD PTR [rax+64]
  00217	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR $T21[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp

; 55   : 			else if(cur > m_impl->lastPoint)

  0021f	48 8b 84 24 e8
	00 00 00	 mov	 rax, QWORD PTR $T21[rsp]
  00227	8b 40 04	 mov	 eax, DWORD PTR [rax+4]
  0022a	39 44 24 54	 cmp	 DWORD PTR cur$2[rsp], eax
  0022e	7e 64		 jle	 SHORT $LN6@executeCom

; 56   : 			{
; 57   : 				TrophyPoint temp = 0;

  00230	c7 44 24 68 00
	00 00 00	 mov	 DWORD PTR temp$6[rsp], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3453 :         return _Mypair._Myval2;

  00238	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00240	48 8b 40 40	 mov	 rax, QWORD PTR [rax+64]
  00244	48 89 84 24 f8
	00 00 00	 mov	 QWORD PTR $T23[rsp], rax

; 1309 :         return _Ptr;

  0024c	48 8b 44 24 70	 mov	 rax, QWORD PTR knightagePtr$7[rsp]
  00251	48 89 84 24 f0
	00 00 00	 mov	 QWORD PTR $T22[rsp], rax

; 1773 :         return get();

  00259	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR $T22[rsp]
  00261	48 89 84 24 00
	01 00 00	 mov	 QWORD PTR $T24[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp

; 58   : 				knightagePtr->SubTrophyPoint(cur - m_impl->lastPoint, temp);

  00269	48 8b 84 24 f8
	00 00 00	 mov	 rax, QWORD PTR $T23[rsp]
  00271	8b 40 04	 mov	 eax, DWORD PTR [rax+4]
  00274	8b 4c 24 54	 mov	 ecx, DWORD PTR cur$2[rsp]
  00278	2b c8		 sub	 ecx, eax
  0027a	8b c1		 mov	 eax, ecx
  0027c	48 8b 8c 24 00
	01 00 00	 mov	 rcx, QWORD PTR $T24[rsp]
  00284	41 b1 01	 mov	 r9b, 1
  00287	4c 8d 44 24 68	 lea	 r8, QWORD PTR temp$6[rsp]
  0028c	8b d0		 mov	 edx, eax
  0028e	e8 00 00 00 00	 call	 ?SubTrophyPoint@Knightage@mu2@@QEAA_NHAEAH_N@Z ; mu2::Knightage::SubTrophyPoint
  00293	90		 npad	 1
$LN6@executeCom:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3453 :         return _Mypair._Myval2;

  00294	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0029c	48 8b 40 40	 mov	 rax, QWORD PTR [rax+64]
  002a0	48 89 84 24 08
	01 00 00	 mov	 QWORD PTR $T25[rsp], rax
  002a8	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  002b0	48 8b 40 40	 mov	 rax, QWORD PTR [rax+64]
  002b4	48 89 84 24 10
	01 00 00	 mov	 QWORD PTR $T26[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp

; 61   : 			MU2_INFO_LOG(LogCategory::CONTENTS, "[WOPS] Set Knightage Trophy : K(%d), P(%d)", m_impl->knightageId, m_impl->lastPoint)

  002bc	48 8b 84 24 08
	01 00 00	 mov	 rax, QWORD PTR $T25[rsp]
  002c4	8b 40 04	 mov	 eax, DWORD PTR [rax+4]
  002c7	89 44 24 40	 mov	 DWORD PTR [rsp+64], eax
  002cb	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR $T26[rsp]
  002d3	8b 00		 mov	 eax, DWORD PTR [rax]
  002d5	89 44 24 38	 mov	 DWORD PTR [rsp+56], eax
  002d9	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CL@BCIDDGBP@?$FLWOPS?$FN?5Set?5Knightage?5Trophy?5?3?5K@
  002e0	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  002e5	c7 44 24 28 3d
	00 00 00	 mov	 DWORD PTR [rsp+40], 61	; 0000003dH
  002ed	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0GG@MDKIIJBC@F?3?2Release_Branch?2Server?2Develo@
  002f4	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  002f9	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0DJ@DCKFMAGJ@mu2?3?3WOPSCommandEditKnightageTr@
  00300	41 b8 20 4e 00
	00		 mov	 r8d, 20000		; 00004e20H
  00306	ba 19 00 00 00	 mov	 edx, 25
  0030b	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  00312	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  00317	90		 npad	 1
$LN3@executeCom:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1384 :         if (_Rep) {

  00318	48 83 7c 24 78
	00		 cmp	 QWORD PTR knightagePtr$7[rsp+8], 0
  0031e	74 0b		 je	 SHORT $LN2@executeCom

; 1385 :             _Rep->_Decref();

  00320	48 8b 4c 24 78	 mov	 rcx, QWORD PTR knightagePtr$7[rsp+8]
  00325	e8 00 00 00 00	 call	 ?_Decref@_Ref_count_base@std@@QEAAXXZ ; std::_Ref_count_base::_Decref
  0032a	90		 npad	 1
$LN2@executeCom:
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp

; 64   : }

  0032b	48 81 c4 28 01
	00 00		 add	 rsp, 296		; 00000128H
  00332	c3		 ret	 0
?executeCommand@WOPSCommandEditKnightageTrophyPoint@mu2@@MEAAXXZ ENDP ; mu2::WOPSCommandEditKnightageTrophyPoint::executeCommand
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 80
cur$2 = 84
tv201 = 88
$T3 = 92
recountTrophyPoint$4 = 96
temp$5 = 100
temp$6 = 104
knightagePtr$7 = 112
info$8 = 128
$T9 = 136
$T10 = 144
$T11 = 152
$T12 = 160
$T13 = 168
$T14 = 176
$T15 = 184
$T16 = 192
$T17 = 200
$T18 = 208
$T19 = 216
$T20 = 224
$T21 = 232
$T22 = 240
$T23 = 248
$T24 = 256
$T25 = 264
$T26 = 272
this$ = 304
?dtor$0@?0??executeCommand@WOPSCommandEditKnightageTrophyPoint@mu2@@MEAAXXZ@4HA PROC ; `mu2::WOPSCommandEditKnightageTrophyPoint::executeCommand'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8d 4d 70	 lea	 rcx, QWORD PTR knightagePtr$7[rbp]
  0000d	e8 00 00 00 00	 call	 ??1?$shared_ptr@VKnightage@mu2@@@std@@QEAA@XZ ; std::shared_ptr<mu2::Knightage>::~shared_ptr<mu2::Knightage>
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$0@?0??executeCommand@WOPSCommandEditKnightageTrophyPoint@mu2@@MEAAXXZ@4HA ENDP ; `mu2::WOPSCommandEditKnightageTrophyPoint::executeCommand'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Mu2Common\Protocol\Common.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp
; File F:\Release_Branch\Server\Development\Mu2Common\Protocol\Common.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp
; File F:\Release_Branch\Server\Development\Mu2Common\Protocol\Common.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp
;	COMDAT ?parse@WOPSCommandEditKnightageTrophyPoint@mu2@@MEAA_NAEBUstWOPSCommand@2@@Z
_TEXT	SEGMENT
$T1 = 96
$T2 = 100
_My_data$3 = 104
hConsole$4 = 112
$T5 = 120
$T6 = 128
$T7 = 136
$T8 = 144
$T9 = 152
$T10 = 160
this$ = 192
command$ = 200
?parse@WOPSCommandEditKnightageTrophyPoint@mu2@@MEAA_NAEBUstWOPSCommand@2@@Z PROC ; mu2::WOPSCommandEditKnightageTrophyPoint::parse, COMDAT

; 67   : {

$LN101:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 81 ec b8 00
	00 00		 sub	 rsp, 184		; 000000b8H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1914 :         auto& _My_data = _Mypair._Myval2;

  00011	48 8b 84 24 c8
	00 00 00	 mov	 rax, QWORD PTR command$[rsp]
  00019	48 83 c0 38	 add	 rax, 56			; 00000038H
  0001d	48 89 44 24 68	 mov	 QWORD PTR _My_data$3[rsp], rax

; 1915 :         return static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst);

  00022	48 8b 44 24 68	 mov	 rax, QWORD PTR _My_data$3[rsp]
  00027	48 8b 4c 24 68	 mov	 rcx, QWORD PTR _My_data$3[rsp]
  0002c	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0002f	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00033	48 2b c1	 sub	 rax, rcx
  00036	48 c1 f8 05	 sar	 rax, 5
  0003a	48 89 44 24 78	 mov	 QWORD PTR $T5[rsp], rax
; File F:\Release_Branch\Server\Development\Mu2Common\Protocol\Common.h

; 132  : 			return values.size();

  0003f	48 8b 44 24 78	 mov	 rax, QWORD PTR $T5[rsp]
  00044	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T6[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp

; 68   : 	VERIFY_RETURN(2 <= command.GetValueCount(), false);

  0004c	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR $T6[rsp]
  00054	48 83 f8 02	 cmp	 rax, 2
  00058	0f 83 a0 00 00
	00		 jae	 $LN2@parse
  0005e	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  00063	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  00069	48 89 44 24 70	 mov	 QWORD PTR hConsole$4[rsp], rax
  0006e	66 ba 0d 00	 mov	 dx, 13
  00072	48 8b 4c 24 70	 mov	 rcx, QWORD PTR hConsole$4[rsp]
  00077	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  0007d	c7 44 24 50 44
	00 00 00	 mov	 DWORD PTR [rsp+80], 68	; 00000044H
  00085	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0GG@MDKIIJBC@F?3?2Release_Branch?2Server?2Develo@
  0008c	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  00091	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BN@ICLBKJID@2?5?$DM?$DN?5command?4GetValueCount?$CI?$CJ@
  00098	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  0009d	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0DA@JAKJJCN@mu2?3?3WOPSCommandEditKnightageTr@
  000a4	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  000a9	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  000b0	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  000b5	c7 44 24 28 44
	00 00 00	 mov	 DWORD PTR [rsp+40], 68	; 00000044H
  000bd	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0GG@MDKIIJBC@F?3?2Release_Branch?2Server?2Develo@
  000c4	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  000c9	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0DA@JAKJJCN@mu2?3?3WOPSCommandEditKnightageTr@
  000d0	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  000d6	ba 02 00 00 00	 mov	 edx, 2
  000db	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  000e2	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  000e7	66 ba 07 00	 mov	 dx, 7
  000eb	48 8b 4c 24 70	 mov	 rcx, QWORD PTR hConsole$4[rsp]
  000f0	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000f6	90		 npad	 1
  000f7	32 c0		 xor	 al, al
  000f9	e9 b0 00 00 00	 jmp	 $LN1@parse
$LN2@parse:
; File F:\Release_Branch\Server\Development\Mu2Common\Protocol\Common.h

; 203  : 			return static_cast<Int64>(_wtoi64(getValue(arryIndex).c_str()));

  000fe	33 d2		 xor	 edx, edx
  00100	48 8b 8c 24 c8
	00 00 00	 mov	 rcx, QWORD PTR command$[rsp]
  00108	e8 00 00 00 00	 call	 ?getValue@stWOPSCommand@mu2@@AEBAAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@H@Z ; mu2::stWOPSCommand::getValue
  0010d	48 8b c8	 mov	 rcx, rax
  00110	e8 00 00 00 00	 call	 ?c_str@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBAPEB_WXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::c_str
  00115	48 8b c8	 mov	 rcx, rax
  00118	e8 00 00 00 00	 call	 _wtoi64
  0011d	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T7[rsp], rax

; 180  : 			return static_cast<T>(val2Int64(arryIndex)); 

  00125	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T7[rsp]
  0012d	89 44 24 60	 mov	 DWORD PTR $T1[rsp], eax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3453 :         return _Mypair._Myval2;

  00131	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00139	48 8b 40 40	 mov	 rax, QWORD PTR [rax+64]
  0013d	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T8[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp

; 70   : 	m_impl->knightageId = command.Val<UInt32>(0);

  00145	8b 44 24 60	 mov	 eax, DWORD PTR $T1[rsp]
  00149	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR $T8[rsp]
  00151	89 01		 mov	 DWORD PTR [rcx], eax
; File F:\Release_Branch\Server\Development\Mu2Common\Protocol\Common.h

; 203  : 			return static_cast<Int64>(_wtoi64(getValue(arryIndex).c_str()));

  00153	ba 01 00 00 00	 mov	 edx, 1
  00158	48 8b 8c 24 c8
	00 00 00	 mov	 rcx, QWORD PTR command$[rsp]
  00160	e8 00 00 00 00	 call	 ?getValue@stWOPSCommand@mu2@@AEBAAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@H@Z ; mu2::stWOPSCommand::getValue
  00165	48 8b c8	 mov	 rcx, rax
  00168	e8 00 00 00 00	 call	 ?c_str@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBAPEB_WXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::c_str
  0016d	48 8b c8	 mov	 rcx, rax
  00170	e8 00 00 00 00	 call	 _wtoi64
  00175	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax

; 180  : 			return static_cast<T>(val2Int64(arryIndex)); 

  0017d	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR $T9[rsp]
  00185	89 44 24 64	 mov	 DWORD PTR $T2[rsp], eax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3453 :         return _Mypair._Myval2;

  00189	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00191	48 8b 40 40	 mov	 rax, QWORD PTR [rax+64]
  00195	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp

; 71   : 	m_impl->lastPoint = command.Val<Int32>(1);

  0019d	8b 44 24 64	 mov	 eax, DWORD PTR $T2[rsp]
  001a1	48 8b 8c 24 a0
	00 00 00	 mov	 rcx, QWORD PTR $T10[rsp]
  001a9	89 41 04	 mov	 DWORD PTR [rcx+4], eax

; 72   : 	return true;

  001ac	b0 01		 mov	 al, 1
$LN1@parse:

; 73   : }

  001ae	48 81 c4 b8 00
	00 00		 add	 rsp, 184		; 000000b8H
  001b5	c3		 ret	 0
?parse@WOPSCommandEditKnightageTrophyPoint@mu2@@MEAA_NAEBUstWOPSCommand@2@@Z ENDP ; mu2::WOPSCommandEditKnightageTrophyPoint::parse
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp
;	COMDAT ?OnEnter@WOPSCommandEditKnightageTrophyPoint@mu2@@UEBAXPEAVEntityPlayer@2@@Z
_TEXT	SEGMENT
this$ = 8
player$ = 16
?OnEnter@WOPSCommandEditKnightageTrophyPoint@mu2@@UEBAXPEAVEntityPlayer@2@@Z PROC ; mu2::WOPSCommandEditKnightageTrophyPoint::OnEnter, COMDAT

; 30   : {

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 31   : 	UNREFERENCED_PARAMETER( player );
; 32   : }

  0000a	c3		 ret	 0
?OnEnter@WOPSCommandEditKnightageTrophyPoint@mu2@@UEBAXPEAVEntityPlayer@2@@Z ENDP ; mu2::WOPSCommandEditKnightageTrophyPoint::OnEnter
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp
;	COMDAT ??1WOPSCommandEditKnightageTrophyPoint@mu2@@UEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1WOPSCommandEditKnightageTrophyPoint@mu2@@UEAA@XZ PROC ; mu2::WOPSCommandEditKnightageTrophyPoint::~WOPSCommandEditKnightageTrophyPoint, COMDAT

; 26   : {

$LN25:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7WOPSCommandEditKnightageTrophyPoint@mu2@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx

; 27   : }

  00018	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 83 c0 40	 add	 rax, 64			; 00000040H
  00021	48 8b c8	 mov	 rcx, rax
  00024	e8 00 00 00 00	 call	 ??1?$unique_ptr@UWOPSEditKnightageTrophyPointImpl@mu2@@U?$default_delete@UWOPSEditKnightageTrophyPointImpl@mu2@@@std@@@std@@QEAA@XZ ; std::unique_ptr<mu2::WOPSEditKnightageTrophyPointImpl,std::default_delete<mu2::WOPSEditKnightageTrophyPointImpl> >::~unique_ptr<mu2::WOPSEditKnightageTrophyPointImpl,std::default_delete<mu2::WOPSEditKnightageTrophyPointImpl> >
  00029	90		 npad	 1
  0002a	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0002e	c3		 ret	 0
??1WOPSCommandEditKnightageTrophyPoint@mu2@@UEAA@XZ ENDP ; mu2::WOPSCommandEditKnightageTrophyPoint::~WOPSCommandEditKnightageTrophyPoint
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp
;	COMDAT ??0WOPSCommandEditKnightageTrophyPoint@mu2@@QEAA@XZ
_TEXT	SEGMENT
$T1 = 32
this$ = 40
_Ptr$ = 48
this$ = 56
$T2 = 64
this$ = 96
??0WOPSCommandEditKnightageTrophyPoint@mu2@@QEAA@XZ PROC ; mu2::WOPSCommandEditKnightageTrophyPoint::WOPSCommandEditKnightageTrophyPoint, COMDAT

; 22   : {

$LN20:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 20   : 	: WOPSCommand(ACTIONCODE)

  00009	66 ba 0f 00	 mov	 dx, 15
  0000d	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  00012	e8 00 00 00 00	 call	 ??0WOPSCommand@mu2@@IEAA@W4Enum@WOPSCommandCode@1@@Z ; mu2::WOPSCommand::WOPSCommand
  00017	90		 npad	 1

; 22   : {

  00018	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7WOPSCommandEditKnightageTrophyPoint@mu2@@6B@
  00024	48 89 08	 mov	 QWORD PTR [rax], rcx

; 21   : 	, m_impl( new WOPSEditKnightageTrophyPointImpl)

  00027	b9 08 00 00 00	 mov	 ecx, 8
  0002c	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00031	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
  00036	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  0003b	48 89 44 24 30	 mov	 QWORD PTR _Ptr$[rsp], rax
  00040	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00045	48 83 c0 40	 add	 rax, 64			; 00000040H
  00049	48 89 44 24 28	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3370 :     _CONSTEXPR23 explicit unique_ptr(pointer _Ptr) noexcept : _Mypair(_Zero_then_variadic_args_t{}, _Ptr) {}

  0004e	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 89 44 24 38	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00058	48 8d 44 24 30	 lea	 rax, QWORD PTR _Ptr$[rsp]
  0005d	48 89 44 24 40	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1531 :         : _Ty1(), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  00062	48 8b 44 24 38	 mov	 rax, QWORD PTR this$[rsp]
  00067	48 8b 4c 24 40	 mov	 rcx, QWORD PTR $T2[rsp]
  0006c	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0006f	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp

; 23   : }

  00072	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00077	48 83 c4 58	 add	 rsp, 88			; 00000058H
  0007b	c3		 ret	 0
??0WOPSCommandEditKnightageTrophyPoint@mu2@@QEAA@XZ ENDP ; mu2::WOPSCommandEditKnightageTrophyPoint::WOPSCommandEditKnightageTrophyPoint
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
this$ = 40
_Ptr$ = 48
this$ = 56
$T2 = 64
this$ = 96
?dtor$0@?0???0WOPSCommandEditKnightageTrophyPoint@mu2@@QEAA@XZ@4HA PROC ; `mu2::WOPSCommandEditKnightageTrophyPoint::WOPSCommandEditKnightageTrophyPoint'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 60	 mov	 rcx, QWORD PTR this$[rbp]
  0000d	e8 00 00 00 00	 call	 ??1WOPSCommand@mu2@@UEAA@XZ ; mu2::WOPSCommand::~WOPSCommand
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$0@?0???0WOPSCommandEditKnightageTrophyPoint@mu2@@QEAA@XZ@4HA ENDP ; `mu2::WOPSCommandEditKnightageTrophyPoint::WOPSCommandEditKnightageTrophyPoint'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
_TEXT	SEGMENT
_Ptr_container$ = 48
_Block_size$ = 56
_Ptr$ = 64
$T1 = 72
_Bytes$ = 96
??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z PROC ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>, COMDAT

; 182  : __declspec(allocator) void* _Allocate_manually_vector_aligned(const size_t _Bytes) {

$LN7:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 183  :     // allocate _Bytes manually aligned to at least _Big_allocation_alignment
; 184  :     const size_t _Block_size = _Non_user_size + _Bytes;

  00009	48 8b 44 24 60	 mov	 rax, QWORD PTR _Bytes$[rsp]
  0000e	48 83 c0 27	 add	 rax, 39			; 00000027H
  00012	48 89 44 24 38	 mov	 QWORD PTR _Block_size$[rsp], rax

; 185  :     if (_Block_size <= _Bytes) {

  00017	48 8b 44 24 60	 mov	 rax, QWORD PTR _Bytes$[rsp]
  0001c	48 39 44 24 38	 cmp	 QWORD PTR _Block_size$[rsp], rax
  00021	77 06		 ja	 SHORT $LN2@Allocate_m

; 186  :         _Throw_bad_array_new_length(); // add overflow

  00023	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00028	90		 npad	 1
$LN2@Allocate_m:

; 136  :         return ::operator new(_Bytes);

  00029	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Block_size$[rsp]
  0002e	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00033	48 89 44 24 48	 mov	 QWORD PTR $T1[rsp], rax

; 187  :     }
; 188  : 
; 189  :     const uintptr_t _Ptr_container = reinterpret_cast<uintptr_t>(_Traits::_Allocate(_Block_size));

  00038	48 8b 44 24 48	 mov	 rax, QWORD PTR $T1[rsp]
  0003d	48 89 44 24 30	 mov	 QWORD PTR _Ptr_container$[rsp], rax

; 190  :     _STL_VERIFY(_Ptr_container != 0, "invalid argument"); // validate even in release since we're doing p[-1]

  00042	48 83 7c 24 30
	00		 cmp	 QWORD PTR _Ptr_container$[rsp], 0
  00048	75 19		 jne	 SHORT $LN3@Allocate_m
  0004a	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR [rsp+32], 0
  00053	45 33 c9	 xor	 r9d, r9d
  00056	45 33 c0	 xor	 r8d, r8d
  00059	33 d2		 xor	 edx, edx
  0005b	33 c9		 xor	 ecx, ecx
  0005d	e8 00 00 00 00	 call	 _invoke_watson
  00062	90		 npad	 1
$LN3@Allocate_m:

; 191  :     void* const _Ptr = reinterpret_cast<void*>((_Ptr_container + _Non_user_size) & ~(_Big_allocation_alignment - 1));

  00063	48 8b 44 24 30	 mov	 rax, QWORD PTR _Ptr_container$[rsp]
  00068	48 83 c0 27	 add	 rax, 39			; 00000027H
  0006c	48 83 e0 e0	 and	 rax, -32		; ffffffffffffffe0H
  00070	48 89 44 24 40	 mov	 QWORD PTR _Ptr$[rsp], rax

; 192  :     static_cast<uintptr_t*>(_Ptr)[-1] = _Ptr_container;

  00075	b8 08 00 00 00	 mov	 eax, 8
  0007a	48 6b c0 ff	 imul	 rax, rax, -1
  0007e	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  00083	48 8b 54 24 30	 mov	 rdx, QWORD PTR _Ptr_container$[rsp]
  00088	48 89 14 01	 mov	 QWORD PTR [rcx+rax], rdx

; 193  : 
; 194  : #ifdef _DEBUG
; 195  :     static_cast<uintptr_t*>(_Ptr)[-2] = _Big_allocation_sentinel;
; 196  : #endif // defined(_DEBUG)
; 197  :     return _Ptr;

  0008c	48 8b 44 24 40	 mov	 rax, QWORD PTR _Ptr$[rsp]
$LN4@Allocate_m:

; 198  : }

  00091	48 83 c4 58	 add	 rsp, 88			; 00000058H
  00095	c3		 ret	 0
??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ENDP ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??$_Construct@$00PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z
_TEXT	SEGMENT
$T1 = 32
$S26$ = 33
$T2 = 34
$T3 = 36
_My_data$ = 40
_New_capacity$ = 48
_Max$ = 56
_Masked$4 = 64
$T5 = 72
_New_ptr$ = 80
$T6 = 88
tv170 = 96
_Fancy_ptr$7 = 104
$T8 = 112
$T9 = 120
_First1$ = 128
$T10 = 136
$T11 = 144
_Al$ = 152
$T12 = 160
$T13 = 168
$T14 = 176
$T15 = 184
$T16 = 192
$T17 = 200
_Ptr$ = 208
$T18 = 216
_First1$ = 224
_Ptr$ = 232
$T19 = 240
_Alproxy$ = 248
this$ = 272
_Arg$ = 280
_Count$ = 288
??$_Construct@$00PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<1,wchar_t const *>, COMDAT

; 871  :     _CONSTEXPR20 void _Construct(const _Char_or_ptr _Arg, _CRT_GUARDOVERFLOW const size_type _Count) {

$LN188:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	57		 push	 rdi
  00010	48 81 ec 00 01
	00 00		 sub	 rsp, 256		; 00000100H

; 872  :         auto& _My_data = _Mypair._Myval2;

  00017	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0001f	48 89 44 24 28	 mov	 QWORD PTR _My_data$[rsp], rax

; 873  :         _STL_INTERNAL_CHECK(!_My_data._Large_mode_engaged());
; 874  : 
; 875  :         if constexpr (_Strat == _Construct_strategy::_From_char) {
; 876  :             _STL_INTERNAL_STATIC_ASSERT(is_same_v<_Char_or_ptr, _Elem>);
; 877  :         } else {
; 878  :             _STL_INTERNAL_STATIC_ASSERT(_Is_elem_cptr<_Char_or_ptr>::value);
; 879  :         }
; 880  : 
; 881  :         if (_Count > max_size()) {

  00024	48 8b 8c 24 10
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0002c	e8 00 00 00 00	 call	 ?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
  00031	48 39 84 24 20
	01 00 00	 cmp	 QWORD PTR _Count$[rsp], rax
  00039	76 06		 jbe	 SHORT $LN2@Construct

; 882  :             _Xlen_string(); // result too long

  0003b	e8 00 00 00 00	 call	 ?_Xlen_string@std@@YAXXZ ; std::_Xlen_string
  00040	90		 npad	 1
$LN2@Construct:

; 3107 :         return _Mypair._Get_first();

  00041	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00049	48 89 44 24 70	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3107 :         return _Mypair._Get_first();

  0004e	48 8b 44 24 70	 mov	 rax, QWORD PTR $T8[rsp]
  00053	48 89 44 24 78	 mov	 QWORD PTR $T9[rsp], rax

; 883  :         }
; 884  : 
; 885  :         auto& _Al       = _Getal();

  00058	48 8b 44 24 78	 mov	 rax, QWORD PTR $T9[rsp]
  0005d	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR _Al$[rsp], rax

; 886  :         auto&& _Alproxy = _GET_PROXY_ALLOCATOR(_Alty, _Al);

  00065	48 8d 44 24 20	 lea	 rax, QWORD PTR $T1[rsp]
  0006a	48 8b f8	 mov	 rdi, rax
  0006d	33 c0		 xor	 eax, eax
  0006f	b9 01 00 00 00	 mov	 ecx, 1
  00074	f3 aa		 rep stosb
  00076	48 8d 44 24 21	 lea	 rax, QWORD PTR $S26$[rsp]
  0007b	48 89 84 24 f8
	00 00 00	 mov	 QWORD PTR _Alproxy$[rsp], rax

; 887  :         _Container_proxy_ptr<_Alty> _Proxy(_Alproxy, _My_data);
; 888  : 
; 889  :         if (_Count <= _Small_string_capacity) {

  00083	48 83 bc 24 20
	01 00 00 07	 cmp	 QWORD PTR _Count$[rsp], 7
  0008c	77 71		 ja	 SHORT $LN3@Construct

; 890  :             _My_data._Mysize = _Count;

  0008e	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00093	48 8b 8c 24 20
	01 00 00	 mov	 rcx, QWORD PTR _Count$[rsp]
  0009b	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 891  :             _My_data._Myres  = _Small_string_capacity;

  0009f	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000a4	48 c7 40 18 07
	00 00 00	 mov	 QWORD PTR [rax+24], 7

; 892  : 
; 893  :             if constexpr (_Strat == _Construct_strategy::_From_char) {
; 894  :                 _Traits::assign(_My_data._Bx._Buf, _Count, _Arg);
; 895  :                 _Traits::assign(_My_data._Bx._Buf[_Count], _Elem());
; 896  :             } else if constexpr (_Strat == _Construct_strategy::_From_ptr) {
; 897  :                 _Traits::copy(_My_data._Bx._Buf, _Arg, _Count);

  000ac	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000b1	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _First1$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  000b9	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  000c1	48 03 c0	 add	 rax, rax
  000c4	4c 8b c0	 mov	 r8, rax
  000c7	48 8b 94 24 18
	01 00 00	 mov	 rdx, QWORD PTR _Arg$[rsp]
  000cf	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR _First1$[rsp]
  000d7	e8 00 00 00 00	 call	 memcpy
  000dc	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 898  :                 _Traits::assign(_My_data._Bx._Buf[_Count], _Elem());

  000dd	33 c0		 xor	 eax, eax
  000df	66 89 44 24 22	 mov	 WORD PTR $T2[rsp], ax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 347  :         _Left = _Right;

  000e4	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000e9	48 8b 8c 24 20
	01 00 00	 mov	 rcx, QWORD PTR _Count$[rsp]
  000f1	0f b7 54 24 22	 movzx	 edx, WORD PTR $T2[rsp]
  000f6	66 89 14 48	 mov	 WORD PTR [rax+rcx*2], dx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 908  :             return;

  000fa	e9 3c 02 00 00	 jmp	 $LN4@Construct
$LN3@Construct:

; 909  :         }
; 910  : 
; 911  :         size_type _New_capacity = _Calculate_growth(_Count, _Small_string_capacity, max_size());

  000ff	48 8b 8c 24 10
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00107	e8 00 00 00 00	 call	 ?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
  0010c	48 89 44 24 38	 mov	 QWORD PTR _Max$[rsp], rax

; 2978 :         const size_type _Masked = _Requested | _Alloc_mask;

  00111	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  00119	48 83 c8 07	 or	 rax, 7
  0011d	48 89 44 24 40	 mov	 QWORD PTR _Masked$4[rsp], rax

; 2979 :         if (_Masked > _Max) { // the mask overflows, settle for max_size()

  00122	48 8b 44 24 38	 mov	 rax, QWORD PTR _Max$[rsp]
  00127	48 39 44 24 40	 cmp	 QWORD PTR _Masked$4[rsp], rax
  0012c	76 0f		 jbe	 SHORT $LN114@Construct

; 2980 :             return _Max;

  0012e	48 8b 44 24 38	 mov	 rax, QWORD PTR _Max$[rsp]
  00133	48 89 44 24 48	 mov	 QWORD PTR $T5[rsp], rax
  00138	e9 93 00 00 00	 jmp	 $LN113@Construct
$LN114@Construct:

; 2981 :         }
; 2982 : 
; 2983 :         if (_Old > _Max - _Old / 2) { // similarly, geometric overflows

  0013d	33 d2		 xor	 edx, edx
  0013f	b8 07 00 00 00	 mov	 eax, 7
  00144	b9 02 00 00 00	 mov	 ecx, 2
  00149	48 f7 f1	 div	 rcx
  0014c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Max$[rsp]
  00151	48 2b c8	 sub	 rcx, rax
  00154	48 8b c1	 mov	 rax, rcx
  00157	48 83 f8 07	 cmp	 rax, 7
  0015b	73 0c		 jae	 SHORT $LN115@Construct

; 2984 :             return _Max;

  0015d	48 8b 44 24 38	 mov	 rax, QWORD PTR _Max$[rsp]
  00162	48 89 44 24 48	 mov	 QWORD PTR $T5[rsp], rax
  00167	eb 67		 jmp	 SHORT $LN113@Construct
$LN115@Construct:

; 2987 :         return (_STD max)(_Masked, _Old + _Old / 2);

  00169	33 d2		 xor	 edx, edx
  0016b	b8 07 00 00 00	 mov	 eax, 7
  00170	b9 02 00 00 00	 mov	 ecx, 2
  00175	48 f7 f1	 div	 rcx
  00178	48 83 c0 07	 add	 rax, 7
  0017c	48 89 44 24 58	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 77   :     return _Left < _Right ? _Right : _Left;

  00181	48 8b 44 24 58	 mov	 rax, QWORD PTR $T6[rsp]
  00186	48 39 44 24 40	 cmp	 QWORD PTR _Masked$4[rsp], rax
  0018b	73 0c		 jae	 SHORT $LN122@Construct
  0018d	48 8d 44 24 58	 lea	 rax, QWORD PTR $T6[rsp]
  00192	48 89 44 24 60	 mov	 QWORD PTR tv170[rsp], rax
  00197	eb 0a		 jmp	 SHORT $LN123@Construct
$LN122@Construct:
  00199	48 8d 44 24 40	 lea	 rax, QWORD PTR _Masked$4[rsp]
  0019e	48 89 44 24 60	 mov	 QWORD PTR tv170[rsp], rax
$LN123@Construct:
  001a3	48 8b 44 24 60	 mov	 rax, QWORD PTR tv170[rsp]
  001a8	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
  001b0	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  001b8	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2987 :         return (_STD max)(_Masked, _Old + _Old / 2);

  001c0	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  001c8	48 8b 00	 mov	 rax, QWORD PTR [rax]
  001cb	48 89 44 24 48	 mov	 QWORD PTR $T5[rsp], rax
$LN113@Construct:

; 909  :         }
; 910  : 
; 911  :         size_type _New_capacity = _Calculate_growth(_Count, _Small_string_capacity, max_size());

  001d0	48 8b 44 24 48	 mov	 rax, QWORD PTR $T5[rsp]
  001d5	48 89 44 24 30	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 825  :         ++_Capacity; // Take null terminator into consideration

  001da	48 8b 44 24 30	 mov	 rax, QWORD PTR _New_capacity$[rsp]
  001df	48 ff c0	 inc	 rax
  001e2	48 89 44 24 30	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 826  : 
; 827  :         pointer _Fancy_ptr = nullptr;

  001e7	48 c7 44 24 68
	00 00 00 00	 mov	 QWORD PTR _Fancy_ptr$7[rsp], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 2303 :         return _Al.allocate(_Count);

  001f0	48 8b 54 24 30	 mov	 rdx, QWORD PTR _New_capacity$[rsp]
  001f5	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR _Al$[rsp]
  001fd	e8 00 00 00 00	 call	 ?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z ; std::allocator<wchar_t>::allocate
  00202	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 829  :             _Fancy_ptr = _Allocate_at_least_helper(_Al, _Capacity);

  0020a	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
  00212	48 89 44 24 68	 mov	 QWORD PTR _Fancy_ptr$7[rsp], rax

; 830  :         } else {
; 831  :             _STL_INTERNAL_STATIC_ASSERT(_Policy == _Allocation_policy::_Exactly);
; 832  :             _Fancy_ptr = _Al.allocate(_Capacity);
; 833  :         }
; 834  : 
; 835  : #if _HAS_CXX20
; 836  :         // Start element lifetimes to avoid UB. This is a more general mechanism than _String_val::_Activate_SSO_buffer,
; 837  :         // but likely more impactful to throughput.
; 838  :         if (_STD is_constant_evaluated()) {
; 839  :             _Elem* const _Ptr = _Unfancy(_Fancy_ptr);
; 840  :             for (size_type _Idx = 0; _Idx < _Capacity; ++_Idx) {
; 841  :                 _STD construct_at(_Ptr + _Idx);
; 842  :             }
; 843  :         }
; 844  : #endif // _HAS_CXX20
; 845  :         --_Capacity;

  00217	48 8b 44 24 30	 mov	 rax, QWORD PTR _New_capacity$[rsp]
  0021c	48 ff c8	 dec	 rax
  0021f	48 89 44 24 30	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 846  :         return _Fancy_ptr;

  00224	48 8b 44 24 68	 mov	 rax, QWORD PTR _Fancy_ptr$7[rsp]
  00229	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax

; 912  :         const pointer _New_ptr  = _Allocate_for_capacity(_Al, _New_capacity); // throws

  00231	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T13[rsp]
  00239	48 89 44 24 50	 mov	 QWORD PTR _New_ptr$[rsp], rax

; 913  :         _Construct_in_place(_My_data._Bx._Ptr, _New_ptr);

  0023e	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00243	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T14[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0024b	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR $T14[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  00253	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T15[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0025b	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T15[rsp]
  00263	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T16[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  0026b	48 8d 44 24 50	 lea	 rax, QWORD PTR _New_ptr$[rsp]
  00270	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T17[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00278	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR $T16[rsp]
  00280	48 8b 8c 24 c8
	00 00 00	 mov	 rcx, QWORD PTR $T17[rsp]
  00288	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0028b	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 915  :         _My_data._Mysize = _Count;

  0028e	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00293	48 8b 8c 24 20
	01 00 00	 mov	 rcx, QWORD PTR _Count$[rsp]
  0029b	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 916  :         _My_data._Myres  = _New_capacity;

  0029f	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  002a4	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _New_capacity$[rsp]
  002a9	48 89 48 18	 mov	 QWORD PTR [rax+24], rcx

; 921  :             _Traits::copy(_Unfancy(_New_ptr), _Arg, _Count);

  002ad	48 8b 44 24 50	 mov	 rax, QWORD PTR _New_ptr$[rsp]
  002b2	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  002ba	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR _Ptr$[rsp]
  002c2	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR $T18[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 921  :             _Traits::copy(_Unfancy(_New_ptr), _Arg, _Count);

  002ca	48 8b 84 24 d8
	00 00 00	 mov	 rax, QWORD PTR $T18[rsp]
  002d2	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR _First1$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  002da	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  002e2	48 03 c0	 add	 rax, rax
  002e5	4c 8b c0	 mov	 r8, rax
  002e8	48 8b 94 24 18
	01 00 00	 mov	 rdx, QWORD PTR _Arg$[rsp]
  002f0	48 8b 8c 24 e0
	00 00 00	 mov	 rcx, QWORD PTR _First1$[rsp]
  002f8	e8 00 00 00 00	 call	 memcpy
  002fd	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 922  :             _Traits::assign(_Unfancy(_New_ptr)[_Count], _Elem());

  002fe	33 c0		 xor	 eax, eax
  00300	66 89 44 24 24	 mov	 WORD PTR $T3[rsp], ax
  00305	48 8b 44 24 50	 mov	 rax, QWORD PTR _New_ptr$[rsp]
  0030a	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  00312	48 8b 84 24 e8
	00 00 00	 mov	 rax, QWORD PTR _Ptr$[rsp]
  0031a	48 89 84 24 f0
	00 00 00	 mov	 QWORD PTR $T19[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 347  :         _Left = _Right;

  00322	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR $T19[rsp]
  0032a	48 8b 8c 24 20
	01 00 00	 mov	 rcx, QWORD PTR _Count$[rsp]
  00332	0f b7 54 24 24	 movzx	 edx, WORD PTR $T3[rsp]
  00337	66 89 14 48	 mov	 WORD PTR [rax+rcx*2], dx
$LN4@Construct:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 929  :     }

  0033b	48 81 c4 00 01
	00 00		 add	 rsp, 256		; 00000100H
  00342	5f		 pop	 rdi
  00343	c3		 ret	 0
??$_Construct@$00PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<1,wchar_t const *>
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEvent.h
;	COMDAT ?ToString@WOPSCommand@mu2@@UEBA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ
_TEXT	SEGMENT
$T1 = 32
this$ = 64
__$ReturnUdt$ = 72
?ToString@WOPSCommand@mu2@@UEBA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ PROC ; mu2::WOPSCommand::ToString, COMDAT

; 23   : 	virtual std::wstring	ToString() const { return L""; }		// 

$LN225:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 38	 sub	 rsp, 56			; 00000038H
  0000e	c7 44 24 20 00
	00 00 00	 mov	 DWORD PTR $T1[rsp], 0
  00016	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:??_C@_11LOCGONAA@@
  0001d	48 8b 4c 24 48	 mov	 rcx, QWORD PTR __$ReturnUdt$[rsp]
  00022	e8 00 00 00 00	 call	 ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  00027	8b 44 24 20	 mov	 eax, DWORD PTR $T1[rsp]
  0002b	83 c8 01	 or	 eax, 1
  0002e	89 44 24 20	 mov	 DWORD PTR $T1[rsp], eax
  00032	48 8b 44 24 48	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]
  00037	48 83 c4 38	 add	 rsp, 56			; 00000038H
  0003b	c3		 ret	 0
?ToString@WOPSCommand@mu2@@UEBA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ ENDP ; mu2::WOPSCommand::ToString
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEvent.h
;	COMDAT ?IsOverlapable@WOPSCommand@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsOverlapable@WOPSCommand@mu2@@UEBA_NXZ PROC		; mu2::WOPSCommand::IsOverlapable, COMDAT

; 22   : 	virtual Bool			IsOverlapable() const { return true; }	// 중복가능한 명령어인가?

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	b0 01		 mov	 al, 1
  00007	c3		 ret	 0
?IsOverlapable@WOPSCommand@mu2@@UEBA_NXZ ENDP		; mu2::WOPSCommand::IsOverlapable
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEvent.h
;	COMDAT ?OnEnd@WOPSCommand@mu2@@UEBAXXZ
_TEXT	SEGMENT
this$ = 8
?OnEnd@WOPSCommand@mu2@@UEBAXXZ PROC			; mu2::WOPSCommand::OnEnd, COMDAT

; 20   : 	virtual void		OnEnd() const {};

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?OnEnd@WOPSCommand@mu2@@UEBAXXZ ENDP			; mu2::WOPSCommand::OnEnd
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEvent.h
;	COMDAT ?OnBegin@WOPSCommand@mu2@@UEBAXXZ
_TEXT	SEGMENT
this$ = 8
?OnBegin@WOPSCommand@mu2@@UEBAXXZ PROC			; mu2::WOPSCommand::OnBegin, COMDAT

; 19   : 	virtual void		OnBegin() const {};

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?OnBegin@WOPSCommand@mu2@@UEBAXXZ ENDP			; mu2::WOPSCommand::OnBegin
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEvent.h
;	COMDAT ??1WOPSCommand@mu2@@UEAA@XZ
_TEXT	SEGMENT
this$ = 8
??1WOPSCommand@mu2@@UEAA@XZ PROC			; mu2::WOPSCommand::~WOPSCommand, COMDAT

; 13   : 	virtual ~WOPSCommand() = default;

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
??1WOPSCommand@mu2@@UEAA@XZ ENDP			; mu2::WOPSCommand::~WOPSCommand
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Mu2Common\Protocol\Common.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Mu2Common\Protocol\Common.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Mu2Common\Protocol\Common.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Mu2Common\Protocol\Common.h
;	COMDAT ?getValue@stWOPSCommand@mu2@@AEBAAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@H@Z
_TEXT	SEGMENT
_My_data$1 = 96
hConsole$2 = 104
$T3 = 112
_My_data$4 = 120
$T5 = 128
this$ = 160
arryIndex$ = 168
?getValue@stWOPSCommand@mu2@@AEBAAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@H@Z PROC ; mu2::stWOPSCommand::getValue, COMDAT

; 196  : 		{

$LN14:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 81 ec 98 00
	00 00		 sub	 rsp, 152		; 00000098H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1914 :         auto& _My_data = _Mypair._Myval2;

  00010	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00018	48 83 c0 38	 add	 rax, 56			; 00000038H
  0001c	48 89 44 24 60	 mov	 QWORD PTR _My_data$1[rsp], rax

; 1915 :         return static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst);

  00021	48 8b 44 24 60	 mov	 rax, QWORD PTR _My_data$1[rsp]
  00026	48 8b 4c 24 60	 mov	 rcx, QWORD PTR _My_data$1[rsp]
  0002b	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0002e	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00032	48 2b c1	 sub	 rax, rcx
  00035	48 c1 f8 05	 sar	 rax, 5
  00039	48 89 44 24 70	 mov	 QWORD PTR $T3[rsp], rax
; File F:\Release_Branch\Server\Development\Mu2Common\Protocol\Common.h

; 197  : 			VERIFY_RETURN(arryIndex < values.size(), InvaldValue);

  0003e	48 63 84 24 a8
	00 00 00	 movsxd	 rax, DWORD PTR arryIndex$[rsp]
  00046	48 8b 4c 24 70	 mov	 rcx, QWORD PTR $T3[rsp]
  0004b	48 3b c1	 cmp	 rax, rcx
  0004e	0f 82 a2 00 00
	00		 jb	 $LN2@getValue
  00054	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  00059	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  0005f	48 89 44 24 68	 mov	 QWORD PTR hConsole$2[rsp], rax
  00064	66 ba 0d 00	 mov	 dx, 13
  00068	48 8b 4c 24 68	 mov	 rcx, QWORD PTR hConsole$2[rsp]
  0006d	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  00073	c7 44 24 50 c5
	00 00 00	 mov	 DWORD PTR [rsp+80], 197	; 000000c5H
  0007b	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EB@JGAFMFJE@F?3?2Release_Branch?2Server?2Develo@
  00082	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  00087	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BK@FMPHEHPK@arryIndex?5?$DM?5values?4size?$CI?$CJ@
  0008e	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  00093	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BN@FHEEOEA@mu2?3?3stWOPSCommand?3?3getValue@
  0009a	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  0009f	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  000a6	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  000ab	c7 44 24 28 c5
	00 00 00	 mov	 DWORD PTR [rsp+40], 197	; 000000c5H
  000b3	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EB@JGAFMFJE@F?3?2Release_Branch?2Server?2Develo@
  000ba	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  000bf	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0BN@FHEEOEA@mu2?3?3stWOPSCommand?3?3getValue@
  000c6	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  000cc	ba 02 00 00 00	 mov	 edx, 2
  000d1	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  000d8	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  000dd	66 ba 07 00	 mov	 dx, 7
  000e1	48 8b 4c 24 68	 mov	 rcx, QWORD PTR hConsole$2[rsp]
  000e6	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000ec	90		 npad	 1
  000ed	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?InvaldValue@stWOPSCommand@mu2@@0V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@B ; mu2::stWOPSCommand::InvaldValue
  000f4	eb 35		 jmp	 SHORT $LN1@getValue
$LN2@getValue:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1938 :         auto& _My_data = _Mypair._Myval2;

  000f6	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000fe	48 83 c0 38	 add	 rax, 56			; 00000038H
  00102	48 89 44 24 78	 mov	 QWORD PTR _My_data$4[rsp], rax
; File F:\Release_Branch\Server\Development\Mu2Common\Protocol\Common.h

; 198  : 			return values[arryIndex];

  00107	48 63 84 24 a8
	00 00 00	 movsxd	 rax, DWORD PTR arryIndex$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1944 :         return _My_data._Myfirst[_Pos];

  0010f	48 6b c0 20	 imul	 rax, rax, 32		; 00000020H
  00113	48 8b 4c 24 78	 mov	 rcx, QWORD PTR _My_data$4[rsp]
  00118	48 03 01	 add	 rax, QWORD PTR [rcx]
  0011b	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T5[rsp], rax
; File F:\Release_Branch\Server\Development\Mu2Common\Protocol\Common.h

; 198  : 			return values[arryIndex];

  00123	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR $T5[rsp]
$LN1@getValue:

; 199  : 		}

  0012b	48 81 c4 98 00
	00 00		 add	 rsp, 152		; 00000098H
  00132	c3		 ret	 0
?getValue@stWOPSCommand@mu2@@AEBAAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@H@Z ENDP ; mu2::stWOPSCommand::getValue
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ?_Decref@_Ref_count_base@std@@QEAAXXZ
_TEXT	SEGMENT
this$ = 48
?_Decref@_Ref_count_base@std@@QEAAXXZ PROC		; std::_Ref_count_base::_Decref, COMDAT

; 1158 :     void _Decref() noexcept { // decrement use count

$LN11:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 1159 :         if (_MT_DECR(_Uses) == 0) {

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 c0 08	 add	 rax, 8
  00012	b9 ff ff ff ff	 mov	 ecx, -1
  00017	f0 0f c1 08	 lock xadd DWORD PTR [rax], ecx
  0001b	ff c9		 dec	 ecx
  0001d	8b c1		 mov	 eax, ecx
  0001f	85 c0		 test	 eax, eax
  00021	75 3a		 jne	 SHORT $LN2@Decref

; 1160 :             _Destroy();

  00023	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00028	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0002b	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00030	ff 10		 call	 QWORD PTR [rax]

; 1166 :         if (_MT_DECR(_Weaks) == 0) {

  00032	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00037	48 83 c0 0c	 add	 rax, 12
  0003b	b9 ff ff ff ff	 mov	 ecx, -1
  00040	f0 0f c1 08	 lock xadd DWORD PTR [rax], ecx
  00044	ff c9		 dec	 ecx
  00046	8b c1		 mov	 eax, ecx
  00048	85 c0		 test	 eax, eax
  0004a	75 11		 jne	 SHORT $LN2@Decref

; 1167 :             _Delete_this();

  0004c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00051	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00054	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00059	ff 50 08	 call	 QWORD PTR [rax+8]
  0005c	90		 npad	 1
$LN2@Decref:

; 1161 :             _Decwref();
; 1162 :         }
; 1163 :     }

  0005d	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00061	c3		 ret	 0
?_Decref@_Ref_count_base@std@@QEAAXXZ ENDP		; std::_Ref_count_base::_Decref
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??1?$_Compressed_pair@V?$allocator@_W@std@@V?$_String_val@U?$_Simple_types@_W@std@@@2@$00@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 8
??1?$_Compressed_pair@V?$allocator@_W@std@@V?$_String_val@U?$_Simple_types@_W@std@@@2@$00@std@@QEAA@XZ PROC ; std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>::~_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>, COMDAT
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
??1?$_Compressed_pair@V?$allocator@_W@std@@V?$_String_val@U?$_Simple_types@_W@std@@@2@$00@std@@QEAA@XZ ENDP ; std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>::~_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ
_TEXT	SEGMENT
$T1 = 0
_Alloc_max$ = 8
tv66 = 16
$T2 = 24
$T3 = 32
tv70 = 40
$T4 = 48
$T5 = 56
$T6 = 64
$T7 = 72
_Storage_max$ = 80
$T8 = 88
$T9 = 96
$T10 = 104
$T11 = 112
_Unsigned_max$12 = 120
this$ = 144
?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size, COMDAT

; 2377 :     _NODISCARD _CONSTEXPR20 size_type max_size() const noexcept {

$LN38:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H

; 3111 :         return _Mypair._Get_first();

  0000c	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1543 :         return *this;

  00014	48 89 44 24 30	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3111 :         return _Mypair._Get_first();

  00019	48 8b 44 24 30	 mov	 rax, QWORD PTR $T4[rsp]
  0001e	48 89 44 24 70	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 746  :         return static_cast<size_t>(-1) / sizeof(value_type);

  00023	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  0002d	48 89 44 24 38	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2378 :         const size_type _Alloc_max   = _Alty_traits::max_size(_Getal());

  00032	48 8b 44 24 38	 mov	 rax, QWORD PTR $T5[rsp]
  00037	48 89 44 24 08	 mov	 QWORD PTR _Alloc_max$[rsp], rax

; 2379 :         const size_type _Storage_max = // can always store small string

  0003c	48 c7 04 24 08
	00 00 00	 mov	 QWORD PTR $T1[rsp], 8
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 77   :     return _Left < _Right ? _Right : _Left;

  00044	48 8b 04 24	 mov	 rax, QWORD PTR $T1[rsp]
  00048	48 39 44 24 08	 cmp	 QWORD PTR _Alloc_max$[rsp], rax
  0004d	73 0b		 jae	 SHORT $LN21@max_size
  0004f	48 8d 04 24	 lea	 rax, QWORD PTR $T1[rsp]
  00053	48 89 44 24 10	 mov	 QWORD PTR tv66[rsp], rax
  00058	eb 0a		 jmp	 SHORT $LN22@max_size
$LN21@max_size:
  0005a	48 8d 44 24 08	 lea	 rax, QWORD PTR _Alloc_max$[rsp]
  0005f	48 89 44 24 10	 mov	 QWORD PTR tv66[rsp], rax
$LN22@max_size:
  00064	48 8b 44 24 10	 mov	 rax, QWORD PTR tv66[rsp]
  00069	48 89 44 24 40	 mov	 QWORD PTR $T6[rsp], rax
  0006e	48 8b 44 24 40	 mov	 rax, QWORD PTR $T6[rsp]
  00073	48 89 44 24 48	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2379 :         const size_type _Storage_max = // can always store small string

  00078	48 8b 44 24 48	 mov	 rax, QWORD PTR $T7[rsp]
  0007d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00080	48 89 44 24 50	 mov	 QWORD PTR _Storage_max$[rsp], rax

; 2381 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()),

  00085	48 8b 44 24 50	 mov	 rax, QWORD PTR _Storage_max$[rsp]
  0008a	48 ff c8	 dec	 rax
  0008d	48 89 44 24 18	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 866  :         constexpr auto _Unsigned_max = static_cast<make_unsigned_t<_Ty>>(-1);

  00092	48 c7 44 24 78
	ff ff ff ff	 mov	 QWORD PTR _Unsigned_max$12[rsp], -1

; 867  :         return static_cast<_Ty>(_Unsigned_max >> 1);

  0009b	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  000a5	48 89 44 24 58	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2381 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()),

  000aa	48 8b 44 24 58	 mov	 rax, QWORD PTR $T8[rsp]
  000af	48 89 44 24 20	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 101  :     return _Right < _Left ? _Right : _Left;

  000b4	48 8b 44 24 20	 mov	 rax, QWORD PTR $T3[rsp]
  000b9	48 39 44 24 18	 cmp	 QWORD PTR $T2[rsp], rax
  000be	73 0c		 jae	 SHORT $LN33@max_size
  000c0	48 8d 44 24 18	 lea	 rax, QWORD PTR $T2[rsp]
  000c5	48 89 44 24 28	 mov	 QWORD PTR tv70[rsp], rax
  000ca	eb 0a		 jmp	 SHORT $LN34@max_size
$LN33@max_size:
  000cc	48 8d 44 24 20	 lea	 rax, QWORD PTR $T3[rsp]
  000d1	48 89 44 24 28	 mov	 QWORD PTR tv70[rsp], rax
$LN34@max_size:
  000d6	48 8b 44 24 28	 mov	 rax, QWORD PTR tv70[rsp]
  000db	48 89 44 24 60	 mov	 QWORD PTR $T9[rsp], rax
  000e0	48 8b 44 24 60	 mov	 rax, QWORD PTR $T9[rsp]
  000e5	48 89 44 24 68	 mov	 QWORD PTR $T10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2381 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()),

  000ea	48 8b 44 24 68	 mov	 rax, QWORD PTR $T10[rsp]
  000ef	48 8b 00	 mov	 rax, QWORD PTR [rax]

; 2382 :             _Storage_max - 1 // -1 is for null terminator and/or npos
; 2383 :         );
; 2384 :     }

  000f2	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  000f9	c3		 ret	 0
?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?c_str@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBAPEB_WXZ
_TEXT	SEGMENT
$T1 = 0
tv80 = 4
this$ = 8
_Result$2 = 16
_Ptr$ = 24
$T3 = 32
$T4 = 40
this$ = 64
?c_str@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBAPEB_WXZ PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::c_str, COMDAT

; 2355 :     _NODISCARD _CONSTEXPR20 _Ret_z_ const _Elem* c_str() const noexcept {

$LN22:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 2356 :         return _Mypair._Myval2._Myptr();

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 89 44 24 08	 mov	 QWORD PTR this$[rsp], rax

; 444  :         const value_type* _Result = _Bx._Buf;

  00013	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00018	48 89 44 24 10	 mov	 QWORD PTR _Result$2[rsp], rax

; 453  :         return _Myres > _Small_string_capacity;

  0001d	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00022	48 83 78 18 07	 cmp	 QWORD PTR [rax+24], 7
  00027	76 0a		 jbe	 SHORT $LN12@c_str
  00029	c7 44 24 04 01
	00 00 00	 mov	 DWORD PTR tv80[rsp], 1
  00031	eb 08		 jmp	 SHORT $LN13@c_str
$LN12@c_str:
  00033	c7 44 24 04 00
	00 00 00	 mov	 DWORD PTR tv80[rsp], 0
$LN13@c_str:
  0003b	0f b6 44 24 04	 movzx	 eax, BYTE PTR tv80[rsp]
  00040	88 04 24	 mov	 BYTE PTR $T1[rsp], al

; 445  :         if (_Large_mode_engaged()) {

  00043	0f b6 04 24	 movzx	 eax, BYTE PTR $T1[rsp]
  00047	0f b6 c0	 movzx	 eax, al
  0004a	85 c0		 test	 eax, eax
  0004c	74 21		 je	 SHORT $LN5@c_str

; 446  :             _Result = _Unfancy(_Bx._Ptr);

  0004e	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00056	48 89 44 24 18	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  0005b	48 8b 44 24 18	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00060	48 89 44 24 20	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 446  :             _Result = _Unfancy(_Bx._Ptr);

  00065	48 8b 44 24 20	 mov	 rax, QWORD PTR $T3[rsp]
  0006a	48 89 44 24 10	 mov	 QWORD PTR _Result$2[rsp], rax
$LN5@c_str:

; 447  :         }
; 448  : 
; 449  :         return _Result;

  0006f	48 8b 44 24 10	 mov	 rax, QWORD PTR _Result$2[rsp]
  00074	48 89 44 24 28	 mov	 QWORD PTR $T4[rsp], rax

; 2356 :         return _Mypair._Myval2._Myptr();

  00079	48 8b 44 24 28	 mov	 rax, QWORD PTR $T4[rsp]

; 2357 :     }

  0007e	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00082	c3		 ret	 0
?c_str@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBAPEB_WXZ ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::c_str
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z
_TEXT	SEGMENT
this$ = 32
this$ = 40
this$ = 48
$T1 = 56
$T2 = 64
this$ = 96
_Ptr$ = 104
??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >, COMDAT

; 768  :     _CONSTEXPR20 basic_string(_In_z_ const _Elem* const _Ptr) : _Mypair(_Zero_then_variadic_args_t{}) {

$LN221:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 50	 sub	 rsp, 80			; 00000050H
  0000f	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 89 44 24 30	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1531 :         : _Ty1(), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  00019	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001e	48 89 44 24 20	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 402  :     _CONSTEXPR20 _String_val() noexcept : _Bx() {}

  00023	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00028	48 89 44 24 28	 mov	 QWORD PTR this$[rsp], rax

; 493  :         _CONSTEXPR20 _Bxty() noexcept : _Buf() {} // user-provided, for fancy pointers

  0002d	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00032	48 8b 7c 24 28	 mov	 rdi, QWORD PTR this$[rsp]
  00037	33 c0		 xor	 eax, eax
  00039	b9 10 00 00 00	 mov	 ecx, 16
  0003e	f3 aa		 rep stosb

; 517  :     size_type _Mysize = 0; // current length of string (size)

  00040	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00045	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 518  :     size_type _Myres  = 0; // current storage reserved for string (capacity)

  0004d	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00052	48 c7 40 18 00
	00 00 00	 mov	 QWORD PTR [rax+24], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 310  :         return _CSTD wcslen(reinterpret_cast<const wchar_t*>(_First));

  0005a	48 8b 4c 24 68	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  0005f	e8 00 00 00 00	 call	 wcslen
  00064	48 89 44 24 38	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 769  :         _Construct<_Construct_strategy::_From_ptr>(_Ptr, _Convert_size<size_type>(_Traits::length(_Ptr)));

  00069	48 8b 44 24 38	 mov	 rax, QWORD PTR $T1[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1131 :     return static_cast<_Size_type>(_Len);

  0006e	48 89 44 24 40	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 769  :         _Construct<_Construct_strategy::_From_ptr>(_Ptr, _Convert_size<size_type>(_Traits::length(_Ptr)));

  00073	48 8b 44 24 40	 mov	 rax, QWORD PTR $T2[rsp]
  00078	4c 8b c0	 mov	 r8, rax
  0007b	48 8b 54 24 68	 mov	 rdx, QWORD PTR _Ptr$[rsp]
  00080	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  00085	e8 00 00 00 00	 call	 ??$_Construct@$00PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<1,wchar_t const *>
  0008a	90		 npad	 1

; 770  :     }

  0008b	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00090	48 83 c4 50	 add	 rsp, 80			; 00000050H
  00094	5f		 pop	 rdi
  00095	c3		 ret	 0
??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
this$ = 32
this$ = 40
this$ = 48
$T1 = 56
$T2 = 64
this$ = 96
_Ptr$ = 104
?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z@4HA PROC ; `std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 60	 mov	 rcx, QWORD PTR this$[rbp]
  0000d	e8 00 00 00 00	 call	 ??1?$_Compressed_pair@V?$allocator@_W@std@@V?$_String_val@U?$_Simple_types@_W@std@@@2@$00@std@@QEAA@XZ
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z@4HA ENDP ; `std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z
_TEXT	SEGMENT
_Overflow_is_possible$1 = 32
_Bytes$ = 40
$T2 = 48
$T3 = 56
$T4 = 64
_Max_possible$5 = 72
this$ = 96
_Count$ = 104
?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z PROC	; std::allocator<wchar_t>::allocate, COMDAT

; 988  :     _NODISCARD_RAW_PTR_ALLOC _CONSTEXPR20 __declspec(allocator) _Ty* allocate(_CRT_GUARDOVERFLOW const size_t _Count) {

$LN13:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 113  :     constexpr bool _Overflow_is_possible = _Ty_size > 1;

  0000e	c6 44 24 20 01	 mov	 BYTE PTR _Overflow_is_possible$1[rsp], 1

; 114  : 
; 115  :     if constexpr (_Overflow_is_possible) {
; 116  :         constexpr size_t _Max_possible = static_cast<size_t>(-1) / _Ty_size;

  00013	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  0001d	48 89 44 24 48	 mov	 QWORD PTR _Max_possible$5[rsp], rax

; 117  :         if (_Count > _Max_possible) {

  00022	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  0002c	48 39 44 24 68	 cmp	 QWORD PTR _Count$[rsp], rax
  00031	76 06		 jbe	 SHORT $LN4@allocate

; 118  :             _Throw_bad_array_new_length(); // multiply overflow

  00033	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00038	90		 npad	 1
$LN4@allocate:

; 119  :         }
; 120  :     }
; 121  : 
; 122  :     return _Count * _Ty_size;

  00039	48 8b 44 24 68	 mov	 rax, QWORD PTR _Count$[rsp]
  0003e	48 d1 e0	 shl	 rax, 1
  00041	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00046	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
  0004b	48 89 44 24 28	 mov	 QWORD PTR _Bytes$[rsp], rax

; 227  :     if (_Bytes == 0) {

  00050	48 83 7c 24 28
	00		 cmp	 QWORD PTR _Bytes$[rsp], 0
  00056	75 0b		 jne	 SHORT $LN8@allocate

; 228  :         return nullptr;

  00058	48 c7 44 24 30
	00 00 00 00	 mov	 QWORD PTR $T2[rsp], 0
  00061	eb 35		 jmp	 SHORT $LN7@allocate
$LN8@allocate:

; 229  :     }
; 230  : 
; 231  : #if _HAS_CXX20 // TRANSITION, GH-1532
; 232  :     if (_STD is_constant_evaluated()) {
; 233  :         return _Traits::_Allocate(_Bytes);
; 234  :     }
; 235  : #endif // _HAS_CXX20
; 236  : 
; 237  : #ifdef __cpp_aligned_new
; 238  :     if constexpr (_Align > __STDCPP_DEFAULT_NEW_ALIGNMENT__) {
; 239  :         size_t _Passed_align = _Align;
; 240  : #if defined(_M_IX86) || defined(_M_X64)
; 241  :         if (_Bytes >= _Big_allocation_threshold) {
; 242  :             // boost the alignment of big allocations to help autovectorization
; 243  :             _Passed_align = (_STD max)(_Align, _Big_allocation_alignment);
; 244  :         }
; 245  : #endif // defined(_M_IX86) || defined(_M_X64)
; 246  :         return _Traits::_Allocate_aligned(_Bytes, _Passed_align);
; 247  :     } else
; 248  : #endif // defined(__cpp_aligned_new)
; 249  :     {
; 250  : #if defined(_M_IX86) || defined(_M_X64)
; 251  :         if (_Bytes >= _Big_allocation_threshold) {

  00063	48 81 7c 24 28
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  0006c	72 11		 jb	 SHORT $LN9@allocate

; 252  :             // boost the alignment of big allocations to help autovectorization
; 253  :             return _Allocate_manually_vector_aligned<_Traits>(_Bytes);

  0006e	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00073	e8 00 00 00 00	 call	 ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
  00078	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
  0007d	eb 19		 jmp	 SHORT $LN7@allocate
$LN9@allocate:

; 136  :         return ::operator new(_Bytes);

  0007f	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00084	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00089	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax

; 256  :         return _Traits::_Allocate(_Bytes);

  0008e	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  00093	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
$LN7@allocate:

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00098	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
$LN6@allocate:

; 991  :     }

  0009d	48 83 c4 58	 add	 rsp, 88			; 00000058H
  000a1	c3		 ret	 0
?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z ENDP	; std::allocator<wchar_t>::allocate
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Xlen_string@std@@YAXXZ
_TEXT	SEGMENT
?_Xlen_string@std@@YAXXZ PROC				; std::_Xlen_string, COMDAT

; 530  : [[noreturn]] inline void _Xlen_string() {

$LN3:
  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 531  :     _Xlength_error("string too long");

  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_0BA@JFNIOLAK@string?5too?5long@
  0000b	e8 00 00 00 00	 call	 ?_Xlength_error@std@@YAXPEBD@Z ; std::_Xlength_error
  00010	90		 npad	 1
$LN2@Xlen_strin:

; 532  : }

  00011	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00015	c3		 ret	 0
?_Xlen_string@std@@YAXXZ ENDP				; std::_Xlen_string
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?_Throw_bad_array_new_length@std@@YAXXZ
_TEXT	SEGMENT
$T1 = 32
?_Throw_bad_array_new_length@std@@YAXXZ PROC		; std::_Throw_bad_array_new_length, COMDAT

; 107  : [[noreturn]] inline void _Throw_bad_array_new_length() {

$LN3:
  00000	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 108  :     _THROW(bad_array_new_length{});

  00004	48 8d 4c 24 20	 lea	 rcx, QWORD PTR $T1[rsp]
  00009	e8 00 00 00 00	 call	 ??0bad_array_new_length@std@@QEAA@XZ ; std::bad_array_new_length::bad_array_new_length
  0000e	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:_TI3?AVbad_array_new_length@std@@
  00015	48 8d 4c 24 20	 lea	 rcx, QWORD PTR $T1[rsp]
  0001a	e8 00 00 00 00	 call	 _CxxThrowException
  0001f	90		 npad	 1
$LN2@Throw_bad_:

; 109  : }

  00020	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00024	c3		 ret	 0
?_Throw_bad_array_new_length@std@@YAXXZ ENDP		; std::_Throw_bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gbad_array_new_length@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gbad_array_new_length@std@@UEAAPEAXI@Z PROC		; std::bad_array_new_length::`scalar deleting destructor', COMDAT
$LN20:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gbad_array_new_length@std@@UEAAPEAXI@Z ENDP		; std::bad_array_new_length::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??0bad_array_new_length@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
__that$ = 56
??0bad_array_new_length@std@@QEAA@AEBV01@@Z PROC	; std::bad_array_new_length::bad_array_new_length, COMDAT
$LN14:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000e	48 8b 54 24 38	 mov	 rdx, QWORD PTR __that$[rsp]
  00013	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00018	e8 00 00 00 00	 call	 ??0bad_alloc@std@@QEAA@AEBV01@@Z
  0001d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00022	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_array_new_length@std@@6B@
  00029	48 89 08	 mov	 QWORD PTR [rax], rcx
  0002c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00031	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00035	c3		 ret	 0
??0bad_array_new_length@std@@QEAA@AEBV01@@Z ENDP	; std::bad_array_new_length::bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??1bad_array_new_length@std@@UEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1bad_array_new_length@std@@UEAA@XZ PROC		; std::bad_array_new_length::~bad_array_new_length, COMDAT
$LN14:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  00018	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 83 c0 08	 add	 rax, 8
  00021	48 8b c8	 mov	 rcx, rax
  00024	e8 00 00 00 00	 call	 __std_exception_destroy
  00029	90		 npad	 1
  0002a	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0002e	c3		 ret	 0
??1bad_array_new_length@std@@UEAA@XZ ENDP		; std::bad_array_new_length::~bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0bad_array_new_length@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 16
??0bad_array_new_length@std@@QEAA@XZ PROC		; std::bad_array_new_length::bad_array_new_length, COMDAT

; 144  :     {

$LN14:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi

; 67   :     {

  00006	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0000b	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00012	48 89 08	 mov	 QWORD PTR [rax], rcx

; 66   :         : _Data()

  00015	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 83 c0 08	 add	 rax, 8
  0001e	48 8b f8	 mov	 rdi, rax
  00021	33 c0		 xor	 eax, eax
  00023	b9 10 00 00 00	 mov	 ecx, 16
  00028	f3 aa		 rep stosb

; 68   :         _Data._What = _Message;

  0002a	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0002f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_0BF@KINCDENJ@bad?5array?5new?5length@
  00036	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 133  :     {

  0003a	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0003f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_alloc@std@@6B@
  00046	48 89 08	 mov	 QWORD PTR [rax], rcx

; 144  :     {

  00049	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_array_new_length@std@@6B@
  00055	48 89 08	 mov	 QWORD PTR [rax], rcx

; 145  :     }

  00058	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0005d	5f		 pop	 rdi
  0005e	c3		 ret	 0
??0bad_array_new_length@std@@QEAA@XZ ENDP		; std::bad_array_new_length::bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gbad_alloc@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gbad_alloc@std@@UEAAPEAXI@Z PROC			; std::bad_alloc::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gbad_alloc@std@@UEAAPEAXI@Z ENDP			; std::bad_alloc::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0bad_alloc@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
__that$ = 56
??0bad_alloc@std@@QEAA@AEBV01@@Z PROC			; std::bad_alloc::bad_alloc, COMDAT
$LN9:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 20	 sub	 rsp, 32			; 00000020H

; 73   :     {

  0000f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  0001b	48 89 08	 mov	 QWORD PTR [rax], rcx

; 72   :         : _Data()

  0001e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 83 c0 08	 add	 rax, 8
  00027	48 8b f8	 mov	 rdi, rax
  0002a	33 c0		 xor	 eax, eax
  0002c	b9 10 00 00 00	 mov	 ecx, 16
  00031	f3 aa		 rep stosb

; 74   :         __std_exception_copy(&_Other._Data, &_Data);

  00033	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 83 c0 08	 add	 rax, 8
  0003c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR __that$[rsp]
  00041	48 83 c1 08	 add	 rcx, 8
  00045	48 8b d0	 mov	 rdx, rax
  00048	e8 00 00 00 00	 call	 __std_exception_copy
  0004d	90		 npad	 1
  0004e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_alloc@std@@6B@
  0005a	48 89 08	 mov	 QWORD PTR [rax], rcx
  0005d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00062	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00066	5f		 pop	 rdi
  00067	c3		 ret	 0
??0bad_alloc@std@@QEAA@AEBV01@@Z ENDP			; std::bad_alloc::bad_alloc
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gexception@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gexception@std@@UEAAPEAXI@Z PROC			; std::exception::`scalar deleting destructor', COMDAT
$LN10:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gexception@std@@UEAAPEAXI@Z ENDP			; std::exception::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ?what@exception@std@@UEBAPEBDXZ
_TEXT	SEGMENT
tv69 = 0
this$ = 32
?what@exception@std@@UEBAPEBDXZ PROC			; std::exception::what, COMDAT

; 95   :     {

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 18	 sub	 rsp, 24

; 96   :         return _Data._What ? _Data._What : "Unknown exception";

  00009	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00013	74 0f		 je	 SHORT $LN3@what
  00015	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0001e	48 89 04 24	 mov	 QWORD PTR tv69[rsp], rax
  00022	eb 0b		 jmp	 SHORT $LN4@what
$LN3@what:
  00024	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BC@EOODALEL@Unknown?5exception@
  0002b	48 89 04 24	 mov	 QWORD PTR tv69[rsp], rax
$LN4@what:
  0002f	48 8b 04 24	 mov	 rax, QWORD PTR tv69[rsp]

; 97   :     }

  00033	48 83 c4 18	 add	 rsp, 24
  00037	c3		 ret	 0
?what@exception@std@@UEBAPEBDXZ ENDP			; std::exception::what
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0exception@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
_Other$ = 56
??0exception@std@@QEAA@AEBV01@@Z PROC			; std::exception::exception, COMDAT

; 73   :     {

$LN4:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  0000f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  0001b	48 89 08	 mov	 QWORD PTR [rax], rcx

; 72   :         : _Data()

  0001e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 83 c0 08	 add	 rax, 8
  00027	48 8b f8	 mov	 rdi, rax
  0002a	33 c0		 xor	 eax, eax
  0002c	b9 10 00 00 00	 mov	 ecx, 16
  00031	f3 aa		 rep stosb

; 74   :         __std_exception_copy(&_Other._Data, &_Data);

  00033	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 83 c0 08	 add	 rax, 8
  0003c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Other$[rsp]
  00041	48 83 c1 08	 add	 rcx, 8
  00045	48 8b d0	 mov	 rdx, rax
  00048	e8 00 00 00 00	 call	 __std_exception_copy
  0004d	90		 npad	 1

; 75   :     }

  0004e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00057	5f		 pop	 rdi
  00058	c3		 ret	 0
??0exception@std@@QEAA@AEBV01@@Z ENDP			; std::exception::exception
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
