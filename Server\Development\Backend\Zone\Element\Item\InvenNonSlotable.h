﻿#pragma once

#include "InvenSlot.h"

namespace mu2
{	
	
	//====================================================================================================
	//
	//====================================================================================================

	class InvenNonSlotable : public Inven
	{
	public:
		explicit InvenNonSlotable(EntityPlayer& owner, eInvenType eType, const SlotSizeType maxSlot, Bool isActiveAdd, Bool isActiveSub) 
			: Inven(owner, eType, maxSlot, isActiveAdd, isActiveSub) 
		{
			m_direction[SOURCE] = false;
			m_direction[TARGET] = false;
		}
		virtual ~InvenNonSlotable() override {}
		virtual void				Initialize() override {}
		virtual Bool				IsSlotBags() const { return false; }
		virtual Bool				isValidSlot(const eSlotType& eSlot) const override;
		virtual Bool				IsPushable(const eSlotType& toSlot, const ItemConstPtr& item, Bool isAllowBlockNExpired = false) const { return true; }
		virtual void				OnNotifyPushed(const ItemPtr& item) override {};
		virtual void				OnNotifyPoped(const ItemPtr& item) override {};
		virtual ItemPtr				GetItemByPlace(const ItemPlace& place) const override;
		virtual Bool				IsFreeSlot(const eSlotType& slot) const override;

	protected:		
		virtual void				fillUpWithPos(const eSlotType& slot, ItemData& data) override;
		virtual void				setItemPos(const ItemPlace place, const ItemPtr& pushableItem) override;
		virtual ErrorItem::Error	setUpItemFromProcedure(const ItemPlace oldPlace, const ItemPtr& item, ProcedureType::Enum eProcType) override;
		ErrorItem::Error			pushFront(const ItemPtr& item);
		ItemPtr						clearItemPos(ItemId id);
	};

	//====================================================================================================
	//
	//====================================================================================================

	class alignas(32) InvenReBuy : public InvenNonSlotable
	{
	public:
		explicit InvenReBuy(EntityPlayer& owner, eInvenType eType, const SlotSizeType maxSlot, Bool isActiveAdd, Bool isActiveSub)
			: InvenNonSlotable(owner, eType, maxSlot, isActiveAdd, isActiveSub)
		{
			m_emergencyRange = RebuyBags;
		}
		virtual ~InvenReBuy() override {}
	public:

		virtual Bool				IsRebuyBag() const { return true; }		
		virtual ErrorItem::Error	PrevCreateItem(const ItemData& data);

	public:
		Bool				CombOutToRebuy();
		Bool				SyncRebuyInven();
		Bool				IsRebuyItemTimeOver( const UInt32 curTime, const UInt32 itemTime );
		ErrorItem::Error	AcquireItemFromRebuyInven(const ItemId id, TransactionPtr transPtr);
		ErrorItem::Error	SyncRebuyItemList();
		
	};

	//====================================================================================================
	//
	//====================================================================================================

	class alignas(32) InvenMail : public InvenNonSlotable
	{
	private:
		std::set<ItemId>	m_ssetSendingItems;

	public:
		explicit InvenMail(EntityPlayer& owner, eInvenType eType, const SlotSizeType maxSlot, Bool isActiveAdd, Bool isActiveSub)
			: InvenNonSlotable(owner, eType, maxSlot, isActiveAdd, isActiveSub)
			, m_ssetSendingItems()
		{
			m_emergencyRange = MailBags;
		}
		virtual ~InvenMail() override {}

	public:
		virtual Bool		IsMailBag() const { return true; }

	public:
		ErrorMail::Error	AcquireMailItems( Mail* mail );
		ErrorItem::Error	PushMail( const IndexItem& indexItem, const Int32& count, const MailData& mail = MailData() );
		ErrorItem::Error	PushMail(const ItemData& pushItem, const MailData& mail = MailData());
		Bool				AppendSendingItems(ItemId itemId);
		void				RemoveSendingItems(ItemId itemId);
#ifdef __Patch_Mail_HwaYeong_20190117
		ErrorMail::Error	CheckAcquireIInvenSlot( const MailData& mailData, OUT ItemBag &bag, OUT SimpleItems	&recvItems );
#endif

	private:
		void				setPerodAtAcquireItem(__inout ItemData& acquirableItem);
	};




}