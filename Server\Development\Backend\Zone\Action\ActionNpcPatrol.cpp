﻿#include "stdafx.h"
#include <Backend/Zone/ZoneServer.h>
#include <Backend/Zone/Action/ActionNpcPatrol.h>
#include <Backend/Zone/Action/ActionNpc.h>
#include <Backend/Zone/Action/ActionNpcScript.h>
#include <Backend/Zone/Action/ActionNpcMove.h>
#include <Backend/Zone/View/ViewPosition.h>
namespace mu2
{

/* 
 * rinfo.pathKind, rinfo.patrolIndex
 * 	Int32				currentPatrolIndex;			// 
	UInt32 				currentPatrolIdleTime; 		// IDLE 상태에 알려주기 위한 것
	EPatrolDirection 	patrolDir; 					// 방향
 */

ActionNpcPatrol::ActionNpcPatrol(EntityNpc* owner )
	: Action4Npc( owner, ID )
{
	CounterInc("ActionNpcPatrol");

	m_attrNpc = GetEntityAttribute( owner );

	MU2_ASSERT( m_attrNpc );
}

ActionNpcPatrol::~ActionNpcPatrol()
{
	CounterDec("ActionNpcPatrol");
}

ActionNpcPatrol::EPatrolDecision ActionNpcPatrol::DecideOnEnter()
{
	EntityNpc* npc = GetOwnerNpc();
	VERIFY_RETURN(npc && npc->IsValid(), EPD_QUIT);

	if (m_attrNpc->GetPathKind() == PathVolumeElem::NONE)
	{
		return EPD_QUIT;
	}

	// 해당 볼륨을 찾아온다
	ActionSector* actSector = GetEntityAction( npc );
	MU2_ASSERT( actSector );

	IndexZone indexZone = (actSector->GetSector()->GetIndexZone());

	PathVolumeScript* script = 	GetZoneScript<PathVolumeScript>(indexZone);
	if ( script == nullptr )
	{
		return EPD_QUIT;
	}

	// 현재 볼륨이 있는 지 확인 
	if ( m_attrNpc->currentPatrolIndex == 0 )
	{
		m_attrNpc->currentPatrolIndex = m_attrNpc->GetPatrolIndex();

		// 처음은 전진
		m_attrNpc->patrolDir = AttributeNpc::EMOVE_DIR_FORWARD;		
	}

	if ( m_attrNpc->currentPatrolIndex == 0 )
	{
		return EPD_QUIT;		
	}

	// onEnter에서는 타겟을 무조건 업데이트 한다. 
	updateNewTargetPos( script );	

	return EPD_CONTINUE; 
}

ActionNpcPatrol::EPatrolDecision ActionNpcPatrol::DecideOnUpdate()
{
	EntityNpc* npc = GetOwnerNpc();
	VERIFY_RETURN(npc && npc->IsValid(), EPD_QUIT);

	if ( m_attrNpc->GetPathKind() == PathVolumeElem::NONE)
	{
		return EPD_QUIT;
	}	

	// 해당 볼륨을 찾아온다
	ActionSector* actSector = GetEntityAction( npc );
	MU2_ASSERT( actSector );

	IndexZone indexZone = actSector->GetSector()->GetIndexZone();

	PathVolumeScript* script = 	GetZoneScript<PathVolumeScript>(indexZone);
	if ( script == nullptr )
	{
		return EPD_QUIT;		
	}

	const PathVolumeScript::Element* elem = script->Get( m_attrNpc->currentPatrolIndex );
	if ( elem == nullptr )
	{
		return EPD_QUIT;		
	}	

	// 목표 볼륨에 도착. 30 정도 미만이면 
	if ( targetReached() )
	{
		// 도착하면 다음으로 이동
		m_attrNpc->currentPatrolIndex = findNextIndex( script, elem );

		if ( elem->breakTime > 0 )
		{
			m_attrNpc->currentPatrolIdleTime = elem->breakTime;

			ActionNpcMove* anm = GetEntityAction( npc );
			anm->MoveStop();

			return EPD_IDLE_WAIT;
		}

		if ( m_attrNpc->currentPatrolIndex == 0 )
		{
			return EPD_QUIT; 			
		}

		elem = script->Get( m_attrNpc->currentPatrolIndex );

		if ( elem == nullptr )
		{
			return EPD_QUIT;
		}

		updateNewTargetPos( script );
	}

	return EPD_CONTINUE;
}

Bool ActionNpcPatrol::updateNewTargetPos(PathVolumeScript* script)
{
	VERIFY_RETURN(script, false)

	EntityNpc* npc = GetOwnerNpc();
	VERIFY_RETURN(npc && npc->IsValid(), false);

	ActionSector* actSector = GetEntityAction(npc);
	const PathVolumeScript::Element* elem = script->Get( m_attrNpc->currentPatrolIndex );
	if ( elem == nullptr )
	{
		MU2_WARN_LOG( LogCategory::CONTENTS, "%s> Path is null. [ ZonedID : %d ][ PatrolIndex: %d ]", __FUNCTION__, actSector->GetSector()->GetIndexZone(), m_attrNpc->currentPatrolIndex );

		return false; 
	}

	// Return 위치를 현재 위치로 변경 
	// 스폰 위치도 현재 위치로 변경
	//ActionNpc* actNpc = GetEntityAction(npc);

	Vector3 center( elem->x, elem->y, elem->z );

	npc->GetNpcAction().FindRandomPosInVolume( center, elem->radius, m_targetPos );

	m_targetPos = actSector->GetSector()->GetRealworldSector()->GetWalkablePosition(center, m_targetPos, actSector->GetTraverseLogicData());
	
	//ActionNpcMove* anm = GetEntityAction(npc);

	// 목표 볼륨의 위치로 이동하도록 시킨다. 
	return npc->GetMoveAction().MoveToPos( m_targetPos, npc->GetDefaultMoveState() );
}

void ActionNpcPatrol::Move()
{
	EntityNpc* npc = GetOwnerNpc();
	VERIFY_RETURN(npc && npc->IsValid(), );

	ActionNpcMove* anm = GetEntityAction(npc);
	if( anm == NULL )
	{
		return;
	}
	anm->Update();
}

void ActionNpcPatrol::QuitPatrol()
{
	m_attrNpc->ResetPathKind(); // 더 이상 정찰 안 하는 타잎으로 변경	
}

Bool ActionNpcPatrol::targetReached()
{	
	EntityNpc* npc = GetOwnerNpc();
	VERIFY_RETURN(npc && npc->IsValid(), false);

	ViewPosition* ownerViewPosition = GetEntityView(npc);

	Vector3 curPosition;
	ownerViewPosition->GetRealPosition( curPosition );

	const UInt32 moveSpeed = npc->GetAction< ActionAbility >()->GetAbilityValue(EAT::WALK_SPEED);

	float remainDestSq = DistanceSquared2D(curPosition, m_targetPos);
	float destThreshold = (float) moveSpeed * 2.0f;

	return (remainDestSq <= (destThreshold * destThreshold));
}

// 목표 볼륨 안에 들어갈 때 다음 위치를 찾음.
UInt32 ActionNpcPatrol::findNextIndex( PathVolumeScript* script, const PathVolumeElem* elem )
{
	MU2_ASSERT( m_attrNpc->patrolDir != AttributeNpc::EMOVE_DIR_NONE );

	if ( m_attrNpc->patrolDir == AttributeNpc::EMOVE_DIR_FORWARD )
	{
		if ( elem->next > 0 ) 
		{
			return elem->next;
		}

		// 전진 불가 (끝에 옴)
		switch ( m_attrNpc->GetPathKind())
		{
		case PathVolumeElem::PATROL_STOP:
			{
				return 0;	// 더 이상 뭘 안 함				
			}
			break;
		case PathVolumeElem::PATROL_REVERSE:
			{
				m_attrNpc->patrolDir = AttributeNpc::EMOVE_DIR_BACKWARD;

				return elem->prev;
			}
			break;
		case PathVolumeElem::PATROL_ROUND:
			{		
				UInt32 vindex = 0;
				UInt32 breakCount = 0;

				const PathVolumeElem* velem = elem;

				while ( velem && velem->prev != 0 && breakCount < 100 )
				{
					vindex = velem->prev;

					velem = script->Get( vindex );

					breakCount++;
				}

				m_attrNpc->patrolDir = AttributeNpc::EMOVE_DIR_FORWARD;

				return vindex;
			}
			break;
		}
		
		return 0; // 모르는 타잎
	}
	else 
	{
		if ( elem->prev > 0 ) 
		{
			return elem->prev;
		}

		// 전진 불가 (처음에 옴)
		switch ( m_attrNpc->GetPathKind())
		{
		case PathVolumeElem::PATROL_STOP:
			{
				return 0;	// 더 이상 뭘 안 함				
			}
			break;
		case PathVolumeElem::PATROL_REVERSE:
			{
				m_attrNpc->patrolDir = AttributeNpc::EMOVE_DIR_FORWARD;

				return elem->next;
			}
			break;
		case PathVolumeElem::PATROL_ROUND:
			{	
				MU2_ASSERT( !"巡逻回合不能反向移动" );

				m_attrNpc->patrolDir = AttributeNpc::EMOVE_DIR_FORWARD;

				return elem->next;
			}
			break;
		}
	}

	return 0; 
}

} // namespace mu2
