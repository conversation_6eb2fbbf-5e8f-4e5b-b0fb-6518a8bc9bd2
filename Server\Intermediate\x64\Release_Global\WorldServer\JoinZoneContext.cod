; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

CONST	SEGMENT
?_1@placeholders@std@@3U?$_Ph@$00@2@B	ORG $+1		; std::placeholders::_1
CONST	ENDS
PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	??_GISerializer@mu2@@UEAAPEAXI@Z		; mu2::ISerializer::`scalar deleting destructor'
PUBLIC	??_GExecutionZoneId@mu2@@UEAAPEAXI@Z		; mu2::ExecutionZoneId::`scalar deleting destructor'
PUBLIC	??0JoinZoneContext@mu2@@QEAA@XZ			; mu2::JoinZoneContext::JoinZoneContext
PUBLIC	??0JoinZoneContext@mu2@@QEAA@AEBVExecutionZoneId@1@@Z ; mu2::JoinZoneContext::JoinZoneContext
PUBLIC	?Clear@JoinZoneContext@mu2@@UEAAXXZ		; mu2::JoinZoneContext::Clear
PUBLIC	?IsValid@JoinZoneContext@mu2@@QEBA?B_NXZ	; mu2::JoinZoneContext::IsValid
PUBLIC	??0JoinZoneContextCurr@mu2@@QEAA@XZ		; mu2::JoinZoneContextCurr::JoinZoneContextCurr
PUBLIC	?Clear@JoinZoneContextCurr@mu2@@UEAAXXZ		; mu2::JoinZoneContextCurr::Clear
PUBLIC	?Setup@JoinZoneContextCurr@mu2@@QEAAXAEBVExecution@2@@Z ; mu2::JoinZoneContextCurr::Setup
PUBLIC	??0JoinZoneContextDest@mu2@@QEAA@XZ		; mu2::JoinZoneContextDest::JoinZoneContextDest
PUBLIC	??0JoinZoneContextDest@mu2@@QEAA@AEBVExecutionZoneId@1@AEBH@Z ; mu2::JoinZoneContextDest::JoinZoneContextDest
PUBLIC	?Clear@JoinZoneContextDest@mu2@@UEAAXXZ		; mu2::JoinZoneContextDest::Clear
PUBLIC	?IsChannel@JoinZoneContextDest@mu2@@QEBA?B_NXZ	; mu2::JoinZoneContextDest::IsChannel
PUBLIC	?IsInstance@JoinZoneContextDest@mu2@@QEBA?B_NXZ	; mu2::JoinZoneContextDest::IsInstance
PUBLIC	?Setup@JoinZoneContextDest@mu2@@QEAAXAEBVExecution@2@@Z ; mu2::JoinZoneContextDest::Setup
PUBLIC	?IsCountable@JoinZoneContextDest@mu2@@SA?B_NW4Enum@JoinContext@2@@Z ; mu2::JoinZoneContextDest::IsCountable
PUBLIC	??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@ ; `string'
PUBLIC	??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@		; `string'
PUBLIC	??_7ISerializer@mu2@@6B@			; mu2::ISerializer::`vftable'
PUBLIC	??_7ExecutionZoneId@mu2@@6B@			; mu2::ExecutionZoneId::`vftable'
PUBLIC	??_R4ISerializer@mu2@@6B@			; mu2::ISerializer::`RTTI Complete Object Locator'
PUBLIC	??_R0?AUISerializer@mu2@@@8			; mu2::ISerializer `RTTI Type Descriptor'
PUBLIC	??_R3ISerializer@mu2@@8				; mu2::ISerializer::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2ISerializer@mu2@@8				; mu2::ISerializer::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@ISerializer@mu2@@8		; mu2::ISerializer::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4ExecutionZoneId@mu2@@6B@			; mu2::ExecutionZoneId::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVExecutionZoneId@mu2@@@8			; mu2::ExecutionZoneId `RTTI Type Descriptor'
PUBLIC	??_R3ExecutionZoneId@mu2@@8			; mu2::ExecutionZoneId::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2ExecutionZoneId@mu2@@8			; mu2::ExecutionZoneId::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@ExecutionZoneId@mu2@@8		; mu2::ExecutionZoneId::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_7JoinZoneContext@mu2@@6B@			; mu2::JoinZoneContext::`vftable'
PUBLIC	??_7JoinZoneContextCurr@mu2@@6B@		; mu2::JoinZoneContextCurr::`vftable'
PUBLIC	??_7JoinZoneContextDest@mu2@@6B@		; mu2::JoinZoneContextDest::`vftable'
PUBLIC	??_C@_0EO@NNODNCE@F?3?2Release_Branch?2Server?2Develo@ ; `string'
PUBLIC	??_C@_0P@KLKKGGLE@channelId?5?$DN?$DN?50@	; `string'
PUBLIC	??_C@_0CF@FLLECCEB@mu2?3?3JoinZoneContextDest?3?3IsIns@ ; `string'
PUBLIC	??_C@_0BA@NLHNJEHG@instanceId?5?$DN?$DN?50@	; `string'
PUBLIC	??_C@_0CE@BCKECPFB@mu2?3?3JoinZoneContextDest?3?3IsCha@ ; `string'
PUBLIC	??_R4JoinZoneContextDest@mu2@@6B@		; mu2::JoinZoneContextDest::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVJoinZoneContextDest@mu2@@@8		; mu2::JoinZoneContextDest `RTTI Type Descriptor'
PUBLIC	??_R3JoinZoneContextDest@mu2@@8			; mu2::JoinZoneContextDest::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2JoinZoneContextDest@mu2@@8			; mu2::JoinZoneContextDest::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@JoinZoneContextDest@mu2@@8	; mu2::JoinZoneContextDest::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@JoinZoneContext@mu2@@8		; mu2::JoinZoneContext::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R0?AVJoinZoneContext@mu2@@@8			; mu2::JoinZoneContext `RTTI Type Descriptor'
PUBLIC	??_R3JoinZoneContext@mu2@@8			; mu2::JoinZoneContext::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2JoinZoneContext@mu2@@8			; mu2::JoinZoneContext::`RTTI Base Class Array'
PUBLIC	??_R4JoinZoneContextCurr@mu2@@6B@		; mu2::JoinZoneContextCurr::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVJoinZoneContextCurr@mu2@@@8		; mu2::JoinZoneContextCurr `RTTI Type Descriptor'
PUBLIC	??_R3JoinZoneContextCurr@mu2@@8			; mu2::JoinZoneContextCurr::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2JoinZoneContextCurr@mu2@@8			; mu2::JoinZoneContextCurr::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@JoinZoneContextCurr@mu2@@8	; mu2::JoinZoneContextCurr::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4JoinZoneContext@mu2@@6B@			; mu2::JoinZoneContext::`RTTI Complete Object Locator'
EXTRN	_purecall:PROC
EXTRN	??3@YAXPEAX_K@Z:PROC				; operator delete
EXTRN	__imp_GetStdHandle:PROC
EXTRN	__imp_SetConsoleTextAttribute:PROC
EXTRN	?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ:PROC	; mu2::Logger::Logging
EXTRN	??_EISerializer@mu2@@UEAAPEAXI@Z:PROC		; mu2::ISerializer::`vector deleting destructor'
EXTRN	??0ExecutionZoneId@mu2@@QEAA@XZ:PROC		; mu2::ExecutionZoneId::ExecutionZoneId
EXTRN	?PackUnpack@ExecutionZoneId@mu2@@UEAA_NAEAVPacketStream@2@@Z:PROC ; mu2::ExecutionZoneId::PackUnpack
EXTRN	?Reset@ExecutionZoneId@mu2@@UEAAXXZ:PROC	; mu2::ExecutionZoneId::Reset
EXTRN	?IsValid@ExecutionZoneId@mu2@@QEBA?B_NXZ:PROC	; mu2::ExecutionZoneId::IsValid
EXTRN	??4ExecutionZoneId@mu2@@QEAAAEAV01@AEBI@Z:PROC	; mu2::ExecutionZoneId::operator=
EXTRN	??_EExecutionZoneId@mu2@@UEAAPEAXI@Z:PROC	; mu2::ExecutionZoneId::`vector deleting destructor'
EXTRN	?GetExecId@Execution@mu2@@QEBAAEBVExecutionZoneId@2@XZ:PROC ; mu2::Execution::GetExecId
EXTRN	??_7type_info@@6B@:BYTE				; type_info::`vftable'
EXTRN	_fltused:DWORD
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GISerializer@mu2@@UEAAPEAXI@Z DD imagerel $LN10
	DD	imagerel $LN10+65
	DD	imagerel $unwind$??_GISerializer@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GExecutionZoneId@mu2@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+65
	DD	imagerel $unwind$??_GExecutionZoneId@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0JoinZoneContext@mu2@@QEAA@XZ DD imagerel $LN10
	DD	imagerel $LN10+115
	DD	imagerel $unwind$??0JoinZoneContext@mu2@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0JoinZoneContext@mu2@@QEAA@AEBVExecutionZoneId@1@@Z DD imagerel $LN20
	DD	imagerel $LN20+174
	DD	imagerel $unwind$??0JoinZoneContext@mu2@@QEAA@AEBVExecutionZoneId@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Clear@JoinZoneContext@mu2@@UEAAXXZ DD imagerel $LN5
	DD	imagerel $LN5+109
	DD	imagerel $unwind$?Clear@JoinZoneContext@mu2@@UEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?IsValid@JoinZoneContext@mu2@@QEBA?B_NXZ DD imagerel $LN5
	DD	imagerel $LN5+66
	DD	imagerel $unwind$?IsValid@JoinZoneContext@mu2@@QEBA?B_NXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0JoinZoneContextCurr@mu2@@QEAA@XZ DD imagerel $LN4
	DD	imagerel $LN4+56
	DD	imagerel $unwind$??0JoinZoneContextCurr@mu2@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Clear@JoinZoneContextCurr@mu2@@UEAAXXZ DD imagerel $LN3
	DD	imagerel $LN3+37
	DD	imagerel $unwind$?Clear@JoinZoneContextCurr@mu2@@UEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Setup@JoinZoneContextCurr@mu2@@QEAAXAEBVExecution@2@@Z DD imagerel $LN15
	DD	imagerel $LN15+131
	DD	imagerel $unwind$?Setup@JoinZoneContextCurr@mu2@@QEAAXAEBVExecution@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0JoinZoneContextDest@mu2@@QEAA@XZ DD imagerel $LN4
	DD	imagerel $LN4+125
	DD	imagerel $unwind$??0JoinZoneContextDest@mu2@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0JoinZoneContextDest@mu2@@QEAA@AEBVExecutionZoneId@1@AEBH@Z DD imagerel $LN4
	DD	imagerel $LN4+74
	DD	imagerel $unwind$??0JoinZoneContextDest@mu2@@QEAA@AEBVExecutionZoneId@1@AEBH@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Clear@JoinZoneContextDest@mu2@@UEAAXXZ DD imagerel $LN3
	DD	imagerel $LN3+106
	DD	imagerel $unwind$?Clear@JoinZoneContextDest@mu2@@UEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?IsChannel@JoinZoneContextDest@mu2@@QEBA?B_NXZ DD imagerel $LN9
	DD	imagerel $LN9+267
	DD	imagerel $unwind$?IsChannel@JoinZoneContextDest@mu2@@QEBA?B_NXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?IsInstance@JoinZoneContextDest@mu2@@QEBA?B_NXZ DD imagerel $LN9
	DD	imagerel $LN9+267
	DD	imagerel $unwind$?IsInstance@JoinZoneContextDest@mu2@@QEBA?B_NXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Setup@JoinZoneContextDest@mu2@@QEAAXAEBVExecution@2@@Z DD imagerel $LN3
	DD	imagerel $LN3+54
	DD	imagerel $unwind$?Setup@JoinZoneContextDest@mu2@@QEAAXAEBVExecution@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?IsCountable@JoinZoneContextDest@mu2@@SA?B_NW4Enum@JoinContext@2@@Z DD imagerel $LN5
	DD	imagerel $LN5+37
	DD	imagerel $unwind$?IsCountable@JoinZoneContextDest@mu2@@SA?B_NW4Enum@JoinContext@2@@Z
pdata	ENDS
;	COMDAT ??_R4JoinZoneContext@mu2@@6B@
rdata$r	SEGMENT
??_R4JoinZoneContext@mu2@@6B@ DD 01H			; mu2::JoinZoneContext::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVJoinZoneContext@mu2@@@8
	DD	imagerel ??_R3JoinZoneContext@mu2@@8
	DD	imagerel ??_R4JoinZoneContext@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@JoinZoneContextCurr@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@JoinZoneContextCurr@mu2@@8 DD imagerel ??_R0?AVJoinZoneContextCurr@mu2@@@8 ; mu2::JoinZoneContextCurr::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3JoinZoneContextCurr@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2JoinZoneContextCurr@mu2@@8
rdata$r	SEGMENT
??_R2JoinZoneContextCurr@mu2@@8 DD imagerel ??_R1A@?0A@EA@JoinZoneContextCurr@mu2@@8 ; mu2::JoinZoneContextCurr::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@JoinZoneContext@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3JoinZoneContextCurr@mu2@@8
rdata$r	SEGMENT
??_R3JoinZoneContextCurr@mu2@@8 DD 00H			; mu2::JoinZoneContextCurr::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2JoinZoneContextCurr@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVJoinZoneContextCurr@mu2@@@8
data$rs	SEGMENT
??_R0?AVJoinZoneContextCurr@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::JoinZoneContextCurr `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVJoinZoneContextCurr@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4JoinZoneContextCurr@mu2@@6B@
rdata$r	SEGMENT
??_R4JoinZoneContextCurr@mu2@@6B@ DD 01H		; mu2::JoinZoneContextCurr::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVJoinZoneContextCurr@mu2@@@8
	DD	imagerel ??_R3JoinZoneContextCurr@mu2@@8
	DD	imagerel ??_R4JoinZoneContextCurr@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R2JoinZoneContext@mu2@@8
rdata$r	SEGMENT
??_R2JoinZoneContext@mu2@@8 DD imagerel ??_R1A@?0A@EA@JoinZoneContext@mu2@@8 ; mu2::JoinZoneContext::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3JoinZoneContext@mu2@@8
rdata$r	SEGMENT
??_R3JoinZoneContext@mu2@@8 DD 00H			; mu2::JoinZoneContext::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2JoinZoneContext@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVJoinZoneContext@mu2@@@8
data$rs	SEGMENT
??_R0?AVJoinZoneContext@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::JoinZoneContext `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVJoinZoneContext@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R1A@?0A@EA@JoinZoneContext@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@JoinZoneContext@mu2@@8 DD imagerel ??_R0?AVJoinZoneContext@mu2@@@8 ; mu2::JoinZoneContext::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3JoinZoneContext@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@JoinZoneContextDest@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@JoinZoneContextDest@mu2@@8 DD imagerel ??_R0?AVJoinZoneContextDest@mu2@@@8 ; mu2::JoinZoneContextDest::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3JoinZoneContextDest@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2JoinZoneContextDest@mu2@@8
rdata$r	SEGMENT
??_R2JoinZoneContextDest@mu2@@8 DD imagerel ??_R1A@?0A@EA@JoinZoneContextDest@mu2@@8 ; mu2::JoinZoneContextDest::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@JoinZoneContext@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3JoinZoneContextDest@mu2@@8
rdata$r	SEGMENT
??_R3JoinZoneContextDest@mu2@@8 DD 00H			; mu2::JoinZoneContextDest::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2JoinZoneContextDest@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVJoinZoneContextDest@mu2@@@8
data$rs	SEGMENT
??_R0?AVJoinZoneContextDest@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::JoinZoneContextDest `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVJoinZoneContextDest@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4JoinZoneContextDest@mu2@@6B@
rdata$r	SEGMENT
??_R4JoinZoneContextDest@mu2@@6B@ DD 01H		; mu2::JoinZoneContextDest::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVJoinZoneContextDest@mu2@@@8
	DD	imagerel ??_R3JoinZoneContextDest@mu2@@8
	DD	imagerel ??_R4JoinZoneContextDest@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_C@_0CE@BCKECPFB@mu2?3?3JoinZoneContextDest?3?3IsCha@
CONST	SEGMENT
??_C@_0CE@BCKECPFB@mu2?3?3JoinZoneContextDest?3?3IsCha@ DB 'mu2::JoinZone'
	DB	'ContextDest::IsChannel', 00H		; `string'
CONST	ENDS
;	COMDAT ??_C@_0BA@NLHNJEHG@instanceId?5?$DN?$DN?50@
CONST	SEGMENT
??_C@_0BA@NLHNJEHG@instanceId?5?$DN?$DN?50@ DB 'instanceId == 0', 00H ; `string'
CONST	ENDS
;	COMDAT ??_C@_0CF@FLLECCEB@mu2?3?3JoinZoneContextDest?3?3IsIns@
CONST	SEGMENT
??_C@_0CF@FLLECCEB@mu2?3?3JoinZoneContextDest?3?3IsIns@ DB 'mu2::JoinZone'
	DB	'ContextDest::IsInstance', 00H		; `string'
CONST	ENDS
;	COMDAT ??_C@_0P@KLKKGGLE@channelId?5?$DN?$DN?50@
CONST	SEGMENT
??_C@_0P@KLKKGGLE@channelId?5?$DN?$DN?50@ DB 'channelId == 0', 00H ; `string'
CONST	ENDS
;	COMDAT ??_C@_0EO@NNODNCE@F?3?2Release_Branch?2Server?2Develo@
CONST	SEGMENT
??_C@_0EO@NNODNCE@F?3?2Release_Branch?2Server?2Develo@ DB 'F:\Release_Bra'
	DB	'nch\Server\Development\Frontend\WorldServer\JoinZoneContext.c'
	DB	'pp', 00H					; `string'
CONST	ENDS
;	COMDAT ??_7JoinZoneContextDest@mu2@@6B@
CONST	SEGMENT
??_7JoinZoneContextDest@mu2@@6B@ DQ FLAT:??_R4JoinZoneContextDest@mu2@@6B@ ; mu2::JoinZoneContextDest::`vftable'
	DQ	FLAT:?Clear@JoinZoneContextDest@mu2@@UEAAXXZ
CONST	ENDS
;	COMDAT ??_7JoinZoneContextCurr@mu2@@6B@
CONST	SEGMENT
??_7JoinZoneContextCurr@mu2@@6B@ DQ FLAT:??_R4JoinZoneContextCurr@mu2@@6B@ ; mu2::JoinZoneContextCurr::`vftable'
	DQ	FLAT:?Clear@JoinZoneContextCurr@mu2@@UEAAXXZ
CONST	ENDS
;	COMDAT ??_7JoinZoneContext@mu2@@6B@
CONST	SEGMENT
??_7JoinZoneContext@mu2@@6B@ DQ FLAT:??_R4JoinZoneContext@mu2@@6B@ ; mu2::JoinZoneContext::`vftable'
	DQ	FLAT:?Clear@JoinZoneContext@mu2@@UEAAXXZ
CONST	ENDS
;	COMDAT ??_R1A@?0A@EA@ExecutionZoneId@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@ExecutionZoneId@mu2@@8 DD imagerel ??_R0?AVExecutionZoneId@mu2@@@8 ; mu2::ExecutionZoneId::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3ExecutionZoneId@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2ExecutionZoneId@mu2@@8
rdata$r	SEGMENT
??_R2ExecutionZoneId@mu2@@8 DD imagerel ??_R1A@?0A@EA@ExecutionZoneId@mu2@@8 ; mu2::ExecutionZoneId::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@ISerializer@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3ExecutionZoneId@mu2@@8
rdata$r	SEGMENT
??_R3ExecutionZoneId@mu2@@8 DD 00H			; mu2::ExecutionZoneId::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2ExecutionZoneId@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVExecutionZoneId@mu2@@@8
data$rs	SEGMENT
??_R0?AVExecutionZoneId@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::ExecutionZoneId `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVExecutionZoneId@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4ExecutionZoneId@mu2@@6B@
rdata$r	SEGMENT
??_R4ExecutionZoneId@mu2@@6B@ DD 01H			; mu2::ExecutionZoneId::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVExecutionZoneId@mu2@@@8
	DD	imagerel ??_R3ExecutionZoneId@mu2@@8
	DD	imagerel ??_R4ExecutionZoneId@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@ISerializer@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@ISerializer@mu2@@8 DD imagerel ??_R0?AUISerializer@mu2@@@8 ; mu2::ISerializer::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3ISerializer@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2ISerializer@mu2@@8
rdata$r	SEGMENT
??_R2ISerializer@mu2@@8 DD imagerel ??_R1A@?0A@EA@ISerializer@mu2@@8 ; mu2::ISerializer::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3ISerializer@mu2@@8
rdata$r	SEGMENT
??_R3ISerializer@mu2@@8 DD 00H				; mu2::ISerializer::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2ISerializer@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AUISerializer@mu2@@@8
data$rs	SEGMENT
??_R0?AUISerializer@mu2@@@8 DQ FLAT:??_7type_info@@6B@	; mu2::ISerializer `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AUISerializer@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4ISerializer@mu2@@6B@
rdata$r	SEGMENT
??_R4ISerializer@mu2@@6B@ DD 01H			; mu2::ISerializer::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AUISerializer@mu2@@@8
	DD	imagerel ??_R3ISerializer@mu2@@8
	DD	imagerel ??_R4ISerializer@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_7ExecutionZoneId@mu2@@6B@
CONST	SEGMENT
??_7ExecutionZoneId@mu2@@6B@ DQ FLAT:??_R4ExecutionZoneId@mu2@@6B@ ; mu2::ExecutionZoneId::`vftable'
	DQ	FLAT:?PackUnpack@ExecutionZoneId@mu2@@UEAA_NAEAVPacketStream@2@@Z
	DQ	FLAT:?Reset@ExecutionZoneId@mu2@@UEAAXXZ
	DQ	FLAT:??_EExecutionZoneId@mu2@@UEAAPEAXI@Z
CONST	ENDS
;	COMDAT ??_7ISerializer@mu2@@6B@
CONST	SEGMENT
??_7ISerializer@mu2@@6B@ DQ FLAT:??_R4ISerializer@mu2@@6B@ ; mu2::ISerializer::`vftable'
	DQ	FLAT:_purecall
	DQ	FLAT:_purecall
	DQ	FLAT:??_EISerializer@mu2@@UEAAPEAXI@Z
CONST	ENDS
;	COMDAT ??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
CONST	SEGMENT
??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@ DB 'g', 00H, 'a', 00H, 'm', 00H, 'e'
	DB	00H, 00H, 00H				; `string'
CONST	ENDS
;	COMDAT ??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
CONST	SEGMENT
??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@ DB '%'
	DB	's> ASSERT - %s, %s(%d)', 00H		; `string'
CONST	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?IsCountable@JoinZoneContextDest@mu2@@SA?B_NW4Enum@JoinContext@2@@Z DD 010801H
	DD	02208H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Setup@JoinZoneContextDest@mu2@@QEAAXAEBVExecution@2@@Z DD 010e01H
	DD	0620eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?IsInstance@JoinZoneContextDest@mu2@@QEBA?B_NXZ DD 010901H
	DD	0e209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?IsChannel@JoinZoneContextDest@mu2@@QEBA?B_NXZ DD 010901H
	DD	0e209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Clear@JoinZoneContextDest@mu2@@UEAAXXZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0JoinZoneContextDest@mu2@@QEAA@AEBVExecutionZoneId@1@AEBH@Z DD 011301H
	DD	04213H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0JoinZoneContextDest@mu2@@QEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Setup@JoinZoneContextCurr@mu2@@QEAAXAEBVExecution@2@@Z DD 010e01H
	DD	0820eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Clear@JoinZoneContextCurr@mu2@@UEAAXXZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0JoinZoneContextCurr@mu2@@QEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?IsValid@JoinZoneContext@mu2@@QEBA?B_NXZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Clear@JoinZoneContext@mu2@@UEAAXXZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0JoinZoneContext@mu2@@QEAA@AEBVExecutionZoneId@1@@Z DD 010e01H
	DD	0220eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0JoinZoneContext@mu2@@QEAA@XZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GExecutionZoneId@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GISerializer@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContext.cpp
;	COMDAT ?IsCountable@JoinZoneContextDest@mu2@@SA?B_NW4Enum@JoinContext@2@@Z
_TEXT	SEGMENT
tv66 = 0
eJoinContext$ = 32
?IsCountable@JoinZoneContextDest@mu2@@SA?B_NW4Enum@JoinContext@2@@Z PROC ; mu2::JoinZoneContextDest::IsCountable, COMDAT

; 118  : 	{

$LN5:
  00000	88 4c 24 08	 mov	 BYTE PTR [rsp+8], cl
  00004	48 83 ec 18	 sub	 rsp, 24

; 119  : 		// 워프이동은 LeaveExecution처리를 안하므로 유저카운트 증가를 제외해야한다. 
; 120  : 		return eJoinContext != JoinContext::JC_WARP_MAP;

  00008	0f b6 44 24 20	 movzx	 eax, BYTE PTR eJoinContext$[rsp]
  0000d	83 f8 03	 cmp	 eax, 3
  00010	74 06		 je	 SHORT $LN3@IsCountabl
  00012	c6 04 24 01	 mov	 BYTE PTR tv66[rsp], 1
  00016	eb 04		 jmp	 SHORT $LN4@IsCountabl
$LN3@IsCountabl:
  00018	c6 04 24 00	 mov	 BYTE PTR tv66[rsp], 0
$LN4@IsCountabl:
  0001c	0f b6 04 24	 movzx	 eax, BYTE PTR tv66[rsp]

; 121  : 	}	

  00020	48 83 c4 18	 add	 rsp, 24
  00024	c3		 ret	 0
?IsCountable@JoinZoneContextDest@mu2@@SA?B_NW4Enum@JoinContext@2@@Z ENDP ; mu2::JoinZoneContextDest::IsCountable
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContext.cpp
;	COMDAT ?Setup@JoinZoneContextDest@mu2@@QEAAXAEBVExecution@2@@Z
_TEXT	SEGMENT
dest$ = 32
this$ = 64
pExecution$ = 72
?Setup@JoinZoneContextDest@mu2@@QEAAXAEBVExecution@2@@Z PROC ; mu2::JoinZoneContextDest::Setup, COMDAT

; 124  : 	{

$LN3:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 125  : 		JoinZoneContextDest* dest = this;

  0000e	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00013	48 89 44 24 20	 mov	 QWORD PTR dest$[rsp], rax

; 126  : 		pExecution.Fillup(*dest);

  00018	48 8b 44 24 48	 mov	 rax, QWORD PTR pExecution$[rsp]
  0001d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00020	48 8b 54 24 20	 mov	 rdx, QWORD PTR dest$[rsp]
  00025	48 8b 4c 24 48	 mov	 rcx, QWORD PTR pExecution$[rsp]
  0002a	ff 90 f0 00 00
	00		 call	 QWORD PTR [rax+240]
  00030	90		 npad	 1

; 127  : 	}

  00031	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00035	c3		 ret	 0
?Setup@JoinZoneContextDest@mu2@@QEAAXAEBVExecution@2@@Z ENDP ; mu2::JoinZoneContextDest::Setup
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContext.cpp
;	COMDAT ?IsInstance@JoinZoneContextDest@mu2@@QEBA?B_NXZ
_TEXT	SEGMENT
tv87 = 96
tv90 = 97
hConsole$1 = 104
this$ = 128
?IsInstance@JoinZoneContextDest@mu2@@QEBA?B_NXZ PROC	; mu2::JoinZoneContextDest::IsInstance, COMDAT

; 130  : 	{

$LN9:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 78	 sub	 rsp, 120		; 00000078H

; 131  : 		if (instanceId > 0)

  00009	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00011	83 78 44 00	 cmp	 DWORD PTR [rax+68], 0
  00015	0f 86 cc 00 00
	00		 jbe	 $LN2@IsInstance

; 132  : 		{
; 133  : 			VERIFY_RETURN( channelId == 0, instanceId > 0)

  0001b	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00023	83 78 38 00	 cmp	 DWORD PTR [rax+56], 0
  00027	0f 84 ba 00 00
	00		 je	 $LN3@IsInstance
  0002d	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  00032	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  00038	48 89 44 24 68	 mov	 QWORD PTR hConsole$1[rsp], rax
  0003d	66 ba 0d 00	 mov	 dx, 13
  00041	48 8b 4c 24 68	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  00046	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  0004c	c7 44 24 50 85
	00 00 00	 mov	 DWORD PTR [rsp+80], 133	; 00000085H
  00054	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EO@NNODNCE@F?3?2Release_Branch?2Server?2Develo@
  0005b	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  00060	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0P@KLKKGGLE@channelId?5?$DN?$DN?50@
  00067	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  0006c	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CF@FLLECCEB@mu2?3?3JoinZoneContextDest?3?3IsIns@
  00073	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  00078	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  0007f	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  00084	c7 44 24 28 85
	00 00 00	 mov	 DWORD PTR [rsp+40], 133	; 00000085H
  0008c	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EO@NNODNCE@F?3?2Release_Branch?2Server?2Develo@
  00093	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  00098	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CF@FLLECCEB@mu2?3?3JoinZoneContextDest?3?3IsIns@
  0009f	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  000a5	ba 02 00 00 00	 mov	 edx, 2
  000aa	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  000b1	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  000b6	66 ba 07 00	 mov	 dx, 7
  000ba	48 8b 4c 24 68	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  000bf	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000c5	90		 npad	 1
  000c6	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000ce	83 78 44 00	 cmp	 DWORD PTR [rax+68], 0
  000d2	76 07		 jbe	 SHORT $LN5@IsInstance
  000d4	c6 44 24 60 01	 mov	 BYTE PTR tv87[rsp], 1
  000d9	eb 05		 jmp	 SHORT $LN6@IsInstance
$LN5@IsInstance:
  000db	c6 44 24 60 00	 mov	 BYTE PTR tv87[rsp], 0
$LN6@IsInstance:
  000e0	0f b6 44 24 60	 movzx	 eax, BYTE PTR tv87[rsp]
  000e5	eb 1f		 jmp	 SHORT $LN1@IsInstance
$LN3@IsInstance:
$LN2@IsInstance:

; 134  : 		}
; 135  : 		
; 136  : 		return instanceId > 0;

  000e7	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000ef	83 78 44 00	 cmp	 DWORD PTR [rax+68], 0
  000f3	76 07		 jbe	 SHORT $LN7@IsInstance
  000f5	c6 44 24 61 01	 mov	 BYTE PTR tv90[rsp], 1
  000fa	eb 05		 jmp	 SHORT $LN8@IsInstance
$LN7@IsInstance:
  000fc	c6 44 24 61 00	 mov	 BYTE PTR tv90[rsp], 0
$LN8@IsInstance:
  00101	0f b6 44 24 61	 movzx	 eax, BYTE PTR tv90[rsp]
$LN1@IsInstance:

; 137  : 	}

  00106	48 83 c4 78	 add	 rsp, 120		; 00000078H
  0010a	c3		 ret	 0
?IsInstance@JoinZoneContextDest@mu2@@QEBA?B_NXZ ENDP	; mu2::JoinZoneContextDest::IsInstance
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContext.cpp
;	COMDAT ?IsChannel@JoinZoneContextDest@mu2@@QEBA?B_NXZ
_TEXT	SEGMENT
tv87 = 96
tv90 = 97
hConsole$1 = 104
this$ = 128
?IsChannel@JoinZoneContextDest@mu2@@QEBA?B_NXZ PROC	; mu2::JoinZoneContextDest::IsChannel, COMDAT

; 140  : 	{

$LN9:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 78	 sub	 rsp, 120		; 00000078H

; 141  : 		if (channelId > 0)

  00009	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00011	83 78 38 00	 cmp	 DWORD PTR [rax+56], 0
  00015	0f 86 cc 00 00
	00		 jbe	 $LN2@IsChannel

; 142  : 		{
; 143  : 			VERIFY_RETURN(instanceId == 0, channelId > 0)

  0001b	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00023	83 78 44 00	 cmp	 DWORD PTR [rax+68], 0
  00027	0f 84 ba 00 00
	00		 je	 $LN3@IsChannel
  0002d	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  00032	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  00038	48 89 44 24 68	 mov	 QWORD PTR hConsole$1[rsp], rax
  0003d	66 ba 0d 00	 mov	 dx, 13
  00041	48 8b 4c 24 68	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  00046	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  0004c	c7 44 24 50 8f
	00 00 00	 mov	 DWORD PTR [rsp+80], 143	; 0000008fH
  00054	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EO@NNODNCE@F?3?2Release_Branch?2Server?2Develo@
  0005b	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  00060	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BA@NLHNJEHG@instanceId?5?$DN?$DN?50@
  00067	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  0006c	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CE@BCKECPFB@mu2?3?3JoinZoneContextDest?3?3IsCha@
  00073	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  00078	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  0007f	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  00084	c7 44 24 28 8f
	00 00 00	 mov	 DWORD PTR [rsp+40], 143	; 0000008fH
  0008c	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EO@NNODNCE@F?3?2Release_Branch?2Server?2Develo@
  00093	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  00098	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CE@BCKECPFB@mu2?3?3JoinZoneContextDest?3?3IsCha@
  0009f	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  000a5	ba 02 00 00 00	 mov	 edx, 2
  000aa	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  000b1	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  000b6	66 ba 07 00	 mov	 dx, 7
  000ba	48 8b 4c 24 68	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  000bf	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000c5	90		 npad	 1
  000c6	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000ce	83 78 38 00	 cmp	 DWORD PTR [rax+56], 0
  000d2	76 07		 jbe	 SHORT $LN5@IsChannel
  000d4	c6 44 24 60 01	 mov	 BYTE PTR tv87[rsp], 1
  000d9	eb 05		 jmp	 SHORT $LN6@IsChannel
$LN5@IsChannel:
  000db	c6 44 24 60 00	 mov	 BYTE PTR tv87[rsp], 0
$LN6@IsChannel:
  000e0	0f b6 44 24 60	 movzx	 eax, BYTE PTR tv87[rsp]
  000e5	eb 1f		 jmp	 SHORT $LN1@IsChannel
$LN3@IsChannel:
$LN2@IsChannel:

; 144  : 		}
; 145  : 		return channelId > 0;

  000e7	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000ef	83 78 38 00	 cmp	 DWORD PTR [rax+56], 0
  000f3	76 07		 jbe	 SHORT $LN7@IsChannel
  000f5	c6 44 24 61 01	 mov	 BYTE PTR tv90[rsp], 1
  000fa	eb 05		 jmp	 SHORT $LN8@IsChannel
$LN7@IsChannel:
  000fc	c6 44 24 61 00	 mov	 BYTE PTR tv90[rsp], 0
$LN8@IsChannel:
  00101	0f b6 44 24 61	 movzx	 eax, BYTE PTR tv90[rsp]
$LN1@IsChannel:

; 146  : 	}

  00106	48 83 c4 78	 add	 rsp, 120		; 00000078H
  0010a	c3		 ret	 0
?IsChannel@JoinZoneContextDest@mu2@@QEBA?B_NXZ ENDP	; mu2::JoinZoneContextDest::IsChannel
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContext.cpp
;	COMDAT ?Clear@JoinZoneContextDest@mu2@@UEAAXXZ
_TEXT	SEGMENT
this$ = 48
?Clear@JoinZoneContextDest@mu2@@UEAAXXZ PROC		; mu2::JoinZoneContextDest::Clear, COMDAT

; 105  : 	{

$LN3:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 106  : 		indexPosition = 0;

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	c7 40 30 00 00
	00 00		 mov	 DWORD PTR [rax+48], 0

; 107  : 		continent = 0;

  00015	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001a	c6 40 34 00	 mov	 BYTE PTR [rax+52], 0

; 108  : 		channelId = 0;

  0001e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00023	c7 40 38 00 00
	00 00		 mov	 DWORD PTR [rax+56], 0

; 109  : 		eDungeonType = DungeonType::INVALID_TYPE;

  0002a	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0002f	c7 40 3c 00 00
	00 00		 mov	 DWORD PTR [rax+60], 0

; 110  : 		portalInfoIndex = 0;

  00036	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003b	c7 40 40 00 00
	00 00		 mov	 DWORD PTR [rax+64], 0

; 111  : 		instanceId = 0;		

  00042	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00047	c7 40 44 00 00
	00 00		 mov	 DWORD PTR [rax+68], 0

; 112  : 		relayServerId = 0;

  0004e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00053	c7 40 48 00 00
	00 00		 mov	 DWORD PTR [rax+72], 0

; 113  : 
; 114  : 		__super::Clear();

  0005a	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0005f	e8 00 00 00 00	 call	 ?Clear@JoinZoneContext@mu2@@UEAAXXZ ; mu2::JoinZoneContext::Clear
  00064	90		 npad	 1

; 115  : 	}

  00065	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00069	c3		 ret	 0
?Clear@JoinZoneContextDest@mu2@@UEAAXXZ ENDP		; mu2::JoinZoneContextDest::Clear
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContext.cpp
;	COMDAT ??0JoinZoneContextDest@mu2@@QEAA@AEBVExecutionZoneId@1@AEBH@Z
_TEXT	SEGMENT
this$ = 48
execId$ = 56
indexPosition$ = 64
??0JoinZoneContextDest@mu2@@QEAA@AEBVExecutionZoneId@1@AEBH@Z PROC ; mu2::JoinZoneContextDest::JoinZoneContextDest, COMDAT

; 101  : 	{

$LN4:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 99   : 		: JoinZoneContext(execId)

  00013	48 8b 54 24 38	 mov	 rdx, QWORD PTR execId$[rsp]
  00018	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0001d	e8 00 00 00 00	 call	 ??0JoinZoneContext@mu2@@QEAA@AEBVExecutionZoneId@1@@Z ; mu2::JoinZoneContext::JoinZoneContext

; 101  : 	{

  00022	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00027	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7JoinZoneContextDest@mu2@@6B@
  0002e	48 89 08	 mov	 QWORD PTR [rax], rcx

; 100  : 		, indexPosition(indexPosition)

  00031	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00036	48 8b 4c 24 40	 mov	 rcx, QWORD PTR indexPosition$[rsp]
  0003b	8b 09		 mov	 ecx, DWORD PTR [rcx]
  0003d	89 48 30	 mov	 DWORD PTR [rax+48], ecx

; 102  : 	}

  00040	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00045	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00049	c3		 ret	 0
??0JoinZoneContextDest@mu2@@QEAA@AEBVExecutionZoneId@1@AEBH@Z ENDP ; mu2::JoinZoneContextDest::JoinZoneContextDest
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContext.cpp
;	COMDAT ??0JoinZoneContextDest@mu2@@QEAA@XZ
_TEXT	SEGMENT
this$ = 48
??0JoinZoneContextDest@mu2@@QEAA@XZ PROC		; mu2::JoinZoneContextDest::JoinZoneContextDest, COMDAT

; 94   : 	{

$LN4:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 85   : 		: JoinZoneContext()

  00009	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0000e	e8 00 00 00 00	 call	 ??0JoinZoneContext@mu2@@QEAA@XZ ; mu2::JoinZoneContext::JoinZoneContext

; 94   : 	{

  00013	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00018	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7JoinZoneContextDest@mu2@@6B@
  0001f	48 89 08	 mov	 QWORD PTR [rax], rcx

; 86   : 		, indexPosition(0)

  00022	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00027	c7 40 30 00 00
	00 00		 mov	 DWORD PTR [rax+48], 0

; 87   : 		, continent(0)

  0002e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00033	c6 40 34 00	 mov	 BYTE PTR [rax+52], 0

; 88   : 		, channelId(0)

  00037	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003c	c7 40 38 00 00
	00 00		 mov	 DWORD PTR [rax+56], 0

; 89   : 		, eDungeonType(DungeonType::INVALID_TYPE)

  00043	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00048	c7 40 3c 00 00
	00 00		 mov	 DWORD PTR [rax+60], 0

; 90   : 		, portalInfoIndex(0)

  0004f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00054	c7 40 40 00 00
	00 00		 mov	 DWORD PTR [rax+64], 0

; 91   : 		, instanceId(0)		

  0005b	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00060	c7 40 44 00 00
	00 00		 mov	 DWORD PTR [rax+68], 0

; 92   : 		, relayServerId(0)

  00067	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0006c	c7 40 48 00 00
	00 00		 mov	 DWORD PTR [rax+72], 0

; 95   : 	
; 96   : 	}

  00073	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00078	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0007c	c3		 ret	 0
??0JoinZoneContextDest@mu2@@QEAA@XZ ENDP		; mu2::JoinZoneContextDest::JoinZoneContextDest
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContext.cpp
;	COMDAT ?Setup@JoinZoneContextCurr@mu2@@QEAAXAEBVExecution@2@@Z
_TEXT	SEGMENT
tv84 = 32
this$ = 40
$T1 = 48
this$ = 80
exec$ = 88
?Setup@JoinZoneContextCurr@mu2@@QEAAXAEBVExecution@2@@Z PROC ; mu2::JoinZoneContextCurr::Setup, COMDAT

; 75   : 	{

$LN15:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 76   : 		execId = exec.GetExecId().IsValid() ? exec.GetExecId() : execId;

  0000e	48 8b 4c 24 58	 mov	 rcx, QWORD PTR exec$[rsp]
  00013	e8 00 00 00 00	 call	 ?GetExecId@Execution@mu2@@QEBAAEBVExecutionZoneId@2@XZ ; mu2::Execution::GetExecId
  00018	48 8b c8	 mov	 rcx, rax
  0001b	e8 00 00 00 00	 call	 ?IsValid@ExecutionZoneId@mu2@@QEBA?B_NXZ ; mu2::ExecutionZoneId::IsValid
  00020	0f b6 c0	 movzx	 eax, al
  00023	85 c0		 test	 eax, eax
  00025	74 11		 je	 SHORT $LN3@Setup
  00027	48 8b 4c 24 58	 mov	 rcx, QWORD PTR exec$[rsp]
  0002c	e8 00 00 00 00	 call	 ?GetExecId@Execution@mu2@@QEBAAEBVExecutionZoneId@2@XZ ; mu2::Execution::GetExecId
  00031	48 89 44 24 20	 mov	 QWORD PTR tv84[rsp], rax
  00036	eb 0e		 jmp	 SHORT $LN4@Setup
$LN3@Setup:
  00038	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0003d	48 83 c0 10	 add	 rax, 16
  00041	48 89 44 24 20	 mov	 QWORD PTR tv84[rsp], rax
$LN4@Setup:
  00046	48 8b 44 24 20	 mov	 rax, QWORD PTR tv84[rsp]
  0004b	48 89 44 24 30	 mov	 QWORD PTR $T1[rsp], rax
  00050	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00055	48 83 c0 10	 add	 rax, 16
  00059	48 89 44 24 28	 mov	 QWORD PTR this$[rsp], rax
  0005e	48 8b 44 24 30	 mov	 rax, QWORD PTR $T1[rsp]
  00063	8b 40 08	 mov	 eax, DWORD PTR [rax+8]
  00066	48 8b 4c 24 28	 mov	 rcx, QWORD PTR this$[rsp]
  0006b	89 41 08	 mov	 DWORD PTR [rcx+8], eax
  0006e	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00073	48 8b 4c 24 30	 mov	 rcx, QWORD PTR $T1[rsp]
  00078	8b 49 08	 mov	 ecx, DWORD PTR [rcx+8]
  0007b	89 48 08	 mov	 DWORD PTR [rax+8], ecx

; 77   : 	}

  0007e	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00082	c3		 ret	 0
?Setup@JoinZoneContextCurr@mu2@@QEAAXAEBVExecution@2@@Z ENDP ; mu2::JoinZoneContextCurr::Setup
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContext.cpp
;	COMDAT ?Clear@JoinZoneContextCurr@mu2@@UEAAXXZ
_TEXT	SEGMENT
this$ = 48
?Clear@JoinZoneContextCurr@mu2@@UEAAXXZ PROC		; mu2::JoinZoneContextCurr::Clear, COMDAT

; 68   : 	{

$LN3:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 69   : 		beginPositionIndex = 0;

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	c7 40 30 00 00
	00 00		 mov	 DWORD PTR [rax+48], 0

; 70   : 
; 71   : 		__super::Clear();

  00015	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0001a	e8 00 00 00 00	 call	 ?Clear@JoinZoneContext@mu2@@UEAAXXZ ; mu2::JoinZoneContext::Clear
  0001f	90		 npad	 1

; 72   : 	}

  00020	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00024	c3		 ret	 0
?Clear@JoinZoneContextCurr@mu2@@UEAAXXZ ENDP		; mu2::JoinZoneContextCurr::Clear
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContext.cpp
;	COMDAT ??0JoinZoneContextCurr@mu2@@QEAA@XZ
_TEXT	SEGMENT
this$ = 48
??0JoinZoneContextCurr@mu2@@QEAA@XZ PROC		; mu2::JoinZoneContextCurr::JoinZoneContextCurr, COMDAT

; 63   : 	{

$LN4:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 62   : 		: JoinZoneContext()

  00009	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0000e	e8 00 00 00 00	 call	 ??0JoinZoneContext@mu2@@QEAA@XZ ; mu2::JoinZoneContext::JoinZoneContext

; 63   : 	{

  00013	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00018	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7JoinZoneContextCurr@mu2@@6B@
  0001f	48 89 08	 mov	 QWORD PTR [rax], rcx

; 64   : 		beginPositionIndex = 0;

  00022	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00027	c7 40 30 00 00
	00 00		 mov	 DWORD PTR [rax+48], 0

; 65   : 	}

  0002e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00033	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00037	c3		 ret	 0
??0JoinZoneContextCurr@mu2@@QEAA@XZ ENDP		; mu2::JoinZoneContextCurr::JoinZoneContextCurr
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContext.cpp
;	COMDAT ?IsValid@JoinZoneContext@mu2@@QEBA?B_NXZ
_TEXT	SEGMENT
tv71 = 32
this$ = 64
?IsValid@JoinZoneContext@mu2@@QEBA?B_NXZ PROC		; mu2::JoinZoneContext::IsValid, COMDAT

; 53   : 	{

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 54   : 		return zoneServerId > 0 && execId.IsValid() /* && !position.IsZero()*/;

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	83 78 08 00	 cmp	 DWORD PTR [rax+8], 0
  00012	76 1f		 jbe	 SHORT $LN3@IsValid
  00014	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00019	48 83 c0 10	 add	 rax, 16
  0001d	48 8b c8	 mov	 rcx, rax
  00020	e8 00 00 00 00	 call	 ?IsValid@ExecutionZoneId@mu2@@QEBA?B_NXZ ; mu2::ExecutionZoneId::IsValid
  00025	0f b6 c0	 movzx	 eax, al
  00028	85 c0		 test	 eax, eax
  0002a	74 07		 je	 SHORT $LN3@IsValid
  0002c	c6 44 24 20 01	 mov	 BYTE PTR tv71[rsp], 1
  00031	eb 05		 jmp	 SHORT $LN4@IsValid
$LN3@IsValid:
  00033	c6 44 24 20 00	 mov	 BYTE PTR tv71[rsp], 0
$LN4@IsValid:
  00038	0f b6 44 24 20	 movzx	 eax, BYTE PTR tv71[rsp]

; 55   : 	}

  0003d	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00041	c3		 ret	 0
?IsValid@JoinZoneContext@mu2@@QEBA?B_NXZ ENDP		; mu2::JoinZoneContext::IsValid
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContext.cpp
; File F:\Release_Branch\Server\Development\Framework\Math\Vector3.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContext.cpp
;	COMDAT ?Clear@JoinZoneContext@mu2@@UEAAXXZ
_TEXT	SEGMENT
$T1 = 32
this$ = 40
this$ = 64
?Clear@JoinZoneContext@mu2@@UEAAXXZ PROC		; mu2::JoinZoneContext::Clear, COMDAT

; 46   : 	{

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 47   : 		zoneServerId = 0;

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	c7 40 08 00 00
	00 00		 mov	 DWORD PTR [rax+8], 0

; 48   : 		execId = (ExecUnique)0;

  00015	c7 44 24 20 00
	00 00 00	 mov	 DWORD PTR $T1[rsp], 0
  0001d	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00022	48 83 c0 10	 add	 rax, 16
  00026	48 8d 54 24 20	 lea	 rdx, QWORD PTR $T1[rsp]
  0002b	48 8b c8	 mov	 rcx, rax
  0002e	e8 00 00 00 00	 call	 ??4ExecutionZoneId@mu2@@QEAAAEAV01@AEBI@Z ; mu2::ExecutionZoneId::operator=
  00033	90		 npad	 1

; 49   : 		position.clear();

  00034	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00039	48 83 c0 20	 add	 rax, 32			; 00000020H
  0003d	48 89 44 24 28	 mov	 QWORD PTR this$[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Math\Vector3.h

; 154  : 	x = y = z = 0.0f;

  00042	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00047	0f 57 c0	 xorps	 xmm0, xmm0
  0004a	f3 0f 11 40 08	 movss	 DWORD PTR [rax+8], xmm0
  0004f	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00054	0f 57 c0	 xorps	 xmm0, xmm0
  00057	f3 0f 11 40 04	 movss	 DWORD PTR [rax+4], xmm0
  0005c	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00061	0f 57 c0	 xorps	 xmm0, xmm0
  00064	f3 0f 11 00	 movss	 DWORD PTR [rax], xmm0
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContext.cpp

; 50   : 	}

  00068	48 83 c4 38	 add	 rsp, 56			; 00000038H
  0006c	c3		 ret	 0
?Clear@JoinZoneContext@mu2@@UEAAXXZ ENDP		; mu2::JoinZoneContext::Clear
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContext.cpp
; File F:\Release_Branch\Server\Development\Framework\Math\Vector3.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContext.cpp
;	COMDAT ??0JoinZoneContext@mu2@@QEAA@AEBVExecutionZoneId@1@@Z
_TEXT	SEGMENT
this$ = 0
this$ = 8
this$ = 32
_execId$ = 40
??0JoinZoneContext@mu2@@QEAA@AEBVExecutionZoneId@1@@Z PROC ; mu2::JoinZoneContext::JoinZoneContext, COMDAT

; 42   : 	{

$LN20:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 18	 sub	 rsp, 24
  0000e	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00013	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7JoinZoneContext@mu2@@6B@
  0001a	48 89 08	 mov	 QWORD PTR [rax], rcx

; 40   : 		, zoneServerId(0)

  0001d	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00022	c7 40 08 00 00
	00 00		 mov	 DWORD PTR [rax+8], 0

; 39   : 		: execId(_execId)

  00029	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0002e	48 83 c0 10	 add	 rax, 16
  00032	48 89 04 24	 mov	 QWORD PTR this$[rsp], rax
  00036	48 8b 04 24	 mov	 rax, QWORD PTR this$[rsp]
  0003a	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  00041	48 89 08	 mov	 QWORD PTR [rax], rcx
  00044	48 8b 04 24	 mov	 rax, QWORD PTR this$[rsp]
  00048	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ExecutionZoneId@mu2@@6B@
  0004f	48 89 08	 mov	 QWORD PTR [rax], rcx
  00052	48 8b 44 24 28	 mov	 rax, QWORD PTR _execId$[rsp]
  00057	8b 40 08	 mov	 eax, DWORD PTR [rax+8]
  0005a	48 8b 0c 24	 mov	 rcx, QWORD PTR this$[rsp]
  0005e	89 41 08	 mov	 DWORD PTR [rcx+8], eax
  00061	48 8b 04 24	 mov	 rax, QWORD PTR this$[rsp]
  00065	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _execId$[rsp]
  0006a	8b 49 08	 mov	 ecx, DWORD PTR [rcx+8]
  0006d	89 48 08	 mov	 DWORD PTR [rax+8], ecx

; 41   : 		, position()

  00070	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00075	48 83 c0 20	 add	 rax, 32			; 00000020H
  00079	48 89 44 24 08	 mov	 QWORD PTR this$[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Math\Vector3.h

; 13   : 	inline Vector3() : x(0.f), y(0.f), z(0.f) {}

  0007e	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00083	0f 57 c0	 xorps	 xmm0, xmm0
  00086	f3 0f 11 00	 movss	 DWORD PTR [rax], xmm0
  0008a	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0008f	0f 57 c0	 xorps	 xmm0, xmm0
  00092	f3 0f 11 40 04	 movss	 DWORD PTR [rax+4], xmm0
  00097	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0009c	0f 57 c0	 xorps	 xmm0, xmm0
  0009f	f3 0f 11 40 08	 movss	 DWORD PTR [rax+8], xmm0
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContext.cpp

; 43   : 	}

  000a4	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  000a9	48 83 c4 18	 add	 rsp, 24
  000ad	c3		 ret	 0
??0JoinZoneContext@mu2@@QEAA@AEBVExecutionZoneId@1@@Z ENDP ; mu2::JoinZoneContext::JoinZoneContext
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContext.cpp
; File F:\Release_Branch\Server\Development\Framework\Math\Vector3.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContext.cpp
;	COMDAT ??0JoinZoneContext@mu2@@QEAA@XZ
_TEXT	SEGMENT
this$ = 32
this$ = 64
??0JoinZoneContext@mu2@@QEAA@XZ PROC			; mu2::JoinZoneContext::JoinZoneContext, COMDAT

; 35   : 	{

$LN10:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H
  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7JoinZoneContext@mu2@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx

; 32   : 		: zoneServerId(0)

  00018	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0001d	c7 40 08 00 00
	00 00		 mov	 DWORD PTR [rax+8], 0

; 33   : 		, execId()

  00024	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00029	48 83 c0 10	 add	 rax, 16
  0002d	48 8b c8	 mov	 rcx, rax
  00030	e8 00 00 00 00	 call	 ??0ExecutionZoneId@mu2@@QEAA@XZ ; mu2::ExecutionZoneId::ExecutionZoneId

; 34   : 		, position()

  00035	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0003a	48 83 c0 20	 add	 rax, 32			; 00000020H
  0003e	48 89 44 24 20	 mov	 QWORD PTR this$[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Math\Vector3.h

; 13   : 	inline Vector3() : x(0.f), y(0.f), z(0.f) {}

  00043	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00048	0f 57 c0	 xorps	 xmm0, xmm0
  0004b	f3 0f 11 00	 movss	 DWORD PTR [rax], xmm0
  0004f	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00054	0f 57 c0	 xorps	 xmm0, xmm0
  00057	f3 0f 11 40 04	 movss	 DWORD PTR [rax+4], xmm0
  0005c	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00061	0f 57 c0	 xorps	 xmm0, xmm0
  00064	f3 0f 11 40 08	 movss	 DWORD PTR [rax+8], xmm0
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContext.cpp

; 36   : 	}

  00069	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0006e	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00072	c3		 ret	 0
??0JoinZoneContext@mu2@@QEAA@XZ ENDP			; mu2::JoinZoneContext::JoinZoneContext
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ??_GExecutionZoneId@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GExecutionZoneId@mu2@@UEAAPEAXI@Z PROC		; mu2::ExecutionZoneId::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 172  : 	virtual~ISerializer() {}

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx
  0001c	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00020	83 e0 01	 and	 eax, 1
  00023	85 c0		 test	 eax, eax
  00025	74 10		 je	 SHORT $LN2@scalar
  00027	ba 10 00 00 00	 mov	 edx, 16
  0002c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00031	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00036	90		 npad	 1
$LN2@scalar:
  00037	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003c	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00040	c3		 ret	 0
??_GExecutionZoneId@mu2@@UEAAPEAXI@Z ENDP		; mu2::ExecutionZoneId::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ??_GISerializer@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GISerializer@mu2@@UEAAPEAXI@Z PROC			; mu2::ISerializer::`scalar deleting destructor', COMDAT
$LN10:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 172  : 	virtual~ISerializer() {}

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx
  0001c	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00020	83 e0 01	 and	 eax, 1
  00023	85 c0		 test	 eax, eax
  00025	74 10		 je	 SHORT $LN2@scalar
  00027	ba 08 00 00 00	 mov	 edx, 8
  0002c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00031	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00036	90		 npad	 1
$LN2@scalar:
  00037	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003c	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00040	c3		 ret	 0
??_GISerializer@mu2@@UEAAPEAXI@Z ENDP			; mu2::ISerializer::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContext.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContext.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
