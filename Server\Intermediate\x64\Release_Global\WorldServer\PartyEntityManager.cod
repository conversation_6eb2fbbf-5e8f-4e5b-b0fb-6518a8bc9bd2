; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	??0exception@std@@QEAA@AEBV01@@Z		; std::exception::exception
PUBLIC	?what@exception@std@@UEBAPEBDXZ			; std::exception::what
PUBLIC	??_Gexception@std@@UEAAPEAXI@Z			; std::exception::`scalar deleting destructor'
PUBLIC	??0bad_alloc@std@@QEAA@AEBV01@@Z		; std::bad_alloc::bad_alloc
PUBLIC	??_Gbad_alloc@std@@UEAAPEAXI@Z			; std::bad_alloc::`scalar deleting destructor'
PUBL<PERSON>	??0bad_array_new_length@std@@QEAA@XZ		; std::bad_array_new_length::bad_array_new_length
PUBLIC	??1bad_array_new_length@std@@UEAA@XZ		; std::bad_array_new_length::~bad_array_new_length
PUBLIC	??0bad_array_new_length@std@@QEAA@AEBV01@@Z	; std::bad_array_new_length::bad_array_new_length
PUBLIC	??_Gbad_array_new_length@std@@UEAAPEAXI@Z	; std::bad_array_new_length::`scalar deleting destructor'
PUBLIC	?_Throw_bad_array_new_length@std@@YAXXZ		; std::_Throw_bad_array_new_length
PUBLIC	?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
PUBLIC	?_Throw_tree_length_error@std@@YAXXZ		; std::_Throw_tree_length_error
PUBLIC	??0ManagerPartyEntity@mu2@@QEAA@XZ		; mu2::ManagerPartyEntity::ManagerPartyEntity
PUBLIC	??1ManagerPartyEntity@mu2@@UEAA@XZ		; mu2::ManagerPartyEntity::~ManagerPartyEntity
PUBLIC	?OnTick@ManagerPartyEntity@mu2@@QEAAXXZ		; mu2::ManagerPartyEntity::OnTick
PUBLIC	?Insert@ManagerPartyEntity@mu2@@QEAA_NPEAVEntityParty@2@@Z ; mu2::ManagerPartyEntity::Insert
PUBLIC	?Remove@ManagerPartyEntity@mu2@@QEAAXI@Z	; mu2::ManagerPartyEntity::Remove
PUBLIC	?GetEntity@ManagerPartyEntity@mu2@@QEAAPEAVEntityParty@2@I@Z ; mu2::ManagerPartyEntity::GetEntity
PUBLIC	?remove_i@ManagerPartyEntity@mu2@@AEAA_NI@Z	; mu2::ManagerPartyEntity::remove_i
PUBLIC	?allocate@?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@_K@Z ; std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> >::allocate
PUBLIC	??1?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@QEAA@XZ ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::~_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >
PUBLIC	?max_size@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@QEBA_KXZ ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::max_size
PUBLIC	?_Erase_unchecked@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@AEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::_Erase_unchecked
PUBLIC	?erase@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@QEAA?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@@2@V?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@@2@@Z ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::erase
PUBLIC	?_Check_grow_by_1@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEAAXXZ ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::_Check_grow_by_1
PUBLIC	?_Alloc_sentinel_and_proxy@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEAAXXZ ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::_Alloc_sentinel_and_proxy
PUBLIC	?_Lrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >::_Lrotate
PUBLIC	?_Rrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >::_Rrotate
PUBLIC	?_Extract@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >::_Extract
PUBLIC	?_Insert_node@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@U?$_Tree_id@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@2@QEAU32@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >::_Insert_node
PUBLIC	??0?$map@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@@std@@QEAA@XZ ; std::map<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> > >::map<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> > >
PUBLIC	??_GManagerPartyEntity@mu2@@UEAAPEAXI@Z		; mu2::ManagerPartyEntity::`scalar deleting destructor'
PUBLIC	??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
PUBLIC	??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ ; std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >,std::_Iterator_base0>::operator++
PUBLIC	??$insert@$0A@$0A@@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@QEAA?AU?$pair@V?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@@std@@_N@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::insert<0,0>
PUBLIC	??$_Emplace@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@_N@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::_Emplace<std::pair<unsigned int const ,mu2::EntityParty *> >
PUBLIC	??$_Find@I@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@AEBAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@AEBI@Z ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::_Find<unsigned int>
PUBLIC	??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >::_Erase_head<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > >
PUBLIC	??$_Find_lower_bound@I@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEBA?AU?$_Tree_find_result@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@AEBI@Z ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::_Find_lower_bound<unsigned int>
PUBLIC	??$_Lower_bound_duplicate@I@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEBA_NQEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@AEBI@Z ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::_Lower_bound_duplicate<unsigned int>
PUBLIC	??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ ; std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > >
PUBLIC	??1?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ ; std::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > >::~_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > >
PUBLIC	??$?0U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z ; std::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > >::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > ><std::pair<unsigned int const ,mu2::EntityParty *> >
PUBLIC	??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > >
PUBLIC	??$_Buyheadnode@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@@Z ; std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *>::_Buyheadnode<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > >
PUBLIC	??_7exception@std@@6B@				; std::exception::`vftable'
PUBLIC	??_C@_0BC@EOODALEL@Unknown?5exception@		; `string'
PUBLIC	??_7bad_alloc@std@@6B@				; std::bad_alloc::`vftable'
PUBLIC	??_7bad_array_new_length@std@@6B@		; std::bad_array_new_length::`vftable'
PUBLIC	??_C@_0BF@KINCDENJ@bad?5array?5new?5length@	; `string'
PUBLIC	??_R0?AVexception@std@@@8			; std::exception `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
PUBLIC	_TI3?AVbad_array_new_length@std@@
PUBLIC	_CTA3?AVbad_array_new_length@std@@
PUBLIC	??_R0?AVbad_array_new_length@std@@@8		; std::bad_array_new_length `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
PUBLIC	??_R0?AVbad_alloc@std@@@8			; std::bad_alloc `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
PUBLIC	??_C@_0BB@GCADKGJO@map?1set?5too?5long@		; `string'
PUBLIC	??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@ ; `string'
PUBLIC	??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@		; `string'
PUBLIC	??_C@_09OLBBGJEO@Assertion@			; `string'
PUBLIC	??_7ManagerPartyEntity@mu2@@6B@			; mu2::ManagerPartyEntity::`vftable'
PUBLIC	??_R4exception@std@@6B@				; std::exception::`RTTI Complete Object Locator'
PUBLIC	??_R3exception@std@@8				; std::exception::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2exception@std@@8				; std::exception::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@exception@std@@8			; std::exception::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4bad_array_new_length@std@@6B@		; std::bad_array_new_length::`RTTI Complete Object Locator'
PUBLIC	??_R3bad_array_new_length@std@@8		; std::bad_array_new_length::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2bad_array_new_length@std@@8		; std::bad_array_new_length::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@bad_array_new_length@std@@8	; std::bad_array_new_length::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@bad_alloc@std@@8			; std::bad_alloc::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R3bad_alloc@std@@8				; std::bad_alloc::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2bad_alloc@std@@8				; std::bad_alloc::`RTTI Base Class Array'
PUBLIC	??_R4bad_alloc@std@@6B@				; std::bad_alloc::`RTTI Complete Object Locator'
PUBLIC	??_C@_0FB@EJDJLCL@F?3?2Release_Branch?2Server?2Develo@ ; `string'
PUBLIC	??_C@_0O@BLOGMFHF@result?4second@		; `string'
PUBLIC	??_C@_0P@HJBEIDCF@remove_i?$CI?5id?5?$CJ@	; `string'
PUBLIC	??_C@_0CA@FANFAMJ@mu2?3?3ManagerPartyEntity?3?3Remove@ ; `string'
PUBLIC	??_R4ManagerPartyEntity@mu2@@6B@		; mu2::ManagerPartyEntity::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVManagerPartyEntity@mu2@@@8		; mu2::ManagerPartyEntity `RTTI Type Descriptor'
PUBLIC	??_R3ManagerPartyEntity@mu2@@8			; mu2::ManagerPartyEntity::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2ManagerPartyEntity@mu2@@8			; mu2::ManagerPartyEntity::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@ManagerPartyEntity@mu2@@8		; mu2::ManagerPartyEntity::`RTTI Base Class Descriptor at (0,-1,0,64)'
EXTRN	??2@YAPEAX_K@Z:PROC				; operator new
EXTRN	??3@YAXPEAX_K@Z:PROC				; operator delete
EXTRN	__std_terminate:PROC
EXTRN	_invoke_watson:PROC
EXTRN	__imp_GetStdHandle:PROC
EXTRN	__imp_GetTickCount64:PROC
EXTRN	__imp_SetConsoleTextAttribute:PROC
EXTRN	__std_exception_copy:PROC
EXTRN	__std_exception_destroy:PROC
EXTRN	??_Eexception@std@@UEAAPEAXI@Z:PROC		; std::exception::`vector deleting destructor'
EXTRN	??_Ebad_alloc@std@@UEAAPEAXI@Z:PROC		; std::bad_alloc::`vector deleting destructor'
EXTRN	??_Ebad_array_new_length@std@@UEAAPEAXI@Z:PROC	; std::bad_array_new_length::`vector deleting destructor'
EXTRN	?_Xlength_error@std@@YAXPEBD@Z:PROC		; std::_Xlength_error
EXTRN	?LogRuntimeAssertionFailure@mu2@@YAXPEBD00_K@Z:PROC ; mu2::LogRuntimeAssertionFailure
EXTRN	?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ:PROC	; mu2::Logger::Logging
EXTRN	?GetPartyId@EntityParty@mu2@@QEAAIXZ:PROC	; mu2::EntityParty::GetPartyId
EXTRN	?OnTick4World@EntityParty@mu2@@QEAAXXZ:PROC	; mu2::EntityParty::OnTick4World
EXTRN	??_EManagerPartyEntity@mu2@@UEAAPEAXI@Z:PROC	; mu2::ManagerPartyEntity::`vector deleting destructor'
EXTRN	_CxxThrowException:PROC
EXTRN	__CxxFrameHandler4:PROC
EXTRN	??_7type_info@@6B@:BYTE				; type_info::`vftable'
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0exception@std@@QEAA@AEBV01@@Z DD imagerel $LN4
	DD	imagerel $LN4+89
	DD	imagerel $unwind$??0exception@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?what@exception@std@@UEBAPEBDXZ DD imagerel $LN5
	DD	imagerel $LN5+56
	DD	imagerel $unwind$?what@exception@std@@UEBAPEBDXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gexception@std@@UEAAPEAXI@Z DD imagerel $LN10
	DD	imagerel $LN10+83
	DD	imagerel $unwind$??_Gexception@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z DD imagerel $LN9
	DD	imagerel $LN9+104
	DD	imagerel $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+83
	DD	imagerel $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_array_new_length@std@@QEAA@XZ DD imagerel $LN14
	DD	imagerel $LN14+95
	DD	imagerel $unwind$??0bad_array_new_length@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1bad_array_new_length@std@@UEAA@XZ DD imagerel $LN14
	DD	imagerel $LN14+47
	DD	imagerel $unwind$??1bad_array_new_length@std@@UEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z DD imagerel $LN14
	DD	imagerel $LN14+54
	DD	imagerel $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z DD imagerel $LN20
	DD	imagerel $LN20+83
	DD	imagerel $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Throw_bad_array_new_length@std@@YAXXZ DD imagerel $LN3
	DD	imagerel $LN3+37
	DD	imagerel $unwind$?_Throw_bad_array_new_length@std@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z DD imagerel $LN5
	DD	imagerel $LN5+159
	DD	imagerel $unwind$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Throw_tree_length_error@std@@YAXXZ DD imagerel $LN3
	DD	imagerel $LN3+22
	DD	imagerel $unwind$?_Throw_tree_length_error@std@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0ManagerPartyEntity@mu2@@QEAA@XZ DD imagerel $LN147
	DD	imagerel $LN147+99
	DD	imagerel $unwind$??0ManagerPartyEntity@mu2@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1ManagerPartyEntity@mu2@@UEAA@XZ DD imagerel $LN138
	DD	imagerel $LN138+47
	DD	imagerel $unwind$??1ManagerPartyEntity@mu2@@UEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?OnTick@ManagerPartyEntity@mu2@@QEAAXXZ DD imagerel $LN88
	DD	imagerel $LN88+368
	DD	imagerel $unwind$?OnTick@ManagerPartyEntity@mu2@@QEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Insert@ManagerPartyEntity@mu2@@QEAA_NPEAVEntityParty@2@@Z DD imagerel $LN217
	DD	imagerel $LN217+177
	DD	imagerel $unwind$?Insert@ManagerPartyEntity@mu2@@QEAA_NPEAVEntityParty@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Remove@ManagerPartyEntity@mu2@@QEAAXI@Z DD imagerel $LN4
	DD	imagerel $LN4+202
	DD	imagerel $unwind$?Remove@ManagerPartyEntity@mu2@@QEAAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?GetEntity@ManagerPartyEntity@mu2@@QEAAPEAVEntityParty@2@I@Z DD imagerel $LN141
	DD	imagerel $LN141+357
	DD	imagerel $unwind$?GetEntity@ManagerPartyEntity@mu2@@QEAAPEAVEntityParty@2@I@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?remove_i@ManagerPartyEntity@mu2@@AEAA_NI@Z DD imagerel $LN291
	DD	imagerel $LN291+457
	DD	imagerel $unwind$?remove_i@ManagerPartyEntity@mu2@@AEAA_NI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?allocate@?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@_K@Z DD imagerel $LN13
	DD	imagerel $LN13+160
	DD	imagerel $unwind$?allocate@?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@QEAA@XZ DD imagerel $LN128
	DD	imagerel $LN128+83
	DD	imagerel $unwind$??1?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?max_size@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@QEBA_KXZ DD imagerel $LN31
	DD	imagerel $LN31+152
	DD	imagerel $unwind$?max_size@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@QEBA_KXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Erase_unchecked@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@AEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z DD imagerel $LN174
	DD	imagerel $LN174+207
	DD	imagerel $unwind$?_Erase_unchecked@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@AEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?erase@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@QEAA?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@@2@V?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@@2@@Z DD imagerel $LN147
	DD	imagerel $LN147+137
	DD	imagerel $unwind$?erase@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@QEAA?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@@2@V?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Check_grow_by_1@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEAAXXZ DD imagerel $LN46
	DD	imagerel $LN46+61
	DD	imagerel $unwind$?_Check_grow_by_1@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Alloc_sentinel_and_proxy@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEAAXXZ DD imagerel $LN107
	DD	imagerel $LN107+114
	DD	imagerel $unwind$?_Alloc_sentinel_and_proxy@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Lrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@@Z DD imagerel $LN9
	DD	imagerel $LN9+212
	DD	imagerel $unwind$?_Lrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Rrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@@Z DD imagerel $LN9
	DD	imagerel $LN9+215
	DD	imagerel $unwind$?_Rrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Extract@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z DD imagerel $LN157
	DD	imagerel $LN157+1703
	DD	imagerel $unwind$?_Extract@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Insert_node@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@U?$_Tree_id@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@2@QEAU32@@Z DD imagerel $LN60
	DD	imagerel $LN60+744
	DD	imagerel $unwind$?_Insert_node@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@U?$_Tree_id@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@2@QEAU32@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0?$map@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@@std@@QEAA@XZ DD imagerel $LN142
	DD	imagerel $LN142+107
	DD	imagerel $unwind$??0?$map@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GManagerPartyEntity@mu2@@UEAAPEAXI@Z DD imagerel $LN5
	DD	imagerel $LN5+60
	DD	imagerel $unwind$??_GManagerPartyEntity@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z DD imagerel $LN7
	DD	imagerel $LN7+150
	DD	imagerel $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ DD imagerel $LN15
	DD	imagerel $LN15+184
	DD	imagerel $unwind$??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$insert@$0A@$0A@@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@QEAA?AU?$pair@V?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@@std@@_N@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z DD imagerel $LN198
	DD	imagerel $LN198+183
	DD	imagerel $unwind$??$insert@$0A@$0A@@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@QEAA?AU?$pair@V?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@@std@@_N@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Emplace@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@_N@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z DD imagerel $LN317
	DD	imagerel $LN317+767
	DD	imagerel $unwind$??$_Emplace@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@_N@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Find@I@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@AEBAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@AEBI@Z DD imagerel $LN72
	DD	imagerel $LN72+102
	DD	imagerel $unwind$??$_Find@I@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@AEBAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@AEBI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@@Z DD imagerel $LN103
	DD	imagerel $LN103+126
	DD	imagerel $unwind$??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Find_lower_bound@I@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEBA?AU?$_Tree_find_result@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@AEBI@Z DD imagerel $LN36
	DD	imagerel $LN36+320
	DD	imagerel $unwind$??$_Find_lower_bound@I@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEBA?AU?$_Tree_find_result@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@AEBI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Lower_bound_duplicate@I@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEBA_NQEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@AEBI@Z DD imagerel $LN24
	DD	imagerel $LN24+147
	DD	imagerel $unwind$??$_Lower_bound_duplicate@I@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEBA_NQEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@AEBI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ DD imagerel $LN20
	DD	imagerel $LN20+120
	DD	imagerel $unwind$??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ DD imagerel $LN53
	DD	imagerel $LN53+115
	DD	imagerel $unwind$??1?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$?0U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z DD imagerel $LN105
	DD	imagerel $LN105+563
	DD	imagerel $unwind$??$?0U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$1@?0???$?0U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z@4HA DD imagerel ?dtor$1@?0???$?0U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z@4HA
	DD	imagerel ?dtor$1@?0???$?0U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z@4HA+27
	DD	imagerel $unwind$?dtor$1@?0???$?0U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@@Z DD imagerel $LN58
	DD	imagerel $LN58+219
	DD	imagerel $unwind$??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Buyheadnode@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@@Z DD imagerel $LN75
	DD	imagerel $LN75+296
	DD	imagerel $unwind$??$_Buyheadnode@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@@Z
pdata	ENDS
;	COMDAT ??_R1A@?0A@EA@ManagerPartyEntity@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@ManagerPartyEntity@mu2@@8 DD imagerel ??_R0?AVManagerPartyEntity@mu2@@@8 ; mu2::ManagerPartyEntity::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3ManagerPartyEntity@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2ManagerPartyEntity@mu2@@8
rdata$r	SEGMENT
??_R2ManagerPartyEntity@mu2@@8 DD imagerel ??_R1A@?0A@EA@ManagerPartyEntity@mu2@@8 ; mu2::ManagerPartyEntity::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3ManagerPartyEntity@mu2@@8
rdata$r	SEGMENT
??_R3ManagerPartyEntity@mu2@@8 DD 00H			; mu2::ManagerPartyEntity::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2ManagerPartyEntity@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVManagerPartyEntity@mu2@@@8
data$rs	SEGMENT
??_R0?AVManagerPartyEntity@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::ManagerPartyEntity `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVManagerPartyEntity@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4ManagerPartyEntity@mu2@@6B@
rdata$r	SEGMENT
??_R4ManagerPartyEntity@mu2@@6B@ DD 01H			; mu2::ManagerPartyEntity::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVManagerPartyEntity@mu2@@@8
	DD	imagerel ??_R3ManagerPartyEntity@mu2@@8
	DD	imagerel ??_R4ManagerPartyEntity@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_C@_0CA@FANFAMJ@mu2?3?3ManagerPartyEntity?3?3Remove@
CONST	SEGMENT
??_C@_0CA@FANFAMJ@mu2?3?3ManagerPartyEntity?3?3Remove@ DB 'mu2::ManagerPa'
	DB	'rtyEntity::Remove', 00H			; `string'
CONST	ENDS
;	COMDAT ??_C@_0P@HJBEIDCF@remove_i?$CI?5id?5?$CJ@
CONST	SEGMENT
??_C@_0P@HJBEIDCF@remove_i?$CI?5id?5?$CJ@ DB 'remove_i( id )', 00H ; `string'
CONST	ENDS
;	COMDAT ??_C@_0O@BLOGMFHF@result?4second@
CONST	SEGMENT
??_C@_0O@BLOGMFHF@result?4second@ DB 'result.second', 00H ; `string'
CONST	ENDS
;	COMDAT ??_C@_0FB@EJDJLCL@F?3?2Release_Branch?2Server?2Develo@
CONST	SEGMENT
??_C@_0FB@EJDJLCL@F?3?2Release_Branch?2Server?2Develo@ DB 'F:\Release_Bra'
	DB	'nch\Server\Development\Frontend\WorldServer\PartyEntityManage'
	DB	'r.cpp', 00H					; `string'
CONST	ENDS
;	COMDAT ??_R4bad_alloc@std@@6B@
rdata$r	SEGMENT
??_R4bad_alloc@std@@6B@ DD 01H				; std::bad_alloc::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVbad_alloc@std@@@8
	DD	imagerel ??_R3bad_alloc@std@@8
	DD	imagerel ??_R4bad_alloc@std@@6B@
rdata$r	ENDS
;	COMDAT ??_R2bad_alloc@std@@8
rdata$r	SEGMENT
??_R2bad_alloc@std@@8 DD imagerel ??_R1A@?0A@EA@bad_alloc@std@@8 ; std::bad_alloc::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@exception@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3bad_alloc@std@@8
rdata$r	SEGMENT
??_R3bad_alloc@std@@8 DD 00H				; std::bad_alloc::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2bad_alloc@std@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@bad_alloc@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@bad_alloc@std@@8 DD imagerel ??_R0?AVbad_alloc@std@@@8 ; std::bad_alloc::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3bad_alloc@std@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@bad_array_new_length@std@@8 DD imagerel ??_R0?AVbad_array_new_length@std@@@8 ; std::bad_array_new_length::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	02H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3bad_array_new_length@std@@8
rdata$r	ENDS
;	COMDAT ??_R2bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R2bad_array_new_length@std@@8 DD imagerel ??_R1A@?0A@EA@bad_array_new_length@std@@8 ; std::bad_array_new_length::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@bad_alloc@std@@8
	DD	imagerel ??_R1A@?0A@EA@exception@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R3bad_array_new_length@std@@8 DD 00H			; std::bad_array_new_length::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	03H
	DD	imagerel ??_R2bad_array_new_length@std@@8
rdata$r	ENDS
;	COMDAT ??_R4bad_array_new_length@std@@6B@
rdata$r	SEGMENT
??_R4bad_array_new_length@std@@6B@ DD 01H		; std::bad_array_new_length::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVbad_array_new_length@std@@@8
	DD	imagerel ??_R3bad_array_new_length@std@@8
	DD	imagerel ??_R4bad_array_new_length@std@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@exception@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@exception@std@@8 DD imagerel ??_R0?AVexception@std@@@8 ; std::exception::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3exception@std@@8
rdata$r	ENDS
;	COMDAT ??_R2exception@std@@8
rdata$r	SEGMENT
??_R2exception@std@@8 DD imagerel ??_R1A@?0A@EA@exception@std@@8 ; std::exception::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3exception@std@@8
rdata$r	SEGMENT
??_R3exception@std@@8 DD 00H				; std::exception::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2exception@std@@8
rdata$r	ENDS
;	COMDAT ??_R4exception@std@@6B@
rdata$r	SEGMENT
??_R4exception@std@@6B@ DD 01H				; std::exception::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVexception@std@@@8
	DD	imagerel ??_R3exception@std@@8
	DD	imagerel ??_R4exception@std@@6B@
rdata$r	ENDS
;	COMDAT ??_7ManagerPartyEntity@mu2@@6B@
CONST	SEGMENT
??_7ManagerPartyEntity@mu2@@6B@ DQ FLAT:??_R4ManagerPartyEntity@mu2@@6B@ ; mu2::ManagerPartyEntity::`vftable'
	DQ	FLAT:??_EManagerPartyEntity@mu2@@UEAAPEAXI@Z
CONST	ENDS
;	COMDAT ??_C@_09OLBBGJEO@Assertion@
CONST	SEGMENT
??_C@_09OLBBGJEO@Assertion@ DB 'Assertion', 00H		; `string'
CONST	ENDS
;	COMDAT ??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
CONST	SEGMENT
??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@ DB 'g', 00H, 'a', 00H, 'm', 00H, 'e'
	DB	00H, 00H, 00H				; `string'
CONST	ENDS
;	COMDAT ??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
CONST	SEGMENT
??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@ DB '%'
	DB	's> ASSERT - %s, %s(%d)', 00H		; `string'
CONST	ENDS
;	COMDAT ??_C@_0BB@GCADKGJO@map?1set?5too?5long@
CONST	SEGMENT
??_C@_0BB@GCADKGJO@map?1set?5too?5long@ DB 'map/set too long', 00H ; `string'
CONST	ENDS
;	COMDAT _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 DD 010H
	DD	imagerel ??_R0?AVbad_alloc@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0bad_alloc@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVbad_alloc@std@@@8
data$r	SEGMENT
??_R0?AVbad_alloc@std@@@8 DQ FLAT:??_7type_info@@6B@	; std::bad_alloc `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVbad_alloc@std@@', 00H
data$r	ENDS
;	COMDAT _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 DD 00H
	DD	imagerel ??_R0?AVbad_array_new_length@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0bad_array_new_length@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVbad_array_new_length@std@@@8
data$r	SEGMENT
??_R0?AVbad_array_new_length@std@@@8 DQ FLAT:??_7type_info@@6B@ ; std::bad_array_new_length `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVbad_array_new_length@std@@', 00H
data$r	ENDS
;	COMDAT _CTA3?AVbad_array_new_length@std@@
xdata$x	SEGMENT
_CTA3?AVbad_array_new_length@std@@ DD 03H
	DD	imagerel _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
	DD	imagerel _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
	DD	imagerel _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
xdata$x	ENDS
;	COMDAT _TI3?AVbad_array_new_length@std@@
xdata$x	SEGMENT
_TI3?AVbad_array_new_length@std@@ DD 00H
	DD	imagerel ??1bad_array_new_length@std@@UEAA@XZ
	DD	00H
	DD	imagerel _CTA3?AVbad_array_new_length@std@@
xdata$x	ENDS
;	COMDAT _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 DD 00H
	DD	imagerel ??_R0?AVexception@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0exception@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVexception@std@@@8
data$r	SEGMENT
??_R0?AVexception@std@@@8 DQ FLAT:??_7type_info@@6B@	; std::exception `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVexception@std@@', 00H
data$r	ENDS
;	COMDAT ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@
CONST	SEGMENT
??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ DB 'bad array new length', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7bad_array_new_length@std@@6B@
CONST	SEGMENT
??_7bad_array_new_length@std@@6B@ DQ FLAT:??_R4bad_array_new_length@std@@6B@ ; std::bad_array_new_length::`vftable'
	DQ	FLAT:??_Ebad_array_new_length@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT ??_7bad_alloc@std@@6B@
CONST	SEGMENT
??_7bad_alloc@std@@6B@ DQ FLAT:??_R4bad_alloc@std@@6B@	; std::bad_alloc::`vftable'
	DQ	FLAT:??_Ebad_alloc@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT ??_C@_0BC@EOODALEL@Unknown?5exception@
CONST	SEGMENT
??_C@_0BC@EOODALEL@Unknown?5exception@ DB 'Unknown exception', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7exception@std@@6B@
CONST	SEGMENT
??_7exception@std@@6B@ DQ FLAT:??_R4exception@std@@6B@	; std::exception::`vftable'
	DQ	FLAT:??_Eexception@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Buyheadnode@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@@Z DD 020c01H
	DD	015010cH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@@Z DB 06H
	DB	00H
	DB	00H
	DB	099H, 02H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@@Z DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@@Z DB 068H
	DD	imagerel $stateUnwindMap$??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@@Z
	DD	imagerel $ip2state$??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@@Z DD 011319H
	DD	0c213H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$1@?0???$?0U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$?0U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z DB 06H
	DB	00H
	DB	00H
	DB	080H
	DB	02H
	DB	'b'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$?0U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z DB 02H
	DB	0eH
	DD	imagerel ?dtor$1@?0???$?0U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$?0U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z DB 028H
	DD	imagerel $stateUnwindMap$??$?0U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z
	DD	imagerel $ip2state$??$?0U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$?0U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z DD 041d11H
	DD	01b011dH
	DD	060157016H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$?0U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ DD 010901H
	DD	0a209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ DB 06H
	DB	00H
	DB	00H
	DB	090H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ DB 068H
	DD	imagerel $stateUnwindMap$??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ
	DD	imagerel $ip2state$??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ DD 010919H
	DD	08209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Lower_bound_duplicate@I@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEBA_NQEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@AEBI@Z DD 011301H
	DD	06213H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Find_lower_bound@I@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEBA?AU?$_Tree_find_result@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@AEBI@Z DD 031501H
	DD	07011c215H
	DD	06010H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@@Z DB 06H
	DB	00H
	DB	00H
	DB	09cH
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@@Z DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@@Z DB 068H
	DD	imagerel $stateUnwindMap$??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@@Z
	DD	imagerel $ip2state$??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@@Z DD 010e19H
	DD	0820eH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Find@I@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@AEBAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@AEBI@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Emplace@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@_N@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z DD 041801H
	DD	02b0118H
	DD	060107011H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$insert@$0A@$0A@@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@QEAA?AU?$pair@V?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@@std@@_N@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z DD 011301H
	DD	0e213H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z DD 010901H
	DD	0a209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GManagerPartyEntity@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0?$map@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@@std@@QEAA@XZ DD 010901H
	DD	0a209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Insert_node@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@U?$_Tree_id@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@2@QEAU32@@Z DD 011301H
	DD	08213H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Extract@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z DD 021101H
	DD	0150111H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Rrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@@Z DD 010e01H
	DD	0220eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Lrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@@Z DD 010e01H
	DD	0220eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Alloc_sentinel_and_proxy@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEAAXXZ DD 020a01H
	DD	07006b20aH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Check_grow_by_1@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEAAXXZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?erase@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@QEAA?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@@2@V?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@@2@@Z DD 011301H
	DD	0c213H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?_Erase_unchecked@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@AEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z DB 06H
	DB	00H
	DB	00H
	DB	'i', 02H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?_Erase_unchecked@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@AEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?_Erase_unchecked@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@AEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z DB 068H
	DD	imagerel $stateUnwindMap$?_Erase_unchecked@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@AEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z
	DD	imagerel $ip2state$?_Erase_unchecked@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@AEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Erase_unchecked@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@AEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z DD 010e19H
	DD	0e20eH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?_Erase_unchecked@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@AEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?max_size@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@QEBA_KXZ DD 010901H
	DD	0a209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@QEAA@XZ DD 010901H
	DD	0a209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?allocate@?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?remove_i@ManagerPartyEntity@mu2@@AEAA_NI@Z DD 021001H
	DD	01b0110H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?GetEntity@ManagerPartyEntity@mu2@@QEAAPEAVEntityParty@2@I@Z DD 021001H
	DD	0170110H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Remove@ManagerPartyEntity@mu2@@QEAAXI@Z DD 010d01H
	DD	0e20dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Insert@ManagerPartyEntity@mu2@@QEAA_NPEAVEntityParty@2@@Z DD 010e01H
	DD	0c20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?OnTick@ManagerPartyEntity@mu2@@QEAAXXZ DD 030d01H
	DD	012010dH
	DD	07006H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1ManagerPartyEntity@mu2@@UEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0ManagerPartyEntity@mu2@@QEAA@XZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Throw_tree_length_error@std@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Throw_bad_array_new_length@std@@YAXXZ DD 010401H
	DD	08204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1bad_array_new_length@std@@UEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_array_new_length@std@@QEAA@XZ DD 010601H
	DD	07006H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z DD 020f01H
	DD	0700b320fH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gexception@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?what@exception@std@@UEBAPEBDXZ DD 010901H
	DD	02209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0exception@std@@QEAA@AEBV01@@Z DD 020f01H
	DD	0700b320fH
xdata	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??$_Buyheadnode@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@@Z
_TEXT	SEGMENT
_Pnode$ = 32
_Obj$ = 40
$T1 = 48
$T2 = 56
$T3 = 64
$T4 = 72
_Obj$ = 80
$T5 = 88
$T6 = 96
$T7 = 104
$T8 = 112
_Obj$ = 120
$T9 = 128
$T10 = 136
$T11 = 144
$T12 = 152
_Al$ = 176
??$_Buyheadnode@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@@Z PROC ; std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *>::_Buyheadnode<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > >, COMDAT

; 343  :     static _Nodeptr _Buyheadnode(_Alloc& _Al) {

$LN75:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec a8 00
	00 00		 sub	 rsp, 168		; 000000a8H

; 344  :         static_assert(is_same_v<typename _Alloc::value_type, _Tree_node>, "Bad _Buyheadnode call");
; 345  :         const auto _Pnode = _Al.allocate(1);

  0000c	ba 01 00 00 00	 mov	 edx, 1
  00011	48 8b 8c 24 b0
	00 00 00	 mov	 rcx, QWORD PTR _Al$[rsp]
  00019	e8 00 00 00 00	 call	 ?allocate@?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@_K@Z ; std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> >::allocate
  0001e	48 89 44 24 20	 mov	 QWORD PTR _Pnode$[rsp], rax

; 346  :         _Construct_in_place(_Pnode->_Left, _Pnode);

  00023	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00028	48 89 44 24 28	 mov	 QWORD PTR _Obj$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0002d	48 8b 44 24 28	 mov	 rax, QWORD PTR _Obj$[rsp]
  00032	48 89 44 24 30	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00037	48 8b 44 24 30	 mov	 rax, QWORD PTR $T1[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  0003c	48 89 44 24 38	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00041	48 8b 44 24 38	 mov	 rax, QWORD PTR $T2[rsp]
  00046	48 89 44 24 40	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  0004b	48 8d 44 24 20	 lea	 rax, QWORD PTR _Pnode$[rsp]
  00050	48 89 44 24 48	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00055	48 8b 44 24 40	 mov	 rax, QWORD PTR $T3[rsp]
  0005a	48 8b 4c 24 48	 mov	 rcx, QWORD PTR $T4[rsp]
  0005f	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00062	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 347  :         _Construct_in_place(_Pnode->_Parent, _Pnode);

  00065	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0006a	48 83 c0 08	 add	 rax, 8
  0006e	48 89 44 24 50	 mov	 QWORD PTR _Obj$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00073	48 8b 44 24 50	 mov	 rax, QWORD PTR _Obj$[rsp]
  00078	48 89 44 24 58	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0007d	48 8b 44 24 58	 mov	 rax, QWORD PTR $T5[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  00082	48 89 44 24 60	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00087	48 8b 44 24 60	 mov	 rax, QWORD PTR $T6[rsp]
  0008c	48 89 44 24 68	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00091	48 8d 44 24 20	 lea	 rax, QWORD PTR _Pnode$[rsp]
  00096	48 89 44 24 70	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0009b	48 8b 44 24 68	 mov	 rax, QWORD PTR $T7[rsp]
  000a0	48 8b 4c 24 70	 mov	 rcx, QWORD PTR $T8[rsp]
  000a5	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  000a8	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 348  :         _Construct_in_place(_Pnode->_Right, _Pnode);

  000ab	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  000b0	48 83 c0 10	 add	 rax, 16
  000b4	48 89 44 24 78	 mov	 QWORD PTR _Obj$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  000b9	48 8b 44 24 78	 mov	 rax, QWORD PTR _Obj$[rsp]
  000be	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  000c6	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR $T9[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  000ce	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  000d6	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  000de	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  000e6	48 8d 44 24 20	 lea	 rax, QWORD PTR _Pnode$[rsp]
  000eb	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  000f3	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  000fb	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR $T12[rsp]
  00103	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00106	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 349  :         _Pnode->_Color = _Black;

  00109	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0010e	c6 40 18 01	 mov	 BYTE PTR [rax+24], 1

; 350  :         _Pnode->_Isnil = true;

  00112	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00117	c6 40 19 01	 mov	 BYTE PTR [rax+25], 1

; 351  :         return _Pnode;

  0011b	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]

; 352  :     }

  00120	48 81 c4 a8 00
	00 00		 add	 rsp, 168		; 000000a8H
  00127	c3		 ret	 0
??$_Buyheadnode@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@@Z ENDP ; std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *>::_Buyheadnode<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@@Z
_TEXT	SEGMENT
_Bytes$ = 32
_Ptr$ = 40
_Ptr$ = 48
_New_val$ = 56
_Old_val$1 = 64
$T2 = 72
$T3 = 80
this$ = 112
_Al$ = 120
_Rootnode$ = 128
??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@@Z PROC ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > >, COMDAT

; 767  :     void _Erase_tree(_Alnode& _Al, _Nodeptr _Rootnode) noexcept {

$LN58:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 68	 sub	 rsp, 104		; 00000068H
$LN2@Erase_tree:

; 768  :         while (!_Rootnode->_Isnil) { // free subtrees, then node

  00013	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _Rootnode$[rsp]
  0001b	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  0001f	85 c0		 test	 eax, eax
  00021	0f 85 af 00 00
	00		 jne	 $LN3@Erase_tree

; 769  :             _Erase_tree(_Al, _Rootnode->_Right);

  00027	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _Rootnode$[rsp]
  0002f	4c 8b 40 10	 mov	 r8, QWORD PTR [rax+16]
  00033	48 8b 54 24 78	 mov	 rdx, QWORD PTR _Al$[rsp]
  00038	48 8b 4c 24 70	 mov	 rcx, QWORD PTR this$[rsp]
  0003d	e8 00 00 00 00	 call	 ??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > >

; 770  :             _Alnode::value_type::_Freenode(_Al, _STD exchange(_Rootnode, _Rootnode->_Left));

  00042	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _Rootnode$[rsp]
  0004a	48 89 44 24 38	 mov	 QWORD PTR _New_val$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 773  :     _Ty _Old_val = static_cast<_Ty&&>(_Val);

  0004f	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _Rootnode$[rsp]
  00057	48 89 44 24 40	 mov	 QWORD PTR _Old_val$1[rsp], rax

; 774  :     _Val         = static_cast<_Other&&>(_New_val);

  0005c	48 8b 44 24 38	 mov	 rax, QWORD PTR _New_val$[rsp]
  00061	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00064	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _Rootnode$[rsp], rax

; 775  :     return _Old_val;

  0006c	48 8b 44 24 40	 mov	 rax, QWORD PTR _Old_val$1[rsp]
  00071	48 89 44 24 48	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 770  :             _Alnode::value_type::_Freenode(_Al, _STD exchange(_Rootnode, _Rootnode->_Left));

  00076	48 8b 44 24 48	 mov	 rax, QWORD PTR $T2[rsp]
  0007b	48 89 44 24 28	 mov	 QWORD PTR _Ptr$[rsp], rax

; 381  :         allocator_traits<_Alloc>::destroy(_Al, _STD addressof(_Ptr->_Myval));

  00080	48 8b 44 24 28	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00085	48 83 c0 20	 add	 rax, 32			; 00000020H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00089	48 89 44 24 50	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 723  :             _STD _Deallocate<_New_alignof<value_type>>(_Ptr, sizeof(value_type) * _Count);

  0008e	b8 01 00 00 00	 mov	 eax, 1
  00093	48 6b c0 30	 imul	 rax, rax, 48		; 00000030H
  00097	48 89 44 24 20	 mov	 QWORD PTR _Bytes$[rsp], rax
  0009c	48 8b 44 24 28	 mov	 rax, QWORD PTR _Ptr$[rsp]
  000a1	48 89 44 24 30	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  000a6	48 81 7c 24 20
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  000af	72 10		 jb	 SHORT $LN49@Erase_tree

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  000b1	48 8d 54 24 20	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  000b6	48 8d 4c 24 30	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  000bb	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  000c0	90		 npad	 1
$LN49@Erase_tree:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  000c1	48 8b 54 24 20	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  000c6	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  000cb	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  000d0	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 771  :         }

  000d1	e9 3d ff ff ff	 jmp	 $LN2@Erase_tree
$LN3@Erase_tree:

; 772  :     }

  000d6	48 83 c4 68	 add	 rsp, 104		; 00000068H
  000da	c3		 ret	 0
??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@@Z ENDP ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??$?0U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z
_TEXT	SEGMENT
_Val$ = 32
$T1 = 40
$T2 = 48
$T3 = 56
$T4 = 64
$T5 = 72
_Obj$ = 80
$T6 = 88
$T7 = 96
$T8 = 104
$T9 = 112
_Obj$ = 120
$T10 = 128
$T11 = 136
$T12 = 144
$T13 = 152
_Obj$ = 160
$T14 = 168
$T15 = 176
$T16 = 184
$T17 = 192
__formal$ = 200
this$ = 240
_Al_$ = 248
_Myhead$ = 256
<_Vals_0>$ = 264
??$?0U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z PROC ; std::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > >::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > ><std::pair<unsigned int const ,mu2::EntityParty *> >, COMDAT

; 829  :         : _Tree_temp_node_alloc<_Alnode>(_Al_) {

$LN105:
  00000	4c 89 4c 24 20	 mov	 QWORD PTR [rsp+32], r9
  00005	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	56		 push	 rsi
  00015	57		 push	 rdi
  00016	48 81 ec d8 00
	00 00		 sub	 rsp, 216		; 000000d8H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1160 :     _CONSTEXPR20 explicit _Alloc_construct_ptr(_Alloc& _Al_) : _Al(_Al_), _Ptr(nullptr) {}

  0001d	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00025	48 8b 8c 24 f8
	00 00 00	 mov	 rcx, QWORD PTR _Al_$[rsp]
  0002d	48 89 08	 mov	 QWORD PTR [rax], rcx
  00030	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0

; 1167 :         _Ptr = nullptr; // if allocate throws, prevents double-free

  00040	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00048	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0

; 1168 :         _Ptr = _Al.allocate(1);

  00050	ba 01 00 00 00	 mov	 edx, 1
  00055	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0005d	48 8b 08	 mov	 rcx, QWORD PTR [rax]
  00060	e8 00 00 00 00	 call	 ?allocate@?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@_K@Z ; std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> >::allocate
  00065	48 8b 8c 24 f0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0006d	48 89 41 08	 mov	 QWORD PTR [rcx+8], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00071	48 8b 84 24 08
	01 00 00	 mov	 rax, QWORD PTR <_Vals_0>$[rsp]
  00079	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 830  :         _Alnode_traits::construct(this->_Al, _STD addressof(this->_Ptr->_Myval), _STD forward<_Valtys>(_Vals)...);

  0007e	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00086	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0008a	48 83 c0 20	 add	 rax, 32			; 00000020H
  0008e	48 89 44 24 20	 mov	 QWORD PTR _Val$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00093	48 8b 44 24 20	 mov	 rax, QWORD PTR _Val$[rsp]
  00098	48 89 44 24 28	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 830  :         _Alnode_traits::construct(this->_Al, _STD addressof(this->_Ptr->_Myval), _STD forward<_Valtys>(_Vals)...);

  0009d	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000a5	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000a8	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR __formal$[rsp], rax
  000b0	48 8b 44 24 28	 mov	 rax, QWORD PTR $T1[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  000b5	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 732  :         ::new (const_cast<void*>(static_cast<const volatile void*>(_Ptr))) _Objty(_STD forward<_Types>(_Args)...);

  000ba	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
  000bf	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 830  :         _Alnode_traits::construct(this->_Al, _STD addressof(this->_Ptr->_Myval), _STD forward<_Valtys>(_Vals)...);

  000c4	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  000c9	48 89 44 24 48	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 732  :         ::new (const_cast<void*>(static_cast<const volatile void*>(_Ptr))) _Objty(_STD forward<_Types>(_Args)...);

  000ce	48 8b 7c 24 40	 mov	 rdi, QWORD PTR $T4[rsp]
  000d3	48 8b 74 24 48	 mov	 rsi, QWORD PTR $T5[rsp]
  000d8	b9 10 00 00 00	 mov	 ecx, 16
  000dd	f3 a4		 rep movsb
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 831  :         _Construct_in_place(this->_Ptr->_Left, _Myhead);

  000df	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000e7	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  000eb	48 89 44 24 50	 mov	 QWORD PTR _Obj$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  000f0	48 8b 44 24 50	 mov	 rax, QWORD PTR _Obj$[rsp]
  000f5	48 89 44 24 58	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  000fa	48 8b 44 24 58	 mov	 rax, QWORD PTR $T6[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  000ff	48 89 44 24 60	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00104	48 8b 44 24 60	 mov	 rax, QWORD PTR $T7[rsp]
  00109	48 89 44 24 68	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  0010e	48 8d 84 24 00
	01 00 00	 lea	 rax, QWORD PTR _Myhead$[rsp]
  00116	48 89 44 24 70	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0011b	48 8b 44 24 68	 mov	 rax, QWORD PTR $T8[rsp]
  00120	48 8b 4c 24 70	 mov	 rcx, QWORD PTR $T9[rsp]
  00125	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00128	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 832  :         _Construct_in_place(this->_Ptr->_Parent, _Myhead);

  0012b	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00133	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00137	48 83 c0 08	 add	 rax, 8
  0013b	48 89 44 24 78	 mov	 QWORD PTR _Obj$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00140	48 8b 44 24 78	 mov	 rax, QWORD PTR _Obj$[rsp]
  00145	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0014d	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  00155	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0015d	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  00165	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  0016d	48 8d 84 24 00
	01 00 00	 lea	 rax, QWORD PTR _Myhead$[rsp]
  00175	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0017d	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
  00185	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR $T13[rsp]
  0018d	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00190	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 833  :         _Construct_in_place(this->_Ptr->_Right, _Myhead);

  00193	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0019b	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0019f	48 83 c0 10	 add	 rax, 16
  001a3	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR _Obj$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  001ab	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR _Obj$[rsp]
  001b3	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T14[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  001bb	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T14[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  001c3	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T15[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  001cb	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR $T15[rsp]
  001d3	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T16[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  001db	48 8d 84 24 00
	01 00 00	 lea	 rax, QWORD PTR _Myhead$[rsp]
  001e3	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T17[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  001eb	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T16[rsp]
  001f3	48 8b 8c 24 c0
	00 00 00	 mov	 rcx, QWORD PTR $T17[rsp]
  001fb	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  001fe	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 834  :         this->_Ptr->_Color = _Red;

  00201	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00209	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0020d	c6 40 18 00	 mov	 BYTE PTR [rax+24], 0

; 835  :         this->_Ptr->_Isnil = false;

  00211	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00219	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0021d	c6 40 19 00	 mov	 BYTE PTR [rax+25], 0

; 836  :     }

  00221	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00229	48 81 c4 d8 00
	00 00		 add	 rsp, 216		; 000000d8H
  00230	5f		 pop	 rdi
  00231	5e		 pop	 rsi
  00232	c3		 ret	 0
??$?0U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z ENDP ; std::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > >::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > ><std::pair<unsigned int const ,mu2::EntityParty *> >
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
_Val$ = 32
$T1 = 40
$T2 = 48
$T3 = 56
$T4 = 64
$T5 = 72
_Obj$ = 80
$T6 = 88
$T7 = 96
$T8 = 104
$T9 = 112
_Obj$ = 120
$T10 = 128
$T11 = 136
$T12 = 144
$T13 = 152
_Obj$ = 160
$T14 = 168
$T15 = 176
$T16 = 184
$T17 = 192
__formal$ = 200
this$ = 240
_Al_$ = 248
_Myhead$ = 256
<_Vals_0>$ = 264
?dtor$1@?0???$?0U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z@4HA PROC ; `std::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > >::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > ><std::pair<unsigned int const ,mu2::EntityParty *> >'::`1'::dtor$1
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 8d f0 00
	00 00		 mov	 rcx, QWORD PTR this$[rbp]
  00010	e8 00 00 00 00	 call	 ??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ ; std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > >
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$1@?0???$?0U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z@4HA ENDP ; `std::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > >::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > ><std::pair<unsigned int const ,mu2::EntityParty *> >'::`1'::dtor$1
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??1?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ
_TEXT	SEGMENT
_Val$ = 32
_Obj$ = 40
_Obj$ = 48
_Obj$ = 56
$T1 = 64
this$ = 96
??1?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ PROC ; std::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > >::~_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > >, COMDAT

; 841  :     ~_Tree_temp_node() {

$LN53:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 842  :         if (this->_Ptr) {

  00009	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00013	74 4e		 je	 SHORT $LN2@Tree_temp_

; 843  :             _Destroy_in_place(this->_Ptr->_Left);

  00015	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0001e	48 89 44 24 28	 mov	 QWORD PTR _Obj$[rsp], rax

; 844  :             _Destroy_in_place(this->_Ptr->_Parent);

  00023	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00028	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0002c	48 83 c0 08	 add	 rax, 8
  00030	48 89 44 24 30	 mov	 QWORD PTR _Obj$[rsp], rax

; 845  :             _Destroy_in_place(this->_Ptr->_Right);

  00035	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  0003a	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0003e	48 83 c0 10	 add	 rax, 16
  00042	48 89 44 24 38	 mov	 QWORD PTR _Obj$[rsp], rax

; 846  :             _Alnode_traits::destroy(this->_Al, _STD addressof(this->_Ptr->_Myval));

  00047	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  0004c	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00050	48 83 c0 20	 add	 rax, 32			; 00000020H
  00054	48 89 44 24 20	 mov	 QWORD PTR _Val$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00059	48 8b 44 24 20	 mov	 rax, QWORD PTR _Val$[rsp]
  0005e	48 89 44 24 40	 mov	 QWORD PTR $T1[rsp], rax
$LN2@Tree_temp_:
  00063	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  00068	e8 00 00 00 00	 call	 ??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ ; std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > >
  0006d	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 848  :     }

  0006e	48 83 c4 58	 add	 rsp, 88			; 00000058H
  00072	c3		 ret	 0
??1?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ ENDP ; std::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > >::~_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ
_TEXT	SEGMENT
_Bytes$ = 32
_Ptr$ = 40
_Ptr$ = 48
this$ = 56
this$ = 80
??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ PROC ; std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > >, COMDAT

; 1171 :     _CONSTEXPR20 ~_Alloc_construct_ptr() { // if this instance is engaged, deallocate storage

$LN20:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 1172 :         if (_Ptr) {

  00009	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00013	74 5e		 je	 SHORT $LN2@Alloc_cons

; 1173 :             _Al.deallocate(_Ptr, 1);

  00015	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0001e	48 89 44 24 30	 mov	 QWORD PTR _Ptr$[rsp], rax
  00023	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00028	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0002b	48 89 44 24 38	 mov	 QWORD PTR this$[rsp], rax

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  00030	b8 01 00 00 00	 mov	 eax, 1
  00035	48 6b c0 30	 imul	 rax, rax, 48		; 00000030H
  00039	48 89 44 24 20	 mov	 QWORD PTR _Bytes$[rsp], rax
  0003e	48 8b 44 24 30	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00043	48 89 44 24 28	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  00048	48 81 7c 24 20
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  00051	72 10		 jb	 SHORT $LN11@Alloc_cons

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  00053	48 8d 54 24 20	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  00058	48 8d 4c 24 28	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  0005d	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  00062	90		 npad	 1
$LN11@Alloc_cons:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  00063	48 8b 54 24 20	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  00068	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  0006d	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00072	90		 npad	 1
$LN2@Alloc_cons:

; 1174 :         }
; 1175 :     }

  00073	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00077	c3		 ret	 0
??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ ENDP ; std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??$_Lower_bound_duplicate@I@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEBA_NQEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@AEBI@Z
_TEXT	SEGMENT
$T1 = 0
tv81 = 4
tv78 = 8
$T2 = 16
$T3 = 24
$T4 = 32
this$ = 64
_Bound$ = 72
_Keyval$ = 80
??$_Lower_bound_duplicate@I@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEBA_NQEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@AEBI@Z PROC ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::_Lower_bound_duplicate<unsigned int>, COMDAT

; 1623 :     bool _Lower_bound_duplicate(const _Nodeptr _Bound, const _Keyty& _Keyval) const {

$LN24:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 1624 :         return !_Bound->_Isnil && !_DEBUG_LT_PRED(_Getcomp(), _Keyval, _Traits::_Kfn(_Bound->_Myval));

  00013	48 8b 44 24 48	 mov	 rax, QWORD PTR _Bound$[rsp]
  00018	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  0001c	85 c0		 test	 eax, eax
  0001e	75 61		 jne	 SHORT $LN3@Lower_boun
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map

; 67   :         return _Val.first;

  00020	48 8b 44 24 48	 mov	 rax, QWORD PTR _Bound$[rsp]
  00025	48 83 c0 20	 add	 rax, 32			; 00000020H
  00029	48 89 44 24 18	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1971 :         return _Mypair._Get_first();

  0002e	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1543 :         return *this;

  00033	48 89 44 24 10	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1971 :         return _Mypair._Get_first();

  00038	48 8b 44 24 10	 mov	 rax, QWORD PTR $T2[rsp]
  0003d	48 89 44 24 20	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 2380 :         return _Left < _Right;

  00042	48 8b 44 24 50	 mov	 rax, QWORD PTR _Keyval$[rsp]
  00047	48 8b 4c 24 18	 mov	 rcx, QWORD PTR $T3[rsp]
  0004c	8b 09		 mov	 ecx, DWORD PTR [rcx]
  0004e	39 08		 cmp	 DWORD PTR [rax], ecx
  00050	73 0a		 jae	 SHORT $LN19@Lower_boun
  00052	c7 44 24 04 01
	00 00 00	 mov	 DWORD PTR tv81[rsp], 1
  0005a	eb 08		 jmp	 SHORT $LN20@Lower_boun
$LN19@Lower_boun:
  0005c	c7 44 24 04 00
	00 00 00	 mov	 DWORD PTR tv81[rsp], 0
$LN20@Lower_boun:
  00064	0f b6 44 24 04	 movzx	 eax, BYTE PTR tv81[rsp]
  00069	88 04 24	 mov	 BYTE PTR $T1[rsp], al
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1624 :         return !_Bound->_Isnil && !_DEBUG_LT_PRED(_Getcomp(), _Keyval, _Traits::_Kfn(_Bound->_Myval));

  0006c	0f b6 04 24	 movzx	 eax, BYTE PTR $T1[rsp]
  00070	0f b6 c0	 movzx	 eax, al
  00073	85 c0		 test	 eax, eax
  00075	75 0a		 jne	 SHORT $LN3@Lower_boun
  00077	c7 44 24 08 01
	00 00 00	 mov	 DWORD PTR tv78[rsp], 1
  0007f	eb 08		 jmp	 SHORT $LN4@Lower_boun
$LN3@Lower_boun:
  00081	c7 44 24 08 00
	00 00 00	 mov	 DWORD PTR tv78[rsp], 0
$LN4@Lower_boun:
  00089	0f b6 44 24 08	 movzx	 eax, BYTE PTR tv78[rsp]

; 1625 :     }

  0008e	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00092	c3		 ret	 0
??$_Lower_bound_duplicate@I@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEBA_NQEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@AEBI@Z ENDP ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::_Lower_bound_duplicate<unsigned int>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??$_Find_lower_bound@I@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEBA?AU?$_Tree_find_result@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@AEBI@Z
_TEXT	SEGMENT
$T1 = 0
_Trynode$ = 8
tv89 = 16
_Scary$ = 24
_Result$ = 32
$T2 = 56
$T3 = 64
$T4 = 72
$T5 = 80
$T6 = 88
this$ = 128
__$ReturnUdt$ = 136
_Keyval$ = 144
??$_Find_lower_bound@I@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEBA?AU?$_Tree_find_result@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@AEBI@Z PROC ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::_Find_lower_bound<unsigned int>, COMDAT

; 1628 :     _Tree_find_result<_Nodeptr> _Find_lower_bound(const _Keyty& _Keyval) const {

$LN36:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	56		 push	 rsi
  00010	57		 push	 rdi
  00011	48 83 ec 68	 sub	 rsp, 104		; 00000068H

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00015	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0001d	48 89 44 24 38	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00022	48 8b 44 24 38	 mov	 rax, QWORD PTR $T2[rsp]
  00027	48 89 44 24 40	 mov	 QWORD PTR $T3[rsp], rax

; 1629 :         const auto _Scary = _Get_scary();

  0002c	48 8b 44 24 40	 mov	 rax, QWORD PTR $T3[rsp]
  00031	48 89 44 24 18	 mov	 QWORD PTR _Scary$[rsp], rax

; 1630 :         _Tree_find_result<_Nodeptr> _Result{{_Scary->_Myhead->_Parent, _Tree_child::_Right}, _Scary->_Myhead};

  00036	48 8b 44 24 18	 mov	 rax, QWORD PTR _Scary$[rsp]
  0003b	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0003e	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00042	48 89 44 24 20	 mov	 QWORD PTR _Result$[rsp], rax
  00047	c7 44 24 28 00
	00 00 00	 mov	 DWORD PTR _Result$[rsp+8], 0
  0004f	48 8b 44 24 18	 mov	 rax, QWORD PTR _Scary$[rsp]
  00054	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00057	48 89 44 24 30	 mov	 QWORD PTR _Result$[rsp+16], rax

; 1631 :         _Nodeptr _Trynode = _Result._Location._Parent;

  0005c	48 8b 44 24 20	 mov	 rax, QWORD PTR _Result$[rsp]
  00061	48 89 44 24 08	 mov	 QWORD PTR _Trynode$[rsp], rax
$LN2@Find_lower:

; 1632 :         while (!_Trynode->_Isnil) {

  00066	48 8b 44 24 08	 mov	 rax, QWORD PTR _Trynode$[rsp]
  0006b	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  0006f	85 c0		 test	 eax, eax
  00071	0f 85 a3 00 00
	00		 jne	 $LN3@Find_lower

; 1633 :             _Result._Location._Parent = _Trynode;

  00077	48 8b 44 24 08	 mov	 rax, QWORD PTR _Trynode$[rsp]
  0007c	48 89 44 24 20	 mov	 QWORD PTR _Result$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map

; 67   :         return _Val.first;

  00081	48 8b 44 24 08	 mov	 rax, QWORD PTR _Trynode$[rsp]
  00086	48 83 c0 20	 add	 rax, 32			; 00000020H
  0008a	48 89 44 24 50	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1971 :         return _Mypair._Get_first();

  0008f	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1543 :         return *this;

  00097	48 89 44 24 48	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1971 :         return _Mypair._Get_first();

  0009c	48 8b 44 24 48	 mov	 rax, QWORD PTR $T4[rsp]
  000a1	48 89 44 24 58	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 2380 :         return _Left < _Right;

  000a6	48 8b 44 24 50	 mov	 rax, QWORD PTR $T5[rsp]
  000ab	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR _Keyval$[rsp]
  000b3	8b 09		 mov	 ecx, DWORD PTR [rcx]
  000b5	39 08		 cmp	 DWORD PTR [rax], ecx
  000b7	73 0a		 jae	 SHORT $LN31@Find_lower
  000b9	c7 44 24 10 01
	00 00 00	 mov	 DWORD PTR tv89[rsp], 1
  000c1	eb 08		 jmp	 SHORT $LN32@Find_lower
$LN31@Find_lower:
  000c3	c7 44 24 10 00
	00 00 00	 mov	 DWORD PTR tv89[rsp], 0
$LN32@Find_lower:
  000cb	0f b6 44 24 10	 movzx	 eax, BYTE PTR tv89[rsp]
  000d0	88 04 24	 mov	 BYTE PTR $T1[rsp], al
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1634 :             if (_DEBUG_LT_PRED(_Getcomp(), _Traits::_Kfn(_Trynode->_Myval), _Keyval)) {

  000d3	0f b6 04 24	 movzx	 eax, BYTE PTR $T1[rsp]
  000d7	0f b6 c0	 movzx	 eax, al
  000da	85 c0		 test	 eax, eax
  000dc	74 18		 je	 SHORT $LN4@Find_lower

; 1635 :                 _Result._Location._Child = _Tree_child::_Right;

  000de	c7 44 24 28 00
	00 00 00	 mov	 DWORD PTR _Result$[rsp+8], 0

; 1636 :                 _Trynode                 = _Trynode->_Right;

  000e6	48 8b 44 24 08	 mov	 rax, QWORD PTR _Trynode$[rsp]
  000eb	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  000ef	48 89 44 24 08	 mov	 QWORD PTR _Trynode$[rsp], rax

; 1637 :             } else {

  000f4	eb 1f		 jmp	 SHORT $LN5@Find_lower
$LN4@Find_lower:

; 1638 :                 _Result._Location._Child = _Tree_child::_Left;

  000f6	c7 44 24 28 01
	00 00 00	 mov	 DWORD PTR _Result$[rsp+8], 1

; 1639 :                 _Result._Bound           = _Trynode;

  000fe	48 8b 44 24 08	 mov	 rax, QWORD PTR _Trynode$[rsp]
  00103	48 89 44 24 30	 mov	 QWORD PTR _Result$[rsp+16], rax

; 1640 :                 _Trynode                 = _Trynode->_Left;

  00108	48 8b 44 24 08	 mov	 rax, QWORD PTR _Trynode$[rsp]
  0010d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00110	48 89 44 24 08	 mov	 QWORD PTR _Trynode$[rsp], rax
$LN5@Find_lower:

; 1641 :             }
; 1642 :         }

  00115	e9 4c ff ff ff	 jmp	 $LN2@Find_lower
$LN3@Find_lower:

; 1643 : 
; 1644 :         return _Result;

  0011a	48 8d 44 24 20	 lea	 rax, QWORD PTR _Result$[rsp]
  0011f	48 8b bc 24 88
	00 00 00	 mov	 rdi, QWORD PTR __$ReturnUdt$[rsp]
  00127	48 8b f0	 mov	 rsi, rax
  0012a	b9 18 00 00 00	 mov	 ecx, 24
  0012f	f3 a4		 rep movsb
  00131	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]

; 1645 :     }

  00139	48 83 c4 68	 add	 rsp, 104		; 00000068H
  0013d	5f		 pop	 rdi
  0013e	5e		 pop	 rsi
  0013f	c3		 ret	 0
??$_Find_lower_bound@I@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEBA?AU?$_Tree_find_result@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@AEBI@Z ENDP ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::_Find_lower_bound<unsigned int>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@@Z
_TEXT	SEGMENT
_Bytes$ = 32
_Ptr$ = 40
_Ptr$ = 48
this$ = 80
_Al$ = 88
??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@@Z PROC ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >::_Erase_head<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > >, COMDAT

; 775  :     void _Erase_head(_Alnode& _Al) noexcept {

$LN103:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 776  :         this->_Orphan_all();
; 777  :         _Erase_tree(_Al, _Myhead->_Parent);

  0000e	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00013	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00016	4c 8b 40 08	 mov	 r8, QWORD PTR [rax+8]
  0001a	48 8b 54 24 58	 mov	 rdx, QWORD PTR _Al$[rsp]
  0001f	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  00024	e8 00 00 00 00	 call	 ??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > >

; 778  :         _Alnode::value_type::_Freenode0(_Al, _Myhead);

  00029	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0002e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00031	48 89 44 24 30	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 723  :             _STD _Deallocate<_New_alignof<value_type>>(_Ptr, sizeof(value_type) * _Count);

  00036	b8 01 00 00 00	 mov	 eax, 1
  0003b	48 6b c0 30	 imul	 rax, rax, 48		; 00000030H
  0003f	48 89 44 24 20	 mov	 QWORD PTR _Bytes$[rsp], rax
  00044	48 8b 44 24 30	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00049	48 89 44 24 28	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  0004e	48 81 7c 24 20
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  00057	72 10		 jb	 SHORT $LN94@Erase_head

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  00059	48 8d 54 24 20	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  0005e	48 8d 4c 24 28	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  00063	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  00068	90		 npad	 1
$LN94@Erase_head:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  00069	48 8b 54 24 20	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  0006e	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  00073	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00078	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 779  :     }

  00079	48 83 c4 48	 add	 rsp, 72			; 00000048H
  0007d	c3		 ret	 0
??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@@Z ENDP ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >::_Erase_head<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??$_Find@I@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@AEBAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@AEBI@Z
_TEXT	SEGMENT
$T1 = 32
$T2 = 40
_Loc$ = 48
this$ = 96
_Keyval$ = 104
??$_Find@I@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@AEBAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@AEBI@Z PROC ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::_Find<unsigned int>, COMDAT

; 1383 :     _NODISCARD _Nodeptr _Find(const _Other& _Keyval) const {

$LN72:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 1384 :         const _Tree_find_result<_Nodeptr> _Loc = _Find_lower_bound(_Keyval);

  0000e	4c 8b 44 24 68	 mov	 r8, QWORD PTR _Keyval$[rsp]
  00013	48 8d 54 24 30	 lea	 rdx, QWORD PTR _Loc$[rsp]
  00018	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  0001d	e8 00 00 00 00	 call	 ??$_Find_lower_bound@I@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEBA?AU?$_Tree_find_result@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@AEBI@Z ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::_Find_lower_bound<unsigned int>
  00022	90		 npad	 1

; 1385 :         if (_Lower_bound_duplicate(_Loc._Bound, _Keyval)) {

  00023	4c 8b 44 24 68	 mov	 r8, QWORD PTR _Keyval$[rsp]
  00028	48 8b 54 24 40	 mov	 rdx, QWORD PTR _Loc$[rsp+16]
  0002d	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  00032	e8 00 00 00 00	 call	 ??$_Lower_bound_duplicate@I@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEBA_NQEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@AEBI@Z ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::_Lower_bound_duplicate<unsigned int>
  00037	0f b6 c0	 movzx	 eax, al
  0003a	85 c0		 test	 eax, eax
  0003c	74 07		 je	 SHORT $LN2@Find

; 1386 :             return _Loc._Bound;

  0003e	48 8b 44 24 40	 mov	 rax, QWORD PTR _Loc$[rsp+16]
  00043	eb 1c		 jmp	 SHORT $LN1@Find
$LN2@Find:

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00045	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0004a	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  0004f	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00054	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax

; 1387 :         }
; 1388 : 
; 1389 :         return _Get_scary()->_Myhead;

  00059	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
  0005e	48 8b 00	 mov	 rax, QWORD PTR [rax]
$LN1@Find:

; 1390 :     }

  00061	48 83 c4 58	 add	 rsp, 88			; 00000058H
  00065	c3		 ret	 0
??$_Find@I@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@AEBAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@AEBI@Z ENDP ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::_Find<unsigned int>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??$_Emplace@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@_N@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z
_TEXT	SEGMENT
$T1 = 32
$T2 = 33
$T3 = 34
$T4 = 35
tv148 = 36
tv151 = 40
_Bound$ = 48
_Keyval$5 = 56
_Val$ = 64
_Scary$ = 72
$T6 = 80
$T7 = 88
$T8 = 96
$T9 = 104
$T10 = 112
$T11 = 120
$T12 = 128
$T13 = 136
$T14 = 144
$T15 = 152
$T16 = 160
$T17 = 168
_Old_val$18 = 176
$T19 = 184
$T20 = 192
_Inserted$ = 200
$T21 = 208
$T22 = 216
$T23 = 224
_Loc$ = 232
$T24 = 256
$T25 = 264
$T26 = 288
$T27 = 304
this$ = 368
__$ReturnUdt$ = 376
<_Vals_0>$ = 384
??$_Emplace@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@_N@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z PROC ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::_Emplace<std::pair<unsigned int const ,mu2::EntityParty *> >, COMDAT

; 1010 :     pair<_Nodeptr, bool> _Emplace(_Valtys&&... _Vals) {

$LN317:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	56		 push	 rsi
  00010	57		 push	 rdi
  00011	48 81 ec 58 01
	00 00		 sub	 rsp, 344		; 00000158H

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00018	48 8b 84 24 70
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00020	48 89 44 24 50	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00025	48 8b 44 24 50	 mov	 rax, QWORD PTR $T6[rsp]
  0002a	48 89 44 24 58	 mov	 QWORD PTR $T7[rsp], rax

; 1011 :         using _In_place_key_extractor = typename _Traits::template _In_place_key_extractor<_Valtys...>;
; 1012 :         const auto _Scary             = _Get_scary();

  0002f	48 8b 44 24 58	 mov	 rax, QWORD PTR $T7[rsp]
  00034	48 89 44 24 48	 mov	 QWORD PTR _Scary$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 2175 :         return _Val.first;

  00039	48 8b 84 24 80
	01 00 00	 mov	 rax, QWORD PTR <_Vals_0>$[rsp]
  00041	48 89 44 24 60	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1016 :             const auto& _Keyval = _In_place_key_extractor::_Extract(_Vals...);

  00046	48 8b 44 24 60	 mov	 rax, QWORD PTR $T8[rsp]
  0004b	48 89 44 24 38	 mov	 QWORD PTR _Keyval$5[rsp], rax

; 1017 :             _Loc                = _Find_lower_bound(_Keyval);

  00050	4c 8b 44 24 38	 mov	 r8, QWORD PTR _Keyval$5[rsp]
  00055	48 8d 94 24 30
	01 00 00	 lea	 rdx, QWORD PTR $T27[rsp]
  0005d	48 8b 8c 24 70
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00065	e8 00 00 00 00	 call	 ??$_Find_lower_bound@I@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEBA?AU?$_Tree_find_result@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@AEBI@Z ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::_Find_lower_bound<unsigned int>
  0006a	48 8d 8c 24 e8
	00 00 00	 lea	 rcx, QWORD PTR _Loc$[rsp]
  00072	48 8b f9	 mov	 rdi, rcx
  00075	48 8b f0	 mov	 rsi, rax
  00078	b9 18 00 00 00	 mov	 ecx, 24
  0007d	f3 a4		 rep movsb

; 1018 :             if (_Lower_bound_duplicate(_Loc._Bound, _Keyval)) {

  0007f	48 8b 84 24 f8
	00 00 00	 mov	 rax, QWORD PTR _Loc$[rsp+16]
  00087	48 89 44 24 30	 mov	 QWORD PTR _Bound$[rsp], rax

; 1624 :         return !_Bound->_Isnil && !_DEBUG_LT_PRED(_Getcomp(), _Keyval, _Traits::_Kfn(_Bound->_Myval));

  0008c	48 8b 44 24 30	 mov	 rax, QWORD PTR _Bound$[rsp]
  00091	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  00095	85 c0		 test	 eax, eax
  00097	75 69		 jne	 SHORT $LN57@Emplace
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map

; 67   :         return _Val.first;

  00099	48 8b 44 24 30	 mov	 rax, QWORD PTR _Bound$[rsp]
  0009e	48 83 c0 20	 add	 rax, 32			; 00000020H
  000a2	48 89 44 24 70	 mov	 QWORD PTR $T10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1971 :         return _Mypair._Get_first();

  000a7	48 8b 84 24 70
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1543 :         return *this;

  000af	48 89 44 24 68	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1971 :         return _Mypair._Get_first();

  000b4	48 8b 44 24 68	 mov	 rax, QWORD PTR $T9[rsp]
  000b9	48 89 84 24 00
	01 00 00	 mov	 QWORD PTR $T24[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 2380 :         return _Left < _Right;

  000c1	48 8b 44 24 38	 mov	 rax, QWORD PTR _Keyval$5[rsp]
  000c6	48 8b 4c 24 70	 mov	 rcx, QWORD PTR $T10[rsp]
  000cb	8b 09		 mov	 ecx, DWORD PTR [rcx]
  000cd	39 08		 cmp	 DWORD PTR [rax], ecx
  000cf	73 0a		 jae	 SHORT $LN73@Emplace
  000d1	c7 44 24 24 01
	00 00 00	 mov	 DWORD PTR tv148[rsp], 1
  000d9	eb 08		 jmp	 SHORT $LN74@Emplace
$LN73@Emplace:
  000db	c7 44 24 24 00
	00 00 00	 mov	 DWORD PTR tv148[rsp], 0
$LN74@Emplace:
  000e3	0f b6 44 24 24	 movzx	 eax, BYTE PTR tv148[rsp]
  000e8	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1624 :         return !_Bound->_Isnil && !_DEBUG_LT_PRED(_Getcomp(), _Keyval, _Traits::_Kfn(_Bound->_Myval));

  000ec	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  000f1	0f b6 c0	 movzx	 eax, al
  000f4	85 c0		 test	 eax, eax
  000f6	75 0a		 jne	 SHORT $LN57@Emplace
  000f8	c7 44 24 28 01
	00 00 00	 mov	 DWORD PTR tv151[rsp], 1
  00100	eb 08		 jmp	 SHORT $LN58@Emplace
$LN57@Emplace:
  00102	c7 44 24 28 00
	00 00 00	 mov	 DWORD PTR tv151[rsp], 0
$LN58@Emplace:
  0010a	0f b6 44 24 28	 movzx	 eax, BYTE PTR tv151[rsp]
  0010f	88 44 24 21	 mov	 BYTE PTR $T2[rsp], al

; 1018 :             if (_Lower_bound_duplicate(_Loc._Bound, _Keyval)) {

  00113	0f b6 44 24 21	 movzx	 eax, BYTE PTR $T2[rsp]
  00118	0f b6 c0	 movzx	 eax, al
  0011b	85 c0		 test	 eax, eax
  0011d	74 55		 je	 SHORT $LN2@Emplace

; 1019 :                 return {_Loc._Bound, false};

  0011f	c6 44 24 22 00	 mov	 BYTE PTR $T3[rsp], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00124	48 8d 84 24 f8
	00 00 00	 lea	 rax, QWORD PTR _Loc$[rsp+16]
  0012c	48 89 44 24 78	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 274  :         : first(_STD forward<_Other1>(_Val1)), second(_STD forward<_Other2>(_Val2)) {

  00131	48 8b 84 24 78
	01 00 00	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]
  00139	48 8b 4c 24 78	 mov	 rcx, QWORD PTR $T11[rsp]
  0013e	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00141	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00144	48 8d 44 24 22	 lea	 rax, QWORD PTR $T3[rsp]
  00149	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 274  :         : first(_STD forward<_Other1>(_Val1)), second(_STD forward<_Other2>(_Val2)) {

  00151	48 8b 84 24 78
	01 00 00	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]
  00159	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR $T12[rsp]
  00161	0f b6 09	 movzx	 ecx, BYTE PTR [rcx]
  00164	88 48 08	 mov	 BYTE PTR [rax+8], cl
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1019 :                 return {_Loc._Bound, false};

  00167	48 8b 84 24 78
	01 00 00	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]
  0016f	e9 81 01 00 00	 jmp	 $LN1@Emplace
$LN2@Emplace:

; 1020 :             }
; 1021 : 
; 1022 :             _Check_grow_by_1();

  00174	48 8b 8c 24 70
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0017c	e8 00 00 00 00	 call	 ?_Check_grow_by_1@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEAAXXZ ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::_Check_grow_by_1
  00181	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00182	48 8b 84 24 80
	01 00 00	 mov	 rax, QWORD PTR <_Vals_0>$[rsp]
  0018a	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T14[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1975 :         return _Mypair._Myval2._Get_first();

  00192	48 8b 84 24 70
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  0019a	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1975 :         return _Mypair._Myval2._Get_first();

  001a2	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T13[rsp]
  001aa	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR $T15[rsp], rax

; 1023 :             _Inserted = _Tree_temp_node<_Alnode>(_Getal(), _Scary->_Myhead, _STD forward<_Valtys>(_Vals)...)._Release();

  001b2	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR $T14[rsp]
  001ba	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR $T15[rsp]
  001c2	4c 8b c8	 mov	 r9, rax
  001c5	48 8b 44 24 48	 mov	 rax, QWORD PTR _Scary$[rsp]
  001ca	4c 8b 00	 mov	 r8, QWORD PTR [rax]
  001cd	48 8b d1	 mov	 rdx, rcx
  001d0	48 8d 8c 24 08
	01 00 00	 lea	 rcx, QWORD PTR $T25[rsp]
  001d8	e8 00 00 00 00	 call	 ??$?0U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z ; std::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > >::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > ><std::pair<unsigned int const ,mu2::EntityParty *> >
  001dd	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T16[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1163 :         return _STD exchange(_Ptr, nullptr);

  001e5	48 c7 84 24 a8
	00 00 00 00 00
	00 00		 mov	 QWORD PTR $T17[rsp], 0
  001f1	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR $T16[rsp]
  001f9	48 83 c0 08	 add	 rax, 8
  001fd	48 89 44 24 40	 mov	 QWORD PTR _Val$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 773  :     _Ty _Old_val = static_cast<_Ty&&>(_Val);

  00202	48 8b 44 24 40	 mov	 rax, QWORD PTR _Val$[rsp]
  00207	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0020a	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR _Old_val$18[rsp], rax

; 774  :     _Val         = static_cast<_Other&&>(_New_val);

  00212	48 8b 44 24 40	 mov	 rax, QWORD PTR _Val$[rsp]
  00217	48 8b 8c 24 a8
	00 00 00	 mov	 rcx, QWORD PTR $T17[rsp]
  0021f	48 89 08	 mov	 QWORD PTR [rax], rcx

; 775  :     return _Old_val;

  00222	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR _Old_val$18[rsp]
  0022a	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T19[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1163 :         return _STD exchange(_Ptr, nullptr);

  00232	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T19[rsp]
  0023a	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T20[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1023 :             _Inserted = _Tree_temp_node<_Alnode>(_Getal(), _Scary->_Myhead, _STD forward<_Valtys>(_Vals)...)._Release();

  00242	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR $T20[rsp]
  0024a	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR _Inserted$[rsp], rax
  00252	48 8d 8c 24 08
	01 00 00	 lea	 rcx, QWORD PTR $T25[rsp]
  0025a	e8 00 00 00 00	 call	 ??1?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ ; std::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > >::~_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > >
  0025f	90		 npad	 1

; 1042 :         return {_Scary->_Insert_node(_Loc._Location, _Inserted), true};

  00260	48 8d 84 24 20
	01 00 00	 lea	 rax, QWORD PTR $T26[rsp]
  00268	48 8d 8c 24 e8
	00 00 00	 lea	 rcx, QWORD PTR _Loc$[rsp]
  00270	48 8b f8	 mov	 rdi, rax
  00273	48 8b f1	 mov	 rsi, rcx
  00276	b9 10 00 00 00	 mov	 ecx, 16
  0027b	f3 a4		 rep movsb
  0027d	4c 8b 84 24 c8
	00 00 00	 mov	 r8, QWORD PTR _Inserted$[rsp]
  00285	48 8d 94 24 20
	01 00 00	 lea	 rdx, QWORD PTR $T26[rsp]
  0028d	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _Scary$[rsp]
  00292	e8 00 00 00 00	 call	 ?_Insert_node@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@U?$_Tree_id@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@2@QEAU32@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >::_Insert_node
  00297	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR $T21[rsp], rax
  0029f	c6 44 24 23 01	 mov	 BYTE PTR $T4[rsp], 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  002a4	48 8d 84 24 d0
	00 00 00	 lea	 rax, QWORD PTR $T21[rsp]
  002ac	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR $T22[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 274  :         : first(_STD forward<_Other1>(_Val1)), second(_STD forward<_Other2>(_Val2)) {

  002b4	48 8b 84 24 78
	01 00 00	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]
  002bc	48 8b 8c 24 d8
	00 00 00	 mov	 rcx, QWORD PTR $T22[rsp]
  002c4	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  002c7	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  002ca	48 8d 44 24 23	 lea	 rax, QWORD PTR $T4[rsp]
  002cf	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR $T23[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 274  :         : first(_STD forward<_Other1>(_Val1)), second(_STD forward<_Other2>(_Val2)) {

  002d7	48 8b 84 24 78
	01 00 00	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]
  002df	48 8b 8c 24 e0
	00 00 00	 mov	 rcx, QWORD PTR $T23[rsp]
  002e7	0f b6 09	 movzx	 ecx, BYTE PTR [rcx]
  002ea	88 48 08	 mov	 BYTE PTR [rax+8], cl
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1042 :         return {_Scary->_Insert_node(_Loc._Location, _Inserted), true};

  002ed	48 8b 84 24 78
	01 00 00	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]
$LN1@Emplace:

; 1043 :     }

  002f5	48 81 c4 58 01
	00 00		 add	 rsp, 344		; 00000158H
  002fc	5f		 pop	 rdi
  002fd	5e		 pop	 rsi
  002fe	c3		 ret	 0
??$_Emplace@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@_N@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z ENDP ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::_Emplace<std::pair<unsigned int const ,mu2::EntityParty *> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??$insert@$0A@$0A@@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@QEAA?AU?$pair@V?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@@std@@_N@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z
_TEXT	SEGMENT
$T1 = 32
$T2 = 40
__param0$ = 48
$T3 = 56
$T4 = 64
$T5 = 72
$T6 = 80
_Result$ = 88
$T7 = 104
this$ = 128
__$ReturnUdt$ = 136
_Val$ = 144
??$insert@$0A@$0A@@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@QEAA?AU?$pair@V?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@@std@@_N@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z PROC ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::insert<0,0>, COMDAT

; 1245 :     pair<iterator, bool> insert(value_type&& _Val) {

$LN198:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 78	 sub	 rsp, 120		; 00000078H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00013	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR _Val$[rsp]
  0001b	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1246 :         const auto _Result = _Emplace(_STD move(_Val));

  00020	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00025	4c 8b c0	 mov	 r8, rax
  00028	48 8d 54 24 58	 lea	 rdx, QWORD PTR _Result$[rsp]
  0002d	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00035	e8 00 00 00 00	 call	 ??$_Emplace@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEAA?AU?$pair@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@_N@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::_Emplace<std::pair<unsigned int const ,mu2::EntityParty *> >
  0003a	90		 npad	 1

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  0003b	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00043	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00048	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
  0004d	48 89 44 24 68	 mov	 QWORD PTR $T7[rsp], rax

; 1247 :         return {iterator(_Result.first, _Get_scary()), _Result.second};

  00052	48 8b 44 24 58	 mov	 rax, QWORD PTR _Result$[rsp]
  00057	48 89 44 24 30	 mov	 QWORD PTR __param0$[rsp], rax

; 37   :     _Tree_unchecked_const_iterator(_Nodeptr _Pnode, const _Mytree* _Plist) noexcept : _Ptr(_Pnode) {

  0005c	48 8b 44 24 30	 mov	 rax, QWORD PTR __param0$[rsp]
  00061	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax
  00066	48 8d 44 24 38	 lea	 rax, QWORD PTR $T3[rsp]
  0006b	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax

; 1247 :         return {iterator(_Result.first, _Get_scary()), _Result.second};

  00070	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00075	48 89 44 24 48	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 274  :         : first(_STD forward<_Other1>(_Val1)), second(_STD forward<_Other2>(_Val2)) {

  0007a	48 8b 44 24 48	 mov	 rax, QWORD PTR $T5[rsp]
  0007f	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00082	48 8b 8c 24 88
	00 00 00	 mov	 rcx, QWORD PTR __$ReturnUdt$[rsp]
  0008a	48 89 01	 mov	 QWORD PTR [rcx], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  0008d	48 8d 44 24 60	 lea	 rax, QWORD PTR _Result$[rsp+8]
  00092	48 89 44 24 50	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 274  :         : first(_STD forward<_Other1>(_Val1)), second(_STD forward<_Other2>(_Val2)) {

  00097	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]
  0009f	48 8b 4c 24 50	 mov	 rcx, QWORD PTR $T6[rsp]
  000a4	0f b6 09	 movzx	 ecx, BYTE PTR [rcx]
  000a7	88 48 08	 mov	 BYTE PTR [rax+8], cl
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1247 :         return {iterator(_Result.first, _Get_scary()), _Result.second};

  000aa	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]

; 1248 :     }

  000b2	48 83 c4 78	 add	 rsp, 120		; 00000078H
  000b6	c3		 ret	 0
??$insert@$0A@$0A@@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@QEAA?AU?$pair@V?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@@std@@_N@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z ENDP ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::insert<0,0>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ
_TEXT	SEGMENT
_Pnode$1 = 0
_Pnode$ = 8
$T2 = 16
this$ = 48
??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ PROC ; std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >,std::_Iterator_base0>::operator++, COMDAT

; 49   :     _Tree_unchecked_const_iterator& operator++() noexcept {

$LN15:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 50   :         if (_Ptr->_Right->_Isnil) { // climb looking for right subtree

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00011	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00015	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  00019	85 c0		 test	 eax, eax
  0001b	74 4a		 je	 SHORT $LN4@operator
$LN2@operator:

; 51   :             _Nodeptr _Pnode;
; 52   :             while (!(_Pnode = _Ptr->_Parent)->_Isnil && _Ptr == _Pnode->_Right) {

  0001d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00022	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00025	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00029	48 89 04 24	 mov	 QWORD PTR _Pnode$1[rsp], rax
  0002d	48 8b 04 24	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  00031	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  00035	85 c0		 test	 eax, eax
  00037	75 20		 jne	 SHORT $LN3@operator
  00039	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003e	48 8b 0c 24	 mov	 rcx, QWORD PTR _Pnode$1[rsp]
  00042	48 8b 49 10	 mov	 rcx, QWORD PTR [rcx+16]
  00046	48 39 08	 cmp	 QWORD PTR [rax], rcx
  00049	75 0e		 jne	 SHORT $LN3@operator

; 53   :                 _Ptr = _Pnode; // ==> parent while right subtree

  0004b	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00050	48 8b 0c 24	 mov	 rcx, QWORD PTR _Pnode$1[rsp]
  00054	48 89 08	 mov	 QWORD PTR [rax], rcx

; 54   :             }

  00057	eb c4		 jmp	 SHORT $LN2@operator
$LN3@operator:

; 55   : 
; 56   :             _Ptr = _Pnode; // ==> parent (head if end())

  00059	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0005e	48 8b 0c 24	 mov	 rcx, QWORD PTR _Pnode$1[rsp]
  00062	48 89 08	 mov	 QWORD PTR [rax], rcx

; 57   :         } else {

  00065	eb 47		 jmp	 SHORT $LN5@operator
$LN4@operator:

; 58   :             _Ptr = _Mytree::_Min(_Ptr->_Right); // ==> smallest of right subtree

  00067	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0006c	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0006f	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00073	48 89 44 24 08	 mov	 QWORD PTR _Pnode$[rsp], rax
$LN9@operator:

; 476  :         while (!_Pnode->_Left->_Isnil) {

  00078	48 8b 44 24 08	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0007d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00080	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  00084	85 c0		 test	 eax, eax
  00086	75 0f		 jne	 SHORT $LN10@operator

; 477  :             _Pnode = _Pnode->_Left;

  00088	48 8b 44 24 08	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0008d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00090	48 89 44 24 08	 mov	 QWORD PTR _Pnode$[rsp], rax

; 478  :         }

  00095	eb e1		 jmp	 SHORT $LN9@operator
$LN10@operator:

; 479  : 
; 480  :         return _Pnode;

  00097	48 8b 44 24 08	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0009c	48 89 44 24 10	 mov	 QWORD PTR $T2[rsp], rax

; 58   :             _Ptr = _Mytree::_Min(_Ptr->_Right); // ==> smallest of right subtree

  000a1	48 8b 44 24 10	 mov	 rax, QWORD PTR $T2[rsp]
  000a6	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  000ab	48 89 01	 mov	 QWORD PTR [rcx], rax
$LN5@operator:

; 59   :         }
; 60   : 
; 61   :         return *this;

  000ae	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]

; 62   :     }

  000b3	48 83 c4 28	 add	 rsp, 40			; 00000028H
  000b7	c3		 ret	 0
??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ ENDP ; std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >,std::_Iterator_base0>::operator++
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
_TEXT	SEGMENT
_Ptr_container$ = 48
_Block_size$ = 56
_Ptr$ = 64
$T1 = 72
_Bytes$ = 96
??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z PROC ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>, COMDAT

; 182  : __declspec(allocator) void* _Allocate_manually_vector_aligned(const size_t _Bytes) {

$LN7:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 183  :     // allocate _Bytes manually aligned to at least _Big_allocation_alignment
; 184  :     const size_t _Block_size = _Non_user_size + _Bytes;

  00009	48 8b 44 24 60	 mov	 rax, QWORD PTR _Bytes$[rsp]
  0000e	48 83 c0 27	 add	 rax, 39			; 00000027H
  00012	48 89 44 24 38	 mov	 QWORD PTR _Block_size$[rsp], rax

; 185  :     if (_Block_size <= _Bytes) {

  00017	48 8b 44 24 60	 mov	 rax, QWORD PTR _Bytes$[rsp]
  0001c	48 39 44 24 38	 cmp	 QWORD PTR _Block_size$[rsp], rax
  00021	77 06		 ja	 SHORT $LN2@Allocate_m

; 186  :         _Throw_bad_array_new_length(); // add overflow

  00023	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00028	90		 npad	 1
$LN2@Allocate_m:

; 136  :         return ::operator new(_Bytes);

  00029	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Block_size$[rsp]
  0002e	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00033	48 89 44 24 48	 mov	 QWORD PTR $T1[rsp], rax

; 187  :     }
; 188  : 
; 189  :     const uintptr_t _Ptr_container = reinterpret_cast<uintptr_t>(_Traits::_Allocate(_Block_size));

  00038	48 8b 44 24 48	 mov	 rax, QWORD PTR $T1[rsp]
  0003d	48 89 44 24 30	 mov	 QWORD PTR _Ptr_container$[rsp], rax

; 190  :     _STL_VERIFY(_Ptr_container != 0, "invalid argument"); // validate even in release since we're doing p[-1]

  00042	48 83 7c 24 30
	00		 cmp	 QWORD PTR _Ptr_container$[rsp], 0
  00048	75 19		 jne	 SHORT $LN3@Allocate_m
  0004a	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR [rsp+32], 0
  00053	45 33 c9	 xor	 r9d, r9d
  00056	45 33 c0	 xor	 r8d, r8d
  00059	33 d2		 xor	 edx, edx
  0005b	33 c9		 xor	 ecx, ecx
  0005d	e8 00 00 00 00	 call	 _invoke_watson
  00062	90		 npad	 1
$LN3@Allocate_m:

; 191  :     void* const _Ptr = reinterpret_cast<void*>((_Ptr_container + _Non_user_size) & ~(_Big_allocation_alignment - 1));

  00063	48 8b 44 24 30	 mov	 rax, QWORD PTR _Ptr_container$[rsp]
  00068	48 83 c0 27	 add	 rax, 39			; 00000027H
  0006c	48 83 e0 e0	 and	 rax, -32		; ffffffffffffffe0H
  00070	48 89 44 24 40	 mov	 QWORD PTR _Ptr$[rsp], rax

; 192  :     static_cast<uintptr_t*>(_Ptr)[-1] = _Ptr_container;

  00075	b8 08 00 00 00	 mov	 eax, 8
  0007a	48 6b c0 ff	 imul	 rax, rax, -1
  0007e	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  00083	48 8b 54 24 30	 mov	 rdx, QWORD PTR _Ptr_container$[rsp]
  00088	48 89 14 01	 mov	 QWORD PTR [rcx+rax], rdx

; 193  : 
; 194  : #ifdef _DEBUG
; 195  :     static_cast<uintptr_t*>(_Ptr)[-2] = _Big_allocation_sentinel;
; 196  : #endif // defined(_DEBUG)
; 197  :     return _Ptr;

  0008c	48 8b 44 24 40	 mov	 rax, QWORD PTR _Ptr$[rsp]
$LN4@Allocate_m:

; 198  : }

  00091	48 83 c4 58	 add	 rsp, 88			; 00000058H
  00095	c3		 ret	 0
??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ENDP ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??_GManagerPartyEntity@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GManagerPartyEntity@mu2@@UEAAPEAXI@Z PROC		; mu2::ManagerPartyEntity::`scalar deleting destructor', COMDAT
$LN5:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00012	e8 00 00 00 00	 call	 ??1ManagerPartyEntity@mu2@@UEAA@XZ ; mu2::ManagerPartyEntity::~ManagerPartyEntity
  00017	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  0001b	83 e0 01	 and	 eax, 1
  0001e	85 c0		 test	 eax, eax
  00020	74 10		 je	 SHORT $LN2@scalar
  00022	ba 28 00 00 00	 mov	 edx, 40			; 00000028H
  00027	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0002c	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00031	90		 npad	 1
$LN2@scalar:
  00032	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00037	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0003b	c3		 ret	 0
??_GManagerPartyEntity@mu2@@UEAAPEAXI@Z ENDP		; mu2::ManagerPartyEntity::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map
;	COMDAT ??0?$map@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@@std@@QEAA@XZ
_TEXT	SEGMENT
$T1 = 32
__formal$ = 40
this$ = 48
$T2 = 56
this$ = 64
this$ = 72
this$ = 96
??0?$map@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@@std@@QEAA@XZ PROC ; std::map<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> > >::map<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> > >, COMDAT

; 106  :     map() : _Mybase(key_compare()) {}

$LN142:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 58	 sub	 rsp, 88			; 00000058H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 904  :     _Tree(const key_compare& _Parg) : _Mypair(_One_then_variadic_args_t{}, _Parg, _Zero_then_variadic_args_t{}) {

  00009	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 89 44 24 40	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00013	48 8d 44 24 20	 lea	 rax, QWORD PTR $T1[rsp]
  00018	48 89 44 24 38	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1536 :         : _Ty1(_STD forward<_Other1>(_Val1)), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  0001d	48 8b 44 24 38	 mov	 rax, QWORD PTR $T2[rsp]
  00022	0f b6 00	 movzx	 eax, BYTE PTR [rax]
  00025	88 44 24 28	 mov	 BYTE PTR __formal$[rsp], al
  00029	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0002e	48 89 44 24 48	 mov	 QWORD PTR this$[rsp], rax

; 1531 :         : _Ty1(), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  00033	48 8b 44 24 48	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 89 44 24 30	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 450  :     _Tree_val() noexcept : _Myhead(), _Mysize(0) {}

  0003d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00042	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0

; 905  :         _Alloc_sentinel_and_proxy();

  00056	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  0005b	e8 00 00 00 00	 call	 ?_Alloc_sentinel_and_proxy@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEAAXXZ ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::_Alloc_sentinel_and_proxy
  00060	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map

; 106  :     map() : _Mybase(key_compare()) {}

  00061	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00066	48 83 c4 58	 add	 rsp, 88			; 00000058H
  0006a	c3		 ret	 0
??0?$map@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@@std@@QEAA@XZ ENDP ; std::map<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> > >::map<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ?_Insert_node@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@U?$_Tree_id@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@2@QEAU32@@Z
_TEXT	SEGMENT
_Pnode$1 = 32
_Head$ = 40
_Parent_sibling$2 = 48
_Parent_sibling$3 = 56
this$ = 80
_Loc$ = 88
_Newnode$ = 96
?_Insert_node@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@U?$_Tree_id@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@2@QEAU32@@Z PROC ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >::_Insert_node, COMDAT

; 669  :     _Nodeptr _Insert_node(const _Tree_id<_Nodeptr> _Loc, const _Nodeptr _Newnode) noexcept {

$LN60:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 670  :         ++_Mysize;

  00013	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00018	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0001c	48 ff c0	 inc	 rax
  0001f	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  00024	48 89 41 08	 mov	 QWORD PTR [rcx+8], rax

; 671  :         const auto _Head  = _Myhead;

  00028	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0002d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00030	48 89 44 24 28	 mov	 QWORD PTR _Head$[rsp], rax

; 672  :         _Newnode->_Parent = _Loc._Parent;

  00035	48 8b 44 24 60	 mov	 rax, QWORD PTR _Newnode$[rsp]
  0003a	48 8b 4c 24 58	 mov	 rcx, QWORD PTR _Loc$[rsp]
  0003f	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00042	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 673  :         if (_Loc._Parent == _Head) { // first node in tree, just set head values

  00046	48 8b 44 24 58	 mov	 rax, QWORD PTR _Loc$[rsp]
  0004b	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Head$[rsp]
  00050	48 39 08	 cmp	 QWORD PTR [rax], rcx
  00053	75 3c		 jne	 SHORT $LN5@Insert_nod

; 674  :             _Head->_Left     = _Newnode;

  00055	48 8b 44 24 28	 mov	 rax, QWORD PTR _Head$[rsp]
  0005a	48 8b 4c 24 60	 mov	 rcx, QWORD PTR _Newnode$[rsp]
  0005f	48 89 08	 mov	 QWORD PTR [rax], rcx

; 675  :             _Head->_Parent   = _Newnode;

  00062	48 8b 44 24 28	 mov	 rax, QWORD PTR _Head$[rsp]
  00067	48 8b 4c 24 60	 mov	 rcx, QWORD PTR _Newnode$[rsp]
  0006c	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 676  :             _Head->_Right    = _Newnode;

  00070	48 8b 44 24 28	 mov	 rax, QWORD PTR _Head$[rsp]
  00075	48 8b 4c 24 60	 mov	 rcx, QWORD PTR _Newnode$[rsp]
  0007a	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 677  :             _Newnode->_Color = _Black; // the root is black

  0007e	48 8b 44 24 60	 mov	 rax, QWORD PTR _Newnode$[rsp]
  00083	c6 40 18 01	 mov	 BYTE PTR [rax+24], 1

; 678  :             return _Newnode;

  00087	48 8b 44 24 60	 mov	 rax, QWORD PTR _Newnode$[rsp]
  0008c	e9 52 02 00 00	 jmp	 $LN1@Insert_nod
$LN5@Insert_nod:

; 679  :         }
; 680  : 
; 681  :         _STL_INTERNAL_CHECK(_Loc._Child != _Tree_child::_Unused);
; 682  :         if (_Loc._Child == _Tree_child::_Right) { // add to right of _Loc._Parent

  00091	48 8b 44 24 58	 mov	 rax, QWORD PTR _Loc$[rsp]
  00096	83 78 08 00	 cmp	 DWORD PTR [rax+8], 0
  0009a	75 34		 jne	 SHORT $LN6@Insert_nod

; 683  :             _STL_INTERNAL_CHECK(_Loc._Parent->_Right->_Isnil);
; 684  :             _Loc._Parent->_Right = _Newnode;

  0009c	48 8b 44 24 58	 mov	 rax, QWORD PTR _Loc$[rsp]
  000a1	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000a4	48 8b 4c 24 60	 mov	 rcx, QWORD PTR _Newnode$[rsp]
  000a9	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 685  :             if (_Loc._Parent == _Head->_Right) { // remember rightmost node

  000ad	48 8b 44 24 58	 mov	 rax, QWORD PTR _Loc$[rsp]
  000b2	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Head$[rsp]
  000b7	48 8b 49 10	 mov	 rcx, QWORD PTR [rcx+16]
  000bb	48 39 08	 cmp	 QWORD PTR [rax], rcx
  000be	75 0e		 jne	 SHORT $LN8@Insert_nod

; 686  :                 _Head->_Right = _Newnode;

  000c0	48 8b 44 24 28	 mov	 rax, QWORD PTR _Head$[rsp]
  000c5	48 8b 4c 24 60	 mov	 rcx, QWORD PTR _Newnode$[rsp]
  000ca	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx
$LN8@Insert_nod:

; 687  :             }
; 688  :         } else { // add to left of _Loc._Parent

  000ce	eb 2f		 jmp	 SHORT $LN7@Insert_nod
$LN6@Insert_nod:

; 689  :             _STL_INTERNAL_CHECK(_Loc._Parent->_Left->_Isnil);
; 690  :             _Loc._Parent->_Left = _Newnode;

  000d0	48 8b 44 24 58	 mov	 rax, QWORD PTR _Loc$[rsp]
  000d5	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000d8	48 8b 4c 24 60	 mov	 rcx, QWORD PTR _Newnode$[rsp]
  000dd	48 89 08	 mov	 QWORD PTR [rax], rcx

; 691  :             if (_Loc._Parent == _Head->_Left) { // remember leftmost node

  000e0	48 8b 44 24 58	 mov	 rax, QWORD PTR _Loc$[rsp]
  000e5	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Head$[rsp]
  000ea	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  000ed	48 39 08	 cmp	 QWORD PTR [rax], rcx
  000f0	75 0d		 jne	 SHORT $LN7@Insert_nod

; 692  :                 _Head->_Left = _Newnode;

  000f2	48 8b 44 24 28	 mov	 rax, QWORD PTR _Head$[rsp]
  000f7	48 8b 4c 24 60	 mov	 rcx, QWORD PTR _Newnode$[rsp]
  000fc	48 89 08	 mov	 QWORD PTR [rax], rcx
$LN7@Insert_nod:

; 693  :             }
; 694  :         }
; 695  : 
; 696  :         for (_Nodeptr _Pnode = _Newnode; _Pnode->_Parent->_Color == _Red;) {

  000ff	48 8b 44 24 60	 mov	 rax, QWORD PTR _Newnode$[rsp]
  00104	48 89 44 24 20	 mov	 QWORD PTR _Pnode$1[rsp], rax
$LN2@Insert_nod:
  00109	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  0010e	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00112	0f be 40 18	 movsx	 eax, BYTE PTR [rax+24]
  00116	85 c0		 test	 eax, eax
  00118	0f 85 b3 01 00
	00		 jne	 $LN3@Insert_nod

; 697  :             if (_Pnode->_Parent == _Pnode->_Parent->_Parent->_Left) { // fixup red-red in left subtree

  0011e	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  00123	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00127	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0012b	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Pnode$1[rsp]
  00130	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00133	48 39 41 08	 cmp	 QWORD PTR [rcx+8], rax
  00137	0f 85 cb 00 00
	00		 jne	 $LN10@Insert_nod

; 698  :                 const auto _Parent_sibling = _Pnode->_Parent->_Parent->_Right;

  0013d	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  00142	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00146	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0014a	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0014e	48 89 44 24 30	 mov	 QWORD PTR _Parent_sibling$2[rsp], rax

; 699  :                 if (_Parent_sibling->_Color == _Red) { // parent's sibling has two red children, blacken both

  00153	48 8b 44 24 30	 mov	 rax, QWORD PTR _Parent_sibling$2[rsp]
  00158	0f be 40 18	 movsx	 eax, BYTE PTR [rax+24]
  0015c	85 c0		 test	 eax, eax
  0015e	75 3b		 jne	 SHORT $LN12@Insert_nod

; 700  :                     _Pnode->_Parent->_Color          = _Black;

  00160	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  00165	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00169	c6 40 18 01	 mov	 BYTE PTR [rax+24], 1

; 701  :                     _Parent_sibling->_Color          = _Black;

  0016d	48 8b 44 24 30	 mov	 rax, QWORD PTR _Parent_sibling$2[rsp]
  00172	c6 40 18 01	 mov	 BYTE PTR [rax+24], 1

; 702  :                     _Pnode->_Parent->_Parent->_Color = _Red;

  00176	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  0017b	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0017f	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00183	c6 40 18 00	 mov	 BYTE PTR [rax+24], 0

; 703  :                     _Pnode                           = _Pnode->_Parent->_Parent;

  00187	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  0018c	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00190	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00194	48 89 44 24 20	 mov	 QWORD PTR _Pnode$1[rsp], rax

; 704  :                 } else { // parent's sibling has red and black children

  00199	eb 68		 jmp	 SHORT $LN13@Insert_nod
$LN12@Insert_nod:

; 705  :                     if (_Pnode == _Pnode->_Parent->_Right) { // rotate right child to left

  0019b	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  001a0	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  001a4	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  001a8	48 39 44 24 20	 cmp	 QWORD PTR _Pnode$1[rsp], rax
  001ad	75 1e		 jne	 SHORT $LN14@Insert_nod

; 706  :                         _Pnode = _Pnode->_Parent;

  001af	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  001b4	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  001b8	48 89 44 24 20	 mov	 QWORD PTR _Pnode$1[rsp], rax

; 707  :                         _Lrotate(_Pnode);

  001bd	48 8b 54 24 20	 mov	 rdx, QWORD PTR _Pnode$1[rsp]
  001c2	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  001c7	e8 00 00 00 00	 call	 ?_Lrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >::_Lrotate
  001cc	90		 npad	 1
$LN14@Insert_nod:

; 708  :                     }
; 709  : 
; 710  :                     _Pnode->_Parent->_Color          = _Black; // propagate red up

  001cd	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  001d2	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  001d6	c6 40 18 01	 mov	 BYTE PTR [rax+24], 1

; 711  :                     _Pnode->_Parent->_Parent->_Color = _Red;

  001da	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  001df	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  001e3	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  001e7	c6 40 18 00	 mov	 BYTE PTR [rax+24], 0

; 712  :                     _Rrotate(_Pnode->_Parent->_Parent);

  001eb	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  001f0	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  001f4	48 8b 50 08	 mov	 rdx, QWORD PTR [rax+8]
  001f8	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  001fd	e8 00 00 00 00	 call	 ?_Rrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >::_Rrotate
  00202	90		 npad	 1
$LN13@Insert_nod:

; 713  :                 }
; 714  :             } else { // fixup red-red in right subtree

  00203	e9 c4 00 00 00	 jmp	 $LN11@Insert_nod
$LN10@Insert_nod:

; 715  :                 const auto _Parent_sibling = _Pnode->_Parent->_Parent->_Left;

  00208	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  0020d	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00211	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00215	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00218	48 89 44 24 38	 mov	 QWORD PTR _Parent_sibling$3[rsp], rax

; 716  :                 if (_Parent_sibling->_Color == _Red) { // parent's sibling has two red children, blacken both

  0021d	48 8b 44 24 38	 mov	 rax, QWORD PTR _Parent_sibling$3[rsp]
  00222	0f be 40 18	 movsx	 eax, BYTE PTR [rax+24]
  00226	85 c0		 test	 eax, eax
  00228	75 3b		 jne	 SHORT $LN15@Insert_nod

; 717  :                     _Pnode->_Parent->_Color          = _Black;

  0022a	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  0022f	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00233	c6 40 18 01	 mov	 BYTE PTR [rax+24], 1

; 718  :                     _Parent_sibling->_Color          = _Black;

  00237	48 8b 44 24 38	 mov	 rax, QWORD PTR _Parent_sibling$3[rsp]
  0023c	c6 40 18 01	 mov	 BYTE PTR [rax+24], 1

; 719  :                     _Pnode->_Parent->_Parent->_Color = _Red;

  00240	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  00245	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00249	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0024d	c6 40 18 00	 mov	 BYTE PTR [rax+24], 0

; 720  :                     _Pnode                           = _Pnode->_Parent->_Parent;

  00251	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  00256	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0025a	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0025e	48 89 44 24 20	 mov	 QWORD PTR _Pnode$1[rsp], rax

; 721  :                 } else { // parent's sibling has red and black children

  00263	eb 67		 jmp	 SHORT $LN11@Insert_nod
$LN15@Insert_nod:

; 722  :                     if (_Pnode == _Pnode->_Parent->_Left) { // rotate left child to right

  00265	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  0026a	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0026e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00271	48 39 44 24 20	 cmp	 QWORD PTR _Pnode$1[rsp], rax
  00276	75 1e		 jne	 SHORT $LN17@Insert_nod

; 723  :                         _Pnode = _Pnode->_Parent;

  00278	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  0027d	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00281	48 89 44 24 20	 mov	 QWORD PTR _Pnode$1[rsp], rax

; 724  :                         _Rrotate(_Pnode);

  00286	48 8b 54 24 20	 mov	 rdx, QWORD PTR _Pnode$1[rsp]
  0028b	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  00290	e8 00 00 00 00	 call	 ?_Rrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >::_Rrotate
  00295	90		 npad	 1
$LN17@Insert_nod:

; 725  :                     }
; 726  : 
; 727  :                     _Pnode->_Parent->_Color          = _Black; // propagate red up

  00296	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  0029b	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0029f	c6 40 18 01	 mov	 BYTE PTR [rax+24], 1

; 728  :                     _Pnode->_Parent->_Parent->_Color = _Red;

  002a3	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  002a8	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  002ac	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  002b0	c6 40 18 00	 mov	 BYTE PTR [rax+24], 0

; 729  :                     _Lrotate(_Pnode->_Parent->_Parent);

  002b4	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  002b9	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  002bd	48 8b 50 08	 mov	 rdx, QWORD PTR [rax+8]
  002c1	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  002c6	e8 00 00 00 00	 call	 ?_Lrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >::_Lrotate
  002cb	90		 npad	 1
$LN11@Insert_nod:

; 730  :                 }
; 731  :             }
; 732  :         }

  002cc	e9 38 fe ff ff	 jmp	 $LN2@Insert_nod
$LN3@Insert_nod:

; 733  : 
; 734  :         _Head->_Parent->_Color = _Black; // root is always black

  002d1	48 8b 44 24 28	 mov	 rax, QWORD PTR _Head$[rsp]
  002d6	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  002da	c6 40 18 01	 mov	 BYTE PTR [rax+24], 1

; 735  :         return _Newnode;

  002de	48 8b 44 24 60	 mov	 rax, QWORD PTR _Newnode$[rsp]
$LN1@Insert_nod:

; 736  :     }

  002e3	48 83 c4 48	 add	 rsp, 72			; 00000048H
  002e7	c3		 ret	 0
?_Insert_node@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@U?$_Tree_id@PEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@2@QEAU32@@Z ENDP ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >::_Insert_node
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ?_Extract@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z
_TEXT	SEGMENT
_Pnode$ = 32
_Fixnodeparent$ = 40
_Fixnode$ = 48
_Erasednode$ = 56
_Tmp$1 = 64
_Pnode$ = 72
_Pnode$ = 80
tv133 = 88
tv144 = 96
_Left$ = 104
_Right$ = 112
$T2 = 120
$T3 = 128
$T4 = 136
$T5 = 144
$T6 = 152
this$ = 176
_Where$ = 184
?_Extract@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z PROC ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >::_Extract, COMDAT

; 527  :     _Nodeptr _Extract(_Unchecked_const_iterator _Where) noexcept {

$LN157:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 81 ec a8 00
	00 00		 sub	 rsp, 168		; 000000a8H

; 528  :         _Nodeptr _Erasednode = _Where._Ptr; // node to erase

  00011	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR _Where$[rsp]
  00019	48 89 44 24 38	 mov	 QWORD PTR _Erasednode$[rsp], rax

; 529  :         ++_Where; // save successor iterator for return

  0001e	48 8d 8c 24 b8
	00 00 00	 lea	 rcx, QWORD PTR _Where$[rsp]
  00026	e8 00 00 00 00	 call	 ??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ ; std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >,std::_Iterator_base0>::operator++

; 530  : 
; 531  :         _Nodeptr _Fixnode; // the node to recolor as needed
; 532  :         _Nodeptr _Fixnodeparent; // parent of _Fixnode (which may be nil)
; 533  :         _Nodeptr _Pnode = _Erasednode;

  0002b	48 8b 44 24 38	 mov	 rax, QWORD PTR _Erasednode$[rsp]
  00030	48 89 44 24 20	 mov	 QWORD PTR _Pnode$[rsp], rax

; 534  : 
; 535  :         if (_Pnode->_Left->_Isnil) {

  00035	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0003a	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0003d	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  00041	85 c0		 test	 eax, eax
  00043	74 10		 je	 SHORT $LN5@Extract

; 536  :             _Fixnode = _Pnode->_Right; // stitch up right subtree

  00045	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0004a	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0004e	48 89 44 24 30	 mov	 QWORD PTR _Fixnode$[rsp], rax
  00053	eb 3b		 jmp	 SHORT $LN6@Extract
$LN5@Extract:

; 537  :         } else if (_Pnode->_Right->_Isnil) {

  00055	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0005a	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0005e	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  00062	85 c0		 test	 eax, eax
  00064	74 0f		 je	 SHORT $LN7@Extract

; 538  :             _Fixnode = _Pnode->_Left; // stitch up left subtree

  00066	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0006b	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0006e	48 89 44 24 30	 mov	 QWORD PTR _Fixnode$[rsp], rax

; 539  :         } else { // two subtrees, must lift successor node to replace erased

  00073	eb 1b		 jmp	 SHORT $LN6@Extract
$LN7@Extract:

; 540  :             _Pnode   = _Where._Ptr; // _Pnode is successor node

  00075	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR _Where$[rsp]
  0007d	48 89 44 24 20	 mov	 QWORD PTR _Pnode$[rsp], rax

; 541  :             _Fixnode = _Pnode->_Right; // _Fixnode is only subtree

  00082	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00087	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0008b	48 89 44 24 30	 mov	 QWORD PTR _Fixnode$[rsp], rax
$LN6@Extract:

; 542  :         }
; 543  : 
; 544  :         if (_Pnode == _Erasednode) { // at most one subtree, relink it

  00090	48 8b 44 24 38	 mov	 rax, QWORD PTR _Erasednode$[rsp]
  00095	48 39 44 24 20	 cmp	 QWORD PTR _Pnode$[rsp], rax
  0009a	0f 85 8c 01 00
	00		 jne	 $LN9@Extract

; 545  :             _Fixnodeparent = _Erasednode->_Parent;

  000a0	48 8b 44 24 38	 mov	 rax, QWORD PTR _Erasednode$[rsp]
  000a5	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  000a9	48 89 44 24 28	 mov	 QWORD PTR _Fixnodeparent$[rsp], rax

; 546  :             if (!_Fixnode->_Isnil) {

  000ae	48 8b 44 24 30	 mov	 rax, QWORD PTR _Fixnode$[rsp]
  000b3	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  000b7	85 c0		 test	 eax, eax
  000b9	75 0e		 jne	 SHORT $LN11@Extract

; 547  :                 _Fixnode->_Parent = _Fixnodeparent; // link up

  000bb	48 8b 44 24 30	 mov	 rax, QWORD PTR _Fixnode$[rsp]
  000c0	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Fixnodeparent$[rsp]
  000c5	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx
$LN11@Extract:

; 548  :             }
; 549  : 
; 550  :             if (_Myhead->_Parent == _Erasednode) {

  000c9	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000d1	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000d4	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Erasednode$[rsp]
  000d9	48 39 48 08	 cmp	 QWORD PTR [rax+8], rcx
  000dd	75 16		 jne	 SHORT $LN12@Extract

; 551  :                 _Myhead->_Parent = _Fixnode; // link down from root

  000df	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000e7	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000ea	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Fixnode$[rsp]
  000ef	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx
  000f3	eb 2c		 jmp	 SHORT $LN13@Extract
$LN12@Extract:

; 552  :             } else if (_Fixnodeparent->_Left == _Erasednode) {

  000f5	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  000fa	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Erasednode$[rsp]
  000ff	48 39 08	 cmp	 QWORD PTR [rax], rcx
  00102	75 0f		 jne	 SHORT $LN14@Extract

; 553  :                 _Fixnodeparent->_Left = _Fixnode; // link down to left

  00104	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  00109	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Fixnode$[rsp]
  0010e	48 89 08	 mov	 QWORD PTR [rax], rcx

; 554  :             } else {

  00111	eb 0e		 jmp	 SHORT $LN13@Extract
$LN14@Extract:

; 555  :                 _Fixnodeparent->_Right = _Fixnode; // link down to right

  00113	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  00118	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Fixnode$[rsp]
  0011d	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx
$LN13@Extract:

; 556  :             }
; 557  : 
; 558  :             if (_Myhead->_Left == _Erasednode) {

  00121	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00129	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0012c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Erasednode$[rsp]
  00131	48 39 08	 cmp	 QWORD PTR [rax], rcx
  00134	75 69		 jne	 SHORT $LN16@Extract

; 559  :                 _Myhead->_Left = _Fixnode->_Isnil ? _Fixnodeparent // smallest is parent of erased node

  00136	48 8b 44 24 30	 mov	 rax, QWORD PTR _Fixnode$[rsp]
  0013b	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  0013f	85 c0		 test	 eax, eax
  00141	74 0c		 je	 SHORT $LN42@Extract
  00143	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  00148	48 89 44 24 58	 mov	 QWORD PTR tv133[rsp], rax
  0014d	eb 3d		 jmp	 SHORT $LN43@Extract
$LN42@Extract:
  0014f	48 8b 44 24 30	 mov	 rax, QWORD PTR _Fixnode$[rsp]
  00154	48 89 44 24 48	 mov	 QWORD PTR _Pnode$[rsp], rax
$LN64@Extract:

; 476  :         while (!_Pnode->_Left->_Isnil) {

  00159	48 8b 44 24 48	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0015e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00161	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  00165	85 c0		 test	 eax, eax
  00167	75 0f		 jne	 SHORT $LN65@Extract

; 477  :             _Pnode = _Pnode->_Left;

  00169	48 8b 44 24 48	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0016e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00171	48 89 44 24 48	 mov	 QWORD PTR _Pnode$[rsp], rax

; 478  :         }

  00176	eb e1		 jmp	 SHORT $LN64@Extract
$LN65@Extract:

; 479  : 
; 480  :         return _Pnode;

  00178	48 8b 44 24 48	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0017d	48 89 44 24 78	 mov	 QWORD PTR $T2[rsp], rax

; 559  :                 _Myhead->_Left = _Fixnode->_Isnil ? _Fixnodeparent // smallest is parent of erased node

  00182	48 8b 44 24 78	 mov	 rax, QWORD PTR $T2[rsp]
  00187	48 89 44 24 58	 mov	 QWORD PTR tv133[rsp], rax
$LN43@Extract:
  0018c	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00194	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00197	48 8b 4c 24 58	 mov	 rcx, QWORD PTR tv133[rsp]
  0019c	48 89 08	 mov	 QWORD PTR [rax], rcx
$LN16@Extract:

; 560  :                                                   : _Min(_Fixnode); // smallest in relinked subtree
; 561  :             }
; 562  : 
; 563  :             if (_Myhead->_Right == _Erasednode) {

  0019f	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  001a7	48 8b 00	 mov	 rax, QWORD PTR [rax]
  001aa	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Erasednode$[rsp]
  001af	48 39 48 10	 cmp	 QWORD PTR [rax+16], rcx
  001b3	75 72		 jne	 SHORT $LN17@Extract

; 564  :                 _Myhead->_Right = _Fixnode->_Isnil ? _Fixnodeparent // largest is parent of erased node

  001b5	48 8b 44 24 30	 mov	 rax, QWORD PTR _Fixnode$[rsp]
  001ba	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  001be	85 c0		 test	 eax, eax
  001c0	74 0c		 je	 SHORT $LN44@Extract
  001c2	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  001c7	48 89 44 24 60	 mov	 QWORD PTR tv144[rsp], rax
  001cc	eb 45		 jmp	 SHORT $LN45@Extract
$LN44@Extract:
  001ce	48 8b 44 24 30	 mov	 rax, QWORD PTR _Fixnode$[rsp]
  001d3	48 89 44 24 50	 mov	 QWORD PTR _Pnode$[rsp], rax
$LN71@Extract:

; 468  :         while (!_Pnode->_Right->_Isnil) {

  001d8	48 8b 44 24 50	 mov	 rax, QWORD PTR _Pnode$[rsp]
  001dd	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  001e1	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  001e5	85 c0		 test	 eax, eax
  001e7	75 10		 jne	 SHORT $LN72@Extract

; 469  :             _Pnode = _Pnode->_Right;

  001e9	48 8b 44 24 50	 mov	 rax, QWORD PTR _Pnode$[rsp]
  001ee	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  001f2	48 89 44 24 50	 mov	 QWORD PTR _Pnode$[rsp], rax

; 470  :         }

  001f7	eb df		 jmp	 SHORT $LN71@Extract
$LN72@Extract:

; 471  : 
; 472  :         return _Pnode;

  001f9	48 8b 44 24 50	 mov	 rax, QWORD PTR _Pnode$[rsp]
  001fe	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T3[rsp], rax

; 564  :                 _Myhead->_Right = _Fixnode->_Isnil ? _Fixnodeparent // largest is parent of erased node

  00206	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR $T3[rsp]
  0020e	48 89 44 24 60	 mov	 QWORD PTR tv144[rsp], rax
$LN45@Extract:
  00213	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0021b	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0021e	48 8b 4c 24 60	 mov	 rcx, QWORD PTR tv144[rsp]
  00223	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx
$LN17@Extract:

; 565  :                                                    : _Max(_Fixnode); // largest in relinked subtree
; 566  :             }
; 567  :         } else { // erased has two subtrees, _Pnode is successor to erased

  00227	e9 83 01 00 00	 jmp	 $LN10@Extract
$LN9@Extract:

; 568  :             _Erasednode->_Left->_Parent = _Pnode; // link left up

  0022c	48 8b 44 24 38	 mov	 rax, QWORD PTR _Erasednode$[rsp]
  00231	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00234	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Pnode$[rsp]
  00239	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 569  :             _Pnode->_Left               = _Erasednode->_Left; // link successor down

  0023d	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00242	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Erasednode$[rsp]
  00247	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0024a	48 89 08	 mov	 QWORD PTR [rax], rcx

; 570  : 
; 571  :             if (_Pnode == _Erasednode->_Right) {

  0024d	48 8b 44 24 38	 mov	 rax, QWORD PTR _Erasednode$[rsp]
  00252	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00256	48 39 44 24 20	 cmp	 QWORD PTR _Pnode$[rsp], rax
  0025b	75 0c		 jne	 SHORT $LN18@Extract

; 572  :                 _Fixnodeparent = _Pnode; // successor is next to erased

  0025d	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00262	48 89 44 24 28	 mov	 QWORD PTR _Fixnodeparent$[rsp], rax

; 573  :             } else { // successor further down, link in place of erased

  00267	eb 5a		 jmp	 SHORT $LN19@Extract
$LN18@Extract:

; 574  :                 _Fixnodeparent = _Pnode->_Parent; // parent is successor's

  00269	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0026e	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00272	48 89 44 24 28	 mov	 QWORD PTR _Fixnodeparent$[rsp], rax

; 575  :                 if (!_Fixnode->_Isnil) {

  00277	48 8b 44 24 30	 mov	 rax, QWORD PTR _Fixnode$[rsp]
  0027c	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  00280	85 c0		 test	 eax, eax
  00282	75 0e		 jne	 SHORT $LN20@Extract

; 576  :                     _Fixnode->_Parent = _Fixnodeparent; // link fix up

  00284	48 8b 44 24 30	 mov	 rax, QWORD PTR _Fixnode$[rsp]
  00289	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Fixnodeparent$[rsp]
  0028e	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx
$LN20@Extract:

; 577  :                 }
; 578  : 
; 579  :                 _Fixnodeparent->_Left        = _Fixnode; // link fix down

  00292	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  00297	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Fixnode$[rsp]
  0029c	48 89 08	 mov	 QWORD PTR [rax], rcx

; 580  :                 _Pnode->_Right               = _Erasednode->_Right; // link next down

  0029f	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  002a4	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Erasednode$[rsp]
  002a9	48 8b 49 10	 mov	 rcx, QWORD PTR [rcx+16]
  002ad	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 581  :                 _Erasednode->_Right->_Parent = _Pnode; // right up

  002b1	48 8b 44 24 38	 mov	 rax, QWORD PTR _Erasednode$[rsp]
  002b6	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  002ba	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Pnode$[rsp]
  002bf	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx
$LN19@Extract:

; 582  :             }
; 583  : 
; 584  :             if (_Myhead->_Parent == _Erasednode) {

  002c3	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  002cb	48 8b 00	 mov	 rax, QWORD PTR [rax]
  002ce	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Erasednode$[rsp]
  002d3	48 39 48 08	 cmp	 QWORD PTR [rax+8], rcx
  002d7	75 16		 jne	 SHORT $LN21@Extract

; 585  :                 _Myhead->_Parent = _Pnode; // link down from root

  002d9	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  002e1	48 8b 00	 mov	 rax, QWORD PTR [rax]
  002e4	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Pnode$[rsp]
  002e9	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx
  002ed	eb 38		 jmp	 SHORT $LN22@Extract
$LN21@Extract:

; 586  :             } else if (_Erasednode->_Parent->_Left == _Erasednode) {

  002ef	48 8b 44 24 38	 mov	 rax, QWORD PTR _Erasednode$[rsp]
  002f4	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  002f8	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Erasednode$[rsp]
  002fd	48 39 08	 cmp	 QWORD PTR [rax], rcx
  00300	75 13		 jne	 SHORT $LN23@Extract

; 587  :                 _Erasednode->_Parent->_Left = _Pnode; // link down to left

  00302	48 8b 44 24 38	 mov	 rax, QWORD PTR _Erasednode$[rsp]
  00307	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0030b	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Pnode$[rsp]
  00310	48 89 08	 mov	 QWORD PTR [rax], rcx

; 588  :             } else {

  00313	eb 12		 jmp	 SHORT $LN22@Extract
$LN23@Extract:

; 589  :                 _Erasednode->_Parent->_Right = _Pnode; // link down to right

  00315	48 8b 44 24 38	 mov	 rax, QWORD PTR _Erasednode$[rsp]
  0031a	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0031e	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Pnode$[rsp]
  00323	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx
$LN22@Extract:

; 590  :             }
; 591  : 
; 592  :             _Pnode->_Parent = _Erasednode->_Parent; // link successor up

  00327	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0032c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Erasednode$[rsp]
  00331	48 8b 49 08	 mov	 rcx, QWORD PTR [rcx+8]
  00335	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 593  :             _STD swap(_Pnode->_Color, _Erasednode->_Color); // recolor it

  00339	48 8b 44 24 38	 mov	 rax, QWORD PTR _Erasednode$[rsp]
  0033e	48 83 c0 18	 add	 rax, 24
  00342	48 89 44 24 70	 mov	 QWORD PTR _Right$[rsp], rax
  00347	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0034c	48 83 c0 18	 add	 rax, 24
  00350	48 89 44 24 68	 mov	 QWORD PTR _Left$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00355	48 8b 44 24 68	 mov	 rax, QWORD PTR _Left$[rsp]
  0035a	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 139  :     _Ty _Tmp = _STD move(_Left);

  00362	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T4[rsp]
  0036a	0f b6 00	 movzx	 eax, BYTE PTR [rax]
  0036d	88 44 24 40	 mov	 BYTE PTR _Tmp$1[rsp], al
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00371	48 8b 44 24 70	 mov	 rax, QWORD PTR _Right$[rsp]
  00376	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 140  :     _Left    = _STD move(_Right);

  0037e	48 8b 44 24 68	 mov	 rax, QWORD PTR _Left$[rsp]
  00383	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR $T5[rsp]
  0038b	0f b6 09	 movzx	 ecx, BYTE PTR [rcx]
  0038e	88 08		 mov	 BYTE PTR [rax], cl
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00390	48 8d 44 24 40	 lea	 rax, QWORD PTR _Tmp$1[rsp]
  00395	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 141  :     _Right   = _STD move(_Tmp);

  0039d	48 8b 44 24 70	 mov	 rax, QWORD PTR _Right$[rsp]
  003a2	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR $T6[rsp]
  003aa	0f b6 09	 movzx	 ecx, BYTE PTR [rcx]
  003ad	88 08		 mov	 BYTE PTR [rax], cl
$LN10@Extract:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 596  :         if (_Erasednode->_Color == _Black) { // erasing black link, must recolor/rebalance tree

  003af	48 8b 44 24 38	 mov	 rax, QWORD PTR _Erasednode$[rsp]
  003b4	0f be 40 18	 movsx	 eax, BYTE PTR [rax+24]
  003b8	83 f8 01	 cmp	 eax, 1
  003bb	0f 85 af 02 00
	00		 jne	 $LN25@Extract

; 597  :             for (; _Fixnode != _Myhead->_Parent && _Fixnode->_Color == _Black; _Fixnodeparent = _Fixnode->_Parent) {

  003c1	eb 0e		 jmp	 SHORT $LN4@Extract
$LN2@Extract:
  003c3	48 8b 44 24 30	 mov	 rax, QWORD PTR _Fixnode$[rsp]
  003c8	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  003cc	48 89 44 24 28	 mov	 QWORD PTR _Fixnodeparent$[rsp], rax
$LN4@Extract:
  003d1	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  003d9	48 8b 00	 mov	 rax, QWORD PTR [rax]
  003dc	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  003e0	48 39 44 24 30	 cmp	 QWORD PTR _Fixnode$[rsp], rax
  003e5	0f 84 7c 02 00
	00		 je	 $LN3@Extract
  003eb	48 8b 44 24 30	 mov	 rax, QWORD PTR _Fixnode$[rsp]
  003f0	0f be 40 18	 movsx	 eax, BYTE PTR [rax+24]
  003f4	83 f8 01	 cmp	 eax, 1
  003f7	0f 85 6a 02 00
	00		 jne	 $LN3@Extract

; 598  :                 if (_Fixnode == _Fixnodeparent->_Left) { // fixup left subtree

  003fd	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  00402	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00405	48 39 44 24 30	 cmp	 QWORD PTR _Fixnode$[rsp], rax
  0040a	0f 85 2f 01 00
	00		 jne	 $LN26@Extract

; 599  :                     _Pnode = _Fixnodeparent->_Right;

  00410	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  00415	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00419	48 89 44 24 20	 mov	 QWORD PTR _Pnode$[rsp], rax

; 600  :                     if (_Pnode->_Color == _Red) { // rotate red up from right subtree

  0041e	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00423	0f be 40 18	 movsx	 eax, BYTE PTR [rax+24]
  00427	85 c0		 test	 eax, eax
  00429	75 32		 jne	 SHORT $LN28@Extract

; 601  :                         _Pnode->_Color         = _Black;

  0042b	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00430	c6 40 18 01	 mov	 BYTE PTR [rax+24], 1

; 602  :                         _Fixnodeparent->_Color = _Red;

  00434	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  00439	c6 40 18 00	 mov	 BYTE PTR [rax+24], 0

; 603  :                         _Lrotate(_Fixnodeparent);

  0043d	48 8b 54 24 28	 mov	 rdx, QWORD PTR _Fixnodeparent$[rsp]
  00442	48 8b 8c 24 b0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0044a	e8 00 00 00 00	 call	 ?_Lrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >::_Lrotate

; 604  :                         _Pnode = _Fixnodeparent->_Right;

  0044f	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  00454	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00458	48 89 44 24 20	 mov	 QWORD PTR _Pnode$[rsp], rax
$LN28@Extract:

; 605  :                     }
; 606  : 
; 607  :                     if (_Pnode->_Isnil) {

  0045d	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00462	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  00466	85 c0		 test	 eax, eax
  00468	74 0f		 je	 SHORT $LN29@Extract

; 608  :                         _Fixnode = _Fixnodeparent; // shouldn't happen

  0046a	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  0046f	48 89 44 24 30	 mov	 QWORD PTR _Fixnode$[rsp], rax

; 609  :                     } else if (_Pnode->_Left->_Color == _Black

  00474	e9 c1 00 00 00	 jmp	 $LN30@Extract
$LN29@Extract:

; 610  :                                && _Pnode->_Right->_Color == _Black) { // redden right subtree with black children

  00479	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0047e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00481	0f be 40 18	 movsx	 eax, BYTE PTR [rax+24]
  00485	83 f8 01	 cmp	 eax, 1
  00488	75 2a		 jne	 SHORT $LN31@Extract
  0048a	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0048f	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00493	0f be 40 18	 movsx	 eax, BYTE PTR [rax+24]
  00497	83 f8 01	 cmp	 eax, 1
  0049a	75 18		 jne	 SHORT $LN31@Extract

; 611  :                         _Pnode->_Color = _Red;

  0049c	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  004a1	c6 40 18 00	 mov	 BYTE PTR [rax+24], 0

; 612  :                         _Fixnode       = _Fixnodeparent;

  004a5	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  004aa	48 89 44 24 30	 mov	 QWORD PTR _Fixnode$[rsp], rax

; 613  :                     } else { // must rearrange right subtree

  004af	e9 86 00 00 00	 jmp	 $LN30@Extract
$LN31@Extract:

; 614  :                         if (_Pnode->_Right->_Color == _Black) { // rotate red up from left sub-subtree

  004b4	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  004b9	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  004bd	0f be 40 18	 movsx	 eax, BYTE PTR [rax+24]
  004c1	83 f8 01	 cmp	 eax, 1
  004c4	75 35		 jne	 SHORT $LN33@Extract

; 615  :                             _Pnode->_Left->_Color = _Black;

  004c6	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  004cb	48 8b 00	 mov	 rax, QWORD PTR [rax]
  004ce	c6 40 18 01	 mov	 BYTE PTR [rax+24], 1

; 616  :                             _Pnode->_Color        = _Red;

  004d2	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  004d7	c6 40 18 00	 mov	 BYTE PTR [rax+24], 0

; 617  :                             _Rrotate(_Pnode);

  004db	48 8b 54 24 20	 mov	 rdx, QWORD PTR _Pnode$[rsp]
  004e0	48 8b 8c 24 b0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  004e8	e8 00 00 00 00	 call	 ?_Rrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >::_Rrotate

; 618  :                             _Pnode = _Fixnodeparent->_Right;

  004ed	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  004f2	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  004f6	48 89 44 24 20	 mov	 QWORD PTR _Pnode$[rsp], rax
$LN33@Extract:

; 619  :                         }
; 620  : 
; 621  :                         _Pnode->_Color         = _Fixnodeparent->_Color;

  004fb	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00500	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Fixnodeparent$[rsp]
  00505	0f b6 49 18	 movzx	 ecx, BYTE PTR [rcx+24]
  00509	88 48 18	 mov	 BYTE PTR [rax+24], cl

; 622  :                         _Fixnodeparent->_Color = _Black;

  0050c	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  00511	c6 40 18 01	 mov	 BYTE PTR [rax+24], 1

; 623  :                         _Pnode->_Right->_Color = _Black;

  00515	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0051a	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0051e	c6 40 18 01	 mov	 BYTE PTR [rax+24], 1

; 624  :                         _Lrotate(_Fixnodeparent);

  00522	48 8b 54 24 28	 mov	 rdx, QWORD PTR _Fixnodeparent$[rsp]
  00527	48 8b 8c 24 b0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0052f	e8 00 00 00 00	 call	 ?_Lrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >::_Lrotate
  00534	90		 npad	 1

; 625  :                         break; // tree now recolored/rebalanced

  00535	e9 2d 01 00 00	 jmp	 $LN3@Extract
$LN30@Extract:

; 626  :                     }
; 627  :                 } else { // fixup right subtree

  0053a	e9 23 01 00 00	 jmp	 $LN27@Extract
$LN26@Extract:

; 628  :                     _Pnode = _Fixnodeparent->_Left;

  0053f	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  00544	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00547	48 89 44 24 20	 mov	 QWORD PTR _Pnode$[rsp], rax

; 629  :                     if (_Pnode->_Color == _Red) { // rotate red up from left subtree

  0054c	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00551	0f be 40 18	 movsx	 eax, BYTE PTR [rax+24]
  00555	85 c0		 test	 eax, eax
  00557	75 31		 jne	 SHORT $LN34@Extract

; 630  :                         _Pnode->_Color         = _Black;

  00559	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0055e	c6 40 18 01	 mov	 BYTE PTR [rax+24], 1

; 631  :                         _Fixnodeparent->_Color = _Red;

  00562	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  00567	c6 40 18 00	 mov	 BYTE PTR [rax+24], 0

; 632  :                         _Rrotate(_Fixnodeparent);

  0056b	48 8b 54 24 28	 mov	 rdx, QWORD PTR _Fixnodeparent$[rsp]
  00570	48 8b 8c 24 b0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00578	e8 00 00 00 00	 call	 ?_Rrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >::_Rrotate

; 633  :                         _Pnode = _Fixnodeparent->_Left;

  0057d	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  00582	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00585	48 89 44 24 20	 mov	 QWORD PTR _Pnode$[rsp], rax
$LN34@Extract:

; 634  :                     }
; 635  : 
; 636  :                     if (_Pnode->_Isnil) {

  0058a	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0058f	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  00593	85 c0		 test	 eax, eax
  00595	74 0f		 je	 SHORT $LN35@Extract

; 637  :                         _Fixnode = _Fixnodeparent; // shouldn't happen

  00597	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  0059c	48 89 44 24 30	 mov	 QWORD PTR _Fixnode$[rsp], rax

; 638  :                     } else if (_Pnode->_Right->_Color == _Black

  005a1	e9 bc 00 00 00	 jmp	 $LN27@Extract
$LN35@Extract:

; 639  :                                && _Pnode->_Left->_Color == _Black) { // redden left subtree with black children

  005a6	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  005ab	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  005af	0f be 40 18	 movsx	 eax, BYTE PTR [rax+24]
  005b3	83 f8 01	 cmp	 eax, 1
  005b6	75 29		 jne	 SHORT $LN37@Extract
  005b8	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  005bd	48 8b 00	 mov	 rax, QWORD PTR [rax]
  005c0	0f be 40 18	 movsx	 eax, BYTE PTR [rax+24]
  005c4	83 f8 01	 cmp	 eax, 1
  005c7	75 18		 jne	 SHORT $LN37@Extract

; 640  :                         _Pnode->_Color = _Red;

  005c9	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  005ce	c6 40 18 00	 mov	 BYTE PTR [rax+24], 0

; 641  :                         _Fixnode       = _Fixnodeparent;

  005d2	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  005d7	48 89 44 24 30	 mov	 QWORD PTR _Fixnode$[rsp], rax

; 642  :                     } else { // must rearrange left subtree

  005dc	e9 81 00 00 00	 jmp	 $LN27@Extract
$LN37@Extract:

; 643  :                         if (_Pnode->_Left->_Color == _Black) { // rotate red up from right sub-subtree

  005e1	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  005e6	48 8b 00	 mov	 rax, QWORD PTR [rax]
  005e9	0f be 40 18	 movsx	 eax, BYTE PTR [rax+24]
  005ed	83 f8 01	 cmp	 eax, 1
  005f0	75 35		 jne	 SHORT $LN39@Extract

; 644  :                             _Pnode->_Right->_Color = _Black;

  005f2	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  005f7	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  005fb	c6 40 18 01	 mov	 BYTE PTR [rax+24], 1

; 645  :                             _Pnode->_Color         = _Red;

  005ff	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00604	c6 40 18 00	 mov	 BYTE PTR [rax+24], 0

; 646  :                             _Lrotate(_Pnode);

  00608	48 8b 54 24 20	 mov	 rdx, QWORD PTR _Pnode$[rsp]
  0060d	48 8b 8c 24 b0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00615	e8 00 00 00 00	 call	 ?_Lrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >::_Lrotate

; 647  :                             _Pnode = _Fixnodeparent->_Left;

  0061a	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  0061f	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00622	48 89 44 24 20	 mov	 QWORD PTR _Pnode$[rsp], rax
$LN39@Extract:

; 648  :                         }
; 649  : 
; 650  :                         _Pnode->_Color         = _Fixnodeparent->_Color;

  00627	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0062c	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Fixnodeparent$[rsp]
  00631	0f b6 49 18	 movzx	 ecx, BYTE PTR [rcx+24]
  00635	88 48 18	 mov	 BYTE PTR [rax+24], cl

; 651  :                         _Fixnodeparent->_Color = _Black;

  00638	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  0063d	c6 40 18 01	 mov	 BYTE PTR [rax+24], 1

; 652  :                         _Pnode->_Left->_Color  = _Black;

  00641	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00646	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00649	c6 40 18 01	 mov	 BYTE PTR [rax+24], 1

; 653  :                         _Rrotate(_Fixnodeparent);

  0064d	48 8b 54 24 28	 mov	 rdx, QWORD PTR _Fixnodeparent$[rsp]
  00652	48 8b 8c 24 b0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0065a	e8 00 00 00 00	 call	 ?_Rrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >::_Rrotate
  0065f	90		 npad	 1

; 654  :                         break; // tree now recolored/rebalanced

  00660	eb 05		 jmp	 SHORT $LN3@Extract
$LN27@Extract:

; 655  :                     }
; 656  :                 }
; 657  :             }

  00662	e9 5c fd ff ff	 jmp	 $LN2@Extract
$LN3@Extract:

; 658  : 
; 659  :             _Fixnode->_Color = _Black; // stopping node is black

  00667	48 8b 44 24 30	 mov	 rax, QWORD PTR _Fixnode$[rsp]
  0066c	c6 40 18 01	 mov	 BYTE PTR [rax+24], 1
$LN25@Extract:

; 660  :         }
; 661  : 
; 662  :         if (0 < _Mysize) {

  00670	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00678	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  0067d	76 1b		 jbe	 SHORT $LN40@Extract

; 663  :             --_Mysize;

  0067f	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00687	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0068b	48 ff c8	 dec	 rax
  0068e	48 8b 8c 24 b0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00696	48 89 41 08	 mov	 QWORD PTR [rcx+8], rax
$LN40@Extract:

; 664  :         }
; 665  : 
; 666  :         return _Erasednode;

  0069a	48 8b 44 24 38	 mov	 rax, QWORD PTR _Erasednode$[rsp]

; 667  :     }

  0069f	48 81 c4 a8 00
	00 00		 add	 rsp, 168		; 000000a8H
  006a6	c3		 ret	 0
?_Extract@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z ENDP ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >::_Extract
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ?_Rrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@@Z
_TEXT	SEGMENT
_Pnode$ = 0
this$ = 32
_Wherenode$ = 40
?_Rrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@@Z PROC ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >::_Rrotate, COMDAT

; 505  :     void _Rrotate(_Nodeptr _Wherenode) noexcept { // promote left node to root of subtree

$LN9:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 18	 sub	 rsp, 24

; 506  :         _Nodeptr _Pnode   = _Wherenode->_Left;

  0000e	48 8b 44 24 28	 mov	 rax, QWORD PTR _Wherenode$[rsp]
  00013	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00016	48 89 04 24	 mov	 QWORD PTR _Pnode$[rsp], rax

; 507  :         _Wherenode->_Left = _Pnode->_Right;

  0001a	48 8b 44 24 28	 mov	 rax, QWORD PTR _Wherenode$[rsp]
  0001f	48 8b 0c 24	 mov	 rcx, QWORD PTR _Pnode$[rsp]
  00023	48 8b 49 10	 mov	 rcx, QWORD PTR [rcx+16]
  00027	48 89 08	 mov	 QWORD PTR [rax], rcx

; 508  : 
; 509  :         if (!_Pnode->_Right->_Isnil) {

  0002a	48 8b 04 24	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0002e	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00032	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  00036	85 c0		 test	 eax, eax
  00038	75 11		 jne	 SHORT $LN2@Rrotate

; 510  :             _Pnode->_Right->_Parent = _Wherenode;

  0003a	48 8b 04 24	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0003e	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00042	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Wherenode$[rsp]
  00047	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx
$LN2@Rrotate:

; 511  :         }
; 512  : 
; 513  :         _Pnode->_Parent = _Wherenode->_Parent;

  0004b	48 8b 04 24	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0004f	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Wherenode$[rsp]
  00054	48 8b 49 08	 mov	 rcx, QWORD PTR [rcx+8]
  00058	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 514  : 
; 515  :         if (_Wherenode == _Myhead->_Parent) {

  0005c	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00061	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00064	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00068	48 39 44 24 28	 cmp	 QWORD PTR _Wherenode$[rsp], rax
  0006d	75 12		 jne	 SHORT $LN3@Rrotate

; 516  :             _Myhead->_Parent = _Pnode;

  0006f	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00074	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00077	48 8b 0c 24	 mov	 rcx, QWORD PTR _Pnode$[rsp]
  0007b	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx
  0007f	eb 37		 jmp	 SHORT $LN4@Rrotate
$LN3@Rrotate:

; 517  :         } else if (_Wherenode == _Wherenode->_Parent->_Right) {

  00081	48 8b 44 24 28	 mov	 rax, QWORD PTR _Wherenode$[rsp]
  00086	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0008a	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0008e	48 39 44 24 28	 cmp	 QWORD PTR _Wherenode$[rsp], rax
  00093	75 13		 jne	 SHORT $LN5@Rrotate

; 518  :             _Wherenode->_Parent->_Right = _Pnode;

  00095	48 8b 44 24 28	 mov	 rax, QWORD PTR _Wherenode$[rsp]
  0009a	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0009e	48 8b 0c 24	 mov	 rcx, QWORD PTR _Pnode$[rsp]
  000a2	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 519  :         } else {

  000a6	eb 10		 jmp	 SHORT $LN4@Rrotate
$LN5@Rrotate:

; 520  :             _Wherenode->_Parent->_Left = _Pnode;

  000a8	48 8b 44 24 28	 mov	 rax, QWORD PTR _Wherenode$[rsp]
  000ad	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  000b1	48 8b 0c 24	 mov	 rcx, QWORD PTR _Pnode$[rsp]
  000b5	48 89 08	 mov	 QWORD PTR [rax], rcx
$LN4@Rrotate:

; 521  :         }
; 522  : 
; 523  :         _Pnode->_Right      = _Wherenode;

  000b8	48 8b 04 24	 mov	 rax, QWORD PTR _Pnode$[rsp]
  000bc	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Wherenode$[rsp]
  000c1	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 524  :         _Wherenode->_Parent = _Pnode;

  000c5	48 8b 44 24 28	 mov	 rax, QWORD PTR _Wherenode$[rsp]
  000ca	48 8b 0c 24	 mov	 rcx, QWORD PTR _Pnode$[rsp]
  000ce	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 525  :     }

  000d2	48 83 c4 18	 add	 rsp, 24
  000d6	c3		 ret	 0
?_Rrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@@Z ENDP ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >::_Rrotate
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ?_Lrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@@Z
_TEXT	SEGMENT
_Pnode$ = 0
this$ = 32
_Wherenode$ = 40
?_Lrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@@Z PROC ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >::_Lrotate, COMDAT

; 483  :     void _Lrotate(_Nodeptr _Wherenode) noexcept { // promote right node to root of subtree

$LN9:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 18	 sub	 rsp, 24

; 484  :         _Nodeptr _Pnode    = _Wherenode->_Right;

  0000e	48 8b 44 24 28	 mov	 rax, QWORD PTR _Wherenode$[rsp]
  00013	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00017	48 89 04 24	 mov	 QWORD PTR _Pnode$[rsp], rax

; 485  :         _Wherenode->_Right = _Pnode->_Left;

  0001b	48 8b 44 24 28	 mov	 rax, QWORD PTR _Wherenode$[rsp]
  00020	48 8b 0c 24	 mov	 rcx, QWORD PTR _Pnode$[rsp]
  00024	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00027	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 486  : 
; 487  :         if (!_Pnode->_Left->_Isnil) {

  0002b	48 8b 04 24	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0002f	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00032	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  00036	85 c0		 test	 eax, eax
  00038	75 10		 jne	 SHORT $LN2@Lrotate

; 488  :             _Pnode->_Left->_Parent = _Wherenode;

  0003a	48 8b 04 24	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0003e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00041	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Wherenode$[rsp]
  00046	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx
$LN2@Lrotate:

; 489  :         }
; 490  : 
; 491  :         _Pnode->_Parent = _Wherenode->_Parent;

  0004a	48 8b 04 24	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0004e	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Wherenode$[rsp]
  00053	48 8b 49 08	 mov	 rcx, QWORD PTR [rcx+8]
  00057	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 492  : 
; 493  :         if (_Wherenode == _Myhead->_Parent) {

  0005b	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00060	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00063	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00067	48 39 44 24 28	 cmp	 QWORD PTR _Wherenode$[rsp], rax
  0006c	75 12		 jne	 SHORT $LN3@Lrotate

; 494  :             _Myhead->_Parent = _Pnode;

  0006e	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00073	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00076	48 8b 0c 24	 mov	 rcx, QWORD PTR _Pnode$[rsp]
  0007a	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx
  0007e	eb 36		 jmp	 SHORT $LN4@Lrotate
$LN3@Lrotate:

; 495  :         } else if (_Wherenode == _Wherenode->_Parent->_Left) {

  00080	48 8b 44 24 28	 mov	 rax, QWORD PTR _Wherenode$[rsp]
  00085	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00089	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0008c	48 39 44 24 28	 cmp	 QWORD PTR _Wherenode$[rsp], rax
  00091	75 12		 jne	 SHORT $LN5@Lrotate

; 496  :             _Wherenode->_Parent->_Left = _Pnode;

  00093	48 8b 44 24 28	 mov	 rax, QWORD PTR _Wherenode$[rsp]
  00098	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0009c	48 8b 0c 24	 mov	 rcx, QWORD PTR _Pnode$[rsp]
  000a0	48 89 08	 mov	 QWORD PTR [rax], rcx

; 497  :         } else {

  000a3	eb 11		 jmp	 SHORT $LN4@Lrotate
$LN5@Lrotate:

; 498  :             _Wherenode->_Parent->_Right = _Pnode;

  000a5	48 8b 44 24 28	 mov	 rax, QWORD PTR _Wherenode$[rsp]
  000aa	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  000ae	48 8b 0c 24	 mov	 rcx, QWORD PTR _Pnode$[rsp]
  000b2	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx
$LN4@Lrotate:

; 499  :         }
; 500  : 
; 501  :         _Pnode->_Left       = _Wherenode;

  000b6	48 8b 04 24	 mov	 rax, QWORD PTR _Pnode$[rsp]
  000ba	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Wherenode$[rsp]
  000bf	48 89 08	 mov	 QWORD PTR [rax], rcx

; 502  :         _Wherenode->_Parent = _Pnode;

  000c2	48 8b 44 24 28	 mov	 rax, QWORD PTR _Wherenode$[rsp]
  000c7	48 8b 0c 24	 mov	 rcx, QWORD PTR _Pnode$[rsp]
  000cb	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 503  :     }

  000cf	48 83 c4 18	 add	 rsp, 24
  000d3	c3		 ret	 0
?_Lrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@@Z ENDP ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >::_Lrotate
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ?_Alloc_sentinel_and_proxy@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEAAXXZ
_TEXT	SEGMENT
$T1 = 32
$S140$ = 33
$T2 = 40
$T3 = 48
$T4 = 56
$T5 = 64
_Scary$ = 72
_Alproxy$ = 80
this$ = 112
?_Alloc_sentinel_and_proxy@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEAAXXZ PROC ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::_Alloc_sentinel_and_proxy, COMDAT

; 1953 :     void _Alloc_sentinel_and_proxy() {

$LN107:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi
  00006	48 83 ec 60	 sub	 rsp, 96			; 00000060H

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  0000a	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0000f	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00014	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
  00019	48 89 44 24 30	 mov	 QWORD PTR $T3[rsp], rax

; 1954 :         const auto _Scary = _Get_scary();

  0001e	48 8b 44 24 30	 mov	 rax, QWORD PTR $T3[rsp]
  00023	48 89 44 24 48	 mov	 QWORD PTR _Scary$[rsp], rax

; 1955 :         auto&& _Alproxy   = _GET_PROXY_ALLOCATOR(_Alnode, _Getal());

  00028	48 8d 44 24 20	 lea	 rax, QWORD PTR $T1[rsp]
  0002d	48 8b f8	 mov	 rdi, rax
  00030	33 c0		 xor	 eax, eax
  00032	b9 01 00 00 00	 mov	 ecx, 1
  00037	f3 aa		 rep stosb
  00039	48 8d 44 24 21	 lea	 rax, QWORD PTR $S140$[rsp]
  0003e	48 89 44 24 50	 mov	 QWORD PTR _Alproxy$[rsp], rax

; 1975 :         return _Mypair._Myval2._Get_first();

  00043	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00048	48 89 44 24 38	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1975 :         return _Mypair._Myval2._Get_first();

  0004d	48 8b 44 24 38	 mov	 rax, QWORD PTR $T4[rsp]
  00052	48 89 44 24 40	 mov	 QWORD PTR $T5[rsp], rax

; 1956 :         _Container_proxy_ptr<_Alnode> _Proxy(_Alproxy, *_Scary);
; 1957 :         _Scary->_Myhead = _Node::_Buyheadnode(_Getal());

  00057	48 8b 44 24 40	 mov	 rax, QWORD PTR $T5[rsp]
  0005c	48 8b c8	 mov	 rcx, rax
  0005f	e8 00 00 00 00	 call	 ??$_Buyheadnode@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@@Z ; std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *>::_Buyheadnode<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > >
  00064	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _Scary$[rsp]
  00069	48 89 01	 mov	 QWORD PTR [rcx], rax

; 1958 :         _Proxy._Release();
; 1959 :     }

  0006c	48 83 c4 60	 add	 rsp, 96			; 00000060H
  00070	5f		 pop	 rdi
  00071	c3		 ret	 0
?_Alloc_sentinel_and_proxy@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEAAXXZ ENDP ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::_Alloc_sentinel_and_proxy
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ?_Check_grow_by_1@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEAAXXZ
_TEXT	SEGMENT
$T1 = 32
$T2 = 40
this$ = 64
?_Check_grow_by_1@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEAAXXZ PROC ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::_Check_grow_by_1, COMDAT

; 1647 :     void _Check_grow_by_1() {

$LN46:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0000e	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00013	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00018	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax

; 1648 :         if (max_size() == _Get_scary()->_Mysize) {

  0001d	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00022	e8 00 00 00 00	 call	 ?max_size@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@QEBA_KXZ ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::max_size
  00027	48 8b 4c 24 28	 mov	 rcx, QWORD PTR $T2[rsp]
  0002c	48 3b 41 08	 cmp	 rax, QWORD PTR [rcx+8]
  00030	75 06		 jne	 SHORT $LN3@Check_grow

; 1649 :             _Throw_tree_length_error();

  00032	e8 00 00 00 00	 call	 ?_Throw_tree_length_error@std@@YAXXZ ; std::_Throw_tree_length_error
  00037	90		 npad	 1
$LN3@Check_grow:

; 1650 :         }
; 1651 :     }

  00038	48 83 c4 38	 add	 rsp, 56			; 00000038H
  0003c	c3		 ret	 0
?_Check_grow_by_1@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@IEAAXXZ ENDP ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::_Check_grow_by_1
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ?erase@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@QEAA?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@@2@V?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@@2@@Z
_TEXT	SEGMENT
$T1 = 32
$T2 = 40
_Pnode$ = 48
$T3 = 56
$T4 = 64
__param0$ = 72
_Scary$ = 80
$T5 = 88
this$ = 112
__$ReturnUdt$ = 120
_Where$ = 128
?erase@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@QEAA?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@@2@V?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@@2@@Z PROC ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::erase, COMDAT

; 1344 :     iterator erase(const_iterator _Where) noexcept /* strengthened */ {

$LN147:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 68	 sub	 rsp, 104		; 00000068H

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00013	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00018	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  0001d	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00022	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax

; 1345 :         const auto _Scary = _Get_scary();

  00027	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
  0002c	48 89 44 24 50	 mov	 QWORD PTR _Scary$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1193 :         return nullptr;

  00031	48 c7 44 24 58
	00 00 00 00	 mov	 QWORD PTR $T5[rsp], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 250  :         return _Tree_unchecked_const_iterator<_Mytree>(this->_Ptr, static_cast<const _Mytree*>(this->_Getcont()));

  0003a	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _Where$[rsp]
  00042	48 89 44 24 30	 mov	 QWORD PTR _Pnode$[rsp], rax

; 37   :     _Tree_unchecked_const_iterator(_Nodeptr _Pnode, const _Mytree* _Plist) noexcept : _Ptr(_Pnode) {

  00047	48 8b 44 24 30	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0004c	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax

; 250  :         return _Tree_unchecked_const_iterator<_Mytree>(this->_Ptr, static_cast<const _Mytree*>(this->_Getcont()));

  00051	48 8d 44 24 38	 lea	 rax, QWORD PTR $T3[rsp]
  00056	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax

; 1346 : #if _ITERATOR_DEBUG_LEVEL == 2
; 1347 :         _STL_VERIFY(_Where._Getcont() == _Scary, "map/set erase iterator from incorrect container");
; 1348 :         _STL_VERIFY(!_Where._Ptr->_Isnil, "cannot erase map/set end() iterator");
; 1349 : #endif // _ITERATOR_DEBUG_LEVEL == 2
; 1350 :         return iterator(_Erase_unchecked(_Where._Unwrapped()), _Scary);

  0005b	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  00060	48 8b 10	 mov	 rdx, QWORD PTR [rax]
  00063	48 8b 4c 24 70	 mov	 rcx, QWORD PTR this$[rsp]
  00068	e8 00 00 00 00	 call	 ?_Erase_unchecked@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@AEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::_Erase_unchecked
  0006d	48 89 44 24 48	 mov	 QWORD PTR __param0$[rsp], rax

; 37   :     _Tree_unchecked_const_iterator(_Nodeptr _Pnode, const _Mytree* _Plist) noexcept : _Ptr(_Pnode) {

  00072	48 8b 44 24 78	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]
  00077	48 8b 4c 24 48	 mov	 rcx, QWORD PTR __param0$[rsp]
  0007c	48 89 08	 mov	 QWORD PTR [rax], rcx

; 1346 : #if _ITERATOR_DEBUG_LEVEL == 2
; 1347 :         _STL_VERIFY(_Where._Getcont() == _Scary, "map/set erase iterator from incorrect container");
; 1348 :         _STL_VERIFY(!_Where._Ptr->_Isnil, "cannot erase map/set end() iterator");
; 1349 : #endif // _ITERATOR_DEBUG_LEVEL == 2
; 1350 :         return iterator(_Erase_unchecked(_Where._Unwrapped()), _Scary);

  0007f	48 8b 44 24 78	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]

; 1351 :     }

  00084	48 83 c4 68	 add	 rsp, 104		; 00000068H
  00088	c3		 ret	 0
?erase@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@QEAA?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@@2@V?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@@2@@Z ENDP ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::erase
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ?_Erase_unchecked@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@AEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z
_TEXT	SEGMENT
_Bytes$ = 32
_Erasednode$ = 40
_Ptr$ = 48
_Successor$ = 56
$T1 = 64
$T2 = 72
_Scary$ = 80
$T3 = 88
$T4 = 96
$T5 = 104
this$ = 128
_Where$ = 136
?_Erase_unchecked@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@AEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z PROC ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::_Erase_unchecked, COMDAT

; 1299 :     _Nodeptr _Erase_unchecked(_Unchecked_const_iterator _Where) noexcept {

$LN174:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 78	 sub	 rsp, 120		; 00000078H

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  0000e	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00016	48 89 44 24 40	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  0001b	48 8b 44 24 40	 mov	 rax, QWORD PTR $T1[rsp]
  00020	48 89 44 24 48	 mov	 QWORD PTR $T2[rsp], rax

; 1300 :         const auto _Scary                    = _Get_scary();

  00025	48 8b 44 24 48	 mov	 rax, QWORD PTR $T2[rsp]
  0002a	48 89 44 24 50	 mov	 QWORD PTR _Scary$[rsp], rax

; 1301 :         _Unchecked_const_iterator _Successor = _Where;

  0002f	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR _Where$[rsp]
  00037	48 89 44 24 38	 mov	 QWORD PTR _Successor$[rsp], rax

; 1302 :         ++_Successor; // save successor iterator for return

  0003c	48 8d 4c 24 38	 lea	 rcx, QWORD PTR _Successor$[rsp]
  00041	e8 00 00 00 00	 call	 ??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ ; std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >,std::_Iterator_base0>::operator++

; 1303 :         _Nodeptr _Erasednode = _Scary->_Extract(_Where); // node to erase

  00046	48 8b 94 24 88
	00 00 00	 mov	 rdx, QWORD PTR _Where$[rsp]
  0004e	48 8b 4c 24 50	 mov	 rcx, QWORD PTR _Scary$[rsp]
  00053	e8 00 00 00 00	 call	 ?_Extract@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >::_Extract
  00058	48 89 44 24 28	 mov	 QWORD PTR _Erasednode$[rsp], rax

; 1975 :         return _Mypair._Myval2._Get_first();

  0005d	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00065	48 89 44 24 58	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1975 :         return _Mypair._Myval2._Get_first();

  0006a	48 8b 44 24 58	 mov	 rax, QWORD PTR $T3[rsp]
  0006f	48 89 44 24 60	 mov	 QWORD PTR $T4[rsp], rax

; 381  :         allocator_traits<_Alloc>::destroy(_Al, _STD addressof(_Ptr->_Myval));

  00074	48 8b 44 24 28	 mov	 rax, QWORD PTR _Erasednode$[rsp]
  00079	48 83 c0 20	 add	 rax, 32			; 00000020H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0007d	48 89 44 24 68	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 723  :             _STD _Deallocate<_New_alignof<value_type>>(_Ptr, sizeof(value_type) * _Count);

  00082	b8 01 00 00 00	 mov	 eax, 1
  00087	48 6b c0 30	 imul	 rax, rax, 48		; 00000030H
  0008b	48 89 44 24 20	 mov	 QWORD PTR _Bytes$[rsp], rax
  00090	48 8b 44 24 28	 mov	 rax, QWORD PTR _Erasednode$[rsp]
  00095	48 89 44 24 30	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  0009a	48 81 7c 24 20
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  000a3	72 10		 jb	 SHORT $LN165@Erase_unch

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  000a5	48 8d 54 24 20	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  000aa	48 8d 4c 24 30	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  000af	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  000b4	90		 npad	 1
$LN165@Erase_unch:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  000b5	48 8b 54 24 20	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  000ba	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  000bf	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  000c4	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1306 :         return _Successor._Ptr; // return successor nodeptr

  000c5	48 8b 44 24 38	 mov	 rax, QWORD PTR _Successor$[rsp]

; 1307 :     }

  000ca	48 83 c4 78	 add	 rsp, 120		; 00000078H
  000ce	c3		 ret	 0
?_Erase_unchecked@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@AEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z ENDP ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::_Erase_unchecked
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ?max_size@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@QEBA_KXZ
_TEXT	SEGMENT
$T1 = 0
$T2 = 8
tv67 = 16
$T3 = 24
$T4 = 32
$T5 = 40
$T6 = 48
$T7 = 56
$T8 = 64
_Unsigned_max$9 = 72
this$ = 96
?max_size@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@QEBA_KXZ PROC ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::max_size, COMDAT

; 1212 :     _NODISCARD size_type max_size() const noexcept {

$LN31:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 1979 :         return _Mypair._Myval2._Get_first();

  00009	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1543 :         return *this;

  0000e	48 89 44 24 18	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1979 :         return _Mypair._Myval2._Get_first();

  00013	48 8b 44 24 18	 mov	 rax, QWORD PTR $T3[rsp]
  00018	48 89 44 24 40	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 746  :         return static_cast<size_t>(-1) / sizeof(value_type);

  0001d	48 b8 55 55 55
	55 55 55 55 05	 mov	 rax, 384307168202282325	; 0555555555555555H
  00027	48 89 44 24 20	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1213 :         return (_STD min)(

  0002c	48 8b 44 24 20	 mov	 rax, QWORD PTR $T4[rsp]
  00031	48 89 04 24	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 866  :         constexpr auto _Unsigned_max = static_cast<make_unsigned_t<_Ty>>(-1);

  00035	48 c7 44 24 48
	ff ff ff ff	 mov	 QWORD PTR _Unsigned_max$9[rsp], -1

; 867  :         return static_cast<_Ty>(_Unsigned_max >> 1);

  0003e	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  00048	48 89 44 24 28	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1213 :         return (_STD min)(

  0004d	48 8b 44 24 28	 mov	 rax, QWORD PTR $T5[rsp]
  00052	48 89 44 24 08	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 101  :     return _Right < _Left ? _Right : _Left;

  00057	48 8b 44 24 08	 mov	 rax, QWORD PTR $T2[rsp]
  0005c	48 39 04 24	 cmp	 QWORD PTR $T1[rsp], rax
  00060	73 0b		 jae	 SHORT $LN26@max_size
  00062	48 8d 04 24	 lea	 rax, QWORD PTR $T1[rsp]
  00066	48 89 44 24 10	 mov	 QWORD PTR tv67[rsp], rax
  0006b	eb 0a		 jmp	 SHORT $LN27@max_size
$LN26@max_size:
  0006d	48 8d 44 24 08	 lea	 rax, QWORD PTR $T2[rsp]
  00072	48 89 44 24 10	 mov	 QWORD PTR tv67[rsp], rax
$LN27@max_size:
  00077	48 8b 44 24 10	 mov	 rax, QWORD PTR tv67[rsp]
  0007c	48 89 44 24 30	 mov	 QWORD PTR $T6[rsp], rax
  00081	48 8b 44 24 30	 mov	 rax, QWORD PTR $T6[rsp]
  00086	48 89 44 24 38	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1213 :         return (_STD min)(

  0008b	48 8b 44 24 38	 mov	 rax, QWORD PTR $T7[rsp]
  00090	48 8b 00	 mov	 rax, QWORD PTR [rax]

; 1214 :             static_cast<size_type>(_STD _Max_limit<difference_type>()), _Alnode_traits::max_size(_Getal()));
; 1215 :     }

  00093	48 83 c4 58	 add	 rsp, 88			; 00000058H
  00097	c3		 ret	 0
?max_size@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@QEBA_KXZ ENDP ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::max_size
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??1?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@QEAA@XZ
_TEXT	SEGMENT
$T1 = 32
$T2 = 40
$T3 = 48
$T4 = 56
_Scary$ = 64
this$ = 96
??1?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@QEAA@XZ PROC ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::~_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >, COMDAT

; 1095 :     ~_Tree() noexcept {

$LN128:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00009	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0000e	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00013	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00018	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax

; 1096 :         const auto _Scary = _Get_scary();

  0001d	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
  00022	48 89 44 24 40	 mov	 QWORD PTR _Scary$[rsp], rax

; 1975 :         return _Mypair._Myval2._Get_first();

  00027	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  0002c	48 89 44 24 30	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1975 :         return _Mypair._Myval2._Get_first();

  00031	48 8b 44 24 30	 mov	 rax, QWORD PTR $T3[rsp]
  00036	48 89 44 24 38	 mov	 QWORD PTR $T4[rsp], rax

; 1097 :         _Scary->_Erase_head(_Getal());

  0003b	48 8b 44 24 38	 mov	 rax, QWORD PTR $T4[rsp]
  00040	48 8b d0	 mov	 rdx, rax
  00043	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Scary$[rsp]
  00048	e8 00 00 00 00	 call	 ??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@1@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >::_Erase_head<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> > >
  0004d	90		 npad	 1

; 1098 : #if _ITERATOR_DEBUG_LEVEL != 0 // TRANSITION, ABI
; 1099 :         auto&& _Alproxy = _GET_PROXY_ALLOCATOR(_Alnode, _Getal());
; 1100 :         _Delete_plain_internal(_Alproxy, _Scary->_Myproxy);
; 1101 : #endif // _ITERATOR_DEBUG_LEVEL != 0
; 1102 :     }

  0004e	48 83 c4 58	 add	 rsp, 88			; 00000058H
  00052	c3		 ret	 0
??1?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@QEAA@XZ ENDP ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::~_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?allocate@?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@_K@Z
_TEXT	SEGMENT
_Overflow_is_possible$1 = 32
_Bytes$ = 40
$T2 = 48
$T3 = 56
$T4 = 64
_Max_possible$5 = 72
this$ = 96
_Count$ = 104
?allocate@?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@_K@Z PROC ; std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> >::allocate, COMDAT

; 988  :     _NODISCARD_RAW_PTR_ALLOC _CONSTEXPR20 __declspec(allocator) _Ty* allocate(_CRT_GUARDOVERFLOW const size_t _Count) {

$LN13:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 113  :     constexpr bool _Overflow_is_possible = _Ty_size > 1;

  0000e	c6 44 24 20 01	 mov	 BYTE PTR _Overflow_is_possible$1[rsp], 1

; 114  : 
; 115  :     if constexpr (_Overflow_is_possible) {
; 116  :         constexpr size_t _Max_possible = static_cast<size_t>(-1) / _Ty_size;

  00013	48 b8 55 55 55
	55 55 55 55 05	 mov	 rax, 384307168202282325	; 0555555555555555H
  0001d	48 89 44 24 48	 mov	 QWORD PTR _Max_possible$5[rsp], rax

; 117  :         if (_Count > _Max_possible) {

  00022	48 b8 55 55 55
	55 55 55 55 05	 mov	 rax, 384307168202282325	; 0555555555555555H
  0002c	48 39 44 24 68	 cmp	 QWORD PTR _Count$[rsp], rax
  00031	76 06		 jbe	 SHORT $LN4@allocate

; 118  :             _Throw_bad_array_new_length(); // multiply overflow

  00033	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00038	90		 npad	 1
$LN4@allocate:

; 119  :         }
; 120  :     }
; 121  : 
; 122  :     return _Count * _Ty_size;

  00039	48 6b 44 24 68
	30		 imul	 rax, QWORD PTR _Count$[rsp], 48 ; 00000030H
  0003f	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00044	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
  00049	48 89 44 24 28	 mov	 QWORD PTR _Bytes$[rsp], rax

; 227  :     if (_Bytes == 0) {

  0004e	48 83 7c 24 28
	00		 cmp	 QWORD PTR _Bytes$[rsp], 0
  00054	75 0b		 jne	 SHORT $LN8@allocate

; 228  :         return nullptr;

  00056	48 c7 44 24 30
	00 00 00 00	 mov	 QWORD PTR $T2[rsp], 0
  0005f	eb 35		 jmp	 SHORT $LN7@allocate
$LN8@allocate:

; 229  :     }
; 230  : 
; 231  : #if _HAS_CXX20 // TRANSITION, GH-1532
; 232  :     if (_STD is_constant_evaluated()) {
; 233  :         return _Traits::_Allocate(_Bytes);
; 234  :     }
; 235  : #endif // _HAS_CXX20
; 236  : 
; 237  : #ifdef __cpp_aligned_new
; 238  :     if constexpr (_Align > __STDCPP_DEFAULT_NEW_ALIGNMENT__) {
; 239  :         size_t _Passed_align = _Align;
; 240  : #if defined(_M_IX86) || defined(_M_X64)
; 241  :         if (_Bytes >= _Big_allocation_threshold) {
; 242  :             // boost the alignment of big allocations to help autovectorization
; 243  :             _Passed_align = (_STD max)(_Align, _Big_allocation_alignment);
; 244  :         }
; 245  : #endif // defined(_M_IX86) || defined(_M_X64)
; 246  :         return _Traits::_Allocate_aligned(_Bytes, _Passed_align);
; 247  :     } else
; 248  : #endif // defined(__cpp_aligned_new)
; 249  :     {
; 250  : #if defined(_M_IX86) || defined(_M_X64)
; 251  :         if (_Bytes >= _Big_allocation_threshold) {

  00061	48 81 7c 24 28
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  0006a	72 11		 jb	 SHORT $LN9@allocate

; 252  :             // boost the alignment of big allocations to help autovectorization
; 253  :             return _Allocate_manually_vector_aligned<_Traits>(_Bytes);

  0006c	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00071	e8 00 00 00 00	 call	 ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
  00076	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
  0007b	eb 19		 jmp	 SHORT $LN7@allocate
$LN9@allocate:

; 136  :         return ::operator new(_Bytes);

  0007d	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00082	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00087	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax

; 256  :         return _Traits::_Allocate(_Bytes);

  0008c	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  00091	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
$LN7@allocate:

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00096	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
$LN6@allocate:

; 991  :     }

  0009b	48 83 c4 58	 add	 rsp, 88			; 00000058H
  0009f	c3		 ret	 0
?allocate@?$allocator@U?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@2@_K@Z ENDP ; std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::EntityParty *>,void *> >::allocate
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp
;	COMDAT ?remove_i@ManagerPartyEntity@mu2@@AEAA_NI@Z
_TEXT	SEGMENT
$T1 = 32
$T2 = 33
tv213 = 36
tv174 = 40
$T3 = 48
it$ = 56
this$ = 64
$T4 = 72
__param0$ = 80
$T5 = 88
$T6 = 96
$T7 = 104
_Scary$8 = 112
__param0$ = 120
$T9 = 128
$T10 = 136
$T11 = 144
$T12 = 152
$T13 = 160
$T14 = 168
tv140 = 176
$T15 = 184
$T16 = 192
this$ = 224
id$ = 232
?remove_i@ManagerPartyEntity@mu2@@AEAA_NI@Z PROC	; mu2::ManagerPartyEntity::remove_i, COMDAT

; 58   : {	

$LN291:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 81 ec d8 00
	00 00		 sub	 rsp, 216		; 000000d8H

; 59   : 	MapPartyEntity::const_iterator  it = m_mapParty.find(MapPartyEntity::key_type(id));

  00010	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00018	48 83 c0 08	 add	 rax, 8
  0001c	48 89 44 24 40	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00021	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00026	48 89 44 24 48	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  0002b	48 8b 44 24 48	 mov	 rax, QWORD PTR $T4[rsp]
  00030	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T15[rsp], rax

; 1394 :         return iterator(_Find(_Keyval), _Get_scary());

  00038	48 8d 94 24 e8
	00 00 00	 lea	 rdx, QWORD PTR id$[rsp]
  00040	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00045	e8 00 00 00 00	 call	 ??$_Find@I@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@AEBAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@AEBI@Z ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::_Find<unsigned int>
  0004a	48 89 44 24 50	 mov	 QWORD PTR __param0$[rsp], rax

; 37   :     _Tree_unchecked_const_iterator(_Nodeptr _Pnode, const _Mytree* _Plist) noexcept : _Ptr(_Pnode) {

  0004f	48 8b 44 24 50	 mov	 rax, QWORD PTR __param0$[rsp]
  00054	48 89 44 24 58	 mov	 QWORD PTR $T5[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp

; 59   : 	MapPartyEntity::const_iterator  it = m_mapParty.find(MapPartyEntity::key_type(id));

  00059	48 8b 44 24 58	 mov	 rax, QWORD PTR $T5[rsp]
  0005e	48 89 44 24 38	 mov	 QWORD PTR it$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00063	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0006b	48 83 c0 08	 add	 rax, 8
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0006f	48 89 44 24 60	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00074	48 8b 44 24 60	 mov	 rax, QWORD PTR $T6[rsp]
  00079	48 89 44 24 68	 mov	 QWORD PTR $T7[rsp], rax

; 1151 :         const auto _Scary = _Get_scary();

  0007e	48 8b 44 24 68	 mov	 rax, QWORD PTR $T7[rsp]
  00083	48 89 44 24 70	 mov	 QWORD PTR _Scary$8[rsp], rax

; 1152 :         return iterator(_Scary->_Myhead, _Scary);

  00088	48 8b 44 24 70	 mov	 rax, QWORD PTR _Scary$8[rsp]
  0008d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00090	48 89 44 24 78	 mov	 QWORD PTR __param0$[rsp], rax

; 37   :     _Tree_unchecked_const_iterator(_Nodeptr _Pnode, const _Mytree* _Plist) noexcept : _Ptr(_Pnode) {

  00095	48 8b 44 24 78	 mov	 rax, QWORD PTR __param0$[rsp]
  0009a	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax

; 1152 :         return iterator(_Scary->_Myhead, _Scary);

  000a2	48 8d 84 24 80
	00 00 00	 lea	 rax, QWORD PTR $T9[rsp]
  000aa	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax

; 232  :         return this->_Ptr == _Right._Ptr;

  000b2	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  000ba	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000bd	48 39 44 24 38	 cmp	 QWORD PTR it$[rsp], rax
  000c2	75 0a		 jne	 SHORT $LN118@remove_i
  000c4	c7 44 24 24 01
	00 00 00	 mov	 DWORD PTR tv213[rsp], 1
  000cc	eb 08		 jmp	 SHORT $LN119@remove_i
$LN118@remove_i:
  000ce	c7 44 24 24 00
	00 00 00	 mov	 DWORD PTR tv213[rsp], 0
$LN119@remove_i:
  000d6	0f b6 44 24 24	 movzx	 eax, BYTE PTR tv213[rsp]
  000db	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al

; 237  :         return !(*this == _Right);

  000df	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  000e4	0f b6 c0	 movzx	 eax, al
  000e7	85 c0		 test	 eax, eax
  000e9	75 0a		 jne	 SHORT $LN111@remove_i
  000eb	c7 44 24 28 01
	00 00 00	 mov	 DWORD PTR tv174[rsp], 1
  000f3	eb 08		 jmp	 SHORT $LN112@remove_i
$LN111@remove_i:
  000f5	c7 44 24 28 00
	00 00 00	 mov	 DWORD PTR tv174[rsp], 0
$LN112@remove_i:
  000fd	0f b6 44 24 28	 movzx	 eax, BYTE PTR tv174[rsp]
  00102	88 44 24 21	 mov	 BYTE PTR $T2[rsp], al
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp

; 60   : 	VALID_RETURN( it != m_mapParty.end(), false);

  00106	0f b6 44 24 21	 movzx	 eax, BYTE PTR $T2[rsp]
  0010b	0f b6 c0	 movzx	 eax, al
  0010e	85 c0		 test	 eax, eax
  00110	75 07		 jne	 SHORT $LN2@remove_i
  00112	32 c0		 xor	 al, al
  00114	e9 a8 00 00 00	 jmp	 $LN1@remove_i
$LN2@remove_i:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 185  :         return this->_Ptr->_Myval;

  00119	48 8b 44 24 38	 mov	 rax, QWORD PTR it$[rsp]
  0011e	48 83 c0 20	 add	 rax, 32			; 00000020H
  00122	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax

; 189  :         return pointer_traits<pointer>::pointer_to(**this);

  0012a	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00132	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 528  :         return _STD addressof(_Val);

  0013a	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
  00142	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 189  :         return pointer_traits<pointer>::pointer_to(**this);

  0014a	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR $T13[rsp]
  00152	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T14[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp

; 62   : 	delete it->second;

  0015a	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T14[rsp]
  00162	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00166	48 89 44 24 30	 mov	 QWORD PTR $T3[rsp], rax
  0016b	48 83 7c 24 30
	00		 cmp	 QWORD PTR $T3[rsp], 0
  00171	74 1e		 je	 SHORT $LN4@remove_i
  00173	48 8b 44 24 30	 mov	 rax, QWORD PTR $T3[rsp]
  00178	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0017b	ba 01 00 00 00	 mov	 edx, 1
  00180	48 8b 4c 24 30	 mov	 rcx, QWORD PTR $T3[rsp]
  00185	ff 10		 call	 QWORD PTR [rax]
  00187	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR tv140[rsp], rax
  0018f	eb 0c		 jmp	 SHORT $LN5@remove_i
$LN4@remove_i:
  00191	48 c7 84 24 b0
	00 00 00 00 00
	00 00		 mov	 QWORD PTR tv140[rsp], 0
$LN5@remove_i:

; 63   : 	m_mapParty.erase(it);

  0019d	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  001a5	48 83 c0 08	 add	 rax, 8
  001a9	4c 8b 44 24 38	 mov	 r8, QWORD PTR it$[rsp]
  001ae	48 8d 94 24 c0
	00 00 00	 lea	 rdx, QWORD PTR $T16[rsp]
  001b6	48 8b c8	 mov	 rcx, rax
  001b9	e8 00 00 00 00	 call	 ?erase@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@QEAA?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@@2@V?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@@2@@Z ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::erase
  001be	90		 npad	 1

; 64   : 	return true;

  001bf	b0 01		 mov	 al, 1
$LN1@remove_i:

; 65   : }

  001c1	48 81 c4 d8 00
	00 00		 add	 rsp, 216		; 000000d8H
  001c8	c3		 ret	 0
?remove_i@ManagerPartyEntity@mu2@@AEAA_NI@Z ENDP	; mu2::ManagerPartyEntity::remove_i
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp
;	COMDAT ?GetEntity@ManagerPartyEntity@mu2@@QEAAPEAVEntityParty@2@I@Z
_TEXT	SEGMENT
$T1 = 32
$T2 = 33
tv194 = 36
tv155 = 40
this$ = 48
it$ = 56
$T3 = 64
__param0$ = 72
$T4 = 80
$T5 = 88
$T6 = 96
_Scary$7 = 104
__param0$ = 112
$T8 = 120
$T9 = 128
$T10 = 136
$T11 = 144
$T12 = 152
$T13 = 160
$T14 = 168
this$ = 192
id$ = 200
?GetEntity@ManagerPartyEntity@mu2@@QEAAPEAVEntityParty@2@I@Z PROC ; mu2::ManagerPartyEntity::GetEntity, COMDAT

; 50   : {

$LN141:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 81 ec b8 00
	00 00		 sub	 rsp, 184		; 000000b8H

; 51   : 	MapPartyEntity::const_iterator  it = m_mapParty.find(MapPartyEntity::key_type(id));

  00010	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00018	48 83 c0 08	 add	 rax, 8
  0001c	48 89 44 24 30	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00021	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00026	48 89 44 24 40	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  0002b	48 8b 44 24 40	 mov	 rax, QWORD PTR $T3[rsp]
  00030	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T14[rsp], rax

; 1394 :         return iterator(_Find(_Keyval), _Get_scary());

  00038	48 8d 94 24 c8
	00 00 00	 lea	 rdx, QWORD PTR id$[rsp]
  00040	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00045	e8 00 00 00 00	 call	 ??$_Find@I@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@AEBAPEAU?$_Tree_node@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@PEAX@1@AEBI@Z ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::_Find<unsigned int>
  0004a	48 89 44 24 48	 mov	 QWORD PTR __param0$[rsp], rax

; 37   :     _Tree_unchecked_const_iterator(_Nodeptr _Pnode, const _Mytree* _Plist) noexcept : _Ptr(_Pnode) {

  0004f	48 8b 44 24 48	 mov	 rax, QWORD PTR __param0$[rsp]
  00054	48 89 44 24 50	 mov	 QWORD PTR $T4[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp

; 51   : 	MapPartyEntity::const_iterator  it = m_mapParty.find(MapPartyEntity::key_type(id));

  00059	48 8b 44 24 50	 mov	 rax, QWORD PTR $T4[rsp]
  0005e	48 89 44 24 38	 mov	 QWORD PTR it$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00063	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0006b	48 83 c0 08	 add	 rax, 8
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0006f	48 89 44 24 58	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00074	48 8b 44 24 58	 mov	 rax, QWORD PTR $T5[rsp]
  00079	48 89 44 24 60	 mov	 QWORD PTR $T6[rsp], rax

; 1151 :         const auto _Scary = _Get_scary();

  0007e	48 8b 44 24 60	 mov	 rax, QWORD PTR $T6[rsp]
  00083	48 89 44 24 68	 mov	 QWORD PTR _Scary$7[rsp], rax

; 1152 :         return iterator(_Scary->_Myhead, _Scary);

  00088	48 8b 44 24 68	 mov	 rax, QWORD PTR _Scary$7[rsp]
  0008d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00090	48 89 44 24 70	 mov	 QWORD PTR __param0$[rsp], rax

; 37   :     _Tree_unchecked_const_iterator(_Nodeptr _Pnode, const _Mytree* _Plist) noexcept : _Ptr(_Pnode) {

  00095	48 8b 44 24 70	 mov	 rax, QWORD PTR __param0$[rsp]
  0009a	48 89 44 24 78	 mov	 QWORD PTR $T8[rsp], rax

; 1152 :         return iterator(_Scary->_Myhead, _Scary);

  0009f	48 8d 44 24 78	 lea	 rax, QWORD PTR $T8[rsp]
  000a4	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax

; 232  :         return this->_Ptr == _Right._Ptr;

  000ac	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR $T9[rsp]
  000b4	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000b7	48 39 44 24 38	 cmp	 QWORD PTR it$[rsp], rax
  000bc	75 0a		 jne	 SHORT $LN116@GetEntity
  000be	c7 44 24 24 01
	00 00 00	 mov	 DWORD PTR tv194[rsp], 1
  000c6	eb 08		 jmp	 SHORT $LN117@GetEntity
$LN116@GetEntity:
  000c8	c7 44 24 24 00
	00 00 00	 mov	 DWORD PTR tv194[rsp], 0
$LN117@GetEntity:
  000d0	0f b6 44 24 24	 movzx	 eax, BYTE PTR tv194[rsp]
  000d5	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al

; 237  :         return !(*this == _Right);

  000d9	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  000de	0f b6 c0	 movzx	 eax, al
  000e1	85 c0		 test	 eax, eax
  000e3	75 0a		 jne	 SHORT $LN109@GetEntity
  000e5	c7 44 24 28 01
	00 00 00	 mov	 DWORD PTR tv155[rsp], 1
  000ed	eb 08		 jmp	 SHORT $LN110@GetEntity
$LN109@GetEntity:
  000ef	c7 44 24 28 00
	00 00 00	 mov	 DWORD PTR tv155[rsp], 0
$LN110@GetEntity:
  000f7	0f b6 44 24 28	 movzx	 eax, BYTE PTR tv155[rsp]
  000fc	88 44 24 21	 mov	 BYTE PTR $T2[rsp], al
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp

; 52   : 	VALID_RETURN( it != m_mapParty.end(), nullptr );

  00100	0f b6 44 24 21	 movzx	 eax, BYTE PTR $T2[rsp]
  00105	0f b6 c0	 movzx	 eax, al
  00108	85 c0		 test	 eax, eax
  0010a	75 04		 jne	 SHORT $LN2@GetEntity
  0010c	33 c0		 xor	 eax, eax
  0010e	eb 4d		 jmp	 SHORT $LN1@GetEntity
$LN2@GetEntity:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 185  :         return this->_Ptr->_Myval;

  00110	48 8b 44 24 38	 mov	 rax, QWORD PTR it$[rsp]
  00115	48 83 c0 20	 add	 rax, 32			; 00000020H
  00119	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax

; 189  :         return pointer_traits<pointer>::pointer_to(**this);

  00121	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00129	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 528  :         return _STD addressof(_Val);

  00131	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  00139	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 189  :         return pointer_traits<pointer>::pointer_to(**this);

  00141	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
  00149	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp

; 54   : 	return it->second;

  00151	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR $T13[rsp]
  00159	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
$LN1@GetEntity:

; 55   : }

  0015d	48 81 c4 b8 00
	00 00		 add	 rsp, 184		; 000000b8H
  00164	c3		 ret	 0
?GetEntity@ManagerPartyEntity@mu2@@QEAAPEAVEntityParty@2@I@Z ENDP ; mu2::ManagerPartyEntity::GetEntity
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp
;	COMDAT ?Remove@ManagerPartyEntity@mu2@@QEAAXI@Z
_TEXT	SEGMENT
hConsole$1 = 96
this$ = 128
id$ = 136
?Remove@ManagerPartyEntity@mu2@@QEAAXI@Z PROC		; mu2::ManagerPartyEntity::Remove, COMDAT

; 45   : {

$LN4:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 78	 sub	 rsp, 120		; 00000078H

; 46   : 	VERIFY_RETURN( remove_i( id ), );

  0000d	8b 94 24 88 00
	00 00		 mov	 edx, DWORD PTR id$[rsp]
  00014	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0001c	e8 00 00 00 00	 call	 ?remove_i@ManagerPartyEntity@mu2@@AEAA_NI@Z ; mu2::ManagerPartyEntity::remove_i
  00021	0f b6 c0	 movzx	 eax, al
  00024	85 c0		 test	 eax, eax
  00026	0f 85 99 00 00
	00		 jne	 $LN2@Remove
  0002c	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  00031	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  00037	48 89 44 24 60	 mov	 QWORD PTR hConsole$1[rsp], rax
  0003c	66 ba 0d 00	 mov	 dx, 13
  00040	48 8b 4c 24 60	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  00045	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  0004b	c7 44 24 50 2e
	00 00 00	 mov	 DWORD PTR [rsp+80], 46	; 0000002eH
  00053	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FB@EJDJLCL@F?3?2Release_Branch?2Server?2Develo@
  0005a	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  0005f	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0P@HJBEIDCF@remove_i?$CI?5id?5?$CJ@
  00066	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  0006b	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CA@FANFAMJ@mu2?3?3ManagerPartyEntity?3?3Remove@
  00072	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  00077	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  0007e	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  00083	c7 44 24 28 2e
	00 00 00	 mov	 DWORD PTR [rsp+40], 46	; 0000002eH
  0008b	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FB@EJDJLCL@F?3?2Release_Branch?2Server?2Develo@
  00092	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  00097	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CA@FANFAMJ@mu2?3?3ManagerPartyEntity?3?3Remove@
  0009e	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  000a4	ba 02 00 00 00	 mov	 edx, 2
  000a9	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  000b0	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  000b5	66 ba 07 00	 mov	 dx, 7
  000b9	48 8b 4c 24 60	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  000be	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000c4	90		 npad	 1
$LN2@Remove:

; 47   : }

  000c5	48 83 c4 78	 add	 rsp, 120		; 00000078H
  000c9	c3		 ret	 0
?Remove@ManagerPartyEntity@mu2@@QEAAXI@Z ENDP		; mu2::ManagerPartyEntity::Remove
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp
;	COMDAT ?Insert@ManagerPartyEntity@mu2@@QEAA_NPEAVEntityParty@2@@Z
_TEXT	SEGMENT
$T1 = 32
$T2 = 40
$T3 = 48
$T4 = 56
$T5 = 64
result$ = 80
this$ = 112
entity$ = 120
?Insert@ManagerPartyEntity@mu2@@QEAA_NPEAVEntityParty@2@@Z PROC ; mu2::ManagerPartyEntity::Insert, COMDAT

; 35   : {

$LN217:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 68	 sub	 rsp, 104		; 00000068H

; 36   : 	VALID_RETURN( entity, false );

  0000e	48 83 7c 24 78
	00		 cmp	 QWORD PTR entity$[rsp], 0
  00014	75 07		 jne	 SHORT $LN2@Insert
  00016	32 c0		 xor	 al, al
  00018	e9 8f 00 00 00	 jmp	 $LN1@Insert
$LN2@Insert:

; 38   : 	auto result = m_mapParty.insert(MapPartyEntity::value_type(entity->GetPartyId(), entity));

  0001d	48 8b 4c 24 78	 mov	 rcx, QWORD PTR entity$[rsp]
  00022	e8 00 00 00 00	 call	 ?GetPartyId@EntityParty@mu2@@QEAAIXZ ; mu2::EntityParty::GetPartyId
  00027	89 44 24 20	 mov	 DWORD PTR $T1[rsp], eax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  0002b	48 8d 44 24 20	 lea	 rax, QWORD PTR $T1[rsp]
  00030	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 274  :         : first(_STD forward<_Other1>(_Val1)), second(_STD forward<_Other2>(_Val2)) {

  00035	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
  0003a	8b 00		 mov	 eax, DWORD PTR [rax]
  0003c	89 44 24 40	 mov	 DWORD PTR $T5[rsp], eax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00040	48 8d 44 24 78	 lea	 rax, QWORD PTR entity$[rsp]
  00045	48 89 44 24 30	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 274  :         : first(_STD forward<_Other1>(_Val1)), second(_STD forward<_Other2>(_Val2)) {

  0004a	48 8b 44 24 30	 mov	 rax, QWORD PTR $T3[rsp]
  0004f	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00052	48 89 44 24 48	 mov	 QWORD PTR $T5[rsp+8], rax

; 275  :     }

  00057	48 8d 44 24 40	 lea	 rax, QWORD PTR $T5[rsp]
  0005c	48 89 44 24 38	 mov	 QWORD PTR $T4[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp

; 38   : 	auto result = m_mapParty.insert(MapPartyEntity::value_type(entity->GetPartyId(), entity));

  00061	48 8b 44 24 38	 mov	 rax, QWORD PTR $T4[rsp]
  00066	48 8b 4c 24 70	 mov	 rcx, QWORD PTR this$[rsp]
  0006b	48 83 c1 08	 add	 rcx, 8
  0006f	4c 8b c0	 mov	 r8, rax
  00072	48 8d 54 24 50	 lea	 rdx, QWORD PTR result$[rsp]
  00077	e8 00 00 00 00	 call	 ??$insert@$0A@$0A@@?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@QEAA?AU?$pair@V?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@@std@@_N@1@$$QEAU?$pair@$$CBIPEAVEntityParty@mu2@@@1@@Z ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::insert<0,0>
  0007c	90		 npad	 1

; 39   : 
; 40   : 	MU2_ASSERT( result.second );

  0007d	0f b6 44 24 58	 movzx	 eax, BYTE PTR result$[rsp+8]
  00082	85 c0		 test	 eax, eax
  00084	75 21		 jne	 SHORT $LN3@Insert
  00086	41 b9 28 00 00
	00		 mov	 r9d, 40			; 00000028H
  0008c	4c 8d 05 00 00
	00 00		 lea	 r8, OFFSET FLAT:??_C@_0FB@EJDJLCL@F?3?2Release_Branch?2Server?2Develo@
  00093	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:??_C@_0O@BLOGMFHF@result?4second@
  0009a	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_09OLBBGJEO@Assertion@
  000a1	e8 00 00 00 00	 call	 ?LogRuntimeAssertionFailure@mu2@@YAXPEBD00_K@Z ; mu2::LogRuntimeAssertionFailure
  000a6	90		 npad	 1
$LN3@Insert:

; 41   : 	return result.second;

  000a7	0f b6 44 24 58	 movzx	 eax, BYTE PTR result$[rsp+8]
$LN1@Insert:

; 42   : }

  000ac	48 83 c4 68	 add	 rsp, 104		; 00000068H
  000b0	c3		 ret	 0
?Insert@ManagerPartyEntity@mu2@@QEAA_NPEAVEntityParty@2@@Z ENDP ; mu2::ManagerPartyEntity::Insert
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\SimpleTimer.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\SimpleTimer.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp
; File F:\Release_Branch\Server\Development\Framework\Entity\Entity.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp
;	COMDAT ?OnTick@ManagerPartyEntity@mu2@@QEAAXXZ
_TEXT	SEGMENT
<end>$L0$1 = 32
$T2 = 33
$T3 = 34
__formal$ = 40
tv194 = 48
$T4 = 52
<begin>$L0$5 = 56
party$6 = 64
this$ = 72
<range>$L0$7 = 80
$T8 = 88
$T9 = 96
__param0$ = 104
$T10 = 112
$T11 = 120
iter$12 = 128
this$ = 160
?OnTick@ManagerPartyEntity@mu2@@QEAAXXZ PROC		; mu2::ManagerPartyEntity::OnTick, COMDAT

; 23   : {

$LN88:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi
  00006	48 81 ec 90 00
	00 00		 sub	 rsp, 144		; 00000090H
; File F:\Release_Branch\Server\Development\Framework\Core\SimpleTimer.h

; 33   : 		return static_cast<Tick>(::GetTickCount64() - m_start);

  0000d	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetTickCount64
  00013	48 8b 8c 24 a0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0001b	8b 49 18	 mov	 ecx, DWORD PTR [rcx+24]
  0001e	48 2b c1	 sub	 rax, rcx
  00021	89 44 24 34	 mov	 DWORD PTR $T4[rsp], eax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp

; 24   : 	SIMPLE_TIMER_CHECK( m_updateTimer, EntityParty::PARTY_ON_TICK_INTERVAL  );

  00025	8b 44 24 34	 mov	 eax, DWORD PTR $T4[rsp]
  00029	3d e8 03 00 00	 cmp	 eax, 1000		; 000003e8H
  0002e	73 05		 jae	 SHORT $LN5@OnTick
  00030	e9 32 01 00 00	 jmp	 $LN1@OnTick
$LN5@OnTick:
  00035	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0003d	48 83 c0 18	 add	 rax, 24
  00041	48 89 44 24 48	 mov	 QWORD PTR this$[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\SimpleTimer.h

; 38   : 		m_start = static_cast<Tick>(::GetTickCount64());

  00046	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetTickCount64
  0004c	48 8b 4c 24 48	 mov	 rcx, QWORD PTR this$[rsp]
  00051	89 01		 mov	 DWORD PTR [rcx], eax

; 39   : 		m_isInvoked = false;

  00053	48 8b 44 24 48	 mov	 rax, QWORD PTR this$[rsp]
  00058	c6 40 08 00	 mov	 BYTE PTR [rax+8], 0
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp

; 25   : 	for(auto &iter : m_mapParty)

  0005c	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00064	48 83 c0 08	 add	 rax, 8
  00068	48 89 44 24 50	 mov	 QWORD PTR <range>$L0$7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  0006d	48 8b 44 24 50	 mov	 rax, QWORD PTR <range>$L0$7[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00072	48 89 44 24 58	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00077	48 8b 44 24 58	 mov	 rax, QWORD PTR $T8[rsp]
  0007c	48 89 44 24 60	 mov	 QWORD PTR $T9[rsp], rax

; 1161 :         return _Unchecked_iterator(_Get_scary()->_Myhead->_Left, nullptr);

  00081	48 8b 44 24 60	 mov	 rax, QWORD PTR $T9[rsp]
  00086	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00089	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0008c	48 89 44 24 68	 mov	 QWORD PTR __param0$[rsp], rax

; 37   :     _Tree_unchecked_const_iterator(_Nodeptr _Pnode, const _Mytree* _Plist) noexcept : _Ptr(_Pnode) {

  00091	48 8b 44 24 68	 mov	 rax, QWORD PTR __param0$[rsp]
  00096	48 89 44 24 38	 mov	 QWORD PTR <begin>$L0$5[rsp], rax

; 1169 :         return {};

  0009b	48 8d 44 24 20	 lea	 rax, QWORD PTR <end>$L0$1[rsp]
  000a0	48 8d 44 24 20	 lea	 rax, QWORD PTR <end>$L0$1[rsp]
  000a5	48 8b f8	 mov	 rdi, rax
  000a8	33 c0		 xor	 eax, eax
  000aa	b9 01 00 00 00	 mov	 ecx, 1
  000af	f3 aa		 rep stosb
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp

; 25   : 	for(auto &iter : m_mapParty)

  000b1	eb 0b		 jmp	 SHORT $LN4@OnTick
$LN2@OnTick:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 142  :         _Mybase::operator++();

  000b3	48 8d 4c 24 38	 lea	 rcx, QWORD PTR <begin>$L0$5[rsp]
  000b8	e8 00 00 00 00	 call	 ??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ ; std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::EntityParty *> > >,std::_Iterator_base0>::operator++
  000bd	90		 npad	 1
$LN4@OnTick:
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp

; 25   : 	for(auto &iter : m_mapParty)

  000be	0f b6 44 24 20	 movzx	 eax, BYTE PTR <end>$L0$1[rsp]
  000c3	88 44 24 28	 mov	 BYTE PTR __formal$[rsp], al
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 112  :         return !_Ptr->_Isnil;

  000c7	48 8b 44 24 38	 mov	 rax, QWORD PTR <begin>$L0$5[rsp]
  000cc	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  000d0	85 c0		 test	 eax, eax
  000d2	75 0a		 jne	 SHORT $LN71@OnTick
  000d4	c7 44 24 30 01
	00 00 00	 mov	 DWORD PTR tv194[rsp], 1
  000dc	eb 08		 jmp	 SHORT $LN72@OnTick
$LN71@OnTick:
  000de	c7 44 24 30 00
	00 00 00	 mov	 DWORD PTR tv194[rsp], 0
$LN72@OnTick:
  000e6	0f b6 44 24 30	 movzx	 eax, BYTE PTR tv194[rsp]
  000eb	88 44 24 21	 mov	 BYTE PTR $T2[rsp], al
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp

; 25   : 	for(auto &iter : m_mapParty)

  000ef	0f b6 44 24 21	 movzx	 eax, BYTE PTR $T2[rsp]
  000f4	0f b6 c0	 movzx	 eax, al
  000f7	85 c0		 test	 eax, eax
  000f9	74 6c		 je	 SHORT $LN1@OnTick
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 42   :         return _Ptr->_Myval;

  000fb	48 8b 44 24 38	 mov	 rax, QWORD PTR <begin>$L0$5[rsp]
  00100	48 83 c0 20	 add	 rax, 32			; 00000020H
  00104	48 89 44 24 70	 mov	 QWORD PTR $T10[rsp], rax

; 134  :         return const_cast<reference>(_Mybase::operator*());

  00109	48 8b 44 24 70	 mov	 rax, QWORD PTR $T10[rsp]
  0010e	48 89 44 24 78	 mov	 QWORD PTR $T11[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp

; 25   : 	for(auto &iter : m_mapParty)

  00113	48 8b 44 24 78	 mov	 rax, QWORD PTR $T11[rsp]
  00118	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR iter$12[rsp], rax

; 26   : 	{
; 27   : 		EntityParty* party = iter.second;

  00120	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR iter$12[rsp]
  00128	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0012c	48 89 44 24 40	 mov	 QWORD PTR party$6[rsp], rax

; 28   : 		VALID_DO( party && party->isRemove() == false, continue );

  00131	48 83 7c 24 40
	00		 cmp	 QWORD PTR party$6[rsp], 0
  00137	74 19		 je	 SHORT $LN7@OnTick
; File F:\Release_Branch\Server\Development\Framework\Entity\Entity.h

; 256  : 	return m_remove;

  00139	48 8b 44 24 40	 mov	 rax, QWORD PTR party$6[rsp]
  0013e	0f b6 40 08	 movzx	 eax, BYTE PTR [rax+8]
  00142	88 44 24 22	 mov	 BYTE PTR $T3[rsp], al
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp

; 28   : 		VALID_DO( party && party->isRemove() == false, continue );

  00146	0f b6 44 24 22	 movzx	 eax, BYTE PTR $T3[rsp]
  0014b	0f b6 c0	 movzx	 eax, al
  0014e	85 c0		 test	 eax, eax
  00150	74 05		 je	 SHORT $LN6@OnTick
$LN7@OnTick:
  00152	e9 5c ff ff ff	 jmp	 $LN2@OnTick
$LN6@OnTick:

; 29   : 
; 30   : 		party->OnTick4World();

  00157	48 8b 4c 24 40	 mov	 rcx, QWORD PTR party$6[rsp]
  0015c	e8 00 00 00 00	 call	 ?OnTick4World@EntityParty@mu2@@QEAAXXZ ; mu2::EntityParty::OnTick4World
  00161	90		 npad	 1

; 31   : 	}

  00162	e9 4c ff ff ff	 jmp	 $LN2@OnTick
$LN1@OnTick:

; 32   : }

  00167	48 81 c4 90 00
	00 00		 add	 rsp, 144		; 00000090H
  0016e	5f		 pop	 rdi
  0016f	c3		 ret	 0
?OnTick@ManagerPartyEntity@mu2@@QEAAXXZ ENDP		; mu2::ManagerPartyEntity::OnTick
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp
;	COMDAT ??1ManagerPartyEntity@mu2@@UEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1ManagerPartyEntity@mu2@@UEAA@XZ PROC			; mu2::ManagerPartyEntity::~ManagerPartyEntity, COMDAT

; 19   : {

$LN138:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ManagerPartyEntity@mu2@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx

; 20   : }

  00018	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 83 c0 08	 add	 rax, 8
  00021	48 8b c8	 mov	 rcx, rax
  00024	e8 00 00 00 00	 call	 ??1?$_Tree@V?$_Tmap_traits@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@$0A@@std@@@std@@QEAA@XZ ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >::~_Tree<std::_Tmap_traits<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> >,0> >
  00029	90		 npad	 1
  0002a	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0002e	c3		 ret	 0
??1ManagerPartyEntity@mu2@@UEAA@XZ ENDP			; mu2::ManagerPartyEntity::~ManagerPartyEntity
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\SimpleTimer.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp
;	COMDAT ??0ManagerPartyEntity@mu2@@QEAA@XZ
_TEXT	SEGMENT
this$ = 32
this$ = 64
??0ManagerPartyEntity@mu2@@QEAA@XZ PROC			; mu2::ManagerPartyEntity::ManagerPartyEntity, COMDAT

; 14   : {

$LN147:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H
  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ManagerPartyEntity@mu2@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx

; 12   : 	: m_mapParty()

  00018	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 83 c0 08	 add	 rax, 8
  00021	48 8b c8	 mov	 rcx, rax
  00024	e8 00 00 00 00	 call	 ??0?$map@IPEAVEntityParty@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIPEAVEntityParty@mu2@@@std@@@4@@std@@QEAA@XZ ; std::map<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> > >::map<unsigned int,mu2::EntityParty *,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::EntityParty *> > >

; 13   : 	, m_updateTimer()

  00029	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0002e	48 83 c0 18	 add	 rax, 24
  00032	48 89 44 24 20	 mov	 QWORD PTR this$[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\SimpleTimer.h

; 19   : 		, m_start(static_cast<Tick>(::GetTickCount64()))

  00037	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetTickCount64
  0003d	48 8b 4c 24 20	 mov	 rcx, QWORD PTR this$[rsp]
  00042	89 01		 mov	 DWORD PTR [rcx], eax

; 17   : 		: m_msInterval(msInterval)

  00044	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00049	c7 40 04 00 00
	00 00		 mov	 DWORD PTR [rax+4], 0

; 18   : 		, m_isInvoked(0)

  00050	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00055	c6 40 08 00	 mov	 BYTE PTR [rax+8], 0
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp

; 15   : }

  00059	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0005e	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00062	c3		 ret	 0
??0ManagerPartyEntity@mu2@@QEAA@XZ ENDP			; mu2::ManagerPartyEntity::ManagerPartyEntity
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ?_Throw_tree_length_error@std@@YAXXZ
_TEXT	SEGMENT
?_Throw_tree_length_error@std@@YAXXZ PROC		; std::_Throw_tree_length_error, COMDAT

; 416  : [[noreturn]] inline void _Throw_tree_length_error() {

$LN3:
  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 417  :     _Xlength_error("map/set too long");

  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_0BB@GCADKGJO@map?1set?5too?5long@
  0000b	e8 00 00 00 00	 call	 ?_Xlength_error@std@@YAXPEBD@Z ; std::_Xlength_error
  00010	90		 npad	 1
$LN2@Throw_tree:

; 418  : }

  00011	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00015	c3		 ret	 0
?_Throw_tree_length_error@std@@YAXXZ ENDP		; std::_Throw_tree_length_error
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z
_TEXT	SEGMENT
_Back_shift$ = 48
_Ptr_container$ = 56
_Ptr_user$ = 64
_Min_back_shift$ = 72
_Ptr$ = 96
_Bytes$ = 104
?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z PROC ; std::_Adjust_manually_vector_aligned, COMDAT

; 200  : inline void _Adjust_manually_vector_aligned(void*& _Ptr, size_t& _Bytes) {

$LN5:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 201  :     // adjust parameters from _Allocate_manually_vector_aligned to pass to operator delete
; 202  :     _Bytes += _Non_user_size;

  0000e	48 8b 44 24 68	 mov	 rax, QWORD PTR _Bytes$[rsp]
  00013	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00016	48 83 c0 27	 add	 rax, 39			; 00000027H
  0001a	48 8b 4c 24 68	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  0001f	48 89 01	 mov	 QWORD PTR [rcx], rax

; 203  : 
; 204  :     const uintptr_t* const _Ptr_user = static_cast<uintptr_t*>(_Ptr);

  00022	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00027	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0002a	48 89 44 24 40	 mov	 QWORD PTR _Ptr_user$[rsp], rax

; 205  :     const uintptr_t _Ptr_container   = _Ptr_user[-1];

  0002f	b8 08 00 00 00	 mov	 eax, 8
  00034	48 6b c0 ff	 imul	 rax, rax, -1
  00038	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr_user$[rsp]
  0003d	48 8b 04 01	 mov	 rax, QWORD PTR [rcx+rax]
  00041	48 89 44 24 38	 mov	 QWORD PTR _Ptr_container$[rsp], rax

; 206  : 
; 207  :     // If the following asserts, it likely means that we are performing
; 208  :     // an aligned delete on memory coming from an unaligned allocation.
; 209  :     _STL_ASSERT(_Ptr_user[-2] == _Big_allocation_sentinel, "invalid argument");
; 210  : 
; 211  :     // Extra paranoia on aligned allocation/deallocation; ensure _Ptr_container is
; 212  :     // in range [_Min_back_shift, _Non_user_size]
; 213  : #ifdef _DEBUG
; 214  :     constexpr uintptr_t _Min_back_shift = 2 * sizeof(void*);
; 215  : #else // ^^^ defined(_DEBUG) / !defined(_DEBUG) vvv
; 216  :     constexpr uintptr_t _Min_back_shift = sizeof(void*);

  00046	48 c7 44 24 48
	08 00 00 00	 mov	 QWORD PTR _Min_back_shift$[rsp], 8

; 217  : #endif // ^^^ !defined(_DEBUG) ^^^
; 218  :     const uintptr_t _Back_shift = reinterpret_cast<uintptr_t>(_Ptr) - _Ptr_container;

  0004f	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00054	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr_container$[rsp]
  00059	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0005c	48 2b c1	 sub	 rax, rcx
  0005f	48 89 44 24 30	 mov	 QWORD PTR _Back_shift$[rsp], rax

; 219  :     _STL_VERIFY(_Back_shift >= _Min_back_shift && _Back_shift <= _Non_user_size, "invalid argument");

  00064	48 83 7c 24 30
	08		 cmp	 QWORD PTR _Back_shift$[rsp], 8
  0006a	72 08		 jb	 SHORT $LN3@Adjust_man
  0006c	48 83 7c 24 30
	27		 cmp	 QWORD PTR _Back_shift$[rsp], 39 ; 00000027H
  00072	76 19		 jbe	 SHORT $LN2@Adjust_man
$LN3@Adjust_man:
  00074	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR [rsp+32], 0
  0007d	45 33 c9	 xor	 r9d, r9d
  00080	45 33 c0	 xor	 r8d, r8d
  00083	33 d2		 xor	 edx, edx
  00085	33 c9		 xor	 ecx, ecx
  00087	e8 00 00 00 00	 call	 _invoke_watson
  0008c	90		 npad	 1
$LN2@Adjust_man:

; 220  :     _Ptr = reinterpret_cast<void*>(_Ptr_container);

  0008d	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00092	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr_container$[rsp]
  00097	48 89 08	 mov	 QWORD PTR [rax], rcx
$LN4@Adjust_man:

; 221  : }

  0009a	48 83 c4 58	 add	 rsp, 88			; 00000058H
  0009e	c3		 ret	 0
?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ENDP ; std::_Adjust_manually_vector_aligned
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?_Throw_bad_array_new_length@std@@YAXXZ
_TEXT	SEGMENT
$T1 = 32
?_Throw_bad_array_new_length@std@@YAXXZ PROC		; std::_Throw_bad_array_new_length, COMDAT

; 107  : [[noreturn]] inline void _Throw_bad_array_new_length() {

$LN3:
  00000	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 108  :     _THROW(bad_array_new_length{});

  00004	48 8d 4c 24 20	 lea	 rcx, QWORD PTR $T1[rsp]
  00009	e8 00 00 00 00	 call	 ??0bad_array_new_length@std@@QEAA@XZ ; std::bad_array_new_length::bad_array_new_length
  0000e	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:_TI3?AVbad_array_new_length@std@@
  00015	48 8d 4c 24 20	 lea	 rcx, QWORD PTR $T1[rsp]
  0001a	e8 00 00 00 00	 call	 _CxxThrowException
  0001f	90		 npad	 1
$LN2@Throw_bad_:

; 109  : }

  00020	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00024	c3		 ret	 0
?_Throw_bad_array_new_length@std@@YAXXZ ENDP		; std::_Throw_bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gbad_array_new_length@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gbad_array_new_length@std@@UEAAPEAXI@Z PROC		; std::bad_array_new_length::`scalar deleting destructor', COMDAT
$LN20:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gbad_array_new_length@std@@UEAAPEAXI@Z ENDP		; std::bad_array_new_length::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??0bad_array_new_length@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
__that$ = 56
??0bad_array_new_length@std@@QEAA@AEBV01@@Z PROC	; std::bad_array_new_length::bad_array_new_length, COMDAT
$LN14:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000e	48 8b 54 24 38	 mov	 rdx, QWORD PTR __that$[rsp]
  00013	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00018	e8 00 00 00 00	 call	 ??0bad_alloc@std@@QEAA@AEBV01@@Z
  0001d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00022	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_array_new_length@std@@6B@
  00029	48 89 08	 mov	 QWORD PTR [rax], rcx
  0002c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00031	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00035	c3		 ret	 0
??0bad_array_new_length@std@@QEAA@AEBV01@@Z ENDP	; std::bad_array_new_length::bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??1bad_array_new_length@std@@UEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1bad_array_new_length@std@@UEAA@XZ PROC		; std::bad_array_new_length::~bad_array_new_length, COMDAT
$LN14:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  00018	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 83 c0 08	 add	 rax, 8
  00021	48 8b c8	 mov	 rcx, rax
  00024	e8 00 00 00 00	 call	 __std_exception_destroy
  00029	90		 npad	 1
  0002a	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0002e	c3		 ret	 0
??1bad_array_new_length@std@@UEAA@XZ ENDP		; std::bad_array_new_length::~bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0bad_array_new_length@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 16
??0bad_array_new_length@std@@QEAA@XZ PROC		; std::bad_array_new_length::bad_array_new_length, COMDAT

; 144  :     {

$LN14:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi

; 67   :     {

  00006	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0000b	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00012	48 89 08	 mov	 QWORD PTR [rax], rcx

; 66   :         : _Data()

  00015	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 83 c0 08	 add	 rax, 8
  0001e	48 8b f8	 mov	 rdi, rax
  00021	33 c0		 xor	 eax, eax
  00023	b9 10 00 00 00	 mov	 ecx, 16
  00028	f3 aa		 rep stosb

; 68   :         _Data._What = _Message;

  0002a	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0002f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_0BF@KINCDENJ@bad?5array?5new?5length@
  00036	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 133  :     {

  0003a	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0003f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_alloc@std@@6B@
  00046	48 89 08	 mov	 QWORD PTR [rax], rcx

; 144  :     {

  00049	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_array_new_length@std@@6B@
  00055	48 89 08	 mov	 QWORD PTR [rax], rcx

; 145  :     }

  00058	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0005d	5f		 pop	 rdi
  0005e	c3		 ret	 0
??0bad_array_new_length@std@@QEAA@XZ ENDP		; std::bad_array_new_length::bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gbad_alloc@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gbad_alloc@std@@UEAAPEAXI@Z PROC			; std::bad_alloc::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gbad_alloc@std@@UEAAPEAXI@Z ENDP			; std::bad_alloc::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0bad_alloc@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
__that$ = 56
??0bad_alloc@std@@QEAA@AEBV01@@Z PROC			; std::bad_alloc::bad_alloc, COMDAT
$LN9:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 20	 sub	 rsp, 32			; 00000020H

; 73   :     {

  0000f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  0001b	48 89 08	 mov	 QWORD PTR [rax], rcx

; 72   :         : _Data()

  0001e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 83 c0 08	 add	 rax, 8
  00027	48 8b f8	 mov	 rdi, rax
  0002a	33 c0		 xor	 eax, eax
  0002c	b9 10 00 00 00	 mov	 ecx, 16
  00031	f3 aa		 rep stosb

; 74   :         __std_exception_copy(&_Other._Data, &_Data);

  00033	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 83 c0 08	 add	 rax, 8
  0003c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR __that$[rsp]
  00041	48 83 c1 08	 add	 rcx, 8
  00045	48 8b d0	 mov	 rdx, rax
  00048	e8 00 00 00 00	 call	 __std_exception_copy
  0004d	90		 npad	 1
  0004e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_alloc@std@@6B@
  0005a	48 89 08	 mov	 QWORD PTR [rax], rcx
  0005d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00062	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00066	5f		 pop	 rdi
  00067	c3		 ret	 0
??0bad_alloc@std@@QEAA@AEBV01@@Z ENDP			; std::bad_alloc::bad_alloc
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gexception@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gexception@std@@UEAAPEAXI@Z PROC			; std::exception::`scalar deleting destructor', COMDAT
$LN10:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gexception@std@@UEAAPEAXI@Z ENDP			; std::exception::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ?what@exception@std@@UEBAPEBDXZ
_TEXT	SEGMENT
tv69 = 0
this$ = 32
?what@exception@std@@UEBAPEBDXZ PROC			; std::exception::what, COMDAT

; 95   :     {

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 18	 sub	 rsp, 24

; 96   :         return _Data._What ? _Data._What : "Unknown exception";

  00009	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00013	74 0f		 je	 SHORT $LN3@what
  00015	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0001e	48 89 04 24	 mov	 QWORD PTR tv69[rsp], rax
  00022	eb 0b		 jmp	 SHORT $LN4@what
$LN3@what:
  00024	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BC@EOODALEL@Unknown?5exception@
  0002b	48 89 04 24	 mov	 QWORD PTR tv69[rsp], rax
$LN4@what:
  0002f	48 8b 04 24	 mov	 rax, QWORD PTR tv69[rsp]

; 97   :     }

  00033	48 83 c4 18	 add	 rsp, 24
  00037	c3		 ret	 0
?what@exception@std@@UEBAPEBDXZ ENDP			; std::exception::what
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0exception@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
_Other$ = 56
??0exception@std@@QEAA@AEBV01@@Z PROC			; std::exception::exception, COMDAT

; 73   :     {

$LN4:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  0000f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  0001b	48 89 08	 mov	 QWORD PTR [rax], rcx

; 72   :         : _Data()

  0001e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 83 c0 08	 add	 rax, 8
  00027	48 8b f8	 mov	 rdi, rax
  0002a	33 c0		 xor	 eax, eax
  0002c	b9 10 00 00 00	 mov	 ecx, 16
  00031	f3 aa		 rep stosb

; 74   :         __std_exception_copy(&_Other._Data, &_Data);

  00033	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 83 c0 08	 add	 rax, 8
  0003c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Other$[rsp]
  00041	48 83 c1 08	 add	 rcx, 8
  00045	48 8b d0	 mov	 rdx, rax
  00048	e8 00 00 00 00	 call	 __std_exception_copy
  0004d	90		 npad	 1

; 75   :     }

  0004e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00057	5f		 pop	 rdi
  00058	c3		 ret	 0
??0exception@std@@QEAA@AEBV01@@Z ENDP			; std::exception::exception
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
