; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	??0exception@std@@QEAA@AEBV01@@Z		; std::exception::exception
PUBLIC	?what@exception@std@@UEBAPEBDXZ			; std::exception::what
PUBLIC	??_Gexception@std@@UEAAPEAXI@Z			; std::exception::`scalar deleting destructor'
PUBLIC	??0bad_alloc@std@@QEAA@AEBV01@@Z		; std::bad_alloc::bad_alloc
PUBLIC	??_Gbad_alloc@std@@UEAAPEAXI@Z			; std::bad_alloc::`scalar deleting destructor'
PUBL<PERSON>	??0bad_array_new_length@std@@QEAA@XZ		; std::bad_array_new_length::bad_array_new_length
PUBLIC	??1bad_array_new_length@std@@UEAA@XZ		; std::bad_array_new_length::~bad_array_new_length
PUBLIC	??0bad_array_new_length@std@@QEAA@AEBV01@@Z	; std::bad_array_new_length::bad_array_new_length
PUBLIC	??_Gbad_array_new_length@std@@UEAAPEAXI@Z	; std::bad_array_new_length::`scalar deleting destructor'
PUBLIC	?_Throw_bad_array_new_length@std@@YAXXZ		; std::_Throw_bad_array_new_length
PUBLIC	?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
PUBLIC	??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
PUBLIC	?c_str@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBAPEBDXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::c_str
PUBLIC	?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Tidy_deallocate
PUBLIC	?_Decref@_Ref_count_base@std@@QEAAXXZ		; std::_Ref_count_base::_Decref
PUBLIC	?_Get_deleter@_Ref_count_base@std@@UEBAPEAXAEBVtype_info@@@Z ; std::_Ref_count_base::_Get_deleter
PUBLIC	?Write@PacketStream@mu2@@QEAAIPEBEI@Z		; mu2::PacketStream::Write
PUBLIC	?Read@PacketStream@mu2@@QEAAIPEAEI@Z		; mu2::PacketStream::Read
PUBLIC	??$rw@VVector3@mu2@@@PacketStream@mu2@@QEAA_NAEAVVector3@1@@Z ; mu2::PacketStream::rw<mu2::Vector3>
PUBLIC	??$rw@H@PacketStream@mu2@@QEAA_NAEAH@Z		; mu2::PacketStream::rw<int>
PUBLIC	??$rw@M@PacketStream@mu2@@QEAA_NAEAM@Z		; mu2::PacketStream::rw<float>
PUBLIC	?checkingBuffer@PacketStream@mu2@@AEAA_NXZ	; mu2::PacketStream::checkingBuffer
PUBLIC	??$swapEndian@H@PacketStream@mu2@@AEAAHAEBH@Z	; mu2::PacketStream::swapEndian<int>
PUBLIC	??$swapEndian@M@PacketStream@mu2@@AEAAMAEBM@Z	; mu2::PacketStream::swapEndian<float>
PUBLIC	??1ISerializer@mu2@@UEAA@XZ			; mu2::ISerializer::~ISerializer
PUBLIC	??_GISerializer@mu2@@UEAAPEAXI@Z		; mu2::ISerializer::`scalar deleting destructor'
PUBLIC	?OnTick@Action@mu2@@UEAAXXZ			; mu2::Action::OnTick
PUBLIC	?allocate@?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@2@_K@Z ; std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> >::allocate
PUBLIC	??1?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAA@XZ ; std::_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::~_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >
PUBLIC	??4?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAAEAV01@AEBV01@@Z ; std::_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::operator=
PUBLIC	?clear@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAXXZ ; std::_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::clear
PUBLIC	?_Alloc_sentinel_and_proxy@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAXXZ ; std::_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Alloc_sentinel_and_proxy
PUBLIC	??0?$map@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ ; std::map<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >::map<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >
PUBLIC	??1?$map@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ ; std::map<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >::~map<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >
PUBLIC	??$rw@VExecutionZoneId@mu2@@@PacketStream@mu2@@QEAA_NAEAVExecutionZoneId@1@@Z ; mu2::PacketStream::rw<mu2::ExecutionZoneId>
PUBLIC	?Reset@ExecLocation@mu2@@UEAAXXZ		; mu2::ExecLocation::Reset
PUBLIC	?PackUnpack@ExecLocation@mu2@@UEAA_NAEAVPacketStream@2@@Z ; mu2::ExecLocation::PackUnpack
PUBLIC	??0ExecLocation@mu2@@QEAA@XZ			; mu2::ExecLocation::ExecLocation
PUBLIC	??1ExecLocation@mu2@@UEAA@XZ			; mu2::ExecLocation::~ExecLocation
PUBLIC	??_GExecLocation@mu2@@UEAAPEAXI@Z		; mu2::ExecLocation::`scalar deleting destructor'
PUBLIC	??1?$SmartPtrEx@UEvent@mu2@@@mu2@@QEAA@XZ	; mu2::SmartPtrEx<mu2::Event>::~SmartPtrEx<mu2::Event>
PUBLIC	??4?$SmartPtrEx@UEvent@mu2@@@mu2@@QEAAAEAV01@AEBV01@@Z ; mu2::SmartPtrEx<mu2::Event>::operator=
PUBLIC	?_Swap@?$_Ptr_base@UEvent@mu2@@@std@@IEAAXAEAV12@@Z ; std::_Ptr_base<mu2::Event>::_Swap
PUBLIC	??0?$shared_ptr@UEvent@mu2@@@std@@QEAA@AEBV01@@Z ; std::shared_ptr<mu2::Event>::shared_ptr<mu2::Event>
PUBLIC	??4?$shared_ptr@UEvent@mu2@@@std@@QEAAAEAV01@AEBV01@@Z ; std::shared_ptr<mu2::Event>::operator=
PUBLIC	??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,unsigned int> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >
PUBLIC	??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,unsigned int> > >::_Erase_head<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >
PUBLIC	??$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z ; std::shared_ptr<mu2::Event>::shared_ptr<mu2::Event><mu2::Event,0>
PUBLIC	??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@std@@QEAA@XZ ; std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >
PUBLIC	??1?$_Temporary_owner@UEvent@mu2@@@std@@QEAA@XZ	; std::_Temporary_owner<mu2::Event>::~_Temporary_owner<mu2::Event>
PUBLIC	?_Destroy@?$_Ref_count@UEvent@mu2@@@std@@EEAAXXZ ; std::_Ref_count<mu2::Event>::_Destroy
PUBLIC	?_Delete_this@?$_Ref_count@UEvent@mu2@@@std@@EEAAXXZ ; std::_Ref_count<mu2::Event>::_Delete_this
PUBLIC	??_G?$_Ref_count@UEvent@mu2@@@std@@UEAAPEAXI@Z	; std::_Ref_count<mu2::Event>::`scalar deleting destructor'
PUBLIC	??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
PUBLIC	??$_Buyheadnode@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@@Z ; std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *>::_Buyheadnode<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >
PUBLIC	??_G?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@UEAAPEAXI@Z ; mu2::ISingleton<mu2::theManagerJoinContext>::`scalar deleting destructor'
PUBLIC	??_GtheManagerJoinContext@mu2@@UEAAPEAXI@Z	; mu2::theManagerJoinContext::`scalar deleting destructor'
PUBLIC	??0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z ; mu2::PlayerJoinAction::PlayerJoinAction
PUBLIC	??1PlayerJoinAction@mu2@@UEAA@XZ		; mu2::PlayerJoinAction::~PlayerJoinAction
PUBLIC	?Reset@PlayerJoinAction@mu2@@UEAAXXZ		; mu2::PlayerJoinAction::Reset
PUBLIC	?ResetInReady@PlayerJoinAction@mu2@@QEAAXXZ	; mu2::PlayerJoinAction::ResetInReady
PUBLIC	?TranStateJoinExecution@PlayerJoinAction@mu2@@QEAA?AW4Error@ErrorJoin@2@W4Enum@JoinContext@2@AEBVExecutionZoneId@2@HAEBVVector3@2@PEBUInstanceDungeonInfo@2@@Z ; mu2::PlayerJoinAction::TranStateJoinExecution
PUBLIC	?GetJoinContextBase@PlayerJoinAction@mu2@@QEBAAEBVJoinContextBase@2@XZ ; mu2::PlayerJoinAction::GetJoinContextBase
PUBLIC	?GetJoinContext@PlayerJoinAction@mu2@@QEBA?BW4Enum@JoinContext@2@XZ ; mu2::PlayerJoinAction::GetJoinContext
PUBLIC	?GetPortalType4Client@PlayerJoinAction@mu2@@QEBA?AW4Enum@PortalType@2@XZ ; mu2::PlayerJoinAction::GetPortalType4Client
PUBLIC	?GetContinent@PlayerJoinAction@mu2@@QEBAEXZ	; mu2::PlayerJoinAction::GetContinent
PUBLIC	?GetToLocation@PlayerJoinAction@mu2@@QEBAAEBUExecLocation@2@XZ ; mu2::PlayerJoinAction::GetToLocation
PUBLIC	?GetToExecution@PlayerJoinAction@mu2@@QEAAAEBVExecution@2@XZ ; mu2::PlayerJoinAction::GetToExecution
PUBLIC	?IsEnterableJoinState@PlayerJoinAction@mu2@@QEBA_NXZ ; mu2::PlayerJoinAction::IsEnterableJoinState
PUBLIC	?SetELoopbackWorldLogout@PlayerJoinAction@mu2@@QEAAXAEBV?$SmartPtrEx@UEvent@mu2@@@2@@Z ; mu2::PlayerJoinAction::SetELoopbackWorldLogout
PUBLIC	?GetELoopbackWorldLogout@PlayerJoinAction@mu2@@QEAA?AV?$SmartPtrEx@UEvent@mu2@@@2@XZ ; mu2::PlayerJoinAction::GetELoopbackWorldLogout
PUBLIC	?IsWorldLogout@PlayerJoinAction@mu2@@QEBA_NXZ	; mu2::PlayerJoinAction::IsWorldLogout
PUBLIC	?SetJoinContext@PlayerJoinAction@mu2@@QEAAXW4Enum@JoinContext@2@@Z ; mu2::PlayerJoinAction::SetJoinContext
PUBLIC	?SetDead@PlayerJoinAction@mu2@@QEAAX_N@Z	; mu2::PlayerJoinAction::SetDead
PUBLIC	?IsDead@PlayerJoinAction@mu2@@QEBA_NXZ		; mu2::PlayerJoinAction::IsDead
PUBLIC	?SetTalismanSkillInfo@PlayerJoinAction@mu2@@QEAAXAEBV?$map@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@I@Z ; mu2::PlayerJoinAction::SetTalismanSkillInfo
PUBLIC	?GetTalismanSkillInfo@PlayerJoinAction@mu2@@QEAAXAEBVJoinZoneContextCurr@2@AEBVJoinZoneContextDest@2@AEAV?$map@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@AEAI@Z ; mu2::PlayerJoinAction::GetTalismanSkillInfo
PUBLIC	?IsMultistageDungeon@PlayerJoinAction@mu2@@QEBA_NAEBVJoinZoneContextCurr@2@AEBVJoinZoneContextDest@2@@Z ; mu2::PlayerJoinAction::IsMultistageDungeon
PUBLIC	?InitContext@PlayerJoinAction@mu2@@AEAAXW4Enum@JoinContext@2@AEBVExecutionZoneId@2@HAEBVVector3@2@PEBUInstanceDungeonInfo@2@@Z ; mu2::PlayerJoinAction::InitContext
PUBLIC	?SetToExecId@PlayerJoinAction@mu2@@AEAAXAEBVExecutionZoneId@2@@Z ; mu2::PlayerJoinAction::SetToExecId
PUBLIC	?IsValid@PlayerJoinAction@mu2@@AEBA_NXZ		; mu2::PlayerJoinAction::IsValid
PUBLIC	?SetPortalType4Client@PlayerJoinAction@mu2@@AEAAXW4Enum@PortalType@2@@Z ; mu2::PlayerJoinAction::SetPortalType4Client
PUBLIC	??_GPlayerJoinAction@mu2@@UEAAPEAXI@Z		; mu2::PlayerJoinAction::`scalar deleting destructor'
PUBLIC	??$_Copy@$0A@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAXAEBV01@@Z ; std::_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Copy<0>
PUBLIC	??$_Copy_nodes@$0A@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@PEAU21@0@Z ; std::_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Copy_nodes<0>
PUBLIC	??1?$_Erase_tree_and_orphan_guard@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAA@XZ ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,unsigned int> > >::_Erase_tree_and_orphan_guard<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >::~_Erase_tree_and_orphan_guard<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >
PUBLIC	??$_Buynode@AEAU?$pair@$$CBII@std@@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@AEAU?$pair@$$CBII@1@@Z ; std::_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Buynode<std::pair<unsigned int const ,unsigned int> &>
PUBLIC	??$_Erase_tree_and_orphan@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,unsigned int> > >::_Erase_tree_and_orphan<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >
PUBLIC	??$_Buynode@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@AEAU?$pair@$$CBII@2@@?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU01@AEAU?$pair@$$CBII@1@@Z ; std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *>::_Buynode<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> >,std::pair<unsigned int const ,unsigned int> &>
PUBLIC	??_7exception@std@@6B@				; std::exception::`vftable'
PUBLIC	??_C@_0BC@EOODALEL@Unknown?5exception@		; `string'
PUBLIC	??_7bad_alloc@std@@6B@				; std::bad_alloc::`vftable'
PUBLIC	??_7bad_array_new_length@std@@6B@		; std::bad_array_new_length::`vftable'
PUBLIC	??_C@_0BF@KINCDENJ@bad?5array?5new?5length@	; `string'
PUBLIC	??_R0?AVexception@std@@@8			; std::exception `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
PUBLIC	_TI3?AVbad_array_new_length@std@@
PUBLIC	_CTA3?AVbad_array_new_length@std@@
PUBLIC	??_R0?AVbad_array_new_length@std@@@8		; std::bad_array_new_length `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
PUBLIC	??_R0?AVbad_alloc@std@@@8			; std::bad_alloc `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
PUBLIC	??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@ ; `string'
PUBLIC	??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@		; `string'
PUBLIC	??_7ISerializer@mu2@@6B@			; mu2::ISerializer::`vftable'
PUBLIC	??_C@_0EJ@IFEHPKPB@F?3?2Release_Branch?2Server?2Develo@ ; `string'
PUBLIC	??_C@_0BB@HAIDOJLN@checkingBuffer?$CI?$CJ@	; `string'
PUBLIC	??_C@_0BJ@NEGBBJOE@mu2?3?3PacketStream?3?3Write@ ; `string'
PUBLIC	??_C@_05IOMEMJEC@count@				; `string'
PUBLIC	??_C@_0BI@KGAODAAL@mu2?3?3PacketStream?3?3Read@	; `string'
PUBLIC	??_C@_03COAJHJPB@buf@				; `string'
PUBLIC	??_C@_09CEDEMLBJ@IsValid?$CI?$CJ@		; `string'
PUBLIC	??_7ExecLocation@mu2@@6B@			; mu2::ExecLocation::`vftable'
PUBLIC	?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A ; mu2::ISingleton<mu2::GlobalLoadScript>::inst
PUBLIC	??_C@_06BALNJMNP@player@			; `string'
PUBLIC	??_7?$_Ref_count@UEvent@mu2@@@std@@6B@		; std::_Ref_count<mu2::Event>::`vftable'
PUBLIC	??_R4exception@std@@6B@				; std::exception::`RTTI Complete Object Locator'
PUBLIC	??_R3exception@std@@8				; std::exception::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2exception@std@@8				; std::exception::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@exception@std@@8			; std::exception::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4bad_array_new_length@std@@6B@		; std::bad_array_new_length::`RTTI Complete Object Locator'
PUBLIC	??_R3bad_array_new_length@std@@8		; std::bad_array_new_length::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2bad_array_new_length@std@@8		; std::bad_array_new_length::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@bad_array_new_length@std@@8	; std::bad_array_new_length::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@bad_alloc@std@@8			; std::bad_alloc::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R3bad_alloc@std@@8				; std::bad_alloc::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2bad_alloc@std@@8				; std::bad_alloc::`RTTI Base Class Array'
PUBLIC	??_R4bad_alloc@std@@6B@				; std::bad_alloc::`RTTI Complete Object Locator'
PUBLIC	??_R0?AV_Ref_count_base@std@@@8			; std::_Ref_count_base `RTTI Type Descriptor'
PUBLIC	??_R3_Ref_count_base@std@@8			; std::_Ref_count_base::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2_Ref_count_base@std@@8			; std::_Ref_count_base::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@_Ref_count_base@std@@8		; std::_Ref_count_base::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R17?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Base Class Descriptor at (8,-1,0,64)'
PUBLIC	??_R0?AV?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> > `RTTI Type Descriptor'
PUBLIC	??_R3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4ISerializer@mu2@@6B@			; mu2::ISerializer::`RTTI Complete Object Locator'
PUBLIC	??_R0?AUISerializer@mu2@@@8			; mu2::ISerializer `RTTI Type Descriptor'
PUBLIC	??_R3ISerializer@mu2@@8				; mu2::ISerializer::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2ISerializer@mu2@@8				; mu2::ISerializer::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@ISerializer@mu2@@8		; mu2::ISerializer::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R0?AVAction@mu2@@@8				; mu2::Action `RTTI Type Descriptor'
PUBLIC	??_R3Action@mu2@@8				; mu2::Action::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2Action@mu2@@8				; mu2::Action::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@Action@mu2@@8			; mu2::Action::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4ExecLocation@mu2@@6B@			; mu2::ExecLocation::`RTTI Complete Object Locator'
PUBLIC	??_R0?AUExecLocation@mu2@@@8			; mu2::ExecLocation `RTTI Type Descriptor'
PUBLIC	??_R3ExecLocation@mu2@@8			; mu2::ExecLocation::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2ExecLocation@mu2@@8			; mu2::ExecLocation::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@ExecLocation@mu2@@8		; mu2::ExecLocation::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4?$_Ref_count@UEvent@mu2@@@std@@6B@		; std::_Ref_count<mu2::Event>::`RTTI Complete Object Locator'
PUBLIC	??_R0?AV?$_Ref_count@UEvent@mu2@@@std@@@8	; std::_Ref_count<mu2::Event> `RTTI Type Descriptor'
PUBLIC	??_R3?$_Ref_count@UEvent@mu2@@@std@@8		; std::_Ref_count<mu2::Event>::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2?$_Ref_count@UEvent@mu2@@@std@@8		; std::_Ref_count<mu2::Event>::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@?$_Ref_count@UEvent@mu2@@@std@@8	; std::_Ref_count<mu2::Event>::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A ; mu2::ISingleton<mu2::ExecutionManager>::inst
PUBLIC	?inst@?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@1VtheManagerJoinContext@2@A ; mu2::ISingleton<mu2::theManagerJoinContext>::inst
PUBLIC	??_7?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@6B@ ; mu2::ISingleton<mu2::theManagerJoinContext>::`vftable'
PUBLIC	??_7theManagerJoinContext@mu2@@6B@		; mu2::theManagerJoinContext::`vftable'
PUBLIC	??_7PlayerJoinAction@mu2@@6B@			; mu2::PlayerJoinAction::`vftable'
PUBLIC	??_C@_0EP@HBIDHLFH@F?3?2Release_Branch?2Server?2Develo@ ; `string'
PUBLIC	??_C@_0CB@FJFBAPGB@eContext?5?$CB?$DN?5JoinContext?3?3JC_NON@ ; `string'
PUBLIC	??_C@_0CD@ONJPOCCC@mu2?3?3PlayerJoinAction?3?3InitCont@ ; `string'
PUBLIC	??_C@_0DH@CMCFJJFD@toExecId?4IsValid?$CI?$CJ?5?$CG?$CG?5eContext?5@ ; `string'
PUBLIC	??_C@_0CM@DBNLLACM@mu2?3?3PlayerJoinAction?3?3IsEntera@ ; `string'
PUBLIC	??_C@_05EMNBIGOA@pCurr@				; `string'
PUBLIC	??_C@_0DJ@DJGHFFHF@IsEnterableJoinState?$DO?5failed?5?9?5@ ; `string'
PUBLIC	??_C@_0CO@LEIOINCE@mu2?3?3PlayerJoinAction?3?3TranStat@ ; `string'
PUBLIC	??_C@_0BH@LGPKNFKE@IsEnterableJoinState?$CI?$CJ@ ; `string'
PUBLIC	??_C@_0N@MDLFKPJI@m_pToContext@			; `string'
PUBLIC	??_C@_0CG@PJGPIGCO@mu2?3?3PlayerJoinAction?3?3GetJoinC@ ; `string'
PUBLIC	??_C@_0CG@DHHBHMAC@mu2?3?3PlayerJoinAction?3?3SetJoinC@ ; `string'
PUBLIC	??_C@_0O@CDHODAOF@destWorldElem@		; `string'
PUBLIC	??_C@_0CL@NLMJGNPH@mu2?3?3PlayerJoinAction?3?3IsMultis@ ; `string'
PUBLIC	??_R4PlayerJoinAction@mu2@@6B@			; mu2::PlayerJoinAction::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVPlayerJoinAction@mu2@@@8		; mu2::PlayerJoinAction `RTTI Type Descriptor'
PUBLIC	??_R3PlayerJoinAction@mu2@@8			; mu2::PlayerJoinAction::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2PlayerJoinAction@mu2@@8			; mu2::PlayerJoinAction::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@PlayerJoinAction@mu2@@8		; mu2::PlayerJoinAction::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@ActionPlayer@mu2@@8		; mu2::ActionPlayer::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R0?AVActionPlayer@mu2@@@8			; mu2::ActionPlayer `RTTI Type Descriptor'
PUBLIC	??_R3ActionPlayer@mu2@@8			; mu2::ActionPlayer::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2ActionPlayer@mu2@@8			; mu2::ActionPlayer::`RTTI Base Class Array'
PUBLIC	??_R4theManagerJoinContext@mu2@@6B@		; mu2::theManagerJoinContext::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVtheManagerJoinContext@mu2@@@8		; mu2::theManagerJoinContext `RTTI Type Descriptor'
PUBLIC	??_R3theManagerJoinContext@mu2@@8		; mu2::theManagerJoinContext::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2theManagerJoinContext@mu2@@8		; mu2::theManagerJoinContext::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@theManagerJoinContext@mu2@@8	; mu2::theManagerJoinContext::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@8 ; mu2::ISingleton<mu2::theManagerJoinContext>::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R0?AV?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@@8 ; mu2::ISingleton<mu2::theManagerJoinContext> `RTTI Type Descriptor'
PUBLIC	??_R3?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@8 ; mu2::ISingleton<mu2::theManagerJoinContext>::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@8 ; mu2::ISingleton<mu2::theManagerJoinContext>::`RTTI Base Class Array'
PUBLIC	??_R4?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@6B@ ; mu2::ISingleton<mu2::theManagerJoinContext>::`RTTI Complete Object Locator'
EXTRN	_purecall:PROC
EXTRN	??2@YAPEAX_K@Z:PROC				; operator new
EXTRN	??3@YAXPEAX_K@Z:PROC				; operator delete
EXTRN	?__global_delete@@YAXPEAX_K@Z:PROC		; __global_delete
EXTRN	atexit:PROC
EXTRN	__std_terminate:PROC
EXTRN	_invoke_watson:PROC
EXTRN	memcpy:PROC
EXTRN	__imp_GetStdHandle:PROC
EXTRN	__imp_SetConsoleTextAttribute:PROC
EXTRN	free:PROC
EXTRN	__std_exception_copy:PROC
EXTRN	__std_exception_destroy:PROC
EXTRN	??_Eexception@std@@UEAAPEAXI@Z:PROC		; std::exception::`vector deleting destructor'
EXTRN	??_Ebad_alloc@std@@UEAAPEAXI@Z:PROC		; std::bad_alloc::`vector deleting destructor'
EXTRN	??_Ebad_array_new_length@std@@UEAAPEAXI@Z:PROC	; std::bad_array_new_length::`vector deleting destructor'
EXTRN	?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ:PROC	; mu2::Logger::Logging
EXTRN	?isValid@PacketStream@mu2@@QEBA_NXZ:PROC	; mu2::PacketStream::isValid
EXTRN	?remains@PacketStream@mu2@@AEAAIXZ:PROC		; mu2::PacketStream::remains
EXTRN	?SetError@PacketStream@mu2@@AEAAXW4Error@ErrorNet@2@@Z:PROC ; mu2::PacketStream::SetError
EXTRN	??_EISerializer@mu2@@UEAAPEAXI@Z:PROC		; mu2::ISerializer::`vector deleting destructor'
EXTRN	??0ExecutionZoneId@mu2@@QEAA@XZ:PROC		; mu2::ExecutionZoneId::ExecutionZoneId
EXTRN	?GetZoneIndex@ExecutionZoneId@mu2@@QEBAGXZ:PROC	; mu2::ExecutionZoneId::GetZoneIndex
EXTRN	?Clear@ExecutionZoneId@mu2@@QEAAXXZ:PROC	; mu2::ExecutionZoneId::Clear
EXTRN	?IsValid@ExecutionZoneId@mu2@@QEBA?B_NXZ:PROC	; mu2::ExecutionZoneId::IsValid
EXTRN	?IsValidStong@ExecutionZoneId@mu2@@QEBA?B_NXZ:PROC ; mu2::ExecutionZoneId::IsValidStong
EXTRN	??8ExecutionZoneId@mu2@@QEBA_NAEBV01@@Z:PROC	; mu2::ExecutionZoneId::operator==
EXTRN	??_EExecLocation@mu2@@UEAAPEAXI@Z:PROC		; mu2::ExecLocation::`vector deleting destructor'
EXTRN	??1GlobalLoadScript@mu2@@UEAA@XZ:PROC		; mu2::GlobalLoadScript::~GlobalLoadScript
EXTRN	?GetWorldScript@GlobalLoadScript@mu2@@QEBAPEBUWorldElem@2@G@Z:PROC ; mu2::GlobalLoadScript::GetWorldScript
EXTRN	??0GlobalLoadScript@mu2@@AEAA@XZ:PROC		; mu2::GlobalLoadScript::GlobalLoadScript
EXTRN	?ToString@EntityPlayer@mu2@@QEAA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ:PROC ; mu2::EntityPlayer::ToString
EXTRN	?ChangeTran@EntityPlayer@mu2@@QEAAXW4PlayerState@@@Z:PROC ; mu2::EntityPlayer::ChangeTran
EXTRN	?GetCurrExecId@EntityPlayer@mu2@@QEBAAEBVExecutionZoneId@2@XZ:PROC ; mu2::EntityPlayer::GetCurrExecId
EXTRN	?GetCurrentState@EntityPlayer@mu2@@QEAAPEAVStatePlayer@2@XZ:PROC ; mu2::EntityPlayer::GetCurrentState
EXTRN	??0ActionPlayer@mu2@@QEAA@PEAVEntityPlayer@1@I@Z:PROC ; mu2::ActionPlayer::ActionPlayer
EXTRN	??1ActionPlayer@mu2@@UEAA@XZ:PROC		; mu2::ActionPlayer::~ActionPlayer
EXTRN	??_E?$_Ref_count@UEvent@mu2@@@std@@UEAAPEAXI@Z:PROC ; std::_Ref_count<mu2::Event>::`vector deleting destructor'
EXTRN	??1ExecutionManager@mu2@@UEAA@XZ:PROC		; mu2::ExecutionManager::~ExecutionManager
EXTRN	?GetExecution@ExecutionManager@mu2@@QEAAAEBVExecution@2@AEBVExecutionZoneId@2@@Z:PROC ; mu2::ExecutionManager::GetExecution
EXTRN	??0ExecutionManager@mu2@@AEAA@XZ:PROC		; mu2::ExecutionManager::ExecutionManager
EXTRN	??_E?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@UEAAPEAXI@Z:PROC ; mu2::ISingleton<mu2::theManagerJoinContext>::`vector deleting destructor'
EXTRN	?Init@theManagerJoinContext@mu2@@UEAA_NPEAX@Z:PROC ; mu2::theManagerJoinContext::Init
EXTRN	?UnInit@theManagerJoinContext@mu2@@UEAA_NXZ:PROC ; mu2::theManagerJoinContext::UnInit
EXTRN	??AtheManagerJoinContext@mu2@@QEBAPEAVJoinContextBase@1@W4Enum@JoinContext@1@@Z:PROC ; mu2::theManagerJoinContext::operator[]
EXTRN	??_EtheManagerJoinContext@mu2@@UEAAPEAXI@Z:PROC	; mu2::theManagerJoinContext::`vector deleting destructor'
EXTRN	??_EPlayerJoinAction@mu2@@UEAAPEAXI@Z:PROC	; mu2::PlayerJoinAction::`vector deleting destructor'
EXTRN	_CxxThrowException:PROC
EXTRN	__CxxFrameHandler4:PROC
EXTRN	__GSHandlerCheck_EH4:PROC
EXTRN	__security_check_cookie:PROC
EXTRN	??_7type_info@@6B@:BYTE				; type_info::`vftable'
EXTRN	?isBigEndian@PacketStream@mu2@@2_NA:BYTE	; mu2::PacketStream::isBigEndian
EXTRN	__security_cookie:QWORD
EXTRN	_fltused:DWORD
;	COMDAT ?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A
_BSS	SEGMENT
?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A DB 0d0H DUP (?) ; mu2::ISingleton<mu2::GlobalLoadScript>::inst
_BSS	ENDS
;	COMDAT ?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A
_BSS	SEGMENT
?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A DB 01e0H DUP (?) ; mu2::ISingleton<mu2::ExecutionManager>::inst
_BSS	ENDS
;	COMDAT ?inst@?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@1VtheManagerJoinContext@2@A
_BSS	SEGMENT
?inst@?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@1VtheManagerJoinContext@2@A DB 040H DUP (?) ; mu2::ISingleton<mu2::theManagerJoinContext>::inst
_BSS	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0exception@std@@QEAA@AEBV01@@Z DD imagerel $LN4
	DD	imagerel $LN4+89
	DD	imagerel $unwind$??0exception@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?what@exception@std@@UEBAPEBDXZ DD imagerel $LN5
	DD	imagerel $LN5+56
	DD	imagerel $unwind$?what@exception@std@@UEBAPEBDXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gexception@std@@UEAAPEAXI@Z DD imagerel $LN10
	DD	imagerel $LN10+83
	DD	imagerel $unwind$??_Gexception@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z DD imagerel $LN9
	DD	imagerel $LN9+104
	DD	imagerel $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+83
	DD	imagerel $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_array_new_length@std@@QEAA@XZ DD imagerel $LN14
	DD	imagerel $LN14+95
	DD	imagerel $unwind$??0bad_array_new_length@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1bad_array_new_length@std@@UEAA@XZ DD imagerel $LN14
	DD	imagerel $LN14+47
	DD	imagerel $unwind$??1bad_array_new_length@std@@UEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z DD imagerel $LN14
	DD	imagerel $LN14+54
	DD	imagerel $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z DD imagerel $LN20
	DD	imagerel $LN20+83
	DD	imagerel $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Throw_bad_array_new_length@std@@YAXXZ DD imagerel $LN3
	DD	imagerel $LN3+37
	DD	imagerel $unwind$?_Throw_bad_array_new_length@std@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z DD imagerel $LN5
	DD	imagerel $LN5+159
	DD	imagerel $unwind$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ DD imagerel $LN82
	DD	imagerel $LN82+25
	DD	imagerel $unwind$??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?c_str@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBAPEBDXZ DD imagerel $LN22
	DD	imagerel $LN22+131
	DD	imagerel $unwind$?c_str@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBAPEBDXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ DD imagerel $LN62
	DD	imagerel $LN62+257
	DD	imagerel $unwind$?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Decref@_Ref_count_base@std@@QEAAXXZ DD imagerel $LN11
	DD	imagerel $LN11+98
	DD	imagerel $unwind$?_Decref@_Ref_count_base@std@@QEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Write@PacketStream@mu2@@QEAAIPEBEI@Z DD imagerel $LN10
	DD	imagerel $LN10+426
	DD	imagerel $unwind$?Write@PacketStream@mu2@@QEAAIPEBEI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Read@PacketStream@mu2@@QEAAIPEAEI@Z DD imagerel $LN10
	DD	imagerel $LN10+602
	DD	imagerel $unwind$?Read@PacketStream@mu2@@QEAAIPEAEI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$rw@VVector3@mu2@@@PacketStream@mu2@@QEAA_NAEAVVector3@1@@Z DD imagerel $LN93
	DD	imagerel $LN93+210
	DD	imagerel $unwind$??$rw@VVector3@mu2@@@PacketStream@mu2@@QEAA_NAEAVVector3@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$rw@H@PacketStream@mu2@@QEAA_NAEAH@Z DD imagerel $LN47
	DD	imagerel $LN47+232
	DD	imagerel $unwind$??$rw@H@PacketStream@mu2@@QEAA_NAEAH@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$rw@M@PacketStream@mu2@@QEAA_NAEAM@Z DD imagerel $LN47
	DD	imagerel $LN47+236
	DD	imagerel $unwind$??$rw@M@PacketStream@mu2@@QEAA_NAEAM@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?checkingBuffer@PacketStream@mu2@@AEAA_NXZ DD imagerel $LN5
	DD	imagerel $LN5+78
	DD	imagerel $unwind$?checkingBuffer@PacketStream@mu2@@AEAA_NXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$swapEndian@H@PacketStream@mu2@@AEAAHAEBH@Z DD imagerel $LN6
	DD	imagerel $LN6+100
	DD	imagerel $unwind$??$swapEndian@H@PacketStream@mu2@@AEAAHAEBH@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$swapEndian@M@PacketStream@mu2@@AEAAMAEBM@Z DD imagerel $LN6
	DD	imagerel $LN6+110
	DD	imagerel $unwind$??$swapEndian@M@PacketStream@mu2@@AEAAMAEBM@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GISerializer@mu2@@UEAAPEAXI@Z DD imagerel $LN10
	DD	imagerel $LN10+65
	DD	imagerel $unwind$??_GISerializer@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?allocate@?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@2@_K@Z DD imagerel $LN13
	DD	imagerel $LN13+160
	DD	imagerel $unwind$?allocate@?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@2@_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAA@XZ DD imagerel $LN128
	DD	imagerel $LN128+83
	DD	imagerel $unwind$??1?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??4?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAAEAV01@AEBV01@@Z DD imagerel $LN304
	DD	imagerel $LN304+194
	DD	imagerel $unwind$??4?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAAEAV01@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?clear@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAXXZ DD imagerel $LN88
	DD	imagerel $LN88+158
	DD	imagerel $unwind$?clear@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Alloc_sentinel_and_proxy@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAXXZ DD imagerel $LN107
	DD	imagerel $LN107+114
	DD	imagerel $unwind$?_Alloc_sentinel_and_proxy@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0?$map@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ DD imagerel $LN142
	DD	imagerel $LN142+107
	DD	imagerel $unwind$??0?$map@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$map@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ DD imagerel $LN133
	DD	imagerel $LN133+25
	DD	imagerel $unwind$??1?$map@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$rw@VExecutionZoneId@mu2@@@PacketStream@mu2@@QEAA_NAEAVExecutionZoneId@1@@Z DD imagerel $LN4
	DD	imagerel $LN4+60
	DD	imagerel $unwind$??$rw@VExecutionZoneId@mu2@@@PacketStream@mu2@@QEAA_NAEAVExecutionZoneId@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Reset@ExecLocation@mu2@@UEAAXXZ DD imagerel $LN5
	DD	imagerel $LN5+95
	DD	imagerel $unwind$?Reset@ExecLocation@mu2@@UEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?PackUnpack@ExecLocation@mu2@@UEAA_NAEAVPacketStream@2@@Z DD imagerel $LN114
	DD	imagerel $LN114+95
	DD	imagerel $unwind$?PackUnpack@ExecLocation@mu2@@UEAA_NAEAVPacketStream@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0ExecLocation@mu2@@QEAA@XZ DD imagerel $LN16
	DD	imagerel $LN16+118
	DD	imagerel $unwind$??0ExecLocation@mu2@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???0ExecLocation@mu2@@QEAA@XZ@4HA DD imagerel ?dtor$0@?0???0ExecLocation@mu2@@QEAA@XZ@4HA
	DD	imagerel ?dtor$0@?0???0ExecLocation@mu2@@QEAA@XZ@4HA+24
	DD	imagerel $unwind$?dtor$0@?0???0ExecLocation@mu2@@QEAA@XZ@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GExecLocation@mu2@@UEAAPEAXI@Z DD imagerel $LN35
	DD	imagerel $LN35+81
	DD	imagerel $unwind$??_GExecLocation@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$SmartPtrEx@UEvent@mu2@@@mu2@@QEAA@XZ DD imagerel $LN27
	DD	imagerel $LN27+51
	DD	imagerel $unwind$??1?$SmartPtrEx@UEvent@mu2@@@mu2@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??4?$SmartPtrEx@UEvent@mu2@@@mu2@@QEAAAEAV01@AEBV01@@Z DD imagerel $LN107
	DD	imagerel $LN107+92
	DD	imagerel $unwind$??4?$SmartPtrEx@UEvent@mu2@@@mu2@@QEAAAEAV01@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Swap@?$_Ptr_base@UEvent@mu2@@@std@@IEAAXAEAV12@@Z DD imagerel $LN44
	DD	imagerel $LN44+214
	DD	imagerel $unwind$?_Swap@?$_Ptr_base@UEvent@mu2@@@std@@IEAAXAEAV12@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0?$shared_ptr@UEvent@mu2@@@std@@QEAA@AEBV01@@Z DD imagerel $LN25
	DD	imagerel $LN25+119
	DD	imagerel $unwind$??0?$shared_ptr@UEvent@mu2@@@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??4?$shared_ptr@UEvent@mu2@@@std@@QEAAAEAV01@AEBV01@@Z DD imagerel $LN103
	DD	imagerel $LN103+155
	DD	imagerel $unwind$??4?$shared_ptr@UEvent@mu2@@@std@@QEAAAEAV01@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@@Z DD imagerel $LN58
	DD	imagerel $LN58+219
	DD	imagerel $unwind$??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@@Z DD imagerel $LN103
	DD	imagerel $LN103+126
	DD	imagerel $unwind$??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z DD imagerel $LN37
	DD	imagerel $LN37+320
	DD	imagerel $unwind$??$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z@4HA DD imagerel ?dtor$0@?0???$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z@4HA
	DD	imagerel ?dtor$0@?0???$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z@4HA+24
	DD	imagerel $unwind$?dtor$0@?0???$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@std@@QEAA@XZ DD imagerel $LN20
	DD	imagerel $LN20+120
	DD	imagerel $unwind$??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$_Temporary_owner@UEvent@mu2@@@std@@QEAA@XZ DD imagerel $LN6
	DD	imagerel $LN6+71
	DD	imagerel $unwind$??1?$_Temporary_owner@UEvent@mu2@@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Destroy@?$_Ref_count@UEvent@mu2@@@std@@EEAAXXZ DD imagerel $LN6
	DD	imagerel $LN6+72
	DD	imagerel $unwind$?_Destroy@?$_Ref_count@UEvent@mu2@@@std@@EEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Delete_this@?$_Ref_count@UEvent@mu2@@@std@@EEAAXXZ DD imagerel $LN6
	DD	imagerel $LN6+69
	DD	imagerel $unwind$?_Delete_this@?$_Ref_count@UEvent@mu2@@@std@@EEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_G?$_Ref_count@UEvent@mu2@@@std@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+50
	DD	imagerel $unwind$??_G?$_Ref_count@UEvent@mu2@@@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z DD imagerel $LN7
	DD	imagerel $LN7+150
	DD	imagerel $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Buyheadnode@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@@Z DD imagerel $LN75
	DD	imagerel $LN75+296
	DD	imagerel $unwind$??$_Buyheadnode@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_G?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@UEAAPEAXI@Z DD imagerel $LN10
	DD	imagerel $LN10+65
	DD	imagerel $unwind$??_G?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GtheManagerJoinContext@mu2@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+80
	DD	imagerel $unwind$??_GtheManagerJoinContext@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z DD imagerel $LN228
	DD	imagerel $LN228+256
	DD	imagerel $unwind$??0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z@4HA DD imagerel ?dtor$0@?0???0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z@4HA
	DD	imagerel ?dtor$0@?0???0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z@4HA+24
	DD	imagerel $unwind$?dtor$0@?0???0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$1@?0???0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z@4HA DD imagerel ?dtor$1@?0???0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z@4HA
	DD	imagerel ?dtor$1@?0???0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z@4HA+28
	DD	imagerel $unwind$?dtor$1@?0???0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$2@?0???0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z@4HA DD imagerel ?dtor$2@?0???0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z@4HA
	DD	imagerel ?dtor$2@?0???0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z@4HA+28
	DD	imagerel $unwind$?dtor$2@?0???0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$3@?0???0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z@4HA DD imagerel ?dtor$3@?0???0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z@4HA
	DD	imagerel ?dtor$3@?0???0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z@4HA+28
	DD	imagerel $unwind$?dtor$3@?0???0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1PlayerJoinAction@mu2@@UEAA@XZ DD imagerel $LN196
	DD	imagerel $LN196+144
	DD	imagerel $unwind$??1PlayerJoinAction@mu2@@UEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Reset@PlayerJoinAction@mu2@@UEAAXXZ DD imagerel $LN268
	DD	imagerel $LN268+226
	DD	imagerel $unwind$?Reset@PlayerJoinAction@mu2@@UEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?ResetInReady@PlayerJoinAction@mu2@@QEAAXXZ DD imagerel $LN13
	DD	imagerel $LN13+136
	DD	imagerel $unwind$?ResetInReady@PlayerJoinAction@mu2@@QEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?TranStateJoinExecution@PlayerJoinAction@mu2@@QEAA?AW4Error@ErrorJoin@2@W4Enum@JoinContext@2@AEBVExecutionZoneId@2@HAEBVVector3@2@PEBUInstanceDungeonInfo@2@@Z DD imagerel $LN13
	DD	imagerel $LN13+957
	DD	imagerel $unwind$?TranStateJoinExecution@PlayerJoinAction@mu2@@QEAA?AW4Error@ErrorJoin@2@W4Enum@JoinContext@2@AEBVExecutionZoneId@2@HAEBVVector3@2@PEBUInstanceDungeonInfo@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?GetJoinContext@PlayerJoinAction@mu2@@QEBA?BW4Enum@JoinContext@2@XZ DD imagerel $LN4
	DD	imagerel $LN4+220
	DD	imagerel $unwind$?GetJoinContext@PlayerJoinAction@mu2@@QEBA?BW4Enum@JoinContext@2@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?GetToExecution@PlayerJoinAction@mu2@@QEAAAEBVExecution@2@XZ DD imagerel $LN5
	DD	imagerel $LN5+48
	DD	imagerel $unwind$?GetToExecution@PlayerJoinAction@mu2@@QEAAAEBVExecution@2@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?IsEnterableJoinState@PlayerJoinAction@mu2@@QEBA_NXZ DD imagerel $LN121
	DD	imagerel $LN121+742
	DD	imagerel $unwind$?IsEnterableJoinState@PlayerJoinAction@mu2@@QEBA_NXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0??IsEnterableJoinState@PlayerJoinAction@mu2@@QEBA_NXZ@4HA DD imagerel ?dtor$0@?0??IsEnterableJoinState@PlayerJoinAction@mu2@@QEBA_NXZ@4HA
	DD	imagerel ?dtor$0@?0??IsEnterableJoinState@PlayerJoinAction@mu2@@QEBA_NXZ@4HA+27
	DD	imagerel $unwind$?dtor$0@?0??IsEnterableJoinState@PlayerJoinAction@mu2@@QEBA_NXZ@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?SetELoopbackWorldLogout@PlayerJoinAction@mu2@@QEAAXAEBV?$SmartPtrEx@UEvent@mu2@@@2@@Z DD imagerel $LN109
	DD	imagerel $LN109+42
	DD	imagerel $unwind$?SetELoopbackWorldLogout@PlayerJoinAction@mu2@@QEAAXAEBV?$SmartPtrEx@UEvent@mu2@@@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?GetELoopbackWorldLogout@PlayerJoinAction@mu2@@QEAA?AV?$SmartPtrEx@UEvent@mu2@@@2@XZ DD imagerel $LN123
	DD	imagerel $LN123+101
	DD	imagerel $unwind$?GetELoopbackWorldLogout@PlayerJoinAction@mu2@@QEAA?AV?$SmartPtrEx@UEvent@mu2@@@2@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?IsWorldLogout@PlayerJoinAction@mu2@@QEBA_NXZ DD imagerel $LN12
	DD	imagerel $LN12+68
	DD	imagerel $unwind$?IsWorldLogout@PlayerJoinAction@mu2@@QEBA_NXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?SetJoinContext@PlayerJoinAction@mu2@@QEAAXW4Enum@JoinContext@2@@Z DD imagerel $LN7
	DD	imagerel $LN7+415
	DD	imagerel $unwind$?SetJoinContext@PlayerJoinAction@mu2@@QEAAXW4Enum@JoinContext@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?SetTalismanSkillInfo@PlayerJoinAction@mu2@@QEAAXAEBV?$map@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@I@Z DD imagerel $LN324
	DD	imagerel $LN324+110
	DD	imagerel $unwind$?SetTalismanSkillInfo@PlayerJoinAction@mu2@@QEAAXAEBV?$map@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@I@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?GetTalismanSkillInfo@PlayerJoinAction@mu2@@QEAAXAEBVJoinZoneContextCurr@2@AEBVJoinZoneContextDest@2@AEAV?$map@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@AEAI@Z DD imagerel $LN410
	DD	imagerel $LN410+173
	DD	imagerel $unwind$?GetTalismanSkillInfo@PlayerJoinAction@mu2@@QEAAXAEBVJoinZoneContextCurr@2@AEBVJoinZoneContextDest@2@AEAV?$map@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@AEAI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?IsMultistageDungeon@PlayerJoinAction@mu2@@QEBA_NAEBVJoinZoneContextCurr@2@AEBVJoinZoneContextDest@2@@Z DD imagerel $LN12
	DD	imagerel $LN12+373
	DD	imagerel $unwind$?IsMultistageDungeon@PlayerJoinAction@mu2@@QEBA_NAEBVJoinZoneContextCurr@2@AEBVJoinZoneContextDest@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?InitContext@PlayerJoinAction@mu2@@AEAAXW4Enum@JoinContext@2@AEBVExecutionZoneId@2@HAEBVVector3@2@PEBUInstanceDungeonInfo@2@@Z DD imagerel $LN11
	DD	imagerel $LN11+616
	DD	imagerel $unwind$?InitContext@PlayerJoinAction@mu2@@AEAAXW4Enum@JoinContext@2@AEBVExecutionZoneId@2@HAEBVVector3@2@PEBUInstanceDungeonInfo@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?SetToExecId@PlayerJoinAction@mu2@@AEAAXAEBVExecutionZoneId@2@@Z DD imagerel $LN21
	DD	imagerel $LN21+193
	DD	imagerel $unwind$?SetToExecId@PlayerJoinAction@mu2@@AEAAXAEBVExecutionZoneId@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?IsValid@PlayerJoinAction@mu2@@AEBA_NXZ DD imagerel $LN4
	DD	imagerel $LN4+53
	DD	imagerel $unwind$?IsValid@PlayerJoinAction@mu2@@AEBA_NXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GPlayerJoinAction@mu2@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+84
	DD	imagerel $unwind$??_GPlayerJoinAction@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__E?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ DD imagerel ??__E?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ
	DD	imagerel ??__E?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ+34
	DD	imagerel $unwind$??__E?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__F?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ DD imagerel ??__F?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ
	DD	imagerel ??__F?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ+22
	DD	imagerel $unwind$??__F?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__E?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ DD imagerel ??__E?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ
	DD	imagerel ??__E?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ+34
	DD	imagerel $unwind$??__E?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__F?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ DD imagerel ??__F?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ
	DD	imagerel ??__F?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ+22
	DD	imagerel $unwind$??__F?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__E?inst@?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@1VtheManagerJoinContext@2@A@@YAXXZ DD imagerel ??__E?inst@?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@1VtheManagerJoinContext@2@A@@YAXXZ
	DD	imagerel ??__E?inst@?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@1VtheManagerJoinContext@2@A@@YAXXZ+53
	DD	imagerel $unwind$??__E?inst@?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@1VtheManagerJoinContext@2@A@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Copy@$0A@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAXAEBV01@@Z DD imagerel $LN162
	DD	imagerel $LN162+364
	DD	imagerel $unwind$??$_Copy@$0A@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAXAEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Copy_nodes@$0A@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@PEAU21@0@Z DD imagerel $LN256
	DD	imagerel $LN256+342
	DD	imagerel $unwind$??$_Copy_nodes@$0A@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@PEAU21@0@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???$_Copy_nodes@$0A@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@PEAU21@0@Z@4HA DD imagerel ?dtor$0@?0???$_Copy_nodes@$0A@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@PEAU21@0@Z@4HA
	DD	imagerel ?dtor$0@?0???$_Copy_nodes@$0A@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@PEAU21@0@Z@4HA+24
	DD	imagerel $unwind$?dtor$0@?0???$_Copy_nodes@$0A@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@PEAU21@0@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$_Erase_tree_and_orphan_guard@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAA@XZ DD imagerel $LN69
	DD	imagerel $LN69+57
	DD	imagerel $unwind$??1?$_Erase_tree_and_orphan_guard@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Buynode@AEAU?$pair@$$CBII@std@@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@AEAU?$pair@$$CBII@1@@Z DD imagerel $LN160
	DD	imagerel $LN160+95
	DD	imagerel $unwind$??$_Buynode@AEAU?$pair@$$CBII@std@@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@AEAU?$pair@$$CBII@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Erase_tree_and_orphan@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@@Z DD imagerel $LN63
	DD	imagerel $LN63+219
	DD	imagerel $unwind$??$_Erase_tree_and_orphan@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Buynode@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@AEAU?$pair@$$CBII@2@@?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU01@AEAU?$pair@$$CBII@1@@Z DD imagerel $LN133
	DD	imagerel $LN133+589
	DD	imagerel $unwind$??$_Buynode@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@AEAU?$pair@$$CBII@2@@?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU01@AEAU?$pair@$$CBII@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???$_Buynode@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@AEAU?$pair@$$CBII@2@@?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU01@AEAU?$pair@$$CBII@1@@Z@4HA DD imagerel ?dtor$0@?0???$_Buynode@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@AEAU?$pair@$$CBII@2@@?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU01@AEAU?$pair@$$CBII@1@@Z@4HA
	DD	imagerel ?dtor$0@?0???$_Buynode@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@AEAU?$pair@$$CBII@2@@?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU01@AEAU?$pair@$$CBII@1@@Z@4HA+24
	DD	imagerel $unwind$?dtor$0@?0???$_Buynode@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@AEAU?$pair@$$CBII@2@@?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU01@AEAU?$pair@$$CBII@1@@Z@4HA
pdata	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
??inst$initializer$@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA DQ FLAT:??__E?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ ; ??inst$initializer$@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA
CRT$XCU	ENDS
;	COMDAT ??_R4?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@6B@
rdata$r	SEGMENT
??_R4?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@6B@ DD 01H ; mu2::ISingleton<mu2::theManagerJoinContext>::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AV?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@@8
	DD	imagerel ??_R3?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@8
	DD	imagerel ??_R4?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R2?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@8
rdata$r	SEGMENT
??_R2?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@8 DD imagerel ??_R1A@?0A@EA@?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@8 ; mu2::ISingleton<mu2::theManagerJoinContext>::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@8
rdata$r	SEGMENT
??_R3?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@8 DD 00H ; mu2::ISingleton<mu2::theManagerJoinContext>::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AV?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@@8
data$rs	SEGMENT
??_R0?AV?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::ISingleton<mu2::theManagerJoinContext> `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AV?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R1A@?0A@EA@?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@8 DD imagerel ??_R0?AV?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@@8 ; mu2::ISingleton<mu2::theManagerJoinContext>::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@theManagerJoinContext@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@theManagerJoinContext@mu2@@8 DD imagerel ??_R0?AVtheManagerJoinContext@mu2@@@8 ; mu2::theManagerJoinContext::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3theManagerJoinContext@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2theManagerJoinContext@mu2@@8
rdata$r	SEGMENT
??_R2theManagerJoinContext@mu2@@8 DD imagerel ??_R1A@?0A@EA@theManagerJoinContext@mu2@@8 ; mu2::theManagerJoinContext::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3theManagerJoinContext@mu2@@8
rdata$r	SEGMENT
??_R3theManagerJoinContext@mu2@@8 DD 00H		; mu2::theManagerJoinContext::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2theManagerJoinContext@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVtheManagerJoinContext@mu2@@@8
data$rs	SEGMENT
??_R0?AVtheManagerJoinContext@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::theManagerJoinContext `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVtheManagerJoinContext@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4theManagerJoinContext@mu2@@6B@
rdata$r	SEGMENT
??_R4theManagerJoinContext@mu2@@6B@ DD 01H		; mu2::theManagerJoinContext::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVtheManagerJoinContext@mu2@@@8
	DD	imagerel ??_R3theManagerJoinContext@mu2@@8
	DD	imagerel ??_R4theManagerJoinContext@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R2ActionPlayer@mu2@@8
rdata$r	SEGMENT
??_R2ActionPlayer@mu2@@8 DD imagerel ??_R1A@?0A@EA@ActionPlayer@mu2@@8 ; mu2::ActionPlayer::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@Action@mu2@@8
	DD	imagerel ??_R17?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3ActionPlayer@mu2@@8
rdata$r	SEGMENT
??_R3ActionPlayer@mu2@@8 DD 00H				; mu2::ActionPlayer::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	03H
	DD	imagerel ??_R2ActionPlayer@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVActionPlayer@mu2@@@8
data$rs	SEGMENT
??_R0?AVActionPlayer@mu2@@@8 DQ FLAT:??_7type_info@@6B@	; mu2::ActionPlayer `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVActionPlayer@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R1A@?0A@EA@ActionPlayer@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@ActionPlayer@mu2@@8 DD imagerel ??_R0?AVActionPlayer@mu2@@@8 ; mu2::ActionPlayer::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	02H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3ActionPlayer@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@PlayerJoinAction@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@PlayerJoinAction@mu2@@8 DD imagerel ??_R0?AVPlayerJoinAction@mu2@@@8 ; mu2::PlayerJoinAction::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	03H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3PlayerJoinAction@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2PlayerJoinAction@mu2@@8
rdata$r	SEGMENT
??_R2PlayerJoinAction@mu2@@8 DD imagerel ??_R1A@?0A@EA@PlayerJoinAction@mu2@@8 ; mu2::PlayerJoinAction::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@ActionPlayer@mu2@@8
	DD	imagerel ??_R1A@?0A@EA@Action@mu2@@8
	DD	imagerel ??_R17?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3PlayerJoinAction@mu2@@8
rdata$r	SEGMENT
??_R3PlayerJoinAction@mu2@@8 DD 00H			; mu2::PlayerJoinAction::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	04H
	DD	imagerel ??_R2PlayerJoinAction@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVPlayerJoinAction@mu2@@@8
data$rs	SEGMENT
??_R0?AVPlayerJoinAction@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::PlayerJoinAction `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVPlayerJoinAction@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4PlayerJoinAction@mu2@@6B@
rdata$r	SEGMENT
??_R4PlayerJoinAction@mu2@@6B@ DD 01H			; mu2::PlayerJoinAction::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVPlayerJoinAction@mu2@@@8
	DD	imagerel ??_R3PlayerJoinAction@mu2@@8
	DD	imagerel ??_R4PlayerJoinAction@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_C@_0CL@NLMJGNPH@mu2?3?3PlayerJoinAction?3?3IsMultis@
CONST	SEGMENT
??_C@_0CL@NLMJGNPH@mu2?3?3PlayerJoinAction?3?3IsMultis@ DB 'mu2::PlayerJo'
	DB	'inAction::IsMultistageDungeon', 00H		; `string'
CONST	ENDS
;	COMDAT ??_C@_0O@CDHODAOF@destWorldElem@
CONST	SEGMENT
??_C@_0O@CDHODAOF@destWorldElem@ DB 'destWorldElem', 00H ; `string'
CONST	ENDS
;	COMDAT ??_C@_0CG@DHHBHMAC@mu2?3?3PlayerJoinAction?3?3SetJoinC@
CONST	SEGMENT
??_C@_0CG@DHHBHMAC@mu2?3?3PlayerJoinAction?3?3SetJoinC@ DB 'mu2::PlayerJo'
	DB	'inAction::SetJoinContext', 00H		; `string'
CONST	ENDS
;	COMDAT ??_C@_0CG@PJGPIGCO@mu2?3?3PlayerJoinAction?3?3GetJoinC@
CONST	SEGMENT
??_C@_0CG@PJGPIGCO@mu2?3?3PlayerJoinAction?3?3GetJoinC@ DB 'mu2::PlayerJo'
	DB	'inAction::GetJoinContext', 00H		; `string'
CONST	ENDS
;	COMDAT ??_C@_0N@MDLFKPJI@m_pToContext@
CONST	SEGMENT
??_C@_0N@MDLFKPJI@m_pToContext@ DB 'm_pToContext', 00H	; `string'
CONST	ENDS
;	COMDAT ??_C@_0BH@LGPKNFKE@IsEnterableJoinState?$CI?$CJ@
CONST	SEGMENT
??_C@_0BH@LGPKNFKE@IsEnterableJoinState?$CI?$CJ@ DB 'IsEnterableJoinState'
	DB	'()', 00H					; `string'
CONST	ENDS
;	COMDAT ??_C@_0CO@LEIOINCE@mu2?3?3PlayerJoinAction?3?3TranStat@
CONST	SEGMENT
??_C@_0CO@LEIOINCE@mu2?3?3PlayerJoinAction?3?3TranStat@ DB 'mu2::PlayerJo'
	DB	'inAction::TranStateJoinExecution', 00H	; `string'
CONST	ENDS
;	COMDAT ??_C@_0DJ@DJGHFFHF@IsEnterableJoinState?$DO?5failed?5?9?5@
CONST	SEGMENT
??_C@_0DJ@DJGHFFHF@IsEnterableJoinState?$DO?5failed?5?9?5@ DB 'IsEnterabl'
	DB	'eJoinState> failed - player(%s), currState(%s)', 00H ; `string'
CONST	ENDS
;	COMDAT ??_C@_05EMNBIGOA@pCurr@
CONST	SEGMENT
??_C@_05EMNBIGOA@pCurr@ DB 'pCurr', 00H			; `string'
CONST	ENDS
;	COMDAT ??_C@_0CM@DBNLLACM@mu2?3?3PlayerJoinAction?3?3IsEntera@
CONST	SEGMENT
??_C@_0CM@DBNLLACM@mu2?3?3PlayerJoinAction?3?3IsEntera@ DB 'mu2::PlayerJo'
	DB	'inAction::IsEnterableJoinState', 00H	; `string'
CONST	ENDS
;	COMDAT ??_C@_0DH@CMCFJJFD@toExecId?4IsValid?$CI?$CJ?5?$CG?$CG?5eContext?5@
CONST	SEGMENT
??_C@_0DH@CMCFJJFD@toExecId?4IsValid?$CI?$CJ?5?$CG?$CG?5eContext?5@ DB 't'
	DB	'oExecId.IsValid() && eContext != JoinContext::JC_NONE', 00H ; `string'
CONST	ENDS
;	COMDAT ??_C@_0CD@ONJPOCCC@mu2?3?3PlayerJoinAction?3?3InitCont@
CONST	SEGMENT
??_C@_0CD@ONJPOCCC@mu2?3?3PlayerJoinAction?3?3InitCont@ DB 'mu2::PlayerJo'
	DB	'inAction::InitContext', 00H			; `string'
CONST	ENDS
;	COMDAT ??_C@_0CB@FJFBAPGB@eContext?5?$CB?$DN?5JoinContext?3?3JC_NON@
CONST	SEGMENT
??_C@_0CB@FJFBAPGB@eContext?5?$CB?$DN?5JoinContext?3?3JC_NON@ DB 'eContex'
	DB	't != JoinContext::JC_NONE', 00H		; `string'
CONST	ENDS
;	COMDAT ??_C@_0EP@HBIDHLFH@F?3?2Release_Branch?2Server?2Develo@
CONST	SEGMENT
??_C@_0EP@HBIDHLFH@F?3?2Release_Branch?2Server?2Develo@ DB 'F:\Release_Br'
	DB	'anch\Server\Development\Frontend\WorldServer\PlayerJoinAction'
	DB	'.cpp', 00H					; `string'
CONST	ENDS
;	COMDAT ??_7PlayerJoinAction@mu2@@6B@
CONST	SEGMENT
??_7PlayerJoinAction@mu2@@6B@ DQ FLAT:??_R4PlayerJoinAction@mu2@@6B@ ; mu2::PlayerJoinAction::`vftable'
	DQ	FLAT:??_EPlayerJoinAction@mu2@@UEAAPEAXI@Z
	DQ	FLAT:?OnTick@Action@mu2@@UEAAXXZ
	DQ	FLAT:?Reset@PlayerJoinAction@mu2@@UEAAXXZ
CONST	ENDS
;	COMDAT ??_7theManagerJoinContext@mu2@@6B@
CONST	SEGMENT
??_7theManagerJoinContext@mu2@@6B@ DQ FLAT:??_R4theManagerJoinContext@mu2@@6B@ ; mu2::theManagerJoinContext::`vftable'
	DQ	FLAT:?Init@theManagerJoinContext@mu2@@UEAA_NPEAX@Z
	DQ	FLAT:?UnInit@theManagerJoinContext@mu2@@UEAA_NXZ
	DQ	FLAT:??_EtheManagerJoinContext@mu2@@UEAAPEAXI@Z
CONST	ENDS
;	COMDAT ??_7?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@6B@
CONST	SEGMENT
??_7?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@6B@ DQ FLAT:??_R4?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@6B@ ; mu2::ISingleton<mu2::theManagerJoinContext>::`vftable'
	DQ	FLAT:_purecall
	DQ	FLAT:_purecall
	DQ	FLAT:??_E?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@UEAAPEAXI@Z
CONST	ENDS
;	COMDAT ??_R1A@?0A@EA@?$_Ref_count@UEvent@mu2@@@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@?$_Ref_count@UEvent@mu2@@@std@@8 DD imagerel ??_R0?AV?$_Ref_count@UEvent@mu2@@@std@@@8 ; std::_Ref_count<mu2::Event>::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3?$_Ref_count@UEvent@mu2@@@std@@8
rdata$r	ENDS
;	COMDAT ??_R2?$_Ref_count@UEvent@mu2@@@std@@8
rdata$r	SEGMENT
??_R2?$_Ref_count@UEvent@mu2@@@std@@8 DD imagerel ??_R1A@?0A@EA@?$_Ref_count@UEvent@mu2@@@std@@8 ; std::_Ref_count<mu2::Event>::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@_Ref_count_base@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3?$_Ref_count@UEvent@mu2@@@std@@8
rdata$r	SEGMENT
??_R3?$_Ref_count@UEvent@mu2@@@std@@8 DD 00H		; std::_Ref_count<mu2::Event>::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2?$_Ref_count@UEvent@mu2@@@std@@8
rdata$r	ENDS
;	COMDAT ??_R0?AV?$_Ref_count@UEvent@mu2@@@std@@@8
data$rs	SEGMENT
??_R0?AV?$_Ref_count@UEvent@mu2@@@std@@@8 DQ FLAT:??_7type_info@@6B@ ; std::_Ref_count<mu2::Event> `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AV?$_Ref_count@UEvent@mu2@@@std@@', 00H
data$rs	ENDS
;	COMDAT ??_R4?$_Ref_count@UEvent@mu2@@@std@@6B@
rdata$r	SEGMENT
??_R4?$_Ref_count@UEvent@mu2@@@std@@6B@ DD 01H		; std::_Ref_count<mu2::Event>::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AV?$_Ref_count@UEvent@mu2@@@std@@@8
	DD	imagerel ??_R3?$_Ref_count@UEvent@mu2@@@std@@8
	DD	imagerel ??_R4?$_Ref_count@UEvent@mu2@@@std@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@ExecLocation@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@ExecLocation@mu2@@8 DD imagerel ??_R0?AUExecLocation@mu2@@@8 ; mu2::ExecLocation::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3ExecLocation@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2ExecLocation@mu2@@8
rdata$r	SEGMENT
??_R2ExecLocation@mu2@@8 DD imagerel ??_R1A@?0A@EA@ExecLocation@mu2@@8 ; mu2::ExecLocation::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@ISerializer@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3ExecLocation@mu2@@8
rdata$r	SEGMENT
??_R3ExecLocation@mu2@@8 DD 00H				; mu2::ExecLocation::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2ExecLocation@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AUExecLocation@mu2@@@8
data$rs	SEGMENT
??_R0?AUExecLocation@mu2@@@8 DQ FLAT:??_7type_info@@6B@	; mu2::ExecLocation `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AUExecLocation@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4ExecLocation@mu2@@6B@
rdata$r	SEGMENT
??_R4ExecLocation@mu2@@6B@ DD 01H			; mu2::ExecLocation::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AUExecLocation@mu2@@@8
	DD	imagerel ??_R3ExecLocation@mu2@@8
	DD	imagerel ??_R4ExecLocation@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@Action@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@Action@mu2@@8 DD imagerel ??_R0?AVAction@mu2@@@8 ; mu2::Action::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3Action@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2Action@mu2@@8
rdata$r	SEGMENT
??_R2Action@mu2@@8 DD imagerel ??_R1A@?0A@EA@Action@mu2@@8 ; mu2::Action::`RTTI Base Class Array'
	DD	imagerel ??_R17?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3Action@mu2@@8
rdata$r	SEGMENT
??_R3Action@mu2@@8 DD 00H				; mu2::Action::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2Action@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVAction@mu2@@@8
data$rs	SEGMENT
??_R0?AVAction@mu2@@@8 DQ FLAT:??_7type_info@@6B@	; mu2::Action `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVAction@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R1A@?0A@EA@ISerializer@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@ISerializer@mu2@@8 DD imagerel ??_R0?AUISerializer@mu2@@@8 ; mu2::ISerializer::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3ISerializer@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2ISerializer@mu2@@8
rdata$r	SEGMENT
??_R2ISerializer@mu2@@8 DD imagerel ??_R1A@?0A@EA@ISerializer@mu2@@8 ; mu2::ISerializer::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3ISerializer@mu2@@8
rdata$r	SEGMENT
??_R3ISerializer@mu2@@8 DD 00H				; mu2::ISerializer::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2ISerializer@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AUISerializer@mu2@@@8
data$rs	SEGMENT
??_R0?AUISerializer@mu2@@@8 DQ FLAT:??_7type_info@@6B@	; mu2::ISerializer `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AUISerializer@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4ISerializer@mu2@@6B@
rdata$r	SEGMENT
??_R4ISerializer@mu2@@6B@ DD 01H			; mu2::ISerializer::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AUISerializer@mu2@@@8
	DD	imagerel ??_R3ISerializer@mu2@@8
	DD	imagerel ??_R4ISerializer@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 DD imagerel ??_R0?AV?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
rdata$r	ENDS
;	COMDAT ??_R2?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
rdata$r	SEGMENT
??_R2?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 DD imagerel ??_R1A@?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
rdata$r	SEGMENT
??_R3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 DD 00H ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
rdata$r	ENDS
;	COMDAT ??_R0?AV?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@@8
data$rs	SEGMENT
??_R0?AV?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@@8 DQ FLAT:??_7type_info@@6B@ ; AllocatedObject<mu2::CategorisedAllocPolicy<0> > `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AV?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2'
	DB	'@@@@', 00H
data$rs	ENDS
;	COMDAT ??_R17?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
rdata$r	SEGMENT
??_R17?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 DD imagerel ??_R0?AV?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Base Class Descriptor at (8,-1,0,64)'
	DD	00H
	DD	08H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@_Ref_count_base@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@_Ref_count_base@std@@8 DD imagerel ??_R0?AV_Ref_count_base@std@@@8 ; std::_Ref_count_base::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3_Ref_count_base@std@@8
rdata$r	ENDS
;	COMDAT ??_R2_Ref_count_base@std@@8
rdata$r	SEGMENT
??_R2_Ref_count_base@std@@8 DD imagerel ??_R1A@?0A@EA@_Ref_count_base@std@@8 ; std::_Ref_count_base::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3_Ref_count_base@std@@8
rdata$r	SEGMENT
??_R3_Ref_count_base@std@@8 DD 00H			; std::_Ref_count_base::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2_Ref_count_base@std@@8
rdata$r	ENDS
;	COMDAT ??_R0?AV_Ref_count_base@std@@@8
data$rs	SEGMENT
??_R0?AV_Ref_count_base@std@@@8 DQ FLAT:??_7type_info@@6B@ ; std::_Ref_count_base `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AV_Ref_count_base@std@@', 00H
data$rs	ENDS
;	COMDAT ??_R4bad_alloc@std@@6B@
rdata$r	SEGMENT
??_R4bad_alloc@std@@6B@ DD 01H				; std::bad_alloc::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVbad_alloc@std@@@8
	DD	imagerel ??_R3bad_alloc@std@@8
	DD	imagerel ??_R4bad_alloc@std@@6B@
rdata$r	ENDS
;	COMDAT ??_R2bad_alloc@std@@8
rdata$r	SEGMENT
??_R2bad_alloc@std@@8 DD imagerel ??_R1A@?0A@EA@bad_alloc@std@@8 ; std::bad_alloc::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@exception@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3bad_alloc@std@@8
rdata$r	SEGMENT
??_R3bad_alloc@std@@8 DD 00H				; std::bad_alloc::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2bad_alloc@std@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@bad_alloc@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@bad_alloc@std@@8 DD imagerel ??_R0?AVbad_alloc@std@@@8 ; std::bad_alloc::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3bad_alloc@std@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@bad_array_new_length@std@@8 DD imagerel ??_R0?AVbad_array_new_length@std@@@8 ; std::bad_array_new_length::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	02H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3bad_array_new_length@std@@8
rdata$r	ENDS
;	COMDAT ??_R2bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R2bad_array_new_length@std@@8 DD imagerel ??_R1A@?0A@EA@bad_array_new_length@std@@8 ; std::bad_array_new_length::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@bad_alloc@std@@8
	DD	imagerel ??_R1A@?0A@EA@exception@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R3bad_array_new_length@std@@8 DD 00H			; std::bad_array_new_length::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	03H
	DD	imagerel ??_R2bad_array_new_length@std@@8
rdata$r	ENDS
;	COMDAT ??_R4bad_array_new_length@std@@6B@
rdata$r	SEGMENT
??_R4bad_array_new_length@std@@6B@ DD 01H		; std::bad_array_new_length::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVbad_array_new_length@std@@@8
	DD	imagerel ??_R3bad_array_new_length@std@@8
	DD	imagerel ??_R4bad_array_new_length@std@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@exception@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@exception@std@@8 DD imagerel ??_R0?AVexception@std@@@8 ; std::exception::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3exception@std@@8
rdata$r	ENDS
;	COMDAT ??_R2exception@std@@8
rdata$r	SEGMENT
??_R2exception@std@@8 DD imagerel ??_R1A@?0A@EA@exception@std@@8 ; std::exception::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3exception@std@@8
rdata$r	SEGMENT
??_R3exception@std@@8 DD 00H				; std::exception::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2exception@std@@8
rdata$r	ENDS
;	COMDAT ??_R4exception@std@@6B@
rdata$r	SEGMENT
??_R4exception@std@@6B@ DD 01H				; std::exception::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVexception@std@@@8
	DD	imagerel ??_R3exception@std@@8
	DD	imagerel ??_R4exception@std@@6B@
rdata$r	ENDS
;	COMDAT ??_7?$_Ref_count@UEvent@mu2@@@std@@6B@
CONST	SEGMENT
??_7?$_Ref_count@UEvent@mu2@@@std@@6B@ DQ FLAT:??_R4?$_Ref_count@UEvent@mu2@@@std@@6B@ ; std::_Ref_count<mu2::Event>::`vftable'
	DQ	FLAT:?_Destroy@?$_Ref_count@UEvent@mu2@@@std@@EEAAXXZ
	DQ	FLAT:?_Delete_this@?$_Ref_count@UEvent@mu2@@@std@@EEAAXXZ
	DQ	FLAT:??_E?$_Ref_count@UEvent@mu2@@@std@@UEAAPEAXI@Z
	DQ	FLAT:?_Get_deleter@_Ref_count_base@std@@UEBAPEAXAEBVtype_info@@@Z
CONST	ENDS
;	COMDAT ??_C@_06BALNJMNP@player@
CONST	SEGMENT
??_C@_06BALNJMNP@player@ DB 'player', 00H		; `string'
CONST	ENDS
;	COMDAT ??_7ExecLocation@mu2@@6B@
CONST	SEGMENT
??_7ExecLocation@mu2@@6B@ DQ FLAT:??_R4ExecLocation@mu2@@6B@ ; mu2::ExecLocation::`vftable'
	DQ	FLAT:?PackUnpack@ExecLocation@mu2@@UEAA_NAEAVPacketStream@2@@Z
	DQ	FLAT:?Reset@ExecLocation@mu2@@UEAAXXZ
	DQ	FLAT:??_EExecLocation@mu2@@UEAAPEAXI@Z
CONST	ENDS
;	COMDAT ??_C@_09CEDEMLBJ@IsValid?$CI?$CJ@
CONST	SEGMENT
??_C@_09CEDEMLBJ@IsValid?$CI?$CJ@ DB 'IsValid()', 00H	; `string'
CONST	ENDS
;	COMDAT ??_C@_03COAJHJPB@buf@
CONST	SEGMENT
??_C@_03COAJHJPB@buf@ DB 'buf', 00H			; `string'
CONST	ENDS
;	COMDAT ??_C@_0BI@KGAODAAL@mu2?3?3PacketStream?3?3Read@
CONST	SEGMENT
??_C@_0BI@KGAODAAL@mu2?3?3PacketStream?3?3Read@ DB 'mu2::PacketStream::Re'
	DB	'ad', 00H					; `string'
CONST	ENDS
;	COMDAT ??_C@_05IOMEMJEC@count@
CONST	SEGMENT
??_C@_05IOMEMJEC@count@ DB 'count', 00H			; `string'
CONST	ENDS
;	COMDAT ??_C@_0BJ@NEGBBJOE@mu2?3?3PacketStream?3?3Write@
CONST	SEGMENT
??_C@_0BJ@NEGBBJOE@mu2?3?3PacketStream?3?3Write@ DB 'mu2::PacketStream::W'
	DB	'rite', 00H					; `string'
CONST	ENDS
;	COMDAT ??_C@_0BB@HAIDOJLN@checkingBuffer?$CI?$CJ@
CONST	SEGMENT
??_C@_0BB@HAIDOJLN@checkingBuffer?$CI?$CJ@ DB 'checkingBuffer()', 00H ; `string'
CONST	ENDS
;	COMDAT ??_C@_0EJ@IFEHPKPB@F?3?2Release_Branch?2Server?2Develo@
CONST	SEGMENT
??_C@_0EJ@IFEHPKPB@F?3?2Release_Branch?2Server?2Develo@ DB 'F:\Release_Br'
	DB	'anch\Server\Development\Framework\Net\Common\PacketStream.h', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7ISerializer@mu2@@6B@
CONST	SEGMENT
??_7ISerializer@mu2@@6B@ DQ FLAT:??_R4ISerializer@mu2@@6B@ ; mu2::ISerializer::`vftable'
	DQ	FLAT:_purecall
	DQ	FLAT:_purecall
	DQ	FLAT:??_EISerializer@mu2@@UEAAPEAXI@Z
CONST	ENDS
;	COMDAT ??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
CONST	SEGMENT
??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@ DB 'g', 00H, 'a', 00H, 'm', 00H, 'e'
	DB	00H, 00H, 00H				; `string'
CONST	ENDS
;	COMDAT ??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
CONST	SEGMENT
??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@ DB '%'
	DB	's> ASSERT - %s, %s(%d)', 00H		; `string'
CONST	ENDS
;	COMDAT _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 DD 010H
	DD	imagerel ??_R0?AVbad_alloc@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0bad_alloc@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVbad_alloc@std@@@8
data$r	SEGMENT
??_R0?AVbad_alloc@std@@@8 DQ FLAT:??_7type_info@@6B@	; std::bad_alloc `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVbad_alloc@std@@', 00H
data$r	ENDS
;	COMDAT _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 DD 00H
	DD	imagerel ??_R0?AVbad_array_new_length@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0bad_array_new_length@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVbad_array_new_length@std@@@8
data$r	SEGMENT
??_R0?AVbad_array_new_length@std@@@8 DQ FLAT:??_7type_info@@6B@ ; std::bad_array_new_length `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVbad_array_new_length@std@@', 00H
data$r	ENDS
;	COMDAT _CTA3?AVbad_array_new_length@std@@
xdata$x	SEGMENT
_CTA3?AVbad_array_new_length@std@@ DD 03H
	DD	imagerel _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
	DD	imagerel _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
	DD	imagerel _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
xdata$x	ENDS
;	COMDAT _TI3?AVbad_array_new_length@std@@
xdata$x	SEGMENT
_TI3?AVbad_array_new_length@std@@ DD 00H
	DD	imagerel ??1bad_array_new_length@std@@UEAA@XZ
	DD	00H
	DD	imagerel _CTA3?AVbad_array_new_length@std@@
xdata$x	ENDS
;	COMDAT _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 DD 00H
	DD	imagerel ??_R0?AVexception@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0exception@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVexception@std@@@8
data$r	SEGMENT
??_R0?AVexception@std@@@8 DQ FLAT:??_7type_info@@6B@	; std::exception `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVexception@std@@', 00H
data$r	ENDS
;	COMDAT ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@
CONST	SEGMENT
??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ DB 'bad array new length', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7bad_array_new_length@std@@6B@
CONST	SEGMENT
??_7bad_array_new_length@std@@6B@ DQ FLAT:??_R4bad_array_new_length@std@@6B@ ; std::bad_array_new_length::`vftable'
	DQ	FLAT:??_Ebad_array_new_length@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT ??_7bad_alloc@std@@6B@
CONST	SEGMENT
??_7bad_alloc@std@@6B@ DQ FLAT:??_R4bad_alloc@std@@6B@	; std::bad_alloc::`vftable'
	DQ	FLAT:??_Ebad_alloc@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT ??_C@_0BC@EOODALEL@Unknown?5exception@
CONST	SEGMENT
??_C@_0BC@EOODALEL@Unknown?5exception@ DB 'Unknown exception', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7exception@std@@6B@
CONST	SEGMENT
??_7exception@std@@6B@ DQ FLAT:??_R4exception@std@@6B@	; std::exception::`vftable'
	DQ	FLAT:??_Eexception@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???$_Buynode@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@AEAU?$pair@$$CBII@2@@?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU01@AEAU?$pair@$$CBII@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$_Buynode@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@AEAU?$pair@$$CBII@2@@?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU01@AEAU?$pair@$$CBII@1@@Z DB 06H
	DB	00H
	DB	00H
	DB	'X'
	DB	02H
	DB	01dH, 08H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$_Buynode@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@AEAU?$pair@$$CBII@2@@?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU01@AEAU?$pair@$$CBII@1@@Z DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???$_Buynode@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@AEAU?$pair@$$CBII@2@@?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU01@AEAU?$pair@$$CBII@1@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$_Buynode@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@AEAU?$pair@$$CBII@2@@?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU01@AEAU?$pair@$$CBII@1@@Z DB 028H
	DD	imagerel $stateUnwindMap$??$_Buynode@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@AEAU?$pair@$$CBII@2@@?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU01@AEAU?$pair@$$CBII@1@@Z
	DD	imagerel $ip2state$??$_Buynode@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@AEAU?$pair@$$CBII@2@@?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU01@AEAU?$pair@$$CBII@1@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Buynode@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@AEAU?$pair@$$CBII@2@@?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU01@AEAU?$pair@$$CBII@1@@Z DD 021611H
	DD	0230116H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$_Buynode@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@AEAU?$pair@$$CBII@2@@?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU01@AEAU?$pair@$$CBII@1@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$_Erase_tree_and_orphan@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@@Z DB 06H
	DB	00H
	DB	00H
	DB	099H, 02H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$_Erase_tree_and_orphan@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@@Z DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$_Erase_tree_and_orphan@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@@Z DB 068H
	DD	imagerel $stateUnwindMap$??$_Erase_tree_and_orphan@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@@Z
	DD	imagerel $ip2state$??$_Erase_tree_and_orphan@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Erase_tree_and_orphan@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@@Z DD 011319H
	DD	0c213H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$_Erase_tree_and_orphan@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Buynode@AEAU?$pair@$$CBII@std@@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@AEAU?$pair@$$CBII@1@@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$_Erase_tree_and_orphan_guard@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???$_Copy_nodes@$0A@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@PEAU21@0@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$_Copy_nodes@$0A@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@PEAU21@0@Z DB 06H
	DB	00H
	DB	00H
	DB	'i', 03H
	DB	02H
	DB	0aaH
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$_Copy_nodes@$0A@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@PEAU21@0@Z DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???$_Copy_nodes@$0A@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@PEAU21@0@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$_Copy_nodes@$0A@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@PEAU21@0@Z DB 028H
	DD	imagerel $stateUnwindMap$??$_Copy_nodes@$0A@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@PEAU21@0@Z
	DD	imagerel $ip2state$??$_Copy_nodes@$0A@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@PEAU21@0@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Copy_nodes@$0A@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@PEAU21@0@Z DD 011311H
	DD	0e213H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$_Copy_nodes@$0A@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@PEAU21@0@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Copy@$0A@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAXAEBV01@@Z DD 010e01H
	DD	0e20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__E?inst@?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@1VtheManagerJoinContext@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__F?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__E?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__F?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__E?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GPlayerJoinAction@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?IsValid@PlayerJoinAction@mu2@@AEBA_NXZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?SetToExecId@PlayerJoinAction@mu2@@AEAAXAEBVExecutionZoneId@2@@Z DD 010e01H
	DD	0820eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?InitContext@PlayerJoinAction@mu2@@AEAAXW4Enum@JoinContext@2@AEBVExecutionZoneId@2@HAEBVVector3@2@PEBUInstanceDungeonInfo@2@@Z DD 021a01H
	DD	011011aH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?IsMultistageDungeon@PlayerJoinAction@mu2@@QEBA_NAEBVJoinZoneContextCurr@2@AEBVJoinZoneContextDest@2@@Z DD 021601H
	DD	0130116H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?GetTalismanSkillInfo@PlayerJoinAction@mu2@@QEAAXAEBVJoinZoneContextCurr@2@AEBVJoinZoneContextDest@2@AEAV?$map@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@AEAI@Z DD 011801H
	DD	08218H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?SetTalismanSkillInfo@PlayerJoinAction@mu2@@QEAAXAEBV?$map@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@I@Z DD 011301H
	DD	08213H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?SetJoinContext@PlayerJoinAction@mu2@@QEAAXW4Enum@JoinContext@2@@Z DD 021001H
	DD	0110110H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?IsWorldLogout@PlayerJoinAction@mu2@@QEBA_NXZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?GetELoopbackWorldLogout@PlayerJoinAction@mu2@@QEAA?AV?$SmartPtrEx@UEvent@mu2@@@2@XZ DD 010e01H
	DD	0620eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?SetELoopbackWorldLogout@PlayerJoinAction@mu2@@QEAAXAEBV?$SmartPtrEx@UEvent@mu2@@@2@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DW	016H
	DW	02ceH
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0??IsEnterableJoinState@PlayerJoinAction@mu2@@QEBA_NXZ@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?IsEnterableJoinState@PlayerJoinAction@mu2@@QEBA_NXZ DB 06H
	DB	00H
	DB	00H
	DB	0a1H, 08H
	DB	02H
	DB	0feH
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?IsEnterableJoinState@PlayerJoinAction@mu2@@QEBA_NXZ DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0??IsEnterableJoinState@PlayerJoinAction@mu2@@QEBA_NXZ@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?IsEnterableJoinState@PlayerJoinAction@mu2@@QEBA_NXZ DB 028H
	DD	imagerel $stateUnwindMap$?IsEnterableJoinState@PlayerJoinAction@mu2@@QEBA_NXZ
	DD	imagerel $ip2state$?IsEnterableJoinState@PlayerJoinAction@mu2@@QEBA_NXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?IsEnterableJoinState@PlayerJoinAction@mu2@@QEBA_NXZ DD 021e19H
	DD	01f010cH
	DD	imagerel __GSHandlerCheck_EH4
	DD	imagerel $cppxdata$?IsEnterableJoinState@PlayerJoinAction@mu2@@QEBA_NXZ
	DD	0e2H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?GetToExecution@PlayerJoinAction@mu2@@QEAAAEBVExecution@2@XZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?GetJoinContext@PlayerJoinAction@mu2@@QEBA?BW4Enum@JoinContext@2@XZ DD 010901H
	DD	0e209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?TranStateJoinExecution@PlayerJoinAction@mu2@@QEAA?AW4Error@ErrorJoin@2@W4Enum@JoinContext@2@AEBVExecutionZoneId@2@HAEBVVector3@2@PEBUInstanceDungeonInfo@2@@Z DD 021a01H
	DD	015011aH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?ResetInReady@PlayerJoinAction@mu2@@QEAAXXZ DD 010901H
	DD	08209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Reset@PlayerJoinAction@mu2@@UEAAXXZ DD 010901H
	DD	0a209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1PlayerJoinAction@mu2@@UEAA@XZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$3@?0???0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$2@?0???0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$1@?0???0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z DB 0cH
	DB	00H
	DB	00H
	DB	'J'
	DB	02H
	DB	086H
	DB	04H
	DB	'`'
	DB	06H
	DB	'6'
	DB	08H
	DB	084H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z DB 08H
	DB	0eH
	DD	imagerel ?dtor$0@?0???0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z@4HA
	DB	02eH
	DD	imagerel ?dtor$1@?0???0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z@4HA
	DB	02eH
	DD	imagerel ?dtor$2@?0???0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z@4HA
	DB	02eH
	DD	imagerel ?dtor$3@?0???0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z DB 028H
	DD	imagerel $stateUnwindMap$??0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z
	DD	imagerel $ip2state$??0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z DD 020f11H
	DD	0700b520fH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GtheManagerJoinContext@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_G?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Buyheadnode@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@@Z DD 020c01H
	DD	015010cH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z DD 010901H
	DD	0a209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_G?$_Ref_count@UEvent@mu2@@@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Delete_this@?$_Ref_count@UEvent@mu2@@@std@@EEAAXXZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Destroy@?$_Ref_count@UEvent@mu2@@@std@@EEAAXXZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$_Temporary_owner@UEvent@mu2@@@std@@QEAA@XZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@std@@QEAA@XZ DB 06H
	DB	00H
	DB	00H
	DB	090H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@std@@QEAA@XZ DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@std@@QEAA@XZ DB 068H
	DD	imagerel $stateUnwindMap$??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@std@@QEAA@XZ
	DD	imagerel $ip2state$??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@std@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@std@@QEAA@XZ DD 010919H
	DD	08209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@std@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z DB 06H
	DB	00H
	DB	00H
	DB	'v'
	DB	02H
	DB	05H, 03H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z DB 028H
	DD	imagerel $stateUnwindMap$??$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z
	DD	imagerel $ip2state$??$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z DD 020f11H
	DD	0700bd20fH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@@Z DB 06H
	DB	00H
	DB	00H
	DB	09cH
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@@Z DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@@Z DB 068H
	DD	imagerel $stateUnwindMap$??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@@Z
	DD	imagerel $ip2state$??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@@Z DD 010e19H
	DD	0820eH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@@Z DB 06H
	DB	00H
	DB	00H
	DB	099H, 02H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@@Z DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@@Z DB 068H
	DD	imagerel $stateUnwindMap$??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@@Z
	DD	imagerel $ip2state$??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@@Z DD 011319H
	DD	0c213H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@@Z
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DB	043H
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??4?$shared_ptr@UEvent@mu2@@@std@@QEAAAEAV01@AEBV01@@Z DD 010e01H
	DD	0820eH
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DB	048H
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0?$shared_ptr@UEvent@mu2@@@std@@QEAA@AEBV01@@Z DD 010e01H
	DD	0220eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Swap@?$_Ptr_base@UEvent@mu2@@@std@@IEAAXAEAV12@@Z DD 010e01H
	DD	0c20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??4?$SmartPtrEx@UEvent@mu2@@@mu2@@QEAAAEAV01@AEBV01@@Z DD 010e01H
	DD	0820eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$SmartPtrEx@UEvent@mu2@@@mu2@@QEAA@XZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GExecLocation@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???0ExecLocation@mu2@@QEAA@XZ@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??0ExecLocation@mu2@@QEAA@XZ DB 06H
	DB	00H
	DB	00H
	DB	'0'
	DB	02H
	DB	0a8H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??0ExecLocation@mu2@@QEAA@XZ DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???0ExecLocation@mu2@@QEAA@XZ@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??0ExecLocation@mu2@@QEAA@XZ DB 028H
	DD	imagerel $stateUnwindMap$??0ExecLocation@mu2@@QEAA@XZ
	DD	imagerel $ip2state$??0ExecLocation@mu2@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0ExecLocation@mu2@@QEAA@XZ DD 010911H
	DD	06209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??0ExecLocation@mu2@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?PackUnpack@ExecLocation@mu2@@UEAA_NAEAVPacketStream@2@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Reset@ExecLocation@mu2@@UEAAXXZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$rw@VExecutionZoneId@mu2@@@PacketStream@mu2@@QEAA_NAEAVExecutionZoneId@1@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$map@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0?$map@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ DD 010901H
	DD	0a209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Alloc_sentinel_and_proxy@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAXXZ DD 020a01H
	DD	07006b20aH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?clear@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAXXZ DD 010901H
	DD	0a209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??4?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAAEAV01@AEBV01@@Z DD 010e01H
	DD	0e20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAA@XZ DD 010901H
	DD	0a209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?allocate@?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@2@_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GISerializer@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$swapEndian@M@PacketStream@mu2@@AEAAMAEBM@Z DD 010e01H
	DD	0220eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$swapEndian@H@PacketStream@mu2@@AEAAHAEBH@Z DD 010e01H
	DD	0220eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?checkingBuffer@PacketStream@mu2@@AEAA_NXZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$rw@M@PacketStream@mu2@@QEAA_NAEAM@Z DD 010e01H
	DD	0620eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$rw@H@PacketStream@mu2@@QEAA_NAEAH@Z DD 010e01H
	DD	0620eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$rw@VVector3@mu2@@@PacketStream@mu2@@QEAA_NAEAVVector3@1@@Z DD 010e01H
	DD	0620eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Read@PacketStream@mu2@@QEAAIPEAEI@Z DD 021601H
	DD	0110116H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Write@PacketStream@mu2@@QEAAIPEBEI@Z DD 021601H
	DD	0110116H
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DB	017H
	DB	040H
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Decref@_Ref_count_base@std@@QEAAXXZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ DB 06H
	DB	00H
	DB	00H
	DB	'q', 02H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ DB 068H
	DD	imagerel $stateUnwindMap$?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ
	DD	imagerel $ip2state$?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ DD 010919H
	DD	0e209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?c_str@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBAPEBDXZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Throw_bad_array_new_length@std@@YAXXZ DD 010401H
	DD	08204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1bad_array_new_length@std@@UEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_array_new_length@std@@QEAA@XZ DD 010601H
	DD	07006H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z DD 020f01H
	DD	0700b320fH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gexception@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?what@exception@std@@UEBAPEBDXZ DD 010901H
	DD	02209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0exception@std@@QEAA@AEBV01@@Z DD 020f01H
	DD	0700b320fH
xdata	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
??inst$initializer$@?$ISingleton@VExecutionManager@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA DQ FLAT:??__E?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ ; ??inst$initializer$@?$ISingleton@VExecutionManager@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA
CRT$XCU	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
??inst$initializer$@?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA DQ FLAT:??__E?inst@?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@1VtheManagerJoinContext@2@A@@YAXXZ ; ??inst$initializer$@?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA
CRT$XCU	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??$_Buynode@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@AEAU?$pair@$$CBII@2@@?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU01@AEAU?$pair@$$CBII@1@@Z
_TEXT	SEGMENT
_Newnode$ = 32
_Val$ = 48
_Val$ = 56
$T1 = 64
$T2 = 72
$T3 = 80
$T4 = 88
$T5 = 96
_Obj$ = 104
$T6 = 112
$T7 = 120
$T8 = 128
$T9 = 136
_Obj$ = 144
$T10 = 152
$T11 = 160
$T12 = 168
$T13 = 176
_Obj$ = 184
$T14 = 192
$T15 = 200
$T16 = 208
$T17 = 216
$T18 = 224
_Old_val$19 = 232
$T20 = 240
$T21 = 248
$T22 = 256
_Al$ = 288
_Myhead$ = 296
<_Val_0>$ = 304
??$_Buynode@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@AEAU?$pair@$$CBII@2@@?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU01@AEAU?$pair@$$CBII@1@@Z PROC ; std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *>::_Buynode<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> >,std::pair<unsigned int const ,unsigned int> &>, COMDAT

; 355  :     static _Nodeptr _Buynode(_Alloc& _Al, _Nodeptr _Myhead, _Valty&&... _Val) {

$LN133:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 81 ec 18 01
	00 00		 sub	 rsp, 280		; 00000118H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1160 :     _CONSTEXPR20 explicit _Alloc_construct_ptr(_Alloc& _Al_) : _Al(_Al_), _Ptr(nullptr) {}

  00016	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR _Al$[rsp]
  0001e	48 89 44 24 20	 mov	 QWORD PTR _Newnode$[rsp], rax
  00023	48 c7 44 24 28
	00 00 00 00	 mov	 QWORD PTR _Newnode$[rsp+8], 0

; 1167 :         _Ptr = nullptr; // if allocate throws, prevents double-free

  0002c	48 c7 44 24 28
	00 00 00 00	 mov	 QWORD PTR _Newnode$[rsp+8], 0

; 1168 :         _Ptr = _Al.allocate(1);

  00035	ba 01 00 00 00	 mov	 edx, 1
  0003a	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Newnode$[rsp]
  0003f	e8 00 00 00 00	 call	 ?allocate@?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@2@_K@Z ; std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> >::allocate
  00044	48 89 44 24 28	 mov	 QWORD PTR _Newnode$[rsp+8], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00049	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR <_Val_0>$[rsp]
  00051	48 89 44 24 50	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 360  :         allocator_traits<_Alloc>::construct(_Al, _STD addressof(_Newnode._Ptr->_Myval), _STD forward<_Valty>(_Val)...);

  00056	48 8b 44 24 28	 mov	 rax, QWORD PTR _Newnode$[rsp+8]
  0005b	48 83 c0 1c	 add	 rax, 28
  0005f	48 89 44 24 38	 mov	 QWORD PTR _Val$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00064	48 8b 44 24 38	 mov	 rax, QWORD PTR _Val$[rsp]
  00069	48 89 44 24 40	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 360  :         allocator_traits<_Alloc>::construct(_Al, _STD addressof(_Newnode._Ptr->_Myval), _STD forward<_Valty>(_Val)...);

  0006e	48 8b 44 24 40	 mov	 rax, QWORD PTR $T1[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  00073	48 89 44 24 48	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 732  :         ::new (const_cast<void*>(static_cast<const volatile void*>(_Ptr))) _Objty(_STD forward<_Types>(_Args)...);

  00078	48 8b 44 24 48	 mov	 rax, QWORD PTR $T2[rsp]
  0007d	48 89 44 24 60	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 360  :         allocator_traits<_Alloc>::construct(_Al, _STD addressof(_Newnode._Ptr->_Myval), _STD forward<_Valty>(_Val)...);

  00082	48 8b 44 24 50	 mov	 rax, QWORD PTR $T3[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00087	48 89 44 24 58	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 732  :         ::new (const_cast<void*>(static_cast<const volatile void*>(_Ptr))) _Objty(_STD forward<_Types>(_Args)...);

  0008c	48 8b 44 24 58	 mov	 rax, QWORD PTR $T4[rsp]
  00091	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00094	48 8b 4c 24 60	 mov	 rcx, QWORD PTR $T5[rsp]
  00099	48 89 01	 mov	 QWORD PTR [rcx], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 361  :         _Construct_in_place(_Newnode._Ptr->_Left, _Myhead);

  0009c	48 8b 44 24 28	 mov	 rax, QWORD PTR _Newnode$[rsp+8]
  000a1	48 89 44 24 68	 mov	 QWORD PTR _Obj$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  000a6	48 8b 44 24 68	 mov	 rax, QWORD PTR _Obj$[rsp]
  000ab	48 89 44 24 70	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  000b0	48 8b 44 24 70	 mov	 rax, QWORD PTR $T6[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  000b5	48 89 44 24 78	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  000ba	48 8b 44 24 78	 mov	 rax, QWORD PTR $T7[rsp]
  000bf	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  000c7	48 8d 84 24 28
	01 00 00	 lea	 rax, QWORD PTR _Myhead$[rsp]
  000cf	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  000d7	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR $T8[rsp]
  000df	48 8b 8c 24 88
	00 00 00	 mov	 rcx, QWORD PTR $T9[rsp]
  000e7	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  000ea	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 362  :         _Construct_in_place(_Newnode._Ptr->_Parent, _Myhead);

  000ed	48 8b 44 24 28	 mov	 rax, QWORD PTR _Newnode$[rsp+8]
  000f2	48 83 c0 08	 add	 rax, 8
  000f6	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR _Obj$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  000fe	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR _Obj$[rsp]
  00106	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0010e	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  00116	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0011e	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  00126	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  0012e	48 8d 84 24 28
	01 00 00	 lea	 rax, QWORD PTR _Myhead$[rsp]
  00136	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0013e	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
  00146	48 8b 8c 24 b0
	00 00 00	 mov	 rcx, QWORD PTR $T13[rsp]
  0014e	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00151	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 363  :         _Construct_in_place(_Newnode._Ptr->_Right, _Myhead);

  00154	48 8b 44 24 28	 mov	 rax, QWORD PTR _Newnode$[rsp+8]
  00159	48 83 c0 10	 add	 rax, 16
  0015d	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR _Obj$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00165	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR _Obj$[rsp]
  0016d	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T14[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00175	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR $T14[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  0017d	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T15[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00185	48 8b 84 24 c8
	00 00 00	 mov	 rax, QWORD PTR $T15[rsp]
  0018d	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR $T16[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00195	48 8d 84 24 28
	01 00 00	 lea	 rax, QWORD PTR _Myhead$[rsp]
  0019d	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR $T17[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  001a5	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR $T16[rsp]
  001ad	48 8b 8c 24 d8
	00 00 00	 mov	 rcx, QWORD PTR $T17[rsp]
  001b5	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  001b8	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 364  :         _Newnode._Ptr->_Color = _Red;

  001bb	48 8b 44 24 28	 mov	 rax, QWORD PTR _Newnode$[rsp+8]
  001c0	c6 40 18 00	 mov	 BYTE PTR [rax+24], 0

; 365  :         _Newnode._Ptr->_Isnil = false;

  001c4	48 8b 44 24 28	 mov	 rax, QWORD PTR _Newnode$[rsp+8]
  001c9	c6 40 19 00	 mov	 BYTE PTR [rax+25], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1163 :         return _STD exchange(_Ptr, nullptr);

  001cd	48 c7 84 24 e0
	00 00 00 00 00
	00 00		 mov	 QWORD PTR $T18[rsp], 0
  001d9	48 8d 44 24 28	 lea	 rax, QWORD PTR _Newnode$[rsp+8]
  001de	48 89 44 24 30	 mov	 QWORD PTR _Val$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 773  :     _Ty _Old_val = static_cast<_Ty&&>(_Val);

  001e3	48 8b 44 24 30	 mov	 rax, QWORD PTR _Val$[rsp]
  001e8	48 8b 00	 mov	 rax, QWORD PTR [rax]
  001eb	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR _Old_val$19[rsp], rax

; 774  :     _Val         = static_cast<_Other&&>(_New_val);

  001f3	48 8b 44 24 30	 mov	 rax, QWORD PTR _Val$[rsp]
  001f8	48 8b 8c 24 e0
	00 00 00	 mov	 rcx, QWORD PTR $T18[rsp]
  00200	48 89 08	 mov	 QWORD PTR [rax], rcx

; 775  :     return _Old_val;

  00203	48 8b 84 24 e8
	00 00 00	 mov	 rax, QWORD PTR _Old_val$19[rsp]
  0020b	48 89 84 24 f0
	00 00 00	 mov	 QWORD PTR $T20[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1163 :         return _STD exchange(_Ptr, nullptr);

  00213	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR $T20[rsp]
  0021b	48 89 84 24 f8
	00 00 00	 mov	 QWORD PTR $T21[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 366  :         return _Newnode._Release();

  00223	48 8b 84 24 f8
	00 00 00	 mov	 rax, QWORD PTR $T21[rsp]
  0022b	48 89 84 24 00
	01 00 00	 mov	 QWORD PTR $T22[rsp], rax
  00233	48 8d 4c 24 20	 lea	 rcx, QWORD PTR _Newnode$[rsp]
  00238	e8 00 00 00 00	 call	 ??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@std@@QEAA@XZ ; std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >
  0023d	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR $T22[rsp]

; 367  :     }

  00245	48 81 c4 18 01
	00 00		 add	 rsp, 280		; 00000118H
  0024c	c3		 ret	 0
??$_Buynode@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@AEAU?$pair@$$CBII@2@@?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU01@AEAU?$pair@$$CBII@1@@Z ENDP ; std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *>::_Buynode<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> >,std::pair<unsigned int const ,unsigned int> &>
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
_Newnode$ = 32
_Val$ = 48
_Val$ = 56
$T1 = 64
$T2 = 72
$T3 = 80
$T4 = 88
$T5 = 96
_Obj$ = 104
$T6 = 112
$T7 = 120
$T8 = 128
$T9 = 136
_Obj$ = 144
$T10 = 152
$T11 = 160
$T12 = 168
$T13 = 176
_Obj$ = 184
$T14 = 192
$T15 = 200
$T16 = 208
$T17 = 216
$T18 = 224
_Old_val$19 = 232
$T20 = 240
$T21 = 248
$T22 = 256
_Al$ = 288
_Myhead$ = 296
<_Val_0>$ = 304
?dtor$0@?0???$_Buynode@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@AEAU?$pair@$$CBII@2@@?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU01@AEAU?$pair@$$CBII@1@@Z@4HA PROC ; `std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *>::_Buynode<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> >,std::pair<unsigned int const ,unsigned int> &>'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8d 4d 20	 lea	 rcx, QWORD PTR _Newnode$[rbp]
  0000d	e8 00 00 00 00	 call	 ??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@std@@QEAA@XZ ; std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$0@?0???$_Buynode@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@AEAU?$pair@$$CBII@2@@?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU01@AEAU?$pair@$$CBII@1@@Z@4HA ENDP ; `std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *>::_Buynode<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> >,std::pair<unsigned int const ,unsigned int> &>'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??$_Erase_tree_and_orphan@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@@Z
_TEXT	SEGMENT
_Bytes$ = 32
_To_delete$1 = 40
_Ptr$ = 48
_New_val$ = 56
_Old_val$2 = 64
$T3 = 72
$T4 = 80
this$ = 112
_Al$ = 120
_Rootnode$ = 128
??$_Erase_tree_and_orphan@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@@Z PROC ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,unsigned int> > >::_Erase_tree_and_orphan<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >, COMDAT

; 757  :     void _Erase_tree_and_orphan(_Alnode& _Al, _Nodeptr _Rootnode) noexcept {

$LN63:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 68	 sub	 rsp, 104		; 00000068H
$LN2@Erase_tree:

; 758  :         while (!_Rootnode->_Isnil) { // free subtrees, then node

  00013	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _Rootnode$[rsp]
  0001b	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  0001f	85 c0		 test	 eax, eax
  00021	0f 85 af 00 00
	00		 jne	 $LN3@Erase_tree

; 759  :             _Erase_tree_and_orphan(_Al, _Rootnode->_Right);

  00027	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _Rootnode$[rsp]
  0002f	4c 8b 40 10	 mov	 r8, QWORD PTR [rax+16]
  00033	48 8b 54 24 78	 mov	 rdx, QWORD PTR _Al$[rsp]
  00038	48 8b 4c 24 70	 mov	 rcx, QWORD PTR this$[rsp]
  0003d	e8 00 00 00 00	 call	 ??$_Erase_tree_and_orphan@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,unsigned int> > >::_Erase_tree_and_orphan<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >

; 760  :             auto _To_delete = _STD exchange(_Rootnode, _Rootnode->_Left);

  00042	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _Rootnode$[rsp]
  0004a	48 89 44 24 38	 mov	 QWORD PTR _New_val$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 773  :     _Ty _Old_val = static_cast<_Ty&&>(_Val);

  0004f	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _Rootnode$[rsp]
  00057	48 89 44 24 40	 mov	 QWORD PTR _Old_val$2[rsp], rax

; 774  :     _Val         = static_cast<_Other&&>(_New_val);

  0005c	48 8b 44 24 38	 mov	 rax, QWORD PTR _New_val$[rsp]
  00061	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00064	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _Rootnode$[rsp], rax

; 775  :     return _Old_val;

  0006c	48 8b 44 24 40	 mov	 rax, QWORD PTR _Old_val$2[rsp]
  00071	48 89 44 24 48	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 760  :             auto _To_delete = _STD exchange(_Rootnode, _Rootnode->_Left);

  00076	48 8b 44 24 48	 mov	 rax, QWORD PTR $T3[rsp]
  0007b	48 89 44 24 28	 mov	 QWORD PTR _To_delete$1[rsp], rax

; 381  :         allocator_traits<_Alloc>::destroy(_Al, _STD addressof(_Ptr->_Myval));

  00080	48 8b 44 24 28	 mov	 rax, QWORD PTR _To_delete$1[rsp]
  00085	48 83 c0 1c	 add	 rax, 28
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00089	48 89 44 24 50	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 723  :             _STD _Deallocate<_New_alignof<value_type>>(_Ptr, sizeof(value_type) * _Count);

  0008e	b8 01 00 00 00	 mov	 eax, 1
  00093	48 6b c0 28	 imul	 rax, rax, 40		; 00000028H
  00097	48 89 44 24 20	 mov	 QWORD PTR _Bytes$[rsp], rax
  0009c	48 8b 44 24 28	 mov	 rax, QWORD PTR _To_delete$1[rsp]
  000a1	48 89 44 24 30	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  000a6	48 81 7c 24 20
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  000af	72 10		 jb	 SHORT $LN54@Erase_tree

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  000b1	48 8d 54 24 20	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  000b6	48 8d 4c 24 30	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  000bb	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  000c0	90		 npad	 1
$LN54@Erase_tree:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  000c1	48 8b 54 24 20	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  000c6	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  000cb	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  000d0	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 763  :         }

  000d1	e9 3d ff ff ff	 jmp	 $LN2@Erase_tree
$LN3@Erase_tree:

; 764  :     }

  000d6	48 83 c4 68	 add	 rsp, 104		; 00000068H
  000da	c3		 ret	 0
??$_Erase_tree_and_orphan@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@@Z ENDP ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,unsigned int> > >::_Erase_tree_and_orphan<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??$_Buynode@AEAU?$pair@$$CBII@std@@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@AEAU?$pair@$$CBII@1@@Z
_TEXT	SEGMENT
$T1 = 32
$T2 = 40
$T3 = 48
$T4 = 56
$T5 = 64
this$ = 96
<_Val_0>$ = 104
??$_Buynode@AEAU?$pair@$$CBII@std@@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@AEAU?$pair@$$CBII@1@@Z PROC ; std::_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Buynode<std::pair<unsigned int const ,unsigned int> &>, COMDAT

; 1962 :     _Nodeptr _Buynode(_Valty&&... _Val) {

$LN160:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  0000e	48 8b 44 24 68	 mov	 rax, QWORD PTR <_Val_0>$[rsp]
  00013	48 89 44 24 30	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00018	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0001d	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00022	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00027	48 89 44 24 40	 mov	 QWORD PTR $T5[rsp], rax

; 1975 :         return _Mypair._Myval2._Get_first();

  0002c	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00031	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1975 :         return _Mypair._Myval2._Get_first();

  00036	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
  0003b	48 89 44 24 38	 mov	 QWORD PTR $T4[rsp], rax

; 1963 :         return _Node::_Buynode(_Getal(), _Get_scary()->_Myhead, _STD forward<_Valty>(_Val)...);

  00040	48 8b 44 24 30	 mov	 rax, QWORD PTR $T3[rsp]
  00045	48 8b 4c 24 38	 mov	 rcx, QWORD PTR $T4[rsp]
  0004a	4c 8b c0	 mov	 r8, rax
  0004d	48 8b 44 24 40	 mov	 rax, QWORD PTR $T5[rsp]
  00052	48 8b 10	 mov	 rdx, QWORD PTR [rax]
  00055	e8 00 00 00 00	 call	 ??$_Buynode@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@AEAU?$pair@$$CBII@2@@?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU01@AEAU?$pair@$$CBII@1@@Z ; std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *>::_Buynode<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> >,std::pair<unsigned int const ,unsigned int> &>

; 1964 :     }

  0005a	48 83 c4 58	 add	 rsp, 88			; 00000058H
  0005e	c3		 ret	 0
??$_Buynode@AEAU?$pair@$$CBII@std@@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@AEAU?$pair@$$CBII@1@@Z ENDP ; std::_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Buynode<std::pair<unsigned int const ,unsigned int> &>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??1?$_Erase_tree_and_orphan_guard@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1?$_Erase_tree_and_orphan_guard@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAA@XZ PROC ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,unsigned int> > >::_Erase_tree_and_orphan_guard<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >::~_Erase_tree_and_orphan_guard<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >, COMDAT

; 443  :         ~_Erase_tree_and_orphan_guard() noexcept {

$LN69:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 444  :             if (_Val_ptr != nullptr) {

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 38 00	 cmp	 QWORD PTR [rax], 0
  00012	74 20		 je	 SHORT $LN2@Erase_tree

; 445  :                 _Val_ptr->_Erase_tree_and_orphan(_Al, _New_root); // subtree copy failed, bail out

  00014	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00019	4c 8b 40 10	 mov	 r8, QWORD PTR [rax+16]
  0001d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00022	48 8b 50 08	 mov	 rdx, QWORD PTR [rax+8]
  00026	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0002b	48 8b 08	 mov	 rcx, QWORD PTR [rax]
  0002e	e8 00 00 00 00	 call	 ??$_Erase_tree_and_orphan@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,unsigned int> > >::_Erase_tree_and_orphan<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >
  00033	90		 npad	 1
$LN2@Erase_tree:

; 446  :             }
; 447  :         }

  00034	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00038	c3		 ret	 0
??1?$_Erase_tree_and_orphan_guard@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAA@XZ ENDP ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,unsigned int> > >::_Erase_tree_and_orphan_guard<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >::~_Erase_tree_and_orphan_guard<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??$_Copy_nodes@$0A@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@PEAU21@0@Z
_TEXT	SEGMENT
_Newroot$ = 32
_Scary$ = 40
_Guard$1 = 48
$T2 = 72
$T3 = 80
$T4 = 88
$T5 = 96
$T6 = 104
this$ = 128
_Rootnode$ = 136
_Wherenode$ = 144
??$_Copy_nodes@$0A@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@PEAU21@0@Z PROC ; std::_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Copy_nodes<0>, COMDAT

; 1682 :     _Nodeptr _Copy_nodes(_Nodeptr _Rootnode, _Nodeptr _Wherenode) {

$LN256:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 78	 sub	 rsp, 120		; 00000078H

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00013	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0001b	48 89 44 24 48	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00020	48 8b 44 24 48	 mov	 rax, QWORD PTR $T2[rsp]
  00025	48 89 44 24 50	 mov	 QWORD PTR $T3[rsp], rax

; 1683 :         // copy entire subtree, recursively
; 1684 :         const auto _Scary = _Get_scary();

  0002a	48 8b 44 24 50	 mov	 rax, QWORD PTR $T3[rsp]
  0002f	48 89 44 24 28	 mov	 QWORD PTR _Scary$[rsp], rax

; 1685 :         _Nodeptr _Newroot = _Scary->_Myhead; // point at nil node

  00034	48 8b 44 24 28	 mov	 rax, QWORD PTR _Scary$[rsp]
  00039	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0003c	48 89 44 24 20	 mov	 QWORD PTR _Newroot$[rsp], rax

; 1686 : 
; 1687 :         if (!_Rootnode->_Isnil) { // copy or move a node, then any subtrees

  00041	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR _Rootnode$[rsp]
  00049	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  0004d	85 c0		 test	 eax, eax
  0004f	0f 85 f7 00 00
	00		 jne	 $LN2@Copy_nodes

; 1688 :             _Newroot          = _Copy_or_move<_Strat>(_Rootnode->_Myval); // memorize new root

  00055	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR _Rootnode$[rsp]
  0005d	48 83 c0 1c	 add	 rax, 28

; 1671 :             return _Buynode(_Val);

  00061	48 8b d0	 mov	 rdx, rax
  00064	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0006c	e8 00 00 00 00	 call	 ??$_Buynode@AEAU?$pair@$$CBII@std@@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@AEAU?$pair@$$CBII@1@@Z ; std::_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Buynode<std::pair<unsigned int const ,unsigned int> &>
  00071	48 89 44 24 58	 mov	 QWORD PTR $T4[rsp], rax

; 1688 :             _Newroot          = _Copy_or_move<_Strat>(_Rootnode->_Myval); // memorize new root

  00076	48 8b 44 24 58	 mov	 rax, QWORD PTR $T4[rsp]
  0007b	48 89 44 24 20	 mov	 QWORD PTR _Newroot$[rsp], rax

; 1689 :             _Newroot->_Parent = _Wherenode;

  00080	48 8b 44 24 20	 mov	 rax, QWORD PTR _Newroot$[rsp]
  00085	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR _Wherenode$[rsp]
  0008d	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 1690 :             _Newroot->_Color  = _Rootnode->_Color;

  00091	48 8b 44 24 20	 mov	 rax, QWORD PTR _Newroot$[rsp]
  00096	48 8b 8c 24 88
	00 00 00	 mov	 rcx, QWORD PTR _Rootnode$[rsp]
  0009e	0f b6 49 18	 movzx	 ecx, BYTE PTR [rcx+24]
  000a2	88 48 18	 mov	 BYTE PTR [rax+24], cl

; 1691 : 
; 1692 :             typename _Scary_val::template _Erase_tree_and_orphan_guard<_Alnode> _Guard{_Scary, _Getal(), _Newroot};

  000a5	48 8b 44 24 28	 mov	 rax, QWORD PTR _Scary$[rsp]
  000aa	48 89 44 24 30	 mov	 QWORD PTR _Guard$1[rsp], rax

; 1975 :         return _Mypair._Myval2._Get_first();

  000af	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  000b7	48 89 44 24 60	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1975 :         return _Mypair._Myval2._Get_first();

  000bc	48 8b 44 24 60	 mov	 rax, QWORD PTR $T5[rsp]
  000c1	48 89 44 24 68	 mov	 QWORD PTR $T6[rsp], rax

; 1691 : 
; 1692 :             typename _Scary_val::template _Erase_tree_and_orphan_guard<_Alnode> _Guard{_Scary, _Getal(), _Newroot};

  000c6	48 8b 44 24 68	 mov	 rax, QWORD PTR $T6[rsp]
  000cb	48 89 44 24 38	 mov	 QWORD PTR _Guard$1[rsp+8], rax
  000d0	48 8b 44 24 20	 mov	 rax, QWORD PTR _Newroot$[rsp]
  000d5	48 89 44 24 40	 mov	 QWORD PTR _Guard$1[rsp+16], rax

; 1693 : 
; 1694 :             _Newroot->_Left  = _Copy_nodes<_Strat>(_Rootnode->_Left, _Newroot);

  000da	4c 8b 44 24 20	 mov	 r8, QWORD PTR _Newroot$[rsp]
  000df	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR _Rootnode$[rsp]
  000e7	48 8b 10	 mov	 rdx, QWORD PTR [rax]
  000ea	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  000f2	e8 00 00 00 00	 call	 ??$_Copy_nodes@$0A@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@PEAU21@0@Z ; std::_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Copy_nodes<0>
  000f7	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Newroot$[rsp]
  000fc	48 89 01	 mov	 QWORD PTR [rcx], rax

; 1695 :             _Newroot->_Right = _Copy_nodes<_Strat>(_Rootnode->_Right, _Newroot);

  000ff	4c 8b 44 24 20	 mov	 r8, QWORD PTR _Newroot$[rsp]
  00104	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR _Rootnode$[rsp]
  0010c	48 8b 50 10	 mov	 rdx, QWORD PTR [rax+16]
  00110	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00118	e8 00 00 00 00	 call	 ??$_Copy_nodes@$0A@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@PEAU21@0@Z ; std::_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Copy_nodes<0>
  0011d	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Newroot$[rsp]
  00122	48 89 41 10	 mov	 QWORD PTR [rcx+16], rax

; 1696 : 
; 1697 :             _Guard._Val_ptr = nullptr;

  00126	48 c7 44 24 30
	00 00 00 00	 mov	 QWORD PTR _Guard$1[rsp], 0

; 444  :             if (_Val_ptr != nullptr) {

  0012f	48 83 7c 24 30
	00		 cmp	 QWORD PTR _Guard$1[rsp], 0
  00135	74 15		 je	 SHORT $LN2@Copy_nodes

; 445  :                 _Val_ptr->_Erase_tree_and_orphan(_Al, _New_root); // subtree copy failed, bail out

  00137	4c 8b 44 24 40	 mov	 r8, QWORD PTR _Guard$1[rsp+16]
  0013c	48 8b 54 24 38	 mov	 rdx, QWORD PTR _Guard$1[rsp+8]
  00141	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Guard$1[rsp]
  00146	e8 00 00 00 00	 call	 ??$_Erase_tree_and_orphan@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,unsigned int> > >::_Erase_tree_and_orphan<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >
  0014b	90		 npad	 1
$LN2@Copy_nodes:

; 1698 :         }
; 1699 : 
; 1700 :         return _Newroot; // return newly constructed tree

  0014c	48 8b 44 24 20	 mov	 rax, QWORD PTR _Newroot$[rsp]

; 1701 :     }

  00151	48 83 c4 78	 add	 rsp, 120		; 00000078H
  00155	c3		 ret	 0
??$_Copy_nodes@$0A@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@PEAU21@0@Z ENDP ; std::_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Copy_nodes<0>
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
_Newroot$ = 32
_Scary$ = 40
_Guard$1 = 48
$T2 = 72
$T3 = 80
$T4 = 88
$T5 = 96
$T6 = 104
this$ = 128
_Rootnode$ = 136
_Wherenode$ = 144
?dtor$0@?0???$_Copy_nodes@$0A@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@PEAU21@0@Z@4HA PROC ; `std::_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Copy_nodes<0>'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8d 4d 30	 lea	 rcx, QWORD PTR _Guard$1[rbp]
  0000d	e8 00 00 00 00	 call	 ??1?$_Erase_tree_and_orphan_guard@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAA@XZ ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,unsigned int> > >::_Erase_tree_and_orphan_guard<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >::~_Erase_tree_and_orphan_guard<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$0@?0???$_Copy_nodes@$0A@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@PEAU21@0@Z@4HA ENDP ; `std::_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Copy_nodes<0>'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??$_Copy@$0A@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAXAEBV01@@Z
_TEXT	SEGMENT
_Scary$ = 32
_Pnode$ = 40
_Pnode$ = 48
_Right_scary$ = 56
$T1 = 64
$T2 = 72
$T3 = 80
$T4 = 88
$T5 = 96
$T6 = 104
this$ = 128
_Right$ = 136
??$_Copy@$0A@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAXAEBV01@@Z PROC ; std::_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Copy<0>, COMDAT

; 1654 :     void _Copy(const _Tree& _Right) { // copy or move entire tree from _Right

$LN162:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 78	 sub	 rsp, 120		; 00000078H

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  0000e	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00016	48 89 44 24 40	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  0001b	48 8b 44 24 40	 mov	 rax, QWORD PTR $T1[rsp]
  00020	48 89 44 24 48	 mov	 QWORD PTR $T2[rsp], rax

; 1655 :         const auto _Scary        = _Get_scary();

  00025	48 8b 44 24 48	 mov	 rax, QWORD PTR $T2[rsp]
  0002a	48 89 44 24 20	 mov	 QWORD PTR _Scary$[rsp], rax

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  0002f	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR _Right$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00037	48 89 44 24 50	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  0003c	48 8b 44 24 50	 mov	 rax, QWORD PTR $T3[rsp]
  00041	48 89 44 24 58	 mov	 QWORD PTR $T4[rsp], rax

; 1656 :         const auto _Right_scary  = _Right._Get_scary();

  00046	48 8b 44 24 58	 mov	 rax, QWORD PTR $T4[rsp]
  0004b	48 89 44 24 38	 mov	 QWORD PTR _Right_scary$[rsp], rax

; 1657 :         _Scary->_Myhead->_Parent = _Copy_nodes<_Strat>(_Right_scary->_Myhead->_Parent, _Scary->_Myhead);

  00050	48 8b 44 24 38	 mov	 rax, QWORD PTR _Right_scary$[rsp]
  00055	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00058	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Scary$[rsp]
  0005d	4c 8b 01	 mov	 r8, QWORD PTR [rcx]
  00060	48 8b 50 08	 mov	 rdx, QWORD PTR [rax+8]
  00064	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0006c	e8 00 00 00 00	 call	 ??$_Copy_nodes@$0A@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@PEAU21@0@Z ; std::_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Copy_nodes<0>
  00071	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Scary$[rsp]
  00076	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00079	48 89 41 08	 mov	 QWORD PTR [rcx+8], rax

; 1658 :         _Scary->_Mysize          = _Right_scary->_Mysize;

  0007d	48 8b 44 24 20	 mov	 rax, QWORD PTR _Scary$[rsp]
  00082	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Right_scary$[rsp]
  00087	48 8b 49 08	 mov	 rcx, QWORD PTR [rcx+8]
  0008b	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 1659 :         if (!_Scary->_Myhead->_Parent->_Isnil) { // nonempty tree, look for new smallest and largest

  0008f	48 8b 44 24 20	 mov	 rax, QWORD PTR _Scary$[rsp]
  00094	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00097	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0009b	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  0009f	85 c0		 test	 eax, eax
  000a1	0f 85 99 00 00
	00		 jne	 $LN2@Copy

; 1660 :             _Scary->_Myhead->_Left  = _Scary_val::_Min(_Scary->_Myhead->_Parent);

  000a7	48 8b 44 24 20	 mov	 rax, QWORD PTR _Scary$[rsp]
  000ac	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000af	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  000b3	48 89 44 24 28	 mov	 QWORD PTR _Pnode$[rsp], rax
$LN149@Copy:

; 476  :         while (!_Pnode->_Left->_Isnil) {

  000b8	48 8b 44 24 28	 mov	 rax, QWORD PTR _Pnode$[rsp]
  000bd	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000c0	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  000c4	85 c0		 test	 eax, eax
  000c6	75 0f		 jne	 SHORT $LN150@Copy

; 477  :             _Pnode = _Pnode->_Left;

  000c8	48 8b 44 24 28	 mov	 rax, QWORD PTR _Pnode$[rsp]
  000cd	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000d0	48 89 44 24 28	 mov	 QWORD PTR _Pnode$[rsp], rax

; 478  :         }

  000d5	eb e1		 jmp	 SHORT $LN149@Copy
$LN150@Copy:

; 479  : 
; 480  :         return _Pnode;

  000d7	48 8b 44 24 28	 mov	 rax, QWORD PTR _Pnode$[rsp]
  000dc	48 89 44 24 60	 mov	 QWORD PTR $T5[rsp], rax

; 1660 :             _Scary->_Myhead->_Left  = _Scary_val::_Min(_Scary->_Myhead->_Parent);

  000e1	48 8b 44 24 60	 mov	 rax, QWORD PTR $T5[rsp]
  000e6	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Scary$[rsp]
  000eb	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  000ee	48 89 01	 mov	 QWORD PTR [rcx], rax

; 1661 :             _Scary->_Myhead->_Right = _Scary_val::_Max(_Scary->_Myhead->_Parent);

  000f1	48 8b 44 24 20	 mov	 rax, QWORD PTR _Scary$[rsp]
  000f6	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000f9	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  000fd	48 89 44 24 30	 mov	 QWORD PTR _Pnode$[rsp], rax
$LN156@Copy:

; 468  :         while (!_Pnode->_Right->_Isnil) {

  00102	48 8b 44 24 30	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00107	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0010b	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  0010f	85 c0		 test	 eax, eax
  00111	75 10		 jne	 SHORT $LN157@Copy

; 469  :             _Pnode = _Pnode->_Right;

  00113	48 8b 44 24 30	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00118	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0011c	48 89 44 24 30	 mov	 QWORD PTR _Pnode$[rsp], rax

; 470  :         }

  00121	eb df		 jmp	 SHORT $LN156@Copy
$LN157@Copy:

; 471  : 
; 472  :         return _Pnode;

  00123	48 8b 44 24 30	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00128	48 89 44 24 68	 mov	 QWORD PTR $T6[rsp], rax

; 1661 :             _Scary->_Myhead->_Right = _Scary_val::_Max(_Scary->_Myhead->_Parent);

  0012d	48 8b 44 24 68	 mov	 rax, QWORD PTR $T6[rsp]
  00132	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Scary$[rsp]
  00137	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0013a	48 89 41 10	 mov	 QWORD PTR [rcx+16], rax

; 1662 :         } else { // empty tree, just tidy head pointers

  0013e	eb 27		 jmp	 SHORT $LN3@Copy
$LN2@Copy:

; 1663 :             _Scary->_Myhead->_Left  = _Scary->_Myhead;

  00140	48 8b 44 24 20	 mov	 rax, QWORD PTR _Scary$[rsp]
  00145	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00148	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Scary$[rsp]
  0014d	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00150	48 89 08	 mov	 QWORD PTR [rax], rcx

; 1664 :             _Scary->_Myhead->_Right = _Scary->_Myhead;

  00153	48 8b 44 24 20	 mov	 rax, QWORD PTR _Scary$[rsp]
  00158	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0015b	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Scary$[rsp]
  00160	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00163	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx
$LN3@Copy:

; 1665 :         }
; 1666 :     }

  00167	48 83 c4 78	 add	 rsp, 120		; 00000078H
  0016b	c3		 ret	 0
??$_Copy@$0A@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAXAEBV01@@Z ENDP ; std::_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Copy<0>
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ManagerJoinZoneContext.h
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??__F?inst@?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@1VtheManagerJoinContext@2@A@@YAXXZ
text$yd	SEGMENT
??__F?inst@?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@1VtheManagerJoinContext@2@A@@YAXXZ PROC ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::theManagerJoinContext>::inst'', COMDAT
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ManagerJoinZoneContext.h

; 15   : 		virtual~theManagerJoinContext() override {};

  00000	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_7theManagerJoinContext@mu2@@6B@
  00007	48 89 05 00 00
	00 00		 mov	 QWORD PTR ?inst@?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@1VtheManagerJoinContext@2@A, rax
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 80   : 	virtual~ISingleton() {}

  0000e	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@1VtheManagerJoinContext@2@A ; mu2::ISingleton<mu2::theManagerJoinContext>::inst
  00015	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@6B@
  0001c	48 89 08	 mov	 QWORD PTR [rax], rcx
  0001f	c3		 ret	 0
??__F?inst@?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@1VtheManagerJoinContext@2@A@@YAXXZ ENDP ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::theManagerJoinContext>::inst''
text$yd	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ManagerJoinZoneContext.h
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??__E?inst@?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@1VtheManagerJoinContext@2@A@@YAXXZ
text$di	SEGMENT
??__E?inst@?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@1VtheManagerJoinContext@2@A@@YAXXZ PROC ; `dynamic initializer for 'mu2::ISingleton<mu2::theManagerJoinContext>::inst'', COMDAT

; 90   : template<typename T> T ISingleton<T>::inst;

  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 84   : 	ISingleton() {}	

  00004	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@1VtheManagerJoinContext@2@A ; mu2::ISingleton<mu2::theManagerJoinContext>::inst
  0000b	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@6B@
  00012	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ManagerJoinZoneContext.h

; 20   : 		theManagerJoinContext() {};

  00015	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_7theManagerJoinContext@mu2@@6B@
  0001c	48 89 05 00 00
	00 00		 mov	 QWORD PTR ?inst@?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@1VtheManagerJoinContext@2@A, rax
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 90   : template<typename T> T ISingleton<T>::inst;

  00023	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??__F?inst@?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@1VtheManagerJoinContext@2@A@@YAXXZ ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::theManagerJoinContext>::inst''
  0002a	e8 00 00 00 00	 call	 atexit
  0002f	90		 npad	 1
  00030	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00034	c3		 ret	 0
??__E?inst@?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@1VtheManagerJoinContext@2@A@@YAXXZ ENDP ; `dynamic initializer for 'mu2::ISingleton<mu2::theManagerJoinContext>::inst''
text$di	ENDS
; Function compile flags: /Odtp
;	COMDAT ??__F?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ
text$yd	SEGMENT
??__F?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ PROC ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::ExecutionManager>::inst'', COMDAT
  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A ; mu2::ISingleton<mu2::ExecutionManager>::inst
  0000b	e8 00 00 00 00	 call	 ??1ExecutionManager@mu2@@UEAA@XZ ; mu2::ExecutionManager::~ExecutionManager
  00010	90		 npad	 1
  00011	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00015	c3		 ret	 0
??__F?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ ENDP ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::ExecutionManager>::inst''
text$yd	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??__E?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ
text$di	SEGMENT
??__E?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ PROC ; `dynamic initializer for 'mu2::ISingleton<mu2::ExecutionManager>::inst'', COMDAT

; 90   : template<typename T> T ISingleton<T>::inst;

  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A ; mu2::ISingleton<mu2::ExecutionManager>::inst
  0000b	e8 00 00 00 00	 call	 ??0ExecutionManager@mu2@@AEAA@XZ ; mu2::ExecutionManager::ExecutionManager
  00010	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??__F?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::ExecutionManager>::inst''
  00017	e8 00 00 00 00	 call	 atexit
  0001c	90		 npad	 1
  0001d	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00021	c3		 ret	 0
??__E?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ ENDP ; `dynamic initializer for 'mu2::ISingleton<mu2::ExecutionManager>::inst''
text$di	ENDS
; Function compile flags: /Odtp
;	COMDAT ??__F?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ
text$yd	SEGMENT
??__F?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ PROC ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::GlobalLoadScript>::inst'', COMDAT
  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A ; mu2::ISingleton<mu2::GlobalLoadScript>::inst
  0000b	e8 00 00 00 00	 call	 ??1GlobalLoadScript@mu2@@UEAA@XZ ; mu2::GlobalLoadScript::~GlobalLoadScript
  00010	90		 npad	 1
  00011	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00015	c3		 ret	 0
??__F?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ ENDP ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::GlobalLoadScript>::inst''
text$yd	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??__E?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ
text$di	SEGMENT
??__E?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ PROC ; `dynamic initializer for 'mu2::ISingleton<mu2::GlobalLoadScript>::inst'', COMDAT

; 90   : template<typename T> T ISingleton<T>::inst;

  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A ; mu2::ISingleton<mu2::GlobalLoadScript>::inst
  0000b	e8 00 00 00 00	 call	 ??0GlobalLoadScript@mu2@@AEAA@XZ ; mu2::GlobalLoadScript::GlobalLoadScript
  00010	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??__F?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::GlobalLoadScript>::inst''
  00017	e8 00 00 00 00	 call	 atexit
  0001c	90		 npad	 1
  0001d	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00021	c3		 ret	 0
??__E?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ ENDP ; `dynamic initializer for 'mu2::ISingleton<mu2::GlobalLoadScript>::inst''
text$di	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h
;	COMDAT ??_GPlayerJoinAction@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GPlayerJoinAction@mu2@@UEAAPEAXI@Z PROC		; mu2::PlayerJoinAction::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00012	e8 00 00 00 00	 call	 ??1PlayerJoinAction@mu2@@UEAA@XZ ; mu2::PlayerJoinAction::~PlayerJoinAction
  00017	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  0001b	83 e0 01	 and	 eax, 1
  0001e	85 c0		 test	 eax, eax
  00020	74 28		 je	 SHORT $LN2@scalar
  00022	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00026	83 e0 04	 and	 eax, 4
  00029	85 c0		 test	 eax, eax
  0002b	75 0d		 jne	 SHORT $LN3@scalar

; 45   : 			::free(ptr);

  0002d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00032	e8 00 00 00 00	 call	 free
  00037	90		 npad	 1
  00038	eb 10		 jmp	 SHORT $LN2@scalar
$LN3@scalar:
  0003a	ba 80 00 00 00	 mov	 edx, 128		; 00000080H
  0003f	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00044	e8 00 00 00 00	 call	 ?__global_delete@@YAXPEAX_K@Z ; __global_delete
  00049	90		 npad	 1
$LN2@scalar:
  0004a	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004f	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00053	c3		 ret	 0
??_GPlayerJoinAction@mu2@@UEAAPEAXI@Z ENDP		; mu2::PlayerJoinAction::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
;	COMDAT ?SetPortalType4Client@PlayerJoinAction@mu2@@AEAAXW4Enum@PortalType@2@@Z
_TEXT	SEGMENT
this$ = 8
eType$ = 16
?SetPortalType4Client@PlayerJoinAction@mu2@@AEAAXW4Enum@PortalType@2@@Z PROC ; mu2::PlayerJoinAction::SetPortalType4Client, COMDAT

; 138  : 	{

  00000	88 54 24 10	 mov	 BYTE PTR [rsp+16], dl
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 139  : 		m_eCurrProtalType4Client = eType;

  00009	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0000e	0f b6 4c 24 10	 movzx	 ecx, BYTE PTR eType$[rsp]
  00013	88 48 49	 mov	 BYTE PTR [rax+73], cl

; 140  : 	}

  00016	c3		 ret	 0
?SetPortalType4Client@PlayerJoinAction@mu2@@AEAAXW4Enum@PortalType@2@@Z ENDP ; mu2::PlayerJoinAction::SetPortalType4Client
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
;	COMDAT ?IsValid@PlayerJoinAction@mu2@@AEBA_NXZ
_TEXT	SEGMENT
this$ = 48
?IsValid@PlayerJoinAction@mu2@@AEBA_NXZ PROC		; mu2::PlayerJoinAction::IsValid, COMDAT

; 130  : 	{	

$LN4:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 131  : 		if (GetJoinContext() == JoinContext::JC_LOGOUT)

  00009	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0000e	e8 00 00 00 00	 call	 ?GetJoinContext@PlayerJoinAction@mu2@@QEBA?BW4Enum@JoinContext@2@XZ ; mu2::PlayerJoinAction::GetJoinContext
  00013	0f b6 c0	 movzx	 eax, al
  00016	83 f8 06	 cmp	 eax, 6
  00019	75 04		 jne	 SHORT $LN2@IsValid

; 132  : 			return true;

  0001b	b0 01		 mov	 al, 1
  0001d	eb 11		 jmp	 SHORT $LN1@IsValid
$LN2@IsValid:

; 133  : 		return m_toLocation.execId.IsValid();

  0001f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00024	48 83 c0 28	 add	 rax, 40			; 00000028H
  00028	48 8b c8	 mov	 rcx, rax
  0002b	e8 00 00 00 00	 call	 ?IsValid@ExecutionZoneId@mu2@@QEBA?B_NXZ ; mu2::ExecutionZoneId::IsValid
$LN1@IsValid:

; 134  : 	}

  00030	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00034	c3		 ret	 0
?IsValid@PlayerJoinAction@mu2@@AEBA_NXZ ENDP		; mu2::PlayerJoinAction::IsValid
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
; File F:\Release_Branch\Server\Development\Framework\Entity\Action.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EntityPlayer.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
;	COMDAT ?SetToExecId@PlayerJoinAction@mu2@@AEAAXAEBVExecutionZoneId@2@@Z
_TEXT	SEGMENT
this$ = 32
$T1 = 40
$T2 = 48
player$ = 56
this$ = 80
toExecId$ = 88
?SetToExecId@PlayerJoinAction@mu2@@AEAAXAEBVExecutionZoneId@2@@Z PROC ; mu2::PlayerJoinAction::SetToExecId, COMDAT

; 171  : 	{

$LN21:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 48	 sub	 rsp, 72			; 00000048H
; File F:\Release_Branch\Server\Development\Framework\Entity\Action.h

; 29   : 	inline Entity* GetOwner() const { return m_owner; }

  0000e	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00013	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00017	48 89 44 24 28	 mov	 QWORD PTR $T1[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EntityPlayer.h

; 461  : 			return static_cast<EntityPlayer*>(GetOwner());

  0001c	48 8b 44 24 28	 mov	 rax, QWORD PTR $T1[rsp]
  00021	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp

; 172  : 		EntityPlayer* player = GetOwnerPlayer();

  00026	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
  0002b	48 89 44 24 38	 mov	 QWORD PTR player$[rsp], rax

; 173  : 		if (GetJoinContext() == JoinContext::JC_CHANGE_MAP )

  00030	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  00035	e8 00 00 00 00	 call	 ?GetJoinContext@PlayerJoinAction@mu2@@QEBA?BW4Enum@JoinContext@2@XZ ; mu2::PlayerJoinAction::GetJoinContext
  0003a	0f b6 c0	 movzx	 eax, al
  0003d	83 f8 02	 cmp	 eax, 2
  00040	75 4c		 jne	 SHORT $LN2@SetToExecI

; 174  : 		{	
; 175  : 			if (player->GetCurrExecId() == toExecId)

  00042	48 8b 4c 24 38	 mov	 rcx, QWORD PTR player$[rsp]
  00047	e8 00 00 00 00	 call	 ?GetCurrExecId@EntityPlayer@mu2@@QEBAAEBVExecutionZoneId@2@XZ ; mu2::EntityPlayer::GetCurrExecId
  0004c	48 8b 54 24 58	 mov	 rdx, QWORD PTR toExecId$[rsp]
  00051	48 8b c8	 mov	 rcx, rax
  00054	e8 00 00 00 00	 call	 ??8ExecutionZoneId@mu2@@QEBA_NAEBV01@@Z ; mu2::ExecutionZoneId::operator==
  00059	0f b6 c0	 movzx	 eax, al
  0005c	85 c0		 test	 eax, eax
  0005e	74 0f		 je	 SHORT $LN3@SetToExecI

; 176  : 			{
; 177  : 				SetJoinContext(JoinContext::JC_WARP_MAP);

  00060	b2 03		 mov	 dl, 3
  00062	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  00067	e8 00 00 00 00	 call	 ?SetJoinContext@PlayerJoinAction@mu2@@QEAAXW4Enum@JoinContext@2@@Z ; mu2::PlayerJoinAction::SetJoinContext
  0006c	90		 npad	 1

; 178  : 			}

  0006d	eb 1f		 jmp	 SHORT $LN2@SetToExecI
$LN3@SetToExecI:

; 179  : 			else if (toExecId.GetZoneIndex() == TutorialZoneIndex)

  0006f	48 8b 4c 24 58	 mov	 rcx, QWORD PTR toExecId$[rsp]
  00074	e8 00 00 00 00	 call	 ?GetZoneIndex@ExecutionZoneId@mu2@@QEBAGXZ ; mu2::ExecutionZoneId::GetZoneIndex
  00079	0f b7 c0	 movzx	 eax, ax
  0007c	83 f8 65	 cmp	 eax, 101		; 00000065H
  0007f	75 0d		 jne	 SHORT $LN2@SetToExecI

; 180  : 			{
; 181  : 				SetJoinContext(JoinContext::JC_TUTORIAL);

  00081	b2 05		 mov	 dl, 5
  00083	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  00088	e8 00 00 00 00	 call	 ?SetJoinContext@PlayerJoinAction@mu2@@QEAAXW4Enum@JoinContext@2@@Z ; mu2::PlayerJoinAction::SetJoinContext
  0008d	90		 npad	 1
$LN2@SetToExecI:

; 182  : 			}
; 183  : 		}
; 184  : 
; 185  : 		m_toLocation.execId = toExecId;

  0008e	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00093	48 83 c0 28	 add	 rax, 40			; 00000028H
  00097	48 89 44 24 20	 mov	 QWORD PTR this$[rsp], rax
  0009c	48 8b 44 24 58	 mov	 rax, QWORD PTR toExecId$[rsp]
  000a1	8b 40 08	 mov	 eax, DWORD PTR [rax+8]
  000a4	48 8b 4c 24 20	 mov	 rcx, QWORD PTR this$[rsp]
  000a9	89 41 08	 mov	 DWORD PTR [rcx+8], eax
  000ac	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  000b1	48 8b 4c 24 58	 mov	 rcx, QWORD PTR toExecId$[rsp]
  000b6	8b 49 08	 mov	 ecx, DWORD PTR [rcx+8]
  000b9	89 48 08	 mov	 DWORD PTR [rax+8], ecx

; 186  : 	}

  000bc	48 83 c4 48	 add	 rsp, 72			; 00000048H
  000c0	c3		 ret	 0
?SetToExecId@PlayerJoinAction@mu2@@AEAAXAEBVExecutionZoneId@2@@Z ENDP ; mu2::PlayerJoinAction::SetToExecId
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
; File F:\Release_Branch\Server\Development\Framework\Math\Vector3.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
;	COMDAT ?InitContext@PlayerJoinAction@mu2@@AEAAXW4Enum@JoinContext@2@AEBVExecutionZoneId@2@HAEBVVector3@2@PEBUInstanceDungeonInfo@2@@Z
_TEXT	SEGMENT
this$ = 96
hConsole$1 = 104
hConsole$2 = 112
this$ = 144
eContext$ = 152
toExecId$ = 160
toIndexPosition$ = 168
toPos$ = 176
pDungeonInfo$ = 184
?InitContext@PlayerJoinAction@mu2@@AEAAXW4Enum@JoinContext@2@AEBVExecutionZoneId@2@HAEBVVector3@2@PEBUInstanceDungeonInfo@2@@Z PROC ; mu2::PlayerJoinAction::InitContext, COMDAT

; 69   : 	{

$LN11:
  00000	44 89 4c 24 20	 mov	 DWORD PTR [rsp+32], r9d
  00005	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  0000a	88 54 24 10	 mov	 BYTE PTR [rsp+16], dl
  0000e	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00013	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H

; 70   : 		VERIFY_RETURN(eContext != JoinContext::JC_NONE, );

  0001a	0f b6 84 24 98
	00 00 00	 movzx	 eax, BYTE PTR eContext$[rsp]
  00022	85 c0		 test	 eax, eax
  00024	0f 85 9e 00 00
	00		 jne	 $LN2@InitContex
  0002a	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  0002f	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  00035	48 89 44 24 68	 mov	 QWORD PTR hConsole$1[rsp], rax
  0003a	66 ba 0d 00	 mov	 dx, 13
  0003e	48 8b 4c 24 68	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  00043	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  00049	c7 44 24 50 46
	00 00 00	 mov	 DWORD PTR [rsp+80], 70	; 00000046H
  00051	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EP@HBIDHLFH@F?3?2Release_Branch?2Server?2Develo@
  00058	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  0005d	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CB@FJFBAPGB@eContext?5?$CB?$DN?5JoinContext?3?3JC_NON@
  00064	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  00069	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CD@ONJPOCCC@mu2?3?3PlayerJoinAction?3?3InitCont@
  00070	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  00075	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  0007c	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  00081	c7 44 24 28 46
	00 00 00	 mov	 DWORD PTR [rsp+40], 70	; 00000046H
  00089	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EP@HBIDHLFH@F?3?2Release_Branch?2Server?2Develo@
  00090	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  00095	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CD@ONJPOCCC@mu2?3?3PlayerJoinAction?3?3InitCont@
  0009c	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  000a2	ba 02 00 00 00	 mov	 edx, 2
  000a7	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  000ae	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  000b3	66 ba 07 00	 mov	 dx, 7
  000b7	48 8b 4c 24 68	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  000bc	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000c2	90		 npad	 1
  000c3	e9 98 01 00 00	 jmp	 $LN1@InitContex
$LN2@InitContex:

; 71   : 
; 72   : 		SetJoinContext(eContext);		

  000c8	0f b6 94 24 98
	00 00 00	 movzx	 edx, BYTE PTR eContext$[rsp]
  000d0	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  000d8	e8 00 00 00 00	 call	 ?SetJoinContext@PlayerJoinAction@mu2@@QEAAXW4Enum@JoinContext@2@@Z ; mu2::PlayerJoinAction::SetJoinContext
  000dd	90		 npad	 1

; 73   : 
; 74   : 		if (eContext != JoinContext::JC_LOGOUT)

  000de	0f b6 84 24 98
	00 00 00	 movzx	 eax, BYTE PTR eContext$[rsp]
  000e6	83 f8 06	 cmp	 eax, 6
  000e9	0f 84 c2 00 00
	00		 je	 $LN3@InitContex

; 75   : 		{
; 76   : 			VERIFY_RETURN(toExecId.IsValid() && eContext != JoinContext::JC_NONE, );

  000ef	48 8b 8c 24 a0
	00 00 00	 mov	 rcx, QWORD PTR toExecId$[rsp]
  000f7	e8 00 00 00 00	 call	 ?IsValid@ExecutionZoneId@mu2@@QEBA?B_NXZ ; mu2::ExecutionZoneId::IsValid
  000fc	0f b6 c0	 movzx	 eax, al
  000ff	85 c0		 test	 eax, eax
  00101	74 10		 je	 SHORT $LN5@InitContex
  00103	0f b6 84 24 98
	00 00 00	 movzx	 eax, BYTE PTR eContext$[rsp]
  0010b	85 c0		 test	 eax, eax
  0010d	0f 85 9e 00 00
	00		 jne	 $LN4@InitContex
$LN5@InitContex:
  00113	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  00118	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  0011e	48 89 44 24 70	 mov	 QWORD PTR hConsole$2[rsp], rax
  00123	66 ba 0d 00	 mov	 dx, 13
  00127	48 8b 4c 24 70	 mov	 rcx, QWORD PTR hConsole$2[rsp]
  0012c	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  00132	c7 44 24 50 4c
	00 00 00	 mov	 DWORD PTR [rsp+80], 76	; 0000004cH
  0013a	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EP@HBIDHLFH@F?3?2Release_Branch?2Server?2Develo@
  00141	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  00146	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0DH@CMCFJJFD@toExecId?4IsValid?$CI?$CJ?5?$CG?$CG?5eContext?5@
  0014d	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  00152	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CD@ONJPOCCC@mu2?3?3PlayerJoinAction?3?3InitCont@
  00159	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  0015e	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  00165	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  0016a	c7 44 24 28 4c
	00 00 00	 mov	 DWORD PTR [rsp+40], 76	; 0000004cH
  00172	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EP@HBIDHLFH@F?3?2Release_Branch?2Server?2Develo@
  00179	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  0017e	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CD@ONJPOCCC@mu2?3?3PlayerJoinAction?3?3InitCont@
  00185	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  0018b	ba 02 00 00 00	 mov	 edx, 2
  00190	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  00197	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  0019c	66 ba 07 00	 mov	 dx, 7
  001a0	48 8b 4c 24 70	 mov	 rcx, QWORD PTR hConsole$2[rsp]
  001a5	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  001ab	90		 npad	 1
  001ac	e9 af 00 00 00	 jmp	 $LN1@InitContex
$LN4@InitContex:
$LN3@InitContex:

; 77   : 		}
; 78   : 
; 79   : 		m_toLocation.indexPosition = toIndexPosition;

  001b1	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  001b9	8b 8c 24 a8 00
	00 00		 mov	 ecx, DWORD PTR toIndexPosition$[rsp]
  001c0	89 48 38	 mov	 DWORD PTR [rax+56], ecx

; 80   : 		m_toLocation.pos = toPos;

  001c3	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  001cb	48 83 c0 3c	 add	 rax, 60			; 0000003cH
  001cf	48 89 44 24 60	 mov	 QWORD PTR this$[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Math\Vector3.h

; 33   : 		if (this == &other)

  001d4	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR toPos$[rsp]
  001dc	48 39 44 24 60	 cmp	 QWORD PTR this$[rsp], rax
  001e1	75 02		 jne	 SHORT $LN9@InitContex

; 34   : 			return *this;

  001e3	eb 43		 jmp	 SHORT $LN8@InitContex
$LN9@InitContex:

; 35   : 
; 36   : 		x = other.x;

  001e5	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  001ea	48 8b 8c 24 b0
	00 00 00	 mov	 rcx, QWORD PTR toPos$[rsp]
  001f2	f3 0f 10 01	 movss	 xmm0, DWORD PTR [rcx]
  001f6	f3 0f 11 00	 movss	 DWORD PTR [rax], xmm0

; 37   : 		y = other.y;

  001fa	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  001ff	48 8b 8c 24 b0
	00 00 00	 mov	 rcx, QWORD PTR toPos$[rsp]
  00207	f3 0f 10 41 04	 movss	 xmm0, DWORD PTR [rcx+4]
  0020c	f3 0f 11 40 04	 movss	 DWORD PTR [rax+4], xmm0

; 38   : 		z = other.z;

  00211	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00216	48 8b 8c 24 b0
	00 00 00	 mov	 rcx, QWORD PTR toPos$[rsp]
  0021e	f3 0f 10 41 08	 movss	 xmm0, DWORD PTR [rcx+8]
  00223	f3 0f 11 40 08	 movss	 DWORD PTR [rax+8], xmm0
$LN8@InitContex:
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp

; 81   : 		SetToExecId(toExecId);		

  00228	48 8b 94 24 a0
	00 00 00	 mov	 rdx, QWORD PTR toExecId$[rsp]
  00230	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00238	e8 00 00 00 00	 call	 ?SetToExecId@PlayerJoinAction@mu2@@AEAAXAEBVExecutionZoneId@2@@Z ; mu2::PlayerJoinAction::SetToExecId
  0023d	90		 npad	 1

; 82   : 
; 83   : 		if (pDungeonInfo)

  0023e	48 83 bc 24 b8
	00 00 00 00	 cmp	 QWORD PTR pDungeonInfo$[rsp], 0
  00247	74 17		 je	 SHORT $LN6@InitContex

; 84   : 		{
; 85   : 			m_continent = pDungeonInfo->continent;

  00249	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00251	48 8b 8c 24 b8
	00 00 00	 mov	 rcx, QWORD PTR pDungeonInfo$[rsp]
  00259	0f b6 49 2c	 movzx	 ecx, BYTE PTR [rcx+44]
  0025d	88 48 48	 mov	 BYTE PTR [rax+72], cl
$LN6@InitContex:
$LN1@InitContex:

; 86   : 		}
; 87   : 	}

  00260	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  00267	c3		 ret	 0
?InitContext@PlayerJoinAction@mu2@@AEAAXW4Enum@JoinContext@2@AEBVExecutionZoneId@2@HAEBVVector3@2@PEBUInstanceDungeonInfo@2@@Z ENDP ; mu2::PlayerJoinAction::InitContext
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
;	COMDAT ?IsMultistageDungeon@PlayerJoinAction@mu2@@QEBA_NAEBVJoinZoneContextCurr@2@AEBVJoinZoneContextDest@2@@Z
_TEXT	SEGMENT
curWorldElem$ = 96
destWorldElem$ = 104
hConsole$1 = 112
$T2 = 120
$T3 = 128
this$ = 160
curr$ = 168
dest$ = 176
?IsMultistageDungeon@PlayerJoinAction@mu2@@QEBA_NAEBVJoinZoneContextCurr@2@AEBVJoinZoneContextDest@2@@Z PROC ; mu2::PlayerJoinAction::IsMultistageDungeon, COMDAT

; 236  : 	{

$LN12:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 81 ec 98 00
	00 00		 sub	 rsp, 152		; 00000098H
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  00016	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A ; mu2::ISingleton<mu2::GlobalLoadScript>::inst
  0001d	48 89 44 24 78	 mov	 QWORD PTR $T2[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp

; 237  : 		const WorldElem* curWorldElem = SCRIPTS.GetWorldScript(curr.execId.GetZoneIndex());

  00022	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR curr$[rsp]
  0002a	48 83 c0 10	 add	 rax, 16
  0002e	48 8b c8	 mov	 rcx, rax
  00031	e8 00 00 00 00	 call	 ?GetZoneIndex@ExecutionZoneId@mu2@@QEBAGXZ ; mu2::ExecutionZoneId::GetZoneIndex
  00036	48 8b 4c 24 78	 mov	 rcx, QWORD PTR $T2[rsp]
  0003b	0f b7 d0	 movzx	 edx, ax
  0003e	e8 00 00 00 00	 call	 ?GetWorldScript@GlobalLoadScript@mu2@@QEBAPEBUWorldElem@2@G@Z ; mu2::GlobalLoadScript::GetWorldScript
  00043	48 89 44 24 60	 mov	 QWORD PTR curWorldElem$[rsp], rax

; 238  : 		if (nullptr == curWorldElem || 0 == curWorldElem->groupId)

  00048	48 83 7c 24 60
	00		 cmp	 QWORD PTR curWorldElem$[rsp], 0
  0004e	74 0d		 je	 SHORT $LN3@IsMultista
  00050	48 8b 44 24 60	 mov	 rax, QWORD PTR curWorldElem$[rsp]
  00055	0f b7 40 02	 movzx	 eax, WORD PTR [rax+2]
  00059	85 c0		 test	 eax, eax
  0005b	75 07		 jne	 SHORT $LN2@IsMultista
$LN3@IsMultista:

; 239  : 		{
; 240  : 			return false;

  0005d	32 c0		 xor	 al, al
  0005f	e9 09 01 00 00	 jmp	 $LN1@IsMultista
$LN2@IsMultista:
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  00064	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A ; mu2::ISingleton<mu2::GlobalLoadScript>::inst
  0006b	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T3[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp

; 243  : 		const WorldElem* destWorldElem = SCRIPTS.GetWorldScript(dest.execId.GetZoneIndex());

  00073	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR dest$[rsp]
  0007b	48 83 c0 10	 add	 rax, 16
  0007f	48 8b c8	 mov	 rcx, rax
  00082	e8 00 00 00 00	 call	 ?GetZoneIndex@ExecutionZoneId@mu2@@QEBAGXZ ; mu2::ExecutionZoneId::GetZoneIndex
  00087	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR $T3[rsp]
  0008f	0f b7 d0	 movzx	 edx, ax
  00092	e8 00 00 00 00	 call	 ?GetWorldScript@GlobalLoadScript@mu2@@QEBAPEBUWorldElem@2@G@Z ; mu2::GlobalLoadScript::GetWorldScript
  00097	48 89 44 24 68	 mov	 QWORD PTR destWorldElem$[rsp], rax

; 244  : 		if (nullptr == destWorldElem)

  0009c	48 83 7c 24 68
	00		 cmp	 QWORD PTR destWorldElem$[rsp], 0
  000a2	0f 85 a9 00 00
	00		 jne	 $LN4@IsMultista

; 245  : 		{
; 246  : 			VERIFY_RETURN(destWorldElem, false);

  000a8	48 83 7c 24 68
	00		 cmp	 QWORD PTR destWorldElem$[rsp], 0
  000ae	0f 85 9d 00 00
	00		 jne	 $LN5@IsMultista
  000b4	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  000b9	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  000bf	48 89 44 24 70	 mov	 QWORD PTR hConsole$1[rsp], rax
  000c4	66 ba 0d 00	 mov	 dx, 13
  000c8	48 8b 4c 24 70	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  000cd	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000d3	c7 44 24 50 f6
	00 00 00	 mov	 DWORD PTR [rsp+80], 246	; 000000f6H
  000db	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EP@HBIDHLFH@F?3?2Release_Branch?2Server?2Develo@
  000e2	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  000e7	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0O@CDHODAOF@destWorldElem@
  000ee	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  000f3	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CL@NLMJGNPH@mu2?3?3PlayerJoinAction?3?3IsMultis@
  000fa	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  000ff	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  00106	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  0010b	c7 44 24 28 f6
	00 00 00	 mov	 DWORD PTR [rsp+40], 246	; 000000f6H
  00113	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EP@HBIDHLFH@F?3?2Release_Branch?2Server?2Develo@
  0011a	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  0011f	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CL@NLMJGNPH@mu2?3?3PlayerJoinAction?3?3IsMultis@
  00126	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  0012c	ba 02 00 00 00	 mov	 edx, 2
  00131	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  00138	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  0013d	66 ba 07 00	 mov	 dx, 7
  00141	48 8b 4c 24 70	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  00146	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  0014c	90		 npad	 1
  0014d	32 c0		 xor	 al, al
  0014f	eb 1c		 jmp	 SHORT $LN1@IsMultista
$LN5@IsMultista:
$LN4@IsMultista:

; 247  : 		}
; 248  : 
; 249  : 		if (curWorldElem->groupId != destWorldElem->groupId)

  00151	48 8b 44 24 60	 mov	 rax, QWORD PTR curWorldElem$[rsp]
  00156	0f b7 40 02	 movzx	 eax, WORD PTR [rax+2]
  0015a	48 8b 4c 24 68	 mov	 rcx, QWORD PTR destWorldElem$[rsp]
  0015f	0f b7 49 02	 movzx	 ecx, WORD PTR [rcx+2]
  00163	3b c1		 cmp	 eax, ecx
  00165	74 04		 je	 SHORT $LN6@IsMultista

; 250  : 		{
; 251  : 			return false;

  00167	32 c0		 xor	 al, al
  00169	eb 02		 jmp	 SHORT $LN1@IsMultista
$LN6@IsMultista:

; 252  : 		}
; 253  : 
; 254  : 		return true;

  0016b	b0 01		 mov	 al, 1
$LN1@IsMultista:

; 255  : 	}

  0016d	48 81 c4 98 00
	00 00		 add	 rsp, 152		; 00000098H
  00174	c3		 ret	 0
?IsMultistageDungeon@PlayerJoinAction@mu2@@QEBA_NAEBVJoinZoneContextCurr@2@AEBVJoinZoneContextDest@2@@Z ENDP ; mu2::PlayerJoinAction::IsMultistageDungeon
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
;	COMDAT ?GetTalismanSkillInfo@PlayerJoinAction@mu2@@QEAAXAEBVJoinZoneContextCurr@2@AEBVJoinZoneContextDest@2@AEAV?$map@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@AEAI@Z
_TEXT	SEGMENT
$T1 = 32
$T2 = 40
$T3 = 48
this$ = 80
curr$ = 88
dest$ = 96
talismanSkills$ = 104
talismanPoint$ = 112
?GetTalismanSkillInfo@PlayerJoinAction@mu2@@QEAAXAEBVJoinZoneContextCurr@2@AEBVJoinZoneContextDest@2@AEAV?$map@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@AEAI@Z PROC ; mu2::PlayerJoinAction::GetTalismanSkillInfo, COMDAT

; 219  : 	{

$LN410:
  00000	4c 89 4c 24 20	 mov	 QWORD PTR [rsp+32], r9
  00005	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 220  : 		if (false == IsMultistageDungeon(curr, dest))

  00018	4c 8b 44 24 60	 mov	 r8, QWORD PTR dest$[rsp]
  0001d	48 8b 54 24 58	 mov	 rdx, QWORD PTR curr$[rsp]
  00022	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  00027	e8 00 00 00 00	 call	 ?IsMultistageDungeon@PlayerJoinAction@mu2@@QEBA_NAEBVJoinZoneContextCurr@2@AEBVJoinZoneContextDest@2@@Z ; mu2::PlayerJoinAction::IsMultistageDungeon
  0002c	0f b6 c0	 movzx	 eax, al
  0002f	85 c0		 test	 eax, eax
  00031	75 1f		 jne	 SHORT $LN2@GetTalisma

; 221  : 		{
; 222  : 			m_talismanSkills.clear();

  00033	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 83 c0 68	 add	 rax, 104		; 00000068H
  0003c	48 8b c8	 mov	 rcx, rax
  0003f	e8 00 00 00 00	 call	 ?clear@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAXXZ ; std::_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::clear

; 223  : 			m_talismanPoint = 0;

  00044	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00049	c7 40 78 00 00
	00 00		 mov	 DWORD PTR [rax+120], 0

; 224  : 			return;

  00050	eb 56		 jmp	 SHORT $LN1@GetTalisma
$LN2@GetTalisma:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00052	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00057	48 83 c0 68	 add	 rax, 104		; 00000068H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0005b	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00060	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00065	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax

; 1209 :         return _Get_scary()->_Mysize;

  0006a	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
  0006f	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00073	48 89 44 24 30	 mov	 QWORD PTR $T3[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp

; 227  : 		if (0 < m_talismanSkills.size())

  00078	48 8b 44 24 30	 mov	 rax, QWORD PTR $T3[rsp]
  0007d	48 85 c0	 test	 rax, rax
  00080	76 17		 jbe	 SHORT $LN3@GetTalisma

; 228  : 		{
; 229  : 			talismanSkills = m_talismanSkills;

  00082	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00087	48 83 c0 68	 add	 rax, 104		; 00000068H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map

; 161  :         _Mybase::operator=(_Right);

  0008b	48 8b d0	 mov	 rdx, rax
  0008e	48 8b 4c 24 68	 mov	 rcx, QWORD PTR talismanSkills$[rsp]
  00093	e8 00 00 00 00	 call	 ??4?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAAEAV01@AEBV01@@Z ; std::_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::operator=
  00098	90		 npad	 1
$LN3@GetTalisma:
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp

; 232  : 		talismanPoint = m_talismanPoint;

  00099	48 8b 44 24 70	 mov	 rax, QWORD PTR talismanPoint$[rsp]
  0009e	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  000a3	8b 49 78	 mov	 ecx, DWORD PTR [rcx+120]
  000a6	89 08		 mov	 DWORD PTR [rax], ecx
$LN1@GetTalisma:

; 233  : 	}

  000a8	48 83 c4 48	 add	 rsp, 72			; 00000048H
  000ac	c3		 ret	 0
?GetTalismanSkillInfo@PlayerJoinAction@mu2@@QEAAXAEBVJoinZoneContextCurr@2@AEBVJoinZoneContextDest@2@AEAV?$map@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@AEAI@Z ENDP ; mu2::PlayerJoinAction::GetTalismanSkillInfo
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
;	COMDAT ?SetTalismanSkillInfo@PlayerJoinAction@mu2@@QEAAXAEBV?$map@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@I@Z
_TEXT	SEGMENT
$T1 = 32
$T2 = 40
$T3 = 48
this$ = 56
this$ = 80
talismanSkills$ = 88
talismanPoint$ = 96
?SetTalismanSkillInfo@PlayerJoinAction@mu2@@QEAAXAEBV?$map@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@I@Z PROC ; mu2::PlayerJoinAction::SetTalismanSkillInfo, COMDAT

; 209  : 	{

$LN324:
  00000	44 89 44 24 18	 mov	 DWORD PTR [rsp+24], r8d
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 48	 sub	 rsp, 72			; 00000048H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00013	48 8b 44 24 58	 mov	 rax, QWORD PTR talismanSkills$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00018	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  0001d	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00022	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax

; 1209 :         return _Get_scary()->_Mysize;

  00027	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
  0002c	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00030	48 89 44 24 30	 mov	 QWORD PTR $T3[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp

; 210  : 		if (0 < talismanSkills.size())

  00035	48 8b 44 24 30	 mov	 rax, QWORD PTR $T3[rsp]
  0003a	48 85 c0	 test	 rax, rax
  0003d	76 1e		 jbe	 SHORT $LN2@SetTalisma

; 211  : 		{
; 212  : 			m_talismanSkills = talismanSkills;

  0003f	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00044	48 83 c0 68	 add	 rax, 104		; 00000068H
  00048	48 89 44 24 38	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map

; 161  :         _Mybase::operator=(_Right);

  0004d	48 8b 54 24 58	 mov	 rdx, QWORD PTR talismanSkills$[rsp]
  00052	48 8b 4c 24 38	 mov	 rcx, QWORD PTR this$[rsp]
  00057	e8 00 00 00 00	 call	 ??4?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAAEAV01@AEBV01@@Z ; std::_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::operator=
  0005c	90		 npad	 1
$LN2@SetTalisma:
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp

; 215  : 		m_talismanPoint = talismanPoint;

  0005d	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00062	8b 4c 24 60	 mov	 ecx, DWORD PTR talismanPoint$[rsp]
  00066	89 48 78	 mov	 DWORD PTR [rax+120], ecx

; 216  : 	}

  00069	48 83 c4 48	 add	 rsp, 72			; 00000048H
  0006d	c3		 ret	 0
?SetTalismanSkillInfo@PlayerJoinAction@mu2@@QEAAXAEBV?$map@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@I@Z ENDP ; mu2::PlayerJoinAction::SetTalismanSkillInfo
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
;	COMDAT ?IsDead@PlayerJoinAction@mu2@@QEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsDead@PlayerJoinAction@mu2@@QEBA_NXZ PROC		; mu2::PlayerJoinAction::IsDead, COMDAT

; 204  : 	{

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 205  : 		return m_isDead;

  00005	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0000a	0f b6 40 60	 movzx	 eax, BYTE PTR [rax+96]

; 206  : 	}

  0000e	c3		 ret	 0
?IsDead@PlayerJoinAction@mu2@@QEBA_NXZ ENDP		; mu2::PlayerJoinAction::IsDead
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
;	COMDAT ?SetDead@PlayerJoinAction@mu2@@QEAAX_N@Z
_TEXT	SEGMENT
this$ = 8
isDead$ = 16
?SetDead@PlayerJoinAction@mu2@@QEAAX_N@Z PROC		; mu2::PlayerJoinAction::SetDead, COMDAT

; 199  : 	{

  00000	88 54 24 10	 mov	 BYTE PTR [rsp+16], dl
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 200  : 		m_isDead = isDead;

  00009	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0000e	0f b6 4c 24 10	 movzx	 ecx, BYTE PTR isDead$[rsp]
  00013	88 48 60	 mov	 BYTE PTR [rax+96], cl

; 201  : 	}

  00016	c3		 ret	 0
?SetDead@PlayerJoinAction@mu2@@QEAAX_N@Z ENDP		; mu2::PlayerJoinAction::SetDead
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
;	COMDAT ?SetJoinContext@PlayerJoinAction@mu2@@QEAAXW4Enum@JoinContext@2@@Z
_TEXT	SEGMENT
hConsole$1 = 96
hConsole$2 = 104
$T3 = 112
this$ = 144
eContext$ = 152
?SetJoinContext@PlayerJoinAction@mu2@@QEAAXW4Enum@JoinContext@2@@Z PROC ; mu2::PlayerJoinAction::SetJoinContext, COMDAT

; 159  : 	{

$LN7:
  00000	88 54 24 10	 mov	 BYTE PTR [rsp+16], dl
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H

; 160  : 		VERIFY_RETURN(eContext != JoinContext::JC_NONE, );

  00010	0f b6 84 24 98
	00 00 00	 movzx	 eax, BYTE PTR eContext$[rsp]
  00018	85 c0		 test	 eax, eax
  0001a	0f 85 9e 00 00
	00		 jne	 $LN2@SetJoinCon
  00020	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  00025	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  0002b	48 89 44 24 60	 mov	 QWORD PTR hConsole$1[rsp], rax
  00030	66 ba 0d 00	 mov	 dx, 13
  00034	48 8b 4c 24 60	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  00039	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  0003f	c7 44 24 50 a0
	00 00 00	 mov	 DWORD PTR [rsp+80], 160	; 000000a0H
  00047	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EP@HBIDHLFH@F?3?2Release_Branch?2Server?2Develo@
  0004e	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  00053	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CB@FJFBAPGB@eContext?5?$CB?$DN?5JoinContext?3?3JC_NON@
  0005a	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  0005f	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CG@DHHBHMAC@mu2?3?3PlayerJoinAction?3?3SetJoinC@
  00066	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  0006b	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  00072	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  00077	c7 44 24 28 a0
	00 00 00	 mov	 DWORD PTR [rsp+40], 160	; 000000a0H
  0007f	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EP@HBIDHLFH@F?3?2Release_Branch?2Server?2Develo@
  00086	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  0008b	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CG@DHHBHMAC@mu2?3?3PlayerJoinAction?3?3SetJoinC@
  00092	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  00098	ba 02 00 00 00	 mov	 edx, 2
  0009d	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  000a4	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  000a9	66 ba 07 00	 mov	 dx, 7
  000ad	48 8b 4c 24 60	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  000b2	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000b8	90		 npad	 1
  000b9	e9 d9 00 00 00	 jmp	 $LN1@SetJoinCon
$LN2@SetJoinCon:
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  000be	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@1VtheManagerJoinContext@2@A ; mu2::ISingleton<mu2::theManagerJoinContext>::inst
  000c5	48 89 44 24 70	 mov	 QWORD PTR $T3[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp

; 161  : 		m_pToContext = theManagerJoinContext::GetInstance()[eContext];

  000ca	48 8b 44 24 70	 mov	 rax, QWORD PTR $T3[rsp]
  000cf	0f b6 94 24 98
	00 00 00	 movzx	 edx, BYTE PTR eContext$[rsp]
  000d7	48 8b c8	 mov	 rcx, rax
  000da	e8 00 00 00 00	 call	 ??AtheManagerJoinContext@mu2@@QEBAPEAVJoinContextBase@1@W4Enum@JoinContext@1@@Z ; mu2::theManagerJoinContext::operator[]
  000df	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  000e7	48 89 41 18	 mov	 QWORD PTR [rcx+24], rax

; 162  : 		VERIFY_RETURN(m_pToContext, );

  000eb	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000f3	48 83 78 18 00	 cmp	 QWORD PTR [rax+24], 0
  000f8	0f 85 99 00 00
	00		 jne	 $LN3@SetJoinCon
  000fe	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  00103	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  00109	48 89 44 24 68	 mov	 QWORD PTR hConsole$2[rsp], rax
  0010e	66 ba 0d 00	 mov	 dx, 13
  00112	48 8b 4c 24 68	 mov	 rcx, QWORD PTR hConsole$2[rsp]
  00117	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  0011d	c7 44 24 50 a2
	00 00 00	 mov	 DWORD PTR [rsp+80], 162	; 000000a2H
  00125	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EP@HBIDHLFH@F?3?2Release_Branch?2Server?2Develo@
  0012c	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  00131	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0N@MDLFKPJI@m_pToContext@
  00138	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  0013d	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CG@DHHBHMAC@mu2?3?3PlayerJoinAction?3?3SetJoinC@
  00144	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  00149	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  00150	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  00155	c7 44 24 28 a2
	00 00 00	 mov	 DWORD PTR [rsp+40], 162	; 000000a2H
  0015d	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EP@HBIDHLFH@F?3?2Release_Branch?2Server?2Develo@
  00164	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  00169	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CG@DHHBHMAC@mu2?3?3PlayerJoinAction?3?3SetJoinC@
  00170	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  00176	ba 02 00 00 00	 mov	 edx, 2
  0017b	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  00182	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  00187	66 ba 07 00	 mov	 dx, 7
  0018b	48 8b 4c 24 68	 mov	 rcx, QWORD PTR hConsole$2[rsp]
  00190	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  00196	90		 npad	 1
$LN3@SetJoinCon:
$LN1@SetJoinCon:

; 163  : 	}

  00197	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  0019e	c3		 ret	 0
?SetJoinContext@PlayerJoinAction@mu2@@QEAAXW4Enum@JoinContext@2@@Z ENDP ; mu2::PlayerJoinAction::SetJoinContext
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
;	COMDAT ?IsWorldLogout@PlayerJoinAction@mu2@@QEBA_NXZ
_TEXT	SEGMENT
tv68 = 0
$T1 = 8
$T2 = 16
this$ = 48
?IsWorldLogout@PlayerJoinAction@mu2@@QEBA_NXZ PROC	; mu2::PlayerJoinAction::IsWorldLogout, COMDAT

; 64   : 	{

$LN12:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1309 :         return _Ptr;

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8b 40 50	 mov	 rax, QWORD PTR [rax+80]
  00012	48 89 44 24 08	 mov	 QWORD PTR $T1[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h

; 191  : 		return ptr.get();

  00017	48 8b 44 24 08	 mov	 rax, QWORD PTR $T1[rsp]
  0001c	48 89 44 24 10	 mov	 QWORD PTR $T2[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp

; 65   : 		return m_ELoopbackWorldLogout.RawPtr() != NULL;

  00021	48 8b 44 24 10	 mov	 rax, QWORD PTR $T2[rsp]
  00026	48 85 c0	 test	 rax, rax
  00029	74 09		 je	 SHORT $LN3@IsWorldLog
  0002b	c7 04 24 01 00
	00 00		 mov	 DWORD PTR tv68[rsp], 1
  00032	eb 07		 jmp	 SHORT $LN4@IsWorldLog
$LN3@IsWorldLog:
  00034	c7 04 24 00 00
	00 00		 mov	 DWORD PTR tv68[rsp], 0
$LN4@IsWorldLog:
  0003b	0f b6 04 24	 movzx	 eax, BYTE PTR tv68[rsp]

; 66   : 	}

  0003f	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00043	c3		 ret	 0
?IsWorldLogout@PlayerJoinAction@mu2@@QEBA_NXZ ENDP	; mu2::PlayerJoinAction::IsWorldLogout
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
;	COMDAT ?GetELoopbackWorldLogout@PlayerJoinAction@mu2@@QEAA?AV?$SmartPtrEx@UEvent@mu2@@@2@XZ
_TEXT	SEGMENT
$T1 = 32
this$ = 40
this$ = 64
__$ReturnUdt$ = 72
?GetELoopbackWorldLogout@PlayerJoinAction@mu2@@QEAA?AV?$SmartPtrEx@UEvent@mu2@@@2@XZ PROC ; mu2::PlayerJoinAction::GetELoopbackWorldLogout, COMDAT

; 59   : 	{

$LN123:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 38	 sub	 rsp, 56			; 00000038H
  0000e	c7 44 24 20 00
	00 00 00	 mov	 DWORD PTR $T1[rsp], 0
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h

; 169  : 	{

  00016	48 8b 44 24 48	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]
  0001b	48 89 44 24 28	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1452 :     element_type* _Ptr{nullptr};

  00020	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00025	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0

; 1453 :     _Ref_count_base* _Rep{nullptr};

  0002c	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00031	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h

; 175  : 		this->ptr = r.ptr;

  00039	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0003e	48 83 c0 50	 add	 rax, 80			; 00000050H
  00042	48 8b 4c 24 48	 mov	 rcx, QWORD PTR __$ReturnUdt$[rsp]
  00047	48 8b d0	 mov	 rdx, rax
  0004a	e8 00 00 00 00	 call	 ??4?$shared_ptr@UEvent@mu2@@@std@@QEAAAEAV01@AEBV01@@Z ; std::shared_ptr<mu2::Event>::operator=
  0004f	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp

; 60   : 		return m_ELoopbackWorldLogout;

  00050	8b 44 24 20	 mov	 eax, DWORD PTR $T1[rsp]
  00054	83 c8 01	 or	 eax, 1
  00057	89 44 24 20	 mov	 DWORD PTR $T1[rsp], eax
  0005b	48 8b 44 24 48	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]

; 61   : 	}

  00060	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00064	c3		 ret	 0
?GetELoopbackWorldLogout@PlayerJoinAction@mu2@@QEAA?AV?$SmartPtrEx@UEvent@mu2@@@2@XZ ENDP ; mu2::PlayerJoinAction::GetELoopbackWorldLogout
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
;	COMDAT ?SetELoopbackWorldLogout@PlayerJoinAction@mu2@@QEAAXAEBV?$SmartPtrEx@UEvent@mu2@@@2@@Z
_TEXT	SEGMENT
this$ = 48
e$ = 56
?SetELoopbackWorldLogout@PlayerJoinAction@mu2@@QEAAXAEBV?$SmartPtrEx@UEvent@mu2@@@2@@Z PROC ; mu2::PlayerJoinAction::SetELoopbackWorldLogout, COMDAT

; 54   : 	{	

$LN109:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 55   : 		m_ELoopbackWorldLogout = e;

  0000e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00013	48 83 c0 50	 add	 rax, 80			; 00000050H
  00017	48 8b 54 24 38	 mov	 rdx, QWORD PTR e$[rsp]
  0001c	48 8b c8	 mov	 rcx, rax
  0001f	e8 00 00 00 00	 call	 ??4?$SmartPtrEx@UEvent@mu2@@@mu2@@QEAAAEAV01@AEBV01@@Z ; mu2::SmartPtrEx<mu2::Event>::operator=
  00024	90		 npad	 1

; 56   : 	}

  00025	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00029	c3		 ret	 0
?SetELoopbackWorldLogout@PlayerJoinAction@mu2@@QEAAXAEBV?$SmartPtrEx@UEvent@mu2@@@2@@Z ENDP ; mu2::PlayerJoinAction::SetELoopbackWorldLogout
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
; File F:\Release_Branch\Server\Development\Framework\Entity\Action.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EntityPlayer.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
; File F:\Release_Branch\Server\Development\Framework\Entity\State.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
; File F:\Release_Branch\Server\Development\Framework\Entity\State.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
;	COMDAT ?IsEnterableJoinState@PlayerJoinAction@mu2@@QEBA_NXZ
_TEXT	SEGMENT
$T1 = 96
player$ = 104
pCurr$ = 112
hConsole$2 = 120
hConsole$3 = 128
hConsole$4 = 136
$T5 = 144
$T6 = 152
tv161 = 160
$T7 = 168
tv176 = 176
tv156 = 184
$T8 = 192
__$ArrayPad$ = 224
this$ = 256
?IsEnterableJoinState@PlayerJoinAction@mu2@@QEBA_NXZ PROC ; mu2::PlayerJoinAction::IsEnterableJoinState, COMDAT

; 91   : 	{

$LN121:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec f8 00
	00 00		 sub	 rsp, 248		; 000000f8H
  0000c	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR __security_cookie
  00013	48 33 c4	 xor	 rax, rsp
  00016	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR __$ArrayPad$[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Entity\Action.h

; 29   : 	inline Entity* GetOwner() const { return m_owner; }

  0001e	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00026	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0002a	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T5[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EntityPlayer.h

; 461  : 			return static_cast<EntityPlayer*>(GetOwner());

  00032	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR $T5[rsp]
  0003a	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR $T6[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp

; 92   : 		EntityPlayer* player = GetOwnerPlayer();

  00042	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR $T6[rsp]
  0004a	48 89 44 24 68	 mov	 QWORD PTR player$[rsp], rax

; 93   : 		VERIFY_RETURN(player, false);

  0004f	48 83 7c 24 68
	00		 cmp	 QWORD PTR player$[rsp], 0
  00055	0f 85 a0 00 00
	00		 jne	 $LN2@IsEnterabl
  0005b	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  00060	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  00066	48 89 44 24 78	 mov	 QWORD PTR hConsole$2[rsp], rax
  0006b	66 ba 0d 00	 mov	 dx, 13
  0006f	48 8b 4c 24 78	 mov	 rcx, QWORD PTR hConsole$2[rsp]
  00074	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  0007a	c7 44 24 50 5d
	00 00 00	 mov	 DWORD PTR [rsp+80], 93	; 0000005dH
  00082	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EP@HBIDHLFH@F?3?2Release_Branch?2Server?2Develo@
  00089	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  0008e	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_06BALNJMNP@player@
  00095	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  0009a	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CM@DBNLLACM@mu2?3?3PlayerJoinAction?3?3IsEntera@
  000a1	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  000a6	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  000ad	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  000b2	c7 44 24 28 5d
	00 00 00	 mov	 DWORD PTR [rsp+40], 93	; 0000005dH
  000ba	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EP@HBIDHLFH@F?3?2Release_Branch?2Server?2Develo@
  000c1	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  000c6	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CM@DBNLLACM@mu2?3?3PlayerJoinAction?3?3IsEntera@
  000cd	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  000d3	ba 02 00 00 00	 mov	 edx, 2
  000d8	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  000df	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  000e4	66 ba 07 00	 mov	 dx, 7
  000e8	48 8b 4c 24 78	 mov	 rcx, QWORD PTR hConsole$2[rsp]
  000ed	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000f3	90		 npad	 1
  000f4	32 c0		 xor	 al, al
  000f6	e9 d3 01 00 00	 jmp	 $LN1@IsEnterabl
$LN2@IsEnterabl:

; 94   : 
; 95   : 		StatePlayer* pCurr = player->GetCurrentState();

  000fb	48 8b 4c 24 68	 mov	 rcx, QWORD PTR player$[rsp]
  00100	e8 00 00 00 00	 call	 ?GetCurrentState@EntityPlayer@mu2@@QEAAPEAVStatePlayer@2@XZ ; mu2::EntityPlayer::GetCurrentState
  00105	48 89 44 24 70	 mov	 QWORD PTR pCurr$[rsp], rax

; 96   : 		VERIFY_RETURN(pCurr, false);

  0010a	48 83 7c 24 70
	00		 cmp	 QWORD PTR pCurr$[rsp], 0
  00110	0f 85 a9 00 00
	00		 jne	 $LN3@IsEnterabl
  00116	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  0011b	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  00121	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR hConsole$3[rsp], rax
  00129	66 ba 0d 00	 mov	 dx, 13
  0012d	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR hConsole$3[rsp]
  00135	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  0013b	c7 44 24 50 60
	00 00 00	 mov	 DWORD PTR [rsp+80], 96	; 00000060H
  00143	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EP@HBIDHLFH@F?3?2Release_Branch?2Server?2Develo@
  0014a	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  0014f	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_05EMNBIGOA@pCurr@
  00156	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  0015b	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CM@DBNLLACM@mu2?3?3PlayerJoinAction?3?3IsEntera@
  00162	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  00167	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  0016e	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  00173	c7 44 24 28 60
	00 00 00	 mov	 DWORD PTR [rsp+40], 96	; 00000060H
  0017b	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EP@HBIDHLFH@F?3?2Release_Branch?2Server?2Develo@
  00182	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  00187	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CM@DBNLLACM@mu2?3?3PlayerJoinAction?3?3IsEntera@
  0018e	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  00194	ba 02 00 00 00	 mov	 edx, 2
  00199	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  001a0	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  001a5	66 ba 07 00	 mov	 dx, 7
  001a9	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR hConsole$3[rsp]
  001b1	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  001b7	90		 npad	 1
  001b8	32 c0		 xor	 al, al
  001ba	e9 0f 01 00 00	 jmp	 $LN1@IsEnterabl
$LN3@IsEnterabl:
; File F:\Release_Branch\Server\Development\Framework\Entity\State.h

; 105  : 	return m_id;

  001bf	48 8b 44 24 70	 mov	 rax, QWORD PTR pCurr$[rsp]
  001c4	8b 40 10	 mov	 eax, DWORD PTR [rax+16]
  001c7	89 44 24 60	 mov	 DWORD PTR $T1[rsp], eax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp

; 98   : 		if (pCurr->GetId() == PlayerState::PLAYER_STATE_JOIN_EXECUTION)

  001cb	8b 44 24 60	 mov	 eax, DWORD PTR $T1[rsp]
  001cf	83 f8 04	 cmp	 eax, 4
  001d2	0f 85 f4 00 00
	00		 jne	 $LN4@IsEnterabl

; 100  : 			MU2_ERROR_LOG(LogCategory::JOIN, "IsEnterableJoinState> failed - player(%s), currState(%s)", player->ToString().c_str(), pCurr->GetName());

  001d8	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  001dd	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  001e3	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR hConsole$4[rsp], rax
  001eb	66 ba 0d 00	 mov	 dx, 13
  001ef	48 8b 8c 24 88
	00 00 00	 mov	 rcx, QWORD PTR hConsole$4[rsp]
  001f7	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  001fd	90		 npad	 1
  001fe	48 8d 94 24 c0
	00 00 00	 lea	 rdx, QWORD PTR $T8[rsp]
  00206	48 8b 4c 24 68	 mov	 rcx, QWORD PTR player$[rsp]
  0020b	e8 00 00 00 00	 call	 ?ToString@EntityPlayer@mu2@@QEAA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ ; mu2::EntityPlayer::ToString
  00210	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR tv161[rsp], rax
  00218	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR tv161[rsp]
  00220	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR tv176[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Entity\State.h

; 110  : 	return m_name;

  00228	48 8b 44 24 70	 mov	 rax, QWORD PTR pCurr$[rsp]
  0022d	48 8b 40 50	 mov	 rax, QWORD PTR [rax+80]
  00231	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T7[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp

; 100  : 			MU2_ERROR_LOG(LogCategory::JOIN, "IsEnterableJoinState> failed - player(%s), currState(%s)", player->ToString().c_str(), pCurr->GetName());

  00239	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T7[rsp]
  00241	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR tv156[rsp], rax
  00249	48 8b 8c 24 b0
	00 00 00	 mov	 rcx, QWORD PTR tv176[rsp]
  00251	e8 00 00 00 00	 call	 ?c_str@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBAPEBDXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::c_str
  00256	48 8b 8c 24 b8
	00 00 00	 mov	 rcx, QWORD PTR tv156[rsp]
  0025e	48 89 4c 24 40	 mov	 QWORD PTR [rsp+64], rcx
  00263	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  00268	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0DJ@DJGHFFHF@IsEnterableJoinState?$DO?5failed?5?9?5@
  0026f	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  00274	c7 44 24 28 64
	00 00 00	 mov	 DWORD PTR [rsp+40], 100	; 00000064H
  0027c	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EP@HBIDHLFH@F?3?2Release_Branch?2Server?2Develo@
  00283	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  00288	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CM@DBNLLACM@mu2?3?3PlayerJoinAction?3?3IsEntera@
  0028f	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  00295	ba 15 00 00 00	 mov	 edx, 21
  0029a	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  002a1	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  002a6	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1383 :         _Tidy_deallocate();

  002a7	48 8d 8c 24 c0
	00 00 00	 lea	 rcx, QWORD PTR $T8[rsp]
  002af	e8 00 00 00 00	 call	 ?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Tidy_deallocate
  002b4	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp

; 100  : 			MU2_ERROR_LOG(LogCategory::JOIN, "IsEnterableJoinState> failed - player(%s), currState(%s)", player->ToString().c_str(), pCurr->GetName());

  002b5	66 ba 07 00	 mov	 dx, 7
  002b9	48 8b 8c 24 88
	00 00 00	 mov	 rcx, QWORD PTR hConsole$4[rsp]
  002c1	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  002c7	90		 npad	 1

; 101  : 			return false;

  002c8	32 c0		 xor	 al, al
  002ca	eb 02		 jmp	 SHORT $LN1@IsEnterabl
$LN4@IsEnterabl:

; 102  : 		}
; 103  : 
; 104  : 		return true;

  002cc	b0 01		 mov	 al, 1
$LN1@IsEnterabl:

; 105  : 	}

  002ce	48 8b 8c 24 e0
	00 00 00	 mov	 rcx, QWORD PTR __$ArrayPad$[rsp]
  002d6	48 33 cc	 xor	 rcx, rsp
  002d9	e8 00 00 00 00	 call	 __security_check_cookie
  002de	48 81 c4 f8 00
	00 00		 add	 rsp, 248		; 000000f8H
  002e5	c3		 ret	 0
?IsEnterableJoinState@PlayerJoinAction@mu2@@QEBA_NXZ ENDP ; mu2::PlayerJoinAction::IsEnterableJoinState
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 96
player$ = 104
pCurr$ = 112
hConsole$2 = 120
hConsole$3 = 128
hConsole$4 = 136
$T5 = 144
$T6 = 152
tv161 = 160
$T7 = 168
tv176 = 176
tv156 = 184
$T8 = 192
__$ArrayPad$ = 224
this$ = 256
?dtor$0@?0??IsEnterableJoinState@PlayerJoinAction@mu2@@QEBA_NXZ@4HA PROC ; `mu2::PlayerJoinAction::IsEnterableJoinState'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8d 8d c0 00
	00 00		 lea	 rcx, QWORD PTR $T8[rbp]
  00010	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$0@?0??IsEnterableJoinState@PlayerJoinAction@mu2@@QEBA_NXZ@4HA ENDP ; `mu2::PlayerJoinAction::IsEnterableJoinState'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
;	COMDAT ?GetToExecution@PlayerJoinAction@mu2@@QEAAAEBVExecution@2@XZ
_TEXT	SEGMENT
$T1 = 32
this$ = 64
?GetToExecution@PlayerJoinAction@mu2@@QEAAAEBVExecution@2@XZ PROC ; mu2::PlayerJoinAction::GetToExecution, COMDAT

; 194  : 	{

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  00009	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A ; mu2::ISingleton<mu2::ExecutionManager>::inst
  00010	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp

; 195  : 		return theExecutionManager.GetExecution(m_toLocation.execId);

  00015	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 83 c0 28	 add	 rax, 40			; 00000028H
  0001e	48 8b 4c 24 20	 mov	 rcx, QWORD PTR $T1[rsp]
  00023	48 8b d0	 mov	 rdx, rax
  00026	e8 00 00 00 00	 call	 ?GetExecution@ExecutionManager@mu2@@QEAAAEBVExecution@2@AEBVExecutionZoneId@2@@Z ; mu2::ExecutionManager::GetExecution

; 196  : 	}

  0002b	48 83 c4 38	 add	 rsp, 56			; 00000038H
  0002f	c3		 ret	 0
?GetToExecution@PlayerJoinAction@mu2@@QEAAAEBVExecution@2@XZ ENDP ; mu2::PlayerJoinAction::GetToExecution
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
;	COMDAT ?GetToLocation@PlayerJoinAction@mu2@@QEBAAEBUExecLocation@2@XZ
_TEXT	SEGMENT
this$ = 8
?GetToLocation@PlayerJoinAction@mu2@@QEBAAEBUExecLocation@2@XZ PROC ; mu2::PlayerJoinAction::GetToLocation, COMDAT

; 166  : 	{

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 167  : 		return m_toLocation;

  00005	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0000a	48 83 c0 20	 add	 rax, 32			; 00000020H

; 168  : 	}

  0000e	c3		 ret	 0
?GetToLocation@PlayerJoinAction@mu2@@QEBAAEBUExecLocation@2@XZ ENDP ; mu2::PlayerJoinAction::GetToLocation
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
;	COMDAT ?GetContinent@PlayerJoinAction@mu2@@QEBAEXZ
_TEXT	SEGMENT
this$ = 8
?GetContinent@PlayerJoinAction@mu2@@QEBAEXZ PROC	; mu2::PlayerJoinAction::GetContinent, COMDAT

; 189  : 	{ 

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 190  : 		return m_continent; 

  00005	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0000a	0f b6 40 48	 movzx	 eax, BYTE PTR [rax+72]

; 191  : 	}

  0000e	c3		 ret	 0
?GetContinent@PlayerJoinAction@mu2@@QEBAEXZ ENDP	; mu2::PlayerJoinAction::GetContinent
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
;	COMDAT ?GetPortalType4Client@PlayerJoinAction@mu2@@QEBA?AW4Enum@PortalType@2@XZ
_TEXT	SEGMENT
this$ = 8
?GetPortalType4Client@PlayerJoinAction@mu2@@QEBA?AW4Enum@PortalType@2@XZ PROC ; mu2::PlayerJoinAction::GetPortalType4Client, COMDAT

; 143  : 	{

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 144  : 		return m_eCurrProtalType4Client;

  00005	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0000a	0f b6 40 49	 movzx	 eax, BYTE PTR [rax+73]

; 145  : 	}

  0000e	c3		 ret	 0
?GetPortalType4Client@PlayerJoinAction@mu2@@QEBA?AW4Enum@PortalType@2@XZ ENDP ; mu2::PlayerJoinAction::GetPortalType4Client
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
;	COMDAT ?GetJoinContext@PlayerJoinAction@mu2@@QEBA?BW4Enum@JoinContext@2@XZ
_TEXT	SEGMENT
hConsole$1 = 96
this$ = 128
?GetJoinContext@PlayerJoinAction@mu2@@QEBA?BW4Enum@JoinContext@2@XZ PROC ; mu2::PlayerJoinAction::GetJoinContext, COMDAT

; 153  : 	{

$LN4:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 78	 sub	 rsp, 120		; 00000078H

; 154  : 		VERIFY_RETURN(m_pToContext, JoinContext::JC_NONE);

  00009	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00011	48 83 78 18 00	 cmp	 QWORD PTR [rax+24], 0
  00016	0f 85 9d 00 00
	00		 jne	 $LN2@GetJoinCon
  0001c	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  00021	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  00027	48 89 44 24 60	 mov	 QWORD PTR hConsole$1[rsp], rax
  0002c	66 ba 0d 00	 mov	 dx, 13
  00030	48 8b 4c 24 60	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  00035	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  0003b	c7 44 24 50 9a
	00 00 00	 mov	 DWORD PTR [rsp+80], 154	; 0000009aH
  00043	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EP@HBIDHLFH@F?3?2Release_Branch?2Server?2Develo@
  0004a	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  0004f	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0N@MDLFKPJI@m_pToContext@
  00056	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  0005b	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CG@PJGPIGCO@mu2?3?3PlayerJoinAction?3?3GetJoinC@
  00062	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  00067	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  0006e	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  00073	c7 44 24 28 9a
	00 00 00	 mov	 DWORD PTR [rsp+40], 154	; 0000009aH
  0007b	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EP@HBIDHLFH@F?3?2Release_Branch?2Server?2Develo@
  00082	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  00087	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CG@PJGPIGCO@mu2?3?3PlayerJoinAction?3?3GetJoinC@
  0008e	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  00094	ba 02 00 00 00	 mov	 edx, 2
  00099	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  000a0	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  000a5	66 ba 07 00	 mov	 dx, 7
  000a9	48 8b 4c 24 60	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  000ae	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000b4	90		 npad	 1
  000b5	32 c0		 xor	 al, al
  000b7	eb 1e		 jmp	 SHORT $LN1@GetJoinCon
$LN2@GetJoinCon:

; 155  : 		return m_pToContext->GetContext();

  000b9	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000c1	48 8b 40 18	 mov	 rax, QWORD PTR [rax+24]
  000c5	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  000cd	48 8b 49 18	 mov	 rcx, QWORD PTR [rcx+24]
  000d1	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000d4	ff 50 08	 call	 QWORD PTR [rax+8]
$LN1@GetJoinCon:

; 156  : 	}

  000d7	48 83 c4 78	 add	 rsp, 120		; 00000078H
  000db	c3		 ret	 0
?GetJoinContext@PlayerJoinAction@mu2@@QEBA?BW4Enum@JoinContext@2@XZ ENDP ; mu2::PlayerJoinAction::GetJoinContext
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
;	COMDAT ?GetJoinContextBase@PlayerJoinAction@mu2@@QEBAAEBVJoinContextBase@2@XZ
_TEXT	SEGMENT
this$ = 8
?GetJoinContextBase@PlayerJoinAction@mu2@@QEBAAEBVJoinContextBase@2@XZ PROC ; mu2::PlayerJoinAction::GetJoinContextBase, COMDAT

; 148  : 	{	

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 149  : 		return *m_pToContext;

  00005	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0000a	48 8b 40 18	 mov	 rax, QWORD PTR [rax+24]

; 150  : 	}

  0000e	c3		 ret	 0
?GetJoinContextBase@PlayerJoinAction@mu2@@QEBAAEBVJoinContextBase@2@XZ ENDP ; mu2::PlayerJoinAction::GetJoinContextBase
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
; File F:\Release_Branch\Server\Development\Framework\Entity\Action.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EntityPlayer.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
;	COMDAT ?TranStateJoinExecution@PlayerJoinAction@mu2@@QEAA?AW4Error@ErrorJoin@2@W4Enum@JoinContext@2@AEBVExecutionZoneId@2@HAEBVVector3@2@PEBUInstanceDungeonInfo@2@@Z
_TEXT	SEGMENT
player$ = 96
hConsole$1 = 104
hConsole$2 = 112
hConsole$3 = 120
hConsole$4 = 128
$T5 = 136
$T6 = 144
this$ = 176
eContext$ = 184
toExecId$ = 192
toIndexPosition$ = 200
toPos$ = 208
pDungeonInfo$ = 216
?TranStateJoinExecution@PlayerJoinAction@mu2@@QEAA?AW4Error@ErrorJoin@2@W4Enum@JoinContext@2@AEBVExecutionZoneId@2@HAEBVVector3@2@PEBUInstanceDungeonInfo@2@@Z PROC ; mu2::PlayerJoinAction::TranStateJoinExecution, COMDAT

; 108  : 	{

$LN13:
  00000	44 89 4c 24 20	 mov	 DWORD PTR [rsp+32], r9d
  00005	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  0000a	88 54 24 10	 mov	 BYTE PTR [rsp+16], dl
  0000e	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00013	48 81 ec a8 00
	00 00		 sub	 rsp, 168		; 000000a8H

; 109  : 		VERIFY_RETURN(eContext != JoinContext::JC_NONE, ErrorJoin::InvalidJoinContext);

  0001a	0f b6 84 24 b8
	00 00 00	 movzx	 eax, BYTE PTR eContext$[rsp]
  00022	85 c0		 test	 eax, eax
  00024	0f 85 a3 00 00
	00		 jne	 $LN2@TranStateJ
  0002a	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  0002f	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  00035	48 89 44 24 68	 mov	 QWORD PTR hConsole$1[rsp], rax
  0003a	66 ba 0d 00	 mov	 dx, 13
  0003e	48 8b 4c 24 68	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  00043	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  00049	c7 44 24 50 6d
	00 00 00	 mov	 DWORD PTR [rsp+80], 109	; 0000006dH
  00051	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EP@HBIDHLFH@F?3?2Release_Branch?2Server?2Develo@
  00058	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  0005d	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CB@FJFBAPGB@eContext?5?$CB?$DN?5JoinContext?3?3JC_NON@
  00064	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  00069	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CO@LEIOINCE@mu2?3?3PlayerJoinAction?3?3TranStat@
  00070	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  00075	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  0007c	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  00081	c7 44 24 28 6d
	00 00 00	 mov	 DWORD PTR [rsp+40], 109	; 0000006dH
  00089	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EP@HBIDHLFH@F?3?2Release_Branch?2Server?2Develo@
  00090	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  00095	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CO@LEIOINCE@mu2?3?3PlayerJoinAction?3?3TranStat@
  0009c	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  000a2	ba 02 00 00 00	 mov	 edx, 2
  000a7	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  000ae	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  000b3	66 ba 07 00	 mov	 dx, 7
  000b7	48 8b 4c 24 68	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  000bc	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000c2	90		 npad	 1
  000c3	b8 4c 1b 06 00	 mov	 eax, 400204		; 00061b4cH
  000c8	e9 e8 02 00 00	 jmp	 $LN1@TranStateJ
$LN2@TranStateJ:
; File F:\Release_Branch\Server\Development\Framework\Entity\Action.h

; 29   : 	inline Entity* GetOwner() const { return m_owner; }

  000cd	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000d5	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  000d9	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T5[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EntityPlayer.h

; 461  : 			return static_cast<EntityPlayer*>(GetOwner());

  000e1	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T5[rsp]
  000e9	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T6[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp

; 111  : 		EntityPlayer* player = GetOwnerPlayer();

  000f1	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR $T6[rsp]
  000f9	48 89 44 24 60	 mov	 QWORD PTR player$[rsp], rax

; 112  : 		VERIFY_RETURN(player, ErrorJoin::playerNotFound);

  000fe	48 83 7c 24 60
	00		 cmp	 QWORD PTR player$[rsp], 0
  00104	0f 85 a3 00 00
	00		 jne	 $LN3@TranStateJ
  0010a	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  0010f	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  00115	48 89 44 24 70	 mov	 QWORD PTR hConsole$2[rsp], rax
  0011a	66 ba 0d 00	 mov	 dx, 13
  0011e	48 8b 4c 24 70	 mov	 rcx, QWORD PTR hConsole$2[rsp]
  00123	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  00129	c7 44 24 50 70
	00 00 00	 mov	 DWORD PTR [rsp+80], 112	; 00000070H
  00131	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EP@HBIDHLFH@F?3?2Release_Branch?2Server?2Develo@
  00138	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  0013d	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_06BALNJMNP@player@
  00144	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  00149	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CO@LEIOINCE@mu2?3?3PlayerJoinAction?3?3TranStat@
  00150	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  00155	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  0015c	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  00161	c7 44 24 28 70
	00 00 00	 mov	 DWORD PTR [rsp+40], 112	; 00000070H
  00169	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EP@HBIDHLFH@F?3?2Release_Branch?2Server?2Develo@
  00170	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  00175	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CO@LEIOINCE@mu2?3?3PlayerJoinAction?3?3TranStat@
  0017c	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  00182	ba 02 00 00 00	 mov	 edx, 2
  00187	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  0018e	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  00193	66 ba 07 00	 mov	 dx, 7
  00197	48 8b 4c 24 70	 mov	 rcx, QWORD PTR hConsole$2[rsp]
  0019c	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  001a2	90		 npad	 1
  001a3	b8 01 1b 06 00	 mov	 eax, 400129		; 00061b01H
  001a8	e9 08 02 00 00	 jmp	 $LN1@TranStateJ
$LN3@TranStateJ:

; 113  : 		VERIFY_RETURN(IsEnterableJoinState(), ErrorJoin::EnterJoinStateFailed);

  001ad	48 8b 8c 24 b0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  001b5	e8 00 00 00 00	 call	 ?IsEnterableJoinState@PlayerJoinAction@mu2@@QEBA_NXZ ; mu2::PlayerJoinAction::IsEnterableJoinState
  001ba	0f b6 c0	 movzx	 eax, al
  001bd	85 c0		 test	 eax, eax
  001bf	0f 85 a3 00 00
	00		 jne	 $LN4@TranStateJ
  001c5	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  001ca	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  001d0	48 89 44 24 78	 mov	 QWORD PTR hConsole$3[rsp], rax
  001d5	66 ba 0d 00	 mov	 dx, 13
  001d9	48 8b 4c 24 78	 mov	 rcx, QWORD PTR hConsole$3[rsp]
  001de	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  001e4	c7 44 24 50 71
	00 00 00	 mov	 DWORD PTR [rsp+80], 113	; 00000071H
  001ec	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EP@HBIDHLFH@F?3?2Release_Branch?2Server?2Develo@
  001f3	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  001f8	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BH@LGPKNFKE@IsEnterableJoinState?$CI?$CJ@
  001ff	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  00204	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CO@LEIOINCE@mu2?3?3PlayerJoinAction?3?3TranStat@
  0020b	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  00210	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  00217	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  0021c	c7 44 24 28 71
	00 00 00	 mov	 DWORD PTR [rsp+40], 113	; 00000071H
  00224	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EP@HBIDHLFH@F?3?2Release_Branch?2Server?2Develo@
  0022b	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  00230	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CO@LEIOINCE@mu2?3?3PlayerJoinAction?3?3TranStat@
  00237	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  0023d	ba 02 00 00 00	 mov	 edx, 2
  00242	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  00249	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  0024e	66 ba 07 00	 mov	 dx, 7
  00252	48 8b 4c 24 78	 mov	 rcx, QWORD PTR hConsole$3[rsp]
  00257	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  0025d	90		 npad	 1
  0025e	b8 c7 1a 06 00	 mov	 eax, 400071		; 00061ac7H
  00263	e9 4d 01 00 00	 jmp	 $LN1@TranStateJ
$LN4@TranStateJ:

; 114  : 
; 115  : 		//MU2_TRACE_LOG(LogCategory::JOIN, "TranStateJoinExecution> player(%s), toExec(%s), context(%s)", player->ToString().c_str(), toExecId.ToString().c_str(), JoinContext::ToChar(eContext));
; 116  : 
; 117  : 		InitContext(eContext, toExecId, toIndexPosition, toPos, pDungeonInfo);

  00268	48 8b 84 24 d8
	00 00 00	 mov	 rax, QWORD PTR pDungeonInfo$[rsp]
  00270	48 89 44 24 28	 mov	 QWORD PTR [rsp+40], rax
  00275	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR toPos$[rsp]
  0027d	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  00282	44 8b 8c 24 c8
	00 00 00	 mov	 r9d, DWORD PTR toIndexPosition$[rsp]
  0028a	4c 8b 84 24 c0
	00 00 00	 mov	 r8, QWORD PTR toExecId$[rsp]
  00292	0f b6 94 24 b8
	00 00 00	 movzx	 edx, BYTE PTR eContext$[rsp]
  0029a	48 8b 8c 24 b0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  002a2	e8 00 00 00 00	 call	 ?InitContext@PlayerJoinAction@mu2@@AEAAXW4Enum@JoinContext@2@AEBVExecutionZoneId@2@HAEBVVector3@2@PEBUInstanceDungeonInfo@2@@Z ; mu2::PlayerJoinAction::InitContext
  002a7	90		 npad	 1

; 118  : 		VERIFY_RETURN(IsValid(), ErrorJoin::TranStateJoinExecutionFailed);

  002a8	48 8b 8c 24 b0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  002b0	e8 00 00 00 00	 call	 ?IsValid@PlayerJoinAction@mu2@@AEBA_NXZ ; mu2::PlayerJoinAction::IsValid
  002b5	0f b6 c0	 movzx	 eax, al
  002b8	85 c0		 test	 eax, eax
  002ba	0f 85 a9 00 00
	00		 jne	 $LN5@TranStateJ
  002c0	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  002c5	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  002cb	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR hConsole$4[rsp], rax
  002d3	66 ba 0d 00	 mov	 dx, 13
  002d7	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR hConsole$4[rsp]
  002df	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  002e5	c7 44 24 50 76
	00 00 00	 mov	 DWORD PTR [rsp+80], 118	; 00000076H
  002ed	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EP@HBIDHLFH@F?3?2Release_Branch?2Server?2Develo@
  002f4	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  002f9	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_09CEDEMLBJ@IsValid?$CI?$CJ@
  00300	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  00305	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CO@LEIOINCE@mu2?3?3PlayerJoinAction?3?3TranStat@
  0030c	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  00311	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  00318	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  0031d	c7 44 24 28 76
	00 00 00	 mov	 DWORD PTR [rsp+40], 118	; 00000076H
  00325	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EP@HBIDHLFH@F?3?2Release_Branch?2Server?2Develo@
  0032c	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  00331	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CO@LEIOINCE@mu2?3?3PlayerJoinAction?3?3TranStat@
  00338	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  0033e	ba 02 00 00 00	 mov	 edx, 2
  00343	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  0034a	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  0034f	66 ba 07 00	 mov	 dx, 7
  00353	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR hConsole$4[rsp]
  0035b	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  00361	90		 npad	 1
  00362	b8 09 1b 06 00	 mov	 eax, 400137		; 00061b09H
  00367	eb 4c		 jmp	 SHORT $LN1@TranStateJ
$LN5@TranStateJ:

; 119  : 
; 120  : 		if (player->GetCurrExecId().IsValidStong())		SetPortalType4Client(PortalType::EXIT);

  00369	48 8b 4c 24 60	 mov	 rcx, QWORD PTR player$[rsp]
  0036e	e8 00 00 00 00	 call	 ?GetCurrExecId@EntityPlayer@mu2@@QEBAAEBVExecutionZoneId@2@XZ ; mu2::EntityPlayer::GetCurrExecId
  00373	48 8b c8	 mov	 rcx, rax
  00376	e8 00 00 00 00	 call	 ?IsValidStong@ExecutionZoneId@mu2@@QEBA?B_NXZ ; mu2::ExecutionZoneId::IsValidStong
  0037b	0f b6 c0	 movzx	 eax, al
  0037e	85 c0		 test	 eax, eax
  00380	74 12		 je	 SHORT $LN6@TranStateJ
  00382	b2 09		 mov	 dl, 9
  00384	48 8b 8c 24 b0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0038c	e8 00 00 00 00	 call	 ?SetPortalType4Client@PlayerJoinAction@mu2@@AEAAXW4Enum@PortalType@2@@Z ; mu2::PlayerJoinAction::SetPortalType4Client
  00391	90		 npad	 1
  00392	eb 10		 jmp	 SHORT $LN7@TranStateJ
$LN6@TranStateJ:

; 121  : 		else											SetPortalType4Client(PortalType::NOR_ENTER);

  00394	b2 04		 mov	 dl, 4
  00396	48 8b 8c 24 b0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0039e	e8 00 00 00 00	 call	 ?SetPortalType4Client@PlayerJoinAction@mu2@@AEAAXW4Enum@PortalType@2@@Z ; mu2::PlayerJoinAction::SetPortalType4Client
  003a3	90		 npad	 1
$LN7@TranStateJ:

; 122  : 
; 123  : 		player->ChangeTran(PlayerState::PLAYER_STATE_JOIN_EXECUTION);

  003a4	ba 04 00 00 00	 mov	 edx, 4
  003a9	48 8b 4c 24 60	 mov	 rcx, QWORD PTR player$[rsp]
  003ae	e8 00 00 00 00	 call	 ?ChangeTran@EntityPlayer@mu2@@QEAAXW4PlayerState@@@Z ; mu2::EntityPlayer::ChangeTran

; 124  : 
; 125  : 		return ErrorJoin::SUCCESS;

  003b3	33 c0		 xor	 eax, eax
$LN1@TranStateJ:

; 126  : 	}

  003b5	48 81 c4 a8 00
	00 00		 add	 rsp, 168		; 000000a8H
  003bc	c3		 ret	 0
?TranStateJoinExecution@PlayerJoinAction@mu2@@QEAA?AW4Error@ErrorJoin@2@W4Enum@JoinContext@2@AEBVExecutionZoneId@2@HAEBVVector3@2@PEBUInstanceDungeonInfo@2@@Z ENDP ; mu2::PlayerJoinAction::TranStateJoinExecution
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
;	COMDAT ?ResetInReady@PlayerJoinAction@mu2@@QEAAXXZ
_TEXT	SEGMENT
$T1 = 32
$T2 = 40
$T3 = 48
tv65 = 56
this$ = 80
?ResetInReady@PlayerJoinAction@mu2@@QEAAXXZ PROC	; mu2::PlayerJoinAction::ResetInReady, COMDAT

; 43   : 	{

$LN13:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 48	 sub	 rsp, 72			; 00000048H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1309 :         return _Ptr;

  00009	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8b 40 50	 mov	 rax, QWORD PTR [rax+80]
  00012	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h

; 191  : 		return ptr.get();

  00017	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  0001c	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp

; 44   : 		if (m_ELoopbackWorldLogout.RawPtr() == NULL)

  00021	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
  00026	48 85 c0	 test	 rax, rax
  00029	75 58		 jne	 SHORT $LN2@ResetInRea
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  0002b	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@1VtheManagerJoinContext@2@A ; mu2::ISingleton<mu2::theManagerJoinContext>::inst
  00032	48 89 44 24 30	 mov	 QWORD PTR $T3[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp

; 46   : 			m_pToContext = theManagerJoinContext::GetInstance()[JoinContext::JC_NONE];

  00037	48 8b 44 24 30	 mov	 rax, QWORD PTR $T3[rsp]
  0003c	33 d2		 xor	 edx, edx
  0003e	48 8b c8	 mov	 rcx, rax
  00041	e8 00 00 00 00	 call	 ??AtheManagerJoinContext@mu2@@QEBAPEAVJoinContextBase@1@W4Enum@JoinContext@1@@Z ; mu2::theManagerJoinContext::operator[]
  00046	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  0004b	48 89 41 18	 mov	 QWORD PTR [rcx+24], rax

; 47   : 			m_toLocation.Reset();

  0004f	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00054	48 83 c0 20	 add	 rax, 32			; 00000020H
  00058	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  0005d	48 8b 49 20	 mov	 rcx, QWORD PTR [rcx+32]
  00061	48 89 4c 24 38	 mov	 QWORD PTR tv65[rsp], rcx
  00066	48 8b c8	 mov	 rcx, rax
  00069	48 8b 44 24 38	 mov	 rax, QWORD PTR tv65[rsp]
  0006e	ff 50 08	 call	 QWORD PTR [rax+8]

; 48   : 			m_eCurrProtalType4Client = PortalType::NONE;

  00071	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00076	c6 40 49 00	 mov	 BYTE PTR [rax+73], 0

; 49   : 			m_continent = 0;

  0007a	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0007f	c6 40 48 00	 mov	 BYTE PTR [rax+72], 0
$LN2@ResetInRea:

; 50   : 		}
; 51   : 	}

  00083	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00087	c3		 ret	 0
?ResetInReady@PlayerJoinAction@mu2@@QEAAXXZ ENDP	; mu2::PlayerJoinAction::ResetInReady
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
;	COMDAT ?Reset@PlayerJoinAction@mu2@@UEAAXXZ
_TEXT	SEGMENT
this$ = 32
$T1 = 40
tv84 = 48
this$ = 56
$T2 = 64
this$ = 96
?Reset@PlayerJoinAction@mu2@@UEAAXXZ PROC		; mu2::PlayerJoinAction::Reset, COMDAT

; 31   : 	{	

$LN268:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 58	 sub	 rsp, 88			; 00000058H
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  00009	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@1VtheManagerJoinContext@2@A ; mu2::ISingleton<mu2::theManagerJoinContext>::inst
  00010	48 89 44 24 28	 mov	 QWORD PTR $T1[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp

; 32   : 		m_pToContext = theManagerJoinContext::GetInstance()[JoinContext::JC_NONE];

  00015	48 8b 44 24 28	 mov	 rax, QWORD PTR $T1[rsp]
  0001a	33 d2		 xor	 edx, edx
  0001c	48 8b c8	 mov	 rcx, rax
  0001f	e8 00 00 00 00	 call	 ??AtheManagerJoinContext@mu2@@QEBAPEAVJoinContextBase@1@W4Enum@JoinContext@1@@Z ; mu2::theManagerJoinContext::operator[]
  00024	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  00029	48 89 41 18	 mov	 QWORD PTR [rcx+24], rax

; 33   : 		m_toLocation.Reset();		

  0002d	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00032	48 83 c0 20	 add	 rax, 32			; 00000020H
  00036	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  0003b	48 8b 49 20	 mov	 rcx, QWORD PTR [rcx+32]
  0003f	48 89 4c 24 30	 mov	 QWORD PTR tv84[rsp], rcx
  00044	48 8b c8	 mov	 rcx, rax
  00047	48 8b 44 24 30	 mov	 rax, QWORD PTR tv84[rsp]
  0004c	ff 50 08	 call	 QWORD PTR [rax+8]

; 34   : 		m_eCurrProtalType4Client = PortalType::NONE;

  0004f	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00054	c6 40 49 00	 mov	 BYTE PTR [rax+73], 0

; 35   : 		m_continent = 0;

  00058	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  0005d	c6 40 48 00	 mov	 BYTE PTR [rax+72], 0
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h

; 161  : 		: ptr(p)

  00061	48 8d 44 24 40	 lea	 rax, QWORD PTR $T2[rsp]
  00066	33 d2		 xor	 edx, edx
  00068	48 8b c8	 mov	 rcx, rax
  0006b	e8 00 00 00 00	 call	 ??$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z ; std::shared_ptr<mu2::Event>::shared_ptr<mu2::Event><mu2::Event,0>
  00070	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp

; 36   : 		m_ELoopbackWorldLogout = NULL;

  00071	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00076	48 83 c0 50	 add	 rax, 80			; 00000050H
  0007a	48 89 44 24 38	 mov	 QWORD PTR this$[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h

; 175  : 		this->ptr = r.ptr;

  0007f	48 8d 44 24 40	 lea	 rax, QWORD PTR $T2[rsp]
  00084	48 8b 4c 24 38	 mov	 rcx, QWORD PTR this$[rsp]
  00089	48 8b d0	 mov	 rdx, rax
  0008c	e8 00 00 00 00	 call	 ??4?$shared_ptr@UEvent@mu2@@@std@@QEAAAEAV01@AEBV01@@Z ; std::shared_ptr<mu2::Event>::operator=
  00091	90		 npad	 1

; 166  : 	}

  00092	48 8d 44 24 40	 lea	 rax, QWORD PTR $T2[rsp]
  00097	48 89 44 24 20	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1384 :         if (_Rep) {

  0009c	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  000a1	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  000a6	74 0f		 je	 SHORT $LN162@Reset

; 1385 :             _Rep->_Decref();

  000a8	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  000ad	48 8b 48 08	 mov	 rcx, QWORD PTR [rax+8]
  000b1	e8 00 00 00 00	 call	 ?_Decref@_Ref_count_base@std@@QEAAXXZ ; std::_Ref_count_base::_Decref
  000b6	90		 npad	 1
$LN162@Reset:
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp

; 37   : 		m_isDead = false;

  000b7	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  000bc	c6 40 60 00	 mov	 BYTE PTR [rax+96], 0

; 38   : 		m_talismanSkills.clear();

  000c0	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  000c5	48 83 c0 68	 add	 rax, 104		; 00000068H
  000c9	48 8b c8	 mov	 rcx, rax
  000cc	e8 00 00 00 00	 call	 ?clear@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAXXZ ; std::_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::clear

; 39   : 		m_talismanPoint = 0;

  000d1	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  000d6	c7 40 78 00 00
	00 00		 mov	 DWORD PTR [rax+120], 0

; 40   : 	}

  000dd	48 83 c4 58	 add	 rsp, 88			; 00000058H
  000e1	c3		 ret	 0
?Reset@PlayerJoinAction@mu2@@UEAAXXZ ENDP		; mu2::PlayerJoinAction::Reset
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
;	COMDAT ??1PlayerJoinAction@mu2@@UEAA@XZ
_TEXT	SEGMENT
this$ = 32
this$ = 40
this$ = 64
??1PlayerJoinAction@mu2@@UEAA@XZ PROC			; mu2::PlayerJoinAction::~PlayerJoinAction, COMDAT

; 26   : 	{

$LN196:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H
  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7PlayerJoinAction@mu2@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx

; 28   : 	}

  00018	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 83 c0 68	 add	 rax, 104		; 00000068H
  00021	48 8b c8	 mov	 rcx, rax
  00024	e8 00 00 00 00	 call	 ??1?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAA@XZ ; std::_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::~_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >
  00029	90		 npad	 1
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h

; 166  : 	}

  0002a	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0002f	48 83 c0 50	 add	 rax, 80			; 00000050H
  00033	48 89 44 24 20	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1384 :         if (_Rep) {

  00038	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0003d	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00042	74 0f		 je	 SHORT $LN149@PlayerJoin

; 1385 :             _Rep->_Decref();

  00044	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00049	48 8b 48 08	 mov	 rcx, QWORD PTR [rax+8]
  0004d	e8 00 00 00 00	 call	 ?_Decref@_Ref_count_base@std@@QEAAXXZ ; std::_Ref_count_base::_Decref
  00052	90		 npad	 1
$LN149@PlayerJoin:
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp

; 28   : 	}

  00053	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00058	48 83 c0 20	 add	 rax, 32			; 00000020H
  0005c	48 89 44 24 28	 mov	 QWORD PTR this$[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h

; 172  : 	virtual~ISerializer() {}

  00061	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00066	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  0006d	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx
  00071	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00076	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  0007d	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp

; 28   : 	}

  00080	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00085	e8 00 00 00 00	 call	 ??1ActionPlayer@mu2@@UEAA@XZ ; mu2::ActionPlayer::~ActionPlayer
  0008a	90		 npad	 1
  0008b	48 83 c4 38	 add	 rsp, 56			; 00000038H
  0008f	c3		 ret	 0
??1PlayerJoinAction@mu2@@UEAA@XZ ENDP			; mu2::PlayerJoinAction::~PlayerJoinAction
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
;	COMDAT ??0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z
_TEXT	SEGMENT
this$ = 32
$T1 = 40
this$ = 64
owner$ = 72
??0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z PROC ; mu2::PlayerJoinAction::PlayerJoinAction, COMDAT

; 20   : 	{		

$LN228:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 30	 sub	 rsp, 48			; 00000030H

; 12   : 	: ActionPlayer(owner, ID)	

  0000f	41 b8 01 00 00
	00		 mov	 r8d, 1
  00015	48 8b 54 24 48	 mov	 rdx, QWORD PTR owner$[rsp]
  0001a	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0001f	e8 00 00 00 00	 call	 ??0ActionPlayer@mu2@@QEAA@PEAVEntityPlayer@1@I@Z ; mu2::ActionPlayer::ActionPlayer
  00024	90		 npad	 1

; 20   : 	{		

  00025	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0002a	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7PlayerJoinAction@mu2@@6B@
  00031	48 89 08	 mov	 QWORD PTR [rax], rcx

; 14   : 	, m_pToContext(NULL)	

  00034	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00039	48 c7 40 18 00
	00 00 00	 mov	 QWORD PTR [rax+24], 0

; 15   : 	, m_toLocation()

  00041	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00046	48 83 c0 20	 add	 rax, 32			; 00000020H
  0004a	48 8b f8	 mov	 rdi, rax
  0004d	33 c0		 xor	 eax, eax
  0004f	b9 28 00 00 00	 mov	 ecx, 40			; 00000028H
  00054	f3 aa		 rep stosb
  00056	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0005b	48 83 c0 20	 add	 rax, 32			; 00000020H
  0005f	48 8b c8	 mov	 rcx, rax
  00062	e8 00 00 00 00	 call	 ??0ExecLocation@mu2@@QEAA@XZ
  00067	90		 npad	 1

; 16   : 	, m_continent(0)

  00068	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0006d	c6 40 48 00	 mov	 BYTE PTR [rax+72], 0

; 13   : 	, m_eCurrProtalType4Client(PortalType::NONE)

  00071	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00076	c6 40 49 00	 mov	 BYTE PTR [rax+73], 0

; 17   : 	, m_ELoopbackWorldLogout(NULL)

  0007a	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0007f	48 83 c0 50	 add	 rax, 80			; 00000050H
  00083	48 89 44 24 20	 mov	 QWORD PTR this$[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h

; 161  : 		: ptr(p)

  00088	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0008d	33 d2		 xor	 edx, edx
  0008f	48 8b c8	 mov	 rcx, rax
  00092	e8 00 00 00 00	 call	 ??$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z ; std::shared_ptr<mu2::Event>::shared_ptr<mu2::Event><mu2::Event,0>
  00097	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp

; 18   : 	, m_isDead(false)

  00098	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0009d	c6 40 60 00	 mov	 BYTE PTR [rax+96], 0

; 20   : 	{		

  000a1	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  000a6	48 83 c0 68	 add	 rax, 104		; 00000068H
  000aa	48 8b c8	 mov	 rcx, rax
  000ad	e8 00 00 00 00	 call	 ??0?$map@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ ; std::map<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >::map<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >
  000b2	90		 npad	 1

; 19   : 	, m_talismanPoint(0)

  000b3	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  000b8	c7 40 78 00 00
	00 00		 mov	 DWORD PTR [rax+120], 0
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  000bf	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@1VtheManagerJoinContext@2@A ; mu2::ISingleton<mu2::theManagerJoinContext>::inst
  000c6	48 89 44 24 28	 mov	 QWORD PTR $T1[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp

; 21   : 		m_pToContext = theManagerJoinContext::GetInstance()[JoinContext::JC_NONE];

  000cb	48 8b 44 24 28	 mov	 rax, QWORD PTR $T1[rsp]
  000d0	33 d2		 xor	 edx, edx
  000d2	48 8b c8	 mov	 rcx, rax
  000d5	e8 00 00 00 00	 call	 ??AtheManagerJoinContext@mu2@@QEBAPEAVJoinContextBase@1@W4Enum@JoinContext@1@@Z ; mu2::theManagerJoinContext::operator[]
  000da	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  000df	48 89 41 18	 mov	 QWORD PTR [rcx+24], rax

; 22   : 		m_talismanSkills.clear();

  000e3	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  000e8	48 83 c0 68	 add	 rax, 104		; 00000068H
  000ec	48 8b c8	 mov	 rcx, rax
  000ef	e8 00 00 00 00	 call	 ?clear@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAXXZ ; std::_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::clear
  000f4	90		 npad	 1

; 23   : 	}

  000f5	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  000fa	48 83 c4 30	 add	 rsp, 48			; 00000030H
  000fe	5f		 pop	 rdi
  000ff	c3		 ret	 0
??0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z ENDP ; mu2::PlayerJoinAction::PlayerJoinAction
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
this$ = 32
$T1 = 40
this$ = 64
owner$ = 72
?dtor$0@?0???0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z@4HA PROC ; `mu2::PlayerJoinAction::PlayerJoinAction'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 40	 mov	 rcx, QWORD PTR this$[rbp]
  0000d	e8 00 00 00 00	 call	 ??1ActionPlayer@mu2@@UEAA@XZ ; mu2::ActionPlayer::~ActionPlayer
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$0@?0???0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z@4HA ENDP ; `mu2::PlayerJoinAction::PlayerJoinAction'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
this$ = 32
$T1 = 40
this$ = 64
owner$ = 72
?dtor$1@?0???0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z@4HA PROC ; `mu2::PlayerJoinAction::PlayerJoinAction'::`1'::dtor$1
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 40	 mov	 rcx, QWORD PTR this$[rbp]
  0000d	48 83 c1 20	 add	 rcx, 32			; 00000020H
  00011	e8 00 00 00 00	 call	 ??1ExecLocation@mu2@@UEAA@XZ
  00016	48 83 c4 20	 add	 rsp, 32			; 00000020H
  0001a	5d		 pop	 rbp
  0001b	c3		 ret	 0
?dtor$1@?0???0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z@4HA ENDP ; `mu2::PlayerJoinAction::PlayerJoinAction'::`1'::dtor$1
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
this$ = 32
$T1 = 40
this$ = 64
owner$ = 72
?dtor$2@?0???0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z@4HA PROC ; `mu2::PlayerJoinAction::PlayerJoinAction'::`1'::dtor$2
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 40	 mov	 rcx, QWORD PTR this$[rbp]
  0000d	48 83 c1 50	 add	 rcx, 80			; 00000050H
  00011	e8 00 00 00 00	 call	 ??1?$SmartPtrEx@UEvent@mu2@@@mu2@@QEAA@XZ ; mu2::SmartPtrEx<mu2::Event>::~SmartPtrEx<mu2::Event>
  00016	48 83 c4 20	 add	 rsp, 32			; 00000020H
  0001a	5d		 pop	 rbp
  0001b	c3		 ret	 0
?dtor$2@?0???0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z@4HA ENDP ; `mu2::PlayerJoinAction::PlayerJoinAction'::`1'::dtor$2
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
this$ = 32
$T1 = 40
this$ = 64
owner$ = 72
?dtor$3@?0???0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z@4HA PROC ; `mu2::PlayerJoinAction::PlayerJoinAction'::`1'::dtor$3
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 40	 mov	 rcx, QWORD PTR this$[rbp]
  0000d	48 83 c1 68	 add	 rcx, 104		; 00000068H
  00011	e8 00 00 00 00	 call	 ??1?$map@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ
  00016	48 83 c4 20	 add	 rsp, 32			; 00000020H
  0001a	5d		 pop	 rbp
  0001b	c3		 ret	 0
?dtor$3@?0???0PlayerJoinAction@mu2@@QEAA@PEAVEntityPlayer@1@@Z@4HA ENDP ; `mu2::PlayerJoinAction::PlayerJoinAction'::`1'::dtor$3
text$x	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ManagerJoinZoneContext.h
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??_GtheManagerJoinContext@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GtheManagerJoinContext@mu2@@UEAAPEAXI@Z PROC		; mu2::theManagerJoinContext::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ManagerJoinZoneContext.h

; 15   : 		virtual~theManagerJoinContext() override {};

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7theManagerJoinContext@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 80   : 	virtual~ISingleton() {}

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@6B@
  00028	48 89 08	 mov	 QWORD PTR [rax], rcx
  0002b	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  0002f	83 e0 01	 and	 eax, 1
  00032	85 c0		 test	 eax, eax
  00034	74 10		 je	 SHORT $LN2@scalar
  00036	ba 40 00 00 00	 mov	 edx, 64			; 00000040H
  0003b	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00040	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00045	90		 npad	 1
$LN2@scalar:
  00046	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004b	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0004f	c3		 ret	 0
??_GtheManagerJoinContext@mu2@@UEAAPEAXI@Z ENDP		; mu2::theManagerJoinContext::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??_G?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_G?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@UEAAPEAXI@Z PROC ; mu2::ISingleton<mu2::theManagerJoinContext>::`scalar deleting destructor', COMDAT
$LN10:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 80   : 	virtual~ISingleton() {}

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx
  0001c	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00020	83 e0 01	 and	 eax, 1
  00023	85 c0		 test	 eax, eax
  00025	74 10		 je	 SHORT $LN2@scalar
  00027	ba 08 00 00 00	 mov	 edx, 8
  0002c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00031	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00036	90		 npad	 1
$LN2@scalar:
  00037	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003c	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00040	c3		 ret	 0
??_G?$ISingleton@VtheManagerJoinContext@mu2@@@mu2@@UEAAPEAXI@Z ENDP ; mu2::ISingleton<mu2::theManagerJoinContext>::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??$_Buyheadnode@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@@Z
_TEXT	SEGMENT
_Pnode$ = 32
_Obj$ = 40
$T1 = 48
$T2 = 56
$T3 = 64
$T4 = 72
_Obj$ = 80
$T5 = 88
$T6 = 96
$T7 = 104
$T8 = 112
_Obj$ = 120
$T9 = 128
$T10 = 136
$T11 = 144
$T12 = 152
_Al$ = 176
??$_Buyheadnode@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@@Z PROC ; std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *>::_Buyheadnode<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >, COMDAT

; 343  :     static _Nodeptr _Buyheadnode(_Alloc& _Al) {

$LN75:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec a8 00
	00 00		 sub	 rsp, 168		; 000000a8H

; 344  :         static_assert(is_same_v<typename _Alloc::value_type, _Tree_node>, "Bad _Buyheadnode call");
; 345  :         const auto _Pnode = _Al.allocate(1);

  0000c	ba 01 00 00 00	 mov	 edx, 1
  00011	48 8b 8c 24 b0
	00 00 00	 mov	 rcx, QWORD PTR _Al$[rsp]
  00019	e8 00 00 00 00	 call	 ?allocate@?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@2@_K@Z ; std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> >::allocate
  0001e	48 89 44 24 20	 mov	 QWORD PTR _Pnode$[rsp], rax

; 346  :         _Construct_in_place(_Pnode->_Left, _Pnode);

  00023	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00028	48 89 44 24 28	 mov	 QWORD PTR _Obj$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0002d	48 8b 44 24 28	 mov	 rax, QWORD PTR _Obj$[rsp]
  00032	48 89 44 24 30	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00037	48 8b 44 24 30	 mov	 rax, QWORD PTR $T1[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  0003c	48 89 44 24 38	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00041	48 8b 44 24 38	 mov	 rax, QWORD PTR $T2[rsp]
  00046	48 89 44 24 40	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  0004b	48 8d 44 24 20	 lea	 rax, QWORD PTR _Pnode$[rsp]
  00050	48 89 44 24 48	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00055	48 8b 44 24 40	 mov	 rax, QWORD PTR $T3[rsp]
  0005a	48 8b 4c 24 48	 mov	 rcx, QWORD PTR $T4[rsp]
  0005f	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00062	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 347  :         _Construct_in_place(_Pnode->_Parent, _Pnode);

  00065	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0006a	48 83 c0 08	 add	 rax, 8
  0006e	48 89 44 24 50	 mov	 QWORD PTR _Obj$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00073	48 8b 44 24 50	 mov	 rax, QWORD PTR _Obj$[rsp]
  00078	48 89 44 24 58	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0007d	48 8b 44 24 58	 mov	 rax, QWORD PTR $T5[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  00082	48 89 44 24 60	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00087	48 8b 44 24 60	 mov	 rax, QWORD PTR $T6[rsp]
  0008c	48 89 44 24 68	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00091	48 8d 44 24 20	 lea	 rax, QWORD PTR _Pnode$[rsp]
  00096	48 89 44 24 70	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0009b	48 8b 44 24 68	 mov	 rax, QWORD PTR $T7[rsp]
  000a0	48 8b 4c 24 70	 mov	 rcx, QWORD PTR $T8[rsp]
  000a5	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  000a8	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 348  :         _Construct_in_place(_Pnode->_Right, _Pnode);

  000ab	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  000b0	48 83 c0 10	 add	 rax, 16
  000b4	48 89 44 24 78	 mov	 QWORD PTR _Obj$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  000b9	48 8b 44 24 78	 mov	 rax, QWORD PTR _Obj$[rsp]
  000be	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  000c6	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR $T9[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  000ce	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  000d6	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  000de	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  000e6	48 8d 44 24 20	 lea	 rax, QWORD PTR _Pnode$[rsp]
  000eb	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  000f3	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  000fb	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR $T12[rsp]
  00103	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00106	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 349  :         _Pnode->_Color = _Black;

  00109	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0010e	c6 40 18 01	 mov	 BYTE PTR [rax+24], 1

; 350  :         _Pnode->_Isnil = true;

  00112	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00117	c6 40 19 01	 mov	 BYTE PTR [rax+25], 1

; 351  :         return _Pnode;

  0011b	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]

; 352  :     }

  00120	48 81 c4 a8 00
	00 00		 add	 rsp, 168		; 000000a8H
  00127	c3		 ret	 0
??$_Buyheadnode@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@@Z ENDP ; std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *>::_Buyheadnode<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
_TEXT	SEGMENT
_Ptr_container$ = 48
_Block_size$ = 56
_Ptr$ = 64
$T1 = 72
_Bytes$ = 96
??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z PROC ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>, COMDAT

; 182  : __declspec(allocator) void* _Allocate_manually_vector_aligned(const size_t _Bytes) {

$LN7:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 183  :     // allocate _Bytes manually aligned to at least _Big_allocation_alignment
; 184  :     const size_t _Block_size = _Non_user_size + _Bytes;

  00009	48 8b 44 24 60	 mov	 rax, QWORD PTR _Bytes$[rsp]
  0000e	48 83 c0 27	 add	 rax, 39			; 00000027H
  00012	48 89 44 24 38	 mov	 QWORD PTR _Block_size$[rsp], rax

; 185  :     if (_Block_size <= _Bytes) {

  00017	48 8b 44 24 60	 mov	 rax, QWORD PTR _Bytes$[rsp]
  0001c	48 39 44 24 38	 cmp	 QWORD PTR _Block_size$[rsp], rax
  00021	77 06		 ja	 SHORT $LN2@Allocate_m

; 186  :         _Throw_bad_array_new_length(); // add overflow

  00023	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00028	90		 npad	 1
$LN2@Allocate_m:

; 136  :         return ::operator new(_Bytes);

  00029	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Block_size$[rsp]
  0002e	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00033	48 89 44 24 48	 mov	 QWORD PTR $T1[rsp], rax

; 187  :     }
; 188  : 
; 189  :     const uintptr_t _Ptr_container = reinterpret_cast<uintptr_t>(_Traits::_Allocate(_Block_size));

  00038	48 8b 44 24 48	 mov	 rax, QWORD PTR $T1[rsp]
  0003d	48 89 44 24 30	 mov	 QWORD PTR _Ptr_container$[rsp], rax

; 190  :     _STL_VERIFY(_Ptr_container != 0, "invalid argument"); // validate even in release since we're doing p[-1]

  00042	48 83 7c 24 30
	00		 cmp	 QWORD PTR _Ptr_container$[rsp], 0
  00048	75 19		 jne	 SHORT $LN3@Allocate_m
  0004a	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR [rsp+32], 0
  00053	45 33 c9	 xor	 r9d, r9d
  00056	45 33 c0	 xor	 r8d, r8d
  00059	33 d2		 xor	 edx, edx
  0005b	33 c9		 xor	 ecx, ecx
  0005d	e8 00 00 00 00	 call	 _invoke_watson
  00062	90		 npad	 1
$LN3@Allocate_m:

; 191  :     void* const _Ptr = reinterpret_cast<void*>((_Ptr_container + _Non_user_size) & ~(_Big_allocation_alignment - 1));

  00063	48 8b 44 24 30	 mov	 rax, QWORD PTR _Ptr_container$[rsp]
  00068	48 83 c0 27	 add	 rax, 39			; 00000027H
  0006c	48 83 e0 e0	 and	 rax, -32		; ffffffffffffffe0H
  00070	48 89 44 24 40	 mov	 QWORD PTR _Ptr$[rsp], rax

; 192  :     static_cast<uintptr_t*>(_Ptr)[-1] = _Ptr_container;

  00075	b8 08 00 00 00	 mov	 eax, 8
  0007a	48 6b c0 ff	 imul	 rax, rax, -1
  0007e	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  00083	48 8b 54 24 30	 mov	 rdx, QWORD PTR _Ptr_container$[rsp]
  00088	48 89 14 01	 mov	 QWORD PTR [rcx+rax], rdx

; 193  : 
; 194  : #ifdef _DEBUG
; 195  :     static_cast<uintptr_t*>(_Ptr)[-2] = _Big_allocation_sentinel;
; 196  : #endif // defined(_DEBUG)
; 197  :     return _Ptr;

  0008c	48 8b 44 24 40	 mov	 rax, QWORD PTR _Ptr$[rsp]
$LN4@Allocate_m:

; 198  : }

  00091	48 83 c4 58	 add	 rsp, 88			; 00000058H
  00095	c3		 ret	 0
??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ENDP ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??_G?$_Ref_count@UEvent@mu2@@@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_G?$_Ref_count@UEvent@mu2@@@std@@UEAAPEAXI@Z PROC	; std::_Ref_count<mu2::Event>::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000d	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00011	83 e0 01	 and	 eax, 1
  00014	85 c0		 test	 eax, eax
  00016	74 10		 je	 SHORT $LN2@scalar
  00018	ba 18 00 00 00	 mov	 edx, 24
  0001d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00022	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00027	90		 npad	 1
$LN2@scalar:
  00028	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0002d	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00031	c3		 ret	 0
??_G?$_Ref_count@UEvent@mu2@@@std@@UEAAPEAXI@Z ENDP	; std::_Ref_count<mu2::Event>::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ?_Delete_this@?$_Ref_count@UEvent@mu2@@@std@@EEAAXXZ
_TEXT	SEGMENT
$T1 = 32
tv74 = 40
this$ = 64
?_Delete_this@?$_Ref_count@UEvent@mu2@@@std@@EEAAXXZ PROC ; std::_Ref_count<mu2::Event>::_Delete_this, COMDAT

; 1190 :     void _Delete_this() noexcept override { // destroy self

$LN6:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 1191 :         delete this;

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
  00013	48 83 7c 24 20
	00		 cmp	 QWORD PTR $T1[rsp], 0
  00019	74 1c		 je	 SHORT $LN3@Delete_thi
  0001b	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00020	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00023	ba 01 00 00 00	 mov	 edx, 1
  00028	48 8b 4c 24 20	 mov	 rcx, QWORD PTR $T1[rsp]
  0002d	ff 50 10	 call	 QWORD PTR [rax+16]
  00030	48 89 44 24 28	 mov	 QWORD PTR tv74[rsp], rax
  00035	eb 09		 jmp	 SHORT $LN4@Delete_thi
$LN3@Delete_thi:
  00037	48 c7 44 24 28
	00 00 00 00	 mov	 QWORD PTR tv74[rsp], 0
$LN4@Delete_thi:

; 1192 :     }

  00040	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00044	c3		 ret	 0
?_Delete_this@?$_Ref_count@UEvent@mu2@@@std@@EEAAXXZ ENDP ; std::_Ref_count<mu2::Event>::_Delete_this
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ?_Destroy@?$_Ref_count@UEvent@mu2@@@std@@EEAAXXZ
_TEXT	SEGMENT
$T1 = 32
tv71 = 40
this$ = 64
?_Destroy@?$_Ref_count@UEvent@mu2@@@std@@EEAAXXZ PROC	; std::_Ref_count<mu2::Event>::_Destroy, COMDAT

; 1186 :     void _Destroy() noexcept override { // destroy managed resource

$LN6:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 1187 :         delete _Ptr;

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00012	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
  00017	48 83 7c 24 20
	00		 cmp	 QWORD PTR $T1[rsp], 0
  0001d	74 1b		 je	 SHORT $LN3@Destroy
  0001f	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00024	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00027	ba 01 00 00 00	 mov	 edx, 1
  0002c	48 8b 4c 24 20	 mov	 rcx, QWORD PTR $T1[rsp]
  00031	ff 10		 call	 QWORD PTR [rax]
  00033	48 89 44 24 28	 mov	 QWORD PTR tv71[rsp], rax
  00038	eb 09		 jmp	 SHORT $LN4@Destroy
$LN3@Destroy:
  0003a	48 c7 44 24 28
	00 00 00 00	 mov	 QWORD PTR tv71[rsp], 0
$LN4@Destroy:

; 1188 :     }

  00043	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00047	c3		 ret	 0
?_Destroy@?$_Ref_count@UEvent@mu2@@@std@@EEAAXXZ ENDP	; std::_Ref_count<mu2::Event>::_Destroy
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ??1?$_Temporary_owner@UEvent@mu2@@@std@@QEAA@XZ
_TEXT	SEGMENT
$T1 = 32
tv71 = 40
this$ = 64
??1?$_Temporary_owner@UEvent@mu2@@@std@@QEAA@XZ PROC	; std::_Temporary_owner<mu2::Event>::~_Temporary_owner<mu2::Event>, COMDAT

; 1529 :     ~_Temporary_owner() {

$LN6:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 1530 :         delete _Ptr;

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00011	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
  00016	48 83 7c 24 20
	00		 cmp	 QWORD PTR $T1[rsp], 0
  0001c	74 1b		 je	 SHORT $LN3@Temporary_
  0001e	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00023	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00026	ba 01 00 00 00	 mov	 edx, 1
  0002b	48 8b 4c 24 20	 mov	 rcx, QWORD PTR $T1[rsp]
  00030	ff 10		 call	 QWORD PTR [rax]
  00032	48 89 44 24 28	 mov	 QWORD PTR tv71[rsp], rax
  00037	eb 09		 jmp	 SHORT $LN4@Temporary_
$LN3@Temporary_:
  00039	48 c7 44 24 28
	00 00 00 00	 mov	 QWORD PTR tv71[rsp], 0
$LN4@Temporary_:

; 1531 :     }

  00042	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00046	c3		 ret	 0
??1?$_Temporary_owner@UEvent@mu2@@@std@@QEAA@XZ ENDP	; std::_Temporary_owner<mu2::Event>::~_Temporary_owner<mu2::Event>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@std@@QEAA@XZ
_TEXT	SEGMENT
_Bytes$ = 32
_Ptr$ = 40
_Ptr$ = 48
this$ = 56
this$ = 80
??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@std@@QEAA@XZ PROC ; std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >, COMDAT

; 1171 :     _CONSTEXPR20 ~_Alloc_construct_ptr() { // if this instance is engaged, deallocate storage

$LN20:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 1172 :         if (_Ptr) {

  00009	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00013	74 5e		 je	 SHORT $LN2@Alloc_cons

; 1173 :             _Al.deallocate(_Ptr, 1);

  00015	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0001e	48 89 44 24 30	 mov	 QWORD PTR _Ptr$[rsp], rax
  00023	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00028	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0002b	48 89 44 24 38	 mov	 QWORD PTR this$[rsp], rax

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  00030	b8 01 00 00 00	 mov	 eax, 1
  00035	48 6b c0 28	 imul	 rax, rax, 40		; 00000028H
  00039	48 89 44 24 20	 mov	 QWORD PTR _Bytes$[rsp], rax
  0003e	48 8b 44 24 30	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00043	48 89 44 24 28	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  00048	48 81 7c 24 20
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  00051	72 10		 jb	 SHORT $LN11@Alloc_cons

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  00053	48 8d 54 24 20	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  00058	48 8d 4c 24 28	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  0005d	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  00062	90		 npad	 1
$LN11@Alloc_cons:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  00063	48 8b 54 24 20	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  00068	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  0006d	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00072	90		 npad	 1
$LN2@Alloc_cons:

; 1174 :         }
; 1175 :     }

  00073	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00077	c3		 ret	 0
??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@std@@QEAA@XZ ENDP ; std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ??$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z
_TEXT	SEGMENT
$T1 = 32
_Owner$2 = 40
$T3 = 48
tv89 = 56
_Px$ = 64
$T4 = 72
_Px$ = 80
$T5 = 88
tv166 = 96
this$ = 128
_Px$ = 136
??$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z PROC ; std::shared_ptr<mu2::Event>::shared_ptr<mu2::Event><mu2::Event,0>, COMDAT

; 1584 :     explicit shared_ptr(_Ux* _Px) { // construct shared_ptr object that owns _Px

$LN37:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 70	 sub	 rsp, 112		; 00000070H

; 1452 :     element_type* _Ptr{nullptr};

  0000f	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00017	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0

; 1453 :     _Ref_count_base* _Rep{nullptr};

  0001e	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00026	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0

; 1526 :     explicit _Temporary_owner(_Ux* const _Ptr_) noexcept : _Ptr(_Ptr_) {}

  0002e	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR _Px$[rsp]
  00036	48 89 44 24 28	 mov	 QWORD PTR _Owner$2[rsp], rax

; 1585 :         if constexpr (is_array_v<_Ty>) {
; 1586 :             _Setpd(_Px, default_delete<_Ux[]>{});
; 1587 :         } else {
; 1588 :             _Temporary_owner<_Ux> _Owner(_Px);
; 1589 :             _Set_ptr_rep_and_enable_shared(_Owner._Ptr, new _Ref_count<_Ux>(_Owner._Ptr));

  0003b	b9 18 00 00 00	 mov	 ecx, 24
  00040	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00045	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
  0004a	48 83 7c 24 20
	00		 cmp	 QWORD PTR $T1[rsp], 0
  00050	74 63		 je	 SHORT $LN3@Event
  00052	48 8b 44 24 28	 mov	 rax, QWORD PTR _Owner$2[rsp]
  00057	48 89 44 24 40	 mov	 QWORD PTR _Px$[rsp], rax

; 1183 :     explicit _Ref_count(_Ty* _Px) : _Ref_count_base(), _Ptr(_Px) {}

  0005c	48 8b 7c 24 20	 mov	 rdi, QWORD PTR $T1[rsp]
  00061	33 c0		 xor	 eax, eax
  00063	b9 10 00 00 00	 mov	 ecx, 16
  00068	f3 aa		 rep stosb

; 1119 :     _Atomic_counter_t _Uses  = 1;

  0006a	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  0006f	c7 40 08 01 00
	00 00		 mov	 DWORD PTR [rax+8], 1

; 1120 :     _Atomic_counter_t _Weaks = 1;

  00076	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  0007b	c7 40 0c 01 00
	00 00		 mov	 DWORD PTR [rax+12], 1

; 1183 :     explicit _Ref_count(_Ty* _Px) : _Ref_count_base(), _Ptr(_Px) {}

  00082	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00087	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$_Ref_count@UEvent@mu2@@@std@@6B@
  0008e	48 89 08	 mov	 QWORD PTR [rax], rcx
  00091	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00096	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Px$[rsp]
  0009b	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx
  0009f	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  000a4	48 89 44 24 48	 mov	 QWORD PTR $T4[rsp], rax

; 1585 :         if constexpr (is_array_v<_Ty>) {
; 1586 :             _Setpd(_Px, default_delete<_Ux[]>{});
; 1587 :         } else {
; 1588 :             _Temporary_owner<_Ux> _Owner(_Px);
; 1589 :             _Set_ptr_rep_and_enable_shared(_Owner._Ptr, new _Ref_count<_Ux>(_Owner._Ptr));

  000a9	48 8b 44 24 48	 mov	 rax, QWORD PTR $T4[rsp]
  000ae	48 89 44 24 38	 mov	 QWORD PTR tv89[rsp], rax
  000b3	eb 09		 jmp	 SHORT $LN4@Event
$LN3@Event:
  000b5	48 c7 44 24 38
	00 00 00 00	 mov	 QWORD PTR tv89[rsp], 0
$LN4@Event:
  000be	48 8b 44 24 38	 mov	 rax, QWORD PTR tv89[rsp]
  000c3	48 89 44 24 58	 mov	 QWORD PTR $T5[rsp], rax
  000c8	48 8b 44 24 28	 mov	 rax, QWORD PTR _Owner$2[rsp]
  000cd	48 89 44 24 50	 mov	 QWORD PTR _Px$[rsp], rax

; 1856 :         this->_Ptr = _Px;

  000d2	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000da	48 8b 4c 24 50	 mov	 rcx, QWORD PTR _Px$[rsp]
  000df	48 89 08	 mov	 QWORD PTR [rax], rcx

; 1857 :         this->_Rep = _Rx;

  000e2	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000ea	48 8b 4c 24 58	 mov	 rcx, QWORD PTR $T5[rsp]
  000ef	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 1590 :             _Owner._Ptr = nullptr;

  000f3	48 c7 44 24 28
	00 00 00 00	 mov	 QWORD PTR _Owner$2[rsp], 0

; 1530 :         delete _Ptr;

  000fc	48 8b 44 24 28	 mov	 rax, QWORD PTR _Owner$2[rsp]
  00101	48 89 44 24 30	 mov	 QWORD PTR $T3[rsp], rax
  00106	48 83 7c 24 30
	00		 cmp	 QWORD PTR $T3[rsp], 0
  0010c	74 1b		 je	 SHORT $LN32@Event
  0010e	48 8b 44 24 30	 mov	 rax, QWORD PTR $T3[rsp]
  00113	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00116	ba 01 00 00 00	 mov	 edx, 1
  0011b	48 8b 4c 24 30	 mov	 rcx, QWORD PTR $T3[rsp]
  00120	ff 10		 call	 QWORD PTR [rax]
  00122	48 89 44 24 60	 mov	 QWORD PTR tv166[rsp], rax
  00127	eb 09		 jmp	 SHORT $LN33@Event
$LN32@Event:
  00129	48 c7 44 24 60
	00 00 00 00	 mov	 QWORD PTR tv166[rsp], 0
$LN33@Event:

; 1591 :         }
; 1592 :     }

  00132	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0013a	48 83 c4 70	 add	 rsp, 112		; 00000070H
  0013e	5f		 pop	 rdi
  0013f	c3		 ret	 0
??$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z ENDP ; std::shared_ptr<mu2::Event>::shared_ptr<mu2::Event><mu2::Event,0>
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
_Owner$2 = 40
$T3 = 48
tv89 = 56
_Px$ = 64
$T4 = 72
_Px$ = 80
$T5 = 88
tv166 = 96
this$ = 128
_Px$ = 136
?dtor$0@?0???$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z@4HA PROC ; `std::shared_ptr<mu2::Event>::shared_ptr<mu2::Event><mu2::Event,0>'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8d 4d 28	 lea	 rcx, QWORD PTR _Owner$2[rbp]
  0000d	e8 00 00 00 00	 call	 ??1?$_Temporary_owner@UEvent@mu2@@@std@@QEAA@XZ ; std::_Temporary_owner<mu2::Event>::~_Temporary_owner<mu2::Event>
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$0@?0???$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z@4HA ENDP ; `std::shared_ptr<mu2::Event>::shared_ptr<mu2::Event><mu2::Event,0>'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@@Z
_TEXT	SEGMENT
_Bytes$ = 32
_Ptr$ = 40
_Ptr$ = 48
this$ = 80
_Al$ = 88
??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@@Z PROC ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,unsigned int> > >::_Erase_head<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >, COMDAT

; 775  :     void _Erase_head(_Alnode& _Al) noexcept {

$LN103:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 776  :         this->_Orphan_all();
; 777  :         _Erase_tree(_Al, _Myhead->_Parent);

  0000e	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00013	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00016	4c 8b 40 08	 mov	 r8, QWORD PTR [rax+8]
  0001a	48 8b 54 24 58	 mov	 rdx, QWORD PTR _Al$[rsp]
  0001f	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  00024	e8 00 00 00 00	 call	 ??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,unsigned int> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >

; 778  :         _Alnode::value_type::_Freenode0(_Al, _Myhead);

  00029	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0002e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00031	48 89 44 24 30	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 723  :             _STD _Deallocate<_New_alignof<value_type>>(_Ptr, sizeof(value_type) * _Count);

  00036	b8 01 00 00 00	 mov	 eax, 1
  0003b	48 6b c0 28	 imul	 rax, rax, 40		; 00000028H
  0003f	48 89 44 24 20	 mov	 QWORD PTR _Bytes$[rsp], rax
  00044	48 8b 44 24 30	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00049	48 89 44 24 28	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  0004e	48 81 7c 24 20
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  00057	72 10		 jb	 SHORT $LN94@Erase_head

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  00059	48 8d 54 24 20	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  0005e	48 8d 4c 24 28	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  00063	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  00068	90		 npad	 1
$LN94@Erase_head:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  00069	48 8b 54 24 20	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  0006e	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  00073	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00078	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 779  :     }

  00079	48 83 c4 48	 add	 rsp, 72			; 00000048H
  0007d	c3		 ret	 0
??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@@Z ENDP ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,unsigned int> > >::_Erase_head<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@@Z
_TEXT	SEGMENT
_Bytes$ = 32
_Ptr$ = 40
_Ptr$ = 48
_New_val$ = 56
_Old_val$1 = 64
$T2 = 72
$T3 = 80
this$ = 112
_Al$ = 120
_Rootnode$ = 128
??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@@Z PROC ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,unsigned int> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >, COMDAT

; 767  :     void _Erase_tree(_Alnode& _Al, _Nodeptr _Rootnode) noexcept {

$LN58:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 68	 sub	 rsp, 104		; 00000068H
$LN2@Erase_tree:

; 768  :         while (!_Rootnode->_Isnil) { // free subtrees, then node

  00013	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _Rootnode$[rsp]
  0001b	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  0001f	85 c0		 test	 eax, eax
  00021	0f 85 af 00 00
	00		 jne	 $LN3@Erase_tree

; 769  :             _Erase_tree(_Al, _Rootnode->_Right);

  00027	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _Rootnode$[rsp]
  0002f	4c 8b 40 10	 mov	 r8, QWORD PTR [rax+16]
  00033	48 8b 54 24 78	 mov	 rdx, QWORD PTR _Al$[rsp]
  00038	48 8b 4c 24 70	 mov	 rcx, QWORD PTR this$[rsp]
  0003d	e8 00 00 00 00	 call	 ??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,unsigned int> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >

; 770  :             _Alnode::value_type::_Freenode(_Al, _STD exchange(_Rootnode, _Rootnode->_Left));

  00042	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _Rootnode$[rsp]
  0004a	48 89 44 24 38	 mov	 QWORD PTR _New_val$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 773  :     _Ty _Old_val = static_cast<_Ty&&>(_Val);

  0004f	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _Rootnode$[rsp]
  00057	48 89 44 24 40	 mov	 QWORD PTR _Old_val$1[rsp], rax

; 774  :     _Val         = static_cast<_Other&&>(_New_val);

  0005c	48 8b 44 24 38	 mov	 rax, QWORD PTR _New_val$[rsp]
  00061	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00064	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _Rootnode$[rsp], rax

; 775  :     return _Old_val;

  0006c	48 8b 44 24 40	 mov	 rax, QWORD PTR _Old_val$1[rsp]
  00071	48 89 44 24 48	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 770  :             _Alnode::value_type::_Freenode(_Al, _STD exchange(_Rootnode, _Rootnode->_Left));

  00076	48 8b 44 24 48	 mov	 rax, QWORD PTR $T2[rsp]
  0007b	48 89 44 24 28	 mov	 QWORD PTR _Ptr$[rsp], rax

; 381  :         allocator_traits<_Alloc>::destroy(_Al, _STD addressof(_Ptr->_Myval));

  00080	48 8b 44 24 28	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00085	48 83 c0 1c	 add	 rax, 28
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00089	48 89 44 24 50	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 723  :             _STD _Deallocate<_New_alignof<value_type>>(_Ptr, sizeof(value_type) * _Count);

  0008e	b8 01 00 00 00	 mov	 eax, 1
  00093	48 6b c0 28	 imul	 rax, rax, 40		; 00000028H
  00097	48 89 44 24 20	 mov	 QWORD PTR _Bytes$[rsp], rax
  0009c	48 8b 44 24 28	 mov	 rax, QWORD PTR _Ptr$[rsp]
  000a1	48 89 44 24 30	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  000a6	48 81 7c 24 20
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  000af	72 10		 jb	 SHORT $LN49@Erase_tree

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  000b1	48 8d 54 24 20	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  000b6	48 8d 4c 24 30	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  000bb	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  000c0	90		 npad	 1
$LN49@Erase_tree:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  000c1	48 8b 54 24 20	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  000c6	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  000cb	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  000d0	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 771  :         }

  000d1	e9 3d ff ff ff	 jmp	 $LN2@Erase_tree
$LN3@Erase_tree:

; 772  :     }

  000d6	48 83 c4 68	 add	 rsp, 104		; 00000068H
  000da	c3		 ret	 0
??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@@Z ENDP ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,unsigned int> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ??4?$shared_ptr@UEvent@mu2@@@std@@QEAAAEAV01@AEBV01@@Z
_TEXT	SEGMENT
this$ = 32
$T1 = 40
$T2 = 48
this$ = 80
_Right$ = 88
??4?$shared_ptr@UEvent@mu2@@@std@@QEAAAEAV01@AEBV01@@Z PROC ; std::shared_ptr<mu2::Event>::operator=, COMDAT

; 1693 :     shared_ptr& operator=(const shared_ptr& _Right) noexcept {

$LN103:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 1452 :     element_type* _Ptr{nullptr};

  0000e	48 c7 44 24 30
	00 00 00 00	 mov	 QWORD PTR $T2[rsp], 0

; 1453 :     _Ref_count_base* _Rep{nullptr};

  00017	48 c7 44 24 38
	00 00 00 00	 mov	 QWORD PTR $T2[rsp+8], 0

; 1378 :         if (_Rep) {

  00020	48 8b 44 24 58	 mov	 rax, QWORD PTR _Right$[rsp]
  00025	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  0002a	74 1a		 je	 SHORT $LN20@operator

; 1379 :             _Rep->_Incref();

  0002c	48 8b 44 24 58	 mov	 rax, QWORD PTR _Right$[rsp]
  00031	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00035	48 89 44 24 20	 mov	 QWORD PTR this$[rsp], rax

; 1151 :         _MT_INCR(_Uses);

  0003a	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0003f	48 83 c0 08	 add	 rax, 8
  00043	f0 ff 00	 lock inc DWORD PTR [rax]
$LN20@operator:

; 1339 :         _Ptr = _Other._Ptr;

  00046	48 8b 44 24 58	 mov	 rax, QWORD PTR _Right$[rsp]
  0004b	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0004e	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax

; 1340 :         _Rep = _Other._Rep;

  00053	48 8b 44 24 58	 mov	 rax, QWORD PTR _Right$[rsp]
  00058	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0005c	48 89 44 24 38	 mov	 QWORD PTR $T2[rsp+8], rax

; 1636 :     }

  00061	48 8d 44 24 30	 lea	 rax, QWORD PTR $T2[rsp]
  00066	48 89 44 24 28	 mov	 QWORD PTR $T1[rsp], rax

; 1694 :         shared_ptr(_Right).swap(*this);

  0006b	48 8b 44 24 28	 mov	 rax, QWORD PTR $T1[rsp]

; 1733 :         this->_Swap(_Other);

  00070	48 8b 54 24 50	 mov	 rdx, QWORD PTR this$[rsp]
  00075	48 8b c8	 mov	 rcx, rax
  00078	e8 00 00 00 00	 call	 ?_Swap@?$_Ptr_base@UEvent@mu2@@@std@@IEAAXAEAV12@@Z ; std::_Ptr_base<mu2::Event>::_Swap
  0007d	90		 npad	 1

; 1384 :         if (_Rep) {

  0007e	48 83 7c 24 38
	00		 cmp	 QWORD PTR $T2[rsp+8], 0
  00084	74 0b		 je	 SHORT $LN86@operator

; 1385 :             _Rep->_Decref();

  00086	48 8b 4c 24 38	 mov	 rcx, QWORD PTR $T2[rsp+8]
  0008b	e8 00 00 00 00	 call	 ?_Decref@_Ref_count_base@std@@QEAAXXZ ; std::_Ref_count_base::_Decref
  00090	90		 npad	 1
$LN86@operator:

; 1695 :         return *this;

  00091	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]

; 1696 :     }

  00096	48 83 c4 48	 add	 rsp, 72			; 00000048H
  0009a	c3		 ret	 0
??4?$shared_ptr@UEvent@mu2@@@std@@QEAAAEAV01@AEBV01@@Z ENDP ; std::shared_ptr<mu2::Event>::operator=
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ??0?$shared_ptr@UEvent@mu2@@@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 0
this$ = 32
_Other$ = 40
??0?$shared_ptr@UEvent@mu2@@@std@@QEAA@AEBV01@@Z PROC	; std::shared_ptr<mu2::Event>::shared_ptr<mu2::Event>, COMDAT

; 1634 :     shared_ptr(const shared_ptr& _Other) noexcept { // construct shared_ptr object that owns same resource as _Other

$LN25:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 18	 sub	 rsp, 24

; 1452 :     element_type* _Ptr{nullptr};

  0000e	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00013	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0

; 1453 :     _Ref_count_base* _Rep{nullptr};

  0001a	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0001f	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0

; 1378 :         if (_Rep) {

  00027	48 8b 44 24 28	 mov	 rax, QWORD PTR _Other$[rsp]
  0002c	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00031	74 18		 je	 SHORT $LN15@shared_ptr

; 1379 :             _Rep->_Incref();

  00033	48 8b 44 24 28	 mov	 rax, QWORD PTR _Other$[rsp]
  00038	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0003c	48 89 04 24	 mov	 QWORD PTR this$[rsp], rax

; 1151 :         _MT_INCR(_Uses);

  00040	48 8b 04 24	 mov	 rax, QWORD PTR this$[rsp]
  00044	48 83 c0 08	 add	 rax, 8
  00048	f0 ff 00	 lock inc DWORD PTR [rax]
$LN15@shared_ptr:

; 1339 :         _Ptr = _Other._Ptr;

  0004b	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00050	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Other$[rsp]
  00055	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00058	48 89 08	 mov	 QWORD PTR [rax], rcx

; 1340 :         _Rep = _Other._Rep;

  0005b	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00060	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Other$[rsp]
  00065	48 8b 49 08	 mov	 rcx, QWORD PTR [rcx+8]
  00069	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 1635 :         this->_Copy_construct_from(_Other);
; 1636 :     }

  0006d	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00072	48 83 c4 18	 add	 rsp, 24
  00076	c3		 ret	 0
??0?$shared_ptr@UEvent@mu2@@@std@@QEAA@AEBV01@@Z ENDP	; std::shared_ptr<mu2::Event>::shared_ptr<mu2::Event>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ?_Swap@?$_Ptr_base@UEvent@mu2@@@std@@IEAAXAEAV12@@Z
_TEXT	SEGMENT
_Left$ = 0
_Right$ = 8
_Left$ = 16
_Right$ = 24
$T1 = 32
$T2 = 40
_Tmp$3 = 48
$T4 = 56
$T5 = 64
$T6 = 72
_Tmp$7 = 80
$T8 = 88
this$ = 112
_Right$ = 120
?_Swap@?$_Ptr_base@UEvent@mu2@@@std@@IEAAXAEAV12@@Z PROC ; std::_Ptr_base<mu2::Event>::_Swap, COMDAT

; 1389 :     void _Swap(_Ptr_base& _Right) noexcept { // swap pointers

$LN44:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 68	 sub	 rsp, 104		; 00000068H

; 1390 :         _STD swap(_Ptr, _Right._Ptr);

  0000e	48 8b 44 24 78	 mov	 rax, QWORD PTR _Right$[rsp]
  00013	48 89 44 24 08	 mov	 QWORD PTR _Right$[rsp], rax
  00018	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 89 04 24	 mov	 QWORD PTR _Left$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00021	48 8b 04 24	 mov	 rax, QWORD PTR _Left$[rsp]
  00025	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 139  :     _Ty _Tmp = _STD move(_Left);

  0002a	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  0002f	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00032	48 89 44 24 30	 mov	 QWORD PTR _Tmp$3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00037	48 8b 44 24 08	 mov	 rax, QWORD PTR _Right$[rsp]
  0003c	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 140  :     _Left    = _STD move(_Right);

  00041	48 8b 04 24	 mov	 rax, QWORD PTR _Left$[rsp]
  00045	48 8b 4c 24 28	 mov	 rcx, QWORD PTR $T2[rsp]
  0004a	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0004d	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00050	48 8d 44 24 30	 lea	 rax, QWORD PTR _Tmp$3[rsp]
  00055	48 89 44 24 38	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 141  :     _Right   = _STD move(_Tmp);

  0005a	48 8b 44 24 08	 mov	 rax, QWORD PTR _Right$[rsp]
  0005f	48 8b 4c 24 38	 mov	 rcx, QWORD PTR $T4[rsp]
  00064	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00067	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1391 :         _STD swap(_Rep, _Right._Rep);

  0006a	48 8b 44 24 78	 mov	 rax, QWORD PTR _Right$[rsp]
  0006f	48 83 c0 08	 add	 rax, 8
  00073	48 89 44 24 18	 mov	 QWORD PTR _Right$[rsp], rax
  00078	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0007d	48 83 c0 08	 add	 rax, 8
  00081	48 89 44 24 10	 mov	 QWORD PTR _Left$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00086	48 8b 44 24 10	 mov	 rax, QWORD PTR _Left$[rsp]
  0008b	48 89 44 24 40	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 139  :     _Ty _Tmp = _STD move(_Left);

  00090	48 8b 44 24 40	 mov	 rax, QWORD PTR $T5[rsp]
  00095	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00098	48 89 44 24 50	 mov	 QWORD PTR _Tmp$7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  0009d	48 8b 44 24 18	 mov	 rax, QWORD PTR _Right$[rsp]
  000a2	48 89 44 24 48	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 140  :     _Left    = _STD move(_Right);

  000a7	48 8b 44 24 10	 mov	 rax, QWORD PTR _Left$[rsp]
  000ac	48 8b 4c 24 48	 mov	 rcx, QWORD PTR $T6[rsp]
  000b1	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  000b4	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  000b7	48 8d 44 24 50	 lea	 rax, QWORD PTR _Tmp$7[rsp]
  000bc	48 89 44 24 58	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 141  :     _Right   = _STD move(_Tmp);

  000c1	48 8b 44 24 18	 mov	 rax, QWORD PTR _Right$[rsp]
  000c6	48 8b 4c 24 58	 mov	 rcx, QWORD PTR $T8[rsp]
  000cb	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  000ce	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1392 :     }

  000d1	48 83 c4 68	 add	 rsp, 104		; 00000068H
  000d5	c3		 ret	 0
?_Swap@?$_Ptr_base@UEvent@mu2@@@std@@IEAAXAEAV12@@Z ENDP ; std::_Ptr_base<mu2::Event>::_Swap
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
;	COMDAT ??4?$SmartPtrEx@UEvent@mu2@@@mu2@@QEAAAEAV01@AEBV01@@Z
_TEXT	SEGMENT
this$ = 32
this$ = 40
$T1 = 48
this$ = 80
r$ = 88
??4?$SmartPtrEx@UEvent@mu2@@@mu2@@QEAAAEAV01@AEBV01@@Z PROC ; mu2::SmartPtrEx<mu2::Event>::operator=, COMDAT

; 174  : 	{

$LN107:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 175  : 		this->ptr = r.ptr;

  0000e	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00013	48 89 44 24 20	 mov	 QWORD PTR this$[rsp], rax
  00018	48 8b 44 24 58	 mov	 rax, QWORD PTR r$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1694 :         shared_ptr(_Right).swap(*this);

  0001d	48 8b d0	 mov	 rdx, rax
  00020	48 8d 4c 24 30	 lea	 rcx, QWORD PTR $T1[rsp]
  00025	e8 00 00 00 00	 call	 ??0?$shared_ptr@UEvent@mu2@@@std@@QEAA@AEBV01@@Z ; std::shared_ptr<mu2::Event>::shared_ptr<mu2::Event>
  0002a	48 89 44 24 28	 mov	 QWORD PTR this$[rsp], rax

; 1733 :         this->_Swap(_Other);

  0002f	48 8b 54 24 20	 mov	 rdx, QWORD PTR this$[rsp]
  00034	48 8b 4c 24 28	 mov	 rcx, QWORD PTR this$[rsp]
  00039	e8 00 00 00 00	 call	 ?_Swap@?$_Ptr_base@UEvent@mu2@@@std@@IEAAXAEAV12@@Z ; std::_Ptr_base<mu2::Event>::_Swap
  0003e	90		 npad	 1

; 1384 :         if (_Rep) {

  0003f	48 83 7c 24 38
	00		 cmp	 QWORD PTR $T1[rsp+8], 0
  00045	74 0b		 je	 SHORT $LN90@operator

; 1385 :             _Rep->_Decref();

  00047	48 8b 4c 24 38	 mov	 rcx, QWORD PTR $T1[rsp+8]
  0004c	e8 00 00 00 00	 call	 ?_Decref@_Ref_count_base@std@@QEAAXXZ ; std::_Ref_count_base::_Decref
  00051	90		 npad	 1
$LN90@operator:
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h

; 176  : 		return *this;

  00052	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]

; 177  : 	}

  00057	48 83 c4 48	 add	 rsp, 72			; 00000048H
  0005b	c3		 ret	 0
??4?$SmartPtrEx@UEvent@mu2@@@mu2@@QEAAAEAV01@AEBV01@@Z ENDP ; mu2::SmartPtrEx<mu2::Event>::operator=
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
;	COMDAT ??1?$SmartPtrEx@UEvent@mu2@@@mu2@@QEAA@XZ
_TEXT	SEGMENT
this$ = 32
this$ = 64
??1?$SmartPtrEx@UEvent@mu2@@@mu2@@QEAA@XZ PROC		; mu2::SmartPtrEx<mu2::Event>::~SmartPtrEx<mu2::Event>, COMDAT

; 165  : 	{

$LN27:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 166  : 	}

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 89 44 24 20	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1384 :         if (_Rep) {

  00013	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00018	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  0001d	74 0f		 je	 SHORT $LN10@SmartPtrEx

; 1385 :             _Rep->_Decref();

  0001f	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00024	48 8b 48 08	 mov	 rcx, QWORD PTR [rax+8]
  00028	e8 00 00 00 00	 call	 ?_Decref@_Ref_count_base@std@@QEAAXXZ ; std::_Ref_count_base::_Decref
  0002d	90		 npad	 1
$LN10@SmartPtrEx:
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h

; 166  : 	}

  0002e	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00032	c3		 ret	 0
??1?$SmartPtrEx@UEvent@mu2@@@mu2@@QEAA@XZ ENDP		; mu2::SmartPtrEx<mu2::Event>::~SmartPtrEx<mu2::Event>
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ??_GExecLocation@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GExecLocation@mu2@@UEAAPEAXI@Z PROC			; mu2::ExecLocation::`scalar deleting destructor', COMDAT
$LN35:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 172  : 	virtual~ISerializer() {}

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  00019	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx
  0001d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00022	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  00029	48 89 08	 mov	 QWORD PTR [rax], rcx
  0002c	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00030	83 e0 01	 and	 eax, 1
  00033	85 c0		 test	 eax, eax
  00035	74 10		 je	 SHORT $LN2@scalar
  00037	ba 28 00 00 00	 mov	 edx, 40			; 00000028H
  0003c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00041	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00046	90		 npad	 1
$LN2@scalar:
  00047	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004c	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00050	c3		 ret	 0
??_GExecLocation@mu2@@UEAAPEAXI@Z ENDP			; mu2::ExecLocation::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ??1ExecLocation@mu2@@UEAA@XZ
_TEXT	SEGMENT
this$ = 8
??1ExecLocation@mu2@@UEAA@XZ PROC			; mu2::ExecLocation::~ExecLocation, COMDAT
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 172  : 	virtual~ISerializer() {}

  00005	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0000a	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  00011	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx
  00015	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  00021	48 89 08	 mov	 QWORD PTR [rax], rcx
  00024	c3		 ret	 0
??1ExecLocation@mu2@@UEAA@XZ ENDP			; mu2::ExecLocation::~ExecLocation
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Math\Vector3.h
;	COMDAT ??0ExecLocation@mu2@@QEAA@XZ
_TEXT	SEGMENT
this$ = 32
this$ = 64
??0ExecLocation@mu2@@QEAA@XZ PROC			; mu2::ExecLocation::ExecLocation, COMDAT
$LN16:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H
  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx
  00018	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ExecLocation@mu2@@6B@
  00024	48 89 08	 mov	 QWORD PTR [rax], rcx
  00027	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0002c	48 83 c0 08	 add	 rax, 8
  00030	48 8b c8	 mov	 rcx, rax
  00033	e8 00 00 00 00	 call	 ??0ExecutionZoneId@mu2@@QEAA@XZ ; mu2::ExecutionZoneId::ExecutionZoneId
  00038	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0003d	48 83 c0 1c	 add	 rax, 28
  00041	48 89 44 24 20	 mov	 QWORD PTR this$[rsp], rax

; 13   : 	inline Vector3() : x(0.f), y(0.f), z(0.f) {}

  00046	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0004b	0f 57 c0	 xorps	 xmm0, xmm0
  0004e	f3 0f 11 00	 movss	 DWORD PTR [rax], xmm0
  00052	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00057	0f 57 c0	 xorps	 xmm0, xmm0
  0005a	f3 0f 11 40 04	 movss	 DWORD PTR [rax+4], xmm0
  0005f	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00064	0f 57 c0	 xorps	 xmm0, xmm0
  00067	f3 0f 11 40 08	 movss	 DWORD PTR [rax+8], xmm0
  0006c	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00071	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00075	c3		 ret	 0
??0ExecLocation@mu2@@QEAA@XZ ENDP			; mu2::ExecLocation::ExecLocation
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
this$ = 32
this$ = 64
?dtor$0@?0???0ExecLocation@mu2@@QEAA@XZ@4HA PROC	; `mu2::ExecLocation::ExecLocation'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 40	 mov	 rcx, QWORD PTR this$[rbp]
  0000d	e8 00 00 00 00	 call	 ??1ISerializer@mu2@@UEAA@XZ ; mu2::ISerializer::~ISerializer
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$0@?0???0ExecLocation@mu2@@QEAA@XZ@4HA ENDP	; `mu2::ExecLocation::ExecLocation'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Shared\Protocol\Common.h
;	COMDAT ?PackUnpack@ExecLocation@mu2@@UEAA_NAEAVPacketStream@2@@Z
_TEXT	SEGMENT
this$ = 48
bs$ = 56
?PackUnpack@ExecLocation@mu2@@UEAA_NAEAVPacketStream@2@@Z PROC ; mu2::ExecLocation::PackUnpack, COMDAT

; 5043 : 	{

$LN114:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 5044 : 		bs.rw(execId);

  0000e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00013	48 83 c0 08	 add	 rax, 8
  00017	48 8b d0	 mov	 rdx, rax
  0001a	48 8b 4c 24 38	 mov	 rcx, QWORD PTR bs$[rsp]
  0001f	e8 00 00 00 00	 call	 ??$rw@VExecutionZoneId@mu2@@@PacketStream@mu2@@QEAA_NAEAVExecutionZoneId@1@@Z ; mu2::PacketStream::rw<mu2::ExecutionZoneId>

; 5045 : 		bs.rw(indexPosition);

  00024	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00029	48 83 c0 18	 add	 rax, 24
  0002d	48 8b d0	 mov	 rdx, rax
  00030	48 8b 4c 24 38	 mov	 rcx, QWORD PTR bs$[rsp]
  00035	e8 00 00 00 00	 call	 ??$rw@H@PacketStream@mu2@@QEAA_NAEAH@Z ; mu2::PacketStream::rw<int>

; 5046 : 		bs.rw(pos);

  0003a	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003f	48 83 c0 1c	 add	 rax, 28
  00043	48 8b d0	 mov	 rdx, rax
  00046	48 8b 4c 24 38	 mov	 rcx, QWORD PTR bs$[rsp]
  0004b	e8 00 00 00 00	 call	 ??$rw@VVector3@mu2@@@PacketStream@mu2@@QEAA_NAEAVVector3@1@@Z ; mu2::PacketStream::rw<mu2::Vector3>

; 5047 : 		return bs.isValid();

  00050	48 8b 4c 24 38	 mov	 rcx, QWORD PTR bs$[rsp]
  00055	e8 00 00 00 00	 call	 ?isValid@PacketStream@mu2@@QEBA_NXZ ; mu2::PacketStream::isValid

; 5048 : 	}

  0005a	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0005e	c3		 ret	 0
?PackUnpack@ExecLocation@mu2@@UEAA_NAEAVPacketStream@2@@Z ENDP ; mu2::ExecLocation::PackUnpack
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Shared\Protocol\Common.h
; File F:\Release_Branch\Server\Development\Framework\Math\Vector3.h
; File F:\Release_Branch\Shared\Protocol\Common.h
;	COMDAT ?Reset@ExecLocation@mu2@@UEAAXXZ
_TEXT	SEGMENT
this$ = 32
this$ = 64
?Reset@ExecLocation@mu2@@UEAAXXZ PROC			; mu2::ExecLocation::Reset, COMDAT

; 5036 : 	{

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 5037 : 		execId.Clear();

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 c0 08	 add	 rax, 8
  00012	48 8b c8	 mov	 rcx, rax
  00015	e8 00 00 00 00	 call	 ?Clear@ExecutionZoneId@mu2@@QEAAXXZ ; mu2::ExecutionZoneId::Clear

; 5038 : 		indexPosition = 0;

  0001a	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0001f	c7 40 18 00 00
	00 00		 mov	 DWORD PTR [rax+24], 0

; 5039 : 		pos.Zero();

  00026	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0002b	48 83 c0 1c	 add	 rax, 28
  0002f	48 89 44 24 20	 mov	 QWORD PTR this$[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Math\Vector3.h

; 148  :     x = y = z = 0.0f;

  00034	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00039	0f 57 c0	 xorps	 xmm0, xmm0
  0003c	f3 0f 11 40 08	 movss	 DWORD PTR [rax+8], xmm0
  00041	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00046	0f 57 c0	 xorps	 xmm0, xmm0
  00049	f3 0f 11 40 04	 movss	 DWORD PTR [rax+4], xmm0
  0004e	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00053	0f 57 c0	 xorps	 xmm0, xmm0
  00056	f3 0f 11 00	 movss	 DWORD PTR [rax], xmm0
; File F:\Release_Branch\Shared\Protocol\Common.h

; 5040 : 	}

  0005a	48 83 c4 38	 add	 rsp, 56			; 00000038H
  0005e	c3		 ret	 0
?Reset@ExecLocation@mu2@@UEAAXXZ ENDP			; mu2::ExecLocation::Reset
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ??$rw@VExecutionZoneId@mu2@@@PacketStream@mu2@@QEAA_NAEAVExecutionZoneId@1@@Z
_TEXT	SEGMENT
this$ = 48
s$ = 56
??$rw@VExecutionZoneId@mu2@@@PacketStream@mu2@@QEAA_NAEAVExecutionZoneId@1@@Z PROC ; mu2::PacketStream::rw<mu2::ExecutionZoneId>, COMDAT

; 233  : {

$LN4:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 234  : 	if (isValid() == false)

  0000e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00013	e8 00 00 00 00	 call	 ?isValid@PacketStream@mu2@@QEBA_NXZ ; mu2::PacketStream::isValid
  00018	0f b6 c0	 movzx	 eax, al
  0001b	85 c0		 test	 eax, eax
  0001d	75 04		 jne	 SHORT $LN2@rw

; 235  : 		return false;

  0001f	32 c0		 xor	 al, al
  00021	eb 14		 jmp	 SHORT $LN1@rw
$LN2@rw:

; 236  : 
; 237  : 	static_assert(std::is_base_of<ISerializer, T>::value, "T& - type must be derived ISerializer");
; 238  : 	return s.PackUnpack(*this);

  00023	48 8b 44 24 38	 mov	 rax, QWORD PTR s$[rsp]
  00028	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0002b	48 8b 54 24 30	 mov	 rdx, QWORD PTR this$[rsp]
  00030	48 8b 4c 24 38	 mov	 rcx, QWORD PTR s$[rsp]
  00035	ff 10		 call	 QWORD PTR [rax]
$LN1@rw:

; 239  : }

  00037	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0003b	c3		 ret	 0
??$rw@VExecutionZoneId@mu2@@@PacketStream@mu2@@QEAA_NAEAVExecutionZoneId@1@@Z ENDP ; mu2::PacketStream::rw<mu2::ExecutionZoneId>
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??1?$map@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1?$map@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ PROC ; std::map<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >::~map<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >, COMDAT
$LN133:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00009	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0000e	e8 00 00 00 00	 call	 ??1?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAA@XZ ; std::_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::~_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >
  00013	90		 npad	 1
  00014	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00018	c3		 ret	 0
??1?$map@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ ENDP ; std::map<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >::~map<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map
;	COMDAT ??0?$map@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ
_TEXT	SEGMENT
$T1 = 32
__formal$ = 40
this$ = 48
$T2 = 56
this$ = 64
this$ = 72
this$ = 96
??0?$map@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ PROC ; std::map<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >::map<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >, COMDAT

; 106  :     map() : _Mybase(key_compare()) {}

$LN142:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 58	 sub	 rsp, 88			; 00000058H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 904  :     _Tree(const key_compare& _Parg) : _Mypair(_One_then_variadic_args_t{}, _Parg, _Zero_then_variadic_args_t{}) {

  00009	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 89 44 24 40	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00013	48 8d 44 24 20	 lea	 rax, QWORD PTR $T1[rsp]
  00018	48 89 44 24 38	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1536 :         : _Ty1(_STD forward<_Other1>(_Val1)), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  0001d	48 8b 44 24 38	 mov	 rax, QWORD PTR $T2[rsp]
  00022	0f b6 00	 movzx	 eax, BYTE PTR [rax]
  00025	88 44 24 28	 mov	 BYTE PTR __formal$[rsp], al
  00029	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0002e	48 89 44 24 48	 mov	 QWORD PTR this$[rsp], rax

; 1531 :         : _Ty1(), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  00033	48 8b 44 24 48	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 89 44 24 30	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 450  :     _Tree_val() noexcept : _Myhead(), _Mysize(0) {}

  0003d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00042	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0

; 905  :         _Alloc_sentinel_and_proxy();

  00056	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  0005b	e8 00 00 00 00	 call	 ?_Alloc_sentinel_and_proxy@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAXXZ ; std::_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Alloc_sentinel_and_proxy
  00060	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map

; 106  :     map() : _Mybase(key_compare()) {}

  00061	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00066	48 83 c4 58	 add	 rsp, 88			; 00000058H
  0006a	c3		 ret	 0
??0?$map@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ ENDP ; std::map<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >::map<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ?_Alloc_sentinel_and_proxy@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAXXZ
_TEXT	SEGMENT
$T1 = 32
$S108$ = 33
$T2 = 40
$T3 = 48
$T4 = 56
$T5 = 64
_Scary$ = 72
_Alproxy$ = 80
this$ = 112
?_Alloc_sentinel_and_proxy@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAXXZ PROC ; std::_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Alloc_sentinel_and_proxy, COMDAT

; 1953 :     void _Alloc_sentinel_and_proxy() {

$LN107:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi
  00006	48 83 ec 60	 sub	 rsp, 96			; 00000060H

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  0000a	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0000f	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00014	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
  00019	48 89 44 24 30	 mov	 QWORD PTR $T3[rsp], rax

; 1954 :         const auto _Scary = _Get_scary();

  0001e	48 8b 44 24 30	 mov	 rax, QWORD PTR $T3[rsp]
  00023	48 89 44 24 48	 mov	 QWORD PTR _Scary$[rsp], rax

; 1955 :         auto&& _Alproxy   = _GET_PROXY_ALLOCATOR(_Alnode, _Getal());

  00028	48 8d 44 24 20	 lea	 rax, QWORD PTR $T1[rsp]
  0002d	48 8b f8	 mov	 rdi, rax
  00030	33 c0		 xor	 eax, eax
  00032	b9 01 00 00 00	 mov	 ecx, 1
  00037	f3 aa		 rep stosb
  00039	48 8d 44 24 21	 lea	 rax, QWORD PTR $S108$[rsp]
  0003e	48 89 44 24 50	 mov	 QWORD PTR _Alproxy$[rsp], rax

; 1975 :         return _Mypair._Myval2._Get_first();

  00043	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00048	48 89 44 24 38	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1975 :         return _Mypair._Myval2._Get_first();

  0004d	48 8b 44 24 38	 mov	 rax, QWORD PTR $T4[rsp]
  00052	48 89 44 24 40	 mov	 QWORD PTR $T5[rsp], rax

; 1956 :         _Container_proxy_ptr<_Alnode> _Proxy(_Alproxy, *_Scary);
; 1957 :         _Scary->_Myhead = _Node::_Buyheadnode(_Getal());

  00057	48 8b 44 24 40	 mov	 rax, QWORD PTR $T5[rsp]
  0005c	48 8b c8	 mov	 rcx, rax
  0005f	e8 00 00 00 00	 call	 ??$_Buyheadnode@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@@Z ; std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *>::_Buyheadnode<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >
  00064	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _Scary$[rsp]
  00069	48 89 01	 mov	 QWORD PTR [rcx], rax

; 1958 :         _Proxy._Release();
; 1959 :     }

  0006c	48 83 c4 60	 add	 rsp, 96			; 00000060H
  00070	5f		 pop	 rdi
  00071	c3		 ret	 0
?_Alloc_sentinel_and_proxy@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAXXZ ENDP ; std::_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Alloc_sentinel_and_proxy
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ?clear@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAXXZ
_TEXT	SEGMENT
_Head$ = 32
_Scary$ = 40
$T1 = 48
$T2 = 56
$T3 = 64
$T4 = 72
this$ = 96
?clear@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAXXZ PROC ; std::_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::clear, COMDAT

; 1370 :     void clear() noexcept {

$LN88:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00009	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0000e	48 89 44 24 30	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00013	48 8b 44 24 30	 mov	 rax, QWORD PTR $T1[rsp]
  00018	48 89 44 24 38	 mov	 QWORD PTR $T2[rsp], rax

; 1371 :         const auto _Scary = _Get_scary();

  0001d	48 8b 44 24 38	 mov	 rax, QWORD PTR $T2[rsp]
  00022	48 89 44 24 28	 mov	 QWORD PTR _Scary$[rsp], rax

; 1372 :         _Scary->_Orphan_ptr(nullptr);
; 1373 :         auto _Head = _Scary->_Myhead;

  00027	48 8b 44 24 28	 mov	 rax, QWORD PTR _Scary$[rsp]
  0002c	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0002f	48 89 44 24 20	 mov	 QWORD PTR _Head$[rsp], rax

; 1975 :         return _Mypair._Myval2._Get_first();

  00034	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00039	48 89 44 24 40	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1975 :         return _Mypair._Myval2._Get_first();

  0003e	48 8b 44 24 40	 mov	 rax, QWORD PTR $T3[rsp]
  00043	48 89 44 24 48	 mov	 QWORD PTR $T4[rsp], rax

; 1374 :         _Scary->_Erase_tree(_Getal(), _Head->_Parent);

  00048	48 8b 44 24 48	 mov	 rax, QWORD PTR $T4[rsp]
  0004d	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Head$[rsp]
  00052	4c 8b 41 08	 mov	 r8, QWORD PTR [rcx+8]
  00056	48 8b d0	 mov	 rdx, rax
  00059	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Scary$[rsp]
  0005e	e8 00 00 00 00	 call	 ??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@1@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,unsigned int> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >

; 1375 :         _Head->_Parent  = _Head;

  00063	48 8b 44 24 20	 mov	 rax, QWORD PTR _Head$[rsp]
  00068	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Head$[rsp]
  0006d	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 1376 :         _Head->_Left    = _Head;

  00071	48 8b 44 24 20	 mov	 rax, QWORD PTR _Head$[rsp]
  00076	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Head$[rsp]
  0007b	48 89 08	 mov	 QWORD PTR [rax], rcx

; 1377 :         _Head->_Right   = _Head;

  0007e	48 8b 44 24 20	 mov	 rax, QWORD PTR _Head$[rsp]
  00083	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Head$[rsp]
  00088	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 1378 :         _Scary->_Mysize = 0;

  0008c	48 8b 44 24 28	 mov	 rax, QWORD PTR _Scary$[rsp]
  00091	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0

; 1379 :     }

  00099	48 83 c4 58	 add	 rsp, 88			; 00000058H
  0009d	c3		 ret	 0
?clear@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAXXZ ENDP ; std::_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::clear
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??4?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAAEAV01@AEBV01@@Z
_TEXT	SEGMENT
$T1 = 32
$T2 = 40
$T3 = 48
$T4 = 56
$T5 = 64
_Al$ = 72
_Right_al$ = 80
$T6 = 88
$T7 = 96
this$ = 128
_Right$ = 136
??4?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAAEAV01@AEBV01@@Z PROC ; std::_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::operator=, COMDAT

; 1105 :     _Tree& operator=(const _Tree& _Right) {

$LN304:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 78	 sub	 rsp, 120		; 00000078H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0000e	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR _Right$[rsp]
  00016	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1106 :         if (this == _STD addressof(_Right)) {

  0001b	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00020	48 39 84 24 80
	00 00 00	 cmp	 QWORD PTR this$[rsp], rax
  00028	75 0d		 jne	 SHORT $LN2@operator

; 1107 :             return *this;

  0002a	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00032	e9 86 00 00 00	 jmp	 $LN1@operator
$LN2@operator:

; 1975 :         return _Mypair._Myval2._Get_first();

  00037	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  0003f	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1975 :         return _Mypair._Myval2._Get_first();

  00044	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
  00049	48 89 44 24 30	 mov	 QWORD PTR $T3[rsp], rax

; 1108 :         }
; 1109 : 
; 1110 :         auto& _Al       = _Getal();

  0004e	48 8b 44 24 30	 mov	 rax, QWORD PTR $T3[rsp]
  00053	48 89 44 24 48	 mov	 QWORD PTR _Al$[rsp], rax

; 1979 :         return _Mypair._Myval2._Get_first();

  00058	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR _Right$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1543 :         return *this;

  00060	48 89 44 24 38	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1979 :         return _Mypair._Myval2._Get_first();

  00065	48 8b 44 24 38	 mov	 rax, QWORD PTR $T4[rsp]
  0006a	48 89 44 24 40	 mov	 QWORD PTR $T5[rsp], rax

; 1111 :         auto& _Right_al = _Right._Getal();

  0006f	48 8b 44 24 40	 mov	 rax, QWORD PTR $T5[rsp]
  00074	48 89 44 24 50	 mov	 QWORD PTR _Right_al$[rsp], rax

; 1112 :         if constexpr (_Choose_pocca_v<_Alnode>) {
; 1113 :             if (_Al != _Right_al) {
; 1114 :                 clear();
; 1115 :                 const auto _Scary = _Get_scary();
; 1116 :                 _Scary->_Orphan_all();
; 1117 :                 auto&& _Alproxy       = _GET_PROXY_ALLOCATOR(_Alnode, _Al);
; 1118 :                 auto&& _Right_alproxy = _GET_PROXY_ALLOCATOR(_Alnode, _Right_al);
; 1119 :                 _Container_proxy_ptr<_Alty> _Proxy(_Right_alproxy, _Leave_proxy_unbound{});
; 1120 :                 auto _Right_al_non_const = _Right_al;
; 1121 :                 auto _Newhead            = _Node::_Buyheadnode(_Right_al_non_const);
; 1122 :                 _Node::_Freenode0(_Al, _Scary->_Myhead);
; 1123 :                 _Pocca(_Al, _Right_al);
; 1124 :                 _Scary->_Myhead = _Newhead;
; 1125 :                 _Proxy._Bind(_Alproxy, _Scary);
; 1126 :                 _Getcomp() = _Right._Getcomp();
; 1127 :                 _Copy<_Strategy::_Copy>(_Right);
; 1128 :                 return *this;
; 1129 :             }
; 1130 :         }
; 1131 : 
; 1132 :         clear();

  00079	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00081	e8 00 00 00 00	 call	 ?clear@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAXXZ ; std::_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::clear

; 1971 :         return _Mypair._Get_first();

  00086	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR _Right$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1543 :         return *this;

  0008e	48 89 44 24 58	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1967 :         return _Mypair._Get_first();

  00093	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  0009b	48 89 44 24 60	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1135 :         _Copy<_Strategy::_Copy>(_Right);

  000a0	48 8b 94 24 88
	00 00 00	 mov	 rdx, QWORD PTR _Right$[rsp]
  000a8	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  000b0	e8 00 00 00 00	 call	 ??$_Copy@$0A@@?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAAXAEBV01@@Z ; std::_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Copy<0>

; 1136 : 
; 1137 :         return *this;

  000b5	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
$LN1@operator:

; 1138 :     }

  000bd	48 83 c4 78	 add	 rsp, 120		; 00000078H
  000c1	c3		 ret	 0
??4?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAAEAV01@AEBV01@@Z ENDP ; std::_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::operator=
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??1?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAA@XZ
_TEXT	SEGMENT
$T1 = 32
$T2 = 40
$T3 = 48
$T4 = 56
_Scary$ = 64
this$ = 96
??1?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAA@XZ PROC ; std::_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::~_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >, COMDAT

; 1095 :     ~_Tree() noexcept {

$LN128:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00009	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0000e	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00013	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00018	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax

; 1096 :         const auto _Scary = _Get_scary();

  0001d	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
  00022	48 89 44 24 40	 mov	 QWORD PTR _Scary$[rsp], rax

; 1975 :         return _Mypair._Myval2._Get_first();

  00027	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  0002c	48 89 44 24 30	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1975 :         return _Mypair._Myval2._Get_first();

  00031	48 8b 44 24 30	 mov	 rax, QWORD PTR $T3[rsp]
  00036	48 89 44 24 38	 mov	 QWORD PTR $T4[rsp], rax

; 1097 :         _Scary->_Erase_head(_Getal());

  0003b	48 8b 44 24 38	 mov	 rax, QWORD PTR $T4[rsp]
  00040	48 8b d0	 mov	 rdx, rax
  00043	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Scary$[rsp]
  00048	e8 00 00 00 00	 call	 ??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBII@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@1@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,unsigned int> > >::_Erase_head<std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> > >
  0004d	90		 npad	 1

; 1098 : #if _ITERATOR_DEBUG_LEVEL != 0 // TRANSITION, ABI
; 1099 :         auto&& _Alproxy = _GET_PROXY_ALLOCATOR(_Alnode, _Getal());
; 1100 :         _Delete_plain_internal(_Alproxy, _Scary->_Myproxy);
; 1101 : #endif // _ITERATOR_DEBUG_LEVEL != 0
; 1102 :     }

  0004e	48 83 c4 58	 add	 rsp, 88			; 00000058H
  00052	c3		 ret	 0
??1?$_Tree@V?$_Tmap_traits@IIU?$less@I@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAA@XZ ENDP ; std::_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::~_Tree<std::_Tmap_traits<unsigned int,unsigned int,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?allocate@?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@2@_K@Z
_TEXT	SEGMENT
_Overflow_is_possible$1 = 32
_Bytes$ = 40
$T2 = 48
$T3 = 56
$T4 = 64
_Max_possible$5 = 72
this$ = 96
_Count$ = 104
?allocate@?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@2@_K@Z PROC ; std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> >::allocate, COMDAT

; 988  :     _NODISCARD_RAW_PTR_ALLOC _CONSTEXPR20 __declspec(allocator) _Ty* allocate(_CRT_GUARDOVERFLOW const size_t _Count) {

$LN13:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 113  :     constexpr bool _Overflow_is_possible = _Ty_size > 1;

  0000e	c6 44 24 20 01	 mov	 BYTE PTR _Overflow_is_possible$1[rsp], 1

; 114  : 
; 115  :     if constexpr (_Overflow_is_possible) {
; 116  :         constexpr size_t _Max_possible = static_cast<size_t>(-1) / _Ty_size;

  00013	48 b8 66 66 66
	66 66 66 66 06	 mov	 rax, ******************	; 0666666666666666H
  0001d	48 89 44 24 48	 mov	 QWORD PTR _Max_possible$5[rsp], rax

; 117  :         if (_Count > _Max_possible) {

  00022	48 b8 66 66 66
	66 66 66 66 06	 mov	 rax, ******************	; 0666666666666666H
  0002c	48 39 44 24 68	 cmp	 QWORD PTR _Count$[rsp], rax
  00031	76 06		 jbe	 SHORT $LN4@allocate

; 118  :             _Throw_bad_array_new_length(); // multiply overflow

  00033	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00038	90		 npad	 1
$LN4@allocate:

; 119  :         }
; 120  :     }
; 121  : 
; 122  :     return _Count * _Ty_size;

  00039	48 6b 44 24 68
	28		 imul	 rax, QWORD PTR _Count$[rsp], 40 ; 00000028H
  0003f	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00044	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
  00049	48 89 44 24 28	 mov	 QWORD PTR _Bytes$[rsp], rax

; 227  :     if (_Bytes == 0) {

  0004e	48 83 7c 24 28
	00		 cmp	 QWORD PTR _Bytes$[rsp], 0
  00054	75 0b		 jne	 SHORT $LN8@allocate

; 228  :         return nullptr;

  00056	48 c7 44 24 30
	00 00 00 00	 mov	 QWORD PTR $T2[rsp], 0
  0005f	eb 35		 jmp	 SHORT $LN7@allocate
$LN8@allocate:

; 229  :     }
; 230  : 
; 231  : #if _HAS_CXX20 // TRANSITION, GH-1532
; 232  :     if (_STD is_constant_evaluated()) {
; 233  :         return _Traits::_Allocate(_Bytes);
; 234  :     }
; 235  : #endif // _HAS_CXX20
; 236  : 
; 237  : #ifdef __cpp_aligned_new
; 238  :     if constexpr (_Align > __STDCPP_DEFAULT_NEW_ALIGNMENT__) {
; 239  :         size_t _Passed_align = _Align;
; 240  : #if defined(_M_IX86) || defined(_M_X64)
; 241  :         if (_Bytes >= _Big_allocation_threshold) {
; 242  :             // boost the alignment of big allocations to help autovectorization
; 243  :             _Passed_align = (_STD max)(_Align, _Big_allocation_alignment);
; 244  :         }
; 245  : #endif // defined(_M_IX86) || defined(_M_X64)
; 246  :         return _Traits::_Allocate_aligned(_Bytes, _Passed_align);
; 247  :     } else
; 248  : #endif // defined(__cpp_aligned_new)
; 249  :     {
; 250  : #if defined(_M_IX86) || defined(_M_X64)
; 251  :         if (_Bytes >= _Big_allocation_threshold) {

  00061	48 81 7c 24 28
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  0006a	72 11		 jb	 SHORT $LN9@allocate

; 252  :             // boost the alignment of big allocations to help autovectorization
; 253  :             return _Allocate_manually_vector_aligned<_Traits>(_Bytes);

  0006c	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00071	e8 00 00 00 00	 call	 ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
  00076	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
  0007b	eb 19		 jmp	 SHORT $LN7@allocate
$LN9@allocate:

; 136  :         return ::operator new(_Bytes);

  0007d	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00082	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00087	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax

; 256  :         return _Traits::_Allocate(_Bytes);

  0008c	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  00091	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
$LN7@allocate:

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00096	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
$LN6@allocate:

; 991  :     }

  0009b	48 83 c4 58	 add	 rsp, 88			; 00000058H
  0009f	c3		 ret	 0
?allocate@?$allocator@U?$_Tree_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBII@std@@PEAX@2@_K@Z ENDP ; std::allocator<std::_Tree_node<std::pair<unsigned int const ,unsigned int>,void *> >::allocate
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Entity\Action.h
;	COMDAT ?OnTick@Action@mu2@@UEAAXXZ
_TEXT	SEGMENT
this$ = 8
?OnTick@Action@mu2@@UEAAXXZ PROC			; mu2::Action::OnTick, COMDAT

; 18   : 	virtual void OnTick() {}

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?OnTick@Action@mu2@@UEAAXXZ ENDP			; mu2::Action::OnTick
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ??_GISerializer@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GISerializer@mu2@@UEAAPEAXI@Z PROC			; mu2::ISerializer::`scalar deleting destructor', COMDAT
$LN10:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 172  : 	virtual~ISerializer() {}

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx
  0001c	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00020	83 e0 01	 and	 eax, 1
  00023	85 c0		 test	 eax, eax
  00025	74 10		 je	 SHORT $LN2@scalar
  00027	ba 08 00 00 00	 mov	 edx, 8
  0002c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00031	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00036	90		 npad	 1
$LN2@scalar:
  00037	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003c	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00040	c3		 ret	 0
??_GISerializer@mu2@@UEAAPEAXI@Z ENDP			; mu2::ISerializer::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ??1ISerializer@mu2@@UEAA@XZ
_TEXT	SEGMENT
this$ = 8
??1ISerializer@mu2@@UEAA@XZ PROC			; mu2::ISerializer::~ISerializer, COMDAT

; 172  : 	virtual~ISerializer() {}

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0000a	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  00011	48 89 08	 mov	 QWORD PTR [rax], rcx
  00014	c3		 ret	 0
??1ISerializer@mu2@@UEAA@XZ ENDP			; mu2::ISerializer::~ISerializer
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ??$swapEndian@M@PacketStream@mu2@@AEAAMAEBM@Z
_TEXT	SEGMENT
dest$ = 0
source$ = 4
k$1 = 8
this$ = 32
u$ = 40
??$swapEndian@M@PacketStream@mu2@@AEAAMAEBM@Z PROC	; mu2::PacketStream::swapEndian<float>, COMDAT

; 1103 : {

$LN6:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 18	 sub	 rsp, 24

; 1104 : 	static_assert(std::is_fundamental<T>::value || std::is_enum<T>::value, "swapEndian> Type must be fundamental");
; 1105 : 
; 1106 : 	union
; 1107 : 	{
; 1108 : 		T u;
; 1109 : 		unsigned char u8[sizeof(T)];
; 1110 : 	} source, dest;
; 1111 : 
; 1112 : 	source.u = u;

  0000e	48 8b 44 24 28	 mov	 rax, QWORD PTR u$[rsp]
  00013	f3 0f 10 00	 movss	 xmm0, DWORD PTR [rax]
  00017	f3 0f 11 44 24
	04		 movss	 DWORD PTR source$[rsp], xmm0

; 1113 : 	dest.u = u;

  0001d	48 8b 44 24 28	 mov	 rax, QWORD PTR u$[rsp]
  00022	f3 0f 10 00	 movss	 xmm0, DWORD PTR [rax]
  00026	f3 0f 11 04 24	 movss	 DWORD PTR dest$[rsp], xmm0

; 1114 : 
; 1115 : 	for (size_t k = 0; k < sizeof(T); k++)

  0002b	48 c7 44 24 08
	00 00 00 00	 mov	 QWORD PTR k$1[rsp], 0
  00034	eb 0d		 jmp	 SHORT $LN4@swapEndian
$LN2@swapEndian:
  00036	48 8b 44 24 08	 mov	 rax, QWORD PTR k$1[rsp]
  0003b	48 ff c0	 inc	 rax
  0003e	48 89 44 24 08	 mov	 QWORD PTR k$1[rsp], rax
$LN4@swapEndian:
  00043	48 83 7c 24 08
	04		 cmp	 QWORD PTR k$1[rsp], 4
  00049	73 19		 jae	 SHORT $LN3@swapEndian

; 1116 : 		dest.u8[k] = source.u8[sizeof(T) - k - 1];

  0004b	b8 04 00 00 00	 mov	 eax, 4
  00050	48 2b 44 24 08	 sub	 rax, QWORD PTR k$1[rsp]
  00055	48 8b 4c 24 08	 mov	 rcx, QWORD PTR k$1[rsp]
  0005a	0f b6 44 04 03	 movzx	 eax, BYTE PTR source$[rsp+rax-1]
  0005f	88 04 0c	 mov	 BYTE PTR dest$[rsp+rcx], al
  00062	eb d2		 jmp	 SHORT $LN2@swapEndian
$LN3@swapEndian:

; 1117 : 
; 1118 : 	return dest.u;

  00064	f3 0f 10 04 24	 movss	 xmm0, DWORD PTR dest$[rsp]

; 1119 : }

  00069	48 83 c4 18	 add	 rsp, 24
  0006d	c3		 ret	 0
??$swapEndian@M@PacketStream@mu2@@AEAAMAEBM@Z ENDP	; mu2::PacketStream::swapEndian<float>
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ??$swapEndian@H@PacketStream@mu2@@AEAAHAEBH@Z
_TEXT	SEGMENT
dest$ = 0
source$ = 4
k$1 = 8
this$ = 32
u$ = 40
??$swapEndian@H@PacketStream@mu2@@AEAAHAEBH@Z PROC	; mu2::PacketStream::swapEndian<int>, COMDAT

; 1103 : {

$LN6:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 18	 sub	 rsp, 24

; 1104 : 	static_assert(std::is_fundamental<T>::value || std::is_enum<T>::value, "swapEndian> Type must be fundamental");
; 1105 : 
; 1106 : 	union
; 1107 : 	{
; 1108 : 		T u;
; 1109 : 		unsigned char u8[sizeof(T)];
; 1110 : 	} source, dest;
; 1111 : 
; 1112 : 	source.u = u;

  0000e	48 8b 44 24 28	 mov	 rax, QWORD PTR u$[rsp]
  00013	8b 00		 mov	 eax, DWORD PTR [rax]
  00015	89 44 24 04	 mov	 DWORD PTR source$[rsp], eax

; 1113 : 	dest.u = u;

  00019	48 8b 44 24 28	 mov	 rax, QWORD PTR u$[rsp]
  0001e	8b 00		 mov	 eax, DWORD PTR [rax]
  00020	89 04 24	 mov	 DWORD PTR dest$[rsp], eax

; 1114 : 
; 1115 : 	for (size_t k = 0; k < sizeof(T); k++)

  00023	48 c7 44 24 08
	00 00 00 00	 mov	 QWORD PTR k$1[rsp], 0
  0002c	eb 0d		 jmp	 SHORT $LN4@swapEndian
$LN2@swapEndian:
  0002e	48 8b 44 24 08	 mov	 rax, QWORD PTR k$1[rsp]
  00033	48 ff c0	 inc	 rax
  00036	48 89 44 24 08	 mov	 QWORD PTR k$1[rsp], rax
$LN4@swapEndian:
  0003b	48 83 7c 24 08
	04		 cmp	 QWORD PTR k$1[rsp], 4
  00041	73 19		 jae	 SHORT $LN3@swapEndian

; 1116 : 		dest.u8[k] = source.u8[sizeof(T) - k - 1];

  00043	b8 04 00 00 00	 mov	 eax, 4
  00048	48 2b 44 24 08	 sub	 rax, QWORD PTR k$1[rsp]
  0004d	48 8b 4c 24 08	 mov	 rcx, QWORD PTR k$1[rsp]
  00052	0f b6 44 04 03	 movzx	 eax, BYTE PTR source$[rsp+rax-1]
  00057	88 04 0c	 mov	 BYTE PTR dest$[rsp+rcx], al
  0005a	eb d2		 jmp	 SHORT $LN2@swapEndian
$LN3@swapEndian:

; 1117 : 
; 1118 : 	return dest.u;

  0005c	8b 04 24	 mov	 eax, DWORD PTR dest$[rsp]

; 1119 : }

  0005f	48 83 c4 18	 add	 rsp, 24
  00063	c3		 ret	 0
??$swapEndian@H@PacketStream@mu2@@AEAAHAEBH@Z ENDP	; mu2::PacketStream::swapEndian<int>
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ?checkingBuffer@PacketStream@mu2@@AEAA_NXZ
_TEXT	SEGMENT
this$ = 48
?checkingBuffer@PacketStream@mu2@@AEAA_NXZ PROC		; mu2::PacketStream::checkingBuffer, COMDAT

; 182  : {

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 183  : 	if (m_data == NULL)

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00013	75 32		 jne	 SHORT $LN2@checkingBu

; 184  : 	{
; 185  : 		if (false == resize(m_initSize))

  00015	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0001d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00022	8b 51 2c	 mov	 edx, DWORD PTR [rcx+44]
  00025	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0002a	ff 50 08	 call	 QWORD PTR [rax+8]
  0002d	0f b6 c0	 movzx	 eax, al
  00030	85 c0		 test	 eax, eax
  00032	75 13		 jne	 SHORT $LN3@checkingBu

; 186  : 		{
; 187  : 			SetError(ErrorNet::ResizeFailed);

  00034	ba 54 00 00 00	 mov	 edx, 84			; 00000054H
  00039	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0003e	e8 00 00 00 00	 call	 ?SetError@PacketStream@mu2@@AEAAXW4Error@ErrorNet@2@@Z ; mu2::PacketStream::SetError

; 188  : 			return false;

  00043	32 c0		 xor	 al, al
  00045	eb 02		 jmp	 SHORT $LN1@checkingBu
$LN3@checkingBu:
$LN2@checkingBu:

; 189  : 		}
; 190  : 	}
; 191  : 
; 192  : 	return true;

  00047	b0 01		 mov	 al, 1
$LN1@checkingBu:

; 193  : }

  00049	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0004d	c3		 ret	 0
?checkingBuffer@PacketStream@mu2@@AEAA_NXZ ENDP		; mu2::PacketStream::checkingBuffer
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ??$rw@M@PacketStream@mu2@@QEAA_NAEAM@Z
_TEXT	SEGMENT
swapped$1 = 32
org$2 = 36
this$ = 64
s$ = 72
??$rw@M@PacketStream@mu2@@QEAA_NAEAM@Z PROC		; mu2::PacketStream::rw<float>, COMDAT

; 143  : 	DECLARE_PACKUNPACK(float)

$LN47:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 38	 sub	 rsp, 56			; 00000038H
  0000e	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00013	e8 00 00 00 00	 call	 ?isValid@PacketStream@mu2@@QEBA_NXZ ; mu2::PacketStream::isValid
  00018	0f b6 c0	 movzx	 eax, al
  0001b	85 c0		 test	 eax, eax
  0001d	75 07		 jne	 SHORT $LN2@rw
  0001f	32 c0		 xor	 al, al
  00021	e9 c1 00 00 00	 jmp	 $LN1@rw
$LN2@rw:
  00026	0f b6 05 00 00
	00 00		 movzx	 eax, BYTE PTR ?isBigEndian@PacketStream@mu2@@2_NA ; mu2::PacketStream::isBigEndian
  0002d	85 c0		 test	 eax, eax
  0002f	75 3d		 jne	 SHORT $LN3@rw
  00031	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00036	0f b6 40 28	 movzx	 eax, BYTE PTR [rax+40]
  0003a	85 c0		 test	 eax, eax
  0003c	75 18		 jne	 SHORT $LN5@rw
  0003e	41 b8 04 00 00
	00		 mov	 r8d, 4
  00044	48 8b 54 24 48	 mov	 rdx, QWORD PTR s$[rsp]
  00049	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0004e	e8 00 00 00 00	 call	 ?Write@PacketStream@mu2@@QEAAIPEBEI@Z ; mu2::PacketStream::Write
  00053	90		 npad	 1
  00054	eb 16		 jmp	 SHORT $LN6@rw
$LN5@rw:
  00056	41 b8 04 00 00
	00		 mov	 r8d, 4
  0005c	48 8b 54 24 48	 mov	 rdx, QWORD PTR s$[rsp]
  00061	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00066	e8 00 00 00 00	 call	 ?Read@PacketStream@mu2@@QEAAIPEAEI@Z ; mu2::PacketStream::Read
  0006b	90		 npad	 1
$LN6@rw:
  0006c	eb 6f		 jmp	 SHORT $LN4@rw
$LN3@rw:
  0006e	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00073	0f b6 40 28	 movzx	 eax, BYTE PTR [rax+40]
  00077	85 c0		 test	 eax, eax
  00079	75 2d		 jne	 SHORT $LN7@rw
  0007b	48 8b 54 24 48	 mov	 rdx, QWORD PTR s$[rsp]
  00080	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00085	e8 00 00 00 00	 call	 ??$swapEndian@M@PacketStream@mu2@@AEAAMAEBM@Z ; mu2::PacketStream::swapEndian<float>
  0008a	f3 0f 11 44 24
	20		 movss	 DWORD PTR swapped$1[rsp], xmm0
  00090	41 b8 04 00 00
	00		 mov	 r8d, 4
  00096	48 8d 54 24 20	 lea	 rdx, QWORD PTR swapped$1[rsp]
  0009b	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  000a0	e8 00 00 00 00	 call	 ?Write@PacketStream@mu2@@QEAAIPEBEI@Z ; mu2::PacketStream::Write
  000a5	90		 npad	 1
  000a6	eb 35		 jmp	 SHORT $LN8@rw
$LN7@rw:
  000a8	41 b8 04 00 00
	00		 mov	 r8d, 4
  000ae	48 8d 54 24 24	 lea	 rdx, QWORD PTR org$2[rsp]
  000b3	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  000b8	e8 00 00 00 00	 call	 ?Read@PacketStream@mu2@@QEAAIPEAEI@Z ; mu2::PacketStream::Read
  000bd	85 c0		 test	 eax, eax
  000bf	75 04		 jne	 SHORT $LN9@rw
  000c1	32 c0		 xor	 al, al
  000c3	eb 22		 jmp	 SHORT $LN1@rw
$LN9@rw:
  000c5	48 8d 54 24 24	 lea	 rdx, QWORD PTR org$2[rsp]
  000ca	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  000cf	e8 00 00 00 00	 call	 ??$swapEndian@M@PacketStream@mu2@@AEAAMAEBM@Z ; mu2::PacketStream::swapEndian<float>
  000d4	48 8b 44 24 48	 mov	 rax, QWORD PTR s$[rsp]
  000d9	f3 0f 11 00	 movss	 DWORD PTR [rax], xmm0
$LN8@rw:
$LN4@rw:
  000dd	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  000e2	e8 00 00 00 00	 call	 ?isValid@PacketStream@mu2@@QEBA_NXZ ; mu2::PacketStream::isValid
$LN1@rw:
  000e7	48 83 c4 38	 add	 rsp, 56			; 00000038H
  000eb	c3		 ret	 0
??$rw@M@PacketStream@mu2@@QEAA_NAEAM@Z ENDP		; mu2::PacketStream::rw<float>
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ??$rw@H@PacketStream@mu2@@QEAA_NAEAH@Z
_TEXT	SEGMENT
swapped$1 = 32
org$2 = 36
this$ = 64
s$ = 72
??$rw@H@PacketStream@mu2@@QEAA_NAEAH@Z PROC		; mu2::PacketStream::rw<int>, COMDAT

; 139  : 	DECLARE_PACKUNPACK(Int32)

$LN47:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 38	 sub	 rsp, 56			; 00000038H
  0000e	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00013	e8 00 00 00 00	 call	 ?isValid@PacketStream@mu2@@QEBA_NXZ ; mu2::PacketStream::isValid
  00018	0f b6 c0	 movzx	 eax, al
  0001b	85 c0		 test	 eax, eax
  0001d	75 07		 jne	 SHORT $LN2@rw
  0001f	32 c0		 xor	 al, al
  00021	e9 bd 00 00 00	 jmp	 $LN1@rw
$LN2@rw:
  00026	0f b6 05 00 00
	00 00		 movzx	 eax, BYTE PTR ?isBigEndian@PacketStream@mu2@@2_NA ; mu2::PacketStream::isBigEndian
  0002d	85 c0		 test	 eax, eax
  0002f	75 3d		 jne	 SHORT $LN3@rw
  00031	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00036	0f b6 40 28	 movzx	 eax, BYTE PTR [rax+40]
  0003a	85 c0		 test	 eax, eax
  0003c	75 18		 jne	 SHORT $LN5@rw
  0003e	41 b8 04 00 00
	00		 mov	 r8d, 4
  00044	48 8b 54 24 48	 mov	 rdx, QWORD PTR s$[rsp]
  00049	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0004e	e8 00 00 00 00	 call	 ?Write@PacketStream@mu2@@QEAAIPEBEI@Z ; mu2::PacketStream::Write
  00053	90		 npad	 1
  00054	eb 16		 jmp	 SHORT $LN6@rw
$LN5@rw:
  00056	41 b8 04 00 00
	00		 mov	 r8d, 4
  0005c	48 8b 54 24 48	 mov	 rdx, QWORD PTR s$[rsp]
  00061	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00066	e8 00 00 00 00	 call	 ?Read@PacketStream@mu2@@QEAAIPEAEI@Z ; mu2::PacketStream::Read
  0006b	90		 npad	 1
$LN6@rw:
  0006c	eb 6b		 jmp	 SHORT $LN4@rw
$LN3@rw:
  0006e	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00073	0f b6 40 28	 movzx	 eax, BYTE PTR [rax+40]
  00077	85 c0		 test	 eax, eax
  00079	75 2b		 jne	 SHORT $LN7@rw
  0007b	48 8b 54 24 48	 mov	 rdx, QWORD PTR s$[rsp]
  00080	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00085	e8 00 00 00 00	 call	 ??$swapEndian@H@PacketStream@mu2@@AEAAHAEBH@Z ; mu2::PacketStream::swapEndian<int>
  0008a	89 44 24 20	 mov	 DWORD PTR swapped$1[rsp], eax
  0008e	41 b8 04 00 00
	00		 mov	 r8d, 4
  00094	48 8d 54 24 20	 lea	 rdx, QWORD PTR swapped$1[rsp]
  00099	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0009e	e8 00 00 00 00	 call	 ?Write@PacketStream@mu2@@QEAAIPEBEI@Z ; mu2::PacketStream::Write
  000a3	90		 npad	 1
  000a4	eb 33		 jmp	 SHORT $LN8@rw
$LN7@rw:
  000a6	41 b8 04 00 00
	00		 mov	 r8d, 4
  000ac	48 8d 54 24 24	 lea	 rdx, QWORD PTR org$2[rsp]
  000b1	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  000b6	e8 00 00 00 00	 call	 ?Read@PacketStream@mu2@@QEAAIPEAEI@Z ; mu2::PacketStream::Read
  000bb	85 c0		 test	 eax, eax
  000bd	75 04		 jne	 SHORT $LN9@rw
  000bf	32 c0		 xor	 al, al
  000c1	eb 20		 jmp	 SHORT $LN1@rw
$LN9@rw:
  000c3	48 8d 54 24 24	 lea	 rdx, QWORD PTR org$2[rsp]
  000c8	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  000cd	e8 00 00 00 00	 call	 ??$swapEndian@H@PacketStream@mu2@@AEAAHAEBH@Z ; mu2::PacketStream::swapEndian<int>
  000d2	48 8b 4c 24 48	 mov	 rcx, QWORD PTR s$[rsp]
  000d7	89 01		 mov	 DWORD PTR [rcx], eax
$LN8@rw:
$LN4@rw:
  000d9	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  000de	e8 00 00 00 00	 call	 ?isValid@PacketStream@mu2@@QEBA_NXZ ; mu2::PacketStream::isValid
$LN1@rw:
  000e3	48 83 c4 38	 add	 rsp, 56			; 00000038H
  000e7	c3		 ret	 0
??$rw@H@PacketStream@mu2@@QEAA_NAEAH@Z ENDP		; mu2::PacketStream::rw<int>
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ??$rw@VVector3@mu2@@@PacketStream@mu2@@QEAA_NAEAVVector3@1@@Z
_TEXT	SEGMENT
ret$ = 32
tv92 = 36
this$ = 64
v$ = 72
??$rw@VVector3@mu2@@@PacketStream@mu2@@QEAA_NAEAVVector3@1@@Z PROC ; mu2::PacketStream::rw<mu2::Vector3>, COMDAT

; 996  : {	

$LN93:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 997  : 	if (isValid() == false)

  0000e	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00013	e8 00 00 00 00	 call	 ?isValid@PacketStream@mu2@@QEBA_NXZ ; mu2::PacketStream::isValid
  00018	0f b6 c0	 movzx	 eax, al
  0001b	85 c0		 test	 eax, eax
  0001d	75 07		 jne	 SHORT $LN2@rw

; 998  : 		return false;

  0001f	32 c0		 xor	 al, al
  00021	e9 a7 00 00 00	 jmp	 $LN1@rw
$LN2@rw:

; 999  : 
; 1000 : 	Bool ret = true;

  00026	c6 44 24 20 01	 mov	 BYTE PTR ret$[rsp], 1

; 1001 : 
; 1002 : 	ret = rw(v.x);		if (ret == false) return false;

  0002b	48 8b 44 24 48	 mov	 rax, QWORD PTR v$[rsp]
  00030	48 8b d0	 mov	 rdx, rax
  00033	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00038	e8 00 00 00 00	 call	 ??$rw@M@PacketStream@mu2@@QEAA_NAEAM@Z ; mu2::PacketStream::rw<float>
  0003d	88 44 24 20	 mov	 BYTE PTR ret$[rsp], al
  00041	0f b6 44 24 20	 movzx	 eax, BYTE PTR ret$[rsp]
  00046	85 c0		 test	 eax, eax
  00048	75 04		 jne	 SHORT $LN3@rw
  0004a	32 c0		 xor	 al, al
  0004c	eb 7f		 jmp	 SHORT $LN1@rw
$LN3@rw:

; 1003 : 	ret = rw(v.y);		if (ret == false) return false;

  0004e	48 8b 44 24 48	 mov	 rax, QWORD PTR v$[rsp]
  00053	48 83 c0 04	 add	 rax, 4
  00057	48 8b d0	 mov	 rdx, rax
  0005a	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0005f	e8 00 00 00 00	 call	 ??$rw@M@PacketStream@mu2@@QEAA_NAEAM@Z ; mu2::PacketStream::rw<float>
  00064	88 44 24 20	 mov	 BYTE PTR ret$[rsp], al
  00068	0f b6 44 24 20	 movzx	 eax, BYTE PTR ret$[rsp]
  0006d	85 c0		 test	 eax, eax
  0006f	75 04		 jne	 SHORT $LN4@rw
  00071	32 c0		 xor	 al, al
  00073	eb 58		 jmp	 SHORT $LN1@rw
$LN4@rw:

; 1004 : 	ret = rw(v.z);		if (ret == false) return false;

  00075	48 8b 44 24 48	 mov	 rax, QWORD PTR v$[rsp]
  0007a	48 83 c0 08	 add	 rax, 8
  0007e	48 8b d0	 mov	 rdx, rax
  00081	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00086	e8 00 00 00 00	 call	 ??$rw@M@PacketStream@mu2@@QEAA_NAEAM@Z ; mu2::PacketStream::rw<float>
  0008b	88 44 24 20	 mov	 BYTE PTR ret$[rsp], al
  0008f	0f b6 44 24 20	 movzx	 eax, BYTE PTR ret$[rsp]
  00094	85 c0		 test	 eax, eax
  00096	75 04		 jne	 SHORT $LN5@rw
  00098	32 c0		 xor	 al, al
  0009a	eb 31		 jmp	 SHORT $LN1@rw
$LN5@rw:

; 1005 : 
; 1006 : 	return ret && isValid();

  0009c	0f b6 44 24 20	 movzx	 eax, BYTE PTR ret$[rsp]
  000a1	85 c0		 test	 eax, eax
  000a3	74 1b		 je	 SHORT $LN7@rw
  000a5	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  000aa	e8 00 00 00 00	 call	 ?isValid@PacketStream@mu2@@QEBA_NXZ ; mu2::PacketStream::isValid
  000af	0f b6 c0	 movzx	 eax, al
  000b2	85 c0		 test	 eax, eax
  000b4	74 0a		 je	 SHORT $LN7@rw
  000b6	c7 44 24 24 01
	00 00 00	 mov	 DWORD PTR tv92[rsp], 1
  000be	eb 08		 jmp	 SHORT $LN8@rw
$LN7@rw:
  000c0	c7 44 24 24 00
	00 00 00	 mov	 DWORD PTR tv92[rsp], 0
$LN8@rw:
  000c8	0f b6 44 24 24	 movzx	 eax, BYTE PTR tv92[rsp]
$LN1@rw:

; 1007 : }

  000cd	48 83 c4 38	 add	 rsp, 56			; 00000038H
  000d1	c3		 ret	 0
??$rw@VVector3@mu2@@@PacketStream@mu2@@QEAA_NAEAVVector3@1@@Z ENDP ; mu2::PacketStream::rw<mu2::Vector3>
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ?Read@PacketStream@mu2@@QEAAIPEAEI@Z
_TEXT	SEGMENT
cnt$ = 96
hConsole$1 = 104
hConsole$2 = 112
readCnt$3 = 120
this$ = 144
buf$ = 152
count$ = 160
?Read@PacketStream@mu2@@QEAAIPEAEI@Z PROC		; mu2::PacketStream::Read, COMDAT

; 1066 : {

$LN10:
  00000	44 89 44 24 18	 mov	 DWORD PTR [rsp+24], r8d
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H

; 1067 : 	VERIFY_RETURN(count, 0);

  00016	83 bc 24 a0 00
	00 00 00	 cmp	 DWORD PTR count$[rsp], 0
  0001e	0f 85 a0 00 00
	00		 jne	 $LN2@Read
  00024	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  00029	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  0002f	48 89 44 24 68	 mov	 QWORD PTR hConsole$1[rsp], rax
  00034	66 ba 0d 00	 mov	 dx, 13
  00038	48 8b 4c 24 68	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  0003d	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  00043	c7 44 24 50 2b
	04 00 00	 mov	 DWORD PTR [rsp+80], 1067 ; 0000042bH
  0004b	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EJ@IFEHPKPB@F?3?2Release_Branch?2Server?2Develo@
  00052	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  00057	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_05IOMEMJEC@count@
  0005e	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  00063	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@KGAODAAL@mu2?3?3PacketStream?3?3Read@
  0006a	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  0006f	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  00076	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  0007b	c7 44 24 28 2b
	04 00 00	 mov	 DWORD PTR [rsp+40], 1067 ; 0000042bH
  00083	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EJ@IFEHPKPB@F?3?2Release_Branch?2Server?2Develo@
  0008a	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  0008f	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0BI@KGAODAAL@mu2?3?3PacketStream?3?3Read@
  00096	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  0009c	ba 02 00 00 00	 mov	 edx, 2
  000a1	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  000a8	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  000ad	66 ba 07 00	 mov	 dx, 7
  000b1	48 8b 4c 24 68	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  000b6	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000bc	90		 npad	 1
  000bd	33 c0		 xor	 eax, eax
  000bf	e9 8e 01 00 00	 jmp	 $LN1@Read
$LN2@Read:

; 1068 : 	VERIFY_RETURN(buf, 0);

  000c4	48 83 bc 24 98
	00 00 00 00	 cmp	 QWORD PTR buf$[rsp], 0
  000cd	0f 85 a0 00 00
	00		 jne	 $LN3@Read
  000d3	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  000d8	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  000de	48 89 44 24 70	 mov	 QWORD PTR hConsole$2[rsp], rax
  000e3	66 ba 0d 00	 mov	 dx, 13
  000e7	48 8b 4c 24 70	 mov	 rcx, QWORD PTR hConsole$2[rsp]
  000ec	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000f2	c7 44 24 50 2c
	04 00 00	 mov	 DWORD PTR [rsp+80], 1068 ; 0000042cH
  000fa	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EJ@IFEHPKPB@F?3?2Release_Branch?2Server?2Develo@
  00101	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  00106	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_03COAJHJPB@buf@
  0010d	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  00112	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@KGAODAAL@mu2?3?3PacketStream?3?3Read@
  00119	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  0011e	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  00125	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  0012a	c7 44 24 28 2c
	04 00 00	 mov	 DWORD PTR [rsp+40], 1068 ; 0000042cH
  00132	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EJ@IFEHPKPB@F?3?2Release_Branch?2Server?2Develo@
  00139	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  0013e	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0BI@KGAODAAL@mu2?3?3PacketStream?3?3Read@
  00145	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  0014b	ba 02 00 00 00	 mov	 edx, 2
  00150	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  00157	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  0015c	66 ba 07 00	 mov	 dx, 7
  00160	48 8b 4c 24 70	 mov	 rcx, QWORD PTR hConsole$2[rsp]
  00165	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  0016b	90		 npad	 1
  0016c	33 c0		 xor	 eax, eax
  0016e	e9 df 00 00 00	 jmp	 $LN1@Read
$LN3@Read:

; 1069 : 
; 1070 : 	if (m_pos == NULL)

  00173	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0017b	48 83 78 10 00	 cmp	 QWORD PTR [rax+16], 0
  00180	75 07		 jne	 SHORT $LN4@Read

; 1071 : 		return 0;

  00182	33 c0		 xor	 eax, eax
  00184	e9 c9 00 00 00	 jmp	 $LN1@Read
$LN4@Read:

; 1072 : 
; 1073 : 	if( isValid() == false ) 

  00189	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00191	e8 00 00 00 00	 call	 ?isValid@PacketStream@mu2@@QEBA_NXZ ; mu2::PacketStream::isValid
  00196	0f b6 c0	 movzx	 eax, al
  00199	85 c0		 test	 eax, eax
  0019b	75 07		 jne	 SHORT $LN5@Read

; 1074 : 		return 0;

  0019d	33 c0		 xor	 eax, eax
  0019f	e9 ae 00 00 00	 jmp	 $LN1@Read
$LN5@Read:

; 1075 : 
; 1076 : 	UInt32 cnt = count;

  001a4	8b 84 24 a0 00
	00 00		 mov	 eax, DWORD PTR count$[rsp]
  001ab	89 44 24 60	 mov	 DWORD PTR cnt$[rsp], eax

; 1077 : 
; 1078 : 	if (m_pos + cnt > m_end)

  001af	8b 44 24 60	 mov	 eax, DWORD PTR cnt$[rsp]
  001b3	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  001bb	48 03 41 10	 add	 rax, QWORD PTR [rcx+16]
  001bf	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  001c7	48 3b 41 18	 cmp	 rax, QWORD PTR [rcx+24]
  001cb	76 3a		 jbe	 SHORT $LN6@Read

; 1079 : 	{
; 1080 : 		SetError(ErrorNet::NotEnoughReadableSize);

  001cd	ba 61 00 00 00	 mov	 edx, 97			; 00000061H
  001d2	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  001da	e8 00 00 00 00	 call	 ?SetError@PacketStream@mu2@@AEAAXW4Error@ErrorNet@2@@Z ; mu2::PacketStream::SetError

; 1081 : 
; 1082 : 		Int64 readCnt = remains();

  001df	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  001e7	e8 00 00 00 00	 call	 ?remains@PacketStream@mu2@@AEAAIXZ ; mu2::PacketStream::remains
  001ec	8b c0		 mov	 eax, eax
  001ee	48 89 44 24 78	 mov	 QWORD PTR readCnt$3[rsp], rax

; 1083 : 		if ( readCnt <= 0 )

  001f3	48 83 7c 24 78
	00		 cmp	 QWORD PTR readCnt$3[rsp], 0
  001f9	7f 04		 jg	 SHORT $LN7@Read

; 1084 : 			return 0;

  001fb	33 c0		 xor	 eax, eax
  001fd	eb 53		 jmp	 SHORT $LN1@Read
$LN7@Read:

; 1085 : 
; 1086 : 		cnt = static_cast<UInt32>(readCnt);

  001ff	8b 44 24 78	 mov	 eax, DWORD PTR readCnt$3[rsp]
  00203	89 44 24 60	 mov	 DWORD PTR cnt$[rsp], eax
$LN6@Read:

; 1087 : 	}
; 1088 : 
; 1089 : 	if (cnt == 0 )

  00207	83 7c 24 60 00	 cmp	 DWORD PTR cnt$[rsp], 0
  0020c	75 04		 jne	 SHORT $LN8@Read

; 1090 : 		return 0;

  0020e	33 c0		 xor	 eax, eax
  00210	eb 40		 jmp	 SHORT $LN1@Read
$LN8@Read:

; 1091 : 
; 1092 : 	assert (cnt<=count);
; 1093 : 
; 1094 : 	memcpy(buf, m_pos, cnt);

  00212	8b 44 24 60	 mov	 eax, DWORD PTR cnt$[rsp]
  00216	44 8b c0	 mov	 r8d, eax
  00219	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00221	48 8b 50 10	 mov	 rdx, QWORD PTR [rax+16]
  00225	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR buf$[rsp]
  0022d	e8 00 00 00 00	 call	 memcpy

; 1095 : 	
; 1096 : 	m_pos += cnt;

  00232	8b 44 24 60	 mov	 eax, DWORD PTR cnt$[rsp]
  00236	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0023e	48 03 41 10	 add	 rax, QWORD PTR [rcx+16]
  00242	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0024a	48 89 41 10	 mov	 QWORD PTR [rcx+16], rax

; 1097 : 
; 1098 : 	return cnt;

  0024e	8b 44 24 60	 mov	 eax, DWORD PTR cnt$[rsp]
$LN1@Read:

; 1099 : }

  00252	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  00259	c3		 ret	 0
?Read@PacketStream@mu2@@QEAAIPEAEI@Z ENDP		; mu2::PacketStream::Read
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ?Write@PacketStream@mu2@@QEAAIPEBEI@Z
_TEXT	SEGMENT
written$ = 96
hConsole$1 = 104
tv157 = 112
this$ = 144
buf$ = 152
count$ = 160
?Write@PacketStream@mu2@@QEAAIPEBEI@Z PROC		; mu2::PacketStream::Write, COMDAT

; 1045 : {	

$LN10:
  00000	44 89 44 24 18	 mov	 DWORD PTR [rsp+24], r8d
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H

; 1046 : 	VERIFY_RETURN(checkingBuffer(), 0);

  00016	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0001e	e8 00 00 00 00	 call	 ?checkingBuffer@PacketStream@mu2@@AEAA_NXZ ; mu2::PacketStream::checkingBuffer
  00023	0f b6 c0	 movzx	 eax, al
  00026	85 c0		 test	 eax, eax
  00028	0f 85 a0 00 00
	00		 jne	 $LN2@Write
  0002e	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  00033	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  00039	48 89 44 24 68	 mov	 QWORD PTR hConsole$1[rsp], rax
  0003e	66 ba 0d 00	 mov	 dx, 13
  00042	48 8b 4c 24 68	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  00047	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  0004d	c7 44 24 50 16
	04 00 00	 mov	 DWORD PTR [rsp+80], 1046 ; 00000416H
  00055	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EJ@IFEHPKPB@F?3?2Release_Branch?2Server?2Develo@
  0005c	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  00061	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BB@HAIDOJLN@checkingBuffer?$CI?$CJ@
  00068	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  0006d	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BJ@NEGBBJOE@mu2?3?3PacketStream?3?3Write@
  00074	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  00079	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  00080	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  00085	c7 44 24 28 16
	04 00 00	 mov	 DWORD PTR [rsp+40], 1046 ; 00000416H
  0008d	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EJ@IFEHPKPB@F?3?2Release_Branch?2Server?2Develo@
  00094	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  00099	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0BJ@NEGBBJOE@mu2?3?3PacketStream?3?3Write@
  000a0	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  000a6	ba 02 00 00 00	 mov	 edx, 2
  000ab	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  000b2	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  000b7	66 ba 07 00	 mov	 dx, 7
  000bb	48 8b 4c 24 68	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  000c0	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000c6	90		 npad	 1
  000c7	33 c0		 xor	 eax, eax
  000c9	e9 d4 00 00 00	 jmp	 $LN1@Write
$LN2@Write:

; 1047 : 
; 1048 : 	UInt32 written = count;

  000ce	8b 84 24 a0 00
	00 00		 mov	 eax, DWORD PTR count$[rsp]
  000d5	89 44 24 60	 mov	 DWORD PTR written$[rsp], eax

; 1049 : 
; 1050 : 	if (m_pos + written > m_end)

  000d9	8b 44 24 60	 mov	 eax, DWORD PTR written$[rsp]
  000dd	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  000e5	48 03 41 10	 add	 rax, QWORD PTR [rcx+16]
  000e9	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  000f1	48 3b 41 18	 cmp	 rax, QWORD PTR [rcx+24]
  000f5	76 62		 jbe	 SHORT $LN3@Write

; 1051 : 	{
; 1052 : 		written = static_cast<UInt32>( (m_pos + written) - m_end );

  000f7	8b 44 24 60	 mov	 eax, DWORD PTR written$[rsp]
  000fb	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00103	48 8b 49 10	 mov	 rcx, QWORD PTR [rcx+16]
  00107	48 03 c8	 add	 rcx, rax
  0010a	48 8b c1	 mov	 rax, rcx
  0010d	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00115	48 2b 41 18	 sub	 rax, QWORD PTR [rcx+24]
  00119	89 44 24 60	 mov	 DWORD PTR written$[rsp], eax

; 1053 : 		if (false == resize(m_capacity + written))

  0011d	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00125	8b 40 20	 mov	 eax, DWORD PTR [rax+32]
  00128	03 44 24 60	 add	 eax, DWORD PTR written$[rsp]
  0012c	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00134	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00137	48 89 4c 24 70	 mov	 QWORD PTR tv157[rsp], rcx
  0013c	8b d0		 mov	 edx, eax
  0013e	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00146	48 8b 44 24 70	 mov	 rax, QWORD PTR tv157[rsp]
  0014b	ff 50 08	 call	 QWORD PTR [rax+8]
  0014e	0f b6 c0	 movzx	 eax, al
  00151	85 c0		 test	 eax, eax
  00153	75 04		 jne	 SHORT $LN4@Write

; 1054 : 		{
; 1055 : 			return 0;

  00155	33 c0		 xor	 eax, eax
  00157	eb 49		 jmp	 SHORT $LN1@Write
$LN4@Write:
$LN3@Write:

; 1056 : 		}
; 1057 : 	}
; 1058 : 
; 1059 : 	memcpy(m_pos, buf, count);

  00159	8b 84 24 a0 00
	00 00		 mov	 eax, DWORD PTR count$[rsp]
  00160	44 8b c0	 mov	 r8d, eax
  00163	48 8b 94 24 98
	00 00 00	 mov	 rdx, QWORD PTR buf$[rsp]
  0016b	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00173	48 8b 48 10	 mov	 rcx, QWORD PTR [rax+16]
  00177	e8 00 00 00 00	 call	 memcpy

; 1060 : 	m_pos += count;

  0017c	8b 84 24 a0 00
	00 00		 mov	 eax, DWORD PTR count$[rsp]
  00183	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0018b	48 03 41 10	 add	 rax, QWORD PTR [rcx+16]
  0018f	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00197	48 89 41 10	 mov	 QWORD PTR [rcx+16], rax

; 1061 : 
; 1062 : 	return count;

  0019b	8b 84 24 a0 00
	00 00		 mov	 eax, DWORD PTR count$[rsp]
$LN1@Write:

; 1063 : }

  001a2	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  001a9	c3		 ret	 0
?Write@PacketStream@mu2@@QEAAIPEBEI@Z ENDP		; mu2::PacketStream::Write
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ?_Get_deleter@_Ref_count_base@std@@UEBAPEAXAEBVtype_info@@@Z
_TEXT	SEGMENT
this$ = 8
__formal$ = 16
?_Get_deleter@_Ref_count_base@std@@UEBAPEAXAEBVtype_info@@@Z PROC ; std::_Ref_count_base::_Get_deleter, COMDAT

; 1175 :     virtual void* _Get_deleter(const type_info&) const noexcept {

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 1176 :         return nullptr;

  0000a	33 c0		 xor	 eax, eax

; 1177 :     }

  0000c	c3		 ret	 0
?_Get_deleter@_Ref_count_base@std@@UEBAPEAXAEBVtype_info@@@Z ENDP ; std::_Ref_count_base::_Get_deleter
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ?_Decref@_Ref_count_base@std@@QEAAXXZ
_TEXT	SEGMENT
this$ = 48
?_Decref@_Ref_count_base@std@@QEAAXXZ PROC		; std::_Ref_count_base::_Decref, COMDAT

; 1158 :     void _Decref() noexcept { // decrement use count

$LN11:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 1159 :         if (_MT_DECR(_Uses) == 0) {

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 c0 08	 add	 rax, 8
  00012	b9 ff ff ff ff	 mov	 ecx, -1
  00017	f0 0f c1 08	 lock xadd DWORD PTR [rax], ecx
  0001b	ff c9		 dec	 ecx
  0001d	8b c1		 mov	 eax, ecx
  0001f	85 c0		 test	 eax, eax
  00021	75 3a		 jne	 SHORT $LN2@Decref

; 1160 :             _Destroy();

  00023	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00028	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0002b	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00030	ff 10		 call	 QWORD PTR [rax]

; 1166 :         if (_MT_DECR(_Weaks) == 0) {

  00032	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00037	48 83 c0 0c	 add	 rax, 12
  0003b	b9 ff ff ff ff	 mov	 ecx, -1
  00040	f0 0f c1 08	 lock xadd DWORD PTR [rax], ecx
  00044	ff c9		 dec	 ecx
  00046	8b c1		 mov	 eax, ecx
  00048	85 c0		 test	 eax, eax
  0004a	75 11		 jne	 SHORT $LN2@Decref

; 1167 :             _Delete_this();

  0004c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00051	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00054	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00059	ff 50 08	 call	 QWORD PTR [rax+8]
  0005c	90		 npad	 1
$LN2@Decref:

; 1161 :             _Decwref();
; 1162 :         }
; 1163 :     }

  0005d	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00061	c3		 ret	 0
?_Decref@_Ref_count_base@std@@QEAAXXZ ENDP		; std::_Ref_count_base::_Decref
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ
_TEXT	SEGMENT
$T1 = 32
$T2 = 33
_My_data$ = 40
tv94 = 48
_Bytes$ = 56
_Ptr$ = 64
$T3 = 72
$T4 = 80
_Capacity$ = 88
_Old_ptr$ = 96
_Al$5 = 104
this$ = 128
?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ PROC ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Tidy_deallocate, COMDAT

; 3080 :     _CONSTEXPR20 void _Tidy_deallocate() noexcept { // initialize buffer, deallocating any storage

$LN62:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 78	 sub	 rsp, 120		; 00000078H

; 3081 :         auto& _My_data = _Mypair._Myval2;

  00009	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00011	48 89 44 24 28	 mov	 QWORD PTR _My_data$[rsp], rax

; 453  :         return _Myres > _Small_string_capacity;

  00016	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  0001b	48 83 78 18 0f	 cmp	 QWORD PTR [rax+24], 15
  00020	76 0a		 jbe	 SHORT $LN12@Tidy_deall
  00022	c7 44 24 30 01
	00 00 00	 mov	 DWORD PTR tv94[rsp], 1
  0002a	eb 08		 jmp	 SHORT $LN13@Tidy_deall
$LN12@Tidy_deall:
  0002c	c7 44 24 30 00
	00 00 00	 mov	 DWORD PTR tv94[rsp], 0
$LN13@Tidy_deall:
  00034	0f b6 44 24 30	 movzx	 eax, BYTE PTR tv94[rsp]
  00039	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al

; 3082 :         _My_data._Orphan_all();
; 3083 :         if (_My_data._Large_mode_engaged()) {

  0003d	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  00042	0f b6 c0	 movzx	 eax, al
  00045	85 c0		 test	 eax, eax
  00047	74 7e		 je	 SHORT $LN2@Tidy_deall

; 3107 :         return _Mypair._Get_first();

  00049	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00051	48 89 44 24 48	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3107 :         return _Mypair._Get_first();

  00056	48 8b 44 24 48	 mov	 rax, QWORD PTR $T3[rsp]
  0005b	48 89 44 24 50	 mov	 QWORD PTR $T4[rsp], rax

; 3084 :             _ASAN_STRING_REMOVE(*this);
; 3085 :             auto& _Al = _Getal();

  00060	48 8b 44 24 50	 mov	 rax, QWORD PTR $T4[rsp]
  00065	48 89 44 24 68	 mov	 QWORD PTR _Al$5[rsp], rax

; 3086 :             _Deallocate_for_capacity(_Al, _My_data._Bx._Ptr, _My_data._Myres);

  0006a	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  0006f	48 8b 40 18	 mov	 rax, QWORD PTR [rax+24]
  00073	48 89 44 24 58	 mov	 QWORD PTR _Capacity$[rsp], rax
  00078	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  0007d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00080	48 89 44 24 60	 mov	 QWORD PTR _Old_ptr$[rsp], rax

; 852  :         _Al.deallocate(_Old_ptr, _Capacity + 1); // +1 for null terminator

  00085	48 8b 44 24 58	 mov	 rax, QWORD PTR _Capacity$[rsp]
  0008a	48 ff c0	 inc	 rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  0008d	48 89 44 24 38	 mov	 QWORD PTR _Bytes$[rsp], rax
  00092	48 8b 44 24 60	 mov	 rax, QWORD PTR _Old_ptr$[rsp]
  00097	48 89 44 24 40	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  0009c	48 81 7c 24 38
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  000a5	72 10		 jb	 SHORT $LN38@Tidy_deall

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  000a7	48 8d 54 24 38	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  000ac	48 8d 4c 24 40	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  000b1	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  000b6	90		 npad	 1
$LN38@Tidy_deall:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  000b7	48 8b 54 24 38	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  000bc	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  000c1	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  000c6	90		 npad	 1
$LN2@Tidy_deall:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3090 :         _My_data._Mysize = 0;

  000c7	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000cc	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 3091 :         _My_data._Myres  = _Small_string_capacity;

  000d4	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000d9	48 c7 40 18 0f
	00 00 00	 mov	 QWORD PTR [rax+24], 15

; 3092 :         // the _Traits::assign is last so the codegen doesn't think the char write can alias this
; 3093 :         _Traits::assign(_My_data._Bx._Buf[0], _Elem());

  000e1	c6 44 24 21 00	 mov	 BYTE PTR $T2[rsp], 0
  000e6	b8 01 00 00 00	 mov	 eax, 1
  000eb	48 6b c0 00	 imul	 rax, rax, 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 502  :         _Left = _Right;

  000ef	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _My_data$[rsp]
  000f4	0f b6 54 24 21	 movzx	 edx, BYTE PTR $T2[rsp]
  000f9	88 14 01	 mov	 BYTE PTR [rcx+rax], dl
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3094 :     }

  000fc	48 83 c4 78	 add	 rsp, 120		; 00000078H
  00100	c3		 ret	 0
?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ ENDP ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Tidy_deallocate
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?c_str@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBAPEBDXZ
_TEXT	SEGMENT
$T1 = 0
tv80 = 4
this$ = 8
_Result$2 = 16
_Ptr$ = 24
$T3 = 32
$T4 = 40
this$ = 64
?c_str@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBAPEBDXZ PROC ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::c_str, COMDAT

; 2355 :     _NODISCARD _CONSTEXPR20 _Ret_z_ const _Elem* c_str() const noexcept {

$LN22:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 2356 :         return _Mypair._Myval2._Myptr();

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 89 44 24 08	 mov	 QWORD PTR this$[rsp], rax

; 444  :         const value_type* _Result = _Bx._Buf;

  00013	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00018	48 89 44 24 10	 mov	 QWORD PTR _Result$2[rsp], rax

; 453  :         return _Myres > _Small_string_capacity;

  0001d	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00022	48 83 78 18 0f	 cmp	 QWORD PTR [rax+24], 15
  00027	76 0a		 jbe	 SHORT $LN12@c_str
  00029	c7 44 24 04 01
	00 00 00	 mov	 DWORD PTR tv80[rsp], 1
  00031	eb 08		 jmp	 SHORT $LN13@c_str
$LN12@c_str:
  00033	c7 44 24 04 00
	00 00 00	 mov	 DWORD PTR tv80[rsp], 0
$LN13@c_str:
  0003b	0f b6 44 24 04	 movzx	 eax, BYTE PTR tv80[rsp]
  00040	88 04 24	 mov	 BYTE PTR $T1[rsp], al

; 445  :         if (_Large_mode_engaged()) {

  00043	0f b6 04 24	 movzx	 eax, BYTE PTR $T1[rsp]
  00047	0f b6 c0	 movzx	 eax, al
  0004a	85 c0		 test	 eax, eax
  0004c	74 21		 je	 SHORT $LN5@c_str

; 446  :             _Result = _Unfancy(_Bx._Ptr);

  0004e	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00056	48 89 44 24 18	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  0005b	48 8b 44 24 18	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00060	48 89 44 24 20	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 446  :             _Result = _Unfancy(_Bx._Ptr);

  00065	48 8b 44 24 20	 mov	 rax, QWORD PTR $T3[rsp]
  0006a	48 89 44 24 10	 mov	 QWORD PTR _Result$2[rsp], rax
$LN5@c_str:

; 447  :         }
; 448  : 
; 449  :         return _Result;

  0006f	48 8b 44 24 10	 mov	 rax, QWORD PTR _Result$2[rsp]
  00074	48 89 44 24 28	 mov	 QWORD PTR $T4[rsp], rax

; 2356 :         return _Mypair._Myval2._Myptr();

  00079	48 8b 44 24 28	 mov	 rax, QWORD PTR $T4[rsp]

; 2357 :     }

  0007e	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00082	c3		 ret	 0
?c_str@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBAPEBDXZ ENDP ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::c_str
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ PROC ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >, COMDAT

; 1382 :     _CONSTEXPR20 ~basic_string() noexcept {

$LN82:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 1383 :         _Tidy_deallocate();

  00009	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0000e	e8 00 00 00 00	 call	 ?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Tidy_deallocate
  00013	90		 npad	 1

; 1384 : #if _ITERATOR_DEBUG_LEVEL != 0
; 1385 :         auto&& _Alproxy          = _GET_PROXY_ALLOCATOR(_Alty, _Getal());
; 1386 :         const auto _To_delete    = _Mypair._Myval2._Myproxy;
; 1387 :         _Mypair._Myval2._Myproxy = nullptr;
; 1388 :         _Delete_plain_internal(_Alproxy, _To_delete);
; 1389 : #endif // _ITERATOR_DEBUG_LEVEL != 0
; 1390 :     }

  00014	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00018	c3		 ret	 0
??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ ENDP ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z
_TEXT	SEGMENT
_Back_shift$ = 48
_Ptr_container$ = 56
_Ptr_user$ = 64
_Min_back_shift$ = 72
_Ptr$ = 96
_Bytes$ = 104
?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z PROC ; std::_Adjust_manually_vector_aligned, COMDAT

; 200  : inline void _Adjust_manually_vector_aligned(void*& _Ptr, size_t& _Bytes) {

$LN5:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 201  :     // adjust parameters from _Allocate_manually_vector_aligned to pass to operator delete
; 202  :     _Bytes += _Non_user_size;

  0000e	48 8b 44 24 68	 mov	 rax, QWORD PTR _Bytes$[rsp]
  00013	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00016	48 83 c0 27	 add	 rax, 39			; 00000027H
  0001a	48 8b 4c 24 68	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  0001f	48 89 01	 mov	 QWORD PTR [rcx], rax

; 203  : 
; 204  :     const uintptr_t* const _Ptr_user = static_cast<uintptr_t*>(_Ptr);

  00022	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00027	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0002a	48 89 44 24 40	 mov	 QWORD PTR _Ptr_user$[rsp], rax

; 205  :     const uintptr_t _Ptr_container   = _Ptr_user[-1];

  0002f	b8 08 00 00 00	 mov	 eax, 8
  00034	48 6b c0 ff	 imul	 rax, rax, -1
  00038	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr_user$[rsp]
  0003d	48 8b 04 01	 mov	 rax, QWORD PTR [rcx+rax]
  00041	48 89 44 24 38	 mov	 QWORD PTR _Ptr_container$[rsp], rax

; 206  : 
; 207  :     // If the following asserts, it likely means that we are performing
; 208  :     // an aligned delete on memory coming from an unaligned allocation.
; 209  :     _STL_ASSERT(_Ptr_user[-2] == _Big_allocation_sentinel, "invalid argument");
; 210  : 
; 211  :     // Extra paranoia on aligned allocation/deallocation; ensure _Ptr_container is
; 212  :     // in range [_Min_back_shift, _Non_user_size]
; 213  : #ifdef _DEBUG
; 214  :     constexpr uintptr_t _Min_back_shift = 2 * sizeof(void*);
; 215  : #else // ^^^ defined(_DEBUG) / !defined(_DEBUG) vvv
; 216  :     constexpr uintptr_t _Min_back_shift = sizeof(void*);

  00046	48 c7 44 24 48
	08 00 00 00	 mov	 QWORD PTR _Min_back_shift$[rsp], 8

; 217  : #endif // ^^^ !defined(_DEBUG) ^^^
; 218  :     const uintptr_t _Back_shift = reinterpret_cast<uintptr_t>(_Ptr) - _Ptr_container;

  0004f	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00054	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr_container$[rsp]
  00059	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0005c	48 2b c1	 sub	 rax, rcx
  0005f	48 89 44 24 30	 mov	 QWORD PTR _Back_shift$[rsp], rax

; 219  :     _STL_VERIFY(_Back_shift >= _Min_back_shift && _Back_shift <= _Non_user_size, "invalid argument");

  00064	48 83 7c 24 30
	08		 cmp	 QWORD PTR _Back_shift$[rsp], 8
  0006a	72 08		 jb	 SHORT $LN3@Adjust_man
  0006c	48 83 7c 24 30
	27		 cmp	 QWORD PTR _Back_shift$[rsp], 39 ; 00000027H
  00072	76 19		 jbe	 SHORT $LN2@Adjust_man
$LN3@Adjust_man:
  00074	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR [rsp+32], 0
  0007d	45 33 c9	 xor	 r9d, r9d
  00080	45 33 c0	 xor	 r8d, r8d
  00083	33 d2		 xor	 edx, edx
  00085	33 c9		 xor	 ecx, ecx
  00087	e8 00 00 00 00	 call	 _invoke_watson
  0008c	90		 npad	 1
$LN2@Adjust_man:

; 220  :     _Ptr = reinterpret_cast<void*>(_Ptr_container);

  0008d	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00092	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr_container$[rsp]
  00097	48 89 08	 mov	 QWORD PTR [rax], rcx
$LN4@Adjust_man:

; 221  : }

  0009a	48 83 c4 58	 add	 rsp, 88			; 00000058H
  0009e	c3		 ret	 0
?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ENDP ; std::_Adjust_manually_vector_aligned
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?_Throw_bad_array_new_length@std@@YAXXZ
_TEXT	SEGMENT
$T1 = 32
?_Throw_bad_array_new_length@std@@YAXXZ PROC		; std::_Throw_bad_array_new_length, COMDAT

; 107  : [[noreturn]] inline void _Throw_bad_array_new_length() {

$LN3:
  00000	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 108  :     _THROW(bad_array_new_length{});

  00004	48 8d 4c 24 20	 lea	 rcx, QWORD PTR $T1[rsp]
  00009	e8 00 00 00 00	 call	 ??0bad_array_new_length@std@@QEAA@XZ ; std::bad_array_new_length::bad_array_new_length
  0000e	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:_TI3?AVbad_array_new_length@std@@
  00015	48 8d 4c 24 20	 lea	 rcx, QWORD PTR $T1[rsp]
  0001a	e8 00 00 00 00	 call	 _CxxThrowException
  0001f	90		 npad	 1
$LN2@Throw_bad_:

; 109  : }

  00020	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00024	c3		 ret	 0
?_Throw_bad_array_new_length@std@@YAXXZ ENDP		; std::_Throw_bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gbad_array_new_length@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gbad_array_new_length@std@@UEAAPEAXI@Z PROC		; std::bad_array_new_length::`scalar deleting destructor', COMDAT
$LN20:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gbad_array_new_length@std@@UEAAPEAXI@Z ENDP		; std::bad_array_new_length::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??0bad_array_new_length@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
__that$ = 56
??0bad_array_new_length@std@@QEAA@AEBV01@@Z PROC	; std::bad_array_new_length::bad_array_new_length, COMDAT
$LN14:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000e	48 8b 54 24 38	 mov	 rdx, QWORD PTR __that$[rsp]
  00013	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00018	e8 00 00 00 00	 call	 ??0bad_alloc@std@@QEAA@AEBV01@@Z
  0001d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00022	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_array_new_length@std@@6B@
  00029	48 89 08	 mov	 QWORD PTR [rax], rcx
  0002c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00031	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00035	c3		 ret	 0
??0bad_array_new_length@std@@QEAA@AEBV01@@Z ENDP	; std::bad_array_new_length::bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??1bad_array_new_length@std@@UEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1bad_array_new_length@std@@UEAA@XZ PROC		; std::bad_array_new_length::~bad_array_new_length, COMDAT
$LN14:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  00018	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 83 c0 08	 add	 rax, 8
  00021	48 8b c8	 mov	 rcx, rax
  00024	e8 00 00 00 00	 call	 __std_exception_destroy
  00029	90		 npad	 1
  0002a	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0002e	c3		 ret	 0
??1bad_array_new_length@std@@UEAA@XZ ENDP		; std::bad_array_new_length::~bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0bad_array_new_length@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 16
??0bad_array_new_length@std@@QEAA@XZ PROC		; std::bad_array_new_length::bad_array_new_length, COMDAT

; 144  :     {

$LN14:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi

; 67   :     {

  00006	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0000b	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00012	48 89 08	 mov	 QWORD PTR [rax], rcx

; 66   :         : _Data()

  00015	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 83 c0 08	 add	 rax, 8
  0001e	48 8b f8	 mov	 rdi, rax
  00021	33 c0		 xor	 eax, eax
  00023	b9 10 00 00 00	 mov	 ecx, 16
  00028	f3 aa		 rep stosb

; 68   :         _Data._What = _Message;

  0002a	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0002f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_0BF@KINCDENJ@bad?5array?5new?5length@
  00036	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 133  :     {

  0003a	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0003f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_alloc@std@@6B@
  00046	48 89 08	 mov	 QWORD PTR [rax], rcx

; 144  :     {

  00049	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_array_new_length@std@@6B@
  00055	48 89 08	 mov	 QWORD PTR [rax], rcx

; 145  :     }

  00058	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0005d	5f		 pop	 rdi
  0005e	c3		 ret	 0
??0bad_array_new_length@std@@QEAA@XZ ENDP		; std::bad_array_new_length::bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gbad_alloc@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gbad_alloc@std@@UEAAPEAXI@Z PROC			; std::bad_alloc::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gbad_alloc@std@@UEAAPEAXI@Z ENDP			; std::bad_alloc::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0bad_alloc@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
__that$ = 56
??0bad_alloc@std@@QEAA@AEBV01@@Z PROC			; std::bad_alloc::bad_alloc, COMDAT
$LN9:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 20	 sub	 rsp, 32			; 00000020H

; 73   :     {

  0000f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  0001b	48 89 08	 mov	 QWORD PTR [rax], rcx

; 72   :         : _Data()

  0001e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 83 c0 08	 add	 rax, 8
  00027	48 8b f8	 mov	 rdi, rax
  0002a	33 c0		 xor	 eax, eax
  0002c	b9 10 00 00 00	 mov	 ecx, 16
  00031	f3 aa		 rep stosb

; 74   :         __std_exception_copy(&_Other._Data, &_Data);

  00033	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 83 c0 08	 add	 rax, 8
  0003c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR __that$[rsp]
  00041	48 83 c1 08	 add	 rcx, 8
  00045	48 8b d0	 mov	 rdx, rax
  00048	e8 00 00 00 00	 call	 __std_exception_copy
  0004d	90		 npad	 1
  0004e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_alloc@std@@6B@
  0005a	48 89 08	 mov	 QWORD PTR [rax], rcx
  0005d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00062	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00066	5f		 pop	 rdi
  00067	c3		 ret	 0
??0bad_alloc@std@@QEAA@AEBV01@@Z ENDP			; std::bad_alloc::bad_alloc
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gexception@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gexception@std@@UEAAPEAXI@Z PROC			; std::exception::`scalar deleting destructor', COMDAT
$LN10:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gexception@std@@UEAAPEAXI@Z ENDP			; std::exception::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ?what@exception@std@@UEBAPEBDXZ
_TEXT	SEGMENT
tv69 = 0
this$ = 32
?what@exception@std@@UEBAPEBDXZ PROC			; std::exception::what, COMDAT

; 95   :     {

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 18	 sub	 rsp, 24

; 96   :         return _Data._What ? _Data._What : "Unknown exception";

  00009	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00013	74 0f		 je	 SHORT $LN3@what
  00015	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0001e	48 89 04 24	 mov	 QWORD PTR tv69[rsp], rax
  00022	eb 0b		 jmp	 SHORT $LN4@what
$LN3@what:
  00024	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BC@EOODALEL@Unknown?5exception@
  0002b	48 89 04 24	 mov	 QWORD PTR tv69[rsp], rax
$LN4@what:
  0002f	48 8b 04 24	 mov	 rax, QWORD PTR tv69[rsp]

; 97   :     }

  00033	48 83 c4 18	 add	 rsp, 24
  00037	c3		 ret	 0
?what@exception@std@@UEBAPEBDXZ ENDP			; std::exception::what
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0exception@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
_Other$ = 56
??0exception@std@@QEAA@AEBV01@@Z PROC			; std::exception::exception, COMDAT

; 73   :     {

$LN4:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  0000f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  0001b	48 89 08	 mov	 QWORD PTR [rax], rcx

; 72   :         : _Data()

  0001e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 83 c0 08	 add	 rax, 8
  00027	48 8b f8	 mov	 rdi, rax
  0002a	33 c0		 xor	 eax, eax
  0002c	b9 10 00 00 00	 mov	 ecx, 16
  00031	f3 aa		 rep stosb

; 74   :         __std_exception_copy(&_Other._Data, &_Data);

  00033	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 83 c0 08	 add	 rax, 8
  0003c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Other$[rsp]
  00041	48 83 c1 08	 add	 rcx, 8
  00045	48 8b d0	 mov	 rdx, rax
  00048	e8 00 00 00 00	 call	 __std_exception_copy
  0004d	90		 npad	 1

; 75   :     }

  0004e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00057	5f		 pop	 rdi
  00058	c3		 ret	 0
??0exception@std@@QEAA@AEBV01@@Z ENDP			; std::exception::exception
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
