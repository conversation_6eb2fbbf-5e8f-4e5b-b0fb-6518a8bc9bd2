﻿#include "stdafx.h"
#include <Backend/Zone/ZoneServer.h>
#include <Backend/Zone/Action/ActionNpcScript.h>
#include <Backend/Zone/Action/ActionNpcDamage.h>
#include <Backend/Zone/Action/ActionNpcAggro.h>
#include <Backend/Zone/Action/ActionNpc.h>
#include <Backend/Zone/Action/ActionNpcSkill.h>
#include <Backend/Zone/Action/ActionPlayerInventory.h>
#include <Backend/Zone/Action/ActionBuff.h>
#include <Backend/Zone/Action/ActionNpcColosseum.h>
#include <Backend/Zone/EntityFactory/NpcEntityFactoryImpl.h>
#include <Backend/Zone/EntityFactory/NpcStateFactory.h>
#include <Backend/Zone/State/StateNpcChase.h>
#include <Backend/Zone/State/StateNpcMoveToRightNow.h>
#include <Backend/Zone/System/DropSystem.h>
#include <Backend/Zone/System/LogicEffectSystem.h>
#include <Backend/Zone/System/LogicEffectSystem/LogicEffectProcessor.h>
#include <Backend/Zone/System/SpawnSystem.h>
#include <Backend/Zone/Action/ActionNpcMove.h>
#include <Backend/Zone/View/ViewPosition.h>

#ifdef __Renewal_Battle_IFF_by_kangms_180829
#include <Backend/Zone/Action/ActionNpc/ActionNpcCognition.h>
#endif//__Renewal_Battle_IFF_by_kangms_180829

namespace mu2
{

ActionNpcScript::ActionNpcScript(EntityNpc* owner )
	: Action4Npc( owner, ID )
, m_sector(nullptr)
, m_L(nullptr)
, m_controlById()
, m_controlByType()
, m_npcLuaName()
, m_npcIndex(0)
, m_aiIndex(0)
, m_timers()
, m_keyValue()
{
	CounterInc("ActionNpcScript");
}

ActionNpcScript::~ActionNpcScript(void)
{
	CounterDec("ActionNpcScript");
	// 서버 내려 갈 때 이슈가 있을 수 있다.
	unregisterNpc();
}
void ActionNpcScript::Reload()
{

	State* pState = GetOwnerNpc()->GetHsm().GetCurrent();
	Int32 CurStateID = pState->GetId();

	m_L = m_sector->GetLuaState();
	MU2_ASSERT(m_L);

	try
	{
		createControl();
		createHsm();
		registerNpc();
	}
	catch (Exception& e)
	{
		MU2_ASSERT(0);
		MU2_WARN_LOG( LogCategory::CONTENTS, "[E][%s] npcId : %d, exception code %d", __FUNCTION__, m_npcIndex, e.GetType());
	}

	GetOwnerNpc()->GetHsm().Init(GetOwnerNpc());

	if (CurStateID != STATE_NPC_DEAD)
	{
		GetOwnerNpc()->GetHsm().Tran(EntityState::STATE_NPC_IDLE);
	}
}
Bool ActionNpcScript::Setup( Sector* sector, const NpcSpawnInfo& npcSpawnInfo)
{
	VERIFY_RETURN(sector, false)

	IndexNpc aiIndex = npcSpawnInfo.aiIndex ? npcSpawnInfo.aiIndex : npcSpawnInfo.npcInfoElem.aiIndex;

	clear();		

	m_sector = sector;	
	m_npcIndex = npcSpawnInfo.npcInfoElem.index;

	if ( aiIndex == 0 )
	{
		aiIndex = m_npcIndex;
	}

	m_aiIndex = aiIndex;

	m_L = sector->GetLuaState();
	MU2_ASSERT( m_L );

	try
	{
		createControl();
		createHsm();
		registerNpc();
	}
	catch (LuaException& e)
	{
		MU2_ASSERT( 0 );
		MU2_ERROR_LOG( LogCategory::CONTENTS, "[Lua Exception][%s] npcId : %d", e.ErrorMsg.c_str(), npcSpawnInfo.npcInfoElem.index);
		return false;
	}
	catch ( Exception& e )
	{
		MU2_ASSERT( 0 );
		MU2_WARN_LOG( LogCategory::CONTENTS, "[Unhandled Exception] npcId : %d, exception code %d", npcSpawnInfo.npcInfoElem.index, e.GetType() );
		return false;
	}
	
	return true;
}

UInt32 ActionNpcScript::GetId() const
{
	return GetOwnerNpc()->GetId().GetSeq();
}

Bool ActionNpcScript::AddState(EntityState parentStateId, EntityState stateId )
{
	Hsm& hsm = GetOwnerNpc()->GetHsm();

	State* parent = hsm.GetState( parentStateId );
	VERIFY_RETURN(parent, false);

	State* child = theNpcStateFactory.Create( stateId );
	VERIFY_RETURN(child, false);

	if (parent->AppendChild( child ) == NULL)
	{
		delete child;
		return false;
	}

	return true;
	//return child->Init( &hsm, stateId, parent );
}

Bool ActionNpcScript::CloneState(EntityState parentStateId, EntityState stateId, EntityState cloneStateId )
{
	Hsm& hsm = GetOwnerNpc()->GetHsm();

	State* parent = hsm.GetState( parentStateId );

	if ( parent == NULL ) 
	{
		MU2_WARN_LOG( LogCategory::CONTENTS, "[E][%s] Parent %x not found", __FUNCTION__, parentStateId );
		return false;
	}

	State* child = theNpcStateFactory.Create( cloneStateId );
	if ( child == NULL )
	{
		MU2_WARN_LOG( LogCategory::CONTENTS, "[E][%s] Faield to create state %x ", __FUNCTION__, stateId);
		return false;
	}

	//return parent->AppendChild(child) != NULL;
	if (parent->AppendChild( child ) == NULL)
	{
		delete child;
		return false;
	}

	return true;
	//return child->Init( &hsm, stateId, parent );
}

Bool ActionNpcScript::Tran(EntityState eState )
{
	Hsm& hsm = GetOwnerNpc()->GetHsm();

	if ( hsm.GetState( eState ) == NULL  )
	{
		//const AttributeNpc* an = GetEntityAttribute( GetOwnerNpc() );

		MU2_WARN_LOG( LogCategory::CONTENTS, "[%s] State %d not found [npcId:%d]", __FUNCTION__, eState, GetOwnerNpc()->GetNpcIndex());

		return false;
	}

	hsm.Tran( eState );

	return true;
}

void ActionNpcScript::Log( const char* msg )
{
	if ( msg == nullptr || strlen(msg) == 0 )
	{
		return;
	}

	MU2_TRACE_LOG(LogCategory::CONTENTS, "[%d] %s", GetId(), msg );
}

void ActionNpcScript::Debug( const char* msg )
{
	if ( msg == nullptr || strlen(msg) == 0 )
	{
		return;
	}

	MU2_DEBUG_LOG( LogCategory::DEBUG_LUA_SCRIPT, "[%d] %s", GetId(), msg );
}

UInt32 ActionNpcScript::GetNearestTargetRange()
{
	return GetOwnerNpc()->GetAction< ActionNpcSkill >()->GetNearestTargetRange();
}

void ActionNpcScript::SelfTarget()
{
	return GetOwnerNpc()->GetAction< ActionNpcSkill >()->SelfTarget();
}

Bool ActionNpcScript::SearchEnemy()
{
	return GetOwnerNpc()->GetAction< ActionNpcSkill >()->SearchEnemy();
}

EntityId ActionNpcScript::SearchMasterEnemy()
{
	EntityNpc* owner = GetOwnerNpc();
	EntityUnit* master = owner->GetAction< ActionNpc >()->GetMasterUnit();
	if ( master == nullptr )
	{
		return 0;
	}

	auto actNpcSkill = master->GetAction< ActionNpcSkill >();
	if ( actNpcSkill->SearchEnemy() == false )
	{
		return 0;
	}

	return actNpcSkill->GetTargetUnitId();
}

void ActionNpcScript::ClearSkillTarget()
{
	GetOwnerNpc()->GetSkillAction().ClearSkillTarget();
}

void ActionNpcScript::FixEnemyId(EntityId enenmyId )
{	
	GetOwnerNpc()->GetNpcAggroAction().SetFixedTarget( enenmyId );
}

UInt32 ActionNpcScript::GetMonsterCountAroundEnemy( UInt32 range )
{
	return GetOwnerNpc()->GetSkillAction().GetMonsterCountAroundEnemy( range );
}

Bool ActionNpcScript::SelectSkill(IndexSkill skillId )
{
	return GetOwnerNpc()->GetAction< ActionNpcSkill >()->SelectSkill( skillId );
}

Bool ActionNpcScript::DoDefaultSkill()
{
	ActionNpcSkill* ans = GetEntityAction(GetOwnerNpc());
	const DefaultSkillElem* defaultSkillElem = ans->GetDefaultSkillElem();
	if (nullptr == defaultSkillElem)
	{
		return false;
	}		

	auto it = defaultSkillElem->defaultSkillUnits.begin();
	for (; it != defaultSkillElem->defaultSkillUnits.end(); ++it)
	{
		if (ans->SelectSkill(it->skillIndex))
		{
			if (ans->SetSkillTarget(it->targetSelectRange, it->targetMonsterId, it->eTargetType, it->targetFilter, it->targetFilterValue))
			{
				if (this->checkDefaultSkillCondition(it->skillFireCondition, it->skillFireValue))
				{
					return true;
				}
			}
		}
	}

	return false;
}

Bool ActionNpcScript::ChangeDefaultSkillOrderPriority()
{
	ActionNpcSkill* ans = GetEntityAction(GetOwnerNpc());
	const DefaultSkillElem* defaultSkillElem = ans->GetDefaultSkillElem();	
	if (nullptr == defaultSkillElem)
	{
		return false;
	}

	IndexSkill currentSelectSkillId = ans->GetCurrentSkillId();

	auto it = defaultSkillElem->defaultSkillUnits.begin();
	for (; it != defaultSkillElem->defaultSkillUnits.end(); ++it)
	{
		if (currentSelectSkillId)
		{
			// 스킬이 선택된 상태에서 다시 호출될 경우 우선순위에 따라 높은 우선순위를 가진 스킬이 선택된다. 
			// defaultSkillElem->defaultSkillUnits에는 우선순위가 높은 순서대로 저장되어 있다. 
			if (currentSelectSkillId == it->skillIndex)
			{
				return false;
			}

			if (false == ans->CanUseSkill(it->skillIndex))
			{
				continue;
			}

			if (false == ans->CheckSkillTarget(it->targetSelectRange, it->targetMonsterId, it->eTargetType, it->targetFilter, it->targetFilterValue))
			{
				continue;
			}

			if (false == this->checkDefaultSkillCondition(it->skillFireCondition, it->skillFireValue))
			{
				continue;
			}
		}

		if (ans->SelectSkill(it->skillIndex))
		{
			if (ans->SetSkillTarget(it->targetSelectRange, it->targetMonsterId, it->eTargetType, it->targetFilter))
			{
				if (this->checkDefaultSkillCondition(it->skillFireCondition, it->skillFireValue))
				{
					return true;
				}

			}
		}
	}

	return false;
}


Bool ActionNpcScript::IsTargetWithInSkillRangeToChase()
{
	return GetOwnerNpc()->GetAction< ActionNpcSkill >()->IsTargetWithInSkillRangeToChase();
}

Bool ActionNpcScript::IsTargetWithInDistance( UInt32 distance )
{
	return GetOwnerNpc()->GetAction< ActionNpcSkill >()->IsTargetWithInDistance( distance );
}

Int32 ActionNpcScript::GetSurroundTargetCount( UInt32 distance )
{
	return GetOwnerNpc()->GetAction< ActionNpcSkill >()->GetSurroundTargetCount( distance );
}

void ActionNpcScript::SetKeyValue( Int32 key, Int32 valueOfKey )
{
	auto itr = m_keyValue.find( key );
	if ( itr != m_keyValue.end() )
	{
		itr->second = valueOfKey;
	}
	else
	{
		m_keyValue.insert( KeyValueMap::value_type( key, valueOfKey ) );
	}
}

void ActionNpcScript::AddKeyValue( Int32 key, Int32 valueOfKey )
{
	auto itr = m_keyValue.find( key );
	if ( itr != m_keyValue.end() )
	{
		itr->second += valueOfKey;
	}
	else
	{
		m_keyValue.insert( KeyValueMap::value_type( key, valueOfKey ) );
	}
}

Int32 ActionNpcScript::GetKeyValue( Int32 key )
{
	auto itr = m_keyValue.find( key );
	if ( itr == m_keyValue.end() )
	{
		return -1;
	}
	else
	{
		return itr->second;
	}
}


void ActionNpcScript::DelKeyValue( Int32 key )
{
	m_keyValue.erase( key );
}

Bool ActionNpcScript::IsKeyValue( Int32 key )
{
	auto itr = m_keyValue.find( key );
	if ( itr == m_keyValue.end() )
	{
		return false;
	}
	else
	{
		return true;
	}
}

void ActionNpcScript::SetServantKeyValue(Int32 key, Int32 valueOfKey)
{
	ActionSkillControl* actionNpcSkillctrl = GetOwnerNpc()->GetAction< ActionSkillControl >();
	actionNpcSkillctrl->ServantSetKeyValue( key, valueOfKey );
}

void ActionNpcScript::AddServantKeyValue(Int32 key, Int32 valueOfKey)
{
	ActionSkillControl* actionNpcSkillctrl = GetOwnerNpc()->GetAction< ActionSkillControl >();
	actionNpcSkillctrl->ServantAddKeyValue(key, valueOfKey);
}

void ActionNpcScript::SetMasterKeyValue(Int32 key, Int32 valueOfKey)
{
	auto master = GetOwnerNpc()->GetAction<ActionNpc>()->GetMasterUnit();
	if (master == nullptr)
	{
		return;
	}

	auto actNpcScript = master->GetAction<ActionNpcScript>();
	if (nullptr == actNpcScript)
	{
		return;
	}

	actNpcScript->SetKeyValue(key, valueOfKey);
}

void ActionNpcScript::AddMasterKeyValue(Int32 key, Int32 valueOfKey)
{
	auto master = GetOwnerNpc()->GetAction<ActionNpc>()->GetMasterUnit();
	if (master == nullptr)
	{
		return;
	}

	auto actNpcScript = master->GetAction<ActionNpcScript>();
	if (nullptr == actNpcScript)
	{
		return;
	}

	actNpcScript->AddKeyValue(key, valueOfKey);
}

Int32 ActionNpcScript::GetPercentHP()
{
	return GetOwnerNpc()->GetAction< ActionAbility >()->GetPercentHp();
}

UInt32 ActionNpcScript::GetSelectedSkillRange()
{
	ActionNpcSkill* ans = GetEntityAction( GetOwnerNpc() );

	return ans->GetSelectedSkillRange();
}

void ActionNpcScript::CancelBuff(IndexSkill skillIndex )
{
	theLogicEffectSystem.OnCancelBuffCast(GetOwnerNpc(), skillIndex );
}

IndexSkill ActionNpcScript::GetCurrentSKillId()
{
	ActionNpcSkill* ans = GetEntityAction( GetOwnerNpc() );

	return ans->GetCurrentSkillId();
}

IndexSkill ActionNpcScript::GetMasterCurrentSKillId()
{
	ActionNpc* actionNpc = GetOwnerNpc()->GetAction<ActionNpc>();
	EntityUnit* masterNpc = actionNpc->GetMasterUnit();
	if (!masterNpc)
		return 0;

	ActionNpcSkill* masterSkill = masterNpc->GetAction<ActionNpcSkill>();
	return masterSkill->GetCurrentSkillId();
}

Bool ActionNpcScript::StartSkill()
{
	ActionNpcSkill* ans = GetEntityAction( GetOwnerNpc() );

	return ans->StartSkill();
}

Bool ActionNpcScript::FireSkill()
{
	ActionNpcSkill* ans = GetEntityAction( GetOwnerNpc() );

	return ans->FireSkill();
}

Bool ActionNpcScript::SetTimer( UInt32 timerId )
{
	auto iter = m_timers.find( timerId );

	if ( iter != m_timers.end() )
	{
		// 기존 타이머 재사용
		ResetTimer( timerId );

		return true;
	}

	TimerEntry e;
	e.id = timerId;
	e.tick = ::GetTickCount();

	m_timers.insert( TimerEntryMap::value_type( timerId, e ) );

	return true;
}

UInt32 ActionNpcScript::GetTimer( UInt32 timerId )
{
	auto iter = m_timers.find( timerId );

	if ( iter == m_timers.end() )
	{
		MU2_WARN_LOG( LogCategory::CONTENTS, "[E][%s] timer %d not found", __FUNCTION__, timerId );

		// 큰 시간을 돌려줘서 스크립트에서 동작하도록 한다.
		return 1000000;
	}

	// 이 tick count에 의존하는 거 다 수정할 것!!!!
	return (::GetTickCount() - iter->second.tick);
}

void ActionNpcScript::ResetTimer( UInt32 timerId )
{
	auto iter = m_timers.find( timerId );

	if ( iter == m_timers.end() )
	{
		MU2_WARN_LOG( LogCategory::CONTENTS, "[E][%s] timer %d not found", __FUNCTION__, timerId );
		return;
	}

	iter->second.tick = ::GetTickCount();
}

void ActionNpcScript::RemoveTimer( UInt32 timerId )
{
	// 키로 지우는 버전
	m_timers.erase( timerId );
}

Bool ActionNpcScript::IsTimer( UInt32 timerId )
{
	auto iter = m_timers.find( timerId );

	if ( iter == m_timers.end() )
	{
		return false;
	}

	return true;
}

Bool ActionNpcScript::HasAggroTargetWithin( UInt32 range )
{
	ActionNpcAggro* ang = GetEntityAction( GetOwnerNpc() );

	return ang->HasAggroTargetWithin( range );
}

Bool ActionNpcScript::IsMasterBattleMode()
{
	auto actNpc = GetOwnerNpc()->GetAction<ActionNpc>();
	auto master = actNpc->GetMasterUnit();

	if ( nullptr == master )
	{
		return false;
	}

	auto actMasterSkillControl = master->GetAction<ActionSkillControl>();
	if ( !actMasterSkillControl->IsBattleMode() )
	{
		return false;
	}
	
	return true;
}

Bool ActionNpcScript::HasAggroTarget()
{
	ActionNpcAggro* aggroAction = GetOwnerNpc()->GetAction<ActionNpcAggro>();
	if( nullptr == aggroAction )
	{
		return false;
	}		

	return aggroAction->IsHavingAggroTarget();
}


// 나 자신을 죽임
void ActionNpcScript::KillMyself()
{
	Hsm& hsm = GetOwnerNpc()->GetHsm();
	hsm.Tran(EntityState::STATE_NPC_DEAD);

	auto actDamage = GetOwnerNpc()->GetAction< ActionNpcDamage >();
	if( nullptr != actDamage )
	{
		actDamage->PreprocessDamaged();
	}	
}

void ActionNpcScript::EraseMyself()
{
	ActionNpc* actNpc = GetOwnerNpc()->GetAction< ActionNpc >();
	if( nullptr == actNpc )
	{
		return;
	}		

	actNpc->SetIsDropItem(false);
	actNpc->SetIsGiveExp(false);

	KillMyself();
}

void ActionNpcScript::ClearAggro()
{
	ActionNpcAggro* ana = GetEntityAction( GetOwnerNpc() );

	ana->ClearCurrentAggro();
}

Bool ActionNpcScript::IsCooltimeOver()
{
	ActionNpcSkill* ans = GetEntityAction( GetOwnerNpc() );

	return ans->IsCooltimeOver( ans->GetCurrentSkillId() );
}

Bool ActionNpcScript::IsSpecificCooltimeOver( const IndexSkill skillId )
{
	ActionNpcSkill* ans = GetEntityAction( GetOwnerNpc() );

	return ans->IsCooltimeOver( skillId );
}

Bool ActionNpcScript::IsSkillWaitTimeOver()
{
	ActionNpcSkill* ans = GetEntityAction( GetOwnerNpc() );

	return ans->IsSkillWaitTimeOver();
}

Bool ActionNpcScript::IsSkillTimeOver()
{
	ActionNpcSkill* ans = GetEntityAction( GetOwnerNpc() );

	return ans->IsSkillTimeOver();
}

Bool ActionNpcScript::HasChasedTooFar()
{
	ActionNpcMove* anm = GetEntityAction( GetOwnerNpc() );

	return anm->HasChasedTooFar();
}

Bool ActionNpcScript::HasMovedTooFar()
{
	ActionNpcMove* anm = GetEntityAction( GetOwnerNpc() );

	return anm->HasMovedTooFar();
}

Bool ActionNpcScript::HasMovedOver( const UInt32 range )
{
	ActionNpcMove* anm = GetEntityAction( GetOwnerNpc() );

	return anm->HasMovedOver( range );
}

Bool ActionNpcScript::IsNotMoving()
{
	ActionNpcMove* anm = GetEntityAction( GetOwnerNpc() );
	return anm->IsHalt() || anm->IsNearGoal();
}

UInt32 ActionNpcScript::GetMana()
{
	const AttributeNpc* an = GetEntityAttribute( GetOwnerNpc() );

	return an->mana;
}

Bool ActionNpcScript::IsLastSkill(IndexSkill skillId )
{
	ActionNpcSkill* ans = GetEntityAction( GetOwnerNpc() );

	return ans->IsLastSkill( skillId );
}

IndexSkill ActionNpcScript::GetLastSkill() const
{
	ActionNpcSkill* ans = GetEntityAction( GetOwnerNpc() );

	return ans->GetLastSkill();
}

void ActionNpcScript::SetLastSkill(IndexSkill skillId )
{
	ActionNpcSkill* ans = GetEntityAction( GetOwnerNpc() );

	ans->SetLastSkill( skillId );
}

void ActionNpcScript::SetBattleMoveMinRange( UInt32 range )
{
	AttributeNpc* attrNpc = GetEntityAttribute( GetOwnerNpc() );

	attrNpc->battleMoveMinRange = range;
}

void ActionNpcScript::SetBattleMoveRangeLimit( UInt32 rangeLimit )
{
	AttributeNpc* attrNpc = GetEntityAttribute( GetOwnerNpc() );

	attrNpc->battleMoveLimit = rangeLimit;
}

Bool ActionNpcScript::IsCurrentSkill(IndexSkill skillId )
{
	ActionNpcSkill* ans = GetEntityAction( GetOwnerNpc() );

	return ans->IsCurrentSkill( skillId );
}

Bool ActionNpcScript::HasSelectedSkill()
{
	ActionNpcSkill* ans = GetEntityAction( GetOwnerNpc() );

	return ans->HasSelectedSkill();
}

UInt32 ActionNpcScript::GetMaxSkillRange()
{
	ActionNpcSkill* ans = GetEntityAction( GetOwnerNpc() );

	IndexSkill curSkillId = ans->GetCurrentSkillId();
	const SkillInfoElem* elem = SCRIPTS.GetSkillInfoScript( curSkillId );	
	if ( elem == NULL )
	{
		return 0;
	}

	return elem->reMaxRange;
}

UInt32 ActionNpcScript::GetEntityCountWithinRange(UInt32 monsterId, UInt32 range)
{
	ActionNpcSkill* ans = GetEntityAction( GetOwnerNpc() );

	VectorUnit candidates;
	if( false == ans->GetUnitsByRangeAndConditionAndID(range, monsterId, TargetType::All, candidates) )  
	{
		return 0;
	}

	return static_cast<UInt32>(candidates.size());
}

UInt32 ActionNpcScript::GetMyServantCount(IndexNpc npcIndex /*= 0*/)
{
	ActionSkillControl* ans = GetOwnerNpc()->GetAction< ActionSkillControl >();
	if( ans == nullptr )
	{
		return 0;
	}

	UInt32 servantCount = (0 == npcIndex) ? ans->GetServantNpcCount() : ans->GetServantNpcCount(npcIndex);

	return servantCount;
}

Bool ActionNpcScript::SetSkillTarget( UInt32 targetExtractRange, IndexNpc targetID, TargetType::Type targetType, UInt32 targetFilter, Int32 conditionValue /*= -1 */ )
{
	ActionNpcSkill* ans = GetEntityAction(GetOwnerNpc());

	return ans->SetSkillTarget(targetExtractRange, targetID, targetType, targetFilter, conditionValue );
}

Bool ActionNpcScript::SetFarthestTarget()
{
	ActionNpcSkill* ans = GetEntityAction(GetOwnerNpc());
	return ans->SearchFarthestEnemy();
}

Bool ActionNpcScript::SetMasterSkillTarget()
{
	ActionNpc* actionNpc = GetOwnerNpc()->GetAction< ActionNpc >();
	EntityUnit* master = actionNpc->GetMasterUnit();
	if (!master)
		return false;

	ActionNpcSkill* masterAcitonNpc = master->GetAction<ActionNpcSkill>();
	EntityId targetId = masterAcitonNpc->GetTargetUnitId();

	if (targetId == 0)
		return false;

	ActionNpcSkill* ans = GetEntityAction(GetOwnerNpc());
	ans->SetTargetUnitId(targetId);

	return true;
}
void ActionNpcScript::SetSkillDirection( Float direction )
{
	ActionNpcSkill* ans = GetEntityAction( GetOwnerNpc() );

	return ans->SetSkillDirection( direction );
}

Bool ActionNpcScript::SetSkillPos( UInt32 minRange, UInt32 maxRange, Float degree, Bool setTargetPos /*= false*/, Bool isWalkablePos /*= false*/, Bool useCalcCustomSkilDegree /*= false*/, Bool isAccurate /*= false */ )
{
	ActionNpcSkill* ans = GetEntityAction( GetOwnerNpc() );

	return ans->SetSkillPos( minRange, maxRange, degree, setTargetPos, isWalkablePos, useCalcCustomSkilDegree, isAccurate );
}

Bool ActionNpcScript::HasSetSkillPos()
{
	ActionNpcSkill* ans = GetEntityAction( GetOwnerNpc() );

	return ans->HasSetSkillPos();
}

UInt32 ActionNpcScript::GetDistance( const EntityId id )
{	
	Entity4Zone* target = GetOwnerNpc()->GetSectorAction().GetEntityInMySector( id );

	// 없으면 엄청 먼걸로 한다.
	if ( target == NULL )
	{
		return 100000000;
	}

	AttributePosition* ownerAttrPos = GetEntityAttribute( GetOwnerNpc() ); // attrPos
	AttributePosition* targetAttrPos = GetEntityAttribute( target ); // attrPos

	Vector3 diff = ( ownerAttrPos->pos - targetAttrPos->pos );
	diff.z = 0;

	return static_cast<UInt32>( diff.Length() );
}

Bool ActionNpcScript::HasFollowee()
{
	if( GetOwnerNpc()->GetNpcAttr().followeeUnit == 0 )
	{
		return false;
	}

	return true;
}

Bool ActionNpcScript::HasPatrolPath()
{
	if(GetOwnerNpc()->GetNpcAttr().GetPatrolIndex() == 0 )
	{
		return false;
	}

	return true;
}

Bool ActionNpcScript::SetFollowee( const EntityId id )
{
	GetOwnerNpc()->GetNpcAttr().followeeUnit = id;

	return true;
}

Bool ActionNpcScript::IsRemovedEntity(const EntityId id)
{
	ActionSector* as = GetEntityAction( GetOwnerNpc() );
	Entity4Zone* target = as->GetEntityInMySector( id );

	if ( target && target->IsValid() )
	{
		return false;
	}

	return true;
}

Bool ActionNpcScript::SetEmployer( const EntityId id )
{
#ifdef __Renewal_Battle_IFF_by_kangms_180829
	auto actNpcCognition = GetOwnerNpc()->GetAction<ActionNpcCognition>();
	if (0 < id) {
		actNpcCognition->AttachFriendlyByContents(EntityRelationshipType::Friendly_Id, id);
	}
	else {
		EntityId prevId = GetOwnerNpc()->GetAttribute< AttributeNpc >()->employerUnitId;
		actNpcCognition->DetachFriendlyByContents(EntityRelationshipType::Friendly_Id, prevId);
	}

	GetOwnerNpc()->GetAttribute< AttributeNpc >()->employerUnitId = id;
#else//__Renewal_Battle_IFF_by_kangms_180829
	GetOwnerNpc()->GetNpcAttr().employerUnitId = id;
#endif//__Renewal_Battle_IFF_by_kangms_180829

	return true;
}


const EntityId& ActionNpcScript::GetEmployer()
{
	return GetOwnerNpc()->GetEmployerId();
}

Bool ActionNpcScript::IsStopped()
{
	ActionNpcMove* actNm = GetEntityAction( GetOwnerNpc() );

	return actNm->IsStopped();
}

// 현재 Npc의 HP
UInt32 ActionNpcScript::GetCurrentHP()
{
	AttributeNpc* an = GetEntityAttribute( GetOwnerNpc() );
	if( an == NULL )
	{
		return 0;
	}

	return an->health;
}

UInt32 ActionNpcScript::GetCurrentHPRate()
{
	EntityNpc* owner = GetOwnerNpc();
	VERIFY_RETURN(owner && owner->IsValid(), 1000);

	UInt64 currentHpRate = 100000;	
	
	UInt64 maxHP = owner->GetMaxHp();
	UInt64 curHP = owner->GetCurrentHp();
	if( maxHP < 1 )
	{
		maxHP = 1;
	}

	currentHpRate = curHP * 1000 / maxHP;

	return static_cast<UInt32>( currentHpRate );
}

UInt32 ActionNpcScript::GetDistanceEnemy()
{
	EntityNpc* owner = GetOwnerNpc();
	if( owner == NULL )
	{
		return 100000000;
	}

	ActionAggro* aggroAction = static_cast< ActionAggro* >(owner->GetAction(ACTION_AGGRO));
	if( nullptr == aggroAction )
	{
		return 100000000;
	}	

	EntityUnit* target = aggroAction->ExtractTargetEntityByAggroOrder(ActionAggro::ExtractTargetType::HIGHEST_AGGRO);
	if( nullptr == target )	// 목표가 없다.
	{
		return 100000000;
	}

	return GetDistance((target->GetId()));
}

// 현재 Npc의 HP를 특정 퍼센티지로 설정
void ActionNpcScript::SetCurrentHPRate( UInt32 hpRate )
{
	EntityNpc* owner = GetOwnerNpc();
	VERIFY_RETURN(owner && owner->IsValid(), );

	AbilityPackVector abilityPacks;
	AbilityPack pack;

	//ActionAbility* actAbility = GetEntityAction( GetOwnerNpc() );
	auto maxHP = static_cast<Int32>(owner->GetMaxHp());
	auto curHP = static_cast<Int32>(owner->GetCurrentHp());
	if( maxHP < 1 )
	{
		maxHP = 1;
	}

	auto targetHP = static_cast<Int32>(maxHP * (hpRate * 0.001));
	owner->AddCurrentHp(targetHP - curHP);
}

// 나의 sight 값
UInt32 ActionNpcScript::GetSight()
{
	const NpcInfoElem* script = SCRIPTS.GetNpcInfoScript( m_npcIndex );
	if ( script == NULL )	
	{
		return 0;
	}

	return script->sightRange;
}

void ActionNpcScript::RecoveryUpdate(Int32 recoveryRate)
{
	EntityNpc* owner = GetOwnerNpc();
	if( NULL == owner )
	{
		return;
	}

	owner->GetAction< ActionNpcAbility >()->RecoveryUpdate(recoveryRate);
}

void ActionNpcScript::RecoveryMPUpdate(Int32 recoveryRate)
{
	EntityNpc* owner = GetOwnerNpc();
	if (NULL == owner)
	{
		return;
	}

	owner->GetAction< ActionNpcAbility >()->RecoveryMPUpdate(recoveryRate);
}

Bool ActionNpcScript::SetMoveToTarget( UInt32 targetExtractRange, IndexNpc targetID, TargetType::Type targetType, UInt32 targetFilter )
{
	ActionNpcSkill* ans = GetEntityAction( GetOwnerNpc() );
	if( nullptr == ans )
	{
		return false;
	}
	
	EntityUnit* target = ans->ExtractTarget(targetExtractRange, targetID, targetType, targetFilter );
	if( nullptr == target )
	{
		return false;
	}
	
	if ( target->GetAction<ActionSkillControl>()->IsDetectedWithCaster(GetOwnerNpc()) == false )
	{
		return false;
	}

	ActionNpc* an = GetEntityAction( GetOwnerNpc() );
	an->SetMovetoTarget(target->GetId());

	return true;
}

Bool ActionNpcScript::TranStateMovetoTarget(EntityState eState )
{
	EntityNpc* npc = GetOwnerNpc();
	VERIFY_RETURN(npc && npc->IsValid(), false);	

	EntityUnit* movetoTargetUnit = npc->GetSectorAction().GetUnitInMySector(npc->GetMovetoTarget());
	VALID_RETURN(movetoTargetUnit && movetoTargetUnit->IsAlive(), false);

	Hsm& hsm = movetoTargetUnit->GetHsm();

	if ( hsm.GetState( eState ) == NULL  )
	{
		MU2_WARN_LOG( LogCategory::CONTENTS, "[%s] State %d not found", __FUNCTION__, eState );

		return false;
	}

	hsm.Tran( eState );	

	return true;
}

Bool ActionNpcScript::EraseMovetoTarget()
{
	EntityNpc* npc = GetOwnerNpc();
	VERIFY_RETURN(npc && npc->IsValid(), false);

	EntityUnit* movetoTarget = npc->GetSectorAction().GetUnitInMySector(npc->GetMovetoTarget());
	VALID_RETURN(movetoTarget && movetoTarget->IsAlive() && movetoTarget->IsNpc(), false);

	EntityNpc* target = static_cast<EntityNpc*>(movetoTarget);
	ActionNpc& actNpc = target->GetNpcAction();

	actNpc.SetIsDropItem(false);
	actNpc.SetIsGiveExp(false);

	Hsm& hsm = movetoTarget->GetHsm();

	if ( hsm.GetState( STATE_NPC_DEAD ) == NULL  )
	{
		MU2_WARN_LOG( LogCategory::CONTENTS, "[%s] State %d not found", __FUNCTION__, STATE_NPC_DEAD );

		return false;
	}

	hsm.Tran(EntityState::STATE_NPC_DEAD );

	return true;

}

// moveto 상태의 moveto position 지정
Bool ActionNpcScript::SetMoveToPosition( UInt32 movetoPositionType, Int32 startDegree, Int32 endDegree, UInt32 minDistance, UInt32 maxDistance )
{
	Vector3 standardPos;
	Vector3 myPos, targetPos;

	ActionSector* sectorAction = GetEntityAction(GetOwnerNpc());
	
	ViewPosition* vpMy = GetEntityView( GetOwnerNpc() );				
	vpMy->GetRealPosition( myPos );
	
		
	Vector3 newPos;
	UInt32 distance = mu2::GetRandBetween( minDistance, maxDistance);	
	Float degree = mu2::GetRandBetweenF( static_cast<float>( startDegree), static_cast<float>( endDegree));	
	Float raidanVAlue = mu2::DegreeToRadian(degree);

	if( MovetoPosionType(MP_MyPosition) == movetoPositionType )
	{
		standardPos = myPos;	

		newPos.x = standardPos.x + ( cos(raidanVAlue) * distance);
		newPos.y = standardPos.y + ( sin(raidanVAlue) * distance);
		newPos.z = standardPos.z;
	}
	else
	{
		ActionNpcSkill* ans = GetEntityAction( GetOwnerNpc() );
		const EntityId& targetUnitId = ans->GetTargetUnitId();
		EntityUnit* target = sectorAction->GetUnitInMySector( targetUnitId );
		ViewPosition* vpTarget = GetEntityView( target );			
		vpTarget->GetRealPosition( targetPos );

		standardPos = targetPos;

		mu2::Vector3 direction = myPos - targetPos;
		direction.Normalize2D();
		Float orgDirectionX = direction.x;

		direction.x = (orgDirectionX * cos(raidanVAlue)) - (direction.y * sin(raidanVAlue));
		direction.y = (direction.y * cos(raidanVAlue)) + (orgDirectionX * sin(raidanVAlue));

		newPos.x = standardPos.x + ( direction.x * distance);
		newPos.y = standardPos.y + ( direction.y * distance);
		newPos.z = standardPos.z;
	}	

	AttributeNpc *attr = GetEntityAttribute( GetOwnerNpc() );		

	if (!sectorAction->GetValidPosition(newPos ,attr->movetoRightNowPosition))
	{
		return false;
	}	

	ActionNpc* an = GetEntityAction( GetOwnerNpc() );
	an->SetMovetoTarget(GetOwnerNpc()->GetId());

	return true;
}


Bool ActionNpcScript::HasToBackward()
{		
	Vector3 myPos, targetPos;
	ActionSector* sectorAction = GetEntityAction(GetOwnerNpc());

	ViewPosition* vpMy = GetEntityView( GetOwnerNpc() );				

	ActionNpcSkill* ans = GetEntityAction( GetOwnerNpc() );
	const DefaultSkillElem* defaultSkillElem = ans->GetDefaultSkillElem();
	if ( nullptr == defaultSkillElem )
	{
		return false;
	}

	IndexSkill curSkillId = ans->GetCurrentSkillId();
	const SkillInfoElem* elem = SCRIPTS.GetSkillInfoScript( curSkillId );
	if ( elem == NULL )
	{
		return false;
	}

	const EntityId& targetUnitId = ans->GetTargetUnitId();
	EntityUnit* target = sectorAction->GetUnitInMySector( targetUnitId );
	if( nullptr == target )
	{
		return false;
	}
	ViewPosition* vpTarget = GetEntityView( target );				

	vpMy->GetRealPosition( myPos );
	vpTarget->GetRealPosition( targetPos );

	UInt32 randValue = GetRandBetween( 0, DEFAULT_RATE);
	if ( defaultSkillElem->precedenceValueC < randValue )
	{
		return false;
	}

	float distance = DistanceSquared2D( myPos, targetPos );
	float distanceRateAgainstSkillRange = (distance * DEFAULT_RATE) / ( elem->reMaxRange * elem->reMaxRange );
	if ( distanceRateAgainstSkillRange > defaultSkillElem->precedenceValueA )
	{
		return false;
	}

	return true;
}

Bool ActionNpcScript::IsTerminateLifeTime()
{
	ActionNpc* actNpc = GetOwnerNpc()->GetAction< ActionNpc >();
	if( nullptr == actNpc )
	{
		return false;
	}		

	return actNpc->IsTerminateLifeTime();
}


void ActionNpcScript::PlayScene( const char* id )
{
	VERIFY_RETURN(id && strlen(id), );
	VERIFY_RETURN(m_sector, );
	return m_sector->GetSectorController().GetControllerAction().PlayScene(id);
}

void ActionNpcScript::LocalPlayScene( const char* id, CharId playSceneCharId )
{
	VERIFY_RETURN(id  && strlen(id), );
	VERIFY_RETURN(m_sector, );

	EntityPlayer* playSceneEntity = m_sector->FindPlayerByCharId( playSceneCharId );
	VALID_RETURN(playSceneEntity, );

	std::wstring msg;
	StringUtil::Convert( id, msg );

	ENtfGamePlayScene* evt = NEW ENtfGamePlayScene;
	evt->id = msg;
	evt->stop = false;

	SERVER.SendToClient( playSceneEntity, EventPtr(evt) );
	
}

void ActionNpcScript::StopPlayScene( const char* id )
{
	VERIFY_RETURN(id && strlen(id), );
	VERIFY_RETURN(m_sector, );
	return m_sector->GetSectorController().GetControllerAction().StopPlayScene(id);
}

void ActionNpcScript::PlaySceneDuringHolding( const char* id, UInt32 seconds )
{
	VERIFY_RETURN(id && strlen(id), );
	VERIFY_RETURN(m_sector, );
	return m_sector->GetSectorController().GetControllerAction().PlaySceneDuringHolding(id, seconds);
}

// 내가 보고 있는 방향 각도
float ActionNpcScript::GetMyDegree()
{
	auto viewPos = GetOwnerNpc()->GetView<ViewPosition>();

	return viewPos->GetLookAtDir();
}

// 타겟과 나의 각도 
UInt32 ActionNpcScript::GetTargetDegree()
{
	ActionNpcSkill* ans = GetEntityAction( GetOwnerNpc() );

	return static_cast<UInt32>(ans->GetTargetDegree());
}

Bool ActionNpcScript::SetMaxDamageUser(IndexNpc indexNpc, UInt32 range)
{
	EntityNpc* owner = GetOwnerNpc();

	Vector3 position;
	owner->GetView< ViewPosition >()->GetRealPosition( position );

	auto as = owner->GetAction< ActionSector >();

	VectorUnit vecUnit;
	SectorCollidedHelper::CheckEntitiesWhoCollidedCircle( EntityTypes::NPC, as->GetSector(), position, range, vecUnit, 0 );
	if ( vecUnit.empty() )
	{
		return false;
	}

	if ( vecUnit.empty() )
	{
		return false;
	}	

	EntityUnit* target = nullptr;

	VectorUnit::const_iterator pos = vecUnit.begin();
	VectorUnit::const_iterator end = vecUnit.end();
	for (; pos != end; ++pos )
	{
		target = *pos;
		VALID_DO(target && target->IsValid() && target->IsNpc(), continue);

		//AttributeNpc *attr = GetEntityAttribute( target );
		//if( nullptr == attr )
		//{
		//	continue;
		//}

		EntityNpc* targetNpc = static_cast<EntityNpc*>(target);

		if(targetNpc->GetNpcIndex() == indexNpc )
		{
			break;
		}		
	}

	ActionNpcDamage* actMyDamage = GetEntityAction( GetOwnerNpc() );
	if( nullptr == actMyDamage )
	{
		return false;
	}

	ActionNpcDamage* actTargetDamage = GetEntityAction( target );
	if( nullptr == actTargetDamage )
	{
		return false;
	}

	const EntityId entityId = actTargetDamage->GetMaxDamageUser();
	if( 0 == entityId )
	{
		return false;
	}

	actMyDamage->SetMaxDamageUser(entityId);

	return true;
}

void ActionNpcScript::SetAttackAccumulationValue(Int32 value)
{
	ActionNpc* actNpc = GetEntityAction( GetOwnerNpc() );

	actNpc->SetAttackAccumulationValue(value);
}

UInt32 ActionNpcScript::GetAttackAccumulationValue()
{
	const AttributeNpc* an = GetEntityAttribute( GetOwnerNpc() );
	if( nullptr == an )
	{
		return 0;
	}

	return an->attackAccumulationValue;
}

Bool ActionNpcScript::IsSetEscape()
{
	ActionNpcSkill* ans = GetEntityAction( GetOwnerNpc() );
	if ( nullptr == ans )
	{
		return false;
	}

	const DefaultSkillElem* defaultSkillElem = ans->GetDefaultSkillElem();
	if (nullptr == defaultSkillElem)
	{
		return false;
	}

	return ((defaultSkillElem->precedenceType & PrecedenceType(PT_ALL)) || (defaultSkillElem->precedenceType & PrecedenceType(PT_ESCAPE)));
}

Bool ActionNpcScript::IsSetChangePosition()
{
	ActionNpcSkill* ans = GetEntityAction( GetOwnerNpc() );
	if ( nullptr == ans )
	{
		return false;
	}

	const DefaultSkillElem* defaultSkillElem = ans->GetDefaultSkillElem();
	if (nullptr == defaultSkillElem)
	{
		return false;
	}

	return ((defaultSkillElem->precedenceType & PrecedenceType(PT_ALL)) || (defaultSkillElem->precedenceType & PrecedenceType(PT_GUERRILLA)));
}

Bool ActionNpcScript::IsSetBackward()
{
	ActionNpcSkill* ans = GetEntityAction( GetOwnerNpc() );
	if ( nullptr == ans )
	{
		return false;
	}

	const DefaultSkillElem* defaultSkillElem = ans->GetDefaultSkillElem();
	if (nullptr == defaultSkillElem)
	{
		return false;
	}

	return ((defaultSkillElem->precedenceType & PrecedenceType(PT_ALL)) || (defaultSkillElem->precedenceType & PrecedenceType(PT_BACKWARD)));
}

Bool ActionNpcScript::IsEscaped()
{
	AttributeNpc* an = GetEntityAttribute( GetOwnerNpc() );
	if( nullptr == an )
	{
		return 0;
	}

	return an->isEscaped;
}

// backward 가 설정되어 있는가	
void ActionNpcScript::SetEscaped(Bool isEscaped)
{
	AttributeNpc* an = GetEntityAttribute( GetOwnerNpc() );
	if( nullptr == an )
	{
		return;
	}

	an->isEscaped = isEscaped;
}

void ActionNpcScript::SetWalkChase(Bool isWalkChase)
{
	//ActionNpc* actNpc = GetEntityAction( GetOwnerNpc() );
	GetOwnerNpc()->SetWalkChase(isWalkChase);
}

UInt32 ActionNpcScript::GetIntervalDamageCount() const 
{
	AttributeNpc* attrNpc = GetEntityAttribute( GetOwnerNpc() ); 

	if ( attrNpc->aiSpec[AttributeNpc::EAI_AVOID_DAMAGED_LOCATIOn] )
	{
		ActionNpcDamage* anm = GetEntityAction( GetOwnerNpc() );

		return anm->GetIntervalDamageCount();
	}
	// else

	return 0;
}

void ActionNpcScript::ResetIntervalDamageCount()
{
	ActionNpcDamage* anm = GetEntityAction( GetOwnerNpc() );

	return anm->ResetIntervalDamageCount();
}

Bool ActionNpcScript::IsProtectShield() const
{
	ActionBuff* actionNpc = GetEntityAction( GetOwnerNpc() );

	if ( actionNpc )
	{
		return actionNpc->IsProtectShield();
	}

	return false;
}
// 황금 고블린 드롭 젠
void ActionNpcScript::DropZenFromGoldenGlblin()
{
	if( nullptr == m_sector )
	{
		return;
	}
	
	VectorPlayer vecPlayer;
	m_sector->GetPlayers(vecPlayer );
	if( vecPlayer.empty() )
	{
		return;
	}
	
	theDropSystem.DropFromGoblin(GetOwnerNpc(), vecPlayer[0]);
}

Bool ActionNpcScript::IsMovingSkill()
{
	ActionNpcSkill* ans = GetEntityAction( GetOwnerNpc() );
	const SkillInfoElem* elem = ans->GetCurrentSkillElem();

	if ( nullptr == elem )
	{
		return false;
	}

	if ( elem->movingSkillSpeed > 0 )
	{
		return true;
	}

	return false;
}

void ActionNpcScript::AddAggroNearPlayers( UInt32 point, UInt32 range )
{
	ActionNpcAggro* aggro = GetEntityAction( GetOwnerNpc() ); 

	if ( aggro )
	{
		aggro->AddAggroNearPlayers( point, range );
	}
}

void ActionNpcScript::RequestInvasion(UInt32 spawnVolumeIndex, UInt32 invaderMissionId, UInt32 defenderMissionId, UInt32 missionDuration )
{
	//// 섹터 정보에 이미 설정으로 되어 있으면 미션을 발동 시키지 않는다.
	//if( m_sector->GetInvasionInfo().worldId != 0 )
	//{
	//	//	이미 사용하고 있으면 침공 미션 절차를 진행하지 않는다.
	//	//	기획팀과 에러처리 관련 이야기를 해봐야 할듯.
	//	return;
	//}

	//// 현재 execution 정보로 세팅을 우선 한다.
	//m_sector->GetInvasionInfo().worldId = static_cast<Byte>(SERVER.GetWorldId());
	//m_sector->GetInvasionInfo().execZoneId = m_sector->GetId();
	//m_sector->GetInvasionInfo().spawnVolumeIndex = spawnVolumeIndex;

	////	침공 포털 인덱스의 세팅은 알수 있기 때문에 미리 세팅해 놓는다.
	//VolumeSpawnScript* volumeScript = GetZoneScript<VolumeSpawnScript>( m_sector->GetId().GetZoneIndex(), ZoneLevelScriptTable::VOLUME_SPAWN_SCRIPT);
	//MU2_ASSERT( volumeScript );	
	//const VolumeSpawnScript::Element* volumeElem = volumeScript->Get( spawnVolumeIndex );
	//MU2_ASSERT( volumeElem );
	//const NpcInfoElem* monsterElem = SCRIPTS.GetNpcInfoScript( volumeElem->npcRegen[0].npcId );
	//MU2_ASSERT( monsterElem );	
	//m_sector->GetInvasionInfo().portalIndex = monsterElem->npcPortal;

	//EzbReqStartInvasion* req = NEW EzbReqStartInvasion;

	//req->requestorExecZoneId = m_sector->GetId();
	//req->missionDuration = missionDuration;
	//req->invaderMissionId = invaderMissionId;
	//req->defenderMissionId = defenderMissionId;

	//SERVER.SendToBridge( EventPtr( req ) );
}

UInt32 ActionNpcScript::GetTeamMemberCount(TeamType::Enum teamType, UInt32 volumeIndex)
{
	VolumeScript* vscript = GetZoneScript<VolumeScript>(m_sector->GetIndexZone());
	VALID_RETURN(vscript && vscript->GetMap().size(), 0);

	auto it = vscript->GetMap().find(volumeIndex);
	VALID_RETURN(it != vscript->GetMap().end(), 0);

	const VolumeElem& elem = it->second;
	VectorUnit vecUnit;
	SectorCollidedHelper::CheckEntitiesWhoCollidedCircle(EntityTypes::ALL, m_sector, elem.pos, static_cast<UInt32>(elem.radius), vecUnit, 0);
	
	UInt32 count = 0;

	for (EntityUnit* pUnit : vecUnit )
	{	 
		VALID_DO(pUnit && pUnit->IsValid() && pUnit->IsPlayer() && !pUnit->IsDead(), continue);

		EntityPlayer* player = static_cast<EntityPlayer*>(pUnit);
		
		if (player->GetMyTeam() == teamType)
		{
			count++;
		}
	}

	return count;
}

void ActionNpcScript::SetNpcSpeed(UInt32 walkSpeed, UInt32 runSpeed)
{
	ActionNpcAbility* actAbility = GetEntityAction(GetOwnerNpc());
	actAbility->SetNpcSpeed(walkSpeed, runSpeed);
}

void ActionNpcScript::SetNpcDefaultMoveState(EntityState state)
{
	//ActionNpc* actNpc = GetEntityAction(GetOwnerNpc());
	GetOwnerNpc()->SetDefaultMoveState(static_cast<MoveState>(state));
}

void ActionNpcScript::SetMoveToRightPos( Float x, Float y, Float z )
{
	EntityNpc* npc = GetOwnerNpc();
	VERIFY_RETURN(npc && npc->IsValid(), );

	Vector3 newPos(x,y,z);
	npc->GetSectorAction().GetValidPosition( newPos, npc->GetNpcAttr().movetoRightNowPosition );
}

void ActionNpcScript::SetTeamNo(TeamType::Enum teamNo)
{
	EntityNpc* npc = GetOwnerNpc();
	VERIFY_RETURN(npc && npc->IsValid(), );

	npc->SetMyTeam(teamNo);
}

Bool ActionNpcScript::CheckSkillRangeCollisionAndSetMoveToPos(const float range /*= 0.f*/)
{
	ActionNpcSkill* ans = GetEntityAction(GetOwnerNpc());

	return ans->CheckSkillRangeCollisionAndSetMoveToPos( range );
}

UInt32 ActionNpcScript::GetCurrentNpcId()
{
	return GetOwnerNpc()->GetNpcIndex();
}


void ActionNpcScript::OnStateEnter(EntityState stateId )
{
	callStateFunction( stateId, "enter" );	
}

void ActionNpcScript::OnStateDecide(EntityState stateId )
{
	callStateFunction( stateId, "decide" );	
}

void ActionNpcScript::OnStateExit(EntityState stateId )
{
	callStateFunction( stateId, "exit" );	
}

Bool ActionNpcScript::callStateFunction(EntityState stateId, const char* func )
{
	
	MU2_ASSERT( stateId > Hsm::STATE_ROOT );

	if ( func == nullptr )
	{
		MU2_ASSERT( func );
		return false;
	}

	// 아이디로 설정된 테이블에서 함수를 찾지 못하면 실패
	// 스크립트에서 테이블 복사를 통해 상속 구현
#ifdef _WIN64
	#define LJ_EXCODE		((DWORD)0xe24c4a00)
	#define LJ_EXCODE_CHECK(cl)	(((cl) ^ LJ_EXCODE) <= 0xff)

	//! filter가 적용되어야 하며 아래와 같이 불로 할 경우 0, 1로 값이 정해지게 된다.
	//! 올바른 처리가 힘들다.
	__try 
	{
		if ( m_controlById.length() > 0 )
		{
			return callStateFunctionWith( m_controlById, stateId, func );	
		}		
	}
	__except ( EXCEPTION_EXECUTE_HANDLER )
	{

		UInt32 except = GetExceptionCode();
		if ( LJ_EXCODE_CHECK(except) )
		{	
			MU2_ERROR_LOG( LogCategory::CONTENTS, "[E] LuaException: code=0x%x", except );			
		}
		else
		{
			MU2_ERROR_LOG( LogCategory::CONTENTS, "[E] Exception without LuaException: code=0x%x", except );
		}
	}
#else // _WIN32
	try 
	{
		if ( m_controlById.length() > 0 )
		{
			return callStateFunctionWith( m_controlById, stateId, func );	
		}		
	}
	catch ( LuaException& e )
	{
		MU2_WARN_LOG( LogCategory::CONTENTS, "[E] LuaException: msg=%s", e.ErrorMsg.c_str() );
	}
#endif // _WIN64
	return false;
}

Bool ActionNpcScript::callStateFunctionWith( const std::string& control, EntityState stateId, const char* func )
{
	bool rc = false;

	if ( func == nullptr )
	{
		MU2_ASSERT( false );
		return false;
	}

	lua_getglobal( m_L, control.c_str() );

	if ( lua_istable(m_L, -1) )
	{
		// get state table
		lua_pushnumber(m_L, stateId);
		lua_gettable(m_L, -2);
	
		if ( lua_istable(m_L, -1) )
		{
			lua_pushcclosure(m_L, lua_util::on_error, 0);
			int errfunc = lua_gettop(m_L);

			// get function
			lua_pushstring(m_L, func);
			lua_gettable(m_L, -3); // func / on_error / state

			if ( lua_isfunction(m_L, -1) )
			{
				pushNpc(); // 글로벌에 등록된 NPC를 가져옴
				lua_pcall(m_L, 1, 0, errfunc);

				rc = true;
			}
			else
			{
				lua_pop(m_L, 1); // 함수 제거
			}

			lua_remove(m_L, errfunc);
		}
		
		lua_pop(m_L, 1); // pop state table
	}

	lua_pop(m_L, 1); // pop npc table	

	return rc;
}

Bool ActionNpcScript::registerNpc()
{
	char name[128];

	sprintf_s( name, 128, "npc_inst_%d", GetId() );

	m_npcLuaName = name;

	lua_util::set( m_L, m_npcLuaName.c_str(), this );

	return true;
}
	
Bool ActionNpcScript::pushNpc()
{
	MU2_ASSERT( m_npcLuaName.size() > 0 );

	lua_getglobal(m_L, m_npcLuaName.c_str() );

	MU2_ASSERT( !lua_isnil(m_L, -1) );

	return !lua_isnil(m_L, -1);
}

void ActionNpcScript::unregisterNpc()
{
	// 특수한 npc들 때문에 스크립트가 없을 수도 있다.
	if ( !m_npcLuaName.empty() )
	{
		// XXX: 안전한가? 체크한 후 주석 제거		
		lua_pushnil( m_L );
		lua_setglobal( m_L, m_npcLuaName.c_str() );
		// 메모리 증가 테스트 위해 추가. 필요할 때만 사용
		// lua_gc( m_L, LUA_GCCOLLECT, 0 );
	}
}

Bool ActionNpcScript::createControl()
{
	return createControlById();
}

Bool ActionNpcScript::createControlById()
{
	MU2_ASSERT( m_L );
	MU2_ASSERT( m_sector );

	char id[128];

	sprintf_s( id, 128, "npc_%d", m_aiIndex );

	lua_getglobal(m_L, id);

	if ( lua_istable(m_L, -1) )
	{
		m_controlById = id;

		lua_pop(m_L, 1);

		return true;
	}

	MU2_WARN_LOG( LogCategory::CONTENTS, "[E] Cannot find table %s", id );

	lua_pop(m_L, 1);

	return false;
}

Bool ActionNpcScript::createHsm()
{
	if ( createHsmById() )
	{
		return true;
	}

	return createHsmByType();
}

Bool ActionNpcScript::createHsmById()
{
	char id[128];

	sprintf_s( id, 128, "hsm_id_%d", m_aiIndex );

	lua_getglobal(m_L, id);
	
	if ( lua_isfunction(m_L, -1) )
	{
		lua_pop(m_L, 1); // pop 하고 다시 호출 

		// 먼저 기존 상태들 클리어
		GetOwnerNpc()->GetHsm().Fini();

		lua_util::call(m_L, id, this );

		lua_gc(m_L, LUA_GCCOLLECT, 0);
		
		return true;
	}
	// else
	lua_pop(m_L, 1); // pop function 

	return false;
}

Bool ActionNpcScript::createHsmByType()
{
	MU2_ASSERT( m_L );
	MU2_ASSERT( m_sector );

	const NpcInfoElem* script = SCRIPTS.GetNpcInfoScript( m_npcIndex );

	if ( script == NULL )	
	{
		return false ;
	}

	if (1 == script->type)
	{
		AddState( static_cast<EntityState>(Hsm::STATE_ROOT), STATE_NPC_LIFE );
		AddState( STATE_NPC_LIFE, STATE_NPC_SPAWN );
		AddState( STATE_NPC_LIFE, STATE_NPC_DEAD );

		AddState( static_cast<EntityState>(Hsm::STATE_ROOT), STATE_NPC_ALIVE );
		AddState( STATE_NPC_ALIVE, STATE_NPC_ONCE );
		AddState( STATE_NPC_ALIVE, STATE_NPC_PROWL );
		AddState( STATE_NPC_ALIVE, STATE_NPC_PEACE );
			AddState( STATE_NPC_PEACE, STATE_NPC_IDLE );
			AddState( STATE_NPC_PEACE, STATE_NPC_FREEWALK );
			AddState( STATE_NPC_PEACE, STATE_NPC_PATROL );
			AddState( STATE_NPC_PEACE, STATE_NPC_FOLLOW );
			AddState( STATE_NPC_PEACE, STATE_NPC_MOVETO_EVENTUALLY );
		AddState( STATE_NPC_ALIVE, STATE_NPC_ENGAGE );
			AddState( STATE_NPC_ENGAGE, STATE_NPC_THINK );
			AddState( STATE_NPC_ENGAGE, STATE_NPC_CHASE );
			AddState( STATE_NPC_ENGAGE, STATE_NPC_ESCAPE );
			AddState( STATE_NPC_ENGAGE, STATE_NPC_SKILL_FIRE );
				AddState( STATE_NPC_SKILL_FIRE, STATE_NPC_MOVE_WHILE_SKILL_FIRE );
			AddState( STATE_NPC_ENGAGE, STATE_NPC_MOVETO_RIGHT_NOW );
			AddState( STATE_NPC_ENGAGE, STATE_BOUND );
				AddState( STATE_BOUND, STATE_BOUND_FLEE );
				AddState( STATE_BOUND, STATE_BOUND_PANIC );
				AddState( STATE_BOUND, STATE_BOUND_PANIC_CHASE );
		AddState( STATE_NPC_ALIVE, STATE_NPC_MOVE );
		AddState( STATE_NPC_MOVE, STATE_NPC_RETURN );
		AddState( STATE_NPC_ALIVE, STATE_NPC_PLAY_ANI );
		AddState( STATE_NPC_ALIVE, STATE_NPC_WAIT );

		return true;
	}
	else
	{
		char type[128];

		sprintf_s( type, 128, "hsm_type_%d", script->type );

		lua_getglobal( m_L, type );

		if (lua_isfunction( m_L, -1 ))
		{
			lua_pop( m_L, 1 ); // pop 하고 다시 호출 

							   // 먼저 기존 상태들 클리어. 
			GetOwnerNpc()->GetHsm().Fini();

			lua_util::call( m_L, type, this );

		lua_gc(m_L, LUA_GCCOLLECT, 0);

			return true;
		}
		// else
		lua_pop( m_L, 1 ); // pop function 
	}	

	return false;
}

void ActionNpcScript::clear()
{
	m_sector = nullptr;
	m_L = nullptr;
	m_controlById.clear();
	m_controlByType.clear();
	m_controlDefault.clear();
	m_npcLuaName.clear();
}

Bool ActionNpcScript::checkDefaultSkillCondition(UInt32 conditionType, UInt32 conditionValue)
{
	ActionNpc* actNpc = GetOwnerNpc()->GetAction< ActionNpc >();

	if (DefaultSkillFireConditionType(RANDOM) == conditionType)
	{
		conditionType = mu2::GetRandBetween(1, NUMBER_OF_TYPE);
	}

	switch (conditionType)
	{
	case DefaultSkillFireConditionType(ALWYAS):
		return true;
	case DefaultSkillFireConditionType(MY_HP_LE):
	{
		if (GetCurrentHPRate() < conditionValue)
			return true;
	}
		break;
	case DefaultSkillFireConditionType(MY_HP_GT):
	{
		if (GetCurrentHPRate() > conditionValue)
			return true;
	}
		break;
	case DefaultSkillFireConditionType(TARGET_DISTANCE_LE):
	{
		if (GetDistanceEnemy() < conditionValue)
			return true;
	}
		break;
	case DefaultSkillFireConditionType(TARGET_DISTANCE_GT):
	{
		if (GetDistanceEnemy() > conditionValue)
			return true;
	}
		break;
	case DefaultSkillFireConditionType(ACCUMULATION_VALUE_GT):
	{
		if (GetAttackAccumulationValue() > conditionValue)
		{
			actNpc->SetAttackAccumulationValue(0);
			return true;
		}
	}
		break;
	default:
		break;
	}

	return false;
}

void ActionNpcScript::checkTargetExitState( EntityNpc* target, const BuffInfoPtr& buffInfo, UInt32& exitState )
{
	switch ( buffInfo->effectElem->subKey )
	{
	case BoundStateEffectType::FEAR:
		exitState = STATE_BOUND_FLEE;
		break;
	case BoundStateEffectType::BLAZE:
		exitState = STATE_BOUND_BLAZE;
		break;
	case BoundStateEffectType::TRANSFORM:
		exitState = STATE_BOUND_TRANSFORM;
		break;
	case BoundStateEffectType::TRANSFORM_2:
		exitState = STATE_BOUND_TRANSFORM2;
		break;
	case BoundStateEffectType::PANIC:
		exitState = STATE_BOUND_PANIC;
		break;
	case BoundStateEffectType::BOUNCE:
		exitState = STATE_BOUND_BOUNCE;
		break;
	case BoundStateEffectType::FREEZE:
		exitState = STATE_BOUND_FREEZE;
		break;
	case BoundStateEffectType::STONE:
		exitState = STATE_BOUND_STONE;
		break;
	case BoundStateEffectType::GREENSTONE:
		exitState = STATE_BOUND_GREENSTONE;
		break;
	case BoundStateEffectType::DANCING:
		exitState = STATE_BOUND_DANCING;
		break;
	case BoundStateEffectType::STUN:
		exitState = STATE_BOUND_STUN;
		break;
	case BoundStateEffectType::SNARE:
		exitState = STATE_BOUND_SNARE;
		break;
	case BoundStateEffectType::SILENCE:
		exitState = STATE_BOUND_SILENCE;
		break;
	case BoundStateEffectType::BLIND:
		exitState = STATE_BOUND_BLIND;
		break;
	case BoundStateEffectType::SLOW:
		{
			AbilityParam abilityParam;
			abilityParam.type = EAT::RUN_SPEED;
			abilityParam.value = buffInfo->effectElem->value[0];
			abilityParam.section = buffInfo->effectElem->section;
			abilityParam.isOnOff = (buffInfo->effectElem->sectionBuff == EffectBuffType::DEBUFF) ? true : false;

			target->GetAction<ActionAbility>()->ChangedSkillAbility(abilityParam);
			exitState = BoundStateEffectType::SLOW;
		}
		break;
	case BoundStateEffectType::TEMPTATION:
		exitState = STATE_BOUND_TEMPTATION;
		break;
	case BoundStateEffectType::OPPRESSION:
		exitState = STATE_BOUND_OPPRESSION;
		break;
	case BoundStateEffectType::HALT:
		exitState = STATE_BOUND_HALT;
		break;
	case BoundStateEffectType::HALLUCINATION:
		exitState = STATE_BOUND_HALLUCINATION;
		break;
	}
}

Bool ActionNpcScript::isCleanable( const BuffInfoPtr& buffInfo )
{
	switch (buffInfo->effectElem->keyOperator)
	{
		case EffectOperatorType::KNOCK_BACK:
		case EffectOperatorType::PULLING:
		case EffectOperatorType::THROW_TARGET:
		return false;
	}

	if ( buffInfo->IsEqualKeyOperator(EffectOperatorType::BOUND_STATE_BUFF, BoundStateEffectType::AIRBORNE))
	{
		return false;
	}

	return true;
}

Bool ActionNpcScript::CheckCurrentMasterHpPercentLessThan( UInt32 checkPercent )
{
	ActionNpc* actNpc = GetOwnerNpc()->GetAction< ActionNpc >();
	if ( nullptr == actNpc )
	{
		return false;
	}

	EntityUnit* master = actNpc->GetMasterUnit();
	if ( nullptr == master )
	{
		return false;
	}

	auto actAbility = master->GetAction<ActionAbility>();
	if ( nullptr == actAbility )
	{
		return false;
	}

	if ( actAbility->GetPercentHp() > (checkPercent * 100) )
	{
		return false;
	}

	return true;
}

Bool ActionNpcScript::CheckMasterBoundState()
{
	ActionNpc* actNpc = GetOwnerNpc()->GetAction< ActionNpc >();
	if ( nullptr == actNpc )
	{
		return false;
	}

	EntityUnit* master = actNpc->GetMasterUnit();
	VALID_RETURN(master && master->IsPlayer(), false);

	auto actBuff = master->GetAction<ActionBuff>();
	if ( nullptr == actBuff )
	{
		return false;
	}

	auto found = actBuff->FindBuffInfo([&](const BuffInfoPtr buff)
	{
		return EffectOperatorType::BOUND_STATE_BUFF == buff->effectElem->keyOperator;
	});

	if (nullptr == found)
	{
		return false;
	}
	
	return true;
}

void ActionNpcScript::SetTargetingMaster()
{
	auto actNpcSkill = GetOwnerNpc()->GetAction<ActionNpcSkill>();
	if ( nullptr == actNpcSkill )
	{
		return;
	}

	auto actNpc = GetOwnerNpc()->GetAction<ActionNpc>();
	if ( nullptr == actNpc )
	{
		return;
	}

	EntityUnit* pUnitMaster = actNpc->GetMasterUnit();
	VALID_RETURN(pUnitMaster, );
	
	actNpcSkill->SetTargetingMaster(pUnitMaster->GetId() );
}

Bool ActionNpcScript::HasSkillTarget()
{
	auto actNpcSkill = GetOwnerNpc()->GetAction<ActionNpcSkill>();
	if ( nullptr == actNpcSkill )
	{
		return false;
	}

	return actNpcSkill->HasSkillTarget();
}
Bool ActionNpcScript::IsDarklordColosseumNpc()
{
	ActionNpcColosseum* npcColosseumAction = GetOwnerNpc()->GetAction<ActionNpcColosseum>();
	if (npcColosseumAction == nullptr)
		return false;

	if (npcColosseumAction->GetNpcClass() != ClassType::DARKLORD)
		return false;

	return true;

}
Bool ActionNpcScript::IsBladerColosseumNpc()
{
	ActionNpcColosseum* npcColosseumAction = GetOwnerNpc()->GetAction<ActionNpcColosseum>();
	if (npcColosseumAction == nullptr)
		return false;

	if (npcColosseumAction->GetNpcClass() != ClassType::BLADER)
		return false;

	return true;
}
Bool ActionNpcScript::IsWhispererColosseumNpc()
{
	ActionNpcColosseum* npcColosseumAction = GetOwnerNpc()->GetAction<ActionNpcColosseum>();
	if (npcColosseumAction == nullptr)
		return false;

	if (npcColosseumAction->GetNpcClass() != ClassType::WHISPERER)
		return false;

	return true;
}
Bool ActionNpcScript::IsWarmageColosseumNpc()
{
	ActionNpcColosseum* npcColosseumAction = GetOwnerNpc()->GetAction<ActionNpcColosseum>();
	if (npcColosseumAction == nullptr)
		return false;

	if (npcColosseumAction->GetNpcClass() != ClassType::WARMAGE)
		return false;

	return true;
}

Bool ActionNpcScript::IsBlackPhantomColosseumNpc()
{
	ActionNpcColosseum* npcColosseumAction = GetOwnerNpc()->GetAction<ActionNpcColosseum>();
	if (npcColosseumAction == nullptr)
		return false;

	if (npcColosseumAction->GetNpcClass() != ClassType::BLACKPHANTOM)
		return false;

	return true;
}

Bool ActionNpcScript::IsEmphasizerColosseumNpc()
{
	ActionNpcColosseum* npcColosseumAction = GetOwnerNpc()->GetAction<ActionNpcColosseum>();
	if (npcColosseumAction == nullptr)
		return false;

	if (npcColosseumAction->GetNpcClass() != ClassType::EMPHASIZER)
		return false;

	return true;
}


Bool ActionNpcScript::ColosseumUseSkill(IndexSkill skillId)
{
	ActionNpcColosseum* npcColosseumAction = GetOwnerNpc()->GetAction<ActionNpcColosseum>();
	if (npcColosseumAction == nullptr)
		return false;

	if (npcColosseumAction->UseSkill(skillId) == false)
		return false;

	return true;
}

Bool ActionNpcScript::IsShortDistanceColosseumAI()
{
	ActionNpcColosseum* npcColosseumAction = GetOwnerNpc()->GetAction<ActionNpcColosseum>();
	if (npcColosseumAction == nullptr)
		return false;

	if (npcColosseumAction->IsShortDistanceAI() == false)
		return false;

	return true;
}
IndexSkill ActionNpcScript::GetRandomChargeSkill()
{
	ActionNpcColosseum* npcColosseumAction = GetOwnerNpc()->GetAction<ActionNpcColosseum>();
	if (npcColosseumAction == nullptr)
		return 0;

	IndexSkill id = npcColosseumAction->GetRandomChargeSkill();
	return id;
		
}

Bool ActionNpcScript::IsCurrentChargeSkill()
{
	ActionNpcSkill* ans = GetEntityAction(GetOwnerNpc());

	auto elem = ans->GetCurrentSkillElem();
	if (elem)
	{
		if (elem->CanChargingSkill() == true)
			return true;
	}

	return false;
}

Bool ActionNpcScript::IsColosseumBattleState()
{
	ActionSector* actionSector = GetEntityAction(GetOwnerNpc());
	VALID_RETURN(actionSector, false);

	auto ActionSectorController = actionSector->GetActionSectorController();
	VALID_RETURN(ActionSectorController, false);

	SectorProcessor* sectorProcessor = ActionSectorController->GetSectorProcess();
	VALID_RETURN(sectorProcessor, false);

	return sectorProcessor->IsBattleState();
}

void ActionNpcScript::SkillCancel()
{
	ActionNpcSkill* ans = GetEntityAction(GetOwnerNpc());
	if (ans)
	{
		ans->CancelSkill();
	}
}

Bool ActionNpcScript::IsArriveToRemainingDistance( Float placeDistance, Float fasterDistance )
{
	ActionNpcMove* anm = GetEntityAction( GetOwnerNpc() );
	return anm->IsArriveToRemainingDistance( placeDistance, fasterDistance );
}

void ActionNpcScript::CheckChangeStateInChaseState()
{
	ActionNpcMove* anm = GetEntityAction( GetOwnerNpc() );

	if ( anm->IsHalt() )
	{
		Tran(EntityState::STATE_NPC_THINK );
	}
	else if ( anm->HasChasedTooFar() )
	{
		Tran(EntityState::STATE_NPC_RETURN );
	}
}

Bool ActionNpcScript::CheckSummonerExchangeWeapon( WeaponType::Type type )
{
	EntityNpc* ownerNpc = GetOwnerNpc();
	VERIFY_RETURN(ownerNpc && ownerNpc->IsValid(), false);

	EntityUnit* summoner = m_sector->FindUnit(ownerNpc->GetNpcAttr().employerUnitId );
	VALID_RETURN(summoner && summoner->IsPlayer(), false);

	EntityPlayer* summonerPlayer = static_cast<EntityPlayer*>(summoner);

	return summonerPlayer->GetInventoryAction().CheckSummonerEquipedWeaponType( type );
}

Bool ActionNpcScript::SetCompelHasAggroList( UInt32 hasAggroList )
{
	ActionNpcAggro* ana = GetEntityAction( GetOwnerNpc() );
	if ( ana )
	{
		if ( hasAggroList > 0 )
		{
			ana->SetCompelHasAggroList( true );
		}
		else
		{
			ana->SetCompelHasAggroList( false );
		}
		
		return true;
	}

	return false;
}

UInt32 ActionNpcScript::GetPlayerCountAtPosition( Float x, Float y, Float z, UInt32 radius )
{
	// EntityTypes::NONE
	mu2::Vector3 position( x, y, z );

	VectorUnit candidates;
	SectorCollidedHelper::CheckEntitiesWhoCollidedCircle( EntityTypes::PLAYER_ZONE, m_sector, position, radius, candidates, 0 );

	return static_cast<UInt32>( candidates.size() );
}

void ActionNpcScript::SetSkillSpecificPos( Float x, Float y, Float z )
{
	// EntityTypes::NONE
	mu2::Vector3 position( x, y, z );

	ActionNpcSkill* ans = GetEntityAction( GetOwnerNpc() );

	if ( ans )
	{
		ans->SetSkillTargetPosList( position );
	}
}

void ActionNpcScript::SetSkillPosToReturnPos()
{
	ActionNpcSkill* ans = GetEntityAction( GetOwnerNpc() );
	AttributeNpc* an = GetEntityAttribute( GetOwnerNpc() );

	mu2::Vector3 returnPos = an->battleStartPosition;

	if ( returnPos.IsZero() )
	{
		returnPos = an->GetReturnPosition();
	}

	ans->SetSkillTargetPosList( returnPos );
}


void ActionNpcScript::AddImmune( UInt32 immuneType )
{
	EntityNpc* owner = GetOwnerNpc();
	MU2_ASSERT( owner );

	ImmuneType::Enum enumImmuneType = static_cast< ImmuneType::Enum >( immuneType );

	auto actSkillControl = owner->GetAction< ActionSkillControl >();
	if ( nullptr != actSkillControl )
	{
		actSkillControl->AddImmune( enumImmuneType );
	}
}

void ActionNpcScript::DelImmune( UInt32 immuneType )
{
	EntityNpc* owner = GetOwnerNpc();
	MU2_ASSERT( owner );

	ImmuneType::Enum enumImmuneType = static_cast< ImmuneType::Enum >( immuneType );

	auto actSkillControl = owner->GetAction< ActionSkillControl >();
	if ( nullptr != actSkillControl )
	{
		if ( false == actSkillControl->DelImmune( enumImmuneType ) )
		{
			MU2_WARN_LOG( LogCategory::CONTENTS, "[%s]{ fail to DelImmune ]{ npcId : %d ][ immuneType : %d ]", __FUNCTION__, m_npcIndex, immuneType );
		}
	}
}

void ActionNpcScript::SummonServantSpawnVolumeIndex( UInt32 spawnVolumeIndex, IndexNpc npcIndex, UInt32 count, Bool isStraightMove )
{
	IndexZone indexZone = ( m_sector->GetIndexZone() );

	VolumeSpawnScript* vscript = GetZoneScript<VolumeSpawnScript>(indexZone);
	if ( vscript == nullptr )
	{
		return;
	}

	const VolumeSpawnElem* elem = vscript->Get( spawnVolumeIndex );
	if ( elem == nullptr )
	{
		return;
	}

	
	const NpcInfoElem* script = SCRIPTS.GetNpcInfoScript( npcIndex );
	if ( !script )
	{
		return;
	}
	
	Vector3 pos( elem->x, elem->y, elem->z );
	for ( UInt32 n = 0; n < count; n++ )
	{
		if ( false == theSpawnSystem.GetRandomPostion( pos, 0, elem->radius, m_sector ) )
		{
			continue;
		}

		EntityNpc* npc = NULL;

		SpawnSystem::BaseSpawnInfo baseSpawnInfo;
		baseSpawnInfo.npcIndex = npcIndex;
		baseSpawnInfo.sector = m_sector;
		baseSpawnInfo.spawnPos = pos;
		baseSpawnInfo.dir = elem->dir;
		baseSpawnInfo.aiIndex = script->aiIndex;
		baseSpawnInfo.pursuitRange = elem->npcRegen[0].pursuitRange;		
		baseSpawnInfo.masterUnit = GetOwnerNpc();
		baseSpawnInfo.lifeTime = elem->npcRegen[0].lifetime;
		baseSpawnInfo.isStraightMove = isStraightMove;

		if ( 0 < elem->npcRegen[0].level )
		{
			baseSpawnInfo.level = elem->npcRegen[0].level;
		}

		if ( false == theSpawnSystem.SpawnGeneralNpc( &baseSpawnInfo, &npc ) )
		{
			continue;
		}		
	}
}

void ActionNpcScript::SummonServant(IndexNpc npcIndex, UInt32 count, Float radius, Bool isStraightMove )
{
	EntityNpc* npc = GetOwnerNpc();
	VERIFY_RETURN(npc && npc->IsValid(), );

	Vector3 pos;
	Bool ret = npc->GetPositionView().GetRealPosition( pos );
	MU2_ASSERT( ret );

	Sector* sector = npc->GetSector();
	VERIFY_RETURN( sector, );

	Float lookatDegree = npc->GetPositionView().GetRealLookAtDegree();
	Float startPositionDegree = 180.f / static_cast<Float>( count );
	if ( count == 1 )
	{
		startPositionDegree = 0;
	}
	Float addDegree = 360.f / static_cast<Float>( count );

	for ( UInt32 n = 0; n < count; n++ )
	{
		Vector3 summonPos = pos;
		summonPos.x += cos( DegreeToRadian( lookatDegree + startPositionDegree + addDegree*n ) ) * radius;
		summonPos.y += sin( DegreeToRadian( lookatDegree + startPositionDegree + addDegree*n ) ) * radius;

		
		const NpcInfoElem* script = SCRIPTS.GetNpcInfoScript( npcIndex );
		if ( !script )
		{
			return;
		}

		
		SpawnSystem::BaseSpawnInfo baseSpawnInfo;
		baseSpawnInfo.npcIndex = npcIndex;
		baseSpawnInfo.level = npc->GetLevel();
		baseSpawnInfo.sector = sector;
		baseSpawnInfo.spawnPos = summonPos;
		baseSpawnInfo.aiIndex = script->aiIndex;
		baseSpawnInfo.dir = static_cast<Float>( lookatDegree );
		baseSpawnInfo.pursuitRange = npc->GetPursuitRange();
		baseSpawnInfo.aiIndex = script->aiIndex;
		baseSpawnInfo.masterUnit = npc;
		baseSpawnInfo.isStraightMove = isStraightMove;

		EntityNpc* npcTemp = NULL;
		if ( false == theSpawnSystem.SpawnGeneralNpc( &baseSpawnInfo, &npcTemp) )
		{
			continue;
		}
	}
}


void ActionNpcScript::KillMyServant(IndexNpc npcIndex /*= 0*/)
{
	ActionSkillControl* ans = GetOwnerNpc()->GetAction< ActionSkillControl >();
	if ( ans == nullptr )
	{
		return ;
	}

	(0 == npcIndex) ? ans->RemoveAllServantNpc() : ans->RemoveServantNpc(npcIndex);
}

void ActionNpcScript::RemoveEffectVolume()
{
	ActionSkillControl* ans = GetOwnerNpc()->GetAction< ActionSkillControl >();
	if ( ans == nullptr )
	{
		return;
	}

	ans->RemoveAllEffectVolume();
}

void ActionNpcScript::CleanAllBoundState()
{
	EntityNpc* owner = GetOwnerNpc();

	auto actBuff = owner->GetAction<ActionBuff>();
	if (nullptr == actBuff)
	{
		return;
	}

	UInt32 exitState = 0;
	BuffInfoPtrList debuffList;
	actBuff->FillUpDebuff(debuffList);

	for (auto buffInfo : debuffList)
	{
		if (false == isCleanable(buffInfo))
		{
			continue;
		}

		buffInfo->cancel = true;

		checkTargetExitState(owner, buffInfo, exitState);

		if (0 != exitState)
		{
			auto attrNpc = owner->GetAttribute<AttributeNpc>();
			if (attrNpc)
			{
				attrNpc->npcAbilityCalculator.Calculate();
			}

			ENtfBuffInfo* ntf = NEW ENtfBuffInfo;
			ntf->isOnOff = false;
			ntf->entityId = owner->GetId();
			ntf->buff.buffId = buffInfo->buffId;
			ntf->buff.effectId = buffInfo->effectElem->index;
			ntf->buff.totalTime = buffInfo->effectElem->duration;
			ntf->buff.remainTime = 0;
			owner->GetAction< ActionSector >()->NearGridCast(EventPtr(ntf));

			theLogicEffectProcessor.OnExit(owner, buffInfo);
			actBuff->SetBuffCancel(buffInfo->buffId);
		}
	}
}

void ActionNpcScript::StartDungeonRanking()
{
	EntityNpc* owner = GetOwnerNpc();
	owner->GetAction<ActionSector>()->StartDungeonRankingBoss();
}

void ActionNpcScript::EndDungeonRanking()
{
	EntityNpc* owner = GetOwnerNpc();
	owner->GetAction<ActionSector>()->EndDungeonRankingBoss();
}

Bool ActionNpcScript::IsLastSkillHitTarget()
{
	EntityNpc* owner = GetOwnerNpc();
	return owner->GetAction<ActionNpcSkill>()->IsLastSkillHitTarget();
}

Float ActionNpcScript::GetLastSkillDirection()
{
	EntityNpc* owner = GetOwnerNpc();
	return owner->GetAction<ActionNpcSkill>()->GetLastkSkillDirection();
}

Bool ActionNpcScript::HasBuff(IndexSkill skillId )
{
	auto actBuff = GetOwnerNpc()->GetAction<ActionBuff>();
	VALID_RETURN(actBuff, false );

	auto found = actBuff->FindBuffInfo( [skillId]( const BuffInfoPtr src )
										{
											return src->infoElem->index == skillId; 
										} );

	return ( found == nullptr ) ? false : true;
}

Bool ActionNpcScript::ColosseumUseSkillByWeight()
{
	ActionNpcColosseum* anc = GetEntityAction( GetOwnerNpc() );
	VALID_RETURN(anc, false );
	return anc->UseSkillByWeidht();
}

UInt32 ActionNpcScript::GetTargetDistance()
{
	auto actSkill = GetOwnerNpc()->GetAction<ActionNpcSkill>();
	return GetDistance( actSkill->GetTargetUnitId() );
}

Bool ActionNpcScript::IsMasterSkillIdFire(IndexSkill SkillId)
{
	ActionNpc* actionNpc = GetOwnerNpc()->GetAction< ActionNpc >();
	EntityUnit* master = actionNpc->GetMasterUnit();
	if (!master)
		return false;

	State* masterCurState = master->GetHsm().GetCurrent();
	if (!masterCurState)
		return false;

	if (masterCurState->GetId() != STATE_NPC_SKILL_FIRE)
		return false;

	ActionNpcSkill* masterAns = GetEntityAction(master);
	if (!masterAns)
		return false;

	if (SkillId != masterAns->GetCurrentSkillId())
		return false;

	return true;
}

void ActionNpcScript::ServantsSkillFire( IndexSkill SkillId )
{
	ActionSkillControl* actionNpcSkillctrl = GetOwnerNpc()->GetAction< ActionSkillControl >();
	VALID_RETURN(actionNpcSkillctrl, );
	actionNpcSkillctrl->ServantSkillFire(SkillId);
}

void ActionNpcScript::ServantsSkillFireFromNpc( IndexNpc indexNpc, IndexSkill SkillId )
{
	EntityNpc* npc = GetOwnerNpc();
	VERIFY_RETURN(npc && npc->IsValid(), );

	npc->GetSkillControlAction().ServantSkillFire( indexNpc, SkillId );
}

Bool ActionNpcScript::ObjectSkillFire(IndexNpc indexNpc, IndexSkill skillId)
{
	
	Entity* npc = m_sector->FindNpcByCond(
		[&](const SectorEntityMap::value_type& v) -> bool
	{
		EntityNpc* eachNpc = static_cast<EntityNpc*>(v.second);
		return eachNpc->GetNpcIndex() == indexNpc;
	}
	);

	VALID_RETURN(npc, false);
	
	npc->GetAction< ActionNpcSkill >()->SetTargetUnitId(npc->GetId());

	VALID_RETURN(npc->GetAction< ActionNpcSkill >()->SelectSkill(skillId), false);
	
	npc->GetHsm().Tran(STATE_NPC_SKILL_FIRE);

	return true;
	
}

Bool ActionNpcScript::HasNpcSpeed()
{
	ActionNpcAbility* actNpcAbility = GetEntityAction( GetOwnerNpc() );
	MU2_ASSERT( actNpcAbility );

	UInt32 walkSpeed = 0, runSpeed = 0;

	actNpcAbility->GetNpcSpeed( walkSpeed, runSpeed );

	if ( walkSpeed <= 0 && runSpeed <= 0 )
	{
		return false;
	}

	return true;
}

UInt16 ActionNpcScript::GetBuffStackCount(IndexSkill skillId)
{
	auto actBuff = GetOwnerNpc()->GetAction<ActionBuff>();
	if (nullptr == actBuff)
	{
		return 0;
	}

	auto foundBuffInfo = actBuff->FindBuffInfo( [&](const BuffInfoPtr buffInfo) 
	{
		return buffInfo->infoElem->index == skillId;
	} );

	return (nullptr == foundBuffInfo) ? 0 : foundBuffInfo->stack;
}

void ActionNpcScript::FirePointSkillSpawnVolumeIndex(IndexSkill skillId, UInt32 spawnVolumeIndex)
{
	IndexZone indexZone = (m_sector->GetIndexZone());

	VolumeSpawnScript* vscript = GetZoneScript<VolumeSpawnScript>(indexZone);
	VERIFY_RETURN(vscript, );

	const VolumeSpawnElem* spawnElem = vscript->Get(spawnVolumeIndex);
	VERIFY_RETURN(spawnElem, );

	EntityNpc* owner = GetOwnerNpc();

	auto actionNpcAggro = owner->GetAction<ActionNpcAggro>();
	VERIFY_RETURN(actionNpcAggro, );

	VERIFY_RETURN(SelectSkill(skillId), );

	actionNpcAggro->SetCompelHasAggroList(true);

	SetSkillSpecificPos(spawnElem->x, spawnElem->y, spawnElem->z);

	Tran(EntityState::STATE_NPC_SKILL_FIRE);
}

Bool ActionNpcScript::LinkDamageByNpcIndex(IndexNpc npcIndex)
{
	EntityNpc* ownNpc = GetOwnerNpc();
	VERIFY_RETURN(ownNpc && ownNpc->IsValid(), false);

	Sector* sector = ownNpc->GetSector();
	VERIFY_RETURN(sector, false);

	VectorNpc vecNpc;
	sector->GetNpcs(vecNpc);

	for (EntityNpc* foundNpc : vecNpc)
	{
		VALID_DO(foundNpc && foundNpc->IsValid() && !foundNpc->IsDead(), continue);				
		VALID_DO(foundNpc->GetNpcIndex() == npcIndex, continue);

		ownNpc->GetNpcAction().	InsertLinkNpc(foundNpc->GetId());
		foundNpc->GetNpcAction().InsertLinkNpc(ownNpc->GetId());
	}

	VALID_RETURN(ownNpc->GetNpcAction().GetLinkNpcCount(), false);

	return true;
}

EntityId ActionNpcScript::GetTargetEntityId()
{
	EntityNpc* ownNpc = GetOwnerNpc();
	
	if (ownNpc->GetSkillAction().SearchEnemy() == false )
	{
		return 0;
	}

	return ownNpc->GetSkillAction().GetTargetUnitId();
}

void ActionNpcScript::SetLuaTargetEntityId()
{
	AttributeNpc* an = GetEntityAttribute( GetOwnerNpc() );
	EntityNpc* ownNpc = GetOwnerNpc();	

	an->luaTargetUnit = ownNpc->GetSkillAction().GetTargetUnitId();
}

EntityId ActionNpcScript::GetLuaTargetEntityId()
{	
	return GetOwnerNpc()->GetNpcAttr().luaTargetUnit;
}

void ActionNpcScript::KillLuaTargetNpc()
{
	EntityNpc* ownNpc = GetOwnerNpc();

	Sector* sector = ownNpc->GetSector();
	VERIFY_RETURN( sector, );
	VectorNpc vecNpc;
	sector->GetNpcs( vecNpc );

	for ( auto& target : vecNpc )
	{
		VALID_DO( GetLuaTargetEntityId() == target->GetId(), continue );

		target->GetHsm().Tran( EntityState::STATE_NPC_DEAD );

		// 죽는 애니매이션 나올 수 있도록
		EffectDamagedInfo damagedInfo;
		damagedInfo.entityId = target->GetId();
		ENtfGameSkillEventResult* ntf = NEW ENtfGameSkillEventResult;
		ntf->skillEventResult.directEffectDamagedInfos.push_back( damagedInfo );
		ownNpc->GetSectorAction().RangeCast( EventPtr( ntf ) );

		break;
	}
}

void ActionNpcScript::SetInvincible( UInt32 invincible )
{
	AttributeNpc* attrNpc = GetEntityAttribute( GetOwnerNpc() );
	attrNpc->invincible = invincible;
}

void ActionNpcScript::SetInvincibleLuaTargetNpc( UInt32 invincible )
{
	EntityNpc* ownNpc = GetOwnerNpc();

	Sector* sector = ownNpc->GetSector();
	VERIFY_RETURN( sector, );
	VectorNpc vecNpc;
	sector->GetNpcs( vecNpc );

	for ( auto& target : vecNpc )
	{
		VALID_DO( GetLuaTargetEntityId() == target->GetId(), continue );
		AttributeNpc* attrNpc = GetEntityAttribute( target );
		attrNpc->invincible = invincible;

		break;
	}
}

void ActionNpcScript::KillNpcWithinRange(UInt32 range, IndexNpc indexNpc)
{
	EntityNpc* ownerNpc = GetOwnerNpc();
	VERIFY_RETURN(ownerNpc && ownerNpc->IsValid(), );

	Sector* sector = ownerNpc->GetSector();
	VERIFY_RETURN(sector, );

	VectorNpc vecNpc;
	sector->GetNpcs(vecNpc);

	for (auto npc : vecNpc)
	{
		VALID_DO(npc && npc->IsValid() && npc->GetId() != ownerNpc->GetId() && npc->GetNpcIndex() == indexNpc, continue);

		if (range >= GetDistance(npc->GetId()))
		{
			npc->GetHsm().Tran(EntityState::STATE_NPC_DEAD);
		}
	}
}

#ifdef __Hotfix_Barex_AI_fix_by_jjangmo_2019_1_24
void ActionNpcScript::SetLuaTargetEntityIdByUniqueNpcIndex( IndexNpc npcIndex )
{
	AttributeNpc* an = GetEntityAttribute( GetOwnerNpc() );
	EntityNpc* ownNpc = GetOwnerNpc();

	Sector* sector = ownNpc->GetSector();
	VERIFY_RETURN( sector, );
	VectorNpc vecNpc;
	sector->GetNpcs( vecNpc );

	for ( auto& target : vecNpc )
	{
		VALID_DO( npcIndex == target->GetNpcIndex(), continue );
		an->luaTargetUnit = target->GetId().GetUnique();

		break;
	}
}

Bool ActionNpcScript::FindEntityIdByUniqueNpcIndex( IndexNpc npcIndex )
{
	EntityNpc* ownNpc = GetOwnerNpc();

	Sector* sector = ownNpc->GetSector();
	VERIFY_RETURN( sector, false );
	VectorNpc vecNpc;
	sector->GetNpcs( vecNpc );

	for ( auto& target : vecNpc )
	{
		VALID_DO( npcIndex == target->GetNpcIndex(), continue );
		return true;
	}

	return false;
}

void ActionNpcScript::FirePointDirSkillSpawnVolumeIndex( IndexSkill skillId, UInt32 spawnVolumeIndex )
{
	IndexZone indexZone = ( m_sector->GetIndexZone() );

	VolumeSpawnScript* vscript = GetZoneScript<VolumeSpawnScript>( indexZone );
	VERIFY_RETURN( vscript, );

	const VolumeSpawnElem* spawnElem = vscript->Get( spawnVolumeIndex );
	VERIFY_RETURN( spawnElem, );
	EntityNpc* owner = GetOwnerNpc();

	auto actionNpcAggro = owner->GetAction<ActionNpcAggro>();
	VERIFY_RETURN( actionNpcAggro, );

	VERIFY_RETURN( SelectSkill( skillId ), );

	actionNpcAggro->SetCompelHasAggroList( true );

	SetSkillSpecificPos( spawnElem->x, spawnElem->y, spawnElem->z );
	SetSkillDirection( spawnElem->dir );

	Tran( EntityState::STATE_NPC_SKILL_FIRE );
}
#endif
void ActionNpcScript::SetRandomMoveChase()
{
	EntityNpc* ownerNpc = GetOwnerNpc();
	VERIFY_RETURN(ownerNpc && ownerNpc->IsValid(), );

	ActionNpc& actNpc = ownerNpc->GetNpcAction();
	actNpc.SetRandomMoveChase();
}

UInt32 ActionNpcScript::GetDistanceNpc(const IndexNpc npcIndex)
{
	EntityNpc* ownerNpc = GetOwnerNpc();
	VERIFY_RETURN(ownerNpc && ownerNpc->IsValid(), 100000000);

	Sector* sector = ownerNpc->GetSector();
	VERIFY_RETURN(sector, 100000000);

	
	Vector3 sourcePos = ownerNpc->GetRealPosition();

	VectorNpc vecNpc;
	sector->GetNpcs(vecNpc);

	for (auto npc : vecNpc)
	{
		VALID_DO(npc && npc->IsValid() && npc->GetId() != ownerNpc->GetId() && npc->GetNpcIndex() == npcIndex, continue);

		
		Vector3 targetPos = npc->GetRealPosition();
		
		Float distance = (sourcePos - targetPos).Length();

		UInt32 myCollisionR = 0;
		ownerNpc->GetCollision(myCollisionR);

		UInt32 targetCollisionR = 0;
		npc->GetCollision(targetCollisionR);

		distance -= myCollisionR;
		distance -= targetCollisionR;
		distance = std::max< Float >(0, distance);
		
		return static_cast< UInt32 >(distance);
	}

	return 100000000;
}
void ActionNpcScript::CastAndNearestNpcDead(const IndexNpc npcIndex, const UInt32 nearestRange, const IndexSkill skillId)
{
	EntityNpc* ownerNpc = GetOwnerNpc();
	VERIFY_RETURN(ownerNpc && ownerNpc->IsValid(), );

	Sector* sector = ownerNpc->GetSector();
	VERIFY_RETURN(sector, );

	Vector3 sourcePos = ownerNpc->GetRealPosition();

	VectorNpc vecNpc;
	sector->GetNpcs(vecNpc);

	for (auto npc : vecNpc)
	{
		VALID_DO(npc && npc->IsValid() && npc->IsAlive() && npc->GetNpcIndex() == npcIndex, continue);

		Vector3 targetPos = npc->GetRealPosition();

		Float distance = (sourcePos - targetPos).Length();

		UInt32 myCollisionR = 0;
		ownerNpc->GetCollision(myCollisionR);

		UInt32 targetCollisionR = 0;
		npc->GetCollision(targetCollisionR);

		distance -= myCollisionR;
		distance -= targetCollisionR;
		distance = std::max< Float >(0, distance);

		if (nearestRange > distance)
		{
			theLogicEffectSystem.OnSingleCast(ownerNpc, skillId);
			npc->GetHsm().Tran(EntityState::STATE_NPC_DEAD);
		}
	}
}



	

void ActionNpcScript::SummonNpcInSpawnVolume(const IndexSpawnvolume indexSpawnvolume)
{
	EntityNpc* ownerNpc = GetOwnerNpc();
	VERIFY_RETURN(ownerNpc && ownerNpc->IsValid(), );

	Sector* sector = ownerNpc->GetSector();
	VERIFY_RETURN(sector, );

	VolumeSpawnScript* vscript = GetZoneScript<VolumeSpawnScript>(sector->GetIndexZone());
	VALID_RETURN(vscript, );

	const VolumeSpawnElem* elem = vscript->Get(indexSpawnvolume);
	VALID_RETURN(elem, );

	for (auto& npcRegen : elem->npcRegen)
	{
		if (npcRegen.npcIndex == 0)
			continue;

		const NpcInfoElem* script = SCRIPTS.GetNpcInfoScript(npcRegen.npcIndex);
		if (script == nullptr)
			continue;

		SpawnSystem::BaseSpawnInfo baseSpawnInfo;
		baseSpawnInfo.npcIndex		  = npcRegen.npcIndex;
		baseSpawnInfo.sector		  = sector;
		baseSpawnInfo.spawnPos		  = ownerNpc->GetCurrPos();
		baseSpawnInfo.dir			  = elem->dir;
		baseSpawnInfo.aiIndex		  = script->aiIndex;
		baseSpawnInfo.pursuitRange	  = elem->npcRegen[0].pursuitRange;
		baseSpawnInfo.level			  = ownerNpc->GetLevel();
		baseSpawnInfo.spawnPos		  = Vector3(elem->x, elem->y, elem->z);
				
		EntityNpc* npc = NULL;
		theSpawnSystem.SpawnGeneralNpc(&baseSpawnInfo, &npc);
	}
}


#ifdef	SCRIPT_FORCED_SKILL_SUCCESS_by_cheolhoon_180530	
void ActionNpcScript::SetForcedSuccessWithoutTarget(UInt32 forcedSuccessWithoutTarget)
{
	ActionNpcSkill* ans = GetEntityAction(GetOwnerNpc());

	if(ans)
	{
		ans->SetForcedSuccessWithoutTarget(forcedSuccessWithoutTarget ? true : false);
	}
}
#endif // SCRIPT_FORCED_SKILL_SUCCESS_by_cheolhoon_180530

#ifdef TALISMAN_AI_ADD_SEARCH_ENEMY_by_cheolhoon_180626
Bool ActionNpcScript::SearchEnemyOnEmployerTarget()
{
	return GetOwnerNpc()->GetAction< ActionNpcSkill >()->SearchEnemyOnEmployerTarget();
}
#endif

void ActionNpcScript::CancelTargetBuff(const IndexSkill indexSkill)
{
	ActionNpcSkill* ans = GetEntityAction(GetOwnerNpc());
	const EntityId& targetUnitId = ans->GetTargetUnitId();

	EntityUnit* target = GetOwnerNpc()->GetSectorAction().GetUnitInMySector(targetUnitId);
	if (target == nullptr)
	{
		return;
	}
	
	theLogicEffectSystem.OnCancelBuffCast(target, indexSkill);
	
}
} // mu2
