; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	??0exception@std@@QEAA@AEBV01@@Z		; std::exception::exception
PUBLIC	?what@exception@std@@UEBAPEBDXZ			; std::exception::what
PUBLIC	??_Gexception@std@@UEAAPEAXI@Z			; std::exception::`scalar deleting destructor'
PUBLIC	??0bad_alloc@std@@QEAA@AEBV01@@Z		; std::bad_alloc::bad_alloc
PUBLIC	??_Gbad_alloc@std@@UEAAPEAXI@Z			; std::bad_alloc::`scalar deleting destructor'
PUBL<PERSON>	??0bad_array_new_length@std@@QEAA@XZ		; std::bad_array_new_length::bad_array_new_length
PUBLIC	??1bad_array_new_length@std@@UEAA@XZ		; std::bad_array_new_length::~bad_array_new_length
PUBLIC	??0bad_array_new_length@std@@QEAA@AEBV01@@Z	; std::bad_array_new_length::bad_array_new_length
PUBLIC	??_Gbad_array_new_length@std@@UEAAPEAXI@Z	; std::bad_array_new_length::`scalar deleting destructor'
PUBLIC	?_Throw_bad_array_new_length@std@@YAXXZ		; std::_Throw_bad_array_new_length
PUBLIC	?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
PUBLIC	?_Xlen_string@std@@YAXXZ			; std::_Xlen_string
PUBLIC	?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z	; std::allocator<wchar_t>::allocate
PUBLIC	??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
PUBLIC	?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
PUBLIC	??1?$_Compressed_pair@V?$allocator@_W@std@@V?$_String_val@U?$_Simple_types@_W@std@@@2@$00@std@@QEAA@XZ ; std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>::~_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>
PUBLIC	?_Decref@_Ref_count_base@std@@QEAAXXZ		; std::_Ref_count_base::_Decref
PUBLIC	?_Get_deleter@_Ref_count_base@std@@UEBAPEAXAEBVtype_info@@@Z ; std::_Ref_count_base::_Get_deleter
PUBLIC	??$_Hash_representation@I@std@@YA_KAEBI@Z	; std::_Hash_representation<unsigned int>
PUBLIC	??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::operator delete
PUBLIC	??1?$ISingleton@VLogAssist@mu2@@@mu2@@UEAA@XZ	; mu2::ISingleton<mu2::LogAssist>::~ISingleton<mu2::LogAssist>
PUBLIC	??_G?$ISingleton@VLogAssist@mu2@@@mu2@@UEAAPEAXI@Z ; mu2::ISingleton<mu2::LogAssist>::`scalar deleting destructor'
PUBLIC	?Init@LogAssist@mu2@@UEAA_NPEAX@Z		; mu2::LogAssist::Init
PUBLIC	?UnInit@LogAssist@mu2@@UEAA_NXZ			; mu2::LogAssist::UnInit
PUBLIC	?allocate@?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@QEAAPEAU?$_List_node@U?$pair@$$CBII@std@@PEAX@2@_K@Z ; std::allocator<std::_List_node<std::pair<unsigned int const ,unsigned int>,void *> >::allocate
PUBLIC	??1?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ ; std::list<std::pair<unsigned int const ,unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >::~list<std::pair<unsigned int const ,unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >
PUBLIC	?clear@?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAAXXZ ; std::list<std::pair<unsigned int const ,unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >::clear
PUBLIC	?_Tidy@?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@AEAAXXZ ; std::list<std::pair<unsigned int const ,unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >::_Tidy
PUBLIC	?_Alloc_sentinel_and_proxy@?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@AEAAXXZ ; std::list<std::pair<unsigned int const ,unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >::_Alloc_sentinel_and_proxy
PUBLIC	??0?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA@AEBV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@1@AEBV?$allocator@U?$pair@$$CBII@std@@@1@@Z ; std::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >
PUBLIC	?_Unchecked_erase@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@AEAAPEAU?$_List_node@U?$pair@$$CBII@std@@PEAX@2@PEAU32@QEAU32@@Z ; std::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Unchecked_erase
PUBLIC	?clear@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAXXZ ; std::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::clear
PUBLIC	?allocate@?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@QEAAPEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@2@_K@Z ; std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > >::allocate
PUBLIC	?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@2@@Z ; std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > > >::_Assign_grow
PUBLIC	?_Tidy@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAXXZ ; std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > > >::_Tidy
PUBLIC	??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ ; std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > > >::~_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > > >
PUBLIC	??0?$unordered_map@IIU?$hash@I@std@@U?$equal_to@I@2@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ ; std::unordered_map<unsigned int,unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >::unordered_map<unsigned int,unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >
PUBLIC	??0LogAssist@mu2@@QEAA@XZ			; mu2::LogAssist::LogAssist
PUBLIC	??_GLogAssist@mu2@@UEAAPEAXI@Z			; mu2::LogAssist::`scalar deleting destructor'
PUBLIC	??$_Fnv1a_append_value@I@std@@YA_K_KAEBI@Z	; std::_Fnv1a_append_value<unsigned int>
PUBLIC	??$_Construct@$00PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<1,wchar_t const *>
PUBLIC	??$_Free_non_head@V?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@SAXAEAV?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU01@@Z ; std::_List_node<std::pair<unsigned int const ,unsigned int>,void *>::_Free_non_head<std::allocator<std::_List_node<std::pair<unsigned int const ,unsigned int>,void *> > >
PUBLIC	?_Bump_erased@_Range_eraser@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAXXZ ; std::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Range_eraser::_Bump_erased
PUBLIC	??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
PUBLIC	??$uninitialized_fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@0@0AEBV10@@Z ; std::uninitialized_fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > >
PUBLIC	wmain
PUBLIC	??1?$shared_ptr@VWorldServer@mu2@@@std@@QEAA@XZ	; std::shared_ptr<mu2::WorldServer>::~shared_ptr<mu2::WorldServer>
PUBLIC	??$?0VWorldServer@mu2@@$0A@@?$shared_ptr@VWorldServer@mu2@@@std@@QEAA@PEAVWorldServer@mu2@@@Z ; std::shared_ptr<mu2::WorldServer>::shared_ptr<mu2::WorldServer><mu2::WorldServer,0>
PUBLIC	??1?$_Temporary_owner@VWorldServer@mu2@@@std@@QEAA@XZ ; std::_Temporary_owner<mu2::WorldServer>::~_Temporary_owner<mu2::WorldServer>
PUBLIC	?_Destroy@?$_Ref_count@VWorldServer@mu2@@@std@@EEAAXXZ ; std::_Ref_count<mu2::WorldServer>::_Destroy
PUBLIC	?_Delete_this@?$_Ref_count@VWorldServer@mu2@@@std@@EEAAXXZ ; std::_Ref_count<mu2::WorldServer>::_Delete_this
PUBLIC	??_G?$_Ref_count@VWorldServer@mu2@@@std@@UEAAPEAXI@Z ; std::_Ref_count<mu2::WorldServer>::`scalar deleting destructor'
PUBLIC	??_7exception@std@@6B@				; std::exception::`vftable'
PUBLIC	??_C@_0BC@EOODALEL@Unknown?5exception@		; `string'
PUBLIC	??_7bad_alloc@std@@6B@				; std::bad_alloc::`vftable'
PUBLIC	??_7bad_array_new_length@std@@6B@		; std::bad_array_new_length::`vftable'
PUBLIC	??_C@_0BF@KINCDENJ@bad?5array?5new?5length@	; `string'
PUBLIC	??_R0?AVexception@std@@@8			; std::exception `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
PUBLIC	_TI3?AVbad_array_new_length@std@@
PUBLIC	_CTA3?AVbad_array_new_length@std@@
PUBLIC	??_R0?AVbad_array_new_length@std@@@8		; std::bad_array_new_length `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
PUBLIC	??_R0?AVbad_alloc@std@@@8			; std::bad_alloc `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
PUBLIC	??_C@_0BA@JFNIOLAK@string?5too?5long@		; `string'
PUBLIC	?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A ; mu2::ISingleton<mu2::Logger>::inst
PUBLIC	??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@		; `string'
PUBLIC	?inst@?$ISingleton@VLogAssist@mu2@@@mu2@@1VLogAssist@2@A ; mu2::ISingleton<mu2::LogAssist>::inst
PUBLIC	??_7?$ISingleton@VLogAssist@mu2@@@mu2@@6B@	; mu2::ISingleton<mu2::LogAssist>::`vftable'
PUBLIC	??_7LogAssist@mu2@@6B@				; mu2::LogAssist::`vftable'
PUBLIC	??_R4exception@std@@6B@				; std::exception::`RTTI Complete Object Locator'
PUBLIC	??_R3exception@std@@8				; std::exception::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2exception@std@@8				; std::exception::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@exception@std@@8			; std::exception::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4bad_array_new_length@std@@6B@		; std::bad_array_new_length::`RTTI Complete Object Locator'
PUBLIC	??_R3bad_array_new_length@std@@8		; std::bad_array_new_length::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2bad_array_new_length@std@@8		; std::bad_array_new_length::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@bad_array_new_length@std@@8	; std::bad_array_new_length::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@bad_alloc@std@@8			; std::bad_alloc::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R3bad_alloc@std@@8				; std::bad_alloc::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2bad_alloc@std@@8				; std::bad_alloc::`RTTI Base Class Array'
PUBLIC	??_R4bad_alloc@std@@6B@				; std::bad_alloc::`RTTI Complete Object Locator'
PUBLIC	??_R0?AV_Ref_count_base@std@@@8			; std::_Ref_count_base `RTTI Type Descriptor'
PUBLIC	??_R3_Ref_count_base@std@@8			; std::_Ref_count_base::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2_Ref_count_base@std@@8			; std::_Ref_count_base::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@_Ref_count_base@std@@8		; std::_Ref_count_base::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4LogAssist@mu2@@6B@				; mu2::LogAssist::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVLogAssist@mu2@@@8			; mu2::LogAssist `RTTI Type Descriptor'
PUBLIC	??_R3LogAssist@mu2@@8				; mu2::LogAssist::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2LogAssist@mu2@@8				; mu2::LogAssist::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@LogAssist@mu2@@8			; mu2::LogAssist::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@?$ISingleton@VLogAssist@mu2@@@mu2@@8 ; mu2::ISingleton<mu2::LogAssist>::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R0?AV?$ISingleton@VLogAssist@mu2@@@mu2@@@8	; mu2::ISingleton<mu2::LogAssist> `RTTI Type Descriptor'
PUBLIC	??_R3?$ISingleton@VLogAssist@mu2@@@mu2@@8	; mu2::ISingleton<mu2::LogAssist>::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2?$ISingleton@VLogAssist@mu2@@@mu2@@8	; mu2::ISingleton<mu2::LogAssist>::`RTTI Base Class Array'
PUBLIC	??_R4?$ISingleton@VLogAssist@mu2@@@mu2@@6B@	; mu2::ISingleton<mu2::LogAssist>::`RTTI Complete Object Locator'
PUBLIC	??_C@_0BH@FOJBDNAM@Failed?5to?5start?5server@	; `string'
PUBLIC	??_C@_0ED@INKANDI@F?3?2Release_Branch?2Server?2Develo@ ; `string'
PUBLIC	??_C@_05GFKPGDJ@wmain@				; `string'
PUBLIC	??_C@_05PDJBBECF@pause@				; `string'
PUBLIC	??_7?$_Ref_count@VWorldServer@mu2@@@std@@6B@	; std::_Ref_count<mu2::WorldServer>::`vftable'
PUBLIC	??_R4?$_Ref_count@VWorldServer@mu2@@@std@@6B@	; std::_Ref_count<mu2::WorldServer>::`RTTI Complete Object Locator'
PUBLIC	??_R0?AV?$_Ref_count@VWorldServer@mu2@@@std@@@8	; std::_Ref_count<mu2::WorldServer> `RTTI Type Descriptor'
PUBLIC	??_R3?$_Ref_count@VWorldServer@mu2@@@std@@8	; std::_Ref_count<mu2::WorldServer>::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2?$_Ref_count@VWorldServer@mu2@@@std@@8	; std::_Ref_count<mu2::WorldServer>::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@?$_Ref_count@VWorldServer@mu2@@@std@@8 ; std::_Ref_count<mu2::WorldServer>::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	__real@3f800000
EXTRN	_purecall:PROC
EXTRN	??2@YAPEAX_K@Z:PROC				; operator new
EXTRN	??3@YAXPEAX_K@Z:PROC				; operator delete
EXTRN	atexit:PROC
EXTRN	__std_terminate:PROC
EXTRN	_invoke_watson:PROC
EXTRN	memcpy:PROC
EXTRN	wcslen:PROC
EXTRN	free:PROC
EXTRN	malloc:PROC
EXTRN	system:PROC
EXTRN	__std_exception_copy:PROC
EXTRN	__std_exception_destroy:PROC
EXTRN	??_Eexception@std@@UEAAPEAXI@Z:PROC		; std::exception::`vector deleting destructor'
EXTRN	??_Ebad_alloc@std@@UEAAPEAXI@Z:PROC		; std::bad_alloc::`vector deleting destructor'
EXTRN	??_Ebad_array_new_length@std@@UEAAPEAXI@Z:PROC	; std::bad_array_new_length::`vector deleting destructor'
EXTRN	?_Xlength_error@std@@YAXPEBD@Z:PROC		; std::_Xlength_error
EXTRN	??1Logger@mu2@@UEAA@XZ:PROC			; mu2::Logger::~Logger
EXTRN	??0Logger@mu2@@AEAA@XZ:PROC			; mu2::Logger::Logger
EXTRN	?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ:PROC	; mu2::Logger::Logging
EXTRN	?Initialize@Server@mu2@@QEAA_NXZ:PROC		; mu2::Server::Initialize
EXTRN	?Destroy@Server@mu2@@QEAAXXZ:PROC		; mu2::Server::Destroy
EXTRN	?RunServer@Server@mu2@@QEAAXXZ:PROC		; mu2::Server::RunServer
EXTRN	?GetExitCode@Server@mu2@@QEBAIXZ:PROC		; mu2::Server::GetExitCode
EXTRN	??_E?$ISingleton@VLogAssist@mu2@@@mu2@@UEAAPEAXI@Z:PROC ; mu2::ISingleton<mu2::LogAssist>::`vector deleting destructor'
EXTRN	?LogSetupFlag@LogAssist@mu2@@QEAAXXZ:PROC	; mu2::LogAssist::LogSetupFlag
EXTRN	??_ELogAssist@mu2@@UEAAPEAXI@Z:PROC		; mu2::LogAssist::`vector deleting destructor'
EXTRN	??0WorldServer@mu2@@QEAA@XZ:PROC		; mu2::WorldServer::WorldServer
EXTRN	??0MonitorWorld@mu2@@QEAA@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z:PROC ; mu2::MonitorWorld::MonitorWorld
EXTRN	??_E?$_Ref_count@VWorldServer@mu2@@@std@@UEAAPEAXI@Z:PROC ; std::_Ref_count<mu2::WorldServer>::`vector deleting destructor'
EXTRN	_CxxThrowException:PROC
EXTRN	__CxxFrameHandler4:PROC
EXTRN	??_7type_info@@6B@:BYTE				; type_info::`vftable'
EXTRN	_fltused:DWORD
;	COMDAT ?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A
_BSS	SEGMENT
?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A DB 0350H DUP (?) ; mu2::ISingleton<mu2::Logger>::inst
_BSS	ENDS
;	COMDAT ?inst@?$ISingleton@VLogAssist@mu2@@@mu2@@1VLogAssist@2@A
_BSS	SEGMENT
?inst@?$ISingleton@VLogAssist@mu2@@@mu2@@1VLogAssist@2@A DB 048H DUP (?) ; mu2::ISingleton<mu2::LogAssist>::inst
_BSS	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0exception@std@@QEAA@AEBV01@@Z DD imagerel $LN4
	DD	imagerel $LN4+89
	DD	imagerel $unwind$??0exception@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?what@exception@std@@UEBAPEBDXZ DD imagerel $LN5
	DD	imagerel $LN5+56
	DD	imagerel $unwind$?what@exception@std@@UEBAPEBDXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gexception@std@@UEAAPEAXI@Z DD imagerel $LN10
	DD	imagerel $LN10+83
	DD	imagerel $unwind$??_Gexception@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z DD imagerel $LN9
	DD	imagerel $LN9+104
	DD	imagerel $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+83
	DD	imagerel $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_array_new_length@std@@QEAA@XZ DD imagerel $LN14
	DD	imagerel $LN14+95
	DD	imagerel $unwind$??0bad_array_new_length@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1bad_array_new_length@std@@UEAA@XZ DD imagerel $LN14
	DD	imagerel $LN14+47
	DD	imagerel $unwind$??1bad_array_new_length@std@@UEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z DD imagerel $LN14
	DD	imagerel $LN14+54
	DD	imagerel $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z DD imagerel $LN20
	DD	imagerel $LN20+83
	DD	imagerel $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Throw_bad_array_new_length@std@@YAXXZ DD imagerel $LN3
	DD	imagerel $LN3+37
	DD	imagerel $unwind$?_Throw_bad_array_new_length@std@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z DD imagerel $LN5
	DD	imagerel $LN5+159
	DD	imagerel $unwind$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Xlen_string@std@@YAXXZ DD imagerel $LN3
	DD	imagerel $LN3+22
	DD	imagerel $unwind$?_Xlen_string@std@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z DD imagerel $LN13
	DD	imagerel $LN13+162
	DD	imagerel $unwind$?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z DD imagerel $LN221
	DD	imagerel $LN221+150
	DD	imagerel $unwind$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z@4HA DD imagerel ?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z@4HA
	DD	imagerel ?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z@4HA+24
	DD	imagerel $unwind$?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ DD imagerel $LN38
	DD	imagerel $LN38+250
	DD	imagerel $unwind$?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Decref@_Ref_count_base@std@@QEAAXXZ DD imagerel $LN11
	DD	imagerel $LN11+98
	DD	imagerel $unwind$?_Decref@_Ref_count_base@std@@QEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Hash_representation@I@std@@YA_KAEBI@Z DD imagerel $LN4
	DD	imagerel $LN4+34
	DD	imagerel $unwind$??$_Hash_representation@I@std@@YA_KAEBI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z DD imagerel $LN6
	DD	imagerel $LN6+25
	DD	imagerel $unwind$??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_G?$ISingleton@VLogAssist@mu2@@@mu2@@UEAAPEAXI@Z DD imagerel $LN10
	DD	imagerel $LN10+65
	DD	imagerel $unwind$??_G?$ISingleton@VLogAssist@mu2@@@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?UnInit@LogAssist@mu2@@UEAA_NXZ DD imagerel $LN340
	DD	imagerel $LN340+33
	DD	imagerel $unwind$?UnInit@LogAssist@mu2@@UEAA_NXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?allocate@?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@QEAAPEAU?$_List_node@U?$pair@$$CBII@std@@PEAX@2@_K@Z DD imagerel $LN13
	DD	imagerel $LN13+160
	DD	imagerel $unwind$?allocate@?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@QEAAPEAU?$_List_node@U?$pair@$$CBII@std@@PEAX@2@_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ DD imagerel $LN104
	DD	imagerel $LN104+25
	DD	imagerel $unwind$??1?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?clear@?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAAXXZ DD imagerel $LN69
	DD	imagerel $LN69+117
	DD	imagerel $unwind$?clear@?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Tidy@?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@AEAAXXZ DD imagerel $LN99
	DD	imagerel $LN99+152
	DD	imagerel $unwind$?_Tidy@?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@AEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Alloc_sentinel_and_proxy@?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@AEAAXXZ DD imagerel $LN75
	DD	imagerel $LN75+275
	DD	imagerel $unwind$?_Alloc_sentinel_and_proxy@?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@AEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA@AEBV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@1@AEBV?$allocator@U?$pair@$$CBII@std@@@1@@Z DD imagerel $LN200
	DD	imagerel $LN200+432
	DD	imagerel $unwind$??0?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA@AEBV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@1@AEBV?$allocator@U?$pair@$$CBII@std@@@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???0?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA@AEBV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@1@AEBV?$allocator@U?$pair@$$CBII@std@@@1@@Z@4HA DD imagerel ?dtor$0@?0???0?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA@AEBV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@1@AEBV?$allocator@U?$pair@$$CBII@std@@@1@@Z@4HA
	DD	imagerel ?dtor$0@?0???0?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA@AEBV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@1@AEBV?$allocator@U?$pair@$$CBII@std@@@1@@Z@4HA+31
	DD	imagerel $unwind$?dtor$0@?0???0?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA@AEBV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@1@AEBV?$allocator@U?$pair@$$CBII@std@@@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$1@?0???0?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA@AEBV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@1@AEBV?$allocator@U?$pair@$$CBII@std@@@1@@Z@4HA DD imagerel ?dtor$1@?0???0?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA@AEBV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@1@AEBV?$allocator@U?$pair@$$CBII@std@@@1@@Z@4HA
	DD	imagerel ?dtor$1@?0???0?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA@AEBV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@1@AEBV?$allocator@U?$pair@$$CBII@std@@@1@@Z@4HA+31
	DD	imagerel $unwind$?dtor$1@?0???0?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA@AEBV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@1@AEBV?$allocator@U?$pair@$$CBII@std@@@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Unchecked_erase@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@AEAAPEAU?$_List_node@U?$pair@$$CBII@std@@PEAX@2@PEAU32@QEAU32@@Z DD imagerel $LN220
	DD	imagerel $LN220+1034
	DD	imagerel $unwind$?_Unchecked_erase@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@AEAAPEAU?$_List_node@U?$pair@$$CBII@std@@PEAX@2@PEAU32@QEAU32@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?clear@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAXXZ DD imagerel $LN336
	DD	imagerel $LN336+316
	DD	imagerel $unwind$?clear@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?allocate@?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@QEAAPEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@2@_K@Z DD imagerel $LN13
	DD	imagerel $LN13+163
	DD	imagerel $unwind$?allocate@?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@QEAAPEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@2@_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@2@@Z DD imagerel $LN70
	DD	imagerel $LN70+565
	DD	imagerel $unwind$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Tidy@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAXXZ DD imagerel $LN35
	DD	imagerel $LN35+213
	DD	imagerel $unwind$?_Tidy@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ DD imagerel $LN40
	DD	imagerel $LN40+25
	DD	imagerel $unwind$??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0?$unordered_map@IIU?$hash@I@std@@U?$equal_to@I@2@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ DD imagerel $LN204
	DD	imagerel $LN204+161
	DD	imagerel $unwind$??0?$unordered_map@IIU?$hash@I@std@@U?$equal_to@I@2@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0LogAssist@mu2@@QEAA@XZ DD imagerel $LN210
	DD	imagerel $LN210+67
	DD	imagerel $unwind$??0LogAssist@mu2@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???0LogAssist@mu2@@QEAA@XZ@4HA DD imagerel ?dtor$0@?0???0LogAssist@mu2@@QEAA@XZ@4HA
	DD	imagerel ?dtor$0@?0???0LogAssist@mu2@@QEAA@XZ@4HA+24
	DD	imagerel $unwind$?dtor$0@?0???0LogAssist@mu2@@QEAA@XZ@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GLogAssist@mu2@@UEAAPEAXI@Z DD imagerel $LN171
	DD	imagerel $LN171+115
	DD	imagerel $unwind$??_GLogAssist@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Fnv1a_append_value@I@std@@YA_K_KAEBI@Z DD imagerel $LN12
	DD	imagerel $LN12+134
	DD	imagerel $unwind$??$_Fnv1a_append_value@I@std@@YA_K_KAEBI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Construct@$00PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z DD imagerel $LN188
	DD	imagerel $LN188+836
	DD	imagerel $unwind$??$_Construct@$00PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__E?inst@?$ISingleton@VLogAssist@mu2@@@mu2@@1VLogAssist@2@A@@YAXXZ DD imagerel ??__E?inst@?$ISingleton@VLogAssist@mu2@@@mu2@@1VLogAssist@2@A@@YAXXZ
	DD	imagerel ??__E?inst@?$ISingleton@VLogAssist@mu2@@@mu2@@1VLogAssist@2@A@@YAXXZ+34
	DD	imagerel $unwind$??__E?inst@?$ISingleton@VLogAssist@mu2@@@mu2@@1VLogAssist@2@A@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__F?inst@?$ISingleton@VLogAssist@mu2@@@mu2@@1VLogAssist@2@A@@YAXXZ DD imagerel ??__F?inst@?$ISingleton@VLogAssist@mu2@@@mu2@@1VLogAssist@2@A@@YAXXZ
	DD	imagerel ??__F?inst@?$ISingleton@VLogAssist@mu2@@@mu2@@1VLogAssist@2@A@@YAXXZ+78
	DD	imagerel $unwind$??__F?inst@?$ISingleton@VLogAssist@mu2@@@mu2@@1VLogAssist@2@A@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Free_non_head@V?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@SAXAEAV?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU01@@Z DD imagerel $LN49
	DD	imagerel $LN49+164
	DD	imagerel $unwind$??$_Free_non_head@V?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@SAXAEAV?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Bump_erased@_Range_eraser@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAXXZ DD imagerel $LN56
	DD	imagerel $LN56+186
	DD	imagerel $unwind$?_Bump_erased@_Range_eraser@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z DD imagerel $LN7
	DD	imagerel $LN7+150
	DD	imagerel $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$uninitialized_fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@0@0AEBV10@@Z DD imagerel $LN59
	DD	imagerel $LN59+224
	DD	imagerel $unwind$??$uninitialized_fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@0@0AEBV10@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$wmain DD	imagerel $LN159
	DD	imagerel $LN159+760
	DD	imagerel $unwind$wmain
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$wmain$dtor$0 DD imagerel wmain$dtor$0
	DD	imagerel wmain$dtor$0+24
	DD	imagerel $unwind$wmain$dtor$0
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$wmain$dtor$2 DD imagerel wmain$dtor$2
	DD	imagerel wmain$dtor$2+24
	DD	imagerel $unwind$wmain$dtor$2
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$wmain$dtor$3 DD imagerel wmain$dtor$3
	DD	imagerel wmain$dtor$3+24
	DD	imagerel $unwind$wmain$dtor$3
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$shared_ptr@VWorldServer@mu2@@@std@@QEAA@XZ DD imagerel $LN22
	DD	imagerel $LN22+41
	DD	imagerel $unwind$??1?$shared_ptr@VWorldServer@mu2@@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$?0VWorldServer@mu2@@$0A@@?$shared_ptr@VWorldServer@mu2@@@std@@QEAA@PEAVWorldServer@mu2@@@Z DD imagerel $LN37
	DD	imagerel $LN37+320
	DD	imagerel $unwind$??$?0VWorldServer@mu2@@$0A@@?$shared_ptr@VWorldServer@mu2@@@std@@QEAA@PEAVWorldServer@mu2@@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???$?0VWorldServer@mu2@@$0A@@?$shared_ptr@VWorldServer@mu2@@@std@@QEAA@PEAVWorldServer@mu2@@@Z@4HA DD imagerel ?dtor$0@?0???$?0VWorldServer@mu2@@$0A@@?$shared_ptr@VWorldServer@mu2@@@std@@QEAA@PEAVWorldServer@mu2@@@Z@4HA
	DD	imagerel ?dtor$0@?0???$?0VWorldServer@mu2@@$0A@@?$shared_ptr@VWorldServer@mu2@@@std@@QEAA@PEAVWorldServer@mu2@@@Z@4HA+24
	DD	imagerel $unwind$?dtor$0@?0???$?0VWorldServer@mu2@@$0A@@?$shared_ptr@VWorldServer@mu2@@@std@@QEAA@PEAVWorldServer@mu2@@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$_Temporary_owner@VWorldServer@mu2@@@std@@QEAA@XZ DD imagerel $LN6
	DD	imagerel $LN6+71
	DD	imagerel $unwind$??1?$_Temporary_owner@VWorldServer@mu2@@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Destroy@?$_Ref_count@VWorldServer@mu2@@@std@@EEAAXXZ DD imagerel $LN6
	DD	imagerel $LN6+72
	DD	imagerel $unwind$?_Destroy@?$_Ref_count@VWorldServer@mu2@@@std@@EEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Delete_this@?$_Ref_count@VWorldServer@mu2@@@std@@EEAAXXZ DD imagerel $LN6
	DD	imagerel $LN6+69
	DD	imagerel $unwind$?_Delete_this@?$_Ref_count@VWorldServer@mu2@@@std@@EEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_G?$_Ref_count@VWorldServer@mu2@@@std@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+50
	DD	imagerel $unwind$??_G?$_Ref_count@VWorldServer@mu2@@@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__E?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A@@YAXXZ DD imagerel ??__E?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A@@YAXXZ
	DD	imagerel ??__E?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A@@YAXXZ+34
	DD	imagerel $unwind$??__E?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__F?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A@@YAXXZ DD imagerel ??__F?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A@@YAXXZ
	DD	imagerel ??__F?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A@@YAXXZ+22
	DD	imagerel $unwind$??__F?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A@@YAXXZ
pdata	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
??inst$initializer$@?$ISingleton@VLogAssist@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA DQ FLAT:??__E?inst@?$ISingleton@VLogAssist@mu2@@@mu2@@1VLogAssist@2@A@@YAXXZ ; ??inst$initializer$@?$ISingleton@VLogAssist@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA
CRT$XCU	ENDS
;	COMDAT __real@3f800000
CONST	SEGMENT
__real@3f800000 DD 03f800000r			; 1
CONST	ENDS
;	COMDAT ??_R1A@?0A@EA@?$_Ref_count@VWorldServer@mu2@@@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@?$_Ref_count@VWorldServer@mu2@@@std@@8 DD imagerel ??_R0?AV?$_Ref_count@VWorldServer@mu2@@@std@@@8 ; std::_Ref_count<mu2::WorldServer>::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3?$_Ref_count@VWorldServer@mu2@@@std@@8
rdata$r	ENDS
;	COMDAT ??_R2?$_Ref_count@VWorldServer@mu2@@@std@@8
rdata$r	SEGMENT
??_R2?$_Ref_count@VWorldServer@mu2@@@std@@8 DD imagerel ??_R1A@?0A@EA@?$_Ref_count@VWorldServer@mu2@@@std@@8 ; std::_Ref_count<mu2::WorldServer>::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@_Ref_count_base@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3?$_Ref_count@VWorldServer@mu2@@@std@@8
rdata$r	SEGMENT
??_R3?$_Ref_count@VWorldServer@mu2@@@std@@8 DD 00H	; std::_Ref_count<mu2::WorldServer>::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2?$_Ref_count@VWorldServer@mu2@@@std@@8
rdata$r	ENDS
;	COMDAT ??_R0?AV?$_Ref_count@VWorldServer@mu2@@@std@@@8
data$rs	SEGMENT
??_R0?AV?$_Ref_count@VWorldServer@mu2@@@std@@@8 DQ FLAT:??_7type_info@@6B@ ; std::_Ref_count<mu2::WorldServer> `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AV?$_Ref_count@VWorldServer@mu2@@@std@@', 00H
data$rs	ENDS
;	COMDAT ??_R4?$_Ref_count@VWorldServer@mu2@@@std@@6B@
rdata$r	SEGMENT
??_R4?$_Ref_count@VWorldServer@mu2@@@std@@6B@ DD 01H	; std::_Ref_count<mu2::WorldServer>::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AV?$_Ref_count@VWorldServer@mu2@@@std@@@8
	DD	imagerel ??_R3?$_Ref_count@VWorldServer@mu2@@@std@@8
	DD	imagerel ??_R4?$_Ref_count@VWorldServer@mu2@@@std@@6B@
rdata$r	ENDS
;	COMDAT ??_7?$_Ref_count@VWorldServer@mu2@@@std@@6B@
CONST	SEGMENT
??_7?$_Ref_count@VWorldServer@mu2@@@std@@6B@ DQ FLAT:??_R4?$_Ref_count@VWorldServer@mu2@@@std@@6B@ ; std::_Ref_count<mu2::WorldServer>::`vftable'
	DQ	FLAT:?_Destroy@?$_Ref_count@VWorldServer@mu2@@@std@@EEAAXXZ
	DQ	FLAT:?_Delete_this@?$_Ref_count@VWorldServer@mu2@@@std@@EEAAXXZ
	DQ	FLAT:??_E?$_Ref_count@VWorldServer@mu2@@@std@@UEAAPEAXI@Z
	DQ	FLAT:?_Get_deleter@_Ref_count_base@std@@UEBAPEAXAEBVtype_info@@@Z
CONST	ENDS
;	COMDAT ??_C@_05PDJBBECF@pause@
CONST	SEGMENT
??_C@_05PDJBBECF@pause@ DB 'pause', 00H			; `string'
CONST	ENDS
;	COMDAT ??_C@_05GFKPGDJ@wmain@
CONST	SEGMENT
??_C@_05GFKPGDJ@wmain@ DB 'wmain', 00H			; `string'
CONST	ENDS
;	COMDAT ??_C@_0ED@INKANDI@F?3?2Release_Branch?2Server?2Develo@
CONST	SEGMENT
??_C@_0ED@INKANDI@F?3?2Release_Branch?2Server?2Develo@ DB 'F:\Release_Bra'
	DB	'nch\Server\Development\Frontend\WorldServer\main.cpp', 00H ; `string'
CONST	ENDS
;	COMDAT ??_C@_0BH@FOJBDNAM@Failed?5to?5start?5server@
CONST	SEGMENT
??_C@_0BH@FOJBDNAM@Failed?5to?5start?5server@ DB 'Failed to start server', 00H ; `string'
CONST	ENDS
;	COMDAT ??_R4?$ISingleton@VLogAssist@mu2@@@mu2@@6B@
rdata$r	SEGMENT
??_R4?$ISingleton@VLogAssist@mu2@@@mu2@@6B@ DD 01H	; mu2::ISingleton<mu2::LogAssist>::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AV?$ISingleton@VLogAssist@mu2@@@mu2@@@8
	DD	imagerel ??_R3?$ISingleton@VLogAssist@mu2@@@mu2@@8
	DD	imagerel ??_R4?$ISingleton@VLogAssist@mu2@@@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R2?$ISingleton@VLogAssist@mu2@@@mu2@@8
rdata$r	SEGMENT
??_R2?$ISingleton@VLogAssist@mu2@@@mu2@@8 DD imagerel ??_R1A@?0A@EA@?$ISingleton@VLogAssist@mu2@@@mu2@@8 ; mu2::ISingleton<mu2::LogAssist>::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3?$ISingleton@VLogAssist@mu2@@@mu2@@8
rdata$r	SEGMENT
??_R3?$ISingleton@VLogAssist@mu2@@@mu2@@8 DD 00H	; mu2::ISingleton<mu2::LogAssist>::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2?$ISingleton@VLogAssist@mu2@@@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AV?$ISingleton@VLogAssist@mu2@@@mu2@@@8
data$rs	SEGMENT
??_R0?AV?$ISingleton@VLogAssist@mu2@@@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::ISingleton<mu2::LogAssist> `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AV?$ISingleton@VLogAssist@mu2@@@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R1A@?0A@EA@?$ISingleton@VLogAssist@mu2@@@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@?$ISingleton@VLogAssist@mu2@@@mu2@@8 DD imagerel ??_R0?AV?$ISingleton@VLogAssist@mu2@@@mu2@@@8 ; mu2::ISingleton<mu2::LogAssist>::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3?$ISingleton@VLogAssist@mu2@@@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@LogAssist@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@LogAssist@mu2@@8 DD imagerel ??_R0?AVLogAssist@mu2@@@8 ; mu2::LogAssist::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3LogAssist@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2LogAssist@mu2@@8
rdata$r	SEGMENT
??_R2LogAssist@mu2@@8 DD imagerel ??_R1A@?0A@EA@LogAssist@mu2@@8 ; mu2::LogAssist::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@?$ISingleton@VLogAssist@mu2@@@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3LogAssist@mu2@@8
rdata$r	SEGMENT
??_R3LogAssist@mu2@@8 DD 00H				; mu2::LogAssist::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2LogAssist@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVLogAssist@mu2@@@8
data$rs	SEGMENT
??_R0?AVLogAssist@mu2@@@8 DQ FLAT:??_7type_info@@6B@	; mu2::LogAssist `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVLogAssist@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4LogAssist@mu2@@6B@
rdata$r	SEGMENT
??_R4LogAssist@mu2@@6B@ DD 01H				; mu2::LogAssist::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVLogAssist@mu2@@@8
	DD	imagerel ??_R3LogAssist@mu2@@8
	DD	imagerel ??_R4LogAssist@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@_Ref_count_base@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@_Ref_count_base@std@@8 DD imagerel ??_R0?AV_Ref_count_base@std@@@8 ; std::_Ref_count_base::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3_Ref_count_base@std@@8
rdata$r	ENDS
;	COMDAT ??_R2_Ref_count_base@std@@8
rdata$r	SEGMENT
??_R2_Ref_count_base@std@@8 DD imagerel ??_R1A@?0A@EA@_Ref_count_base@std@@8 ; std::_Ref_count_base::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3_Ref_count_base@std@@8
rdata$r	SEGMENT
??_R3_Ref_count_base@std@@8 DD 00H			; std::_Ref_count_base::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2_Ref_count_base@std@@8
rdata$r	ENDS
;	COMDAT ??_R0?AV_Ref_count_base@std@@@8
data$rs	SEGMENT
??_R0?AV_Ref_count_base@std@@@8 DQ FLAT:??_7type_info@@6B@ ; std::_Ref_count_base `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AV_Ref_count_base@std@@', 00H
data$rs	ENDS
;	COMDAT ??_R4bad_alloc@std@@6B@
rdata$r	SEGMENT
??_R4bad_alloc@std@@6B@ DD 01H				; std::bad_alloc::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVbad_alloc@std@@@8
	DD	imagerel ??_R3bad_alloc@std@@8
	DD	imagerel ??_R4bad_alloc@std@@6B@
rdata$r	ENDS
;	COMDAT ??_R2bad_alloc@std@@8
rdata$r	SEGMENT
??_R2bad_alloc@std@@8 DD imagerel ??_R1A@?0A@EA@bad_alloc@std@@8 ; std::bad_alloc::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@exception@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3bad_alloc@std@@8
rdata$r	SEGMENT
??_R3bad_alloc@std@@8 DD 00H				; std::bad_alloc::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2bad_alloc@std@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@bad_alloc@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@bad_alloc@std@@8 DD imagerel ??_R0?AVbad_alloc@std@@@8 ; std::bad_alloc::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3bad_alloc@std@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@bad_array_new_length@std@@8 DD imagerel ??_R0?AVbad_array_new_length@std@@@8 ; std::bad_array_new_length::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	02H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3bad_array_new_length@std@@8
rdata$r	ENDS
;	COMDAT ??_R2bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R2bad_array_new_length@std@@8 DD imagerel ??_R1A@?0A@EA@bad_array_new_length@std@@8 ; std::bad_array_new_length::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@bad_alloc@std@@8
	DD	imagerel ??_R1A@?0A@EA@exception@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R3bad_array_new_length@std@@8 DD 00H			; std::bad_array_new_length::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	03H
	DD	imagerel ??_R2bad_array_new_length@std@@8
rdata$r	ENDS
;	COMDAT ??_R4bad_array_new_length@std@@6B@
rdata$r	SEGMENT
??_R4bad_array_new_length@std@@6B@ DD 01H		; std::bad_array_new_length::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVbad_array_new_length@std@@@8
	DD	imagerel ??_R3bad_array_new_length@std@@8
	DD	imagerel ??_R4bad_array_new_length@std@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@exception@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@exception@std@@8 DD imagerel ??_R0?AVexception@std@@@8 ; std::exception::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3exception@std@@8
rdata$r	ENDS
;	COMDAT ??_R2exception@std@@8
rdata$r	SEGMENT
??_R2exception@std@@8 DD imagerel ??_R1A@?0A@EA@exception@std@@8 ; std::exception::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3exception@std@@8
rdata$r	SEGMENT
??_R3exception@std@@8 DD 00H				; std::exception::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2exception@std@@8
rdata$r	ENDS
;	COMDAT ??_R4exception@std@@6B@
rdata$r	SEGMENT
??_R4exception@std@@6B@ DD 01H				; std::exception::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVexception@std@@@8
	DD	imagerel ??_R3exception@std@@8
	DD	imagerel ??_R4exception@std@@6B@
rdata$r	ENDS
;	COMDAT ??_7LogAssist@mu2@@6B@
CONST	SEGMENT
??_7LogAssist@mu2@@6B@ DQ FLAT:??_R4LogAssist@mu2@@6B@	; mu2::LogAssist::`vftable'
	DQ	FLAT:?Init@LogAssist@mu2@@UEAA_NPEAX@Z
	DQ	FLAT:?UnInit@LogAssist@mu2@@UEAA_NXZ
	DQ	FLAT:??_ELogAssist@mu2@@UEAAPEAXI@Z
CONST	ENDS
;	COMDAT ??_7?$ISingleton@VLogAssist@mu2@@@mu2@@6B@
CONST	SEGMENT
??_7?$ISingleton@VLogAssist@mu2@@@mu2@@6B@ DQ FLAT:??_R4?$ISingleton@VLogAssist@mu2@@@mu2@@6B@ ; mu2::ISingleton<mu2::LogAssist>::`vftable'
	DQ	FLAT:_purecall
	DQ	FLAT:_purecall
	DQ	FLAT:??_E?$ISingleton@VLogAssist@mu2@@@mu2@@UEAAPEAXI@Z
CONST	ENDS
;	COMDAT ??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
CONST	SEGMENT
??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@ DB 'g', 00H, 'a', 00H, 'm', 00H, 'e'
	DB	00H, 00H, 00H				; `string'
CONST	ENDS
;	COMDAT ??_C@_0BA@JFNIOLAK@string?5too?5long@
CONST	SEGMENT
??_C@_0BA@JFNIOLAK@string?5too?5long@ DB 'string too long', 00H ; `string'
CONST	ENDS
;	COMDAT _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 DD 010H
	DD	imagerel ??_R0?AVbad_alloc@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0bad_alloc@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVbad_alloc@std@@@8
data$r	SEGMENT
??_R0?AVbad_alloc@std@@@8 DQ FLAT:??_7type_info@@6B@	; std::bad_alloc `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVbad_alloc@std@@', 00H
data$r	ENDS
;	COMDAT _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 DD 00H
	DD	imagerel ??_R0?AVbad_array_new_length@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0bad_array_new_length@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVbad_array_new_length@std@@@8
data$r	SEGMENT
??_R0?AVbad_array_new_length@std@@@8 DQ FLAT:??_7type_info@@6B@ ; std::bad_array_new_length `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVbad_array_new_length@std@@', 00H
data$r	ENDS
;	COMDAT _CTA3?AVbad_array_new_length@std@@
xdata$x	SEGMENT
_CTA3?AVbad_array_new_length@std@@ DD 03H
	DD	imagerel _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
	DD	imagerel _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
	DD	imagerel _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
xdata$x	ENDS
;	COMDAT _TI3?AVbad_array_new_length@std@@
xdata$x	SEGMENT
_TI3?AVbad_array_new_length@std@@ DD 00H
	DD	imagerel ??1bad_array_new_length@std@@UEAA@XZ
	DD	00H
	DD	imagerel _CTA3?AVbad_array_new_length@std@@
xdata$x	ENDS
;	COMDAT _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 DD 00H
	DD	imagerel ??_R0?AVexception@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0exception@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVexception@std@@@8
data$r	SEGMENT
??_R0?AVexception@std@@@8 DQ FLAT:??_7type_info@@6B@	; std::exception `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVexception@std@@', 00H
data$r	ENDS
;	COMDAT ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@
CONST	SEGMENT
??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ DB 'bad array new length', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7bad_array_new_length@std@@6B@
CONST	SEGMENT
??_7bad_array_new_length@std@@6B@ DQ FLAT:??_R4bad_array_new_length@std@@6B@ ; std::bad_array_new_length::`vftable'
	DQ	FLAT:??_Ebad_array_new_length@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT ??_7bad_alloc@std@@6B@
CONST	SEGMENT
??_7bad_alloc@std@@6B@ DQ FLAT:??_R4bad_alloc@std@@6B@	; std::bad_alloc::`vftable'
	DQ	FLAT:??_Ebad_alloc@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT ??_C@_0BC@EOODALEL@Unknown?5exception@
CONST	SEGMENT
??_C@_0BC@EOODALEL@Unknown?5exception@ DB 'Unknown exception', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7exception@std@@6B@
CONST	SEGMENT
??_7exception@std@@6B@ DQ FLAT:??_R4exception@std@@6B@	; std::exception::`vftable'
	DQ	FLAT:??_Eexception@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__F?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__E?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_G?$_Ref_count@VWorldServer@mu2@@@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Delete_this@?$_Ref_count@VWorldServer@mu2@@@std@@EEAAXXZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Destroy@?$_Ref_count@VWorldServer@mu2@@@std@@EEAAXXZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$_Temporary_owner@VWorldServer@mu2@@@std@@QEAA@XZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???$?0VWorldServer@mu2@@$0A@@?$shared_ptr@VWorldServer@mu2@@@std@@QEAA@PEAVWorldServer@mu2@@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$?0VWorldServer@mu2@@$0A@@?$shared_ptr@VWorldServer@mu2@@@std@@QEAA@PEAVWorldServer@mu2@@@Z DB 06H
	DB	00H
	DB	00H
	DB	'v'
	DB	02H
	DB	05H, 03H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$?0VWorldServer@mu2@@$0A@@?$shared_ptr@VWorldServer@mu2@@@std@@QEAA@PEAVWorldServer@mu2@@@Z DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???$?0VWorldServer@mu2@@$0A@@?$shared_ptr@VWorldServer@mu2@@@std@@QEAA@PEAVWorldServer@mu2@@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$?0VWorldServer@mu2@@$0A@@?$shared_ptr@VWorldServer@mu2@@@std@@QEAA@PEAVWorldServer@mu2@@@Z DB 028H
	DD	imagerel $stateUnwindMap$??$?0VWorldServer@mu2@@$0A@@?$shared_ptr@VWorldServer@mu2@@@std@@QEAA@PEAVWorldServer@mu2@@@Z
	DD	imagerel $ip2state$??$?0VWorldServer@mu2@@$0A@@?$shared_ptr@VWorldServer@mu2@@@std@@QEAA@PEAVWorldServer@mu2@@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$?0VWorldServer@mu2@@$0A@@?$shared_ptr@VWorldServer@mu2@@@std@@QEAA@PEAVWorldServer@mu2@@@Z DD 020f11H
	DD	0700bd20fH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$?0VWorldServer@mu2@@$0A@@?$shared_ptr@VWorldServer@mu2@@@std@@QEAA@PEAVWorldServer@mu2@@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$shared_ptr@VWorldServer@mu2@@@std@@QEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$wmain$dtor$3 DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$wmain$dtor$2 DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$wmain$dtor$0 DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$wmain DB 012H
	DB	00H
	DB	00H
	DB	'~'
	DB	02H
	DB	0e2H
	DB	00H
	DB	0dH, 02H
	DB	04H
	DB	'^'
	DB	00H
	DB	'&'
	DB	06H
	DB	01dH, 02H
	DB	00H
	DB	'8'
	DB	06H
	DB	05H, 03H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$wmain DB 06H
	DB	0eH
	DD	imagerel wmain$dtor$0
	DB	036H
	DD	imagerel wmain$dtor$2
	DB	05eH
	DD	imagerel wmain$dtor$3
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$wmain DB 028H
	DD	imagerel $stateUnwindMap$wmain
	DD	imagerel $ip2state$wmain
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$wmain DD 021011H
	DD	0290110H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$wmain
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$uninitialized_fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@0@0AEBV10@@Z DD 011301H
	DD	0e213H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z DD 010901H
	DD	0a209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?_Bump_erased@_Range_eraser@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAXXZ DB 06H
	DB	00H
	DB	00H
	DB	0deH
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?_Bump_erased@_Range_eraser@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAXXZ DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?_Bump_erased@_Range_eraser@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAXXZ DB 068H
	DD	imagerel $stateUnwindMap$?_Bump_erased@_Range_eraser@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAXXZ
	DD	imagerel $ip2state$?_Bump_erased@_Range_eraser@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Bump_erased@_Range_eraser@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAXXZ DD 010919H
	DD	0c209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?_Bump_erased@_Range_eraser@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$_Free_non_head@V?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@SAXAEAV?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU01@@Z DB 06H
	DB	00H
	DB	00H
	DB	0e4H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$_Free_non_head@V?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@SAXAEAV?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU01@@Z DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$_Free_non_head@V?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@SAXAEAV?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU01@@Z DB 068H
	DD	imagerel $stateUnwindMap$??$_Free_non_head@V?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@SAXAEAV?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU01@@Z
	DD	imagerel $ip2state$??$_Free_non_head@V?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@SAXAEAV?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU01@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Free_non_head@V?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@SAXAEAV?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU01@@Z DD 010e19H
	DD	0a20eH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$_Free_non_head@V?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@SAXAEAV?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU01@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__F?inst@?$ISingleton@VLogAssist@mu2@@@mu2@@1VLogAssist@2@A@@YAXXZ DD 010401H
	DD	06204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__E?inst@?$ISingleton@VLogAssist@mu2@@@mu2@@1VLogAssist@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Construct@$00PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z DD 031701H
	DD	0200117H
	DD	07010H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Fnv1a_append_value@I@std@@YA_K_KAEBI@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GLogAssist@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0620dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???0LogAssist@mu2@@QEAA@XZ@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??0LogAssist@mu2@@QEAA@XZ DB 06H
	DB	00H
	DB	00H
	DB	'0'
	DB	02H
	DB	'B'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??0LogAssist@mu2@@QEAA@XZ DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???0LogAssist@mu2@@QEAA@XZ@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??0LogAssist@mu2@@QEAA@XZ DB 028H
	DD	imagerel $stateUnwindMap$??0LogAssist@mu2@@QEAA@XZ
	DD	imagerel $ip2state$??0LogAssist@mu2@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0LogAssist@mu2@@QEAA@XZ DD 010911H
	DD	04209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??0LogAssist@mu2@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0?$unordered_map@IIU?$hash@I@std@@U?$equal_to@I@2@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ DD 010901H
	DD	0e209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?_Tidy@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAXXZ DB 06H
	DB	00H
	DB	00H
	DB	0feH
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?_Tidy@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAXXZ DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?_Tidy@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAXXZ DB 068H
	DD	imagerel $stateUnwindMap$?_Tidy@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAXXZ
	DD	imagerel $ip2state$?_Tidy@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Tidy@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAXXZ DD 010919H
	DD	0c209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?_Tidy@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@2@@Z DB 06H
	DB	00H
	DB	00H
	DB	'!', 04H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@2@@Z DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@2@@Z DB 028H
	DD	imagerel $stateUnwindMap$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@2@@Z
	DD	imagerel $ip2state$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@2@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@2@@Z DD 021611H
	DD	0190116H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@2@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?allocate@?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@QEAAPEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@2@_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?clear@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAXXZ DD 020c01H
	DD	011010cH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Unchecked_erase@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@AEAAPEAU?$_List_node@U?$pair@$$CBII@std@@PEAX@2@PEAU32@QEAU32@@Z DD 021601H
	DD	0230116H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$1@?0???0?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA@AEBV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@1@AEBV?$allocator@U?$pair@$$CBII@std@@@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???0?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA@AEBV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@1@AEBV?$allocator@U?$pair@$$CBII@std@@@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??0?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA@AEBV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@1@AEBV?$allocator@U?$pair@$$CBII@std@@@1@@Z DB 08H
	DB	00H
	DB	00H
	DB	'9', 02H
	DB	02H
	DB	0ecH
	DB	04H
	DB	'm', 02H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??0?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA@AEBV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@1@AEBV?$allocator@U?$pair@$$CBII@std@@@1@@Z DB 04H
	DB	0eH
	DD	imagerel ?dtor$0@?0???0?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA@AEBV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@1@AEBV?$allocator@U?$pair@$$CBII@std@@@1@@Z@4HA
	DB	02eH
	DD	imagerel ?dtor$1@?0???0?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA@AEBV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@1@AEBV?$allocator@U?$pair@$$CBII@std@@@1@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??0?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA@AEBV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@1@AEBV?$allocator@U?$pair@$$CBII@std@@@1@@Z DB 028H
	DD	imagerel $stateUnwindMap$??0?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA@AEBV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@1@AEBV?$allocator@U?$pair@$$CBII@std@@@1@@Z
	DD	imagerel $ip2state$??0?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA@AEBV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@1@AEBV?$allocator@U?$pair@$$CBII@std@@@1@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA@AEBV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@1@AEBV?$allocator@U?$pair@$$CBII@std@@@1@@Z DD 031711H
	DD	0140117H
	DD	07010H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??0?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA@AEBV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@1@AEBV?$allocator@U?$pair@$$CBII@std@@@1@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Alloc_sentinel_and_proxy@?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@AEAAXXZ DD 030d01H
	DD	014010dH
	DD	07006H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?_Tidy@?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@AEAAXXZ DB 06H
	DB	00H
	DB	00H
	DB	0d0H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?_Tidy@?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@AEAAXXZ DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?_Tidy@?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@AEAAXXZ DB 068H
	DD	imagerel $stateUnwindMap$?_Tidy@?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@AEAAXXZ
	DD	imagerel $ip2state$?_Tidy@?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@AEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Tidy@?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@AEAAXXZ DD 010919H
	DD	0c209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?_Tidy@?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@AEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?clear@?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAAXXZ DD 010901H
	DD	08209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?allocate@?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@QEAAPEAU?$_List_node@U?$pair@$$CBII@std@@PEAX@2@_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?UnInit@LogAssist@mu2@@UEAA_NXZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_G?$ISingleton@VLogAssist@mu2@@@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Hash_representation@I@std@@YA_KAEBI@Z DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DB	017H
	DB	040H
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Decref@_Ref_count_base@std@@QEAAXXZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ DD 020c01H
	DD	011010cH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z DB 06H
	DB	00H
	DB	00H
	DB	0b4H
	DB	02H
	DB	'b'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z DB 028H
	DD	imagerel $stateUnwindMap$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z
	DD	imagerel $ip2state$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z DD 020f11H
	DD	0700b920fH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Xlen_string@std@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Throw_bad_array_new_length@std@@YAXXZ DD 010401H
	DD	08204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1bad_array_new_length@std@@UEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_array_new_length@std@@QEAA@XZ DD 010601H
	DD	07006H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z DD 020f01H
	DD	0700b320fH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gexception@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?what@exception@std@@UEBAPEBDXZ DD 010901H
	DD	02209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0exception@std@@QEAA@AEBV01@@Z DD 020f01H
	DD	0700b320fH
xdata	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
??inst$initializer$@?$ISingleton@VLogger@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA DQ FLAT:??__E?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A@@YAXXZ ; ??inst$initializer$@?$ISingleton@VLogger@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA
CRT$XCU	ENDS
; Function compile flags: /Odtp
;	COMDAT ??__F?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A@@YAXXZ
text$yd	SEGMENT
??__F?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A@@YAXXZ PROC ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::Logger>::inst'', COMDAT
  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A ; mu2::ISingleton<mu2::Logger>::inst
  0000b	e8 00 00 00 00	 call	 ??1Logger@mu2@@UEAA@XZ	; mu2::Logger::~Logger
  00010	90		 npad	 1
  00011	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00015	c3		 ret	 0
??__F?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A@@YAXXZ ENDP ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::Logger>::inst''
text$yd	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??__E?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A@@YAXXZ
text$di	SEGMENT
??__E?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A@@YAXXZ PROC ; `dynamic initializer for 'mu2::ISingleton<mu2::Logger>::inst'', COMDAT

; 90   : template<typename T> T ISingleton<T>::inst;

  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A ; mu2::ISingleton<mu2::Logger>::inst
  0000b	e8 00 00 00 00	 call	 ??0Logger@mu2@@AEAA@XZ	; mu2::Logger::Logger
  00010	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??__F?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A@@YAXXZ ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::Logger>::inst''
  00017	e8 00 00 00 00	 call	 atexit
  0001c	90		 npad	 1
  0001d	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00021	c3		 ret	 0
??__E?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A@@YAXXZ ENDP ; `dynamic initializer for 'mu2::ISingleton<mu2::Logger>::inst''
text$di	ENDS
; Function compile flags: /Odtp
;	COMDAT ??_G?$_Ref_count@VWorldServer@mu2@@@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_G?$_Ref_count@VWorldServer@mu2@@@std@@UEAAPEAXI@Z PROC ; std::_Ref_count<mu2::WorldServer>::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000d	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00011	83 e0 01	 and	 eax, 1
  00014	85 c0		 test	 eax, eax
  00016	74 10		 je	 SHORT $LN2@scalar
  00018	ba 18 00 00 00	 mov	 edx, 24
  0001d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00022	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00027	90		 npad	 1
$LN2@scalar:
  00028	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0002d	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00031	c3		 ret	 0
??_G?$_Ref_count@VWorldServer@mu2@@@std@@UEAAPEAXI@Z ENDP ; std::_Ref_count<mu2::WorldServer>::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ?_Delete_this@?$_Ref_count@VWorldServer@mu2@@@std@@EEAAXXZ
_TEXT	SEGMENT
$T1 = 32
tv74 = 40
this$ = 64
?_Delete_this@?$_Ref_count@VWorldServer@mu2@@@std@@EEAAXXZ PROC ; std::_Ref_count<mu2::WorldServer>::_Delete_this, COMDAT

; 1190 :     void _Delete_this() noexcept override { // destroy self

$LN6:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 1191 :         delete this;

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
  00013	48 83 7c 24 20
	00		 cmp	 QWORD PTR $T1[rsp], 0
  00019	74 1c		 je	 SHORT $LN3@Delete_thi
  0001b	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00020	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00023	ba 01 00 00 00	 mov	 edx, 1
  00028	48 8b 4c 24 20	 mov	 rcx, QWORD PTR $T1[rsp]
  0002d	ff 50 10	 call	 QWORD PTR [rax+16]
  00030	48 89 44 24 28	 mov	 QWORD PTR tv74[rsp], rax
  00035	eb 09		 jmp	 SHORT $LN4@Delete_thi
$LN3@Delete_thi:
  00037	48 c7 44 24 28
	00 00 00 00	 mov	 QWORD PTR tv74[rsp], 0
$LN4@Delete_thi:

; 1192 :     }

  00040	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00044	c3		 ret	 0
?_Delete_this@?$_Ref_count@VWorldServer@mu2@@@std@@EEAAXXZ ENDP ; std::_Ref_count<mu2::WorldServer>::_Delete_this
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ?_Destroy@?$_Ref_count@VWorldServer@mu2@@@std@@EEAAXXZ
_TEXT	SEGMENT
$T1 = 32
tv75 = 40
this$ = 64
?_Destroy@?$_Ref_count@VWorldServer@mu2@@@std@@EEAAXXZ PROC ; std::_Ref_count<mu2::WorldServer>::_Destroy, COMDAT

; 1186 :     void _Destroy() noexcept override { // destroy managed resource

$LN6:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 1187 :         delete _Ptr;

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00012	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
  00017	48 83 7c 24 20
	00		 cmp	 QWORD PTR $T1[rsp], 0
  0001d	74 1b		 je	 SHORT $LN3@Destroy
  0001f	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00024	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00027	ba 01 00 00 00	 mov	 edx, 1
  0002c	48 8b 4c 24 20	 mov	 rcx, QWORD PTR $T1[rsp]
  00031	ff 10		 call	 QWORD PTR [rax]
  00033	48 89 44 24 28	 mov	 QWORD PTR tv75[rsp], rax
  00038	eb 09		 jmp	 SHORT $LN4@Destroy
$LN3@Destroy:
  0003a	48 c7 44 24 28
	00 00 00 00	 mov	 QWORD PTR tv75[rsp], 0
$LN4@Destroy:

; 1188 :     }

  00043	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00047	c3		 ret	 0
?_Destroy@?$_Ref_count@VWorldServer@mu2@@@std@@EEAAXXZ ENDP ; std::_Ref_count<mu2::WorldServer>::_Destroy
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ??1?$_Temporary_owner@VWorldServer@mu2@@@std@@QEAA@XZ
_TEXT	SEGMENT
$T1 = 32
tv75 = 40
this$ = 64
??1?$_Temporary_owner@VWorldServer@mu2@@@std@@QEAA@XZ PROC ; std::_Temporary_owner<mu2::WorldServer>::~_Temporary_owner<mu2::WorldServer>, COMDAT

; 1529 :     ~_Temporary_owner() {

$LN6:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 1530 :         delete _Ptr;

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00011	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
  00016	48 83 7c 24 20
	00		 cmp	 QWORD PTR $T1[rsp], 0
  0001c	74 1b		 je	 SHORT $LN3@Temporary_
  0001e	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00023	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00026	ba 01 00 00 00	 mov	 edx, 1
  0002b	48 8b 4c 24 20	 mov	 rcx, QWORD PTR $T1[rsp]
  00030	ff 10		 call	 QWORD PTR [rax]
  00032	48 89 44 24 28	 mov	 QWORD PTR tv75[rsp], rax
  00037	eb 09		 jmp	 SHORT $LN4@Temporary_
$LN3@Temporary_:
  00039	48 c7 44 24 28
	00 00 00 00	 mov	 QWORD PTR tv75[rsp], 0
$LN4@Temporary_:

; 1531 :     }

  00042	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00046	c3		 ret	 0
??1?$_Temporary_owner@VWorldServer@mu2@@@std@@QEAA@XZ ENDP ; std::_Temporary_owner<mu2::WorldServer>::~_Temporary_owner<mu2::WorldServer>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ??$?0VWorldServer@mu2@@$0A@@?$shared_ptr@VWorldServer@mu2@@@std@@QEAA@PEAVWorldServer@mu2@@@Z
_TEXT	SEGMENT
$T1 = 32
_Owner$2 = 40
$T3 = 48
tv89 = 56
_Px$ = 64
$T4 = 72
_Px$ = 80
$T5 = 88
tv170 = 96
this$ = 128
_Px$ = 136
??$?0VWorldServer@mu2@@$0A@@?$shared_ptr@VWorldServer@mu2@@@std@@QEAA@PEAVWorldServer@mu2@@@Z PROC ; std::shared_ptr<mu2::WorldServer>::shared_ptr<mu2::WorldServer><mu2::WorldServer,0>, COMDAT

; 1584 :     explicit shared_ptr(_Ux* _Px) { // construct shared_ptr object that owns _Px

$LN37:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 70	 sub	 rsp, 112		; 00000070H

; 1452 :     element_type* _Ptr{nullptr};

  0000f	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00017	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0

; 1453 :     _Ref_count_base* _Rep{nullptr};

  0001e	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00026	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0

; 1526 :     explicit _Temporary_owner(_Ux* const _Ptr_) noexcept : _Ptr(_Ptr_) {}

  0002e	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR _Px$[rsp]
  00036	48 89 44 24 28	 mov	 QWORD PTR _Owner$2[rsp], rax

; 1585 :         if constexpr (is_array_v<_Ty>) {
; 1586 :             _Setpd(_Px, default_delete<_Ux[]>{});
; 1587 :         } else {
; 1588 :             _Temporary_owner<_Ux> _Owner(_Px);
; 1589 :             _Set_ptr_rep_and_enable_shared(_Owner._Ptr, new _Ref_count<_Ux>(_Owner._Ptr));

  0003b	b9 18 00 00 00	 mov	 ecx, 24
  00040	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00045	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
  0004a	48 83 7c 24 20
	00		 cmp	 QWORD PTR $T1[rsp], 0
  00050	74 63		 je	 SHORT $LN3@WorldServe
  00052	48 8b 44 24 28	 mov	 rax, QWORD PTR _Owner$2[rsp]
  00057	48 89 44 24 40	 mov	 QWORD PTR _Px$[rsp], rax

; 1183 :     explicit _Ref_count(_Ty* _Px) : _Ref_count_base(), _Ptr(_Px) {}

  0005c	48 8b 7c 24 20	 mov	 rdi, QWORD PTR $T1[rsp]
  00061	33 c0		 xor	 eax, eax
  00063	b9 10 00 00 00	 mov	 ecx, 16
  00068	f3 aa		 rep stosb

; 1119 :     _Atomic_counter_t _Uses  = 1;

  0006a	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  0006f	c7 40 08 01 00
	00 00		 mov	 DWORD PTR [rax+8], 1

; 1120 :     _Atomic_counter_t _Weaks = 1;

  00076	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  0007b	c7 40 0c 01 00
	00 00		 mov	 DWORD PTR [rax+12], 1

; 1183 :     explicit _Ref_count(_Ty* _Px) : _Ref_count_base(), _Ptr(_Px) {}

  00082	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00087	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$_Ref_count@VWorldServer@mu2@@@std@@6B@
  0008e	48 89 08	 mov	 QWORD PTR [rax], rcx
  00091	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00096	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Px$[rsp]
  0009b	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx
  0009f	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  000a4	48 89 44 24 48	 mov	 QWORD PTR $T4[rsp], rax

; 1585 :         if constexpr (is_array_v<_Ty>) {
; 1586 :             _Setpd(_Px, default_delete<_Ux[]>{});
; 1587 :         } else {
; 1588 :             _Temporary_owner<_Ux> _Owner(_Px);
; 1589 :             _Set_ptr_rep_and_enable_shared(_Owner._Ptr, new _Ref_count<_Ux>(_Owner._Ptr));

  000a9	48 8b 44 24 48	 mov	 rax, QWORD PTR $T4[rsp]
  000ae	48 89 44 24 38	 mov	 QWORD PTR tv89[rsp], rax
  000b3	eb 09		 jmp	 SHORT $LN4@WorldServe
$LN3@WorldServe:
  000b5	48 c7 44 24 38
	00 00 00 00	 mov	 QWORD PTR tv89[rsp], 0
$LN4@WorldServe:
  000be	48 8b 44 24 38	 mov	 rax, QWORD PTR tv89[rsp]
  000c3	48 89 44 24 58	 mov	 QWORD PTR $T5[rsp], rax
  000c8	48 8b 44 24 28	 mov	 rax, QWORD PTR _Owner$2[rsp]
  000cd	48 89 44 24 50	 mov	 QWORD PTR _Px$[rsp], rax

; 1856 :         this->_Ptr = _Px;

  000d2	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000da	48 8b 4c 24 50	 mov	 rcx, QWORD PTR _Px$[rsp]
  000df	48 89 08	 mov	 QWORD PTR [rax], rcx

; 1857 :         this->_Rep = _Rx;

  000e2	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000ea	48 8b 4c 24 58	 mov	 rcx, QWORD PTR $T5[rsp]
  000ef	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 1590 :             _Owner._Ptr = nullptr;

  000f3	48 c7 44 24 28
	00 00 00 00	 mov	 QWORD PTR _Owner$2[rsp], 0

; 1530 :         delete _Ptr;

  000fc	48 8b 44 24 28	 mov	 rax, QWORD PTR _Owner$2[rsp]
  00101	48 89 44 24 30	 mov	 QWORD PTR $T3[rsp], rax
  00106	48 83 7c 24 30
	00		 cmp	 QWORD PTR $T3[rsp], 0
  0010c	74 1b		 je	 SHORT $LN32@WorldServe
  0010e	48 8b 44 24 30	 mov	 rax, QWORD PTR $T3[rsp]
  00113	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00116	ba 01 00 00 00	 mov	 edx, 1
  0011b	48 8b 4c 24 30	 mov	 rcx, QWORD PTR $T3[rsp]
  00120	ff 10		 call	 QWORD PTR [rax]
  00122	48 89 44 24 60	 mov	 QWORD PTR tv170[rsp], rax
  00127	eb 09		 jmp	 SHORT $LN33@WorldServe
$LN32@WorldServe:
  00129	48 c7 44 24 60
	00 00 00 00	 mov	 QWORD PTR tv170[rsp], 0
$LN33@WorldServe:

; 1591 :         }
; 1592 :     }

  00132	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0013a	48 83 c4 70	 add	 rsp, 112		; 00000070H
  0013e	5f		 pop	 rdi
  0013f	c3		 ret	 0
??$?0VWorldServer@mu2@@$0A@@?$shared_ptr@VWorldServer@mu2@@@std@@QEAA@PEAVWorldServer@mu2@@@Z ENDP ; std::shared_ptr<mu2::WorldServer>::shared_ptr<mu2::WorldServer><mu2::WorldServer,0>
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
_Owner$2 = 40
$T3 = 48
tv89 = 56
_Px$ = 64
$T4 = 72
_Px$ = 80
$T5 = 88
tv170 = 96
this$ = 128
_Px$ = 136
?dtor$0@?0???$?0VWorldServer@mu2@@$0A@@?$shared_ptr@VWorldServer@mu2@@@std@@QEAA@PEAVWorldServer@mu2@@@Z@4HA PROC ; `std::shared_ptr<mu2::WorldServer>::shared_ptr<mu2::WorldServer><mu2::WorldServer,0>'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8d 4d 28	 lea	 rcx, QWORD PTR _Owner$2[rbp]
  0000d	e8 00 00 00 00	 call	 ??1?$_Temporary_owner@VWorldServer@mu2@@@std@@QEAA@XZ ; std::_Temporary_owner<mu2::WorldServer>::~_Temporary_owner<mu2::WorldServer>
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$0@?0???$?0VWorldServer@mu2@@$0A@@?$shared_ptr@VWorldServer@mu2@@@std@@QEAA@PEAVWorldServer@mu2@@@Z@4HA ENDP ; `std::shared_ptr<mu2::WorldServer>::shared_ptr<mu2::WorldServer><mu2::WorldServer,0>'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ??1?$shared_ptr@VWorldServer@mu2@@@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1?$shared_ptr@VWorldServer@mu2@@@std@@QEAA@XZ PROC	; std::shared_ptr<mu2::WorldServer>::~shared_ptr<mu2::WorldServer>, COMDAT

; 1689 :     ~shared_ptr() noexcept { // release resource

$LN22:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 1384 :         if (_Rep) {

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00013	74 0f		 je	 SHORT $LN5@shared_ptr

; 1385 :             _Rep->_Decref();

  00015	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 8b 48 08	 mov	 rcx, QWORD PTR [rax+8]
  0001e	e8 00 00 00 00	 call	 ?_Decref@_Ref_count_base@std@@QEAAXXZ ; std::_Ref_count_base::_Decref
  00023	90		 npad	 1
$LN5@shared_ptr:

; 1690 :         this->_Decref();
; 1691 :     }

  00024	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00028	c3		 ret	 0
??1?$shared_ptr@VWorldServer@mu2@@@std@@QEAA@XZ ENDP	; std::shared_ptr<mu2::WorldServer>::~shared_ptr<mu2::WorldServer>
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\main.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\main.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\main.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\main.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\main.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\main.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\main.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\main.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\main.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\main.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\main.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\main.cpp
;	COMDAT wmain
_TEXT	SEGMENT
server$ = 64
$T1 = 80
$T2 = 84
$T3 = 88
tv130 = 96
tv70 = 104
$T4 = 112
tv142 = 120
tv182 = 128
$T5 = 136
$T6 = 144
$T7 = 152
tv195 = 160
$T8 = 168
$T9 = 176
$T10 = 184
$T11 = 192
$T12 = 200
$T13 = 208
$T14 = 216
$T15 = 224
$T16 = 232
$T17 = 240
$T18 = 248
$T19 = 256
$T20 = 264
$T21 = 272
$T22 = 280
$T23 = 288
argc$ = 336
argv$ = 344
wmain	PROC						; COMDAT

; 8    : {

$LN159:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	89 4c 24 08	 mov	 DWORD PTR [rsp+8], ecx
  00009	48 81 ec 48 01
	00 00		 sub	 rsp, 328		; 00000148H
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h

; 26   : 			return malloc(size);

  00010	b9 68 00 00 00	 mov	 ecx, 104		; 00000068H
  00015	e8 00 00 00 00	 call	 malloc
  0001a	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T5[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h

; 26   : 		return Alloc::allocate(size);

  00022	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T5[rsp]
  0002a	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T6[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\main.cpp

; 17   : 	Logger::GetInstance().Init(new MonitorWorld(argv[0]));

  00032	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR $T6[rsp]
  0003a	48 89 44 24 58	 mov	 QWORD PTR $T3[rsp], rax
  0003f	48 83 7c 24 58
	00		 cmp	 QWORD PTR $T3[rsp], 0
  00045	74 53		 je	 SHORT $LN4@wmain
  00047	48 8d 84 24 20
	01 00 00	 lea	 rax, QWORD PTR $T23[rsp]
  0004f	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR $T7[rsp], rax
  00057	b8 08 00 00 00	 mov	 eax, 8
  0005c	48 6b c0 00	 imul	 rax, rax, 0
  00060	48 8b 8c 24 58
	01 00 00	 mov	 rcx, QWORD PTR argv$[rsp]
  00068	48 8b 14 01	 mov	 rdx, QWORD PTR [rcx+rax]
  0006c	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR $T7[rsp]
  00074	e8 00 00 00 00	 call	 ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  00079	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR tv195[rsp], rax
  00081	48 8b 94 24 a0
	00 00 00	 mov	 rdx, QWORD PTR tv195[rsp]
  00089	48 8b 4c 24 58	 mov	 rcx, QWORD PTR $T3[rsp]
  0008e	e8 00 00 00 00	 call	 ??0MonitorWorld@mu2@@QEAA@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z ; mu2::MonitorWorld::MonitorWorld
  00093	48 89 44 24 60	 mov	 QWORD PTR tv130[rsp], rax
  00098	eb 09		 jmp	 SHORT $LN5@wmain
$LN4@wmain:
  0009a	48 c7 44 24 60
	00 00 00 00	 mov	 QWORD PTR tv130[rsp], 0
$LN5@wmain:
  000a3	48 8b 44 24 60	 mov	 rax, QWORD PTR tv130[rsp]
  000a8	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  000b0	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A ; mu2::ISingleton<mu2::Logger>::inst
  000b7	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T8[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\main.cpp

; 17   : 	Logger::GetInstance().Init(new MonitorWorld(argv[0]));

  000bf	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T8[rsp]
  000c7	48 89 44 24 68	 mov	 QWORD PTR tv70[rsp], rax
  000cc	48 8b 44 24 68	 mov	 rax, QWORD PTR tv70[rsp]
  000d1	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000d4	48 8b 94 24 b0
	00 00 00	 mov	 rdx, QWORD PTR $T9[rsp]
  000dc	48 8b 4c 24 68	 mov	 rcx, QWORD PTR tv70[rsp]
  000e1	ff 10		 call	 QWORD PTR [rax]
  000e3	90		 npad	 1
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  000e4	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VLogAssist@mu2@@@mu2@@1VLogAssist@2@A ; mu2::ISingleton<mu2::LogAssist>::inst
  000eb	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\main.cpp

; 18   : 	LogAssist::GetInstance().LogSetupFlag();

  000f3	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  000fb	48 8b c8	 mov	 rcx, rax
  000fe	e8 00 00 00 00	 call	 ?LogSetupFlag@LogAssist@mu2@@QEAAXXZ ; mu2::LogAssist::LogSetupFlag
  00103	90		 npad	 1
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h

; 26   : 			return malloc(size);

  00104	b9 e0 07 00 00	 mov	 ecx, 2016		; 000007e0H
  00109	e8 00 00 00 00	 call	 malloc
  0010e	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h

; 26   : 		return Alloc::allocate(size);

  00116	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  0011e	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\main.cpp

; 20   : 	std::shared_ptr<WorldServer> server(new WorldServer);

  00126	48 8b 84 24 c8
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
  0012e	48 89 44 24 70	 mov	 QWORD PTR $T4[rsp], rax
  00133	48 83 7c 24 70
	00		 cmp	 QWORD PTR $T4[rsp], 0
  00139	74 11		 je	 SHORT $LN6@wmain
  0013b	48 8b 4c 24 70	 mov	 rcx, QWORD PTR $T4[rsp]
  00140	e8 00 00 00 00	 call	 ??0WorldServer@mu2@@QEAA@XZ ; mu2::WorldServer::WorldServer
  00145	48 89 44 24 78	 mov	 QWORD PTR tv142[rsp], rax
  0014a	eb 09		 jmp	 SHORT $LN7@wmain
$LN6@wmain:
  0014c	48 c7 44 24 78
	00 00 00 00	 mov	 QWORD PTR tv142[rsp], 0
$LN7@wmain:
  00155	48 8b 44 24 78	 mov	 rax, QWORD PTR tv142[rsp]
  0015a	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax
  00162	48 8b 94 24 d0
	00 00 00	 mov	 rdx, QWORD PTR $T13[rsp]
  0016a	48 8d 4c 24 40	 lea	 rcx, QWORD PTR server$[rsp]
  0016f	e8 00 00 00 00	 call	 ??$?0VWorldServer@mu2@@$0A@@?$shared_ptr@VWorldServer@mu2@@@std@@QEAA@PEAVWorldServer@mu2@@@Z ; std::shared_ptr<mu2::WorldServer>::shared_ptr<mu2::WorldServer><mu2::WorldServer,0>
  00174	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1309 :         return _Ptr;

  00175	48 8b 44 24 40	 mov	 rax, QWORD PTR server$[rsp]
  0017a	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR $T14[rsp], rax

; 1773 :         return get();

  00182	48 8b 84 24 d8
	00 00 00	 mov	 rax, QWORD PTR $T14[rsp]
  0018a	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR $T15[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\main.cpp

; 22   : 	if (!server->Initialize())

  00192	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR $T15[rsp]
  0019a	48 8b c8	 mov	 rcx, rax
  0019d	e8 00 00 00 00	 call	 ?Initialize@Server@mu2@@QEAA_NXZ ; mu2::Server::Initialize
  001a2	0f b6 c0	 movzx	 eax, al
  001a5	85 c0		 test	 eax, eax
  001a7	75 6f		 jne	 SHORT $LN2@wmain

; 23   : 	{
; 24   : 		MU2_WARN_LOG(core::LogCategory::SYSTEM, "Failed to start server");

  001a9	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BH@FOJBDNAM@Failed?5to?5start?5server@
  001b0	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  001b5	c7 44 24 28 18
	00 00 00	 mov	 DWORD PTR [rsp+40], 24
  001bd	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0ED@INKANDI@F?3?2Release_Branch?2Server?2Develo@
  001c4	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  001c9	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_05GFKPGDJ@wmain@
  001d0	41 b8 30 75 00
	00		 mov	 r8d, 30000		; 00007530H
  001d6	ba 02 00 00 00	 mov	 edx, 2
  001db	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  001e2	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging

; 25   : #ifdef MU2_SHIPPING
; 26   : #else
; 27   : 		system("pause");	// 에러 코드 확인좀 허자!

  001e7	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_05PDJBBECF@pause@
  001ee	e8 00 00 00 00	 call	 system
  001f3	90		 npad	 1

; 29   : 		return -1;

  001f4	c7 44 24 50 ff
	ff ff ff	 mov	 DWORD PTR $T1[rsp], -1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1384 :         if (_Rep) {

  001fc	48 83 7c 24 48
	00		 cmp	 QWORD PTR server$[rsp+8], 0
  00202	74 0b		 je	 SHORT $LN87@wmain

; 1385 :             _Rep->_Decref();

  00204	48 8b 4c 24 48	 mov	 rcx, QWORD PTR server$[rsp+8]
  00209	e8 00 00 00 00	 call	 ?_Decref@_Ref_count_base@std@@QEAAXXZ ; std::_Ref_count_base::_Decref
  0020e	90		 npad	 1
$LN87@wmain:
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\main.cpp

; 29   : 		return -1;

  0020f	8b 44 24 50	 mov	 eax, DWORD PTR $T1[rsp]
  00213	e9 d8 00 00 00	 jmp	 $LN1@wmain
$LN2@wmain:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1309 :         return _Ptr;

  00218	48 8b 44 24 40	 mov	 rax, QWORD PTR server$[rsp]
  0021d	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR $T16[rsp], rax

; 1773 :         return get();

  00225	48 8b 84 24 e8
	00 00 00	 mov	 rax, QWORD PTR $T16[rsp]
  0022d	48 89 84 24 f0
	00 00 00	 mov	 QWORD PTR $T17[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\main.cpp

; 32   : 	server->RunServer();

  00235	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR $T17[rsp]
  0023d	48 8b c8	 mov	 rcx, rax
  00240	e8 00 00 00 00	 call	 ?RunServer@Server@mu2@@QEAAXXZ ; mu2::Server::RunServer
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1309 :         return _Ptr;

  00245	48 8b 44 24 40	 mov	 rax, QWORD PTR server$[rsp]
  0024a	48 89 84 24 f8
	00 00 00	 mov	 QWORD PTR $T18[rsp], rax

; 1773 :         return get();

  00252	48 8b 84 24 f8
	00 00 00	 mov	 rax, QWORD PTR $T18[rsp]
  0025a	48 89 84 24 00
	01 00 00	 mov	 QWORD PTR $T19[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\main.cpp

; 33   : 	server->Destroy();

  00262	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR $T19[rsp]
  0026a	48 8b c8	 mov	 rcx, rax
  0026d	e8 00 00 00 00	 call	 ?Destroy@Server@mu2@@QEAAXXZ ; mu2::Server::Destroy
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  00272	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A ; mu2::ISingleton<mu2::Logger>::inst
  00279	48 89 84 24 08
	01 00 00	 mov	 QWORD PTR $T20[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\main.cpp

; 35   : 	Logger::GetInstance().UnInit();

  00281	48 8b 84 24 08
	01 00 00	 mov	 rax, QWORD PTR $T20[rsp]
  00289	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR tv182[rsp], rax
  00291	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR tv182[rsp]
  00299	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0029c	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR tv182[rsp]
  002a4	ff 50 08	 call	 QWORD PTR [rax+8]
  002a7	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1309 :         return _Ptr;

  002a8	48 8b 44 24 40	 mov	 rax, QWORD PTR server$[rsp]
  002ad	48 89 84 24 10
	01 00 00	 mov	 QWORD PTR $T21[rsp], rax

; 1773 :         return get();

  002b5	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR $T21[rsp]
  002bd	48 89 84 24 18
	01 00 00	 mov	 QWORD PTR $T22[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\main.cpp

; 37   : 	return server->GetExitCode();

  002c5	48 8b 84 24 18
	01 00 00	 mov	 rax, QWORD PTR $T22[rsp]
  002cd	48 8b c8	 mov	 rcx, rax
  002d0	e8 00 00 00 00	 call	 ?GetExitCode@Server@mu2@@QEBAIXZ ; mu2::Server::GetExitCode
  002d5	89 44 24 54	 mov	 DWORD PTR $T2[rsp], eax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1384 :         if (_Rep) {

  002d9	48 83 7c 24 48
	00		 cmp	 QWORD PTR server$[rsp+8], 0
  002df	74 0b		 je	 SHORT $LN142@wmain

; 1385 :             _Rep->_Decref();

  002e1	48 8b 4c 24 48	 mov	 rcx, QWORD PTR server$[rsp+8]
  002e6	e8 00 00 00 00	 call	 ?_Decref@_Ref_count_base@std@@QEAAXXZ ; std::_Ref_count_base::_Decref
  002eb	90		 npad	 1
$LN142@wmain:
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\main.cpp

; 37   : 	return server->GetExitCode();

  002ec	8b 44 24 54	 mov	 eax, DWORD PTR $T2[rsp]
$LN1@wmain:

; 38   : }

  002f0	48 81 c4 48 01
	00 00		 add	 rsp, 328		; 00000148H
  002f7	c3		 ret	 0
wmain	ENDP
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
server$ = 64
$T1 = 80
$T2 = 84
$T3 = 88
tv130 = 96
tv70 = 104
$T4 = 112
tv142 = 120
tv182 = 128
$T5 = 136
$T6 = 144
$T7 = 152
tv195 = 160
$T8 = 168
$T9 = 176
$T10 = 184
$T11 = 192
$T12 = 200
$T13 = 208
$T14 = 216
$T15 = 224
$T16 = 232
$T17 = 240
$T18 = 248
$T19 = 256
$T20 = 264
$T21 = 272
$T22 = 280
$T23 = 288
argc$ = 336
argv$ = 344
wmain$dtor$0 PROC
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 58	 mov	 rcx, QWORD PTR $T3[rbp]
  0000d	e8 00 00 00 00	 call	 ??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::operator delete
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
wmain$dtor$0 ENDP
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
server$ = 64
$T1 = 80
$T2 = 84
$T3 = 88
tv130 = 96
tv70 = 104
$T4 = 112
tv142 = 120
tv182 = 128
$T5 = 136
$T6 = 144
$T7 = 152
tv195 = 160
$T8 = 168
$T9 = 176
$T10 = 184
$T11 = 192
$T12 = 200
$T13 = 208
$T14 = 216
$T15 = 224
$T16 = 232
$T17 = 240
$T18 = 248
$T19 = 256
$T20 = 264
$T21 = 272
$T22 = 280
$T23 = 288
argc$ = 336
argv$ = 344
wmain$dtor$2 PROC
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 70	 mov	 rcx, QWORD PTR $T4[rbp]
  0000d	e8 00 00 00 00	 call	 ??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::operator delete
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
wmain$dtor$2 ENDP
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
server$ = 64
$T1 = 80
$T2 = 84
$T3 = 88
tv130 = 96
tv70 = 104
$T4 = 112
tv142 = 120
tv182 = 128
$T5 = 136
$T6 = 144
$T7 = 152
tv195 = 160
$T8 = 168
$T9 = 176
$T10 = 184
$T11 = 192
$T12 = 200
$T13 = 208
$T14 = 216
$T15 = 224
$T16 = 232
$T17 = 240
$T18 = 248
$T19 = 256
$T20 = 264
$T21 = 272
$T22 = 280
$T23 = 288
argc$ = 336
argv$ = 344
wmain$dtor$3 PROC
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8d 4d 40	 lea	 rcx, QWORD PTR server$[rbp]
  0000d	e8 00 00 00 00	 call	 ??1?$shared_ptr@VWorldServer@mu2@@@std@@QEAA@XZ ; std::shared_ptr<mu2::WorldServer>::~shared_ptr<mu2::WorldServer>
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
wmain$dtor$3 ENDP
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??$uninitialized_fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@0@0AEBV10@@Z
_TEXT	SEGMENT
_Backout$1 = 0
_UFirst$ = 16
$T2 = 24
$T3 = 32
_ULast$ = 40
_Obj$ = 48
$T4 = 56
$T5 = 64
$T6 = 72
$T7 = 80
$T8 = 88
_Last$ = 96
_First$ = 104
_First$ = 128
_Last$ = 136
_Val$ = 144
??$uninitialized_fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@0@0AEBV10@@Z PROC ; std::uninitialized_fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > >, COMDAT

; 2039 : void uninitialized_fill(const _NoThrowFwdIt _First, const _NoThrowFwdIt _Last, const _Tval& _Val) {

$LN59:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 78	 sub	 rsp, 120		; 00000078H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 1382 :         return _It + 0;

  00013	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _First$[rsp]
  0001b	48 89 44 24 18	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 2042 :     auto _UFirst      = _STD _Get_unwrapped(_First);

  00020	48 8b 44 24 18	 mov	 rax, QWORD PTR $T2[rsp]
  00025	48 89 44 24 10	 mov	 QWORD PTR _UFirst$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 1382 :         return _It + 0;

  0002a	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR _Last$[rsp]
  00032	48 89 44 24 20	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 2043 :     const auto _ULast = _STD _Get_unwrapped(_Last);

  00037	48 8b 44 24 20	 mov	 rax, QWORD PTR $T3[rsp]
  0003c	48 89 44 24 28	 mov	 QWORD PTR _ULast$[rsp], rax

; 1620 :     constexpr explicit _Uninitialized_backout(_NoThrowFwdIt _Dest) : _First(_Dest), _Last(_Dest) {}

  00041	48 8b 44 24 10	 mov	 rax, QWORD PTR _UFirst$[rsp]
  00046	48 89 04 24	 mov	 QWORD PTR _Backout$1[rsp], rax
  0004a	48 8b 44 24 10	 mov	 rax, QWORD PTR _UFirst$[rsp]
  0004f	48 89 44 24 08	 mov	 QWORD PTR _Backout$1[rsp+8], rax
$LN2@uninitiali:

; 2044 :     if constexpr (_Fill_memset_is_safe<_Unwrapped_t<const _NoThrowFwdIt&>, _Tval>) {
; 2045 :         _STD _Fill_memset(_UFirst, _Val, static_cast<size_t>(_ULast - _UFirst));
; 2046 :     } else {
; 2047 :         if constexpr (_Fill_zero_memset_is_safe<_Unwrapped_t<const _NoThrowFwdIt&>, _Tval>) {
; 2048 :             if (_STD _Is_all_bits_zero(_Val)) {
; 2049 :                 _STD _Fill_zero_memset(_UFirst, static_cast<size_t>(_ULast - _UFirst));
; 2050 :                 return;
; 2051 :             }
; 2052 :         }
; 2053 : 
; 2054 :         _Uninitialized_backout<_Unwrapped_t<const _NoThrowFwdIt&>> _Backout{_UFirst};
; 2055 :         while (_Backout._Last != _ULast) {

  00054	48 8b 44 24 28	 mov	 rax, QWORD PTR _ULast$[rsp]
  00059	48 39 44 24 08	 cmp	 QWORD PTR _Backout$1[rsp+8], rax
  0005e	74 5f		 je	 SHORT $LN3@uninitiali
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00060	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR _Val$[rsp]
  00068	48 89 44 24 48	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1634 :         _STD _Construct_in_place(*_Last, _STD forward<_Types>(_Vals)...);

  0006d	48 8b 44 24 08	 mov	 rax, QWORD PTR _Backout$1[rsp+8]
  00072	48 89 44 24 30	 mov	 QWORD PTR _Obj$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00077	48 8b 44 24 30	 mov	 rax, QWORD PTR _Obj$[rsp]
  0007c	48 89 44 24 38	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00081	48 8b 44 24 38	 mov	 rax, QWORD PTR $T4[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  00086	48 89 44 24 40	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0008b	48 8b 44 24 40	 mov	 rax, QWORD PTR $T5[rsp]
  00090	48 89 44 24 58	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1634 :         _STD _Construct_in_place(*_Last, _STD forward<_Types>(_Vals)...);

  00095	48 8b 44 24 48	 mov	 rax, QWORD PTR $T6[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  0009a	48 89 44 24 50	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0009f	48 8b 44 24 50	 mov	 rax, QWORD PTR $T7[rsp]
  000a4	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000a7	48 8b 4c 24 58	 mov	 rcx, QWORD PTR $T8[rsp]
  000ac	48 89 01	 mov	 QWORD PTR [rcx], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1635 :         ++_Last;

  000af	48 8b 44 24 08	 mov	 rax, QWORD PTR _Backout$1[rsp+8]
  000b4	48 83 c0 08	 add	 rax, 8
  000b8	48 89 44 24 08	 mov	 QWORD PTR _Backout$1[rsp+8], rax

; 2056 :             _Backout._Emplace_back(_Val);
; 2057 :         }

  000bd	eb 95		 jmp	 SHORT $LN2@uninitiali
$LN3@uninitiali:

; 1658 :         _First = _Last;

  000bf	48 8b 44 24 08	 mov	 rax, QWORD PTR _Backout$1[rsp+8]
  000c4	48 89 04 24	 mov	 QWORD PTR _Backout$1[rsp], rax

; 1628 :         _STD _Destroy_range(_First, _Last);

  000c8	48 8b 44 24 08	 mov	 rax, QWORD PTR _Backout$1[rsp+8]
  000cd	48 89 44 24 60	 mov	 QWORD PTR _Last$[rsp], rax
  000d2	48 8b 04 24	 mov	 rax, QWORD PTR _Backout$1[rsp]
  000d6	48 89 44 24 68	 mov	 QWORD PTR _First$[rsp], rax

; 2058 : 
; 2059 :         _Backout._Release();
; 2060 :     }
; 2061 : }

  000db	48 83 c4 78	 add	 rsp, 120		; 00000078H
  000df	c3		 ret	 0
??$uninitialized_fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@0@0AEBV10@@Z ENDP ; std::uninitialized_fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
_TEXT	SEGMENT
_Ptr_container$ = 48
_Block_size$ = 56
_Ptr$ = 64
$T1 = 72
_Bytes$ = 96
??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z PROC ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>, COMDAT

; 182  : __declspec(allocator) void* _Allocate_manually_vector_aligned(const size_t _Bytes) {

$LN7:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 183  :     // allocate _Bytes manually aligned to at least _Big_allocation_alignment
; 184  :     const size_t _Block_size = _Non_user_size + _Bytes;

  00009	48 8b 44 24 60	 mov	 rax, QWORD PTR _Bytes$[rsp]
  0000e	48 83 c0 27	 add	 rax, 39			; 00000027H
  00012	48 89 44 24 38	 mov	 QWORD PTR _Block_size$[rsp], rax

; 185  :     if (_Block_size <= _Bytes) {

  00017	48 8b 44 24 60	 mov	 rax, QWORD PTR _Bytes$[rsp]
  0001c	48 39 44 24 38	 cmp	 QWORD PTR _Block_size$[rsp], rax
  00021	77 06		 ja	 SHORT $LN2@Allocate_m

; 186  :         _Throw_bad_array_new_length(); // add overflow

  00023	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00028	90		 npad	 1
$LN2@Allocate_m:

; 136  :         return ::operator new(_Bytes);

  00029	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Block_size$[rsp]
  0002e	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00033	48 89 44 24 48	 mov	 QWORD PTR $T1[rsp], rax

; 187  :     }
; 188  : 
; 189  :     const uintptr_t _Ptr_container = reinterpret_cast<uintptr_t>(_Traits::_Allocate(_Block_size));

  00038	48 8b 44 24 48	 mov	 rax, QWORD PTR $T1[rsp]
  0003d	48 89 44 24 30	 mov	 QWORD PTR _Ptr_container$[rsp], rax

; 190  :     _STL_VERIFY(_Ptr_container != 0, "invalid argument"); // validate even in release since we're doing p[-1]

  00042	48 83 7c 24 30
	00		 cmp	 QWORD PTR _Ptr_container$[rsp], 0
  00048	75 19		 jne	 SHORT $LN3@Allocate_m
  0004a	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR [rsp+32], 0
  00053	45 33 c9	 xor	 r9d, r9d
  00056	45 33 c0	 xor	 r8d, r8d
  00059	33 d2		 xor	 edx, edx
  0005b	33 c9		 xor	 ecx, ecx
  0005d	e8 00 00 00 00	 call	 _invoke_watson
  00062	90		 npad	 1
$LN3@Allocate_m:

; 191  :     void* const _Ptr = reinterpret_cast<void*>((_Ptr_container + _Non_user_size) & ~(_Big_allocation_alignment - 1));

  00063	48 8b 44 24 30	 mov	 rax, QWORD PTR _Ptr_container$[rsp]
  00068	48 83 c0 27	 add	 rax, 39			; 00000027H
  0006c	48 83 e0 e0	 and	 rax, -32		; ffffffffffffffe0H
  00070	48 89 44 24 40	 mov	 QWORD PTR _Ptr$[rsp], rax

; 192  :     static_cast<uintptr_t*>(_Ptr)[-1] = _Ptr_container;

  00075	b8 08 00 00 00	 mov	 eax, 8
  0007a	48 6b c0 ff	 imul	 rax, rax, -1
  0007e	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  00083	48 8b 54 24 30	 mov	 rdx, QWORD PTR _Ptr_container$[rsp]
  00088	48 89 14 01	 mov	 QWORD PTR [rcx+rax], rdx

; 193  : 
; 194  : #ifdef _DEBUG
; 195  :     static_cast<uintptr_t*>(_Ptr)[-2] = _Big_allocation_sentinel;
; 196  : #endif // defined(_DEBUG)
; 197  :     return _Ptr;

  0008c	48 8b 44 24 40	 mov	 rax, QWORD PTR _Ptr$[rsp]
$LN4@Allocate_m:

; 198  : }

  00091	48 83 c4 58	 add	 rsp, 88			; 00000058H
  00095	c3		 ret	 0
??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ENDP ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash
;	COMDAT ?_Bump_erased@_Range_eraser@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAXXZ
_TEXT	SEGMENT
_Oldnext$ = 32
_Bytes$ = 40
_Ptr$ = 48
this$ = 56
$T1 = 64
$T2 = 72
$T3 = 80
this$ = 112
?_Bump_erased@_Range_eraser@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAXXZ PROC ; std::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Range_eraser::_Bump_erased, COMDAT

; 1013 :         void _Bump_erased() noexcept {

$LN56:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 68	 sub	 rsp, 104		; 00000068H

; 1014 :             const auto _Oldnext = _Next;

  00009	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00012	48 89 44 24 20	 mov	 QWORD PTR _Oldnext$[rsp], rax

; 1015 :             _Next               = _Oldnext->_Next;

  00017	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0001c	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Oldnext$[rsp]
  00021	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00024	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 1016 :             _Node::_Freenode(_List._Getal(), _Oldnext);

  00028	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0002d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00030	48 89 44 24 38	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 1863 :         return _Mypair._Get_first();

  00035	48 8b 44 24 38	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  0003a	48 89 44 24 40	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 1863 :         return _Mypair._Get_first();

  0003f	48 8b 44 24 40	 mov	 rax, QWORD PTR $T1[rsp]
  00044	48 89 44 24 48	 mov	 QWORD PTR $T2[rsp], rax

; 317  :         allocator_traits<_Alnode>::destroy(_Al, _STD addressof(_Ptr->_Myval));

  00049	48 8b 44 24 20	 mov	 rax, QWORD PTR _Oldnext$[rsp]
  0004e	48 83 c0 10	 add	 rax, 16
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00052	48 89 44 24 50	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 723  :             _STD _Deallocate<_New_alignof<value_type>>(_Ptr, sizeof(value_type) * _Count);

  00057	b8 01 00 00 00	 mov	 eax, 1
  0005c	48 6b c0 18	 imul	 rax, rax, 24
  00060	48 89 44 24 28	 mov	 QWORD PTR _Bytes$[rsp], rax
  00065	48 8b 44 24 20	 mov	 rax, QWORD PTR _Oldnext$[rsp]
  0006a	48 89 44 24 30	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  0006f	48 81 7c 24 28
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  00078	72 10		 jb	 SHORT $LN47@Bump_erase

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  0007a	48 8d 54 24 28	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  0007f	48 8d 4c 24 30	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  00084	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  00089	90		 npad	 1
$LN47@Bump_erase:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  0008a	48 8b 54 24 28	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  0008f	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  00094	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00099	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash

; 1017 :             --_List._Mypair._Myval2._Mysize;

  0009a	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0009f	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000a2	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  000a6	48 ff c8	 dec	 rax
  000a9	48 8b 4c 24 70	 mov	 rcx, QWORD PTR this$[rsp]
  000ae	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  000b1	48 89 41 08	 mov	 QWORD PTR [rcx+8], rax

; 1018 :         }

  000b5	48 83 c4 68	 add	 rsp, 104		; 00000068H
  000b9	c3		 ret	 0
?_Bump_erased@_Range_eraser@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAXXZ ENDP ; std::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Range_eraser::_Bump_erased
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
;	COMDAT ??$_Free_non_head@V?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@SAXAEAV?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU01@@Z
_TEXT	SEGMENT
_Pnode$ = 32
_Bytes$ = 40
_Ptr$ = 48
_Pnext$1 = 56
$T2 = 64
_Al$ = 96
_Head$ = 104
??$_Free_non_head@V?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@SAXAEAV?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU01@@Z PROC ; std::_List_node<std::pair<unsigned int const ,unsigned int>,void *>::_Free_non_head<std::allocator<std::_List_node<std::pair<unsigned int const ,unsigned int>,void *> > >, COMDAT

; 323  :         _Alnode& _Al, _Nodeptr _Head) noexcept { // free a list starting at _First and terminated at nullptr

$LN49:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 324  :         _Head->_Prev->_Next = nullptr;

  0000e	48 8b 44 24 68	 mov	 rax, QWORD PTR _Head$[rsp]
  00013	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00017	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0

; 325  : 
; 326  :         auto _Pnode = _Head->_Next;

  0001e	48 8b 44 24 68	 mov	 rax, QWORD PTR _Head$[rsp]
  00023	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00026	48 89 44 24 20	 mov	 QWORD PTR _Pnode$[rsp], rax

; 327  :         for (_Nodeptr _Pnext; _Pnode; _Pnode = _Pnext) {

  0002b	eb 0a		 jmp	 SHORT $LN4@Free_non_h
$LN2@Free_non_h:
  0002d	48 8b 44 24 38	 mov	 rax, QWORD PTR _Pnext$1[rsp]
  00032	48 89 44 24 20	 mov	 QWORD PTR _Pnode$[rsp], rax
$LN4@Free_non_h:
  00037	48 83 7c 24 20
	00		 cmp	 QWORD PTR _Pnode$[rsp], 0
  0003d	74 60		 je	 SHORT $LN3@Free_non_h

; 328  :             _Pnext = _Pnode->_Next;

  0003f	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00044	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00047	48 89 44 24 38	 mov	 QWORD PTR _Pnext$1[rsp], rax

; 317  :         allocator_traits<_Alnode>::destroy(_Al, _STD addressof(_Ptr->_Myval));

  0004c	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00051	48 83 c0 10	 add	 rax, 16
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00055	48 89 44 24 40	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 723  :             _STD _Deallocate<_New_alignof<value_type>>(_Ptr, sizeof(value_type) * _Count);

  0005a	b8 01 00 00 00	 mov	 eax, 1
  0005f	48 6b c0 18	 imul	 rax, rax, 24
  00063	48 89 44 24 28	 mov	 QWORD PTR _Bytes$[rsp], rax
  00068	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0006d	48 89 44 24 30	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  00072	48 81 7c 24 28
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  0007b	72 10		 jb	 SHORT $LN40@Free_non_h

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  0007d	48 8d 54 24 28	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  00082	48 8d 4c 24 30	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  00087	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  0008c	90		 npad	 1
$LN40@Free_non_h:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  0008d	48 8b 54 24 28	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  00092	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  00097	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  0009c	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 330  :         }

  0009d	eb 8e		 jmp	 SHORT $LN2@Free_non_h
$LN3@Free_non_h:

; 331  :     }

  0009f	48 83 c4 58	 add	 rsp, 88			; 00000058H
  000a3	c3		 ret	 0
??$_Free_non_head@V?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@SAXAEAV?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU01@@Z ENDP ; std::_List_node<std::pair<unsigned int const ,unsigned int>,void *>::_Free_non_head<std::allocator<std::_List_node<std::pair<unsigned int const ,unsigned int>,void *> > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??__F?inst@?$ISingleton@VLogAssist@mu2@@@mu2@@1VLogAssist@2@A@@YAXXZ
text$yd	SEGMENT
this$ = 32
??__F?inst@?$ISingleton@VLogAssist@mu2@@@mu2@@1VLogAssist@2@A@@YAXXZ PROC ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::LogAssist>::inst'', COMDAT
  00000	48 83 ec 38	 sub	 rsp, 56			; 00000038H
  00004	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VLogAssist@mu2@@@mu2@@1VLogAssist@2@A ; mu2::ISingleton<mu2::LogAssist>::inst
  0000b	48 83 c0 08	 add	 rax, 8
  0000f	48 89 44 24 20	 mov	 QWORD PTR this$[rsp], rax
  00014	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00019	48 83 c0 18	 add	 rax, 24
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash

; 317  :         _Tidy();

  0001d	48 8b c8	 mov	 rcx, rax
  00020	e8 00 00 00 00	 call	 ?_Tidy@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAXXZ ; std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > > >::_Tidy
  00025	90		 npad	 1
  00026	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0002b	48 83 c0 08	 add	 rax, 8
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 1061 :         _Tidy();

  0002f	48 8b c8	 mov	 rcx, rax
  00032	e8 00 00 00 00	 call	 ?_Tidy@?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@AEAAXXZ ; std::list<std::pair<unsigned int const ,unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >::_Tidy
  00037	90		 npad	 1
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 80   : 	virtual~ISingleton() {}

  00038	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VLogAssist@mu2@@@mu2@@1VLogAssist@2@A ; mu2::ISingleton<mu2::LogAssist>::inst
  0003f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$ISingleton@VLogAssist@mu2@@@mu2@@6B@
  00046	48 89 08	 mov	 QWORD PTR [rax], rcx
  00049	48 83 c4 38	 add	 rsp, 56			; 00000038H
  0004d	c3		 ret	 0
??__F?inst@?$ISingleton@VLogAssist@mu2@@@mu2@@1VLogAssist@2@A@@YAXXZ ENDP ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::LogAssist>::inst''
text$yd	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??__E?inst@?$ISingleton@VLogAssist@mu2@@@mu2@@1VLogAssist@2@A@@YAXXZ
text$di	SEGMENT
??__E?inst@?$ISingleton@VLogAssist@mu2@@@mu2@@1VLogAssist@2@A@@YAXXZ PROC ; `dynamic initializer for 'mu2::ISingleton<mu2::LogAssist>::inst'', COMDAT

; 90   : template<typename T> T ISingleton<T>::inst;

  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?inst@?$ISingleton@VLogAssist@mu2@@@mu2@@1VLogAssist@2@A ; mu2::ISingleton<mu2::LogAssist>::inst
  0000b	e8 00 00 00 00	 call	 ??0LogAssist@mu2@@QEAA@XZ
  00010	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??__F?inst@?$ISingleton@VLogAssist@mu2@@@mu2@@1VLogAssist@2@A@@YAXXZ ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::LogAssist>::inst''
  00017	e8 00 00 00 00	 call	 atexit
  0001c	90		 npad	 1
  0001d	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00021	c3		 ret	 0
??__E?inst@?$ISingleton@VLogAssist@mu2@@@mu2@@1VLogAssist@2@A@@YAXXZ ENDP ; `dynamic initializer for 'mu2::ISingleton<mu2::LogAssist>::inst''
text$di	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??$_Construct@$00PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z
_TEXT	SEGMENT
$T1 = 32
$S26$ = 33
$T2 = 34
$T3 = 36
_My_data$ = 40
_New_capacity$ = 48
_Max$ = 56
_Masked$4 = 64
$T5 = 72
_New_ptr$ = 80
$T6 = 88
tv170 = 96
_Fancy_ptr$7 = 104
$T8 = 112
$T9 = 120
_First1$ = 128
$T10 = 136
$T11 = 144
_Al$ = 152
$T12 = 160
$T13 = 168
$T14 = 176
$T15 = 184
$T16 = 192
$T17 = 200
_Ptr$ = 208
$T18 = 216
_First1$ = 224
_Ptr$ = 232
$T19 = 240
_Alproxy$ = 248
this$ = 272
_Arg$ = 280
_Count$ = 288
??$_Construct@$00PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<1,wchar_t const *>, COMDAT

; 871  :     _CONSTEXPR20 void _Construct(const _Char_or_ptr _Arg, _CRT_GUARDOVERFLOW const size_type _Count) {

$LN188:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	57		 push	 rdi
  00010	48 81 ec 00 01
	00 00		 sub	 rsp, 256		; 00000100H

; 872  :         auto& _My_data = _Mypair._Myval2;

  00017	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0001f	48 89 44 24 28	 mov	 QWORD PTR _My_data$[rsp], rax

; 873  :         _STL_INTERNAL_CHECK(!_My_data._Large_mode_engaged());
; 874  : 
; 875  :         if constexpr (_Strat == _Construct_strategy::_From_char) {
; 876  :             _STL_INTERNAL_STATIC_ASSERT(is_same_v<_Char_or_ptr, _Elem>);
; 877  :         } else {
; 878  :             _STL_INTERNAL_STATIC_ASSERT(_Is_elem_cptr<_Char_or_ptr>::value);
; 879  :         }
; 880  : 
; 881  :         if (_Count > max_size()) {

  00024	48 8b 8c 24 10
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0002c	e8 00 00 00 00	 call	 ?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
  00031	48 39 84 24 20
	01 00 00	 cmp	 QWORD PTR _Count$[rsp], rax
  00039	76 06		 jbe	 SHORT $LN2@Construct

; 882  :             _Xlen_string(); // result too long

  0003b	e8 00 00 00 00	 call	 ?_Xlen_string@std@@YAXXZ ; std::_Xlen_string
  00040	90		 npad	 1
$LN2@Construct:

; 3107 :         return _Mypair._Get_first();

  00041	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00049	48 89 44 24 70	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3107 :         return _Mypair._Get_first();

  0004e	48 8b 44 24 70	 mov	 rax, QWORD PTR $T8[rsp]
  00053	48 89 44 24 78	 mov	 QWORD PTR $T9[rsp], rax

; 883  :         }
; 884  : 
; 885  :         auto& _Al       = _Getal();

  00058	48 8b 44 24 78	 mov	 rax, QWORD PTR $T9[rsp]
  0005d	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR _Al$[rsp], rax

; 886  :         auto&& _Alproxy = _GET_PROXY_ALLOCATOR(_Alty, _Al);

  00065	48 8d 44 24 20	 lea	 rax, QWORD PTR $T1[rsp]
  0006a	48 8b f8	 mov	 rdi, rax
  0006d	33 c0		 xor	 eax, eax
  0006f	b9 01 00 00 00	 mov	 ecx, 1
  00074	f3 aa		 rep stosb
  00076	48 8d 44 24 21	 lea	 rax, QWORD PTR $S26$[rsp]
  0007b	48 89 84 24 f8
	00 00 00	 mov	 QWORD PTR _Alproxy$[rsp], rax

; 887  :         _Container_proxy_ptr<_Alty> _Proxy(_Alproxy, _My_data);
; 888  : 
; 889  :         if (_Count <= _Small_string_capacity) {

  00083	48 83 bc 24 20
	01 00 00 07	 cmp	 QWORD PTR _Count$[rsp], 7
  0008c	77 71		 ja	 SHORT $LN3@Construct

; 890  :             _My_data._Mysize = _Count;

  0008e	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00093	48 8b 8c 24 20
	01 00 00	 mov	 rcx, QWORD PTR _Count$[rsp]
  0009b	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 891  :             _My_data._Myres  = _Small_string_capacity;

  0009f	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000a4	48 c7 40 18 07
	00 00 00	 mov	 QWORD PTR [rax+24], 7

; 892  : 
; 893  :             if constexpr (_Strat == _Construct_strategy::_From_char) {
; 894  :                 _Traits::assign(_My_data._Bx._Buf, _Count, _Arg);
; 895  :                 _Traits::assign(_My_data._Bx._Buf[_Count], _Elem());
; 896  :             } else if constexpr (_Strat == _Construct_strategy::_From_ptr) {
; 897  :                 _Traits::copy(_My_data._Bx._Buf, _Arg, _Count);

  000ac	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000b1	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _First1$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  000b9	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  000c1	48 03 c0	 add	 rax, rax
  000c4	4c 8b c0	 mov	 r8, rax
  000c7	48 8b 94 24 18
	01 00 00	 mov	 rdx, QWORD PTR _Arg$[rsp]
  000cf	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR _First1$[rsp]
  000d7	e8 00 00 00 00	 call	 memcpy
  000dc	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 898  :                 _Traits::assign(_My_data._Bx._Buf[_Count], _Elem());

  000dd	33 c0		 xor	 eax, eax
  000df	66 89 44 24 22	 mov	 WORD PTR $T2[rsp], ax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 347  :         _Left = _Right;

  000e4	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000e9	48 8b 8c 24 20
	01 00 00	 mov	 rcx, QWORD PTR _Count$[rsp]
  000f1	0f b7 54 24 22	 movzx	 edx, WORD PTR $T2[rsp]
  000f6	66 89 14 48	 mov	 WORD PTR [rax+rcx*2], dx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 908  :             return;

  000fa	e9 3c 02 00 00	 jmp	 $LN4@Construct
$LN3@Construct:

; 909  :         }
; 910  : 
; 911  :         size_type _New_capacity = _Calculate_growth(_Count, _Small_string_capacity, max_size());

  000ff	48 8b 8c 24 10
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00107	e8 00 00 00 00	 call	 ?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
  0010c	48 89 44 24 38	 mov	 QWORD PTR _Max$[rsp], rax

; 2978 :         const size_type _Masked = _Requested | _Alloc_mask;

  00111	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  00119	48 83 c8 07	 or	 rax, 7
  0011d	48 89 44 24 40	 mov	 QWORD PTR _Masked$4[rsp], rax

; 2979 :         if (_Masked > _Max) { // the mask overflows, settle for max_size()

  00122	48 8b 44 24 38	 mov	 rax, QWORD PTR _Max$[rsp]
  00127	48 39 44 24 40	 cmp	 QWORD PTR _Masked$4[rsp], rax
  0012c	76 0f		 jbe	 SHORT $LN114@Construct

; 2980 :             return _Max;

  0012e	48 8b 44 24 38	 mov	 rax, QWORD PTR _Max$[rsp]
  00133	48 89 44 24 48	 mov	 QWORD PTR $T5[rsp], rax
  00138	e9 93 00 00 00	 jmp	 $LN113@Construct
$LN114@Construct:

; 2981 :         }
; 2982 : 
; 2983 :         if (_Old > _Max - _Old / 2) { // similarly, geometric overflows

  0013d	33 d2		 xor	 edx, edx
  0013f	b8 07 00 00 00	 mov	 eax, 7
  00144	b9 02 00 00 00	 mov	 ecx, 2
  00149	48 f7 f1	 div	 rcx
  0014c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Max$[rsp]
  00151	48 2b c8	 sub	 rcx, rax
  00154	48 8b c1	 mov	 rax, rcx
  00157	48 83 f8 07	 cmp	 rax, 7
  0015b	73 0c		 jae	 SHORT $LN115@Construct

; 2984 :             return _Max;

  0015d	48 8b 44 24 38	 mov	 rax, QWORD PTR _Max$[rsp]
  00162	48 89 44 24 48	 mov	 QWORD PTR $T5[rsp], rax
  00167	eb 67		 jmp	 SHORT $LN113@Construct
$LN115@Construct:

; 2987 :         return (_STD max)(_Masked, _Old + _Old / 2);

  00169	33 d2		 xor	 edx, edx
  0016b	b8 07 00 00 00	 mov	 eax, 7
  00170	b9 02 00 00 00	 mov	 ecx, 2
  00175	48 f7 f1	 div	 rcx
  00178	48 83 c0 07	 add	 rax, 7
  0017c	48 89 44 24 58	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 77   :     return _Left < _Right ? _Right : _Left;

  00181	48 8b 44 24 58	 mov	 rax, QWORD PTR $T6[rsp]
  00186	48 39 44 24 40	 cmp	 QWORD PTR _Masked$4[rsp], rax
  0018b	73 0c		 jae	 SHORT $LN122@Construct
  0018d	48 8d 44 24 58	 lea	 rax, QWORD PTR $T6[rsp]
  00192	48 89 44 24 60	 mov	 QWORD PTR tv170[rsp], rax
  00197	eb 0a		 jmp	 SHORT $LN123@Construct
$LN122@Construct:
  00199	48 8d 44 24 40	 lea	 rax, QWORD PTR _Masked$4[rsp]
  0019e	48 89 44 24 60	 mov	 QWORD PTR tv170[rsp], rax
$LN123@Construct:
  001a3	48 8b 44 24 60	 mov	 rax, QWORD PTR tv170[rsp]
  001a8	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
  001b0	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  001b8	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2987 :         return (_STD max)(_Masked, _Old + _Old / 2);

  001c0	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  001c8	48 8b 00	 mov	 rax, QWORD PTR [rax]
  001cb	48 89 44 24 48	 mov	 QWORD PTR $T5[rsp], rax
$LN113@Construct:

; 909  :         }
; 910  : 
; 911  :         size_type _New_capacity = _Calculate_growth(_Count, _Small_string_capacity, max_size());

  001d0	48 8b 44 24 48	 mov	 rax, QWORD PTR $T5[rsp]
  001d5	48 89 44 24 30	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 825  :         ++_Capacity; // Take null terminator into consideration

  001da	48 8b 44 24 30	 mov	 rax, QWORD PTR _New_capacity$[rsp]
  001df	48 ff c0	 inc	 rax
  001e2	48 89 44 24 30	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 826  : 
; 827  :         pointer _Fancy_ptr = nullptr;

  001e7	48 c7 44 24 68
	00 00 00 00	 mov	 QWORD PTR _Fancy_ptr$7[rsp], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 2303 :         return _Al.allocate(_Count);

  001f0	48 8b 54 24 30	 mov	 rdx, QWORD PTR _New_capacity$[rsp]
  001f5	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR _Al$[rsp]
  001fd	e8 00 00 00 00	 call	 ?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z ; std::allocator<wchar_t>::allocate
  00202	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 829  :             _Fancy_ptr = _Allocate_at_least_helper(_Al, _Capacity);

  0020a	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
  00212	48 89 44 24 68	 mov	 QWORD PTR _Fancy_ptr$7[rsp], rax

; 830  :         } else {
; 831  :             _STL_INTERNAL_STATIC_ASSERT(_Policy == _Allocation_policy::_Exactly);
; 832  :             _Fancy_ptr = _Al.allocate(_Capacity);
; 833  :         }
; 834  : 
; 835  : #if _HAS_CXX20
; 836  :         // Start element lifetimes to avoid UB. This is a more general mechanism than _String_val::_Activate_SSO_buffer,
; 837  :         // but likely more impactful to throughput.
; 838  :         if (_STD is_constant_evaluated()) {
; 839  :             _Elem* const _Ptr = _Unfancy(_Fancy_ptr);
; 840  :             for (size_type _Idx = 0; _Idx < _Capacity; ++_Idx) {
; 841  :                 _STD construct_at(_Ptr + _Idx);
; 842  :             }
; 843  :         }
; 844  : #endif // _HAS_CXX20
; 845  :         --_Capacity;

  00217	48 8b 44 24 30	 mov	 rax, QWORD PTR _New_capacity$[rsp]
  0021c	48 ff c8	 dec	 rax
  0021f	48 89 44 24 30	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 846  :         return _Fancy_ptr;

  00224	48 8b 44 24 68	 mov	 rax, QWORD PTR _Fancy_ptr$7[rsp]
  00229	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax

; 912  :         const pointer _New_ptr  = _Allocate_for_capacity(_Al, _New_capacity); // throws

  00231	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T13[rsp]
  00239	48 89 44 24 50	 mov	 QWORD PTR _New_ptr$[rsp], rax

; 913  :         _Construct_in_place(_My_data._Bx._Ptr, _New_ptr);

  0023e	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00243	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T14[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0024b	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR $T14[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  00253	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T15[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0025b	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T15[rsp]
  00263	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T16[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  0026b	48 8d 44 24 50	 lea	 rax, QWORD PTR _New_ptr$[rsp]
  00270	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T17[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00278	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR $T16[rsp]
  00280	48 8b 8c 24 c8
	00 00 00	 mov	 rcx, QWORD PTR $T17[rsp]
  00288	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0028b	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 915  :         _My_data._Mysize = _Count;

  0028e	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00293	48 8b 8c 24 20
	01 00 00	 mov	 rcx, QWORD PTR _Count$[rsp]
  0029b	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 916  :         _My_data._Myres  = _New_capacity;

  0029f	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  002a4	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _New_capacity$[rsp]
  002a9	48 89 48 18	 mov	 QWORD PTR [rax+24], rcx

; 921  :             _Traits::copy(_Unfancy(_New_ptr), _Arg, _Count);

  002ad	48 8b 44 24 50	 mov	 rax, QWORD PTR _New_ptr$[rsp]
  002b2	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  002ba	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR _Ptr$[rsp]
  002c2	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR $T18[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 921  :             _Traits::copy(_Unfancy(_New_ptr), _Arg, _Count);

  002ca	48 8b 84 24 d8
	00 00 00	 mov	 rax, QWORD PTR $T18[rsp]
  002d2	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR _First1$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  002da	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  002e2	48 03 c0	 add	 rax, rax
  002e5	4c 8b c0	 mov	 r8, rax
  002e8	48 8b 94 24 18
	01 00 00	 mov	 rdx, QWORD PTR _Arg$[rsp]
  002f0	48 8b 8c 24 e0
	00 00 00	 mov	 rcx, QWORD PTR _First1$[rsp]
  002f8	e8 00 00 00 00	 call	 memcpy
  002fd	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 922  :             _Traits::assign(_Unfancy(_New_ptr)[_Count], _Elem());

  002fe	33 c0		 xor	 eax, eax
  00300	66 89 44 24 24	 mov	 WORD PTR $T3[rsp], ax
  00305	48 8b 44 24 50	 mov	 rax, QWORD PTR _New_ptr$[rsp]
  0030a	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  00312	48 8b 84 24 e8
	00 00 00	 mov	 rax, QWORD PTR _Ptr$[rsp]
  0031a	48 89 84 24 f0
	00 00 00	 mov	 QWORD PTR $T19[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 347  :         _Left = _Right;

  00322	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR $T19[rsp]
  0032a	48 8b 8c 24 20
	01 00 00	 mov	 rcx, QWORD PTR _Count$[rsp]
  00332	0f b7 54 24 24	 movzx	 edx, WORD PTR $T3[rsp]
  00337	66 89 14 48	 mov	 WORD PTR [rax+rcx*2], dx
$LN4@Construct:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 929  :     }

  0033b	48 81 c4 00 01
	00 00		 add	 rsp, 256		; 00000100H
  00342	5f		 pop	 rdi
  00343	c3		 ret	 0
??$_Construct@$00PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<1,wchar_t const *>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
;	COMDAT ??$_Fnv1a_append_value@I@std@@YA_K_KAEBI@Z
_TEXT	SEGMENT
_Val$ = 0
_Idx$1 = 8
$T2 = 16
_Val$ = 48
_Keyval$ = 56
??$_Fnv1a_append_value@I@std@@YA_K_KAEBI@Z PROC		; std::_Fnv1a_append_value<unsigned int>, COMDAT

; 2278 :     const size_t _Val, const _Kty& _Keyval) noexcept { // accumulate _Keyval into partial FNV-1a hash _Val

$LN12:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 2279 :     static_assert(is_trivially_copyable_v<_Kty>, "Only trivially copyable types can be directly hashed.");
; 2280 :     return _Fnv1a_append_bytes(_Val, &reinterpret_cast<const unsigned char&>(_Keyval), sizeof(_Kty));

  0000e	48 8b 44 24 30	 mov	 rax, QWORD PTR _Val$[rsp]
  00013	48 89 04 24	 mov	 QWORD PTR _Val$[rsp], rax

; 2259 :     for (size_t _Idx = 0; _Idx < _Count; ++_Idx) {

  00017	48 c7 44 24 08
	00 00 00 00	 mov	 QWORD PTR _Idx$1[rsp], 0
  00020	eb 0d		 jmp	 SHORT $LN7@Fnv1a_appe
$LN5@Fnv1a_appe:
  00022	48 8b 44 24 08	 mov	 rax, QWORD PTR _Idx$1[rsp]
  00027	48 ff c0	 inc	 rax
  0002a	48 89 44 24 08	 mov	 QWORD PTR _Idx$1[rsp], rax
$LN7@Fnv1a_appe:
  0002f	48 83 7c 24 08
	04		 cmp	 QWORD PTR _Idx$1[rsp], 4
  00035	73 3c		 jae	 SHORT $LN6@Fnv1a_appe

; 2260 :         _Val ^= static_cast<size_t>(_First[_Idx]);

  00037	48 8b 44 24 08	 mov	 rax, QWORD PTR _Idx$1[rsp]
  0003c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Keyval$[rsp]
  00041	48 03 c8	 add	 rcx, rax
  00044	48 8b c1	 mov	 rax, rcx
  00047	0f b6 00	 movzx	 eax, BYTE PTR [rax]
  0004a	48 8b 0c 24	 mov	 rcx, QWORD PTR _Val$[rsp]
  0004e	48 33 c8	 xor	 rcx, rax
  00051	48 8b c1	 mov	 rax, rcx
  00054	48 89 04 24	 mov	 QWORD PTR _Val$[rsp], rax

; 2261 :         _Val *= _FNV_prime;

  00058	48 b8 b3 01 00
	00 00 01 00 00	 mov	 rax, 1099511628211	; 00000100000001b3H
  00062	48 8b 0c 24	 mov	 rcx, QWORD PTR _Val$[rsp]
  00066	48 0f af c8	 imul	 rcx, rax
  0006a	48 8b c1	 mov	 rax, rcx
  0006d	48 89 04 24	 mov	 QWORD PTR _Val$[rsp], rax

; 2262 :     }

  00071	eb af		 jmp	 SHORT $LN5@Fnv1a_appe
$LN6@Fnv1a_appe:

; 2263 : 
; 2264 :     return _Val;

  00073	48 8b 04 24	 mov	 rax, QWORD PTR _Val$[rsp]
  00077	48 89 44 24 10	 mov	 QWORD PTR $T2[rsp], rax

; 2279 :     static_assert(is_trivially_copyable_v<_Kty>, "Only trivially copyable types can be directly hashed.");
; 2280 :     return _Fnv1a_append_bytes(_Val, &reinterpret_cast<const unsigned char&>(_Keyval), sizeof(_Kty));

  0007c	48 8b 44 24 10	 mov	 rax, QWORD PTR $T2[rsp]

; 2281 : }

  00081	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00085	c3		 ret	 0
??$_Fnv1a_append_value@I@std@@YA_K_KAEBI@Z ENDP		; std::_Fnv1a_append_value<unsigned int>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??_GLogAssist@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 32
this$ = 64
__flags$ = 72
??_GLogAssist@mu2@@UEAAPEAXI@Z PROC			; mu2::LogAssist::`scalar deleting destructor', COMDAT
$LN171:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 38	 sub	 rsp, 56			; 00000038H
  0000d	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 83 c0 08	 add	 rax, 8
  00016	48 89 44 24 20	 mov	 QWORD PTR this$[rsp], rax
  0001b	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00020	48 83 c0 18	 add	 rax, 24
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash

; 317  :         _Tidy();

  00024	48 8b c8	 mov	 rcx, rax
  00027	e8 00 00 00 00	 call	 ?_Tidy@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAXXZ ; std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > > >::_Tidy
  0002c	90		 npad	 1
  0002d	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00032	48 83 c0 08	 add	 rax, 8
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 1061 :         _Tidy();

  00036	48 8b c8	 mov	 rcx, rax
  00039	e8 00 00 00 00	 call	 ?_Tidy@?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@AEAAXXZ ; std::list<std::pair<unsigned int const ,unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >::_Tidy
  0003e	90		 npad	 1
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 80   : 	virtual~ISingleton() {}

  0003f	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00044	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$ISingleton@VLogAssist@mu2@@@mu2@@6B@
  0004b	48 89 08	 mov	 QWORD PTR [rax], rcx
  0004e	8b 44 24 48	 mov	 eax, DWORD PTR __flags$[rsp]
  00052	83 e0 01	 and	 eax, 1
  00055	85 c0		 test	 eax, eax
  00057	74 10		 je	 SHORT $LN2@scalar
  00059	ba 48 00 00 00	 mov	 edx, 72			; 00000048H
  0005e	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00063	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00068	90		 npad	 1
$LN2@scalar:
  00069	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0006e	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00072	c3		 ret	 0
??_GLogAssist@mu2@@UEAAPEAXI@Z ENDP			; mu2::LogAssist::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??0LogAssist@mu2@@QEAA@XZ
_TEXT	SEGMENT
this$ = 48
??0LogAssist@mu2@@QEAA@XZ PROC				; mu2::LogAssist::LogAssist, COMDAT
$LN210:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 84   : 	ISingleton() {}	

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$ISingleton@VLogAssist@mu2@@@mu2@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx
  00018	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7LogAssist@mu2@@6B@
  00024	48 89 08	 mov	 QWORD PTR [rax], rcx
  00027	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0002c	48 83 c0 08	 add	 rax, 8
  00030	48 8b c8	 mov	 rcx, rax
  00033	e8 00 00 00 00	 call	 ??0?$unordered_map@IIU?$hash@I@std@@U?$equal_to@I@2@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ ; std::unordered_map<unsigned int,unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >::unordered_map<unsigned int,unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >
  00038	90		 npad	 1
  00039	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00042	c3		 ret	 0
??0LogAssist@mu2@@QEAA@XZ ENDP				; mu2::LogAssist::LogAssist
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
this$ = 48
?dtor$0@?0???0LogAssist@mu2@@QEAA@XZ@4HA PROC		; `mu2::LogAssist::LogAssist'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 30	 mov	 rcx, QWORD PTR this$[rbp]
  0000d	e8 00 00 00 00	 call	 ??1?$ISingleton@VLogAssist@mu2@@@mu2@@UEAA@XZ ; mu2::ISingleton<mu2::LogAssist>::~ISingleton<mu2::LogAssist>
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$0@?0???0LogAssist@mu2@@QEAA@XZ@4HA ENDP		; `mu2::LogAssist::LogAssist'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\unordered_map
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\unordered_map
;	COMDAT ??0?$unordered_map@IIU?$hash@I@std@@U?$equal_to@I@2@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ
_TEXT	SEGMENT
$T1 = 32
$T2 = 33
__formal$ = 40
$T3 = 48
$T4 = 52
$T5 = 56
this$ = 64
$T6 = 72
this$ = 80
$T7 = 88
$T8 = 96
$T9 = 104
this$ = 128
??0?$unordered_map@IIU?$hash@I@std@@U?$equal_to@I@2@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ PROC ; std::unordered_map<unsigned int,unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >::unordered_map<unsigned int,unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >, COMDAT

; 101  :     unordered_map() : _Mybase(_Key_compare(), allocator_type()) {}

$LN204:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 78	 sub	 rsp, 120		; 00000078H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 974  :     constexpr allocator() noexcept {}

  00009	48 8d 44 24 20	 lea	 rax, QWORD PTR $T1[rsp]
  0000e	48 89 44 24 60	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash

; 130  :         : _Mypair(_Zero_then_variadic_args_t{}, _Zero_then_variadic_args_t{}, 0.0f) {}

  00013	0f 57 c0	 xorps	 xmm0, xmm0
  00016	f3 0f 11 44 24
	30		 movss	 DWORD PTR $T3[rsp], xmm0
  0001c	48 8d 44 24 34	 lea	 rax, QWORD PTR $T4[rsp]
  00021	48 89 44 24 40	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00026	48 8d 44 24 30	 lea	 rax, QWORD PTR $T3[rsp]
  0002b	48 89 44 24 48	 mov	 QWORD PTR $T6[rsp], rax
  00030	48 8d 44 24 21	 lea	 rax, QWORD PTR $T2[rsp]
  00035	48 89 44 24 38	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1531 :         : _Ty1(), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  0003a	48 8b 44 24 38	 mov	 rax, QWORD PTR $T5[rsp]
  0003f	0f b6 00	 movzx	 eax, BYTE PTR [rax]
  00042	88 44 24 28	 mov	 BYTE PTR __formal$[rsp], al
  00046	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0004b	48 89 44 24 50	 mov	 QWORD PTR this$[rsp], rax
  00050	48 8b 44 24 48	 mov	 rax, QWORD PTR $T6[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00055	48 89 44 24 58	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1531 :         : _Ty1(), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  0005a	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0005f	48 8b 4c 24 58	 mov	 rcx, QWORD PTR $T7[rsp]
  00064	f3 0f 10 01	 movss	 xmm0, DWORD PTR [rcx]
  00068	f3 0f 11 00	 movss	 DWORD PTR [rax], xmm0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash

; 130  :         : _Mypair(_Zero_then_variadic_args_t{}, _Zero_then_variadic_args_t{}, 0.0f) {}

  0006c	48 8d 44 24 34	 lea	 rax, QWORD PTR $T4[rsp]
  00071	48 89 44 24 68	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\unordered_map

; 101  :     unordered_map() : _Mybase(_Key_compare(), allocator_type()) {}

  00076	48 8b 44 24 60	 mov	 rax, QWORD PTR $T8[rsp]
  0007b	48 8b 4c 24 68	 mov	 rcx, QWORD PTR $T9[rsp]
  00080	4c 8b c0	 mov	 r8, rax
  00083	48 8b d1	 mov	 rdx, rcx
  00086	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0008e	e8 00 00 00 00	 call	 ??0?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA@AEBV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@1@AEBV?$allocator@U?$pair@$$CBII@std@@@1@@Z ; std::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >
  00093	90		 npad	 1
  00094	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0009c	48 83 c4 78	 add	 rsp, 120		; 00000078H
  000a0	c3		 ret	 0
??0?$unordered_map@IIU?$hash@I@std@@U?$equal_to@I@2@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ ENDP ; std::unordered_map<unsigned int,unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >::unordered_map<unsigned int,unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash
;	COMDAT ??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ PROC ; std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > > >::~_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > > >, COMDAT

; 316  :     ~_Hash_vec() {

$LN40:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 317  :         _Tidy();

  00009	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0000e	e8 00 00 00 00	 call	 ?_Tidy@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAXXZ ; std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > > >::_Tidy
  00013	90		 npad	 1

; 318  : #if _ITERATOR_DEBUG_LEVEL != 0
; 319  :         auto&& _Alproxy = _GET_PROXY_ALLOCATOR(_Aliter, _Mypair._Get_first());
; 320  :         _Delete_plain_internal(_Alproxy, _STD exchange(_Mypair._Myval2._Myproxy, nullptr));
; 321  : #endif // _ITERATOR_DEBUG_LEVEL != 0
; 322  :     }

  00014	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00018	c3		 ret	 0
??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ ENDP ; std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > > >::~_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash
;	COMDAT ?_Tidy@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAXXZ
_TEXT	SEGMENT
_Bytes$ = 32
_Ptr$ = 40
$T1 = 48
_Ptr$ = 56
_Last$ = 64
_First$ = 72
$T2 = 80
this$ = 112
?_Tidy@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAXXZ PROC ; std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > > >::_Tidy, COMDAT

; 306  :     void _Tidy() noexcept {

$LN35:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 68	 sub	 rsp, 104		; 00000068H

; 307  :         if (_Mypair._Myval2._Myfirst != nullptr) {

  00009	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 38 00	 cmp	 QWORD PTR [rax], 0
  00012	0f 84 b8 00 00
	00		 je	 $LN2@Tidy

; 308  :             _Destroy_range(_Mypair._Myval2._Myfirst, _Mypair._Myval2._Mylast);

  00018	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00021	48 89 44 24 40	 mov	 QWORD PTR _Last$[rsp], rax
  00026	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0002b	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0002e	48 89 44 24 48	 mov	 QWORD PTR _First$[rsp], rax

; 279  :         return static_cast<size_type>(_Mypair._Myval2._Myend - _Mypair._Myval2._Myfirst);

  00033	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 8b 4c 24 70	 mov	 rcx, QWORD PTR this$[rsp]
  0003d	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00040	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00044	48 2b c1	 sub	 rax, rcx
  00047	48 c1 f8 03	 sar	 rax, 3
  0004b	48 89 44 24 30	 mov	 QWORD PTR $T1[rsp], rax

; 309  :             _Mypair._Get_first().deallocate(_Mypair._Myval2._Myfirst, capacity());

  00050	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00055	48 89 44 24 50	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash

; 309  :             _Mypair._Get_first().deallocate(_Mypair._Myval2._Myfirst, capacity());

  0005a	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0005f	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00062	48 89 44 24 38	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  00067	48 8b 44 24 30	 mov	 rax, QWORD PTR $T1[rsp]
  0006c	48 c1 e0 03	 shl	 rax, 3
  00070	48 89 44 24 20	 mov	 QWORD PTR _Bytes$[rsp], rax
  00075	48 8b 44 24 38	 mov	 rax, QWORD PTR _Ptr$[rsp]
  0007a	48 89 44 24 28	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  0007f	48 81 7c 24 20
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  00088	72 10		 jb	 SHORT $LN26@Tidy

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  0008a	48 8d 54 24 20	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  0008f	48 8d 4c 24 28	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  00094	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  00099	90		 npad	 1
$LN26@Tidy:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  0009a	48 8b 54 24 20	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  0009f	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  000a4	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  000a9	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash

; 310  :             _Mypair._Myval2._Myfirst = nullptr;

  000aa	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  000af	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0

; 311  :             _Mypair._Myval2._Mylast  = nullptr;

  000b6	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  000bb	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0

; 312  :             _Mypair._Myval2._Myend   = nullptr;

  000c3	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  000c8	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0
$LN2@Tidy:

; 313  :         }
; 314  :     }

  000d0	48 83 c4 68	 add	 rsp, 104		; 00000068H
  000d4	c3		 ret	 0
?_Tidy@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAXXZ ENDP ; std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > > >::_Tidy
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash
;	COMDAT ?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@2@@Z
_TEXT	SEGMENT
_UFirst$1 = 32
_Bytes$ = 40
_Newend$2 = 48
_Newvec$3 = 56
_Oldcapacity$4 = 64
_Ptr$ = 72
$T5 = 80
$T6 = 88
_Oldsize$ = 96
_Alvec$ = 104
$T7 = 112
_Ptr$ = 120
_First$ = 128
$T8 = 136
_Last$ = 144
$T9 = 152
_ULast$10 = 160
_Last$ = 168
_First$ = 176
this$ = 208
_Cells$ = 216
_Val$ = 224
?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@2@@Z PROC ; std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > > >::_Assign_grow, COMDAT

; 282  :     void _Assign_grow(const size_type _Cells, const value_type _Val) {

$LN70:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 81 ec c8 00
	00 00		 sub	 rsp, 200		; 000000c8H

; 268  :         return static_cast<size_type>(_Mypair._Myval2._Mylast - _Mypair._Myval2._Myfirst);

  00016	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0001e	48 8b 8c 24 d0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00026	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00029	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0002d	48 2b c1	 sub	 rax, rcx
  00030	48 c1 f8 03	 sar	 rax, 3
  00034	48 89 44 24 50	 mov	 QWORD PTR $T5[rsp], rax

; 283  :         // set the elements stored here to _Cells copies of _Val, leaving the value unchanged if an exception is thrown
; 284  :         const auto _Oldsize = size();

  00039	48 8b 44 24 50	 mov	 rax, QWORD PTR $T5[rsp]
  0003e	48 89 44 24 60	 mov	 QWORD PTR _Oldsize$[rsp], rax

; 286  :         auto& _Alvec = _Mypair._Get_first();

  00043	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  0004b	48 89 44 24 58	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash

; 286  :         auto& _Alvec = _Mypair._Get_first();

  00050	48 8b 44 24 58	 mov	 rax, QWORD PTR $T6[rsp]
  00055	48 89 44 24 68	 mov	 QWORD PTR _Alvec$[rsp], rax

; 287  :         if (_Oldsize < _Cells) {

  0005a	48 8b 84 24 d8
	00 00 00	 mov	 rax, QWORD PTR _Cells$[rsp]
  00062	48 39 44 24 60	 cmp	 QWORD PTR _Oldsize$[rsp], rax
  00067	0f 83 2b 01 00
	00		 jae	 $LN2@Assign_gro

; 288  :             const auto _Newvec = _Alvec.allocate(_Cells); // throws

  0006d	48 8b 94 24 d8
	00 00 00	 mov	 rdx, QWORD PTR _Cells$[rsp]
  00075	48 8b 4c 24 68	 mov	 rcx, QWORD PTR _Alvec$[rsp]
  0007a	e8 00 00 00 00	 call	 ?allocate@?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@QEAAPEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@2@_K@Z ; std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > >::allocate
  0007f	48 89 44 24 38	 mov	 QWORD PTR _Newvec$3[rsp], rax

; 279  :         return static_cast<size_type>(_Mypair._Myval2._Myend - _Mypair._Myval2._Myfirst);

  00084	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0008c	48 8b 8c 24 d0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00094	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00097	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0009b	48 2b c1	 sub	 rax, rcx
  0009e	48 c1 f8 03	 sar	 rax, 3
  000a2	48 89 44 24 70	 mov	 QWORD PTR $T7[rsp], rax

; 289  :             // nothrow hereafter
; 290  :             const auto _Oldcapacity = capacity();

  000a7	48 8b 44 24 70	 mov	 rax, QWORD PTR $T7[rsp]
  000ac	48 89 44 24 40	 mov	 QWORD PTR _Oldcapacity$4[rsp], rax

; 291  :             if (_Oldcapacity != 0) {

  000b1	48 83 7c 24 40
	00		 cmp	 QWORD PTR _Oldcapacity$4[rsp], 0
  000b7	74 7a		 je	 SHORT $LN4@Assign_gro

; 292  :                 _Destroy_range(_Mypair._Myval2._Myfirst, _Mypair._Myval2._Mylast);

  000b9	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000c1	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  000c5	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR _Last$[rsp], rax
  000cd	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000d5	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000d8	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR _First$[rsp], rax

; 293  :                 _Alvec.deallocate(_Mypair._Myval2._Myfirst, _Oldcapacity);

  000e0	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000e8	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000eb	48 89 44 24 78	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  000f0	48 8b 44 24 40	 mov	 rax, QWORD PTR _Oldcapacity$4[rsp]
  000f5	48 c1 e0 03	 shl	 rax, 3
  000f9	48 89 44 24 28	 mov	 QWORD PTR _Bytes$[rsp], rax
  000fe	48 8b 44 24 78	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00103	48 89 44 24 48	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  00108	48 81 7c 24 28
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  00111	72 10		 jb	 SHORT $LN44@Assign_gro

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  00113	48 8d 54 24 28	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  00118	48 8d 4c 24 48	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  0011d	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  00122	90		 npad	 1
$LN44@Assign_gro:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  00123	48 8b 54 24 28	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  00128	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  0012d	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00132	90		 npad	 1
$LN4@Assign_gro:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash

; 296  :             _Mypair._Myval2._Myfirst = _Newvec;

  00133	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0013b	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Newvec$3[rsp]
  00140	48 89 08	 mov	 QWORD PTR [rax], rcx

; 297  :             const auto _Newend       = _Newvec + _Cells;

  00143	48 8b 44 24 38	 mov	 rax, QWORD PTR _Newvec$3[rsp]
  00148	48 8b 8c 24 d8
	00 00 00	 mov	 rcx, QWORD PTR _Cells$[rsp]
  00150	48 8d 04 c8	 lea	 rax, QWORD PTR [rax+rcx*8]
  00154	48 89 44 24 30	 mov	 QWORD PTR _Newend$2[rsp], rax

; 298  :             _Mypair._Myval2._Mylast  = _Newend;

  00159	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00161	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Newend$2[rsp]
  00166	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 299  :             _Mypair._Myval2._Myend   = _Newend;

  0016a	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00172	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Newend$2[rsp]
  00177	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 300  :             _STD uninitialized_fill(_Newvec, _Newend, _Val);

  0017b	4c 8d 84 24 e0
	00 00 00	 lea	 r8, QWORD PTR _Val$[rsp]
  00183	48 8b 54 24 30	 mov	 rdx, QWORD PTR _Newend$2[rsp]
  00188	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Newvec$3[rsp]
  0018d	e8 00 00 00 00	 call	 ??$uninitialized_fill@PEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@V12@@std@@YAXQEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@0@0AEBV10@@Z ; std::uninitialized_fill<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > *,std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > >
  00192	90		 npad	 1

; 301  :         } else {

  00193	e9 95 00 00 00	 jmp	 $LN3@Assign_gro
$LN2@Assign_gro:

; 302  :             _STD fill(_Mypair._Myval2._Myfirst, _Mypair._Myval2._Mylast, _Val);

  00198	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  001a0	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  001a4	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR _Last$[rsp], rax
  001ac	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  001b4	48 8b 00	 mov	 rax, QWORD PTR [rax]
  001b7	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _First$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 1382 :         return _It + 0;

  001bf	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _First$[rsp]
  001c7	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T8[rsp], rax

; 5292 :         auto _UFirst      = _STD _Get_unwrapped(_First);

  001cf	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T8[rsp]
  001d7	48 89 44 24 20	 mov	 QWORD PTR _UFirst$1[rsp], rax

; 1382 :         return _It + 0;

  001dc	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR _Last$[rsp]
  001e4	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax

; 5293 :         const auto _ULast = _STD _Get_unwrapped(_Last);

  001ec	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR $T9[rsp]
  001f4	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR _ULast$10[rsp], rax

; 5294 : #if _HAS_CXX20
; 5295 :         if (!_STD is_constant_evaluated())
; 5296 : #endif // _HAS_CXX20
; 5297 :         {
; 5298 :             if constexpr (_Fill_memset_is_safe<decltype(_UFirst), _Ty>) {
; 5299 :                 _STD _Fill_memset(_UFirst, _Val, static_cast<size_t>(_ULast - _UFirst));
; 5300 :                 return;
; 5301 :             } else if constexpr (_Fill_zero_memset_is_safe<decltype(_UFirst), _Ty>) {
; 5302 :                 if (_STD _Is_all_bits_zero(_Val)) {
; 5303 :                     _STD _Fill_zero_memset(_UFirst, static_cast<size_t>(_ULast - _UFirst));
; 5304 :                     return;
; 5305 :                 }
; 5306 :             }
; 5307 :         }
; 5308 : 
; 5309 :         for (; _UFirst != _ULast; ++_UFirst) {

  001fc	eb 0e		 jmp	 SHORT $LN56@Assign_gro
$LN54@Assign_gro:
  001fe	48 8b 44 24 20	 mov	 rax, QWORD PTR _UFirst$1[rsp]
  00203	48 83 c0 08	 add	 rax, 8
  00207	48 89 44 24 20	 mov	 QWORD PTR _UFirst$1[rsp], rax
$LN56@Assign_gro:
  0020c	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR _ULast$10[rsp]
  00214	48 39 44 24 20	 cmp	 QWORD PTR _UFirst$1[rsp], rax
  00219	74 12		 je	 SHORT $LN3@Assign_gro

; 5310 :             *_UFirst = _Val;

  0021b	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR _Val$[rsp]
  00223	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _UFirst$1[rsp]
  00228	48 89 01	 mov	 QWORD PTR [rcx], rax

; 5311 :         }

  0022b	eb d1		 jmp	 SHORT $LN54@Assign_gro
$LN3@Assign_gro:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash

; 304  :     }

  0022d	48 81 c4 c8 00
	00 00		 add	 rsp, 200		; 000000c8H
  00234	c3		 ret	 0
?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@2@@Z ENDP ; std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > > >::_Assign_grow
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?allocate@?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@QEAAPEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@2@_K@Z
_TEXT	SEGMENT
_Overflow_is_possible$1 = 32
_Bytes$ = 40
$T2 = 48
$T3 = 56
$T4 = 64
_Max_possible$5 = 72
this$ = 96
_Count$ = 104
?allocate@?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@QEAAPEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@2@_K@Z PROC ; std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > >::allocate, COMDAT

; 988  :     _NODISCARD_RAW_PTR_ALLOC _CONSTEXPR20 __declspec(allocator) _Ty* allocate(_CRT_GUARDOVERFLOW const size_t _Count) {

$LN13:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 113  :     constexpr bool _Overflow_is_possible = _Ty_size > 1;

  0000e	c6 44 24 20 01	 mov	 BYTE PTR _Overflow_is_possible$1[rsp], 1

; 114  : 
; 115  :     if constexpr (_Overflow_is_possible) {
; 116  :         constexpr size_t _Max_possible = static_cast<size_t>(-1) / _Ty_size;

  00013	48 b8 ff ff ff
	ff ff ff ff 1f	 mov	 rax, 2305843009213693951 ; 1fffffffffffffffH
  0001d	48 89 44 24 48	 mov	 QWORD PTR _Max_possible$5[rsp], rax

; 117  :         if (_Count > _Max_possible) {

  00022	48 b8 ff ff ff
	ff ff ff ff 1f	 mov	 rax, 2305843009213693951 ; 1fffffffffffffffH
  0002c	48 39 44 24 68	 cmp	 QWORD PTR _Count$[rsp], rax
  00031	76 06		 jbe	 SHORT $LN4@allocate

; 118  :             _Throw_bad_array_new_length(); // multiply overflow

  00033	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00038	90		 npad	 1
$LN4@allocate:

; 119  :         }
; 120  :     }
; 121  : 
; 122  :     return _Count * _Ty_size;

  00039	48 8b 44 24 68	 mov	 rax, QWORD PTR _Count$[rsp]
  0003e	48 c1 e0 03	 shl	 rax, 3
  00042	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00047	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
  0004c	48 89 44 24 28	 mov	 QWORD PTR _Bytes$[rsp], rax

; 227  :     if (_Bytes == 0) {

  00051	48 83 7c 24 28
	00		 cmp	 QWORD PTR _Bytes$[rsp], 0
  00057	75 0b		 jne	 SHORT $LN8@allocate

; 228  :         return nullptr;

  00059	48 c7 44 24 30
	00 00 00 00	 mov	 QWORD PTR $T2[rsp], 0
  00062	eb 35		 jmp	 SHORT $LN7@allocate
$LN8@allocate:

; 229  :     }
; 230  : 
; 231  : #if _HAS_CXX20 // TRANSITION, GH-1532
; 232  :     if (_STD is_constant_evaluated()) {
; 233  :         return _Traits::_Allocate(_Bytes);
; 234  :     }
; 235  : #endif // _HAS_CXX20
; 236  : 
; 237  : #ifdef __cpp_aligned_new
; 238  :     if constexpr (_Align > __STDCPP_DEFAULT_NEW_ALIGNMENT__) {
; 239  :         size_t _Passed_align = _Align;
; 240  : #if defined(_M_IX86) || defined(_M_X64)
; 241  :         if (_Bytes >= _Big_allocation_threshold) {
; 242  :             // boost the alignment of big allocations to help autovectorization
; 243  :             _Passed_align = (_STD max)(_Align, _Big_allocation_alignment);
; 244  :         }
; 245  : #endif // defined(_M_IX86) || defined(_M_X64)
; 246  :         return _Traits::_Allocate_aligned(_Bytes, _Passed_align);
; 247  :     } else
; 248  : #endif // defined(__cpp_aligned_new)
; 249  :     {
; 250  : #if defined(_M_IX86) || defined(_M_X64)
; 251  :         if (_Bytes >= _Big_allocation_threshold) {

  00064	48 81 7c 24 28
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  0006d	72 11		 jb	 SHORT $LN9@allocate

; 252  :             // boost the alignment of big allocations to help autovectorization
; 253  :             return _Allocate_manually_vector_aligned<_Traits>(_Bytes);

  0006f	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00074	e8 00 00 00 00	 call	 ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
  00079	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
  0007e	eb 19		 jmp	 SHORT $LN7@allocate
$LN9@allocate:

; 136  :         return ::operator new(_Bytes);

  00080	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00085	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  0008a	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax

; 256  :         return _Traits::_Allocate(_Bytes);

  0008f	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  00094	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
$LN7@allocate:

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00099	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
$LN6@allocate:

; 991  :     }

  0009e	48 83 c4 58	 add	 rsp, 88			; 00000058H
  000a2	c3		 ret	 0
?allocate@?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@QEAAPEAV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@2@_K@Z ENDP ; std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > >::allocate
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash
;	COMDAT ?clear@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAXXZ
_TEXT	SEGMENT
_UFirst$1 = 32
_Oldsize$ = 40
_Head$2 = 48
$T3 = 56
__param0$ = 64
$T4 = 72
_First$ = 80
$T5 = 88
_Last$ = 96
$T6 = 104
_ULast$7 = 112
$T8 = 120
this$ = 144
?clear@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAXXZ PROC ; std::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::clear, COMDAT

; 1155 :     void clear() noexcept {

$LN336:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H

; 1156 :         // TRANSITION, ABI:
; 1157 :         // LWG-2550 requires implementations to make clear() O(size()), independent of bucket_count().
; 1158 :         // Unfortunately our current data structure / ABI does not allow achieving this in the general case because:
; 1159 :         //   (1) Finding the bucket that goes with an element requires running the hash function
; 1160 :         //   (2) The hash function operator() may throw exceptions, and
; 1161 :         //   (3) clear() is a noexcept function.
; 1162 :         // We do comply with LWG-2550 if the hash function is noexcept, or if the container was empty.
; 1163 :         const auto _Oldsize = _List._Mypair._Myval2._Mysize;

  0000c	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00018	48 89 44 24 28	 mov	 QWORD PTR _Oldsize$[rsp], rax

; 1164 :         if (_Oldsize == 0) {

  0001d	48 83 7c 24 28
	00		 cmp	 QWORD PTR _Oldsize$[rsp], 0
  00023	75 05		 jne	 SHORT $LN2@clear

; 1165 :             return;

  00025	e9 0a 01 00 00	 jmp	 $LN1@clear
$LN2@clear:

; 851  :         return _Maxidx;

  0002a	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00032	48 8b 40 38	 mov	 rax, QWORD PTR [rax+56]
  00036	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax

; 1166 :         }
; 1167 : 
; 1168 :         if constexpr (_Nothrow_hash<_Traits, key_type>) {
; 1169 :             // In testing, hash<size_t>{}(size_t{}) takes about 14 times as much time as assigning a pointer, or
; 1170 :             // ~7-8 times as much as clearing a bucket. Therefore, if we would need to assign over more than 8 times
; 1171 :             // as many buckets as elements, remove element-by-element.
; 1172 :             if (bucket_count() / 8 > _Oldsize) {

  0003b	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
  00040	33 d2		 xor	 edx, edx
  00042	b9 08 00 00 00	 mov	 ecx, 8
  00047	48 f7 f1	 div	 rcx
  0004a	48 3b 44 24 28	 cmp	 rax, QWORD PTR _Oldsize$[rsp]
  0004f	76 31		 jbe	 SHORT $LN3@clear

; 1173 :                 const auto _Head = _List._Mypair._Myval2._Myhead;

  00051	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00059	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0005d	48 89 44 24 30	 mov	 QWORD PTR _Head$2[rsp], rax

; 1174 :                 _Unchecked_erase(_Head->_Next, _Head);

  00062	4c 8b 44 24 30	 mov	 r8, QWORD PTR _Head$2[rsp]
  00067	48 8b 44 24 30	 mov	 rax, QWORD PTR _Head$2[rsp]
  0006c	48 8b 10	 mov	 rdx, QWORD PTR [rax]
  0006f	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00077	e8 00 00 00 00	 call	 ?_Unchecked_erase@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@AEAAPEAU?$_List_node@U?$pair@$$CBII@std@@PEAX@2@PEAU32@QEAU32@@Z ; std::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Unchecked_erase
  0007c	90		 npad	 1

; 1175 :                 return;

  0007d	e9 b2 00 00 00	 jmp	 $LN1@clear
$LN3@clear:

; 1176 :             }
; 1177 :         }
; 1178 : 
; 1179 :         // Bulk destroy items and reset buckets
; 1180 :         _List.clear();

  00082	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0008a	48 83 c0 08	 add	 rax, 8
  0008e	48 8b c8	 mov	 rcx, rax
  00091	e8 00 00 00 00	 call	 ?clear@?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAAXXZ ; std::list<std::pair<unsigned int const ,unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >::clear
  00096	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 1129 :         return _Unchecked_iterator(_Mypair._Myval2._Myhead, nullptr);

  00097	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0009f	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  000a3	48 89 44 24 40	 mov	 QWORD PTR __param0$[rsp], rax

; 37   :     _List_unchecked_const_iterator(_Nodeptr _Pnode, const _Mylist* _Plist) noexcept : _Ptr(_Pnode) {

  000a8	48 8b 44 24 40	 mov	 rax, QWORD PTR __param0$[rsp]
  000ad	48 89 44 24 48	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash

; 816  :         return _List._Unchecked_end();

  000b2	48 8d 44 24 48	 lea	 rax, QWORD PTR $T4[rsp]
  000b7	48 89 44 24 78	 mov	 QWORD PTR $T8[rsp], rax

; 1181 :         _STD fill(_Vec._Mypair._Myval2._Myfirst, _Vec._Mypair._Myval2._Mylast, _Unchecked_end());

  000bc	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000c4	48 8b 40 20	 mov	 rax, QWORD PTR [rax+32]
  000c8	48 89 44 24 60	 mov	 QWORD PTR _Last$[rsp], rax
  000cd	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000d5	48 8b 40 18	 mov	 rax, QWORD PTR [rax+24]
  000d9	48 89 44 24 50	 mov	 QWORD PTR _First$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 1382 :         return _It + 0;

  000de	48 8b 44 24 50	 mov	 rax, QWORD PTR _First$[rsp]
  000e3	48 89 44 24 58	 mov	 QWORD PTR $T5[rsp], rax

; 5292 :         auto _UFirst      = _STD _Get_unwrapped(_First);

  000e8	48 8b 44 24 58	 mov	 rax, QWORD PTR $T5[rsp]
  000ed	48 89 44 24 20	 mov	 QWORD PTR _UFirst$1[rsp], rax

; 1382 :         return _It + 0;

  000f2	48 8b 44 24 60	 mov	 rax, QWORD PTR _Last$[rsp]
  000f7	48 89 44 24 68	 mov	 QWORD PTR $T6[rsp], rax

; 5293 :         const auto _ULast = _STD _Get_unwrapped(_Last);

  000fc	48 8b 44 24 68	 mov	 rax, QWORD PTR $T6[rsp]
  00101	48 89 44 24 70	 mov	 QWORD PTR _ULast$7[rsp], rax

; 5294 : #if _HAS_CXX20
; 5295 :         if (!_STD is_constant_evaluated())
; 5296 : #endif // _HAS_CXX20
; 5297 :         {
; 5298 :             if constexpr (_Fill_memset_is_safe<decltype(_UFirst), _Ty>) {
; 5299 :                 _STD _Fill_memset(_UFirst, _Val, static_cast<size_t>(_ULast - _UFirst));
; 5300 :                 return;
; 5301 :             } else if constexpr (_Fill_zero_memset_is_safe<decltype(_UFirst), _Ty>) {
; 5302 :                 if (_STD _Is_all_bits_zero(_Val)) {
; 5303 :                     _STD _Fill_zero_memset(_UFirst, static_cast<size_t>(_ULast - _UFirst));
; 5304 :                     return;
; 5305 :                 }
; 5306 :             }
; 5307 :         }
; 5308 : 
; 5309 :         for (; _UFirst != _ULast; ++_UFirst) {

  00106	eb 0e		 jmp	 SHORT $LN322@clear
$LN320@clear:
  00108	48 8b 44 24 20	 mov	 rax, QWORD PTR _UFirst$1[rsp]
  0010d	48 83 c0 08	 add	 rax, 8
  00111	48 89 44 24 20	 mov	 QWORD PTR _UFirst$1[rsp], rax
$LN322@clear:
  00116	48 8b 44 24 70	 mov	 rax, QWORD PTR _ULast$7[rsp]
  0011b	48 39 44 24 20	 cmp	 QWORD PTR _UFirst$1[rsp], rax
  00120	74 12		 je	 SHORT $LN1@clear

; 5310 :             *_UFirst = _Val;

  00122	48 8b 44 24 78	 mov	 rax, QWORD PTR $T8[rsp]
  00127	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0012a	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _UFirst$1[rsp]
  0012f	48 89 01	 mov	 QWORD PTR [rcx], rax

; 5311 :         }

  00132	eb d4		 jmp	 SHORT $LN320@clear
$LN1@clear:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash

; 1182 :     }

  00134	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  0013b	c3		 ret	 0
?clear@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAXXZ ENDP ; std::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::clear
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\unordered_map
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\unordered_map
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash
;	COMDAT ?_Unchecked_erase@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@AEAAPEAU?$_List_node@U?$pair@$$CBII@std@@PEAX@2@PEAU32@QEAU32@@Z
_TEXT	SEGMENT
tv136 = 32
tv138 = 33
_Update_lo$1 = 34
tv172 = 35
_At_bucket_back$2 = 36
_At_bucket_back$3 = 37
_Eraser$ = 40
_Bucket_bounds$ = 64
_End$ = 72
_Bucket_lo$4 = 80
_Bucket_hi$5 = 88
_Bucket$6 = 96
_Bucket$7 = 104
_Bucket_lo$8 = 112
_Bucket_hi$9 = 120
_Val$ = 128
$T10 = 136
$T11 = 144
$T12 = 152
$T13 = 160
_Old_hi$14 = 168
$T15 = 176
_Predecessor$16 = 184
_Val$ = 192
$T17 = 200
$T18 = 208
$T19 = 216
$T20 = 224
$T21 = 232
_Old_hi$22 = 240
$T23 = 248
$T24 = 256
$T25 = 264
this$ = 288
_First$ = 296
_Last$ = 304
?_Unchecked_erase@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@AEAAPEAU?$_List_node@U?$pair@$$CBII@std@@PEAX@2@PEAU32@QEAU32@@Z PROC ; std::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Unchecked_erase, COMDAT

; 1031 :     _Nodeptr _Unchecked_erase(_Nodeptr _First, const _Nodeptr _Last) noexcept(_Nothrow_hash<_Traits, key_type>) {

$LN220:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 81 ec 18 01
	00 00		 sub	 rsp, 280		; 00000118H

; 1032 :         if (_First == _Last) {

  00016	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR _Last$[rsp]
  0001e	48 39 84 24 28
	01 00 00	 cmp	 QWORD PTR _First$[rsp], rax
  00026	75 0d		 jne	 SHORT $LN10@Unchecked_

; 1033 :             return _Last;

  00028	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR _Last$[rsp]
  00030	e9 cd 03 00 00	 jmp	 $LN1@Unchecked_
$LN10@Unchecked_:

; 1034 :         }
; 1035 : 
; 1036 :         const auto _End           = _List._Mypair._Myval2._Myhead;

  00035	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0003d	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00041	48 89 44 24 48	 mov	 QWORD PTR _End$[rsp], rax

; 1037 :         const auto _Bucket_bounds = _Vec._Mypair._Myval2._Myfirst;

  00046	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 8b 40 18	 mov	 rax, QWORD PTR [rax+24]
  00052	48 89 44 24 40	 mov	 QWORD PTR _Bucket_bounds$[rsp], rax

; 1038 :         _Range_eraser _Eraser{_List, _First};

  00057	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0005f	48 83 c0 08	 add	 rax, 8

; 1011 :             : _List(_List_), _Predecessor(_First_->_Prev), _Next(_First_) {}

  00063	48 89 44 24 28	 mov	 QWORD PTR _Eraser$[rsp], rax
  00068	48 8b 84 24 28
	01 00 00	 mov	 rax, QWORD PTR _First$[rsp]
  00070	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00074	48 89 44 24 30	 mov	 QWORD PTR _Eraser$[rsp+8], rax
  00079	48 8b 84 24 28
	01 00 00	 mov	 rax, QWORD PTR _First$[rsp]
  00081	48 89 44 24 38	 mov	 QWORD PTR _Eraser$[rsp+16], rax

; 1039 :         {
; 1040 :             // process the first bucket, which is special because here _First might not be the beginning of the bucket
; 1041 :             const auto _Predecessor = _First->_Prev;

  00086	48 8b 84 24 28
	01 00 00	 mov	 rax, QWORD PTR _First$[rsp]
  0008e	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00092	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR _Predecessor$16[rsp], rax

; 1042 :             const size_type _Bucket = bucket(_Traits::_Kfn(_Eraser._Next->_Myval)); // throws

  0009a	48 8b 44 24 38	 mov	 rax, QWORD PTR _Eraser$[rsp+16]
  0009f	48 83 c0 10	 add	 rax, 16
  000a3	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _Val$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\unordered_map

; 55   :         return _Val.first;

  000ab	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _Val$[rsp]
  000b3	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash

; 1042 :             const size_type _Bucket = bucket(_Traits::_Kfn(_Eraser._Next->_Myval)); // throws

  000bb	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 2324 :         return _Hash_representation(_Keyval);

  000c3	48 8b c8	 mov	 rcx, rax
  000c6	e8 00 00 00 00	 call	 ??$_Hash_representation@I@std@@YA_KAEBI@Z ; std::_Hash_representation<unsigned int>
  000cb	48 89 84 24 08
	01 00 00	 mov	 QWORD PTR $T25[rsp], rax

; 2306 :         return hash<_Kty>::_Do_hash(_Keyval);

  000d3	48 8b 84 24 08
	01 00 00	 mov	 rax, QWORD PTR $T25[rsp]
  000db	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash

; 143  :         return static_cast<size_t>(_Mypair._Get_first()(_Keyval));

  000e3	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  000eb	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax

; 860  :         return _Traitsobj(_Keyval) & _Mask;

  000f3	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
  000fb	48 8b 8c 24 20
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00103	48 23 41 30	 and	 rax, QWORD PTR [rcx+48]
  00107	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax

; 1042 :             const size_type _Bucket = bucket(_Traits::_Kfn(_Eraser._Next->_Myval)); // throws

  0010f	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR $T13[rsp]
  00117	48 89 44 24 60	 mov	 QWORD PTR _Bucket$6[rsp], rax

; 1043 :             // nothrow hereafter this block
; 1044 :             _Nodeptr& _Bucket_lo   = _Bucket_bounds[_Bucket << 1]._Ptr;

  0011c	48 8b 44 24 60	 mov	 rax, QWORD PTR _Bucket$6[rsp]
  00121	48 d1 e0	 shl	 rax, 1
  00124	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Bucket_bounds$[rsp]
  00129	48 8d 04 c1	 lea	 rax, QWORD PTR [rcx+rax*8]
  0012d	48 89 44 24 50	 mov	 QWORD PTR _Bucket_lo$4[rsp], rax

; 1045 :             _Nodeptr& _Bucket_hi   = _Bucket_bounds[(_Bucket << 1) + 1]._Ptr;

  00132	48 8b 44 24 60	 mov	 rax, QWORD PTR _Bucket$6[rsp]
  00137	48 d1 e0	 shl	 rax, 1
  0013a	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Bucket_bounds$[rsp]
  0013f	48 8d 44 c1 08	 lea	 rax, QWORD PTR [rcx+rax*8+8]
  00144	48 89 44 24 58	 mov	 QWORD PTR _Bucket_hi$5[rsp], rax

; 1046 :             const bool _Update_lo  = _Bucket_lo == _Eraser._Next;

  00149	48 8b 44 24 50	 mov	 rax, QWORD PTR _Bucket_lo$4[rsp]
  0014e	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Eraser$[rsp+16]
  00153	48 39 08	 cmp	 QWORD PTR [rax], rcx
  00156	75 07		 jne	 SHORT $LN19@Unchecked_
  00158	c6 44 24 20 01	 mov	 BYTE PTR tv136[rsp], 1
  0015d	eb 05		 jmp	 SHORT $LN20@Unchecked_
$LN19@Unchecked_:
  0015f	c6 44 24 20 00	 mov	 BYTE PTR tv136[rsp], 0
$LN20@Unchecked_:
  00164	0f b6 44 24 20	 movzx	 eax, BYTE PTR tv136[rsp]
  00169	88 44 24 22	 mov	 BYTE PTR _Update_lo$1[rsp], al

; 1047 :             const _Nodeptr _Old_hi = _Bucket_hi;

  0016d	48 8b 44 24 58	 mov	 rax, QWORD PTR _Bucket_hi$5[rsp]
  00172	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00175	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR _Old_hi$14[rsp], rax
$LN2@Unchecked_:

; 1048 :             for (;;) { // remove elements until we hit the end of the bucket
; 1049 :                 const bool _At_bucket_back = _Eraser._Next == _Old_hi;

  0017d	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR _Old_hi$14[rsp]
  00185	48 39 44 24 38	 cmp	 QWORD PTR _Eraser$[rsp+16], rax
  0018a	75 07		 jne	 SHORT $LN21@Unchecked_
  0018c	c6 44 24 21 01	 mov	 BYTE PTR tv138[rsp], 1
  00191	eb 05		 jmp	 SHORT $LN22@Unchecked_
$LN21@Unchecked_:
  00193	c6 44 24 21 00	 mov	 BYTE PTR tv138[rsp], 0
$LN22@Unchecked_:
  00198	0f b6 44 24 21	 movzx	 eax, BYTE PTR tv138[rsp]
  0019d	88 44 24 24	 mov	 BYTE PTR _At_bucket_back$2[rsp], al

; 1050 :                 _Eraser._Bump_erased();

  001a1	48 8d 4c 24 28	 lea	 rcx, QWORD PTR _Eraser$[rsp]
  001a6	e8 00 00 00 00	 call	 ?_Bump_erased@_Range_eraser@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAXXZ ; std::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Range_eraser::_Bump_erased
  001ab	90		 npad	 1

; 1051 :                 if (_At_bucket_back) {

  001ac	0f b6 44 24 24	 movzx	 eax, BYTE PTR _At_bucket_back$2[rsp]
  001b1	85 c0		 test	 eax, eax
  001b3	74 02		 je	 SHORT $LN11@Unchecked_

; 1052 :                     break;

  001b5	eb 62		 jmp	 SHORT $LN3@Unchecked_
$LN11@Unchecked_:

; 1053 :                 }
; 1054 : 
; 1055 :                 if (_Eraser._Next == _Last) {

  001b7	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR _Last$[rsp]
  001bf	48 39 44 24 38	 cmp	 QWORD PTR _Eraser$[rsp+16], rax
  001c4	75 4e		 jne	 SHORT $LN12@Unchecked_

; 1056 :                     if (_Update_lo) {

  001c6	0f b6 44 24 22	 movzx	 eax, BYTE PTR _Update_lo$1[rsp]
  001cb	85 c0		 test	 eax, eax
  001cd	74 0d		 je	 SHORT $LN13@Unchecked_

; 1057 :                         // erased the bucket's prefix
; 1058 :                         _Bucket_lo = _Eraser._Next;

  001cf	48 8b 44 24 50	 mov	 rax, QWORD PTR _Bucket_lo$4[rsp]
  001d4	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Eraser$[rsp+16]
  001d9	48 89 08	 mov	 QWORD PTR [rax], rcx
$LN13@Unchecked_:

; 1059 :                     }
; 1060 : 
; 1061 :                     return _Last;

  001dc	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR _Last$[rsp]
  001e4	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T15[rsp], rax

; 1021 :             _Predecessor->_Next = _Next;

  001ec	48 8b 44 24 30	 mov	 rax, QWORD PTR _Eraser$[rsp+8]
  001f1	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Eraser$[rsp+16]
  001f6	48 89 08	 mov	 QWORD PTR [rax], rcx

; 1022 :             _Next->_Prev        = _Predecessor;

  001f9	48 8b 44 24 38	 mov	 rax, QWORD PTR _Eraser$[rsp+16]
  001fe	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Eraser$[rsp+8]
  00203	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 1059 :                     }
; 1060 : 
; 1061 :                     return _Last;

  00207	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR $T15[rsp]
  0020f	e9 ee 01 00 00	 jmp	 $LN1@Unchecked_
$LN12@Unchecked_:

; 1062 :                 }
; 1063 :             }

  00214	e9 64 ff ff ff	 jmp	 $LN2@Unchecked_
$LN3@Unchecked_:

; 1064 : 
; 1065 :             if (_Update_lo) {

  00219	0f b6 44 24 22	 movzx	 eax, BYTE PTR _Update_lo$1[rsp]
  0021e	85 c0		 test	 eax, eax
  00220	74 1c		 je	 SHORT $LN14@Unchecked_

; 1066 :                 // emptied the bucket
; 1067 :                 _Bucket_lo = _End;

  00222	48 8b 44 24 50	 mov	 rax, QWORD PTR _Bucket_lo$4[rsp]
  00227	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _End$[rsp]
  0022c	48 89 08	 mov	 QWORD PTR [rax], rcx

; 1068 :                 _Bucket_hi = _End;

  0022f	48 8b 44 24 58	 mov	 rax, QWORD PTR _Bucket_hi$5[rsp]
  00234	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _End$[rsp]
  00239	48 89 08	 mov	 QWORD PTR [rax], rcx

; 1069 :             } else {

  0023c	eb 10		 jmp	 SHORT $LN5@Unchecked_
$LN14@Unchecked_:

; 1070 :                 _Bucket_hi = _Predecessor;

  0023e	48 8b 44 24 58	 mov	 rax, QWORD PTR _Bucket_hi$5[rsp]
  00243	48 8b 8c 24 b8
	00 00 00	 mov	 rcx, QWORD PTR _Predecessor$16[rsp]
  0024b	48 89 08	 mov	 QWORD PTR [rax], rcx
$LN5@Unchecked_:

; 1071 :             }
; 1072 :         }
; 1073 : 
; 1074 :         // hereafter we are always erasing buckets' prefixes
; 1075 :         while (_Eraser._Next != _Last) {

  0024e	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR _Last$[rsp]
  00256	48 39 44 24 38	 cmp	 QWORD PTR _Eraser$[rsp+16], rax
  0025b	0f 84 6e 01 00
	00		 je	 $LN6@Unchecked_

; 1076 :             const size_type _Bucket = bucket(_Traits::_Kfn(_Eraser._Next->_Myval)); // throws

  00261	48 8b 44 24 38	 mov	 rax, QWORD PTR _Eraser$[rsp+16]
  00266	48 83 c0 10	 add	 rax, 16
  0026a	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR _Val$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\unordered_map

; 55   :         return _Val.first;

  00272	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR _Val$[rsp]
  0027a	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T17[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash

; 1076 :             const size_type _Bucket = bucket(_Traits::_Kfn(_Eraser._Next->_Myval)); // throws

  00282	48 8b 84 24 c8
	00 00 00	 mov	 rax, QWORD PTR $T17[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 2324 :         return _Hash_representation(_Keyval);

  0028a	48 8b c8	 mov	 rcx, rax
  0028d	e8 00 00 00 00	 call	 ??$_Hash_representation@I@std@@YA_KAEBI@Z ; std::_Hash_representation<unsigned int>
  00292	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR $T18[rsp], rax

; 2306 :         return hash<_Kty>::_Do_hash(_Keyval);

  0029a	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR $T18[rsp]
  002a2	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR $T19[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash

; 143  :         return static_cast<size_t>(_Mypair._Get_first()(_Keyval));

  002aa	48 8b 84 24 d8
	00 00 00	 mov	 rax, QWORD PTR $T19[rsp]
  002b2	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR $T20[rsp], rax

; 860  :         return _Traitsobj(_Keyval) & _Mask;

  002ba	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR $T20[rsp]
  002c2	48 8b 8c 24 20
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  002ca	48 23 41 30	 and	 rax, QWORD PTR [rcx+48]
  002ce	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR $T21[rsp], rax

; 1076 :             const size_type _Bucket = bucket(_Traits::_Kfn(_Eraser._Next->_Myval)); // throws

  002d6	48 8b 84 24 e8
	00 00 00	 mov	 rax, QWORD PTR $T21[rsp]
  002de	48 89 44 24 68	 mov	 QWORD PTR _Bucket$7[rsp], rax

; 1077 :             // nothrow hereafter this block
; 1078 :             _Nodeptr& _Bucket_lo   = _Bucket_bounds[_Bucket << 1]._Ptr;

  002e3	48 8b 44 24 68	 mov	 rax, QWORD PTR _Bucket$7[rsp]
  002e8	48 d1 e0	 shl	 rax, 1
  002eb	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Bucket_bounds$[rsp]
  002f0	48 8d 04 c1	 lea	 rax, QWORD PTR [rcx+rax*8]
  002f4	48 89 44 24 70	 mov	 QWORD PTR _Bucket_lo$8[rsp], rax

; 1079 :             _Nodeptr& _Bucket_hi   = _Bucket_bounds[(_Bucket << 1) + 1]._Ptr;

  002f9	48 8b 44 24 68	 mov	 rax, QWORD PTR _Bucket$7[rsp]
  002fe	48 d1 e0	 shl	 rax, 1
  00301	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Bucket_bounds$[rsp]
  00306	48 8d 44 c1 08	 lea	 rax, QWORD PTR [rcx+rax*8+8]
  0030b	48 89 44 24 78	 mov	 QWORD PTR _Bucket_hi$9[rsp], rax

; 1080 :             const _Nodeptr _Old_hi = _Bucket_hi;

  00310	48 8b 44 24 78	 mov	 rax, QWORD PTR _Bucket_hi$9[rsp]
  00315	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00318	48 89 84 24 f0
	00 00 00	 mov	 QWORD PTR _Old_hi$22[rsp], rax
$LN7@Unchecked_:

; 1081 :             for (;;) { // remove elements until we hit the end of the bucket
; 1082 :                 const bool _At_bucket_back = _Eraser._Next == _Old_hi;

  00320	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR _Old_hi$22[rsp]
  00328	48 39 44 24 38	 cmp	 QWORD PTR _Eraser$[rsp+16], rax
  0032d	75 07		 jne	 SHORT $LN23@Unchecked_
  0032f	c6 44 24 23 01	 mov	 BYTE PTR tv172[rsp], 1
  00334	eb 05		 jmp	 SHORT $LN24@Unchecked_
$LN23@Unchecked_:
  00336	c6 44 24 23 00	 mov	 BYTE PTR tv172[rsp], 0
$LN24@Unchecked_:
  0033b	0f b6 44 24 23	 movzx	 eax, BYTE PTR tv172[rsp]
  00340	88 44 24 25	 mov	 BYTE PTR _At_bucket_back$3[rsp], al

; 1083 :                 _Eraser._Bump_erased();

  00344	48 8d 4c 24 28	 lea	 rcx, QWORD PTR _Eraser$[rsp]
  00349	e8 00 00 00 00	 call	 ?_Bump_erased@_Range_eraser@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAXXZ ; std::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Range_eraser::_Bump_erased
  0034e	90		 npad	 1

; 1084 :                 if (_At_bucket_back) {

  0034f	0f b6 44 24 25	 movzx	 eax, BYTE PTR _At_bucket_back$3[rsp]
  00354	85 c0		 test	 eax, eax
  00356	74 02		 je	 SHORT $LN16@Unchecked_

; 1085 :                     break;

  00358	eb 56		 jmp	 SHORT $LN8@Unchecked_
$LN16@Unchecked_:

; 1086 :                 }
; 1087 : 
; 1088 :                 if (_Eraser._Next == _Last) {

  0035a	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR _Last$[rsp]
  00362	48 39 44 24 38	 cmp	 QWORD PTR _Eraser$[rsp+16], rax
  00367	75 42		 jne	 SHORT $LN17@Unchecked_

; 1089 :                     // erased the bucket's prefix
; 1090 :                     _Bucket_lo = _Eraser._Next;

  00369	48 8b 44 24 70	 mov	 rax, QWORD PTR _Bucket_lo$8[rsp]
  0036e	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Eraser$[rsp+16]
  00373	48 89 08	 mov	 QWORD PTR [rax], rcx

; 1091 :                     return _Last;

  00376	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR _Last$[rsp]
  0037e	48 89 84 24 f8
	00 00 00	 mov	 QWORD PTR $T23[rsp], rax

; 1021 :             _Predecessor->_Next = _Next;

  00386	48 8b 44 24 30	 mov	 rax, QWORD PTR _Eraser$[rsp+8]
  0038b	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Eraser$[rsp+16]
  00390	48 89 08	 mov	 QWORD PTR [rax], rcx

; 1022 :             _Next->_Prev        = _Predecessor;

  00393	48 8b 44 24 38	 mov	 rax, QWORD PTR _Eraser$[rsp+16]
  00398	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Eraser$[rsp+8]
  0039d	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 1091 :                     return _Last;

  003a1	48 8b 84 24 f8
	00 00 00	 mov	 rax, QWORD PTR $T23[rsp]
  003a9	eb 57		 jmp	 SHORT $LN1@Unchecked_
$LN17@Unchecked_:

; 1092 :                 }
; 1093 :             }

  003ab	e9 70 ff ff ff	 jmp	 $LN7@Unchecked_
$LN8@Unchecked_:

; 1094 : 
; 1095 :             // emptied the bucket
; 1096 :             _Bucket_lo = _End;

  003b0	48 8b 44 24 70	 mov	 rax, QWORD PTR _Bucket_lo$8[rsp]
  003b5	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _End$[rsp]
  003ba	48 89 08	 mov	 QWORD PTR [rax], rcx

; 1097 :             _Bucket_hi = _End;

  003bd	48 8b 44 24 78	 mov	 rax, QWORD PTR _Bucket_hi$9[rsp]
  003c2	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _End$[rsp]
  003c7	48 89 08	 mov	 QWORD PTR [rax], rcx

; 1098 :         }

  003ca	e9 7f fe ff ff	 jmp	 $LN5@Unchecked_
$LN6@Unchecked_:

; 1099 : 
; 1100 :         return _Last;

  003cf	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR _Last$[rsp]
  003d7	48 89 84 24 00
	01 00 00	 mov	 QWORD PTR $T24[rsp], rax

; 1021 :             _Predecessor->_Next = _Next;

  003df	48 8b 44 24 30	 mov	 rax, QWORD PTR _Eraser$[rsp+8]
  003e4	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Eraser$[rsp+16]
  003e9	48 89 08	 mov	 QWORD PTR [rax], rcx

; 1022 :             _Next->_Prev        = _Predecessor;

  003ec	48 8b 44 24 38	 mov	 rax, QWORD PTR _Eraser$[rsp+16]
  003f1	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Eraser$[rsp+8]
  003f6	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 1099 : 
; 1100 :         return _Last;

  003fa	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR $T24[rsp]
$LN1@Unchecked_:

; 1101 :     }

  00402	48 81 c4 18 01
	00 00		 add	 rsp, 280		; 00000118H
  00409	c3		 ret	 0
?_Unchecked_erase@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@AEAAPEAU?$_List_node@U?$pair@$$CBII@std@@PEAX@2@PEAU32@QEAU32@@Z ENDP ; std::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Unchecked_erase
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\unordered_map
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash
;	COMDAT ??0?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA@AEBV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@1@AEBV?$allocator@U?$pair@$$CBII@std@@@1@@Z
_TEXT	SEGMENT
$T1 = 32
this$ = 40
this$ = 48
this$ = 56
this$ = 64
this$ = 72
this$ = 80
$T2 = 88
this$ = 96
$T3 = 104
$T4 = 112
__param0$ = 120
$T5 = 128
$T6 = 136
$T7 = 144
$T8 = 152
this$ = 176
_Parg$ = 184
_Al$ = 192
??0?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA@AEBV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@1@AEBV?$allocator@U?$pair@$$CBII@std@@@1@@Z PROC ; std::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >, COMDAT

; 371  :         : _Traitsobj(_Parg), _List(_Al), _Vec(_Al), _Mask(_Min_buckets - 1), _Maxidx(_Min_buckets) {

$LN200:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	57		 push	 rdi
  00010	48 81 ec a0 00
	00 00		 sub	 rsp, 160		; 000000a0H
  00017	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0001f	48 89 44 24 40	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\unordered_map

; 49   :     explicit _Umap_traits(const _Tr& _Traits) noexcept(is_nothrow_copy_constructible_v<_Tr>) : _Tr(_Traits) {}

  00024	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR _Parg$[rsp]
  0002c	8b 00		 mov	 eax, DWORD PTR [rax]
  0002e	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00033	89 01		 mov	 DWORD PTR [rcx], eax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash

; 371  :         : _Traitsobj(_Parg), _List(_Al), _Vec(_Al), _Mask(_Min_buckets - 1), _Maxidx(_Min_buckets) {

  00035	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0003d	48 83 c0 08	 add	 rax, 8
  00041	48 89 44 24 38	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 816  :     explicit list(const _Alloc& _Al) : _Mypair(_One_then_variadic_args_t{}, _Al) {

  00046	48 8b 44 24 38	 mov	 rax, QWORD PTR this$[rsp]
  0004b	48 89 44 24 48	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00050	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR _Al$[rsp]
  00058	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1536 :         : _Ty1(_STD forward<_Other1>(_Val1)), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  00060	48 8b 44 24 48	 mov	 rax, QWORD PTR this$[rsp]
  00065	48 89 44 24 30	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 353  :     _List_val() noexcept : _Myhead(), _Mysize(0) {} // initialize data

  0006a	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0006f	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0
  00076	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0007b	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0

; 817  :         _Alloc_sentinel_and_proxy();

  00083	48 8b 4c 24 38	 mov	 rcx, QWORD PTR this$[rsp]
  00088	e8 00 00 00 00	 call	 ?_Alloc_sentinel_and_proxy@?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@AEAAXXZ ; std::list<std::pair<unsigned int const ,unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >::_Alloc_sentinel_and_proxy
  0008d	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash

; 371  :         : _Traitsobj(_Parg), _List(_Al), _Vec(_Al), _Mask(_Min_buckets - 1), _Maxidx(_Min_buckets) {

  0008e	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00096	48 83 c0 18	 add	 rax, 24
  0009a	48 89 44 24 50	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  0009f	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR _Al$[rsp]
  000a7	48 89 44 24 58	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash

; 260  :         : _Mypair(_One_then_variadic_args_t{}, _STD forward<_Any_alloc>(_Al)) { // construct empty vector, allocator

  000ac	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  000b1	48 89 44 24 60	 mov	 QWORD PTR this$[rsp], rax
  000b6	48 8b 44 24 58	 mov	 rax, QWORD PTR $T2[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  000bb	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1536 :         : _Ty1(_STD forward<_Other1>(_Val1)), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  000c3	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  000c8	48 89 44 24 28	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 400  :     _CONSTEXPR20 _Vector_val() noexcept : _Myfirst(), _Mylast(), _Myend() {}

  000cd	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  000d2	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0
  000d9	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  000de	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0
  000e6	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  000eb	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash

; 261  :         _Mypair._Myval2._Alloc_proxy(_GET_PROXY_ALLOCATOR(_Aliter, _Mypair._Get_first()));

  000f3	48 8d 44 24 20	 lea	 rax, QWORD PTR $T1[rsp]
  000f8	48 8b f8	 mov	 rdi, rax
  000fb	33 c0		 xor	 eax, eax
  000fd	b9 01 00 00 00	 mov	 ecx, 1
  00102	f3 aa		 rep stosb

; 371  :         : _Traitsobj(_Parg), _List(_Al), _Vec(_Al), _Mask(_Min_buckets - 1), _Maxidx(_Min_buckets) {

  00104	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0010c	48 c7 40 30 07
	00 00 00	 mov	 QWORD PTR [rax+48], 7
  00114	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0011c	48 c7 40 38 08
	00 00 00	 mov	 QWORD PTR [rax+56], 8

; 154  :         return _Mypair._Myval2._Myval2;

  00124	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0012c	48 89 44 24 68	 mov	 QWORD PTR $T3[rsp], rax

; 1820 :         return _Traitsobj._Get_max_bucket_size();

  00131	48 8b 44 24 68	 mov	 rax, QWORD PTR $T3[rsp]
  00136	48 89 44 24 70	 mov	 QWORD PTR $T4[rsp], rax

; 372  :         // construct empty hash table
; 373  :         _Max_bucket_size() = _Bucket_size;

  0013b	48 8b 44 24 70	 mov	 rax, QWORD PTR $T4[rsp]
  00140	f3 0f 10 05 00
	00 00 00	 movss	 xmm0, DWORD PTR __real@3f800000
  00148	f3 0f 11 00	 movss	 DWORD PTR [rax], xmm0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 1129 :         return _Unchecked_iterator(_Mypair._Myval2._Myhead, nullptr);

  0014c	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00154	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00158	48 89 44 24 78	 mov	 QWORD PTR __param0$[rsp], rax

; 37   :     _List_unchecked_const_iterator(_Nodeptr _Pnode, const _Mylist* _Plist) noexcept : _Ptr(_Pnode) {

  0015d	48 8b 44 24 78	 mov	 rax, QWORD PTR __param0$[rsp]
  00162	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T5[rsp], rax

; 1129 :         return _Unchecked_iterator(_Mypair._Myval2._Myhead, nullptr);

  0016a	48 8d 84 24 80
	00 00 00	 lea	 rax, QWORD PTR $T5[rsp]
  00172	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xhash

; 374  :         _Vec._Assign_grow(_Min_buckets * 2, _List._Unchecked_end());

  0017a	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00182	48 83 c0 18	 add	 rax, 24
  00186	48 8b 8c 24 88
	00 00 00	 mov	 rcx, QWORD PTR $T6[rsp]
  0018e	4c 8b 01	 mov	 r8, QWORD PTR [rcx]
  00191	ba 10 00 00 00	 mov	 edx, 16
  00196	48 8b c8	 mov	 rcx, rax
  00199	e8 00 00 00 00	 call	 ?_Assign_grow@?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAAX_KV?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@2@@Z ; std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > > >::_Assign_grow
  0019e	90		 npad	 1

; 375  : #ifdef _ENABLE_STL_INTERNAL_CHECK
; 376  :         _Stl_internal_check_container_invariants();
; 377  : #endif // _ENABLE_STL_INTERNAL_CHECK
; 378  :     }

  0019f	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  001a7	48 81 c4 a0 00
	00 00		 add	 rsp, 160		; 000000a0H
  001ae	5f		 pop	 rdi
  001af	c3		 ret	 0
??0?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA@AEBV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@1@AEBV?$allocator@U?$pair@$$CBII@std@@@1@@Z ENDP ; std::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
this$ = 40
this$ = 48
this$ = 56
this$ = 64
this$ = 72
this$ = 80
$T2 = 88
this$ = 96
$T3 = 104
$T4 = 112
__param0$ = 120
$T5 = 128
$T6 = 136
$T7 = 144
$T8 = 152
this$ = 176
_Parg$ = 184
_Al$ = 192
?dtor$0@?0???0?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA@AEBV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@1@AEBV?$allocator@U?$pair@$$CBII@std@@@1@@Z@4HA PROC ; `std::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 8d b0 00
	00 00		 mov	 rcx, QWORD PTR this$[rbp]
  00010	48 83 c1 08	 add	 rcx, 8
  00014	e8 00 00 00 00	 call	 ??1?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ ; std::list<std::pair<unsigned int const ,unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >::~list<std::pair<unsigned int const ,unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >
  00019	48 83 c4 20	 add	 rsp, 32			; 00000020H
  0001d	5d		 pop	 rbp
  0001e	c3		 ret	 0
?dtor$0@?0???0?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA@AEBV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@1@AEBV?$allocator@U?$pair@$$CBII@std@@@1@@Z@4HA ENDP ; `std::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
this$ = 40
this$ = 48
this$ = 56
this$ = 64
this$ = 72
this$ = 80
$T2 = 88
this$ = 96
$T3 = 104
$T4 = 112
__param0$ = 120
$T5 = 128
$T6 = 136
$T7 = 144
$T8 = 152
this$ = 176
_Parg$ = 184
_Al$ = 192
?dtor$1@?0???0?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA@AEBV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@1@AEBV?$allocator@U?$pair@$$CBII@std@@@1@@Z@4HA PROC ; `std::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >'::`1'::dtor$1
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 8d b0 00
	00 00		 mov	 rcx, QWORD PTR this$[rbp]
  00010	48 83 c1 18	 add	 rcx, 24
  00014	e8 00 00 00 00	 call	 ??1?$_Hash_vec@V?$allocator@V?$_List_unchecked_iterator@V?$_List_val@U?$_List_simple_types@U?$pair@$$CBII@std@@@std@@@std@@@std@@@std@@@std@@QEAA@XZ ; std::_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > > >::~_Hash_vec<std::allocator<std::_List_unchecked_iterator<std::_List_val<std::_List_simple_types<std::pair<unsigned int const ,unsigned int> > > > > >
  00019	48 83 c4 20	 add	 rsp, 32			; 00000020H
  0001d	5d		 pop	 rbp
  0001e	c3		 ret	 0
?dtor$1@?0???0?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@IEAA@AEBV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@1@AEBV?$allocator@U?$pair@$$CBII@std@@@1@@Z@4HA ENDP ; `std::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >'::`1'::dtor$1
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
;	COMDAT ?_Alloc_sentinel_and_proxy@?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@AEAAXXZ
_TEXT	SEGMENT
$T1 = 32
$S119$ = 33
_Newhead$ = 40
$T2 = 48
$T3 = 56
_Al$ = 64
_Obj$ = 72
$T4 = 80
$T5 = 88
$T6 = 96
$T7 = 104
_Obj$ = 112
$T8 = 120
$T9 = 128
$T10 = 136
$T11 = 144
_Alproxy$ = 152
this$ = 176
?_Alloc_sentinel_and_proxy@?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@AEAAXXZ PROC ; std::list<std::pair<unsigned int const ,unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >::_Alloc_sentinel_and_proxy, COMDAT

; 1847 :     void _Alloc_sentinel_and_proxy() {

$LN75:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi
  00006	48 81 ec a0 00
	00 00		 sub	 rsp, 160		; 000000a0H

; 1848 :         auto&& _Alproxy = _GET_PROXY_ALLOCATOR(_Alnode, _Getal());

  0000d	48 8d 44 24 20	 lea	 rax, QWORD PTR $T1[rsp]
  00012	48 8b f8	 mov	 rdi, rax
  00015	33 c0		 xor	 eax, eax
  00017	b9 01 00 00 00	 mov	 ecx, 1
  0001c	f3 aa		 rep stosb
  0001e	48 8d 44 24 21	 lea	 rax, QWORD PTR $S119$[rsp]
  00023	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR _Alproxy$[rsp], rax

; 1863 :         return _Mypair._Get_first();

  0002b	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00033	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 1863 :         return _Mypair._Get_first();

  00038	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
  0003d	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax

; 1849 :         _Container_proxy_ptr<_Alty> _Proxy(_Alproxy, _Mypair._Myval2);
; 1850 :         auto& _Al     = _Getal();

  00042	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
  00047	48 89 44 24 40	 mov	 QWORD PTR _Al$[rsp], rax

; 1851 :         auto _Newhead = _Al.allocate(1);

  0004c	ba 01 00 00 00	 mov	 edx, 1
  00051	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Al$[rsp]
  00056	e8 00 00 00 00	 call	 ?allocate@?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@QEAAPEAU?$_List_node@U?$pair@$$CBII@std@@PEAX@2@_K@Z ; std::allocator<std::_List_node<std::pair<unsigned int const ,unsigned int>,void *> >::allocate
  0005b	48 89 44 24 28	 mov	 QWORD PTR _Newhead$[rsp], rax

; 1852 :         _Construct_in_place(_Newhead->_Next, _Newhead);

  00060	48 8b 44 24 28	 mov	 rax, QWORD PTR _Newhead$[rsp]
  00065	48 89 44 24 48	 mov	 QWORD PTR _Obj$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0006a	48 8b 44 24 48	 mov	 rax, QWORD PTR _Obj$[rsp]
  0006f	48 89 44 24 50	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00074	48 8b 44 24 50	 mov	 rax, QWORD PTR $T4[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  00079	48 89 44 24 58	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0007e	48 8b 44 24 58	 mov	 rax, QWORD PTR $T5[rsp]
  00083	48 89 44 24 60	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00088	48 8d 44 24 28	 lea	 rax, QWORD PTR _Newhead$[rsp]
  0008d	48 89 44 24 68	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00092	48 8b 44 24 60	 mov	 rax, QWORD PTR $T6[rsp]
  00097	48 8b 4c 24 68	 mov	 rcx, QWORD PTR $T7[rsp]
  0009c	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0009f	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 1853 :         _Construct_in_place(_Newhead->_Prev, _Newhead);

  000a2	48 8b 44 24 28	 mov	 rax, QWORD PTR _Newhead$[rsp]
  000a7	48 83 c0 08	 add	 rax, 8
  000ab	48 89 44 24 70	 mov	 QWORD PTR _Obj$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  000b0	48 8b 44 24 70	 mov	 rax, QWORD PTR _Obj$[rsp]
  000b5	48 89 44 24 78	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  000ba	48 8b 44 24 78	 mov	 rax, QWORD PTR $T8[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  000bf	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  000c7	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR $T9[rsp]
  000cf	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  000d7	48 8d 44 24 28	 lea	 rax, QWORD PTR _Newhead$[rsp]
  000dc	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  000e4	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  000ec	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR $T11[rsp]
  000f4	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  000f7	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 1854 :         _Mypair._Myval2._Myhead = _Newhead;

  000fa	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00102	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Newhead$[rsp]
  00107	48 89 08	 mov	 QWORD PTR [rax], rcx

; 1855 :         _Proxy._Release();
; 1856 :     }

  0010a	48 81 c4 a0 00
	00 00		 add	 rsp, 160		; 000000a0H
  00111	5f		 pop	 rdi
  00112	c3		 ret	 0
?_Alloc_sentinel_and_proxy@?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@AEAAXXZ ENDP ; std::list<std::pair<unsigned int const ,unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >::_Alloc_sentinel_and_proxy
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
;	COMDAT ?_Tidy@?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@AEAAXXZ
_TEXT	SEGMENT
_Bytes$ = 32
_My_data$ = 40
_Ptr$ = 48
$T1 = 56
$T2 = 64
_Al$ = 72
_Ptr$ = 80
this$ = 112
?_Tidy@?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@AEAAXXZ PROC ; std::list<std::pair<unsigned int const ,unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >::_Tidy, COMDAT

; 1514 :     void _Tidy() noexcept {

$LN99:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 68	 sub	 rsp, 104		; 00000068H

; 1863 :         return _Mypair._Get_first();

  00009	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  0000e	48 89 44 24 38	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 1863 :         return _Mypair._Get_first();

  00013	48 8b 44 24 38	 mov	 rax, QWORD PTR $T1[rsp]
  00018	48 89 44 24 40	 mov	 QWORD PTR $T2[rsp], rax

; 1515 :         auto& _Al      = _Getal();

  0001d	48 8b 44 24 40	 mov	 rax, QWORD PTR $T2[rsp]
  00022	48 89 44 24 48	 mov	 QWORD PTR _Al$[rsp], rax

; 1516 :         auto& _My_data = _Mypair._Myval2;

  00027	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0002c	48 89 44 24 28	 mov	 QWORD PTR _My_data$[rsp], rax

; 1517 :         _My_data._Orphan_all();
; 1518 :         _Node::_Free_non_head(_Al, _My_data._Myhead);

  00031	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00036	48 8b 10	 mov	 rdx, QWORD PTR [rax]
  00039	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _Al$[rsp]
  0003e	e8 00 00 00 00	 call	 ??$_Free_non_head@V?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@SAXAEAV?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU01@@Z ; std::_List_node<std::pair<unsigned int const ,unsigned int>,void *>::_Free_non_head<std::allocator<std::_List_node<std::pair<unsigned int const ,unsigned int>,void *> > >

; 1519 :         _Node::_Freenode0(_Al, _My_data._Myhead);

  00043	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00048	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0004b	48 89 44 24 50	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 723  :             _STD _Deallocate<_New_alignof<value_type>>(_Ptr, sizeof(value_type) * _Count);

  00050	b8 01 00 00 00	 mov	 eax, 1
  00055	48 6b c0 18	 imul	 rax, rax, 24
  00059	48 89 44 24 20	 mov	 QWORD PTR _Bytes$[rsp], rax
  0005e	48 8b 44 24 50	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00063	48 89 44 24 30	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  00068	48 81 7c 24 20
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  00071	72 10		 jb	 SHORT $LN90@Tidy

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  00073	48 8d 54 24 20	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  00078	48 8d 4c 24 30	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  0007d	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  00082	90		 npad	 1
$LN90@Tidy:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  00083	48 8b 54 24 20	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  00088	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  0008d	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00092	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 1520 :     }

  00093	48 83 c4 68	 add	 rsp, 104		; 00000068H
  00097	c3		 ret	 0
?_Tidy@?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@AEAAXXZ ENDP ; std::list<std::pair<unsigned int const ,unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >::_Tidy
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
;	COMDAT ?clear@?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAAXXZ
_TEXT	SEGMENT
_My_data$ = 32
$T1 = 40
$T2 = 48
this$ = 80
?clear@?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAAXXZ PROC ; std::list<std::pair<unsigned int const ,unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >::clear, COMDAT

; 1504 :     void clear() noexcept { // erase all

$LN69:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 1505 :         auto& _My_data = _Mypair._Myval2;

  00009	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 89 44 24 20	 mov	 QWORD PTR _My_data$[rsp], rax

; 1863 :         return _Mypair._Get_first();

  00013	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00018	48 89 44 24 28	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 1863 :         return _Mypair._Get_first();

  0001d	48 8b 44 24 28	 mov	 rax, QWORD PTR $T1[rsp]
  00022	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax

; 1506 :         _My_data._Orphan_non_end();
; 1507 :         _Node::_Free_non_head(_Getal(), _My_data._Myhead);

  00027	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
  0002c	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _My_data$[rsp]
  00031	48 8b 11	 mov	 rdx, QWORD PTR [rcx]
  00034	48 8b c8	 mov	 rcx, rax
  00037	e8 00 00 00 00	 call	 ??$_Free_non_head@V?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@@?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@SAXAEAV?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@1@PEAU01@@Z ; std::_List_node<std::pair<unsigned int const ,unsigned int>,void *>::_Free_non_head<std::allocator<std::_List_node<std::pair<unsigned int const ,unsigned int>,void *> > >

; 1508 :         _My_data._Myhead->_Next = _My_data._Myhead;

  0003c	48 8b 44 24 20	 mov	 rax, QWORD PTR _My_data$[rsp]
  00041	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00044	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _My_data$[rsp]
  00049	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0004c	48 89 08	 mov	 QWORD PTR [rax], rcx

; 1509 :         _My_data._Myhead->_Prev = _My_data._Myhead;

  0004f	48 8b 44 24 20	 mov	 rax, QWORD PTR _My_data$[rsp]
  00054	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00057	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _My_data$[rsp]
  0005c	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0005f	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 1510 :         _My_data._Mysize        = 0;

  00063	48 8b 44 24 20	 mov	 rax, QWORD PTR _My_data$[rsp]
  00068	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0

; 1511 :     }

  00070	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00074	c3		 ret	 0
?clear@?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAAXXZ ENDP ; std::list<std::pair<unsigned int const ,unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >::clear
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
;	COMDAT ??1?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ PROC ; std::list<std::pair<unsigned int const ,unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >::~list<std::pair<unsigned int const ,unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >, COMDAT

; 1060 :     ~list() noexcept {

$LN104:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 1061 :         _Tidy();

  00009	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0000e	e8 00 00 00 00	 call	 ?_Tidy@?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@AEAAXXZ ; std::list<std::pair<unsigned int const ,unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >::_Tidy
  00013	90		 npad	 1

; 1062 : #if _ITERATOR_DEBUG_LEVEL != 0 // TRANSITION, ABI
; 1063 :         auto&& _Alproxy = _GET_PROXY_ALLOCATOR(_Alnode, _Getal());
; 1064 :         _Delete_plain_internal(_Alproxy, _Mypair._Myval2._Myproxy);
; 1065 : #endif // _ITERATOR_DEBUG_LEVEL != 0
; 1066 :     }

  00014	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00018	c3		 ret	 0
??1?$list@U?$pair@$$CBII@std@@V?$allocator@U?$pair@$$CBII@std@@@2@@std@@QEAA@XZ ENDP ; std::list<std::pair<unsigned int const ,unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >::~list<std::pair<unsigned int const ,unsigned int>,std::allocator<std::pair<unsigned int const ,unsigned int> > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?allocate@?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@QEAAPEAU?$_List_node@U?$pair@$$CBII@std@@PEAX@2@_K@Z
_TEXT	SEGMENT
_Overflow_is_possible$1 = 32
_Bytes$ = 40
$T2 = 48
$T3 = 56
$T4 = 64
_Max_possible$5 = 72
this$ = 96
_Count$ = 104
?allocate@?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@QEAAPEAU?$_List_node@U?$pair@$$CBII@std@@PEAX@2@_K@Z PROC ; std::allocator<std::_List_node<std::pair<unsigned int const ,unsigned int>,void *> >::allocate, COMDAT

; 988  :     _NODISCARD_RAW_PTR_ALLOC _CONSTEXPR20 __declspec(allocator) _Ty* allocate(_CRT_GUARDOVERFLOW const size_t _Count) {

$LN13:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 113  :     constexpr bool _Overflow_is_possible = _Ty_size > 1;

  0000e	c6 44 24 20 01	 mov	 BYTE PTR _Overflow_is_possible$1[rsp], 1

; 114  : 
; 115  :     if constexpr (_Overflow_is_possible) {
; 116  :         constexpr size_t _Max_possible = static_cast<size_t>(-1) / _Ty_size;

  00013	48 b8 aa aa aa
	aa aa aa aa 0a	 mov	 rax, 768614336404564650	; 0aaaaaaaaaaaaaaaH
  0001d	48 89 44 24 48	 mov	 QWORD PTR _Max_possible$5[rsp], rax

; 117  :         if (_Count > _Max_possible) {

  00022	48 b8 aa aa aa
	aa aa aa aa 0a	 mov	 rax, 768614336404564650	; 0aaaaaaaaaaaaaaaH
  0002c	48 39 44 24 68	 cmp	 QWORD PTR _Count$[rsp], rax
  00031	76 06		 jbe	 SHORT $LN4@allocate

; 118  :             _Throw_bad_array_new_length(); // multiply overflow

  00033	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00038	90		 npad	 1
$LN4@allocate:

; 119  :         }
; 120  :     }
; 121  : 
; 122  :     return _Count * _Ty_size;

  00039	48 6b 44 24 68
	18		 imul	 rax, QWORD PTR _Count$[rsp], 24
  0003f	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00044	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
  00049	48 89 44 24 28	 mov	 QWORD PTR _Bytes$[rsp], rax

; 227  :     if (_Bytes == 0) {

  0004e	48 83 7c 24 28
	00		 cmp	 QWORD PTR _Bytes$[rsp], 0
  00054	75 0b		 jne	 SHORT $LN8@allocate

; 228  :         return nullptr;

  00056	48 c7 44 24 30
	00 00 00 00	 mov	 QWORD PTR $T2[rsp], 0
  0005f	eb 35		 jmp	 SHORT $LN7@allocate
$LN8@allocate:

; 229  :     }
; 230  : 
; 231  : #if _HAS_CXX20 // TRANSITION, GH-1532
; 232  :     if (_STD is_constant_evaluated()) {
; 233  :         return _Traits::_Allocate(_Bytes);
; 234  :     }
; 235  : #endif // _HAS_CXX20
; 236  : 
; 237  : #ifdef __cpp_aligned_new
; 238  :     if constexpr (_Align > __STDCPP_DEFAULT_NEW_ALIGNMENT__) {
; 239  :         size_t _Passed_align = _Align;
; 240  : #if defined(_M_IX86) || defined(_M_X64)
; 241  :         if (_Bytes >= _Big_allocation_threshold) {
; 242  :             // boost the alignment of big allocations to help autovectorization
; 243  :             _Passed_align = (_STD max)(_Align, _Big_allocation_alignment);
; 244  :         }
; 245  : #endif // defined(_M_IX86) || defined(_M_X64)
; 246  :         return _Traits::_Allocate_aligned(_Bytes, _Passed_align);
; 247  :     } else
; 248  : #endif // defined(__cpp_aligned_new)
; 249  :     {
; 250  : #if defined(_M_IX86) || defined(_M_X64)
; 251  :         if (_Bytes >= _Big_allocation_threshold) {

  00061	48 81 7c 24 28
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  0006a	72 11		 jb	 SHORT $LN9@allocate

; 252  :             // boost the alignment of big allocations to help autovectorization
; 253  :             return _Allocate_manually_vector_aligned<_Traits>(_Bytes);

  0006c	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00071	e8 00 00 00 00	 call	 ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
  00076	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
  0007b	eb 19		 jmp	 SHORT $LN7@allocate
$LN9@allocate:

; 136  :         return ::operator new(_Bytes);

  0007d	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00082	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00087	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax

; 256  :         return _Traits::_Allocate(_Bytes);

  0008c	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  00091	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
$LN7@allocate:

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00096	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
$LN6@allocate:

; 991  :     }

  0009b	48 83 c4 58	 add	 rsp, 88			; 00000058H
  0009f	c3		 ret	 0
?allocate@?$allocator@U?$_List_node@U?$pair@$$CBII@std@@PEAX@std@@@std@@QEAAPEAU?$_List_node@U?$pair@$$CBII@std@@PEAX@2@_K@Z ENDP ; std::allocator<std::_List_node<std::pair<unsigned int const ,unsigned int>,void *> >::allocate
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Mu2Common\Protocol\EventLog.h
;	COMDAT ?UnInit@LogAssist@mu2@@UEAA_NXZ
_TEXT	SEGMENT
this$ = 48
?UnInit@LogAssist@mu2@@UEAA_NXZ PROC			; mu2::LogAssist::UnInit, COMDAT

; 70   : 	{

$LN340:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 71   : 		mCodeFlags.clear();

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 c0 08	 add	 rax, 8
  00012	48 8b c8	 mov	 rcx, rax
  00015	e8 00 00 00 00	 call	 ?clear@?$_Hash@V?$_Umap_traits@IIV?$_Uhash_compare@IU?$hash@I@std@@U?$equal_to@I@2@@std@@V?$allocator@U?$pair@$$CBII@std@@@2@$0A@@std@@@std@@QEAAXXZ ; std::_Hash<std::_Umap_traits<unsigned int,unsigned int,std::_Uhash_compare<unsigned int,std::hash<unsigned int>,std::equal_to<unsigned int> >,std::allocator<std::pair<unsigned int const ,unsigned int> >,0> >::clear

; 72   : 		return true; 

  0001a	b0 01		 mov	 al, 1

; 73   : 	}

  0001c	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00020	c3		 ret	 0
?UnInit@LogAssist@mu2@@UEAA_NXZ ENDP			; mu2::LogAssist::UnInit
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Mu2Common\Protocol\EventLog.h
;	COMDAT ?Init@LogAssist@mu2@@UEAA_NPEAX@Z
_TEXT	SEGMENT
this$ = 8
__formal$ = 16
?Init@LogAssist@mu2@@UEAA_NPEAX@Z PROC			; mu2::LogAssist::Init, COMDAT

; 68   : 	Bool Init(void*) { return true; }

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	b0 01		 mov	 al, 1
  0000c	c3		 ret	 0
?Init@LogAssist@mu2@@UEAA_NPEAX@Z ENDP			; mu2::LogAssist::Init
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??_G?$ISingleton@VLogAssist@mu2@@@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_G?$ISingleton@VLogAssist@mu2@@@mu2@@UEAAPEAXI@Z PROC	; mu2::ISingleton<mu2::LogAssist>::`scalar deleting destructor', COMDAT
$LN10:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 80   : 	virtual~ISingleton() {}

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$ISingleton@VLogAssist@mu2@@@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx
  0001c	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00020	83 e0 01	 and	 eax, 1
  00023	85 c0		 test	 eax, eax
  00025	74 10		 je	 SHORT $LN2@scalar
  00027	ba 08 00 00 00	 mov	 edx, 8
  0002c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00031	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00036	90		 npad	 1
$LN2@scalar:
  00037	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003c	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00040	c3		 ret	 0
??_G?$ISingleton@VLogAssist@mu2@@@mu2@@UEAAPEAXI@Z ENDP	; mu2::ISingleton<mu2::LogAssist>::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??1?$ISingleton@VLogAssist@mu2@@@mu2@@UEAA@XZ
_TEXT	SEGMENT
this$ = 8
??1?$ISingleton@VLogAssist@mu2@@@mu2@@UEAA@XZ PROC	; mu2::ISingleton<mu2::LogAssist>::~ISingleton<mu2::LogAssist>, COMDAT

; 80   : 	virtual~ISingleton() {}

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0000a	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$ISingleton@VLogAssist@mu2@@@mu2@@6B@
  00011	48 89 08	 mov	 QWORD PTR [rax], rcx
  00014	c3		 ret	 0
??1?$ISingleton@VLogAssist@mu2@@@mu2@@UEAA@XZ ENDP	; mu2::ISingleton<mu2::LogAssist>::~ISingleton<mu2::LogAssist>
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h
;	COMDAT ??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z
_TEXT	SEGMENT
ptr$ = 48
??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z PROC ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::operator delete, COMDAT

; 57   : 	{

$LN6:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h

; 45   : 			::free(ptr);

  00009	48 8b 4c 24 30	 mov	 rcx, QWORD PTR ptr$[rsp]
  0000e	e8 00 00 00 00	 call	 free
  00013	90		 npad	 1
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h

; 59   : 	}

  00014	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00018	c3		 ret	 0
??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z ENDP ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::operator delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
;	COMDAT ??$_Hash_representation@I@std@@YA_KAEBI@Z
_TEXT	SEGMENT
_Keyval$ = 48
??$_Hash_representation@I@std@@YA_KAEBI@Z PROC		; std::_Hash_representation<unsigned int>, COMDAT

; 2284 : _NODISCARD size_t _Hash_representation(const _Kty& _Keyval) noexcept { // bitwise hashes the representation of a key

$LN4:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 2285 :     return _Fnv1a_append_value(_FNV_offset_basis, _Keyval);

  00009	48 8b 54 24 30	 mov	 rdx, QWORD PTR _Keyval$[rsp]
  0000e	48 b9 25 23 22
	84 e4 9c f2 cb	 mov	 rcx, -3750763034362895579 ; cbf29ce484222325H
  00018	e8 00 00 00 00	 call	 ??$_Fnv1a_append_value@I@std@@YA_K_KAEBI@Z ; std::_Fnv1a_append_value<unsigned int>

; 2286 : }

  0001d	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00021	c3		 ret	 0
??$_Hash_representation@I@std@@YA_KAEBI@Z ENDP		; std::_Hash_representation<unsigned int>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ?_Get_deleter@_Ref_count_base@std@@UEBAPEAXAEBVtype_info@@@Z
_TEXT	SEGMENT
this$ = 8
__formal$ = 16
?_Get_deleter@_Ref_count_base@std@@UEBAPEAXAEBVtype_info@@@Z PROC ; std::_Ref_count_base::_Get_deleter, COMDAT

; 1175 :     virtual void* _Get_deleter(const type_info&) const noexcept {

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 1176 :         return nullptr;

  0000a	33 c0		 xor	 eax, eax

; 1177 :     }

  0000c	c3		 ret	 0
?_Get_deleter@_Ref_count_base@std@@UEBAPEAXAEBVtype_info@@@Z ENDP ; std::_Ref_count_base::_Get_deleter
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ?_Decref@_Ref_count_base@std@@QEAAXXZ
_TEXT	SEGMENT
this$ = 48
?_Decref@_Ref_count_base@std@@QEAAXXZ PROC		; std::_Ref_count_base::_Decref, COMDAT

; 1158 :     void _Decref() noexcept { // decrement use count

$LN11:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 1159 :         if (_MT_DECR(_Uses) == 0) {

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 c0 08	 add	 rax, 8
  00012	b9 ff ff ff ff	 mov	 ecx, -1
  00017	f0 0f c1 08	 lock xadd DWORD PTR [rax], ecx
  0001b	ff c9		 dec	 ecx
  0001d	8b c1		 mov	 eax, ecx
  0001f	85 c0		 test	 eax, eax
  00021	75 3a		 jne	 SHORT $LN2@Decref

; 1160 :             _Destroy();

  00023	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00028	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0002b	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00030	ff 10		 call	 QWORD PTR [rax]

; 1166 :         if (_MT_DECR(_Weaks) == 0) {

  00032	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00037	48 83 c0 0c	 add	 rax, 12
  0003b	b9 ff ff ff ff	 mov	 ecx, -1
  00040	f0 0f c1 08	 lock xadd DWORD PTR [rax], ecx
  00044	ff c9		 dec	 ecx
  00046	8b c1		 mov	 eax, ecx
  00048	85 c0		 test	 eax, eax
  0004a	75 11		 jne	 SHORT $LN2@Decref

; 1167 :             _Delete_this();

  0004c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00051	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00054	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00059	ff 50 08	 call	 QWORD PTR [rax+8]
  0005c	90		 npad	 1
$LN2@Decref:

; 1161 :             _Decwref();
; 1162 :         }
; 1163 :     }

  0005d	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00061	c3		 ret	 0
?_Decref@_Ref_count_base@std@@QEAAXXZ ENDP		; std::_Ref_count_base::_Decref
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??1?$_Compressed_pair@V?$allocator@_W@std@@V?$_String_val@U?$_Simple_types@_W@std@@@2@$00@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 8
??1?$_Compressed_pair@V?$allocator@_W@std@@V?$_String_val@U?$_Simple_types@_W@std@@@2@$00@std@@QEAA@XZ PROC ; std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>::~_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>, COMDAT
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
??1?$_Compressed_pair@V?$allocator@_W@std@@V?$_String_val@U?$_Simple_types@_W@std@@@2@$00@std@@QEAA@XZ ENDP ; std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>::~_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ
_TEXT	SEGMENT
$T1 = 0
_Alloc_max$ = 8
tv66 = 16
$T2 = 24
$T3 = 32
tv70 = 40
$T4 = 48
$T5 = 56
$T6 = 64
$T7 = 72
_Storage_max$ = 80
$T8 = 88
$T9 = 96
$T10 = 104
$T11 = 112
_Unsigned_max$12 = 120
this$ = 144
?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size, COMDAT

; 2377 :     _NODISCARD _CONSTEXPR20 size_type max_size() const noexcept {

$LN38:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H

; 3111 :         return _Mypair._Get_first();

  0000c	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1543 :         return *this;

  00014	48 89 44 24 30	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3111 :         return _Mypair._Get_first();

  00019	48 8b 44 24 30	 mov	 rax, QWORD PTR $T4[rsp]
  0001e	48 89 44 24 70	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 746  :         return static_cast<size_t>(-1) / sizeof(value_type);

  00023	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  0002d	48 89 44 24 38	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2378 :         const size_type _Alloc_max   = _Alty_traits::max_size(_Getal());

  00032	48 8b 44 24 38	 mov	 rax, QWORD PTR $T5[rsp]
  00037	48 89 44 24 08	 mov	 QWORD PTR _Alloc_max$[rsp], rax

; 2379 :         const size_type _Storage_max = // can always store small string

  0003c	48 c7 04 24 08
	00 00 00	 mov	 QWORD PTR $T1[rsp], 8
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 77   :     return _Left < _Right ? _Right : _Left;

  00044	48 8b 04 24	 mov	 rax, QWORD PTR $T1[rsp]
  00048	48 39 44 24 08	 cmp	 QWORD PTR _Alloc_max$[rsp], rax
  0004d	73 0b		 jae	 SHORT $LN21@max_size
  0004f	48 8d 04 24	 lea	 rax, QWORD PTR $T1[rsp]
  00053	48 89 44 24 10	 mov	 QWORD PTR tv66[rsp], rax
  00058	eb 0a		 jmp	 SHORT $LN22@max_size
$LN21@max_size:
  0005a	48 8d 44 24 08	 lea	 rax, QWORD PTR _Alloc_max$[rsp]
  0005f	48 89 44 24 10	 mov	 QWORD PTR tv66[rsp], rax
$LN22@max_size:
  00064	48 8b 44 24 10	 mov	 rax, QWORD PTR tv66[rsp]
  00069	48 89 44 24 40	 mov	 QWORD PTR $T6[rsp], rax
  0006e	48 8b 44 24 40	 mov	 rax, QWORD PTR $T6[rsp]
  00073	48 89 44 24 48	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2379 :         const size_type _Storage_max = // can always store small string

  00078	48 8b 44 24 48	 mov	 rax, QWORD PTR $T7[rsp]
  0007d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00080	48 89 44 24 50	 mov	 QWORD PTR _Storage_max$[rsp], rax

; 2381 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()),

  00085	48 8b 44 24 50	 mov	 rax, QWORD PTR _Storage_max$[rsp]
  0008a	48 ff c8	 dec	 rax
  0008d	48 89 44 24 18	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 866  :         constexpr auto _Unsigned_max = static_cast<make_unsigned_t<_Ty>>(-1);

  00092	48 c7 44 24 78
	ff ff ff ff	 mov	 QWORD PTR _Unsigned_max$12[rsp], -1

; 867  :         return static_cast<_Ty>(_Unsigned_max >> 1);

  0009b	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  000a5	48 89 44 24 58	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2381 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()),

  000aa	48 8b 44 24 58	 mov	 rax, QWORD PTR $T8[rsp]
  000af	48 89 44 24 20	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 101  :     return _Right < _Left ? _Right : _Left;

  000b4	48 8b 44 24 20	 mov	 rax, QWORD PTR $T3[rsp]
  000b9	48 39 44 24 18	 cmp	 QWORD PTR $T2[rsp], rax
  000be	73 0c		 jae	 SHORT $LN33@max_size
  000c0	48 8d 44 24 18	 lea	 rax, QWORD PTR $T2[rsp]
  000c5	48 89 44 24 28	 mov	 QWORD PTR tv70[rsp], rax
  000ca	eb 0a		 jmp	 SHORT $LN34@max_size
$LN33@max_size:
  000cc	48 8d 44 24 20	 lea	 rax, QWORD PTR $T3[rsp]
  000d1	48 89 44 24 28	 mov	 QWORD PTR tv70[rsp], rax
$LN34@max_size:
  000d6	48 8b 44 24 28	 mov	 rax, QWORD PTR tv70[rsp]
  000db	48 89 44 24 60	 mov	 QWORD PTR $T9[rsp], rax
  000e0	48 8b 44 24 60	 mov	 rax, QWORD PTR $T9[rsp]
  000e5	48 89 44 24 68	 mov	 QWORD PTR $T10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2381 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()),

  000ea	48 8b 44 24 68	 mov	 rax, QWORD PTR $T10[rsp]
  000ef	48 8b 00	 mov	 rax, QWORD PTR [rax]

; 2382 :             _Storage_max - 1 // -1 is for null terminator and/or npos
; 2383 :         );
; 2384 :     }

  000f2	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  000f9	c3		 ret	 0
?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z
_TEXT	SEGMENT
this$ = 32
this$ = 40
this$ = 48
$T1 = 56
$T2 = 64
this$ = 96
_Ptr$ = 104
??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >, COMDAT

; 768  :     _CONSTEXPR20 basic_string(_In_z_ const _Elem* const _Ptr) : _Mypair(_Zero_then_variadic_args_t{}) {

$LN221:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 50	 sub	 rsp, 80			; 00000050H
  0000f	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 89 44 24 30	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1531 :         : _Ty1(), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  00019	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001e	48 89 44 24 20	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 402  :     _CONSTEXPR20 _String_val() noexcept : _Bx() {}

  00023	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00028	48 89 44 24 28	 mov	 QWORD PTR this$[rsp], rax

; 493  :         _CONSTEXPR20 _Bxty() noexcept : _Buf() {} // user-provided, for fancy pointers

  0002d	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00032	48 8b 7c 24 28	 mov	 rdi, QWORD PTR this$[rsp]
  00037	33 c0		 xor	 eax, eax
  00039	b9 10 00 00 00	 mov	 ecx, 16
  0003e	f3 aa		 rep stosb

; 517  :     size_type _Mysize = 0; // current length of string (size)

  00040	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00045	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 518  :     size_type _Myres  = 0; // current storage reserved for string (capacity)

  0004d	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00052	48 c7 40 18 00
	00 00 00	 mov	 QWORD PTR [rax+24], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 310  :         return _CSTD wcslen(reinterpret_cast<const wchar_t*>(_First));

  0005a	48 8b 4c 24 68	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  0005f	e8 00 00 00 00	 call	 wcslen
  00064	48 89 44 24 38	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 769  :         _Construct<_Construct_strategy::_From_ptr>(_Ptr, _Convert_size<size_type>(_Traits::length(_Ptr)));

  00069	48 8b 44 24 38	 mov	 rax, QWORD PTR $T1[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1131 :     return static_cast<_Size_type>(_Len);

  0006e	48 89 44 24 40	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 769  :         _Construct<_Construct_strategy::_From_ptr>(_Ptr, _Convert_size<size_type>(_Traits::length(_Ptr)));

  00073	48 8b 44 24 40	 mov	 rax, QWORD PTR $T2[rsp]
  00078	4c 8b c0	 mov	 r8, rax
  0007b	48 8b 54 24 68	 mov	 rdx, QWORD PTR _Ptr$[rsp]
  00080	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  00085	e8 00 00 00 00	 call	 ??$_Construct@$00PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<1,wchar_t const *>
  0008a	90		 npad	 1

; 770  :     }

  0008b	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00090	48 83 c4 50	 add	 rsp, 80			; 00000050H
  00094	5f		 pop	 rdi
  00095	c3		 ret	 0
??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
this$ = 32
this$ = 40
this$ = 48
$T1 = 56
$T2 = 64
this$ = 96
_Ptr$ = 104
?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z@4HA PROC ; `std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 60	 mov	 rcx, QWORD PTR this$[rbp]
  0000d	e8 00 00 00 00	 call	 ??1?$_Compressed_pair@V?$allocator@_W@std@@V?$_String_val@U?$_Simple_types@_W@std@@@2@$00@std@@QEAA@XZ
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z@4HA ENDP ; `std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z
_TEXT	SEGMENT
_Overflow_is_possible$1 = 32
_Bytes$ = 40
$T2 = 48
$T3 = 56
$T4 = 64
_Max_possible$5 = 72
this$ = 96
_Count$ = 104
?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z PROC	; std::allocator<wchar_t>::allocate, COMDAT

; 988  :     _NODISCARD_RAW_PTR_ALLOC _CONSTEXPR20 __declspec(allocator) _Ty* allocate(_CRT_GUARDOVERFLOW const size_t _Count) {

$LN13:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 113  :     constexpr bool _Overflow_is_possible = _Ty_size > 1;

  0000e	c6 44 24 20 01	 mov	 BYTE PTR _Overflow_is_possible$1[rsp], 1

; 114  : 
; 115  :     if constexpr (_Overflow_is_possible) {
; 116  :         constexpr size_t _Max_possible = static_cast<size_t>(-1) / _Ty_size;

  00013	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  0001d	48 89 44 24 48	 mov	 QWORD PTR _Max_possible$5[rsp], rax

; 117  :         if (_Count > _Max_possible) {

  00022	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  0002c	48 39 44 24 68	 cmp	 QWORD PTR _Count$[rsp], rax
  00031	76 06		 jbe	 SHORT $LN4@allocate

; 118  :             _Throw_bad_array_new_length(); // multiply overflow

  00033	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00038	90		 npad	 1
$LN4@allocate:

; 119  :         }
; 120  :     }
; 121  : 
; 122  :     return _Count * _Ty_size;

  00039	48 8b 44 24 68	 mov	 rax, QWORD PTR _Count$[rsp]
  0003e	48 d1 e0	 shl	 rax, 1
  00041	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00046	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
  0004b	48 89 44 24 28	 mov	 QWORD PTR _Bytes$[rsp], rax

; 227  :     if (_Bytes == 0) {

  00050	48 83 7c 24 28
	00		 cmp	 QWORD PTR _Bytes$[rsp], 0
  00056	75 0b		 jne	 SHORT $LN8@allocate

; 228  :         return nullptr;

  00058	48 c7 44 24 30
	00 00 00 00	 mov	 QWORD PTR $T2[rsp], 0
  00061	eb 35		 jmp	 SHORT $LN7@allocate
$LN8@allocate:

; 229  :     }
; 230  : 
; 231  : #if _HAS_CXX20 // TRANSITION, GH-1532
; 232  :     if (_STD is_constant_evaluated()) {
; 233  :         return _Traits::_Allocate(_Bytes);
; 234  :     }
; 235  : #endif // _HAS_CXX20
; 236  : 
; 237  : #ifdef __cpp_aligned_new
; 238  :     if constexpr (_Align > __STDCPP_DEFAULT_NEW_ALIGNMENT__) {
; 239  :         size_t _Passed_align = _Align;
; 240  : #if defined(_M_IX86) || defined(_M_X64)
; 241  :         if (_Bytes >= _Big_allocation_threshold) {
; 242  :             // boost the alignment of big allocations to help autovectorization
; 243  :             _Passed_align = (_STD max)(_Align, _Big_allocation_alignment);
; 244  :         }
; 245  : #endif // defined(_M_IX86) || defined(_M_X64)
; 246  :         return _Traits::_Allocate_aligned(_Bytes, _Passed_align);
; 247  :     } else
; 248  : #endif // defined(__cpp_aligned_new)
; 249  :     {
; 250  : #if defined(_M_IX86) || defined(_M_X64)
; 251  :         if (_Bytes >= _Big_allocation_threshold) {

  00063	48 81 7c 24 28
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  0006c	72 11		 jb	 SHORT $LN9@allocate

; 252  :             // boost the alignment of big allocations to help autovectorization
; 253  :             return _Allocate_manually_vector_aligned<_Traits>(_Bytes);

  0006e	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00073	e8 00 00 00 00	 call	 ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
  00078	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
  0007d	eb 19		 jmp	 SHORT $LN7@allocate
$LN9@allocate:

; 136  :         return ::operator new(_Bytes);

  0007f	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00084	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00089	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax

; 256  :         return _Traits::_Allocate(_Bytes);

  0008e	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  00093	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
$LN7@allocate:

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00098	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
$LN6@allocate:

; 991  :     }

  0009d	48 83 c4 58	 add	 rsp, 88			; 00000058H
  000a1	c3		 ret	 0
?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z ENDP	; std::allocator<wchar_t>::allocate
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Xlen_string@std@@YAXXZ
_TEXT	SEGMENT
?_Xlen_string@std@@YAXXZ PROC				; std::_Xlen_string, COMDAT

; 530  : [[noreturn]] inline void _Xlen_string() {

$LN3:
  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 531  :     _Xlength_error("string too long");

  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_0BA@JFNIOLAK@string?5too?5long@
  0000b	e8 00 00 00 00	 call	 ?_Xlength_error@std@@YAXPEBD@Z ; std::_Xlength_error
  00010	90		 npad	 1
$LN2@Xlen_strin:

; 532  : }

  00011	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00015	c3		 ret	 0
?_Xlen_string@std@@YAXXZ ENDP				; std::_Xlen_string
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z
_TEXT	SEGMENT
_Back_shift$ = 48
_Ptr_container$ = 56
_Ptr_user$ = 64
_Min_back_shift$ = 72
_Ptr$ = 96
_Bytes$ = 104
?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z PROC ; std::_Adjust_manually_vector_aligned, COMDAT

; 200  : inline void _Adjust_manually_vector_aligned(void*& _Ptr, size_t& _Bytes) {

$LN5:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 201  :     // adjust parameters from _Allocate_manually_vector_aligned to pass to operator delete
; 202  :     _Bytes += _Non_user_size;

  0000e	48 8b 44 24 68	 mov	 rax, QWORD PTR _Bytes$[rsp]
  00013	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00016	48 83 c0 27	 add	 rax, 39			; 00000027H
  0001a	48 8b 4c 24 68	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  0001f	48 89 01	 mov	 QWORD PTR [rcx], rax

; 203  : 
; 204  :     const uintptr_t* const _Ptr_user = static_cast<uintptr_t*>(_Ptr);

  00022	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00027	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0002a	48 89 44 24 40	 mov	 QWORD PTR _Ptr_user$[rsp], rax

; 205  :     const uintptr_t _Ptr_container   = _Ptr_user[-1];

  0002f	b8 08 00 00 00	 mov	 eax, 8
  00034	48 6b c0 ff	 imul	 rax, rax, -1
  00038	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr_user$[rsp]
  0003d	48 8b 04 01	 mov	 rax, QWORD PTR [rcx+rax]
  00041	48 89 44 24 38	 mov	 QWORD PTR _Ptr_container$[rsp], rax

; 206  : 
; 207  :     // If the following asserts, it likely means that we are performing
; 208  :     // an aligned delete on memory coming from an unaligned allocation.
; 209  :     _STL_ASSERT(_Ptr_user[-2] == _Big_allocation_sentinel, "invalid argument");
; 210  : 
; 211  :     // Extra paranoia on aligned allocation/deallocation; ensure _Ptr_container is
; 212  :     // in range [_Min_back_shift, _Non_user_size]
; 213  : #ifdef _DEBUG
; 214  :     constexpr uintptr_t _Min_back_shift = 2 * sizeof(void*);
; 215  : #else // ^^^ defined(_DEBUG) / !defined(_DEBUG) vvv
; 216  :     constexpr uintptr_t _Min_back_shift = sizeof(void*);

  00046	48 c7 44 24 48
	08 00 00 00	 mov	 QWORD PTR _Min_back_shift$[rsp], 8

; 217  : #endif // ^^^ !defined(_DEBUG) ^^^
; 218  :     const uintptr_t _Back_shift = reinterpret_cast<uintptr_t>(_Ptr) - _Ptr_container;

  0004f	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00054	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr_container$[rsp]
  00059	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0005c	48 2b c1	 sub	 rax, rcx
  0005f	48 89 44 24 30	 mov	 QWORD PTR _Back_shift$[rsp], rax

; 219  :     _STL_VERIFY(_Back_shift >= _Min_back_shift && _Back_shift <= _Non_user_size, "invalid argument");

  00064	48 83 7c 24 30
	08		 cmp	 QWORD PTR _Back_shift$[rsp], 8
  0006a	72 08		 jb	 SHORT $LN3@Adjust_man
  0006c	48 83 7c 24 30
	27		 cmp	 QWORD PTR _Back_shift$[rsp], 39 ; 00000027H
  00072	76 19		 jbe	 SHORT $LN2@Adjust_man
$LN3@Adjust_man:
  00074	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR [rsp+32], 0
  0007d	45 33 c9	 xor	 r9d, r9d
  00080	45 33 c0	 xor	 r8d, r8d
  00083	33 d2		 xor	 edx, edx
  00085	33 c9		 xor	 ecx, ecx
  00087	e8 00 00 00 00	 call	 _invoke_watson
  0008c	90		 npad	 1
$LN2@Adjust_man:

; 220  :     _Ptr = reinterpret_cast<void*>(_Ptr_container);

  0008d	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00092	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr_container$[rsp]
  00097	48 89 08	 mov	 QWORD PTR [rax], rcx
$LN4@Adjust_man:

; 221  : }

  0009a	48 83 c4 58	 add	 rsp, 88			; 00000058H
  0009e	c3		 ret	 0
?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ENDP ; std::_Adjust_manually_vector_aligned
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?_Throw_bad_array_new_length@std@@YAXXZ
_TEXT	SEGMENT
$T1 = 32
?_Throw_bad_array_new_length@std@@YAXXZ PROC		; std::_Throw_bad_array_new_length, COMDAT

; 107  : [[noreturn]] inline void _Throw_bad_array_new_length() {

$LN3:
  00000	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 108  :     _THROW(bad_array_new_length{});

  00004	48 8d 4c 24 20	 lea	 rcx, QWORD PTR $T1[rsp]
  00009	e8 00 00 00 00	 call	 ??0bad_array_new_length@std@@QEAA@XZ ; std::bad_array_new_length::bad_array_new_length
  0000e	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:_TI3?AVbad_array_new_length@std@@
  00015	48 8d 4c 24 20	 lea	 rcx, QWORD PTR $T1[rsp]
  0001a	e8 00 00 00 00	 call	 _CxxThrowException
  0001f	90		 npad	 1
$LN2@Throw_bad_:

; 109  : }

  00020	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00024	c3		 ret	 0
?_Throw_bad_array_new_length@std@@YAXXZ ENDP		; std::_Throw_bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gbad_array_new_length@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gbad_array_new_length@std@@UEAAPEAXI@Z PROC		; std::bad_array_new_length::`scalar deleting destructor', COMDAT
$LN20:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gbad_array_new_length@std@@UEAAPEAXI@Z ENDP		; std::bad_array_new_length::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??0bad_array_new_length@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
__that$ = 56
??0bad_array_new_length@std@@QEAA@AEBV01@@Z PROC	; std::bad_array_new_length::bad_array_new_length, COMDAT
$LN14:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000e	48 8b 54 24 38	 mov	 rdx, QWORD PTR __that$[rsp]
  00013	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00018	e8 00 00 00 00	 call	 ??0bad_alloc@std@@QEAA@AEBV01@@Z
  0001d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00022	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_array_new_length@std@@6B@
  00029	48 89 08	 mov	 QWORD PTR [rax], rcx
  0002c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00031	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00035	c3		 ret	 0
??0bad_array_new_length@std@@QEAA@AEBV01@@Z ENDP	; std::bad_array_new_length::bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??1bad_array_new_length@std@@UEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1bad_array_new_length@std@@UEAA@XZ PROC		; std::bad_array_new_length::~bad_array_new_length, COMDAT
$LN14:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  00018	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 83 c0 08	 add	 rax, 8
  00021	48 8b c8	 mov	 rcx, rax
  00024	e8 00 00 00 00	 call	 __std_exception_destroy
  00029	90		 npad	 1
  0002a	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0002e	c3		 ret	 0
??1bad_array_new_length@std@@UEAA@XZ ENDP		; std::bad_array_new_length::~bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0bad_array_new_length@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 16
??0bad_array_new_length@std@@QEAA@XZ PROC		; std::bad_array_new_length::bad_array_new_length, COMDAT

; 144  :     {

$LN14:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi

; 67   :     {

  00006	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0000b	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00012	48 89 08	 mov	 QWORD PTR [rax], rcx

; 66   :         : _Data()

  00015	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 83 c0 08	 add	 rax, 8
  0001e	48 8b f8	 mov	 rdi, rax
  00021	33 c0		 xor	 eax, eax
  00023	b9 10 00 00 00	 mov	 ecx, 16
  00028	f3 aa		 rep stosb

; 68   :         _Data._What = _Message;

  0002a	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0002f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_0BF@KINCDENJ@bad?5array?5new?5length@
  00036	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 133  :     {

  0003a	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0003f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_alloc@std@@6B@
  00046	48 89 08	 mov	 QWORD PTR [rax], rcx

; 144  :     {

  00049	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_array_new_length@std@@6B@
  00055	48 89 08	 mov	 QWORD PTR [rax], rcx

; 145  :     }

  00058	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0005d	5f		 pop	 rdi
  0005e	c3		 ret	 0
??0bad_array_new_length@std@@QEAA@XZ ENDP		; std::bad_array_new_length::bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gbad_alloc@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gbad_alloc@std@@UEAAPEAXI@Z PROC			; std::bad_alloc::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gbad_alloc@std@@UEAAPEAXI@Z ENDP			; std::bad_alloc::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0bad_alloc@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
__that$ = 56
??0bad_alloc@std@@QEAA@AEBV01@@Z PROC			; std::bad_alloc::bad_alloc, COMDAT
$LN9:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 20	 sub	 rsp, 32			; 00000020H

; 73   :     {

  0000f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  0001b	48 89 08	 mov	 QWORD PTR [rax], rcx

; 72   :         : _Data()

  0001e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 83 c0 08	 add	 rax, 8
  00027	48 8b f8	 mov	 rdi, rax
  0002a	33 c0		 xor	 eax, eax
  0002c	b9 10 00 00 00	 mov	 ecx, 16
  00031	f3 aa		 rep stosb

; 74   :         __std_exception_copy(&_Other._Data, &_Data);

  00033	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 83 c0 08	 add	 rax, 8
  0003c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR __that$[rsp]
  00041	48 83 c1 08	 add	 rcx, 8
  00045	48 8b d0	 mov	 rdx, rax
  00048	e8 00 00 00 00	 call	 __std_exception_copy
  0004d	90		 npad	 1
  0004e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_alloc@std@@6B@
  0005a	48 89 08	 mov	 QWORD PTR [rax], rcx
  0005d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00062	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00066	5f		 pop	 rdi
  00067	c3		 ret	 0
??0bad_alloc@std@@QEAA@AEBV01@@Z ENDP			; std::bad_alloc::bad_alloc
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gexception@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gexception@std@@UEAAPEAXI@Z PROC			; std::exception::`scalar deleting destructor', COMDAT
$LN10:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gexception@std@@UEAAPEAXI@Z ENDP			; std::exception::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ?what@exception@std@@UEBAPEBDXZ
_TEXT	SEGMENT
tv69 = 0
this$ = 32
?what@exception@std@@UEBAPEBDXZ PROC			; std::exception::what, COMDAT

; 95   :     {

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 18	 sub	 rsp, 24

; 96   :         return _Data._What ? _Data._What : "Unknown exception";

  00009	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00013	74 0f		 je	 SHORT $LN3@what
  00015	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0001e	48 89 04 24	 mov	 QWORD PTR tv69[rsp], rax
  00022	eb 0b		 jmp	 SHORT $LN4@what
$LN3@what:
  00024	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BC@EOODALEL@Unknown?5exception@
  0002b	48 89 04 24	 mov	 QWORD PTR tv69[rsp], rax
$LN4@what:
  0002f	48 8b 04 24	 mov	 rax, QWORD PTR tv69[rsp]

; 97   :     }

  00033	48 83 c4 18	 add	 rsp, 24
  00037	c3		 ret	 0
?what@exception@std@@UEBAPEBDXZ ENDP			; std::exception::what
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0exception@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
_Other$ = 56
??0exception@std@@QEAA@AEBV01@@Z PROC			; std::exception::exception, COMDAT

; 73   :     {

$LN4:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  0000f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  0001b	48 89 08	 mov	 QWORD PTR [rax], rcx

; 72   :         : _Data()

  0001e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 83 c0 08	 add	 rax, 8
  00027	48 8b f8	 mov	 rdi, rax
  0002a	33 c0		 xor	 eax, eax
  0002c	b9 10 00 00 00	 mov	 ecx, 16
  00031	f3 aa		 rep stosb

; 74   :         __std_exception_copy(&_Other._Data, &_Data);

  00033	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 83 c0 08	 add	 rax, 8
  0003c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Other$[rsp]
  00041	48 83 c1 08	 add	 rcx, 8
  00045	48 8b d0	 mov	 rdx, rax
  00048	e8 00 00 00 00	 call	 __std_exception_copy
  0004d	90		 npad	 1

; 75   :     }

  0004e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00057	5f		 pop	 rdi
  00058	c3		 ret	 0
??0exception@std@@QEAA@AEBV01@@Z ENDP			; std::exception::exception
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\main.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\main.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
