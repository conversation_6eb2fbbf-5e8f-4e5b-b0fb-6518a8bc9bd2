; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	??1ContentsDeactivator@mu2@@UEAA@XZ		; mu2::ContentsDeactivator::~ContentsDeactivator
PUBLIC	?loadContentsDeactivateFile@ContentsDeactivator@mu2@@QEAAHXZ ; mu2::ContentsDeactivator::loadContentsDeactivateFile
PUBLIC	??_GContentsDeactivator@mu2@@UEAAPEAXI@Z	; mu2::ContentsDeactivator::`scalar deleting destructor'
PUBLIC	??_7ContentsDeactivator@mu2@@6B@		; mu2::ContentsDeactivator::`vftable'
PUBLIC	??_R4ContentsDeactivator@mu2@@6B@		; mu2::ContentsDeactivator::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVContentsDeactivator@mu2@@@8		; mu2::ContentsDeactivator `RTTI Type Descriptor'
PUBLIC	??_R3ContentsDeactivator@mu2@@8			; mu2::ContentsDeactivator::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2ContentsDeactivator@mu2@@8			; mu2::ContentsDeactivator::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@ContentsDeactivator@mu2@@8	; mu2::ContentsDeactivator::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@ContentsDeactivateSystem@mu2@@8	; mu2::ContentsDeactivateSystem::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R0?AVContentsDeactivateSystem@mu2@@@8	; mu2::ContentsDeactivateSystem `RTTI Type Descriptor'
PUBLIC	??_R3ContentsDeactivateSystem@mu2@@8		; mu2::ContentsDeactivateSystem::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2ContentsDeactivateSystem@mu2@@8		; mu2::ContentsDeactivateSystem::`RTTI Base Class Array'
EXTRN	??3@YAXPEAX_K@Z:PROC				; operator delete
EXTRN	??1ContentsDeactivateSystem@mu2@@UEAA@XZ:PROC	; mu2::ContentsDeactivateSystem::~ContentsDeactivateSystem
EXTRN	??_EContentsDeactivator@mu2@@UEAAPEAXI@Z:PROC	; mu2::ContentsDeactivator::`vector deleting destructor'
EXTRN	??_7type_info@@6B@:BYTE				; type_info::`vftable'
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1ContentsDeactivator@mu2@@UEAA@XZ DD imagerel $LN4
	DD	imagerel $LN4+40
	DD	imagerel $unwind$??1ContentsDeactivator@mu2@@UEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GContentsDeactivator@mu2@@UEAAPEAXI@Z DD imagerel $LN5
	DD	imagerel $LN5+60
	DD	imagerel $unwind$??_GContentsDeactivator@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT ??_R2ContentsDeactivateSystem@mu2@@8
rdata$r	SEGMENT
??_R2ContentsDeactivateSystem@mu2@@8 DD imagerel ??_R1A@?0A@EA@ContentsDeactivateSystem@mu2@@8 ; mu2::ContentsDeactivateSystem::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3ContentsDeactivateSystem@mu2@@8
rdata$r	SEGMENT
??_R3ContentsDeactivateSystem@mu2@@8 DD 00H		; mu2::ContentsDeactivateSystem::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2ContentsDeactivateSystem@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVContentsDeactivateSystem@mu2@@@8
data$rs	SEGMENT
??_R0?AVContentsDeactivateSystem@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::ContentsDeactivateSystem `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVContentsDeactivateSystem@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R1A@?0A@EA@ContentsDeactivateSystem@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@ContentsDeactivateSystem@mu2@@8 DD imagerel ??_R0?AVContentsDeactivateSystem@mu2@@@8 ; mu2::ContentsDeactivateSystem::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3ContentsDeactivateSystem@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@ContentsDeactivator@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@ContentsDeactivator@mu2@@8 DD imagerel ??_R0?AVContentsDeactivator@mu2@@@8 ; mu2::ContentsDeactivator::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3ContentsDeactivator@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2ContentsDeactivator@mu2@@8
rdata$r	SEGMENT
??_R2ContentsDeactivator@mu2@@8 DD imagerel ??_R1A@?0A@EA@ContentsDeactivator@mu2@@8 ; mu2::ContentsDeactivator::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@ContentsDeactivateSystem@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3ContentsDeactivator@mu2@@8
rdata$r	SEGMENT
??_R3ContentsDeactivator@mu2@@8 DD 00H			; mu2::ContentsDeactivator::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2ContentsDeactivator@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVContentsDeactivator@mu2@@@8
data$rs	SEGMENT
??_R0?AVContentsDeactivator@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::ContentsDeactivator `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVContentsDeactivator@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4ContentsDeactivator@mu2@@6B@
rdata$r	SEGMENT
??_R4ContentsDeactivator@mu2@@6B@ DD 01H		; mu2::ContentsDeactivator::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVContentsDeactivator@mu2@@@8
	DD	imagerel ??_R3ContentsDeactivator@mu2@@8
	DD	imagerel ??_R4ContentsDeactivator@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_7ContentsDeactivator@mu2@@6B@
CONST	SEGMENT
??_7ContentsDeactivator@mu2@@6B@ DQ FLAT:??_R4ContentsDeactivator@mu2@@6B@ ; mu2::ContentsDeactivator::`vftable'
	DQ	FLAT:??_EContentsDeactivator@mu2@@UEAAPEAXI@Z
CONST	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GContentsDeactivator@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1ContentsDeactivator@mu2@@UEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
; Function compile flags: /Odtp
;	COMDAT ??_GContentsDeactivator@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GContentsDeactivator@mu2@@UEAAPEAXI@Z PROC		; mu2::ContentsDeactivator::`scalar deleting destructor', COMDAT
$LN5:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00012	e8 00 00 00 00	 call	 ??1ContentsDeactivator@mu2@@UEAA@XZ ; mu2::ContentsDeactivator::~ContentsDeactivator
  00017	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  0001b	83 e0 01	 and	 eax, 1
  0001e	85 c0		 test	 eax, eax
  00020	74 10		 je	 SHORT $LN2@scalar
  00022	ba 48 00 00 00	 mov	 edx, 72			; 00000048H
  00027	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0002c	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00031	90		 npad	 1
$LN2@scalar:
  00032	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00037	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0003b	c3		 ret	 0
??_GContentsDeactivator@mu2@@UEAAPEAXI@Z ENDP		; mu2::ContentsDeactivator::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ContentsDeactivator.cpp
;	COMDAT ?loadContentsDeactivateFile@ContentsDeactivator@mu2@@QEAAHXZ
_TEXT	SEGMENT
this$ = 8
?loadContentsDeactivateFile@ContentsDeactivator@mu2@@QEAAHXZ PROC ; mu2::ContentsDeactivator::loadContentsDeactivateFile, COMDAT

; 11   : 	{

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 12   : #undef DEF
; 13   : #define DEF(TYPE) EVENT_MAP_REGISTER(TYPE)
; 14   : #ifdef  FOR_GLOBAL
; 15   : #include ".\ExternalData\EventDeactivate_INT.def" 
; 16   : #endif
; 17   : #ifdef  FOR_JAPAN
; 18   : #include ".\ExternalData\EventDeactivate_JPN.def" 
; 19   : #endif
; 20   : #undef DEF
; 21   : 		return 0;

  00005	33 c0		 xor	 eax, eax

; 22   : 	}

  00007	c3		 ret	 0
?loadContentsDeactivateFile@ContentsDeactivator@mu2@@QEAAHXZ ENDP ; mu2::ContentsDeactivator::loadContentsDeactivateFile
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ContentsDeactivator.cpp
;	COMDAT ??1ContentsDeactivator@mu2@@UEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1ContentsDeactivator@mu2@@UEAA@XZ PROC		; mu2::ContentsDeactivator::~ContentsDeactivator, COMDAT

; 7    : 	{

$LN4:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ContentsDeactivator@mu2@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx

; 8    : 	}

  00018	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0001d	e8 00 00 00 00	 call	 ??1ContentsDeactivateSystem@mu2@@UEAA@XZ ; mu2::ContentsDeactivateSystem::~ContentsDeactivateSystem
  00022	90		 npad	 1
  00023	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00027	c3		 ret	 0
??1ContentsDeactivator@mu2@@UEAA@XZ ENDP		; mu2::ContentsDeactivator::~ContentsDeactivator
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ContentsDeactivator.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ContentsDeactivator.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
