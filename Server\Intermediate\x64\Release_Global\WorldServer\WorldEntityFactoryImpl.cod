; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::operator delete
PUBLIC	??_GEntityFactoryImpl@mu2@@UEAAPEAXI@Z		; mu2::EntityFactoryImpl::`scalar deleting destructor'
PUBLIC	??0WorldEntityFactoryImpl@mu2@@QEAA@XZ		; mu2::WorldEntityFactoryImpl::WorldEntityFactoryImpl
PUBLIC	??1WorldEntityFactoryImpl@mu2@@UEAA@XZ		; mu2::WorldEntityFactoryImpl::~WorldEntityFactoryImpl
PUBLIC	?Create@WorldEntityFactoryImpl@mu2@@UEAAPEAVEntity@2@AEBI0@Z ; mu2::WorldEntityFactoryImpl::Create
PUBLIC	?GetType@WorldEntityFactoryImpl@mu2@@UEAAGXZ	; mu2::WorldEntityFactoryImpl::GetType
PUBLIC	??_GWorldEntityFactoryImpl@mu2@@UEAAPEAXI@Z	; mu2::WorldEntityFactoryImpl::`scalar deleting destructor'
PUBLIC	??_7EntityFactoryImpl@mu2@@6B@			; mu2::EntityFactoryImpl::`vftable'
PUBLIC	??_R17?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Base Class Descriptor at (8,-1,0,64)'
PUBLIC	??_R0?AV?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> > `RTTI Type Descriptor'
PUBLIC	??_R3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4EntityFactoryImpl@mu2@@6B@			; mu2::EntityFactoryImpl::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVEntityFactoryImpl@mu2@@@8		; mu2::EntityFactoryImpl `RTTI Type Descriptor'
PUBLIC	??_R3EntityFactoryImpl@mu2@@8			; mu2::EntityFactoryImpl::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2EntityFactoryImpl@mu2@@8			; mu2::EntityFactoryImpl::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@EntityFactoryImpl@mu2@@8		; mu2::EntityFactoryImpl::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_7WorldEntityFactoryImpl@mu2@@6B@		; mu2::WorldEntityFactoryImpl::`vftable'
PUBLIC	??_R4WorldEntityFactoryImpl@mu2@@6B@		; mu2::WorldEntityFactoryImpl::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVWorldEntityFactoryImpl@mu2@@@8		; mu2::WorldEntityFactoryImpl `RTTI Type Descriptor'
PUBLIC	??_R3WorldEntityFactoryImpl@mu2@@8		; mu2::WorldEntityFactoryImpl::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2WorldEntityFactoryImpl@mu2@@8		; mu2::WorldEntityFactoryImpl::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@WorldEntityFactoryImpl@mu2@@8	; mu2::WorldEntityFactoryImpl::`RTTI Base Class Descriptor at (0,-1,0,64)'
EXTRN	_purecall:PROC
EXTRN	?__global_delete@@YAXPEAX_K@Z:PROC		; __global_delete
EXTRN	free:PROC
EXTRN	malloc:PROC
EXTRN	?Init@Entity@mu2@@QEAA_NAEBUstEntityId@2@@Z:PROC ; mu2::Entity::Init
EXTRN	??_EEntityFactoryImpl@mu2@@UEAAPEAXI@Z:PROC	; mu2::EntityFactoryImpl::`vector deleting destructor'
EXTRN	??0EntityPlayer@mu2@@QEAA@III@Z:PROC		; mu2::EntityPlayer::EntityPlayer
EXTRN	??_EWorldEntityFactoryImpl@mu2@@UEAAPEAXI@Z:PROC ; mu2::WorldEntityFactoryImpl::`vector deleting destructor'
EXTRN	__CxxFrameHandler4:PROC
EXTRN	??_7type_info@@6B@:BYTE				; type_info::`vftable'
;	COMDAT pdata
pdata	SEGMENT
$pdata$??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z DD imagerel $LN6
	DD	imagerel $LN6+25
	DD	imagerel $unwind$??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GEntityFactoryImpl@mu2@@UEAAPEAXI@Z DD imagerel $LN25
	DD	imagerel $LN25+89
	DD	imagerel $unwind$??_GEntityFactoryImpl@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Create@WorldEntityFactoryImpl@mu2@@UEAAPEAVEntity@2@AEBI0@Z DD imagerel $LN12
	DD	imagerel $LN12+200
	DD	imagerel $unwind$?Create@WorldEntityFactoryImpl@mu2@@UEAAPEAVEntity@2@AEBI0@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0??Create@WorldEntityFactoryImpl@mu2@@UEAAPEAVEntity@2@AEBI0@Z@4HA DD imagerel ?dtor$0@?0??Create@WorldEntityFactoryImpl@mu2@@UEAAPEAVEntity@2@AEBI0@Z@4HA
	DD	imagerel ?dtor$0@?0??Create@WorldEntityFactoryImpl@mu2@@UEAAPEAVEntity@2@AEBI0@Z@4HA+24
	DD	imagerel $unwind$?dtor$0@?0??Create@WorldEntityFactoryImpl@mu2@@UEAAPEAVEntity@2@AEBI0@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GWorldEntityFactoryImpl@mu2@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+84
	DD	imagerel $unwind$??_GWorldEntityFactoryImpl@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT ??_R1A@?0A@EA@WorldEntityFactoryImpl@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@WorldEntityFactoryImpl@mu2@@8 DD imagerel ??_R0?AVWorldEntityFactoryImpl@mu2@@@8 ; mu2::WorldEntityFactoryImpl::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	02H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3WorldEntityFactoryImpl@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2WorldEntityFactoryImpl@mu2@@8
rdata$r	SEGMENT
??_R2WorldEntityFactoryImpl@mu2@@8 DD imagerel ??_R1A@?0A@EA@WorldEntityFactoryImpl@mu2@@8 ; mu2::WorldEntityFactoryImpl::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@EntityFactoryImpl@mu2@@8
	DD	imagerel ??_R17?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3WorldEntityFactoryImpl@mu2@@8
rdata$r	SEGMENT
??_R3WorldEntityFactoryImpl@mu2@@8 DD 00H		; mu2::WorldEntityFactoryImpl::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	03H
	DD	imagerel ??_R2WorldEntityFactoryImpl@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVWorldEntityFactoryImpl@mu2@@@8
data$rs	SEGMENT
??_R0?AVWorldEntityFactoryImpl@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::WorldEntityFactoryImpl `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVWorldEntityFactoryImpl@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4WorldEntityFactoryImpl@mu2@@6B@
rdata$r	SEGMENT
??_R4WorldEntityFactoryImpl@mu2@@6B@ DD 01H		; mu2::WorldEntityFactoryImpl::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVWorldEntityFactoryImpl@mu2@@@8
	DD	imagerel ??_R3WorldEntityFactoryImpl@mu2@@8
	DD	imagerel ??_R4WorldEntityFactoryImpl@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_7WorldEntityFactoryImpl@mu2@@6B@
CONST	SEGMENT
??_7WorldEntityFactoryImpl@mu2@@6B@ DQ FLAT:??_R4WorldEntityFactoryImpl@mu2@@6B@ ; mu2::WorldEntityFactoryImpl::`vftable'
	DQ	FLAT:??_EWorldEntityFactoryImpl@mu2@@UEAAPEAXI@Z
	DQ	FLAT:?Create@WorldEntityFactoryImpl@mu2@@UEAAPEAVEntity@2@AEBI0@Z
	DQ	FLAT:?GetType@WorldEntityFactoryImpl@mu2@@UEAAGXZ
CONST	ENDS
;	COMDAT ??_R1A@?0A@EA@EntityFactoryImpl@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@EntityFactoryImpl@mu2@@8 DD imagerel ??_R0?AVEntityFactoryImpl@mu2@@@8 ; mu2::EntityFactoryImpl::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3EntityFactoryImpl@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2EntityFactoryImpl@mu2@@8
rdata$r	SEGMENT
??_R2EntityFactoryImpl@mu2@@8 DD imagerel ??_R1A@?0A@EA@EntityFactoryImpl@mu2@@8 ; mu2::EntityFactoryImpl::`RTTI Base Class Array'
	DD	imagerel ??_R17?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3EntityFactoryImpl@mu2@@8
rdata$r	SEGMENT
??_R3EntityFactoryImpl@mu2@@8 DD 00H			; mu2::EntityFactoryImpl::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2EntityFactoryImpl@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVEntityFactoryImpl@mu2@@@8
data$rs	SEGMENT
??_R0?AVEntityFactoryImpl@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::EntityFactoryImpl `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVEntityFactoryImpl@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4EntityFactoryImpl@mu2@@6B@
rdata$r	SEGMENT
??_R4EntityFactoryImpl@mu2@@6B@ DD 01H			; mu2::EntityFactoryImpl::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVEntityFactoryImpl@mu2@@@8
	DD	imagerel ??_R3EntityFactoryImpl@mu2@@8
	DD	imagerel ??_R4EntityFactoryImpl@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 DD imagerel ??_R0?AV?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
rdata$r	ENDS
;	COMDAT ??_R2?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
rdata$r	SEGMENT
??_R2?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 DD imagerel ??_R1A@?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
rdata$r	SEGMENT
??_R3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 DD 00H ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
rdata$r	ENDS
;	COMDAT ??_R0?AV?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@@8
data$rs	SEGMENT
??_R0?AV?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@@8 DQ FLAT:??_7type_info@@6B@ ; AllocatedObject<mu2::CategorisedAllocPolicy<0> > `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AV?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2'
	DB	'@@@@', 00H
data$rs	ENDS
;	COMDAT ??_R17?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
rdata$r	SEGMENT
??_R17?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 DD imagerel ??_R0?AV?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Base Class Descriptor at (8,-1,0,64)'
	DD	00H
	DD	08H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
rdata$r	ENDS
;	COMDAT ??_7EntityFactoryImpl@mu2@@6B@
CONST	SEGMENT
??_7EntityFactoryImpl@mu2@@6B@ DQ FLAT:??_R4EntityFactoryImpl@mu2@@6B@ ; mu2::EntityFactoryImpl::`vftable'
	DQ	FLAT:??_EEntityFactoryImpl@mu2@@UEAAPEAXI@Z
	DQ	FLAT:_purecall
	DQ	FLAT:_purecall
CONST	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GWorldEntityFactoryImpl@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0??Create@WorldEntityFactoryImpl@mu2@@UEAAPEAVEntity@2@AEBI0@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?Create@WorldEntityFactoryImpl@mu2@@UEAAPEAVEntity@2@AEBI0@Z DB 06H
	DB	00H
	DB	00H
	DB	'l'
	DB	02H
	DB	'z'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?Create@WorldEntityFactoryImpl@mu2@@UEAAPEAVEntity@2@AEBI0@Z DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0??Create@WorldEntityFactoryImpl@mu2@@UEAAPEAVEntity@2@AEBI0@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?Create@WorldEntityFactoryImpl@mu2@@UEAAPEAVEntity@2@AEBI0@Z DB 028H
	DD	imagerel $stateUnwindMap$?Create@WorldEntityFactoryImpl@mu2@@UEAAPEAVEntity@2@AEBI0@Z
	DD	imagerel $ip2state$?Create@WorldEntityFactoryImpl@mu2@@UEAAPEAVEntity@2@AEBI0@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Create@WorldEntityFactoryImpl@mu2@@UEAAPEAVEntity@2@AEBI0@Z DD 011311H
	DD	0e213H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?Create@WorldEntityFactoryImpl@mu2@@UEAAPEAVEntity@2@AEBI0@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GEntityFactoryImpl@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z DD 010901H
	DD	04209H
xdata	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h
;	COMDAT ??_GWorldEntityFactoryImpl@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GWorldEntityFactoryImpl@mu2@@UEAAPEAXI@Z PROC	; mu2::WorldEntityFactoryImpl::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00012	e8 00 00 00 00	 call	 ??1WorldEntityFactoryImpl@mu2@@UEAA@XZ ; mu2::WorldEntityFactoryImpl::~WorldEntityFactoryImpl
  00017	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  0001b	83 e0 01	 and	 eax, 1
  0001e	85 c0		 test	 eax, eax
  00020	74 28		 je	 SHORT $LN2@scalar
  00022	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00026	83 e0 04	 and	 eax, 4
  00029	85 c0		 test	 eax, eax
  0002b	75 0d		 jne	 SHORT $LN3@scalar

; 45   : 			::free(ptr);

  0002d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00032	e8 00 00 00 00	 call	 free
  00037	90		 npad	 1
  00038	eb 10		 jmp	 SHORT $LN2@scalar
$LN3@scalar:
  0003a	ba 08 00 00 00	 mov	 edx, 8
  0003f	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00044	e8 00 00 00 00	 call	 ?__global_delete@@YAXPEAX_K@Z ; __global_delete
  00049	90		 npad	 1
$LN2@scalar:
  0004a	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004f	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00053	c3		 ret	 0
??_GWorldEntityFactoryImpl@mu2@@UEAAPEAXI@Z ENDP	; mu2::WorldEntityFactoryImpl::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldEntityFactoryImpl.h
;	COMDAT ?GetType@WorldEntityFactoryImpl@mu2@@UEAAGXZ
_TEXT	SEGMENT
this$ = 8
?GetType@WorldEntityFactoryImpl@mu2@@UEAAGXZ PROC	; mu2::WorldEntityFactoryImpl::GetType, COMDAT

; 15   : 	UInt16 GetType() { return WorldEntityTypes::PLAYER; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	33 c0		 xor	 eax, eax
  00007	c3		 ret	 0
?GetType@WorldEntityFactoryImpl@mu2@@UEAAGXZ ENDP	; mu2::WorldEntityFactoryImpl::GetType
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldEntityFactoryImpl.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldEntityFactoryImpl.cpp
; File F:\Release_Branch\Server\Development\Framework\Entity\Entity.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldEntityFactoryImpl.cpp
;	COMDAT ?Create@WorldEntityFactoryImpl@mu2@@UEAAPEAVEntity@2@AEBI0@Z
_TEXT	SEGMENT
seq$ = 32
$T1 = 40
$T2 = 48
tv77 = 56
playerEntity$ = 64
$T3 = 72
$T4 = 80
$T5 = 88
$T6 = 96
this$ = 128
id$ = 136
__formal$ = 144
?Create@WorldEntityFactoryImpl@mu2@@UEAAPEAVEntity@2@AEBI0@Z PROC ; mu2::WorldEntityFactoryImpl::Create, COMDAT

; 19   : {

$LN12:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 78	 sub	 rsp, 120		; 00000078H
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h

; 26   : 			return malloc(size);

  00013	b9 40 29 00 00	 mov	 ecx, 10560		; 00002940H
  00018	e8 00 00 00 00	 call	 malloc
  0001d	48 89 44 24 48	 mov	 QWORD PTR $T3[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h

; 26   : 		return Alloc::allocate(size);

  00022	48 8b 44 24 48	 mov	 rax, QWORD PTR $T3[rsp]
  00027	48 89 44 24 50	 mov	 QWORD PTR $T4[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldEntityFactoryImpl.cpp

; 20   : 	EntityPlayer* playerEntity = new EntityPlayer( ATTRIBUTE_MAX , VIEW_MAX, ACTION_MAX);

  0002c	48 8b 44 24 50	 mov	 rax, QWORD PTR $T4[rsp]
  00031	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
  00036	48 83 7c 24 30
	00		 cmp	 QWORD PTR $T2[rsp], 0
  0003c	74 22		 je	 SHORT $LN3@Create
  0003e	41 b9 17 00 00
	00		 mov	 r9d, 23
  00044	41 b8 01 00 00
	00		 mov	 r8d, 1
  0004a	ba 09 00 00 00	 mov	 edx, 9
  0004f	48 8b 4c 24 30	 mov	 rcx, QWORD PTR $T2[rsp]
  00054	e8 00 00 00 00	 call	 ??0EntityPlayer@mu2@@QEAA@III@Z ; mu2::EntityPlayer::EntityPlayer
  00059	48 89 44 24 38	 mov	 QWORD PTR tv77[rsp], rax
  0005e	eb 09		 jmp	 SHORT $LN4@Create
$LN3@Create:
  00060	48 c7 44 24 38
	00 00 00 00	 mov	 QWORD PTR tv77[rsp], 0
$LN4@Create:
  00069	48 8b 44 24 38	 mov	 rax, QWORD PTR tv77[rsp]
  0006e	48 89 44 24 58	 mov	 QWORD PTR $T5[rsp], rax
  00073	48 8b 44 24 58	 mov	 rax, QWORD PTR $T5[rsp]
  00078	48 89 44 24 40	 mov	 QWORD PTR playerEntity$[rsp], rax

; 21   : 	playerEntity->Init(stEntityId( WorldEntityTypes::PLAYER, id ) );

  0007d	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR id$[rsp]
  00085	8b 00		 mov	 eax, DWORD PTR [rax]
  00087	89 44 24 20	 mov	 DWORD PTR seq$[rsp], eax
; File F:\Release_Branch\Server\Development\Framework\Entity\Entity.h

; 48   : 		: Type( type ), worldId( 0 ), Seq( seq )

  0008b	8b 44 24 20	 mov	 eax, DWORD PTR seq$[rsp]
  0008f	89 44 24 28	 mov	 DWORD PTR $T1[rsp], eax
  00093	33 c0		 xor	 eax, eax
  00095	66 89 44 24 2c	 mov	 WORD PTR $T1[rsp+4], ax
  0009a	33 c0		 xor	 eax, eax
  0009c	66 89 44 24 2e	 mov	 WORD PTR $T1[rsp+6], ax

; 49   : 	{		
; 50   : 	}

  000a1	48 8d 44 24 28	 lea	 rax, QWORD PTR $T1[rsp]
  000a6	48 89 44 24 60	 mov	 QWORD PTR $T6[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldEntityFactoryImpl.cpp

; 21   : 	playerEntity->Init(stEntityId( WorldEntityTypes::PLAYER, id ) );

  000ab	48 8b 44 24 60	 mov	 rax, QWORD PTR $T6[rsp]
  000b0	48 8b d0	 mov	 rdx, rax
  000b3	48 8b 4c 24 40	 mov	 rcx, QWORD PTR playerEntity$[rsp]
  000b8	e8 00 00 00 00	 call	 ?Init@Entity@mu2@@QEAA_NAEBUstEntityId@2@@Z ; mu2::Entity::Init
  000bd	90		 npad	 1

; 22   : 	return playerEntity; 

  000be	48 8b 44 24 40	 mov	 rax, QWORD PTR playerEntity$[rsp]

; 23   : }

  000c3	48 83 c4 78	 add	 rsp, 120		; 00000078H
  000c7	c3		 ret	 0
?Create@WorldEntityFactoryImpl@mu2@@UEAAPEAVEntity@2@AEBI0@Z ENDP ; mu2::WorldEntityFactoryImpl::Create
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
seq$ = 32
$T1 = 40
$T2 = 48
tv77 = 56
playerEntity$ = 64
$T3 = 72
$T4 = 80
$T5 = 88
$T6 = 96
this$ = 128
id$ = 136
__formal$ = 144
?dtor$0@?0??Create@WorldEntityFactoryImpl@mu2@@UEAAPEAVEntity@2@AEBI0@Z@4HA PROC ; `mu2::WorldEntityFactoryImpl::Create'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 30	 mov	 rcx, QWORD PTR $T2[rbp]
  0000d	e8 00 00 00 00	 call	 ??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::operator delete
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$0@?0??Create@WorldEntityFactoryImpl@mu2@@UEAAPEAVEntity@2@AEBI0@Z@4HA ENDP ; `mu2::WorldEntityFactoryImpl::Create'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldEntityFactoryImpl.cpp
; File F:\Release_Branch\Server\Development\Framework\Entity\EntityFactoryImpl.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldEntityFactoryImpl.cpp
;	COMDAT ??1WorldEntityFactoryImpl@mu2@@UEAA@XZ
_TEXT	SEGMENT
this$ = 8
??1WorldEntityFactoryImpl@mu2@@UEAA@XZ PROC		; mu2::WorldEntityFactoryImpl::~WorldEntityFactoryImpl, COMDAT

; 14   : {

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0000a	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7WorldEntityFactoryImpl@mu2@@6B@
  00011	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Framework\Entity\EntityFactoryImpl.h

; 11   : 	virtual ~EntityFactoryImpl() {}

  00014	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00019	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7EntityFactoryImpl@mu2@@6B@
  00020	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldEntityFactoryImpl.cpp

; 16   : }

  00023	c3		 ret	 0
??1WorldEntityFactoryImpl@mu2@@UEAA@XZ ENDP		; mu2::WorldEntityFactoryImpl::~WorldEntityFactoryImpl
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldEntityFactoryImpl.cpp
;	COMDAT ??0WorldEntityFactoryImpl@mu2@@QEAA@XZ
_TEXT	SEGMENT
this$ = 8
??0WorldEntityFactoryImpl@mu2@@QEAA@XZ PROC		; mu2::WorldEntityFactoryImpl::WorldEntityFactoryImpl, COMDAT

; 9    : {

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0000a	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7EntityFactoryImpl@mu2@@6B@
  00011	48 89 08	 mov	 QWORD PTR [rax], rcx
  00014	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00019	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7WorldEntityFactoryImpl@mu2@@6B@
  00020	48 89 08	 mov	 QWORD PTR [rax], rcx

; 10   : 
; 11   : }

  00023	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00028	c3		 ret	 0
??0WorldEntityFactoryImpl@mu2@@QEAA@XZ ENDP		; mu2::WorldEntityFactoryImpl::WorldEntityFactoryImpl
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Entity\EntityFactoryImpl.h
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h
;	COMDAT ??_GEntityFactoryImpl@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GEntityFactoryImpl@mu2@@UEAAPEAXI@Z PROC		; mu2::EntityFactoryImpl::`scalar deleting destructor', COMDAT
$LN25:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H
; File F:\Release_Branch\Server\Development\Framework\Entity\EntityFactoryImpl.h

; 11   : 	virtual ~EntityFactoryImpl() {}

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7EntityFactoryImpl@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx
  0001c	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00020	83 e0 01	 and	 eax, 1
  00023	85 c0		 test	 eax, eax
  00025	74 28		 je	 SHORT $LN2@scalar
  00027	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  0002b	83 e0 04	 and	 eax, 4
  0002e	85 c0		 test	 eax, eax
  00030	75 0d		 jne	 SHORT $LN3@scalar
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h

; 45   : 			::free(ptr);

  00032	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00037	e8 00 00 00 00	 call	 free
  0003c	90		 npad	 1
  0003d	eb 10		 jmp	 SHORT $LN2@scalar
$LN3@scalar:
  0003f	ba 08 00 00 00	 mov	 edx, 8
  00044	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00049	e8 00 00 00 00	 call	 ?__global_delete@@YAXPEAX_K@Z ; __global_delete
  0004e	90		 npad	 1
$LN2@scalar:
  0004f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00054	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00058	c3		 ret	 0
??_GEntityFactoryImpl@mu2@@UEAAPEAXI@Z ENDP		; mu2::EntityFactoryImpl::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h
;	COMDAT ??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z
_TEXT	SEGMENT
ptr$ = 48
??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z PROC ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::operator delete, COMDAT

; 57   : 	{

$LN6:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h

; 45   : 			::free(ptr);

  00009	48 8b 4c 24 30	 mov	 rcx, QWORD PTR ptr$[rsp]
  0000e	e8 00 00 00 00	 call	 free
  00013	90		 npad	 1
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h

; 59   : 	}

  00014	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00018	c3		 ret	 0
??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z ENDP ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::operator delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldEntityFactoryImpl.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldEntityFactoryImpl.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
