; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	?IsChannel@Execution@mu2@@UEBA_NXZ		; mu2::Execution::IsChannel
PUBLIC	?IsWorldcross@Execution@mu2@@UEBA_NXZ		; mu2::Execution::IsWorldcross
PUBLIC	?IsTournamentEnter@Execution@mu2@@UEBA_NXZ	; mu2::Execution::IsTournamentEnter
PUBLIC	?IsWorldcrossing@Execution@mu2@@UEBA_NXZ	; mu2::Execution::IsWorldcrossing
PUBLIC	?IsGroup@Execution@mu2@@UEBA_NXZ		; mu2::Execution::IsGroup
PUBLIC	?IsNull@Execution@mu2@@UEBA_NXZ			; mu2::Execution::IsNull
PUBLIC	?IsInstance@Instance@mu2@@UEBA_NXZ		; mu2::Instance::IsInstance
PUBLIC	?IsValid@Instance@mu2@@UEBA?B_NXZ		; mu2::Instance::IsValid
PUBLIC	?GetGroupKey@Instance@mu2@@UEBA?BIXZ		; mu2::Instance::GetGroupKey
PUBLIC	?IsSingle@InstanceSinglestage@mu2@@UEBA_NXZ	; mu2::InstanceSinglestage::IsSingle
PUBLIC	??_GInstanceSinglestage@mu2@@UEAAPEAXI@Z	; mu2::InstanceSinglestage::`scalar deleting destructor'
PUBLIC	??0InstanceNormal@mu2@@QEAA@PEAVManagerExecutionBase@1@AEAUExecutionCreateInfo@1@@Z ; mu2::InstanceNormal::InstanceNormal
PUBLIC	??1InstanceNormal@mu2@@UEAA@XZ			; mu2::InstanceNormal::~InstanceNormal
PUBLIC	??_GInstanceNormal@mu2@@UEAAPEAXI@Z		; mu2::InstanceNormal::`scalar deleting destructor'
PUBLIC	??_7InstanceSinglestage@mu2@@6B@		; mu2::InstanceSinglestage::`vftable'
PUBLIC	??_7InstanceNormal@mu2@@6B@			; mu2::InstanceNormal::`vftable'
PUBLIC	??_R4InstanceSinglestage@mu2@@6B@		; mu2::InstanceSinglestage::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVInstanceSinglestage@mu2@@@8		; mu2::InstanceSinglestage `RTTI Type Descriptor'
PUBLIC	??_R3InstanceSinglestage@mu2@@8			; mu2::InstanceSinglestage::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2InstanceSinglestage@mu2@@8			; mu2::InstanceSinglestage::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@InstanceSinglestage@mu2@@8	; mu2::InstanceSinglestage::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@Instance@mu2@@8			; mu2::Instance::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R0?AVInstance@mu2@@@8			; mu2::Instance `RTTI Type Descriptor'
PUBLIC	??_R3Instance@mu2@@8				; mu2::Instance::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2Instance@mu2@@8				; mu2::Instance::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@Execution@mu2@@8			; mu2::Execution::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R0?AVExecution@mu2@@@8			; mu2::Execution `RTTI Type Descriptor'
PUBLIC	??_R3Execution@mu2@@8				; mu2::Execution::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2Execution@mu2@@8				; mu2::Execution::`RTTI Base Class Array'
PUBLIC	??_R4InstanceNormal@mu2@@6B@			; mu2::InstanceNormal::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVInstanceNormal@mu2@@@8			; mu2::InstanceNormal `RTTI Type Descriptor'
PUBLIC	??_R3InstanceNormal@mu2@@8			; mu2::InstanceNormal::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2InstanceNormal@mu2@@8			; mu2::InstanceNormal::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@InstanceNormal@mu2@@8		; mu2::InstanceNormal::`RTTI Base Class Descriptor at (0,-1,0,64)'
EXTRN	??3@YAXPEAX_K@Z:PROC				; operator delete
EXTRN	?OnDestroyedFailed@Execution@mu2@@UEAAXXZ:PROC	; mu2::Execution::OnDestroyedFailed
EXTRN	?OnCreatedFailed@Execution@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::Execution::OnCreatedFailed
EXTRN	?EnterExecutionPrev@Execution@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::Execution::EnterExecutionPrev
EXTRN	?EnterExecutionPost@Execution@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::Execution::EnterExecutionPost
EXTRN	?OnShutdownZone@Execution@mu2@@UEAAXXZ:PROC	; mu2::Execution::OnShutdownZone
EXTRN	?IsValid@Execution@mu2@@UEBA?B_NXZ:PROC		; mu2::Execution::IsValid
EXTRN	?IsJoinable@Execution@mu2@@UEBA_NPEAVEntityPlayer@2@@Z:PROC ; mu2::Execution::IsJoinable
EXTRN	?GetRelayServerId@Execution@mu2@@UEBA?BIXZ:PROC	; mu2::Execution::GetRelayServerId
EXTRN	?Fillup@Execution@mu2@@MEBA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAPEAUEwzReqJoinExecution@2@@Z:PROC ; mu2::Execution::Fillup
EXTRN	??0Instance@mu2@@QEAA@PEAVManagerExecutionBase@1@AEAUExecutionCreateInfo@1@@Z:PROC ; mu2::Instance::Instance
EXTRN	??1Instance@mu2@@UEAA@XZ:PROC			; mu2::Instance::~Instance
EXTRN	?SendReqJoinExecution@Instance@mu2@@UEBA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@@Z:PROC ; mu2::Instance::SendReqJoinExecution
EXTRN	?EnterExecution@Instance@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::Instance::EnterExecution
EXTRN	?ExitExecutionPost@Instance@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::Instance::ExitExecutionPost
EXTRN	?OnResGameReady@Instance@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::Instance::OnResGameReady
EXTRN	?OnCreatedSucceed@Instance@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::Instance::OnCreatedSucceed
EXTRN	?OnDestroyed@Instance@mu2@@UEAAXXZ:PROC		; mu2::Instance::OnDestroyed
EXTRN	?IsDestroyable@Instance@mu2@@UEBA_NXZ:PROC	; mu2::Instance::IsDestroyable
EXTRN	?IsRunning@Instance@mu2@@UEBA_NXZ:PROC		; mu2::Instance::IsRunning
EXTRN	?GetDamageMeterKey@Instance@mu2@@UEBA?BIXZ:PROC	; mu2::Instance::GetDamageMeterKey
EXTRN	?ToString@Instance@mu2@@UEBA?BV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ:PROC ; mu2::Instance::ToString
EXTRN	?Init@Instance@mu2@@UEAA_NAEAUExecutionCreateInfo@2@@Z:PROC ; mu2::Instance::Init
EXTRN	?Fillup@Instance@mu2@@UEAAXAEAPEAUEwzReqCreateExecution@2@@Z:PROC ; mu2::Instance::Fillup
EXTRN	?Fillup@Instance@mu2@@UEAAXAEAPEAUEwzReqDestroyExecution@2@@Z:PROC ; mu2::Instance::Fillup
EXTRN	?Fillup@Instance@mu2@@UEBAXAEAVJoinZoneContextDest@2@@Z:PROC ; mu2::Instance::Fillup
EXTRN	?Update@InstanceSinglestage@mu2@@UEAAXXZ:PROC	; mu2::InstanceSinglestage::Update
EXTRN	??_EInstanceSinglestage@mu2@@UEAAPEAXI@Z:PROC	; mu2::InstanceSinglestage::`vector deleting destructor'
EXTRN	??_EInstanceNormal@mu2@@UEAAPEAXI@Z:PROC	; mu2::InstanceNormal::`vector deleting destructor'
EXTRN	??_7type_info@@6B@:BYTE				; type_info::`vftable'
;	COMDAT pdata
pdata	SEGMENT
$pdata$?IsValid@Instance@mu2@@UEBA?B_NXZ DD imagerel $LN5
	DD	imagerel $LN5+62
	DD	imagerel $unwind$?IsValid@Instance@mu2@@UEBA?B_NXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GInstanceSinglestage@mu2@@UEAAPEAXI@Z DD imagerel $LN10
	DD	imagerel $LN10+76
	DD	imagerel $unwind$??_GInstanceSinglestage@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0InstanceNormal@mu2@@QEAA@PEAVManagerExecutionBase@1@AEAUExecutionCreateInfo@1@@Z DD imagerel $LN7
	DD	imagerel $LN7+79
	DD	imagerel $unwind$??0InstanceNormal@mu2@@QEAA@PEAVManagerExecutionBase@1@AEAUExecutionCreateInfo@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1InstanceNormal@mu2@@UEAA@XZ DD imagerel $LN9
	DD	imagerel $LN9+55
	DD	imagerel $unwind$??1InstanceNormal@mu2@@UEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GInstanceNormal@mu2@@UEAAPEAXI@Z DD imagerel $LN5
	DD	imagerel $LN5+60
	DD	imagerel $unwind$??_GInstanceNormal@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT ??_R1A@?0A@EA@InstanceNormal@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@InstanceNormal@mu2@@8 DD imagerel ??_R0?AVInstanceNormal@mu2@@@8 ; mu2::InstanceNormal::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	03H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3InstanceNormal@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2InstanceNormal@mu2@@8
rdata$r	SEGMENT
??_R2InstanceNormal@mu2@@8 DD imagerel ??_R1A@?0A@EA@InstanceNormal@mu2@@8 ; mu2::InstanceNormal::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@InstanceSinglestage@mu2@@8
	DD	imagerel ??_R1A@?0A@EA@Instance@mu2@@8
	DD	imagerel ??_R1A@?0A@EA@Execution@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3InstanceNormal@mu2@@8
rdata$r	SEGMENT
??_R3InstanceNormal@mu2@@8 DD 00H			; mu2::InstanceNormal::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	04H
	DD	imagerel ??_R2InstanceNormal@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVInstanceNormal@mu2@@@8
data$rs	SEGMENT
??_R0?AVInstanceNormal@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::InstanceNormal `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVInstanceNormal@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4InstanceNormal@mu2@@6B@
rdata$r	SEGMENT
??_R4InstanceNormal@mu2@@6B@ DD 01H			; mu2::InstanceNormal::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVInstanceNormal@mu2@@@8
	DD	imagerel ??_R3InstanceNormal@mu2@@8
	DD	imagerel ??_R4InstanceNormal@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R2Execution@mu2@@8
rdata$r	SEGMENT
??_R2Execution@mu2@@8 DD imagerel ??_R1A@?0A@EA@Execution@mu2@@8 ; mu2::Execution::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3Execution@mu2@@8
rdata$r	SEGMENT
??_R3Execution@mu2@@8 DD 00H				; mu2::Execution::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2Execution@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVExecution@mu2@@@8
data$rs	SEGMENT
??_R0?AVExecution@mu2@@@8 DQ FLAT:??_7type_info@@6B@	; mu2::Execution `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVExecution@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R1A@?0A@EA@Execution@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@Execution@mu2@@8 DD imagerel ??_R0?AVExecution@mu2@@@8 ; mu2::Execution::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3Execution@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2Instance@mu2@@8
rdata$r	SEGMENT
??_R2Instance@mu2@@8 DD imagerel ??_R1A@?0A@EA@Instance@mu2@@8 ; mu2::Instance::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@Execution@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3Instance@mu2@@8
rdata$r	SEGMENT
??_R3Instance@mu2@@8 DD 00H				; mu2::Instance::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2Instance@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVInstance@mu2@@@8
data$rs	SEGMENT
??_R0?AVInstance@mu2@@@8 DQ FLAT:??_7type_info@@6B@	; mu2::Instance `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVInstance@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R1A@?0A@EA@Instance@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@Instance@mu2@@8 DD imagerel ??_R0?AVInstance@mu2@@@8 ; mu2::Instance::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3Instance@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@InstanceSinglestage@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@InstanceSinglestage@mu2@@8 DD imagerel ??_R0?AVInstanceSinglestage@mu2@@@8 ; mu2::InstanceSinglestage::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	02H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3InstanceSinglestage@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2InstanceSinglestage@mu2@@8
rdata$r	SEGMENT
??_R2InstanceSinglestage@mu2@@8 DD imagerel ??_R1A@?0A@EA@InstanceSinglestage@mu2@@8 ; mu2::InstanceSinglestage::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@Instance@mu2@@8
	DD	imagerel ??_R1A@?0A@EA@Execution@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3InstanceSinglestage@mu2@@8
rdata$r	SEGMENT
??_R3InstanceSinglestage@mu2@@8 DD 00H			; mu2::InstanceSinglestage::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	03H
	DD	imagerel ??_R2InstanceSinglestage@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVInstanceSinglestage@mu2@@@8
data$rs	SEGMENT
??_R0?AVInstanceSinglestage@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::InstanceSinglestage `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVInstanceSinglestage@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4InstanceSinglestage@mu2@@6B@
rdata$r	SEGMENT
??_R4InstanceSinglestage@mu2@@6B@ DD 01H		; mu2::InstanceSinglestage::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVInstanceSinglestage@mu2@@@8
	DD	imagerel ??_R3InstanceSinglestage@mu2@@8
	DD	imagerel ??_R4InstanceSinglestage@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_7InstanceNormal@mu2@@6B@
CONST	SEGMENT
??_7InstanceNormal@mu2@@6B@ DQ FLAT:??_R4InstanceNormal@mu2@@6B@ ; mu2::InstanceNormal::`vftable'
	DQ	FLAT:??_EInstanceNormal@mu2@@UEAAPEAXI@Z
	DQ	FLAT:?OnDestroyed@Instance@mu2@@UEAAXXZ
	DQ	FLAT:?OnDestroyedFailed@Execution@mu2@@UEAAXXZ
	DQ	FLAT:?OnCreatedSucceed@Instance@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?OnCreatedFailed@Execution@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?OnResGameReady@Instance@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?EnterExecutionPrev@Execution@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?EnterExecution@Instance@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?EnterExecutionPost@Execution@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?ExitExecutionPost@Instance@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?Init@Instance@mu2@@UEAA_NAEAUExecutionCreateInfo@2@@Z
	DQ	FLAT:?Update@InstanceSinglestage@mu2@@UEAAXXZ
	DQ	FLAT:?OnShutdownZone@Execution@mu2@@UEAAXXZ
	DQ	FLAT:?SendReqJoinExecution@Instance@mu2@@UEBA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@@Z
	DQ	FLAT:?IsInstance@Instance@mu2@@UEBA_NXZ
	DQ	FLAT:?IsChannel@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsWorldcross@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsTournamentEnter@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsWorldcrossing@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsSingle@InstanceSinglestage@mu2@@UEBA_NXZ
	DQ	FLAT:?IsGroup@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsNull@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsValid@Instance@mu2@@UEBA?B_NXZ
	DQ	FLAT:?IsRunning@Instance@mu2@@UEBA_NXZ
	DQ	FLAT:?IsJoinable@Execution@mu2@@UEBA_NPEAVEntityPlayer@2@@Z
	DQ	FLAT:?IsDestroyable@Instance@mu2@@UEBA_NXZ
	DQ	FLAT:?GetDamageMeterKey@Instance@mu2@@UEBA?BIXZ
	DQ	FLAT:?ToString@Instance@mu2@@UEBA?BV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ
	DQ	FLAT:?GetRelayServerId@Execution@mu2@@UEBA?BIXZ
	DQ	FLAT:?Fillup@Execution@mu2@@MEBA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAPEAUEwzReqJoinExecution@2@@Z
	DQ	FLAT:?Fillup@Instance@mu2@@UEBAXAEAVJoinZoneContextDest@2@@Z
	DQ	FLAT:?Fillup@Instance@mu2@@UEAAXAEAPEAUEwzReqDestroyExecution@2@@Z
	DQ	FLAT:?Fillup@Instance@mu2@@UEAAXAEAPEAUEwzReqCreateExecution@2@@Z
	DQ	FLAT:?GetGroupKey@Instance@mu2@@UEBA?BIXZ
CONST	ENDS
;	COMDAT ??_7InstanceSinglestage@mu2@@6B@
CONST	SEGMENT
??_7InstanceSinglestage@mu2@@6B@ DQ FLAT:??_R4InstanceSinglestage@mu2@@6B@ ; mu2::InstanceSinglestage::`vftable'
	DQ	FLAT:??_EInstanceSinglestage@mu2@@UEAAPEAXI@Z
	DQ	FLAT:?OnDestroyed@Instance@mu2@@UEAAXXZ
	DQ	FLAT:?OnDestroyedFailed@Execution@mu2@@UEAAXXZ
	DQ	FLAT:?OnCreatedSucceed@Instance@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?OnCreatedFailed@Execution@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?OnResGameReady@Instance@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?EnterExecutionPrev@Execution@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?EnterExecution@Instance@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?EnterExecutionPost@Execution@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?ExitExecutionPost@Instance@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?Init@Instance@mu2@@UEAA_NAEAUExecutionCreateInfo@2@@Z
	DQ	FLAT:?Update@InstanceSinglestage@mu2@@UEAAXXZ
	DQ	FLAT:?OnShutdownZone@Execution@mu2@@UEAAXXZ
	DQ	FLAT:?SendReqJoinExecution@Instance@mu2@@UEBA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@@Z
	DQ	FLAT:?IsInstance@Instance@mu2@@UEBA_NXZ
	DQ	FLAT:?IsChannel@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsWorldcross@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsTournamentEnter@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsWorldcrossing@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsSingle@InstanceSinglestage@mu2@@UEBA_NXZ
	DQ	FLAT:?IsGroup@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsNull@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsValid@Instance@mu2@@UEBA?B_NXZ
	DQ	FLAT:?IsRunning@Instance@mu2@@UEBA_NXZ
	DQ	FLAT:?IsJoinable@Execution@mu2@@UEBA_NPEAVEntityPlayer@2@@Z
	DQ	FLAT:?IsDestroyable@Instance@mu2@@UEBA_NXZ
	DQ	FLAT:?GetDamageMeterKey@Instance@mu2@@UEBA?BIXZ
	DQ	FLAT:?ToString@Instance@mu2@@UEBA?BV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ
	DQ	FLAT:?GetRelayServerId@Execution@mu2@@UEBA?BIXZ
	DQ	FLAT:?Fillup@Execution@mu2@@MEBA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAPEAUEwzReqJoinExecution@2@@Z
	DQ	FLAT:?Fillup@Instance@mu2@@UEBAXAEAVJoinZoneContextDest@2@@Z
	DQ	FLAT:?Fillup@Instance@mu2@@UEAAXAEAPEAUEwzReqDestroyExecution@2@@Z
	DQ	FLAT:?Fillup@Instance@mu2@@UEAAXAEAPEAUEwzReqCreateExecution@2@@Z
	DQ	FLAT:?GetGroupKey@Instance@mu2@@UEBA?BIXZ
CONST	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GInstanceNormal@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1InstanceNormal@mu2@@UEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0InstanceNormal@mu2@@QEAA@PEAVManagerExecutionBase@1@AEAUExecutionCreateInfo@1@@Z DD 011301H
	DD	04213H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GInstanceSinglestage@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?IsValid@Instance@mu2@@UEBA?B_NXZ DD 010901H
	DD	06209H
xdata	ENDS
; Function compile flags: /Odtp
;	COMDAT ??_GInstanceNormal@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GInstanceNormal@mu2@@UEAAPEAXI@Z PROC		; mu2::InstanceNormal::`scalar deleting destructor', COMDAT
$LN5:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00012	e8 00 00 00 00	 call	 ??1InstanceNormal@mu2@@UEAA@XZ ; mu2::InstanceNormal::~InstanceNormal
  00017	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  0001b	83 e0 01	 and	 eax, 1
  0001e	85 c0		 test	 eax, eax
  00020	74 10		 je	 SHORT $LN2@scalar
  00022	ba 70 01 00 00	 mov	 edx, 368		; 00000170H
  00027	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0002c	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00031	90		 npad	 1
$LN2@scalar:
  00032	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00037	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0003b	c3		 ret	 0
??_GInstanceNormal@mu2@@UEAAPEAXI@Z ENDP		; mu2::InstanceNormal::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceNormal.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceSinglestage.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceNormal.cpp
;	COMDAT ??1InstanceNormal@mu2@@UEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1InstanceNormal@mu2@@UEAA@XZ PROC			; mu2::InstanceNormal::~InstanceNormal, COMDAT

; 17   : 	{

$LN9:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7InstanceNormal@mu2@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceSinglestage.h

; 15   : 		virtual~InstanceSinglestage() override {}

  00018	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7InstanceSinglestage@mu2@@6B@
  00024	48 89 08	 mov	 QWORD PTR [rax], rcx
  00027	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0002c	e8 00 00 00 00	 call	 ??1Instance@mu2@@UEAA@XZ ; mu2::Instance::~Instance
  00031	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceNormal.cpp

; 18   : 	}

  00032	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00036	c3		 ret	 0
??1InstanceNormal@mu2@@UEAA@XZ ENDP			; mu2::InstanceNormal::~InstanceNormal
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceNormal.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceSinglestage.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceNormal.cpp
;	COMDAT ??0InstanceNormal@mu2@@QEAA@PEAVManagerExecutionBase@1@AEAUExecutionCreateInfo@1@@Z
_TEXT	SEGMENT
this$ = 48
pOwnerManager$ = 56
cInfo$ = 64
??0InstanceNormal@mu2@@QEAA@PEAVManagerExecutionBase@1@AEAUExecutionCreateInfo@1@@Z PROC ; mu2::InstanceNormal::InstanceNormal, COMDAT

; 13   : 	{

$LN7:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 28	 sub	 rsp, 40			; 00000028H
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceSinglestage.h

; 14   : 		InstanceSinglestage(ManagerExecutionBase* pOwnerManager, ExecutionCreateInfo& cInfo) : Instance(pOwnerManager, cInfo) {}

  00013	4c 8b 44 24 40	 mov	 r8, QWORD PTR cInfo$[rsp]
  00018	48 8b 54 24 38	 mov	 rdx, QWORD PTR pOwnerManager$[rsp]
  0001d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00022	e8 00 00 00 00	 call	 ??0Instance@mu2@@QEAA@PEAVManagerExecutionBase@1@AEAUExecutionCreateInfo@1@@Z ; mu2::Instance::Instance
  00027	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0002c	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7InstanceSinglestage@mu2@@6B@
  00033	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceNormal.cpp

; 13   : 	{

  00036	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003b	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7InstanceNormal@mu2@@6B@
  00042	48 89 08	 mov	 QWORD PTR [rax], rcx

; 14   : 	}

  00045	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004a	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0004e	c3		 ret	 0
??0InstanceNormal@mu2@@QEAA@PEAVManagerExecutionBase@1@AEAUExecutionCreateInfo@1@@Z ENDP ; mu2::InstanceNormal::InstanceNormal
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceSinglestage.h
;	COMDAT ??_GInstanceSinglestage@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GInstanceSinglestage@mu2@@UEAAPEAXI@Z PROC		; mu2::InstanceSinglestage::`scalar deleting destructor', COMDAT
$LN10:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 15   : 		virtual~InstanceSinglestage() override {}

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7InstanceSinglestage@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx
  0001c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00021	e8 00 00 00 00	 call	 ??1Instance@mu2@@UEAA@XZ ; mu2::Instance::~Instance
  00026	90		 npad	 1
  00027	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  0002b	83 e0 01	 and	 eax, 1
  0002e	85 c0		 test	 eax, eax
  00030	74 10		 je	 SHORT $LN2@scalar
  00032	ba 70 01 00 00	 mov	 edx, 368		; 00000170H
  00037	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0003c	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00041	90		 npad	 1
$LN2@scalar:
  00042	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00047	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0004b	c3		 ret	 0
??_GInstanceSinglestage@mu2@@UEAAPEAXI@Z ENDP		; mu2::InstanceSinglestage::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceSinglestage.h
;	COMDAT ?IsSingle@InstanceSinglestage@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsSingle@InstanceSinglestage@mu2@@UEBA_NXZ PROC	; mu2::InstanceSinglestage::IsSingle, COMDAT

; 16   : 		virtual Bool					IsSingle() const { return true; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	b0 01		 mov	 al, 1
  00007	c3		 ret	 0
?IsSingle@InstanceSinglestage@mu2@@UEBA_NXZ ENDP	; mu2::InstanceSinglestage::IsSingle
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Instance.h
;	COMDAT ?GetGroupKey@Instance@mu2@@UEBA?BIXZ
_TEXT	SEGMENT
this$ = 8
?GetGroupKey@Instance@mu2@@UEBA?BIXZ PROC		; mu2::Instance::GetGroupKey, COMDAT

; 83   : 		virtual const InstanceGroupKey	GetGroupKey() const { return 0; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	33 c0		 xor	 eax, eax
  00007	c3		 ret	 0
?GetGroupKey@Instance@mu2@@UEBA?BIXZ ENDP		; mu2::Instance::GetGroupKey
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Instance.h
;	COMDAT ?IsValid@Instance@mu2@@UEBA?B_NXZ
_TEXT	SEGMENT
tv74 = 32
this$ = 64
?IsValid@Instance@mu2@@UEBA?B_NXZ PROC			; mu2::Instance::IsValid, COMDAT

; 82   : 		virtual const Bool				IsValid() const override { return m_instanceId > 0 && __super::IsValid(); }

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H
  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	83 b8 84 00 00
	00 00		 cmp	 DWORD PTR [rax+132], 0
  00015	76 18		 jbe	 SHORT $LN3@IsValid
  00017	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0001c	e8 00 00 00 00	 call	 ?IsValid@Execution@mu2@@UEBA?B_NXZ ; mu2::Execution::IsValid
  00021	0f b6 c0	 movzx	 eax, al
  00024	85 c0		 test	 eax, eax
  00026	74 07		 je	 SHORT $LN3@IsValid
  00028	c6 44 24 20 01	 mov	 BYTE PTR tv74[rsp], 1
  0002d	eb 05		 jmp	 SHORT $LN4@IsValid
$LN3@IsValid:
  0002f	c6 44 24 20 00	 mov	 BYTE PTR tv74[rsp], 0
$LN4@IsValid:
  00034	0f b6 44 24 20	 movzx	 eax, BYTE PTR tv74[rsp]
  00039	48 83 c4 38	 add	 rsp, 56			; 00000038H
  0003d	c3		 ret	 0
?IsValid@Instance@mu2@@UEBA?B_NXZ ENDP			; mu2::Instance::IsValid
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Instance.h
;	COMDAT ?IsInstance@Instance@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsInstance@Instance@mu2@@UEBA_NXZ PROC			; mu2::Instance::IsInstance, COMDAT

; 80   : 		virtual Bool					IsInstance() const final { return true; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	b0 01		 mov	 al, 1
  00007	c3		 ret	 0
?IsInstance@Instance@mu2@@UEBA_NXZ ENDP			; mu2::Instance::IsInstance
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?IsNull@Execution@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsNull@Execution@mu2@@UEBA_NXZ PROC			; mu2::Execution::IsNull, COMDAT

; 86   : 		virtual Bool					IsNull() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsNull@Execution@mu2@@UEBA_NXZ ENDP			; mu2::Execution::IsNull
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?IsGroup@Execution@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsGroup@Execution@mu2@@UEBA_NXZ PROC			; mu2::Execution::IsGroup, COMDAT

; 85   : 		virtual Bool					IsGroup() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsGroup@Execution@mu2@@UEBA_NXZ ENDP			; mu2::Execution::IsGroup
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?IsWorldcrossing@Execution@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsWorldcrossing@Execution@mu2@@UEBA_NXZ PROC		; mu2::Execution::IsWorldcrossing, COMDAT

; 83   : 		virtual Bool					IsWorldcrossing() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsWorldcrossing@Execution@mu2@@UEBA_NXZ ENDP		; mu2::Execution::IsWorldcrossing
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?IsTournamentEnter@Execution@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsTournamentEnter@Execution@mu2@@UEBA_NXZ PROC		; mu2::Execution::IsTournamentEnter, COMDAT

; 82   : 		virtual Bool					IsTournamentEnter() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsTournamentEnter@Execution@mu2@@UEBA_NXZ ENDP		; mu2::Execution::IsTournamentEnter
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?IsWorldcross@Execution@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsWorldcross@Execution@mu2@@UEBA_NXZ PROC		; mu2::Execution::IsWorldcross, COMDAT

; 81   : 		virtual Bool					IsWorldcross() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsWorldcross@Execution@mu2@@UEBA_NXZ ENDP		; mu2::Execution::IsWorldcross
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?IsChannel@Execution@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsChannel@Execution@mu2@@UEBA_NXZ PROC			; mu2::Execution::IsChannel, COMDAT

; 80   : 		virtual Bool					IsChannel() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsChannel@Execution@mu2@@UEBA_NXZ ENDP			; mu2::Execution::IsChannel
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceNormal.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceNormal.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
