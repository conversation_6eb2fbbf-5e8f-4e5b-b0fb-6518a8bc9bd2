﻿#include "stdafx.h"
#include <Mu2Common/CommandString.h>
#include <Backend/Zone/ZoneServer.h>
#include <Backend/Zone/System/SpawnSystem.h>
#include <Backend/Zone/Action/ActionAchievement.h>
#include <Backend/Zone/Action/ActionNpcDamage.h>
#include <Backend/Zone/Action/ActionDroppedItem.h>
#include <Backend/Zone/Action/ActionDynamicTagVolume.h>
#include <Backend/Zone/Action/ActionMissionDungeon.h>
#include <Backend/Zone/Action/ActionMissionHero.h>
#include <Backend/Zone/Action/ActionNpc.h>
#include <Backend/Zone/Action/ActionNpcAggro.h>
#include <Backend/Zone/Action/ActionNpcScript.h>
#include <Backend/Zone/Action/ActionParty.h>
#include <Backend/Zone/Action/ActionPlayer.h>
#include <Backend/Zone/Action/ActionPlayerInventory.h>
#include <Backend/Zone/Action/ActionQuest.h>
#include <Backend/Zone/Action/ActionPlayerMailBox.h>
#include <Backend/Zone/Action/ActionPortal.h>
#include <Backend/Zone/Action/ActionUseItem.h>
#include <Backend/Zone/Action/ActionPlayer/ActionSkillManage.h>
#include <Backend/Zone/Action/ActionSkillRune.h>
#include <Backend/Zone/Action/ActionPlayerMaking.h>
#include <Backend/Zone/Action/ActionNpc/ActionNpcOperate.h>
#include <Backend/Zone/Action/ActionDurability.h>
#include <Backend/Zone/Action/ActionNpcSkill.h>
#include <Backend/Zone/Action/ActionSuppliedSkillEachPortal.h>
#include <Backend/Zone/Action/ActionPassivity.h>
#include <Backend/Zone/Action/ActionTutorial.h>
#include <Backend/Zone/Action/ActionEloPoint.h>
#include <Backend/Zone/Action/ActionBuff.h>
#include <Backend/Zone/Action/ActionNpcBlackMarket.h>
#include <Backend/Zone/Action/ActionPlayerBlackMarket.h>
#include <Backend/Zone/Action/ActionSkillChanger.h>
#include <Backend/Zone/Action/ActionPlayer/ActionSkillMastery.h>
#ifndef __Reincarnation__jason_180628__
#include <Backend/Zone/Action/ActionPlayer/ActionSoulSkillManage.h>
#endif
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerCombatPower.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerCognition.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerSurvey.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerColosseum.h>
#include <Backend/Zone/Action/ActionPlayerStorage.h>
#include <Backend/Zone/Action/ActionDailyMission.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerExchange.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerRevive.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerMembership.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerEventInventory.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerMissionMapTier.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerEvent.h>
#include <Backend/Zone/Action/ActionArtifact.h>
#include <Backend/Zone/Action/ActionAutoHackChecker.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerKnightageStorage.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerKnightage.h>
#include <Backend/Zone/State/StateNpcPeace.h>
#include <Backend/Zone/World/ColosseumProcessor.h>
#include <Backend/Zone/System/DropSystem.h>
#include <Backend/Zone/System/PortalSystem.h>
#include <Backend/Zone/System/GMCommandSystem.h>
#include <Backend/Zone/System/LogicEffectSystem.h>
#include <Backend/Zone/System/QuestSystem.h>
#include <Backend/Zone/World/MazeProcessor.h>
#include <Backend/Zone/World/MissionMap/PVPChaosCastleProcessor.h>
#include <Backend/Zone/World/OhrdorSewerageProcessor.h>
#include <Backend/Zone/World/MazeInDarknessProcessor.h>
#include <Backend/Zone/Transaction/ItemTransaction/ItemBaseTransaction.h>
#include <Backend/Zone/Transaction/ItemTransaction/ItemKnightageTransaction.h>
#include <Backend/Zone/System/revivesystem.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerSeason.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerGuideSystem.h>
#include <Backend/Zone/ExecutionManager.h>
#include <Backend/Zone/NpcDebugger.h>
#include <Backend/Zone/View/ViewPosition.h>
#include <Backend/Zone/Element/Achievement/GuideTrigger.h>
#include <Backend/Zone/State/StatePlayerBase.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerTalisman.h>
#include <Backend/Zone/Element/Item/InvenSlotable.h>
#include <Backend/Zone/Element/Item/InvenPrivateStorage.h>
#include <Backend/Zone/Element/Item/InvenAccountStorage.h>
#include <Backend/Zone/Element/Item/InvenEquip.h>
#include <Backend/Zone/Element/Item/ItemFinder.h>

#include <Backend/Zone/Action/ActionPlayer/ActionPlayerNoticePopUp.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerEventBuff.h>
#ifdef __Hotfix_SkillQuickSlotChange_by_kangms_180912
#include <Backend/Zone/Action/ActionPlayer/ActionQuickSlot.h>
#endif//__Hotfix_SkillQuickSlotChange_by_kangms_180912
#ifdef __Patch_Tower_of_dawn_eunseok_2018_11_05
#include <Backend/Zone/World/MissionMap/TowerOfDawnProcessor.h>
#endif //__Patch_Tower_of_dawn_eunseok_2018_11_05

#ifdef __Patch_WebOfGod_by_jason_20181213
#include <Backend/Zone/World/PVEMissionMap/WebOfGodProcessor.h>
#endif

#ifdef __Patch_SetItem_Renewal_by_kangms_2019_4_10
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerSetCard.h>
#endif//__Patch_SetItem_Renewal_by_kangms_2019_4_10

namespace mu2
{

//---------------------------------------------------------------------------------------------------------------
// Type defines
//---------------------------------------------------------------------------------------------------------------

//[AccountName][SecurityLevel]
typedef std::map< std::wstring, UInt32 > GMAccountSecurityMap;

typedef const Bool (GMCommandSystem::*GMCommandProcessor)( EntityPlayer*, const CommandString& );
struct GMCommandProcessInfo
{	
	GMCommandProcessInfo()
		: processorFunc( nullptr )
		, securityLevel(0)
		, argCount(0)
		, category("@未分类")
		, description("nothing")
	{ /*Nothing!*/ }

	GMCommandProcessor processorFunc;
	UInt32			   securityLevel;
	UInt32			   argCount;
	std::string			category;
	std::string			description;
};

typedef std::map< std::wstring, GMCommandProcessInfo > GMCommandProcessorMap;


//---------------------------------------------------------------------------------------------------------------
// Pimpl
//---------------------------------------------------------------------------------------------------------------

struct GMCommandSystem::Impl : public GeneralAlloc
{
	void RegisterCommand(GMCommandProcessor func, std::wstring command, UInt32 argCount, UInt32 securityLevel, std::string category, std::string description)
	{
		GMCommandProcessInfo info;
		info.securityLevel = 0;
		info.argCount = argCount;
		info.processorFunc = func;
		info.category = category;
		info.description = description;
		gmCommandProcessorMap.insert(GMCommandProcessorMap::value_type(command, info));
	}

	GMCommandProcessorMap	gmCommandProcessorMap;
};

GMCommandSystem::GMCommandSystem()
	: m_impl( NEW Impl )
{	
}

GMCommandSystem::~GMCommandSystem()
{
}

Bool GMCommandSystem::Init(void* param)
{
	UNREFERENCED_PARAMETER(param);

	setupGMCommandProcessors();
	MakeXML();

	return true;
}

Bool GMCommandSystem::UnInit()
{
	return true;
}

const Bool GMCommandSystem::LoadGMAuthConfigure()
{
	return true;
}

const Bool GMCommandSystem::Execute( EntityPlayer* player, const std::wstring& command )
{
	VERIFY_RETURN(player && player->IsValid(), false);
	VALID_RETURN(command.size(), false);

	MU2_WARN_LOG(LogCategory::GMCOMMAND, L"[USE GMCOMMAND][%d][%s]", player->GetCharId(), command.c_str());

	//auto view = player->GetView<ViewPlayer>();
	VALID_RETURN(player->CheckAccountGrade(static_cast<AccountGrade::Enum>(AccountGrade::DEV | AccountGrade::GM)), false);

	/*UInt32 securityLevel = 0xffff;*/

	std::vector< std::wstring > commandArgs;
	mu2::StringUtil::TokenizeStr(command, commandArgs, std::wstring(L" "));
	VALID_RETURN(commandArgs.size(), false);

	const std::wstring& commandId = commandArgs[0];
	GMCommandProcessorMap::iterator pos = m_impl->gmCommandProcessorMap.find(commandId);
	VALID_RETURN(m_impl->gmCommandProcessorMap.end() != pos, false);

	const GMCommandProcessInfo& gmCmdProcessInfo = (*pos).second;
	//if( securityLevel < gmCmdProcessInfo.securityLevel )
	//{
	////! TODO : Disconnect this session
	//	return false;
	//}
	//!

	VALID_RETURN(gmCmdProcessInfo.argCount <= commandArgs.size(), false);

	CommandString commandString(commandArgs);
	commandString.Forward();
	return (this->*(*pos).second.processorFunc)(player, commandString);
}



//---------------------------------------------------------------------------------------------------------------
// Utility operations
//---------------------------------------------------------------------------------------------------------------

void GMCommandSystem::setupGMCommandProcessors()
{
	GMCommandProcessInfo cmdInfo;
	cmdInfo.securityLevel = 0; //! 

	cmdInfo.processorFunc = &GMCommandSystem::onCommandCreateItem;
	cmdInfo.argCount = 3;
	cmdInfo.category = "아이템";
	cmdInfo.description = "[아이템 생성, 최대 50개까지][3][아이템 INDEX][강화수치][개수]";
	m_impl->gmCommandProcessorMap[L"create"] = cmdInfo;
	m_impl->gmCommandProcessorMap[L"아이템"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onCommandCreateOptionItem;
	cmdInfo.argCount = 4;
	cmdInfo.category = "아이템";
	cmdInfo.description = "[아이템 생성][4][아이템 INDEX][옵션타입][계산 타입 POINT/PERCENT][옵션수치]";
	m_impl->gmCommandProcessorMap[L"createoption"] = cmdInfo;
	m_impl->gmCommandProcessorMap[L"아이템"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onCommandSummon;
	cmdInfo.argCount = 2;
	cmdInfo.category = "아이템";
	cmdInfo.description = "[][2]";
	m_impl->gmCommandProcessorMap[L"summon"] = cmdInfo;
	m_impl->gmCommandProcessorMap[L"드롭"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onCommandChangeMoveSpeed;
	cmdInfo.argCount = 2;
	cmdInfo.category = "캐릭터";
	cmdInfo.description = "[캐릭터 이동속도 변경][1][N:속도]";
	m_impl->gmCommandProcessorMap[L"speed"] = cmdInfo;
	m_impl->gmCommandProcessorMap[L"이속"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onCommandChangeMoveSpeedHack;
	cmdInfo.argCount = 1;
	cmdInfo.category = "캐릭터";
	cmdInfo.description = "[캐릭터 이동속도 핵테스트][1][N:속도]";
	m_impl->gmCommandProcessorMap[L"hackspeed"] = cmdInfo;
	m_impl->gmCommandProcessorMap[L"핵이속"] = cmdInfo;

	//cmdInfo.processorFunc = &GMCommandSystem::onCommandChangeChannel;
	//cmdInfo.argCount = 2;
	//cmdInfo.category = "@미분류";
	//cmdInfo.description = "[][2]";
	//m_impl->gmCommandProcessorMap[L"chch"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onCommandChangeHealth;
	cmdInfo.argCount = 2;
	cmdInfo.category = "캐릭터";
	cmdInfo.description = "[현재 HP 변경][1][현재 HP값]";
	m_impl->gmCommandProcessorMap[L"hp"] = cmdInfo;
	m_impl->gmCommandProcessorMap[L"체력"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onCommandChangeMana;
	cmdInfo.argCount = 2;
	cmdInfo.category = "캐릭터";
	cmdInfo.description = "[현재 MP 변경][1][현재 MP값]";
	m_impl->gmCommandProcessorMap[L"mp"] = cmdInfo;
	m_impl->gmCommandProcessorMap[L"마나"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onCommandChangeLevel;
	cmdInfo.argCount = 2;
	cmdInfo.category = "캐릭터";
	cmdInfo.description = "[캐릭터 레벨설정, 경험치 초기화X][1][N:레벨]";
	m_impl->gmCommandProcessorMap[L"level"] = cmdInfo;
	m_impl->gmCommandProcessorMap[L"레벨"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onCommandChangeForceLevel;
	cmdInfo.argCount = 2;
	cmdInfo.category = "캐릭터";
	cmdInfo.description = "[캐릭터 레벨설정, 경험치 초기화X][1][N:레벨]";
	m_impl->gmCommandProcessorMap[L"forcelevel"] = cmdInfo;
	m_impl->gmCommandProcessorMap[L"강제레벨"] = cmdInfo;
	

	cmdInfo.processorFunc = &GMCommandSystem::onCommandChangeSoulLevel;
	cmdInfo.argCount = 2;
	cmdInfo.category = "캐릭터";
	cmdInfo.description = "[캐릭터 영혼 레벨설정, 경험치 초기화X][1][N:레벨]";
	m_impl->gmCommandProcessorMap[L"soullevel"] = cmdInfo;
	m_impl->gmCommandProcessorMap[L"영혼레벨"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onCommandAddExp;
	cmdInfo.argCount = 2;
	cmdInfo.category = "캐릭터";
	cmdInfo.description = "[플레이어 경험치 획득][1][N:경험치]";
	m_impl->gmCommandProcessorMap[L"addexp"] = cmdInfo;
	m_impl->gmCommandProcessorMap[L"경험치"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onCommandAddMoney;
	cmdInfo.argCount = 2;
	cmdInfo.category = "아이템";
	cmdInfo.description = "[인벤토리의 젠을 지정한 수치만큼 설정한다.][1][N:젠]";
	m_impl->gmCommandProcessorMap[L"addmoney"] = cmdInfo;
	m_impl->gmCommandProcessorMap[L"머니"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onCommandSubMoney;
	cmdInfo.argCount = 2;
	cmdInfo.category = "@미분류";
	cmdInfo.description = "[][2]";
	m_impl->gmCommandProcessorMap[L"submoney"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onCommandDead;
	cmdInfo.argCount = 1;
	cmdInfo.category = "캐릭터";
	cmdInfo.description = "[][1]";
	m_impl->gmCommandProcessorMap[L"dead"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onCommandCureDeathPenalty;
	cmdInfo.argCount = 0;
	cmdInfo.category = "캐릭터";
	cmdInfo.description = "[사망후유증을 치료한다.][0]";
	m_impl->gmCommandProcessorMap[L"cure"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onCommandChatNormal;
	cmdInfo.argCount = 2;
	cmdInfo.category = "@미분류";
	cmdInfo.description = "[][2]";
	m_impl->gmCommandProcessorMap[L"chatnormal"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onCommandTransparent;
	cmdInfo.argCount = 2;
	cmdInfo.category = "@미분류";
	cmdInfo.description = "[][2]";
	m_impl->gmCommandProcessorMap[L"transparent"] = cmdInfo;
	m_impl->gmCommandProcessorMap[L"투명"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onCommandNpcSummonFollow;
	cmdInfo.argCount = 1;
	cmdInfo.category = "몬스터";
	cmdInfo.description = "[][1]";
	m_impl->gmCommandProcessorMap[L"npcsummonfollow"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onCommandMyPos;
	cmdInfo.argCount = 1;
	cmdInfo.category = "@미분류";
	cmdInfo.description = "[][1]";
	m_impl->gmCommandProcessorMap[L"mypos"] = cmdInfo;
	m_impl->gmCommandProcessorMap[L"위치"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onCommandWarp;
	cmdInfo.argCount = 0;
	cmdInfo.category = "@미분류";
	cmdInfo.description = "[][0]";
	m_impl->gmCommandProcessorMap[L"warp"] = cmdInfo;
	m_impl->gmCommandProcessorMap[L"워프"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onCommandClearBag;
	cmdInfo.argCount = 1;
	cmdInfo.category = "아이템";
	cmdInfo.description = "[인벤토리 초기화][0]";
	m_impl->gmCommandProcessorMap[L"clearbag"] = cmdInfo;

	// blueadel npc 디버그용 	
	cmdInfo.processorFunc = &GMCommandSystem::onCommandNpcTracking;
	cmdInfo.argCount = 1;
	cmdInfo.category = "@미분류";
	cmdInfo.description = "[][1]";
	m_impl->gmCommandProcessorMap[L"npctracking"] = cmdInfo;

	//cmdInfo.processorFunc = &GMCommandSystem::onCommandItemMagicSheetDestory;
	//cmdInfo.argCount = 2;
	//cmdInfo.category = "@미분류";
	//cmdInfo.description = "[][2]";
	//m_impl->gmCommandProcessorMap[L"magic"] = cmdInfo;
	//m_impl->gmCommandProcessorMap[L"마부"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onQuestAccept;
	cmdInfo.argCount = 1;
	cmdInfo.category = "퀘스트";
	cmdInfo.description = "[퀘스트를 수락한다][1][N:퀘스트 인덱스]";
	m_impl->gmCommandProcessorMap[L"acceptquest"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onQuestFinish;
	cmdInfo.argCount = 1;
	cmdInfo.category = "퀘스트";
	cmdInfo.description = "[퀘스트를 완료한다][1][N:퀘스트 인덱스]";
	m_impl->gmCommandProcessorMap[L"finishquest"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onQuestFinishAll;
	cmdInfo.argCount = 0;
	cmdInfo.category = "퀘스트";
	cmdInfo.description = "[현재 레벨까지 모든 퀘스트를 완료한다][0][]";
	m_impl->gmCommandProcessorMap[L"finishquestall"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onGiveupquest;
	cmdInfo.argCount = 1;
	cmdInfo.category = "퀘스트";
	cmdInfo.description = "[퀘스트를 포기한다][1][N:퀘스트 인덱스]";
	m_impl->gmCommandProcessorMap[L"giveupquest"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onQuestRenewal;
	cmdInfo.argCount = 1;
	cmdInfo.category = "퀘스트";
	cmdInfo.description = "[반복퀘스트 리뉴얼]";
	m_impl->gmCommandProcessorMap[L"renewalquest"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onRewardQuest;
	cmdInfo.argCount = 2;
	cmdInfo.category = "퀘스트";
	cmdInfo.description = "[퀘스트 보상을 받는다][2][N:퀘스트 인덱스 N:선택아이템인덱스]";
	m_impl->gmCommandProcessorMap[L"rewardquest"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onMeetNpc;
	cmdInfo.argCount = 1;
	cmdInfo.category = "퀘스트";
	cmdInfo.description = "[특정NPC를 만난다][1][N:NPC인덱스]";
	m_impl->gmCommandProcessorMap[L"meetnpc"] = cmdInfo;

	/*cmdInfo.processorFunc = &GMCommandSystem::onQuestShare;
	cmdInfo.argCount = 2;
	cmdInfo.category = "퀘스트";
	cmdInfo.description = "[퀘스트를 공유 한다.][2][N:퀘스트인덱스 N:entityId]";
	m_impl->gmCommandProcessorMap[L"sharequest"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onQuestShareResult;
	cmdInfo.argCount = 3;
	cmdInfo.category = "퀘스트";
	cmdInfo.description = "[퀘스트공유요청에대한 응답한다.][3][N:퀘스트인덱스 N:entityId N:result]";
	m_impl->gmCommandProcessorMap[L"sharequestresult"] = cmdInfo;*/

	cmdInfo.processorFunc = &GMCommandSystem::onQuestVolume;
	cmdInfo.argCount = 2;
	cmdInfo.category = "퀘스트";
	cmdInfo.description = "[퀘스트 볼륨에 입장한다][1][N:Volume Index]";
	m_impl->gmCommandProcessorMap[L"volumeinquest"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onQuestUseItem;
	cmdInfo.argCount = 1;
	cmdInfo.category = "퀘스트";
	cmdInfo.description = "[퀘스트 아이템 사용을 확인한다][1][N:item index]";
	m_impl->gmCommandProcessorMap[L"useitemquest"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onQuestRequest;
	cmdInfo.argCount = 2;
	cmdInfo.category = "퀘스트";
	cmdInfo.description = "[특정퀘스트에 대한 요청을 한다.][2][N:퀘스트 인덱스 N:요청타입(0-요청,1-수락,3-실패,4-거부,6-UI표시,7-UI제거)]";
	m_impl->gmCommandProcessorMap[L"requestquest"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onQuestRefuse;
	cmdInfo.argCount = 1;
	cmdInfo.category = "퀘스트";
	cmdInfo.description = "[특정 퀘스트를 거부한다.][1][N:퀘스트인덱스]";
	m_impl->gmCommandProcessorMap[L"refusequest"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onQuestFail;
	cmdInfo.argCount = 1;
	cmdInfo.category = "퀘스트";
	cmdInfo.description = "[퀘스트][1]";
	m_impl->gmCommandProcessorMap[L"failquest"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onQuestReset;
	cmdInfo.argCount = 1;
	cmdInfo.category = "퀘스트";
	cmdInfo.description = "[완료된 퀘스트를 초기화 한다.][1][N:퀘스트인덱스(0-완료된 모든 퀘스트를 초기화)]";
	m_impl->gmCommandProcessorMap[L"resetquest"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onQuestVirtualTestKillMonster;
	cmdInfo.argCount = 1;
	cmdInfo.category = "퀘스트";
	cmdInfo.description = "[][1]";
	m_impl->gmCommandProcessorMap[L"qkill"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onQuestVirtualTestTimeout;
	cmdInfo.argCount = 1;
	cmdInfo.category = "퀘스트";
	cmdInfo.description = "[][1]";
	m_impl->gmCommandProcessorMap[L"qtimeout"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onRegistFootHold;
	cmdInfo.argCount = 1;
	cmdInfo.category = "@미분류";
	cmdInfo.description = "[][1]";
	m_impl->gmCommandProcessorMap[L"regist_foothold"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onRevive;
	cmdInfo.argCount = 1;
	cmdInfo.category = "@미분류";
	cmdInfo.description = "[][1]";
	m_impl->gmCommandProcessorMap[L"revive"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onUseItem;
	cmdInfo.argCount = 1;
	cmdInfo.category = "아이템";
	cmdInfo.description = "[사용 아이템][2][N:대상ID][N:인벤토리슬롯번호(0~)]";
	m_impl->gmCommandProcessorMap[L"useitem"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onCastingCancel;
	cmdInfo.argCount = 1;
	cmdInfo.category = "@미분류";
	cmdInfo.description = "[][1]";
	m_impl->gmCommandProcessorMap[L"cc"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onReloadCommonConfig;
	cmdInfo.argCount = 0;
	cmdInfo.category = "@미분류";
	cmdInfo.description = "[][0]";
	m_impl->gmCommandProcessorMap[L"reloadcfg"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onOpenPortal;
	cmdInfo.argCount = 1;
	cmdInfo.category = "@미분류";
	cmdInfo.description = "[][1]";
	m_impl->gmCommandProcessorMap[L"portal"] = cmdInfo;


	cmdInfo.processorFunc = &GMCommandSystem::onMakingBigHit;
	cmdInfo.argCount = 1;
	cmdInfo.category = "제작";
	cmdInfo.description = "[제작 시 대성공 확률을 최대 수치인 30%로 고정][1][on/off]";
	m_impl->gmCommandProcessorMap[L"bighit"] = cmdInfo;
	m_impl->gmCommandProcessorMap[L"대성공"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onMaking;
	cmdInfo.argCount = 2;
	cmdInfo.category = "@미분류";
	cmdInfo.description = "[][1]";
	m_impl->gmCommandProcessorMap[L"making"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onWorldOpen;
	cmdInfo.argCount = 1;
	cmdInfo.category = "@미분류";
	cmdInfo.description = "[][1]";
	m_impl->gmCommandProcessorMap[L"worldopen"] = cmdInfo;
	m_impl->gmCommandProcessorMap[L"월드오픈"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onWorldClose;
	cmdInfo.argCount = 1;
	cmdInfo.category = "@미분류";
	cmdInfo.description = "[][1]";
	m_impl->gmCommandProcessorMap[L"worldclose"] = cmdInfo;
	m_impl->gmCommandProcessorMap[L"월드닫기"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onSpeedHack;
	cmdInfo.argCount = 1;
	cmdInfo.category = "스피드핵 체크";
	cmdInfo.description = "[][1]";
	m_impl->gmCommandProcessorMap[L"speedhack"] = cmdInfo;
	m_impl->gmCommandProcessorMap[L"스피드핵"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onSpeedHackDealy;
	cmdInfo.argCount = 2;
	cmdInfo.category = "스피드딜레이";
	cmdInfo.description = "[][2]";
	m_impl->gmCommandProcessorMap[L"delay"] = cmdInfo;
	m_impl->gmCommandProcessorMap[L"이동패킷딜레이"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onStopPacket;
	cmdInfo.argCount = 1;
	cmdInfo.category = "스피드딜레이";
	cmdInfo.description = "[][1]";
	m_impl->gmCommandProcessorMap[L"stoppacket"] = cmdInfo;
	m_impl->gmCommandProcessorMap[L"이동정지패킷"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onOhrdorSewerageTimeout;
	cmdInfo.argCount = 0;
	cmdInfo.category = "오르도르 하수구";
	cmdInfo.description = "오르도르 하수구 타임아웃";
	m_impl->gmCommandProcessorMap[L"timeout"] = cmdInfo;
	m_impl->gmCommandProcessorMap[L"타임아웃"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onOhrdorSewerageInvasionCount;
	cmdInfo.argCount = 1;
	cmdInfo.category = "오르도르 하수구";
	cmdInfo.description = "오르도르 하수구 침입 카운트 변경";
	m_impl->gmCommandProcessorMap[L"invasioncount"] = cmdInfo;
	m_impl->gmCommandProcessorMap[L"침입카운트"] = cmdInfo;


	cmdInfo.processorFunc = &GMCommandSystem::onMissionMapReward;
	cmdInfo.argCount = 0;
	cmdInfo.category = "오르도르 하수구";
	cmdInfo.description = "오르도르 하수구 보상받고 퇴장";
	m_impl->gmCommandProcessorMap[L"reward"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onGotoGate;
	cmdInfo.argCount = 0;
	cmdInfo.category = "미로";
	cmdInfo.description = "미로 출구오브젝트로 이동";
	m_impl->gmCommandProcessorMap[L"gotogate"] = cmdInfo;

	cmdInfo.processorFunc = &GMCommandSystem::onGuideArrow;
	cmdInfo.argCount = 0;
	cmdInfo.category = "미로";
	cmdInfo.description = "출구 화살표 표시";
	m_impl->gmCommandProcessorMap[L"guidearrow"] = cmdInfo;

	m_impl->RegisterCommand(&GMCommandSystem::onMissionmap, L"mmc", 3, 0, "미션맵", "[미션맵 신청][3][(매치타입)N: 0:없음, 11:카오스캐슬, 12:정령의재단, 13:데스매치][(파티여부)N: 0:개인신청, 1:파티신청][(난입여부)N: 0:난입불가, 1:난입허용]");
	m_impl->RegisterCommand(&GMCommandSystem::onShowEloPoint, L"showelo", 1, 0, "미션맵", "[ELO점수확인][1][(미션맵명)S: chaos, altar, death20, death40 ]");
	m_impl->RegisterCommand(&GMCommandSystem::onSetEloPoint, L"setelo", 3, 0, "미션맵", "[ELO점수변경][3][(미션맵명)S: chaos, altar, death20, death40][(Elo점수)N][(Elo경기수)N");
	m_impl->RegisterCommand(&GMCommandSystem::onBeginWarp, L"bw", 0, 0, "@미분류", "BeginWarp");
	m_impl->RegisterCommand(&GMCommandSystem::onPlaceBoxObstacle, L"place_box_obstacle", 0, 0, "위치", "[Box 충돌체 생성][0]");
	m_impl->RegisterCommand(&GMCommandSystem::onRemoveBoxObstacle, L"remove_box_obstacle", 1, 0, "위치", "[Box 충돌체 파괴][1][0:하나씩 삭제, 1:모두 삭제]");
	m_impl->RegisterCommand(&GMCommandSystem::onPlaceTagVolume, L"place_tag_volume", 1, 0, "위치", "[TagVolume 소환][1][World별 MU2DynamicTagVolume Index]");
	m_impl->RegisterCommand(&GMCommandSystem::onRemoveTagVolume, L"remove_tag_volume", 1, 0, "위치", "[TagVolume 제거][1][World별 MU2DynamicTagVolume Index]");
	m_impl->RegisterCommand(&GMCommandSystem::onGetTagValue, L"get_tag_value", 0, 0, "위치", "[현재 위치의 Tag 값][0]");
	m_impl->RegisterCommand(&GMCommandSystem::onSyncMailList, L"sync", 0, 0, "우편함", "[우편함 강제 동기화][0]");
	m_impl->RegisterCommand(&GMCommandSystem::onOpenMail, L"open", 1, 0, "우편함", "[우편 조회][1][우편 Index]");
	m_impl->RegisterCommand(&GMCommandSystem::onGetMailItem, L"getitem", 1, 0, "우편함", "[우편 첨부 아이템 획득][1][우편 Index]");
	m_impl->RegisterCommand(&GMCommandSystem::onReturnMail, L"return", 1, 0, "우편함", "[우편 반송][1][우편 Index]");
	m_impl->RegisterCommand(&GMCommandSystem::onGetMailMoney, L"getmoney", 1, 0, "우편함", "[우편 첨부 금액 획득][1][우편 Index]");
	m_impl->RegisterCommand(&GMCommandSystem::onDeleteMail, L"delmail", 1, 0, "우편함", "[우편 삭제][1][우편 Index]");
	m_impl->RegisterCommand(&GMCommandSystem::onGetMailList, L"maillist", 1, 0, "우편함", "[우편 목록 전송(개발치트)][1][우편함 타입(받은편지함:0 / 보낸편지함:1 / 스팸함:2)]");
	m_impl->RegisterCommand(&GMCommandSystem::onSystemMessage, L"sysmsg", 1, 0, "@미분류", "[본인에게 시스템 메시지 전송][1][메시지 내용]");
	m_impl->RegisterCommand(&GMCommandSystem::onSendMail, L"send", 1, 0, "@미분류", "[대량 메일 전송][1~5][받는사람 캐릭터명][메일 갯수][제목][내용][시스템 메일 여부(시스템:1 / 일반:0)]");
	m_impl->RegisterCommand(&GMCommandSystem::onChangeMailDate, L"changedate", 3, 0, "우편함", "[(메일)현재 날짜 변경(해당 날짜만큼 현재 날짜에 더함)][Day][Hour][Min]");
	m_impl->RegisterCommand(&GMCommandSystem::onReqSuggestTrade, L"sugggest_t", 1, 0, "거래", "[거래 신청][1][거래받을 캐릭터명]");
	m_impl->RegisterCommand(&GMCommandSystem::onReqAnswerTrade, L"answer_t", 1, 0, "거래", "[거래에 응답][1][거래신청 캐릭터 Id]");
	m_impl->RegisterCommand(&GMCommandSystem::onReqChangeTradeInfo, L"change_t", 3, 0, "거래", "[거래 내용 변경(개발치트)][3][젠 수량][인벤토리 Index][슬롯 Index]");
	m_impl->RegisterCommand(&GMCommandSystem::onReqCancelTrade, L"cancel_t", 0, 0, "거래", "[거래 취소][0]");
	m_impl->RegisterCommand(&GMCommandSystem::onReqReadyTrade, L"ready_t", 0, 0, "거래", "[거래 준비 완료][0]");
	m_impl->RegisterCommand(&GMCommandSystem::onReqStartTrade, L"start_t", 0, 0, "거래", "[거래 최종 완료][0]");
	m_impl->RegisterCommand(&GMCommandSystem::onItemExtract, L"extract", 0, 0, "@미분류", "[설명 추가해주세요]");
	m_impl->RegisterCommand(&GMCommandSystem::onBuyStorageBag, L"buybag", 3, 0, "창고", "[창고 가방 추가][3][기본 가방 슬롯 수][확장 가능한 최대 슬롯 수][창고 사용 기간(일단위)][타입(기간제:0 / 무제한:1]");
	m_impl->RegisterCommand(&GMCommandSystem::onExpandSlot, L"addslot", 1, 0, "창고", "[창고 슬롯 확장][1][확장할 창고 Index(0:첫번째 창고, 4:다섯번째 창고)]");
	m_impl->RegisterCommand(&GMCommandSystem::onExpandPeriod, L"addperiod", 2, 0, "창고", "[창고 기간 연장][2][연장할 창고 Index(0:첫번째 창고, 4:다섯번째 창고)][기간]");
	m_impl->RegisterCommand(&GMCommandSystem::onSectorLuaScriptSetValue, L"sector_set_value", 2, 0, "@미분류", "[설명 추가해주세요]");
	m_impl->RegisterCommand(&GMCommandSystem::onSectorLuaScriptGetValue, L"sector_get_value", 1, 0, "@미분류", "[설명 추가해주세요]");
	m_impl->RegisterCommand(&GMCommandSystem::onKillMonster, L"killmonster", 3, 0, "몬스터", "[특정 몬스터, 혹은 모든 몬스터를 사망 시킴][3][N:아이템 드롭 여부:0(드롭안함)/1(드롭)][N:몬스터INDEX][N:수량(0:전체)]");
	m_impl->RegisterCommand(&GMCommandSystem::onKillAllOfMonsters, L"killallnpc", 0, 0, "@미분류", "[설명 추가해주세요]");
	m_impl->RegisterCommand(&GMCommandSystem::onFindNpc, L"findnpc", 1, 0, "몬스터", "[캐릭터가 위치한 섹터의 NPC개수를 확인][1][N:몬스터INDEX]");
	m_impl->RegisterCommand(&GMCommandSystem::onCommandDropIndex, L"dropindex", 1, 0, "드롭", "[설정한 아이템 드랍INDEX의 아이템을 모두 드랍][1][N:드랍INDEX]");
	m_impl->RegisterCommand(&GMCommandSystem::onCommandDropCount, L"dropcount", 1, 0, "드롭", "[설정한 수량의 아이템을 드랍][2][S:on/off][N:드랍개수]");
	m_impl->RegisterCommand(&GMCommandSystem::onWhereAmI, L"whereami", 0, 0, "위치", "[현재 캐릭터의 섹터번호를 출력][0]");
	m_impl->RegisterCommand(&GMCommandSystem::onCurrServerTime, L"stime", 0, 0, "@미분류", "[설명 추가해주세요]");
	m_impl->RegisterCommand(&GMCommandSystem::onCurrServerVersion, L"ver", 0, 0, "@미분류", "[현재 서버의 버전을 확인][0]");
	m_impl->RegisterCommand(&GMCommandSystem::onSpawnVolumeEnable, L"spawnvolume", 2, 0, "몬스터", "[특정 스폰 볼륨을 활성화/비활성화][2][S:on/off][N:볼륨INDEX]");
	m_impl->RegisterCommand(&GMCommandSystem::onHeroMissionInfo, L"heromissioninfo", 0, 0, "퀘스트", "[캐릭터가 위치한 섹터에서 진행 중인 영웅 미션 인덱스와 남은시간을 표시(영웅 미션이 종료된 경우 다음 미션의 시작 시간이 표시, 10초 간격으로 미션 진행을 확인하기 때문에 최대 10초의 차이가 발생할 수 있다.][0]");
	m_impl->RegisterCommand(&GMCommandSystem::onStartHeroMission, L"startheromission", 1, 0, "@미분류", "[케릭터가 위치한 섹터의 특정 영웅미션을 시작한다][1][N:미션인덱스]");
	m_impl->RegisterCommand(&GMCommandSystem::onSetPvP, L"setpvp", 1, 0, "PVP", "[PVP설정][3][pvp 대상 캐릭터 이름][내 팀 번호][상대팀 번호]");
	m_impl->RegisterCommand(&GMCommandSystem::onSetPvPAll, L"setpvpall", 0, 0, "PVP", "[PVP설정][0]");
	m_impl->RegisterCommand(&GMCommandSystem::onSetPvPAll2, L"setpvpall2", 1, 0, "PVP", "[PVP설정][0]");
	m_impl->RegisterCommand(&GMCommandSystem::onChangeTeam, L"changeteam", 1, 0, "PVP", "[팀설정][0]");
	m_impl->RegisterCommand(&GMCommandSystem::onMyTeam, L"myteam", 1, 0, "PVP", "[팀설정][0]");
	m_impl->RegisterCommand(&GMCommandSystem::onInitPvP, L"initpvp", 0, 0, "PVP", "[PVP초기화][0]");
	m_impl->RegisterCommand(&GMCommandSystem::onReqSearchMonster, L"searchmonster", 0, 0, "@미분류", "[설명 추가해주세요]");
	m_impl->RegisterCommand(&GMCommandSystem::onCommandMaxDamage, L"maxd", 1, 0, "캐릭터", "[캐릭터의 공격력을 현재 캐릭터가 출력할 수 있는 최대 공격력으로 설정][1][S:on/off]");
	m_impl->RegisterCommand(&GMCommandSystem::onCommandMaxDamageByNpcIndex, L"monmaxd", 1, 0, "캐릭터", "[캐릭터의 공격력을 현재 캐릭터가 출력할 수 있는 최대 공격력으로 설정][1][npcidex]");
	m_impl->RegisterCommand(&GMCommandSystem::onCommandInvincible, L"superman", 1, 0, "캐릭터", "[현재 캐릭터 무적 설정][1][S:on/off]");
	m_impl->RegisterCommand(&GMCommandSystem::onCommandStopRecovery, L"recovery", 1, 0, "캐릭터", "[현재 캐릭터 회복 멈춤][1][S:on/off]");
	m_impl->RegisterCommand(&GMCommandSystem::onCommandHide, L"hide", 1, 0, "캐릭터", "[현재 캐릭터 은신][1][S:on/off]");
	m_impl->RegisterCommand(&GMCommandSystem::onCommandRecognize, L"nonattackable", 1, 0, "캐릭터", "[현재 캐릭터 감지][1][S:on/off]");
	m_impl->RegisterCommand(&GMCommandSystem::onChangeMap, L"changemap", 2, 0, "위치", "[맵 이동][2][character_position_info.csv 내 Index 값][layer]");
	m_impl->RegisterCommand(&GMCommandSystem::onChangePos, L"changepos", 2, 0, "위치", "[동일 맵내 좌표이동][2][X][Y][Z]");
	m_impl->RegisterCommand(&GMCommandSystem::onCall, L"call", 1, 0, "위치", "[특정 캐릭터를 자신의 있는 위치로 소환한다.][1][캐릭터명]");
	m_impl->RegisterCommand(&GMCommandSystem::onCommandNpcSummon, L"npcsummon", 3, 0, "몬스터", "[NPC 소환][5][N:NPC Index][N:Level][N:Count][Direction][isStraightMove]");
	m_impl->RegisterCommand(&GMCommandSystem::onRepairAll, L"repair_all", 0, 0, "아이템", "[내구도 모두 회복][0]");
	m_impl->RegisterCommand(&GMCommandSystem::onSetNpcKeyValue, L"npcsetkey", 0, 0, "몬스터", "[NPC 키 값 변경][3][NPC Index][Key Index][Key value]");
	m_impl->RegisterCommand(&GMCommandSystem::onQuestReserve, L"questreserve", 1, 0, "@미분류", "[퀘스트예약][1][questId]");
	m_impl->RegisterCommand(&GMCommandSystem::onResetAchievement, L"resetachievement", 1, 0, "업적", "[업적 초기화][S:on/off(on:해당플레이어업적 모두 제거/off:특정 업적 제거)][N:(off일때)특정 업적 인덱스]");
	m_impl->RegisterCommand(&GMCommandSystem::onfinishAchivement, L"finishachievement", 1, 0, "업적", "[업적 달성][N:특정 업적 인덱스]");
	m_impl->RegisterCommand(&GMCommandSystem::onShowAbilityOption, L"showability", 1, 0, "@미분류", "[능력치 개수 표시][1][N:능력치이름]");
	m_impl->RegisterCommand(&GMCommandSystem::onCommandSetEnchantResult, L"setenchant", 2, 0, "@미분류", "[아이템 인첸트 결과 강제 설정][S:on/off][S:success/fail]");
	m_impl->RegisterCommand(&GMCommandSystem::onJumpTo, L"jump", 4, 0, "위치", "[임의의 맵의 특정 좌표로 이동][4][N:맵인덱스][F:좌표(x,y,z)]");
	m_impl->RegisterCommand(&GMCommandSystem::onRezenState, L"rezen", 0, 0, "@미분류", "[아이템 인첸트 결과 강제 설정][S:on/off][S:success/fail]");
	m_impl->RegisterCommand(&GMCommandSystem::onGiveBuff, L"givebuff", 1, 0, "@미분류", "[특정 버프 적용][1][skill_info.csv skillId 정보]");
	m_impl->RegisterCommand(&GMCommandSystem::onTakeBuff, L"takebuff", 1, 0, "@미분류", "[특정 버프 삭제][1][skill_info.csv skillId 정보]");
	m_impl->RegisterCommand(&GMCommandSystem::onShowBuff, L"showbuff", 1, 0, "@미분류", "[특정 버프 삭제][1][skill_info.csv skillId 정보]");
	m_impl->RegisterCommand(&GMCommandSystem::onSetSkillCheatId, L"npcsetskill", 2, 0, "몬스터", "[항상 지정된 스킬을 사용하도록 한다][1][N:npcIndex][N:skillID]");
	m_impl->RegisterCommand(&GMCommandSystem::onPlaySecen, L"playsecen", 1, 0, "@미분류", "[시네마틱설정][1][ 플레이 시간(초단위) ex) 1분이면 60초를 입력]");
	m_impl->RegisterCommand(&GMCommandSystem::onStartDungeonMission, L"startmission", 1, 0, "@미분류", "[탐색던젼 미션 시작][1][ adv_mission.csv의 index 입력 ]");
	m_impl->RegisterCommand(&GMCommandSystem::onClearDungeonMission, L"clearmission", 1, 0, "@미분류", "[탐색던전 미션 클리어][1][ adv_mission.csv의 index 입력 ]");
	m_impl->RegisterCommand(&GMCommandSystem::onGetItems, L"get", 1, 0, "아이템", "[아이템줍기][0]");
	m_impl->RegisterCommand(&GMCommandSystem::onPlayDeadAniAndDespawn, L"killspawnvolumemonster", 2, 0, "@미분류", "[특정 스폰볼륨 몬스터 사망애니메이션 플레이후 디스폰][2][spawn volume index][dead type:0~4(애니,빙결,석화,분쇄)]");
	m_impl->RegisterCommand(&GMCommandSystem::onIdleTime, L"idletime", 1, 0, "@미분류", "[현재 섹터의 npc dile time 설정][1][idle time]");
	m_impl->RegisterCommand(&GMCommandSystem::onShowPassivity, L"show_passivity", 1, 0, "@미분류", "[등록된 패시브 스킬을 보여준다.][0]");
	m_impl->RegisterCommand(&GMCommandSystem::onAddPassivity, L"add_passivity", 1, 1, "@미분류", "[패시브 스킬을 삽입.][1][1:스킬ID]");
	m_impl->RegisterCommand(&GMCommandSystem::onDelPassivity, L"del_passivity", 1, 1, "@미분류", "[패시브 스킬을 삭제.][1][1:스킬ID]");
	m_impl->RegisterCommand(&GMCommandSystem::onRemoveItemInRange, L"delitem", 1, 0, "@미분류", "[캐릭터를 중심으로 반경 N(m)범위 아이템 삭제][1][range]");
	//m_impl->RegisterCommand(&GMCommandSystem::onOpenSlotSpecialSkill,		L"openslotspecialskill", 1, 0, "캐릭터", "[전문기술 슬롯개방][1][slotPlace]");
	m_impl->RegisterCommand(&GMCommandSystem::onEquipSlotSpecialSkill, L"equipslotspecialskill", 1, 0, "@미분류", "[전문기술 슬롯장착][2][slotPlace][specialskill]");
#ifdef SPECIAL_SKILL_RENEWAL_by_jjangmo_180612
#else
	m_impl->RegisterCommand(&GMCommandSystem::onUnequipSlotSpecialSkill, L"unequipslotspecialskill", 1, 0, "@미분류", "[전문기술 슬롯해제][1][slotPlace]");
#endif
	m_impl->RegisterCommand(&GMCommandSystem::onSaveCoachMarks, L"savecoachmarks", 2, 0, "@미분류", "[도움말 정보저장][1][coachmark ID][2][coachmark Value]");
	m_impl->RegisterCommand(&GMCommandSystem::onSetDecreaseAggroRateByAttack, L"setaggrorateattack", 1, 0, "@미분류", "[공격으로 인한 aggro point 감소 비율 설정]");
	m_impl->RegisterCommand(&GMCommandSystem::onSetDecreaseAggroRateByTime, L"setaggroratetime", 1, 0, "@미분류", "[시간에 따른 aggro point 감소 비율 설정]");
	m_impl->RegisterCommand(&GMCommandSystem::onSetDecreaseAggroInterval, L"setaggrointerval", 1, 0, "@미분류", "[aggro point 감소 간격]");
	m_impl->RegisterCommand(&GMCommandSystem::onSetShowAggroState, L"setshowaggro", 1, 0, "@미분류", "[aggro point 현황 표시 여부 1:표시 0:표시안함]");
	m_impl->RegisterCommand(&GMCommandSystem::onSetShowAggroState, L"setshowaggro", 1, 0, "@미분류", "[aggro point 현황 표시 여부 1:표시 0:표시안함]");
	m_impl->RegisterCommand(&GMCommandSystem::onTestSectorFunction, L"testSectorFunction", 0, 0, "@미분류", "[주변 포탈 사용 요청]");
	m_impl->RegisterCommand(&GMCommandSystem::onSetShowAggroState, L"setshowaggro", 1, 0, "@미분류", "[aggro point 현황 표시 여부 1:표시 0:표시안함]");
	m_impl->RegisterCommand(&GMCommandSystem::onSetNearNPCAttackSpeedRatio, L"setnpcattackspeed", 1, 0, "@미분류", "[주변 NPC 이동 속도 변경]");
	m_impl->RegisterCommand(&GMCommandSystem::onSetNearNPCMoveSpeed, L"setnpcmovespeed", 1, 0, "@미분류", "[주변 NPC 이동 속도 변경]");
	m_impl->RegisterCommand(&GMCommandSystem::onDropSimulation, L"dropsimulation", 3, 0, "드롭", "[드랍 시뮬레이션]");
	m_impl->RegisterCommand(&GMCommandSystem::onGambleTest, L"gamble", 1, 0, "@미분류", "[겜블 테스트]");
	m_impl->RegisterCommand(&GMCommandSystem::onWhereIsNpc, L"whereisnpc", 1, 0, "@미분류", "[겜블 테스트]");
	m_impl->RegisterCommand(&GMCommandSystem::onSkillNoCooltime, L"skillnocooltime", 1, 0, "@미분류", "[쿨타임제거][on/off]");
	m_impl->RegisterCommand(&GMCommandSystem::onSkillClearCooltime, L"skillclearcooltime", 0, 0, "@미분류", "[쿨타임삭제][on/off]");
	m_impl->RegisterCommand(&GMCommandSystem::onJumpToNpc, L"jumptonpc", 1, 0, "위치", "[NPC위치로 이동][1][N:npcIndex]");
	m_impl->RegisterCommand(&GMCommandSystem::onCompleteHeroMission, L"completeheromission", 1, 0, "@미분류", "영웅 퀘스트 완료 : 영웅 미션 인덱스");
	m_impl->RegisterCommand(&GMCommandSystem::onFailHeroMission, L"failheromission", 1, 0, "@미분류", "영웅 퀘스트 실패: 영웅 미션 인덱스");
	m_impl->RegisterCommand(&GMCommandSystem::onSetAdventureMapTypeAndFloor, L"setnavitf", 2, 0, "@미분류", "[시공의틈 타입 & 층수 임의 설정][타입][층수]");
	m_impl->RegisterCommand(&GMCommandSystem::onSetAdventureMapList, L"setnavidungeon", 4, 0, "@미분류", "[시공의틈 맵 임의설정][맵1층인덱스][맵1층레이어][맵2층인덱스][맵2층레이어]......");
	m_impl->RegisterCommand(&GMCommandSystem::onDungeonGuideMove, L"dungeonguidemove", 2, 0, "@미분류", "[던전가이드 이동][타입][인덱스]");
	m_impl->RegisterCommand(&GMCommandSystem::onStartDialog, L"startdialog", 2, 0, "@미분류", "[npc 말풍선 표시][npc index][dialogindex]");
	m_impl->RegisterCommand(&GMCommandSystem::onNoSpentMp, L"nospentmp", 1, 0, "@미분류", "[on/off]");
	m_impl->RegisterCommand(&GMCommandSystem::onSetBattleDistance, L"setbattledistance", 1, 0, "@미분류", "distance value");
	m_impl->RegisterCommand(&GMCommandSystem::onSuicide, L"suicide", 0, 0, "@미분류", "자살");
	//m_impl->RegisterCommand( &GMCommandSystem::onOpenAllSpecialskillSlot,	L"allopenspecialskillslot", 0, 0, "@미분류", "전문기술 슬롯 모두 개방" );
	//m_impl->RegisterCommand( &GMCommandSystem::onBuyPortalNeedItem, L"buyportalneeditem", 2, 0, "@미분류", "포탈아이템 구매 테스트. [인덱스][개수]" );
	m_impl->RegisterCommand(&GMCommandSystem::onPortalRequest, L"portalrequest", 2, 0, "@미분류", "포탈 사용요청. [advAdmissionInde][난이도]");
	m_impl->RegisterCommand(&GMCommandSystem::onStorageMove, L"stroageMove", 2, 0, "@미분류", "창고이동 테스트 치트. [src.inventory][src.slot][to Storage = 1, to inventory = 0]");
	m_impl->RegisterCommand(&GMCommandSystem::onClearQuestMission, L"clearquestmission", 2, 0, "@미분류", "퀘스트 미션 클리어. [questId][missionIndex]");
	m_impl->RegisterCommand(&GMCommandSystem::onRegisterSkill, L"registerskill", 1, 0, "스킬", "대체스킬 등록 [skillId]");
	m_impl->RegisterCommand(&GMCommandSystem::onUnregisterSkill, L"unregisterskill", 1, 0, "스킬", "대체스킬 해제 [skillId]");
	m_impl->RegisterCommand(&GMCommandSystem::onSetSkillLevel, L"setskilllevel", 2, 0, "스킬", "스킬 레벨업[skillId][level]");
	m_impl->RegisterCommand(&GMCommandSystem::onUseSkillRune, L"useskillrune", 2, 0, "스킬", "스킬 룬 아이템 사용[iventory][slot]");
	m_impl->RegisterCommand(&GMCommandSystem::onEquipSkillRune, L"equipskillrune", 4, 0, "스킬", "스킬 룬 아이템 사용[iventory][slot]");
	m_impl->RegisterCommand(&GMCommandSystem::onChangeSkillRune, L"changeskillrune", 6, 0, "스킬", "스킬 룬 아이템 사용[iventory][slot]");
	m_impl->RegisterCommand(&GMCommandSystem::onAddSkillRune, L"addskillrune", 0, 0, "스킬", "클래스의 모든 스킬 룬 등록");
	m_impl->RegisterCommand(&GMCommandSystem::onPetTest, L"pet", 2, 0, "펫", "펫 테스트용 치트키[testType][petId][param1][param2]");
	m_impl->RegisterCommand(&GMCommandSystem::onAddPetPoint, L"addpetpoint", 1, 0, "펫", "펫 성장 포인트 추가[N:point]");
	m_impl->RegisterCommand(&GMCommandSystem::onEndlessNextStage, L"nextstage", 1, 0, "무한의탑", "[다음stageIndex]");

#ifdef __Patch_Tower_of_dawn_eunseok_2018_11_05
	m_impl->RegisterCommand( &GMCommandSystem::onChance,					L"chance",					1, 0, "새벽의탑", "[찬스번호]");
	m_impl->RegisterCommand( &GMCommandSystem::onTowerOfDawnStage,			L"stage",					1, 0, "새벽의탑", "[층변경]");
	m_impl->RegisterCommand( &GMCommandSystem::onRewardPoint,				L"rewardpoint",				1, 0, "새벽의탑", "[보상포인트 변경]");
#endif //__Patch_Tower_of_dawn_eunseok_2018_11_05
	m_impl->RegisterCommand(&GMCommandSystem::onAddGreenZen, L"addgreenzen", 1, 0, "아이템", "[마정석 획득 수량]");
	m_impl->RegisterCommand(&GMCommandSystem::onSoulSKillTest, L"soul", 2, 0, "스킬", "영혼 패시브 치트키[testType][param1][param2]");
	m_impl->RegisterCommand(&GMCommandSystem::onCharMoveGoodPosition, L"goodpos", 1, 0, "위치", "잘못된 위치인 경우 이동 테스트 치트키[1 = 거점, 2 = 마을]");
	m_impl->RegisterCommand(&GMCommandSystem::onUseItemRecoveryEntrance, L"testrecoveryitem", 2, 0, "아이템", "입장횟수 아이템 사용");
	m_impl->RegisterCommand(&GMCommandSystem::onRemoveUseItemLimit, L"removeuseitemlimit", 1, 0, "아이템", "아이템 그룹 쿨타임 제거");
	m_impl->RegisterCommand(&GMCommandSystem::onUpdateCombatPower, L"updatecombatpower", 0, 0, "캐릭터", "전투력갱신");
	m_impl->RegisterCommand(&GMCommandSystem::onBloodCastleComplete, L"bcwin", 0, 0, "블러드캐슬", "블캐완료");
	m_impl->RegisterCommand(&GMCommandSystem::onAchievementComplete, L"getachieve", 1, 0, "업적", "업적 강제 획득[업적id]");
	m_impl->RegisterCommand(&GMCommandSystem::onAchievementDelete, L"delachieve", 1, 0, "업적", "업적 강제 삭제[업적id]");
	m_impl->RegisterCommand(&GMCommandSystem::onAchievementGrade, L"achievelevel", 1, 0, "업적", "업적 등급 변경[등급id]");
	m_impl->RegisterCommand(&GMCommandSystem::onUseTownPortalItem, L"portalstone", 3, 0, "아이템", "마을귀환석 사용[N:inventory][N:slot][S:true/false]");
	m_impl->RegisterCommand(&GMCommandSystem::onCustomizingItem, L"customizing", 2, 0, "아이템", "아이템 외형 변화[S:insert/remove][N:target inventory][N:target slot][N:source invetory][N:source slot]");
	m_impl->RegisterCommand(&GMCommandSystem::onColosseumTest, L"colosseum", 2, 0, "투기장", "테스트");
	m_impl->RegisterCommand(&GMCommandSystem::onColosseumSearch, L"colsearch", 1, 0, "투기장재탐색 초기화", "테스트");
	m_impl->RegisterCommand(&GMCommandSystem::onColosseumStop, L"colpause", 0, 0, "투기장정지", "테스트");
	m_impl->RegisterCommand(&GMCommandSystem::onAddColosseumWP, L"addwp", 1, 0, "투기장승점", "테스트");
	m_impl->RegisterCommand(&GMCommandSystem::onColosseumNpcSpawn, L"colosseumnpc", 0, 0, "투기장", "테스트");
	m_impl->RegisterCommand(&GMCommandSystem::onChangeAccountGrade, L"accountgrade", 0, 0, "@미분류", "계정등급변경 [N:0:일반,1:개발자,2:GM]");
	m_impl->RegisterCommand(&GMCommandSystem::onRequestSurvey, L"getsurvey", 1, 0, "설문조사", "[1:설문인덱스]");
	m_impl->RegisterCommand(&GMCommandSystem::onDeleteCompletedSurvey, L"delsurvey", 0, 0, "설문조사", "완료,진행중인것 모두 삭제");
	m_impl->RegisterCommand(&GMCommandSystem::onEquipedItemEnchant, L"equipenchant", 1, 0, "착용아이템강화", "[1:강화수치]");
	m_impl->RegisterCommand(&GMCommandSystem::onQuestComplete, L"completequest", 1, 0, "퀘스트미션완료", "[1:퀘스트인덱스]");
	m_impl->RegisterCommand(&GMCommandSystem::onSortBag, L"sortbag", 1, 0, "인벤", "가방정렬");
	m_impl->RegisterCommand(&GMCommandSystem::onMoveNpc, L"movenpc", 4, 0, "몬스터", "[npc특정위치이동][4][NPC인덱스][X][Y][Z]");
	m_impl->RegisterCommand(&GMCommandSystem::onResetCoachMark, L"resetcoachmark", 0, 0, "@미분류", "도움말정보클리어");
	m_impl->RegisterCommand(&GMCommandSystem::onGrowItem, L"growitem", 0, 0, "아이템", "[아이템성장][2][itemSlot:Inventory][itemSlot:Slot]");
	m_impl->RegisterCommand(&GMCommandSystem::onItemExp, L"itemexp", 0, 0, "아이템", "[아이템성장][3][itemSlot:Inventory][itemSlot:Slot][exp]");

	m_impl->RegisterCommand(&GMCommandSystem::onSetSearchInterval, L"searchinterval", 1, 0, "몬스터", "[몬스터적탐색인터발][1][searchinterval]");
	m_impl->RegisterCommand(&GMCommandSystem::onKnightageRequirement, L"knireq", 0, 0, "기사단", "[기사단요건정보]");
	m_impl->RegisterCommand(&GMCommandSystem::onKnightageCheckName, L"kniname", 1, 0, "기사단", "[기사단이름확인][1][기사단이름]");
	m_impl->RegisterCommand(&GMCommandSystem::onKnightageCreate, L"knicreate", 1, 0, "기사단", "[기사단생성][1][기사단이름]");
	m_impl->RegisterCommand(&GMCommandSystem::onKnightageOpenStorage, L"kniopen", 0, 0, "기사단", "[기사단창고열기]");
	m_impl->RegisterCommand(&GMCommandSystem::onKnightageCloseStorage, L"kniclose", 0, 0, "기사단", "[기사단창고닫기]");
	m_impl->RegisterCommand(&GMCommandSystem::onKnightageAddContributionPoint, L"knihonor", 1, 0, "기사단", "[기사단전공포인트획득][1][획득할포인트]");
	m_impl->RegisterCommand(&GMCommandSystem::onKnightageClearStorage, L"knistclear", 0, 0, "기사단", "[기사단창고아이템모두비우기]");
	m_impl->RegisterCommand(&GMCommandSystem::onKnightageGetMyKnightageId, L"knightageid", 0, 0, "기사단", "[기사단id]");
	m_impl->RegisterCommand(&GMCommandSystem::onArtifact, L"artifact", 1, 0, "유물", "[유물 관련[1][create, exp, level, createall, openslot, openslotall, clear, list, recall, recallall, recallcancel, recalllevelmove, recallbyfile]");
	m_impl->RegisterCommand(&GMCommandSystem::onArtifactAlchemy, L"alchemy", 1, 0, "유물 연금", "[유물 연금 관련[1][complete, count]");
	m_impl->RegisterCommand(&GMCommandSystem::onMaze, L"maze", 2, 0, "미궁", "테스트");
	m_impl->RegisterCommand(&GMCommandSystem::onMazeTime, L"mazetime", 1, 0, "미궁", "시간조정 [증감수치 초단위]");
	m_impl->RegisterCommand(&GMCommandSystem::onMazeDifficulty, L"difficulty", 1, 0, "미궁", "입장레벨변경 [미궁 입장 최대난이도]");
	m_impl->RegisterCommand(&GMCommandSystem::onGuide, L"guide", 2, 0, "가이드", "테스트");
	m_impl->RegisterCommand(&GMCommandSystem::onAddRiftPoint, L"addriftpoint", 1, 0, "아이템", "[시공의조각 증가][1][시공의조각 개수]");
	m_impl->RegisterCommand(&GMCommandSystem::onSubRiftPoint, L"subriftpoint", 1, 0, "아이템", "[시공의조각 감소][1][시공의조각 개수]");
	m_impl->RegisterCommand(&GMCommandSystem::onChangePlayerCylinder, L"playercylinder", 1, 0, "플레이어", "[플레이어 서버 반지름 변경][1][실린더 반지름]");
	m_impl->RegisterCommand(&GMCommandSystem::onSetMyTeam, L"setmyteam", 1, 0, "플레이어", "[team 정보 변경][1][team index ( 1 or 2]");
	m_impl->RegisterCommand(&GMCommandSystem::onBagReset, L"streset", 0, 0, "창고", "초기화");
	m_impl->RegisterCommand(&GMCommandSystem::onBagItemClr, L"stclear", 0, 0, "창고", "아이템삭제");
	m_impl->RegisterCommand(&GMCommandSystem::onTestAbilityOptionChangeList, L"optionlist", 2, 0, "아이템", " 옵션 변경 목록 가져오기");
	m_impl->RegisterCommand(&GMCommandSystem::onTestAbilityOptionChangeSelect, L"optionselect", 4, 0, "아이템", "옵션 변경 선택");
	m_impl->RegisterCommand(&GMCommandSystem::onFaction, L"faction", 0, 0, "피아식별표시", "");
	m_impl->RegisterCommand(&GMCommandSystem::onShowMessageMap, L"showmessagemap", 0, 0, "볼륨메세지", "");
	m_impl->RegisterCommand(&GMCommandSystem::onSelfkill, L"selfkill", 0, 0, "자신사망", "");
	m_impl->RegisterCommand(&GMCommandSystem::onPlayerKillAll, L"playerkillall", 0, 0, "모든플레이어사망", "");
	m_impl->RegisterCommand(&GMCommandSystem::onGotoEnemy, L"gotoenemy", 0, 0, "카오스캐슬 적으로이동", "");
	m_impl->RegisterCommand(&GMCommandSystem::onRecovery, L"recover", 0, 0, "즉시부활", "");
	m_impl->RegisterCommand(&GMCommandSystem::onRecoverParty, L"recoverparty", 0, 0, "임의의파티원 부활", "");
	m_impl->RegisterCommand(&GMCommandSystem::onDungeonDifficultyCtrl, L"changemapdctrl", 4, 0, "위치", "[맵 이동][2][character_position_info.csv 내 Index 값][layer][difficulty][member]");
	m_impl->RegisterCommand(&GMCommandSystem::onEnterMythology, L"entermythology", 1, 0, "신화입장", "신화입장　[난이도]");
	m_impl->RegisterCommand(&GMCommandSystem::onDailyMission, L"dailymission", 0, 0, "일일미션 확인", "일일미션확인");
	m_impl->RegisterCommand(&GMCommandSystem::onDailyMissionReward, L"dailymissionreward", 2, 0, "일일미션보상[missionindex][mssionNO]", "일일미션보상");
	m_impl->RegisterCommand(&GMCommandSystem::onResetDailyMission, L"resetdailymission", 0, 0, "일일미션초기화", "일일미션초기화");
	m_impl->RegisterCommand(&GMCommandSystem::onChangeCharName, L"changename", 1, 0, "캐릭터", "이름변경 [새이름]");
	m_impl->RegisterCommand(&GMCommandSystem::onChangeKnightageName, L"changeknightagename", 1, 0, "기사단", "이름 변경　[새이름]");
	m_impl->RegisterCommand(&GMCommandSystem::onUseCubeItem, L"cubeuse", 4, 0, "아이템", "큐브");
	m_impl->RegisterCommand(&GMCommandSystem::onTranscendStone, L"transstone", 2, 0, "아이템 ", "초월석");
	m_impl->RegisterCommand(&GMCommandSystem::onRemoveTrascendStoneFailCount, L"rtransstone", 4, 0, "아이템", "초월석 [스크롤인벤][스크롤슬롯][타겟인벤[타겟슬롯]");
	m_impl->RegisterCommand(&GMCommandSystem::onItemExpand, L"itemexpand", 4, 0, "아이템", "기간제 연장 테스트 [기간연장 아이템의 인벤토리번호][기간연장 아이템의 슬롯번호][대상 아이템의 인벤토리번호][대상 아이템의 슬롯번호]");
	m_impl->RegisterCommand(&GMCommandSystem::onItemExpiry, L"itemexpiry", 2, 0, "아이템", "기간제 아이템 만료 [대상 아이템의 인벤토리번호][아이템의 슬롯번호]");
	m_impl->RegisterCommand(&GMCommandSystem::onItemIdentify, L"itemidentity", 2, 0, "아이템", "[인벤토리번호][슬롯번호]");
	m_impl->RegisterCommand(&GMCommandSystem::onAdditionalOption, L"additionaloption", 2, 0, "ㄴ아이템", "[인벤토리번호][슬롯번호]");
	m_impl->RegisterCommand(&GMCommandSystem::onRequestAchievementRewardItem, L"getachievereward", 0, 0, "업적보상 아이템 획득", "");
	m_impl->RegisterCommand(&GMCommandSystem::onExchangeItemShop, L"exchange", 1, 0, "거래소", "[거래소테스트]");
	m_impl->RegisterCommand(&GMCommandSystem::onExchangeItemShopNoDealy, L"exchangenodelay", 0, 0, "거래소", "[거래소 아이템 등록이 즉시 되도록 변경]");
	m_impl->RegisterCommand(&GMCommandSystem::onExchangeItemShopAvgPrice, L"exchangeprice", 0, 0, "거래소", "[거래소 아이템 시세조회]");
	m_impl->RegisterCommand(&GMCommandSystem::onExchangeItemShopAcquireAtOnce, L"exchangeacquires", 0, 0, "거래소", "[거래소 한번에 가져오기]");
	m_impl->RegisterCommand(&GMCommandSystem::onRequestEmergencyEscape,		L"escape",					0, 0, "비상탈출", "");
	m_impl->RegisterCommand(&GMCommandSystem::onSetAutoRevivalInfo,			L"setautorevive",			3, 0, "자동부활정보 세팅", "[체력회복률][마나회복률][대기시간]");
	m_impl->RegisterCommand(&GMCommandSystem::onAddIgnoreMpCount,			L"addnompcount",			1, 0, "스킬MP무시 개수증가", "[증가개수]");
	m_impl->RegisterCommand(&GMCommandSystem::onUseMembershipServiceItem,	L"usemembershipitem",		2, 0, "플래티넘 아이템 사용", "[인벤토리번호][슬롯번호]");
	m_impl->RegisterCommand(&GMCommandSystem::onSetMembershipServiceTime,	L"setmembershiptime",		1, 0, "플래티넘 멤버쉽 종료", "[남은시간(최소5초)]");
	m_impl->RegisterCommand(&GMCommandSystem::onShowMembershipServiceState, L"showmembership",			0, 0, "플래티넘 멤버쉽 상태 보기", "");
	m_impl->RegisterCommand(&GMCommandSystem::onRequestMembershipReward,	L"getmembershipreward",		1, 0, "맴버쉽보상 받기", "[N: 0(eDaily_Connect_Reward), 1(eDaily_Time_Reward)]");
	m_impl->RegisterCommand(&GMCommandSystem::onInsEventItems,				L"eventitemins",			1, 0, "이벤트 인벤토리에 아이템 넣기", "[N:반복횟수][N:아이템번호][N:수량]");
	m_impl->RegisterCommand(&GMCommandSystem::onReqEventItemPage,			L"eventitempage",			2, 0, "이벤트 인벤토리 페이지 보기", "[N:페이지당 라인수][N:페이지번호]");
	m_impl->RegisterCommand(&GMCommandSystem::onReqEventItemPickUp,			L"eventitempickup",			1, 0, "이벤트 인벤토리 아이템 랜덤 수령", "[N:수령할 아이템 수량]");
	m_impl->RegisterCommand(&GMCommandSystem::onShowEventItem,				L"eventitemshow",			0, 0, "이벤트 인벤토리 보기", "");
	m_impl->RegisterCommand(&GMCommandSystem::onBlockItem,					L"blockitem",				3, 0, "아이템", "아이템 블럭[InvenPos][Slot][0,1]");
	m_impl->RegisterCommand( &GMCommandSystem::onWeaponFusion,				L"weaponfusion",			4, 0, "아이템", "아이템 합성[InvenPos][Slot][0,1]" );
	m_impl->RegisterCommand( &GMCommandSystem::onMissionMapEntrance,		L"pvpenter",				2, 0, "pvp입장", "[N:indexZone][N:executionKey]" );
	m_impl->RegisterCommand( &GMCommandSystem::onApplyRaid,					L"applyRaid",				1, 0, "레이드에 신청", "[N:raidId]" );
	m_impl->RegisterCommand( &GMCommandSystem::onKnightageFollower,			L"follower",				2, 0, "영지", "팔로우[0 : 해제 , 1 : 가입][ 1일 경우 기사단 아이디]" );
	m_impl->RegisterCommand( &GMCommandSystem::onDailyShopReset,			L"dailyReset",				0, 0, "상점", "" );
	m_impl->RegisterCommand( &GMCommandSystem::onAmplicationComplete,		L"ampcomplete",				0, 0, "아이템", "" );
	m_impl->RegisterCommand( &GMCommandSystem::onJoinDominionWisdom,		L"joinwisdom",				1, 0, "영지", "영지 입장[영지아이디]" );
	m_impl->RegisterCommand( &GMCommandSystem::onSectorDominion,			L"sectordominion",			1, 0, "영지", "[기사단아이디]" );
	m_impl->RegisterCommand( &GMCommandSystem::onKnightageGiveTrophy,		L"givetrophy",				0, 0, "기사단", "트로피 기부[N:raidId]" );
	m_impl->RegisterCommand(&GMCommandSystem::on33pvpGiveUp,				L"giveup",					0, 0, "투기장항복", "[None]");
	m_impl->RegisterCommand(&GMCommandSystem::on33pvpMSG,					L"33pvp",					1, 0, "투기장디버깅메세지", "[None]");
	m_impl->RegisterCommand(&GMCommandSystem::onwinpoint,					L"winpoint",				2, 0, "투기장승점변경", "[None]");
	m_impl->RegisterCommand(&GMCommandSystem::onsetwinpoint,				L"setwinpoint",				1, 0, "33투기장승점변경", "[None]");
	m_impl->RegisterCommand(&GMCommandSystem::onsurrender,					L"surrender",				0, 0, "투기장항복활성", "[None]");
	m_impl->RegisterCommand( &GMCommandSystem::onwincount,					L"wincount",				0, 0, "투기장승리횟수", "[None]" );
	m_impl->RegisterCommand(&GMCommandSystem::onChangeAllowedMoveDistance,	L"changeallowdist",			1, 0, "미분류", "[PC 이동 시 MoveSpeedChecker 허용 가능 거리 수정][1][허용거리]");
	m_impl->RegisterCommand( &GMCommandSystem::onCreateSocket,				L"csocket",					2, 0, "아이템", "[인벤번호][슬롯번호]" );
	m_impl->RegisterCommand( &GMCommandSystem::onEnchantItem,				L"enchant",					2, 0, "아이템", "[인벤번호][슬롯번호]" );
	m_impl->RegisterCommand(&GMCommandSystem::onBlackMarketList,			L"blackmarketlist",			0, 0, "암상인", "아이템리스트요청 [None]");
	m_impl->RegisterCommand(&GMCommandSystem::onBlackMarketBuy,				L"blackmarketbuy",			2, 0, "암상인", "아이템구입요청 [아이템인덱스][수량]");
	m_impl->RegisterCommand(&GMCommandSystem::onBlackMarketExtract,			L"blackmarketextract",		0, 0, "암상인", "아이템 추출 [None]");
	m_impl->RegisterCommand(&GMCommandSystem::onBlackMarketBuyReset,		L"blackmarketreset",		0, 0, "암상인", "구입 초기화 [None]");
	m_impl->RegisterCommand(&GMCommandSystem::onautohack,					L"autohack",				0, 0, "오토", "질문 [None]");
	m_impl->RegisterCommand(&GMCommandSystem::onautohackchange,				L"autohackchange",			0, 0, "오토", "질문 변경[None]");
	m_impl->RegisterCommand(&GMCommandSystem::onAnswer,						L"answer",					0, 0, "오토", "질문 응답 [None]");
	m_impl->RegisterCommand(&GMCommandSystem::onautoview,					L"autoview",				0, 0, "오토", "정보[None]");
	m_impl->RegisterCommand( &GMCommandSystem::onSeasonReqTest,				L"seasonreqtest",			2, 0, "시즌", "[패킷이벤트번호][밸류]" );
	m_impl->RegisterCommand( &GMCommandSystem::onSeasonOnEvent,				L"seasonevent",				1, 0, "시즌", "[이벤트번호][밸류]" );
	m_impl->RegisterCommand( &GMCommandSystem::onSeasonMissionClear,		L"seasonmissionclear",		1, 0, "시즌", "[미션번호]" );
	m_impl->RegisterCommand( &GMCommandSystem::onSeasonMissionEventAllClear, L"seasonclear",			1, 0, "시즌", "[이벤트번호][밸류]" );
	m_impl->RegisterCommand( &GMCommandSystem::onNetherWorldStageStateChange, L"netherchange",			0, 0, "사자의 나락", "[1:클리어 2: 실패]" );
	m_impl->RegisterCommand( &GMCommandSystem::onGameEvent,					L"gevent",					1, 0, "이벤트", "[타입][이벤트아이디]" );
	m_impl->RegisterCommand( &GMCommandSystem::onEnchantTicket,				L"eticket",					3, 0, "강화권", "[강화권 위치][타겟 위치][보호권 위치]" );
	m_impl->RegisterCommand( &GMCommandSystem::onReturnUserReward,			L"retreward",				0, 0, "복귀 유저 보상", "" );
	m_impl->RegisterCommand( &GMCommandSystem::onAchievementGradeToZero,	L"achievegradezero",		0, 0, "업적 등급 초기화", "" );
	m_impl->RegisterCommand( &GMCommandSystem::onRemoveDivison,				L"removeDivision",			2, 0, "아이템", "[디비전][카운트]" );
	m_impl->RegisterCommand(&GMCommandSystem::onPrivateStorageTabUnlock,	L"ptunlock",				0, 0, "[개인창고]", "탭확장 [None]");
	m_impl->RegisterCommand(&GMCommandSystem::onPrivateStorageTabUnlockAll, L"ptunlockall",				0, 0, "[개인창고]", "모든탭확장 [None]");
	m_impl->RegisterCommand(&GMCommandSystem::onPrivateStorageClearItemAll, L"ptitemclear",				0, 0, "[개인창고]", "모든아이템제거 [None]");
	m_impl->RegisterCommand(&GMCommandSystem::onPrivateStorageTabReset,		L"ptreset",					0, 0, "[개인창고]", "초기화 [None]");
	m_impl->RegisterCommand( &GMCommandSystem::onPSROpen,					L"psropen",					1, 0, "[가위바위보 이벤트]", "창 오픈 [eventindex]" );
	m_impl->RegisterCommand( &GMCommandSystem::onPSREvent,					L"psrevent",				3, 0, "[가위바위보 이벤트]", "[eventindex, eventType, psrType ]" );
	m_impl->RegisterCommand(&GMCommandSystem::onUAccEvent,					L"uaccevent",				3, 0, "[노리아전야제 이벤트]", "[opt(1set,2resetstamp), eventindex, count]");
	m_impl->RegisterCommand( &GMCommandSystem::onCharacterSlotExtend,		L"charextend",				0, 0, "아이템", "[]" );
	m_impl->RegisterCommand(&GMCommandSystem::onTalisman,					L"talisman",				1, 0, "탈리스만", "[create, exp, level, point, skillreset, reset, changeowner]");
	m_impl->RegisterCommand(&GMCommandSystem::onAdvanture,					L"advanture",				1, 0, "탐험이벤트", "[reset, set, complete, reqinfo]");
	m_impl->RegisterCommand(&GMCommandSystem::onEmotion,					L"emotion",					1, 0, "에테르시스템", "[resetcount, open, info, infopk, backdate, point]");
	m_impl->RegisterCommand(&GMCommandSystem::onMarble,						L"marble",					1, 0, "레전드마블", "[condreset, condset, throw, point]");
	m_impl->RegisterCommand(&GMCommandSystem::onPVEGameGiveup,				L"pvegiveup",				1, 0, "비공정 디펜스 포기", "[]");
	m_impl->RegisterCommand(&GMCommandSystem::onSelectGambleIndex,			L"sgItem",					2, 0, "아이템", "겜블 선택 보상[]");
	m_impl->RegisterCommand(&GMCommandSystem::onResetBuffEvent,				L"resetbuffevent",			1, 0, "버프이벤트 정보 리셋", "[evnetIndex]");
	m_impl->RegisterCommand(&GMCommandSystem::onGuideCategoryEvent,			L"guidetrigger",			2, 0, "안내 트리거 이벤트", "[category] [value]");
	m_impl->RegisterCommand(&GMCommandSystem::onGuideCategoryReset,			L"guidereset",				1, 0, "안내 카테고리 리셋", "[category]");
	m_impl->RegisterCommand(&GMCommandSystem::onGuideMissionClear,			L"guidemissionclear",		2, 0, "안내 미션 완료", "[category][division]");
	m_impl->RegisterCommand(&GMCommandSystem::onGuideMissionReward,			L"guidemissionreward",		1, 0, "안내 미션 보상", "[category]");
	m_impl->RegisterCommand(&GMCommandSystem::onSetReturnDays,				L"setreturndays",			2, 0, "복귀날짜 세팅", "[type : 'account' or 'character'][days]");
	m_impl->RegisterCommand(&GMCommandSystem::onGameSystemReturnUserGetReward, L"returnuserreward",		1, 0, "계정복귀보상", "[]");
	m_impl->RegisterCommand(&GMCommandSystem::onSetItemSellFlag,			L"setsellflag",				1, 0, "판매 플래그 설정", "[open/close]");
	m_impl->RegisterCommand(&GMCommandSystem::onFloorSpawn,					L"floorspawn",				0, 0, "카오스캐슬", "바닥오픈");
	m_impl->RegisterCommand(&GMCommandSystem::onPetBoosterSkillReset,		L"petboosterskillreset",	0, 0, "펫", "부스터 스킬 초기화 [none]");
	m_impl->RegisterCommand(&GMCommandSystem::onPetQuestReset,				L"petquestreset",			0, 0, "펫", "모험 초기화 [none]");
	m_impl->RegisterCommand(&GMCommandSystem::onPetInfoUpdateAll,			L"petinfoupdateall",		0, 0, "펫", "모든 펫 정보 갱신[none]");
	m_impl->RegisterCommand(&GMCommandSystem::onNoticePopUpReset, L"noticepopupreset", 0, 0, "알림 팝업", "초기화 [none]");
	m_impl->RegisterCommand(&GMCommandSystem::onNoticePopUpLoad, L"noticepopupload", 0, 0, "알림 팝업", "읽기 [none]");
	m_impl->RegisterCommand(&GMCommandSystem::onTreasureOpen, L"treasureopen", 0, 0, "루에리보물상자", "UI 열기 [none]");
	m_impl->RegisterCommand(&GMCommandSystem::onTreasureClose, L"treasureclose", 0, 0, "루에리보물상자", "UI 닫기 [none]");
	m_impl->RegisterCommand(&GMCommandSystem::onTreasurePickup, L"treasurepickup", 0, 0, "루에리보물상자", "뽑기 [none]");
	m_impl->RegisterCommand(&GMCommandSystem::onTreasureRefreshSlot, L"treasurerefreshslot", 0, 0, "루에리보물상자", "새로 고침 [none]");
	m_impl->RegisterCommand(&GMCommandSystem::onTreasureRefillRefreshCount, L"treasurerefill", 0, 0, "루에리보물상자", "리셋 [none]");
	m_impl->RegisterCommand(&GMCommandSystem::onTreasureRefillAllUsers, L"treasurerefillallusers", 0, 0, "루에리보물상자", "모든 유저 새로고침 회복[none]");
	m_impl->RegisterCommand( &GMCommandSystem::onEventBuffOnOff,			L"eventbuff",				1, 0, "이벤트", "이벤트 버프 온오프[none]" );
	m_impl->RegisterCommand(&GMCommandSystem::onItemFusion, L"runeFusion", 0, 0, "아이템", "[none]");
	m_impl->RegisterCommand(&GMCommandSystem::onRuneEquip, L"runeequip", 0, 0, "아이템", "[none]");
	m_impl->RegisterCommand(&GMCommandSystem::onAliveCount, L"playerCount", 1, 0, "맵", "[0 모두 : 1 살아 있는 플레이어]");

#ifdef ABILITY_RENEWAL_20180508
	m_impl->RegisterCommand(&GMCommandSystem::onOpenBraceletSlot, L"openBraceletSlot", 1, 0, "아이템", "팔지 오픈 [none]");
	m_impl->RegisterCommand(&GMCommandSystem::onBraceletEquip, L"braceletEquip", 1, 0, "아이템", "팔지 장착 [0 : equip : 1 : unEquip");
	m_impl->RegisterCommand(&GMCommandSystem::onSelectOption, L"selectOpt", 3, 0, "아이템", "팔지 선택 옵션");
#endif
	m_impl->RegisterCommand(&GMCommandSystem::onShowDifficulty, L"showdiff", 0, 0, "난이도", "");
#ifdef	__Patch_Limited_Banner_Shop_robinhwp
	m_impl->RegisterCommand(&GMCommandSystem::onLimitedBannerSystem,		L"limitedbanner",			0, 0, "제한배너상품", "제한배너상품 GM command");
#endif	__Patch_Limited_Banner_Shop_robinhwp
#ifdef __lockable_item_181001
	m_impl->RegisterCommand(&GMCommandSystem::onLockableItem,				L"lockItem",				0, 0, "아이템", "[Inven],[Slot],[onOff]");
	m_impl->RegisterCommand(&GMCommandSystem::onLockableItemSetup,			L"lockItemCheat",			0, 0, "아이템", "0 == value false, 0 != value true");

#endif
#ifdef __Patch_Event_Buff_Add_Benefit_by_jjangmo_20181122
	m_impl->RegisterCommand( &GMCommandSystem::onEventBuffGift,				L"eventbuffgift",			1, 0, "이벤트버프", "커멘드" );
#endif
#ifdef __Patch_ImprintedMemory_by_raylee_181128
	m_impl->RegisterCommand(&GMCommandSystem::onImprint,					L"imprint",					1, 0, "기억각인", "커멘드");
#endif 
#ifdef __Patch_Legend_Of_Bingo_by_jaekwan_20181126
	m_impl->RegisterCommand(&GMCommandSystem::onOpenBingoEvent,				L"openbingo",				1, 0, "빙고이벤트UI오픈", "커멘드 []");
	m_impl->RegisterCommand(&GMCommandSystem::onSetBingoPoint,				L"setbingopoint",			1, 0, "빙고포인트 지정", "커멘드 [bingopoint]");
	m_impl->RegisterCommand(&GMCommandSystem::onSetBingoResetPoint,			L"setbingoresetpoint",		1, 0, "빙고슬롯초기화포인트 지정", "커멘드 [resetpoint]");
	m_impl->RegisterCommand(&GMCommandSystem::onResetBingoEventInfo,		L"resetbingoevent",			1, 0, "빙고이벤트 초기화", "커멘드 [resetbingoevent]");
#endif // __Patch_Legend_Of_Bingo_by_jaekwan_20181126

#ifdef __Patch_WebOfGod_by_jason_20181213
	m_impl->RegisterCommand(&GMCommandSystem::onWebOfGod, L"wog", 1, 0, "관리자의 회랑", "");
#endif
#ifdef __Patch_Talismanbox_Ticket_Reward_System_by_ch_181224
	m_impl->RegisterCommand(&GMCommandSystem::onResetTalismanboxTicketRewardInfo, L"resettalismanbox", 1, 0, "", "");	
#endif


	m_impl->RegisterCommand(&GMCommandSystem::onSendMailWithItem, L"sendmail", 2, 0, "아이템", "");

#ifdef __Patch_Mercler_Secret_Shop_by_ch_2019_04_24
	m_impl->RegisterCommand(&GMCommandSystem::onResetMerclerSecretShopInfo, L"resetmercler", 0, 0, "", "");
	m_impl->RegisterCommand(&GMCommandSystem::onBuyMerclerSecretShop, L"buymercler", 0, 0, "", "");
#endif // __Patch_Mercler_Secret_Shop_by_ch_2019_04_24

#ifdef __Patch_SetItem_Renewal_by_kangms_2019_4_10
	m_impl->RegisterCommand(&GMCommandSystem::onSetCardOpen,		L"setcardopen",		1, 0, "세트카드 개방",					"setcardopen [세트카드인덱스]");
	m_impl->RegisterCommand(&GMCommandSystem::onSetCardClose,		L"setcardclose",	1, 0, "세트카드 닫기(장착않됨)",		"setcardclose [세트카드인덱스]");
	m_impl->RegisterCommand(&GMCommandSystem::onSetCardCloseAll,	L"setcardcloseall", 0, 0, "모든 세트카드 닫기(장착않됨)",	"setcardcloseall ");
	m_impl->RegisterCommand(&GMCommandSystem::onSetPoint,			L"setpoint",		3, 0, "세트 포인트 조정",				"setpoint [1] = [change, calc]");
	m_impl->RegisterCommand(&GMCommandSystem::onSetCardEquipPos,	L"setcardequippos", 2, 0, "세트카드 장착위치 설정",			"setcardequippos [세트카드인덱스][장착위치]");
#endif//__Patch_SetItem_Renewal_by_kangms_2019_4_10
}

SessionKey const GMCommandSystem::getSessionKey(EntityPlayer* player)
{
	return player->GetWorldServerSessionKey();
}

//---------------------------------------------------------------------------------------------------------------
// GM Command processors
//---------------------------------------------------------------------------------------------------------------

const Bool GMCommandSystem::onCommandCreateItem(EntityPlayer* player, const CommandString& cmd)
{
	VALID_RETURN(player && player->IsValid(), false);

	IndexItem itemIndex = 0;
	Int32 cnt = 0, enchantLv = 0;
	cmd >> itemIndex >> cnt >> enchantLv;

	auto scriptItemOption = SCRIPTS.GetScript<ItemOptionScript>();
	VALID_RETURN(scriptItemOption, false);

	const ItemInfoElem* infoElem = SCRIPTS.GetItemInfoScript(itemIndex);
	VALID_DO(infoElem, theGameMsg.SendGameDebugMsg(player, L"%d : 타입의 아이템은 존재 하지 않습니다.\n", itemIndex);
	return false;);
	VERIFY_RETURN( infoElem->overlap, false );

	cnt = MAX( cnt, 1 );
	cnt = MIN( cnt, 50 * infoElem->overlap );
	enchantLv = MAX( enchantLv, 0 );
	enchantLv = MIN( enchantLv, Limits::MAX_ENCHANT_LEVEL );

	EnchantLevel enchantLvLimit = scriptItemOption->MaxEnchantLevel(infoElem->indexItem);
	enchantLv = MIN(enchantLv, enchantLvLimit);

	ItemData itemData(itemIndex, cnt);
	itemData.enchant = static_cast<EnchantLevel>(enchantLv);

	ErrorItem::Error eErrorItem = player->GetInventoryAction().Pickup(itemData);
	if (eErrorItem != ErrorItem::SUCCESS)
	{
		theGameMsg.SendGameDebugMsg(player, L"%d : 아이템 생성에 실패하였습니다 (%d)\n", itemIndex);
		if (eErrorItem == ErrorItem::E_TRANSACION_FAILED)
		{
			EzcResPickUp* res = NEW EzcResPickUp;
			res->item.indexItem = itemData.indexItem;
			res->eError = eErrorItem;
			SERVER.SendToClient(player, EventPtr(res));
		}
		return true;
	}

	return true;
}

const Bool GMCommandSystem::onCommandCreateOptionItem(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexItem itemIndex = 0;
	EAT::Enum optionType = EAT::NONE;
	EffectSection::Enum optionSection = EffectSection::NONE;
	Int32 optionValue = 0;
	cmd >> itemIndex >> (AbilityType&)optionType >> (Byte&)optionSection >> optionValue;

	ActionPlayerInventory& invenAction = player->GetInventoryAction();

	const ItemInfoElem* elem = SCRIPTS.GetItemInfoScript(itemIndex);
	VERIFY_RETURN(elem, false);
	VERIFY_RETURN(elem->IsEquipable(), false);

	ItemPos freeSlot;
	VERIFY_RETURN(invenAction.GetFreeSlot(elem->GetInvenRange4Pushable(), __out freeSlot), false);

	ItemData data = ItemFactory::CreateItemData(*elem, 1);
	{
		AbilityPack pack;
		pack.type = optionType;
		if (EffectSection::POINT == optionSection)
		{
			pack.point = optionValue;
			pack.percent = 0;
		}
		else if (EffectSection::PERCENT == optionSection)
		{
			pack.point = 0;
			pack.percent = optionValue;
		}
		else
		{
			return false;
		}
		data.AppendOptionAbility<AbilityPack>(pack, data.abilityPackMap);
	}

	ItemFactory::CreateItemInputParam param(data, ItemObject::ItemAllocReason::PUSH );
	ItemPtr createdItem = ItemFactory::CreateItem(param);
	VERIFY_RETURN(createdItem, false);

	ItemPlace toPlace = ItemPlace(freeSlot, createdItem->GetItemId());
	createdItem->SetPos(InvalidItemPos); // 메모리에 처음잡히므로, 명시적으로 초기화(InvalidItemPos), from->to룰참조	

#ifdef __delete_itemMove__hwayeong180822
	ErrorItem::Error eErrorItem = Inven::SetUpItemFromProcedure(player, toPlace, createdItem, ProcedureType::Insert);
#else
	ErrorItem::Error eErrorItem = Inven::SetUpItemFromProcedure( player, toPlace, createdItem, ProcedureType::PROC_INSERT );
#endif
	if (eErrorItem != ErrorItem::SUCCESS)
	{
		ItemFactory::DeallocateItem(createdItem, ItemObject::ItemAllocReason::PUSH_ITEM, __FILE__, __LINE__);
		return false;
	}

	EReqDbInsertItem* reqDb = NEW EReqDbInsertItem;
	reqDb->accountId = player->GetAccountId();
	reqDb->charId = player->GetCharId();
#ifdef __Item_Database_logic_change__hwaYeong_180822
	reqDb->items.push_back(createdItem->Clone());
#else
	createdItem->FillUpItemData( reqDb->item );
#endif
	SERVER.SendToDb(player, EventPtr( reqDb ) );

	EzcResPickUp* res = NEW EzcResPickUp;
	res->eError = ErrorItem::SUCCESS;
	createdItem->FillUpItemData( res->item );
	SERVER.SendToClient( player, EventPtr(res) );
	
	theGameMsg.SendGameDebugMsg( player, L"Create manual Item(%s)\n", createdItem->ToString().c_str());

	return true;
}

const Bool GMCommandSystem::onCommandSummon(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexItem itemIndex = 0;
	Int32 itemCount = 0;
	cmd >> itemIndex >> itemCount;

	itemCount = (0 >= itemCount) ? 1 : itemCount;

	Sector* sector = player->GetSector();
	VERIFY_RETURN(sector, false);

	EntityDropped* dropped = static_cast<EntityDropped*>(theEntityFactory.Create(EntityTypes::DROPPED_ITEM, sector->GetNextSeq(EntityTypes::DROPPED_ITEM)));
	VERIFY_RETURN(dropped, false);

	ActionDroppedItem* droppedItemAction = dropped->GetAction<ActionDroppedItem>();
	if (droppedItemAction == NULL)
	{
		MU2_ASSERT(0);
		delete dropped;
		return false;
	}

	const ItemInfoElem* elem = SCRIPTS.GetItemInfoScript(itemIndex);
	VERIFY_RETURN(elem, false);

	ItemData data = ItemFactory::CreateItemData(*elem, itemCount);

	ItemFactory::CreateItemInputParam param(data, ItemObject::ItemAllocReason::PUSH);

	const ItemPtr item = ItemFactory::CreateItem(param );
	VERIFY_DO( item, delete dropped; return false;);

	droppedItemAction->SetItem(item);

	Vector3 dropPosition = player->GetCurrPos();

	Float diff_x = GetRandBetweenF(-150.f, 150.f);
	Float diff_y = GetRandBetweenF(-150.f, 150.f);

	dropPosition.x += diff_x;
	dropPosition.y += diff_y;

	droppedItemAction->SetPos(dropPosition);

	VectorEntityId ownerPlayers;
	ownerPlayers.push_back(player->GetId());

	droppedItemAction->SetOwners(ownerPlayers);
	droppedItemAction->PlaceGrid(sector);
	droppedItemAction->SetParent(0);
	droppedItemAction->SetObjectIndex(0);
	dropped->GetHsm().Tran(EntityState::STATE_DROPPED_ITEM_SPAWN);

	ENtfGameAddEntity* ntf = NEW ENtfGameAddEntity;
	EventPtr ntfPtr(ntf);

	dropped->GetCognitionAction().SyncAppearance(ntf->syncInfo, player);

	ActionSector& actionSector = player->GetSectorAction();

	for (const EntityId & ownerId : ownerPlayers)
	{
		EntityPlayer* dropOwner = actionSector.GetPlayerInMyNearGrid(ownerId);
		VALID_DO(dropOwner && dropOwner->IsValid(), continue);

		SERVER.SendToClient(dropOwner, ntfPtr);
	}

	return true;
}

const Bool GMCommandSystem::onCommandChangeMoveSpeed(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Int32 inputSpeed = 0;
	cmd >> inputSpeed;

	const Float currentSpeed = static_cast<Float>(player->GetAbilityValue(EAT::RUN_SPEED));

	Int32 speed = inputSpeed - static_cast<Int32>(currentSpeed);

	AbilityParam abilityParam;
	abilityParam.type = EAT::RUN_SPEED;
	abilityParam.value = speed;
	abilityParam.section = EffectSection::POINT;
	abilityParam.isOnOff = true;
	player->ChangedSkillAbility(abilityParam);
	player->CalculateAbility();

	const Float computedSpeed = static_cast< Float >(player->GetAbilityValue(EAT::RUN_SPEED));

	theGameMsg.SendGameDebugMsg(player, L"MoveSpeed(%f) changed", computedSpeed);

#ifdef __Patch_Lua_InstanceGroup_Param_by_jason_20190109
	InstanceSectorGroupEx* group = player->GetSector()->GetInstanceGroup();
	if (inputSpeed > 0 && group )
	{
		group->SetValueByKey("gmCommand_speed", inputSpeed);
	}
#endif

	return true;
}

//---------------------------------------------------------------------------------------------------------------


const Bool GMCommandSystem::onCommandChangeMoveSpeedHack(EntityPlayer* owner, const CommandString& cmd)
{
	VERIFY_RETURN(owner && owner->IsValid(), false);

	Int32 speed = 0;
	cmd >> speed;
#ifdef BES_SPEED_HACK_TEST
	owner->GetAction<ActionPlayerAbility>()->debufMoveSpeed = speed;
#endif 

	/*ENtfGameUpdateAbilityValue* ntf = new ENtfGameUpdateAbilityValue;
	ntf->dps			= owner->GetAction<ActionPlayerAbility>()->GetDps();
	ntf->combatPower	= owner->GetAttribute<AttributePlayer>()->combatPower;
	for (AbilityType type = 0; type < EAT::MAX_ABILITY_TYPE; ++type)
	{
	AbilityResult abilityResult;
	if (type == EAT::RUN_SPEED)
	{
	abilityResult.type = EAT::RUN_SPEED;
	abilityResult.value = speed;
	ntf->results.push_back(abilityResult);
	}
	else
	{
	auto value = owner->GetAction<ActionPlayerAbility>()->GetAbilityValue(static_cast<EAT::Enum>(type));
	if (value == 0)
	{
	continue;
	}
	abilityResult.type = type;
	abilityResult.value = value;
	ntf->results.push_back(abilityResult);
	}

	}
	SERVER.SendToClient(owner, EventPtr(ntf));*/

	theGameMsg.SendGameDebugMsg(owner, L"HackMoveSpeed(%f) changed", speed);

	return true;
}

const Bool GMCommandSystem::onCommandChangeHealth(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Int32 health = 0;
	cmd >> health;

	ActionAbility& actAbility = player->GetAbilityAction();

	ENtfGameChangeHealthnMana* ntf = NEW ENtfGameChangeHealthnMana;
	ntf->entityId = player->GetId();
	ntf->curHealth = actAbility.AddCurrentHp(health);
	ntf->curMana = actAbility.GetCurrentMp();
	ntf->maxHealth = actAbility.GetMaxHp();
	ntf->maxMana = actAbility.GetMaxMp();
	SERVER.SendToClient(player, EventPtr(ntf));

	return true;
}

const Bool GMCommandSystem::onCommandChangeMana(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Int32 mana = 0;
	cmd >> mana;

	ActionAbility& actAbility = player->GetAbilityAction();

	ENtfGameChangeHealthnMana* ntf = NEW ENtfGameChangeHealthnMana;
	ntf->entityId = player->GetId();
	ntf->curHealth = actAbility.GetCurrentHp();
	ntf->curMana = actAbility.AddCurrentMp(mana);
	ntf->maxHealth = actAbility.GetMaxHp();
	ntf->maxMana = actAbility.GetMaxMp();
	SERVER.SendToClient(player, EventPtr(ntf));

	return true;
}

//---------------------------------------------------------------------------------------------------------------

const Bool GMCommandSystem::onCommandChangeLevel(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	CharLevel level = 0;
	cmd >> level;
	if (level > Limits::MAX_LEVEL)
	{
		level = Limits::MAX_LEVEL;
	}

	const LevelInfoElem* levelInfoScript = SCRIPTS.GetLevelScript(level);
	VERIFY_RETURN(levelInfoScript, true);
	VERIFY_RETURN(SCRIPTS.GetPlayerAbilityScript(player->GetClassType(), level), true);

	player->SetLevel(level);
	player->SetExp(0);
	player->SetTotalExp(levelInfoScript->totalExp);
	player->OnCharLevelUp();

	return true;
}

//---------------------------------------------------------------------------------------------------------------



//---------------------------------------------------------------------------------------------------------------
const Bool GMCommandSystem::onCommandChangeForceLevel(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	CharLevel level = 0;
	cmd >> level;

	const LevelInfoElem* levelInfoScript = SCRIPTS.GetLevelScript(level);
	VERIFY_RETURN(levelInfoScript, true);
	VERIFY_RETURN(SCRIPTS.GetPlayerAbilityScript(player->GetClassType(), level), true);

	player->SetLevel(level);
	player->SetExp(0);
	player->SetTotalExp(levelInfoScript->totalExp);
	player->OnCharLevelUp();

#ifdef __Hotfix_SkillQuickSlotChange_by_kangms_180912
	auto actSkillQuickSlot = player->GetAction<ActionPlayerQuickSlot>();
	actSkillQuickSlot->RefreshSkillQuickSlotAllByNotLearnedSkill();
#endif//__Hotfix_SkillQuickSlotChange_by_kangms_180912

	return true;
}
//---------------------------------------------------------------------------------------------------------------
const Bool GMCommandSystem::onCommandChangeSoulLevel(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

#ifdef __Reincarnation__jason_180628__
	return player->GetCharSoulInfo().OnGmCommandChangeSoulLevel(cmd);
#else
	RawSoulLevel rawSoulLevel = 0;
	cmd >> rawSoulLevel;

	const SoulLevelInfoElem* soulLevelInfoElem = SCRIPTS.GetSoulLevelInfoElem(rawSoulLevel);
	VERIFY_RETURN(soulLevelInfoElem, false);

	player->SetRawSoulLevel(rawSoulLevel);
	player->SetSoulExp(0);
	player->SetTotalSoulExp(soulLevelInfoElem->totalSoulExp);
	player->GetSoulSkillManageAction().OnSoulLevelUp();

	return true;

#endif
}

//---------------------------------------------------------------------------------------------------------------

const Bool GMCommandSystem::onCommandAddExp(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 charExp = 0, soulExp = 0, petExp = 0;
	cmd >> charExp >> soulExp >> petExp;

	if (charExp > 0 || soulExp > 0 || petExp > 0)
	{
		player->GetPlayerAction().AddExp(charExp, soulExp, petExp);
	}
	return true;
}


//---------------------------------------------------------------------------------------------------------------

const Bool GMCommandSystem::onCommandAddMoney(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Zen money = 0;
	cmd >> money;

	if (player->CheckZen(money, ChangedMoney::ADD) == false)
	{
		SendSystemMsg(player, SystemMessage(L"sys", L"Msg_Zen_Err_Max"));
		return false;
	}

	player->AddMoney(money);
	player->SendChangeMoney(money, ChangedMoney::ADD);

	theGameMsg.SendGameDebugMsg(player, L"Add Money(%d)", money);

	return true;
}

//---------------------------------------------------------------------------------------------------------------

const Bool GMCommandSystem::onCommandSubMoney(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Zen money = 0;
	cmd >> money;

	player->SubMoney(money);
	player->SendChangeMoney(money, ChangedMoney::SUB);

	theGameMsg.SendGameDebugMsg(player, L"Sub Money(%d)", money);

	return true;
}
//---------------------------------------------------------------------------------------------------------------
const Bool GMCommandSystem::onCommandDead(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring targetName;
	cmd >> targetName;

	Sector* sector = player->GetSector();
	VALID_RETURN(sector, false);

	EntityPlayer* target = sector->FindPlayerByName(targetName);
	if (NULL == target)
	{
		theGameMsg.SendGameDebugMsg(player, L"Can't Find Player...Insert Correct Name..!!\n");
		return false;
	}

	player->GetAction< ActionAbility >()->SetCurrentHp(0);

	target->SetDead(true);
	target->GetHsm().Tran(EntityState::STATE_PLAYER_DEAD);
	return true;
}

const Bool GMCommandSystem::onCommandCureDeathPenalty(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	player->GetHsm().OnEvent(EventPtr(NEW EReqGameCureDeathPenalty));

	return true;
}

//---------------------------------------------------------------------------------------------------------------

const Bool GMCommandSystem::onCommandChatNormal(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring msg;
	cmd >> msg;

	EcwReqGameChatNormal* req = NEW EcwReqGameChatNormal;
	req->SetIdxBySessionKey(getSessionKey(player));
	req->msg = msg;
	req->languageCode = 0;
	SERVER.Notify(EventPtr(req));

	return true;
}

//---------------------------------------------------------------------------------------------------------------

const Bool GMCommandSystem::onCommandTransparent(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring onOff;
	cmd >> onOff;

	//ActionSector& sectorAction = player->GetSectorAction();

	if (L"on" == onOff)
	{
		//	sectorAction.SetTransparentMode();
	}
	else
	{
		//	sectorAction.SetTransparentMode(false);
	}
	return true;
}


//---------------------------------------------------------------------------------------------------------------
// blueadel 맵이동 (아직 다른 존으로 이동처리는 안된다.
//---------------------------------------------------------------------------------------------------------------
const Bool GMCommandSystem::onCommandWarp(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Float x, y;
	cmd >> x
		>> y;

	Vector3 curPos = player->GetCurrPos();
	ExecutionZoneId execZoneId = player->GetCurrExecId();

#if ENABLE_WHEN_WARP_PROTOCOL_DEVELOPED
	EReqAppZonePortal* reqToZone = NEW EReqAppZonePortal;
	reqToZone->entityId = player->GetId();
	viewEntity->GetSessionKey(reqToZone->sessionKey);
	reqToZone->index = 0;
	reqToZone->sectorId = execZoneId.indexZone;
	reqToZone->zoneId = execZoneId.indexZone;
	reqToZone->pos = Vector3(x, y, curPos.z);
	viewPos->GetDirToRadian(reqToZone->dir);
	SERVER.Notify(EventPtr(reqToZone));
#endif 

	return true;
}

//---------------------------------------------------------------------------------------------------------------
// blueadel NPC 디버그 명령어 모음
//---------------------------------------------------------------------------------------------------------------

const Bool GMCommandSystem::onCommandNpcTracking(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 Id;
	cmd >> Id;

	Sector *sector = player->GetSector();
	VALID_RETURN(sector, false);

	NpcDebugger* npcDebugger = sector->GetNpcDebug();
	VALID_RETURN(npcDebugger, false);

	npcDebugger->SetNpcId(player->GetId(), stEntityId(EntityTypes::NPC, Id));

	return true;
}

const Bool GMCommandSystem::onCommandNpcSummon(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexNpc npcIndex = 0;
	UInt32 count = 0;
	CharLevel level = 0;
	Float direction = 0.f;
	UInt32 straightMove = 0;;
	cmd >> npcIndex >> level >> count >> direction >> straightMove;

	if (count == 0)
	{
		count = 1;
	}

	if (level == 0)
	{
		level = 1;
	}

	Vector3 pos = player->GetCurrPos();
	Sector* sector = player->GetSector();
	VERIFY_RETURN(sector, true);

	UInt32 successCount = 0;
	UInt32 failCount = 0;

	for (UInt32 n = 0; n < count; n++)
	{
		if (false == theSpawnSystem.GetRandomPostion(pos, 0, 200, sector))
		{
			failCount++;
			continue;
		}

		const NpcInfoElem* script = SCRIPTS.GetNpcInfoScript(npcIndex);
		if (!script)
		{
			theGameMsg.SendGameDebugMsg(player, L"[NPC Summon] Can't find NpcIndex[%d] \n", npcIndex);
			return false;
		}



		EntityNpc* npc = NULL;
		SpawnSystem::BaseSpawnInfo baseSpawnInfo;
		baseSpawnInfo.npcIndex = npcIndex;
		baseSpawnInfo.level = level;
		baseSpawnInfo.sector = sector;
		baseSpawnInfo.spawnPos = pos;
		baseSpawnInfo.aiIndex = script->aiIndex;
		baseSpawnInfo.dir = direction;
		baseSpawnInfo.isStraightMove = (0 < straightMove) ? true : false;
		if (false == theSpawnSystem.SpawnGeneralNpc(&baseSpawnInfo, &npc, true))
		{
			failCount++;
			continue;
		}

		successCount++;
	}

	theGameMsg.SendGameDebugMsg(player, L"[NPC Summon][Try : %d][Success : %d][Fail : %d] \n", count, successCount, failCount);

	return true;
		}

const Bool GMCommandSystem::onCommandNpcSummonFollow(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexNpc npcIndex = 0;
	UInt32 count = 1;
	cmd >> npcIndex >> count;

	if (count == 0)
	{
		count = 1;
	}

	Vector3 pos = player->GetCurrPos();
	Sector* sector = player->GetSector();
	VALID_RETURN(sector, false);

	UInt32 successCount = 0;
	UInt32 failCount = 0;

	for (UInt32 n = 0; n < count; n++)
	{
		if (false == theSpawnSystem.GetRandomPostion(pos, 0, 200, sector))
		{
			failCount++;
			continue;
		}

		EntityNpc* npc = NULL;
		SpawnSystem::BaseSpawnInfo baseSpawnInfo;
		baseSpawnInfo.npcIndex = npcIndex;
		baseSpawnInfo.sector = sector;
		baseSpawnInfo.spawnPos = pos;

		baseSpawnInfo.aiIndex = SCRIPTS.GetNpcInfoScript(npcIndex)->aiIndex;
		baseSpawnInfo.type = SCRIPTS.GetNpcInfoScript(npcIndex)->type;
		baseSpawnInfo.eMyTeam = player->GetMyTeam();
		baseSpawnInfo.masterUnit = player;

		if (false == theSpawnSystem.SpawnGeneralNpc(&baseSpawnInfo, &npc))
		{
			failCount++;
			continue;
		}

		successCount++;
	}

	theGameMsg.SendGameDebugMsg(player, L"[Follow Summon][Try : %d][Success : %d][Fail : %d] \n", count, successCount, failCount);


	return true;
}

const Bool GMCommandSystem::onChangeMap(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	if (false == onChangeMap_(player, cmd))
	{
		SERVER.SendForwardError(player, ErrorJoin::CheatChangemapFailed);
	}
	else
	{
		SERVER.SendForwardError(player, ErrorJoin::CheatChangemapReceived);
	}
	return true;
}


const Bool GMCommandSystem::onChangeMap_(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexPosition toIndexPosition = 0;
	UInt32 layer = 0;
	// WorldId world = 0;
	DifficultyLevel difficulty = 0;

	cmd >> toIndexPosition >> layer >> difficulty;

	const PositionElem* pToPosElem = SCRIPTS.GetPositionElem(toIndexPosition);
	if (NULL == pToPosElem)
	{
		theGameMsg.SendGameDebugMsg(player, L"change map fail\n");
		VERIFY_RETURN(!"Position not found", false);
		return false;
	}

	IndexZone toIndexZone = pToPosElem->GetDestZoneIndex();

	const WorldElem* elem = SCRIPTS.GetWorldScript(toIndexZone);
	VERIFY_RETURN(elem, false);

	InstanceDungeonInfo dungeonInfo;
	dungeonInfo.SetDungeonType(elem->ToDungeonType());

	if (elem->IsInstance())
	{
		// 인스턴스 던전
		dungeonInfo.layer = layer;
		dungeonInfo.level = player->GetLevel();
		dungeonInfo.moveUpPositionIndex = toIndexPosition;
		dungeonInfo.indexZone = toIndexZone;
		if (0 == difficulty)
			dungeonInfo.dungeonDifficultyLevel = player->GetSector()->GetDifficulty();
		else
		{
			if (difficulty <= DungeonDifficultyLevelType::NONE)
				difficulty = 1;

			dungeonInfo.dungeonDifficultyLevel = difficulty;
		}
	}

	EzwReqChangeMap* reqToWorld = NEW EzwReqChangeMap;
	reqToWorld->toIndexPositionMovable = toIndexPosition;
	reqToWorld->toInsInfoList.push_back(dungeonInfo);

	return SERVER.SendChangemapToWorld(player, EzwReqChangeMap::GMCommandSystemonChangeMap_, EventPtr(reqToWorld));
}

const Bool GMCommandSystem::onChangePos(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Float x = 0.f, y = 0.f, z = 0.f;
	cmd >> x >> y >> z;

	Vector3 dstPos(x, y, z);
	Vector3 rightPos;

	ActionSector& actSector = player->GetSectorAction();
	if (!actSector.GetValidPosition(dstPos, rightPos))
	{
		theGameMsg.SendGameDebugMsg(player, L"Fail to changepos :%f %f %f", dstPos.x, dstPos.y, dstPos.z);
		return false;
	}

	if (actSector.Place(rightPos))
	{
		ENtfGameForceChangePos* ntf = NEW ENtfGameForceChangePos;
		ntf->entityId = player->GetId();
		ntf->stopPosition = rightPos;
		ntf->dir = 0;
		actSector.NearGridCast(EventPtr(ntf));

		theGameMsg.SendGameDebugMsg(player, L"Success to changepos :%f %f %f", rightPos.x, rightPos.y, rightPos.z);

		return true;
	}

	theGameMsg.SendGameDebugMsg(player, L"Fail to changepos :%f %f %f", dstPos.x, dstPos.y, dstPos.z);

	return false;
}

const Bool GMCommandSystem::onJumpTo(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexZone indexZone = 0;
	Float x = 0.f, y = 0.f, z = 0.f;
	cmd >> indexZone >> x >> y >> z;

	Vector3 dstPos(x, y, z);

	if ((indexZone == 0) || dstPos.IsZero())
	{
		theGameMsg.SendGameDebugMsg(player, L"onJumpTo Err - 똑띠 입력하라는.[z:%d][p-x:%f, y:%f, z:%f]\n",
			indexZone, dstPos.x, dstPos.y, dstPos.z);
		return false;
	}

	/*
	검증 코드 필요 - 잘못된 좌표를 보내면 클라가 맵을 무한로딩 하게 된다.
	1) 존아이디가 유효한지 검사...
	2) 좌표가 유효한지 검사...
	*/

	EzwReqGameJumpTo* req = NEW EzwReqGameJumpTo;
	req->toLocation.execId = ExecId(0, indexZone);
	req->toLocation.pos = dstPos;
	req->toLocation.indexPosition = ActionPlayerPortal::searchNearestPortalPositionIndex(indexZone, dstPos);
	SERVER.SendToClient(player, EventPtr(req));
	return true;
}

const Bool GMCommandSystem::onJumpToNpc(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexNpc npcIndex;
	cmd >> npcIndex;

	const NpcInfoElem* script = SCRIPTS.GetNpcInfoScript(npcIndex);
	if (script == nullptr)
	{
		theGameMsg.SendGameDebugMsg(player, L"onJumpToNpc Err - npc Index available\n");
		return false;
	}

	//ZoneHandler* zoneHandler = SERVER.GetZoneRunner();
	EntityNpc* foundNpc = theExecutionManager.FindNpcForTest([npcIndex](const SectorEntityMap::value_type& v) -> bool
	{
		EntityNpc* npc = static_cast<EntityNpc*>(v.second);
		VALID_RETURN(npc && npc->IsValid() && npc->IsAlive(), false);
		VALID_RETURN(npc->GetNpcIndex() == npcIndex, false);
		return true;
	});

	if (foundNpc == nullptr)
	{
		theGameMsg.SendGameDebugMsg(player, L"onJumpToNpc Err - no npc exists\n");
		return false;
	}


	EzwReqGameJumpTo* req = NEW EzwReqGameJumpTo;
	EventPtr reqPtr(req);
	{
		req->toLocation.execId = foundNpc->GetCurrExecId();
		req->toLocation.pos = foundNpc->GetCurrPos();
		req->toLocation.indexPosition = ActionPlayerPortal::searchNearestPortalPositionIndex(foundNpc->GetCurrZoneIndex(), foundNpc->GetCurrPos());
		SERVER.SendToClient(player, reqPtr);
	}

	return true;
}

const Bool GMCommandSystem::onCall(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring targetName;
	cmd >> targetName;

	VALID_RETURN(targetName.size(), false);

	Sector* sector = player->GetSector();
	VALID_RETURN(sector, false);

	EntityPlayer* target = sector->FindPlayerByName(targetName);
	VALID_DO(target, theGameMsg.SendGameDebugMsg(player, L"[E][ %s is not my sector", targetName.c_str());
	return false;);

	VALID_DO(target->IsAlive(), theGameMsg.SendGameDebugMsg(player, L"[E][ %s is dead", targetName.c_str());
	return false;);

	Vector3 dstPos = player->GetCurrPos();

	if (target->GetSectorAction().Place(dstPos))
	{
		ENtfGameSyncMoveStop* ntf = NEW ENtfGameSyncMoveStop;
		ntf->entityId = player->GetId();
		ntf->stopPosition = dstPos;
		player->GetSectorAction().NearGridCast(EventPtr(ntf));

		ENtfGameSyncMoveStop* ntfTarget = NEW ENtfGameSyncMoveStop;
		ntfTarget->entityId = target->GetId();
		ntfTarget->stopPosition = dstPos;
		target->GetSectorAction().NearGridCast(EventPtr(ntfTarget));

		theGameMsg.SendGameDebugMsg(player, L"Success to call %s :%f %f %f", targetName.c_str(), dstPos.x, dstPos.y, dstPos.z);
		theGameMsg.SendGameDebugMsg(target, L"Success to call %s :%f %f %f", targetName.c_str(), dstPos.x, dstPos.y, dstPos.z);

		return true;
	}

	theGameMsg.SendGameDebugMsg(player, L"Fail to call %s", targetName.c_str());
	theGameMsg.SendGameDebugMsg(target, L"Fail to call %s", targetName.c_str());

	return false;
}
const Bool GMCommandSystem::onCommandClearBag(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	player->GetInventoryAction().ClearBagByGMCommand();
	return true;
}

const Bool GMCommandSystem::onCommandMyPos(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Vector3 pos = player->GetCurrPos();

	theGameMsg.SendGameDebugMsg(player, L"mypos:%f %f %f", pos.x, pos.y, pos.z);

	return false;
}

#ifdef __ItemEnchant_Renew_Jason_180508
const Bool GMCommandSystem::onCommandSetEnchantResult(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	/// Desc: /setenchant [아이템 인첸트 결과 강제 설정][S:on/off][S:success/fail]
	std::wstring onOff, result;
	cmd >> onOff >> result;

	return player->GetItemEnchant().SetCheat(onOff, result);
}
#else
const Bool GMCommandSystem::onCommandSetEnchantResult(EntityPlayer* player, const CommandString& cmd)
{
	/// Desc: /setenchant [아이템 인첸트 결과 강제 설정][S:on/off][S:success/fail]
	std::wstring onOff, result;
	cmd >> onOff >> result;

	if (L"on" == onOff)
	{
		if (L"success" == result)
		{
			theGameMsg.SendGameDebugMsg(player, L"무조건 강화 성공\n");
			ItemFactory::enchantState = ItemFactory::ForceEnchantResult::FORCED_SUCCESS;
		}
		else if (L"fail" == result)
		{
			theGameMsg.SendGameDebugMsg(player, L"무조건 강화 실패\n");
			ItemFactory::enchantState = ItemFactory::ForceEnchantResult::FORCED_FAILURE;
		}
	}
	else if (L"off" == onOff)
	{
		theGameMsg.SendGameDebugMsg(player, L"강화 결과 설정 해제\n");
		ItemFactory::enchantState = ItemFactory::ForceEnchantResult::NORMAL;
	}
	else
	{
		return false;
	}

	return true;
}
#endif

const Bool GMCommandSystem::onCommandMaxDamage(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring onOff;
	cmd >> onOff;

	ActionPlayerAbility& actPlayerAbility = player->GetAbilityAction();
	if (onOff == L"on")
	{
		theGameMsg.SendGameDebugMsg(player, L"max damage on \n");
		actPlayerAbility.FixMaxDamage(true);
	}
	else
	{
		theGameMsg.SendGameDebugMsg(player, L"max damage off\n");
		actPlayerAbility.FixMaxDamage(false);
	}

	return true;
}

const Bool GMCommandSystem::onCommandMaxDamageByNpcIndex(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexNpc npcIndex;
	cmd >> npcIndex;

	ActionPlayerAbility& actPlayerAbility = player->GetAbilityAction();

	if (actPlayerAbility.DevFindFixMaxDamageByNpcIndexs(npcIndex) == false)
	{
		actPlayerAbility.DevFixMaxDamageByNpcIndex(npcIndex);
		theGameMsg.SendGameDebugMsg(player, L"max damage on npc(%d)\n", npcIndex);
	}
	else
	{
		actPlayerAbility.DevRemoveFixMaxDamageByNpcIndexs(npcIndex);
		theGameMsg.SendGameDebugMsg(player, L"max damage off npc(%d)\n", npcIndex);
	}
	return true;
}

const Bool GMCommandSystem::onCommandInvincible(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring onOff;
	cmd >> onOff;

	ActionSkillControl& actSkillControl = player->GetSkillControlAction();
	if (onOff == L"on")
	{
		theGameMsg.SendGameDebugMsg(player, L"superman on \n");
		actSkillControl.AddImmune(ImmuneType::ALL);
	}
	else
	{
		theGameMsg.SendGameDebugMsg(player, L"superman off\n");
		if (false == actSkillControl.DelImmune(ImmuneType::ALL))
		{
			MU2_WARN_LOG(LogCategory::CONTENTS, "[%s]{ fail to DelImmune ][ immuneType : %d ]", __FUNCTION__, ImmuneType::ALL);
		}
	}

	return true;
}

const Bool GMCommandSystem::onCommandStopRecovery(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring onOff;
	cmd >> onOff;

	ActionPlayerAbility& actPlayerAbility = player->GetAbilityAction();
	if (onOff == L"on")
	{
		theGameMsg.SendGameDebugMsg(player, L"recovery on \n");
		actPlayerAbility.SetStopRecovery(false);
	}
	else
	{
		theGameMsg.SendGameDebugMsg(player, L"recovery off\n");
		actPlayerAbility.SetStopRecovery(true);
	}

	return true;
}

const Bool GMCommandSystem::onCommandHide(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring onOff;
	cmd >> onOff;

	IndexSkill skillId = SCRIPTS.GetCommonSkillConfigScript().cheatHide;

	if (onOff == L"on")
	{
		theGameMsg.SendGameDebugMsg(player, L"hide on \n");
		theLogicEffectSystem.OnSingleCast(player, skillId);
	}
	else
	{
		theGameMsg.SendGameDebugMsg(player, L"hide off\n");
		theLogicEffectSystem.OnCancelBuffCast(player, skillId);
	}

	return true;
	}

const Bool GMCommandSystem::onCommandRecognize(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring onOff;
	cmd >> onOff;

	IndexSkill skillId = SCRIPTS.GetCommonSkillConfigScript().recognize;

	if (onOff == L"on")
	{
		theGameMsg.SendGameDebugMsg(player, L"recognize on \n");
		theLogicEffectSystem.OnSingleCast(player, skillId);
	}
	else
	{
		theGameMsg.SendGameDebugMsg(player, L"recognize off\n");
		theLogicEffectSystem.OnCancelBuffCast(player, skillId);
	}

	return true;
}

const Bool GMCommandSystem::onCommandDropIndex(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 dropIndex;
	cmd >> dropIndex;

	theDropSystem.DropFromIndexNumber(player, dropIndex);
	theGameMsg.SendGameDebugMsg(player, L"Drop [Index:%d] \n", dropIndex);

	return true;
}

const Bool GMCommandSystem::onCommandDropCount(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring onOff;
	UInt32 dropCount = 0;
	cmd >> onOff >> dropCount;

	if (onOff == L"on")
	{
		theGameMsg.SendGameDebugMsg(player, L"Set Drop Count on [count:%d] \n", dropCount);
		theDropSystem.SetDropCountOn(true, dropCount);
	}
	else
	{
		theGameMsg.SendGameDebugMsg(player, L"Set Drop Count off\n");
		theDropSystem.SetDropCountOn(false, dropCount);
	}

	return true;
}

const Bool GMCommandSystem::onRewardQuest(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 questId;
	UInt32 selectItem;
	cmd >> questId >> selectItem;

	EReqQuestReward* req = NEW EReqQuestReward;
	req->questId = questId;
	req->rewardIndex = selectItem;
	player->OnEvent(EventPtr(req));

	return true;

}

const Bool GMCommandSystem::onGiveupquest(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 questId;
	cmd >> questId;

	player->GetQuestAction().TestGiveUpQuest(questId);

	return true;

}

const Bool GMCommandSystem::onQuestRenewal(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);
	player->GetQuestAction().RenwalQuestForTest();

	return true;
}

const Bool GMCommandSystem::onMeetNpc(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexNpc npcId;
	cmd >> npcId;

	theQuestSystem.OnDialogNpc(player, npcId);
	return true;
}

const Bool GMCommandSystem::onQuestAccept(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 questId;
	cmd >> questId;

	const QuestElem* elem = SCRIPTS.GetQuest(questId);
	VALID_RETURN(elem, false);

	ActionPlayerQuest& actionQuest = player->GetQuestAction();
	Int32 err = actionQuest.TestAcceptQuest(questId);
	if (err != ErrorQuest::SUCCESS)
	{
		actionQuest.SendQuestError(player, err);
	}

	return true;
}

const Bool GMCommandSystem::onQuestFinish(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 questId;
	cmd >> questId;

	const QuestElem* elem = SCRIPTS.GetQuest(questId);
	VALID_RETURN(elem, false);

	ActionPlayerQuest& actionQuest = player->GetQuestAction();
	Int32 err = actionQuest.TestFinishQuest(questId);
	if (err != ErrorQuest::SUCCESS)
	{
		actionQuest.SendQuestError(player, err);
	}

	return true;
}
const Bool GMCommandSystem::onQuestComplete(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);
	UInt32 questId;
	cmd >> questId;

	// 퀘스트 미션 모두 완료
	const QuestElem* elem = SCRIPTS.GetQuest(questId);
	VALID_RETURN(elem, false);

	ActionPlayerQuest& actQuest = player->GetQuestAction();
	Int32 err = actQuest.TestCompleteQuest(questId);
	if (err != ErrorQuest::SUCCESS)
	{
		actQuest.SendQuestError(player, err);
	}

	return true;
}

const Bool GMCommandSystem::onQuestFinishAll(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);
	player->GetQuestAction().TestCompleteQuestAll();
	return true;
}

//const Bool GMCommandSystem::onQuestShare( EntityPlayer* player, const CommandString& cmd )
//{
//	UInt64 entityId;
//	UInt32 questId;
//
//	cmd >> questId >> entityId;
//	
//	EReqQuestShare* req = NEW EReqQuestShare;
//	req->entityId = entityId;
//	req->questId = questId;
//	player->OnEvent( EventPtr( req ) );
//
//	return true;
//}

//const Bool GMCommandSystem::onQuestShareResult( EntityPlayer* player, const CommandString& cmd )
//{
//	UInt32 questId, result;
//	UInt64 entityId;
//	
//	cmd >> questId >> entityId >> result;
//
//	EResQuestShareResult* res = NEW EResQuestShareResult;
//	res->questId = questId;
//	res->entityId = entityId;
//	res->result = result;	
//	player->OnEvent( EventPtr( res ) );
//
//	return true;
//}

const Bool GMCommandSystem::onQuestVolume(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 volumeId;
	cmd >> volumeId;

	EReqQuestVolumeCheck* req = NEW EReqQuestVolumeCheck;
	req->volumeId = volumeId;
	player->OnEvent(EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onQuestUseItem(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexItem itemIndex;
	cmd >> itemIndex;

	theQuestSystem.OnUseItem(player, itemIndex, 1);

	return true;
}

const Bool GMCommandSystem::onQuestRequest(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexQuest questId;
	RequestQuestFlag::Enum requestType = RequestQuestFlag::Enum::REQUEST;

	cmd >> questId >> (Byte&)requestType;

	EReqQuest* req = NEW EReqQuest;
	req->questId = questId;
	req->type = requestType;

	player->OnEvent(EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onQuestRefuse(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexQuest questId;
	cmd >> questId;

	EReqQuest* req = NEW EReqQuest;
	req->questId = questId;
	req->type = RequestQuestFlag::REFUSE;

	player->OnEvent(EventPtr(req));

	return true;

}

const Bool GMCommandSystem::onQuestFail(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexQuest questId;
	cmd >> questId;

	EReqQuest* req = NEW EReqQuest;
	req->questId = questId;
	req->type = RequestQuestFlag::FAILED;

	player->OnEvent(EventPtr(req));

	return true;

}

// ADD 가상 실험
const Bool GMCommandSystem::onQuestVirtualTestKillMonster(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 mobId;
	cmd >> mobId;

	// 이건 카운트 테스트는 되었으나 전체적으로 문제가 있다. 
	theGameMsg.SendGameDebugMsg(player, L"더이상 지원하지 않습니다. 다른 방법 찾는 중.");

	return true;
}


const Bool GMCommandSystem::onQuestVirtualTestTimeout(EntityPlayer* player, const CommandString &cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexQuest questId;
	cmd >> questId;

	ActionPlayerQuest& act = player->GetQuestAction();

	act.GiveUpQuest(questId);
	act.SendFailQuest(player, questId);
	act.NotifyHeroMissionState(questId);

	return true;
}

const Bool GMCommandSystem::onRegistFootHold(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexPosition positionIndex;
	cmd >> positionIndex;

	const PositionElem* foundElem = SCRIPTS.GetFoothold(positionIndex);
	VALID_DO(foundElem, theGameMsg.SendGameDebugMsg(player, L"등록된 거점이 없습니다\n");
	return false;);

	Vector3 dest(foundElem->pos);
	Vector3 curPos = player->GetCurrPos();
	Float diff = (dest - curPos).Length();

	if (diff <= foundElem->radius)
	{
		player->SetFootholdAtChannelAndSaveDb(positionIndex);
		theGameMsg.SendGameDebugMsg(player, L"거점 정상 저장 완료 mypos:%f %f %f\n", curPos.x, curPos.y, curPos.z);
	}
	else
	{
		theGameMsg.SendGameDebugMsg(player, L"거점 저장 실패 Out of Range mypos:%f %f %f\n", curPos.x, curPos.y, curPos.z);
	}
	return true;
}

const Bool GMCommandSystem::onQuestReset(EntityPlayer* player, const CommandString &cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexQuest questId;
	cmd >> questId;

	player->GetQuestAction().TestResetQuest(questId);

	return true;
}

const Bool GMCommandSystem::onQuestReserve(EntityPlayer* player, const CommandString &cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexQuest questId;
	cmd >> questId;

	player->GetQuestAction().ReserveQuest(questId);

	return true;
}

const Bool GMCommandSystem::onRevive(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring name;
	cmd >> name;

	Sector* sector = player->GetSector();
	VALID_RETURN(sector, false);

	ELoopbackGameReviveCheat* req = NEW ELoopbackGameReviveCheat;
	EventPtr reqPtr(req);
	if (name.length() > 0)
	{
		EntityPlayer* targetUser = sector->FindPlayerByName(name);
		VALID_RETURN(targetUser, false);
		targetUser->OnEvent(reqPtr);
	}
	else
	{
		player->OnEvent(reqPtr);
	}

	return true;
}

const Bool GMCommandSystem::onUseItem(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 targetId;	
	Int32 slot;
	cmd >> targetId >> slot;

	ItemPos itemPos(InvenType::BAG_1ST, slot);
	Vector3 pos = player->GetCurrPos();

	UseItem::InputValue ivalue(player);
	ivalue.SetSourceItemPos(itemPos);
	ivalue.SetTarget(stEntityId(EntityTypes::PLAYER_ZONE, targetId));
	ivalue.targetPos = pos;

	player->GetUseItemAction().Use(ivalue);

	return true;
}

const Bool GMCommandSystem::onCastingCancel(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	EReqCastingCancel* req = NEW EReqCastingCancel;
	req->itemId = 0;

	player->OnEvent(EventPtr(req));

	return true;
}

// 다음에 드랍될 아이템부터 적용.
const Bool GMCommandSystem::onReloadCommonConfig(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	if (!SERVER.ReloadCommonConfig())
	{
		theGameMsg.SendGameDebugMsg(player, L"common.cfg 파일 다시 읽기 실패. 함바바\n");
		return false;
	}

	theGameMsg.SendGameDebugMsg(player, L"common.cfg 파일 다시 읽었음.\n");

	return true;
}

const Bool GMCommandSystem::onOpenPortal(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexNpc npcIndex = 0;
	cmd >> npcIndex;

	if (!thePortalSystem.CheckDistance(player, npcIndex))
	{
		theGameMsg.SendGameDebugMsg(player, L"onOpenPortal failed! because out of range \n");
		return false;
	}

	Sector* sector = player->GetSector();
	VERIFY_RETURN(sector, false);

#ifdef PORTAL_NEED_QUEST_by_jjangmo_180521
	IndexQuest failQuest = 0;
	Int32 ret = thePortalSystem.Request(player, npcIndex, 1, sector->GetDifficulty(), false, failQuest);
#else
	Int32 ret = thePortalSystem.Request(player, npcIndex, 1, sector->GetDifficulty(), false);
#endif
	if (ret != 0)
	{
		theGameMsg.SendGameDebugMsg(player, L"onOpenPortal failed! / error:%d\n", ret);
	}
	else
	{
		theGameMsg.SendGameDebugMsg(player, L"onOpenPortal success!\n");
	}

	return true;
}

const Bool GMCommandSystem::onBeginWarp(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	player->GetPortalAction().WarpBeginPortal();
	return true;
}

const Bool GMCommandSystem::onPlaceBoxObstacle(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	//player->GetSectorAction().PlaceBoxObstacle();

	return true;
}

const Bool GMCommandSystem::onRemoveBoxObstacle(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 input = 0;
	cmd >> input;

	Bool IsRemoveAll;
	if (input > 0)
	{
		IsRemoveAll = true;
	}
	else
	{
		IsRemoveAll = false;
	}

	//player->GetSectorAction().RemoveBoxObstacle(IsRemoveAll);

	return true;
}

const Bool GMCommandSystem::onPlaceTagVolume(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 tagVolumeIndex = 0;
	cmd >> tagVolumeIndex;

	Sector* sector = player->GetSector();
	VALID_RETURN(sector, false);

	return theSpawnSystem.SpawnDynamicTagVolumeByWorldTagVolumeScript(sector, tagVolumeIndex);

}

const Bool GMCommandSystem::onRemoveTagVolume(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 tagVolumeIndex = 0;
	cmd >> tagVolumeIndex;

	Sector* sector = player->GetSector();
	VALID_RETURN(sector, false);

	return theSpawnSystem.DespawnDynamicTagVolume(sector, tagVolumeIndex);
}

const Bool GMCommandSystem::onGetTagValue(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 tagValue = player->GetSectorAction().GetNavTagValueOnNavTriangle();

	theGameMsg.SendGameDebugMsg(player, L" [Tag Value : %u] \n", tagValue);

	return true;
}

const Bool GMCommandSystem::onSyncMailList(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	EczReqGameSyncMailList* req = NEW EczReqGameSyncMailList;
	player->OnEvent(EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onOpenMail(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	MailId id;
	cmd >> id;

	EReqGameOpenMail* req = NEW EReqGameOpenMail;
	req->mailId = id;
	player->OnEvent(EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onGetMailItem(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	MailId id;
	cmd >> id;

	EReqGameGetMailItems* req = NEW EReqGameGetMailItems;
	req->mailId = id;
	player->OnEvent(EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onGetMailMoney(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	MailId id;
	cmd >> id;

	EReqGameGetMailMoney* req = NEW EReqGameGetMailMoney;
	req->mailId = id;
	player->OnEvent(EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onDeleteMail(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	MailId id;
	cmd >> id;

	EReqGameDelMail* req = NEW EReqGameDelMail;
#ifdef __Patch_Mail_HwaYeong_20190117
	req->removeMailIds.emplace( id );
#else
	req->mailId = id;
#endif
	player->OnEvent(EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onReturnMail(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt64 id;
	cmd >> id;

	ActionPlayerMailBox& actMail = player->GetMailBoxAction();

	Mail* mail = actMail.GetMail(id);
	VALID_RETURN(mail, false);
	MailData mailData = mail->GetMailData();
	actMail.ReturnMail(mailData);

	return true;
}

const Bool GMCommandSystem::onGetMailList(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	MailId id;
	cmd >> id;
	EReqGameGetMailList* req = NEW EReqGameGetMailList;
	req->lastMailId = id;
	req->mailBoxType = MailBoxType::INBOX;
	player->OnEvent(EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onSendMail(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring name = L"", content = L"", subject = L"", senderName = L"";
	UInt32 count = 1, system = 0;
	cmd >> name >> count >> subject >> content >> system >> senderName;

	MailData mail;
	mail.recverName = name;

	if (system == 2)
	{
		mail.senderName = L"Msg_Mailbox_10VS10_ITEM_SENDER";
		mail.subject = L"Msg_Mailbox_10VS10_ITEM_SUBJECT";
		mail.content = L"Msg_Mailbox_10VS10_ITEM_CONTENT";

		SimpleItem simpleItem;
		simpleItem.itemIndex = 70002;
		simpleItem.itemCount = 200;
		SimpleItems simpleItems;
		simpleItems.push_back(simpleItem);

		player->GetMailBoxAction().SendSystemMail(mail, simpleItems);

		return true;
	}
	else if (system == 3)
	{
		mail.senderName = L"Msg_Mailbox_10VS10_ITEM_SENDER";
		mail.subject = L"Msg_Mailbox_10VS10_ITEM_SUBJECT";
		mail.content = L"Msg_Mailbox_10VS10_ITEM_CONTENT";

		SimpleItem simpleItem;
		simpleItem.itemIndex = ********;
		simpleItem.itemCount = 2;
		SimpleItems simpleItems;
		simpleItems.push_back(simpleItem);

		player->GetMailBoxAction().SendSystemMail(mail, simpleItems);

		return true;
	}
	else if (system == 4)		// 아이템 있는 계정 시스템 메일
	{
		mail.senderName = L"Msg_Mailbox_10VS10_ITEM_SENDER";
		mail.subject = L"Msg_Mailbox_10VS10_ITEM_SUBJECT";
		mail.content = L"Msg_Mailbox_10VS10_ITEM_CONTENT";

		mail.recverName = player->GetCharName();
		mail.accMailAccId = player->GetAccountId();

		SimpleItem simpleItem;
		simpleItem.itemIndex = 60000;
		simpleItem.itemCount = 1;
		SimpleItems simpleItems;
		simpleItems.push_back(simpleItem);

		player->GetMailBoxAction().SendSystemMail(mail, simpleItems);

		return true;
	}
	else if (system == 5)		// 아이템 없는 계정 시스템 메일
	{
		mail.senderName = L"Msg_Mailbox_10VS10_ITEM_SENDER";
		mail.subject = L"Msg_Mailbox_10VS10_ITEM_SUBJECT";
		mail.content = L"Msg_Mailbox_10VS10_ITEM_CONTENT";
		mail.recverName = player->GetCharName();
		mail.accMailAccId = player->GetAccountId();

		SimpleItems simpleItems;
		player->GetMailBoxAction().SendSystemMail(mail, simpleItems);

		return true;
	}
	else if (system > 0)
	{
		mail.type = MailType::SYSTEM;
		mail.senderName = senderName;
		mail.subject = subject;
		mail.content = content;
	}
	else
	{
		mail.type = MailType::NORMAL;
		mail.subject = subject;
		mail.content = content;
	}

	player->GetMailBoxAction().SendMailPreProcess(mail, count);

	return true;
}


const Bool GMCommandSystem::onChangeMailDate(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 day = 0, hour = 0, min = 0;
	cmd >> day >> hour >> min;

	VALID_RETURN(day <= 31 && hour <= 24 && min <= 60, false);

	DateTimeSpan time;
	time.SetHighTimeSpan(day, hour, min, 0);
	player->GetMailBoxAction().SetAdjustedTime(time);

	SYSTEMTIME systime = player->GetMailBoxAction().GetAdjustedSysTime();
	theGameMsg.SendGameDebugMsg(player, L"날짜 변경 [%d년:%d월:%d일:%d시:%d분]\n",
		systime.wYear, systime.wMonth, systime.wDay, systime.wHour, systime.wMinute);
	return true;
}


const Bool GMCommandSystem::onReqSuggestTrade(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring charName;
	cmd >> charName;

	EReqSuggestTrade* req = NEW EReqSuggestTrade;
	req->inviteeName = charName;
	player->OnEvent(EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onReqAnswerTrade(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 inviter = 0;
	cmd >> inviter;

	EReqAnswerTradeSuggested* req = NEW EReqAnswerTradeSuggested;
	req->inviter = inviter;
	req->response = true;
	player->OnEvent(EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onReqChangeTradeInfo(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Zen money = 0;
	ItemPos itemPos;

	cmd >> money >> itemPos;

	auto act = player->GetAction<ActionPlayerInventory>();
	std::set< ItemSlotInfo > infos;

	const ItemConstPtr item = act->GetItem(itemPos);
	VERIFY_RETURN(item, false);
	infos.insert(item->GetSlotInfo());

	EReqChangeTradeInfo* req = NEW EReqChangeTradeInfo;
	req->money = money;
	req->items = infos;
	player->OnEvent(EventPtr(req));
	return true;
}

const Bool GMCommandSystem::onReqCancelTrade(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	EReqCancelTrade* req = NEW EReqCancelTrade;
	player->OnEvent(EventPtr(req));
	return true;
}


const Bool GMCommandSystem::onReqStartTrade(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	EReqStartTrade* req = NEW EReqStartTrade;
	player->OnEvent(EventPtr(req));
	return true;
}


const Bool GMCommandSystem::onReqReadyTrade(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	EReqReadyTrade* req = NEW EReqReadyTrade;
	req->ready = true;
	player->OnEvent(EventPtr(req));
	return true;
}

const Bool GMCommandSystem::onSystemMessage(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring str;

	cmd >> str;

#ifdef __Reincarnation__jason_180628__
	theGameMsg.SendSystemMessage(player, SystemMessage(L"sys", str));
#else
	ENtfGameSystemMessage* ntf = NEW ENtfGameSystemMessage;
	SystemMessage msg(L"sys", str);
	ntf->msg = msg.GetString();
	SERVER.SendToClient(player, EventPtr(ntf));
#endif

	return true;
}

const Bool GMCommandSystem::onReqSearchMonster(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring str;
	cmd >> str;

	player->GetSectorAction().SearchNearMonsterForTest();

	return true;
}

const Bool GMCommandSystem::onItemExtract(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	ItemPos pos;
	cmd >> pos;

	EReqItemExtract* req = NEW EReqItemExtract;
	req->list.insert(pos);
	player->OnEvent(EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onBuyStorageBag(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 slotCnt = 0, slotMaxCnt = 0, period = 0;
	UInt32 unlimited = 0;
	cmd >> slotCnt >> slotMaxCnt >> period >> unlimited;

	EReqBuyStorageBag* req = NEW EReqBuyStorageBag;
	req->slotCnt = slotCnt;
	req->slotMaxCnt = Limits::MAX_STORAGE_TAB_COUNT_PER_INVEN;
	req->period = 0;
	req->unlimited = true;
	player->OnEvent(EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onExpandSlot(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 storageIdx = 0;
	cmd >> storageIdx;

	StatePlayerBase* pBase = dynamic_cast<StatePlayerBase*>(player->GetHsm().GetState(EntityState::STATE_PLAYER_BASE));
	VERIFY_RETURN(pBase, false);

	EReqExpandStorageTab *req = NEW EReqExpandStorageTab();
	EventPtr eventPtr(req);

	ErrorItem::Error eError = pBase->reqExpandStorageTab(req);
	if (eError != ErrorItem::SUCCESS)
	{
		EResExpandStorageSlot* res = NEW EResExpandStorageSlot;
		res->result = eError;
		SERVER.SendToClient(player, EventPtr(res));
		theGameMsg.SendGameDebugMsg(player, L"슬롯확장 실패:%d\n", eError);
		return false;
	}

	return true;
}

const Bool GMCommandSystem::onExpandPeriod(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Byte storageIdx = 0; UInt32 period = 0;
	cmd >> storageIdx >> period;

	EReqExpandStoragePeriod* req = NEW EReqExpandStoragePeriod;
	req->eInven = InvenStorageAccount::CastBagIndexToInvenType(storageIdx);
	req->period = period;
	player->OnEvent(EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onMakingBigHit(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring flag;
	cmd >> flag;

	ActionPlayerMaking* actMaking = GetEntityAction(player);
	if (flag == L"on" || flag == L"켬")
	{
		actMaking->CheatMakingSetBigHit(true);
		theGameMsg.SendGameDebugMsg(player, L"Making BigHit On.!");
	}
	else if (flag == L"off" || flag == L"끔")
	{
		actMaking->CheatMakingSetBigHit(false);
		theGameMsg.SendGameDebugMsg(player, L"Making BigHit Off.!");
	}
	else
	{
		theGameMsg.SendGameDebugMsg(player, L"invaild parameter ( Params:on/off/켬/끔 )");
	}

	return true;
}

const Bool GMCommandSystem::onMaking(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring command;
	UInt32 param1, param2;
	cmd >> command >> param1 >> param2;

	if (command == L"cast")
	{
		EReqGameMakingCastStart* req = NEW EReqGameMakingCastStart;
		req->formulaIndex = param1;
		player->OnEvent(EventPtr(req));
	}
	else if (command == L"cancel")
	{
		EReqGameMakingCastCancel* req = NEW EReqGameMakingCastCancel;
		player->OnEvent(EventPtr(req));
	}
	else if (command == L"make")
	{
		EReqGameMaking* req = NEW EReqGameMaking;
		req->formulaIndex = param1;
		player->OnEvent(EventPtr(req));
	}
	/*else if( command == L"choice" )
	{
	EReqGameMakingJobChoice* req = NEW EReqGameMakingJobChoice;
	req->jobIndex = static_cast<Byte>(param1);
	player->OnEvent( EventPtr( req ) );
	}
	else if( command == L"abandon" )
	{
	EReqGameMakingJobAbandon* req = NEW EReqGameMakingJobAbandon;
	req->jobIndex = static_cast<Byte>(param1);
	player->OnEvent( EventPtr( req ) );
	}
	else if( command == L"upgrade" )
	{
	EReqGameMakingJobUpgrade* req = NEW EReqGameMakingJobUpgrade;
	req->jobIndex = static_cast<Byte>(param1);
	req->jobGrade = static_cast<Byte>(param2);
	player->OnEvent( EventPtr( req ) );
	}*/
	else if (command == L"add")
	{
		EReqGameMakingFormulaAdd* req = NEW EReqGameMakingFormulaAdd;
		req->itemPos = ItemPos(param1, param2);
		player->OnEvent(EventPtr(req));
	}
	else if (command == L"showmsg")
	{
		ActionPlayerMaking* actMaking = GetEntityAction(player);
		if (actMaking == nullptr)
		{
			theGameMsg.SendGameDebugMsg(player, L"\nInvalid ActionPlayerMaking object.!");
		}
		else if (actMaking->IsDebugMsg())
		{
			actMaking->SetDebugMsg(false);
			theGameMsg.SendGameDebugMsg(player, L"\nDebugging message off.");
		}
		else
		{
			actMaking->SetDebugMsg(true);
			theGameMsg.SendGameDebugMsg(player, L"\nDebugging message on.");
		}
	}
	else if (command == L"showlist")
	{
		ActionPlayerMaking* actMaking = GetEntityAction(player);
		if (actMaking == nullptr)
		{
			theGameMsg.SendGameDebugMsg(player, L"\nInvalid ActionPlayerMaking object.!");
		}
		else
		{
			actMaking->DebugShowFormulaList();
		}
	}
	else if (command == L"show")
	{
		ActionPlayerMaking* actMaking = GetEntityAction(player);
		if (actMaking == nullptr)
		{
			theGameMsg.SendGameDebugMsg(player, L"\nInvalid ActionPlayerMaking object.!");
		}
		else
		{
			actMaking->DebugShowMaterials(param1);
		}
	}
	else
	{
		theGameMsg.SendGameDebugMsg(player, L"\n/making command list -");
		theGameMsg.SendGameDebugMsg(player, L"\n/making showmsg - debugging message on/off.");
		theGameMsg.SendGameDebugMsg(player, L"\n/making showjob - show my job list");
		theGameMsg.SendGameDebugMsg(player, L"\n/making showlist - show my formula list");
		theGameMsg.SendGameDebugMsg(player, L"\n/making show [FORMULA] - show formula script info");
		theGameMsg.SendGameDebugMsg(player, L"\n/making cast [FORMULA]");
		theGameMsg.SendGameDebugMsg(player, L"\n/making cancel");
		theGameMsg.SendGameDebugMsg(player, L"\n/making make");
		theGameMsg.SendGameDebugMsg(player, L"\n/making choice [JOB]");
		theGameMsg.SendGameDebugMsg(player, L"\n/making abandon [JOB]");
		theGameMsg.SendGameDebugMsg(player, L"\n/making upgrade [JOB][GRADE]");
		theGameMsg.SendGameDebugMsg(player, L"\n/making add [INVENTORY][SLOT]");
		theGameMsg.SendGameDebugMsg(player, L"\nRange JOB( 1 ~ 8 ), GRADE(1~5)");
	}
	return true;
}

const Bool GMCommandSystem::onSectorLuaScriptSetValue(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring key;
	UInt32 value = 0;
	cmd >> key >> value;

	VALID_RETURN(key.size(), false);

	Sector* sector = player->GetSector();
	VALID_RETURN(sector, false);

	std::string keystr;
	keystr.assign(key.begin(), key.end());
	sector->GetSectorController().GetControllerAction().SetKeyValue(keystr.c_str(), value);

	Int32 resultValue = sector->GetSectorController().GetControllerAction().GetKeyValue(keystr.c_str());

	theGameMsg.SendGameDebugMsg(player, L"SectorLuaScript Key: %s, Value: %d", key.c_str(), resultValue);

	return true;
}

const Bool GMCommandSystem::onSectorLuaScriptGetValue(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring key;
	cmd >> key;

	VALID_RETURN(key.size(), false);

	std::string keystr;
	keystr.assign(key.begin(), key.end());

	Sector* sector = player->GetSector();
	VALID_RETURN(sector, false);

	UInt32 value = sector->GetSectorController().GetControllerAction().GetKeyValue(keystr.c_str());

	theGameMsg.SendGameDebugMsg(player, L"SectorLuaScript Key: %s, Value: %d", key.c_str(), value);

	return true;
}

const Bool GMCommandSystem::onAccountDbTest(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	EReqDbSaveKeyShortcut* req = NEW EReqDbSaveKeyShortcut;
	req->accountId = 1;
	SERVER.SendToDb(player, EventPtr(req));

	EReqDbLoadKeyShortcut* req1 = NEW EReqDbLoadKeyShortcut;
	req1->accountId = 1;
	SERVER.SendToDb(player, EventPtr(req1));

	return true;
}

const Bool GMCommandSystem::onKillMonster(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	// isDrop == 0 : not drop, isDrop == 1 : drop 
	IndexNpc npcIndex = 0;
	UInt32 isDrop = 0, count = 0;
	cmd >> isDrop >> npcIndex >> count;

	Sector* sector = player->GetSector();
	VALID_RETURN(sector, false);

	Vector3 pos = player->GetCurrPos();

	Grid* myGrid = sector->FindGrid(pos.x, pos.y);
	VALID_RETURN(myGrid, false);

	NearGrids nearGrids;
	myGrid->GetNearGrids(nearGrids);

	for (const Grid* grid : nearGrids)
	{
		VALID_DO(grid, continue);

		VectorNpc vecNpc;
		grid->GetAllNpcs(sector, vecNpc);
		VALID_DO(vecNpc.size(), continue);

		UInt32 deleteCount = 0;
		for (EntityNpc* npc : vecNpc)
		{
			VALID_DO(npc && npc->IsValid(), continue);

			if (0 != count && deleteCount >= count)
			{
				break;
			}

			VALID_DO(NpcJobType::IsMonsterType(npc->GetNpcJobType()), continue);

			if (0 == npcIndex || npc->GetNpcIndex() == npcIndex)
			{
				if (isDrop)
				{
					npc->GetDamageAction().SetMaxDamageUser(player->GetId());
				}

				npc->GetSkillControlAction().RemoveAllEffectVolume();

				npc->GetHsm().Tran(EntityState::STATE_NPC_DEAD);
				deleteCount++;
			}
		}
	}

	return true;
}

const Bool GMCommandSystem::onKillAllOfMonsters(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	// isDrop == 0 : not drop, isDrop == 1 : drop 
	//Int32 isDrop = 0;
	//cmd >> isDrop;

	// 기존에 인접한 그리드의 몬스터를 죽이는 것으로 되어 있다. 
	// 섹터의 모든 몬스터를 죽이는 걸로 수정함.

	Sector* sector = player->GetSector();
	VALID_RETURN(sector, false);

	sector->TranDeadAllNpc();

	return true;
}

const Bool GMCommandSystem::onFindNpc(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexNpc npcIndex = 0;
	cmd >> npcIndex;

	Sector* sector = player->GetSector();
	VALID_RETURN(sector, false);

	UInt32 count = sector->GetCurNpcCount(npcIndex);

	if (0 == npcIndex)
	{
		theGameMsg.SendGameDebugMsg(player, L"NPC[All] : %d\n", count);
	}
	else
	{
		theGameMsg.SendGameDebugMsg(player, L"NPC[%d] : %d\n", npcIndex, count);
	}


	return true;
}

const Bool GMCommandSystem::onWhereAmI(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring type;
	cmd >> type;

	if (type.empty())
	{
		Sector* sector = player->GetSector();
		VALID_RETURN(sector, false);

		InstanceDungeonInfo insInfo = sector->GetInstanceDungeonInfo();

		if (sector->GetInstanceId())
		{
			theGameMsg.SendGameDebugMsg(player, L"ZoneID[%d] layer[%d] multiStageMission[%d] \n", sector->GetIndexZone(), insInfo.layer, insInfo.multistageMissionIndex);
		}
		else
		{
			theGameMsg.SendGameDebugMsg(player, L"ZoneID : [%d] \n", sector->GetIndexZone());
		}
	}
	else if (type == L"maze")
	{
		ActionSector& actionSector = player->GetSectorAction();

		auto ActionSectorController = actionSector.GetActionSectorController();
		VERIFY_RETURN(ActionSectorController, false);

		MazeProcessor* mazeProcessor = dynamic_cast<MazeProcessor*>(ActionSectorController->GetSectorProcess());
		VERIFY_RETURN(mazeProcessor, false);

		mazeProcessor->DevWhereAmI(player);
	}

	return true;
}

const Bool GMCommandSystem::onCurrServerTime(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	SYSTEMTIME currTime;
	GetLocalTime(&currTime);
	theGameMsg.SendGameDebugMsg(player, L"current server time : %d-%d-%d %d:%d:%d\n"
		, currTime.wYear, currTime.wMonth, currTime.wDay
		, currTime.wHour, currTime.wMinute, currTime.wSecond);

	/*
	wstring str;
	str.resize( 1024 );
	swprintf_s( &str[0], 1024, L"current server time : %d-%d-%d %d:%d:%d\n"
	, currTime.wYear, currTime.wMonth, currTime.wDay
	, currTime.wHour, currTime.wMinute, currTime.wSecond );

	SystemMessage sysMsg( L"sys", str );
	SendSystemMsg( player, sysMsg );
	*/

	return true;
}

const Bool GMCommandSystem::onCurrServerVersion(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	theGameMsg.SendGameDebugMsg(player, L"ZoneVer  : Major(%d) Minor(%d) Patch(%d)\n"
		, MU2_MAJOR_VERSION, MU2_MINOR_VERSION, MU2_PATCH_VERSION);

	return true;
}

const Bool GMCommandSystem::onSpawnVolumeEnable(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexSpawnvolume volumeIndex = 0;
	std::wstring command;
	Bool flag = false;

	cmd >> command >> volumeIndex;

	if (command == L"on")
	{
		flag = false;
	}
	else if (command == L"off")
	{
		flag = true;
	}
	else
	{
		theGameMsg.SendGameDebugMsg(player, L" Wrong Command!! Ex) /spawnvolume on 77 \n");
		return false;
	}

	Sector* sector = player->GetSector();
	VALID_RETURN(sector, false);

	EntityControlVolume* spawner = sector->FindControlVolumeByCond(
		[volumeIndex, flag](const SectorEntityMap::value_type& v)->bool
	{
		VALID_RETURN(v.second->HasAttribute(ATTRIBUTE_SPAWN_VOLUME), false);

		AttributeVolumeSpawn* attr = GetEntityAttribute(v.second);
		VALID_RETURN(attr, false);

		if (attr->spawnVolumeIndex == volumeIndex)
		{
			attr->disabled = flag;
			return true;
		}

		return false;
	});

	if (spawner)
	{
		theGameMsg.SendGameDebugMsg(player, L"SpawnVolume state : %s, VolumeIndex : %d \n", command.c_str(), volumeIndex);
		return true;
	}
	else
	{
		theGameMsg.SendGameDebugMsg(player, L"VolumeIndex Not Exist : %d \n", volumeIndex);
		return false;
	}
}

const Bool GMCommandSystem::onHeroMissionInfo(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Sector* sector = player->GetSector();
	VALID_RETURN(sector, false);

	EntityFunctional* missionHero = static_cast<EntityFunctional*>(sector->FindEntity(stEntityId(EntityTypes::FUNCTIONAL, FunctionalType::MISSION_HERO)));
	VALID_RETURN(missionHero && missionHero->IsValid(), false);

	ActionMissionHero* actMissionHero = GetEntityAction(missionHero);
	VALID_RETURN(actMissionHero, false);

	actMissionHero->ShowProgressMissionInfo(player);

	return true;
}

const Bool GMCommandSystem::onStartHeroMission(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 missionIndex = 0;
	cmd >> missionIndex;

	Sector* sector = player->GetSector();
	VALID_RETURN(sector, false);

	EntityFunctional* missionHero = static_cast<EntityFunctional*>(sector->FindEntity(stEntityId(EntityTypes::FUNCTIONAL, FunctionalType::MISSION_HERO)));
	VALID_RETURN(missionHero && missionHero->IsValid(), false);

	ActionMissionHero* actMissionHero = GetEntityAction(missionHero);
	VALID_RETURN(actMissionHero, false);


	const MissionHeroElem* elem = SCRIPTS.GetMissionHeroScript(missionIndex);
	if (elem == nullptr)
	{
		theGameMsg.SendGameDebugMsg(player, L"Mission Not Exist!! : %d \n", missionIndex);
		return false;
	}

	IndexZone indexZone = sector->GetIndexZone();
	if (indexZone != elem->zoneId)
	{
		theGameMsg.SendGameDebugMsg(player, L"Mission Not Exist In This Zone!! : %d \n", missionIndex);
		return false;
	}

	if (actMissionHero->StartHeroMission(missionIndex, true))
	{
		theGameMsg.SendGameDebugMsg(player, L"Mission Start!! : %d \n", missionIndex);
	}
	else
	{
		theGameMsg.SendGameDebugMsg(player, L"Mission Can't Start!! : %d \n", missionIndex);
		return false;
	}

	return true;
}

const Bool GMCommandSystem::MakeXML()
{
	MU2_TRACE_LOG(LogCategory::GMCOMMAND, "[GMCommand] start make XML..........");
	TiXmlDocument doc;
	doc.SetBom(true);

	TiXmlDeclaration* decl = new TiXmlDeclaration("1.0", "UTF-8", "");
	doc.LinkEndChild(decl);

	//! root
	TiXmlElement* element = new TiXmlElement("GMCommand");
	doc.LinkEndChild(element);

	for (auto it = m_impl->gmCommandProcessorMap.begin(); it != m_impl->gmCommandProcessorMap.end(); ++it)
	{
		const std::wstring& cmd = it->first;
		const GMCommandProcessInfo& info = it->second;

		std::string scmd;
		StringUtil::Convert(cmd, scmd);

		TiXmlElement* sub_element = new TiXmlElement("Command");
		std::string buffer;

		StringUtil::Convert(scmd, buffer, CP_UTF8);
		sub_element->SetAttribute("name", buffer.c_str());

		sub_element->SetAttribute("argment_count", info.argCount);
		StringUtil::Convert(info.category, buffer, CP_UTF8);
		sub_element->SetAttribute("category", buffer.c_str());
		StringUtil::Convert(info.description, buffer, CP_UTF8);
		sub_element->SetAttribute("description", buffer.c_str());
		element->LinkEndChild(sub_element);
	}

	doc.SaveFile("ZoneCommand.xml");
	MU2_TRACE_LOG(LogCategory::GMCOMMAND, "[GMCommand] finish make XML..........");

	return true;
}

const Bool GMCommandSystem::onSetPvP(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring charName;
	UInt32 myTeam = 0, otherTeam = 0;
	cmd >> charName >> myTeam >> otherTeam;

	TeamType::Enum eMyTeam = static_cast<TeamType::Enum>(myTeam);
	TeamType::Enum eOtherTeam = static_cast<TeamType::Enum>(otherTeam);

	Sector* sector = player->GetSector();
	VERIFY_RETURN(sector, false);

	EntityPlayer* targetPlayer = sector->FindPlayerByName(charName);
	if (targetPlayer == nullptr)
	{
		theGameMsg.SendGameDebugMsg(player, L"%s는 없다\n", charName.c_str());
		return false;
	}

	if (targetPlayer->GetId() == player->GetId())
	{
		theGameMsg.SendGameDebugMsg(player, L"내다\n");
		return false;
	}

	ENtfMyTeamInfo* ntf = NEW ENtfMyTeamInfo;
	EventPtr ep(ntf);

	ntf->myTeamInfo.entityId = player->GetId();
	ntf->myTeamInfo.eMyTeam = eMyTeam;
	ntf->myTeamInfo.eOtherTeam = eOtherTeam;
	ntf->myTeamInfo.eAppearanceTeam = eMyTeam;

	// 나
	player->SetDuelPlayerId(targetPlayer->GetId());
	player->SetMyTeam(eMyTeam);
	player->SetAppearanceTeam(eMyTeam);
	ntf->myTeamInfo.duelId = player->GetDuelPlayerId();
	SERVER.SendToClient(player, ep);

	// 적
	targetPlayer->SetDuelPlayerId(player->GetId());
	targetPlayer->SetMyTeam(eOtherTeam);
	targetPlayer->SetAppearanceTeam(eOtherTeam);
	ntf->myTeamInfo.duelId = targetPlayer->GetDuelPlayerId();
	SERVER.SendToClient(targetPlayer, ep);

	theGameMsg.SendGameDebugMsg(player, L"%s와 pvp 진행, myTeam:%d, otherTeam:%d\n", charName.c_str(), eMyTeam, eOtherTeam);
	theGameMsg.SendGameDebugMsg(targetPlayer, L"%s가 pvp신청함 myTeam:%d, otherTeam:%d\n\n", player->GetCharName().c_str(), eOtherTeam, eMyTeam);

	return true;
}
static int teamCnt = 1;
const Bool GMCommandSystem::onSetPvPAll2(EntityPlayer* owner, const CommandString& cmd)
{
	VERIFY_RETURN(owner && owner->IsValid(), false);

	UInt32 myTeam = 0;
	cmd >> myTeam;

	Sector* sector = owner->GetSector();
	VERIFY_RETURN(sector, false);

	owner->SetMyTeam(static_cast<TeamType::Enum>(myTeam));
	owner->SetAppearanceTeam(owner->GetMyTeam());

	ENtfMyTeamInfo* ntf = NEW ENtfMyTeamInfo;
	EventPtr ep(ntf);

	ntf->myTeamInfo.entityId = owner->GetId();
	sector->Broadcast(ep);

	ntf->myTeamInfo.eMyTeam = owner->GetMyTeam();
	ntf->myTeamInfo.eAppearanceTeam = owner->GetAppearanceTeam();
	ntf->myTeamInfo.eOtherTeam = TeamType::TEAM_NONE;
	ntf->myTeamInfo.duelId = 0;
	sector->Broadcast(ep);

	VectorPlayer vecPlayer;
	sector->GetPlayers(vecPlayer);
	for (auto player : vecPlayer)
	{
		VALID_DO(player && player->IsValid(), continue);
		VALID_DO(player->GetId() != owner->GetId(), continue);

		ntf->myTeamInfo.entityId = player->GetId();
		ntf->myTeamInfo.eMyTeam = player->GetMyTeam();
		ntf->myTeamInfo.eAppearanceTeam = player->GetAppearanceTeam();
		ntf->myTeamInfo.eOtherTeam = TeamType::TEAM_NONE;
		ntf->myTeamInfo.duelId = 0;
		sector->Broadcast(ep);
	}

	return true;
}

const Bool GMCommandSystem::onChangeTeam(EntityPlayer* owner, const CommandString& cmd)
{
	VERIFY_RETURN(owner && owner->IsValid(), false);

	UInt32 myTeam = 0;
	cmd >> myTeam;

	Sector* sector = owner->GetSector();
	VERIFY_RETURN(sector, false);

	owner->SetMyTeam(static_cast<TeamType::Enum>(myTeam));
	owner->SetAppearanceTeam(static_cast<TeamType::Enum>(myTeam));

	ENtfChangeMyTeamInfo* ntf = NEW ENtfChangeMyTeamInfo;
	EventPtr ep(ntf);
	ntf->entityId = owner->GetId();
	ntf->eMyTeam = owner->GetMyTeam();
	ntf->eAppearanceTeam = owner->GetAppearanceTeam();
	ntf->eOtherTeam = TeamType::TEAM_NONE;

	owner->GetSectorAction().NearGridCast(ep);

	return true;
}

const Bool GMCommandSystem::onMyTeam(EntityPlayer* owner, const CommandString& cmd)
{
	VERIFY_RETURN(owner && owner->IsValid(), false);

	UInt32 myTeam = 0;
	cmd >> myTeam;

	Sector* sector = owner->GetSector();
	VERIFY_RETURN(sector, false);

	owner->SetMyTeam(static_cast<TeamType::Enum>(myTeam));
	owner->SetAppearanceTeam(static_cast<TeamType::Enum>(myTeam));

	ENtfMyTeamInfo* ntf = NEW ENtfMyTeamInfo;
	EventPtr ep(ntf);
	ntf->myTeamInfo.entityId = owner->GetId();
	ntf->myTeamInfo.eMyTeam = owner->GetMyTeam();
	ntf->myTeamInfo.eAppearanceTeam = owner->GetAppearanceTeam();
	ntf->myTeamInfo.eOtherTeam = TeamType::TEAM_NONE;
	ntf->myTeamInfo.duelId = 0;

	owner->GetSectorAction().NearGridCast(ep);

	return true;
}

const Bool GMCommandSystem::onSetPvPAll(EntityPlayer* owner, const CommandString& cmd)
{
	VERIFY_RETURN(owner && owner->IsValid(), false);

	Sector* sector = owner->GetSector();
	VERIFY_RETURN(sector, false);

	ENtfMyTeamInfo* ntf = NEW ENtfMyTeamInfo;
	EventPtr ep(ntf);

	ntf->myTeamInfo.entityId = owner->GetId();
	ntf->myTeamInfo.eMyTeam = owner->GetMyTeam();
	ntf->myTeamInfo.eAppearanceTeam = owner->GetAppearanceTeam();
	ntf->myTeamInfo.eOtherTeam = TeamType::TEAM_NONE;
	ntf->myTeamInfo.duelId = 0;

	sector->Broadcast(ep);

	return true;
}

const Bool GMCommandSystem::onInitPvP(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Sector* sector = player->GetSector();
	VERIFY_RETURN(sector, false);

	EntityId duelPlayerId = player->GetDuelPlayerId();
	VERIFY_RETURN(duelPlayerId, false);

	player->SetDuelPlayerId(0);
	player->SetMyTeam(TeamType::TEAM_NONE);
	player->SetAppearanceTeam(TeamType::TEAM_NONE);

	ENtfMyTeamInfo* ntf = NEW ENtfMyTeamInfo;
	EventPtr ep(ntf);

	ntf->myTeamInfo.entityId = player->GetId();
	ntf->myTeamInfo.eMyTeam = TeamType::TEAM_NONE;
	ntf->myTeamInfo.eAppearanceTeam = 0;
	ntf->myTeamInfo.eOtherTeam = TeamType::TEAM_NONE;
	ntf->myTeamInfo.duelId = 0;

	SERVER.SendToClient(player, ep);

	EntityPlayer* targetPlayer = sector->FindPlayer(duelPlayerId);
	if (targetPlayer == nullptr)
	{
		theGameMsg.SendGameDebugMsg(player, L" duelId : %d는 없다\n", duelPlayerId);
		return false;
	}

	targetPlayer->SetDuelPlayerId(0);
	targetPlayer->SetMyTeam(TeamType::TEAM_NONE);
	targetPlayer->SetAppearanceTeam(TeamType::TEAM_NONE);
	SERVER.SendToClient(targetPlayer, ep);

	theGameMsg.SendGameDebugMsg(player, L"%s와 pvp 끝\n", targetPlayer->GetCharName().c_str());
	theGameMsg.SendGameDebugMsg(targetPlayer, L"%s와 pvp 끝\n", player->GetCharName().c_str());

	return true;
}

const Bool GMCommandSystem::onRepairAll(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	player->GetDurabilityAction().RepairAll(0, true);

	return true;
}

const Bool GMCommandSystem::onSetNpcKeyValue(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexNpc npcIndex = 0;
	UInt32 keyIndex = 0, keyValue = 0;
	cmd >> npcIndex >> keyIndex >> keyValue;

	Sector* sector = player->GetSector();
	VALID_RETURN(sector, false);

	Vector3 pos = player->GetCurrPos();

	Grid* myGrid = sector->FindGrid(pos.x, pos.y);
	VALID_RETURN(myGrid, false);

	NearGrids nearGrids;
	myGrid->GetNearGrids(nearGrids);

	for (const Grid* grid : nearGrids)
	{
		VALID_DO(grid, continue);

		VectorNpc vecNpc;
		grid->GetAllNpcs(sector, vecNpc);
		VALID_DO(vecNpc.size(), continue);

		for (EntityNpc* npc : vecNpc)
		{
			VALID_DO(npc && npc->IsValid(), continue);

			if (0 == npcIndex || npc->GetNpcIndex() == npcIndex)
			{
				npc->GetScriptAction().SetKeyValue(keyIndex, keyValue);
			}
		}
	}
	return true;
}

const Bool GMCommandSystem::onfinishAchivement(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 achieveIndex;
	cmd >> achieveIndex;

	return true;
}

const Bool GMCommandSystem::onResetAchievement(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring onOff;
	UInt32 achieveIndex;
	cmd >> onOff >> achieveIndex;

	return true;
}

const Bool GMCommandSystem::onShowAbilityOption(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), true);

	std::wstring  abilityType;
	cmd >> abilityType;

	//auto aability = player->GetAction<ActionAbility>();
	//if ( aability )
	{
		Int32 outkey = 0;
		if (ScriptEnum::FindToken(L"EntityAbilityType", abilityType, outkey))
		{
			Int32 abilityValue = player->GetAbilityValue(static_cast< EAT::Enum >(outkey));
			theGameMsg.SendGameDebugMsg(player, L"%s : %d", abilityType.c_str(), abilityValue);
			return true;
		}
		else
		{
			theGameMsg.SendGameDebugMsg(player, L"invalid key");
			return true;
		}
	}
	//else
	//{
	//	theGameMsg.SendGameDebugMsg( player, L"invalid key" );
	//	return true;
	//}
}

const Bool GMCommandSystem::onRezenState(EntityPlayer* player, const CommandString&)
{
	return theSpawnSystem.SetSpawnState(player);
}

const Bool GMCommandSystem::onGiveBuff(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexSkill skillId;
	cmd >> skillId;

	theLogicEffectSystem.OnSingleCast(player, skillId);

	return true;
}

const Bool GMCommandSystem::onTakeBuff(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexSkill skillId;
	cmd >> skillId;

	theLogicEffectSystem.OnCancelBuffCast(player, skillId);

	return true;
}

const Bool GMCommandSystem::onShowBuff(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexSkill skillId;
	cmd >> skillId;

	return player->GetBuffAction().ShowBuffInfo();
}

const Bool GMCommandSystem::onSetSkillCheatId(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexNpc npcIndex = 0;
	IndexSkill skillId = 0;
	cmd >> npcIndex >> skillId;

	Sector* sector = player->GetSector();
	VERIFY_RETURN(sector, false);

	Vector3 pos = player->GetCurrPos();

	Grid* myGrid = sector->FindGrid(pos.x, pos.y);
	VALID_RETURN(myGrid, false);

	NearGrids nearGrids;
	myGrid->GetNearGrids(nearGrids);

	for (const Grid* grid : nearGrids)
	{
		VALID_DO(grid, continue);

		VectorNpc vecNpc;
		grid->GetAllNpcs(sector, vecNpc);
		VALID_DO(vecNpc.size(), continue);

		for (EntityNpc* npc : vecNpc)
		{
			VALID_DO(npc && npc->IsValid(), continue);
			VALID_DO(npc->GetNpcIndex() == npcIndex, continue);

			npc->GetSkillAction().SetSkillCheatId(skillId);
		}
	}

	return true;
}

const Bool GMCommandSystem::onPlaySecen(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 playTime = 0;
	cmd >> playTime;

	Sector* sector = player->GetSector();
	sector->SetHold(playTime);

	theGameMsg.SendGameDebugMsg(player, L"play cinematic during seconds(%d)", playTime);

	return true;
}

const Bool GMCommandSystem::onStartDungeonMission(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 missionIndex = 0;
	cmd >> missionIndex;

	Sector* sector = player->GetSector();
	VERIFY_RETURN(sector, false);

	if (sector->GetSectorController().GetMissionDungeonAction().CheatStartMission(player, missionIndex))
	{
		theGameMsg.SendGameDebugMsg(player, L"missionId :[ %d ] start success", missionIndex);
	}
	else
	{
		theGameMsg.SendGameDebugMsg(player, L"missionId :[ %d ] start fail", missionIndex);
	}

	return true;
}

const Bool GMCommandSystem::onClearDungeonMission(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 missionIndex = 0;
	cmd >> missionIndex;

	Sector* sector = player->GetSector();
	VERIFY_RETURN(sector, false);

	if (sector->GetSectorController().GetMissionDungeonAction().CheatClearMission(missionIndex))
	{
		theGameMsg.SendGameDebugMsg(player, L"missionId :[ %d ] clear success", missionIndex);
	}
	else
	{
		theGameMsg.SendGameDebugMsg(player, L"missionId :[ %d ] clear fail", missionIndex);
	}

	return true;
}

const Bool GMCommandSystem::onGetItems(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	player->GetInventoryAction().PickupAllRangeItems();

	return true;
}

const Bool GMCommandSystem::onPlayDeadAniAndDespawn(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);
	IndexSpawnvolume spawnvolumeIndex = 0;
	UInt32 deadType = 0;	// (0:ani, 1:빙결, 2:석화, 3:분쇄)

	cmd >> spawnvolumeIndex >> deadType;

	Sector* sector = player->GetSector();
	VALID_RETURN(sector, false);

	sector->GetSectorController().GetControllerAction().KillSpawnVolumeMontser(spawnvolumeIndex, deadType);

	return true;
}

const Bool GMCommandSystem::onIdleTime(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 idleTime = 0;
	cmd >> idleTime;

	Sector* sector = player->GetSector();
	VERIFY_RETURN(sector, false);

	sector->SetGMIdleTime(idleTime);

	theGameMsg.SendGameDebugMsg(player, L"monster idle Time :[ %d ]", idleTime);

	return true;

}

const Bool GMCommandSystem::onShowPassivity(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	player->GetPassivityAction().ShowDebugPassivity();

	return true;
}

const Bool GMCommandSystem::onAddPassivity(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexSkill skillId = 0;
	cmd >> skillId;

	player->GetPassivityAction().Apply(skillId);

	return true;
}

const Bool GMCommandSystem::onDelPassivity(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexSkill skillId = 0;
	cmd >> skillId;

	player->GetPassivityAction().Cancel(skillId);

	return true;
}

const Bool GMCommandSystem::onRemoveItemInRange(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 range = 0;
	cmd >> range;

	Vector3 playerPos = player->GetCurrPos();
	Sector* sector = player->GetSector();
	VALID_RETURN(sector, false);

	Grid* myGrid = sector->FindGrid(playerPos.x, playerPos.y);
	VALID_RETURN(myGrid, false);

	NearGrids nearGrids;
	myGrid->GetNearGrids(nearGrids);

	for (const Grid* grid : nearGrids)
	{
		VALID_DO(grid, continue);

		VectorEntity entities;

		grid->GetAllEntities(sector, EntityTypes::DROPPED_ITEM, entities);

		for (Entity4Zone* droppedItem : entities)
		{
			VALID_DO(droppedItem, continue);

			Vector3 droppedItemPos;
			ViewPosition* droppedItemViewPos = GetEntityView(droppedItem);
			VALID_DO(droppedItemViewPos, continue);

			droppedItemViewPos->GetPosition(droppedItemPos);

			Float distance = Distance(playerPos, droppedItemPos);

			VALID_DO(distance <= range, continue);

			ActionDroppedItem* adropped = GetEntityAction(droppedItem);
			if (adropped)
			{
				adropped->ReleaseItem();
				adropped->RemoveGrid();
			}
		}
	}

	return true;
}

//const Bool GMCommandSystem::onOpenSlotSpecialSkill( EntityPlayer* player, const CommandString& cmd )
//{
//	VERIFY_RETURN( player && player->IsValid(), false );
//
//	UInt32 slot = 0;
//	cmd >> slot;
//
//	player->GetSkillManageAction().OpenSpecialSkill( static_cast< Byte >( slot ) );
//
//	return true;
//}

const Bool GMCommandSystem::onEquipSlotSpecialSkill(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 slotPlace = 0;
	SubindexSkill specialSkill = 0;

	cmd >> slotPlace >> specialSkill;

	EReqGameEquipSlotSpecialSkill* req = NEW EReqGameEquipSlotSpecialSkill;
	req->slotPlace = static_cast< Byte >(slotPlace);
	req->specialSkill = specialSkill;
	player->OnEvent(EventPtr(req));

	return true;
}
#ifdef SPECIAL_SKILL_RENEWAL_by_jjangmo_180612
#else
const Bool GMCommandSystem::onUnequipSlotSpecialSkill(EntityPlayer* player, const CommandString& cmd)
{
	UInt32 slotPlace = 0;
	cmd >> slotPlace;

	EReqGameUnequipSlotSpecialSkill* req = NEW EReqGameUnequipSlotSpecialSkill;
	req->slotPlace = static_cast< Byte >(slotPlace);
	player->OnEvent(EventPtr(req));

	return true;
}
#endif
const Bool GMCommandSystem::onSaveCoachMarks(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 coachId = 0;
	UInt32 coachValue = 0;
	cmd >> coachId >> coachValue;

	EReqDbSaveCoachMarks* req = NEW EReqDbSaveCoachMarks;

	req->charId = player->GetCharId();
	req->coachId = coachId;
	req->coachValue = coachValue;

	SERVER.SendToDb(player, EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onSetDecreaseAggroRateByAttack(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 decreasePointRate = 0;
	cmd >> decreasePointRate;

	Sector* sector = player->GetSector();
	VALID_RETURN(sector, false);

	Vector3 pos = player->GetCurrPos();

	Grid* myGrid = sector->FindGrid(pos.x, pos.y);
	VALID_RETURN(myGrid, false);

	NearGrids nearGrids;
	myGrid->GetNearGrids(nearGrids);

	for (const Grid* grid : nearGrids)
	{
		VALID_DO(grid, continue);

		VectorNpc entities;
		grid->GetAllNpcs(sector, entities);
		VALID_DO(entities.size(), continue);

		for (EntityNpc* npc : entities)
		{
			VALID_DO(npc && npc->IsValid(), continue);

			npc->GetNpcAggroAction().SetDecreaseAggroPointRateByAttack(decreasePointRate);
		}
	}

	theGameMsg.SendGameDebugMsg(player, L"Decrease Aggro point rate by hit:[ %d ]", decreasePointRate);

	return true;
}

const Bool GMCommandSystem::onSetDecreaseAggroRateByTime(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 decreasePointRate = 0;
	cmd >> decreasePointRate;

	Sector* sector = player->GetSector();
	VALID_RETURN(sector, false);

	Vector3 pos = player->GetCurrPos();

	Grid* myGrid = sector->FindGrid(pos.x, pos.y);
	VALID_RETURN(myGrid, false);

	NearGrids nearGrids;
	myGrid->GetNearGrids(nearGrids);

	for (const Grid* grid : nearGrids)
	{
		VALID_DO(grid, continue);

		VectorNpc entities;
		grid->GetAllNpcs(sector, entities);
		VALID_DO(entities.size(), continue);

		for (EntityNpc* npc : entities)
		{
			VALID_DO(npc && npc->IsValid(), continue);

			npc->GetNpcAggroAction().SetDecreaseAggroPointRateByTime(decreasePointRate);
		}
	}

	theGameMsg.SendGameDebugMsg(player, L"Decrease Aggro point rate by time :[ %d ]", decreasePointRate);

	return true;
}


const Bool GMCommandSystem::onSetDecreaseAggroInterval(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 decreasePointRate = 0;
	cmd >> decreasePointRate;

	Sector* sector = player->GetSector();
	VALID_RETURN(sector, false);

	Vector3 pos = player->GetCurrPos();
	Grid* myGrid = sector->FindGrid(pos.x, pos.y);
	VALID_RETURN(myGrid, false);

	NearGrids nearGrids;
	myGrid->GetNearGrids(nearGrids);

	for (const Grid* grid : nearGrids)
	{
		VALID_DO(grid, continue);

		VectorNpc entities;
		grid->GetAllNpcs(sector, entities);
		VALID_DO(entities.size(), continue);

		for (EntityNpc* npc : entities)
		{
			VALID_DO(npc && npc->IsValid(), continue);

			npc->GetNpcAggroAction().SetDecreaseAggroPointInterval(decreasePointRate);
		}
	}

	theGameMsg.SendGameDebugMsg(player, L"Aggro point interval :[ %d ]", decreasePointRate);

	return true;
	}

const Bool GMCommandSystem::onSetShowAggroState(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 inputValue = 0;
	cmd >> inputValue;

	Bool showAggroState = false;
	if (inputValue > 0)
	{
		showAggroState = true;
	}

	Sector* sector = player->GetSector();
	VALID_RETURN(sector, false);

	Vector3 pos = player->GetCurrPos();
	Grid* myGrid = sector->FindGrid(pos.x, pos.y);
	VALID_RETURN(myGrid, false);

	NearGrids nearGrids;
	myGrid->GetNearGrids(nearGrids);

	for (const Grid* grid : nearGrids)
	{
		VALID_DO(grid, continue);

		VectorNpc entities;
		grid->GetAllNpcs(sector, entities);
		VALID_DO(entities.size(), continue);

		for (EntityNpc* npc : entities)
		{
			VALID_DO(npc && npc->IsValid(), continue);

			npc->GetNpcAggroAction().SetShowAggroPointState(showAggroState);
		}
	}

	return true;
}

const Bool GMCommandSystem::onMissionmap(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 type = 0, party = 0, intrusion = 0;
	cmd >> type >> party >> intrusion;

	// "[미션맵 신청:mmc][3][(매치타입)N: 0:없음, 11:카오스캐슬, 12:정령의재단, 13:데스매치][(파티여부)N: 0:개인신청, 1:파티신청][(난입여부)N: 0:난입불가, 1:난입허용]"


	EReqMissionmapCandidateC* req = NEW EReqMissionmapCandidateC;
	EventPtr reqPtr(req);
	req->matchType = (DungeonType::Enum)type;
	req->isParty = (party > 0) ? true : false;
	req->isIntrusion = (intrusion > 0) ? true : false;
	theZoneHandler.Notify(player, reqPtr);

	return true;
}

const Bool GMCommandSystem::onShowEloPoint(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring missionmapName;
	cmd >> missionmapName;

	ActionPlayerEloPoint* actEloPoint = GetEntityAction(player);
	VERIFY_RETURN(actEloPoint, false);

	auto output = [&actEloPoint, &player, &missionmapName](int type)
	{
		EloPoint eloPoint;
		actEloPoint->GetEloPoint(type, eloPoint);
		theGameMsg.SendGameDebugMsg(player, L"%s elo : point = %d, count = %d\n", missionmapName.c_str(), eloPoint.playPoint, eloPoint.playCount);
	};

	if (missionmapName != L"")
	{
		std::transform(missionmapName.begin(), missionmapName.end(), missionmapName.begin(), towlower);
		if (missionmapName == L"chaos")
		{
			output(DungeonType::DT_CHAOS_CASTLE);
		}
		else if (missionmapName == L"altar")
		{
			output(DungeonType::DT_ALTAR_OF_ELEMENTS);
		}
		else if (missionmapName == L"death20")
		{
			output(DungeonType::DT_DEATH_MATCH_20);
		}
		else if (missionmapName == L"death40")
		{
			output(DungeonType::DT_DEATH_MATCH_40);
		}
		else if (missionmapName == L"pvp33")
		{
			output(DungeonType::DT_PVP_COLOSSEUM_33);
		}

		else
		{
			theGameMsg.SendGameDebugMsg(player, L"invalid missionmap name = %s\n", missionmapName.c_str());
		}
	}
	else
	{
		for (UInt32 i = DungeonType::MISSIONMAP_START; i <= DungeonType::MISSIONMAP_END; ++i)
		{
			switch (i)
			{
			case DungeonType::DT_CHAOS_CASTLE:
				missionmapName = L"chaos";
				break;
			case DungeonType::DT_ALTAR_OF_ELEMENTS:
				missionmapName = L"altar";
				break;

			case DungeonType::DT_PVP_COLOSSEUM_33:
				missionmapName = L"pvp33";
				break;
			case DungeonType::DT_DEATH_MATCH_20:
				missionmapName = L"death20";
				break;
			case DungeonType::DT_DEATH_MATCH_40:
				missionmapName = L"death40";
				break;
			default:
				missionmapName = L"Unknown";
				break;
			}
			output(i);
		}
	}

	return true;
}

const Bool GMCommandSystem::onSetEloPoint(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring missionmapName;
	UInt32 point, count;
	cmd >> missionmapName >> point >> count;

	ActionPlayerEloPoint* actEloPoint = GetEntityAction(player);
	VERIFY_RETURN(actEloPoint, false);

	// 만약 치트키 사용중 문제가 발생한다면, 프로토콜을 생성해서 World 서버로 보낸 후 받아서 처리해야한다.
	// 아마도 그렇게 해야할것 같다.
	if (missionmapName != L"")
	{
		std::transform(missionmapName.begin(), missionmapName.end(), missionmapName.begin(), towlower);
		if (missionmapName == L"chaos")
		{
			actEloPoint->SetEloPoint(DungeonType::DT_CHAOS_CASTLE, EloPoint(count, point), true);
		}
		else if (missionmapName == L"altar")
		{
			actEloPoint->SetEloPoint(DungeonType::DT_ALTAR_OF_ELEMENTS, EloPoint(count, point), true);
		}
		else if (missionmapName == L"death20")
		{
			actEloPoint->SetEloPoint(DungeonType::DT_DEATH_MATCH_20, EloPoint(count, point), true);
		}
		else if (missionmapName == L"death40")
		{
			actEloPoint->SetEloPoint(DungeonType::DT_DEATH_MATCH_40, EloPoint(count, point), true);
		}
		else
		{
			theGameMsg.SendGameDebugMsg(player, L"invalid missionmap name = %s\n", missionmapName.c_str());
		}
	}

	return true;
}


const Bool GMCommandSystem::onTestSectorFunction(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Int32 type = 0, arg1 = 0, arg2 = 0, arg3 = 0, arg4 = 0, arg5 = 0;
	cmd >> type >> arg1 >> arg2 >> arg3 >> arg4 >> arg5;

	// "[미션맵 신청:mmc][3][(매치타입)N: 0:없음, 11:카오스캐슬, 12:정령의재단, 13:데스매치][(파티여부)N: 0:개인신청, 1:파티신청][(난입여부)N: 0:난입불가, 1:난입허용]"

	Sector* sector = player->GetSector();
	VALID_RETURN(sector, false);

	ActionSectorController& asc = sector->GetSectorController().GetControllerAction();

	switch (type)
	{
	case 1:
	{
		asc.FireDirectionSkillSpawnVolumeIndex(arg1, arg2, static_cast<Float>(arg3));
		break;
	}
	case 2:
	{
		asc.FirePointSkillSpawnVolumeIndex(arg1, arg2, static_cast<Float>(arg3), static_cast<Float>(arg4), static_cast<Float>(arg5));
		break;
	}
	case 3:
	{
		asc.StartDialogSpawnVolumeIndex(arg1, arg2, arg3); // arg1:spawnvolumeindex, arg2:dialogindex
		break;
	}
	case 4:
	{
		asc.MoveIgnoreSpawnVolumeIndex(arg1, arg2, arg3, arg4);
		break;
	}
	case 5:
	{
		asc.UpdatePortalState(arg1, static_cast<PortalState::Enum>(arg2));
		break;
	}
	case 6:
	{
		asc.MovePc(arg1, static_cast<Float>(arg2), static_cast<UInt16>(arg3));
		break;
	}
	case 7:
	{
		asc.RotatePc(static_cast<Float>(arg1));
		break;
	}
	case 8:
	{
		asc.ChangePos(static_cast<Float>(arg1), static_cast<Float>(arg2), static_cast<Float>(arg3), static_cast<Float>(arg4));
		break;
	}
	case 9:
	{
		asc.CancelSkillSpawnVolumeIndex(arg1);
		break;
	}
	case 10:
	{
		EntityNpc* npc = sector->FindNpcByCond([arg1](const SectorEntityMap::value_type& v) -> bool
		{
			AttributeNpc* attrNpc = GetEntityAttribute(v.second);
			if (attrNpc->GetNpcIndex() == static_cast<IndexNpc>(arg1))
			{
				return true;
			}
			return false;
		}
		);

		if (nullptr != npc)
		{
			ENtfNpcBubbleString *ntf = NEW ENtfNpcBubbleString;
			ntf->entityId = npc->GetId();
			ntf->bubbleStringIndex = static_cast<Byte>(arg2);
			player->GetSectorAction().NearGridCast(EventPtr(ntf));
		}

		break;
	}
	case 11:
	{
		asc.SpawnGroup(arg1);
		break;
	}
	}

	return true;
}

const Bool GMCommandSystem::onSetNearNPCAttackSpeedRatio(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Float attackSpeedRatio;
	cmd >> attackSpeedRatio;

	Sector* sector = player->GetSector();
	VALID_RETURN(sector, false);

	Vector3 pos = player->GetCurrPos();
	Grid* myGrid = sector->FindGrid(pos.x, pos.y);
	VALID_RETURN(myGrid, false);

	NearGrids nearGrids;
	myGrid->GetNearGrids(nearGrids);

	for (const Grid* grid : nearGrids)
	{
		VALID_DO(grid, continue);

		VectorNpc entities;
		grid->GetAllNpcs(sector, entities);
		VALID_DO(entities.size(), continue);

		for (EntityNpc* npc : entities)
		{
			VALID_DO(npc && npc->IsValid(), continue);

			npc->GetNpcAttr().forceAttackSpeedRatio = attackSpeedRatio;
		}
	}

	return true;

}


const Bool GMCommandSystem::onSetNearNPCMoveSpeed(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 moveSpeed;
	cmd >> moveSpeed;

	Sector* sector = player->GetSector();
	VALID_RETURN(sector, false);

	Vector3 pos = player->GetCurrPos();
	Grid* myGrid = sector->FindGrid(pos.x, pos.y);
	VALID_RETURN(myGrid, false);

	NearGrids nearGrids;
	myGrid->GetNearGrids(nearGrids);

	for (const Grid* grid : nearGrids)
	{
		VALID_DO(grid, continue);

		VectorNpc entities;
		grid->GetAllNpcs(sector, entities);
		VALID_DO(entities.size(), continue);

		for (EntityNpc* npc : entities)
		{
			VALID_DO(npc && npc->IsValid(), continue);

			npc->GetNpcAttr().forceMoveSpeed = moveSpeed;
		}
	}

	return true;
}

const Bool GMCommandSystem::onDropSimulation(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexNpc npcIndex = 0;
	UInt32 count = 0;
	CharLevel level = 0;

	cmd >> npcIndex >> level >> count;

	// 최대 만번으로 제한한다.
	count = std::min< UInt32 >(10'000UL, count);

	const NpcInfoElem* npcInfoElem = SCRIPTS.GetNpcInfoScript(npcIndex);
	VALID_RETURN(npcInfoElem, false);

	Vector3 pos = player->GetCurrPos();
	Sector* sector = player->GetSector();
	VALID_RETURN(sector, true);

	// 드랍 내부에서 Entity4Zone 내부의 Action 을 가지고 처리하는 부분이 존재해서 해당 NPC 한마리를 생성한다.
	EntityNpc* npc = nullptr;
	SpawnSystem::BaseSpawnInfo baseSpawnInfo;
	baseSpawnInfo.npcIndex = npcIndex;
	baseSpawnInfo.level = level;
	baseSpawnInfo.sector = sector;
	baseSpawnInfo.spawnPos = pos;
	baseSpawnInfo.aiIndex = npcInfoElem->aiIndex;
	VALID_RETURN(theSpawnSystem.SpawnGeneralNpc(&baseSpawnInfo, &npc), false);

	ActionNpcDamage* actionDamage = GetEntityAction(npc);
	if (nullptr != actionDamage)
	{
		actionDamage->AddDamaged(player, 1);
		actionDamage->PreprocessDamaged();
	}

	theDropSystem.DropFromNpcSimulation(npc, player, count);
	sector->RemoveEntity(npc);
	return true;
}

const Bool GMCommandSystem::onGambleTest(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	ItemDivisionType::Enum division = ItemDivisionType::NONE;

	cmd >> (int32&)division;

	ActionPlayerInventory& invenAction = player->GetInventoryAction();

	VERIFY_RETURN(invenAction.HasDivisionItem(InvenBags, division), false);
	VERIFY_RETURN(ItemDivisionType::IsGambleItemDivisionType(division), false);

	Items foundItems;
	VERIFY_RETURN(ItemFinderByDivision(*player, division).Find(InvenBags, foundItems, 1), false);

	EReqUseGambleItem* req = NEW EReqUseGambleItem;
	req->itemPos = foundItems.front()->GetPos();
	req->useItemCount = 1;
	player->OnEvent(EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onWhereIsNpc(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 npcId = 0;
	cmd >> npcId;

	SERVER.PrintZoneIdMatchNpcId(player, npcId);

	return true;
}

const Bool GMCommandSystem::onCompleteHeroMission(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 missionIndex = 0;
	cmd >> missionIndex;

	Sector* sector = player->GetSector();
	VALID_RETURN(sector, false);

	EntityFunctional* missionHero = static_cast<EntityFunctional*>(sector->FindEntity(stEntityId(EntityTypes::FUNCTIONAL, FunctionalType::MISSION_HERO)));
	VALID_RETURN(missionHero && missionHero->IsValid(), false);

	ActionMissionHero* actMissionHero = GetEntityAction(missionHero);
	VALID_RETURN(actMissionHero, false);

	const MissionHeroElem* elem = SCRIPTS.GetMissionHeroScript(missionIndex);
	if (elem == nullptr)
	{
		theGameMsg.SendGameDebugMsg(player, L"Mission Not Exist!! : %d \n", missionIndex);
		return false;
	}

	if (!actMissionHero->IsProgressMission(elem))
	{
		theGameMsg.SendGameDebugMsg(player, L"Mission is not running!! : %d \n", missionIndex);
		return false;
	}

	ActionPlayerQuest* actQuest = GetEntityAction(player);
	if (!actQuest->IsProgressQuest(elem->questIndex))
	{
		theGameMsg.SendGameDebugMsg(player, L"Mission Quest is not progress!! : %d, %d \n", missionIndex, elem->questIndex);
		return false;
	}

	Int32 err = actQuest->TestFinishQuest(elem->questIndex);
	if (err != ErrorQuest::SUCCESS)
	{
		actQuest->SendQuestError(player, err);
	}
	actMissionHero->TestStopHeroMission(missionIndex);
	return true;
}

const Bool GMCommandSystem::onFailHeroMission(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 missionIndex = 0;
	cmd >> missionIndex;

	Sector* sector = player->GetSector();
	VALID_RETURN(sector, false);

	EntityFunctional* missionHero = static_cast<EntityFunctional*>(sector->FindEntity(stEntityId(EntityTypes::FUNCTIONAL, FunctionalType::MISSION_HERO)));
	VALID_RETURN(missionHero && missionHero->IsValid(), false);

	ActionMissionHero* actMissionHero = GetEntityAction(missionHero);
	VALID_RETURN(actMissionHero, false);

	const MissionHeroElem* elem = SCRIPTS.GetMissionHeroScript(missionIndex);
	if (elem == nullptr)
	{
		theGameMsg.SendGameDebugMsg(player, L"Mission Not Exist!! : %d \n", missionIndex);
		return false;
	}

	if (!actMissionHero->IsProgressMission(elem))
	{
		theGameMsg.SendGameDebugMsg(player, L"Mission is not running!! : %d \n", missionIndex);
		return false;
	}

	ActionPlayerQuest* actQuest = GetEntityAction(player);
	if (!actQuest->IsProgressQuest(elem->questIndex))
	{
		theGameMsg.SendGameDebugMsg(player, L"Mission Quest is not progress!! : %d, %d \n", missionIndex, elem->questIndex);
		return false;
	}

	actQuest->FailQuest(elem->questIndex);
	actMissionHero->TestStopHeroMission(missionIndex);
	return true;
}
const Bool GMCommandSystem::onSkillNoCooltime(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring onoff;
	cmd >> onoff;

	ActionSkillControl& asc = player->GetSkillControlAction();

	asc.ClearAllCool();
	asc.SetNoCool(onoff == L"true" ? true : false);
	theGameMsg.SendGameDebugMsg(player, L"apply no cool");
	return true;
}

const Bool GMCommandSystem::onSkillClearCooltime(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	ActionSkillControl& asc = player->GetSkillControlAction();
	asc.ClearAllCool();
	theGameMsg.SendGameDebugMsg(player, L"apply clear cool");
	return true;
}

const Bool GMCommandSystem::onSetAdventureMapTypeAndFloor(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 advType = 0;
	UInt32 floor = 0;

	cmd >> advType >> floor;
	const AdvMultistageMapSelectScript* script = SCRIPTS.GetScript< AdvMultistageMapSelectScript >();
	VERIFY_RETURN(script, true);

	VALID_RETURN(script->IsExistAdvType(advType), false);

	// 2층 이상 최고층 아래까지 설정가능
	if ((floor < 2) || script->GetMaxFloor() < floor)
	{
		return false;
	}

	player->GetPortalAction().SetForceAdventureDungeonInfo(advType, floor);
	return true;
}

const Bool GMCommandSystem::onSetAdventureMapList(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	if (1 == cmd.Size() % 2)
	{
		return false;
	}

	UInt32 indexZone = 0;
	IndexPosition positionIndex = 0;
	UInt32 layer = 0;
	UInt32 advType = 0;
	UInt32 errorPos = 0;
	AdvDungeonPositionIndexLayerInfoList list;

	cmd >> indexZone >> layer;
	while (0 < indexZone && 0 < layer)
	{
		// 포지션 인덱스 생성 요령. zoneId * 100 + 1, 룰 변경시 변경되어야한다.
		//[MUSTBE] - 매직넘버 정리해야함
		positionIndex = indexZone * 100 + 1;
		auto it = std::find_if(list.begin(), list.end(), [positionIndex](const AdvDungeonPositionIndexLayerInfo& value)
		{
			return value.first == positionIndex;
		});

		if (list.end() != it)
		{
			errorPos = static_cast< UInt32 >(list.size());
			break;
		}

		// 최대 레이어는 3
		if (3 <= layer)
		{
			errorPos = static_cast< UInt32 >(list.size());
			break;
		}

		list.emplace_back(AdvDungeonPositionIndexLayerInfo(positionIndex, layer));

		cmd >> indexZone >> layer;
	}


	SystemMessage msg(L"sys", L"Msg_Error_Enter_AdvDungeon");
	if (0 < errorPos)
	{
		msg.SetParam(L"str", errorPos);
		SendSystemMsg(player, msg);
		return false;
	}

	// 최소 2층
	if (list.size() < 2)
	{
		return false;
	}

	ActionPlayerPortal* actPortal = GetEntityAction(player);
	VERIFY_RETURN(actPortal, false);

	auto it = list.begin();

	std::string value = std::to_string(it->first);
	value.resize(2);
	advType = atoi(value.c_str());

	const AdvMultistageMapSelectScript* script = SCRIPTS.GetScript< AdvMultistageMapSelectScript >();
	VERIFY_RETURN(script, false);

	for (UInt32 i = 0; i < list.size(); ++i)
	{
		const auto& elem = list[i];
		if (i == list.size() - 1)
		{
			if (!script->IsExistBossFloorPositionIndex(elem.first))
			{
				errorPos = i + 1;
				break;
			}
		}
		else
		{
			if (!script->IsExistFloorPositionIndex(elem.first))
			{
				errorPos = i + 1;
				break;
			}
		}
	}

	if (0 < errorPos)
	{
		msg.SetParam(L"str", errorPos);
		SendSystemMsg(player, msg);
		return false;
	}

	actPortal->SetForceAdventureDungeonInfo(advType, list);
	return true;
}


const Bool GMCommandSystem::onDungeonGuideMove(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt16 type = 0;
	UInt16 index = 0;

	cmd >> type >> index;

	EReqDungeonGuideMove* req = NEW EReqDungeonGuideMove;
	req->type = type;
	req->index = index;

	player->OnEvent(EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onStartDialog(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexNpc npcIndex = 0;
	UInt32 dialogIndex = 0;

	cmd >> npcIndex >> dialogIndex;

	Sector* sector = player->GetSector();
	VALID_RETURN(sector, false);

	Vector3 pos = player->GetCurrPos();

	Grid* myGrid = sector->FindGrid(pos.x, pos.y);
	VERIFY_RETURN(myGrid, false);

	NearGrids nearGrids;
	myGrid->GetNearGrids(nearGrids);

	for (const Grid* grid : nearGrids)
	{
		VALID_DO(grid, continue);

		VectorNpc vecNpc;
		grid->GetAllNpcs(sector, vecNpc);
		VALID_DO(vecNpc.size(), continue);

		for (EntityNpc* npc : vecNpc)
		{
			VALID_DO(npc && npc->IsValid(), continue);

			if (0 == npcIndex || npc->GetNpcIndex() == npcIndex)
			{
				ENtfStartDialog* ntf = NEW ENtfStartDialog;
				ntf->entityId = npc->GetId();
				ntf->dialogIndex = dialogIndex;

				npc->GetSectorAction().NearGridCast(EventPtr(ntf));
			}
		}
	}

	return true;
}

const Bool GMCommandSystem::onNoSpentMp(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring onoff;
	cmd >> onoff;

	if (L"on" == onoff)
	{
		player->GetSkillControlAction().SetNoSpentMpHp(true);
	}
	else
	{
		player->GetSkillControlAction().SetNoSpentMpHp(false);
	}

	return true;
}

const Bool GMCommandSystem::onSetBattleDistance(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Int32 distance = 0;
	cmd >> distance;

	Sector *sector = player->GetSector();
	VALID_RETURN(sector, false);
	sector->SetBattleDistance(distance);

	return true;
}

const Bool GMCommandSystem::onSuicide(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	player->GetHsm().Tran(EntityState::STATE_PLAYER_DEAD);

	return true;
}

//const Bool GMCommandSystem::onOpenAllSpecialskillSlot( EntityPlayer* player, const CommandString& )
//{
//	VERIFY_RETURN( player && player->IsValid(), false );
//
//	for ( auto i = 0; i < 3; ++i )
//	{
//		player->GetSkillManageAction().OpenSpecialSkill( static_cast< Byte >( i ) );
//	}
//
//	return true;
//}

//const Bool GMCommandSystem::onBuyPortalNeedItem( EntityPlayer* player, const CommandString& cmd )
//{
//	UInt32 advAdmissionIndex = 0;
//	UInt32 count = 0;
//	cmd >> advAdmissionIndex >> count;
//
//	count = std::max< UInt32 >( count, 1 );
//
//	ViewPosition* viewPos = GetEntityView( player );
//	ActionSector* actSector = GetEntityAction( player );
//	Sector* sector = actSector->GetSector();
//
//	VectorNpc entities;
//	sector->GetNpcs(entities );
//
//	if( entities.empty() )
//	{
//		return false;
//	}
//
//	EntityId id;
//	float distance = 99999999.0f;
//	for( auto& i : entities )
//	{
//		ActionNpcOperate* targetActObj = GetEntityAction( i );
//		if( nullptr == targetActObj )
//		{
//			continue;
//		}
//
//		if( PortalType::NONE != targetActObj->GetPortalType() )
//		{
//			float dist = 0.f;
//			viewPos->GetDistance2D( i, dist );
//
//			if( dist < distance)
//			{
//				distance = dist;
//				id = i->GetId();
//			}
//		}
//	}
//
//	if( 0 != id.Unique )
//	{
//		EReqBuyPortalNeedItem* req = NEW EReqBuyPortalNeedItem;
//
//		req->portalEntityId = id.Unique;
//		req->advAdmissionItemIndex = advAdmissionIndex;
//		req->count = count;
//
//		player->OnEvent( EventPtr( req ) );
//	}
//
//	return true;
//}

const Bool GMCommandSystem::onPortalRequest(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32	advAdmissionIndex = 0;
	UInt32	difficulty = 0;
	cmd >> advAdmissionIndex >> difficulty;

	ViewPosition* viewPos = GetEntityView(player);
	VALID_RETURN(viewPos, false);

	Sector* sector = player->GetSector();
	VALID_RETURN(sector, false);

	VectorNpc entities;
	sector->GetNpcs(entities);

	VALID_RETURN(entities.size(), false);

	IndexPortal portalIndex = 0;
	Float distance = 99999999.0f;

	for (auto& i : entities)
	{
		ActionNpcOperate* targetActObj = GetEntityAction(i);
		VALID_DO(targetActObj, continue);

		if (PortalType::NONE != targetActObj->GetPortalType())
		{
			Float dist = 0.f;
			viewPos->GetDistance2D(i, dist);

			if (dist < distance)
			{
				distance = dist;
				portalIndex = targetActObj->GetPortalIndex();
			}
		}
	}

	if (0 < portalIndex)
	{
		EczReqGamePortal* req = NEW EczReqGamePortal;
		req->advAdMissionItemIndex = advAdmissionIndex;
		req->difficulty = static_cast< UInt8 >(difficulty);
		req->portalIndex = portalIndex;

		player->OnEvent(EventPtr(req));
	}

	return true;
}


const Bool GMCommandSystem::onStorageMove(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	ItemPos pos;
	UShort toStorage = 1;

	cmd >> pos >> toStorage;

	const ItemConstPtr item = player->GetInventoryAction().GetItem( pos);
	VALID_RETURN( item, false );

	EReqStorageMove* req = NEW EReqStorageMove;
	VERIFY_RETURN(req, false);

	item->FillUp(req->src);
	if (toStorage == 1)
	{
		req->tar.SetPos(InvenType::ACCOUNT_STORAGE_START);
	}
	else
	{
		req->tar.SetPos(InvenType::BAG_START);
	}

	player->OnEvent(EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onClearQuestMission(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32	questId = 0;
	UInt32	missionIndex = 0;

	cmd >> questId >> missionIndex;

	player->GetQuestAction().ClearQuestMission(questId, static_cast< UInt8 >(missionIndex));
	return true;
}

const Bool GMCommandSystem::onSetSkillLevel(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexSkill skillId = 0;
	SkillLevel level = 0;

	cmd >> skillId >> level;

	player->GetSkillMasteryAction().SetLevel(skillId, level);
	return true;
}

const Bool GMCommandSystem::onUseSkillRune(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	ItemPos pos;
	cmd >> pos;

	player->GetUseItemAction().UseSkillRuneItem(pos, false);
	return true;
}

const Bool GMCommandSystem::onEquipSkillRune(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 slot;
	IndexSkill skillId;
	SubindexRune runeId;
	IndexItem indexItem;

	cmd >> skillId >> slot >> runeId >> indexItem;

	EReqGameSaveSkillRune* req = NEW EReqGameSaveSkillRune;

	req->skillId = skillId;
	req->runeSlot = static_cast< UInt8 >(slot);
	req->runeId = runeId;
	req->indexItem = indexItem;

	player->OnEvent(EventPtr(req));
	return true;
}

const Bool GMCommandSystem::onChangeSkillRune(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	ItemPos itemSlot;
	eSlotType slot;
	IndexSkill skillId;
	SubindexRune runeId;
	UInt32 type;

	cmd >> itemSlot >> skillId >> slot >> runeId >> type;

	EReqChangeSkillRune* req = NEW EReqChangeSkillRune;

	req->itemSlot = itemSlot;
	req->skillId = skillId;
	req->slot = slot;
	req->runeId = runeId;
	req->type = type;

	player->OnEvent(EventPtr(req));
	return true;
}

const Bool GMCommandSystem::onAddSkillRune(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	ActionPlayerSkillRune& action = player->GetSkillRuneAction();

	const ItemInfoScript* script = SCRIPTS.GetScript< ItemInfoScript >();
	VALID_RETURN(script, false);

	auto& list = script->GetSkillRuneItems();

	for (auto& i : list)
	{
		if (nullptr != action.GetActiveSkill(i.runeSkillId))
		{
			action.AddSkillRune(i.runeSkillId, SkillRuneStorageKey(i.runeSkillCategory, i.indexItem));
		}
	}

	return true;
}

const Bool GMCommandSystem::onRegisterSkill(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexSkill skillId = 0;
	Int32 equipPlace = 0;
	cmd >> skillId >> equipPlace;

	if (player->GetSkillChangerAction().Register(skillId, equipPlace))
	{
		theGameMsg.SendGameDebugMsg(player, L"succeed register changer skill [%d]\n", skillId);
		return true;
	}

	theGameMsg.SendGameDebugMsg(player, L"failed register changer skill [%d]\n", skillId);

	return true;
}

const Bool GMCommandSystem::onUnregisterSkill(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexSkill skillId = 0;
	cmd >> skillId;

	if (player->GetSkillChangerAction().Unregister(skillId))
	{
		theGameMsg.SendGameDebugMsg(player, L"succeed unregister changer skill [%d]\n", skillId);
		return true;
	}

	theGameMsg.SendGameDebugMsg(player, L"failed unregister changer skill [%d]\n", skillId);

	return false;
}


const Bool GMCommandSystem::onPetTest(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring testType;
	UInt32 index = 0, param1 = 0, param2 = 0;
	cmd >> testType >> index >> param1 >> param2;

	ErrorPet::Error eError = ErrorPet::SUCCESS;

	ActionPlayerPetManage& actPet = player->GetPetManageAction();

	PetId petId(player->GetCharId(), index);
	PetData data;
	if (testType == L"register")
	{
		eError = actPet.Register(param1, data);
		VERIFY_RETURN(eError == ErrorPet::SUCCESS, false);
	}

	else if (testType == L"spawn")
	{
		PetSpawnState state;
		state.state = PetSpawnState::SPAWN;
		state.petUnique = petId.unique;

		eError = actPet.UpdateSpawnState(state);
		if (eError != ErrorPet::SUCCESS)
		{
			theGameMsg.SendGameDebugMsg(player, L"failed to update spawn [petId:%d][errorCode:%d]\n", index, eError);
			return false;
		}
	}
	else if (testType == L"despawn")
	{
		PetSpawnState state;
		state.state = PetSpawnState::DESPAWN;
		state.petUnique = petId.unique;

		eError = actPet.UpdateSpawnState(state);
		if (eError != ErrorPet::SUCCESS)
		{
			theGameMsg.SendGameDebugMsg(player, L"failed to update despawn [petId:%d][errorCode:%d]\n", index, eError);
			return false;

		}
	}
	else if (testType == L"rename")
	{
		std::wstring name = std::to_wstring(param1);
		eError = actPet.Rename(petId, name);
		if (eError != ErrorPet::SUCCESS)
		{
			theGameMsg.SendGameDebugMsg(player, L"failed to rename [petId:%d][errorCode:%d]\n", index, eError);
			return false;
		}
	}
	else if (testType == L"unregister")
	{
		eError = actPet.Unregister(petId);
		if (eError != ErrorPet::SUCCESS)
		{
			theGameMsg.SendGameDebugMsg(player, L"failed to Unregister [petId:%d][errorCode:%d]\n", index, eError);
			return false;
		}
	}
	else if (testType == L"grow")
	{
		EReqGamePetGrow* req = NEW EReqGamePetGrow;
		EventPtr reqPtr(req);

		req->petUnique = petId.unique;
		PetPtr petptr = actPet.GetPet(req->petUnique);

		UInt64Vector vec;
		actPet.GetPetIds(vec);

		UInt32 max = GetRandBetween(0, 5);
		UInt32 count = 0;
		for (auto it : vec)
		{
			if (count >= max)
				break;

			if (it != req->petUnique)
			{
				req->materialPetIds.push_back(it);
				count++;
			}
		}
		theZoneHandler.Notify(player, reqPtr);
	}
	else if (testType == L"upgrade")
	{
		EReqPetUpgrade* req = NEW EReqPetUpgrade;
		EventPtr reqPtr(req);

		req->petUnique = petId.unique;
		PetPtr petptr = actPet.GetPet(req->petUnique);
		const PetShopPack &pack = SCRIPTS.GetPetShopPack();

#ifdef __Patch_Pet_Upgrade_Grade_check_by_kangms_2019_3_22
		const auto upgradeElem = pack.PetUpgradePacks.find(PetUpgrade::Key(petptr->GetElem()->grade, petptr->GetElem()->tier + 1));
#else//__Patch_Pet_Upgrade_Grade_check_by_kangms_2019_3_22
		const auto upgradeElem = pack.PetUpgradePacks.find(petptr->GetElem()->tier + 1);
#endif//__Patch_Pet_Upgrade_Grade_check_by_kangms_2019_3_22

		if (pack.PetUpgradePacks.end() == upgradeElem)
			return false;

		UInt64Vector vec;
		actPet.GetPetIds(vec);

		UInt32 count = 0;
		for (auto it : vec)
		{
			if (count <= upgradeElem->second.materialPetAmount)
				break;

			if (it != req->petUnique)
			{
				req->materialPets.push_back(it);
				count++;
			}
		}
		theZoneHandler.Notify(player, reqPtr);
	}
	else if (testType == L"fellow")
	{
		EReqGamePetChangeFellow* req = NEW EReqGamePetChangeFellow;
		EventPtr reqPtr(req);
		req->petUnique = petId.unique;
		req->fellowSlotNumber = static_cast<Byte>(param1);
		theZoneHandler.Notify(player, reqPtr);
	}
	else
	{
		theGameMsg.SendGameDebugMsg(player, L"invalid test type [petId:%d]\n", index);
		return false;
	}

	theGameMsg.SendGameDebugMsg(player, L"succeed [petId:%d]\n", index);
	return true;
}

const Bool GMCommandSystem::onEndlessNextStage(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 index = 0;
	cmd >> index;

	Sector* sector = player->GetSector();
	VALID_RETURN(sector, false);

	sector->GetSectorController().GetControllerAction().SetKeyValue("endlessTowerStage", index);
	return true;
}

#ifdef __Patch_Tower_of_dawn_eunseok_2018_11_05
const Bool GMCommandSystem::onChance(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt8 index = 0;
	cmd >> index;

	Sector* sector = player->GetSector();
	VALID_RETURN(sector, false);

	EReqTowerOfDawnUsePlayChance* req = NEW EReqTowerOfDawnUsePlayChance;
	EventPtr reqPtr(req);
	req->chanceIndex = index;
	theZoneHandler.Notify(player, reqPtr);

	return true;
}
const Bool GMCommandSystem::onTowerOfDawnStage(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt16 index = 0;
	cmd >> index;

	Sector* sector = player->GetSector();
	VALID_RETURN(sector, false);

	ActionSector& actionSector = player->GetSectorAction();

	auto ActionSectorController = actionSector.GetActionSectorController();
	VERIFY_RETURN(ActionSectorController, false);

	TowerOfDawnProcessor* processor = dynamic_cast<TowerOfDawnProcessor*>(ActionSectorController->GetSectorProcess());
	VERIFY_RETURN(processor, false);

	processor->DevChangeStage(index);
	return true;
}

const Bool GMCommandSystem::onRewardPoint(EntityPlayer* player, const CommandString& cmd)
{

	VERIFY_RETURN(player && player->IsValid(), false);

	RewardPoint index = 0;
	cmd >> index;

	player->GetPlayerAction().UpdateTowerOfDawnRewardPoint(index);
	return true;
}
#endif //__Patch_Tower_of_dawn_eunseok_2018_11_05

const Bool GMCommandSystem::onAddGreenZen(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	GreenZen count = 0;
	cmd >> count;

	VALID_RETURN(0 < count, false);

	if (player->CheckGreenZen(count, ChangedMoney::ADD) == false)
	{
		SendSystemMsg(player, SystemMessage(L"sys", L"Msg_GreenZen_Err_Max"));
		return false;
	}

	player->AddGreenZen(count);
	player->SendChangeGreenZen(count);

	return true;
}

const Bool GMCommandSystem::onSoulSKillTest(EntityPlayer* player, const CommandString& cmd)
{

#ifdef __Reincarnation__jason_180628__
	return player->GetCharSoulInfo().OnGmCommandSoul(cmd);
#else
	std::wstring testType;
	IndexSoulPassive indexSoulPassive = 0;
	SoulPoint soulPoint = 0;

	cmd >> testType >> indexSoulPassive >> soulPoint;

	if (testType == L"reset")
	{
		auto actSoulSkill = player->GetAction<ActionPlayerSoulSkillManage>();
		if (false == actSoulSkill->ResetPoint())
		{
			theGameMsg.SendGameDebugMsg(player, L"failed to reset Point\n");
			return false;
		}
	}
	else if (testType == L"distr")
	{
		SoulPassivePointInfo info;
		info.indexSoulPassive = indexSoulPassive;
		info.point = soulPoint;

		SoulPassivePointInfos infos = { info };
		auto actSoulSkill = player->GetAction<ActionPlayerSoulSkillManage>();
		if (false == actSoulSkill->DistrPoint(std::move(infos)))
		{
			theGameMsg.SendGameDebugMsg(player, L"failed to distribute point\n");
			return false;
		}
	}
	else
	{
		theGameMsg.SendGameDebugMsg(player, L"invalid test type\n");
		return false;
	}

	theGameMsg.SendGameDebugMsg(player, L"succeed\n");

	return true;

#endif
}


const Bool GMCommandSystem::onCharMoveGoodPosition(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	EczReqCharMoveGoodPosition::MoveType eMoveType;
	cmd >> (Byte&)eMoveType;

	EczReqCharMoveGoodPosition* req = NEW EczReqCharMoveGoodPosition;
	EventPtr reqPtr(req);
	req->eMoveType = eMoveType;
	theZoneHandler.Notify(player, reqPtr);

	return true;
}


const Bool GMCommandSystem::onUseItemRecoveryEntrance(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	ItemPos pos;
	cmd >> pos;

	const ItemConstPtr item = player->GetInventoryAction().GetItem( pos);
	VERIFY_RETURN(item, false);
	VERIFY_RETURN(ItemDivisionType::RECOVERY_ENTRANCE_COUNT == item->GetDivision(), false);

	EReqUseRecoveryEntranceItem* req = NEW EReqUseRecoveryEntranceItem;
	req->item = pos;
	player->OnEvent(EventPtr(req));
	return true;
}

const Bool GMCommandSystem::onRemoveUseItemLimit(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 group;
	cmd >> group;

	player->GetUseItemAction().CheatRemoveLimit(static_cast< UInt8 >(group));
	return true;
}

const Bool GMCommandSystem::onUpdateCombatPower(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	player->GetAction< ActionPlayerCombatPower >()->Update();
	return true;
}

const Bool GMCommandSystem::onBloodCastleComplete(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Sector* sector = player->GetSector();
	VERIFY_RETURN(sector, false);

	if (sector->GetId().GetZoneIndex() == 9202) // 블캐
	{
		sector->GetSectorController().GetControllerAction().OnEvent(DungeonEventType::CHEAT, player, nullptr);
		return true;
	}

	return false;
}

const Bool GMCommandSystem::onAchievementComplete(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexAchievement achievementIndex = 0;
	cmd >> achievementIndex;

	auto elem = SCRIPTS.GetAchievement(achievementIndex);
	VALID_RETURN(elem, false);

	player->GetAchievementAction().CheatComplete(elem);
	return true;
}

const Bool GMCommandSystem::onAchievementDelete(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexAchievement achievementIndex = 0;
	cmd >> achievementIndex;

	auto elem = SCRIPTS.GetAchievement(achievementIndex);
	VALID_RETURN(elem, false);

	player->GetAchievementAction().CheatDelete(elem);
	return true;
}

const Bool GMCommandSystem::onAchievementGrade(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt16 grade = 0;
	cmd >> grade;

	auto elem = SCRIPTS.GetAchievementGrade(static_cast< UInt8 >(grade));
	VALID_RETURN(elem, false);

	player->GetAchievementAction().CheatAchievementGrade(elem);
	return true;
}

const Bool GMCommandSystem::onUseTownPortalItem(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	ItemPos pos;
	std::wstring defaultSelected;

	cmd >> pos >> defaultSelected;

	EReqUseTownPortalItem* req = NEW EReqUseTownPortalItem;
	EventPtr reqPtr(req);
	req->slotInfo = pos;
	req->selectDefault = defaultSelected == L"true" ? true : false;
	theZoneHandler.Notify(player, reqPtr);

	return true;
}

const Bool GMCommandSystem::onCustomizingItem(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	ItemSlotInfo tarPlace, srcPlace;
	std::wstring insertOrRemove;

	cmd >> insertOrRemove >> tarPlace >> srcPlace;

	EventPtr reqPtr;
	if (insertOrRemove == L"insert")
	{
		EReqItemInsertCustomizing* req = NEW EReqItemInsertCustomizing;
		reqPtr = req;
		req->srcItem = srcPlace;
		req->tarItem = tarPlace;
	}
	else if (insertOrRemove == L"remove")
	{
		EReqItemRemoveCustomizing* req = NEW EReqItemRemoveCustomizing;
		reqPtr = req;
		req->tarItem = tarPlace;
	}

	theZoneHandler.Notify(player, reqPtr);

	return true;
}

const Bool GMCommandSystem::onColosseumTest(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32	command;
	cmd >> command;

	Sector* sector = player->GetSector();
	VERIFY_RETURN(sector, false);

	ActionSectorController& asc = sector->GetSectorController().GetControllerAction();

	ColosseumProcessor* colosseumProcessor = dynamic_cast<ColosseumProcessor*>(asc.GetSectorProcess());
	VALID_RETURN(colosseumProcessor, true);

	switch (command)
	{
	case 3:
	{
		VALID_RETURN(asc.IsColosseum(), false);

		EReqGameColosseumConsecutiveBattle* req = NEW EReqGameColosseumConsecutiveBattle;
		player->OnEvent(EventPtr(req));
	} break;
	case 4:
	{
		VALID_RETURN(asc.IsColosseum(), false);

		EReqGameColosseumBattleLeave* req = NEW EReqGameColosseumBattleLeave;
		player->OnEvent(EventPtr(req));
	} break;
	case 5:
	{
		VALID_RETURN(false == asc.IsColosseum(), false);

		EReqGameEnterDungeonType* req = NEW EReqGameEnterDungeonType;
		req->dungeonType = DungeonType::COLOSSEUM;
		player->OnEvent(EventPtr(req));
	}break;
	case 6:
	{
		VALID_RETURN(asc.IsColosseum(), false);
		colosseumProcessor->DevResearchBattleList();
	}break;
	case 7:
	{
		EReqGameColosseumReSearchPopup* req = NEW EReqGameColosseumReSearchPopup;
		EventPtr reqPtr(req);
		theZoneHandler.Notify(player, reqPtr);
	}break;

	}


	return true;
}

const Bool GMCommandSystem::onColosseumSearch(EntityPlayer* player, const CommandString& cmd)
{
	Int32	command = 0;
	cmd >> command;


	VERIFY_RETURN(player && player->IsValid(), false);
	Sector *sector = player->GetSector();
	VALID_RETURN(sector, false);

	ActionSectorController& asc = sector->GetSectorController().GetControllerAction();

	ColosseumProcessor* colosseumProcessor = dynamic_cast<ColosseumProcessor*>(asc.GetSectorProcess());
	VALID_RETURN(colosseumProcessor, true);

	VALID_RETURN(asc.IsColosseum(), false);

	// 그냥 재탐색
	if (command == 0)
	{
		colosseumProcessor->DevResearchBattleList();
	}
	else if (command == 1)
	{
		colosseumProcessor->DevResearchBattleListByBlackPhantom();
	}


	theGameMsg.SendGameDebugMsg(player, L"colsearch on \n");
	return true;
}
const Bool GMCommandSystem::onColosseumStop(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);
	Sector *sector = player->GetSector();
	VALID_RETURN(sector, false);

	ActionSectorController& asc = sector->GetSectorController().GetControllerAction();

	ColosseumProcessor* colosseumProcessor = dynamic_cast<ColosseumProcessor*>(asc.GetSectorProcess());
	VALID_RETURN(colosseumProcessor, true);

	VALID_RETURN(asc.IsColosseum(), false);
	colosseumProcessor->DevStopTimer(true);
	theGameMsg.SendGameDebugMsg(player, L"colpause on \n");
	return true;
}

const Bool GMCommandSystem::onAddColosseumWP(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Int32	command;
	cmd >> command;

	ActionPlayerColosseum&	apc = player->GetColosseumAction();

	if (command > 0)
	{
		apc.AddColosseumPoint(command);
	}
	else
	{
		apc.RemoveColosseumPoint(std::abs(command));
	}

	apc.SendColosseumInfoToClient();

	return true;

}
const Bool GMCommandSystem::onColosseumNpcSpawn(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Sector* sector = player->GetSector();
	VERIFY_RETURN(sector, false);

	Vector3 pos = player->GetCurrPos();
	theSpawnSystem.GetRandomPostion(pos, 0, 200, sector);

	auto& elem = SCRIPTS.GetColosseumInfo();

	ColosseumCharInfo info;
	player->GetPlayerCognitionAction().GetCharInfo(info.charInfo);

	auto script = SCRIPTS.GetNpcInfoScript(elem.aipcNpcIndex);
	VERIFY_RETURN(script, false);

	EntityNpc* npc = nullptr;
	SpawnSystem::BaseSpawnInfo baseSpawnInfo;
	baseSpawnInfo.npcIndex = script->index;
	baseSpawnInfo.level = player->GetLevel();
	baseSpawnInfo.sector = sector;
	baseSpawnInfo.spawnPos = pos;
	baseSpawnInfo.aiIndex = script->aiIndex;
	baseSpawnInfo.dir = 0.f;
	baseSpawnInfo.charInfo = info;

	MU2_ASSERT(theSpawnSystem.SpawnGeneralNpc(&baseSpawnInfo, &npc, true));

	return true;
}



const Bool GMCommandSystem::onChangeAccountGrade(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	AccountGrade::Enum accountGrade = AccountGrade::NONE;
	cmd >> (Byte&)accountGrade;

	if (SERVER.BlockDevOnly())
	{
		theGameMsg.SendGameDebugMsg(player, L"blocked development login.");
		return false;
	}

	player->SetAccountGrade(accountGrade);

	{
		EzwNtfAccountGradeChanged* ntf = NEW EzwNtfAccountGradeChanged;
		ntf->accountGrade = player->GetAccountGrade();
		ntf->accountGuid = player->GetAccountId();
		SERVER.SendToWorldServer(player, EventPtr(ntf));
	}

	{
		EReqDbAccountGradeChanged* ntf = NEW EReqDbAccountGradeChanged;
		ntf->accountGrade = player->GetAccountGrade();
		ntf->accountGuid = player->GetAccountId();
		SERVER.SendToDb(player, EventPtr(ntf));
	}

	return true;
}

const Bool GMCommandSystem::onRequestSurvey(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 surveyindex = 0;
	cmd >> surveyindex;

	player->GetSurveyAction().OnCheatRequestSurvey(surveyindex);
	return true;
}

const Bool GMCommandSystem::onDeleteCompletedSurvey(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);
	player->GetSurveyAction().OnCheatDeleteSurvey();

	return true;
}

const Bool GMCommandSystem::onEquipedItemEnchant(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	EnchantLevel tryEnchantLevel = 0;
	cmd >> tryEnchantLevel;

	auto scriptItemOption = SCRIPTS.GetScript<ItemOptionScript>();
	VALID_RETURN(scriptItemOption, false);

	if (player->GetAccountGrade() < AccountGrade::DEV)
	{
		MU2_TRACE_LOG(LogCategory::GMCOMMAND, L"GMCommandSystem::onEquipedItemEnchant >> Permission Denied. accountId:(%u), charId:(%u), charNickName:(%s), IpAddress:(%s)",
			player->GetAccountId(), player->GetCharId(), player->GetCharName().c_str(), player->GetIp().c_str());
		return false;
	}

	MU2_TRACE_LOG(LogCategory::GMCOMMAND, L"GMCommandSystem::onEquipedItemEnchant >> Success Cheat Activate. accountId:(%u), charId:(%u), charNickName:(%s), IpAddress:(%s)",
		player->GetAccountId(), player->GetCharId(), player->GetCharName().c_str(), player->GetIp().c_str());

	return player->GetEquipInven().EnchantAllByGM(tryEnchantLevel);
}

const Bool GMCommandSystem::onSortBag(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	eInvenType invenBegin = InvenType::NONE, invenEnd = InvenType::NONE;
	cmd >> invenBegin >> invenEnd;

	EReqSortBagItems* req = NEW EReqSortBagItems;
	EventPtr reqPtr(req);
	req->eInvenBegin = invenBegin;
	req->eInvenEnd = invenEnd;
	theZoneHandler.Notify(player, reqPtr);

	return true;
}
const Bool GMCommandSystem::onWorldOpen(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	WorldId worldId = InvalidWorldId;
	cmd >> worldId;

	EztNtfWorldOpen* req = NEW EztNtfWorldOpen;
	req->worldId = worldId;
	SERVER.SendToCenter(EventPtr(req));
	return true;
}

const Bool GMCommandSystem::onWorldClose(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	WorldId worldId = InvalidWorldId;
	cmd >> worldId;

	EztNtfWorldClose* req = NEW EztNtfWorldClose;
	req->worldId = worldId;
	SERVER.SendToCenter(EventPtr(req));
	return true;
}


const Bool GMCommandSystem::onSpeedHack(EntityPlayer* owner, const CommandString& cmd)
{
	VERIFY_RETURN(owner && owner->IsValid(), false);

	std::wstring onOff;
	cmd >> onOff;

	if (L"on" == onOff)
	{
		EztNtfSpeedHack* req = new EztNtfSpeedHack;
		req->on = true;
		SERVER.SendToCenter(EventPtr(req));
	}
	else if (L"off" == onOff)
	{
		EztNtfSpeedHack* req = new EztNtfSpeedHack;
		req->on = false;
		SERVER.SendToCenter(EventPtr(req));
	}

	return true;
}
const Bool GMCommandSystem::onSpeedHackDealy(EntityPlayer* owner, const CommandString& cmd)
{
	VERIFY_RETURN(owner && owner->IsValid(), false);

	std::wstring onOff;
	UInt32 min, max;
	cmd >> min >> max;

	if (min > max)
	{
		UInt32 temp = min;
		min = max;
		max = temp;
	}
	SERVER.SetMovePacketDealy(min, max);

	return true;
}
const Bool GMCommandSystem::onStopPacket(EntityPlayer* owner, const CommandString& cmd)
{
	VERIFY_RETURN(owner && owner->IsValid(), false);
	Int32 count = 0;
	cmd >> count;

	for (int i = 0; i < count; i++)
	{
		EReqGameMoveStart* move = new EReqGameMoveStart;
		move->current = owner->GetCurrPos();			  ///이동시 클라이언트상 현재 좌표
		move->destination = owner->GetCurrPos();			  ///이동시 클라이언트상 현재 좌표
		move->currentTimeStampTick = GetTickCount();  /// ENtfMoveSyncTick::timeStampTick + 증가 된 Tick

		theZoneHandler.Notify(owner, EventPtr(move));

		EReqGameMoveStop* stop = new EReqGameMoveStop;
		stop->stopPosition = owner->GetCurrPos();
		stop->dir = 0.0f;
		stop->currentTimeStampTick = GetTickCount();  /// ENtfMoveSyncTick::timeStampTick + 증가 된 Tick

		theZoneHandler.Notify(owner, EventPtr(stop));

	}

	return true;
}
const Bool GMCommandSystem::onMoveNpc(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexNpc npcIndex = 0;
	Vector3 dst;
	cmd >> npcIndex >> dst.x >> dst.y >> dst.z;

	auto sector = player->GetSector();
	VALID_RETURN(sector, false);

	Vector3 myPos = player->GetCurrPos();
	Grid* myGrid = sector->FindGrid(myPos.x, myPos.y);
	VALID_RETURN(myGrid, false);

	VectorNpc entities;
	myGrid->GetAllNpcs(sector, entities);
	VALID_RETURN(entities.size(), false);

	for (EntityNpc* npc : entities)
	{
		VALID_DO(npc && npc->IsValid(), continue);

		AttributeNpc& attrNpc = npc->GetNpcAttr();

		VALID_DO(npcIndex == attrNpc.GetNpcIndex(), continue);

		attrNpc.movetoEventuallyPosition.Set(dst.x, dst.y, dst.z);
		npc->GetHsm().Tran(EntityState::STATE_NPC_MOVETO_EVENTUALLY);
	}

	return true;
}

const Bool GMCommandSystem::onResetCoachMark(EntityPlayer* player, const CommandString & cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	EReqDbDeleteCoachMark* req = NEW EReqDbDeleteCoachMark;
	req->charId = player->GetCharId();
	SERVER.SendToDb(player, EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onGrowItem(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	ItemPos itemPos;
	cmd >> itemPos;

	const ItemConstPtr foundItem = player->GetInventoryAction().GetItem(itemPos);
	VERIFY_RETURN(foundItem, false);

	EReqItemGrow* req = NEW EReqItemGrow;
	EventPtr reqPtr(req);
	req->item = foundItem->GetSlotInfo();
	theZoneHandler.Notify(player, reqPtr);

	return true;
}


const Bool GMCommandSystem::onItemExp(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	ItemSlotInfo slotInfo;
	UInt32 exp;
	cmd >> slotInfo >> exp;
		
	const ItemConstPtr item = player->GetInventoryAction().GetItem(slotInfo);
	VERIFY_RETURN(item, false);

	ItemGrowInfoKey currItemGrowInfoKey(item->GetDivision(), item->GetGrade(), item->GetTier(), item->GetEnchantRawLevel());
	const ItemGrowInfo* itemGrowInfoElem = SCRIPTS.GetItemGrowInfo(currItemGrowInfoKey);
	VERIFY_RETURN(itemGrowInfoElem, false);

	ItemData enchantCloned = item->Clone();
	enchantCloned.growExp += ItemGrowExpCalculator::GetItemGrowCurrentExpRate(exp, itemGrowInfoElem->totalExp);
	enchantCloned.growExp = std::min(enchantCloned.growExp, ItemGrowExpCalculator::MaxGrowExpPoint);

	std::set<ItemId> targetItems;
	targetItems.insert(item->GetItemId());
	Materials mts;
	ItemGrowTransaction* trans = NEW ItemGrowTransaction(__FUNCTION__, __LINE__, player, LogCode::E_ITEM_ENCHANT, 0, 0, targetItems, mts);
	TransactionPtr transPtr(trans);
	{
		ItemBag bag(*player);

		VERIFY_RETURN(bag.Update(item, enchantCloned), false);
		VERIFY_RETURN(bag.Write(transPtr), false);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), false);
	}

	return true;

}

const Bool GMCommandSystem::onSetSearchInterval(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 searchInterval;
	cmd >> searchInterval;

	StateNpcPeace::SetSearchEnemyInterval(searchInterval);

	return true;
}

const Bool GMCommandSystem::onKnightageRequirement(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	EReqKnightageGetCreationRequirement* req = NEW EReqKnightageGetCreationRequirement;
	player->OnEvent(EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onKnightageCheckName(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	EReqKnightageCheckKnightageName* req = NEW EReqKnightageCheckKnightageName;
	req->SetIdxBySessionKey(getSessionKey(player));
	cmd >> req->knightageName;
	player->OnEvent(EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onKnightageCreate(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	EReqKnightageCreate* req = NEW EReqKnightageCreate;
	req->SetIdxBySessionKey(getSessionKey(player));
	cmd >> req->knightageName;
	player->OnEvent(EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onKnightageOpenStorage(EntityPlayer* player, const CommandString &)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	EReqKnightageOpenStorage* req = NEW EReqKnightageOpenStorage;
	req->SetIdxBySessionKey(getSessionKey(player));
	player->OnEvent(EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onKnightageCloseStorage(EntityPlayer* player, const CommandString &)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	EReqKnightageCloseStorage* req = NEW EReqKnightageCloseStorage;
	req->SetIdxBySessionKey(getSessionKey(player));
	player->OnEvent(EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onKnightageAddContributionPoint(EntityPlayer* player, const CommandString & cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 contributionPoint = 0;
	cmd >> contributionPoint;

	// 전공 포인트를 증가시킨다.	
	{
		if (player->AddContributionPoint(contributionPoint))
		{
			player->SendChangedContributionPoint(contributionPoint, ChangedMoney::ADD);

			ENtfKnightageReceiveContributionPoint* ntf2 = NEW ENtfKnightageReceiveContributionPoint;
			ntf2->contributionPoint = contributionPoint;
			SERVER.SendToClient(player, EventPtr(ntf2));
		}
	}

	return true;
}

const Bool GMCommandSystem::onKnightageClearStorage(EntityPlayer* player, const CommandString &)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	// 기사단 창고에 있는 아이템을 모두 삭제한다.
	// 기사단 창고를 열고 있어야 한다.
	player->GetKnightageStorageAction().RemoveAllStorageItems();

	return true;
}

const Bool GMCommandSystem::onKnightageGetMyKnightageId(EntityPlayer* player, const CommandString &)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	theGameMsg.SendGameDebugMsg(player, L"knightageId : %d \n", player->GetKnightageId());

	return true;
}


const Bool GMCommandSystem::onArtifact(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring command;
	std::array< UInt32, 3 > value;
	cmd >> command >> value[0] >> value[1] >> value[2];

	ActionPlayerArtifact& actArtifact = player->GetArtifactAction();

	if (command == L"exp")
	{
		actArtifact.AddExp(value[0]);
	}
	else if (command == L"level")
	{
		actArtifact.TestSetLevel(static_cast<Byte>(value[0]));
	}
	else if (command == L"create")
	{
		actArtifact.CreateResult(value[0], static_cast<ArtifactLevel>(value[1]));
	}
	else if (command == L"createall")
	{
		auto artifactInfoScript = SCRIPTS.GetScript<ArtifactInfoScript>();
		VALID_RETURN(artifactInfoScript, false);

		const auto& elems = artifactInfoScript->Get();
		for (const auto& elem : elems)
		{
			actArtifact.CreateResult(elem.first, static_cast<ArtifactLevel>(value[0]));
		}
	}
	else if (command == L"openslot")
	{
		actArtifact.TestOpenSlot(ArtifactSlotInfoKey(ArtifactSlotCategory::EQUIP, static_cast<Byte>(value[0])));
	}
	else if (command == L"openslotall")
	{
		actArtifact.TestOpenSlotAll(ArtifactSlotCategory::EQUIP);
	}
	else if (command == L"clear")
	{
		actArtifact.TestClearArtiafcts();
	}

	else if (command == L"list")
	{
		actArtifact.TestArtifactList();
	}
	else if (command == L"recall")
	{
		actArtifact.TestRecall(static_cast<UInt32>(value[0]));
	}
	else if (command == L"recallall")
	{
		actArtifact.TestRecallAll();
	}
	else if (command == L"recallcancel")
	{
		actArtifact.TestRecallCancel(static_cast<UInt32>(value[0]));
	}
	else if (command == L"recalllevelmove")
	{
		actArtifact.TestArtifactLevelMove(static_cast<UInt32>(value[0]), static_cast<UInt32>(value[1]));
	}
	else if (command == L"recallbyfile")
	{
		actArtifact.TestRecallAllByFile();
	}
#ifdef __Renewal_Artifact_Level_Transfer_by_kangms_181001
	else if (command == L"leveltransfer")
	{
#ifdef	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320
		actArtifact.TestArtifactLevelTransferByNPC(static_cast<UInt32>(value[0]), static_cast<UInt32>(value[1]), CostPack(), false);
#else	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320
		actArtifact.TestArtifactLevelTransferByNPC(static_cast<UInt32>(value[0]), static_cast<UInt32>(value[1]), false);
#endif	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320
	}
#endif//__Renewal_Artifact_Level_Transfer_by_kangms_181001

	return true;
}

const Bool GMCommandSystem::onArtifactAlchemy(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring command;
	std::array< UInt32, 3 > value;
	cmd >> command >> value[0] >> value[1] >> value[2];

	ActionPlayerArtifact& actArtifact = player->GetArtifactAction();

	if (command == L"complete")
	{
		actArtifact.TestCompleteAlchemy();
	}
	else if (command == L"count")
	{
		actArtifact.TestClearAlchemyBoostCount(static_cast<Byte>(value[0]));
	}
	else if (command == L"openslot")
	{
		actArtifact.TestOpenSlot(ArtifactSlotInfoKey(ArtifactSlotCategory::ALCHEMY, static_cast<Byte>(value[0])));
	}
	else if (command == L"openslotall")
	{
		actArtifact.TestOpenSlotAll(ArtifactSlotCategory::ALCHEMY);
	}
	return true;
}
const Bool GMCommandSystem::onMaze(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 type;
	UInt32 value1;
	UInt32 value2;
	cmd >> type >> value1 >> value2;

	if (type == 1) // 포탈이동 수락
	{
		EReqSelectMazeChangePopup* req = NEW EReqSelectMazeChangePopup;
		EventPtr reqPtr(req);
		req->isOK = true;
		theZoneHandler.Notify(player, reqPtr);
	}

	else if (type == 2) // 포탈이동 거절 
	{
		EReqSelectMazeChangePopup* req = NEW EReqSelectMazeChangePopup;
		EventPtr reqPtr(req);
		req->isOK = false;
		theZoneHandler.Notify(player, reqPtr);
	}
	else if (type == 3) // 미궁 포인트 변경 
	{
		auto ActionSectorController = player->GetSectorAction().GetActionSectorController();
		VERIFY_RETURN(ActionSectorController, false);

		MazeProcessor* mazeProcessor = dynamic_cast<MazeProcessor*>(ActionSectorController->GetSectorProcess());
		VERIFY_RETURN(mazeProcessor, false);

		mazeProcessor->DevChangeChargingPoint(value1);

	}
	else if (type == 4) // 미궁 완료레벨 변경 
	{
		ActionSector* actionSector = GetEntityAction(player);
		VERIFY_RETURN(actionSector, false);

		if (value1 <= 0)
		{
			value1 = 1;
		}
		else if (value1 >= SCRIPTS.GetMaxMazeDifficult())
		{
			value1 = SCRIPTS.GetMaxMazeDifficult();
		}

		auto ActionSectorController = actionSector->GetActionSectorController();
		VERIFY_RETURN(ActionSectorController, false);

		MazeProcessor* mazeProcessor = dynamic_cast<MazeProcessor*>(ActionSectorController->GetSectorProcess());
		VERIFY_RETURN(mazeProcessor, false);

		mazeProcessor->DevChangeDifficult(static_cast<EnchantLevel>(value1));
	}
	else if (type == 5)
	{
		ActionSector* actionSector = GetEntityAction(player);
		VERIFY_RETURN(actionSector, false);

		auto ActionSectorController = actionSector->GetActionSectorController();
		VERIFY_RETURN(ActionSectorController, false);

		MazeProcessor* mazeProcessor = dynamic_cast<MazeProcessor*>(ActionSectorController->GetSectorProcess());
		VERIFY_RETURN(mazeProcessor, false);

		mazeProcessor->DevSelectBlockAndLayer(player, value1, value2);

		/*EReqGotoMazeBossBlock* req = NEW EReqGotoMazeBossBlock;

		ViewEntity* view = GetEntityView(player);
		MU2_ASSERT(view);
		AttributePlayer * attr = GetEntityAttribute(player);
		MU2_ASSERT(attr);

		view->GetWorldSession(req->internal_index);
		view->GetSessionKey(req->GetIdxAsSessionKey());
		req->key = attr->charId;

		theZoneHandler.Notify(EventPtr(req));*/
	}
	else if (type == 6)
	{
		ActionSector* actionSector = GetEntityAction(player);
		VERIFY_RETURN(actionSector, false);

		auto ActionSectorController = actionSector->GetActionSectorController();
		VERIFY_RETURN(ActionSectorController, false);

		MazeProcessor* mazeProcessor = dynamic_cast<MazeProcessor*>(ActionSectorController->GetSectorProcess());
		VERIFY_RETURN(mazeProcessor, false);

		mazeProcessor->DevChangeBlacRoomChargingPoint(value1);
	}
	return true;
}
const Bool GMCommandSystem::onMazeTime(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Float time;
	cmd >> time;

	ActionSector* actionSector = GetEntityAction(player);
	VERIFY_RETURN(actionSector, false);

	auto ActionSectorController = actionSector->GetActionSectorController();
	VERIFY_RETURN(ActionSectorController, false);

	MazeProcessor* mazeProcessor = dynamic_cast<MazeProcessor*>(ActionSectorController->GetSectorProcess());
	VERIFY_RETURN(mazeProcessor, false);

	mazeProcessor->DevMazeTimer(static_cast<Int32>(time * 1000));
	theGameMsg.SendGameDebugMsg(player, L"mazetime(%0.1f) on \n", time);
	return true;
}
const Bool GMCommandSystem::onMazeDifficulty(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	DifficultyLevel level;
	cmd >> level;

	if (level >= SCRIPTS.GetMaxMazeDifficult())
	{
		level = SCRIPTS.GetMaxMazeDifficult();
	}
	else if (level < 0)
	{
		level = 0;
	}

	player->GetPlayerAction().UpdateMazeLevel(level);

	theGameMsg.SendGameDebugMsg(player, L"difficulty(%d) on \n", level);
	return true;
}

const Bool GMCommandSystem::onAddPetPoint(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 point = 0;
	cmd >> point;

	player->GetPetManageAction().AddGrowPoint(point);
	return true;
}

const Bool GMCommandSystem::onAddRiftPoint(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt64 riftPointCount = 0;
	cmd >> riftPointCount;

	VALID_RETURN(0 < riftPointCount, false);

	player->AddRiftPoint(riftPointCount);
	player->SendChangeRiftPoint(riftPointCount, ChangedMoney::ADD);

	return true;
}

const Bool GMCommandSystem::onSubRiftPoint(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt64 riftPointCount = 0;
	cmd >> riftPointCount;

	VALID_RETURN(0 < riftPointCount, false);

	player->SubRiftPoint(riftPointCount);
	player->SendChangeRiftPoint(riftPointCount, ChangedMoney::SUB);

	return true;
}

const Bool GMCommandSystem::onGuide(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 type;
	cmd >> type;

	if (player->GetTutorialState() != TutorialState::PLAYING)
		return false;

	Sector* sector = player->GetSector();
	VERIFY_RETURN(sector, false);


	IndexPosition tutorialPositionIndex = 0;
	const TutorialScript* tutorialScript = SCRIPTS.GetScript<TutorialScript>();

	if (tutorialScript)
	{
		const TutorialElem* elem = tutorialScript->Get(player->GetRaceType());
		if (elem)
		{
			tutorialPositionIndex = elem->positionIndex;
		}
	}


	const InstanceDungeonInfo& dungeonInfo = sector->GetInstanceDungeonInfo();

	EzwReqChangeMap* reqToWorld = NEW EzwReqChangeMap;
	reqToWorld->toIndexPositionMovable = tutorialPositionIndex;
	reqToWorld->toInsInfoList.push_back(dungeonInfo);
	reqToWorld->clickPortal.indexPosition4BackChannel = tutorialPositionIndex;
	reqToWorld->logNpcPortalIndex = 0;
	SERVER.SendChangemapToWorld(player, EzwReqChangeMap::GMCommandSystemonGuide, EventPtr(reqToWorld));
	return true;
}

const Bool GMCommandSystem::onChangePlayerCylinder(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 radius = 0;
	cmd >> radius;

	AttributePlayer&  attrPlayer = player->GetPlayerAttr();

	attrPlayer.targetCylinders.clear();

	TargetCylinder tc;
	tc.radius = radius;
	attrPlayer.targetCylinders.push_back(tc);

	theGameMsg.SendGameDebugMsg(player, L"Cylinder radius %d", radius);

	return true;
}

const Bool GMCommandSystem::onSetMyTeam(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 team = 0;
	cmd >> team;

	player->SetMyTeam(static_cast<TeamType::Enum>(team));
	player->SetAppearanceTeam(static_cast<TeamType::Enum>(team));

	ActionPlayerCognition&	apc = player->GetPlayerCognitionAction();
	ActionSector& sectorAction = player->GetSectorAction();

	ENtfGameExistEntity* existEntityInfo = NEW ENtfGameExistEntity;
	apc.SyncAppearance(existEntityInfo->syncInfo, nullptr);
	sectorAction.NearGridCast(EventPtr(existEntityInfo));

	ENtfMyTeamInfo* ntf = NEW ENtfMyTeamInfo;
	ntf->myTeamInfo.entityId = player->GetId();
	ntf->myTeamInfo.eMyTeam = static_cast<TeamType::Enum>(player->GetMyTeam());

	if (player->GetMyTeam() == TeamType::TEAM_A)
	{
		ntf->myTeamInfo.eOtherTeam = TeamType::TEAM_B;
	}
	else
	{
		ntf->myTeamInfo.eOtherTeam = TeamType::TEAM_A;
	}
	ntf->myTeamInfo.duelId = 0;
	ntf->myTeamInfo.eAppearanceTeam = player->GetMyTeam();

	SERVER.SendToClient(player, EventPtr(ntf));

	theGameMsg.SendGameDebugMsg(player, L"Team %d", team);

	return true;
}
const Bool GMCommandSystem::onBagReset(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	player->GetStorageAction().DevClearBagAndSlot();
	theGameMsg.SendGameDebugMsg(player, L"BagReset\n");
	return true;
}

const Bool GMCommandSystem::onBagItemClr(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	player->GetStorageAction().DevDeleteBagItemAll();
	theGameMsg.SendGameDebugMsg(player, L"Bag item clear\n");
	return true;
}

const Bool GMCommandSystem::onTestAbilityOptionChangeList(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);
	ItemPos slotPos;
	cmd >> slotPos;

	const ItemConstPtr item = player->GetInventoryAction().GetItem( slotPos);
	VALID_RETURN( item, false );

	AbilityTypeSection selectedAbility;

	for (const auto& pack : item->abilityPackMap)
	{
		EAT::Enum eAbilityType = static_cast<EAT::Enum>(pack.first);

		if (pack.second.percent > 0)
		{
			selectedAbility.type = eAbilityType;
			selectedAbility.section = EffectSection::PERCENT;
			break;
		}
		else if (pack.second.point > 0)
		{
			selectedAbility.type = eAbilityType;
			selectedAbility.section = EffectSection::POINT;
			break;
		}
	}

	theGameMsg.SendGameDebugMsg( player, L"%s, 선택된 옵션 [type:%d][section:%d].\n", slotPos.ToString().c_str(), selectedAbility.type, selectedAbility.section );

	EReqItemAbilityOptionChangeList* req = NEW EReqItemAbilityOptionChangeList;
	EventPtr reqPtr(req);
	req->selectedAbilityOption = selectedAbility;
	req->selectedItem = slotPos;
	theZoneHandler.Notify(player, reqPtr);

	return true;
}


const Bool GMCommandSystem::onTestAbilityOptionChangeSelect(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	ItemPos slotPos;
	AbilityOption selectedAbility;
	UInt32 section = 0;
	cmd >> slotPos >> (AbilityType&)selectedAbility.type >> section >> selectedAbility.value;

	selectedAbility.section = static_cast<EffectSection::Enum>(section);

	const ItemConstPtr item = player->GetInventoryAction().GetItem( slotPos );
	VALID_RETURN( item, false );

	theGameMsg.SendGameDebugMsg(player, L"바꿀 옵션 [type:%d][section:%d].\n", selectedAbility.type, selectedAbility.section);

	EReqItemAbilityOptionChangeSelect* req = NEW EReqItemAbilityOptionChangeSelect;
	EventPtr reqPtr(req);
	req->changedAbilityOption = selectedAbility;
	req->selectedItem = slotPos;
	theZoneHandler.Notify(player, reqPtr);

	return true;
}

const Bool GMCommandSystem::onSelfkill(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	player->GetHsm().Tran(EntityState::STATE_PLAYER_DEAD);
	return true;
}

const Bool GMCommandSystem::onPlayerKillAll(EntityPlayer* owner, const CommandString& cmd)
{
	VERIFY_RETURN(owner && owner->IsValid(), false);

	Sector* sector = owner->GetSector();
	VALID_RETURN(sector, false);

	VectorPlayer vecPlayer;
	sector->GetPlayers(vecPlayer);

	for (EntityPlayer* player : vecPlayer)
	{
		VALID_DO(player && player->IsValid(), );

		player->GetHsm().Tran(EntityState::STATE_PLAYER_DEAD);
	}

	return true;
}



const Bool GMCommandSystem::onGotoEnemy(EntityPlayer* owner, const CommandString& cmd)
{
	VERIFY_RETURN(owner && owner->IsValid(), false);

	auto ActionSectorController = owner->GetSectorAction().GetActionSectorController();
	VERIFY_RETURN(ActionSectorController, false);

	PVPChaosCastleProcessor* pvpChaosCastleProcessor = dynamic_cast<PVPChaosCastleProcessor*>(ActionSectorController->GetSectorProcess());
	VERIFY_RETURN(pvpChaosCastleProcessor, false);

	Int32 result = pvpChaosCastleProcessor->DevGotoEnemy(owner);
	switch (result)
	{
	case 0:
		theGameMsg.SendGameDebugMsg(owner, L"move success\n");
		break;
	case 1:
		theGameMsg.SendGameDebugMsg(owner, L"not found enemy\n");
		break;
	case 2:
		theGameMsg.SendGameDebugMsg(owner, L"invalid enemy pos\n");
		break;
	}

	return true;
}

const Bool GMCommandSystem::onFaction(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	ENtfMyTeamInfo* ntf = NEW ENtfMyTeamInfo;
	ntf->myTeamInfo.entityId = player->GetId();
	ntf->myTeamInfo.eMyTeam = player->GetMyTeam();

	if (player->GetMyTeam() == TeamType::TEAM_A)
	{
		ntf->myTeamInfo.eOtherTeam = TeamType::TEAM_B;
	}
	else
	{
		ntf->myTeamInfo.eOtherTeam = TeamType::TEAM_A;
	}
	ntf->myTeamInfo.duelId = 0;
	ntf->myTeamInfo.eAppearanceTeam = player->GetMyTeam();

	SERVER.SendToClient(player, EventPtr(ntf));
	return true;
}

const Bool GMCommandSystem::onShowMessageMap(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Sector* sector = player->GetSector();
	VALID_RETURN(sector, false);

	auto& devConfigInfo = sector->GetDevConfigInfo();
	devConfigInfo.showVolumeMessage = !devConfigInfo.showVolumeMessage;

	if (devConfigInfo.showVolumeMessage == true)
	{
		theGameMsg.SendGameDebugMsg(player, L"Show Messagemap ON");
	}
	else
	{
		theGameMsg.SendGameDebugMsg(player, L"Show Messagemap OFF");
	}

	return true;
}
const Bool GMCommandSystem::onRecovery(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	ReviveParam param(GameReviveType::InPlaceRevive, player);
	theReviveSystem.Revive(param);

	return true;
}
const Bool GMCommandSystem::onRecoverParty(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Sector *sector = player->GetSector();
	VALID_RETURN(sector, false);

	VecPartyMember members;
	player->GetPartyAction().GetMembers(members);
	for (const PartyMember& member : members)
	{
		EntityPlayer* memberPlayer = sector->FindPlayer(member.entityId);
		VALID_DO(memberPlayer && memberPlayer->IsValid() && memberPlayer->IsDead(), continue);

		EReqQuickReviveToPartyMember* req = NEW EReqQuickReviveToPartyMember;
		EventPtr reqPtr(req);
		req->partyMemberEntityId = memberPlayer->GetId();
		theZoneHandler.Notify(memberPlayer, reqPtr);
	}
	return true;
}

const Bool GMCommandSystem::onChangeCharName(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring name;
	cmd >> name;

	Items items;
	ItemFinderByIndex(*player, 200300).Find(PremiumBags, items);
	//player->GetInventoryAction().FindItem(PremiumBags, 200300, &items);

	VALID_RETURN(items.size(), false);

	EReqChangeCharName* req = NEW EReqChangeCharName;
	const ItemConstPtr item = (*items.begin());

	req->targetItem.SetPos(item->GetPos());
	req->targetItem.SetCurStackCount(1);
	req->targetItem.itemId = item->GetItemId();
	req->newName = name;

	player->GetHsm().OnEvent(EventPtr(req));
	return true;
}

const Bool GMCommandSystem::onChangeKnightageName(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring name;
	cmd >> name;

	Items items;
	ItemFinderByIndex(*player, 200301).Find(PremiumBags, items);

	VALID_RETURN(items.size(), false);

	EReqChangeKnightageName* req = NEW EReqChangeKnightageName;

	const ItemConstPtr& item = (*items.begin());

	req->targetItem.SetPos(item->GetPos());
	req->targetItem.SetCurStackCount(1);
	req->targetItem.itemId = item->GetItemId();

	req->newName = name;

	//ItemSlotInfo aInfo;

	player->GetHsm().OnEvent(EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onDungeonDifficultyCtrl(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexPosition indexPosition = 0;
	UInt32 layer = 0;
	UInt32 dungeonDifficultyLevel = DungeonDifficultyLevelType::NONE;
	UInt32 firstDungeonMember = 0;

	cmd >> indexPosition >> layer;

	cmd >> dungeonDifficultyLevel >> firstDungeonMember;
	if (DungeonDifficultyLevelType::MAX <= dungeonDifficultyLevel || DungeonDifficultyLevelType::NONE >= dungeonDifficultyLevel)
		return false;
	if (0 >= firstDungeonMember || Limits::MAX_PARTY_MEMBER < firstDungeonMember)
		return false;


	const PositionElem* foundPositionElem = SCRIPTS.GetPositionElem(indexPosition);
	if (foundPositionElem == NULL)
	{
		theGameMsg.SendGameDebugMsg(player, L"change map fail\n");
		return false;
	}

	const WorldElem* elem = SCRIPTS.GetWorldScript(foundPositionElem->GetDestZoneIndex());
	if (nullptr == elem)
	{
		MU2_WARN_LOG(core::LogCategory::DEFAULT, "invalid indexZone(%d)", foundPositionElem->GetDestZoneIndex());
		return false;
	}

	InstanceDungeonInfo dungeonInfo;
	dungeonInfo.SetDungeonType(elem->ToDungeonType());

	if (elem->IsInstance())
	{
		// 인스턴스 던전
		dungeonInfo.layer = layer;
		dungeonInfo.level = player->GetLevel();
		dungeonInfo.moveUpPositionIndex = indexPosition;
		dungeonInfo.dungeonDifficultyLevel = player->GetAction<ActionSector>()->GetSector()->GetDifficulty();

		if (elem->IsPVEDungeonType())
		{
			dungeonInfo.dungeonDifficultyLevel = static_cast<UInt8>(dungeonDifficultyLevel);
		}
	}

	EzwReqChangeMap* reqToWorld = NEW EzwReqChangeMap;
	reqToWorld->toIndexPositionMovable = indexPosition;
	reqToWorld->toInsInfoList.push_back(dungeonInfo);
	SERVER.SendChangemapToWorld(player, EzwReqChangeMap::onDungeonDifficultyCtrl, EventPtr(reqToWorld));

	return false;
}
const Bool GMCommandSystem::onEnterMythology(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 difficulty = 0;
	cmd >> difficulty;

	ExecutionZoneId execZoneId = player->GetCurrExecId();

	VolumeSpawnScript* volumeScript = GetZoneScript<VolumeSpawnScript>(execZoneId.GetZoneIndex());
	if (nullptr == volumeScript)
	{
		MU2_WARN_LOG(LogCategory::DEBUG_VOLUME, "[E] [ checkDistance ][ ZoneID : %d ]", execZoneId.GetZoneIndex());

		return false;
	}

	auto& elems = volumeScript->GetMap();

	Vector3 ownerPos = player->GetCurrPos();

	for (auto& i : elems)
	{
		VolumeSpawnElem& elem = i.second;
		VALID_DO(elem.volumeType == 2, continue);

		VALID_DO( elem.npcRegen[0].npcIndex, continue );

		Vector3  portalPos;

		portalPos.Set(elem.x, elem.y, elem.z);

		Float diff = Distance(ownerPos, portalPos);
		VALID_DO(diff <= 1000.0f, continue);

#ifdef PORTAL_NEED_QUEST_by_jjangmo_180521
		IndexQuest failquest = 0;
		thePortalSystem.Request( player, elem.npcRegen[0].npcIndex, 0, static_cast<UInt8>( difficulty ), false, failquest );
#else
		thePortalSystem.Request(player, elem.npcRegen[0].npcId, 0, static_cast<UInt8>(difficulty), false, 0, 0);
#endif
		return false;
	}

	return true;
}

const Bool GMCommandSystem::onDailyMission(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	EReqDailyMission* req = NEW EReqDailyMission;
	EventPtr reqPtr(req);
	theZoneHandler.Notify(player, reqPtr);

	return true;
}
const Bool GMCommandSystem::onDailyMissionReward(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 missionIndex = 0;
	UInt32 missionNo = 0;
	cmd >> missionIndex >> missionNo;

	EReqGetDailyMissionReward* req = NEW EReqGetDailyMissionReward;
	EventPtr reqPtr(req);
	req->dailyMissionIndex = missionIndex;
	req->missionNo = static_cast<UInt8>(missionNo);
	theZoneHandler.Notify(player, reqPtr);

	return true;
}

const Bool GMCommandSystem::onResetDailyMission(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	player->GetDailyMissionAction().Dev_ResetDailyMission();

	return true;
}

const Bool GMCommandSystem::onItemExpand(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	ItemPos sorucePos;
	ItemPos targetPos;	
	cmd >> sorucePos >> targetPos;

	ActionPlayerInventory& actInven = player->GetInventoryAction();

	const ItemConstPtr targetItem = actInven.GetItem(targetPos);
	const ItemConstPtr sourceItem = actInven.GetItem(sorucePos);
	VALID_RETURN(targetItem && sourceItem, false);

	EReqItemExpandPeriod* req = NEW EReqItemExpandPeriod;
	req->targetItem = targetItem->GetPos();
	req->sourceItem = sourceItem->GetPos();
	player->GetHsm().OnEvent(EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onItemExpiry(EntityPlayer * owner, const CommandString& cmd)
{
	VERIFY_RETURN(owner && owner->IsValid(), false);

	ItemPos targetPos;
	cmd >> targetPos;

	const ItemConstPtr targetItem = owner->GetInventoryAction().GetItem(targetPos);
	VALID_RETURN(targetItem, false);

	VERIFY_RETURN(owner->GetInventoryAction().TestItemExpiry(targetItem), false);

	theGameMsg.SendGameDebugMsg(owner, L"Item Expiry\n");
	return true;
}

const Bool GMCommandSystem::onItemIdentify(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	ItemPos itemPos;

	cmd >> itemPos;

	const ItemConstPtr targetItem = player->GetInventoryAction().GetItem(itemPos);
	VERIFY_RETURN(targetItem, false);

	EReqItemIdentity* req = NEW EReqItemIdentity;
	req->targetItem = targetItem->GetPos();
	player->GetHsm().OnEvent(EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onAdditionalOption(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	ItemPos itemPos;

	cmd >> itemPos;

	IndexItem itemIndex = 200400;

	const ItemInfoElem* elem = SCRIPTS.GetItemInfoScript(itemIndex);
	VERIFY_RETURN(elem, false);

	Items sourceItems;

	ItemFinderByIndex(*player, itemIndex).Find(elem->GetInvenRange4Pushable(), sourceItems, 1);
	VALID_RETURN(sourceItems.size(), false);

	const ItemConstPtr targetItem = player->GetInventoryAction().GetItem(itemPos);
	VERIFY_RETURN(targetItem, false);

	EReqAdditionalOption* req = NEW EReqAdditionalOption;
	const ItemConstPtr& sourceItem = (*sourceItems.begin());
	req->sourceItem = sourceItem->GetPos();
	req->targetItem = targetItem->GetPos();
	player->GetHsm().OnEvent(EventPtr(req));

	return true;
}

// 큐브 아이템
const Bool GMCommandSystem::onUseCubeItem(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring command;
	ItemPos pos;

	cmd >> command >> pos;

	if (command == L"open")
	{
		const ItemConstPtr item = player->GetInventoryAction().GetItem(pos);
		VERIFY_RETURN(item, false);

		EReqItemCubeOpen *req = NEW EReqItemCubeOpen;
		const ItemConstPtr foundItem = player->GetInventoryAction().GetItem(pos);
		VERIFY_RETURN(foundItem, false);
		player->GetHsm().OnEvent(EventPtr(req));
	}
	else if (command == L"close")
	{
		EReqItemCubeClose *req = NEW EReqItemCubeClose;
		player->GetHsm().OnEvent(EventPtr(req));
	}
	else if (command == L"check")
	{
		const ItemConstPtr item = player->GetInventoryAction().GetItem(pos);
		VERIFY_RETURN(item, false);

		EReqItemCubeCheck* req = NEW EReqItemCubeCheck;
		const ItemConstPtr foundItem = player->GetInventoryAction().GetItem(pos);
		VERIFY_RETURN(foundItem, false);
		req->sourceItem = foundItem->GetSlotInfo();
		req->wishStepWhenOnce = 0;
		player->GetHsm().OnEvent(EventPtr(req));
	}

	return true;
}

// 초월석 관련
const Bool GMCommandSystem::onTranscendStone(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring command;
	std::array< UInt32, 5 > value;

	ItemPos srcPos;
	ItemPos tarPos;

	cmd >> command >> srcPos >> tarPos;

	if (command == L"equip")
	{
		EReqEquipTranscendStone * req = NEW EReqEquipTranscendStone;

		req->sourceItem = srcPos;
		req->targetItem = tarPos;

		player->GetHsm().OnEvent(EventPtr(req));
	}
	else if (command == L"upgrade")
	{
		EReqUpgradeTranscendStone * req = NEW EReqUpgradeTranscendStone;

		req->sourceItem = srcPos;
		req->targetItem = tarPos;

		player->GetHsm().OnEvent(EventPtr(req));
	}
	else if (command == L"create")
	{
		EReqCreateAmplifcationStone * req = NEW EReqCreateAmplifcationStone;

		req->dominionType = static_cast<CreateAmplificationType>(value[0]);
		req->slot = static_cast<Byte>(value[1]);
		req->productionIndex = value[2];

		player->GetHsm().OnEvent(EventPtr(req));
	}
	else if (command == L"Accele")
	{
		EReqAccelerationAmplifcationStone * req = NEW EReqAccelerationAmplifcationStone;

		req->dominionType = static_cast<CreateAmplificationType>(value[0]);
		req->slot = static_cast<Byte>(value[1]);

		player->GetHsm().OnEvent(EventPtr(req));
	}
	else if (command == L"Recive")
	{
		EReqReciveAmplifcationStone * req = NEW EReqReciveAmplifcationStone;

		req->dominionType = static_cast<CreateAmplificationType>(value[0]);
		req->slot = static_cast<Byte>(value[1]);

		player->GetHsm().OnEvent(EventPtr(req));
	}

	return true;
}

const Bool GMCommandSystem::onRemoveTrascendStoneFailCount(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	ItemPos srcPos;
	ItemPos tarPos;

	cmd >> srcPos >> tarPos;

	EReqRemoveTrascendStoneFailCount * req = NEW EReqRemoveTrascendStoneFailCount;

	req->sourceItem = srcPos;
	req->targetItem = tarPos;

	player->GetHsm().OnEvent(EventPtr(req));

	return true;
}


const Bool GMCommandSystem::onRequestAchievementRewardItem(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	player->GetAchievementAction().CheatGetAchievementRewardItem();

	return true;
}

const Bool GMCommandSystem::onExchangeItemShop(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring command;
	std::array< UInt32, 5 > value;
	cmd >> command >> value[0] >> value[1] >> value[2] >> value[3] >> value[4];

	EventPtr reqPtr = nullptr;

	if (command == L"open")
	{
		EReqExchangeItemOpenUI* req = NEW EReqExchangeItemOpenUI;
		reqPtr = EventPtr(req);
	}
	else if (command == L"close")
	{
		EReqExchangeItemCloseUI* req = NEW EReqExchangeItemCloseUI;
		reqPtr = EventPtr(req);
	}
	else if (command == L"register")
	{
		EReqExchangeItemRegister* req = NEW EReqExchangeItemRegister;
		reqPtr = EventPtr(req);
		req->slotInfo.SetPos(ItemPos(value[0], value[1]));
		req->shopType = ItemExchangeShopType::ZEN;
		req->price = value[2];
		req->count = static_cast<Byte>(value[3]);
		req->registerHours = static_cast<Byte>(value[4]);

	}
	else if (command == L"unregister")
	{
		EReqExchangeItemUnregister* req = NEW EReqExchangeItemUnregister;
		reqPtr = EventPtr(req);
		req->itemIds.push_back(value[0]);
	}
	else if (command == L"acquire")
	{
		EReqExchangeItemAcquireSoldMoney* req = NEW EReqExchangeItemAcquireSoldMoney;
		reqPtr = EventPtr(req);
		req->itemIds.push_back(value[0]);
	}
	else if (command == L"buy")
	{
		EReqExchangeItemBuy* req = NEW EReqExchangeItemBuy;
		reqPtr = EventPtr(req);
		req->itemId = value[0];

	}
	else
	{
		return false;
	}

	theZoneHandler.Notify(player, reqPtr);
	return true;
}

const Bool GMCommandSystem::onExchangeItemShopNoDealy(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	player->GetExchangeAction().DevSetNoDelay(true);

	return true;
}

const Bool GMCommandSystem::onExchangeItemShopAvgPrice(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexItem itemIndex;
	cmd >> itemIndex;

	player->GetExchangeAction().GetAvgPriceList(itemIndex);

	return true;
}

const Bool GMCommandSystem::onExchangeItemShopAcquireAtOnce(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	static const Int32 Count = 10;
	ItemId itemId[Count] = { 0, };

	cmd >> itemId[0] >> itemId[1] >> itemId[2] >> itemId[3] >> itemId[4] >> itemId[5] >> itemId[6] >> itemId[7] >> itemId[8] >> itemId[9];

	EReqExchangeShopAcquireAtOnce* req = new EReqExchangeShopAcquireAtOnce;

	for (int i = 0; i < Count; ++i)
	{
		if (itemId[i] == 0)
			break;
		req->itemIds.push_back(itemId[i]);
	}

	theZoneHandler.Notify(player, EventPtr(req));

	return true;
}



const Bool GMCommandSystem::onRequestEmergencyEscape(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Sector* sector = player->GetSector();
	VERIFY_RETURN(sector, false);

	auto indexZone = sector->GetIndexZone();

	auto worldElem = SCRIPTS.GetWorldScript(indexZone);
	VERIFY_RETURN(worldElem, false);

	EReqGameEmergencyEscape* req = NEW EReqGameEmergencyEscape;
	req->escapePositionIndex = worldElem->escapePosition;

	player->OnEvent(EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onSetAutoRevivalInfo(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Int32 recoveryHpRate, recoveryMpRate;
	Tick waitTick;
	cmd >> recoveryHpRate >> recoveryMpRate >> waitTick;

	player->GetReviveAction().SetAutoRevivalInfo(recoveryHpRate, recoveryMpRate, waitTick);

	return true;
}

const Bool GMCommandSystem::onAddIgnoreMpCount(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 count;
	cmd >> count;

	player->GetSkillControlAction().AddIgnoreMpCount(static_cast<Byte>(count),10000);

	return true;
}

const Bool GMCommandSystem::onUseMembershipServiceItem(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	ItemPos itemPos;
	cmd >> itemPos;

	EReqUseMembershipServiceItem* req = NEW EReqUseMembershipServiceItem;
	req->pos = itemPos;
	player->OnEvent(EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onSetMembershipServiceTime(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Int32	remainSec;
	cmd >> remainSec;

	remainSec = std::max<Int32>(remainSec, 5);

	player->GetMembershipAction().SetRemainSecond4Dev(remainSec);

	return true;
}

const Bool GMCommandSystem::onShowMembershipServiceState(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	AttributePlayer* attrPlayer = GetEntityAttribute(player);
	VALID_RETURN(attrPlayer, false);

	std::wstring output;
	DateTime present = DateTime::GetPresentTime();

	if (player->IsPcRoom())
	{
		if (present > attrPlayer->GetMembershipService().pcRoomKeepInfo.lastResetTime)
		{
			theGameMsg.SendGameDebugMsg(player, L"in PC_Room ( %d )\n", attrPlayer->GetMembershipService().pcRoomKeepInfo.keepSeconds / 60);
		}
		else
		{
			theGameMsg.SendGameDebugMsg(player, L"in PC_Room ( wrong time )\n");
		}
	}

	if (present < attrPlayer->GetMembershipService().info.platinumPlusEndTime.ToDateTime())
	{
		attrPlayer->GetMembershipService().info.platinumPlusEndTime.ToDateTime().ToDateString(L"%y-%m-%d %H:%M:%S", output);
	}
	else
	{
		output = L"not used";
	}
	theGameMsg.SendGameDebugMsg(player, L"PlatinumPlus : %s.\n", output.c_str());

	if (present < attrPlayer->GetMembershipService().info.platinumEndTime.ToDateTime())
	{
		attrPlayer->GetMembershipService().info.platinumEndTime.ToDateTime().ToDateString(L"%y-%m-%d %H:%M:%S", output);
	}
	else
	{
		output = L"not used";
	}
	theGameMsg.SendGameDebugMsg(player, L"Platinum : %s.\n", output.c_str());
	theGameMsg.SendGameDebugMsg(player, L"Platinum Level : %d.\n", attrPlayer->GetMembershipService().info.level);
	theGameMsg.SendGameDebugMsg(player, L"Platinum Exp : %d (%d).\n", attrPlayer->GetMembershipService().info.exp, attrPlayer->GetMembershipService().info.remainSecond);

	return true;
	}

const Bool GMCommandSystem::onRequestMembershipReward(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt16	benefitType;
	cmd >> benefitType;

	EMembershipBenefitType eBenefitType = EMembershipBenefitType::eNone;

	switch (benefitType)
	{
	case 0:
		eBenefitType = EMembershipBenefitType::eDaily_Connect_Reward;
		break;

	case 1:
		eBenefitType = EMembershipBenefitType::eDaily_Time_Reward;
		break;

	default:
		theGameMsg.SendGameDebugMsg(player, L"invalid - benefit type.!\n");
		theGameMsg.SendGameDebugMsg(player, L"< benefit:0> : eDaily_Connect_Reward\n");
		theGameMsg.SendGameDebugMsg(player, L"< benefit:1> : eDaily_Time_Reward\n");
		return false;
	}

	EReqGameMembershipDailyReward* req = NEW EReqGameMembershipDailyReward;
	req->eBenefitType = eBenefitType;
	player->OnEvent(EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onInsEventItems(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Int32 loopCount, itemType, itemCount;
	cmd >> loopCount >> itemType >> itemCount;

	loopCount = std::max<Int32>(1, loopCount);
	itemType = (itemType == 0) ? 60000 : itemType;
	itemCount = std::max<Int32>(1, itemCount);

	auto elem = SCRIPTS.GetItemInfoScript(itemType);
	if (elem == nullptr)
	{
		theGameMsg.SendGameDebugMsg(player, L"invalid item type : %d.\n", itemType);
		return false;
	}

	for (auto i = 0; i < loopCount; ++i)
	{
		EReqDbInsertEventItem* dbReq = NEW EReqDbInsertEventItem;
		dbReq->charId = player->GetCharId();
		dbReq->subject = L"GM test subject";
		dbReq->title = L"GM test title";
		dbReq->keepDays = rand() % 5 > 0 ? 30 : 0;
		dbReq->itemType = itemType;
		dbReq->itemCount = static_cast<Int16>(itemCount);
		SERVER.SendToDb(player, EventPtr(dbReq));
	}

	return true;
}

const Bool GMCommandSystem::onReqEventItemPage(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	EReqEventItemPage* req = NEW EReqEventItemPage;
	cmd >> req->rowPerPage;
	cmd >> req->pageNumber;
	player->OnEvent(EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onReqEventItemPickUp(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Int32 count;
	cmd >> count;

	std::vector<IndexItem> indexes;

	player->GetEventInventoryAction().DevQueryIndexes(indexes, count);

	EReqEventItemPickUp* req = NEW EReqEventItemPickUp;
	req->eventItemIndexes = indexes;
	player->OnEvent(EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onShowEventItem(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	// action을 만들어서 해당 액션에서 출력
	player->GetEventInventoryAction().DevShowItems();

	return true;
}

const Bool GMCommandSystem::onBlockItem(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	ItemPos itemPos;
	UInt32 onOff = 0;
	cmd >> itemPos >> onOff;

	const ItemPtr item = player->GetInventoryAction().GetItem(itemPos);
	VALID_RETURN(item, false);

	ItemData data;
	item->FillUpItemData(data);

	if (data.block == (onOff != 0))
		return false;

	data.block = false;
	item->SetItemData(data);

	ItemBaseTransaction *trans = NEW ItemBaseTransaction(__FUNCTION__, __LINE__, player, LogCode::E_UPDATE_GM_COMMAND);
	TransactionPtr transPtr(trans);
	{
		ItemBag bag(*player);
		data.block = (onOff != 0);

		VERIFY_RETURN(bag.Update(item, data), false);
		VERIFY_RETURN(bag.Write(transPtr), false);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), false);
	}

	return true;
}


const Bool GMCommandSystem::onMissionMapEntrance(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexZone indexZone;
	ExecKey execKey;

	cmd >> indexZone >> execKey;

	ExecId execId(execKey, indexZone);

	// 섹터에서 뽑기
	// joinining신청 


	Sector* pSector = player->GetSector();
	VERIFY_RETURN(pSector, false);

	auto pvpScript = SCRIPTS.GetScript<PVPMissionMapScript>();
	VERIFY_RETURN(pvpScript, false);
	auto gameRule = pvpScript->Get<MMRuleDefault >(execId.GetZoneIndex());
	VALID_RETURN(gameRule, false);

	std::array< UInt32, TeamType::TEAM_MAX > teamMemberCount;
	teamMemberCount.fill(0);

	UInt16 reaminedMember = gameRule->maxMember * 2;

	auto globalRule = pvpScript->GetGlobalRule();
	VERIFY_RETURN(globalRule, false);

	EzwNtfPVPMissionmapEntranceByCheat * req = NEW EzwNtfPVPMissionmapEntranceByCheat;
	EventPtr evetReq(req);
	{
		req->targetExecutionZoneId = execId;
		req->dungeonType = gameRule->dungeonType;
	}

	auto matchUser = [&](Entity4Zone* entity)
	{
		VERIFY_RETURN(entity && entity->IsValid() && entity->IsPlayer(), );
		EntityPlayer* found = static_cast<EntityPlayer*>(entity);

		if (reaminedMember == 0)
		{
			return;
		}

		TeamType::Enum matchTeam = static_cast<TeamType::Enum>((reaminedMember % 2) + 1);
		if (teamMemberCount[matchTeam] < gameRule->maxMember)
		{
			++teamMemberCount[matchTeam];
		}
		else
		{
			matchTeam = matchTeam == TeamType::TEAM_A ? TeamType::TEAM_B : TeamType::TEAM_A;
			++teamMemberCount[matchTeam];
		}

		--reaminedMember;

		EzwNtfPVPMissionmapEntranceByCheat::PlayerInfo playerInfo;
		playerInfo.charId = found->GetCharId();
		playerInfo.worldSessionKey = found->GetWorldSessionKey();
		playerInfo.eMatchTeamType = matchTeam;
		playerInfo.startingPositionIndex = gameRule->teamPositions[matchTeam].startPoisition;
		req->players.emplace_back(std::move(playerInfo));
	};

	// 신청한 사람 먼저 넣고
	//matchUser( player );
	// 매칭
	pSector->ForEachByCond(EntityTypes::PLAYER_ZONE, [&](const auto& value) {matchUser(value.second); });

	SERVER.SendToOnlyWorldServer(evetReq);
	//theWorldRunner.GetHandler<PlayerHandler>()->Notify( EventPtr( req ) );

	return true;
}

const Bool GMCommandSystem::onWeaponFusion(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	ItemPos startSlotPos;
	UInt32 materialCount = 0, wiseCount = 0, keepEnchant = 0;

	cmd >> startSlotPos >> materialCount >> wiseCount >> keepEnchant;

	EReqItemWeaponFusion *req = NEW EReqItemWeaponFusion;
	EventPtr reqPtr(req);

	for (eSlotType index = 0; index < materialCount; ++index)
	{
		ItemPos pos(startSlotPos.GetInven(), startSlotPos.GetSlot() + index);

		const ItemConstPtr item = player->GetInventoryAction().GetItem(pos);
		VALID_RETURN(item, false);
		VALID_RETURN(item->GetOptionElem(), false);
		VALID_RETURN(item->GetFusionGroup() != ItemData::FusionGroup::NONE, false);

		req->materiallist.push_back(item->GetPos());
	}
	req->wiseStoneCount = static_cast<UInt16>(wiseCount);
	req->keepEnchant = keepEnchant != 0;

	player->OnEvent(reqPtr);

	return true;
}

const Bool GMCommandSystem::onApplyRaid(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	RaidId raidId = 0;
	cmd >> raidId;

	EReqRaidApplyRaid* req = NEW EReqRaidApplyRaid;
	EventPtr reqPtr(req);
	req->raidId = raidId;
	theZoneHandler.Notify(player, reqPtr);

	return true;
}


// 팔로우 테스트
const Bool GMCommandSystem::onKnightageFollower(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	KnightageId knightageId = 0;
	UInt32 type;
	cmd >> type >> knightageId;

	if (type == 0)
	{
		//UnFollowing
		KnightageFollowerTrascation *trans = NEW KnightageFollowerTrascation(__FUNCTION__, __LINE__, player, LogCode::E_KNIGHTAGE_FOLLOWER, player->GetFollowerId(), KnightageFollowerTrascation::UNFOLLOWING);
		TransactionPtr transPtr(trans);
		{
			VERIFY_RETURN(TransactionSystem::Register(transPtr), false);
		}
	}
	else
	{
		//Following
		KnightageFollowerTrascation *trans = NEW KnightageFollowerTrascation(__FUNCTION__, __LINE__, player, LogCode::E_KNIGHTAGE_FOLLOWER, knightageId, KnightageFollowerTrascation::FOLLOWING);
		TransactionPtr transPtr(trans);
		{
			VERIFY_RETURN(TransactionSystem::Register(transPtr), false);
		}
	}

	return true;
}

// 일일상점 초기화
const Bool GMCommandSystem::onDailyShopReset(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	player->GetInventoryAction().ResetDailyShopInfo();

	return true;
}

const Bool GMCommandSystem::onAmplicationComplete(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	player->GetInventoryAction().AmplicationStoneAllClear();
	return true;
}

// 도미니언 영지 진입
const Bool GMCommandSystem::onJoinDominionWisdom(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexDominion dominionId = 0;
	Int32 type;
	cmd >> type >> dominionId;

	if (type == 0)
	{
		EReqJoinKnightageDominion *req = NEW EReqJoinKnightageDominion;

		req->dominionIndex = dominionId;
		player->OnEvent(EventPtr(req));
	}
	else
	{
		SystemMessage msg(L"sys", L"Msg_Dominion_End_Tournament");

		//auto veiwPlayer = player->GetView<ViewPlayer>();

		SendSystemMsg(player, msg);

		EzwNtfExitDominionWisdom *res = NEW EzwNtfExitDominionWisdom;
		res->exitPlayers.push_back(player->GetCharId());
		SERVER.SendToOnlyWorldServer(EventPtr(res));
	}
	return true;
}

// 현재 섹터에 기사단 영지값부여
const Bool GMCommandSystem::onSectorDominion(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	KnightageId knightageId = 0;
	cmd >> knightageId;

	Sector* sector = player->GetSector();
	VALID_RETURN(sector, false);

	sector->SetDominionKnightageId(knightageId);

	theGameMsg.SendGameDebugMsg(player, L"현재 채널이 영지값이 [%d]로 세팅되었습니다.\n", knightageId);

	return true;
}


// 기부 테스트
const Bool GMCommandSystem::onKnightageGiveTrophy(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	EReqKnightageGiveTrophy *req = NEW EReqKnightageGiveTrophy;
	player->OnEvent(EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onChangeAllowedMoveDistance(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Float allowedMoveDist = 0.f;
	cmd >> allowedMoveDist;

	player->GetPlayerAction().SetCheatAllowedMoveDistance(allowedMoveDist);

	return true;
}

// 3:3투기장 테스트 
const Bool GMCommandSystem::on33pvpGiveUp(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	// 임시용 
	ActionPlayerMissionMapTier* actionTier = GetEntityAction(player);
	EzcNtfCharacterTierInfo* ntf_Tier = NEW EzcNtfCharacterTierInfo;
	actionTier->FillTierInfo(ntf_Tier->vecTierInfo);
	SERVER.SendToClient(player, EventPtr(ntf_Tier));

	EReqColosseum33PVPGiveUp* req = NEW EReqColosseum33PVPGiveUp;
	EventPtr reqPtr(req);
	theZoneHandler.Notify(player, reqPtr);

	theGameMsg.SendGameDebugMsg(player, L"GiveUp");
	SendSystemMsg(player, SystemMessage(L"sys", L"sys_giveup"));
	return true;
}

const Bool GMCommandSystem::on33pvpMSG(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 type = 0;
	cmd >> type;

	EReqColosseum33PVPDevMSG* req = NEW EReqColosseum33PVPDevMSG;
	EventPtr reqPtr(req);
	req->type = type;
	theZoneHandler.Notify(player, reqPtr);

	theGameMsg.SendGameDebugMsg(player, L"on33pvpMSG\n");
	SendSystemMsg(player, SystemMessage(L"sys", L"sys_on33pvpMSG"));
	return true;
}
const Bool GMCommandSystem::onwinpoint(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Int32 team = 0;
	Int32 point = 0;
	cmd >> team >> point;

	EReqColosseum33PVPDevMSG* req = NEW EReqColosseum33PVPDevMSG;
	EventPtr reqPtr(req);
	req->type = 100;
	req->value1 = team;
	req->value2 = point;
	theZoneHandler.Notify(player, reqPtr);

	return true;
}

const Bool GMCommandSystem::onsetwinpoint(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Int32 point = 0;
	cmd >> point;

	ActionPlayerMissionMapTier* actionTier = GetEntityAction(player);
	VERIFY_RETURN(actionTier, false);

	actionTier->DevSetPoint(TierType::COLOSSEUM33_PVP, point);

	EzcNtfCharacterTierInfo* ntf_Tier = NEW EzcNtfCharacterTierInfo;
	actionTier->FillTierInfo(ntf_Tier->vecTierInfo);
	SERVER.SendToClient(player, EventPtr(ntf_Tier));


	return true;
}

const Bool GMCommandSystem::onsurrender(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	EReqColosseum33PVPDevMSG* req = NEW EReqColosseum33PVPDevMSG;
	EventPtr reqPtr(req);
	req->type = 200;
	theZoneHandler.Notify(player, reqPtr);

	return true;
}

const Bool GMCommandSystem::onwincount(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Int32 winCount = 0;
	Int32 loseCount = 0;
	cmd >> winCount >> loseCount;

	ActionPlayerMissionMapTier* actionTier = GetEntityAction(player);
	VERIFY_RETURN(actionTier, false);

	for (Int32 count = 0; count < winCount; ++count)
	{
		actionTier->IncreaseWinPoint(TierType::COLOSSEUM33_PVP);
	}

	for (Int32 count = 0; count < loseCount; ++count)
	{
		actionTier->DecreaseLosePoint(TierType::COLOSSEUM33_PVP);
	}

	return true;
}

const Bool GMCommandSystem::onCreateSocket(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	ItemPos itemPos;

	cmd >> itemPos;

	EReqItemSocketCreate *req = NEW EReqItemSocketCreate;
	req->item.SetPos(itemPos);
	player->GetHsm().OnEvent(EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onEnchantItem(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	ItemPos itemPos;

	cmd >> itemPos;

	EReqItemEnchant *req = NEW EReqItemEnchant;
	req->item.SetPos(itemPos);
	player->GetHsm().OnEvent(EventPtr(req));

	return true;
}
const Bool GMCommandSystem::onBlackMarketList(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Sector* sector = player->GetSector();
	VERIFY_RETURN(sector, false);

	EntityNpc* npc = sector->FindNpcByCond([&](const SectorEntityMap::value_type& v) -> bool
	{
		ActionNpcBlackMarket* actionNpcBlackMarket = GetEntityAction(v.second);
		VALID_RETURN(actionNpcBlackMarket, false);
		VALID_RETURN(actionNpcBlackMarket->IsBlackMarketNpc(), false);

		return true;
	}
	);

	VALID_RETURN(npc, false);

	ActionNpcBlackMarket* actionNpcBlackMarket = GetEntityAction(npc);
	VALID_RETURN(actionNpcBlackMarket, false);

	actionNpcBlackMarket->SendBlackMarketItemList(player);

	actionNpcBlackMarket->DevBlackMarketItemListMsg(player);
	theGameMsg.SendGameDebugMsg(player, L"succeed\n");
	return true;
}
const Bool GMCommandSystem::onBlackMarketBuy(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	ItemBuyNode node;
	cmd >> node.itemIndex >> node.stack;

	Sector* sector = player->GetSector();
	VERIFY_RETURN(sector, false);

	EntityNpc* npc = sector->FindNpcByCond([&](const SectorEntityMap::value_type& v) -> bool
	{
		ActionNpcBlackMarket* actionNpcBlackMarket = GetEntityAction(v.second);
		VALID_RETURN(actionNpcBlackMarket, false);
		VALID_RETURN(actionNpcBlackMarket->IsBlackMarketNpc(), false);
		return true;
	}
	);

	VALID_RETURN(npc, false);

	EReqBuy* req = NEW EReqBuy;
	EventPtr reqPtr(req);
	{
		req->npcId = npc->GetId();		///< NpcId로 Type을 찾아 Sell.xml에서 물건을 찾는다
		req->items.emplace_back(node);
		theZoneHandler.Notify(player, reqPtr);
	}

	theGameMsg.SendGameDebugMsg(player, L"succeed\n");
	return true;
}
const Bool GMCommandSystem::onBlackMarketExtract(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Sector* sector = player->GetSector();
	VERIFY_RETURN(sector, false);

	EntityNpc* npc = sector->FindNpcByCond([&](const SectorEntityMap::value_type& v) -> bool
	{
		ActionNpcBlackMarket* actionNpcBlackMarket = GetEntityAction(v.second);
		VALID_RETURN(actionNpcBlackMarket, false);
		VALID_RETURN(actionNpcBlackMarket->IsBlackMarketNpc(), false);

		return true;
	}
	);

	VALID_RETURN(npc, false);

	ActionNpcBlackMarket* actionNpcBlackMarket = GetEntityAction(npc);
	actionNpcBlackMarket->SetupBlackMarketItems();
	theGameMsg.SendGameDebugMsg(player, L"succeed\n");
	return true;
}
const Bool GMCommandSystem::onBlackMarketBuyReset(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	ActionPlayerBlackMarket* actionPlayerBlackMarket = GetEntityAction(player);
	VALID_RETURN(actionPlayerBlackMarket, false);

	actionPlayerBlackMarket->DevResetBlackMarketDateAndItem();

	theGameMsg.SendGameDebugMsg(player, L"succeed\n");
	return true;
}


const Bool GMCommandSystem::onautohack(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	ActionPlayerAutoHackChecker* checker = GetEntityAction(player);
	VERIFY_RETURN(checker, false);

	checker->Dev_Hack();
	return true;
}

const Bool GMCommandSystem::onautohackchange(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	ActionPlayerAutoHackChecker* checker = GetEntityAction(player);
	VERIFY_RETURN(checker, false);

	EReqChangeAutoHackQuestion* req = NEW EReqChangeAutoHackQuestion;
	theZoneHandler.Notify(player, EventPtr(req));
	return true;
}

const Bool GMCommandSystem::onAnswer(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Int32 asnwer;
	cmd >> asnwer;

	EReqAutoHackQuestionAnswer* req = NEW EReqAutoHackQuestionAnswer;
	EventPtr reqPtr(req);
	req->answerNo = asnwer;
	theZoneHandler.Notify(player, reqPtr);

	return true;

}

const Bool GMCommandSystem::onautoview(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Int32 asnwer;
	cmd >> asnwer;

	EReqAutoHackQuestionAnswer* req = NEW EReqAutoHackQuestionAnswer;
	req->answerNo = asnwer;

	player->GetAutoHackCheckerAction().DevMSG_ViewEnterCount();

	return true;

}


const Bool GMCommandSystem::onSeasonReqTest(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt8 commandType = 0;
	UInt32 value = 0;

	cmd >> commandType;
	cmd >> value;

	EventPtr reqPtr;
	enum CommandType
	{
		ReqSeasonInfo = 0,
		ReqSeasonMissionInfo,
		ReqSeasonMissionDetailInfo,
#ifdef SEASON_RENEWAL_by_jjangmo_180710
#else
		ReqSeasonTierInfo,
#endif
		ReqSeasonRewardInfo,
	};
	switch (commandType)
	{
	case ReqSeasonInfo:
		reqPtr = NEW EReqSeasonInfo;
		break;
	case ReqSeasonMissionInfo:
		reqPtr = NEW EReqSeasonMissionInfo;
		break;
	case ReqSeasonMissionDetailInfo:
		reqPtr = NEW EReqSeasonMissionDetailInfo;
		break;
#ifdef SEASON_RENEWAL_by_jjangmo_180710
#else
	case ReqSeasonTierInfo:
		reqPtr = NEW EReqSeasonTierInfo;
		break;
#endif
	case ReqSeasonRewardInfo:
		reqPtr = NEW EReqSeasonRewardInfo;
		break;

	default:
		return true;
	}
	VALID_RETURN(reqPtr.RawPtr(), false);
	theZoneHandler.Notify(player, reqPtr);

	return true;
}

const Bool GMCommandSystem::onSeasonOnEvent(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	wstring strConditionGroup;
	Int32 conditionGroup;

	UInt8 maxCount = 0;
	std::vector<UInt32> values;
	cmd >> strConditionGroup >> maxCount;
	for (Int32 count = 3; count < cmd.Size(); ++count)
	{
		UInt32 value = 0;
		cmd >> value;
		values.push_back(value);
	}

	if (!ScriptEnum::FindToken(L"SeasonConditionGroup", strConditionGroup, conditionGroup))
		return false;

	ActionPlayerSeason& actionSeason = player->GetSeasonAction();
	for (UInt8 count = 0; count < maxCount; ++count)
	{
		SeasonMissionEvent* event = SeasonMissionEvent::MakeSeasonMissionEvent(static_cast<SeasonMissionConditionElem::ConditionGroup>(conditionGroup), values);
		if (nullptr != event)
			actionSeason.Event(event);
	}
	return true;
}

const Bool GMCommandSystem::onSeasonMissionClear(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	SeasonMissionIndex index = 0;
	cmd >> index;

	if (0 < index)
		player->GetSeasonAction().MissionClearForDevTest(index);

	return true;
}

const Bool GMCommandSystem::onSeasonMissionEventAllClear(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

#ifdef SEASON_RENEWAL_by_jjangmo_180710
	UInt8 progress = 0;
	cmd >> progress;

	player->GetSeasonAction().ClearAllMissionEventForDevTest(progress);
#else
	player->GetSeasonAction().ClearAllMissionEventForDevTest();
#endif
	return true;
}

const Bool GMCommandSystem::onGameEvent(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring msg;

	UInt32 eventIndex = 0;
	UInt32 Index = 0;
	UInt32 stmp = 0;

	cmd >> msg;
	cmd >> eventIndex;
	cmd >> Index;
	cmd >> stmp;

	EventPtr eventPtr;

	if (msg == L"getexchange")
	{
		if (eventIndex != 3)
		{
			eventPtr = NEW EReqGetExchangeEventInfo;
			auto myEvent = static_cast<EReqGetExchangeEventInfo*>(eventPtr.RawPtr());
			myEvent->eventIndex = eventIndex;
		}
		else
		{
			eventPtr = NEW EReqGetExchangePuzzleEventInfo;
			auto myEvent = static_cast<EReqGetExchangePuzzleEventInfo*>(eventPtr.RawPtr());
			myEvent->eventIndex = eventIndex;
		}
	}
	else if (msg == L"getstamp")
	{
		eventPtr = NEW EReqGetStampEventInfo;
		auto myEvent = static_cast<EReqGetStampEventInfo*>(eventPtr.RawPtr());

		myEvent->eventIndex = eventIndex;
	}
	else if (msg == L"exchange")
	{
		eventPtr = NEW EReqExchangeEventItem;
		auto myEvent = static_cast<EReqExchangeEventItem*>(eventPtr.RawPtr());

		myEvent->eventIndex = eventIndex;
		myEvent->exchangeEventIndex = Index;
		myEvent->count = 1;
	}
	else if (msg == L"stamp")
	{
		if (eventIndex != 3)
		{
			eventPtr = NEW EReqStampPickUpItem;

			auto myEvent = static_cast<EReqStampPickUpItem*>(eventPtr.RawPtr());

			myEvent->eventIndex = eventIndex;
			myEvent->stampEventIndex = Index;
			myEvent->stampStep = stmp;
		}
		else
		{
			eventPtr = NEW EReqStampPickUpItemByPuzzleEvent;

			auto myEvent = static_cast<EReqStampPickUpItemByPuzzleEvent*>(eventPtr.RawPtr());
			myEvent->eventIndex = eventIndex;
			myEvent->stampNo = Index;
		}
	}
	else if (msg == L"clear")
	{
		ActionPlayerEvent *actEvent = GetEntityAction(player);
		if (actEvent == nullptr)
			return false;

		actEvent->ClearEventAll();
	}
	else if (msg == L"reset")
	{
		eventPtr = NEW ENtfDbResetEvent;
		auto myEvent = static_cast<ENtfDbResetEvent*>(eventPtr.RawPtr());
		AttributePlayer* attrplayer = GetEntityAttribute(player);
		myEvent->charId = attrplayer->charId;
		myEvent->eventId = eventIndex;

		SERVER.SendToDb(player, eventPtr);
		return true;
	}
	else if (msg == L"background")
	{
		eventPtr = NEW EReqResetPuzzle;
		auto myEvent = static_cast<EReqResetPuzzle*>(eventPtr.RawPtr());
		myEvent->eventIndex = eventIndex; // 요청한 이벤트 인덱스
		myEvent->backgroundId = Index; // 랜덤 퍼즐배경 
	}

	else if (msg == L"jigsaw")
	{
		eventPtr = NEW EReqJigsawPuzzleEvent;
		auto myEvent = static_cast<EReqJigsawPuzzleEvent*>(eventPtr.RawPtr());
		myEvent->eventIndex = eventIndex; // 요청한 이벤트 인덱스
		myEvent->puzzleSlot = static_cast<Byte>(Index); // 랜덤 퍼즐배경 
	}


	VALID_RETURN(eventPtr.RawPtr(), false);
	theZoneHandler.Notify(player, eventPtr);

	return true;
}

const Bool GMCommandSystem::onNetherWorldStageStateChange(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 state = 0; // 1 : 클리어 2 : 실패
	cmd >> state;

	auto sector = player->GetSector();
	VALID_RETURN(sector, false);

	if (sector->GetId().GetZoneIndex() == 9700 || sector->GetId().GetZoneIndex() == 9701 || sector->GetId().GetZoneIndex() == 9702)
	{
		sector->GetSectorController().GetControllerAction().OnEvent(DungeonEventType::CHEAT, player, player, state);
		return true;
	}

	return true;
}

const Bool GMCommandSystem::onEnchantTicket(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	eSlotType ticketSlot, targetSlot, protectSlot;
	UInt8 isProtect = 0;
	cmd >> ticketSlot >> targetSlot >> isProtect >> protectSlot;

	EReqUseEnchantTicket* req = NEW EReqUseEnchantTicket;
	EventPtr reqPtr(req);
	req->enchantTicket = ItemPos(InvenType::PREMIUM_BAG_START, ticketSlot);
	req->target = ItemPos(InvenType::BAG_1ST, targetSlot);

#ifdef __ItemEnchant_Renew_Jason_180508
#else
	if (isProtect)
	{
		req->protectTicket = ItemPos(InvenType::PLATINUM_BAG_START, protectSlot);
	}
#endif

	theZoneHandler.Notify(player, reqPtr);

	return true;
}

const Bool GMCommandSystem::onReturnUserReward(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	EReqReturnUserReward* req = NEW EReqReturnUserReward;
	EventPtr reqPtr(req);
	theZoneHandler.Notify(player, reqPtr);

	return true;
}

const Bool GMCommandSystem::onAchievementGradeToZero(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	player->GetAchievementAction().CheatGradeZero();

	return true;
}

const Bool GMCommandSystem::onRemoveDivison(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	ItemDivisionType::Enum division = ItemDivisionType::NONE;
	Int32 count = 0;

	cmd >> (Byte&)division >> count;

	ErrorItem::Error eError = player->GetInventoryAction().RemoveByDivision(InvenBags, division, count);
	VERIFY_RETURN(eError == ErrorItem::SUCCESS, MustErrorCheck);

	return true;
}


const Bool GMCommandSystem::onPrivateStorageTabUnlock(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	EReqPrivateStorageTabUnlock *req = NEW EReqPrivateStorageTabUnlock;
	EventPtr reqPtr(req);

	InvenStoragePrivate* next = InvenStoragePrivate::GetNextOpenablePrivateStorage(player);
	VALID_RETURN(next, true);

	req->targetInvenType = next->GetInven();

	theZoneHandler.Notify(player, reqPtr);

	return true;
}

const Bool GMCommandSystem::onPrivateStorageClearItemAll(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	if (true != InvenStoragePrivate::TestClearItemPrivateStorageAll(player))
	{
		return false;
	}

	theGameMsg.SendGameDebugMsg(player, L"PrivateStorage Clear Item All\n");
	return true;
}

const Bool GMCommandSystem::onPrivateStorageTabUnlockAll(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	if (true != InvenStoragePrivate::TestUnlockPrivateStorageTabAll(player))
	{
		return false;
	}

	theGameMsg.SendGameDebugMsg(player, L"PrivateStorage Unlock All\n");
	return true;
}

const Bool GMCommandSystem::onPrivateStorageTabReset(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	if (true != InvenStoragePrivate::TestResetPrivateStorageTab(player))
	{
		return false;
	}

	theGameMsg.SendGameDebugMsg(player, L"PrivateStorage Reset\n");
	return true;
}


const Bool GMCommandSystem::onPSROpen(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Index32 eventIndex = 0;
	cmd >> eventIndex;

	EReqPSROpen	* req = NEW EReqPSROpen;
	EventPtr reqPtr(req);

	req->eventIndex = eventIndex;

	theZoneHandler.Notify(player, reqPtr);

	return true;
}

const Bool GMCommandSystem::onPSREvent(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Index32 eventIndex = 0;
	UInt32 eventType = 0;
	UInt8 value = 0;

	cmd >> eventIndex >> eventType >> value;

	switch (eventType)
	{
	case PSREventType::DEV_ADD_POINT:
	{
		auto actionEvent = player->GetAction<ActionPlayerEvent>();
		VERIFY_RETURN(actionEvent, false);
		actionEvent->DevPSREvent(eventIndex, PSREventType::DEV_ADD_POINT, value);
	}
	break;
	case PSREventType::DEV_REQ_CUR_INFO:
	{
		EReqPSRCurInfo * req = NEW EReqPSRCurInfo;
		EventPtr reqPtr(req);

		req->eventIndex = eventIndex;

		theZoneHandler.Notify(player, reqPtr);
	}
	break;
	default:
	{
		EReqPSREvent * req = NEW EReqPSREvent;
		EventPtr reqPtr(req);

		req->eventIndex = eventIndex;
		req->eventType = static_cast<PSREventType::Enum>(eventType);
		req->psrType = static_cast<PSRType::Enum>(value);

		theZoneHandler.Notify(player, reqPtr);
	}
	break;
}


	return true;
}
const Bool GMCommandSystem::onCharacterSlotExtend(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Items items;
	ItemFinderByIndex(*player, 200302).Find(PremiumBags, items);
	VALID_RETURN(items.size(), false);

	EReqCharacterSlotExtend* req = NEW EReqCharacterSlotExtend;
	const ItemConstPtr& item = *(items.begin());
	req->sourceItem = item->GetPos();

	theZoneHandler.Notify(player, EventPtr(req));
	return true;
}


const Bool GMCommandSystem::onUAccEvent(EntityPlayer* player, const CommandString& cmd)
{
	Index32 eventIndex = 0;
	Int32 opt = 0;
	Int32 value = 0;

	cmd >> opt >> eventIndex >> value;

	auto actEvent = player->GetAction<ActionPlayerEvent>();

	switch (opt)
	{
	case 1:			// Set Count
		actEvent->SetBossKillCount(eventIndex, value);
		break;

	case 2:			// Reset Stamp Recv Info.
		actEvent->ResetBossKillStamp(eventIndex);
		break;
	}

	return true;
}

const Bool GMCommandSystem::onTalisman(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	ErrorTalisman::Error eError = player->GetTalismanAction().OnGmCommand(cmd);
	VERIFY_RETURN(eError == ErrorTalisman::SUCCESS, false);

	return true;
}

const Bool GMCommandSystem::onAdvanture(EntityPlayer* player, const CommandString& cmd)
{
	// "[reset, set, complete]");
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring command;
	// std::array< UInt32, 3 > value;
	cmd >> command;

	if (L"reset" == command)
	{

	}
	else if (L"set" == command)
	{
		UInt32 reportIndex, pageIndex;
		cmd >> reportIndex >> pageIndex;
		if (reportIndex == 0 || pageIndex == 0)
			return false;

		ActionPlayerQuest * actQuest = GetEntityAction(player);
		if (actQuest)
		{
			//!
			actQuest->GMSetAdvantureState(static_cast<UInt16>(reportIndex), static_cast<UInt16>(pageIndex));
		}

	}
	else if (L"complete" == command)
	{
		ActionPlayerQuest * actQuest = GetEntityAction(player);
		if (actQuest)
		{
			//!
			actQuest->GMFinishAdvantureCurPage();
		}
		else
			return false;
	}
	else if (L"reqinfo")
	{
		EReqAdvantureEventInfo * info = NEW EReqAdvantureEventInfo;

		theZoneHandler.Notify(player, EventPtr(info));
	}
	else
	{
		return false;
	}

	return true;
}

const Bool GMCommandSystem::onEmotion(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring command;
	cmd >> command;

	if (L"resetcount" == command)
	{
		player->GetInventoryAction().GMEmotionboxResetCount();
	}
	else if (L"open" == command)
	{

		UInt32 openIndex;
		cmd >> openIndex;
		VERIFY_RETURN(openIndex <= EmotionBoxDivisionEnum::CenterBox, false);

		EReqEmotionBoxOpen * req = NEW EReqEmotionBoxOpen;
		req->pos = static_cast<UInt8>(openIndex);

		theZoneHandler.Notify(player, EventPtr(req));

	}
	else if (L"info" == command)
	{
		player->GetInventoryAction().ShowEmotionboxInfo();
	}
	else if (L"infopk" == command)
	{
		EReqEmotionBoxInfo * req = NEW EReqEmotionBoxInfo;

		theZoneHandler.Notify(player, EventPtr(req));
	}
	else if (L"backdate" == command)
	{
		player->GetInventoryAction().BackEmotionboxDate();
	}
	else if (L"point" == command)
	{
		UInt64 ePoint;
		cmd >> ePoint;

		player->SetEmotionPointByGmCommand(ePoint);
	}
	else if (L"reset" == command)
	{
		std::wstring with;
		cmd >> with;

		if (L"with" == with)
		{
			EReqEmotionBoxRearrange * req = NEW EReqEmotionBoxRearrange;

			theZoneHandler.Notify(player, EventPtr(req));
		}
		else if (L"without" == with)
		{
			player->GetInventoryAction().CompleteEmotionBoxReset(0, 0);
		}
		else
			return false;
	}
	else
	{
		return false;
	}

	return true;
}



const Bool GMCommandSystem::onMarble(EntityPlayer* player, const CommandString& cmd)
{
	// "[condreset, condset, throw, point]"
	// condreset : 해당 콘디션의 상태를 0 으로 ( 콘디션 아이디가 0 이면, 모두 리셋 )
	// condset : 해당 콘디션의 상태를 1 로 ( 콘디션 아이디가 0 이면, 모두 셋 )
	// throw : 숫자가 없으면 랜덤, 있으면 해당값만큼만.
	// point : 포인트를 강제로 셋팅한다. 

	VERIFY_RETURN(player && player->IsValid(), false);

	ActionPlayerInventory& api = player->GetInventoryAction();

	std::wstring command;
	cmd >> command;

	if (L"condreset" == command)
	{
		UInt32 conditionId = 0;
		cmd >> conditionId;

		api.MarbleResetCondition(conditionId);
	}
	else if (L"info" == command)
	{
		api.MarbleWriteInfo();
		api.MarbleWriteCondition();
		api.MarbleWriteHistory();
	}
	else if (L"conditions" == command)
	{
		api.MarbleWriteCondition();

	}
	else if (L"history" == command)
	{
		api.MarbleWriteHistory();
	}

	else if (L"dayreset" == command)
	{
		api.MarbleDailyReset(true);
	}
	else if (L"condset" == command)
	{
#ifdef __Patch_LegendMarble_v4_raylee_2019_1_23
		Int32 value = 0;
		UInt32 conditionId = 0;
		cmd >> conditionId >> value;

		// api.MarbleSetCondition(conditionId);
#else
		UInt32 conditionId = 0;
		cmd >> conditionId;

		api.MarbleSetCondition(conditionId);
#endif
	}
	else if (L"throw" == command)
	{
		UInt32 diceNum = 0;
		cmd >> diceNum;

#ifdef	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320
#else	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320
		api.MarbleThrowDice(diceNum, false);
#endif	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320
	}
	else if (L"throwredzen" == command)
	{
		UInt32 diceNum = 0;
		cmd >> diceNum;
#ifdef	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320
#else	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320
		api.MarbleThrowDice(diceNum, true);
#endif	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320
	}
	else if (L"point" == command)
	{
		MarblePlayPoint point = 0;
		cmd >> point;

		api.MarbleGMSetPoint(point);
	}
	else if (L"redzencount" == command)
	{
		MarblePlayPoint count = 0;
		cmd >> count;

		api.MarbleGMSetRedzenCount(count);
	}
	else if (L"setbox" == command)
	{
		Byte grade = 0;
		MarbleBoxExp exp = 0;
		cmd >> grade >> exp;

		api.MarbleGMSetBox(grade, exp);
	}
	else if (L"packet" == command)
	{
		std::wstring packet;
		cmd >> packet;

		if (0 == packet.compare(L"info"))
		{
			EReqMarbleInfo* req = NEW EReqMarbleInfo;
			theZoneHandler.Notify(player, EventPtr(req));
		}
		else if (0 == packet.compare(L"throw"))
		{
			EReqMarbleThrowDice* req = NEW EReqMarbleThrowDice;
			theZoneHandler.Notify(player, EventPtr(req));
		}
		else if (0 == packet.compare(L"arrive"))
		{
			EReqMarbleArriveArea* req = NEW EReqMarbleArriveArea;
			theZoneHandler.Notify(player, EventPtr(req));
		}
		else if (0 == packet.compare(L"pos"))
		{
			UInt32 seq = 0;
			cmd >> seq;

			EReqMarbleSelectPos* req = NEW EReqMarbleSelectPos;
			req->selectSequence = seq;

			theZoneHandler.Notify(player, EventPtr(req));
		}
		else if (0 == packet.compare(L"open"))
		{
			EReqMarbleOpenRewardBox* req = NEW EReqMarbleOpenRewardBox;
			theZoneHandler.Notify(player, EventPtr(req));
		}
	}

	return true;
}


const Bool GMCommandSystem::onPVEGameGiveup(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	EReqAirshipDefenceGameGiveUp* req = NEW EReqAirshipDefenceGameGiveUp;
	req->SetIdxBySessionKey(getSessionKey(player));
	theZoneHandler.Notify(player, EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onSelectGambleIndex(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Index32 selectIndex = 0, bagIndex = 0;;
	cmd >> bagIndex >> selectIndex;

	EReqSelectGambleItem* req = NEW EReqSelectGambleItem;
	req->gambleBagIndex = bagIndex;
	req->selectIndex = selectIndex;
	theZoneHandler.Notify(player, EventPtr(req));


	return true;
}

const Bool mu2::GMCommandSystem::onResetBuffEvent(EntityPlayer * player, const CommandString & cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Index32 eventIndex;
	cmd >> eventIndex;

	player->GetEventAction().CheatResetBuffEvent(eventIndex);

	return true;
}
const Bool mu2::GMCommandSystem::onOhrdorSewerageTimeout(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	auto ActionSectorController = player->GetSectorAction().GetActionSectorController();
	VERIFY_RETURN(ActionSectorController, false);

	OhrdorSewerageProcessor* ohrdorSewerageProcessor = dynamic_cast<OhrdorSewerageProcessor*>(ActionSectorController->GetSectorProcess());
	VALID_RETURN(ohrdorSewerageProcessor, false);

	ohrdorSewerageProcessor->DevTimeout();
	return true;
}

const Bool mu2::GMCommandSystem::onOhrdorSewerageInvasionCount(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Index32 invasionCount;
	cmd >> invasionCount;

	auto ActionSectorController = player->GetSectorAction().GetActionSectorController();
	VERIFY_RETURN(ActionSectorController, false);

	OhrdorSewerageProcessor* ohrdorSewerageProcessor = dynamic_cast<OhrdorSewerageProcessor*>(ActionSectorController->GetSectorProcess());
	VALID_RETURN(ohrdorSewerageProcessor, false);

	ohrdorSewerageProcessor->DevChangeInvasionCount(invasionCount);
	return true;
}

const Bool mu2::GMCommandSystem::onMissionMapReward(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	EReqPVEMissionMapReward* req = NEW EReqPVEMissionMapReward;
	EventPtr eventPtr(req);
	req->receiveState = 2;
	theZoneHandler.Notify(player, eventPtr);
	return true;
}


const Bool mu2::GMCommandSystem::onGotoGate(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	auto ActionSectorController = player->GetSectorAction().GetActionSectorController();
	VERIFY_RETURN(ActionSectorController, false);

	MazeInDarknessProcessor* mazeInDarknessProcessor = dynamic_cast<MazeInDarknessProcessor*>(ActionSectorController->GetSectorProcess());
	VALID_RETURN(mazeInDarknessProcessor, false);

	Vector3 dstPos = mazeInDarknessProcessor->GetRealGatePos();

	Vector3 rightPos;

	auto actSector = player->GetAction< ActionSector >();
	if (!actSector->GetValidPosition(dstPos, rightPos))
	{
		theGameMsg.SendGameDebugMsg(player, L"Fail to changepos :%f %f %f", dstPos.x, dstPos.y, dstPos.z);
		return false;
	}

	if (actSector->Place(rightPos))
	{
		ENtfGameForceChangePos* ntf = NEW ENtfGameForceChangePos;
		ntf->entityId = player->GetId();
		ntf->stopPosition = rightPos;
		ntf->dir = 0;
		actSector->NearGridCast(EventPtr(ntf));

		theGameMsg.SendGameDebugMsg(player, L"Success to changepos :%f %f %f", rightPos.x, rightPos.y, rightPos.z);

		return true;
	}

	theGameMsg.SendGameDebugMsg(player, L"Fail to changepos :%f %f %f", dstPos.x, dstPos.y, dstPos.z);
	return true;
}
const Bool GMCommandSystem::onGuideArrow(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	auto ActionSectorController = player->GetSectorAction().GetActionSectorController();
	VALID_RETURN(ActionSectorController, true);

	MazeInDarknessProcessor* mazeInDarknessProcessor = dynamic_cast<MazeInDarknessProcessor*>(ActionSectorController->GetSectorProcess());
	VALID_RETURN(mazeInDarknessProcessor, true);

	mazeInDarknessProcessor->DevSendGuideArrow(player);
	return true;
}

const Bool GMCommandSystem::onGuideCategoryEvent(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 trigerType;
	UInt32 value;

	cmd >> trigerType >> value;

	ActionPlayerGuideSystem* playerGuideSystem = GetEntityAction(player);


	switch (trigerType)
	{
	case static_cast<UInt32>(GuideTriggerType::CHARACTER_LEVEL) :
	{
		GuideTriggerCharacterLevelUp triggerEvent(static_cast<Byte>(value));
		playerGuideSystem->OnCheckTrigger(&triggerEvent);
		if (value == 0)
		{
			value = player->GetLevel();
		}

		theGameMsg.SendGameDebugMsg(player, L"CharacterLevelUp trigger event - value(%d)", value);
	}
																break;

	case static_cast<UInt32>(GuideTriggerType::COMPLETE_CATEGORY) :
	{
		GuideTriggerCompleteCategory triggerEvent(value);
		playerGuideSystem->OnCheckTrigger(&triggerEvent);
		theGameMsg.SendGameDebugMsg(player, L"Complete_Category trigger event - value(%d)", value);
	}
																  break;

	case static_cast<UInt32>(GuideTriggerType::CHARACTER_RETURN) :
	{
		GuideTriggerReturnCharacter triggerEvent(static_cast<UInt16>(value));
		playerGuideSystem->OnCheckTrigger(&triggerEvent);
		theGameMsg.SendGameDebugMsg(player, L"ReturnUser trigger event - value(%d)", value);
	}
																 break;

	default:
		theGameMsg.SendGameDebugMsg(player, L"%d : invalid category trigger type! ( range : %d ~ %d )",
			trigerType, GuideTriggerType::CHARACTER_LEVEL, GuideTriggerType::MAX - 1);
		break;
	}

	return true;
}

const Bool GMCommandSystem::onGuideCategoryReset(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexGuide category;
	cmd >> category;

	ActionPlayerGuideSystem* playerGuideSystem = GetEntityAction(player);

	if (category == 0)
	{
		// 모든 카테고리를 삭제한다.
		playerGuideSystem->Reset();
		playerGuideSystem->SendGuideSystemToClient();
	}
	else
	{
		const GuideSystemScript* script = SCRIPTS.GetScript< GuideSystemScript >();

		EReqDbDeleteCharGuideSystem* dbReq = new EReqDbDeleteCharGuideSystem;
		ENtfGameGuideSystemDeleteList* ntf = new ENtfGameGuideSystemDeleteList;

		dbReq->charId = player->GetCharId();

		while (category != 0)
		{
			auto guideCategory = playerGuideSystem->GetGuideCategory(category);
			if (guideCategory)
			{
				guideCategory->ResetDivision();
				dbReq->guideIndexes.push_back(category);
				ntf->categoryIndexes.push_back(category);
			}

			VERIFY_DO(script, break);

			category = script->GetNextCategoryIndex(category);
		}

		SERVER.SendToDb(player, EventPtr(dbReq));
		SERVER.SendToClient(player, EventPtr(ntf));
	}

	return true;
}

const Bool GMCommandSystem::onGuideMissionClear(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexGuide category;
	Int32 division = 0;
	cmd >> category >> division;

	ActionPlayerGuideSystem* playerGuideSystem = GetEntityAction(player);

	// 보상처리로 변경한다.

	auto guideCategory = playerGuideSystem->GetGuideCategory(category);
	if (guideCategory == nullptr)
	{
		theGameMsg.SendGameDebugMsg(player, L"not found guide category (%d)", category);
		return false;
	}

	ClientGuideSystemInfo clientInfo;

	if (division == 0)
	{
		guideCategory->Pack(clientInfo);
	}
	else
	{
		clientInfo.category = category;
		const GuideSystemConditionScript* script = SCRIPTS.GetScript< GuideSystemConditionScript >();

		const GuideSystemConditionElem* elem = script->Get(category);

		Int32 max = -1;
		Int32 correct = -1;
		Int32 correctTotal = 0;
		for (auto& part : elem->conditions)
		{
			if (part.division == division)
			{
				correct = division;
				AchievementCountCondition* condition = dynamic_cast<AchievementCountCondition*>(part.countCondition.get());
				correctTotal = (condition) ? condition->count : 0;
				break;
			}
			else if (max < part.division)
			{
				max = part.division;
				AchievementCountCondition* condition = dynamic_cast<AchievementCountCondition*>(part.countCondition.get());
				correctTotal = (condition) ? condition->count : 0;
			}
		}

		if (correct != division)
		{
			correct = max;
		}

		clientInfo.division = static_cast<Byte>(correct);
		clientInfo.divisionTotal = correctTotal;
	}


	EReqDbUpdateCharGuideSystem* dbReq = new EReqDbUpdateCharGuideSystem;
	dbReq->charId = player->GetCharId();
	dbReq->info.guideIndex = category;
	dbReq->info.division = clientInfo.division;
	dbReq->info.divisionValue = clientInfo.divisionTotal;
	dbReq->info.divisionState = GuideSystemState::REWARD;

	guideCategory->SetupFromDb(dbReq->info);

	SERVER.SendToDb(player, EventPtr(dbReq));

	ENtfGameGuideSystemUpdateInfo* ntf = new ENtfGameGuideSystemUpdateInfo;
	clientInfo.eState = GuideSystemState::REWARD;
	ntf->info = std::move(clientInfo);
	SERVER.SendToClient(player, EventPtr(ntf));

	return true;
}

const Bool GMCommandSystem::onGuideMissionReward(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	IndexGuide category;
	cmd >> category;

	ActionPlayerGuideSystem* playerGuideSystem = GetEntityAction(player);

	// 보상처리로 변경한다.

	auto guideCategory = playerGuideSystem->GetGuideCategory(category);
	if (nullptr == guideCategory)
	{
		theGameMsg.SendGameDebugMsg(player, L"not found guide category (%d)", category);
		return false;
	}

	ClientGuideSystemInfo info;
	guideCategory->Pack(info);

	if (info.eState != GuideSystemState::REWARD)
	{
		theGameMsg.SendGameDebugMsg(player, L"divisin not reward state : category(%d), division(%d), state(%d)", category, info.division, static_cast<Byte>(info.eState));
		return false;
	}

	EReqGameGuideSystemGetReward* req = new EReqGameGuideSystemGetReward;
	req->category = category;
	req->division = info.division;
	theZoneHandler.Notify(player, EventPtr(req));

	return true;
}


const Bool GMCommandSystem::onSetReturnDays(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring type;
	Int32 days;
	cmd >> type >> days;

	if (type.compare(L"account") == 0)
	{
		// 계정 날자를 업데이트 시킨다.
		ENtfDbUpdateAccountReturnUserDays* ntf = new ENtfDbUpdateAccountReturnUserDays;
		ntf->accountGuid = player->GetAccountId();
		ntf->returnUserDays = days;
		SERVER.SendToDb(player, EventPtr(ntf));
	}
	else if (type.compare(L"character") == 0)
	{
		// 현재 캐릭터 날짜를 업데이트 시킨다.		
		ENtfDbUpdateCharacterLoginInfo* ntf = new ENtfDbUpdateCharacterLoginInfo;
		ntf->charId = player->GetCharId();
		ntf->longestReturnCharacterDays = days;
		SERVER.SendToDb(player, EventPtr(ntf));
	}
	else
	{
		theGameMsg.SendGameDebugMsg(player, L"usage : /setreutrndays [type][days] - (tyep : account or character)");
	}

	return true;
}



const Bool GMCommandSystem::onGameSystemReturnUserGetReward(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	EReqGameSystemReturnUserGetReward* req = new EReqGameSystemReturnUserGetReward;
	theZoneHandler.Notify(player, EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onPetBoosterSkillReset(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	player->GetPetManageAction().ResetPetBoosterSkillList();

	return true;
}

const Bool GMCommandSystem::onPetQuestReset(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	player->GetPetManageAction().ResetPetQuest();

	return true;
}

const Bool GMCommandSystem::onPetInfoUpdateAll(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	player->GetPetManageAction().UpdatePetInfoAll();

	return true;
}

const Bool GMCommandSystem::onSetItemSellFlag(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	std::wstring input;
	cmd >> input;

	if (input.compare(L"open") == 0)
	{
		EReqGameSetItemSellFalg* req = new EReqGameSetItemSellFalg;
		req->SetIdxBySessionKey(getSessionKey(player));
		req->flag = EReqGameSetItemSellFalg::OPEN;
		theZoneHandler.Notify(player, EventPtr(req));

	}
	else if (input.compare(L"close") == 0)
	{
		EReqGameSetItemSellFalg* req = new EReqGameSetItemSellFalg;
		req->SetIdxBySessionKey(getSessionKey(player));
		req->flag = EReqGameSetItemSellFalg::CLOSE;
		theZoneHandler.Notify(player, EventPtr(req));
	}
	else
	{
		theGameMsg.SendGameDebugMsg(player, L"usage : /setsellflag [ open/close ]");
		return false;
	}

	return true;
}

const Bool GMCommandSystem::onFloorSpawn(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	ActionSector* actionSector = player->GetAction<ActionSector>();
	VERIFY_RETURN(actionSector, false);

	auto ActionSectorController = actionSector->GetActionSectorController();
	VERIFY_RETURN(ActionSectorController, false);

	PVPChaosCastleProcessor* pvpChaosCastleProcessor = dynamic_cast<PVPChaosCastleProcessor*>(ActionSectorController->GetSectorProcess());
	VERIFY_RETURN(pvpChaosCastleProcessor, false);

	pvpChaosCastleProcessor->DevFloorSpawn();

	return true;

}

const Bool GMCommandSystem::onNoticePopUpReset(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	ActionPlayerNoticePopUp* apNoticePopUp = GetEntityAction(player);
	VALID_RETURN(apNoticePopUp, false);
	apNoticePopUp->ResetNoticePopUp();
	return true;
}
const Bool GMCommandSystem::onNoticePopUpLoad(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	ActionPlayerNoticePopUp* apNoticePopUp = GetEntityAction(player);
	VALID_RETURN(apNoticePopUp, false);
	apNoticePopUp->ReqNoticePopupInfo();
	return true;
}

const Bool GMCommandSystem::onTreasureOpen(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	EczReqTreasureOpen* req = new EczReqTreasureOpen;
	theZoneHandler.Notify(player, EventPtr(req));
	return true;
}

const Bool GMCommandSystem::onTreasureClose(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	EczReqTreasureClose* req = new EczReqTreasureClose;
	theZoneHandler.Notify(player, EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onTreasurePickup(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	EczReqTreasurePickup* req = new EczReqTreasurePickup;
	theZoneHandler.Notify(player, EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onTreasureRefreshSlot(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	EczReqTreasureRefreshSlot* req = new EczReqTreasureRefreshSlot;
	theZoneHandler.Notify(player, EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onTreasureRefillRefreshCount(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	ItemPos itemPos;
	cmd >> itemPos;

	const ItemConstPtr foundItem = player->GetInventoryAction().GetItem(itemPos);
	VERIFY_RETURN(foundItem, false);

	EczReqTreasureItemUseRefillRefreshCount* req = new EczReqTreasureItemUseRefillRefreshCount;
	req->srcItem = foundItem->GetSlotInfo();
	theZoneHandler.Notify(player, EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onTreasureRefillAllUsers(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	player->GetTreasure().ReqDbTreasureRefillAllUserCountGmCommand();

	return true;
}
const Bool GMCommandSystem::onEventBuffOnOff(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	wstring eventBuff;
	IndexSkill buffSkillIndex = 0;
	cmd >> eventBuff >> buffSkillIndex;

	if (0 == eventBuff.compare(L"on"))
	{
		player->GetPlayerEventBuffAction().DevOnEventBuff(buffSkillIndex);
	}
	else if (0 == eventBuff.compare(L"off"))
	{
		player->GetPlayerEventBuffAction().DevOffEventBuff(buffSkillIndex);
	}

	return true;
}


const Bool GMCommandSystem::onItemFusion(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);
	Items foundItems;
	ItemFinderByDivision(*player, IDT::ITEM_RUNE).Find(InvenBags, foundItems);
	VALID_RETURN(3 <= foundItems.size(), false);

	EczItemRuneFusion* req = new EczItemRuneFusion;
	Int32 count = 0;
	for (auto iter : foundItems)
	{
		VALID_DO(count < 3, break);

		req->fusionItems.push_back(iter->GetPlace());
		count++;
	}

	theZoneHandler.Notify(player, EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onRuneEquip(EntityPlayer* player, const CommandString&)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Items foundItems;
	ItemFinderByDivision(*player, IDT::ITEM_RUNE).Find(InvenBags, foundItems);
	VALID_RETURN(false == foundItems.empty(), false);

	EReqEquip* req = new EReqEquip;
	req->eEquipType = SlotType::RUNE;

	auto item = foundItems.begin();
	(*item)->FillUp(req->item);

	theZoneHandler.Notify(player, EventPtr(req));

	return true;
}

#ifdef ABILITY_RENEWAL_20180508
const Bool GMCommandSystem::onOpenBraceletSlot(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Byte state = 0;
	cmd >> state;

	switch (state)
	{
	default:
	case 0:
	{
		Items foundItems;
		ItemFinderByDivision(*player, IDT::BRACELET_OPEN_SCROLL).Find(InvenBags, foundItems);
		VALID_RETURN(false == foundItems.empty(), false);

		EczOpenBraceletSlot* req = new EczOpenBraceletSlot;

		auto item = foundItems.begin();
		(*item)->FillUp(req->useItem);

		theZoneHandler.Notify(player, EventPtr(req));
	}
	break;
	case 1:
	{
		ItemBraceletSlotOpenTranscation* transaction = NEW ItemBraceletSlotOpenTranscation(__FUNCTION__, __LINE__, player, LogCode::E_BRACELT_SLOT_OPEN);
		TransactionPtr transPtr(transaction);
		{
			transaction->SetState(false);
			VERIFY_RETURN(TransactionSystem::Register(transPtr), false);
		}
	}
	break;
	}


	return true;
}

const Bool GMCommandSystem::onBraceletEquip(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);
	Items foundItems;

	Byte state = 0;
	cmd >> state;

	switch (state)
	{
	case 0:
		{
			ItemFinderByDivision( *player, IDT::BRACELET ).Find( InvenBags, foundItems );
			VALID_RETURN( false == foundItems.empty(), false );

			EReqEquip* req = new EReqEquip;
			req->eEquipType = SlotType::BRACELET;

			auto item = foundItems.begin();
			(*item)->FillUp( req->item );

			theZoneHandler.Notify( player, EventPtr( req ) );
		}
		break;
	case 1:
		{
			const ItemConstPtr item = player->GetInventoryAction().GetItem(ItemPos(InvenType::EQUIP, SlotType::BRACELET));
			VALID_RETURN( item, false );

			EReqUnequip* req = new EReqUnequip;
			item->FillUp( req->item );
			player->GetInventoryAction().GetFreeSlot( InvenBags, req->pos );
			theZoneHandler.Notify( player, EventPtr( req ) );
		}
		break;
	}


	return true;
}

const Bool GMCommandSystem::onSelectOption( EntityPlayer* player, const CommandString& cmd )
{
	VERIFY_RETURN( player && player->IsValid(), false );

	ItemPos slotInfo;
	IndexItem index;
	SelectAbilityIndex selectIndex;
	Items foundItems;

	cmd >> slotInfo >> index >> selectIndex;

	EczUseOptionScroll* req = new EczUseOptionScroll;
	VALID_RETURN( req, false );
	EventPtr eventReq( req );

	const ItemConstPtr itemSlot = player->GetInventoryAction().GetItem( slotInfo );
	VALID_RETURN( itemSlot, false );
	itemSlot->FillUp( req->targetItem );

	ItemFinderByIndex( *player, index ).Find( InvenBags << PremiumBags, foundItems );
	VALID_RETURN( false == foundItems.empty(), false );

	auto item = foundItems.begin();
	(*item)->FillUp( req->useItem );

	req->selectAbilityIndex = selectIndex;

	theZoneHandler.Notify( player, eventReq );

	return true;
}

const Bool GMCommandSystem::onAliveCount( EntityPlayer* player, const CommandString& cmd )
{
	VERIFY_RETURN( player && player->IsValid(), false );

	Byte type = 0;
	cmd >> type;

	UInt32 count = 0;

	Sector *sector = player->GetSector();
	VALID_RETURN( sector, false );

	switch (type)
	{
	case 0:
	default:
		count = sector->GetCurPlayerCount();
		break;
	case 1:
		count = sector->GetCurAlivePlayerCount();
		break;
	}

	theGameMsg.SendGameDebugMsg( player, L"%d 타입의 유저는%d 입니다.", type, count );

	return true;
}

#endif


const Bool GMCommandSystem::onShowDifficulty(EntityPlayer* player, const CommandString& cmd)
{
	DifficultyLevel difficulty = player->GetSector()->GetDifficulty();

	theGameMsg.SendGameDebugMsg(player, L"Sector Difficulty %d", difficulty);

	return true;
}


#ifdef	__Patch_Limited_Banner_Shop_robinhwp
const Bool GMCommandSystem::onLimitedBannerSystem(EntityPlayer* player, const CommandString& cmd)
{
	wchar_t* usage_del = L"Usage : /limitedbanner del [banner index]";
#ifdef __Patch_Add_Promotion_Banner_Condition_Type_by_robinhwp_20190312
	wchar_t* usage_event_cl = L"Usage : /limitedbanner event [[CHARACTER_LEVEL/cl][SOUL_LEVEL/sl][RETURN_CHARACTER/rc [days]]]";
	Int32 returnDays = 0;
	std::wstring type;
	cmd >> type >> returnDays;
#else __Patch_Add_Promotion_Banner_Condition_Type_by_robinhwp_20190312
	wchar_t* usage_event_cl = L"Usage : /limitedbanner event [[CHARACTER_LEVEL/cl][SOUL_LEVEL/sl]]";

	std::wstring type;
	cmd >> type;
#endif __Patch_Add_Promotion_Banner_Condition_Type_by_robinhwp_20190312

	if (type.compare(L"event") == 0)
	{
		std::wstring conditionType;
		cmd >> conditionType;
		if (conditionType.compare(L"CHARACTER_LEVEL") == 0 || conditionType.compare(L"cl") == 0)
		{
			LimitedBannerEvent limitedBannerEvent(LimitedBannerConditionType::CHARACTER_LEVEL, LimitedBannerConditionValue(player->GetLevel()));
			player->GetPlayerLimitedBannerGoodsAction().CheckAndAddToDB(limitedBannerEvent);
		}
		else if (conditionType.compare(L"SOUL_LEVEL") == 0 || conditionType.compare(L"sl") == 0)
		{
			LimitedBannerEvent limitedBannerEvent(LimitedBannerConditionType::SOUL_LEVEL, LimitedBannerConditionValue(player->GetSoulLevelEx()));
			player->GetPlayerLimitedBannerGoodsAction().CheckAndAddToDB(limitedBannerEvent);
		}
#ifdef __Patch_Add_Promotion_Banner_Condition_Type_by_robinhwp_20190312
		else if (conditionType.compare(L"RETURN_CHARACTER") == 0 || conditionType.compare(L"rc") == 0)
		{
			LimitedBannerEvent limitedBannerEvent(LimitedBannerConditionType::RETURN_CHARACTER, LimitedBannerConditionValue(returnDays));
			player->GetPlayerLimitedBannerGoodsAction().CheckAndAddToDB(limitedBannerEvent);
		}
#endif __Patch_Add_Promotion_Banner_Condition_Type_by_robinhwp_20190312
		else
		{
			theGameMsg.SendGameDebugMsg(player, usage_event_cl);
			return false;
		}
	}
	else if (type.compare(L"del") == 0)
	{
		IndexBanner bannerIndex;
		cmd >> bannerIndex;

		auto elem = SCRIPTS.GetNoticeLimitedSellElem(bannerIndex);
		if (elem == nullptr)
		{
			theGameMsg.SendGameDebugMsg(player, L"cannot found %d index in NoticeLimitedSellElems", bannerIndex);
			return false;
		}

		player->GetPlayerLimitedBannerGoodsAction().DevOnly_DeleteGoodsInfo(bannerIndex);
	}
	else if (type.compare(L"buy") == 0)
	{
		IndexBanner bannerIndex;
		cmd >> bannerIndex;

		
		EReqBuyLimitedBannerGoods* req = new EReqBuyLimitedBannerGoods;
		req->SetIdxBySessionKey(getSessionKey(player));
		req->bannerIndex = bannerIndex;
		auto elem = SCRIPTS.GetNoticeLimitedSellElem(bannerIndex);
		if (elem)
		{
#ifdef	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320
			auto& iter = elem->costPackSet.GetList().begin();
			if (iter != elem->costPackSet.GetList().end())
			{
				req->costPack = *iter;
			}
#else	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320
			req->costType = elem->price.type;
			req->costValue = elem->price.value;
#endif	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320
		}

		theZoneHandler.Notify(player, EventPtr(req));
	}
	else
	{
		theGameMsg.SendGameDebugMsg(player, usage_event_cl);
		theGameMsg.SendGameDebugMsg(player, usage_del);
		return false;
	}


	return true;
}
#endif	__Patch_Limited_Banner_Shop_robinhwp
#ifdef __lockable_item_181001
const Bool GMCommandSystem::onLockableItem( EntityPlayer* player, const CommandString& cmd )
{
	ItemPos pos;
	Bool state;

	cmd >> pos >> state;

	Inven * inven = player->GetInventoryAction().GetInven(pos);
	VALID_RETURN( inven, false );
	VALID_RETURN( inven->IsInvenBags() || 
					inven->IsPlatinumBag() || 
					inven->IsPremiumBag() || 
					inven->IsPremiumBag() ||
					inven->IsEquipBag(), false );

	const ItemConstPtr item = player->GetInventoryAction().GetItem(pos);
	VALID_RETURN( item, false);

	EczLockableItem* req = NEW EczLockableItem;
	req->selectItem.SetPos(pos);
	req->selectItem.itemId = item->GetItemId();
	req->isLockable = state;
	player->OnEvent( EventPtr( req ) );

	return true;
}

const Bool GMCommandSystem::onLockableItemSetup( EntityPlayer* player, const CommandString& cmd )
{
	UInt32 state = 0;
	cmd >> state;

	player->GetInventoryAction().SetItemLockableCheat( state != 0 ? true : false );
	
	return true;
}
#endif

#ifdef __Patch_Event_Buff_Add_Benefit_by_jjangmo_20181122
const Bool GMCommandSystem::onEventBuffGift( EntityPlayer* player, const CommandString& cmd )
{
	std::wstring command = L"";

	cmd >> command;

	if ( command.compare( L"reward" ) == 0 )
	{
		EczReqEventBuffGiftGetReward* req = new EczReqEventBuffGiftGetReward;
		theZoneHandler.Notify( player, EventPtr( req ) );
	}
	else if ( command.compare( L"reset" ) == 0 )
	{
		player->GetPlayerEventBuffAction().DevResetGiftInfo();
	}

	return true;
}

#endif


#ifdef __Patch_ImprintedMemory_by_raylee_181128
const Bool GMCommandSystem::onImprint(EntityPlayer* player, const CommandString& cmd)
{
	std::wstring command = L"";

	cmd >> command;

	if (command.compare(L"complete") == 0)
	{
		player->GetAchievementAction().GMCommand_Complete(player);
	}

	return true;
}
#endif

#ifdef __Patch_Legend_Of_Bingo_by_jaekwan_20181126
const Bool GMCommandSystem::onOpenBingoEvent(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	EReqBingoEventInfo* req = NEW EReqBingoEventInfo;

	req->eventIndex = SCRIPTS.GetBingoEventIndex();

	theZoneHandler.Notify(player, EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onSetBingoPoint(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt8 bingoPoint = 0;

	cmd >> bingoPoint;

	ActionPlayerEvent *actionPlayerEvent = GetEntityAction(player);

	VALID_RETURN(actionPlayerEvent->onSetBingoPoint(bingoPoint), false);

	return true;
}

const Bool GMCommandSystem::onSetBingoResetPoint(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt8 resetPoint = 0;

	cmd >> resetPoint;

	ActionPlayerEvent *actionPlayerEvent = GetEntityAction(player);

	VALID_RETURN(actionPlayerEvent->onSetBingoResetPoint(resetPoint), false);

	return true;
}

const Bool GMCommandSystem::onResetBingoEventInfo(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	ActionPlayerEvent *actionPlayerEvent = GetEntityAction(player);

	VALID_RETURN(actionPlayerEvent->onResetBingoEvent(), false);

	return true;
}
#endif // __Patch_Legend_Of_Bingo_by_jaekwan_20181126


#ifdef __Patch_Talismanbox_Ticket_Reward_System_by_ch_181224
const Bool GMCommandSystem::onResetTalismanboxTicketRewardInfo(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);
#ifdef __Patch_Add_Imputed_Talismanbox_Ticket_Reward_by_ch_20190513
	UInt8 resetFlag = 0;
	cmd >> resetFlag;
#endif // __Patch_Add_Imputed_Talismanbox_Ticket_Reward_by_ch_20190513

	ActionPlayerInventory *actionPlayerInventory = GetEntityAction(player);
#ifdef __Patch_Add_Imputed_Talismanbox_Ticket_Reward_by_ch_20190513
	BYTE curFlag = actionPlayerInventory->GetTalismanboxTicketRewardCondition();
	if(TalismanboxTicketRewardCondition::NONE == resetFlag)
	{		
		actionPlayerInventory->SetTalismanboxTicketRewardCondition(TalismanboxTicketRewardCondition::NONE);
	}
	else if(TalismanboxTicketRewardCondition::RECIEVE_REDZEN_REWARD == resetFlag && TalismanboxTicketRewardCondition::RECIEVE_REDZEN_REWARD <= curFlag)
	{
		actionPlayerInventory->SetTalismanboxTicketRewardCondition(static_cast<TalismanboxTicketRewardCondition::Enum>(curFlag - TalismanboxTicketRewardCondition::RECIEVE_REDZEN_REWARD));
	}
	else if(TalismanboxTicketRewardCondition::RECIEVE_IMPUTED_REDZEN_REWARD == resetFlag && TalismanboxTicketRewardCondition::RECIEVE_IMPUTED_REDZEN_REWARD <= curFlag)
	{
		actionPlayerInventory->SetTalismanboxTicketRewardCondition(static_cast<TalismanboxTicketRewardCondition::Enum>(curFlag - TalismanboxTicketRewardCondition::RECIEVE_IMPUTED_REDZEN_REWARD));
	}
	else
	{
		return false;
	}
#else
	actionPlayerInventory->SetTalismanboxTicketRewardCondition(TalismanboxTicketRewardCondition::NONE);
#endif // __Patch_Add_Imputed_Talismanbox_Ticket_Reward_by_ch_20190513

	EReqDbUspResetTalismanboxTicketReward *req = NEW EReqDbUspResetTalismanboxTicketReward;
	req->accountId = player->GetAccountId();
#ifdef __Patch_Add_Imputed_Talismanbox_Ticket_Reward_by_ch_20190513
	req->rewardFlag = actionPlayerInventory->GetTalismanboxTicketRewardCondition();
#endif // __Patch_Add_Imputed_Talismanbox_Ticket_Reward_by_ch_20190513

	SERVER.SendToDb(player, EventPtr(req));

	return true;
}
#endif

#ifdef __Patch_WebOfGod_by_jason_20181213
const Bool GMCommandSystem::onWebOfGod(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	Sector* sector = player->GetSector();
	VERIFY_RETURN(sector, false);

	SectorProcessor* found = sector->GetSectorController().GetControllerAction().GetSectorProcess();
	VERIFY_RETURN(found && found->GetType() == SectorProcessorType::WEB_OF_GOD, false);

	static_cast<WebOfGodProcessor*>(found)->OnGMCommand(*player, cmd);

	

	return true;
}
#endif
const Bool GMCommandSystem::onSendMailWithItem(EntityPlayer* player, const CommandString& cmd)
{
	SimpleItem simpleItem;
	cmd >> simpleItem.itemIndex >> simpleItem.itemCount;

	MailData mail;
	mail.recverName = player->GetCharName();;
	mail.senderName = L"test";
	mail.subject = L"test";
	mail.content = L"test";

	SimpleItems simpleItems;
	simpleItems.push_back(simpleItem);
	player->GetMailBoxAction().SendSystemMail(mail, simpleItems);

	return true;
}

#ifdef __Patch_Mercler_Secret_Shop_by_ch_2019_04_24
const Bool GMCommandSystem::onResetMerclerSecretShopInfo(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	ActionPlayerInventory *actionPlayerInventory = GetEntityAction(player);
	MerclerSecretShopInfo& merclerSecretShopInfo = actionPlayerInventory->GetMerclerSecretShopInfo();

	merclerSecretShopInfo.identify = false;
	merclerSecretShopInfo.lastBuyIndex = 0;	
	merclerSecretShopInfo.bonusIndex = 0;
	merclerSecretShopInfo.bonusCount = 0;
	merclerSecretShopInfo.updateTime = DateTime::GetPresentTime(); // 업데이트 시간 갱신은 체크가 다 끝난 후.

	EReqDbUspMerclerSecretShopInfoUpdate* req = NEW EReqDbUspMerclerSecretShopInfoUpdate;
	req->accountId = merclerSecretShopInfo.accountId;
	req->updateTime = merclerSecretShopInfo.updateTime;
	req->identify = merclerSecretShopInfo.identify;
	req->productIndex = merclerSecretShopInfo.lastBuyIndex;
	req->bonusIndex = merclerSecretShopInfo.bonusIndex;
	req->bonusCount = merclerSecretShopInfo.bonusCount;

	SERVER.SendToDb(player, EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onBuyMerclerSecretShop(EntityPlayer* player, const CommandString& cmd)
{
	VERIFY_RETURN(player && player->IsValid(), false);

	ActionPlayerInventory *actionPlayerInventory = GetEntityAction(player);
	MerclerSecretShopInfo& merclerSecretShopInfo = actionPlayerInventory->GetMerclerSecretShopInfo();
	
	auto sellElem = SCRIPTS.GetScript<MerclerSecretShopSellScript>()->GetNext(merclerSecretShopInfo.lastBuyIndex);
	VALID_RETURN(sellElem, true);
	CostPack costPack = *(sellElem->costPackSet.GetList().begin());
		
	ErrorItem::Error eError = player->GetInventoryAction().BuyMerclerSecretShop(sellElem->productIndex, costPack, true); // 강제 구매. 날짜 체크 무시.
	if(eError != ErrorItem::SUCCESS)
	{
		EResMerclerSecretShopInfo* res = NEW EResMerclerSecretShopInfo;
		res->eError = eError;

		SERVER.SendToClient(player, EventPtr(res));
	}

	return true;
}
#endif // __Patch_Mercler_Secret_Shop_by_ch_2019_04_24


#ifdef __Patch_SetItem_Renewal_by_kangms_2019_4_10

const Bool GMCommandSystem::onSetCardOpen(EntityPlayer *player, const CommandString& cmd)
{
	SetCardIndex targetSetCardIndex;
	cmd >> targetSetCardIndex;

	EczReqSetCardOpen* req = NEW EczReqSetCardOpen;
	req->reqOpenSetCardIndex = targetSetCardIndex;

	theZoneHandler.Notify(player, EventPtr(req));

	return true;
}

const Bool GMCommandSystem::onSetCardClose(EntityPlayer *player, const CommandString& cmd)
{
	SetCardIndex targetSetCardIndex;
	cmd >> targetSetCardIndex;

	auto actPlayerSetCard = player->GetAction<ActionPlayerSetCard>();

	return actPlayerSetCard->TestCloseSetCard(targetSetCardIndex);
}

const Bool GMCommandSystem::onSetCardCloseAll(EntityPlayer *player, const CommandString& cmd)
{
	auto actPlayerSetCard = player->GetAction<ActionPlayerSetCard>();

	return actPlayerSetCard->TestCloseSetCardAll();
}

const Bool GMCommandSystem::onSetPoint(EntityPlayer *player, const CommandString& cmd)
{
	std::wstring command;
	std::array<UInt32, 2> value;
	cmd >> command >> value[0] >> value[1];

	auto actPlayerSetCard = player->GetAction<ActionPlayerSetCard>();

	if (command == L"change") {
		return actPlayerSetCard->TestChangeSetPoint(static_cast<SetPointType::Enum>(value[0]), value[1]);
	}
	else if (command == L"calc") {
		return actPlayerSetCard->TestCalculateSetPoint();
	}

	return false;
}

const Bool GMCommandSystem::onSetCardEquipPos(EntityPlayer *player, const CommandString& cmd)
{
	std::array<UInt32, 2> value;
	cmd >> value[0] >> value[1];

	EczReqSetCardEquip* req = NEW EczReqSetCardEquip;
	req->sourceSetCardIndex = value[0];
	req->targetPos = static_cast<UInt16>(value[1]);

	theZoneHandler.Notify(player, EventPtr(req));

	return true;
}

#endif//__Patch_SetItem_Renewal_by_kangms_2019_4_10



} // namespace mu2
