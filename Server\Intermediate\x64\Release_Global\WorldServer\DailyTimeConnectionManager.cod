; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	??1DailyComparer@mu2@@QEAA@XZ			; mu2::DailyComparer::~DailyComparer
PUBLIC	?GetDailyTimeStamp@DailyComparer@mu2@@QEAA?AU_SYSTEMTIME@@XZ ; mu2::DailyComparer::GetDailyTimeStamp
PUBLIC	?CompareDailyTimeStamp@DailyComparer@mu2@@QEAA_NAEBU_SYSTEMTIME@@@Z ; mu2::DailyComparer::CompareDailyTimeStamp
PUBLIC	??_G?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@UEAAPEAXI@Z ; mu2::ISingleton<mu2::DailyTimeConnectionManager>::`scalar deleting destructor'
PUBLIC	??1DailyTimeConnectionManager@mu2@@UEAA@XZ	; mu2::DailyTimeConnectionManager::~DailyTimeConnectionManager
PUBLIC	?Init@DailyTimeConnectionManager@mu2@@UEAA_NPEAX@Z ; mu2::DailyTimeConnectionManager::Init
PUBLIC	?UnInit@DailyTimeConnectionManager@mu2@@UEAA_NXZ ; mu2::DailyTimeConnectionManager::UnInit
PUBLIC	?InitDbOpen@DailyTimeConnectionManager@mu2@@QEAAXXZ ; mu2::DailyTimeConnectionManager::InitDbOpen
PUBLIC	?IsOpenEvent@DailyTimeConnectionManager@mu2@@QEAA_NXZ ; mu2::DailyTimeConnectionManager::IsOpenEvent
PUBLIC	?GetEventIndex@DailyTimeConnectionManager@mu2@@QEAAGXZ ; mu2::DailyTimeConnectionManager::GetEventIndex
PUBLIC	?GetDivisionDate@DailyTimeConnectionManager@mu2@@QEAA?AU_SYSTEMTIME@@XZ ; mu2::DailyTimeConnectionManager::GetDivisionDate
PUBLIC	?GetEventDate@DailyTimeConnectionManager@mu2@@QEAA_NAEAU_SYSTEMTIME@@0@Z ; mu2::DailyTimeConnectionManager::GetEventDate
PUBLIC	?Update@DailyTimeConnectionManager@mu2@@QEAAXXZ	; mu2::DailyTimeConnectionManager::Update
PUBLIC	??0DailyTimeConnectionManager@mu2@@AEAA@XZ	; mu2::DailyTimeConnectionManager::DailyTimeConnectionManager
PUBLIC	?isDurationOfEvent@DailyTimeConnectionManager@mu2@@AEAA_NU_SYSTEMTIME@@0@Z ; mu2::DailyTimeConnectionManager::isDurationOfEvent
PUBLIC	?checkUpdateTimer@DailyTimeConnectionManager@mu2@@AEAA_NXZ ; mu2::DailyTimeConnectionManager::checkUpdateTimer
PUBLIC	?checkEventOpen@DailyTimeConnectionManager@mu2@@AEAA_NXZ ; mu2::DailyTimeConnectionManager::checkEventOpen
PUBLIC	?updateEventOpen@DailyTimeConnectionManager@mu2@@AEAA_NXZ ; mu2::DailyTimeConnectionManager::updateEventOpen
PUBLIC	?updateEventClose@DailyTimeConnectionManager@mu2@@AEAA_NXZ ; mu2::DailyTimeConnectionManager::updateEventClose
PUBLIC	?updatePlayers@DailyTimeConnectionManager@mu2@@AEAAXXZ ; mu2::DailyTimeConnectionManager::updatePlayers
PUBLIC	?updateNextDivisonDate@DailyTimeConnectionManager@mu2@@AEAAXXZ ; mu2::DailyTimeConnectionManager::updateNextDivisonDate
PUBLIC	??_GDailyTimeConnectionManager@mu2@@UEAAPEAXI@Z	; mu2::DailyTimeConnectionManager::`scalar deleting destructor'
PUBLIC	?begin@?$_Tree@V?$_Tmap_traits@IV?$SmartPtrEx@VEntityPlayer@mu2@@@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIV?$SmartPtrEx@VEntityPlayer@mu2@@@mu2@@@std@@@4@$0A@@std@@@std@@QEBA?AV?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$SmartPtrEx@VEntityPlayer@mu2@@@mu2@@@std@@@std@@@std@@@2@XZ ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::SmartPtrEx<mu2::EntityPlayer>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::SmartPtrEx<mu2::EntityPlayer> > >,0> >::begin
PUBLIC	??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$SmartPtrEx@VEntityPlayer@mu2@@@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ ; std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::SmartPtrEx<mu2::EntityPlayer> > > >,std::_Iterator_base0>::operator++
PUBLIC	?end@?$_Tree@V?$_Tmap_traits@GUDailyTimeConnectionEventElem@mu2@@U?$less@G@std@@V?$allocator@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@4@$0A@@std@@@std@@QEBA?AV?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@std@@@std@@@2@XZ ; std::_Tree<std::_Tmap_traits<unsigned short,mu2::DailyTimeConnectionEventElem,std::less<unsigned short>,std::allocator<std::pair<unsigned short const ,mu2::DailyTimeConnectionEventElem> >,0> >::end
PUBLIC	??0?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAA@PEAU?$_Tree_node@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@PEAX@1@PEBV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@std@@@1@@Z ; std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned short const ,mu2::DailyTimeConnectionEventElem> > >,std::_Iterator_base0>::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned short const ,mu2::DailyTimeConnectionEventElem> > >,std::_Iterator_base0>
PUBLIC	??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ ; std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned short const ,mu2::DailyTimeConnectionEventElem> > >,std::_Iterator_base0>::operator++
PUBLIC	??$_Find@G@?$_Tree@V?$_Tmap_traits@GUDailyTimeConnectionEventElem@mu2@@U?$less@G@std@@V?$allocator@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@4@$0A@@std@@@std@@AEBAPEAU?$_Tree_node@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@PEAX@1@AEBG@Z ; std::_Tree<std::_Tmap_traits<unsigned short,mu2::DailyTimeConnectionEventElem,std::less<unsigned short>,std::allocator<std::pair<unsigned short const ,mu2::DailyTimeConnectionEventElem> >,0> >::_Find<unsigned short>
PUBLIC	??$_Find_lower_bound@G@?$_Tree@V?$_Tmap_traits@GUDailyTimeConnectionEventElem@mu2@@U?$less@G@std@@V?$allocator@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@4@$0A@@std@@@std@@IEBA?AU?$_Tree_find_result@PEAU?$_Tree_node@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@PEAX@std@@@1@AEBG@Z ; std::_Tree<std::_Tmap_traits<unsigned short,mu2::DailyTimeConnectionEventElem,std::less<unsigned short>,std::allocator<std::pair<unsigned short const ,mu2::DailyTimeConnectionEventElem> >,0> >::_Find_lower_bound<unsigned short>
PUBLIC	??$_Lower_bound_duplicate@G@?$_Tree@V?$_Tmap_traits@GUDailyTimeConnectionEventElem@mu2@@U?$less@G@std@@V?$allocator@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@4@$0A@@std@@@std@@IEBA_NQEAU?$_Tree_node@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@PEAX@1@AEBG@Z ; std::_Tree<std::_Tmap_traits<unsigned short,mu2::DailyTimeConnectionEventElem,std::less<unsigned short>,std::allocator<std::pair<unsigned short const ,mu2::DailyTimeConnectionEventElem> >,0> >::_Lower_bound_duplicate<unsigned short>
PUBLIC	?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A ; mu2::ISingleton<mu2::GlobalLoadScript>::inst
PUBLIC	??_7?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@6B@ ; mu2::ISingleton<mu2::DailyTimeConnectionManager>::`vftable'
PUBLIC	??_7DailyTimeConnectionManager@mu2@@6B@		; mu2::DailyTimeConnectionManager::`vftable'
PUBLIC	?inst@?$ISingleton@VManagerEntityPlayer@mu2@@@mu2@@1VManagerEntityPlayer@2@A ; mu2::ISingleton<mu2::ManagerEntityPlayer>::inst
PUBLIC	??_R4DailyTimeConnectionManager@mu2@@6B@	; mu2::DailyTimeConnectionManager::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVDailyTimeConnectionManager@mu2@@@8	; mu2::DailyTimeConnectionManager `RTTI Type Descriptor'
PUBLIC	??_R3DailyTimeConnectionManager@mu2@@8		; mu2::DailyTimeConnectionManager::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2DailyTimeConnectionManager@mu2@@8		; mu2::DailyTimeConnectionManager::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@DailyTimeConnectionManager@mu2@@8	; mu2::DailyTimeConnectionManager::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@8 ; mu2::ISingleton<mu2::DailyTimeConnectionManager>::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R0?AV?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@@8 ; mu2::ISingleton<mu2::DailyTimeConnectionManager> `RTTI Type Descriptor'
PUBLIC	??_R3?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@8 ; mu2::ISingleton<mu2::DailyTimeConnectionManager>::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@8 ; mu2::ISingleton<mu2::DailyTimeConnectionManager>::`RTTI Base Class Array'
PUBLIC	??_R4?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@6B@ ; mu2::ISingleton<mu2::DailyTimeConnectionManager>::`RTTI Complete Object Locator'
EXTRN	_purecall:PROC
EXTRN	??3@YAXPEAX_K@Z:PROC				; operator delete
EXTRN	atexit:PROC
EXTRN	__imp_CompareFileTime:PROC
EXTRN	__imp_GetLocalTime:PROC
EXTRN	__imp_GetTickCount64:PROC
EXTRN	__imp_FileTimeToSystemTime:PROC
EXTRN	__imp_SystemTimeToFileTime:PROC
EXTRN	?GetHsm@Entity@mu2@@QEAAAEAVHsm@2@XZ:PROC	; mu2::Entity::GetHsm
EXTRN	?GetAction@Entity@mu2@@QEBAPEAVAction@2@I@Z:PROC ; mu2::Entity::GetAction
EXTRN	??1GlobalLoadScript@mu2@@UEAA@XZ:PROC		; mu2::GlobalLoadScript::~GlobalLoadScript
EXTRN	?GetDailyDungeonRechargeTime@GlobalLoadScript@mu2@@QEBA?BGXZ:PROC ; mu2::GlobalLoadScript::GetDailyDungeonRechargeTime
EXTRN	?GetDailyTimeConnectionEventMap@GlobalLoadScript@mu2@@QEAAAEBV?$map@GUDailyTimeConnectionEventElem@mu2@@U?$less@G@std@@V?$allocator@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@4@@std@@XZ:PROC ; mu2::GlobalLoadScript::GetDailyTimeConnectionEventMap
EXTRN	?GetDailyTimeConnectionRewardElem@GlobalLoadScript@mu2@@QEAAPEBUDailyTimeConnectionRewardElem@2@G@Z:PROC ; mu2::GlobalLoadScript::GetDailyTimeConnectionRewardElem
EXTRN	??0GlobalLoadScript@mu2@@AEAA@XZ:PROC		; mu2::GlobalLoadScript::GlobalLoadScript
EXTRN	??_E?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@UEAAPEAXI@Z:PROC ; mu2::ISingleton<mu2::DailyTimeConnectionManager>::`vector deleting destructor'
EXTRN	??_EDailyTimeConnectionManager@mu2@@UEAAPEAXI@Z:PROC ; mu2::DailyTimeConnectionManager::`vector deleting destructor'
EXTRN	?NotifyOpenEvent@PlayerDailyTimeConnectionAction@mu2@@QEAAXXZ:PROC ; mu2::PlayerDailyTimeConnectionAction::NotifyOpenEvent
EXTRN	?NotifyCloseEvent@PlayerDailyTimeConnectionAction@mu2@@QEAAXXZ:PROC ; mu2::PlayerDailyTimeConnectionAction::NotifyCloseEvent
EXTRN	?NotifyChangeNextDay@PlayerDailyTimeConnectionAction@mu2@@QEAAXXZ:PROC ; mu2::PlayerDailyTimeConnectionAction::NotifyChangeNextDay
EXTRN	??1ManagerEntityPlayer@mu2@@UEAA@XZ:PROC	; mu2::ManagerEntityPlayer::~ManagerEntityPlayer
EXTRN	??0ManagerEntityPlayer@mu2@@AEAA@XZ:PROC	; mu2::ManagerEntityPlayer::ManagerEntityPlayer
EXTRN	__GSHandlerCheck:PROC
EXTRN	__GSHandlerCheck_EH4:PROC
EXTRN	__security_check_cookie:PROC
EXTRN	??_7type_info@@6B@:BYTE				; type_info::`vftable'
EXTRN	__security_cookie:QWORD
;	COMDAT ?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A
_BSS	SEGMENT
?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A DB 0d0H DUP (?) ; mu2::ISingleton<mu2::GlobalLoadScript>::inst
_BSS	ENDS
;	COMDAT ?inst@?$ISingleton@VManagerEntityPlayer@mu2@@@mu2@@1VManagerEntityPlayer@2@A
_BSS	SEGMENT
?inst@?$ISingleton@VManagerEntityPlayer@mu2@@@mu2@@1VManagerEntityPlayer@2@A DB 090H DUP (?) ; mu2::ISingleton<mu2::ManagerEntityPlayer>::inst
_BSS	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?GetDailyTimeStamp@DailyComparer@mu2@@QEAA?AU_SYSTEMTIME@@XZ DD imagerel $LN18
	DD	imagerel $LN18+468
	DD	imagerel $unwind$?GetDailyTimeStamp@DailyComparer@mu2@@QEAA?AU_SYSTEMTIME@@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?CompareDailyTimeStamp@DailyComparer@mu2@@QEAA_NAEBU_SYSTEMTIME@@@Z DD imagerel $LN25
	DD	imagerel $LN25+217
	DD	imagerel $unwind$?CompareDailyTimeStamp@DailyComparer@mu2@@QEAA_NAEBU_SYSTEMTIME@@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_G?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@UEAAPEAXI@Z DD imagerel $LN10
	DD	imagerel $LN10+65
	DD	imagerel $unwind$??_G?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?GetDivisionDate@DailyTimeConnectionManager@mu2@@QEAA?AU_SYSTEMTIME@@XZ DD imagerel $LN3
	DD	imagerel $LN3+41
	DD	imagerel $unwind$?GetDivisionDate@DailyTimeConnectionManager@mu2@@QEAA?AU_SYSTEMTIME@@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?GetEventDate@DailyTimeConnectionManager@mu2@@QEAA_NAEAU_SYSTEMTIME@@0@Z DD imagerel $LN4
	DD	imagerel $LN4+88
	DD	imagerel $unwind$?GetEventDate@DailyTimeConnectionManager@mu2@@QEAA_NAEAU_SYSTEMTIME@@0@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Update@DailyTimeConnectionManager@mu2@@QEAAXXZ DD imagerel $LN131
	DD	imagerel $LN131+666
	DD	imagerel $unwind$?Update@DailyTimeConnectionManager@mu2@@QEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0DailyTimeConnectionManager@mu2@@AEAA@XZ DD imagerel $LN10
	DD	imagerel $LN10+163
	DD	imagerel $unwind$??0DailyTimeConnectionManager@mu2@@AEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?isDurationOfEvent@DailyTimeConnectionManager@mu2@@AEAA_NU_SYSTEMTIME@@0@Z DD imagerel $LN4
	DD	imagerel $LN4+174
	DD	imagerel $unwind$?isDurationOfEvent@DailyTimeConnectionManager@mu2@@AEAA_NU_SYSTEMTIME@@0@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?checkUpdateTimer@DailyTimeConnectionManager@mu2@@AEAA_NXZ DD imagerel $LN7
	DD	imagerel $LN7+70
	DD	imagerel $unwind$?checkUpdateTimer@DailyTimeConnectionManager@mu2@@AEAA_NXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?checkEventOpen@DailyTimeConnectionManager@mu2@@AEAA_NXZ DD imagerel $LN8
	DD	imagerel $LN8+180
	DD	imagerel $unwind$?checkEventOpen@DailyTimeConnectionManager@mu2@@AEAA_NXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?updateEventOpen@DailyTimeConnectionManager@mu2@@AEAA_NXZ DD imagerel $LN137
	DD	imagerel $LN137+686
	DD	imagerel $unwind$?updateEventOpen@DailyTimeConnectionManager@mu2@@AEAA_NXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?updateEventClose@DailyTimeConnectionManager@mu2@@AEAA_NXZ DD imagerel $LN308
	DD	imagerel $LN308+1514
	DD	imagerel $unwind$?updateEventClose@DailyTimeConnectionManager@mu2@@AEAA_NXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0??updateEventClose@DailyTimeConnectionManager@mu2@@AEAA_NXZ@4HA DD imagerel ?dtor$0@?0??updateEventClose@DailyTimeConnectionManager@mu2@@AEAA_NXZ@4HA
	DD	imagerel ?dtor$0@?0??updateEventClose@DailyTimeConnectionManager@mu2@@AEAA_NXZ@4HA+24
	DD	imagerel $unwind$?dtor$0@?0??updateEventClose@DailyTimeConnectionManager@mu2@@AEAA_NXZ@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$1@?0??updateEventClose@DailyTimeConnectionManager@mu2@@AEAA_NXZ@4HA DD imagerel ?dtor$1@?0??updateEventClose@DailyTimeConnectionManager@mu2@@AEAA_NXZ@4HA
	DD	imagerel ?dtor$1@?0??updateEventClose@DailyTimeConnectionManager@mu2@@AEAA_NXZ@4HA+24
	DD	imagerel $unwind$?dtor$1@?0??updateEventClose@DailyTimeConnectionManager@mu2@@AEAA_NXZ@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?updateNextDivisonDate@DailyTimeConnectionManager@mu2@@AEAAXXZ DD imagerel $LN160
	DD	imagerel $LN160+835
	DD	imagerel $unwind$?updateNextDivisonDate@DailyTimeConnectionManager@mu2@@AEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0??updateNextDivisonDate@DailyTimeConnectionManager@mu2@@AEAAXXZ@4HA DD imagerel ?dtor$0@?0??updateNextDivisonDate@DailyTimeConnectionManager@mu2@@AEAAXXZ@4HA
	DD	imagerel ?dtor$0@?0??updateNextDivisonDate@DailyTimeConnectionManager@mu2@@AEAAXXZ@4HA+24
	DD	imagerel $unwind$?dtor$0@?0??updateNextDivisonDate@DailyTimeConnectionManager@mu2@@AEAAXXZ@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GDailyTimeConnectionManager@mu2@@UEAAPEAXI@Z DD imagerel $LN5
	DD	imagerel $LN5+60
	DD	imagerel $unwind$??_GDailyTimeConnectionManager@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?begin@?$_Tree@V?$_Tmap_traits@IV?$SmartPtrEx@VEntityPlayer@mu2@@@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIV?$SmartPtrEx@VEntityPlayer@mu2@@@mu2@@@std@@@4@$0A@@std@@@std@@QEBA?AV?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$SmartPtrEx@VEntityPlayer@mu2@@@mu2@@@std@@@std@@@std@@@2@XZ DD imagerel $LN29
	DD	imagerel $LN29+81
	DD	imagerel $unwind$?begin@?$_Tree@V?$_Tmap_traits@IV?$SmartPtrEx@VEntityPlayer@mu2@@@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIV?$SmartPtrEx@VEntityPlayer@mu2@@@mu2@@@std@@@4@$0A@@std@@@std@@QEBA?AV?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$SmartPtrEx@VEntityPlayer@mu2@@@mu2@@@std@@@std@@@std@@@2@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$SmartPtrEx@VEntityPlayer@mu2@@@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ DD imagerel $LN15
	DD	imagerel $LN15+184
	DD	imagerel $unwind$??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$SmartPtrEx@VEntityPlayer@mu2@@@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?end@?$_Tree@V?$_Tmap_traits@GUDailyTimeConnectionEventElem@mu2@@U?$less@G@std@@V?$allocator@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@4@$0A@@std@@@std@@QEBA?AV?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@std@@@std@@@2@XZ DD imagerel $LN29
	DD	imagerel $LN29+78
	DD	imagerel $unwind$?end@?$_Tree@V?$_Tmap_traits@GUDailyTimeConnectionEventElem@mu2@@U?$less@G@std@@V?$allocator@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@4@$0A@@std@@@std@@QEBA?AV?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@std@@@std@@@2@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ DD imagerel $LN15
	DD	imagerel $LN15+184
	DD	imagerel $unwind$??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Find@G@?$_Tree@V?$_Tmap_traits@GUDailyTimeConnectionEventElem@mu2@@U?$less@G@std@@V?$allocator@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@4@$0A@@std@@@std@@AEBAPEAU?$_Tree_node@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@PEAX@1@AEBG@Z DD imagerel $LN72
	DD	imagerel $LN72+102
	DD	imagerel $unwind$??$_Find@G@?$_Tree@V?$_Tmap_traits@GUDailyTimeConnectionEventElem@mu2@@U?$less@G@std@@V?$allocator@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@4@$0A@@std@@@std@@AEBAPEAU?$_Tree_node@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@PEAX@1@AEBG@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Find_lower_bound@G@?$_Tree@V?$_Tmap_traits@GUDailyTimeConnectionEventElem@mu2@@U?$less@G@std@@V?$allocator@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@4@$0A@@std@@@std@@IEBA?AU?$_Tree_find_result@PEAU?$_Tree_node@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@PEAX@std@@@1@AEBG@Z DD imagerel $LN36
	DD	imagerel $LN36+324
	DD	imagerel $unwind$??$_Find_lower_bound@G@?$_Tree@V?$_Tmap_traits@GUDailyTimeConnectionEventElem@mu2@@U?$less@G@std@@V?$allocator@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@4@$0A@@std@@@std@@IEBA?AU?$_Tree_find_result@PEAU?$_Tree_node@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@PEAX@std@@@1@AEBG@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Lower_bound_duplicate@G@?$_Tree@V?$_Tmap_traits@GUDailyTimeConnectionEventElem@mu2@@U?$less@G@std@@V?$allocator@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@4@$0A@@std@@@std@@IEBA_NQEAU?$_Tree_node@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@PEAX@1@AEBG@Z DD imagerel $LN24
	DD	imagerel $LN24+151
	DD	imagerel $unwind$??$_Lower_bound_duplicate@G@?$_Tree@V?$_Tmap_traits@GUDailyTimeConnectionEventElem@mu2@@U?$less@G@std@@V?$allocator@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@4@$0A@@std@@@std@@IEBA_NQEAU?$_Tree_node@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@PEAX@1@AEBG@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__E?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ DD imagerel ??__E?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ
	DD	imagerel ??__E?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ+34
	DD	imagerel $unwind$??__E?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__F?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ DD imagerel ??__F?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ
	DD	imagerel ??__F?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ+22
	DD	imagerel $unwind$??__F?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__E?inst@?$ISingleton@VManagerEntityPlayer@mu2@@@mu2@@1VManagerEntityPlayer@2@A@@YAXXZ DD imagerel ??__E?inst@?$ISingleton@VManagerEntityPlayer@mu2@@@mu2@@1VManagerEntityPlayer@2@A@@YAXXZ
	DD	imagerel ??__E?inst@?$ISingleton@VManagerEntityPlayer@mu2@@@mu2@@1VManagerEntityPlayer@2@A@@YAXXZ+34
	DD	imagerel $unwind$??__E?inst@?$ISingleton@VManagerEntityPlayer@mu2@@@mu2@@1VManagerEntityPlayer@2@A@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__F?inst@?$ISingleton@VManagerEntityPlayer@mu2@@@mu2@@1VManagerEntityPlayer@2@A@@YAXXZ DD imagerel ??__F?inst@?$ISingleton@VManagerEntityPlayer@mu2@@@mu2@@1VManagerEntityPlayer@2@A@@YAXXZ
	DD	imagerel ??__F?inst@?$ISingleton@VManagerEntityPlayer@mu2@@@mu2@@1VManagerEntityPlayer@2@A@@YAXXZ+22
	DD	imagerel $unwind$??__F?inst@?$ISingleton@VManagerEntityPlayer@mu2@@@mu2@@1VManagerEntityPlayer@2@A@@YAXXZ
pdata	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
??inst$initializer$@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA DQ FLAT:??__E?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ ; ??inst$initializer$@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA
CRT$XCU	ENDS
;	COMDAT ??_R4?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@6B@
rdata$r	SEGMENT
??_R4?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@6B@ DD 01H ; mu2::ISingleton<mu2::DailyTimeConnectionManager>::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AV?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@@8
	DD	imagerel ??_R3?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@8
	DD	imagerel ??_R4?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R2?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@8
rdata$r	SEGMENT
??_R2?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@8 DD imagerel ??_R1A@?0A@EA@?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@8 ; mu2::ISingleton<mu2::DailyTimeConnectionManager>::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@8
rdata$r	SEGMENT
??_R3?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@8 DD 00H ; mu2::ISingleton<mu2::DailyTimeConnectionManager>::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AV?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@@8
data$rs	SEGMENT
??_R0?AV?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::ISingleton<mu2::DailyTimeConnectionManager> `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AV?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@'
	DB	00H
data$rs	ENDS
;	COMDAT ??_R1A@?0A@EA@?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@8 DD imagerel ??_R0?AV?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@@8 ; mu2::ISingleton<mu2::DailyTimeConnectionManager>::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@DailyTimeConnectionManager@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@DailyTimeConnectionManager@mu2@@8 DD imagerel ??_R0?AVDailyTimeConnectionManager@mu2@@@8 ; mu2::DailyTimeConnectionManager::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3DailyTimeConnectionManager@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2DailyTimeConnectionManager@mu2@@8
rdata$r	SEGMENT
??_R2DailyTimeConnectionManager@mu2@@8 DD imagerel ??_R1A@?0A@EA@DailyTimeConnectionManager@mu2@@8 ; mu2::DailyTimeConnectionManager::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3DailyTimeConnectionManager@mu2@@8
rdata$r	SEGMENT
??_R3DailyTimeConnectionManager@mu2@@8 DD 00H		; mu2::DailyTimeConnectionManager::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2DailyTimeConnectionManager@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVDailyTimeConnectionManager@mu2@@@8
data$rs	SEGMENT
??_R0?AVDailyTimeConnectionManager@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::DailyTimeConnectionManager `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVDailyTimeConnectionManager@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4DailyTimeConnectionManager@mu2@@6B@
rdata$r	SEGMENT
??_R4DailyTimeConnectionManager@mu2@@6B@ DD 01H		; mu2::DailyTimeConnectionManager::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVDailyTimeConnectionManager@mu2@@@8
	DD	imagerel ??_R3DailyTimeConnectionManager@mu2@@8
	DD	imagerel ??_R4DailyTimeConnectionManager@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_7DailyTimeConnectionManager@mu2@@6B@
CONST	SEGMENT
??_7DailyTimeConnectionManager@mu2@@6B@ DQ FLAT:??_R4DailyTimeConnectionManager@mu2@@6B@ ; mu2::DailyTimeConnectionManager::`vftable'
	DQ	FLAT:?Init@DailyTimeConnectionManager@mu2@@UEAA_NPEAX@Z
	DQ	FLAT:?UnInit@DailyTimeConnectionManager@mu2@@UEAA_NXZ
	DQ	FLAT:??_EDailyTimeConnectionManager@mu2@@UEAAPEAXI@Z
CONST	ENDS
;	COMDAT ??_7?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@6B@
CONST	SEGMENT
??_7?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@6B@ DQ FLAT:??_R4?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@6B@ ; mu2::ISingleton<mu2::DailyTimeConnectionManager>::`vftable'
	DQ	FLAT:_purecall
	DQ	FLAT:_purecall
	DQ	FLAT:??_E?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@UEAAPEAXI@Z
CONST	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__F?inst@?$ISingleton@VManagerEntityPlayer@mu2@@@mu2@@1VManagerEntityPlayer@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__E?inst@?$ISingleton@VManagerEntityPlayer@mu2@@@mu2@@1VManagerEntityPlayer@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__F?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__E?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Lower_bound_duplicate@G@?$_Tree@V?$_Tmap_traits@GUDailyTimeConnectionEventElem@mu2@@U?$less@G@std@@V?$allocator@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@4@$0A@@std@@@std@@IEBA_NQEAU?$_Tree_node@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@PEAX@1@AEBG@Z DD 011301H
	DD	06213H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Find_lower_bound@G@?$_Tree@V?$_Tmap_traits@GUDailyTimeConnectionEventElem@mu2@@U?$less@G@std@@V?$allocator@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@4@$0A@@std@@@std@@IEBA?AU?$_Tree_find_result@PEAU?$_Tree_node@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@PEAX@std@@@1@AEBG@Z DD 031501H
	DD	07011c215H
	DD	06010H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Find@G@?$_Tree@V?$_Tmap_traits@GUDailyTimeConnectionEventElem@mu2@@U?$less@G@std@@V?$allocator@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@4@$0A@@std@@@std@@AEBAPEAU?$_Tree_node@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@PEAX@1@AEBG@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?end@?$_Tree@V?$_Tmap_traits@GUDailyTimeConnectionEventElem@mu2@@U?$less@G@std@@V?$allocator@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@4@$0A@@std@@@std@@QEBA?AV?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@std@@@std@@@2@XZ DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$SmartPtrEx@VEntityPlayer@mu2@@@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?begin@?$_Tree@V?$_Tmap_traits@IV?$SmartPtrEx@VEntityPlayer@mu2@@@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIV?$SmartPtrEx@VEntityPlayer@mu2@@@mu2@@@std@@@4@$0A@@std@@@std@@QEBA?AV?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$SmartPtrEx@VEntityPlayer@mu2@@@mu2@@@std@@@std@@@std@@@2@XZ DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GDailyTimeConnectionManager@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DW	018H
	DW	0329H
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0??updateNextDivisonDate@DailyTimeConnectionManager@mu2@@AEAAXXZ@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?updateNextDivisonDate@DailyTimeConnectionManager@mu2@@AEAAXXZ DB 0aH
	DB	00H
	DB	00H
	DB	090H
	DB	02H
	DB	'@'
	DB	00H
	DB	0aH
	DB	02H
	DB	0f1H, 0aH
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?updateNextDivisonDate@DailyTimeConnectionManager@mu2@@AEAAXXZ DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0??updateNextDivisonDate@DailyTimeConnectionManager@mu2@@AEAAXXZ@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?updateNextDivisonDate@DailyTimeConnectionManager@mu2@@AEAAXXZ DB 028H
	DD	imagerel $stateUnwindMap$?updateNextDivisonDate@DailyTimeConnectionManager@mu2@@AEAAXXZ
	DD	imagerel $ip2state$?updateNextDivisonDate@DailyTimeConnectionManager@mu2@@AEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?updateNextDivisonDate@DailyTimeConnectionManager@mu2@@AEAAXXZ DD 042019H
	DD	029010eH
	DD	060067007H
	DD	imagerel __GSHandlerCheck_EH4
	DD	imagerel $cppxdata$?updateNextDivisonDate@DailyTimeConnectionManager@mu2@@AEAAXXZ
	DD	013aH
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DW	018H
	DW	05d0H
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$1@?0??updateEventClose@DailyTimeConnectionManager@mu2@@AEAA_NXZ@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0??updateEventClose@DailyTimeConnectionManager@mu2@@AEAA_NXZ@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?updateEventClose@DailyTimeConnectionManager@mu2@@AEAA_NXZ DB 0aH
	DB	00H
	DB	00H
	DB	0f5H, 07H
	DB	02H
	DB	'Z'
	DB	00H
	DB	0adH, 0dH
	DB	04H
	DB	'Z'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?updateEventClose@DailyTimeConnectionManager@mu2@@AEAA_NXZ DB 04H
	DB	0eH
	DD	imagerel ?dtor$0@?0??updateEventClose@DailyTimeConnectionManager@mu2@@AEAA_NXZ@4HA
	DB	036H
	DD	imagerel ?dtor$1@?0??updateEventClose@DailyTimeConnectionManager@mu2@@AEAA_NXZ@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?updateEventClose@DailyTimeConnectionManager@mu2@@AEAA_NXZ DB 028H
	DD	imagerel $stateUnwindMap$?updateEventClose@DailyTimeConnectionManager@mu2@@AEAA_NXZ
	DD	imagerel $ip2state$?updateEventClose@DailyTimeConnectionManager@mu2@@AEAA_NXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?updateEventClose@DailyTimeConnectionManager@mu2@@AEAA_NXZ DD 042019H
	DD	045010eH
	DD	060067007H
	DD	imagerel __GSHandlerCheck_EH4
	DD	imagerel $cppxdata$?updateEventClose@DailyTimeConnectionManager@mu2@@AEAA_NXZ
	DD	0212H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?updateEventOpen@DailyTimeConnectionManager@mu2@@AEAA_NXZ DD 020c01H
	DD	01f010cH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?checkEventOpen@DailyTimeConnectionManager@mu2@@AEAA_NXZ DD 030b01H
	DD	07007820bH
	DD	06006H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?checkUpdateTimer@DailyTimeConnectionManager@mu2@@AEAA_NXZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DB	01dH
	DB	09cH
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?isDurationOfEvent@DailyTimeConnectionManager@mu2@@AEAA_NU_SYSTEMTIME@@0@Z DD 012219H
	DD	0c213H
	DD	imagerel __GSHandlerCheck
	DD	050H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0DailyTimeConnectionManager@mu2@@AEAA@XZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Update@DailyTimeConnectionManager@mu2@@QEAAXXZ DD 020c01H
	DD	01d010cH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?GetEventDate@DailyTimeConnectionManager@mu2@@QEAA_NAEAU_SYSTEMTIME@@0@Z DD 021101H
	DD	060107011H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?GetDivisionDate@DailyTimeConnectionManager@mu2@@QEAA?AU_SYSTEMTIME@@XZ DD 020c01H
	DD	0600b700cH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_G?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DB	01dH
	DB	0bfH
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?CompareDailyTimeStamp@DailyComparer@mu2@@QEAA_NAEBU_SYSTEMTIME@@@Z DD 042519H
	DD	0130113H
	DD	0600b700cH
	DD	imagerel __GSHandlerCheck
	DD	080H
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DW	01dH
	DW	01baH
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?GetDailyTimeStamp@DailyComparer@mu2@@QEAA?AU_SYSTEMTIME@@XZ DD 042519H
	DD	01b0113H
	DD	0600b700cH
	DD	imagerel __GSHandlerCheck
	DD	0c0H
xdata	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
??inst$initializer$@?$ISingleton@VManagerEntityPlayer@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA DQ FLAT:??__E?inst@?$ISingleton@VManagerEntityPlayer@mu2@@@mu2@@1VManagerEntityPlayer@2@A@@YAXXZ ; ??inst$initializer$@?$ISingleton@VManagerEntityPlayer@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA
CRT$XCU	ENDS
; Function compile flags: /Odtp
;	COMDAT ??__F?inst@?$ISingleton@VManagerEntityPlayer@mu2@@@mu2@@1VManagerEntityPlayer@2@A@@YAXXZ
text$yd	SEGMENT
??__F?inst@?$ISingleton@VManagerEntityPlayer@mu2@@@mu2@@1VManagerEntityPlayer@2@A@@YAXXZ PROC ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::ManagerEntityPlayer>::inst'', COMDAT
  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?inst@?$ISingleton@VManagerEntityPlayer@mu2@@@mu2@@1VManagerEntityPlayer@2@A ; mu2::ISingleton<mu2::ManagerEntityPlayer>::inst
  0000b	e8 00 00 00 00	 call	 ??1ManagerEntityPlayer@mu2@@UEAA@XZ ; mu2::ManagerEntityPlayer::~ManagerEntityPlayer
  00010	90		 npad	 1
  00011	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00015	c3		 ret	 0
??__F?inst@?$ISingleton@VManagerEntityPlayer@mu2@@@mu2@@1VManagerEntityPlayer@2@A@@YAXXZ ENDP ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::ManagerEntityPlayer>::inst''
text$yd	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??__E?inst@?$ISingleton@VManagerEntityPlayer@mu2@@@mu2@@1VManagerEntityPlayer@2@A@@YAXXZ
text$di	SEGMENT
??__E?inst@?$ISingleton@VManagerEntityPlayer@mu2@@@mu2@@1VManagerEntityPlayer@2@A@@YAXXZ PROC ; `dynamic initializer for 'mu2::ISingleton<mu2::ManagerEntityPlayer>::inst'', COMDAT

; 90   : template<typename T> T ISingleton<T>::inst;

  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?inst@?$ISingleton@VManagerEntityPlayer@mu2@@@mu2@@1VManagerEntityPlayer@2@A ; mu2::ISingleton<mu2::ManagerEntityPlayer>::inst
  0000b	e8 00 00 00 00	 call	 ??0ManagerEntityPlayer@mu2@@AEAA@XZ ; mu2::ManagerEntityPlayer::ManagerEntityPlayer
  00010	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??__F?inst@?$ISingleton@VManagerEntityPlayer@mu2@@@mu2@@1VManagerEntityPlayer@2@A@@YAXXZ ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::ManagerEntityPlayer>::inst''
  00017	e8 00 00 00 00	 call	 atexit
  0001c	90		 npad	 1
  0001d	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00021	c3		 ret	 0
??__E?inst@?$ISingleton@VManagerEntityPlayer@mu2@@@mu2@@1VManagerEntityPlayer@2@A@@YAXXZ ENDP ; `dynamic initializer for 'mu2::ISingleton<mu2::ManagerEntityPlayer>::inst''
text$di	ENDS
; Function compile flags: /Odtp
;	COMDAT ??__F?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ
text$yd	SEGMENT
??__F?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ PROC ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::GlobalLoadScript>::inst'', COMDAT
  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A ; mu2::ISingleton<mu2::GlobalLoadScript>::inst
  0000b	e8 00 00 00 00	 call	 ??1GlobalLoadScript@mu2@@UEAA@XZ ; mu2::GlobalLoadScript::~GlobalLoadScript
  00010	90		 npad	 1
  00011	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00015	c3		 ret	 0
??__F?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ ENDP ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::GlobalLoadScript>::inst''
text$yd	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??__E?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ
text$di	SEGMENT
??__E?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ PROC ; `dynamic initializer for 'mu2::ISingleton<mu2::GlobalLoadScript>::inst'', COMDAT

; 90   : template<typename T> T ISingleton<T>::inst;

  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A ; mu2::ISingleton<mu2::GlobalLoadScript>::inst
  0000b	e8 00 00 00 00	 call	 ??0GlobalLoadScript@mu2@@AEAA@XZ ; mu2::GlobalLoadScript::GlobalLoadScript
  00010	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??__F?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::GlobalLoadScript>::inst''
  00017	e8 00 00 00 00	 call	 atexit
  0001c	90		 npad	 1
  0001d	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00021	c3		 ret	 0
??__E?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ ENDP ; `dynamic initializer for 'mu2::ISingleton<mu2::GlobalLoadScript>::inst''
text$di	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??$_Lower_bound_duplicate@G@?$_Tree@V?$_Tmap_traits@GUDailyTimeConnectionEventElem@mu2@@U?$less@G@std@@V?$allocator@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@4@$0A@@std@@@std@@IEBA_NQEAU?$_Tree_node@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@PEAX@1@AEBG@Z
_TEXT	SEGMENT
$T1 = 0
tv88 = 4
tv78 = 8
$T2 = 16
$T3 = 24
$T4 = 32
this$ = 64
_Bound$ = 72
_Keyval$ = 80
??$_Lower_bound_duplicate@G@?$_Tree@V?$_Tmap_traits@GUDailyTimeConnectionEventElem@mu2@@U?$less@G@std@@V?$allocator@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@4@$0A@@std@@@std@@IEBA_NQEAU?$_Tree_node@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@PEAX@1@AEBG@Z PROC ; std::_Tree<std::_Tmap_traits<unsigned short,mu2::DailyTimeConnectionEventElem,std::less<unsigned short>,std::allocator<std::pair<unsigned short const ,mu2::DailyTimeConnectionEventElem> >,0> >::_Lower_bound_duplicate<unsigned short>, COMDAT

; 1623 :     bool _Lower_bound_duplicate(const _Nodeptr _Bound, const _Keyty& _Keyval) const {

$LN24:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 1624 :         return !_Bound->_Isnil && !_DEBUG_LT_PRED(_Getcomp(), _Keyval, _Traits::_Kfn(_Bound->_Myval));

  00013	48 8b 44 24 48	 mov	 rax, QWORD PTR _Bound$[rsp]
  00018	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  0001c	85 c0		 test	 eax, eax
  0001e	75 65		 jne	 SHORT $LN3@Lower_boun
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map

; 67   :         return _Val.first;

  00020	48 8b 44 24 48	 mov	 rax, QWORD PTR _Bound$[rsp]
  00025	48 83 c0 1a	 add	 rax, 26
  00029	48 89 44 24 18	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1971 :         return _Mypair._Get_first();

  0002e	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1543 :         return *this;

  00033	48 89 44 24 10	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1971 :         return _Mypair._Get_first();

  00038	48 8b 44 24 10	 mov	 rax, QWORD PTR $T2[rsp]
  0003d	48 89 44 24 20	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 2380 :         return _Left < _Right;

  00042	48 8b 44 24 50	 mov	 rax, QWORD PTR _Keyval$[rsp]
  00047	0f b7 00	 movzx	 eax, WORD PTR [rax]
  0004a	48 8b 4c 24 18	 mov	 rcx, QWORD PTR $T3[rsp]
  0004f	0f b7 09	 movzx	 ecx, WORD PTR [rcx]
  00052	3b c1		 cmp	 eax, ecx
  00054	7d 0a		 jge	 SHORT $LN19@Lower_boun
  00056	c7 44 24 04 01
	00 00 00	 mov	 DWORD PTR tv88[rsp], 1
  0005e	eb 08		 jmp	 SHORT $LN20@Lower_boun
$LN19@Lower_boun:
  00060	c7 44 24 04 00
	00 00 00	 mov	 DWORD PTR tv88[rsp], 0
$LN20@Lower_boun:
  00068	0f b6 44 24 04	 movzx	 eax, BYTE PTR tv88[rsp]
  0006d	88 04 24	 mov	 BYTE PTR $T1[rsp], al
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1624 :         return !_Bound->_Isnil && !_DEBUG_LT_PRED(_Getcomp(), _Keyval, _Traits::_Kfn(_Bound->_Myval));

  00070	0f b6 04 24	 movzx	 eax, BYTE PTR $T1[rsp]
  00074	0f b6 c0	 movzx	 eax, al
  00077	85 c0		 test	 eax, eax
  00079	75 0a		 jne	 SHORT $LN3@Lower_boun
  0007b	c7 44 24 08 01
	00 00 00	 mov	 DWORD PTR tv78[rsp], 1
  00083	eb 08		 jmp	 SHORT $LN4@Lower_boun
$LN3@Lower_boun:
  00085	c7 44 24 08 00
	00 00 00	 mov	 DWORD PTR tv78[rsp], 0
$LN4@Lower_boun:
  0008d	0f b6 44 24 08	 movzx	 eax, BYTE PTR tv78[rsp]

; 1625 :     }

  00092	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00096	c3		 ret	 0
??$_Lower_bound_duplicate@G@?$_Tree@V?$_Tmap_traits@GUDailyTimeConnectionEventElem@mu2@@U?$less@G@std@@V?$allocator@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@4@$0A@@std@@@std@@IEBA_NQEAU?$_Tree_node@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@PEAX@1@AEBG@Z ENDP ; std::_Tree<std::_Tmap_traits<unsigned short,mu2::DailyTimeConnectionEventElem,std::less<unsigned short>,std::allocator<std::pair<unsigned short const ,mu2::DailyTimeConnectionEventElem> >,0> >::_Lower_bound_duplicate<unsigned short>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??$_Find_lower_bound@G@?$_Tree@V?$_Tmap_traits@GUDailyTimeConnectionEventElem@mu2@@U?$less@G@std@@V?$allocator@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@4@$0A@@std@@@std@@IEBA?AU?$_Tree_find_result@PEAU?$_Tree_node@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@PEAX@std@@@1@AEBG@Z
_TEXT	SEGMENT
$T1 = 0
_Trynode$ = 8
tv128 = 16
_Scary$ = 24
_Result$ = 32
$T2 = 56
$T3 = 64
$T4 = 72
$T5 = 80
$T6 = 88
this$ = 128
__$ReturnUdt$ = 136
_Keyval$ = 144
??$_Find_lower_bound@G@?$_Tree@V?$_Tmap_traits@GUDailyTimeConnectionEventElem@mu2@@U?$less@G@std@@V?$allocator@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@4@$0A@@std@@@std@@IEBA?AU?$_Tree_find_result@PEAU?$_Tree_node@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@PEAX@std@@@1@AEBG@Z PROC ; std::_Tree<std::_Tmap_traits<unsigned short,mu2::DailyTimeConnectionEventElem,std::less<unsigned short>,std::allocator<std::pair<unsigned short const ,mu2::DailyTimeConnectionEventElem> >,0> >::_Find_lower_bound<unsigned short>, COMDAT

; 1628 :     _Tree_find_result<_Nodeptr> _Find_lower_bound(const _Keyty& _Keyval) const {

$LN36:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	56		 push	 rsi
  00010	57		 push	 rdi
  00011	48 83 ec 68	 sub	 rsp, 104		; 00000068H

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00015	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0001d	48 89 44 24 38	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00022	48 8b 44 24 38	 mov	 rax, QWORD PTR $T2[rsp]
  00027	48 89 44 24 40	 mov	 QWORD PTR $T3[rsp], rax

; 1629 :         const auto _Scary = _Get_scary();

  0002c	48 8b 44 24 40	 mov	 rax, QWORD PTR $T3[rsp]
  00031	48 89 44 24 18	 mov	 QWORD PTR _Scary$[rsp], rax

; 1630 :         _Tree_find_result<_Nodeptr> _Result{{_Scary->_Myhead->_Parent, _Tree_child::_Right}, _Scary->_Myhead};

  00036	48 8b 44 24 18	 mov	 rax, QWORD PTR _Scary$[rsp]
  0003b	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0003e	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00042	48 89 44 24 20	 mov	 QWORD PTR _Result$[rsp], rax
  00047	c7 44 24 28 00
	00 00 00	 mov	 DWORD PTR _Result$[rsp+8], 0
  0004f	48 8b 44 24 18	 mov	 rax, QWORD PTR _Scary$[rsp]
  00054	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00057	48 89 44 24 30	 mov	 QWORD PTR _Result$[rsp+16], rax

; 1631 :         _Nodeptr _Trynode = _Result._Location._Parent;

  0005c	48 8b 44 24 20	 mov	 rax, QWORD PTR _Result$[rsp]
  00061	48 89 44 24 08	 mov	 QWORD PTR _Trynode$[rsp], rax
$LN2@Find_lower:

; 1632 :         while (!_Trynode->_Isnil) {

  00066	48 8b 44 24 08	 mov	 rax, QWORD PTR _Trynode$[rsp]
  0006b	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  0006f	85 c0		 test	 eax, eax
  00071	0f 85 a7 00 00
	00		 jne	 $LN3@Find_lower

; 1633 :             _Result._Location._Parent = _Trynode;

  00077	48 8b 44 24 08	 mov	 rax, QWORD PTR _Trynode$[rsp]
  0007c	48 89 44 24 20	 mov	 QWORD PTR _Result$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map

; 67   :         return _Val.first;

  00081	48 8b 44 24 08	 mov	 rax, QWORD PTR _Trynode$[rsp]
  00086	48 83 c0 1a	 add	 rax, 26
  0008a	48 89 44 24 50	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1971 :         return _Mypair._Get_first();

  0008f	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1543 :         return *this;

  00097	48 89 44 24 48	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1971 :         return _Mypair._Get_first();

  0009c	48 8b 44 24 48	 mov	 rax, QWORD PTR $T4[rsp]
  000a1	48 89 44 24 58	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 2380 :         return _Left < _Right;

  000a6	48 8b 44 24 50	 mov	 rax, QWORD PTR $T5[rsp]
  000ab	0f b7 00	 movzx	 eax, WORD PTR [rax]
  000ae	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR _Keyval$[rsp]
  000b6	0f b7 09	 movzx	 ecx, WORD PTR [rcx]
  000b9	3b c1		 cmp	 eax, ecx
  000bb	7d 0a		 jge	 SHORT $LN31@Find_lower
  000bd	c7 44 24 10 01
	00 00 00	 mov	 DWORD PTR tv128[rsp], 1
  000c5	eb 08		 jmp	 SHORT $LN32@Find_lower
$LN31@Find_lower:
  000c7	c7 44 24 10 00
	00 00 00	 mov	 DWORD PTR tv128[rsp], 0
$LN32@Find_lower:
  000cf	0f b6 44 24 10	 movzx	 eax, BYTE PTR tv128[rsp]
  000d4	88 04 24	 mov	 BYTE PTR $T1[rsp], al
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1634 :             if (_DEBUG_LT_PRED(_Getcomp(), _Traits::_Kfn(_Trynode->_Myval), _Keyval)) {

  000d7	0f b6 04 24	 movzx	 eax, BYTE PTR $T1[rsp]
  000db	0f b6 c0	 movzx	 eax, al
  000de	85 c0		 test	 eax, eax
  000e0	74 18		 je	 SHORT $LN4@Find_lower

; 1635 :                 _Result._Location._Child = _Tree_child::_Right;

  000e2	c7 44 24 28 00
	00 00 00	 mov	 DWORD PTR _Result$[rsp+8], 0

; 1636 :                 _Trynode                 = _Trynode->_Right;

  000ea	48 8b 44 24 08	 mov	 rax, QWORD PTR _Trynode$[rsp]
  000ef	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  000f3	48 89 44 24 08	 mov	 QWORD PTR _Trynode$[rsp], rax

; 1637 :             } else {

  000f8	eb 1f		 jmp	 SHORT $LN5@Find_lower
$LN4@Find_lower:

; 1638 :                 _Result._Location._Child = _Tree_child::_Left;

  000fa	c7 44 24 28 01
	00 00 00	 mov	 DWORD PTR _Result$[rsp+8], 1

; 1639 :                 _Result._Bound           = _Trynode;

  00102	48 8b 44 24 08	 mov	 rax, QWORD PTR _Trynode$[rsp]
  00107	48 89 44 24 30	 mov	 QWORD PTR _Result$[rsp+16], rax

; 1640 :                 _Trynode                 = _Trynode->_Left;

  0010c	48 8b 44 24 08	 mov	 rax, QWORD PTR _Trynode$[rsp]
  00111	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00114	48 89 44 24 08	 mov	 QWORD PTR _Trynode$[rsp], rax
$LN5@Find_lower:

; 1641 :             }
; 1642 :         }

  00119	e9 48 ff ff ff	 jmp	 $LN2@Find_lower
$LN3@Find_lower:

; 1643 : 
; 1644 :         return _Result;

  0011e	48 8d 44 24 20	 lea	 rax, QWORD PTR _Result$[rsp]
  00123	48 8b bc 24 88
	00 00 00	 mov	 rdi, QWORD PTR __$ReturnUdt$[rsp]
  0012b	48 8b f0	 mov	 rsi, rax
  0012e	b9 18 00 00 00	 mov	 ecx, 24
  00133	f3 a4		 rep movsb
  00135	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]

; 1645 :     }

  0013d	48 83 c4 68	 add	 rsp, 104		; 00000068H
  00141	5f		 pop	 rdi
  00142	5e		 pop	 rsi
  00143	c3		 ret	 0
??$_Find_lower_bound@G@?$_Tree@V?$_Tmap_traits@GUDailyTimeConnectionEventElem@mu2@@U?$less@G@std@@V?$allocator@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@4@$0A@@std@@@std@@IEBA?AU?$_Tree_find_result@PEAU?$_Tree_node@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@PEAX@std@@@1@AEBG@Z ENDP ; std::_Tree<std::_Tmap_traits<unsigned short,mu2::DailyTimeConnectionEventElem,std::less<unsigned short>,std::allocator<std::pair<unsigned short const ,mu2::DailyTimeConnectionEventElem> >,0> >::_Find_lower_bound<unsigned short>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??$_Find@G@?$_Tree@V?$_Tmap_traits@GUDailyTimeConnectionEventElem@mu2@@U?$less@G@std@@V?$allocator@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@4@$0A@@std@@@std@@AEBAPEAU?$_Tree_node@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@PEAX@1@AEBG@Z
_TEXT	SEGMENT
$T1 = 32
$T2 = 40
_Loc$ = 48
this$ = 96
_Keyval$ = 104
??$_Find@G@?$_Tree@V?$_Tmap_traits@GUDailyTimeConnectionEventElem@mu2@@U?$less@G@std@@V?$allocator@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@4@$0A@@std@@@std@@AEBAPEAU?$_Tree_node@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@PEAX@1@AEBG@Z PROC ; std::_Tree<std::_Tmap_traits<unsigned short,mu2::DailyTimeConnectionEventElem,std::less<unsigned short>,std::allocator<std::pair<unsigned short const ,mu2::DailyTimeConnectionEventElem> >,0> >::_Find<unsigned short>, COMDAT

; 1383 :     _NODISCARD _Nodeptr _Find(const _Other& _Keyval) const {

$LN72:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 1384 :         const _Tree_find_result<_Nodeptr> _Loc = _Find_lower_bound(_Keyval);

  0000e	4c 8b 44 24 68	 mov	 r8, QWORD PTR _Keyval$[rsp]
  00013	48 8d 54 24 30	 lea	 rdx, QWORD PTR _Loc$[rsp]
  00018	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  0001d	e8 00 00 00 00	 call	 ??$_Find_lower_bound@G@?$_Tree@V?$_Tmap_traits@GUDailyTimeConnectionEventElem@mu2@@U?$less@G@std@@V?$allocator@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@4@$0A@@std@@@std@@IEBA?AU?$_Tree_find_result@PEAU?$_Tree_node@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@PEAX@std@@@1@AEBG@Z ; std::_Tree<std::_Tmap_traits<unsigned short,mu2::DailyTimeConnectionEventElem,std::less<unsigned short>,std::allocator<std::pair<unsigned short const ,mu2::DailyTimeConnectionEventElem> >,0> >::_Find_lower_bound<unsigned short>
  00022	90		 npad	 1

; 1385 :         if (_Lower_bound_duplicate(_Loc._Bound, _Keyval)) {

  00023	4c 8b 44 24 68	 mov	 r8, QWORD PTR _Keyval$[rsp]
  00028	48 8b 54 24 40	 mov	 rdx, QWORD PTR _Loc$[rsp+16]
  0002d	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  00032	e8 00 00 00 00	 call	 ??$_Lower_bound_duplicate@G@?$_Tree@V?$_Tmap_traits@GUDailyTimeConnectionEventElem@mu2@@U?$less@G@std@@V?$allocator@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@4@$0A@@std@@@std@@IEBA_NQEAU?$_Tree_node@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@PEAX@1@AEBG@Z ; std::_Tree<std::_Tmap_traits<unsigned short,mu2::DailyTimeConnectionEventElem,std::less<unsigned short>,std::allocator<std::pair<unsigned short const ,mu2::DailyTimeConnectionEventElem> >,0> >::_Lower_bound_duplicate<unsigned short>
  00037	0f b6 c0	 movzx	 eax, al
  0003a	85 c0		 test	 eax, eax
  0003c	74 07		 je	 SHORT $LN2@Find

; 1386 :             return _Loc._Bound;

  0003e	48 8b 44 24 40	 mov	 rax, QWORD PTR _Loc$[rsp+16]
  00043	eb 1c		 jmp	 SHORT $LN1@Find
$LN2@Find:

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00045	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0004a	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  0004f	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00054	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax

; 1387 :         }
; 1388 : 
; 1389 :         return _Get_scary()->_Myhead;

  00059	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
  0005e	48 8b 00	 mov	 rax, QWORD PTR [rax]
$LN1@Find:

; 1390 :     }

  00061	48 83 c4 58	 add	 rsp, 88			; 00000058H
  00065	c3		 ret	 0
??$_Find@G@?$_Tree@V?$_Tmap_traits@GUDailyTimeConnectionEventElem@mu2@@U?$less@G@std@@V?$allocator@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@4@$0A@@std@@@std@@AEBAPEAU?$_Tree_node@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@PEAX@1@AEBG@Z ENDP ; std::_Tree<std::_Tmap_traits<unsigned short,mu2::DailyTimeConnectionEventElem,std::less<unsigned short>,std::allocator<std::pair<unsigned short const ,mu2::DailyTimeConnectionEventElem> >,0> >::_Find<unsigned short>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ
_TEXT	SEGMENT
_Pnode$1 = 0
_Pnode$ = 8
$T2 = 16
this$ = 48
??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ PROC ; std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned short const ,mu2::DailyTimeConnectionEventElem> > >,std::_Iterator_base0>::operator++, COMDAT

; 49   :     _Tree_unchecked_const_iterator& operator++() noexcept {

$LN15:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 50   :         if (_Ptr->_Right->_Isnil) { // climb looking for right subtree

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00011	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00015	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  00019	85 c0		 test	 eax, eax
  0001b	74 4a		 je	 SHORT $LN4@operator
$LN2@operator:

; 51   :             _Nodeptr _Pnode;
; 52   :             while (!(_Pnode = _Ptr->_Parent)->_Isnil && _Ptr == _Pnode->_Right) {

  0001d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00022	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00025	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00029	48 89 04 24	 mov	 QWORD PTR _Pnode$1[rsp], rax
  0002d	48 8b 04 24	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  00031	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  00035	85 c0		 test	 eax, eax
  00037	75 20		 jne	 SHORT $LN3@operator
  00039	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003e	48 8b 0c 24	 mov	 rcx, QWORD PTR _Pnode$1[rsp]
  00042	48 8b 49 10	 mov	 rcx, QWORD PTR [rcx+16]
  00046	48 39 08	 cmp	 QWORD PTR [rax], rcx
  00049	75 0e		 jne	 SHORT $LN3@operator

; 53   :                 _Ptr = _Pnode; // ==> parent while right subtree

  0004b	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00050	48 8b 0c 24	 mov	 rcx, QWORD PTR _Pnode$1[rsp]
  00054	48 89 08	 mov	 QWORD PTR [rax], rcx

; 54   :             }

  00057	eb c4		 jmp	 SHORT $LN2@operator
$LN3@operator:

; 55   : 
; 56   :             _Ptr = _Pnode; // ==> parent (head if end())

  00059	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0005e	48 8b 0c 24	 mov	 rcx, QWORD PTR _Pnode$1[rsp]
  00062	48 89 08	 mov	 QWORD PTR [rax], rcx

; 57   :         } else {

  00065	eb 47		 jmp	 SHORT $LN5@operator
$LN4@operator:

; 58   :             _Ptr = _Mytree::_Min(_Ptr->_Right); // ==> smallest of right subtree

  00067	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0006c	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0006f	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00073	48 89 44 24 08	 mov	 QWORD PTR _Pnode$[rsp], rax
$LN9@operator:

; 476  :         while (!_Pnode->_Left->_Isnil) {

  00078	48 8b 44 24 08	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0007d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00080	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  00084	85 c0		 test	 eax, eax
  00086	75 0f		 jne	 SHORT $LN10@operator

; 477  :             _Pnode = _Pnode->_Left;

  00088	48 8b 44 24 08	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0008d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00090	48 89 44 24 08	 mov	 QWORD PTR _Pnode$[rsp], rax

; 478  :         }

  00095	eb e1		 jmp	 SHORT $LN9@operator
$LN10@operator:

; 479  : 
; 480  :         return _Pnode;

  00097	48 8b 44 24 08	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0009c	48 89 44 24 10	 mov	 QWORD PTR $T2[rsp], rax

; 58   :             _Ptr = _Mytree::_Min(_Ptr->_Right); // ==> smallest of right subtree

  000a1	48 8b 44 24 10	 mov	 rax, QWORD PTR $T2[rsp]
  000a6	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  000ab	48 89 01	 mov	 QWORD PTR [rcx], rax
$LN5@operator:

; 59   :         }
; 60   : 
; 61   :         return *this;

  000ae	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]

; 62   :     }

  000b3	48 83 c4 28	 add	 rsp, 40			; 00000028H
  000b7	c3		 ret	 0
??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ ENDP ; std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned short const ,mu2::DailyTimeConnectionEventElem> > >,std::_Iterator_base0>::operator++
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??0?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAA@PEAU?$_Tree_node@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@PEAX@1@PEBV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@std@@@1@@Z
_TEXT	SEGMENT
this$ = 8
_Pnode$ = 16
_Plist$ = 24
??0?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAA@PEAU?$_Tree_node@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@PEAX@1@PEBV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@std@@@1@@Z PROC ; std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned short const ,mu2::DailyTimeConnectionEventElem> > >,std::_Iterator_base0>::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned short const ,mu2::DailyTimeConnectionEventElem> > >,std::_Iterator_base0>, COMDAT

; 37   :     _Tree_unchecked_const_iterator(_Nodeptr _Pnode, const _Mytree* _Plist) noexcept : _Ptr(_Pnode) {

  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 8b 4c 24 10	 mov	 rcx, QWORD PTR _Pnode$[rsp]
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 38   :         this->_Adopt(_Plist);
; 39   :     }

  0001c	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00021	c3		 ret	 0
??0?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAA@PEAU?$_Tree_node@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@PEAX@1@PEBV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@std@@@1@@Z ENDP ; std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned short const ,mu2::DailyTimeConnectionEventElem> > >,std::_Iterator_base0>::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned short const ,mu2::DailyTimeConnectionEventElem> > >,std::_Iterator_base0>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ?end@?$_Tree@V?$_Tmap_traits@GUDailyTimeConnectionEventElem@mu2@@U?$less@G@std@@V?$allocator@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@4@$0A@@std@@@std@@QEBA?AV?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@std@@@std@@@2@XZ
_TEXT	SEGMENT
$T1 = 0
$T2 = 8
_Scary$ = 16
__param0$ = 24
this$ = 48
__$ReturnUdt$ = 56
?end@?$_Tree@V?$_Tmap_traits@GUDailyTimeConnectionEventElem@mu2@@U?$less@G@std@@V?$allocator@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@4@$0A@@std@@@std@@QEBA?AV?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@std@@@std@@@2@XZ PROC ; std::_Tree<std::_Tmap_traits<unsigned short,mu2::DailyTimeConnectionEventElem,std::less<unsigned short>,std::allocator<std::pair<unsigned short const ,mu2::DailyTimeConnectionEventElem> >,0> >::end, COMDAT

; 1155 :     _NODISCARD const_iterator end() const noexcept {

$LN29:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  0000e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00013	48 89 04 24	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00017	48 8b 04 24	 mov	 rax, QWORD PTR $T1[rsp]
  0001b	48 89 44 24 08	 mov	 QWORD PTR $T2[rsp], rax

; 1156 :         const auto _Scary = _Get_scary();

  00020	48 8b 44 24 08	 mov	 rax, QWORD PTR $T2[rsp]
  00025	48 89 44 24 10	 mov	 QWORD PTR _Scary$[rsp], rax

; 1157 :         return const_iterator(_Scary->_Myhead, _Scary);

  0002a	48 8b 44 24 10	 mov	 rax, QWORD PTR _Scary$[rsp]
  0002f	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00032	48 89 44 24 18	 mov	 QWORD PTR __param0$[rsp], rax

; 37   :     _Tree_unchecked_const_iterator(_Nodeptr _Pnode, const _Mytree* _Plist) noexcept : _Ptr(_Pnode) {

  00037	48 8b 44 24 38	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]
  0003c	48 8b 4c 24 18	 mov	 rcx, QWORD PTR __param0$[rsp]
  00041	48 89 08	 mov	 QWORD PTR [rax], rcx

; 1157 :         return const_iterator(_Scary->_Myhead, _Scary);

  00044	48 8b 44 24 38	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]

; 1158 :     }

  00049	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0004d	c3		 ret	 0
?end@?$_Tree@V?$_Tmap_traits@GUDailyTimeConnectionEventElem@mu2@@U?$less@G@std@@V?$allocator@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@4@$0A@@std@@@std@@QEBA?AV?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@std@@@std@@@2@XZ ENDP ; std::_Tree<std::_Tmap_traits<unsigned short,mu2::DailyTimeConnectionEventElem,std::less<unsigned short>,std::allocator<std::pair<unsigned short const ,mu2::DailyTimeConnectionEventElem> >,0> >::end
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$SmartPtrEx@VEntityPlayer@mu2@@@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ
_TEXT	SEGMENT
_Pnode$1 = 0
_Pnode$ = 8
$T2 = 16
this$ = 48
??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$SmartPtrEx@VEntityPlayer@mu2@@@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ PROC ; std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::SmartPtrEx<mu2::EntityPlayer> > > >,std::_Iterator_base0>::operator++, COMDAT

; 49   :     _Tree_unchecked_const_iterator& operator++() noexcept {

$LN15:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 50   :         if (_Ptr->_Right->_Isnil) { // climb looking for right subtree

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00011	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00015	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  00019	85 c0		 test	 eax, eax
  0001b	74 4a		 je	 SHORT $LN4@operator
$LN2@operator:

; 51   :             _Nodeptr _Pnode;
; 52   :             while (!(_Pnode = _Ptr->_Parent)->_Isnil && _Ptr == _Pnode->_Right) {

  0001d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00022	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00025	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00029	48 89 04 24	 mov	 QWORD PTR _Pnode$1[rsp], rax
  0002d	48 8b 04 24	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  00031	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  00035	85 c0		 test	 eax, eax
  00037	75 20		 jne	 SHORT $LN3@operator
  00039	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003e	48 8b 0c 24	 mov	 rcx, QWORD PTR _Pnode$1[rsp]
  00042	48 8b 49 10	 mov	 rcx, QWORD PTR [rcx+16]
  00046	48 39 08	 cmp	 QWORD PTR [rax], rcx
  00049	75 0e		 jne	 SHORT $LN3@operator

; 53   :                 _Ptr = _Pnode; // ==> parent while right subtree

  0004b	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00050	48 8b 0c 24	 mov	 rcx, QWORD PTR _Pnode$1[rsp]
  00054	48 89 08	 mov	 QWORD PTR [rax], rcx

; 54   :             }

  00057	eb c4		 jmp	 SHORT $LN2@operator
$LN3@operator:

; 55   : 
; 56   :             _Ptr = _Pnode; // ==> parent (head if end())

  00059	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0005e	48 8b 0c 24	 mov	 rcx, QWORD PTR _Pnode$1[rsp]
  00062	48 89 08	 mov	 QWORD PTR [rax], rcx

; 57   :         } else {

  00065	eb 47		 jmp	 SHORT $LN5@operator
$LN4@operator:

; 58   :             _Ptr = _Mytree::_Min(_Ptr->_Right); // ==> smallest of right subtree

  00067	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0006c	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0006f	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00073	48 89 44 24 08	 mov	 QWORD PTR _Pnode$[rsp], rax
$LN9@operator:

; 476  :         while (!_Pnode->_Left->_Isnil) {

  00078	48 8b 44 24 08	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0007d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00080	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  00084	85 c0		 test	 eax, eax
  00086	75 0f		 jne	 SHORT $LN10@operator

; 477  :             _Pnode = _Pnode->_Left;

  00088	48 8b 44 24 08	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0008d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00090	48 89 44 24 08	 mov	 QWORD PTR _Pnode$[rsp], rax

; 478  :         }

  00095	eb e1		 jmp	 SHORT $LN9@operator
$LN10@operator:

; 479  : 
; 480  :         return _Pnode;

  00097	48 8b 44 24 08	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0009c	48 89 44 24 10	 mov	 QWORD PTR $T2[rsp], rax

; 58   :             _Ptr = _Mytree::_Min(_Ptr->_Right); // ==> smallest of right subtree

  000a1	48 8b 44 24 10	 mov	 rax, QWORD PTR $T2[rsp]
  000a6	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  000ab	48 89 01	 mov	 QWORD PTR [rcx], rax
$LN5@operator:

; 59   :         }
; 60   : 
; 61   :         return *this;

  000ae	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]

; 62   :     }

  000b3	48 83 c4 28	 add	 rsp, 40			; 00000028H
  000b7	c3		 ret	 0
??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$SmartPtrEx@VEntityPlayer@mu2@@@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ ENDP ; std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::SmartPtrEx<mu2::EntityPlayer> > > >,std::_Iterator_base0>::operator++
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ?begin@?$_Tree@V?$_Tmap_traits@IV?$SmartPtrEx@VEntityPlayer@mu2@@@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIV?$SmartPtrEx@VEntityPlayer@mu2@@@mu2@@@std@@@4@$0A@@std@@@std@@QEBA?AV?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$SmartPtrEx@VEntityPlayer@mu2@@@mu2@@@std@@@std@@@std@@@2@XZ
_TEXT	SEGMENT
$T1 = 0
$T2 = 8
_Scary$ = 16
__param0$ = 24
this$ = 48
__$ReturnUdt$ = 56
?begin@?$_Tree@V?$_Tmap_traits@IV?$SmartPtrEx@VEntityPlayer@mu2@@@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIV?$SmartPtrEx@VEntityPlayer@mu2@@@mu2@@@std@@@4@$0A@@std@@@std@@QEBA?AV?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$SmartPtrEx@VEntityPlayer@mu2@@@mu2@@@std@@@std@@@std@@@2@XZ PROC ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::SmartPtrEx<mu2::EntityPlayer>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::SmartPtrEx<mu2::EntityPlayer> > >,0> >::begin, COMDAT

; 1145 :     _NODISCARD const_iterator begin() const noexcept {

$LN29:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  0000e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00013	48 89 04 24	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00017	48 8b 04 24	 mov	 rax, QWORD PTR $T1[rsp]
  0001b	48 89 44 24 08	 mov	 QWORD PTR $T2[rsp], rax

; 1146 :         const auto _Scary = _Get_scary();

  00020	48 8b 44 24 08	 mov	 rax, QWORD PTR $T2[rsp]
  00025	48 89 44 24 10	 mov	 QWORD PTR _Scary$[rsp], rax

; 1147 :         return const_iterator(_Scary->_Myhead->_Left, _Scary);

  0002a	48 8b 44 24 10	 mov	 rax, QWORD PTR _Scary$[rsp]
  0002f	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00032	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00035	48 89 44 24 18	 mov	 QWORD PTR __param0$[rsp], rax

; 37   :     _Tree_unchecked_const_iterator(_Nodeptr _Pnode, const _Mytree* _Plist) noexcept : _Ptr(_Pnode) {

  0003a	48 8b 44 24 38	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]
  0003f	48 8b 4c 24 18	 mov	 rcx, QWORD PTR __param0$[rsp]
  00044	48 89 08	 mov	 QWORD PTR [rax], rcx

; 1147 :         return const_iterator(_Scary->_Myhead->_Left, _Scary);

  00047	48 8b 44 24 38	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]

; 1148 :     }

  0004c	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00050	c3		 ret	 0
?begin@?$_Tree@V?$_Tmap_traits@IV?$SmartPtrEx@VEntityPlayer@mu2@@@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIV?$SmartPtrEx@VEntityPlayer@mu2@@@mu2@@@std@@@4@$0A@@std@@@std@@QEBA?AV?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$SmartPtrEx@VEntityPlayer@mu2@@@mu2@@@std@@@std@@@std@@@2@XZ ENDP ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::SmartPtrEx<mu2::EntityPlayer>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::SmartPtrEx<mu2::EntityPlayer> > >,0> >::begin
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??_GDailyTimeConnectionManager@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GDailyTimeConnectionManager@mu2@@UEAAPEAXI@Z PROC	; mu2::DailyTimeConnectionManager::`scalar deleting destructor', COMDAT
$LN5:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00012	e8 00 00 00 00	 call	 ??1DailyTimeConnectionManager@mu2@@UEAA@XZ ; mu2::DailyTimeConnectionManager::~DailyTimeConnectionManager
  00017	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  0001b	83 e0 01	 and	 eax, 1
  0001e	85 c0		 test	 eax, eax
  00020	74 10		 je	 SHORT $LN2@scalar
  00022	ba 50 00 00 00	 mov	 edx, 80			; 00000050H
  00027	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0002c	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00031	90		 npad	 1
$LN2@scalar:
  00032	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00037	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0003b	c3		 ret	 0
??_GDailyTimeConnectionManager@mu2@@UEAAPEAXI@Z ENDP	; mu2::DailyTimeConnectionManager::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File F:\Release_Branch\Shared\Protocol\Common\StructDateTimeEx.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EntityManager.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File F:\Release_Branch\Server\Development\Framework\Entity\Hsm.h
; File F:\Release_Branch\Server\Development\Framework\Entity\State.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File F:\Release_Branch\Server\Development\Framework\Entity\Action.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File F:\Release_Branch\Server\Development\Framework\Entity\Action.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
;	COMDAT ?updateNextDivisonDate@DailyTimeConnectionManager@mu2@@AEAAXXZ
_TEXT	SEGMENT
$T1 = 32
$T2 = 33
comparer$ = 36
resetHour$ = 40
tv255 = 48
tv241 = 52
$T3 = 56
$S141$4 = 64
$T5 = 72
$S139$6 = 80
i$7 = 88
this$ = 96
elem$8 = 104
action$9 = 112
$T10 = 120
$T11 = 128
$T12 = 136
entityMap$ = 144
$T13 = 152
$T14 = 160
_Scary$15 = 168
__param0$ = 176
$T16 = 184
$T17 = 192
_Scary$18 = 200
__param0$ = 208
$S140$19 = 216
$T20 = 224
$T21 = 232
$T22 = 240
this$ = 248
$T23 = 256
$T24 = 264
$T25 = 272
$T26 = 280
$T27 = 288
$T28 = 296
__$ArrayPad$ = 312
this$ = 352
?updateNextDivisonDate@DailyTimeConnectionManager@mu2@@AEAAXXZ PROC ; mu2::DailyTimeConnectionManager::updateNextDivisonDate, COMDAT

; 104  : 	{

$LN160:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	56		 push	 rsi
  00006	57		 push	 rdi
  00007	48 81 ec 48 01
	00 00		 sub	 rsp, 328		; 00000148H
  0000e	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR __security_cookie
  00015	48 33 c4	 xor	 rax, rsp
  00018	48 89 84 24 38
	01 00 00	 mov	 QWORD PTR __$ArrayPad$[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  00020	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A ; mu2::ISingleton<mu2::GlobalLoadScript>::inst
  00027	48 89 44 24 78	 mov	 QWORD PTR $T10[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 105  : 		DailyComparer comparer(SCRIPTS.GetDailyDungeonRechargeTime());

  0002c	48 8b 44 24 78	 mov	 rax, QWORD PTR $T10[rsp]
  00031	48 8b c8	 mov	 rcx, rax
  00034	e8 00 00 00 00	 call	 ?GetDailyDungeonRechargeTime@GlobalLoadScript@mu2@@QEBA?BGXZ ; mu2::GlobalLoadScript::GetDailyDungeonRechargeTime
  00039	66 89 44 24 28	 mov	 WORD PTR resetHour$[rsp], ax
; File F:\Release_Branch\Shared\Protocol\Common\StructDateTimeEx.h

; 369  : 	DailyComparer(WORD resetHour) :m_resetHour(resetHour)

  0003e	0f b7 44 24 28	 movzx	 eax, WORD PTR resetHour$[rsp]
  00043	66 89 44 24 24	 mov	 WORD PTR comparer$[rsp], ax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 107  : 		if (comparer.CompareDailyTimeStamp(m_divisionDate) == false)

  00048	48 8b 84 24 60
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00050	48 83 c0 38	 add	 rax, 56			; 00000038H
  00054	48 8b d0	 mov	 rdx, rax
  00057	48 8d 4c 24 24	 lea	 rcx, QWORD PTR comparer$[rsp]
  0005c	e8 00 00 00 00	 call	 ?CompareDailyTimeStamp@DailyComparer@mu2@@QEAA_NAEBU_SYSTEMTIME@@@Z ; mu2::DailyComparer::CompareDailyTimeStamp
  00061	0f b6 c0	 movzx	 eax, al
  00064	85 c0		 test	 eax, eax
  00066	75 05		 jne	 SHORT $LN5@updateNext

; 108  : 			return;

  00068	e9 bc 02 00 00	 jmp	 $LN1@updateNext
$LN5@updateNext:

; 109  : 
; 110  : 		m_divisionDate = comparer.GetDailyTimeStamp();

  0006d	48 8d 94 24 28
	01 00 00	 lea	 rdx, QWORD PTR $T28[rsp]
  00075	48 8d 4c 24 24	 lea	 rcx, QWORD PTR comparer$[rsp]
  0007a	e8 00 00 00 00	 call	 ?GetDailyTimeStamp@DailyComparer@mu2@@QEAA?AU_SYSTEMTIME@@XZ ; mu2::DailyComparer::GetDailyTimeStamp
  0007f	48 8b 8c 24 60
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00087	48 8d 79 38	 lea	 rdi, QWORD PTR [rcx+56]
  0008b	48 8b f0	 mov	 rsi, rax
  0008e	b9 10 00 00 00	 mov	 ecx, 16
  00093	f3 a4		 rep movsb
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  00095	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VManagerEntityPlayer@mu2@@@mu2@@1VManagerEntityPlayer@2@A ; mu2::ISingleton<mu2::ManagerEntityPlayer>::inst
  0009c	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EntityManager.h

; 154  : 	const MapPlayer& GetEntityMap() const { return m_mapPlayerSessionKey; }

  000a4	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  000ac	48 83 c0 08	 add	 rax, 8
  000b0	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 112  : 		auto& entityMap = theManagerPlayer.GetEntityMap();

  000b8	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
  000c0	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR entityMap$[rsp], rax

; 113  : 		for each(auto& i in entityMap)

  000c8	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR entityMap$[rsp]
  000d0	48 89 44 24 50	 mov	 QWORD PTR $S139$6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  000d5	48 8b 44 24 50	 mov	 rax, QWORD PTR $S139$6[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  000da	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  000e2	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR $T13[rsp]
  000ea	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T14[rsp], rax

; 1156 :         const auto _Scary = _Get_scary();

  000f2	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR $T14[rsp]
  000fa	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR _Scary$15[rsp], rax

; 1157 :         return const_iterator(_Scary->_Myhead, _Scary);

  00102	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR _Scary$15[rsp]
  0010a	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0010d	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR __param0$[rsp], rax

; 37   :     _Tree_unchecked_const_iterator(_Nodeptr _Pnode, const _Mytree* _Plist) noexcept : _Ptr(_Pnode) {

  00115	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR __param0$[rsp]
  0011d	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR $S140$19[rsp], rax

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00125	48 8b 44 24 50	 mov	 rax, QWORD PTR $S139$6[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0012a	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T16[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00132	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T16[rsp]
  0013a	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T17[rsp], rax

; 1146 :         const auto _Scary = _Get_scary();

  00142	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR $T17[rsp]
  0014a	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR _Scary$18[rsp], rax

; 1147 :         return const_iterator(_Scary->_Myhead->_Left, _Scary);

  00152	48 8b 84 24 c8
	00 00 00	 mov	 rax, QWORD PTR _Scary$18[rsp]
  0015a	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0015d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00160	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR __param0$[rsp], rax

; 37   :     _Tree_unchecked_const_iterator(_Nodeptr _Pnode, const _Mytree* _Plist) noexcept : _Ptr(_Pnode) {

  00168	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR __param0$[rsp]
  00170	48 89 44 24 40	 mov	 QWORD PTR $S141$4[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 113  : 		for each(auto& i in entityMap)

  00175	eb 0b		 jmp	 SHORT $LN4@updateNext
$LN2@updateNext:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 198  :         _Mybase::operator++();

  00177	48 8d 4c 24 40	 lea	 rcx, QWORD PTR $S141$4[rsp]
  0017c	e8 00 00 00 00	 call	 ??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$SmartPtrEx@VEntityPlayer@mu2@@@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ ; std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::SmartPtrEx<mu2::EntityPlayer> > > >,std::_Iterator_base0>::operator++
  00181	90		 npad	 1
$LN4@updateNext:

; 232  :         return this->_Ptr == _Right._Ptr;

  00182	48 8b 84 24 d8
	00 00 00	 mov	 rax, QWORD PTR $S140$19[rsp]
  0018a	48 39 44 24 40	 cmp	 QWORD PTR $S141$4[rsp], rax
  0018f	75 0a		 jne	 SHORT $LN121@updateNext
  00191	c7 44 24 30 01
	00 00 00	 mov	 DWORD PTR tv255[rsp], 1
  00199	eb 08		 jmp	 SHORT $LN122@updateNext
$LN121@updateNext:
  0019b	c7 44 24 30 00
	00 00 00	 mov	 DWORD PTR tv255[rsp], 0
$LN122@updateNext:
  001a3	0f b6 44 24 30	 movzx	 eax, BYTE PTR tv255[rsp]
  001a8	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al

; 237  :         return !(*this == _Right);

  001ac	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  001b1	0f b6 c0	 movzx	 eax, al
  001b4	85 c0		 test	 eax, eax
  001b6	75 0a		 jne	 SHORT $LN114@updateNext
  001b8	c7 44 24 34 01
	00 00 00	 mov	 DWORD PTR tv241[rsp], 1
  001c0	eb 08		 jmp	 SHORT $LN115@updateNext
$LN114@updateNext:
  001c2	c7 44 24 34 00
	00 00 00	 mov	 DWORD PTR tv241[rsp], 0
$LN115@updateNext:
  001ca	0f b6 44 24 34	 movzx	 eax, BYTE PTR tv241[rsp]
  001cf	88 44 24 21	 mov	 BYTE PTR $T2[rsp], al
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 113  : 		for each(auto& i in entityMap)

  001d3	0f b6 44 24 21	 movzx	 eax, BYTE PTR $T2[rsp]
  001d8	0f b6 c0	 movzx	 eax, al
  001db	85 c0		 test	 eax, eax
  001dd	0f 84 46 01 00
	00		 je	 $LN3@updateNext
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 185  :         return this->_Ptr->_Myval;

  001e3	48 8b 44 24 40	 mov	 rax, QWORD PTR $S141$4[rsp]
  001e8	48 83 c0 20	 add	 rax, 32			; 00000020H
  001ec	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR $T20[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 113  : 		for each(auto& i in entityMap)

  001f4	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR $T20[rsp]
  001fc	48 89 44 24 58	 mov	 QWORD PTR i$7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1309 :         return _Ptr;

  00201	48 8b 44 24 58	 mov	 rax, QWORD PTR i$7[rsp]
  00206	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0020a	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR $T21[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h

; 186  : 		return ptr.get();

  00212	48 8b 84 24 e8
	00 00 00	 mov	 rax, QWORD PTR $T21[rsp]
  0021a	48 89 84 24 f0
	00 00 00	 mov	 QWORD PTR $T22[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 115  : 			if (i.second->GetHsm().GetCurrent()->GetId() == PLAYER_STATE_ZONE)

  00222	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR $T22[rsp]
  0022a	48 8b c8	 mov	 rcx, rax
  0022d	e8 00 00 00 00	 call	 ?GetHsm@Entity@mu2@@QEAAAEAVHsm@2@XZ ; mu2::Entity::GetHsm
  00232	48 89 84 24 f8
	00 00 00	 mov	 QWORD PTR this$[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Entity\Hsm.h

; 79   : 	return m_current;

  0023a	48 8b 84 24 f8
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00242	48 8b 40 68	 mov	 rax, QWORD PTR [rax+104]
  00246	48 89 84 24 00
	01 00 00	 mov	 QWORD PTR $T23[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Entity\State.h

; 105  : 	return m_id;

  0024e	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR $T23[rsp]
  00256	8b 40 10	 mov	 eax, DWORD PTR [rax+16]
  00259	89 44 24 38	 mov	 DWORD PTR $T3[rsp], eax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 115  : 			if (i.second->GetHsm().GetCurrent()->GetId() == PLAYER_STATE_ZONE)

  0025d	8b 44 24 38	 mov	 eax, DWORD PTR $T3[rsp]
  00261	83 f8 05	 cmp	 eax, 5
  00264	0f 85 ba 00 00
	00		 jne	 $LN6@updateNext
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1309 :         return _Ptr;

  0026a	48 8b 44 24 58	 mov	 rax, QWORD PTR i$7[rsp]
  0026f	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00273	48 89 84 24 08
	01 00 00	 mov	 QWORD PTR $T24[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h

; 191  : 		return ptr.get();

  0027b	48 8b 84 24 08
	01 00 00	 mov	 rax, QWORD PTR $T24[rsp]
  00283	48 89 84 24 10
	01 00 00	 mov	 QWORD PTR $T25[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 117  : 				PlayerDailyTimeConnectionAction* action = GetEntityAction(i.second.RawPtr());

  0028b	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR $T25[rsp]
; File F:\Release_Branch\Server\Development\Framework\Entity\Action.h

; 43   : 	GetEntityAction(Entity* owner) : owner_(owner) {}

  00293	48 89 84 24 18
	01 00 00	 mov	 QWORD PTR $T26[rsp], rax
  0029b	48 8d 84 24 18
	01 00 00	 lea	 rax, QWORD PTR $T26[rsp]
  002a3	48 89 84 24 20
	01 00 00	 mov	 QWORD PTR $T27[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 117  : 				PlayerDailyTimeConnectionAction* action = GetEntityAction(i.second.RawPtr());

  002ab	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR $T27[rsp]
  002b3	48 89 44 24 60	 mov	 QWORD PTR this$[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Entity\Action.h

; 49   : 		if ( nullptr == owner_ )

  002b8	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  002bd	48 83 38 00	 cmp	 QWORD PTR [rax], 0
  002c1	75 0b		 jne	 SHORT $LN152@updateNext

; 50   : 		{
; 51   : 			return nullptr;

  002c3	48 c7 44 24 48
	00 00 00 00	 mov	 QWORD PTR $T5[rsp], 0
  002cc	eb 34		 jmp	 SHORT $LN151@updateNext
$LN152@updateNext:

; 52   : 		}
; 53   : 
; 54   : 		Action* elem = owner_->GetAction( ActionType::ID );

  002ce	ba 15 00 00 00	 mov	 edx, 21
  002d3	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  002d8	48 8b 08	 mov	 rcx, QWORD PTR [rax]
  002db	e8 00 00 00 00	 call	 ?GetAction@Entity@mu2@@QEBAPEAVAction@2@I@Z ; mu2::Entity::GetAction
  002e0	48 89 44 24 68	 mov	 QWORD PTR elem$8[rsp], rax

; 55   : 		if ( elem == nullptr )

  002e5	48 83 7c 24 68
	00		 cmp	 QWORD PTR elem$8[rsp], 0
  002eb	75 0b		 jne	 SHORT $LN153@updateNext

; 56   : 		{
; 57   : 			return nullptr;

  002ed	48 c7 44 24 48
	00 00 00 00	 mov	 QWORD PTR $T5[rsp], 0
  002f6	eb 0a		 jmp	 SHORT $LN151@updateNext
$LN153@updateNext:

; 58   : 		}
; 59   : 
; 60   : 		assert( dynamic_cast<ActionType*>( elem ) != nullptr );
; 61   : 		return static_cast<ActionType*>( elem );

  002f8	48 8b 44 24 68	 mov	 rax, QWORD PTR elem$8[rsp]
  002fd	48 89 44 24 48	 mov	 QWORD PTR $T5[rsp], rax
$LN151@updateNext:
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 117  : 				PlayerDailyTimeConnectionAction* action = GetEntityAction(i.second.RawPtr());

  00302	48 8b 44 24 48	 mov	 rax, QWORD PTR $T5[rsp]
  00307	48 89 44 24 70	 mov	 QWORD PTR action$9[rsp], rax

; 118  : 				VALID_DO(action, continue);

  0030c	48 83 7c 24 70
	00		 cmp	 QWORD PTR action$9[rsp], 0
  00312	75 05		 jne	 SHORT $LN7@updateNext
  00314	e9 5e fe ff ff	 jmp	 $LN2@updateNext
$LN7@updateNext:

; 119  : 				action->NotifyChangeNextDay();

  00319	48 8b 4c 24 70	 mov	 rcx, QWORD PTR action$9[rsp]
  0031e	e8 00 00 00 00	 call	 ?NotifyChangeNextDay@PlayerDailyTimeConnectionAction@mu2@@QEAAXXZ ; mu2::PlayerDailyTimeConnectionAction::NotifyChangeNextDay
  00323	90		 npad	 1
$LN6@updateNext:

; 120  : 			}
; 121  : 		}

  00324	e9 4e fe ff ff	 jmp	 $LN2@updateNext
$LN3@updateNext:
$LN1@updateNext:

; 122  : 	}

  00329	48 8b 8c 24 38
	01 00 00	 mov	 rcx, QWORD PTR __$ArrayPad$[rsp]
  00331	48 33 cc	 xor	 rcx, rsp
  00334	e8 00 00 00 00	 call	 __security_check_cookie
  00339	48 81 c4 48 01
	00 00		 add	 rsp, 328		; 00000148H
  00340	5f		 pop	 rdi
  00341	5e		 pop	 rsi
  00342	c3		 ret	 0
?updateNextDivisonDate@DailyTimeConnectionManager@mu2@@AEAAXXZ ENDP ; mu2::DailyTimeConnectionManager::updateNextDivisonDate
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 33
comparer$ = 36
resetHour$ = 40
tv255 = 48
tv241 = 52
$T3 = 56
$S141$4 = 64
$T5 = 72
$S139$6 = 80
i$7 = 88
this$ = 96
elem$8 = 104
action$9 = 112
$T10 = 120
$T11 = 128
$T12 = 136
entityMap$ = 144
$T13 = 152
$T14 = 160
_Scary$15 = 168
__param0$ = 176
$T16 = 184
$T17 = 192
_Scary$18 = 200
__param0$ = 208
$S140$19 = 216
$T20 = 224
$T21 = 232
$T22 = 240
this$ = 248
$T23 = 256
$T24 = 264
$T25 = 272
$T26 = 280
$T27 = 288
$T28 = 296
__$ArrayPad$ = 312
this$ = 352
?dtor$0@?0??updateNextDivisonDate@DailyTimeConnectionManager@mu2@@AEAAXXZ@4HA PROC ; `mu2::DailyTimeConnectionManager::updateNextDivisonDate'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8d 4d 24	 lea	 rcx, QWORD PTR comparer$[rbp]
  0000d	e8 00 00 00 00	 call	 ??1DailyComparer@mu2@@QEAA@XZ ; mu2::DailyComparer::~DailyComparer
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$0@?0??updateNextDivisonDate@DailyTimeConnectionManager@mu2@@AEAAXXZ@4HA ENDP ; `mu2::DailyTimeConnectionManager::updateNextDivisonDate'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
;	COMDAT ?updatePlayers@DailyTimeConnectionManager@mu2@@AEAAXXZ
_TEXT	SEGMENT
this$ = 8
?updatePlayers@DailyTimeConnectionManager@mu2@@AEAAXXZ PROC ; mu2::DailyTimeConnectionManager::updatePlayers, COMDAT

; 125  : 	{

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 126  : 		// PlayerStateZone으로 업데이트 옮김. 
; 127  : 
; 128  : 		/*if (m_playerUpdateTimer.Elapsed() < PLAYER_UPDATE_TIMER)
; 129  : 			return;
; 130  : 
; 131  : 		m_playerUpdateTimer.Reset();
; 132  : 
; 133  : 		auto& entityMap = theManagerPlayer.GetEntityMap();
; 134  : 		for each(auto& i in entityMap)
; 135  : 		{
; 136  : 			if (i.second->GetHsm().GetCurrent()->GetId() == PLAYER_STATE_ZONE)
; 137  : 			{
; 138  : 				PlayerDailyTimeConnectionAction* action = GetEntityAction(i.second.RawPtr());
; 139  : 				VALID_DO(action, continue);
; 140  : 				action->SyncAndSaveToDB();
; 141  : 			}
; 142  : 		}*/
; 143  : 	}

  00005	c3		 ret	 0
?updatePlayers@DailyTimeConnectionManager@mu2@@AEAAXXZ ENDP ; mu2::DailyTimeConnectionManager::updatePlayers
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File F:\Release_Branch\Shared\Protocol\Common\StructDateTimeEx.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File F:\Release_Branch\Shared\Protocol\Common\StructDateTimeEx.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
;	COMDAT ?updateEventClose@DailyTimeConnectionManager@mu2@@AEAA_NXZ
_TEXT	SEGMENT
$T1 = 32
$T2 = 33
$T3 = 34
$T4 = 35
$T5 = 36
$T6 = 37
resetHour$ = 40
comparer$7 = 48
resetHour$ = 56
comparer$8 = 64
iter_srt$9 = 72
tv248 = 80
tv218 = 84
tv373 = 88
tv353 = 92
map$10 = 96
iter$11 = 104
map$ = 112
$T12 = 120
$T13 = 128
$T14 = 136
__param0$ = 144
_Right$ = 152
$T15 = 160
$T16 = 168
$T17 = 176
$T18 = 184
$T19 = 192
$T20 = 200
$T21 = 208
$T22 = 216
$T23 = 224
_Scary$24 = 232
__param0$ = 240
_Tmp$25 = 248
$T26 = 256
$T27 = 264
_Scary$28 = 272
__param0$ = 280
$T29 = 288
$T30 = 296
$T31 = 304
$T32 = 312
$T33 = 320
$T34 = 328
$T35 = 336
$T36 = 344
$T37 = 352
$T38 = 360
$T39 = 368
$T40 = 376
$T41 = 384
$T42 = 392
$T43 = 400
$T44 = 408
$T45 = 416
$T46 = 424
$T47 = 432
$T48 = 440
$T49 = 448
$T50 = 456
$T51 = 464
$T52 = 480
$T53 = 496
$T54 = 512
__$ArrayPad$ = 528
this$ = 576
?updateEventClose@DailyTimeConnectionManager@mu2@@AEAA_NXZ PROC ; mu2::DailyTimeConnectionManager::updateEventClose, COMDAT

; 211  : 	{

$LN308:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	56		 push	 rsi
  00006	57		 push	 rdi
  00007	48 81 ec 28 02
	00 00		 sub	 rsp, 552		; 00000228H
  0000e	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR __security_cookie
  00015	48 33 c4	 xor	 rax, rsp
  00018	48 89 84 24 10
	02 00 00	 mov	 QWORD PTR __$ArrayPad$[rsp], rax

; 212  : 		//================================================================================================================
; 213  : 		// 개발 테스트용 
; 214  : 		if (m_devIndexEvent > 0)

  00020	48 8b 84 24 40
	02 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00028	0f b7 40 48	 movzx	 eax, WORD PTR [rax+72]
  0002c	85 c0		 test	 eax, eax
  0002e	0f 8e 07 02 00
	00		 jle	 $LN5@updateEven
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  00034	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A ; mu2::ISingleton<mu2::GlobalLoadScript>::inst
  0003b	48 89 44 24 78	 mov	 QWORD PTR $T12[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 216  : 			const DailyTimeConnectionEventElems& map = SCRIPTS.GetDailyTimeConnectionEventMap();

  00040	48 8b 44 24 78	 mov	 rax, QWORD PTR $T12[rsp]
  00045	48 8b c8	 mov	 rcx, rax
  00048	e8 00 00 00 00	 call	 ?GetDailyTimeConnectionEventMap@GlobalLoadScript@mu2@@QEAAAEBV?$map@GUDailyTimeConnectionEventElem@mu2@@U?$less@G@std@@V?$allocator@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@4@@std@@XZ ; mu2::GlobalLoadScript::GetDailyTimeConnectionEventMap
  0004d	48 89 44 24 60	 mov	 QWORD PTR map$10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00052	48 8b 44 24 60	 mov	 rax, QWORD PTR map$10[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00057	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  0005f	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR $T13[rsp]
  00067	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T14[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 217  : 			auto iter = map.find(m_devIndexEvent);

  0006f	48 8b 84 24 40
	02 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00077	48 83 c0 48	 add	 rax, 72			; 00000048H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1398 :         return const_iterator(_Find(_Keyval), _Get_scary());

  0007b	48 8b d0	 mov	 rdx, rax
  0007e	48 8b 4c 24 60	 mov	 rcx, QWORD PTR map$10[rsp]
  00083	e8 00 00 00 00	 call	 ??$_Find@G@?$_Tree@V?$_Tmap_traits@GUDailyTimeConnectionEventElem@mu2@@U?$less@G@std@@V?$allocator@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@4@$0A@@std@@@std@@AEBAPEAU?$_Tree_node@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@PEAX@1@AEBG@Z ; std::_Tree<std::_Tmap_traits<unsigned short,mu2::DailyTimeConnectionEventElem,std::less<unsigned short>,std::allocator<std::pair<unsigned short const ,mu2::DailyTimeConnectionEventElem> >,0> >::_Find<unsigned short>
  00088	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR __param0$[rsp], rax
  00090	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T14[rsp]
  00098	4c 8b c0	 mov	 r8, rax
  0009b	48 8b 94 24 90
	00 00 00	 mov	 rdx, QWORD PTR __param0$[rsp]
  000a3	48 8d 4c 24 68	 lea	 rcx, QWORD PTR iter$11[rsp]
  000a8	e8 00 00 00 00	 call	 ??0?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAA@PEAU?$_Tree_node@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@PEAX@1@PEBV?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@std@@@1@@Z ; std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned short const ,mu2::DailyTimeConnectionEventElem> > >,std::_Iterator_base0>::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned short const ,mu2::DailyTimeConnectionEventElem> > >,std::_Iterator_base0>
  000ad	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 218  : 			if (iter != map.end())

  000ae	48 8d 94 24 c0
	01 00 00	 lea	 rdx, QWORD PTR $T49[rsp]
  000b6	48 8b 4c 24 60	 mov	 rcx, QWORD PTR map$10[rsp]
  000bb	e8 00 00 00 00	 call	 ?end@?$_Tree@V?$_Tmap_traits@GUDailyTimeConnectionEventElem@mu2@@U?$less@G@std@@V?$allocator@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@4@$0A@@std@@@std@@QEBA?AV?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@std@@@std@@@2@XZ ; std::_Tree<std::_Tmap_traits<unsigned short,mu2::DailyTimeConnectionEventElem,std::less<unsigned short>,std::allocator<std::pair<unsigned short const ,mu2::DailyTimeConnectionEventElem> >,0> >::end
  000c0	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR _Right$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 232  :         return this->_Ptr == _Right._Ptr;

  000c8	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR _Right$[rsp]
  000d0	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000d3	48 39 44 24 68	 cmp	 QWORD PTR iter$11[rsp], rax
  000d8	75 0a		 jne	 SHORT $LN61@updateEven
  000da	c7 44 24 50 01
	00 00 00	 mov	 DWORD PTR tv248[rsp], 1
  000e2	eb 08		 jmp	 SHORT $LN62@updateEven
$LN61@updateEven:
  000e4	c7 44 24 50 00
	00 00 00	 mov	 DWORD PTR tv248[rsp], 0
$LN62@updateEven:
  000ec	0f b6 44 24 50	 movzx	 eax, BYTE PTR tv248[rsp]
  000f1	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al

; 237  :         return !(*this == _Right);

  000f5	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  000fa	0f b6 c0	 movzx	 eax, al
  000fd	85 c0		 test	 eax, eax
  000ff	75 0a		 jne	 SHORT $LN54@updateEven
  00101	c7 44 24 54 01
	00 00 00	 mov	 DWORD PTR tv218[rsp], 1
  00109	eb 08		 jmp	 SHORT $LN55@updateEven
$LN54@updateEven:
  0010b	c7 44 24 54 00
	00 00 00	 mov	 DWORD PTR tv218[rsp], 0
$LN55@updateEven:
  00113	0f b6 44 24 54	 movzx	 eax, BYTE PTR tv218[rsp]
  00118	88 44 24 21	 mov	 BYTE PTR $T2[rsp], al
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 218  : 			if (iter != map.end())

  0011c	0f b6 44 24 21	 movzx	 eax, BYTE PTR $T2[rsp]
  00121	0f b6 c0	 movzx	 eax, al
  00124	85 c0		 test	 eax, eax
  00126	0f 84 08 01 00
	00		 je	 $LN6@updateEven
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  0012c	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A ; mu2::ISingleton<mu2::GlobalLoadScript>::inst
  00133	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T15[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 220  : 				m_dailyTimeConnectionRewardElem = SCRIPTS.GetDailyTimeConnectionRewardElem(m_devIndexEvent);

  0013b	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR $T15[rsp]
  00143	48 8b 8c 24 40
	02 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0014b	0f b7 51 48	 movzx	 edx, WORD PTR [rcx+72]
  0014f	48 8b c8	 mov	 rcx, rax
  00152	e8 00 00 00 00	 call	 ?GetDailyTimeConnectionRewardElem@GlobalLoadScript@mu2@@QEAAPEBUDailyTimeConnectionRewardElem@2@G@Z ; mu2::GlobalLoadScript::GetDailyTimeConnectionRewardElem
  00157	48 8b 8c 24 40
	02 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0015f	48 89 41 30	 mov	 QWORD PTR [rcx+48], rax

; 221  : 				if (m_dailyTimeConnectionRewardElem)

  00163	48 8b 84 24 40
	02 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0016b	48 83 78 30 00	 cmp	 QWORD PTR [rax+48], 0
  00170	0f 84 be 00 00
	00		 je	 $LN6@updateEven
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 185  :         return this->_Ptr->_Myval;

  00176	48 8b 44 24 68	 mov	 rax, QWORD PTR iter$11[rsp]
  0017b	48 83 c0 1a	 add	 rax, 26
  0017f	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T16[rsp], rax

; 189  :         return pointer_traits<pointer>::pointer_to(**this);

  00187	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T16[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0018f	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T17[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 528  :         return _STD addressof(_Val);

  00197	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR $T17[rsp]
  0019f	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T18[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 189  :         return pointer_traits<pointer>::pointer_to(**this);

  001a7	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T18[rsp]
  001af	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T19[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 223  : 					m_dailyTimeConnectionEventElem = &iter->second;

  001b7	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR $T19[rsp]
  001bf	48 83 c0 02	 add	 rax, 2
  001c3	48 8b 8c 24 40
	02 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  001cb	48 89 41 28	 mov	 QWORD PTR [rcx+40], rax
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  001cf	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A ; mu2::ISingleton<mu2::GlobalLoadScript>::inst
  001d6	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T20[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 225  : 					DailyComparer comparer(SCRIPTS.GetDailyDungeonRechargeTime());

  001de	48 8b 84 24 c8
	00 00 00	 mov	 rax, QWORD PTR $T20[rsp]
  001e6	48 8b c8	 mov	 rcx, rax
  001e9	e8 00 00 00 00	 call	 ?GetDailyDungeonRechargeTime@GlobalLoadScript@mu2@@QEBA?BGXZ ; mu2::GlobalLoadScript::GetDailyDungeonRechargeTime
  001ee	66 89 44 24 28	 mov	 WORD PTR resetHour$[rsp], ax
; File F:\Release_Branch\Shared\Protocol\Common\StructDateTimeEx.h

; 369  : 	DailyComparer(WORD resetHour) :m_resetHour(resetHour)

  001f3	0f b7 44 24 28	 movzx	 eax, WORD PTR resetHour$[rsp]
  001f8	66 89 44 24 30	 mov	 WORD PTR comparer$7[rsp], ax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 226  : 					m_divisionDate = comparer.GetDailyTimeStamp();

  001fd	48 8d 94 24 f0
	01 00 00	 lea	 rdx, QWORD PTR $T53[rsp]
  00205	48 8d 4c 24 30	 lea	 rcx, QWORD PTR comparer$7[rsp]
  0020a	e8 00 00 00 00	 call	 ?GetDailyTimeStamp@DailyComparer@mu2@@QEAA?AU_SYSTEMTIME@@XZ ; mu2::DailyComparer::GetDailyTimeStamp
  0020f	48 8b 8c 24 40
	02 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00217	48 8d 79 38	 lea	 rdi, QWORD PTR [rcx+56]
  0021b	48 8b f0	 mov	 rsi, rax
  0021e	b9 10 00 00 00	 mov	 ecx, 16
  00223	f3 a4		 rep movsb

; 227  : 
; 228  : 					return true;

  00225	c6 44 24 22 01	 mov	 BYTE PTR $T3[rsp], 1
  0022a	0f b6 44 24 22	 movzx	 eax, BYTE PTR $T3[rsp]
  0022f	e9 9c 03 00 00	 jmp	 $LN1@updateEven
$LN6@updateEven:

; 229  : 				}
; 230  : 			}
; 231  : 			return false;

  00234	32 c0		 xor	 al, al
  00236	e9 95 03 00 00	 jmp	 $LN1@updateEven
$LN5@updateEven:
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  0023b	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A ; mu2::ISingleton<mu2::GlobalLoadScript>::inst
  00242	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR $T21[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 236  : 		const DailyTimeConnectionEventElems& map = SCRIPTS.GetDailyTimeConnectionEventMap();

  0024a	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR $T21[rsp]
  00252	48 8b c8	 mov	 rcx, rax
  00255	e8 00 00 00 00	 call	 ?GetDailyTimeConnectionEventMap@GlobalLoadScript@mu2@@QEAAAEBV?$map@GUDailyTimeConnectionEventElem@mu2@@U?$less@G@std@@V?$allocator@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@4@@std@@XZ ; mu2::GlobalLoadScript::GetDailyTimeConnectionEventMap
  0025a	48 89 44 24 70	 mov	 QWORD PTR map$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  0025f	48 8b 44 24 70	 mov	 rax, QWORD PTR map$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00264	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR $T22[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  0026c	48 8b 84 24 d8
	00 00 00	 mov	 rax, QWORD PTR $T22[rsp]
  00274	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR $T23[rsp], rax

; 1146 :         const auto _Scary = _Get_scary();

  0027c	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR $T23[rsp]
  00284	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR _Scary$24[rsp], rax

; 1147 :         return const_iterator(_Scary->_Myhead->_Left, _Scary);

  0028c	48 8b 84 24 e8
	00 00 00	 mov	 rax, QWORD PTR _Scary$24[rsp]
  00294	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00297	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0029a	48 89 84 24 f0
	00 00 00	 mov	 QWORD PTR __param0$[rsp], rax

; 37   :     _Tree_unchecked_const_iterator(_Nodeptr _Pnode, const _Mytree* _Plist) noexcept : _Ptr(_Pnode) {

  002a2	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR __param0$[rsp]
  002aa	48 89 44 24 48	 mov	 QWORD PTR iter_srt$9[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 238  : 		for (auto iter_srt = map.begin(); iter_srt != map.end(); iter_srt++)

  002af	eb 28		 jmp	 SHORT $LN4@updateEven
$LN2@updateEven:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 203  :         _Tree_const_iterator _Tmp = *this;

  002b1	48 8b 44 24 48	 mov	 rax, QWORD PTR iter_srt$9[rsp]
  002b6	48 89 84 24 f8
	00 00 00	 mov	 QWORD PTR _Tmp$25[rsp], rax

; 198  :         _Mybase::operator++();

  002be	48 8d 4c 24 48	 lea	 rcx, QWORD PTR iter_srt$9[rsp]
  002c3	e8 00 00 00 00	 call	 ??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBGUDailyTimeConnectionEventElem@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ ; std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned short const ,mu2::DailyTimeConnectionEventElem> > >,std::_Iterator_base0>::operator++
  002c8	90		 npad	 1

; 205  :         return _Tmp;

  002c9	48 8b 84 24 f8
	00 00 00	 mov	 rax, QWORD PTR _Tmp$25[rsp]
  002d1	48 89 84 24 c8
	01 00 00	 mov	 QWORD PTR $T50[rsp], rax
$LN4@updateEven:

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  002d9	48 8b 44 24 70	 mov	 rax, QWORD PTR map$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  002de	48 89 84 24 00
	01 00 00	 mov	 QWORD PTR $T26[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  002e6	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR $T26[rsp]
  002ee	48 89 84 24 08
	01 00 00	 mov	 QWORD PTR $T27[rsp], rax

; 1156 :         const auto _Scary = _Get_scary();

  002f6	48 8b 84 24 08
	01 00 00	 mov	 rax, QWORD PTR $T27[rsp]
  002fe	48 89 84 24 10
	01 00 00	 mov	 QWORD PTR _Scary$28[rsp], rax

; 1157 :         return const_iterator(_Scary->_Myhead, _Scary);

  00306	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR _Scary$28[rsp]
  0030e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00311	48 89 84 24 18
	01 00 00	 mov	 QWORD PTR __param0$[rsp], rax

; 37   :     _Tree_unchecked_const_iterator(_Nodeptr _Pnode, const _Mytree* _Plist) noexcept : _Ptr(_Pnode) {

  00319	48 8b 84 24 18
	01 00 00	 mov	 rax, QWORD PTR __param0$[rsp]
  00321	48 89 84 24 20
	01 00 00	 mov	 QWORD PTR $T29[rsp], rax

; 1157 :         return const_iterator(_Scary->_Myhead, _Scary);

  00329	48 8d 84 24 20
	01 00 00	 lea	 rax, QWORD PTR $T29[rsp]
  00331	48 89 84 24 28
	01 00 00	 mov	 QWORD PTR $T30[rsp], rax

; 232  :         return this->_Ptr == _Right._Ptr;

  00339	48 8b 84 24 28
	01 00 00	 mov	 rax, QWORD PTR $T30[rsp]
  00341	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00344	48 39 44 24 48	 cmp	 QWORD PTR iter_srt$9[rsp], rax
  00349	75 0a		 jne	 SHORT $LN195@updateEven
  0034b	c7 44 24 58 01
	00 00 00	 mov	 DWORD PTR tv373[rsp], 1
  00353	eb 08		 jmp	 SHORT $LN196@updateEven
$LN195@updateEven:
  00355	c7 44 24 58 00
	00 00 00	 mov	 DWORD PTR tv373[rsp], 0
$LN196@updateEven:
  0035d	0f b6 44 24 58	 movzx	 eax, BYTE PTR tv373[rsp]
  00362	88 44 24 23	 mov	 BYTE PTR $T4[rsp], al

; 237  :         return !(*this == _Right);

  00366	0f b6 44 24 23	 movzx	 eax, BYTE PTR $T4[rsp]
  0036b	0f b6 c0	 movzx	 eax, al
  0036e	85 c0		 test	 eax, eax
  00370	75 0a		 jne	 SHORT $LN188@updateEven
  00372	c7 44 24 5c 01
	00 00 00	 mov	 DWORD PTR tv353[rsp], 1
  0037a	eb 08		 jmp	 SHORT $LN189@updateEven
$LN188@updateEven:
  0037c	c7 44 24 5c 00
	00 00 00	 mov	 DWORD PTR tv353[rsp], 0
$LN189@updateEven:
  00384	0f b6 44 24 5c	 movzx	 eax, BYTE PTR tv353[rsp]
  00389	88 44 24 24	 mov	 BYTE PTR $T5[rsp], al
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 238  : 		for (auto iter_srt = map.begin(); iter_srt != map.end(); iter_srt++)

  0038d	0f b6 44 24 24	 movzx	 eax, BYTE PTR $T5[rsp]
  00392	0f b6 c0	 movzx	 eax, al
  00395	85 c0		 test	 eax, eax
  00397	0f 84 31 02 00
	00		 je	 $LN3@updateEven
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 185  :         return this->_Ptr->_Myval;

  0039d	48 8b 44 24 48	 mov	 rax, QWORD PTR iter_srt$9[rsp]
  003a2	48 83 c0 1a	 add	 rax, 26
  003a6	48 89 84 24 30
	01 00 00	 mov	 QWORD PTR $T31[rsp], rax

; 189  :         return pointer_traits<pointer>::pointer_to(**this);

  003ae	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR $T31[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  003b6	48 89 84 24 38
	01 00 00	 mov	 QWORD PTR $T32[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 528  :         return _STD addressof(_Val);

  003be	48 8b 84 24 38
	01 00 00	 mov	 rax, QWORD PTR $T32[rsp]
  003c6	48 89 84 24 40
	01 00 00	 mov	 QWORD PTR $T33[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 189  :         return pointer_traits<pointer>::pointer_to(**this);

  003ce	48 8b 84 24 40
	01 00 00	 mov	 rax, QWORD PTR $T33[rsp]
  003d6	48 89 84 24 48
	01 00 00	 mov	 QWORD PTR $T34[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 240  : 			if (isDurationOfEvent(iter_srt->second.startTime, iter_srt->second.endTime) == true)

  003de	48 8d 84 24 d0
	01 00 00	 lea	 rax, QWORD PTR $T51[rsp]
  003e6	48 8b 8c 24 48
	01 00 00	 mov	 rcx, QWORD PTR $T34[rsp]
  003ee	48 8b f8	 mov	 rdi, rax
  003f1	48 8d 71 14	 lea	 rsi, QWORD PTR [rcx+20]
  003f5	b9 10 00 00 00	 mov	 ecx, 16
  003fa	f3 a4		 rep movsb
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 185  :         return this->_Ptr->_Myval;

  003fc	48 8b 44 24 48	 mov	 rax, QWORD PTR iter_srt$9[rsp]
  00401	48 83 c0 1a	 add	 rax, 26
  00405	48 89 84 24 50
	01 00 00	 mov	 QWORD PTR $T35[rsp], rax

; 189  :         return pointer_traits<pointer>::pointer_to(**this);

  0040d	48 8b 84 24 50
	01 00 00	 mov	 rax, QWORD PTR $T35[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00415	48 89 84 24 58
	01 00 00	 mov	 QWORD PTR $T36[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 528  :         return _STD addressof(_Val);

  0041d	48 8b 84 24 58
	01 00 00	 mov	 rax, QWORD PTR $T36[rsp]
  00425	48 89 84 24 60
	01 00 00	 mov	 QWORD PTR $T37[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 189  :         return pointer_traits<pointer>::pointer_to(**this);

  0042d	48 8b 84 24 60
	01 00 00	 mov	 rax, QWORD PTR $T37[rsp]
  00435	48 89 84 24 68
	01 00 00	 mov	 QWORD PTR $T38[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 240  : 			if (isDurationOfEvent(iter_srt->second.startTime, iter_srt->second.endTime) == true)

  0043d	48 8d 84 24 e0
	01 00 00	 lea	 rax, QWORD PTR $T52[rsp]
  00445	48 8b 8c 24 68
	01 00 00	 mov	 rcx, QWORD PTR $T38[rsp]
  0044d	48 8b f8	 mov	 rdi, rax
  00450	48 8d 71 04	 lea	 rsi, QWORD PTR [rcx+4]
  00454	b9 10 00 00 00	 mov	 ecx, 16
  00459	f3 a4		 rep movsb
  0045b	4c 8d 84 24 d0
	01 00 00	 lea	 r8, QWORD PTR $T51[rsp]
  00463	48 8d 94 24 e0
	01 00 00	 lea	 rdx, QWORD PTR $T52[rsp]
  0046b	48 8b 8c 24 40
	02 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00473	e8 00 00 00 00	 call	 ?isDurationOfEvent@DailyTimeConnectionManager@mu2@@AEAA_NU_SYSTEMTIME@@0@Z ; mu2::DailyTimeConnectionManager::isDurationOfEvent
  00478	0f b6 c0	 movzx	 eax, al
  0047b	83 f8 01	 cmp	 eax, 1
  0047e	0f 85 45 01 00
	00		 jne	 $LN8@updateEven
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 185  :         return this->_Ptr->_Myval;

  00484	48 8b 44 24 48	 mov	 rax, QWORD PTR iter_srt$9[rsp]
  00489	48 83 c0 1a	 add	 rax, 26
  0048d	48 89 84 24 70
	01 00 00	 mov	 QWORD PTR $T39[rsp], rax

; 189  :         return pointer_traits<pointer>::pointer_to(**this);

  00495	48 8b 84 24 70
	01 00 00	 mov	 rax, QWORD PTR $T39[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0049d	48 89 84 24 78
	01 00 00	 mov	 QWORD PTR $T40[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 528  :         return _STD addressof(_Val);

  004a5	48 8b 84 24 78
	01 00 00	 mov	 rax, QWORD PTR $T40[rsp]
  004ad	48 89 84 24 80
	01 00 00	 mov	 QWORD PTR $T41[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 189  :         return pointer_traits<pointer>::pointer_to(**this);

  004b5	48 8b 84 24 80
	01 00 00	 mov	 rax, QWORD PTR $T41[rsp]
  004bd	48 89 84 24 90
	01 00 00	 mov	 QWORD PTR $T43[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  004c5	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A ; mu2::ISingleton<mu2::GlobalLoadScript>::inst
  004cc	48 89 84 24 88
	01 00 00	 mov	 QWORD PTR $T42[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 243  : 				m_dailyTimeConnectionRewardElem = SCRIPTS.GetDailyTimeConnectionRewardElem(iter_srt->first);

  004d4	48 8b 84 24 88
	01 00 00	 mov	 rax, QWORD PTR $T42[rsp]
  004dc	48 8b 8c 24 90
	01 00 00	 mov	 rcx, QWORD PTR $T43[rsp]
  004e4	0f b7 11	 movzx	 edx, WORD PTR [rcx]
  004e7	48 8b c8	 mov	 rcx, rax
  004ea	e8 00 00 00 00	 call	 ?GetDailyTimeConnectionRewardElem@GlobalLoadScript@mu2@@QEAAPEBUDailyTimeConnectionRewardElem@2@G@Z ; mu2::GlobalLoadScript::GetDailyTimeConnectionRewardElem
  004ef	48 8b 8c 24 40
	02 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  004f7	48 89 41 30	 mov	 QWORD PTR [rcx+48], rax

; 244  : 				if (m_dailyTimeConnectionRewardElem)

  004fb	48 8b 84 24 40
	02 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00503	48 83 78 30 00	 cmp	 QWORD PTR [rax+48], 0
  00508	0f 84 bb 00 00
	00		 je	 $LN8@updateEven
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 185  :         return this->_Ptr->_Myval;

  0050e	48 8b 44 24 48	 mov	 rax, QWORD PTR iter_srt$9[rsp]
  00513	48 83 c0 1a	 add	 rax, 26
  00517	48 89 84 24 98
	01 00 00	 mov	 QWORD PTR $T44[rsp], rax

; 189  :         return pointer_traits<pointer>::pointer_to(**this);

  0051f	48 8b 84 24 98
	01 00 00	 mov	 rax, QWORD PTR $T44[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00527	48 89 84 24 a0
	01 00 00	 mov	 QWORD PTR $T45[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 528  :         return _STD addressof(_Val);

  0052f	48 8b 84 24 a0
	01 00 00	 mov	 rax, QWORD PTR $T45[rsp]
  00537	48 89 84 24 a8
	01 00 00	 mov	 QWORD PTR $T46[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 189  :         return pointer_traits<pointer>::pointer_to(**this);

  0053f	48 8b 84 24 a8
	01 00 00	 mov	 rax, QWORD PTR $T46[rsp]
  00547	48 89 84 24 b0
	01 00 00	 mov	 QWORD PTR $T47[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 246  : 					m_dailyTimeConnectionEventElem = &iter_srt->second;

  0054f	48 8b 84 24 b0
	01 00 00	 mov	 rax, QWORD PTR $T47[rsp]
  00557	48 83 c0 02	 add	 rax, 2
  0055b	48 8b 8c 24 40
	02 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00563	48 89 41 28	 mov	 QWORD PTR [rcx+40], rax
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  00567	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A ; mu2::ISingleton<mu2::GlobalLoadScript>::inst
  0056e	48 89 84 24 b8
	01 00 00	 mov	 QWORD PTR $T48[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 248  : 					DailyComparer comparer(SCRIPTS.GetDailyDungeonRechargeTime());

  00576	48 8b 84 24 b8
	01 00 00	 mov	 rax, QWORD PTR $T48[rsp]
  0057e	48 8b c8	 mov	 rcx, rax
  00581	e8 00 00 00 00	 call	 ?GetDailyDungeonRechargeTime@GlobalLoadScript@mu2@@QEBA?BGXZ ; mu2::GlobalLoadScript::GetDailyDungeonRechargeTime
  00586	66 89 44 24 38	 mov	 WORD PTR resetHour$[rsp], ax
; File F:\Release_Branch\Shared\Protocol\Common\StructDateTimeEx.h

; 369  : 	DailyComparer(WORD resetHour) :m_resetHour(resetHour)

  0058b	0f b7 44 24 38	 movzx	 eax, WORD PTR resetHour$[rsp]
  00590	66 89 44 24 40	 mov	 WORD PTR comparer$8[rsp], ax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 249  : 					m_divisionDate = comparer.GetDailyTimeStamp();

  00595	48 8d 94 24 00
	02 00 00	 lea	 rdx, QWORD PTR $T54[rsp]
  0059d	48 8d 4c 24 40	 lea	 rcx, QWORD PTR comparer$8[rsp]
  005a2	e8 00 00 00 00	 call	 ?GetDailyTimeStamp@DailyComparer@mu2@@QEAA?AU_SYSTEMTIME@@XZ ; mu2::DailyComparer::GetDailyTimeStamp
  005a7	48 8b 8c 24 40
	02 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  005af	48 8d 79 38	 lea	 rdi, QWORD PTR [rcx+56]
  005b3	48 8b f0	 mov	 rsi, rax
  005b6	b9 10 00 00 00	 mov	 ecx, 16
  005bb	f3 a4		 rep movsb

; 250  : 
; 251  : 					return true;

  005bd	c6 44 24 25 01	 mov	 BYTE PTR $T6[rsp], 1
  005c2	0f b6 44 24 25	 movzx	 eax, BYTE PTR $T6[rsp]
  005c7	eb 07		 jmp	 SHORT $LN1@updateEven
$LN8@updateEven:

; 252  : 				}
; 253  : 			}
; 254  : 		}

  005c9	e9 e3 fc ff ff	 jmp	 $LN2@updateEven
$LN3@updateEven:

; 255  : 
; 256  : 		return false;

  005ce	32 c0		 xor	 al, al
$LN1@updateEven:

; 257  : 	}

  005d0	48 8b 8c 24 10
	02 00 00	 mov	 rcx, QWORD PTR __$ArrayPad$[rsp]
  005d8	48 33 cc	 xor	 rcx, rsp
  005db	e8 00 00 00 00	 call	 __security_check_cookie
  005e0	48 81 c4 28 02
	00 00		 add	 rsp, 552		; 00000228H
  005e7	5f		 pop	 rdi
  005e8	5e		 pop	 rsi
  005e9	c3		 ret	 0
?updateEventClose@DailyTimeConnectionManager@mu2@@AEAA_NXZ ENDP ; mu2::DailyTimeConnectionManager::updateEventClose
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 33
$T3 = 34
$T4 = 35
$T5 = 36
$T6 = 37
resetHour$ = 40
comparer$7 = 48
resetHour$ = 56
comparer$8 = 64
iter_srt$9 = 72
tv248 = 80
tv218 = 84
tv373 = 88
tv353 = 92
map$10 = 96
iter$11 = 104
map$ = 112
$T12 = 120
$T13 = 128
$T14 = 136
__param0$ = 144
_Right$ = 152
$T15 = 160
$T16 = 168
$T17 = 176
$T18 = 184
$T19 = 192
$T20 = 200
$T21 = 208
$T22 = 216
$T23 = 224
_Scary$24 = 232
__param0$ = 240
_Tmp$25 = 248
$T26 = 256
$T27 = 264
_Scary$28 = 272
__param0$ = 280
$T29 = 288
$T30 = 296
$T31 = 304
$T32 = 312
$T33 = 320
$T34 = 328
$T35 = 336
$T36 = 344
$T37 = 352
$T38 = 360
$T39 = 368
$T40 = 376
$T41 = 384
$T42 = 392
$T43 = 400
$T44 = 408
$T45 = 416
$T46 = 424
$T47 = 432
$T48 = 440
$T49 = 448
$T50 = 456
$T51 = 464
$T52 = 480
$T53 = 496
$T54 = 512
__$ArrayPad$ = 528
this$ = 576
?dtor$0@?0??updateEventClose@DailyTimeConnectionManager@mu2@@AEAA_NXZ@4HA PROC ; `mu2::DailyTimeConnectionManager::updateEventClose'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8d 4d 30	 lea	 rcx, QWORD PTR comparer$7[rbp]
  0000d	e8 00 00 00 00	 call	 ??1DailyComparer@mu2@@QEAA@XZ ; mu2::DailyComparer::~DailyComparer
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$0@?0??updateEventClose@DailyTimeConnectionManager@mu2@@AEAA_NXZ@4HA ENDP ; `mu2::DailyTimeConnectionManager::updateEventClose'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 33
$T3 = 34
$T4 = 35
$T5 = 36
$T6 = 37
resetHour$ = 40
comparer$7 = 48
resetHour$ = 56
comparer$8 = 64
iter_srt$9 = 72
tv248 = 80
tv218 = 84
tv373 = 88
tv353 = 92
map$10 = 96
iter$11 = 104
map$ = 112
$T12 = 120
$T13 = 128
$T14 = 136
__param0$ = 144
_Right$ = 152
$T15 = 160
$T16 = 168
$T17 = 176
$T18 = 184
$T19 = 192
$T20 = 200
$T21 = 208
$T22 = 216
$T23 = 224
_Scary$24 = 232
__param0$ = 240
_Tmp$25 = 248
$T26 = 256
$T27 = 264
_Scary$28 = 272
__param0$ = 280
$T29 = 288
$T30 = 296
$T31 = 304
$T32 = 312
$T33 = 320
$T34 = 328
$T35 = 336
$T36 = 344
$T37 = 352
$T38 = 360
$T39 = 368
$T40 = 376
$T41 = 384
$T42 = 392
$T43 = 400
$T44 = 408
$T45 = 416
$T46 = 424
$T47 = 432
$T48 = 440
$T49 = 448
$T50 = 456
$T51 = 464
$T52 = 480
$T53 = 496
$T54 = 512
__$ArrayPad$ = 528
this$ = 576
?dtor$1@?0??updateEventClose@DailyTimeConnectionManager@mu2@@AEAA_NXZ@4HA PROC ; `mu2::DailyTimeConnectionManager::updateEventClose'::`1'::dtor$1
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8d 4d 40	 lea	 rcx, QWORD PTR comparer$8[rbp]
  0000d	e8 00 00 00 00	 call	 ??1DailyComparer@mu2@@QEAA@XZ ; mu2::DailyComparer::~DailyComparer
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$1@?0??updateEventClose@DailyTimeConnectionManager@mu2@@AEAA_NXZ@4HA ENDP ; `mu2::DailyTimeConnectionManager::updateEventClose'::`1'::dtor$1
text$x	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EntityManager.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File F:\Release_Branch\Server\Development\Framework\Entity\Hsm.h
; File F:\Release_Branch\Server\Development\Framework\Entity\State.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File F:\Release_Branch\Server\Development\Framework\Entity\Action.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File F:\Release_Branch\Server\Development\Framework\Entity\Action.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
;	COMDAT ?updateEventOpen@DailyTimeConnectionManager@mu2@@AEAA_NXZ
_TEXT	SEGMENT
$T1 = 32
$T2 = 33
tv188 = 36
tv211 = 40
$T3 = 44
$S144$4 = 48
$T5 = 56
$S142$6 = 64
i$7 = 72
this$ = 80
elem$8 = 88
action$9 = 96
$T10 = 104
$T11 = 112
entityMap$ = 120
$T12 = 128
$T13 = 136
_Scary$14 = 144
__param0$ = 152
$S143$15 = 160
$T16 = 168
$T17 = 176
$T18 = 184
this$ = 192
$T19 = 200
$T20 = 208
$T21 = 216
$T22 = 224
$T23 = 232
this$ = 256
?updateEventOpen@DailyTimeConnectionManager@mu2@@AEAA_NXZ PROC ; mu2::DailyTimeConnectionManager::updateEventOpen, COMDAT

; 182  : 	{

$LN137:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec f8 00
	00 00		 sub	 rsp, 248		; 000000f8H

; 183  : 		// 현재날짜가 아직 이벤트기간이면 스킵한다. 
; 184  : 		if (checkEventOpen() == true)

  0000c	48 8b 8c 24 00
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00014	e8 00 00 00 00	 call	 ?checkEventOpen@DailyTimeConnectionManager@mu2@@AEAA_NXZ ; mu2::DailyTimeConnectionManager::checkEventOpen
  00019	0f b6 c0	 movzx	 eax, al
  0001c	83 f8 01	 cmp	 eax, 1
  0001f	75 21		 jne	 SHORT $LN5@updateEven

; 185  : 		{
; 186  : 			updateNextDivisonDate();

  00021	48 8b 8c 24 00
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00029	e8 00 00 00 00	 call	 ?updateNextDivisonDate@DailyTimeConnectionManager@mu2@@AEAAXXZ ; mu2::DailyTimeConnectionManager::updateNextDivisonDate

; 187  : 
; 188  : 			updatePlayers();

  0002e	48 8b 8c 24 00
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00036	e8 00 00 00 00	 call	 ?updatePlayers@DailyTimeConnectionManager@mu2@@AEAAXXZ ; mu2::DailyTimeConnectionManager::updatePlayers

; 189  : 
; 190  : 			return false;

  0003b	32 c0		 xor	 al, al
  0003d	e9 64 02 00 00	 jmp	 $LN1@updateEven
$LN5@updateEven:

; 191  : 		}
; 192  : 
; 193  : 		// 3. 이벤트 플래그 OFF
; 194  : 		m_dailyTimeConnectionEventElem = nullptr;

  00042	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0004a	48 c7 40 28 00
	00 00 00	 mov	 QWORD PTR [rax+40], 0

; 195  : 		m_dailyTimeConnectionRewardElem = nullptr;

  00052	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0005a	48 c7 40 30 00
	00 00 00	 mov	 QWORD PTR [rax+48], 0
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  00062	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VManagerEntityPlayer@mu2@@@mu2@@1VManagerEntityPlayer@2@A ; mu2::ISingleton<mu2::ManagerEntityPlayer>::inst
  00069	48 89 44 24 68	 mov	 QWORD PTR $T10[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EntityManager.h

; 154  : 	const MapPlayer& GetEntityMap() const { return m_mapPlayerSessionKey; }

  0006e	48 8b 44 24 68	 mov	 rax, QWORD PTR $T10[rsp]
  00073	48 83 c0 08	 add	 rax, 8
  00077	48 89 44 24 70	 mov	 QWORD PTR $T11[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 197  : 		auto& entityMap = theManagerPlayer.GetEntityMap();

  0007c	48 8b 44 24 70	 mov	 rax, QWORD PTR $T11[rsp]
  00081	48 89 44 24 78	 mov	 QWORD PTR entityMap$[rsp], rax

; 198  : 		for each(auto& i in entityMap)

  00086	48 8b 44 24 78	 mov	 rax, QWORD PTR entityMap$[rsp]
  0008b	48 89 44 24 40	 mov	 QWORD PTR $S142$6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00090	48 8b 44 24 40	 mov	 rax, QWORD PTR $S142$6[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00095	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  0009d	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
  000a5	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax

; 1156 :         const auto _Scary = _Get_scary();

  000ad	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T13[rsp]
  000b5	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR _Scary$14[rsp], rax

; 1157 :         return const_iterator(_Scary->_Myhead, _Scary);

  000bd	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR _Scary$14[rsp]
  000c5	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000c8	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR __param0$[rsp], rax

; 37   :     _Tree_unchecked_const_iterator(_Nodeptr _Pnode, const _Mytree* _Plist) noexcept : _Ptr(_Pnode) {

  000d0	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR __param0$[rsp]
  000d8	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $S143$15[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 198  : 		for each(auto& i in entityMap)

  000e0	48 8d 54 24 30	 lea	 rdx, QWORD PTR $S144$4[rsp]
  000e5	48 8b 4c 24 40	 mov	 rcx, QWORD PTR $S142$6[rsp]
  000ea	e8 00 00 00 00	 call	 ?begin@?$_Tree@V?$_Tmap_traits@IV?$SmartPtrEx@VEntityPlayer@mu2@@@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIV?$SmartPtrEx@VEntityPlayer@mu2@@@mu2@@@std@@@4@$0A@@std@@@std@@QEBA?AV?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$SmartPtrEx@VEntityPlayer@mu2@@@mu2@@@std@@@std@@@std@@@2@XZ ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::SmartPtrEx<mu2::EntityPlayer>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::SmartPtrEx<mu2::EntityPlayer> > >,0> >::begin
  000ef	90		 npad	 1
  000f0	eb 0b		 jmp	 SHORT $LN4@updateEven
$LN2@updateEven:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 198  :         _Mybase::operator++();

  000f2	48 8d 4c 24 30	 lea	 rcx, QWORD PTR $S144$4[rsp]
  000f7	e8 00 00 00 00	 call	 ??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$SmartPtrEx@VEntityPlayer@mu2@@@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ ; std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::SmartPtrEx<mu2::EntityPlayer> > > >,std::_Iterator_base0>::operator++
  000fc	90		 npad	 1
$LN4@updateEven:

; 232  :         return this->_Ptr == _Right._Ptr;

  000fd	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR $S143$15[rsp]
  00105	48 39 44 24 30	 cmp	 QWORD PTR $S144$4[rsp], rax
  0010a	75 0a		 jne	 SHORT $LN103@updateEven
  0010c	c7 44 24 24 01
	00 00 00	 mov	 DWORD PTR tv188[rsp], 1
  00114	eb 08		 jmp	 SHORT $LN104@updateEven
$LN103@updateEven:
  00116	c7 44 24 24 00
	00 00 00	 mov	 DWORD PTR tv188[rsp], 0
$LN104@updateEven:
  0011e	0f b6 44 24 24	 movzx	 eax, BYTE PTR tv188[rsp]
  00123	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al

; 237  :         return !(*this == _Right);

  00127	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  0012c	0f b6 c0	 movzx	 eax, al
  0012f	85 c0		 test	 eax, eax
  00131	75 0a		 jne	 SHORT $LN96@updateEven
  00133	c7 44 24 28 01
	00 00 00	 mov	 DWORD PTR tv211[rsp], 1
  0013b	eb 08		 jmp	 SHORT $LN97@updateEven
$LN96@updateEven:
  0013d	c7 44 24 28 00
	00 00 00	 mov	 DWORD PTR tv211[rsp], 0
$LN97@updateEven:
  00145	0f b6 44 24 28	 movzx	 eax, BYTE PTR tv211[rsp]
  0014a	88 44 24 21	 mov	 BYTE PTR $T2[rsp], al
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 198  : 		for each(auto& i in entityMap)

  0014e	0f b6 44 24 21	 movzx	 eax, BYTE PTR $T2[rsp]
  00153	0f b6 c0	 movzx	 eax, al
  00156	85 c0		 test	 eax, eax
  00158	0f 84 46 01 00
	00		 je	 $LN3@updateEven
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 185  :         return this->_Ptr->_Myval;

  0015e	48 8b 44 24 30	 mov	 rax, QWORD PTR $S144$4[rsp]
  00163	48 83 c0 20	 add	 rax, 32			; 00000020H
  00167	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T16[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 198  : 		for each(auto& i in entityMap)

  0016f	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T16[rsp]
  00177	48 89 44 24 48	 mov	 QWORD PTR i$7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1309 :         return _Ptr;

  0017c	48 8b 44 24 48	 mov	 rax, QWORD PTR i$7[rsp]
  00181	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00185	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T17[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h

; 186  : 		return ptr.get();

  0018d	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR $T17[rsp]
  00195	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T18[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 200  : 			if (i.second->GetHsm().GetCurrent()->GetId() == PLAYER_STATE_ZONE)

  0019d	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T18[rsp]
  001a5	48 8b c8	 mov	 rcx, rax
  001a8	e8 00 00 00 00	 call	 ?GetHsm@Entity@mu2@@QEAAAEAVHsm@2@XZ ; mu2::Entity::GetHsm
  001ad	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR this$[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Entity\Hsm.h

; 79   : 	return m_current;

  001b5	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  001bd	48 8b 40 68	 mov	 rax, QWORD PTR [rax+104]
  001c1	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T19[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Entity\State.h

; 105  : 	return m_id;

  001c9	48 8b 84 24 c8
	00 00 00	 mov	 rax, QWORD PTR $T19[rsp]
  001d1	8b 40 10	 mov	 eax, DWORD PTR [rax+16]
  001d4	89 44 24 2c	 mov	 DWORD PTR $T3[rsp], eax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 200  : 			if (i.second->GetHsm().GetCurrent()->GetId() == PLAYER_STATE_ZONE)

  001d8	8b 44 24 2c	 mov	 eax, DWORD PTR $T3[rsp]
  001dc	83 f8 05	 cmp	 eax, 5
  001df	0f 85 ba 00 00
	00		 jne	 $LN6@updateEven
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1309 :         return _Ptr;

  001e5	48 8b 44 24 48	 mov	 rax, QWORD PTR i$7[rsp]
  001ea	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  001ee	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR $T20[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h

; 191  : 		return ptr.get();

  001f6	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR $T20[rsp]
  001fe	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR $T21[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 202  : 				PlayerDailyTimeConnectionAction* action = GetEntityAction(i.second.RawPtr());

  00206	48 8b 84 24 d8
	00 00 00	 mov	 rax, QWORD PTR $T21[rsp]
; File F:\Release_Branch\Server\Development\Framework\Entity\Action.h

; 43   : 	GetEntityAction(Entity* owner) : owner_(owner) {}

  0020e	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR $T22[rsp], rax
  00216	48 8d 84 24 e0
	00 00 00	 lea	 rax, QWORD PTR $T22[rsp]
  0021e	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR $T23[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 202  : 				PlayerDailyTimeConnectionAction* action = GetEntityAction(i.second.RawPtr());

  00226	48 8b 84 24 e8
	00 00 00	 mov	 rax, QWORD PTR $T23[rsp]
  0022e	48 89 44 24 50	 mov	 QWORD PTR this$[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Entity\Action.h

; 49   : 		if ( nullptr == owner_ )

  00233	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00238	48 83 38 00	 cmp	 QWORD PTR [rax], 0
  0023c	75 0b		 jne	 SHORT $LN134@updateEven

; 50   : 		{
; 51   : 			return nullptr;

  0023e	48 c7 44 24 38
	00 00 00 00	 mov	 QWORD PTR $T5[rsp], 0
  00247	eb 34		 jmp	 SHORT $LN133@updateEven
$LN134@updateEven:

; 52   : 		}
; 53   : 
; 54   : 		Action* elem = owner_->GetAction( ActionType::ID );

  00249	ba 15 00 00 00	 mov	 edx, 21
  0024e	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00253	48 8b 08	 mov	 rcx, QWORD PTR [rax]
  00256	e8 00 00 00 00	 call	 ?GetAction@Entity@mu2@@QEBAPEAVAction@2@I@Z ; mu2::Entity::GetAction
  0025b	48 89 44 24 58	 mov	 QWORD PTR elem$8[rsp], rax

; 55   : 		if ( elem == nullptr )

  00260	48 83 7c 24 58
	00		 cmp	 QWORD PTR elem$8[rsp], 0
  00266	75 0b		 jne	 SHORT $LN135@updateEven

; 56   : 		{
; 57   : 			return nullptr;

  00268	48 c7 44 24 38
	00 00 00 00	 mov	 QWORD PTR $T5[rsp], 0
  00271	eb 0a		 jmp	 SHORT $LN133@updateEven
$LN135@updateEven:

; 58   : 		}
; 59   : 
; 60   : 		assert( dynamic_cast<ActionType*>( elem ) != nullptr );
; 61   : 		return static_cast<ActionType*>( elem );

  00273	48 8b 44 24 58	 mov	 rax, QWORD PTR elem$8[rsp]
  00278	48 89 44 24 38	 mov	 QWORD PTR $T5[rsp], rax
$LN133@updateEven:
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 202  : 				PlayerDailyTimeConnectionAction* action = GetEntityAction(i.second.RawPtr());

  0027d	48 8b 44 24 38	 mov	 rax, QWORD PTR $T5[rsp]
  00282	48 89 44 24 60	 mov	 QWORD PTR action$9[rsp], rax

; 203  : 				VALID_DO(action, continue);

  00287	48 83 7c 24 60
	00		 cmp	 QWORD PTR action$9[rsp], 0
  0028d	75 05		 jne	 SHORT $LN7@updateEven
  0028f	e9 5e fe ff ff	 jmp	 $LN2@updateEven
$LN7@updateEven:

; 204  : 				action->NotifyCloseEvent();

  00294	48 8b 4c 24 60	 mov	 rcx, QWORD PTR action$9[rsp]
  00299	e8 00 00 00 00	 call	 ?NotifyCloseEvent@PlayerDailyTimeConnectionAction@mu2@@QEAAXXZ ; mu2::PlayerDailyTimeConnectionAction::NotifyCloseEvent
  0029e	90		 npad	 1
$LN6@updateEven:

; 205  : 			}
; 206  : 		}

  0029f	e9 4e fe ff ff	 jmp	 $LN2@updateEven
$LN3@updateEven:

; 207  : 
; 208  : 		return true;

  002a4	b0 01		 mov	 al, 1
$LN1@updateEven:

; 209  : 	}

  002a6	48 81 c4 f8 00
	00 00		 add	 rsp, 248		; 000000f8H
  002ad	c3		 ret	 0
?updateEventOpen@DailyTimeConnectionManager@mu2@@AEAA_NXZ ENDP ; mu2::DailyTimeConnectionManager::updateEventOpen
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
;	COMDAT ?checkEventOpen@DailyTimeConnectionManager@mu2@@AEAA_NXZ
_TEXT	SEGMENT
$T1 = 32
$T2 = 48
this$ = 96
?checkEventOpen@DailyTimeConnectionManager@mu2@@AEAA_NXZ PROC ; mu2::DailyTimeConnectionManager::checkEventOpen, COMDAT

; 159  : 	{

$LN8:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	56		 push	 rsi
  00006	57		 push	 rdi
  00007	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 160  : 		if (m_devIndexEvent > 0)

  0000b	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00010	0f b7 40 48	 movzx	 eax, WORD PTR [rax+72]
  00014	85 c0		 test	 eax, eax
  00016	7e 2f		 jle	 SHORT $LN2@checkEvent

; 161  : 		{
; 162  : 			if (m_dailyTimeConnectionEventElem && m_devIndexEvent == m_dailyTimeConnectionEventElem->indexEvent)

  00018	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 83 78 28 00	 cmp	 QWORD PTR [rax+40], 0
  00022	74 1f		 je	 SHORT $LN3@checkEvent
  00024	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00029	0f b7 40 48	 movzx	 eax, WORD PTR [rax+72]
  0002d	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  00032	48 8b 49 28	 mov	 rcx, QWORD PTR [rcx+40]
  00036	0f b7 09	 movzx	 ecx, WORD PTR [rcx]
  00039	3b c1		 cmp	 eax, ecx
  0003b	75 06		 jne	 SHORT $LN3@checkEvent

; 163  : 			{
; 164  : 				return true;

  0003d	b0 01		 mov	 al, 1
  0003f	eb 6c		 jmp	 SHORT $LN1@checkEvent

; 165  : 			}

  00041	eb 04		 jmp	 SHORT $LN4@checkEvent
$LN3@checkEvent:

; 166  : 			else
; 167  : 			{
; 168  : 				return false;

  00043	32 c0		 xor	 al, al
  00045	eb 66		 jmp	 SHORT $LN1@checkEvent
$LN4@checkEvent:
$LN2@checkEvent:

; 169  : 			}
; 170  : 		}
; 171  : 
; 172  : 		// 현재날짜가 아직 이벤트기간이면 스킵한다. 
; 173  : 		if (m_dailyTimeConnectionEventElem)

  00047	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  0004c	48 83 78 28 00	 cmp	 QWORD PTR [rax+40], 0
  00051	74 58		 je	 SHORT $LN5@checkEvent

; 174  : 		{
; 175  : 			if (isDurationOfEvent(m_dailyTimeConnectionEventElem->startTime, m_dailyTimeConnectionEventElem->endTime) == true)

  00053	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00058	48 8b 40 28	 mov	 rax, QWORD PTR [rax+40]
  0005c	48 8d 4c 24 20	 lea	 rcx, QWORD PTR $T1[rsp]
  00061	48 8b f9	 mov	 rdi, rcx
  00064	48 8d 70 12	 lea	 rsi, QWORD PTR [rax+18]
  00068	b9 10 00 00 00	 mov	 ecx, 16
  0006d	f3 a4		 rep movsb
  0006f	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00074	48 8b 40 28	 mov	 rax, QWORD PTR [rax+40]
  00078	48 8d 4c 24 30	 lea	 rcx, QWORD PTR $T2[rsp]
  0007d	48 8b f9	 mov	 rdi, rcx
  00080	48 8d 70 02	 lea	 rsi, QWORD PTR [rax+2]
  00084	b9 10 00 00 00	 mov	 ecx, 16
  00089	f3 a4		 rep movsb
  0008b	4c 8d 44 24 20	 lea	 r8, QWORD PTR $T1[rsp]
  00090	48 8d 54 24 30	 lea	 rdx, QWORD PTR $T2[rsp]
  00095	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  0009a	e8 00 00 00 00	 call	 ?isDurationOfEvent@DailyTimeConnectionManager@mu2@@AEAA_NU_SYSTEMTIME@@0@Z ; mu2::DailyTimeConnectionManager::isDurationOfEvent
  0009f	0f b6 c0	 movzx	 eax, al
  000a2	83 f8 01	 cmp	 eax, 1
  000a5	75 04		 jne	 SHORT $LN6@checkEvent

; 176  : 				return true;

  000a7	b0 01		 mov	 al, 1
  000a9	eb 02		 jmp	 SHORT $LN1@checkEvent
$LN6@checkEvent:
$LN5@checkEvent:

; 177  : 		}
; 178  : 
; 179  : 		return false;

  000ab	32 c0		 xor	 al, al
$LN1@checkEvent:

; 180  : 	}

  000ad	48 83 c4 48	 add	 rsp, 72			; 00000048H
  000b1	5f		 pop	 rdi
  000b2	5e		 pop	 rsi
  000b3	c3		 ret	 0
?checkEventOpen@DailyTimeConnectionManager@mu2@@AEAA_NXZ ENDP ; mu2::DailyTimeConnectionManager::checkEventOpen
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\SimpleTimer.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
;	COMDAT ?checkUpdateTimer@DailyTimeConnectionManager@mu2@@AEAA_NXZ
_TEXT	SEGMENT
$T1 = 32
this$ = 64
?checkUpdateTimer@DailyTimeConnectionManager@mu2@@AEAA_NXZ PROC ; mu2::DailyTimeConnectionManager::checkUpdateTimer, COMDAT

; 147  : 	{

$LN7:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 148  : 		// 서버가 켜진상태에서 처음 업데이트는 바로 이벤트가 열려있는지 확인해주자.
; 149  : 
; 150  : 		if (m_firstUpdate == true)

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	0f b6 40 09	 movzx	 eax, BYTE PTR [rax+9]
  00012	83 f8 01	 cmp	 eax, 1
  00015	75 04		 jne	 SHORT $LN2@checkUpdat

; 151  : 			return true;

  00017	b0 01		 mov	 al, 1
  00019	eb 26		 jmp	 SHORT $LN1@checkUpdat
$LN2@checkUpdat:
; File F:\Release_Branch\Server\Development\Framework\Core\SimpleTimer.h

; 33   : 		return static_cast<Tick>(::GetTickCount64() - m_start);

  0001b	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetTickCount64
  00021	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00026	8b 49 0c	 mov	 ecx, DWORD PTR [rcx+12]
  00029	48 2b c1	 sub	 rax, rcx
  0002c	89 44 24 20	 mov	 DWORD PTR $T1[rsp], eax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 153  : 		if (m_updateTimer.Elapsed() > UPDATE_TIMER)

  00030	8b 44 24 20	 mov	 eax, DWORD PTR $T1[rsp]
  00034	3d e8 03 00 00	 cmp	 eax, 1000		; 000003e8H
  00039	76 04		 jbe	 SHORT $LN3@checkUpdat

; 154  : 			return true;

  0003b	b0 01		 mov	 al, 1
  0003d	eb 02		 jmp	 SHORT $LN1@checkUpdat
$LN3@checkUpdat:

; 155  : 
; 156  : 		return false;

  0003f	32 c0		 xor	 al, al
$LN1@checkUpdat:

; 157  : 	}

  00041	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00045	c3		 ret	 0
?checkUpdateTimer@DailyTimeConnectionManager@mu2@@AEAA_NXZ ENDP ; mu2::DailyTimeConnectionManager::checkUpdateTimer
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
;	COMDAT ?isDurationOfEvent@DailyTimeConnectionManager@mu2@@AEAA_NU_SYSTEMTIME@@0@Z
_TEXT	SEGMENT
startDiff$ = 32
endDiff$ = 36
curFileTime$ = 40
startFileTime$ = 48
endFileTime$ = 56
curTime$ = 64
__$ArrayPad$ = 80
this$ = 112
startTime$ = 120
endTime$ = 128
?isDurationOfEvent@DailyTimeConnectionManager@mu2@@AEAA_NU_SYSTEMTIME@@0@Z PROC ; mu2::DailyTimeConnectionManager::isDurationOfEvent, COMDAT

; 77   : 	{

$LN4:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 68	 sub	 rsp, 104		; 00000068H
  00013	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR __security_cookie
  0001a	48 33 c4	 xor	 rax, rsp
  0001d	48 89 44 24 50	 mov	 QWORD PTR __$ArrayPad$[rsp], rax

; 78   : 		SYSTEMTIME curTime;
; 79   : 		FILETIME   curFileTime;
; 80   : 		GetLocalTime(&curTime);

  00022	48 8d 4c 24 40	 lea	 rcx, QWORD PTR curTime$[rsp]
  00027	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetLocalTime

; 81   : 		SystemTimeToFileTime(&curTime, &curFileTime);

  0002d	48 8d 54 24 28	 lea	 rdx, QWORD PTR curFileTime$[rsp]
  00032	48 8d 4c 24 40	 lea	 rcx, QWORD PTR curTime$[rsp]
  00037	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SystemTimeToFileTime

; 82   : 
; 83   : 		FILETIME   startFileTime;
; 84   : 		SystemTimeToFileTime(&startTime, &startFileTime);

  0003d	48 8d 54 24 30	 lea	 rdx, QWORD PTR startFileTime$[rsp]
  00042	48 8b 4c 24 78	 mov	 rcx, QWORD PTR startTime$[rsp]
  00047	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SystemTimeToFileTime

; 85   : 
; 86   : 		FILETIME   endFileTime;
; 87   : 		SystemTimeToFileTime(&endTime, &endFileTime);

  0004d	48 8d 54 24 38	 lea	 rdx, QWORD PTR endFileTime$[rsp]
  00052	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR endTime$[rsp]
  0005a	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SystemTimeToFileTime

; 88   : 
; 89   : 		long startDiff = CompareFileTime(&curFileTime, &startFileTime);

  00060	48 8d 54 24 30	 lea	 rdx, QWORD PTR startFileTime$[rsp]
  00065	48 8d 4c 24 28	 lea	 rcx, QWORD PTR curFileTime$[rsp]
  0006a	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_CompareFileTime
  00070	89 44 24 20	 mov	 DWORD PTR startDiff$[rsp], eax

; 90   : 		long endDiff = CompareFileTime(&curFileTime, &endFileTime);

  00074	48 8d 54 24 38	 lea	 rdx, QWORD PTR endFileTime$[rsp]
  00079	48 8d 4c 24 28	 lea	 rcx, QWORD PTR curFileTime$[rsp]
  0007e	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_CompareFileTime
  00084	89 44 24 24	 mov	 DWORD PTR endDiff$[rsp], eax

; 91   : 
; 92   : 		// 현재시간이 이벤트시작시간보다 크고, 현재시간이 이벤트 종료시간보다 작을 경우 
; 93   : 		// 현재시간은 이벤트 기간이다.
; 94   : 		if (startDiff >= 0 && endDiff == -1)

  00088	83 7c 24 20 00	 cmp	 DWORD PTR startDiff$[rsp], 0
  0008d	7c 0b		 jl	 SHORT $LN2@isDuration
  0008f	83 7c 24 24 ff	 cmp	 DWORD PTR endDiff$[rsp], -1
  00094	75 04		 jne	 SHORT $LN2@isDuration

; 95   : 			return true;

  00096	b0 01		 mov	 al, 1
  00098	eb 02		 jmp	 SHORT $LN1@isDuration
$LN2@isDuration:

; 96   : 
; 97   : 		return false;

  0009a	32 c0		 xor	 al, al
$LN1@isDuration:

; 98   : 	}

  0009c	48 8b 4c 24 50	 mov	 rcx, QWORD PTR __$ArrayPad$[rsp]
  000a1	48 33 cc	 xor	 rcx, rsp
  000a4	e8 00 00 00 00	 call	 __security_check_cookie
  000a9	48 83 c4 68	 add	 rsp, 104		; 00000068H
  000ad	c3		 ret	 0
?isDurationOfEvent@DailyTimeConnectionManager@mu2@@AEAA_NU_SYSTEMTIME@@0@Z ENDP ; mu2::DailyTimeConnectionManager::isDurationOfEvent
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\SimpleTimer.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\SimpleTimer.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
;	COMDAT ??0DailyTimeConnectionManager@mu2@@AEAA@XZ
_TEXT	SEGMENT
this$ = 32
this$ = 40
this$ = 64
??0DailyTimeConnectionManager@mu2@@AEAA@XZ PROC		; mu2::DailyTimeConnectionManager::DailyTimeConnectionManager, COMDAT

; 12   : 	{

$LN10:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 84   : 	ISingleton() {}	

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 12   : 	{

  00018	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7DailyTimeConnectionManager@mu2@@6B@
  00024	48 89 08	 mov	 QWORD PTR [rax], rcx
  00027	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0002c	48 83 c0 0c	 add	 rax, 12
  00030	48 89 44 24 20	 mov	 QWORD PTR this$[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\SimpleTimer.h

; 19   : 		, m_start(static_cast<Tick>(::GetTickCount64()))

  00035	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetTickCount64
  0003b	48 8b 4c 24 20	 mov	 rcx, QWORD PTR this$[rsp]
  00040	89 01		 mov	 DWORD PTR [rcx], eax

; 17   : 		: m_msInterval(msInterval)

  00042	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00047	c7 40 04 00 00
	00 00		 mov	 DWORD PTR [rax+4], 0

; 18   : 		, m_isInvoked(0)

  0004e	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00053	c6 40 08 00	 mov	 BYTE PTR [rax+8], 0
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 12   : 	{

  00057	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0005c	48 83 c0 18	 add	 rax, 24
  00060	48 89 44 24 28	 mov	 QWORD PTR this$[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\SimpleTimer.h

; 19   : 		, m_start(static_cast<Tick>(::GetTickCount64()))

  00065	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetTickCount64
  0006b	48 8b 4c 24 28	 mov	 rcx, QWORD PTR this$[rsp]
  00070	89 01		 mov	 DWORD PTR [rcx], eax

; 17   : 		: m_msInterval(msInterval)

  00072	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00077	c7 40 04 00 00
	00 00		 mov	 DWORD PTR [rax+4], 0

; 18   : 		, m_isInvoked(0)

  0007e	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00083	c6 40 08 00	 mov	 BYTE PTR [rax+8], 0
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 13   : 		m_DBOpen = false;

  00087	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0008c	c6 40 08 00	 mov	 BYTE PTR [rax+8], 0

; 14   : 		m_firstUpdate = true;

  00090	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00095	c6 40 09 01	 mov	 BYTE PTR [rax+9], 1

; 15   : 	}

  00099	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0009e	48 83 c4 38	 add	 rsp, 56			; 00000038H
  000a2	c3		 ret	 0
??0DailyTimeConnectionManager@mu2@@AEAA@XZ ENDP		; mu2::DailyTimeConnectionManager::DailyTimeConnectionManager
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EntityManager.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File F:\Release_Branch\Server\Development\Framework\Entity\Action.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File F:\Release_Branch\Server\Development\Framework\Entity\Action.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\SimpleTimer.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
;	COMDAT ?Update@DailyTimeConnectionManager@mu2@@QEAAXXZ
_TEXT	SEGMENT
isOpen$1 = 32
$T2 = 33
$T3 = 34
tv185 = 36
tv208 = 40
$S147$4 = 48
$T5 = 56
$S145$6 = 64
this$ = 72
elem$7 = 80
action$8 = 88
this$ = 96
$T9 = 104
$T10 = 112
entityMap$11 = 120
$T12 = 128
$T13 = 136
_Scary$14 = 144
__param0$ = 152
$S146$15 = 160
$T16 = 168
i$17 = 176
$T18 = 184
$T19 = 192
$T20 = 200
$T21 = 208
this$ = 240
?Update@DailyTimeConnectionManager@mu2@@QEAAXXZ PROC	; mu2::DailyTimeConnectionManager::Update, COMDAT

; 259  : 	{

$LN131:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec e8 00
	00 00		 sub	 rsp, 232		; 000000e8H

; 260  : 		if (m_DBOpen == false)

  0000c	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00014	0f b6 40 08	 movzx	 eax, BYTE PTR [rax+8]
  00018	85 c0		 test	 eax, eax
  0001a	75 05		 jne	 SHORT $LN5@Update

; 261  : 			return;

  0001c	e9 71 02 00 00	 jmp	 $LN1@Update
$LN5@Update:

; 262  : 
; 263  : 		if (checkUpdateTimer() == false)

  00021	48 8b 8c 24 f0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00029	e8 00 00 00 00	 call	 ?checkUpdateTimer@DailyTimeConnectionManager@mu2@@AEAA_NXZ ; mu2::DailyTimeConnectionManager::checkUpdateTimer
  0002e	0f b6 c0	 movzx	 eax, al
  00031	85 c0		 test	 eax, eax
  00033	75 05		 jne	 SHORT $LN6@Update

; 264  : 			return;

  00035	e9 58 02 00 00	 jmp	 $LN1@Update
$LN6@Update:

; 265  : 
; 266  : 
; 267  : 		if (IsOpenEvent() == false)

  0003a	48 8b 8c 24 f0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00042	e8 00 00 00 00	 call	 ?IsOpenEvent@DailyTimeConnectionManager@mu2@@QEAA_NXZ ; mu2::DailyTimeConnectionManager::IsOpenEvent
  00047	0f b6 c0	 movzx	 eax, al
  0004a	85 c0		 test	 eax, eax
  0004c	0f 85 ff 01 00
	00		 jne	 $LN7@Update

; 268  : 		{
; 269  : 			Bool isOpen = updateEventClose();

  00052	48 8b 8c 24 f0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0005a	e8 00 00 00 00	 call	 ?updateEventClose@DailyTimeConnectionManager@mu2@@AEAA_NXZ ; mu2::DailyTimeConnectionManager::updateEventClose
  0005f	88 44 24 20	 mov	 BYTE PTR isOpen$1[rsp], al

; 270  : 
; 271  : 			if (isOpen)

  00063	0f b6 44 24 20	 movzx	 eax, BYTE PTR isOpen$1[rsp]
  00068	85 c0		 test	 eax, eax
  0006a	0f 84 df 01 00
	00		 je	 $LN9@Update
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  00070	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VManagerEntityPlayer@mu2@@@mu2@@1VManagerEntityPlayer@2@A ; mu2::ISingleton<mu2::ManagerEntityPlayer>::inst
  00077	48 89 44 24 68	 mov	 QWORD PTR $T9[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EntityManager.h

; 154  : 	const MapPlayer& GetEntityMap() const { return m_mapPlayerSessionKey; }

  0007c	48 8b 44 24 68	 mov	 rax, QWORD PTR $T9[rsp]
  00081	48 83 c0 08	 add	 rax, 8
  00085	48 89 44 24 70	 mov	 QWORD PTR $T10[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 273  : 				auto& entityMap = theManagerPlayer.GetEntityMap();

  0008a	48 8b 44 24 70	 mov	 rax, QWORD PTR $T10[rsp]
  0008f	48 89 44 24 78	 mov	 QWORD PTR entityMap$11[rsp], rax

; 274  : 				for each(auto& i in entityMap)

  00094	48 8b 44 24 78	 mov	 rax, QWORD PTR entityMap$11[rsp]
  00099	48 89 44 24 40	 mov	 QWORD PTR $S145$6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  0009e	48 8b 44 24 40	 mov	 rax, QWORD PTR $S145$6[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  000a3	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  000ab	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
  000b3	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax

; 1156 :         const auto _Scary = _Get_scary();

  000bb	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T13[rsp]
  000c3	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR _Scary$14[rsp], rax

; 1157 :         return const_iterator(_Scary->_Myhead, _Scary);

  000cb	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR _Scary$14[rsp]
  000d3	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000d6	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR __param0$[rsp], rax

; 37   :     _Tree_unchecked_const_iterator(_Nodeptr _Pnode, const _Mytree* _Plist) noexcept : _Ptr(_Pnode) {

  000de	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR __param0$[rsp]
  000e6	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $S146$15[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 274  : 				for each(auto& i in entityMap)

  000ee	48 8d 54 24 30	 lea	 rdx, QWORD PTR $S147$4[rsp]
  000f3	48 8b 4c 24 40	 mov	 rcx, QWORD PTR $S145$6[rsp]
  000f8	e8 00 00 00 00	 call	 ?begin@?$_Tree@V?$_Tmap_traits@IV?$SmartPtrEx@VEntityPlayer@mu2@@@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIV?$SmartPtrEx@VEntityPlayer@mu2@@@mu2@@@std@@@4@$0A@@std@@@std@@QEBA?AV?$_Tree_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$SmartPtrEx@VEntityPlayer@mu2@@@mu2@@@std@@@std@@@std@@@2@XZ ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::SmartPtrEx<mu2::EntityPlayer>,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::SmartPtrEx<mu2::EntityPlayer> > >,0> >::begin
  000fd	90		 npad	 1
  000fe	eb 0b		 jmp	 SHORT $LN4@Update
$LN2@Update:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 198  :         _Mybase::operator++();

  00100	48 8d 4c 24 30	 lea	 rcx, QWORD PTR $S147$4[rsp]
  00105	e8 00 00 00 00	 call	 ??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIV?$SmartPtrEx@VEntityPlayer@mu2@@@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ ; std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::SmartPtrEx<mu2::EntityPlayer> > > >,std::_Iterator_base0>::operator++
  0010a	90		 npad	 1
$LN4@Update:

; 232  :         return this->_Ptr == _Right._Ptr;

  0010b	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR $S146$15[rsp]
  00113	48 39 44 24 30	 cmp	 QWORD PTR $S147$4[rsp], rax
  00118	75 0a		 jne	 SHORT $LN106@Update
  0011a	c7 44 24 24 01
	00 00 00	 mov	 DWORD PTR tv185[rsp], 1
  00122	eb 08		 jmp	 SHORT $LN107@Update
$LN106@Update:
  00124	c7 44 24 24 00
	00 00 00	 mov	 DWORD PTR tv185[rsp], 0
$LN107@Update:
  0012c	0f b6 44 24 24	 movzx	 eax, BYTE PTR tv185[rsp]
  00131	88 44 24 21	 mov	 BYTE PTR $T2[rsp], al

; 237  :         return !(*this == _Right);

  00135	0f b6 44 24 21	 movzx	 eax, BYTE PTR $T2[rsp]
  0013a	0f b6 c0	 movzx	 eax, al
  0013d	85 c0		 test	 eax, eax
  0013f	75 0a		 jne	 SHORT $LN99@Update
  00141	c7 44 24 28 01
	00 00 00	 mov	 DWORD PTR tv208[rsp], 1
  00149	eb 08		 jmp	 SHORT $LN100@Update
$LN99@Update:
  0014b	c7 44 24 28 00
	00 00 00	 mov	 DWORD PTR tv208[rsp], 0
$LN100@Update:
  00153	0f b6 44 24 28	 movzx	 eax, BYTE PTR tv208[rsp]
  00158	88 44 24 22	 mov	 BYTE PTR $T3[rsp], al
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 274  : 				for each(auto& i in entityMap)

  0015c	0f b6 44 24 22	 movzx	 eax, BYTE PTR $T3[rsp]
  00161	0f b6 c0	 movzx	 eax, al
  00164	85 c0		 test	 eax, eax
  00166	0f 84 e3 00 00
	00		 je	 $LN9@Update
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 185  :         return this->_Ptr->_Myval;

  0016c	48 8b 44 24 30	 mov	 rax, QWORD PTR $S147$4[rsp]
  00171	48 83 c0 20	 add	 rax, 32			; 00000020H
  00175	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T16[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 274  : 				for each(auto& i in entityMap)

  0017d	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T16[rsp]
  00185	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR i$17[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1309 :         return _Ptr;

  0018d	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR i$17[rsp]
  00195	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00199	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T18[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h

; 191  : 		return ptr.get();

  001a1	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T18[rsp]
  001a9	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T19[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 276  : 					PlayerDailyTimeConnectionAction* action = GetEntityAction(i.second.RawPtr());

  001b1	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR $T19[rsp]
; File F:\Release_Branch\Server\Development\Framework\Entity\Action.h

; 43   : 	GetEntityAction(Entity* owner) : owner_(owner) {}

  001b9	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T20[rsp], rax
  001c1	48 8d 84 24 c8
	00 00 00	 lea	 rax, QWORD PTR $T20[rsp]
  001c9	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR $T21[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 276  : 					PlayerDailyTimeConnectionAction* action = GetEntityAction(i.second.RawPtr());

  001d1	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR $T21[rsp]
  001d9	48 89 44 24 48	 mov	 QWORD PTR this$[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Entity\Action.h

; 49   : 		if ( nullptr == owner_ )

  001de	48 8b 44 24 48	 mov	 rax, QWORD PTR this$[rsp]
  001e3	48 83 38 00	 cmp	 QWORD PTR [rax], 0
  001e7	75 0b		 jne	 SHORT $LN126@Update

; 50   : 		{
; 51   : 			return nullptr;

  001e9	48 c7 44 24 38
	00 00 00 00	 mov	 QWORD PTR $T5[rsp], 0
  001f2	eb 34		 jmp	 SHORT $LN125@Update
$LN126@Update:

; 52   : 		}
; 53   : 
; 54   : 		Action* elem = owner_->GetAction( ActionType::ID );

  001f4	ba 15 00 00 00	 mov	 edx, 21
  001f9	48 8b 44 24 48	 mov	 rax, QWORD PTR this$[rsp]
  001fe	48 8b 08	 mov	 rcx, QWORD PTR [rax]
  00201	e8 00 00 00 00	 call	 ?GetAction@Entity@mu2@@QEBAPEAVAction@2@I@Z ; mu2::Entity::GetAction
  00206	48 89 44 24 50	 mov	 QWORD PTR elem$7[rsp], rax

; 55   : 		if ( elem == nullptr )

  0020b	48 83 7c 24 50
	00		 cmp	 QWORD PTR elem$7[rsp], 0
  00211	75 0b		 jne	 SHORT $LN127@Update

; 56   : 		{
; 57   : 			return nullptr;

  00213	48 c7 44 24 38
	00 00 00 00	 mov	 QWORD PTR $T5[rsp], 0
  0021c	eb 0a		 jmp	 SHORT $LN125@Update
$LN127@Update:

; 58   : 		}
; 59   : 
; 60   : 		assert( dynamic_cast<ActionType*>( elem ) != nullptr );
; 61   : 		return static_cast<ActionType*>( elem );

  0021e	48 8b 44 24 50	 mov	 rax, QWORD PTR elem$7[rsp]
  00223	48 89 44 24 38	 mov	 QWORD PTR $T5[rsp], rax
$LN125@Update:
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 276  : 					PlayerDailyTimeConnectionAction* action = GetEntityAction(i.second.RawPtr());

  00228	48 8b 44 24 38	 mov	 rax, QWORD PTR $T5[rsp]
  0022d	48 89 44 24 58	 mov	 QWORD PTR action$8[rsp], rax

; 277  : 					VALID_DO(action, continue);

  00232	48 83 7c 24 58
	00		 cmp	 QWORD PTR action$8[rsp], 0
  00238	75 05		 jne	 SHORT $LN10@Update
  0023a	e9 c1 fe ff ff	 jmp	 $LN2@Update
$LN10@Update:

; 278  : 					action->NotifyOpenEvent();

  0023f	48 8b 4c 24 58	 mov	 rcx, QWORD PTR action$8[rsp]
  00244	e8 00 00 00 00	 call	 ?NotifyOpenEvent@PlayerDailyTimeConnectionAction@mu2@@QEAAXXZ ; mu2::PlayerDailyTimeConnectionAction::NotifyOpenEvent
  00249	90		 npad	 1

; 279  : 				}

  0024a	e9 b1 fe ff ff	 jmp	 $LN2@Update
$LN9@Update:

; 280  : 			}
; 281  : 		}

  0024f	eb 0e		 jmp	 SHORT $LN8@Update
$LN7@Update:

; 282  : 		else
; 283  : 		{
; 284  : 			updateEventOpen();

  00251	48 8b 8c 24 f0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00259	e8 00 00 00 00	 call	 ?updateEventOpen@DailyTimeConnectionManager@mu2@@AEAA_NXZ ; mu2::DailyTimeConnectionManager::updateEventOpen
  0025e	90		 npad	 1
$LN8@Update:

; 285  : 		}
; 286  : 
; 287  : 		m_firstUpdate = false;

  0025f	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00267	c6 40 09 00	 mov	 BYTE PTR [rax+9], 0

; 288  : 
; 289  : 		m_updateTimer.Reset();

  0026b	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00273	48 83 c0 0c	 add	 rax, 12
  00277	48 89 44 24 60	 mov	 QWORD PTR this$[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\SimpleTimer.h

; 38   : 		m_start = static_cast<Tick>(::GetTickCount64());

  0027c	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetTickCount64
  00282	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  00287	89 01		 mov	 DWORD PTR [rcx], eax

; 39   : 		m_isInvoked = false;

  00289	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  0028e	c6 40 08 00	 mov	 BYTE PTR [rax+8], 0
$LN1@Update:
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 292  : 	}

  00292	48 81 c4 e8 00
	00 00		 add	 rsp, 232		; 000000e8H
  00299	c3		 ret	 0
?Update@DailyTimeConnectionManager@mu2@@QEAAXXZ ENDP	; mu2::DailyTimeConnectionManager::Update
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
;	COMDAT ?GetEventDate@DailyTimeConnectionManager@mu2@@QEAA_NAEAU_SYSTEMTIME@@0@Z
_TEXT	SEGMENT
this$ = 24
start$ = 32
end$ = 40
?GetEventDate@DailyTimeConnectionManager@mu2@@QEAA_NAEAU_SYSTEMTIME@@0@Z PROC ; mu2::DailyTimeConnectionManager::GetEventDate, COMDAT

; 64   : 	{

$LN4:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	56		 push	 rsi
  00010	57		 push	 rdi

; 65   : 		if (m_dailyTimeConnectionEventElem == nullptr)

  00011	48 8b 44 24 18	 mov	 rax, QWORD PTR this$[rsp]
  00016	48 83 78 28 00	 cmp	 QWORD PTR [rax+40], 0
  0001b	75 04		 jne	 SHORT $LN2@GetEventDa

; 66   : 			return false;

  0001d	32 c0		 xor	 al, al
  0001f	eb 34		 jmp	 SHORT $LN1@GetEventDa
$LN2@GetEventDa:

; 67   : 
; 68   : 		start = m_dailyTimeConnectionEventElem->startTime;

  00021	48 8b 44 24 18	 mov	 rax, QWORD PTR this$[rsp]
  00026	48 8b 40 28	 mov	 rax, QWORD PTR [rax+40]
  0002a	48 8b 7c 24 20	 mov	 rdi, QWORD PTR start$[rsp]
  0002f	48 8d 70 02	 lea	 rsi, QWORD PTR [rax+2]
  00033	b9 10 00 00 00	 mov	 ecx, 16
  00038	f3 a4		 rep movsb

; 69   : 		end = m_dailyTimeConnectionEventElem->endTime;

  0003a	48 8b 44 24 18	 mov	 rax, QWORD PTR this$[rsp]
  0003f	48 8b 40 28	 mov	 rax, QWORD PTR [rax+40]
  00043	48 8b 7c 24 28	 mov	 rdi, QWORD PTR end$[rsp]
  00048	48 8d 70 12	 lea	 rsi, QWORD PTR [rax+18]
  0004c	b9 10 00 00 00	 mov	 ecx, 16
  00051	f3 a4		 rep movsb

; 70   : 
; 71   : 		return true;

  00053	b0 01		 mov	 al, 1
$LN1@GetEventDa:

; 72   : 	}

  00055	5f		 pop	 rdi
  00056	5e		 pop	 rsi
  00057	c3		 ret	 0
?GetEventDate@DailyTimeConnectionManager@mu2@@QEAA_NAEAU_SYSTEMTIME@@0@Z ENDP ; mu2::DailyTimeConnectionManager::GetEventDate
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
;	COMDAT ?GetDivisionDate@DailyTimeConnectionManager@mu2@@QEAA?AU_SYSTEMTIME@@XZ
_TEXT	SEGMENT
this$ = 24
__$ReturnUdt$ = 32
?GetDivisionDate@DailyTimeConnectionManager@mu2@@QEAA?AU_SYSTEMTIME@@XZ PROC ; mu2::DailyTimeConnectionManager::GetDivisionDate, COMDAT

; 59   : 	{

$LN3:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	56		 push	 rsi
  0000b	57		 push	 rdi

; 60   : 		return m_divisionDate;

  0000c	48 8b 44 24 18	 mov	 rax, QWORD PTR this$[rsp]
  00011	48 8b 7c 24 20	 mov	 rdi, QWORD PTR __$ReturnUdt$[rsp]
  00016	48 8d 70 38	 lea	 rsi, QWORD PTR [rax+56]
  0001a	b9 10 00 00 00	 mov	 ecx, 16
  0001f	f3 a4		 rep movsb
  00021	48 8b 44 24 20	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]

; 61   : 	}

  00026	5f		 pop	 rdi
  00027	5e		 pop	 rsi
  00028	c3		 ret	 0
?GetDivisionDate@DailyTimeConnectionManager@mu2@@QEAA?AU_SYSTEMTIME@@XZ ENDP ; mu2::DailyTimeConnectionManager::GetDivisionDate
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
;	COMDAT ?GetEventIndex@DailyTimeConnectionManager@mu2@@QEAAGXZ
_TEXT	SEGMENT
this$ = 8
?GetEventIndex@DailyTimeConnectionManager@mu2@@QEAAGXZ PROC ; mu2::DailyTimeConnectionManager::GetEventIndex, COMDAT

; 51   : 	{

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 52   : 		if (m_dailyTimeConnectionEventElem == nullptr)

  00005	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0000a	48 83 78 28 00	 cmp	 QWORD PTR [rax+40], 0
  0000f	75 04		 jne	 SHORT $LN2@GetEventIn

; 53   : 			return 0;

  00011	33 c0		 xor	 eax, eax
  00013	eb 0c		 jmp	 SHORT $LN1@GetEventIn
$LN2@GetEventIn:

; 54   : 
; 55   : 		return m_dailyTimeConnectionEventElem->indexEvent;

  00015	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 8b 40 28	 mov	 rax, QWORD PTR [rax+40]
  0001e	0f b7 00	 movzx	 eax, WORD PTR [rax]
$LN1@GetEventIn:

; 56   : 	}

  00021	c3		 ret	 0
?GetEventIndex@DailyTimeConnectionManager@mu2@@QEAAGXZ ENDP ; mu2::DailyTimeConnectionManager::GetEventIndex
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
;	COMDAT ?IsOpenEvent@DailyTimeConnectionManager@mu2@@QEAA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsOpenEvent@DailyTimeConnectionManager@mu2@@QEAA_NXZ PROC ; mu2::DailyTimeConnectionManager::IsOpenEvent, COMDAT

; 40   : 	{

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 41   : 		if (m_dailyTimeConnectionEventElem == nullptr ||

  00005	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0000a	48 83 78 28 00	 cmp	 QWORD PTR [rax+40], 0
  0000f	74 0c		 je	 SHORT $LN3@IsOpenEven
  00011	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00016	48 83 78 30 00	 cmp	 QWORD PTR [rax+48], 0
  0001b	75 04		 jne	 SHORT $LN2@IsOpenEven
$LN3@IsOpenEven:

; 42   : 			m_dailyTimeConnectionRewardElem == nullptr)
; 43   : 		{
; 44   : 			return false;

  0001d	32 c0		 xor	 al, al
  0001f	eb 02		 jmp	 SHORT $LN1@IsOpenEven
$LN2@IsOpenEven:

; 45   : 		}
; 46   : 
; 47   : 		return true;

  00021	b0 01		 mov	 al, 1
$LN1@IsOpenEven:

; 48   : 	}

  00023	c3		 ret	 0
?IsOpenEvent@DailyTimeConnectionManager@mu2@@QEAA_NXZ ENDP ; mu2::DailyTimeConnectionManager::IsOpenEvent
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
;	COMDAT ?InitDbOpen@DailyTimeConnectionManager@mu2@@QEAAXXZ
_TEXT	SEGMENT
this$ = 8
?InitDbOpen@DailyTimeConnectionManager@mu2@@QEAAXXZ PROC ; mu2::DailyTimeConnectionManager::InitDbOpen, COMDAT

; 34   : 	{

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 35   : 		m_DBOpen = true;

  00005	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0000a	c6 40 08 01	 mov	 BYTE PTR [rax+8], 1

; 36   : 	}

  0000e	c3		 ret	 0
?InitDbOpen@DailyTimeConnectionManager@mu2@@QEAAXXZ ENDP ; mu2::DailyTimeConnectionManager::InitDbOpen
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
;	COMDAT ?UnInit@DailyTimeConnectionManager@mu2@@UEAA_NXZ
_TEXT	SEGMENT
this$ = 8
?UnInit@DailyTimeConnectionManager@mu2@@UEAA_NXZ PROC	; mu2::DailyTimeConnectionManager::UnInit, COMDAT

; 28   : 	{

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 29   : 		return true;

  00005	b0 01		 mov	 al, 1

; 30   : 	}

  00007	c3		 ret	 0
?UnInit@DailyTimeConnectionManager@mu2@@UEAA_NXZ ENDP	; mu2::DailyTimeConnectionManager::UnInit
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
;	COMDAT ?Init@DailyTimeConnectionManager@mu2@@UEAA_NPEAX@Z
_TEXT	SEGMENT
this$ = 8
param$ = 16
?Init@DailyTimeConnectionManager@mu2@@UEAA_NPEAX@Z PROC	; mu2::DailyTimeConnectionManager::Init, COMDAT

; 22   : 	{

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 23   : 		UNREFERENCED_PARAMETER(param);
; 24   : 		return true;

  0000a	b0 01		 mov	 al, 1

; 25   : 	}

  0000c	c3		 ret	 0
?Init@DailyTimeConnectionManager@mu2@@UEAA_NPEAX@Z ENDP	; mu2::DailyTimeConnectionManager::Init
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
;	COMDAT ??1DailyTimeConnectionManager@mu2@@UEAA@XZ
_TEXT	SEGMENT
this$ = 8
??1DailyTimeConnectionManager@mu2@@UEAA@XZ PROC		; mu2::DailyTimeConnectionManager::~DailyTimeConnectionManager, COMDAT

; 18   : 	{

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0000a	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7DailyTimeConnectionManager@mu2@@6B@
  00011	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 80   : 	virtual~ISingleton() {}

  00014	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00019	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@6B@
  00020	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp

; 19   : 	}

  00023	c3		 ret	 0
??1DailyTimeConnectionManager@mu2@@UEAA@XZ ENDP		; mu2::DailyTimeConnectionManager::~DailyTimeConnectionManager
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??_G?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_G?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@UEAAPEAXI@Z PROC ; mu2::ISingleton<mu2::DailyTimeConnectionManager>::`scalar deleting destructor', COMDAT
$LN10:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 80   : 	virtual~ISingleton() {}

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx
  0001c	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00020	83 e0 01	 and	 eax, 1
  00023	85 c0		 test	 eax, eax
  00025	74 10		 je	 SHORT $LN2@scalar
  00027	ba 08 00 00 00	 mov	 edx, 8
  0002c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00031	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00036	90		 npad	 1
$LN2@scalar:
  00037	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003c	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00040	c3		 ret	 0
??_G?$ISingleton@VDailyTimeConnectionManager@mu2@@@mu2@@UEAAPEAXI@Z ENDP ; mu2::ISingleton<mu2::DailyTimeConnectionManager>::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Shared\Protocol\Common\StructDateTimeEx.h
;	COMDAT ?CompareDailyTimeStamp@DailyComparer@mu2@@QEAA_NAEBU_SYSTEMTIME@@@Z
_TEXT	SEGMENT
microSecond$1 = 32
$T2 = 40
microSecond$3 = 48
$T4 = 56
compareDate_$ = 64
dailyTimeStamp_$ = 72
$T5 = 80
$T6 = 96
dailyTimeStamp$ = 112
__$ArrayPad$ = 128
this$ = 176
compareDate$ = 184
?CompareDailyTimeStamp@DailyComparer@mu2@@QEAA_NAEBU_SYSTEMTIME@@@Z PROC ; mu2::DailyComparer::CompareDailyTimeStamp, COMDAT

; 436  : 	{

$LN25:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	56		 push	 rsi
  0000b	57		 push	 rdi
  0000c	48 81 ec 98 00
	00 00		 sub	 rsp, 152		; 00000098H
  00013	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR __security_cookie
  0001a	48 33 c4	 xor	 rax, rsp
  0001d	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR __$ArrayPad$[rsp], rax

; 437  : 		SYSTEMTIME dailyTimeStamp = GetDailyTimeStamp();

  00025	48 8d 54 24 70	 lea	 rdx, QWORD PTR dailyTimeStamp$[rsp]
  0002a	48 8b 8c 24 b0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00032	e8 00 00 00 00	 call	 ?GetDailyTimeStamp@DailyComparer@mu2@@QEAA?AU_SYSTEMTIME@@XZ ; mu2::DailyComparer::GetDailyTimeStamp

; 438  : 
; 439  : 		ULONGLONG dailyTimeStamp_ = getMicroSecond(dailyTimeStamp);

  00037	48 8d 44 24 50	 lea	 rax, QWORD PTR $T5[rsp]
  0003c	48 8d 4c 24 70	 lea	 rcx, QWORD PTR dailyTimeStamp$[rsp]
  00041	48 8b f8	 mov	 rdi, rax
  00044	48 8b f1	 mov	 rsi, rcx
  00047	b9 10 00 00 00	 mov	 ecx, 16
  0004c	f3 a4		 rep movsb

; 386  : 		SystemTimeToFileTime(&sTime, (FILETIME*)&microSecond);

  0004e	48 8d 54 24 20	 lea	 rdx, QWORD PTR microSecond$1[rsp]
  00053	48 8d 4c 24 50	 lea	 rcx, QWORD PTR $T5[rsp]
  00058	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SystemTimeToFileTime

; 387  : 		return microSecond;

  0005e	48 8b 44 24 20	 mov	 rax, QWORD PTR microSecond$1[rsp]
  00063	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax

; 438  : 
; 439  : 		ULONGLONG dailyTimeStamp_ = getMicroSecond(dailyTimeStamp);

  00068	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
  0006d	48 89 44 24 48	 mov	 QWORD PTR dailyTimeStamp_$[rsp], rax

; 440  : 		ULONGLONG compareDate_ = getMicroSecond(compareDate);

  00072	48 8d 44 24 60	 lea	 rax, QWORD PTR $T6[rsp]
  00077	48 8b f8	 mov	 rdi, rax
  0007a	48 8b b4 24 b8
	00 00 00	 mov	 rsi, QWORD PTR compareDate$[rsp]
  00082	b9 10 00 00 00	 mov	 ecx, 16
  00087	f3 a4		 rep movsb

; 386  : 		SystemTimeToFileTime(&sTime, (FILETIME*)&microSecond);

  00089	48 8d 54 24 30	 lea	 rdx, QWORD PTR microSecond$3[rsp]
  0008e	48 8d 4c 24 60	 lea	 rcx, QWORD PTR $T6[rsp]
  00093	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SystemTimeToFileTime

; 387  : 		return microSecond;

  00099	48 8b 44 24 30	 mov	 rax, QWORD PTR microSecond$3[rsp]
  0009e	48 89 44 24 38	 mov	 QWORD PTR $T4[rsp], rax

; 440  : 		ULONGLONG compareDate_ = getMicroSecond(compareDate);

  000a3	48 8b 44 24 38	 mov	 rax, QWORD PTR $T4[rsp]
  000a8	48 89 44 24 40	 mov	 QWORD PTR compareDate_$[rsp], rax

; 441  : 
; 442  : 		if (dailyTimeStamp_ > compareDate_)

  000ad	48 8b 44 24 40	 mov	 rax, QWORD PTR compareDate_$[rsp]
  000b2	48 39 44 24 48	 cmp	 QWORD PTR dailyTimeStamp_$[rsp], rax
  000b7	76 04		 jbe	 SHORT $LN2@CompareDai

; 443  : 		{
; 444  : 			return true;

  000b9	b0 01		 mov	 al, 1
  000bb	eb 02		 jmp	 SHORT $LN1@CompareDai
$LN2@CompareDai:

; 445  : 		}
; 446  : 
; 447  : 		return false;

  000bd	32 c0		 xor	 al, al
$LN1@CompareDai:

; 448  : 	}

  000bf	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR __$ArrayPad$[rsp]
  000c7	48 33 cc	 xor	 rcx, rsp
  000ca	e8 00 00 00 00	 call	 __security_check_cookie
  000cf	48 81 c4 98 00
	00 00		 add	 rsp, 152		; 00000098H
  000d6	5f		 pop	 rdi
  000d7	5e		 pop	 rsi
  000d8	c3		 ret	 0
?CompareDailyTimeStamp@DailyComparer@mu2@@QEAA_NAEBU_SYSTEMTIME@@@Z ENDP ; mu2::DailyComparer::CompareDailyTimeStamp
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Shared\Protocol\Common\StructDateTimeEx.h
;	COMDAT ?GetDailyTimeStamp@DailyComparer@mu2@@QEAA?AU_SYSTEMTIME@@XZ
_TEXT	SEGMENT
microSecond$1 = 32
microSecond$2 = 40
$T3 = 48
$T4 = 56
$T5 = 64
$T6 = 72
$T7 = 80
microSecond$ = 88
$T8 = 96
dailyTimeStamp$ = 112
currentTime$ = 128
localTime$9 = 144
sTime$10 = 160
$T11 = 176
__$ArrayPad$ = 192
this$ = 240
__$ReturnUdt$ = 248
?GetDailyTimeStamp@DailyComparer@mu2@@QEAA?AU_SYSTEMTIME@@XZ PROC ; mu2::DailyComparer::GetDailyTimeStamp, COMDAT

; 407  : 	{

$LN18:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	56		 push	 rsi
  0000b	57		 push	 rdi
  0000c	48 81 ec d8 00
	00 00		 sub	 rsp, 216		; 000000d8H
  00013	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR __security_cookie
  0001a	48 33 c4	 xor	 rax, rsp
  0001d	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR __$ArrayPad$[rsp], rax

; 400  : 		GetLocalTime(&localTime);

  00025	48 8d 8c 24 90
	00 00 00	 lea	 rcx, QWORD PTR localTime$9[rsp]
  0002d	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetLocalTime

; 401  : 		return localTime;

  00033	48 8d 84 24 80
	00 00 00	 lea	 rax, QWORD PTR currentTime$[rsp]
  0003b	48 8d 8c 24 90
	00 00 00	 lea	 rcx, QWORD PTR localTime$9[rsp]
  00043	48 8b f8	 mov	 rdi, rax
  00046	48 8b f1	 mov	 rsi, rcx
  00049	b9 10 00 00 00	 mov	 ecx, 16
  0004e	f3 a4		 rep movsb

; 408  : 		SYSTEMTIME currentTime = getlocalSystemTime();
; 409  : 
; 410  : 		SYSTEMTIME dailyTimeStamp;
; 411  : 		dailyTimeStamp.wYear = currentTime.wYear;

  00050	0f b7 84 24 80
	00 00 00	 movzx	 eax, WORD PTR currentTime$[rsp]
  00058	66 89 44 24 70	 mov	 WORD PTR dailyTimeStamp$[rsp], ax

; 412  : 		dailyTimeStamp.wMonth = currentTime.wMonth;

  0005d	0f b7 84 24 82
	00 00 00	 movzx	 eax, WORD PTR currentTime$[rsp+2]
  00065	66 89 44 24 72	 mov	 WORD PTR dailyTimeStamp$[rsp+2], ax

; 413  : 		dailyTimeStamp.wDayOfWeek = currentTime.wDayOfWeek;

  0006a	0f b7 84 24 84
	00 00 00	 movzx	 eax, WORD PTR currentTime$[rsp+4]
  00072	66 89 44 24 74	 mov	 WORD PTR dailyTimeStamp$[rsp+4], ax

; 414  : 		dailyTimeStamp.wDay = currentTime.wDay;

  00077	0f b7 84 24 86
	00 00 00	 movzx	 eax, WORD PTR currentTime$[rsp+6]
  0007f	66 89 44 24 76	 mov	 WORD PTR dailyTimeStamp$[rsp+6], ax

; 415  : 		dailyTimeStamp.wHour = m_resetHour;

  00084	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0008c	0f b7 00	 movzx	 eax, WORD PTR [rax]
  0008f	66 89 44 24 78	 mov	 WORD PTR dailyTimeStamp$[rsp+8], ax

; 416  : 		dailyTimeStamp.wMinute = 0;

  00094	33 c0		 xor	 eax, eax
  00096	66 89 44 24 7a	 mov	 WORD PTR dailyTimeStamp$[rsp+10], ax

; 417  : 		dailyTimeStamp.wSecond = 0;

  0009b	33 c0		 xor	 eax, eax
  0009d	66 89 44 24 7c	 mov	 WORD PTR dailyTimeStamp$[rsp+12], ax

; 418  : 		dailyTimeStamp.wMilliseconds = 0;

  000a2	33 c0		 xor	 eax, eax
  000a4	66 89 44 24 7e	 mov	 WORD PTR dailyTimeStamp$[rsp+14], ax

; 419  : 
; 420  : 
; 421  : 		// 현재시간이 리셋 시 보다 작을경우 하루전 리셋시간으로 설정
; 422  : 		if (currentTime.wHour < m_resetHour)

  000a9	0f b7 84 24 88
	00 00 00	 movzx	 eax, WORD PTR currentTime$[rsp+8]
  000b1	48 8b 8c 24 f0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  000b9	0f b7 09	 movzx	 ecx, WORD PTR [rcx]
  000bc	3b c1		 cmp	 eax, ecx
  000be	0f 8d d7 00 00
	00		 jge	 $LN2@GetDailyTi

; 423  : 		{
; 424  : 			ULONGLONG microSecond = getMicroSecond(dailyTimeStamp);

  000c4	48 8d 44 24 60	 lea	 rax, QWORD PTR $T8[rsp]
  000c9	48 8d 4c 24 70	 lea	 rcx, QWORD PTR dailyTimeStamp$[rsp]
  000ce	48 8b f8	 mov	 rdi, rax
  000d1	48 8b f1	 mov	 rsi, rcx
  000d4	b9 10 00 00 00	 mov	 ecx, 16
  000d9	f3 a4		 rep movsb

; 386  : 		SystemTimeToFileTime(&sTime, (FILETIME*)&microSecond);

  000db	48 8d 54 24 28	 lea	 rdx, QWORD PTR microSecond$2[rsp]
  000e0	48 8d 4c 24 60	 lea	 rcx, QWORD PTR $T8[rsp]
  000e5	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SystemTimeToFileTime

; 387  : 		return microSecond;

  000eb	48 8b 44 24 28	 mov	 rax, QWORD PTR microSecond$2[rsp]
  000f0	48 89 44 24 30	 mov	 QWORD PTR $T3[rsp], rax

; 423  : 		{
; 424  : 			ULONGLONG microSecond = getMicroSecond(dailyTimeStamp);

  000f5	48 8b 44 24 30	 mov	 rax, QWORD PTR $T3[rsp]
  000fa	48 89 44 24 20	 mov	 QWORD PTR microSecond$1[rsp], rax

; 378  : 	ULONGLONG getSecond() { return ((ULONGLONG)10000000); }

  000ff	48 c7 44 24 38
	80 96 98 00	 mov	 QWORD PTR $T4[rsp], 10000000 ; 00989680H

; 379  : 	ULONGLONG getMinute() { return getSecond() * 60; }

  00108	48 8b 44 24 38	 mov	 rax, QWORD PTR $T4[rsp]
  0010d	48 6b c0 3c	 imul	 rax, rax, 60		; 0000003cH
  00111	48 89 44 24 40	 mov	 QWORD PTR $T5[rsp], rax

; 380  : 	ULONGLONG getHour() { return getMinute() * 60; }

  00116	48 8b 44 24 40	 mov	 rax, QWORD PTR $T5[rsp]
  0011b	48 6b c0 3c	 imul	 rax, rax, 60		; 0000003cH
  0011f	48 89 44 24 48	 mov	 QWORD PTR $T6[rsp], rax

; 381  : 	ULONGLONG getDay() { return getHour() * 24; }

  00124	48 8b 44 24 48	 mov	 rax, QWORD PTR $T6[rsp]
  00129	48 6b c0 18	 imul	 rax, rax, 24
  0012d	48 89 44 24 50	 mov	 QWORD PTR $T7[rsp], rax

; 425  : 
; 426  : 			microSecond = microSecond - getDay();

  00132	48 8b 44 24 50	 mov	 rax, QWORD PTR $T7[rsp]
  00137	48 8b 4c 24 20	 mov	 rcx, QWORD PTR microSecond$1[rsp]
  0013c	48 2b c8	 sub	 rcx, rax
  0013f	48 8b c1	 mov	 rax, rcx
  00142	48 89 44 24 20	 mov	 QWORD PTR microSecond$1[rsp], rax

; 427  : 
; 428  : 			dailyTimeStamp = getSystemTime(microSecond);

  00147	48 8b 44 24 20	 mov	 rax, QWORD PTR microSecond$1[rsp]
  0014c	48 89 44 24 58	 mov	 QWORD PTR microSecond$[rsp], rax

; 393  : 		FileTimeToSystemTime((FILETIME*)&microSecond, &sTime);

  00151	48 8d 94 24 a0
	00 00 00	 lea	 rdx, QWORD PTR sTime$10[rsp]
  00159	48 8d 4c 24 58	 lea	 rcx, QWORD PTR microSecond$[rsp]
  0015e	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_FileTimeToSystemTime

; 394  : 		return sTime;

  00164	48 8d 84 24 b0
	00 00 00	 lea	 rax, QWORD PTR $T11[rsp]
  0016c	48 8d 8c 24 a0
	00 00 00	 lea	 rcx, QWORD PTR sTime$10[rsp]
  00174	48 8b f8	 mov	 rdi, rax
  00177	48 8b f1	 mov	 rsi, rcx
  0017a	b9 10 00 00 00	 mov	 ecx, 16
  0017f	f3 a4		 rep movsb

; 427  : 
; 428  : 			dailyTimeStamp = getSystemTime(microSecond);

  00181	48 8d 44 24 70	 lea	 rax, QWORD PTR dailyTimeStamp$[rsp]
  00186	48 8d 8c 24 b0
	00 00 00	 lea	 rcx, QWORD PTR $T11[rsp]
  0018e	48 8b f8	 mov	 rdi, rax
  00191	48 8b f1	 mov	 rsi, rcx
  00194	b9 10 00 00 00	 mov	 ecx, 16
  00199	f3 a4		 rep movsb
$LN2@GetDailyTi:

; 429  : 		}
; 430  : 
; 431  : 		return dailyTimeStamp;

  0019b	48 8d 44 24 70	 lea	 rax, QWORD PTR dailyTimeStamp$[rsp]
  001a0	48 8b bc 24 f8
	00 00 00	 mov	 rdi, QWORD PTR __$ReturnUdt$[rsp]
  001a8	48 8b f0	 mov	 rsi, rax
  001ab	b9 10 00 00 00	 mov	 ecx, 16
  001b0	f3 a4		 rep movsb
  001b2	48 8b 84 24 f8
	00 00 00	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]

; 432  : 	}

  001ba	48 8b 8c 24 c0
	00 00 00	 mov	 rcx, QWORD PTR __$ArrayPad$[rsp]
  001c2	48 33 cc	 xor	 rcx, rsp
  001c5	e8 00 00 00 00	 call	 __security_check_cookie
  001ca	48 81 c4 d8 00
	00 00		 add	 rsp, 216		; 000000d8H
  001d1	5f		 pop	 rdi
  001d2	5e		 pop	 rsi
  001d3	c3		 ret	 0
?GetDailyTimeStamp@DailyComparer@mu2@@QEAA?AU_SYSTEMTIME@@XZ ENDP ; mu2::DailyComparer::GetDailyTimeStamp
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Shared\Protocol\Common\StructDateTimeEx.h
;	COMDAT ??1DailyComparer@mu2@@QEAA@XZ
_TEXT	SEGMENT
this$ = 8
??1DailyComparer@mu2@@QEAA@XZ PROC			; mu2::DailyComparer::~DailyComparer, COMDAT

; 373  : 	{

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 374  : 
; 375  : 	}

  00005	c3		 ret	 0
??1DailyComparer@mu2@@QEAA@XZ ENDP			; mu2::DailyComparer::~DailyComparer
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
