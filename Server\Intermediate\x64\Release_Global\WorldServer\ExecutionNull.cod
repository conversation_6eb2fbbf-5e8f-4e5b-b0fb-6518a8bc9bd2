; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	?IsInstance@Execution@mu2@@UEBA_NXZ		; mu2::Execution::IsInstance
PUBLIC	?IsChannel@Execution@mu2@@UEBA_NXZ		; mu2::Execution::IsChannel
PUBLIC	?IsWorldcross@Execution@mu2@@UEBA_NXZ		; mu2::Execution::IsWorldcross
PUBLIC	?IsTournamentEnter@Execution@mu2@@UEBA_NXZ	; mu2::Execution::IsTournamentEnter
PUBLIC	?IsWorldcrossing@Execution@mu2@@UEBA_NXZ	; mu2::Execution::IsWorldcrossing
PUBLIC	?IsSingle@Execution@mu2@@UEBA_NXZ		; mu2::Execution::IsSingle
PUBLIC	?IsGroup@Execution@mu2@@UEBA_NXZ		; mu2::Execution::IsGroup
PUBLIC	?GetDamageMeterKey@Execution@mu2@@UEBA?BIXZ	; mu2::Execution::GetDamageMeterKey
PUBLIC	??0NullExecution@mu2@@QEAA@XZ			; mu2::NullExecution::NullExecution
PUBLIC	??1NullExecution@mu2@@UEAA@XZ			; mu2::NullExecution::~NullExecution
PUBLIC	?IsNull@NullExecution@mu2@@UEBA_NXZ		; mu2::NullExecution::IsNull
PUBLIC	?OnCreatedSucceed@NullExecution@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ; mu2::NullExecution::OnCreatedSucceed
PUBLIC	?OnDestroyed@NullExecution@mu2@@UEAAXXZ		; mu2::NullExecution::OnDestroyed
PUBLIC	?EnterExecution@NullExecution@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ; mu2::NullExecution::EnterExecution
PUBLIC	?ExitExecutionPost@NullExecution@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ; mu2::NullExecution::ExitExecutionPost
PUBLIC	??_GNullExecution@mu2@@UEAAPEAXI@Z		; mu2::NullExecution::`scalar deleting destructor'
PUBLIC	??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@ ; `string'
PUBLIC	??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@		; `string'
PUBLIC	??_7NullExecution@mu2@@6B@			; mu2::NullExecution::`vftable'
PUBLIC	??_C@_0BJ@LDCOFOGA@NullExecution?3?3OnCreated@	; `string'
PUBLIC	??_C@_0EM@KFGKPGIL@F?3?2Release_Branch?2Server?2Develo@ ; `string'
PUBLIC	??_C@_0BM@JDNEPOGI@?$CB?$CCNullExecution?3?3OnCreated?$CC@ ; `string'
PUBLIC	??_C@_0CF@LIPLGMLL@mu2?3?3NullExecution?3?3OnCreatedSu@ ; `string'
PUBLIC	??_C@_0BL@DNHGMIOI@NullExecution?3?3OnDestroyed@ ; `string'
PUBLIC	??_C@_0BO@EEHNCGPB@?$CB?$CCNullExecution?3?3OnDestroyed?$CC@ ; `string'
PUBLIC	??_C@_0CA@DDCNGEGC@mu2?3?3NullExecution?3?3OnDestroyed@ ; `string'
PUBLIC	??_R4NullExecution@mu2@@6B@			; mu2::NullExecution::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVNullExecution@mu2@@@8			; mu2::NullExecution `RTTI Type Descriptor'
PUBLIC	??_R3NullExecution@mu2@@8			; mu2::NullExecution::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2NullExecution@mu2@@8			; mu2::NullExecution::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@NullExecution@mu2@@8		; mu2::NullExecution::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@Execution@mu2@@8			; mu2::Execution::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R0?AVExecution@mu2@@@8			; mu2::Execution `RTTI Type Descriptor'
PUBLIC	??_R3Execution@mu2@@8				; mu2::Execution::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2Execution@mu2@@8				; mu2::Execution::`RTTI Base Class Array'
EXTRN	??3@YAXPEAX_K@Z:PROC				; operator delete
EXTRN	__imp_GetStdHandle:PROC
EXTRN	__imp_SetConsoleTextAttribute:PROC
EXTRN	?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ:PROC	; mu2::Logger::Logging
EXTRN	??0Execution@mu2@@QEAA@PEAVManagerExecutionBase@1@@Z:PROC ; mu2::Execution::Execution
EXTRN	??1Execution@mu2@@UEAA@XZ:PROC			; mu2::Execution::~Execution
EXTRN	?OnDestroyedFailed@Execution@mu2@@UEAAXXZ:PROC	; mu2::Execution::OnDestroyedFailed
EXTRN	?OnCreatedFailed@Execution@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::Execution::OnCreatedFailed
EXTRN	?OnResGameReady@Execution@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::Execution::OnResGameReady
EXTRN	?EnterExecutionPrev@Execution@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::Execution::EnterExecutionPrev
EXTRN	?EnterExecutionPost@Execution@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::Execution::EnterExecutionPost
EXTRN	?Init@Execution@mu2@@UEAA_NAEAUExecutionCreateInfo@2@@Z:PROC ; mu2::Execution::Init
EXTRN	?Update@Execution@mu2@@UEAAXXZ:PROC		; mu2::Execution::Update
EXTRN	?OnShutdownZone@Execution@mu2@@UEAAXXZ:PROC	; mu2::Execution::OnShutdownZone
EXTRN	?SendReqJoinExecution@Execution@mu2@@UEBA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@@Z:PROC ; mu2::Execution::SendReqJoinExecution
EXTRN	?IsValid@Execution@mu2@@UEBA?B_NXZ:PROC		; mu2::Execution::IsValid
EXTRN	?IsRunning@Execution@mu2@@UEBA_NXZ:PROC		; mu2::Execution::IsRunning
EXTRN	?IsJoinable@Execution@mu2@@UEBA_NPEAVEntityPlayer@2@@Z:PROC ; mu2::Execution::IsJoinable
EXTRN	?IsDestroyable@Execution@mu2@@UEBA_NXZ:PROC	; mu2::Execution::IsDestroyable
EXTRN	?ToString@Execution@mu2@@UEBA?BV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ:PROC ; mu2::Execution::ToString
EXTRN	?GetRelayServerId@Execution@mu2@@UEBA?BIXZ:PROC	; mu2::Execution::GetRelayServerId
EXTRN	?Fillup@Execution@mu2@@UEAAXAEAPEAUEwzReqCreateExecution@2@@Z:PROC ; mu2::Execution::Fillup
EXTRN	?Fillup@Execution@mu2@@UEAAXAEAPEAUEwzReqDestroyExecution@2@@Z:PROC ; mu2::Execution::Fillup
EXTRN	?Fillup@Execution@mu2@@UEBAXAEAVJoinZoneContextDest@2@@Z:PROC ; mu2::Execution::Fillup
EXTRN	?Fillup@Execution@mu2@@MEBA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAPEAUEwzReqJoinExecution@2@@Z:PROC ; mu2::Execution::Fillup
EXTRN	??_ENullExecution@mu2@@UEAAPEAXI@Z:PROC		; mu2::NullExecution::`vector deleting destructor'
EXTRN	??_7type_info@@6B@:BYTE				; type_info::`vftable'
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0NullExecution@mu2@@QEAA@XZ DD imagerel $LN4
	DD	imagerel $LN4+46
	DD	imagerel $unwind$??0NullExecution@mu2@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1NullExecution@mu2@@UEAA@XZ DD imagerel $LN4
	DD	imagerel $LN4+40
	DD	imagerel $unwind$??1NullExecution@mu2@@UEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?OnCreatedSucceed@NullExecution@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z DD imagerel $LN4
	DD	imagerel $LN4+188
	DD	imagerel $unwind$?OnCreatedSucceed@NullExecution@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?OnDestroyed@NullExecution@mu2@@UEAAXXZ DD imagerel $LN4
	DD	imagerel $LN4+183
	DD	imagerel $unwind$?OnDestroyed@NullExecution@mu2@@UEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GNullExecution@mu2@@UEAAPEAXI@Z DD imagerel $LN5
	DD	imagerel $LN5+60
	DD	imagerel $unwind$??_GNullExecution@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT ??_R2Execution@mu2@@8
rdata$r	SEGMENT
??_R2Execution@mu2@@8 DD imagerel ??_R1A@?0A@EA@Execution@mu2@@8 ; mu2::Execution::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3Execution@mu2@@8
rdata$r	SEGMENT
??_R3Execution@mu2@@8 DD 00H				; mu2::Execution::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2Execution@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVExecution@mu2@@@8
data$rs	SEGMENT
??_R0?AVExecution@mu2@@@8 DQ FLAT:??_7type_info@@6B@	; mu2::Execution `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVExecution@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R1A@?0A@EA@Execution@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@Execution@mu2@@8 DD imagerel ??_R0?AVExecution@mu2@@@8 ; mu2::Execution::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3Execution@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@NullExecution@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@NullExecution@mu2@@8 DD imagerel ??_R0?AVNullExecution@mu2@@@8 ; mu2::NullExecution::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3NullExecution@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2NullExecution@mu2@@8
rdata$r	SEGMENT
??_R2NullExecution@mu2@@8 DD imagerel ??_R1A@?0A@EA@NullExecution@mu2@@8 ; mu2::NullExecution::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@Execution@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3NullExecution@mu2@@8
rdata$r	SEGMENT
??_R3NullExecution@mu2@@8 DD 00H			; mu2::NullExecution::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2NullExecution@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVNullExecution@mu2@@@8
data$rs	SEGMENT
??_R0?AVNullExecution@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::NullExecution `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVNullExecution@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4NullExecution@mu2@@6B@
rdata$r	SEGMENT
??_R4NullExecution@mu2@@6B@ DD 01H			; mu2::NullExecution::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVNullExecution@mu2@@@8
	DD	imagerel ??_R3NullExecution@mu2@@8
	DD	imagerel ??_R4NullExecution@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_C@_0CA@DDCNGEGC@mu2?3?3NullExecution?3?3OnDestroyed@
CONST	SEGMENT
??_C@_0CA@DDCNGEGC@mu2?3?3NullExecution?3?3OnDestroyed@ DB 'mu2::NullExec'
	DB	'ution::OnDestroyed', 00H			; `string'
CONST	ENDS
;	COMDAT ??_C@_0BO@EEHNCGPB@?$CB?$CCNullExecution?3?3OnDestroyed?$CC@
CONST	SEGMENT
??_C@_0BO@EEHNCGPB@?$CB?$CCNullExecution?3?3OnDestroyed?$CC@ DB '!"NullEx'
	DB	'ecution::OnDestroyed"', 00H			; `string'
CONST	ENDS
;	COMDAT ??_C@_0BL@DNHGMIOI@NullExecution?3?3OnDestroyed@
CONST	SEGMENT
??_C@_0BL@DNHGMIOI@NullExecution?3?3OnDestroyed@ DB 'NullExecution::OnDes'
	DB	'troyed', 00H				; `string'
CONST	ENDS
;	COMDAT ??_C@_0CF@LIPLGMLL@mu2?3?3NullExecution?3?3OnCreatedSu@
CONST	SEGMENT
??_C@_0CF@LIPLGMLL@mu2?3?3NullExecution?3?3OnCreatedSu@ DB 'mu2::NullExec'
	DB	'ution::OnCreatedSucceed', 00H		; `string'
CONST	ENDS
;	COMDAT ??_C@_0BM@JDNEPOGI@?$CB?$CCNullExecution?3?3OnCreated?$CC@
CONST	SEGMENT
??_C@_0BM@JDNEPOGI@?$CB?$CCNullExecution?3?3OnCreated?$CC@ DB '!"NullExec'
	DB	'ution::OnCreated"', 00H			; `string'
CONST	ENDS
;	COMDAT ??_C@_0EM@KFGKPGIL@F?3?2Release_Branch?2Server?2Develo@
CONST	SEGMENT
??_C@_0EM@KFGKPGIL@F?3?2Release_Branch?2Server?2Develo@ DB 'F:\Release_Br'
	DB	'anch\Server\Development\Frontend\WorldServer\ExecutionNull.cp'
	DB	'p', 00H					; `string'
CONST	ENDS
;	COMDAT ??_C@_0BJ@LDCOFOGA@NullExecution?3?3OnCreated@
CONST	SEGMENT
??_C@_0BJ@LDCOFOGA@NullExecution?3?3OnCreated@ DB 'NullExecution::OnCreat'
	DB	'ed', 00H					; `string'
CONST	ENDS
;	COMDAT ??_7NullExecution@mu2@@6B@
CONST	SEGMENT
??_7NullExecution@mu2@@6B@ DQ FLAT:??_R4NullExecution@mu2@@6B@ ; mu2::NullExecution::`vftable'
	DQ	FLAT:??_ENullExecution@mu2@@UEAAPEAXI@Z
	DQ	FLAT:?OnDestroyed@NullExecution@mu2@@UEAAXXZ
	DQ	FLAT:?OnDestroyedFailed@Execution@mu2@@UEAAXXZ
	DQ	FLAT:?OnCreatedSucceed@NullExecution@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?OnCreatedFailed@Execution@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?OnResGameReady@Execution@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?EnterExecutionPrev@Execution@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?EnterExecution@NullExecution@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?EnterExecutionPost@Execution@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?ExitExecutionPost@NullExecution@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?Init@Execution@mu2@@UEAA_NAEAUExecutionCreateInfo@2@@Z
	DQ	FLAT:?Update@Execution@mu2@@UEAAXXZ
	DQ	FLAT:?OnShutdownZone@Execution@mu2@@UEAAXXZ
	DQ	FLAT:?SendReqJoinExecution@Execution@mu2@@UEBA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@@Z
	DQ	FLAT:?IsInstance@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsChannel@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsWorldcross@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsTournamentEnter@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsWorldcrossing@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsSingle@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsGroup@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsNull@NullExecution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsValid@Execution@mu2@@UEBA?B_NXZ
	DQ	FLAT:?IsRunning@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsJoinable@Execution@mu2@@UEBA_NPEAVEntityPlayer@2@@Z
	DQ	FLAT:?IsDestroyable@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?GetDamageMeterKey@Execution@mu2@@UEBA?BIXZ
	DQ	FLAT:?ToString@Execution@mu2@@UEBA?BV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ
	DQ	FLAT:?GetRelayServerId@Execution@mu2@@UEBA?BIXZ
	DQ	FLAT:?Fillup@Execution@mu2@@MEBA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAPEAUEwzReqJoinExecution@2@@Z
	DQ	FLAT:?Fillup@Execution@mu2@@UEBAXAEAVJoinZoneContextDest@2@@Z
	DQ	FLAT:?Fillup@Execution@mu2@@UEAAXAEAPEAUEwzReqDestroyExecution@2@@Z
	DQ	FLAT:?Fillup@Execution@mu2@@UEAAXAEAPEAUEwzReqCreateExecution@2@@Z
CONST	ENDS
;	COMDAT ??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
CONST	SEGMENT
??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@ DB 'g', 00H, 'a', 00H, 'm', 00H, 'e'
	DB	00H, 00H, 00H				; `string'
CONST	ENDS
;	COMDAT ??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
CONST	SEGMENT
??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@ DB '%'
	DB	's> ASSERT - %s, %s(%d)', 00H		; `string'
CONST	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GNullExecution@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?OnDestroyed@NullExecution@mu2@@UEAAXXZ DD 010901H
	DD	0e209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?OnCreatedSucceed@NullExecution@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z DD 010e01H
	DD	0e20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1NullExecution@mu2@@UEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0NullExecution@mu2@@QEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
; Function compile flags: /Odtp
;	COMDAT ??_GNullExecution@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GNullExecution@mu2@@UEAAPEAXI@Z PROC			; mu2::NullExecution::`scalar deleting destructor', COMDAT
$LN5:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00012	e8 00 00 00 00	 call	 ??1NullExecution@mu2@@UEAA@XZ ; mu2::NullExecution::~NullExecution
  00017	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  0001b	83 e0 01	 and	 eax, 1
  0001e	85 c0		 test	 eax, eax
  00020	74 10		 je	 SHORT $LN2@scalar
  00022	ba 80 00 00 00	 mov	 edx, 128		; 00000080H
  00027	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0002c	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00031	90		 npad	 1
$LN2@scalar:
  00032	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00037	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0003b	c3		 ret	 0
??_GNullExecution@mu2@@UEAAPEAXI@Z ENDP			; mu2::NullExecution::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ExecutionNull.h
;	COMDAT ?ExitExecutionPost@NullExecution@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
_TEXT	SEGMENT
this$ = 8
__formal$ = 16
__formal$ = 24
?ExitExecutionPost@NullExecution@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z PROC ; mu2::NullExecution::ExitExecutionPost, COMDAT

; 21   : 		virtual ErrorJoin::Error ExitExecutionPost(EntityPlayer*, EventPtr&) override { return ErrorJoin::NotfoundExecution; }

  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	b8 bc 1a 06 00	 mov	 eax, 400060		; 00061abcH
  00014	c3		 ret	 0
?ExitExecutionPost@NullExecution@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ENDP ; mu2::NullExecution::ExitExecutionPost
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ExecutionNull.h
;	COMDAT ?EnterExecution@NullExecution@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
_TEXT	SEGMENT
this$ = 8
__formal$ = 16
__formal$ = 24
?EnterExecution@NullExecution@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z PROC ; mu2::NullExecution::EnterExecution, COMDAT

; 20   : 		virtual ErrorJoin::Error EnterExecution(EntityPlayer*, EventPtr&) override { return ErrorJoin::NotfoundExecution;  }

  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	b8 bc 1a 06 00	 mov	 eax, 400060		; 00061abcH
  00014	c3		 ret	 0
?EnterExecution@NullExecution@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ENDP ; mu2::NullExecution::EnterExecution
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ExecutionNull.cpp
;	COMDAT ?OnDestroyed@NullExecution@mu2@@UEAAXXZ
_TEXT	SEGMENT
hConsole$1 = 96
this$ = 128
?OnDestroyed@NullExecution@mu2@@UEAAXXZ PROC		; mu2::NullExecution::OnDestroyed, COMDAT

; 29   : 	{

$LN4:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 78	 sub	 rsp, 120		; 00000078H

; 30   : 		VERIFY_RETURN(!"NullExecution::OnDestroyed", );

  00009	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BL@DNHGMIOI@NullExecution?3?3OnDestroyed@
  00010	48 85 c0	 test	 rax, rax
  00013	0f 84 99 00 00
	00		 je	 $LN2@OnDestroye
  00019	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  0001e	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  00024	48 89 44 24 60	 mov	 QWORD PTR hConsole$1[rsp], rax
  00029	66 ba 0d 00	 mov	 dx, 13
  0002d	48 8b 4c 24 60	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  00032	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  00038	c7 44 24 50 1e
	00 00 00	 mov	 DWORD PTR [rsp+80], 30
  00040	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EM@KFGKPGIL@F?3?2Release_Branch?2Server?2Develo@
  00047	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  0004c	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BO@EEHNCGPB@?$CB?$CCNullExecution?3?3OnDestroyed?$CC@
  00053	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  00058	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CA@DDCNGEGC@mu2?3?3NullExecution?3?3OnDestroyed@
  0005f	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  00064	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  0006b	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  00070	c7 44 24 28 1e
	00 00 00	 mov	 DWORD PTR [rsp+40], 30
  00078	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EM@KFGKPGIL@F?3?2Release_Branch?2Server?2Develo@
  0007f	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  00084	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CA@DDCNGEGC@mu2?3?3NullExecution?3?3OnDestroyed@
  0008b	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  00091	ba 02 00 00 00	 mov	 edx, 2
  00096	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  0009d	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  000a2	66 ba 07 00	 mov	 dx, 7
  000a6	48 8b 4c 24 60	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  000ab	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000b1	90		 npad	 1
$LN2@OnDestroye:

; 31   : 	}

  000b2	48 83 c4 78	 add	 rsp, 120		; 00000078H
  000b6	c3		 ret	 0
?OnDestroyed@NullExecution@mu2@@UEAAXXZ ENDP		; mu2::NullExecution::OnDestroyed
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ExecutionNull.cpp
;	COMDAT ?OnCreatedSucceed@NullExecution@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
_TEXT	SEGMENT
hConsole$1 = 96
this$ = 128
e$ = 136
?OnCreatedSucceed@NullExecution@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z PROC ; mu2::NullExecution::OnCreatedSucceed, COMDAT

; 23   : 	{

$LN4:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 78	 sub	 rsp, 120		; 00000078H

; 24   : 		UNREFERENCED_PARAMETER(e);
; 25   : 		VERIFY_RETURN(!"NullExecution::OnCreated", );

  0000e	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BJ@LDCOFOGA@NullExecution?3?3OnCreated@
  00015	48 85 c0	 test	 rax, rax
  00018	0f 84 99 00 00
	00		 je	 $LN2@OnCreatedS
  0001e	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  00023	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  00029	48 89 44 24 60	 mov	 QWORD PTR hConsole$1[rsp], rax
  0002e	66 ba 0d 00	 mov	 dx, 13
  00032	48 8b 4c 24 60	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  00037	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  0003d	c7 44 24 50 19
	00 00 00	 mov	 DWORD PTR [rsp+80], 25
  00045	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EM@KFGKPGIL@F?3?2Release_Branch?2Server?2Develo@
  0004c	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  00051	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BM@JDNEPOGI@?$CB?$CCNullExecution?3?3OnCreated?$CC@
  00058	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  0005d	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CF@LIPLGMLL@mu2?3?3NullExecution?3?3OnCreatedSu@
  00064	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  00069	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  00070	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  00075	c7 44 24 28 19
	00 00 00	 mov	 DWORD PTR [rsp+40], 25
  0007d	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EM@KFGKPGIL@F?3?2Release_Branch?2Server?2Develo@
  00084	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  00089	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CF@LIPLGMLL@mu2?3?3NullExecution?3?3OnCreatedSu@
  00090	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  00096	ba 02 00 00 00	 mov	 edx, 2
  0009b	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  000a2	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  000a7	66 ba 07 00	 mov	 dx, 7
  000ab	48 8b 4c 24 60	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  000b0	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000b6	90		 npad	 1
$LN2@OnCreatedS:

; 26   : 	}

  000b7	48 83 c4 78	 add	 rsp, 120		; 00000078H
  000bb	c3		 ret	 0
?OnCreatedSucceed@NullExecution@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ENDP ; mu2::NullExecution::OnCreatedSucceed
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ExecutionNull.h
;	COMDAT ?IsNull@NullExecution@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsNull@NullExecution@mu2@@UEBA_NXZ PROC		; mu2::NullExecution::IsNull, COMDAT

; 17   : 		virtual Bool	IsNull() const override { return true; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	b0 01		 mov	 al, 1
  00007	c3		 ret	 0
?IsNull@NullExecution@mu2@@UEBA_NXZ ENDP		; mu2::NullExecution::IsNull
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ExecutionNull.cpp
;	COMDAT ??1NullExecution@mu2@@UEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1NullExecution@mu2@@UEAA@XZ PROC			; mu2::NullExecution::~NullExecution, COMDAT

; 19   : 	{

$LN4:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7NullExecution@mu2@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx

; 20   : 	}

  00018	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0001d	e8 00 00 00 00	 call	 ??1Execution@mu2@@UEAA@XZ ; mu2::Execution::~Execution
  00022	90		 npad	 1
  00023	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00027	c3		 ret	 0
??1NullExecution@mu2@@UEAA@XZ ENDP			; mu2::NullExecution::~NullExecution
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ExecutionNull.cpp
;	COMDAT ??0NullExecution@mu2@@QEAA@XZ
_TEXT	SEGMENT
this$ = 48
??0NullExecution@mu2@@QEAA@XZ PROC			; mu2::NullExecution::NullExecution, COMDAT

; 15   : 	{

$LN4:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 14   : 		: Execution(NULL)

  00009	33 d2		 xor	 edx, edx
  0000b	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00010	e8 00 00 00 00	 call	 ??0Execution@mu2@@QEAA@PEAVManagerExecutionBase@1@@Z ; mu2::Execution::Execution

; 15   : 	{

  00015	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7NullExecution@mu2@@6B@
  00021	48 89 08	 mov	 QWORD PTR [rax], rcx

; 16   : 	}

  00024	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00029	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0002d	c3		 ret	 0
??0NullExecution@mu2@@QEAA@XZ ENDP			; mu2::NullExecution::NullExecution
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?GetDamageMeterKey@Execution@mu2@@UEBA?BIXZ
_TEXT	SEGMENT
this$ = 8
?GetDamageMeterKey@Execution@mu2@@UEBA?BIXZ PROC	; mu2::Execution::GetDamageMeterKey, COMDAT

; 95   : 		virtual const DamageMeterKey	GetDamageMeterKey() const { return 0; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	33 c0		 xor	 eax, eax
  00007	c3		 ret	 0
?GetDamageMeterKey@Execution@mu2@@UEBA?BIXZ ENDP	; mu2::Execution::GetDamageMeterKey
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?IsGroup@Execution@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsGroup@Execution@mu2@@UEBA_NXZ PROC			; mu2::Execution::IsGroup, COMDAT

; 85   : 		virtual Bool					IsGroup() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsGroup@Execution@mu2@@UEBA_NXZ ENDP			; mu2::Execution::IsGroup
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?IsSingle@Execution@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsSingle@Execution@mu2@@UEBA_NXZ PROC			; mu2::Execution::IsSingle, COMDAT

; 84   : 		virtual Bool					IsSingle() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsSingle@Execution@mu2@@UEBA_NXZ ENDP			; mu2::Execution::IsSingle
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?IsWorldcrossing@Execution@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsWorldcrossing@Execution@mu2@@UEBA_NXZ PROC		; mu2::Execution::IsWorldcrossing, COMDAT

; 83   : 		virtual Bool					IsWorldcrossing() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsWorldcrossing@Execution@mu2@@UEBA_NXZ ENDP		; mu2::Execution::IsWorldcrossing
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?IsTournamentEnter@Execution@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsTournamentEnter@Execution@mu2@@UEBA_NXZ PROC		; mu2::Execution::IsTournamentEnter, COMDAT

; 82   : 		virtual Bool					IsTournamentEnter() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsTournamentEnter@Execution@mu2@@UEBA_NXZ ENDP		; mu2::Execution::IsTournamentEnter
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?IsWorldcross@Execution@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsWorldcross@Execution@mu2@@UEBA_NXZ PROC		; mu2::Execution::IsWorldcross, COMDAT

; 81   : 		virtual Bool					IsWorldcross() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsWorldcross@Execution@mu2@@UEBA_NXZ ENDP		; mu2::Execution::IsWorldcross
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?IsChannel@Execution@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsChannel@Execution@mu2@@UEBA_NXZ PROC			; mu2::Execution::IsChannel, COMDAT

; 80   : 		virtual Bool					IsChannel() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsChannel@Execution@mu2@@UEBA_NXZ ENDP			; mu2::Execution::IsChannel
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?IsInstance@Execution@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsInstance@Execution@mu2@@UEBA_NXZ PROC		; mu2::Execution::IsInstance, COMDAT

; 79   : 		virtual Bool					IsInstance() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsInstance@Execution@mu2@@UEBA_NXZ ENDP		; mu2::Execution::IsInstance
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ExecutionNull.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ExecutionNull.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
