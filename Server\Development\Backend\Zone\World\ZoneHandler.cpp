﻿#include "stdafx.h"

#include <Backend/Zone/System/StatueSystem.h>
#include <Backend/Zone/System/GameEventSystem.h>
#include <Backend/Zone/System/SeasonSystem.h>
#include <Backend/Zone/ZoneServer.h>
#include <Backend/Zone/FieldKnightage/FieldKnightageManager.h>
#include <Backend/Zone/ExecutionManager.h>
#ifdef __Patch_Quest_ProgressType_Add_Period_by_cheolhoon_20181114
#include <Backend/Zone/System/QuestSystem.h>
#endif

namespace mu2
{

ZoneHandler::ZoneHandler()
: m_dispatcher( this )
{		
#ifdef MEASURE_PERFORMANCE
	last = GetTickCount();
	runcount = 0;
#endif
}

ZoneHandler::~ZoneHandler()
{
	Finish();
}

Bool ZoneHandler::Initialize()
{
	if (false == SetHandlers())
		return false;
	
	if( !theSectorGridManager.Initialize() )
	{
		MU2_ERROR_LOG( LogCategory::CONTENTS, L"ZoneHandler::Initialize > SectorGridManager init failed");
		return false;
	}

	MU2_TRACE_LOG(LogCategory::CONTENTS, "ZoneHandler> Initialized" );

	return theExecutionManager.Init(this);
}

void ZoneHandler::Notify(const EventPtr& e)
{
	VERIFY_RETURN(e.RawPtr(), );

	if (IsSectorEvent(e->GetEvent()))
	{
		EventPtr& epConst = const_cast<EventPtr&>(e);
		VALID_RETURN(ErrorJoin::SUCCESS == theExecutionManager.NotifySectorRunner(EventSetter::GetKeyAsCharId(epConst), epConst), );
	}
	else
	{
		m_queue.Push(e);
	}
}

void ZoneHandler::Notify(EntityPlayer* player, __in EventPtr& e)
{
	VERIFY_RETURN(player && e.RawPtr(), );
	EventSetter::SetSessionKeyWorldSessionCharId(player, e);
	Notify(e);
}

void ZoneHandler::Update()
{
	EventPtr e;

	UInt32 eventRecvCount = 0;
	
	if ( m_queue.GetSize() > 10 )
	{
		MU2_WARN_LOG( LogCategory::MONITOR, "[ZoneHandler][current queue size :%d]", m_queue.GetSize() );
	}	

	while ( m_queue.Pop( e ) )
	{
		++eventRecvCount;

		if ( ! m_dispatcher.Dispatch(e) )
		{
			MU2_WARN_LOG( LogCategory::CONTENTS, "ZoneHandler> Unhandled Event %s", e->GetEventname() );
		}
	}

	if ( eventRecvCount == 0 )
	{
		::Sleep( 1 ); // yield
	}

	theExecutionManager.Update();


	if( m_speedHackLatencyLogTimer.Elapsed() > 1000*30)
	{
		updateSpeedHackLatencyLog();
		m_speedHackLatencyLogTimer.Reset();
	}

	

}

void ZoneHandler::Finish()
{
	theExecutionManager.UnInit();

	MU2_INFO_LOG( shared::LogCategory::START_SERVER, "ZoneHandler> Finished" );
}

Bool ZoneHandler::IsSectorEvent( Short code )
{
	auto it = m_sectorEvent.find( code );
	if ( it == m_sectorEvent.end() )
	{
		return false;
	}

	return true;
}

void ZoneHandler::onReqCreateExecution( EventPtr& e )
{
	ErrorJoin::Error eError = theExecutionManager.OnReqCreateExecution(e);
	if (eError != ErrorJoin::SUCCESS)
	{		
		EzwResCreateExecution* res = Event::AllocEvent<EzwResCreateExecution>(e);
		res->eError = eError;
		SERVER.SendToOnlyWorldServer(EventPtr(res));
		
		EwzReqCreateExecution* req = static_cast<EwzReqCreateExecution*>(e.RawPtr());
		MU2_ERROR_LOG(LogCategory::JOIN, "%s> failed - eError(%d), %s", __FUNCTION__, eError, req->info.ToString().c_str());
		return;
	}	
}


// 여기서 먼저 처리하고 Sector로 요청
// 특정 인스턴스에 진입 요청이 월드 서버에서 도착

void ZoneHandler::onEwzReqJoinExecution(EventPtr& e)
{
	ErrorJoin::Error eError = theExecutionManager.OnEwzReqJoinExecution(e);

	VERIFY_DO(eError == ErrorJoin::SUCCESS,

		EwzReqJoinExecution* req = static_cast<EwzReqJoinExecution*>(e.RawPtr());
		EzwResJoinExecution* res = Event::AllocEvent<EzwResJoinExecution>(e);
		{
			res->eError = eError;
			res->charId = req->charId;
			res->context = req->m_eJoinContext;
			res->arrivedLocation = req->toLocation;
		}		
		SERVER.Send(EventSetter::GetIdxAsSessionKey(e), EventPtr(res));
	);
}

// 특정 인스턴스에서 진출 요청이 월드 서버에서 도착
void ZoneHandler::onReqLeaveExecution(EventPtr& e)
{
	ErrorJoin::Error eError = theExecutionManager.OnReqLeaveExecution(e);

	VERIFY_DO(eError == ErrorJoin::SUCCESS,

		EwzReqLeaveExecution* req = static_cast<EwzReqLeaveExecution*>(e.RawPtr());
		EzwResLeaveExecution* resp = Event::AllocEvent<EzwResLeaveExecution>(e);
		{
			resp->eError = eError;
			resp->charId = req->charId;
			resp->context = req->context;
			resp->leaveExecId = req->leaveExecId;
		}		
		SERVER.Send(EventSetter::GetIdxAsSessionKey(e), EventPtr(resp));
	);
}

void ZoneHandler::onReqDelayedLeaveExecution(EventPtr& e)
{
	ELoopbackDelayedLeaveExecution* req = static_cast<ELoopbackDelayedLeaveExecution*>(e.RawPtr());

	ErrorJoin::Error eError = theExecutionManager.NotifySectorRunner(req->charId, req->executionZoneId, e);

	VERIFY_DO(eError == ErrorJoin::SUCCESS,

		EzwResLeaveExecution* res = Event::AllocEvent<EzwResLeaveExecution>(e);
		{
			res->eError = eError;
			res->charId = req->charId;
			res->context = req->context;
			res->leaveExecId = req->executionZoneId;
		}		
		SERVER.Send(EventSetter::GetIdxAsSessionKey(e), EventPtr(res));
	);
}

void ZoneHandler::onReqDestroyExecution( EventPtr& e  )
{
	EwzReqDestroyExecution* req = static_cast<EwzReqDestroyExecution*>(e.RawPtr());

	ErrorJoin::Error eError = theExecutionManager.OnReqDestroyExecution(e);

	// 섹터러너로 위임했으므로 실패시만 패킷 전송
	if (eError != ErrorJoin::SUCCESS)
	{
		MU2_ERROR_LOG(LogCategory::JOIN, "onReqDestroyExecution> failed - exec(%s), error(%d)", req->execId.ToString().c_str(), eError);

		EzwResDestroyExecution* res = Event::AllocEvent<EzwResDestroyExecution>(e);
		{
			res->eError = eError;
			res->execId = req->execId;
			res->channelId = req->channelId;
			res->serverId = SERVER.GetServerId();
		}
		SERVER.SendToOnlyWorldServer(EventPtr(res));
		return;
	}
	
	MU2_TRACE_LOG(LogCategory::JOIN, "onReqDestroyExecution> - exec(%s)", req->execId.ToString().c_str());
}

void ZoneHandler::onELoopbackNtfEraseExecutions( EventPtr& e )
{
	ELoopbackNtfEraseExecutions* req = static_cast<ELoopbackNtfEraseExecutions*>(e.RawPtr());

	ErrorJoin::Error eError = theExecutionManager.OnELoopbackNtfEraseExecutions(e);
	
	// 실패, 성공 모두 패킷전송
	EzwResDestroyExecution* resp = Event::AllocEvent<EzwResDestroyExecution>(e);
	{
		resp->eError = eError;
		resp->execId = req->execZoneId;
		resp->serverId = SERVER.GetServerId();
	}	
	SERVER.SendToOnlyWorldServer(EventPtr(resp));

	if (eError != ErrorJoin::SUCCESS)
	{
		MU2_ERROR_LOG(LogCategory::JOIN, "onELoopbackNtfEraseExecutions failed - exec(%s), error(%d)", req->execZoneId.ToString().c_str(), eError);
	}
	else
	{
		MU2_TRACE_LOG(LogCategory::JOIN, "onELoopbackNtfEraseExecutions - exec(%s), error(%d)", req->execZoneId.ToString().c_str(), eError);
	}
	
}

void ZoneHandler::onEczReqZoneDirectRelay( EventPtr& e )
{	
	ErrorJoin::Error eError = theExecutionManager.OnEczReqZoneDirectRelay(e);

	if (eError != ErrorJoin::SUCCESS)
	{
		MU2_ERROR_LOG(LogCategory::JOIN, "onEczReqZoneDirectRelay> failed, sessionKey(%u), error(%d)", e->GetIdxAsSessionKey(), eError );	

		EzcResZoneDirectRelay* res = Event::AllocEvent<EzcResZoneDirectRelay>(e);
		res->eError = eError;
		SERVER.Send(EventSetter::GetIdxAsSessionKey(e), EventPtr(res));

		SERVER.DisconnectLateWithReason(e->GetIdxAsSessionKey(), 500, DisReason::DirectRelayFailed);
		return;
	}
}


//void ZoneHandler::onEwzReqCreateInvasionMission(EventPtr& e)
//{
//	EwzReqCreateInvasionMission* req = static_cast<EwzReqCreateInvasionMission*>(e.RawPtr());
//
//	ErrorJoin::Error eError = theExecutionManager.NotifySectorRunner(req->defenderExecZoneId, e);
//	VERIFY_DO(eError == ErrorJoin::SUCCESS,
//
//		EsbResCreateInvasionMission* res = NEW EsbResCreateInvasionMission;
//		res->requestorWorldId = req->requestorWorldId;
//		res->requestorExecZoneId = req->requestorExecZoneId;
//		res->eError = ErrorInvasion::E_INVALID_INVASION_EXECUTION;
//		SERVER.SendToBridge(EventPtr(res));
//	);
//}

//void ZoneHandler::onEbzResStartInvasion(EventPtr& e)
//{
//	EbzResStartInvasion* res = static_cast<EbzResStartInvasion*>(e.RawPtr());
//	ErrorJoin::Error eError = theExecutionManager.NotifySectorRunner(res->requestorExecZoneId, e);
//	VERIFY_RETURN(eError == ErrorJoin::SUCCESS, );
//}
//
//void ZoneHandler::onEbzNtfEndInvasion(EventPtr& e)
//{
//	EbzNtfEndInvasion* ntf = static_cast<EbzNtfEndInvasion*>(e.RawPtr());
//	ErrorJoin::Error eError = theExecutionManager.NotifySectorRunner(ntf->executionZoneId, e);
//	VERIFY_RETURN(eError == ErrorJoin::SUCCESS, );
//}

void ZoneHandler::onEwzNtfKickoutDuplicateLogin(EventPtr& e)
{
	SessionKey sessionKey = theExecutionManager.GetClientSessionKeyByCharId(e->GetKey());
	if (sessionKey > 0)
	{
		SERVER.Disconnect(sessionKey);
		MU2_INFO_LOG(LogCategory::CONTENTS, L"onEwzNtfKickoutDuplicateLogin. session:%u, charId(%u)", sessionKey, e->GetKey());
	}

	
}
void ZoneHandler::onResDbNothing(EventPtr& e)
{
	EventResult* recv = static_cast<EventResult*>(e.RawPtr());
	
	if (recv->eError != ErrorDB::SUCCESS)
	{
		MU2_WARN_LOG(core::LogCategory::DATABASE, L"Database query result failed - %s, result(%d)", recv->strQuery.c_str(), recv->eError);
	}
}

void ZoneHandler::onNotifySectorByExecutionKey(EventPtr& e)
{
	ErrorJoin::Error eError = theExecutionManager.OnNotifySectorByExecutionKey(e);
	if (eError != ErrorJoin::SUCCESS)
	{
		EventSectorByExecId* req = dynamic_cast<EventSectorByExecId*>(e.RawPtr());
		MU2_ERROR_LOG(LogCategory::CONTENTS, "onNotifySectorByExecutionKey failed - %s, exec(%s)", e->GetEventname(), req->GetKeyAsExecId().ToString().c_str());
	}
}

void ZoneHandler::onNotifySector( EventPtr& e )
{
	ErrorJoin::Error eError = theExecutionManager.NotifySectorRunner(EventSetter::GetKeyAsCharId(e), e);
	if (eError != ErrorJoin::SUCCESS)
	{
		MU2_ERROR_LOG(LogCategory::CONTENTS, "onNotifySector failed - %s, charid(%d)", e->GetEventname(), EventSetter::GetKeyAsCharId(e));
	}
}

void ZoneHandler::onNotifySectorErrorReturn(EventPtr& e)
{
	ErrorJoin::Error eError = theExecutionManager.OnNotifySectorErrorReturn(e);

	VERIFY_DO(eError == ErrorJoin::SUCCESS, SERVER.Send(EventSetter::GetIdxAsSessionKey(e), e)	);
}

void ZoneHandler::onNotifyAllSector( EventPtr& e )
{
	ErrorJoin::Error eError = theExecutionManager.OnNotifyAllSector(e);
	VERIFY_RETURN(eError == ErrorJoin::SUCCESS, );
}

void ZoneHandler::onNotifySectorController( EventPtr& e )
{
	ErrorJoin::Error eError = theExecutionManager.OnNotifySectorController(e);
	VERIFY_RETURN(eError == ErrorJoin::SUCCESS, );
}

// 섹터에서 진출 처리 결과가 도착
void ZoneHandler::onResLeaveExecution( EventPtr& e )
{	
	theExecutionManager.OnEzwResLeaveExecution(e);
	SERVER.Send(EventSetter::GetIdxAsSessionKey(e), e);
}

// 클라에서 요청한 로그 아웃 처리 결과
void ZoneHandler::onDbLogout( EventPtr& e )
{
	ErrorJoin::Error eError = theExecutionManager.OnDbLogout(e);
	UNREFERENCED_PARAMETER(eError);
	//VERIFY_RETURN(eError == ErrorJoin::SUCCESS, );
}

void ZoneHandler::onReqSectorLuaReload(EventPtr& e)
{	
	ErrorJoin::Error eError = theExecutionManager.OnNotifyAllSector(e);
	VERIFY_RETURN(eError == ErrorJoin::SUCCESS, );
}

// bridge 서버로 받은 메시지를 섹터에 전달한다.
void ZoneHandler::onMissionmapSectorEvent( EventPtr& e )
{
	SectorEvent* sectorEvent = static_cast<SectorEvent*>( e.RawPtr() );
	ErrorJoin::Error eError = theExecutionManager.NotifySectorRunner(sectorEvent->executionZoneId, e);
	VERIFY_RETURN(eError == ErrorJoin::SUCCESS, );
}

// 보스룸 포탈 정보를 설정한다. 
void ZoneHandler::onReqSetBossPortalSectorInfo( EventPtr& e )
{
	EwzReqSetBossPortalSectorInfo* req = static_cast<EwzReqSetBossPortalSectorInfo*>( e.RawPtr() );
	ErrorJoin::Error eError = theExecutionManager.NotifySectorRunner(req->bossRoomSectorExecutionZoneId, e);
	VERIFY_RETURN(eError == ErrorJoin::SUCCESS, );
}

void ZoneHandler::onReqChangePortalState( EventPtr& e )
{	
	EzwReqChangePortalState* req = static_cast<EzwReqChangePortalState*>(e.RawPtr());
	ErrorJoin::Error eError = theExecutionManager.NotifySectorRunner(req->executionZoneId, e);
	VERIFY_RETURN(eError == ErrorJoin::SUCCESS, );
}


void ZoneHandler::onEwzNtfSyncLoadCompletedParty( EventPtr& e )
{
	EwzNtfSyncLoadCompletedParty* ntf = static_cast<EwzNtfSyncLoadCompletedParty*>( e.RawPtr() );
	ErrorJoin::Error eError = theExecutionManager.NotifySectorRunner(ntf->executionZoneId, e);
	VERIFY_RETURN(eError == ErrorJoin::SUCCESS, );
}

void ZoneHandler::onNtfKnightageUpdateAll(EventPtr & e)
{
	// 플레이어가 존서버에 입장하기 전이라 섹터에 알려주지 않아도 된다.
	ENtfKnightageUpdateAll* ntf = static_cast<ENtfKnightageUpdateAll*>(e.RawPtr());
	
	for (auto& info : ntf->knightageZoneInfos)
	{
		if (!theFieldKnightageManager.Add(info))
		{
			MU2_ERROR_LOG( LogCategory::CONTENTS, "ZoneHandler::onNtfKnightageUpdateAll> failed addition FieldKnightage(world = %d, id = %u, %d)", info.guid.worldId, info.guid.knightageId, info.level);
		}
	}
}

void ZoneHandler::onNtfKnightageDominionBuffActivation(EventPtr& e)
{
	ENtfKnightageDominionBuffActivation* ntf = static_cast<ENtfKnightageDominionBuffActivation*>(e.RawPtr());

	FieldKnightagePtr knightagePtr = theFieldKnightageManager.Get(SERVER.GetWorldId(), ntf->knightageId);
	if (theFieldKnightageManager.AddWorldBuffInfo(knightagePtr, ntf->worldBuffInfo))
	{
		theFieldKnightageManager.MakeWorldBuffList();

		ELoopbackKnightageDominionBuffCast* loopback = new ELoopbackKnightageDominionBuffCast;
		loopback->knightageId = knightagePtr->GetGuid().knightageId;
		loopback->knightageName = knightagePtr->GetName();
		loopback->knightageEmblem = knightagePtr->GetEmblem();
		loopback->buffId = ntf->worldBuffInfo.buffId;
		loopback->buffEndTime = ntf->worldBuffInfo.buffBeginTime.ToDateTime() + DateTimeSpan(0, 0, ntf->worldBuffInfo.buffMinutes, 0);
		
		theExecutionManager.NotifySectorRunnerAll(EventPtr(loopback));
	}
}

void ZoneHandler::onNtfKnightageLoaded(EventPtr & e)
{
	ENtfKnightageLoaded* ntf = static_cast<ENtfKnightageLoaded*>(e.RawPtr());
		
	if (ntf->knightageZoneInfo.guid.worldId == SERVER.GetWorldId())
	{
		if (!theFieldKnightageManager.Add(ntf->knightageZoneInfo))
		{
			MU2_ERROR_LOG(  LogCategory::CONTENTS, "ZoneHandler::onNtfKnightageLoaded> failed addition FieldKnightage (%s)", ntf->knightageZoneInfo.name.c_str());
		}
	}
	else 
	{
		// 다른 월드의 기사단 정보는 유저들이 돌아가는 시점과 찾아오는 시점으로 인해 바로 삭제하지 못한다.
		// 그래서 추가 삭제보다는 업데이트를 이용해서 등록해놓고 오지 않거나 다시 오는 유저를 위해 적용되어야 한다.
		if (theFieldKnightageManager.UpdateForOtherWorld(ntf->knightageZoneInfo) ||
			theFieldKnightageManager.Add(ntf->knightageZoneInfo))
		{
			theFieldKnightageManager.UpdateRemoveKnightageInfo(ntf->knightageZoneInfo);
		}
	}
}

void ZoneHandler::onNtfKnightageChangedLevel(EventPtr & e)
{
	ENtfKnightageChangedLevel* ntf = static_cast<ENtfKnightageChangedLevel*>(e.RawPtr());

	FieldKnightagePtr knightagePtr = theFieldKnightageManager.Get(SERVER.GetWorldId(), ntf->knightageId);
	VERIFY_RETURN(knightagePtr, );
	
	knightagePtr->UpdateLevel(ntf->level, ntf->zoneSkillList);
	
	ErrorJoin::Error eError = theExecutionManager.NotifySectorRunnerAll(e);
	VERIFY_RETURN(eError == ErrorJoin::SUCCESS, );	
}

void ZoneHandler::onNtfKnightageSkillChanged(EventPtr& e)
{
	ENtfKnightageSkillChanged* ntf = static_cast<ENtfKnightageSkillChanged*>(e.RawPtr());

	FieldKnightagePtr knightagePtr = theFieldKnightageManager.Get(SERVER.GetWorldId(), ntf->knightageId);
	VERIFY_RETURN(knightagePtr, );

	knightagePtr->ResetSkills();
	knightagePtr->UpdateLevel(ntf->level, ntf->zoneSkillList);

	ErrorJoin::Error eError = theExecutionManager.NotifySectorRunnerAll(e);
	VERIFY_RETURN(eError == ErrorJoin::SUCCESS, );
}

void ZoneHandler::onNtfKnightageChangedAltar(EventPtr & e)
{
	ENtfKnightageChangedAltar* ntf = static_cast<ENtfKnightageChangedAltar*>(e.RawPtr());

	FieldKnightagePtr knightagePtr = theFieldKnightageManager.Get(SERVER.GetWorldId(), ntf->knightageId);
	VERIFY_RETURN(knightagePtr, );
	
	knightagePtr->UpdateAltar(ntf->zoneSkillList);

	ErrorJoin::Error eError = theExecutionManager.NotifySectorRunnerAll(e);
	VERIFY_RETURN(eError == ErrorJoin::SUCCESS, );
}

void ZoneHandler::onNtfKnightageChangedEmblem(EventPtr & e)
{
	ENtfKnightageChangedEmblem* ntf = static_cast<ENtfKnightageChangedEmblem*>(e.RawPtr());
	FieldKnightagePtr knightagePtr = theFieldKnightageManager.Get(SERVER.GetWorldId(), ntf->knightageId);
	VERIFY_RETURN(knightagePtr, );
	
	knightagePtr->UpdateEmblem(ntf->emblem);

	ErrorJoin::Error eError = theExecutionManager.NotifySectorRunnerAll(e);
	VERIFY_RETURN(eError == ErrorJoin::SUCCESS, );
}

void ZoneHandler::onNtfKnightageChangedActive(EventPtr & e)
{
	ENtfKnightageChangedActive* ntf = static_cast<ENtfKnightageChangedActive*>(e.RawPtr());

	FieldKnightagePtr knightagePtr = theFieldKnightageManager.Get(SERVER.GetWorldId(), ntf->knightageId);
	VERIFY_RETURN(knightagePtr, );
}

void ZoneHandler::onNtfKnightageChangedRanking( EventPtr& e )
{
	ENtfKnightageChangedRanking* ntf = static_cast<ENtfKnightageChangedRanking*>(e.RawPtr());
	FieldKnightagePtr knightagePtr = theFieldKnightageManager.Get(SERVER.GetWorldId(), ntf->knightageId );
	MU2_ASSERT( knightagePtr != nullptr );
	if (knightagePtr != nullptr)
	{
		knightagePtr->UpdateRanking( ntf->ranking );
		theExecutionManager.NotifySectorRunnerAll(e);
	}
}

void ZoneHandler::onNtfKnightageBreakup(EventPtr & e)
{
	ENtfKnightageBreakup* ntf = static_cast<ENtfKnightageBreakup*>(e.RawPtr());
	
	theFieldKnightageManager.ReserveRemove(knightage::FieldGuid(SERVER.GetWorldId(), ntf->knightageId));
}

void ZoneHandler::onNtfRankingStoneFirstRankerToZone( EventPtr& e )
{
	ENtfRankingStoneFirstRankerToZone* ntf = static_cast<ENtfRankingStoneFirstRankerToZone*>(e.RawPtr());
	theStatueSystem.AddRankingInfo(ntf->rankingType, ntf->partyIndex, ntf->firstRankerIds);
}

void ZoneHandler::onReqZoneCorrectDateTime( EventPtr& e )
{
	EReqZoneCorrectDateTime* req = static_cast<EReqZoneCorrectDateTime*>( e.RawPtr() );

	DateTimeCorrector::GetInstance().SetCorrectDateTimeSpan( DateTimeSpan( req->nDays, req->nHours, req->nMins, req->nSecs) );

	std::wstring output;
	DateTime::GetPresentTime().ToDateString( L"%y-%m-%d %H:%M:%S", output );
	MU2_TRACE_LOG( shared::LogCategory::CONTENTS,
		L"onCorrectServerDateTime> Server DateTime is Corrected.[Days: %d][Hours: %d][Mins: %d][Secs: %d][CurrentTime:%s]"
		, req->nDays, req->nHours, req->nMins, req->nSecs, output.c_str() );
}

void ZoneHandler::onNtfKnightageChangedName( EventPtr& e )
{
	ENtfKnightageChangedName *ntf = static_cast<ENtfKnightageChangedName*>(e.RawPtr());

	FieldKnightagePtr knightagePtr = theFieldKnightageManager.Get(SERVER.GetWorldId(), ntf->knightageId);
	VERIFY_RETURN(knightagePtr, );
	
	knightagePtr->UpdateName(ntf->newName);

	ErrorJoin::Error eError = theExecutionManager.NotifySectorRunnerAll(e);
	VERIFY_RETURN(eError == ErrorJoin::SUCCESS, );	
}

void ZoneHandler::onNtfKnightageDelegateZone(EventPtr& e)
{
	ENtfKnightageDelegateZone *ntf = static_cast<ENtfKnightageDelegateZone*>(e.RawPtr());

	FieldKnightagePtr knightagePtr = theFieldKnightageManager.Get(SERVER.GetWorldId(), ntf->knightageId);
	MU2_ASSERT(knightagePtr != nullptr);

	if (knightagePtr != nullptr)
	{
		knightagePtr->DelegateMatser(ntf->newMasterId);
	}
}

void ZoneHandler::onNtfGameTouramentInfo( EventPtr& e )
{
	EwzGameTournamentInfo *req = static_cast<EwzGameTournamentInfo*>(e.RawPtr());
	req;
	MU2_INFO_LOG( LogCategory::CONTENTS, "onNtfGameTouramentInfo> Req [TournamentTurnId:%d]", req->dominionTournamentId );

	theStatueSystem.SetCurrentTournamentTurnId( (TournamentTurnId)req->dominionTournamentId );
}

void ZoneHandler::onNtfGameEventStart( EventPtr& e )
{
	ENtfGameEventStart *req = static_cast<ENtfGameEventStart*>(e.RawPtr());

	theEventSystem.UpdateEventStart(req->vecEvents);
}

void ZoneHandler::onNtfGameEventEnd( EventPtr& e )
{
	ENtfGameEventEnd *req = static_cast<ENtfGameEventEnd*>(e.RawPtr());

	theEventSystem.UpdateEventEnd( req->vecEvents );
}

void ZoneHandler::onNtfGameEventList(EventPtr& e)
{
	ENtfGameEventList *req = static_cast<ENtfGameEventList*>(e.RawPtr());

	theEventSystem.SetupFromWorld(req->eventLists, req->completeEvents, req->bossKillEvents, req->advantureEvent);

}

void ZoneHandler::onNtfSeasonInfoUpdate( EventPtr& e )
{
	auto ntf = static_cast<ENtfSeasonInfoUpdate*>( e.RawPtr() );

	theSeasonSystem.SetSeasonData( ntf->seasonId, static_cast<UInt8>(ntf->state), ntf->openDate, ntf->closeDate );
}

void ZoneHandler::onEwzNtfBlockTimeForExchangeItemRegistration(EventPtr& e)
{
	auto ntf = static_cast<EwzNtfBlockTimeForExchangeItemRegistration*>(e.RawPtr());

	SERVER.SetBlockTimeForExchangeItemRegistration(ntf->start, ntf->end);
}

void ZoneHandler::onEztNtfSpeedHack(EventPtr& e)
{
	EztNtfSpeedHack* ntf = static_cast<EztNtfSpeedHack*>(e.RawPtr());
	SERVER.SetspeedHackCheck(ntf->on);

	if (ntf->on)
	{
		MU2_INFO_LOG(LogCategory::CONTENTS, L"<SpeedHack Check On!!>");
	}
	else
	{
		MU2_INFO_LOG(LogCategory::CONTENTS, L"<SpeedHack Check Off!!>");
	}
}

void ZoneHandler::onELoopbackMoveLatencyStatInfo(EventPtr& e)
{
	ELoopbackMoveLatencyStatInfo* ntf = static_cast<ELoopbackMoveLatencyStatInfo*>(e.RawPtr());
	for (auto info : ntf->vecSpeedHackLatencyInfo)
	{

		auto found = std::find_if(m_vecSpeedHackLatencyInfo.begin(), m_vecSpeedHackLatencyInfo.end(),
			[&](const SpeedHackLatencyInfo& latencyInfo)
		{
			return latencyInfo.min == info.min && latencyInfo.max == info.max;
		});

		if (found == m_vecSpeedHackLatencyInfo.end())
		{
			m_vecSpeedHackLatencyInfo.emplace_back(info);
		}
		else
		{
			found->count += info.count;
		}

	}
}

#ifdef __SectorRunner_Monitor__jason_180523__
void ZoneHandler::onEwzNtfShutdownZoneServerWitDump(EventPtr& e)
{
	UNREFERENCED_PARAMETER(e);

	MU2_FATAL_LOG(LogCategory::SYSTEM, "EwzNtfShutdownZoneServerWitDump> invoke crash !!!!!!!!!!");

	volatile Int32 Div = 0;
	volatile Int32 crashDump = 1 / Div;

	UNREFERENCED_PARAMETER(crashDump);
}
#endif

void ZoneHandler::updateSpeedHackLatencyLog()
{
	std::wstring logString = L"<SpeedHack MoveLatency>";

	UInt32 sumCount = 0;
	for (auto info : m_vecSpeedHackLatencyInfo)
	{
		sumCount += info.count;
	}
	if (sumCount == 0)
	{
		return;
	}

	wchar_t buf[256] = { 0, };
	for (auto info : m_vecSpeedHackLatencyInfo)
	{
		swprintf_s(buf, 256, L"[%d - %d : %d(%d)], ", info.min, info.max, info.count, info.count*100/ sumCount);
		logString += buf;
	}
	
	MU2_INFO_LOG(LogCategory::CONTENTS, L"%s", logString.c_str());
}


#ifdef __Patch_Quest_ProgressType_Add_Period_by_cheolhoon_20181114
void ZoneHandler::onNtfPeriodQuestCheckTimeChange(EventPtr& e)
{
	EwzNtfPeriodQuestCheckTimeChange* ntf = static_cast<EwzNtfPeriodQuestCheckTimeChange*>(e.RawPtr());

	theQuestSystem.SetPeriodQuestTime(ntf->periodQuestCheckTime);
}
#endif



#ifdef __Patch_Teacher_And_Student_System_by_ch_20190604
void ZoneHandler::onENtfTeacherAndStudentChangeBuff(EventPtr& e)
{
	ENtfTeacherAndStudentChangeBuff* ntf = static_cast<ENtfTeacherAndStudentChangeBuff*>(e.RawPtr());

	theExecutionManager.NotifySectorRunner(ntf->charId, e);
}
#endif // __Patch_Teacher_And_Student_System_by_ch_20190604

} // namespace mu2
