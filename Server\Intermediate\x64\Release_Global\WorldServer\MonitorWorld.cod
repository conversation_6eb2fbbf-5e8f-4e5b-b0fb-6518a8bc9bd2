; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	??0exception@std@@QEAA@AEBV01@@Z		; std::exception::exception
PUBLIC	?what@exception@std@@UEBAPEBDXZ			; std::exception::what
PUBLIC	??_Gexception@std@@UEAAPEAXI@Z			; std::exception::`scalar deleting destructor'
PUBLIC	??0bad_alloc@std@@QEAA@AEBV01@@Z		; std::bad_alloc::bad_alloc
PUBLIC	??_Gbad_alloc@std@@UEAAPEAXI@Z			; std::bad_alloc::`scalar deleting destructor'
PUBL<PERSON>	??0bad_array_new_length@std@@QEAA@XZ		; std::bad_array_new_length::bad_array_new_length
PUBLIC	??1bad_array_new_length@std@@UEAA@XZ		; std::bad_array_new_length::~bad_array_new_length
PUBLIC	??0bad_array_new_length@std@@QEAA@AEBV01@@Z	; std::bad_array_new_length::bad_array_new_length
PUBLIC	??_Gbad_array_new_length@std@@UEAAPEAXI@Z	; std::bad_array_new_length::`scalar deleting destructor'
PUBLIC	?_Throw_bad_array_new_length@std@@YAXXZ		; std::_Throw_bad_array_new_length
PUBLIC	?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
PUBLIC	?_Xlen_string@std@@YAXXZ			; std::_Xlen_string
PUBLIC	?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z	; std::allocator<wchar_t>::allocate
PUBLIC	??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
PUBLIC	??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
PUBLIC	??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
PUBLIC	?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
PUBLIC	?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate
PUBLIC	??1?$_Compressed_pair@V?$allocator@_W@std@@V?$_String_val@U?$_Simple_types@_W@std@@@2@$00@std@@QEAA@XZ ; std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>::~_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>
PUBLIC	??$_Construct@$00PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<1,wchar_t const *>
PUBLIC	??$_Construct@$01PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<2,wchar_t const *>
PUBLIC	??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
PUBLIC	??_GIMonitor@mu2@@UEAAPEAXI@Z			; mu2::IMonitor::`scalar deleting destructor'
PUBLIC	??0MonitorWorld@mu2@@QEAA@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z ; mu2::MonitorWorld::MonitorWorld
PUBLIC	??1MonitorWorld@mu2@@UEAA@XZ			; mu2::MonitorWorld::~MonitorWorld
PUBLIC	?Initialize@MonitorWorld@mu2@@MEAA_NXZ		; mu2::MonitorWorld::Initialize
PUBLIC	?Update@MonitorWorld@mu2@@MEAAXXZ		; mu2::MonitorWorld::Update
PUBLIC	??_GMonitorWorld@mu2@@UEAAPEAXI@Z		; mu2::MonitorWorld::`scalar deleting destructor'
PUBLIC	??_7exception@std@@6B@				; std::exception::`vftable'
PUBLIC	??_C@_0BC@EOODALEL@Unknown?5exception@		; `string'
PUBLIC	??_7bad_alloc@std@@6B@				; std::bad_alloc::`vftable'
PUBLIC	??_7bad_array_new_length@std@@6B@		; std::bad_array_new_length::`vftable'
PUBLIC	??_C@_0BF@KINCDENJ@bad?5array?5new?5length@	; `string'
PUBLIC	??_R0?AVexception@std@@@8			; std::exception `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
PUBLIC	_TI3?AVbad_array_new_length@std@@
PUBLIC	_CTA3?AVbad_array_new_length@std@@
PUBLIC	??_R0?AVbad_array_new_length@std@@@8		; std::bad_array_new_length `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
PUBLIC	??_R0?AVbad_alloc@std@@@8			; std::bad_alloc `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
PUBLIC	??_C@_0BA@JFNIOLAK@string?5too?5long@		; `string'
PUBLIC	?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A ; mu2::ISingleton<mu2::Logger>::inst
PUBLIC	??_R4exception@std@@6B@				; std::exception::`RTTI Complete Object Locator'
PUBLIC	??_R3exception@std@@8				; std::exception::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2exception@std@@8				; std::exception::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@exception@std@@8			; std::exception::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4bad_array_new_length@std@@6B@		; std::bad_array_new_length::`RTTI Complete Object Locator'
PUBLIC	??_R3bad_array_new_length@std@@8		; std::bad_array_new_length::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2bad_array_new_length@std@@8		; std::bad_array_new_length::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@bad_array_new_length@std@@8	; std::bad_array_new_length::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@bad_alloc@std@@8			; std::bad_alloc::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R3bad_alloc@std@@8				; std::bad_alloc::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2bad_alloc@std@@8				; std::bad_alloc::`RTTI Base Class Array'
PUBLIC	??_R4bad_alloc@std@@6B@				; std::bad_alloc::`RTTI Complete Object Locator'
PUBLIC	??_R17?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Base Class Descriptor at (8,-1,0,64)'
PUBLIC	??_R0?AV?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> > `RTTI Type Descriptor'
PUBLIC	??_R3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_7IMonitor@mu2@@6B@				; mu2::IMonitor::`vftable'
PUBLIC	??_7MonitorWorld@mu2@@6B@			; mu2::MonitorWorld::`vftable'
PUBLIC	??_C@_1DC@NAPNNLEP@?$AA?4?$AA?4?$AA?1?$AAc?$AAo?$AAn?$AAf?$AAi?$AAg?$AA?1?$AAl?$AAo?$AAg?$AA_?$AAw@ ; `string'
PUBLIC	??_R4IMonitor@mu2@@6B@				; mu2::IMonitor::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVIMonitor@mu2@@@8			; mu2::IMonitor `RTTI Type Descriptor'
PUBLIC	??_R3IMonitor@mu2@@8				; mu2::IMonitor::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2IMonitor@mu2@@8				; mu2::IMonitor::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@IMonitor@mu2@@8			; mu2::IMonitor::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4MonitorWorld@mu2@@6B@			; mu2::MonitorWorld::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVMonitorWorld@mu2@@@8			; mu2::MonitorWorld `RTTI Type Descriptor'
PUBLIC	??_R3MonitorWorld@mu2@@8			; mu2::MonitorWorld::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2MonitorWorld@mu2@@8			; mu2::MonitorWorld::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@MonitorWorld@mu2@@8		; mu2::MonitorWorld::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@MonitorServerCommon@mu2@@8	; mu2::MonitorServerCommon::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R0?AVMonitorServerCommon@mu2@@@8		; mu2::MonitorServerCommon `RTTI Type Descriptor'
PUBLIC	??_R3MonitorServerCommon@mu2@@8			; mu2::MonitorServerCommon::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2MonitorServerCommon@mu2@@8			; mu2::MonitorServerCommon::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@MonitorServer@mu2@@8		; mu2::MonitorServer::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R0?AVMonitorServer@mu2@@@8			; mu2::MonitorServer `RTTI Type Descriptor'
PUBLIC	??_R3MonitorServer@mu2@@8			; mu2::MonitorServer::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2MonitorServer@mu2@@8			; mu2::MonitorServer::`RTTI Base Class Array'
EXTRN	_purecall:PROC
EXTRN	??2@YAPEAX_K@Z:PROC				; operator new
EXTRN	??3@YAXPEAX_K@Z:PROC				; operator delete
EXTRN	?__global_delete@@YAXPEAX_K@Z:PROC		; __global_delete
EXTRN	atexit:PROC
EXTRN	__std_terminate:PROC
EXTRN	_invoke_watson:PROC
EXTRN	memcpy:PROC
EXTRN	wcslen:PROC
EXTRN	free:PROC
EXTRN	__std_exception_copy:PROC
EXTRN	__std_exception_destroy:PROC
EXTRN	??_Eexception@std@@UEAAPEAXI@Z:PROC		; std::exception::`vector deleting destructor'
EXTRN	??_Ebad_alloc@std@@UEAAPEAXI@Z:PROC		; std::bad_alloc::`vector deleting destructor'
EXTRN	??_Ebad_array_new_length@std@@UEAAPEAXI@Z:PROC	; std::bad_array_new_length::`vector deleting destructor'
EXTRN	?_Xlength_error@std@@YAXPEBD@Z:PROC		; std::_Xlength_error
EXTRN	??1Logger@mu2@@UEAA@XZ:PROC			; mu2::Logger::~Logger
EXTRN	??0Logger@mu2@@AEAA@XZ:PROC			; mu2::Logger::Logger
EXTRN	?EnableLogType@Logger@mu2@@QEAAXE@Z:PROC	; mu2::Logger::EnableLogType
EXTRN	??_EIMonitor@mu2@@UEAAPEAXI@Z:PROC		; mu2::IMonitor::`vector deleting destructor'
EXTRN	?ResetLogTypeFilter@MonitorServerCommon@mu2@@UEAAXXZ:PROC ; mu2::MonitorServerCommon::ResetLogTypeFilter
EXTRN	??0MonitorServerCommon@mu2@@IEAA@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z:PROC ; mu2::MonitorServerCommon::MonitorServerCommon
EXTRN	?Initialize@MonitorServerCommon@mu2@@MEAA_NXZ:PROC ; mu2::MonitorServerCommon::Initialize
EXTRN	?UnInitialize@MonitorServerCommon@mu2@@MEAA_NXZ:PROC ; mu2::MonitorServerCommon::UnInitialize
EXTRN	?BuildStatusString@MonitorServerCommon@mu2@@MEAAXAEAV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@I@Z:PROC ; mu2::MonitorServerCommon::BuildStatusString
EXTRN	?SetTitle@MonitorServerCommon@mu2@@MEAAXXZ:PROC	; mu2::MonitorServerCommon::SetTitle
EXTRN	??_EMonitorWorld@mu2@@UEAAPEAXI@Z:PROC		; mu2::MonitorWorld::`vector deleting destructor'
EXTRN	_CxxThrowException:PROC
EXTRN	__CxxFrameHandler4:PROC
EXTRN	??_7type_info@@6B@:BYTE				; type_info::`vftable'
;	COMDAT ?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A
_BSS	SEGMENT
?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A DB 0350H DUP (?) ; mu2::ISingleton<mu2::Logger>::inst
_BSS	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0exception@std@@QEAA@AEBV01@@Z DD imagerel $LN4
	DD	imagerel $LN4+89
	DD	imagerel $unwind$??0exception@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?what@exception@std@@UEBAPEBDXZ DD imagerel $LN5
	DD	imagerel $LN5+56
	DD	imagerel $unwind$?what@exception@std@@UEBAPEBDXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gexception@std@@UEAAPEAXI@Z DD imagerel $LN10
	DD	imagerel $LN10+83
	DD	imagerel $unwind$??_Gexception@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z DD imagerel $LN9
	DD	imagerel $LN9+104
	DD	imagerel $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+83
	DD	imagerel $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_array_new_length@std@@QEAA@XZ DD imagerel $LN14
	DD	imagerel $LN14+95
	DD	imagerel $unwind$??0bad_array_new_length@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1bad_array_new_length@std@@UEAA@XZ DD imagerel $LN14
	DD	imagerel $LN14+47
	DD	imagerel $unwind$??1bad_array_new_length@std@@UEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z DD imagerel $LN14
	DD	imagerel $LN14+54
	DD	imagerel $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z DD imagerel $LN20
	DD	imagerel $LN20+83
	DD	imagerel $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Throw_bad_array_new_length@std@@YAXXZ DD imagerel $LN3
	DD	imagerel $LN3+37
	DD	imagerel $unwind$?_Throw_bad_array_new_length@std@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z DD imagerel $LN5
	DD	imagerel $LN5+159
	DD	imagerel $unwind$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Xlen_string@std@@YAXXZ DD imagerel $LN3
	DD	imagerel $LN3+22
	DD	imagerel $unwind$?_Xlen_string@std@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z DD imagerel $LN13
	DD	imagerel $LN13+162
	DD	imagerel $unwind$?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z DD imagerel $LN226
	DD	imagerel $LN226+287
	DD	imagerel $unwind$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z@4HA DD imagerel ?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z@4HA
	DD	imagerel ?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z@4HA+27
	DD	imagerel $unwind$?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z DD imagerel $LN221
	DD	imagerel $LN221+150
	DD	imagerel $unwind$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z@4HA DD imagerel ?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z@4HA
	DD	imagerel ?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z@4HA+24
	DD	imagerel $unwind$?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ DD imagerel $LN82
	DD	imagerel $LN82+25
	DD	imagerel $unwind$??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ DD imagerel $LN38
	DD	imagerel $LN38+250
	DD	imagerel $unwind$?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DD imagerel $LN62
	DD	imagerel $LN62+266
	DD	imagerel $unwind$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Construct@$00PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z DD imagerel $LN188
	DD	imagerel $LN188+836
	DD	imagerel $unwind$??$_Construct@$00PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Construct@$01PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z DD imagerel $LN173
	DD	imagerel $LN173+746
	DD	imagerel $unwind$??$_Construct@$01PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z DD imagerel $LN7
	DD	imagerel $LN7+150
	DD	imagerel $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GIMonitor@mu2@@UEAAPEAXI@Z DD imagerel $LN104
	DD	imagerel $LN104+107
	DD	imagerel $unwind$??_GIMonitor@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0MonitorWorld@mu2@@QEAA@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z DD imagerel $LN290
	DD	imagerel $LN290+194
	DD	imagerel $unwind$??0MonitorWorld@mu2@@QEAA@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???0MonitorWorld@mu2@@QEAA@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z@4HA DD imagerel ?dtor$0@?0???0MonitorWorld@mu2@@QEAA@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z@4HA
	DD	imagerel ?dtor$0@?0???0MonitorWorld@mu2@@QEAA@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z@4HA+27
	DD	imagerel $unwind$?dtor$0@?0???0MonitorWorld@mu2@@QEAA@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$1@?0???0MonitorWorld@mu2@@QEAA@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z@4HA DD imagerel ?dtor$1@?0???0MonitorWorld@mu2@@QEAA@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z@4HA
	DD	imagerel ?dtor$1@?0???0MonitorWorld@mu2@@QEAA@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z@4HA+24
	DD	imagerel $unwind$?dtor$1@?0???0MonitorWorld@mu2@@QEAA@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1MonitorWorld@mu2@@UEAA@XZ DD imagerel $LN186
	DD	imagerel $LN186+80
	DD	imagerel $unwind$??1MonitorWorld@mu2@@UEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Initialize@MonitorWorld@mu2@@MEAA_NXZ DD imagerel $LN8
	DD	imagerel $LN8+91
	DD	imagerel $unwind$?Initialize@MonitorWorld@mu2@@MEAA_NXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GMonitorWorld@mu2@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+84
	DD	imagerel $unwind$??_GMonitorWorld@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__E?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A@@YAXXZ DD imagerel ??__E?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A@@YAXXZ
	DD	imagerel ??__E?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A@@YAXXZ+34
	DD	imagerel $unwind$??__E?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__F?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A@@YAXXZ DD imagerel ??__F?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A@@YAXXZ
	DD	imagerel ??__F?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A@@YAXXZ+22
	DD	imagerel $unwind$??__F?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A@@YAXXZ
pdata	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
??inst$initializer$@?$ISingleton@VLogger@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA DQ FLAT:??__E?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A@@YAXXZ ; ??inst$initializer$@?$ISingleton@VLogger@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA
CRT$XCU	ENDS
;	COMDAT ??_R2MonitorServer@mu2@@8
rdata$r	SEGMENT
??_R2MonitorServer@mu2@@8 DD imagerel ??_R1A@?0A@EA@MonitorServer@mu2@@8 ; mu2::MonitorServer::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@IMonitor@mu2@@8
	DD	imagerel ??_R17?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3MonitorServer@mu2@@8
rdata$r	SEGMENT
??_R3MonitorServer@mu2@@8 DD 00H			; mu2::MonitorServer::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	03H
	DD	imagerel ??_R2MonitorServer@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVMonitorServer@mu2@@@8
data$rs	SEGMENT
??_R0?AVMonitorServer@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::MonitorServer `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVMonitorServer@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R1A@?0A@EA@MonitorServer@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@MonitorServer@mu2@@8 DD imagerel ??_R0?AVMonitorServer@mu2@@@8 ; mu2::MonitorServer::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	02H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3MonitorServer@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2MonitorServerCommon@mu2@@8
rdata$r	SEGMENT
??_R2MonitorServerCommon@mu2@@8 DD imagerel ??_R1A@?0A@EA@MonitorServerCommon@mu2@@8 ; mu2::MonitorServerCommon::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@MonitorServer@mu2@@8
	DD	imagerel ??_R1A@?0A@EA@IMonitor@mu2@@8
	DD	imagerel ??_R17?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3MonitorServerCommon@mu2@@8
rdata$r	SEGMENT
??_R3MonitorServerCommon@mu2@@8 DD 00H			; mu2::MonitorServerCommon::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	04H
	DD	imagerel ??_R2MonitorServerCommon@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVMonitorServerCommon@mu2@@@8
data$rs	SEGMENT
??_R0?AVMonitorServerCommon@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::MonitorServerCommon `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVMonitorServerCommon@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R1A@?0A@EA@MonitorServerCommon@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@MonitorServerCommon@mu2@@8 DD imagerel ??_R0?AVMonitorServerCommon@mu2@@@8 ; mu2::MonitorServerCommon::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	03H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3MonitorServerCommon@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@MonitorWorld@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@MonitorWorld@mu2@@8 DD imagerel ??_R0?AVMonitorWorld@mu2@@@8 ; mu2::MonitorWorld::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	04H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3MonitorWorld@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2MonitorWorld@mu2@@8
rdata$r	SEGMENT
??_R2MonitorWorld@mu2@@8 DD imagerel ??_R1A@?0A@EA@MonitorWorld@mu2@@8 ; mu2::MonitorWorld::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@MonitorServerCommon@mu2@@8
	DD	imagerel ??_R1A@?0A@EA@MonitorServer@mu2@@8
	DD	imagerel ??_R1A@?0A@EA@IMonitor@mu2@@8
	DD	imagerel ??_R17?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3MonitorWorld@mu2@@8
rdata$r	SEGMENT
??_R3MonitorWorld@mu2@@8 DD 00H				; mu2::MonitorWorld::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	05H
	DD	imagerel ??_R2MonitorWorld@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVMonitorWorld@mu2@@@8
data$rs	SEGMENT
??_R0?AVMonitorWorld@mu2@@@8 DQ FLAT:??_7type_info@@6B@	; mu2::MonitorWorld `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVMonitorWorld@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4MonitorWorld@mu2@@6B@
rdata$r	SEGMENT
??_R4MonitorWorld@mu2@@6B@ DD 01H			; mu2::MonitorWorld::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVMonitorWorld@mu2@@@8
	DD	imagerel ??_R3MonitorWorld@mu2@@8
	DD	imagerel ??_R4MonitorWorld@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@IMonitor@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@IMonitor@mu2@@8 DD imagerel ??_R0?AVIMonitor@mu2@@@8 ; mu2::IMonitor::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3IMonitor@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2IMonitor@mu2@@8
rdata$r	SEGMENT
??_R2IMonitor@mu2@@8 DD imagerel ??_R1A@?0A@EA@IMonitor@mu2@@8 ; mu2::IMonitor::`RTTI Base Class Array'
	DD	imagerel ??_R17?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3IMonitor@mu2@@8
rdata$r	SEGMENT
??_R3IMonitor@mu2@@8 DD 00H				; mu2::IMonitor::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2IMonitor@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVIMonitor@mu2@@@8
data$rs	SEGMENT
??_R0?AVIMonitor@mu2@@@8 DQ FLAT:??_7type_info@@6B@	; mu2::IMonitor `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVIMonitor@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4IMonitor@mu2@@6B@
rdata$r	SEGMENT
??_R4IMonitor@mu2@@6B@ DD 01H				; mu2::IMonitor::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVIMonitor@mu2@@@8
	DD	imagerel ??_R3IMonitor@mu2@@8
	DD	imagerel ??_R4IMonitor@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_C@_1DC@NAPNNLEP@?$AA?4?$AA?4?$AA?1?$AAc?$AAo?$AAn?$AAf?$AAi?$AAg?$AA?1?$AAl?$AAo?$AAg?$AA_?$AAw@
CONST	SEGMENT
??_C@_1DC@NAPNNLEP@?$AA?4?$AA?4?$AA?1?$AAc?$AAo?$AAn?$AAf?$AAi?$AAg?$AA?1?$AAl?$AAo?$AAg?$AA_?$AAw@ DB '.'
	DB	00H, '.', 00H, '/', 00H, 'c', 00H, 'o', 00H, 'n', 00H, 'f', 00H
	DB	'i', 00H, 'g', 00H, '/', 00H, 'l', 00H, 'o', 00H, 'g', 00H, '_'
	DB	00H, 'w', 00H, 'o', 00H, 'r', 00H, 'l', 00H, 'd', 00H, '.', 00H
	DB	'p', 00H, 'r', 00H, 'o', 00H, 'p', 00H, 00H, 00H ; `string'
CONST	ENDS
;	COMDAT ??_7MonitorWorld@mu2@@6B@
CONST	SEGMENT
??_7MonitorWorld@mu2@@6B@ DQ FLAT:??_R4MonitorWorld@mu2@@6B@ ; mu2::MonitorWorld::`vftable'
	DQ	FLAT:??_EMonitorWorld@mu2@@UEAAPEAXI@Z
	DQ	FLAT:?SetTitle@MonitorServerCommon@mu2@@MEAAXXZ
	DQ	FLAT:?ResetLogTypeFilter@MonitorServerCommon@mu2@@UEAAXXZ
	DQ	FLAT:?Initialize@MonitorWorld@mu2@@MEAA_NXZ
	DQ	FLAT:?UnInitialize@MonitorServerCommon@mu2@@MEAA_NXZ
	DQ	FLAT:?BuildStatusString@MonitorServerCommon@mu2@@MEAAXAEAV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@I@Z
	DQ	FLAT:?Update@MonitorWorld@mu2@@MEAAXXZ
CONST	ENDS
;	COMDAT ??_7IMonitor@mu2@@6B@
CONST	SEGMENT
??_7IMonitor@mu2@@6B@ DQ FLAT:??_R4IMonitor@mu2@@6B@	; mu2::IMonitor::`vftable'
	DQ	FLAT:??_EIMonitor@mu2@@UEAAPEAXI@Z
	DQ	FLAT:_purecall
	DQ	FLAT:_purecall
	DQ	FLAT:_purecall
	DQ	FLAT:_purecall
	DQ	FLAT:_purecall
	DQ	FLAT:_purecall
CONST	ENDS
;	COMDAT ??_R1A@?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 DD imagerel ??_R0?AV?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
rdata$r	ENDS
;	COMDAT ??_R2?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
rdata$r	SEGMENT
??_R2?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 DD imagerel ??_R1A@?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
rdata$r	SEGMENT
??_R3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 DD 00H ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
rdata$r	ENDS
;	COMDAT ??_R0?AV?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@@8
data$rs	SEGMENT
??_R0?AV?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@@8 DQ FLAT:??_7type_info@@6B@ ; AllocatedObject<mu2::CategorisedAllocPolicy<0> > `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AV?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2'
	DB	'@@@@', 00H
data$rs	ENDS
;	COMDAT ??_R17?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
rdata$r	SEGMENT
??_R17?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 DD imagerel ??_R0?AV?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Base Class Descriptor at (8,-1,0,64)'
	DD	00H
	DD	08H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
rdata$r	ENDS
;	COMDAT ??_R4bad_alloc@std@@6B@
rdata$r	SEGMENT
??_R4bad_alloc@std@@6B@ DD 01H				; std::bad_alloc::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVbad_alloc@std@@@8
	DD	imagerel ??_R3bad_alloc@std@@8
	DD	imagerel ??_R4bad_alloc@std@@6B@
rdata$r	ENDS
;	COMDAT ??_R2bad_alloc@std@@8
rdata$r	SEGMENT
??_R2bad_alloc@std@@8 DD imagerel ??_R1A@?0A@EA@bad_alloc@std@@8 ; std::bad_alloc::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@exception@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3bad_alloc@std@@8
rdata$r	SEGMENT
??_R3bad_alloc@std@@8 DD 00H				; std::bad_alloc::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2bad_alloc@std@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@bad_alloc@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@bad_alloc@std@@8 DD imagerel ??_R0?AVbad_alloc@std@@@8 ; std::bad_alloc::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3bad_alloc@std@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@bad_array_new_length@std@@8 DD imagerel ??_R0?AVbad_array_new_length@std@@@8 ; std::bad_array_new_length::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	02H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3bad_array_new_length@std@@8
rdata$r	ENDS
;	COMDAT ??_R2bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R2bad_array_new_length@std@@8 DD imagerel ??_R1A@?0A@EA@bad_array_new_length@std@@8 ; std::bad_array_new_length::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@bad_alloc@std@@8
	DD	imagerel ??_R1A@?0A@EA@exception@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R3bad_array_new_length@std@@8 DD 00H			; std::bad_array_new_length::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	03H
	DD	imagerel ??_R2bad_array_new_length@std@@8
rdata$r	ENDS
;	COMDAT ??_R4bad_array_new_length@std@@6B@
rdata$r	SEGMENT
??_R4bad_array_new_length@std@@6B@ DD 01H		; std::bad_array_new_length::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVbad_array_new_length@std@@@8
	DD	imagerel ??_R3bad_array_new_length@std@@8
	DD	imagerel ??_R4bad_array_new_length@std@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@exception@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@exception@std@@8 DD imagerel ??_R0?AVexception@std@@@8 ; std::exception::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3exception@std@@8
rdata$r	ENDS
;	COMDAT ??_R2exception@std@@8
rdata$r	SEGMENT
??_R2exception@std@@8 DD imagerel ??_R1A@?0A@EA@exception@std@@8 ; std::exception::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3exception@std@@8
rdata$r	SEGMENT
??_R3exception@std@@8 DD 00H				; std::exception::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2exception@std@@8
rdata$r	ENDS
;	COMDAT ??_R4exception@std@@6B@
rdata$r	SEGMENT
??_R4exception@std@@6B@ DD 01H				; std::exception::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVexception@std@@@8
	DD	imagerel ??_R3exception@std@@8
	DD	imagerel ??_R4exception@std@@6B@
rdata$r	ENDS
;	COMDAT ??_C@_0BA@JFNIOLAK@string?5too?5long@
CONST	SEGMENT
??_C@_0BA@JFNIOLAK@string?5too?5long@ DB 'string too long', 00H ; `string'
CONST	ENDS
;	COMDAT _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 DD 010H
	DD	imagerel ??_R0?AVbad_alloc@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0bad_alloc@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVbad_alloc@std@@@8
data$r	SEGMENT
??_R0?AVbad_alloc@std@@@8 DQ FLAT:??_7type_info@@6B@	; std::bad_alloc `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVbad_alloc@std@@', 00H
data$r	ENDS
;	COMDAT _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 DD 00H
	DD	imagerel ??_R0?AVbad_array_new_length@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0bad_array_new_length@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVbad_array_new_length@std@@@8
data$r	SEGMENT
??_R0?AVbad_array_new_length@std@@@8 DQ FLAT:??_7type_info@@6B@ ; std::bad_array_new_length `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVbad_array_new_length@std@@', 00H
data$r	ENDS
;	COMDAT _CTA3?AVbad_array_new_length@std@@
xdata$x	SEGMENT
_CTA3?AVbad_array_new_length@std@@ DD 03H
	DD	imagerel _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
	DD	imagerel _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
	DD	imagerel _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
xdata$x	ENDS
;	COMDAT _TI3?AVbad_array_new_length@std@@
xdata$x	SEGMENT
_TI3?AVbad_array_new_length@std@@ DD 00H
	DD	imagerel ??1bad_array_new_length@std@@UEAA@XZ
	DD	00H
	DD	imagerel _CTA3?AVbad_array_new_length@std@@
xdata$x	ENDS
;	COMDAT _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 DD 00H
	DD	imagerel ??_R0?AVexception@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0exception@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVexception@std@@@8
data$r	SEGMENT
??_R0?AVexception@std@@@8 DQ FLAT:??_7type_info@@6B@	; std::exception `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVexception@std@@', 00H
data$r	ENDS
;	COMDAT ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@
CONST	SEGMENT
??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ DB 'bad array new length', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7bad_array_new_length@std@@6B@
CONST	SEGMENT
??_7bad_array_new_length@std@@6B@ DQ FLAT:??_R4bad_array_new_length@std@@6B@ ; std::bad_array_new_length::`vftable'
	DQ	FLAT:??_Ebad_array_new_length@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT ??_7bad_alloc@std@@6B@
CONST	SEGMENT
??_7bad_alloc@std@@6B@ DQ FLAT:??_R4bad_alloc@std@@6B@	; std::bad_alloc::`vftable'
	DQ	FLAT:??_Ebad_alloc@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT ??_C@_0BC@EOODALEL@Unknown?5exception@
CONST	SEGMENT
??_C@_0BC@EOODALEL@Unknown?5exception@ DB 'Unknown exception', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7exception@std@@6B@
CONST	SEGMENT
??_7exception@std@@6B@ DQ FLAT:??_R4exception@std@@6B@	; std::exception::`vftable'
	DQ	FLAT:??_Eexception@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__F?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__E?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GMonitorWorld@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Initialize@MonitorWorld@mu2@@MEAA_NXZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1MonitorWorld@mu2@@UEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$1@?0???0MonitorWorld@mu2@@QEAA@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???0MonitorWorld@mu2@@QEAA@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??0MonitorWorld@mu2@@QEAA@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z DB 0aH
	DB	00H
	DB	00H
	DB	'"'
	DB	02H
	DB	'j'
	DB	04H
	DB	','
	DB	02H
	DB	090H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??0MonitorWorld@mu2@@QEAA@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z DB 04H
	DB	0eH
	DD	imagerel ?dtor$0@?0???0MonitorWorld@mu2@@QEAA@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z@4HA
	DB	02eH
	DD	imagerel ?dtor$1@?0???0MonitorWorld@mu2@@QEAA@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??0MonitorWorld@mu2@@QEAA@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z DB 028H
	DD	imagerel $stateUnwindMap$??0MonitorWorld@mu2@@QEAA@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z
	DD	imagerel $ip2state$??0MonitorWorld@mu2@@QEAA@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0MonitorWorld@mu2@@QEAA@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z DD 021111H
	DD	0130111H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??0MonitorWorld@mu2@@QEAA@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GIMonitor@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z DD 010901H
	DD	0a209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Construct@$01PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z DD 031701H
	DD	01e0117H
	DD	07010H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Construct@$00PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z DD 031701H
	DD	0200117H
	DD	07010H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DB 06H
	DB	00H
	DB	00H
	DB	089H, 02H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DB 068H
	DD	imagerel $stateUnwindMap$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
	DD	imagerel $ip2state$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DD 010919H
	DD	0e209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ DD 020c01H
	DD	011010cH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z DB 06H
	DB	00H
	DB	00H
	DB	0b4H
	DB	02H
	DB	'b'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z DB 028H
	DD	imagerel $stateUnwindMap$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z
	DD	imagerel $ip2state$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z DD 020f11H
	DD	0700b920fH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z DB 06H
	DB	00H
	DB	00H
	DB	0eeH
	DB	02H
	DB	']', 02H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z DB 028H
	DD	imagerel $stateUnwindMap$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z
	DD	imagerel $ip2state$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z DD 021211H
	DD	0700bf212H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Xlen_string@std@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Throw_bad_array_new_length@std@@YAXXZ DD 010401H
	DD	08204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1bad_array_new_length@std@@UEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_array_new_length@std@@QEAA@XZ DD 010601H
	DD	07006H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z DD 020f01H
	DD	0700b320fH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gexception@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?what@exception@std@@UEBAPEBDXZ DD 010901H
	DD	02209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0exception@std@@QEAA@AEBV01@@Z DD 020f01H
	DD	0700b320fH
xdata	ENDS
; Function compile flags: /Odtp
;	COMDAT ??__F?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A@@YAXXZ
text$yd	SEGMENT
??__F?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A@@YAXXZ PROC ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::Logger>::inst'', COMDAT
  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A ; mu2::ISingleton<mu2::Logger>::inst
  0000b	e8 00 00 00 00	 call	 ??1Logger@mu2@@UEAA@XZ	; mu2::Logger::~Logger
  00010	90		 npad	 1
  00011	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00015	c3		 ret	 0
??__F?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A@@YAXXZ ENDP ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::Logger>::inst''
text$yd	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??__E?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A@@YAXXZ
text$di	SEGMENT
??__E?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A@@YAXXZ PROC ; `dynamic initializer for 'mu2::ISingleton<mu2::Logger>::inst'', COMDAT

; 90   : template<typename T> T ISingleton<T>::inst;

  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A ; mu2::ISingleton<mu2::Logger>::inst
  0000b	e8 00 00 00 00	 call	 ??0Logger@mu2@@AEAA@XZ	; mu2::Logger::Logger
  00010	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??__F?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A@@YAXXZ ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::Logger>::inst''
  00017	e8 00 00 00 00	 call	 atexit
  0001c	90		 npad	 1
  0001d	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00021	c3		 ret	 0
??__E?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A@@YAXXZ ENDP ; `dynamic initializer for 'mu2::ISingleton<mu2::Logger>::inst''
text$di	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h
;	COMDAT ??_GMonitorWorld@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GMonitorWorld@mu2@@UEAAPEAXI@Z PROC			; mu2::MonitorWorld::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00012	e8 00 00 00 00	 call	 ??1MonitorWorld@mu2@@UEAA@XZ ; mu2::MonitorWorld::~MonitorWorld
  00017	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  0001b	83 e0 01	 and	 eax, 1
  0001e	85 c0		 test	 eax, eax
  00020	74 28		 je	 SHORT $LN2@scalar
  00022	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00026	83 e0 04	 and	 eax, 4
  00029	85 c0		 test	 eax, eax
  0002b	75 0d		 jne	 SHORT $LN3@scalar

; 45   : 			::free(ptr);

  0002d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00032	e8 00 00 00 00	 call	 free
  00037	90		 npad	 1
  00038	eb 10		 jmp	 SHORT $LN2@scalar
$LN3@scalar:
  0003a	ba 68 00 00 00	 mov	 edx, 104		; 00000068H
  0003f	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00044	e8 00 00 00 00	 call	 ?__global_delete@@YAXPEAX_K@Z ; __global_delete
  00049	90		 npad	 1
$LN2@scalar:
  0004a	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004f	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00053	c3		 ret	 0
??_GMonitorWorld@mu2@@UEAAPEAXI@Z ENDP			; mu2::MonitorWorld::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MonitorWorld.cpp
;	COMDAT ?Update@MonitorWorld@mu2@@MEAAXXZ
_TEXT	SEGMENT
this$ = 8
?Update@MonitorWorld@mu2@@MEAAXXZ PROC			; mu2::MonitorWorld::Update, COMDAT

; 43   : {

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 44   : 
; 45   : 	//concurrentUserCount = theManagerPlayer.GetConcurrentUser(0);
; 46   : 	//concurrentPCRoomCount = theManagerPlayer.GetConcurrentPCRoomUser(0);
; 47   : 	
; 48   : }

  00005	c3		 ret	 0
?Update@MonitorWorld@mu2@@MEAAXXZ ENDP			; mu2::MonitorWorld::Update
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MonitorWorld.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MonitorWorld.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MonitorWorld.cpp
;	COMDAT ?Initialize@MonitorWorld@mu2@@MEAA_NXZ
_TEXT	SEGMENT
$T1 = 32
$T2 = 40
this$ = 64
?Initialize@MonitorWorld@mu2@@MEAA_NXZ PROC		; mu2::MonitorWorld::Initialize, COMDAT

; 23   : {

$LN8:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 24   : 	if (false == __super::Initialize())

  00009	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0000e	e8 00 00 00 00	 call	 ?Initialize@MonitorServerCommon@mu2@@MEAA_NXZ ; mu2::MonitorServerCommon::Initialize
  00013	0f b6 c0	 movzx	 eax, al
  00016	85 c0		 test	 eax, eax
  00018	75 04		 jne	 SHORT $LN2@Initialize

; 25   : 		return false;	

  0001a	32 c0		 xor	 al, al
  0001c	eb 38		 jmp	 SHORT $LN1@Initialize
$LN2@Initialize:
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  0001e	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A ; mu2::ISingleton<mu2::Logger>::inst
  00025	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MonitorWorld.cpp

; 27   : 	Logger::GetInstance().EnableLogType(shared::LogCategory::ACCOUNT);

  0002a	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  0002f	b2 24		 mov	 dl, 36			; 00000024H
  00031	48 8b c8	 mov	 rcx, rax
  00034	e8 00 00 00 00	 call	 ?EnableLogType@Logger@mu2@@QEAAXE@Z ; mu2::Logger::EnableLogType
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  00039	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VLogger@mu2@@@mu2@@1VLogger@2@A ; mu2::ISingleton<mu2::Logger>::inst
  00040	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MonitorWorld.cpp

; 30   : 	Logger::GetInstance().EnableLogType(shared::LogCategory::CHAT_DATACENTER);

  00045	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
  0004a	b2 16		 mov	 dl, 22
  0004c	48 8b c8	 mov	 rcx, rax
  0004f	e8 00 00 00 00	 call	 ?EnableLogType@Logger@mu2@@QEAAXE@Z ; mu2::Logger::EnableLogType

; 31   : #endif
; 32   : 
; 33   : 
; 34   : #ifdef _DEBUG
; 35   : 	Logger::GetInstance().EnableLogType(shared::LogCategory::WORLD_LOBBY);
; 36   : 	Logger::GetInstance().EnableLogType(shared::LogCategory::DEBUG_BURNING_STAMINA);
; 37   : #endif
; 38   : 
; 39   : 	return true;

  00054	b0 01		 mov	 al, 1
$LN1@Initialize:

; 40   : }

  00056	48 83 c4 38	 add	 rsp, 56			; 00000038H
  0005a	c3		 ret	 0
?Initialize@MonitorWorld@mu2@@MEAA_NXZ ENDP		; mu2::MonitorWorld::Initialize
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MonitorWorld.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File F:\Release_Branch\Server\Development\Framework\Core\Monitor.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MonitorWorld.cpp
;	COMDAT ??1MonitorWorld@mu2@@UEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1MonitorWorld@mu2@@UEAA@XZ PROC			; mu2::MonitorWorld::~MonitorWorld, COMDAT

; 16   : {

$LN186:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7MonitorWorld@mu2@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx
  00018	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 83 c0 38	 add	 rax, 56			; 00000038H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1383 :         _Tidy_deallocate();

  00021	48 8b c8	 mov	 rcx, rax
  00024	e8 00 00 00 00	 call	 ?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate
  00029	90		 npad	 1
; File F:\Release_Branch\Server\Development\Framework\Core\Monitor.h

; 21   : 		virtual~IMonitor() {};

  0002a	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0002f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7IMonitor@mu2@@6B@
  00036	48 89 08	 mov	 QWORD PTR [rax], rcx
  00039	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003e	48 83 c0 18	 add	 rax, 24
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1383 :         _Tidy_deallocate();

  00042	48 8b c8	 mov	 rcx, rax
  00045	e8 00 00 00 00	 call	 ?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate
  0004a	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MonitorWorld.cpp

; 17   : }

  0004b	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0004f	c3		 ret	 0
??1MonitorWorld@mu2@@UEAA@XZ ENDP			; mu2::MonitorWorld::~MonitorWorld
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MonitorWorld.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MonitorWorld.cpp
;	COMDAT ??0MonitorWorld@mu2@@QEAA@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z
_TEXT	SEGMENT
$T1 = 32
tv70 = 40
$T2 = 48
tv136 = 56
tv138 = 64
$T3 = 72
$T4 = 104
this$ = 160
strModuleName$ = 168
??0MonitorWorld@mu2@@QEAA@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z PROC ; mu2::MonitorWorld::MonitorWorld, COMDAT

; 12   : {

$LN290:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 81 ec 98 00
	00 00		 sub	 rsp, 152		; 00000098H

; 9    : 	: MonitorServerCommon(_T("../config/log_world.prop"), strModuleName)

  00011	48 8d 44 24 48	 lea	 rax, QWORD PTR $T3[rsp]
  00016	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
  0001b	48 8d 44 24 68	 lea	 rax, QWORD PTR $T4[rsp]
  00020	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
  00025	48 8b 94 24 a8
	00 00 00	 mov	 rdx, QWORD PTR strModuleName$[rsp]
  0002d	48 8b 4c 24 20	 mov	 rcx, QWORD PTR $T1[rsp]
  00032	e8 00 00 00 00	 call	 ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  00037	48 89 44 24 28	 mov	 QWORD PTR tv70[rsp], rax
  0003c	48 8b 44 24 28	 mov	 rax, QWORD PTR tv70[rsp]
  00041	48 89 44 24 38	 mov	 QWORD PTR tv136[rsp], rax
  00046	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:??_C@_1DC@NAPNNLEP@?$AA?4?$AA?4?$AA?1?$AAc?$AAo?$AAn?$AAf?$AAi?$AAg?$AA?1?$AAl?$AAo?$AAg?$AA_?$AAw@
  0004d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR $T2[rsp]
  00052	e8 00 00 00 00	 call	 ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  00057	48 89 44 24 40	 mov	 QWORD PTR tv138[rsp], rax
  0005c	4c 8b 44 24 38	 mov	 r8, QWORD PTR tv136[rsp]
  00061	48 8b 54 24 40	 mov	 rdx, QWORD PTR tv138[rsp]
  00066	48 8b 8c 24 a0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0006e	e8 00 00 00 00	 call	 ??0MonitorServerCommon@mu2@@IEAA@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z ; mu2::MonitorServerCommon::MonitorServerCommon
  00073	90		 npad	 1

; 12   : {

  00074	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0007c	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7MonitorWorld@mu2@@6B@
  00083	48 89 08	 mov	 QWORD PTR [rax], rcx

; 10   : 	, concurrentUserCount(0)

  00086	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0008e	c7 40 60 00 00
	00 00		 mov	 DWORD PTR [rax+96], 0

; 11   : 	, concurrentPCRoomCount(0)

  00095	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0009d	c7 40 64 00 00
	00 00		 mov	 DWORD PTR [rax+100], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1383 :         _Tidy_deallocate();

  000a4	48 8b 8c 24 a8
	00 00 00	 mov	 rcx, QWORD PTR strModuleName$[rsp]
  000ac	e8 00 00 00 00	 call	 ?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate
  000b1	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MonitorWorld.cpp

; 13   : }

  000b2	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000ba	48 81 c4 98 00
	00 00		 add	 rsp, 152		; 00000098H
  000c1	c3		 ret	 0
??0MonitorWorld@mu2@@QEAA@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z ENDP ; mu2::MonitorWorld::MonitorWorld
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
tv70 = 40
$T2 = 48
tv136 = 56
tv138 = 64
$T3 = 72
$T4 = 104
this$ = 160
strModuleName$ = 168
?dtor$0@?0???0MonitorWorld@mu2@@QEAA@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z@4HA PROC ; `mu2::MonitorWorld::MonitorWorld'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 8d a8 00
	00 00		 mov	 rcx, QWORD PTR strModuleName$[rbp]
  00010	e8 00 00 00 00	 call	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$0@?0???0MonitorWorld@mu2@@QEAA@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z@4HA ENDP ; `mu2::MonitorWorld::MonitorWorld'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
tv70 = 40
$T2 = 48
tv136 = 56
tv138 = 64
$T3 = 72
$T4 = 104
this$ = 160
strModuleName$ = 168
?dtor$1@?0???0MonitorWorld@mu2@@QEAA@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z@4HA PROC ; `mu2::MonitorWorld::MonitorWorld'::`1'::dtor$1
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 20	 mov	 rcx, QWORD PTR $T1[rbp]
  0000d	e8 00 00 00 00	 call	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$1@?0???0MonitorWorld@mu2@@QEAA@V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z@4HA ENDP ; `mu2::MonitorWorld::MonitorWorld'::`1'::dtor$1
text$x	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Monitor.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h
;	COMDAT ??_GIMonitor@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GIMonitor@mu2@@UEAAPEAXI@Z PROC			; mu2::IMonitor::`scalar deleting destructor', COMDAT
$LN104:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H
; File F:\Release_Branch\Server\Development\Framework\Core\Monitor.h

; 21   : 		virtual~IMonitor() {};

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7IMonitor@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx
  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 18	 add	 rax, 24
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1383 :         _Tidy_deallocate();

  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 ?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 28		 je	 SHORT $LN2@scalar
  00039	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  0003d	83 e0 04	 and	 eax, 4
  00040	85 c0		 test	 eax, eax
  00042	75 0d		 jne	 SHORT $LN3@scalar
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h

; 45   : 			::free(ptr);

  00044	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00049	e8 00 00 00 00	 call	 free
  0004e	90		 npad	 1
  0004f	eb 10		 jmp	 SHORT $LN2@scalar
$LN3@scalar:
  00051	ba 38 00 00 00	 mov	 edx, 56			; 00000038H
  00056	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0005b	e8 00 00 00 00	 call	 ?__global_delete@@YAXPEAX_K@Z ; __global_delete
  00060	90		 npad	 1
$LN2@scalar:
  00061	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00066	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0006a	c3		 ret	 0
??_GIMonitor@mu2@@UEAAPEAXI@Z ENDP			; mu2::IMonitor::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
_TEXT	SEGMENT
_Ptr_container$ = 48
_Block_size$ = 56
_Ptr$ = 64
$T1 = 72
_Bytes$ = 96
??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z PROC ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>, COMDAT

; 182  : __declspec(allocator) void* _Allocate_manually_vector_aligned(const size_t _Bytes) {

$LN7:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 183  :     // allocate _Bytes manually aligned to at least _Big_allocation_alignment
; 184  :     const size_t _Block_size = _Non_user_size + _Bytes;

  00009	48 8b 44 24 60	 mov	 rax, QWORD PTR _Bytes$[rsp]
  0000e	48 83 c0 27	 add	 rax, 39			; 00000027H
  00012	48 89 44 24 38	 mov	 QWORD PTR _Block_size$[rsp], rax

; 185  :     if (_Block_size <= _Bytes) {

  00017	48 8b 44 24 60	 mov	 rax, QWORD PTR _Bytes$[rsp]
  0001c	48 39 44 24 38	 cmp	 QWORD PTR _Block_size$[rsp], rax
  00021	77 06		 ja	 SHORT $LN2@Allocate_m

; 186  :         _Throw_bad_array_new_length(); // add overflow

  00023	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00028	90		 npad	 1
$LN2@Allocate_m:

; 136  :         return ::operator new(_Bytes);

  00029	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Block_size$[rsp]
  0002e	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00033	48 89 44 24 48	 mov	 QWORD PTR $T1[rsp], rax

; 187  :     }
; 188  : 
; 189  :     const uintptr_t _Ptr_container = reinterpret_cast<uintptr_t>(_Traits::_Allocate(_Block_size));

  00038	48 8b 44 24 48	 mov	 rax, QWORD PTR $T1[rsp]
  0003d	48 89 44 24 30	 mov	 QWORD PTR _Ptr_container$[rsp], rax

; 190  :     _STL_VERIFY(_Ptr_container != 0, "invalid argument"); // validate even in release since we're doing p[-1]

  00042	48 83 7c 24 30
	00		 cmp	 QWORD PTR _Ptr_container$[rsp], 0
  00048	75 19		 jne	 SHORT $LN3@Allocate_m
  0004a	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR [rsp+32], 0
  00053	45 33 c9	 xor	 r9d, r9d
  00056	45 33 c0	 xor	 r8d, r8d
  00059	33 d2		 xor	 edx, edx
  0005b	33 c9		 xor	 ecx, ecx
  0005d	e8 00 00 00 00	 call	 _invoke_watson
  00062	90		 npad	 1
$LN3@Allocate_m:

; 191  :     void* const _Ptr = reinterpret_cast<void*>((_Ptr_container + _Non_user_size) & ~(_Big_allocation_alignment - 1));

  00063	48 8b 44 24 30	 mov	 rax, QWORD PTR _Ptr_container$[rsp]
  00068	48 83 c0 27	 add	 rax, 39			; 00000027H
  0006c	48 83 e0 e0	 and	 rax, -32		; ffffffffffffffe0H
  00070	48 89 44 24 40	 mov	 QWORD PTR _Ptr$[rsp], rax

; 192  :     static_cast<uintptr_t*>(_Ptr)[-1] = _Ptr_container;

  00075	b8 08 00 00 00	 mov	 eax, 8
  0007a	48 6b c0 ff	 imul	 rax, rax, -1
  0007e	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  00083	48 8b 54 24 30	 mov	 rdx, QWORD PTR _Ptr_container$[rsp]
  00088	48 89 14 01	 mov	 QWORD PTR [rcx+rax], rdx

; 193  : 
; 194  : #ifdef _DEBUG
; 195  :     static_cast<uintptr_t*>(_Ptr)[-2] = _Big_allocation_sentinel;
; 196  : #endif // defined(_DEBUG)
; 197  :     return _Ptr;

  0008c	48 8b 44 24 40	 mov	 rax, QWORD PTR _Ptr$[rsp]
$LN4@Allocate_m:

; 198  : }

  00091	48 83 c4 58	 add	 rsp, 88			; 00000058H
  00095	c3		 ret	 0
??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ENDP ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??$_Construct@$01PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z
_TEXT	SEGMENT
$T1 = 32
$S27$ = 33
_My_data$ = 40
_New_capacity$ = 48
_Max$ = 56
_Masked$2 = 64
$T3 = 72
$T4 = 80
tv154 = 88
_Fancy_ptr$5 = 96
_New_ptr$ = 104
$T6 = 112
$T7 = 120
_First1$ = 128
$T8 = 136
$T9 = 144
_Al$ = 152
$T10 = 160
$T11 = 168
$T12 = 176
$T13 = 184
$T14 = 192
$T15 = 200
_Ptr$ = 208
$T16 = 216
_First1$ = 224
_Alproxy$ = 232
this$ = 256
_Arg$ = 264
_Count$ = 272
??$_Construct@$01PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<2,wchar_t const *>, COMDAT

; 871  :     _CONSTEXPR20 void _Construct(const _Char_or_ptr _Arg, _CRT_GUARDOVERFLOW const size_type _Count) {

$LN173:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	57		 push	 rdi
  00010	48 81 ec f0 00
	00 00		 sub	 rsp, 240		; 000000f0H

; 872  :         auto& _My_data = _Mypair._Myval2;

  00017	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0001f	48 89 44 24 28	 mov	 QWORD PTR _My_data$[rsp], rax

; 873  :         _STL_INTERNAL_CHECK(!_My_data._Large_mode_engaged());
; 874  : 
; 875  :         if constexpr (_Strat == _Construct_strategy::_From_char) {
; 876  :             _STL_INTERNAL_STATIC_ASSERT(is_same_v<_Char_or_ptr, _Elem>);
; 877  :         } else {
; 878  :             _STL_INTERNAL_STATIC_ASSERT(_Is_elem_cptr<_Char_or_ptr>::value);
; 879  :         }
; 880  : 
; 881  :         if (_Count > max_size()) {

  00024	48 8b 8c 24 00
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0002c	e8 00 00 00 00	 call	 ?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
  00031	48 39 84 24 10
	01 00 00	 cmp	 QWORD PTR _Count$[rsp], rax
  00039	76 06		 jbe	 SHORT $LN2@Construct

; 882  :             _Xlen_string(); // result too long

  0003b	e8 00 00 00 00	 call	 ?_Xlen_string@std@@YAXXZ ; std::_Xlen_string
  00040	90		 npad	 1
$LN2@Construct:

; 3107 :         return _Mypair._Get_first();

  00041	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00049	48 89 44 24 70	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3107 :         return _Mypair._Get_first();

  0004e	48 8b 44 24 70	 mov	 rax, QWORD PTR $T6[rsp]
  00053	48 89 44 24 78	 mov	 QWORD PTR $T7[rsp], rax

; 883  :         }
; 884  : 
; 885  :         auto& _Al       = _Getal();

  00058	48 8b 44 24 78	 mov	 rax, QWORD PTR $T7[rsp]
  0005d	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR _Al$[rsp], rax

; 886  :         auto&& _Alproxy = _GET_PROXY_ALLOCATOR(_Alty, _Al);

  00065	48 8d 44 24 20	 lea	 rax, QWORD PTR $T1[rsp]
  0006a	48 8b f8	 mov	 rdi, rax
  0006d	33 c0		 xor	 eax, eax
  0006f	b9 01 00 00 00	 mov	 ecx, 1
  00074	f3 aa		 rep stosb
  00076	48 8d 44 24 21	 lea	 rax, QWORD PTR $S27$[rsp]
  0007b	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR _Alproxy$[rsp], rax

; 887  :         _Container_proxy_ptr<_Alty> _Proxy(_Alproxy, _My_data);
; 888  : 
; 889  :         if (_Count <= _Small_string_capacity) {

  00083	48 83 bc 24 10
	01 00 00 07	 cmp	 QWORD PTR _Count$[rsp], 7
  0008c	77 52		 ja	 SHORT $LN3@Construct

; 890  :             _My_data._Mysize = _Count;

  0008e	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00093	48 8b 8c 24 10
	01 00 00	 mov	 rcx, QWORD PTR _Count$[rsp]
  0009b	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 891  :             _My_data._Myres  = _Small_string_capacity;

  0009f	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000a4	48 c7 40 18 07
	00 00 00	 mov	 QWORD PTR [rax+24], 7

; 892  : 
; 893  :             if constexpr (_Strat == _Construct_strategy::_From_char) {
; 894  :                 _Traits::assign(_My_data._Bx._Buf, _Count, _Arg);
; 895  :                 _Traits::assign(_My_data._Bx._Buf[_Count], _Elem());
; 896  :             } else if constexpr (_Strat == _Construct_strategy::_From_ptr) {
; 897  :                 _Traits::copy(_My_data._Bx._Buf, _Arg, _Count);
; 898  :                 _Traits::assign(_My_data._Bx._Buf[_Count], _Elem());
; 899  :             } else { // _Strat == _Construct_strategy::_From_string
; 900  : #ifdef _INSERT_STRING_ANNOTATION
; 901  :                 _Traits::copy(_My_data._Bx._Buf, _Arg, _Count + 1);
; 902  : #else // ^^^ _INSERT_STRING_ANNOTATION / !_INSERT_STRING_ANNOTATION vvv
; 903  :                 _Traits::copy(_My_data._Bx._Buf, _Arg, _BUF_SIZE);

  000ac	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000b1	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _First1$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  000b9	b8 08 00 00 00	 mov	 eax, 8
  000be	48 6b c0 02	 imul	 rax, rax, 2
  000c2	4c 8b c0	 mov	 r8, rax
  000c5	48 8b 94 24 08
	01 00 00	 mov	 rdx, QWORD PTR _Arg$[rsp]
  000cd	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR _First1$[rsp]
  000d5	e8 00 00 00 00	 call	 memcpy
  000da	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 908  :             return;

  000db	e9 01 02 00 00	 jmp	 $LN4@Construct
$LN3@Construct:

; 909  :         }
; 910  : 
; 911  :         size_type _New_capacity = _Calculate_growth(_Count, _Small_string_capacity, max_size());

  000e0	48 8b 8c 24 00
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  000e8	e8 00 00 00 00	 call	 ?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
  000ed	48 89 44 24 38	 mov	 QWORD PTR _Max$[rsp], rax

; 2978 :         const size_type _Masked = _Requested | _Alloc_mask;

  000f2	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  000fa	48 83 c8 07	 or	 rax, 7
  000fe	48 89 44 24 40	 mov	 QWORD PTR _Masked$2[rsp], rax

; 2979 :         if (_Masked > _Max) { // the mask overflows, settle for max_size()

  00103	48 8b 44 24 38	 mov	 rax, QWORD PTR _Max$[rsp]
  00108	48 39 44 24 40	 cmp	 QWORD PTR _Masked$2[rsp], rax
  0010d	76 0f		 jbe	 SHORT $LN109@Construct

; 2980 :             return _Max;

  0010f	48 8b 44 24 38	 mov	 rax, QWORD PTR _Max$[rsp]
  00114	48 89 44 24 48	 mov	 QWORD PTR $T3[rsp], rax
  00119	e9 93 00 00 00	 jmp	 $LN108@Construct
$LN109@Construct:

; 2981 :         }
; 2982 : 
; 2983 :         if (_Old > _Max - _Old / 2) { // similarly, geometric overflows

  0011e	33 d2		 xor	 edx, edx
  00120	b8 07 00 00 00	 mov	 eax, 7
  00125	b9 02 00 00 00	 mov	 ecx, 2
  0012a	48 f7 f1	 div	 rcx
  0012d	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Max$[rsp]
  00132	48 2b c8	 sub	 rcx, rax
  00135	48 8b c1	 mov	 rax, rcx
  00138	48 83 f8 07	 cmp	 rax, 7
  0013c	73 0c		 jae	 SHORT $LN110@Construct

; 2984 :             return _Max;

  0013e	48 8b 44 24 38	 mov	 rax, QWORD PTR _Max$[rsp]
  00143	48 89 44 24 48	 mov	 QWORD PTR $T3[rsp], rax
  00148	eb 67		 jmp	 SHORT $LN108@Construct
$LN110@Construct:

; 2987 :         return (_STD max)(_Masked, _Old + _Old / 2);

  0014a	33 d2		 xor	 edx, edx
  0014c	b8 07 00 00 00	 mov	 eax, 7
  00151	b9 02 00 00 00	 mov	 ecx, 2
  00156	48 f7 f1	 div	 rcx
  00159	48 83 c0 07	 add	 rax, 7
  0015d	48 89 44 24 50	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 77   :     return _Left < _Right ? _Right : _Left;

  00162	48 8b 44 24 50	 mov	 rax, QWORD PTR $T4[rsp]
  00167	48 39 44 24 40	 cmp	 QWORD PTR _Masked$2[rsp], rax
  0016c	73 0c		 jae	 SHORT $LN117@Construct
  0016e	48 8d 44 24 50	 lea	 rax, QWORD PTR $T4[rsp]
  00173	48 89 44 24 58	 mov	 QWORD PTR tv154[rsp], rax
  00178	eb 0a		 jmp	 SHORT $LN118@Construct
$LN117@Construct:
  0017a	48 8d 44 24 40	 lea	 rax, QWORD PTR _Masked$2[rsp]
  0017f	48 89 44 24 58	 mov	 QWORD PTR tv154[rsp], rax
$LN118@Construct:
  00184	48 8b 44 24 58	 mov	 rax, QWORD PTR tv154[rsp]
  00189	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T8[rsp], rax
  00191	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T8[rsp]
  00199	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2987 :         return (_STD max)(_Masked, _Old + _Old / 2);

  001a1	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR $T9[rsp]
  001a9	48 8b 00	 mov	 rax, QWORD PTR [rax]
  001ac	48 89 44 24 48	 mov	 QWORD PTR $T3[rsp], rax
$LN108@Construct:

; 909  :         }
; 910  : 
; 911  :         size_type _New_capacity = _Calculate_growth(_Count, _Small_string_capacity, max_size());

  001b1	48 8b 44 24 48	 mov	 rax, QWORD PTR $T3[rsp]
  001b6	48 89 44 24 30	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 825  :         ++_Capacity; // Take null terminator into consideration

  001bb	48 8b 44 24 30	 mov	 rax, QWORD PTR _New_capacity$[rsp]
  001c0	48 ff c0	 inc	 rax
  001c3	48 89 44 24 30	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 826  : 
; 827  :         pointer _Fancy_ptr = nullptr;

  001c8	48 c7 44 24 60
	00 00 00 00	 mov	 QWORD PTR _Fancy_ptr$5[rsp], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 2303 :         return _Al.allocate(_Count);

  001d1	48 8b 54 24 30	 mov	 rdx, QWORD PTR _New_capacity$[rsp]
  001d6	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR _Al$[rsp]
  001de	e8 00 00 00 00	 call	 ?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z ; std::allocator<wchar_t>::allocate
  001e3	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 829  :             _Fancy_ptr = _Allocate_at_least_helper(_Al, _Capacity);

  001eb	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  001f3	48 89 44 24 60	 mov	 QWORD PTR _Fancy_ptr$5[rsp], rax

; 830  :         } else {
; 831  :             _STL_INTERNAL_STATIC_ASSERT(_Policy == _Allocation_policy::_Exactly);
; 832  :             _Fancy_ptr = _Al.allocate(_Capacity);
; 833  :         }
; 834  : 
; 835  : #if _HAS_CXX20
; 836  :         // Start element lifetimes to avoid UB. This is a more general mechanism than _String_val::_Activate_SSO_buffer,
; 837  :         // but likely more impactful to throughput.
; 838  :         if (_STD is_constant_evaluated()) {
; 839  :             _Elem* const _Ptr = _Unfancy(_Fancy_ptr);
; 840  :             for (size_type _Idx = 0; _Idx < _Capacity; ++_Idx) {
; 841  :                 _STD construct_at(_Ptr + _Idx);
; 842  :             }
; 843  :         }
; 844  : #endif // _HAS_CXX20
; 845  :         --_Capacity;

  001f8	48 8b 44 24 30	 mov	 rax, QWORD PTR _New_capacity$[rsp]
  001fd	48 ff c8	 dec	 rax
  00200	48 89 44 24 30	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 846  :         return _Fancy_ptr;

  00205	48 8b 44 24 60	 mov	 rax, QWORD PTR _Fancy_ptr$5[rsp]
  0020a	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax

; 912  :         const pointer _New_ptr  = _Allocate_for_capacity(_Al, _New_capacity); // throws

  00212	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  0021a	48 89 44 24 68	 mov	 QWORD PTR _New_ptr$[rsp], rax

; 913  :         _Construct_in_place(_My_data._Bx._Ptr, _New_ptr);

  0021f	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00224	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0022c	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  00234	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0023c	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T13[rsp]
  00244	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T14[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  0024c	48 8d 44 24 68	 lea	 rax, QWORD PTR _New_ptr$[rsp]
  00251	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T15[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00259	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR $T14[rsp]
  00261	48 8b 8c 24 c8
	00 00 00	 mov	 rcx, QWORD PTR $T15[rsp]
  00269	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0026c	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 915  :         _My_data._Mysize = _Count;

  0026f	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00274	48 8b 8c 24 10
	01 00 00	 mov	 rcx, QWORD PTR _Count$[rsp]
  0027c	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 916  :         _My_data._Myres  = _New_capacity;

  00280	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00285	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _New_capacity$[rsp]
  0028a	48 89 48 18	 mov	 QWORD PTR [rax+24], rcx

; 924  :             _Traits::copy(_Unfancy(_New_ptr), _Arg, _Count + 1);

  0028e	48 8b 44 24 68	 mov	 rax, QWORD PTR _New_ptr$[rsp]
  00293	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  0029b	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR _Ptr$[rsp]
  002a3	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR $T16[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 924  :             _Traits::copy(_Unfancy(_New_ptr), _Arg, _Count + 1);

  002ab	48 8b 84 24 d8
	00 00 00	 mov	 rax, QWORD PTR $T16[rsp]
  002b3	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR _First1$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  002bb	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  002c3	48 8d 44 00 02	 lea	 rax, QWORD PTR [rax+rax+2]
  002c8	4c 8b c0	 mov	 r8, rax
  002cb	48 8b 94 24 08
	01 00 00	 mov	 rdx, QWORD PTR _Arg$[rsp]
  002d3	48 8b 8c 24 e0
	00 00 00	 mov	 rcx, QWORD PTR _First1$[rsp]
  002db	e8 00 00 00 00	 call	 memcpy
  002e0	90		 npad	 1
$LN4@Construct:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 929  :     }

  002e1	48 81 c4 f0 00
	00 00		 add	 rsp, 240		; 000000f0H
  002e8	5f		 pop	 rdi
  002e9	c3		 ret	 0
??$_Construct@$01PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<2,wchar_t const *>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??$_Construct@$00PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z
_TEXT	SEGMENT
$T1 = 32
$S26$ = 33
$T2 = 34
$T3 = 36
_My_data$ = 40
_New_capacity$ = 48
_Max$ = 56
_Masked$4 = 64
$T5 = 72
_New_ptr$ = 80
$T6 = 88
tv170 = 96
_Fancy_ptr$7 = 104
$T8 = 112
$T9 = 120
_First1$ = 128
$T10 = 136
$T11 = 144
_Al$ = 152
$T12 = 160
$T13 = 168
$T14 = 176
$T15 = 184
$T16 = 192
$T17 = 200
_Ptr$ = 208
$T18 = 216
_First1$ = 224
_Ptr$ = 232
$T19 = 240
_Alproxy$ = 248
this$ = 272
_Arg$ = 280
_Count$ = 288
??$_Construct@$00PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<1,wchar_t const *>, COMDAT

; 871  :     _CONSTEXPR20 void _Construct(const _Char_or_ptr _Arg, _CRT_GUARDOVERFLOW const size_type _Count) {

$LN188:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	57		 push	 rdi
  00010	48 81 ec 00 01
	00 00		 sub	 rsp, 256		; 00000100H

; 872  :         auto& _My_data = _Mypair._Myval2;

  00017	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0001f	48 89 44 24 28	 mov	 QWORD PTR _My_data$[rsp], rax

; 873  :         _STL_INTERNAL_CHECK(!_My_data._Large_mode_engaged());
; 874  : 
; 875  :         if constexpr (_Strat == _Construct_strategy::_From_char) {
; 876  :             _STL_INTERNAL_STATIC_ASSERT(is_same_v<_Char_or_ptr, _Elem>);
; 877  :         } else {
; 878  :             _STL_INTERNAL_STATIC_ASSERT(_Is_elem_cptr<_Char_or_ptr>::value);
; 879  :         }
; 880  : 
; 881  :         if (_Count > max_size()) {

  00024	48 8b 8c 24 10
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0002c	e8 00 00 00 00	 call	 ?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
  00031	48 39 84 24 20
	01 00 00	 cmp	 QWORD PTR _Count$[rsp], rax
  00039	76 06		 jbe	 SHORT $LN2@Construct

; 882  :             _Xlen_string(); // result too long

  0003b	e8 00 00 00 00	 call	 ?_Xlen_string@std@@YAXXZ ; std::_Xlen_string
  00040	90		 npad	 1
$LN2@Construct:

; 3107 :         return _Mypair._Get_first();

  00041	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00049	48 89 44 24 70	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3107 :         return _Mypair._Get_first();

  0004e	48 8b 44 24 70	 mov	 rax, QWORD PTR $T8[rsp]
  00053	48 89 44 24 78	 mov	 QWORD PTR $T9[rsp], rax

; 883  :         }
; 884  : 
; 885  :         auto& _Al       = _Getal();

  00058	48 8b 44 24 78	 mov	 rax, QWORD PTR $T9[rsp]
  0005d	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR _Al$[rsp], rax

; 886  :         auto&& _Alproxy = _GET_PROXY_ALLOCATOR(_Alty, _Al);

  00065	48 8d 44 24 20	 lea	 rax, QWORD PTR $T1[rsp]
  0006a	48 8b f8	 mov	 rdi, rax
  0006d	33 c0		 xor	 eax, eax
  0006f	b9 01 00 00 00	 mov	 ecx, 1
  00074	f3 aa		 rep stosb
  00076	48 8d 44 24 21	 lea	 rax, QWORD PTR $S26$[rsp]
  0007b	48 89 84 24 f8
	00 00 00	 mov	 QWORD PTR _Alproxy$[rsp], rax

; 887  :         _Container_proxy_ptr<_Alty> _Proxy(_Alproxy, _My_data);
; 888  : 
; 889  :         if (_Count <= _Small_string_capacity) {

  00083	48 83 bc 24 20
	01 00 00 07	 cmp	 QWORD PTR _Count$[rsp], 7
  0008c	77 71		 ja	 SHORT $LN3@Construct

; 890  :             _My_data._Mysize = _Count;

  0008e	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00093	48 8b 8c 24 20
	01 00 00	 mov	 rcx, QWORD PTR _Count$[rsp]
  0009b	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 891  :             _My_data._Myres  = _Small_string_capacity;

  0009f	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000a4	48 c7 40 18 07
	00 00 00	 mov	 QWORD PTR [rax+24], 7

; 892  : 
; 893  :             if constexpr (_Strat == _Construct_strategy::_From_char) {
; 894  :                 _Traits::assign(_My_data._Bx._Buf, _Count, _Arg);
; 895  :                 _Traits::assign(_My_data._Bx._Buf[_Count], _Elem());
; 896  :             } else if constexpr (_Strat == _Construct_strategy::_From_ptr) {
; 897  :                 _Traits::copy(_My_data._Bx._Buf, _Arg, _Count);

  000ac	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000b1	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _First1$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  000b9	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  000c1	48 03 c0	 add	 rax, rax
  000c4	4c 8b c0	 mov	 r8, rax
  000c7	48 8b 94 24 18
	01 00 00	 mov	 rdx, QWORD PTR _Arg$[rsp]
  000cf	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR _First1$[rsp]
  000d7	e8 00 00 00 00	 call	 memcpy
  000dc	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 898  :                 _Traits::assign(_My_data._Bx._Buf[_Count], _Elem());

  000dd	33 c0		 xor	 eax, eax
  000df	66 89 44 24 22	 mov	 WORD PTR $T2[rsp], ax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 347  :         _Left = _Right;

  000e4	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000e9	48 8b 8c 24 20
	01 00 00	 mov	 rcx, QWORD PTR _Count$[rsp]
  000f1	0f b7 54 24 22	 movzx	 edx, WORD PTR $T2[rsp]
  000f6	66 89 14 48	 mov	 WORD PTR [rax+rcx*2], dx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 908  :             return;

  000fa	e9 3c 02 00 00	 jmp	 $LN4@Construct
$LN3@Construct:

; 909  :         }
; 910  : 
; 911  :         size_type _New_capacity = _Calculate_growth(_Count, _Small_string_capacity, max_size());

  000ff	48 8b 8c 24 10
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00107	e8 00 00 00 00	 call	 ?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
  0010c	48 89 44 24 38	 mov	 QWORD PTR _Max$[rsp], rax

; 2978 :         const size_type _Masked = _Requested | _Alloc_mask;

  00111	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  00119	48 83 c8 07	 or	 rax, 7
  0011d	48 89 44 24 40	 mov	 QWORD PTR _Masked$4[rsp], rax

; 2979 :         if (_Masked > _Max) { // the mask overflows, settle for max_size()

  00122	48 8b 44 24 38	 mov	 rax, QWORD PTR _Max$[rsp]
  00127	48 39 44 24 40	 cmp	 QWORD PTR _Masked$4[rsp], rax
  0012c	76 0f		 jbe	 SHORT $LN114@Construct

; 2980 :             return _Max;

  0012e	48 8b 44 24 38	 mov	 rax, QWORD PTR _Max$[rsp]
  00133	48 89 44 24 48	 mov	 QWORD PTR $T5[rsp], rax
  00138	e9 93 00 00 00	 jmp	 $LN113@Construct
$LN114@Construct:

; 2981 :         }
; 2982 : 
; 2983 :         if (_Old > _Max - _Old / 2) { // similarly, geometric overflows

  0013d	33 d2		 xor	 edx, edx
  0013f	b8 07 00 00 00	 mov	 eax, 7
  00144	b9 02 00 00 00	 mov	 ecx, 2
  00149	48 f7 f1	 div	 rcx
  0014c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Max$[rsp]
  00151	48 2b c8	 sub	 rcx, rax
  00154	48 8b c1	 mov	 rax, rcx
  00157	48 83 f8 07	 cmp	 rax, 7
  0015b	73 0c		 jae	 SHORT $LN115@Construct

; 2984 :             return _Max;

  0015d	48 8b 44 24 38	 mov	 rax, QWORD PTR _Max$[rsp]
  00162	48 89 44 24 48	 mov	 QWORD PTR $T5[rsp], rax
  00167	eb 67		 jmp	 SHORT $LN113@Construct
$LN115@Construct:

; 2987 :         return (_STD max)(_Masked, _Old + _Old / 2);

  00169	33 d2		 xor	 edx, edx
  0016b	b8 07 00 00 00	 mov	 eax, 7
  00170	b9 02 00 00 00	 mov	 ecx, 2
  00175	48 f7 f1	 div	 rcx
  00178	48 83 c0 07	 add	 rax, 7
  0017c	48 89 44 24 58	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 77   :     return _Left < _Right ? _Right : _Left;

  00181	48 8b 44 24 58	 mov	 rax, QWORD PTR $T6[rsp]
  00186	48 39 44 24 40	 cmp	 QWORD PTR _Masked$4[rsp], rax
  0018b	73 0c		 jae	 SHORT $LN122@Construct
  0018d	48 8d 44 24 58	 lea	 rax, QWORD PTR $T6[rsp]
  00192	48 89 44 24 60	 mov	 QWORD PTR tv170[rsp], rax
  00197	eb 0a		 jmp	 SHORT $LN123@Construct
$LN122@Construct:
  00199	48 8d 44 24 40	 lea	 rax, QWORD PTR _Masked$4[rsp]
  0019e	48 89 44 24 60	 mov	 QWORD PTR tv170[rsp], rax
$LN123@Construct:
  001a3	48 8b 44 24 60	 mov	 rax, QWORD PTR tv170[rsp]
  001a8	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
  001b0	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  001b8	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2987 :         return (_STD max)(_Masked, _Old + _Old / 2);

  001c0	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  001c8	48 8b 00	 mov	 rax, QWORD PTR [rax]
  001cb	48 89 44 24 48	 mov	 QWORD PTR $T5[rsp], rax
$LN113@Construct:

; 909  :         }
; 910  : 
; 911  :         size_type _New_capacity = _Calculate_growth(_Count, _Small_string_capacity, max_size());

  001d0	48 8b 44 24 48	 mov	 rax, QWORD PTR $T5[rsp]
  001d5	48 89 44 24 30	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 825  :         ++_Capacity; // Take null terminator into consideration

  001da	48 8b 44 24 30	 mov	 rax, QWORD PTR _New_capacity$[rsp]
  001df	48 ff c0	 inc	 rax
  001e2	48 89 44 24 30	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 826  : 
; 827  :         pointer _Fancy_ptr = nullptr;

  001e7	48 c7 44 24 68
	00 00 00 00	 mov	 QWORD PTR _Fancy_ptr$7[rsp], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 2303 :         return _Al.allocate(_Count);

  001f0	48 8b 54 24 30	 mov	 rdx, QWORD PTR _New_capacity$[rsp]
  001f5	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR _Al$[rsp]
  001fd	e8 00 00 00 00	 call	 ?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z ; std::allocator<wchar_t>::allocate
  00202	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 829  :             _Fancy_ptr = _Allocate_at_least_helper(_Al, _Capacity);

  0020a	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
  00212	48 89 44 24 68	 mov	 QWORD PTR _Fancy_ptr$7[rsp], rax

; 830  :         } else {
; 831  :             _STL_INTERNAL_STATIC_ASSERT(_Policy == _Allocation_policy::_Exactly);
; 832  :             _Fancy_ptr = _Al.allocate(_Capacity);
; 833  :         }
; 834  : 
; 835  : #if _HAS_CXX20
; 836  :         // Start element lifetimes to avoid UB. This is a more general mechanism than _String_val::_Activate_SSO_buffer,
; 837  :         // but likely more impactful to throughput.
; 838  :         if (_STD is_constant_evaluated()) {
; 839  :             _Elem* const _Ptr = _Unfancy(_Fancy_ptr);
; 840  :             for (size_type _Idx = 0; _Idx < _Capacity; ++_Idx) {
; 841  :                 _STD construct_at(_Ptr + _Idx);
; 842  :             }
; 843  :         }
; 844  : #endif // _HAS_CXX20
; 845  :         --_Capacity;

  00217	48 8b 44 24 30	 mov	 rax, QWORD PTR _New_capacity$[rsp]
  0021c	48 ff c8	 dec	 rax
  0021f	48 89 44 24 30	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 846  :         return _Fancy_ptr;

  00224	48 8b 44 24 68	 mov	 rax, QWORD PTR _Fancy_ptr$7[rsp]
  00229	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax

; 912  :         const pointer _New_ptr  = _Allocate_for_capacity(_Al, _New_capacity); // throws

  00231	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T13[rsp]
  00239	48 89 44 24 50	 mov	 QWORD PTR _New_ptr$[rsp], rax

; 913  :         _Construct_in_place(_My_data._Bx._Ptr, _New_ptr);

  0023e	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00243	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T14[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0024b	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR $T14[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  00253	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T15[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0025b	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T15[rsp]
  00263	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T16[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  0026b	48 8d 44 24 50	 lea	 rax, QWORD PTR _New_ptr$[rsp]
  00270	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T17[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00278	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR $T16[rsp]
  00280	48 8b 8c 24 c8
	00 00 00	 mov	 rcx, QWORD PTR $T17[rsp]
  00288	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0028b	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 915  :         _My_data._Mysize = _Count;

  0028e	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00293	48 8b 8c 24 20
	01 00 00	 mov	 rcx, QWORD PTR _Count$[rsp]
  0029b	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 916  :         _My_data._Myres  = _New_capacity;

  0029f	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  002a4	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _New_capacity$[rsp]
  002a9	48 89 48 18	 mov	 QWORD PTR [rax+24], rcx

; 921  :             _Traits::copy(_Unfancy(_New_ptr), _Arg, _Count);

  002ad	48 8b 44 24 50	 mov	 rax, QWORD PTR _New_ptr$[rsp]
  002b2	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  002ba	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR _Ptr$[rsp]
  002c2	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR $T18[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 921  :             _Traits::copy(_Unfancy(_New_ptr), _Arg, _Count);

  002ca	48 8b 84 24 d8
	00 00 00	 mov	 rax, QWORD PTR $T18[rsp]
  002d2	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR _First1$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  002da	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  002e2	48 03 c0	 add	 rax, rax
  002e5	4c 8b c0	 mov	 r8, rax
  002e8	48 8b 94 24 18
	01 00 00	 mov	 rdx, QWORD PTR _Arg$[rsp]
  002f0	48 8b 8c 24 e0
	00 00 00	 mov	 rcx, QWORD PTR _First1$[rsp]
  002f8	e8 00 00 00 00	 call	 memcpy
  002fd	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 922  :             _Traits::assign(_Unfancy(_New_ptr)[_Count], _Elem());

  002fe	33 c0		 xor	 eax, eax
  00300	66 89 44 24 24	 mov	 WORD PTR $T3[rsp], ax
  00305	48 8b 44 24 50	 mov	 rax, QWORD PTR _New_ptr$[rsp]
  0030a	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  00312	48 8b 84 24 e8
	00 00 00	 mov	 rax, QWORD PTR _Ptr$[rsp]
  0031a	48 89 84 24 f0
	00 00 00	 mov	 QWORD PTR $T19[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 347  :         _Left = _Right;

  00322	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR $T19[rsp]
  0032a	48 8b 8c 24 20
	01 00 00	 mov	 rcx, QWORD PTR _Count$[rsp]
  00332	0f b7 54 24 24	 movzx	 edx, WORD PTR $T3[rsp]
  00337	66 89 14 48	 mov	 WORD PTR [rax+rcx*2], dx
$LN4@Construct:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 929  :     }

  0033b	48 81 c4 00 01
	00 00		 add	 rsp, 256		; 00000100H
  00342	5f		 pop	 rdi
  00343	c3		 ret	 0
??$_Construct@$00PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<1,wchar_t const *>
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??1?$_Compressed_pair@V?$allocator@_W@std@@V?$_String_val@U?$_Simple_types@_W@std@@@2@$00@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 8
??1?$_Compressed_pair@V?$allocator@_W@std@@V?$_String_val@U?$_Simple_types@_W@std@@@2@$00@std@@QEAA@XZ PROC ; std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>::~_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>, COMDAT
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
??1?$_Compressed_pair@V?$allocator@_W@std@@V?$_String_val@U?$_Simple_types@_W@std@@@2@$00@std@@QEAA@XZ ENDP ; std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>::~_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
_TEXT	SEGMENT
$T1 = 32
$T2 = 34
_My_data$ = 40
tv94 = 48
_Bytes$ = 56
_Ptr$ = 64
$T3 = 72
$T4 = 80
_Capacity$ = 88
_Old_ptr$ = 96
_Al$5 = 104
this$ = 128
?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate, COMDAT

; 3080 :     _CONSTEXPR20 void _Tidy_deallocate() noexcept { // initialize buffer, deallocating any storage

$LN62:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 78	 sub	 rsp, 120		; 00000078H

; 3081 :         auto& _My_data = _Mypair._Myval2;

  00009	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00011	48 89 44 24 28	 mov	 QWORD PTR _My_data$[rsp], rax

; 453  :         return _Myres > _Small_string_capacity;

  00016	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  0001b	48 83 78 18 07	 cmp	 QWORD PTR [rax+24], 7
  00020	76 0a		 jbe	 SHORT $LN12@Tidy_deall
  00022	c7 44 24 30 01
	00 00 00	 mov	 DWORD PTR tv94[rsp], 1
  0002a	eb 08		 jmp	 SHORT $LN13@Tidy_deall
$LN12@Tidy_deall:
  0002c	c7 44 24 30 00
	00 00 00	 mov	 DWORD PTR tv94[rsp], 0
$LN13@Tidy_deall:
  00034	0f b6 44 24 30	 movzx	 eax, BYTE PTR tv94[rsp]
  00039	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al

; 3082 :         _My_data._Orphan_all();
; 3083 :         if (_My_data._Large_mode_engaged()) {

  0003d	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  00042	0f b6 c0	 movzx	 eax, al
  00045	85 c0		 test	 eax, eax
  00047	0f 84 80 00 00
	00		 je	 $LN2@Tidy_deall

; 3107 :         return _Mypair._Get_first();

  0004d	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00055	48 89 44 24 48	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3107 :         return _Mypair._Get_first();

  0005a	48 8b 44 24 48	 mov	 rax, QWORD PTR $T3[rsp]
  0005f	48 89 44 24 50	 mov	 QWORD PTR $T4[rsp], rax

; 3084 :             _ASAN_STRING_REMOVE(*this);
; 3085 :             auto& _Al = _Getal();

  00064	48 8b 44 24 50	 mov	 rax, QWORD PTR $T4[rsp]
  00069	48 89 44 24 68	 mov	 QWORD PTR _Al$5[rsp], rax

; 3086 :             _Deallocate_for_capacity(_Al, _My_data._Bx._Ptr, _My_data._Myres);

  0006e	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00073	48 8b 40 18	 mov	 rax, QWORD PTR [rax+24]
  00077	48 89 44 24 58	 mov	 QWORD PTR _Capacity$[rsp], rax
  0007c	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00081	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00084	48 89 44 24 60	 mov	 QWORD PTR _Old_ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  00089	48 8b 44 24 58	 mov	 rax, QWORD PTR _Capacity$[rsp]
  0008e	48 8d 44 00 02	 lea	 rax, QWORD PTR [rax+rax+2]
  00093	48 89 44 24 38	 mov	 QWORD PTR _Bytes$[rsp], rax
  00098	48 8b 44 24 60	 mov	 rax, QWORD PTR _Old_ptr$[rsp]
  0009d	48 89 44 24 40	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  000a2	48 81 7c 24 38
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  000ab	72 10		 jb	 SHORT $LN38@Tidy_deall

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  000ad	48 8d 54 24 38	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  000b2	48 8d 4c 24 40	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  000b7	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  000bc	90		 npad	 1
$LN38@Tidy_deall:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  000bd	48 8b 54 24 38	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  000c2	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  000c7	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  000cc	90		 npad	 1
$LN2@Tidy_deall:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3090 :         _My_data._Mysize = 0;

  000cd	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000d2	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 3091 :         _My_data._Myres  = _Small_string_capacity;

  000da	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000df	48 c7 40 18 07
	00 00 00	 mov	 QWORD PTR [rax+24], 7

; 3092 :         // the _Traits::assign is last so the codegen doesn't think the char write can alias this
; 3093 :         _Traits::assign(_My_data._Bx._Buf[0], _Elem());

  000e7	33 c0		 xor	 eax, eax
  000e9	66 89 44 24 22	 mov	 WORD PTR $T2[rsp], ax
  000ee	b8 02 00 00 00	 mov	 eax, 2
  000f3	48 6b c0 00	 imul	 rax, rax, 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 347  :         _Left = _Right;

  000f7	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _My_data$[rsp]
  000fc	0f b7 54 24 22	 movzx	 edx, WORD PTR $T2[rsp]
  00101	66 89 14 01	 mov	 WORD PTR [rcx+rax], dx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3094 :     }

  00105	48 83 c4 78	 add	 rsp, 120		; 00000078H
  00109	c3		 ret	 0
?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ
_TEXT	SEGMENT
$T1 = 0
_Alloc_max$ = 8
tv66 = 16
$T2 = 24
$T3 = 32
tv70 = 40
$T4 = 48
$T5 = 56
$T6 = 64
$T7 = 72
_Storage_max$ = 80
$T8 = 88
$T9 = 96
$T10 = 104
$T11 = 112
_Unsigned_max$12 = 120
this$ = 144
?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size, COMDAT

; 2377 :     _NODISCARD _CONSTEXPR20 size_type max_size() const noexcept {

$LN38:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H

; 3111 :         return _Mypair._Get_first();

  0000c	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1543 :         return *this;

  00014	48 89 44 24 30	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3111 :         return _Mypair._Get_first();

  00019	48 8b 44 24 30	 mov	 rax, QWORD PTR $T4[rsp]
  0001e	48 89 44 24 70	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 746  :         return static_cast<size_t>(-1) / sizeof(value_type);

  00023	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  0002d	48 89 44 24 38	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2378 :         const size_type _Alloc_max   = _Alty_traits::max_size(_Getal());

  00032	48 8b 44 24 38	 mov	 rax, QWORD PTR $T5[rsp]
  00037	48 89 44 24 08	 mov	 QWORD PTR _Alloc_max$[rsp], rax

; 2379 :         const size_type _Storage_max = // can always store small string

  0003c	48 c7 04 24 08
	00 00 00	 mov	 QWORD PTR $T1[rsp], 8
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 77   :     return _Left < _Right ? _Right : _Left;

  00044	48 8b 04 24	 mov	 rax, QWORD PTR $T1[rsp]
  00048	48 39 44 24 08	 cmp	 QWORD PTR _Alloc_max$[rsp], rax
  0004d	73 0b		 jae	 SHORT $LN21@max_size
  0004f	48 8d 04 24	 lea	 rax, QWORD PTR $T1[rsp]
  00053	48 89 44 24 10	 mov	 QWORD PTR tv66[rsp], rax
  00058	eb 0a		 jmp	 SHORT $LN22@max_size
$LN21@max_size:
  0005a	48 8d 44 24 08	 lea	 rax, QWORD PTR _Alloc_max$[rsp]
  0005f	48 89 44 24 10	 mov	 QWORD PTR tv66[rsp], rax
$LN22@max_size:
  00064	48 8b 44 24 10	 mov	 rax, QWORD PTR tv66[rsp]
  00069	48 89 44 24 40	 mov	 QWORD PTR $T6[rsp], rax
  0006e	48 8b 44 24 40	 mov	 rax, QWORD PTR $T6[rsp]
  00073	48 89 44 24 48	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2379 :         const size_type _Storage_max = // can always store small string

  00078	48 8b 44 24 48	 mov	 rax, QWORD PTR $T7[rsp]
  0007d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00080	48 89 44 24 50	 mov	 QWORD PTR _Storage_max$[rsp], rax

; 2381 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()),

  00085	48 8b 44 24 50	 mov	 rax, QWORD PTR _Storage_max$[rsp]
  0008a	48 ff c8	 dec	 rax
  0008d	48 89 44 24 18	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 866  :         constexpr auto _Unsigned_max = static_cast<make_unsigned_t<_Ty>>(-1);

  00092	48 c7 44 24 78
	ff ff ff ff	 mov	 QWORD PTR _Unsigned_max$12[rsp], -1

; 867  :         return static_cast<_Ty>(_Unsigned_max >> 1);

  0009b	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  000a5	48 89 44 24 58	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2381 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()),

  000aa	48 8b 44 24 58	 mov	 rax, QWORD PTR $T8[rsp]
  000af	48 89 44 24 20	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 101  :     return _Right < _Left ? _Right : _Left;

  000b4	48 8b 44 24 20	 mov	 rax, QWORD PTR $T3[rsp]
  000b9	48 39 44 24 18	 cmp	 QWORD PTR $T2[rsp], rax
  000be	73 0c		 jae	 SHORT $LN33@max_size
  000c0	48 8d 44 24 18	 lea	 rax, QWORD PTR $T2[rsp]
  000c5	48 89 44 24 28	 mov	 QWORD PTR tv70[rsp], rax
  000ca	eb 0a		 jmp	 SHORT $LN34@max_size
$LN33@max_size:
  000cc	48 8d 44 24 20	 lea	 rax, QWORD PTR $T3[rsp]
  000d1	48 89 44 24 28	 mov	 QWORD PTR tv70[rsp], rax
$LN34@max_size:
  000d6	48 8b 44 24 28	 mov	 rax, QWORD PTR tv70[rsp]
  000db	48 89 44 24 60	 mov	 QWORD PTR $T9[rsp], rax
  000e0	48 8b 44 24 60	 mov	 rax, QWORD PTR $T9[rsp]
  000e5	48 89 44 24 68	 mov	 QWORD PTR $T10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2381 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()),

  000ea	48 8b 44 24 68	 mov	 rax, QWORD PTR $T10[rsp]
  000ef	48 8b 00	 mov	 rax, QWORD PTR [rax]

; 2382 :             _Storage_max - 1 // -1 is for null terminator and/or npos
; 2383 :         );
; 2384 :     }

  000f2	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  000f9	c3		 ret	 0
?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >, COMDAT

; 1382 :     _CONSTEXPR20 ~basic_string() noexcept {

$LN82:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 1383 :         _Tidy_deallocate();

  00009	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0000e	e8 00 00 00 00	 call	 ?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate
  00013	90		 npad	 1

; 1384 : #if _ITERATOR_DEBUG_LEVEL != 0
; 1385 :         auto&& _Alproxy          = _GET_PROXY_ALLOCATOR(_Alty, _Getal());
; 1386 :         const auto _To_delete    = _Mypair._Myval2._Myproxy;
; 1387 :         _Mypair._Myval2._Myproxy = nullptr;
; 1388 :         _Delete_plain_internal(_Alproxy, _To_delete);
; 1389 : #endif // _ITERATOR_DEBUG_LEVEL != 0
; 1390 :     }

  00014	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00018	c3		 ret	 0
??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z
_TEXT	SEGMENT
this$ = 32
this$ = 40
this$ = 48
$T1 = 56
$T2 = 64
this$ = 96
_Ptr$ = 104
??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >, COMDAT

; 768  :     _CONSTEXPR20 basic_string(_In_z_ const _Elem* const _Ptr) : _Mypair(_Zero_then_variadic_args_t{}) {

$LN221:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 50	 sub	 rsp, 80			; 00000050H
  0000f	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 89 44 24 30	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1531 :         : _Ty1(), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  00019	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001e	48 89 44 24 20	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 402  :     _CONSTEXPR20 _String_val() noexcept : _Bx() {}

  00023	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00028	48 89 44 24 28	 mov	 QWORD PTR this$[rsp], rax

; 493  :         _CONSTEXPR20 _Bxty() noexcept : _Buf() {} // user-provided, for fancy pointers

  0002d	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00032	48 8b 7c 24 28	 mov	 rdi, QWORD PTR this$[rsp]
  00037	33 c0		 xor	 eax, eax
  00039	b9 10 00 00 00	 mov	 ecx, 16
  0003e	f3 aa		 rep stosb

; 517  :     size_type _Mysize = 0; // current length of string (size)

  00040	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00045	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 518  :     size_type _Myres  = 0; // current storage reserved for string (capacity)

  0004d	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00052	48 c7 40 18 00
	00 00 00	 mov	 QWORD PTR [rax+24], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 310  :         return _CSTD wcslen(reinterpret_cast<const wchar_t*>(_First));

  0005a	48 8b 4c 24 68	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  0005f	e8 00 00 00 00	 call	 wcslen
  00064	48 89 44 24 38	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 769  :         _Construct<_Construct_strategy::_From_ptr>(_Ptr, _Convert_size<size_type>(_Traits::length(_Ptr)));

  00069	48 8b 44 24 38	 mov	 rax, QWORD PTR $T1[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1131 :     return static_cast<_Size_type>(_Len);

  0006e	48 89 44 24 40	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 769  :         _Construct<_Construct_strategy::_From_ptr>(_Ptr, _Convert_size<size_type>(_Traits::length(_Ptr)));

  00073	48 8b 44 24 40	 mov	 rax, QWORD PTR $T2[rsp]
  00078	4c 8b c0	 mov	 r8, rax
  0007b	48 8b 54 24 68	 mov	 rdx, QWORD PTR _Ptr$[rsp]
  00080	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  00085	e8 00 00 00 00	 call	 ??$_Construct@$00PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<1,wchar_t const *>
  0008a	90		 npad	 1

; 770  :     }

  0008b	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00090	48 83 c4 50	 add	 rsp, 80			; 00000050H
  00094	5f		 pop	 rdi
  00095	c3		 ret	 0
??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
this$ = 32
this$ = 40
this$ = 48
$T1 = 56
$T2 = 64
this$ = 96
_Ptr$ = 104
?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z@4HA PROC ; `std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 60	 mov	 rcx, QWORD PTR this$[rbp]
  0000d	e8 00 00 00 00	 call	 ??1?$_Compressed_pair@V?$allocator@_W@std@@V?$_String_val@U?$_Simple_types@_W@std@@@2@$00@std@@QEAA@XZ
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z@4HA ENDP ; `std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
$T1 = 32
tv152 = 36
this$ = 40
this$ = 48
this$ = 56
_Result$2 = 64
$T3 = 72
this$ = 80
_Ptr$ = 88
$T4 = 96
$T5 = 104
$T6 = 112
this$ = 144
_Right$ = 152
??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >, COMDAT

; 717  :         : _Mypair(_One_then_variadic_args_t{}, _Alty_traits::select_on_container_copy_construction(_Right._Getal())) {

$LN226:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 81 ec 80 00
	00 00		 sub	 rsp, 128		; 00000080H

; 3111 :         return _Mypair._Get_first();

  00012	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR _Right$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1543 :         return *this;

  0001a	48 89 44 24 48	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3111 :         return _Mypair._Get_first();

  0001f	48 8b 44 24 48	 mov	 rax, QWORD PTR $T3[rsp]
  00024	48 89 44 24 70	 mov	 QWORD PTR $T6[rsp], rax

; 717  :         : _Mypair(_One_then_variadic_args_t{}, _Alty_traits::select_on_container_copy_construction(_Right._Getal())) {

  00029	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00031	48 89 44 24 50	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1536 :         : _Ty1(_STD forward<_Other1>(_Val1)), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  00036	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0003b	48 89 44 24 28	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 402  :     _CONSTEXPR20 _String_val() noexcept : _Bx() {}

  00040	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00045	48 89 44 24 38	 mov	 QWORD PTR this$[rsp], rax

; 493  :         _CONSTEXPR20 _Bxty() noexcept : _Buf() {} // user-provided, for fancy pointers

  0004a	48 8b 44 24 38	 mov	 rax, QWORD PTR this$[rsp]
  0004f	48 8b 7c 24 38	 mov	 rdi, QWORD PTR this$[rsp]
  00054	33 c0		 xor	 eax, eax
  00056	b9 10 00 00 00	 mov	 ecx, 16
  0005b	f3 aa		 rep stosb

; 517  :     size_type _Mysize = 0; // current length of string (size)

  0005d	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00062	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 518  :     size_type _Myres  = 0; // current storage reserved for string (capacity)

  0006a	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  0006f	48 c7 40 18 00
	00 00 00	 mov	 QWORD PTR [rax+24], 0

; 718  :         _Construct<_Construct_strategy::_From_string>(_Right._Mypair._Myval2._Myptr(), _Right._Mypair._Myval2._Mysize);

  00077	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR _Right$[rsp]
  0007f	48 89 44 24 30	 mov	 QWORD PTR this$[rsp], rax

; 444  :         const value_type* _Result = _Bx._Buf;

  00084	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00089	48 89 44 24 40	 mov	 QWORD PTR _Result$2[rsp], rax

; 453  :         return _Myres > _Small_string_capacity;

  0008e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00093	48 83 78 18 07	 cmp	 QWORD PTR [rax+24], 7
  00098	76 0a		 jbe	 SHORT $LN44@basic_stri
  0009a	c7 44 24 24 01
	00 00 00	 mov	 DWORD PTR tv152[rsp], 1
  000a2	eb 08		 jmp	 SHORT $LN45@basic_stri
$LN44@basic_stri:
  000a4	c7 44 24 24 00
	00 00 00	 mov	 DWORD PTR tv152[rsp], 0
$LN45@basic_stri:
  000ac	0f b6 44 24 24	 movzx	 eax, BYTE PTR tv152[rsp]
  000b1	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al

; 445  :         if (_Large_mode_engaged()) {

  000b5	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  000ba	0f b6 c0	 movzx	 eax, al
  000bd	85 c0		 test	 eax, eax
  000bf	74 21		 je	 SHORT $LN37@basic_stri

; 446  :             _Result = _Unfancy(_Bx._Ptr);

  000c1	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  000c6	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000c9	48 89 44 24 58	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  000ce	48 8b 44 24 58	 mov	 rax, QWORD PTR _Ptr$[rsp]
  000d3	48 89 44 24 60	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 446  :             _Result = _Unfancy(_Bx._Ptr);

  000d8	48 8b 44 24 60	 mov	 rax, QWORD PTR $T4[rsp]
  000dd	48 89 44 24 40	 mov	 QWORD PTR _Result$2[rsp], rax
$LN37@basic_stri:

; 447  :         }
; 448  : 
; 449  :         return _Result;

  000e2	48 8b 44 24 40	 mov	 rax, QWORD PTR _Result$2[rsp]
  000e7	48 89 44 24 68	 mov	 QWORD PTR $T5[rsp], rax

; 718  :         _Construct<_Construct_strategy::_From_string>(_Right._Mypair._Myval2._Myptr(), _Right._Mypair._Myval2._Mysize);

  000ec	48 8b 44 24 68	 mov	 rax, QWORD PTR $T5[rsp]
  000f1	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR _Right$[rsp]
  000f9	4c 8b 41 10	 mov	 r8, QWORD PTR [rcx+16]
  000fd	48 8b d0	 mov	 rdx, rax
  00100	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00108	e8 00 00 00 00	 call	 ??$_Construct@$01PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<2,wchar_t const *>
  0010d	90		 npad	 1

; 719  :     }

  0010e	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00116	48 81 c4 80 00
	00 00		 add	 rsp, 128		; 00000080H
  0011d	5f		 pop	 rdi
  0011e	c3		 ret	 0
??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
tv152 = 36
this$ = 40
this$ = 48
this$ = 56
_Result$2 = 64
$T3 = 72
this$ = 80
_Ptr$ = 88
$T4 = 96
$T5 = 104
$T6 = 112
this$ = 144
_Right$ = 152
?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z@4HA PROC ; `std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 8d 90 00
	00 00		 mov	 rcx, QWORD PTR this$[rbp]
  00010	e8 00 00 00 00	 call	 ??1?$_Compressed_pair@V?$allocator@_W@std@@V?$_String_val@U?$_Simple_types@_W@std@@@2@$00@std@@QEAA@XZ
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z@4HA ENDP ; `std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z
_TEXT	SEGMENT
_Overflow_is_possible$1 = 32
_Bytes$ = 40
$T2 = 48
$T3 = 56
$T4 = 64
_Max_possible$5 = 72
this$ = 96
_Count$ = 104
?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z PROC	; std::allocator<wchar_t>::allocate, COMDAT

; 988  :     _NODISCARD_RAW_PTR_ALLOC _CONSTEXPR20 __declspec(allocator) _Ty* allocate(_CRT_GUARDOVERFLOW const size_t _Count) {

$LN13:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 113  :     constexpr bool _Overflow_is_possible = _Ty_size > 1;

  0000e	c6 44 24 20 01	 mov	 BYTE PTR _Overflow_is_possible$1[rsp], 1

; 114  : 
; 115  :     if constexpr (_Overflow_is_possible) {
; 116  :         constexpr size_t _Max_possible = static_cast<size_t>(-1) / _Ty_size;

  00013	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  0001d	48 89 44 24 48	 mov	 QWORD PTR _Max_possible$5[rsp], rax

; 117  :         if (_Count > _Max_possible) {

  00022	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  0002c	48 39 44 24 68	 cmp	 QWORD PTR _Count$[rsp], rax
  00031	76 06		 jbe	 SHORT $LN4@allocate

; 118  :             _Throw_bad_array_new_length(); // multiply overflow

  00033	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00038	90		 npad	 1
$LN4@allocate:

; 119  :         }
; 120  :     }
; 121  : 
; 122  :     return _Count * _Ty_size;

  00039	48 8b 44 24 68	 mov	 rax, QWORD PTR _Count$[rsp]
  0003e	48 d1 e0	 shl	 rax, 1
  00041	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00046	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
  0004b	48 89 44 24 28	 mov	 QWORD PTR _Bytes$[rsp], rax

; 227  :     if (_Bytes == 0) {

  00050	48 83 7c 24 28
	00		 cmp	 QWORD PTR _Bytes$[rsp], 0
  00056	75 0b		 jne	 SHORT $LN8@allocate

; 228  :         return nullptr;

  00058	48 c7 44 24 30
	00 00 00 00	 mov	 QWORD PTR $T2[rsp], 0
  00061	eb 35		 jmp	 SHORT $LN7@allocate
$LN8@allocate:

; 229  :     }
; 230  : 
; 231  : #if _HAS_CXX20 // TRANSITION, GH-1532
; 232  :     if (_STD is_constant_evaluated()) {
; 233  :         return _Traits::_Allocate(_Bytes);
; 234  :     }
; 235  : #endif // _HAS_CXX20
; 236  : 
; 237  : #ifdef __cpp_aligned_new
; 238  :     if constexpr (_Align > __STDCPP_DEFAULT_NEW_ALIGNMENT__) {
; 239  :         size_t _Passed_align = _Align;
; 240  : #if defined(_M_IX86) || defined(_M_X64)
; 241  :         if (_Bytes >= _Big_allocation_threshold) {
; 242  :             // boost the alignment of big allocations to help autovectorization
; 243  :             _Passed_align = (_STD max)(_Align, _Big_allocation_alignment);
; 244  :         }
; 245  : #endif // defined(_M_IX86) || defined(_M_X64)
; 246  :         return _Traits::_Allocate_aligned(_Bytes, _Passed_align);
; 247  :     } else
; 248  : #endif // defined(__cpp_aligned_new)
; 249  :     {
; 250  : #if defined(_M_IX86) || defined(_M_X64)
; 251  :         if (_Bytes >= _Big_allocation_threshold) {

  00063	48 81 7c 24 28
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  0006c	72 11		 jb	 SHORT $LN9@allocate

; 252  :             // boost the alignment of big allocations to help autovectorization
; 253  :             return _Allocate_manually_vector_aligned<_Traits>(_Bytes);

  0006e	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00073	e8 00 00 00 00	 call	 ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
  00078	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
  0007d	eb 19		 jmp	 SHORT $LN7@allocate
$LN9@allocate:

; 136  :         return ::operator new(_Bytes);

  0007f	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00084	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00089	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax

; 256  :         return _Traits::_Allocate(_Bytes);

  0008e	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  00093	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
$LN7@allocate:

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00098	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
$LN6@allocate:

; 991  :     }

  0009d	48 83 c4 58	 add	 rsp, 88			; 00000058H
  000a1	c3		 ret	 0
?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z ENDP	; std::allocator<wchar_t>::allocate
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Xlen_string@std@@YAXXZ
_TEXT	SEGMENT
?_Xlen_string@std@@YAXXZ PROC				; std::_Xlen_string, COMDAT

; 530  : [[noreturn]] inline void _Xlen_string() {

$LN3:
  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 531  :     _Xlength_error("string too long");

  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_0BA@JFNIOLAK@string?5too?5long@
  0000b	e8 00 00 00 00	 call	 ?_Xlength_error@std@@YAXPEBD@Z ; std::_Xlength_error
  00010	90		 npad	 1
$LN2@Xlen_strin:

; 532  : }

  00011	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00015	c3		 ret	 0
?_Xlen_string@std@@YAXXZ ENDP				; std::_Xlen_string
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z
_TEXT	SEGMENT
_Back_shift$ = 48
_Ptr_container$ = 56
_Ptr_user$ = 64
_Min_back_shift$ = 72
_Ptr$ = 96
_Bytes$ = 104
?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z PROC ; std::_Adjust_manually_vector_aligned, COMDAT

; 200  : inline void _Adjust_manually_vector_aligned(void*& _Ptr, size_t& _Bytes) {

$LN5:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 201  :     // adjust parameters from _Allocate_manually_vector_aligned to pass to operator delete
; 202  :     _Bytes += _Non_user_size;

  0000e	48 8b 44 24 68	 mov	 rax, QWORD PTR _Bytes$[rsp]
  00013	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00016	48 83 c0 27	 add	 rax, 39			; 00000027H
  0001a	48 8b 4c 24 68	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  0001f	48 89 01	 mov	 QWORD PTR [rcx], rax

; 203  : 
; 204  :     const uintptr_t* const _Ptr_user = static_cast<uintptr_t*>(_Ptr);

  00022	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00027	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0002a	48 89 44 24 40	 mov	 QWORD PTR _Ptr_user$[rsp], rax

; 205  :     const uintptr_t _Ptr_container   = _Ptr_user[-1];

  0002f	b8 08 00 00 00	 mov	 eax, 8
  00034	48 6b c0 ff	 imul	 rax, rax, -1
  00038	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr_user$[rsp]
  0003d	48 8b 04 01	 mov	 rax, QWORD PTR [rcx+rax]
  00041	48 89 44 24 38	 mov	 QWORD PTR _Ptr_container$[rsp], rax

; 206  : 
; 207  :     // If the following asserts, it likely means that we are performing
; 208  :     // an aligned delete on memory coming from an unaligned allocation.
; 209  :     _STL_ASSERT(_Ptr_user[-2] == _Big_allocation_sentinel, "invalid argument");
; 210  : 
; 211  :     // Extra paranoia on aligned allocation/deallocation; ensure _Ptr_container is
; 212  :     // in range [_Min_back_shift, _Non_user_size]
; 213  : #ifdef _DEBUG
; 214  :     constexpr uintptr_t _Min_back_shift = 2 * sizeof(void*);
; 215  : #else // ^^^ defined(_DEBUG) / !defined(_DEBUG) vvv
; 216  :     constexpr uintptr_t _Min_back_shift = sizeof(void*);

  00046	48 c7 44 24 48
	08 00 00 00	 mov	 QWORD PTR _Min_back_shift$[rsp], 8

; 217  : #endif // ^^^ !defined(_DEBUG) ^^^
; 218  :     const uintptr_t _Back_shift = reinterpret_cast<uintptr_t>(_Ptr) - _Ptr_container;

  0004f	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00054	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr_container$[rsp]
  00059	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0005c	48 2b c1	 sub	 rax, rcx
  0005f	48 89 44 24 30	 mov	 QWORD PTR _Back_shift$[rsp], rax

; 219  :     _STL_VERIFY(_Back_shift >= _Min_back_shift && _Back_shift <= _Non_user_size, "invalid argument");

  00064	48 83 7c 24 30
	08		 cmp	 QWORD PTR _Back_shift$[rsp], 8
  0006a	72 08		 jb	 SHORT $LN3@Adjust_man
  0006c	48 83 7c 24 30
	27		 cmp	 QWORD PTR _Back_shift$[rsp], 39 ; 00000027H
  00072	76 19		 jbe	 SHORT $LN2@Adjust_man
$LN3@Adjust_man:
  00074	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR [rsp+32], 0
  0007d	45 33 c9	 xor	 r9d, r9d
  00080	45 33 c0	 xor	 r8d, r8d
  00083	33 d2		 xor	 edx, edx
  00085	33 c9		 xor	 ecx, ecx
  00087	e8 00 00 00 00	 call	 _invoke_watson
  0008c	90		 npad	 1
$LN2@Adjust_man:

; 220  :     _Ptr = reinterpret_cast<void*>(_Ptr_container);

  0008d	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00092	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr_container$[rsp]
  00097	48 89 08	 mov	 QWORD PTR [rax], rcx
$LN4@Adjust_man:

; 221  : }

  0009a	48 83 c4 58	 add	 rsp, 88			; 00000058H
  0009e	c3		 ret	 0
?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ENDP ; std::_Adjust_manually_vector_aligned
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?_Throw_bad_array_new_length@std@@YAXXZ
_TEXT	SEGMENT
$T1 = 32
?_Throw_bad_array_new_length@std@@YAXXZ PROC		; std::_Throw_bad_array_new_length, COMDAT

; 107  : [[noreturn]] inline void _Throw_bad_array_new_length() {

$LN3:
  00000	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 108  :     _THROW(bad_array_new_length{});

  00004	48 8d 4c 24 20	 lea	 rcx, QWORD PTR $T1[rsp]
  00009	e8 00 00 00 00	 call	 ??0bad_array_new_length@std@@QEAA@XZ ; std::bad_array_new_length::bad_array_new_length
  0000e	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:_TI3?AVbad_array_new_length@std@@
  00015	48 8d 4c 24 20	 lea	 rcx, QWORD PTR $T1[rsp]
  0001a	e8 00 00 00 00	 call	 _CxxThrowException
  0001f	90		 npad	 1
$LN2@Throw_bad_:

; 109  : }

  00020	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00024	c3		 ret	 0
?_Throw_bad_array_new_length@std@@YAXXZ ENDP		; std::_Throw_bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gbad_array_new_length@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gbad_array_new_length@std@@UEAAPEAXI@Z PROC		; std::bad_array_new_length::`scalar deleting destructor', COMDAT
$LN20:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gbad_array_new_length@std@@UEAAPEAXI@Z ENDP		; std::bad_array_new_length::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??0bad_array_new_length@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
__that$ = 56
??0bad_array_new_length@std@@QEAA@AEBV01@@Z PROC	; std::bad_array_new_length::bad_array_new_length, COMDAT
$LN14:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000e	48 8b 54 24 38	 mov	 rdx, QWORD PTR __that$[rsp]
  00013	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00018	e8 00 00 00 00	 call	 ??0bad_alloc@std@@QEAA@AEBV01@@Z
  0001d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00022	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_array_new_length@std@@6B@
  00029	48 89 08	 mov	 QWORD PTR [rax], rcx
  0002c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00031	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00035	c3		 ret	 0
??0bad_array_new_length@std@@QEAA@AEBV01@@Z ENDP	; std::bad_array_new_length::bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??1bad_array_new_length@std@@UEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1bad_array_new_length@std@@UEAA@XZ PROC		; std::bad_array_new_length::~bad_array_new_length, COMDAT
$LN14:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  00018	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 83 c0 08	 add	 rax, 8
  00021	48 8b c8	 mov	 rcx, rax
  00024	e8 00 00 00 00	 call	 __std_exception_destroy
  00029	90		 npad	 1
  0002a	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0002e	c3		 ret	 0
??1bad_array_new_length@std@@UEAA@XZ ENDP		; std::bad_array_new_length::~bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0bad_array_new_length@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 16
??0bad_array_new_length@std@@QEAA@XZ PROC		; std::bad_array_new_length::bad_array_new_length, COMDAT

; 144  :     {

$LN14:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi

; 67   :     {

  00006	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0000b	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00012	48 89 08	 mov	 QWORD PTR [rax], rcx

; 66   :         : _Data()

  00015	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 83 c0 08	 add	 rax, 8
  0001e	48 8b f8	 mov	 rdi, rax
  00021	33 c0		 xor	 eax, eax
  00023	b9 10 00 00 00	 mov	 ecx, 16
  00028	f3 aa		 rep stosb

; 68   :         _Data._What = _Message;

  0002a	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0002f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_0BF@KINCDENJ@bad?5array?5new?5length@
  00036	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 133  :     {

  0003a	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0003f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_alloc@std@@6B@
  00046	48 89 08	 mov	 QWORD PTR [rax], rcx

; 144  :     {

  00049	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_array_new_length@std@@6B@
  00055	48 89 08	 mov	 QWORD PTR [rax], rcx

; 145  :     }

  00058	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0005d	5f		 pop	 rdi
  0005e	c3		 ret	 0
??0bad_array_new_length@std@@QEAA@XZ ENDP		; std::bad_array_new_length::bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gbad_alloc@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gbad_alloc@std@@UEAAPEAXI@Z PROC			; std::bad_alloc::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gbad_alloc@std@@UEAAPEAXI@Z ENDP			; std::bad_alloc::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0bad_alloc@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
__that$ = 56
??0bad_alloc@std@@QEAA@AEBV01@@Z PROC			; std::bad_alloc::bad_alloc, COMDAT
$LN9:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 20	 sub	 rsp, 32			; 00000020H

; 73   :     {

  0000f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  0001b	48 89 08	 mov	 QWORD PTR [rax], rcx

; 72   :         : _Data()

  0001e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 83 c0 08	 add	 rax, 8
  00027	48 8b f8	 mov	 rdi, rax
  0002a	33 c0		 xor	 eax, eax
  0002c	b9 10 00 00 00	 mov	 ecx, 16
  00031	f3 aa		 rep stosb

; 74   :         __std_exception_copy(&_Other._Data, &_Data);

  00033	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 83 c0 08	 add	 rax, 8
  0003c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR __that$[rsp]
  00041	48 83 c1 08	 add	 rcx, 8
  00045	48 8b d0	 mov	 rdx, rax
  00048	e8 00 00 00 00	 call	 __std_exception_copy
  0004d	90		 npad	 1
  0004e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_alloc@std@@6B@
  0005a	48 89 08	 mov	 QWORD PTR [rax], rcx
  0005d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00062	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00066	5f		 pop	 rdi
  00067	c3		 ret	 0
??0bad_alloc@std@@QEAA@AEBV01@@Z ENDP			; std::bad_alloc::bad_alloc
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gexception@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gexception@std@@UEAAPEAXI@Z PROC			; std::exception::`scalar deleting destructor', COMDAT
$LN10:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gexception@std@@UEAAPEAXI@Z ENDP			; std::exception::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ?what@exception@std@@UEBAPEBDXZ
_TEXT	SEGMENT
tv69 = 0
this$ = 32
?what@exception@std@@UEBAPEBDXZ PROC			; std::exception::what, COMDAT

; 95   :     {

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 18	 sub	 rsp, 24

; 96   :         return _Data._What ? _Data._What : "Unknown exception";

  00009	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00013	74 0f		 je	 SHORT $LN3@what
  00015	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0001e	48 89 04 24	 mov	 QWORD PTR tv69[rsp], rax
  00022	eb 0b		 jmp	 SHORT $LN4@what
$LN3@what:
  00024	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BC@EOODALEL@Unknown?5exception@
  0002b	48 89 04 24	 mov	 QWORD PTR tv69[rsp], rax
$LN4@what:
  0002f	48 8b 04 24	 mov	 rax, QWORD PTR tv69[rsp]

; 97   :     }

  00033	48 83 c4 18	 add	 rsp, 24
  00037	c3		 ret	 0
?what@exception@std@@UEBAPEBDXZ ENDP			; std::exception::what
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0exception@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
_Other$ = 56
??0exception@std@@QEAA@AEBV01@@Z PROC			; std::exception::exception, COMDAT

; 73   :     {

$LN4:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  0000f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  0001b	48 89 08	 mov	 QWORD PTR [rax], rcx

; 72   :         : _Data()

  0001e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 83 c0 08	 add	 rax, 8
  00027	48 8b f8	 mov	 rdi, rax
  0002a	33 c0		 xor	 eax, eax
  0002c	b9 10 00 00 00	 mov	 ecx, 16
  00031	f3 aa		 rep stosb

; 74   :         __std_exception_copy(&_Other._Data, &_Data);

  00033	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 83 c0 08	 add	 rax, 8
  0003c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Other$[rsp]
  00041	48 83 c1 08	 add	 rcx, 8
  00045	48 8b d0	 mov	 rdx, rax
  00048	e8 00 00 00 00	 call	 __std_exception_copy
  0004d	90		 npad	 1

; 75   :     }

  0004e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00057	5f		 pop	 rdi
  00058	c3		 ret	 0
??0exception@std@@QEAA@AEBV01@@Z ENDP			; std::exception::exception
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MonitorWorld.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MonitorWorld.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
