; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	?ReqJoinExecutionPrev@JoinContextChannelChange@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@@Z ; mu2::JoinContextChannelChange::ReqJoinExecutionPrev
PUBLIC	?SendResJoinResult@JoinContextChannelChange@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z ; mu2::JoinContextChannelChange::SendResJoinResult
EXTRN	?GetPresentTime@DateTime@mu2@@SA?BV12@XZ:PROC	; mu2::DateTime::GetPresentTime
EXTRN	?ResetCharLoginTime@EntityPlayer@mu2@@QEAAXAEBVDateTime@2@@Z:PROC ; mu2::EntityPlayer::ResetCharLoginTime
EXTRN	?SendEscResGamePortal@JoinContextBase@mu2@@IEBAXAEAPEAVEntityPlayer@2@AEBVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z:PROC ; mu2::JoinContextBase::SendEscResGamePortal
EXTRN	_fltused:DWORD
;	COMDAT pdata
pdata	SEGMENT
$pdata$?ReqJoinExecutionPrev@JoinContextChannelChange@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@@Z DD imagerel $LN5
	DD	imagerel $LN5+103
	DD	imagerel $unwind$?ReqJoinExecutionPrev@JoinContextChannelChange@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?SendResJoinResult@JoinContextChannelChange@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z DD imagerel $LN3
	DD	imagerel $LN3+55
	DD	imagerel $unwind$?SendResJoinResult@JoinContextChannelChange@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z
pdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?SendResJoinResult@JoinContextChannelChange@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z DD 011801H
	DD	04218H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?ReqJoinExecutionPrev@JoinContextChannelChange@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@@Z DD 011301H
	DD	08213H
xdata	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextChangeChannel.cpp
;	COMDAT ?SendResJoinResult@JoinContextChannelChange@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z
_TEXT	SEGMENT
this$ = 48
player$ = 56
arrived$ = 64
eError$ = 72
?SendResJoinResult@JoinContextChannelChange@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z PROC ; mu2::JoinContextChannelChange::SendResJoinResult, COMDAT

; 17   : 	{

$LN3:
  00000	44 89 4c 24 20	 mov	 DWORD PTR [rsp+32], r9d
  00005	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 18   : 		SendEscResGamePortal(player, arrived, eError);

  00018	44 8b 4c 24 48	 mov	 r9d, DWORD PTR eError$[rsp]
  0001d	4c 8b 44 24 40	 mov	 r8, QWORD PTR arrived$[rsp]
  00022	48 8b 54 24 38	 mov	 rdx, QWORD PTR player$[rsp]
  00027	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0002c	e8 00 00 00 00	 call	 ?SendEscResGamePortal@JoinContextBase@mu2@@IEBAXAEAPEAVEntityPlayer@2@AEBVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z ; mu2::JoinContextBase::SendEscResGamePortal
  00031	90		 npad	 1

; 19   : 	}

  00032	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00036	c3		 ret	 0
?SendResJoinResult@JoinContextChannelChange@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z ENDP ; mu2::JoinContextChannelChange::SendResJoinResult
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextChangeChannel.cpp
; File F:\Release_Branch\Server\Development\Framework\Math\Vector3.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextChangeChannel.cpp
;	COMDAT ?ReqJoinExecutionPrev@JoinContextChannelChange@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@@Z
_TEXT	SEGMENT
this$ = 32
$T1 = 40
this$ = 80
player$ = 88
dest$ = 96
?ReqJoinExecutionPrev@JoinContextChannelChange@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@@Z PROC ; mu2::JoinContextChannelChange::ReqJoinExecutionPrev, COMDAT

; 11   : 	{

$LN5:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 12   : 		player->ResetCharLoginTime(DateTime::GetPresentTime());

  00013	48 8d 4c 24 28	 lea	 rcx, QWORD PTR $T1[rsp]
  00018	e8 00 00 00 00	 call	 ?GetPresentTime@DateTime@mu2@@SA?BV12@XZ ; mu2::DateTime::GetPresentTime
  0001d	48 8b d0	 mov	 rdx, rax
  00020	48 8b 44 24 58	 mov	 rax, QWORD PTR player$[rsp]
  00025	48 8b 08	 mov	 rcx, QWORD PTR [rax]
  00028	e8 00 00 00 00	 call	 ?ResetCharLoginTime@EntityPlayer@mu2@@QEAAXAEBVDateTime@2@@Z ; mu2::EntityPlayer::ResetCharLoginTime
  0002d	90		 npad	 1

; 13   : 		dest.position.clear();

  0002e	48 8b 44 24 60	 mov	 rax, QWORD PTR dest$[rsp]
  00033	48 83 c0 20	 add	 rax, 32			; 00000020H
  00037	48 89 44 24 20	 mov	 QWORD PTR this$[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Math\Vector3.h

; 154  : 	x = y = z = 0.0f;

  0003c	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00041	0f 57 c0	 xorps	 xmm0, xmm0
  00044	f3 0f 11 40 08	 movss	 DWORD PTR [rax+8], xmm0
  00049	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0004e	0f 57 c0	 xorps	 xmm0, xmm0
  00051	f3 0f 11 40 04	 movss	 DWORD PTR [rax+4], xmm0
  00056	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0005b	0f 57 c0	 xorps	 xmm0, xmm0
  0005e	f3 0f 11 00	 movss	 DWORD PTR [rax], xmm0
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextChangeChannel.cpp

; 14   : 	}

  00062	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00066	c3		 ret	 0
?ReqJoinExecutionPrev@JoinContextChannelChange@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@@Z ENDP ; mu2::JoinContextChannelChange::ReqJoinExecutionPrev
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextChangeChannel.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextChangeChannel.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
