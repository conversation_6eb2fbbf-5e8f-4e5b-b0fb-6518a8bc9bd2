F:\Release_Branch\Server\Development\Frontend\WorldServer\AchievementHandler.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\AchievementHandler.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\AchievementJobLimitAchievementCache.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\AchievementJobLimitAchievementCache.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\AchievementManager.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\AchievementManager.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\AppChat.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\AttributePlayer.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\AttributePlayer.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaContinetInfo.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\BurningStaminaContinetInfo.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaHandler.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\BurningStaminaHandler.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaInfo.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\BurningStaminaInfo.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaManager.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\BurningStaminaManager.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaTimeInfo.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\BurningStaminaTimeInfo.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaUserInfo.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\BurningStaminaUserInfo.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\Channel.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\Channel.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\ChannelDominion.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\ChannelDominion.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\ChannelFieldpoint.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\ChannelFieldpoint.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\ChannelKnightageFieldRaid.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\ChannelKnightageFieldRaid.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\ChannelGroup.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\ChannelGroup.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\ChannelManager.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\ChannelManager.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\ChannelNull.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\ChannelNull.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\ChatHandler.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\ChatHandler.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\ColosseumHandler.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\ColosseumHandler.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\CommunityFriend.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\CommunityFriend.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\CommunitySearchChar.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\CommunitySearchChar.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\CommunityCharBlock.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\CommunityCharBlock.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\CommunityTeacherAndStudent.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\CommunityTeacherAndStudent.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\ContentsDeactivator.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\ContentsDeactivator.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyComparer.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\DailyComparer.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyMissionHandler.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\DailyMissionHandler.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyMissionManager.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\DailyMissionManager.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyTimeConnectionManager.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\DailyTimeConnectionManager.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\Entity4WorldServer.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\Entity4WorldServer.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\Dominion\Dominion.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\Dominion.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\Dominion\DominionManager.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\DominionManager.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\EntityParty.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\EntityParty.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\EntityPlayer.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\EntityPlayer.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\EventSchedule.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\EventScheduleManager.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\Event\EventSystem.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\EventSystem.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\Event\PlayerReturnEventAction.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PlayerReturnEventAction.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\ExecInstanceBase.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\ExecInstanceBase.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\Execution.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\ExecutionGroup.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\ExecutionGroup.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\ExecutionNull.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\ExecutionNull.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\Fcs\WorldFcsAsyncReceiverAuth.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WorldFcsAsyncReceiverAuth.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\FieldPoint\FieldPointForwarder.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\FieldPointForwarder.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\FieldPoint\FieldPointHandler.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\FieldPointHandler.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\FieldPoint\FieldPointManager.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\FieldPointManager.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\FieldPoint\FieldPointProcessor.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\FieldPointProcessor.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\Instance.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\Instance.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceColloseumPvp33.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\InstanceColloseumPvp33.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceGroup.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\InstanceGroup.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceMultisage.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\InstanceMultisage.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceNormal.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\InstanceNormal.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceNull.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\InstanceNull.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\InstancePartyInfo.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\InstancePartyInfo.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\InstancePvp.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\InstancePvp.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceSinglestage.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\InstanceSinglestage.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceTournament.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\InstanceTournament.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceWorldcross.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\InstanceWorldcross.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContext.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\JoinZoneContext.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextBase.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\JoinZoneContextBase.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextChangeChannel.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\JoinZoneContextChangeChannel.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextChangeMap.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\JoinZoneContextChangeMap.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextCheckIn.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\JoinZoneContextCheckIn.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextLogout.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\JoinZoneContextLogout.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextTutorial.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\JoinZoneContextTutorial.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextWarpMap.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\JoinZoneContextWarpMap.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\Knightage\KnightageFieldRaidScheduler.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\KnightageFieldRaidScheduler.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\Knightage\KnightagePaymentScheduler.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\KnightagePaymentScheduler.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\Liberation\LiberationManager.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\LiberationManager.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\LuckyMonster\LuckyMonsterHandler.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\LuckyMonsterHandler.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\LuckyMonster\LuckyMonsterManager.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\LuckyMonsterManager.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\ManagerInvasionContext.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\ManagerInvasionContext.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\ManagerJoinZoneContext.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\ManagerJoinZoneContext.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\Membership\PlayerMembershipAction.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PlayerMembershipAction.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\MissionMapJoinManager.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\MopupHandler.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\MopupHandler.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\Mopup\MopupManager.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\MopupManager.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\OhrdorTreasureHunt.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\OhrdorTreasureHunt.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\PCRoom\PCRoomBillingEventLoopBackHandler.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PCRoomBillingEventLoopBackHandler.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerDailyLoginAction.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PlayerDailyLoginAction.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyLoginHandler.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\DailyLoginHandler.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\DailyLoginManager.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\DailyLoginManager.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\DamageMeterHandler.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\DamageMeterHandler.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\DamageMeterManager.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\DamageMeterManager.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\DungeonLog.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\DungeonLog.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\DungeonLogManager.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\DungeonLogManager.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\GlobalGameConfigInfo.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\GlobalGameConfigInfo.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\GlobalGameConfigManager.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\GlobalGameConfigManager.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\Invasion\InvasionAction.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\InvasionAction.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\JoinRankTimeCalculator.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\KISS\KISSApiWrapper.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\KISSApiWrapper.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\Knightage\Knightage.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\Knightage.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\Knightage\KnightageAbility.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\KnightageAbility.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\Knightage\KnightageHandler.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\KnightageHandler.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\Knightage\KnightageLoader.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\KnightageLoader.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\Knightage\KnightageManager.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\KnightageManager.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\Knightage\KnightageMember.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\KnightageMember.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\Knightage\KnightageSaveChecker.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\KnightageSaveChecker.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\Knightage\PlayerKnightageAction.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PlayerKnightageAction.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\MonitorWorld.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\MonitorWorld.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerDailyMissionAction.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PlayerDailyMissionAction.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerDailyTimeConnectionAction.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PlayerDailyTimeConnectionAction.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerTeacherAndStudentAction.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PlayerTeacherAndStudentAction.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\PVPFieldSchedule.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PVPFieldSchedule.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\QuestSystem.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\QuestSystem.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\Raid\RaidHandler.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\RaidHandler.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\Raid\RaidManager.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\RaidManager.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\Raid\TournamentRaid.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\TournamentRaid.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\Ranking\Ranking.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\Ranking.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\ReLoginGetFcsAuthInfoHandler.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\ReLoginGetFcsAuthInfoHandler.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\Season\SeasonHandler.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\SeasonHandler.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\SwitchInvasion.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\SwitchInvasion.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\SwitchNonInvasion.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\SwitchNonInvasion.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\Season\SeasonManager.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\SeasonManager.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\Tournament\Tournament.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\Tournament.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\Tournament\TournamentManager.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\TournamentManager.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSAutoHackDetectionEvent.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WOPSAutoHackDetectionEvent.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSAutoHackRemoveDebuffEvent.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WOPSAutoHackRemoveDebuffEvent.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSChatBlockCharacterEvent.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WOPSChatBlockCharacterEvent.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSCmdKnightage.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WOPSCmdKnightage.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSCommandItemEnchantDiscount.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WOPSCommandItemEnchantDiscount.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEditKnightageTrophyPoint.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WOPSEditKnightageTrophyPoint.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSExchangeItemEvent.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WOPSExchangeItemEvent.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSKickAccountEvent.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WOPSKickAccountEvent.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSKickCharacterEvent.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WOPSKickCharacterEvent.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSLoginBlockAccountEvent.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WOPSLoginBlockAccountEvent.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSMinigameRewardEvent.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WOPSMinigameRewardEvent.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSMopupEvent.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WOPSMopupEvent.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSNoticeInstantEvent.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WOPSNoticeInstantEvent.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSNoticeReservationEvent.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WOPSNoticeReservationEvent.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSWorldCloseEvent.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WOPSWorldCloseEvent.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSWorldOpenEvent.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WOPSWorldOpenEvent.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\BaseItemTranscation.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\BaseItemTranscation.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\KnightageTranscation.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\KnightageTranscation.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\PlayerWShopAction.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PlayerWShopAction.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\TaskItemTranscation.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WShop.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopBillingEventHandler.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WShopBillingEventHandler.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopInventoryEventHandler.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WShopInventoryEventHandler.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJewelEventHandler.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WShopJewelEventHandler.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WShopJob.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJobManager.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WShopJobManager.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopStatusEventHandler.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WShopStatusEventHandler.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTask.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WShopTask.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskChargeJewel.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WShopTaskChargeJewel.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskCheckBalance.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WShopTaskCheckBalance.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskGift.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WShopTaskGift.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskInventory.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WShopTaskInventory.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskItemTranscation.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WShopTaskItemTranscation.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskMopupPurchaseJewel.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WShopTaskMopupPurchaseJewel.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPickup.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WShopTaskPickup.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPickupTradeJewel.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WShopTaskPickupTradeJewel.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchase.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WShopTaskPurchase.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseCharacSlot.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WShopTaskPurchaseCharacSlot.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskPurchaseJewel.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WShopTaskPurchaseJewel.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTaskTradeJewel.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WShopTaskTradeJewel.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\ZoneLoadBalancer.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\ZoneLoadBalancer.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\MultistageDungeonHandler.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\MultistageDungeonHandler.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\MultistageDungeonManager.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\MultistageDungeonManager.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyDungeonManager.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PartyDungeonManager.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\ExecutionManager.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\ExecutionManager.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\CommunityHandler.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\CommunityHandler.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\GloryRankManager.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\GloryRankManager.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\GloryRankHandler.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\GloryRankHandler.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\GMCommandSystem.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\GMCommandSystem.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceManager.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\InstanceManager.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\JoinRankManager.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinWaitHandler.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\JoinWaitHandler.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\main.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\main.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapHandler.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\MissionMapHandler.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyAction.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PartyAction.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityFactoryImpl.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PartyEntityFactoryImpl.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyEntityManager.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PartyEntityManager.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\PartyHandler.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PartyHandler.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\PartySearchManager.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PartySearchManager.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\PCRoom\Export\PCBillAdapterLoader.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PCBillAdapterLoader.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\PCRoom\PCRoomBillingEventHandler.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PCRoomBillingEventHandler.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\PCRoom\PCRoomBillingManager.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PCRoomBillingManager.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerBlockAction.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PlayerBlockAction.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerChatPenaltyAction.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PlayerChatPenaltyAction.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerDamageMeterAction.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PlayerDamageMeterAction.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerFriendAction.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PlayerFriendAction.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerHandler.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PlayerHandler.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerJoinAction.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PlayerJoinAction.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerMissionmapAction.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PlayerMissionmapAction.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerPartyAction.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PlayerPartyAction.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerSearchPartyAction.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PlayerSearchPartyAction.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerPortalAction.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PlayerPortalAction.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerServantAction.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PlayerServantAction.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerServantHandler.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PlayerServantHandler.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerStateJoinExecution.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PlayerStateJoinExecution.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerStateWait.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PlayerStateWait.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerStateZone.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PlayerStateZone.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerSearchCharAction.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PlayerSearchCharAction.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\DungeonMatchHandler.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\DungeonMatchHandler.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\DungeonMatchManager.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\DungeonMatchManager.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\DungeonMatchRoom.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\DungeonMatchRoom.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\Ranking\PlayerRankingAction.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PlayerRankingAction.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\Ranking\RankingHandler.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\RankingHandler.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\Ranking\RankingManager.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\RankingManager.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\stdafx.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\stdafx.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\EntityManager.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\EntityManager.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerLobbyAction.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PlayerLobbyAction.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerStateLobby.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\PlayerStateLobby.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\SyncSystem.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\SyncSystem.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEvent.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WOPSEvent.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeBuffEvent.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WOPSGoldenTimeBuffEvent.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeGiftEvent.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WOPSGoldenTimeGiftEvent.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\WOPSEventNotifier.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WOPSEventNotifier.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\WOPSHandler.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WOPSHandler.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\WOPSEventManager.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WOPSEventManager.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldEntityFactoryImpl.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WorldEntityFactoryImpl.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldHandler.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WorldHandler.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WorldRunner.obj
F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldServer.cpp;F:\Release_Branch\Server\Intermediate\x64\Release_Global\WorldServer\WorldServer.obj
