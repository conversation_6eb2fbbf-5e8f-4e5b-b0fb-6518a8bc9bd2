﻿#pragma once

namespace mu2
{
	class WorkBase;
	class QueryExecutor : public AsyncWorker::Executor
	{	
		friend WorkBase;

#pragma region 
	public:
		enum
		{
			DB_None = 0,

			DB_UspLoginByDev,
			DB_UspLoginByFcs,
			DB_UspLoginByRelogin,
			DB_UspSessionInfoChoicedWorld,
			DB_UspSessionInfoGet,
			DB_UspLogout,
			
			DB_UspSSCodeCheck,			// Account DB UspSSCodeCheck
			DB_UspSSCodeSet,			// Account DB UspSSCodeSet
			DB_UspSSCodeReset,			// Account DB UspSSCodeReset
		};

		enum State
		{
			DISCONNECTED,
			CONNECTED,
		};

#pragma endregion

	private:
		std::string					m_dsn;
		State						m_state;
		SqlSession*					m_sqlSession;
		SqlPrepare*					m_sqlPrepare;		
		WorkBase*					m_work;

	public:
		QueryExecutor();
		virtual ~QueryExecutor();

		Bool Initialize(std::string dsn);
		void UnInitialize();
		virtual Bool Run(EventPtr&) final { return true; }		
				
		ErrorLogin::Error QueryUspSSCodeReset(EventPtr& e);
		ErrorLogin::Error QueryUspSessionInfoChoicedWorld(EventPtr& e);
		ErrorLogin::Error QueryUspSessionInfoGet(EventPtr& e);	
		ErrorLogin::Error QuerySSCodeSet(EventPtr& e);
		ErrorLogin::Error QueryUspSSCodeCheck(EventPtr& e);
		ErrorLogin::Error QueryUspLogout(EventPtr& e);
		ErrorLogin::Error QueryReLogin(EventPtr& e);
		ErrorLogin::Error QueryUspLoginByCommon(SqlParam* query);
		ErrorLogin::Error QueryUspLoginByDev(EventPtr& e);
		ErrorLogin::Error QueryUspLobinByFcs(EventPtr& e);
		
	private:
		void SetWork(WorkBase* work) { m_work = work; }
		Bool RegistQuery();
		void sendSystemErrorMsg(UInt32 idx, UInt32 err);
	};


}
