; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	??0exception@std@@QEAA@AEBV01@@Z		; std::exception::exception
PUBLIC	?what@exception@std@@UEBAPEBDXZ			; std::exception::what
PUBLIC	??_Gexception@std@@UEAAPEAXI@Z			; std::exception::`scalar deleting destructor'
PUBLIC	??0bad_alloc@std@@QEAA@AEBV01@@Z		; std::bad_alloc::bad_alloc
PUBLIC	??_Gbad_alloc@std@@UEAAPEAXI@Z			; std::bad_alloc::`scalar deleting destructor'
PUBL<PERSON>	??0bad_array_new_length@std@@QEAA@XZ		; std::bad_array_new_length::bad_array_new_length
PUBLIC	??1bad_array_new_length@std@@UEAA@XZ		; std::bad_array_new_length::~bad_array_new_length
PUBLIC	??0bad_array_new_length@std@@QEAA@AEBV01@@Z	; std::bad_array_new_length::bad_array_new_length
PUBLIC	??_Gbad_array_new_length@std@@UEAAPEAXI@Z	; std::bad_array_new_length::`scalar deleting destructor'
PUBLIC	?_Throw_bad_array_new_length@std@@YAXXZ		; std::_Throw_bad_array_new_length
PUBLIC	?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
PUBLIC	?_Tidy@?$vector@UScheduleCycle@mu2@@V?$allocator@UScheduleCycle@mu2@@@std@@@std@@AEAAXXZ ; std::vector<mu2::ScheduleCycle,std::allocator<mu2::ScheduleCycle> >::_Tidy
PUBLIC	??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
PUBLIC	??0EventSchedule@mu2@@QEAA@XZ			; mu2::EventSchedule::EventSchedule
PUBLIC	??1EventSchedule@mu2@@UEAA@XZ			; mu2::EventSchedule::~EventSchedule
PUBLIC	?InitializeEventSchedule@EventSchedule@mu2@@QEAAXXZ ; mu2::EventSchedule::InitializeEventSchedule
PUBLIC	?Update@EventSchedule@mu2@@QEAAXXZ		; mu2::EventSchedule::Update
PUBLIC	?refreshNextWeek@EventSchedule@mu2@@AEAAXXZ	; mu2::EventSchedule::refreshNextWeek
PUBLIC	?processClosedState@EventSchedule@mu2@@AEAAXAEB_J@Z ; mu2::EventSchedule::processClosedState
PUBLIC	?processReadyState@EventSchedule@mu2@@AEAAXAEB_J@Z ; mu2::EventSchedule::processReadyState
PUBLIC	?processOpenState@EventSchedule@mu2@@AEAAXAEB_J@Z ; mu2::EventSchedule::processOpenState
PUBLIC	?isWeekendToday@EventSchedule@mu2@@IEAA_NXZ	; mu2::EventSchedule::isWeekendToday
PUBLIC	?GetDayofWeek@EventSchedule@mu2@@IEAAEXZ	; mu2::EventSchedule::GetDayofWeek
PUBLIC	?getWeekStartTime@EventSchedule@mu2@@IEAA_JXZ	; mu2::EventSchedule::getWeekStartTime
PUBLIC	?getReadyToStartTime@EventSchedule@mu2@@MEAAHXZ	; mu2::EventSchedule::getReadyToStartTime
PUBLIC	?OnScheduleEvent@EventSchedule@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ; mu2::EventSchedule::OnScheduleEvent
PUBLIC	?allocate@?$allocator@UEventScheduleDate@mu2@@@std@@QEAAPEAUEventScheduleDate@mu2@@_K@Z ; std::allocator<mu2::EventScheduleDate>::allocate
PUBLIC	?_Calculate_growth@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEBA_K_K@Z ; std::vector<mu2::EventScheduleDate,std::allocator<mu2::EventScheduleDate> >::_Calculate_growth
PUBLIC	?_Change_array@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAXQEAUEventScheduleDate@mu2@@_K1@Z ; std::vector<mu2::EventScheduleDate,std::allocator<mu2::EventScheduleDate> >::_Change_array
PUBLIC	?_Tidy@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAXXZ ; std::vector<mu2::EventScheduleDate,std::allocator<mu2::EventScheduleDate> >::_Tidy
PUBLIC	?_Xlength@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@CAXXZ ; std::vector<mu2::EventScheduleDate,std::allocator<mu2::EventScheduleDate> >::_Xlength
PUBLIC	??_GEventSchedule@mu2@@UEAAPEAXI@Z		; mu2::EventSchedule::`scalar deleting destructor'
PUBLIC	??$_Emplace_one_at_back@AEBUEventScheduleDate@mu2@@@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAAEAUEventScheduleDate@mu2@@AEBU23@@Z ; std::vector<mu2::EventScheduleDate,std::allocator<mu2::EventScheduleDate> >::_Emplace_one_at_back<mu2::EventScheduleDate const &>
PUBLIC	??$_Emplace_reallocate@AEBUEventScheduleDate@mu2@@@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAPEAUEventScheduleDate@mu2@@QEAU23@AEBU23@@Z ; std::vector<mu2::EventScheduleDate,std::allocator<mu2::EventScheduleDate> >::_Emplace_reallocate<mu2::EventScheduleDate const &>
PUBLIC	??1_Reallocation_guard@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@QEAA@XZ ; std::vector<mu2::EventScheduleDate,std::allocator<mu2::EventScheduleDate> >::_Reallocation_guard::~_Reallocation_guard
PUBLIC	??$_Uninitialized_move@PEAUEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@YAPEAUEventScheduleDate@mu2@@QEAU12@0PEAU12@AEAV?$allocator@UEventScheduleDate@mu2@@@0@@Z ; std::_Uninitialized_move<mu2::EventScheduleDate *,std::allocator<mu2::EventScheduleDate> >
PUBLIC	??$_Copy_backward_memmove@PEAUEventScheduleDate@mu2@@PEAU12@@std@@YAPEAUEventScheduleDate@mu2@@PEAU12@00@Z ; std::_Copy_backward_memmove<mu2::EventScheduleDate *,mu2::EventScheduleDate *>
PUBLIC	??$_Copy_memmove@PEAUEventScheduleDate@mu2@@PEAU12@@std@@YAPEAUEventScheduleDate@mu2@@PEAU12@00@Z ; std::_Copy_memmove<mu2::EventScheduleDate *,mu2::EventScheduleDate *>
PUBLIC	??$_Copy_memmove_tail@PEAUEventScheduleDate@mu2@@@std@@YAPEAUEventScheduleDate@mu2@@QEBDQEAU12@_K2@Z ; std::_Copy_memmove_tail<mu2::EventScheduleDate *>
PUBLIC	??_7exception@std@@6B@				; std::exception::`vftable'
PUBLIC	??_C@_0BC@EOODALEL@Unknown?5exception@		; `string'
PUBLIC	??_7bad_alloc@std@@6B@				; std::bad_alloc::`vftable'
PUBLIC	??_7bad_array_new_length@std@@6B@		; std::bad_array_new_length::`vftable'
PUBLIC	??_C@_0BF@KINCDENJ@bad?5array?5new?5length@	; `string'
PUBLIC	??_R0?AVexception@std@@@8			; std::exception `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
PUBLIC	_TI3?AVbad_array_new_length@std@@
PUBLIC	_CTA3?AVbad_array_new_length@std@@
PUBLIC	??_R0?AVbad_array_new_length@std@@@8		; std::bad_array_new_length `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
PUBLIC	??_R0?AVbad_alloc@std@@@8			; std::bad_alloc `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
PUBLIC	??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@		; `string'
PUBLIC	??_C@_0BA@FOIKENOD@vector?5too?5long@		; `string'
PUBLIC	??_R4exception@std@@6B@				; std::exception::`RTTI Complete Object Locator'
PUBLIC	??_R3exception@std@@8				; std::exception::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2exception@std@@8				; std::exception::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@exception@std@@8			; std::exception::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4bad_array_new_length@std@@6B@		; std::bad_array_new_length::`RTTI Complete Object Locator'
PUBLIC	??_R3bad_array_new_length@std@@8		; std::bad_array_new_length::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2bad_array_new_length@std@@8		; std::bad_array_new_length::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@bad_array_new_length@std@@8	; std::bad_array_new_length::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@bad_alloc@std@@8			; std::bad_alloc::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R3bad_alloc@std@@8				; std::bad_alloc::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2bad_alloc@std@@8				; std::bad_alloc::`RTTI Base Class Array'
PUBLIC	??_R4bad_alloc@std@@6B@				; std::bad_alloc::`RTTI Complete Object Locator'
PUBLIC	??_7EventSchedule@mu2@@6B@			; mu2::EventSchedule::`vftable'
PUBLIC	??_C@_0GN@NJKGFILB@World?5EventSchedule?5ready?3?$CFd?9?$CF0@ ; `string'
PUBLIC	??_C@_0EM@EAHOIPCK@F?3?2Release_Branch?2Server?2Develo@ ; `string'
PUBLIC	??_C@_0CE@PHJOKCDC@mu2?3?3EventSchedule?3?3refreshNext@ ; `string'
PUBLIC	??_R4EventSchedule@mu2@@6B@			; mu2::EventSchedule::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVEventSchedule@mu2@@@8			; mu2::EventSchedule `RTTI Type Descriptor'
PUBLIC	??_R3EventSchedule@mu2@@8			; mu2::EventSchedule::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2EventSchedule@mu2@@8			; mu2::EventSchedule::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@EventSchedule@mu2@@8		; mu2::EventSchedule::`RTTI Base Class Descriptor at (0,-1,0,64)'
EXTRN	_purecall:PROC
EXTRN	??2@YAPEAX_K@Z:PROC				; operator new
EXTRN	??3@YAXPEAX_K@Z:PROC				; operator delete
EXTRN	__std_terminate:PROC
EXTRN	_invoke_watson:PROC
EXTRN	memmove:PROC
EXTRN	__std_exception_copy:PROC
EXTRN	__std_exception_destroy:PROC
EXTRN	??_Eexception@std@@UEAAPEAXI@Z:PROC		; std::exception::`vector deleting destructor'
EXTRN	??_Ebad_alloc@std@@UEAAPEAXI@Z:PROC		; std::bad_alloc::`vector deleting destructor'
EXTRN	??_Ebad_array_new_length@std@@UEAAPEAXI@Z:PROC	; std::bad_array_new_length::`vector deleting destructor'
EXTRN	?_Xlength_error@std@@YAXPEBD@Z:PROC		; std::_Xlength_error
EXTRN	_localtime64_s:PROC
EXTRN	_time64:PROC
EXTRN	?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ:PROC	; mu2::Logger::Logging
EXTRN	??_EEventSchedule@mu2@@UEAAPEAXI@Z:PROC		; mu2::EventSchedule::`vector deleting destructor'
EXTRN	_CxxThrowException:PROC
EXTRN	__CxxFrameHandler4:PROC
EXTRN	__GSHandlerCheck:PROC
EXTRN	__security_check_cookie:PROC
EXTRN	??_7type_info@@6B@:BYTE				; type_info::`vftable'
EXTRN	__security_cookie:QWORD
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0exception@std@@QEAA@AEBV01@@Z DD imagerel $LN4
	DD	imagerel $LN4+89
	DD	imagerel $unwind$??0exception@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?what@exception@std@@UEBAPEBDXZ DD imagerel $LN5
	DD	imagerel $LN5+56
	DD	imagerel $unwind$?what@exception@std@@UEBAPEBDXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gexception@std@@UEAAPEAXI@Z DD imagerel $LN10
	DD	imagerel $LN10+83
	DD	imagerel $unwind$??_Gexception@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z DD imagerel $LN9
	DD	imagerel $LN9+104
	DD	imagerel $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+83
	DD	imagerel $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_array_new_length@std@@QEAA@XZ DD imagerel $LN14
	DD	imagerel $LN14+95
	DD	imagerel $unwind$??0bad_array_new_length@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1bad_array_new_length@std@@UEAA@XZ DD imagerel $LN14
	DD	imagerel $LN14+47
	DD	imagerel $unwind$??1bad_array_new_length@std@@UEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z DD imagerel $LN14
	DD	imagerel $LN14+54
	DD	imagerel $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z DD imagerel $LN20
	DD	imagerel $LN20+83
	DD	imagerel $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Throw_bad_array_new_length@std@@YAXXZ DD imagerel $LN3
	DD	imagerel $LN3+37
	DD	imagerel $unwind$?_Throw_bad_array_new_length@std@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z DD imagerel $LN5
	DD	imagerel $LN5+159
	DD	imagerel $unwind$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Tidy@?$vector@UScheduleCycle@mu2@@V?$allocator@UScheduleCycle@mu2@@@std@@@std@@AEAAXXZ DD imagerel $LN40
	DD	imagerel $LN40+295
	DD	imagerel $unwind$?_Tidy@?$vector@UScheduleCycle@mu2@@V?$allocator@UScheduleCycle@mu2@@@std@@@std@@AEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z DD imagerel $LN7
	DD	imagerel $LN7+150
	DD	imagerel $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0EventSchedule@mu2@@QEAA@XZ DD imagerel $LN60
	DD	imagerel $LN60+265
	DD	imagerel $unwind$??0EventSchedule@mu2@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1EventSchedule@mu2@@UEAA@XZ DD imagerel $LN122
	DD	imagerel $LN122+195
	DD	imagerel $unwind$??1EventSchedule@mu2@@UEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?InitializeEventSchedule@EventSchedule@mu2@@QEAAXXZ DD imagerel $LN423
	DD	imagerel $LN423+1023
	DD	imagerel $unwind$?InitializeEventSchedule@EventSchedule@mu2@@QEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Update@EventSchedule@mu2@@QEAAXXZ DD imagerel $LN16
	DD	imagerel $LN16+212
	DD	imagerel $unwind$?Update@EventSchedule@mu2@@QEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?refreshNextWeek@EventSchedule@mu2@@AEAAXXZ DD imagerel $LN186
	DD	imagerel $LN186+1727
	DD	imagerel $unwind$?refreshNextWeek@EventSchedule@mu2@@AEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?processClosedState@EventSchedule@mu2@@AEAAXAEB_J@Z DD imagerel $LN6
	DD	imagerel $LN6+99
	DD	imagerel $unwind$?processClosedState@EventSchedule@mu2@@AEAAXAEB_J@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?processReadyState@EventSchedule@mu2@@AEAAXAEB_J@Z DD imagerel $LN6
	DD	imagerel $LN6+99
	DD	imagerel $unwind$?processReadyState@EventSchedule@mu2@@AEAAXAEB_J@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?processOpenState@EventSchedule@mu2@@AEAAXAEB_J@Z DD imagerel $LN8
	DD	imagerel $LN8+155
	DD	imagerel $unwind$?processOpenState@EventSchedule@mu2@@AEAAXAEB_J@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?isWeekendToday@EventSchedule@mu2@@IEAA_NXZ DD imagerel $LN9
	DD	imagerel $LN9+100
	DD	imagerel $unwind$?isWeekendToday@EventSchedule@mu2@@IEAA_NXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?GetDayofWeek@EventSchedule@mu2@@IEAAEXZ DD imagerel $LN7
	DD	imagerel $LN7+85
	DD	imagerel $unwind$?GetDayofWeek@EventSchedule@mu2@@IEAAEXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?getWeekStartTime@EventSchedule@mu2@@IEAA_JXZ DD imagerel $LN13
	DD	imagerel $LN13+181
	DD	imagerel $unwind$?getWeekStartTime@EventSchedule@mu2@@IEAA_JXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?allocate@?$allocator@UEventScheduleDate@mu2@@@std@@QEAAPEAUEventScheduleDate@mu2@@_K@Z DD imagerel $LN13
	DD	imagerel $LN13+163
	DD	imagerel $unwind$?allocate@?$allocator@UEventScheduleDate@mu2@@@std@@QEAAPEAUEventScheduleDate@mu2@@_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Calculate_growth@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEBA_K_K@Z DD imagerel $LN42
	DD	imagerel $LN42+338
	DD	imagerel $unwind$?_Calculate_growth@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEBA_K_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Change_array@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAXQEAUEventScheduleDate@mu2@@_K1@Z DD imagerel $LN40
	DD	imagerel $LN40+361
	DD	imagerel $unwind$?_Change_array@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAXQEAUEventScheduleDate@mu2@@_K1@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Tidy@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAXXZ DD imagerel $LN40
	DD	imagerel $LN40+298
	DD	imagerel $unwind$?_Tidy@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Xlength@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@CAXXZ DD imagerel $LN3
	DD	imagerel $LN3+22
	DD	imagerel $unwind$?_Xlength@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@CAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GEventSchedule@mu2@@UEAAPEAXI@Z DD imagerel $LN5
	DD	imagerel $LN5+60
	DD	imagerel $unwind$??_GEventSchedule@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$sort@V?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@UEventScheduleDate@mu2@@@std@@@std@@@std@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXV?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@UEventScheduleDate@mu2@@@std@@@std@@@0@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z DD imagerel ??$sort@V?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@UEventScheduleDate@mu2@@@std@@@std@@@std@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXV?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@UEventScheduleDate@mu2@@@std@@@std@@@0@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z
	DD	imagerel ??$sort@V?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@UEventScheduleDate@mu2@@@std@@@std@@@std@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXV?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@UEventScheduleDate@mu2@@@std@@@std@@@0@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z+205
	DD	imagerel $unwind$??$sort@V?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@UEventScheduleDate@mu2@@@std@@@std@@@std@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXV?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@UEventScheduleDate@mu2@@@std@@@std@@@0@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Sort_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@0_JV<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z DD imagerel ??$_Sort_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@0_JV<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z
	DD	imagerel ??$_Sort_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@0_JV<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z+406
	DD	imagerel $unwind$??$_Sort_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@0_JV<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Emplace_one_at_back@AEBUEventScheduleDate@mu2@@@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAAEAUEventScheduleDate@mu2@@AEBU23@@Z DD imagerel $LN419
	DD	imagerel $LN419+305
	DD	imagerel $unwind$??$_Emplace_one_at_back@AEBUEventScheduleDate@mu2@@@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAAEAUEventScheduleDate@mu2@@AEBU23@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Insertion_sort_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAPEAUEventScheduleDate@mu2@@QEAU12@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z DD imagerel ??$_Insertion_sort_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAPEAUEventScheduleDate@mu2@@QEAU12@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z
	DD	imagerel ??$_Insertion_sort_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAPEAUEventScheduleDate@mu2@@QEAU12@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z+588
	DD	imagerel $unwind$??$_Insertion_sort_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAPEAUEventScheduleDate@mu2@@QEAU12@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Make_heap_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z DD imagerel ??$_Make_heap_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z
	DD	imagerel ??$_Make_heap_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z+243
	DD	imagerel $unwind$??$_Make_heap_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Partition_by_median_guess_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YA?AU?$pair@PEAUEventScheduleDate@mu2@@PEAU12@@0@PEAUEventScheduleDate@mu2@@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z DD imagerel ??$_Partition_by_median_guess_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YA?AU?$pair@PEAUEventScheduleDate@mu2@@PEAU12@@0@PEAUEventScheduleDate@mu2@@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z
	DD	imagerel ??$_Partition_by_median_guess_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YA?AU?$pair@PEAUEventScheduleDate@mu2@@PEAU12@@0@PEAUEventScheduleDate@mu2@@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z+2447
	DD	imagerel $unwind$??$_Partition_by_median_guess_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YA?AU?$pair@PEAUEventScheduleDate@mu2@@PEAU12@@0@PEAUEventScheduleDate@mu2@@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Emplace_reallocate@AEBUEventScheduleDate@mu2@@@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAPEAUEventScheduleDate@mu2@@QEAU23@AEBU23@@Z DD imagerel $LN386
	DD	imagerel $LN386+972
	DD	imagerel $unwind$??$_Emplace_reallocate@AEBUEventScheduleDate@mu2@@@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAPEAUEventScheduleDate@mu2@@QEAU23@AEBU23@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???$_Emplace_reallocate@AEBUEventScheduleDate@mu2@@@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAPEAUEventScheduleDate@mu2@@QEAU23@AEBU23@@Z@4HA DD imagerel ?dtor$0@?0???$_Emplace_reallocate@AEBUEventScheduleDate@mu2@@@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAPEAUEventScheduleDate@mu2@@QEAU23@AEBU23@@Z@4HA
	DD	imagerel ?dtor$0@?0???$_Emplace_reallocate@AEBUEventScheduleDate@mu2@@@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAPEAUEventScheduleDate@mu2@@QEAU23@AEBU23@@Z@4HA+27
	DD	imagerel $unwind$?dtor$0@?0???$_Emplace_reallocate@AEBUEventScheduleDate@mu2@@@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAPEAUEventScheduleDate@mu2@@QEAU23@AEBU23@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Pop_heap_hole_by_index@PEAUEventScheduleDate@mu2@@U12@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@_J1$$QEAU12@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z DD imagerel ??$_Pop_heap_hole_by_index@PEAUEventScheduleDate@mu2@@U12@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@_J1$$QEAU12@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z
	DD	imagerel ??$_Pop_heap_hole_by_index@PEAUEventScheduleDate@mu2@@U12@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@_J1$$QEAU12@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z+707
	DD	imagerel $unwind$??$_Pop_heap_hole_by_index@PEAUEventScheduleDate@mu2@@U12@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@_J1$$QEAU12@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Pop_heap_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z DD imagerel ??$_Pop_heap_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z
	DD	imagerel ??$_Pop_heap_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z+302
	DD	imagerel $unwind$??$_Pop_heap_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Guess_median_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@00V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z DD imagerel ??$_Guess_median_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@00V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z
	DD	imagerel ??$_Guess_median_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@00V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z+371
	DD	imagerel $unwind$??$_Guess_median_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@00V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1_Reallocation_guard@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@QEAA@XZ DD imagerel $LN25
	DD	imagerel $LN25+179
	DD	imagerel $unwind$??1_Reallocation_guard@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Uninitialized_move@PEAUEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@YAPEAUEventScheduleDate@mu2@@QEAU12@0PEAU12@AEAV?$allocator@UEventScheduleDate@mu2@@@0@@Z DD imagerel $LN65
	DD	imagerel $LN65+471
	DD	imagerel $unwind$??$_Uninitialized_move@PEAUEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@YAPEAUEventScheduleDate@mu2@@QEAU12@0PEAU12@AEAV?$allocator@UEventScheduleDate@mu2@@@0@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Copy_backward_memmove@PEAUEventScheduleDate@mu2@@PEAU12@@std@@YAPEAUEventScheduleDate@mu2@@PEAU12@00@Z DD imagerel $LN18
	DD	imagerel $LN18+194
	DD	imagerel $unwind$??$_Copy_backward_memmove@PEAUEventScheduleDate@mu2@@PEAU12@@std@@YAPEAUEventScheduleDate@mu2@@PEAU12@00@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Med3_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@00V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z DD imagerel ??$_Med3_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@00V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z
	DD	imagerel ??$_Med3_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@00V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z+564
	DD	imagerel $unwind$??$_Med3_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@00V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Copy_memmove@PEAUEventScheduleDate@mu2@@PEAU12@@std@@YAPEAUEventScheduleDate@mu2@@PEAU12@00@Z DD imagerel $LN18
	DD	imagerel $LN18+183
	DD	imagerel $unwind$??$_Copy_memmove@PEAUEventScheduleDate@mu2@@PEAU12@@std@@YAPEAUEventScheduleDate@mu2@@PEAU12@00@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Copy_memmove_tail@PEAUEventScheduleDate@mu2@@@std@@YAPEAUEventScheduleDate@mu2@@QEBDQEAU12@_K2@Z DD imagerel $LN8
	DD	imagerel $LN8+96
	DD	imagerel $unwind$??$_Copy_memmove_tail@PEAUEventScheduleDate@mu2@@@std@@YAPEAUEventScheduleDate@mu2@@QEBDQEAU12@_K2@Z
pdata	ENDS
;	COMDAT ??_R1A@?0A@EA@EventSchedule@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@EventSchedule@mu2@@8 DD imagerel ??_R0?AVEventSchedule@mu2@@@8 ; mu2::EventSchedule::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3EventSchedule@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2EventSchedule@mu2@@8
rdata$r	SEGMENT
??_R2EventSchedule@mu2@@8 DD imagerel ??_R1A@?0A@EA@EventSchedule@mu2@@8 ; mu2::EventSchedule::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3EventSchedule@mu2@@8
rdata$r	SEGMENT
??_R3EventSchedule@mu2@@8 DD 00H			; mu2::EventSchedule::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2EventSchedule@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVEventSchedule@mu2@@@8
data$rs	SEGMENT
??_R0?AVEventSchedule@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::EventSchedule `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVEventSchedule@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4EventSchedule@mu2@@6B@
rdata$r	SEGMENT
??_R4EventSchedule@mu2@@6B@ DD 01H			; mu2::EventSchedule::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVEventSchedule@mu2@@@8
	DD	imagerel ??_R3EventSchedule@mu2@@8
	DD	imagerel ??_R4EventSchedule@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_C@_0CE@PHJOKCDC@mu2?3?3EventSchedule?3?3refreshNext@
CONST	SEGMENT
??_C@_0CE@PHJOKCDC@mu2?3?3EventSchedule?3?3refreshNext@ DB 'mu2::EventSch'
	DB	'edule::refreshNextWeek', 00H		; `string'
CONST	ENDS
;	COMDAT ??_C@_0EM@EAHOIPCK@F?3?2Release_Branch?2Server?2Develo@
CONST	SEGMENT
??_C@_0EM@EAHOIPCK@F?3?2Release_Branch?2Server?2Develo@ DB 'F:\Release_Br'
	DB	'anch\Server\Development\Frontend\WorldServer\EventSchedule.cp'
	DB	'p', 00H					; `string'
CONST	ENDS
;	COMDAT ??_C@_0GN@NJKGFILB@World?5EventSchedule?5ready?3?$CFd?9?$CF0@
CONST	SEGMENT
??_C@_0GN@NJKGFILB@World?5EventSchedule?5ready?3?$CFd?9?$CF0@ DB 'World E'
	DB	'ventSchedule ready:%d-%02d-%02d %02d:%02d open::%d-%02d-%02d '
	DB	'%02d:%02d  close::%d-%02d-%02d %02d:%02d', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7EventSchedule@mu2@@6B@
CONST	SEGMENT
??_7EventSchedule@mu2@@6B@ DQ FLAT:??_R4EventSchedule@mu2@@6B@ ; mu2::EventSchedule::`vftable'
	DQ	FLAT:??_EEventSchedule@mu2@@UEAAPEAXI@Z
	DQ	FLAT:?getReadyToStartTime@EventSchedule@mu2@@MEAAHXZ
	DQ	FLAT:_purecall
	DQ	FLAT:_purecall
	DQ	FLAT:_purecall
	DQ	FLAT:_purecall
	DQ	FLAT:_purecall
	DQ	FLAT:?OnScheduleEvent@EventSchedule@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
CONST	ENDS
;	COMDAT ??_R4bad_alloc@std@@6B@
rdata$r	SEGMENT
??_R4bad_alloc@std@@6B@ DD 01H				; std::bad_alloc::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVbad_alloc@std@@@8
	DD	imagerel ??_R3bad_alloc@std@@8
	DD	imagerel ??_R4bad_alloc@std@@6B@
rdata$r	ENDS
;	COMDAT ??_R2bad_alloc@std@@8
rdata$r	SEGMENT
??_R2bad_alloc@std@@8 DD imagerel ??_R1A@?0A@EA@bad_alloc@std@@8 ; std::bad_alloc::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@exception@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3bad_alloc@std@@8
rdata$r	SEGMENT
??_R3bad_alloc@std@@8 DD 00H				; std::bad_alloc::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2bad_alloc@std@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@bad_alloc@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@bad_alloc@std@@8 DD imagerel ??_R0?AVbad_alloc@std@@@8 ; std::bad_alloc::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3bad_alloc@std@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@bad_array_new_length@std@@8 DD imagerel ??_R0?AVbad_array_new_length@std@@@8 ; std::bad_array_new_length::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	02H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3bad_array_new_length@std@@8
rdata$r	ENDS
;	COMDAT ??_R2bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R2bad_array_new_length@std@@8 DD imagerel ??_R1A@?0A@EA@bad_array_new_length@std@@8 ; std::bad_array_new_length::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@bad_alloc@std@@8
	DD	imagerel ??_R1A@?0A@EA@exception@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R3bad_array_new_length@std@@8 DD 00H			; std::bad_array_new_length::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	03H
	DD	imagerel ??_R2bad_array_new_length@std@@8
rdata$r	ENDS
;	COMDAT ??_R4bad_array_new_length@std@@6B@
rdata$r	SEGMENT
??_R4bad_array_new_length@std@@6B@ DD 01H		; std::bad_array_new_length::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVbad_array_new_length@std@@@8
	DD	imagerel ??_R3bad_array_new_length@std@@8
	DD	imagerel ??_R4bad_array_new_length@std@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@exception@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@exception@std@@8 DD imagerel ??_R0?AVexception@std@@@8 ; std::exception::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3exception@std@@8
rdata$r	ENDS
;	COMDAT ??_R2exception@std@@8
rdata$r	SEGMENT
??_R2exception@std@@8 DD imagerel ??_R1A@?0A@EA@exception@std@@8 ; std::exception::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3exception@std@@8
rdata$r	SEGMENT
??_R3exception@std@@8 DD 00H				; std::exception::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2exception@std@@8
rdata$r	ENDS
;	COMDAT ??_R4exception@std@@6B@
rdata$r	SEGMENT
??_R4exception@std@@6B@ DD 01H				; std::exception::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVexception@std@@@8
	DD	imagerel ??_R3exception@std@@8
	DD	imagerel ??_R4exception@std@@6B@
rdata$r	ENDS
;	COMDAT ??_C@_0BA@FOIKENOD@vector?5too?5long@
CONST	SEGMENT
??_C@_0BA@FOIKENOD@vector?5too?5long@ DB 'vector too long', 00H ; `string'
CONST	ENDS
;	COMDAT ??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
CONST	SEGMENT
??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@ DB 'g', 00H, 'a', 00H, 'm', 00H, 'e'
	DB	00H, 00H, 00H				; `string'
CONST	ENDS
;	COMDAT _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 DD 010H
	DD	imagerel ??_R0?AVbad_alloc@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0bad_alloc@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVbad_alloc@std@@@8
data$r	SEGMENT
??_R0?AVbad_alloc@std@@@8 DQ FLAT:??_7type_info@@6B@	; std::bad_alloc `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVbad_alloc@std@@', 00H
data$r	ENDS
;	COMDAT _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 DD 00H
	DD	imagerel ??_R0?AVbad_array_new_length@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0bad_array_new_length@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVbad_array_new_length@std@@@8
data$r	SEGMENT
??_R0?AVbad_array_new_length@std@@@8 DQ FLAT:??_7type_info@@6B@ ; std::bad_array_new_length `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVbad_array_new_length@std@@', 00H
data$r	ENDS
;	COMDAT _CTA3?AVbad_array_new_length@std@@
xdata$x	SEGMENT
_CTA3?AVbad_array_new_length@std@@ DD 03H
	DD	imagerel _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
	DD	imagerel _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
	DD	imagerel _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
xdata$x	ENDS
;	COMDAT _TI3?AVbad_array_new_length@std@@
xdata$x	SEGMENT
_TI3?AVbad_array_new_length@std@@ DD 00H
	DD	imagerel ??1bad_array_new_length@std@@UEAA@XZ
	DD	00H
	DD	imagerel _CTA3?AVbad_array_new_length@std@@
xdata$x	ENDS
;	COMDAT _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 DD 00H
	DD	imagerel ??_R0?AVexception@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0exception@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVexception@std@@@8
data$r	SEGMENT
??_R0?AVexception@std@@@8 DQ FLAT:??_7type_info@@6B@	; std::exception `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVexception@std@@', 00H
data$r	ENDS
;	COMDAT ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@
CONST	SEGMENT
??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ DB 'bad array new length', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7bad_array_new_length@std@@6B@
CONST	SEGMENT
??_7bad_array_new_length@std@@6B@ DQ FLAT:??_R4bad_array_new_length@std@@6B@ ; std::bad_array_new_length::`vftable'
	DQ	FLAT:??_Ebad_array_new_length@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT ??_7bad_alloc@std@@6B@
CONST	SEGMENT
??_7bad_alloc@std@@6B@ DQ FLAT:??_R4bad_alloc@std@@6B@	; std::bad_alloc::`vftable'
	DQ	FLAT:??_Ebad_alloc@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT ??_C@_0BC@EOODALEL@Unknown?5exception@
CONST	SEGMENT
??_C@_0BC@EOODALEL@Unknown?5exception@ DB 'Unknown exception', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7exception@std@@6B@
CONST	SEGMENT
??_7exception@std@@6B@ DQ FLAT:??_R4exception@std@@6B@	; std::exception::`vftable'
	DQ	FLAT:??_Eexception@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Copy_memmove_tail@PEAUEventScheduleDate@mu2@@@std@@YAPEAUEventScheduleDate@mu2@@QEBDQEAU12@_K2@Z DD 011801H
	DD	08218H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Copy_memmove@PEAUEventScheduleDate@mu2@@PEAU12@@std@@YAPEAUEventScheduleDate@mu2@@PEAU12@00@Z DD 011301H
	DD	0e213H
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DW	027H
	DW	021aH
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Med3_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@00V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z DD 042f19H
	DD	051011dH
	DD	060157016H
	DD	imagerel __GSHandlerCheck
	DD	0270H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Copy_backward_memmove@PEAUEventScheduleDate@mu2@@PEAU12@@std@@YAPEAUEventScheduleDate@mu2@@PEAU12@00@Z DD 021601H
	DD	0110116H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Uninitialized_move@PEAUEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@YAPEAUEventScheduleDate@mu2@@QEAU12@0PEAU12@AEAV?$allocator@UEventScheduleDate@mu2@@@0@@Z DD 041d01H
	DD	01b011dH
	DD	060157016H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??1_Reallocation_guard@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@QEAA@XZ DB 06H
	DB	00H
	DB	00H
	DB	0dH, 02H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??1_Reallocation_guard@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@QEAA@XZ DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??1_Reallocation_guard@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@QEAA@XZ DB 068H
	DD	imagerel $stateUnwindMap$??1_Reallocation_guard@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@QEAA@XZ
	DD	imagerel $ip2state$??1_Reallocation_guard@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1_Reallocation_guard@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@QEAA@XZ DD 010919H
	DD	0c209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??1_Reallocation_guard@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Guess_median_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@00V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z DD 011801H
	DD	08218H
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DW	022H
	DW	0114H
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Pop_heap_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z DD 042a19H
	DD	0250118H
	DD	060107011H
	DD	imagerel __GSHandlerCheck
	DD	0110H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Pop_heap_hole_by_index@PEAUEventScheduleDate@mu2@@U12@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@_J1$$QEAU12@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z DD 031a01H
	DD	07016e21aH
	DD	06015H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???$_Emplace_reallocate@AEBUEventScheduleDate@mu2@@@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAPEAUEventScheduleDate@mu2@@QEAU23@AEBU23@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$_Emplace_reallocate@AEBUEventScheduleDate@mu2@@@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAPEAUEventScheduleDate@mu2@@QEAU23@AEBU23@@Z DB 06H
	DB	00H
	DB	00H
	DB	0b1H, 08H
	DB	02H
	DB	05H, 06H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$_Emplace_reallocate@AEBUEventScheduleDate@mu2@@@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAPEAUEventScheduleDate@mu2@@QEAU23@AEBU23@@Z DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???$_Emplace_reallocate@AEBUEventScheduleDate@mu2@@@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAPEAUEventScheduleDate@mu2@@QEAU23@AEBU23@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$_Emplace_reallocate@AEBUEventScheduleDate@mu2@@@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAPEAUEventScheduleDate@mu2@@QEAU23@AEBU23@@Z DB 028H
	DD	imagerel $stateUnwindMap$??$_Emplace_reallocate@AEBUEventScheduleDate@mu2@@@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAPEAUEventScheduleDate@mu2@@QEAU23@AEBU23@@Z
	DD	imagerel $ip2state$??$_Emplace_reallocate@AEBUEventScheduleDate@mu2@@@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAPEAUEventScheduleDate@mu2@@QEAU23@AEBU23@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Emplace_reallocate@AEBUEventScheduleDate@mu2@@@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAPEAUEventScheduleDate@mu2@@QEAU23@AEBU23@@Z DD 041811H
	DD	0290118H
	DD	060107011H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$_Emplace_reallocate@AEBUEventScheduleDate@mu2@@@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAPEAUEventScheduleDate@mu2@@QEAU23@AEBU23@@Z
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DW	027H
	DW	0975H
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Partition_by_median_guess_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YA?AU?$pair@PEAUEventScheduleDate@mu2@@PEAU12@@0@PEAUEventScheduleDate@mu2@@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z DD 042f19H
	DD	0d5011dH
	DD	060157016H
	DD	imagerel __GSHandlerCheck
	DD	0690H
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DB	022H
	DB	0d9H
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Make_heap_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z DD 042a19H
	DD	0230118H
	DD	060107011H
	DD	imagerel __GSHandlerCheck
	DD	0100H
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DW	022H
	DW	0232H
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Insertion_sort_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAPEAUEventScheduleDate@mu2@@QEAU12@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z DD 042a19H
	DD	0290118H
	DD	060107011H
	DD	imagerel __GSHandlerCheck
	DD	0130H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Emplace_one_at_back@AEBUEventScheduleDate@mu2@@@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAAEAUEventScheduleDate@mu2@@AEBU23@@Z DD 041301H
	DD	0130113H
	DD	0600b700cH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Sort_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@0_JV<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z DD 011801H
	DD	08218H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$sort@V?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@UEventScheduleDate@mu2@@@std@@@std@@@std@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXV?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@UEventScheduleDate@mu2@@@std@@@std@@@0@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z DD 021601H
	DD	0110116H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GEventSchedule@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Xlength@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@CAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?_Tidy@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAXXZ DB 06H
	DB	00H
	DB	00H
	DB	'M', 03H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?_Tidy@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAXXZ DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?_Tidy@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAXXZ DB 068H
	DD	imagerel $stateUnwindMap$?_Tidy@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAXXZ
	DD	imagerel $ip2state$?_Tidy@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Tidy@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAXXZ DD 020c19H
	DD	013010cH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?_Tidy@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?_Change_array@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAXQEAUEventScheduleDate@mu2@@_K1@Z DB 06H
	DB	00H
	DB	00H
	DB	089H, 03H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?_Change_array@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAXQEAUEventScheduleDate@mu2@@_K1@Z DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?_Change_array@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAXQEAUEventScheduleDate@mu2@@_K1@Z DB 068H
	DD	imagerel $stateUnwindMap$?_Change_array@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAXQEAUEventScheduleDate@mu2@@_K1@Z
	DD	imagerel $ip2state$?_Change_array@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAXQEAUEventScheduleDate@mu2@@_K1@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Change_array@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAXQEAUEventScheduleDate@mu2@@_K1@Z DD 021b19H
	DD	013011bH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?_Change_array@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAXQEAUEventScheduleDate@mu2@@_K1@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Calculate_growth@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEBA_K_K@Z DD 021101H
	DD	0110111H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?allocate@?$allocator@UEventScheduleDate@mu2@@@std@@QEAAPEAUEventScheduleDate@mu2@@_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DB	016H
	DB	0a0H
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?getWeekStartTime@EventSchedule@mu2@@IEAA_JXZ DD 021b19H
	DD	011010cH
	DD	imagerel __GSHandlerCheck
	DD	078H
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DB	013H
	DB	043H
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?GetDayofWeek@EventSchedule@mu2@@IEAAEXZ DD 011819H
	DD	0c209H
	DD	imagerel __GSHandlerCheck
	DD	058H
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DB	013H
	DB	052H
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?isWeekendToday@EventSchedule@mu2@@IEAA_NXZ DD 011819H
	DD	0c209H
	DD	imagerel __GSHandlerCheck
	DD	058H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?processOpenState@EventSchedule@mu2@@AEAAXAEB_J@Z DD 010e01H
	DD	0620eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?processReadyState@EventSchedule@mu2@@AEAAXAEB_J@Z DD 010e01H
	DD	0620eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?processClosedState@EventSchedule@mu2@@AEAAXAEB_J@Z DD 010e01H
	DD	0620eH
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DW	018H
	DW	06a5H
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?refreshNextWeek@EventSchedule@mu2@@AEAAXXZ DD 042019H
	DD	049010eH
	DD	060067007H
	DD	imagerel __GSHandlerCheck
	DD	0238H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Update@EventSchedule@mu2@@QEAAXXZ DD 010901H
	DD	0a209H
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DW	016H
	DW	03e7H
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?InitializeEventSchedule@EventSchedule@mu2@@QEAAXXZ DD 021e19H
	DD	035010cH
	DD	imagerel __GSHandlerCheck
	DD	0190H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1EventSchedule@mu2@@UEAA@XZ DD 010901H
	DD	0c209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0EventSchedule@mu2@@QEAA@XZ DD 020a01H
	DD	07006720aH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z DD 010901H
	DD	0a209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?_Tidy@?$vector@UScheduleCycle@mu2@@V?$allocator@UScheduleCycle@mu2@@@std@@@std@@AEAAXXZ DB 06H
	DB	00H
	DB	00H
	DB	'A', 03H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?_Tidy@?$vector@UScheduleCycle@mu2@@V?$allocator@UScheduleCycle@mu2@@@std@@@std@@AEAAXXZ DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?_Tidy@?$vector@UScheduleCycle@mu2@@V?$allocator@UScheduleCycle@mu2@@@std@@@std@@AEAAXXZ DB 068H
	DD	imagerel $stateUnwindMap$?_Tidy@?$vector@UScheduleCycle@mu2@@V?$allocator@UScheduleCycle@mu2@@@std@@@std@@AEAAXXZ
	DD	imagerel $ip2state$?_Tidy@?$vector@UScheduleCycle@mu2@@V?$allocator@UScheduleCycle@mu2@@@std@@@std@@AEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Tidy@?$vector@UScheduleCycle@mu2@@V?$allocator@UScheduleCycle@mu2@@@std@@@std@@AEAAXXZ DD 020c19H
	DD	013010cH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?_Tidy@?$vector@UScheduleCycle@mu2@@V?$allocator@UScheduleCycle@mu2@@@std@@@std@@AEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Throw_bad_array_new_length@std@@YAXXZ DD 010401H
	DD	08204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1bad_array_new_length@std@@UEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_array_new_length@std@@QEAA@XZ DD 010601H
	DD	07006H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z DD 020f01H
	DD	0700b320fH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gexception@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?what@exception@std@@UEBAPEBDXZ DD 010901H
	DD	02209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0exception@std@@QEAA@AEBV01@@Z DD 020f01H
	DD	0700b320fH
xdata	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
;	COMDAT ??$_Copy_memmove_tail@PEAUEventScheduleDate@mu2@@@std@@YAPEAUEventScheduleDate@mu2@@QEBDQEAU12@_K2@Z
_TEXT	SEGMENT
_Dest_ch$ = 32
$T1 = 40
_Dest_ptr$ = 48
_First_ch$ = 80
_Dest$ = 88
_Byte_count$ = 96
_Object_count$ = 104
??$_Copy_memmove_tail@PEAUEventScheduleDate@mu2@@@std@@YAPEAUEventScheduleDate@mu2@@QEBDQEAU12@_K2@Z PROC ; std::_Copy_memmove_tail<mu2::EventScheduleDate *>, COMDAT

; 4747 :     const char* const _First_ch, const _OutCtgIt _Dest, const size_t _Byte_count, const size_t _Object_count) {

$LN8:
  00000	4c 89 4c 24 20	 mov	 QWORD PTR [rsp+32], r9
  00005	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 4627 :     return _Val;

  00018	48 8b 44 24 58	 mov	 rax, QWORD PTR _Dest$[rsp]
  0001d	48 89 44 24 28	 mov	 QWORD PTR $T1[rsp], rax

; 4748 :     _STL_INTERNAL_CHECK(_Byte_count == _Object_count * sizeof(*_Dest));
; 4749 :     const auto _Dest_ptr = _STD _To_address(_Dest);

  00022	48 8b 44 24 28	 mov	 rax, QWORD PTR $T1[rsp]
  00027	48 89 44 24 30	 mov	 QWORD PTR _Dest_ptr$[rsp], rax

; 4750 :     const auto _Dest_ch  = const_cast<char*>(reinterpret_cast<const volatile char*>(_Dest_ptr));

  0002c	48 8b 44 24 30	 mov	 rax, QWORD PTR _Dest_ptr$[rsp]
  00031	48 89 44 24 20	 mov	 QWORD PTR _Dest_ch$[rsp], rax

; 4751 :     _CSTD memmove(_Dest_ch, _First_ch, _Byte_count);

  00036	4c 8b 44 24 60	 mov	 r8, QWORD PTR _Byte_count$[rsp]
  0003b	48 8b 54 24 50	 mov	 rdx, QWORD PTR _First_ch$[rsp]
  00040	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Dest_ch$[rsp]
  00045	e8 00 00 00 00	 call	 memmove
  0004a	90		 npad	 1

; 4752 :     if constexpr (is_pointer_v<_OutCtgIt>) {
; 4753 :         (void) _Object_count;
; 4754 :         // CodeQL [SM02986] This cast is correct: we're bypassing pointer arithmetic for performance.
; 4755 :         return reinterpret_cast<_OutCtgIt>(_Dest_ch + _Byte_count);

  0004b	48 8b 44 24 60	 mov	 rax, QWORD PTR _Byte_count$[rsp]
  00050	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Dest_ch$[rsp]
  00055	48 03 c8	 add	 rcx, rax
  00058	48 8b c1	 mov	 rax, rcx

; 4756 :     } else {
; 4757 :         return _Dest + static_cast<_Iter_diff_t<_OutCtgIt>>(_Object_count);
; 4758 :     }
; 4759 : }

  0005b	48 83 c4 48	 add	 rsp, 72			; 00000048H
  0005f	c3		 ret	 0
??$_Copy_memmove_tail@PEAUEventScheduleDate@mu2@@@std@@YAPEAUEventScheduleDate@mu2@@QEBDQEAU12@_K2@Z ENDP ; std::_Copy_memmove_tail<mu2::EventScheduleDate *>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
;	COMDAT ??$_Copy_memmove@PEAUEventScheduleDate@mu2@@PEAU12@@std@@YAPEAUEventScheduleDate@mu2@@PEAU12@00@Z
_TEXT	SEGMENT
_First_ptr$ = 32
_Last_ptr$ = 40
_First_ch$ = 48
$T1 = 56
$T2 = 64
_Last_ch$ = 72
_Object_count$ = 80
_Byte_count$ = 88
$T3 = 96
_First$ = 128
_Last$ = 136
_Dest$ = 144
??$_Copy_memmove@PEAUEventScheduleDate@mu2@@PEAU12@@std@@YAPEAUEventScheduleDate@mu2@@PEAU12@00@Z PROC ; std::_Copy_memmove<mu2::EventScheduleDate *,mu2::EventScheduleDate *>, COMDAT

; 4762 : _OutCtgIt _Copy_memmove(_CtgIt _First, _CtgIt _Last, _OutCtgIt _Dest) {

$LN18:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 78	 sub	 rsp, 120		; 00000078H

; 4627 :     return _Val;

  00013	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _First$[rsp]
  0001b	48 89 44 24 38	 mov	 QWORD PTR $T1[rsp], rax

; 4763 :     _STL_INTERNAL_CHECK(_First <= _Last);
; 4764 :     const auto _First_ptr    = _STD _To_address(_First);

  00020	48 8b 44 24 38	 mov	 rax, QWORD PTR $T1[rsp]
  00025	48 89 44 24 20	 mov	 QWORD PTR _First_ptr$[rsp], rax

; 4627 :     return _Val;

  0002a	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR _Last$[rsp]
  00032	48 89 44 24 40	 mov	 QWORD PTR $T2[rsp], rax

; 4765 :     const auto _Last_ptr     = _STD _To_address(_Last);

  00037	48 8b 44 24 40	 mov	 rax, QWORD PTR $T2[rsp]
  0003c	48 89 44 24 28	 mov	 QWORD PTR _Last_ptr$[rsp], rax

; 4766 :     const auto _Object_count = static_cast<size_t>(_Last_ptr - _First_ptr);

  00041	48 8b 44 24 20	 mov	 rax, QWORD PTR _First_ptr$[rsp]
  00046	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Last_ptr$[rsp]
  0004b	48 2b c8	 sub	 rcx, rax
  0004e	48 8b c1	 mov	 rax, rcx
  00051	48 99		 cdq
  00053	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  00058	48 f7 f9	 idiv	 rcx
  0005b	48 89 44 24 50	 mov	 QWORD PTR _Object_count$[rsp], rax

; 4767 :     const auto _First_ch     = const_cast<const char*>(reinterpret_cast<const volatile char*>(_First_ptr));

  00060	48 8b 44 24 20	 mov	 rax, QWORD PTR _First_ptr$[rsp]
  00065	48 89 44 24 30	 mov	 QWORD PTR _First_ch$[rsp], rax

; 4768 :     const auto _Last_ch      = const_cast<const char*>(reinterpret_cast<const volatile char*>(_Last_ptr));

  0006a	48 8b 44 24 28	 mov	 rax, QWORD PTR _Last_ptr$[rsp]
  0006f	48 89 44 24 48	 mov	 QWORD PTR _Last_ch$[rsp], rax

; 4769 :     const auto _Byte_count   = static_cast<size_t>(_Last_ch - _First_ch);

  00074	48 8b 44 24 30	 mov	 rax, QWORD PTR _First_ch$[rsp]
  00079	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _Last_ch$[rsp]
  0007e	48 2b c8	 sub	 rcx, rax
  00081	48 8b c1	 mov	 rax, rcx
  00084	48 89 44 24 58	 mov	 QWORD PTR _Byte_count$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00089	48 8d 84 24 90
	00 00 00	 lea	 rax, QWORD PTR _Dest$[rsp]
  00091	48 89 44 24 60	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 4770 :     return _STD _Copy_memmove_tail(_First_ch, _STD move(_Dest), _Byte_count, _Object_count);

  00096	4c 8b 4c 24 50	 mov	 r9, QWORD PTR _Object_count$[rsp]
  0009b	4c 8b 44 24 58	 mov	 r8, QWORD PTR _Byte_count$[rsp]
  000a0	48 8b 44 24 60	 mov	 rax, QWORD PTR $T3[rsp]
  000a5	48 8b 10	 mov	 rdx, QWORD PTR [rax]
  000a8	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _First_ch$[rsp]
  000ad	e8 00 00 00 00	 call	 ??$_Copy_memmove_tail@PEAUEventScheduleDate@mu2@@@std@@YAPEAUEventScheduleDate@mu2@@QEBDQEAU12@_K2@Z ; std::_Copy_memmove_tail<mu2::EventScheduleDate *>

; 4771 : }

  000b2	48 83 c4 78	 add	 rsp, 120		; 00000078H
  000b6	c3		 ret	 0
??$_Copy_memmove@PEAUEventScheduleDate@mu2@@PEAU12@@std@@YAPEAUEventScheduleDate@mu2@@PEAU12@00@Z ENDP ; std::_Copy_memmove<mu2::EventScheduleDate *,mu2::EventScheduleDate *>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
;	COMDAT ??$_Med3_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@00V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z
_TEXT	SEGMENT
$T1 = 0
$T2 = 1
$T3 = 2
tv91 = 4
tv94 = 8
tv132 = 12
$T4 = 16
$T5 = 24
$T6 = 32
$T7 = 40
$T8 = 48
$T9 = 56
$T10 = 64
$T11 = 72
$T12 = 80
_Tmp$13 = 96
_Tmp$14 = 272
_Tmp$15 = 448
__$ArrayPad$ = 624
_First$ = 672
_Mid$ = 680
_Last$ = 688
_Pred$ = 696
??$_Med3_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@00V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z PROC ; std::_Med3_unchecked<mu2::EventScheduleDate *,<lambda_5acf0176913ce2eb7dca573a416bd931> >, COMDAT

; 8265 : _CONSTEXPR20 void _Med3_unchecked(_RanIt _First, _RanIt _Mid, _RanIt _Last, _Pr _Pred) {

  00000	44 88 4c 24 20	 mov	 BYTE PTR [rsp+32], r9b
  00005	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	56		 push	 rsi
  00015	57		 push	 rdi
  00016	48 81 ec 88 02
	00 00		 sub	 rsp, 648		; 00000288H
  0001d	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR __security_cookie
  00024	48 33 c4	 xor	 rax, rsp
  00027	48 89 84 24 70
	02 00 00	 mov	 QWORD PTR __$ArrayPad$[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 229  : 			return lhs.readyTime < rhs.readyTime;

  0002f	48 8b 84 24 a8
	02 00 00	 mov	 rax, QWORD PTR _Mid$[rsp]
  00037	48 8b 8c 24 a0
	02 00 00	 mov	 rcx, QWORD PTR _First$[rsp]
  0003f	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00042	48 39 08	 cmp	 QWORD PTR [rax], rcx
  00045	7d 0a		 jge	 SHORT $LN8@Med3_unche
  00047	c7 44 24 04 01
	00 00 00	 mov	 DWORD PTR tv91[rsp], 1
  0004f	eb 08		 jmp	 SHORT $LN9@Med3_unche
$LN8@Med3_unche:
  00051	c7 44 24 04 00
	00 00 00	 mov	 DWORD PTR tv91[rsp], 0
$LN9@Med3_unche:
  00059	0f b6 44 24 04	 movzx	 eax, BYTE PTR tv91[rsp]
  0005e	88 04 24	 mov	 BYTE PTR $T1[rsp], al
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm

; 8267 :     if (_DEBUG_LT_PRED(_Pred, *_Mid, *_First)) {

  00061	0f b6 04 24	 movzx	 eax, BYTE PTR $T1[rsp]
  00065	0f b6 c0	 movzx	 eax, al
  00068	85 c0		 test	 eax, eax
  0006a	74 60		 je	 SHORT $LN2@Med3_unche
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  0006c	48 8b 84 24 a8
	02 00 00	 mov	 rax, QWORD PTR _Mid$[rsp]
  00074	48 89 44 24 10	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 139  :     _Ty _Tmp = _STD move(_Left);

  00079	48 8d 44 24 60	 lea	 rax, QWORD PTR _Tmp$13[rsp]
  0007e	48 8b f8	 mov	 rdi, rax
  00081	48 8b 74 24 10	 mov	 rsi, QWORD PTR $T4[rsp]
  00086	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  0008b	f3 a4		 rep movsb
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  0008d	48 8b 84 24 a0
	02 00 00	 mov	 rax, QWORD PTR _First$[rsp]
  00095	48 89 44 24 18	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 140  :     _Left    = _STD move(_Right);

  0009a	48 8b bc 24 a8
	02 00 00	 mov	 rdi, QWORD PTR _Mid$[rsp]
  000a2	48 8b 74 24 18	 mov	 rsi, QWORD PTR $T5[rsp]
  000a7	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  000ac	f3 a4		 rep movsb
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  000ae	48 8d 44 24 60	 lea	 rax, QWORD PTR _Tmp$13[rsp]
  000b3	48 89 44 24 20	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 141  :     _Right   = _STD move(_Tmp);

  000b8	48 8b bc 24 a0
	02 00 00	 mov	 rdi, QWORD PTR _First$[rsp]
  000c0	48 8b 74 24 20	 mov	 rsi, QWORD PTR $T6[rsp]
  000c5	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  000ca	f3 a4		 rep movsb
$LN2@Med3_unche:
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 229  : 			return lhs.readyTime < rhs.readyTime;

  000cc	48 8b 84 24 b0
	02 00 00	 mov	 rax, QWORD PTR _Last$[rsp]
  000d4	48 8b 8c 24 a8
	02 00 00	 mov	 rcx, QWORD PTR _Mid$[rsp]
  000dc	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  000df	48 39 08	 cmp	 QWORD PTR [rax], rcx
  000e2	7d 0a		 jge	 SHORT $LN32@Med3_unche
  000e4	c7 44 24 08 01
	00 00 00	 mov	 DWORD PTR tv94[rsp], 1
  000ec	eb 08		 jmp	 SHORT $LN33@Med3_unche
$LN32@Med3_unche:
  000ee	c7 44 24 08 00
	00 00 00	 mov	 DWORD PTR tv94[rsp], 0
$LN33@Med3_unche:
  000f6	0f b6 44 24 08	 movzx	 eax, BYTE PTR tv94[rsp]
  000fb	88 44 24 01	 mov	 BYTE PTR $T2[rsp], al
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm

; 8271 :     if (_DEBUG_LT_PRED(_Pred, *_Last, *_Mid)) { // swap middle and last, then test first again

  000ff	0f b6 44 24 01	 movzx	 eax, BYTE PTR $T2[rsp]
  00104	0f b6 c0	 movzx	 eax, al
  00107	85 c0		 test	 eax, eax
  00109	0f 84 0b 01 00
	00		 je	 $LN3@Med3_unche
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  0010f	48 8b 84 24 b0
	02 00 00	 mov	 rax, QWORD PTR _Last$[rsp]
  00117	48 89 44 24 28	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 139  :     _Ty _Tmp = _STD move(_Left);

  0011c	48 8d 84 24 10
	01 00 00	 lea	 rax, QWORD PTR _Tmp$14[rsp]
  00124	48 8b f8	 mov	 rdi, rax
  00127	48 8b 74 24 28	 mov	 rsi, QWORD PTR $T7[rsp]
  0012c	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  00131	f3 a4		 rep movsb
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00133	48 8b 84 24 a8
	02 00 00	 mov	 rax, QWORD PTR _Mid$[rsp]
  0013b	48 89 44 24 30	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 140  :     _Left    = _STD move(_Right);

  00140	48 8b bc 24 b0
	02 00 00	 mov	 rdi, QWORD PTR _Last$[rsp]
  00148	48 8b 74 24 30	 mov	 rsi, QWORD PTR $T8[rsp]
  0014d	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  00152	f3 a4		 rep movsb
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00154	48 8d 84 24 10
	01 00 00	 lea	 rax, QWORD PTR _Tmp$14[rsp]
  0015c	48 89 44 24 38	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 141  :     _Right   = _STD move(_Tmp);

  00161	48 8b bc 24 a8
	02 00 00	 mov	 rdi, QWORD PTR _Mid$[rsp]
  00169	48 8b 74 24 38	 mov	 rsi, QWORD PTR $T9[rsp]
  0016e	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  00173	f3 a4		 rep movsb
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 229  : 			return lhs.readyTime < rhs.readyTime;

  00175	48 8b 84 24 a8
	02 00 00	 mov	 rax, QWORD PTR _Mid$[rsp]
  0017d	48 8b 8c 24 a0
	02 00 00	 mov	 rcx, QWORD PTR _First$[rsp]
  00185	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00188	48 39 08	 cmp	 QWORD PTR [rax], rcx
  0018b	7d 0a		 jge	 SHORT $LN56@Med3_unche
  0018d	c7 44 24 0c 01
	00 00 00	 mov	 DWORD PTR tv132[rsp], 1
  00195	eb 08		 jmp	 SHORT $LN57@Med3_unche
$LN56@Med3_unche:
  00197	c7 44 24 0c 00
	00 00 00	 mov	 DWORD PTR tv132[rsp], 0
$LN57@Med3_unche:
  0019f	0f b6 44 24 0c	 movzx	 eax, BYTE PTR tv132[rsp]
  001a4	88 44 24 02	 mov	 BYTE PTR $T3[rsp], al
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm

; 8274 :         if (_DEBUG_LT_PRED(_Pred, *_Mid, *_First)) {

  001a8	0f b6 44 24 02	 movzx	 eax, BYTE PTR $T3[rsp]
  001ad	0f b6 c0	 movzx	 eax, al
  001b0	85 c0		 test	 eax, eax
  001b2	74 66		 je	 SHORT $LN3@Med3_unche
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  001b4	48 8b 84 24 a8
	02 00 00	 mov	 rax, QWORD PTR _Mid$[rsp]
  001bc	48 89 44 24 40	 mov	 QWORD PTR $T10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 139  :     _Ty _Tmp = _STD move(_Left);

  001c1	48 8d 84 24 c0
	01 00 00	 lea	 rax, QWORD PTR _Tmp$15[rsp]
  001c9	48 8b f8	 mov	 rdi, rax
  001cc	48 8b 74 24 40	 mov	 rsi, QWORD PTR $T10[rsp]
  001d1	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  001d6	f3 a4		 rep movsb
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  001d8	48 8b 84 24 a0
	02 00 00	 mov	 rax, QWORD PTR _First$[rsp]
  001e0	48 89 44 24 48	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 140  :     _Left    = _STD move(_Right);

  001e5	48 8b bc 24 a8
	02 00 00	 mov	 rdi, QWORD PTR _Mid$[rsp]
  001ed	48 8b 74 24 48	 mov	 rsi, QWORD PTR $T11[rsp]
  001f2	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  001f7	f3 a4		 rep movsb
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  001f9	48 8d 84 24 c0
	01 00 00	 lea	 rax, QWORD PTR _Tmp$15[rsp]
  00201	48 89 44 24 50	 mov	 QWORD PTR $T12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 141  :     _Right   = _STD move(_Tmp);

  00206	48 8b bc 24 a0
	02 00 00	 mov	 rdi, QWORD PTR _First$[rsp]
  0020e	48 8b 74 24 50	 mov	 rsi, QWORD PTR $T12[rsp]
  00213	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  00218	f3 a4		 rep movsb
$LN3@Med3_unche:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm

; 8278 : }

  0021a	48 8b 8c 24 70
	02 00 00	 mov	 rcx, QWORD PTR __$ArrayPad$[rsp]
  00222	48 33 cc	 xor	 rcx, rsp
  00225	e8 00 00 00 00	 call	 __security_check_cookie
  0022a	48 81 c4 88 02
	00 00		 add	 rsp, 648		; 00000288H
  00231	5f		 pop	 rdi
  00232	5e		 pop	 rsi
  00233	c3		 ret	 0
??$_Med3_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@00V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z ENDP ; std::_Med3_unchecked<mu2::EventScheduleDate *,<lambda_5acf0176913ce2eb7dca573a416bd931> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
;	COMDAT ??$_Copy_backward_memmove@PEAUEventScheduleDate@mu2@@PEAU12@@std@@YAPEAUEventScheduleDate@mu2@@PEAU12@00@Z
_TEXT	SEGMENT
_Count$ = 32
_First_ch$ = 40
$T1 = 48
$T2 = 56
$T3 = 64
_First_ptr$ = 72
_Last_ptr$ = 80
_Dest_ptr$ = 88
_Last_ch$ = 96
_Dest_ch$ = 104
_Result$ = 112
_First$ = 144
_Last$ = 152
_Dest$ = 160
??$_Copy_backward_memmove@PEAUEventScheduleDate@mu2@@PEAU12@@std@@YAPEAUEventScheduleDate@mu2@@PEAU12@00@Z PROC ; std::_Copy_backward_memmove<mu2::EventScheduleDate *,mu2::EventScheduleDate *>, COMDAT

; 5083 : _CtgIt2 _Copy_backward_memmove(_CtgIt1 _First, _CtgIt1 _Last, _CtgIt2 _Dest) {

$LN18:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H

; 4627 :     return _Val;

  00016	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR _First$[rsp]
  0001e	48 89 44 24 30	 mov	 QWORD PTR $T1[rsp], rax

; 5084 :     // implement copy_backward-like function as memmove
; 5085 :     const auto _First_ptr = _STD _To_address(_First);

  00023	48 8b 44 24 30	 mov	 rax, QWORD PTR $T1[rsp]
  00028	48 89 44 24 48	 mov	 QWORD PTR _First_ptr$[rsp], rax

; 4627 :     return _Val;

  0002d	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR _Last$[rsp]
  00035	48 89 44 24 38	 mov	 QWORD PTR $T2[rsp], rax

; 5086 :     const auto _Last_ptr  = _STD _To_address(_Last);

  0003a	48 8b 44 24 38	 mov	 rax, QWORD PTR $T2[rsp]
  0003f	48 89 44 24 50	 mov	 QWORD PTR _Last_ptr$[rsp], rax

; 4627 :     return _Val;

  00044	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR _Dest$[rsp]
  0004c	48 89 44 24 40	 mov	 QWORD PTR $T3[rsp], rax

; 5087 :     const auto _Dest_ptr  = _STD _To_address(_Dest);

  00051	48 8b 44 24 40	 mov	 rax, QWORD PTR $T3[rsp]
  00056	48 89 44 24 58	 mov	 QWORD PTR _Dest_ptr$[rsp], rax

; 5088 :     const auto _First_ch  = const_cast<const char*>(reinterpret_cast<const volatile char*>(_First_ptr));

  0005b	48 8b 44 24 48	 mov	 rax, QWORD PTR _First_ptr$[rsp]
  00060	48 89 44 24 28	 mov	 QWORD PTR _First_ch$[rsp], rax

; 5089 :     const auto _Last_ch   = const_cast<const char*>(reinterpret_cast<const volatile char*>(_Last_ptr));

  00065	48 8b 44 24 50	 mov	 rax, QWORD PTR _Last_ptr$[rsp]
  0006a	48 89 44 24 60	 mov	 QWORD PTR _Last_ch$[rsp], rax

; 5090 :     const auto _Dest_ch   = const_cast<char*>(reinterpret_cast<const volatile char*>(_Dest_ptr));

  0006f	48 8b 44 24 58	 mov	 rax, QWORD PTR _Dest_ptr$[rsp]
  00074	48 89 44 24 68	 mov	 QWORD PTR _Dest_ch$[rsp], rax

; 5091 :     const auto _Count     = static_cast<size_t>(_Last_ch - _First_ch);

  00079	48 8b 44 24 28	 mov	 rax, QWORD PTR _First_ch$[rsp]
  0007e	48 8b 4c 24 60	 mov	 rcx, QWORD PTR _Last_ch$[rsp]
  00083	48 2b c8	 sub	 rcx, rax
  00086	48 8b c1	 mov	 rax, rcx
  00089	48 89 44 24 20	 mov	 QWORD PTR _Count$[rsp], rax

; 5092 :     const auto _Result    = _CSTD memmove(_Dest_ch - _Count, _First_ch, _Count);

  0008e	48 8b 44 24 20	 mov	 rax, QWORD PTR _Count$[rsp]
  00093	48 8b 4c 24 68	 mov	 rcx, QWORD PTR _Dest_ch$[rsp]
  00098	48 2b c8	 sub	 rcx, rax
  0009b	48 8b c1	 mov	 rax, rcx
  0009e	4c 8b 44 24 20	 mov	 r8, QWORD PTR _Count$[rsp]
  000a3	48 8b 54 24 28	 mov	 rdx, QWORD PTR _First_ch$[rsp]
  000a8	48 8b c8	 mov	 rcx, rax
  000ab	e8 00 00 00 00	 call	 memmove
  000b0	48 89 44 24 70	 mov	 QWORD PTR _Result$[rsp], rax

; 5093 :     if constexpr (is_pointer_v<_CtgIt2>) {
; 5094 :         return static_cast<_CtgIt2>(_Result);

  000b5	48 8b 44 24 70	 mov	 rax, QWORD PTR _Result$[rsp]

; 5095 :     } else {
; 5096 :         return _Dest - static_cast<_Iter_diff_t<_CtgIt2>>(_Last_ptr - _First_ptr);
; 5097 :     }
; 5098 : }

  000ba	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  000c1	c3		 ret	 0
??$_Copy_backward_memmove@PEAUEventScheduleDate@mu2@@PEAU12@@std@@YAPEAUEventScheduleDate@mu2@@PEAU12@00@Z ENDP ; std::_Copy_backward_memmove<mu2::EventScheduleDate *,mu2::EventScheduleDate *>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??$_Uninitialized_move@PEAUEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@YAPEAUEventScheduleDate@mu2@@QEAU12@0PEAU12@AEAV?$allocator@UEventScheduleDate@mu2@@@0@@Z
_TEXT	SEGMENT
_UFirst$ = 32
_Backout$ = 40
_ULast$ = 64
$T1 = 72
$T2 = 80
$T3 = 88
$T4 = 96
_Ptr$ = 104
$T5 = 112
$T6 = 120
$T7 = 128
$T8 = 136
$T9 = 144
$T10 = 152
$T11 = 160
__formal$ = 168
_Al$ = 176
_Last$ = 184
_First$ = 192
_First$ = 240
_Last$ = 248
_Dest$ = 256
_Al$ = 264
??$_Uninitialized_move@PEAUEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@YAPEAUEventScheduleDate@mu2@@QEAU12@0PEAU12@AEAV?$allocator@UEventScheduleDate@mu2@@@0@@Z PROC ; std::_Uninitialized_move<mu2::EventScheduleDate *,std::allocator<mu2::EventScheduleDate> >, COMDAT

; 1977 :     const _InIt _First, const _InIt _Last, _Alloc_ptr_t<_Alloc> _Dest, _Alloc& _Al) {

$LN65:
  00000	4c 89 4c 24 20	 mov	 QWORD PTR [rsp+32], r9
  00005	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	56		 push	 rsi
  00015	57		 push	 rdi
  00016	48 81 ec d8 00
	00 00		 sub	 rsp, 216		; 000000d8H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 1382 :         return _It + 0;

  0001d	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR _First$[rsp]
  00025	48 89 44 24 48	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1984 :     auto _UFirst      = _STD _Get_unwrapped(_First);

  0002a	48 8b 44 24 48	 mov	 rax, QWORD PTR $T1[rsp]
  0002f	48 89 44 24 20	 mov	 QWORD PTR _UFirst$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 1382 :         return _It + 0;

  00034	48 8b 84 24 f8
	00 00 00	 mov	 rax, QWORD PTR _Last$[rsp]
  0003c	48 89 44 24 50	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1985 :     const auto _ULast = _STD _Get_unwrapped(_Last);

  00041	48 8b 44 24 50	 mov	 rax, QWORD PTR $T2[rsp]
  00046	48 89 44 24 40	 mov	 QWORD PTR _ULast$[rsp], rax

; 69   :     return _Ptr;

  0004b	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR _Dest$[rsp]
  00053	48 89 44 24 58	 mov	 QWORD PTR $T3[rsp], rax

; 1986 :     if constexpr (conjunction_v<bool_constant<_Iter_move_cat<decltype(_UFirst), _Ptrval>::_Bitcopy_constructible>,
; 1987 :                       _Uses_default_construct<_Alloc, _Ptrval, decltype(_STD move(*_UFirst))>>) {
; 1988 : #if _HAS_CXX20
; 1989 :         if (!_STD is_constant_evaluated())
; 1990 : #endif // _HAS_CXX20
; 1991 :         {
; 1992 :             _STD _Copy_memmove(_UFirst, _ULast, _STD _Unfancy(_Dest));

  00058	48 8b 44 24 58	 mov	 rax, QWORD PTR $T3[rsp]
  0005d	4c 8b c0	 mov	 r8, rax
  00060	48 8b 54 24 40	 mov	 rdx, QWORD PTR _ULast$[rsp]
  00065	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _UFirst$[rsp]
  0006a	e8 00 00 00 00	 call	 ??$_Copy_memmove@PEAUEventScheduleDate@mu2@@PEAU12@@std@@YAPEAUEventScheduleDate@mu2@@PEAU12@00@Z ; std::_Copy_memmove<mu2::EventScheduleDate *,mu2::EventScheduleDate *>

; 1993 :             return _Dest + (_ULast - _UFirst);

  0006f	48 8b 44 24 20	 mov	 rax, QWORD PTR _UFirst$[rsp]
  00074	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _ULast$[rsp]
  00079	48 2b c8	 sub	 rcx, rax
  0007c	48 8b c1	 mov	 rax, rcx
  0007f	48 99		 cdq
  00081	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  00086	48 f7 f9	 idiv	 rcx
  00089	48 69 c0 a8 00
	00 00		 imul	 rax, rax, 168		; 000000a8H
  00090	48 8b 8c 24 00
	01 00 00	 mov	 rcx, QWORD PTR _Dest$[rsp]
  00098	48 03 c8	 add	 rcx, rax
  0009b	48 8b c1	 mov	 rax, rcx
  0009e	e9 2a 01 00 00	 jmp	 $LN1@Uninitiali

; 1833 :     _CONSTEXPR20 _Uninitialized_backout_al(pointer _Dest, _Alloc& _Al_) : _First(_Dest), _Last(_Dest), _Al(_Al_) {}

  000a3	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR _Dest$[rsp]
  000ab	48 89 44 24 28	 mov	 QWORD PTR _Backout$[rsp], rax
  000b0	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR _Dest$[rsp]
  000b8	48 89 44 24 30	 mov	 QWORD PTR _Backout$[rsp+8], rax
  000bd	48 8b 84 24 08
	01 00 00	 mov	 rax, QWORD PTR _Al$[rsp]
  000c5	48 89 44 24 38	 mov	 QWORD PTR _Backout$[rsp+16], rax

; 1994 :         }
; 1995 :     }
; 1996 : 
; 1997 :     _Uninitialized_backout_al<_Alloc> _Backout{_Dest, _Al};
; 1998 :     for (; _UFirst != _ULast; ++_UFirst) {

  000ca	eb 10		 jmp	 SHORT $LN4@Uninitiali
$LN2@Uninitiali:
  000cc	48 8b 44 24 20	 mov	 rax, QWORD PTR _UFirst$[rsp]
  000d1	48 05 a8 00 00
	00		 add	 rax, 168		; 000000a8H
  000d7	48 89 44 24 20	 mov	 QWORD PTR _UFirst$[rsp], rax
$LN4@Uninitiali:
  000dc	48 8b 44 24 40	 mov	 rax, QWORD PTR _ULast$[rsp]
  000e1	48 39 44 24 20	 cmp	 QWORD PTR _UFirst$[rsp], rax
  000e6	0f 84 8b 00 00
	00		 je	 $LN3@Uninitiali
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  000ec	48 8b 44 24 20	 mov	 rax, QWORD PTR _UFirst$[rsp]
  000f1	48 89 44 24 60	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1999 :         _Backout._Emplace_back(_STD move(*_UFirst));

  000f6	48 8b 44 24 60	 mov	 rax, QWORD PTR $T4[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  000fb	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1844 :         allocator_traits<_Alloc>::construct(_Al, _STD _Unfancy(_Last), _STD forward<_Types>(_Vals)...);

  00103	48 8b 44 24 30	 mov	 rax, QWORD PTR _Backout$[rsp+8]
  00108	48 89 44 24 68	 mov	 QWORD PTR _Ptr$[rsp], rax

; 69   :     return _Ptr;

  0010d	48 8b 44 24 68	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00112	48 89 44 24 70	 mov	 QWORD PTR $T5[rsp], rax

; 1844 :         allocator_traits<_Alloc>::construct(_Al, _STD _Unfancy(_Last), _STD forward<_Types>(_Vals)...);

  00117	48 8b 44 24 38	 mov	 rax, QWORD PTR _Backout$[rsp+16]
  0011c	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR __formal$[rsp], rax
  00124	48 8b 44 24 70	 mov	 rax, QWORD PTR $T5[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  00129	48 89 44 24 78	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 732  :         ::new (const_cast<void*>(static_cast<const volatile void*>(_Ptr))) _Objty(_STD forward<_Types>(_Args)...);

  0012e	48 8b 44 24 78	 mov	 rax, QWORD PTR $T6[rsp]
  00133	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T8[rsp], rax

; 1844 :         allocator_traits<_Alloc>::construct(_Al, _STD _Unfancy(_Last), _STD forward<_Types>(_Vals)...);

  0013b	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR $T7[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00143	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 732  :         ::new (const_cast<void*>(static_cast<const volatile void*>(_Ptr))) _Objty(_STD forward<_Types>(_Args)...);

  0014b	48 8b bc 24 88
	00 00 00	 mov	 rdi, QWORD PTR $T8[rsp]
  00153	48 8b b4 24 90
	00 00 00	 mov	 rsi, QWORD PTR $T9[rsp]
  0015b	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  00160	f3 a4		 rep movsb

; 1845 :         ++_Last;

  00162	48 8b 44 24 30	 mov	 rax, QWORD PTR _Backout$[rsp+8]
  00167	48 05 a8 00 00
	00		 add	 rax, 168		; 000000a8H
  0016d	48 89 44 24 30	 mov	 QWORD PTR _Backout$[rsp+8], rax

; 2000 :     }

  00172	e9 55 ff ff ff	 jmp	 $LN2@Uninitiali
$LN3@Uninitiali:

; 1849 :         _First = _Last;

  00177	48 8b 44 24 30	 mov	 rax, QWORD PTR _Backout$[rsp+8]
  0017c	48 89 44 24 28	 mov	 QWORD PTR _Backout$[rsp], rax

; 1850 :         return _Last;

  00181	48 8b 44 24 30	 mov	 rax, QWORD PTR _Backout$[rsp+8]
  00186	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax

; 2001 : 
; 2002 :     return _Backout._Release();

  0018e	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  00196	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax

; 1839 :         _STD _Destroy_range(_First, _Last, _Al);

  0019e	48 8b 44 24 38	 mov	 rax, QWORD PTR _Backout$[rsp+16]
  001a3	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR _Al$[rsp], rax
  001ab	48 8b 44 24 30	 mov	 rax, QWORD PTR _Backout$[rsp+8]
  001b0	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR _Last$[rsp], rax
  001b8	48 8b 44 24 28	 mov	 rax, QWORD PTR _Backout$[rsp]
  001bd	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR _First$[rsp], rax

; 2001 : 
; 2002 :     return _Backout._Release();

  001c5	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
$LN1@Uninitiali:

; 2003 : }

  001cd	48 81 c4 d8 00
	00 00		 add	 rsp, 216		; 000000d8H
  001d4	5f		 pop	 rdi
  001d5	5e		 pop	 rsi
  001d6	c3		 ret	 0
??$_Uninitialized_move@PEAUEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@YAPEAUEventScheduleDate@mu2@@QEAU12@0PEAU12@AEAV?$allocator@UEventScheduleDate@mu2@@@0@@Z ENDP ; std::_Uninitialized_move<mu2::EventScheduleDate *,std::allocator<mu2::EventScheduleDate> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ??1_Reallocation_guard@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@QEAA@XZ
_TEXT	SEGMENT
_Bytes$ = 32
_Ptr$ = 40
_Count$ = 48
_Ptr$ = 56
_Al$ = 64
_Last$ = 72
_First$ = 80
this$ = 88
this$ = 112
??1_Reallocation_guard@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@QEAA@XZ PROC ; std::vector<mu2::EventScheduleDate,std::allocator<mu2::EventScheduleDate> >::_Reallocation_guard::~_Reallocation_guard, COMDAT

; 620  :         _CONSTEXPR20 ~_Reallocation_guard() noexcept {

$LN25:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 68	 sub	 rsp, 104		; 00000068H

; 621  :             if (_New_begin != nullptr) {

  00009	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00013	0f 84 95 00 00
	00		 je	 $LN2@Reallocati

; 622  :                 _STD _Destroy_range(_Constructed_first, _Constructed_last, _Al);

  00019	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0001e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00021	48 89 44 24 40	 mov	 QWORD PTR _Al$[rsp], rax
  00026	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0002b	48 8b 40 20	 mov	 rax, QWORD PTR [rax+32]
  0002f	48 89 44 24 48	 mov	 QWORD PTR _Last$[rsp], rax
  00034	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  00039	48 8b 40 18	 mov	 rax, QWORD PTR [rax+24]
  0003d	48 89 44 24 50	 mov	 QWORD PTR _First$[rsp], rax

; 623  :                 _Al.deallocate(_New_begin, _New_capacity);

  00042	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  00047	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0004b	48 89 44 24 30	 mov	 QWORD PTR _Count$[rsp], rax
  00050	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  00055	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00059	48 89 44 24 38	 mov	 QWORD PTR _Ptr$[rsp], rax
  0005e	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  00063	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00066	48 89 44 24 58	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  0006b	48 69 44 24 30
	a8 00 00 00	 imul	 rax, QWORD PTR _Count$[rsp], 168 ; 000000a8H
  00074	48 89 44 24 20	 mov	 QWORD PTR _Bytes$[rsp], rax
  00079	48 8b 44 24 38	 mov	 rax, QWORD PTR _Ptr$[rsp]
  0007e	48 89 44 24 28	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  00083	48 81 7c 24 20
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  0008c	72 10		 jb	 SHORT $LN16@Reallocati

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  0008e	48 8d 54 24 20	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  00093	48 8d 4c 24 28	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  00098	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  0009d	90		 npad	 1
$LN16@Reallocati:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  0009e	48 8b 54 24 20	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  000a3	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  000a8	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  000ad	90		 npad	 1
$LN2@Reallocati:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 625  :         }

  000ae	48 83 c4 68	 add	 rsp, 104		; 00000068H
  000b2	c3		 ret	 0
??1_Reallocation_guard@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@QEAA@XZ ENDP ; std::vector<mu2::EventScheduleDate,std::allocator<mu2::EventScheduleDate> >::_Reallocation_guard::~_Reallocation_guard
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
;	COMDAT ??$_Guess_median_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@00V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z
_TEXT	SEGMENT
_Step$1 = 32
_Count$ = 40
_Two_step$2 = 48
_First$ = 80
_Mid$ = 88
_Last$ = 96
_Pred$ = 104
??$_Guess_median_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@00V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z PROC ; std::_Guess_median_unchecked<mu2::EventScheduleDate *,<lambda_5acf0176913ce2eb7dca573a416bd931> >, COMDAT

; 8281 : _CONSTEXPR20 void _Guess_median_unchecked(_RanIt _First, _RanIt _Mid, _RanIt _Last, _Pr _Pred) {

  00000	44 88 4c 24 20	 mov	 BYTE PTR [rsp+32], r9b
  00005	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 8282 :     // sort median element to middle
; 8283 :     using _Diff        = _Iter_diff_t<_RanIt>;
; 8284 :     const _Diff _Count = _Last - _First;

  00018	48 8b 44 24 50	 mov	 rax, QWORD PTR _First$[rsp]
  0001d	48 8b 4c 24 60	 mov	 rcx, QWORD PTR _Last$[rsp]
  00022	48 2b c8	 sub	 rcx, rax
  00025	48 8b c1	 mov	 rax, rcx
  00028	48 99		 cdq
  0002a	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  0002f	48 f7 f9	 idiv	 rcx
  00032	48 89 44 24 28	 mov	 QWORD PTR _Count$[rsp], rax

; 8285 :     if (40 < _Count) { // Tukey's ninther

  00037	48 83 7c 24 28
	28		 cmp	 QWORD PTR _Count$[rsp], 40 ; 00000028H
  0003d	0f 8e 10 01 00
	00		 jle	 $LN2@Guess_medi

; 8286 :         const _Diff _Step     = (_Count + 1) >> 3; // +1 can't overflow because range was made inclusive in caller

  00043	48 8b 44 24 28	 mov	 rax, QWORD PTR _Count$[rsp]
  00048	48 ff c0	 inc	 rax
  0004b	48 c1 f8 03	 sar	 rax, 3
  0004f	48 89 44 24 20	 mov	 QWORD PTR _Step$1[rsp], rax

; 8287 :         const _Diff _Two_step = _Step << 1; // note: intentionally discards low-order bit

  00054	48 8b 44 24 20	 mov	 rax, QWORD PTR _Step$1[rsp]
  00059	48 d1 e0	 shl	 rax, 1
  0005c	48 89 44 24 30	 mov	 QWORD PTR _Two_step$2[rsp], rax

; 8288 :         _STD _Med3_unchecked(_First, _First + _Step, _First + _Two_step, _Pred);

  00061	48 69 44 24 30
	a8 00 00 00	 imul	 rax, QWORD PTR _Two_step$2[rsp], 168 ; 000000a8H
  0006a	48 8b 4c 24 50	 mov	 rcx, QWORD PTR _First$[rsp]
  0006f	48 03 c8	 add	 rcx, rax
  00072	48 8b c1	 mov	 rax, rcx
  00075	48 69 4c 24 20
	a8 00 00 00	 imul	 rcx, QWORD PTR _Step$1[rsp], 168 ; 000000a8H
  0007e	48 8b 54 24 50	 mov	 rdx, QWORD PTR _First$[rsp]
  00083	48 03 d1	 add	 rdx, rcx
  00086	48 8b ca	 mov	 rcx, rdx
  00089	44 0f b6 4c 24
	68		 movzx	 r9d, BYTE PTR _Pred$[rsp]
  0008f	4c 8b c0	 mov	 r8, rax
  00092	48 8b d1	 mov	 rdx, rcx
  00095	48 8b 4c 24 50	 mov	 rcx, QWORD PTR _First$[rsp]
  0009a	e8 00 00 00 00	 call	 ??$_Med3_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@00V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z ; std::_Med3_unchecked<mu2::EventScheduleDate *,<lambda_5acf0176913ce2eb7dca573a416bd931> >

; 8289 :         _STD _Med3_unchecked(_Mid - _Step, _Mid, _Mid + _Step, _Pred);

  0009f	48 69 44 24 20
	a8 00 00 00	 imul	 rax, QWORD PTR _Step$1[rsp], 168 ; 000000a8H
  000a8	48 8b 4c 24 58	 mov	 rcx, QWORD PTR _Mid$[rsp]
  000ad	48 03 c8	 add	 rcx, rax
  000b0	48 8b c1	 mov	 rax, rcx
  000b3	48 69 4c 24 20
	a8 00 00 00	 imul	 rcx, QWORD PTR _Step$1[rsp], 168 ; 000000a8H
  000bc	48 8b 54 24 58	 mov	 rdx, QWORD PTR _Mid$[rsp]
  000c1	48 2b d1	 sub	 rdx, rcx
  000c4	48 8b ca	 mov	 rcx, rdx
  000c7	44 0f b6 4c 24
	68		 movzx	 r9d, BYTE PTR _Pred$[rsp]
  000cd	4c 8b c0	 mov	 r8, rax
  000d0	48 8b 54 24 58	 mov	 rdx, QWORD PTR _Mid$[rsp]
  000d5	e8 00 00 00 00	 call	 ??$_Med3_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@00V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z ; std::_Med3_unchecked<mu2::EventScheduleDate *,<lambda_5acf0176913ce2eb7dca573a416bd931> >

; 8290 :         _STD _Med3_unchecked(_Last - _Two_step, _Last - _Step, _Last, _Pred);

  000da	48 69 44 24 20
	a8 00 00 00	 imul	 rax, QWORD PTR _Step$1[rsp], 168 ; 000000a8H
  000e3	48 8b 4c 24 60	 mov	 rcx, QWORD PTR _Last$[rsp]
  000e8	48 2b c8	 sub	 rcx, rax
  000eb	48 8b c1	 mov	 rax, rcx
  000ee	48 69 4c 24 30
	a8 00 00 00	 imul	 rcx, QWORD PTR _Two_step$2[rsp], 168 ; 000000a8H
  000f7	48 8b 54 24 60	 mov	 rdx, QWORD PTR _Last$[rsp]
  000fc	48 2b d1	 sub	 rdx, rcx
  000ff	48 8b ca	 mov	 rcx, rdx
  00102	44 0f b6 4c 24
	68		 movzx	 r9d, BYTE PTR _Pred$[rsp]
  00108	4c 8b 44 24 60	 mov	 r8, QWORD PTR _Last$[rsp]
  0010d	48 8b d0	 mov	 rdx, rax
  00110	e8 00 00 00 00	 call	 ??$_Med3_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@00V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z ; std::_Med3_unchecked<mu2::EventScheduleDate *,<lambda_5acf0176913ce2eb7dca573a416bd931> >

; 8291 :         _STD _Med3_unchecked(_First + _Step, _Mid, _Last - _Step, _Pred);

  00115	48 69 44 24 20
	a8 00 00 00	 imul	 rax, QWORD PTR _Step$1[rsp], 168 ; 000000a8H
  0011e	48 8b 4c 24 60	 mov	 rcx, QWORD PTR _Last$[rsp]
  00123	48 2b c8	 sub	 rcx, rax
  00126	48 8b c1	 mov	 rax, rcx
  00129	48 69 4c 24 20
	a8 00 00 00	 imul	 rcx, QWORD PTR _Step$1[rsp], 168 ; 000000a8H
  00132	48 8b 54 24 50	 mov	 rdx, QWORD PTR _First$[rsp]
  00137	48 03 d1	 add	 rdx, rcx
  0013a	48 8b ca	 mov	 rcx, rdx
  0013d	44 0f b6 4c 24
	68		 movzx	 r9d, BYTE PTR _Pred$[rsp]
  00143	4c 8b c0	 mov	 r8, rax
  00146	48 8b 54 24 58	 mov	 rdx, QWORD PTR _Mid$[rsp]
  0014b	e8 00 00 00 00	 call	 ??$_Med3_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@00V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z ; std::_Med3_unchecked<mu2::EventScheduleDate *,<lambda_5acf0176913ce2eb7dca573a416bd931> >
  00150	90		 npad	 1

; 8292 :     } else {

  00151	eb 1b		 jmp	 SHORT $LN3@Guess_medi
$LN2@Guess_medi:

; 8293 :         _STD _Med3_unchecked(_First, _Mid, _Last, _Pred);

  00153	44 0f b6 4c 24
	68		 movzx	 r9d, BYTE PTR _Pred$[rsp]
  00159	4c 8b 44 24 60	 mov	 r8, QWORD PTR _Last$[rsp]
  0015e	48 8b 54 24 58	 mov	 rdx, QWORD PTR _Mid$[rsp]
  00163	48 8b 4c 24 50	 mov	 rcx, QWORD PTR _First$[rsp]
  00168	e8 00 00 00 00	 call	 ??$_Med3_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@00V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z ; std::_Med3_unchecked<mu2::EventScheduleDate *,<lambda_5acf0176913ce2eb7dca573a416bd931> >
  0016d	90		 npad	 1
$LN3@Guess_medi:

; 8294 :     }
; 8295 : }

  0016e	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00172	c3		 ret	 0
??$_Guess_median_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@00V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z ENDP ; std::_Guess_median_unchecked<mu2::EventScheduleDate *,<lambda_5acf0176913ce2eb7dca573a416bd931> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_heap_algorithms.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_heap_algorithms.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_heap_algorithms.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_heap_algorithms.hpp
;	COMDAT ??$_Pop_heap_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z
_TEXT	SEGMENT
$T1 = 48
$T2 = 56
$T3 = 64
$T4 = 72
tv80 = 80
_Val$5 = 96
__$ArrayPad$ = 272
_First$ = 320
_Last$ = 328
_Pred$ = 336
??$_Pop_heap_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z PROC ; std::_Pop_heap_unchecked<mu2::EventScheduleDate *,<lambda_5acf0176913ce2eb7dca573a416bd931> >, COMDAT

; 98   : _CONSTEXPR20 void _Pop_heap_unchecked(_RanIt _First, _RanIt _Last, _Pr _Pred) {

  00000	44 88 44 24 18	 mov	 BYTE PTR [rsp+24], r8b
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	56		 push	 rsi
  00010	57		 push	 rdi
  00011	48 81 ec 28 01
	00 00		 sub	 rsp, 296		; 00000128H
  00018	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR __security_cookie
  0001f	48 33 c4	 xor	 rax, rsp
  00022	48 89 84 24 10
	01 00 00	 mov	 QWORD PTR __$ArrayPad$[rsp], rax

; 99   :     // pop *_First to *(_Last - 1) and reheap
; 100  :     if (2 <= _Last - _First) {

  0002a	48 8b 84 24 40
	01 00 00	 mov	 rax, QWORD PTR _First$[rsp]
  00032	48 8b 8c 24 48
	01 00 00	 mov	 rcx, QWORD PTR _Last$[rsp]
  0003a	48 2b c8	 sub	 rcx, rax
  0003d	48 8b c1	 mov	 rax, rcx
  00040	48 99		 cdq
  00042	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  00047	48 f7 f9	 idiv	 rcx
  0004a	48 83 f8 02	 cmp	 rax, 2
  0004e	0f 8c c0 00 00
	00		 jl	 $LN2@Pop_heap_u

; 101  :         --_Last;

  00054	48 8b 84 24 48
	01 00 00	 mov	 rax, QWORD PTR _Last$[rsp]
  0005c	48 2d a8 00 00
	00		 sub	 rax, 168		; 000000a8H
  00062	48 89 84 24 48
	01 00 00	 mov	 QWORD PTR _Last$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  0006a	48 8b 84 24 48
	01 00 00	 mov	 rax, QWORD PTR _Last$[rsp]
  00072	48 89 44 24 30	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_heap_algorithms.hpp

; 102  :         _Iter_value_t<_RanIt> _Val(_STD move(*_Last));

  00077	48 8d 44 24 60	 lea	 rax, QWORD PTR _Val$5[rsp]
  0007c	48 8b f8	 mov	 rdi, rax
  0007f	48 8b 74 24 30	 mov	 rsi, QWORD PTR $T1[rsp]
  00084	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  00089	f3 a4		 rep movsb
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  0008b	48 8d 44 24 60	 lea	 rax, QWORD PTR _Val$5[rsp]
  00090	48 89 44 24 40	 mov	 QWORD PTR $T3[rsp], rax
  00095	48 8b 84 24 40
	01 00 00	 mov	 rax, QWORD PTR _First$[rsp]
  0009d	48 89 44 24 38	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_heap_algorithms.hpp

; 91   :     *_Dest      = _STD move(*_First);

  000a2	48 8b bc 24 48
	01 00 00	 mov	 rdi, QWORD PTR _Last$[rsp]
  000aa	48 8b 74 24 38	 mov	 rsi, QWORD PTR $T2[rsp]
  000af	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  000b4	f3 a4		 rep movsb

; 103  :         _STD _Pop_heap_hole_unchecked(_First, _Last, _Last, _STD move(_Val), _Pred);

  000b6	48 8b 44 24 40	 mov	 rax, QWORD PTR $T3[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  000bb	48 89 44 24 48	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_heap_algorithms.hpp

; 93   :     _STD _Pop_heap_hole_by_index(

  000c0	48 8b 44 24 48	 mov	 rax, QWORD PTR $T4[rsp]
  000c5	48 89 44 24 50	 mov	 QWORD PTR tv80[rsp], rax
  000ca	48 8b 8c 24 40
	01 00 00	 mov	 rcx, QWORD PTR _First$[rsp]
  000d2	48 8b 94 24 48
	01 00 00	 mov	 rdx, QWORD PTR _Last$[rsp]
  000da	48 2b d1	 sub	 rdx, rcx
  000dd	48 8b ca	 mov	 rcx, rdx
  000e0	48 8b c1	 mov	 rax, rcx
  000e3	48 99		 cdq
  000e5	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  000ea	48 f7 f9	 idiv	 rcx
  000ed	0f b6 8c 24 50
	01 00 00	 movzx	 ecx, BYTE PTR _Pred$[rsp]
  000f5	88 4c 24 20	 mov	 BYTE PTR [rsp+32], cl
  000f9	48 8b 4c 24 50	 mov	 rcx, QWORD PTR tv80[rsp]
  000fe	4c 8b c9	 mov	 r9, rcx
  00101	4c 8b c0	 mov	 r8, rax
  00104	33 d2		 xor	 edx, edx
  00106	48 8b 8c 24 40
	01 00 00	 mov	 rcx, QWORD PTR _First$[rsp]
  0010e	e8 00 00 00 00	 call	 ??$_Pop_heap_hole_by_index@PEAUEventScheduleDate@mu2@@U12@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@_J1$$QEAU12@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z ; std::_Pop_heap_hole_by_index<mu2::EventScheduleDate *,mu2::EventScheduleDate,<lambda_5acf0176913ce2eb7dca573a416bd931> >
  00113	90		 npad	 1
$LN2@Pop_heap_u:

; 104  :     }
; 105  : }

  00114	48 8b 8c 24 10
	01 00 00	 mov	 rcx, QWORD PTR __$ArrayPad$[rsp]
  0011c	48 33 cc	 xor	 rcx, rsp
  0011f	e8 00 00 00 00	 call	 __security_check_cookie
  00124	48 81 c4 28 01
	00 00		 add	 rsp, 296		; 00000128H
  0012b	5f		 pop	 rdi
  0012c	5e		 pop	 rsi
  0012d	c3		 ret	 0
??$_Pop_heap_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z ENDP ; std::_Pop_heap_unchecked<mu2::EventScheduleDate *,<lambda_5acf0176913ce2eb7dca573a416bd931> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_heap_algorithms.hpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_heap_algorithms.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_heap_algorithms.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_heap_algorithms.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_heap_algorithms.hpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_heap_algorithms.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_heap_algorithms.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_heap_algorithms.hpp
;	COMDAT ??$_Pop_heap_hole_by_index@PEAUEventScheduleDate@mu2@@U12@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@_J1$$QEAU12@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z
_TEXT	SEGMENT
$T1 = 0
$T2 = 1
_Idx$ = 8
_Pred$ = 16
_Hole$ = 24
tv142 = 32
tv166 = 36
_Idx$3 = 40
_Max_sequence_non_leaf$ = 48
_Val$ = 56
$T4 = 64
$T5 = 72
$T6 = 80
_Top$ = 88
$T7 = 96
$T8 = 104
_First$ = 144
_Hole$ = 152
_Bottom$ = 160
_Val$ = 168
_Pred$ = 176
??$_Pop_heap_hole_by_index@PEAUEventScheduleDate@mu2@@U12@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@_J1$$QEAU12@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z PROC ; std::_Pop_heap_hole_by_index<mu2::EventScheduleDate *,mu2::EventScheduleDate,<lambda_5acf0176913ce2eb7dca573a416bd931> >, COMDAT

; 58   :     _RanIt _First, _Iter_diff_t<_RanIt> _Hole, _Iter_diff_t<_RanIt> _Bottom, _Ty&& _Val, _Pr _Pred) {

  00000	4c 89 4c 24 20	 mov	 QWORD PTR [rsp+32], r9
  00005	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	56		 push	 rsi
  00015	57		 push	 rdi
  00016	48 83 ec 78	 sub	 rsp, 120		; 00000078H

; 59   :     // percolate _Hole to _Bottom, then push _Val
; 60   :     _STL_INTERNAL_CHECK(_Bottom > 0);
; 61   : 
; 62   :     using _Diff      = _Iter_diff_t<_RanIt>;
; 63   :     const _Diff _Top = _Hole;

  0001a	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR _Hole$[rsp]
  00022	48 89 44 24 58	 mov	 QWORD PTR _Top$[rsp], rax

; 64   :     _Diff _Idx       = _Hole;

  00027	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR _Hole$[rsp]
  0002f	48 89 44 24 08	 mov	 QWORD PTR _Idx$[rsp], rax

; 65   : 
; 66   :     // Check whether _Idx can have a child before calculating that child's index, since
; 67   :     // calculating the child's index can trigger integer overflows
; 68   :     const _Diff _Max_sequence_non_leaf = (_Bottom - 1) >> 1; // shift for codegen

  00034	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR _Bottom$[rsp]
  0003c	48 ff c8	 dec	 rax
  0003f	48 d1 f8	 sar	 rax, 1
  00042	48 89 44 24 30	 mov	 QWORD PTR _Max_sequence_non_leaf$[rsp], rax
$LN2@Pop_heap_h:

; 69   :     while (_Idx < _Max_sequence_non_leaf) { // move _Hole down to larger child

  00047	48 8b 44 24 30	 mov	 rax, QWORD PTR _Max_sequence_non_leaf$[rsp]
  0004c	48 39 44 24 08	 cmp	 QWORD PTR _Idx$[rsp], rax
  00051	0f 8d c5 00 00
	00		 jge	 $LN3@Pop_heap_h

; 70   :         _Idx = 2 * _Idx + 2;

  00057	48 8b 44 24 08	 mov	 rax, QWORD PTR _Idx$[rsp]
  0005c	48 8d 44 00 02	 lea	 rax, QWORD PTR [rax+rax+2]
  00061	48 89 44 24 08	 mov	 QWORD PTR _Idx$[rsp], rax

; 71   :         if (_DEBUG_LT_PRED(_Pred, *(_First + _Idx), *(_First + (_Idx - 1)))) {

  00066	48 69 44 24 08
	a8 00 00 00	 imul	 rax, QWORD PTR _Idx$[rsp], 168 ; 000000a8H
  0006f	48 8b 4c 24 08	 mov	 rcx, QWORD PTR _Idx$[rsp]
  00074	48 ff c9	 dec	 rcx
  00077	48 69 c9 a8 00
	00 00		 imul	 rcx, rcx, 168		; 000000a8H
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 229  : 			return lhs.readyTime < rhs.readyTime;

  0007e	48 8b 94 24 90
	00 00 00	 mov	 rdx, QWORD PTR _First$[rsp]
  00086	4c 8b 84 24 90
	00 00 00	 mov	 r8, QWORD PTR _First$[rsp]
  0008e	49 8b 0c 08	 mov	 rcx, QWORD PTR [r8+rcx]
  00092	48 39 0c 02	 cmp	 QWORD PTR [rdx+rax], rcx
  00096	7d 0a		 jge	 SHORT $LN9@Pop_heap_h
  00098	c7 44 24 20 01
	00 00 00	 mov	 DWORD PTR tv142[rsp], 1
  000a0	eb 08		 jmp	 SHORT $LN10@Pop_heap_h
$LN9@Pop_heap_h:
  000a2	c7 44 24 20 00
	00 00 00	 mov	 DWORD PTR tv142[rsp], 0
$LN10@Pop_heap_h:
  000aa	0f b6 44 24 20	 movzx	 eax, BYTE PTR tv142[rsp]
  000af	88 04 24	 mov	 BYTE PTR $T1[rsp], al
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_heap_algorithms.hpp

; 71   :         if (_DEBUG_LT_PRED(_Pred, *(_First + _Idx), *(_First + (_Idx - 1)))) {

  000b2	0f b6 04 24	 movzx	 eax, BYTE PTR $T1[rsp]
  000b6	0f b6 c0	 movzx	 eax, al
  000b9	85 c0		 test	 eax, eax
  000bb	74 0d		 je	 SHORT $LN4@Pop_heap_h

; 72   :             --_Idx;

  000bd	48 8b 44 24 08	 mov	 rax, QWORD PTR _Idx$[rsp]
  000c2	48 ff c8	 dec	 rax
  000c5	48 89 44 24 08	 mov	 QWORD PTR _Idx$[rsp], rax
$LN4@Pop_heap_h:

; 74   :         *(_First + _Hole) = _STD move(*(_First + _Idx));

  000ca	48 69 44 24 08
	a8 00 00 00	 imul	 rax, QWORD PTR _Idx$[rsp], 168 ; 000000a8H
  000d3	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR _First$[rsp]
  000db	48 03 c8	 add	 rcx, rax
  000de	48 8b c1	 mov	 rax, rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  000e1	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_heap_algorithms.hpp

; 74   :         *(_First + _Hole) = _STD move(*(_First + _Idx));

  000e6	48 69 84 24 98
	00 00 00 a8 00
	00 00		 imul	 rax, QWORD PTR _Hole$[rsp], 168 ; 000000a8H
  000f2	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR _First$[rsp]
  000fa	48 8d 3c 01	 lea	 rdi, QWORD PTR [rcx+rax]
  000fe	48 8b 74 24 40	 mov	 rsi, QWORD PTR $T4[rsp]
  00103	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  00108	f3 a4		 rep movsb

; 75   :         _Hole             = _Idx;

  0010a	48 8b 44 24 08	 mov	 rax, QWORD PTR _Idx$[rsp]
  0010f	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR _Hole$[rsp], rax

; 76   :     }

  00117	e9 2b ff ff ff	 jmp	 $LN2@Pop_heap_h
$LN3@Pop_heap_h:

; 77   : 
; 78   :     if (_Idx == _Max_sequence_non_leaf && _Bottom % 2 == 0) { // only child at bottom, move _Hole down to it

  0011c	48 8b 44 24 30	 mov	 rax, QWORD PTR _Max_sequence_non_leaf$[rsp]
  00121	48 39 44 24 08	 cmp	 QWORD PTR _Idx$[rsp], rax
  00126	75 75		 jne	 SHORT $LN5@Pop_heap_h
  00128	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR _Bottom$[rsp]
  00130	48 99		 cdq
  00132	48 83 e0 01	 and	 rax, 1
  00136	48 33 c2	 xor	 rax, rdx
  00139	48 2b c2	 sub	 rax, rdx
  0013c	48 85 c0	 test	 rax, rax
  0013f	75 5c		 jne	 SHORT $LN5@Pop_heap_h

; 79   :         *(_First + _Hole) = _STD move(*(_First + (_Bottom - 1)));

  00141	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR _Bottom$[rsp]
  00149	48 ff c8	 dec	 rax
  0014c	48 69 c0 a8 00
	00 00		 imul	 rax, rax, 168		; 000000a8H
  00153	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR _First$[rsp]
  0015b	48 03 c8	 add	 rcx, rax
  0015e	48 8b c1	 mov	 rax, rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00161	48 89 44 24 48	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_heap_algorithms.hpp

; 79   :         *(_First + _Hole) = _STD move(*(_First + (_Bottom - 1)));

  00166	48 69 84 24 98
	00 00 00 a8 00
	00 00		 imul	 rax, QWORD PTR _Hole$[rsp], 168 ; 000000a8H
  00172	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR _First$[rsp]
  0017a	48 8d 3c 01	 lea	 rdi, QWORD PTR [rcx+rax]
  0017e	48 8b 74 24 48	 mov	 rsi, QWORD PTR $T5[rsp]
  00183	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  00188	f3 a4		 rep movsb

; 80   :         _Hole             = _Bottom - 1;

  0018a	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR _Bottom$[rsp]
  00192	48 ff c8	 dec	 rax
  00195	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR _Hole$[rsp], rax
$LN5@Pop_heap_h:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  0019d	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR _Val$[rsp]
  001a5	48 89 44 24 50	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_heap_algorithms.hpp

; 83   :     _STD _Push_heap_by_index(_First, _Hole, _Top, _STD forward<_Ty>(_Val), _Pred);

  001aa	0f b6 84 24 b0
	00 00 00	 movzx	 eax, BYTE PTR _Pred$[rsp]
  001b2	88 44 24 10	 mov	 BYTE PTR _Pred$[rsp], al
  001b6	48 8b 44 24 50	 mov	 rax, QWORD PTR $T6[rsp]
  001bb	48 89 44 24 38	 mov	 QWORD PTR _Val$[rsp], rax
  001c0	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR _Hole$[rsp]
  001c8	48 89 44 24 18	 mov	 QWORD PTR _Hole$[rsp], rax

; 26   :     for (_Diff _Idx                                                         = (_Hole - 1) >> 1; // shift for codegen

  001cd	48 8b 44 24 18	 mov	 rax, QWORD PTR _Hole$[rsp]
  001d2	48 ff c8	 dec	 rax
  001d5	48 d1 f8	 sar	 rax, 1
  001d8	48 89 44 24 28	 mov	 QWORD PTR _Idx$3[rsp], rax
  001dd	eb 10		 jmp	 SHORT $LN29@Pop_heap_h
$LN27@Pop_heap_h:

; 27   :         _Top < _Hole && _DEBUG_LT_PRED(_Pred, *(_First + _Idx), _Val); _Idx = (_Hole - 1) >> 1) { // shift for codegen

  001df	48 8b 44 24 18	 mov	 rax, QWORD PTR _Hole$[rsp]
  001e4	48 ff c8	 dec	 rax
  001e7	48 d1 f8	 sar	 rax, 1
  001ea	48 89 44 24 28	 mov	 QWORD PTR _Idx$3[rsp], rax
$LN29@Pop_heap_h:
  001ef	48 8b 44 24 18	 mov	 rax, QWORD PTR _Hole$[rsp]
  001f4	48 39 44 24 58	 cmp	 QWORD PTR _Top$[rsp], rax
  001f9	0f 8d 92 00 00
	00		 jge	 $LN28@Pop_heap_h
  001ff	48 69 44 24 28
	a8 00 00 00	 imul	 rax, QWORD PTR _Idx$3[rsp], 168 ; 000000a8H
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 229  : 			return lhs.readyTime < rhs.readyTime;

  00208	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR _First$[rsp]
  00210	48 8b 54 24 38	 mov	 rdx, QWORD PTR _Val$[rsp]
  00215	48 8b 12	 mov	 rdx, QWORD PTR [rdx]
  00218	48 39 14 01	 cmp	 QWORD PTR [rcx+rax], rdx
  0021c	7d 0a		 jge	 SHORT $LN33@Pop_heap_h
  0021e	c7 44 24 24 01
	00 00 00	 mov	 DWORD PTR tv166[rsp], 1
  00226	eb 08		 jmp	 SHORT $LN34@Pop_heap_h
$LN33@Pop_heap_h:
  00228	c7 44 24 24 00
	00 00 00	 mov	 DWORD PTR tv166[rsp], 0
$LN34@Pop_heap_h:
  00230	0f b6 44 24 24	 movzx	 eax, BYTE PTR tv166[rsp]
  00235	88 44 24 01	 mov	 BYTE PTR $T2[rsp], al
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_heap_algorithms.hpp

; 27   :         _Top < _Hole && _DEBUG_LT_PRED(_Pred, *(_First + _Idx), _Val); _Idx = (_Hole - 1) >> 1) { // shift for codegen

  00239	0f b6 44 24 01	 movzx	 eax, BYTE PTR $T2[rsp]
  0023e	0f b6 c0	 movzx	 eax, al
  00241	85 c0		 test	 eax, eax
  00243	74 4c		 je	 SHORT $LN28@Pop_heap_h

; 29   :         *(_First + _Hole) = _STD move(*(_First + _Idx));

  00245	48 69 44 24 28
	a8 00 00 00	 imul	 rax, QWORD PTR _Idx$3[rsp], 168 ; 000000a8H
  0024e	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR _First$[rsp]
  00256	48 03 c8	 add	 rcx, rax
  00259	48 8b c1	 mov	 rax, rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  0025c	48 89 44 24 60	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_heap_algorithms.hpp

; 29   :         *(_First + _Hole) = _STD move(*(_First + _Idx));

  00261	48 69 44 24 18
	a8 00 00 00	 imul	 rax, QWORD PTR _Hole$[rsp], 168 ; 000000a8H
  0026a	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR _First$[rsp]
  00272	48 8d 3c 01	 lea	 rdi, QWORD PTR [rcx+rax]
  00276	48 8b 74 24 60	 mov	 rsi, QWORD PTR $T7[rsp]
  0027b	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  00280	f3 a4		 rep movsb

; 30   :         _Hole             = _Idx;

  00282	48 8b 44 24 28	 mov	 rax, QWORD PTR _Idx$3[rsp]
  00287	48 89 44 24 18	 mov	 QWORD PTR _Hole$[rsp], rax

; 31   :     }

  0028c	e9 4e ff ff ff	 jmp	 $LN27@Pop_heap_h
$LN28@Pop_heap_h:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00291	48 8b 44 24 38	 mov	 rax, QWORD PTR _Val$[rsp]
  00296	48 89 44 24 68	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_heap_algorithms.hpp

; 33   :     *(_First + _Hole) = _STD forward<_Ty>(_Val); // drop _Val into final hole

  0029b	48 69 44 24 18
	a8 00 00 00	 imul	 rax, QWORD PTR _Hole$[rsp], 168 ; 000000a8H
  002a4	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR _First$[rsp]
  002ac	48 8d 3c 01	 lea	 rdi, QWORD PTR [rcx+rax]
  002b0	48 8b 74 24 68	 mov	 rsi, QWORD PTR $T8[rsp]
  002b5	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  002ba	f3 a4		 rep movsb

; 84   : }

  002bc	48 83 c4 78	 add	 rsp, 120		; 00000078H
  002c0	5f		 pop	 rdi
  002c1	5e		 pop	 rsi
  002c2	c3		 ret	 0
??$_Pop_heap_hole_by_index@PEAUEventScheduleDate@mu2@@U12@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@_J1$$QEAU12@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z ENDP ; std::_Pop_heap_hole_by_index<mu2::EventScheduleDate *,mu2::EventScheduleDate,<lambda_5acf0176913ce2eb7dca573a416bd931> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ??$_Emplace_reallocate@AEBUEventScheduleDate@mu2@@@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAPEAUEventScheduleDate@mu2@@QEAU23@AEBU23@@Z
_TEXT	SEGMENT
_Newvec$ = 32
_Al$ = 40
_Whereoff$ = 48
_Myfirst$ = 56
_Mylast$ = 64
_Newcapacity$ = 72
_My_data$ = 80
$T1 = 88
$T2 = 96
tv163 = 104
_Oldsize$ = 112
_Constructed_last$ = 120
_Constructed_first$ = 128
_Newsize$ = 136
$T3 = 144
$T4 = 152
$T5 = 160
$T6 = 168
$T7 = 176
$T8 = 184
$T9 = 192
$T10 = 200
$T11 = 208
$T12 = 216
$T13 = 224
$T14 = 232
$T15 = 240
$T16 = 248
$T17 = 256
_Guard$ = 264
$T18 = 304
_Unsigned_max$19 = 312
this$ = 352
_Whereptr$ = 360
<_Val_0>$ = 368
??$_Emplace_reallocate@AEBUEventScheduleDate@mu2@@@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAPEAUEventScheduleDate@mu2@@QEAU23@AEBU23@@Z PROC ; std::vector<mu2::EventScheduleDate,std::allocator<mu2::EventScheduleDate> >::_Emplace_reallocate<mu2::EventScheduleDate const &>, COMDAT

; 875  :     _CONSTEXPR20 pointer _Emplace_reallocate(const pointer _Whereptr, _Valty&&... _Val) {

$LN386:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	56		 push	 rsi
  00010	57		 push	 rdi
  00011	48 81 ec 48 01
	00 00		 sub	 rsp, 328		; 00000148H

; 2227 :         return _Mypair._Get_first();

  00018	48 8b 84 24 60
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00020	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2227 :         return _Mypair._Get_first();

  00028	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR $T3[rsp]
  00030	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR $T4[rsp], rax

; 876  :         // reallocate and insert by perfectly forwarding _Val at _Whereptr
; 877  :         _Alty& _Al        = _Getal();

  00038	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR $T4[rsp]
  00040	48 89 44 24 28	 mov	 QWORD PTR _Al$[rsp], rax

; 878  :         auto& _My_data    = _Mypair._Myval2;

  00045	48 8b 84 24 60
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0004d	48 89 44 24 50	 mov	 QWORD PTR _My_data$[rsp], rax

; 879  :         pointer& _Myfirst = _My_data._Myfirst;

  00052	48 8b 44 24 50	 mov	 rax, QWORD PTR _My_data$[rsp]
  00057	48 89 44 24 38	 mov	 QWORD PTR _Myfirst$[rsp], rax

; 880  :         pointer& _Mylast  = _My_data._Mylast;

  0005c	48 8b 44 24 50	 mov	 rax, QWORD PTR _My_data$[rsp]
  00061	48 83 c0 08	 add	 rax, 8
  00065	48 89 44 24 40	 mov	 QWORD PTR _Mylast$[rsp], rax

; 881  : 
; 882  :         _STL_INTERNAL_CHECK(_Mylast == _My_data._Myend); // check that we have no unused capacity
; 883  : 
; 884  :         const auto _Whereoff = static_cast<size_type>(_Whereptr - _Myfirst);

  0006a	48 8b 44 24 38	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  0006f	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00072	48 8b 8c 24 68
	01 00 00	 mov	 rcx, QWORD PTR _Whereptr$[rsp]
  0007a	48 2b c8	 sub	 rcx, rax
  0007d	48 8b c1	 mov	 rax, rcx
  00080	48 99		 cdq
  00082	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  00087	48 f7 f9	 idiv	 rcx
  0008a	48 89 44 24 30	 mov	 QWORD PTR _Whereoff$[rsp], rax

; 885  :         const auto _Oldsize  = static_cast<size_type>(_Mylast - _Myfirst);

  0008f	48 8b 44 24 40	 mov	 rax, QWORD PTR _Mylast$[rsp]
  00094	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Myfirst$[rsp]
  00099	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0009c	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0009f	48 2b c1	 sub	 rax, rcx
  000a2	48 99		 cdq
  000a4	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  000a9	48 f7 f9	 idiv	 rcx
  000ac	48 89 44 24 70	 mov	 QWORD PTR _Oldsize$[rsp], rax

; 2231 :         return _Mypair._Get_first();

  000b1	48 8b 84 24 60
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1543 :         return *this;

  000b9	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2231 :         return _Mypair._Get_first();

  000c1	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR $T5[rsp]
  000c9	48 89 84 24 30
	01 00 00	 mov	 QWORD PTR $T18[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 746  :         return static_cast<size_t>(-1) / sizeof(value_type);

  000d1	48 b8 86 61 18
	86 61 18 86 01	 mov	 rax, 109802048057794950	; 0186186186186186H
  000db	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1919 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()), _Alty_traits::max_size(_Getal()));

  000e3	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T6[rsp]
  000eb	48 89 44 24 58	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 866  :         constexpr auto _Unsigned_max = static_cast<make_unsigned_t<_Ty>>(-1);

  000f0	48 c7 84 24 38
	01 00 00 ff ff
	ff ff		 mov	 QWORD PTR _Unsigned_max$19[rsp], -1

; 867  :         return static_cast<_Ty>(_Unsigned_max >> 1);

  000fc	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  00106	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1919 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()), _Alty_traits::max_size(_Getal()));

  0010e	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR $T7[rsp]
  00116	48 89 44 24 60	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 101  :     return _Right < _Left ? _Right : _Left;

  0011b	48 8b 44 24 60	 mov	 rax, QWORD PTR $T2[rsp]
  00120	48 39 44 24 58	 cmp	 QWORD PTR $T1[rsp], rax
  00125	73 0c		 jae	 SHORT $LN44@Emplace_re
  00127	48 8d 44 24 58	 lea	 rax, QWORD PTR $T1[rsp]
  0012c	48 89 44 24 68	 mov	 QWORD PTR tv163[rsp], rax
  00131	eb 0a		 jmp	 SHORT $LN45@Emplace_re
$LN44@Emplace_re:
  00133	48 8d 44 24 60	 lea	 rax, QWORD PTR $T2[rsp]
  00138	48 89 44 24 68	 mov	 QWORD PTR tv163[rsp], rax
$LN45@Emplace_re:
  0013d	48 8b 44 24 68	 mov	 rax, QWORD PTR tv163[rsp]
  00142	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T8[rsp], rax
  0014a	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T8[rsp]
  00152	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1919 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()), _Alty_traits::max_size(_Getal()));

  0015a	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR $T9[rsp]
  00162	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00165	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax

; 886  : 
; 887  :         if (_Oldsize == max_size()) {

  0016d	48 8b 84 24 c8
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  00175	48 39 44 24 70	 cmp	 QWORD PTR _Oldsize$[rsp], rax
  0017a	75 06		 jne	 SHORT $LN2@Emplace_re

; 888  :             _Xlength();

  0017c	e8 00 00 00 00	 call	 ?_Xlength@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@CAXXZ ; std::vector<mu2::EventScheduleDate,std::allocator<mu2::EventScheduleDate> >::_Xlength
  00181	90		 npad	 1
$LN2@Emplace_re:

; 889  :         }
; 890  : 
; 891  :         const size_type _Newsize = _Oldsize + 1;

  00182	48 8b 44 24 70	 mov	 rax, QWORD PTR _Oldsize$[rsp]
  00187	48 ff c0	 inc	 rax
  0018a	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR _Newsize$[rsp], rax

; 892  :         size_type _Newcapacity   = _Calculate_growth(_Newsize);

  00192	48 8b 94 24 88
	00 00 00	 mov	 rdx, QWORD PTR _Newsize$[rsp]
  0019a	48 8b 8c 24 60
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  001a2	e8 00 00 00 00	 call	 ?_Calculate_growth@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEBA_K_K@Z ; std::vector<mu2::EventScheduleDate,std::allocator<mu2::EventScheduleDate> >::_Calculate_growth
  001a7	48 89 44 24 48	 mov	 QWORD PTR _Newcapacity$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 2303 :         return _Al.allocate(_Count);

  001ac	48 8b 54 24 48	 mov	 rdx, QWORD PTR _Newcapacity$[rsp]
  001b1	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Al$[rsp]
  001b6	e8 00 00 00 00	 call	 ?allocate@?$allocator@UEventScheduleDate@mu2@@@std@@QEAAPEAUEventScheduleDate@mu2@@_K@Z ; std::allocator<mu2::EventScheduleDate>::allocate
  001bb	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 894  :         const pointer _Newvec           = _STD _Allocate_at_least_helper(_Al, _Newcapacity);

  001c3	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  001cb	48 89 44 24 20	 mov	 QWORD PTR _Newvec$[rsp], rax

; 895  :         const pointer _Constructed_last = _Newvec + _Whereoff + 1;

  001d0	48 69 44 24 30
	a8 00 00 00	 imul	 rax, QWORD PTR _Whereoff$[rsp], 168 ; 000000a8H
  001d9	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Newvec$[rsp]
  001de	48 8d 84 01 a8
	00 00 00	 lea	 rax, QWORD PTR [rcx+rax+168]
  001e6	48 89 44 24 78	 mov	 QWORD PTR _Constructed_last$[rsp], rax

; 896  : 
; 897  :         _Reallocation_guard _Guard{_Al, _Newvec, _Newcapacity, _Constructed_last, _Constructed_last};

  001eb	48 8b 44 24 28	 mov	 rax, QWORD PTR _Al$[rsp]
  001f0	48 89 84 24 08
	01 00 00	 mov	 QWORD PTR _Guard$[rsp], rax
  001f8	48 8b 44 24 20	 mov	 rax, QWORD PTR _Newvec$[rsp]
  001fd	48 89 84 24 10
	01 00 00	 mov	 QWORD PTR _Guard$[rsp+8], rax
  00205	48 8b 44 24 48	 mov	 rax, QWORD PTR _Newcapacity$[rsp]
  0020a	48 89 84 24 18
	01 00 00	 mov	 QWORD PTR _Guard$[rsp+16], rax
  00212	48 8b 44 24 78	 mov	 rax, QWORD PTR _Constructed_last$[rsp]
  00217	48 89 84 24 20
	01 00 00	 mov	 QWORD PTR _Guard$[rsp+24], rax
  0021f	48 8b 44 24 78	 mov	 rax, QWORD PTR _Constructed_last$[rsp]
  00224	48 89 84 24 28
	01 00 00	 mov	 QWORD PTR _Guard$[rsp+32], rax

; 898  :         auto& _Constructed_first = _Guard._Constructed_first;

  0022c	48 8d 84 24 20
	01 00 00	 lea	 rax, QWORD PTR _Guard$[rsp+24]
  00234	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _Constructed_first$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  0023c	48 8b 84 24 70
	01 00 00	 mov	 rax, QWORD PTR <_Val_0>$[rsp]
  00244	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR $T14[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 900  :         _Alty_traits::construct(_Al, _STD _Unfancy(_Newvec + _Whereoff), _STD forward<_Valty>(_Val)...);

  0024c	48 69 44 24 30
	a8 00 00 00	 imul	 rax, QWORD PTR _Whereoff$[rsp], 168 ; 000000a8H
  00255	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Newvec$[rsp]
  0025a	48 03 c8	 add	 rcx, rax
  0025d	48 8b c1	 mov	 rax, rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  00260	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 900  :         _Alty_traits::construct(_Al, _STD _Unfancy(_Newvec + _Whereoff), _STD forward<_Valty>(_Val)...);

  00268	48 8b 84 24 d8
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  00270	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 732  :         ::new (const_cast<void*>(static_cast<const volatile void*>(_Ptr))) _Objty(_STD forward<_Types>(_Args)...);

  00278	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR $T13[rsp]
  00280	48 89 84 24 f0
	00 00 00	 mov	 QWORD PTR $T15[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 900  :         _Alty_traits::construct(_Al, _STD _Unfancy(_Newvec + _Whereoff), _STD forward<_Valty>(_Val)...);

  00288	48 8b 84 24 e8
	00 00 00	 mov	 rax, QWORD PTR $T14[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00290	48 89 84 24 f8
	00 00 00	 mov	 QWORD PTR $T16[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 732  :         ::new (const_cast<void*>(static_cast<const volatile void*>(_Ptr))) _Objty(_STD forward<_Types>(_Args)...);

  00298	48 8b bc 24 f0
	00 00 00	 mov	 rdi, QWORD PTR $T15[rsp]
  002a0	48 8b b4 24 f8
	00 00 00	 mov	 rsi, QWORD PTR $T16[rsp]
  002a8	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  002ad	f3 a4		 rep movsb
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 901  :         _Constructed_first = _Newvec + _Whereoff;

  002af	48 69 44 24 30
	a8 00 00 00	 imul	 rax, QWORD PTR _Whereoff$[rsp], 168 ; 000000a8H
  002b8	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Newvec$[rsp]
  002bd	48 03 c8	 add	 rcx, rax
  002c0	48 8b c1	 mov	 rax, rcx
  002c3	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR _Constructed_first$[rsp]
  002cb	48 89 01	 mov	 QWORD PTR [rcx], rax

; 902  : 
; 903  :         if (_Whereptr == _Mylast) { // at back, provide strong guarantee

  002ce	48 8b 44 24 40	 mov	 rax, QWORD PTR _Mylast$[rsp]
  002d3	48 8b 00	 mov	 rax, QWORD PTR [rax]
  002d6	48 39 84 24 68
	01 00 00	 cmp	 QWORD PTR _Whereptr$[rsp], rax
  002de	75 22		 jne	 SHORT $LN3@Emplace_re

; 904  :             if constexpr (is_nothrow_move_constructible_v<_Ty> || !is_copy_constructible_v<_Ty>) {
; 905  :                 _STD _Uninitialized_move(_Myfirst, _Mylast, _Newvec, _Al);

  002e0	4c 8b 4c 24 28	 mov	 r9, QWORD PTR _Al$[rsp]
  002e5	4c 8b 44 24 20	 mov	 r8, QWORD PTR _Newvec$[rsp]
  002ea	48 8b 44 24 40	 mov	 rax, QWORD PTR _Mylast$[rsp]
  002ef	48 8b 10	 mov	 rdx, QWORD PTR [rax]
  002f2	48 8b 44 24 38	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  002f7	48 8b 08	 mov	 rcx, QWORD PTR [rax]
  002fa	e8 00 00 00 00	 call	 ??$_Uninitialized_move@PEAUEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@YAPEAUEventScheduleDate@mu2@@QEAU12@0PEAU12@AEAV?$allocator@UEventScheduleDate@mu2@@@0@@Z ; std::_Uninitialized_move<mu2::EventScheduleDate *,std::allocator<mu2::EventScheduleDate> >
  002ff	90		 npad	 1

; 906  :             } else {
; 907  :                 _STD _Uninitialized_copy(_Myfirst, _Mylast, _Newvec, _Al);
; 908  :             }
; 909  :         } else { // provide basic guarantee

  00300	eb 63		 jmp	 SHORT $LN4@Emplace_re
$LN3@Emplace_re:

; 910  :             _STD _Uninitialized_move(_Myfirst, _Whereptr, _Newvec, _Al);

  00302	4c 8b 4c 24 28	 mov	 r9, QWORD PTR _Al$[rsp]
  00307	4c 8b 44 24 20	 mov	 r8, QWORD PTR _Newvec$[rsp]
  0030c	48 8b 94 24 68
	01 00 00	 mov	 rdx, QWORD PTR _Whereptr$[rsp]
  00314	48 8b 44 24 38	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  00319	48 8b 08	 mov	 rcx, QWORD PTR [rax]
  0031c	e8 00 00 00 00	 call	 ??$_Uninitialized_move@PEAUEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@YAPEAUEventScheduleDate@mu2@@QEAU12@0PEAU12@AEAV?$allocator@UEventScheduleDate@mu2@@@0@@Z ; std::_Uninitialized_move<mu2::EventScheduleDate *,std::allocator<mu2::EventScheduleDate> >

; 911  :             _Constructed_first = _Newvec;

  00321	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _Constructed_first$[rsp]
  00329	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Newvec$[rsp]
  0032e	48 89 08	 mov	 QWORD PTR [rax], rcx

; 912  :             _STD _Uninitialized_move(_Whereptr, _Mylast, _Newvec + _Whereoff + 1, _Al);

  00331	48 69 44 24 30
	a8 00 00 00	 imul	 rax, QWORD PTR _Whereoff$[rsp], 168 ; 000000a8H
  0033a	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Newvec$[rsp]
  0033f	48 8d 84 01 a8
	00 00 00	 lea	 rax, QWORD PTR [rcx+rax+168]
  00347	4c 8b 4c 24 28	 mov	 r9, QWORD PTR _Al$[rsp]
  0034c	4c 8b c0	 mov	 r8, rax
  0034f	48 8b 44 24 40	 mov	 rax, QWORD PTR _Mylast$[rsp]
  00354	48 8b 10	 mov	 rdx, QWORD PTR [rax]
  00357	48 8b 8c 24 68
	01 00 00	 mov	 rcx, QWORD PTR _Whereptr$[rsp]
  0035f	e8 00 00 00 00	 call	 ??$_Uninitialized_move@PEAUEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@YAPEAUEventScheduleDate@mu2@@QEAU12@0PEAU12@AEAV?$allocator@UEventScheduleDate@mu2@@@0@@Z ; std::_Uninitialized_move<mu2::EventScheduleDate *,std::allocator<mu2::EventScheduleDate> >
  00364	90		 npad	 1
$LN4@Emplace_re:

; 913  :         }
; 914  : 
; 915  :         _Guard._New_begin = nullptr;

  00365	48 c7 84 24 10
	01 00 00 00 00
	00 00		 mov	 QWORD PTR _Guard$[rsp+8], 0

; 916  :         _Change_array(_Newvec, _Newsize, _Newcapacity);

  00371	4c 8b 4c 24 48	 mov	 r9, QWORD PTR _Newcapacity$[rsp]
  00376	4c 8b 84 24 88
	00 00 00	 mov	 r8, QWORD PTR _Newsize$[rsp]
  0037e	48 8b 54 24 20	 mov	 rdx, QWORD PTR _Newvec$[rsp]
  00383	48 8b 8c 24 60
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0038b	e8 00 00 00 00	 call	 ?_Change_array@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAXQEAUEventScheduleDate@mu2@@_K1@Z ; std::vector<mu2::EventScheduleDate,std::allocator<mu2::EventScheduleDate> >::_Change_array
  00390	90		 npad	 1

; 917  :         return _Newvec + _Whereoff;

  00391	48 69 44 24 30
	a8 00 00 00	 imul	 rax, QWORD PTR _Whereoff$[rsp], 168 ; 000000a8H
  0039a	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Newvec$[rsp]
  0039f	48 03 c8	 add	 rcx, rax
  003a2	48 8b c1	 mov	 rax, rcx
  003a5	48 89 84 24 00
	01 00 00	 mov	 QWORD PTR $T17[rsp], rax
  003ad	48 8d 8c 24 08
	01 00 00	 lea	 rcx, QWORD PTR _Guard$[rsp]
  003b5	e8 00 00 00 00	 call	 ??1_Reallocation_guard@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@QEAA@XZ ; std::vector<mu2::EventScheduleDate,std::allocator<mu2::EventScheduleDate> >::_Reallocation_guard::~_Reallocation_guard
  003ba	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR $T17[rsp]

; 918  :     }

  003c2	48 81 c4 48 01
	00 00		 add	 rsp, 328		; 00000148H
  003c9	5f		 pop	 rdi
  003ca	5e		 pop	 rsi
  003cb	c3		 ret	 0
$LN385@Emplace_re:
??$_Emplace_reallocate@AEBUEventScheduleDate@mu2@@@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAPEAUEventScheduleDate@mu2@@QEAU23@AEBU23@@Z ENDP ; std::vector<mu2::EventScheduleDate,std::allocator<mu2::EventScheduleDate> >::_Emplace_reallocate<mu2::EventScheduleDate const &>
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
_Newvec$ = 32
_Al$ = 40
_Whereoff$ = 48
_Myfirst$ = 56
_Mylast$ = 64
_Newcapacity$ = 72
_My_data$ = 80
$T1 = 88
$T2 = 96
tv163 = 104
_Oldsize$ = 112
_Constructed_last$ = 120
_Constructed_first$ = 128
_Newsize$ = 136
$T3 = 144
$T4 = 152
$T5 = 160
$T6 = 168
$T7 = 176
$T8 = 184
$T9 = 192
$T10 = 200
$T11 = 208
$T12 = 216
$T13 = 224
$T14 = 232
$T15 = 240
$T16 = 248
$T17 = 256
_Guard$ = 264
$T18 = 304
_Unsigned_max$19 = 312
this$ = 352
_Whereptr$ = 360
<_Val_0>$ = 368
?dtor$0@?0???$_Emplace_reallocate@AEBUEventScheduleDate@mu2@@@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAPEAUEventScheduleDate@mu2@@QEAU23@AEBU23@@Z@4HA PROC ; `std::vector<mu2::EventScheduleDate,std::allocator<mu2::EventScheduleDate> >::_Emplace_reallocate<mu2::EventScheduleDate const &>'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8d 8d 08 01
	00 00		 lea	 rcx, QWORD PTR _Guard$[rbp]
  00010	e8 00 00 00 00	 call	 ??1_Reallocation_guard@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@QEAA@XZ ; std::vector<mu2::EventScheduleDate,std::allocator<mu2::EventScheduleDate> >::_Reallocation_guard::~_Reallocation_guard
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$0@?0???$_Emplace_reallocate@AEBUEventScheduleDate@mu2@@@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAPEAUEventScheduleDate@mu2@@QEAU23@AEBU23@@Z@4HA ENDP ; `std::vector<mu2::EventScheduleDate,std::allocator<mu2::EventScheduleDate> >::_Emplace_reallocate<mu2::EventScheduleDate const &>'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
;	COMDAT ??$_Partition_by_median_guess_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YA?AU?$pair@PEAUEventScheduleDate@mu2@@PEAU12@@0@PEAUEventScheduleDate@mu2@@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z
_TEXT	SEGMENT
_Pfirst$ = 32
_Plast$ = 40
_Gfirst$ = 48
$T1 = 56
$T2 = 57
$T3 = 58
$T4 = 59
$T5 = 60
$T6 = 61
$T7 = 62
$T8 = 63
_Glast$ = 64
tv243 = 72
tv241 = 76
tv239 = 80
tv237 = 84
tv235 = 88
tv232 = 92
tv227 = 96
tv245 = 100
_Glast_prev$9 = 104
_First$ = 112
_First$ = 120
_First$ = 128
_First$ = 136
_First$ = 144
_Mid$ = 152
_Left$ = 160
_Left$ = 168
_Left$ = 176
_Right$ = 184
_Left$ = 192
_Right$ = 200
_Left$ = 208
_Right$ = 216
$T10 = 224
$T11 = 232
$T12 = 240
$T13 = 248
$T14 = 256
$T15 = 264
$T16 = 272
$T17 = 280
$T18 = 288
$T19 = 296
$T20 = 304
$T21 = 312
$T22 = 320
$T23 = 328
$T24 = 336
$T25 = 344
$T26 = 352
$T27 = 360
$T28 = 368
$T29 = 376
$T30 = 384
$T31 = 392
$T32 = 400
$T33 = 408
$T34 = 416
$T35 = 424
$T36 = 432
$T37 = 440
_Tmp$38 = 448
_Tmp$39 = 624
_Tmp$40 = 800
_Tmp$41 = 976
_Tmp$42 = 1152
_Tmp$43 = 1328
_Tmp$44 = 1504
__$ArrayPad$ = 1680
__$ReturnUdt$ = 1728
_First$ = 1736
_Last$ = 1744
_Pred$ = 1752
??$_Partition_by_median_guess_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YA?AU?$pair@PEAUEventScheduleDate@mu2@@PEAU12@@0@PEAUEventScheduleDate@mu2@@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z PROC ; std::_Partition_by_median_guess_unchecked<mu2::EventScheduleDate *,<lambda_5acf0176913ce2eb7dca573a416bd931> >, COMDAT

; 8298 : _CONSTEXPR20 pair<_RanIt, _RanIt> _Partition_by_median_guess_unchecked(_RanIt _First, _RanIt _Last, _Pr _Pred) {

  00000	44 88 4c 24 20	 mov	 BYTE PTR [rsp+32], r9b
  00005	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	56		 push	 rsi
  00015	57		 push	 rdi
  00016	48 81 ec a8 06
	00 00		 sub	 rsp, 1704		; 000006a8H
  0001d	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR __security_cookie
  00024	48 33 c4	 xor	 rax, rsp
  00027	48 89 84 24 90
	06 00 00	 mov	 QWORD PTR __$ArrayPad$[rsp], rax

; 8299 :     // partition [_First, _Last)
; 8300 :     _RanIt _Mid = _First + ((_Last - _First) >> 1); // shift for codegen

  0002f	48 8b 84 24 c8
	06 00 00	 mov	 rax, QWORD PTR _First$[rsp]
  00037	48 8b 8c 24 d0
	06 00 00	 mov	 rcx, QWORD PTR _Last$[rsp]
  0003f	48 2b c8	 sub	 rcx, rax
  00042	48 8b c1	 mov	 rax, rcx
  00045	48 99		 cdq
  00047	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  0004c	48 f7 f9	 idiv	 rcx
  0004f	48 d1 f8	 sar	 rax, 1
  00052	48 69 c0 a8 00
	00 00		 imul	 rax, rax, 168		; 000000a8H
  00059	48 8b 8c 24 c8
	06 00 00	 mov	 rcx, QWORD PTR _First$[rsp]
  00061	48 03 c8	 add	 rcx, rax
  00064	48 8b c1	 mov	 rax, rcx
  00067	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR _Mid$[rsp], rax

; 8301 :     _STD _Guess_median_unchecked(_First, _Mid, _STD _Prev_iter(_Last), _Pred);

  0006f	48 8b 84 24 d0
	06 00 00	 mov	 rax, QWORD PTR _Last$[rsp]
  00077	48 89 44 24 70	 mov	 QWORD PTR _First$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 1692 :     return --_First;

  0007c	48 8b 44 24 70	 mov	 rax, QWORD PTR _First$[rsp]
  00081	48 2d a8 00 00
	00		 sub	 rax, 168		; 000000a8H
  00087	48 89 44 24 70	 mov	 QWORD PTR _First$[rsp], rax
  0008c	48 8b 44 24 70	 mov	 rax, QWORD PTR _First$[rsp]
  00091	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm

; 8301 :     _STD _Guess_median_unchecked(_First, _Mid, _STD _Prev_iter(_Last), _Pred);

  00099	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  000a1	44 0f b6 8c 24
	d8 06 00 00	 movzx	 r9d, BYTE PTR _Pred$[rsp]
  000aa	4c 8b c0	 mov	 r8, rax
  000ad	48 8b 94 24 98
	00 00 00	 mov	 rdx, QWORD PTR _Mid$[rsp]
  000b5	48 8b 8c 24 c8
	06 00 00	 mov	 rcx, QWORD PTR _First$[rsp]
  000bd	e8 00 00 00 00	 call	 ??$_Guess_median_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@00V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z ; std::_Guess_median_unchecked<mu2::EventScheduleDate *,<lambda_5acf0176913ce2eb7dca573a416bd931> >

; 8302 :     _RanIt _Pfirst = _Mid;

  000c2	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR _Mid$[rsp]
  000ca	48 89 44 24 20	 mov	 QWORD PTR _Pfirst$[rsp], rax

; 8303 :     _RanIt _Plast  = _STD _Next_iter(_Pfirst);

  000cf	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pfirst$[rsp]
  000d4	48 89 44 24 78	 mov	 QWORD PTR _First$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 1678 :     return ++_First;

  000d9	48 8b 44 24 78	 mov	 rax, QWORD PTR _First$[rsp]
  000de	48 05 a8 00 00
	00		 add	 rax, 168		; 000000a8H
  000e4	48 89 44 24 78	 mov	 QWORD PTR _First$[rsp], rax
  000e9	48 8b 44 24 78	 mov	 rax, QWORD PTR _First$[rsp]
  000ee	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm

; 8303 :     _RanIt _Plast  = _STD _Next_iter(_Pfirst);

  000f6	48 8b 84 24 e8
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  000fe	48 89 44 24 28	 mov	 QWORD PTR _Plast$[rsp], rax
$LN2@Partition_:

; 8306 :            && !_Pred(*_Pfirst, *_STD _Prev_iter(_Pfirst))) {

  00103	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pfirst$[rsp]
  00108	48 39 84 24 c8
	06 00 00	 cmp	 QWORD PTR _First$[rsp], rax
  00110	0f 83 f7 00 00
	00		 jae	 $LN4@Partition_
  00116	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pfirst$[rsp]
  0011b	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _First$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 1692 :     return --_First;

  00123	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _First$[rsp]
  0012b	48 2d a8 00 00
	00		 sub	 rax, 168		; 000000a8H
  00131	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _First$[rsp], rax
  00139	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _First$[rsp]
  00141	48 89 84 24 f0
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 229  : 			return lhs.readyTime < rhs.readyTime;

  00149	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
  00151	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Pfirst$[rsp]
  00156	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00159	48 39 08	 cmp	 QWORD PTR [rax], rcx
  0015c	7d 0a		 jge	 SHORT $LN46@Partition_
  0015e	c7 44 24 64 01
	00 00 00	 mov	 DWORD PTR tv245[rsp], 1
  00166	eb 08		 jmp	 SHORT $LN47@Partition_
$LN46@Partition_:
  00168	c7 44 24 64 00
	00 00 00	 mov	 DWORD PTR tv245[rsp], 0
$LN47@Partition_:
  00170	0f b6 44 24 64	 movzx	 eax, BYTE PTR tv245[rsp]
  00175	88 44 24 3e	 mov	 BYTE PTR $T7[rsp], al
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm

; 8306 :            && !_Pred(*_Pfirst, *_STD _Prev_iter(_Pfirst))) {

  00179	0f b6 44 24 3e	 movzx	 eax, BYTE PTR $T7[rsp]
  0017e	0f b6 c0	 movzx	 eax, al
  00181	85 c0		 test	 eax, eax
  00183	0f 85 84 00 00
	00		 jne	 $LN4@Partition_
  00189	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pfirst$[rsp]
  0018e	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR _First$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 1692 :     return --_First;

  00196	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR _First$[rsp]
  0019e	48 2d a8 00 00
	00		 sub	 rax, 168		; 000000a8H
  001a4	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR _First$[rsp], rax
  001ac	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR _First$[rsp]
  001b4	48 89 84 24 f8
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 229  : 			return lhs.readyTime < rhs.readyTime;

  001bc	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pfirst$[rsp]
  001c1	48 8b 8c 24 f8
	00 00 00	 mov	 rcx, QWORD PTR $T13[rsp]
  001c9	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  001cc	48 39 08	 cmp	 QWORD PTR [rax], rcx
  001cf	7d 0a		 jge	 SHORT $LN52@Partition_
  001d1	c7 44 24 48 01
	00 00 00	 mov	 DWORD PTR tv243[rsp], 1
  001d9	eb 08		 jmp	 SHORT $LN53@Partition_
$LN52@Partition_:
  001db	c7 44 24 48 00
	00 00 00	 mov	 DWORD PTR tv243[rsp], 0
$LN53@Partition_:
  001e3	0f b6 44 24 48	 movzx	 eax, BYTE PTR tv243[rsp]
  001e8	88 44 24 3f	 mov	 BYTE PTR $T8[rsp], al
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm

; 8306 :            && !_Pred(*_Pfirst, *_STD _Prev_iter(_Pfirst))) {

  001ec	0f b6 44 24 3f	 movzx	 eax, BYTE PTR $T8[rsp]
  001f1	0f b6 c0	 movzx	 eax, al
  001f4	85 c0		 test	 eax, eax
  001f6	75 15		 jne	 SHORT $LN4@Partition_

; 8307 :         --_Pfirst;

  001f8	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pfirst$[rsp]
  001fd	48 2d a8 00 00
	00		 sub	 rax, 168		; 000000a8H
  00203	48 89 44 24 20	 mov	 QWORD PTR _Pfirst$[rsp], rax

; 8308 :     }

  00208	e9 f6 fe ff ff	 jmp	 $LN2@Partition_
$LN4@Partition_:

; 8310 :     while (_Plast < _Last && !_DEBUG_LT_PRED(_Pred, *_Plast, *_Pfirst) && !_Pred(*_Pfirst, *_Plast)) {

  0020d	48 8b 84 24 d0
	06 00 00	 mov	 rax, QWORD PTR _Last$[rsp]
  00215	48 39 44 24 28	 cmp	 QWORD PTR _Plast$[rsp], rax
  0021a	0f 83 87 00 00
	00		 jae	 $LN5@Partition_
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 229  : 			return lhs.readyTime < rhs.readyTime;

  00220	48 8b 44 24 28	 mov	 rax, QWORD PTR _Plast$[rsp]
  00225	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Pfirst$[rsp]
  0022a	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0022d	48 39 08	 cmp	 QWORD PTR [rax], rcx
  00230	7d 0a		 jge	 SHORT $LN56@Partition_
  00232	c7 44 24 4c 01
	00 00 00	 mov	 DWORD PTR tv241[rsp], 1
  0023a	eb 08		 jmp	 SHORT $LN57@Partition_
$LN56@Partition_:
  0023c	c7 44 24 4c 00
	00 00 00	 mov	 DWORD PTR tv241[rsp], 0
$LN57@Partition_:
  00244	0f b6 44 24 4c	 movzx	 eax, BYTE PTR tv241[rsp]
  00249	88 44 24 38	 mov	 BYTE PTR $T1[rsp], al
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm

; 8310 :     while (_Plast < _Last && !_DEBUG_LT_PRED(_Pred, *_Plast, *_Pfirst) && !_Pred(*_Pfirst, *_Plast)) {

  0024d	0f b6 44 24 38	 movzx	 eax, BYTE PTR $T1[rsp]
  00252	0f b6 c0	 movzx	 eax, al
  00255	85 c0		 test	 eax, eax
  00257	75 4e		 jne	 SHORT $LN5@Partition_
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 229  : 			return lhs.readyTime < rhs.readyTime;

  00259	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pfirst$[rsp]
  0025e	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Plast$[rsp]
  00263	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00266	48 39 08	 cmp	 QWORD PTR [rax], rcx
  00269	7d 0a		 jge	 SHORT $LN60@Partition_
  0026b	c7 44 24 50 01
	00 00 00	 mov	 DWORD PTR tv239[rsp], 1
  00273	eb 08		 jmp	 SHORT $LN61@Partition_
$LN60@Partition_:
  00275	c7 44 24 50 00
	00 00 00	 mov	 DWORD PTR tv239[rsp], 0
$LN61@Partition_:
  0027d	0f b6 44 24 50	 movzx	 eax, BYTE PTR tv239[rsp]
  00282	88 44 24 39	 mov	 BYTE PTR $T2[rsp], al
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm

; 8310 :     while (_Plast < _Last && !_DEBUG_LT_PRED(_Pred, *_Plast, *_Pfirst) && !_Pred(*_Pfirst, *_Plast)) {

  00286	0f b6 44 24 39	 movzx	 eax, BYTE PTR $T2[rsp]
  0028b	0f b6 c0	 movzx	 eax, al
  0028e	85 c0		 test	 eax, eax
  00290	75 15		 jne	 SHORT $LN5@Partition_

; 8311 :         ++_Plast;

  00292	48 8b 44 24 28	 mov	 rax, QWORD PTR _Plast$[rsp]
  00297	48 05 a8 00 00
	00		 add	 rax, 168		; 000000a8H
  0029d	48 89 44 24 28	 mov	 QWORD PTR _Plast$[rsp], rax

; 8312 :     }

  002a2	e9 66 ff ff ff	 jmp	 $LN4@Partition_
$LN5@Partition_:

; 8313 : 
; 8314 :     _RanIt _Gfirst = _Plast;

  002a7	48 8b 44 24 28	 mov	 rax, QWORD PTR _Plast$[rsp]
  002ac	48 89 44 24 30	 mov	 QWORD PTR _Gfirst$[rsp], rax

; 8315 :     _RanIt _Glast  = _Pfirst;

  002b1	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pfirst$[rsp]
  002b6	48 89 44 24 40	 mov	 QWORD PTR _Glast$[rsp], rax
$LN6@Partition_:

; 8316 : 
; 8317 :     for (;;) { // partition
; 8318 :         for (; _Gfirst < _Last; ++_Gfirst) {

  002bb	eb 10		 jmp	 SHORT $LN11@Partition_
$LN9@Partition_:
  002bd	48 8b 44 24 30	 mov	 rax, QWORD PTR _Gfirst$[rsp]
  002c2	48 05 a8 00 00
	00		 add	 rax, 168		; 000000a8H
  002c8	48 89 44 24 30	 mov	 QWORD PTR _Gfirst$[rsp], rax
$LN11@Partition_:
  002cd	48 8b 84 24 d0
	06 00 00	 mov	 rax, QWORD PTR _Last$[rsp]
  002d5	48 39 44 24 30	 cmp	 QWORD PTR _Gfirst$[rsp], rax
  002da	0f 83 39 01 00
	00		 jae	 $LN10@Partition_
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 229  : 			return lhs.readyTime < rhs.readyTime;

  002e0	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pfirst$[rsp]
  002e5	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Gfirst$[rsp]
  002ea	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  002ed	48 39 08	 cmp	 QWORD PTR [rax], rcx
  002f0	7d 0a		 jge	 SHORT $LN64@Partition_
  002f2	c7 44 24 54 01
	00 00 00	 mov	 DWORD PTR tv237[rsp], 1
  002fa	eb 08		 jmp	 SHORT $LN65@Partition_
$LN64@Partition_:
  002fc	c7 44 24 54 00
	00 00 00	 mov	 DWORD PTR tv237[rsp], 0
$LN65@Partition_:
  00304	0f b6 44 24 54	 movzx	 eax, BYTE PTR tv237[rsp]
  00309	88 44 24 3a	 mov	 BYTE PTR $T3[rsp], al
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm

; 8319 :             if (_DEBUG_LT_PRED(_Pred, *_Pfirst, *_Gfirst)) {

  0030d	0f b6 44 24 3a	 movzx	 eax, BYTE PTR $T3[rsp]
  00312	0f b6 c0	 movzx	 eax, al
  00315	85 c0		 test	 eax, eax
  00317	74 07		 je	 SHORT $LN15@Partition_

; 8320 :                 continue;

  00319	eb a2		 jmp	 SHORT $LN9@Partition_

; 8321 :             } else if (_Pred(*_Gfirst, *_Pfirst)) {

  0031b	e9 f4 00 00 00	 jmp	 $LN16@Partition_
$LN15@Partition_:
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 229  : 			return lhs.readyTime < rhs.readyTime;

  00320	48 8b 44 24 30	 mov	 rax, QWORD PTR _Gfirst$[rsp]
  00325	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Pfirst$[rsp]
  0032a	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0032d	48 39 08	 cmp	 QWORD PTR [rax], rcx
  00330	7d 0a		 jge	 SHORT $LN68@Partition_
  00332	c7 44 24 58 01
	00 00 00	 mov	 DWORD PTR tv235[rsp], 1
  0033a	eb 08		 jmp	 SHORT $LN69@Partition_
$LN68@Partition_:
  0033c	c7 44 24 58 00
	00 00 00	 mov	 DWORD PTR tv235[rsp], 0
$LN69@Partition_:
  00344	0f b6 44 24 58	 movzx	 eax, BYTE PTR tv235[rsp]
  00349	88 44 24 3b	 mov	 BYTE PTR $T4[rsp], al
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm

; 8321 :             } else if (_Pred(*_Gfirst, *_Pfirst)) {

  0034d	0f b6 44 24 3b	 movzx	 eax, BYTE PTR $T4[rsp]
  00352	0f b6 c0	 movzx	 eax, al
  00355	85 c0		 test	 eax, eax
  00357	74 0a		 je	 SHORT $LN17@Partition_

; 8322 :                 break;

  00359	e9 bb 00 00 00	 jmp	 $LN10@Partition_
  0035e	e9 b1 00 00 00	 jmp	 $LN16@Partition_
$LN17@Partition_:

; 8323 :             } else if (_Plast != _Gfirst) {

  00363	48 8b 44 24 30	 mov	 rax, QWORD PTR _Gfirst$[rsp]
  00368	48 39 44 24 28	 cmp	 QWORD PTR _Plast$[rsp], rax
  0036d	0f 84 91 00 00
	00		 je	 $LN19@Partition_

; 8324 :                 swap(*_Plast, *_Gfirst); // intentional ADL

  00373	48 8b 44 24 28	 mov	 rax, QWORD PTR _Plast$[rsp]
  00378	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR _Left$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00380	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR _Left$[rsp]
  00388	48 89 84 24 00
	01 00 00	 mov	 QWORD PTR $T14[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 139  :     _Ty _Tmp = _STD move(_Left);

  00390	48 8d 84 24 c0
	01 00 00	 lea	 rax, QWORD PTR _Tmp$38[rsp]
  00398	48 8b f8	 mov	 rdi, rax
  0039b	48 8b b4 24 00
	01 00 00	 mov	 rsi, QWORD PTR $T14[rsp]
  003a3	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  003a8	f3 a4		 rep movsb
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  003aa	48 8b 44 24 30	 mov	 rax, QWORD PTR _Gfirst$[rsp]
  003af	48 89 84 24 08
	01 00 00	 mov	 QWORD PTR $T15[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 140  :     _Left    = _STD move(_Right);

  003b7	48 8b bc 24 a0
	00 00 00	 mov	 rdi, QWORD PTR _Left$[rsp]
  003bf	48 8b b4 24 08
	01 00 00	 mov	 rsi, QWORD PTR $T15[rsp]
  003c7	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  003cc	f3 a4		 rep movsb
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  003ce	48 8d 84 24 c0
	01 00 00	 lea	 rax, QWORD PTR _Tmp$38[rsp]
  003d6	48 89 84 24 10
	01 00 00	 mov	 QWORD PTR $T16[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 141  :     _Right   = _STD move(_Tmp);

  003de	48 8b 7c 24 30	 mov	 rdi, QWORD PTR _Gfirst$[rsp]
  003e3	48 8b b4 24 10
	01 00 00	 mov	 rsi, QWORD PTR $T16[rsp]
  003eb	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  003f0	f3 a4		 rep movsb
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm

; 8325 :                 ++_Plast;

  003f2	48 8b 44 24 28	 mov	 rax, QWORD PTR _Plast$[rsp]
  003f7	48 05 a8 00 00
	00		 add	 rax, 168		; 000000a8H
  003fd	48 89 44 24 28	 mov	 QWORD PTR _Plast$[rsp], rax

; 8326 :             } else {

  00402	eb 10		 jmp	 SHORT $LN16@Partition_
$LN19@Partition_:

; 8327 :                 ++_Plast;

  00404	48 8b 44 24 28	 mov	 rax, QWORD PTR _Plast$[rsp]
  00409	48 05 a8 00 00
	00		 add	 rax, 168		; 000000a8H
  0040f	48 89 44 24 28	 mov	 QWORD PTR _Plast$[rsp], rax
$LN16@Partition_:

; 8328 :             }
; 8329 :         }

  00414	e9 a4 fe ff ff	 jmp	 $LN9@Partition_
$LN10@Partition_:

; 8330 : 
; 8331 :         for (; _First < _Glast; --_Glast) {

  00419	eb 10		 jmp	 SHORT $LN14@Partition_
$LN12@Partition_:
  0041b	48 8b 44 24 40	 mov	 rax, QWORD PTR _Glast$[rsp]
  00420	48 2d a8 00 00
	00		 sub	 rax, 168		; 000000a8H
  00426	48 89 44 24 40	 mov	 QWORD PTR _Glast$[rsp], rax
$LN14@Partition_:
  0042b	48 8b 44 24 40	 mov	 rax, QWORD PTR _Glast$[rsp]
  00430	48 39 84 24 c8
	06 00 00	 cmp	 QWORD PTR _First$[rsp], rax
  00438	0f 83 66 01 00
	00		 jae	 $LN13@Partition_

; 8332 :             const auto _Glast_prev = _STD _Prev_iter(_Glast);

  0043e	48 8b 44 24 40	 mov	 rax, QWORD PTR _Glast$[rsp]
  00443	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR _First$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 1692 :     return --_First;

  0044b	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR _First$[rsp]
  00453	48 2d a8 00 00
	00		 sub	 rax, 168		; 000000a8H
  00459	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR _First$[rsp], rax
  00461	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR _First$[rsp]
  00469	48 89 84 24 18
	01 00 00	 mov	 QWORD PTR $T17[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm

; 8332 :             const auto _Glast_prev = _STD _Prev_iter(_Glast);

  00471	48 8b 84 24 18
	01 00 00	 mov	 rax, QWORD PTR $T17[rsp]
  00479	48 89 44 24 68	 mov	 QWORD PTR _Glast_prev$9[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 229  : 			return lhs.readyTime < rhs.readyTime;

  0047e	48 8b 44 24 68	 mov	 rax, QWORD PTR _Glast_prev$9[rsp]
  00483	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Pfirst$[rsp]
  00488	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0048b	48 39 08	 cmp	 QWORD PTR [rax], rcx
  0048e	7d 0a		 jge	 SHORT $LN94@Partition_
  00490	c7 44 24 5c 01
	00 00 00	 mov	 DWORD PTR tv232[rsp], 1
  00498	eb 08		 jmp	 SHORT $LN95@Partition_
$LN94@Partition_:
  0049a	c7 44 24 5c 00
	00 00 00	 mov	 DWORD PTR tv232[rsp], 0
$LN95@Partition_:
  004a2	0f b6 44 24 5c	 movzx	 eax, BYTE PTR tv232[rsp]
  004a7	88 44 24 3c	 mov	 BYTE PTR $T5[rsp], al
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm

; 8333 :             if (_DEBUG_LT_PRED(_Pred, *_Glast_prev, *_Pfirst)) {

  004ab	0f b6 44 24 3c	 movzx	 eax, BYTE PTR $T5[rsp]
  004b0	0f b6 c0	 movzx	 eax, al
  004b3	85 c0		 test	 eax, eax
  004b5	74 0a		 je	 SHORT $LN21@Partition_

; 8334 :                 continue;

  004b7	e9 5f ff ff ff	 jmp	 $LN12@Partition_

; 8335 :             } else if (_Pred(*_Pfirst, *_Glast_prev)) {

  004bc	e9 de 00 00 00	 jmp	 $LN22@Partition_
$LN21@Partition_:
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 229  : 			return lhs.readyTime < rhs.readyTime;

  004c1	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pfirst$[rsp]
  004c6	48 8b 4c 24 68	 mov	 rcx, QWORD PTR _Glast_prev$9[rsp]
  004cb	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  004ce	48 39 08	 cmp	 QWORD PTR [rax], rcx
  004d1	7d 0a		 jge	 SHORT $LN98@Partition_
  004d3	c7 44 24 60 01
	00 00 00	 mov	 DWORD PTR tv227[rsp], 1
  004db	eb 08		 jmp	 SHORT $LN99@Partition_
$LN98@Partition_:
  004dd	c7 44 24 60 00
	00 00 00	 mov	 DWORD PTR tv227[rsp], 0
$LN99@Partition_:
  004e5	0f b6 44 24 60	 movzx	 eax, BYTE PTR tv227[rsp]
  004ea	88 44 24 3d	 mov	 BYTE PTR $T6[rsp], al
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm

; 8335 :             } else if (_Pred(*_Pfirst, *_Glast_prev)) {

  004ee	0f b6 44 24 3d	 movzx	 eax, BYTE PTR $T6[rsp]
  004f3	0f b6 c0	 movzx	 eax, al
  004f6	85 c0		 test	 eax, eax
  004f8	74 0a		 je	 SHORT $LN23@Partition_

; 8336 :                 break;

  004fa	e9 a5 00 00 00	 jmp	 $LN13@Partition_
  004ff	e9 9b 00 00 00	 jmp	 $LN22@Partition_
$LN23@Partition_:

; 8337 :             } else if (--_Pfirst != _Glast_prev) {

  00504	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pfirst$[rsp]
  00509	48 2d a8 00 00
	00		 sub	 rax, 168		; 000000a8H
  0050f	48 89 44 24 20	 mov	 QWORD PTR _Pfirst$[rsp], rax
  00514	48 8b 44 24 68	 mov	 rax, QWORD PTR _Glast_prev$9[rsp]
  00519	48 39 44 24 20	 cmp	 QWORD PTR _Pfirst$[rsp], rax
  0051e	74 7f		 je	 SHORT $LN22@Partition_

; 8338 :                 swap(*_Pfirst, *_Glast_prev); // intentional ADL

  00520	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pfirst$[rsp]
  00525	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR _Left$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  0052d	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR _Left$[rsp]
  00535	48 89 84 24 20
	01 00 00	 mov	 QWORD PTR $T18[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 139  :     _Ty _Tmp = _STD move(_Left);

  0053d	48 8d 84 24 70
	02 00 00	 lea	 rax, QWORD PTR _Tmp$39[rsp]
  00545	48 8b f8	 mov	 rdi, rax
  00548	48 8b b4 24 20
	01 00 00	 mov	 rsi, QWORD PTR $T18[rsp]
  00550	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  00555	f3 a4		 rep movsb
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00557	48 8b 44 24 68	 mov	 rax, QWORD PTR _Glast_prev$9[rsp]
  0055c	48 89 84 24 28
	01 00 00	 mov	 QWORD PTR $T19[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 140  :     _Left    = _STD move(_Right);

  00564	48 8b bc 24 a8
	00 00 00	 mov	 rdi, QWORD PTR _Left$[rsp]
  0056c	48 8b b4 24 28
	01 00 00	 mov	 rsi, QWORD PTR $T19[rsp]
  00574	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  00579	f3 a4		 rep movsb
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  0057b	48 8d 84 24 70
	02 00 00	 lea	 rax, QWORD PTR _Tmp$39[rsp]
  00583	48 89 84 24 30
	01 00 00	 mov	 QWORD PTR $T20[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 141  :     _Right   = _STD move(_Tmp);

  0058b	48 8b 7c 24 68	 mov	 rdi, QWORD PTR _Glast_prev$9[rsp]
  00590	48 8b b4 24 30
	01 00 00	 mov	 rsi, QWORD PTR $T20[rsp]
  00598	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  0059d	f3 a4		 rep movsb
$LN22@Partition_:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm

; 8340 :         }

  0059f	e9 77 fe ff ff	 jmp	 $LN12@Partition_
$LN13@Partition_:

; 8341 : 
; 8342 :         if (_Glast == _First && _Gfirst == _Last) {

  005a4	48 8b 84 24 c8
	06 00 00	 mov	 rax, QWORD PTR _First$[rsp]
  005ac	48 39 44 24 40	 cmp	 QWORD PTR _Glast$[rsp], rax
  005b1	75 63		 jne	 SHORT $LN26@Partition_
  005b3	48 8b 84 24 d0
	06 00 00	 mov	 rax, QWORD PTR _Last$[rsp]
  005bb	48 39 44 24 30	 cmp	 QWORD PTR _Gfirst$[rsp], rax
  005c0	75 54		 jne	 SHORT $LN26@Partition_
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  005c2	48 8d 44 24 20	 lea	 rax, QWORD PTR _Pfirst$[rsp]
  005c7	48 89 84 24 38
	01 00 00	 mov	 QWORD PTR $T21[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 274  :         : first(_STD forward<_Other1>(_Val1)), second(_STD forward<_Other2>(_Val2)) {

  005cf	48 8b 84 24 c0
	06 00 00	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]
  005d7	48 8b 8c 24 38
	01 00 00	 mov	 rcx, QWORD PTR $T21[rsp]
  005df	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  005e2	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  005e5	48 8d 44 24 28	 lea	 rax, QWORD PTR _Plast$[rsp]
  005ea	48 89 84 24 40
	01 00 00	 mov	 QWORD PTR $T22[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 274  :         : first(_STD forward<_Other1>(_Val1)), second(_STD forward<_Other2>(_Val2)) {

  005f2	48 8b 84 24 c0
	06 00 00	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]
  005fa	48 8b 8c 24 40
	01 00 00	 mov	 rcx, QWORD PTR $T22[rsp]
  00602	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00605	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm

; 8343 :             return pair<_RanIt, _RanIt>(_Pfirst, _Plast);

  00609	48 8b 84 24 c0
	06 00 00	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]
  00611	e9 5f 03 00 00	 jmp	 $LN1@Partition_
$LN26@Partition_:

; 8344 :         }
; 8345 : 
; 8346 :         if (_Glast == _First) { // no room at bottom, rotate pivot upward

  00616	48 8b 84 24 c8
	06 00 00	 mov	 rax, QWORD PTR _First$[rsp]
  0061e	48 39 44 24 40	 cmp	 QWORD PTR _Glast$[rsp], rax
  00623	0f 85 56 01 00
	00		 jne	 $LN27@Partition_

; 8347 :             if (_Plast != _Gfirst) {

  00629	48 8b 44 24 30	 mov	 rax, QWORD PTR _Gfirst$[rsp]
  0062e	48 39 44 24 28	 cmp	 QWORD PTR _Plast$[rsp], rax
  00633	0f 84 92 00 00
	00		 je	 $LN29@Partition_

; 8348 :                 swap(*_Pfirst, *_Plast); // intentional ADL

  00639	48 8b 44 24 28	 mov	 rax, QWORD PTR _Plast$[rsp]
  0063e	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR _Right$[rsp], rax
  00646	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pfirst$[rsp]
  0064b	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR _Left$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00653	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR _Left$[rsp]
  0065b	48 89 84 24 48
	01 00 00	 mov	 QWORD PTR $T23[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 139  :     _Ty _Tmp = _STD move(_Left);

  00663	48 8d 84 24 20
	03 00 00	 lea	 rax, QWORD PTR _Tmp$40[rsp]
  0066b	48 8b f8	 mov	 rdi, rax
  0066e	48 8b b4 24 48
	01 00 00	 mov	 rsi, QWORD PTR $T23[rsp]
  00676	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  0067b	f3 a4		 rep movsb
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  0067d	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR _Right$[rsp]
  00685	48 89 84 24 50
	01 00 00	 mov	 QWORD PTR $T24[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 140  :     _Left    = _STD move(_Right);

  0068d	48 8b bc 24 b0
	00 00 00	 mov	 rdi, QWORD PTR _Left$[rsp]
  00695	48 8b b4 24 50
	01 00 00	 mov	 rsi, QWORD PTR $T24[rsp]
  0069d	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  006a2	f3 a4		 rep movsb
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  006a4	48 8d 84 24 20
	03 00 00	 lea	 rax, QWORD PTR _Tmp$40[rsp]
  006ac	48 89 84 24 58
	01 00 00	 mov	 QWORD PTR $T25[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 141  :     _Right   = _STD move(_Tmp);

  006b4	48 8b bc 24 b8
	00 00 00	 mov	 rdi, QWORD PTR _Right$[rsp]
  006bc	48 8b b4 24 58
	01 00 00	 mov	 rsi, QWORD PTR $T25[rsp]
  006c4	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  006c9	f3 a4		 rep movsb
$LN29@Partition_:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm

; 8351 :             ++_Plast;

  006cb	48 8b 44 24 28	 mov	 rax, QWORD PTR _Plast$[rsp]
  006d0	48 05 a8 00 00
	00		 add	 rax, 168		; 000000a8H
  006d6	48 89 44 24 28	 mov	 QWORD PTR _Plast$[rsp], rax

; 8352 :             swap(*_Pfirst, *_Gfirst); // intentional ADL

  006db	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pfirst$[rsp]
  006e0	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR _Left$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  006e8	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR _Left$[rsp]
  006f0	48 89 84 24 60
	01 00 00	 mov	 QWORD PTR $T26[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 139  :     _Ty _Tmp = _STD move(_Left);

  006f8	48 8d 84 24 d0
	03 00 00	 lea	 rax, QWORD PTR _Tmp$41[rsp]
  00700	48 8b f8	 mov	 rdi, rax
  00703	48 8b b4 24 60
	01 00 00	 mov	 rsi, QWORD PTR $T26[rsp]
  0070b	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  00710	f3 a4		 rep movsb
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00712	48 8b 44 24 30	 mov	 rax, QWORD PTR _Gfirst$[rsp]
  00717	48 89 84 24 68
	01 00 00	 mov	 QWORD PTR $T27[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 140  :     _Left    = _STD move(_Right);

  0071f	48 8b bc 24 c0
	00 00 00	 mov	 rdi, QWORD PTR _Left$[rsp]
  00727	48 8b b4 24 68
	01 00 00	 mov	 rsi, QWORD PTR $T27[rsp]
  0072f	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  00734	f3 a4		 rep movsb
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00736	48 8d 84 24 d0
	03 00 00	 lea	 rax, QWORD PTR _Tmp$41[rsp]
  0073e	48 89 84 24 70
	01 00 00	 mov	 QWORD PTR $T28[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 141  :     _Right   = _STD move(_Tmp);

  00746	48 8b 7c 24 30	 mov	 rdi, QWORD PTR _Gfirst$[rsp]
  0074b	48 8b b4 24 70
	01 00 00	 mov	 rsi, QWORD PTR $T28[rsp]
  00753	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  00758	f3 a4		 rep movsb
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm

; 8353 :             ++_Pfirst;

  0075a	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pfirst$[rsp]
  0075f	48 05 a8 00 00
	00		 add	 rax, 168		; 000000a8H
  00765	48 89 44 24 20	 mov	 QWORD PTR _Pfirst$[rsp], rax

; 8354 :             ++_Gfirst;

  0076a	48 8b 44 24 30	 mov	 rax, QWORD PTR _Gfirst$[rsp]
  0076f	48 05 a8 00 00
	00		 add	 rax, 168		; 000000a8H
  00775	48 89 44 24 30	 mov	 QWORD PTR _Gfirst$[rsp], rax
  0077a	e9 f1 01 00 00	 jmp	 $LN28@Partition_
$LN27@Partition_:

; 8355 :         } else if (_Gfirst == _Last) { // no room at top, rotate pivot downward

  0077f	48 8b 84 24 d0
	06 00 00	 mov	 rax, QWORD PTR _Last$[rsp]
  00787	48 39 44 24 30	 cmp	 QWORD PTR _Gfirst$[rsp], rax
  0078c	0f 85 52 01 00
	00		 jne	 $LN30@Partition_

; 8356 :             if (--_Glast != --_Pfirst) {

  00792	48 8b 44 24 40	 mov	 rax, QWORD PTR _Glast$[rsp]
  00797	48 2d a8 00 00
	00		 sub	 rax, 168		; 000000a8H
  0079d	48 89 44 24 40	 mov	 QWORD PTR _Glast$[rsp], rax
  007a2	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pfirst$[rsp]
  007a7	48 2d a8 00 00
	00		 sub	 rax, 168		; 000000a8H
  007ad	48 89 44 24 20	 mov	 QWORD PTR _Pfirst$[rsp], rax
  007b2	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pfirst$[rsp]
  007b7	48 39 44 24 40	 cmp	 QWORD PTR _Glast$[rsp], rax
  007bc	74 7f		 je	 SHORT $LN32@Partition_

; 8357 :                 swap(*_Glast, *_Pfirst); // intentional ADL

  007be	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pfirst$[rsp]
  007c3	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR _Right$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  007cb	48 8b 44 24 40	 mov	 rax, QWORD PTR _Glast$[rsp]
  007d0	48 89 84 24 78
	01 00 00	 mov	 QWORD PTR $T29[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 139  :     _Ty _Tmp = _STD move(_Left);

  007d8	48 8d 84 24 80
	04 00 00	 lea	 rax, QWORD PTR _Tmp$42[rsp]
  007e0	48 8b f8	 mov	 rdi, rax
  007e3	48 8b b4 24 78
	01 00 00	 mov	 rsi, QWORD PTR $T29[rsp]
  007eb	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  007f0	f3 a4		 rep movsb
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  007f2	48 8b 84 24 c8
	00 00 00	 mov	 rax, QWORD PTR _Right$[rsp]
  007fa	48 89 84 24 80
	01 00 00	 mov	 QWORD PTR $T30[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 140  :     _Left    = _STD move(_Right);

  00802	48 8b 7c 24 40	 mov	 rdi, QWORD PTR _Glast$[rsp]
  00807	48 8b b4 24 80
	01 00 00	 mov	 rsi, QWORD PTR $T30[rsp]
  0080f	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  00814	f3 a4		 rep movsb
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00816	48 8d 84 24 80
	04 00 00	 lea	 rax, QWORD PTR _Tmp$42[rsp]
  0081e	48 89 84 24 88
	01 00 00	 mov	 QWORD PTR $T31[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 141  :     _Right   = _STD move(_Tmp);

  00826	48 8b bc 24 c8
	00 00 00	 mov	 rdi, QWORD PTR _Right$[rsp]
  0082e	48 8b b4 24 88
	01 00 00	 mov	 rsi, QWORD PTR $T31[rsp]
  00836	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  0083b	f3 a4		 rep movsb
$LN32@Partition_:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm

; 8360 :             swap(*_Pfirst, *--_Plast); // intentional ADL

  0083d	48 8b 44 24 28	 mov	 rax, QWORD PTR _Plast$[rsp]
  00842	48 2d a8 00 00
	00		 sub	 rax, 168		; 000000a8H
  00848	48 89 44 24 28	 mov	 QWORD PTR _Plast$[rsp], rax
  0084d	48 8b 44 24 28	 mov	 rax, QWORD PTR _Plast$[rsp]
  00852	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR _Right$[rsp], rax
  0085a	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pfirst$[rsp]
  0085f	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR _Left$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00867	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR _Left$[rsp]
  0086f	48 89 84 24 90
	01 00 00	 mov	 QWORD PTR $T32[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 139  :     _Ty _Tmp = _STD move(_Left);

  00877	48 8d 84 24 30
	05 00 00	 lea	 rax, QWORD PTR _Tmp$43[rsp]
  0087f	48 8b f8	 mov	 rdi, rax
  00882	48 8b b4 24 90
	01 00 00	 mov	 rsi, QWORD PTR $T32[rsp]
  0088a	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  0088f	f3 a4		 rep movsb
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00891	48 8b 84 24 d8
	00 00 00	 mov	 rax, QWORD PTR _Right$[rsp]
  00899	48 89 84 24 98
	01 00 00	 mov	 QWORD PTR $T33[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 140  :     _Left    = _STD move(_Right);

  008a1	48 8b bc 24 d0
	00 00 00	 mov	 rdi, QWORD PTR _Left$[rsp]
  008a9	48 8b b4 24 98
	01 00 00	 mov	 rsi, QWORD PTR $T33[rsp]
  008b1	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  008b6	f3 a4		 rep movsb
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  008b8	48 8d 84 24 30
	05 00 00	 lea	 rax, QWORD PTR _Tmp$43[rsp]
  008c0	48 89 84 24 a0
	01 00 00	 mov	 QWORD PTR $T34[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 141  :     _Right   = _STD move(_Tmp);

  008c8	48 8b bc 24 d8
	00 00 00	 mov	 rdi, QWORD PTR _Right$[rsp]
  008d0	48 8b b4 24 a0
	01 00 00	 mov	 rsi, QWORD PTR $T34[rsp]
  008d8	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  008dd	f3 a4		 rep movsb
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm

; 8361 :         } else {

  008df	e9 8c 00 00 00	 jmp	 $LN28@Partition_
$LN30@Partition_:

; 8362 :             swap(*_Gfirst, *--_Glast); // intentional ADL

  008e4	48 8b 44 24 40	 mov	 rax, QWORD PTR _Glast$[rsp]
  008e9	48 2d a8 00 00
	00		 sub	 rax, 168		; 000000a8H
  008ef	48 89 44 24 40	 mov	 QWORD PTR _Glast$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  008f4	48 8b 44 24 30	 mov	 rax, QWORD PTR _Gfirst$[rsp]
  008f9	48 89 84 24 a8
	01 00 00	 mov	 QWORD PTR $T35[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 139  :     _Ty _Tmp = _STD move(_Left);

  00901	48 8d 84 24 e0
	05 00 00	 lea	 rax, QWORD PTR _Tmp$44[rsp]
  00909	48 8b f8	 mov	 rdi, rax
  0090c	48 8b b4 24 a8
	01 00 00	 mov	 rsi, QWORD PTR $T35[rsp]
  00914	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  00919	f3 a4		 rep movsb
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  0091b	48 8b 44 24 40	 mov	 rax, QWORD PTR _Glast$[rsp]
  00920	48 89 84 24 b0
	01 00 00	 mov	 QWORD PTR $T36[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 140  :     _Left    = _STD move(_Right);

  00928	48 8b 7c 24 30	 mov	 rdi, QWORD PTR _Gfirst$[rsp]
  0092d	48 8b b4 24 b0
	01 00 00	 mov	 rsi, QWORD PTR $T36[rsp]
  00935	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  0093a	f3 a4		 rep movsb
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  0093c	48 8d 84 24 e0
	05 00 00	 lea	 rax, QWORD PTR _Tmp$44[rsp]
  00944	48 89 84 24 b8
	01 00 00	 mov	 QWORD PTR $T37[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 141  :     _Right   = _STD move(_Tmp);

  0094c	48 8b 7c 24 40	 mov	 rdi, QWORD PTR _Glast$[rsp]
  00951	48 8b b4 24 b8
	01 00 00	 mov	 rsi, QWORD PTR $T37[rsp]
  00959	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  0095e	f3 a4		 rep movsb
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm

; 8363 :             ++_Gfirst;

  00960	48 8b 44 24 30	 mov	 rax, QWORD PTR _Gfirst$[rsp]
  00965	48 05 a8 00 00
	00		 add	 rax, 168		; 000000a8H
  0096b	48 89 44 24 30	 mov	 QWORD PTR _Gfirst$[rsp], rax
$LN28@Partition_:

; 8364 :         }
; 8365 :     }

  00970	e9 46 f9 ff ff	 jmp	 $LN6@Partition_
$LN1@Partition_:

; 8366 : }

  00975	48 8b 8c 24 90
	06 00 00	 mov	 rcx, QWORD PTR __$ArrayPad$[rsp]
  0097d	48 33 cc	 xor	 rcx, rsp
  00980	e8 00 00 00 00	 call	 __security_check_cookie
  00985	48 81 c4 a8 06
	00 00		 add	 rsp, 1704		; 000006a8H
  0098c	5f		 pop	 rdi
  0098d	5e		 pop	 rsi
  0098e	c3		 ret	 0
??$_Partition_by_median_guess_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YA?AU?$pair@PEAUEventScheduleDate@mu2@@PEAU12@@0@PEAUEventScheduleDate@mu2@@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z ENDP ; std::_Partition_by_median_guess_unchecked<mu2::EventScheduleDate *,<lambda_5acf0176913ce2eb7dca573a416bd931> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_heap_algorithms.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_heap_algorithms.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_heap_algorithms.hpp
;	COMDAT ??$_Make_heap_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z
_TEXT	SEGMENT
_Hole$1 = 48
_Bottom$ = 56
$T2 = 64
$T3 = 72
_Val$4 = 80
__$ArrayPad$ = 256
_First$ = 304
_Last$ = 312
_Pred$ = 320
??$_Make_heap_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z PROC ; std::_Make_heap_unchecked<mu2::EventScheduleDate *,<lambda_5acf0176913ce2eb7dca573a416bd931> >, COMDAT

; 121  : _CONSTEXPR20 void _Make_heap_unchecked(_RanIt _First, _RanIt _Last, _Pr _Pred) {

  00000	44 88 44 24 18	 mov	 BYTE PTR [rsp+24], r8b
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	56		 push	 rsi
  00010	57		 push	 rdi
  00011	48 81 ec 18 01
	00 00		 sub	 rsp, 280		; 00000118H
  00018	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR __security_cookie
  0001f	48 33 c4	 xor	 rax, rsp
  00022	48 89 84 24 00
	01 00 00	 mov	 QWORD PTR __$ArrayPad$[rsp], rax

; 122  :     // make [_First, _Last) into a heap
; 123  :     using _Diff   = _Iter_diff_t<_RanIt>;
; 124  :     _Diff _Bottom = _Last - _First;

  0002a	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR _First$[rsp]
  00032	48 8b 8c 24 38
	01 00 00	 mov	 rcx, QWORD PTR _Last$[rsp]
  0003a	48 2b c8	 sub	 rcx, rax
  0003d	48 8b c1	 mov	 rax, rcx
  00040	48 99		 cdq
  00042	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  00047	48 f7 f9	 idiv	 rcx
  0004a	48 89 44 24 38	 mov	 QWORD PTR _Bottom$[rsp], rax

; 125  :     for (_Diff _Hole = _Bottom >> 1; _Hole > 0;) { // shift for codegen

  0004f	48 8b 44 24 38	 mov	 rax, QWORD PTR _Bottom$[rsp]
  00054	48 d1 f8	 sar	 rax, 1
  00057	48 89 44 24 30	 mov	 QWORD PTR _Hole$1[rsp], rax
$LN2@Make_heap_:
  0005c	48 83 7c 24 30
	00		 cmp	 QWORD PTR _Hole$1[rsp], 0
  00062	7e 75		 jle	 SHORT $LN3@Make_heap_

; 126  :         // reheap top half, bottom to top
; 127  :         --_Hole;

  00064	48 8b 44 24 30	 mov	 rax, QWORD PTR _Hole$1[rsp]
  00069	48 ff c8	 dec	 rax
  0006c	48 89 44 24 30	 mov	 QWORD PTR _Hole$1[rsp], rax

; 128  :         _Iter_value_t<_RanIt> _Val(_STD move(*(_First + _Hole)));

  00071	48 69 44 24 30
	a8 00 00 00	 imul	 rax, QWORD PTR _Hole$1[rsp], 168 ; 000000a8H
  0007a	48 8b 8c 24 30
	01 00 00	 mov	 rcx, QWORD PTR _First$[rsp]
  00082	48 03 c8	 add	 rcx, rax
  00085	48 8b c1	 mov	 rax, rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00088	48 89 44 24 40	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_heap_algorithms.hpp

; 128  :         _Iter_value_t<_RanIt> _Val(_STD move(*(_First + _Hole)));

  0008d	48 8d 44 24 50	 lea	 rax, QWORD PTR _Val$4[rsp]
  00092	48 8b f8	 mov	 rdi, rax
  00095	48 8b 74 24 40	 mov	 rsi, QWORD PTR $T2[rsp]
  0009a	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  0009f	f3 a4		 rep movsb
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  000a1	48 8d 44 24 50	 lea	 rax, QWORD PTR _Val$4[rsp]
  000a6	48 89 44 24 48	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_heap_algorithms.hpp

; 129  :         _STD _Pop_heap_hole_by_index(_First, _Hole, _Bottom, _STD move(_Val), _Pred);

  000ab	48 8b 44 24 48	 mov	 rax, QWORD PTR $T3[rsp]
  000b0	0f b6 8c 24 40
	01 00 00	 movzx	 ecx, BYTE PTR _Pred$[rsp]
  000b8	88 4c 24 20	 mov	 BYTE PTR [rsp+32], cl
  000bc	4c 8b c8	 mov	 r9, rax
  000bf	4c 8b 44 24 38	 mov	 r8, QWORD PTR _Bottom$[rsp]
  000c4	48 8b 54 24 30	 mov	 rdx, QWORD PTR _Hole$1[rsp]
  000c9	48 8b 8c 24 30
	01 00 00	 mov	 rcx, QWORD PTR _First$[rsp]
  000d1	e8 00 00 00 00	 call	 ??$_Pop_heap_hole_by_index@PEAUEventScheduleDate@mu2@@U12@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@_J1$$QEAU12@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z ; std::_Pop_heap_hole_by_index<mu2::EventScheduleDate *,mu2::EventScheduleDate,<lambda_5acf0176913ce2eb7dca573a416bd931> >
  000d6	90		 npad	 1

; 130  :     }

  000d7	eb 83		 jmp	 SHORT $LN2@Make_heap_
$LN3@Make_heap_:

; 131  : }

  000d9	48 8b 8c 24 00
	01 00 00	 mov	 rcx, QWORD PTR __$ArrayPad$[rsp]
  000e1	48 33 cc	 xor	 rcx, rsp
  000e4	e8 00 00 00 00	 call	 __security_check_cookie
  000e9	48 81 c4 18 01
	00 00		 add	 rsp, 280		; 00000118H
  000f0	5f		 pop	 rdi
  000f1	5e		 pop	 rsi
  000f2	c3		 ret	 0
??$_Make_heap_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z ENDP ; std::_Make_heap_unchecked<mu2::EventScheduleDate *,<lambda_5acf0176913ce2eb7dca573a416bd931> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
;	COMDAT ??$_Insertion_sort_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAPEAUEventScheduleDate@mu2@@QEAU12@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z
_TEXT	SEGMENT
$T1 = 32
$T2 = 33
_Hole$3 = 40
_Mid$4 = 48
tv130 = 56
tv79 = 60
_Last$ = 64
_Prev$5 = 72
_Dest$ = 80
$T6 = 88
$T7 = 96
$T8 = 104
$T9 = 112
$T10 = 120
_Val$11 = 128
__$ArrayPad$ = 304
_First$ = 352
_Last$ = 360
_Pred$ = 368
??$_Insertion_sort_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAPEAUEventScheduleDate@mu2@@QEAU12@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z PROC ; std::_Insertion_sort_unchecked<mu2::EventScheduleDate *,<lambda_5acf0176913ce2eb7dca573a416bd931> >, COMDAT

; 8241 : _CONSTEXPR20 _BidIt _Insertion_sort_unchecked(const _BidIt _First, const _BidIt _Last, _Pr _Pred) {

  00000	44 88 44 24 18	 mov	 BYTE PTR [rsp+24], r8b
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	56		 push	 rsi
  00010	57		 push	 rdi
  00011	48 81 ec 48 01
	00 00		 sub	 rsp, 328		; 00000148H
  00018	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR __security_cookie
  0001f	48 33 c4	 xor	 rax, rsp
  00022	48 89 84 24 30
	01 00 00	 mov	 QWORD PTR __$ArrayPad$[rsp], rax

; 8242 :     // insertion sort [_First, _Last)
; 8243 :     if (_First != _Last) {

  0002a	48 8b 84 24 68
	01 00 00	 mov	 rax, QWORD PTR _Last$[rsp]
  00032	48 39 84 24 60
	01 00 00	 cmp	 QWORD PTR _First$[rsp], rax
  0003a	0f 84 ea 01 00
	00		 je	 $LN8@Insertion_

; 8244 :         for (_BidIt _Mid = _First; ++_Mid != _Last;) { // order next element

  00040	48 8b 84 24 60
	01 00 00	 mov	 rax, QWORD PTR _First$[rsp]
  00048	48 89 44 24 30	 mov	 QWORD PTR _Mid$4[rsp], rax
$LN2@Insertion_:
  0004d	48 8b 44 24 30	 mov	 rax, QWORD PTR _Mid$4[rsp]
  00052	48 05 a8 00 00
	00		 add	 rax, 168		; 000000a8H
  00058	48 89 44 24 30	 mov	 QWORD PTR _Mid$4[rsp], rax
  0005d	48 8b 84 24 68
	01 00 00	 mov	 rax, QWORD PTR _Last$[rsp]
  00065	48 39 44 24 30	 cmp	 QWORD PTR _Mid$4[rsp], rax
  0006a	0f 84 ba 01 00
	00		 je	 $LN8@Insertion_

; 8245 :             _BidIt _Hole = _Mid;

  00070	48 8b 44 24 30	 mov	 rax, QWORD PTR _Mid$4[rsp]
  00075	48 89 44 24 28	 mov	 QWORD PTR _Hole$3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  0007a	48 8b 44 24 30	 mov	 rax, QWORD PTR _Mid$4[rsp]
  0007f	48 89 44 24 58	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm

; 8246 :             _Iter_value_t<_BidIt> _Val(_STD move(*_Mid));

  00084	48 8d 84 24 80
	00 00 00	 lea	 rax, QWORD PTR _Val$11[rsp]
  0008c	48 8b f8	 mov	 rdi, rax
  0008f	48 8b 74 24 58	 mov	 rsi, QWORD PTR $T6[rsp]
  00094	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  00099	f3 a4		 rep movsb
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 229  : 			return lhs.readyTime < rhs.readyTime;

  0009b	48 8b 84 24 60
	01 00 00	 mov	 rax, QWORD PTR _First$[rsp]
  000a3	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000a6	48 39 84 24 80
	00 00 00	 cmp	 QWORD PTR _Val$11[rsp], rax
  000ae	7d 0a		 jge	 SHORT $LN19@Insertion_
  000b0	c7 44 24 38 01
	00 00 00	 mov	 DWORD PTR tv130[rsp], 1
  000b8	eb 08		 jmp	 SHORT $LN20@Insertion_
$LN19@Insertion_:
  000ba	c7 44 24 38 00
	00 00 00	 mov	 DWORD PTR tv130[rsp], 0
$LN20@Insertion_:
  000c2	0f b6 44 24 38	 movzx	 eax, BYTE PTR tv130[rsp]
  000c7	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm

; 8248 :             if (_DEBUG_LT_PRED(_Pred, _Val, *_First)) { // found new earliest element, move to front

  000cb	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  000d0	0f b6 c0	 movzx	 eax, al
  000d3	85 c0		 test	 eax, eax
  000d5	0f 84 b0 00 00
	00		 je	 $LN9@Insertion_

; 8249 :                 _STD _Move_backward_unchecked(_First, _Mid, ++_Hole);

  000db	48 8b 44 24 28	 mov	 rax, QWORD PTR _Hole$3[rsp]
  000e0	48 05 a8 00 00
	00		 add	 rax, 168		; 000000a8H
  000e6	48 89 44 24 28	 mov	 QWORD PTR _Hole$3[rsp], rax
  000eb	48 8b 44 24 28	 mov	 rax, QWORD PTR _Hole$3[rsp]
  000f0	48 89 44 24 50	 mov	 QWORD PTR _Dest$[rsp], rax
  000f5	48 8b 44 24 30	 mov	 rax, QWORD PTR _Mid$4[rsp]
  000fa	48 89 44 24 40	 mov	 QWORD PTR _Last$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 5190 :             return _STD _Copy_backward_memmove(_First, _Last, _Dest);

  000ff	4c 8b 44 24 50	 mov	 r8, QWORD PTR _Dest$[rsp]
  00104	48 8b 54 24 40	 mov	 rdx, QWORD PTR _Last$[rsp]
  00109	48 8b 8c 24 60
	01 00 00	 mov	 rcx, QWORD PTR _First$[rsp]
  00111	e8 00 00 00 00	 call	 ??$_Copy_backward_memmove@PEAUEventScheduleDate@mu2@@PEAU12@@std@@YAPEAUEventScheduleDate@mu2@@PEAU12@00@Z ; std::_Copy_backward_memmove<mu2::EventScheduleDate *,mu2::EventScheduleDate *>
  00116	90		 npad	 1
  00117	eb 4c		 jmp	 SHORT $LN21@Insertion_
$LN22@Insertion_:

; 5191 :         }
; 5192 :     }
; 5193 : 
; 5194 :     while (_First != _Last) {

  00119	48 8b 44 24 40	 mov	 rax, QWORD PTR _Last$[rsp]
  0011e	48 39 84 24 60
	01 00 00	 cmp	 QWORD PTR _First$[rsp], rax
  00126	74 3d		 je	 SHORT $LN21@Insertion_

; 5195 :         *--_Dest = _STD move(*--_Last);

  00128	48 8b 44 24 40	 mov	 rax, QWORD PTR _Last$[rsp]
  0012d	48 2d a8 00 00
	00		 sub	 rax, 168		; 000000a8H
  00133	48 89 44 24 40	 mov	 QWORD PTR _Last$[rsp], rax
  00138	48 8b 44 24 50	 mov	 rax, QWORD PTR _Dest$[rsp]
  0013d	48 2d a8 00 00
	00		 sub	 rax, 168		; 000000a8H
  00143	48 89 44 24 50	 mov	 QWORD PTR _Dest$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00148	48 8b 44 24 40	 mov	 rax, QWORD PTR _Last$[rsp]
  0014d	48 89 44 24 60	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 5195 :         *--_Dest = _STD move(*--_Last);

  00152	48 8b 7c 24 50	 mov	 rdi, QWORD PTR _Dest$[rsp]
  00157	48 8b 74 24 60	 mov	 rsi, QWORD PTR $T7[rsp]
  0015c	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  00161	f3 a4		 rep movsb

; 5196 :     }

  00163	eb b4		 jmp	 SHORT $LN22@Insertion_
$LN21@Insertion_:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00165	48 8d 84 24 80
	00 00 00	 lea	 rax, QWORD PTR _Val$11[rsp]
  0016d	48 89 44 24 68	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm

; 8250 :                 *_First = _STD move(_Val);

  00172	48 8b bc 24 60
	01 00 00	 mov	 rdi, QWORD PTR _First$[rsp]
  0017a	48 8b 74 24 68	 mov	 rsi, QWORD PTR $T8[rsp]
  0017f	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  00184	f3 a4		 rep movsb

; 8251 :             } else { // look for insertion point after first

  00186	e9 9a 00 00 00	 jmp	 $LN10@Insertion_
$LN9@Insertion_:

; 8252 :                 for (_BidIt _Prev = _Hole; _DEBUG_LT_PRED(_Pred, _Val, *--_Prev); _Hole = _Prev) {

  0018b	48 8b 44 24 28	 mov	 rax, QWORD PTR _Hole$3[rsp]
  00190	48 89 44 24 48	 mov	 QWORD PTR _Prev$5[rsp], rax
  00195	eb 0a		 jmp	 SHORT $LN7@Insertion_
$LN5@Insertion_:
  00197	48 8b 44 24 48	 mov	 rax, QWORD PTR _Prev$5[rsp]
  0019c	48 89 44 24 28	 mov	 QWORD PTR _Hole$3[rsp], rax
$LN7@Insertion_:
  001a1	48 8b 44 24 48	 mov	 rax, QWORD PTR _Prev$5[rsp]
  001a6	48 2d a8 00 00
	00		 sub	 rax, 168		; 000000a8H
  001ac	48 89 44 24 48	 mov	 QWORD PTR _Prev$5[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 229  : 			return lhs.readyTime < rhs.readyTime;

  001b1	48 8b 44 24 48	 mov	 rax, QWORD PTR _Prev$5[rsp]
  001b6	48 8b 00	 mov	 rax, QWORD PTR [rax]
  001b9	48 39 84 24 80
	00 00 00	 cmp	 QWORD PTR _Val$11[rsp], rax
  001c1	7d 0a		 jge	 SHORT $LN37@Insertion_
  001c3	c7 44 24 3c 01
	00 00 00	 mov	 DWORD PTR tv79[rsp], 1
  001cb	eb 08		 jmp	 SHORT $LN38@Insertion_
$LN37@Insertion_:
  001cd	c7 44 24 3c 00
	00 00 00	 mov	 DWORD PTR tv79[rsp], 0
$LN38@Insertion_:
  001d5	0f b6 44 24 3c	 movzx	 eax, BYTE PTR tv79[rsp]
  001da	88 44 24 21	 mov	 BYTE PTR $T2[rsp], al
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm

; 8252 :                 for (_BidIt _Prev = _Hole; _DEBUG_LT_PRED(_Pred, _Val, *--_Prev); _Hole = _Prev) {

  001de	0f b6 44 24 21	 movzx	 eax, BYTE PTR $T2[rsp]
  001e3	0f b6 c0	 movzx	 eax, al
  001e6	85 c0		 test	 eax, eax
  001e8	74 1d		 je	 SHORT $LN6@Insertion_
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  001ea	48 8b 44 24 48	 mov	 rax, QWORD PTR _Prev$5[rsp]
  001ef	48 89 44 24 70	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm

; 8253 :                     *_Hole = _STD move(*_Prev); // move hole down

  001f4	48 8b 7c 24 28	 mov	 rdi, QWORD PTR _Hole$3[rsp]
  001f9	48 8b 74 24 70	 mov	 rsi, QWORD PTR $T9[rsp]
  001fe	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  00203	f3 a4		 rep movsb

; 8254 :                 }

  00205	eb 90		 jmp	 SHORT $LN5@Insertion_
$LN6@Insertion_:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00207	48 8d 84 24 80
	00 00 00	 lea	 rax, QWORD PTR _Val$11[rsp]
  0020f	48 89 44 24 78	 mov	 QWORD PTR $T10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm

; 8256 :                 *_Hole = _STD move(_Val); // insert element in hole

  00214	48 8b 7c 24 28	 mov	 rdi, QWORD PTR _Hole$3[rsp]
  00219	48 8b 74 24 78	 mov	 rsi, QWORD PTR $T10[rsp]
  0021e	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  00223	f3 a4		 rep movsb
$LN10@Insertion_:

; 8257 :             }
; 8258 :         }

  00225	e9 23 fe ff ff	 jmp	 $LN2@Insertion_
$LN8@Insertion_:

; 8259 :     }
; 8260 : 
; 8261 :     return _Last;

  0022a	48 8b 84 24 68
	01 00 00	 mov	 rax, QWORD PTR _Last$[rsp]

; 8262 : }

  00232	48 8b 8c 24 30
	01 00 00	 mov	 rcx, QWORD PTR __$ArrayPad$[rsp]
  0023a	48 33 cc	 xor	 rcx, rsp
  0023d	e8 00 00 00 00	 call	 __security_check_cookie
  00242	48 81 c4 48 01
	00 00		 add	 rsp, 328		; 00000148H
  00249	5f		 pop	 rdi
  0024a	5e		 pop	 rsi
  0024b	c3		 ret	 0
??$_Insertion_sort_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAPEAUEventScheduleDate@mu2@@QEAU12@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z ENDP ; std::_Insertion_sort_unchecked<mu2::EventScheduleDate *,<lambda_5acf0176913ce2eb7dca573a416bd931> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ??$_Emplace_one_at_back@AEBUEventScheduleDate@mu2@@@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAAEAUEventScheduleDate@mu2@@AEBU23@@Z
_TEXT	SEGMENT
_Mylast$1 = 32
_My_data$ = 40
_Mylast$ = 48
_My_data$2 = 56
$T3 = 64
_Obj$ = 72
$T4 = 80
$T5 = 88
$T6 = 96
$T7 = 104
$T8 = 112
_Result$9 = 120
$T10 = 128
$T11 = 136
this$ = 176
<_Val_0>$ = 184
??$_Emplace_one_at_back@AEBUEventScheduleDate@mu2@@@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAAEAUEventScheduleDate@mu2@@AEBU23@@Z PROC ; std::vector<mu2::EventScheduleDate,std::allocator<mu2::EventScheduleDate> >::_Emplace_one_at_back<mu2::EventScheduleDate const &>, COMDAT

; 839  :     _CONSTEXPR20 _Ty& _Emplace_one_at_back(_Valty&&... _Val) {

$LN419:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	56		 push	 rsi
  0000b	57		 push	 rdi
  0000c	48 81 ec 98 00
	00 00		 sub	 rsp, 152		; 00000098H

; 840  :         // insert by perfectly forwarding into element at end, provide strong guarantee
; 841  :         auto& _My_data   = _Mypair._Myval2;

  00013	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0001b	48 89 44 24 28	 mov	 QWORD PTR _My_data$[rsp], rax

; 842  :         pointer& _Mylast = _My_data._Mylast;

  00020	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00025	48 83 c0 08	 add	 rax, 8
  00029	48 89 44 24 30	 mov	 QWORD PTR _Mylast$[rsp], rax

; 843  : 
; 844  :         if (_Mylast != _My_data._Myend) {

  0002e	48 8b 44 24 30	 mov	 rax, QWORD PTR _Mylast$[rsp]
  00033	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _My_data$[rsp]
  00038	48 8b 49 10	 mov	 rcx, QWORD PTR [rcx+16]
  0003c	48 39 08	 cmp	 QWORD PTR [rax], rcx
  0003f	0f 84 b2 00 00
	00		 je	 $LN2@Emplace_on
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00045	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR <_Val_0>$[rsp]
  0004d	48 89 44 24 40	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 854  :         auto& _My_data   = _Mypair._Myval2;

  00052	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0005a	48 89 44 24 38	 mov	 QWORD PTR _My_data$2[rsp], rax

; 855  :         pointer& _Mylast = _My_data._Mylast;

  0005f	48 8b 44 24 38	 mov	 rax, QWORD PTR _My_data$2[rsp]
  00064	48 83 c0 08	 add	 rax, 8
  00068	48 89 44 24 20	 mov	 QWORD PTR _Mylast$1[rsp], rax

; 845  :             return _Emplace_back_with_unused_capacity(_STD forward<_Valty>(_Val)...);

  0006d	48 8b 44 24 40	 mov	 rax, QWORD PTR $T3[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00072	48 89 44 24 60	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 860  :             _STD _Construct_in_place(*_Mylast, _STD forward<_Valty>(_Val)...);

  00077	48 8b 44 24 20	 mov	 rax, QWORD PTR _Mylast$1[rsp]
  0007c	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0007f	48 89 44 24 48	 mov	 QWORD PTR _Obj$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00084	48 8b 44 24 48	 mov	 rax, QWORD PTR _Obj$[rsp]
  00089	48 89 44 24 50	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0008e	48 8b 44 24 50	 mov	 rax, QWORD PTR $T4[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  00093	48 89 44 24 58	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00098	48 8b 44 24 58	 mov	 rax, QWORD PTR $T5[rsp]
  0009d	48 89 44 24 68	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 860  :             _STD _Construct_in_place(*_Mylast, _STD forward<_Valty>(_Val)...);

  000a2	48 8b 44 24 60	 mov	 rax, QWORD PTR $T6[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  000a7	48 89 44 24 70	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  000ac	48 8b 7c 24 68	 mov	 rdi, QWORD PTR $T7[rsp]
  000b1	48 8b 74 24 70	 mov	 rsi, QWORD PTR $T8[rsp]
  000b6	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  000bb	f3 a4		 rep movsb
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 868  :         _Ty& _Result = *_Mylast;

  000bd	48 8b 44 24 20	 mov	 rax, QWORD PTR _Mylast$1[rsp]
  000c2	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000c5	48 89 44 24 78	 mov	 QWORD PTR _Result$9[rsp], rax

; 869  :         ++_Mylast;

  000ca	48 8b 44 24 20	 mov	 rax, QWORD PTR _Mylast$1[rsp]
  000cf	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000d2	48 05 a8 00 00
	00		 add	 rax, 168		; 000000a8H
  000d8	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Mylast$1[rsp]
  000dd	48 89 01	 mov	 QWORD PTR [rcx], rax

; 870  : 
; 871  :         return _Result;

  000e0	48 8b 44 24 78	 mov	 rax, QWORD PTR _Result$9[rsp]
  000e5	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax

; 845  :             return _Emplace_back_with_unused_capacity(_STD forward<_Valty>(_Val)...);

  000ed	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  000f5	eb 30		 jmp	 SHORT $LN1@Emplace_on
$LN2@Emplace_on:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  000f7	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR <_Val_0>$[rsp]
  000ff	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 848  :         return *_Emplace_reallocate(_Mylast, _STD forward<_Valty>(_Val)...);

  00107	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  0010f	4c 8b c0	 mov	 r8, rax
  00112	48 8b 44 24 30	 mov	 rax, QWORD PTR _Mylast$[rsp]
  00117	48 8b 10	 mov	 rdx, QWORD PTR [rax]
  0011a	48 8b 8c 24 b0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00122	e8 00 00 00 00	 call	 ??$_Emplace_reallocate@AEBUEventScheduleDate@mu2@@@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAPEAUEventScheduleDate@mu2@@QEAU23@AEBU23@@Z ; std::vector<mu2::EventScheduleDate,std::allocator<mu2::EventScheduleDate> >::_Emplace_reallocate<mu2::EventScheduleDate const &>
$LN1@Emplace_on:

; 849  :     }

  00127	48 81 c4 98 00
	00 00		 add	 rsp, 152		; 00000098H
  0012e	5f		 pop	 rdi
  0012f	5e		 pop	 rsi
  00130	c3		 ret	 0
??$_Emplace_one_at_back@AEBUEventScheduleDate@mu2@@@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAAEAUEventScheduleDate@mu2@@AEBU23@@Z ENDP ; std::vector<mu2::EventScheduleDate,std::allocator<mu2::EventScheduleDate> >::_Emplace_one_at_back<mu2::EventScheduleDate const &>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
;	COMDAT ??$_Sort_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@0_JV<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z
_TEXT	SEGMENT
_Last$ = 32
tv85 = 40
_Mid$1 = 48
_First$ = 80
_Last$ = 88
_Ideal$ = 96
_Pred$ = 104
??$_Sort_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@0_JV<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z PROC ; std::_Sort_unchecked<mu2::EventScheduleDate *,<lambda_5acf0176913ce2eb7dca573a416bd931> >, COMDAT

; 8369 : _CONSTEXPR20 void _Sort_unchecked(_RanIt _First, _RanIt _Last, _Iter_diff_t<_RanIt> _Ideal, _Pr _Pred) {

  00000	44 88 4c 24 20	 mov	 BYTE PTR [rsp+32], r9b
  00005	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	48 83 ec 48	 sub	 rsp, 72			; 00000048H
$LN2@Sort_unche:

; 8370 :     // order [_First, _Last)
; 8371 :     for (;;) {
; 8372 :         if (_Last - _First <= _ISORT_MAX) { // small

  00018	48 8b 44 24 50	 mov	 rax, QWORD PTR _First$[rsp]
  0001d	48 8b 4c 24 58	 mov	 rcx, QWORD PTR _Last$[rsp]
  00022	48 2b c8	 sub	 rcx, rax
  00025	48 8b c1	 mov	 rax, rcx
  00028	48 99		 cdq
  0002a	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  0002f	48 f7 f9	 idiv	 rcx
  00032	48 83 f8 20	 cmp	 rax, 32			; 00000020H
  00036	7f 1b		 jg	 SHORT $LN5@Sort_unche

; 8373 :             _STD _Insertion_sort_unchecked(_First, _Last, _Pred);

  00038	44 0f b6 44 24
	68		 movzx	 r8d, BYTE PTR _Pred$[rsp]
  0003e	48 8b 54 24 58	 mov	 rdx, QWORD PTR _Last$[rsp]
  00043	48 8b 4c 24 50	 mov	 rcx, QWORD PTR _First$[rsp]
  00048	e8 00 00 00 00	 call	 ??$_Insertion_sort_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAPEAUEventScheduleDate@mu2@@QEAU12@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z ; std::_Insertion_sort_unchecked<mu2::EventScheduleDate *,<lambda_5acf0176913ce2eb7dca573a416bd931> >
  0004d	90		 npad	 1

; 8374 :             return;

  0004e	e9 3e 01 00 00	 jmp	 $LN1@Sort_unche
$LN5@Sort_unche:

; 8375 :         }
; 8376 : 
; 8377 :         if (_Ideal <= 0) { // heap sort if too many divisions

  00053	48 83 7c 24 60
	00		 cmp	 QWORD PTR _Ideal$[rsp], 0
  00059	7f 6e		 jg	 SHORT $LN6@Sort_unche

; 8378 :             _STD _Make_heap_unchecked(_First, _Last, _Pred);

  0005b	44 0f b6 44 24
	68		 movzx	 r8d, BYTE PTR _Pred$[rsp]
  00061	48 8b 54 24 58	 mov	 rdx, QWORD PTR _Last$[rsp]
  00066	48 8b 4c 24 50	 mov	 rcx, QWORD PTR _First$[rsp]
  0006b	e8 00 00 00 00	 call	 ??$_Make_heap_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z ; std::_Make_heap_unchecked<mu2::EventScheduleDate *,<lambda_5acf0176913ce2eb7dca573a416bd931> >

; 8379 :             _STD _Sort_heap_unchecked(_First, _Last, _Pred);

  00070	48 8b 44 24 58	 mov	 rax, QWORD PTR _Last$[rsp]
  00075	48 89 44 24 20	 mov	 QWORD PTR _Last$[rsp], rax

; 7148 :     for (; _Last - _First >= 2; --_Last) {

  0007a	eb 10		 jmp	 SHORT $LN120@Sort_unche
$LN118@Sort_unche:
  0007c	48 8b 44 24 20	 mov	 rax, QWORD PTR _Last$[rsp]
  00081	48 2d a8 00 00
	00		 sub	 rax, 168		; 000000a8H
  00087	48 89 44 24 20	 mov	 QWORD PTR _Last$[rsp], rax
$LN120@Sort_unche:
  0008c	48 8b 44 24 50	 mov	 rax, QWORD PTR _First$[rsp]
  00091	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Last$[rsp]
  00096	48 2b c8	 sub	 rcx, rax
  00099	48 8b c1	 mov	 rax, rcx
  0009c	48 99		 cdq
  0009e	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  000a3	48 f7 f9	 idiv	 rcx
  000a6	48 83 f8 02	 cmp	 rax, 2
  000aa	7c 18		 jl	 SHORT $LN119@Sort_unche

; 7149 :         _STD _Pop_heap_unchecked(_First, _Last, _Pred);

  000ac	44 0f b6 44 24
	68		 movzx	 r8d, BYTE PTR _Pred$[rsp]
  000b2	48 8b 54 24 20	 mov	 rdx, QWORD PTR _Last$[rsp]
  000b7	48 8b 4c 24 50	 mov	 rcx, QWORD PTR _First$[rsp]
  000bc	e8 00 00 00 00	 call	 ??$_Pop_heap_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z ; std::_Pop_heap_unchecked<mu2::EventScheduleDate *,<lambda_5acf0176913ce2eb7dca573a416bd931> >
  000c1	90		 npad	 1

; 7150 :     }

  000c2	eb b8		 jmp	 SHORT $LN118@Sort_unche
$LN119@Sort_unche:

; 8380 :             return;

  000c4	e9 c8 00 00 00	 jmp	 $LN1@Sort_unche
$LN6@Sort_unche:

; 8381 :         }
; 8382 : 
; 8383 :         // divide and conquer by quicksort
; 8384 :         auto _Mid = _STD _Partition_by_median_guess_unchecked(_First, _Last, _Pred);

  000c9	44 0f b6 4c 24
	68		 movzx	 r9d, BYTE PTR _Pred$[rsp]
  000cf	4c 8b 44 24 58	 mov	 r8, QWORD PTR _Last$[rsp]
  000d4	48 8b 54 24 50	 mov	 rdx, QWORD PTR _First$[rsp]
  000d9	48 8d 4c 24 30	 lea	 rcx, QWORD PTR _Mid$1[rsp]
  000de	e8 00 00 00 00	 call	 ??$_Partition_by_median_guess_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YA?AU?$pair@PEAUEventScheduleDate@mu2@@PEAU12@@0@PEAUEventScheduleDate@mu2@@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z ; std::_Partition_by_median_guess_unchecked<mu2::EventScheduleDate *,<lambda_5acf0176913ce2eb7dca573a416bd931> >

; 8385 : 
; 8386 :         _Ideal = (_Ideal >> 1) + (_Ideal >> 2); // allow 1.5 log2(N) divisions

  000e3	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ideal$[rsp]
  000e8	48 d1 f8	 sar	 rax, 1
  000eb	48 8b 4c 24 60	 mov	 rcx, QWORD PTR _Ideal$[rsp]
  000f0	48 c1 f9 02	 sar	 rcx, 2
  000f4	48 03 c1	 add	 rax, rcx
  000f7	48 89 44 24 60	 mov	 QWORD PTR _Ideal$[rsp], rax

; 8387 : 
; 8388 :         if (_Mid.first - _First < _Last - _Mid.second) { // loop on second half

  000fc	48 8b 44 24 50	 mov	 rax, QWORD PTR _First$[rsp]
  00101	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Mid$1[rsp]
  00106	48 2b c8	 sub	 rcx, rax
  00109	48 8b c1	 mov	 rax, rcx
  0010c	48 99		 cdq
  0010e	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  00113	48 f7 f9	 idiv	 rcx
  00116	48 89 44 24 28	 mov	 QWORD PTR tv85[rsp], rax
  0011b	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Mid$1[rsp+8]
  00120	48 8b 54 24 58	 mov	 rdx, QWORD PTR _Last$[rsp]
  00125	48 2b d1	 sub	 rdx, rcx
  00128	48 8b ca	 mov	 rcx, rdx
  0012b	48 8b c1	 mov	 rax, rcx
  0012e	48 99		 cdq
  00130	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  00135	48 f7 f9	 idiv	 rcx
  00138	48 8b 4c 24 28	 mov	 rcx, QWORD PTR tv85[rsp]
  0013d	48 3b c8	 cmp	 rcx, rax
  00140	7d 26		 jge	 SHORT $LN7@Sort_unche

; 8389 :             _STD _Sort_unchecked(_First, _Mid.first, _Ideal, _Pred);

  00142	44 0f b6 4c 24
	68		 movzx	 r9d, BYTE PTR _Pred$[rsp]
  00148	4c 8b 44 24 60	 mov	 r8, QWORD PTR _Ideal$[rsp]
  0014d	48 8b 54 24 30	 mov	 rdx, QWORD PTR _Mid$1[rsp]
  00152	48 8b 4c 24 50	 mov	 rcx, QWORD PTR _First$[rsp]
  00157	e8 00 00 00 00	 call	 ??$_Sort_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@0_JV<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z ; std::_Sort_unchecked<mu2::EventScheduleDate *,<lambda_5acf0176913ce2eb7dca573a416bd931> >

; 8390 :             _First = _Mid.second;

  0015c	48 8b 44 24 38	 mov	 rax, QWORD PTR _Mid$1[rsp+8]
  00161	48 89 44 24 50	 mov	 QWORD PTR _First$[rsp], rax

; 8391 :         } else { // loop on first half

  00166	eb 24		 jmp	 SHORT $LN8@Sort_unche
$LN7@Sort_unche:

; 8392 :             _STD _Sort_unchecked(_Mid.second, _Last, _Ideal, _Pred);

  00168	44 0f b6 4c 24
	68		 movzx	 r9d, BYTE PTR _Pred$[rsp]
  0016e	4c 8b 44 24 60	 mov	 r8, QWORD PTR _Ideal$[rsp]
  00173	48 8b 54 24 58	 mov	 rdx, QWORD PTR _Last$[rsp]
  00178	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Mid$1[rsp+8]
  0017d	e8 00 00 00 00	 call	 ??$_Sort_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@0_JV<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z ; std::_Sort_unchecked<mu2::EventScheduleDate *,<lambda_5acf0176913ce2eb7dca573a416bd931> >

; 8393 :             _Last = _Mid.first;

  00182	48 8b 44 24 30	 mov	 rax, QWORD PTR _Mid$1[rsp]
  00187	48 89 44 24 58	 mov	 QWORD PTR _Last$[rsp], rax
$LN8@Sort_unche:

; 8394 :         }
; 8395 :     }

  0018c	e9 87 fe ff ff	 jmp	 $LN2@Sort_unche
$LN1@Sort_unche:

; 8396 : }

  00191	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00195	c3		 ret	 0
??$_Sort_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@0_JV<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z ENDP ; std::_Sort_unchecked<mu2::EventScheduleDate *,<lambda_5acf0176913ce2eb7dca573a416bd931> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm
;	COMDAT ??$sort@V?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@UEventScheduleDate@mu2@@@std@@@std@@@std@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXV?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@UEventScheduleDate@mu2@@@std@@@std@@@0@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z
_TEXT	SEGMENT
_Pass_by_value$1 = 32
$T2 = 33
_ULast$ = 40
_UFirst$ = 48
_Ptr$ = 56
$T3 = 64
$T4 = 72
$T5 = 80
_Ptr$ = 88
$T6 = 96
$T7 = 104
$T8 = 112
$T9 = 120
_First$ = 144
_Last$ = 152
_Pred$ = 160
??$sort@V?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@UEventScheduleDate@mu2@@@std@@@std@@@std@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXV?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@UEventScheduleDate@mu2@@@std@@@std@@@0@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z PROC ; std::sort<std::_Vector_iterator<std::_Vector_val<std::_Simple_types<mu2::EventScheduleDate> > >,<lambda_5acf0176913ce2eb7dca573a416bd931> >, COMDAT

; 8399 : _CONSTEXPR20 void sort(const _RanIt _First, const _RanIt _Last, _Pr _Pred) { // order [_First, _Last)

  00000	44 88 44 24 18	 mov	 BYTE PTR [rsp+24], r8b
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 345  :         return _STD _Unfancy_maybe_null(this->_Ptr);

  00016	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR _First$[rsp]
  0001e	48 89 44 24 38	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 80   :     return _Ptr;

  00023	48 8b 44 24 38	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00028	48 89 44 24 40	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 345  :         return _STD _Unfancy_maybe_null(this->_Ptr);

  0002d	48 8b 44 24 40	 mov	 rax, QWORD PTR $T3[rsp]
  00032	48 89 44 24 48	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 1384 :         return static_cast<_Iter&&>(_It)._Unwrapped();

  00037	48 8b 44 24 48	 mov	 rax, QWORD PTR $T4[rsp]
  0003c	48 89 44 24 50	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm

; 8401 :     const auto _UFirst = _STD _Get_unwrapped(_First);

  00041	48 8b 44 24 50	 mov	 rax, QWORD PTR $T5[rsp]
  00046	48 89 44 24 30	 mov	 QWORD PTR _UFirst$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 345  :         return _STD _Unfancy_maybe_null(this->_Ptr);

  0004b	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR _Last$[rsp]
  00053	48 89 44 24 58	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 80   :     return _Ptr;

  00058	48 8b 44 24 58	 mov	 rax, QWORD PTR _Ptr$[rsp]
  0005d	48 89 44 24 60	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 345  :         return _STD _Unfancy_maybe_null(this->_Ptr);

  00062	48 8b 44 24 60	 mov	 rax, QWORD PTR $T6[rsp]
  00067	48 89 44 24 68	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 1384 :         return static_cast<_Iter&&>(_It)._Unwrapped();

  0006c	48 8b 44 24 68	 mov	 rax, QWORD PTR $T7[rsp]
  00071	48 89 44 24 70	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm

; 8402 :     const auto _ULast  = _STD _Get_unwrapped(_Last);

  00076	48 8b 44 24 70	 mov	 rax, QWORD PTR $T8[rsp]
  0007b	48 89 44 24 28	 mov	 QWORD PTR _ULast$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 772  :     constexpr bool _Pass_by_value = conjunction_v<bool_constant<sizeof(_Fn) <= sizeof(void*)>,

  00080	c6 44 24 20 01	 mov	 BYTE PTR _Pass_by_value$1[rsp], 1

; 773  :         is_trivially_copy_constructible<_Fn>, is_trivially_destructible<_Fn>>;
; 774  :     if constexpr (_Pass_by_value) {
; 775  :         return _Func;

  00085	48 8d 44 24 21	 lea	 rax, QWORD PTR $T2[rsp]
  0008a	48 89 44 24 78	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm

; 8403 :     _STD _Sort_unchecked(_UFirst, _ULast, _ULast - _UFirst, _STD _Pass_fn(_Pred));

  0008f	48 8b 44 24 30	 mov	 rax, QWORD PTR _UFirst$[rsp]
  00094	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _ULast$[rsp]
  00099	48 2b c8	 sub	 rcx, rax
  0009c	48 8b c1	 mov	 rax, rcx
  0009f	48 99		 cdq
  000a1	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  000a6	48 f7 f9	 idiv	 rcx
  000a9	48 8b 4c 24 78	 mov	 rcx, QWORD PTR $T9[rsp]
  000ae	44 0f b6 09	 movzx	 r9d, BYTE PTR [rcx]
  000b2	4c 8b c0	 mov	 r8, rax
  000b5	48 8b 54 24 28	 mov	 rdx, QWORD PTR _ULast$[rsp]
  000ba	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _UFirst$[rsp]
  000bf	e8 00 00 00 00	 call	 ??$_Sort_unchecked@PEAUEventScheduleDate@mu2@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXPEAUEventScheduleDate@mu2@@0_JV<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z ; std::_Sort_unchecked<mu2::EventScheduleDate *,<lambda_5acf0176913ce2eb7dca573a416bd931> >
  000c4	90		 npad	 1

; 8404 : }

  000c5	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  000cc	c3		 ret	 0
??$sort@V?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@UEventScheduleDate@mu2@@@std@@@std@@@std@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXV?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@UEventScheduleDate@mu2@@@std@@@std@@@0@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z ENDP ; std::sort<std::_Vector_iterator<std::_Vector_val<std::_Simple_types<mu2::EventScheduleDate> > >,<lambda_5acf0176913ce2eb7dca573a416bd931> >
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??_GEventSchedule@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GEventSchedule@mu2@@UEAAPEAXI@Z PROC			; mu2::EventSchedule::`scalar deleting destructor', COMDAT
$LN5:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00012	e8 00 00 00 00	 call	 ??1EventSchedule@mu2@@UEAA@XZ ; mu2::EventSchedule::~EventSchedule
  00017	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  0001b	83 e0 01	 and	 eax, 1
  0001e	85 c0		 test	 eax, eax
  00020	74 10		 je	 SHORT $LN2@scalar
  00022	ba e8 00 00 00	 mov	 edx, 232		; 000000e8H
  00027	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0002c	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00031	90		 npad	 1
$LN2@scalar:
  00032	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00037	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0003b	c3		 ret	 0
??_GEventSchedule@mu2@@UEAAPEAXI@Z ENDP			; mu2::EventSchedule::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ?_Xlength@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@CAXXZ
_TEXT	SEGMENT
?_Xlength@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@CAXXZ PROC ; std::vector<mu2::EventScheduleDate,std::allocator<mu2::EventScheduleDate> >::_Xlength, COMDAT

; 2183 :     [[noreturn]] static void _Xlength() {

$LN3:
  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 2184 :         _Xlength_error("vector too long");

  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_0BA@FOIKENOD@vector?5too?5long@
  0000b	e8 00 00 00 00	 call	 ?_Xlength_error@std@@YAXPEBD@Z ; std::_Xlength_error
  00010	90		 npad	 1
$LN2@Xlength:

; 2185 :     }

  00011	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00015	c3		 ret	 0
?_Xlength@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@CAXXZ ENDP ; std::vector<mu2::EventScheduleDate,std::allocator<mu2::EventScheduleDate> >::_Xlength
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ?_Tidy@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAXXZ
_TEXT	SEGMENT
_Myfirst$ = 32
_My_data$ = 40
_Bytes$ = 48
_Ptr$ = 56
_Mylast$ = 64
_Myend$ = 72
$T1 = 80
$T2 = 88
_Count$ = 96
_Ptr$ = 104
_Al$ = 112
_Last$ = 120
_First$ = 128
this$ = 160
?_Tidy@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAXXZ PROC ; std::vector<mu2::EventScheduleDate,std::allocator<mu2::EventScheduleDate> >::_Tidy, COMDAT

; 2081 :     _CONSTEXPR20 void _Tidy() noexcept { // free all storage

$LN40:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec 98 00
	00 00		 sub	 rsp, 152		; 00000098H

; 2227 :         return _Mypair._Get_first();

  0000c	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00014	48 89 44 24 50	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2227 :         return _Mypair._Get_first();

  00019	48 8b 44 24 50	 mov	 rax, QWORD PTR $T1[rsp]
  0001e	48 89 44 24 58	 mov	 QWORD PTR $T2[rsp], rax

; 2082 :         auto& _Al         = _Getal();

  00023	48 8b 44 24 58	 mov	 rax, QWORD PTR $T2[rsp]
  00028	48 89 44 24 70	 mov	 QWORD PTR _Al$[rsp], rax

; 2083 :         auto& _My_data    = _Mypair._Myval2;

  0002d	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00035	48 89 44 24 28	 mov	 QWORD PTR _My_data$[rsp], rax

; 2084 :         pointer& _Myfirst = _My_data._Myfirst;

  0003a	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  0003f	48 89 44 24 20	 mov	 QWORD PTR _Myfirst$[rsp], rax

; 2085 :         pointer& _Mylast  = _My_data._Mylast;

  00044	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00049	48 83 c0 08	 add	 rax, 8
  0004d	48 89 44 24 40	 mov	 QWORD PTR _Mylast$[rsp], rax

; 2086 :         pointer& _Myend   = _My_data._Myend;

  00052	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00057	48 83 c0 10	 add	 rax, 16
  0005b	48 89 44 24 48	 mov	 QWORD PTR _Myend$[rsp], rax

; 2087 : 
; 2088 :         _My_data._Orphan_all();
; 2089 : 
; 2090 :         if (_Myfirst) { // destroy and deallocate old array

  00060	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  00065	48 83 38 00	 cmp	 QWORD PTR [rax], 0
  00069	0f 84 b3 00 00
	00		 je	 $LN2@Tidy

; 2091 :             _STD _Destroy_range(_Myfirst, _Mylast, _Al);

  0006f	48 8b 44 24 40	 mov	 rax, QWORD PTR _Mylast$[rsp]
  00074	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00077	48 89 44 24 78	 mov	 QWORD PTR _Last$[rsp], rax
  0007c	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  00081	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00084	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _First$[rsp], rax

; 2092 :             _ASAN_VECTOR_REMOVE;
; 2093 :             _Al.deallocate(_Myfirst, static_cast<size_type>(_Myend - _Myfirst));

  0008c	48 8b 44 24 48	 mov	 rax, QWORD PTR _Myend$[rsp]
  00091	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Myfirst$[rsp]
  00096	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00099	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0009c	48 2b c1	 sub	 rax, rcx
  0009f	48 99		 cdq
  000a1	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  000a6	48 f7 f9	 idiv	 rcx
  000a9	48 89 44 24 60	 mov	 QWORD PTR _Count$[rsp], rax
  000ae	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  000b3	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000b6	48 89 44 24 68	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  000bb	48 69 44 24 60
	a8 00 00 00	 imul	 rax, QWORD PTR _Count$[rsp], 168 ; 000000a8H
  000c4	48 89 44 24 30	 mov	 QWORD PTR _Bytes$[rsp], rax
  000c9	48 8b 44 24 68	 mov	 rax, QWORD PTR _Ptr$[rsp]
  000ce	48 89 44 24 38	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  000d3	48 81 7c 24 30
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  000dc	72 10		 jb	 SHORT $LN31@Tidy

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  000de	48 8d 54 24 30	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  000e3	48 8d 4c 24 38	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  000e8	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  000ed	90		 npad	 1
$LN31@Tidy:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  000ee	48 8b 54 24 30	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  000f3	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  000f8	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  000fd	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2095 :             _Myfirst = nullptr;

  000fe	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  00103	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0

; 2096 :             _Mylast  = nullptr;

  0010a	48 8b 44 24 40	 mov	 rax, QWORD PTR _Mylast$[rsp]
  0010f	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0

; 2097 :             _Myend   = nullptr;

  00116	48 8b 44 24 48	 mov	 rax, QWORD PTR _Myend$[rsp]
  0011b	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0
$LN2@Tidy:

; 2098 :         }
; 2099 :     }

  00122	48 81 c4 98 00
	00 00		 add	 rsp, 152		; 00000098H
  00129	c3		 ret	 0
?_Tidy@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAXXZ ENDP ; std::vector<mu2::EventScheduleDate,std::allocator<mu2::EventScheduleDate> >::_Tidy
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ?_Change_array@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAXQEAUEventScheduleDate@mu2@@_K1@Z
_TEXT	SEGMENT
_Myfirst$ = 32
_My_data$ = 40
_Bytes$ = 48
_Ptr$ = 56
_Mylast$ = 64
_Myend$ = 72
$T1 = 80
$T2 = 88
_Count$ = 96
_Ptr$ = 104
_Al$ = 112
_Last$ = 120
_First$ = 128
this$ = 160
_Newvec$ = 168
_Newsize$ = 176
_Newcapacity$ = 184
?_Change_array@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAXQEAUEventScheduleDate@mu2@@_K1@Z PROC ; std::vector<mu2::EventScheduleDate,std::allocator<mu2::EventScheduleDate> >::_Change_array, COMDAT

; 2059 :         const pointer _Newvec, const size_type _Newsize, const size_type _Newcapacity) noexcept {

$LN40:
  00000	4c 89 4c 24 20	 mov	 QWORD PTR [rsp+32], r9
  00005	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	48 81 ec 98 00
	00 00		 sub	 rsp, 152		; 00000098H

; 2227 :         return _Mypair._Get_first();

  0001b	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00023	48 89 44 24 50	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2227 :         return _Mypair._Get_first();

  00028	48 8b 44 24 50	 mov	 rax, QWORD PTR $T1[rsp]
  0002d	48 89 44 24 58	 mov	 QWORD PTR $T2[rsp], rax

; 2060 :         // orphan all iterators, discard old array, acquire new array
; 2061 :         auto& _Al         = _Getal();

  00032	48 8b 44 24 58	 mov	 rax, QWORD PTR $T2[rsp]
  00037	48 89 44 24 70	 mov	 QWORD PTR _Al$[rsp], rax

; 2062 :         auto& _My_data    = _Mypair._Myval2;

  0003c	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00044	48 89 44 24 28	 mov	 QWORD PTR _My_data$[rsp], rax

; 2063 :         pointer& _Myfirst = _My_data._Myfirst;

  00049	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  0004e	48 89 44 24 20	 mov	 QWORD PTR _Myfirst$[rsp], rax

; 2064 :         pointer& _Mylast  = _My_data._Mylast;

  00053	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00058	48 83 c0 08	 add	 rax, 8
  0005c	48 89 44 24 40	 mov	 QWORD PTR _Mylast$[rsp], rax

; 2065 :         pointer& _Myend   = _My_data._Myend;

  00061	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00066	48 83 c0 10	 add	 rax, 16
  0006a	48 89 44 24 48	 mov	 QWORD PTR _Myend$[rsp], rax

; 2066 : 
; 2067 :         _My_data._Orphan_all();
; 2068 : 
; 2069 :         if (_Myfirst) { // destroy and deallocate old array

  0006f	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  00074	48 83 38 00	 cmp	 QWORD PTR [rax], 0
  00078	0f 84 8f 00 00
	00		 je	 $LN2@Change_arr

; 2070 :             _STD _Destroy_range(_Myfirst, _Mylast, _Al);

  0007e	48 8b 44 24 40	 mov	 rax, QWORD PTR _Mylast$[rsp]
  00083	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00086	48 89 44 24 78	 mov	 QWORD PTR _Last$[rsp], rax
  0008b	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  00090	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00093	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _First$[rsp], rax

; 2071 :             _ASAN_VECTOR_REMOVE;
; 2072 :             _Al.deallocate(_Myfirst, static_cast<size_type>(_Myend - _Myfirst));

  0009b	48 8b 44 24 48	 mov	 rax, QWORD PTR _Myend$[rsp]
  000a0	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Myfirst$[rsp]
  000a5	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  000a8	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000ab	48 2b c1	 sub	 rax, rcx
  000ae	48 99		 cdq
  000b0	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  000b5	48 f7 f9	 idiv	 rcx
  000b8	48 89 44 24 60	 mov	 QWORD PTR _Count$[rsp], rax
  000bd	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  000c2	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000c5	48 89 44 24 68	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  000ca	48 69 44 24 60
	a8 00 00 00	 imul	 rax, QWORD PTR _Count$[rsp], 168 ; 000000a8H
  000d3	48 89 44 24 30	 mov	 QWORD PTR _Bytes$[rsp], rax
  000d8	48 8b 44 24 68	 mov	 rax, QWORD PTR _Ptr$[rsp]
  000dd	48 89 44 24 38	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  000e2	48 81 7c 24 30
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  000eb	72 10		 jb	 SHORT $LN31@Change_arr

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  000ed	48 8d 54 24 30	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  000f2	48 8d 4c 24 38	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  000f7	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  000fc	90		 npad	 1
$LN31@Change_arr:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  000fd	48 8b 54 24 30	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  00102	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  00107	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  0010c	90		 npad	 1
$LN2@Change_arr:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2075 :         _Myfirst = _Newvec;

  0010d	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  00112	48 8b 8c 24 a8
	00 00 00	 mov	 rcx, QWORD PTR _Newvec$[rsp]
  0011a	48 89 08	 mov	 QWORD PTR [rax], rcx

; 2076 :         _Mylast  = _Newvec + _Newsize;

  0011d	48 69 84 24 b0
	00 00 00 a8 00
	00 00		 imul	 rax, QWORD PTR _Newsize$[rsp], 168 ; 000000a8H
  00129	48 8b 8c 24 a8
	00 00 00	 mov	 rcx, QWORD PTR _Newvec$[rsp]
  00131	48 03 c8	 add	 rcx, rax
  00134	48 8b c1	 mov	 rax, rcx
  00137	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Mylast$[rsp]
  0013c	48 89 01	 mov	 QWORD PTR [rcx], rax

; 2077 :         _Myend   = _Newvec + _Newcapacity;

  0013f	48 69 84 24 b8
	00 00 00 a8 00
	00 00		 imul	 rax, QWORD PTR _Newcapacity$[rsp], 168 ; 000000a8H
  0014b	48 8b 8c 24 a8
	00 00 00	 mov	 rcx, QWORD PTR _Newvec$[rsp]
  00153	48 03 c8	 add	 rcx, rax
  00156	48 8b c1	 mov	 rax, rcx
  00159	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _Myend$[rsp]
  0015e	48 89 01	 mov	 QWORD PTR [rcx], rax

; 2078 :         _ASAN_VECTOR_CREATE;
; 2079 :     }

  00161	48 81 c4 98 00
	00 00		 add	 rsp, 152		; 00000098H
  00168	c3		 ret	 0
?_Change_array@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAXQEAUEventScheduleDate@mu2@@_K1@Z ENDP ; std::vector<mu2::EventScheduleDate,std::allocator<mu2::EventScheduleDate> >::_Change_array
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ?_Calculate_growth@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEBA_K_K@Z
_TEXT	SEGMENT
_Oldcapacity$ = 0
_My_data$1 = 8
$T2 = 16
$T3 = 24
tv82 = 32
_Max$ = 40
_Geometric$ = 48
$T4 = 56
$T5 = 64
$T6 = 72
$T7 = 80
$T8 = 88
$T9 = 96
$T10 = 104
$T11 = 112
_Unsigned_max$12 = 120
this$ = 144
_Newsize$ = 152
?_Calculate_growth@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEBA_K_K@Z PROC ; std::vector<mu2::EventScheduleDate,std::allocator<mu2::EventScheduleDate> >::_Calculate_growth, COMDAT

; 2006 :     _CONSTEXPR20 size_type _Calculate_growth(const size_type _Newsize) const {

$LN42:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H

; 1923 :         auto& _My_data = _Mypair._Myval2;

  00011	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00019	48 89 44 24 08	 mov	 QWORD PTR _My_data$1[rsp], rax

; 1924 :         return static_cast<size_type>(_My_data._Myend - _My_data._Myfirst);

  0001e	48 8b 44 24 08	 mov	 rax, QWORD PTR _My_data$1[rsp]
  00023	48 8b 4c 24 08	 mov	 rcx, QWORD PTR _My_data$1[rsp]
  00028	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0002b	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0002f	48 2b c1	 sub	 rax, rcx
  00032	48 99		 cdq
  00034	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  00039	48 f7 f9	 idiv	 rcx
  0003c	48 89 44 24 38	 mov	 QWORD PTR $T4[rsp], rax

; 2007 :         // given _Oldcapacity and _Newsize, calculate geometric growth
; 2008 :         const size_type _Oldcapacity = capacity();

  00041	48 8b 44 24 38	 mov	 rax, QWORD PTR $T4[rsp]
  00046	48 89 04 24	 mov	 QWORD PTR _Oldcapacity$[rsp], rax

; 2231 :         return _Mypair._Get_first();

  0004a	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1543 :         return *this;

  00052	48 89 44 24 40	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2231 :         return _Mypair._Get_first();

  00057	48 8b 44 24 40	 mov	 rax, QWORD PTR $T5[rsp]
  0005c	48 89 44 24 70	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 746  :         return static_cast<size_t>(-1) / sizeof(value_type);

  00061	48 b8 86 61 18
	86 61 18 86 01	 mov	 rax, 109802048057794950	; 0186186186186186H
  0006b	48 89 44 24 48	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1919 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()), _Alty_traits::max_size(_Getal()));

  00070	48 8b 44 24 48	 mov	 rax, QWORD PTR $T6[rsp]
  00075	48 89 44 24 10	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 866  :         constexpr auto _Unsigned_max = static_cast<make_unsigned_t<_Ty>>(-1);

  0007a	48 c7 44 24 78
	ff ff ff ff	 mov	 QWORD PTR _Unsigned_max$12[rsp], -1

; 867  :         return static_cast<_Ty>(_Unsigned_max >> 1);

  00083	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  0008d	48 89 44 24 50	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1919 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()), _Alty_traits::max_size(_Getal()));

  00092	48 8b 44 24 50	 mov	 rax, QWORD PTR $T7[rsp]
  00097	48 89 44 24 18	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 101  :     return _Right < _Left ? _Right : _Left;

  0009c	48 8b 44 24 18	 mov	 rax, QWORD PTR $T3[rsp]
  000a1	48 39 44 24 10	 cmp	 QWORD PTR $T2[rsp], rax
  000a6	73 0c		 jae	 SHORT $LN37@Calculate_
  000a8	48 8d 44 24 10	 lea	 rax, QWORD PTR $T2[rsp]
  000ad	48 89 44 24 20	 mov	 QWORD PTR tv82[rsp], rax
  000b2	eb 0a		 jmp	 SHORT $LN38@Calculate_
$LN37@Calculate_:
  000b4	48 8d 44 24 18	 lea	 rax, QWORD PTR $T3[rsp]
  000b9	48 89 44 24 20	 mov	 QWORD PTR tv82[rsp], rax
$LN38@Calculate_:
  000be	48 8b 44 24 20	 mov	 rax, QWORD PTR tv82[rsp]
  000c3	48 89 44 24 58	 mov	 QWORD PTR $T8[rsp], rax
  000c8	48 8b 44 24 58	 mov	 rax, QWORD PTR $T8[rsp]
  000cd	48 89 44 24 60	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1919 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()), _Alty_traits::max_size(_Getal()));

  000d2	48 8b 44 24 60	 mov	 rax, QWORD PTR $T9[rsp]
  000d7	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000da	48 89 44 24 68	 mov	 QWORD PTR $T10[rsp], rax

; 2009 :         const auto _Max              = max_size();

  000df	48 8b 44 24 68	 mov	 rax, QWORD PTR $T10[rsp]
  000e4	48 89 44 24 28	 mov	 QWORD PTR _Max$[rsp], rax

; 2010 : 
; 2011 :         if (_Oldcapacity > _Max - _Oldcapacity / 2) {

  000e9	33 d2		 xor	 edx, edx
  000eb	48 8b 04 24	 mov	 rax, QWORD PTR _Oldcapacity$[rsp]
  000ef	b9 02 00 00 00	 mov	 ecx, 2
  000f4	48 f7 f1	 div	 rcx
  000f7	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Max$[rsp]
  000fc	48 2b c8	 sub	 rcx, rax
  000ff	48 8b c1	 mov	 rax, rcx
  00102	48 39 04 24	 cmp	 QWORD PTR _Oldcapacity$[rsp], rax
  00106	76 07		 jbe	 SHORT $LN2@Calculate_

; 2012 :             return _Max; // geometric growth would overflow

  00108	48 8b 44 24 28	 mov	 rax, QWORD PTR _Max$[rsp]
  0010d	eb 3b		 jmp	 SHORT $LN1@Calculate_
$LN2@Calculate_:

; 2013 :         }
; 2014 : 
; 2015 :         const size_type _Geometric = _Oldcapacity + _Oldcapacity / 2;

  0010f	33 d2		 xor	 edx, edx
  00111	48 8b 04 24	 mov	 rax, QWORD PTR _Oldcapacity$[rsp]
  00115	b9 02 00 00 00	 mov	 ecx, 2
  0011a	48 f7 f1	 div	 rcx
  0011d	48 8b 0c 24	 mov	 rcx, QWORD PTR _Oldcapacity$[rsp]
  00121	48 03 c8	 add	 rcx, rax
  00124	48 8b c1	 mov	 rax, rcx
  00127	48 89 44 24 30	 mov	 QWORD PTR _Geometric$[rsp], rax

; 2016 : 
; 2017 :         if (_Geometric < _Newsize) {

  0012c	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR _Newsize$[rsp]
  00134	48 39 44 24 30	 cmp	 QWORD PTR _Geometric$[rsp], rax
  00139	73 0a		 jae	 SHORT $LN3@Calculate_

; 2018 :             return _Newsize; // geometric growth would be insufficient

  0013b	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR _Newsize$[rsp]
  00143	eb 05		 jmp	 SHORT $LN1@Calculate_
$LN3@Calculate_:

; 2019 :         }
; 2020 : 
; 2021 :         return _Geometric; // geometric growth is sufficient

  00145	48 8b 44 24 30	 mov	 rax, QWORD PTR _Geometric$[rsp]
$LN1@Calculate_:

; 2022 :     }

  0014a	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  00151	c3		 ret	 0
?_Calculate_growth@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEBA_K_K@Z ENDP ; std::vector<mu2::EventScheduleDate,std::allocator<mu2::EventScheduleDate> >::_Calculate_growth
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?allocate@?$allocator@UEventScheduleDate@mu2@@@std@@QEAAPEAUEventScheduleDate@mu2@@_K@Z
_TEXT	SEGMENT
_Overflow_is_possible$1 = 32
_Bytes$ = 40
$T2 = 48
$T3 = 56
$T4 = 64
_Max_possible$5 = 72
this$ = 96
_Count$ = 104
?allocate@?$allocator@UEventScheduleDate@mu2@@@std@@QEAAPEAUEventScheduleDate@mu2@@_K@Z PROC ; std::allocator<mu2::EventScheduleDate>::allocate, COMDAT

; 988  :     _NODISCARD_RAW_PTR_ALLOC _CONSTEXPR20 __declspec(allocator) _Ty* allocate(_CRT_GUARDOVERFLOW const size_t _Count) {

$LN13:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 113  :     constexpr bool _Overflow_is_possible = _Ty_size > 1;

  0000e	c6 44 24 20 01	 mov	 BYTE PTR _Overflow_is_possible$1[rsp], 1

; 114  : 
; 115  :     if constexpr (_Overflow_is_possible) {
; 116  :         constexpr size_t _Max_possible = static_cast<size_t>(-1) / _Ty_size;

  00013	48 b8 86 61 18
	86 61 18 86 01	 mov	 rax, 109802048057794950	; 0186186186186186H
  0001d	48 89 44 24 48	 mov	 QWORD PTR _Max_possible$5[rsp], rax

; 117  :         if (_Count > _Max_possible) {

  00022	48 b8 86 61 18
	86 61 18 86 01	 mov	 rax, 109802048057794950	; 0186186186186186H
  0002c	48 39 44 24 68	 cmp	 QWORD PTR _Count$[rsp], rax
  00031	76 06		 jbe	 SHORT $LN4@allocate

; 118  :             _Throw_bad_array_new_length(); // multiply overflow

  00033	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00038	90		 npad	 1
$LN4@allocate:

; 119  :         }
; 120  :     }
; 121  : 
; 122  :     return _Count * _Ty_size;

  00039	48 69 44 24 68
	a8 00 00 00	 imul	 rax, QWORD PTR _Count$[rsp], 168 ; 000000a8H
  00042	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00047	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
  0004c	48 89 44 24 28	 mov	 QWORD PTR _Bytes$[rsp], rax

; 227  :     if (_Bytes == 0) {

  00051	48 83 7c 24 28
	00		 cmp	 QWORD PTR _Bytes$[rsp], 0
  00057	75 0b		 jne	 SHORT $LN8@allocate

; 228  :         return nullptr;

  00059	48 c7 44 24 30
	00 00 00 00	 mov	 QWORD PTR $T2[rsp], 0
  00062	eb 35		 jmp	 SHORT $LN7@allocate
$LN8@allocate:

; 229  :     }
; 230  : 
; 231  : #if _HAS_CXX20 // TRANSITION, GH-1532
; 232  :     if (_STD is_constant_evaluated()) {
; 233  :         return _Traits::_Allocate(_Bytes);
; 234  :     }
; 235  : #endif // _HAS_CXX20
; 236  : 
; 237  : #ifdef __cpp_aligned_new
; 238  :     if constexpr (_Align > __STDCPP_DEFAULT_NEW_ALIGNMENT__) {
; 239  :         size_t _Passed_align = _Align;
; 240  : #if defined(_M_IX86) || defined(_M_X64)
; 241  :         if (_Bytes >= _Big_allocation_threshold) {
; 242  :             // boost the alignment of big allocations to help autovectorization
; 243  :             _Passed_align = (_STD max)(_Align, _Big_allocation_alignment);
; 244  :         }
; 245  : #endif // defined(_M_IX86) || defined(_M_X64)
; 246  :         return _Traits::_Allocate_aligned(_Bytes, _Passed_align);
; 247  :     } else
; 248  : #endif // defined(__cpp_aligned_new)
; 249  :     {
; 250  : #if defined(_M_IX86) || defined(_M_X64)
; 251  :         if (_Bytes >= _Big_allocation_threshold) {

  00064	48 81 7c 24 28
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  0006d	72 11		 jb	 SHORT $LN9@allocate

; 252  :             // boost the alignment of big allocations to help autovectorization
; 253  :             return _Allocate_manually_vector_aligned<_Traits>(_Bytes);

  0006f	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00074	e8 00 00 00 00	 call	 ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
  00079	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
  0007e	eb 19		 jmp	 SHORT $LN7@allocate
$LN9@allocate:

; 136  :         return ::operator new(_Bytes);

  00080	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00085	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  0008a	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax

; 256  :         return _Traits::_Allocate(_Bytes);

  0008f	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  00094	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
$LN7@allocate:

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00099	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
$LN6@allocate:

; 991  :     }

  0009e	48 83 c4 58	 add	 rsp, 88			; 00000058H
  000a2	c3		 ret	 0
?allocate@?$allocator@UEventScheduleDate@mu2@@@std@@QEAAPEAUEventScheduleDate@mu2@@_K@Z ENDP ; std::allocator<mu2::EventScheduleDate>::allocate
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.h
;	COMDAT ?OnScheduleEvent@EventSchedule@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
_TEXT	SEGMENT
this$ = 8
e$ = 16
?OnScheduleEvent@EventSchedule@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z PROC ; mu2::EventSchedule::OnScheduleEvent, COMDAT

; 65   : 		virtual void OnScheduleEvent(EventPtr& e) { e; }

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?OnScheduleEvent@EventSchedule@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ENDP ; mu2::EventSchedule::OnScheduleEvent
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.h
;	COMDAT ?getReadyToStartTime@EventSchedule@mu2@@MEAAHXZ
_TEXT	SEGMENT
this$ = 8
?getReadyToStartTime@EventSchedule@mu2@@MEAAHXZ PROC	; mu2::EventSchedule::getReadyToStartTime, COMDAT

; 50   : 		virtual Int32 getReadyToStartTime() { return 0; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	33 c0		 xor	 eax, eax
  00007	c3		 ret	 0
?getReadyToStartTime@EventSchedule@mu2@@MEAAHXZ ENDP	; mu2::EventSchedule::getReadyToStartTime
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
;	COMDAT ?getWeekStartTime@EventSchedule@mu2@@IEAA_JXZ
_TEXT	SEGMENT
currentTime$ = 32
$T1 = 40
$T2 = 48
$T3 = 56
$T4 = 64
weekStartTime$ = 72
currentTime_tm$ = 80
__$ArrayPad$ = 120
this$ = 144
?getWeekStartTime@EventSchedule@mu2@@IEAA_JXZ PROC	; mu2::EventSchedule::getWeekStartTime, COMDAT

; 17   : 	{

$LN13:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H
  0000c	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR __security_cookie
  00013	48 33 c4	 xor	 rax, rsp
  00016	48 89 44 24 78	 mov	 QWORD PTR __$ArrayPad$[rsp], rax
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h

; 552  :             return _time64(_Time);

  0001b	33 c9		 xor	 ecx, ecx
  0001d	e8 00 00 00 00	 call	 _time64
  00022	48 89 44 24 28	 mov	 QWORD PTR $T1[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 19   : 		time_t currentTime = time(nullptr);

  00027	48 8b 44 24 28	 mov	 rax, QWORD PTR $T1[rsp]
  0002c	48 89 44 24 20	 mov	 QWORD PTR currentTime$[rsp], rax
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h

; 617  :                 return _localtime64_s(_Tm, _Time);

  00031	48 8d 54 24 20	 lea	 rdx, QWORD PTR currentTime$[rsp]
  00036	48 8d 4c 24 50	 lea	 rcx, QWORD PTR currentTime_tm$[rsp]
  0003b	e8 00 00 00 00	 call	 _localtime64_s
  00040	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.h

; 42   : 		inline time_t dayToSecond(const Int32& week)	{ return week * 24 * 60 * 60; }

  00041	6b 44 24 68 18	 imul	 eax, DWORD PTR currentTime_tm$[rsp+24], 24
  00046	6b c0 3c	 imul	 eax, eax, 60		; 0000003cH
  00049	6b c0 3c	 imul	 eax, eax, 60		; 0000003cH
  0004c	48 98		 cdqe
  0004e	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax

; 43   : 		inline time_t hourToSecond(const Int32& hour)	{ return hour * 60 * 60; }

  00053	6b 44 24 58 3c	 imul	 eax, DWORD PTR currentTime_tm$[rsp+8], 60 ; 0000003cH
  00058	6b c0 3c	 imul	 eax, eax, 60		; 0000003cH
  0005b	48 98		 cdqe
  0005d	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax

; 44   : 		inline time_t minToSecond(const Int32& min)		{ return min * 60; }

  00062	6b 44 24 54 3c	 imul	 eax, DWORD PTR currentTime_tm$[rsp+4], 60 ; 0000003cH
  00067	48 98		 cdqe
  00069	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 25   : 		time_t weekStartTime = currentTime -

  0006e	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
  00073	48 8b 4c 24 38	 mov	 rcx, QWORD PTR $T3[rsp]
  00078	48 03 c8	 add	 rcx, rax
  0007b	48 8b c1	 mov	 rax, rcx
  0007e	48 03 44 24 40	 add	 rax, QWORD PTR $T4[rsp]
  00083	48 63 4c 24 50	 movsxd	 rcx, DWORD PTR currentTime_tm$[rsp]
  00088	48 03 c1	 add	 rax, rcx
  0008b	48 8b 4c 24 20	 mov	 rcx, QWORD PTR currentTime$[rsp]
  00090	48 2b c8	 sub	 rcx, rax
  00093	48 8b c1	 mov	 rax, rcx
  00096	48 89 44 24 48	 mov	 QWORD PTR weekStartTime$[rsp], rax

; 26   : 			(dayToSecond(currentTime_tm.tm_wday) + hourToSecond(currentTime_tm.tm_hour) +
; 27   : 				minToSecond(currentTime_tm.tm_min) + currentTime_tm.tm_sec);
; 28   : 
; 29   : 		return weekStartTime;

  0009b	48 8b 44 24 48	 mov	 rax, QWORD PTR weekStartTime$[rsp]

; 30   : 	}

  000a0	48 8b 4c 24 78	 mov	 rcx, QWORD PTR __$ArrayPad$[rsp]
  000a5	48 33 cc	 xor	 rcx, rsp
  000a8	e8 00 00 00 00	 call	 __security_check_cookie
  000ad	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  000b4	c3		 ret	 0
?getWeekStartTime@EventSchedule@mu2@@IEAA_JXZ ENDP	; mu2::EventSchedule::getWeekStartTime
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
;	COMDAT ?GetDayofWeek@EventSchedule@mu2@@IEAAEXZ
_TEXT	SEGMENT
$T1 = 32
currentTime$ = 40
currentTime_tm$ = 48
__$ArrayPad$ = 88
this$ = 112
?GetDayofWeek@EventSchedule@mu2@@IEAAEXZ PROC		; mu2::EventSchedule::GetDayofWeek, COMDAT

; 192  : 	{

$LN7:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 68	 sub	 rsp, 104		; 00000068H
  00009	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR __security_cookie
  00010	48 33 c4	 xor	 rax, rsp
  00013	48 89 44 24 58	 mov	 QWORD PTR __$ArrayPad$[rsp], rax
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h

; 552  :             return _time64(_Time);

  00018	33 c9		 xor	 ecx, ecx
  0001a	e8 00 00 00 00	 call	 _time64
  0001f	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 194  : 		time_t currentTime = time(nullptr);

  00024	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00029	48 89 44 24 28	 mov	 QWORD PTR currentTime$[rsp], rax
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h

; 617  :                 return _localtime64_s(_Tm, _Time);

  0002e	48 8d 54 24 28	 lea	 rdx, QWORD PTR currentTime$[rsp]
  00033	48 8d 4c 24 30	 lea	 rcx, QWORD PTR currentTime_tm$[rsp]
  00038	e8 00 00 00 00	 call	 _localtime64_s
  0003d	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 200  : 		return static_cast<Byte>(currentTime_tm.tm_wday);

  0003e	0f b6 44 24 48	 movzx	 eax, BYTE PTR currentTime_tm$[rsp+24]

; 201  : 	}

  00043	48 8b 4c 24 58	 mov	 rcx, QWORD PTR __$ArrayPad$[rsp]
  00048	48 33 cc	 xor	 rcx, rsp
  0004b	e8 00 00 00 00	 call	 __security_check_cookie
  00050	48 83 c4 68	 add	 rsp, 104		; 00000068H
  00054	c3		 ret	 0
?GetDayofWeek@EventSchedule@mu2@@IEAAEXZ ENDP		; mu2::EventSchedule::GetDayofWeek
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
;	COMDAT ?isWeekendToday@EventSchedule@mu2@@IEAA_NXZ
_TEXT	SEGMENT
$T1 = 32
currentTime$ = 40
currentTime_tm$ = 48
__$ArrayPad$ = 88
this$ = 112
?isWeekendToday@EventSchedule@mu2@@IEAA_NXZ PROC	; mu2::EventSchedule::isWeekendToday, COMDAT

; 176  : 	{

$LN9:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 68	 sub	 rsp, 104		; 00000068H
  00009	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR __security_cookie
  00010	48 33 c4	 xor	 rax, rsp
  00013	48 89 44 24 58	 mov	 QWORD PTR __$ArrayPad$[rsp], rax
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h

; 552  :             return _time64(_Time);

  00018	33 c9		 xor	 ecx, ecx
  0001a	e8 00 00 00 00	 call	 _time64
  0001f	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 178  : 		time_t currentTime = time(nullptr);

  00024	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00029	48 89 44 24 28	 mov	 QWORD PTR currentTime$[rsp], rax
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h

; 617  :                 return _localtime64_s(_Tm, _Time);

  0002e	48 8d 54 24 28	 lea	 rdx, QWORD PTR currentTime$[rsp]
  00033	48 8d 4c 24 30	 lea	 rcx, QWORD PTR currentTime_tm$[rsp]
  00038	e8 00 00 00 00	 call	 _localtime64_s
  0003d	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 184  : 		if (currentTime_tm.tm_wday >= 1 || currentTime_tm.tm_wday <= 5)

  0003e	83 7c 24 48 01	 cmp	 DWORD PTR currentTime_tm$[rsp+24], 1
  00043	7d 07		 jge	 SHORT $LN3@isWeekendT
  00045	83 7c 24 48 05	 cmp	 DWORD PTR currentTime_tm$[rsp+24], 5
  0004a	7f 04		 jg	 SHORT $LN2@isWeekendT
$LN3@isWeekendT:

; 185  : 		{
; 186  : 			return false;

  0004c	32 c0		 xor	 al, al
  0004e	eb 02		 jmp	 SHORT $LN1@isWeekendT
$LN2@isWeekendT:

; 187  : 		}
; 188  : 
; 189  : 		return true;

  00050	b0 01		 mov	 al, 1
$LN1@isWeekendT:

; 190  : 	}

  00052	48 8b 4c 24 58	 mov	 rcx, QWORD PTR __$ArrayPad$[rsp]
  00057	48 33 cc	 xor	 rcx, rsp
  0005a	e8 00 00 00 00	 call	 __security_check_cookie
  0005f	48 83 c4 68	 add	 rsp, 104		; 00000068H
  00063	c3		 ret	 0
?isWeekendToday@EventSchedule@mu2@@IEAA_NXZ ENDP	; mu2::EventSchedule::isWeekendToday
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
;	COMDAT ?processOpenState@EventSchedule@mu2@@AEAAXAEB_J@Z
_TEXT	SEGMENT
$T1 = 32
$T2 = 40
this$ = 64
current$ = 72
?processOpenState@EventSchedule@mu2@@AEAAXAEB_J@Z PROC	; mu2::EventSchedule::processOpenState, COMDAT

; 275  : 	{

$LN8:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 38	 sub	 rsp, 56			; 00000038H
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.h

; 46   : 		inline time_t getOpenTime() const			{ return static_cast<time_t>(m_currentSchedule.openTime - m_timeExtension); }

  0000e	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00013	8b 80 e0 00 00
	00		 mov	 eax, DWORD PTR [rax+224]
  00019	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0001e	48 8b 49 40	 mov	 rcx, QWORD PTR [rcx+64]
  00022	48 2b c8	 sub	 rcx, rax
  00025	48 8b c1	 mov	 rax, rcx
  00028	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 276  : 		VALID_RETURN(current < getOpenTime() || current >= getCloseTime(), );

  0002d	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00032	48 8b 4c 24 48	 mov	 rcx, QWORD PTR current$[rsp]
  00037	48 39 01	 cmp	 QWORD PTR [rcx], rax
  0003a	7c 30		 jl	 SHORT $LN2@processOpe
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.h

; 47   : 		inline time_t getCloseTime() const			{ return static_cast<time_t>(m_currentSchedule.closeTime - m_timeExtension); }

  0003c	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00041	8b 80 e0 00 00
	00		 mov	 eax, DWORD PTR [rax+224]
  00047	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0004c	48 8b 49 48	 mov	 rcx, QWORD PTR [rcx+72]
  00050	48 2b c8	 sub	 rcx, rax
  00053	48 8b c1	 mov	 rax, rcx
  00056	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 276  : 		VALID_RETURN(current < getOpenTime() || current >= getCloseTime(), );

  0005b	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
  00060	48 8b 4c 24 48	 mov	 rcx, QWORD PTR current$[rsp]
  00065	48 39 01	 cmp	 QWORD PTR [rcx], rax
  00068	7d 02		 jge	 SHORT $LN2@processOpe
  0006a	eb 2a		 jmp	 SHORT $LN1@processOpe
$LN2@processOpe:

; 277  : 		
; 278  : 		refreshNextWeek();

  0006c	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00071	e8 00 00 00 00	 call	 ?refreshNextWeek@EventSchedule@mu2@@AEAAXXZ ; mu2::EventSchedule::refreshNextWeek

; 279  : 
; 280  : 		m_state = State::CLOSE;

  00076	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0007b	c7 80 e4 00 00
	00 01 00 00 00	 mov	 DWORD PTR [rax+228], 1

; 281  : 		OnNotifyClose();

  00085	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0008a	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0008d	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00092	ff 50 30	 call	 QWORD PTR [rax+48]
  00095	90		 npad	 1
$LN1@processOpe:

; 282  : 	}

  00096	48 83 c4 38	 add	 rsp, 56			; 00000038H
  0009a	c3		 ret	 0
?processOpenState@EventSchedule@mu2@@AEAAXAEB_J@Z ENDP	; mu2::EventSchedule::processOpenState
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
;	COMDAT ?processReadyState@EventSchedule@mu2@@AEAAXAEB_J@Z
_TEXT	SEGMENT
$T1 = 32
this$ = 64
current$ = 72
?processReadyState@EventSchedule@mu2@@AEAAXAEB_J@Z PROC	; mu2::EventSchedule::processReadyState, COMDAT

; 267  : 	{

$LN6:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 38	 sub	 rsp, 56			; 00000038H
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.h

; 46   : 		inline time_t getOpenTime() const			{ return static_cast<time_t>(m_currentSchedule.openTime - m_timeExtension); }

  0000e	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00013	8b 80 e0 00 00
	00		 mov	 eax, DWORD PTR [rax+224]
  00019	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0001e	48 8b 49 40	 mov	 rcx, QWORD PTR [rcx+64]
  00022	48 2b c8	 sub	 rcx, rax
  00025	48 8b c1	 mov	 rax, rcx
  00028	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 268  : 		VALID_RETURN(current >= getOpenTime(), );

  0002d	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00032	48 8b 4c 24 48	 mov	 rcx, QWORD PTR current$[rsp]
  00037	48 39 01	 cmp	 QWORD PTR [rcx], rax
  0003a	7d 02		 jge	 SHORT $LN2@processRea
  0003c	eb 20		 jmp	 SHORT $LN1@processRea
$LN2@processRea:

; 269  : 		
; 270  : 		m_state = State::OPEN;

  0003e	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00043	c7 80 e4 00 00
	00 03 00 00 00	 mov	 DWORD PTR [rax+228], 3

; 271  : 		OnNotifyOpen();

  0004d	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00052	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00055	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0005a	ff 50 28	 call	 QWORD PTR [rax+40]
  0005d	90		 npad	 1
$LN1@processRea:

; 272  : 	}

  0005e	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00062	c3		 ret	 0
?processReadyState@EventSchedule@mu2@@AEAAXAEB_J@Z ENDP	; mu2::EventSchedule::processReadyState
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
;	COMDAT ?processClosedState@EventSchedule@mu2@@AEAAXAEB_J@Z
_TEXT	SEGMENT
$T1 = 32
this$ = 64
current$ = 72
?processClosedState@EventSchedule@mu2@@AEAAXAEB_J@Z PROC ; mu2::EventSchedule::processClosedState, COMDAT

; 259  : 	{

$LN6:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 38	 sub	 rsp, 56			; 00000038H
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.h

; 45   : 		inline time_t getReadyTime() const		  	{ return static_cast<time_t>(m_currentSchedule.readyTime - m_timeExtension); }

  0000e	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00013	8b 80 e0 00 00
	00		 mov	 eax, DWORD PTR [rax+224]
  00019	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0001e	48 8b 49 38	 mov	 rcx, QWORD PTR [rcx+56]
  00022	48 2b c8	 sub	 rcx, rax
  00025	48 8b c1	 mov	 rax, rcx
  00028	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 260  : 		VALID_RETURN(current >= getReadyTime(), );

  0002d	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00032	48 8b 4c 24 48	 mov	 rcx, QWORD PTR current$[rsp]
  00037	48 39 01	 cmp	 QWORD PTR [rcx], rax
  0003a	7d 02		 jge	 SHORT $LN2@processClo
  0003c	eb 20		 jmp	 SHORT $LN1@processClo
$LN2@processClo:

; 261  : 		
; 262  : 		m_state = State::READY;

  0003e	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00043	c7 80 e4 00 00
	00 02 00 00 00	 mov	 DWORD PTR [rax+228], 2

; 263  : 		OnNotifyReady();

  0004d	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00052	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00055	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0005a	ff 50 20	 call	 QWORD PTR [rax+32]
  0005d	90		 npad	 1
$LN1@processClo:

; 264  : 	}

  0005e	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00062	c3		 ret	 0
?processClosedState@EventSchedule@mu2@@AEAAXAEB_J@Z ENDP ; mu2::EventSchedule::processClosedState
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
;	COMDAT ?refreshNextWeek@EventSchedule@mu2@@AEAAXXZ
_TEXT	SEGMENT
$T1 = 176
$T2 = 177
schedule$3 = 184
schedule$4 = 192
tv247 = 200
<begin>$L0$5 = 208
<begin>$L1$6 = 216
$T7 = 224
$T8 = 228
$T9 = 232
_My_data$10 = 240
<range>$L0$11 = 248
_My_data$12 = 256
_My_data$13 = 264
<range>$L1$14 = 272
$T15 = 280
_Ptr$ = 288
$T16 = 296
$T17 = 304
_Ptr$ = 312
$T18 = 320
$T19 = 328
<end>$L0$20 = 336
$T21 = 344
$T22 = 352
$T23 = 360
$T24 = 368
$T25 = 376
current$ = 384
__param0$ = 392
$T26 = 400
__param0$ = 408
$T27 = 416
$T28 = 424
$T29 = 432
_My_data$30 = 440
$T31 = 448
_Ptr$ = 456
$T32 = 464
$T33 = 472
_Ptr$ = 480
$T34 = 488
$T35 = 496
<end>$L1$36 = 504
$T37 = 512
$T38 = 520
curr_tm$ = 528
__$ArrayPad$ = 568
this$ = 608
?refreshNextWeek@EventSchedule@mu2@@AEAAXXZ PROC	; mu2::EventSchedule::refreshNextWeek, COMDAT

; 204  : 	{

$LN186:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	56		 push	 rsi
  00006	57		 push	 rdi
  00007	48 81 ec 48 02
	00 00		 sub	 rsp, 584		; 00000248H
  0000e	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR __security_cookie
  00015	48 33 c4	 xor	 rax, rsp
  00018	48 89 84 24 38
	02 00 00	 mov	 QWORD PTR __$ArrayPad$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1914 :         auto& _My_data = _Mypair._Myval2;

  00020	48 8b 84 24 60
	02 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00028	48 83 c0 20	 add	 rax, 32			; 00000020H
  0002c	48 89 84 24 f0
	00 00 00	 mov	 QWORD PTR _My_data$10[rsp], rax

; 1915 :         return static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst);

  00034	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR _My_data$10[rsp]
  0003c	48 8b 8c 24 f0
	00 00 00	 mov	 rcx, QWORD PTR _My_data$10[rsp]
  00044	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00047	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0004b	48 2b c1	 sub	 rax, rcx
  0004e	48 99		 cdq
  00050	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  00055	48 f7 f9	 idiv	 rcx
  00058	48 89 84 24 18
	01 00 00	 mov	 QWORD PTR $T15[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 205  : 		VALID_RETURN(m_vecSchedule.size(), );

  00060	48 8b 84 24 18
	01 00 00	 mov	 rax, QWORD PTR $T15[rsp]
  00068	48 85 c0	 test	 rax, rax
  0006b	75 05		 jne	 SHORT $LN8@refreshNex
  0006d	e9 33 06 00 00	 jmp	 $LN1@refreshNex
$LN8@refreshNex:

; 206  : 
; 207  : 		m_timeExtension = 0;

  00072	48 8b 84 24 60
	02 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0007a	c7 80 e0 00 00
	00 00 00 00 00	 mov	 DWORD PTR [rax+224], 0

; 209  : 		for (auto& schedule: m_vecSchedule)

  00084	48 8b 84 24 60
	02 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0008c	48 83 c0 20	 add	 rax, 32			; 00000020H
  00090	48 89 84 24 f8
	00 00 00	 mov	 QWORD PTR <range>$L0$11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1893 :         return _Unfancy_maybe_null(_Mypair._Myval2._Myfirst);

  00098	48 8b 84 24 f8
	00 00 00	 mov	 rax, QWORD PTR <range>$L0$11[rsp]
  000a0	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000a3	48 89 84 24 20
	01 00 00	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 80   :     return _Ptr;

  000ab	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR _Ptr$[rsp]
  000b3	48 89 84 24 28
	01 00 00	 mov	 QWORD PTR $T16[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1893 :         return _Unfancy_maybe_null(_Mypair._Myval2._Myfirst);

  000bb	48 8b 84 24 28
	01 00 00	 mov	 rax, QWORD PTR $T16[rsp]
  000c3	48 89 84 24 30
	01 00 00	 mov	 QWORD PTR $T17[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 209  : 		for (auto& schedule: m_vecSchedule)

  000cb	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR $T17[rsp]
  000d3	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR <begin>$L0$5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1901 :         return _Unfancy_maybe_null(_Mypair._Myval2._Mylast);

  000db	48 8b 84 24 f8
	00 00 00	 mov	 rax, QWORD PTR <range>$L0$11[rsp]
  000e3	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  000e7	48 89 84 24 38
	01 00 00	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 80   :     return _Ptr;

  000ef	48 8b 84 24 38
	01 00 00	 mov	 rax, QWORD PTR _Ptr$[rsp]
  000f7	48 89 84 24 40
	01 00 00	 mov	 QWORD PTR $T18[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1901 :         return _Unfancy_maybe_null(_Mypair._Myval2._Mylast);

  000ff	48 8b 84 24 40
	01 00 00	 mov	 rax, QWORD PTR $T18[rsp]
  00107	48 89 84 24 48
	01 00 00	 mov	 QWORD PTR $T19[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 209  : 		for (auto& schedule: m_vecSchedule)

  0010f	48 8b 84 24 48
	01 00 00	 mov	 rax, QWORD PTR $T19[rsp]
  00117	48 89 84 24 50
	01 00 00	 mov	 QWORD PTR <end>$L0$20[rsp], rax
  0011f	eb 16		 jmp	 SHORT $LN4@refreshNex
$LN2@refreshNex:
  00121	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR <begin>$L0$5[rsp]
  00129	48 05 a8 00 00
	00		 add	 rax, 168		; 000000a8H
  0012f	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR <begin>$L0$5[rsp], rax
$LN4@refreshNex:
  00137	48 8b 84 24 50
	01 00 00	 mov	 rax, QWORD PTR <end>$L0$20[rsp]
  0013f	48 39 84 24 d0
	00 00 00	 cmp	 QWORD PTR <begin>$L0$5[rsp], rax
  00147	0f 84 b0 01 00
	00		 je	 $LN3@refreshNex
  0014d	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR <begin>$L0$5[rsp]
  00155	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR schedule$4[rsp], rax
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h

; 552  :             return _time64(_Time);

  0015d	33 c9		 xor	 ecx, ecx
  0015f	e8 00 00 00 00	 call	 _time64
  00164	48 89 84 24 58
	01 00 00	 mov	 QWORD PTR $T21[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.h

; 16   : 		bool IsExpire() { return time(nullptr) >= closeTime; }

  0016c	48 8b 84 24 58
	01 00 00	 mov	 rax, QWORD PTR $T21[rsp]
  00174	48 8b 8c 24 c0
	00 00 00	 mov	 rcx, QWORD PTR schedule$4[rsp]
  0017c	48 3b 41 10	 cmp	 rax, QWORD PTR [rcx+16]
  00180	7c 0d		 jl	 SHORT $LN38@refreshNex
  00182	c7 84 24 c8 00
	00 00 01 00 00
	00		 mov	 DWORD PTR tv247[rsp], 1
  0018d	eb 0b		 jmp	 SHORT $LN39@refreshNex
$LN38@refreshNex:
  0018f	c7 84 24 c8 00
	00 00 00 00 00
	00		 mov	 DWORD PTR tv247[rsp], 0
$LN39@refreshNex:
  0019a	0f b6 84 24 c8
	00 00 00	 movzx	 eax, BYTE PTR tv247[rsp]
  001a2	88 84 24 b0 00
	00 00		 mov	 BYTE PTR $T1[rsp], al
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 211  : 			if (schedule.IsExpire() == true)

  001a9	0f b6 84 24 b0
	00 00 00	 movzx	 eax, BYTE PTR $T1[rsp]
  001b1	0f b6 c0	 movzx	 eax, al
  001b4	83 f8 01	 cmp	 eax, 1
  001b7	0f 85 3b 01 00
	00		 jne	 $LN9@refreshNex

; 213  : 				schedule.openTime  += dayToSecond(7);

  001bd	c7 84 24 e0 00
	00 00 07 00 00
	00		 mov	 DWORD PTR $T7[rsp], 7
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.h

; 42   : 		inline time_t dayToSecond(const Int32& week)	{ return week * 24 * 60 * 60; }

  001c8	6b 84 24 e0 00
	00 00 18	 imul	 eax, DWORD PTR $T7[rsp], 24
  001d0	6b c0 3c	 imul	 eax, eax, 60		; 0000003cH
  001d3	6b c0 3c	 imul	 eax, eax, 60		; 0000003cH
  001d6	48 98		 cdqe
  001d8	48 89 84 24 60
	01 00 00	 mov	 QWORD PTR $T22[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 213  : 				schedule.openTime  += dayToSecond(7);

  001e0	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR schedule$4[rsp]
  001e8	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  001ec	48 8b 8c 24 60
	01 00 00	 mov	 rcx, QWORD PTR $T22[rsp]
  001f4	48 03 c8	 add	 rcx, rax
  001f7	48 8b c1	 mov	 rax, rcx
  001fa	48 8b 8c 24 c0
	00 00 00	 mov	 rcx, QWORD PTR schedule$4[rsp]
  00202	48 89 41 08	 mov	 QWORD PTR [rcx+8], rax

; 214  : 				schedule.readyTime += dayToSecond(7);

  00206	c7 84 24 e4 00
	00 00 07 00 00
	00		 mov	 DWORD PTR $T8[rsp], 7
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.h

; 42   : 		inline time_t dayToSecond(const Int32& week)	{ return week * 24 * 60 * 60; }

  00211	6b 84 24 e4 00
	00 00 18	 imul	 eax, DWORD PTR $T8[rsp], 24
  00219	6b c0 3c	 imul	 eax, eax, 60		; 0000003cH
  0021c	6b c0 3c	 imul	 eax, eax, 60		; 0000003cH
  0021f	48 98		 cdqe
  00221	48 89 84 24 68
	01 00 00	 mov	 QWORD PTR $T23[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 214  : 				schedule.readyTime += dayToSecond(7);

  00229	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR schedule$4[rsp]
  00231	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00234	48 8b 8c 24 68
	01 00 00	 mov	 rcx, QWORD PTR $T23[rsp]
  0023c	48 03 c8	 add	 rcx, rax
  0023f	48 8b c1	 mov	 rax, rcx
  00242	48 8b 8c 24 c0
	00 00 00	 mov	 rcx, QWORD PTR schedule$4[rsp]
  0024a	48 89 01	 mov	 QWORD PTR [rcx], rax

; 215  : 				schedule.closeTime += dayToSecond(7);

  0024d	c7 84 24 e8 00
	00 00 07 00 00
	00		 mov	 DWORD PTR $T9[rsp], 7
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.h

; 42   : 		inline time_t dayToSecond(const Int32& week)	{ return week * 24 * 60 * 60; }

  00258	6b 84 24 e8 00
	00 00 18	 imul	 eax, DWORD PTR $T9[rsp], 24
  00260	6b c0 3c	 imul	 eax, eax, 60		; 0000003cH
  00263	6b c0 3c	 imul	 eax, eax, 60		; 0000003cH
  00266	48 98		 cdqe
  00268	48 89 84 24 70
	01 00 00	 mov	 QWORD PTR $T24[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 215  : 				schedule.closeTime += dayToSecond(7);

  00270	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR schedule$4[rsp]
  00278	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0027c	48 8b 8c 24 70
	01 00 00	 mov	 rcx, QWORD PTR $T24[rsp]
  00284	48 03 c8	 add	 rcx, rax
  00287	48 8b c1	 mov	 rax, rcx
  0028a	48 8b 8c 24 c0
	00 00 00	 mov	 rcx, QWORD PTR schedule$4[rsp]
  00292	48 89 41 10	 mov	 QWORD PTR [rcx+16], rax

; 216  : 
; 217  : 				localtime_s(&schedule.openTime_tm, &schedule.openTime);

  00296	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR schedule$4[rsp]
  0029e	48 83 c0 08	 add	 rax, 8
  002a2	48 8b 8c 24 c0
	00 00 00	 mov	 rcx, QWORD PTR schedule$4[rsp]
  002aa	48 83 c1 60	 add	 rcx, 96			; 00000060H
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h

; 617  :                 return _localtime64_s(_Tm, _Time);

  002ae	48 8b d0	 mov	 rdx, rax
  002b1	e8 00 00 00 00	 call	 _localtime64_s
  002b6	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 218  : 				localtime_s(&schedule.readyTime_tm, &schedule.readyTime);

  002b7	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR schedule$4[rsp]
  002bf	48 8b 8c 24 c0
	00 00 00	 mov	 rcx, QWORD PTR schedule$4[rsp]
  002c7	48 83 c1 3c	 add	 rcx, 60			; 0000003cH
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h

; 617  :                 return _localtime64_s(_Tm, _Time);

  002cb	48 8b d0	 mov	 rdx, rax
  002ce	e8 00 00 00 00	 call	 _localtime64_s
  002d3	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 219  : 				localtime_s(&schedule.closeTime_tm, &schedule.closeTime);

  002d4	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR schedule$4[rsp]
  002dc	48 83 c0 10	 add	 rax, 16
  002e0	48 8b 8c 24 c0
	00 00 00	 mov	 rcx, QWORD PTR schedule$4[rsp]
  002e8	48 81 c1 84 00
	00 00		 add	 rcx, 132		; 00000084H
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h

; 617  :                 return _localtime64_s(_Tm, _Time);

  002ef	48 8b d0	 mov	 rdx, rax
  002f2	e8 00 00 00 00	 call	 _localtime64_s
  002f7	90		 npad	 1
$LN9@refreshNex:
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 221  : 		}

  002f8	e9 24 fe ff ff	 jmp	 $LN2@refreshNex
$LN3@refreshNex:
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h

; 552  :             return _time64(_Time);

  002fd	33 c9		 xor	 ecx, ecx
  002ff	e8 00 00 00 00	 call	 _time64
  00304	48 89 84 24 78
	01 00 00	 mov	 QWORD PTR $T25[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 224  : 		time_t current = time(nullptr);

  0030c	48 8b 84 24 78
	01 00 00	 mov	 rax, QWORD PTR $T25[rsp]
  00314	48 89 84 24 80
	01 00 00	 mov	 QWORD PTR current$[rsp], rax
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h

; 617  :                 return _localtime64_s(_Tm, _Time);

  0031c	48 8d 94 24 80
	01 00 00	 lea	 rdx, QWORD PTR current$[rsp]
  00324	48 8d 8c 24 10
	02 00 00	 lea	 rcx, QWORD PTR curr_tm$[rsp]
  0032c	e8 00 00 00 00	 call	 _localtime64_s
  00331	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 227  : 		std::sort(m_vecSchedule.begin(), m_vecSchedule.end(),[](const EventScheduleDate& lhs, const EventScheduleDate& rhs)

  00332	48 8d 84 24 b1
	00 00 00	 lea	 rax, QWORD PTR $T2[rsp]
  0033a	48 8b f8	 mov	 rdi, rax
  0033d	33 c0		 xor	 eax, eax
  0033f	b9 01 00 00 00	 mov	 ecx, 1
  00344	f3 aa		 rep stosb
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1851 :         auto& _My_data = _Mypair._Myval2;

  00346	48 8b 84 24 60
	02 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0034e	48 83 c0 20	 add	 rax, 32			; 00000020H
  00352	48 89 84 24 00
	01 00 00	 mov	 QWORD PTR _My_data$12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0035a	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR _My_data$12[rsp]
  00362	48 89 84 24 00
	02 00 00	 mov	 QWORD PTR $T37[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1852 :         return iterator(_My_data._Mylast, _STD addressof(_My_data));

  0036a	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR _My_data$12[rsp]
  00372	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00376	48 89 84 24 88
	01 00 00	 mov	 QWORD PTR __param0$[rsp], rax

; 46   :     _CONSTEXPR20 _Vector_const_iterator(_Tptr _Parg, const _Container_base* _Pvector) noexcept : _Ptr(_Parg) {

  0037e	48 8b 84 24 88
	01 00 00	 mov	 rax, QWORD PTR __param0$[rsp]
  00386	48 89 84 24 90
	01 00 00	 mov	 QWORD PTR $T26[rsp], rax

; 1852 :         return iterator(_My_data._Mylast, _STD addressof(_My_data));

  0038e	48 8d 84 24 90
	01 00 00	 lea	 rax, QWORD PTR $T26[rsp]
  00396	48 89 84 24 a8
	01 00 00	 mov	 QWORD PTR $T28[rsp], rax

; 1841 :         auto& _My_data = _Mypair._Myval2;

  0039e	48 8b 84 24 60
	02 00 00	 mov	 rax, QWORD PTR this$[rsp]
  003a6	48 83 c0 20	 add	 rax, 32			; 00000020H
  003aa	48 89 84 24 08
	01 00 00	 mov	 QWORD PTR _My_data$13[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  003b2	48 8b 84 24 08
	01 00 00	 mov	 rax, QWORD PTR _My_data$13[rsp]
  003ba	48 89 84 24 08
	02 00 00	 mov	 QWORD PTR $T38[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1842 :         return iterator(_My_data._Myfirst, _STD addressof(_My_data));

  003c2	48 8b 84 24 08
	01 00 00	 mov	 rax, QWORD PTR _My_data$13[rsp]
  003ca	48 8b 00	 mov	 rax, QWORD PTR [rax]
  003cd	48 89 84 24 98
	01 00 00	 mov	 QWORD PTR __param0$[rsp], rax

; 46   :     _CONSTEXPR20 _Vector_const_iterator(_Tptr _Parg, const _Container_base* _Pvector) noexcept : _Ptr(_Parg) {

  003d5	48 8b 84 24 98
	01 00 00	 mov	 rax, QWORD PTR __param0$[rsp]
  003dd	48 89 84 24 a0
	01 00 00	 mov	 QWORD PTR $T27[rsp], rax

; 1842 :         return iterator(_My_data._Myfirst, _STD addressof(_My_data));

  003e5	48 8d 84 24 a0
	01 00 00	 lea	 rax, QWORD PTR $T27[rsp]
  003ed	48 89 84 24 b0
	01 00 00	 mov	 QWORD PTR $T29[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 227  : 		std::sort(m_vecSchedule.begin(), m_vecSchedule.end(),[](const EventScheduleDate& lhs, const EventScheduleDate& rhs)

  003f5	44 0f b6 84 24
	b1 00 00 00	 movzx	 r8d, BYTE PTR $T2[rsp]
  003fe	48 8b 84 24 a8
	01 00 00	 mov	 rax, QWORD PTR $T28[rsp]
  00406	48 8b 10	 mov	 rdx, QWORD PTR [rax]
  00409	48 8b 84 24 b0
	01 00 00	 mov	 rax, QWORD PTR $T29[rsp]
  00411	48 8b 08	 mov	 rcx, QWORD PTR [rax]
  00414	e8 00 00 00 00	 call	 ??$sort@V?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@UEventScheduleDate@mu2@@@std@@@std@@@std@@V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@std@@YAXV?$_Vector_iterator@V?$_Vector_val@U?$_Simple_types@UEventScheduleDate@mu2@@@std@@@std@@@0@0V<lambda_5acf0176913ce2eb7dca573a416bd931>@@@Z ; std::sort<std::_Vector_iterator<std::_Vector_val<std::_Simple_types<mu2::EventScheduleDate> > >,<lambda_5acf0176913ce2eb7dca573a416bd931> >
  00419	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1928 :         auto& _My_data = _Mypair._Myval2;

  0041a	48 8b 84 24 60
	02 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00422	48 83 c0 20	 add	 rax, 32			; 00000020H
  00426	48 89 84 24 b8
	01 00 00	 mov	 QWORD PTR _My_data$30[rsp], rax

; 1929 : #if _MSVC_STL_HARDENING_VECTOR || _ITERATOR_DEBUG_LEVEL != 0
; 1930 :         _STL_VERIFY(
; 1931 :             _Pos < static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst), "vector subscript out of range");
; 1932 : #endif
; 1933 : 
; 1934 :         return _My_data._Myfirst[_Pos];

  0042e	33 c0		 xor	 eax, eax
  00430	48 69 c0 a8 00
	00 00		 imul	 rax, rax, 168		; 000000a8H
  00437	48 8b 8c 24 b8
	01 00 00	 mov	 rcx, QWORD PTR _My_data$30[rsp]
  0043f	48 03 01	 add	 rax, QWORD PTR [rcx]
  00442	48 89 84 24 c0
	01 00 00	 mov	 QWORD PTR $T31[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 232  : 		m_currentSchedule = m_vecSchedule[0];

  0044a	48 8b 84 24 60
	02 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00452	48 8d 78 38	 lea	 rdi, QWORD PTR [rax+56]
  00456	48 8b b4 24 c0
	01 00 00	 mov	 rsi, QWORD PTR $T31[rsp]
  0045e	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  00463	f3 a4		 rep movsb

; 234  : 		for (auto& schedule : m_vecSchedule)

  00465	48 8b 84 24 60
	02 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0046d	48 83 c0 20	 add	 rax, 32			; 00000020H
  00471	48 89 84 24 10
	01 00 00	 mov	 QWORD PTR <range>$L1$14[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1893 :         return _Unfancy_maybe_null(_Mypair._Myval2._Myfirst);

  00479	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR <range>$L1$14[rsp]
  00481	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00484	48 89 84 24 c8
	01 00 00	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 80   :     return _Ptr;

  0048c	48 8b 84 24 c8
	01 00 00	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00494	48 89 84 24 d0
	01 00 00	 mov	 QWORD PTR $T32[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1893 :         return _Unfancy_maybe_null(_Mypair._Myval2._Myfirst);

  0049c	48 8b 84 24 d0
	01 00 00	 mov	 rax, QWORD PTR $T32[rsp]
  004a4	48 89 84 24 d8
	01 00 00	 mov	 QWORD PTR $T33[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 234  : 		for (auto& schedule : m_vecSchedule)

  004ac	48 8b 84 24 d8
	01 00 00	 mov	 rax, QWORD PTR $T33[rsp]
  004b4	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR <begin>$L1$6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1901 :         return _Unfancy_maybe_null(_Mypair._Myval2._Mylast);

  004bc	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR <range>$L1$14[rsp]
  004c4	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  004c8	48 89 84 24 e0
	01 00 00	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 80   :     return _Ptr;

  004d0	48 8b 84 24 e0
	01 00 00	 mov	 rax, QWORD PTR _Ptr$[rsp]
  004d8	48 89 84 24 e8
	01 00 00	 mov	 QWORD PTR $T34[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1901 :         return _Unfancy_maybe_null(_Mypair._Myval2._Mylast);

  004e0	48 8b 84 24 e8
	01 00 00	 mov	 rax, QWORD PTR $T34[rsp]
  004e8	48 89 84 24 f0
	01 00 00	 mov	 QWORD PTR $T35[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 234  : 		for (auto& schedule : m_vecSchedule)

  004f0	48 8b 84 24 f0
	01 00 00	 mov	 rax, QWORD PTR $T35[rsp]
  004f8	48 89 84 24 f8
	01 00 00	 mov	 QWORD PTR <end>$L1$36[rsp], rax
  00500	eb 16		 jmp	 SHORT $LN7@refreshNex
$LN5@refreshNex:
  00502	48 8b 84 24 d8
	00 00 00	 mov	 rax, QWORD PTR <begin>$L1$6[rsp]
  0050a	48 05 a8 00 00
	00		 add	 rax, 168		; 000000a8H
  00510	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR <begin>$L1$6[rsp], rax
$LN7@refreshNex:
  00518	48 8b 84 24 f8
	01 00 00	 mov	 rax, QWORD PTR <end>$L1$36[rsp]
  00520	48 39 84 24 d8
	00 00 00	 cmp	 QWORD PTR <begin>$L1$6[rsp], rax
  00528	0f 84 77 01 00
	00		 je	 $LN1@refreshNex
  0052e	48 8b 84 24 d8
	00 00 00	 mov	 rax, QWORD PTR <begin>$L1$6[rsp]
  00536	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR schedule$3[rsp], rax

; 235  : 		{
; 236  : 			MU2_INFO_LOG(LogCategory::CONTENTS, "World EventSchedule ready:%d-%02d-%02d %02d:%02d open::%d-%02d-%02d %02d:%02d  close::%d-%02d-%02d %02d:%02d",

  0053e	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR schedule$3[rsp]
  00546	8b 80 94 00 00
	00		 mov	 eax, DWORD PTR [rax+148]
  0054c	ff c0		 inc	 eax
  0054e	48 8b 8c 24 b8
	00 00 00	 mov	 rcx, QWORD PTR schedule$3[rsp]
  00556	8b 89 98 00 00
	00		 mov	 ecx, DWORD PTR [rcx+152]
  0055c	81 c1 6c 07 00
	00		 add	 ecx, 1900		; 0000076cH
  00562	48 8b 94 24 b8
	00 00 00	 mov	 rdx, QWORD PTR schedule$3[rsp]
  0056a	8b 52 70	 mov	 edx, DWORD PTR [rdx+112]
  0056d	ff c2		 inc	 edx
  0056f	48 8b bc 24 b8
	00 00 00	 mov	 rdi, QWORD PTR schedule$3[rsp]
  00577	8b 7f 74	 mov	 edi, DWORD PTR [rdi+116]
  0057a	81 c7 6c 07 00
	00		 add	 edi, 1900		; 0000076cH
  00580	48 8b b4 24 b8
	00 00 00	 mov	 rsi, QWORD PTR schedule$3[rsp]
  00588	8b 76 4c	 mov	 esi, DWORD PTR [rsi+76]
  0058b	ff c6		 inc	 esi
  0058d	4c 8b 84 24 b8
	00 00 00	 mov	 r8, QWORD PTR schedule$3[rsp]
  00595	45 8b 40 50	 mov	 r8d, DWORD PTR [r8+80]
  00599	41 81 c0 6c 07
	00 00		 add	 r8d, 1900		; 0000076cH
  005a0	4c 8b 8c 24 b8
	00 00 00	 mov	 r9, QWORD PTR schedule$3[rsp]
  005a8	45 8b 89 88 00
	00 00		 mov	 r9d, DWORD PTR [r9+136]
  005af	44 89 8c 24 a8
	00 00 00	 mov	 DWORD PTR [rsp+168], r9d
  005b7	4c 8b 8c 24 b8
	00 00 00	 mov	 r9, QWORD PTR schedule$3[rsp]
  005bf	45 8b 89 8c 00
	00 00		 mov	 r9d, DWORD PTR [r9+140]
  005c6	44 89 8c 24 a0
	00 00 00	 mov	 DWORD PTR [rsp+160], r9d
  005ce	4c 8b 8c 24 b8
	00 00 00	 mov	 r9, QWORD PTR schedule$3[rsp]
  005d6	45 8b 89 90 00
	00 00		 mov	 r9d, DWORD PTR [r9+144]
  005dd	44 89 8c 24 98
	00 00 00	 mov	 DWORD PTR [rsp+152], r9d
  005e5	89 84 24 90 00
	00 00		 mov	 DWORD PTR [rsp+144], eax
  005ec	89 8c 24 88 00
	00 00		 mov	 DWORD PTR [rsp+136], ecx
  005f3	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR schedule$3[rsp]
  005fb	8b 40 64	 mov	 eax, DWORD PTR [rax+100]
  005fe	89 84 24 80 00
	00 00		 mov	 DWORD PTR [rsp+128], eax
  00605	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR schedule$3[rsp]
  0060d	8b 40 68	 mov	 eax, DWORD PTR [rax+104]
  00610	89 44 24 78	 mov	 DWORD PTR [rsp+120], eax
  00614	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR schedule$3[rsp]
  0061c	8b 40 6c	 mov	 eax, DWORD PTR [rax+108]
  0061f	89 44 24 70	 mov	 DWORD PTR [rsp+112], eax
  00623	89 54 24 68	 mov	 DWORD PTR [rsp+104], edx
  00627	89 7c 24 60	 mov	 DWORD PTR [rsp+96], edi
  0062b	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR schedule$3[rsp]
  00633	8b 40 40	 mov	 eax, DWORD PTR [rax+64]
  00636	89 44 24 58	 mov	 DWORD PTR [rsp+88], eax
  0063a	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR schedule$3[rsp]
  00642	8b 40 44	 mov	 eax, DWORD PTR [rax+68]
  00645	89 44 24 50	 mov	 DWORD PTR [rsp+80], eax
  00649	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR schedule$3[rsp]
  00651	8b 40 48	 mov	 eax, DWORD PTR [rax+72]
  00654	89 44 24 48	 mov	 DWORD PTR [rsp+72], eax
  00658	89 74 24 40	 mov	 DWORD PTR [rsp+64], esi
  0065c	44 89 44 24 38	 mov	 DWORD PTR [rsp+56], r8d
  00661	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0GN@NJKGFILB@World?5EventSchedule?5ready?3?$CFd?9?$CF0@
  00668	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  0066d	c7 44 24 28 fd
	00 00 00	 mov	 DWORD PTR [rsp+40], 253	; 000000fdH
  00675	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EM@EAHOIPCK@F?3?2Release_Branch?2Server?2Develo@
  0067c	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  00681	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CE@PHJOKCDC@mu2?3?3EventSchedule?3?3refreshNext@
  00688	41 b8 20 4e 00
	00		 mov	 r8d, 20000		; 00004e20H
  0068e	ba 19 00 00 00	 mov	 edx, 25
  00693	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  0069a	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  0069f	90		 npad	 1

; 237  : 				schedule.readyTime_tm.tm_year + 1900,
; 238  : 				schedule.readyTime_tm.tm_mon + 1,
; 239  : 				schedule.readyTime_tm.tm_mday,
; 240  : 				schedule.readyTime_tm.tm_hour,
; 241  : 				schedule.readyTime_tm.tm_min,
; 242  : 
; 243  : 				schedule.openTime_tm.tm_year + 1900,
; 244  : 				schedule.openTime_tm.tm_mon + 1,
; 245  : 				schedule.openTime_tm.tm_mday,
; 246  : 				schedule.openTime_tm.tm_hour,
; 247  : 				schedule.openTime_tm.tm_min,
; 248  : 
; 249  : 				schedule.closeTime_tm.tm_year + 1900,
; 250  : 				schedule.closeTime_tm.tm_mon + 1,
; 251  : 				schedule.closeTime_tm.tm_mday,
; 252  : 				schedule.closeTime_tm.tm_hour,
; 253  : 				schedule.closeTime_tm.tm_min);
; 254  : 		}

  006a0	e9 5d fe ff ff	 jmp	 $LN5@refreshNex
$LN1@refreshNex:

; 255  : 	
; 256  : 	}

  006a5	48 8b 8c 24 38
	02 00 00	 mov	 rcx, QWORD PTR __$ArrayPad$[rsp]
  006ad	48 33 cc	 xor	 rcx, rsp
  006b0	e8 00 00 00 00	 call	 __security_check_cookie
  006b5	48 81 c4 48 02
	00 00		 add	 rsp, 584		; 00000248H
  006bc	5f		 pop	 rdi
  006bd	5e		 pop	 rsi
  006be	c3		 ret	 0
?refreshNextWeek@EventSchedule@mu2@@AEAAXXZ ENDP	; mu2::EventSchedule::refreshNextWeek
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
;	COMDAT ?Update@EventSchedule@mu2@@QEAAXXZ
_TEXT	SEGMENT
tv71 = 32
current$ = 40
_My_data$1 = 48
$T2 = 56
$T3 = 64
this$ = 96
?Update@EventSchedule@mu2@@QEAAXXZ PROC			; mu2::EventSchedule::Update, COMDAT

; 148  : 	{

$LN16:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 58	 sub	 rsp, 88			; 00000058H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1914 :         auto& _My_data = _Mypair._Myval2;

  00009	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 c0 20	 add	 rax, 32			; 00000020H
  00012	48 89 44 24 30	 mov	 QWORD PTR _My_data$1[rsp], rax

; 1915 :         return static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst);

  00017	48 8b 44 24 30	 mov	 rax, QWORD PTR _My_data$1[rsp]
  0001c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _My_data$1[rsp]
  00021	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00024	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00028	48 2b c1	 sub	 rax, rcx
  0002b	48 99		 cdq
  0002d	b9 a8 00 00 00	 mov	 ecx, 168		; 000000a8H
  00032	48 f7 f9	 idiv	 rcx
  00035	48 89 44 24 38	 mov	 QWORD PTR $T2[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 149  : 		VALID_RETURN(m_vecSchedule.size(), );

  0003a	48 8b 44 24 38	 mov	 rax, QWORD PTR $T2[rsp]
  0003f	48 85 c0	 test	 rax, rax
  00042	75 05		 jne	 SHORT $LN4@Update
  00044	e9 86 00 00 00	 jmp	 $LN1@Update
$LN4@Update:
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h

; 552  :             return _time64(_Time);

  00049	33 c9		 xor	 ecx, ecx
  0004b	e8 00 00 00 00	 call	 _time64
  00050	48 89 44 24 40	 mov	 QWORD PTR $T3[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 151  : 		time_t current = time(nullptr);

  00055	48 8b 44 24 40	 mov	 rax, QWORD PTR $T3[rsp]
  0005a	48 89 44 24 28	 mov	 QWORD PTR current$[rsp], rax

; 152  : 
; 153  : 		switch (m_state)

  0005f	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00064	8b 80 e4 00 00
	00		 mov	 eax, DWORD PTR [rax+228]
  0006a	89 44 24 20	 mov	 DWORD PTR tv71[rsp], eax
  0006e	83 7c 24 20 01	 cmp	 DWORD PTR tv71[rsp], 1
  00073	74 10		 je	 SHORT $LN5@Update
  00075	83 7c 24 20 02	 cmp	 DWORD PTR tv71[rsp], 2
  0007a	74 1b		 je	 SHORT $LN6@Update
  0007c	83 7c 24 20 03	 cmp	 DWORD PTR tv71[rsp], 3
  00081	74 26		 je	 SHORT $LN7@Update
  00083	eb 34		 jmp	 SHORT $LN2@Update
$LN5@Update:

; 154  : 		{
; 155  : 		case State::CLOSE:
; 156  : 			processClosedState(current);

  00085	48 8d 54 24 28	 lea	 rdx, QWORD PTR current$[rsp]
  0008a	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  0008f	e8 00 00 00 00	 call	 ?processClosedState@EventSchedule@mu2@@AEAAXAEB_J@Z ; mu2::EventSchedule::processClosedState
  00094	90		 npad	 1

; 157  : 			break;

  00095	eb 22		 jmp	 SHORT $LN2@Update
$LN6@Update:

; 158  : 
; 159  : 		case State::READY:
; 160  : 			processReadyState(current);

  00097	48 8d 54 24 28	 lea	 rdx, QWORD PTR current$[rsp]
  0009c	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  000a1	e8 00 00 00 00	 call	 ?processReadyState@EventSchedule@mu2@@AEAAXAEB_J@Z ; mu2::EventSchedule::processReadyState
  000a6	90		 npad	 1

; 161  : 			break;

  000a7	eb 10		 jmp	 SHORT $LN2@Update
$LN7@Update:

; 162  : 
; 163  : 		case State::OPEN:
; 164  : 			processOpenState(current);

  000a9	48 8d 54 24 28	 lea	 rdx, QWORD PTR current$[rsp]
  000ae	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  000b3	e8 00 00 00 00	 call	 ?processOpenState@EventSchedule@mu2@@AEAAXAEB_J@Z ; mu2::EventSchedule::processOpenState
  000b8	90		 npad	 1
$LN2@Update:

; 165  : 			break;
; 166  : 		}
; 167  : 
; 168  : #ifdef __patch_PVP_Field_Renewal_by_eunseok_2019_02_22
; 169  : 		OnUpdate(current);

  000b9	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  000be	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000c1	48 8b 54 24 28	 mov	 rdx, QWORD PTR current$[rsp]
  000c6	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  000cb	ff 50 18	 call	 QWORD PTR [rax+24]
  000ce	90		 npad	 1
$LN1@Update:

; 170  : #else
; 171  : 		OnUpdate();
; 172  : #endif 
; 173  : 	}

  000cf	48 83 c4 58	 add	 rsp, 88			; 00000058H
  000d3	c3		 ret	 0
?Update@EventSchedule@mu2@@QEAAXXZ ENDP			; mu2::EventSchedule::Update
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
;	COMDAT ?InitializeEventSchedule@EventSchedule@mu2@@QEAAXXZ
_TEXT	SEGMENT
$T1 = 32
cycle$2 = 40
i$3 = 48
tv203 = 52
$T4 = 56
$T5 = 60
$T6 = 64
min$7 = 68
$T8 = 72
$T9 = 76
$T10 = 80
$T11 = 84
$T12 = 88
$T13 = 92
weekStartTime$ = 96
_My_data$14 = 104
_My_data$15 = 112
$T16 = 120
_My_data$17 = 128
$T18 = 136
$T19 = 144
$T20 = 152
$T21 = 160
$T22 = 168
$T23 = 176
$T24 = 184
$T25 = 192
$T26 = 200
$T27 = 208
$T28 = 216
schedule$29 = 224
__$ArrayPad$ = 400
this$ = 432
?InitializeEventSchedule@EventSchedule@mu2@@QEAAXXZ PROC ; mu2::EventSchedule::InitializeEventSchedule, COMDAT

; 32   : 	{

$LN423:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec a8 01
	00 00		 sub	 rsp, 424		; 000001a8H
  0000c	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR __security_cookie
  00013	48 33 c4	 xor	 rax, rsp
  00016	48 89 84 24 90
	01 00 00	 mov	 QWORD PTR __$ArrayPad$[rsp], rax

; 33   : 		prepare();

  0001e	48 8b 84 24 b0
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00026	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00029	48 8b 8c 24 b0
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00031	ff 50 10	 call	 QWORD PTR [rax+16]
  00034	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1909 :         auto& _My_data = _Mypair._Myval2;

  00035	48 8b 84 24 b0
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0003d	48 83 c0 08	 add	 rax, 8
  00041	48 89 44 24 68	 mov	 QWORD PTR _My_data$14[rsp], rax

; 1910 :         return _My_data._Myfirst == _My_data._Mylast;

  00046	48 8b 44 24 68	 mov	 rax, QWORD PTR _My_data$14[rsp]
  0004b	48 8b 4c 24 68	 mov	 rcx, QWORD PTR _My_data$14[rsp]
  00050	48 8b 49 08	 mov	 rcx, QWORD PTR [rcx+8]
  00054	48 39 08	 cmp	 QWORD PTR [rax], rcx
  00057	75 0a		 jne	 SHORT $LN11@Initialize
  00059	c7 44 24 34 01
	00 00 00	 mov	 DWORD PTR tv203[rsp], 1
  00061	eb 08		 jmp	 SHORT $LN12@Initialize
$LN11@Initialize:
  00063	c7 44 24 34 00
	00 00 00	 mov	 DWORD PTR tv203[rsp], 0
$LN12@Initialize:
  0006b	0f b6 44 24 34	 movzx	 eax, BYTE PTR tv203[rsp]
  00070	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 35   : 		if (m_vecScheduleCycle.empty())

  00074	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  00079	0f b6 c0	 movzx	 eax, al
  0007c	85 c0		 test	 eax, eax
  0007e	74 17		 je	 SHORT $LN5@Initialize

; 36   : 		{
; 37   : 			m_state = State::EMPTY;

  00080	48 8b 84 24 b0
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00088	c7 80 e4 00 00
	00 00 00 00 00	 mov	 DWORD PTR [rax+228], 0

; 38   : 			return;

  00092	e9 50 03 00 00	 jmp	 $LN1@Initialize
$LN5@Initialize:

; 39   : 		}
; 40   : 
; 41   : 		time_t weekStartTime = getWeekStartTime();

  00097	48 8b 8c 24 b0
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0009f	e8 00 00 00 00	 call	 ?getWeekStartTime@EventSchedule@mu2@@IEAA_JXZ ; mu2::EventSchedule::getWeekStartTime
  000a4	48 89 44 24 60	 mov	 QWORD PTR weekStartTime$[rsp], rax

; 43   : 		for (UInt32 i = 0; i < m_vecScheduleCycle.size(); ++i)

  000a9	c7 44 24 30 00
	00 00 00	 mov	 DWORD PTR i$3[rsp], 0
  000b1	eb 0a		 jmp	 SHORT $LN4@Initialize
$LN2@Initialize:
  000b3	8b 44 24 30	 mov	 eax, DWORD PTR i$3[rsp]
  000b7	ff c0		 inc	 eax
  000b9	89 44 24 30	 mov	 DWORD PTR i$3[rsp], eax
$LN4@Initialize:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1914 :         auto& _My_data = _Mypair._Myval2;

  000bd	48 8b 84 24 b0
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000c5	48 83 c0 08	 add	 rax, 8
  000c9	48 89 44 24 70	 mov	 QWORD PTR _My_data$15[rsp], rax

; 1915 :         return static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst);

  000ce	48 8b 44 24 70	 mov	 rax, QWORD PTR _My_data$15[rsp]
  000d3	48 8b 4c 24 70	 mov	 rcx, QWORD PTR _My_data$15[rsp]
  000d8	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  000db	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  000df	48 2b c1	 sub	 rax, rcx
  000e2	48 99		 cdq
  000e4	b9 07 00 00 00	 mov	 ecx, 7
  000e9	48 f7 f9	 idiv	 rcx
  000ec	48 89 44 24 78	 mov	 QWORD PTR $T16[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 43   : 		for (UInt32 i = 0; i < m_vecScheduleCycle.size(); ++i)

  000f1	8b 44 24 30	 mov	 eax, DWORD PTR i$3[rsp]
  000f5	48 8b 4c 24 78	 mov	 rcx, QWORD PTR $T16[rsp]
  000fa	48 3b c1	 cmp	 rax, rcx
  000fd	0f 83 c5 02 00
	00		 jae	 $LN3@Initialize
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1928 :         auto& _My_data = _Mypair._Myval2;

  00103	48 8b 84 24 b0
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0010b	48 83 c0 08	 add	 rax, 8
  0010f	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _My_data$17[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 45   : 			const ScheduleCycle& cycle = m_vecScheduleCycle[i];

  00117	8b 44 24 30	 mov	 eax, DWORD PTR i$3[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1934 :         return _My_data._Myfirst[_Pos];

  0011b	48 6b c0 07	 imul	 rax, rax, 7
  0011f	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR _My_data$17[rsp]
  00127	48 03 01	 add	 rax, QWORD PTR [rcx]
  0012a	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T18[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 45   : 			const ScheduleCycle& cycle = m_vecScheduleCycle[i];

  00132	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T18[rsp]
  0013a	48 89 44 24 28	 mov	 QWORD PTR cycle$2[rsp], rax
  0013f	48 c7 84 24 e0
	00 00 00 00 00
	00 00		 mov	 QWORD PTR schedule$29[rsp], 0
  0014b	48 c7 84 24 e8
	00 00 00 00 00
	00 00		 mov	 QWORD PTR schedule$29[rsp+8], 0
  00157	48 c7 84 24 f0
	00 00 00 00 00
	00 00		 mov	 QWORD PTR schedule$29[rsp+16], 0

; 49   : 			schedule.openTime = weekStartTime + dayToSecond(cycle.startDay) + hourToSecond(cycle.startHour) + minToSecond(cycle.startMin);

  00163	48 8b 44 24 28	 mov	 rax, QWORD PTR cycle$2[rsp]
  00168	0f b6 40 01	 movzx	 eax, BYTE PTR [rax+1]
  0016c	89 44 24 38	 mov	 DWORD PTR $T4[rsp], eax
  00170	48 8b 44 24 28	 mov	 rax, QWORD PTR cycle$2[rsp]
  00175	0f b6 40 02	 movzx	 eax, BYTE PTR [rax+2]
  00179	89 44 24 3c	 mov	 DWORD PTR $T5[rsp], eax
  0017d	48 8b 44 24 28	 mov	 rax, QWORD PTR cycle$2[rsp]
  00182	0f b6 40 03	 movzx	 eax, BYTE PTR [rax+3]
  00186	89 44 24 40	 mov	 DWORD PTR $T6[rsp], eax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.h

; 42   : 		inline time_t dayToSecond(const Int32& week)	{ return week * 24 * 60 * 60; }

  0018a	6b 44 24 38 18	 imul	 eax, DWORD PTR $T4[rsp], 24
  0018f	6b c0 3c	 imul	 eax, eax, 60		; 0000003cH
  00192	6b c0 3c	 imul	 eax, eax, 60		; 0000003cH
  00195	48 98		 cdqe
  00197	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T19[rsp], rax

; 43   : 		inline time_t hourToSecond(const Int32& hour)	{ return hour * 60 * 60; }

  0019f	6b 44 24 3c 3c	 imul	 eax, DWORD PTR $T5[rsp], 60 ; 0000003cH
  001a4	6b c0 3c	 imul	 eax, eax, 60		; 0000003cH
  001a7	48 98		 cdqe
  001a9	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR $T20[rsp], rax

; 44   : 		inline time_t minToSecond(const Int32& min)		{ return min * 60; }

  001b1	6b 44 24 40 3c	 imul	 eax, DWORD PTR $T6[rsp], 60 ; 0000003cH
  001b6	48 98		 cdqe
  001b8	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T21[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 49   : 			schedule.openTime = weekStartTime + dayToSecond(cycle.startDay) + hourToSecond(cycle.startHour) + minToSecond(cycle.startMin);

  001c0	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR $T19[rsp]
  001c8	48 8b 4c 24 60	 mov	 rcx, QWORD PTR weekStartTime$[rsp]
  001cd	48 03 c8	 add	 rcx, rax
  001d0	48 8b c1	 mov	 rax, rcx
  001d3	48 03 84 24 98
	00 00 00	 add	 rax, QWORD PTR $T20[rsp]
  001db	48 03 84 24 a0
	00 00 00	 add	 rax, QWORD PTR $T21[rsp]
  001e3	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR schedule$29[rsp+8], rax
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h

; 617  :                 return _localtime64_s(_Tm, _Time);

  001eb	48 8d 94 24 e8
	00 00 00	 lea	 rdx, QWORD PTR schedule$29[rsp+8]
  001f3	48 8d 8c 24 40
	01 00 00	 lea	 rcx, QWORD PTR schedule$29[rsp+96]
  001fb	e8 00 00 00 00	 call	 _localtime64_s
  00200	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 53   : 			Int32 min = getReadyToStartTime();

  00201	48 8b 84 24 b0
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00209	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0020c	48 8b 8c 24 b0
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00214	ff 50 08	 call	 QWORD PTR [rax+8]
  00217	89 44 24 44	 mov	 DWORD PTR min$7[rsp], eax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.h

; 44   : 		inline time_t minToSecond(const Int32& min)		{ return min * 60; }

  0021b	6b 44 24 44 3c	 imul	 eax, DWORD PTR min$7[rsp], 60 ; 0000003cH
  00220	48 98		 cdqe
  00222	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T22[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 54   : 			schedule.readyTime = schedule.openTime - minToSecond(min);

  0022a	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T22[rsp]
  00232	48 8b 8c 24 e8
	00 00 00	 mov	 rcx, QWORD PTR schedule$29[rsp+8]
  0023a	48 2b c8	 sub	 rcx, rax
  0023d	48 8b c1	 mov	 rax, rcx
  00240	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR schedule$29[rsp], rax
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h

; 617  :                 return _localtime64_s(_Tm, _Time);

  00248	48 8d 94 24 e0
	00 00 00	 lea	 rdx, QWORD PTR schedule$29[rsp]
  00250	48 8d 8c 24 1c
	01 00 00	 lea	 rcx, QWORD PTR schedule$29[rsp+60]
  00258	e8 00 00 00 00	 call	 _localtime64_s
  0025d	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 59   : 			if (cycle.endDay >= cycle.startDay)

  0025e	48 8b 44 24 28	 mov	 rax, QWORD PTR cycle$2[rsp]
  00263	0f b6 40 04	 movzx	 eax, BYTE PTR [rax+4]
  00267	48 8b 4c 24 28	 mov	 rcx, QWORD PTR cycle$2[rsp]
  0026c	0f b6 49 01	 movzx	 ecx, BYTE PTR [rcx+1]
  00270	3b c1		 cmp	 eax, ecx
  00272	0f 8c 8d 00 00
	00		 jl	 $LN6@Initialize

; 61   : 				schedule.closeTime = weekStartTime + dayToSecond(cycle.endDay) + hourToSecond(cycle.endHour) + minToSecond(cycle.endMin);

  00278	48 8b 44 24 28	 mov	 rax, QWORD PTR cycle$2[rsp]
  0027d	0f b6 40 04	 movzx	 eax, BYTE PTR [rax+4]
  00281	89 44 24 48	 mov	 DWORD PTR $T8[rsp], eax
  00285	48 8b 44 24 28	 mov	 rax, QWORD PTR cycle$2[rsp]
  0028a	0f b6 40 05	 movzx	 eax, BYTE PTR [rax+5]
  0028e	89 44 24 4c	 mov	 DWORD PTR $T9[rsp], eax
  00292	48 8b 44 24 28	 mov	 rax, QWORD PTR cycle$2[rsp]
  00297	0f b6 40 06	 movzx	 eax, BYTE PTR [rax+6]
  0029b	89 44 24 50	 mov	 DWORD PTR $T10[rsp], eax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.h

; 42   : 		inline time_t dayToSecond(const Int32& week)	{ return week * 24 * 60 * 60; }

  0029f	6b 44 24 48 18	 imul	 eax, DWORD PTR $T8[rsp], 24
  002a4	6b c0 3c	 imul	 eax, eax, 60		; 0000003cH
  002a7	6b c0 3c	 imul	 eax, eax, 60		; 0000003cH
  002aa	48 98		 cdqe
  002ac	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T23[rsp], rax

; 43   : 		inline time_t hourToSecond(const Int32& hour)	{ return hour * 60 * 60; }

  002b4	6b 44 24 4c 3c	 imul	 eax, DWORD PTR $T9[rsp], 60 ; 0000003cH
  002b9	6b c0 3c	 imul	 eax, eax, 60		; 0000003cH
  002bc	48 98		 cdqe
  002be	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T24[rsp], rax

; 44   : 		inline time_t minToSecond(const Int32& min)		{ return min * 60; }

  002c6	6b 44 24 50 3c	 imul	 eax, DWORD PTR $T10[rsp], 60 ; 0000003cH
  002cb	48 98		 cdqe
  002cd	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T25[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 61   : 				schedule.closeTime = weekStartTime + dayToSecond(cycle.endDay) + hourToSecond(cycle.endHour) + minToSecond(cycle.endMin);

  002d5	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR $T23[rsp]
  002dd	48 8b 4c 24 60	 mov	 rcx, QWORD PTR weekStartTime$[rsp]
  002e2	48 03 c8	 add	 rcx, rax
  002e5	48 8b c1	 mov	 rax, rcx
  002e8	48 03 84 24 b8
	00 00 00	 add	 rax, QWORD PTR $T24[rsp]
  002f0	48 03 84 24 c0
	00 00 00	 add	 rax, QWORD PTR $T25[rsp]
  002f8	48 89 84 24 f0
	00 00 00	 mov	 QWORD PTR schedule$29[rsp+16], rax

; 62   : 			}

  00300	e9 8b 00 00 00	 jmp	 $LN7@Initialize
$LN6@Initialize:

; 66   : 				schedule.closeTime = weekStartTime + dayToSecond(cycle.endDay + 7) + hourToSecond(cycle.endHour) + minToSecond(cycle.endMin);

  00305	48 8b 44 24 28	 mov	 rax, QWORD PTR cycle$2[rsp]
  0030a	0f b6 40 04	 movzx	 eax, BYTE PTR [rax+4]
  0030e	83 c0 07	 add	 eax, 7
  00311	89 44 24 54	 mov	 DWORD PTR $T11[rsp], eax
  00315	48 8b 44 24 28	 mov	 rax, QWORD PTR cycle$2[rsp]
  0031a	0f b6 40 05	 movzx	 eax, BYTE PTR [rax+5]
  0031e	89 44 24 58	 mov	 DWORD PTR $T12[rsp], eax
  00322	48 8b 44 24 28	 mov	 rax, QWORD PTR cycle$2[rsp]
  00327	0f b6 40 06	 movzx	 eax, BYTE PTR [rax+6]
  0032b	89 44 24 5c	 mov	 DWORD PTR $T13[rsp], eax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.h

; 42   : 		inline time_t dayToSecond(const Int32& week)	{ return week * 24 * 60 * 60; }

  0032f	6b 44 24 54 18	 imul	 eax, DWORD PTR $T11[rsp], 24
  00334	6b c0 3c	 imul	 eax, eax, 60		; 0000003cH
  00337	6b c0 3c	 imul	 eax, eax, 60		; 0000003cH
  0033a	48 98		 cdqe
  0033c	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T26[rsp], rax

; 43   : 		inline time_t hourToSecond(const Int32& hour)	{ return hour * 60 * 60; }

  00344	6b 44 24 58 3c	 imul	 eax, DWORD PTR $T12[rsp], 60 ; 0000003cH
  00349	6b c0 3c	 imul	 eax, eax, 60		; 0000003cH
  0034c	48 98		 cdqe
  0034e	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR $T27[rsp], rax

; 44   : 		inline time_t minToSecond(const Int32& min)		{ return min * 60; }

  00356	6b 44 24 5c 3c	 imul	 eax, DWORD PTR $T13[rsp], 60 ; 0000003cH
  0035b	48 98		 cdqe
  0035d	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR $T28[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 66   : 				schedule.closeTime = weekStartTime + dayToSecond(cycle.endDay + 7) + hourToSecond(cycle.endHour) + minToSecond(cycle.endMin);

  00365	48 8b 84 24 c8
	00 00 00	 mov	 rax, QWORD PTR $T26[rsp]
  0036d	48 8b 4c 24 60	 mov	 rcx, QWORD PTR weekStartTime$[rsp]
  00372	48 03 c8	 add	 rcx, rax
  00375	48 8b c1	 mov	 rax, rcx
  00378	48 03 84 24 d0
	00 00 00	 add	 rax, QWORD PTR $T27[rsp]
  00380	48 03 84 24 d8
	00 00 00	 add	 rax, QWORD PTR $T28[rsp]
  00388	48 89 84 24 f0
	00 00 00	 mov	 QWORD PTR schedule$29[rsp+16], rax
$LN7@Initialize:
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\time.h

; 617  :                 return _localtime64_s(_Tm, _Time);

  00390	48 8d 94 24 f0
	00 00 00	 lea	 rdx, QWORD PTR schedule$29[rsp+16]
  00398	48 8d 8c 24 64
	01 00 00	 lea	 rcx, QWORD PTR schedule$29[rsp+132]
  003a0	e8 00 00 00 00	 call	 _localtime64_s
  003a5	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 72   : 			m_vecSchedule.push_back(schedule);

  003a6	48 8b 84 24 b0
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  003ae	48 83 c0 20	 add	 rax, 32			; 00000020H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 933  :         _Emplace_one_at_back(_Val);

  003b2	48 8d 94 24 e0
	00 00 00	 lea	 rdx, QWORD PTR schedule$29[rsp]
  003ba	48 8b c8	 mov	 rcx, rax
  003bd	e8 00 00 00 00	 call	 ??$_Emplace_one_at_back@AEBUEventScheduleDate@mu2@@@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAAEAUEventScheduleDate@mu2@@AEBU23@@Z ; std::vector<mu2::EventScheduleDate,std::allocator<mu2::EventScheduleDate> >::_Emplace_one_at_back<mu2::EventScheduleDate const &>
  003c2	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 73   : 		}

  003c3	e9 eb fc ff ff	 jmp	 $LN2@Initialize
$LN3@Initialize:

; 74   : 		
; 75   : 
; 76   : 		refreshNextWeek();

  003c8	48 8b 8c 24 b0
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  003d0	e8 00 00 00 00	 call	 ?refreshNextWeek@EventSchedule@mu2@@AEAAXXZ ; mu2::EventSchedule::refreshNextWeek

; 77   : 
; 78   : 		m_state = State::CLOSE;

  003d5	48 8b 84 24 b0
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  003dd	c7 80 e4 00 00
	00 01 00 00 00	 mov	 DWORD PTR [rax+228], 1
$LN1@Initialize:

; 79   : 	}

  003e7	48 8b 8c 24 90
	01 00 00	 mov	 rcx, QWORD PTR __$ArrayPad$[rsp]
  003ef	48 33 cc	 xor	 rcx, rsp
  003f2	e8 00 00 00 00	 call	 __security_check_cookie
  003f7	48 81 c4 a8 01
	00 00		 add	 rsp, 424		; 000001a8H
  003fe	c3		 ret	 0
?InitializeEventSchedule@EventSchedule@mu2@@QEAAXXZ ENDP ; mu2::EventSchedule::InitializeEventSchedule
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
;	COMDAT ??1EventSchedule@mu2@@UEAA@XZ
_TEXT	SEGMENT
_Mylast$1 = 32
_Myfirst$2 = 40
_My_data$3 = 48
this$ = 56
$T4 = 64
$T5 = 72
_Last$ = 80
_First$ = 88
this$ = 112
??1EventSchedule@mu2@@UEAA@XZ PROC			; mu2::EventSchedule::~EventSchedule, COMDAT

; 12   : 	{

$LN122:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 68	 sub	 rsp, 104		; 00000068H
  00009	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7EventSchedule@mu2@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx

; 13   : 		m_vecSchedule.clear();

  00018	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 83 c0 20	 add	 rax, 32			; 00000020H
  00021	48 89 44 24 38	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1808 :         auto& _My_data    = _Mypair._Myval2;

  00026	48 8b 44 24 38	 mov	 rax, QWORD PTR this$[rsp]
  0002b	48 89 44 24 30	 mov	 QWORD PTR _My_data$3[rsp], rax

; 1809 :         pointer& _Myfirst = _My_data._Myfirst;

  00030	48 8b 44 24 30	 mov	 rax, QWORD PTR _My_data$3[rsp]
  00035	48 89 44 24 28	 mov	 QWORD PTR _Myfirst$2[rsp], rax

; 1810 :         pointer& _Mylast  = _My_data._Mylast;

  0003a	48 8b 44 24 30	 mov	 rax, QWORD PTR _My_data$3[rsp]
  0003f	48 83 c0 08	 add	 rax, 8
  00043	48 89 44 24 20	 mov	 QWORD PTR _Mylast$1[rsp], rax

; 1811 : 
; 1812 :         if (_Myfirst == _Mylast) { // already empty, nothing to do

  00048	48 8b 44 24 28	 mov	 rax, QWORD PTR _Myfirst$2[rsp]
  0004d	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Mylast$1[rsp]
  00052	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00055	48 39 08	 cmp	 QWORD PTR [rax], rcx
  00058	75 02		 jne	 SHORT $LN5@EventSched

; 1813 :             // This is an optimization for debug mode: we can avoid taking the debug lock to invalidate iterators.
; 1814 :             // Note that when clearing an empty vector, this will preserve past-the-end iterators, which is allowed by
; 1815 :             // N4950 [sequence.reqmts]/54 "a.clear() [...] may invalidate the past-the-end iterator".
; 1816 :             return;

  0005a	eb 3e		 jmp	 SHORT $LN4@EventSched
$LN5@EventSched:

; 2227 :         return _Mypair._Get_first();

  0005c	48 8b 44 24 38	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00061	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2227 :         return _Mypair._Get_first();

  00066	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  0006b	48 89 44 24 48	 mov	 QWORD PTR $T5[rsp], rax

; 1820 :         _Destroy_range(_Myfirst, _Mylast, _Getal());

  00070	48 8b 44 24 20	 mov	 rax, QWORD PTR _Mylast$1[rsp]
  00075	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00078	48 89 44 24 50	 mov	 QWORD PTR _Last$[rsp], rax
  0007d	48 8b 44 24 28	 mov	 rax, QWORD PTR _Myfirst$2[rsp]
  00082	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00085	48 89 44 24 58	 mov	 QWORD PTR _First$[rsp], rax

; 1821 :         _ASAN_VECTOR_MODIFY(static_cast<difference_type>(_Myfirst - _Mylast)); // negative when destroying elements
; 1822 :         _Mylast = _Myfirst;

  0008a	48 8b 44 24 20	 mov	 rax, QWORD PTR _Mylast$1[rsp]
  0008f	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Myfirst$2[rsp]
  00094	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00097	48 89 08	 mov	 QWORD PTR [rax], rcx
$LN4@EventSched:
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 14   : 	}

  0009a	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0009f	48 83 c0 20	 add	 rax, 32			; 00000020H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 830  :         _Tidy();

  000a3	48 8b c8	 mov	 rcx, rax
  000a6	e8 00 00 00 00	 call	 ?_Tidy@?$vector@UEventScheduleDate@mu2@@V?$allocator@UEventScheduleDate@mu2@@@std@@@std@@AEAAXXZ ; std::vector<mu2::EventScheduleDate,std::allocator<mu2::EventScheduleDate> >::_Tidy
  000ab	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 14   : 	}

  000ac	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  000b1	48 83 c0 08	 add	 rax, 8
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 830  :         _Tidy();

  000b5	48 8b c8	 mov	 rcx, rax
  000b8	e8 00 00 00 00	 call	 ?_Tidy@?$vector@UScheduleCycle@mu2@@V?$allocator@UScheduleCycle@mu2@@@std@@@std@@AEAAXXZ ; std::vector<mu2::ScheduleCycle,std::allocator<mu2::ScheduleCycle> >::_Tidy
  000bd	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 14   : 	}

  000be	48 83 c4 68	 add	 rsp, 104		; 00000068H
  000c2	c3		 ret	 0
??1EventSchedule@mu2@@UEAA@XZ ENDP			; mu2::EventSchedule::~EventSchedule
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
;	COMDAT ??0EventSchedule@mu2@@QEAA@XZ
_TEXT	SEGMENT
$T1 = 0
$T2 = 1
this$ = 8
this$ = 16
this$ = 24
this$ = 32
this$ = 40
this$ = 48
this$ = 56
this$ = 80
??0EventSchedule@mu2@@QEAA@XZ PROC			; mu2::EventSchedule::EventSchedule, COMDAT

; 8    : 	{

$LN60:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi
  00006	48 83 ec 40	 sub	 rsp, 64			; 00000040H
  0000a	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0000f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7EventSchedule@mu2@@6B@
  00016	48 89 08	 mov	 QWORD PTR [rax], rcx
  00019	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0001e	48 83 c0 08	 add	 rax, 8
  00022	48 89 44 24 20	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 670  :     _CONSTEXPR20 vector() noexcept(is_nothrow_default_constructible_v<_Alty>) : _Mypair(_Zero_then_variadic_args_t{}) {

  00027	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0002c	48 89 44 24 28	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1531 :         : _Ty1(), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  00031	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00036	48 89 44 24 08	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 400  :     _CONSTEXPR20 _Vector_val() noexcept : _Myfirst(), _Mylast(), _Myend() {}

  0003b	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00040	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0
  00047	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0004c	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0
  00054	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00059	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 671  :         _Mypair._Myval2._Alloc_proxy(_GET_PROXY_ALLOCATOR(_Alty, _Getal()));

  00061	48 8d 04 24	 lea	 rax, QWORD PTR $T1[rsp]
  00065	48 8b f8	 mov	 rdi, rax
  00068	33 c0		 xor	 eax, eax
  0006a	b9 01 00 00 00	 mov	 ecx, 1
  0006f	f3 aa		 rep stosb
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 8    : 	{

  00071	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00076	48 83 c0 20	 add	 rax, 32			; 00000020H
  0007a	48 89 44 24 30	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 670  :     _CONSTEXPR20 vector() noexcept(is_nothrow_default_constructible_v<_Alty>) : _Mypair(_Zero_then_variadic_args_t{}) {

  0007f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00084	48 89 44 24 38	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1531 :         : _Ty1(), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  00089	48 8b 44 24 38	 mov	 rax, QWORD PTR this$[rsp]
  0008e	48 89 44 24 10	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 400  :     _CONSTEXPR20 _Vector_val() noexcept : _Myfirst(), _Mylast(), _Myend() {}

  00093	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  00098	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0
  0009f	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  000a4	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0
  000ac	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  000b1	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 671  :         _Mypair._Myval2._Alloc_proxy(_GET_PROXY_ALLOCATOR(_Alty, _Getal()));

  000b9	48 8d 44 24 01	 lea	 rax, QWORD PTR $T2[rsp]
  000be	48 8b f8	 mov	 rdi, rax
  000c1	33 c0		 xor	 eax, eax
  000c3	b9 01 00 00 00	 mov	 ecx, 1
  000c8	f3 aa		 rep stosb
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp

; 8    : 	{

  000ca	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  000cf	48 83 c0 38	 add	 rax, 56			; 00000038H
  000d3	48 89 44 24 18	 mov	 QWORD PTR this$[rsp], rax
  000d8	48 8b 44 24 18	 mov	 rax, QWORD PTR this$[rsp]
  000dd	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0
  000e4	48 8b 44 24 18	 mov	 rax, QWORD PTR this$[rsp]
  000e9	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0
  000f1	48 8b 44 24 18	 mov	 rax, QWORD PTR this$[rsp]
  000f6	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 9    : 	}

  000fe	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00103	48 83 c4 40	 add	 rsp, 64			; 00000040H
  00107	5f		 pop	 rdi
  00108	c3		 ret	 0
??0EventSchedule@mu2@@QEAA@XZ ENDP			; mu2::EventSchedule::EventSchedule
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
_TEXT	SEGMENT
_Ptr_container$ = 48
_Block_size$ = 56
_Ptr$ = 64
$T1 = 72
_Bytes$ = 96
??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z PROC ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>, COMDAT

; 182  : __declspec(allocator) void* _Allocate_manually_vector_aligned(const size_t _Bytes) {

$LN7:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 183  :     // allocate _Bytes manually aligned to at least _Big_allocation_alignment
; 184  :     const size_t _Block_size = _Non_user_size + _Bytes;

  00009	48 8b 44 24 60	 mov	 rax, QWORD PTR _Bytes$[rsp]
  0000e	48 83 c0 27	 add	 rax, 39			; 00000027H
  00012	48 89 44 24 38	 mov	 QWORD PTR _Block_size$[rsp], rax

; 185  :     if (_Block_size <= _Bytes) {

  00017	48 8b 44 24 60	 mov	 rax, QWORD PTR _Bytes$[rsp]
  0001c	48 39 44 24 38	 cmp	 QWORD PTR _Block_size$[rsp], rax
  00021	77 06		 ja	 SHORT $LN2@Allocate_m

; 186  :         _Throw_bad_array_new_length(); // add overflow

  00023	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00028	90		 npad	 1
$LN2@Allocate_m:

; 136  :         return ::operator new(_Bytes);

  00029	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Block_size$[rsp]
  0002e	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00033	48 89 44 24 48	 mov	 QWORD PTR $T1[rsp], rax

; 187  :     }
; 188  : 
; 189  :     const uintptr_t _Ptr_container = reinterpret_cast<uintptr_t>(_Traits::_Allocate(_Block_size));

  00038	48 8b 44 24 48	 mov	 rax, QWORD PTR $T1[rsp]
  0003d	48 89 44 24 30	 mov	 QWORD PTR _Ptr_container$[rsp], rax

; 190  :     _STL_VERIFY(_Ptr_container != 0, "invalid argument"); // validate even in release since we're doing p[-1]

  00042	48 83 7c 24 30
	00		 cmp	 QWORD PTR _Ptr_container$[rsp], 0
  00048	75 19		 jne	 SHORT $LN3@Allocate_m
  0004a	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR [rsp+32], 0
  00053	45 33 c9	 xor	 r9d, r9d
  00056	45 33 c0	 xor	 r8d, r8d
  00059	33 d2		 xor	 edx, edx
  0005b	33 c9		 xor	 ecx, ecx
  0005d	e8 00 00 00 00	 call	 _invoke_watson
  00062	90		 npad	 1
$LN3@Allocate_m:

; 191  :     void* const _Ptr = reinterpret_cast<void*>((_Ptr_container + _Non_user_size) & ~(_Big_allocation_alignment - 1));

  00063	48 8b 44 24 30	 mov	 rax, QWORD PTR _Ptr_container$[rsp]
  00068	48 83 c0 27	 add	 rax, 39			; 00000027H
  0006c	48 83 e0 e0	 and	 rax, -32		; ffffffffffffffe0H
  00070	48 89 44 24 40	 mov	 QWORD PTR _Ptr$[rsp], rax

; 192  :     static_cast<uintptr_t*>(_Ptr)[-1] = _Ptr_container;

  00075	b8 08 00 00 00	 mov	 eax, 8
  0007a	48 6b c0 ff	 imul	 rax, rax, -1
  0007e	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  00083	48 8b 54 24 30	 mov	 rdx, QWORD PTR _Ptr_container$[rsp]
  00088	48 89 14 01	 mov	 QWORD PTR [rcx+rax], rdx

; 193  : 
; 194  : #ifdef _DEBUG
; 195  :     static_cast<uintptr_t*>(_Ptr)[-2] = _Big_allocation_sentinel;
; 196  : #endif // defined(_DEBUG)
; 197  :     return _Ptr;

  0008c	48 8b 44 24 40	 mov	 rax, QWORD PTR _Ptr$[rsp]
$LN4@Allocate_m:

; 198  : }

  00091	48 83 c4 58	 add	 rsp, 88			; 00000058H
  00095	c3		 ret	 0
??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ENDP ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ?_Tidy@?$vector@UScheduleCycle@mu2@@V?$allocator@UScheduleCycle@mu2@@@std@@@std@@AEAAXXZ
_TEXT	SEGMENT
_Myfirst$ = 32
_My_data$ = 40
_Bytes$ = 48
_Ptr$ = 56
_Mylast$ = 64
_Myend$ = 72
$T1 = 80
$T2 = 88
_Count$ = 96
_Ptr$ = 104
_Al$ = 112
_Last$ = 120
_First$ = 128
this$ = 160
?_Tidy@?$vector@UScheduleCycle@mu2@@V?$allocator@UScheduleCycle@mu2@@@std@@@std@@AEAAXXZ PROC ; std::vector<mu2::ScheduleCycle,std::allocator<mu2::ScheduleCycle> >::_Tidy, COMDAT

; 2081 :     _CONSTEXPR20 void _Tidy() noexcept { // free all storage

$LN40:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec 98 00
	00 00		 sub	 rsp, 152		; 00000098H

; 2227 :         return _Mypair._Get_first();

  0000c	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00014	48 89 44 24 50	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2227 :         return _Mypair._Get_first();

  00019	48 8b 44 24 50	 mov	 rax, QWORD PTR $T1[rsp]
  0001e	48 89 44 24 58	 mov	 QWORD PTR $T2[rsp], rax

; 2082 :         auto& _Al         = _Getal();

  00023	48 8b 44 24 58	 mov	 rax, QWORD PTR $T2[rsp]
  00028	48 89 44 24 70	 mov	 QWORD PTR _Al$[rsp], rax

; 2083 :         auto& _My_data    = _Mypair._Myval2;

  0002d	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00035	48 89 44 24 28	 mov	 QWORD PTR _My_data$[rsp], rax

; 2084 :         pointer& _Myfirst = _My_data._Myfirst;

  0003a	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  0003f	48 89 44 24 20	 mov	 QWORD PTR _Myfirst$[rsp], rax

; 2085 :         pointer& _Mylast  = _My_data._Mylast;

  00044	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00049	48 83 c0 08	 add	 rax, 8
  0004d	48 89 44 24 40	 mov	 QWORD PTR _Mylast$[rsp], rax

; 2086 :         pointer& _Myend   = _My_data._Myend;

  00052	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00057	48 83 c0 10	 add	 rax, 16
  0005b	48 89 44 24 48	 mov	 QWORD PTR _Myend$[rsp], rax

; 2087 : 
; 2088 :         _My_data._Orphan_all();
; 2089 : 
; 2090 :         if (_Myfirst) { // destroy and deallocate old array

  00060	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  00065	48 83 38 00	 cmp	 QWORD PTR [rax], 0
  00069	0f 84 b0 00 00
	00		 je	 $LN2@Tidy

; 2091 :             _STD _Destroy_range(_Myfirst, _Mylast, _Al);

  0006f	48 8b 44 24 40	 mov	 rax, QWORD PTR _Mylast$[rsp]
  00074	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00077	48 89 44 24 78	 mov	 QWORD PTR _Last$[rsp], rax
  0007c	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  00081	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00084	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _First$[rsp], rax

; 2092 :             _ASAN_VECTOR_REMOVE;
; 2093 :             _Al.deallocate(_Myfirst, static_cast<size_type>(_Myend - _Myfirst));

  0008c	48 8b 44 24 48	 mov	 rax, QWORD PTR _Myend$[rsp]
  00091	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Myfirst$[rsp]
  00096	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00099	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0009c	48 2b c1	 sub	 rax, rcx
  0009f	48 99		 cdq
  000a1	b9 07 00 00 00	 mov	 ecx, 7
  000a6	48 f7 f9	 idiv	 rcx
  000a9	48 89 44 24 60	 mov	 QWORD PTR _Count$[rsp], rax
  000ae	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  000b3	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000b6	48 89 44 24 68	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  000bb	48 6b 44 24 60
	07		 imul	 rax, QWORD PTR _Count$[rsp], 7
  000c1	48 89 44 24 30	 mov	 QWORD PTR _Bytes$[rsp], rax
  000c6	48 8b 44 24 68	 mov	 rax, QWORD PTR _Ptr$[rsp]
  000cb	48 89 44 24 38	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  000d0	48 81 7c 24 30
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  000d9	72 10		 jb	 SHORT $LN31@Tidy

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  000db	48 8d 54 24 30	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  000e0	48 8d 4c 24 38	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  000e5	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  000ea	90		 npad	 1
$LN31@Tidy:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  000eb	48 8b 54 24 30	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  000f0	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  000f5	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  000fa	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2095 :             _Myfirst = nullptr;

  000fb	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  00100	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0

; 2096 :             _Mylast  = nullptr;

  00107	48 8b 44 24 40	 mov	 rax, QWORD PTR _Mylast$[rsp]
  0010c	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0

; 2097 :             _Myend   = nullptr;

  00113	48 8b 44 24 48	 mov	 rax, QWORD PTR _Myend$[rsp]
  00118	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0
$LN2@Tidy:

; 2098 :         }
; 2099 :     }

  0011f	48 81 c4 98 00
	00 00		 add	 rsp, 152		; 00000098H
  00126	c3		 ret	 0
?_Tidy@?$vector@UScheduleCycle@mu2@@V?$allocator@UScheduleCycle@mu2@@@std@@@std@@AEAAXXZ ENDP ; std::vector<mu2::ScheduleCycle,std::allocator<mu2::ScheduleCycle> >::_Tidy
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z
_TEXT	SEGMENT
_Back_shift$ = 48
_Ptr_container$ = 56
_Ptr_user$ = 64
_Min_back_shift$ = 72
_Ptr$ = 96
_Bytes$ = 104
?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z PROC ; std::_Adjust_manually_vector_aligned, COMDAT

; 200  : inline void _Adjust_manually_vector_aligned(void*& _Ptr, size_t& _Bytes) {

$LN5:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 201  :     // adjust parameters from _Allocate_manually_vector_aligned to pass to operator delete
; 202  :     _Bytes += _Non_user_size;

  0000e	48 8b 44 24 68	 mov	 rax, QWORD PTR _Bytes$[rsp]
  00013	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00016	48 83 c0 27	 add	 rax, 39			; 00000027H
  0001a	48 8b 4c 24 68	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  0001f	48 89 01	 mov	 QWORD PTR [rcx], rax

; 203  : 
; 204  :     const uintptr_t* const _Ptr_user = static_cast<uintptr_t*>(_Ptr);

  00022	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00027	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0002a	48 89 44 24 40	 mov	 QWORD PTR _Ptr_user$[rsp], rax

; 205  :     const uintptr_t _Ptr_container   = _Ptr_user[-1];

  0002f	b8 08 00 00 00	 mov	 eax, 8
  00034	48 6b c0 ff	 imul	 rax, rax, -1
  00038	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr_user$[rsp]
  0003d	48 8b 04 01	 mov	 rax, QWORD PTR [rcx+rax]
  00041	48 89 44 24 38	 mov	 QWORD PTR _Ptr_container$[rsp], rax

; 206  : 
; 207  :     // If the following asserts, it likely means that we are performing
; 208  :     // an aligned delete on memory coming from an unaligned allocation.
; 209  :     _STL_ASSERT(_Ptr_user[-2] == _Big_allocation_sentinel, "invalid argument");
; 210  : 
; 211  :     // Extra paranoia on aligned allocation/deallocation; ensure _Ptr_container is
; 212  :     // in range [_Min_back_shift, _Non_user_size]
; 213  : #ifdef _DEBUG
; 214  :     constexpr uintptr_t _Min_back_shift = 2 * sizeof(void*);
; 215  : #else // ^^^ defined(_DEBUG) / !defined(_DEBUG) vvv
; 216  :     constexpr uintptr_t _Min_back_shift = sizeof(void*);

  00046	48 c7 44 24 48
	08 00 00 00	 mov	 QWORD PTR _Min_back_shift$[rsp], 8

; 217  : #endif // ^^^ !defined(_DEBUG) ^^^
; 218  :     const uintptr_t _Back_shift = reinterpret_cast<uintptr_t>(_Ptr) - _Ptr_container;

  0004f	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00054	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr_container$[rsp]
  00059	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0005c	48 2b c1	 sub	 rax, rcx
  0005f	48 89 44 24 30	 mov	 QWORD PTR _Back_shift$[rsp], rax

; 219  :     _STL_VERIFY(_Back_shift >= _Min_back_shift && _Back_shift <= _Non_user_size, "invalid argument");

  00064	48 83 7c 24 30
	08		 cmp	 QWORD PTR _Back_shift$[rsp], 8
  0006a	72 08		 jb	 SHORT $LN3@Adjust_man
  0006c	48 83 7c 24 30
	27		 cmp	 QWORD PTR _Back_shift$[rsp], 39 ; 00000027H
  00072	76 19		 jbe	 SHORT $LN2@Adjust_man
$LN3@Adjust_man:
  00074	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR [rsp+32], 0
  0007d	45 33 c9	 xor	 r9d, r9d
  00080	45 33 c0	 xor	 r8d, r8d
  00083	33 d2		 xor	 edx, edx
  00085	33 c9		 xor	 ecx, ecx
  00087	e8 00 00 00 00	 call	 _invoke_watson
  0008c	90		 npad	 1
$LN2@Adjust_man:

; 220  :     _Ptr = reinterpret_cast<void*>(_Ptr_container);

  0008d	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00092	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr_container$[rsp]
  00097	48 89 08	 mov	 QWORD PTR [rax], rcx
$LN4@Adjust_man:

; 221  : }

  0009a	48 83 c4 58	 add	 rsp, 88			; 00000058H
  0009e	c3		 ret	 0
?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ENDP ; std::_Adjust_manually_vector_aligned
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?_Throw_bad_array_new_length@std@@YAXXZ
_TEXT	SEGMENT
$T1 = 32
?_Throw_bad_array_new_length@std@@YAXXZ PROC		; std::_Throw_bad_array_new_length, COMDAT

; 107  : [[noreturn]] inline void _Throw_bad_array_new_length() {

$LN3:
  00000	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 108  :     _THROW(bad_array_new_length{});

  00004	48 8d 4c 24 20	 lea	 rcx, QWORD PTR $T1[rsp]
  00009	e8 00 00 00 00	 call	 ??0bad_array_new_length@std@@QEAA@XZ ; std::bad_array_new_length::bad_array_new_length
  0000e	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:_TI3?AVbad_array_new_length@std@@
  00015	48 8d 4c 24 20	 lea	 rcx, QWORD PTR $T1[rsp]
  0001a	e8 00 00 00 00	 call	 _CxxThrowException
  0001f	90		 npad	 1
$LN2@Throw_bad_:

; 109  : }

  00020	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00024	c3		 ret	 0
?_Throw_bad_array_new_length@std@@YAXXZ ENDP		; std::_Throw_bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gbad_array_new_length@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gbad_array_new_length@std@@UEAAPEAXI@Z PROC		; std::bad_array_new_length::`scalar deleting destructor', COMDAT
$LN20:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gbad_array_new_length@std@@UEAAPEAXI@Z ENDP		; std::bad_array_new_length::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??0bad_array_new_length@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
__that$ = 56
??0bad_array_new_length@std@@QEAA@AEBV01@@Z PROC	; std::bad_array_new_length::bad_array_new_length, COMDAT
$LN14:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000e	48 8b 54 24 38	 mov	 rdx, QWORD PTR __that$[rsp]
  00013	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00018	e8 00 00 00 00	 call	 ??0bad_alloc@std@@QEAA@AEBV01@@Z
  0001d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00022	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_array_new_length@std@@6B@
  00029	48 89 08	 mov	 QWORD PTR [rax], rcx
  0002c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00031	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00035	c3		 ret	 0
??0bad_array_new_length@std@@QEAA@AEBV01@@Z ENDP	; std::bad_array_new_length::bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??1bad_array_new_length@std@@UEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1bad_array_new_length@std@@UEAA@XZ PROC		; std::bad_array_new_length::~bad_array_new_length, COMDAT
$LN14:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  00018	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 83 c0 08	 add	 rax, 8
  00021	48 8b c8	 mov	 rcx, rax
  00024	e8 00 00 00 00	 call	 __std_exception_destroy
  00029	90		 npad	 1
  0002a	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0002e	c3		 ret	 0
??1bad_array_new_length@std@@UEAA@XZ ENDP		; std::bad_array_new_length::~bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0bad_array_new_length@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 16
??0bad_array_new_length@std@@QEAA@XZ PROC		; std::bad_array_new_length::bad_array_new_length, COMDAT

; 144  :     {

$LN14:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi

; 67   :     {

  00006	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0000b	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00012	48 89 08	 mov	 QWORD PTR [rax], rcx

; 66   :         : _Data()

  00015	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 83 c0 08	 add	 rax, 8
  0001e	48 8b f8	 mov	 rdi, rax
  00021	33 c0		 xor	 eax, eax
  00023	b9 10 00 00 00	 mov	 ecx, 16
  00028	f3 aa		 rep stosb

; 68   :         _Data._What = _Message;

  0002a	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0002f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_0BF@KINCDENJ@bad?5array?5new?5length@
  00036	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 133  :     {

  0003a	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0003f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_alloc@std@@6B@
  00046	48 89 08	 mov	 QWORD PTR [rax], rcx

; 144  :     {

  00049	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_array_new_length@std@@6B@
  00055	48 89 08	 mov	 QWORD PTR [rax], rcx

; 145  :     }

  00058	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0005d	5f		 pop	 rdi
  0005e	c3		 ret	 0
??0bad_array_new_length@std@@QEAA@XZ ENDP		; std::bad_array_new_length::bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gbad_alloc@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gbad_alloc@std@@UEAAPEAXI@Z PROC			; std::bad_alloc::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gbad_alloc@std@@UEAAPEAXI@Z ENDP			; std::bad_alloc::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0bad_alloc@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
__that$ = 56
??0bad_alloc@std@@QEAA@AEBV01@@Z PROC			; std::bad_alloc::bad_alloc, COMDAT
$LN9:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 20	 sub	 rsp, 32			; 00000020H

; 73   :     {

  0000f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  0001b	48 89 08	 mov	 QWORD PTR [rax], rcx

; 72   :         : _Data()

  0001e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 83 c0 08	 add	 rax, 8
  00027	48 8b f8	 mov	 rdi, rax
  0002a	33 c0		 xor	 eax, eax
  0002c	b9 10 00 00 00	 mov	 ecx, 16
  00031	f3 aa		 rep stosb

; 74   :         __std_exception_copy(&_Other._Data, &_Data);

  00033	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 83 c0 08	 add	 rax, 8
  0003c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR __that$[rsp]
  00041	48 83 c1 08	 add	 rcx, 8
  00045	48 8b d0	 mov	 rdx, rax
  00048	e8 00 00 00 00	 call	 __std_exception_copy
  0004d	90		 npad	 1
  0004e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_alloc@std@@6B@
  0005a	48 89 08	 mov	 QWORD PTR [rax], rcx
  0005d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00062	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00066	5f		 pop	 rdi
  00067	c3		 ret	 0
??0bad_alloc@std@@QEAA@AEBV01@@Z ENDP			; std::bad_alloc::bad_alloc
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gexception@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gexception@std@@UEAAPEAXI@Z PROC			; std::exception::`scalar deleting destructor', COMDAT
$LN10:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gexception@std@@UEAAPEAXI@Z ENDP			; std::exception::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ?what@exception@std@@UEBAPEBDXZ
_TEXT	SEGMENT
tv69 = 0
this$ = 32
?what@exception@std@@UEBAPEBDXZ PROC			; std::exception::what, COMDAT

; 95   :     {

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 18	 sub	 rsp, 24

; 96   :         return _Data._What ? _Data._What : "Unknown exception";

  00009	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00013	74 0f		 je	 SHORT $LN3@what
  00015	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0001e	48 89 04 24	 mov	 QWORD PTR tv69[rsp], rax
  00022	eb 0b		 jmp	 SHORT $LN4@what
$LN3@what:
  00024	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BC@EOODALEL@Unknown?5exception@
  0002b	48 89 04 24	 mov	 QWORD PTR tv69[rsp], rax
$LN4@what:
  0002f	48 8b 04 24	 mov	 rax, QWORD PTR tv69[rsp]

; 97   :     }

  00033	48 83 c4 18	 add	 rsp, 24
  00037	c3		 ret	 0
?what@exception@std@@UEBAPEBDXZ ENDP			; std::exception::what
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0exception@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
_Other$ = 56
??0exception@std@@QEAA@AEBV01@@Z PROC			; std::exception::exception, COMDAT

; 73   :     {

$LN4:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  0000f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  0001b	48 89 08	 mov	 QWORD PTR [rax], rcx

; 72   :         : _Data()

  0001e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 83 c0 08	 add	 rax, 8
  00027	48 8b f8	 mov	 rdi, rax
  0002a	33 c0		 xor	 eax, eax
  0002c	b9 10 00 00 00	 mov	 ecx, 16
  00031	f3 aa		 rep stosb

; 74   :         __std_exception_copy(&_Other._Data, &_Data);

  00033	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 83 c0 08	 add	 rax, 8
  0003c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Other$[rsp]
  00041	48 83 c1 08	 add	 rcx, 8
  00045	48 8b d0	 mov	 rdx, rax
  00048	e8 00 00 00 00	 call	 __std_exception_copy
  0004d	90		 npad	 1

; 75   :     }

  0004e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00057	5f		 pop	 rdi
  00058	c3		 ret	 0
??0exception@std@@QEAA@AEBV01@@Z ENDP			; std::exception::exception
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventSchedule.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
