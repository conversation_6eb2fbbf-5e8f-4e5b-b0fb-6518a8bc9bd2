; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	?SendResJoinResult@JoinContextMapWarp@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z ; mu2::JoinContextMapWarp::SendResJoinResult
EXTRN	?SendEscResGamePortal@JoinContextBase@mu2@@IEBAXAEAPEAVEntityPlayer@2@AEBVJoinZoneContextDest@2@W4Error@E<PERSON>rJoin@2@@Z:PROC ; mu2::JoinContextBase::SendEscResGamePortal
;	COMDAT pdata
pdata	SEGMENT
$pdata$?SendResJoinResult@JoinContextMapWarp@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z DD imagerel $LN3
	DD	imagerel $LN3+55
	DD	imagerel $unwind$?SendResJoinResult@JoinContextMapWarp@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z
pdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?SendResJoinResult@JoinContextMapWarp@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z DD 011801H
	DD	04218H
xdata	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextWarpMap.cpp
;	COMDAT ?SendResJoinResult@JoinContextMapWarp@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z
_TEXT	SEGMENT
this$ = 48
player$ = 56
arrived$ = 64
eError$ = 72
?SendResJoinResult@JoinContextMapWarp@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z PROC ; mu2::JoinContextMapWarp::SendResJoinResult, COMDAT

; 7    : 	{	

$LN3:
  00000	44 89 4c 24 20	 mov	 DWORD PTR [rsp+32], r9d
  00005	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 8    : 		SendEscResGamePortal(player, arrived, eError);

  00018	44 8b 4c 24 48	 mov	 r9d, DWORD PTR eError$[rsp]
  0001d	4c 8b 44 24 40	 mov	 r8, QWORD PTR arrived$[rsp]
  00022	48 8b 54 24 38	 mov	 rdx, QWORD PTR player$[rsp]
  00027	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0002c	e8 00 00 00 00	 call	 ?SendEscResGamePortal@JoinContextBase@mu2@@IEBAXAEAPEAVEntityPlayer@2@AEBVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z ; mu2::JoinContextBase::SendEscResGamePortal
  00031	90		 npad	 1

; 9    : 	}

  00032	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00036	c3		 ret	 0
?SendResJoinResult@JoinContextMapWarp@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z ENDP ; mu2::JoinContextMapWarp::SendResJoinResult
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextWarpMap.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextWarpMap.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
