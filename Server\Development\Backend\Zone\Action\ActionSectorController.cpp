﻿#include "stdafx.h"
#include <Backend/Zone/ZoneServer.h>
#include <Backend/Zone/World/MissionMap/AltarOfElementsProcessor.h>
#include <Backend/Zone/World/MissionMap/ChaosCastleProcessor.h>
#include <Backend/Zone/World/MissionMap/DeathMatchProcessor.h>
#include <Backend/Zone/World/MissionMap/EndlessTowerProcessor.h>
#include <Backend/Zone/World/MissionMap/BloodCastleProcessor.h>
#include <Backend/Zone/World/MissionMap/TournamentAltarOfElementsProcessor.h>
#include <Backend/Zone/World/MissionMap/BattleFieldInTheSkyProcessor.h>
#include <Backend/Zone/World/MissionMap/ColosseumPVP33Processor.h>
#include <Backend/Zone/World/MissionMap/PVPChaosCastleProcessor.h>
#include <Backend/Zone/World/MissionMap/PVPFieldProcessor.h>
#include <Backend/Zone/World/MissionMap/NetherWorldProcessor.h>
#ifdef __Patch_Tower_of_dawn_eunseok_2018_11_05
#include <Backend/Zone/World/MissionMap/TowerOfDawnProcessor.h>
#endif //__Patch_Tower_of_dawn_eunseok_2018_11_05
#include <Backend/Zone/World/DominionProcessor.h>
#include <Backend/Zone/World/ColosseumProcessor.h>
#include <Backend/Zone/World/MazeProcessor.h>
#include <Backend/Zone/World/OhrdorSewerageProcessor.h>
#include <Backend/Zone/World/MazeInDarknessProcessor.h>
#include <Backend/Zone/World/ExplorerProcessor.h>
#include <Backend/Zone/World/BossProcessor.h>
#include <Backend/Zone/World/MissionMap/AirshipDefenceProcessor.h>
#include <Backend/Zone/World/GUild/GuildhallProcessor.h>
#include <Backend/Zone/World/FieldRaidProcessor.h>

#include <Backend/Zone/Action/ActionPlayerInventory.h>
#include <Backend/Zone/Action/ActionMissionDunGeon.h>
#include <Backend/Zone/Action/ActionParty.h>
#include <Backend/Zone/Action/ActionPlayer.h>
#include <Backend/Zone/Action/ActionPortal.h>
#include <Backend/Zone/Action/ActionNpc/ActionNpcOperate.h>
#include <Backend/Zone/Action/ActionFunctional.h>
#include <Backend/Zone/Action/ActionQuest.h>
#include <Backend/Zone/Action/ActionNpcDamage.h>
#include <Backend/Zone/Action/ActionNpc/ActionNpcCognition.h>

#include <Backend/Zone/Action/ActionNpc.h>
#include <Backend/Zone/Action/ActionNpcAggro.h>
#include <Backend/Zone/Action/ActionNpcScript.h>
#include <Backend/Zone/Action/ActionNpcSkill.h>

#include <Backend/Zone/System/LogicEffectSystem.h>
#include <Backend/Zone/System/SpawnSystem.h>//

#include <Backend/Zone/State/StatePlayerBase.h>
#include <Backend/Zone/Action/ActionBuff.h>
#include <Backend/Zone/World/SectorRunner.h>
#include <Backend/Zone/View/ViewPosition.h>

#ifdef __Patch_WebOfGod_by_jason_20181213
#include <Backend/Zone/World/PVEMissionMap/WebOfGodProcessor.h>
#endif


namespace mu2
{
#ifdef __Reincarnation__jason_180628__
#else
void SendInSector( Sector* sector, EventPtr evt );
#endif

ActionSectorController::ActionSectorController(EntitySectorController* owner )
	: Action4SectorController( owner, ID )
, m_L( nullptr )
, m_sector( nullptr )
, m_name()
, m_sectorLuaName()
, m_timers()
, m_workFlags()
, m_maxCrowdNumber( 0 )
, m_processor( nullptr )
#ifdef __Renewal_Dungeon_Entrance_Cost_by_jason_20190114
#else
, m_invalidRequirement( false )
#endif
{
	CounterInc("ActionSectorController");

	MU2_ASSERT( owner );
}

ActionSectorController::~ActionSectorController()
{
	CounterDec("ActionSectorController");

	unregisterSector();

	if ( m_processor != nullptr )
	{
		delete m_processor;
		m_processor = nullptr;
	}
}

Bool ActionSectorController::Setup( Sector* sector )
{
	// 스크립트 실행 가능 여부와 관계 없이 설정되도록 한다. 
	// 그리고 궁극적으로 모든 섹터가 섹터 제어기를 통해 설정이 가능하도록 한다. 
	VERIFY_RETURN( sector, false );

	m_L = sector->GetLuaState();
	m_sector = sector;
	m_name = "sector_template";

	registerSector();

	return true;
}

void ActionSectorController::OnStateEnter( UInt32 stateId )
{
	callStateFunction( stateId, "enter" );	
}

void ActionSectorController::OnStateDecide( UInt32 stateId )
{
	callStateFunction( stateId, "decide" );	
}

void ActionSectorController::OnStateExit( UInt32 stateId )
{
	callStateFunction( stateId, "exit" );	
}

void ActionSectorController::AddBlueScore( UInt32 score )
{
	VERIFY_RETURN( m_L, );

	lua_pushcclosure(m_L, lua_util::on_error, 0);
	int errfunc = lua_gettop(m_L);

	lua_getglobal(m_L, "add_blue_score");
	lua_pushnumber( m_L, score );

	if ( lua_pcall( m_L, 1, 0, errfunc) )
	{
		MU2_WARN_LOG(LogCategory::CONTENTS, "lua error : add_red_score" );
	}
}

void ActionSectorController::AddRedScore( UInt32 score )
{
	VERIFY_RETURN( m_L, );

	lua_pushcclosure(m_L, lua_util::on_error, 0);
	int errfunc = lua_gettop(m_L);

	lua_getglobal(m_L, "add_red_score");
	lua_pushnumber( m_L, score );
	
	if ( lua_pcall( m_L, 1, 0, errfunc) )
	{
		MU2_WARN_LOG(LogCategory::CONTENTS, "lua error : add_red_score" );
	}
}

Bool ActionSectorController::callStateFunction( UInt32 stateId, const char* func )
{
	if ( m_L == nullptr )
		return false;

	bool rc = false;

	std::string control = "sector_template";
	lua_getglobal( m_L, control.c_str() );

	if ( lua_istable(m_L, -1) )
	{
		// get state table
		lua_pushnumber(m_L, stateId);
		lua_gettable(m_L, -2);

		if ( lua_istable(m_L, -1) )
		{
			lua_pushcclosure(m_L, lua_util::on_error, 0);
			int errfunc = lua_gettop(m_L);

			// get function
			lua_pushstring(m_L, func);
			lua_gettable(m_L, -3); // func / on_error / state

			if ( lua_isfunction(m_L, -1) )
			{
				pushSector(); // 글로벌에 등록된 NPC를 가져옴
				lua_pcall(m_L, 1, 0, errfunc);

				rc = true;
			}
			else
			{
				lua_pop(m_L, 1); // 함수 제거
			}

			lua_remove(m_L, errfunc);
		}
		else
		{
			//stackDump();
		}

		lua_pop(m_L, 1); // pop state table
	}
	else
	{
		//stackDump();
	}

	lua_pop(m_L, 1); // pop npc table	

	

	return true;
}

void ActionSectorController::stackDump()
{
	int top = lua_gettop(m_L);
	for ( int i = 1; i < top; i++ )
	{
		int t = lua_type( m_L, i );
		switch ( t )
		{
		case LUA_TSTRING:
			MU2_ERROR_LOG( LogCategory::CONTENTS, "Lua Dump : %s\n", lua_tostring( m_L, i ) );
			break;
		case LUA_TBOOLEAN:
			MU2_ERROR_LOG( LogCategory::CONTENTS, "Lua Dump : %s\n", lua_toboolean( m_L, i ) ? "true" : "false" );
			break;
		case LUA_TNUMBER:
			MU2_ERROR_LOG( LogCategory::CONTENTS, "Lua Dump : %d\n", lua_tonumber( m_L, i ) );
			break;
		default:
			MU2_ERROR_LOG( LogCategory::CONTENTS, "Lua Dump Type : %s", lua_typename( m_L, t ) );
			break;
		}
	}
}


Bool ActionSectorController::Tran(EntityState eState )
{
	Hsm& hsm = GetOwnerController()->GetHsm();

	if ( hsm.GetState( eState ) == NULL  )
	{
		MU2_WARN_LOG(LogCategory::CONTENTS, "[%s] State %d not found", __FUNCTION__, eState );

		return false;
	}

	hsm.Tran( eState );

	if ( m_processor != nullptr )
	{
		m_processor->UpdateState( eState );
	}

	return true;
}

void ActionSectorController::Log( const char* msg )
{
	if ( msg == nullptr )
	{
		return;
	}

	size_t length = strlen(msg);
	if ( length > 0 )
	{
		MU2_DEBUG_LOG( LogCategory::DEBUG_SECTOR_LUA_SCRIPT, "[%d] %s", GetId(), msg );
	}
}

void ActionSectorController::Debug( const char* msg )
{
	if ( msg == nullptr )
	{
		return;
	}

	size_t length = strlen(msg);
	if ( length > 0 )
	{
		MU2_DEBUG_LOG( LogCategory::DEBUG_SECTOR_LUA_SCRIPT, "[%d] %s", GetId(), msg );
	}
}

void ActionSectorController::Message( const char* msg )
{
	if ( msg == nullptr )
	{
		return;
	}
	
	if (false == SERVER.GetZoneInfo().usingLuaLogMessage)
	{
		return;
	}
	
	size_t length = strlen( msg );
	if ( length > 0 )
	{
		VectorPlayer vecPlayer;
		m_sector->GetPlayers(vecPlayer );

		for ( auto i = vecPlayer.begin(); i != vecPlayer.end(); ++i )
		{
			EntityPlayer* player = ( *i );
			if ( player == nullptr )
				continue;			

			ENtfGameChatNormal* ntf = NEW ENtfGameChatNormal;	
			ntf->nickName = std::wstring( L"Lua Script" );
			ntf->charId = player->GetCharId();
			ntf->msg = std::wstring( msg, msg + length );			
			SERVER.SendToClient( player, EventPtr( ntf ) );
		}		
	}
}

Bool ActionSectorController::registerSector()
{
	// 섹터는 맵에 하나이므로 글로벌하게 고정함.
	m_sectorLuaName = "g_sector";
	
	lua_util::set( m_L, m_sectorLuaName.c_str(), this );

	return true;
}

Bool ActionSectorController::pushSector()
{
	lua_getglobal(m_L, m_sectorLuaName.c_str() );

	MU2_ASSERT( !lua_isnil(m_L, -1) );

	return !lua_isnil(m_L, -1);
}

void ActionSectorController::unregisterSector()
{
	//lua_util::set( m_L, m_sectorLuaName.c_str(), nullptr );
}

Bool ActionSectorController::HasTimer( const UInt32 id )
{
	return ( m_timers.find( id ) != m_timers.end() );
}

void ActionSectorController::AddTimer( const UInt32 id )
{
	m_timers.erase( id );

	m_timers.insert( Timers::value_type(id, SimpleTimer()) );
}

void ActionSectorController::DelTimer( const UInt32 id )
{
	m_timers.erase( id );
}

void ActionSectorController::ResetTimer( const UInt32 id )
{
	auto iter = m_timers.find( id );
	if ( iter != m_timers.end() )
	{
		SimpleTimer& timer = iter->second;
		return timer.Reset();
	}
}

Tick ActionSectorController::GetTick( const UInt32 id )
{
	auto iter = m_timers.find( id );
	if ( iter != m_timers.end() )
	{
		SimpleTimer& timer = iter->second;
		return timer.Elapsed();
	}

	return 0;
}

UInt32 ActionSectorController::GetPlayerCount()
{
	return m_sector->GetCurPlayerCount();
}

UInt32 ActionSectorController::GetPlayerCountInVolume(UInt32 volumeIndex)
{
	VectorUnit vecUnit;
	getUnitsInVolume(volumeIndex, EntityTypes::PLAYER_ZONE, vecUnit);
	return static_cast<UInt32>(vecUnit.size());
}

UInt32 ActionSectorController::GetNpcCountInVolume(UInt32 volumeIndex)
{
	VectorUnit vecUnit;
	getUnitsInVolume(volumeIndex, EntityTypes::NPC, vecUnit);
	return static_cast<UInt32>(vecUnit.size());
}

UInt32 ActionSectorController::GetAlivePlayerCount()
{
	return m_sector->GetCurAlivePlayerCount();
}

UInt32 ActionSectorController::GetTeamMemberCount( const char* teamType )
{
	if ( teamType == nullptr )
	{
		return TeamType::TEAM_NONE;
	}

	size_t length = strlen( teamType );
	if ( length == 0 )
	{
		return TeamType::TEAM_NONE;
	}

	TeamType::Enum eTeamType = TeamType::TEAM_NONE;
	if ( !strcmp( teamType, "red") )
		eTeamType = TeamType::TEAM_A;
	else if ( !strcmp( teamType, "blue") )
		eTeamType = TeamType::TEAM_B;
	else
		MU2_ASSERT(false);

	return m_sector->GetTeamMemberCount( eTeamType );
}

void ActionSectorController::AddEvent( const UInt32 id )
{
	m_workFlags.insert( WorkFlags::value_type( id, true ) );
}

Bool ActionSectorController::HasEvent( const UInt32 id )
{
	if ( m_workFlags.find( id ) != m_workFlags.end() )
	{
		return true;
	}

	return false;
}

Bool ActionSectorController::ClearEvent( const UInt32 id )
{
	m_workFlags.erase( id );

	return true;
}

void ActionSectorController::ActiveEvent( const UInt32 id )
{
	auto iter = m_workFlags.find( id );
	if ( iter != m_workFlags.end() )
	{
		iter->second = true;
	}
}


void ActionSectorController::DeactiveEvent( const UInt32 id )
{
	auto iter = m_workFlags.find( id );
	if ( iter != m_workFlags.end() )
	{
		iter->second = false;
	}
}

Bool ActionSectorController::IsActiveEvent( const UInt32 id )
{
	auto iter = m_workFlags.find( id );
	if ( iter != m_workFlags.end() )
	{
		return iter->second;
	}

	return false;
}

void ActionSectorController::BlockEnter( const UInt32 id )
{
	AddEvent( id );
}

Bool ActionSectorController::CanEnter( const UInt32 id )
{
	return !HasEvent(id);
}

Bool ActionSectorController::SpawnGroup( const UInt32 id)
{
	Tick resolutionTick = 0;
	return SpawnGroupAndresolutionTick(id, resolutionTick);
}

Bool ActionSectorController::SpawnGroupAndresolutionTick(const IndexSpawnvolume id, Tick& resolutionTick)
{
	resolutionTick = 0;

	EntityControlVolume* spawner = m_sector->FindControlVolumeByCond(
		[&](const SectorEntityMap::value_type& v)->bool
	{
		if (v.second->HasAttribute(ATTRIBUTE_SPAWN_VOLUME) == false)
		{
			return false;
		}

		AttributeVolumeSpawn* attr = GetEntityAttribute(v.second);
		if (attr == nullptr)
		{
			return false;
		}
		if (attr->spawnVolumeIndex == id)
		{
			attr->disabled = false;
			return true;
		}


		return false;
	});

	if (spawner)
	{
		resolutionTick = spawner->GetDelayResolution();
		if (m_processor)
		{
			m_processor->ProcessEvent(DungeonEventType::NPC_SPAWNED, nullptr/*spawner*/, nullptr);
		}

		return true;
	}

	return false;
}

Bool ActionSectorController::SpawnGroupByLevel( const IndexSpawnvolume id, CharLevel level )
{
	EntityControlVolume* spawner = m_sector->FindControlVolumeByCond(
		[id, level](const SectorEntityMap::value_type& v)->bool
		{
			if ( v.second->HasAttribute( ATTRIBUTE_SPAWN_VOLUME ) == false )
				return false;

			AttributeVolumeSpawn* attr = GetEntityAttribute( v.second );
			if ( attr == nullptr )
				return false;

			if ( attr->spawnVolumeIndex == id )
			{
				attr->disabled = false;
				attr->exteriorSetLevel = level;
				return true;
			}

			return false;
		});

	return ( spawner ) ? true : false;
}

Bool ActionSectorController::SpawnGroupByMaxHpBase( const IndexSpawnvolume id, UInt32 hpBase )
{
	EntityControlVolume* spawner = m_sector->FindControlVolumeByCond(
		[id, hpBase](const SectorEntityMap::value_type& v)->bool
		{
			if ( v.second->HasAttribute( ATTRIBUTE_SPAWN_VOLUME ) == false )
				return false;

			AttributeVolumeSpawn* attr = GetEntityAttribute( v.second );
			if ( attr == nullptr )
				return false;

			if ( attr->spawnVolumeIndex == id )
			{
				attr->disabled = false;
				attr->exteriorSpawnHPBase = hpBase;
				return true;
			}

			return false;
		});

	return ( spawner ) ? true : false;
}

Bool ActionSectorController::SpawnGroupBySpawnHpRate( const IndexSpawnvolume id, UInt32 hpRate )
{
	EntityControlVolume* spawner = m_sector->FindControlVolumeByCond(
		[id, hpRate]( const SectorEntityMap::value_type& v )->bool
	{
		if ( v.second->HasAttribute( ATTRIBUTE_SPAWN_VOLUME ) == false )
			return false;

		AttributeVolumeSpawn* attr = GetEntityAttribute( v.second );
		if ( attr == nullptr )
			return false;

		if ( attr->spawnVolumeIndex == id )
		{
			attr->disabled = false;
			attr->exteriorSpawnHPRate = hpRate;
			return true;
		}

		return false;
	} );

	return ( spawner ) ? true : false;
}

Bool ActionSectorController::SpawnGroupBySetData( const IndexSpawnvolume id, IndexNpc npcIndex, IndexQuest questIndex, CharLevel level, UInt32 unitCount )
{
	EntityControlVolume* spawner = m_sector->FindControlVolumeByCond(
		[id, npcIndex, questIndex, level, unitCount](const SectorEntityMap::value_type& v)->bool
	{
		if ( v.second->HasAttribute( ATTRIBUTE_SPAWN_VOLUME ) == false )
			return false;

		AttributeVolumeSpawn* attr = GetEntityAttribute( v.second );
		if ( attr == nullptr )
			return false;

		if ( attr->spawnVolumeIndex == id )
		{
			attr->disabled = false;
			attr->npcs[0].npcId = npcIndex;
			attr->npcs[0].questIndex = questIndex;
			attr->npcs[0].unitCount = unitCount;
			attr->exteriorSetLevel = level;

			return true;
		}

		return false;
	});

	return ( spawner ) ? true : false;
}

Bool ActionSectorController::SpawnLayerWithLevel( IndexLayer layer, Bool disabled, CharLevel level )
{
	m_sector->ForEachControlVolumeByCond(
		[layer, disabled, level](const SectorEntityMap::value_type& v)->void
	{
		if ( v.second->HasAttribute( ATTRIBUTE_SPAWN_VOLUME ) )
		{
			AttributeVolumeSpawn* attr = GetEntityAttribute( v.second );
			if ( attr != nullptr )
			{
				if ( attr->layer == layer )
				{
					attr->disabled = disabled;					
				}
				
				attr->exteriorSetLevel = level;								
			}			
		}
		else if ( v.second->HasAttribute( ATTRIBUTE_IR_SPAWN ) )
		{
			AttributeIRSpawn* attr = GetEntityAttribute( v.second );
			if ( attr != nullptr )
			{
				if ( attr->layer == layer )
				{
					attr->disabled = disabled;							
				}			
			}
		}		
	});

	return true;
}
void ActionSectorController::SpawnVolumeChangeLevel(IndexLayer layer, CharLevel level)
{
	m_sector->ForEachControlVolumeByCond(
		[layer, level](const SectorEntityMap::value_type& v)->void
	{
		if (v.second->HasAttribute(ATTRIBUTE_SPAWN_VOLUME))
		{
			AttributeVolumeSpawn* attr = GetEntityAttribute(v.second);
			if (attr != nullptr)
			{
				if (attr->layer == layer)
				{
					attr->exteriorSetLevel = level;
				}
			}
		}
		
	});

}

Bool ActionSectorController::DespawnGroup( const IndexSpawnvolume id )
{
	EntityControlVolume* spawner = m_sector->FindControlVolumeByCond(
		[id](const SectorEntityMap::value_type& v)->bool
	{
		if ( v.second->HasAttribute( ATTRIBUTE_SPAWN_VOLUME ) == false )
			return false;

		AttributeVolumeSpawn* attr = GetEntityAttribute( v.second );
		if ( attr == nullptr )
			return false;

		if ( attr->spawnVolumeIndex == id )
		{
			attr->disabled = true;
			attr->exteriorSetLevel = 0;			
			return true;
		}

		return false;
	});

	return ( spawner ) ? true : false;
}

Bool ActionSectorController::SetSpawnVolumeMonsterLevel( const IndexSpawnvolume id, CharLevel level )
{
	EntityControlVolume* spawner = m_sector->FindControlVolumeByCond(
		[id, level](const SectorEntityMap::value_type& v)->bool
	{
		if ( v.second->HasAttribute( ATTRIBUTE_SPAWN_VOLUME ) == false )
			return false;

		AttributeVolumeSpawn* attr = GetEntityAttribute( v.second );
		if ( attr == nullptr )
			return false;

		if ( attr->spawnVolumeIndex == id )
		{			
			attr->exteriorSetLevel = level;
			return true;
		}

		return false;
	});

	return ( spawner ) ? true : false;
}

Bool ActionSectorController::ControlIRTouchVolume( const UInt32 id, UInt32 enable )
{
	EntityControlVolume* spawner = m_sector->FindControlVolumeByCond(
		[id, enable](const SectorEntityMap::value_type& v)->bool
	{
		if ( v.second->HasAttribute( ATTRIBUTE_IR_SPAWN ) == false )
			return false;

		AttributeIRSpawn* attr = GetEntityAttribute( v.second );
		if ( attr == nullptr )
			return false;

		if ( attr->touchVolumeIndex == id )
		{
			attr->disabled = enable;
			
			return true;
		}

		return false;
	});

	return ( spawner ) ? true : false;
}

void ActionSectorController::PlayScene( const char* id )
{
	if ( id == nullptr || strlen(id) == 0 )
	{
		MU2_ASSERT( false );
		return;
	}

	std::wstring msg;
	StringUtil::Convert(id, msg);
	ENtfGamePlayScene* evt = NEW ENtfGamePlayScene;
	evt->id = msg;	
	evt->stop = false;
#ifdef __Reincarnation__jason_180628__
	m_sector->Broadcast(EventPtr(evt));
#else
	SendInSector( m_sector, EventPtr(evt) );
#endif
}

void ActionSectorController::PlaySceneVolume( const char* id, UInt32 eventVolumeId )
{
	if ( id == nullptr || strlen( id ) == 0 )
	{
		MU2_ASSERT( false );
		return;
	}

	std::wstring msg;
	StringUtil::Convert( id, msg );

	ENtfGamePlayScene* ntf = NEW ENtfGamePlayScene;
	EventPtr ntfPtr(ntf);
	
	ntf->id = msg;
	ntf->stop = false;	

	VolumeScript* vscript = GetZoneScript<VolumeScript>(m_sector->GetIndexZone());
	if ( vscript == nullptr )
	{
		return;
	}
	const VolumeElem* elem = vscript->Get( eventVolumeId );
	if ( elem == nullptr )
	{
		return;
	}

	VectorPlayer vecPlayer;
	m_sector->GetPlayers(vecPlayer );

	for ( auto i = vecPlayer.begin(); i != vecPlayer.end(); ++i )
	{
		EntityPlayer* entity = ( *i );
		if ( entity == nullptr )
			continue;

		ViewPosition* viewPos = GetEntityView( entity );
		Vector3 pos;
		viewPos->GetRealPosition( pos );

		Float diff = Distance2D( pos, elem->pos );
		if ( diff < elem->radius )
		{
			SERVER.SendToClient( entity, ntfPtr );
		}
	}	
}

void ActionSectorController::StopPlayScene( const char* id )
{
	if ( id == nullptr || strlen(id) == 0 )
	{
		return;
	}

	std::wstring msg;
	StringUtil::Convert(id, msg);
	ENtfGamePlayScene* evt = NEW ENtfGamePlayScene;
	evt->id = msg;	
	evt->stop = true;
#ifdef __Reincarnation__jason_180628__
	m_sector->Broadcast(EventPtr(evt));
#else
	SendInSector( m_sector, EventPtr(evt) );
#endif
}

void ActionSectorController::PlaySceneDuringHolding( const char* id, UInt32 time_second )
{
	if ( id == nullptr || strlen(id) == 0 )
	{
		MU2_ASSERT( false );
		return;
	}	

	m_sector->SetHold( time_second );

	std::wstring msg;
	StringUtil::Convert(id, msg);
	ENtfGamePlayScene* evt = NEW ENtfGamePlayScene;
	evt->id = msg;	
#ifdef __Reincarnation__jason_180628__
	m_sector->Broadcast(EventPtr(evt));
#else
	SendInSector( m_sector, EventPtr(evt) );
#endif
}


void ActionSectorController::SendSystemMessage( const char* msg )
{
	VERIFY_RETURN(msg && 0 < strlen(msg), );

#ifdef __Reincarnation__jason_180628__	
	SystemMessage sysMsg(L"sys", msg);
	m_sector->Broadcast(sysMsg);
#else
	std::string str(msg);
	if (str.empty())
		return;

	std::wstring message(str.begin(), str.end());
	SystemMessage sysMsg(L"sys", message);
	ENtfGameSystemMessage* evt = NEW ENtfGameSystemMessage;
	evt->msg = sysMsg.GetString();
	SendInSector( m_sector, EventPtr(evt) );
#endif
}


void ActionSectorController::SendEventString( UInt32 index )
{
	ENtfEventString* evt = NEW ENtfEventString;
	evt->index = index;
#ifdef __Reincarnation__jason_180628__
	m_sector->Broadcast(EventPtr(evt));
#else
	SendInSector( m_sector, EventPtr( evt ) );
#endif
	// 튜토리얼 마지막에 대사 안나오는 이벤트때문에 재발시 로그 분석용 [10/24/2016 eunseok]
	if (index == 10104)
	{
		MU2_DEBUG_LOG(LogCategory::DEBUG_SECTOR_LUA_SCRIPT, "event execute[%d]", index);
	}
}

void ActionSectorController::SendEventStringInVolume(UInt32 index, UInt32 volumeIndex)
{
	VectorUnit vecUnit;
	getUnitsInVolume(volumeIndex, EntityTypes::PLAYER_ZONE, vecUnit);

	auto evt = NEW ENtfEventString;
	evt->index = index;
	auto evenPtr = EventPtr(evt);
	for (auto entity : vecUnit)
	{
		SERVER.SendToClient(entity, evenPtr);
	}
}


void SendInVolume(Sector* sector, UInt32 eventVolumeIndex, EventPtr evt)
{
	if (sector == nullptr)
		return;

	VectorUnit vecUnit;

	VolumeScript* vscript = GetZoneScript<VolumeScript>(sector->GetIndexZone());
	VALID_RETURN(vscript, );

	const VolumeElem* elem = vscript->Get(eventVolumeIndex);
	VALID_RETURN(elem, );

	VectorUnit entitiesFromType;
	sector->GetUnits(EntityTypes::PLAYER_ZONE, entitiesFromType);

	for (auto i = entitiesFromType.begin(); i != entitiesFromType.end(); ++i)
	{
		EntityUnit* pUnit = (*i);
		VALID_DO(pUnit, continue);

		Vector3 pos;
		pUnit->GetPositionView().GetRealPosition(pos);

		Float diff = Distance2D(pos, elem->pos);
		if (diff < elem->radius)
		{
			vecUnit.push_back(pUnit);
		}
	}

	for (auto itPos : vecUnit) {

		auto player = static_cast<EntityPlayer*>(itPos);
		if (player == nullptr)
			continue;

		SERVER.SendToClient(player, evt);
	}
}

#ifdef __Reincarnation__jason_180628__
#else
void SendInSector(Sector* sector, EventPtr evt)
{
	if ( sector == nullptr )
		return;

	VectorPlayer vecPlayer;
	sector->GetPlayers(vecPlayer );

	for ( auto iter = vecPlayer.begin(); iter != vecPlayer.end(); ++iter )
	{
		EntityPlayer* entity = (*iter);
		if ( entity == nullptr )
			continue;

		SERVER.SendToClient( entity, evt );
	}
}
#endif

Bool ActionSectorController::CheckNpcSpawn()
{
	UInt32 crowdCount = m_sector->GetCurPlayerCount();
	crowdCount += m_sector->GetCurMonsterCount();

	if ( m_maxCrowdNumber > 0 )
	{
		if ( crowdCount > m_maxCrowdNumber )
			return false;
	}

	return true;
}

void ActionSectorController::SetMaxCrowdNumber( const UInt32 num )
{
	m_maxCrowdNumber = num;
}

void ActionSectorController::KickAllPlayers()
{
	VectorPlayer vecPlayer;
	m_sector->GetPlayers(vecPlayer );

	for ( auto i = vecPlayer.begin(); i != vecPlayer.end(); ++i )
	{
		EntityPlayer* entity = (*i);
		if ( entity == nullptr )
			continue;

		ViewPosition* posView = GetEntityView( entity );
		if ( posView == nullptr )
			continue;

		ActionPlayerPortal* actPortal = GetEntityAction( entity );
		if ( actPortal == nullptr )
			continue;

	//	actPortal->WarpBeginPortal();
		actPortal->WarpBeginPosition();
	}
}

void ActionSectorController::ActiveTagVolume( const UInt32 id )
{
	theSpawnSystem.SpawnDynamicTagVolumeByWorldTagVolumeScript(m_sector, id);

	return;
}

void ActionSectorController::DeactiveTagVolume( const UInt32 id )
{	
	theSpawnSystem.DespawnDynamicTagVolume(m_sector, id);

	return;
}

void ActionSectorController::SetControlVolumeNotProcessOnTick( Bool setValue )
{
	m_sector->ForEachControlVolumeByCond(
		[setValue]( const SectorEntityMap::value_type& v )->void
	{
		if (v.second->HasAttribute( ATTRIBUTE_SPAWN_VOLUME ))
		{
			AttributeVolumeSpawn* attr = GetEntityAttribute( v.second );
			if (attr != nullptr)
			{
				attr->isNotProcessOnTick = setValue;
			}
		}
		else if (v.second->HasAttribute( ATTRIBUTE_IR_SPAWN ))
		{
			AttributeIRSpawn* attr = GetEntityAttribute( v.second );
			if (attr != nullptr)
			{
				attr->isNotProcessOnTick = setValue;
			}
		}
	} );
}

UInt32 ActionSectorController::GetRandomSeed( const UInt32 seedLow, const UInt32 seedHigh)
{
	return GetRandBetween( seedLow, seedHigh );
}

UInt32 ActionSectorController::GetPercentHp( const EntityId unitId )
{
	EntityUnit* entity = m_sector->FindUnit(unitId);
	if ( entity == nullptr)
		return 0;
	
	ActionAbility* actAbility = GetEntityAction( entity );

	UInt32 currentHp = MAX( 0, actAbility->GetCurrentHp() );
	UInt32 maxHp = MAX( 1, actAbility->GetMaxHp() );

	return currentHp * 100 / maxHp;
}

UInt32 ActionSectorController::GetPercentHpAvg()
{
	VectorPlayer vecPlayer;
	m_sector->GetPlayers(vecPlayer );

	UInt32 hp = 0;
	UInt32 hpSum = 0;
	UInt32 memberCnt = 0;

	for ( auto i = vecPlayer.begin(); i != vecPlayer.end(); ++i )
	{
		EntityPlayer* entity = (*i);
		if ( entity == nullptr )
			continue;

		hp = GetPercentHp(entity->GetId());
		hpSum += hp;
		memberCnt++;
	}

	if(memberCnt > 1)
	{
		return ( hpSum / memberCnt );
	}
	else
		return hpSum;
}

UInt32 ActionSectorController::GetPlayerLevelAvg()
{
	VectorPlayer vecPlayer;
	m_sector->GetPlayers(vecPlayer );
		
	UInt32 levelSum = 0;
	UInt32 memberCnt = 0;

	for ( auto i = vecPlayer.begin(); i != vecPlayer.end(); ++i )
	{
		EntityPlayer* player = (*i);
		if ( player == nullptr )
		{
			continue;
		}

		levelSum += player->GetLevel();
		memberCnt++;
	}



	if (memberCnt == 0)
		return 0;

	return ( levelSum / memberCnt );
}

UInt32 ActionSectorController::GetPartyLevelAvg()
{
	VectorPlayer vecPlayer;
	m_sector->GetPlayers(vecPlayer );

	UInt32 level = 0;
	UInt32 levelSum = 0;
	UInt32 memberCnt = 0;

	for ( auto i = vecPlayer.begin(); i != vecPlayer.end(); ++i )
	{
		EntityPlayer* player = (*i);
		if ( player == nullptr )
			continue;
					
		ActionPlayerParty* actParty = GetEntityAction( player );
		if ( actParty == nullptr )
			continue;

		if ( !actParty->IsParty() )
		{
			return player->GetLevel();
		}
		else
		{
			VecPartyMember members;
			actParty->GetMembers( members );
			for ( UInt32 n = 0; n < members.size(); ++n )
			{
				level = members[n].level;
				levelSum += level;
				memberCnt++;
			}

			if(memberCnt > 1)
			{
				return ( levelSum / memberCnt );
			}
			else
				return levelSum;

		}
	}

	return 0;
}

UInt32 ActionSectorController::GetMonsterHpRate(IndexNpc npcIndex )
{
	UInt64 currentHpRate = 100'000;
	EntityNpc* npc = m_sector->FindNpcByCond([npcIndex]( const SectorEntityMap::value_type& v ) -> bool
	{
		AttributeNpc* attrNpc = GetEntityAttribute( v.second );										
		if ( attrNpc->GetNpcIndex() == npcIndex )
		{
			return true;
		}
		return false;
	}
	);

	if ( npc != nullptr )
	{
		ActionAbility* actAbility = GetEntityAction( npc );		

		UInt64 maxHP = actAbility->GetMaxHp();
		UInt64 curHP = actAbility->GetCurrentHp();
		if( maxHP < 1 )
		{
			maxHP = 1;
		}

		currentHpRate = curHP * 1000 / maxHP ;
	}

	return static_cast<UInt32>(currentHpRate);
}

Bool ActionSectorController::KillMontser( IndexNpc npcIndex, UInt32 count)
{
	VectorNpc vecNpc;
	m_sector->GetNpcs(vecNpc);
	
	if( count < 1 )
		return false;

	for ( EntityNpc* npc : vecNpc)
	{		
		if(npc->GetNpcIndex() == npcIndex && count > 0)
		{	
			npc->GetSkillControlAction().RemoveAllEffectVolume();
			npc->GetDamageAction().ClearDamaged();			

			npc->GetHsm().Tran(EntityState::STATE_NPC_DEAD );
			count--;
		}

		if( count == 0)
			return true;

	}
	return false;
}

void ActionSectorController::KillMontserByIndex(IndexNpc npcIndex )
{
	VectorNpc vecNpc;
	m_sector->GetNpcs(vecNpc );

	for ( EntityNpc* npc : vecNpc )
	{
		if (npc->GetNpcIndex() == npcIndex )
		{
			npc->GetSkillControlAction().RemoveAllEffectVolume();
			npc->GetHsm().Tran(EntityState::STATE_NPC_DEAD );
		}
	}
}

Bool ActionSectorController::KillAllMonster(EntityUnit* killerUnit, const std::set<IndexNpc>& exceptMonsterIndex )
{
	VectorNpc vecNpc;
	m_sector->GetNpcs(vecNpc );

	for( EntityNpc* npc : vecNpc )
	{
		if( false == npc->IsMonsterType() )
		{
			continue;
		}

		if( !npc->IsDead() )
		{
			if (exceptMonsterIndex.end() != exceptMonsterIndex.find( npc->GetNpcIndex() ))
			{
				continue;
			}

			// 킬러 정보가 존재하면 킬러 정보를 세팅한다.
			if( nullptr != killerUnit )
			{
				ActionNpcDamage& damageAction = npc->GetDamageAction();

				// 경험치 및 아이템 드랍을 위한 정보 추가
				damageAction.AddDamaged( killerUnit, 1 );
				damageAction.PreprocessDamaged();
			}
			
			npc->GetSkillControlAction().RemoveAllEffectVolume();
			npc->GetHsm().Tran(EntityState::STATE_NPC_DEAD );
		}
	}

	return true;
}

Bool ActionSectorController::KillSpawnVolumeMontser(IndexSpawnvolume spawnvolumeIndex, UInt32 deadAniType )
{
	VectorNpc vecNpc;
	m_sector->GetNpcs(vecNpc );

	for ( EntityNpc* npc : vecNpc )
	{
		if( 0 == spawnvolumeIndex )
		{	
			npc->GetSkillControlAction().RemoveAllEffectVolume();
			npc->GetHsm().Tran(EntityState::STATE_NPC_DEAD );
		}
		else
		{	
			if( npc->GetVolumeSpawnIndex() == spawnvolumeIndex )
			{	
				npc->GetSkillControlAction().RemoveAllEffectVolume();
				npc->GetHsm().Tran(EntityState::STATE_NPC_DEAD );
			}
		}		
	}

	return true;
}

Bool ActionSectorController::SummonMonster( UInt32 volumeIndex, IndexNpc npcIndex, UInt32 count, UInt32 delayTime /*= 0*/, Vector3 pos /*= Vector3() */, Float radius /* 0 */)
{	
	VolumeSpawnScript* vscript = GetZoneScript<VolumeSpawnScript>(m_sector->GetIndexZone());
	if ( vscript == nullptr )
	{
		return false; 
	}

	const VolumeSpawnElem* elem = vscript->Get( volumeIndex );
	if ( elem == nullptr )
	{
		return false;
	}

	
	const NpcInfoElem* script = SCRIPTS.GetNpcInfoScript( npcIndex );
	if ( !script )
	{
		return false;
	}

	UInt32 spawnCount = 0;
	if ( pos.IsZero() )
	{
		pos = Vector3( elem->x, elem->y, elem->z );
	}

	if ( radius == 0 )
	{
		radius = elem->radius;
	}

	for (UInt32 n = 0; n < count; n++)
	{			
		Vector3 spawnPos = pos;
		if( false == theSpawnSystem.GetRandomPostion( spawnPos, 0, radius, m_sector) )
		{
			continue;
		}

		EntityNpc* npc = NULL;

		SpawnSystem::BaseSpawnInfo baseSpawnInfo;
		baseSpawnInfo.npcIndex = npcIndex;
		baseSpawnInfo.sector = m_sector;
		baseSpawnInfo.spawnPos = spawnPos;
		baseSpawnInfo.dir = elem->dir;
		baseSpawnInfo.aiIndex = script->aiIndex;
		baseSpawnInfo.pursuitRange = elem->npcRegen[0].pursuitRange;		
		baseSpawnInfo.summonDelayTime = delayTime;

		if( 0 < elem->npcRegen[0].level )
		{
			baseSpawnInfo.level = elem->npcRegen[0].level;
		}

		if ( false == theSpawnSystem.SpawnGeneralNpc( &baseSpawnInfo, &npc ) )
		{			
			continue;
		}
		
		spawnCount++;
	}

	if( spawnCount == count)
		return true;
	else
		return false;
}

Bool ActionSectorController::SummonMonsterAtDeadLocation(IndexNpc deadNpcIndex, IndexNpc npcIndex, UInt32 count, UInt32 delayTime /*= 0*/, Float radius /*= 0 */ )
{
	EntityNpc* foundNpc = m_sector->FindNpcByCond( [deadNpcIndex]( const SectorEntityMap::value_type& v ) -> bool
	{
		AttributeNpc* attrNpc = GetEntityAttribute( v.second );
		if ( attrNpc->GetNpcIndex() == deadNpcIndex )
		{
			return true;
		}
		return false;
	}
	);

	if ( nullptr == foundNpc )
	{
		return false;
	}

	/*ViewNpc* npcView = GetEntityView( npc );
	if ( npcView == nullptr )
	{
		return false;
	}*/

	//ViewPosition* viewPos = GetEntityView( foundNpc );
	Vector3 deadPos = foundNpc->GetRealPosition();

	
	const NpcInfoElem* script = SCRIPTS.GetNpcInfoScript( npcIndex );
	VERIFY_RETURN(script, false);
	
	UInt32 spawnCount = 0;
	for ( UInt32 n = 0; n < count; n++ )
	{		
		EntityNpc* spawnedNpc = nullptr;
		Vector3 spawnPos = deadPos;

		if ( radius != 0 )
		{
			if ( !theSpawnSystem.GetRandomPostion( spawnPos, 0, radius, m_sector ) )
			{
				MU2_WARN_LOG( LogCategory::CONTENTS, "%s> spwanPosition invalid [npcIndex : %d], [ZoneId : %d],[Radius : %.2f], [spawnPosition : %.2f, %.2f, %.2f]",
					__FUNCTION__, npcIndex, m_sector->GetId().GetZoneIndex(), radius, spawnPos.x, spawnPos.y, spawnPos.z );
				continue;
			}
		}
		
		SpawnSystem::BaseSpawnInfo baseSpawnInfo;
		baseSpawnInfo.npcIndex = npcIndex;
		baseSpawnInfo.sector = m_sector;
		baseSpawnInfo.spawnPos = spawnPos;
		baseSpawnInfo.dir = foundNpc->GetLookAtDir();
		baseSpawnInfo.aiIndex = script->aiIndex;
		baseSpawnInfo.pursuitRange = script->sightRange;
		baseSpawnInfo.level = foundNpc->GetLevel();
		baseSpawnInfo.summonDelayTime = delayTime;

		if ( false == theSpawnSystem.SpawnGeneralNpc( &baseSpawnInfo, &spawnedNpc ) )
		{
			MU2_ASSERT( false );
			continue;
		}

		spawnCount++;
	}

	if ( spawnCount == count )
		return true;
	else
		return false;

}

Bool ActionSectorController::SummonMonsterVolume( UInt32 volumeIndex, IndexNpc npcIndex, UInt32 count )
{	

	VolumeSpawnScript* vscript = GetZoneScript<VolumeSpawnScript>(m_sector->GetIndexZone());
	if ( vscript == nullptr )
	{
		return false;
	}

	const VolumeSpawnElem* elem = vscript->Get( volumeIndex );
	if ( elem == nullptr )
	{
		return false;
	}

	
	const NpcInfoElem* script = SCRIPTS.GetNpcInfoScript( npcIndex );
	if ( !script )
	{
		return false;
	}

	UInt32 spawnCount = 0;
	Vector3 pos( elem->x, elem->y, elem->z );
	for ( UInt32 n = 0; n < count; n++ )
	{
		if ( false == theSpawnSystem.GetRandomPostion( pos, 0, elem->radius, m_sector ) )
		{
			continue;
		}

		EntityNpc* npc = NULL;

		SpawnSystem::BaseSpawnInfo baseSpawnInfo;
		baseSpawnInfo.npcIndex = npcIndex;
		baseSpawnInfo.sector = m_sector;
		baseSpawnInfo.spawnPos = pos;
		baseSpawnInfo.dir = elem->dir;
		baseSpawnInfo.aiIndex = script->aiIndex;
		baseSpawnInfo.pursuitRange = elem->npcRegen[0].pursuitRange;
		baseSpawnInfo.volumeSpawnIndex = volumeIndex;

		if ( 0 < elem->npcRegen[0].level )
		{
			baseSpawnInfo.level = elem->npcRegen[0].level;
		}

		if ( false == theSpawnSystem.SpawnGeneralNpc( &baseSpawnInfo, &npc ) )
		{
			continue;
		}
		spawnCount++;
	}

	if ( spawnCount == count )
		return true;
	else
		return false;
}

Bool ActionSectorController::SummonMonsterInLua(UInt32 volumeIndex, IndexNpc npcIndex, UInt32 count, UInt32 delayTime /*= 0*/)
{
	return SummonMonster(volumeIndex, npcIndex, count, delayTime);
}

Bool ActionSectorController::HasBeenActiveOperateNpc(IndexNpc npcIndex, IndexSpawnvolume spawnVolumeIndex )
{	
	EntityNpc* npc = m_sector->FindNpcByCond( [npcIndex,spawnVolumeIndex]( const SectorEntityMap::value_type& v ) -> bool
	{
		ActionNpcOperate* actObject = GetEntityAction( v.second );										
		VALID_RETURN(actObject, false);
		VALID_RETURN((IndexNpc)actObject->GetObjectId() == npcIndex, false);

		if (spawnVolumeIndex != 0)
		{
			AttributeNpc* attributeNpc = GetEntityAttribute(v.second);
			VALID_RETURN(attributeNpc, false);
			VALID_RETURN(attributeNpc->GetVolumeSpawnIndex() == spawnVolumeIndex, false);
		}

		return true;
		
	}
	);

	if ( npc != nullptr )
	{
		ActionNpcOperate* actObject = GetEntityAction( npc );
		if( actObject != nullptr && actObject->GetCompleteCount() > 0 )
		{
			return true;
		}		
	}

	return false;
}

UInt32 ActionSectorController::GetMonsterCount()
{
	return m_sector->GetCurNpcCount();
}

UInt32 ActionSectorController::GetAliveMonsterCount()
{
	return m_sector->GetAliveNpcCount();
}

UInt32 ActionSectorController::GetAliveTheMonsterCount(IndexNpc npcIndex)
{
	return m_sector->GetAliveNpcCount(npcIndex);
}

UInt32 ActionSectorController::GetTheMonsterCount(IndexNpc npcIndex )
{
	return m_sector->GetCurNpcCount( npcIndex );
}

UInt32 ActionSectorController::GetSpawnVolumeMonsterCount( Int32 spawnVolumeIndex )
{
	return m_sector->GetCurSpawnVolumeNpcCount( spawnVolumeIndex );
}

Bool ActionSectorController::CheckPlayerInVolume( UINT32 volumeIndex )
{
	VolumeScript* vscript = GetZoneScript<VolumeScript>(m_sector->GetIndexZone());
	VALID_RETURN(vscript, false);

	const VolumeElem* elem = vscript->Get( volumeIndex );
	VALID_RETURN(elem, false);

	VectorPlayer vecPlayer;
	m_sector->GetPlayers(vecPlayer );
	for ( auto i = vecPlayer.begin(); i != vecPlayer.end(); ++i )
	{
		EntityPlayer* player = (*i);
		VALID_DO(player, continue);

		Float diff = Distance2D(player->GetRealPosition(), elem->pos );
		if ( diff < elem->radius )
		{
			return true;
		}
	}
	
	return false;
}

Bool ActionSectorController::CheckMosterInVolume( UInt32 volumeIndex, IndexNpc npcIndex )
{
	VolumeScript* vscript = GetZoneScript<VolumeScript>(m_sector->GetIndexZone());
	VALID_RETURN(vscript, false);

	const VolumeElem* elem = vscript->Get( volumeIndex );
	VALID_RETURN(elem, false);

	VectorNpc vecNpc;
	m_sector->GetNpcs(vecNpc );

	for ( EntityNpc* npc :  vecNpc)
	{
		if(npc->GetNpcIndex() == npcIndex )
		{
			Float diff = Distance2D( npc->GetRealPosition(), elem->pos );
			if ( diff < elem->radius )
			{
				return true;
			}
		}
	}

	return false;
}
EntityId ActionSectorController::GetPlayerTouchVolume( UInt32 volumeIndex )
{
	

	VolumeScript* vscript = GetZoneScript<VolumeScript>(m_sector->GetIndexZone());
	if ( vscript == nullptr )
	{
		return 0; 
	}
	const VolumeElem* elem = vscript->Get( volumeIndex );
	if ( elem == nullptr )
	{
		return 0;
	}

	VectorPlayer vecPlayer;
	m_sector->GetPlayers(vecPlayer );

	for (EntityPlayer* player : vecPlayer)
	{
		Float diff = Distance2D( player->GetRealPosition(), elem->pos );
		if ( diff < elem->radius )
		{
			return player->GetId();
		}
	}

	return 0;
}

void ActionSectorController::SetMonsterTeam(IndexNpc npcIndex, TeamType::Enum team )
{
	VectorNpc vecNpc;
	m_sector->GetNpcs(vecNpc );
	
	for (EntityNpc* npc : vecNpc)
	{	 
		VALID_DO(npc && npc->IsValid(), continue);

		if( npc->GetNpcIndex() == npcIndex )
		{
			npc->SetMyTeam(team);
		}
	}
}

UInt32 ActionSectorController::GetDungeonType()
{
	return m_sector->GetDungeonType();
}

ErrorJoin::Error ActionSectorController::InstallProcessor()
{
	VERIFY_RETURN(m_sector, ErrorJoin::InvalidSector);

	EntitySectorController* pController = GetOwnerController();
	VERIFY_RETURN(pController && pController->IsValid(), ErrorJoin::E_FAILED);

	if ( m_processor != nullptr )
	{
		delete m_processor;
		m_processor = nullptr;
	}

	InstanceSectorType::Enum type = m_sector->GetInstanceType();

	if ( type == InstanceSectorType::CHAOS_CASTLE )
	{
		m_processor = NEW ChaosCastleProcessor(pController);
	}
	else if ( type == InstanceSectorType::ALTAR_OF_ELEMENTS )
	{
		m_processor = NEW AltarOfElementsProcessor(pController);
	}
	//else if (type == InstanceSectorType::DEATH_MATCH_20 || type == InstanceSectorType::DEATH_MATCH_40)
	//{
	//	m_processor = NEW DeathMatchProcessor(pController);
	//}
	else if ( type == InstanceSectorType::BOSS )
	{
		m_processor = NEW BossProcessor(pController);
	}
	else if ( type == InstanceSectorType::EXPLORE )
	{
		m_processor = NEW ExplorerProcessor(pController);
	}
	//else if (type == InstanceSectorType::GUILDHALL)
	//{
	//	m_processor = NEW GuildhallProcessor(pController);
	//}
	else if ( type == InstanceSectorType::ENDLESS_TOWER )
	{
		m_processor = NEW EndlessTowerProcessor(pController);
		//m_processor = NEW TowerOfDawnProcessor(pController);
	}
	else if ( type == InstanceSectorType::BLOOD_CASTLE )
	{
		m_processor = NEW BloodCastleProcessor(pController);
	}
	else if( type == InstanceSectorType::COLOSSEUM )
	{
		m_processor = NEW ColosseumProcessor(pController);
	}
	else if (type == InstanceSectorType::MAZE)
	{
		m_processor = NEW MazeProcessor(pController);
	}
	else if (type == InstanceSectorType::TOURNAMENT_ALTAR_OF_ELEMENTS)
	{
		m_processor = NEW TournamentAltarOfElementsProcessor(pController);
	}
	else if (type == InstanceSectorType::BATTLEFIELD_IN_THE_SKY)
	{
		m_processor = NEW BattleFieldInTheSkyProcessor(pController);
	}
	else if (type == InstanceSectorType::PVP_COLOSSEUM_33)
	{
		m_processor = NEW ColosseumPVP33Processor(pController);
	}
	else if (type == InstanceSectorType::DOMINION)
	{
		m_processor = NEW DominionProcessor(pController);
	}
	else if ( type == InstanceSectorType::Nether_World )
	{
		m_processor = NEW NetherWorldProcessor(pController);
	}
	else if (type == InstanceSectorType::AIRSHIP_DEFENCE)
	{
		m_processor = NEW AirshipDefenceProcessor(pController);
	}
	else if (type == InstanceSectorType::OHRDOR_SEWERAGE)
	{
		m_processor = NEW OhrdorSewerageProcessor(pController);
	}
	else if (type == InstanceSectorType::MAZE_IN_DARKNESS)
	{
		m_processor = NEW MazeInDarknessProcessor(pController);
	}
	else if (type == InstanceSectorType::PVP_CHAOSCASTLE)
	{
		m_processor = NEW PVPChaosCastleProcessor(pController);
	}
	else if (type == InstanceSectorType::PVP_FIELD)
	{
		m_processor = NEW PVPFieldProcessor(pController);
	}
	else if (type == InstanceSectorType::KNIGHTAGE_FIELD_RAID)
	{
		m_processor = new FieldRaidProcessor(pController);
	}

#ifdef __Patch_Tower_of_dawn_eunseok_2018_11_05
	else if (type == InstanceSectorType::TOWER_OF_DAWN)
	{
		m_processor = NEW TowerOfDawnProcessor(pController);
	}
#endif //__Patch_Tower_of_dawn_eunseok_2018_11_05

#ifdef __Patch_WebOfGod_by_jason_20181213
	else if (type == InstanceSectorType::WEB_OF_GOD)
	{
		m_processor = NEW WebOfGodProcessor(pController);
	}
#endif
	else
	{
		m_processor = NEW SectorProcessor(pController, SectorProcessorType::SECTOR);
	}


	ErrorJoin::Error eError = m_processor->SetupDungeonInfo();

	if ( eError != ErrorJoin::SUCCESS)
	{
		if (m_processor != nullptr)
		{
			delete m_processor;
			m_processor = nullptr;
		}

		return eError;
	}

	return ErrorJoin::SUCCESS;
}

void ActionSectorController::NoticeNpcSpawn(IndexNpc npcId )
{
	ENtfGameMMNpcInfo* ntf = NEW ENtfGameMMNpcInfo;
	ntf->indexNpc = npcId;
	ntf->state = MM_NpcState::NOTICE_SPAWN;
	ntf->teamType = TeamType::TEAM_NONE;
#ifdef __Reincarnation__jason_180628__
	m_sector->Broadcast(EventPtr(ntf));
#else
	SendInSector( m_sector, EventPtr( ntf ) );
#endif
}

Bool ActionSectorController::JumpTo(IndexZone indexZone, Float x, Float y, Float z, IndexPosition positionIndex )
{
	VALID_RETURN(indexZone, false );
	
	Vector3 dstPos( x, y, z );
	VERIFY_RETURN( false == dstPos.IsZero(), false );

	VectorPlayer vecPlayer;
	m_sector->GetPlayers(vecPlayer );
	
	for ( size_t i = 0; i < vecPlayer.size(); ++i )
	{
		EntityPlayer* player = vecPlayer[ i ];
		VALID_DO(player && player->IsValid(), continue);
		
		EzwReqGameJumpTo* req = NEW EzwReqGameJumpTo;
		req->toLocation.execId = ExecId(0, indexZone);
		req->toLocation.pos = dstPos;
		req->toLocation.indexPosition = positionIndex;
		req->toLocation.pos.x = req->toLocation.pos.x + GetRandBetweenF(-100.f, 100.f);
		req->toLocation.pos.y = req->toLocation.pos.y + GetRandBetweenF(-100.f, 100.f);
		player->SendToClient(EventPtr(req) );
	}

	return true;
}

Bool ActionSectorController::ChangePos( Float x, Float y, Float z, Float randomRange, Float degree )
{
	Vector3 dstPos( x, y, z );
	VERIFY_RETURN(false == dstPos.IsZero(), false);

	VectorPlayer vecPlayer;
	m_sector->GetPlayers(vecPlayer );
	
	for ( size_t i = 0; i < vecPlayer.size(); ++i )
	{
		EntityPlayer* player = vecPlayer[i];
		VALID_DO(player && player->IsValid(), continue);

		ENtfGameForceChangePos* ntf = NEW ENtfGameForceChangePos;
		EventPtr eNtfPtr(ntf);

		ntf->stopPosition = dstPos;
		ntf->entityId = player->GetId();

		ntf->stopPosition.x = ntf->stopPosition.x + GetRandBetweenF( randomRange * -1, randomRange );
		ntf->stopPosition.y = ntf->stopPosition.y + GetRandBetweenF( randomRange * -1, randomRange );
		ntf->dir = degree;
		auto actSector = player->GetAction< ActionSector >();
		if ( !actSector->GetValidPosition( ntf->stopPosition, ntf->stopPosition ) )
		{
			theGameMsg.SendGameDebugMsg( player, L"Fail to changepos :%f %f %f", ntf->stopPosition.x, ntf->stopPosition.y, ntf->stopPosition.z );
			return false;
		}

		if ( actSector->Place( ntf->stopPosition ) )
		{
			actSector->NearGridCast(eNtfPtr);
		}		
	}	

	return true;
}

Bool ActionSectorController::ChangePlayerPosToFootholds(UInt32 fh1, UInt32 fh2, UInt32 fh3, UInt32 fh4, UInt32 fh5)
{	
	std::vector<UInt32> footholds;
	footholds.emplace_back(fh1);
	footholds.emplace_back(fh2); 
	footholds.emplace_back(fh3); 
	footholds.emplace_back(fh4); 
	footholds.emplace_back(fh5); 
	
	VectorPlayer vecPlayer;
	m_sector->GetPlayers(vecPlayer);

	for (size_t i = 0; i < vecPlayer.size(); ++i)
	{
		VERIFY_DO(i < Limits::MAX_PARTY_MEMBER, continue);

		EntityPlayer* player = vecPlayer[i];
		VALID_DO(player && player->IsValid() && player->IsAlive(), continue);
		
		IndexPosition indexFoothold = footholds[i];
		VALID_DO(indexFoothold > 0, continue);
				
		const PositionElem* footholdElem = SCRIPTS.GetFoothold(indexFoothold);
		VERIFY_DO(footholdElem, continue);

		ActionSector* actSector = GetEntityAction(player);
		VERIFY_RETURN(actSector, false);


		Vector3 rightPos;

		VERIFY_RETURN(actSector->GetValidPosition(footholdElem->pos, rightPos), false);

		VERIFY_RETURN(actSector->Place(rightPos), false);

		ENtfGameForceChangePos* ntf = NEW ENtfGameForceChangePos;
		ntf->entityId = player->GetId();
		ntf->stopPosition = rightPos;
		ntf->dir = 0;
		actSector->NearGridCast(EventPtr(ntf));
	}

	return true;
}



Bool ActionSectorController::changeMonsterPosToFoothold(UInt32 sequence, IndexPosition footholdIndex)
{
	stEntityId	target(static_cast<UInt16>(EntityTypes::NPC), sequence);
	Entity4Zone* entity4Zone = m_sector->FindEntity(target);
	VERIFY_RETURN(entity4Zone, false);

	const PositionElem* footholdElem = SCRIPTS.GetFoothold(footholdIndex);
	VERIFY_RETURN(footholdElem, false);

	ActionSector* actSector = GetEntityAction(entity4Zone);
	VERIFY_RETURN(actSector, false);

	Vector3 rightPos;

	VERIFY_RETURN(actSector->GetValidPosition(footholdElem->pos, rightPos), false);

	VERIFY_RETURN(actSector->Place(rightPos), false);

	ENtfGameForceChangePos* ntf = NEW ENtfGameForceChangePos;
	ntf->entityId = entity4Zone->GetId();
	ntf->stopPosition = rightPos;
	ntf->dir = 0;
	actSector->NearGridCast(EventPtr(ntf));

	return true;
}

Bool ActionSectorController::HasQuestBeenFinished( UInt32 questId )
{
	VectorPlayer vecPlayer;
	m_sector->GetPlayers(vecPlayer );

	for ( size_t i = 0; i < vecPlayer.size(); ++i )
	{
		EntityPlayer* player = vecPlayer[ i ];
		VALID_DO(player && player->IsValid(), continue);

		if(player->GetQuestAction().IsFinished( questId ) )
		{
			return true;
		}		
	}

	return false;
}

Bool ActionSectorController::OngoingQuest( UInt32 questId )
{
	VectorPlayer vecPlayer;
	m_sector->GetPlayers(vecPlayer );

	for ( size_t i = 0; i < vecPlayer.size(); ++i )
	{
		EntityPlayer* player = vecPlayer[ i ];
		VALID_DO(player && player->IsValid(), continue);

		const QuestElem* questElem = SCRIPTS.GetQuest( questId );
		VALID_DO(questElem, continue);

		// 진행 중인 녀석이 한명이라도 있으면 true
		if( player->GetQuestAction().IsProgressOrComplete( questId ) )
		{
			return true;
		}
	}

	return false;
}

Bool ActionSectorController::IsCompleteQuest( UInt32 questId )
{
	VectorPlayer vecPlayer;
	m_sector->GetPlayers(vecPlayer );

	for ( size_t i = 0; i < vecPlayer.size(); ++i )
	{
		EntityPlayer* player = vecPlayer[ i ];
		VALID_DO(player && player->IsValid(), continue);

		const QuestElem* questElem = SCRIPTS.GetQuest( questId );
		VALID_DO(questElem, continue);

		if(player->GetQuestAction().IsComplete( questId ) )
		{
			return true;
		}		
	}

	return false;
}

//[MUSTBE BY MOOK] - ActionPlayerInventory는 항상 널이다
Bool ActionSectorController::HasItem(IndexItem itemIndex )
{
	ActionPlayerInventory* actPlayerInven = GetEntityAction( GetOwnerController() );
	if( actPlayerInven == nullptr )
	{
		return false;
	}

	const ItemInfoElem* elem = SCRIPTS.GetItemInfoScript(itemIndex);
	VERIFY_RETURN(elem, false);

	return actPlayerInven->HasItem(elem->IsEquipable() ? (EquipBags << InvenBags) : InvenBags, itemIndex);
}

Bool ActionSectorController::IsClassTypeInSector( ClassType::Enum classType )
{
	VERIFY_RETURN(m_sector, 0);
	VERIFY_RETURN(m_processor, 0 );

	VectorPlayer vecPlayer;
	m_sector->GetPlayers(vecPlayer );

	for ( size_t i = 0; i < vecPlayer.size(); ++i )
	{
		EntityPlayer* player = vecPlayer[i];
		VALID_DO(player && player->IsValid(), continue);

		if( player->GetPlayerAttr().eClassType & classType )
		{
			return true;
		}
	}

	return false;
}

Bool ActionSectorController::IsBattleStateNpcIndex(IndexNpc npcIndex)
{
	VERIFY_RETURN(m_sector, 0);
	VERIFY_RETURN(m_processor, 0);

	VectorNpc npcEntitiesInSector;
	m_sector->GetNpcs(npcEntitiesInSector);

	for (auto i = npcEntitiesInSector.begin(); i != npcEntitiesInSector.end(); ++i)
	{
		EntityNpc* npc = (*i);
		if (npc == nullptr)
			continue;

		AttributeNpc* attrNpc = GetEntityAttribute(npc);
		if (attrNpc && attrNpc->GetNpcIndex() == npcIndex)
		{
			ActionSkillControl *asc = npc->GetAction< ActionSkillControl >();
			if (asc != nullptr)
			{
				if (asc->IsBattleMode())
				{
					return true;
				}
			}
		}
	}

	return false;
}

Bool ActionSectorController::IsBattleStateSpawnVolumeIndex(IndexSpawnvolume spawnVolumeIndex)
{
	VERIFY_RETURN(m_sector, 0);
	VERIFY_RETURN(m_processor, 0);

	VectorNpc npcEntitiesInSector;
	m_sector->GetNpcs(npcEntitiesInSector);

	for (auto i = npcEntitiesInSector.begin(); i != npcEntitiesInSector.end(); ++i)
	{
		EntityNpc* npc = (*i);
		if (npc == nullptr)
			continue;

		AttributeNpc* attrNpc = GetEntityAttribute(npc);
		if (attrNpc && attrNpc->GetVolumeSpawnIndex() == spawnVolumeIndex)
		{
			ActionSkillControl *asc = npc->GetAction< ActionSkillControl >();
			if (asc != nullptr)
			{
				if (asc->IsBattleMode())
				{
					return true;
				}
			}
		}
	}

	return false;
}

Bool ActionSectorController::SetInvincibleSpawnvolume(IndexSpawnvolume spawnVolumeIndex, UInt32 invincible )
{
	VERIFY_RETURN(m_sector, 0 );
	VERIFY_RETURN(m_processor, 0 );

	VectorNpc npcsInSector;
	m_sector->GetNpcs(npcsInSector );

	for ( auto i = npcsInSector.begin(); i != npcsInSector.end(); ++i )
	{
		EntityNpc* npc = ( *i );
		VERIFY_DO(npc, continue);

		//AttributeNpc* attrNpc = GetEntityAttribute( npc );
		if (npc->GetNpcAttr().GetVolumeSpawnIndex() == spawnVolumeIndex )
		{
			npc->SetInvincible(invincible);
			//if ( attrNpc->scriptElems.npcInfoElem )
			//{
			//	attrNpc->invincible = invincible;
			//}
		}
	}

	return true;
}

Bool ActionSectorController::SetAttackableSpawnvolume(IndexSpawnvolume spawnVolumeIndex, UInt32 attackable )
{
	VERIFY_RETURN( m_sector, 0 );
	VERIFY_RETURN( m_processor, 0 );

	VectorNpc npcsInSector;
	m_sector->GetNpcs(npcsInSector );

	for ( auto i = npcsInSector.begin(); i != npcsInSector.end(); ++i )
	{
		EntityNpc* npc = ( *i );
		VERIFY_DO(npc, continue);

		//AttributeNpc* attrNpc = GetEntityAttribute( npc );
		if (npc->GetNpcAttr().GetVolumeSpawnIndex() == spawnVolumeIndex )
		{
			//if ( attrNpc && attrNpc->scriptElems.npcInfoElem )
			{
				npc->SetAttackable(attackable);
				//attrNpc->attackable = attackable;
				if (0 == attackable)
				{
					npc->GetHsm().Tran(EntityState::STATE_NPC_IDLE );
				}				
			}
		}
	}

	return true;
}

void ActionSectorController::FireDirectionSkillSpawnVolumeIndex(IndexSpawnvolume spawnVolumeIndex, IndexSkill skillIndex, Float direction )
{
	VERIFY_RETURN( m_sector && m_processor, );

	VectorNpc npcEntitiesInSector;
	m_sector->GetNpcs(npcEntitiesInSector );

	for ( auto i = npcEntitiesInSector.begin(); i != npcEntitiesInSector.end(); ++i )
	{
		EntityNpc* npc = ( *i );
		if ( npc == nullptr )
			continue;

		AttributeNpc* attrNpc = GetEntityAttribute( npc );
		if ( attrNpc && attrNpc->GetVolumeSpawnIndex() == spawnVolumeIndex )
		{
			ActionNpcAggro* ana = GetEntityAction( npc );
			if ( ana )
			{
				ActionNpcScript* ans = GetEntityAction( npc );
				if ( ans )
				{
					if ( ans->SelectSkill( skillIndex ) )
					{
						ana->SetCompelHasAggroList( true );
						ans->SetSkillDirection( direction );
						ans->SetSkillTarget( 0, 0, TargetType::MYSELF, 0 );
						npc->GetHsm().Tran(EntityState::STATE_NPC_SKILL_FIRE );
					}
				}		
			}			
		}
	}
}

void ActionSectorController::AddTimeJob( UInt32 interval, UInt32 repeatcount, std::function<void()> func_ )
{
	LTimerWorker->Push( TimerJobPtr( NEW RelativeTimerJob( interval, repeatcount, func_, m_sector->GetId() ) ) );		
}

void ActionSectorController::SetSectorSkillSpawnvolumeTimeJob( UInt32 interval, UInt32 repeatcount, UInt32 spawnvolumeIndex, IndexSkill skillIndex, Float direction )
{
	AddTimeJob( interval, repeatcount, std::bind( &ActionSectorController::FireDirectionSkillSpawnVolumeIndex, this, spawnvolumeIndex, skillIndex, direction ) );	
}

void ActionSectorController::ResetSectorEntities()
{
	// 기존에 모든 entity 삭제			
	m_sector->RemoveAllSpecificEntityType( EntityTypes::NPC );
	m_sector->RemoveAllSpecificEntityType( EntityTypes::DROPPED_ITEM );

	// 스폰 볼륨 재 활성화
	m_sector->ResetSpawnVolume( );
}

Bool ActionSectorController::FireSkillSpawnVolumeIndex(IndexSpawnvolume spawnVolumeIndex, IndexSkill skillIndex )
{
	VERIFY_RETURN(m_sector, 0);
	VERIFY_RETURN(m_processor, 0);

	VectorNpc npcEntitiesInSector;
	m_sector->GetNpcs(npcEntitiesInSector );

	for ( auto i = npcEntitiesInSector.begin(); i != npcEntitiesInSector.end(); ++i )
	{
		EntityNpc* npc = ( *i );
		if ( npc == nullptr )
			continue;

		AttributeNpc* attrNpc = GetEntityAttribute( npc );
		if ( attrNpc && attrNpc->GetVolumeSpawnIndex() == spawnVolumeIndex )
		{
			ActionNpcAggro* ana = GetEntityAction( npc );
			if ( ana )
			{
				ActionNpcScript* ans = GetEntityAction( npc );
				if ( ans )
				{
					if ( ans->SelectSkill( skillIndex ) )
					{
						ana->SetCompelHasAggroList( true );						
						ans->SetSkillTarget( 0, 0, TargetType::MYSELF, 0 );
						npc->GetHsm().Tran(EntityState::STATE_NPC_SKILL_FIRE );
					}
				}
			}
		}
	}

	return true;
}

Bool ActionSectorController::CancelSkillSpawnVolumeIndex(IndexSpawnvolume spawnVolumeIndex )
{
	VERIFY_RETURN(m_sector, 0);
	VERIFY_RETURN(m_processor, 0);

	VectorNpc npcEntitiesInSector;
	m_sector->GetNpcs(npcEntitiesInSector );

	for ( auto i = npcEntitiesInSector.begin(); i != npcEntitiesInSector.end(); ++i )
	{
		EntityNpc* npc = ( *i );
		if ( npc == nullptr )
			continue;

		AttributeNpc* attrNpc = GetEntityAttribute( npc );
		if ( attrNpc && attrNpc->GetVolumeSpawnIndex() == spawnVolumeIndex )
		{
			ActionNpcSkill* ans = GetEntityAction( npc );
			ans->CancelSkill();

			npc->GetHsm().Tran(EntityState::STATE_NPC_IDLE );
		}		
	}

	return true;
}

#ifdef	SCRIPT_FORCED_SKILL_SUCCESS_by_cheolhoon_180530
Bool ActionSectorController::FirePointSkillSpawnVolumeIndex(IndexSpawnvolume spawnVolumeIndex, IndexSkill skillIndex, Float x, Float y, Float z, UInt32 isForcedSuccessWithoutTarget)
#else
Bool ActionSectorController::FirePointSkillSpawnVolumeIndex(IndexSpawnvolume spawnVolumeIndex, IndexSkill skillIndex, Float x, Float y, Float z )
#endif // SCRIPT_FORCED_SKILL_SUCCESS_by_cheolhoon_180530

{
	VERIFY_RETURN(m_sector, 0);
	VERIFY_RETURN(m_processor, 0);

	VectorNpc npcEntitiesInSector;
	m_sector->GetNpcs(npcEntitiesInSector );

	for ( auto i = npcEntitiesInSector.begin(); i != npcEntitiesInSector.end(); ++i )
	{
		EntityNpc* npc = ( *i );
		if ( npc == nullptr )
			continue;

		AttributeNpc* attrNpc = GetEntityAttribute( npc );
		if ( attrNpc && attrNpc->GetVolumeSpawnIndex() == spawnVolumeIndex )
		{
			ActionNpcAggro* ana = GetEntityAction( npc );
			if ( ana )
			{
				ActionNpcScript* ans = GetEntityAction( npc );
				if ( ans )
				{
					if ( ans->SelectSkill( skillIndex ) )
					{
						ana->SetCompelHasAggroList( true );
						ans->SetSkillSpecificPos( x, y, z );						
#ifdef	SCRIPT_FORCED_SKILL_SUCCESS_by_cheolhoon_180530							
						ans->SetForcedSuccessWithoutTarget(isForcedSuccessWithoutTarget);
#endif // SCRIPT_FORCED_SKILL_SUCCESS_by_cheolhoon_180530
						npc->GetHsm().Tran(EntityState::STATE_NPC_SKILL_FIRE );
					}
				}
			}
		}
	}

	return true;
}

Bool ActionSectorController::MoveIgnoreSpawnVolumeIndex(IndexSpawnvolume spawnVolumeIndex, UInt32 pathVolumeIndex, UInt32 direction, UInt32 isRun )
{
	VERIFY_RETURN(m_sector, 0);
	VERIFY_RETURN(m_processor, 0);

	VectorNpc npcEntitiesInSector;
	m_sector->GetNpcs(npcEntitiesInSector );

	for ( auto i = npcEntitiesInSector.begin(); i != npcEntitiesInSector.end(); ++i )
	{
		EntityNpc* npc = ( *i );
		if ( npc == nullptr )
			continue;

		AttributeNpc* attrNpc = GetEntityAttribute( npc );
		if ( attrNpc && attrNpc->GetVolumeSpawnIndex() == spawnVolumeIndex )
		{
			ActionSector* actSector = GetEntityAction( npc );
			if ( nullptr == actSector )
			{
				continue;
			}

			PathVolumeScript* script = 	GetZoneScript<PathVolumeScript>(npc->GetCurrZoneIndex());
			if ( script == nullptr )
			{
				continue;
			}

			const PathVolumeScript::Element* elem = script->Get( pathVolumeIndex );
			if ( elem == nullptr )
			{
				continue;
			}			
			
			if ( !actSector->GetValidPosition( Vector3( elem->x, elem->y, elem->z ), attrNpc->movetoRightNowPosition ) )
			{
				return false;
			}

			//ActionNpc* ac = GetEntityAction( npc );
			if (isRun )
			{
				npc->SetDefaultMoveState( MoveState::MOVE_STATE_RUN );
			}
			else
			{
				npc->SetDefaultMoveState( MoveState::MOVE_STATE_WALK );
			}
			
			attrNpc->forceDirection = static_cast<Float>(direction);
			attrNpc->SetPatrolIndex(pathVolumeIndex);
			attrNpc->SetReturnPosition(attrNpc->movetoRightNowPosition);
			npc->GetHsm().Tran(EntityState::STATE_NPC_MOVETO_RIGHT_NOW );
		}
	}

	return true;
}

Bool ActionSectorController::MoveSpawnVolumeIndex(IndexSpawnvolume spawnVolumeIndex, UInt32 pathVolumeIndex, UInt32 direction, UInt32 isRun )
{
	VERIFY_RETURN(m_sector, 0 );
	VERIFY_RETURN(m_processor, 0);

	VectorNpc npcEntitiesInSector;
	m_sector->GetNpcs(npcEntitiesInSector );

	for ( auto i = npcEntitiesInSector.begin(); i != npcEntitiesInSector.end(); ++i )
	{
		EntityNpc* npc = ( *i );
		if ( npc == nullptr )
			continue;

		if ( false == MoveEntityWithSpawnVolumeIndex( npc, spawnVolumeIndex, pathVolumeIndex, isRun, direction ) )
			continue;
	}

	return true;
}

Bool ActionSectorController::MoveEntityWithSpawnVolumeIndex(EntityUnit* npc, IndexSpawnvolume spawnVolumeIndex, UInt32 pathVolumeIndex, UInt32 isRun, UInt32 direction )
{
	AttributeNpc* attrNpc = GetEntityAttribute( npc );
	if ( nullptr == attrNpc 
		|| attrNpc->GetVolumeSpawnIndex() != spawnVolumeIndex )
	{
		return false;
	}
	ActionSector* actSector = GetEntityAction( npc );
	if ( nullptr == actSector )
	{
		return false;
	}

	PathVolumeScript* script = GetZoneScript<PathVolumeScript>(npc->GetCurrZoneIndex());
	if ( script == nullptr )
	{
		return false;
	}

	const PathVolumeScript::Element* elem = script->Get( pathVolumeIndex );
	if ( elem == nullptr )
	{
		return false;
	}

	if ( !actSector->GetValidPosition( Vector3( elem->x, elem->y, elem->z ), attrNpc->movetoEventuallyPosition ) )
	{
		return false;
	}

	ActionNpc* ac = GetEntityAction( npc );
	if ( nullptr != ac && isRun )
	{
		ac->SetMoveToEventuallyMoveState( MoveState::MOVE_STATE_RUN );
	}
	else
	{
		ac->SetMoveToEventuallyMoveState( MoveState::MOVE_STATE_WALK );
	}

	attrNpc->forceDirection = static_cast<Float>( direction );
	attrNpc->SetPatrolIndex(pathVolumeIndex);
	attrNpc->SetReturnPosition(attrNpc->movetoEventuallyPosition);
	npc->GetHsm().Tran(EntityState::STATE_NPC_MOVETO_EVENTUALLY );
	return true;
}

Bool ActionSectorController::Show_helpvolume(IndexQuest questIndex, UInt32 helpVolumeIndex)
{
	VERIFY_RETURN(questIndex, false);
	VERIFY_RETURN(m_sector, false);
	VERIFY_RETURN(m_processor, false);

	const QuestElem *elem = SCRIPTS.GetQuest(questIndex);
	VALID_RETURN(elem, false);

	VectorPlayer vecPlayer;
	m_sector->GetPlayers(vecPlayer);

	if (helpVolumeIndex == 0)
	{
		for (auto iter = elem->helpVolume.begin(); iter != elem->helpVolume.end(); iter++)
		{
			sendAndUpdateHelpVolume(questIndex, *iter, true, vecPlayer);
		}
	}
	else
	{
		auto iter = elem->helpVolume.find(helpVolumeIndex);
		VALID_RETURN(iter != elem->helpVolume.end(), false);

		sendAndUpdateHelpVolume(questIndex, *iter, true, vecPlayer);
	}

	return true;
}

Bool ActionSectorController::Hide_helpvolume(IndexQuest questIndex, UInt32 helpVolumeIndex)
{
	VERIFY_RETURN(questIndex, false);
	VERIFY_RETURN(m_sector, false);
	VERIFY_RETURN(m_processor, false);

	const QuestElem *elem = SCRIPTS.GetQuest(questIndex);
	VALID_RETURN(elem, false);

	VectorPlayer vecPlayer;
	m_sector->GetPlayers(vecPlayer);

	if (helpVolumeIndex == 0)
	{
		for (auto iter = elem->helpVolume.begin(); iter != elem->helpVolume.end(); iter++)
		{
			sendAndUpdateHelpVolume(questIndex, *iter, false, vecPlayer);
		}
	}
	else
	{
		auto iter = elem->helpVolume.find(helpVolumeIndex);
		VALID_RETURN(iter != elem->helpVolume.end(), false);

		sendAndUpdateHelpVolume(questIndex, *iter, false, vecPlayer);
	}

	return true;
}

Bool ActionSectorController::Load( const char* key, UInt32& value )
{
	if ( key == nullptr || strlen(key) == 0 )
	{
		MU2_ASSERT(false);
		return false;
	}

	lua_getglobal( m_L, key );
	if ( lua_isnumber(m_L, -1) )
	{
		value = static_cast<UInt32>( lua_tointeger(m_L, -1) );
		return true;
	}

	return false;
}

Bool ActionSectorController::Load( const char* key, Int32& value )
{
	if ( key == nullptr || strlen(key) == 0 )
	{
		MU2_ASSERT(false);
		return false;
	}

	lua_getglobal( m_L, key );
	if ( lua_isnumber(m_L, -1) )
	{
		value = static_cast<Int32>(lua_tointeger(m_L, -1));
		return true;
	}

	return false;
}

Bool ActionSectorController::Load( const char* key, Float& value )
{
	if ( key == nullptr || strlen(key) == 0 )
	{
		MU2_ASSERT(false);
		return false;
	}

	lua_getglobal( m_L, key );
	if ( lua_isnumber( m_L, -1 ) )
	{
		value = static_cast<Float>(lua_tonumber(m_L, -1));
		return true;
	}

	return false;
}

void ActionSectorController::SetKeyValue( const char* key, UInt32 valueOfKey )
{
	if ( key == nullptr || strlen(key) == 0 )
	{
		MU2_ASSERT(false);
		return;
	}

	auto inserted= m_keyValueMap.insert( KeyValueMap::value_type(key, valueOfKey));
	if ( inserted.second == false )
	{
		inserted.first->second = valueOfKey;
	}
}

Int32 ActionSectorController::GetKeyValue( const char* key )
{
	if ( key == nullptr || strlen(key) == 0 )
	{
		MU2_ASSERT(false);
		return 0;
	}

	auto iter = m_keyValueMap.find( key );
	if ( iter != m_keyValueMap.end() )
		return iter->second;

	return -1;
}

void ActionSectorController::DelKeyValue( const char* key )
{
	if ( key == nullptr || strlen(key) == 0 )
	{
		MU2_ASSERT(false);
		return;
	}

	m_keyValueMap.erase( key );
}

Bool ActionSectorController::IsKeyValue( const char* key )
{
	if ( key == nullptr || strlen(key) == 0 )
	{
		MU2_ASSERT(false);
		return false;
	}

	return m_keyValueMap.find( key ) != m_keyValueMap.end();
}

void ActionSectorController::OnEvent(DungeonEventType::Enum eventType, EntityUnit* owner, EntityUnit* caster, UInt32 v1, UInt32 v2, const char* strId )
{
	VERIFY_RETURN(m_sector && m_processor, );

	m_processor->ProcessEvent( eventType, owner, caster, v1, v2 );

	triggerScriptEvent( eventType, owner, caster, v1, v2, strId);
}

void ActionSectorController::OnTick()
{
	// 볼륨 체크 / 타이머 체크를 위해 호출. 자체적으로 실행 시간 조절
	processScriptEvent();

	VERIFY_RETURN(m_processor, );
	
	m_processor->Update();
	
}

void ActionSectorController::DestroyDungeon()
{
	VERIFY_RETURN(m_sector && m_processor, );

	KickAllPlayers();
	m_processor->DestroyDungeon();
}

void ActionSectorController::ChangeEntityState( const EntityId& id, UInt32 state, const UInt32 _teamType, const UInt32 _point )
{
	VERIFY_RETURN(m_sector && m_processor, );

	EntityNpc* npc = m_sector->FindNpc( id );
	VALID_RETURN( npc && npc->IsValid(), );

	m_processor->ChangeEntityState( npc, state, _teamType, _point );	
}

void ActionSectorController::AddPlayerSpawnVolume(IndexSpawnvolume volumeId )
{
	VERIFY_RETURN(m_sector && m_processor, );

	m_processor->AddPlayerSpawnVolume( volumeId );
}

void ActionSectorController::AddTagVolume( UInt32 stage, UInt32 volumeId )
{
	VERIFY_RETURN(m_sector && m_processor, );
	m_processor->AddTagVolume( stage, volumeId );
}

void ActionSectorController::EnterStage( UInt32 num )
{
	VERIFY_RETURN(m_sector && m_processor, );
	m_processor->OnEnterStage( num );
}

void ActionSectorController::ProcessResult()
{
	VERIFY_RETURN(m_sector && m_processor, );
	m_processor->ProcessResult();
}

void ActionSectorController::OnEnterLongDisplacement(EntityUnit* entity )
{
	VERIFY_RETURN(m_sector && m_processor, );
	m_processor->OnEnterLongDisplacement( entity );
}

void ActionSectorController::OnCallLongDisplacement(EntityUnit* entity, const Vector3& opos, const Vector3& npos )
{
	VERIFY_RETURN(m_sector && m_processor, );
	m_processor->OnCallLongDisplacement( entity, opos, npos );
}

void ActionSectorController::OnLeaveLongDisplacement(EntityUnit* entity )
{
	VERIFY_RETURN(m_sector && m_processor, );
	m_processor->OnLeaveLongDisplacement( entity );
}

UInt32 ActionSectorController::CheckDungeonDestroy()
{
	VERIFY_RETURN(m_sector && m_processor, 0);

	if ( GetPlayerCount() == 0 )
	{
		return 18;	// 삭제!
	}

	TeamType::Enum eDiffTeam = TeamType::TEAM_NONE;
	VectorPlayer vecPlayer;
	m_sector->GetPlayers(vecPlayer );

	for ( size_t i = 0; i < vecPlayer.size(); ++i )
	{
		EntityPlayer* player = vecPlayer[i];
		VALID_DO(player && player->IsValid(), continue);

		TeamType::Enum eTeam = player->GetMyTeam();
		VALID_DO(eTeam, continue);		
		

		if ( eDiffTeam == 0 )
		{
			eDiffTeam = eTeam;
		}

		if ( eDiffTeam != eTeam)
		{
			return 0;	
		}
	}

	return static_cast<UInt32>(eDiffTeam);	// 다른 팀이 없다고 판단 남은 팀이 무조건 이김.
}

void ActionSectorController::PushTraverseLogicValueInSectorPlayer( UInt32 checkValue )
{
	VERIFY_RETURN(m_sector, );

	VectorPlayer vecPlayer;
	m_sector->GetPlayers(vecPlayer );

	for ( auto  i = vecPlayer.begin(); i != vecPlayer.end(); ++i )
	{
		EntityPlayer* entity = (*i);
		if ( entity == nullptr )
		{
			continue;
		}

		ActionSector* as = GetEntityAction( entity );
		if ( as && as->GetTraverseLogicData() )
		{
			as->GetTraverseLogicData()->Push( checkValue );
		}
	}
}

void ActionSectorController::SyncGameState()
{	
	VERIFY_RETURN(m_sector && m_processor, );
	m_processor->SyncGameState();
}

void ActionSectorController::OnTagValueChanged(Entity4Zone* entity, UInt32 oldValue, UInt32 newValue)
{
	VERIFY_RETURN(m_sector && m_processor, );
	m_processor->OnTagValueChanged(entity, oldValue, newValue);
}

Bool ActionSectorController::SetBossPortalRoomInfo(InstanceSectorType::Enum instanceSectorType, const BossPortalInfo& info )
{	
	switch ( instanceSectorType )
	{
	case InstanceSectorType::BOSS:
		{
			BossProcessor* processor = static_cast<BossProcessor*>( m_processor );
			if ( processor )
			{
				processor->SetBossPortalRoomInfo( info );
				return true;
			}
		}
		break;

	case InstanceSectorType::EXPLORE:
		{
			ExplorerProcessor* process = static_cast<ExplorerProcessor*>( m_processor );
			if( process )
			{
				process->SetBossPortalInfo( info );
				return true;
			}
		}
		break;
	}
	
	return false;
}

bool ActionSectorController::AddScriptEvent(UInt32 type, UInt32 id, UInt32 v1, UInt32 v2)
{
	// type / id is a key

	DelScriptEvent(type, id);

	m_scriptEvents.emplace_back(type, id, v1, v2);

	return true;
}

bool ActionSectorController::DelScriptEvent(UInt32 type, UInt32 id)
{
	std::for_each(m_scriptEvents.begin(), m_scriptEvents.end(),
		[scriptEventId = ScriptEventIdPtr(NEW IntScriptEventId(id)), &type](ScriptEvent& se) { if (se.type == type && scriptEventId->isEqual(se.id)) { se.removed = true; } }
	);

	return true;
}

bool ActionSectorController::AddStringScriptEvent(UInt32 type, const char* id, UInt32 v1, UInt32 v2)
{
	DelStringScriptEvent(type, id);

	m_scriptEvents.emplace_back(type, id, v1, v2);
	return true;
}

bool ActionSectorController::DelStringScriptEvent(UInt32 type, const char* id)
{
	std::for_each(m_scriptEvents.begin(), m_scriptEvents.end(),
		[scriptEventId = ScriptEventIdPtr(NEW StrScriptEventId(id)), &type](ScriptEvent& se) { if (se.type == type && scriptEventId->isEqual(se.id)) { se.removed = true; } }
	);

	return true;
}

void ActionSectorController::processScriptEvent()
 {	
 	SIMPLE_TIMER_CHECK( m_scriptTimer, 100 );

 	ScriptEvents events;
 	for (auto iter = m_scriptEvents.begin(); iter != m_scriptEvents.end(); ++iter )
 	{
 		ScriptEvent& se = *iter;
 
 		if (se.removed)
 		{
 			continue;
 		}

 		switch ( se.type )
 		{
 		case DungeonEventType::VOLUME_IN:
 			{
 				if ( CheckPlayerInVolume( se.GetId()) )
 				{
 					events.push_back( *iter );					
 				}
 			}
 			break;
 		case DungeonEventType::TIMER:
 			{
 				if ( se.timer.Elapsed() >= se.v1 )
 				{
 					events.push_back( *iter );					
 
 					se.execCount++;
 
 					se.timer.Reset();
 
 					// 실행 제한 회수를 넘어가면 
 					if ( se.execCount >= se.v2 )
 					{
 						se.removed = true;
 					}
 				}
 			}
 			break;
 		}
 	}
 
 	for ( auto& iter : events )
 	{
 		ScriptEvent& se = iter;
 
 		switch ( se.type )
 		{
 		case DungeonEventType::VOLUME_IN:
 			{
 				triggerScriptEvent( se.type, nullptr/*GetOwnerController()*/, nullptr, se.GetId(), 0 );
 			}			
 			break;
 		case DungeonEventType::TIMER:
 			{
 				triggerScriptEvent( se.type, nullptr/*GetOwnerController()*/, nullptr, se.GetId(), se.execCount );
 			}	
 			break;
 		}
 	}
 
 	// purge removed events
 	m_scriptEvents.erase( 
 		std::remove_if( m_scriptEvents.begin(), m_scriptEvents.end(), 
 						[&](const ScriptEvent& se)->bool { return se.removed; } 
 		), 
 		m_scriptEvents.end() 
 	);
 }
 
 void ActionSectorController::triggerScriptEvent( UInt32 eventType, EntityUnit* owner, EntityUnit* target, UInt32 av1, UInt32 av2, const char* strId )
 {
 	// eventType별로 제공해야 할 데이터가 다르므로 여기서 해석해서 호출한다. 
 
 	UInt32 type = eventType; 
	CharId triggerCharId = 0;
	ScriptEventIdPtr id = nullptr;
 	UInt32 v1 = 0;
 	UInt32 v2 = 0;
 
 	if ( nullptr != owner && owner->IsPlayer() )
 	{
 		AttributePlayer* attrPlayer = GetEntityAttribute( owner );
 		triggerCharId = attrPlayer->charId;
 		
 	}
 
 	switch ( eventType )
 	{
 	case DungeonEventType::NPC_DEAD:
 		{
			VERIFY_RETURN(owner && owner->IsValid() && owner->IsNpc(), );
			EntityNpc* npc = static_cast<EntityNpc*>(owner);
 			
 			id = ScriptEventIdPtr(NEW IntScriptEventId( npc->GetNpcIndex() ) );
 
 			if ( nullptr != target && target->IsPlayer() )
 			{
 				AttributePlayer* attrPlayer = GetEntityAttribute( target ); 				
 				triggerCharId = attrPlayer->charId;
 			}
#ifdef __Patch_Lua_InstanceGroup_Param_by_jason_20190109
			v1 = av1;
#endif
 		}
 		break;
 	case DungeonEventType::NPC_SPAWNED:
 		{
			VERIFY_RETURN(owner && owner->IsValid() && owner->IsNpc(), );
			EntityNpc* npc = static_cast<EntityNpc*>(owner);

 			id = ScriptEventIdPtr(NEW IntScriptEventId(npc->GetNpcIndex()) );
 		}
 		break;
	case DungeonEventType::SPAWN_VOLUME_SPAWNED:
	{
		VERIFY_RETURN(owner && owner->IsValid() && owner->IsNpc(), );
		EntityNpc* npc = static_cast<EntityNpc*>(owner);
		
		id = ScriptEventIdPtr(NEW IntScriptEventId(npc->GetNpcAttr().GetVolumeSpawnIndex()));

		if (nullptr != target && target->IsPlayer())
		{
			AttributePlayer* attrPlayer = GetEntityAttribute(target);
			triggerCharId = attrPlayer->charId;
		}
	}
	break;
	case DungeonEventType::SPAWN_VOLUME_DEAD:
	{
		VERIFY_RETURN(owner && owner->IsValid() && owner->IsNpc(), );
		EntityNpc* npc = static_cast<EntityNpc*>(owner);

		id = ScriptEventIdPtr(NEW IntScriptEventId(npc->GetNpcAttr().GetVolumeSpawnIndex()));
	}
	break;
 	case DungeonEventType::QUEST_ACCEPTED:
 		{
 			id = ScriptEventIdPtr(NEW IntScriptEventId(av1) );
 		}
 		break;
 	case DungeonEventType::QUEST_COMPLETED:
 		{
 			id = ScriptEventIdPtr(NEW IntScriptEventId(av1) );
 		}
 		break;
 	case DungeonEventType::OBJECT_TOUCHED:
 		{
 			// v1이 오브젝트 인덱스
 			id = ScriptEventIdPtr(NEW IntScriptEventId(av1));
 			v1 = v2; // 오브젝트 타잎 : 퀘스트 / 포털 등등
 		}
 		break;
 	case DungeonEventType::OBJECT_COMPLETED:
 		{
 			// v1이 오브젝트 인덱스
 			id = ScriptEventIdPtr(NEW IntScriptEventId(av1));
 			v1 = v2;
 		}
 		break;		
 	case DungeonEventType::VOLUME_IN:
 		{
 			id = ScriptEventIdPtr(NEW IntScriptEventId(av1));
 		}
 		break;
 	case DungeonEventType::TIMER:
 		{
 			id = ScriptEventIdPtr(NEW IntScriptEventId(av1));
 
 		}
 		break;
 	case DungeonEventType::SKIP_PLAY_SCENE:
 	{
		if (strId == nullptr || strlen(strId) == 0)
		{
			return;
		}

 		id = ScriptEventIdPtr(NEW StrScriptEventId(strId));
 	}	
 	break;
	case DungeonEventType::MONSTER_SKILL:
		{
			VERIFY_RETURN(owner && owner->IsValid() && owner->IsNpc(), );

			id = ScriptEventIdPtr(NEW IntScriptEventId(av1));
		}
	break;
 	default:
 		// 지원되는 타잎만 호출
 		return;
 	}
 
 	callScriptEvent( type, triggerCharId, id, v1, v2 );
 }
 
 void ActionSectorController::callScriptEvent( UInt32 type, CharId triggerCharId, ScriptEventIdPtr id, UInt32 v1, UInt32 v2 )
 {
 	auto iter = std::find_if( 
 		m_scriptEvents.begin(), m_scriptEvents.end(), 
 		[=](const ScriptEvent& se)->bool { return se.type == type && id->isEqual(se.id	); }
 	);
 
 	if ( iter != m_scriptEvents.end() )
 	{
 		// call on_event lua function 
 		// argument : sector, type, id, v1, v2 
 		// 
 		if ( m_L == nullptr )
 			return;
 
 		bool rc = false;
 
 		std::string control = "sector_template";		
 
 		lua_getglobal( m_L, control.c_str() );
 
 		if ( lua_istable(m_L, -1) )
 		{
 			// get state table
 			lua_pushnumber(m_L, getCurrentSectorStateId());
 			lua_gettable(m_L, -2);
 
 			if ( lua_istable(m_L, -1) )
 			{
 				lua_pushcclosure(m_L, lua_util::on_error, 0);
 				int errfunc = lua_gettop(m_L);
 
 				// get function
 				lua_pushstring(m_L, "on_event");
 				lua_gettable(m_L, -3); // func / on_error / state
 
 				if ( lua_isfunction(m_L, -1) )
 				{
 					pushSector(); // 글로벌에 등록된 NPC를 가져옴
 					
 					lua_pushnumber( m_L, type );
 					lua_pushnumber( m_L, triggerCharId );
					auto intEventId = dynamic_cast< IntScriptEventId*>(id.get());
					if (intEventId)
					{
						lua_pushnumber(m_L, intEventId->id);
					}
					else
					{
						auto strEventId = dynamic_cast< StrScriptEventId*>(id.get());
						if (strEventId)
						{
							lua_pushstring(m_L, strEventId->id.c_str());
						}
					}
 				
 					lua_pushnumber( m_L, v1 );
 					lua_pushnumber( m_L, v2 );
 
 					if ( lua_pcall(m_L, 6, 0, errfunc) != 0 )
 					{
						if (intEventId) {
							MU2_WARN_LOG(LogCategory::CONTENTS, "callScriptEvent> Failed to call on_event. [type: %d][id: %d][v1: %d][v2: %d]", type, intEventId->id, v1, v2 );
						} else {
							MU2_WARN_LOG(LogCategory::CONTENTS, "callScriptEvent> Failed to call on_event. [type: %d][id: str][v1: %d][v2: %d]", type, v1, v2 );
						}
						
						if (m_sector)
						{
							MU2_WARN_LOG( LogCategory::CONTENTS, "callScriptEvent> [sectorID: %d]", m_sector->GetWorldElem().index);
						}
						else
						{
							MU2_WARN_LOG( LogCategory::CONTENTS, "callScriptEvent> sector is null");
						}
 					}
 
 					rc = true;
 				}
 				else
 				{
 					lua_pop(m_L, 1); // 함수 제거
 				}
 
 				lua_remove(m_L, errfunc);
 			}
 			else
 			{
 				//stackDump();
 			}
 
 			lua_pop(m_L, 1); // pop state table
 		}
 		else
 		{
 			//stackDump();
 		}
 
 		lua_pop(m_L, 1); // pop sector table	
 	}
 }

UInt32 ActionSectorController::getCurrentSectorStateId() const
{
	return GetOwnerController()->GetCurrStateId();
}

void ActionSectorController::sendAndUpdateHelpVolume(IndexQuest questId, UInt32 helpVolumeId, Bool state, const VectorPlayer& vecPlayer)
{
	VALID_RETURN(m_sector->GetSharedQuestSystem()->SetHelpVolume(questId, helpVolumeId, state), );
	
	ENtfSectorHelpVolumeState* req = NEW ENtfSectorHelpVolumeState;
	EventPtr evtNtf(req);

	req->questIndex = questId;
	req->helpVolumeIndex = helpVolumeId;
	req->state = state;

	wstring msg;
	wostringstream stream;

	stream << L"Debug Message : Helpvolume " << helpVolumeId << L" is " << (state == false ? L"Off" : L"On") << "\n";
	msg = stream.str();

	for (size_t i = 0; i < vecPlayer.size(); ++i)
	{
		EntityPlayer* player = vecPlayer.at(i);
		VALID_DO(player && player->IsValid(), continue);

		player->SendToClient(evtNtf);

		theGameMsg.SendGameDebugMsg(player, const_cast<WChar*>(msg.c_str()));
	}
}

void ActionSectorController::getUnitsInVolume(UInt32 volumeIndex, EntityTypes::Enum entityType, OUT VectorUnit& entities)
{
	VolumeScript* vscript = GetZoneScript<VolumeScript>(m_sector->GetIndexZone());
	VALID_RETURN(vscript, );

	const VolumeElem* elem = vscript->Get(volumeIndex);
	VALID_RETURN(elem, );

	VectorUnit entitiesFromType;
	m_sector->GetUnits(entityType, entitiesFromType);

	for (auto i = entitiesFromType.begin(); i != entitiesFromType.end(); ++i)
	{
		EntityUnit* pUnit = (*i);
		VALID_DO(pUnit, continue);
		
		Vector3 pos;
		pUnit->GetPositionView().GetRealPosition(pos);

		Float diff = Distance2D(pos, elem->pos);
		if (diff < elem->radius)
		{
			entities.push_back(pUnit);
		}
	}
}

void ActionSectorController::OnEvent(EventPtr& e)
{
	m_processor->OnProcessEvent( e ); 
}

Bool ActionSectorController::KillAllNpc(NpcJobType::Enum type )
{
	VectorNpc vecNpc;
	m_sector->GetNpcs(vecNpc );

	if( vecNpc.empty() )
	{
		return false;
	}

	for(EntityNpc* npc : vecNpc)
	{
		if(npc->GetNpcJobType() == type )
		{	
			npc->GetSkillControlAction().RemoveAllEffectVolume();
			npc->GetHsm().Tran(EntityState::STATE_NPC_DEAD );
		}
	}

	return true;
}

Bool ActionSectorController::IsMultistageMissionClear() const
{
	return m_sector->GetSectorController().GetMissionDungeonAction().IsMultistageMissionClear();
}

Bool ActionSectorController::IsMissionClear( UInt32 index ) const
{	
	return m_sector->GetSectorController().GetMissionDungeonAction().IsMissionClear( index );
}

void ActionSectorController::DisableSharedQuest( IndexQuest questId )
{
	m_sector->GetSharedQuestSystem()->RemoveSharedQuest( questId );
}

void ActionSectorController::ClearQuestMission( UInt32 questId, UInt8 missionIndex )
{
	VectorPlayer vecPlayer;
	m_sector->GetPlayers(vecPlayer );

	for( auto& i : vecPlayer )
	{
		ActionPlayerQuest* actQuest = GetEntityAction( i );
		VERIFY_DO( actQuest, continue );

		actQuest->ClearQuestMission( questId, missionIndex );
	}
}

void ActionSectorController::MovePc( UInt32 pathVolumeIndex, Float direction, UInt16 aniType )
{
	PathVolumeScript* script = 	GetZoneScript<PathVolumeScript>(m_sector->GetIndexZone());
	if ( script == nullptr )
	{
		return;
	}

	const PathVolumeScript::Element* elem = script->Get( pathVolumeIndex );
	if ( elem == nullptr )
	{
		return;
	}
	
	VectorPlayer vecPlayer;
	m_sector->GetPlayers(vecPlayer );

	for ( size_t i = 0; i < vecPlayer.size(); ++i )
	{
		EntityPlayer* player = vecPlayer[i];
		if ( player == nullptr || player->isRemove() )
		{
			continue;
		}		

		ActionSector* actSector = GetEntityAction( player );

		float squarMaxRadious = elem->radius * elem->radius;	

		float square_len_x = mu2::GetRandBetweenF( 0, squarMaxRadious );
		float len_x = sqrt( square_len_x );
		if ( mu2::GetRand() % 2 )
		{
			len_x *= ( -1 );
		}

		float max_square_len_y = squarMaxRadious - square_len_x;
		float square_len_y = mu2::GetRandBetweenF( 0, max_square_len_y );
		float len_y = sqrt( square_len_y );
		if ( mu2::GetRand() % 2 )
		{
			len_y *= ( -1 );
		}

		Vector3 pos( elem->x, elem->y, elem->z );

		pos.x = pos.x + len_x;
		pos.y = pos.y + len_y;
		pos.z = pos.z;

		ENtfGameForceMoveStart* ntf = NEW ENtfGameForceMoveStart;
		EventPtr ntfPtr(ntf);

		ntf->entityId = player->GetId();
		ntf->position = pos;		
		ntf->aniType = aniType;
		ntf->direction = mu2::DegreeToRadian(direction);

		AttributePlayer* attrPlayer = GetEntityAttribute( player );
		if ( nullptr == attrPlayer )
		{
			return;
		}
		attrPlayer->moveState = aniType;

		ActionPlayer* playerAction = GetEntityAction( player );
		Int32 moveSpeed = player->GetAction< ActionAbility >()->GetAbilityValue(EAT::RUN_SPEED );
		if ( aniType == NpcAnimation::MOVE_WALK )
		{
			moveSpeed = player->GetAction< ActionAbility >()->GetAbilityValue(EAT::WALK_SPEED );
			playerAction->SetupSpeed( static_cast<Float>( moveSpeed ) );
		}		
		ntf->speed = moveSpeed;

		actSector->NearGridCast( ntfPtr );
	}	
}

void ActionSectorController::RotatePc( Float direction )
{
	VectorPlayer vecPlayer;
	m_sector->GetPlayers(vecPlayer );

	for ( size_t i = 0; i < vecPlayer.size(); ++i )
	{
		EntityPlayer* player = vecPlayer[i];
		VERIFY_DO(player && player->IsValid(), continue);

		ViewPosition* posView = GetEntityView(player);
		VERIFY_DO(posView, continue);

		ActionSector* actSector = GetEntityAction(player);
		VERIFY_DO(actSector, continue);


		ENtfGameSyncDirection* ntf = NEW ENtfGameSyncDirection;
		EventPtr ntfPtr(ntf);
		ntf->entityId = player->GetId();
		ntf->direction = mu2::DegreeToRadian(direction);
		posView->GetRealPosition( ntf->castLocation );
		actSector->NearGridCast( ntfPtr );
	}
}

void ActionSectorController::StartDialogSpawnVolumeIndex(IndexSpawnvolume spawnvolumeIndex, UInt32 dialogIndex, UInt32 entityType )
{	
	if (entityType)
	{
		VectorPlayer vecPlayers;
		m_sector->GetPlayers(vecPlayers);

		for ( EntityPlayer* player : vecPlayers )
		{	
			VERIFY_DO(player && player->IsValid(), continue);
			if (player->IsDead())
			{
				continue;
			}

			ENtfStartDialog* ntf = NEW ENtfStartDialog;
			ntf->entityId = player->GetId();
			ntf->dialogIndex = dialogIndex;

			ActionSector* actSector = GetEntityAction( player );
			actSector->NearGridCast( EventPtr( ntf ) );
		}

		return;
	}
	

	VectorNpc vecNpcs;
	m_sector->GetNpcs(vecNpcs);
	
	for ( EntityNpc* npc : vecNpcs)
	{	
		if ( 0 == spawnvolumeIndex )
		{
			npc->GetHsm().Tran(EntityState::STATE_NPC_DEAD );
		}
		else
		{	
			if (npc->GetVolumeSpawnIndex() == spawnvolumeIndex )
			{
				ENtfStartDialog* ntf = NEW ENtfStartDialog;
				ntf->entityId = npc->GetId();
				ntf->dialogIndex = dialogIndex;

				ActionSector* actSector = GetEntityAction( npc );
				actSector->NearGridCast( EventPtr( ntf ) );				
			}
		}
	}
}

void ActionSectorController::StateChangeSpawnVolumeIndex(IndexSpawnvolume spawnvolumeIndex, EntityState state )
{
	VectorNpc vecNpc;
	m_sector->GetNpcs(vecNpc );

	for (EntityNpc* npc : vecNpc)
	{	
		if ( 0 == spawnvolumeIndex )
		{
			npc->GetHsm().Tran(EntityState::STATE_NPC_DEAD );
		}
		else
		{	
			if ( npc->GetVolumeSpawnIndex() == spawnvolumeIndex )
			{
				npc->GetHsm().Tran( state );
			}
		}
	}
}

void ActionSectorController::UpdatePortalState( IndexPortal portalIndex, PortalState::Enum eState )
{
	VectorNpc vecNpc;
	m_sector->GetNpcs(vecNpc );

	for ( EntityNpc* npc : vecNpc )
	{
		if (npc->GetNpcIndex() == (IndexNpc)portalIndex )
		{
			ActionNpcOperate* act = GetEntityAction(npc);
			if ( act == nullptr )
			{
				continue;
			}

			if ( act->GetPortalIndex() == portalIndex )
			{
				act->UpdatePortalState( act->GetPortalType(), eState );
				break;
			}
		}		
	}

	return;
}

void ActionSectorController::SendNpcAppearanceInEventVolume( UInt32 eventvolumeIndex )
{
	IndexZone indexZone = m_sector->GetIndexZone();

	VolumeScript* vscript = GetZoneScript<VolumeScript>(indexZone);
	if (vscript == nullptr)
	{
		return;
	}
	const VolumeElem* elem = vscript->Get( eventvolumeIndex );
	if (elem == nullptr)
	{
		return;
	}

	ENtfGameExistEntity* ntf = NEW ENtfGameExistEntity;
	EventPtr evtNtf = EventPtr( ntf );

	VectorNpc vecNpc;
	m_sector->GetNpcs(vecNpc );

	if (vecNpc.empty())
	{
		return;
	}

	
	Bool isCandidate = false;
	for (EntityNpc* npc : vecNpc )
	{	
		Float diff = Distance2D( npc->GetRealPosition(), elem->pos );
		if (diff < elem->radius)
		{
			npc->GetNpcCognitionAction().SyncAppearance( ntf->syncInfo, nullptr );
			isCandidate = true;
		}		
	}

	if (false == isCandidate)
	{
		return;
	}
	
	VectorPlayer vecPlayer;
	m_sector->GetPlayers(vecPlayer );

	for (EntityPlayer* player : vecPlayer)
	{
		SERVER.SendToClient( player, evtNtf );
	}
}

void ActionSectorController::OnEndlessSelectStage( EntityPlayer* entity )
{
	m_processor->onEndlessTowerSelectStage( entity );
}

Bool ActionSectorController::ClearRemainedEntities()
{
	auto func = [&]( const SectorEntityMap::value_type& v ) -> void
	{
		Entity4Zone* entity = v.second;
		VALID_RETURN(entity && entity->IsValid(), );
		
		auto actControl = entity->GetAction< ActionSkillControl >();
		VALID_RETURN(actControl, );

		actControl->RemoveAllEffectVolume();
		actControl->RemoveAllServantNpc( NpcJobType::PET );
	};

	Sector* sector = m_sector;
	sector->ForEachByCond( EntityTypes::NPC, func );
	sector->ForEachByCond( EntityTypes::PLAYER_ZONE, func );
	return true;
}
 
 void ActionSectorController::PlayerFireSkill( const ClassType::Enum job, const IndexSkill skillIndex )
{
 	// job : 0 - 전직업, 1 ~ 4 직업
 	// skillIndex : 스킬 인덱스
 	// duration : 적용 시간 -> 나중에 추가

 	auto func = [job, skillIndex]( const SectorEntityMap::value_type& v ) -> void
 	{
		EntityPlayer* player = static_cast<EntityPlayer*>(v.second);
		VALID_RETURN(player && player->IsValid(), )
 
 		auto attr = player->GetAttribute<AttributePlayer>();
		if ( job > 0 && false == ( attr->eClassType & job ) )
			return;

		auto actControl = player->GetAction<ActionSkillControl>();
		VALID_RETURN(actControl, );
		auto viewPosition = static_cast<ViewPosition*>(player->GetView(VIEW_POSITION));
		VALID_RETURN(viewPosition, );

		ENtfGameStartCastSkill* ntf = NEW ENtfGameStartCastSkill;
		ntf->serverSide = true;
		ntf->entityId = player->GetId();
		ntf->targetId = ntf->entityId;
		ntf->seed = 0;
		ntf->castSkillId = skillIndex;
		ntf->aniType = actControl->GetCurAniIndex(skillIndex);
		ntf->speedRatio = 1;
		viewPosition->GetPosition(ntf->position);

		player->GetAction<ActionSector>()->NearGridCast(EventPtr(ntf));

		theLogicEffectSystem.OnSingleCast(player, skillIndex);
 	};

	Sector* sector = m_sector;
 	sector->ForEachByCond( EntityTypes::PLAYER_ZONE, func );
 }

 Bool ActionSectorController::IsBattleState() const
 {
	 return m_processor->IsBattleState();
 }

 Bool ActionSectorController::IsColosseum() const
 {
	 return SectorProcessorType::COLOSSEUM == m_processor->GetType();;
 }

 Bool ActionSectorController::IsColosseum33PvP() const
 {
	 return SectorProcessorType::COLOSSEUM_PVP_33 == m_processor->GetType();;
 }

 Bool ActionSectorController::IsAltarOfElements() const
 {
	 return SectorProcessorType::ALTAR_OF_ELEMENTS == m_processor->GetType();
 }

 void ActionSectorController::EnableExploredPortal(IndexPortal portalIndex )
 {
	 VectorPlayer playerEntities;
	 m_sector->GetPlayers(playerEntities );
	 if ( playerEntities.empty() )
	 {
		 return;
	 }

	 for ( const auto& player : playerEntities )
	 {
		 AttributePlayer* attrPlayer = GetEntityAttribute( player );
		 if ( attrPlayer == nullptr )
		 {
			 continue;
		 }

		 if ( attrPlayer->exploredPortals.end() ==
			 std::find( attrPlayer->exploredPortals.begin(), attrPlayer->exploredPortals.end(), portalIndex ) )
		 {
			 attrPlayer->exploredPortals.push_back( portalIndex );

			 EReqInsertExploredPortal* req = NEW EReqInsertExploredPortal;
			 req->charId = attrPlayer->charId;
			 req->portalIndex = portalIndex;
			 SERVER.SendToDb( player, EventPtr( req ) );

			 ENtfGameExploredPortalAdd* newPortal = NEW ENtfGameExploredPortalAdd;
			 newPortal->addProtalIndex = portalIndex;
			 SERVER.SendToClient( player, EventPtr( newPortal ) );

			 theGameMsg.SendGameDebugMsg( player, L"위치가 등록 되었습니다.\n" );
		 }
	 }
 }


 void ActionSectorController::OnSkipPlayScene( const std::wstring& id, EntityPlayer* player )
 {
	 auto it = m_skipPlayScenePlayers.find( id );
	 if ( it == m_skipPlayScenePlayers.end() )
	 {
		 if ( false == m_skipPlayScenePlayers.emplace( id, std::set<EntityId>{player->GetId()} ).second )
		 {
			 return;
		 }
	 }
	 else
	 {
		 it->second.insert( player->GetId() );
	 }

 	 const auto& skipPlayers = m_skipPlayScenePlayers[id];
 
 	 auto notSkipEntity = m_sector->FindIfByCond( EntityTypes::PLAYER_ZONE, [&]( const SectorEntityMap::value_type& v )
 	 {
 		 auto foundIt = skipPlayers.find( v.first );
 		 return ( foundIt == skipPlayers.end() );
 	 } );
 
 	 if ( notSkipEntity )
 	 {
 		 return;
 	 }

	 m_skipPlayScenePlayers.erase( id );
	 
	 std::string str;
	 StringUtil::Convert( id, str );
	 OnEvent( DungeonEventType::SKIP_PLAY_SCENE, player, nullptr, 0, 0, str.c_str() );

	 SetKeyValue((str + "_Event_Skip").c_str(), 1);

	 ENtfGameSkipPlayScene* ntf = NEW ENtfGameSkipPlayScene;
	 ntf->id = id;
#ifdef __Reincarnation__jason_180628__
	 m_sector->Broadcast(EventPtr(ntf));
#else
	 SendInSector( m_sector, EventPtr( ntf ) );
#endif
 }

 Bool ActionSectorController::IsSkipPlayScene( const char* id )
 {
	 std::string str;
	 StringUtil::Convert(id, str);
	 str.append("_Event_Skip");

	 return ( GetKeyValue( str.c_str() ) > 0 );
 }

 void ActionSectorController::SkipSpawnGroup( const UInt32 /*spawnVolumeIndex*/ )
 {
	 // TODO
 }

 void ActionSectorController::SkipKillSpawnVolumeMonster( const UInt32 /*spawnVolumeIndex*/ )
 {
	 // TODO
 }

 #ifdef __Renewal_Dungeon_Entrance_Cost_by_jason_20190114 
#else
 void ActionSectorController::SetInvalidRequirement( const Bool invalidRequirement )
 {
	 if ( m_invalidRequirement == invalidRequirement || invalidRequirement == false )
	 {
		 return;
	 }

	 m_invalidRequirement = invalidRequirement;

	 VectorPlayer vecPlayer;
	 m_sector->GetPlayers(vecPlayer );

	 ELoopbackBeginPortalWhenInvalidRequirement* req = NEW ELoopbackBeginPortalWhenInvalidRequirement;
	 EventPtr reqPtr(req);
	 for (EntityPlayer* player : vecPlayer )
	 {		 
		 theZoneHandler.Notify(player, reqPtr );
	 }
 }
#endif

 UInt32 ActionSectorController::GetFieldPointWaitTime()
 {
	 VERIFY_RETURN(m_sector, 0);

	 auto fieldPointSector = m_sector->GetFieldPointSector();
	 VERIFY_RETURN(fieldPointSector, 0);

	 return fieldPointSector->GetWaitTime();
 }

 UInt32 ActionSectorController::GetFieldPointDefenceTime()
 {
	 VERIFY_RETURN(m_sector, 0);

	 auto fieldPointSector = m_sector->GetFieldPointSector();
	 VERIFY_RETURN(fieldPointSector, 0);

	 return fieldPointSector->GetDefenceTime();
 }

 UInt32 ActionSectorController::GetFieldPointLimitTime()
 {
	 VERIFY_RETURN(m_sector, 0);

	 auto fieldPointSector = m_sector->GetFieldPointSector();
	 VERIFY_RETURN(fieldPointSector, 0);

	 return fieldPointSector->GetLimitTime();
 }

 void ActionSectorController::FinishFieldPoint()
 {
	 VERIFY_RETURN(m_sector, );

	 auto fieldPointSector = m_sector->GetFieldPointSector();
	 VALID_RETURN(fieldPointSector, );

	 fieldPointSector->OnFieldPointFinish();
 }

 void ActionSectorController::SetFieldPointResult(Bool result)
 {
	 VERIFY_RETURN(m_sector, );

	 auto fieldPointSector = m_sector->GetFieldPointSector();
	 VALID_RETURN(fieldPointSector, );

	 fieldPointSector->SetResult(result);
 }

 void ActionSectorController::RequestFieldPointReward()
 {
	 VERIFY_RETURN(m_sector, );

	 auto fieldPointSector = m_sector->GetFieldPointSector();
	 VALID_RETURN(fieldPointSector && fieldPointSector->IsEnable(), );
	 
	 VectorPlayer vecPlayer;
	 m_sector->GetPlayers(vecPlayer);
	 for (auto player : vecPlayer)
	 {
		 VALID_DO(player && player->IsValid(), continue);

		 auto req = NEW EReqFieldPointReward;
		 SERVER.SendToWorldServer(player, EventPtr(req));
	 }
 }

 void ActionSectorController::RequestChangeMap(IndexPosition positionIndex)
 {
	 const PositionElem* positionElem = SCRIPTS.GetPositionElem(positionIndex);	 
	 VALID_RETURN(positionElem, );

	 VectorPlayer vecPlayer;
	 m_sector->GetPlayers(vecPlayer);
	 for (EntityPlayer* player : vecPlayer)
	 {
		 EzwReqChangeMap* reqToWorld = NEW EzwReqChangeMap;
		 reqToWorld->toIndexPositionMovable = positionIndex;
		 SERVER.SendChangemapToWorld(player, EzwReqChangeMap::ActionSectorController, EventPtr(reqToWorld));
	 }
 }

 void ActionSectorController::AcceptQuestAllPlayer(IndexQuest questIndex)
 {
	 VERIFY_RETURN(m_sector, );

	 VectorPlayer vecPlayer;
	 m_sector->GetPlayers(vecPlayer);
	 for (auto player : vecPlayer)
	 {
		 VALID_DO(player && player->IsValid(), continue);

		 player->GetQuestAction().AcceptAutoGainQuest( questIndex, static_cast<QuestElem::AUTO_GAIN_IGNORE_MASK>(QuestElem::QC_VOLUME|QuestElem::QC_TALISMAN_LEVEL));
	 }
 }

 void ActionSectorController::FailQuestAllPlayer(IndexQuest questIndex)
 {
	 VERIFY_RETURN(m_sector, );

	 VectorPlayer vecPlayer;
	 m_sector->GetPlayers(vecPlayer);
	 for (const EntityPlayer* entity : vecPlayer)
	 {
		 VALID_DO(entity && entity->IsValid(), continue);
		 entity->GetQuestAction().FailQuest(questIndex);
	 }
 }

 void ActionSectorController::DoCompleteQuestAllPlayer(UInt32 questIndex)
 {
	 VERIFY_RETURN(m_sector, );

	 const QuestElem* elem = SCRIPTS.GetQuest(questIndex);
	 VERIFY_RETURN(elem, );

	 VectorPlayer vecPlayer;
	 m_sector->GetPlayers(vecPlayer);
	 for (const EntityPlayer* player : vecPlayer)
	 {
		 VALID_DO(player && player->IsValid(), continue);
		 player->GetQuestAction().TestFinishQuest(questIndex);
	 }

 }


 UInt16 ActionSectorController::GetInVolumeTotalBuffStack( UInt32 volumeIndex, IndexSkill skillId )
 {
	 UInt16 totalStack = 0;

	 VolumeScript* vscript = GetZoneScript<VolumeScript>( m_sector->GetIndexZone());
	 VALID_RETURN(vscript, false );

	 const VolumeElem* elem = vscript->Get( volumeIndex );
	 VALID_RETURN(elem, false );

	 VectorPlayer vecPlayer;
	 m_sector->GetPlayers( vecPlayer );
	 for ( auto i = vecPlayer.begin(); i != vecPlayer.end(); ++i )
	 {
		 EntityPlayer* player = ( *i );
		 VALID_DO(player && player->IsValid(), continue);		 

		 Float diff = Distance2D(player->GetRealPosition(), elem->pos );
		 if ( diff < elem->radius )
		 {
			 auto foundBuffInfo = player->GetBuffAction().FindBuffInfo( [&] ( const BuffInfoPtr buffInfo )
			 {
				 return buffInfo->infoElem->index == skillId;
			 } );

			 totalStack += ( nullptr == foundBuffInfo ) ? 0 : foundBuffInfo->stack;
		 }
	 }

	 return totalStack;
 }

 void ActionSectorController::SetBroadCastSector()
 {
	 VERIFY_RETURN(m_sector, );

	 m_sector->SetBroadCastSector();
 }

 UInt32 ActionSectorController::GetMissionMapReviveTime()
 {
	 return m_processor->GetMissionMapReviveTime();
 }

 //루아전용
 void ActionSectorController::RequestRegistFootHold(IndexPosition positionIndex)
 {
	 VERIFY_RETURN(m_sector, );

	 VectorPlayer vecPlayer;
	 m_sector->GetPlayers(vecPlayer);
	 for (EntityPlayer* player : vecPlayer)
	 {
		 VALID_DO(player && player->IsValid(), continue);
		 player->RegistFootHold(positionIndex, true);
	 }
 }

 UInt32 ActionSectorController::GetDifficultType()
 {
	 VERIFY_RETURN(m_sector, 0);
	 
	 return m_sector->GetDifficulty();
 }

 // 특정 레이어 몬스터 스폰
 void ActionSectorController::AddSpawnLayer(UInt32 layer)
 {
	 VERIFY_RETURN(m_sector, );

	 VolumeSpawnScript* script = GetZoneScript<VolumeSpawnScript>(m_sector->GetIndexZone());
	 VERIFY_RETURN(script, );
	 
	 const auto& elems = script->GetMap();

	 for (const auto& i : elems)
	 {
		 const VolumeSpawnElem& elem = i.second;
		 VALID_DO(elem.layer == static_cast<Int32>(layer), continue);

		 // 이미 생성된 layer가 있을 경우에는 스폰을 안한다. 
		 EntityControlVolume* spawner = m_sector->FindControlVolumeByCond(
			 [&](const SectorEntityMap::value_type& v)->bool
		 {
			 AttributeVolumeSpawn* attr = GetEntityAttribute(v.second);
			 VALID_RETURN(attr, false);
			 
			 return elem.index == attr->spawnVolumeIndex && elem.layer == static_cast<Int32>(attr->layer);
		 });

		 VALID_DO(NULL == spawner, continue);

		 if (theSpawnSystem.SpawnVolumeSpawn(m_sector, elem, 0, 0) == false)
		 {
			 MU2_WARN_LOG(LogCategory::CONTENTS, "[E] Failed to spawn volume spawn. [zone:%d][index:%d]", m_sector->GetIndexZone(), elem.index);
			 continue;
		 }
		 
		 SpawnVolumeChangeLevel(elem.layer, static_cast<Byte>(m_sector->GetDungeonLevel()));
		 
	 }
 }

 // 특정 레이어의 몬스터 죽임
 void ActionSectorController::KillSpawnLayer(UInt32 layer)
 {
	 VERIFY_RETURN(m_sector, );

	 VectorNpc vecNpc;
	 m_sector->GetNpcs(vecNpc);

	 for (auto& npc : vecNpc)
	 {
		 VALID_DO(npc->IsAlive() && npc->IsValid(), continue)
		 VALID_DO(layer == npc->GetVolumeSpawnLayerIndex(), continue);

		 npc->GetHsm().Tran(EntityState::STATE_NPC_DEAD);
	 }
 }

 // 특정 레이어의 몬스터 디스폰
 void ActionSectorController::DespawnSpawnLayer(UInt32 layer)
 {
	 VERIFY_RETURN(m_sector, );

	 theSpawnSystem.DespawnVolumeSpawn(m_sector, layer);
 }

 void ActionSectorController::ConditionSpawnGroup(IndexSpawnvolume spawnIndex, UInt32 condition)
 {
	 VERIFY_RETURN(m_sector, );
	 VALID_RETURN(spawnIndex, );
	 VALID_RETURN(condition, );

	 EntityControlVolume* spawner = m_sector->FindControlVolumeByCond(
		 [&](const SectorEntityMap::value_type& v)->bool
	 {
		 VALID_RETURN(v.second->HasAttribute(ATTRIBUTE_SPAWN_VOLUME), false);

		 AttributeVolumeSpawn* attr = GetEntityAttribute(v.second);
		 VALID_RETURN(attr, false);
		 VALID_RETURN(attr->spawnVolumeIndex == spawnIndex, false);
		 

		 attr->disabled = false;

		 // 이미 등록되어 있다면, 에러 로그를 남겨야 하나?.... Warning으로 하나 추가 하면 될듯
		 attr->replacedGroupNpcCondition = condition;

		 return true;
	 });

	 spawner;
	
 }

 // 특정 스폰볼륨의 특정 몬스터를 스폰시 컨디션 값 부여 
 void ActionSectorController::ConditionSummonMonster(IndexSpawnvolume spawnIndex, IndexNpc npcIndex, UInt32 condition)
 {
	 VERIFY_RETURN(m_sector, );
	 VALID_RETURN(spawnIndex, );
	 VALID_RETURN(npcIndex, );
	 VALID_RETURN(condition, );


	 EntityControlVolume* spawner = m_sector->FindControlVolumeByCond(
		 [&](const SectorEntityMap::value_type& v)->bool
	 {
		 VALID_RETURN(v.second->HasAttribute(ATTRIBUTE_SPAWN_VOLUME), false);

		 AttributeVolumeSpawn* attr = GetEntityAttribute(v.second);
		 VALID_RETURN(attr, false);
		 VALID_RETURN(attr->spawnVolumeIndex == spawnIndex, false);

		 return true;
	 });

	 VALID_RETURN(spawner, );

	 AttributeVolumeSpawn* attr = GetEntityAttribute(spawner);
	 VALID_RETURN(attr, );

	 // 해당 npc와 condition을 추가
	 attr->replacedNpcConditionDequeMap[npcIndex].push_back(condition);

	 SummonMonsterVolume(spawnIndex, npcIndex, 1);
 }
 void ActionSectorController::NpcRotate(IndexSpawnvolume spawnvolumeIndex, float dir)
 {
	 
	 VectorNpc vecNpc;
	 m_sector->GetNpcs(vecNpc);

	 for (EntityNpc* npc : vecNpc)
	 {
		 VALID_DO(npc && npc->IsValid(), continue);
		 VALID_DO(npc->GetVolumeSpawnIndex() == spawnvolumeIndex, continue);

		 npc->SetDir(dir);
		 npc->GetNpcAction().SendSyncDirection(static_cast<Int32>(npc->GetDir()));
	 }

	 
 }

Bool ActionSectorController::IsEmotionPlayerCountInVolume(UInt32 eventVolumeIndex, ClassType::Enum classType, UInt32 emotionIndex, UInt32 playerCount)
{
	if (0 >= playerCount)
		return true;

	VectorUnit unitList;
	getUnitsInVolume(eventVolumeIndex, EntityTypes::PLAYER_ZONE, OUT unitList);

	UInt32 foundPlayerCount = 0;

	for (auto entity : unitList) {

		auto player = static_cast<EntityPlayer*>(entity);
		ActionPlayer* actionPlayer = GetEntityAction(player);
		if (nullptr != actionPlayer) {

			if (ClassType::COMMON > classType) {
				continue;
			}
			else if(ClassType::COMMON < classType) {
				if (player->IsUsableClass(classType) == false)
					continue;
			}

			if (actionPlayer->GetEmotionIndex() != emotionIndex)
				continue;

			foundPlayerCount++;
		}

		if (foundPlayerCount >= playerCount) {
			return true;
		}
	}

	return false;
}

void ActionSectorController::GiveRewardInVolume( UInt32 eventVolumeIndex, RewardType::Enum rewardType, UInt32 rewardValue)
{
	VectorUnit unitList;
	getUnitsInVolume(eventVolumeIndex, EntityTypes::PLAYER_ZONE, OUT unitList);

	for (auto entity : unitList) {

		auto player = static_cast<EntityPlayer*>(entity);

		// 이모션 동작 통지
		if (RewardType::PlayerEmotion == rewardType) {
			ActionPlayer* actionPlayer = GetEntityAction(player);
			if (nullptr != actionPlayer) {

				actionPlayer->SetEmotionIndex(rewardValue);

				EResGamePlaySocialMotion* res = NEW EResGamePlaySocialMotion;
				res->result = 0;
				res->motionType = rewardValue;
				SERVER.SendToClient(player, EventPtr(res));

				ActionSector* actionSector = GetEntityAction(player);
				if (actionSector == nullptr) {
					continue;
				}

				ENtfGamePlaySocialMotion* ntf = NEW ENtfGamePlaySocialMotion;
				ntf->entityId = player->GetId();
				ntf->motionType = rewardValue;
				actionSector->NearGridCast(EventPtr(ntf));
			}
		}
		// 퀘스트 완료 통지
		else if (RewardType::QuestCompletion == rewardType) {
			ActionPlayerQuest* actionPlayerQuest = GetEntityAction(player);
			if (nullptr != actionPlayerQuest) {
				actionPlayerQuest->FinishQuest(rewardValue);
			}
		}
		// 퀘스트 지급
		else if (RewardType::GiveQuest == rewardType) {
			ActionPlayerQuest* actionPlayerQuest = GetEntityAction(player);
			if (nullptr != actionPlayerQuest) {
				Int32 err = actionPlayerQuest->AcceptQuest(rewardValue);
				if (err != ErrorQuest::SUCCESS) {
					actionPlayerQuest->SendQuestError(player, err);
					MU2_ERROR_LOG(LogCategory::CONTENTS, "[E] Failed to accept Quest !!! - Error:%d, QuestIndex:%d", err, rewardValue);
				}
			}
		}
		else if (RewardType::RequestChanageMap == rewardType) 
		{
			IndexPosition indexPosition4Reward = rewardValue;
						
			const PositionElem* found = SCRIPTS.GetPositionElem(indexPosition4Reward);
			VERIFY_RETURN(found, );

			EzwReqChangeMap* reqToWorld = NEW EzwReqChangeMap;
			reqToWorld->toIndexPositionMovable = indexPosition4Reward;
			SERVER.SendChangemapToWorld(player, EzwReqChangeMap::ActionSectorController, EventPtr(reqToWorld));
		}
	}
}

void ActionSectorController::GiveRewardScene(UInt32 eventVolumeIndex, const char* sceneName)
{
	std::wstring msg;
	StringUtil::Convert(sceneName, msg);
	ENtfGamePlayScene* evt = NEW ENtfGamePlayScene;
	evt->id = msg;
	evt->stop = false;

	EventPtr ntf(evt);

	VectorUnit vecUnit;
	getUnitsInVolume(eventVolumeIndex, EntityTypes::PLAYER_ZONE, vecUnit);

	for (auto entity : vecUnit) {

		auto player = static_cast<EntityPlayer*>(entity);

		SERVER.SendToClient(player, ntf);
	}
}

Bool ActionSectorController::IsCharacterLevel(int compareOperatorType, UInt16 characterLevel)
{
	/*
		equal = 0			// 같음
		greater_equal = 1	// 이상
		less_equal = 2		// 이하
		greater = 3			// 초과
		less = 4			// 미만
	*/

	VectorPlayer vecPlayer;
	m_sector->GetPlayers(vecPlayer);

	for (auto i = vecPlayer.begin(); i != vecPlayer.end(); ++i) {
		EntityPlayer* entity = (*i);
		if (entity == nullptr) {
			continue;
		}

		// equal
		if (0 == compareOperatorType) {
			if (entity->GetLevel() == characterLevel) {
				return true;
			}
		}
		// greater_equal
		else if (1 == compareOperatorType) {
			if (entity->GetLevel() >= characterLevel) {
				return true;
			}
		}
		// less_equal
		else if (2 == compareOperatorType) {
			if (entity->GetLevel() <= characterLevel) {
				return true;
			}
		}
		// greater
		else if (3 == compareOperatorType) {
			if (entity->GetLevel() > characterLevel) {
				return true;
			}
		}
		// less
		else if (4 == compareOperatorType) {
			if (entity->GetLevel() < characterLevel) {
				return true;
			}
		}
		else return false;
	}

	return false;
}


#ifdef __Patch_SectorScript_Kill_Monster_Event_Modify_by_cheolhoon_20181105
void ActionSectorController::KillMonsterInVolume(int eventVolumeIndex, int killConditionType, int clearDamage
	, IndexNpc npcIndex1, IndexNpc npcIndex2, IndexNpc npcIndex3, IndexNpc npcIndex4, IndexNpc npcIndex5)
#else
	void ActionSectorController::KillMonsterInVolume(int eventVolumeIndex, int killConditionType
		, IndexNpc npcIndex1, IndexNpc npcIndex2, IndexNpc npcIndex3, IndexNpc npcIndex4, IndexNpc npcIndex5)
#endif
{
	VectorUnit unitList;
	getUnitsInVolume(eventVolumeIndex, EntityTypes::NPC, OUT unitList);

	for (auto entity : unitList) {

		auto npc = static_cast<EntityNpc*>(entity);

		VALID_DO(npc->IsAlive() && npc->IsValid(), continue);

		bool isDead = false;

		if (0 == killConditionType) {
			isDead = true;
		}
		else if (1 == killConditionType) {
			if (   ( 0 != npcIndex1 && npc->GetNpcIndex() == npcIndex1 )
				|| ( 0 != npcIndex2 && npc->GetNpcIndex() == npcIndex2 )
				|| ( 0 != npcIndex3 && npc->GetNpcIndex() == npcIndex3 )
				|| ( 0 != npcIndex4 && npc->GetNpcIndex() == npcIndex4 )
				|| ( 0 != npcIndex5 && npc->GetNpcIndex() == npcIndex5 )
			) {
				isDead = true;
			}
		}
		else if (2 == killConditionType) {
			if (   ( 0 != npcIndex1 && npc->GetNpcIndex() != npcIndex1 )
				&& ( 0 != npcIndex2 && npc->GetNpcIndex() != npcIndex2 )
				&& ( 0 != npcIndex3 && npc->GetNpcIndex() != npcIndex3 )
				&& ( 0 != npcIndex4 && npc->GetNpcIndex() != npcIndex4 )
				&& ( 0 != npcIndex5 && npc->GetNpcIndex() != npcIndex5 )
				) {
				isDead = true;
			}
		}

		if (true == isDead) {
			
			if (m_processor)
			{
				OhrdorSewerageProcessor* ohrdorSewerageProcessor = dynamic_cast<OhrdorSewerageProcessor*>(m_processor);
				if (ohrdorSewerageProcessor)
				{
					npc->GetNpcAction().SetIsGiveExp(false);
					ohrdorSewerageProcessor->AddInvasionNpc(npc->GetId());
				}
			}

#ifdef __Patch_SectorScript_Kill_Monster_Event_Modify_by_cheolhoon_20181105
			if(0 != clearDamage) // 0이 아닌 경우 유저 퀘스트나 드랍에 영향을 주지 않음.
			{
				npc->GetDamageAction().ClearDamaged();
			}
#endif
			npc->GetHsm().Tran(EntityState::STATE_NPC_DEAD);
		}
	}
}

//오르도르 하수구에서 요청한 침입카운트 이상일 경우  성공으로 전달
Bool ActionSectorController::InvadeCount(const UInt32 invadeCount)
{
	VERIFY_RETURN(m_processor, false);
	
	OhrdorSewerageProcessor* ohrdorSewerageProcessor = dynamic_cast<OhrdorSewerageProcessor*>(m_processor);
	VALID_RETURN(ohrdorSewerageProcessor, false);

	UInt32 count = ohrdorSewerageProcessor->GetInvasionCount();
	if (count >= invadeCount)
	{
		return true;
	}
				
	return false;
}

//오르도르 하수구에서 요청한 타이머가 남은시간 이하일 경우 성공으로 전달.
Bool ActionSectorController::OhrdorSewerageTimer(const UInt32 checkTimer)
{
	VERIFY_RETURN(m_processor, false);

	OhrdorSewerageProcessor* ohrdorSewerageProcessor = dynamic_cast<OhrdorSewerageProcessor*>(m_processor);
	VALID_RETURN(ohrdorSewerageProcessor, false);

	if (ohrdorSewerageProcessor->GetRemainTime() < checkTimer)
	{
		return true;
	}

	return false;
}

IndexZone ActionSectorController::GetCurrentZoneIndex()
{
	VERIFY_RETURN(m_sector, 0);

	return m_sector->GetIndexZone();
}

#ifdef __Patch_Tower_of_dawn_eunseok_2018_11_05
void ActionSectorController::OnTowerOfDawnUsePlayChance(EntityPlayer* entity, Int32 chanceIndex)
{
	m_processor->onTowerOfDawnUsePlayChance(entity, chanceIndex);
}
#endif // __Patch_Tower_of_dawn_eunseok_2018_11_05


#ifdef __Patch_Lua_InstanceGroup_Param_by_jason_20190109
InstanceSectorGroupEx* ActionSectorController::getInstanceSectorGroup()
{
	Sector* sector = GetSector();
	VERIFY_RETURN(sector, NULL);
	VERIFY_RETURN(sector->GetInstanceGroupKey(), NULL);

	SectorRunner* runner = sector->GetSectorRunner();
	VERIFY_RETURN(runner, NULL);

	return runner->GetInstanceGroupManager().Find(sector);
}

void ActionSectorController::SetValueByKey4InsGroup(const char* key, InstanceSectorGroupEx::GroupValue valueOfKey)
{
	InstanceSectorGroupEx* found = getInstanceSectorGroup();
	VALID_RETURN(found, );
	found->SetValueByKey(key, valueOfKey);
}

InstanceSectorGroupEx::GroupValue	ActionSectorController::GetValueByKey4InsGroup(const char* key)
{
	InstanceSectorGroupEx* found = getInstanceSectorGroup();
	VALID_RETURN(found, InstanceSectorGroupEx::InvalidGroupValue);
	return found->GetValueByKey(key);
}

void ActionSectorController::DelValueByKey4InsGroup(const char* key)
{
	InstanceSectorGroupEx* found = getInstanceSectorGroup();
	VALID_RETURN(found, );
	found->DelValueByKey(key);
}

Bool ActionSectorController::IsExistValueByKey4InsGroup(const char* key)
{
	InstanceSectorGroupEx* found = getInstanceSectorGroup();
	VALID_RETURN(found, false);
	return found->IsExistValueByKey(key);
}

void ActionSectorController::IncValueByKey4InsGroup(const char* key, InstanceSectorGroupEx::GroupValue delta)
{
	InstanceSectorGroupEx* found = getInstanceSectorGroup();
	VALID_RETURN(found, );
	found->IncValueByKey(key, delta);
}

void ActionSectorController::DecValueByKey4InsGroup(const char* key, InstanceSectorGroupEx::GroupValue delta)
{
	InstanceSectorGroupEx* found = getInstanceSectorGroup();
	VALID_RETURN(found, );
	found->DecValueByKey(key, delta);
}

void ActionSectorController::ClearAllValuesInsGroup()
{
	InstanceSectorGroupEx* found = getInstanceSectorGroup();
	VALID_RETURN(found, );
	found->ClearAllValues();
}
#endif
} // namespace mu2
