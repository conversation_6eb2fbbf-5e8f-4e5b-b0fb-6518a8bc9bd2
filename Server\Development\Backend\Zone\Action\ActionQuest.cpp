﻿#include "stdafx.h"
#include <Backend/Zone/Action/ActionQuest.h>
#include <Backend/Zone/Action/ActionAchievement.h>
#include <Backend/Zone/Action/ActionMissionHero.h>
#include <Backend/Zone/Action/ActionParty.h>
#include <Backend/Zone/Action/ActionPlayer.h>
#include <Backend/Zone/Action/ActionPlayerInventory.h>

#include <Backend/Zone/Action/ActionNpc.h>
#include <Backend/Zone/Action/ActionPlayer/ActionSkillManage.h>
#include <Backend/Zone/Action/ActionPassivity.h>
#include <Backend/Zone/Action/ActionPlayerScript.h>
#include <Backend/Zone/Action/ActionTutorial.h>
#include <Backend/Zone/Action/ActionAchievement.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerSurvey.h>

#include <Backend/Zone/Action/ActionArtifact.h>
#include <Backend/Zone/Action/ActionPlayer/ActionContact.h>
#include <Backend/Zone/Action/ActionPlayerMailBox.h>

#include <Backend/Zone/SectorQuestMissionDecoratorManager.h>

#include <Backend/Zone/System/QuestSystem.h>

#include <Backend/Zone/System/LogicEffectSystem.h>

#include <Backend/Zone/System/GameEventSystem.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerEvent.h>

#include <Backend/Zone/Action/ActionPlayer/ActionPlayerSeason.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerTalisman.h>

#include <Backend/Zone/Element/Item/InvenNonSlotable.h>
#include <Backend/Zone/ZoneServer.h>

#ifdef __Reincarnation__jason_180628__
#include <Backend/Zone/System/NotifierGameContents.h>
#endif

namespace mu2
{

template <typename EVENT> 
void notifyToSharedSystem( ActionPlayerQuest* aq, Mission& info, EVENT& qe, const QuestElem* elem )
{
	// 범위 타잎이고 내가 참가한 퀘스트일 경우
	if ( elem && elem->IsSharedQuest() && qe.activator )
	{
		EVENT nqe = qe; 

		nqe.questId			= info.questId;
		nqe.missionType		= info.mission->type;
		nqe.missionIndex 	= info.mission->index;
		nqe.missionValue 	= info.missionValue;
		nqe.activator		= false;

		aq->GetSharedQuestSystem()->OnQuestEvent( nqe );
	}
}

//---------------------------------------------------------------------------------------------------------------
// ActionQuest - Pimpl
//---------------------------------------------------------------------------------------------------------------
struct ActionPlayerQuest::ActionQuestImpl
{
	QuestFinishMap						finishedQuests;
	QuestGiveupMap						giveupQuests;
	QuestMap							quests;
	QuestReserveSet						reservedQuests;
	QuestMissionTypeList				questMissionList;					// 퀘스트 미션 타입별 수행중인 퀘스트 정보
	QuestRenewalCondSet					renewalCondQuestSet;				// 현재 섹터에서 한번만 실행되어야 하는 퀘스트들
	QuestMissionHeroEntityIdMap			misisonHeroEntityIdMap;
	QuestMissionDecoratorMap			questMissionDecoratorMap;
#ifdef __Patch_AutoGain_Quest_Check_by_cheolhoon_181008
	QuestAutoGainCheckMap				questAutoGainCheckMap;				// 자동획득 퀘스트 체크. 받지 못한 자동획득 연계퀘스트. 로그인, 존 이동 시 획득용.
#endif
#ifdef __Patch_Quest_Over_MaxCount_by_cheolhoon_181025
	QuestWaitSet						questWaitSet;						// 퀘스트 대기열.
#endif

	UInt32								lastRandomQuest;					// 마지막으로 수행한 랜덤퀘스트..
	EntityId							npcTalking;							// 대화 중 NPC

	UInt32								lastCompletedQuest;					// 마지막 수행 퀘스트 
	
	QuestOccurItem						usingQuestOccurItem;				// 현재 사용중인 퀘스트 발주 아이템

	SimpleTimer							autoGainTimer;
	SimpleTimer 						defaultTickTimer;
	SimpleTimer							renewalTimer;
	UInt64								renewalTick;

	std::set<UInt32>					finishQuestsInThisMap;
	QuestEvent							lastEvent;
	std::deque<ReqAutoGainQuest>		autoGainQuests;						// iterator 돌면서 퀘스트 추가를 피하기 위해

	CharAdvantureInfo					advantureInfo;			// 탐험 수첩 진행 정보

	QuestProcessorProcessorList			questProcessorList;					// 퀘스트 타입별 실제 처리부 리스트
	Int32								errorParam;
	ActionQuestImpl()
		: lastRandomQuest(0)
		, lastCompletedQuest(0)
		, renewalTick(0)
		, errorParam(0)
	{
		CounterInc( "ActionQuestImpl" );
	}
	~ActionQuestImpl()
	{
		CounterDec( "ActionQuestImpl" );
	}
};

//---------------------------------------------------------------------------------------------------------------
// ActionQuest
//---------------------------------------------------------------------------------------------------------------
ActionPlayerQuest::ActionPlayerQuest(EntityPlayer* owner )
	: Action4Player( owner, ID )
, m_impl( std::make_unique<ActionQuestImpl>() )
{
	CounterInc("ActionQuest");
	initialize();
}

ActionPlayerQuest::~ActionPlayerQuest()
{ 
	CounterDec("ActionQuest");
}


Bool ActionPlayerQuest::SetupProgressQuestFromDB( ProgressQuestVector& progressQuestVector )
{
	EntityPlayer* player = GetOwnerPlayer();
	clearQuests();

	for ( auto it = progressQuestVector.begin(); it != progressQuestVector.end(); )
	{
		const QuestElem* quest = SCRIPTS.GetQuest( it->questId );

		if ( quest == nullptr )
		{
			theGameMsg.SendGameDebugMsg( player, L"SetupProgressQuestFromDB - %d 존재하지 않는 퀘스트.\n", it->questId );
			++it;
			continue;
		}

#ifdef __Patch_Quest_ProgressType_Add_Period_by_cheolhoon_20181114
		if(quest->progress_Type == QuestProgressType::Period)	// 기간제 퀘스트의 경우 기간을 확인.
		{
			if(theQuestSystem.CheckPeriodQuestTimeOver(it->lastUpdateTime))
			{
				// 기간 초과시 삭제.
				DeleteQuest(it->questId);
				it = progressQuestVector.erase(it);

				continue;
			}
		}
#endif

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
		if(false == SCRIPTS.GetScript<QuestScheduleScript>()->CheckPossibleQuestTime(static_cast<IndexQuest>(it->questId)))
		{
			// 기간 초과시 삭제.
			DeleteQuest(it->questId);
			it = progressQuestVector.erase(it);

			continue;
		}
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313

		addProgressQuest( *it );

#ifdef __Patch_AutoGain_Quest_Check_by_cheolhoon_181008
		if(0 != quest->auto_gain_quest_id)
		{
			if(m_impl->questAutoGainCheckMap.end() == m_impl->questAutoGainCheckMap.find(quest->auto_gain_quest_id))
			{
				m_impl->questAutoGainCheckMap.insert(make_pair(quest->auto_gain_quest_id, quest->index));
			}
		}
#endif

		if ( it->progressFlag == QuestFlag::COMPLETE && quest->complete_Type == QuestCompleteType::ByItself )
		{
			rewardQuestByItself( it->questId );
			it = progressQuestVector.erase( it );
		}
		else
		{
			++it;
		}
		
	}

	return true;
}

Bool ActionPlayerQuest::SetupFinishedQuestFromDB(FinishedQuestVector& finishQuestVector)
{
	EntityPlayer* player = GetOwnerPlayer();
#ifdef __Patch_Quest_ProgressType_Add_Period_by_cheolhoon_20181114
	for(auto i = finishQuestVector.begin(); i != finishQuestVector.end(); )
	{
		const QuestElem* elem = SCRIPTS.GetQuest(i->questId);
		if(nullptr == elem)
		{
			theGameMsg.SendGameDebugMsg(player, L"SetupFinishQuestFromDB - %d 존재하지 않는 퀘스트.\n", i->questId);
			++i;
			continue;
		}

		if(elem->progress_Type == QuestProgressType::Period)	// 기간제 퀘스트의 경우 기간을 확인.
		{
			if(theQuestSystem.CheckPeriodQuestTimeOver(i->lastUpdateTime))
			{
				// 기간 초과시 삭제.
				DeleteQuest(elem->index);
				i = finishQuestVector.erase(i);

				continue;
			}
		}

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
		if(false == SCRIPTS.GetScript<QuestScheduleScript>()->CheckPossibleQuestTime(static_cast<IndexQuest>(i->questId)))
		{
			// 기간 초과시 삭제.
			DeleteQuest(elem->index);
			i = finishQuestVector.erase(i);

			continue;
		}
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313

		m_impl->finishedQuests.emplace(i->questId, *i);
#ifdef __Patch_AutoGain_Quest_Check_by_cheolhoon_181008
		if(0 != elem->auto_gain_quest_id)
		{
			if(m_impl->questAutoGainCheckMap.end() == m_impl->questAutoGainCheckMap.find(elem->auto_gain_quest_id))
			{
				m_impl->questAutoGainCheckMap.insert(make_pair(elem->auto_gain_quest_id, elem->index));
			}
		}
#endif
		++i;
	}
#else
	for(const auto& i : finishQuestVector)
	{
		const QuestElem* elem = SCRIPTS.GetQuest(i.questId);
		if(nullptr == elem)
		{
			theGameMsg.SendGameDebugMsg(player, L"SetupFinishQuestFromDB - %d 존재하지 않는 퀘스트.\n", i.questId);
			continue;
		}

		m_impl->finishedQuests.emplace(i.questId, i);
#ifdef __Patch_AutoGain_Quest_Check_by_cheolhoon_181008
		if(0 != elem->auto_gain_quest_id)
		{
			if(m_impl->questAutoGainCheckMap.end() == m_impl->questAutoGainCheckMap.find(elem->auto_gain_quest_id))
			{
				m_impl->questAutoGainCheckMap.insert(make_pair(elem->auto_gain_quest_id, elem->index));
			}
		}
#endif
	}
#endif

	return true;	
}

Bool ActionPlayerQuest::SetupReserveQuestFromDB( ReservedQuestVector& reserveQuestVector )
{
	EntityPlayer* player = GetOwnerPlayer();
	size_t size = reserveQuestVector.size();

	for ( UInt32 loop = 0; loop < size ; ++loop )
	{
		const QuestElem* quest = SCRIPTS.GetQuest( reserveQuestVector[loop] );
		if ( quest == NULL )
		{
			theGameMsg.SendGameDebugMsg( player, L"SetupReserveQuestFromDB - %d 존재하지 않는 퀘스트.\n", reserveQuestVector[loop] );			
			continue;
		}

		m_impl->reservedQuests.insert( reserveQuestVector[loop] );
	}

	return true;
}

Bool ActionPlayerQuest::SetupGiveupQuestFromDB( GiveupQuestInfoList& giveupQuestList )
{
	EntityPlayer* player = GetOwnerPlayer();
	for( const auto& i : giveupQuestList )
	{
		const QuestElem* elem = SCRIPTS.GetQuest( i.questId );
		if( nullptr == elem )
		{
			theGameMsg.SendGameDebugMsg( player, L"SetupReserveQuestFromDB - %d 존재하지 않는 퀘스트.\n", i.questId );
			continue;
		}

		setupGiveupQuest( elem, i );
	}

	
	return true;
}



	Bool ActionPlayerQuest::SetupAdvantureNoteFromDB(CharAdvantureInfo& advInfo)
	{
#ifdef __Hotfix_AdvantureReward_by_ray_180920
		// 이벤트가 진행중일때만 보내자.
		if (false == theEventSystem.IsActiveEventType(GameEventElem::ADVANTURE_NOTE))
			return true;
#endif

		m_impl->advantureInfo = advInfo;

#ifdef __Hotfix_Adventure_Quest_Process_Sync_Check_by_ch_2018_12_27
		Bool aAdventureUpdateFlag = false;
#endif
		if (StampState::NONE == advInfo.pageRewardState)
		{
			if (0 == advInfo.mission1Complete)
			{
#ifdef __Hotfix_Adventure_Quest_Process_Sync_Check_by_ch_2018_12_27
				if (advInfo.mission1Index && false == IsProgressQuest(advInfo.mission1Index))
				{
					if(ErrorQuest::E_QUEST_FINISH == AcceptQuest(advInfo.mission1Index))
					{
						// 완료된 경우 탐험수첩 정보 갱신.
						m_impl->advantureInfo.mission1Complete  = EventMissionComplete::COMPLETE;
						aAdventureUpdateFlag = true;
					}
				}
#else
				if (advInfo.mission1Index && false == IsProgressQuest(advInfo.mission1Index))
					AcceptQuest(advInfo.mission1Index);
#endif
			}

			if (0 == advInfo.mission2Complete)
			{
#ifdef __Hotfix_Adventure_Quest_Process_Sync_Check_by_ch_2018_12_27
				if (advInfo.mission2Index && false == IsProgressQuest(advInfo.mission2Index))
				{
					if (ErrorQuest::E_QUEST_FINISH == AcceptQuest(advInfo.mission2Index))
					{
						// 완료된 경우 탐험수첩 정보 갱신.
						m_impl->advantureInfo.mission2Complete = EventMissionComplete::COMPLETE;
						aAdventureUpdateFlag = true;
					}
				}
#else
				if (advInfo.mission2Index && false == IsProgressQuest(advInfo.mission2Index))
					AcceptQuest(advInfo.mission2Index);
#endif

			}

		}

#ifdef __Hotfix_Adventure_Quest_Process_Sync_Check_by_ch_2018_12_27
		if(true == aAdventureUpdateFlag)
		{
			if(EventMissionComplete::COMPLETE == m_impl->advantureInfo.mission1Complete && EventMissionComplete::COMPLETE == m_impl->advantureInfo.mission2Complete)
			{
				m_impl->advantureInfo.pageRewardState = StampState::UNRECIEVE;
			}

			UpdateAdvantureInfo();
		}
#endif

		ENtfAdvantureHaveReward * ntf = NEW ENtfAdvantureHaveReward;
		EventPtr ptr(ntf);
		
		if (StampState::UNRECIEVE == advInfo.pageRewardState && 0 != theEventSystem.GetAdventureEvent())
			ntf->haveReward = true;
		else
			ntf->haveReward = false;

		SERVER.SendToClient(GetOwnerPlayer(), ptr);

		return true;
	}

	void ActionPlayerQuest::GMFinishAdvantureCurPage()
	{
		EntityPlayer * player = GetOwnerPlayer();
		VERIFY_RETURN(player && player->IsAlive(), );

		ENtfAdvantureMissionComplete * completePack = NEW ENtfAdvantureMissionComplete;
		EventPtr ptr(completePack);
		completePack->advanturePageIndex = GetAdvantureCurPage();


		CharAdvantureInfo &ainfo = m_impl->advantureInfo;
		
		if(EventMissionComplete::INCOMPLETE == ainfo.mission1Complete )
		{
			SendFinishQuest(player, ainfo.mission1Index);

			ainfo.mission1Complete = EventMissionComplete::COMPLETE;
			completePack->advantureMissionIndex = ainfo.mission1Index;
			
			EReqLogDb2* log = NEW EReqLogDb2;
			EventPtr logEventPtr(log);
			EventSetter::FillLogForEntity(LogCode::E_ADVANTURE_MISSION_COMPLETE, player, log->action);

			log->action.groupKey = player->GetSectorAction().GetDungeonLogKey();
			log->action.var32s.push_back(GetAdvantureCurReport());
			log->action.var32s.push_back(GetAdvantureCurPage());
			log->action.var32s.push_back(ainfo.mission1Complete);
			log->action.var32s.push_back(0);

			SendDataCenter(logEventPtr);

			SERVER.SendToClient(GetOwnerPlayer(), ptr);

		}

		if (EventMissionComplete::INCOMPLETE == ainfo.mission2Complete)
		{
			SendFinishQuest(player, ainfo.mission2Index);

			ainfo.mission2Complete = EventMissionComplete::COMPLETE;
			completePack->advantureMissionIndex = ainfo.mission2Index;

			EReqLogDb2* log = NEW EReqLogDb2;
			EventPtr logEventPtr(log);
			EventSetter::FillLogForEntity(LogCode::E_ADVANTURE_MISSION_COMPLETE, player, log->action);

			log->action.groupKey = player->GetSectorAction().GetDungeonLogKey();
			log->action.var32s.push_back(GetAdvantureCurReport());
			log->action.var32s.push_back(GetAdvantureCurPage());
			log->action.var32s.push_back(ainfo.mission2Complete);
			log->action.var32s.push_back(0);

			SendDataCenter(logEventPtr);
		}

		ainfo.pageRewardState = StampState::UNRECIEVE;

		completePack->pageComplete = true;

		EReqLogDb2* log = NEW EReqLogDb2;
		EventPtr logEventPtr(log);
		EventSetter::FillLogForEntity(LogCode::E_ADVANTURE_PAGE_COMPLETE, player, log->action);

		log->action.groupKey = player->GetSectorAction().GetDungeonLogKey();
		log->action.var32s.push_back(GetAdvantureCurReport());
		log->action.var32s.push_back(GetAdvantureCurPage());
		
		SendDataCenter(logEventPtr);

		UpdateAdvantureInfo();

		SERVER.SendToClient(GetOwnerPlayer(), ptr);
	}
	

	void ActionPlayerQuest::GMSetAdvantureState(UInt16 ri, UInt16 pi)
	{
		ENtfAdvantureInfo * info = NEW ENtfAdvantureInfo;
		EventPtr pack(info);

		EntityPlayer* player = GetOwnerPlayer();
		VERIFY_RETURN(player && player->IsValid(), );
		
		Index32 aeid = theEventSystem.GetAdventureEvent();
		const auto elem = SCRIPTS.GetGameEventElem(aeid);
		if (nullptr == elem)
		{
			SERVER.SendToClient(player, pack);
			return;
		}

		info->advantureEventId = aeid;
		if (player->GetEventAction().checkDate(elem->endDate))
		{
			SERVER.SendToClient(player, pack);
			return;
		}

		info->advatureActiveState = 1;

		CharAdvantureInfo &advinfo = m_impl->advantureInfo;

		if (advinfo.mission1Index)
		{
			SendFinishQuest(player, advinfo.mission1Index);
			DeleteQuest(advinfo.mission1Index);
		}
		if (advinfo.mission2Index)
		{
			SendFinishQuest(player, advinfo.mission2Index);
			DeleteQuest(advinfo.mission2Index);
		}


		if (ri > elem->lastReportIndex)
			ri = elem->lastReportIndex;
		advinfo.reportIndex = ri;

		
		const auto reportInfo = elem->GetAdvReport(advinfo.reportIndex);
		if (nullptr == reportInfo)
		{
			info->advatureActiveState = 0;
			SERVER.SendToClient(player, pack);
			return;
		}

		if (pi > reportInfo->lastPage)
			pi = reportInfo->lastPage;
		advinfo.pageIndex = pi;

		const auto pageInfo = reportInfo->GetPageInfo(advinfo.pageIndex);
		if (nullptr == pageInfo)
		{
			info->advatureActiveState = 0;
			SERVER.SendToClient(player, pack);
			return;
		}
		if (pageInfo->missions.size() < 2)
		{
			info->advatureActiveState = 0;
			SERVER.SendToClient(player, pack);
			return;
		}


		advinfo.pageRewardState = StampState::NONE;
		std::vector<UInt32> missions;
		pageInfo->GetMissions(missions);
		advinfo.mission1Index = missions.at(0);
		advinfo.mission2Index = missions.at(1);
		advinfo.mission1Complete = EventMissionComplete::INCOMPLETE;
		advinfo.mission2Complete = EventMissionComplete::INCOMPLETE;

		//! 혹시 예전에 같은 미션이 있었다면, 퀘스트를 초기화해야 한다.
		ResetQuest(advinfo.mission1Index);
		AcceptQuest(advinfo.mission1Index);

		ResetQuest(advinfo.mission2Index);
		AcceptQuest(advinfo.mission2Index);


		// DB 갱신 요청
		UpdateAdvantureInfo();

		info->advantureData = advinfo;

		SERVER.SendToClient(player, pack);

	}

#ifdef __Patch_Quest_Over_MaxCount_by_cheolhoon_181025
	Bool ActionPlayerQuest::SetupWaitQuestFromDB(WaitQuestVector& waitQuestVector)
	{
		for(const auto& i : waitQuestVector)
		{
			const QuestElem* elem = SCRIPTS.GetQuest(i.questId);
			if(nullptr == elem)
			{
				MU2_WARN_LOG(core::LogCategory::DEFAULT, "SetupWaitQuestFromDB> can't find quest [questId:%d] %s", i.questId);
				continue;
			}

			QuestWaitInfo info;
			info.questId = i.questId;
			info.questStoryPriority = QuestStoryPriorityType::ToQuestStoryPriorityType(elem->story_type);
			info.registTime = i.lastUpdateTime;

			auto it = std::find(m_impl->questWaitSet.begin(), m_impl->questWaitSet.end(), info);
			if(m_impl->questWaitSet.end() == it)
			{
				m_impl->questWaitSet.insert(info);
			}
		}

		return true;
	}
#endif

void ActionPlayerQuest::initialize()
{
	// init
	std::fill( m_impl->questProcessorList.begin(), m_impl->questProcessorList.end(), nullptr );

	// quest processor
	m_impl->questProcessorList[QuestEventType::KILL_MONSTER]			= &ActionPlayerQuest::onKillMonster;
	m_impl->questProcessorList[QuestEventType::VOLUME_IN]				= &ActionPlayerQuest::onVolumeIn;
	m_impl->questProcessorList[QuestEventType::DIALOG_NPC]				= &ActionPlayerQuest::onDialogNpc;
	m_impl->questProcessorList[QuestEventType::DELIVER_ITEM]			= &ActionPlayerQuest::onDeliverItem;
	m_impl->questProcessorList[QuestEventType::DROP_ITEM]				= &ActionPlayerQuest::onDropItem;
	m_impl->questProcessorList[QuestEventType::USE_ITEM]				= &ActionPlayerQuest::onUseItem;
	m_impl->questProcessorList[QuestEventType::EXCHANGE_ITEM]			= &ActionPlayerQuest::onExchangeItem;
	m_impl->questProcessorList[QuestEventType::DESTROY_OBJECT]			= &ActionPlayerQuest::onDestroyObj;
	m_impl->questProcessorList[QuestEventType::COLLECT_ITEM]			= &ActionPlayerQuest::onCollectItem;
	m_impl->questProcessorList[QuestEventType::ADV_MISSION]				= &ActionPlayerQuest::onAdvMission;
	m_impl->questProcessorList[QuestEventType::OPERATE_OBJECT]			= &ActionPlayerQuest::onOperateObject;
	m_impl->questProcessorList[QuestEventType::DEADLINE_OVER]			= &ActionPlayerQuest::onDeadlineOver;
	m_impl->questProcessorList[QuestEventType::ADV_TYPE]				= &ActionPlayerQuest::onAdvType;
	m_impl->questProcessorList[QuestEventType::ADV_FLOOR]				= &ActionPlayerQuest::onAdvFloor;
	m_impl->questProcessorList[QuestEventType::ADV_KILL_MONSTER]		= &ActionPlayerQuest::onAdvKillMonster;
	m_impl->questProcessorList[QuestEventType::TARGET_NPC]				= &ActionPlayerQuest::onVolumeIn;
	m_impl->questProcessorList[QuestEventType::ACCEPT_QUEST]			= &ActionPlayerQuest::onAcceptQuest;
	m_impl->questProcessorList[QuestEventType::COMPLETE_QUEST]			= &ActionPlayerQuest::onCompleteQuest;
	m_impl->questProcessorList[QuestEventType::FINISH_QUEST]			= &ActionPlayerQuest::onFinishQuest;
	m_impl->questProcessorList[QuestEventType::CASTLE_KILL_MONSTER]		= &ActionPlayerQuest::onCastleKillMonster;
	m_impl->questProcessorList[QuestEventType::CASTLE_CLEAR]			= &ActionPlayerQuest::onCastleClear;
	m_impl->questProcessorList[QuestEventType::TOWER_STAGE_CLEAR]		= &ActionPlayerQuest::onTowerStageClear;
	m_impl->questProcessorList[QuestEventType::TOWER_KILL_MONSTER]		= &ActionPlayerQuest::onTowerKillMonster;
	m_impl->questProcessorList[QuestEventType::TOWER_STAGE_PASS]		= &ActionPlayerQuest::onTowerStagePass;
	m_impl->questProcessorList[QuestEventType::COLLECT_ITEM_B]			= &ActionPlayerQuest::onCollectItemB;
	m_impl->questProcessorList[QuestEventType::OBJECT_COLLECT_ITEM]		= &ActionPlayerQuest::onObjectCollectItem;
	m_impl->questProcessorList[QuestEventType::PROTECT_NPC]				= &ActionPlayerQuest::onProtectNpc;
	m_impl->questProcessorList[QuestEventType::ESCORT_NPC_A]			= &ActionPlayerQuest::onEscortNpcA;
	m_impl->questProcessorList[QuestEventType::ESCORT_NPC_B]			= &ActionPlayerQuest::onEscortNpcB;
	m_impl->questProcessorList[QuestEventType::SURVIVAL]				= &ActionPlayerQuest::onSurvival;
	m_impl->questProcessorList[QuestEventType::ITEM_CREATE]				= &ActionPlayerQuest::onItemCreate;
	m_impl->questProcessorList[QuestEventType::ITEM_EXTRACT]			= &ActionPlayerQuest::onItemExtract;
	m_impl->questProcessorList[QuestEventType::ITEM_SOCKET_ADD]			= &ActionPlayerQuest::onItemSocketAdd;
	m_impl->questProcessorList[QuestEventType::ITEM_SOCKET_EQUIP]		= &ActionPlayerQuest::onItemSocketEquip;
	m_impl->questProcessorList[QuestEventType::MAZE_CLEAR]				= &ActionPlayerQuest::onMazeClear;
	m_impl->questProcessorList[QuestEventType::AURA_KILL]				= &ActionPlayerQuest::onAuraKill;
	m_impl->questProcessorList[QuestEventType::ARTIFACT_LEVEL]			= &ActionPlayerQuest::onArtifactLevel;
	m_impl->questProcessorList[QuestEventType::ARTIFACT_CREATE]			= &ActionPlayerQuest::onArtifactCreate;
	m_impl->questProcessorList[QuestEventType::ARTIFACT_USE]			= &ActionPlayerQuest::onArtifactUse;
	m_impl->questProcessorList[QuestEventType::NETHERWORLD_KILL_MONSTER] = &ActionPlayerQuest::onNetherWorldKillMonster;
	m_impl->questProcessorList[QuestEventType::NETHERWORLD_CLEAR]		= &ActionPlayerQuest::onNetherWorldClear;
	m_impl->questProcessorList[QuestEventType::ADV_COLLECT_ITEM]		= &ActionPlayerQuest::onAdvCollectItem;
	m_impl->questProcessorList[QuestEventType::ZONE_KILL_MONSTER]		= &ActionPlayerQuest::onZoneKillMonster;
	m_impl->questProcessorList[QuestEventType::ZONE_COLLECT_ITEM]		= &ActionPlayerQuest::onZoneCollectItem;
	m_impl->questProcessorList[QuestEventType::CHARACTER_LEVEL]			= &ActionPlayerQuest::onCharacterLevel;
	m_impl->questProcessorList[QuestEventType::LAST_HIT_MONSTER]		= &ActionPlayerQuest::onLastHitMonster;
	m_impl->questProcessorList[QuestEventType::PLAYER_KILL]				= &ActionPlayerQuest::onPlayerKill;

#ifdef ABILITY_RENEWAL_20180508
	m_impl->questProcessorList[QuestEventType::OPEN_BRACELET]			= &ActionPlayerQuest::onOpenBracelet;
#endif

	m_impl->questProcessorList[QuestEventType::ADV_COLLECT_TYPE]		= &ActionPlayerQuest::onAdvCollectType;
	m_impl->questProcessorList[QuestEventType::DIFFICULTY_COLLECT_ITEM]	= &ActionPlayerQuest::onDifficultyCollectItem;

#ifdef __Patch_Tower_of_dawn_eunseok_2018_11_05
	m_impl->questProcessorList[QuestEventType::DAWN_TOWER_STAGE_CLEAR] = &ActionPlayerQuest::onDawnTowerStageClear;
	m_impl->questProcessorList[QuestEventType::DAWN_TOWER_STAGE_PASS] = &ActionPlayerQuest::onDawnTowerStagePass;
#endif //__Patch_Tower_of_dawn_eunseok_2018_11_05

#ifdef __Patch_Add_User_Emotion_Quest_Mission_Type_ch_20190117
	m_impl->questProcessorList[QuestEventType::USER_EMOTION] = &ActionPlayerQuest::onUserEmotion;
#endif
	// 기간제 퀘스트 갱신용 타이머 설정
	setNextRenewalTimer();
}


void ActionPlayerQuest::UpdateQuestToDB( QuestInfo& quest, Bool force )
{
	EntityPlayer* player = GetOwnerPlayer();

	const QuestElem* questElem = SCRIPTS.GetQuest( quest.questId );
	VALID_RETURN( questElem,);

	if ( !force && isProgressSaveQuestType( questElem, quest.progressFlag ) == false)
	{
		return; 
	}


	if ( !force && player->GetTutorialState() == TutorialState::PLAYING )
	{
		return;
	}

	EReqDbUpdateQuest* req = NEW EReqDbUpdateQuest;
	EventPtr reqPtr(req);

	VERIFY_RETURN( req, );
	AttributePlayer* attrPlayer = GetEntityAttribute( player );
	VERIFY_RETURN( attrPlayer, );

	//dblog 퀘스트
	ActionSector* sectorAction = GetEntityAction( player );
	EReqLogDb2* log = NEW EReqLogDb2;
	EventPtr logEventPtr( log );
	EventSetter::FillLogForEntity( LogCode::E_QUEST_INFO, player, log->action );
	log->action.groupKey = sectorAction->GetDungeonLogKey();
	log->action.var32s.push_back( quest.questId );
	log->action.var32s.push_back( quest.progressFlag );
	SendDataCenter( logEventPtr );

	req->charId = attrPlayer->charId;
	req->quest = quest;
	req->repeatCount = getQuestRepeatCount( quest.questId );

	SERVER.SendToDb( player, reqPtr );
}

void ActionPlayerQuest::OnTick()
{
	onTickQuest();
	onTickRenewal();
	onTickAutoGainQuest();
}

void ActionPlayerQuest::onTickQuest()
{
	EntityPlayer* player = GetOwnerPlayer();
	VALID_RETURN( player && player->IsValid(), );

	SIMPLE_TIMER_CHECK( m_impl->defaultTickTimer, 1000 );
	for( QuestMap::iterator it = m_impl->quests.begin(); it != m_impl->quests.end(); )
	{
		const QuestElem* questElem = SCRIPTS.GetQuest( it->second.questId );

		if ( questElem == nullptr )
		{
			++it;
			continue;
		}

		if ( it->second.deadline > 0 ) // 완료시간이 있는 퀘스트
		{
			Int64 curTicks = 0;
			time( (time_t*)&curTicks );

			if ( it->second.deadline < (Int32)curTicks ) // 시간오버
			{
				// 공유 타잎일 경우 성공 / 실패 처리는 미션이 종료될 때 이루어진다.
				if ( questElem->GetOccurType() != QuestOccurType::Range )
				{
					if ( it->second.progressFlag == QuestFlag::PROGRESS )
					{
						failQuest( it->second.questId );

						// 결과 전송.
						SendFailQuest(player, it->second.questId );

						it = m_impl->quests.erase(it);

						continue;
					}
					else if ( it->second.progressFlag == QuestFlag::COMPLETE ) 
					{
						// 완료되었는데 남아 있는 퀘스트
						failQuest( it->second.questId );

						// 완료될 때 동기화 되었다고 가정.

						it = m_impl->quests.erase(it);

						continue;
					}
				}
			}
		}
		++it;
	}
}

void ActionPlayerQuest::onTickRenewal()
{
	SIMPLE_TIMER_CHECK( m_impl->renewalTimer, m_impl->renewalTick );
	renewalQuest();
	setNextRenewalTimer();
}

void ActionPlayerQuest::onTickAutoGainQuest()
{
	SIMPLE_TIMER_CHECK( m_impl->autoGainTimer, 1000 );
	checkAutoGainQuests();
}

ErrorQuest::Error ActionPlayerQuest::GetQuest(IndexQuest questId, EntityId entityId )
{
	const QuestElem* quest = SCRIPTS.GetQuest( questId );
	VALID_RETURN( quest , ErrorQuest::E_QUEST_NOT_FOUND );

	VERIFY_RETURN( false == quest->missionVector.empty(), ErrorQuest::E_QUEST_UNKNOW );

	return CheckQuestAcceptCond( quest, entityId );
}

ErrorQuest::Error ActionPlayerQuest::AcceptQuest(IndexQuest questId, EntityId npcEntityId )
{
	EntityPlayer* player = GetOwnerPlayer();
	VALID_RETURN( player && player->IsValid(), ErrorQuest::E_FAILED );

	const QuestElem* elem = SCRIPTS.GetQuest( questId );
	VALID_RETURN( elem, ErrorQuest::E_QUEST_NOT_FOUND );

	ErrorQuest::Error eErrorQuest = CheckQuestAcceptCond( elem, npcEntityId, QuestElem::AUTO_GAIN_IGNORE_MASK::QC_NONE );
#ifdef __Patch_Quest_Over_MaxCount_by_cheolhoon_181025
	if(ErrorQuest::E_QUEST_SIZE_MAX == eErrorQuest && elem->IsWaitableQuest())	// 최대 갯수가 넘어간 경우에 대기열에 추가.
	{
		WaitQuest(questId, elem->story_type);
	}
#endif
	VALID_RETURN( eErrorQuest == ErrorQuest::SUCCESS, eErrorQuest );

	eErrorQuest = startQuest( questId, npcEntityId, false );
	VALID_RETURN( eErrorQuest == ErrorQuest::SUCCESS, eErrorQuest );

	checkJoinSharedQuest( questId );

	if ( elem->startTeleport != 0 )
	{
		ReqChangeMap( elem->startTeleport );
	}

	theQuestSystem.OnAcceptQuest(player, questId );
	player->GetAction<ActionSector>()->OnControllerEvent( DungeonEventType::QUEST_ACCEPTED, player, nullptr, questId );

	if ( elem->progress_Type == QuestProgressType::Mission )
	{
		m_impl->misisonHeroEntityIdMap.insert( QuestMissionHeroEntityIdMap::value_type( questId, npcEntityId ) );
	}

	return ErrorQuest::SUCCESS;	
}

ErrorQuest::Error ActionPlayerQuest::AcceptAutoGainQuest(IndexQuest questId, QuestElem::AUTO_GAIN_IGNORE_MASK ignoreCondFlag )
{
	EntityPlayer* player = GetOwnerPlayer();
	VALID_RETURN( player && player->IsValid(), ErrorQuest::E_FAILED );

	const QuestElem* elem = SCRIPTS.GetQuest( questId );
	VALID_RETURN( elem, ErrorQuest::E_QUEST_NOT_FOUND );

	ErrorQuest::Error eErrorQuest = CheckQuestAcceptCond( elem, 0, ignoreCondFlag );
#ifdef __Patch_Quest_Over_MaxCount_by_cheolhoon_181025
	if(ErrorQuest::E_QUEST_SIZE_MAX == eErrorQuest)	// 최대 갯수가 넘어간 경우에 대기열에 추가. autoGainQuest는 무조건 대기열로 추가.
	{
		WaitQuest(questId, elem->story_type);
	}
#endif
	VALID_RETURN( eErrorQuest == ErrorQuest::SUCCESS, eErrorQuest );

	eErrorQuest = startQuest( questId, 0, false );
	VALID_RETURN( eErrorQuest == ErrorQuest::SUCCESS, eErrorQuest );

	checkJoinSharedQuest( questId );

	if ( elem->startTeleport != 0 )
	{
		ReqChangeMap( elem->startTeleport );
	}

	theQuestSystem.OnAcceptQuest(player, questId );
	player->GetAction<ActionSector>()->OnControllerEvent( DungeonEventType::QUEST_ACCEPTED, player, nullptr, questId );

	return ErrorQuest::SUCCESS;	
}


void ActionPlayerQuest::OccurQuestByLevel( const CharLevel charLevel)
{
	EntityPlayer* player = GetOwnerPlayer();
	VALID_RETURN( player && player->IsValid(), );

	const QuestScript* script = SCRIPTS.GetScript<QuestScript>();
	VERIFY_RETURN( script, );

	const QuestScript::QuestElems& questElems = script->GetQuestElems();

	// XXX: 퀘스트가 많아지면 아래는 성능 이슈가 생길 수 있다. 
	// 미리 레벨업에 해당하는 퀘스트만 모아놓을 필요가 있다.

	Int32 err = ErrorQuest::SUCCESS;
	for( QuestScript::QuestElems::const_iterator it = questElems.begin(); it != questElems.end(); ++it)
	{
		// 레벨 강제 시작
		if ( it->second.GetOccurType() == QuestOccurType::FORCELEVEL && it->second.GetOccurValueAsByte(0) <= charLevel )
		{
			err = AcceptQuest( it->second.index );
			if ( err != ErrorQuest::SUCCESS )
			{
				SendQuestError(player, err );
			}
		}
		// 레벨 일반 시작..퀘스트 알림 등록
		if( it->second.GetOccurType() == QuestOccurType::LEVEL && it->second.GetOccurValueAsByte(0) == charLevel )
		{
			//퀘스트 알림리스트에 등록하고 클라이언트에 통보
			err = ReserveQuest( it->second.index );
			if ( err != ErrorQuest::SUCCESS )
			{
				SendQuestError(player, err );
			}
		}
	}		
}

#ifdef __Reincarnation__jason_180628__
void ActionPlayerQuest::OccurQuestBySoulLevel(const SoulLevelEx& soulInfo, Bool isLevelUp)
{
	UNREFERENCED_PARAMETER(isLevelUp);

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	const QuestScript::QuestElems& questElems = SCRIPTS.GetScript<QuestScript>()->GetQuestElems();

	for (auto itr : questElems)
	{
		const QuestElem& elem = itr.second;
		VALID_DO(elem.GetOccurType() == QuestOccurType::SoulLevelUp, continue);
		VALID_DO(elem.GetOccurValueAsByte(0) == soulInfo.GetReincarnatedLevel(), continue);
		VALID_DO(Limits::Reincarnation_RequireSoulLevel <= soulInfo.GetRawLevel(), continue);

		ErrorQuest::Error eError = AcceptQuest(elem.index);

		// 에러 "이미진행중인퀘스트"를 클라로 전송하지 않음		
		VALID_DO(eError == ErrorQuest::SUCCESS,  continue);
	}
}
#endif

void ActionPlayerQuest::OccurQuestByItemUse( const IndexItem itemIndex, const IndexQuest questIndex )
{
	EntityPlayer* player = GetOwnerPlayer();
	VALID_RETURN( player && player->IsValid(), );

	ActionPlayerInventory* actPlayerInven = GetEntityAction(player);
	VERIFY_RETURN( actPlayerInven, );
	VERIFY_RETURN( actPlayerInven->IsEnoughItem( InvenBags, itemIndex, 1 ), );

	const QuestElem* questElem = SCRIPTS.GetQuest( questIndex );
	VERIFY_RETURN( questElem, );

	setUsingQuestOccurItem( itemIndex, questIndex );

	SendGiveQuest(player, questIndex );
}

ErrorQuest::Error ActionPlayerQuest::GiveUpQuest(IndexQuest questId )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorQuest::playerNotFound);

	const QuestElem* quest = SCRIPTS.GetQuest( questId );
	VERIFY_RETURN( quest, ErrorQuest::E_QUEST_NOT_FOUND );

	if (SCRIPTS.IsAdvantureQuest(questId))
	{
		return ErrorQuest::E_QUEST_NOT_FOUND;
	}

	QuestMap::const_iterator it = m_impl->quests.find( questId );

	if ( it != m_impl->quests.end() )
	{
#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
		if( it->second.progressFlag == QuestFlag::PROGRESS || it->second.progressFlag == QuestFlag::COMPLETE || it->second.progressFlag == QuestFlag::IMPOSSIBLE)
#else
		if( it->second.progressFlag == QuestFlag::PROGRESS || it->second.progressFlag == QuestFlag::COMPLETE)
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313
		{
			m_impl->quests.erase(it);

			removeMission( quest );
		}

		QuestInfo info;		
		info.questId = questId;		
		info.progressFlag = QuestFlag::GIVEUP;

		UpdateQuestToDB(info);

		if ( quest->accept_item != 0 )
		{
			VERIFY_DO(ErrorItem::SUCCESS == player->GetInventoryAction().RemoveByIndex(InvenBags, quest->accept_item, 1), MustErrorCheck);
		}

		checkLeaveSharedQuest( questId );
		checkDeactivate( quest );

#ifdef __Patch_Quest_Over_MaxCount_by_cheolhoon_181025
		CheckWaitQuest();
#endif
	}

	return ErrorQuest::SUCCESS;
}


ErrorQuest::Error ActionPlayerQuest::RewardQuest(IndexQuest questId, IndexItem selectItemIndex, Bool isForce, Bool isGMCommand )
{
	// XXX: transactional 하지 않다. 그러니까 보상이 주어지고 나서 완료되지 않은 상태로 남아 있을 수 있다. 어부징이 발생할 수 있다.
	// RewardQuest가 내부와 외부 모두에서 호출되는데 버그의 소지가 많다. 
	// RewardQuest에서 미션을 다시 시작하거나 퀘스트가 지워지지 않는 경우가 있다. 

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorQuest::E_ERROR);
	
	const QuestElem* elem = SCRIPTS.GetQuest( questId );
	VERIFY_RETURN( elem, ErrorQuest::E_QUEST_NOT_FOUND );

	QuestMap::iterator currQuest = m_impl->quests.find( questId );
	VERIFY_RETURN( currQuest != m_impl->quests.end(), ErrorQuest::E_QUEST_NOT_FOUND );

	const QuestInfo& rewardableQuest = currQuest->second;

	// 컴플릿 되지 않은 퀘스트는 보상을 받을 수 없다.
	VERIFY_RETURN(rewardableQuest.progressFlag == QuestFlag::COMPLETE, ErrorQuest::E_QUEST_NOT_COMPLET );
	
#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	if(false == SCRIPTS.GetScript<QuestScheduleScript>()->CheckPossibleQuestTime(elem->index)) // 기간이 지난 경우 완료 할 수 없음.
	{		
		QuestInfo& questInfo = currQuest->second;
		questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
		SendUpdateQuest(player, &questInfo);

		return ErrorQuest::timeOverPlayQuest;
	}
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313

	/* TODO: 보상 타입이 npc, 지역등인 경우 거리 체크 해야함.  */
	if (isGMCommand == false)
	{
		switch (elem->complete_Type)
		{
		case QuestCompleteType::ByNpc:
			{
				ActionPlayerContact& actContact = player->GetContactAction();

				VALID_RETURN( actContact.GetTargetNpcId() == m_impl->npcTalking, ErrorQuest::E_QUEST_NPC_DISTANCE_CHECK );
				VALID_RETURN( actContact.CheckNpcDistance(), ErrorQuest::E_QUEST_NPC_DISTANCE_CHECK );
								
				EntityNpc* targetNpc = player->GetSectorAction().GetNpcInMySector( actContact.GetTargetNpcId() );
				VERIFY_RETURN(targetNpc, ErrorQuest::E_QUEST_NPC_DISTANCE_CHECK);
				
				VALID_RETURN( targetNpc->GetNpcIndex() == (IndexNpc)elem->complete_Index, ErrorQuest::E_QUEST_NPC_DISTANCE_CHECK );
			}
		break;
		}
	}

	// 섹터 콘트롤러에 퀘스트 완료 이벤트 전송
	player->GetSectorAction().OnControllerEvent( DungeonEventType::QUEST_COMPLETED, player, nullptr, questId );

	// 인벤 확인
	std::vector< QuestCompleteItem > completeItems;	
	ErrorQuest::Error eErrorQuest = ErrorQuest::SUCCESS;
	Int32 cnt = 0;

	cnt = getRewardSlotCnt( elem, selectItemIndex, completeItems, eErrorQuest );
	VALID_RETURN( eErrorQuest == ErrorQuest::SUCCESS, eErrorQuest );

	eErrorQuest = CheckReward( elem, cnt );
	VALID_RETURN( eErrorQuest == ErrorQuest::SUCCESS, eErrorQuest );
	

	// 전공 포인트 제한 확인
	AttributePlayer& attrPlayer = player->GetPlayerAttr();
	if ( attrPlayer.maxRiftPoint < attrPlayer.riftPoint + elem->complete_riftpoint )
	{
		SendSystemMsg(player, SystemMessage( L"sys", L"Msg_RiftPoint_Err_Max" ) );
		return  ErrorQuest::E_QUEST_INVEN_FULL;
	}

	if( !isForce )
	{
		// 미션완료?
		for ( Int32 i = 0; i < (Int32)elem->missionVector.size(); ++i )
		{
			QuestMission* mission = elem->missionVector[ i ];

			if ( !theQuestSystem.CheckMissionComplete(player, rewardableQuest, mission) )
			{	
				// 완료 하지 못했다면 다시 시작한다.
				if ( !updateMission(rewardableQuest.questId, mission, rewardableQuest.value[i] ) )
				{
					startMission(rewardableQuest, mission, rewardableQuest.value[ i ] );
				}

				sendMissionUpdate( questId, QuestFlag::PROGRESS, mission->index, rewardableQuest.value[ i ], false );
				return ErrorQuest::E_QUEST_NOT_COMPLET;
			}
		}		
	}

	processRewardItems( completeItems, elem );
#ifdef __Reincarnation__jason_180628__
	eErrorQuest = processRewardExtra(*elem);
	VALID_DO(eErrorQuest == ErrorQuest::SUCCESS, NoAction);
#endif

	Log( LogCategory::QUEST, rewardableQuest.questId, rewardableQuest.progressFlag );

	updateQuestFinish( questId, elem );
	m_impl->quests.erase( currQuest );
	
	// 완료 플레그 처리..
	QuestInfo info;
	info.questId = questId;
	info.progressFlag = QuestFlag::FINISHED;
	UpdateQuestToDB( info );
	
	theQuestSystem.OnFinishQuest(player, questId );

	if ( elem->IsSharedQuest() )
	{
		GetSharedQuestSystem()->LeaveSharedQuest(player);
		GetSharedQuestSystem()->CompleteQuest( questId );
	}

	if ( elem->enableScriptCall )
	{
		ActionPlayerScript* aps = GetEntityAction(player);
		aps->OnQuestCompleted( questId ); 
	}

#ifdef __Reincarnation__jason_180628__
	theNotifierGameContents.OnQuest_Finished(*player, *elem);
#endif

	return ErrorQuest::SUCCESS;
}

ErrorQuest::Error ActionPlayerQuest::FailQuest(IndexQuest questId )
{
	if (SCRIPTS.IsAdvantureQuest(questId))
	{
		return ErrorQuest::E_QUEST_NOT_FOUND;
	}

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), ErrorQuest::E_ERROR );

	ErrorQuest::Error rc = failQuest( questId );
	if( ErrorQuest::SUCCESS == rc )
	{
		SendFailQuest(player, questId );
		removeQuest( questId );
#ifdef __Patch_Quest_Over_MaxCount_by_cheolhoon_181025
		CheckWaitQuest();
#endif
	}

	return rc;
}

ErrorQuest::Error ActionPlayerQuest::RefuseQuest(IndexQuest questId )
{
	if (SCRIPTS.IsAdvantureQuest(questId))
	{
		return ErrorQuest::E_QUEST_NOT_FOUND;
	}

	const QuestElem* elem = SCRIPTS.GetQuest( questId );
	VALID_RETURN( elem, ErrorQuest::E_QUEST_NOT_FOUND );

	VALID_RETURN( false == elem->missionVector.empty(), ErrorQuest::E_QUEST_UNKNOW );

	QuestInfo questInfo;		
	questInfo.questId = questId;		
	questInfo.progressFlag = QuestFlag::REFUSE;		

	// DB저장
	UpdateQuestToDB( questInfo );

	return ErrorQuest::SUCCESS;
}

void ActionPlayerQuest::ResetQuest(IndexQuest questId )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), );

	const QuestElem* elem = SCRIPTS.GetQuest( questId );
	VERIFY_RETURN( elem, );

	QuestFinishMap::iterator it = m_impl->finishedQuests.find( questId );

	if ( it != m_impl->finishedQuests.end() )
	{
		it->second.repeat = 0;
	
		QuestInfo info;		
		info.questId = questId;		
		info.progressFlag = QuestFlag::GIVEUP;		

		UpdateQuestToDB(info, true);	// nonSave 퀘스트라도 삭제 한다.

		if ( elem->accept_item != 0 )
		{
			VERIFY_DO(ErrorItem::SUCCESS == player->GetInventoryAction().RemoveByIndex(InvenBags, elem->accept_item, 1), MustErrorCheck);
		}

		SendGiveupQuest( player, questId );
	}	
}



void ActionPlayerQuest::DeleteQuest(IndexQuest questId)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), );

	const QuestElem* elem = SCRIPTS.GetQuest(questId);
	VERIFY_RETURN( elem, );

	EReqDbDeleteQuest * req = NEW EReqDbDeleteQuest;
	req->charId = player->GetCharId();
	req->questId = questId;

	SERVER.SendToDb(player, EventPtr(req));

	if (elem->accept_item != 0)
	{
		VERIFY_DO(ErrorItem::SUCCESS == player->GetInventoryAction().RemoveByIndex(InvenBags, elem->accept_item, 1), MustErrorCheck);
	}
}



ErrorQuest::Error ActionPlayerQuest::ReserveQuest(IndexQuest questId )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), ErrorQuest::E_ERROR );

	const QuestElem* elem = SCRIPTS.GetQuest( questId );
	VALID_RETURN( elem, ErrorQuest::E_QUEST_NOT_FOUND );
		
	ErrorQuest::Error eErrorQuest = FindQuest( elem );
	VALID_RETURN( eErrorQuest == ErrorQuest::SUCCESS, eErrorQuest );

	// 레벨 업과 볼륨에서만 예약하는 걸로 되어 있으나 꼭 그럴 필요는 없어서 제거.

	if( elem->GetOccurType() == QuestOccurType::VOLUME )
	{
		ActionSector* act = GetEntityAction( player );
		VALID_RETURN( act, ErrorQuest::E_QUEST_UNKNOW );

		const QuestVolumeMatchElem* volumeElem = SCRIPTS.GetQuestVolume(  elem->GetOccurValueAsUInt32(0));
		VALID_RETURN( volumeElem, ErrorQuest::E_QUEST_UNKNOW );

		VALID_RETURN( act->CheckInsideVolume( volumeElem->indexZone, volumeElem->volumeId ), ErrorQuest::E_QUEST_OCCUR_VOLUMEIN );
	}

	auto it = m_impl->reservedQuests.find( questId );
	if( it == m_impl->reservedQuests.end() )
	{
		QuestInfo info;
		info.questId = questId;
		info.progressFlag = QuestFlag::RESERVE;

		UpdateQuestToDB( info );
		SendReserveQuest( player, questId );

		m_impl->reservedQuests.insert( questId );
	}
	
	return ErrorQuest::SUCCESS;
}

void ActionPlayerQuest::FinishQuest(IndexQuest questId )
{
	finishQuest( questId );
}

Bool ActionPlayerQuest::OnQuestEvent(QuestEvent& qe, Bool bSharedEvent)
{
	// 마지막 처리한 이벤트보다 이전의 이벤트라면 이미 처리된 것.
	if (!(m_impl->lastEvent < qe))
	{
		return false;
	}

	// 먼저 저장. 공유 퀘스트로 전달할 때 참조함.
	m_impl->lastEvent = qe;
	return onQuestEvent(qe, bSharedEvent);
}

void ActionPlayerQuest::OnQuestNpcKilled(IndexQuest questId, IndexNpc npcId )
{
	EntityPlayer* player = GetOwnerPlayer();
	Bool failed = false; 

	QuestMissionList* list = getMissionList( QuestMissionType::GuardNpc );
	VALID_RETURN( list, );

	for( auto& i : *list )
	{
		if ( questId == i.questId )
		{
			QuestGuardNpc* guardNpc = static_cast<QuestGuardNpc*>( i.mission );
			
			if ( guardNpc && guardNpc->indexNpc == npcId && i.isRemove == false )
			{
				i.isRemove = true;	

				failed = true; 

				// 실패한 미션들을 기록하고 나중에 퀘스트 실패, 미션들 지우기 진행
			}
		}
	}

	if ( failed )
	{
		// 수호 미션에 실패. 해당 퀘스트 실패
		failQuest( questId );
		SendFailQuest( player, questId );
		removeQuest( questId );
	}

	purgeMissions( *list );
}

void ActionPlayerQuest::OnUpdateSharedMission(IndexQuest questId, UInt32 missionType, Byte missionIndex, UShort missionValue)
{
	for( auto& i : m_impl->questMissionList )
	{
		updateSharedMission( &i, questId, missionType, missionIndex, missionValue );
	}
}

void ActionPlayerQuest::OnUpdateSharedQuest(IndexQuest questId, Tick qeustDuration)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), );

	auto it = m_impl->quests.find(questId);
	if (it == m_impl->quests.end())
	{
		MU2_WARN_LOG(core::LogCategory::DEFAULT, "sendMissionUpdate> can't find quest [questId:%d] %s", questId );
		return;
	}

	const QuestElem* elem = SCRIPTS.GetQuest(questId);
	if (nullptr == elem)
	{
		MU2_WARN_LOG(core::LogCategory::DEFAULT, "sendMissionUpdate> can't find queste elem [questId:%d]", questId );
		return;
	}

	Int64 curTime = 0;
	time((time_t*)&curTime);

	QuestInfo* info = &it->second;
	if (qeustDuration == 0 && elem->GetOccurType() != QuestOccurType::Range)
	{
		info->deadline = 0;
	}
	else
	{
		info->deadline = static_cast<Int32>(curTime) + qeustDuration / 1000;
	}
	
	SendUpdateQuest(player, info);
}

void ActionPlayerQuest::OnMissionFinished(IndexQuest questId)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), );

	// 해당 퀘스트의 성공 여부를 체크. 미션 사라질 때 퀘스트 미션의 성공 여부 체크
	QeDealineOver qe(theQuestSystem.NextEventSeq() );

	qe.questId = questId;

	OnQuestEvent( qe );

	// 실패 / 성공은 이벤트 내에서 결정되어야 한다. 
	// 현재는 guardNpc만 미션 종료시 성공을 결정한다
	// 만약 퀘스트가 남아 있다면 실패한 것이다. 
	
	if ( m_impl->quests.find( questId) != m_impl->quests.end() )
	{
		FailQuest( questId );
		SendFailQuest( player, questId );
	}
}

void ActionPlayerQuest::OnSharedQuestRemove(IndexQuest questId )
{
	questId; 

	// 
}

ErrorQuest::Error ActionPlayerQuest::FindQuest( const QuestElem* quest )
{
	VALID_RETURN( quest, ErrorQuest::E_QUEST_UNKNOW );
	VALID_RETURN( false == CheckQuestDone( quest ), ErrorQuest::E_QUEST_FINISH );
	VALID_RETURN( false == isExistQuest( quest ), ErrorQuest::E_QUEST_ALREADY_HAVE );

	return ErrorQuest::SUCCESS;
}

Bool ActionPlayerQuest::FindQuestUseItem(IndexItem itemType )
{
	QuestMissionList* list = getMissionList( QuestMissionType::UseItem );
	VALID_RETURN( list, false );

	for( auto& i : *list )
	{
		QuestUseItem* useItem = static_cast<QuestUseItem*>( i.mission );

		if( itemType == useItem->itemType )
		{
			return true;
		}
	}

	return false;
}

void ActionPlayerQuest::GiveQuest(IndexQuest questId, EntityId entityId )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), );

	ActionPlayer* actPlayer = GetEntityAction( player );
	VERIFY_RETURN( actPlayer, );

	Int32 err = GetQuest( questId, entityId );

	if ( err == ErrorQuest::SUCCESS)
	{
		const QuestElem* quest = SCRIPTS.GetQuest( questId );
	
		if ( quest == nullptr )
		{
			SendQuestError( player, ErrorQuest::E_QUEST_NOT_FOUND);
			return;
		}

		if( quest->progress_Type == QuestProgressType::Mission )
		{
			// 미션 히어로 종료된 상태면 퀘스트 전송 안 함
			if ( CheckHeroMissionState( questId ) == false )
			{
				return; 
			}
		}

		SendGiveQuest( player, questId );
	}
	else
	{
		SendQuestError( player, err);
	}
}

void ActionPlayerQuest::LeaveSharedQuest()
{
	SharedQuestSystem* sqs = GetSharedQuestSystem();
	if ( sqs )
	{
		EntityPlayer* player = GetOwnerPlayer();
		sqs->LeaveSharedQuest( player );
	}
	
}

void ActionPlayerQuest::RemoveMissionQuestItem(Bool sendNotice)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), );

	ActionPlayerInventory* actPlayerInven = GetEntityAction( player );				
	VERIFY_RETURN( actPlayerInven, );

	for( QuestMap::iterator it = m_impl->quests.begin(); it != m_impl->quests.end(); ++it )
	{
		const QuestElem* questElem = SCRIPTS.GetQuest( it->second.questId );
		VERIFY_RETURN( questElem, );

		if( questElem->progress_Type == QuestProgressType::Mission && questElem->accept_item )
		{
			ErrorItem::Error eError = actPlayerInven->RemoveByIndex(InvenBags, questElem->accept_item, 1, sendNotice );
			VERIFY_DO(eError == ErrorItem::SUCCESS, MustErrorCheck);
		}
	}
}

Bool ActionPlayerQuest::UpdateEnableQuestUI(IndexQuest questId, Bool enable )
{
	if (SCRIPTS.IsAdvantureQuest(questId))
	{
		return false;
	}

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), false);

	QuestMap::iterator it = m_impl->quests.find( questId );

	if( it != m_impl->quests.end() )
	{
		it->second.enableUI = enable;

		const QuestElem* elem = SCRIPTS.GetQuest( questId );
		VERIFY_RETURN( elem, false );

		UpdateQuestToDB( it->second );
		SendUpdateQuest( player, &it->second );

		return true;
	}

	return false;
}

Bool ActionPlayerQuest::ReqChangeMap( IndexPosition positionIndex )
{	
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), false );

	const PositionElem* found = SCRIPTS.GetPositionElem(positionIndex);
	VERIFY_RETURN(found, false);

	EzwReqChangeMap* reqToWorld = NEW EzwReqChangeMap;
	reqToWorld->toIndexPositionMovable = positionIndex;	
	SERVER.SendChangemapToWorld( player, EzwReqChangeMap::ActionQuest, EventPtr( reqToWorld ) );

	return true;
}

void ActionPlayerQuest::Log( const Byte& logType, const IndexQuest& questId, const UInt32& flag )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), );

	AttributePlayer* attrPlayer = GetEntityAttribute( player );
	VERIFY_RETURN( attrPlayer, );

	MU2_TRACE_LOG( logType, L"accountId=%d;charName=%s;charId=%d;questId=%d;flag=%d",
		attrPlayer->accountId, attrPlayer->charName.c_str(), attrPlayer->charId, questId, flag );
}

Bool ActionPlayerQuest::CheckQuestDone( const QuestElem *elem )
{
	//EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( elem, false );

	auto it = m_impl->finishedQuests.find( elem->index );
	Byte group = it == m_impl->finishedQuests.end() ? 0 : it->second.repeat;
	
	if ( elem->IsSharedQuest() && false == GetSharedQuestSystem()->IsExistSharedQuest( elem->index ) )
	{
		return true;
	}

	if( elem->progress_Type == QuestProgressType::Repeat )	// 반복 타입이면 횟수 검사를 해줘야한다.
	{
		if ( it != m_impl->finishedQuests.end() && false == isPassedRenewalPeriod( elem, it->second.lastUpdateTime ) )
		{
			return true;
		}

		if ( elem->repeatCount == FINISH_INFINITE )	// 무한 반복퀘?
		{
			return false;	// 무조건 퀘스트를 진행 하도록 한다.
		}

		if ( group >= elem->repeatCount )
		{
			return true;
		}
	}

	if( elem->progress_Type == QuestProgressType::Mission ) // 미션 퀘스트라면 무조건 진행
	{
		return false;
	}

	return group > 0;
}


Bool ActionPlayerQuest::CheckInOccurVolume(QuestOccurType::Enum occurType, UInt32 occurIndex )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), false);

	// 다른 타입이면 그냥 통과.
	VALID_RETURN( occurType == QuestOccurType::FORCEVOLUME, true );

	ActionSector* act = GetEntityAction( player );
	VERIFY_RETURN( act, false );

	const QuestVolumeMatchElem* elem = SCRIPTS.GetQuestVolume( occurIndex );
	VERIFY_RETURN( elem, false );

	VALID_RETURN( act->CheckInsideVolume( elem->indexZone, elem->volumeId ), false );

	return true;
}

void ActionPlayerQuest::CheckRemoveCollectItem( IndexItem itemIndex, UInt32 stack )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), );

	QuestMissionList* list = getMissionList( QuestMissionType::CollectItem );
	VERIFY_RETURN( list, );

	for( auto& i : *list )
	{
		CollectItem* coll = static_cast< CollectItem* >( i.mission );
		if ( coll->indexItem != itemIndex )
		{
			continue;
		}

		UShort& value = i.missionValue;
		if ( value < 1 )
		{
			continue;
		}

		if ( i.missionValue >= coll->itemCount )
		{
			ActionPlayerInventory* invenAct = GetEntityAction( player );
			if ( invenAct->IsEnoughItem(InvenBags, itemIndex, ( coll->itemCount + stack )) )
			{
				continue;
			}
		}

		value = ( value < (UShort)stack ) ? 0 : value - (UShort)stack;
		sendMissionUpdate( i.questId, QuestFlag::PROGRESS, coll->index, value, false );
	}
}

Bool ActionPlayerQuest::CheckHeroMissionState(IndexQuest questId )
{
	EntityPlayer* player = GetOwnerPlayer();	
	VERIFY_RETURN( player && player->IsValid(), false);

	EntityFunctional* missionHero = player->GetSector()->GetMissionHeroEntity();
	VERIFY_RETURN(missionHero, false);
	
	return missionHero->GetMissionHeroAction().GetMissionState( questId, player->GetCharId() );
}


void ActionPlayerQuest::NotifyHeroMissionState(IndexQuest questId )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	const QuestElem* quest = SCRIPTS.GetQuest( questId );
	VERIFY_RETURN( quest &&  QuestProgressType::Mission == quest->progress_Type, );

	EntityFunctional* pMissionHero = player->GetSector()->GetMissionHeroEntity();
	VERIFY_RETURN( pMissionHero, );
	
	auto it = m_impl->misisonHeroEntityIdMap.find( questId );
	VALID_RETURN( it != m_impl->misisonHeroEntityIdMap.end(), );

	EntityNpc* npcEntity = player->GetSectorAction().GetNpcInMySector( it->second );
	m_impl->misisonHeroEntityIdMap.erase( it );

	VERIFY_RETURN(npcEntity && npcEntity->IsValid(), ); // 영웅 미션 시간이 지난 경우 이미 사라졌을 수 있다.

	AttributePosition* attrPos = GetEntityAttribute( npcEntity );
	VERIFY_RETURN( attrPos, );

	AttributeNpc* attrNpc = GetEntityAttribute( npcEntity );
	VERIFY_RETURN( attrNpc, );

	ENtfGameHeroNpcAppeared* ntfHeroNpcAppeared = NEW ENtfGameHeroNpcAppeared;
	ntfHeroNpcAppeared->hero.npcEntityId = npcEntity->GetId();
	ntfHeroNpcAppeared->hero.npcIndex = attrNpc->GetNpcIndex();
	ntfHeroNpcAppeared->hero.position = attrPos->pos;
	ntfHeroNpcAppeared->hero.questIndex = attrNpc->questIndex;
	ntfHeroNpcAppeared->hero.stateMission = pMissionHero->GetMissionHeroAction().GetMissionState( attrNpc->questIndex, player->GetCharId() );

	SERVER.SendToClient( player, EventPtr( ntfHeroNpcAppeared ) );
}

void ActionPlayerQuest::GetCollectItemNpc( IndexNpc monsterId, std::vector<IndexItem>& itemIndexes )
{
	QuestMissionList* list = getMissionList( QuestMissionType::CollectItemB );
	VALID_RETURN( list, );

	for( const auto& i : *list )
	{
		QuestCollectItemB* mission = static_cast<QuestCollectItemB*>( i.mission );

		if ( i.missionValue >= mission->count )
		{
			continue;
		}

		if ( monsterId != mission->npcIndex[0] ||
			monsterId != mission->npcIndex[1] ||
			monsterId != mission->npcIndex[2] )
		{
			continue;
		}

		UInt32 seed = GetRandBetween( 0, DEFAULT_RATE);
		if ( seed >= mission->dropRate )
		{
			continue;
		}

		itemIndexes.push_back( mission->itemIndex );
	}
}

void ActionPlayerQuest::GetColletItemObject( IndexNpc objectId, std::vector<IndexItem>& itemIndexes )
{
	QuestMissionList* list = getMissionList( QuestMissionType::ObjectCollectItem );
	VALID_RETURN( list, );

	for ( const auto& i : *list )
	{
		QuestObjectCollectItem* mission = static_cast< QuestObjectCollectItem* >( i.mission );

		if ( i.missionValue >= mission->count )
		{
			continue;
		}

		if ( objectId != mission->objectIndex[0] &&
			objectId != mission->objectIndex[1] &&
			objectId != mission->objectIndex[2] )
		{
			continue;
		}

		UInt32 seed = GetRandBetween( 0, DEFAULT_RATE);
		if ( seed >= mission->dropRate )
		{
			continue;
		}

		itemIndexes.push_back( mission->itemIndex );
	}
}

Bool ActionPlayerQuest::CanCompleteOperateObjMission(IndexNpc objectId )
{
	QuestMissionList* list = getMissionList( QuestMissionType::OperateObj );
	VALID_RETURN( list, true );

	for ( const auto& i : *list )
	{
		QuestObjectCollectItem* mission = static_cast< QuestObjectCollectItem* >( i.mission );
		
		if ( objectId != mission->objectIndex[0] &&
			 objectId != mission->objectIndex[1] &&
			 objectId != mission->objectIndex[2] )
		{
			continue;
		}

		UInt32 seed = GetRandBetween( 0, DEFAULT_RATE);
		if ( seed >= mission->dropRate )
		{
			return false;
		}
	}

	return true;
}

Bool ActionPlayerQuest::CanOperateObj( const IndexQuest questId, const IndexNpc objectId )
{
	QuestMap::iterator it = m_impl->quests.find( questId );
	VALID_RETURN( it != m_impl->quests.end(), false );
	VALID_RETURN( it->second.progressFlag == QuestFlag::PROGRESS, false );

	const auto elem = SCRIPTS.GetQuest( questId );
	VALID_RETURN( elem, false );

	if ( elem->missionStepping )
	{
		QuestMission* mission = elem->missionVector[it->second.currentStepMisionIndex];
		VALID_RETURN( mission, false );

		return mission->IsMatchTarget( { static_cast<UInt32>(objectId) } );
	}

	return true;
}

ErrorQuest::Error ActionPlayerQuest::CheckQuestAcceptCond( const QuestElem* quest, EntityId entityId, QuestElem::AUTO_GAIN_IGNORE_MASK ignoreCondFlag )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorQuest::E_ERROR);

	// step 1: 퀘스트가 이미 완료 되었거나 진행중인가?
	ErrorQuest::Error eErrorQuest = FindQuest( quest );
	VALID_RETURN( ErrorQuest::SUCCESS == eErrorQuest, eErrorQuest );

	// step 2: 기간제 퀘스트면서 포기한 경력이 있는경우 수락 가능한 기간이 되었는지 체크
	if( QuestProgressType::Repeat == quest->progress_Type )
	{
		VALID_RETURN( false == isExistGiveupQuest( quest ), ErrorQuest::E_QUEST_RENEWAL_PERIOD );
	}

	// step 3: 퀘스트를 동시에 진행 할 수 있는 개수가 초과 되었는가?	
#ifdef __Patch_Quest_Over_MaxCount_by_cheolhoon_181025
	if(m_impl->quests.size() >= Limits::QUEST_PROGRESS_INFO_COUNT && quest->story_type != QuestStoryType::GUIDE)	// 관문퀘스트는 개수 초과와 상관없이 진행가능하도록 변경.
	{
		return ErrorQuest::E_QUEST_SIZE_MAX;
	}
#else
	VALID_RETURN( m_impl->quests.size() < Limits::QUEST_PROGRESS_INFO_COUNT, ErrorQuest::E_QUEST_SIZE_MAX );
#endif

	// step 4: 퀘스트 수행 가능 지역인가?
	if ( (ignoreCondFlag & QuestElem::QC_VOLUME) == 0 )
	{
		VALID_RETURN( CheckInOccurVolume( quest->GetOccurType(), quest->GetOccurValueAsUInt32(0)), ErrorQuest::E_QUEST_OCCUR_VOLUMEIN );
	}

	// step 5: 퀘스트 레벨에 적합한가?
	const AttributePlayer* playerAttr = GetEntityAttribute( player );

	if ( (ignoreCondFlag & QuestElem::QC_LEVEL) == 0 )
	{
		VALID_RETURN( quest->level_Min <= player->GetLevel() && player->GetLevel() <= quest->level_Max, ErrorQuest::E_QUEST_OCCUR_LEVEL );
	}

	// step 6: 퀘스트 전투력에 적합한가?
	if ((ignoreCondFlag & QuestElem::QC_CP) == 0)
	{
		if ((quest->combatPower_Min != 0 || quest->combatPower_Max != 0) &&
			(playerAttr->combatPower < quest->combatPower_Min || playerAttr->combatPower > quest->combatPower_Max))
		{
			m_impl->errorParam = quest->combatPower_Min;
			return ErrorQuest::E_QUEST_OCCUR_CP;
		}
	}

#ifdef __Reincarnation__jason_180628__
	// step 6.1: 영혼정보(환생,레벨) 검사
	if ((ignoreCondFlag & QuestElem::QC_SOUL) == 0)
	{
		if ((quest->soul_Min.IsValid() || quest->soul_Max.IsValid() ) &&
			(player->GetSoulLevelEx() < quest->soul_Min || player->GetSoulLevelEx() > quest->soul_Max))
		{
			m_impl->errorParam = quest->soul_Min.GetRawLevel();
			return ErrorQuest::E_QUEST_OCCUR_CP;
		}
	}
#endif



	// Talisman Level 체크
	if ((ignoreCondFlag & QuestElem::QC_TALISMAN_LEVEL) == 0) {

		ActionPlayerTalisman& apTalisman = player->GetTalismanAction();
		if ( (quest->talisman_level_Min != 0 || quest->talisman_level_Max != 0) 
			 && (   apTalisman.GetCollectionLevel() < quest->talisman_level_Min
			     || apTalisman.GetCollectionLevel() > quest->talisman_level_Max
			 )
		) {
			m_impl->errorParam = quest->talisman_level_Min;
			return ErrorQuest::E_QUEST_OCCUR_TALISMAN_LEVEL;
		}
	}

	// step 7: 퀘스트 요구 종족에 적합한가?
	if ( (ignoreCondFlag & QuestElem::QC_RACE) == 0 )
	{
		VALID_RETURN( quest->occur_race == 0 || playerAttr->eRaceType == quest->occur_race, ErrorQuest::E_QUEST_OCCUR_RACE );
	}

	// step 8 : 퀘스트 요구 직업이 적합한가?
	if ( ( ignoreCondFlag & QuestElem::QC_CLASS ) == 0 )
	{
		VALID_RETURN( quest->occur_class == 0 || playerAttr->eClassType == quest->occur_class, ErrorQuest::E_QUEST_OCCUR_CLASS );
	}

	// step 9: 퀘스트 요구 아이템이 있다면 인벤에 요구 아이템이 있는가?
	ActionPlayerInventory* actionInven = GetEntityAction( player );

	if ( ( ignoreCondFlag & QuestElem::QC_ITEM ) == 0 )
	{
		VALID_RETURN( HaveOccurItemInEntityInventory( *actionInven, quest->occur_item ), ErrorQuest::E_QUEST_OCCUR_HAVE_ITEM );
	}

	// step 10: 연계 퀘스트를 수행 하였는가?
	if ( ( ignoreCondFlag & QuestElem::QC_LINKED_QUEST ) == 0 )
	{
		VALID_RETURN( HasCompletedLinkedQuest( quest ), ErrorQuest::E_QUEST_PRE_NOT_FINSH );
	}

	// 무시하라는 값이 있으면 체크 무시.
	if ( ignoreCondFlag == 0 )
	{
		// 영웅 미션 수락 조건 체크 
		if ( quest->progress_Type == QuestProgressType::Mission )
		{ 
			// 영웅 미션 진행 중이 아니면 에러 
			VALID_RETURN( CheckHeroMissionState( quest->index ), ErrorQuest::E_QUEST_HERO_MISSION_STATE );
		}
	}

	// step 11 : 수락시 인벤에 강제 아이템이 들어가는데 인벤 공간이 확보되어 있는가?
	if( quest->accept_item != 0 )
	{
		UInt32 freeSlotCnt = 0;

		for ( size_t n = 0; n < quest->missionVector.size(); ++n )
		{	
			VALID_RETURN( quest->missionVector[n], ErrorQuest::E_QUEST_UNKNOW );

			switch( quest->missionVector[n]->type )
			{
			case QuestMissionType::DeliverItem:
				{
					DeliverItem* deliverItem = static_cast< DeliverItem* >( quest->missionVector[n] );					
					freeSlotCnt += calcMissionItemSlotCnt( actionInven, deliverItem->indexItem, deliverItem->itemCount, eErrorQuest );
					VALID_RETURN( eErrorQuest == ErrorQuest::SUCCESS, eErrorQuest );
				}
				break;
			default:
				{
					freeSlotCnt = 1;
				}
				break;
			}

		}

		if ( ( freeSlotCnt > 0 ) && !actionInven->IsEnoughFreeSlot(InvenBags, freeSlotCnt ) )
		{
			return ErrorQuest::E_QUEST_NEED_SOLT;
		}
	}

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	if(false == SCRIPTS.GetScript<QuestScheduleScript>()->CheckPossibleQuestTime(quest->index))
	{		
		SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
		sysMsg.SetParam(L"quest", quest->index);
		SendSystemMsg(player, sysMsg);
		return ErrorQuest::timeOverPlayQuest;
	}
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313

	return ErrorQuest::SUCCESS;
}

Bool ActionPlayerQuest::CanPlayerDoGetItemQuest(IndexItem itemType )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt32 limitCount = GetQuestItemTotalCount( itemType );
	return player->GetInventoryAction().IsEnoughItem(InvenBags, itemType, limitCount);
}

UInt32 ActionPlayerQuest::GetQuestItemTotalCount(IndexItem indexItem )
{
	UInt32 count = 0;

	for( QuestMap::iterator it = m_impl->quests.begin(); it != m_impl->quests.end(); ++it  )
	{
		const QuestElem* questElem = SCRIPTS.GetQuest( it->second.questId );
		VALID_DO( questElem, continue );

		for ( size_t i = 0; i < questElem->missionVector.size(); ++i )
		{
			QuestMission* mission = questElem->missionVector[i];

			if( mission->type == QuestMissionType::CollectItem )
			{
				CollectItem* missionCollect = static_cast< CollectItem* >( mission );
				if( missionCollect->indexItem == indexItem )
				{
					count += missionCollect->itemCount;
				}
			}
			else if( mission->type == QuestMissionType::DeliverItem )
			{
				DeliverItem* missionDeliver = static_cast< DeliverItem* >( mission );
				if( missionDeliver->indexItem == indexItem )
				{
					count += missionDeliver->itemCount;
				}
			}
			else if( mission->type == QuestMissionType::GatherItem )
			{
				QuestGatherItem* missionGather = static_cast< QuestGatherItem* >( mission );
				if( missionGather->indexItem == indexItem )
				{
					count += missionGather->count; 
				}
			}
		}
	}

	return count;
}

Bool ActionPlayerQuest::HaveOccurItemInEntityInventory( ActionPlayerInventory &actionInven, IndexItem occurItem )
{
	VALID_RETURN( 0 < occurItem , true );
	VALID_RETURN( actionInven.IsEnoughItem( InvenBags, occurItem, 1 ), false );

	return true;
}

Bool ActionPlayerQuest::HasCompletedLinkedQuest( const QuestElem *elem )
{
	for( const auto& i : elem->forestallQuest )
	{
		VALID_RETURN( (IsProgressOrComplete( i ) || HasCompletedOnce( i )), false );
	}

	for( const auto& i : elem->prevQuest )
	{
		VALID_RETURN( HasCompletedOnce( i ), false );
	}

	return true;
}

Bool ActionPlayerQuest::HasCompletedOnce(IndexQuest questId )
{
	Byte group = getQuestRepeatCount( questId );
	return group > 0;
}

Bool ActionPlayerQuest::IsFinished(const IndexQuest& questId)
{
	const QuestElem* elem = SCRIPTS.GetQuest( questId );
	VALID_RETURN( elem, false );

	Byte group = getQuestRepeatCount( questId );
	if ( elem->progress_Type == QuestProgressType::Repeat )	// 반복 타입이면 횟수 검사를 해줘야한다.
	{
// 		if ( elem->repeatCount == FINISH_INFINITE )	// 무한 반복퀘?
// 		{
// 			return false;	// 무조건 퀘스트를 진행 하도록 한다.
// 		}
// 
// 		if ( group >= elem->repeatCount )
// 		{
// 			return true;
// 		}

		if ( elem->repeatCount == FINISH_INFINITE )
		{
			// 현재 맵 상에서 1회라도 완료했다면 완료로 체크한다.
			auto it = m_impl->finishQuestsInThisMap.find( questId );
			return ( it != m_impl->finishQuestsInThisMap.end() );
		}
	}

	return group > 0;
}

Bool ActionPlayerQuest::IsProgressQuest(IndexQuest questId )
{
	QuestMap::iterator it = m_impl->quests.find( questId );
	VALID_RETURN( it != m_impl->quests.end(), false );
	VALID_RETURN( it->second.progressFlag == QuestFlag::PROGRESS, false );

	return true;
}

Bool ActionPlayerQuest::IsComplete(IndexQuest questId )
{
	QuestMap::iterator it = m_impl->quests.find( questId );
	VALID_RETURN( it != m_impl->quests.end(), false );
	VALID_RETURN( it->second.progressFlag == QuestFlag::COMPLETE, false );

	return true;
}

Bool ActionPlayerQuest::IsProgressOrComplete(IndexQuest questId )
{
	QuestMap::iterator it = m_impl->quests.find( questId );
	VALID_RETURN( it != m_impl->quests.end(), false );
	VALID_RETURN( it->second.progressFlag == QuestFlag::PROGRESS || it->second.progressFlag == QuestFlag::COMPLETE, false );

	return true;
}

void ActionPlayerQuest::SendGiveQuest(EntityPlayer* owner, const IndexQuest& questId)
{
	ntfToClient( owner, questId, QuestFlag::GIVEQUEST );
}

void ActionPlayerQuest::SendAcceptQuest(EntityPlayer* owner, const IndexQuest& questId)
{
	ntfToClient( owner, questId, QuestFlag::PROGRESS);
}

void ActionPlayerQuest::SendFinishQuest(EntityPlayer* owner, const IndexQuest& questId)
{
	ntfToClient( owner, questId, QuestFlag::FINISHED );
}

void ActionPlayerQuest::SendGiveupQuest(EntityPlayer* owner, const IndexQuest& questId)
{
	ntfToClient( owner, questId, QuestFlag::GIVEUP );
}

void ActionPlayerQuest::SendFailQuest(EntityPlayer* owner, const IndexQuest& questId)
{
	ntfToClient( owner, questId, QuestFlag::FAILED );
}

void ActionPlayerQuest::SendRefuseQuest(EntityPlayer* owner, const IndexQuest& questId)
{
	ntfToClient( owner, questId, QuestFlag::REFUSE );
}

void ActionPlayerQuest::SendReserveQuest(EntityPlayer* owner, const IndexQuest questId )
{
	ntfToClient( owner, questId, QuestFlag::RESERVE );
}

void ActionPlayerQuest::SendActivateQuest(EntityPlayer* owner, const IndexQuest questId )
{
	ntfToClient( owner, questId, QuestFlag::ACTIVATE );
}

void ActionPlayerQuest::SendDeactivateQuest(EntityPlayer* owner, const IndexQuest questId )
{
	ntfToClient( owner, questId, QuestFlag::DEACTIVATE );
}

void ActionPlayerQuest::SendUpdateQuest(EntityPlayer* owner, const QuestInfo* info )
{
	ntfToClient( owner, info->questId, info->progressFlag, info );
}

void ActionPlayerQuest::SendQuestError(EntityPlayer* owner, const UInt32& err)
{	
	switch( err )
	{ 
	case ErrorQuest::E_QUEST_NOT_FOUND: 
		SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Quest_Error_NotFound" ) );
		break;				// 퀘스트를 찾을수 없음.
	case ErrorQuest::E_QUEST_FINISH:  
		//별도의 메시지 보내지 않는다. SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Quest_Error_Fini.h" ) );
		break;					// 완료 퀘
	case ErrorQuest::E_QUEST_ALREADY_HAVE:  
		SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Quest_Error_AlreadyHave" ) );
		break;			// 이미 수락함
	case ErrorQuest::E_QUEST_NOT_COMPLET:  
		SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Quest_Error_NotComplete" ) );
		break;			// 미션 완료 안함	
	case ErrorQuest::E_QUEST_INVEN_FULL:  
		SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Quest_Error_InvenFull" ) );
		break;				// 인벤 풀ㅋ
	case ErrorQuest::E_QUEST_SIZE_MAX:  
		SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Quest_Error_SizeMax" ) );
		break;				// 진행 퀘 최대 수랑 초과
	case ErrorQuest::E_QUEST_PRE_NOT_FINSH:  
		SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Quest_Error_PreNotFini" ) );
		break;			// 이전 퀘 완료 안함
	case ErrorQuest::E_QUEST_OCCUR_LEVEL:  
		SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Quest_Error_OccurLevel" ) );
		break;			// 시작 조건 - 레벨이 작거나 큼
	case ErrorQuest::E_QUEST_OCCUR_CLASS:  
		SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Quest_Error_OccurClass" ) );
		break;			// 시작 조건 - 다른 클래스 퀘
	case ErrorQuest::E_QUEST_OCCUR_RACE:  
		SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Quest_Error_OccurRace" ) );
		break;				// 시작 조건 - 다른 종족 퀘
	case ErrorQuest::E_QUEST_OCCUR_HAVE_ITEM:  
		SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Quest_Error_OccurHaveItem" ) );
		break;		// 시작 조건 - 요구 아이템이 음슴
	case ErrorQuest::E_QUEST_MAPID:  
		SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Quest_Error_MapID" ) );
		break;					// 해당맵 퀘가 아님
	case ErrorQuest::E_QUEST_NEED_SOLT:  
		SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Quest_Error_NeedSlot" ) );
		break;				// 슬롯수 부족 
	case ErrorQuest::E_QUEST_SELECT_ITEM:  
		SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Quest_Error_SelectItem" ) );
		break;			// 완료 보상아이템을 선택 허삼
	case ErrorQuest::E_QUEST_NOT_FOUND_MISSION_ITEM:  
		SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Quest_Error_NotFoundMissionItem" ) );
		break;	// 미션 아이템은 없는 아이템임.
	case ErrorQuest::E_QUEST_ACTIVE_MISSION_FAIL:  
		SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Quest_Error_ActiveMissionFail" ) );
		break;	// 미션 활성화 실패  
	case ErrorQuest::E_QUEST_SHARE_FAIL:  
		SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Quest_Error_ShareFail" ) );
		break;				// 공유 실패
	case ErrorQuest::E_QUEST_OCCUR_VOLUMEIN:  
		SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Quest_Error_OccurVolumeIn" ) );
		break;			// 시작 조건 - 볼륨에 들어오지 못 했음
	case ErrorQuest::E_QUEST_TIMEOUT:  
		SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Quest_Error_TimeOut" ) );
		break;                // 제한시간 초과
	case ErrorQuest::E_QUEST_MEETNPC:  
		SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Quest_Error_MeetNPC" ) );
		break;                // NPC 만나기 실패
	case ErrorQuest::E_QUEST_USEITEM:  
		SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Quest_Error_UseItem" ) );
		break;                // 아이템 사용 실패
	case ErrorQuest::E_QUEST_ESCORT_NO_NPC:
		SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Quest_Error_No_EscortNpc" ) );
		break;				 // 호위퀘스트의 호위대상 NPC가 없다.
	case ErrorQuest::E_QUEST_ESCORT_OVER_RANGE:
		SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Quest_Error_Too_Far_EscortNpc" ) );
		break;				// 호위대상과 거리가 멀다.
	case ErrorQuest::E_QUEST_ESCORT_ON_DUTY:
		SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Quest_Error_On_Duty_EscortNpc" ) );
		break;				// 이미 퀘스트 수행중이다.
	case ErrorQuest::E_QUEST_SAME_HEROMISSION:
		SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Quest_Error_Same_HeroMission" ) );
		break;				// 동일한 영웅미션을 연속하여 수행할수 없습니다.
	case ErrorQuest::E_QUEST_RENEWAL_PERIOD:
		SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Quest_Err_Cooltime" ) );
		break;				// 반복퀘스트 수락가능한 기간이 되지않았습니다.
	case ErrorQuest::E_QUEST_UNKNOW:  
		SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Quest_Error_Unknown" ) );
		break;				// 알수 없는 에러
	case ErrorQuest::E_QUEST_OCCUR_CP:
		{
			SystemMessage msg( L"sys", L"Msg_Quest_Error_OccurCP" );
			msg.SetParam( L"str", m_impl->errorParam );
			SendSystemMsg( owner, msg );
		}
		break;				// 전투력이 모자르다.
	case ErrorQuest::E_QUEST_OCCUR_TALISMAN_LEVEL:
	{
		SystemMessage msg(L"sys", L"Msg_Quest_Error_TalismanLevel");
		msg.SetParam(L"str", m_impl->errorParam);
		SendSystemMsg(owner, msg);
	}
	break;					// 탈리스만 레벨 부족
	default:
		break;
	}	
}


//------------- test code ------------------
ErrorQuest::Error ActionPlayerQuest::TestAcceptQuest(IndexQuest questId )
{
	EntityPlayer* player = GetOwnerPlayer();
	VALID_RETURN( player && player->IsValid(), ErrorQuest::E_ERROR );

	const QuestElem* elem = SCRIPTS.GetQuest( questId );
	VALID_RETURN( elem, ErrorQuest::E_QUEST_NOT_FOUND );

	ErrorQuest::Error eErrorQuest = startQuest( questId, 0, true );
	VALID_RETURN( eErrorQuest == ErrorQuest::SUCCESS, eErrorQuest );

	checkJoinSharedQuest( questId );

	if ( elem->startTeleport != 0 )
	{
		ReqChangeMap( elem->startTeleport );
	}

	theQuestSystem.OnAcceptQuest( player, questId );
	return ErrorQuest::SUCCESS;
}

ErrorQuest::Error ActionPlayerQuest::TestFinishQuest(IndexQuest questId )
{
	ErrorQuest::Error error = TestCompleteQuest( questId );
	VALID_RETURN( error == ErrorQuest::SUCCESS, error );
	
	error = RewardQuest( questId, 0, true, true );
	VALID_RETURN( error == ErrorQuest::SUCCESS, error );

	return finishQuest( questId );
}

ErrorQuest::Error ActionPlayerQuest::TestCompleteQuest(IndexQuest questId )
{
	EntityPlayer* player = GetOwnerPlayer();
	VALID_RETURN( player && player->IsValid(), ErrorQuest::E_ERROR );

	auto it = m_impl->quests.find( questId );
	VALID_RETURN( m_impl->quests.end() != it, ErrorQuest::E_QUEST_NOT_FOUND );
	VALID_RETURN( it->second.progressFlag == QuestFlag::PROGRESS, ErrorQuest::E_QUEST_NOT_FOUND );

	const QuestElem* elem = SCRIPTS.GetQuest( questId );
	VALID_RETURN( elem, ErrorQuest::E_FAILED );

	UInt16 value = 0;
	for ( const auto& i : elem->missionVector )
	{
		UShort completeValue = 0;

		theQuestSystem.GetMissionCompleteValue( player, it->second, i, completeValue );
		it->second.value[i->index] = completeValue;

		value = it->second.value[i->index];
		
		updateMission( questId, i, value );
		checkQuestComplete( questId, i, value );
	}

	return ErrorQuest::SUCCESS;
}

ErrorQuest::Error ActionPlayerQuest::TestResetQuest(IndexQuest questId )
{
	if( questId != 0 )
	{
		ResetQuest( questId );
	}
	else
	{
		for( auto it = m_impl->finishedQuests.begin() ; it != m_impl->finishedQuests.end() ; ++it )
		{
			ResetQuest( it->first );
		}
	}

	return ErrorQuest::SUCCESS;
}

ErrorQuest::Error ActionPlayerQuest::TestGiveUpQuest(IndexQuest questId )
{
	EntityPlayer* player = GetOwnerPlayer();
	VALID_RETURN( player && player->IsValid(), ErrorQuest::E_ERROR );

	if( 0 != questId )
	{
		if( ErrorQuest::SUCCESS == GiveUpQuest( questId ) )
		{
			SendGiveupQuest( player, questId );
			NotifyHeroMissionState( questId );
		}
	}
	else
	{
		std::vector< UInt32 > questIdList;
		std::transform( m_impl->quests.begin(), m_impl->quests.end(), std::back_inserter( questIdList ), std::bind( &QuestMap::value_type::first, std::placeholders::_1 ) );

		for( auto& i : questIdList )
		{
			if( ErrorQuest::SUCCESS == GiveUpQuest( i ) )
			{
				SendGiveupQuest( player, i );
				NotifyHeroMissionState( questId );
			}
		}
	}

	return ErrorQuest::SUCCESS;
}

void ActionPlayerQuest::TestCompleteQuestAll()
{
	EntityPlayer* player = GetOwnerPlayer();
	VALID_RETURN( player && player->IsValid(), );

	AttributePlayer* attr = GetEntityAttribute( player );
	VALID_RETURN( attr, );

	const QuestScript* script = SCRIPTS.GetScript< QuestScript >();
	VALID_RETURN( script, );

	const auto& elems = script->GetQuestElems();

	for( auto& i : elems )
	{
		const QuestElem& info = i.second;

		if( info.level_Min <= player->GetLevel() )
		{
			VALID_DO( false == isExistFinishedQuest( &info ), continue );

			if( !isExistQuest( &info ) )
			{
				QuestInfo questInfo;	
				questInfo.questId		= info.index;
				questInfo.progressFlag	= QuestFlag::PROGRESS;
				questInfo.enableUI		= true;

				addProgressQuest( questInfo );

				SendAcceptQuest( player, info.index );
			}

			updateQuestFinish( info.index, &info );

			removeMission( &info );
			
			QuestInfo questInfo;	
			questInfo.questId		= info.index;	
			questInfo.progressFlag	= QuestFlag::FINISHED;	
			UpdateQuestToDB( questInfo);

			SendFinishQuest( player, info.index );

			removeQuest( info.index );
		}
	}
}

void ActionPlayerQuest::ClearQuestMission(IndexQuest questId, UInt8 missionIndex )
{
	EntityPlayer* player = GetOwnerPlayer();
	VALID_RETURN( player && player->IsValid(), );

	const QuestElem* elem = SCRIPTS.GetQuest( questId );
	if( nullptr == elem )
	{
		MU2_WARN_LOG( LogCategory::QUEST, "ActionQuest::ClearQuestMission > invalid quest. questId(%u)", questId );
		return;
	}

	if( static_cast< UInt8 >( elem->missionVector.size() ) <= missionIndex )
	{
		MU2_WARN_LOG( LogCategory::QUEST, "ActionQuest::ClearQuestMission > invalid mission index. questId(%u), missionIndex(%u), missionIndexSize(%u)", questId, missionIndex, static_cast< UInt32 >( elem->missionVector.size() ) );
		return;
	}

	auto iter = m_impl->quests.find( questId );
	VALID_RETURN( m_impl->quests.end() != iter, );

	UShort completeValue = 1;

	QuestInfo& questInfo = iter->second;
	questInfo.missionIndex = missionIndex;

	QuestMission* questMission = elem->missionVector[ missionIndex ];
	VALID_RETURN( questMission, );

	Mission* mission = getMission( questId, questMission->type, missionIndex );
	VALID_RETURN( mission, );
	VALID_RETURN( mission->isRemove == false, );

	theQuestSystem.GetMissionCompleteValue( player, questInfo, elem->missionVector[missionIndex], completeValue );

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

	mission->missionValue = completeValue;

	if ( isQuestComplete( questId, questMission, mission->missionValue, mission->isRemove ) )
	{
		questFlag = QuestFlag::COMPLETE;
	}

	sendMissionUpdate( questId, questFlag, questMission->index, mission->missionValue, mission->isRemove );
	if ( questFlag == QuestFlag::COMPLETE && elem->complete_Type == QuestCompleteType::ByItself )
	{
		rewardQuestByItself( questId );
	}
}

SharedQuestSystem* ActionPlayerQuest::GetSharedQuestSystem()
{
	EntityPlayer* player = GetOwnerPlayer();
	VALID_RETURN( player && player->IsValid(), nullptr );

	ActionSector* asector = GetEntityAction(player);
	VALID_RETURN( asector, nullptr );

	Sector* sector = asector->GetSector();
	VALID_RETURN( sector, nullptr );

	return sector->GetSharedQuestSystem();
}


ErrorQuest::Error ActionPlayerQuest::startQuest(IndexQuest questId, EntityId entityId, Bool isGMCommand )
{
	EntityPlayer* player = GetOwnerPlayer();
	VALID_RETURN( player && player->IsValid(), ErrorQuest::E_ERROR );

	const QuestElem* elem = SCRIPTS.GetQuest( questId );
	VERIFY_RETURN( elem, ErrorQuest::E_QUEST_NOT_FOUND );
	VERIFY_RETURN( false == elem->missionVector.empty(), ErrorQuest::E_QUEST_UNKNOW );
	
	if (isGMCommand == false && elem->GetOccurValueAsInt32(0) != 0)
	{
		switch (elem->GetOccurType())
		{
		case QuestOccurType::NPC:
		{
			VALID_RETURN( player->GetContactAction().CheckNpcDistance(), ErrorQuest::E_QUEST_NPC_DISTANCE_CHECK );
						
			EntityNpc* targetNpc = player->GetSectorAction().GetNpcInMySector(player->GetContactAction().GetTargetNpcId() );
			VERIFY_RETURN(targetNpc, ErrorQuest::E_QUEST_NPC_DISTANCE_CHECK);
			
			VALID_RETURN( targetNpc->GetNpcIndex() == (IndexNpc)elem->GetOccurValueAsInt32(0), ErrorQuest::E_QUEST_NPC_DISTANCE_CHECK );
		}
		break;
		}
	}

	ErrorQuest::Error err = FindQuest( elem );
	VALID_RETURN( err == ErrorQuest::SUCCESS, err );	

	QuestInfo info;	
	info.questId = questId;
	if (SCRIPTS.IsAdvantureQuest(questId))
		info.fromcontent = QuestFromContent::ADVENTURENOTE;

	for ( size_t n = 0; n < elem->missionVector.size(); ++n )
	{
#ifdef __Hotfix_Quest_Mission_Count_Modify_by_cheolhoon_20181105
		err = startMission( info, elem->missionVector[n]);
#ifdef __Hotfix_Quest_Mission_Check_Duplication_by_cheolhoon_20181112
		if(!(ErrorQuest::SUCCESS == err || ErrorQuest::startMissionFailedAlreadyCompleted == err || ErrorQuest::startMissionFailedDuplicatedMission == err))
#else
		if(!(ErrorQuest::SUCCESS == err || ErrorQuest::startMissionFailedAlreadyCompleted == err))
#endif // __Hotfix_Quest_Mission_Check_Duplication_by_cheolhoon_20181112
		
#else
		if ( !startMission( info, elem->missionVector[n] ) )
#endif
		{
			// 미션을 활성화 시키다 실패하면 미션 삭제
			removeMission( elem ); // 다 지워줘야하지 않나?
			return ErrorQuest::E_QUEST_ACTIVE_MISSION_FAIL;
		}
	}

	ActionPlayerInventory* actInven = GetEntityAction( player );
	VALID_RETURN( actInven, ErrorQuest ::E_FAILED );

	// 수락 아이템을 넣어줌
	if ( elem->accept_item != 0 )
	{
		if ( ErrorItem::SUCCESS != actInven->Pickup( ItemData(elem->accept_item, 1) ) )
		{
			return ErrorQuest::E_QUEST_UNKNOW;
		}
	}

	info.progressFlag = QuestFlag::PROGRESS;
	info.enableUI = true;

	Int64 curTime = 0;
	time( (time_t*)&curTime );

	if ( elem->IsSharedQuest() )
	{
		// 범위 퀘스트일 경우는 영웅 미션의 지속 시간을 퀘스트 시간으로 함.
		UInt32 sharedDuration = 0; 

		if ( GetSharedQuestSystem()->GetQuestDuration( questId, sharedDuration ) )
		{
			// 영웅 미션 데이터는 밀리 초. 데드라인은 초 단위.
			if (sharedDuration == 0 && elem->GetOccurType() != QuestOccurType::Range )
			{
				info.deadline = 0;
			}
			else
			{
				info.deadline = static_cast<Int32>(curTime) + sharedDuration / 1000;
			}
		}
		else
		{
			if( elem->complete_Time )
			{
				info.deadline = static_cast<Int32>(curTime) + elem->complete_Time;
			}
			else
			{
				info.deadline = 0;
			}
		}
	}
	else
	{
		if( elem->complete_Time )
		{
			info.deadline = static_cast<Int32>(curTime) + elem->complete_Time;
		}
		else
		{
			info.deadline = 0;
		}	
	}
	
#ifdef __Patch_Quest_ProgressType_Add_Period_by_cheolhoon_20181114
	info.lastUpdateTime = DateTime::GetPresentTime();
#endif
	SendDeactivateQuest( player, elem->index );
	SendUpdateQuest( player, &info );
	UpdateQuestToDB( info );

	addProgressQuest( info );

	m_impl->reservedQuests.erase( questId );

	if( m_impl->usingQuestOccurItem.questIndex == questId )
	{
		ErrorItem::Error eError = actInven->RemoveByIndex(InvenBags, m_impl->usingQuestOccurItem.itemIndex, 1);
		VERIFY_DO(eError == ErrorItem::SUCCESS, MustErrorCheck);
	}

	resetUsingQuestOccurItem();

	UShort values = 0;
	
	ActionPlayerArtifact *actAcrtifact = GetEntityAction(player);

	for ( size_t i = 0; i < elem->missionVector.size(); ++i )
	{
		QuestMission* mission = elem->missionVector[i];

		if( mission->type == QuestMissionType::CollectItem )
		{
			CollectItem* collectItem = static_cast< CollectItem* >( mission );
			VALID_DO( collectItem, continue );
			VALID_DO( 0 < collectItem->indexItem , continue );

			values = ( UShort ) actInven->GetItemCount(InvenBags, collectItem->indexItem );

			if ( values > 0 )
			{
				if ( values >= collectItem->itemCount )
				{
					updateMission( questId, mission, values );
					checkQuestComplete( questId, mission, values );
					rewardQuestByItself( questId );
				}
				else
				{
					updateMission( questId, mission, values );
					sendMissionUpdate( questId, QuestFlag::PROGRESS, mission->index, values, false );
				}
			}
		} // MissionCollectItem
		else if (mission->type == QuestMissionType::ArtifactLevel)
		{
			QuestArtifactLevel* artifactLevel = static_cast<QuestArtifactLevel*>(mission);
			VALID_DO( actAcrtifact, continue );

			values = (UShort)actAcrtifact->CalcArtifactLevel(artifactLevel->artifactIdx, artifactLevel->level);

#ifdef __Hotfix_Quest_Mission_Artifact_by_kangms_180822
			if (   0 < artifactLevel->count
				&& values >= artifactLevel->count) {
				theQuestSystem.OnArtifactLevel(player, artifactLevel->artifactIdx, artifactLevel->level);
			}
#else//__Hotfix_Quest_Mission_Artifact_by_kangms_180822
			if (values > 0)
			{
				if (values >= artifactLevel->count)
				{
					updateMission(questId, mission, values);
					checkQuestComplete(questId, mission, values);
					rewardQuestByItself(questId);
				}
				else
				{
					updateMission(questId, mission, values);
					sendMissionUpdate(questId, QuestFlag::PROGRESS, mission->index, values, false);
				}
			}
#endif//__Hotfix_Quest_Mission_Artifact_by_kangms_180822
		}
		else if (mission->type == QuestMissionType::CharacterLevel)
		{
			QuestCharacterLevel* characterLevel = static_cast<QuestCharacterLevel*>(mission);
			VALID_DO( characterLevel, continue );

			values = player->GetLevel();

#ifdef __Hotfix_Quest_Mission_Artifact_by_kangms_180822
			if (   0 < characterLevel->characterLevel
				&& values >= characterLevel->characterLevel ) {
				theQuestSystem.OnCharacterLevel(player, values);
			}
#else//__Hotfix_Quest_Mission_Artifact_by_kangms_180822
			if (values > 0)
			{
				if (values >= characterLevel->characterLevel)
				{
					updateMission(questId, mission, values);
					checkQuestComplete(questId, mission, values);
					rewardQuestByItself(questId);

				}
				else
				{
					updateMission(questId, mission, values);
					sendMissionUpdate(questId, QuestFlag::PROGRESS, mission->index, values, false);
				}
			}
#endif//__Hotfix_Quest_Mission_Artifact_by_kangms_180822
		}
#ifdef ABILITY_RENEWAL_20180508
		else if (mission->type == QuestMissionType::OpenBracelet)
		{
			QuestOpenBracelet* openBracelet = dynamic_cast<QuestOpenBracelet*>(mission);
			VALID_DO(openBracelet, continue);
			if (player->HasBracelet())
			{
				theQuestSystem.OnOpenBraceletSlot(player);
			}
		}
#endif // ABILITY_RENEWAL_20180508
	}
	
#ifdef __Patch_AutoGain_Quest_Check_by_cheolhoon_181008
	if(0 != elem->auto_gain_quest_id)
	{
		if(m_impl->questAutoGainCheckMap.end() == m_impl->questAutoGainCheckMap.find(elem->auto_gain_quest_id))
		{
			m_impl->questAutoGainCheckMap.insert(make_pair(elem->auto_gain_quest_id, elem->index));
		}
	}
#endif

	Log( LogCategory::QUEST, info.questId, info.progressFlag );

	return ErrorQuest::SUCCESS;
}

ErrorQuest::Error ActionPlayerQuest::failQuest(IndexQuest questId )
{
	EntityPlayer* player = GetOwnerPlayer();
	VALID_RETURN( player && player->IsValid(), ErrorQuest::E_FAILED );

	const QuestElem* elem = SCRIPTS.GetQuest( questId );
	VALID_RETURN( elem , ErrorQuest::E_QUEST_NOT_FOUND );

	VALID_RETURN( false == elem->missionVector.empty(), ErrorQuest::E_QUEST_UNKNOW );

	QuestMap::iterator it = m_impl->quests.find( questId );
	VALID_RETURN( it != m_impl->quests.end(), ErrorQuest::E_QUEST_NOT_FOUND );

	QuestInfo& questInfo = it->second;
	questInfo.progressFlag = QuestFlag::FAILED;

	// 미션 삭제
	removeMission( elem );
	
	// DB저장
	UpdateQuestToDB( questInfo );

	if ( elem->accept_item != 0 )
	{
		ErrorItem::Error eError = player->GetInventoryAction().RemoveByIndex(InvenBags, elem->accept_item, 1);
		VERIFY_DO(eError == ErrorItem::SUCCESS, MustErrorCheck);
	}

	checkLeaveSharedQuest( questId );
	GetSharedQuestSystem()->CompleteQuest( questId );

	checkDeactivate( elem );

	return ErrorQuest::SUCCESS;
}

void ActionPlayerQuest::checkJoinSharedQuest(IndexQuest questId )
{
	EntityPlayer* player = GetOwnerPlayer();
	VALID_RETURN( player && player->IsValid(), );

	SharedQuestSystem* sqs = GetSharedQuestSystem();
	if ( sqs )
	{
		sqs->JoinSharedQuest( questId, player );
	}
}

void ActionPlayerQuest::checkLeaveSharedQuest(IndexQuest questId )
{
	EntityPlayer* player = GetOwnerPlayer();
	VALID_RETURN( player && player->IsValid(), );

	SharedQuestSystem* sqs = GetSharedQuestSystem();
	if ( sqs )
	{
		sqs->LeaveSharedQuest( questId, player );
	}
}

#ifdef __Hotfix_Quest_Mission_Count_Modify_by_cheolhoon_20181105
ErrorQuest::Error ActionPlayerQuest::startMission(const QuestInfo& questInfo, QuestMission* targetMission, Short param /*= 0*/, Bool isFirstStart /*= true */)
{
	EntityPlayer* player = GetOwnerPlayer();
	VALID_RETURN(player && player->IsValid(), ErrorQuest::playerNotFound);

	VALID_RETURN(targetMission, ErrorQuest::E_QUEST_NOT_FOUND);

	static auto checkMissionCompleteBeforeStartMission = [](const QuestMissionType::Enum type) -> bool
	{
		switch(type)
		{
			// 미션 시작 전 미션완료 체크하지 않아야 될 것들 목록
		case QuestMissionType::CollectItem:
		case QuestMissionType::DeliverItem:
		case QuestMissionType::GuardNpc:
		case QuestMissionType::AcceptQuest:
		case QuestMissionType::CompleteQuest:
		case QuestMissionType::FinishQuest:
			return false;

		default:
			return true;
		}
	};

	if(checkMissionCompleteBeforeStartMission(targetMission->type))
	{
		if(theQuestSystem.CheckMissionComplete(player, questInfo, targetMission))
		{
			setQuestMissionDecorators(questInfo.questId, targetMission->index, QuesteMissionDecoratorState::SUCCESS);
			return ErrorQuest::startMissionFailedAlreadyCompleted;
		}
	}

	QuestMissionList* missionList = getMissionList(targetMission->type);
	VERIFY_RETURN(missionList, ErrorQuest::startMissionFailed);

#ifdef __Hotfix_Quest_Mission_Check_Duplication_by_cheolhoon_20181112
	for(auto& itr : *missionList)
	{
		if(itr.questId == questInfo.questId && itr.mission->type == targetMission->type && itr.mission->index == targetMission->index)
		{
			return ErrorQuest::startMissionFailedDuplicatedMission;
		}
	}
#endif

	VALID_RETURN(insertQuestMissionDecorators(questInfo.questId, targetMission, isFirstStart), ErrorQuest::startMissionFailed);

	Mission mission;
	mission.questId = questInfo.questId;
	mission.mission = targetMission;
	mission.missionValue = param;

	missionList->push_back(mission);

	return ErrorQuest::SUCCESS;
}
#else
Bool ActionPlayerQuest::startMission( const QuestInfo& questInfo, QuestMission* targetMission, Short param /*= 0*/, Bool isFirstStart /*= true */ ) 
{
	EntityPlayer* player = GetOwnerPlayer();
	VALID_RETURN( player && player->IsValid(), false );

	VALID_RETURN( targetMission, false );

	static auto checkMissionCompleteBeforeStartMission = []( const QuestMissionType::Enum type ) -> bool
	{
		switch ( type )
		{
		// 미션 시작 전 미션완료 체크하지 않아야 될 것들 목록
		case QuestMissionType::CollectItem:
		case QuestMissionType::DeliverItem:
		case QuestMissionType::GuardNpc:
		case QuestMissionType::AcceptQuest:
		case QuestMissionType::CompleteQuest:
		case QuestMissionType::FinishQuest:
			return false;

		default:
			return true;
		}
	};

	if ( checkMissionCompleteBeforeStartMission( targetMission->type ) )
	{
		if (theQuestSystem.CheckMissionComplete( player, questInfo, targetMission ) )
		{
			setQuestMissionDecorators(questInfo.questId, targetMission->index, QuesteMissionDecoratorState::SUCCESS);
			return true;
		}	
	}

	QuestMissionList* missionList = getMissionList( targetMission->type );
	VERIFY_RETURN( missionList, false );

	VALID_RETURN( insertQuestMissionDecorators( questInfo.questId, targetMission, isFirstStart ), false );

	Mission mission;
	mission.questId = questInfo.questId;
	mission.mission = targetMission;
	mission.missionValue = param;

	missionList->push_back( mission );

	return true;
}
#endif

void ActionPlayerQuest::removeMission( const QuestElem* elem )
{
	VERIFY_RETURN( elem, );
	for( auto& i : elem->missionVector )
	{
		removeMission( elem->index, i );
	}
}

Bool ActionPlayerQuest::removeMission(IndexQuest questId, QuestMission* targetMission )
{
	EntityPlayer* player = GetOwnerPlayer();
	VALID_RETURN( player && player->IsValid(), false );

	QuestMissionList* tempMisstionList = getMissionList( targetMission->type );
	VERIFY_RETURN( tempMisstionList, false );
	
	auto actInven = player->GetAction<ActionPlayerInventory>();
	VERIFY_RETURN( actInven, false );

	tempMisstionList->remove_if([&](const auto& mission) -> bool
	{
		return (mission.questId == questId && mission.mission->index == targetMission->index);
	});
	/*tempMisstionList->erase(std::remove_if(tempMisstionList->begin(), tempMisstionList->end(), [&](const auto& mission) -> bool
	{
		return (mission.questId == questId && mission.mission->index == targetMission->index);
	})
		, tempMisstionList->end());*/

	switch ( targetMission->type )
	{
	case QuestMissionType::ObjectCollectItem:
	{
		auto mission = static_cast< QuestObjectCollectItem* >( targetMission );
		auto itemCount = actInven->GetItemCount(InvenBags, mission->itemIndex );
		VERIFY_DO(ErrorItem::SUCCESS == actInven->RemoveByIndex(InvenBags, mission->itemIndex, std::min((Byte)itemCount, mission->count)), MustErrorCheck);
	}
	default:
		break;
	}

	EraseQuestMissionDecorators( questId, targetMission->index );
	return true;
}

Bool ActionPlayerQuest::updateMission(IndexQuest questId, QuestMission* mission, UShort value )
{
	QuestMissionList* tempMisstionList = getMissionList( mission->type );	
	VALID_RETURN( tempMisstionList, false );

	for(QuestMissionList::iterator it = tempMisstionList->begin(); it != tempMisstionList->end(); ++it)
	{
		if(it->questId == questId && it->mission->index == mission->index)
		{
			it->missionValue = value;
			return true;
		}
	}

	return false;
}

Bool ActionPlayerQuest::onQuestEvent(QuestEvent& qe, Bool bSharedEvent)
{
	ProcessMissionList list;

	// 요청받은 이벤트로 처리해야될 퀘스트 리스트를 얻는다.
	VALID_RETURN(getProcessibleEvent(qe, list, bSharedEvent), false);

	return processQuestEvent(qe, list);
}

Bool ActionPlayerQuest::getProcessibleEvent(QuestEvent& qe, ProcessMissionList& out, Bool bSharedEvent)
{
	QuestMissionList* missionList = getMissionList(qe.missionType);
	VALID_RETURN(missionList, false);

	// 이벤트에 의한 수행가능한 미션 리스트를 생성한다.
	for (auto& i : *missionList)
	{
		if (checkProcessible(qe, i, bSharedEvent))
		{
			out.push_back(&i);
		}
	}

	return true;
}




Bool ActionPlayerQuest::processQuestEvent( QuestEvent& qe, const ProcessMissionList& list )
{
	QuestMissionList* missionList = getMissionList( qe.missionType );
	VALID_RETURN( missionList, false );

	auto processor = getQuestProcessor( qe.eventType );
	VALID_RETURN( processor, false );

	for( auto& i : list )
	{
		if ( ( this->*processor )( *i, qe ) )
		{
			rewardQuestByItself( ( *i ).questId );
		}
	}

	purgeMissions( *missionList );
	return true;
}

Bool ActionPlayerQuest::checkProcessible(const QuestEvent& qe, const Mission& mission, Bool bSharedEvent)
{
	// 공통 내용 체크
	VALID_RETURN(checkProcessibleCommon(qe, mission), false);

	const QuestElem* elem = SCRIPTS.GetQuest(mission.questId);
	VALID_RETURN(elem, false);

	//! 혼자 진행(together = 0)인데, 공유받는 이벤트면 리턴한다.
	if (bSharedEvent && 0 == elem->together)
		return false;

#ifdef __Patch_Quest_ProgressType_Add_Period_by_cheolhoon_20181114
	QuestMap::iterator it = m_impl->quests.find(mission.questId);
	VALID_RETURN(it != m_impl->quests.end(), false);

	if(elem->missionStepping)
	{
		if(nullptr == mission.mission || it->second.currentStepMisionIndex != mission.mission->index)
		{
			return false;
		}
	}
	
	if(QuestProgressType::Period ==	 elem->progress_Type)	// 기간제 퀘스트의 경우 현재 진행중인 퀘스트가 기한을 넘겼는지 체크.
	{
		if(theQuestSystem.CheckPeriodQuestTimeOver(it->second.lastUpdateTime))
		{
#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);			
			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", mission.questId);
			SendSystemMsg(player, sysMsg);
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313

			return false;
		}
	}
#else
	if(elem->missionStepping)
	{
		QuestMap::iterator it = m_impl->quests.find(mission.questId);
		if(it != m_impl->quests.end())
		{
			if(nullptr == mission.mission || it->second.currentStepMisionIndex != mission.mission->index)
			{
				return false;
			}
		}
	}
#endif

	// 공통 체크외에 이벤트 별 추가 체크할 것이 있으면 이곳에서 체크한다.
	// 케이스가 적어서 이곳에서 처리한다.
	// 추후 케이스가 늘어나면 별도의 처리부로 빼서 진행한다.
	switch (qe.eventType)
	{
	case QuestEventType::KILL_MONSTER:
	{
		KillMonster* missionKillMon = static_cast<KillMonster*>(mission.mission);
		VALID_RETURN(missionKillMon, false);
	}
	break;

	case QuestEventType::DESTROY_OBJECT:
	{
		VALID_RETURN(0 < qe.questId, false);
	}
	break;
	}

	return true;
}



Bool ActionPlayerQuest::checkProcessibleCommon( const QuestEvent& qe, const Mission& mission )
{
	MU2_ASSERT( qe.missionType == mission.mission->type );

	if (qe.questId > 0)
	{
		VALID_RETURN( qe.questId == mission.questId, false );
	}

	if (qe.missionIndex > 0)
	{
		VALID_RETURN( qe.missionIndex == mission.mission->index, false );
	}

	return true;
}

Bool ActionPlayerQuest::onKillMonster( Mission& info, QuestEvent& qe )
{
	QeKillMonster& evt = static_cast< QeKillMonster& >( qe );
	KillMonster* missionKillMon = static_cast<KillMonster*>( info.mission );
	VERIFY_RETURN( missionKillMon, false );

	const QuestElem* elem = SCRIPTS.GetQuest( info.questId );
	VERIFY_RETURN( elem, false );

	Bool found = false;
	for ( const auto value : missionKillMon->monsterId )
	{
		if ( value > 0 && evt.monsterId == value )
		{
			found = true;
			break;
		}
	}

	VALID_RETURN( found, false );

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest( player, &questInfo );

			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);			
		}	

		return true;
	}
	else
	{
		info.missionValue += count;
		if(missionKillMon->monsterCount < info.missionValue)
		{
			info.missionValue = static_cast<UShort>(missionKillMon->monsterCount);
		}
	}	
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(missionKillMon->monsterCount > info.missionValue)
	{
		++info.missionValue;
	}
#else
	++info.missionValue;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313

	if ( isQuestComplete( info.questId, missionKillMon, info.missionValue, info.isRemove ) )
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem( this, info, evt, elem );
	sendMissionUpdate( info.questId, questFlag, missionKillMon->index, info.missionValue, info.isRemove );
	return true;
}

Bool ActionPlayerQuest::onVolumeIn( Mission& info, QuestEvent& qe )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), false );

	QeVolumeIn& evt = static_cast< QeVolumeIn& >( qe );
	VolumeIn* vol = static_cast<VolumeIn*>(info.mission);
	VALID_RETURN( vol, false );

	if ( !( evt.volumeId == vol->volumeId_0 || evt.volumeId == vol->volumeId_1 || evt.volumeId == vol->volumeId_2 ) )
	{
		return false;
	}

	// 해당 미션의 볼륨이면 진행 처리 

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

	ActionSector* act = GetEntityAction( player );
	VALID_RETURN( act, false );

	const QuestVolumeMatchElem* volumeElem = SCRIPTS.GetQuestVolume( evt.volumeId );
	VALID_RETURN( volumeElem, false );

#if ENABLE_AFTER_CHECK_VOLUME
	if ( !act->CheckInsideVolume( volumeElem->indexZone, volumeElem->volumeId ) )
	{
		theGameMsg.SendGameDebugMsg( player, L"%d 퀘/ %d미션 진행(정찰 vol:%d, cnt%d) / 실패", 
			info.questId, vol->index, evt.volumeId, info.missionValue );
		return false;
	}
#endif 
	const QuestElem* elem = SCRIPTS.GetQuest( info.questId );
	VALID_RETURN( elem, false);

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);
			
			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(vol->count < info.missionValue)
		{
			info.missionValue = vol->count;
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(vol->count > info.missionValue)
	{
		++info.missionValue;
	}
#else
	++info.missionValue;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313

	if ( isQuestComplete( info.questId, vol, info.missionValue, info.isRemove ) )
	{
		questFlag = QuestFlag::COMPLETE;
	}
	else
	{
		questFlag = QuestFlag::PROGRESS;
	}

	notifyToSharedSystem( this, info, evt, elem );

	sendMissionUpdate( info.questId, questFlag, vol->index, info.missionValue, info.isRemove );
	return true;
}	

Bool ActionPlayerQuest::onDialogNpc( Mission& info, QuestEvent& qe )
{
	QeDialogNpc& evt = static_cast< QeDialogNpc& >( qe );
	DialogNpc* dialogNpc = static_cast<DialogNpc*>(info.mission);
	VALID_RETURN( dialogNpc, false );

	if ( static_cast<IndexNpc>(dialogNpc->toNpc) == evt.npcId )
	{	
		const QuestElem* elem = SCRIPTS.GetQuest( info.questId );
		VERIFY_RETURN( elem, false);

		QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

		info.missionValue = 1;

		info.isRemove = true;

		if ( isQuestComplete( info.questId, dialogNpc, info.missionValue, info.isRemove ) )
		{
			questFlag = QuestFlag::COMPLETE;
		}
		else
		{
			questFlag = QuestFlag::PROGRESS;
		}

		notifyToSharedSystem( this, info, evt, elem );

		sendMissionUpdate( info.questId, questFlag, dialogNpc->index, info.missionValue, info.isRemove );
	}

	return true;
}

Bool ActionPlayerQuest::onDestroyObj( Mission& info, QuestEvent& qe )
{
	QeDestroyObject& evt = static_cast< QeDestroyObject& >( qe );
	QuestDestroyObj* destroyObj = static_cast<QuestDestroyObj*>(info.mission);
	VALID_RETURN( destroyObj, false );

	if (evt.objectId == destroyObj->objectId[0] || destroyObj->objectId[1] || destroyObj->objectId[2])
	{
		QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

		const QuestElem* elem = SCRIPTS.GetQuest(info.questId);
		VERIFY_RETURN(elem, false);

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
		UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
		if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
		{
			QuestMap::iterator it = m_impl->quests.find(info.questId);
			if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
			{
				// 진행불가로 처음 처리할 때만 클라로 알려줌.
				EntityPlayer* player = GetOwnerPlayer();
				VALID_RETURN(player && player->IsValid(), false);

				QuestInfo& questInfo = it->second;
				questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
				SendUpdateQuest(player, &questInfo);
				
				SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
				sysMsg.SetParam(L"quest", questInfo.questId);
				SendSystemMsg(player, sysMsg);
			}

			return true;
		}
		else
		{
			info.missionValue += count;
			if(destroyObj->count < info.missionValue)
			{
				info.missionValue = destroyObj->count;
			}
		}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
		if(destroyObj->count > info.missionValue)
		{
			++info.missionValue;
		}
#else
		++info.missionValue;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313

		if ( isQuestComplete( info.questId, destroyObj, info.missionValue, info.isRemove ) )
		{
			questFlag = QuestFlag::COMPLETE;
		}

		notifyToSharedSystem(this, info, evt, elem);

		sendMissionUpdate(info.questId, questFlag, destroyObj->index, info.missionValue, info.isRemove);

	}

	return true;
}

Bool ActionPlayerQuest::onOperateObject( Mission& info, QuestEvent& qe )
{	
	QeOperateObject& evt = static_cast< QeOperateObject& >( qe );

	VERIFY_RETURN( 0 < evt.objectId, false );

	QuestOperateObj* operateObj = static_cast<QuestOperateObj*>( info.mission );
	VERIFY_RETURN( operateObj, false );

	Bool found = false;
	for ( const auto value : operateObj->objectId )
	{
		if( value == 0 || evt.objectId != value )
		{
			continue;
		}
		found = true;
		break;
	}

	VALID_RETURN( found, false );

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

	const QuestElem* elem = SCRIPTS.GetQuest( info.questId );
	VERIFY_RETURN( elem, false );

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);
			
			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(operateObj->count < info.missionValue)
		{
			info.missionValue = operateObj->count;
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(operateObj->count > info.missionValue)
	{
		++info.missionValue;
	}
#else
	++info.missionValue;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313

	if ( isQuestComplete( info.questId, operateObj, info.missionValue, info.isRemove ) )
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem( this, info, evt, elem );

	sendMissionUpdate( info.questId, questFlag, operateObj->index, info.missionValue, info.isRemove );

	return true;
}

Bool ActionPlayerQuest::onUseItem( Mission& info, QuestEvent& qe )
{
	QeUseItem& evt = static_cast< QeUseItem& >( qe );

	QuestUseItem* useItem = static_cast<QuestUseItem*>( info.mission );
	VALID_RETURN( useItem, false );
	VALID_RETURN( evt.itemType == useItem->itemType, false );

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

	const QuestElem* elem = SCRIPTS.GetQuest( info.questId );
	VALID_RETURN( elem, false);

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);
			
			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(useItem->count < info.missionValue)
		{
			info.missionValue = static_cast<UShort>(evt.count);
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(useItem->count > info.missionValue)
	{
		info.missionValue += (UShort)evt.count;
	}
#else
	info.missionValue += (UShort)evt.count;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313

	if ( isQuestComplete( info.questId, useItem, info.missionValue, info.isRemove ) )
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem( this, info, evt, elem );

	sendMissionUpdate( info.questId, questFlag, useItem->index, info.missionValue, info.isRemove );

	return true;
}

Bool ActionPlayerQuest::onExchangeItem( Mission& info, QuestEvent& qe )
{
	QeExchangeItem& evt = static_cast< QeExchangeItem& >( qe );

	QuestExchangeItem* exchangeItem = static_cast<QuestExchangeItem*>( info.mission );
	VALID_RETURN( exchangeItem, false );

	VALID_RETURN( evt.itemType == exchangeItem->itemType_Target, false );

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

	const QuestElem* elem = SCRIPTS.GetQuest( info.questId );
	VALID_RETURN( elem, false);

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);

			
			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(exchangeItem->count < info.missionValue)
		{
			info.missionValue = exchangeItem->count;
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(exchangeItem->count > info.missionValue)
	{
		++info.missionValue;
	}
#else
	++info.missionValue;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313

	if ( isQuestComplete( info.questId, exchangeItem, info.missionValue, info.isRemove ) )
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem( this, info, evt, elem );

	sendMissionUpdate( info.questId, questFlag, exchangeItem->index, info.missionValue, info.isRemove );

	return true;
}

Bool ActionPlayerQuest::onCollectItem( Mission& info, QuestEvent& qe )
{
	QeCollectItem& evt = static_cast< QeCollectItem& >( qe );

	QuestGatherItem* gatherItem = static_cast<QuestGatherItem*>( info.mission );

	VALID_RETURN( gatherItem, false );

	if ( !(evt.objectId == gatherItem->objectId && evt.indexItem == gatherItem->indexItem) )
	{
		return false;
	}

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

	const QuestElem* elem = SCRIPTS.GetQuest( info.questId );
	VERIFY_RETURN( elem, false);

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);
			
			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(gatherItem->count < info.missionValue)
		{
			info.missionValue = gatherItem->count;
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(gatherItem->count > info.missionValue)
	{
		++info.missionValue;
	}
#else
	++info.missionValue;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313

	if ( isQuestComplete( info.questId, gatherItem, info.missionValue, info.isRemove ) )
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem( this, info, evt, elem );

	sendMissionUpdate( info.questId, questFlag, gatherItem->index, info.missionValue, info.isRemove );
	return true;
}

Bool ActionPlayerQuest::onDropItem( Mission& info, QuestEvent& qe )
{
	QeDropItem& evt = static_cast< QeDropItem& >( qe );

	CollectItem* collectItem = static_cast<CollectItem*>( info.mission );
	VERIFY_RETURN( collectItem, false );

	// 아이템 드랍 처리 후 진행 
	bool drop = ( mu2::GetRandBetween( 1, DEFAULT_RATE) <= collectItem->prob ) ? true : false;

	if ( drop && 
		(	collectItem->indexMonster[0] == evt.indexNpc || 
			collectItem->indexMonster[1] == evt.indexNpc || 
			collectItem->indexMonster[2] == evt.indexNpc ) )
	{
		const QuestElem* elem = SCRIPTS.GetQuest( info.questId );
		VERIFY_RETURN( elem, false);

		QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
		UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
		if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
		{
			QuestMap::iterator it = m_impl->quests.find(info.questId);
			if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
			{
				// 진행불가로 처음 처리할 때만 클라로 알려줌.
				EntityPlayer* player = GetOwnerPlayer();
				VALID_RETURN(player && player->IsValid(), false);

				QuestInfo& questInfo = it->second;
				questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
				SendUpdateQuest(player, &questInfo);
				
				SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
				sysMsg.SetParam(L"quest", questInfo.questId);
				SendSystemMsg(player, sysMsg);
			}

			return true;
		}
		else
		{
			info.missionValue += count;
			if(collectItem->itemCount < info.missionValue)
			{
				info.missionValue = static_cast<UShort>(collectItem->itemCount);
			}
		}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
		if(collectItem->itemCount > info.missionValue)
		{
			++info.missionValue;
		}
#else
		info.missionValue++;	// 한번에 하나씩 드랍
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313

		if ( isQuestComplete( info.questId, collectItem, info.missionValue, info.isRemove ) )
		{
			questFlag = QuestFlag::COMPLETE;
		}

		notifyToSharedSystem( this, info, evt, elem );

		sendMissionUpdate( info.questId, questFlag, collectItem->index, info.missionValue, info.isRemove );
		return true;
	}

	return false;
}

Bool ActionPlayerQuest::onDeliverItem( Mission& info, QuestEvent& qe )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), false );

	QeDeliverItem& evt = static_cast< QeDeliverItem& >( qe );

	DeliverItem* deliverItem = static_cast<DeliverItem*>(info.mission);
	VALID_RETURN( deliverItem, false );
	VALID_RETURN( deliverItem->toNpc == evt.npcId, false );

	ActionPlayerInventory* actPlayerInven = GetEntityAction( player );
	VERIFY_RETURN( actPlayerInven, false );

	const QuestElem* elem = SCRIPTS.GetQuest( info.questId );
	VALID_RETURN( elem, false);
	VALID_RETURN( actPlayerInven->IsEnoughItem( InvenBags, deliverItem->indexItem, deliverItem->itemCount ), false );

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);
			
			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(deliverItem->itemCount < info.missionValue)
		{
			info.missionValue = static_cast<UShort>(deliverItem->itemCount);
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(deliverItem->itemCount > info.missionValue)
	{
		info.missionValue = static_cast<UInt16>(deliverItem->itemCount);
	}
#else
	info.missionValue = static_cast<UInt16>(deliverItem->itemCount);
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313

	if ( isQuestComplete( info.questId, deliverItem, info.missionValue, info.isRemove ) )
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem( this, info, evt, elem );

	VERIFY_DO(ErrorItem::SUCCESS == actPlayerInven->RemoveByIndex(InvenBags, deliverItem->indexItem, deliverItem->itemCount), MustErrorCheck);

	sendMissionUpdate( info.questId, questFlag, deliverItem->index, info.missionValue, info.isRemove );

	return true;
}

Bool ActionPlayerQuest::onAdvMission( Mission& info, QuestEvent& qe )
{
	QeAdvMission& evt = static_cast< QeAdvMission& >( qe );

	QuestAdvMission* questAdvMission = static_cast<QuestAdvMission*>(info.mission);
	VALID_RETURN( questAdvMission, false );

	if ( questAdvMission->difficulty != 0 && evt.difficulty < questAdvMission->difficulty )
	{	
		return false; 
	}	

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);
			
			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(questAdvMission->count < info.missionValue)
		{
			info.missionValue = static_cast<UShort>(questAdvMission->count);
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(questAdvMission->count > info.missionValue)
	{
		info.missionValue += 1;
	}
#else
	info.missionValue += 1;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313

	const QuestElem* elem = SCRIPTS.GetQuest( info.questId );
	VERIFY_RETURN( elem , false);

	if ( isQuestComplete( info.questId, questAdvMission, info.missionValue, info.isRemove ) )
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem( this, info, evt, elem );

	sendMissionUpdate( info.questId, questFlag, questAdvMission->index, info.missionValue, info.isRemove );

	return true;
}

Bool ActionPlayerQuest::onDeadlineOver( Mission& info, QuestEvent& qe )
{	
	QeDealineOver& evt = static_cast< QeDealineOver& >( qe );

	QuestGuardNpc* guardNpc = static_cast<QuestGuardNpc*>( info.mission );
	VERIFY_RETURN( guardNpc, false );
	
	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

	const QuestElem* elem = SCRIPTS.GetQuest( info.questId );
	VERIFY_RETURN( elem, false );
	
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	info.missionValue = 1;
#else
	++info.missionValue;
#endif

	// 미션 성패에 관계 없이 지움.
	info.isRemove = true; 

	if ( isQuestComplete( info.questId, guardNpc, info.missionValue, info.isRemove ) )
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem( this, info, evt, elem );

	sendMissionUpdate( info.questId, questFlag, guardNpc->index, info.missionValue, info.isRemove );
		
	return true;
}

Bool ActionPlayerQuest::onAdvType( Mission& info, QuestEvent& qe )
{
	QeAdvType& evt = static_cast< QeAdvType& >( qe );

	QuestAdvType* mission = static_cast<QuestAdvType*>(info.mission);
	VERIFY_RETURN( mission, false );

	VALID_RETURN( mission->difficulty == 0 || mission->difficulty <= evt.difficulty, false );
	VALID_RETURN( mission->advType == 0 || evt.advType == mission->advType, false );

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);
			
			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(mission->count < info.missionValue)
		{
			info.missionValue = static_cast<UShort>(mission->count);
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(mission->count > info.missionValue)
	{
		info.missionValue += 1;
	}
#else
	info.missionValue += 1;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313

	const QuestElem* elem = SCRIPTS.GetQuest( info.questId );
	VERIFY_RETURN( elem , false);

	if ( isQuestComplete( info.questId, mission, info.missionValue, info.isRemove ) )
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem( this, info, evt, elem );

	sendMissionUpdate( info.questId, questFlag, mission->index, info.missionValue, info.isRemove );

	return true;
}

Bool ActionPlayerQuest::onAdvFloor( Mission& info, QuestEvent& qe )
{
	QeAdvFloor& evt = static_cast< QeAdvFloor& >( qe );

	QuestAdvFloor* mission = static_cast<QuestAdvFloor*>(info.mission);

	VALID_RETURN( mission, false );
	VALID_RETURN( mission->floor <= evt.advFloor, false );

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);
			
			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(mission->count < info.missionValue)
		{
			info.missionValue = static_cast<UShort>(mission->count);
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(mission->count > info.missionValue)
	{
		info.missionValue += 1;
	}
#else
	info.missionValue += 1;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313

	const QuestElem* elem = SCRIPTS.GetQuest( info.questId );
	VALID_RETURN( elem, false);

	if ( isQuestComplete( info.questId, mission, info.missionValue, info.isRemove ) )
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem( this, info, evt, elem );

	sendMissionUpdate( info.questId, questFlag, mission->index, info.missionValue, info.isRemove );

	return true;
}

Bool ActionPlayerQuest::onAdvKillMonster( Mission& info, QuestEvent& qe )
{
	QeAdvKillMonster& evt = static_cast< QeAdvKillMonster& >( qe );

	QuestAdvKillMonster* mission = static_cast<QuestAdvKillMonster*>(info.mission);
	VALID_RETURN( mission, false );

	VALID_RETURN( mission->difficulty == 0 || mission->difficulty <= evt.difficulty, false );
	VALID_RETURN( mission->advType == 0 || evt.advType == mission->advType, false );
	VALID_RETURN( mission->grade == 0 || mission->grade <= evt.grade, false );

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);
			
			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(mission->count < info.missionValue)
		{
			info.missionValue = static_cast<UShort>(mission->count);
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(mission->count > info.missionValue)
	{
		info.missionValue += 1;
	}
#else
	info.missionValue += 1;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313

	const QuestElem* elem = SCRIPTS.GetQuest( info.questId );
	VALID_RETURN( elem, false);

	if ( isQuestComplete( info.questId, mission, info.missionValue, info.isRemove ) )
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem( this, info, evt, elem );

	sendMissionUpdate( info.questId, questFlag, mission->index, info.missionValue, info.isRemove );

	return true;
}

Bool ActionPlayerQuest::onAcceptQuest( Mission& info, QuestEvent& qe )
{
	QeAcceptQuest& evt = static_cast< QeAcceptQuest& >( qe );

	QuestAcceptQuest* mission = static_cast< QuestAcceptQuest* >( info.mission );
	VALID_RETURN( mission, false );

	Bool found = false;
	for ( const auto& value : mission->checkQuest )
	{
		if ( value > 0 && value == evt.questIndex )
		{
			found = true;
			break;
		}
	}

	VALID_RETURN( found, false );

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);
			
			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(mission->count < info.missionValue)
		{
			info.missionValue = mission->count;
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(mission->count > info.missionValue)
	{
		info.missionValue += 1;
	}
#else
	info.missionValue += 1;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

	const QuestElem* elem = SCRIPTS.GetQuest( info.questId );
	VERIFY_RETURN( elem, false );

	if ( isQuestComplete( info.questId, mission, info.missionValue, info.isRemove ) )
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem( this, info, evt, elem );

	sendMissionUpdate( info.questId, questFlag, mission->index, info.missionValue, info.isRemove );

	return true;
}

Bool ActionPlayerQuest::onCompleteQuest( Mission& info, QuestEvent& qe )
{
	QeCompeteQuest& evt = static_cast< QeCompeteQuest& >( qe );

	QuestCompleteQuest* mission = static_cast< QuestCompleteQuest* >( info.mission );
	VALID_RETURN( mission, false );

	Bool found = false;
	for ( const auto& value : mission->checkQuest )
	{
		if ( value > 0 && value == evt.questIndex )
		{
			found = true;
			break;
		}
	}

	VALID_RETURN( found, false );

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);
			
			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(mission->count < info.missionValue)
		{
			info.missionValue = mission->count;
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(mission->count > info.missionValue)
	{
		info.missionValue += 1;
	}
#else
	info.missionValue += 1;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

	const QuestElem* elem = SCRIPTS.GetQuest( info.questId );
	VERIFY_RETURN( elem, false );

	if ( isQuestComplete( info.questId, mission, info.missionValue, info.isRemove ) )
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem( this, info, evt, elem );

	sendMissionUpdate( info.questId, questFlag, mission->index, info.missionValue, info.isRemove );

	return true;
}

Bool ActionPlayerQuest::onFinishQuest( Mission& info, QuestEvent& qe )
{
	QeFinishQuest& evt = static_cast< QeFinishQuest& >( qe );

	QuestFinishQuest* mission = static_cast< QuestFinishQuest* >( info.mission );

	VALID_RETURN( mission, false );

	Bool found = false;
	for ( const auto& value : mission->checkQuest )
	{
		if ( value > 0 && value == evt.questIndex )
		{
			found = true;
			break;
		}
	}

	VALID_RETURN( found, false );

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);
			
			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(mission->count < info.missionValue)
		{
			info.missionValue = mission->count;
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(mission->count > info.missionValue)
	{
		info.missionValue += 1;
	}
#else
	info.missionValue += 1;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

	const QuestElem* elem = SCRIPTS.GetQuest( info.questId );
	VERIFY_RETURN( elem, false );

	if ( isQuestComplete( info.questId, mission, info.missionValue, info.isRemove ) )
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem( this, info, evt, elem );

	NotifyAdvanture(info.questId);

	sendMissionUpdate( info.questId, questFlag, mission->index, info.missionValue, info.isRemove );

	return true;
}

Bool ActionPlayerQuest::onCastleKillMonster( Mission& info, QuestEvent& qe )
{
	QeCastleKillMonster& evt = static_cast< QeCastleKillMonster& >( qe );

	QuestCastleKillMonster* mission = static_cast< QuestCastleKillMonster* >( info.mission );
	VALID_RETURN( mission, false );
	VALID_RETURN( mission->grade == 0 || mission->grade <= evt.grade, false );

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);

			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(mission->count < info.missionValue)
		{
			info.missionValue = static_cast<UShort>(mission->count);
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(mission->count > info.missionValue)
	{
		info.missionValue += 1;
	}
#else
	info.missionValue += 1;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313

	const QuestElem* elem = SCRIPTS.GetQuest( info.questId );
	VERIFY_RETURN( elem, false );

	if ( isQuestComplete( info.questId, mission, info.missionValue, info.isRemove ) )
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem( this, info, evt, elem );

	sendMissionUpdate( info.questId, questFlag, mission->index, info.missionValue, info.isRemove );

	return true;
}

Bool ActionPlayerQuest::onCastleClear( Mission& info, QuestEvent& qe )
{
	QeCastleClear& evt = static_cast< QeCastleClear& >( qe );

	QuestCastleClear* mission = static_cast< QuestCastleClear* >( info.mission );
	VERIFY_RETURN( mission, false );
	VALID_RETURN( mission->clearTick == 0 || evt.clearTick <= mission->clearTick, false );

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);

			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(mission->count < info.missionValue)
		{
			info.missionValue = static_cast<UShort>(mission->count);
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(mission->count > info.missionValue)
	{
		info.missionValue += 1;
	}
#else
	info.missionValue += 1;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313

	const QuestElem* elem = SCRIPTS.GetQuest( info.questId );
	VERIFY_RETURN( elem, false );

	if ( isQuestComplete( info.questId, mission, info.missionValue, info.isRemove ) )
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem( this, info, evt, elem );

	sendMissionUpdate( info.questId, questFlag, mission->index, info.missionValue, info.isRemove );

	return true;
}

Bool ActionPlayerQuest::onTowerStageClear( Mission& info, QuestEvent& qe )
{
	QeTowerStageClear& evt = static_cast< QeTowerStageClear& >( qe );

	QuestTowerStageClear* mission = static_cast< QuestTowerStageClear* >( info.mission );
	VALID_RETURN( mission, false );

	VALID_RETURN( mission->stage <= evt.stage, false );
	VALID_RETURN( mission->clearTick == 0 || mission->clearTick >= evt.clearTick, false );

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);

			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(mission->count < info.missionValue)
		{
			info.missionValue = static_cast<UShort>(mission->count);
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(mission->count > info.missionValue)
	{
		info.missionValue += 1;
	}
#else
	info.missionValue += 1;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313

	const QuestElem* elem = SCRIPTS.GetQuest( info.questId );
	VERIFY_RETURN( elem, false );

	if ( isQuestComplete( info.questId, mission, info.missionValue, info.isRemove ) )
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem( this, info, evt, elem );

	sendMissionUpdate( info.questId, questFlag, mission->index, info.missionValue, info.isRemove );

	return true;
}

Bool ActionPlayerQuest::onTowerKillMonster( Mission& info, QuestEvent& qe )
{
	QeTowerKillMonster& evt = static_cast< QeTowerKillMonster& >( qe );

	QuestTowerKillMonster* mission = static_cast< QuestTowerKillMonster* >( info.mission );
	VALID_RETURN( mission, false );
	VALID_RETURN( mission->grade == 0 || mission->grade <= evt.grade, false );

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);

			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(mission->count < info.missionValue)
		{
			info.missionValue = static_cast<UShort>(mission->count);
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(mission->count > info.missionValue)
	{
		info.missionValue += 1;
	}
#else
	info.missionValue += 1;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313

	const QuestElem* elem = SCRIPTS.GetQuest( info.questId );
	VERIFY_RETURN( elem, false );

	if ( isQuestComplete( info.questId, mission, info.missionValue, info.isRemove ) )
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem( this, info, evt, elem );

	sendMissionUpdate( info.questId, questFlag, mission->index, info.missionValue, info.isRemove );

	return true;
}

Bool ActionPlayerQuest::onTowerStagePass( Mission& info, QuestEvent& qe )
{
	QeTowerStagePass& evt = static_cast< QeTowerStagePass& >( qe );

	QuestTowerStagePass* mission = static_cast< QuestTowerStagePass* >( info.mission );
	VALID_RETURN( mission, false );
	VALID_RETURN( mission->passedStage <= evt.passedStage, false );

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);

			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(mission->count < info.missionValue)
		{
			info.missionValue = static_cast<UShort>(mission->count);
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(mission->count > info.missionValue)
	{
		info.missionValue += 1;
	}
#else
	info.missionValue += 1;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313	

	const QuestElem* elem = SCRIPTS.GetQuest( info.questId );
	VERIFY_RETURN( elem, false );

	if ( isQuestComplete( info.questId, mission, info.missionValue, info.isRemove ) )
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem( this, info, evt, elem );

	sendMissionUpdate( info.questId, questFlag, mission->index, info.missionValue, info.isRemove );

	return true;
}
#ifdef __Patch_Tower_of_dawn_eunseok_2018_11_05
Bool ActionPlayerQuest::onDawnTowerStageClear(Mission& info, QuestEvent& qe)
{
	QeDawnTowerStageClear& evt = static_cast<QeDawnTowerStageClear&>(qe);

	QuestDawnTowerStageClear* mission = static_cast<QuestDawnTowerStageClear*>(info.mission);
	VALID_RETURN(mission, false);

	VALID_RETURN(mission->stage <= evt.stage, false);
	VALID_RETURN(mission->clearTick == 0 || mission->clearTick >= evt.clearTick, false);

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);

			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(mission->count < info.missionValue)
		{
			info.missionValue = static_cast<UShort>(mission->count);
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(mission->count > info.missionValue)
	{
		info.missionValue += 1;
	}
#else
	info.missionValue += 1;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313	

	const QuestElem* elem = SCRIPTS.GetQuest(info.questId);
	VERIFY_RETURN(elem, false);

	if (isQuestComplete(info.questId, mission, info.missionValue, info.isRemove))
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem(this, info, evt, elem);

	sendMissionUpdate(info.questId, questFlag, mission->index, info.missionValue, info.isRemove);
	return true;
}
Bool ActionPlayerQuest::onDawnTowerStagePass(Mission& info, QuestEvent& qe)
{
	QeDawnTowerStagePass& evt = static_cast<QeDawnTowerStagePass&>(qe);

	QuestDawnTowerStagePass* mission = static_cast<QuestDawnTowerStagePass*>(info.mission);
	VALID_RETURN(mission, false);
	VALID_RETURN(mission->passedStage <= evt.passedStage, false);

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);

			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(mission->count < info.missionValue)
		{
			info.missionValue = static_cast<UShort>(mission->count);
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(mission->count > info.missionValue)
	{
		info.missionValue += 1;
	}
#else
	info.missionValue += 1;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313	

	const QuestElem* elem = SCRIPTS.GetQuest(info.questId);
	VERIFY_RETURN(elem, false);

	if (isQuestComplete(info.questId, mission, info.missionValue, info.isRemove))
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem(this, info, evt, elem);

	sendMissionUpdate(info.questId, questFlag, mission->index, info.missionValue, info.isRemove);
	return true;
}
#endif// __Patch_Tower_of_dawn_eunseok_2018_11_05
Bool ActionPlayerQuest::onCollectItemB( Mission& info, QuestEvent& qe )
{
	QeCollectItemB& evt = static_cast< QeCollectItemB& >( qe );

	QuestCollectItemB* mission = static_cast< QuestCollectItemB* >( info.mission );
	VALID_RETURN( mission, false );
	VALID_RETURN( evt.itemIndex == mission->itemIndex, false );

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);

			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(mission->count < info.missionValue)
		{
			info.missionValue = mission->count;
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(mission->count > info.missionValue)
	{
		info.missionValue += 1;
	}
#else
	info.missionValue += 1;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313	

	const QuestElem* elem = SCRIPTS.GetQuest( info.questId );
	VERIFY_RETURN( elem, false );

	if ( isQuestComplete( info.questId, mission, info.missionValue, info.isRemove ) )
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem( this, info, evt, elem );

	sendMissionUpdate( info.questId, questFlag, mission->index, info.missionValue, info.isRemove );
	
	return true;
}

Bool ActionPlayerQuest::onObjectCollectItem( Mission& info, QuestEvent& qe )
{
	QeObjectCollectItem& evt = static_cast< QeObjectCollectItem& >( qe );

	QuestObjectCollectItem* mission = static_cast< QuestObjectCollectItem* >( info.mission );
	VALID_RETURN( mission, false );
	VALID_RETURN( evt.itemIndex == mission->itemIndex, false );

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);

			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(mission->count < info.missionValue)
		{
			info.missionValue = static_cast<UShort>(mission->count);
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(mission->count > info.missionValue)
	{
		info.missionValue += 1;
	}
#else
	info.missionValue += 1;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313	

	const QuestElem* elem = SCRIPTS.GetQuest( info.questId );
	VERIFY_RETURN( elem, false );

	if ( isQuestComplete( info.questId, mission, info.missionValue, info.isRemove ) )
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem( this, info, evt, elem );

	sendMissionUpdate( info.questId, questFlag, mission->index, info.missionValue, info.isRemove );

	return true;
}

Bool ActionPlayerQuest::onProtectNpc( Mission& info, QuestEvent& qe )
{
	QeProtectNpc& evt = static_cast<QeProtectNpc&>(qe);

	QuestProtectNpc* mission = static_cast<QuestProtectNpc*>(info.mission);
	VALID_RETURN( mission, false );
	VALID_RETURN( evt.npcIndex == mission->npcIndex, false );

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);

			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(mission->count < info.missionValue)
		{
			info.missionValue = mission->count;
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(mission->count > info.missionValue)
	{
		info.missionValue += 1;
	}
#else
	info.missionValue += 1;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313	

	const QuestElem* elem = SCRIPTS.GetQuest(info.questId);
	VERIFY_RETURN(elem, false);

	if (isQuestComplete(info.questId, mission, info.missionValue, info.isRemove))
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem(this, info, evt, elem);

	sendMissionUpdate(info.questId, questFlag, mission->index, info.missionValue, info.isRemove);
	return true;
}

Bool ActionPlayerQuest::onEscortNpcA( Mission& info, QuestEvent& qe )
{
	QeEscortNpcA& evt = static_cast<QeEscortNpcA&>( qe );

	QuestEscortNpcA* mission = static_cast<QuestEscortNpcA*>( info.mission );
	VALID_RETURN( mission, false );
	VALID_RETURN( evt.npcIndex == mission->npcIndex, false );
	VALID_RETURN( evt.questVolumeIndex == mission->questVolumeIndex, false );

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);

			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(mission->count < info.missionValue)
		{
			info.missionValue = mission->count;
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(mission->count > info.missionValue)
	{
		info.missionValue += 1;
	}
#else
	info.missionValue += 1;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313	

	const QuestElem* elem = SCRIPTS.GetQuest( info.questId );
	VERIFY_RETURN( elem, false );

	if ( isQuestComplete( info.questId, mission, info.missionValue, info.isRemove ) )
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem( this, info, evt, elem );

	sendMissionUpdate( info.questId, questFlag, mission->index, info.missionValue, info.isRemove );
	return true;
}

Bool ActionPlayerQuest::onEscortNpcB( Mission& info, QuestEvent& qe )
{
	QeEscortNpcB& evt = static_cast<QeEscortNpcB&>( qe );

	QuestEscortNpcB* mission = static_cast<QuestEscortNpcB*>( info.mission );
	VALID_RETURN( mission, false );
	VALID_RETURN( evt.npcIndex == mission->npcIndex, false );

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);

			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(mission->count < info.missionValue)
		{
			info.missionValue = mission->count;
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(mission->count > info.missionValue)
	{
		info.missionValue += 1;
	}
#else
	info.missionValue += 1;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313	

	const QuestElem* elem = SCRIPTS.GetQuest( info.questId );
	VERIFY_RETURN( elem, false );

	if ( isQuestComplete( info.questId, mission, info.missionValue, info.isRemove ) )
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem( this, info, evt, elem );

	sendMissionUpdate( info.questId, questFlag, mission->index, info.missionValue, info.isRemove );
	return true;
}

Bool ActionPlayerQuest::onSurvival( Mission& info, QuestEvent& qe )
{
	QeSurvival& evt = static_cast<QeSurvival&>( qe );

	QuestSurvival* mission = static_cast<QuestSurvival*>( info.mission );
	VALID_RETURN( mission, false );

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);

			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(mission->count < info.missionValue)
		{
			info.missionValue = mission->count;
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(mission->count > info.missionValue)
	{
		info.missionValue += 1;
	}
#else
	info.missionValue += 1;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313	

	const QuestElem* elem = SCRIPTS.GetQuest( info.questId );
	VERIFY_RETURN( elem, false );

	if ( isQuestComplete( info.questId, mission, info.missionValue, info.isRemove ) )
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem( this, info, evt, elem );

	sendMissionUpdate( info.questId, questFlag, mission->index, info.missionValue, info.isRemove );
	return true;
}

Bool ActionPlayerQuest::onItemCreate( Mission& info, QuestEvent& qe )
{
	QeItemCreate& evt = static_cast< QeItemCreate& >( qe );

	QuestItemCreate* mission = static_cast< QuestItemCreate* >( info.mission );
	VALID_RETURN( mission, false );

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);

			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(mission->count < info.missionValue)
		{
			info.missionValue = mission->count;
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(mission->count > info.missionValue)
	{
		info.missionValue += 1;
	}
#else
	info.missionValue += 1;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313	

	const QuestElem* elem = SCRIPTS.GetQuest( info.questId );
	VERIFY_RETURN( elem, false );

	if ( isQuestComplete( info.questId, mission, info.missionValue, info.isRemove ) )
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem( this, info, evt, elem );

	sendMissionUpdate( info.questId, questFlag, mission->index, info.missionValue, info.isRemove );
	return true;
}

Bool ActionPlayerQuest::onItemExtract( Mission& info, QuestEvent& qe )
{
	QeItemExtract& evt = static_cast< QeItemExtract& >( qe );

	QuestItemExtract* mission = static_cast< QuestItemExtract* >( info.mission );
	VALID_RETURN( mission, false );

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;
	
#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);

			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(mission->count < info.missionValue)
		{
			info.missionValue = mission->count;
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(mission->count > info.missionValue)
	{
		info.missionValue += 1;
	}
#else
	info.missionValue += 1;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313	

	const QuestElem* elem = SCRIPTS.GetQuest( info.questId );
	VERIFY_RETURN( elem, false );

	if ( isQuestComplete( info.questId, mission, info.missionValue, info.isRemove ) )
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem( this, info, evt, elem );

	sendMissionUpdate( info.questId, questFlag, mission->index, info.missionValue, info.isRemove );
	return true;
}

Bool ActionPlayerQuest::onItemSocketAdd( Mission& info, QuestEvent& qe )
{
	QeItemSocketAdd& evt = static_cast< QeItemSocketAdd& >( qe );

	QuestItemSocketAdd* mission = static_cast< QuestItemSocketAdd* >( info.mission );
	VALID_RETURN( mission, false );

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;
	
#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);

			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(mission->count < info.missionValue)
		{
			info.missionValue = mission->count;
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(mission->count > info.missionValue)
	{
		info.missionValue += 1;
	}
#else
	info.missionValue += 1;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313	

	const QuestElem* elem = SCRIPTS.GetQuest( info.questId );
	VERIFY_RETURN( elem, false );

	if ( isQuestComplete( info.questId, mission, info.missionValue, info.isRemove ) )
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem( this, info, evt, elem );

	sendMissionUpdate( info.questId, questFlag, mission->index, info.missionValue, info.isRemove );
	return true;
}

Bool ActionPlayerQuest::onItemSocketEquip( Mission& info, QuestEvent& qe )
{
	QeItemSocketEquip& evt = static_cast< QeItemSocketEquip& >( qe );

	QuestItemSocketEquip* mission = static_cast< QuestItemSocketEquip* >( info.mission );
	VALID_RETURN( mission, false );

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;
	
#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);

			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(mission->count < info.missionValue)
		{
			info.missionValue = mission->count;
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(mission->count > info.missionValue)
	{
		info.missionValue += 1;
	}
#else
	info.missionValue += 1;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313	

	const QuestElem* elem = SCRIPTS.GetQuest( info.questId );
	VERIFY_RETURN( elem, false );

	if ( isQuestComplete( info.questId, mission, info.missionValue, info.isRemove ) )
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem( this, info, evt, elem );

	sendMissionUpdate( info.questId, questFlag, mission->index, info.missionValue, info.isRemove );
	return true;
}

Bool ActionPlayerQuest::onMazeClear( Mission& info, QuestEvent& qe )
{
	QeMazeClear& evt = static_cast< QeMazeClear& >( qe );

	QuestMazeClear* mission = static_cast< QuestMazeClear* >( info.mission );
	VALID_RETURN( mission, false );
	VALID_RETURN( mission->mazeLevel <= evt.mazeLevel, false );

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;
	
#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);

			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(mission->count < info.missionValue)
		{
			info.missionValue = mission->count;
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(mission->count > info.missionValue)
	{
		++info.missionValue;
	}
#else
	++info.missionValue;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313	

	const QuestElem* elem = SCRIPTS.GetQuest(info.questId);
	VERIFY_RETURN(elem, false);

	if (isQuestComplete(info.questId, mission, info.missionValue, info.isRemove))
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem(this, info, evt, elem);

	sendMissionUpdate(info.questId, questFlag, mission->index, info.missionValue, info.isRemove);

	return true;
}

Bool ActionPlayerQuest::onAuraKill( Mission& info, QuestEvent& qe )
{
	QeAuraKill& evt = static_cast< QeAuraKill& >(qe);

	QuestAuraKill* mission = static_cast< QuestAuraKill* >(info.mission);
	VALID_RETURN( mission, false );
	VALID_RETURN( mission->sectorGroupId == evt.sectorGroupId, false );

	Bool auraExist = false;

	auto first = evt.triggerList.begin();
	auto last = evt.triggerList.end();

	for (auto iter : mission->auraIdx)
	{
		auto buff = find_if(first, last, [&](const UInt32 &info)
		{
			return iter == info;
		});

		if (buff != last)
		{
			auraExist = true;
			break;
		}
	}

	VALID_RETURN( auraExist, false );

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;
	
#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);

			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(mission->count < info.missionValue)
		{
			info.missionValue = mission->count;
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(mission->count > info.missionValue)
	{
		++info.missionValue;
	}
#else
	++info.missionValue;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313	

	const QuestElem* elem = SCRIPTS.GetQuest(info.questId);
	VERIFY_RETURN(elem, false);

	if (isQuestComplete(info.questId, mission, info.missionValue, info.isRemove))
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem(this, info, evt, elem);

	sendMissionUpdate(info.questId, questFlag, mission->index, info.missionValue, info.isRemove);

	return true;
}

Bool ActionPlayerQuest::onArtifactLevel(Mission& info, QuestEvent& qe)
{
	EntityPlayer* player = GetOwnerPlayer();

	QeArtifactLevel& evt = static_cast<QeArtifactLevel&>(qe);

	QuestArtifactLevel* mission = static_cast<QuestArtifactLevel*>(info.mission);
	VALID_RETURN( mission, false );

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

	ActionPlayerArtifact *actArtifact = GetEntityAction(player);

#ifdef __Hotfix_Quest_Mission_Artifact_by_kangms_180822
#else//__Hotfix_Quest_Mission_Artifact_by_kangms_180822
	VALID_RETURN( mission->level <= evt.level, false );
#endif//__Hotfix_Quest_Mission_Artifact_by_kangms_180822
	VALID_RETURN( mission->artifactIdx == QuestArtifactLevel::ALL || mission->artifactIdx == evt.artifactIdx, false );

	info.missionValue = actArtifact->CalcArtifactLevel(mission->artifactIdx, mission->level);
	
	const QuestElem* elem = SCRIPTS.GetQuest(info.questId);
	VERIFY_RETURN(elem, false);

	if (isQuestComplete(info.questId, mission, info.missionValue, info.isRemove))
	{
		questFlag = QuestFlag::COMPLETE;
	}
	else
	{
		questFlag = QuestFlag::PROGRESS;
	}

	notifyToSharedSystem(this, info, evt, elem);

	sendMissionUpdate(info.questId, questFlag, mission->index, info.missionValue, info.isRemove);

	return true;
}

Bool ActionPlayerQuest::onArtifactCreate(Mission& info, QuestEvent& qe)
{
	EntityPlayer* player = GetOwnerPlayer();
	VALID_RETURN(player && player->IsValid(), false);

	QeArtifactCreate& evt = static_cast<QeArtifactCreate&>(qe);

	QuestArtifactCreate* mission = static_cast<QuestArtifactCreate*>(info.mission);
	VALID_RETURN( mission, false );

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

	ActionPlayerArtifact* aa = GetEntityAction(player);
	VALID_RETURN( aa, false );

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);

			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(mission->count < info.missionValue)
		{
			info.missionValue = static_cast<UShort>(mission->count);
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(mission->count > info.missionValue)
	{
		++info.missionValue;
	}
#else
	++info.missionValue;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313	

	const QuestElem* elem = SCRIPTS.GetQuest(info.questId);
	VERIFY_RETURN(elem, false);

	if (isQuestComplete(info.questId, mission, info.missionValue, info.isRemove))
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem(this, info, evt, elem);

	sendMissionUpdate(info.questId, questFlag, mission->index, info.missionValue, info.isRemove);

	return true;
}

Bool ActionPlayerQuest::onArtifactUse(Mission& info, QuestEvent& qe)
{
	QeArtifactUse& evt = static_cast<QeArtifactUse&>(qe);

	QuestArtifactUse* mission = static_cast<QuestArtifactUse*>(info.mission);
	VALID_RETURN( mission, false );

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;
		
#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);

			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(mission->count < info.missionValue)
		{
			info.missionValue = mission->count;
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(mission->count > info.missionValue)
	{
		++info.missionValue;
	}
#else
	++info.missionValue;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313	

	const QuestElem* elem = SCRIPTS.GetQuest(info.questId);
	VERIFY_RETURN(elem, false);

	if (isQuestComplete(info.questId, mission, info.missionValue, info.isRemove))
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem(this, info, evt, elem);

	sendMissionUpdate(info.questId, questFlag, mission->index, info.missionValue, info.isRemove);

	return true;
}

Bool ActionPlayerQuest::onNetherWorldKillMonster( Mission& info, QuestEvent& qe )
{
	QeNetherWorldKillMonster& evt = static_cast<QeNetherWorldKillMonster&>( qe );

	QuestNWKillMonster* mission = static_cast<QuestNWKillMonster*>( info.mission );
	VALID_RETURN( mission, false );

	if ( mission->grade != 0 && evt.grade < mission->grade ||  mission->difficulty != 0 && evt.difficulty < mission->difficulty )
	{
		return false;
	}

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);

			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(mission->count < info.missionValue)
		{
			info.missionValue = mission->count;
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(mission->count > info.missionValue)
	{
		++info.missionValue;
	}
#else
	++info.missionValue;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313	

	const QuestElem* elem = SCRIPTS.GetQuest( info.questId );
	VERIFY_RETURN( elem, false );

	if ( isQuestComplete( info.questId, mission, info.missionValue, info.isRemove ) )
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem( this, info, evt, elem );

	sendMissionUpdate( info.questId, questFlag, mission->index, info.missionValue, info.isRemove );

	return true;
}

Bool ActionPlayerQuest::onNetherWorldClear( Mission& info, QuestEvent& qe )
{
	QeNetherWorldClear& evt = static_cast<QeNetherWorldClear&>( qe );

	QuestNWTypeClear* mission = static_cast<QuestNWTypeClear*>( info.mission );
	VALID_RETURN( mission, false );

	if ( mission->stage != 0 && evt.stage != mission->stage 
			|| mission->difficulty != 0 && evt.difficulty < mission->difficulty 
			|| mission->clearTick != 0 && mission->clearTick < evt.clearTick )
	{
		return false;
	}

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);

			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(mission->count < info.missionValue)
		{
			info.missionValue = mission->count;
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(mission->count > info.missionValue)
	{
		++info.missionValue;
	}
#else
	++info.missionValue;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313	

	const QuestElem* elem = SCRIPTS.GetQuest( info.questId );
	VERIFY_RETURN( elem, false );

	if ( isQuestComplete( info.questId, mission, info.missionValue, info.isRemove ) )
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem( this, info, evt, elem );

	sendMissionUpdate( info.questId, questFlag, mission->index, info.missionValue, info.isRemove );

	return true;
}

Bool ActionPlayerQuest::onAdvCollectItem(Mission& info, QuestEvent& qe)
{
	QeAdvCollectItem& evt = static_cast<QeAdvCollectItem&>(qe);
	QuestAdvCollectItem* missionAdvCollectItem = static_cast<QuestAdvCollectItem*>(info.mission);
	VERIFY_RETURN(missionAdvCollectItem, false);

	const QuestElem* elem = SCRIPTS.GetQuest(info.questId);
	VERIFY_RETURN(elem, false);

	VALID_RETURN( missionAdvCollectItem, false );

	if (    missionAdvCollectItem->difficulty != 0
		 && evt.difficulty < missionAdvCollectItem->difficulty )
	{
		return false;
	}

	if (    missionAdvCollectItem->advType != 0
		 && evt.advType != missionAdvCollectItem->advType )
	{
		return false;
	}

	if (   missionAdvCollectItem->grade != 0
		&& evt.grade < missionAdvCollectItem->grade )
	{
		return false;
	}

	UInt32 seed = static_cast<UInt32>(GetRandBetween(1, DEFAULT_RATE));	
#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);

			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		if(seed <= missionAdvCollectItem->countRate)
		{
			info.missionValue += count;
			if(missionAdvCollectItem->count < info.missionValue)
			{
				info.missionValue = missionAdvCollectItem->count;
			}			
		}		
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(seed <= missionAdvCollectItem->countRate
		&& missionAdvCollectItem->count > info.missionValue)
	{
		++info.missionValue;
	}
#else
	++info.missionValue;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313	

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

	if (isQuestComplete(info.questId, missionAdvCollectItem, info.missionValue, info.isRemove))
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem(this, info, evt, elem);
	sendMissionUpdate(info.questId, questFlag, missionAdvCollectItem->index, info.missionValue, info.isRemove);

	return true;
}

Bool ActionPlayerQuest::onZoneKillMonster(Mission& info, QuestEvent& qe)
{
	QeZoneKillMonster& evt = static_cast<QeZoneKillMonster&>(qe);
	QuestZoneKillMonster* mission = static_cast<QuestZoneKillMonster*>(info.mission);
	VERIFY_RETURN( mission, false);

	const QuestElem* elem = SCRIPTS.GetQuest(info.questId);
	VERIFY_RETURN(elem, false);

	VALID_RETURN(0 == mission->difficulty || evt.difficulty >= mission->difficulty, false);
	VALID_RETURN(NpcGrade::NONE == mission->grade || evt.grade >= mission->grade, false);
	VALID_RETURN(mission->zoneIndexes.end() != mission->zoneIndexes.find(evt.zoneIndex), false);

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);

			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(mission->count < info.missionValue)
		{
			info.missionValue = mission->count;
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(mission->count > info.missionValue)
	{
		++info.missionValue;
	}
#else
	++info.missionValue;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313		

	if (true == isQuestComplete(info.questId, mission, info.missionValue, info.isRemove))
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem(this, info, evt, elem);
	sendMissionUpdate(info.questId, questFlag, mission->index, info.missionValue, info.isRemove);

	return true;
}

Bool ActionPlayerQuest::onZoneCollectItem(Mission& info, QuestEvent& qe)
{
	QeZoneCollectItem& evt = static_cast<QeZoneCollectItem&>(qe);
	QuestZoneCollectItem* mission = static_cast<QuestZoneCollectItem*>(info.mission);
	VERIFY_RETURN(mission, false);

	const QuestElem* elem = SCRIPTS.GetQuest(info.questId);
	VERIFY_RETURN(elem , false);

	VALID_RETURN(0 == mission->difficulty || evt.difficulty >= mission->difficulty, false);
	VALID_RETURN(NpcGrade::NONE == mission->grade || evt.grade >= mission->grade, false);
	VALID_RETURN(mission->zoneIndexes.end() != mission->zoneIndexes.find(evt.zoneIndex), false);

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

	UInt16 rate = static_cast<UInt16>(GetRandBetween(1, Limits::MAX_DEFAULT_RAND));
	VALID_RETURN( rate <= mission->countRate, false );

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);

			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(mission->count < info.missionValue)
		{
			info.missionValue = mission->count;
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(mission->count > info.missionValue)
	{
		++info.missionValue;
	}
#else
	++info.missionValue;
#endif	
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313

	if (true == isQuestComplete(info.questId, mission, info.missionValue, info.isRemove))
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem(this, info, evt, elem);
	sendMissionUpdate(info.questId, questFlag, mission->index, info.missionValue, info.isRemove);

	return true;
}

Bool ActionPlayerQuest::onCharacterLevel(Mission& info, QuestEvent& qe)
{
	QeCharacterLevel& evt = static_cast<QeCharacterLevel&>(qe);
	QuestCharacterLevel* missionCharacterLevel = static_cast<QuestCharacterLevel*>(info.mission);
	VERIFY_RETURN(missionCharacterLevel, false);

	const QuestElem* elem = SCRIPTS.GetQuest(info.questId);
	VERIFY_RETURN(elem, false);
	VALID_RETURN( 0 < missionCharacterLevel->characterLevel, false );

	info.missionValue = evt.characterLevel;

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

	if (isQuestComplete(info.questId, missionCharacterLevel, info.missionValue, info.isRemove))
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem(this, info, evt, elem);
	sendMissionUpdate(info.questId, questFlag, missionCharacterLevel->index, info.missionValue, info.isRemove);

	return true;
}

Bool ActionPlayerQuest::onLastHitMonster( Mission& info, QuestEvent& qe )
{
	QeLastHitMonster& evt = static_cast<QeLastHitMonster&>( qe );
	QuestLastHitMonster* missionLastHitMonster = static_cast<QuestLastHitMonster*>( info.mission );
	VERIFY_RETURN( missionLastHitMonster, false );

	const QuestElem* elem = SCRIPTS.GetQuest( info.questId );
	VERIFY_RETURN( elem, false );

	Bool isFind = false;
	for ( auto& iter : missionLastHitMonster->indexMonsterVec )
	{
		if ( iter == evt.monsterIndex )
		{
			isFind = true;
			break;
		}
	}
	VALID_RETURN(isFind, false );

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);

			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(missionLastHitMonster->monsterCount < info.missionValue)
		{
			info.missionValue = static_cast<UShort>(missionLastHitMonster->monsterCount);
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(missionLastHitMonster->monsterCount > info.missionValue)
	{
		++info.missionValue;
	}
#else
	++info.missionValue;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

	if ( isQuestComplete( info.questId, missionLastHitMonster, info.missionValue, info.isRemove ) )
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem( this, info, evt, elem );
	sendMissionUpdate( info.questId, questFlag, missionLastHitMonster->index, info.missionValue, info.isRemove );

	return true;
}

Bool ActionPlayerQuest::onPlayerKill( Mission& info, QuestEvent& qe )
{
	QePlayerKill& evt = static_cast<QePlayerKill&>( qe );
	QuestPlayerKill* missionquestPlayerKill = static_cast<QuestPlayerKill*>( info.mission );
	VERIFY_RETURN( missionquestPlayerKill, false );

	const QuestElem* elem = SCRIPTS.GetQuest( info.questId );
	VERIFY_RETURN( elem, false );

	Bool isFind = false;

	// Check.
	for (auto &zoneIndex : missionquestPlayerKill->indexZoneVec)
	{
		VALID_DO( zoneIndex == evt.zoneIndex, continue );
		isFind = true;
		break;
	}


	VALID_RETURN(isFind, false );

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);

			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		info.missionValue += count;
		if(missionquestPlayerKill->killCount < info.missionValue)
		{
			info.missionValue = static_cast<UShort>(missionquestPlayerKill->killCount);
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111
	if(missionquestPlayerKill->killCount > info.missionValue)
	{
		++info.missionValue;
	}
#else
	++info.missionValue;
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

	if ( isQuestComplete( info.questId, missionquestPlayerKill, info.missionValue, info.isRemove ) )
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem( this, info, evt, elem );
	sendMissionUpdate( info.questId, questFlag, missionquestPlayerKill->index, info.missionValue, info.isRemove );

	return true;
}

#ifdef ABILITY_RENEWAL_20180508

Bool ActionPlayerQuest::onOpenBracelet( Mission& info, QuestEvent& qe )
{
	QeOpenBracelet& evt = static_cast<QeOpenBracelet&>(qe);
	QuestOpenBracelet* missionquestOpenBracelet = static_cast<QuestOpenBracelet*>(info.mission);
	VERIFY_RETURN( missionquestOpenBracelet, false );

	const QuestElem* elem = SCRIPTS.GetQuest( info.questId );
	VERIFY_RETURN( elem, false );
	
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111	
	info.missionValue = GetOwnerPlayer()->HasBracelet() ? 1 : 0;
#else
	++info.missionValue;
#endif

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

	if (isQuestComplete( info.questId, missionquestOpenBracelet, info.missionValue, info.isRemove ))
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem( this, info, evt, elem );
	sendMissionUpdate( info.questId, questFlag, missionquestOpenBracelet->index, info.missionValue, info.isRemove );

	return true;
}

#endif

Bool ActionPlayerQuest::onAdvCollectType(Mission& info, QuestEvent& qe)
{
	QeAdvCollectType& evt = static_cast<QeAdvCollectType&>(qe);

	QuestAdvCollectType* mission = static_cast<QuestAdvCollectType*>(info.mission);
	VERIFY_RETURN(mission, false);

	VALID_RETURN(mission->difficulty == 0 || mission->difficulty <= evt.difficulty, false);	

	const QuestElem* elem = SCRIPTS.GetQuest(info.questId);
	VERIFY_RETURN(elem, false);

	if(0 != mission->zoneIndex && mission->zoneIndex != evt.zoneIndex)
	{
		return false;
	}

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);

			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		if((GetRandBetween(1, DEFAULT_RATE) <= mission->rate))
		{
			info.missionValue += count;

			if(mission->count < info.missionValue)
			{
				info.missionValue = static_cast<UShort>(mission->count);
			}			
		}		
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111	
	if((GetRandBetween(1, DEFAULT_RATE) <= mission->rate) ? true : false
		&& mission->count > info.missionValue)
	{
		info.missionValue += 1;
	}
#else
	if((GetRandBetween(1, DEFAULT_RATE) <= mission->rate) ? true : false)
	{
		info.missionValue += 1;
	}
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

	if(isQuestComplete(info.questId, mission, info.missionValue, info.isRemove))
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem(this, info, evt, elem);
	sendMissionUpdate(info.questId, questFlag, mission->index, info.missionValue, info.isRemove);

	return true;
}

Bool ActionPlayerQuest::onDifficultyCollectItem(Mission& info, QuestEvent& qe)
{
	QeDifficultyCollectItem& evt = static_cast<QeDifficultyCollectItem&>(qe);

	QuestDifficultyCollectItem* mission = static_cast<QuestDifficultyCollectItem*>(info.mission);
	VERIFY_RETURN(mission, false);

	VALID_RETURN(mission->difficulty == 0 || mission->difficulty <= evt.difficulty, false);

	const QuestElem* elem = SCRIPTS.GetQuest(info.questId);
	VERIFY_RETURN(elem, false);

	auto itr = mission->npcIndexes.find(evt.npcIndex);
	if(mission->npcIndexes.end() == itr)
	{
		return false;
	}

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);

			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", questInfo.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		if((GetRandBetween(1, DEFAULT_RATE) <= mission->rate))
		{
			info.missionValue += count;

			if(mission->count < info.missionValue)
			{
				info.missionValue = static_cast<UShort>(mission->count);
			}
		}
	}
#else
#ifdef __Hotfix_Quest_Mission_Count_Check_Over_by_ch_20190111	
	if((GetRandBetween(1, DEFAULT_RATE) <= mission->rate) ? true : false
		&& mission->count > info.missionValue)
	{
		info.missionValue += 1;
	}
#else
	if((GetRandBetween(1, DEFAULT_RATE) <= mission->rate) ? true : false)
	{
		info.missionValue += 1;
	}
#endif
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

	if(isQuestComplete(info.questId, mission, info.missionValue, info.isRemove))
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem(this, info, evt, elem);
	sendMissionUpdate(info.questId, questFlag, mission->index, info.missionValue, info.isRemove);

	return true;
}

#ifdef __Patch_Add_User_Emotion_Quest_Mission_Type_ch_20190117
Bool ActionPlayerQuest::onUserEmotion(Mission& info, QuestEvent& qe)
{
	QeUserEmotion& evt = static_cast<QeUserEmotion&>(qe);

	QuestUserEmotion* mission = static_cast<QuestUserEmotion*>(info.mission);
	VERIFY_RETURN(mission, false);

	const QuestElem* elem = SCRIPTS.GetQuest(info.questId);
	VERIFY_RETURN(elem, false);

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
	UShort count = SCRIPTS.GetScript<QuestScheduleScript>()->GetMagnifyCount(info.questId);
	if(0 == count)	// 기간이 지나서 진행이 불가한 경우.
	{
		QuestMap::iterator it = m_impl->quests.find(info.questId);
		if(it != m_impl->quests.end() && QuestFlag::IMPOSSIBLE != it->second.progressFlag)
		{
			// 진행불가로 처음 처리할 때만 클라로 알려줌.
			EntityPlayer* player = GetOwnerPlayer();
			VALID_RETURN(player && player->IsValid(), false);

			QuestInfo& questInfo = it->second;
			questInfo.progressFlag = QuestFlag::IMPOSSIBLE;
			SendUpdateQuest(player, &questInfo);

			SystemMessage sysMsg(L"sys", L"Msg_Quest_Error_TimeOverPlayLock");
			sysMsg.SetParam(L"quest", info.questId);
			SendSystemMsg(player, sysMsg);
		}

		return true;
	}
	else
	{
		if(mission->emotion == evt.emotionIndex
			&& mission->time <= evt.time
			&& mission->zoneIndex == evt.zoneIndex
			&& mission->eventVolumeIndex == evt.eventVolumeIndex)
		{
			info.missionValue += count;
			if(mission->count < info.missionValue)
			{
				info.missionValue = static_cast<UShort>(mission->count);
			}
		}
		else
		{
			return false;
		}
	}
#else
	if(mission->emotion == evt.emotionIndex
		&& mission->time <= evt.time
		&& mission->zoneIndex == evt.zoneIndex
		&& mission->eventVolumeIndex == evt.eventVolumeIndex)
	{
		info.missionValue++;
	}
	else
	{
		return false;
	}
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313	

	QuestFlag::Enum questFlag = QuestFlag::PROGRESS;

	if (isQuestComplete(info.questId, mission, info.missionValue, info.isRemove))
	{
		questFlag = QuestFlag::COMPLETE;
	}

	notifyToSharedSystem(this, info, evt, elem);
	sendMissionUpdate(info.questId, questFlag, mission->index, info.missionValue, info.isRemove);

	return true;
}
#endif

void ActionPlayerQuest::purgeMissions( QuestMissionList& lst )
{
	lst.erase( std::remove_if( lst.begin(), lst.end(), [](Mission& ms) -> bool { return ms.isRemove; } ), lst.end() );
}

void ActionPlayerQuest::updateSharedMission( QuestMissionList* lst, IndexQuest questId, UInt32 missionType, Byte missionIndex, UShort missionValue )
{
	std::for_each(lst->begin(), lst->end(),
		[&](Mission& ms)
	{
		if(ms.questId == questId && ms.mission->type == missionType && ms.mission->index == missionIndex)
		{
			ms.missionValue = missionValue;

			// 완료된 퀘스트에 조인할 수는 없으므로 업데이트만 한다. 
			sendMissionUpdate(questId, QuestFlag::PROGRESS, missionIndex, missionValue, ms.isRemove);
		}
	}
	);
}

Bool ActionPlayerQuest::giveDeliverItem( const QuestInfo& questInfo, DeliverItem* mission, Short param )
{
	EntityPlayer* player = GetOwnerPlayer();
	VALID_RETURN( player && player->IsValid(), false );
	VALID_RETURN( questInfo.progressFlag < QuestFlag::PROGRESS, true );	// 이미 진행중인 미션은 더이상 아이템을 주지 않는다.

	ActionPlayerInventory* actionPlayerInven = GetEntityAction( player );
	VALID_RETURN( actionPlayerInven, false );
	VALID_RETURN( actionPlayerInven->Pickup( ItemData( mission->indexItem, mission->itemCount ) ), false );

	return true;
}

Bool ActionPlayerQuest::checkItem( IndexItem itemId, UInt32 count )
{
	EntityPlayer* player = GetOwnerPlayer();
	VALID_RETURN( player && player->IsValid(), false );

	ActionPlayerInventory* actionInven = GetEntityAction( player );

	return actionInven->IsEnoughItem(InvenBags, itemId, count);
}

Bool ActionPlayerQuest::setQuestProgress(IndexQuest questId, Byte flag )
{
	QuestMap::iterator it = m_impl->quests.find( questId );

	if ( it != m_impl->quests.end() )
	{
		it->second.progressFlag = flag;
		return true;
	}

	return false;
}

Byte ActionPlayerQuest::getQuestRepeatCount(IndexQuest questId )
{
	QuestFinishMap::iterator it = m_impl->finishedQuests.find( questId );
	VALID_RETURN( it != m_impl->finishedQuests.end(), 0 );

	return it->second.repeat;
}

ErrorQuest::Error ActionPlayerQuest::checkQuestComplete(IndexQuest questId, QuestMission* mission, UShort value )
{
	EntityPlayer* player = GetOwnerPlayer();
	VALID_RETURN(player && player->IsValid(), ErrorQuest::E_FAILED);
	VALID_RETURN(mission, ErrorQuest::E_QUEST_UNKNOW);

	QuestMap::iterator it = m_impl->quests.find( questId );
	VALID_RETURN( it != m_impl->quests.end(), ErrorQuest::E_QUEST_UNKNOW );

	QuestInfo& questInfo = it->second;
	questInfo.progressFlag = QuestFlag::COMPLETE;
	questInfo.missionIndex = mission->index;
	questInfo.value[ mission->index ] = value;	// 완료값

	const QuestElem* elem = SCRIPTS.GetQuest( questId );
	VALID_RETURN( elem, ErrorQuest::E_QUEST_UNKNOW );

	// 진행중인 미션이 있음?
	for ( size_t i = 0; i < elem->missionVector.size(); ++i )
	{
		if ( !theQuestSystem.CheckMissionComplete( player, questInfo, elem->missionVector[i]) )
		{
			questInfo.progressFlag = QuestFlag::PROGRESS;

	// 여기서 currentStepMisionIndex 변수 갱신 하면 않됨 !!!
#ifdef __Hotfix_Quest_Mission_Artifact_by_kangms_180822
#else//__Hotfix_Quest_Mission_Artifact_by_kangms_180822
				if (elem->missionStepping) {
					questInfo.currentStepMisionIndex = mission->index + 1;
				}
#endif//__Hotfix_Quest_Mission_Artifact_by_kangms_180822
			break;
		}
	}

	UpdateQuestToDB( questInfo );
	SendUpdateQuest( player, &questInfo );

	return ErrorQuest::SUCCESS;
}

ErrorQuest::Error ActionPlayerQuest::rewardQuestByItself(IndexQuest questId)
{
	EntityPlayer* player = GetOwnerPlayer();
	VALID_RETURN( player && player->IsValid(), ErrorQuest::E_FAILED );

	ErrorQuest::Error eErrorQuest = ErrorQuest::SUCCESS;

	QuestMap::iterator it = m_impl->quests.find( questId );
	VALID_RETURN( it != m_impl->quests.end(), ErrorQuest::E_QUEST_UNKNOW );

	const QuestElem* elem = SCRIPTS.GetQuest( questId );
	VALID_RETURN( elem, ErrorQuest::E_QUEST_UNKNOW );

	// 자동 완료 타입 처리 - 보상은 경험치, 게임머니로...
	if ( it->second.progressFlag == QuestFlag::COMPLETE && elem->complete_Type == QuestCompleteType::ByItself )
	{
		eErrorQuest = RewardQuest( questId, 0, false, false );	// 선택보상은 선택아이템이 없음.

		if ( eErrorQuest != ErrorQuest::SUCCESS )
		{
			SendQuestError( player, eErrorQuest );
			return eErrorQuest;
		}
		else 
		{
			return finishQuest( questId );
		}
	}

	return ErrorQuest::SUCCESS;
}


ErrorQuest::Error ActionPlayerQuest::finishQuest(IndexQuest questId )
{
	EntityPlayer* player = GetOwnerPlayer();
	VALID_RETURN( player && player->IsValid(), ErrorQuest::E_FAILED );

	ErrorQuest::Error err = ErrorQuest::SUCCESS;

	SendFinishQuest( player, questId );
	m_impl->lastCompletedQuest = questId;

	const QuestElem* elem = SCRIPTS.GetQuest( questId );
	VALID_RETURN( elem, ErrorQuest::E_QUEST_NOT_FOUND );

	{
		// 퀘스트 스토리 타입 업적
		ActionPlayerAchievement* actAchievement = GetEntityAction(player);
		AchievementQuestSuccessStoryInfoEvent achievement(player, static_cast<Byte>(elem->story_type));
		actAchievement->OnEvent(&achievement);
	}

	{
		// 퀘스트 인덱스 업적
		ActionPlayerAchievement* actAchievement = GetEntityAction(player);
		AchievementQuestClearIndexEvent achievement(player, elem->index);
		actAchievement->OnEvent(&achievement);
	}

	SeasonMissionEventQuestClear seasonEvent( questId );
	ActionPlayerSeason * actionSeason = GetEntityAction( player );
	if ( nullptr != actionSeason )
	{
		actionSeason->Event( &seasonEvent );
	}

	ActionPlayerSurvey* aps = GetEntityAction( player );
	SurveyQuestCompleteEvent survey( player, elem->index );
	aps->OnEvent( &survey );

	PassivityCastParam param;
	param.type = PassiveCastType::QUEST_COMPLETE;
	param.ownerUnit = player;
	param.isSendSkillEventResult = true;
	player->GetAction<ActionPassivity>()->Cast(param);

	ActionSector* acs = GetEntityAction( player );
	VALID_RETURN( acs, ErrorQuest::E_QUEST_UNKNOW );

	Sector* sector = acs->GetSector();
	VALID_RETURN( sector, ErrorQuest::E_QUEST_UNKNOW );

	ActionPlayer* ap = GetEntityAction( player );
	VALID_RETURN( ap, ErrorQuest::E_QUEST_UNKNOW );

	removeMission( elem );

	// 미션 퀘스트일경우 섹터에 완료 통보를 한다.
	if( QuestProgressType::Mission == elem->progress_Type )
	{
		EntityFunctional* missionHero =  sector->GetMissionHeroEntity();
		ActionMissionHero* amh = GetEntityAction( missionHero );
		MU2_ASSERT( nullptr != amh );

		amh->RegisterMissionCompleteUser( questId, ap->GetCharId() );

		auto it = m_impl->misisonHeroEntityIdMap.find( questId );
		if ( it != m_impl->misisonHeroEntityIdMap.end() )
		{
			m_impl->misisonHeroEntityIdMap.erase( it );
		}
	}

	NotifyAdvanture(questId);

	if ( elem->next_Quest > 0 )
	{
		const QuestElem* quest = SCRIPTS.GetQuest( elem->next_Quest );
		if ( quest == NULL )
		{
			SendQuestError( player, ErrorQuest::E_QUEST_NOT_FOUND );
			return ErrorQuest::E_QUEST_NOT_FOUND;
		}

		err = CheckQuestAcceptCond( quest );
		VALID_RETURN( err == ErrorQuest::SUCCESS, err );

		SendGiveQuest( player, elem->next_Quest );
	}

	if ( elem->auto_gain_quest_id > 0 )
	{
		m_impl->autoGainQuests.push_back( ReqAutoGainQuest( elem->auto_gain_quest_id, (QuestElem::AUTO_GAIN_IGNORE_MASK)elem->auto_gain_ignore_cond ) );
	}
#ifdef __Patch_Quest_Over_MaxCount_by_cheolhoon_181025
	else
	{
		CheckWaitQuest();
	}
#endif

	if ( elem->completeTeleport > 0 )
	{
		ReqChangeMap( elem->completeTeleport );
	}

	return err;
}


Bool ActionPlayerQuest::isQuestComplete(IndexQuest questId, QuestMission* mission, UShort value, Bool& isMissionComplete )
{
	EntityPlayer* player = GetOwnerPlayer();
	VALID_RETURN( player && player->IsValid(), false );

	QuestMap::iterator it = m_impl->quests.find( questId );
	VALID_RETURN( it != m_impl->quests.end(), false );

	const QuestElem* elem = SCRIPTS.GetQuest( questId );
	VALID_RETURN( elem, false );

	QuestInfo& questInfo = it->second;
	questInfo.missionIndex = mission->index;
	questInfo.value[ mission->index ] = value;

	// 현재 미션이 완료됐는지 확인
	if (theQuestSystem.CheckMissionComplete( player, questInfo, mission ) )
	{
		isMissionComplete = true;

		if ( elem->missionStepping )
		{
			questInfo.currentStepMisionIndex = mission->index + 1;
		}

		setQuestMissionDecorators(questInfo.questId, mission->index, QuesteMissionDecoratorState::SUCCESS);
	}

	// 퀘스트 미션들을 모두 완료됐는지 확인
	for ( size_t i = 0; i < elem->missionVector.size(); ++i )
	{
		VALID_RETURN( theQuestSystem.CheckMissionComplete( player, questInfo, elem->missionVector[i]), false );
	}

	return true;
}

Bool ActionPlayerQuest::isProgressSaveQuestType( const QuestElem* quest, UInt8 progressFlag)
{
	if( quest->progress_Type == QuestProgressType::NonSaveProgress )
	{
		if ( progressFlag != QuestFlag::FINISHED )
		{
			return false; 
		}
	}

	VALID_RETURN( quest->progress_Type != QuestProgressType::Mission, false );

	// 범위 타잎 퀘스트일 경우 동적으로만 처리되므로 저장하지 않음.
	VALID_RETURN( false == quest->IsSharedQuest(), false );

	return true;
}

Int32 ActionPlayerQuest::calcMissionItemSlotCnt( ActionPlayerInventory* actionInven, IndexItem itemIndex, Int32 itemCnt, ErrorQuest::Error& errcode )
{
	if ( itemCnt <= 0 ) // 아이템이 없거나 아이템 개수가 없을 때 에러처리
	{
		errcode = ErrorQuest::E_QUEST_NOT_FOUND_MISSION_ITEM;
		return 0;
	}

	errcode = ErrorQuest::SUCCESS;

	return actionInven->GetFreeSlotCountByIndex(InvenBags, itemIndex, itemCnt);
}

Int32 ActionPlayerQuest::getQuestItemSlotCnt( const QuestElem* quest, ErrorQuest::Error& errcode )
{
	EntityPlayer* player = GetOwnerPlayer();
	VALID_DO( player && player->IsValid(), errcode = ErrorQuest::E_FAILED; return -1; );

	VALID_DO( quest, errcode = ErrorQuest::E_QUEST_NOT_FOUND; return -1;);

	ActionPlayerInventory *actionInven = GetEntityAction( player );

	Int32 count = 0;
	for ( size_t n = 0; n < quest->missionVector.size(); ++n )
	{
		QuestMission* mission = quest->missionVector[n];

		VALID_DO( mission, errcode = ErrorQuest::E_QUEST_NOT_FOUND_MISSION_ITEM; return -1;);

		if ( mission->type == QuestMissionType::CollectItem )
		{
			CollectItem* missionCollect = static_cast< CollectItem* >( mission );			
			count += calcMissionItemSlotCnt( actionInven, missionCollect->indexItem, missionCollect->itemCount, errcode );
			VALID_RETURN( errcode == ErrorQuest::SUCCESS, -1 );
		}	
	}

	errcode = ErrorQuest::SUCCESS;
	return count;
}


Int32 ActionPlayerQuest::getRewardSlotCnt( const QuestElem* quest, IndexItem selectItemType, std::vector< QuestCompleteItem >& itemTypes, ErrorQuest::Error& errcode )
{	
	EntityPlayer* player = GetOwnerPlayer();
	VALID_DO( player && player->IsValid(), errcode = ErrorQuest::E_FAILED; return 0; );

	auto attrPlayer = player->GetAttribute< AttributePlayer >();
	VALID_DO( attrPlayer, errcode = ErrorQuest::E_FAILED; return 0; );

	// 보상 아이템
	for( const auto& it : quest->complete_Item)
	{
		VALID_DO( 1 <= it.itemIndex, break );

		if ( 0 == it.eClassType || attrPlayer->eClassType == it.eClassType)
		{
			itemTypes.push_back( it );
		}		
	}

	// 선택 아이템
	for (const auto& it : quest->select_Item)
	{
		VALID_DO( 1 <= it.itemIndex, break );

		// 난제 어디 낑가넣자
		VALID_DO( 0 < selectItemType, errcode = ErrorQuest::E_QUEST_SELECT_ITEM; return 0;);

		if ( it.itemIndex == selectItemType )
		{
			itemTypes.push_back( it );
			break;
		}			
	}

	ActionPlayerInventory *actionInven = GetEntityAction( player );
	Int32 cnt = 0;
	for( size_t n = 0; n < itemTypes.size(); ++n )
	{
		cnt += actionInven->GetFreeSlotCountByIndex(InvenBags, itemTypes[n].itemIndex, itemTypes[n].itemCount);
	}
	
	errcode = ErrorQuest::SUCCESS;
	return cnt;
}

void ActionPlayerQuest::updateQuestRepeatCount(IndexQuest questId, Byte repeat)
{
	QuestFinishMap::iterator it = m_impl->finishedQuests.find( questId );

	if ( it != m_impl->finishedQuests.end() )
	{
		it->second.repeat = repeat;
		it->second.lastUpdateTime = DateTime::GetPresentTime();
		//DateTime::GetPresentTime().GetAsSystemTime(it->second.lastUpdateTime);
	}
	else
	{
		FinishedQuestInfo questFinishInfo;
		questFinishInfo.questId = questId;
		questFinishInfo.repeat = repeat;
		questFinishInfo.lastUpdateTime = DateTime::GetPresentTime();
		//DateTime::GetPresentTime().GetAsSystemTime(questFinishInfo.lastUpdateTime);
		m_impl->finishedQuests.insert( QuestFinishMap::value_type( questFinishInfo.questId , questFinishInfo ) );

		GetOwnerPlayer()->GetTalismanAction().CheckTalismanQuestCompleteAndNotifyToClient(questId);
	}

	m_impl->finishQuestsInThisMap.insert( questId );
}

Bool ActionPlayerQuest::updateQuestFinish(IndexQuest questId, const QuestElem *elem )
{
	EntityPlayer* player = GetOwnerPlayer();
	VALID_RETURN( player && player->IsValid(), false );
	VALID_RETURN( elem, false );

	auto it = m_impl->quests.find( questId );
	VERIFY_RETURN( m_impl->quests.end() != it, false );

	BYTE group = it->second.repeat;
	if ( elem->progress_Type == QuestProgressType::Repeat )	// 반복 타입이면 횟수 검사를 해줘야한다.
	{
		if ( elem->renewalCond > 0 )
		{
			m_impl->renewalCondQuestSet.insert( elem->index );
		}

		if ( elem->repeatCount == FINISH_INFINITE )	// 무한 반복퀘?
		{
			//group = 1;
			updateQuestRepeatCount( questId, 1 );
			return true;
		}

		if ( group >= elem->repeatCount )
		{			
			theGameMsg.SendGameDebugMsg( player, L"%d 퀘스트는 더이상 완료 할수없다.[%d>=%d]", group, elem->repeatCount );
			return false;
		}
	}

	++group;	// 완료 횟수 증가.
	updateQuestRepeatCount(questId, group);

	return true;
}

void ActionPlayerQuest::setUsingQuestOccurItem(IndexItem itemIndex, IndexQuest questIndex)
{
	m_impl->usingQuestOccurItem.itemIndex	 = itemIndex;
	m_impl->usingQuestOccurItem.questIndex = questIndex;
}

void ActionPlayerQuest::resetUsingQuestOccurItem()
{
	m_impl->usingQuestOccurItem.itemIndex  = 0;
	m_impl->usingQuestOccurItem.questIndex = 0;
}

Bool ActionPlayerQuest::sendMissionUpdate(IndexQuest questId, QuestFlag::Enum flag, Byte missionIndex, UShort value, Bool isMissionFinish )
{
	QuestMap::iterator it = m_impl->quests.find( questId );
	if (it == m_impl->quests.end())
	{
		//std::stringstream stackTrace;
		//CrashHandler::GetStackTrace(stackTrace);

		//MU2_WARN_LOG( core::LogCategory::DEFAULT, "sendMissionUpdate> can't find quest [questId:%d] %s", questId, stackTrace.str().c_str() );
		MU2_WARN_LOG( core::LogCategory::DEFAULT, "sendMissionUpdate> can't find quest [questId:%d] [flag:%d] [missionIndex:%d] [value:%d] [isMissionFinishi:%d]", questId, flag, missionIndex, value, isMissionFinish);
		return false;
	}

	QuestInfo& questInfo = it->second;					
	questInfo.missionIndex = missionIndex;
	questInfo.progressFlag = flag;
	questInfo.value[ missionIndex ] = value;

	const QuestElem* elem = SCRIPTS.GetQuest( questId );
	VERIFY_RETURN( elem, false );

	EntityPlayer* player = GetOwnerPlayer();
	VALID_RETURN( player && player->IsValid(), false );

	UpdateQuestToDB( questInfo );
	SendUpdateQuest( player, &questInfo );

	if ( flag == QuestFlag::COMPLETE )
	{
		isMissionFinish = true;
		theQuestSystem.OnCompleteQuest( player, questId );
	}

	if ( isMissionFinish )
	{
		this->canStartQuestMissionDecorators( questId, missionIndex, QuestMissionDecoratorCondition::MISSION_FINISH );
	}
	else
	{
		this->canStartQuestMissionDecorators( questId, missionIndex, QuestMissionDecoratorCondition::MISSION_COUNT );
	}

	return true;
}

void ActionPlayerQuest::ntfToClient(EntityPlayer* owner, const IndexQuest& questId, UInt8 flag, const QuestInfo* info )
{
	VERIFY_RETURN(owner && owner->IsValid(), );

	ENtfQuestUpdate* ntf = NEW ENtfQuestUpdate;
	ntf->quest.Reset();
	ntf->quest.questId = questId;
	ntf->quest.progressFlag = flag;

	//auto actSector = owner->GetAction< ActionSector >();
	EntityNpc* target = owner->GetSectorAction().GetNpcInMySector( m_impl->npcTalking );
	if (target != nullptr)
	{
		//auto attrNpc = target->GetAttribute< AttributeNpc >();

		//if (attrNpc)
		//{
			ntf->entityId = target->GetId();
			ntf->npcId = target->GetNpcIndex();
		//}
	/*	else
		{
			ntf->entityId = 0;
			ntf->npcId = 0;
		}*/
	}
	else
	{
		ntf->entityId = 0;
		ntf->npcId = 0;
	}

	m_impl->npcTalking = 0;

	if ( info != NULL )
	{	
		ntf->quest.missionIndex = info->missionIndex;
		ntf->quest.value[0] = info->value[0];
		ntf->quest.value[1] = info->value[1];
		ntf->quest.value[2] = info->value[2];
		ntf->quest.value[3] = info->value[3];
		ntf->quest.value[4] = info->value[4];
		ntf->quest.enableUI = info->enableUI;
		ntf->quest.isAdvQuest = SCRIPTS.IsAdvQuest( questId );

		if( info->deadline > 0)
		{
			Int64 curTicks;
			time( (time_t*)&curTicks );
			Int32 leftTime =  info->deadline - (Int32)curTicks;
			
			ntf->quest.deadline = MAX( 0 , leftTime );
		}
	}

	SERVER.SendToClient( owner, EventPtr(ntf) );
}

void ActionPlayerQuest::checkAutoGainQuests()
{
	while ( !m_impl->autoGainQuests.empty() )
	{
		ReqAutoGainQuest& req = m_impl->autoGainQuests.front(); 

		ErrorQuest::Error eError = AcceptAutoGainQuest( req.questId, req.ignoreCond );
		if ( eError != ErrorQuest::SUCCESS )
		{
			MU2_WARN_LOG( LogCategory::QUEST, "checkAutoGainQuests> failed. [ErrorCode:%d] [questId:%d] [ignoreCond:%d]", eError, req.questId, req.ignoreCond );
		}

		m_impl->autoGainQuests.pop_front();
	}
}

void ActionPlayerQuest::clearQuests()
{
	m_impl->quests.clear();
	
	for( auto& i : m_impl->questMissionList )
	{
		i.clear();
	}
}
#ifdef SPECIAL_SKILL_RENEWAL_by_jjangmo_180612
#else
void ActionPlayerQuest::NonSaveRewardSpecialSkill( SubindexSkill subKey )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), );

	auto attrPlayer = player->GetAttribute< AttributePlayer >();
	VALID_RETURN( attrPlayer, );

	const SpecialSkillElem* elem = SCRIPTS.GetSpecialSkillScript( attrPlayer->eClassType, subKey);
	VALID_RETURN( elem, );

	auto actSkillManage = player->GetAction< ActionPlayerSkillManage >();
	VALID_RETURN( actSkillManage->RegistSpecialSkill( elem ), );

	auto attrSkill = player->GetAttribute< AttributeSkill >();
	VALID_RETURN( attrSkill, );

	auto itr = attrSkill->specialBlocks.find(subKey);
	VALID_RETURN( itr != attrSkill->specialBlocks.end(), );

	SpecialBlock* block = itr->second;

	auto actPassivity = player->GetAction< ActionPassivity >();
	auto actPlayerAbility = player->GetAction< ActionPlayerAbility >();

	Bool empty = false;
	Byte slot = 0;
	for ( ; slot < Limits::MAX_SPECIAL_SKILL_SLOT; ++slot )
	{
		if ( attrSkill->specialBlockSlot.block[ slot ] != nullptr )
		{
			continue;
		}

		if ( attrSkill->specialBlockSlot.openFlag[ slot ] == false )
		{
			attrSkill->specialBlockSlot.openFlag.set( slot );

			ENtfGameOpenSlotSpecialSkill* ntf = NEW ENtfGameOpenSlotSpecialSkill;
			ntf->slotPlace = slot;
			SERVER.SendToClient( player, EventPtr( ntf ) );
		}

		empty = true;
		break;
	}

	attrSkill->specialBlockSlot.block[ slot ] = block;
	
	AbilityParam param;
	const SpecialSkillElem* specialElem = block->GetSpecialInfoElem();
	size_t size = specialElem->abilityPacks.size();
	for ( size_t loop = 0; loop < size; ++loop )
	{
		param.type = specialElem->abilityPacks[ loop ].type;
		param.isOnOff = true;
		if ( specialElem->abilityPacks[ loop ].point != 0 )
		{
			param.section = EffectSection::POINT;
			param.value = specialElem->abilityPacks[loop].point;
			actPlayerAbility->ChangedSkillAbility( param );
		}

		if ( specialElem->abilityPacks[ loop ].percent != 0 )
		{
			param.section = EffectSection::PERCENT;
			param.value = specialElem->abilityPacks[ loop ].percent;
			actPlayerAbility->ChangedSkillAbility( param );
		}
	}

	actPlayerAbility->Calculate();

	actPassivity->Apply( block->GetSpecialInfoElem()->passive_elem );

	EResGameEquipSlotSpecialSkill* res = NEW EResGameEquipSlotSpecialSkill;
	res->slotPlace = slot;
	res->specialSkill = subKey;
	res->eError = ErrorSkill::SUCCESS;
	SERVER.SendToClient( player, EventPtr( res ) );

	ENtfGameUpdateSkillInfo* ntf = NEW ENtfGameUpdateSkillInfo;
	actSkillManage->FillupSkillInfo( ntf->skillInfos );
	SERVER.SendToClient( player, EventPtr( ntf ) );
}
#endif

Bool ActionPlayerQuest::CheckHelpVolumeAndSend(EntityPlayer* owner, const IndexQuest& questId, const HelpVolumeMap &helpVolumes)
{
	VALID_RETURN( owner && owner->IsValid(), false );

	const QuestElem *elem = SCRIPTS.GetQuest(questId);
	VALID_RETURN(elem, false);

	ErrorQuest::Error eError = FindQuest(elem);

	if (eError == ErrorQuest::E_QUEST_ALREADY_HAVE)
	{
		for (auto iter = helpVolumes.begin(); iter != helpVolumes.end(); iter++)
		{
			ENtfSectorHelpVolumeState *ntf = NEW ENtfSectorHelpVolumeState;
			EventPtr ptr(ntf);

			ntf->questIndex = questId;
			ntf->helpVolumeIndex = iter->first;
			ntf->state = iter->second;

			SERVER.SendToClient(owner, ptr);

			wstring msg;
			wostringstream stream;

			stream << L"Debug Message : Helpvolume " << iter->first << L" is " << (iter->first == false ? L"Off" : L"On") << "\n";
			msg = stream.str();

			theGameMsg.SendGameDebugMsg(owner, const_cast<WChar*>(msg.c_str()));
		}

		return true;
	}

	return false;
}

void ActionPlayerQuest::SetTalkingNpc(EntityId npcEntityId )
{ 
	m_impl->npcTalking = npcEntityId; 
}

Mission* ActionPlayerQuest::getMission(IndexQuest questId, QuestMissionType::Enum type, UInt8 index )
{
	auto list = getMissionList( type );
	VALID_RETURN( list, nullptr );

	for( auto& i : *list )
	{
		if( i.questId == questId && i.mission->index == index )
		{
			return &i;
		}
	}

	return nullptr;
}

QuestMissionList* ActionPlayerQuest::getMissionList(QuestMissionType::Enum type)
{
	return (type < m_impl->questMissionList.size()) ? &m_impl->questMissionList[type] : nullptr;
}

ActionPlayerQuest::QuestProcessor ActionPlayerQuest::getQuestProcessor( QuestEventType::Enum type )
{
	return ( type < m_impl->questProcessorList.size() ) ? m_impl->questProcessorList[ type ] : nullptr;
}

void ActionPlayerQuest::checkDeactivate( const QuestElem* elem )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), );
	VERIFY_RETURN( elem, );

	if(QuestProgressType::Repeat != elem->progress_Type )
	{
		SendActivateQuest( player, elem->index );
	}
	else
	{
		DateTime cur =  DateTime::GetPresentTime();

		GiveupQuestInfo info;
		info.questId = elem->index;
		info.lastUpdateTime = cur;
		//cur.GetAsSystemTime( info.lastUpdateTime );

		if( !setupGiveupQuest( elem, info ) )
		{
			SendActivateQuest( player, elem->index );
		}
	}
}

Bool ActionPlayerQuest::setupGiveupQuest( const QuestElem* elem, const GiveupQuestInfo& info )
{
	VERIFY_RETURN( elem, false );

	if(QuestProgressType::Repeat == elem->progress_Type )
	{
		if( !isPassedRenewalPeriod( elem, info.lastUpdateTime ) )
		{
			m_impl->giveupQuests.emplace( info.questId, info );
			return true;
		}
	}

	return false;
}

Bool ActionPlayerQuest::isExistFinishedQuest( const QuestElem* elem ) const
{
	VERIFY_RETURN( elem, false );
	return m_impl->finishedQuests.end() != m_impl->finishedQuests.find( elem->index );
}

Bool ActionPlayerQuest::isExistQuest( const QuestElem* elem ) const
{
	VERIFY_RETURN( elem, false);
	return m_impl->quests.end() != m_impl->quests.find( elem->index );
}

Bool ActionPlayerQuest::isExistGiveupQuest( const QuestElem* elem ) const
{
	VERIFY_RETURN( elem, false );
	MU2_ASSERT( QuestProgressType::Repeat == elem->progress_Type );

	auto it = m_impl->giveupQuests.find( elem->index );
	return m_impl->giveupQuests.end() != it;
}

Bool ActionPlayerQuest::isPassedRenewalPeriod( const QuestElem* elem, const SYSTEMTIME& time ) const
{
	VERIFY_RETURN( elem, false );
	MU2_ASSERT( QuestProgressType::Repeat == elem->progress_Type );

 	if ( elem->renewalCond > 0 && ( m_impl->renewalCondQuestSet.end() != m_impl->renewalCondQuestSet.find( elem->index ) ) )
 	{
		// 해당 맵상에서 1회로 제한
 		return false;
 	}

	UInt32 diff = getQuestRenewalPeriodDiffDay( time );
	return elem->renewalPeriod <= diff;
}

UInt32	ActionPlayerQuest::getQuestRenewalPeriodDiffDay( const SYSTEMTIME& time ) const
{
	const QuestConfigScript* script = SCRIPTS.GetScript< QuestConfigScript >();
	VERIFY_RETURN( script, 0);

	SYSTEMTIME adjustTime( time );

	adjustTime.wHour	= 0;
	adjustTime.wMinute	= 0;
	adjustTime.wSecond	= 0;
	adjustTime.wMilliseconds = 0;

	DateTime adustUpdate( adjustTime );
	DateTime cur( DateTime::GetPresentTime() );
	DateTime adjustCur( script->GetAdjustCurDate() );

	DateTimeSpan diff = adjustCur - adustUpdate;
	UInt32 days = diff.GetDays();

	if( ( time.wHour < script->GetRenewalHour() ) || ( time.wHour == script->GetRenewalHour() && time.wMinute < script->GetRenewalMin() ) )
	{
		++ days;
	}

	if( 0 < days )
	{
		if( ( cur.GetHour() < script->GetRenewalHour() || ( cur.GetHour() == script->GetRenewalHour() && cur.GetMinute() < script->GetRenewalMin() ) ) )
		{
			-- days;
		}
	}

	return days;
}

void ActionPlayerQuest::SendToClientDeactivateQuest()
{
	EntityPlayer* player = GetOwnerPlayer();

	std::vector<UInt32>	questIdList;

	for ( const auto& i : m_impl->quests )
	{
		questIdList.push_back( i.first );
	}

	for ( const auto& i : m_impl->finishedQuests )
	{
		UInt32 questIndex = i.first;

		const QuestElem* elem = SCRIPTS.GetQuest( questIndex );
		VERIFY_DO( elem, continue );

		if ( QuestProgressType::Repeat == elem->progress_Type )
		{
			if( isPassedRenewalPeriod( elem, i.second.lastUpdateTime ) )
			{
				continue;
			}
		}

		questIdList.push_back( questIndex );
	}

	for( const auto& i : m_impl->giveupQuests )
	{
		questIdList.push_back( i.first );
	}

	EResQuestDeactivateInfo* res = NEW EResQuestDeactivateInfo;
	res->questIdList = std::move( questIdList );
	SERVER.SendToClient( player, EventPtr( res ) );
}

void ActionPlayerQuest::sendActivateQuest( const QuestIdList& list )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), );

	if( !list.empty() )
	{
		ENtfQuestActvateInfo* ntf = NEW ENtfQuestActvateInfo;
		ntf->questIdList = list;
		SERVER.SendToClient( player, EventPtr(ntf) );
	}
}

void ActionPlayerQuest::RenwalQuestForTest()
{
	renewalQuest( true );
}

void ActionPlayerQuest::renewalQuest(Bool isForTest /*= false */)
{
	QuestIdList questIdList;

	renewalGiveupQuest( questIdList, isForTest );
	renewalFinishedQuest( questIdList, isForTest );

	sendActivateQuest( questIdList );
}

void ActionPlayerQuest::renewalGiveupQuest( QuestIdList& out, Bool isForTest /*= false */ )
{
	QuestIdList local;
	for( const auto& i : m_impl->giveupQuests )
	{
		const QuestElem* elem = SCRIPTS.GetQuest( i.second.questId );
		VERIFY_DO( elem, continue );
		VERIFY_DO( QuestProgressType::Repeat == elem->progress_Type, continue );

		if( isForTest || isPassedRenewalPeriod( elem, i.second.lastUpdateTime ) )
		{
			local.push_back( i.first );
		}
	}

	for( const auto& i : local )
	{
		m_impl->giveupQuests.erase( i );
	}
	std::copy( local.begin(), local.end(), std::back_inserter( out ) );
}

void ActionPlayerQuest::renewalFinishedQuest( QuestIdList& out, Bool isForTest /*= false */ )
{
	for( const auto& i : m_impl->finishedQuests )
	{
		const QuestElem* elem = SCRIPTS.GetQuest( i.second.questId );
		VERIFY_DO( elem, continue );

		VERIFY_DO( QuestProgressType::Repeat == elem->progress_Type, continue );
		if( isForTest || isPassedRenewalPeriod( elem, i.second.lastUpdateTime ) )
		{
			out.push_back( i.first );
		}
	}
}

void ActionPlayerQuest::setNextRenewalTimer()
{
	m_impl->renewalTick = getNextRenewalTick();
	m_impl->renewalTimer.Reset();
}

UInt64 ActionPlayerQuest::getNextRenewalTick() const
{
	const QuestConfigScript* script = SCRIPTS.GetScript< QuestConfigScript >();
	VERIFY_RETURN( script, 0 );

	DateTime renewal( script->GetNextRenewalDate() );
	DateTime cur( DateTime::GetPresentTime() );

	DateTimeSpan renewalSpan( renewal.GetDay(), renewal.GetHour(), renewal.GetMinute(), renewal.GetSecond() );
	DateTimeSpan curSpan( cur.GetDay(), cur.GetHour(), cur.GetMinute(), cur.GetSecond() );

	return ( renewalSpan.GetTotalMilliSeconds() - curSpan.GetTotalMilliSeconds() );
}

void ActionPlayerQuest::SendToClientFinishedQuests()
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), );

	EResQuestFinishInfo* res = NEW EResQuestFinishInfo;

	for( const auto& i : m_impl->finishedQuests )
	{
		res->FinishQuest.push_back( i.second );
	}

	SERVER.SendToClient( player, EventPtr( res ) );
}

void ActionPlayerQuest::addProgressQuest( QuestInfo& info )
{
	MU2_ASSERT( m_impl->quests.emplace( info.questId, info ).second );
}

void ActionPlayerQuest::removeQuest(IndexQuest questId )
{
	auto it = m_impl->quests.find( questId );
	VALID_RETURN( m_impl->quests.end() != it, );

	m_impl->quests.erase( it );
}

void ActionPlayerQuest::ProcessAutoGainQuestWhenLeaveZone()
{
	checkAutoGainQuests();
}

SectorQuestMissionDecoratorManager* ActionPlayerQuest::GetQuestMissionDecoratorManager()
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), nullptr);

	auto actSector = player->GetAction<ActionSector>();
	VERIFY_RETURN( actSector, nullptr );

	auto sector = actSector->GetSector();
	VERIFY_RETURN( sector, nullptr );

	return sector->GetQuestMissionDecoratorManager();
}

Bool ActionPlayerQuest::insertQuestMissionDecorators(IndexQuest questId, QuestMission* questMission, Bool isFirstStart /*= true */ )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), false);

	auto questMissionDecoratorManager = GetQuestMissionDecoratorManager();

	auto script = SCRIPTS.GetScript<QuestMissionDecoratorScript>();
	VERIFY_RETURN( script, true );

	auto key = QuestMissionDecoratorKeyPlayer( questId, questMission->index );
	const auto elems = script->GetElems( key );
	VALID_RETURN( elems, true );

	auto pointerVector = QuestMissionDecoratorPointerVector();

	auto canProcessQuest = true;
	for ( const auto& elem : (*elems) )
	{
		auto newDecorator = questMissionDecoratorManager->Insert( player, &elem, questMission );

		if ( nullptr == newDecorator )
		{
			if ( elem.isNecessary && elem.indexZone == player->GetCurrZoneIndex())
			{
				MU2_ASSERT( newDecorator );
				canProcessQuest = false;
				break;
			}
			continue;
		}

		pointerVector.push_back( newDecorator );
	}

	if ( canProcessQuest == false )
	{
		for ( const auto& newDecorator : pointerVector )
		{
			questMissionDecoratorManager->Remove( player->GetCharId(), newDecorator->GetElem()->key );
		}
		return false;
	}


	for ( const auto& newDecorator : pointerVector )
	{
		newDecorator->AddOwner( player->GetId() );
		m_impl->questMissionDecoratorMap.insert( std::make_pair( key, newDecorator ) );

		if ( isFirstStart )
		{
			newDecorator->OnEvent( QuestMissionDecoratorCondition::MISSION_START, player );
		}
	}

	return true;
}

void ActionPlayerQuest::canStartQuestMissionDecorators(IndexQuest questId, Byte missionIndex, const QuestMissionDecoratorCondition startCondition )
{	
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), );

	auto rangeIt = m_impl->questMissionDecoratorMap.equal_range( QuestMissionDecoratorKeyPlayer( questId, missionIndex ) );
	for ( auto it = rangeIt.first; it != rangeIt.second; ++it )
	{
		(it->second)->OnEvent( startCondition, player );
	}
}

void ActionPlayerQuest::EraseQuestMissionDecorators(IndexQuest questId /*= 0*/, Byte missionIndex /*= 0 */ )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), );

	auto range = std::make_pair(m_impl->questMissionDecoratorMap.end(), m_impl->questMissionDecoratorMap.end());
	if ( questId != 0  )
	{
		range = m_impl->questMissionDecoratorMap.equal_range( QuestMissionDecoratorKeyPlayer( questId, missionIndex ) );
	}
	else
	{
		range = std::make_pair( m_impl->questMissionDecoratorMap.begin(), m_impl->questMissionDecoratorMap.end() );
	}

	for ( auto it = range.first; it != range.second;  )
	{
		it->second->DelOwner( player->GetId() );
		it = m_impl->questMissionDecoratorMap.erase( it );
	}
}

void ActionPlayerQuest::setQuestMissionDecorators(IndexQuest questId, Byte missionIndex, QuesteMissionDecoratorState state)
{
	auto rangeIt = m_impl->questMissionDecoratorMap.equal_range(QuestMissionDecoratorKeyPlayer(questId, missionIndex));
	for (auto it = rangeIt.first; it != rangeIt.second; ++it)
	{
		(it->second)->SetState(state);
	}
}

void ActionPlayerQuest::EraseQuestMissionDecoratorOnExpired( const QuestMissionDecoratorKey& key )
{
	auto rangeIt = m_impl->questMissionDecoratorMap.equal_range( QuestMissionDecoratorKeyPlayer( key.questId, key.missionId ) );
	
	for ( auto it = rangeIt.first; it != rangeIt.second;  )
	{
		if ( it->second->GetElem()->key == key )
		{
			it = m_impl->questMissionDecoratorMap.erase( it );
		}
		else
		{
			++it;
		}
	}
}

void ActionPlayerQuest::StartProgressQuestMissions()
{
#ifdef __Hotfix_Quest_Mission_Count_Modify_by_cheolhoon_20181105
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );
#endif

	for ( const auto& questPair : m_impl->quests )
	{
		const auto& quest = questPair.second;
		const auto elem = SCRIPTS.GetQuest( quest.questId );
#ifdef __Hotfix_Quest_Mission_Count_Modify_by_cheolhoon_20181105
		Int32 count = 0;
#endif

		if ( quest.progressFlag != QuestFlag::FAILED )
		{
			// 실패한 퀘는 포기하고 다시 받아야함. 
			for ( UInt32 n = 0; n < elem->missionVector.size(); ++n )
			{
				QuestMission* mission = elem->missionVector[n];
#ifdef __Hotfix_Quest_Mission_Count_Modify_by_cheolhoon_20181105
				if(ErrorQuest::startMissionFailedAlreadyCompleted == startMission( quest, mission, quest.value[mission->index], false ))
				{
					count++;
				}
#else
				startMission( quest, mission, quest.value[mission->index], false );
#endif
			}

#ifdef __Hotfix_Quest_Mission_Count_Modify_by_cheolhoon_20181105
			// 퀘스트 미션이 모두 완료된 상태면 완료처리.
			if(elem->missionVector.size() == count)
			{
				QuestInfo& questInfo = const_cast<QuestInfo&>(quest);
				questInfo.progressFlag = QuestFlag::COMPLETE;

				UpdateQuestToDB(questInfo);
				SendUpdateQuest(player, &questInfo);
			}
#endif
		}
	}
}


ErrorQuest::Error ActionPlayerQuest::CheckReward( const QuestElem* elem, const Int32 addItemSlot )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorQuest::playerNotFound);

	VALID_RETURN( elem->complete_Type != QuestCompleteType::ByItself, ErrorQuest::SUCCESS );
	VALID_RETURN( player->GetInventoryAction().IsEnoughFreeSlot( InvenBags, addItemSlot ), ErrorQuest::E_QUEST_INVEN_FULL );

	VALID_RETURN( elem->complete_zen == 0 || player->CheckZen( elem->complete_zen, ChangedMoney::ADD ), ErrorQuest::E_QUEST_LIMIT_ZEN );
	VALID_RETURN( elem->complete_greenzen == 0 || player->CheckGreenZen( elem->complete_greenzen, ChangedMoney::ADD ), ErrorQuest::E_QUEST_LIMIT_GREENZEN) ;
	VALID_RETURN( elem->complete_contribution == 0 || player->CheckContributionPoint( elem->complete_contribution, ChangedMoney::ADD ), ErrorQuest::E_QUEST_LIMIT_CONTRIBUTION_POINT );

	return ErrorQuest::SUCCESS;
}

void ActionPlayerQuest::processRewardItems( const std::vector< QuestCompleteItem >&completeItems, const QuestElem* elem )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), )

	auto attrPlayer = player->GetAttribute<AttributePlayer>();
	VERIFY_RETURN(attrPlayer, );

	InvenMail* mailInven = player->GetInventoryAction().GetInven<InvenMail>(InvenType::MAIL_BAG);
	VERIFY_RETURN(mailInven, );

	if ( !completeItems.empty() )
	{
		MailData mail;
		mail.senderName = L"Msg_Mailbox_QUEST_ITEM_SENDER";
		mail.subject = L"Msg_Mailbox_QUEST_ITEM_SUBJECT";
		mail.content = L"Msg_Mailbox_QUEST_ITEM_CONTENT";

		for ( Int32 i = 0; i < (Int32)completeItems.size(); ++i )
		{
			if (mailInven->PushMail( completeItems[i].itemIndex, completeItems[i].itemCount, mail ) != ErrorItem::SUCCESS )
			{
				MU2_WARN_LOG( LogCategory::CONTENTS, "ActionQuest::RewardQuest> failed to push quest reward Item [charId:%d][QuestId:%d][ItemType:%d][ItemCount:%d]",
					attrPlayer->charId, elem->index, completeItems[i].itemIndex, completeItems[i].itemCount );
			}
		}
	}

	if ( elem->complete_zen > 0 )
	{
		if (elem->complete_Type == QuestCompleteType::ByItself &&
			player->CheckZen(elem->complete_zen, ChangedMoney::ADD) == false)
		{
			MailData mail;
			mail.senderName = L"Msg_Mailbox_QUEST_ITEM_SENDER";
			mail.subject = L"Msg_Mailbox_QUEST_ITEM_SUBJECT";
			mail.content = L"Msg_Mailbox_QUEST_ITEM_CONTENT";
			mail.money = elem->complete_zen;

			ActionPlayerMailBox *actMail = GetEntityAction( player);
			ItemData item;
			Int32 ret = actMail->SendSystemMail( mail, item, false );
			MU2_ASSERT( ret == ErrorMail::SUCCESS );
		}
		else
		{
			player->AddMoney( elem->complete_zen );
			player->SendChangeMoney( elem->complete_zen, ChangedMoney::ADD );
		}
	}

	if ( elem->complete_greenzen > 0 )
	{
		player->AddGreenZen( elem->complete_greenzen );
		player->SendChangeGreenZen( elem->complete_greenzen, ChangedMoney::ADD );
	}

	if ( elem->complete_riftpoint > 0 )
	{
		player->AddRiftPoint( elem->complete_riftpoint );
		player->SendChangeRiftPoint( elem->complete_greenzen, ChangedMoney::ADD );
	}

	if ( elem->complete_contribution > 0 )
	{
		if (player->AddContributionPoint(elem->complete_contribution))
		{
			player->SendChangedContributionPoint(elem->complete_contribution, ChangedMoney::ADD);
		}
	}

	if ( elem->complete_Exp > 0 || elem->complete_SoulExp > 0 )
	{
		Int32 addedRate = player->GetAction< ActionAbility >()->GetAbilityValue(EAT::QUEST_EXP_RATE );
		UInt32 addedCharExp = static_cast<UInt32>( elem->complete_Exp * ( addedRate * DEFAULT_FLOATIZED_RATE) );
		addedCharExp = std::max< UInt32 >( 0, addedCharExp );

		player->GetAction< ActionPlayer >()->AddExp( elem->complete_Exp, elem->complete_SoulExp, 0, addedCharExp );
	}

	if ( elem->completeBuff1 > 0 )
	{
		theLogicEffectSystem.OnSingleCast( player, elem->completeBuff1 );
	}

	if ( elem->completeBuff2 > 0 )
	{
		theLogicEffectSystem.OnSingleCast( player, elem->completeBuff2 );
	}

#ifdef SPECIAL_SKILL_RENEWAL_by_jjangmo_180612
#else
	if ( elem->completeTutorialSkillSlot > 0 )
	{
		// only use for tutorial 
		NonSaveRewardSpecialSkill( elem->completeTutorialSkillSlot );
	}

	if ( elem->completeSpecialSkillSlotIndex > 0 )
	{
		auto actSkillManage = player->GetAction< ActionPlayerSkillManage >();
		actSkillManage->OpenSpecialSkill( elem->completeSpecialSkillSlotIndex - 1, elem->completeSpecialSkillSlotDelayTick );
	}
#endif

}


#ifdef __Reincarnation__jason_180628__
ErrorQuest::Error ActionPlayerQuest::processRewardExtra(const QuestElem& elem)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), ErrorQuest::playerNotFound );

	VALID_RETURN(elem.complete_vecQuestCompleteExtra.size(), ErrorQuest::SUCCESS);

	for (const QuestElemBase::stQuestExtraReward& rewardExtra : elem.complete_vecQuestCompleteExtra)
	{
		switch (rewardExtra.eType)
		{
		case QuestExtraRewardType::Enum::Reincarnation:
			{
				QuestCompletedReincarnated reward = rewardExtra.Get<QuestCompletedReincarnated>();
				VERIFY_RETURN(reward.IsValid(), ErrorQuest::invalidQuestExtraRewardValues);

				ErrorQuest::Error eError = owner->GetCharSoulInfo().ConvertReincarnatedLevel(reward.toReincarnatedLevel);
				VERIFY_RETURN(eError == ErrorQuest::SUCCESS, eError);
			}
			break;
#ifdef COMPLETE_ADD_ARTIFACT_REWARD_180724
		case QuestExtraRewardType::Enum::ArtifactReward:
			{
				QuestCompletedArtifact reward = rewardExtra.Get<QuestCompletedArtifact>();
				VERIFY_RETURN( reward.IsValid(), ErrorQuest::invalidQuestExtraRewardValues );

				ErrorQuest::Error eError = owner->GetArtifactAction().QuestRewardArtifact(reward.index, reward.level);

				VERIFY_RETURN( eError == ErrorQuest::SUCCESS, eError );
			}
			break;
#endif
#ifdef __Patch_Add_COMPLETE_ADD_ENABLE_PORTAL_by_ch_20190306
		case QuestExtraRewardType::Enum::EnablePortal:
			{
				QuestCompletedEnblePortal reward = rewardExtra.Get<QuestCompletedEnblePortal>();
				VERIFY_RETURN(reward.IsValid(), ErrorQuest::invalidQuestExtraRewardValues);

				ErrorQuest::Error eError = ErrorQuest::SUCCESS;
				AttributePlayer& attrPlayer = owner->GetPlayerAttr();

				if(attrPlayer.exploredPortals.end() ==
					std::find(attrPlayer.exploredPortals.begin(), attrPlayer.exploredPortals.end(), reward.portalIndex))
				{
					attrPlayer.exploredPortals.push_back(reward.portalIndex);

					EReqInsertExploredPortal* req = NEW EReqInsertExploredPortal;
					req->charId = owner->GetCharId();
					req->portalIndex = reward.portalIndex;
					SERVER.SendToDb(owner, EventPtr(req));

					ENtfGameExploredPortalAdd* newPortal = NEW ENtfGameExploredPortalAdd;
					newPortal->addProtalIndex = reward.portalIndex;
					SERVER.SendToClient(owner, EventPtr(newPortal));
				}

				VERIFY_RETURN(eError == ErrorQuest::SUCCESS, eError);
		}
			break;
#endif // __Patch_Add_COMPLETE_ADD_ENABLE_PORTAL_by_ch_20190306
		default:
			return ErrorQuest::invalidQuestExtraRewardType;
		}
	}

	return ErrorQuest::SUCCESS;
}
#endif


void ActionPlayerQuest::SendAdvantureInfo()
{
	ENtfAdvantureInfo * info = NEW ENtfAdvantureInfo;
	EventPtr pack(info);

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	ActionPlayerEvent * actEvent = GetEntityAction(player);
	if (nullptr == actEvent)
	{
		SERVER.SendToClient(player, pack);
		return;
	}

	Index32 aeid = theEventSystem.GetAdventureEvent();
	const auto elem = SCRIPTS.GetGameEventElem(aeid);
	if (nullptr == elem)
	{
		SERVER.SendToClient(player, pack);
		return;
	}

	info->advantureEventId = aeid;
	if (actEvent->checkDate(elem->endDate))
	{
		SERVER.SendToClient(player, pack);
		return;
	}

	info->advatureActiveState = 1;

	Bool update = false;
	CharAdvantureInfo &advinfo = m_impl->advantureInfo;
	if (advinfo.reportIndex == 0)
	{
		update = true;

		advinfo.Reset();
	}

	if (0 == advinfo.mission1Index)
	{
		update = true;

		const auto reportInfo = elem->GetAdvReport(advinfo.reportIndex);
		if (nullptr == reportInfo)
		{
			info->advatureActiveState = 0;
			SERVER.SendToClient(player, pack);
			return;
		}

		const auto pageInfo = reportInfo->GetPageInfo(advinfo.pageIndex);
		if (nullptr == pageInfo)
		{
			info->advatureActiveState = 0;
			SERVER.SendToClient(player, pack);
			return;
		}
		if (pageInfo->missions.size() < 2)
		{
			info->advatureActiveState = 0;
			SERVER.SendToClient(player, pack);
			return;
		}

		advinfo.pageRewardState = StampState::NONE;
		std::vector<UInt32> missions;
		pageInfo->GetMissions(missions);
		advinfo.mission1Index = missions.at(0);
		advinfo.mission2Index = missions.at(1);
		advinfo.mission1Complete = EventMissionComplete::INCOMPLETE;
		advinfo.mission2Complete = EventMissionComplete::INCOMPLETE;

		ResetQuest(advinfo.mission1Index);
		AcceptQuest(advinfo.mission1Index);

		ResetQuest(advinfo.mission2Index);
		AcceptQuest(advinfo.mission2Index);
	}

	// DB 갱신 요청
	if(update)
		UpdateAdvantureInfo();
	
	info->advantureData = advinfo;

	MU2_INFO_LOG(LogCategory::CONTENTS, "[Adventure] SendAdventureInfo : curPage ( %d, %d), mi1 ( %d, %d ), mi2 ( %d, %d )", 
		info->advantureData.pageIndex, info->advantureData.pageRewardState
		, info->advantureData.mission1Index, info->advantureData.mission1Complete
		, info->advantureData.mission2Index, info->advantureData.mission2Complete);

	SERVER.SendToClient(player, pack);

}

ErrorItem::Error ActionPlayerQuest::ProcAdvanturePageReward(UInt16 curPgae)
{
	ErrorItem::Error eError = ErrorItem::SUCCESS;

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	Index32 aeid = theEventSystem.GetAdventureEvent();
	const GameEventElem* elem = SCRIPTS.GetGameEventElem(aeid);
	VERIFY_RETURN(elem, ErrorItem::scriptNotFound);

	if (player->GetEventAction().checkDate(elem->endDate))
	{
		SendSystemMsg(GetOwnerPlayer(), SystemMessage(L"sys", L"Msg_Err_In_Game_Event"));
		return ErrorItem::E_GAME_EVENT_EXPIRY_DATE;
	}

	const auto reportInfo = elem->GetAdvReport(m_impl->advantureInfo.reportIndex);
	VERIFY_RETURN(reportInfo, ErrorItem::scriptNotFound);

	const AdvanturePageInfo* pageInfo = reportInfo->GetPageInfo(m_impl->advantureInfo.pageIndex);
	VERIFY_RETURN(pageInfo, ErrorItem::AdvanturePageInfoNotFound);

	//! 미션이 모두 완료되면, pageRewardState 을 StampState::UNRECIEVE 으로 바꾼다.
	if (StampState::UNRECIEVE == m_impl->advantureInfo.pageRewardState)
	{
		ItemBag bag(*player);

		VERIFY_RETURN(bag.Insert(ItemData(pageInfo->itemIndex, pageInfo->itemCount)), ErrorItem::BagInsertFailed);

		AdvantureEventRewardTransaction * trans = NEW AdvantureEventRewardTransaction(
			__FUNCTION__, __LINE__
			, player, LogCode::E_ADVANTURE_PAGE_REWARD
			, m_impl->advantureInfo.reportIndex, m_impl->advantureInfo.pageIndex
			, pageInfo->itemIndex, pageInfo->itemCount );

		TransactionPtr transPtr(trans);
		{
			VERIFY_RETURN(bag.Write(transPtr), ErrorItem::BagWriteFailed);
			VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::TranRegisterFailed);
		}		
	}
	else if (StampState::RECIEVE == m_impl->advantureInfo.pageRewardState)
	{
		EResAdvanturePageReward * res = NEW EResAdvanturePageReward;
		res->eError = ErrorItem::SUCCESS;

		if (reportInfo->lastPage > m_impl->advantureInfo.pageIndex)
		{
			//! 다음 페이지로
			eError = ProcAdvantureNextPage();			
			VERIFY_DO(eError == ErrorItem::SUCCESS, MustErrorCheck);

			res->nextContents = AdvantureContentType::PAGE;
			SERVER.SendToClient(player, EventPtr(res));
		}
		else if (elem->lastReportIndex > m_impl->advantureInfo.reportIndex)
		{
			//! 다음 노트로
			res->nextContents = AdvantureContentType::REPORT;
			SERVER.SendToClient(player, EventPtr(res));
		}
		else
		{
			res->nextContents = AdvantureContentType::END;
			SERVER.SendToClient(player, EventPtr(res));
		}
	}
	else
	{
		eError = ErrorItem::E_NOT_COMPLETE;
	}

	return eError;
}

ErrorItem::Error ActionPlayerQuest::ProcAdvantureNextPage()
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player, ErrorItem::playerNotFound);
	
	Index32 aeid = theEventSystem.GetAdventureEvent();
	const GameEventElem* elem = SCRIPTS.GetGameEventElem(aeid);
	VERIFY_RETURN(elem, ErrorItem::scriptNotFound);

	if (player->GetEventAction().checkDate(elem->endDate))
	{
		SendSystemMsg(player, SystemMessage(L"sys", L"Msg_Err_In_Game_Event"));
		return ErrorItem::E_GAME_EVENT_EXPIRY_DATE;
	}

	const AdvantureNoteInfo* reportInfo = elem->GetAdvReport(m_impl->advantureInfo.reportIndex);
	VERIFY_RETURN(reportInfo, ErrorItem::AdvantureNoteInfoNotFound);
	VERIFY_RETURN(StampState::RECIEVE == m_impl->advantureInfo.pageRewardState, ErrorItem::E_ADVANTURE_PAGE_REWARD);
	VERIFY_RETURN(reportInfo->lastPage > m_impl->advantureInfo.pageIndex, ErrorItem::E_ADVANTURE_NOMORE_PAGE);

	const AdvanturePageInfo* pageInfo = reportInfo->GetPageInfo(m_impl->advantureInfo.pageIndex + 1);
	VERIFY_RETURN(pageInfo, ErrorItem::AdvanturePageInfoNotFound);
	VERIFY_RETURN(pageInfo->missions.size() >= 2, ErrorItem::E_INVALID_GAME_EVENT);

	CharAdvantureInfo &info = m_impl->advantureInfo;
	++info.pageIndex;
	info.pageRewardState = StampState::NONE;

	std::vector<UInt32> missions;
	pageInfo->GetMissions(missions);

	info.mission1Index = missions.at(0);
	info.mission2Index = missions.at(1);
	info.mission1Complete = EventMissionComplete::INCOMPLETE;
	info.mission2Complete = EventMissionComplete::INCOMPLETE;

	ResetQuest(info.mission1Index);
	AcceptQuest(info.mission1Index);

	ResetQuest(info.mission2Index);
	AcceptQuest(info.mission2Index);

	// DB 갱신 요청
	UpdateAdvantureInfo();

	// 캐릭터에 전달
	/*
	EResAdvantureNextPage * res = NEW EResAdvantureNextPage;
	res->advantureNextPageIndex = GetAdvantureCurPage();
	res->mission1Index = GetAdvantureMission1Index();
	res->mission2Index = GetAdvantureMission2Index();
	res->eError = ErrorItem::SUCCESS;
	*/

	ENtfAdvantureInfo * ntf = NEW ENtfAdvantureInfo;
	ntf->advatureActiveState = 1;
	ntf->advantureEventId = theEventSystem.GetAdventureEvent();
	ntf->advantureData = m_impl->advantureInfo;

	SERVER.SendToClient(player, EventPtr(ntf));
	
	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerQuest::ProcAdvantureNextReport()
{
	// ErrorItem::Error eError = ErrorItem::SUCCESS;

	EntityPlayer* player = GetOwnerPlayer();
	VALID_RETURN(player && player->IsValid(), ErrorItem::E_INVALID_GAME_EVENT);

	ActionPlayerEvent * actEvent = GetEntityAction(player);
	VALID_RETURN(actEvent, ErrorItem::E_INVALID_GAME_EVENT);

	Index32 aeid = theEventSystem.GetAdventureEvent();
	const auto elem = SCRIPTS.GetGameEventElem(aeid);
	VALID_RETURN(elem, ErrorItem::E_INVALID_GAME_EVENT);

	if (actEvent->checkDate(elem->endDate))
	{
		SendSystemMsg(GetOwnerPlayer(), SystemMessage(L"sys", L"Msg_Err_In_Game_Event"));
		return ErrorItem::E_GAME_EVENT_EXPIRY_DATE;
	}

	CharAdvantureInfo &info = m_impl->advantureInfo;
	VALID_RETURN(info.reportIndex < elem->lastReportIndex, ErrorItem::E_ADVANTURE_NOMORE_REPORT);

	const auto reportInfo = elem->GetAdvReport(info.reportIndex);
	VALID_RETURN(reportInfo, ErrorItem::E_INVALID_GAME_EVENT);
	
	VALID_RETURN(StampState::RECIEVE == info.pageRewardState, ErrorItem::E_ADVANTURE_PAGE_REWARD);
	VALID_RETURN(reportInfo->lastPage <= info.pageIndex, ErrorItem::E_ADVANTURE_PAGE_NOT_LAST);

	const auto nextReportInfo = elem->GetAdvReport(info.reportIndex + 1);
	VALID_RETURN(nextReportInfo, ErrorItem::E_INVALID_GAME_EVENT);

	auto nextPageInfo = nextReportInfo->GetPageInfo(1);
	VALID_RETURN(nextPageInfo, ErrorItem::E_INVALID_GAME_EVENT);
	VALID_RETURN(nextPageInfo->missions.size() >= 2, ErrorItem::E_INVALID_GAME_EVENT);
	
	++info.reportIndex;
	info.pageIndex = 1;
	info.pageRewardState = StampState::NONE;

	std::vector<UInt32> missions;
	nextPageInfo->GetMissions(missions);
	info.mission1Index = missions.at(0);
	info.mission2Index = missions.at(1);
	info.mission1Complete = EventMissionComplete::INCOMPLETE;
	info.mission2Complete = EventMissionComplete::INCOMPLETE;

	ResetQuest(info.mission1Index);
	AcceptQuest(info.mission1Index);

	ResetQuest(info.mission2Index);
	AcceptQuest(info.mission2Index);


	// DB 갱신 요청
	UpdateAdvantureInfo();

	// 캐릭터에 전달
	ENtfAdvantureInfo * ntf = NEW ENtfAdvantureInfo;
	ntf->advatureActiveState = 1;
	ntf->advantureEventId = theEventSystem.GetAdventureEvent();
	ntf->advantureData = m_impl->advantureInfo;

	SERVER.SendToClient(player, EventPtr(ntf));

	EReqLogDb2* log = NEW EReqLogDb2;
	EventPtr logEventPtr(log);
	EventSetter::FillLogForEntity(LogCode::E_ADVANTURE_NEW_REPORT, GetOwnerPlayer(), log->action);

	log->action.var32s.push_back(info.reportIndex);
	SendDataCenter(logEventPtr);

	return ErrorItem::SUCCESS;
}

UInt16 ActionPlayerQuest::GetAdvantureCurReport()
{
	return m_impl->advantureInfo.reportIndex;
}

UInt16 ActionPlayerQuest::GetAdvantureCurPage()
{
	return m_impl->advantureInfo.pageIndex;
}

UInt32 ActionPlayerQuest::GetAdvantureMission1Index()
{
	return m_impl->advantureInfo.mission1Index;
}

UInt32 ActionPlayerQuest::GetAdvantureMission2Index()
{
	return m_impl->advantureInfo.mission2Index;
}

void ActionPlayerQuest::SetRewardFlag(StampState::Enum recv)
{
	m_impl->advantureInfo.pageRewardState = recv;
}

void ActionPlayerQuest::CompletePageReward()
{
	//! 다음이 페이지면, 다음 페이지 정보, 
	//! 다음이 노트면, 다음 노트 정보
	//! 끝이면 end 를 보낸다.

	EntityPlayer * player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	ActionSector* sectorAction = GetEntityAction(player);
	VALID_RETURN(sectorAction, );
	
	EResAdvanturePageReward * res = NEW EResAdvanturePageReward;
	res->nextContents = AdvantureContentType::END;

	auto sendRewardError = [player, res](ErrorItem::Error error)
	{
		res->eError = error;
		SERVER.SendToClient(player, EventPtr(res));
	};

	Index32 aeid = theEventSystem.GetAdventureEvent();
	const auto elem = SCRIPTS.GetGameEventElem(aeid);
	if (nullptr == elem)
	{
		sendRewardError(ErrorItem::E_INVALID_GAME_EVENT);
		return;
	}

	const auto reportInfo = elem->GetAdvReport(m_impl->advantureInfo.reportIndex);
	if (nullptr == reportInfo)
	{
		sendRewardError(ErrorItem::E_INVALID_GAME_EVENT);
		return;
	}

	DeleteQuest(m_impl->advantureInfo.mission1Index);
	DeleteQuest(m_impl->advantureInfo.mission2Index);

	if (reportInfo->lastPage > m_impl->advantureInfo.pageIndex)
	{
		//! 다음 페이지로
		res->nextContents = AdvantureContentType::PAGE;
		sendRewardError(ErrorItem::SUCCESS);

		VERIFY_RETURN(ErrorItem::SUCCESS == ProcAdvantureNextPage(), );
	}
	else if (elem->lastReportIndex > m_impl->advantureInfo.reportIndex)
	{
		//! 다음 노트로
		res->nextContents = AdvantureContentType::REPORT;
		sendRewardError(ErrorItem::SUCCESS);

		EReqLogDb2* log = NEW EReqLogDb2;
		EventPtr logEventPtr(log);
		EventSetter::FillLogForEntity(LogCode::E_ADVANTURE_REPORT_COMPLETE, player, log->action);

		log->action.groupKey = sectorAction->GetDungeonLogKey();
		log->action.var32s.push_back(m_impl->advantureInfo.reportIndex);
		log->action.var32s.push_back(m_impl->advantureInfo.pageIndex);

		SendDataCenter(logEventPtr);

		ENtfAdvantureInfo * ntf = NEW ENtfAdvantureInfo;
		ntf->advatureActiveState = 1;
		ntf->advantureEventId = theEventSystem.GetAdventureEvent();
		ntf->advantureData = m_impl->advantureInfo;

		SERVER.SendToClient(player, EventPtr(ntf));
	}
	else
	{
		//! 모든 노트 완료
		res->nextContents = AdvantureContentType::END;
		sendRewardError(ErrorItem::SUCCESS);

		EReqLogDb2* log = NEW EReqLogDb2;
		EventPtr logEventPtr(log);
		EventSetter::FillLogForEntity(LogCode::E_ADVANTURE_REPORT_COMPLETE, player, log->action);

		log->action.groupKey = sectorAction->GetDungeonLogKey();
		log->action.var32s.push_back(m_impl->advantureInfo.reportIndex);
		log->action.var32s.push_back(m_impl->advantureInfo.pageIndex);

		SendDataCenter(logEventPtr);

		ENtfAdvantureInfo * ntf = NEW ENtfAdvantureInfo;
		ntf->advatureActiveState = 1;
		ntf->advantureEventId = theEventSystem.GetAdventureEvent();
		ntf->advantureData = m_impl->advantureInfo;

		SERVER.SendToClient(player, EventPtr(ntf));
	}
}

void ActionPlayerQuest::NotifyAdvanture(IndexQuest questid)
{
#ifdef __Hotfix_AdvantureReward_by_ray_180920
	// 이벤트가 진행중일때만 보내자.
	if (false == theEventSystem.IsActiveEventType(GameEventElem::ADVANTURE_NOTE))
		return;
#endif

	EntityPlayer * player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsAlive(), );

	ActionSector* sectorAction = GetEntityAction(player);
	VALID_RETURN(sectorAction, );

	UInt32 missionIndex = questid;
	VALID_RETURN(0 < missionIndex, );

	ENtfAdvantureMissionComplete * completePack = NEW ENtfAdvantureMissionComplete;
	EventPtr ptr(completePack);
	completePack->advanturePageIndex = GetAdvantureCurPage();

	CharAdvantureInfo &ainfo = m_impl->advantureInfo;

	MU2_INFO_LOG(LogCategory::CONTENTS, "[Advanture] Notify Advanture [Q:%d] - [M:%d], Cur M = %d,%d", questid, missionIndex, ainfo.mission1Index, ainfo.mission2Index);
	if (ainfo.mission1Index == missionIndex && EventMissionComplete::INCOMPLETE == ainfo.mission1Complete)
	{
		ainfo.mission1Complete = EventMissionComplete::COMPLETE;

		completePack->advantureMissionIndex = ainfo.mission1Index;


		EReqLogDb2* log = NEW EReqLogDb2;
		EventPtr logEventPtr(log);
		EventSetter::FillLogForEntity(LogCode::E_ADVANTURE_MISSION_COMPLETE, player, log->action);

		log->action.groupKey = sectorAction->GetDungeonLogKey();
		log->action.var32s.push_back(GetAdvantureCurReport());
		log->action.var32s.push_back(GetAdvantureCurPage());
		log->action.var32s.push_back(missionIndex);
		log->action.var32s.push_back(questid);
		
		SendDataCenter(logEventPtr);
		
	}
	else if (ainfo.mission2Index == missionIndex && EventMissionComplete::INCOMPLETE == ainfo.mission2Complete)
	{
		ainfo.mission2Complete = EventMissionComplete::COMPLETE;
		completePack->advantureMissionIndex = ainfo.mission2Index;

		EReqLogDb2* log = NEW EReqLogDb2;
		EventPtr logEventPtr(log);
		EventSetter::FillLogForEntity(LogCode::E_ADVANTURE_MISSION_COMPLETE, player, log->action);

		log->action.groupKey = sectorAction->GetDungeonLogKey();
		log->action.var32s.push_back(GetAdvantureCurReport());
		log->action.var32s.push_back(GetAdvantureCurPage());
		log->action.var32s.push_back(missionIndex);
		log->action.var32s.push_back(questid);

		SendDataCenter(logEventPtr);
	}
	else
		return;

	if (EventMissionComplete::COMPLETE == ainfo.mission1Complete && EventMissionComplete::COMPLETE == ainfo.mission2Complete)
	{
		ainfo.pageRewardState = StampState::UNRECIEVE;

		completePack->pageComplete = true;

		EReqLogDb2* log = NEW EReqLogDb2;
		EventPtr logEventPtr(log);
		EventSetter::FillLogForEntity(LogCode::E_ADVANTURE_PAGE_COMPLETE, player, log->action);

		log->action.groupKey = sectorAction->GetDungeonLogKey();
		log->action.var32s.push_back(GetAdvantureCurReport());
		log->action.var32s.push_back(GetAdvantureCurPage());
		log->action.var32s.push_back(missionIndex);
		log->action.var32s.push_back(questid);

		SendDataCenter(logEventPtr);
	}

	UpdateAdvantureInfo();

	SERVER.SendToClient(GetOwnerPlayer(), ptr);
}

void ActionPlayerQuest::UpdateAdvantureInfo()
{
	EntityPlayer * player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsAlive(), );

	EReqDbUpdateAdvantureInfo * reqDbUpdate = NEW EReqDbUpdateAdvantureInfo;
	reqDbUpdate->charId = player->GetCharId();
	reqDbUpdate->advantureInfo = m_impl->advantureInfo;
	reqDbUpdate->ver = theEventSystem.GetAdventureEventVersion();
	SERVER.SendToDb(GetOwnerPlayer(), EventPtr(reqDbUpdate));
}


void ActionPlayerQuest::NotifyLevelUp(CharLevel lv)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	ActionPlayerEvent * actEvent = GetEntityAction(player);
	VALID_RETURN(actEvent, );
	
	Index32 aeid = theEventSystem.GetAdventureEvent();
	const auto elem = SCRIPTS.GetGameEventElem(aeid);
	VALID_RETURN(elem, );
	
	if (actEvent->checkDate(elem->startDate, elem->endDate))
		return;
	
	Bool update = false;
	CharAdvantureInfo &advinfo = m_impl->advantureInfo;
	if (advinfo.reportIndex == 0)
	{
		update = true;

		advinfo.Reset();
	}

	if (0 == advinfo.mission1Index)
	{
		update = true;

		const auto reportInfo = elem->GetAdvReport(advinfo.reportIndex);
		if (nullptr == reportInfo)
			return;

		const auto pageInfo = reportInfo->GetPageInfo(advinfo.pageIndex);
		if (nullptr == pageInfo)
			return;

		if (pageInfo->missions.size() < 2)
			return;

		std::vector<UInt32> missions;
		pageInfo->GetMissions(missions);

		const QuestElem* questelem = SCRIPTS.GetQuest(missions.at(0));
		VALID_RETURN(questelem, );

		VALID_RETURN(questelem->level_Min <= player->GetLevel() && player->GetLevel() <= questelem->level_Max, );

		advinfo.pageRewardState = StampState::NONE;

		advinfo.mission1Index = missions.at(0);
		advinfo.mission2Index = missions.at(1);
		advinfo.mission1Complete = EventMissionComplete::INCOMPLETE;
		advinfo.mission2Complete = EventMissionComplete::INCOMPLETE;

		
		ResetQuest(advinfo.mission1Index);
		AcceptQuest(advinfo.mission1Index);

		ResetQuest(advinfo.mission2Index);
		AcceptQuest(advinfo.mission2Index);
	}

	// DB 갱신 요청
	if (update)
		UpdateAdvantureInfo();
}


#ifdef __Patch_AutoGain_Quest_Check_by_cheolhoon_181008
void ActionPlayerQuest::CheckMissingAutoGainQuest()
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	auto it = m_impl->questAutoGainCheckMap.begin();
	while(it != m_impl->questAutoGainCheckMap.end())
	{
		IndexQuest index = it->first;		// 자동획득퀘스트
		IndexQuest preIndex = it->second;	// 선행퀘스트

		// 이미 자동획득 퀘스트를 받은 경우.
		if(IsProgressOrComplete(index) || IsFinished(index))
		{
			it = m_impl->questAutoGainCheckMap.erase(it);
			continue;
		}
		// 선행 퀘스트가 완료되었는데 자동획득 퀘스트를 받지 못한 경우.
		else if(IsFinished(preIndex))
		{
			const QuestElem* elem = SCRIPTS.GetQuest(index);
			if(nullptr == elem)
			{
				theGameMsg.SendGameDebugMsg(player, L"SetupFinishQuestFromDB - %d 존재하지 않는 퀘스트.\n", index);
				it = m_impl->questAutoGainCheckMap.erase(it);
				continue;
			}

			//AcceptAutoGainQuest(index, (QuestElem::AUTO_GAIN_IGNORE_MASK)elem->auto_gain_ignore_cond); 기획요청으로 자동획득을 못한 퀘스트는 받을 수 있도록.
			ErrorQuest::Error eError = AcceptAutoGainQuest(index, QuestElem::AUTO_GAIN_IGNORE_MASK::QC_VOLUME);
			if(ErrorQuest::Error::SUCCESS == eError)
			{
				it = m_impl->questAutoGainCheckMap.erase(it);
				continue;
			}
		}

		++it;
	}
}
#endif

#ifdef __Patch_Quest_Over_MaxCount_by_cheolhoon_181025
void ActionPlayerQuest::SendWaitQuest()
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	ENtfQuestWaitInfo* ntf = NEW ENtfQuestWaitInfo;
	ntf->waitCount = static_cast<BYTE>(m_impl->questWaitSet.size());

	SERVER.SendToClient(player, EventPtr(ntf));
}

void ActionPlayerQuest::WaitQuest(IndexQuest questId, QuestStoryType::Enum questStoryType)
{
	QuestInfo info;
	info.questId = questId;
	info.progressFlag = QuestFlag::WAIT;

	UpdateQuestToDB(info);

	QuestWaitInfo waitInfo;
	waitInfo.questId = questId;
	waitInfo.registTime = DateTime::GetPresentTime().GetTime();
	waitInfo.questStoryPriority = QuestStoryPriorityType::ToQuestStoryPriorityType(questStoryType);

	auto it = std::find(m_impl->questWaitSet.begin(), m_impl->questWaitSet.end(), waitInfo);
	if(m_impl->questWaitSet.end() == it)
	{
		m_impl->questWaitSet.insert(waitInfo);
	}	

	SendWaitQuest();
}

void ActionPlayerQuest::CheckWaitQuest()
{
	// 퀘스트를 받을 수 있고 대기열이 있는 경우.
	if(false == m_impl->questWaitSet.empty() && m_impl->quests.size() < Limits::QUEST_PROGRESS_INFO_COUNT)
	{
		auto iter = m_impl->questWaitSet.begin();
		IndexQuest questId = iter->questId;
		ErrorQuest::Error eError = ErrorQuest::Error::E_FAILED;

		if(m_impl->questAutoGainCheckMap.end() != m_impl->questAutoGainCheckMap.find(questId))
		{
			// 자동획득 퀘스트.
			eError = AcceptAutoGainQuest(questId, QuestElem::AUTO_GAIN_IGNORE_MASK::QC_VOLUME);
		}
		else
		{
			eError = AcceptQuest(questId);
		}

		if(ErrorQuest::Error::SUCCESS == eError)
		{
			m_impl->questWaitSet.erase(iter);
		}

		SendWaitQuest();
	}
}
#endif

#ifdef __Patch_Add_User_Emotion_Quest_Mission_Type_ch_20190117
Bool ActionPlayerQuest::GetEventVolumeIndexListByQuestMissionType(QuestMissionType::Enum type, UInt32Vector& vecIndex)
{	
	if(QuestMissionType::End > type)
	{
		auto missionList = m_impl->questMissionList[type];
		for(auto iter : missionList)
		{
			switch (type)
			{
			case QuestMissionType::UserEmotion:
				{
					QuestUserEmotion* mission = static_cast<QuestUserEmotion*>(iter.mission);
					VERIFY_RETURN(mission, false);

					vecIndex.push_back(mission->eventVolumeIndex);
				}
				break;
			}
		}
	}

	return true;
}
#endif

} // mu2s
