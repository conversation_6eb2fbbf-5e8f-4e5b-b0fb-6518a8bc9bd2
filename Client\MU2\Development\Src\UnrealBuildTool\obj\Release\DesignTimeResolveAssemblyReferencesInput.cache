   .winmd.dll.exe    7F:\Release_Branch\Client\MU2\Development\Src\app.confignC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.0\Profile\Client\mscorlib.dll4F:\Release_Branch\Client\MU2\Binaries\RPCUtility.exeqC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.0\Profile\Client\System.Core.dllqC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.0\Profile\Client\System.Data.dlllC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.0\Profile\Client\System.dll}C:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.0\Profile\Client\System.Runtime.Remoting.dllpC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.0\Profile\Client\System.Xml.dll       RC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.0   Full                 {CandidateAssemblyFiles}{HintPathFromItem}{TargetFrameworkDirectory}B{Registry:Software\Microsoft\.NETFramework,v4.0,AssemblyFoldersEx}
{RawFileName}NF:\Release_Branch\Client\MU2\Development\Intermediate\UnrealBuildTool\Release\    ClientB{Registry:Software\Microsoft\.NETFramework,v4.0,AssemblyFoldersEx}rF:\Release_Branch\Client\MU2\Development\Src\UnrealBuildTool\obj\Release\DesignTimeResolveAssemblyReferences.cache   aC:\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.0\Profile\Client).NETFramework,Version=v4.0,Profile=Client.NET Framework 4 Client Profilev4.0msil
v4.0.30319         