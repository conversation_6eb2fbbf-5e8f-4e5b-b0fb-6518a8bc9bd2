; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	?IsInstance@Execution@mu2@@UEBA_NXZ		; mu2::Execution::IsInstance
PUBLIC	?IsWorldcross@Execution@mu2@@UEBA_NXZ		; mu2::Execution::IsWorldcross
PUBLIC	?IsTournamentEnter@Execution@mu2@@UEBA_NXZ	; mu2::Execution::IsTournamentEnter
PUBLIC	?IsWorldcrossing@Execution@mu2@@UEBA_NXZ	; mu2::Execution::IsWorldcrossing
PUBLIC	?IsSing<PERSON>@Execution@mu2@@UEBA_NXZ		; mu2::Execution::IsSingle
PUBLIC	?IsGroup@Execution@mu2@@UEBA_NXZ		; mu2::Execution::IsGroup
PUBLIC	?GetDamageMeterKey@Execution@mu2@@UEBA?BIXZ	; mu2::Execution::GetDamageMeterKey
PUBLIC	?IsChannel@Channel@mu2@@UEBA_NXZ		; mu2::Channel::IsChannel
PUBLIC	?CanUseFoothold@Channel@mu2@@UEBA_NXZ		; mu2::Channel::CanUseFoothold
PUBLIC	?IsValid@Channel@mu2@@UEBA?B_NXZ		; mu2::Channel::IsValid
PUBLIC	?IsNull@NullChannel@mu2@@UEBA_NXZ		; mu2::NullChannel::IsNull
PUBLIC	?EnterExecution@NullChannel@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ; mu2::NullChannel::EnterExecution
PUBLIC	?ExitExecutionPost@NullChannel@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ; mu2::NullChannel::ExitExecutionPost
PUBLIC	??_GNullChannel@mu2@@UEAAPEAXI@Z		; mu2::NullChannel::`scalar deleting destructor'
PUBLIC	?execution@NullChannel@mu2@@2V12@A		; mu2::NullChannel::execution
PUBLIC	??_7NullChannel@mu2@@6B@			; mu2::NullChannel::`vftable'
PUBLIC	??_R4NullChannel@mu2@@6B@			; mu2::NullChannel::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVNullChannel@mu2@@@8			; mu2::NullChannel `RTTI Type Descriptor'
PUBLIC	??_R3NullChannel@mu2@@8				; mu2::NullChannel::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2NullChannel@mu2@@8				; mu2::NullChannel::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@NullChannel@mu2@@8		; mu2::NullChannel::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@Channel@mu2@@8			; mu2::Channel::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R0?AVChannel@mu2@@@8				; mu2::Channel `RTTI Type Descriptor'
PUBLIC	??_R3Channel@mu2@@8				; mu2::Channel::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2Channel@mu2@@8				; mu2::Channel::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@Execution@mu2@@8			; mu2::Execution::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R0?AVExecution@mu2@@@8			; mu2::Execution `RTTI Type Descriptor'
PUBLIC	??_R3Execution@mu2@@8				; mu2::Execution::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2Execution@mu2@@8				; mu2::Execution::`RTTI Base Class Array'
EXTRN	??3@YAXPEAX_K@Z:PROC				; operator delete
EXTRN	atexit:PROC
EXTRN	?OnDestroyedFailed@Execution@mu2@@UEAAXXZ:PROC	; mu2::Execution::OnDestroyedFailed
EXTRN	?OnCreatedFailed@Execution@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::Execution::OnCreatedFailed
EXTRN	?OnResGameReady@Execution@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::Execution::OnResGameReady
EXTRN	?EnterExecutionPost@Execution@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::Execution::EnterExecutionPost
EXTRN	?OnShutdownZone@Execution@mu2@@UEAAXXZ:PROC	; mu2::Execution::OnShutdownZone
EXTRN	?SendReqJoinExecution@Execution@mu2@@UEBA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@@Z:PROC ; mu2::Execution::SendReqJoinExecution
EXTRN	?IsValid@Execution@mu2@@UEBA?B_NXZ:PROC		; mu2::Execution::IsValid
EXTRN	?IsDestroyable@Execution@mu2@@UEBA_NXZ:PROC	; mu2::Execution::IsDestroyable
EXTRN	?GetRelayServerId@Execution@mu2@@UEBA?BIXZ:PROC	; mu2::Execution::GetRelayServerId
EXTRN	??0Channel@mu2@@QEAA@PEAVManagerExecutionBase@1@@Z:PROC ; mu2::Channel::Channel
EXTRN	??1Channel@mu2@@UEAA@XZ:PROC			; mu2::Channel::~Channel
EXTRN	?IsRunning@Channel@mu2@@UEBA_NXZ:PROC		; mu2::Channel::IsRunning
EXTRN	?Init@Channel@mu2@@UEAA_NAEAUExecutionCreateInfo@2@@Z:PROC ; mu2::Channel::Init
EXTRN	?ToString@Channel@mu2@@UEBA?BV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ:PROC ; mu2::Channel::ToString
EXTRN	?IsJoinable@Channel@mu2@@UEBA_NPEAVEntityPlayer@2@@Z:PROC ; mu2::Channel::IsJoinable
EXTRN	?EnterExecutionPrev@Channel@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::Channel::EnterExecutionPrev
EXTRN	?OnCreatedSucceed@Channel@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::Channel::OnCreatedSucceed
EXTRN	?OnDestroyed@Channel@mu2@@UEAAXXZ:PROC		; mu2::Channel::OnDestroyed
EXTRN	?Update@Channel@mu2@@UEAAXXZ:PROC		; mu2::Channel::Update
EXTRN	?Fillup@Channel@mu2@@UEAAXAEAUChannelInfo@2@@Z:PROC ; mu2::Channel::Fillup
EXTRN	?Fillup@Channel@mu2@@UEAAXAEAPEAUEwzReqCreateExecution@2@@Z:PROC ; mu2::Channel::Fillup
EXTRN	?Fillup@Channel@mu2@@UEAAXAEAPEAUEwzReqDestroyExecution@2@@Z:PROC ; mu2::Channel::Fillup
EXTRN	?Fillup@Channel@mu2@@UEBAXAEAVJoinZoneContextDest@2@@Z:PROC ; mu2::Channel::Fillup
EXTRN	?Fillup@Channel@mu2@@MEBA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAPEAUEwzReqJoinExecution@2@@Z:PROC ; mu2::Channel::Fillup
EXTRN	??_ENullChannel@mu2@@UEAAPEAXI@Z:PROC		; mu2::NullChannel::`vector deleting destructor'
EXTRN	??_7type_info@@6B@:BYTE				; type_info::`vftable'
_BSS	SEGMENT
?execution@NullChannel@mu2@@2V12@A DB 090H DUP (?)	; mu2::NullChannel::execution
_BSS	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?IsValid@Channel@mu2@@UEBA?B_NXZ DD imagerel $LN5
	DD	imagerel $LN5+62
	DD	imagerel $unwind$?IsValid@Channel@mu2@@UEBA?B_NXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GNullChannel@mu2@@UEAAPEAXI@Z DD imagerel $LN10
	DD	imagerel $LN10+76
	DD	imagerel $unwind$??_GNullChannel@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__E?execution@NullChannel@mu2@@2V12@A@@YAXXZ DD imagerel ??__E?execution@NullChannel@mu2@@2V12@A@@YAXXZ
	DD	imagerel ??__E?execution@NullChannel@mu2@@2V12@A@@YAXXZ+50
	DD	imagerel $unwind$??__E?execution@NullChannel@mu2@@2V12@A@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__F?execution@NullChannel@mu2@@2V12@A@@YAXXZ DD imagerel ??__F?execution@NullChannel@mu2@@2V12@A@@YAXXZ
	DD	imagerel ??__F?execution@NullChannel@mu2@@2V12@A@@YAXXZ+36
	DD	imagerel $unwind$??__F?execution@NullChannel@mu2@@2V12@A@@YAXXZ
pdata	ENDS
CRT$XCU	SEGMENT
??execution$initializer$@NullChannel@mu2@@2P6AXXZEA@@3P6AXXZEA DQ FLAT:??__E?execution@NullChannel@mu2@@2V12@A@@YAXXZ ; ??execution$initializer$@NullChannel@mu2@@2P6AXXZEA@@3P6AXXZEA
CRT$XCU	ENDS
;	COMDAT ??_R2Execution@mu2@@8
rdata$r	SEGMENT
??_R2Execution@mu2@@8 DD imagerel ??_R1A@?0A@EA@Execution@mu2@@8 ; mu2::Execution::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3Execution@mu2@@8
rdata$r	SEGMENT
??_R3Execution@mu2@@8 DD 00H				; mu2::Execution::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2Execution@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVExecution@mu2@@@8
data$rs	SEGMENT
??_R0?AVExecution@mu2@@@8 DQ FLAT:??_7type_info@@6B@	; mu2::Execution `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVExecution@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R1A@?0A@EA@Execution@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@Execution@mu2@@8 DD imagerel ??_R0?AVExecution@mu2@@@8 ; mu2::Execution::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3Execution@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2Channel@mu2@@8
rdata$r	SEGMENT
??_R2Channel@mu2@@8 DD imagerel ??_R1A@?0A@EA@Channel@mu2@@8 ; mu2::Channel::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@Execution@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3Channel@mu2@@8
rdata$r	SEGMENT
??_R3Channel@mu2@@8 DD 00H				; mu2::Channel::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2Channel@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVChannel@mu2@@@8
data$rs	SEGMENT
??_R0?AVChannel@mu2@@@8 DQ FLAT:??_7type_info@@6B@	; mu2::Channel `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVChannel@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R1A@?0A@EA@Channel@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@Channel@mu2@@8 DD imagerel ??_R0?AVChannel@mu2@@@8 ; mu2::Channel::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3Channel@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@NullChannel@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@NullChannel@mu2@@8 DD imagerel ??_R0?AVNullChannel@mu2@@@8 ; mu2::NullChannel::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	02H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3NullChannel@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2NullChannel@mu2@@8
rdata$r	SEGMENT
??_R2NullChannel@mu2@@8 DD imagerel ??_R1A@?0A@EA@NullChannel@mu2@@8 ; mu2::NullChannel::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@Channel@mu2@@8
	DD	imagerel ??_R1A@?0A@EA@Execution@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3NullChannel@mu2@@8
rdata$r	SEGMENT
??_R3NullChannel@mu2@@8 DD 00H				; mu2::NullChannel::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	03H
	DD	imagerel ??_R2NullChannel@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVNullChannel@mu2@@@8
data$rs	SEGMENT
??_R0?AVNullChannel@mu2@@@8 DQ FLAT:??_7type_info@@6B@	; mu2::NullChannel `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVNullChannel@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4NullChannel@mu2@@6B@
rdata$r	SEGMENT
??_R4NullChannel@mu2@@6B@ DD 01H			; mu2::NullChannel::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVNullChannel@mu2@@@8
	DD	imagerel ??_R3NullChannel@mu2@@8
	DD	imagerel ??_R4NullChannel@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_7NullChannel@mu2@@6B@
CONST	SEGMENT
??_7NullChannel@mu2@@6B@ DQ FLAT:??_R4NullChannel@mu2@@6B@ ; mu2::NullChannel::`vftable'
	DQ	FLAT:??_ENullChannel@mu2@@UEAAPEAXI@Z
	DQ	FLAT:?OnDestroyed@Channel@mu2@@UEAAXXZ
	DQ	FLAT:?OnDestroyedFailed@Execution@mu2@@UEAAXXZ
	DQ	FLAT:?OnCreatedSucceed@Channel@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?OnCreatedFailed@Execution@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?OnResGameReady@Execution@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?EnterExecutionPrev@Channel@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?EnterExecution@NullChannel@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?EnterExecutionPost@Execution@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?ExitExecutionPost@NullChannel@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?Init@Channel@mu2@@UEAA_NAEAUExecutionCreateInfo@2@@Z
	DQ	FLAT:?Update@Channel@mu2@@UEAAXXZ
	DQ	FLAT:?OnShutdownZone@Execution@mu2@@UEAAXXZ
	DQ	FLAT:?SendReqJoinExecution@Execution@mu2@@UEBA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@@Z
	DQ	FLAT:?IsInstance@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsChannel@Channel@mu2@@UEBA_NXZ
	DQ	FLAT:?IsWorldcross@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsTournamentEnter@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsWorldcrossing@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsSingle@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsGroup@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsNull@NullChannel@mu2@@UEBA_NXZ
	DQ	FLAT:?IsValid@Channel@mu2@@UEBA?B_NXZ
	DQ	FLAT:?IsRunning@Channel@mu2@@UEBA_NXZ
	DQ	FLAT:?IsJoinable@Channel@mu2@@UEBA_NPEAVEntityPlayer@2@@Z
	DQ	FLAT:?IsDestroyable@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?GetDamageMeterKey@Execution@mu2@@UEBA?BIXZ
	DQ	FLAT:?ToString@Channel@mu2@@UEBA?BV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ
	DQ	FLAT:?GetRelayServerId@Execution@mu2@@UEBA?BIXZ
	DQ	FLAT:?Fillup@Channel@mu2@@MEBA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAPEAUEwzReqJoinExecution@2@@Z
	DQ	FLAT:?Fillup@Channel@mu2@@UEBAXAEAVJoinZoneContextDest@2@@Z
	DQ	FLAT:?Fillup@Channel@mu2@@UEAAXAEAPEAUEwzReqDestroyExecution@2@@Z
	DQ	FLAT:?Fillup@Channel@mu2@@UEAAXAEAPEAUEwzReqCreateExecution@2@@Z
	DQ	FLAT:?CanUseFoothold@Channel@mu2@@UEBA_NXZ
	DQ	FLAT:?Fillup@Channel@mu2@@UEAAXAEAUChannelInfo@2@@Z
CONST	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__F?execution@NullChannel@mu2@@2V12@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__E?execution@NullChannel@mu2@@2V12@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GNullChannel@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?IsValid@Channel@mu2@@UEBA?B_NXZ DD 010901H
	DD	06209H
xdata	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ChannelNull.h
;	COMDAT ??__F?execution@NullChannel@mu2@@2V12@A@@YAXXZ
text$yd	SEGMENT
??__F?execution@NullChannel@mu2@@2V12@A@@YAXXZ PROC	; `dynamic atexit destructor for 'mu2::NullChannel::execution'', COMDAT
  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 17   : 		virtual~NullChannel() override {}

  00004	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_7NullChannel@mu2@@6B@
  0000b	48 89 05 00 00
	00 00		 mov	 QWORD PTR ?execution@NullChannel@mu2@@2V12@A, rax
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?execution@NullChannel@mu2@@2V12@A ; mu2::NullChannel::execution
  00019	e8 00 00 00 00	 call	 ??1Channel@mu2@@UEAA@XZ	; mu2::Channel::~Channel
  0001e	90		 npad	 1
  0001f	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00023	c3		 ret	 0
??__F?execution@NullChannel@mu2@@2V12@A@@YAXXZ ENDP	; `dynamic atexit destructor for 'mu2::NullChannel::execution''
text$yd	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ChannelNull.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ChannelNull.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ChannelNull.cpp
;	COMDAT ??__E?execution@NullChannel@mu2@@2V12@A@@YAXXZ
text$di	SEGMENT
??__E?execution@NullChannel@mu2@@2V12@A@@YAXXZ PROC	; `dynamic initializer for 'mu2::NullChannel::execution'', COMDAT

; 11   : 	NullChannel NullChannel::execution(NULL);

  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ChannelNull.h

; 16   : 		NullChannel(ManagerExecutionBase* pOwnerManager) : Channel(pOwnerManager) {}

  00004	33 d2		 xor	 edx, edx
  00006	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?execution@NullChannel@mu2@@2V12@A ; mu2::NullChannel::execution
  0000d	e8 00 00 00 00	 call	 ??0Channel@mu2@@QEAA@PEAVManagerExecutionBase@1@@Z ; mu2::Channel::Channel
  00012	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_7NullChannel@mu2@@6B@
  00019	48 89 05 00 00
	00 00		 mov	 QWORD PTR ?execution@NullChannel@mu2@@2V12@A, rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ChannelNull.cpp

; 11   : 	NullChannel NullChannel::execution(NULL);

  00020	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??__F?execution@NullChannel@mu2@@2V12@A@@YAXXZ ; `dynamic atexit destructor for 'mu2::NullChannel::execution''
  00027	e8 00 00 00 00	 call	 atexit
  0002c	90		 npad	 1
  0002d	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00031	c3		 ret	 0
??__E?execution@NullChannel@mu2@@2V12@A@@YAXXZ ENDP	; `dynamic initializer for 'mu2::NullChannel::execution''
text$di	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ChannelNull.h
;	COMDAT ??_GNullChannel@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GNullChannel@mu2@@UEAAPEAXI@Z PROC			; mu2::NullChannel::`scalar deleting destructor', COMDAT
$LN10:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 17   : 		virtual~NullChannel() override {}

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7NullChannel@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx
  0001c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00021	e8 00 00 00 00	 call	 ??1Channel@mu2@@UEAA@XZ	; mu2::Channel::~Channel
  00026	90		 npad	 1
  00027	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  0002b	83 e0 01	 and	 eax, 1
  0002e	85 c0		 test	 eax, eax
  00030	74 10		 je	 SHORT $LN2@scalar
  00032	ba 90 00 00 00	 mov	 edx, 144		; 00000090H
  00037	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0003c	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00041	90		 npad	 1
$LN2@scalar:
  00042	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00047	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0004b	c3		 ret	 0
??_GNullChannel@mu2@@UEAAPEAXI@Z ENDP			; mu2::NullChannel::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ChannelNull.h
;	COMDAT ?ExitExecutionPost@NullChannel@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
_TEXT	SEGMENT
this$ = 8
__formal$ = 16
__formal$ = 24
?ExitExecutionPost@NullChannel@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z PROC ; mu2::NullChannel::ExitExecutionPost, COMDAT

; 22   : 		virtual ErrorJoin::Error ExitExecutionPost(EntityPlayer*, EventPtr&) override { return ErrorJoin::NullChannelFailed; }

  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	b8 f5 1a 06 00	 mov	 eax, 400117		; 00061af5H
  00014	c3		 ret	 0
?ExitExecutionPost@NullChannel@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ENDP ; mu2::NullChannel::ExitExecutionPost
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ChannelNull.h
;	COMDAT ?EnterExecution@NullChannel@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
_TEXT	SEGMENT
this$ = 8
__formal$ = 16
__formal$ = 24
?EnterExecution@NullChannel@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z PROC ; mu2::NullChannel::EnterExecution, COMDAT

; 21   : 		virtual ErrorJoin::Error EnterExecution(EntityPlayer*, EventPtr&) override { return ErrorJoin::NullChannelFailed; }

  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	b8 f5 1a 06 00	 mov	 eax, 400117		; 00061af5H
  00014	c3		 ret	 0
?EnterExecution@NullChannel@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ENDP ; mu2::NullChannel::EnterExecution
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ChannelNull.h
;	COMDAT ?IsNull@NullChannel@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsNull@NullChannel@mu2@@UEBA_NXZ PROC			; mu2::NullChannel::IsNull, COMDAT

; 20   : 		virtual Bool	IsNull() const override { return true; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	b0 01		 mov	 al, 1
  00007	c3		 ret	 0
?IsNull@NullChannel@mu2@@UEBA_NXZ ENDP			; mu2::NullChannel::IsNull
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Channel.h
;	COMDAT ?IsValid@Channel@mu2@@UEBA?B_NXZ
_TEXT	SEGMENT
tv74 = 32
this$ = 64
?IsValid@Channel@mu2@@UEBA?B_NXZ PROC			; mu2::Channel::IsValid, COMDAT

; 35   : 		virtual const Bool			IsValid() const override { return m_channelId > 0 && __super::IsValid(); }

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H
  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	83 b8 80 00 00
	00 00		 cmp	 DWORD PTR [rax+128], 0
  00015	76 18		 jbe	 SHORT $LN3@IsValid
  00017	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0001c	e8 00 00 00 00	 call	 ?IsValid@Execution@mu2@@UEBA?B_NXZ ; mu2::Execution::IsValid
  00021	0f b6 c0	 movzx	 eax, al
  00024	85 c0		 test	 eax, eax
  00026	74 07		 je	 SHORT $LN3@IsValid
  00028	c6 44 24 20 01	 mov	 BYTE PTR tv74[rsp], 1
  0002d	eb 05		 jmp	 SHORT $LN4@IsValid
$LN3@IsValid:
  0002f	c6 44 24 20 00	 mov	 BYTE PTR tv74[rsp], 0
$LN4@IsValid:
  00034	0f b6 44 24 20	 movzx	 eax, BYTE PTR tv74[rsp]
  00039	48 83 c4 38	 add	 rsp, 56			; 00000038H
  0003d	c3		 ret	 0
?IsValid@Channel@mu2@@UEBA?B_NXZ ENDP			; mu2::Channel::IsValid
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Channel.h
;	COMDAT ?CanUseFoothold@Channel@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?CanUseFoothold@Channel@mu2@@UEBA_NXZ PROC		; mu2::Channel::CanUseFoothold, COMDAT

; 31   : 		virtual Bool				CanUseFoothold() const { return true; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	b0 01		 mov	 al, 1
  00007	c3		 ret	 0
?CanUseFoothold@Channel@mu2@@UEBA_NXZ ENDP		; mu2::Channel::CanUseFoothold
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Channel.h
;	COMDAT ?IsChannel@Channel@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsChannel@Channel@mu2@@UEBA_NXZ PROC			; mu2::Channel::IsChannel, COMDAT

; 28   : 		virtual Bool				IsChannel() const final { return true; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	b0 01		 mov	 al, 1
  00007	c3		 ret	 0
?IsChannel@Channel@mu2@@UEBA_NXZ ENDP			; mu2::Channel::IsChannel
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?GetDamageMeterKey@Execution@mu2@@UEBA?BIXZ
_TEXT	SEGMENT
this$ = 8
?GetDamageMeterKey@Execution@mu2@@UEBA?BIXZ PROC	; mu2::Execution::GetDamageMeterKey, COMDAT

; 95   : 		virtual const DamageMeterKey	GetDamageMeterKey() const { return 0; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	33 c0		 xor	 eax, eax
  00007	c3		 ret	 0
?GetDamageMeterKey@Execution@mu2@@UEBA?BIXZ ENDP	; mu2::Execution::GetDamageMeterKey
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?IsGroup@Execution@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsGroup@Execution@mu2@@UEBA_NXZ PROC			; mu2::Execution::IsGroup, COMDAT

; 85   : 		virtual Bool					IsGroup() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsGroup@Execution@mu2@@UEBA_NXZ ENDP			; mu2::Execution::IsGroup
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?IsSingle@Execution@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsSingle@Execution@mu2@@UEBA_NXZ PROC			; mu2::Execution::IsSingle, COMDAT

; 84   : 		virtual Bool					IsSingle() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsSingle@Execution@mu2@@UEBA_NXZ ENDP			; mu2::Execution::IsSingle
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?IsWorldcrossing@Execution@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsWorldcrossing@Execution@mu2@@UEBA_NXZ PROC		; mu2::Execution::IsWorldcrossing, COMDAT

; 83   : 		virtual Bool					IsWorldcrossing() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsWorldcrossing@Execution@mu2@@UEBA_NXZ ENDP		; mu2::Execution::IsWorldcrossing
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?IsTournamentEnter@Execution@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsTournamentEnter@Execution@mu2@@UEBA_NXZ PROC		; mu2::Execution::IsTournamentEnter, COMDAT

; 82   : 		virtual Bool					IsTournamentEnter() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsTournamentEnter@Execution@mu2@@UEBA_NXZ ENDP		; mu2::Execution::IsTournamentEnter
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?IsWorldcross@Execution@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsWorldcross@Execution@mu2@@UEBA_NXZ PROC		; mu2::Execution::IsWorldcross, COMDAT

; 81   : 		virtual Bool					IsWorldcross() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsWorldcross@Execution@mu2@@UEBA_NXZ ENDP		; mu2::Execution::IsWorldcross
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?IsInstance@Execution@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsInstance@Execution@mu2@@UEBA_NXZ PROC		; mu2::Execution::IsInstance, COMDAT

; 79   : 		virtual Bool					IsInstance() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsInstance@Execution@mu2@@UEBA_NXZ ENDP		; mu2::Execution::IsInstance
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ChannelNull.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ChannelNull.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
