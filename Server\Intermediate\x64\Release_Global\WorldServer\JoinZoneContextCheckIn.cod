; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	?SendResJoinResult@JoinContextCheckIn@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z ; mu2::JoinContextCheckIn::SendResJoinResult
PUBLIC	?ReqJoinExecutionPrev@JoinContextCheckIn@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@@Z ; mu2::JoinContextCheckIn::ReqJoinExecutionPrev
PUBLIC	?OnKnightageGameReady@JoinContextCheckIn@mu2@@UEBAXAEAPEAVEntityPlayer@2@@Z ; mu2::JoinContextCheckIn::OnKnightageGameReady
PUBLIC	??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@ ; `string'
PUBLIC	??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@		; `string'
PUBLIC	??_C@_06BALNJMNP@player@			; `string'
PUBLIC	??_C@_0FF@MEOPPEOA@F?3?2Release_Branch?2Server?2Develo@ ; `string'
PUBLIC	??_C@_0CO@HINPDLBI@mu2?3?3JoinContextCheckIn?3?3ReqJoi@ ; `string'
EXTRN	__imp_GetStdHandle:PROC
EXTRN	__imp_SetConsoleTextAttribute:PROC
EXTRN	?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ:PROC	; mu2::Logger::Logging
EXTRN	?GetAction@Entity@mu2@@QEBAPEAVAction@2@I@Z:PROC ; mu2::Entity::GetAction
EXTRN	?GetPresentTime@DateTime@mu2@@SA?BV12@XZ:PROC	; mu2::DateTime::GetPresentTime
EXTRN	?ResetCharLoginTime@EntityPlayer@mu2@@QEAAXAEBVDateTime@2@@Z:PROC ; mu2::EntityPlayer::ResetCharLoginTime
EXTRN	?SendEwcResZoneJoin@JoinContextBase@mu2@@IEBAXAEAPEAVEntityPlayer@2@AEBVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z:PROC ; mu2::JoinContextBase::SendEwcResZoneJoin
EXTRN	?OnLogin@PlayerKnightageAction@mu2@@QEAAXXZ:PROC ; mu2::PlayerKnightageAction::OnLogin
;	COMDAT pdata
pdata	SEGMENT
$pdata$?SendResJoinResult@JoinContextCheckIn@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z DD imagerel $LN3
	DD	imagerel $LN3+55
	DD	imagerel $unwind$?SendResJoinResult@JoinContextCheckIn@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?ReqJoinExecutionPrev@JoinContextCheckIn@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@@Z DD imagerel $LN4
	DD	imagerel $LN4+233
	DD	imagerel $unwind$?ReqJoinExecutionPrev@JoinContextCheckIn@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?OnKnightageGameReady@JoinContextCheckIn@mu2@@UEBAXAEAPEAVEntityPlayer@2@@Z DD imagerel $LN9
	DD	imagerel $LN9+157
	DD	imagerel $unwind$?OnKnightageGameReady@JoinContextCheckIn@mu2@@UEBAXAEAPEAVEntityPlayer@2@@Z
pdata	ENDS
;	COMDAT ??_C@_0CO@HINPDLBI@mu2?3?3JoinContextCheckIn?3?3ReqJoi@
CONST	SEGMENT
??_C@_0CO@HINPDLBI@mu2?3?3JoinContextCheckIn?3?3ReqJoi@ DB 'mu2::JoinCont'
	DB	'extCheckIn::ReqJoinExecutionPrev', 00H	; `string'
CONST	ENDS
;	COMDAT ??_C@_0FF@MEOPPEOA@F?3?2Release_Branch?2Server?2Develo@
CONST	SEGMENT
??_C@_0FF@MEOPPEOA@F?3?2Release_Branch?2Server?2Develo@ DB 'F:\Release_Br'
	DB	'anch\Server\Development\Frontend\WorldServer\JoinZoneContextC'
	DB	'heckIn.cpp', 00H				; `string'
CONST	ENDS
;	COMDAT ??_C@_06BALNJMNP@player@
CONST	SEGMENT
??_C@_06BALNJMNP@player@ DB 'player', 00H		; `string'
CONST	ENDS
;	COMDAT ??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
CONST	SEGMENT
??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@ DB 'g', 00H, 'a', 00H, 'm', 00H, 'e'
	DB	00H, 00H, 00H				; `string'
CONST	ENDS
;	COMDAT ??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
CONST	SEGMENT
??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@ DB '%'
	DB	's> ASSERT - %s, %s(%d)', 00H		; `string'
CONST	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?OnKnightageGameReady@JoinContextCheckIn@mu2@@UEBAXAEAPEAVEntityPlayer@2@@Z DD 010e01H
	DD	0c20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?ReqJoinExecutionPrev@JoinContextCheckIn@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@@Z DD 021601H
	DD	0110116H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?SendResJoinResult@JoinContextCheckIn@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z DD 011801H
	DD	04218H
xdata	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextCheckIn.cpp
; File F:\Release_Branch\Server\Development\Framework\Entity\Action.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextCheckIn.cpp
; File F:\Release_Branch\Server\Development\Framework\Entity\Action.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextCheckIn.cpp
;	COMDAT ?OnKnightageGameReady@JoinContextCheckIn@mu2@@UEBAXAEAPEAVEntityPlayer@2@@Z
_TEXT	SEGMENT
$T1 = 32
this$ = 40
elem$2 = 48
owner$ = 56
$T3 = 64
$T4 = 72
knightageAction$ = 80
this$ = 112
player$ = 120
?OnKnightageGameReady@JoinContextCheckIn@mu2@@UEBAXAEAPEAVEntityPlayer@2@@Z PROC ; mu2::JoinContextCheckIn::OnKnightageGameReady, COMDAT

; 25   : 	{	

$LN9:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 68	 sub	 rsp, 104		; 00000068H

; 26   : 		PlayerKnightageAction* knightageAction = GetEntityAction(player);

  0000e	48 8b 44 24 78	 mov	 rax, QWORD PTR player$[rsp]
  00013	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00016	48 89 44 24 38	 mov	 QWORD PTR owner$[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Entity\Action.h

; 43   : 	GetEntityAction(Entity* owner) : owner_(owner) {}

  0001b	48 8b 44 24 38	 mov	 rax, QWORD PTR owner$[rsp]
  00020	48 89 44 24 40	 mov	 QWORD PTR $T3[rsp], rax
  00025	48 8d 44 24 40	 lea	 rax, QWORD PTR $T3[rsp]
  0002a	48 89 44 24 48	 mov	 QWORD PTR $T4[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextCheckIn.cpp

; 26   : 		PlayerKnightageAction* knightageAction = GetEntityAction(player);

  0002f	48 8b 44 24 48	 mov	 rax, QWORD PTR $T4[rsp]
  00034	48 89 44 24 28	 mov	 QWORD PTR this$[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Entity\Action.h

; 49   : 		if ( nullptr == owner_ )

  00039	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  0003e	48 83 38 00	 cmp	 QWORD PTR [rax], 0
  00042	75 0b		 jne	 SHORT $LN6@OnKnightag

; 50   : 		{
; 51   : 			return nullptr;

  00044	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR $T1[rsp], 0
  0004d	eb 34		 jmp	 SHORT $LN5@OnKnightag
$LN6@OnKnightag:

; 52   : 		}
; 53   : 
; 54   : 		Action* elem = owner_->GetAction( ActionType::ID );

  0004f	ba 0f 00 00 00	 mov	 edx, 15
  00054	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00059	48 8b 08	 mov	 rcx, QWORD PTR [rax]
  0005c	e8 00 00 00 00	 call	 ?GetAction@Entity@mu2@@QEBAPEAVAction@2@I@Z ; mu2::Entity::GetAction
  00061	48 89 44 24 30	 mov	 QWORD PTR elem$2[rsp], rax

; 55   : 		if ( elem == nullptr )

  00066	48 83 7c 24 30
	00		 cmp	 QWORD PTR elem$2[rsp], 0
  0006c	75 0b		 jne	 SHORT $LN7@OnKnightag

; 56   : 		{
; 57   : 			return nullptr;

  0006e	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR $T1[rsp], 0
  00077	eb 0a		 jmp	 SHORT $LN5@OnKnightag
$LN7@OnKnightag:

; 58   : 		}
; 59   : 
; 60   : 		assert( dynamic_cast<ActionType*>( elem ) != nullptr );
; 61   : 		return static_cast<ActionType*>( elem );

  00079	48 8b 44 24 30	 mov	 rax, QWORD PTR elem$2[rsp]
  0007e	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
$LN5@OnKnightag:
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextCheckIn.cpp

; 26   : 		PlayerKnightageAction* knightageAction = GetEntityAction(player);

  00083	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00088	48 89 44 24 50	 mov	 QWORD PTR knightageAction$[rsp], rax

; 27   : 		knightageAction->OnLogin();

  0008d	48 8b 4c 24 50	 mov	 rcx, QWORD PTR knightageAction$[rsp]
  00092	e8 00 00 00 00	 call	 ?OnLogin@PlayerKnightageAction@mu2@@QEAAXXZ ; mu2::PlayerKnightageAction::OnLogin
  00097	90		 npad	 1

; 28   : 	}	

  00098	48 83 c4 68	 add	 rsp, 104		; 00000068H
  0009c	c3		 ret	 0
?OnKnightageGameReady@JoinContextCheckIn@mu2@@UEBAXAEAPEAVEntityPlayer@2@@Z ENDP ; mu2::JoinContextCheckIn::OnKnightageGameReady
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextCheckIn.cpp
;	COMDAT ?ReqJoinExecutionPrev@JoinContextCheckIn@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@@Z
_TEXT	SEGMENT
hConsole$1 = 96
$T2 = 104
this$ = 144
player$ = 152
dest$ = 160
?ReqJoinExecutionPrev@JoinContextCheckIn@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@@Z PROC ; mu2::JoinContextCheckIn::ReqJoinExecutionPrev, COMDAT

; 18   : 	{

$LN4:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H

; 19   : 		UNREFERENCED_PARAMETER(dest);
; 20   : 		VERIFY_RETURN(player, );		

  00016	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR player$[rsp]
  0001e	48 83 38 00	 cmp	 QWORD PTR [rax], 0
  00022	0f 85 9b 00 00
	00		 jne	 $LN2@ReqJoinExe
  00028	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  0002d	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  00033	48 89 44 24 60	 mov	 QWORD PTR hConsole$1[rsp], rax
  00038	66 ba 0d 00	 mov	 dx, 13
  0003c	48 8b 4c 24 60	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  00041	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  00047	c7 44 24 50 14
	00 00 00	 mov	 DWORD PTR [rsp+80], 20
  0004f	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FF@MEOPPEOA@F?3?2Release_Branch?2Server?2Develo@
  00056	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  0005b	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_06BALNJMNP@player@
  00062	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  00067	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CO@HINPDLBI@mu2?3?3JoinContextCheckIn?3?3ReqJoi@
  0006e	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  00073	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  0007a	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  0007f	c7 44 24 28 14
	00 00 00	 mov	 DWORD PTR [rsp+40], 20
  00087	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FF@MEOPPEOA@F?3?2Release_Branch?2Server?2Develo@
  0008e	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  00093	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CO@HINPDLBI@mu2?3?3JoinContextCheckIn?3?3ReqJoi@
  0009a	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  000a0	ba 02 00 00 00	 mov	 edx, 2
  000a5	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  000ac	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  000b1	66 ba 07 00	 mov	 dx, 7
  000b5	48 8b 4c 24 60	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  000ba	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000c0	90		 npad	 1
  000c1	eb 1e		 jmp	 SHORT $LN1@ReqJoinExe
$LN2@ReqJoinExe:

; 21   : 		player->ResetCharLoginTime(DateTime::GetPresentTime());

  000c3	48 8d 4c 24 68	 lea	 rcx, QWORD PTR $T2[rsp]
  000c8	e8 00 00 00 00	 call	 ?GetPresentTime@DateTime@mu2@@SA?BV12@XZ ; mu2::DateTime::GetPresentTime
  000cd	48 8b d0	 mov	 rdx, rax
  000d0	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR player$[rsp]
  000d8	48 8b 08	 mov	 rcx, QWORD PTR [rax]
  000db	e8 00 00 00 00	 call	 ?ResetCharLoginTime@EntityPlayer@mu2@@QEAAXAEBVDateTime@2@@Z ; mu2::EntityPlayer::ResetCharLoginTime
  000e0	90		 npad	 1
$LN1@ReqJoinExe:

; 22   : 	}

  000e1	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  000e8	c3		 ret	 0
?ReqJoinExecutionPrev@JoinContextCheckIn@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@@Z ENDP ; mu2::JoinContextCheckIn::ReqJoinExecutionPrev
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextCheckIn.cpp
;	COMDAT ?SendResJoinResult@JoinContextCheckIn@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z
_TEXT	SEGMENT
this$ = 48
player$ = 56
arrived$ = 64
eError$ = 72
?SendResJoinResult@JoinContextCheckIn@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z PROC ; mu2::JoinContextCheckIn::SendResJoinResult, COMDAT

; 13   : 	{	

$LN3:
  00000	44 89 4c 24 20	 mov	 DWORD PTR [rsp+32], r9d
  00005	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 14   : 		SendEwcResZoneJoin(player, arrived, eError);

  00018	44 8b 4c 24 48	 mov	 r9d, DWORD PTR eError$[rsp]
  0001d	4c 8b 44 24 40	 mov	 r8, QWORD PTR arrived$[rsp]
  00022	48 8b 54 24 38	 mov	 rdx, QWORD PTR player$[rsp]
  00027	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0002c	e8 00 00 00 00	 call	 ?SendEwcResZoneJoin@JoinContextBase@mu2@@IEBAXAEAPEAVEntityPlayer@2@AEBVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z ; mu2::JoinContextBase::SendEwcResZoneJoin
  00031	90		 npad	 1

; 15   : 	}

  00032	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00036	c3		 ret	 0
?SendResJoinResult@JoinContextCheckIn@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z ENDP ; mu2::JoinContextCheckIn::SendResJoinResult
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextCheckIn.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextCheckIn.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
