; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	??0exception@std@@QEAA@AEBV01@@Z		; std::exception::exception
PUBLIC	?what@exception@std@@UEBAPEBDXZ			; std::exception::what
PUBLIC	??_Gexception@std@@UEAAPEAXI@Z			; std::exception::`scalar deleting destructor'
PUBLIC	??0bad_alloc@std@@QEAA@AEBV01@@Z		; std::bad_alloc::bad_alloc
PUBLIC	??_Gbad_alloc@std@@UEAAPEAXI@Z			; std::bad_alloc::`scalar deleting destructor'
PUBL<PERSON>	??0bad_array_new_length@std@@QEAA@XZ		; std::bad_array_new_length::bad_array_new_length
PUBLIC	??1bad_array_new_length@std@@UEAA@XZ		; std::bad_array_new_length::~bad_array_new_length
PUBLIC	??0bad_array_new_length@std@@QEAA@AEBV01@@Z	; std::bad_array_new_length::bad_array_new_length
PUBLIC	??_Gbad_array_new_length@std@@UEAAPEAXI@Z	; std::bad_array_new_length::`scalar deleting destructor'
PUBLIC	?_Throw_bad_array_new_length@std@@YAXXZ		; std::_Throw_bad_array_new_length
PUBLIC	?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
PUBLIC	??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
PUBLIC	??_G?$ISingleton@VEventScheduleManager@mu2@@@mu2@@UEAAPEAXI@Z ; mu2::ISingleton<mu2::EventScheduleManager>::`scalar deleting destructor'
PUBLIC	??0EventScheduleManager@mu2@@QEAA@XZ		; mu2::EventScheduleManager::EventScheduleManager
PUBLIC	??1EventScheduleManager@mu2@@UEAA@XZ		; mu2::EventScheduleManager::~EventScheduleManager
PUBLIC	?Init@EventScheduleManager@mu2@@UEAA_NPEAX@Z	; mu2::EventScheduleManager::Init
PUBLIC	?UnInit@EventScheduleManager@mu2@@UEAA_NXZ	; mu2::EventScheduleManager::UnInit
PUBLIC	?OnEvent@EventScheduleManager@mu2@@QEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ; mu2::EventScheduleManager::OnEvent
PUBLIC	?Update@EventScheduleManager@mu2@@QEAAXXZ	; mu2::EventScheduleManager::Update
PUBLIC	?DevOhrdorTreasureHuntTimeExtension@EventScheduleManager@mu2@@QEAAX_N@Z ; mu2::EventScheduleManager::DevOhrdorTreasureHuntTimeExtension
PUBLIC	?DevGetOhrdorTreasureHuntNextEventTime@EventScheduleManager@mu2@@QEAA_JXZ ; mu2::EventScheduleManager::DevGetOhrdorTreasureHuntNextEventTime
PUBLIC	?DevPvPFieldTimeExtensionNext@EventScheduleManager@mu2@@QEAAXXZ ; mu2::EventScheduleManager::DevPvPFieldTimeExtensionNext
PUBLIC	?allocate@?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@std@@QEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@2@_K@Z ; std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > >::allocate
PUBLIC	?_Calculate_growth@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEBA_K_K@Z ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Calculate_growth
PUBLIC	?_Change_array@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAXQEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@2@_K1@Z ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Change_array
PUBLIC	?_Tidy@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAXXZ ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Tidy
PUBLIC	?_Xlength@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@CAXXZ ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Xlength
PUBLIC	??_GEventScheduleManager@mu2@@UEAAPEAXI@Z	; mu2::EventScheduleManager::`scalar deleting destructor'
PUBLIC	??$make_unique@VOhrdorTreasureHunt@mu2@@$$V$0A@@std@@YA?AV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@0@XZ ; std::make_unique<mu2::OhrdorTreasureHunt,0>
PUBLIC	??1?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@QEAA@XZ ; std::unique_ptr<mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt> >::~unique_ptr<mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt> >
PUBLIC	??$GetScript@VPVPFieldScript@mu2@@@GlobalLoadScript@mu2@@QEBAPEBVPVPFieldScript@1@XZ ; mu2::GlobalLoadScript::GetScript<mu2::PVPFieldScript>
PUBLIC	??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBGUPVPFieldInfoElem@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ ; std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned short const ,mu2::PVPFieldInfoElem> > >,std::_Iterator_base0>::operator++
PUBLIC	??1?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@QEAA@XZ ; std::unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> >::~unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> >
PUBLIC	??$make_unique@VPVPFieldSchedule@mu2@@PEBUPVPFieldInfoElem@2@$0A@@std@@YA?AV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@0@$$QEAPEBUPVPFieldInfoElem@mu2@@@Z ; std::make_unique<mu2::PVPFieldSchedule,mu2::PVPFieldInfoElem const *,0>
PUBLIC	??$?0U?$default_delete@VPVPFieldSchedule@mu2@@@std@@$0A@@?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@QEAA@$$QEAV01@@Z ; std::unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> >::unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> ><std::default_delete<mu2::PVPFieldSchedule>,0>
PUBLIC	??1?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@QEAA@XZ ; std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >::~unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >
PUBLIC	??$?0U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@$0A@@?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@QEAA@$$QEAV01@@Z ; std::unique_ptr<mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt> >::unique_ptr<mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt> ><std::default_delete<mu2::OhrdorTreasureHunt>,0>
PUBLIC	??$_Emplace_one_at_back@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAAEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Emplace_one_at_back<std::unique_ptr<mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt> > >
PUBLIC	??$_Emplace_one_at_back@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAAEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Emplace_one_at_back<std::unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> > >
PUBLIC	??$_Emplace_back_with_unused_capacity@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAAEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Emplace_back_with_unused_capacity<std::unique_ptr<mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt> > >
PUBLIC	??$_Emplace_reallocate@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Emplace_reallocate<std::unique_ptr<mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt> > >
PUBLIC	??$_Emplace_back_with_unused_capacity@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAAEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Emplace_back_with_unused_capacity<std::unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> > >
PUBLIC	??$_Emplace_reallocate@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Emplace_reallocate<std::unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> > >
PUBLIC	??$?0VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@$0A@@?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@QEAA@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z ; std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> ><mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt>,0>
PUBLIC	??$?0U?$default_delete@VEventSchedule@mu2@@@std@@$0A@@?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@QEAA@$$QEAV01@@Z ; std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> ><std::default_delete<mu2::EventSchedule>,0>
PUBLIC	??1_Reallocation_guard@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@QEAA@XZ ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Reallocation_guard::~_Reallocation_guard
PUBLIC	??$_Uninitialized_move@PEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@YAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@0@@Z ; std::_Uninitialized_move<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > *,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >
PUBLIC	??$?0VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@$0A@@?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@QEAA@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z ; std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> ><mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule>,0>
PUBLIC	??1?$_Uninitialized_backout_al@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@std@@@std@@QEAA@XZ ; std::_Uninitialized_backout_al<std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::~_Uninitialized_backout_al<std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >
PUBLIC	??$_Emplace_back@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@?$_Uninitialized_backout_al@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@std@@@std@@QEAAX$$QEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@@Z ; std::_Uninitialized_backout_al<std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Emplace_back<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > >
PUBLIC	??_7exception@std@@6B@				; std::exception::`vftable'
PUBLIC	??_C@_0BC@EOODALEL@Unknown?5exception@		; `string'
PUBLIC	??_7bad_alloc@std@@6B@				; std::bad_alloc::`vftable'
PUBLIC	??_7bad_array_new_length@std@@6B@		; std::bad_array_new_length::`vftable'
PUBLIC	??_C@_0BF@KINCDENJ@bad?5array?5new?5length@	; `string'
PUBLIC	??_R0?AVexception@std@@@8			; std::exception `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
PUBLIC	_TI3?AVbad_array_new_length@std@@
PUBLIC	_CTA3?AVbad_array_new_length@std@@
PUBLIC	??_R0?AVbad_array_new_length@std@@@8		; std::bad_array_new_length `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
PUBLIC	??_R0?AVbad_alloc@std@@@8			; std::bad_alloc `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
PUBLIC	??_C@_09OLBBGJEO@Assertion@			; `string'
PUBLIC	?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A ; mu2::ISingleton<mu2::GlobalLoadScript>::inst
PUBLIC	??_C@_0DA@FCBIDNOM@F?3?2Release_Branch?2Shared?2Script@ ; `string'
PUBLIC	??_C@_0BA@FOIKENOD@vector?5too?5long@		; `string'
PUBLIC	??_R4exception@std@@6B@				; std::exception::`RTTI Complete Object Locator'
PUBLIC	??_R3exception@std@@8				; std::exception::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2exception@std@@8				; std::exception::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@exception@std@@8			; std::exception::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4bad_array_new_length@std@@6B@		; std::bad_array_new_length::`RTTI Complete Object Locator'
PUBLIC	??_R3bad_array_new_length@std@@8		; std::bad_array_new_length::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2bad_array_new_length@std@@8		; std::bad_array_new_length::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@bad_array_new_length@std@@8	; std::bad_array_new_length::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@bad_alloc@std@@8			; std::bad_alloc::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R3bad_alloc@std@@8				; std::bad_alloc::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2bad_alloc@std@@8				; std::bad_alloc::`RTTI Base Class Array'
PUBLIC	??_R4bad_alloc@std@@6B@				; std::bad_alloc::`RTTI Complete Object Locator'
PUBLIC	??_7?$ISingleton@VEventScheduleManager@mu2@@@mu2@@6B@ ; mu2::ISingleton<mu2::EventScheduleManager>::`vftable'
PUBLIC	??_7EventScheduleManager@mu2@@6B@		; mu2::EventScheduleManager::`vftable'
PUBLIC	??_R0?AVEventSchedule@mu2@@@8			; mu2::EventSchedule `RTTI Type Descriptor'
PUBLIC	??_R0?AVPVPFieldSchedule@mu2@@@8		; mu2::PVPFieldSchedule `RTTI Type Descriptor'
PUBLIC	??_R0?AVOhrdorTreasureHunt@mu2@@@8		; mu2::OhrdorTreasureHunt `RTTI Type Descriptor'
PUBLIC	??_C@_0DE@DKALFLNK@static_cast?$DMunsigned?$DO?$CI?5T?3?3Type?5@ ; `string'
PUBLIC	??_R4EventScheduleManager@mu2@@6B@		; mu2::EventScheduleManager::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVEventScheduleManager@mu2@@@8		; mu2::EventScheduleManager `RTTI Type Descriptor'
PUBLIC	??_R3EventScheduleManager@mu2@@8		; mu2::EventScheduleManager::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2EventScheduleManager@mu2@@8		; mu2::EventScheduleManager::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@EventScheduleManager@mu2@@8	; mu2::EventScheduleManager::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@?$ISingleton@VEventScheduleManager@mu2@@@mu2@@8 ; mu2::ISingleton<mu2::EventScheduleManager>::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R0?AV?$ISingleton@VEventScheduleManager@mu2@@@mu2@@@8 ; mu2::ISingleton<mu2::EventScheduleManager> `RTTI Type Descriptor'
PUBLIC	??_R3?$ISingleton@VEventScheduleManager@mu2@@@mu2@@8 ; mu2::ISingleton<mu2::EventScheduleManager>::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2?$ISingleton@VEventScheduleManager@mu2@@@mu2@@8 ; mu2::ISingleton<mu2::EventScheduleManager>::`RTTI Base Class Array'
PUBLIC	??_R4?$ISingleton@VEventScheduleManager@mu2@@@mu2@@6B@ ; mu2::ISingleton<mu2::EventScheduleManager>::`RTTI Complete Object Locator'
EXTRN	_purecall:PROC
EXTRN	??2@YAPEAX_K@Z:PROC				; operator new
EXTRN	??3@YAXPEAX_K@Z:PROC				; operator delete
EXTRN	atexit:PROC
EXTRN	__std_terminate:PROC
EXTRN	_invoke_watson:PROC
EXTRN	__std_exception_copy:PROC
EXTRN	__std_exception_destroy:PROC
EXTRN	??_Eexception@std@@UEAAPEAXI@Z:PROC		; std::exception::`vector deleting destructor'
EXTRN	??_Ebad_alloc@std@@UEAAPEAXI@Z:PROC		; std::bad_alloc::`vector deleting destructor'
EXTRN	??_Ebad_array_new_length@std@@UEAAPEAXI@Z:PROC	; std::bad_array_new_length::`vector deleting destructor'
EXTRN	?_Xlength_error@std@@YAXPEBD@Z:PROC		; std::_Xlength_error
EXTRN	?LogRuntimeAssertionFailure@mu2@@YAXPEBD00_K@Z:PROC ; mu2::LogRuntimeAssertionFailure
EXTRN	??1GlobalLoadScript@mu2@@UEAA@XZ:PROC		; mu2::GlobalLoadScript::~GlobalLoadScript
EXTRN	??0GlobalLoadScript@mu2@@AEAA@XZ:PROC		; mu2::GlobalLoadScript::GlobalLoadScript
EXTRN	?GetPVPFieldInfoElemMap@PVPFieldScript@mu2@@QEBAAEBV?$map@GUPVPFieldInfoElem@mu2@@U?$less@G@std@@V?$allocator@U?$pair@$$CBGUPVPFieldInfoElem@mu2@@@std@@@4@@std@@XZ:PROC ; mu2::PVPFieldScript::GetPVPFieldInfoElemMap
EXTRN	??_E?$ISingleton@VEventScheduleManager@mu2@@@mu2@@UEAAPEAXI@Z:PROC ; mu2::ISingleton<mu2::EventScheduleManager>::`vector deleting destructor'
EXTRN	??_EEventScheduleManager@mu2@@UEAAPEAXI@Z:PROC	; mu2::EventScheduleManager::`vector deleting destructor'
EXTRN	?InitializeEventSchedule@EventSchedule@mu2@@QEAAXXZ:PROC ; mu2::EventSchedule::InitializeEventSchedule
EXTRN	?Update@EventSchedule@mu2@@QEAAXXZ:PROC		; mu2::EventSchedule::Update
EXTRN	??0OhrdorTreasureHunt@mu2@@QEAA@XZ:PROC		; mu2::OhrdorTreasureHunt::OhrdorTreasureHunt
EXTRN	?DevTimeExtension@OhrdorTreasureHunt@mu2@@QEAAX_N@Z:PROC ; mu2::OhrdorTreasureHunt::DevTimeExtension
EXTRN	?DevGetNextTime@OhrdorTreasureHunt@mu2@@QEAA_JXZ:PROC ; mu2::OhrdorTreasureHunt::DevGetNextTime
EXTRN	??0PVPFieldSchedule@mu2@@QEAA@PEBUPVPFieldInfoElem@1@@Z:PROC ; mu2::PVPFieldSchedule::PVPFieldSchedule
EXTRN	?DevTimeExtensionNext@PVPFieldSchedule@mu2@@QEAAXXZ:PROC ; mu2::PVPFieldSchedule::DevTimeExtensionNext
EXTRN	_CxxThrowException:PROC
EXTRN	__CxxFrameHandler4:PROC
EXTRN	__RTDynamicCast:PROC
EXTRN	??_7type_info@@6B@:BYTE				; type_info::`vftable'
;	COMDAT ?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A
_BSS	SEGMENT
?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A DB 0d0H DUP (?) ; mu2::ISingleton<mu2::GlobalLoadScript>::inst
_BSS	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0exception@std@@QEAA@AEBV01@@Z DD imagerel $LN4
	DD	imagerel $LN4+89
	DD	imagerel $unwind$??0exception@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?what@exception@std@@UEBAPEBDXZ DD imagerel $LN5
	DD	imagerel $LN5+56
	DD	imagerel $unwind$?what@exception@std@@UEBAPEBDXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gexception@std@@UEAAPEAXI@Z DD imagerel $LN10
	DD	imagerel $LN10+83
	DD	imagerel $unwind$??_Gexception@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z DD imagerel $LN9
	DD	imagerel $LN9+104
	DD	imagerel $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+83
	DD	imagerel $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_array_new_length@std@@QEAA@XZ DD imagerel $LN14
	DD	imagerel $LN14+95
	DD	imagerel $unwind$??0bad_array_new_length@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1bad_array_new_length@std@@UEAA@XZ DD imagerel $LN14
	DD	imagerel $LN14+47
	DD	imagerel $unwind$??1bad_array_new_length@std@@UEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z DD imagerel $LN14
	DD	imagerel $LN14+54
	DD	imagerel $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z DD imagerel $LN20
	DD	imagerel $LN20+83
	DD	imagerel $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Throw_bad_array_new_length@std@@YAXXZ DD imagerel $LN3
	DD	imagerel $LN3+37
	DD	imagerel $unwind$?_Throw_bad_array_new_length@std@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z DD imagerel $LN5
	DD	imagerel $LN5+159
	DD	imagerel $unwind$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z DD imagerel $LN7
	DD	imagerel $LN7+150
	DD	imagerel $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_G?$ISingleton@VEventScheduleManager@mu2@@@mu2@@UEAAPEAXI@Z DD imagerel $LN10
	DD	imagerel $LN10+65
	DD	imagerel $unwind$??_G?$ISingleton@VEventScheduleManager@mu2@@@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0EventScheduleManager@mu2@@QEAA@XZ DD imagerel $LN32
	DD	imagerel $LN32+139
	DD	imagerel $unwind$??0EventScheduleManager@mu2@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1EventScheduleManager@mu2@@UEAA@XZ DD imagerel $LN89
	DD	imagerel $LN89+62
	DD	imagerel $unwind$??1EventScheduleManager@mu2@@UEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Init@EventScheduleManager@mu2@@UEAA_NPEAX@Z DD imagerel $LN384
	DD	imagerel $LN384+792
	DD	imagerel $unwind$?Init@EventScheduleManager@mu2@@UEAA_NPEAX@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0??Init@EventScheduleManager@mu2@@UEAA_NPEAX@Z@4HA DD imagerel ?dtor$0@?0??Init@EventScheduleManager@mu2@@UEAA_NPEAX@Z@4HA
	DD	imagerel ?dtor$0@?0??Init@EventScheduleManager@mu2@@UEAA_NPEAX@Z@4HA+27
	DD	imagerel $unwind$?dtor$0@?0??Init@EventScheduleManager@mu2@@UEAA_NPEAX@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$2@?0??Init@EventScheduleManager@mu2@@UEAA_NPEAX@Z@4HA DD imagerel ?dtor$2@?0??Init@EventScheduleManager@mu2@@UEAA_NPEAX@Z@4HA
	DD	imagerel ?dtor$2@?0??Init@EventScheduleManager@mu2@@UEAA_NPEAX@Z@4HA+24
	DD	imagerel $unwind$?dtor$2@?0??Init@EventScheduleManager@mu2@@UEAA_NPEAX@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?OnEvent@EventScheduleManager@mu2@@QEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z DD imagerel $LN31
	DD	imagerel $LN31+217
	DD	imagerel $unwind$?OnEvent@EventScheduleManager@mu2@@QEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Update@EventScheduleManager@mu2@@QEAAXXZ DD imagerel $LN31
	DD	imagerel $LN31+191
	DD	imagerel $unwind$?Update@EventScheduleManager@mu2@@QEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?DevOhrdorTreasureHuntTimeExtension@EventScheduleManager@mu2@@QEAAX_N@Z DD imagerel $LN32
	DD	imagerel $LN32+267
	DD	imagerel $unwind$?DevOhrdorTreasureHuntTimeExtension@EventScheduleManager@mu2@@QEAAX_N@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?DevGetOhrdorTreasureHuntNextEventTime@EventScheduleManager@mu2@@QEAA_JXZ DD imagerel $LN32
	DD	imagerel $LN32+255
	DD	imagerel $unwind$?DevGetOhrdorTreasureHuntNextEventTime@EventScheduleManager@mu2@@QEAA_JXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?DevPvPFieldTimeExtensionNext@EventScheduleManager@mu2@@QEAAXXZ DD imagerel $LN32
	DD	imagerel $LN32+252
	DD	imagerel $unwind$?DevPvPFieldTimeExtensionNext@EventScheduleManager@mu2@@QEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?allocate@?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@std@@QEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@2@_K@Z DD imagerel $LN13
	DD	imagerel $LN13+163
	DD	imagerel $unwind$?allocate@?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@std@@QEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@2@_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Calculate_growth@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEBA_K_K@Z DD imagerel $LN42
	DD	imagerel $LN42+332
	DD	imagerel $unwind$?_Calculate_growth@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEBA_K_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Change_array@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAXQEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@2@_K1@Z DD imagerel $LN74
	DD	imagerel $LN74+440
	DD	imagerel $unwind$?_Change_array@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAXQEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@2@_K1@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Tidy@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAXXZ DD imagerel $LN74
	DD	imagerel $LN74+389
	DD	imagerel $unwind$?_Tidy@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Xlength@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@CAXXZ DD imagerel $LN3
	DD	imagerel $LN3+22
	DD	imagerel $unwind$?_Xlength@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@CAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GEventScheduleManager@mu2@@UEAAPEAXI@Z DD imagerel $LN5
	DD	imagerel $LN5+60
	DD	imagerel $unwind$??_GEventScheduleManager@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$make_unique@VOhrdorTreasureHunt@mu2@@$$V$0A@@std@@YA?AV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@0@XZ DD imagerel $LN87
	DD	imagerel $LN87+195
	DD	imagerel $unwind$??$make_unique@VOhrdorTreasureHunt@mu2@@$$V$0A@@std@@YA?AV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@0@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???$make_unique@VOhrdorTreasureHunt@mu2@@$$V$0A@@std@@YA?AV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@0@XZ@4HA DD imagerel ?dtor$0@?0???$make_unique@VOhrdorTreasureHunt@mu2@@$$V$0A@@std@@YA?AV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@0@XZ@4HA
	DD	imagerel ?dtor$0@?0???$make_unique@VOhrdorTreasureHunt@mu2@@$$V$0A@@std@@YA?AV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@0@XZ@4HA+29
	DD	imagerel $unwind$?dtor$0@?0???$make_unique@VOhrdorTreasureHunt@mu2@@$$V$0A@@std@@YA?AV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@0@XZ@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@QEAA@XZ DD imagerel $LN17
	DD	imagerel $LN17+102
	DD	imagerel $unwind$??1?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$GetScript@VPVPFieldScript@mu2@@@GlobalLoadScript@mu2@@QEBAPEBVPVPFieldScript@1@XZ DD imagerel $LN14
	DD	imagerel $LN14+147
	DD	imagerel $unwind$??$GetScript@VPVPFieldScript@mu2@@@GlobalLoadScript@mu2@@QEBAPEBVPVPFieldScript@1@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBGUPVPFieldInfoElem@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ DD imagerel $LN15
	DD	imagerel $LN15+184
	DD	imagerel $unwind$??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBGUPVPFieldInfoElem@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@QEAA@XZ DD imagerel $LN17
	DD	imagerel $LN17+102
	DD	imagerel $unwind$??1?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$make_unique@VPVPFieldSchedule@mu2@@PEBUPVPFieldInfoElem@2@$0A@@std@@YA?AV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@0@$$QEAPEBUPVPFieldInfoElem@mu2@@@Z DD imagerel $LN92
	DD	imagerel $LN92+227
	DD	imagerel $unwind$??$make_unique@VPVPFieldSchedule@mu2@@PEBUPVPFieldInfoElem@2@$0A@@std@@YA?AV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@0@$$QEAPEBUPVPFieldInfoElem@mu2@@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???$make_unique@VPVPFieldSchedule@mu2@@PEBUPVPFieldInfoElem@2@$0A@@std@@YA?AV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@0@$$QEAPEBUPVPFieldInfoElem@mu2@@@Z@4HA DD imagerel ?dtor$0@?0???$make_unique@VPVPFieldSchedule@mu2@@PEBUPVPFieldInfoElem@2@$0A@@std@@YA?AV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@0@$$QEAPEBUPVPFieldInfoElem@mu2@@@Z@4HA
	DD	imagerel ?dtor$0@?0???$make_unique@VPVPFieldSchedule@mu2@@PEBUPVPFieldInfoElem@2@$0A@@std@@YA?AV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@0@$$QEAPEBUPVPFieldInfoElem@mu2@@@Z@4HA+29
	DD	imagerel $unwind$?dtor$0@?0???$make_unique@VPVPFieldSchedule@mu2@@PEBUPVPFieldInfoElem@2@$0A@@std@@YA?AV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@0@$$QEAPEBUPVPFieldInfoElem@mu2@@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$?0U?$default_delete@VPVPFieldSchedule@mu2@@@std@@$0A@@?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@QEAA@$$QEAV01@@Z DD imagerel $LN44
	DD	imagerel $LN44+162
	DD	imagerel $unwind$??$?0U?$default_delete@VPVPFieldSchedule@mu2@@@std@@$0A@@?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@QEAA@$$QEAV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@QEAA@XZ DD imagerel $LN17
	DD	imagerel $LN17+102
	DD	imagerel $unwind$??1?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$?0U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@$0A@@?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@QEAA@$$QEAV01@@Z DD imagerel $LN44
	DD	imagerel $LN44+162
	DD	imagerel $unwind$??$?0U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@$0A@@?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@QEAA@$$QEAV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Emplace_one_at_back@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAAEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z DD imagerel $LN662
	DD	imagerel $LN662+128
	DD	imagerel $unwind$??$_Emplace_one_at_back@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAAEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Emplace_one_at_back@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAAEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z DD imagerel $LN662
	DD	imagerel $LN662+128
	DD	imagerel $unwind$??$_Emplace_one_at_back@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAAEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Emplace_back_with_unused_capacity@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAAEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z DD imagerel $LN80
	DD	imagerel $LN80+169
	DD	imagerel $unwind$??$_Emplace_back_with_unused_capacity@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAAEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Emplace_reallocate@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z DD imagerel $LN681
	DD	imagerel $LN681+926
	DD	imagerel $unwind$??$_Emplace_reallocate@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???$_Emplace_reallocate@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z@4HA DD imagerel ?dtor$0@?0???$_Emplace_reallocate@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z@4HA
	DD	imagerel ?dtor$0@?0???$_Emplace_reallocate@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z@4HA+27
	DD	imagerel $unwind$?dtor$0@?0???$_Emplace_reallocate@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Emplace_back_with_unused_capacity@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAAEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z DD imagerel $LN80
	DD	imagerel $LN80+169
	DD	imagerel $unwind$??$_Emplace_back_with_unused_capacity@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAAEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Emplace_reallocate@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z DD imagerel $LN681
	DD	imagerel $LN681+926
	DD	imagerel $unwind$??$_Emplace_reallocate@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???$_Emplace_reallocate@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z@4HA DD imagerel ?dtor$0@?0???$_Emplace_reallocate@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z@4HA
	DD	imagerel ?dtor$0@?0???$_Emplace_reallocate@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z@4HA+27
	DD	imagerel $unwind$?dtor$0@?0???$_Emplace_reallocate@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__E?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ DD imagerel ??__E?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ
	DD	imagerel ??__E?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ+34
	DD	imagerel $unwind$??__E?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__F?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ DD imagerel ??__F?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ
	DD	imagerel ??__F?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ+22
	DD	imagerel $unwind$??__F?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$?0VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@$0A@@?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@QEAA@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z DD imagerel $LN49
	DD	imagerel $LN49+172
	DD	imagerel $unwind$??$?0VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@$0A@@?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@QEAA@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$?0U?$default_delete@VEventSchedule@mu2@@@std@@$0A@@?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@QEAA@$$QEAV01@@Z DD imagerel $LN44
	DD	imagerel $LN44+162
	DD	imagerel $unwind$??$?0U?$default_delete@VEventSchedule@mu2@@@std@@$0A@@?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@QEAA@$$QEAV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1_Reallocation_guard@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@QEAA@XZ DD imagerel $LN59
	DD	imagerel $LN59+285
	DD	imagerel $unwind$??1_Reallocation_guard@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Uninitialized_move@PEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@YAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@0@@Z DD imagerel $LN139
	DD	imagerel $LN139+218
	DD	imagerel $unwind$??$_Uninitialized_move@PEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@YAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@0@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???$_Uninitialized_move@PEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@YAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@0@@Z@4HA DD imagerel ?dtor$0@?0???$_Uninitialized_move@PEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@YAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@0@@Z@4HA
	DD	imagerel ?dtor$0@?0???$_Uninitialized_move@PEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@YAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@0@@Z@4HA+24
	DD	imagerel $unwind$?dtor$0@?0???$_Uninitialized_move@PEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@YAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@0@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$?0VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@$0A@@?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@QEAA@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z DD imagerel $LN49
	DD	imagerel $LN49+172
	DD	imagerel $unwind$??$?0VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@$0A@@?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@QEAA@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$_Uninitialized_backout_al@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@std@@@std@@QEAA@XZ DD imagerel $LN43
	DD	imagerel $LN43+140
	DD	imagerel $unwind$??1?$_Uninitialized_backout_al@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@std@@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Emplace_back@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@?$_Uninitialized_backout_al@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@std@@@std@@QEAAX$$QEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@@Z DD imagerel $LN70
	DD	imagerel $LN70+138
	DD	imagerel $unwind$??$_Emplace_back@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@?$_Uninitialized_backout_al@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@std@@@std@@QEAAX$$QEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@@Z
pdata	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
??inst$initializer$@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA DQ FLAT:??__E?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ ; ??inst$initializer$@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA
CRT$XCU	ENDS
;	COMDAT ??_R4?$ISingleton@VEventScheduleManager@mu2@@@mu2@@6B@
rdata$r	SEGMENT
??_R4?$ISingleton@VEventScheduleManager@mu2@@@mu2@@6B@ DD 01H ; mu2::ISingleton<mu2::EventScheduleManager>::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AV?$ISingleton@VEventScheduleManager@mu2@@@mu2@@@8
	DD	imagerel ??_R3?$ISingleton@VEventScheduleManager@mu2@@@mu2@@8
	DD	imagerel ??_R4?$ISingleton@VEventScheduleManager@mu2@@@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R2?$ISingleton@VEventScheduleManager@mu2@@@mu2@@8
rdata$r	SEGMENT
??_R2?$ISingleton@VEventScheduleManager@mu2@@@mu2@@8 DD imagerel ??_R1A@?0A@EA@?$ISingleton@VEventScheduleManager@mu2@@@mu2@@8 ; mu2::ISingleton<mu2::EventScheduleManager>::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3?$ISingleton@VEventScheduleManager@mu2@@@mu2@@8
rdata$r	SEGMENT
??_R3?$ISingleton@VEventScheduleManager@mu2@@@mu2@@8 DD 00H ; mu2::ISingleton<mu2::EventScheduleManager>::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2?$ISingleton@VEventScheduleManager@mu2@@@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AV?$ISingleton@VEventScheduleManager@mu2@@@mu2@@@8
data$rs	SEGMENT
??_R0?AV?$ISingleton@VEventScheduleManager@mu2@@@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::ISingleton<mu2::EventScheduleManager> `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AV?$ISingleton@VEventScheduleManager@mu2@@@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R1A@?0A@EA@?$ISingleton@VEventScheduleManager@mu2@@@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@?$ISingleton@VEventScheduleManager@mu2@@@mu2@@8 DD imagerel ??_R0?AV?$ISingleton@VEventScheduleManager@mu2@@@mu2@@@8 ; mu2::ISingleton<mu2::EventScheduleManager>::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3?$ISingleton@VEventScheduleManager@mu2@@@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@EventScheduleManager@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@EventScheduleManager@mu2@@8 DD imagerel ??_R0?AVEventScheduleManager@mu2@@@8 ; mu2::EventScheduleManager::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3EventScheduleManager@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2EventScheduleManager@mu2@@8
rdata$r	SEGMENT
??_R2EventScheduleManager@mu2@@8 DD imagerel ??_R1A@?0A@EA@EventScheduleManager@mu2@@8 ; mu2::EventScheduleManager::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@?$ISingleton@VEventScheduleManager@mu2@@@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3EventScheduleManager@mu2@@8
rdata$r	SEGMENT
??_R3EventScheduleManager@mu2@@8 DD 00H			; mu2::EventScheduleManager::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2EventScheduleManager@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVEventScheduleManager@mu2@@@8
data$rs	SEGMENT
??_R0?AVEventScheduleManager@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::EventScheduleManager `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVEventScheduleManager@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4EventScheduleManager@mu2@@6B@
rdata$r	SEGMENT
??_R4EventScheduleManager@mu2@@6B@ DD 01H		; mu2::EventScheduleManager::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVEventScheduleManager@mu2@@@8
	DD	imagerel ??_R3EventScheduleManager@mu2@@8
	DD	imagerel ??_R4EventScheduleManager@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_C@_0DE@DKALFLNK@static_cast?$DMunsigned?$DO?$CI?5T?3?3Type?5@
CONST	SEGMENT
??_C@_0DE@DKALFLNK@static_cast?$DMunsigned?$DO?$CI?5T?3?3Type?5@ DB 'stat'
	DB	'ic_cast<unsigned>( T::Type ) < m_scripts.size()', 00H ; `string'
CONST	ENDS
;	COMDAT ??_R0?AVOhrdorTreasureHunt@mu2@@@8
data$r	SEGMENT
??_R0?AVOhrdorTreasureHunt@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::OhrdorTreasureHunt `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVOhrdorTreasureHunt@mu2@@', 00H
data$r	ENDS
;	COMDAT ??_R0?AVPVPFieldSchedule@mu2@@@8
data$r	SEGMENT
??_R0?AVPVPFieldSchedule@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::PVPFieldSchedule `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVPVPFieldSchedule@mu2@@', 00H
data$r	ENDS
;	COMDAT ??_R0?AVEventSchedule@mu2@@@8
data$r	SEGMENT
??_R0?AVEventSchedule@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::EventSchedule `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVEventSchedule@mu2@@', 00H
data$r	ENDS
;	COMDAT ??_7EventScheduleManager@mu2@@6B@
CONST	SEGMENT
??_7EventScheduleManager@mu2@@6B@ DQ FLAT:??_R4EventScheduleManager@mu2@@6B@ ; mu2::EventScheduleManager::`vftable'
	DQ	FLAT:?Init@EventScheduleManager@mu2@@UEAA_NPEAX@Z
	DQ	FLAT:?UnInit@EventScheduleManager@mu2@@UEAA_NXZ
	DQ	FLAT:??_EEventScheduleManager@mu2@@UEAAPEAXI@Z
CONST	ENDS
;	COMDAT ??_7?$ISingleton@VEventScheduleManager@mu2@@@mu2@@6B@
CONST	SEGMENT
??_7?$ISingleton@VEventScheduleManager@mu2@@@mu2@@6B@ DQ FLAT:??_R4?$ISingleton@VEventScheduleManager@mu2@@@mu2@@6B@ ; mu2::ISingleton<mu2::EventScheduleManager>::`vftable'
	DQ	FLAT:_purecall
	DQ	FLAT:_purecall
	DQ	FLAT:??_E?$ISingleton@VEventScheduleManager@mu2@@@mu2@@UEAAPEAXI@Z
CONST	ENDS
;	COMDAT ??_R4bad_alloc@std@@6B@
rdata$r	SEGMENT
??_R4bad_alloc@std@@6B@ DD 01H				; std::bad_alloc::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVbad_alloc@std@@@8
	DD	imagerel ??_R3bad_alloc@std@@8
	DD	imagerel ??_R4bad_alloc@std@@6B@
rdata$r	ENDS
;	COMDAT ??_R2bad_alloc@std@@8
rdata$r	SEGMENT
??_R2bad_alloc@std@@8 DD imagerel ??_R1A@?0A@EA@bad_alloc@std@@8 ; std::bad_alloc::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@exception@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3bad_alloc@std@@8
rdata$r	SEGMENT
??_R3bad_alloc@std@@8 DD 00H				; std::bad_alloc::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2bad_alloc@std@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@bad_alloc@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@bad_alloc@std@@8 DD imagerel ??_R0?AVbad_alloc@std@@@8 ; std::bad_alloc::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3bad_alloc@std@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@bad_array_new_length@std@@8 DD imagerel ??_R0?AVbad_array_new_length@std@@@8 ; std::bad_array_new_length::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	02H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3bad_array_new_length@std@@8
rdata$r	ENDS
;	COMDAT ??_R2bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R2bad_array_new_length@std@@8 DD imagerel ??_R1A@?0A@EA@bad_array_new_length@std@@8 ; std::bad_array_new_length::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@bad_alloc@std@@8
	DD	imagerel ??_R1A@?0A@EA@exception@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R3bad_array_new_length@std@@8 DD 00H			; std::bad_array_new_length::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	03H
	DD	imagerel ??_R2bad_array_new_length@std@@8
rdata$r	ENDS
;	COMDAT ??_R4bad_array_new_length@std@@6B@
rdata$r	SEGMENT
??_R4bad_array_new_length@std@@6B@ DD 01H		; std::bad_array_new_length::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVbad_array_new_length@std@@@8
	DD	imagerel ??_R3bad_array_new_length@std@@8
	DD	imagerel ??_R4bad_array_new_length@std@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@exception@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@exception@std@@8 DD imagerel ??_R0?AVexception@std@@@8 ; std::exception::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3exception@std@@8
rdata$r	ENDS
;	COMDAT ??_R2exception@std@@8
rdata$r	SEGMENT
??_R2exception@std@@8 DD imagerel ??_R1A@?0A@EA@exception@std@@8 ; std::exception::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3exception@std@@8
rdata$r	SEGMENT
??_R3exception@std@@8 DD 00H				; std::exception::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2exception@std@@8
rdata$r	ENDS
;	COMDAT ??_R4exception@std@@6B@
rdata$r	SEGMENT
??_R4exception@std@@6B@ DD 01H				; std::exception::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVexception@std@@@8
	DD	imagerel ??_R3exception@std@@8
	DD	imagerel ??_R4exception@std@@6B@
rdata$r	ENDS
;	COMDAT ??_C@_0BA@FOIKENOD@vector?5too?5long@
CONST	SEGMENT
??_C@_0BA@FOIKENOD@vector?5too?5long@ DB 'vector too long', 00H ; `string'
CONST	ENDS
;	COMDAT ??_C@_0DA@FCBIDNOM@F?3?2Release_Branch?2Shared?2Script@
CONST	SEGMENT
??_C@_0DA@FCBIDNOM@F?3?2Release_Branch?2Shared?2Script@ DB 'F:\Release_Br'
	DB	'anch\Shared\Scripts\GlobalScript.h', 00H	; `string'
CONST	ENDS
;	COMDAT ??_C@_09OLBBGJEO@Assertion@
CONST	SEGMENT
??_C@_09OLBBGJEO@Assertion@ DB 'Assertion', 00H		; `string'
CONST	ENDS
;	COMDAT _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 DD 010H
	DD	imagerel ??_R0?AVbad_alloc@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0bad_alloc@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVbad_alloc@std@@@8
data$r	SEGMENT
??_R0?AVbad_alloc@std@@@8 DQ FLAT:??_7type_info@@6B@	; std::bad_alloc `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVbad_alloc@std@@', 00H
data$r	ENDS
;	COMDAT _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 DD 00H
	DD	imagerel ??_R0?AVbad_array_new_length@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0bad_array_new_length@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVbad_array_new_length@std@@@8
data$r	SEGMENT
??_R0?AVbad_array_new_length@std@@@8 DQ FLAT:??_7type_info@@6B@ ; std::bad_array_new_length `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVbad_array_new_length@std@@', 00H
data$r	ENDS
;	COMDAT _CTA3?AVbad_array_new_length@std@@
xdata$x	SEGMENT
_CTA3?AVbad_array_new_length@std@@ DD 03H
	DD	imagerel _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
	DD	imagerel _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
	DD	imagerel _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
xdata$x	ENDS
;	COMDAT _TI3?AVbad_array_new_length@std@@
xdata$x	SEGMENT
_TI3?AVbad_array_new_length@std@@ DD 00H
	DD	imagerel ??1bad_array_new_length@std@@UEAA@XZ
	DD	00H
	DD	imagerel _CTA3?AVbad_array_new_length@std@@
xdata$x	ENDS
;	COMDAT _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 DD 00H
	DD	imagerel ??_R0?AVexception@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0exception@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVexception@std@@@8
data$r	SEGMENT
??_R0?AVexception@std@@@8 DQ FLAT:??_7type_info@@6B@	; std::exception `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVexception@std@@', 00H
data$r	ENDS
;	COMDAT ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@
CONST	SEGMENT
??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ DB 'bad array new length', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7bad_array_new_length@std@@6B@
CONST	SEGMENT
??_7bad_array_new_length@std@@6B@ DQ FLAT:??_R4bad_array_new_length@std@@6B@ ; std::bad_array_new_length::`vftable'
	DQ	FLAT:??_Ebad_array_new_length@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT ??_7bad_alloc@std@@6B@
CONST	SEGMENT
??_7bad_alloc@std@@6B@ DQ FLAT:??_R4bad_alloc@std@@6B@	; std::bad_alloc::`vftable'
	DQ	FLAT:??_Ebad_alloc@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT ??_C@_0BC@EOODALEL@Unknown?5exception@
CONST	SEGMENT
??_C@_0BC@EOODALEL@Unknown?5exception@ DB 'Unknown exception', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7exception@std@@6B@
CONST	SEGMENT
??_7exception@std@@6B@ DQ FLAT:??_R4exception@std@@6B@	; std::exception::`vftable'
	DQ	FLAT:??_Eexception@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Emplace_back@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@?$_Uninitialized_backout_al@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@std@@@std@@QEAAX$$QEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@@Z DD 010e01H
	DD	0c20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$_Uninitialized_backout_al@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@std@@@std@@QEAA@XZ DD 010901H
	DD	0a209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$?0VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@$0A@@?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@QEAA@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z DD 010e01H
	DD	0c20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???$_Uninitialized_move@PEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@YAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@0@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$_Uninitialized_move@PEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@YAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@0@@Z DB 06H
	DB	00H
	DB	00H
	DB	0daH
	DB	02H
	DB	0b2H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$_Uninitialized_move@PEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@YAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@0@@Z DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???$_Uninitialized_move@PEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@YAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@0@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$_Uninitialized_move@PEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@YAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@0@@Z DB 028H
	DD	imagerel $stateUnwindMap$??$_Uninitialized_move@PEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@YAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@0@@Z
	DD	imagerel $ip2state$??$_Uninitialized_move@PEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@YAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@0@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Uninitialized_move@PEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@YAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@0@@Z DD 011811H
	DD	0e218H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$_Uninitialized_move@PEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@YAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@0@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??1_Reallocation_guard@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@QEAA@XZ DB 06H
	DB	00H
	DB	00H
	DB	0b5H, 03H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??1_Reallocation_guard@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@QEAA@XZ DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??1_Reallocation_guard@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@QEAA@XZ DB 068H
	DD	imagerel $stateUnwindMap$??1_Reallocation_guard@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@QEAA@XZ
	DD	imagerel $ip2state$??1_Reallocation_guard@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1_Reallocation_guard@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@QEAA@XZ DD 010919H
	DD	0e209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??1_Reallocation_guard@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$?0U?$default_delete@VEventSchedule@mu2@@@std@@$0A@@?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@QEAA@$$QEAV01@@Z DD 010e01H
	DD	0c20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$?0VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@$0A@@?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@QEAA@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z DD 010e01H
	DD	0c20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__F?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__E?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???$_Emplace_reallocate@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$_Emplace_reallocate@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z DB 06H
	DB	00H
	DB	00H
	DB	']', 08H
	DB	02H
	DB	0a9H, 05H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$_Emplace_reallocate@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???$_Emplace_reallocate@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$_Emplace_reallocate@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z DB 028H
	DD	imagerel $stateUnwindMap$??$_Emplace_reallocate@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z
	DD	imagerel $ip2state$??$_Emplace_reallocate@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Emplace_reallocate@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z DD 021611H
	DD	0290116H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$_Emplace_reallocate@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Emplace_back_with_unused_capacity@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAAEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z DD 010e01H
	DD	0e20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???$_Emplace_reallocate@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$_Emplace_reallocate@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z DB 06H
	DB	00H
	DB	00H
	DB	']', 08H
	DB	02H
	DB	0a9H, 05H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$_Emplace_reallocate@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???$_Emplace_reallocate@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$_Emplace_reallocate@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z DB 028H
	DD	imagerel $stateUnwindMap$??$_Emplace_reallocate@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z
	DD	imagerel $ip2state$??$_Emplace_reallocate@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Emplace_reallocate@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z DD 021611H
	DD	0290116H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$_Emplace_reallocate@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Emplace_back_with_unused_capacity@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAAEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z DD 010e01H
	DD	0e20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Emplace_one_at_back@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAAEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z DD 010e01H
	DD	0820eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Emplace_one_at_back@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAAEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z DD 010e01H
	DD	0820eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$?0U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@$0A@@?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@QEAA@$$QEAV01@@Z DD 010e01H
	DD	0c20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@QEAA@XZ DD 010901H
	DD	08209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$?0U?$default_delete@VPVPFieldSchedule@mu2@@@std@@$0A@@?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@QEAA@$$QEAV01@@Z DD 010e01H
	DD	0c20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???$make_unique@VPVPFieldSchedule@mu2@@PEBUPVPFieldInfoElem@2@$0A@@std@@YA?AV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@0@$$QEAPEBUPVPFieldInfoElem@mu2@@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$make_unique@VPVPFieldSchedule@mu2@@PEBUPVPFieldInfoElem@2@$0A@@std@@YA?AV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@0@$$QEAPEBUPVPFieldInfoElem@mu2@@@Z DB 06H
	DB	00H
	DB	00H
	DB	'P'
	DB	02H
	DB	082H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$make_unique@VPVPFieldSchedule@mu2@@PEBUPVPFieldInfoElem@2@$0A@@std@@YA?AV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@0@$$QEAPEBUPVPFieldInfoElem@mu2@@@Z DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???$make_unique@VPVPFieldSchedule@mu2@@PEBUPVPFieldInfoElem@2@$0A@@std@@YA?AV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@0@$$QEAPEBUPVPFieldInfoElem@mu2@@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$make_unique@VPVPFieldSchedule@mu2@@PEBUPVPFieldInfoElem@2@$0A@@std@@YA?AV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@0@$$QEAPEBUPVPFieldInfoElem@mu2@@@Z DB 028H
	DD	imagerel $stateUnwindMap$??$make_unique@VPVPFieldSchedule@mu2@@PEBUPVPFieldInfoElem@2@$0A@@std@@YA?AV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@0@$$QEAPEBUPVPFieldInfoElem@mu2@@@Z
	DD	imagerel $ip2state$??$make_unique@VPVPFieldSchedule@mu2@@PEBUPVPFieldInfoElem@2@$0A@@std@@YA?AV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@0@$$QEAPEBUPVPFieldInfoElem@mu2@@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$make_unique@VPVPFieldSchedule@mu2@@PEBUPVPFieldInfoElem@2@$0A@@std@@YA?AV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@0@$$QEAPEBUPVPFieldInfoElem@mu2@@@Z DD 021111H
	DD	0110111H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$make_unique@VPVPFieldSchedule@mu2@@PEBUPVPFieldInfoElem@2@$0A@@std@@YA?AV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@0@$$QEAPEBUPVPFieldInfoElem@mu2@@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@QEAA@XZ DD 010901H
	DD	08209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBGUPVPFieldInfoElem@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$GetScript@VPVPFieldScript@mu2@@@GlobalLoadScript@mu2@@QEBAPEBVPVPFieldScript@1@XZ DD 010901H
	DD	08209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@QEAA@XZ DD 010901H
	DD	08209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???$make_unique@VOhrdorTreasureHunt@mu2@@$$V$0A@@std@@YA?AV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@0@XZ@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$make_unique@VOhrdorTreasureHunt@mu2@@$$V$0A@@std@@YA?AV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@0@XZ DB 06H
	DB	00H
	DB	00H
	DB	'@'
	DB	02H
	DB	'X'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$make_unique@VOhrdorTreasureHunt@mu2@@$$V$0A@@std@@YA?AV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@0@XZ DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???$make_unique@VOhrdorTreasureHunt@mu2@@$$V$0A@@std@@YA?AV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@0@XZ@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$make_unique@VOhrdorTreasureHunt@mu2@@$$V$0A@@std@@YA?AV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@0@XZ DB 028H
	DD	imagerel $stateUnwindMap$??$make_unique@VOhrdorTreasureHunt@mu2@@$$V$0A@@std@@YA?AV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@0@XZ
	DD	imagerel $ip2state$??$make_unique@VOhrdorTreasureHunt@mu2@@$$V$0A@@std@@YA?AV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@0@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$make_unique@VOhrdorTreasureHunt@mu2@@$$V$0A@@std@@YA?AV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@0@XZ DD 010911H
	DD	0e209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$make_unique@VOhrdorTreasureHunt@mu2@@$$V$0A@@std@@YA?AV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@0@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GEventScheduleManager@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Xlength@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@CAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?_Tidy@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAXXZ DB 06H
	DB	00H
	DB	00H
	DB	0b9H, 04H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?_Tidy@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAXXZ DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?_Tidy@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAXXZ DB 068H
	DD	imagerel $stateUnwindMap$?_Tidy@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAXXZ
	DD	imagerel $ip2state$?_Tidy@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Tidy@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAXXZ DD 020c19H
	DD	015010cH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?_Tidy@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?_Change_array@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAXQEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@2@_K1@Z DB 06H
	DB	00H
	DB	00H
	DB	0f5H, 04H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?_Change_array@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAXQEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@2@_K1@Z DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?_Change_array@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAXQEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@2@_K1@Z DB 068H
	DD	imagerel $stateUnwindMap$?_Change_array@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAXQEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@2@_K1@Z
	DD	imagerel $ip2state$?_Change_array@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAXQEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@2@_K1@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Change_array@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAXQEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@2@_K1@Z DD 021b19H
	DD	015011bH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?_Change_array@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAXQEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@2@_K1@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Calculate_growth@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEBA_K_K@Z DD 021101H
	DD	0110111H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?allocate@?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@std@@QEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@2@_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?DevPvPFieldTimeExtensionNext@EventScheduleManager@mu2@@QEAAXXZ DD 020c01H
	DD	013010cH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?DevGetOhrdorTreasureHuntNextEventTime@EventScheduleManager@mu2@@QEAA_JXZ DD 020c01H
	DD	013010cH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?DevOhrdorTreasureHuntTimeExtension@EventScheduleManager@mu2@@QEAAX_N@Z DD 021001H
	DD	0130110H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Update@EventScheduleManager@mu2@@QEAAXXZ DD 020c01H
	DD	011010cH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?OnEvent@EventScheduleManager@mu2@@QEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z DD 021101H
	DD	0110111H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$2@?0??Init@EventScheduleManager@mu2@@UEAA_NPEAX@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0??Init@EventScheduleManager@mu2@@UEAA_NPEAX@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?Init@EventScheduleManager@mu2@@UEAA_NPEAX@Z DB 0aH
	DB	00H
	DB	00H
	DB	'l'
	DB	02H
	DB	'V'
	DB	00H
	DB	0b1H, 05H
	DB	04H
	DB	09eH
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?Init@EventScheduleManager@mu2@@UEAA_NPEAX@Z DB 04H
	DB	0eH
	DD	imagerel ?dtor$0@?0??Init@EventScheduleManager@mu2@@UEAA_NPEAX@Z@4HA
	DB	036H
	DD	imagerel ?dtor$2@?0??Init@EventScheduleManager@mu2@@UEAA_NPEAX@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?Init@EventScheduleManager@mu2@@UEAA_NPEAX@Z DB 028H
	DD	imagerel $stateUnwindMap$?Init@EventScheduleManager@mu2@@UEAA_NPEAX@Z
	DD	imagerel $ip2state$?Init@EventScheduleManager@mu2@@UEAA_NPEAX@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Init@EventScheduleManager@mu2@@UEAA_NPEAX@Z DD 031211H
	DD	02a0112H
	DD	0700bH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?Init@EventScheduleManager@mu2@@UEAA_NPEAX@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1EventScheduleManager@mu2@@UEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0EventScheduleManager@mu2@@QEAA@XZ DD 020a01H
	DD	07006320aH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_G?$ISingleton@VEventScheduleManager@mu2@@@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z DD 010901H
	DD	0a209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Throw_bad_array_new_length@std@@YAXXZ DD 010401H
	DD	08204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1bad_array_new_length@std@@UEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_array_new_length@std@@QEAA@XZ DD 010601H
	DD	07006H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z DD 020f01H
	DD	0700b320fH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gexception@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?what@exception@std@@UEBAPEBDXZ DD 010901H
	DD	02209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0exception@std@@QEAA@AEBV01@@Z DD 020f01H
	DD	0700b320fH
xdata	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??$_Emplace_back@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@?$_Uninitialized_backout_al@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@std@@@std@@QEAAX$$QEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@@Z
_TEXT	SEGMENT
_Ptr$ = 32
$T1 = 40
$T2 = 48
$T3 = 56
$T4 = 64
$T5 = 72
__formal$ = 80
this$ = 112
<_Vals_0>$ = 120
??$_Emplace_back@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@?$_Uninitialized_backout_al@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@std@@@std@@QEAAX$$QEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@@Z PROC ; std::_Uninitialized_backout_al<std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Emplace_back<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > >, COMDAT

; 1843 :     _CONSTEXPR20 void _Emplace_back(_Types&&... _Vals) { // construct a new element at *_Last and increment

$LN70:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 68	 sub	 rsp, 104		; 00000068H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  0000e	48 8b 44 24 78	 mov	 rax, QWORD PTR <_Vals_0>$[rsp]
  00013	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1844 :         allocator_traits<_Alloc>::construct(_Al, _STD _Unfancy(_Last), _STD forward<_Types>(_Vals)...);

  00018	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00021	48 89 44 24 20	 mov	 QWORD PTR _Ptr$[rsp], rax

; 69   :     return _Ptr;

  00026	48 8b 44 24 20	 mov	 rax, QWORD PTR _Ptr$[rsp]
  0002b	48 89 44 24 28	 mov	 QWORD PTR $T1[rsp], rax

; 1844 :         allocator_traits<_Alloc>::construct(_Al, _STD _Unfancy(_Last), _STD forward<_Types>(_Vals)...);

  00030	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  00035	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00039	48 89 44 24 50	 mov	 QWORD PTR __formal$[rsp], rax
  0003e	48 8b 44 24 28	 mov	 rax, QWORD PTR $T1[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  00043	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 732  :         ::new (const_cast<void*>(static_cast<const volatile void*>(_Ptr))) _Objty(_STD forward<_Types>(_Args)...);

  00048	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
  0004d	48 89 44 24 48	 mov	 QWORD PTR $T5[rsp], rax

; 1844 :         allocator_traits<_Alloc>::construct(_Al, _STD _Unfancy(_Last), _STD forward<_Types>(_Vals)...);

  00052	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00057	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 732  :         ::new (const_cast<void*>(static_cast<const volatile void*>(_Ptr))) _Objty(_STD forward<_Types>(_Args)...);

  0005c	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  00061	48 8b d0	 mov	 rdx, rax
  00064	48 8b 4c 24 48	 mov	 rcx, QWORD PTR $T5[rsp]
  00069	e8 00 00 00 00	 call	 ??$?0U?$default_delete@VEventSchedule@mu2@@@std@@$0A@@?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@QEAA@$$QEAV01@@Z ; std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> ><std::default_delete<mu2::EventSchedule>,0>
  0006e	90		 npad	 1

; 1845 :         ++_Last;

  0006f	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  00074	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00078	48 83 c0 08	 add	 rax, 8
  0007c	48 8b 4c 24 70	 mov	 rcx, QWORD PTR this$[rsp]
  00081	48 89 41 08	 mov	 QWORD PTR [rcx+8], rax

; 1846 :     }

  00085	48 83 c4 68	 add	 rsp, 104		; 00000068H
  00089	c3		 ret	 0
??$_Emplace_back@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@?$_Uninitialized_backout_al@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@std@@@std@@QEAAX$$QEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@@Z ENDP ; std::_Uninitialized_backout_al<std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Emplace_back<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??1?$_Uninitialized_backout_al@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@std@@@std@@QEAA@XZ
_TEXT	SEGMENT
_First$ = 32
_Ptr$ = 40
_Last$ = 48
$T1 = 56
_Al$ = 64
this$ = 96
??1?$_Uninitialized_backout_al@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@std@@@std@@QEAA@XZ PROC ; std::_Uninitialized_backout_al<std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::~_Uninitialized_backout_al<std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >, COMDAT

; 1838 :     _CONSTEXPR20 ~_Uninitialized_backout_al() {

$LN43:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 1839 :         _STD _Destroy_range(_First, _Last, _Al);

  00009	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00012	48 89 44 24 40	 mov	 QWORD PTR _Al$[rsp], rax
  00017	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  0001c	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00020	48 89 44 24 30	 mov	 QWORD PTR _Last$[rsp], rax
  00025	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  0002a	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0002d	48 89 44 24 20	 mov	 QWORD PTR _First$[rsp], rax

; 1102 :         for (; _First != _Last; ++_First) {

  00032	eb 0e		 jmp	 SHORT $LN7@Uninitiali
$LN5@Uninitiali:
  00034	48 8b 44 24 20	 mov	 rax, QWORD PTR _First$[rsp]
  00039	48 83 c0 08	 add	 rax, 8
  0003d	48 89 44 24 20	 mov	 QWORD PTR _First$[rsp], rax
$LN7@Uninitiali:
  00042	48 8b 44 24 30	 mov	 rax, QWORD PTR _Last$[rsp]
  00047	48 39 44 24 20	 cmp	 QWORD PTR _First$[rsp], rax
  0004c	74 39		 je	 SHORT $LN6@Uninitiali

; 69   :     return _Ptr;

  0004e	48 8b 44 24 20	 mov	 rax, QWORD PTR _First$[rsp]
  00053	48 89 44 24 38	 mov	 QWORD PTR $T1[rsp], rax

; 1103 :             allocator_traits<_Alloc>::destroy(_Al, _STD _Unfancy(_First));

  00058	48 8b 44 24 38	 mov	 rax, QWORD PTR $T1[rsp]
  0005d	48 89 44 24 28	 mov	 QWORD PTR _Ptr$[rsp], rax
  00062	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  00067	e8 00 00 00 00	 call	 ??1?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@QEAA@XZ ; std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >::~unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >
  0006c	33 c0		 xor	 eax, eax
  0006e	83 e0 01	 and	 eax, 1
  00071	85 c0		 test	 eax, eax
  00073	74 10		 je	 SHORT $LN20@Uninitiali
  00075	ba 08 00 00 00	 mov	 edx, 8
  0007a	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  0007f	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00084	90		 npad	 1
$LN20@Uninitiali:

; 1104 :         }

  00085	eb ad		 jmp	 SHORT $LN5@Uninitiali
$LN6@Uninitiali:

; 1840 :     }

  00087	48 83 c4 58	 add	 rsp, 88			; 00000058H
  0008b	c3		 ret	 0
??1?$_Uninitialized_backout_al@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@std@@@std@@QEAA@XZ ENDP ; std::_Uninitialized_backout_al<std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::~_Uninitialized_backout_al<std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ??$?0VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@$0A@@?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@QEAA@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z
_TEXT	SEGMENT
_Val$ = 0
$T1 = 8
_Old_val$2 = 16
$T3 = 24
$T4 = 32
$T5 = 40
$T6 = 48
$T7 = 56
$T8 = 64
this$ = 72
$T9 = 80
$T10 = 88
this$ = 112
_Right$ = 120
??$?0VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@$0A@@?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@QEAA@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z PROC ; std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> ><mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule>,0>, COMDAT

; 3394 :         : _Mypair(_One_then_variadic_args_t{}, _STD forward<_Dx2>(_Right.get_deleter()), _Right.release()) {}

$LN49:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 68	 sub	 rsp, 104		; 00000068H

; 3465 :         return _STD exchange(_Mypair._Myval2, nullptr);

  0000e	48 c7 44 24 08
	00 00 00 00	 mov	 QWORD PTR $T1[rsp], 0
  00017	48 8b 44 24 78	 mov	 rax, QWORD PTR _Right$[rsp]
  0001c	48 89 04 24	 mov	 QWORD PTR _Val$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 773  :     _Ty _Old_val = static_cast<_Ty&&>(_Val);

  00020	48 8b 04 24	 mov	 rax, QWORD PTR _Val$[rsp]
  00024	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00027	48 89 44 24 10	 mov	 QWORD PTR _Old_val$2[rsp], rax

; 774  :     _Val         = static_cast<_Other&&>(_New_val);

  0002c	48 8b 04 24	 mov	 rax, QWORD PTR _Val$[rsp]
  00030	48 8b 4c 24 08	 mov	 rcx, QWORD PTR $T1[rsp]
  00035	48 89 08	 mov	 QWORD PTR [rax], rcx

; 775  :     return _Old_val;

  00038	48 8b 44 24 10	 mov	 rax, QWORD PTR _Old_val$2[rsp]
  0003d	48 89 44 24 18	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3465 :         return _STD exchange(_Mypair._Myval2, nullptr);

  00042	48 8b 44 24 18	 mov	 rax, QWORD PTR $T3[rsp]
  00047	48 89 44 24 20	 mov	 QWORD PTR $T4[rsp], rax

; 3394 :         : _Mypair(_One_then_variadic_args_t{}, _STD forward<_Dx2>(_Right.get_deleter()), _Right.release()) {}

  0004c	48 8b 44 24 20	 mov	 rax, QWORD PTR $T4[rsp]
  00051	48 89 44 24 40	 mov	 QWORD PTR $T8[rsp], rax

; 3441 :         return _Mypair._Get_first();

  00056	48 8b 44 24 78	 mov	 rax, QWORD PTR _Right$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  0005b	48 89 44 24 28	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3441 :         return _Mypair._Get_first();

  00060	48 8b 44 24 28	 mov	 rax, QWORD PTR $T5[rsp]
  00065	48 89 44 24 30	 mov	 QWORD PTR $T6[rsp], rax

; 3394 :         : _Mypair(_One_then_variadic_args_t{}, _STD forward<_Dx2>(_Right.get_deleter()), _Right.release()) {}

  0006a	48 8b 44 24 30	 mov	 rax, QWORD PTR $T6[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  0006f	48 89 44 24 38	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3394 :         : _Mypair(_One_then_variadic_args_t{}, _STD forward<_Dx2>(_Right.get_deleter()), _Right.release()) {}

  00074	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  00079	48 89 44 24 48	 mov	 QWORD PTR this$[rsp], rax
  0007e	48 8b 44 24 38	 mov	 rax, QWORD PTR $T7[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00083	48 89 44 24 58	 mov	 QWORD PTR $T10[rsp], rax
  00088	48 8d 44 24 40	 lea	 rax, QWORD PTR $T8[rsp]
  0008d	48 89 44 24 50	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1536 :         : _Ty1(_STD forward<_Other1>(_Val1)), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  00092	48 8b 44 24 48	 mov	 rax, QWORD PTR this$[rsp]
  00097	48 8b 4c 24 50	 mov	 rcx, QWORD PTR $T9[rsp]
  0009c	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0009f	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3394 :         : _Mypair(_One_then_variadic_args_t{}, _STD forward<_Dx2>(_Right.get_deleter()), _Right.release()) {}

  000a2	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  000a7	48 83 c4 68	 add	 rsp, 104		; 00000068H
  000ab	c3		 ret	 0
??$?0VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@$0A@@?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@QEAA@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z ENDP ; std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> ><mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule>,0>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??$_Uninitialized_move@PEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@YAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@0@@Z
_TEXT	SEGMENT
_UFirst$ = 32
_Backout$ = 40
$T1 = 64
$T2 = 72
_ULast$ = 80
$T3 = 88
$T4 = 96
$T5 = 104
_First$ = 128
_Last$ = 136
_Dest$ = 144
_Al$ = 152
??$_Uninitialized_move@PEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@YAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@0@@Z PROC ; std::_Uninitialized_move<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > *,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >, COMDAT

; 1977 :     const _InIt _First, const _InIt _Last, _Alloc_ptr_t<_Alloc> _Dest, _Alloc& _Al) {

$LN139:
  00000	4c 89 4c 24 20	 mov	 QWORD PTR [rsp+32], r9
  00005	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	48 83 ec 78	 sub	 rsp, 120		; 00000078H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 1382 :         return _It + 0;

  00018	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _First$[rsp]
  00020	48 89 44 24 40	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1984 :     auto _UFirst      = _STD _Get_unwrapped(_First);

  00025	48 8b 44 24 40	 mov	 rax, QWORD PTR $T1[rsp]
  0002a	48 89 44 24 20	 mov	 QWORD PTR _UFirst$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 1382 :         return _It + 0;

  0002f	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR _Last$[rsp]
  00037	48 89 44 24 48	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1985 :     const auto _ULast = _STD _Get_unwrapped(_Last);

  0003c	48 8b 44 24 48	 mov	 rax, QWORD PTR $T2[rsp]
  00041	48 89 44 24 50	 mov	 QWORD PTR _ULast$[rsp], rax

; 1833 :     _CONSTEXPR20 _Uninitialized_backout_al(pointer _Dest, _Alloc& _Al_) : _First(_Dest), _Last(_Dest), _Al(_Al_) {}

  00046	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR _Dest$[rsp]
  0004e	48 89 44 24 28	 mov	 QWORD PTR _Backout$[rsp], rax
  00053	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR _Dest$[rsp]
  0005b	48 89 44 24 30	 mov	 QWORD PTR _Backout$[rsp+8], rax
  00060	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR _Al$[rsp]
  00068	48 89 44 24 38	 mov	 QWORD PTR _Backout$[rsp+16], rax

; 1986 :     if constexpr (conjunction_v<bool_constant<_Iter_move_cat<decltype(_UFirst), _Ptrval>::_Bitcopy_constructible>,
; 1987 :                       _Uses_default_construct<_Alloc, _Ptrval, decltype(_STD move(*_UFirst))>>) {
; 1988 : #if _HAS_CXX20
; 1989 :         if (!_STD is_constant_evaluated())
; 1990 : #endif // _HAS_CXX20
; 1991 :         {
; 1992 :             _STD _Copy_memmove(_UFirst, _ULast, _STD _Unfancy(_Dest));
; 1993 :             return _Dest + (_ULast - _UFirst);
; 1994 :         }
; 1995 :     }
; 1996 : 
; 1997 :     _Uninitialized_backout_al<_Alloc> _Backout{_Dest, _Al};
; 1998 :     for (; _UFirst != _ULast; ++_UFirst) {

  0006d	eb 0e		 jmp	 SHORT $LN4@Uninitiali
$LN2@Uninitiali:
  0006f	48 8b 44 24 20	 mov	 rax, QWORD PTR _UFirst$[rsp]
  00074	48 83 c0 08	 add	 rax, 8
  00078	48 89 44 24 20	 mov	 QWORD PTR _UFirst$[rsp], rax
$LN4@Uninitiali:
  0007d	48 8b 44 24 50	 mov	 rax, QWORD PTR _ULast$[rsp]
  00082	48 39 44 24 20	 cmp	 QWORD PTR _UFirst$[rsp], rax
  00087	74 1f		 je	 SHORT $LN3@Uninitiali
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00089	48 8b 44 24 20	 mov	 rax, QWORD PTR _UFirst$[rsp]
  0008e	48 89 44 24 58	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1999 :         _Backout._Emplace_back(_STD move(*_UFirst));

  00093	48 8b 44 24 58	 mov	 rax, QWORD PTR $T3[rsp]
  00098	48 8b d0	 mov	 rdx, rax
  0009b	48 8d 4c 24 28	 lea	 rcx, QWORD PTR _Backout$[rsp]
  000a0	e8 00 00 00 00	 call	 ??$_Emplace_back@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@?$_Uninitialized_backout_al@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@std@@@std@@QEAAX$$QEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@@Z ; std::_Uninitialized_backout_al<std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Emplace_back<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > >
  000a5	90		 npad	 1

; 2000 :     }

  000a6	eb c7		 jmp	 SHORT $LN2@Uninitiali
$LN3@Uninitiali:

; 1849 :         _First = _Last;

  000a8	48 8b 44 24 30	 mov	 rax, QWORD PTR _Backout$[rsp+8]
  000ad	48 89 44 24 28	 mov	 QWORD PTR _Backout$[rsp], rax

; 1850 :         return _Last;

  000b2	48 8b 44 24 30	 mov	 rax, QWORD PTR _Backout$[rsp+8]
  000b7	48 89 44 24 60	 mov	 QWORD PTR $T4[rsp], rax

; 2001 : 
; 2002 :     return _Backout._Release();

  000bc	48 8b 44 24 60	 mov	 rax, QWORD PTR $T4[rsp]
  000c1	48 89 44 24 68	 mov	 QWORD PTR $T5[rsp], rax
  000c6	48 8d 4c 24 28	 lea	 rcx, QWORD PTR _Backout$[rsp]
  000cb	e8 00 00 00 00	 call	 ??1?$_Uninitialized_backout_al@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@std@@@std@@QEAA@XZ ; std::_Uninitialized_backout_al<std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::~_Uninitialized_backout_al<std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >
  000d0	48 8b 44 24 68	 mov	 rax, QWORD PTR $T5[rsp]

; 2003 : }

  000d5	48 83 c4 78	 add	 rsp, 120		; 00000078H
  000d9	c3		 ret	 0
??$_Uninitialized_move@PEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@YAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@0@@Z ENDP ; std::_Uninitialized_move<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > *,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
_UFirst$ = 32
_Backout$ = 40
$T1 = 64
$T2 = 72
_ULast$ = 80
$T3 = 88
$T4 = 96
$T5 = 104
_First$ = 128
_Last$ = 136
_Dest$ = 144
_Al$ = 152
?dtor$0@?0???$_Uninitialized_move@PEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@YAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@0@@Z@4HA PROC ; `std::_Uninitialized_move<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > *,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8d 4d 28	 lea	 rcx, QWORD PTR _Backout$[rbp]
  0000d	e8 00 00 00 00	 call	 ??1?$_Uninitialized_backout_al@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@std@@@std@@QEAA@XZ ; std::_Uninitialized_backout_al<std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::~_Uninitialized_backout_al<std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$0@?0???$_Uninitialized_move@PEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@YAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@0@@Z@4HA ENDP ; `std::_Uninitialized_move<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > *,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ??1_Reallocation_guard@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@QEAA@XZ
_TEXT	SEGMENT
_First$ = 32
_Bytes$ = 40
_Ptr$ = 48
_Ptr$ = 56
_Last$ = 64
$T1 = 72
_Count$ = 80
_Ptr$ = 88
_Al$ = 96
this$ = 104
this$ = 128
??1_Reallocation_guard@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@QEAA@XZ PROC ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Reallocation_guard::~_Reallocation_guard, COMDAT

; 620  :         _CONSTEXPR20 ~_Reallocation_guard() noexcept {

$LN59:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 78	 sub	 rsp, 120		; 00000078H

; 621  :             if (_New_begin != nullptr) {

  00009	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00011	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00016	0f 84 fc 00 00
	00		 je	 $LN2@Reallocati

; 622  :                 _STD _Destroy_range(_Constructed_first, _Constructed_last, _Al);

  0001c	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00024	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00027	48 89 44 24 60	 mov	 QWORD PTR _Al$[rsp], rax
  0002c	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00034	48 8b 40 20	 mov	 rax, QWORD PTR [rax+32]
  00038	48 89 44 24 40	 mov	 QWORD PTR _Last$[rsp], rax
  0003d	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00045	48 8b 40 18	 mov	 rax, QWORD PTR [rax+24]
  00049	48 89 44 24 20	 mov	 QWORD PTR _First$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1102 :         for (; _First != _Last; ++_First) {

  0004e	eb 0e		 jmp	 SHORT $LN8@Reallocati
$LN6@Reallocati:
  00050	48 8b 44 24 20	 mov	 rax, QWORD PTR _First$[rsp]
  00055	48 83 c0 08	 add	 rax, 8
  00059	48 89 44 24 20	 mov	 QWORD PTR _First$[rsp], rax
$LN8@Reallocati:
  0005e	48 8b 44 24 40	 mov	 rax, QWORD PTR _Last$[rsp]
  00063	48 39 44 24 20	 cmp	 QWORD PTR _First$[rsp], rax
  00068	74 39		 je	 SHORT $LN7@Reallocati

; 69   :     return _Ptr;

  0006a	48 8b 44 24 20	 mov	 rax, QWORD PTR _First$[rsp]
  0006f	48 89 44 24 48	 mov	 QWORD PTR $T1[rsp], rax

; 1103 :             allocator_traits<_Alloc>::destroy(_Al, _STD _Unfancy(_First));

  00074	48 8b 44 24 48	 mov	 rax, QWORD PTR $T1[rsp]
  00079	48 89 44 24 30	 mov	 QWORD PTR _Ptr$[rsp], rax
  0007e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  00083	e8 00 00 00 00	 call	 ??1?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@QEAA@XZ ; std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >::~unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >
  00088	33 c0		 xor	 eax, eax
  0008a	83 e0 01	 and	 eax, 1
  0008d	85 c0		 test	 eax, eax
  0008f	74 10		 je	 SHORT $LN21@Reallocati
  00091	ba 08 00 00 00	 mov	 edx, 8
  00096	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  0009b	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  000a0	90		 npad	 1
$LN21@Reallocati:

; 1104 :         }

  000a1	eb ad		 jmp	 SHORT $LN6@Reallocati
$LN7@Reallocati:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 623  :                 _Al.deallocate(_New_begin, _New_capacity);

  000a3	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000ab	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  000af	48 89 44 24 50	 mov	 QWORD PTR _Count$[rsp], rax
  000b4	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000bc	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  000c0	48 89 44 24 58	 mov	 QWORD PTR _Ptr$[rsp], rax
  000c5	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000cd	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000d0	48 89 44 24 68	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  000d5	48 8b 44 24 50	 mov	 rax, QWORD PTR _Count$[rsp]
  000da	48 c1 e0 03	 shl	 rax, 3
  000de	48 89 44 24 28	 mov	 QWORD PTR _Bytes$[rsp], rax
  000e3	48 8b 44 24 58	 mov	 rax, QWORD PTR _Ptr$[rsp]
  000e8	48 89 44 24 38	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  000ed	48 81 7c 24 28
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  000f6	72 10		 jb	 SHORT $LN50@Reallocati

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  000f8	48 8d 54 24 28	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  000fd	48 8d 4c 24 38	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  00102	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  00107	90		 npad	 1
$LN50@Reallocati:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  00108	48 8b 54 24 28	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  0010d	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  00112	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00117	90		 npad	 1
$LN2@Reallocati:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 625  :         }

  00118	48 83 c4 78	 add	 rsp, 120		; 00000078H
  0011c	c3		 ret	 0
??1_Reallocation_guard@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@QEAA@XZ ENDP ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Reallocation_guard::~_Reallocation_guard
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ??$?0U?$default_delete@VEventSchedule@mu2@@@std@@$0A@@?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@QEAA@$$QEAV01@@Z
_TEXT	SEGMENT
_Val$ = 0
$T1 = 8
_Old_val$2 = 16
$T3 = 24
$T4 = 32
$T5 = 40
$T6 = 48
$T7 = 56
this$ = 64
$T8 = 72
$T9 = 80
this$ = 112
_Right$ = 120
??$?0U?$default_delete@VEventSchedule@mu2@@@std@@$0A@@?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@QEAA@$$QEAV01@@Z PROC ; std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> ><std::default_delete<mu2::EventSchedule>,0>, COMDAT

; 3386 :         : _Mypair(_One_then_variadic_args_t{}, _STD forward<_Dx>(_Right.get_deleter()), _Right.release()) {}

$LN44:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 68	 sub	 rsp, 104		; 00000068H

; 3465 :         return _STD exchange(_Mypair._Myval2, nullptr);

  0000e	48 c7 44 24 08
	00 00 00 00	 mov	 QWORD PTR $T1[rsp], 0
  00017	48 8b 44 24 78	 mov	 rax, QWORD PTR _Right$[rsp]
  0001c	48 89 04 24	 mov	 QWORD PTR _Val$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 773  :     _Ty _Old_val = static_cast<_Ty&&>(_Val);

  00020	48 8b 04 24	 mov	 rax, QWORD PTR _Val$[rsp]
  00024	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00027	48 89 44 24 10	 mov	 QWORD PTR _Old_val$2[rsp], rax

; 774  :     _Val         = static_cast<_Other&&>(_New_val);

  0002c	48 8b 04 24	 mov	 rax, QWORD PTR _Val$[rsp]
  00030	48 8b 4c 24 08	 mov	 rcx, QWORD PTR $T1[rsp]
  00035	48 89 08	 mov	 QWORD PTR [rax], rcx

; 775  :     return _Old_val;

  00038	48 8b 44 24 10	 mov	 rax, QWORD PTR _Old_val$2[rsp]
  0003d	48 89 44 24 18	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3465 :         return _STD exchange(_Mypair._Myval2, nullptr);

  00042	48 8b 44 24 18	 mov	 rax, QWORD PTR $T3[rsp]
  00047	48 89 44 24 20	 mov	 QWORD PTR $T4[rsp], rax

; 3386 :         : _Mypair(_One_then_variadic_args_t{}, _STD forward<_Dx>(_Right.get_deleter()), _Right.release()) {}

  0004c	48 8b 44 24 20	 mov	 rax, QWORD PTR $T4[rsp]
  00051	48 89 44 24 38	 mov	 QWORD PTR $T7[rsp], rax

; 3441 :         return _Mypair._Get_first();

  00056	48 8b 44 24 78	 mov	 rax, QWORD PTR _Right$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  0005b	48 89 44 24 28	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3441 :         return _Mypair._Get_first();

  00060	48 8b 44 24 28	 mov	 rax, QWORD PTR $T5[rsp]
  00065	48 89 44 24 30	 mov	 QWORD PTR $T6[rsp], rax

; 3386 :         : _Mypair(_One_then_variadic_args_t{}, _STD forward<_Dx>(_Right.get_deleter()), _Right.release()) {}

  0006a	48 8b 44 24 30	 mov	 rax, QWORD PTR $T6[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  0006f	48 89 44 24 50	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3386 :         : _Mypair(_One_then_variadic_args_t{}, _STD forward<_Dx>(_Right.get_deleter()), _Right.release()) {}

  00074	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  00079	48 89 44 24 40	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  0007e	48 8d 44 24 38	 lea	 rax, QWORD PTR $T7[rsp]
  00083	48 89 44 24 48	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1536 :         : _Ty1(_STD forward<_Other1>(_Val1)), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  00088	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0008d	48 8b 4c 24 48	 mov	 rcx, QWORD PTR $T8[rsp]
  00092	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00095	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3386 :         : _Mypair(_One_then_variadic_args_t{}, _STD forward<_Dx>(_Right.get_deleter()), _Right.release()) {}

  00098	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0009d	48 83 c4 68	 add	 rsp, 104		; 00000068H
  000a1	c3		 ret	 0
??$?0U?$default_delete@VEventSchedule@mu2@@@std@@$0A@@?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@QEAA@$$QEAV01@@Z ENDP ; std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> ><std::default_delete<mu2::EventSchedule>,0>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ??$?0VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@$0A@@?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@QEAA@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z
_TEXT	SEGMENT
_Val$ = 0
$T1 = 8
_Old_val$2 = 16
$T3 = 24
$T4 = 32
$T5 = 40
$T6 = 48
$T7 = 56
$T8 = 64
this$ = 72
$T9 = 80
$T10 = 88
this$ = 112
_Right$ = 120
??$?0VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@$0A@@?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@QEAA@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z PROC ; std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> ><mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt>,0>, COMDAT

; 3394 :         : _Mypair(_One_then_variadic_args_t{}, _STD forward<_Dx2>(_Right.get_deleter()), _Right.release()) {}

$LN49:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 68	 sub	 rsp, 104		; 00000068H

; 3465 :         return _STD exchange(_Mypair._Myval2, nullptr);

  0000e	48 c7 44 24 08
	00 00 00 00	 mov	 QWORD PTR $T1[rsp], 0
  00017	48 8b 44 24 78	 mov	 rax, QWORD PTR _Right$[rsp]
  0001c	48 89 04 24	 mov	 QWORD PTR _Val$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 773  :     _Ty _Old_val = static_cast<_Ty&&>(_Val);

  00020	48 8b 04 24	 mov	 rax, QWORD PTR _Val$[rsp]
  00024	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00027	48 89 44 24 10	 mov	 QWORD PTR _Old_val$2[rsp], rax

; 774  :     _Val         = static_cast<_Other&&>(_New_val);

  0002c	48 8b 04 24	 mov	 rax, QWORD PTR _Val$[rsp]
  00030	48 8b 4c 24 08	 mov	 rcx, QWORD PTR $T1[rsp]
  00035	48 89 08	 mov	 QWORD PTR [rax], rcx

; 775  :     return _Old_val;

  00038	48 8b 44 24 10	 mov	 rax, QWORD PTR _Old_val$2[rsp]
  0003d	48 89 44 24 18	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3465 :         return _STD exchange(_Mypair._Myval2, nullptr);

  00042	48 8b 44 24 18	 mov	 rax, QWORD PTR $T3[rsp]
  00047	48 89 44 24 20	 mov	 QWORD PTR $T4[rsp], rax

; 3394 :         : _Mypair(_One_then_variadic_args_t{}, _STD forward<_Dx2>(_Right.get_deleter()), _Right.release()) {}

  0004c	48 8b 44 24 20	 mov	 rax, QWORD PTR $T4[rsp]
  00051	48 89 44 24 40	 mov	 QWORD PTR $T8[rsp], rax

; 3441 :         return _Mypair._Get_first();

  00056	48 8b 44 24 78	 mov	 rax, QWORD PTR _Right$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  0005b	48 89 44 24 28	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3441 :         return _Mypair._Get_first();

  00060	48 8b 44 24 28	 mov	 rax, QWORD PTR $T5[rsp]
  00065	48 89 44 24 30	 mov	 QWORD PTR $T6[rsp], rax

; 3394 :         : _Mypair(_One_then_variadic_args_t{}, _STD forward<_Dx2>(_Right.get_deleter()), _Right.release()) {}

  0006a	48 8b 44 24 30	 mov	 rax, QWORD PTR $T6[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  0006f	48 89 44 24 38	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3394 :         : _Mypair(_One_then_variadic_args_t{}, _STD forward<_Dx2>(_Right.get_deleter()), _Right.release()) {}

  00074	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  00079	48 89 44 24 48	 mov	 QWORD PTR this$[rsp], rax
  0007e	48 8b 44 24 38	 mov	 rax, QWORD PTR $T7[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00083	48 89 44 24 58	 mov	 QWORD PTR $T10[rsp], rax
  00088	48 8d 44 24 40	 lea	 rax, QWORD PTR $T8[rsp]
  0008d	48 89 44 24 50	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1536 :         : _Ty1(_STD forward<_Other1>(_Val1)), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  00092	48 8b 44 24 48	 mov	 rax, QWORD PTR this$[rsp]
  00097	48 8b 4c 24 50	 mov	 rcx, QWORD PTR $T9[rsp]
  0009c	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0009f	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3394 :         : _Mypair(_One_then_variadic_args_t{}, _STD forward<_Dx2>(_Right.get_deleter()), _Right.release()) {}

  000a2	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  000a7	48 83 c4 68	 add	 rsp, 104		; 00000068H
  000ab	c3		 ret	 0
??$?0VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@$0A@@?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@QEAA@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z ENDP ; std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> ><mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt>,0>
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??__F?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ
text$yd	SEGMENT
??__F?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ PROC ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::GlobalLoadScript>::inst'', COMDAT
  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A ; mu2::ISingleton<mu2::GlobalLoadScript>::inst
  0000b	e8 00 00 00 00	 call	 ??1GlobalLoadScript@mu2@@UEAA@XZ ; mu2::GlobalLoadScript::~GlobalLoadScript
  00010	90		 npad	 1
  00011	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00015	c3		 ret	 0
??__F?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ ENDP ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::GlobalLoadScript>::inst''
text$yd	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??__E?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ
text$di	SEGMENT
??__E?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ PROC ; `dynamic initializer for 'mu2::ISingleton<mu2::GlobalLoadScript>::inst'', COMDAT

; 90   : template<typename T> T ISingleton<T>::inst;

  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A ; mu2::ISingleton<mu2::GlobalLoadScript>::inst
  0000b	e8 00 00 00 00	 call	 ??0GlobalLoadScript@mu2@@AEAA@XZ ; mu2::GlobalLoadScript::GlobalLoadScript
  00010	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??__F?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::GlobalLoadScript>::inst''
  00017	e8 00 00 00 00	 call	 atexit
  0001c	90		 npad	 1
  0001d	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00021	c3		 ret	 0
??__E?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A@@YAXXZ ENDP ; `dynamic initializer for 'mu2::ISingleton<mu2::GlobalLoadScript>::inst''
text$di	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ??$_Emplace_reallocate@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z
_TEXT	SEGMENT
_Newvec$ = 32
_Al$ = 40
_Whereoff$ = 48
_Myfirst$ = 56
_Mylast$ = 64
_Newcapacity$ = 72
_My_data$ = 80
$T1 = 88
$T2 = 96
tv163 = 104
_Oldsize$ = 112
_Constructed_last$ = 120
_Constructed_first$ = 128
_Newsize$ = 136
$T3 = 144
$T4 = 152
$T5 = 160
$T6 = 168
$T7 = 176
$T8 = 184
$T9 = 192
$T10 = 200
$T11 = 208
$T12 = 216
$T13 = 224
$T14 = 232
$T15 = 240
$T16 = 248
$T17 = 256
_Guard$ = 264
$T18 = 304
_Unsigned_max$19 = 312
this$ = 336
_Whereptr$ = 344
<_Val_0>$ = 352
??$_Emplace_reallocate@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z PROC ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Emplace_reallocate<std::unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> > >, COMDAT

; 875  :     _CONSTEXPR20 pointer _Emplace_reallocate(const pointer _Whereptr, _Valty&&... _Val) {

$LN681:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 81 ec 48 01
	00 00		 sub	 rsp, 328		; 00000148H

; 2227 :         return _Mypair._Get_first();

  00016	48 8b 84 24 50
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  0001e	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2227 :         return _Mypair._Get_first();

  00026	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR $T3[rsp]
  0002e	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR $T4[rsp], rax

; 876  :         // reallocate and insert by perfectly forwarding _Val at _Whereptr
; 877  :         _Alty& _Al        = _Getal();

  00036	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR $T4[rsp]
  0003e	48 89 44 24 28	 mov	 QWORD PTR _Al$[rsp], rax

; 878  :         auto& _My_data    = _Mypair._Myval2;

  00043	48 8b 84 24 50
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0004b	48 89 44 24 50	 mov	 QWORD PTR _My_data$[rsp], rax

; 879  :         pointer& _Myfirst = _My_data._Myfirst;

  00050	48 8b 44 24 50	 mov	 rax, QWORD PTR _My_data$[rsp]
  00055	48 89 44 24 38	 mov	 QWORD PTR _Myfirst$[rsp], rax

; 880  :         pointer& _Mylast  = _My_data._Mylast;

  0005a	48 8b 44 24 50	 mov	 rax, QWORD PTR _My_data$[rsp]
  0005f	48 83 c0 08	 add	 rax, 8
  00063	48 89 44 24 40	 mov	 QWORD PTR _Mylast$[rsp], rax

; 881  : 
; 882  :         _STL_INTERNAL_CHECK(_Mylast == _My_data._Myend); // check that we have no unused capacity
; 883  : 
; 884  :         const auto _Whereoff = static_cast<size_type>(_Whereptr - _Myfirst);

  00068	48 8b 44 24 38	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  0006d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00070	48 8b 8c 24 58
	01 00 00	 mov	 rcx, QWORD PTR _Whereptr$[rsp]
  00078	48 2b c8	 sub	 rcx, rax
  0007b	48 8b c1	 mov	 rax, rcx
  0007e	48 c1 f8 03	 sar	 rax, 3
  00082	48 89 44 24 30	 mov	 QWORD PTR _Whereoff$[rsp], rax

; 885  :         const auto _Oldsize  = static_cast<size_type>(_Mylast - _Myfirst);

  00087	48 8b 44 24 40	 mov	 rax, QWORD PTR _Mylast$[rsp]
  0008c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Myfirst$[rsp]
  00091	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00094	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00097	48 2b c1	 sub	 rax, rcx
  0009a	48 c1 f8 03	 sar	 rax, 3
  0009e	48 89 44 24 70	 mov	 QWORD PTR _Oldsize$[rsp], rax

; 2231 :         return _Mypair._Get_first();

  000a3	48 8b 84 24 50
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1543 :         return *this;

  000ab	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2231 :         return _Mypair._Get_first();

  000b3	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR $T5[rsp]
  000bb	48 89 84 24 30
	01 00 00	 mov	 QWORD PTR $T18[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 746  :         return static_cast<size_t>(-1) / sizeof(value_type);

  000c3	48 b8 ff ff ff
	ff ff ff ff 1f	 mov	 rax, 2305843009213693951 ; 1fffffffffffffffH
  000cd	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1919 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()), _Alty_traits::max_size(_Getal()));

  000d5	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T6[rsp]
  000dd	48 89 44 24 58	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 866  :         constexpr auto _Unsigned_max = static_cast<make_unsigned_t<_Ty>>(-1);

  000e2	48 c7 84 24 38
	01 00 00 ff ff
	ff ff		 mov	 QWORD PTR _Unsigned_max$19[rsp], -1

; 867  :         return static_cast<_Ty>(_Unsigned_max >> 1);

  000ee	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  000f8	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1919 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()), _Alty_traits::max_size(_Getal()));

  00100	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR $T7[rsp]
  00108	48 89 44 24 60	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 101  :     return _Right < _Left ? _Right : _Left;

  0010d	48 8b 44 24 60	 mov	 rax, QWORD PTR $T2[rsp]
  00112	48 39 44 24 58	 cmp	 QWORD PTR $T1[rsp], rax
  00117	73 0c		 jae	 SHORT $LN44@Emplace_re
  00119	48 8d 44 24 58	 lea	 rax, QWORD PTR $T1[rsp]
  0011e	48 89 44 24 68	 mov	 QWORD PTR tv163[rsp], rax
  00123	eb 0a		 jmp	 SHORT $LN45@Emplace_re
$LN44@Emplace_re:
  00125	48 8d 44 24 60	 lea	 rax, QWORD PTR $T2[rsp]
  0012a	48 89 44 24 68	 mov	 QWORD PTR tv163[rsp], rax
$LN45@Emplace_re:
  0012f	48 8b 44 24 68	 mov	 rax, QWORD PTR tv163[rsp]
  00134	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T8[rsp], rax
  0013c	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T8[rsp]
  00144	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1919 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()), _Alty_traits::max_size(_Getal()));

  0014c	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR $T9[rsp]
  00154	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00157	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax

; 886  : 
; 887  :         if (_Oldsize == max_size()) {

  0015f	48 8b 84 24 c8
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  00167	48 39 44 24 70	 cmp	 QWORD PTR _Oldsize$[rsp], rax
  0016c	75 06		 jne	 SHORT $LN2@Emplace_re

; 888  :             _Xlength();

  0016e	e8 00 00 00 00	 call	 ?_Xlength@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@CAXXZ ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Xlength
  00173	90		 npad	 1
$LN2@Emplace_re:

; 889  :         }
; 890  : 
; 891  :         const size_type _Newsize = _Oldsize + 1;

  00174	48 8b 44 24 70	 mov	 rax, QWORD PTR _Oldsize$[rsp]
  00179	48 ff c0	 inc	 rax
  0017c	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR _Newsize$[rsp], rax

; 892  :         size_type _Newcapacity   = _Calculate_growth(_Newsize);

  00184	48 8b 94 24 88
	00 00 00	 mov	 rdx, QWORD PTR _Newsize$[rsp]
  0018c	48 8b 8c 24 50
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00194	e8 00 00 00 00	 call	 ?_Calculate_growth@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEBA_K_K@Z ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Calculate_growth
  00199	48 89 44 24 48	 mov	 QWORD PTR _Newcapacity$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 2303 :         return _Al.allocate(_Count);

  0019e	48 8b 54 24 48	 mov	 rdx, QWORD PTR _Newcapacity$[rsp]
  001a3	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Al$[rsp]
  001a8	e8 00 00 00 00	 call	 ?allocate@?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@std@@QEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@2@_K@Z ; std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > >::allocate
  001ad	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 894  :         const pointer _Newvec           = _STD _Allocate_at_least_helper(_Al, _Newcapacity);

  001b5	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  001bd	48 89 44 24 20	 mov	 QWORD PTR _Newvec$[rsp], rax

; 895  :         const pointer _Constructed_last = _Newvec + _Whereoff + 1;

  001c2	48 8b 44 24 20	 mov	 rax, QWORD PTR _Newvec$[rsp]
  001c7	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Whereoff$[rsp]
  001cc	48 8d 44 c8 08	 lea	 rax, QWORD PTR [rax+rcx*8+8]
  001d1	48 89 44 24 78	 mov	 QWORD PTR _Constructed_last$[rsp], rax

; 896  : 
; 897  :         _Reallocation_guard _Guard{_Al, _Newvec, _Newcapacity, _Constructed_last, _Constructed_last};

  001d6	48 8b 44 24 28	 mov	 rax, QWORD PTR _Al$[rsp]
  001db	48 89 84 24 08
	01 00 00	 mov	 QWORD PTR _Guard$[rsp], rax
  001e3	48 8b 44 24 20	 mov	 rax, QWORD PTR _Newvec$[rsp]
  001e8	48 89 84 24 10
	01 00 00	 mov	 QWORD PTR _Guard$[rsp+8], rax
  001f0	48 8b 44 24 48	 mov	 rax, QWORD PTR _Newcapacity$[rsp]
  001f5	48 89 84 24 18
	01 00 00	 mov	 QWORD PTR _Guard$[rsp+16], rax
  001fd	48 8b 44 24 78	 mov	 rax, QWORD PTR _Constructed_last$[rsp]
  00202	48 89 84 24 20
	01 00 00	 mov	 QWORD PTR _Guard$[rsp+24], rax
  0020a	48 8b 44 24 78	 mov	 rax, QWORD PTR _Constructed_last$[rsp]
  0020f	48 89 84 24 28
	01 00 00	 mov	 QWORD PTR _Guard$[rsp+32], rax

; 898  :         auto& _Constructed_first = _Guard._Constructed_first;

  00217	48 8d 84 24 20
	01 00 00	 lea	 rax, QWORD PTR _Guard$[rsp+24]
  0021f	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _Constructed_first$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00227	48 8b 84 24 60
	01 00 00	 mov	 rax, QWORD PTR <_Val_0>$[rsp]
  0022f	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR $T14[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 900  :         _Alty_traits::construct(_Al, _STD _Unfancy(_Newvec + _Whereoff), _STD forward<_Valty>(_Val)...);

  00237	48 8b 44 24 20	 mov	 rax, QWORD PTR _Newvec$[rsp]
  0023c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Whereoff$[rsp]
  00241	48 8d 04 c8	 lea	 rax, QWORD PTR [rax+rcx*8]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  00245	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 900  :         _Alty_traits::construct(_Al, _STD _Unfancy(_Newvec + _Whereoff), _STD forward<_Valty>(_Val)...);

  0024d	48 8b 84 24 d8
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  00255	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 732  :         ::new (const_cast<void*>(static_cast<const volatile void*>(_Ptr))) _Objty(_STD forward<_Types>(_Args)...);

  0025d	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR $T13[rsp]
  00265	48 89 84 24 f8
	00 00 00	 mov	 QWORD PTR $T16[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 900  :         _Alty_traits::construct(_Al, _STD _Unfancy(_Newvec + _Whereoff), _STD forward<_Valty>(_Val)...);

  0026d	48 8b 84 24 e8
	00 00 00	 mov	 rax, QWORD PTR $T14[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00275	48 89 84 24 f0
	00 00 00	 mov	 QWORD PTR $T15[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 732  :         ::new (const_cast<void*>(static_cast<const volatile void*>(_Ptr))) _Objty(_STD forward<_Types>(_Args)...);

  0027d	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR $T15[rsp]
  00285	48 8b d0	 mov	 rdx, rax
  00288	48 8b 8c 24 f8
	00 00 00	 mov	 rcx, QWORD PTR $T16[rsp]
  00290	e8 00 00 00 00	 call	 ??$?0VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@$0A@@?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@QEAA@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z ; std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> ><mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule>,0>
  00295	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 901  :         _Constructed_first = _Newvec + _Whereoff;

  00296	48 8b 44 24 20	 mov	 rax, QWORD PTR _Newvec$[rsp]
  0029b	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Whereoff$[rsp]
  002a0	48 8d 04 c8	 lea	 rax, QWORD PTR [rax+rcx*8]
  002a4	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR _Constructed_first$[rsp]
  002ac	48 89 01	 mov	 QWORD PTR [rcx], rax

; 902  : 
; 903  :         if (_Whereptr == _Mylast) { // at back, provide strong guarantee

  002af	48 8b 44 24 40	 mov	 rax, QWORD PTR _Mylast$[rsp]
  002b4	48 8b 00	 mov	 rax, QWORD PTR [rax]
  002b7	48 39 84 24 58
	01 00 00	 cmp	 QWORD PTR _Whereptr$[rsp], rax
  002bf	75 22		 jne	 SHORT $LN3@Emplace_re

; 904  :             if constexpr (is_nothrow_move_constructible_v<_Ty> || !is_copy_constructible_v<_Ty>) {
; 905  :                 _STD _Uninitialized_move(_Myfirst, _Mylast, _Newvec, _Al);

  002c1	4c 8b 4c 24 28	 mov	 r9, QWORD PTR _Al$[rsp]
  002c6	4c 8b 44 24 20	 mov	 r8, QWORD PTR _Newvec$[rsp]
  002cb	48 8b 44 24 40	 mov	 rax, QWORD PTR _Mylast$[rsp]
  002d0	48 8b 10	 mov	 rdx, QWORD PTR [rax]
  002d3	48 8b 44 24 38	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  002d8	48 8b 08	 mov	 rcx, QWORD PTR [rax]
  002db	e8 00 00 00 00	 call	 ??$_Uninitialized_move@PEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@YAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@0@@Z ; std::_Uninitialized_move<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > *,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >
  002e0	90		 npad	 1

; 906  :             } else {
; 907  :                 _STD _Uninitialized_copy(_Myfirst, _Mylast, _Newvec, _Al);
; 908  :             }
; 909  :         } else { // provide basic guarantee

  002e1	eb 5c		 jmp	 SHORT $LN4@Emplace_re
$LN3@Emplace_re:

; 910  :             _STD _Uninitialized_move(_Myfirst, _Whereptr, _Newvec, _Al);

  002e3	4c 8b 4c 24 28	 mov	 r9, QWORD PTR _Al$[rsp]
  002e8	4c 8b 44 24 20	 mov	 r8, QWORD PTR _Newvec$[rsp]
  002ed	48 8b 94 24 58
	01 00 00	 mov	 rdx, QWORD PTR _Whereptr$[rsp]
  002f5	48 8b 44 24 38	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  002fa	48 8b 08	 mov	 rcx, QWORD PTR [rax]
  002fd	e8 00 00 00 00	 call	 ??$_Uninitialized_move@PEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@YAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@0@@Z ; std::_Uninitialized_move<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > *,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >

; 911  :             _Constructed_first = _Newvec;

  00302	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _Constructed_first$[rsp]
  0030a	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Newvec$[rsp]
  0030f	48 89 08	 mov	 QWORD PTR [rax], rcx

; 912  :             _STD _Uninitialized_move(_Whereptr, _Mylast, _Newvec + _Whereoff + 1, _Al);

  00312	48 8b 44 24 20	 mov	 rax, QWORD PTR _Newvec$[rsp]
  00317	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Whereoff$[rsp]
  0031c	48 8d 44 c8 08	 lea	 rax, QWORD PTR [rax+rcx*8+8]
  00321	4c 8b 4c 24 28	 mov	 r9, QWORD PTR _Al$[rsp]
  00326	4c 8b c0	 mov	 r8, rax
  00329	48 8b 44 24 40	 mov	 rax, QWORD PTR _Mylast$[rsp]
  0032e	48 8b 10	 mov	 rdx, QWORD PTR [rax]
  00331	48 8b 8c 24 58
	01 00 00	 mov	 rcx, QWORD PTR _Whereptr$[rsp]
  00339	e8 00 00 00 00	 call	 ??$_Uninitialized_move@PEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@YAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@0@@Z ; std::_Uninitialized_move<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > *,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >
  0033e	90		 npad	 1
$LN4@Emplace_re:

; 913  :         }
; 914  : 
; 915  :         _Guard._New_begin = nullptr;

  0033f	48 c7 84 24 10
	01 00 00 00 00
	00 00		 mov	 QWORD PTR _Guard$[rsp+8], 0

; 916  :         _Change_array(_Newvec, _Newsize, _Newcapacity);

  0034b	4c 8b 4c 24 48	 mov	 r9, QWORD PTR _Newcapacity$[rsp]
  00350	4c 8b 84 24 88
	00 00 00	 mov	 r8, QWORD PTR _Newsize$[rsp]
  00358	48 8b 54 24 20	 mov	 rdx, QWORD PTR _Newvec$[rsp]
  0035d	48 8b 8c 24 50
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00365	e8 00 00 00 00	 call	 ?_Change_array@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAXQEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@2@_K1@Z ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Change_array
  0036a	90		 npad	 1

; 917  :         return _Newvec + _Whereoff;

  0036b	48 8b 44 24 20	 mov	 rax, QWORD PTR _Newvec$[rsp]
  00370	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Whereoff$[rsp]
  00375	48 8d 04 c8	 lea	 rax, QWORD PTR [rax+rcx*8]
  00379	48 89 84 24 00
	01 00 00	 mov	 QWORD PTR $T17[rsp], rax
  00381	48 8d 8c 24 08
	01 00 00	 lea	 rcx, QWORD PTR _Guard$[rsp]
  00389	e8 00 00 00 00	 call	 ??1_Reallocation_guard@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@QEAA@XZ ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Reallocation_guard::~_Reallocation_guard
  0038e	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR $T17[rsp]

; 918  :     }

  00396	48 81 c4 48 01
	00 00		 add	 rsp, 328		; 00000148H
  0039d	c3		 ret	 0
$LN680@Emplace_re:
??$_Emplace_reallocate@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z ENDP ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Emplace_reallocate<std::unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> > >
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
_Newvec$ = 32
_Al$ = 40
_Whereoff$ = 48
_Myfirst$ = 56
_Mylast$ = 64
_Newcapacity$ = 72
_My_data$ = 80
$T1 = 88
$T2 = 96
tv163 = 104
_Oldsize$ = 112
_Constructed_last$ = 120
_Constructed_first$ = 128
_Newsize$ = 136
$T3 = 144
$T4 = 152
$T5 = 160
$T6 = 168
$T7 = 176
$T8 = 184
$T9 = 192
$T10 = 200
$T11 = 208
$T12 = 216
$T13 = 224
$T14 = 232
$T15 = 240
$T16 = 248
$T17 = 256
_Guard$ = 264
$T18 = 304
_Unsigned_max$19 = 312
this$ = 336
_Whereptr$ = 344
<_Val_0>$ = 352
?dtor$0@?0???$_Emplace_reallocate@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z@4HA PROC ; `std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Emplace_reallocate<std::unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> > >'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8d 8d 08 01
	00 00		 lea	 rcx, QWORD PTR _Guard$[rbp]
  00010	e8 00 00 00 00	 call	 ??1_Reallocation_guard@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@QEAA@XZ ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Reallocation_guard::~_Reallocation_guard
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$0@?0???$_Emplace_reallocate@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z@4HA ENDP ; `std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Emplace_reallocate<std::unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> > >'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ??$_Emplace_back_with_unused_capacity@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAAEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z
_TEXT	SEGMENT
_Mylast$ = 32
_My_data$ = 40
_Obj$ = 48
$T1 = 56
$T2 = 64
$T3 = 72
$T4 = 80
$T5 = 88
_Result$ = 96
this$ = 128
<_Val_0>$ = 136
??$_Emplace_back_with_unused_capacity@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAAEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z PROC ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Emplace_back_with_unused_capacity<std::unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> > >, COMDAT

; 852  :     _CONSTEXPR20 _Ty& _Emplace_back_with_unused_capacity(_Valty&&... _Val) {

$LN80:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 78	 sub	 rsp, 120		; 00000078H

; 853  :         // insert by perfectly forwarding into element at end, provide strong guarantee
; 854  :         auto& _My_data   = _Mypair._Myval2;

  0000e	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00016	48 89 44 24 28	 mov	 QWORD PTR _My_data$[rsp], rax

; 855  :         pointer& _Mylast = _My_data._Mylast;

  0001b	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00020	48 83 c0 08	 add	 rax, 8
  00024	48 89 44 24 20	 mov	 QWORD PTR _Mylast$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00029	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR <_Val_0>$[rsp]
  00031	48 89 44 24 48	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 860  :             _STD _Construct_in_place(*_Mylast, _STD forward<_Valty>(_Val)...);

  00036	48 8b 44 24 20	 mov	 rax, QWORD PTR _Mylast$[rsp]
  0003b	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0003e	48 89 44 24 30	 mov	 QWORD PTR _Obj$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00043	48 8b 44 24 30	 mov	 rax, QWORD PTR _Obj$[rsp]
  00048	48 89 44 24 38	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0004d	48 8b 44 24 38	 mov	 rax, QWORD PTR $T1[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  00052	48 89 44 24 40	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00057	48 8b 44 24 40	 mov	 rax, QWORD PTR $T2[rsp]
  0005c	48 89 44 24 58	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 860  :             _STD _Construct_in_place(*_Mylast, _STD forward<_Valty>(_Val)...);

  00061	48 8b 44 24 48	 mov	 rax, QWORD PTR $T3[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00066	48 89 44 24 50	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0006b	48 8b 44 24 50	 mov	 rax, QWORD PTR $T4[rsp]
  00070	48 8b d0	 mov	 rdx, rax
  00073	48 8b 4c 24 58	 mov	 rcx, QWORD PTR $T5[rsp]
  00078	e8 00 00 00 00	 call	 ??$?0VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@$0A@@?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@QEAA@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z ; std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> ><mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule>,0>
  0007d	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 868  :         _Ty& _Result = *_Mylast;

  0007e	48 8b 44 24 20	 mov	 rax, QWORD PTR _Mylast$[rsp]
  00083	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00086	48 89 44 24 60	 mov	 QWORD PTR _Result$[rsp], rax

; 869  :         ++_Mylast;

  0008b	48 8b 44 24 20	 mov	 rax, QWORD PTR _Mylast$[rsp]
  00090	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00093	48 83 c0 08	 add	 rax, 8
  00097	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Mylast$[rsp]
  0009c	48 89 01	 mov	 QWORD PTR [rcx], rax

; 870  : 
; 871  :         return _Result;

  0009f	48 8b 44 24 60	 mov	 rax, QWORD PTR _Result$[rsp]

; 872  :     }

  000a4	48 83 c4 78	 add	 rsp, 120		; 00000078H
  000a8	c3		 ret	 0
??$_Emplace_back_with_unused_capacity@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAAEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z ENDP ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Emplace_back_with_unused_capacity<std::unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ??$_Emplace_reallocate@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z
_TEXT	SEGMENT
_Newvec$ = 32
_Al$ = 40
_Whereoff$ = 48
_Myfirst$ = 56
_Mylast$ = 64
_Newcapacity$ = 72
_My_data$ = 80
$T1 = 88
$T2 = 96
tv163 = 104
_Oldsize$ = 112
_Constructed_last$ = 120
_Constructed_first$ = 128
_Newsize$ = 136
$T3 = 144
$T4 = 152
$T5 = 160
$T6 = 168
$T7 = 176
$T8 = 184
$T9 = 192
$T10 = 200
$T11 = 208
$T12 = 216
$T13 = 224
$T14 = 232
$T15 = 240
$T16 = 248
$T17 = 256
_Guard$ = 264
$T18 = 304
_Unsigned_max$19 = 312
this$ = 336
_Whereptr$ = 344
<_Val_0>$ = 352
??$_Emplace_reallocate@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z PROC ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Emplace_reallocate<std::unique_ptr<mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt> > >, COMDAT

; 875  :     _CONSTEXPR20 pointer _Emplace_reallocate(const pointer _Whereptr, _Valty&&... _Val) {

$LN681:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 81 ec 48 01
	00 00		 sub	 rsp, 328		; 00000148H

; 2227 :         return _Mypair._Get_first();

  00016	48 8b 84 24 50
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  0001e	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2227 :         return _Mypair._Get_first();

  00026	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR $T3[rsp]
  0002e	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR $T4[rsp], rax

; 876  :         // reallocate and insert by perfectly forwarding _Val at _Whereptr
; 877  :         _Alty& _Al        = _Getal();

  00036	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR $T4[rsp]
  0003e	48 89 44 24 28	 mov	 QWORD PTR _Al$[rsp], rax

; 878  :         auto& _My_data    = _Mypair._Myval2;

  00043	48 8b 84 24 50
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0004b	48 89 44 24 50	 mov	 QWORD PTR _My_data$[rsp], rax

; 879  :         pointer& _Myfirst = _My_data._Myfirst;

  00050	48 8b 44 24 50	 mov	 rax, QWORD PTR _My_data$[rsp]
  00055	48 89 44 24 38	 mov	 QWORD PTR _Myfirst$[rsp], rax

; 880  :         pointer& _Mylast  = _My_data._Mylast;

  0005a	48 8b 44 24 50	 mov	 rax, QWORD PTR _My_data$[rsp]
  0005f	48 83 c0 08	 add	 rax, 8
  00063	48 89 44 24 40	 mov	 QWORD PTR _Mylast$[rsp], rax

; 881  : 
; 882  :         _STL_INTERNAL_CHECK(_Mylast == _My_data._Myend); // check that we have no unused capacity
; 883  : 
; 884  :         const auto _Whereoff = static_cast<size_type>(_Whereptr - _Myfirst);

  00068	48 8b 44 24 38	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  0006d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00070	48 8b 8c 24 58
	01 00 00	 mov	 rcx, QWORD PTR _Whereptr$[rsp]
  00078	48 2b c8	 sub	 rcx, rax
  0007b	48 8b c1	 mov	 rax, rcx
  0007e	48 c1 f8 03	 sar	 rax, 3
  00082	48 89 44 24 30	 mov	 QWORD PTR _Whereoff$[rsp], rax

; 885  :         const auto _Oldsize  = static_cast<size_type>(_Mylast - _Myfirst);

  00087	48 8b 44 24 40	 mov	 rax, QWORD PTR _Mylast$[rsp]
  0008c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Myfirst$[rsp]
  00091	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00094	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00097	48 2b c1	 sub	 rax, rcx
  0009a	48 c1 f8 03	 sar	 rax, 3
  0009e	48 89 44 24 70	 mov	 QWORD PTR _Oldsize$[rsp], rax

; 2231 :         return _Mypair._Get_first();

  000a3	48 8b 84 24 50
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1543 :         return *this;

  000ab	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2231 :         return _Mypair._Get_first();

  000b3	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR $T5[rsp]
  000bb	48 89 84 24 30
	01 00 00	 mov	 QWORD PTR $T18[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 746  :         return static_cast<size_t>(-1) / sizeof(value_type);

  000c3	48 b8 ff ff ff
	ff ff ff ff 1f	 mov	 rax, 2305843009213693951 ; 1fffffffffffffffH
  000cd	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1919 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()), _Alty_traits::max_size(_Getal()));

  000d5	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T6[rsp]
  000dd	48 89 44 24 58	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 866  :         constexpr auto _Unsigned_max = static_cast<make_unsigned_t<_Ty>>(-1);

  000e2	48 c7 84 24 38
	01 00 00 ff ff
	ff ff		 mov	 QWORD PTR _Unsigned_max$19[rsp], -1

; 867  :         return static_cast<_Ty>(_Unsigned_max >> 1);

  000ee	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  000f8	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1919 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()), _Alty_traits::max_size(_Getal()));

  00100	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR $T7[rsp]
  00108	48 89 44 24 60	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 101  :     return _Right < _Left ? _Right : _Left;

  0010d	48 8b 44 24 60	 mov	 rax, QWORD PTR $T2[rsp]
  00112	48 39 44 24 58	 cmp	 QWORD PTR $T1[rsp], rax
  00117	73 0c		 jae	 SHORT $LN44@Emplace_re
  00119	48 8d 44 24 58	 lea	 rax, QWORD PTR $T1[rsp]
  0011e	48 89 44 24 68	 mov	 QWORD PTR tv163[rsp], rax
  00123	eb 0a		 jmp	 SHORT $LN45@Emplace_re
$LN44@Emplace_re:
  00125	48 8d 44 24 60	 lea	 rax, QWORD PTR $T2[rsp]
  0012a	48 89 44 24 68	 mov	 QWORD PTR tv163[rsp], rax
$LN45@Emplace_re:
  0012f	48 8b 44 24 68	 mov	 rax, QWORD PTR tv163[rsp]
  00134	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T8[rsp], rax
  0013c	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T8[rsp]
  00144	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1919 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()), _Alty_traits::max_size(_Getal()));

  0014c	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR $T9[rsp]
  00154	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00157	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax

; 886  : 
; 887  :         if (_Oldsize == max_size()) {

  0015f	48 8b 84 24 c8
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  00167	48 39 44 24 70	 cmp	 QWORD PTR _Oldsize$[rsp], rax
  0016c	75 06		 jne	 SHORT $LN2@Emplace_re

; 888  :             _Xlength();

  0016e	e8 00 00 00 00	 call	 ?_Xlength@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@CAXXZ ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Xlength
  00173	90		 npad	 1
$LN2@Emplace_re:

; 889  :         }
; 890  : 
; 891  :         const size_type _Newsize = _Oldsize + 1;

  00174	48 8b 44 24 70	 mov	 rax, QWORD PTR _Oldsize$[rsp]
  00179	48 ff c0	 inc	 rax
  0017c	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR _Newsize$[rsp], rax

; 892  :         size_type _Newcapacity   = _Calculate_growth(_Newsize);

  00184	48 8b 94 24 88
	00 00 00	 mov	 rdx, QWORD PTR _Newsize$[rsp]
  0018c	48 8b 8c 24 50
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00194	e8 00 00 00 00	 call	 ?_Calculate_growth@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEBA_K_K@Z ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Calculate_growth
  00199	48 89 44 24 48	 mov	 QWORD PTR _Newcapacity$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 2303 :         return _Al.allocate(_Count);

  0019e	48 8b 54 24 48	 mov	 rdx, QWORD PTR _Newcapacity$[rsp]
  001a3	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Al$[rsp]
  001a8	e8 00 00 00 00	 call	 ?allocate@?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@std@@QEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@2@_K@Z ; std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > >::allocate
  001ad	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 894  :         const pointer _Newvec           = _STD _Allocate_at_least_helper(_Al, _Newcapacity);

  001b5	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  001bd	48 89 44 24 20	 mov	 QWORD PTR _Newvec$[rsp], rax

; 895  :         const pointer _Constructed_last = _Newvec + _Whereoff + 1;

  001c2	48 8b 44 24 20	 mov	 rax, QWORD PTR _Newvec$[rsp]
  001c7	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Whereoff$[rsp]
  001cc	48 8d 44 c8 08	 lea	 rax, QWORD PTR [rax+rcx*8+8]
  001d1	48 89 44 24 78	 mov	 QWORD PTR _Constructed_last$[rsp], rax

; 896  : 
; 897  :         _Reallocation_guard _Guard{_Al, _Newvec, _Newcapacity, _Constructed_last, _Constructed_last};

  001d6	48 8b 44 24 28	 mov	 rax, QWORD PTR _Al$[rsp]
  001db	48 89 84 24 08
	01 00 00	 mov	 QWORD PTR _Guard$[rsp], rax
  001e3	48 8b 44 24 20	 mov	 rax, QWORD PTR _Newvec$[rsp]
  001e8	48 89 84 24 10
	01 00 00	 mov	 QWORD PTR _Guard$[rsp+8], rax
  001f0	48 8b 44 24 48	 mov	 rax, QWORD PTR _Newcapacity$[rsp]
  001f5	48 89 84 24 18
	01 00 00	 mov	 QWORD PTR _Guard$[rsp+16], rax
  001fd	48 8b 44 24 78	 mov	 rax, QWORD PTR _Constructed_last$[rsp]
  00202	48 89 84 24 20
	01 00 00	 mov	 QWORD PTR _Guard$[rsp+24], rax
  0020a	48 8b 44 24 78	 mov	 rax, QWORD PTR _Constructed_last$[rsp]
  0020f	48 89 84 24 28
	01 00 00	 mov	 QWORD PTR _Guard$[rsp+32], rax

; 898  :         auto& _Constructed_first = _Guard._Constructed_first;

  00217	48 8d 84 24 20
	01 00 00	 lea	 rax, QWORD PTR _Guard$[rsp+24]
  0021f	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _Constructed_first$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00227	48 8b 84 24 60
	01 00 00	 mov	 rax, QWORD PTR <_Val_0>$[rsp]
  0022f	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR $T14[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 900  :         _Alty_traits::construct(_Al, _STD _Unfancy(_Newvec + _Whereoff), _STD forward<_Valty>(_Val)...);

  00237	48 8b 44 24 20	 mov	 rax, QWORD PTR _Newvec$[rsp]
  0023c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Whereoff$[rsp]
  00241	48 8d 04 c8	 lea	 rax, QWORD PTR [rax+rcx*8]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  00245	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 900  :         _Alty_traits::construct(_Al, _STD _Unfancy(_Newvec + _Whereoff), _STD forward<_Valty>(_Val)...);

  0024d	48 8b 84 24 d8
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  00255	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 732  :         ::new (const_cast<void*>(static_cast<const volatile void*>(_Ptr))) _Objty(_STD forward<_Types>(_Args)...);

  0025d	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR $T13[rsp]
  00265	48 89 84 24 f8
	00 00 00	 mov	 QWORD PTR $T16[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 900  :         _Alty_traits::construct(_Al, _STD _Unfancy(_Newvec + _Whereoff), _STD forward<_Valty>(_Val)...);

  0026d	48 8b 84 24 e8
	00 00 00	 mov	 rax, QWORD PTR $T14[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00275	48 89 84 24 f0
	00 00 00	 mov	 QWORD PTR $T15[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 732  :         ::new (const_cast<void*>(static_cast<const volatile void*>(_Ptr))) _Objty(_STD forward<_Types>(_Args)...);

  0027d	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR $T15[rsp]
  00285	48 8b d0	 mov	 rdx, rax
  00288	48 8b 8c 24 f8
	00 00 00	 mov	 rcx, QWORD PTR $T16[rsp]
  00290	e8 00 00 00 00	 call	 ??$?0VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@$0A@@?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@QEAA@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z ; std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> ><mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt>,0>
  00295	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 901  :         _Constructed_first = _Newvec + _Whereoff;

  00296	48 8b 44 24 20	 mov	 rax, QWORD PTR _Newvec$[rsp]
  0029b	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Whereoff$[rsp]
  002a0	48 8d 04 c8	 lea	 rax, QWORD PTR [rax+rcx*8]
  002a4	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR _Constructed_first$[rsp]
  002ac	48 89 01	 mov	 QWORD PTR [rcx], rax

; 902  : 
; 903  :         if (_Whereptr == _Mylast) { // at back, provide strong guarantee

  002af	48 8b 44 24 40	 mov	 rax, QWORD PTR _Mylast$[rsp]
  002b4	48 8b 00	 mov	 rax, QWORD PTR [rax]
  002b7	48 39 84 24 58
	01 00 00	 cmp	 QWORD PTR _Whereptr$[rsp], rax
  002bf	75 22		 jne	 SHORT $LN3@Emplace_re

; 904  :             if constexpr (is_nothrow_move_constructible_v<_Ty> || !is_copy_constructible_v<_Ty>) {
; 905  :                 _STD _Uninitialized_move(_Myfirst, _Mylast, _Newvec, _Al);

  002c1	4c 8b 4c 24 28	 mov	 r9, QWORD PTR _Al$[rsp]
  002c6	4c 8b 44 24 20	 mov	 r8, QWORD PTR _Newvec$[rsp]
  002cb	48 8b 44 24 40	 mov	 rax, QWORD PTR _Mylast$[rsp]
  002d0	48 8b 10	 mov	 rdx, QWORD PTR [rax]
  002d3	48 8b 44 24 38	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  002d8	48 8b 08	 mov	 rcx, QWORD PTR [rax]
  002db	e8 00 00 00 00	 call	 ??$_Uninitialized_move@PEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@YAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@0@@Z ; std::_Uninitialized_move<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > *,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >
  002e0	90		 npad	 1

; 906  :             } else {
; 907  :                 _STD _Uninitialized_copy(_Myfirst, _Mylast, _Newvec, _Al);
; 908  :             }
; 909  :         } else { // provide basic guarantee

  002e1	eb 5c		 jmp	 SHORT $LN4@Emplace_re
$LN3@Emplace_re:

; 910  :             _STD _Uninitialized_move(_Myfirst, _Whereptr, _Newvec, _Al);

  002e3	4c 8b 4c 24 28	 mov	 r9, QWORD PTR _Al$[rsp]
  002e8	4c 8b 44 24 20	 mov	 r8, QWORD PTR _Newvec$[rsp]
  002ed	48 8b 94 24 58
	01 00 00	 mov	 rdx, QWORD PTR _Whereptr$[rsp]
  002f5	48 8b 44 24 38	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  002fa	48 8b 08	 mov	 rcx, QWORD PTR [rax]
  002fd	e8 00 00 00 00	 call	 ??$_Uninitialized_move@PEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@YAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@0@@Z ; std::_Uninitialized_move<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > *,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >

; 911  :             _Constructed_first = _Newvec;

  00302	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _Constructed_first$[rsp]
  0030a	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Newvec$[rsp]
  0030f	48 89 08	 mov	 QWORD PTR [rax], rcx

; 912  :             _STD _Uninitialized_move(_Whereptr, _Mylast, _Newvec + _Whereoff + 1, _Al);

  00312	48 8b 44 24 20	 mov	 rax, QWORD PTR _Newvec$[rsp]
  00317	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Whereoff$[rsp]
  0031c	48 8d 44 c8 08	 lea	 rax, QWORD PTR [rax+rcx*8+8]
  00321	4c 8b 4c 24 28	 mov	 r9, QWORD PTR _Al$[rsp]
  00326	4c 8b c0	 mov	 r8, rax
  00329	48 8b 44 24 40	 mov	 rax, QWORD PTR _Mylast$[rsp]
  0032e	48 8b 10	 mov	 rdx, QWORD PTR [rax]
  00331	48 8b 8c 24 58
	01 00 00	 mov	 rcx, QWORD PTR _Whereptr$[rsp]
  00339	e8 00 00 00 00	 call	 ??$_Uninitialized_move@PEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@YAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@0@QEAV10@0PEAV10@AEAV?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@0@@Z ; std::_Uninitialized_move<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > *,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >
  0033e	90		 npad	 1
$LN4@Emplace_re:

; 913  :         }
; 914  : 
; 915  :         _Guard._New_begin = nullptr;

  0033f	48 c7 84 24 10
	01 00 00 00 00
	00 00		 mov	 QWORD PTR _Guard$[rsp+8], 0

; 916  :         _Change_array(_Newvec, _Newsize, _Newcapacity);

  0034b	4c 8b 4c 24 48	 mov	 r9, QWORD PTR _Newcapacity$[rsp]
  00350	4c 8b 84 24 88
	00 00 00	 mov	 r8, QWORD PTR _Newsize$[rsp]
  00358	48 8b 54 24 20	 mov	 rdx, QWORD PTR _Newvec$[rsp]
  0035d	48 8b 8c 24 50
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00365	e8 00 00 00 00	 call	 ?_Change_array@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAXQEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@2@_K1@Z ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Change_array
  0036a	90		 npad	 1

; 917  :         return _Newvec + _Whereoff;

  0036b	48 8b 44 24 20	 mov	 rax, QWORD PTR _Newvec$[rsp]
  00370	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Whereoff$[rsp]
  00375	48 8d 04 c8	 lea	 rax, QWORD PTR [rax+rcx*8]
  00379	48 89 84 24 00
	01 00 00	 mov	 QWORD PTR $T17[rsp], rax
  00381	48 8d 8c 24 08
	01 00 00	 lea	 rcx, QWORD PTR _Guard$[rsp]
  00389	e8 00 00 00 00	 call	 ??1_Reallocation_guard@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@QEAA@XZ ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Reallocation_guard::~_Reallocation_guard
  0038e	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR $T17[rsp]

; 918  :     }

  00396	48 81 c4 48 01
	00 00		 add	 rsp, 328		; 00000148H
  0039d	c3		 ret	 0
$LN680@Emplace_re:
??$_Emplace_reallocate@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z ENDP ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Emplace_reallocate<std::unique_ptr<mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt> > >
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
_Newvec$ = 32
_Al$ = 40
_Whereoff$ = 48
_Myfirst$ = 56
_Mylast$ = 64
_Newcapacity$ = 72
_My_data$ = 80
$T1 = 88
$T2 = 96
tv163 = 104
_Oldsize$ = 112
_Constructed_last$ = 120
_Constructed_first$ = 128
_Newsize$ = 136
$T3 = 144
$T4 = 152
$T5 = 160
$T6 = 168
$T7 = 176
$T8 = 184
$T9 = 192
$T10 = 200
$T11 = 208
$T12 = 216
$T13 = 224
$T14 = 232
$T15 = 240
$T16 = 248
$T17 = 256
_Guard$ = 264
$T18 = 304
_Unsigned_max$19 = 312
this$ = 336
_Whereptr$ = 344
<_Val_0>$ = 352
?dtor$0@?0???$_Emplace_reallocate@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z@4HA PROC ; `std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Emplace_reallocate<std::unique_ptr<mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt> > >'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8d 8d 08 01
	00 00		 lea	 rcx, QWORD PTR _Guard$[rbp]
  00010	e8 00 00 00 00	 call	 ??1_Reallocation_guard@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@QEAA@XZ ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Reallocation_guard::~_Reallocation_guard
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$0@?0???$_Emplace_reallocate@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z@4HA ENDP ; `std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Emplace_reallocate<std::unique_ptr<mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt> > >'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ??$_Emplace_back_with_unused_capacity@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAAEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z
_TEXT	SEGMENT
_Mylast$ = 32
_My_data$ = 40
_Obj$ = 48
$T1 = 56
$T2 = 64
$T3 = 72
$T4 = 80
$T5 = 88
_Result$ = 96
this$ = 128
<_Val_0>$ = 136
??$_Emplace_back_with_unused_capacity@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAAEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z PROC ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Emplace_back_with_unused_capacity<std::unique_ptr<mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt> > >, COMDAT

; 852  :     _CONSTEXPR20 _Ty& _Emplace_back_with_unused_capacity(_Valty&&... _Val) {

$LN80:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 78	 sub	 rsp, 120		; 00000078H

; 853  :         // insert by perfectly forwarding into element at end, provide strong guarantee
; 854  :         auto& _My_data   = _Mypair._Myval2;

  0000e	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00016	48 89 44 24 28	 mov	 QWORD PTR _My_data$[rsp], rax

; 855  :         pointer& _Mylast = _My_data._Mylast;

  0001b	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00020	48 83 c0 08	 add	 rax, 8
  00024	48 89 44 24 20	 mov	 QWORD PTR _Mylast$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00029	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR <_Val_0>$[rsp]
  00031	48 89 44 24 48	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 860  :             _STD _Construct_in_place(*_Mylast, _STD forward<_Valty>(_Val)...);

  00036	48 8b 44 24 20	 mov	 rax, QWORD PTR _Mylast$[rsp]
  0003b	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0003e	48 89 44 24 30	 mov	 QWORD PTR _Obj$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00043	48 8b 44 24 30	 mov	 rax, QWORD PTR _Obj$[rsp]
  00048	48 89 44 24 38	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0004d	48 8b 44 24 38	 mov	 rax, QWORD PTR $T1[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  00052	48 89 44 24 40	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00057	48 8b 44 24 40	 mov	 rax, QWORD PTR $T2[rsp]
  0005c	48 89 44 24 58	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 860  :             _STD _Construct_in_place(*_Mylast, _STD forward<_Valty>(_Val)...);

  00061	48 8b 44 24 48	 mov	 rax, QWORD PTR $T3[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00066	48 89 44 24 50	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0006b	48 8b 44 24 50	 mov	 rax, QWORD PTR $T4[rsp]
  00070	48 8b d0	 mov	 rdx, rax
  00073	48 8b 4c 24 58	 mov	 rcx, QWORD PTR $T5[rsp]
  00078	e8 00 00 00 00	 call	 ??$?0VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@$0A@@?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@QEAA@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z ; std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> ><mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt>,0>
  0007d	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 868  :         _Ty& _Result = *_Mylast;

  0007e	48 8b 44 24 20	 mov	 rax, QWORD PTR _Mylast$[rsp]
  00083	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00086	48 89 44 24 60	 mov	 QWORD PTR _Result$[rsp], rax

; 869  :         ++_Mylast;

  0008b	48 8b 44 24 20	 mov	 rax, QWORD PTR _Mylast$[rsp]
  00090	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00093	48 83 c0 08	 add	 rax, 8
  00097	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Mylast$[rsp]
  0009c	48 89 01	 mov	 QWORD PTR [rcx], rax

; 870  : 
; 871  :         return _Result;

  0009f	48 8b 44 24 60	 mov	 rax, QWORD PTR _Result$[rsp]

; 872  :     }

  000a4	48 83 c4 78	 add	 rsp, 120		; 00000078H
  000a8	c3		 ret	 0
??$_Emplace_back_with_unused_capacity@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAAEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z ENDP ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Emplace_back_with_unused_capacity<std::unique_ptr<mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt> > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ??$_Emplace_one_at_back@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAAEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z
_TEXT	SEGMENT
_My_data$ = 32
_Mylast$ = 40
$T1 = 48
$T2 = 56
this$ = 80
<_Val_0>$ = 88
??$_Emplace_one_at_back@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAAEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z PROC ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Emplace_one_at_back<std::unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> > >, COMDAT

; 839  :     _CONSTEXPR20 _Ty& _Emplace_one_at_back(_Valty&&... _Val) {

$LN662:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 840  :         // insert by perfectly forwarding into element at end, provide strong guarantee
; 841  :         auto& _My_data   = _Mypair._Myval2;

  0000e	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00013	48 89 44 24 20	 mov	 QWORD PTR _My_data$[rsp], rax

; 842  :         pointer& _Mylast = _My_data._Mylast;

  00018	48 8b 44 24 20	 mov	 rax, QWORD PTR _My_data$[rsp]
  0001d	48 83 c0 08	 add	 rax, 8
  00021	48 89 44 24 28	 mov	 QWORD PTR _Mylast$[rsp], rax

; 843  : 
; 844  :         if (_Mylast != _My_data._Myend) {

  00026	48 8b 44 24 28	 mov	 rax, QWORD PTR _Mylast$[rsp]
  0002b	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _My_data$[rsp]
  00030	48 8b 49 10	 mov	 rcx, QWORD PTR [rcx+16]
  00034	48 39 08	 cmp	 QWORD PTR [rax], rcx
  00037	74 1e		 je	 SHORT $LN2@Emplace_on
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00039	48 8b 44 24 58	 mov	 rax, QWORD PTR <_Val_0>$[rsp]
  0003e	48 89 44 24 30	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 845  :             return _Emplace_back_with_unused_capacity(_STD forward<_Valty>(_Val)...);

  00043	48 8b 44 24 30	 mov	 rax, QWORD PTR $T1[rsp]
  00048	48 8b d0	 mov	 rdx, rax
  0004b	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  00050	e8 00 00 00 00	 call	 ??$_Emplace_back_with_unused_capacity@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAAEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Emplace_back_with_unused_capacity<std::unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> > >
  00055	eb 24		 jmp	 SHORT $LN1@Emplace_on
$LN2@Emplace_on:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00057	48 8b 44 24 58	 mov	 rax, QWORD PTR <_Val_0>$[rsp]
  0005c	48 89 44 24 38	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 848  :         return *_Emplace_reallocate(_Mylast, _STD forward<_Valty>(_Val)...);

  00061	48 8b 44 24 38	 mov	 rax, QWORD PTR $T2[rsp]
  00066	4c 8b c0	 mov	 r8, rax
  00069	48 8b 44 24 28	 mov	 rax, QWORD PTR _Mylast$[rsp]
  0006e	48 8b 10	 mov	 rdx, QWORD PTR [rax]
  00071	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  00076	e8 00 00 00 00	 call	 ??$_Emplace_reallocate@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Emplace_reallocate<std::unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> > >
$LN1@Emplace_on:

; 849  :     }

  0007b	48 83 c4 48	 add	 rsp, 72			; 00000048H
  0007f	c3		 ret	 0
??$_Emplace_one_at_back@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAAEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z ENDP ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Emplace_one_at_back<std::unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ??$_Emplace_one_at_back@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAAEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z
_TEXT	SEGMENT
_My_data$ = 32
_Mylast$ = 40
$T1 = 48
$T2 = 56
this$ = 80
<_Val_0>$ = 88
??$_Emplace_one_at_back@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAAEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z PROC ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Emplace_one_at_back<std::unique_ptr<mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt> > >, COMDAT

; 839  :     _CONSTEXPR20 _Ty& _Emplace_one_at_back(_Valty&&... _Val) {

$LN662:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 840  :         // insert by perfectly forwarding into element at end, provide strong guarantee
; 841  :         auto& _My_data   = _Mypair._Myval2;

  0000e	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00013	48 89 44 24 20	 mov	 QWORD PTR _My_data$[rsp], rax

; 842  :         pointer& _Mylast = _My_data._Mylast;

  00018	48 8b 44 24 20	 mov	 rax, QWORD PTR _My_data$[rsp]
  0001d	48 83 c0 08	 add	 rax, 8
  00021	48 89 44 24 28	 mov	 QWORD PTR _Mylast$[rsp], rax

; 843  : 
; 844  :         if (_Mylast != _My_data._Myend) {

  00026	48 8b 44 24 28	 mov	 rax, QWORD PTR _Mylast$[rsp]
  0002b	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _My_data$[rsp]
  00030	48 8b 49 10	 mov	 rcx, QWORD PTR [rcx+16]
  00034	48 39 08	 cmp	 QWORD PTR [rax], rcx
  00037	74 1e		 je	 SHORT $LN2@Emplace_on
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00039	48 8b 44 24 58	 mov	 rax, QWORD PTR <_Val_0>$[rsp]
  0003e	48 89 44 24 30	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 845  :             return _Emplace_back_with_unused_capacity(_STD forward<_Valty>(_Val)...);

  00043	48 8b 44 24 30	 mov	 rax, QWORD PTR $T1[rsp]
  00048	48 8b d0	 mov	 rdx, rax
  0004b	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  00050	e8 00 00 00 00	 call	 ??$_Emplace_back_with_unused_capacity@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAAEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Emplace_back_with_unused_capacity<std::unique_ptr<mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt> > >
  00055	eb 24		 jmp	 SHORT $LN1@Emplace_on
$LN2@Emplace_on:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00057	48 8b 44 24 58	 mov	 rax, QWORD PTR <_Val_0>$[rsp]
  0005c	48 89 44 24 38	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 848  :         return *_Emplace_reallocate(_Mylast, _STD forward<_Valty>(_Val)...);

  00061	48 8b 44 24 38	 mov	 rax, QWORD PTR $T2[rsp]
  00066	4c 8b c0	 mov	 r8, rax
  00069	48 8b 44 24 28	 mov	 rax, QWORD PTR _Mylast$[rsp]
  0006e	48 8b 10	 mov	 rdx, QWORD PTR [rax]
  00071	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  00076	e8 00 00 00 00	 call	 ??$_Emplace_reallocate@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@QEAV21@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Emplace_reallocate<std::unique_ptr<mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt> > >
$LN1@Emplace_on:

; 849  :     }

  0007b	48 83 c4 48	 add	 rsp, 72			; 00000048H
  0007f	c3		 ret	 0
??$_Emplace_one_at_back@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAAEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z ENDP ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Emplace_one_at_back<std::unique_ptr<mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt> > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ??$?0U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@$0A@@?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@QEAA@$$QEAV01@@Z
_TEXT	SEGMENT
_Val$ = 0
$T1 = 8
_Old_val$2 = 16
$T3 = 24
$T4 = 32
$T5 = 40
$T6 = 48
$T7 = 56
this$ = 64
$T8 = 72
$T9 = 80
this$ = 112
_Right$ = 120
??$?0U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@$0A@@?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@QEAA@$$QEAV01@@Z PROC ; std::unique_ptr<mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt> >::unique_ptr<mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt> ><std::default_delete<mu2::OhrdorTreasureHunt>,0>, COMDAT

; 3386 :         : _Mypair(_One_then_variadic_args_t{}, _STD forward<_Dx>(_Right.get_deleter()), _Right.release()) {}

$LN44:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 68	 sub	 rsp, 104		; 00000068H

; 3465 :         return _STD exchange(_Mypair._Myval2, nullptr);

  0000e	48 c7 44 24 08
	00 00 00 00	 mov	 QWORD PTR $T1[rsp], 0
  00017	48 8b 44 24 78	 mov	 rax, QWORD PTR _Right$[rsp]
  0001c	48 89 04 24	 mov	 QWORD PTR _Val$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 773  :     _Ty _Old_val = static_cast<_Ty&&>(_Val);

  00020	48 8b 04 24	 mov	 rax, QWORD PTR _Val$[rsp]
  00024	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00027	48 89 44 24 10	 mov	 QWORD PTR _Old_val$2[rsp], rax

; 774  :     _Val         = static_cast<_Other&&>(_New_val);

  0002c	48 8b 04 24	 mov	 rax, QWORD PTR _Val$[rsp]
  00030	48 8b 4c 24 08	 mov	 rcx, QWORD PTR $T1[rsp]
  00035	48 89 08	 mov	 QWORD PTR [rax], rcx

; 775  :     return _Old_val;

  00038	48 8b 44 24 10	 mov	 rax, QWORD PTR _Old_val$2[rsp]
  0003d	48 89 44 24 18	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3465 :         return _STD exchange(_Mypair._Myval2, nullptr);

  00042	48 8b 44 24 18	 mov	 rax, QWORD PTR $T3[rsp]
  00047	48 89 44 24 20	 mov	 QWORD PTR $T4[rsp], rax

; 3386 :         : _Mypair(_One_then_variadic_args_t{}, _STD forward<_Dx>(_Right.get_deleter()), _Right.release()) {}

  0004c	48 8b 44 24 20	 mov	 rax, QWORD PTR $T4[rsp]
  00051	48 89 44 24 38	 mov	 QWORD PTR $T7[rsp], rax

; 3441 :         return _Mypair._Get_first();

  00056	48 8b 44 24 78	 mov	 rax, QWORD PTR _Right$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  0005b	48 89 44 24 28	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3441 :         return _Mypair._Get_first();

  00060	48 8b 44 24 28	 mov	 rax, QWORD PTR $T5[rsp]
  00065	48 89 44 24 30	 mov	 QWORD PTR $T6[rsp], rax

; 3386 :         : _Mypair(_One_then_variadic_args_t{}, _STD forward<_Dx>(_Right.get_deleter()), _Right.release()) {}

  0006a	48 8b 44 24 30	 mov	 rax, QWORD PTR $T6[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  0006f	48 89 44 24 50	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3386 :         : _Mypair(_One_then_variadic_args_t{}, _STD forward<_Dx>(_Right.get_deleter()), _Right.release()) {}

  00074	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  00079	48 89 44 24 40	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  0007e	48 8d 44 24 38	 lea	 rax, QWORD PTR $T7[rsp]
  00083	48 89 44 24 48	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1536 :         : _Ty1(_STD forward<_Other1>(_Val1)), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  00088	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0008d	48 8b 4c 24 48	 mov	 rcx, QWORD PTR $T8[rsp]
  00092	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00095	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3386 :         : _Mypair(_One_then_variadic_args_t{}, _STD forward<_Dx>(_Right.get_deleter()), _Right.release()) {}

  00098	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0009d	48 83 c4 68	 add	 rsp, 104		; 00000068H
  000a1	c3		 ret	 0
??$?0U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@$0A@@?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@QEAA@$$QEAV01@@Z ENDP ; std::unique_ptr<mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt> >::unique_ptr<mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt> ><std::default_delete<mu2::OhrdorTreasureHunt>,0>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ??1?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@QEAA@XZ
_TEXT	SEGMENT
$T1 = 32
_Ptr$ = 40
tv83 = 48
$T2 = 56
this$ = 80
??1?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@QEAA@XZ PROC ; std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >::~unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >, COMDAT

; 3425 :     _CONSTEXPR23 ~unique_ptr() noexcept {

$LN17:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 3426 :         if (_Mypair._Myval2) {

  00009	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 38 00	 cmp	 QWORD PTR [rax], 0
  00012	74 4d		 je	 SHORT $LN2@unique_ptr

; 3427 :             _Mypair._Get_first()(_Mypair._Myval2);

  00014	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00019	48 89 44 24 38	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3427 :             _Mypair._Get_first()(_Mypair._Myval2);

  0001e	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00026	48 89 44 24 28	 mov	 QWORD PTR _Ptr$[rsp], rax

; 3309 :         delete _Ptr;

  0002b	48 8b 44 24 28	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00030	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
  00035	48 83 7c 24 20
	00		 cmp	 QWORD PTR $T1[rsp], 0
  0003b	74 1b		 je	 SHORT $LN12@unique_ptr
  0003d	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00042	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00045	ba 01 00 00 00	 mov	 edx, 1
  0004a	48 8b 4c 24 20	 mov	 rcx, QWORD PTR $T1[rsp]
  0004f	ff 10		 call	 QWORD PTR [rax]
  00051	48 89 44 24 30	 mov	 QWORD PTR tv83[rsp], rax
  00056	eb 09		 jmp	 SHORT $LN2@unique_ptr
$LN12@unique_ptr:
  00058	48 c7 44 24 30
	00 00 00 00	 mov	 QWORD PTR tv83[rsp], 0
$LN2@unique_ptr:

; 3428 :         }
; 3429 : 
; 3430 : #if _MSVC_STL_DESTRUCTOR_TOMBSTONES
; 3431 :         if constexpr (is_pointer_v<pointer>) {
; 3432 :             if (!_STD _Is_constant_evaluated()) {
; 3433 :                 const auto _Tombstone{reinterpret_cast<pointer>(_MSVC_STL_UINTPTR_TOMBSTONE_VALUE)};
; 3434 :                 _Mypair._Myval2 = _Tombstone;
; 3435 :             }
; 3436 :         }
; 3437 : #endif // _MSVC_STL_DESTRUCTOR_TOMBSTONES
; 3438 :     }

  00061	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00065	c3		 ret	 0
??1?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@QEAA@XZ ENDP ; std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >::~unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ??$?0U?$default_delete@VPVPFieldSchedule@mu2@@@std@@$0A@@?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@QEAA@$$QEAV01@@Z
_TEXT	SEGMENT
_Val$ = 0
$T1 = 8
_Old_val$2 = 16
$T3 = 24
$T4 = 32
$T5 = 40
$T6 = 48
$T7 = 56
this$ = 64
$T8 = 72
$T9 = 80
this$ = 112
_Right$ = 120
??$?0U?$default_delete@VPVPFieldSchedule@mu2@@@std@@$0A@@?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@QEAA@$$QEAV01@@Z PROC ; std::unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> >::unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> ><std::default_delete<mu2::PVPFieldSchedule>,0>, COMDAT

; 3386 :         : _Mypair(_One_then_variadic_args_t{}, _STD forward<_Dx>(_Right.get_deleter()), _Right.release()) {}

$LN44:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 68	 sub	 rsp, 104		; 00000068H

; 3465 :         return _STD exchange(_Mypair._Myval2, nullptr);

  0000e	48 c7 44 24 08
	00 00 00 00	 mov	 QWORD PTR $T1[rsp], 0
  00017	48 8b 44 24 78	 mov	 rax, QWORD PTR _Right$[rsp]
  0001c	48 89 04 24	 mov	 QWORD PTR _Val$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 773  :     _Ty _Old_val = static_cast<_Ty&&>(_Val);

  00020	48 8b 04 24	 mov	 rax, QWORD PTR _Val$[rsp]
  00024	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00027	48 89 44 24 10	 mov	 QWORD PTR _Old_val$2[rsp], rax

; 774  :     _Val         = static_cast<_Other&&>(_New_val);

  0002c	48 8b 04 24	 mov	 rax, QWORD PTR _Val$[rsp]
  00030	48 8b 4c 24 08	 mov	 rcx, QWORD PTR $T1[rsp]
  00035	48 89 08	 mov	 QWORD PTR [rax], rcx

; 775  :     return _Old_val;

  00038	48 8b 44 24 10	 mov	 rax, QWORD PTR _Old_val$2[rsp]
  0003d	48 89 44 24 18	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3465 :         return _STD exchange(_Mypair._Myval2, nullptr);

  00042	48 8b 44 24 18	 mov	 rax, QWORD PTR $T3[rsp]
  00047	48 89 44 24 20	 mov	 QWORD PTR $T4[rsp], rax

; 3386 :         : _Mypair(_One_then_variadic_args_t{}, _STD forward<_Dx>(_Right.get_deleter()), _Right.release()) {}

  0004c	48 8b 44 24 20	 mov	 rax, QWORD PTR $T4[rsp]
  00051	48 89 44 24 38	 mov	 QWORD PTR $T7[rsp], rax

; 3441 :         return _Mypair._Get_first();

  00056	48 8b 44 24 78	 mov	 rax, QWORD PTR _Right$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  0005b	48 89 44 24 28	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3441 :         return _Mypair._Get_first();

  00060	48 8b 44 24 28	 mov	 rax, QWORD PTR $T5[rsp]
  00065	48 89 44 24 30	 mov	 QWORD PTR $T6[rsp], rax

; 3386 :         : _Mypair(_One_then_variadic_args_t{}, _STD forward<_Dx>(_Right.get_deleter()), _Right.release()) {}

  0006a	48 8b 44 24 30	 mov	 rax, QWORD PTR $T6[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  0006f	48 89 44 24 50	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3386 :         : _Mypair(_One_then_variadic_args_t{}, _STD forward<_Dx>(_Right.get_deleter()), _Right.release()) {}

  00074	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  00079	48 89 44 24 40	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  0007e	48 8d 44 24 38	 lea	 rax, QWORD PTR $T7[rsp]
  00083	48 89 44 24 48	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1536 :         : _Ty1(_STD forward<_Other1>(_Val1)), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  00088	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0008d	48 8b 4c 24 48	 mov	 rcx, QWORD PTR $T8[rsp]
  00092	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00095	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3386 :         : _Mypair(_One_then_variadic_args_t{}, _STD forward<_Dx>(_Right.get_deleter()), _Right.release()) {}

  00098	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0009d	48 83 c4 68	 add	 rsp, 104		; 00000068H
  000a1	c3		 ret	 0
??$?0U?$default_delete@VPVPFieldSchedule@mu2@@@std@@$0A@@?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@QEAA@$$QEAV01@@Z ENDP ; std::unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> >::unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> ><std::default_delete<mu2::PVPFieldSchedule>,0>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ??$make_unique@VPVPFieldSchedule@mu2@@PEBUPVPFieldInfoElem@2@$0A@@std@@YA?AV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@0@$$QEAPEBUPVPFieldInfoElem@mu2@@@Z
_TEXT	SEGMENT
$T1 = 32
$T2 = 40
tv82 = 48
$T3 = 56
$T4 = 64
$T5 = 72
_Ptr$ = 80
this$ = 88
$T6 = 96
$T7 = 104
tv90 = 112
__$ReturnUdt$ = 144
<_Args_0>$ = 152
??$make_unique@VPVPFieldSchedule@mu2@@PEBUPVPFieldInfoElem@2@$0A@@std@@YA?AV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@0@$$QEAPEBUPVPFieldInfoElem@mu2@@@Z PROC ; std::make_unique<mu2::PVPFieldSchedule,mu2::PVPFieldInfoElem const *,0>, COMDAT

; 3629 : _NODISCARD_SMART_PTR_ALLOC _CONSTEXPR23 unique_ptr<_Ty> make_unique(_Types&&... _Args) { // make a unique_ptr

$LN92:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H
  00011	c7 44 24 20 00
	00 00 00	 mov	 DWORD PTR $T1[rsp], 0

; 3630 :     return unique_ptr<_Ty>(new _Ty(_STD forward<_Types>(_Args)...));

  00019	b9 40 01 00 00	 mov	 ecx, 320		; 00000140H
  0001e	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00023	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax
  00028	48 83 7c 24 28
	00		 cmp	 QWORD PTR $T2[rsp], 0
  0002e	74 26		 je	 SHORT $LN3@make_uniqu
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00030	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR <_Args_0>$[rsp]
  00038	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3630 :     return unique_ptr<_Ty>(new _Ty(_STD forward<_Types>(_Args)...));

  0003d	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  00042	48 8b 10	 mov	 rdx, QWORD PTR [rax]
  00045	48 8b 4c 24 28	 mov	 rcx, QWORD PTR $T2[rsp]
  0004a	e8 00 00 00 00	 call	 ??0PVPFieldSchedule@mu2@@QEAA@PEBUPVPFieldInfoElem@1@@Z ; mu2::PVPFieldSchedule::PVPFieldSchedule
  0004f	48 89 44 24 30	 mov	 QWORD PTR tv82[rsp], rax
  00054	eb 09		 jmp	 SHORT $LN4@make_uniqu
$LN3@make_uniqu:
  00056	48 c7 44 24 30
	00 00 00 00	 mov	 QWORD PTR tv82[rsp], 0
$LN4@make_uniqu:
  0005f	48 8b 44 24 30	 mov	 rax, QWORD PTR tv82[rsp]
  00064	48 89 44 24 48	 mov	 QWORD PTR $T5[rsp], rax
  00069	48 8b 44 24 48	 mov	 rax, QWORD PTR $T5[rsp]
  0006e	48 89 44 24 50	 mov	 QWORD PTR _Ptr$[rsp], rax

; 3370 :     _CONSTEXPR23 explicit unique_ptr(pointer _Ptr) noexcept : _Mypair(_Zero_then_variadic_args_t{}, _Ptr) {}

  00073	48 8d 44 24 38	 lea	 rax, QWORD PTR $T3[rsp]
  00078	48 89 44 24 58	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  0007d	48 8d 44 24 50	 lea	 rax, QWORD PTR _Ptr$[rsp]
  00082	48 89 44 24 60	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1531 :         : _Ty1(), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  00087	48 8b 44 24 58	 mov	 rax, QWORD PTR this$[rsp]
  0008c	48 8b 4c 24 60	 mov	 rcx, QWORD PTR $T6[rsp]
  00091	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00094	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3370 :     _CONSTEXPR23 explicit unique_ptr(pointer _Ptr) noexcept : _Mypair(_Zero_then_variadic_args_t{}, _Ptr) {}

  00097	48 8d 44 24 38	 lea	 rax, QWORD PTR $T3[rsp]
  0009c	48 89 44 24 68	 mov	 QWORD PTR $T7[rsp], rax

; 3630 :     return unique_ptr<_Ty>(new _Ty(_STD forward<_Types>(_Args)...));

  000a1	48 8b 44 24 68	 mov	 rax, QWORD PTR $T7[rsp]
  000a6	48 89 44 24 70	 mov	 QWORD PTR tv90[rsp], rax
  000ab	48 8b 54 24 70	 mov	 rdx, QWORD PTR tv90[rsp]
  000b0	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR __$ReturnUdt$[rsp]
  000b8	e8 00 00 00 00	 call	 ??$?0U?$default_delete@VPVPFieldSchedule@mu2@@@std@@$0A@@?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@QEAA@$$QEAV01@@Z ; std::unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> >::unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> ><std::default_delete<mu2::PVPFieldSchedule>,0>
  000bd	8b 44 24 20	 mov	 eax, DWORD PTR $T1[rsp]
  000c1	83 c8 01	 or	 eax, 1
  000c4	89 44 24 20	 mov	 DWORD PTR $T1[rsp], eax
  000c8	48 8d 4c 24 38	 lea	 rcx, QWORD PTR $T3[rsp]
  000cd	e8 00 00 00 00	 call	 ??1?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@QEAA@XZ ; std::unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> >::~unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> >
  000d2	90		 npad	 1
  000d3	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]

; 3631 : }

  000db	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  000e2	c3		 ret	 0
??$make_unique@VPVPFieldSchedule@mu2@@PEBUPVPFieldInfoElem@2@$0A@@std@@YA?AV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@0@$$QEAPEBUPVPFieldInfoElem@mu2@@@Z ENDP ; std::make_unique<mu2::PVPFieldSchedule,mu2::PVPFieldInfoElem const *,0>
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 40
tv82 = 48
$T3 = 56
$T4 = 64
$T5 = 72
_Ptr$ = 80
this$ = 88
$T6 = 96
$T7 = 104
tv90 = 112
__$ReturnUdt$ = 144
<_Args_0>$ = 152
?dtor$0@?0???$make_unique@VPVPFieldSchedule@mu2@@PEBUPVPFieldInfoElem@2@$0A@@std@@YA?AV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@0@$$QEAPEBUPVPFieldInfoElem@mu2@@@Z@4HA PROC ; `std::make_unique<mu2::PVPFieldSchedule,mu2::PVPFieldInfoElem const *,0>'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	ba 40 01 00 00	 mov	 edx, 320		; 00000140H
  0000e	48 8b 4d 28	 mov	 rcx, QWORD PTR $T2[rbp]
  00012	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00017	48 83 c4 20	 add	 rsp, 32			; 00000020H
  0001b	5d		 pop	 rbp
  0001c	c3		 ret	 0
?dtor$0@?0???$make_unique@VPVPFieldSchedule@mu2@@PEBUPVPFieldInfoElem@2@$0A@@std@@YA?AV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@0@$$QEAPEBUPVPFieldInfoElem@mu2@@@Z@4HA ENDP ; `std::make_unique<mu2::PVPFieldSchedule,mu2::PVPFieldInfoElem const *,0>'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ??1?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@QEAA@XZ
_TEXT	SEGMENT
$T1 = 32
_Ptr$ = 40
tv87 = 48
$T2 = 56
this$ = 80
??1?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@QEAA@XZ PROC ; std::unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> >::~unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> >, COMDAT

; 3425 :     _CONSTEXPR23 ~unique_ptr() noexcept {

$LN17:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 3426 :         if (_Mypair._Myval2) {

  00009	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 38 00	 cmp	 QWORD PTR [rax], 0
  00012	74 4d		 je	 SHORT $LN2@unique_ptr

; 3427 :             _Mypair._Get_first()(_Mypair._Myval2);

  00014	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00019	48 89 44 24 38	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3427 :             _Mypair._Get_first()(_Mypair._Myval2);

  0001e	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00026	48 89 44 24 28	 mov	 QWORD PTR _Ptr$[rsp], rax

; 3309 :         delete _Ptr;

  0002b	48 8b 44 24 28	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00030	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
  00035	48 83 7c 24 20
	00		 cmp	 QWORD PTR $T1[rsp], 0
  0003b	74 1b		 je	 SHORT $LN12@unique_ptr
  0003d	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00042	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00045	ba 01 00 00 00	 mov	 edx, 1
  0004a	48 8b 4c 24 20	 mov	 rcx, QWORD PTR $T1[rsp]
  0004f	ff 10		 call	 QWORD PTR [rax]
  00051	48 89 44 24 30	 mov	 QWORD PTR tv87[rsp], rax
  00056	eb 09		 jmp	 SHORT $LN2@unique_ptr
$LN12@unique_ptr:
  00058	48 c7 44 24 30
	00 00 00 00	 mov	 QWORD PTR tv87[rsp], 0
$LN2@unique_ptr:

; 3428 :         }
; 3429 : 
; 3430 : #if _MSVC_STL_DESTRUCTOR_TOMBSTONES
; 3431 :         if constexpr (is_pointer_v<pointer>) {
; 3432 :             if (!_STD _Is_constant_evaluated()) {
; 3433 :                 const auto _Tombstone{reinterpret_cast<pointer>(_MSVC_STL_UINTPTR_TOMBSTONE_VALUE)};
; 3434 :                 _Mypair._Myval2 = _Tombstone;
; 3435 :             }
; 3436 :         }
; 3437 : #endif // _MSVC_STL_DESTRUCTOR_TOMBSTONES
; 3438 :     }

  00061	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00065	c3		 ret	 0
??1?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@QEAA@XZ ENDP ; std::unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> >::~unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBGUPVPFieldInfoElem@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ
_TEXT	SEGMENT
_Pnode$1 = 0
_Pnode$ = 8
$T2 = 16
this$ = 48
??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBGUPVPFieldInfoElem@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ PROC ; std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned short const ,mu2::PVPFieldInfoElem> > >,std::_Iterator_base0>::operator++, COMDAT

; 49   :     _Tree_unchecked_const_iterator& operator++() noexcept {

$LN15:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 50   :         if (_Ptr->_Right->_Isnil) { // climb looking for right subtree

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00011	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00015	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  00019	85 c0		 test	 eax, eax
  0001b	74 4a		 je	 SHORT $LN4@operator
$LN2@operator:

; 51   :             _Nodeptr _Pnode;
; 52   :             while (!(_Pnode = _Ptr->_Parent)->_Isnil && _Ptr == _Pnode->_Right) {

  0001d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00022	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00025	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00029	48 89 04 24	 mov	 QWORD PTR _Pnode$1[rsp], rax
  0002d	48 8b 04 24	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  00031	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  00035	85 c0		 test	 eax, eax
  00037	75 20		 jne	 SHORT $LN3@operator
  00039	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003e	48 8b 0c 24	 mov	 rcx, QWORD PTR _Pnode$1[rsp]
  00042	48 8b 49 10	 mov	 rcx, QWORD PTR [rcx+16]
  00046	48 39 08	 cmp	 QWORD PTR [rax], rcx
  00049	75 0e		 jne	 SHORT $LN3@operator

; 53   :                 _Ptr = _Pnode; // ==> parent while right subtree

  0004b	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00050	48 8b 0c 24	 mov	 rcx, QWORD PTR _Pnode$1[rsp]
  00054	48 89 08	 mov	 QWORD PTR [rax], rcx

; 54   :             }

  00057	eb c4		 jmp	 SHORT $LN2@operator
$LN3@operator:

; 55   : 
; 56   :             _Ptr = _Pnode; // ==> parent (head if end())

  00059	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0005e	48 8b 0c 24	 mov	 rcx, QWORD PTR _Pnode$1[rsp]
  00062	48 89 08	 mov	 QWORD PTR [rax], rcx

; 57   :         } else {

  00065	eb 47		 jmp	 SHORT $LN5@operator
$LN4@operator:

; 58   :             _Ptr = _Mytree::_Min(_Ptr->_Right); // ==> smallest of right subtree

  00067	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0006c	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0006f	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00073	48 89 44 24 08	 mov	 QWORD PTR _Pnode$[rsp], rax
$LN9@operator:

; 476  :         while (!_Pnode->_Left->_Isnil) {

  00078	48 8b 44 24 08	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0007d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00080	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  00084	85 c0		 test	 eax, eax
  00086	75 0f		 jne	 SHORT $LN10@operator

; 477  :             _Pnode = _Pnode->_Left;

  00088	48 8b 44 24 08	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0008d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00090	48 89 44 24 08	 mov	 QWORD PTR _Pnode$[rsp], rax

; 478  :         }

  00095	eb e1		 jmp	 SHORT $LN9@operator
$LN10@operator:

; 479  : 
; 480  :         return _Pnode;

  00097	48 8b 44 24 08	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0009c	48 89 44 24 10	 mov	 QWORD PTR $T2[rsp], rax

; 58   :             _Ptr = _Mytree::_Min(_Ptr->_Right); // ==> smallest of right subtree

  000a1	48 8b 44 24 10	 mov	 rax, QWORD PTR $T2[rsp]
  000a6	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  000ab	48 89 01	 mov	 QWORD PTR [rcx], rax
$LN5@operator:

; 59   :         }
; 60   : 
; 61   :         return *this;

  000ae	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]

; 62   :     }

  000b3	48 83 c4 28	 add	 rsp, 40			; 00000028H
  000b7	c3		 ret	 0
??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBGUPVPFieldInfoElem@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ ENDP ; std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned short const ,mu2::PVPFieldInfoElem> > >,std::_Iterator_base0>::operator++
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Shared\Scripts\GlobalScript.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Shared\Scripts\GlobalScript.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Shared\Scripts\GlobalScript.h
;	COMDAT ??$GetScript@VPVPFieldScript@mu2@@@GlobalLoadScript@mu2@@QEBAPEBVPVPFieldScript@1@XZ
_TEXT	SEGMENT
_My_data$1 = 32
$T2 = 40
_My_data$3 = 48
$T4 = 56
this$ = 80
??$GetScript@VPVPFieldScript@mu2@@@GlobalLoadScript@mu2@@QEBAPEBVPVPFieldScript@1@XZ PROC ; mu2::GlobalLoadScript::GetScript<mu2::PVPFieldScript>, COMDAT

; 917  : {

$LN14:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 48	 sub	 rsp, 72			; 00000048H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1914 :         auto& _My_data = _Mypair._Myval2;

  00009	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 c0 08	 add	 rax, 8
  00012	48 89 44 24 20	 mov	 QWORD PTR _My_data$1[rsp], rax

; 1915 :         return static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst);

  00017	48 8b 44 24 20	 mov	 rax, QWORD PTR _My_data$1[rsp]
  0001c	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _My_data$1[rsp]
  00021	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00024	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00028	48 2b c1	 sub	 rax, rcx
  0002b	48 c1 f8 03	 sar	 rax, 3
  0002f	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax
; File F:\Release_Branch\Shared\Scripts\GlobalScript.h

; 918  : 	MU2_ASSERT( static_cast<unsigned>( T::Type ) < m_scripts.size() );

  00034	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
  00039	48 3d 3a 01 00
	00		 cmp	 rax, 314		; 0000013aH
  0003f	77 21		 ja	 SHORT $LN2@GetScript
  00041	41 b9 96 03 00
	00		 mov	 r9d, 918		; 00000396H
  00047	4c 8d 05 00 00
	00 00		 lea	 r8, OFFSET FLAT:??_C@_0DA@FCBIDNOM@F?3?2Release_Branch?2Shared?2Script@
  0004e	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:??_C@_0DE@DKALFLNK@static_cast?$DMunsigned?$DO?$CI?5T?3?3Type?5@
  00055	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_09OLBBGJEO@Assertion@
  0005c	e8 00 00 00 00	 call	 ?LogRuntimeAssertionFailure@mu2@@YAXPEBD00_K@Z ; mu2::LogRuntimeAssertionFailure
  00061	90		 npad	 1
$LN2@GetScript:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1938 :         auto& _My_data = _Mypair._Myval2;

  00062	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00067	48 83 c0 08	 add	 rax, 8
  0006b	48 89 44 24 30	 mov	 QWORD PTR _My_data$3[rsp], rax

; 1939 : #if _MSVC_STL_HARDENING_VECTOR || _ITERATOR_DEBUG_LEVEL != 0
; 1940 :         _STL_VERIFY(
; 1941 :             _Pos < static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst), "vector subscript out of range");
; 1942 : #endif
; 1943 : 
; 1944 :         return _My_data._Myfirst[_Pos];

  00070	b8 3a 01 00 00	 mov	 eax, 314		; 0000013aH
  00075	48 6b c0 08	 imul	 rax, rax, 8
  00079	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _My_data$3[rsp]
  0007e	48 03 01	 add	 rax, QWORD PTR [rcx]
  00081	48 89 44 24 38	 mov	 QWORD PTR $T4[rsp], rax
; File F:\Release_Branch\Shared\Scripts\GlobalScript.h

; 924  : 	return (const T*) m_scripts[T::Type];

  00086	48 8b 44 24 38	 mov	 rax, QWORD PTR $T4[rsp]
  0008b	48 8b 00	 mov	 rax, QWORD PTR [rax]

; 925  : #endif // _DEBUG
; 926  : 
; 927  : }

  0008e	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00092	c3		 ret	 0
??$GetScript@VPVPFieldScript@mu2@@@GlobalLoadScript@mu2@@QEBAPEBVPVPFieldScript@1@XZ ENDP ; mu2::GlobalLoadScript::GetScript<mu2::PVPFieldScript>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ??1?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@QEAA@XZ
_TEXT	SEGMENT
$T1 = 32
_Ptr$ = 40
tv87 = 48
$T2 = 56
this$ = 80
??1?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@QEAA@XZ PROC ; std::unique_ptr<mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt> >::~unique_ptr<mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt> >, COMDAT

; 3425 :     _CONSTEXPR23 ~unique_ptr() noexcept {

$LN17:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 3426 :         if (_Mypair._Myval2) {

  00009	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 38 00	 cmp	 QWORD PTR [rax], 0
  00012	74 4d		 je	 SHORT $LN2@unique_ptr

; 3427 :             _Mypair._Get_first()(_Mypair._Myval2);

  00014	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00019	48 89 44 24 38	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3427 :             _Mypair._Get_first()(_Mypair._Myval2);

  0001e	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00026	48 89 44 24 28	 mov	 QWORD PTR _Ptr$[rsp], rax

; 3309 :         delete _Ptr;

  0002b	48 8b 44 24 28	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00030	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
  00035	48 83 7c 24 20
	00		 cmp	 QWORD PTR $T1[rsp], 0
  0003b	74 1b		 je	 SHORT $LN12@unique_ptr
  0003d	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00042	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00045	ba 01 00 00 00	 mov	 edx, 1
  0004a	48 8b 4c 24 20	 mov	 rcx, QWORD PTR $T1[rsp]
  0004f	ff 10		 call	 QWORD PTR [rax]
  00051	48 89 44 24 30	 mov	 QWORD PTR tv87[rsp], rax
  00056	eb 09		 jmp	 SHORT $LN2@unique_ptr
$LN12@unique_ptr:
  00058	48 c7 44 24 30
	00 00 00 00	 mov	 QWORD PTR tv87[rsp], 0
$LN2@unique_ptr:

; 3428 :         }
; 3429 : 
; 3430 : #if _MSVC_STL_DESTRUCTOR_TOMBSTONES
; 3431 :         if constexpr (is_pointer_v<pointer>) {
; 3432 :             if (!_STD _Is_constant_evaluated()) {
; 3433 :                 const auto _Tombstone{reinterpret_cast<pointer>(_MSVC_STL_UINTPTR_TOMBSTONE_VALUE)};
; 3434 :                 _Mypair._Myval2 = _Tombstone;
; 3435 :             }
; 3436 :         }
; 3437 : #endif // _MSVC_STL_DESTRUCTOR_TOMBSTONES
; 3438 :     }

  00061	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00065	c3		 ret	 0
??1?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@QEAA@XZ ENDP ; std::unique_ptr<mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt> >::~unique_ptr<mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ??$make_unique@VOhrdorTreasureHunt@mu2@@$$V$0A@@std@@YA?AV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@0@XZ
_TEXT	SEGMENT
$T1 = 32
$T2 = 40
tv79 = 48
$T3 = 56
$T4 = 64
_Ptr$ = 72
this$ = 80
$T5 = 88
$T6 = 96
tv87 = 104
__$ReturnUdt$ = 128
??$make_unique@VOhrdorTreasureHunt@mu2@@$$V$0A@@std@@YA?AV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@0@XZ PROC ; std::make_unique<mu2::OhrdorTreasureHunt,0>, COMDAT

; 3629 : _NODISCARD_SMART_PTR_ALLOC _CONSTEXPR23 unique_ptr<_Ty> make_unique(_Types&&... _Args) { // make a unique_ptr

$LN87:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 78	 sub	 rsp, 120		; 00000078H
  00009	c7 44 24 20 00
	00 00 00	 mov	 DWORD PTR $T1[rsp], 0

; 3630 :     return unique_ptr<_Ty>(new _Ty(_STD forward<_Types>(_Args)...));

  00011	b9 10 01 00 00	 mov	 ecx, 272		; 00000110H
  00016	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  0001b	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax
  00020	48 83 7c 24 28
	00		 cmp	 QWORD PTR $T2[rsp], 0
  00026	74 11		 je	 SHORT $LN3@make_uniqu
  00028	48 8b 4c 24 28	 mov	 rcx, QWORD PTR $T2[rsp]
  0002d	e8 00 00 00 00	 call	 ??0OhrdorTreasureHunt@mu2@@QEAA@XZ ; mu2::OhrdorTreasureHunt::OhrdorTreasureHunt
  00032	48 89 44 24 30	 mov	 QWORD PTR tv79[rsp], rax
  00037	eb 09		 jmp	 SHORT $LN4@make_uniqu
$LN3@make_uniqu:
  00039	48 c7 44 24 30
	00 00 00 00	 mov	 QWORD PTR tv79[rsp], 0
$LN4@make_uniqu:
  00042	48 8b 44 24 30	 mov	 rax, QWORD PTR tv79[rsp]
  00047	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax
  0004c	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  00051	48 89 44 24 48	 mov	 QWORD PTR _Ptr$[rsp], rax

; 3370 :     _CONSTEXPR23 explicit unique_ptr(pointer _Ptr) noexcept : _Mypair(_Zero_then_variadic_args_t{}, _Ptr) {}

  00056	48 8d 44 24 38	 lea	 rax, QWORD PTR $T3[rsp]
  0005b	48 89 44 24 50	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00060	48 8d 44 24 48	 lea	 rax, QWORD PTR _Ptr$[rsp]
  00065	48 89 44 24 58	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1531 :         : _Ty1(), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  0006a	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0006f	48 8b 4c 24 58	 mov	 rcx, QWORD PTR $T5[rsp]
  00074	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00077	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3370 :     _CONSTEXPR23 explicit unique_ptr(pointer _Ptr) noexcept : _Mypair(_Zero_then_variadic_args_t{}, _Ptr) {}

  0007a	48 8d 44 24 38	 lea	 rax, QWORD PTR $T3[rsp]
  0007f	48 89 44 24 60	 mov	 QWORD PTR $T6[rsp], rax

; 3630 :     return unique_ptr<_Ty>(new _Ty(_STD forward<_Types>(_Args)...));

  00084	48 8b 44 24 60	 mov	 rax, QWORD PTR $T6[rsp]
  00089	48 89 44 24 68	 mov	 QWORD PTR tv87[rsp], rax
  0008e	48 8b 54 24 68	 mov	 rdx, QWORD PTR tv87[rsp]
  00093	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR __$ReturnUdt$[rsp]
  0009b	e8 00 00 00 00	 call	 ??$?0U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@$0A@@?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@QEAA@$$QEAV01@@Z ; std::unique_ptr<mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt> >::unique_ptr<mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt> ><std::default_delete<mu2::OhrdorTreasureHunt>,0>
  000a0	8b 44 24 20	 mov	 eax, DWORD PTR $T1[rsp]
  000a4	83 c8 01	 or	 eax, 1
  000a7	89 44 24 20	 mov	 DWORD PTR $T1[rsp], eax
  000ab	48 8d 4c 24 38	 lea	 rcx, QWORD PTR $T3[rsp]
  000b0	e8 00 00 00 00	 call	 ??1?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@QEAA@XZ ; std::unique_ptr<mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt> >::~unique_ptr<mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt> >
  000b5	90		 npad	 1
  000b6	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]

; 3631 : }

  000be	48 83 c4 78	 add	 rsp, 120		; 00000078H
  000c2	c3		 ret	 0
??$make_unique@VOhrdorTreasureHunt@mu2@@$$V$0A@@std@@YA?AV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@0@XZ ENDP ; std::make_unique<mu2::OhrdorTreasureHunt,0>
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 40
tv79 = 48
$T3 = 56
$T4 = 64
_Ptr$ = 72
this$ = 80
$T5 = 88
$T6 = 96
tv87 = 104
__$ReturnUdt$ = 128
?dtor$0@?0???$make_unique@VOhrdorTreasureHunt@mu2@@$$V$0A@@std@@YA?AV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@0@XZ@4HA PROC ; `std::make_unique<mu2::OhrdorTreasureHunt,0>'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	ba 10 01 00 00	 mov	 edx, 272		; 00000110H
  0000e	48 8b 4d 28	 mov	 rcx, QWORD PTR $T2[rbp]
  00012	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00017	48 83 c4 20	 add	 rsp, 32			; 00000020H
  0001b	5d		 pop	 rbp
  0001c	c3		 ret	 0
?dtor$0@?0???$make_unique@VOhrdorTreasureHunt@mu2@@$$V$0A@@std@@YA?AV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@0@XZ@4HA ENDP ; `std::make_unique<mu2::OhrdorTreasureHunt,0>'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT ??_GEventScheduleManager@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GEventScheduleManager@mu2@@UEAAPEAXI@Z PROC		; mu2::EventScheduleManager::`scalar deleting destructor', COMDAT
$LN5:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00012	e8 00 00 00 00	 call	 ??1EventScheduleManager@mu2@@UEAA@XZ ; mu2::EventScheduleManager::~EventScheduleManager
  00017	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  0001b	83 e0 01	 and	 eax, 1
  0001e	85 c0		 test	 eax, eax
  00020	74 10		 je	 SHORT $LN2@scalar
  00022	ba 20 00 00 00	 mov	 edx, 32			; 00000020H
  00027	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0002c	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00031	90		 npad	 1
$LN2@scalar:
  00032	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00037	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0003b	c3		 ret	 0
??_GEventScheduleManager@mu2@@UEAAPEAXI@Z ENDP		; mu2::EventScheduleManager::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ?_Xlength@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@CAXXZ
_TEXT	SEGMENT
?_Xlength@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@CAXXZ PROC ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Xlength, COMDAT

; 2183 :     [[noreturn]] static void _Xlength() {

$LN3:
  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 2184 :         _Xlength_error("vector too long");

  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_0BA@FOIKENOD@vector?5too?5long@
  0000b	e8 00 00 00 00	 call	 ?_Xlength_error@std@@YAXPEBD@Z ; std::_Xlength_error
  00010	90		 npad	 1
$LN2@Xlength:

; 2185 :     }

  00011	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00015	c3		 ret	 0
?_Xlength@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@CAXXZ ENDP ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Xlength
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ?_Tidy@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAXXZ
_TEXT	SEGMENT
_Myfirst$ = 32
_First$ = 40
_My_data$ = 48
_Bytes$ = 56
_Ptr$ = 64
_Ptr$ = 72
_Mylast$ = 80
_Myend$ = 88
$T1 = 96
$T2 = 104
_Last$ = 112
$T3 = 120
_Count$ = 128
_Ptr$ = 136
_Al$ = 144
this$ = 176
?_Tidy@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAXXZ PROC ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Tidy, COMDAT

; 2081 :     _CONSTEXPR20 void _Tidy() noexcept { // free all storage

$LN74:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec a8 00
	00 00		 sub	 rsp, 168		; 000000a8H

; 2227 :         return _Mypair._Get_first();

  0000c	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00014	48 89 44 24 60	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2227 :         return _Mypair._Get_first();

  00019	48 8b 44 24 60	 mov	 rax, QWORD PTR $T1[rsp]
  0001e	48 89 44 24 68	 mov	 QWORD PTR $T2[rsp], rax

; 2082 :         auto& _Al         = _Getal();

  00023	48 8b 44 24 68	 mov	 rax, QWORD PTR $T2[rsp]
  00028	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR _Al$[rsp], rax

; 2083 :         auto& _My_data    = _Mypair._Myval2;

  00030	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 89 44 24 30	 mov	 QWORD PTR _My_data$[rsp], rax

; 2084 :         pointer& _Myfirst = _My_data._Myfirst;

  0003d	48 8b 44 24 30	 mov	 rax, QWORD PTR _My_data$[rsp]
  00042	48 89 44 24 20	 mov	 QWORD PTR _Myfirst$[rsp], rax

; 2085 :         pointer& _Mylast  = _My_data._Mylast;

  00047	48 8b 44 24 30	 mov	 rax, QWORD PTR _My_data$[rsp]
  0004c	48 83 c0 08	 add	 rax, 8
  00050	48 89 44 24 50	 mov	 QWORD PTR _Mylast$[rsp], rax

; 2086 :         pointer& _Myend   = _My_data._Myend;

  00055	48 8b 44 24 30	 mov	 rax, QWORD PTR _My_data$[rsp]
  0005a	48 83 c0 10	 add	 rax, 16
  0005e	48 89 44 24 58	 mov	 QWORD PTR _Myend$[rsp], rax

; 2087 : 
; 2088 :         _My_data._Orphan_all();
; 2089 : 
; 2090 :         if (_Myfirst) { // destroy and deallocate old array

  00063	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  00068	48 83 38 00	 cmp	 QWORD PTR [rax], 0
  0006c	0f 84 0b 01 00
	00		 je	 $LN2@Tidy

; 2091 :             _STD _Destroy_range(_Myfirst, _Mylast, _Al);

  00072	48 8b 44 24 50	 mov	 rax, QWORD PTR _Mylast$[rsp]
  00077	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0007a	48 89 44 24 70	 mov	 QWORD PTR _Last$[rsp], rax
  0007f	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  00084	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00087	48 89 44 24 28	 mov	 QWORD PTR _First$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1102 :         for (; _First != _Last; ++_First) {

  0008c	eb 0e		 jmp	 SHORT $LN23@Tidy
$LN21@Tidy:
  0008e	48 8b 44 24 28	 mov	 rax, QWORD PTR _First$[rsp]
  00093	48 83 c0 08	 add	 rax, 8
  00097	48 89 44 24 28	 mov	 QWORD PTR _First$[rsp], rax
$LN23@Tidy:
  0009c	48 8b 44 24 70	 mov	 rax, QWORD PTR _Last$[rsp]
  000a1	48 39 44 24 28	 cmp	 QWORD PTR _First$[rsp], rax
  000a6	74 39		 je	 SHORT $LN22@Tidy

; 69   :     return _Ptr;

  000a8	48 8b 44 24 28	 mov	 rax, QWORD PTR _First$[rsp]
  000ad	48 89 44 24 78	 mov	 QWORD PTR $T3[rsp], rax

; 1103 :             allocator_traits<_Alloc>::destroy(_Al, _STD _Unfancy(_First));

  000b2	48 8b 44 24 78	 mov	 rax, QWORD PTR $T3[rsp]
  000b7	48 89 44 24 40	 mov	 QWORD PTR _Ptr$[rsp], rax
  000bc	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  000c1	e8 00 00 00 00	 call	 ??1?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@QEAA@XZ ; std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >::~unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >
  000c6	33 c0		 xor	 eax, eax
  000c8	83 e0 01	 and	 eax, 1
  000cb	85 c0		 test	 eax, eax
  000cd	74 10		 je	 SHORT $LN36@Tidy
  000cf	ba 08 00 00 00	 mov	 edx, 8
  000d4	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  000d9	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  000de	90		 npad	 1
$LN36@Tidy:

; 1104 :         }

  000df	eb ad		 jmp	 SHORT $LN21@Tidy
$LN22@Tidy:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2093 :             _Al.deallocate(_Myfirst, static_cast<size_type>(_Myend - _Myfirst));

  000e1	48 8b 44 24 58	 mov	 rax, QWORD PTR _Myend$[rsp]
  000e6	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Myfirst$[rsp]
  000eb	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  000ee	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000f1	48 2b c1	 sub	 rax, rcx
  000f4	48 c1 f8 03	 sar	 rax, 3
  000f8	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _Count$[rsp], rax
  00100	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  00105	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00108	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  00110	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  00118	48 c1 e0 03	 shl	 rax, 3
  0011c	48 89 44 24 38	 mov	 QWORD PTR _Bytes$[rsp], rax
  00121	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00129	48 89 44 24 48	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  0012e	48 81 7c 24 38
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  00137	72 10		 jb	 SHORT $LN65@Tidy

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  00139	48 8d 54 24 38	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  0013e	48 8d 4c 24 48	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  00143	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  00148	90		 npad	 1
$LN65@Tidy:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  00149	48 8b 54 24 38	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  0014e	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  00153	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00158	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2095 :             _Myfirst = nullptr;

  00159	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  0015e	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0

; 2096 :             _Mylast  = nullptr;

  00165	48 8b 44 24 50	 mov	 rax, QWORD PTR _Mylast$[rsp]
  0016a	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0

; 2097 :             _Myend   = nullptr;

  00171	48 8b 44 24 58	 mov	 rax, QWORD PTR _Myend$[rsp]
  00176	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0
$LN2@Tidy:

; 2098 :         }
; 2099 :     }

  0017d	48 81 c4 a8 00
	00 00		 add	 rsp, 168		; 000000a8H
  00184	c3		 ret	 0
?_Tidy@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAXXZ ENDP ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Tidy
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ?_Change_array@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAXQEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@2@_K1@Z
_TEXT	SEGMENT
_Myfirst$ = 32
_First$ = 40
_My_data$ = 48
_Bytes$ = 56
_Ptr$ = 64
_Ptr$ = 72
_Mylast$ = 80
_Myend$ = 88
$T1 = 96
$T2 = 104
_Last$ = 112
$T3 = 120
_Count$ = 128
_Ptr$ = 136
_Al$ = 144
this$ = 176
_Newvec$ = 184
_Newsize$ = 192
_Newcapacity$ = 200
?_Change_array@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAXQEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@2@_K1@Z PROC ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Change_array, COMDAT

; 2059 :         const pointer _Newvec, const size_type _Newsize, const size_type _Newcapacity) noexcept {

$LN74:
  00000	4c 89 4c 24 20	 mov	 QWORD PTR [rsp+32], r9
  00005	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	48 81 ec a8 00
	00 00		 sub	 rsp, 168		; 000000a8H

; 2227 :         return _Mypair._Get_first();

  0001b	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00023	48 89 44 24 60	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2227 :         return _Mypair._Get_first();

  00028	48 8b 44 24 60	 mov	 rax, QWORD PTR $T1[rsp]
  0002d	48 89 44 24 68	 mov	 QWORD PTR $T2[rsp], rax

; 2060 :         // orphan all iterators, discard old array, acquire new array
; 2061 :         auto& _Al         = _Getal();

  00032	48 8b 44 24 68	 mov	 rax, QWORD PTR $T2[rsp]
  00037	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR _Al$[rsp], rax

; 2062 :         auto& _My_data    = _Mypair._Myval2;

  0003f	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00047	48 89 44 24 30	 mov	 QWORD PTR _My_data$[rsp], rax

; 2063 :         pointer& _Myfirst = _My_data._Myfirst;

  0004c	48 8b 44 24 30	 mov	 rax, QWORD PTR _My_data$[rsp]
  00051	48 89 44 24 20	 mov	 QWORD PTR _Myfirst$[rsp], rax

; 2064 :         pointer& _Mylast  = _My_data._Mylast;

  00056	48 8b 44 24 30	 mov	 rax, QWORD PTR _My_data$[rsp]
  0005b	48 83 c0 08	 add	 rax, 8
  0005f	48 89 44 24 50	 mov	 QWORD PTR _Mylast$[rsp], rax

; 2065 :         pointer& _Myend   = _My_data._Myend;

  00064	48 8b 44 24 30	 mov	 rax, QWORD PTR _My_data$[rsp]
  00069	48 83 c0 10	 add	 rax, 16
  0006d	48 89 44 24 58	 mov	 QWORD PTR _Myend$[rsp], rax

; 2066 : 
; 2067 :         _My_data._Orphan_all();
; 2068 : 
; 2069 :         if (_Myfirst) { // destroy and deallocate old array

  00072	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  00077	48 83 38 00	 cmp	 QWORD PTR [rax], 0
  0007b	0f 84 e7 00 00
	00		 je	 $LN2@Change_arr

; 2070 :             _STD _Destroy_range(_Myfirst, _Mylast, _Al);

  00081	48 8b 44 24 50	 mov	 rax, QWORD PTR _Mylast$[rsp]
  00086	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00089	48 89 44 24 70	 mov	 QWORD PTR _Last$[rsp], rax
  0008e	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  00093	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00096	48 89 44 24 28	 mov	 QWORD PTR _First$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1102 :         for (; _First != _Last; ++_First) {

  0009b	eb 0e		 jmp	 SHORT $LN23@Change_arr
$LN21@Change_arr:
  0009d	48 8b 44 24 28	 mov	 rax, QWORD PTR _First$[rsp]
  000a2	48 83 c0 08	 add	 rax, 8
  000a6	48 89 44 24 28	 mov	 QWORD PTR _First$[rsp], rax
$LN23@Change_arr:
  000ab	48 8b 44 24 70	 mov	 rax, QWORD PTR _Last$[rsp]
  000b0	48 39 44 24 28	 cmp	 QWORD PTR _First$[rsp], rax
  000b5	74 39		 je	 SHORT $LN22@Change_arr

; 69   :     return _Ptr;

  000b7	48 8b 44 24 28	 mov	 rax, QWORD PTR _First$[rsp]
  000bc	48 89 44 24 78	 mov	 QWORD PTR $T3[rsp], rax

; 1103 :             allocator_traits<_Alloc>::destroy(_Al, _STD _Unfancy(_First));

  000c1	48 8b 44 24 78	 mov	 rax, QWORD PTR $T3[rsp]
  000c6	48 89 44 24 40	 mov	 QWORD PTR _Ptr$[rsp], rax
  000cb	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  000d0	e8 00 00 00 00	 call	 ??1?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@QEAA@XZ ; std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >::~unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >
  000d5	33 c0		 xor	 eax, eax
  000d7	83 e0 01	 and	 eax, 1
  000da	85 c0		 test	 eax, eax
  000dc	74 10		 je	 SHORT $LN36@Change_arr
  000de	ba 08 00 00 00	 mov	 edx, 8
  000e3	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  000e8	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  000ed	90		 npad	 1
$LN36@Change_arr:

; 1104 :         }

  000ee	eb ad		 jmp	 SHORT $LN21@Change_arr
$LN22@Change_arr:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2072 :             _Al.deallocate(_Myfirst, static_cast<size_type>(_Myend - _Myfirst));

  000f0	48 8b 44 24 58	 mov	 rax, QWORD PTR _Myend$[rsp]
  000f5	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Myfirst$[rsp]
  000fa	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  000fd	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00100	48 2b c1	 sub	 rax, rcx
  00103	48 c1 f8 03	 sar	 rax, 3
  00107	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _Count$[rsp], rax
  0010f	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  00114	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00117	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  0011f	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  00127	48 c1 e0 03	 shl	 rax, 3
  0012b	48 89 44 24 38	 mov	 QWORD PTR _Bytes$[rsp], rax
  00130	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00138	48 89 44 24 48	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  0013d	48 81 7c 24 38
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  00146	72 10		 jb	 SHORT $LN65@Change_arr

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  00148	48 8d 54 24 38	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  0014d	48 8d 4c 24 48	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  00152	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  00157	90		 npad	 1
$LN65@Change_arr:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  00158	48 8b 54 24 38	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  0015d	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  00162	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00167	90		 npad	 1
$LN2@Change_arr:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2075 :         _Myfirst = _Newvec;

  00168	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  0016d	48 8b 8c 24 b8
	00 00 00	 mov	 rcx, QWORD PTR _Newvec$[rsp]
  00175	48 89 08	 mov	 QWORD PTR [rax], rcx

; 2076 :         _Mylast  = _Newvec + _Newsize;

  00178	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR _Newvec$[rsp]
  00180	48 8b 8c 24 c0
	00 00 00	 mov	 rcx, QWORD PTR _Newsize$[rsp]
  00188	48 8d 04 c8	 lea	 rax, QWORD PTR [rax+rcx*8]
  0018c	48 8b 4c 24 50	 mov	 rcx, QWORD PTR _Mylast$[rsp]
  00191	48 89 01	 mov	 QWORD PTR [rcx], rax

; 2077 :         _Myend   = _Newvec + _Newcapacity;

  00194	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR _Newvec$[rsp]
  0019c	48 8b 8c 24 c8
	00 00 00	 mov	 rcx, QWORD PTR _Newcapacity$[rsp]
  001a4	48 8d 04 c8	 lea	 rax, QWORD PTR [rax+rcx*8]
  001a8	48 8b 4c 24 58	 mov	 rcx, QWORD PTR _Myend$[rsp]
  001ad	48 89 01	 mov	 QWORD PTR [rcx], rax

; 2078 :         _ASAN_VECTOR_CREATE;
; 2079 :     }

  001b0	48 81 c4 a8 00
	00 00		 add	 rsp, 168		; 000000a8H
  001b7	c3		 ret	 0
?_Change_array@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAXQEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@2@_K1@Z ENDP ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Change_array
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ?_Calculate_growth@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEBA_K_K@Z
_TEXT	SEGMENT
_Oldcapacity$ = 0
_My_data$1 = 8
$T2 = 16
$T3 = 24
tv82 = 32
_Max$ = 40
_Geometric$ = 48
$T4 = 56
$T5 = 64
$T6 = 72
$T7 = 80
$T8 = 88
$T9 = 96
$T10 = 104
$T11 = 112
_Unsigned_max$12 = 120
this$ = 144
_Newsize$ = 152
?_Calculate_growth@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEBA_K_K@Z PROC ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Calculate_growth, COMDAT

; 2006 :     _CONSTEXPR20 size_type _Calculate_growth(const size_type _Newsize) const {

$LN42:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H

; 1923 :         auto& _My_data = _Mypair._Myval2;

  00011	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00019	48 89 44 24 08	 mov	 QWORD PTR _My_data$1[rsp], rax

; 1924 :         return static_cast<size_type>(_My_data._Myend - _My_data._Myfirst);

  0001e	48 8b 44 24 08	 mov	 rax, QWORD PTR _My_data$1[rsp]
  00023	48 8b 4c 24 08	 mov	 rcx, QWORD PTR _My_data$1[rsp]
  00028	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0002b	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0002f	48 2b c1	 sub	 rax, rcx
  00032	48 c1 f8 03	 sar	 rax, 3
  00036	48 89 44 24 38	 mov	 QWORD PTR $T4[rsp], rax

; 2007 :         // given _Oldcapacity and _Newsize, calculate geometric growth
; 2008 :         const size_type _Oldcapacity = capacity();

  0003b	48 8b 44 24 38	 mov	 rax, QWORD PTR $T4[rsp]
  00040	48 89 04 24	 mov	 QWORD PTR _Oldcapacity$[rsp], rax

; 2231 :         return _Mypair._Get_first();

  00044	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1543 :         return *this;

  0004c	48 89 44 24 40	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2231 :         return _Mypair._Get_first();

  00051	48 8b 44 24 40	 mov	 rax, QWORD PTR $T5[rsp]
  00056	48 89 44 24 70	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 746  :         return static_cast<size_t>(-1) / sizeof(value_type);

  0005b	48 b8 ff ff ff
	ff ff ff ff 1f	 mov	 rax, 2305843009213693951 ; 1fffffffffffffffH
  00065	48 89 44 24 48	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1919 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()), _Alty_traits::max_size(_Getal()));

  0006a	48 8b 44 24 48	 mov	 rax, QWORD PTR $T6[rsp]
  0006f	48 89 44 24 10	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 866  :         constexpr auto _Unsigned_max = static_cast<make_unsigned_t<_Ty>>(-1);

  00074	48 c7 44 24 78
	ff ff ff ff	 mov	 QWORD PTR _Unsigned_max$12[rsp], -1

; 867  :         return static_cast<_Ty>(_Unsigned_max >> 1);

  0007d	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  00087	48 89 44 24 50	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1919 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()), _Alty_traits::max_size(_Getal()));

  0008c	48 8b 44 24 50	 mov	 rax, QWORD PTR $T7[rsp]
  00091	48 89 44 24 18	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 101  :     return _Right < _Left ? _Right : _Left;

  00096	48 8b 44 24 18	 mov	 rax, QWORD PTR $T3[rsp]
  0009b	48 39 44 24 10	 cmp	 QWORD PTR $T2[rsp], rax
  000a0	73 0c		 jae	 SHORT $LN37@Calculate_
  000a2	48 8d 44 24 10	 lea	 rax, QWORD PTR $T2[rsp]
  000a7	48 89 44 24 20	 mov	 QWORD PTR tv82[rsp], rax
  000ac	eb 0a		 jmp	 SHORT $LN38@Calculate_
$LN37@Calculate_:
  000ae	48 8d 44 24 18	 lea	 rax, QWORD PTR $T3[rsp]
  000b3	48 89 44 24 20	 mov	 QWORD PTR tv82[rsp], rax
$LN38@Calculate_:
  000b8	48 8b 44 24 20	 mov	 rax, QWORD PTR tv82[rsp]
  000bd	48 89 44 24 58	 mov	 QWORD PTR $T8[rsp], rax
  000c2	48 8b 44 24 58	 mov	 rax, QWORD PTR $T8[rsp]
  000c7	48 89 44 24 60	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1919 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()), _Alty_traits::max_size(_Getal()));

  000cc	48 8b 44 24 60	 mov	 rax, QWORD PTR $T9[rsp]
  000d1	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000d4	48 89 44 24 68	 mov	 QWORD PTR $T10[rsp], rax

; 2009 :         const auto _Max              = max_size();

  000d9	48 8b 44 24 68	 mov	 rax, QWORD PTR $T10[rsp]
  000de	48 89 44 24 28	 mov	 QWORD PTR _Max$[rsp], rax

; 2010 : 
; 2011 :         if (_Oldcapacity > _Max - _Oldcapacity / 2) {

  000e3	33 d2		 xor	 edx, edx
  000e5	48 8b 04 24	 mov	 rax, QWORD PTR _Oldcapacity$[rsp]
  000e9	b9 02 00 00 00	 mov	 ecx, 2
  000ee	48 f7 f1	 div	 rcx
  000f1	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Max$[rsp]
  000f6	48 2b c8	 sub	 rcx, rax
  000f9	48 8b c1	 mov	 rax, rcx
  000fc	48 39 04 24	 cmp	 QWORD PTR _Oldcapacity$[rsp], rax
  00100	76 07		 jbe	 SHORT $LN2@Calculate_

; 2012 :             return _Max; // geometric growth would overflow

  00102	48 8b 44 24 28	 mov	 rax, QWORD PTR _Max$[rsp]
  00107	eb 3b		 jmp	 SHORT $LN1@Calculate_
$LN2@Calculate_:

; 2013 :         }
; 2014 : 
; 2015 :         const size_type _Geometric = _Oldcapacity + _Oldcapacity / 2;

  00109	33 d2		 xor	 edx, edx
  0010b	48 8b 04 24	 mov	 rax, QWORD PTR _Oldcapacity$[rsp]
  0010f	b9 02 00 00 00	 mov	 ecx, 2
  00114	48 f7 f1	 div	 rcx
  00117	48 8b 0c 24	 mov	 rcx, QWORD PTR _Oldcapacity$[rsp]
  0011b	48 03 c8	 add	 rcx, rax
  0011e	48 8b c1	 mov	 rax, rcx
  00121	48 89 44 24 30	 mov	 QWORD PTR _Geometric$[rsp], rax

; 2016 : 
; 2017 :         if (_Geometric < _Newsize) {

  00126	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR _Newsize$[rsp]
  0012e	48 39 44 24 30	 cmp	 QWORD PTR _Geometric$[rsp], rax
  00133	73 0a		 jae	 SHORT $LN3@Calculate_

; 2018 :             return _Newsize; // geometric growth would be insufficient

  00135	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR _Newsize$[rsp]
  0013d	eb 05		 jmp	 SHORT $LN1@Calculate_
$LN3@Calculate_:

; 2019 :         }
; 2020 : 
; 2021 :         return _Geometric; // geometric growth is sufficient

  0013f	48 8b 44 24 30	 mov	 rax, QWORD PTR _Geometric$[rsp]
$LN1@Calculate_:

; 2022 :     }

  00144	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  0014b	c3		 ret	 0
?_Calculate_growth@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEBA_K_K@Z ENDP ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Calculate_growth
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?allocate@?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@std@@QEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@2@_K@Z
_TEXT	SEGMENT
_Overflow_is_possible$1 = 32
_Bytes$ = 40
$T2 = 48
$T3 = 56
$T4 = 64
_Max_possible$5 = 72
this$ = 96
_Count$ = 104
?allocate@?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@std@@QEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@2@_K@Z PROC ; std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > >::allocate, COMDAT

; 988  :     _NODISCARD_RAW_PTR_ALLOC _CONSTEXPR20 __declspec(allocator) _Ty* allocate(_CRT_GUARDOVERFLOW const size_t _Count) {

$LN13:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 113  :     constexpr bool _Overflow_is_possible = _Ty_size > 1;

  0000e	c6 44 24 20 01	 mov	 BYTE PTR _Overflow_is_possible$1[rsp], 1

; 114  : 
; 115  :     if constexpr (_Overflow_is_possible) {
; 116  :         constexpr size_t _Max_possible = static_cast<size_t>(-1) / _Ty_size;

  00013	48 b8 ff ff ff
	ff ff ff ff 1f	 mov	 rax, 2305843009213693951 ; 1fffffffffffffffH
  0001d	48 89 44 24 48	 mov	 QWORD PTR _Max_possible$5[rsp], rax

; 117  :         if (_Count > _Max_possible) {

  00022	48 b8 ff ff ff
	ff ff ff ff 1f	 mov	 rax, 2305843009213693951 ; 1fffffffffffffffH
  0002c	48 39 44 24 68	 cmp	 QWORD PTR _Count$[rsp], rax
  00031	76 06		 jbe	 SHORT $LN4@allocate

; 118  :             _Throw_bad_array_new_length(); // multiply overflow

  00033	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00038	90		 npad	 1
$LN4@allocate:

; 119  :         }
; 120  :     }
; 121  : 
; 122  :     return _Count * _Ty_size;

  00039	48 8b 44 24 68	 mov	 rax, QWORD PTR _Count$[rsp]
  0003e	48 c1 e0 03	 shl	 rax, 3
  00042	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00047	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
  0004c	48 89 44 24 28	 mov	 QWORD PTR _Bytes$[rsp], rax

; 227  :     if (_Bytes == 0) {

  00051	48 83 7c 24 28
	00		 cmp	 QWORD PTR _Bytes$[rsp], 0
  00057	75 0b		 jne	 SHORT $LN8@allocate

; 228  :         return nullptr;

  00059	48 c7 44 24 30
	00 00 00 00	 mov	 QWORD PTR $T2[rsp], 0
  00062	eb 35		 jmp	 SHORT $LN7@allocate
$LN8@allocate:

; 229  :     }
; 230  : 
; 231  : #if _HAS_CXX20 // TRANSITION, GH-1532
; 232  :     if (_STD is_constant_evaluated()) {
; 233  :         return _Traits::_Allocate(_Bytes);
; 234  :     }
; 235  : #endif // _HAS_CXX20
; 236  : 
; 237  : #ifdef __cpp_aligned_new
; 238  :     if constexpr (_Align > __STDCPP_DEFAULT_NEW_ALIGNMENT__) {
; 239  :         size_t _Passed_align = _Align;
; 240  : #if defined(_M_IX86) || defined(_M_X64)
; 241  :         if (_Bytes >= _Big_allocation_threshold) {
; 242  :             // boost the alignment of big allocations to help autovectorization
; 243  :             _Passed_align = (_STD max)(_Align, _Big_allocation_alignment);
; 244  :         }
; 245  : #endif // defined(_M_IX86) || defined(_M_X64)
; 246  :         return _Traits::_Allocate_aligned(_Bytes, _Passed_align);
; 247  :     } else
; 248  : #endif // defined(__cpp_aligned_new)
; 249  :     {
; 250  : #if defined(_M_IX86) || defined(_M_X64)
; 251  :         if (_Bytes >= _Big_allocation_threshold) {

  00064	48 81 7c 24 28
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  0006d	72 11		 jb	 SHORT $LN9@allocate

; 252  :             // boost the alignment of big allocations to help autovectorization
; 253  :             return _Allocate_manually_vector_aligned<_Traits>(_Bytes);

  0006f	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00074	e8 00 00 00 00	 call	 ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
  00079	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
  0007e	eb 19		 jmp	 SHORT $LN7@allocate
$LN9@allocate:

; 136  :         return ::operator new(_Bytes);

  00080	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00085	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  0008a	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax

; 256  :         return _Traits::_Allocate(_Bytes);

  0008f	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  00094	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
$LN7@allocate:

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00099	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
$LN6@allocate:

; 991  :     }

  0009e	48 83 c4 58	 add	 rsp, 88			; 00000058H
  000a2	c3		 ret	 0
?allocate@?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@std@@QEAAPEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@2@_K@Z ENDP ; std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > >::allocate
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
;	COMDAT ?DevPvPFieldTimeExtensionNext@EventScheduleManager@mu2@@QEAAXXZ
_TEXT	SEGMENT
<begin>$L0$1 = 48
<range>$L0$2 = 56
ptr$3 = 64
_Ptr$ = 72
$T4 = 80
$T5 = 88
_Ptr$ = 96
$T6 = 104
$T7 = 112
<end>$L0$8 = 120
i$9 = 128
$T10 = 136
this$ = 160
?DevPvPFieldTimeExtensionNext@EventScheduleManager@mu2@@QEAAXXZ PROC ; mu2::EventScheduleManager::DevPvPFieldTimeExtensionNext, COMDAT

; 80   : 	{

$LN32:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec 98 00
	00 00		 sub	 rsp, 152		; 00000098H

; 81   : 		for (auto& i : m_vecEventSchedulePtr)

  0000c	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 83 c0 08	 add	 rax, 8
  00018	48 89 44 24 38	 mov	 QWORD PTR <range>$L0$2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1893 :         return _Unfancy_maybe_null(_Mypair._Myval2._Myfirst);

  0001d	48 8b 44 24 38	 mov	 rax, QWORD PTR <range>$L0$2[rsp]
  00022	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00025	48 89 44 24 48	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 80   :     return _Ptr;

  0002a	48 8b 44 24 48	 mov	 rax, QWORD PTR _Ptr$[rsp]
  0002f	48 89 44 24 50	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1893 :         return _Unfancy_maybe_null(_Mypair._Myval2._Myfirst);

  00034	48 8b 44 24 50	 mov	 rax, QWORD PTR $T4[rsp]
  00039	48 89 44 24 58	 mov	 QWORD PTR $T5[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp

; 81   : 		for (auto& i : m_vecEventSchedulePtr)

  0003e	48 8b 44 24 58	 mov	 rax, QWORD PTR $T5[rsp]
  00043	48 89 44 24 30	 mov	 QWORD PTR <begin>$L0$1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1901 :         return _Unfancy_maybe_null(_Mypair._Myval2._Mylast);

  00048	48 8b 44 24 38	 mov	 rax, QWORD PTR <range>$L0$2[rsp]
  0004d	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00051	48 89 44 24 60	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 80   :     return _Ptr;

  00056	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  0005b	48 89 44 24 68	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1901 :         return _Unfancy_maybe_null(_Mypair._Myval2._Mylast);

  00060	48 8b 44 24 68	 mov	 rax, QWORD PTR $T6[rsp]
  00065	48 89 44 24 70	 mov	 QWORD PTR $T7[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp

; 81   : 		for (auto& i : m_vecEventSchedulePtr)

  0006a	48 8b 44 24 70	 mov	 rax, QWORD PTR $T7[rsp]
  0006f	48 89 44 24 78	 mov	 QWORD PTR <end>$L0$8[rsp], rax
  00074	eb 0e		 jmp	 SHORT $LN4@DevPvPFiel
$LN2@DevPvPFiel:
  00076	48 8b 44 24 30	 mov	 rax, QWORD PTR <begin>$L0$1[rsp]
  0007b	48 83 c0 08	 add	 rax, 8
  0007f	48 89 44 24 30	 mov	 QWORD PTR <begin>$L0$1[rsp], rax
$LN4@DevPvPFiel:
  00084	48 8b 44 24 78	 mov	 rax, QWORD PTR <end>$L0$8[rsp]
  00089	48 39 44 24 30	 cmp	 QWORD PTR <begin>$L0$1[rsp], rax
  0008e	74 64		 je	 SHORT $LN3@DevPvPFiel
  00090	48 8b 44 24 30	 mov	 rax, QWORD PTR <begin>$L0$1[rsp]
  00095	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR i$9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3457 :         return _Mypair._Myval2;

  0009d	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR i$9[rsp]
  000a5	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000a8	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp

; 83   : 			PVPFieldSchedule* ptr = dynamic_cast<PVPFieldSchedule*>(i.get());

  000b0	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  000b8	c7 44 24 20 00
	00 00 00	 mov	 DWORD PTR [rsp+32], 0
  000c0	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_R0?AVPVPFieldSchedule@mu2@@@8
  000c7	4c 8d 05 00 00
	00 00		 lea	 r8, OFFSET FLAT:??_R0?AVEventSchedule@mu2@@@8
  000ce	33 d2		 xor	 edx, edx
  000d0	48 8b c8	 mov	 rcx, rax
  000d3	e8 00 00 00 00	 call	 __RTDynamicCast
  000d8	48 89 44 24 40	 mov	 QWORD PTR ptr$3[rsp], rax

; 84   : 			if (ptr == nullptr)

  000dd	48 83 7c 24 40
	00		 cmp	 QWORD PTR ptr$3[rsp], 0
  000e3	75 02		 jne	 SHORT $LN5@DevPvPFiel

; 85   : 				continue;

  000e5	eb 8f		 jmp	 SHORT $LN2@DevPvPFiel
$LN5@DevPvPFiel:

; 86   : 
; 87   : 			ptr->DevTimeExtensionNext();

  000e7	48 8b 4c 24 40	 mov	 rcx, QWORD PTR ptr$3[rsp]
  000ec	e8 00 00 00 00	 call	 ?DevTimeExtensionNext@PVPFieldSchedule@mu2@@QEAAXXZ ; mu2::PVPFieldSchedule::DevTimeExtensionNext
  000f1	90		 npad	 1

; 88   : 		}

  000f2	eb 82		 jmp	 SHORT $LN2@DevPvPFiel
$LN3@DevPvPFiel:

; 89   : 	}

  000f4	48 81 c4 98 00
	00 00		 add	 rsp, 152		; 00000098H
  000fb	c3		 ret	 0
?DevPvPFieldTimeExtensionNext@EventScheduleManager@mu2@@QEAAXXZ ENDP ; mu2::EventScheduleManager::DevPvPFieldTimeExtensionNext
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
;	COMDAT ?DevGetOhrdorTreasureHuntNextEventTime@EventScheduleManager@mu2@@QEAA_JXZ
_TEXT	SEGMENT
<begin>$L0$1 = 48
<range>$L0$2 = 56
pOhrdorTreasureHunt$3 = 64
_Ptr$ = 72
$T4 = 80
$T5 = 88
_Ptr$ = 96
$T6 = 104
$T7 = 112
<end>$L0$8 = 120
i$9 = 128
$T10 = 136
this$ = 160
?DevGetOhrdorTreasureHuntNextEventTime@EventScheduleManager@mu2@@QEAA_JXZ PROC ; mu2::EventScheduleManager::DevGetOhrdorTreasureHuntNextEventTime, COMDAT

; 105  : 	{ 

$LN32:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec 98 00
	00 00		 sub	 rsp, 152		; 00000098H

; 107  : 		for (auto& i : m_vecEventSchedulePtr)

  0000c	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 83 c0 08	 add	 rax, 8
  00018	48 89 44 24 38	 mov	 QWORD PTR <range>$L0$2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1893 :         return _Unfancy_maybe_null(_Mypair._Myval2._Myfirst);

  0001d	48 8b 44 24 38	 mov	 rax, QWORD PTR <range>$L0$2[rsp]
  00022	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00025	48 89 44 24 48	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 80   :     return _Ptr;

  0002a	48 8b 44 24 48	 mov	 rax, QWORD PTR _Ptr$[rsp]
  0002f	48 89 44 24 50	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1893 :         return _Unfancy_maybe_null(_Mypair._Myval2._Myfirst);

  00034	48 8b 44 24 50	 mov	 rax, QWORD PTR $T4[rsp]
  00039	48 89 44 24 58	 mov	 QWORD PTR $T5[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp

; 107  : 		for (auto& i : m_vecEventSchedulePtr)

  0003e	48 8b 44 24 58	 mov	 rax, QWORD PTR $T5[rsp]
  00043	48 89 44 24 30	 mov	 QWORD PTR <begin>$L0$1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1901 :         return _Unfancy_maybe_null(_Mypair._Myval2._Mylast);

  00048	48 8b 44 24 38	 mov	 rax, QWORD PTR <range>$L0$2[rsp]
  0004d	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00051	48 89 44 24 60	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 80   :     return _Ptr;

  00056	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  0005b	48 89 44 24 68	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1901 :         return _Unfancy_maybe_null(_Mypair._Myval2._Mylast);

  00060	48 8b 44 24 68	 mov	 rax, QWORD PTR $T6[rsp]
  00065	48 89 44 24 70	 mov	 QWORD PTR $T7[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp

; 107  : 		for (auto& i : m_vecEventSchedulePtr)

  0006a	48 8b 44 24 70	 mov	 rax, QWORD PTR $T7[rsp]
  0006f	48 89 44 24 78	 mov	 QWORD PTR <end>$L0$8[rsp], rax
  00074	eb 0e		 jmp	 SHORT $LN4@DevGetOhrd
$LN2@DevGetOhrd:
  00076	48 8b 44 24 30	 mov	 rax, QWORD PTR <begin>$L0$1[rsp]
  0007b	48 83 c0 08	 add	 rax, 8
  0007f	48 89 44 24 30	 mov	 QWORD PTR <begin>$L0$1[rsp], rax
$LN4@DevGetOhrd:
  00084	48 8b 44 24 78	 mov	 rax, QWORD PTR <end>$L0$8[rsp]
  00089	48 39 44 24 30	 cmp	 QWORD PTR <begin>$L0$1[rsp], rax
  0008e	74 65		 je	 SHORT $LN3@DevGetOhrd
  00090	48 8b 44 24 30	 mov	 rax, QWORD PTR <begin>$L0$1[rsp]
  00095	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR i$9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3457 :         return _Mypair._Myval2;

  0009d	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR i$9[rsp]
  000a5	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000a8	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp

; 109  : 			OhrdorTreasureHunt* pOhrdorTreasureHunt = dynamic_cast<OhrdorTreasureHunt*>(i.get());

  000b0	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  000b8	c7 44 24 20 00
	00 00 00	 mov	 DWORD PTR [rsp+32], 0
  000c0	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_R0?AVOhrdorTreasureHunt@mu2@@@8
  000c7	4c 8d 05 00 00
	00 00		 lea	 r8, OFFSET FLAT:??_R0?AVEventSchedule@mu2@@@8
  000ce	33 d2		 xor	 edx, edx
  000d0	48 8b c8	 mov	 rcx, rax
  000d3	e8 00 00 00 00	 call	 __RTDynamicCast
  000d8	48 89 44 24 40	 mov	 QWORD PTR pOhrdorTreasureHunt$3[rsp], rax

; 110  : 			if (pOhrdorTreasureHunt == nullptr)

  000dd	48 83 7c 24 40
	00		 cmp	 QWORD PTR pOhrdorTreasureHunt$3[rsp], 0
  000e3	75 02		 jne	 SHORT $LN5@DevGetOhrd

; 111  : 				continue;

  000e5	eb 8f		 jmp	 SHORT $LN2@DevGetOhrd
$LN5@DevGetOhrd:

; 112  : 
; 113  : 			return pOhrdorTreasureHunt->DevGetNextTime();

  000e7	48 8b 4c 24 40	 mov	 rcx, QWORD PTR pOhrdorTreasureHunt$3[rsp]
  000ec	e8 00 00 00 00	 call	 ?DevGetNextTime@OhrdorTreasureHunt@mu2@@QEAA_JXZ ; mu2::OhrdorTreasureHunt::DevGetNextTime
  000f1	eb 04		 jmp	 SHORT $LN1@DevGetOhrd

; 114  : 		}

  000f3	eb 81		 jmp	 SHORT $LN2@DevGetOhrd
$LN3@DevGetOhrd:

; 115  : 
; 116  : 		return 0;

  000f5	33 c0		 xor	 eax, eax
$LN1@DevGetOhrd:

; 117  : 	}

  000f7	48 81 c4 98 00
	00 00		 add	 rsp, 152		; 00000098H
  000fe	c3		 ret	 0
?DevGetOhrdorTreasureHuntNextEventTime@EventScheduleManager@mu2@@QEAA_JXZ ENDP ; mu2::EventScheduleManager::DevGetOhrdorTreasureHuntNextEventTime
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
;	COMDAT ?DevOhrdorTreasureHuntTimeExtension@EventScheduleManager@mu2@@QEAAX_N@Z
_TEXT	SEGMENT
<begin>$L0$1 = 48
<range>$L0$2 = 56
pOhrdorTreasureHunt$3 = 64
_Ptr$ = 72
$T4 = 80
$T5 = 88
_Ptr$ = 96
$T6 = 104
$T7 = 112
<end>$L0$8 = 120
i$9 = 128
$T10 = 136
this$ = 160
on$ = 168
?DevOhrdorTreasureHuntTimeExtension@EventScheduleManager@mu2@@QEAAX_N@Z PROC ; mu2::EventScheduleManager::DevOhrdorTreasureHuntTimeExtension, COMDAT

; 93   : 	{

$LN32:
  00000	88 54 24 10	 mov	 BYTE PTR [rsp+16], dl
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 81 ec 98 00
	00 00		 sub	 rsp, 152		; 00000098H

; 94   : 		for (auto& i : m_vecEventSchedulePtr)

  00010	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00018	48 83 c0 08	 add	 rax, 8
  0001c	48 89 44 24 38	 mov	 QWORD PTR <range>$L0$2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1893 :         return _Unfancy_maybe_null(_Mypair._Myval2._Myfirst);

  00021	48 8b 44 24 38	 mov	 rax, QWORD PTR <range>$L0$2[rsp]
  00026	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00029	48 89 44 24 48	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 80   :     return _Ptr;

  0002e	48 8b 44 24 48	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00033	48 89 44 24 50	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1893 :         return _Unfancy_maybe_null(_Mypair._Myval2._Myfirst);

  00038	48 8b 44 24 50	 mov	 rax, QWORD PTR $T4[rsp]
  0003d	48 89 44 24 58	 mov	 QWORD PTR $T5[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp

; 94   : 		for (auto& i : m_vecEventSchedulePtr)

  00042	48 8b 44 24 58	 mov	 rax, QWORD PTR $T5[rsp]
  00047	48 89 44 24 30	 mov	 QWORD PTR <begin>$L0$1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1901 :         return _Unfancy_maybe_null(_Mypair._Myval2._Mylast);

  0004c	48 8b 44 24 38	 mov	 rax, QWORD PTR <range>$L0$2[rsp]
  00051	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00055	48 89 44 24 60	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 80   :     return _Ptr;

  0005a	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  0005f	48 89 44 24 68	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1901 :         return _Unfancy_maybe_null(_Mypair._Myval2._Mylast);

  00064	48 8b 44 24 68	 mov	 rax, QWORD PTR $T6[rsp]
  00069	48 89 44 24 70	 mov	 QWORD PTR $T7[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp

; 94   : 		for (auto& i : m_vecEventSchedulePtr)

  0006e	48 8b 44 24 70	 mov	 rax, QWORD PTR $T7[rsp]
  00073	48 89 44 24 78	 mov	 QWORD PTR <end>$L0$8[rsp], rax
  00078	eb 0e		 jmp	 SHORT $LN4@DevOhrdorT
$LN2@DevOhrdorT:
  0007a	48 8b 44 24 30	 mov	 rax, QWORD PTR <begin>$L0$1[rsp]
  0007f	48 83 c0 08	 add	 rax, 8
  00083	48 89 44 24 30	 mov	 QWORD PTR <begin>$L0$1[rsp], rax
$LN4@DevOhrdorT:
  00088	48 8b 44 24 78	 mov	 rax, QWORD PTR <end>$L0$8[rsp]
  0008d	48 39 44 24 30	 cmp	 QWORD PTR <begin>$L0$1[rsp], rax
  00092	74 6f		 je	 SHORT $LN3@DevOhrdorT
  00094	48 8b 44 24 30	 mov	 rax, QWORD PTR <begin>$L0$1[rsp]
  00099	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR i$9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3457 :         return _Mypair._Myval2;

  000a1	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR i$9[rsp]
  000a9	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000ac	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp

; 96   : 			OhrdorTreasureHunt* pOhrdorTreasureHunt = dynamic_cast<OhrdorTreasureHunt*>(i.get());

  000b4	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  000bc	c7 44 24 20 00
	00 00 00	 mov	 DWORD PTR [rsp+32], 0
  000c4	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_R0?AVOhrdorTreasureHunt@mu2@@@8
  000cb	4c 8d 05 00 00
	00 00		 lea	 r8, OFFSET FLAT:??_R0?AVEventSchedule@mu2@@@8
  000d2	33 d2		 xor	 edx, edx
  000d4	48 8b c8	 mov	 rcx, rax
  000d7	e8 00 00 00 00	 call	 __RTDynamicCast
  000dc	48 89 44 24 40	 mov	 QWORD PTR pOhrdorTreasureHunt$3[rsp], rax

; 97   : 			if (pOhrdorTreasureHunt == nullptr)

  000e1	48 83 7c 24 40
	00		 cmp	 QWORD PTR pOhrdorTreasureHunt$3[rsp], 0
  000e7	75 02		 jne	 SHORT $LN5@DevOhrdorT

; 98   : 				continue;

  000e9	eb 8f		 jmp	 SHORT $LN2@DevOhrdorT
$LN5@DevOhrdorT:

; 99   : 
; 100  : 			pOhrdorTreasureHunt->DevTimeExtension(on);

  000eb	0f b6 94 24 a8
	00 00 00	 movzx	 edx, BYTE PTR on$[rsp]
  000f3	48 8b 4c 24 40	 mov	 rcx, QWORD PTR pOhrdorTreasureHunt$3[rsp]
  000f8	e8 00 00 00 00	 call	 ?DevTimeExtension@OhrdorTreasureHunt@mu2@@QEAAX_N@Z ; mu2::OhrdorTreasureHunt::DevTimeExtension
  000fd	90		 npad	 1

; 101  : 		}

  000fe	e9 77 ff ff ff	 jmp	 $LN2@DevOhrdorT
$LN3@DevOhrdorT:

; 102  : 	}

  00103	48 81 c4 98 00
	00 00		 add	 rsp, 152		; 00000098H
  0010a	c3		 ret	 0
?DevOhrdorTreasureHuntTimeExtension@EventScheduleManager@mu2@@QEAAX_N@Z ENDP ; mu2::EventScheduleManager::DevOhrdorTreasureHuntTimeExtension
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
;	COMDAT ?Update@EventScheduleManager@mu2@@QEAAXXZ
_TEXT	SEGMENT
<begin>$L0$1 = 32
<range>$L0$2 = 40
_Ptr$ = 48
$T3 = 56
$T4 = 64
_Ptr$ = 72
$T5 = 80
$T6 = 88
<end>$L0$7 = 96
i$8 = 104
$T9 = 112
this$ = 144
?Update@EventScheduleManager@mu2@@QEAAXXZ PROC		; mu2::EventScheduleManager::Update, COMDAT

; 71   : 	{

$LN31:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H

; 72   : 		for (auto& i: m_vecEventSchedulePtr)

  0000c	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 83 c0 08	 add	 rax, 8
  00018	48 89 44 24 28	 mov	 QWORD PTR <range>$L0$2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1893 :         return _Unfancy_maybe_null(_Mypair._Myval2._Myfirst);

  0001d	48 8b 44 24 28	 mov	 rax, QWORD PTR <range>$L0$2[rsp]
  00022	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00025	48 89 44 24 30	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 80   :     return _Ptr;

  0002a	48 8b 44 24 30	 mov	 rax, QWORD PTR _Ptr$[rsp]
  0002f	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1893 :         return _Unfancy_maybe_null(_Mypair._Myval2._Myfirst);

  00034	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
  00039	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp

; 72   : 		for (auto& i: m_vecEventSchedulePtr)

  0003e	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  00043	48 89 44 24 20	 mov	 QWORD PTR <begin>$L0$1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1901 :         return _Unfancy_maybe_null(_Mypair._Myval2._Mylast);

  00048	48 8b 44 24 28	 mov	 rax, QWORD PTR <range>$L0$2[rsp]
  0004d	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00051	48 89 44 24 48	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 80   :     return _Ptr;

  00056	48 8b 44 24 48	 mov	 rax, QWORD PTR _Ptr$[rsp]
  0005b	48 89 44 24 50	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1901 :         return _Unfancy_maybe_null(_Mypair._Myval2._Mylast);

  00060	48 8b 44 24 50	 mov	 rax, QWORD PTR $T5[rsp]
  00065	48 89 44 24 58	 mov	 QWORD PTR $T6[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp

; 72   : 		for (auto& i: m_vecEventSchedulePtr)

  0006a	48 8b 44 24 58	 mov	 rax, QWORD PTR $T6[rsp]
  0006f	48 89 44 24 60	 mov	 QWORD PTR <end>$L0$7[rsp], rax
  00074	eb 0e		 jmp	 SHORT $LN4@Update
$LN2@Update:
  00076	48 8b 44 24 20	 mov	 rax, QWORD PTR <begin>$L0$1[rsp]
  0007b	48 83 c0 08	 add	 rax, 8
  0007f	48 89 44 24 20	 mov	 QWORD PTR <begin>$L0$1[rsp], rax
$LN4@Update:
  00084	48 8b 44 24 60	 mov	 rax, QWORD PTR <end>$L0$7[rsp]
  00089	48 39 44 24 20	 cmp	 QWORD PTR <begin>$L0$1[rsp], rax
  0008e	74 27		 je	 SHORT $LN3@Update
  00090	48 8b 44 24 20	 mov	 rax, QWORD PTR <begin>$L0$1[rsp]
  00095	48 89 44 24 68	 mov	 QWORD PTR i$8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3453 :         return _Mypair._Myval2;

  0009a	48 8b 44 24 68	 mov	 rax, QWORD PTR i$8[rsp]
  0009f	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000a2	48 89 44 24 70	 mov	 QWORD PTR $T9[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp

; 74   : 			i->Update();

  000a7	48 8b 44 24 70	 mov	 rax, QWORD PTR $T9[rsp]
  000ac	48 8b c8	 mov	 rcx, rax
  000af	e8 00 00 00 00	 call	 ?Update@EventSchedule@mu2@@QEAAXXZ ; mu2::EventSchedule::Update
  000b4	90		 npad	 1

; 75   : 		}

  000b5	eb bf		 jmp	 SHORT $LN2@Update
$LN3@Update:

; 76   : 	}

  000b7	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  000be	c3		 ret	 0
?Update@EventScheduleManager@mu2@@QEAAXXZ ENDP		; mu2::EventScheduleManager::Update
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
;	COMDAT ?OnEvent@EventScheduleManager@mu2@@QEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
_TEXT	SEGMENT
<begin>$L0$1 = 32
<range>$L0$2 = 40
tv74 = 48
_Ptr$ = 56
$T3 = 64
$T4 = 72
_Ptr$ = 80
$T5 = 88
$T6 = 96
<end>$L0$7 = 104
i$8 = 112
$T9 = 120
this$ = 144
e$ = 152
?OnEvent@EventScheduleManager@mu2@@QEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z PROC ; mu2::EventScheduleManager::OnEvent, COMDAT

; 62   : 	{

$LN31:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H

; 63   : 		for (auto& i : m_vecEventSchedulePtr)

  00011	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00019	48 83 c0 08	 add	 rax, 8
  0001d	48 89 44 24 28	 mov	 QWORD PTR <range>$L0$2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1893 :         return _Unfancy_maybe_null(_Mypair._Myval2._Myfirst);

  00022	48 8b 44 24 28	 mov	 rax, QWORD PTR <range>$L0$2[rsp]
  00027	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0002a	48 89 44 24 38	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 80   :     return _Ptr;

  0002f	48 8b 44 24 38	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00034	48 89 44 24 40	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1893 :         return _Unfancy_maybe_null(_Mypair._Myval2._Myfirst);

  00039	48 8b 44 24 40	 mov	 rax, QWORD PTR $T3[rsp]
  0003e	48 89 44 24 48	 mov	 QWORD PTR $T4[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp

; 63   : 		for (auto& i : m_vecEventSchedulePtr)

  00043	48 8b 44 24 48	 mov	 rax, QWORD PTR $T4[rsp]
  00048	48 89 44 24 20	 mov	 QWORD PTR <begin>$L0$1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1901 :         return _Unfancy_maybe_null(_Mypair._Myval2._Mylast);

  0004d	48 8b 44 24 28	 mov	 rax, QWORD PTR <range>$L0$2[rsp]
  00052	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00056	48 89 44 24 50	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 80   :     return _Ptr;

  0005b	48 8b 44 24 50	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00060	48 89 44 24 58	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1901 :         return _Unfancy_maybe_null(_Mypair._Myval2._Mylast);

  00065	48 8b 44 24 58	 mov	 rax, QWORD PTR $T5[rsp]
  0006a	48 89 44 24 60	 mov	 QWORD PTR $T6[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp

; 63   : 		for (auto& i : m_vecEventSchedulePtr)

  0006f	48 8b 44 24 60	 mov	 rax, QWORD PTR $T6[rsp]
  00074	48 89 44 24 68	 mov	 QWORD PTR <end>$L0$7[rsp], rax
  00079	eb 0e		 jmp	 SHORT $LN4@OnEvent
$LN2@OnEvent:
  0007b	48 8b 44 24 20	 mov	 rax, QWORD PTR <begin>$L0$1[rsp]
  00080	48 83 c0 08	 add	 rax, 8
  00084	48 89 44 24 20	 mov	 QWORD PTR <begin>$L0$1[rsp], rax
$LN4@OnEvent:
  00089	48 8b 44 24 68	 mov	 rax, QWORD PTR <end>$L0$7[rsp]
  0008e	48 39 44 24 20	 cmp	 QWORD PTR <begin>$L0$1[rsp], rax
  00093	74 3c		 je	 SHORT $LN3@OnEvent
  00095	48 8b 44 24 20	 mov	 rax, QWORD PTR <begin>$L0$1[rsp]
  0009a	48 89 44 24 70	 mov	 QWORD PTR i$8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3453 :         return _Mypair._Myval2;

  0009f	48 8b 44 24 70	 mov	 rax, QWORD PTR i$8[rsp]
  000a4	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000a7	48 89 44 24 78	 mov	 QWORD PTR $T9[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp

; 65   : 			i->OnScheduleEvent(e);

  000ac	48 8b 44 24 78	 mov	 rax, QWORD PTR $T9[rsp]
  000b1	48 89 44 24 30	 mov	 QWORD PTR tv74[rsp], rax
  000b6	48 8b 44 24 30	 mov	 rax, QWORD PTR tv74[rsp]
  000bb	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000be	48 8b 94 24 98
	00 00 00	 mov	 rdx, QWORD PTR e$[rsp]
  000c6	48 8b 4c 24 30	 mov	 rcx, QWORD PTR tv74[rsp]
  000cb	ff 50 38	 call	 QWORD PTR [rax+56]
  000ce	90		 npad	 1

; 66   : 		}

  000cf	eb aa		 jmp	 SHORT $LN2@OnEvent
$LN3@OnEvent:

; 67   : 	}

  000d1	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  000d8	c3		 ret	 0
?OnEvent@EventScheduleManager@mu2@@QEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ENDP ; mu2::EventScheduleManager::OnEvent
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
;	COMDAT ?UnInit@EventScheduleManager@mu2@@UEAA_NXZ
_TEXT	SEGMENT
this$ = 8
?UnInit@EventScheduleManager@mu2@@UEAA_NXZ PROC		; mu2::EventScheduleManager::UnInit, COMDAT

; 56   : 	{

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 57   : 		return true;

  00005	b0 01		 mov	 al, 1

; 58   : 	}

  00007	c3		 ret	 0
?UnInit@EventScheduleManager@mu2@@UEAA_NXZ ENDP		; mu2::EventScheduleManager::UnInit
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
;	COMDAT ?Init@EventScheduleManager@mu2@@UEAA_NPEAX@Z
_TEXT	SEGMENT
<end>$L0$1 = 32
$T2 = 33
__formal$ = 40
tv267 = 48
<begin>$L1$3 = 56
<begin>$L0$4 = 64
<range>$L1$5 = 72
script$ = 80
ptr$6 = 88
$T7 = 96
tv151 = 104
$T8 = 112
$T9 = 120
$T10 = 128
$T11 = 136
map$12 = 144
<range>$L0$13 = 152
$T14 = 160
$T15 = 168
_Pnode$ = 176
$T16 = 184
iter$17 = 192
$T18 = 200
tv157 = 208
$T19 = 216
$T20 = 224
$T21 = 232
_Ptr$ = 240
$T22 = 248
$T23 = 256
_Ptr$ = 264
$T24 = 272
$T25 = 280
<end>$L1$26 = 288
ptr$27 = 296
$T28 = 304
_Result$29 = 312
_Result$30 = 320
this$ = 352
param$ = 360
?Init@EventScheduleManager@mu2@@UEAA_NPEAX@Z PROC	; mu2::EventScheduleManager::Init, COMDAT

; 23   : 	{

$LN384:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 81 ec 50 01
	00 00		 sub	 rsp, 336		; 00000150H
  00012	c7 44 24 60 00
	00 00 00	 mov	 DWORD PTR $T7[rsp], 0

; 27   : 		m_vecEventSchedulePtr.emplace_back(std::make_unique<OhrdorTreasureHunt>());

  0001a	48 8d 8c 24 80
	00 00 00	 lea	 rcx, QWORD PTR $T10[rsp]
  00022	e8 00 00 00 00	 call	 ??$make_unique@VOhrdorTreasureHunt@mu2@@$$V$0A@@std@@YA?AV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@0@XZ ; std::make_unique<mu2::OhrdorTreasureHunt,0>
  00027	48 89 44 24 68	 mov	 QWORD PTR tv151[rsp], rax
  0002c	48 8b 44 24 68	 mov	 rax, QWORD PTR tv151[rsp]
  00031	48 89 44 24 70	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00036	48 8b 44 24 70	 mov	 rax, QWORD PTR $T8[rsp]
  0003b	48 89 44 24 78	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 924  :         _Ty& _Result = _Emplace_one_at_back(_STD forward<_Valty>(_Val)...);

  00040	48 8b 44 24 78	 mov	 rax, QWORD PTR $T9[rsp]
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp

; 27   : 		m_vecEventSchedulePtr.emplace_back(std::make_unique<OhrdorTreasureHunt>());

  00045	48 8b 8c 24 60
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0004d	48 83 c1 08	 add	 rcx, 8
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 924  :         _Ty& _Result = _Emplace_one_at_back(_STD forward<_Valty>(_Val)...);

  00051	48 8b d0	 mov	 rdx, rax
  00054	e8 00 00 00 00	 call	 ??$_Emplace_one_at_back@V?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAAEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@$$QEAV?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@1@@Z ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Emplace_one_at_back<std::unique_ptr<mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt> > >
  00059	48 89 84 24 38
	01 00 00	 mov	 QWORD PTR _Result$29[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp

; 27   : 		m_vecEventSchedulePtr.emplace_back(std::make_unique<OhrdorTreasureHunt>());

  00061	48 8d 8c 24 80
	00 00 00	 lea	 rcx, QWORD PTR $T10[rsp]
  00069	e8 00 00 00 00	 call	 ??1?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@QEAA@XZ ; std::unique_ptr<mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt> >::~unique_ptr<mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt> >
  0006e	90		 npad	 1
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  0006f	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VGlobalLoadScript@mu2@@@mu2@@1VGlobalLoadScript@2@A ; mu2::ISingleton<mu2::GlobalLoadScript>::inst
  00076	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp

; 29   : 		auto script = SCRIPTS.GetScript<PVPFieldScript>();

  0007e	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  00086	48 8b c8	 mov	 rcx, rax
  00089	e8 00 00 00 00	 call	 ??$GetScript@VPVPFieldScript@mu2@@@GlobalLoadScript@mu2@@QEBAPEBVPVPFieldScript@1@XZ ; mu2::GlobalLoadScript::GetScript<mu2::PVPFieldScript>
  0008e	48 89 44 24 50	 mov	 QWORD PTR script$[rsp], rax

; 30   : 		if (script)

  00093	48 83 7c 24 50
	00		 cmp	 QWORD PTR script$[rsp], 0
  00099	0f 84 8d 01 00
	00		 je	 $LN8@Init

; 31   : 		{
; 32   : 			auto& map = script->GetPVPFieldInfoElemMap();

  0009f	48 8b 4c 24 50	 mov	 rcx, QWORD PTR script$[rsp]
  000a4	e8 00 00 00 00	 call	 ?GetPVPFieldInfoElemMap@PVPFieldScript@mu2@@QEBAAEBV?$map@GUPVPFieldInfoElem@mu2@@U?$less@G@std@@V?$allocator@U?$pair@$$CBGUPVPFieldInfoElem@mu2@@@std@@@4@@std@@XZ ; mu2::PVPFieldScript::GetPVPFieldInfoElemMap
  000a9	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR map$12[rsp], rax

; 33   : 			for (auto& iter : map)

  000b1	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR map$12[rsp]
  000b9	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR <range>$L0$13[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  000c1	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR <range>$L0$13[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  000c9	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T14[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  000d1	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR $T14[rsp]
  000d9	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T15[rsp], rax

; 1165 :         return _Unchecked_const_iterator(_Get_scary()->_Myhead->_Left, nullptr);

  000e1	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T15[rsp]
  000e9	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000ec	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000ef	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR _Pnode$[rsp], rax

; 37   :     _Tree_unchecked_const_iterator(_Nodeptr _Pnode, const _Mytree* _Plist) noexcept : _Ptr(_Pnode) {

  000f7	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR _Pnode$[rsp]
  000ff	48 89 44 24 40	 mov	 QWORD PTR <begin>$L0$4[rsp], rax

; 1169 :         return {};

  00104	48 8d 44 24 20	 lea	 rax, QWORD PTR <end>$L0$1[rsp]
  00109	48 8d 44 24 20	 lea	 rax, QWORD PTR <end>$L0$1[rsp]
  0010e	48 8b f8	 mov	 rdi, rax
  00111	33 c0		 xor	 eax, eax
  00113	b9 01 00 00 00	 mov	 ecx, 1
  00118	f3 aa		 rep stosb
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp

; 33   : 			for (auto& iter : map)

  0011a	eb 0b		 jmp	 SHORT $LN4@Init
$LN2@Init:
  0011c	48 8d 4c 24 40	 lea	 rcx, QWORD PTR <begin>$L0$4[rsp]
  00121	e8 00 00 00 00	 call	 ??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBGUPVPFieldInfoElem@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ ; std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned short const ,mu2::PVPFieldInfoElem> > >,std::_Iterator_base0>::operator++
  00126	90		 npad	 1
$LN4@Init:
  00127	0f b6 44 24 20	 movzx	 eax, BYTE PTR <end>$L0$1[rsp]
  0012c	88 44 24 28	 mov	 BYTE PTR __formal$[rsp], al
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 112  :         return !_Ptr->_Isnil;

  00130	48 8b 44 24 40	 mov	 rax, QWORD PTR <begin>$L0$4[rsp]
  00135	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  00139	85 c0		 test	 eax, eax
  0013b	75 0a		 jne	 SHORT $LN165@Init
  0013d	c7 44 24 30 01
	00 00 00	 mov	 DWORD PTR tv267[rsp], 1
  00145	eb 08		 jmp	 SHORT $LN166@Init
$LN165@Init:
  00147	c7 44 24 30 00
	00 00 00	 mov	 DWORD PTR tv267[rsp], 0
$LN166@Init:
  0014f	0f b6 44 24 30	 movzx	 eax, BYTE PTR tv267[rsp]
  00154	88 44 24 21	 mov	 BYTE PTR $T2[rsp], al
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp

; 33   : 			for (auto& iter : map)

  00158	0f b6 44 24 21	 movzx	 eax, BYTE PTR $T2[rsp]
  0015d	0f b6 c0	 movzx	 eax, al
  00160	85 c0		 test	 eax, eax
  00162	0f 84 c4 00 00
	00		 je	 $LN8@Init
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 42   :         return _Ptr->_Myval;

  00168	48 8b 44 24 40	 mov	 rax, QWORD PTR <begin>$L0$4[rsp]
  0016d	48 83 c0 20	 add	 rax, 32			; 00000020H
  00171	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T16[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp

; 33   : 			for (auto& iter : map)

  00179	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T16[rsp]
  00181	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR iter$17[rsp], rax

; 34   : 			{
; 35   : 				unique_ptr<PVPFieldSchedule> ptr = std::make_unique<PVPFieldSchedule>(&iter.second);

  00189	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR iter$17[rsp]
  00191	48 83 c0 08	 add	 rax, 8
  00195	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T18[rsp], rax
  0019d	48 8d 94 24 c8
	00 00 00	 lea	 rdx, QWORD PTR $T18[rsp]
  001a5	48 8d 8c 24 d8
	00 00 00	 lea	 rcx, QWORD PTR $T19[rsp]
  001ad	e8 00 00 00 00	 call	 ??$make_unique@VPVPFieldSchedule@mu2@@PEBUPVPFieldInfoElem@2@$0A@@std@@YA?AV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@0@$$QEAPEBUPVPFieldInfoElem@mu2@@@Z ; std::make_unique<mu2::PVPFieldSchedule,mu2::PVPFieldInfoElem const *,0>
  001b2	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR tv157[rsp], rax
  001ba	48 8b 94 24 d0
	00 00 00	 mov	 rdx, QWORD PTR tv157[rsp]
  001c2	48 8d 4c 24 58	 lea	 rcx, QWORD PTR ptr$6[rsp]
  001c7	e8 00 00 00 00	 call	 ??$?0U?$default_delete@VPVPFieldSchedule@mu2@@@std@@$0A@@?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@QEAA@$$QEAV01@@Z ; std::unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> >::unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> ><std::default_delete<mu2::PVPFieldSchedule>,0>
  001cc	90		 npad	 1
  001cd	48 8d 8c 24 d8
	00 00 00	 lea	 rcx, QWORD PTR $T19[rsp]
  001d5	e8 00 00 00 00	 call	 ??1?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@QEAA@XZ ; std::unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> >::~unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> >
  001da	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  001db	48 8d 44 24 58	 lea	 rax, QWORD PTR ptr$6[rsp]
  001e0	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR $T20[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp

; 36   : 				m_vecEventSchedulePtr.emplace_back(std::move(ptr));

  001e8	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR $T20[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  001f0	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR $T21[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 924  :         _Ty& _Result = _Emplace_one_at_back(_STD forward<_Valty>(_Val)...);

  001f8	48 8b 84 24 e8
	00 00 00	 mov	 rax, QWORD PTR $T21[rsp]
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp

; 36   : 				m_vecEventSchedulePtr.emplace_back(std::move(ptr));

  00200	48 8b 8c 24 60
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00208	48 83 c1 08	 add	 rcx, 8
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 924  :         _Ty& _Result = _Emplace_one_at_back(_STD forward<_Valty>(_Val)...);

  0020c	48 8b d0	 mov	 rdx, rax
  0020f	e8 00 00 00 00	 call	 ??$_Emplace_one_at_back@V?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAAEAV?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@1@$$QEAV?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@1@@Z ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Emplace_one_at_back<std::unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> > >
  00214	48 89 84 24 40
	01 00 00	 mov	 QWORD PTR _Result$30[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp

; 37   : 			}

  0021c	48 8d 4c 24 58	 lea	 rcx, QWORD PTR ptr$6[rsp]
  00221	e8 00 00 00 00	 call	 ??1?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@QEAA@XZ ; std::unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> >::~unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> >
  00226	90		 npad	 1
  00227	e9 f0 fe ff ff	 jmp	 $LN2@Init
$LN8@Init:

; 41   : 		for (auto& ptr : m_vecEventSchedulePtr)

  0022c	48 8b 84 24 60
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00234	48 83 c0 08	 add	 rax, 8
  00238	48 89 44 24 48	 mov	 QWORD PTR <range>$L1$5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1893 :         return _Unfancy_maybe_null(_Mypair._Myval2._Myfirst);

  0023d	48 8b 44 24 48	 mov	 rax, QWORD PTR <range>$L1$5[rsp]
  00242	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00245	48 89 84 24 f0
	00 00 00	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 80   :     return _Ptr;

  0024d	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00255	48 89 84 24 f8
	00 00 00	 mov	 QWORD PTR $T22[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1893 :         return _Unfancy_maybe_null(_Mypair._Myval2._Myfirst);

  0025d	48 8b 84 24 f8
	00 00 00	 mov	 rax, QWORD PTR $T22[rsp]
  00265	48 89 84 24 00
	01 00 00	 mov	 QWORD PTR $T23[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp

; 41   : 		for (auto& ptr : m_vecEventSchedulePtr)

  0026d	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR $T23[rsp]
  00275	48 89 44 24 38	 mov	 QWORD PTR <begin>$L1$3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1901 :         return _Unfancy_maybe_null(_Mypair._Myval2._Mylast);

  0027a	48 8b 44 24 48	 mov	 rax, QWORD PTR <range>$L1$5[rsp]
  0027f	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00283	48 89 84 24 08
	01 00 00	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 80   :     return _Ptr;

  0028b	48 8b 84 24 08
	01 00 00	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00293	48 89 84 24 10
	01 00 00	 mov	 QWORD PTR $T24[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1901 :         return _Unfancy_maybe_null(_Mypair._Myval2._Mylast);

  0029b	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR $T24[rsp]
  002a3	48 89 84 24 18
	01 00 00	 mov	 QWORD PTR $T25[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp

; 41   : 		for (auto& ptr : m_vecEventSchedulePtr)

  002ab	48 8b 84 24 18
	01 00 00	 mov	 rax, QWORD PTR $T25[rsp]
  002b3	48 89 84 24 20
	01 00 00	 mov	 QWORD PTR <end>$L1$26[rsp], rax
  002bb	eb 0e		 jmp	 SHORT $LN7@Init
$LN5@Init:
  002bd	48 8b 44 24 38	 mov	 rax, QWORD PTR <begin>$L1$3[rsp]
  002c2	48 83 c0 08	 add	 rax, 8
  002c6	48 89 44 24 38	 mov	 QWORD PTR <begin>$L1$3[rsp], rax
$LN7@Init:
  002cb	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR <end>$L1$26[rsp]
  002d3	48 39 44 24 38	 cmp	 QWORD PTR <begin>$L1$3[rsp], rax
  002d8	74 33		 je	 SHORT $LN6@Init
  002da	48 8b 44 24 38	 mov	 rax, QWORD PTR <begin>$L1$3[rsp]
  002df	48 89 84 24 28
	01 00 00	 mov	 QWORD PTR ptr$27[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3453 :         return _Mypair._Myval2;

  002e7	48 8b 84 24 28
	01 00 00	 mov	 rax, QWORD PTR ptr$27[rsp]
  002ef	48 8b 00	 mov	 rax, QWORD PTR [rax]
  002f2	48 89 84 24 30
	01 00 00	 mov	 QWORD PTR $T28[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp

; 43   : 			ptr->InitializeEventSchedule();

  002fa	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR $T28[rsp]
  00302	48 8b c8	 mov	 rcx, rax
  00305	e8 00 00 00 00	 call	 ?InitializeEventSchedule@EventSchedule@mu2@@QEAAXXZ ; mu2::EventSchedule::InitializeEventSchedule
  0030a	90		 npad	 1

; 44   : 		}

  0030b	eb b0		 jmp	 SHORT $LN5@Init
$LN6@Init:

; 45   : #else 
; 46   : 		std::unique_ptr<OhrdorTreasureHunt> OhrdorTreasureHunt_ptr = std::make_unique<OhrdorTreasureHunt>();
; 47   : 		VALID_RETURN(OhrdorTreasureHunt_ptr, false);
; 48   : 		OhrdorTreasureHunt_ptr->InitializeEventSchedule();
; 49   : 
; 50   : 		m_vecEventSchedulePtr.emplace_back(std::move(OhrdorTreasureHunt_ptr));
; 51   : #endif 
; 52   : 		return true;

  0030d	b0 01		 mov	 al, 1

; 53   : 	}

  0030f	48 81 c4 50 01
	00 00		 add	 rsp, 336		; 00000150H
  00316	5f		 pop	 rdi
  00317	c3		 ret	 0
?Init@EventScheduleManager@mu2@@UEAA_NPEAX@Z ENDP	; mu2::EventScheduleManager::Init
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
<end>$L0$1 = 32
$T2 = 33
__formal$ = 40
tv267 = 48
<begin>$L1$3 = 56
<begin>$L0$4 = 64
<range>$L1$5 = 72
script$ = 80
ptr$6 = 88
$T7 = 96
tv151 = 104
$T8 = 112
$T9 = 120
$T10 = 128
$T11 = 136
map$12 = 144
<range>$L0$13 = 152
$T14 = 160
$T15 = 168
_Pnode$ = 176
$T16 = 184
iter$17 = 192
$T18 = 200
tv157 = 208
$T19 = 216
$T20 = 224
$T21 = 232
_Ptr$ = 240
$T22 = 248
$T23 = 256
_Ptr$ = 264
$T24 = 272
$T25 = 280
<end>$L1$26 = 288
ptr$27 = 296
$T28 = 304
_Result$29 = 312
_Result$30 = 320
this$ = 352
param$ = 360
?dtor$0@?0??Init@EventScheduleManager@mu2@@UEAA_NPEAX@Z@4HA PROC ; `mu2::EventScheduleManager::Init'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8d 8d 80 00
	00 00		 lea	 rcx, QWORD PTR $T10[rbp]
  00010	e8 00 00 00 00	 call	 ??1?$unique_ptr@VOhrdorTreasureHunt@mu2@@U?$default_delete@VOhrdorTreasureHunt@mu2@@@std@@@std@@QEAA@XZ ; std::unique_ptr<mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt> >::~unique_ptr<mu2::OhrdorTreasureHunt,std::default_delete<mu2::OhrdorTreasureHunt> >
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$0@?0??Init@EventScheduleManager@mu2@@UEAA_NPEAX@Z@4HA ENDP ; `mu2::EventScheduleManager::Init'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
<end>$L0$1 = 32
$T2 = 33
__formal$ = 40
tv267 = 48
<begin>$L1$3 = 56
<begin>$L0$4 = 64
<range>$L1$5 = 72
script$ = 80
ptr$6 = 88
$T7 = 96
tv151 = 104
$T8 = 112
$T9 = 120
$T10 = 128
$T11 = 136
map$12 = 144
<range>$L0$13 = 152
$T14 = 160
$T15 = 168
_Pnode$ = 176
$T16 = 184
iter$17 = 192
$T18 = 200
tv157 = 208
$T19 = 216
$T20 = 224
$T21 = 232
_Ptr$ = 240
$T22 = 248
$T23 = 256
_Ptr$ = 264
$T24 = 272
$T25 = 280
<end>$L1$26 = 288
ptr$27 = 296
$T28 = 304
_Result$29 = 312
_Result$30 = 320
this$ = 352
param$ = 360
?dtor$2@?0??Init@EventScheduleManager@mu2@@UEAA_NPEAX@Z@4HA PROC ; `mu2::EventScheduleManager::Init'::`1'::dtor$2
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8d 4d 58	 lea	 rcx, QWORD PTR ptr$6[rbp]
  0000d	e8 00 00 00 00	 call	 ??1?$unique_ptr@VPVPFieldSchedule@mu2@@U?$default_delete@VPVPFieldSchedule@mu2@@@std@@@std@@QEAA@XZ ; std::unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> >::~unique_ptr<mu2::PVPFieldSchedule,std::default_delete<mu2::PVPFieldSchedule> >
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$2@?0??Init@EventScheduleManager@mu2@@UEAA_NPEAX@Z@4HA ENDP ; `mu2::EventScheduleManager::Init'::`1'::dtor$2
text$x	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
;	COMDAT ??1EventScheduleManager@mu2@@UEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1EventScheduleManager@mu2@@UEAA@XZ PROC		; mu2::EventScheduleManager::~EventScheduleManager, COMDAT

; 19   : 	{

$LN89:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7EventScheduleManager@mu2@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx

; 20   : 	}

  00018	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 83 c0 08	 add	 rax, 8
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 830  :         _Tidy();

  00021	48 8b c8	 mov	 rcx, rax
  00024	e8 00 00 00 00	 call	 ?_Tidy@?$vector@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@V?$allocator@V?$unique_ptr@VEventSchedule@mu2@@U?$default_delete@VEventSchedule@mu2@@@std@@@std@@@2@@std@@AEAAXXZ ; std::vector<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> >,std::allocator<std::unique_ptr<mu2::EventSchedule,std::default_delete<mu2::EventSchedule> > > >::_Tidy
  00029	90		 npad	 1
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 80   : 	virtual~ISingleton() {}

  0002a	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0002f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$ISingleton@VEventScheduleManager@mu2@@@mu2@@6B@
  00036	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp

; 20   : 	}

  00039	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0003d	c3		 ret	 0
??1EventScheduleManager@mu2@@UEAA@XZ ENDP		; mu2::EventScheduleManager::~EventScheduleManager
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
;	COMDAT ??0EventScheduleManager@mu2@@QEAA@XZ
_TEXT	SEGMENT
$T1 = 0
this$ = 8
this$ = 16
this$ = 24
this$ = 48
??0EventScheduleManager@mu2@@QEAA@XZ PROC		; mu2::EventScheduleManager::EventScheduleManager, COMDAT

; 15   : 	{

$LN32:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi
  00006	48 83 ec 20	 sub	 rsp, 32			; 00000020H
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 84   : 	ISingleton() {}	

  0000a	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$ISingleton@VEventScheduleManager@mu2@@@mu2@@6B@
  00016	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp

; 15   : 	{

  00019	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7EventScheduleManager@mu2@@6B@
  00025	48 89 08	 mov	 QWORD PTR [rax], rcx
  00028	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0002d	48 83 c0 08	 add	 rax, 8
  00031	48 89 44 24 10	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 670  :     _CONSTEXPR20 vector() noexcept(is_nothrow_default_constructible_v<_Alty>) : _Mypair(_Zero_then_variadic_args_t{}) {

  00036	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0003b	48 89 44 24 18	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1531 :         : _Ty1(), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  00040	48 8b 44 24 18	 mov	 rax, QWORD PTR this$[rsp]
  00045	48 89 44 24 08	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 400  :     _CONSTEXPR20 _Vector_val() noexcept : _Myfirst(), _Mylast(), _Myend() {}

  0004a	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0004f	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0
  00056	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0005b	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0
  00063	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00068	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 671  :         _Mypair._Myval2._Alloc_proxy(_GET_PROXY_ALLOCATOR(_Alty, _Getal()));

  00070	48 8d 04 24	 lea	 rax, QWORD PTR $T1[rsp]
  00074	48 8b f8	 mov	 rdi, rax
  00077	33 c0		 xor	 eax, eax
  00079	b9 01 00 00 00	 mov	 ecx, 1
  0007e	f3 aa		 rep stosb
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp

; 16   : 	}

  00080	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00085	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00089	5f		 pop	 rdi
  0008a	c3		 ret	 0
??0EventScheduleManager@mu2@@QEAA@XZ ENDP		; mu2::EventScheduleManager::EventScheduleManager
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??_G?$ISingleton@VEventScheduleManager@mu2@@@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_G?$ISingleton@VEventScheduleManager@mu2@@@mu2@@UEAAPEAXI@Z PROC ; mu2::ISingleton<mu2::EventScheduleManager>::`scalar deleting destructor', COMDAT
$LN10:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 80   : 	virtual~ISingleton() {}

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$ISingleton@VEventScheduleManager@mu2@@@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx
  0001c	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00020	83 e0 01	 and	 eax, 1
  00023	85 c0		 test	 eax, eax
  00025	74 10		 je	 SHORT $LN2@scalar
  00027	ba 08 00 00 00	 mov	 edx, 8
  0002c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00031	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00036	90		 npad	 1
$LN2@scalar:
  00037	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003c	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00040	c3		 ret	 0
??_G?$ISingleton@VEventScheduleManager@mu2@@@mu2@@UEAAPEAXI@Z ENDP ; mu2::ISingleton<mu2::EventScheduleManager>::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
_TEXT	SEGMENT
_Ptr_container$ = 48
_Block_size$ = 56
_Ptr$ = 64
$T1 = 72
_Bytes$ = 96
??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z PROC ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>, COMDAT

; 182  : __declspec(allocator) void* _Allocate_manually_vector_aligned(const size_t _Bytes) {

$LN7:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 183  :     // allocate _Bytes manually aligned to at least _Big_allocation_alignment
; 184  :     const size_t _Block_size = _Non_user_size + _Bytes;

  00009	48 8b 44 24 60	 mov	 rax, QWORD PTR _Bytes$[rsp]
  0000e	48 83 c0 27	 add	 rax, 39			; 00000027H
  00012	48 89 44 24 38	 mov	 QWORD PTR _Block_size$[rsp], rax

; 185  :     if (_Block_size <= _Bytes) {

  00017	48 8b 44 24 60	 mov	 rax, QWORD PTR _Bytes$[rsp]
  0001c	48 39 44 24 38	 cmp	 QWORD PTR _Block_size$[rsp], rax
  00021	77 06		 ja	 SHORT $LN2@Allocate_m

; 186  :         _Throw_bad_array_new_length(); // add overflow

  00023	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00028	90		 npad	 1
$LN2@Allocate_m:

; 136  :         return ::operator new(_Bytes);

  00029	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Block_size$[rsp]
  0002e	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00033	48 89 44 24 48	 mov	 QWORD PTR $T1[rsp], rax

; 187  :     }
; 188  : 
; 189  :     const uintptr_t _Ptr_container = reinterpret_cast<uintptr_t>(_Traits::_Allocate(_Block_size));

  00038	48 8b 44 24 48	 mov	 rax, QWORD PTR $T1[rsp]
  0003d	48 89 44 24 30	 mov	 QWORD PTR _Ptr_container$[rsp], rax

; 190  :     _STL_VERIFY(_Ptr_container != 0, "invalid argument"); // validate even in release since we're doing p[-1]

  00042	48 83 7c 24 30
	00		 cmp	 QWORD PTR _Ptr_container$[rsp], 0
  00048	75 19		 jne	 SHORT $LN3@Allocate_m
  0004a	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR [rsp+32], 0
  00053	45 33 c9	 xor	 r9d, r9d
  00056	45 33 c0	 xor	 r8d, r8d
  00059	33 d2		 xor	 edx, edx
  0005b	33 c9		 xor	 ecx, ecx
  0005d	e8 00 00 00 00	 call	 _invoke_watson
  00062	90		 npad	 1
$LN3@Allocate_m:

; 191  :     void* const _Ptr = reinterpret_cast<void*>((_Ptr_container + _Non_user_size) & ~(_Big_allocation_alignment - 1));

  00063	48 8b 44 24 30	 mov	 rax, QWORD PTR _Ptr_container$[rsp]
  00068	48 83 c0 27	 add	 rax, 39			; 00000027H
  0006c	48 83 e0 e0	 and	 rax, -32		; ffffffffffffffe0H
  00070	48 89 44 24 40	 mov	 QWORD PTR _Ptr$[rsp], rax

; 192  :     static_cast<uintptr_t*>(_Ptr)[-1] = _Ptr_container;

  00075	b8 08 00 00 00	 mov	 eax, 8
  0007a	48 6b c0 ff	 imul	 rax, rax, -1
  0007e	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  00083	48 8b 54 24 30	 mov	 rdx, QWORD PTR _Ptr_container$[rsp]
  00088	48 89 14 01	 mov	 QWORD PTR [rcx+rax], rdx

; 193  : 
; 194  : #ifdef _DEBUG
; 195  :     static_cast<uintptr_t*>(_Ptr)[-2] = _Big_allocation_sentinel;
; 196  : #endif // defined(_DEBUG)
; 197  :     return _Ptr;

  0008c	48 8b 44 24 40	 mov	 rax, QWORD PTR _Ptr$[rsp]
$LN4@Allocate_m:

; 198  : }

  00091	48 83 c4 58	 add	 rsp, 88			; 00000058H
  00095	c3		 ret	 0
??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ENDP ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z
_TEXT	SEGMENT
_Back_shift$ = 48
_Ptr_container$ = 56
_Ptr_user$ = 64
_Min_back_shift$ = 72
_Ptr$ = 96
_Bytes$ = 104
?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z PROC ; std::_Adjust_manually_vector_aligned, COMDAT

; 200  : inline void _Adjust_manually_vector_aligned(void*& _Ptr, size_t& _Bytes) {

$LN5:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 201  :     // adjust parameters from _Allocate_manually_vector_aligned to pass to operator delete
; 202  :     _Bytes += _Non_user_size;

  0000e	48 8b 44 24 68	 mov	 rax, QWORD PTR _Bytes$[rsp]
  00013	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00016	48 83 c0 27	 add	 rax, 39			; 00000027H
  0001a	48 8b 4c 24 68	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  0001f	48 89 01	 mov	 QWORD PTR [rcx], rax

; 203  : 
; 204  :     const uintptr_t* const _Ptr_user = static_cast<uintptr_t*>(_Ptr);

  00022	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00027	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0002a	48 89 44 24 40	 mov	 QWORD PTR _Ptr_user$[rsp], rax

; 205  :     const uintptr_t _Ptr_container   = _Ptr_user[-1];

  0002f	b8 08 00 00 00	 mov	 eax, 8
  00034	48 6b c0 ff	 imul	 rax, rax, -1
  00038	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr_user$[rsp]
  0003d	48 8b 04 01	 mov	 rax, QWORD PTR [rcx+rax]
  00041	48 89 44 24 38	 mov	 QWORD PTR _Ptr_container$[rsp], rax

; 206  : 
; 207  :     // If the following asserts, it likely means that we are performing
; 208  :     // an aligned delete on memory coming from an unaligned allocation.
; 209  :     _STL_ASSERT(_Ptr_user[-2] == _Big_allocation_sentinel, "invalid argument");
; 210  : 
; 211  :     // Extra paranoia on aligned allocation/deallocation; ensure _Ptr_container is
; 212  :     // in range [_Min_back_shift, _Non_user_size]
; 213  : #ifdef _DEBUG
; 214  :     constexpr uintptr_t _Min_back_shift = 2 * sizeof(void*);
; 215  : #else // ^^^ defined(_DEBUG) / !defined(_DEBUG) vvv
; 216  :     constexpr uintptr_t _Min_back_shift = sizeof(void*);

  00046	48 c7 44 24 48
	08 00 00 00	 mov	 QWORD PTR _Min_back_shift$[rsp], 8

; 217  : #endif // ^^^ !defined(_DEBUG) ^^^
; 218  :     const uintptr_t _Back_shift = reinterpret_cast<uintptr_t>(_Ptr) - _Ptr_container;

  0004f	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00054	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr_container$[rsp]
  00059	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0005c	48 2b c1	 sub	 rax, rcx
  0005f	48 89 44 24 30	 mov	 QWORD PTR _Back_shift$[rsp], rax

; 219  :     _STL_VERIFY(_Back_shift >= _Min_back_shift && _Back_shift <= _Non_user_size, "invalid argument");

  00064	48 83 7c 24 30
	08		 cmp	 QWORD PTR _Back_shift$[rsp], 8
  0006a	72 08		 jb	 SHORT $LN3@Adjust_man
  0006c	48 83 7c 24 30
	27		 cmp	 QWORD PTR _Back_shift$[rsp], 39 ; 00000027H
  00072	76 19		 jbe	 SHORT $LN2@Adjust_man
$LN3@Adjust_man:
  00074	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR [rsp+32], 0
  0007d	45 33 c9	 xor	 r9d, r9d
  00080	45 33 c0	 xor	 r8d, r8d
  00083	33 d2		 xor	 edx, edx
  00085	33 c9		 xor	 ecx, ecx
  00087	e8 00 00 00 00	 call	 _invoke_watson
  0008c	90		 npad	 1
$LN2@Adjust_man:

; 220  :     _Ptr = reinterpret_cast<void*>(_Ptr_container);

  0008d	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00092	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr_container$[rsp]
  00097	48 89 08	 mov	 QWORD PTR [rax], rcx
$LN4@Adjust_man:

; 221  : }

  0009a	48 83 c4 58	 add	 rsp, 88			; 00000058H
  0009e	c3		 ret	 0
?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ENDP ; std::_Adjust_manually_vector_aligned
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?_Throw_bad_array_new_length@std@@YAXXZ
_TEXT	SEGMENT
$T1 = 32
?_Throw_bad_array_new_length@std@@YAXXZ PROC		; std::_Throw_bad_array_new_length, COMDAT

; 107  : [[noreturn]] inline void _Throw_bad_array_new_length() {

$LN3:
  00000	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 108  :     _THROW(bad_array_new_length{});

  00004	48 8d 4c 24 20	 lea	 rcx, QWORD PTR $T1[rsp]
  00009	e8 00 00 00 00	 call	 ??0bad_array_new_length@std@@QEAA@XZ ; std::bad_array_new_length::bad_array_new_length
  0000e	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:_TI3?AVbad_array_new_length@std@@
  00015	48 8d 4c 24 20	 lea	 rcx, QWORD PTR $T1[rsp]
  0001a	e8 00 00 00 00	 call	 _CxxThrowException
  0001f	90		 npad	 1
$LN2@Throw_bad_:

; 109  : }

  00020	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00024	c3		 ret	 0
?_Throw_bad_array_new_length@std@@YAXXZ ENDP		; std::_Throw_bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gbad_array_new_length@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gbad_array_new_length@std@@UEAAPEAXI@Z PROC		; std::bad_array_new_length::`scalar deleting destructor', COMDAT
$LN20:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gbad_array_new_length@std@@UEAAPEAXI@Z ENDP		; std::bad_array_new_length::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??0bad_array_new_length@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
__that$ = 56
??0bad_array_new_length@std@@QEAA@AEBV01@@Z PROC	; std::bad_array_new_length::bad_array_new_length, COMDAT
$LN14:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000e	48 8b 54 24 38	 mov	 rdx, QWORD PTR __that$[rsp]
  00013	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00018	e8 00 00 00 00	 call	 ??0bad_alloc@std@@QEAA@AEBV01@@Z
  0001d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00022	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_array_new_length@std@@6B@
  00029	48 89 08	 mov	 QWORD PTR [rax], rcx
  0002c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00031	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00035	c3		 ret	 0
??0bad_array_new_length@std@@QEAA@AEBV01@@Z ENDP	; std::bad_array_new_length::bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??1bad_array_new_length@std@@UEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1bad_array_new_length@std@@UEAA@XZ PROC		; std::bad_array_new_length::~bad_array_new_length, COMDAT
$LN14:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  00018	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 83 c0 08	 add	 rax, 8
  00021	48 8b c8	 mov	 rcx, rax
  00024	e8 00 00 00 00	 call	 __std_exception_destroy
  00029	90		 npad	 1
  0002a	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0002e	c3		 ret	 0
??1bad_array_new_length@std@@UEAA@XZ ENDP		; std::bad_array_new_length::~bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0bad_array_new_length@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 16
??0bad_array_new_length@std@@QEAA@XZ PROC		; std::bad_array_new_length::bad_array_new_length, COMDAT

; 144  :     {

$LN14:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi

; 67   :     {

  00006	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0000b	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00012	48 89 08	 mov	 QWORD PTR [rax], rcx

; 66   :         : _Data()

  00015	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 83 c0 08	 add	 rax, 8
  0001e	48 8b f8	 mov	 rdi, rax
  00021	33 c0		 xor	 eax, eax
  00023	b9 10 00 00 00	 mov	 ecx, 16
  00028	f3 aa		 rep stosb

; 68   :         _Data._What = _Message;

  0002a	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0002f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_0BF@KINCDENJ@bad?5array?5new?5length@
  00036	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 133  :     {

  0003a	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0003f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_alloc@std@@6B@
  00046	48 89 08	 mov	 QWORD PTR [rax], rcx

; 144  :     {

  00049	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_array_new_length@std@@6B@
  00055	48 89 08	 mov	 QWORD PTR [rax], rcx

; 145  :     }

  00058	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0005d	5f		 pop	 rdi
  0005e	c3		 ret	 0
??0bad_array_new_length@std@@QEAA@XZ ENDP		; std::bad_array_new_length::bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gbad_alloc@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gbad_alloc@std@@UEAAPEAXI@Z PROC			; std::bad_alloc::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gbad_alloc@std@@UEAAPEAXI@Z ENDP			; std::bad_alloc::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0bad_alloc@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
__that$ = 56
??0bad_alloc@std@@QEAA@AEBV01@@Z PROC			; std::bad_alloc::bad_alloc, COMDAT
$LN9:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 20	 sub	 rsp, 32			; 00000020H

; 73   :     {

  0000f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  0001b	48 89 08	 mov	 QWORD PTR [rax], rcx

; 72   :         : _Data()

  0001e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 83 c0 08	 add	 rax, 8
  00027	48 8b f8	 mov	 rdi, rax
  0002a	33 c0		 xor	 eax, eax
  0002c	b9 10 00 00 00	 mov	 ecx, 16
  00031	f3 aa		 rep stosb

; 74   :         __std_exception_copy(&_Other._Data, &_Data);

  00033	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 83 c0 08	 add	 rax, 8
  0003c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR __that$[rsp]
  00041	48 83 c1 08	 add	 rcx, 8
  00045	48 8b d0	 mov	 rdx, rax
  00048	e8 00 00 00 00	 call	 __std_exception_copy
  0004d	90		 npad	 1
  0004e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_alloc@std@@6B@
  0005a	48 89 08	 mov	 QWORD PTR [rax], rcx
  0005d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00062	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00066	5f		 pop	 rdi
  00067	c3		 ret	 0
??0bad_alloc@std@@QEAA@AEBV01@@Z ENDP			; std::bad_alloc::bad_alloc
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gexception@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gexception@std@@UEAAPEAXI@Z PROC			; std::exception::`scalar deleting destructor', COMDAT
$LN10:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gexception@std@@UEAAPEAXI@Z ENDP			; std::exception::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ?what@exception@std@@UEBAPEBDXZ
_TEXT	SEGMENT
tv69 = 0
this$ = 32
?what@exception@std@@UEBAPEBDXZ PROC			; std::exception::what, COMDAT

; 95   :     {

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 18	 sub	 rsp, 24

; 96   :         return _Data._What ? _Data._What : "Unknown exception";

  00009	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00013	74 0f		 je	 SHORT $LN3@what
  00015	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0001e	48 89 04 24	 mov	 QWORD PTR tv69[rsp], rax
  00022	eb 0b		 jmp	 SHORT $LN4@what
$LN3@what:
  00024	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BC@EOODALEL@Unknown?5exception@
  0002b	48 89 04 24	 mov	 QWORD PTR tv69[rsp], rax
$LN4@what:
  0002f	48 8b 04 24	 mov	 rax, QWORD PTR tv69[rsp]

; 97   :     }

  00033	48 83 c4 18	 add	 rsp, 24
  00037	c3		 ret	 0
?what@exception@std@@UEBAPEBDXZ ENDP			; std::exception::what
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0exception@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
_Other$ = 56
??0exception@std@@QEAA@AEBV01@@Z PROC			; std::exception::exception, COMDAT

; 73   :     {

$LN4:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  0000f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  0001b	48 89 08	 mov	 QWORD PTR [rax], rcx

; 72   :         : _Data()

  0001e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 83 c0 08	 add	 rax, 8
  00027	48 8b f8	 mov	 rdi, rax
  0002a	33 c0		 xor	 eax, eax
  0002c	b9 10 00 00 00	 mov	 ecx, 16
  00031	f3 aa		 rep stosb

; 74   :         __std_exception_copy(&_Other._Data, &_Data);

  00033	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 83 c0 08	 add	 rax, 8
  0003c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Other$[rsp]
  00041	48 83 c1 08	 add	 rcx, 8
  00045	48 8b d0	 mov	 rdx, rax
  00048	e8 00 00 00 00	 call	 __std_exception_copy
  0004d	90		 npad	 1

; 75   :     }

  0004e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00057	5f		 pop	 rdi
  00058	c3		 ret	 0
??0exception@std@@QEAA@AEBV01@@Z ENDP			; std::exception::exception
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\EventScheduleManager.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
