; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	?SetType@GlobalGameConfigInfo@mu2@@QEAA_NW4GameConfigType@2@@Z ; mu2::GlobalGameConfigInfo::SetType
PUBLIC	?Apply@GlobalGameConfigInfo@mu2@@QEAA_NI_K@Z	; mu2::GlobalGameConfigInfo::Apply
PUBLIC	?Release@GlobalGameConfigInfo@mu2@@QEAA_NI_K@Z	; mu2::GlobalGameConfigInfo::Release
PUBLIC	?Pack@GlobalGameConfigInfo@mu2@@QEBA_NAEAUGameConfigInfo@2@@Z ; mu2::GlobalGameConfigInfo::Pack
PUBLIC	?GetValue@GlobalGameConfigInfo@mu2@@IEBAIXZ	; mu2::GlobalGameConfigInfo::GetValue
PUBLIC	?GetValue64@GlobalGameConfigInfo@mu2@@IEBA_KXZ	; mu2::GlobalGameConfigInfo::GetValue64
PUBLIC	??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@ ; `string'
PUBLIC	??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@		; `string'
PUBLIC	??_C@_0FD@JEACNNLO@F?3?2Release_Branch?2Server?2Develo@ ; `string'
PUBLIC	??_C@_0BL@NHKBCFHD@type?5?$DM?5GameConfigType?3?3MAX@ ; `string'
PUBLIC	??_C@_0CD@FGAEJMND@mu2?3?3GlobalGameConfigInfo?3?3SetT@ ; `string'
PUBLIC	??_C@_0BL@JDCMGIHI@m_GameConfigInfo?4IsValid?$CI?$CJ@ ; `string'
PUBLIC	??_C@_0CA@NJBMKNGL@mu2?3?3GlobalGameConfigInfo?3?3Pack@ ; `string'
EXTRN	__imp_GetStdHandle:PROC
EXTRN	__imp_SetConsoleTextAttribute:PROC
EXTRN	?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ:PROC	; mu2::Logger::Logging
;	COMDAT pdata
pdata	SEGMENT
$pdata$?SetType@GlobalGameConfigInfo@mu2@@QEAA_NW4GameConfigType@2@@Z DD imagerel $LN8
	DD	imagerel $LN8+260
	DD	imagerel $unwind$?SetType@GlobalGameConfigInfo@mu2@@QEAA_NW4GameConfigType@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Apply@GlobalGameConfigInfo@mu2@@QEAA_NI_K@Z DD imagerel $LN5
	DD	imagerel $LN5+108
	DD	imagerel $unwind$?Apply@GlobalGameConfigInfo@mu2@@QEAA_NI_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Release@GlobalGameConfigInfo@mu2@@QEAA_NI_K@Z DD imagerel $LN7
	DD	imagerel $LN7+144
	DD	imagerel $unwind$?Release@GlobalGameConfigInfo@mu2@@QEAA_NI_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Pack@GlobalGameConfigInfo@mu2@@QEBA_NAEAUGameConfigInfo@2@@Z DD imagerel $LN18
	DD	imagerel $LN18+317
	DD	imagerel $unwind$?Pack@GlobalGameConfigInfo@mu2@@QEBA_NAEAUGameConfigInfo@2@@Z
pdata	ENDS
;	COMDAT ??_C@_0CA@NJBMKNGL@mu2?3?3GlobalGameConfigInfo?3?3Pack@
CONST	SEGMENT
??_C@_0CA@NJBMKNGL@mu2?3?3GlobalGameConfigInfo?3?3Pack@ DB 'mu2::GlobalGa'
	DB	'meConfigInfo::Pack', 00H			; `string'
CONST	ENDS
;	COMDAT ??_C@_0BL@JDCMGIHI@m_GameConfigInfo?4IsValid?$CI?$CJ@
CONST	SEGMENT
??_C@_0BL@JDCMGIHI@m_GameConfigInfo?4IsValid?$CI?$CJ@ DB 'm_GameConfigInf'
	DB	'o.IsValid()', 00H				; `string'
CONST	ENDS
;	COMDAT ??_C@_0CD@FGAEJMND@mu2?3?3GlobalGameConfigInfo?3?3SetT@
CONST	SEGMENT
??_C@_0CD@FGAEJMND@mu2?3?3GlobalGameConfigInfo?3?3SetT@ DB 'mu2::GlobalGa'
	DB	'meConfigInfo::SetType', 00H			; `string'
CONST	ENDS
;	COMDAT ??_C@_0BL@NHKBCFHD@type?5?$DM?5GameConfigType?3?3MAX@
CONST	SEGMENT
??_C@_0BL@NHKBCFHD@type?5?$DM?5GameConfigType?3?3MAX@ DB 'type < GameConf'
	DB	'igType::MAX', 00H				; `string'
CONST	ENDS
;	COMDAT ??_C@_0FD@JEACNNLO@F?3?2Release_Branch?2Server?2Develo@
CONST	SEGMENT
??_C@_0FD@JEACNNLO@F?3?2Release_Branch?2Server?2Develo@ DB 'F:\Release_Br'
	DB	'anch\Server\Development\Frontend\WorldServer\GlobalGameConfig'
	DB	'Info.cpp', 00H				; `string'
CONST	ENDS
;	COMDAT ??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
CONST	SEGMENT
??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@ DB 'g', 00H, 'a', 00H, 'm', 00H, 'e'
	DB	00H, 00H, 00H				; `string'
CONST	ENDS
;	COMDAT ??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
CONST	SEGMENT
??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@ DB '%'
	DB	's> ASSERT - %s, %s(%d)', 00H		; `string'
CONST	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Pack@GlobalGameConfigInfo@mu2@@QEBA_NAEAUGameConfigInfo@2@@Z DD 021101H
	DD	0110111H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Release@GlobalGameConfigInfo@mu2@@QEAA_NI_K@Z DD 011201H
	DD	06212H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Apply@GlobalGameConfigInfo@mu2@@QEAA_NI_K@Z DD 011201H
	DD	06212H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?SetType@GlobalGameConfigInfo@mu2@@QEAA_NW4GameConfigType@2@@Z DD 010d01H
	DD	0e20dH
xdata	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\GlobalGameConfigInfo.cpp
;	COMDAT ?GetValue64@GlobalGameConfigInfo@mu2@@IEBA_KXZ
_TEXT	SEGMENT
this$ = 8
?GetValue64@GlobalGameConfigInfo@mu2@@IEBA_KXZ PROC	; mu2::GlobalGameConfigInfo::GetValue64, COMDAT

; 41   : {

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 42   : 	return m_GameConfigInfo.valueOverwrite;

  00005	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0000a	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]

; 43   : }

  0000e	c3		 ret	 0
?GetValue64@GlobalGameConfigInfo@mu2@@IEBA_KXZ ENDP	; mu2::GlobalGameConfigInfo::GetValue64
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\GlobalGameConfigInfo.cpp
;	COMDAT ?GetValue@GlobalGameConfigInfo@mu2@@IEBAIXZ
_TEXT	SEGMENT
this$ = 8
?GetValue@GlobalGameConfigInfo@mu2@@IEBAIXZ PROC	; mu2::GlobalGameConfigInfo::GetValue, COMDAT

; 36   : {

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 37   : 	return m_GameConfigInfo.value;

  00005	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0000a	8b 40 0c	 mov	 eax, DWORD PTR [rax+12]

; 38   : }

  0000d	c3		 ret	 0
?GetValue@GlobalGameConfigInfo@mu2@@IEBAIXZ ENDP	; mu2::GlobalGameConfigInfo::GetValue
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\GlobalGameConfigInfo.cpp
; File F:\Release_Branch\Shared\Protocol\Common.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\GlobalGameConfigInfo.cpp
;	COMDAT ?Pack@GlobalGameConfigInfo@mu2@@QEBA_NAEAUGameConfigInfo@2@@Z
_TEXT	SEGMENT
$T1 = 96
tv92 = 100
__that$ = 104
hConsole$2 = 112
this$ = 144
out$ = 152
?Pack@GlobalGameConfigInfo@mu2@@QEBA_NAEAUGameConfigInfo@2@@Z PROC ; mu2::GlobalGameConfigInfo::Pack, COMDAT

; 46   : {

$LN18:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H
; File F:\Release_Branch\Shared\Protocol\Common.h

; 4252 : 		return type < GameConfigType::MAX;

  00011	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00019	0f b6 40 08	 movzx	 eax, BYTE PTR [rax+8]
  0001d	83 f8 08	 cmp	 eax, 8
  00020	7d 0a		 jge	 SHORT $LN6@Pack
  00022	c7 44 24 64 01
	00 00 00	 mov	 DWORD PTR tv92[rsp], 1
  0002a	eb 08		 jmp	 SHORT $LN7@Pack
$LN6@Pack:
  0002c	c7 44 24 64 00
	00 00 00	 mov	 DWORD PTR tv92[rsp], 0
$LN7@Pack:
  00034	0f b6 44 24 64	 movzx	 eax, BYTE PTR tv92[rsp]
  00039	88 44 24 60	 mov	 BYTE PTR $T1[rsp], al
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\GlobalGameConfigInfo.cpp

; 47   : 	VERIFY_RETURN(m_GameConfigInfo.IsValid(), false);

  0003d	0f b6 44 24 60	 movzx	 eax, BYTE PTR $T1[rsp]
  00042	0f b6 c0	 movzx	 eax, al
  00045	85 c0		 test	 eax, eax
  00047	0f 85 9d 00 00
	00		 jne	 $LN2@Pack
  0004d	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  00052	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  00058	48 89 44 24 70	 mov	 QWORD PTR hConsole$2[rsp], rax
  0005d	66 ba 0d 00	 mov	 dx, 13
  00061	48 8b 4c 24 70	 mov	 rcx, QWORD PTR hConsole$2[rsp]
  00066	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  0006c	c7 44 24 50 2f
	00 00 00	 mov	 DWORD PTR [rsp+80], 47	; 0000002fH
  00074	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FD@JEACNNLO@F?3?2Release_Branch?2Server?2Develo@
  0007b	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  00080	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BL@JDCMGIHI@m_GameConfigInfo?4IsValid?$CI?$CJ@
  00087	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  0008c	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CA@NJBMKNGL@mu2?3?3GlobalGameConfigInfo?3?3Pack@
  00093	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  00098	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  0009f	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  000a4	c7 44 24 28 2f
	00 00 00	 mov	 DWORD PTR [rsp+40], 47	; 0000002fH
  000ac	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FD@JEACNNLO@F?3?2Release_Branch?2Server?2Develo@
  000b3	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  000b8	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CA@NJBMKNGL@mu2?3?3GlobalGameConfigInfo?3?3Pack@
  000bf	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  000c5	ba 02 00 00 00	 mov	 edx, 2
  000ca	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  000d1	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  000d6	66 ba 07 00	 mov	 dx, 7
  000da	48 8b 4c 24 70	 mov	 rcx, QWORD PTR hConsole$2[rsp]
  000df	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000e5	90		 npad	 1
  000e6	32 c0		 xor	 al, al
  000e8	eb 4b		 jmp	 SHORT $LN1@Pack
$LN2@Pack:

; 48   : 	out = m_GameConfigInfo;

  000ea	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000f2	48 89 44 24 68	 mov	 QWORD PTR __that$[rsp], rax
  000f7	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR out$[rsp]
  000ff	48 8b 4c 24 68	 mov	 rcx, QWORD PTR __that$[rsp]
  00104	0f b6 49 08	 movzx	 ecx, BYTE PTR [rcx+8]
  00108	88 48 08	 mov	 BYTE PTR [rax+8], cl
  0010b	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR out$[rsp]
  00113	48 8b 4c 24 68	 mov	 rcx, QWORD PTR __that$[rsp]
  00118	8b 49 0c	 mov	 ecx, DWORD PTR [rcx+12]
  0011b	89 48 0c	 mov	 DWORD PTR [rax+12], ecx
  0011e	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR out$[rsp]
  00126	48 8b 4c 24 68	 mov	 rcx, QWORD PTR __that$[rsp]
  0012b	48 8b 49 10	 mov	 rcx, QWORD PTR [rcx+16]
  0012f	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 49   : 	return true;

  00133	b0 01		 mov	 al, 1
$LN1@Pack:

; 50   : }

  00135	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  0013c	c3		 ret	 0
?Pack@GlobalGameConfigInfo@mu2@@QEBA_NAEAUGameConfigInfo@2@@Z ENDP ; mu2::GlobalGameConfigInfo::Pack
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\GlobalGameConfigInfo.cpp
;	COMDAT ?Release@GlobalGameConfigInfo@mu2@@QEAA_NI_K@Z
_TEXT	SEGMENT
tv74 = 32
tv80 = 36
prev$ = 40
this$ = 64
value$ = 72
valueOverwrite$ = 80
?Release@GlobalGameConfigInfo@mu2@@QEAA_NI_K@Z PROC	; mu2::GlobalGameConfigInfo::Release, COMDAT

; 23   : {

$LN7:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00009	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000e	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 24   : 	UInt32 prev = GetValue();

  00012	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00017	e8 00 00 00 00	 call	 ?GetValue@GlobalGameConfigInfo@mu2@@IEBAIXZ ; mu2::GlobalGameConfigInfo::GetValue
  0001c	89 44 24 28	 mov	 DWORD PTR prev$[rsp], eax

; 25   : 	m_GameConfigInfo.value = (m_GameConfigInfo.value < value ) ? 0 : m_GameConfigInfo.value - value;

  00020	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00025	8b 4c 24 48	 mov	 ecx, DWORD PTR value$[rsp]
  00029	39 48 0c	 cmp	 DWORD PTR [rax+12], ecx
  0002c	73 0a		 jae	 SHORT $LN3@Release
  0002e	c7 44 24 20 00
	00 00 00	 mov	 DWORD PTR tv74[rsp], 0
  00036	eb 12		 jmp	 SHORT $LN4@Release
$LN3@Release:
  00038	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0003d	8b 4c 24 48	 mov	 ecx, DWORD PTR value$[rsp]
  00041	8b 40 0c	 mov	 eax, DWORD PTR [rax+12]
  00044	2b c1		 sub	 eax, ecx
  00046	89 44 24 20	 mov	 DWORD PTR tv74[rsp], eax
$LN4@Release:
  0004a	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0004f	8b 4c 24 20	 mov	 ecx, DWORD PTR tv74[rsp]
  00053	89 48 0c	 mov	 DWORD PTR [rax+12], ecx

; 26   : 	m_GameConfigInfo.valueOverwrite = valueOverwrite;

  00056	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0005b	48 8b 4c 24 50	 mov	 rcx, QWORD PTR valueOverwrite$[rsp]
  00060	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 27   : 	return prev != GetValue();

  00064	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00069	e8 00 00 00 00	 call	 ?GetValue@GlobalGameConfigInfo@mu2@@IEBAIXZ ; mu2::GlobalGameConfigInfo::GetValue
  0006e	39 44 24 28	 cmp	 DWORD PTR prev$[rsp], eax
  00072	74 0a		 je	 SHORT $LN5@Release
  00074	c7 44 24 24 01
	00 00 00	 mov	 DWORD PTR tv80[rsp], 1
  0007c	eb 08		 jmp	 SHORT $LN6@Release
$LN5@Release:
  0007e	c7 44 24 24 00
	00 00 00	 mov	 DWORD PTR tv80[rsp], 0
$LN6@Release:
  00086	0f b6 44 24 24	 movzx	 eax, BYTE PTR tv80[rsp]

; 28   : }

  0008b	48 83 c4 38	 add	 rsp, 56			; 00000038H
  0008f	c3		 ret	 0
?Release@GlobalGameConfigInfo@mu2@@QEAA_NI_K@Z ENDP	; mu2::GlobalGameConfigInfo::Release
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\GlobalGameConfigInfo.cpp
;	COMDAT ?Apply@GlobalGameConfigInfo@mu2@@QEAA_NI_K@Z
_TEXT	SEGMENT
tv76 = 32
prev$ = 36
this$ = 64
value$ = 72
valueOverwrite$ = 80
?Apply@GlobalGameConfigInfo@mu2@@QEAA_NI_K@Z PROC	; mu2::GlobalGameConfigInfo::Apply, COMDAT

; 15   : {

$LN5:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00009	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000e	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 16   : 	UInt32 prev = GetValue();

  00012	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00017	e8 00 00 00 00	 call	 ?GetValue@GlobalGameConfigInfo@mu2@@IEBAIXZ ; mu2::GlobalGameConfigInfo::GetValue
  0001c	89 44 24 24	 mov	 DWORD PTR prev$[rsp], eax

; 17   : 	m_GameConfigInfo.value += value;

  00020	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00025	8b 40 0c	 mov	 eax, DWORD PTR [rax+12]
  00028	03 44 24 48	 add	 eax, DWORD PTR value$[rsp]
  0002c	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00031	89 41 0c	 mov	 DWORD PTR [rcx+12], eax

; 18   : 	m_GameConfigInfo.valueOverwrite = valueOverwrite;

  00034	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00039	48 8b 4c 24 50	 mov	 rcx, QWORD PTR valueOverwrite$[rsp]
  0003e	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 19   : 	return prev != m_GameConfigInfo.value;

  00042	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00047	8b 40 0c	 mov	 eax, DWORD PTR [rax+12]
  0004a	39 44 24 24	 cmp	 DWORD PTR prev$[rsp], eax
  0004e	74 0a		 je	 SHORT $LN3@Apply
  00050	c7 44 24 20 01
	00 00 00	 mov	 DWORD PTR tv76[rsp], 1
  00058	eb 08		 jmp	 SHORT $LN4@Apply
$LN3@Apply:
  0005a	c7 44 24 20 00
	00 00 00	 mov	 DWORD PTR tv76[rsp], 0
$LN4@Apply:
  00062	0f b6 44 24 20	 movzx	 eax, BYTE PTR tv76[rsp]

; 20   : }

  00067	48 83 c4 38	 add	 rsp, 56			; 00000038H
  0006b	c3		 ret	 0
?Apply@GlobalGameConfigInfo@mu2@@QEAA_NI_K@Z ENDP	; mu2::GlobalGameConfigInfo::Apply
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\GlobalGameConfigInfo.cpp
; File F:\Release_Branch\Shared\Protocol\Common.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\GlobalGameConfigInfo.cpp
;	COMDAT ?SetType@GlobalGameConfigInfo@mu2@@QEAA_NW4GameConfigType@2@@Z
_TEXT	SEGMENT
$T1 = 96
tv91 = 100
hConsole$2 = 104
this$ = 128
type$ = 136
?SetType@GlobalGameConfigInfo@mu2@@QEAA_NW4GameConfigType@2@@Z PROC ; mu2::GlobalGameConfigInfo::SetType, COMDAT

; 8    : {

$LN8:
  00000	88 54 24 10	 mov	 BYTE PTR [rsp+16], dl
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 78	 sub	 rsp, 120		; 00000078H

; 9    : 	VERIFY_RETURN(type < GameConfigType::MAX, false);

  0000d	0f b6 84 24 88
	00 00 00	 movzx	 eax, BYTE PTR type$[rsp]
  00015	83 f8 08	 cmp	 eax, 8
  00018	0f 8c 9d 00 00
	00		 jl	 $LN2@SetType
  0001e	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  00023	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  00029	48 89 44 24 68	 mov	 QWORD PTR hConsole$2[rsp], rax
  0002e	66 ba 0d 00	 mov	 dx, 13
  00032	48 8b 4c 24 68	 mov	 rcx, QWORD PTR hConsole$2[rsp]
  00037	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  0003d	c7 44 24 50 09
	00 00 00	 mov	 DWORD PTR [rsp+80], 9
  00045	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FD@JEACNNLO@F?3?2Release_Branch?2Server?2Develo@
  0004c	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  00051	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BL@NHKBCFHD@type?5?$DM?5GameConfigType?3?3MAX@
  00058	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  0005d	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CD@FGAEJMND@mu2?3?3GlobalGameConfigInfo?3?3SetT@
  00064	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  00069	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  00070	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  00075	c7 44 24 28 09
	00 00 00	 mov	 DWORD PTR [rsp+40], 9
  0007d	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FD@JEACNNLO@F?3?2Release_Branch?2Server?2Develo@
  00084	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  00089	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CD@FGAEJMND@mu2?3?3GlobalGameConfigInfo?3?3SetT@
  00090	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  00096	ba 02 00 00 00	 mov	 edx, 2
  0009b	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  000a2	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  000a7	66 ba 07 00	 mov	 dx, 7
  000ab	48 8b 4c 24 68	 mov	 rcx, QWORD PTR hConsole$2[rsp]
  000b0	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000b6	90		 npad	 1
  000b7	32 c0		 xor	 al, al
  000b9	eb 44		 jmp	 SHORT $LN1@SetType
$LN2@SetType:

; 10   : 	m_GameConfigInfo.type = type;

  000bb	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000c3	0f b6 8c 24 88
	00 00 00	 movzx	 ecx, BYTE PTR type$[rsp]
  000cb	88 48 08	 mov	 BYTE PTR [rax+8], cl
; File F:\Release_Branch\Shared\Protocol\Common.h

; 4252 : 		return type < GameConfigType::MAX;

  000ce	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000d6	0f b6 40 08	 movzx	 eax, BYTE PTR [rax+8]
  000da	83 f8 08	 cmp	 eax, 8
  000dd	7d 0a		 jge	 SHORT $LN6@SetType
  000df	c7 44 24 64 01
	00 00 00	 mov	 DWORD PTR tv91[rsp], 1
  000e7	eb 08		 jmp	 SHORT $LN7@SetType
$LN6@SetType:
  000e9	c7 44 24 64 00
	00 00 00	 mov	 DWORD PTR tv91[rsp], 0
$LN7@SetType:
  000f1	0f b6 44 24 64	 movzx	 eax, BYTE PTR tv91[rsp]
  000f6	88 44 24 60	 mov	 BYTE PTR $T1[rsp], al
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\GlobalGameConfigInfo.cpp

; 11   : 	return m_GameConfigInfo.IsValid();

  000fa	0f b6 44 24 60	 movzx	 eax, BYTE PTR $T1[rsp]
$LN1@SetType:

; 12   : }

  000ff	48 83 c4 78	 add	 rsp, 120		; 00000078H
  00103	c3		 ret	 0
?SetType@GlobalGameConfigInfo@mu2@@QEAA_NW4GameConfigType@2@@Z ENDP ; mu2::GlobalGameConfigInfo::SetType
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\GlobalGameConfigInfo.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\GlobalGameConfigInfo.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
