﻿#include "stdafx.h"
#include "ActionPlayerTalisman.h"
#include <Backend/Zone/Transaction/ItemTransaction/ItemTalismanTransaction.h>
#include <Backend/Zone/ZoneServer.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerCombatPower.h>
#include <Backend/Zone/Action/ActionQuest.h>
#include <Backend/Zone/Action/ActionAchievement.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerSeason.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerCognition.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerTalismanUnit.h>
#include <Backend/Zone/Action/ActionPlayerLoad.h>
#include <Mu2Common/CommandString.h>
#include <Backend/Zone/Element/Item/ItemFinder.h>
#ifdef NEW_SKILL_DECAY_by_kangms_180710
#include <Backend/Zone/Action/ActionBuff.h>
#endif//NEW_SKILL_DECAY_by_kangms_180710

namespace mu2
{
	ActionPlayerTalisman::ActionPlayerTalisman(EntityPlayer* owner)
		: Action4Player(owner, ID)
		, m_vecTalismanCollectionInfoWithCharInfo()
		, m_curDriverPageOffset(0)
		, m_curDriverSlotCount(0)
		, m_talismans()
		, m_talismanSkillUsedCount()
		, m_curTalismanPoint(0)
		, m_bookmarkTalismans()
		, m_totalCombatPower(0)
		, m_isAbailableTalisman(false)
		, m_isUseableSkill(false)
		, m_maxTalismanPoint(0)
		, m_isChangeableDriver(true)
#ifdef __Patch_Talisman_V2_Awaken_by_kangms_20181105
		, m_isAbailableTalismanAwaken(false)
#endif//__Patch_Talisman_V2_Awaken_by_kangms_20181105
		, m_isSendToClientTalismanPoint(false)
	{
		CounterInc("ActionPlayerTalisman");

		for (Byte pageOffset = 0; pageOffset < Limits::MAX_TALISMAN_SLOT_PAGE; ++pageOffset)
		{
			DriverPage driverPage;

			for (Byte slotOffset = 0; slotOffset < Limits::MAX_TALISMAN_SLOT; ++slotOffset)
			{
				driverPage[slotOffset].slotPageNumber = pageOffset;
				driverPage[slotOffset].slotNumber = slotOffset;
				driverPage[slotOffset].talismanIndex = 0;
				driverPage[slotOffset].talismanSkillIndex = 0;
			}
			m_driver.emplace(pageOffset, driverPage);
		}
	}

	ActionPlayerTalisman::~ActionPlayerTalisman()
	{
		CounterDec("ActionPlayerTalisman");
	}
	
	ErrorTalisman::Error ActionPlayerTalisman::createTalisman(const IndexTalisman index)
	{
		EntityPlayer* player = GetOwnerPlayer();
		VERIFY_RETURN(player && player->IsValid(), ErrorTalisman::playerNotFound);

		//=========================================================================
		// 탈리스만 컨텐츠 활성화 여부 체크
		//=========================================================================

		if (false == isAbailableTalisman())
		{
			SendSystemMsg(player, SystemMessage(L"sys", L"Msg_Talisman_Contents_Block"));
			return ErrorTalisman::NOT_COMPLETE_TALISMAN_QUEST;
		}

		//=========================================================================
		// 이미 존재하는 탈리스만이면 실패
		//=========================================================================

		VERIFY_RETURN(false == IsHasTalisman(index), ErrorTalisman::ALREADY_EXIST_TALISMAN);

		const TalismanInfoElem* talismanInfoElem = SCRIPTS.GetScript<TalismanInfoScript>()->Get(index);
		VERIFY_RETURN(talismanInfoElem, ErrorTalisman::NOT_FIND_TALISMAN_SCRIPT);

		TalismanMaterialKey key(index, TalismanMaterialType::GET, 0);
		const TalismanMaterialElem* talismanMaterialElem = SCRIPTS.GetScript<TalismanMaterialScript>()->Get(key);
		VERIFY_RETURN(talismanMaterialElem, ErrorTalisman::NOT_FIND_MATERIAL_SCRIPT);

		std::map<IndexItem, Int32> checkMaterialItems;
		std::vector<TalismanMaterialItemInfo> useMaterialItemInfos;
		//ActionPlayerInventory& invenAction = player->GetInventoryAction();

		for (auto iterMaterial : talismanMaterialElem->materialItems)
		{
			// 인벤토리 검색 순서는 인벤 -> 개인창고 -> 계정창고
			// 인벤토리에서 재료아이템을 찾아서 IndexItem 별로 수량 정보를 만들어 준다. (checkMaterialItems)
			// 재료 아이템 삭제 트랜잭션 처리를 위해 해당 정보도 같이 세팅 한다. (useMaterialItemInfos)

			// 인벤토리
			Items materialItems;			
			ItemFinderByIndex(*player, iterMaterial.first).Find(TalismanBags, materialItems);
			//invenAction.FindItem(TalismanBags, iterMaterial.first, &materialItems);

			for (auto materialItem : materialItems)
			{
				VALID_DO(materialItem, continue);
#ifdef __lockable_item_181001
				VALID_DO(materialItem->IsUnLocakble(), continue);
#endif
				Int32 useCount = 0;
				const ItemData& itemData = *materialItem;
				auto iterCheckMaterial = checkMaterialItems.find(itemData.indexItem);
				if (checkMaterialItems.end() == iterCheckMaterial)
				{
					useCount = (iterMaterial.second > itemData.GetCurStackCount()) ? itemData.GetCurStackCount() : iterMaterial.second;
					checkMaterialItems.emplace(itemData.indexItem, useCount);
				}
				else
				{
					VALID_DO(iterMaterial.second != iterCheckMaterial->second, break);
					useCount = (iterMaterial.second - iterCheckMaterial->second > itemData.GetCurStackCount()) ? itemData.GetCurStackCount() : (iterMaterial.second - iterCheckMaterial->second);
					iterCheckMaterial->second += useCount;
				}

				const TalismanMaterialItemInfo useInfo(itemData.itemId, itemData.indexItem, itemData.GetPos(), useCount);
				useMaterialItemInfos.push_back(useInfo);
			}
		}

		// 필요 재료 아이템 인덱스 및 수량 체크
		for (auto iterMaterial : talismanMaterialElem->materialItems)
		{
			IndexItem itemIndex = iterMaterial.first;
			Int32 count = iterMaterial.second;

			auto iter = checkMaterialItems.find(itemIndex);
			VERIFY_RETURN(checkMaterialItems.end() != iter, ErrorTalisman::NOT_ENOUGH_MATERIAL_ITEM);

			VERIFY_RETURN(count == iter->second, ErrorTalisman::NOT_ENOUGH_MATERIAL_ITEM);
		}

		// 비용 체크
		
		Zen costZen = 0;
		GreenZen costGreenZen = 0;

		for (auto iterCost : talismanMaterialElem->materialCosts)
		{
			if (TalismanMaterialCostType::ZEN == iterCost.first)
			{	
				VERIFY_RETURN(player->IsEnoughZen(iterCost.second), ErrorTalisman::NOT_ENOUGH_ZEN);
				costZen = iterCost.second;
			}
			else if (TalismanMaterialCostType::GREENZEN == iterCost.first)
			{
				VERIFY_RETURN(player->IsEnoughGreenZen(iterCost.second), ErrorTalisman::NOT_ENOUGH_GREENZEN);
				costGreenZen = iterCost.second;
			}
		}

		// 신규 탈리스만 정보 생성, 실제 insert는 DB 트랜잭션 성공 후 처리
		TalismanInfo newTalisman;
		newTalisman.index = index;
		this->calculateCollectionPointAndCombatPower(__out newTalisman);

		// 트랜잭션 처리

		ItemCreateTalismanTransaction* trans = new ItemCreateTalismanTransaction(__FUNCTION__, __LINE__, player, LogCode::E_CREATE_TALISMAN, newTalisman, useMaterialItemInfos);
		TransactionPtr transPtr(trans);
		{	
			trans->SetCost(costZen, costGreenZen);

			VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorTalisman::FAIL_TRANSACTION);
		}

		// 실제 탈리스만 정보 생성 및 삽입은 재료 아이템 트랜잭션 처리 후 적용
		return ErrorTalisman::SUCCESS;
	}


	ErrorTalisman::Error ActionPlayerTalisman::OnReqLimitBreakTalisman(EventPtr& e)
	{
		// 탈리스만 한계돌파
		EntityPlayer* owner = GetOwnerPlayer();
		VERIFY_RETURN(owner && owner->IsValid(), ErrorTalisman::playerNotFound);

		EReqLimitBreakTalisman* req = static_cast<EReqLimitBreakTalisman*>(e.RawPtr());

		ErrorTalisman::Error eError = limitBreakTalisman(req->talismanIndex);
		if (ErrorTalisman::SUCCESS != eError)
		{
			EResLimitBreakTalisman* res = NEW EResLimitBreakTalisman;
			res->eError = eError;
			owner->SendToClient(EventPtr(res));
		}

		return ErrorTalisman::SUCCESS;
	}

	ErrorTalisman::Error ActionPlayerTalisman::growTalisman(const IndexTalisman index, const std::map<IndexItem, Int32>& mapMaterialItems)
	{
		EntityPlayer* player = GetOwnerPlayer();
		VERIFY_RETURN(player && player->IsValid(), ErrorTalisman::playerNotFound);

		// 보유하지 않은 탈리스만을 성장 시도하면 에러
		
		const TalismanInfo* found = findTalisman(index);
		VALID_RETURN(found, ErrorTalisman::NOT_EXIST_TALISMAN);

		// DB 트랜잭션 성공 후 업데이트 되는 탈리스만 정보
		TalismanInfo talismanCloned = *found;

		const TalismanInfoElem* talismanInfoElem = SCRIPTS.GetScript<TalismanInfoScript>()->Get(index);
		VERIFY_RETURN(talismanInfoElem, ErrorTalisman::NOT_FIND_TALISMAN_SCRIPT);

		// 탈리스만 성장 가능 최대 레벨 계산 공식
		const UInt16 maxLevel = talismanInfoElem->maxLevel + (talismanCloned.limitBreak * talismanInfoElem->limitIncrease);
		VERIFY_RETURN(talismanCloned.level < maxLevel, ErrorTalisman::ALREADY_MAX_LEVEL);

		std::map<IndexItem, Int32> usedMaterialItems;
		std::vector<TalismanMaterialItemInfo> usedTalismanMaterialItemInfos;
		//ActionPlayerInventory& invenAction = player->GetInventoryAction();

		for (auto iterMaterial : mapMaterialItems)
		{
			// 인벤토리 검색 순서는 인벤 -> 개인창고 -> 계정창고
			// 인벤토리에서 재료아이템을 찾아서 IndexItem 별로 수량 정보를 만들어 준다. (checkMaterialItems)
			// 재료 아이템 삭제 트랜잭션 처리를 위해 해당 정보도 같이 세팅 한다. (useMaterialItemInfos)

			IndexItem requireMaterialIndex = iterMaterial.first;
			Int32 requireMaterialCount = iterMaterial.second;


			VALID_DO(talismanCloned.level < maxLevel, break);

			

			Items foundMaterialItems;
			ItemFinderByIndex(*player, requireMaterialIndex).Find(TalismanBags, foundMaterialItems);
			//invenAction.FindItem(TalismanBags, requireMaterialIndex, &foundMaterialItems);

			VERIFY_RETURN(foundMaterialItems.size(), ErrorTalisman::NOT_FIND_MATERIAL_ITEM);

			for (const ItemConstPtr& foundMaterial : foundMaterialItems)
			{
				VALID_DO(foundMaterial, continue);
#ifdef __lockable_item_181001
				VALID_DO( foundMaterial->IsUnLocakble(), continue );
#endif
				VALID_DO(talismanCloned.level < maxLevel, break);

				Int32 useCount = 0;
				
				auto iterUsedMaterial = usedMaterialItems.find(foundMaterial->GetItemIndex());
				if (usedMaterialItems.end() == iterUsedMaterial)
				{
					useCount = (requireMaterialCount > foundMaterial->GetCurStackCount()) ? foundMaterial->GetCurStackCount() : requireMaterialCount;
					usedMaterialItems.emplace(foundMaterial->GetItemIndex(), useCount);
				}
				else
				{
					VALID_DO(requireMaterialCount != iterUsedMaterial->second, break);
					useCount = (requireMaterialCount - iterUsedMaterial->second > foundMaterial->GetCurStackCount()) ? foundMaterial->GetCurStackCount() : (requireMaterialCount - iterUsedMaterial->second);
					iterUsedMaterial->second += useCount;
				}

				// TalismanGrowKey 값에서 division, itemIndex 둘 중 하나는 0이다.
				// division 값이 0인 경우에만 itemIndex를 가지고 판단 한다.
				TalismanGrowKey key(foundMaterial->GetDivision(), 0);
				const TalismanGrowElem* talismanGrowElem = SCRIPTS.GetScript<TalismanGrowScript>()->Get(key);
				if (nullptr == talismanGrowElem)
				{
					key.division = ItemDivisionType::NONE;
					key.itemIndex = foundMaterial->GetItemIndex();
					talismanGrowElem = SCRIPTS.GetScript<TalismanGrowScript>()->Get(key);
					VERIFY_RETURN(talismanGrowElem, ErrorTalisman::INVALID_MATERIAL_ITEM);
				}

				// 재료로 사용되는 아이템 개수만큼 exp 증가
				talismanCloned.exp += (talismanGrowElem->exp * useCount);

				const TalismanMaterialItemInfo useInfo(foundMaterial->GetItemId(), foundMaterial->GetItemIndex(), foundMaterial->GetPos(), useCount);
				usedTalismanMaterialItemInfos.push_back(useInfo);

				// 탈리스만 레벨 계산
				calculateTalismanLevel(talismanCloned);
			}
		}

		// 도감 점수, 전투력 계산
		const UInt32 oldCollectionPoint = talismanCloned.collectionPoint;
		const UInt32 oldCombatPower = talismanCloned.combatPower;
		this->calculateCollectionPointAndCombatPower(talismanCloned);

		// 트랜잭션 처리
		TransactionPtr transPtr = std::make_shared<ItemGrowTalismanTransaction>(
			__FUNCTION__, __LINE__
			, player, LogCode::E_GROW_TALISMAN, talismanCloned, usedTalismanMaterialItemInfos, oldCollectionPoint, oldCombatPower);
		{
			VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorTalisman::FAIL_TRANSACTION);
		}

		return ErrorTalisman::SUCCESS;
	}

	ErrorTalisman::Error ActionPlayerTalisman::limitBreakTalisman(const IndexTalisman index)
	{
		EntityPlayer* player = GetOwnerPlayer();
		VERIFY_RETURN(player && player->IsValid(), ErrorTalisman::playerNotFound);

		// 보유하지 않은 탈리스만을 한계돌파 시도하면 에러

		const TalismanInfo* found = findTalisman(index);
		VALID_RETURN(found, ErrorTalisman::NOT_EXIST_TALISMAN);

		// DB 트랜잭션 성공 후 업데이트 되는 탈리스만 정보
		TalismanInfo talismanCloned = *found;

		const TalismanInfoElem* talismanInfoElem = SCRIPTS.GetScript<TalismanInfoScript>()->Get(index);
		VERIFY_RETURN(talismanInfoElem, ErrorTalisman::NOT_FIND_TALISMAN_SCRIPT);

		// 최대 가능한 한계돌파 횟수 체크
		VALID_RETURN( talismanCloned.limitBreak < talismanInfoElem->maxLimitBreak, ErrorTalisman::ALREADY_MAX_LIMIT_BREAK);

		TalismanMaterialKey key(index, TalismanMaterialType::LIMIT_BREAK, talismanCloned.limitBreak);
		const TalismanMaterialElem* talismanMaterialElem = SCRIPTS.GetScript<TalismanMaterialScript>()->Get(key);
		VERIFY_RETURN(talismanMaterialElem, ErrorTalisman::NOT_FIND_MATERIAL_SCRIPT);

		std::map<IndexItem, Int32> usedMaterialItems;
		std::vector<TalismanMaterialItemInfo> usedTalismanMaterialItemInfos;
		
		for (auto iterMaterial : talismanMaterialElem->materialItems)
		{
			// 인벤토리 검색 순서는 인벤 -> 개인창고 -> 계정창고
			// 인벤토리에서 재료아이템을 찾아서 IndexItem 별로 수량 정보를 만들어 준다. (checkMaterialItems)
			// 재료 아이템 삭제 트랜잭션 처리를 위해 해당 정보도 같이 세팅 한다. (useMaterialItemInfos)

			// 인벤토리
			Items foundMaterialItems;			
			ItemFinderByIndex(*player, iterMaterial.first).Find(TalismanBags, foundMaterialItems);
			//player->GetInventoryAction().FindItem(TalismanBags, iterMaterial.first, &foundMaterialItems);

			for (const ItemConstPtr& foundMaterial : foundMaterialItems)
			{
				VERIFY_DO(foundMaterial, continue);

				Int32 useCount = 0;
				const ItemData& itemData = *foundMaterial;
				auto iterCheckMaterial = usedMaterialItems.find(itemData.indexItem);
				if (usedMaterialItems.end() == iterCheckMaterial)
				{
					useCount = (iterMaterial.second > itemData.GetCurStackCount()) ? itemData.GetCurStackCount() : iterMaterial.second;
					usedMaterialItems.emplace(itemData.indexItem, useCount);
				}
				else
				{
					VERIFY_DO(iterMaterial.second != iterCheckMaterial->second, break);
					useCount = (iterMaterial.second - iterCheckMaterial->second > itemData.GetCurStackCount()) ? itemData.GetCurStackCount() : (iterMaterial.second - iterCheckMaterial->second);
					iterCheckMaterial->second += useCount;
				}

				const TalismanMaterialItemInfo useInfo(itemData.itemId, itemData.indexItem, itemData.GetPos(), useCount);
				usedTalismanMaterialItemInfos.push_back(useInfo);
			}
		}

		// 필요 재료 아이템 인덱스 및 수량 체크
		for (auto iterMaterial : talismanMaterialElem->materialItems)
		{
			auto iter = usedMaterialItems.find(iterMaterial.first);
			VERIFY_RETURN(usedMaterialItems.end() != iter, ErrorTalisman::NOT_ENOUGH_MATERIAL_ITEM);
			VERIFY_RETURN(iterMaterial.second == iter->second, ErrorTalisman::NOT_ENOUGH_MATERIAL_ITEM);
		}

		// 비용 체크
		
		for (auto iterCost : talismanMaterialElem->materialCosts)
		{
			if (TalismanMaterialCostType::ZEN == iterCost.first)
			{
				VERIFY_RETURN(player->IsEnoughZen(iterCost.second), ErrorTalisman::NOT_ENOUGH_ZEN);
			}
			else if (TalismanMaterialCostType::GREENZEN == iterCost.first)
			{
				VERIFY_RETURN(player->IsEnoughGreenZen(iterCost.second), ErrorTalisman::NOT_ENOUGH_GREENZEN);
			}
		}

		// 한계돌파 횟수 증가 및 도감 점수, 전투력 계산
		const UInt32 oldCollectionPoint = talismanCloned.collectionPoint;
		const UInt32 oldCombatPower = talismanCloned.combatPower;
		talismanCloned.limitBreak++;
		this->calculateCollectionPointAndCombatPower(talismanCloned);

		// 트랜잭션 처리
		TransactionPtr transPtr = std::make_shared<ItemLimitBreakTalismanTransaction>(__FUNCTION__, __LINE__, player, LogCode::E_LIMIT_BREAK_TALISMAN, talismanCloned, usedTalismanMaterialItemInfos, oldCollectionPoint, oldCombatPower);
		{
			VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorTalisman::FAIL_TRANSACTION);
		}		

		return ErrorTalisman::SUCCESS;
	}
	
	Bool ActionPlayerTalisman::InsertTalisman(const TalismanInfo& talismanInfo, const Bool isDbLoad)
	{
		EntityPlayer* owner = GetOwnerPlayer();
		VERIFY_RETURN(owner && owner->IsValid(), false);

		const TalismanInfoElem* talismanInfoElem = SCRIPTS.GetScript<TalismanInfoScript>()->Get(talismanInfo.index);
		VERIFY_RETURN(talismanInfoElem, false);

		VALID_RETURN(addTalismanInfo(talismanInfo), false);

		// 스킬 사용 횟수 관리를 위한 컨테이너에 데이터 삽입
		if (m_talismanSkillUsedCount.end() == m_talismanSkillUsedCount.find(talismanInfoElem->skillIndex))
		{
			// m_talismanSkills.end()가 아닌 경우는 복층 던전 이동 시 삽입 되었다고 판단한다.
			m_talismanSkillUsedCount.emplace(talismanInfoElem->skillIndex, 0);
		}

		do 
		{
			// PC가 사용 가능한 Active Skill로 등록해 줘야 한다.
			const SkillInfoElem* skillInfoElem = SCRIPTS.GetSkillInfoScript(talismanInfoElem->skillIndex);
			VALID_DO(skillInfoElem, break);

			Skill* created = owner->GetSkillAttr().CreateSkillAndRegist(*skillInfoElem);
			VALID_DO(created, break);

		} while (false);

		if (false == isDbLoad)
		{
			createAchievementAndSeason(talismanInfo.index);
		}

		ActionPlayerTalismanUnit& actPlayerTalismanUnit = GetOwnerPlayer()->GetPlayerTalismanUnitAction();
		actPlayerTalismanUnit.CheckAndInsert(talismanInfo.index, isDbLoad);

		return true;
	}

	Bool ActionPlayerTalisman::UpdateTalisman(const TalismanInfo& talismanInfo)
	{
		const TalismanInfoElem* talismanInfoElem = SCRIPTS.GetScript<TalismanInfoScript>()->Get(talismanInfo.index);
		VERIFY_RETURN(talismanInfoElem, false);
		
		VALID_RETURN(updateTalismanInfo(talismanInfo), false);

		ActionPlayerTalismanUnit& actPlayerTalismanUnit = GetOwnerPlayer()->GetPlayerTalismanUnitAction();
		actPlayerTalismanUnit.CheckAndUpdate(talismanInfo.index);

		return true;
	}
	
	void ActionPlayerTalisman::UpdateCollection(const UInt32 subPoint, const UInt32 addPoint, const std::wstring& fromAction, const Bool isDbLoad)
	{
		VALID_RETURN(subPoint != addPoint, );

		const UInt16 oldHighestCollectionLevel = getHighestCollectionLevelInAccount();
		const UInt32 oldHighestCollectionCombatPower = getHighestCollectionCombatPowerInAccount();

		m_collection.totalCollectionPoint -= subPoint;
		m_collection.totalCollectionPoint += addPoint;

		auto script = SCRIPTS.GetScript<TalismanCollectionLevelScript>();
		const TalismanCollectionLevelElem* oldLevelElem = script->Get(GetCollectionLevel());
		VALID_RETURN(oldLevelElem && oldLevelElem->collectionPoint, );
		
		const TalismanCollectionLevelElem* finalLevelElem = calculateCollectionLevel(GetTotalCollectionPoint());
		VERIFY_RETURN(finalLevelElem, );

		m_collection.combatPower = finalLevelElem->combatPower;
		m_collection.level = finalLevelElem->collectionLevel;

		if (false == isDbLoad)
		{
			for (TalismanCollectionLevel lv = oldLevelElem->collectionLevel + 1; lv <= finalLevelElem->collectionLevel; ++lv)
			{
				onCollectionLevelUp(lv);
			}
		}


		// 도감이 제공하는 전투력 업데이트, 도감 정보를 확인해서 능력치 및 드라이버 증가 처리
		AppendTotalCombatPower(oldHighestCollectionCombatPower, getHighestCollectionCombatPowerInAccount());
		updateAbilityByHighestCollectonLevel(oldHighestCollectionLevel, getHighestCollectionLevelInAccount());
		updateDriverSlotCount(isDbLoad);

		if (false == isDbLoad)
		{
			checkCollectionPointSeason(GetTotalCollectionPoint());

#ifdef __Patch_Talisman_V2_Awaken_by_kangms_20181105
			this->Send_TalismanCollection();
#else//__Patch_Talisman_V2_Awaken_by_kangms_20181105
			// DB에서 로드된 정보가 아닌 경우 도감 정보가 갱신 되었다면 업데이트 정보를 클라에 전송한다.
			auto ntf = NEW ENtfUpdateTalismanCollection;
			ntf->talismanCollectionInfo = m_collection;
			ntf->totalCombatPower = m_totalCombatPower;
			ntf->curDriverSlotCount = GetCurrnetDriverSlotCount();
			SERVER.SendToClient(GetOwnerPlayer(), EventPtr(ntf));
#endif//__Patch_Talisman_V2_Awaken_by_kangms_20181105

			if (subPoint < addPoint)
			{
				auto log = NEW EReqLogDb2;
				EventSetter::FillLogForEntity(LogCode::E_GET_TALISMAN_COLLECTION_POINT, GetOwnerPlayer(), log->action);
				log->action.subInfo = fromAction;
				log->action.var32s.push_back(static_cast<Int32>(GetCollectionLevel()));
				log->action.var32s.push_back(static_cast<Int32>(addPoint - subPoint));
				log->action.var32s.push_back(static_cast<Int32>(GetTotalCollectionPoint()));
				SendDataCenter(EventPtr(log));
			}
		}
	}

#ifdef __Patch_Talisman_V2_Awaken_by_kangms_20181105
	void ActionPlayerTalisman::Send_TalismanCollection()
	{
		auto ntf = NEW ENtfUpdateTalismanCollection;
		ntf->talismanCollectionInfo = m_collection;
		ntf->totalCombatPower = m_totalCombatPower;
		ntf->curDriverSlotCount = this->GetCurrnetDriverSlotCount();
		SERVER.SendToClient(this->GetOwnerPlayer(), EventPtr(ntf));
	}
#endif//__Patch_Talisman_V2_Awaken_by_kangms_20181105

	ErrorTalisman::Error ActionPlayerTalisman::checkDriverSlotAndReqDbUpdate(const IndexTalisman talismanIndex, const Byte pageOffset, const Byte slotOffset) const
	{
		// 탈리스만 스킬을 한번이라도 사용했다면 변경 불가
		VALID_RETURN(m_isChangeableDriver, ErrorTalisman::ALREADY_USE_SKILL);

		// 현재 사용 가능한 드라이버 슬롯 개수 체크
		VALID_RETURN(GetCurrnetDriverSlotCount() > slotOffset, ErrorTalisman::INVALID_DRIVER_SLOT_SIZE);

		// 드라이버 슬롯 및 슬롯 페이지 사이즈 유효성 체크
		VALID_RETURN(Limits::MAX_TALISMAN_SLOT_PAGE > pageOffset, ErrorTalisman::INVALID_DRIVER_SLOT_PAGE_SIZE);
		VALID_RETURN(Limits::MAX_TALISMAN_SLOT > slotOffset, ErrorTalisman::INVALID_DRIVER_SLOT_SIZE);

		// talismanIndex가 0인 경우는 장착 해제하는 경우
		Bool isEquipSameSkill = false;
		Byte sameSkillSlotNum = 0;
		if (0 != talismanIndex)
		{
			const TalismanInfoElem* talismanInfoElem = SCRIPTS.GetScript<TalismanInfoScript>()->Get(talismanIndex);
			VERIFY_RETURN(talismanInfoElem, ErrorTalisman::NOT_FIND_TALISMAN_SCRIPT);
			VALID_RETURN(IsHasTalisman(talismanIndex), ErrorTalisman::NOT_EXIST_TALISMAN);

			// 서로 다른 탈리스만이라고 해도 동일한 스킬 인덱스를 가질 수 있다.
			const DriverPage* foundPage = findDriverPage(pageOffset);
			VALID_RETURN(foundPage, ErrorTalisman::DRIVER_DATA_ERROR);

			auto foundSlot = std::find_if(foundPage->begin(), foundPage->end(), [&](const TalismanDriverSlotInfo& slotInfo)
			{
				return slotInfo.talismanIndex == talismanIndex
					|| slotInfo.talismanSkillIndex == talismanInfoElem->skillIndex;
			});

			if (foundPage->end() != foundSlot)
			{
				isEquipSameSkill = true;
				sameSkillSlotNum = foundSlot->slotNumber;
			}
		}

		// DB 업데이트 정보 세팅
		auto reqDb = NEW EReqDbUpdateTalismanDriver;
		reqDb->eUpdateType = TalismanDriverUpdateType::SLOT;
		reqDb->updateSlotPageNumber = GetCurrentDriverPageOffset();
		
		for (const auto& iter : m_driver)
		{
			for (const TalismanDriverSlotInfo& slotInfo : iter.second)
			{
				if (slotInfo.slotPageNumber == pageOffset)
				{
					if (slotInfo.slotNumber == slotOffset)
					{
						TalismanDriverSlotInfo changeDriverSlotInfo = slotInfo;
						changeDriverSlotInfo.talismanIndex = talismanIndex;

						reqDb->updateDriverSlotInfos.push_back(changeDriverSlotInfo);
						reqDb->returnDriverSlotInfos.push_back(changeDriverSlotInfo);
						continue;
					}

					if (true == isEquipSameSkill && slotInfo.slotNumber == sameSkillSlotNum)
					{
						// 이미 장착중인 탈리스만 혹은 다른 종류의 탈리스만이지만 동일한 스킬 인덱스를 장착중이면 해당 슬롯은 장착 해제
						TalismanDriverSlotInfo unequipDriverSlotInfo = slotInfo;
						unequipDriverSlotInfo.talismanIndex = 0;

						reqDb->updateDriverSlotInfos.push_back(unequipDriverSlotInfo);
						reqDb->returnDriverSlotInfos.push_back(unequipDriverSlotInfo);
						continue;
					}
				}
				
				reqDb->updateDriverSlotInfos.push_back(slotInfo);
			}
		}

		SERVER.SendToDb(GetOwnerPlayer(), EventPtr(reqDb));

		return ErrorTalisman::SUCCESS;
	}

	ErrorTalisman::Error ActionPlayerTalisman::OnReqChangeTalismanDriverSlot(EventPtr& e)
	{
		const EReqChangeTalismanDriverSlot* req = static_cast<EReqChangeTalismanDriverSlot*>(e.RawPtr());

		EntityPlayer* owner = GetOwnerPlayer();
		VERIFY_RETURN(owner && owner->IsValid(), ErrorTalisman::playerNotFound);
				
		ErrorTalisman::Error eError = checkDriverSlotAndReqDbUpdate(req->talismanIndex, req->slotPageNumber, req->slotNumer);
		if (ErrorTalisman::SUCCESS != eError)
		{
			auto res = NEW EResChangeTalismanDriverSlot;
			res->eError = eError;
			SERVER.SendToClient(owner, EventPtr(res));
			SendSystemMsg(owner, SystemMessage(L"sys", L"Msg_Talisman_Cannot_Change_Skill"));
		}

		return ErrorTalisman::SUCCESS;
	}


	ErrorTalisman::Error ActionPlayerTalisman::OnReqBookmarkTalisman(EventPtr& e)
	{
		const EReqBookmarkTalisman* req = static_cast<EReqBookmarkTalisman*>(e.RawPtr());

		EntityPlayer* owner = GetOwnerPlayer();
		VERIFY_RETURN(owner && owner->IsValid(), ErrorTalisman::playerNotFound);
		
		ErrorTalisman::Error eError = checkBookmarkAndReqDbUpdate(req->bookmarkTalismanIndex, req->isRegister);
		if (ErrorTalisman::SUCCESS != eError)
		{
			EResBookmarkTalisman* res = new EResBookmarkTalisman;
			res->eError = eError;
			res->bookmarkTalismanIndex = req->bookmarkTalismanIndex;
			res->isRegister = req->isRegister;
			SERVER.SendToClient(GetOwnerPlayer(), EventPtr(res));

			SendSystemMsg(owner, SystemMessage(L"sys", L"Msg_Talisman_Common_Fail"));
		}

		return ErrorTalisman::SUCCESS;
	}


	ErrorTalisman::Error ActionPlayerTalisman::OnResDbUpdateTalismanDriver(EventPtr& e)
	{
		const EResDbUpdateTalismanDriver* resDb = static_cast<EResDbUpdateTalismanDriver*>(e.RawPtr());

		EntityPlayer* owner = GetOwnerPlayer();
		VERIFY_RETURN(owner && owner->IsValid(), ErrorTalisman::playerNotFound);

		if (TalismanDriverUpdateType::SLOT == resDb->eUpdateType)
		{
			if (ErrorDB::SUCCESS != resDb->eError)
			{
				EResChangeTalismanDriverSlot* res = new EResChangeTalismanDriverSlot;
				res->eError = ErrorTalisman::FAIL_TRANSACTION;
				SERVER.SendToClient(owner, EventPtr(res));
				return ErrorTalisman::dbError;
			}

			updateDriverSlot(resDb->returnDriverSlotInfos);
		}
		else if (TalismanDriverUpdateType::SLOT_PAGE == resDb->eUpdateType)
		{
			if (ErrorDB::SUCCESS != resDb->eError)
			{
				EResChangeTalismanDriverSlotPage* res = new EResChangeTalismanDriverSlotPage;
				res->eError = ErrorTalisman::FAIL_TRANSACTION;
				SERVER.SendToClient(owner, EventPtr(res));
				return ErrorTalisman::dbError;
			}

			updateDriverSlotPage(resDb->updateSlotPageNumber);
		}

		return ErrorTalisman::SUCCESS;
	}

	ErrorTalisman::Error ActionPlayerTalisman::checkDriverSlotPageAndReqDbUpdate(const Byte pageOffset) const
	{
		// 탈리스만 스킬을 한번이라도 사용했다면 변경 불가
		VALID_RETURN(m_isChangeableDriver, ErrorTalisman::ALREADY_USE_SKILL);
		VALID_RETURN(findDriverPage(pageOffset), ErrorTalisman::DRIVER_DATA_ERROR);		

		EReqDbUpdateTalismanDriver* reqDb = NEW EReqDbUpdateTalismanDriver;
		reqDb->eUpdateType = TalismanDriverUpdateType::SLOT_PAGE;
		reqDb->updateSlotPageNumber = pageOffset;

		for (const auto& iter : m_driver)
		{
			for (const TalismanDriverSlotInfo& slotInfo : iter.second)
			{
				reqDb->updateDriverSlotInfos.push_back(slotInfo);
			}
		}

		SERVER.SendToDb(GetOwnerPlayer(), EventPtr(reqDb));

		return ErrorTalisman::SUCCESS;
	}


	ErrorTalisman::Error ActionPlayerTalisman::OnReqChangeTalismanDriverSlotPage(EventPtr& e)
	{
		auto req = static_cast<EReqChangeTalismanDriverSlotPage*>(e.RawPtr());

		EntityPlayer* owner = GetOwnerPlayer();
		VERIFY_RETURN(owner && owner->IsValid(), ErrorTalisman::playerNotFound);

		ErrorTalisman::Error eError = checkDriverSlotPageAndReqDbUpdate(req->slotPageNumber);
		if (eError != ErrorTalisman::SUCCESS)
		{
			auto res = NEW EResChangeTalismanDriverSlotPage;
			res->eError = eError;
			SERVER.SendToClient(owner, EventPtr(res));
			SendSystemMsg(owner, SystemMessage(L"sys", L"Msg_Talisman_Cannot_Change_Skill"));
		}

		return ErrorTalisman::SUCCESS;
	}

	void ActionPlayerTalisman::updateDriverSlot(const std::vector<TalismanDriverSlotInfo>& driverSlotInfos)
	{
		std::vector<TalismanDriverSlotInfo> changeDriverSlotInfos;
		for (auto driverSlotInfo : driverSlotInfos)
		{
			const TalismanInfoElem* talismanInfoElem = SCRIPTS.GetScript<TalismanInfoScript>()->Get(driverSlotInfo.talismanIndex);
			VALID_RETURN(0 == driverSlotInfo.talismanIndex || nullptr != talismanInfoElem, );

			IndexSkill changeSkillIndex = (0 == driverSlotInfo.talismanIndex) ? 0 : talismanInfoElem->skillIndex;


			TalismanDriverSlotInfo& foundSlot = findDriverSlot(driverSlotInfo.slotPageNumber, driverSlotInfo.slotNumber);
			VALID_RETURN(foundSlot.IsValid(), );

			// 액션로그 처리
			if (0 == changeSkillIndex)
			{
				// 해제 되는 경우
				const TalismanInfoElem* unequipTalismanInfoElem = SCRIPTS.GetScript<TalismanInfoScript>()->Get(foundSlot.talismanIndex);
				if (nullptr != unequipTalismanInfoElem)
				{
					auto log = NEW EReqLogDb2;
					EventSetter::FillLogForEntity(LogCode::E_UNEQUIP_TALISMAN, GetOwnerPlayer(), log->action);
					log->action.subInfo = unequipTalismanInfoElem->name;
					log->action.var32s.push_back(static_cast<Int32>(unequipTalismanInfoElem->index));
					log->action.var32s.push_back(static_cast<Int32>(foundSlot.slotPageNumber));
					log->action.var32s.push_back(static_cast<Int32>(foundSlot.slotNumber));
					SendDataCenter(EventPtr(log));
				}
			}
			else
			{
				// 장착 되는 경우
				auto equipLog = NEW EReqLogDb2;
				EventSetter::FillLogForEntity(LogCode::E_EQUIP_TALISMAN, GetOwnerPlayer(), equipLog->action);
				equipLog->action.subInfo = talismanInfoElem->name;
				equipLog->action.var32s.push_back(static_cast<Int32>(talismanInfoElem->index));
				equipLog->action.var32s.push_back(static_cast<Int32>(foundSlot.slotPageNumber));
				equipLog->action.var32s.push_back(static_cast<Int32>(foundSlot.slotNumber));
				SendDataCenter(EventPtr(equipLog));

				if (0 != foundSlot.talismanIndex)
				{
					// 다른 탈리스만 장착으로 해제 되는 경우
					const TalismanInfoElem* unequipTalismanInfoElem = SCRIPTS.GetScript<TalismanInfoScript>()->Get(foundSlot.talismanIndex);
					if (nullptr != unequipTalismanInfoElem)
					{
						auto unequipLog = NEW EReqLogDb2;
						EventSetter::FillLogForEntity(LogCode::E_UNEQUIP_TALISMAN, GetOwnerPlayer(), unequipLog->action);
						unequipLog->action.subInfo = unequipTalismanInfoElem->name;
						unequipLog->action.var32s.push_back(static_cast<Int32>(unequipTalismanInfoElem->index));
						unequipLog->action.var32s.push_back(static_cast<Int32>(foundSlot.slotPageNumber));
						unequipLog->action.var32s.push_back(static_cast<Int32>(foundSlot.slotNumber));
						SendDataCenter(EventPtr(unequipLog));
					}
				}
			}

			foundSlot.talismanIndex = driverSlotInfo.talismanIndex;
			foundSlot.talismanSkillIndex = changeSkillIndex;

			changeDriverSlotInfos.push_back(foundSlot);
		}

		auto res = NEW EResChangeTalismanDriverSlot;
		res->eError = ErrorTalisman::SUCCESS;
		res->talismanDriverInfos = changeDriverSlotInfos;
		SERVER.SendToClient(GetOwnerPlayer(), EventPtr(res));

		equipSkillDriverSlotAchievement();
	}

	void ActionPlayerTalisman::updateDriverSlotPage(const Byte slotPage)
	{
		m_curDriverPageOffset = slotPage;

		auto res = NEW EResChangeTalismanDriverSlotPage;
		res->eError = ErrorTalisman::SUCCESS;
		res->slotPageNumber = GetCurrentDriverPageOffset();
		SERVER.SendToClient(GetOwnerPlayer(), EventPtr(res));
	}

#ifdef __Patch_Talisman_V2_Growth_by_kangms_20181207
	ErrorTalisman::Error ActionPlayerTalisman::OnLoadTalismanInfoFromDb( const DbTalismanInfos& dbTalismanInfos
		                                                               , const DbTalismanDriverInfo& dbDriverInfo
		                                                               , const std::vector<IndexTalisman>& dbBookmarkTalismans
		                                                               , const std::vector<DbTalismanGrowthReward>& dbTalismanGrowthRewardInfos)
#else//__Patch_Talisman_V2_Growth_by_kangms_20181207
	ErrorTalisman::Error ActionPlayerTalisman::OnLoadTalismanInfoFromDb(const DbTalismanInfos& dbTalismanInfos, const DbTalismanDriverInfo& dbDriverInfo, const std::vector<IndexTalisman>& dbBookmarkTalismans)
#endif//__Patch_Talisman_V2_Growth_by_kangms_20181207
	{
#ifdef __Patch_Talisman_V2_Awaken_by_kangms_20181105
		//탈리스만 각성 활성화 체크 & 각성 효과 반영
		IndexQuest talismanAwakenQuestIndex = SCRIPTS.Get<TalismanDataScript>().GetTalismanAwakenQuestIndex();
		Bool isCompleteTalismanAwakenQuest = (0 != talismanAwakenQuestIndex) ? GetOwnerPlayer()->GetQuestAction().HasCompletedOnce(talismanAwakenQuestIndex) : true;

		if (true == isCompleteTalismanAwakenQuest) {
			this->activateTalismanAwakenSystem();
		}
#endif//__Patch_Talisman_V2_Awaken_by_kangms_20181105

		// 탈리스만 컨텐츠 활성화 여부 체크
		IndexQuest talismanQuestIndex = SCRIPTS.Get<TalismanDataScript>().GetTalismanQuestIndex();
		Bool isCompleteTalismanQuest = (0 != talismanQuestIndex) ? GetOwnerPlayer()->GetQuestAction().HasCompletedOnce(talismanQuestIndex) : true;

		if (true == isCompleteTalismanQuest)
		{
			activateTalismanSystem(true);

			//탈리스만 및 도감 정보 세팅
			for (auto info : dbTalismanInfos)
			{
				TalismanInfo loadTalisman(info.talismanIndex, info.level, info.limitBreak, info.awaken, info.exp, info.collectionPoint, info.combatPower);

				// 기획에서 탈리스만이 제공하는 도감포인트를 변경할 수 있다고 한다.
				// 결국 DB에서 로드된 collectionPoint, combatPower를 신뢰할 수 없게 되었다...ㅜㅜ 다시 계산한다.
				calculateCollectionPointAndCombatPower(__inout loadTalisman);

				InsertTalisman(loadTalisman, true);

				UpdateCollection(0, loadTalisman.collectionPoint, L"", true);
				AppendTotalCombatPower(0, loadTalisman.combatPower);
				//ntfTalismanInfos.push_back(loadTalisman);
			}
		}

#ifdef __Patch_Talisman_V2_Growth_by_kangms_20181207
		if (true == this->isAbailableTalisman()) {
			//탈리스만 성장 보상 지급 완료 정보 읽기
			for (auto info : dbTalismanGrowthRewardInfos) {
				this->OnTalismanGrowthRewardFromDB(info);
			}
		}
#endif//__Patch_Talisman_V2_Growth_by_kangms_20181207

		m_curDriverPageOffset = dbDriverInfo.curSlotPageNumber;

		// 탈리스만 드라이버 정보 세팅
		for (Byte pageOffset = 0; pageOffset < static_cast<Byte>(dbDriverInfo.driverSlotInfos.size()); ++pageOffset)
		{
			VALID_DO(pageOffset < Limits::MAX_TALISMAN_SLOT_PAGE , break);

			for (Byte slotOffset = 0; slotOffset < static_cast<Byte>(dbDriverInfo.driverSlotInfos[pageOffset].slots.size()); ++slotOffset)
			{
				VALID_DO(slotOffset < Limits::MAX_TALISMAN_SLOT , continue);

				const IndexTalisman talismanIndex = dbDriverInfo.driverSlotInfos[pageOffset].slots[slotOffset];
				const TalismanInfoElem* talismanInfoElem = SCRIPTS.GetScript<TalismanInfoScript>()->Get(talismanIndex);
				
				if (0 != talismanIndex)
				{
					VALID_DO(talismanInfoElem, continue);
					VALID_DO(IsHasTalisman(talismanIndex), continue);
				}

				IndexSkill talismanSkillIndex = (0 == talismanIndex) ? 0 : talismanInfoElem->skillIndex;

				TalismanDriverSlotInfo& foundSlot = findDriverSlot(pageOffset, slotOffset);
				VALID_DO(foundSlot.IsValid(), continue);

				//foundSlot.slotPageNumber = pageNum;
				//foundSlot.slotNumber = slotNum;

				// 드라이버 슬롯 장착 가능 개수 확인 후 세팅
				foundSlot.talismanIndex			= slotOffset < GetCurrnetDriverSlotCount() ? talismanIndex : 0;
				foundSlot.talismanSkillIndex		= slotOffset < GetCurrnetDriverSlotCount() ? talismanSkillIndex : 0;

				//ntfDriverSlotInfos.push_back(foundSlot);
			}
		}

		// 탈리스만 즐겨찾기 정보 세팅
		for (auto bookmarkTalismanIndex : dbBookmarkTalismans)
		{
			const TalismanInfoElem* talismanInfoElem = SCRIPTS.GetScript<TalismanInfoScript>()->Get(bookmarkTalismanIndex);
			VALID_DO(talismanInfoElem, continue);

			m_bookmarkTalismans.insert(bookmarkTalismanIndex);
		}

		// 탈리스만 스킬 사용 가능 지역 체크
		Sector* sector = GetOwnerPlayer()->GetSector();
		VERIFY_RETURN(sector, ErrorTalisman::sectorNotFound);
			
		IndexZone zoneIndex = sector->GetIndexZone();
		const WorldElem* worldElem = SCRIPTS.GetWorldScript(zoneIndex);
		VERIFY_RETURN(worldElem, ErrorTalisman::elemNotFound);
		VALID_RETURN(worldElem->talismanSkillType, ErrorTalisman::invalidTalismanSkillType);
		
		m_isUseableSkill = true;
		m_maxTalismanPoint = worldElem->maxTalismanPoint;

		return ErrorTalisman::SUCCESS;
	}

	Bool ActionPlayerTalisman::CheckTalismanSkillCast(const IndexSkill skillIndex)
	{
		// 탈리스만 스킬이 아니라면 true
		VALID_RETURN(SCRIPTS.GetScript<TalismanInfoScript>()->IsTalismanSkill(skillIndex), true);

		// 탈리스만 스킬이지만 사용 가능 지역이 아니면 false
		VALID_RETURN(IsUseableTalismanSkill(), false);

		TalismanInfo talisman;
		VALID_RETURN(getTalismanInDriverFromSkillIndex(skillIndex, talisman), false);

		TalismanSkillKey talismanSkillKey(talisman.index, talisman.level);
		auto talismanSkillScript = SCRIPTS.GetScript<TalismanSkillScript>();
		const TalismanSkillElem* talismanSkillElem = talismanSkillScript->Get(talismanSkillKey);
		if (nullptr == talismanSkillElem)
		{
			while (1 < talismanSkillKey.level)
			{
				// 탈리스만 현재 레벨에 대응되는 스킬정보가 없다면 이전 레벨의 정보를 사용한다.
				--talismanSkillKey.level;
				talismanSkillElem = talismanSkillScript->Get(talismanSkillKey);
				VALID_DO(nullptr == talismanSkillElem, break);
			}
		}

		VERIFY_RETURN(talismanSkillElem, false);

#ifdef __Patch_Talisman_V2_Awaken_by_kangms_20181105
		//탈리스만 각성 효과 KeylogicType::SkillChange 해당 하는 스킬 효과 체크
		if (nullptr != talisman.Lookup4TalismanAwakenEffect(TalismanAwakenEffectInfo::KeylogicType::SkillChange)) {
			VERIFY_RETURN(SCRIPTS.GetSkillEffectScript(talismanSkillElem->awakenSkillEffectIndex), false);
		}
		else {
			//탈리스만 스킬 체크
			VERIFY_RETURN(SCRIPTS.GetSkillEffectScript(talismanSkillElem->skillEffect), false);
		}
#else//__Patch_Talisman_V2_Awaken_by_kangms_20181105
		VERIFY_RETURN(SCRIPTS.GetSkillEffectScript(talismanSkillElem->skillEffect), false);
#endif//__Patch_Talisman_V2_Awaken_by_kangms_20181105

		auto iterSkill = m_talismanSkillUsedCount.find(skillIndex);
		VALID_RETURN(m_talismanSkillUsedCount.end() != iterSkill, false);

#ifdef __Patch_Talisman_V2_Awaken_by_kangms_20181105
		//탈리스만 각성 효과 KeylogicType::MaxUseCount 해당 하는 효과 체크
		auto awakenEffectMaxUseCount = talisman.Lookup4TalismanAwakenEffect(TalismanAwakenEffectInfo::KeylogicType::MaxUseCount);
		if (nullptr != awakenEffectMaxUseCount) {
			// 탈리스만 스킬 최대 사용 가능 횟수 체크
			VALID_RETURN((talismanSkillElem->maxUseCount + awakenEffectMaxUseCount->value) > iterSkill->second, false);
		}
		else {
			// 탈리스만 스킬 최대 사용 가능 횟수 체크
			VALID_RETURN(talismanSkillElem->maxUseCount > iterSkill->second, false);
		}
#else//__Patch_Talisman_V2_Awaken_by_kangms_20181105
		// 탈리스만 스킬 최대 사용 가능 횟수 체크
		VALID_RETURN(talismanSkillElem->maxUseCount > iterSkill->second, false);
#endif//__Patch_Talisman_V2_Awaken_by_kangms_20181105
		
		// 탈리스만 감소 포인트를 통한 요구 탈리스만 포인트 계산
		UInt32 reduceTalismanPoint = talisman.GetTalismanAwakenEffectValue(TalismanAwakenEffectInfo::TalismanPoint);
		UInt32 requireTalismanPoint = talismanSkillElem->requireTalismanPoint > reduceTalismanPoint ? talismanSkillElem->requireTalismanPoint - reduceTalismanPoint : 0;

		if (requireTalismanPoint > m_curTalismanPoint)
		{
			SendSystemMsg(GetOwnerPlayer(), SystemMessage(L"sys", L"Msg_Talisman_Not_Enough_Point"));
			return false;
		}

		m_curSkillKey = talismanSkillKey;

		return true;
	}

	void ActionPlayerTalisman::UpdateTalismanSkillCoolTime(Skill* baseSkill, const SkillInfoElem* skillInfoElem) const
	{
		EntityPlayer* owner = GetOwnerPlayer();
		VERIFY_RETURN(owner && owner->IsValid(), );

		VALID_RETURN(baseSkill && skillInfoElem, );
		VALID_RETURN(SCRIPTS.GetScript<TalismanInfoScript>()->IsTalismanSkill(skillInfoElem->index), );

		const TalismanSkillElem* talismanSkillElem = SCRIPTS.GetScript<TalismanSkillScript>()->Get(m_curSkillKey);
		VALID_RETURN(talismanSkillElem, );

		const DriverPage* foundPage = findDriverPage(GetCurrentDriverPageOffset());
		VALID_RETURN(foundPage, );

		AttributeSkill& attrSkill = owner->GetSkillAttr();
		ActionSkillControl& actSkillControl = owner->GetSkillControlAction();
		Tick curTick = ::GetTickCount();
		Tick coolTime = 0;
		CoolTimeInfoVector coolTimeInfos;

		for (const TalismanDriverSlotInfo& slotInfo : *foundPage)
		{
			if (slotInfo.talismanSkillIndex == skillInfoElem->index)
			{
#ifdef __Patch_Talisman_V2_Awaken_by_kangms_20181105
				TalismanInfo talisman;
				VERIFY_RETURN(getTalismanInDriverFromSkillIndex(slotInfo.talismanSkillIndex, talisman), );

				//탈리스만 각성 효과 KeylogicType::Cooltime 해당 하는 효과 체크
				auto awakenEffectCoolTime = talisman.Lookup4TalismanAwakenEffect(TalismanAwakenEffectInfo::KeylogicType::Cooltime);
				if (nullptr != awakenEffectCoolTime) {
					coolTime = actSkillControl.GetCorrectSkillCoolTime(skillInfoElem->coolLineType, talismanSkillElem->coolTime - awakenEffectCoolTime->value);
				}
				else {
					coolTime = actSkillControl.GetCorrectSkillCoolTime(skillInfoElem->coolLineType, talismanSkillElem->coolTime);
				}
#else//__Patch_Talisman_V2_Awaken_by_kangms_20181105
				coolTime = actSkillControl.GetCorrectSkillCoolTime(skillInfoElem->coolLineType, talismanSkillElem->coolTime);
#endif//__Patch_Talisman_V2_Awaken_by_kangms_20181105

	#ifdef __Reincarnation__jason_180628__
				coolTime = static_cast<Tick>(coolTime * owner->GetReverseNormalizedAbilityRate(EAT::TALISMAN_COOLTIME_DEC_RATE));
	#endif//__Reincarnation__jason_180628__

				attrSkill.skillCoolLineManager.SetEndCoolTick(baseSkill->GetCoolLineType(), (coolTime + curTick));

				CoolTimeInfo coolTimeInfo;
				coolTimeInfo.coolLineType = baseSkill->GetCoolLineType();
				coolTimeInfo.totalTime = coolTime;
				coolTimeInfo.remainTime = coolTime;
				coolTimeInfos.push_back(coolTimeInfo);
			}
			else
			{
				// 시전 스킬을 제외한 나머지 장착 스킬들은 글로벌 쿨타임을 적용 시킨다.
				Skill* skill = attrSkill.FindSkill(slotInfo.talismanSkillIndex);
				VALID_DO(skill, continue);

				Tick endTick = attrSkill.skillCoolLineManager.GetEndCoolTick(skill->GetCoolLineType());
				coolTime = actSkillControl.GetCorrectSkillCoolTime(skill->GetCoolLineType(), talismanSkillElem->globalCoolTime);
#ifdef __Reincarnation__jason_180628__
				coolTime = static_cast<Tick>(coolTime * owner->GetReverseNormalizedAbilityRate(EAT::TALISMAN_COOLTIME_DEC_RATE));
#endif
				VALID_DO(endTick <= coolTime + curTick, continue);

				attrSkill.skillCoolLineManager.SetEndCoolTick(skill->GetCoolLineType(), (coolTime + curTick));

				CoolTimeInfo coolTimeInfo;
				coolTimeInfo.coolLineType = skill->GetCoolLineType();
				coolTimeInfo.totalTime = coolTime;
				coolTimeInfo.remainTime = coolTime;
				coolTimeInfos.push_back(coolTimeInfo);
			}
		}

		// 클라이언트로 전송할 스킬 쿨타임 정보가 없다면 리턴
		VALID_RETURN(coolTimeInfos.size(), );

		auto ntf = NEW ENtfGameSkillCoolTimes;
		ntf->infos = coolTimeInfos;
		owner->SendToClient(EventPtr(ntf));
	}

	void ActionPlayerTalisman::ProcessTalismanSkillCast(const IndexSkill skillIndex)
	{
		VALID_RETURN(SCRIPTS.GetScript<TalismanInfoScript>()->IsTalismanSkill(skillIndex), );

		const TalismanSkillElem* talismanSkillElem = SCRIPTS.GetScript<TalismanSkillScript>()->Get(m_curSkillKey);
		VALID_RETURN(talismanSkillElem, );

		auto iterSkill = m_talismanSkillUsedCount.find(skillIndex);
		VALID_RETURN(m_talismanSkillUsedCount.end() != iterSkill, );

		// 스킬 사용 횟수 증가
		iterSkill->second++;

		TalismanInfo* talismanInfo = findTalisman(m_curSkillKey.talismanIndex);

		UInt32 reduceTalismanPoint = (talismanInfo != nullptr) ? talismanInfo->GetTalismanAwakenEffectValue(TalismanAwakenEffectInfo::TalismanPoint) : 0;
		UInt32 requireTalismanPoint = talismanSkillElem->requireTalismanPoint > reduceTalismanPoint ? talismanSkillElem->requireTalismanPoint - reduceTalismanPoint : 0;

		// 탈리스만 포인트 감소
		m_curTalismanPoint = (m_curTalismanPoint > requireTalismanPoint) ? m_curTalismanPoint - requireTalismanPoint : 0;
		
		// 탈리스만 스킬 사용 후에는 드라이버 슬롯 및 슬롯 페이지 변경 불가
		m_isChangeableDriver = false;

		// 업데이트된 스킬 정보 클라이언트로 전송
		auto ntf = NEW ENtfUpdateTalismanSkillInfo;
		ntf->skillCountInfos.emplace(iterSkill->first, iterSkill->second);
		ntf->talismanPoint = m_curTalismanPoint;
		SERVER.SendToClient(GetOwnerPlayer(), EventPtr(ntf));

		useSkillAchievement();
	}

	const SkillEffectElem* ActionPlayerTalisman::GetTalismanSkillEffectElem(const IndexSkill skillIndex) const
	{
		VALID_RETURN(SCRIPTS.GetScript<TalismanInfoScript>()->IsTalismanSkill(skillIndex), nullptr);

		const TalismanSkillElem* talismanSkillElem = SCRIPTS.GetScript<TalismanSkillScript>()->Get(m_curSkillKey);		
		VERIFY_RETURN(talismanSkillElem, nullptr);

#ifdef __Patch_Talisman_V2_Awaken_by_kangms_20181105
		TalismanInfo talisman;
		VALID_RETURN(getTalismanInDriverFromSkillIndex(skillIndex, talisman), false);

		const SkillEffectElem* skillEffectElem = nullptr;

		//탈리스만 각성 효과 KeylogicType::SkillChange 해당 하는 스킬 효과 체크
		if (nullptr != talisman.Lookup4TalismanAwakenEffect(TalismanAwakenEffectInfo::KeylogicType::SkillChange)) {
			skillEffectElem = SCRIPTS.GetSkillEffectScript(talismanSkillElem->awakenSkillEffectIndex);
		}
		else {
			skillEffectElem = SCRIPTS.GetSkillEffectScript(talismanSkillElem->skillEffect);
		}
#else//__Patch_Talisman_V2_Awaken_by_kangms_20181105
		const SkillEffectElem* skillEffectElem = SCRIPTS.GetSkillEffectScript(talismanSkillElem->skillEffect);
		VERIFY_RETURN(skillEffectElem, nullptr);
#endif//__Patch_Talisman_V2_Awaken_by_kangms_20181105

		return skillEffectElem;
	}

	ErrorTalisman::Error ActionPlayerTalisman::checkBookmarkAndReqDbUpdate(const IndexTalisman talismanIndex, const Bool isRegister) const
	{
		EntityPlayer* owner = GetOwnerPlayer();
		VERIFY_RETURN(owner && owner->IsValid(), ErrorTalisman::playerNotFound);

		const TalismanInfoElem* talismanInfoElem = SCRIPTS.GetScript<TalismanInfoScript>()->Get(talismanIndex);
		VERIFY_RETURN(talismanInfoElem, ErrorTalisman::NOT_FIND_TALISMAN_SCRIPT);

		if (true == isRegister)
		{
			// 등록하는 경우
			VALID_RETURN(m_bookmarkTalismans.end() == m_bookmarkTalismans.find(talismanIndex), ErrorTalisman::ALREADY_REGISTER_BOOKMARK);
			
			auto reqDb = NEW EReqDbInsertTalismanBookmark;
			reqDb->talismanIndex = talismanIndex;

			owner->SendToDb(EventPtr(reqDb));
		}
		else if (false == isRegister)
		{
			// 해제하는 경우
			VALID_RETURN(m_bookmarkTalismans.end() != m_bookmarkTalismans.find(talismanIndex), ErrorTalisman::NOT_REGISTER_BOOKMARK);

			auto reqDb = NEW EReqDbDeleteTalismanBookmark;
			reqDb->talismanIndex = talismanIndex;

			owner->SendToDb(EventPtr(reqDb));
		}

		return ErrorTalisman::SUCCESS;
	}


	ErrorTalisman::Error ActionPlayerTalisman::OnResDbUpdateTalismanBookmark(EventPtr& e)
	{
		const EResDbUpdateTalismanBookmark* resDb = static_cast<EResDbUpdateTalismanBookmark*>(e.RawPtr());

		EntityPlayer* owner = GetOwnerPlayer();
		VERIFY_RETURN(owner && owner->IsValid(), ErrorTalisman::playerNotFound);

		if (ErrorDB::SUCCESS != resDb->eError)
		{
			EResBookmarkTalisman* res = new EResBookmarkTalisman;
			res->eError = ErrorTalisman::FAIL_TRANSACTION;
			res->bookmarkTalismanIndex = resDb->talismanIndex;
			res->isRegister = resDb->isRegister;
			owner->SendToClient(EventPtr(res));
			return ErrorTalisman::dbError;
		}

		updateBookmark(resDb->talismanIndex, resDb->isRegister);

		return ErrorTalisman::SUCCESS;
	}

	void ActionPlayerTalisman::updateBookmark(const IndexTalisman talismanIndex, const Bool isRegister)
	{
		const TalismanInfoElem* talismanInfoElem = SCRIPTS.GetScript<TalismanInfoScript>()->Get(talismanIndex);
		VALID_RETURN(talismanInfoElem, );

		if (true == isRegister)
		{
			// 등록하는 경우
			VALID_RETURN(m_bookmarkTalismans.end() == m_bookmarkTalismans.find(talismanIndex), );

			m_bookmarkTalismans.insert(talismanIndex);
			SendSystemMsg(GetOwnerPlayer(), SystemMessage(L"sys", L"Msg_Talisman_Bookmark_On"));
		}
		else if (false == isRegister)
		{
			// 해제하는 경우
			VALID_RETURN(m_bookmarkTalismans.end() != m_bookmarkTalismans.find(talismanIndex), );

			m_bookmarkTalismans.erase(talismanIndex);
			SendSystemMsg(GetOwnerPlayer(), SystemMessage(L"sys", L"Msg_Talisman_Bookmark_Off"));
		}

		auto res = NEW EResBookmarkTalisman;
		res->eError = ErrorTalisman::SUCCESS;
		res->bookmarkTalismanIndex = talismanIndex;
		res->isRegister = isRegister;
		SERVER.SendToClient(GetOwnerPlayer(), EventPtr(res));
	}

	void ActionPlayerTalisman::AppendTotalCombatPower(const UInt32 subCombatPower, const UInt32 addCombatPower)
	{
		VALID_RETURN(subCombatPower != addCombatPower, );

		m_totalCombatPower -= subCombatPower;
		m_totalCombatPower += addCombatPower;

		GetOwnerPlayer()->GetCombatPowerAction().UpdateCombatPower(CombatPowerType::TALISMAN);
	}

	void ActionPlayerTalisman::CheckTalismanQuestCompleteAndNotifyToClient(const IndexQuest questIndex)
	{
#ifdef __Patch_Talisman_V2_Awaken_by_kangms_20181105
		bool isActivityTalisman = false;

		//탈리스만 활성화 상태 & 퀘스트 완료 체크
		if (false == isAbailableTalisman()) {
			IndexQuest talismanQuestIndex = SCRIPTS.GetScript<TalismanDataScript>()->GetTalismanQuestIndex();
			if (questIndex == talismanQuestIndex) {
				if (true == GetOwnerPlayer()->GetQuestAction().HasCompletedOnce(talismanQuestIndex)) {

					auto ntf = NEW ENtfActivateTalismanSystem;
					SERVER.SendToClient(GetOwnerPlayer(), EventPtr(ntf));

					this->activateTalismanSystem();
					isActivityTalisman = true;
				}
			}
		}
		
		//탈리스만 각성 활성화 상태 & 퀘스트 완료 체크
		if (false == isAbailableTalismanAwaken()) {
			IndexQuest talismanAwakenQuestIndex = SCRIPTS.GetScript<TalismanDataScript>()->GetTalismanAwakenQuestIndex();
			if (questIndex == talismanAwakenQuestIndex) {
				if (true == GetOwnerPlayer()->GetQuestAction().HasCompletedOnce(talismanAwakenQuestIndex)) {

					auto ntf = NEW EzcNtfActivateAwakenTalismanSystem;
					SERVER.SendToClient(GetOwnerPlayer(), EventPtr(ntf));

					this->activateTalismanAwakenSystem();
					isActivityTalisman = true;

					if (   true == isAbailableTalisman()
						&& true == this->isAbailableTalismanAwaken() ) {

						for (auto& pairTalismanInfo : m_talismans) {

							auto& talismanInfo = pairTalismanInfo.second;

							talismanInfo.talismanAwakenEffectList.clear();

							//탈리스만 각성 효과 적용
							this->ApplyTalismanAwakenEffectAll(__inout talismanInfo);
						}
					}
				}
			}
		}

		//탈리스만 정보 클라이언트에게 통지
		if (true == isActivityTalisman) {
			this->Send_TalismanCollection();
		}

#else//__Patch_Talisman_V2_Awaken_by_kangms_20181105
		// 이미 컨텐츠가 활성화 되어 있다면 리턴
		VALID_RETURN(false == isAbailableTalisman(), );

		IndexQuest talismanQuestIndex = SCRIPTS.GetScript<TalismanDataScript>()->GetTalismanQuestIndex();
		VALID_RETURN(questIndex == talismanQuestIndex, );
		VALID_RETURN(GetOwnerPlayer()->GetQuestAction().HasCompletedOnce(talismanQuestIndex), );

		auto ntf = NEW ENtfActivateTalismanSystem;
		SERVER.SendToClient(GetOwnerPlayer(), EventPtr(ntf));

		activateTalismanSystem();
#endif//__Patch_Talisman_V2_Awaken_by_kangms_20181105
	}

	void ActionPlayerTalisman::ProcessTalismanPoint(EntityNpc* targetNpc)
	{
		// 탈리스만 스킬 사용 가능 지역이 아니라면 처리할 필요 없음.
		VALID_RETURN(IsUseableTalismanSkill(), );

		auto sector = GetOwnerPlayer()->GetSector();
		VERIFY_RETURN(sector, );

		IndexZone zoneIndex = sector->GetIndexZone();
		const WorldElem* worldElem = SCRIPTS.GetWorldScript(zoneIndex);
		VALID_RETURN(worldElem, );

		auto script = SCRIPTS.GetScript<TalismanSkillPointScript>();
		VERIFY_RETURN(script, );

		UInt32 addTalismanPoint = 0;
		Byte talismanSkillPointType = worldElem->talismanSkillType;
		TalismanSkillPointKey key(talismanSkillPointType, TalismanSkillPointValueType::NPC_GRADE);
		const TalismanSkillPointElem* elem = script->Get(key);
		if (nullptr != elem)
		{
			// 몬스터 등급에 따른 탈리스만 포인트 습득
			Int32 value = static_cast<Int32>(targetNpc->GetNpcGrade());
			auto iterGrade = elem->mapGetTalismanPoint.find(value);
			if (elem->mapGetTalismanPoint.end() != iterGrade)
			{
				addTalismanPoint = iterGrade->second;
			}
		}

		key.valueType = TalismanSkillPointValueType::NPC_INDEX;
		elem = script->Get(key);
		if (nullptr != elem)
		{
			// 몬스터 인덱스에 따른 탈리스만 포인트 습득
			Int32 value = targetNpc->GetNpcIndex();
			auto iterNpcIndex = elem->mapGetTalismanPoint.find(value);
			if (elem->mapGetTalismanPoint.end() != iterNpcIndex)
			{
				addTalismanPoint = iterNpcIndex->second;
			}
		}

		// 자신에게 탈리스만 포인트 지급
		AddTalismanPoint(addTalismanPoint);

		// Sector 내에 있는 모든 아군 플레이어에게 탈리스만 포인트 지급
		VectorPlayer vecPlayer;
		sector->GetPlayers(vecPlayer);
		for (auto player : vecPlayer)
		{
			VALID_DO(player && player->IsValid(), continue);
			VALID_DO(GetOwnerPlayer()->GetId() != player->GetId(), continue);

#ifdef __Renewal_Battle_IFF_by_kangms_180829
			ActionPlayerCognition& actPlayerCognition = GetOwnerPlayer()->GetPlayerCognitionAction();
			VALID_DO(actPlayerCognition.IsSupportable(player), continue);
#else//__Renewal_Battle_IFF_by_kangms_180829
			ActionPlayerCognition& actPlayerCognition = player->GetPlayerCognitionAction();
			VALID_DO(actPlayerCognition.CanBeSupported(GetOwnerPlayer()), continue);
#endif//__Renewal_Battle_IFF_by_kangms_180829

			ActionPlayerTalisman& actPlayerTalisman = player->GetTalismanAction();
			actPlayerTalisman.AddTalismanPoint(addTalismanPoint);
		}
	}

	void ActionPlayerTalisman::SendToClientForUpdateTalismanPoint()
	{
		VALID_RETURN(m_isSendToClientTalismanPoint, );

		auto ntf = NEW ENtfUpdateTalismanSkillInfo;
		ntf->talismanPoint = m_curTalismanPoint;
		SERVER.SendToClient(GetOwnerPlayer(), EventPtr(ntf));

		m_isSendToClientTalismanPoint = false;
	}

	void ActionPlayerTalisman::GetTalismanSkillCountInfo(__out std::map<IndexSkill, UInt32>& skillCountInfos) const
	{
		for (const auto& iterDriver : m_driver)
		{
			for (const TalismanDriverSlotInfo& slotInfo : iterDriver.second)
			{
				auto iterSkill = m_talismanSkillUsedCount.find(slotInfo.talismanSkillIndex);
				VALID_DO(m_talismanSkillUsedCount.end() != iterSkill && iterSkill->second, continue);

				skillCountInfos.emplace(iterSkill->first, iterSkill->second);
			}
		}
	}

	void ActionPlayerTalisman::SetTalismanSkillUseCountInfoFromWorld(const std::map<IndexSkill, UInt32>& talismanSkills)
	{
		for (auto iter : talismanSkills)
		{
			auto iterSkill = m_talismanSkillUsedCount.find(iter.first);
			if (m_talismanSkillUsedCount.end() == iterSkill)
			{
				m_talismanSkillUsedCount.emplace(iter.first, iter.second);
			}
			else
			{
				iterSkill->second = iter.second;
			}

			// 탈리스만 스킬을 한번이라도 사용했다면 드라이버 슬롯 및 슬롯 페이지 변경 불가
			VALID_DO(iter.second, continue);
			m_isChangeableDriver = false;
		}
	}

	void ActionPlayerTalisman::GetTalismanSkillUseCountInfoToWorld(__out std::map<IndexSkill, UInt32>& talismanSkills) const
	{
		// 현재 사용 중인 슬롯 페이지에서만 정보를 추출 한다.

		const DriverPage* foundPage = findDriverPage(GetCurrentDriverPageOffset());
		VALID_RETURN(foundPage, );		
		
		for (const TalismanDriverSlotInfo& slotInfo : *foundPage)
		{
			VALID_DO(slotInfo.talismanSkillIndex, continue);
			
			auto iterSkill = m_talismanSkillUsedCount.find(slotInfo.talismanSkillIndex);
			VALID_DO(m_talismanSkillUsedCount.end() != iterSkill, continue);

			talismanSkills.emplace(iterSkill->first, iterSkill->second);
		}
	}

	Bool ActionPlayerTalisman::GetTalismanLevel( const IndexSkill skillIndex, __out TalismanLevel& level ) const
	{
		const DriverPage* foundPage = findDriverPage(GetCurrentDriverPageOffset());
		VALID_RETURN(foundPage, false);

		auto iterSlotInfo = std::find_if( foundPage->begin(), foundPage->end(), [&] ( const TalismanDriverSlotInfo& slotInfo )
		{
			return slotInfo.talismanSkillIndex == skillIndex;
		} );
		VALID_RETURN( foundPage->end() != iterSlotInfo, false );

		level = GetTalismanLevel(iterSlotInfo->talismanIndex);

		return true;
	}

	void ActionPlayerTalisman::calculateCollectionPointAndCombatPower(__inout TalismanInfo& talismanInfo)
	{
		const TalismanInfoElem* talismanInfoElem = SCRIPTS.GetScript<TalismanInfoScript>()->Get(talismanInfo.index);
		VALID_RETURN(talismanInfoElem, );

		UInt32 newTotalCollectionPoint = 0;
		UInt32 newTotalCombatPower = 0;
		auto talismanScript = SCRIPTS.GetScript<TalismanCollectionPointScript>();

		// level에 의한 도감 포인트 및 전투력 계산
		const TalismanCollectionPointElem* elem = talismanScript->Get(TalismanCollectionPointKey(talismanInfoElem->grade, TalismanCollectionPointType::LEVEL));
		if (nullptr != elem)
		{
			auto iterLevelCollectionPoint = elem->collectionPoints.find(talismanInfo.level);
			if (elem->collectionPoints.end() != iterLevelCollectionPoint)
			{
				newTotalCollectionPoint += iterLevelCollectionPoint->second;
			}

			auto iterLevelCombatPower = elem->combatPowers.find(talismanInfo.level);
			if (elem->combatPowers.end() != iterLevelCombatPower)
			{
				newTotalCombatPower += iterLevelCombatPower->second;
			}
		}

		// limitBreak에 의한 도감 포인트 및 전투력 계산
		elem = talismanScript->Get(TalismanCollectionPointKey(talismanInfoElem->grade, TalismanCollectionPointType::LIMIT_BREAK));
		if (nullptr != elem)
		{
			auto iterLimitBreakCollectionPoint = elem->collectionPoints.find(static_cast<UInt16>(talismanInfo.limitBreak));
			if (elem->collectionPoints.end() != iterLimitBreakCollectionPoint)
			{
				newTotalCollectionPoint += iterLimitBreakCollectionPoint->second;
			}

			auto iterLimitBreakCombatPower = elem->combatPowers.find(talismanInfo.level);
			if (elem->combatPowers.end() != iterLimitBreakCombatPower)
			{
				newTotalCombatPower += iterLimitBreakCombatPower->second;
			}
		}

		// awaken에 의한 도감 포인트 및 전투력 계산
		elem = talismanScript->Get(TalismanCollectionPointKey(talismanInfoElem->grade, TalismanCollectionPointType::AWAKEN));
		if (nullptr != elem)
		{
			auto iterAwakenCollectionPoint = elem->collectionPoints.find(static_cast<UInt16>(talismanInfo.awaken));
			if (elem->collectionPoints.end() != iterAwakenCollectionPoint)
			{
				newTotalCollectionPoint += iterAwakenCollectionPoint->second;
			}

			auto iterAwakenCombatPower = elem->combatPowers.find(talismanInfo.awaken);
			if (elem->combatPowers.end() != iterAwakenCombatPower)
			{
				newTotalCombatPower += iterAwakenCombatPower->second;
			}
		}

		talismanInfo.collectionPoint = newTotalCollectionPoint;
		talismanInfo.combatPower = newTotalCombatPower;

#ifdef __Patch_Talisman_V2_Awaken_by_kangms_20181105
		if (true == this->isAbailableTalismanAwaken()) {
			talismanInfo.talismanAwakenEffectList.clear();

			//탈리스만 각성 효과 적용
			this->ApplyTalismanAwakenEffectAll(__inout talismanInfo);
		}
#endif//__Patch_Talisman_V2_Awaken_by_kangms_20181105
	}

	void ActionPlayerTalisman::calculateTalismanLevel(__inout TalismanInfo& talismanInfo) const
	{
		const TalismanInfoElem* talismanInfoElem = SCRIPTS.GetScript<TalismanInfoScript>()->Get(talismanInfo.index);
		VALID_RETURN(talismanInfoElem, );

		TalismanLevelKey key(talismanInfoElem->grade, talismanInfo.level);
		auto talismanLevelScript = SCRIPTS.GetScript<TalismanLevelScript>();
		const TalismanLevelElem* talismanLevelElem = talismanLevelScript->Get(key);
		VALID_RETURN(talismanLevelElem, );

		// 탈리스만 성장 가능 최대 레벨 계산 공식
		const TalismanLevel maxLevel = talismanInfoElem->maxLevel + (talismanInfo.limitBreak * talismanInfoElem->limitIncrease);

		while (talismanLevelElem->exp <= talismanInfo.exp && maxLevel > talismanInfo.level)
		{
			key.level = ++talismanInfo.level;
			talismanInfo.exp -= talismanLevelElem->exp;
			talismanLevelElem = talismanLevelScript->Get(key);
			VALID_DO(talismanLevelElem, break);
		}

		if (maxLevel == talismanInfo.level)
		{
			// 성장 가능 최대 레벨에 도달한 경우에는 경험치는 0으로 바뀐다.
			talismanInfo.exp = 0;
		}
	}

	void ActionPlayerTalisman::updateDriverSlotCount(const Bool isDbLoad)
	{	
		// 드라이버 슬롯 개수 증가
		const TalismanDriverSlotElem* driverSlotElem = SCRIPTS.GetScript<TalismanDriverSlotScript>()->Get(GetCollectionLevel());
		if (nullptr != driverSlotElem)
		{
			if (false == isDbLoad && GetCurrnetDriverSlotCount() < driverSlotElem->driverSlot)
			{
				openDriverSlotAchievement();

				auto log = NEW EReqLogDb2;
				EventSetter::FillLogForEntity(LogCode::E_INCREASE_TALISMAN_DRIVER_SLOT, GetOwnerPlayer(), log->action);
				log->action.var32s.push_back(static_cast<Int32>(driverSlotElem->driverSlot - 1));
				log->action.var32s.push_back(static_cast<Int32>(GetCollectionLevel()));
				SendDataCenter(EventPtr(log));
			}

			m_curDriverSlotCount = driverSlotElem->driverSlot;
		}
	}

	Bool ActionPlayerTalisman::getTalismanInDriverFromSkillIndex(const IndexSkill skillIndex, __out TalismanInfo& talismanInfo) const
	{
		const DriverPage* foundPage = findDriverPage(GetCurrentDriverPageOffset());
		VALID_RETURN(foundPage, false);

		auto iterSlotInfo = std::find_if(foundPage->begin(), foundPage->end(), [&](const TalismanDriverSlotInfo& slotInfo)
		{
			return slotInfo.talismanSkillIndex == skillIndex;
		});
		VALID_RETURN(foundPage->end() != iterSlotInfo, false);

		const TalismanInfo* foundTalisman = findTalisman(iterSlotInfo->talismanIndex);
		VALID_RETURN(foundTalisman, false);

		talismanInfo = *foundTalisman;
		
		return true;
	}

	void ActionPlayerTalisman::activateTalismanSystem(const Bool isDbLoad)
	{
		// 실제로 이런 조건이 될 수 없지만... 치트 등을 이용해서 테스트 시 가능성이 있음.
		VALID_RETURN(false == isAbailableTalisman(), );

		auto script = SCRIPTS.GetScript<TalismanCollectionLevelScript>();
		const TalismanCollectionLevelElem* elem = script->Get(GetCollectionLevel());
		VALID_RETURN(elem, );

		m_collection.combatPower = elem->combatPower;

		// 도감이 제공하는 전투력 업데이트, 도감 정보를 확인해서 능력치 및 드라이버 증가 처리
		AppendTotalCombatPower(0, getHighestCollectionCombatPowerInAccount());
		updateAbilityByHighestCollectonLevel(0, getHighestCollectionLevelInAccount());
		updateDriverSlotCount(isDbLoad);

		m_isAbailableTalisman = true;

#ifdef __Patch_Talisman_V2_Awaken_by_kangms_20181105
#else//__Patch_Talisman_V2_Awaken_by_kangms_20181105
		if (false == isDbLoad) {
			// DB에서 로드된 정보가 아니라면 클라에게 정보 전송
			auto ntf = NEW ENtfUpdateTalismanCollection;
			ntf->talismanCollectionInfo = m_collection;
			ntf->totalCombatPower = m_totalCombatPower;
			ntf->curDriverSlotCount = GetCurrnetDriverSlotCount();
			SERVER.SendToClient(GetOwnerPlayer(), EventPtr(ntf));
		}
#endif//__Patch_Talisman_V2_Awaken_by_kangms_20181105
	}

#ifdef __Patch_Talisman_V2_Awaken_by_kangms_20181105
	void ActionPlayerTalisman::activateTalismanAwakenSystem()
	{
		VALID_RETURN(false == isAbailableTalismanAwaken(), );

		m_isAbailableTalismanAwaken = true;
	}

	ErrorTalisman::Error ActionPlayerTalisman::checkAndAwakenTalisman(const IndexTalisman indexTalisman)
	{
		ErrorTalisman::Error error = ErrorTalisman::SUCCESS;
		
		Byte nextAwakenCount = 0;
		std::map<IndexItem, UInt16> needMaterialItemList;
		Zen needZen;
		GreenZen needGreenZen;

		error = this->checkTalismanAwaken( indexTalisman
			                             , __out nextAwakenCount
			                             , __out needMaterialItemList
			                             , __out needZen
										 , __out needGreenZen );
		VALID_RETURN(ErrorTalisman::SUCCESS == error, error);

		error = this->awakenTalisman( indexTalisman
			                        , nextAwakenCount
		                            , needMaterialItemList
		                            , needZen
									, needGreenZen );
		VALID_RETURN(ErrorTalisman::SUCCESS == error, error);

		return ErrorTalisman::SUCCESS;
	}

	ErrorTalisman::Error ActionPlayerTalisman::checkTalismanAwaken( const IndexTalisman indexTalisman
																  , __out Byte& nextAwakenCount
																  , __out std::map<IndexItem, UInt16>& needMaterialItemList
																  , __out Zen& needZen
																  , __out GreenZen& needGreenZen )
	{
		EntityPlayer* player = GetOwnerPlayer();
		VERIFY_RETURN(player && player->IsValid(), ErrorTalisman::playerNotFound);

		//탈리스만 각성 정보 조회
		auto talismanScript = SCRIPTS.GetScript<TalismanInfoScript>();
		const TalismanInfoElem* talismanElem = talismanScript->Get(indexTalisman);
		VALID_RETURN(talismanElem, ErrorTalisman::NOT_FIND_TALISMAN_SCRIPT);
		VALID_RETURN(0 < talismanElem->talismanAwakenIndex, ErrorTalisman::CAN_NOT_AWAKEN_TALISMAN);

		const TalismanInfo* talisman = this->findTalisman(indexTalisman);
		VALID_RETURN(talisman, ErrorTalisman::NOT_EXIST_TALISMAN);
		
		//다음 탈리스만 각성 단계 설정
		nextAwakenCount = talisman->awaken + 1;

		//탈리스만 각성 조건 체크
		auto talismanAwakenScript = SCRIPTS.GetScript<TalismanAwakenInfoScript>();
		const TalismanAwakenInfoElem* talismanAwakenElem = talismanAwakenScript->Get(talismanElem->talismanAwakenIndex, nextAwakenCount);
		VALID_RETURN(nullptr != talismanAwakenElem, ErrorTalisman::CAN_NOT_MORE_AWAKEN);
		VALID_RETURN(talisman->limitBreak >= talismanAwakenElem->limitBreak, ErrorTalisman::NOT_ENOUGH_LIMIT_BREAK_COUNT);

		//check material items
		needMaterialItemList = talismanAwakenElem->materialItemList;
		for (auto& itemInfo : needMaterialItemList) {

			if (true != player->GetInventoryAction().IsEnoughItem(InvenWithPreminumsStorages, itemInfo.first, itemInfo.second)) {
				return ErrorTalisman::NOT_FIND_MATERIAL_ITEM;
			}
		}

		//check zen
		needZen = talismanAwakenElem->reqZen;
		if (0 < needZen) {
			if (true != player->IsEnoughZen(needZen)) {
				return ErrorTalisman::NOT_ENOUGH_ZEN;
			}
		}

		//check greenzen
		needGreenZen = talismanAwakenElem->reqGreenZen;
		if (0 < needGreenZen) {
			if (true != player->IsEnoughGreenZen(needGreenZen)) {
				return ErrorTalisman::NOT_ENOUGH_GREENZEN;
			}
		}

		return ErrorTalisman::SUCCESS;
	}

	ErrorTalisman::Error ActionPlayerTalisman::awakenTalisman( const IndexTalisman indexTalisman
															 , const Byte nextAwakenCount
		                                                     , const std::map<IndexItem, UInt16>& needMaterialItemList
															 , const Zen needZen
															 , const GreenZen needGreenZen )
	{
		EntityPlayer* player = GetOwnerPlayer();
		VERIFY_RETURN(player && player->IsValid(), ErrorTalisman::playerNotFound);

		const TalismanInfo* talisman = this->findTalisman(indexTalisman);
		VALID_RETURN(talisman, ErrorTalisman::NOT_EXIST_TALISMAN);
		
		//갱신할 탈리스만 각성 정보 설정
		TalismanInfo talismanCloned;
		talismanCloned = *talisman;
		talismanCloned.awaken = nextAwakenCount;

		const UInt32 oldCollectionPoint = talismanCloned.collectionPoint;
		const UInt32 oldCombatPower = talismanCloned.combatPower;
		this->calculateCollectionPointAndCombatPower(talismanCloned);

		//DB 트랜잭션 처리
		ItemTalismanAwakenTransaction* trans = new ItemTalismanAwakenTransaction( __FUNCTION__, __LINE__
																				, player, LogCode::E_AWAKEN_TALISMAN
			                                                                    , oldCollectionPoint, oldCombatPower
																				, talismanCloned
																				, needMaterialItemList );
		TransactionPtr transPtr(trans);
		{
			trans->SetCost(needZen, needGreenZen);

			VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorTalisman::FAIL_TRANSACTION);
		}

		return ErrorTalisman::SUCCESS;
	}

#endif//__Patch_Talisman_V2_Awaken_by_kangms_20181105

#ifdef __Patch_Talisman_V2_Awaken_by_kangms_20181105
	//탈리스만 정보를 DB 에서 읽은 후에 호출 되어야 함 !!!
	ErrorTalisman::Error ActionPlayerTalisman::ApplyTalismanAwakenEffectAll(__inout TalismanInfo& talismanInfo)
	{
		//탈리스만 각성 정보 조회
		auto talismanScript = SCRIPTS.GetScript<TalismanInfoScript>();
		const TalismanInfoElem* talismanElem = talismanScript->Get(talismanInfo.index);
		VALID_RETURN(talismanElem, ErrorTalisman::NOT_FIND_TALISMAN_SCRIPT);
		VALID_RETURN(0 < talismanElem->talismanAwakenIndex, ErrorTalisman::CAN_NOT_AWAKEN_TALISMAN);

		//탈리스만 각성 단계의 모든 효과 목록 읽기
		std::vector<TalismanAwakenEffectInfo> talismanAwakenEffectInfoList;
		auto talismanAwakenScript = SCRIPTS.GetScript<TalismanAwakenInfoScript>();
		talismanAwakenScript->GetTalismanAwakenEffectListBy( __out talismanAwakenEffectInfoList
			                                               , talismanElem->talismanAwakenIndex
			                                               , talismanInfo.awaken, talismanInfo.limitBreak );

		for (auto& info : talismanAwakenEffectInfoList) {
			ApplyTalismanAwakenEffect(__inout talismanInfo, &info);
		}

		return ErrorTalisman::SUCCESS;
	}

	//탈리스만 각성 실행
	ErrorTalisman::Error ActionPlayerTalisman::OnReqTalismanAwakenAction(EventPtr& e)
	{
		EntityPlayer* owner = GetOwnerPlayer();
		VERIFY_RETURN(owner && owner->IsValid(), ErrorTalisman::playerNotFound);

		EczReqTalismanAwakenAction* req = static_cast<EczReqTalismanAwakenAction*>(e.RawPtr());

		auto error = this->checkAndAwakenTalisman(req->talismanIndex);
		if (ErrorTalisman::SUCCESS != error) {
			EzcResTalismanAwakenAction* res = NEW EzcResTalismanAwakenAction;
			res->eError = error;
			SERVER.SendToClient(owner, EventPtr(res));
		}

		return ErrorTalisman::SUCCESS;
	}

	//탈리스만 각성 효과
	ErrorTalisman::Error ActionPlayerTalisman::OnTalismanAwakenEffect(__inout TalismanInfo& awakenTalismanInfo)
	{
		EntityPlayer* owner = GetOwnerPlayer();
		VERIFY_RETURN(owner && owner->IsValid(), ErrorTalisman::playerNotFound);

		//탈리스만 각성 정보 조회
		auto talismanScript = SCRIPTS.GetScript<TalismanInfoScript>();
		const TalismanInfoElem* talismanElem = talismanScript->Get(awakenTalismanInfo.index);
		VALID_RETURN(talismanElem, ErrorTalisman::NOT_FIND_TALISMAN_SCRIPT);
		VALID_RETURN(0 < talismanElem->talismanAwakenIndex , ErrorTalisman::CAN_NOT_AWAKEN_TALISMAN);

		//탈리스만 각성 조건 체크
		auto talismanAwakenScript = SCRIPTS.GetScript<TalismanAwakenInfoScript>();
		const TalismanAwakenInfoElem* talismanAwakenElem = talismanAwakenScript->Get(talismanElem->talismanAwakenIndex, awakenTalismanInfo.awaken);
		VALID_RETURN(nullptr != talismanAwakenElem, ErrorTalisman::CAN_NOT_MORE_AWAKEN);
		VALID_RETURN(awakenTalismanInfo.limitBreak >= talismanAwakenElem->limitBreak, ErrorTalisman::NOT_ENOUGH_LIMIT_BREAK_COUNT);

		//탈리스만 각성 효과 적용 체크
		VALID_RETURN(0 < talismanAwakenElem->talismanAwakenEffectIndex, ErrorTalisman::SUCCESS);
		
		//탈리스만 각성 효과 체크
		auto talismanAwakenEffectScript = SCRIPTS.GetScript<TalismanAwakenEffectScript>();
		const TalismanAwakenEffectElem* talismanAwakenEffectElem = talismanAwakenEffectScript->Get(talismanAwakenElem->talismanAwakenEffectIndex);
		VALID_RETURN(nullptr != talismanAwakenEffectElem, ErrorTalisman::NOT_FOUND_TALISMAN_AWAKEN_EFFECT_SCRIPT);

		//탈리스만 각성 효과 발동
		auto taEffectList = talismanAwakenEffectElem->talismanAwakenEffectList;
		for (auto& info : taEffectList) {
			this->ApplyTalismanAwakenEffect(__inout awakenTalismanInfo, &info.second);
		}

		return ErrorTalisman::SUCCESS;
	}

	ErrorTalisman::Error ActionPlayerTalisman::ApplyTalismanAwakenEffect(__out TalismanInfo& awakenTalismanInfo, const TalismanAwakenEffectInfo* awakenEffect)
	{
		VERIFY_RETURN(nullptr != awakenEffect, ErrorTalisman::INVALID_PARAMETER);

		EntityPlayer* owner = GetOwnerPlayer();
		VERIFY_RETURN(owner && owner->IsValid(), ErrorTalisman::playerNotFound);

		switch (awakenEffect->keylogicType) {

			//탈리스만 전체 도감 점수 증가
			case TalismanAwakenEffectInfo::KeylogicType::CollectionPoint: {
				awakenTalismanInfo.collectionPoint += awakenEffect->value;
			} break;

			//탈리스만 기술 사용에 필요한 탈리스만 포인트 감소
			case TalismanAwakenEffectInfo::KeylogicType::TalismanPoint: {
				this->SubTalismanPoint(awakenEffect->value);
				this->SendToClientForUpdateTalismanPoint();
			} break;

			//탈리스만 전투력 증가
			case TalismanAwakenEffectInfo::KeylogicType::CombatPower: {
				awakenTalismanInfo.combatPower += awakenEffect->value;
			} break;

			//server after process
			//탈리스만 기술에 적용 되는 기술 재사용 대기 시간 감소
			case TalismanAwakenEffectInfo::KeylogicType::Cooltime:
			//탈리스만 기술에 적용 되는 사용 횟수 제한 증가
			case TalismanAwakenEffectInfo::KeylogicType::MaxUseCount:
			//탈리스만 기술의 레벨 별 SkillEffectIndex 변경
			case TalismanAwakenEffectInfo::KeylogicType::SkillChange:
			break;

			//client after process
			//탈리스만내에 UI & 이펙트를 출력
			case TalismanAwakenEffectInfo::KeylogicType::UIEffect:
			//탈리스만에 지정된 Image Index 를 변경
			case TalismanAwakenEffectInfo::KeylogicType::Image:
			//탈리스만 기술 사용시 지정된 컷신을 출력
			case TalismanAwakenEffectInfo::KeylogicType::CutScene:
			break;

			default: {
				ErrorTalisman::Error error = ErrorTalisman::INVALID_TALISMAN_AWAKEN_EFFECT_TYPE;
				MU2_ERROR_LOG( LogCategory::CONTENTS
							 , "Failed to awaken Talisman by Invalid KeylogicType !!! : keylogicType:%d, EffectIdx:%d, ErrCode:%d - CharID%d"
							 , awakenEffect->keylogicType, awakenEffect->index, error
							 , owner->GetCharId() );
				return error;
			}
		}

		awakenTalismanInfo.AddTalismanAwakenEffect(static_cast<TalismanAwakenEffectInfo::KeylogicType>(awakenEffect->keylogicType), *awakenEffect);

		return ErrorTalisman::SUCCESS;
	}
#endif//__Patch_Talisman_V2_Awaken_by_kangms_20181105

#ifdef __Patch_Talisman_V2_Growth_by_kangms_20181207
	ErrorTalisman::Error ActionPlayerTalisman::OnReqTalismanCoreConversion(EventPtr& e)
	{
		EntityPlayer* owner = GetOwnerPlayer();
		VERIFY_RETURN(owner && owner->IsValid(), ErrorTalisman::playerNotFound);

		EczReqTalismanCoreConversion* req = static_cast<EczReqTalismanCoreConversion*>(e.RawPtr());
		UNREFERENCED_PARAMETER(req);

		auto error = this->checkAndConvertTalismanCore();
		if (ErrorTalisman::SUCCESS != error) {

			EzcResTalismanCoreConversion* res = NEW EzcResTalismanCoreConversion;
			res->eError = error;
			SERVER.SendToClient(owner, EventPtr(res));

			if (ErrorItem::BagInsertFailed == error) {
				SendSystemMsg(owner, SystemMessage(L"sys", L"Msg_Talisman_Core_Transfer_Fail_0"));
			}
		}

		return ErrorTalisman::SUCCESS;
	}

	ErrorTalisman::Error ActionPlayerTalisman::checkAndConvertTalismanCore()
	{
		ErrorTalisman::Error error = ErrorTalisman::SUCCESS;

		std::map<IndexItem, UInt16> convertableTalismanCoreItemList;

		error = this->checkTalismanCore( __out convertableTalismanCoreItemList );
		VALID_RETURN(ErrorTalisman::SUCCESS == error, error);

		error = this->convertTalismanCore(convertableTalismanCoreItemList);
		VALID_RETURN(ErrorTalisman::SUCCESS == error, error);

		return ErrorTalisman::SUCCESS;
	}

	ErrorTalisman::Error ActionPlayerTalisman::checkTalismanCore(__out std::map<IndexItem, UInt16>& convertableTalismanCoreItemList)
	{
		EntityPlayer* owner = GetOwnerPlayer();

		std::map<IndexItem, UInt16> tcItemList;

		//탈리스만 코어 아이템 목록 조회
		ActionPlayerInventory& invenAction = GetOwnerPlayer()->GetInventoryAction();
		invenAction.ForEachSlot(InvenWithPreminumsStorages, [&](const ItemConstPtr& item) {
			VALID_RETURN(item, );
			VALID_RETURN(ItemDivisionType::TALISMAN_CORE == item->GetElem()->eDivision, );

			ItemData tcItemCloned = item->Clone();

			auto found = tcItemList.find(item->GetElem()->indexItem);
			if (found == tcItemList.end()) {
#ifdef __lockable_item_181001
				if (item->IsUnLocakble() && item->period.IsExpiredItem() == false) {
					tcItemList.emplace(item->GetElem()->indexItem, tcItemCloned.GetCurStackCount());
				}
#else
				itemList.emplace(tcItemCloned.GetItemIndex(), tcItemCloned.GetCurStackCount());
#endif
			}
			else {
#ifdef __lockable_item_181001
				if (item->IsUnLocakble() && item->period.IsExpiredItem() == false) {
					found->second += static_cast<UInt16>(tcItemCloned.GetCurStackCount());
				}
#else
				found->second += tcItemCloned.GetCurStackCount();
#endif
			}
		} );

		//탈리스만 코어 전환 조건 체크
		auto talismanCoreScript = SCRIPTS.GetScript<TalismanCoreScript>();

		for (auto itPair : tcItemList) {
			const TalismanCoreElem* elem = talismanCoreScript->Get(itPair.first);
			if (nullptr == elem) {
				MU2_ERROR_LOG( LogCategory::CONTENTS
							 , "Not found TalismanCoreElem !!! : TalismanCoreIndex:%d - CharID:%d"
							 , itPair.first
							 , owner->GetCharId() );
				continue;
			}

			//탈리스만 보유 체크
			TalismanInfo* talismanInfo = this->findTalisman(elem->talismanIndex);
			VALID_DO(nullptr != talismanInfo, continue);

			auto talismanInfoScript = SCRIPTS.GetScript<TalismanInfoScript>();
			const TalismanInfoElem* talismanInfoElem = talismanInfoScript->Get(elem->talismanIndex);
			if (nullptr == talismanInfoElem) {
				MU2_ERROR_LOG( LogCategory::CONTENTS
							 , "Not found TalismanInfoElem !!! : TalismanIndex:%d - TalismanCoreIndex:%d, CharID;%d"
							 , elem->talismanIndex, itPair.first
							 , owner->GetCharId() );
				continue;
			}

			//탈리스만 한계돌파 완료 체크
			VALID_DO(talismanInfo->limitBreak >= talismanInfoElem->maxLimitBreak, continue);

			convertableTalismanCoreItemList.emplace(itPair.first, itPair.second);
		}

		if (convertableTalismanCoreItemList.size() <= 0) {
			return ErrorTalisman::NO_CONVERTABLE_CORE_ITEM;
		}

		return ErrorTalisman::SUCCESS;
	}

	ErrorTalisman::Error ActionPlayerTalisman::convertTalismanCore(const std::map<IndexItem, UInt16>& convertableTalismanCoreItemList)
	{
		EntityPlayer* player = GetOwnerPlayer();

		std::map<IndexItem, UInt16> convertedItemList;

		//탈리스만 코어 아이템 전환
		for (auto& itemInfo : convertableTalismanCoreItemList) {
			if (true != player->GetInventoryAction().IsEnoughItem(InvenWithPreminumsStorages, itemInfo.first, itemInfo.second)) {
				MU2_ERROR_LOG( LogCategory::CONTENTS
							 , "Not found TalismanCoreItem !!! : ItemIndex:%d, ItemCount:%d - CharID:%d"
							 , itemInfo.first, itemInfo.second
							 , player->GetCharId() );
				continue;
			}

			convertedItemList.emplace(itemInfo.first, itemInfo.second);
		}

		if (convertedItemList.size() <= 0) {
			return ErrorTalisman::NO_CONVERTABLE_CORE_ITEM;
		}

		auto talismanCoreScript = SCRIPTS.GetScript<TalismanCoreScript>();
		auto itemInfoScript = SCRIPTS.GetScript<ItemInfoScript>();

		ItemBag bag(*player);

		std::map<IndexItem, UInt16> newItemList;
		for (auto convertedItemInfo : convertedItemList) {
			const TalismanCoreElem* tcElem = talismanCoreScript->Get(convertedItemInfo.first);
			if (nullptr == tcElem) {
				MU2_ERROR_LOG( LogCategory::CONTENTS
							 , "Not found TalismanCoreElem !!! : TalismanCoreIndex:%d - CharID:%d"
							 , convertedItemInfo.first
							 , player->GetCharId() );
				continue;
			}
			
			const ItemInfoElem* iElem = itemInfoScript->Get(tcElem->transferItemIndex);
			if (nullptr == iElem) {
				MU2_ERROR_LOG( LogCategory::CONTENTS
							 , "Not found ItemInfoElem !!! : TalismanCore Trasnfer ItemInfoIndex:%d - CharID:%d"
							 , tcElem->transferItemIndex
							 , player->GetCharId() );
				continue;
			}

			auto found = newItemList.find(iElem->indexItem);
			if (found != newItemList.end())
			{
				UInt32 total = static_cast<UInt32>(found->second) + convertedItemInfo.second;
				if (std::numeric_limits<UInt16>::max() < total)
				{
					// 탈리스만 코어의 동일한 아이템 지급 갯수가 숫자를 넘어가면 
					break;
				}	

				found->second = static_cast<UInt16>(total);
			}
			else
			{
				//전환되어 새로 지급되는 아이템 정보 추가
				newItemList.emplace(iElem->indexItem, convertedItemInfo.second);
			}

			//전환 가능한 탈리스만 코어 아이템 삭제
			VERIFY_RETURN( ErrorItem::SUCCESS == player->GetInventoryAction().RemoveByIndex(InvenWithPreminumsStorages, bag, convertedItemInfo.first, convertedItemInfo.second)
				         , static_cast<ErrorTalisman::Error>(ErrorItem::BagRemoveFailed) );

		}

		//새로 지급 되는 아이템 추가
		for (auto newItemInfo : newItemList) {
			VERIFY_RETURN( bag.Insert( ItemData(newItemInfo.first, newItemInfo.second) )
						 , static_cast<ErrorTalisman::Error>(ErrorItem::BagInsertFailed) );
		}

		//DB 트랜잭션 처리
		ItemTalismanCoreConvertTransaction* trans = new ItemTalismanCoreConvertTransaction( __FUNCTION__, __LINE__
																						  , player, LogCode::E_CONVERT_TALISMAN_CORE
																						  , convertedItemList
																						  , newItemList);
		TransactionPtr transPtr(trans);
		{
			VERIFY_RETURN( bag.Write(transPtr), static_cast<ErrorTalisman::Error>(ErrorItem::BagWriteFailed) );
			VERIFY_RETURN( TransactionSystem::Register(transPtr), ErrorTalisman::FAIL_TRANSACTION );
		}

		return ErrorTalisman::SUCCESS;
	}

	ErrorTalisman::Error ActionPlayerTalisman::OnTalismanGrowthRewardFromDB(DbTalismanGrowthReward& dbTalismanGrowthRewardInfo)
	{
		EntityPlayer* owner = GetOwnerPlayer();
		VERIFY_RETURN(owner && owner->IsValid(), ErrorTalisman::playerNotFound);

		TalismanGrowthReward info(dbTalismanGrowthRewardInfo);

		auto itFoundGrowthReward = m_talismanGrowthRewardList.find(dbTalismanGrowthRewardInfo.talismanIndex);
		if (itFoundGrowthReward == m_talismanGrowthRewardList.end()) {

			TalismanGrowthRewardList talismanGrowthRewardList;
			talismanGrowthRewardList.emplace(info.conditionNo, info);

			m_talismanGrowthRewardList.emplace(info.talismanIndex, talismanGrowthRewardList);
		}
		else {
			auto found = itFoundGrowthReward->second.find(info.conditionNo);
			if (found != itFoundGrowthReward->second.end()) {
				ErrorTalisman::Error error = ErrorTalisman::ALREADY_BE_REWARDED_TALISMAN_GROWTH_REWARD;
				MU2_ERROR_LOG( LogCategory::CONTENTS
							 , "Already be rewared Talisman Growth Reward !!! : ConditionNo:%d, TalismanIndex:%d, ErrCode:%d - CharID%d"
							 , info.conditionNo, info.talismanIndex, error
							 , owner->GetCharId());
				return error;
			}
			else {
				itFoundGrowthReward->second.emplace(info.conditionNo, info);
			}
		}

		return ErrorTalisman::SUCCESS;
	}

	ErrorTalisman::Error ActionPlayerTalisman::OnReqTalismanGrowthReward(EventPtr& e)
	{
		EntityPlayer* owner = GetOwnerPlayer();
		VERIFY_RETURN(owner && owner->IsValid(), ErrorTalisman::playerNotFound);

		EczReqTalismanGrowthReward* req = static_cast<EczReqTalismanGrowthReward*>(e.RawPtr());

		auto error = this->checkAndBeRewaredTalismanGrowth(req);
		if (ErrorTalisman::SUCCESS != error) {

			EzcResTalismanGrowthReward* res = NEW EzcResTalismanGrowthReward;
			res->eError = error;
			SERVER.SendToClient(owner, EventPtr(res));

			if (ErrorItem::BagInsertFailed == error) {
				SendSystemMsg(owner, SystemMessage(L"sys", L"Msg_Itemget_err_inventory_full"));
			}
		}

		return ErrorTalisman::SUCCESS;
	}

	ErrorTalisman::Error ActionPlayerTalisman::checkAndBeRewaredTalismanGrowth(EczReqTalismanGrowthReward* req)
	{
		ErrorTalisman::Error error = ErrorTalisman::SUCCESS;

		TalismanRewardInfo rewardTalismanGrowth;

		error = this->checkTalismanGrowthReward(req->talismanIndex, req->rewardConditionIndex,  __out rewardTalismanGrowth);
		VALID_RETURN(ErrorTalisman::SUCCESS == error, error);

		error = this->beRewardedTalismanGrowth(req->talismanIndex, rewardTalismanGrowth);
		VALID_RETURN(ErrorTalisman::SUCCESS == error, error);

		return ErrorTalisman::SUCCESS;
	}

	ErrorTalisman::Error ActionPlayerTalisman::checkTalismanGrowthReward( IndexTalisman talismanIndex
		                                                                , Int16 conditionNo
		                                                                , __out TalismanRewardInfo& rewardTalismanGrowth )
	{
		const TalismanInfoElem* talismanInfoElem = SCRIPTS.GetScript<TalismanInfoScript>()->Get(talismanIndex);
		VERIFY_RETURN( talismanInfoElem, ErrorTalisman::NOT_FIND_TALISMAN_SCRIPT );
		VERIFY_RETURN( talismanInfoElem->talismanRewardIndex > 0, ErrorTalisman::NOT_SUPPORT_TALISMAN_GROWTH_REWARD );

		const TalismanRewardElem* talismanRewardElem = SCRIPTS.GetScript<TalismanRewardScript>()->Get(talismanInfoElem->talismanRewardIndex);
		VERIFY_RETURN( talismanRewardElem, ErrorTalisman::NOT_FOUND_TALISMAN_REWARD_SCRIPT );
		VERIFY_RETURN( talismanRewardElem->talismanRewardList.find(conditionNo) != talismanRewardElem->talismanRewardList.end()
			         , ErrorTalisman::NOT_FOUND_TALISMAN_REWARD_CONDITION_NO );

		//중복 보상 체크
		auto itFound = m_talismanGrowthRewardList.find(talismanIndex);
		if (itFound != m_talismanGrowthRewardList.end()) {
			if (itFound->second.find(conditionNo) != itFound->second.end()) {
				return ErrorTalisman::ALREADY_BE_REWARDED_TALISMAN_GROWTH_REWARD;
			}
		}

		auto talisman = this->findTalisman(talismanIndex);
		VALID_RETURN(nullptr != talisman, ErrorTalisman::NOT_EXIST_TALISMAN);

		auto itFoundCondition = talismanRewardElem->talismanRewardList.find(conditionNo);
		switch (itFoundCondition->second.keylogicType)
		{
		case TalismanRewardInfo::KeylogicType::Get:
			break;

		case TalismanRewardInfo::KeylogicType::LimitBreak:
			VALID_RETURN(talisman->limitBreak >= static_cast<Byte>(itFoundCondition->second.value), ErrorTalisman::NOT_ENOUGH_LIMIT_BREAK_COUNT);
			break;

		case TalismanRewardInfo::KeylogicType::Awaken:
			VALID_RETURN(talisman->awaken >= static_cast<Byte>(itFoundCondition->second.value), ErrorTalisman::NOT_ENOUGH_AWAKEN_COUNT);
			break;

		case TalismanRewardInfo::KeylogicType::Level:
			VALID_RETURN(talisman->level >= static_cast<Int16>(itFoundCondition->second.value), ErrorTalisman::NOT_ENOUGH_LEVEL);
			break;

		default:
			return ErrorTalisman::NOT_FOUND_TALISMAN_REWARD_KEYLOGIC_TYPE;
		}

		VERIFY_RETURN( nullptr != SCRIPTS.GetScript<ItemInfoScript>()->Get(itFoundCondition->second.itemIndex)
			         , static_cast<ErrorTalisman::Error>(ErrorItem::E_INVALID_SCRIPT) );

		rewardTalismanGrowth = itFoundCondition->second;

		return ErrorTalisman::SUCCESS;
	}

	ErrorTalisman::Error ActionPlayerTalisman::beRewardedTalismanGrowth( IndexTalisman talismanIndex, TalismanRewardInfo& rewardTalismanGrowth )
	{
		EntityPlayer* player = GetOwnerPlayer();
		VERIFY_RETURN(player && player->IsValid(), ErrorTalisman::playerNotFound);

		ItemBag bag(*player);

		//아이템 보상 지급 가능 체크
		VERIFY_RETURN( bag.Insert(ItemData(rewardTalismanGrowth.itemIndex, rewardTalismanGrowth.itemCount))
				     , static_cast<ErrorTalisman::Error>(ErrorItem::BagInsertFailed) );

		//DB 트랜잭션 처리
		ItemTalismanGrowthRewardTransaction* trans = new ItemTalismanGrowthRewardTransaction( __FUNCTION__, __LINE__
																						    , player, LogCode::E_TALISMAN_GROWTH_REWARD
																						    , talismanIndex, rewardTalismanGrowth );
		TransactionPtr transPtr(trans);
		{
			VERIFY_RETURN( bag.Write(transPtr), static_cast<ErrorTalisman::Error>(ErrorItem::BagWriteFailed) );
			VERIFY_RETURN( TransactionSystem::Register(transPtr), ErrorTalisman::FAIL_TRANSACTION );
		}

		return ErrorTalisman::SUCCESS;
	}

	ErrorTalisman::Error ActionPlayerTalisman::reqTalismanGrowthRewardDeleteAll()
	{
		EntityPlayer* owner = GetOwnerPlayer();
		VERIFY_RETURN(owner && owner->IsValid(), ErrorTalisman::playerNotFound);

		auto reqDb = NEW EReqDbTalismanGrowthRewardDeleteAll();
		reqDb->charId = owner->GetCharId();
	
		SERVER.SendToDb(GetOwnerPlayer(), EventPtr(reqDb));

		return ErrorTalisman::SUCCESS;
	}

	ErrorTalisman::Error ActionPlayerTalisman::OnResDbTalismanGrowthRewardDeleteAll(EventPtr& e)
	{
		const EResDbTalismanGrowthRewardDeleteAll* resDb = static_cast<EResDbTalismanGrowthRewardDeleteAll*>(e.RawPtr());

		EntityPlayer* owner = GetOwnerPlayer();
		VERIFY_RETURN(owner && owner->IsValid(), ErrorTalisman::playerNotFound);

		if (ErrorDB::SUCCESS != resDb->eError) {
			return ErrorTalisman::dbError;
		}

		this->TalismanGrowthRewardClear();

		return ErrorTalisman::SUCCESS;
	}

	void ActionPlayerTalisman::TalismanGrowthRewardClear()
	{
		m_talismanGrowthRewardList.clear();

		this->Send_TalismanGrowthRewardAll();
	}

	void ActionPlayerTalisman::Send_TalismanGrowthRewardAll()
	{
		auto ntf = NEW EzcNtfTalismanGrowthRewardAll;

		ntf->talismanGrowthRewardList = m_talismanGrowthRewardList;

		SERVER.SendToClient(this->GetOwnerPlayer(), EventPtr(ntf));
	}
#endif//__Patch_Talisman_V2_Growth_by_kangms_20181207

	void ActionPlayerTalisman::LimitBreakAchievementAndSeason(const IndexTalisman indexTalisman)
	{
		/*
			업적리스트
			1. 등급별 한계돌파 탈리스만 
		*/
		ActionPlayerAchievement* aa = GetEntityAction(GetOwnerPlayer());
		AchievementTalismanLimitBreakEvent achievementHaveTalismanEvent(GetOwnerPlayer(), indexTalisman);
		aa->OnEvent(&achievementHaveTalismanEvent);

		const TalismanInfoElem* talismanInfoElem = SCRIPTS.GetScript<TalismanInfoScript>()->Get( indexTalisman );
		VALID_RETURN(talismanInfoElem, );

		UInt16 level = GetTalismanLevel( indexTalisman );

		SeasonMissionEventLimitBreakTalismanCount seasonEvent( level, talismanInfoElem->grade, talismanInfoElem->assign );
		ActionPlayerSeason * actionSeason = GetEntityAction( GetOwnerPlayer() );
		if ( nullptr != actionSeason )
		{
			actionSeason->Event( &seasonEvent );
		}
	}

	void ActionPlayerTalisman::LevelupTalismanAchievement(const IndexTalisman indexTalisman)
	{
		/*
			업적리스트
			1. 등급 + 레벨별 타리스만 보유
			2. 특정소속 + 레벨 달성 
		*/

		ActionPlayerAchievement* aa = GetEntityAction(GetOwnerPlayer());
		AchievementHaveTalismanEvent achievementHaveTalismanEvent(GetOwnerPlayer(), indexTalisman);
		aa->OnEvent(&achievementHaveTalismanEvent);
	}
	
	void ActionPlayerTalisman::createAchievementAndSeason(const IndexTalisman indexTalisman )
	{
		/*
			업적 리스트 
			1. 탈리스만 보유개수
			2. 특정소속 탈리스만 n개보유
			3. 등급별 탈리스만 보유
		
		*/
		ActionPlayerAchievement* aa = GetEntityAction(GetOwnerPlayer());
		{
			AchievementGetTalismanAsignIndexEvent achievementGetTalismanAsignIndexEvent(GetOwnerPlayer(), indexTalisman);
			aa->OnEvent(&achievementGetTalismanAsignIndexEvent);
		}
		{
			AchievementHaveTalismanEvent achievementHaveTalismanEvent(GetOwnerPlayer(), indexTalisman);
			aa->OnEvent(&achievementHaveTalismanEvent);
		}

		const TalismanInfoElem* talismanInfoElem = SCRIPTS.GetScript<TalismanInfoScript>()->Get( indexTalisman );
		VALID_RETURN(talismanInfoElem, );

		SeasonMissionEventCreateTalismanCount seasonEvent( talismanInfoElem->grade, talismanInfoElem->assign );
		ActionPlayerSeason * actionSeason = GetEntityAction( GetOwnerPlayer() );
		if ( nullptr != actionSeason )
		{
			actionSeason->Event( &seasonEvent );
		}
	}
	
	void ActionPlayerTalisman::checkCollectionPointSeason( const UInt32 totalPoint )
	{
		SeasonMissionEventTalismanCollectionPoint seasonEvent( totalPoint );
		ActionPlayerSeason * actionSeason = GetEntityAction( GetOwnerPlayer() );
		if ( nullptr != actionSeason )
		{
			actionSeason->Event( &seasonEvent );
		}
	}
	
	void ActionPlayerTalisman::onCollectionLevelUp(TalismanCollectionLevel lv)
	{
		/*
			업적 리스트
			1. 탈리스만 도감레벨
		*/

		EntityPlayer* owner = GetOwnerPlayer();
		VERIFY_RETURN(owner && owner->IsValid(), );

		{
			EReqLogDb2* log = NEW EReqLogDb2;
			EventSetter::FillLogForEntity(LogCode::E_LEVEL_UP_TALISMAN_COLLECTION, owner, log->action);
			log->action.var32s.push_back(static_cast<Int32>(lv));
			SendDataCenter(EventPtr(log));
		}
		
		AchievementTalismanCollectionLevelEvent achievementTalismanCorretLevelEvent(owner, lv );
		owner->GetAchievementAction().OnEvent(&achievementTalismanCorretLevelEvent);
	}

	void ActionPlayerTalisman::InvokeAchievementByTalismanCollectionLevel() const
	{
		// 탈리스만 이전으로 인해서, 업적 재호출

		EntityPlayer* owner = GetOwnerPlayer();
		VERIFY_RETURN(owner && owner->IsValid(), );

		for (TalismanCollectionLevel lv = 2; lv <= GetCollectionLevel(); ++lv)
		{	
			AchievementTalismanCollectionLevelEvent achievementTalismanCorretLevelEvent(owner, lv);
			owner->GetAchievementAction().OnEvent(&achievementTalismanCorretLevelEvent);
		}
	}
	
	void ActionPlayerTalisman::openDriverSlotAchievement()
	{
		/*
			업적 리스트
			1. 탈리스만 드라이버 슬롯 오픈
		*/
		ActionPlayerAchievement* aa = GetEntityAction(GetOwnerPlayer());
		AchievementTalismanDriverSlotOpenEvent achievementTalismanDriverSlotOpenEvent(GetOwnerPlayer());
		aa->OnEvent(&achievementTalismanDriverSlotOpenEvent);
		
	}
	
	void ActionPlayerTalisman::equipSkillDriverSlotAchievement()
	{
		/*
			업적 리스트
			1. 탈리스만 드라이버 슬롯 기술 장착 
		*/

		ActionPlayerAchievement* aa = GetEntityAction(GetOwnerPlayer());
		AchievementTalismanMountingEvent achievementTalismanMountingEvent(GetOwnerPlayer());
		aa->OnEvent(&achievementTalismanMountingEvent);
	}
	
	void ActionPlayerTalisman::useSkillAchievement()
	{
		/*
			업적 리스트
			1. 탈리스만 스킬 사용 
		*/

		ActionPlayerAchievement* aa = GetEntityAction(GetOwnerPlayer());
		AchievementTalismanSkillUseEvent achievementTalismanSkillUseEvent(GetOwnerPlayer());
		aa->OnEvent(&achievementTalismanSkillUseEvent);
	}

	TalismanLevel ActionPlayerTalisman::GetTalismanLevel(const IndexTalisman indexTalisman) const
	{
		const TalismanInfo* foundTalisman = findTalisman(indexTalisman);
		VALID_RETURN(foundTalisman, 0);
		return foundTalisman->level;
	}

	Byte ActionPlayerTalisman::GetTalismanLimitBreak(const IndexTalisman indexTalisman) const
	{
		const TalismanInfo* foundTalisman = findTalisman(indexTalisman);
		VALID_RETURN(foundTalisman, 0);
		return foundTalisman->limitBreak;
	}
	
	//-------------------------- 치트 --------------------------//
	void ActionPlayerTalisman::cheatReqTalismanReset()
	{
		auto reqDb = NEW EReqDbDeleteTalismans;
		SERVER.SendToDb(GetOwnerPlayer(), EventPtr(reqDb));
	}

	ErrorTalisman::Error ActionPlayerTalisman::OnResDbDeleteTalismans(EventPtr& e)
	{
		// 치트용 DB 업데이트 호출 함수
		EResDbDeleteTalismans* resDb = static_cast<EResDbDeleteTalismans*>(e.RawPtr());
		VERIFY_RETURN(ErrorDB::SUCCESS == resDb->eError, ErrorTalisman::dbError);

		EntityPlayer* owner = GetOwnerPlayer();
		VERIFY_RETURN(owner && owner->IsValid(), ErrorTalisman::playerNotFound);

		m_curDriverSlotCount = 0;
		m_talismans.clear();
		m_talismanSkillUsedCount.clear();
		m_totalCombatPower = 0;
		m_isChangeableDriver = true;

		resetDriver();

		const TalismanCollectionLevel oldCollectionLevel = GetCollectionLevel();
		(void)oldCollectionLevel; // 未使用变量标记

		m_collection.totalCollectionPoint = 0;
		m_collection.level = 1;
		
		const TalismanCollectionLevelElem* elem = SCRIPTS.GetScript<TalismanCollectionLevelScript>()->Get(GetCollectionLevel());
		VALID_RETURN(elem && elem->collectionPoint, ErrorTalisman::elemNotFound);

		m_collection.combatPower = elem->combatPower;

		AppendTotalCombatPower(0, getHighestCollectionCombatPowerInAccount());
		updateAbilityByHighestCollectonLevel(0, getHighestCollectionLevelInAccount());
		updateDriverSlotCount(false);

		ENtfTalismanInfos* ntf = NEW ENtfTalismanInfos;
		EventPtr ntfPtr(ntf);
		{
			ntf->talismanInfos.clear();
			ntf->curDriverSlotPageNumber = GetCurrentDriverPageOffset();
			ntf->curDriverSlotCount = GetCurrnetDriverSlotCount();
			ntf->talismanCollectionInfo = m_collection;
			ntf->totalCombatPower = m_totalCombatPower;
			ntf->isAbailableTalisman = isAbailableTalisman();
#ifdef __Patch_Talisman_V2_Growth_by_kangms_20181207
			ntf->isAbailableTalismanAwaken = this->isAbailableTalismanAwaken();
#endif//__Patch_Talisman_V2_Growth_by_kangms_20181207

			for (const auto& iterDriver : m_driver)
			{
				for (const TalismanDriverSlotInfo& slotInfo : iterDriver.second)
				{
					ntf->talismanDriverSlotInfos.push_back(slotInfo);
				}
			}
			owner->SendToClient(ntfPtr);
		}

#ifdef __Patch_Talisman_V2_Growth_by_kangms_20181207
		m_talismanGrowthRewardList.clear();
		Send_TalismanGrowthRewardAll();
#endif//__Patch_Talisman_V2_Growth_by_kangms_20181207

		return ErrorTalisman::SUCCESS;

	}
	
	void ActionPlayerTalisman::cheatCreate(const IndexTalisman index, TalismanLevel level, Byte limitBreak)
	{
		EntityPlayer* player = GetOwnerPlayer();
		VERIFY_RETURN(player && player->IsValid(), );

		// 탈리스만 컨텐츠 활성화 여부 체크
		if (false == isAbailableTalisman())
		{
			SendSystemMsg(player, SystemMessage(L"sys", L"Msg_Talisman_Contents_Block"));
			return;
		}

		VERIFY_RETURN(index, );

		level = (0 == level) ? 1 : level;
		
		// 이미 존재하는 탈리스만이면 실패
		VERIFY_RETURN(false == IsHasTalisman(index), );

		const TalismanInfoElem* talismanInfoElem = SCRIPTS.GetScript<TalismanInfoScript>()->Get(index);
		VERIFY_RETURN(talismanInfoElem, );

		// 최대 가능한 한계돌파 횟수 체크
		if (talismanInfoElem->maxLimitBreak < limitBreak)
		{
			limitBreak = talismanInfoElem->maxLimitBreak;
		}

		// 탈리스만 성장 가능 최대 레벨 계산 공식
		const TalismanLevel maxLevel = talismanInfoElem->maxLevel + (limitBreak * talismanInfoElem->limitIncrease);
		if (maxLevel < level)
		{
			level = maxLevel;
		}

		TalismanInfo newTalisman;
		newTalisman.index = index;
		newTalisman.level = level;
		newTalisman.limitBreak = limitBreak;
		calculateCollectionPointAndCombatPower(newTalisman);

		std::vector<TalismanMaterialItemInfo> useMaterialItemInfos; // 치트이기 때문에 재료는 없음

		ItemCreateTalismanTransaction* trans = new ItemCreateTalismanTransaction(__FUNCTION__, __LINE__, player, LogCode::E_CREATE_TALISMAN, newTalisman, useMaterialItemInfos);
		TransactionPtr transPtr(trans);
		{	
			trans->SetCost(0, 0);
			VERIFY_RETURN(TransactionSystem::Register(transPtr), );
		}
	}
	
	void ActionPlayerTalisman::cheatExp(const IndexTalisman index, const UInt32 addExp)
	{
		EntityPlayer* player = GetOwnerPlayer();
		VERIFY_RETURN(player && player->IsValid(), );

		// 탈리스만 컨텐츠 활성화 여부 체크
		if (false == isAbailableTalisman())
		{
			SendSystemMsg(player, SystemMessage(L"sys", L"Msg_Talisman_Contents_Block"));
			return;
		}

		VERIFY_RETURN(addExp, );

		const TalismanInfo* foundTalisman = findTalisman(index);
		VALID_RETURN(foundTalisman, );

		TalismanInfo talismanCloned = *foundTalisman;
		talismanCloned.exp += addExp;;
		calculateTalismanLevel(__inout talismanCloned);
																	
		const UInt32 oldCollectionPoint = talismanCloned.collectionPoint;
		const UInt32 oldCombatPower = talismanCloned.combatPower;
		calculateCollectionPointAndCombatPower(__inout talismanCloned);

		std::vector<TalismanMaterialItemInfo> useMaterialItemInfos; // 치트이기 때문에 재료는 없음
		TransactionPtr transPtr = std::make_shared<ItemGrowTalismanTransaction>(__FUNCTION__, __LINE__, player, LogCode::E_GROW_TALISMAN, talismanCloned, useMaterialItemInfos, oldCollectionPoint, oldCombatPower);
		{
			VERIFY_RETURN(TransactionSystem::Register(transPtr), );
		}		
	}
	
	void ActionPlayerTalisman::cheatLevel(const IndexTalisman index, TalismanLevel level, Byte limitBreak)
	{
		EntityPlayer* player = GetOwnerPlayer();
		VERIFY_RETURN(player && player->IsValid(), );

		// 탈리스만 컨텐츠 활성화 여부 체크
		if (false == isAbailableTalisman())
		{
			SendSystemMsg(player, SystemMessage(L"sys", L"Msg_Talisman_Contents_Block"));
			return;
		}

		const TalismanInfoElem* talismanInfoElem = SCRIPTS.GetScript<TalismanInfoScript>()->Get(index);
		VERIFY_RETURN(talismanInfoElem, );

		const TalismanInfo* foundTalisman = findTalisman(index);
		VALID_RETURN(foundTalisman, );

		level = (0 == level) ? 1 : level;

		// 최대 가능한 한계돌파 횟수 체크
		if (talismanInfoElem->maxLimitBreak < limitBreak)
		{
			limitBreak = talismanInfoElem->maxLimitBreak;
		}

		// 탈리스만 성장 가능 최대 레벨 계산 공식
		const TalismanLevel maxLevel = talismanInfoElem->maxLevel + (limitBreak * talismanInfoElem->limitIncrease);
		if (maxLevel < level)
		{
			level = maxLevel;
		}

		TalismanInfo talismanCloned = *foundTalisman;
		talismanCloned.level = level;
		talismanCloned.limitBreak = limitBreak;

		const UInt32 oldCollectionPoint = talismanCloned.collectionPoint;
		const UInt32 oldCombatPower = talismanCloned.combatPower;
		calculateCollectionPointAndCombatPower(__inout talismanCloned);

		std::vector<TalismanMaterialItemInfo> useMaterialItemInfos; // 치트이기 때문에 재료는 없음
		TransactionPtr transPtr = std::make_shared<ItemGrowTalismanTransaction>(__FUNCTION__,__LINE__, player, LogCode::E_GROW_TALISMAN, talismanCloned, useMaterialItemInfos, oldCollectionPoint, oldCombatPower);
		{
			VERIFY_RETURN(TransactionSystem::Register(transPtr), );
		}
		
	}
	
	void ActionPlayerTalisman::cheatTalismanPoint(const UInt32 talismanPoint)
	{
		EntityPlayer* player = GetOwnerPlayer();
		VERIFY_RETURN(player && player->IsValid(), );

		// 탈리스만 컨텐츠 활성화 여부 체크
		if (false == isAbailableTalisman())
		{
			SendSystemMsg(player, SystemMessage(L"sys", L"Msg_Talisman_Contents_Block"));
			return;
		}

		VERIFY_RETURN(IsUseableTalismanSkill(), );

		m_curTalismanPoint = (m_maxTalismanPoint < (m_curTalismanPoint + talismanPoint)) ?
			m_maxTalismanPoint : (m_curTalismanPoint + talismanPoint);

		auto ntf = NEW ENtfUpdateTalismanSkillInfo;
		ntf->talismanPoint = m_curTalismanPoint;
		player->SendToClient(EventPtr(ntf));
	}
	
	void ActionPlayerTalisman::cheatSkillCountReset()
	{
		EntityPlayer* player = GetOwnerPlayer();
		VERIFY_RETURN(player && player->IsValid(), );

		// 탈리스만 컨텐츠 활성화 여부 체크
		if (false == isAbailableTalisman())
		{
			SendSystemMsg(player, SystemMessage(L"sys", L"Msg_Talisman_Contents_Block"));
			return;
		}

		VERIFY_RETURN(IsUseableTalismanSkill(), );

		std::map<IndexSkill, UInt32> skillCountInfos;
		for (const auto& iterDriver : m_driver)
		{
			for (const TalismanDriverSlotInfo& driverSlotInfo : iterDriver.second)
			{
				auto iterSkill = m_talismanSkillUsedCount.find(driverSlotInfo.talismanSkillIndex);
				VALID_DO(m_talismanSkillUsedCount.end() != iterSkill, continue);
				VALID_DO(iterSkill->second, continue);

				iterSkill->second = 0;
				skillCountInfos.emplace(iterSkill->first, iterSkill->second);
			}
		}

		m_isChangeableDriver = true;

		ENtfUpdateTalismanSkillInfo* ntf = NEW ENtfUpdateTalismanSkillInfo;
		ntf->skillCountInfos = skillCountInfos;
		ntf->talismanPoint = m_curTalismanPoint;
		player->SendToClient(EventPtr(ntf));
	}
	//---------------------------------------------------------//

	Byte ActionPlayerTalisman::GetMaxTalismanEquipCount()
	{
		Byte maxCount = 0;

		for (const auto& itr : m_driver)
		{
			Byte count = 0;
			for (const TalismanDriverSlotInfo& slotInfo : itr.second)
			{
				VALID_DO(slotInfo.talismanIndex, continue);
				count++;
			}

			maxCount = std::max<Byte>(maxCount, count);
		}

		return maxCount;
	}

	void ActionPlayerTalisman::CheckTalismanCoreAndSendToClient(const IndexItem itemIndex, const Int32 itemCount) const
	{
		EntityPlayer* player = GetOwnerPlayer();
		VERIFY_RETURN(player && player->IsValid(), );

		const ItemInfoElem* itemInfoElem = SCRIPTS.GetItemInfoScript(itemIndex);
		VERIFY_RETURN(itemInfoElem, );
		VALID_RETURN(ItemDivisionType::TALISMAN_CORE == itemInfoElem->eDivision, );

		ENtfGetTalismanCore* ntf = NEW ENtfGetTalismanCore;
		ntf->coreItemIndex = itemIndex;
		ntf->coreItemCount = itemCount;
		player->SendToClient(EventPtr(ntf));
	}

	void ActionPlayerTalisman::AddTalismanPoint(const UInt32 talismanPoint)
	{
		VALID_RETURN(m_curTalismanPoint < m_maxTalismanPoint, );

		UInt32 totalTalismanPoint = m_curTalismanPoint + talismanPoint;
		m_curTalismanPoint = (m_maxTalismanPoint < totalTalismanPoint) ? m_maxTalismanPoint : totalTalismanPoint;
		
		// 잦은 패킷 전송을 막기 위해 OnResult 함수에서 모든 탈리스만 포인트 처리 로직이 모두 끝나고 한번만 전송 한다.
		m_isSendToClientTalismanPoint = true;
	}

#ifdef __Patch_Talisman_V2_Awaken_by_kangms_20181105
	void ActionPlayerTalisman::SubTalismanPoint(const UInt32 talismanPoint)
	{
		VALID_RETURN(m_curTalismanPoint <= 0, );

		Int32 totalTalismanPoint = std::max<Int32>(0, static_cast<Int32>(m_curTalismanPoint) - static_cast<Int32>(talismanPoint));
		m_curTalismanPoint = static_cast<UInt32>(totalTalismanPoint);

		// 잦은 패킷 전송을 막기 위해 OnResult 함수에서 모든 탈리스만 포인트 처리 로직이 모두 끝나고 한번만 전송 한다.
		m_isSendToClientTalismanPoint = true;
	}
#endif//__Patch_Talisman_V2_Awaken_by_kangms_20181105

	Bool ActionPlayerTalisman::GetTalisman(const IndexTalisman talismanIndex, __out TalismanInfo& talismanInfo) const
	{
		const TalismanInfo* foundTalisman = findTalisman(talismanIndex);
		VALID_RETURN(foundTalisman, false);
		talismanInfo = *foundTalisman;
		return true;
	}

	void ActionPlayerTalisman::SeasonEventForNewRotation()
	{
		EntityPlayer* player = GetOwnerPlayer();
		VERIFY_RETURN( player && player->IsValid(), );

		for ( auto& iter : m_talismans )
		{
			const IndexTalisman talismanIndex = iter.first;

			const TalismanInfoElem* talismanInfoElem = SCRIPTS.GetScript<TalismanInfoScript>()->Get( talismanIndex );
			VALID_DO(talismanInfoElem, continue );

			SeasonMissionEventCreateTalismanCount seasonCreateEvent( talismanInfoElem->grade, talismanInfoElem->assign );

			ActionPlayerSeason * actionSeason = GetEntityAction( GetOwnerPlayer() );
			if ( nullptr != actionSeason )
			{
				actionSeason->Event( &seasonCreateEvent );
			}

			if ( 0 < iter.second.limitBreak )
			{
				for ( UInt8 count = 0; count < iter.second.limitBreak; ++count )
				{
					SeasonMissionEventLimitBreakTalismanCount seasonLimitBreakEvent( iter.second.level, talismanInfoElem->grade, talismanInfoElem->assign );
					if ( nullptr != actionSeason )
					{
						actionSeason->Event( &seasonLimitBreakEvent );
					}
				}
			}
		}
		
		SeasonMissionEventTalismanCollectionPoint seasonCollectionEvent( GetTotalCollectionPoint() );
		ActionPlayerSeason * actionSeason = GetEntityAction( GetOwnerPlayer() );
		if ( nullptr != actionSeason )
		{
			actionSeason->Event( &seasonCollectionEvent );
		}
	}


	Bool ActionPlayerTalisman::addTalismanInfo(const TalismanInfo& talismanInfo)
	{
		VALID_RETURN(false == IsHasTalisman(talismanInfo.index), false);
		m_talismans.emplace(talismanInfo.index, talismanInfo);
		return true;
	}

	Bool ActionPlayerTalisman::removeTalismanInfo(const IndexTalisman& indexTalisman)
	{	
		VALID_RETURN(IsHasTalisman(indexTalisman), false);
		m_talismans.erase(indexTalisman);
		return true;
	}

	Bool ActionPlayerTalisman::updateTalismanInfo(const TalismanInfo& talismanInfo)
	{
		TalismanInfo* found = findTalisman(talismanInfo.index);
		VALID_RETURN(found, false);
		*found = talismanInfo;
		return true;
	}
	

#ifdef __Patch_Talisman_ChangeOwner_by_jason_180906
	
	ErrorTalisman::Error ActionPlayerTalisman::OnLoadCollectionInAccountFromDb(const std::vector<DbTalismanCollectionInfoWithCharInfo>& vecDb)
	{
		EntityPlayer* owner = GetOwnerPlayer();
		VERIFY_RETURN(owner && owner->IsValid(), ErrorTalisman::playerNotFound);

		m_vecTalismanCollectionInfoWithCharInfo.clear();		

		for (const DbTalismanCollectionInfoWithCharInfo& db : vecDb)
		{
			const TalismanCollectionLevelElem* talismanCollectionLevelElem = calculateCollectionLevel(db.totalCollectionPoint);
			VALID_DO(talismanCollectionLevelElem, continue);

			TalismanCollectionInfoWithCharInfo collectionInfo(db);			
			collectionInfo.collectionLevel	= talismanCollectionLevelElem->collectionLevel;
			collectionInfo.combatPower		= talismanCollectionLevelElem->combatPower;
			m_vecTalismanCollectionInfoWithCharInfo.push_back(collectionInfo);
		}

		return ErrorTalisman::SUCCESS;
	}

	TalismanCollectionLevel	ActionPlayerTalisman::getHighestCollectionLevelInAccount() const
	{
		TalismanCollectionLevel highestLevel = GetCollectionLevel();

		for (const TalismanCollectionInfoWithCharInfo& info : m_vecTalismanCollectionInfoWithCharInfo)
		{
			highestLevel = std::max<TalismanCollectionLevel>(info.collectionLevel, highestLevel);
		}

		return highestLevel;
	}	

	UInt32 ActionPlayerTalisman::getHighestCollectionCombatPowerInAccount() const
	{
		const TalismanCollectionLevelElem* elem = SCRIPTS.Get<TalismanCollectionLevelScript>().Get(getHighestCollectionLevelInAccount());
		VALID_RETURN(elem, 0);
		return elem->combatPower;
	}

	const TalismanCollectionLevelElem* ActionPlayerTalisman::calculateCollectionLevel(const UInt32& totalCollectionPoint) const
	{
		const TalismanCollectionLevelScript& script = SCRIPTS.Get<TalismanCollectionLevelScript>();		
		
		// 도감 레벨 계산
		TalismanCollectionLevel level = 1;
		const TalismanCollectionLevelElem* elem = NULL;

		while(true)
		{
			elem = script.Get(level);
			VALID_DO(elem && elem->collectionPoint, break);
			VALID_DO(elem->collectionPoint <= totalCollectionPoint, break);
			level++;
		}

		return elem;
	}

	void ActionPlayerTalisman::updateAbilityByHighestCollectonLevel(const TalismanCollectionLevel prev, const TalismanCollectionLevel curr)
	{
		VALID_RETURN(prev != curr, );

		ActionPlayerAbility& actPlayerAbility = GetOwnerPlayer()->GetAbilityAction();

		// 최초 도감 레벨에 따른 능력치 계산인 경우(0 == preCollectionLevel)에는 collectionInfoElem이 nullptr일 수 있다.
		const TalismanCollectionInfoElem* prevCollectionInfoElem = SCRIPTS.Get<TalismanCollectionInfoScript>().Get(prev);
		VALID_DO(prevCollectionInfoElem, AllowNull);

		const TalismanCollectionInfoElem* currCollectionInfoElem = SCRIPTS.Get<TalismanCollectionInfoScript>().Get(curr);
		VERIFY_RETURN(currCollectionInfoElem, );

		if ( prevCollectionInfoElem)
		{	
			// 이전에 적용 중인 도감 레벨 정보의 능력치는 제거

			for (auto removeAbilityInfo : prevCollectionInfoElem->collectionAbilityInfoList)
			{
				AbilityParam abilityParam;
				abilityParam.type = removeAbilityInfo.optionType;
				abilityParam.section = removeAbilityInfo.valueType;
				abilityParam.value = removeAbilityInfo.value;
				abilityParam.isOnOff = false;
				actPlayerAbility.ChangedSkillAbility(abilityParam);
			}
		}		

		for (auto addAbilityInfo : currCollectionInfoElem->collectionAbilityInfoList)
		{
			AbilityParam abilityParam;
			abilityParam.type = addAbilityInfo.optionType;
			abilityParam.section = addAbilityInfo.valueType;
			abilityParam.value = addAbilityInfo.value;
			abilityParam.isOnOff = true;
			actPlayerAbility.ChangedSkillAbility(abilityParam);
		}

		actPlayerAbility.Calculate();

	}

	// 제작
	ErrorTalisman::Error ActionPlayerTalisman::OnReqCreateTalisman(EventPtr& e)
	{
		EntityPlayer* owner = GetOwnerPlayer();
		VERIFY_RETURN(owner && owner->IsValid(), ErrorTalisman::playerNotFound);

		EReqCreateTalisman* req = static_cast<EReqCreateTalisman*>(e.RawPtr());

		ErrorTalisman::Error eError = createTalisman(req->talismanIndex);
		if (ErrorTalisman::SUCCESS != eError)
		{
			EResCreateTalisman* res = NEW EResCreateTalisman;
			res->eError = eError;
			owner->SendToClient(EventPtr(res));
		}

		return ErrorTalisman::SUCCESS;
	}

	// 탈리스만 성장
	ErrorTalisman::Error ActionPlayerTalisman::OnReqGrowTalisman(EventPtr& e)
	{	
		EntityPlayer* owner = GetOwnerPlayer();
		VERIFY_RETURN(owner && owner->IsValid(), ErrorTalisman::playerNotFound);

		EReqGrowTalisman* req = static_cast<EReqGrowTalisman*>(e.RawPtr());

		auto error = growTalisman(req->talismanIndex, req->mapMaterialItems);
		if (ErrorTalisman::SUCCESS != error)
		{
			EResGrowTalisman* res = NEW EResGrowTalisman;
			res->eError = error;
			SERVER.SendToClient(owner, EventPtr(res));
		}

		return ErrorTalisman::SUCCESS;
	}	

	ErrorTalisman::Error ActionPlayerTalisman::OnEczReqTalismanChangeOwner(EventPtr& e)
	{
		EczReqTalismanChangeOwner* req = static_cast<EczReqTalismanChangeOwner*>(e.RawPtr());

		EntityPlayer* owner = GetOwnerPlayer();
		VERIFY_RETURN(owner && owner->IsValid(), ErrorTalisman::playerNotFound);

		const TalismanCollectionInfoWithCharInfo* targetCollection = NULL;

		for (const TalismanCollectionInfoWithCharInfo& otherCollection : m_vecTalismanCollectionInfoWithCharInfo)
		{
			if (otherCollection.charId == req->targetCharId &&  otherCollection.totalCollectionPoint < GetTotalCollectionPoint())
			{
				targetCollection = &otherCollection;
				break;
			}
		}

		VERIFY_RETURN(targetCollection && targetCollection->IsValid(), ErrorTalisman::charOwnerTargetNotFoubnd);

		//std::set<IndexAchievement> ssetIndexAchievement;
		//getAchievement4LinkTalisman(__out ssetIndexAchievement);

		ItemTalismanChangeOwnerTransaction* trans = new ItemTalismanChangeOwnerTransaction(__FUNCTION__, __LINE__, owner, LogCode::E_TALISMAN_CHANGEOWNER, targetCollection->charId
#ifdef __LogMod_181017_by_ray_181018
			, targetCollection->collectionLevel, targetCollection->totalCollectionPoint
#endif
		);
		TransactionPtr transPtr(trans);
		{	
			VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorTalisman::TranRegisterFailed);
		}

		return ErrorTalisman::SUCCESS;
	}

	ErrorTalisman::Error ActionPlayerTalisman::OnEResDBTalismanChangeOwner(EventPtr& resDbPtr, ErrorItem::Error eError, EReqLogDb2* log)
	{
		VALID_DO(log, AllowNull);

		EntityPlayer* owner = GetOwnerPlayer();
		VERIFY_RETURN(owner && owner->IsValid(), ErrorTalisman::playerNotFound);

		const EResDBTalismanChangeOwner* resDb = static_cast<EResDBTalismanChangeOwner*>(resDbPtr.RawPtr());

		EzcResTalismanChangeOwner* res = new EzcResTalismanChangeOwner;
		EventPtr resPtr(res);
		{
			res->targetCharId = resDb->toCharId;
		}		

		if (eError != ErrorDB::SUCCESS)
		{	
			res->eError = ErrorTalisman::FAIL_TRANSACTION;
			owner->SendToClient(resPtr);
			return ErrorTalisman::dbError;
		}

		//================================================================================
		// step 1 : 이전정보 기억
		//================================================================================

		const TalismanCollectionLevel prevCollectionLevel = GetCollectionLevel();
		const UInt32 prevTotalCollectionPoint = GetTotalCollectionPoint();
		const UInt32 prevCombatPower = m_totalCombatPower;

		//================================================================================
		// step 2 : 초기화
		//================================================================================

		m_collection.Reset();
		m_talismans.clear();
		resetDriver();

		m_talismanSkillUsedCount.clear();
		m_curDriverPageOffset = 0;
		m_curDriverSlotCount = 0;
		m_curTalismanPoint = 0;
		m_bookmarkTalismans.clear();
		m_totalCombatPower = 0;
		//초기화 하면 안됨 m_isAbailableTalisman;
		m_isUseableSkill = false;
		m_maxTalismanPoint = 0;
		m_isChangeableDriver = true;
		m_isSendToClientTalismanPoint = false;
		m_curSkillKey.Reset();

		owner->GetPlayerTalismanUnitAction().ClearByChangeOwner();

		//================================================================================
		// step 3 : 업데이트
		//================================================================================

		// 나의 컬렉션정보는 이제 타켓의 컬렉션정보가 된다.
		for (TalismanCollectionInfoWithCharInfo& info : m_vecTalismanCollectionInfoWithCharInfo)
		{
			if (info.charId == resDb->toCharId)
			{
				info.totalCollectionPoint = prevTotalCollectionPoint;
				info.collectionLevel = prevCollectionLevel;
				info.combatPower = prevCombatPower;
				break;
			}
		}

		const TalismanCollectionLevelElem* elem = SCRIPTS.Get<TalismanCollectionLevelScript>().Get(GetCollectionLevel());
		VALID_RETURN(elem && elem->collectionPoint, ErrorTalisman::elemNotFound);
		m_collection.combatPower = elem->combatPower;

		AppendTotalCombatPower(0, getHighestCollectionCombatPowerInAccount());
		updateAbilityByHighestCollectonLevel(0, getHighestCollectionLevelInAccount());
		updateDriverSlotCount(true);


		//================================================================================
		// step 4 : 로그를 남기자
		//================================================================================

		if (log)
		{
		}

		//================================================================================
		// step 5 : 클라로 전송
		//================================================================================

		SendToClientAllInofs();	
		owner->GetPlayerTalismanUnitAction().SendToClientAllInofs();

#ifdef __Patch_Talisman_V2_Growth_by_kangms_20181207
		this->TalismanGrowthRewardClear();
#endif//__Patch_Talisman_V2_Growth_by_kangms_20181207
						
		res->eError = ErrorTalisman::SUCCESS;
		owner->SendToClient(resPtr);

		return ErrorTalisman::SUCCESS;
	}

	void ActionPlayerTalisman::SendToClientAllInofs() const
	{
		EntityPlayer* owner = GetOwnerPlayer();
		VERIFY_RETURN(owner && owner->IsValid(), );
		

		// 계정내 도감정보
		{
			EzcNtfTalismanCollectionInAccount* ntf = new EzcNtfTalismanCollectionInAccount;
			ntf->vecTalismanCollectionInfoWithCharInfo = m_vecTalismanCollectionInfoWithCharInfo;
			owner->SendToClient(EventPtr(ntf));
		}

		// 탈리스만
		{
			ENtfTalismanInfos* ntf = new ENtfTalismanInfos;
			for (const auto& itr : m_talismans)
				ntf->talismanInfos.push_back(itr.second);

			ntf->curDriverSlotPageNumber = GetCurrentDriverPageOffset();
			ntf->curDriverSlotCount = GetCurrnetDriverSlotCount();

			ntf->talismanCollectionInfo = m_collection;
			ntf->totalCombatPower = m_totalCombatPower;
			ntf->isAbailableTalisman = isAbailableTalisman();

#ifdef __Patch_Talisman_V2_Awaken_by_kangms_20181105
			ntf->isAbailableTalismanAwaken = isAbailableTalismanAwaken();
#endif//__Patch_Talisman_V2_Awaken_by_kangms_20181105

			for (const auto& itrPage : m_driver)
			{
				for (const TalismanDriverSlotInfo& slot : itrPage.second)
					ntf->talismanDriverSlotInfos.push_back(slot);
			}

			owner->SendToClient(EventPtr(ntf));
		}

		//즐겨찾기
		{
			ENtfBookmarkTalismans* ntfBookmark = new ENtfBookmarkTalismans;
			for (const IndexTalisman& indexTalisman : m_bookmarkTalismans)
				ntfBookmark->bookmarkTalismanIndexes.push_back(indexTalisman);
			owner->SendToClient(EventPtr(ntfBookmark));
		}

		// 탈리스만스킬
		{
			ENtfUpdateTalismanSkillInfo* ntfSkill = new ENtfUpdateTalismanSkillInfo;
			GetTalismanSkillCountInfo(ntfSkill->skillCountInfos);
			ntfSkill->talismanPoint = GetTalismanPoint();
			owner->SendToClient(EventPtr(ntfSkill));
		}
	}
	
	ErrorTalisman::Error ActionPlayerTalisman::OnGmCommand(const CommandString& cmd)
	{
		EntityPlayer* owner = GetOwnerPlayer();
		VERIFY_RETURN(owner && owner->IsValid(), ErrorTalisman::playerNotFound);

		std::wstring strCommand;
		std::array< UInt32, 3 > value;
		cmd >> strCommand >> value[0] >> value[1] >> value[2];
		

		if (L"create" == strCommand)
		{
			cheatCreate(value[0], static_cast<UInt16>(value[1]), static_cast<Byte>(value[2]));
		}
		else if (L"exp" == strCommand)
		{
			cheatExp(value[0], value[1]);
		}
		else if (L"level" == strCommand)
		{
			cheatLevel(value[0], static_cast<UInt16>(value[1]), static_cast<Byte>(value[2]));
		}
		else if (L"point" == strCommand)
		{
			cheatTalismanPoint(value[0]);
		}
		else if (L"skillreset" == strCommand)
		{
			cheatSkillCountReset();
		}
		else if (L"reset" == strCommand)
		{
			cheatReqTalismanReset();
		}
		else if (L"ownerlist" == strCommand)
		{
			for (const TalismanCollectionInfoWithCharInfo& each : m_vecTalismanCollectionInfoWithCharInfo)
			{
				theGameMsg.SendGameDebugMsg(owner, L"charId(%u) %s collection(lv:%d, point:%d) \n", each.charId, each.strCharName.c_str(), each.collectionLevel, each.totalCollectionPoint);
			}			
		}
		else if (L"changeowner" == strCommand)
		{
			EczReqTalismanChangeOwner* req = new EczReqTalismanChangeOwner;
			req->targetCharId = static_cast<CharId>(value[0]);
			theZoneHandler.Notify(owner, EventPtr(req));
		}
#ifdef __Patch_Talisman_V2_Awaken_by_kangms_20181105
		else if (L"awaken" == strCommand) {
			EczReqTalismanAwakenAction* req = new EczReqTalismanAwakenAction;
			req->talismanIndex = static_cast<IndexTalisman>(value[0]);
			theZoneHandler.Notify(owner, EventPtr(req));
		}
#endif//__Patch_Talisman_V2_Awaken_by_kangms_20181105
#ifdef __Patch_Talisman_V2_Growth_by_kangms_20181207
		else if (L"rewardreset" == strCommand) {
			this->reqTalismanGrowthRewardDeleteAll();
		}
#endif//__Patch_Talisman_V2_Growth_by_kangms_20181207

		return ErrorTalisman::SUCCESS;
	}

	const TalismanInfo* ActionPlayerTalisman::findTalisman(const IndexTalisman& indexTalisman) const
	{
		auto found = m_talismans.find(indexTalisman);
		return found != m_talismans.end() ? &found->second : NULL;
	}

	TalismanInfo* ActionPlayerTalisman::findTalisman(const IndexTalisman& indexTalisman)
	{
		auto found = m_talismans.find(indexTalisman);
		return found != m_talismans.end() ? &found->second : NULL;
	}

	Bool ActionPlayerTalisman::IsHasTalisman(const IndexTalisman talismanIndex) const 
	{ 
		return findTalisman(talismanIndex) != NULL;
	}

	void ActionPlayerTalisman::resetDriver()
	{
		for (auto& itr : m_driver)
		{			
			for (TalismanDriverSlotInfo& slot : itr.second)
			{	
				slot.ResetSlot();
			}
		}
	}

	ActionPlayerTalisman::DriverPage* ActionPlayerTalisman::findDriverPage(Byte pageOffset)
	{	
		//offset은 0부터 시작한다는 의미

		auto found = m_driver.find(pageOffset);
		return found != m_driver.end() ? &found->second : NULL;
	}

	const ActionPlayerTalisman::DriverPage* ActionPlayerTalisman::findDriverPage(Byte pageOffset) const
	{
		//offset은 0부터 시작한다는 의미

		auto found = m_driver.find(pageOffset);
		return found != m_driver.end() ? &found->second : NULL;
	}

	TalismanDriverSlotInfo& ActionPlayerTalisman::findDriverSlot(Byte pageOffset, Byte slotOffset)
	{
		//offset은 0부터 시작한다는 의미

		static TalismanDriverSlotInfo staticInvlidTalismanDriverSlotInfo(TalismanDriverSlotInfo::InvalidPage);

		DriverPage* foundPage = findDriverPage(pageOffset);
		VALID_RETURN(foundPage, staticInvlidTalismanDriverSlotInfo);
		VALID_RETURN((size_t)slotOffset < foundPage->size(), staticInvlidTalismanDriverSlotInfo);

		return (*foundPage)[slotOffset];
	}

	//void ActionPlayerTalisman::getAchievement4LinkTalisman(__out std::set<IndexAchievement>& ssetIndexAchievement)
	//{
	//	for (const auto& itr : SCRIPTS.Get<AchievementConditionScript>().Get())
	//	{
	//		const AchievementConditionElem& elem = itr.second;	

	//		IndexAchievement foundAchievement = 0;			

	//		for ( const AchievementConditionPart& part : elem.conditions)
	//		{
	//			AchievementConditionType::Enum eCondition = static_cast<AchievementConditionType::Enum>(part.activateCondition);

	//			switch (eCondition)
	//			{
	//			case AchievementConditionType::GET_TALISMAN_INDEX:
	//			case AchievementConditionType::TALISMAN_ASSIGN_INDEX:
	//			case AchievementConditionType::HAVE_TALISMAN:
	//			case AchievementConditionType::TALISMAN_CORRECT_LEVEL:
	//			case AchievementConditionType::TALISMAN_DRIVER_SLOT:
	//			case AchievementConditionType::TALISMAN_GRADE:
	//			case AchievementConditionType::TALISMAN_LEVEL:
	//			case AchievementConditionType::TALISMAN_LIMIT_BREAK:
	//			case AchievementConditionType::TALISMAN_SKILL_MOUNTING:
	//			//case AchievementConditionType::TALISMAN_SKILL_USE:
	//			case AchievementConditionType::TALISMAN_INDEX:
	//				foundAchievement = elem.achievementIndex;
	//				break;

	//			default:
	//				break;
	//			}

	//			if (foundAchievement != 0)
	//			{
	//				ssetIndexAchievement.insert(foundAchievement);
	//				break;
	//			}
	//			
	//		}
	//	}
	//}

#endif __Patch_Talisman_ChangeOwner_by_jason_180906

#ifdef __Patch_Tower_of_dawn_eunseok_2018_11_05
void ActionPlayerTalisman::AddTalismanPointWhenEnterZone()
{
	EntityPlayer* player = GetOwnerPlayer();
	VALID_RETURN(player, );

	Sector* sector = player->GetSector();
	VALID_RETURN(sector, );

	auto script = SCRIPTS.GetScript<TalismanSkillPointScript>();
	VALID_RETURN(script, );

	auto worldElem = sector->GetWorldElem();


	Byte talismanSkillPointType = worldElem.talismanSkillType;
	TalismanSkillPointKey key(talismanSkillPointType, TalismanSkillPointValueType::ENTER_ZONE);
	const TalismanSkillPointElem* elem = script->Get(key);
	if (nullptr == elem)
	{
		return;
	}
	
	auto iter = elem->mapGetTalismanPoint.find(static_cast<Int32>(worldElem.index));
	if (elem->mapGetTalismanPoint.end() == iter)
	{
		return;
			
	}
	AddTalismanPoint(iter->second);

}

#endif //__Patch_Tower_of_dawn_eunseok_2018_11_05



}
