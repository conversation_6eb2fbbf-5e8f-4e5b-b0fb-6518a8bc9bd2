; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	??0exception@std@@QEAA@AEBV01@@Z		; std::exception::exception
PUBLIC	?what@exception@std@@UEBAPEBDXZ			; std::exception::what
PUBLIC	??_Gexception@std@@UEAAPEAXI@Z			; std::exception::`scalar deleting destructor'
PUBLIC	??0bad_alloc@std@@QEAA@AEBV01@@Z		; std::bad_alloc::bad_alloc
PUBLIC	??_Gbad_alloc@std@@UEAAPEAXI@Z			; std::bad_alloc::`scalar deleting destructor'
PUBL<PERSON>	??0bad_array_new_length@std@@QEAA@XZ		; std::bad_array_new_length::bad_array_new_length
PUBLIC	??1bad_array_new_length@std@@UEAA@XZ		; std::bad_array_new_length::~bad_array_new_length
PUBLIC	??0bad_array_new_length@std@@QEAA@AEBV01@@Z	; std::bad_array_new_length::bad_array_new_length
PUBLIC	??_Gbad_array_new_length@std@@UEAAPEAXI@Z	; std::bad_array_new_length::`scalar deleting destructor'
PUBLIC	?_Throw_bad_array_new_length@std@@YAXXZ		; std::_Throw_bad_array_new_length
PUBLIC	?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
PUBLIC	?_Xlen_string@std@@YAXXZ			; std::_Xlen_string
PUBLIC	?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z	; std::allocator<wchar_t>::allocate
PUBLIC	?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEAAPEA_WXZ ; std::_String_val<std::_Simple_types<wchar_t> >::_Myptr
PUBLIC	??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
PUBLIC	?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_empty
PUBLIC	??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV01@AEBV01@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::operator=
PUBLIC	?assign@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV12@QEB_W_K@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::assign
PUBLIC	?clear@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAXXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::clear
PUBLIC	?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
PUBLIC	?_Calculate_growth@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@CA_K_K00@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Calculate_growth
PUBLIC	?_Eos@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAX_K@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Eos
PUBLIC	?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate
PUBLIC	??$_Reallocate_for@V<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Reallocate_for<<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>,wchar_t const *>
PUBLIC	??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
PUBLIC	??0BurningStaminaUserInfo@mu2@@QEAA@XZ		; mu2::BurningStaminaUserInfo::BurningStaminaUserInfo
PUBLIC	??1BurningStaminaUserInfo@mu2@@QEAA@XZ		; mu2::BurningStaminaUserInfo::~BurningStaminaUserInfo
PUBLIC	?SetUserInfo@BurningStaminaUserInfo@mu2@@QEAAXIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z ; mu2::BurningStaminaUserInfo::SetUserInfo
PUBLIC	?ResetUserInfo@BurningStaminaUserInfo@mu2@@QEAAXXZ ; mu2::BurningStaminaUserInfo::ResetUserInfo
PUBLIC	?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUBurningStaminaContinentSimepleInfo@2@@Z ; mu2::BurningStaminaUserInfo::FillUp
PUBLIC	?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUBurningStaminaBurningInfo@2@@Z ; mu2::BurningStaminaUserInfo::FillUp
PUBLIC	?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUBurningStaminaInstanceDungeonInfo@2@@Z ; mu2::BurningStaminaUserInfo::FillUp
PUBLIC	?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUBurningStaminaZoneServerInfo@2@@Z ; mu2::BurningStaminaUserInfo::FillUp
PUBLIC	?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUDBBurningStaminaInfo@2@@Z ; mu2::BurningStaminaUserInfo::FillUp
PUBLIC	??_7exception@std@@6B@				; std::exception::`vftable'
PUBLIC	??_C@_0BC@EOODALEL@Unknown?5exception@		; `string'
PUBLIC	??_7bad_alloc@std@@6B@				; std::bad_alloc::`vftable'
PUBLIC	??_7bad_array_new_length@std@@6B@		; std::bad_array_new_length::`vftable'
PUBLIC	??_C@_0BF@KINCDENJ@bad?5array?5new?5length@	; `string'
PUBLIC	??_R0?AVexception@std@@@8			; std::exception `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
PUBLIC	_TI3?AVbad_array_new_length@std@@
PUBLIC	_CTA3?AVbad_array_new_length@std@@
PUBLIC	??_R0?AVbad_array_new_length@std@@@8		; std::bad_array_new_length `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
PUBLIC	??_R0?AVbad_alloc@std@@@8			; std::bad_alloc `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
PUBLIC	??_C@_0BA@JFNIOLAK@string?5too?5long@		; `string'
PUBLIC	??_R4exception@std@@6B@				; std::exception::`RTTI Complete Object Locator'
PUBLIC	??_R3exception@std@@8				; std::exception::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2exception@std@@8				; std::exception::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@exception@std@@8			; std::exception::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4bad_array_new_length@std@@6B@		; std::bad_array_new_length::`RTTI Complete Object Locator'
PUBLIC	??_R3bad_array_new_length@std@@8		; std::bad_array_new_length::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2bad_array_new_length@std@@8		; std::bad_array_new_length::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@bad_array_new_length@std@@8	; std::bad_array_new_length::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@bad_alloc@std@@8			; std::bad_alloc::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R3bad_alloc@std@@8				; std::bad_alloc::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2bad_alloc@std@@8				; std::bad_alloc::`RTTI Base Class Array'
PUBLIC	??_R4bad_alloc@std@@6B@				; std::bad_alloc::`RTTI Complete Object Locator'
EXTRN	??2@YAPEAX_K@Z:PROC				; operator new
EXTRN	??3@YAXPEAX_K@Z:PROC				; operator delete
EXTRN	__std_terminate:PROC
EXTRN	_invoke_watson:PROC
EXTRN	memcpy:PROC
EXTRN	memmove:PROC
EXTRN	__std_exception_copy:PROC
EXTRN	__std_exception_destroy:PROC
EXTRN	??_Eexception@std@@UEAAPEAXI@Z:PROC		; std::exception::`vector deleting destructor'
EXTRN	??_Ebad_alloc@std@@UEAAPEAXI@Z:PROC		; std::bad_alloc::`vector deleting destructor'
EXTRN	??_Ebad_array_new_length@std@@UEAAPEAXI@Z:PROC	; std::bad_array_new_length::`vector deleting destructor'
EXTRN	?_Xlength_error@std@@YAXPEBD@Z:PROC		; std::_Xlength_error
EXTRN	_CxxThrowException:PROC
EXTRN	__CxxFrameHandler4:PROC
EXTRN	??_7type_info@@6B@:BYTE				; type_info::`vftable'
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0exception@std@@QEAA@AEBV01@@Z DD imagerel $LN4
	DD	imagerel $LN4+89
	DD	imagerel $unwind$??0exception@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?what@exception@std@@UEBAPEBDXZ DD imagerel $LN5
	DD	imagerel $LN5+56
	DD	imagerel $unwind$?what@exception@std@@UEBAPEBDXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gexception@std@@UEAAPEAXI@Z DD imagerel $LN10
	DD	imagerel $LN10+83
	DD	imagerel $unwind$??_Gexception@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z DD imagerel $LN9
	DD	imagerel $LN9+104
	DD	imagerel $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+83
	DD	imagerel $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_array_new_length@std@@QEAA@XZ DD imagerel $LN14
	DD	imagerel $LN14+95
	DD	imagerel $unwind$??0bad_array_new_length@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1bad_array_new_length@std@@UEAA@XZ DD imagerel $LN14
	DD	imagerel $LN14+47
	DD	imagerel $unwind$??1bad_array_new_length@std@@UEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z DD imagerel $LN14
	DD	imagerel $LN14+54
	DD	imagerel $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z DD imagerel $LN20
	DD	imagerel $LN20+83
	DD	imagerel $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Throw_bad_array_new_length@std@@YAXXZ DD imagerel $LN3
	DD	imagerel $LN3+37
	DD	imagerel $unwind$?_Throw_bad_array_new_length@std@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z DD imagerel $LN5
	DD	imagerel $LN5+159
	DD	imagerel $unwind$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Xlen_string@std@@YAXXZ DD imagerel $LN3
	DD	imagerel $LN3+22
	DD	imagerel $unwind$?_Xlen_string@std@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z DD imagerel $LN13
	DD	imagerel $LN13+162
	DD	imagerel $unwind$?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEAAPEA_WXZ DD imagerel $LN17
	DD	imagerel $LN17+111
	DD	imagerel $unwind$?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEAAPEA_WXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ DD imagerel $LN41
	DD	imagerel $LN41+107
	DD	imagerel $unwind$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DD imagerel $LN18
	DD	imagerel $LN18+98
	DD	imagerel $unwind$?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV01@AEBV01@@Z DD imagerel $LN259
	DD	imagerel $LN259+293
	DD	imagerel $unwind$??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV01@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?assign@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV12@QEB_W_K@Z DD imagerel $LN208
	DD	imagerel $LN208+177
	DD	imagerel $unwind$?assign@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV12@QEB_W_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?clear@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAXXZ DD imagerel $LN32
	DD	imagerel $LN32+27
	DD	imagerel $unwind$?clear@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ DD imagerel $LN38
	DD	imagerel $LN38+250
	DD	imagerel $unwind$?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Calculate_growth@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@CA_K_K00@Z DD imagerel $LN13
	DD	imagerel $LN13+189
	DD	imagerel $unwind$?_Calculate_growth@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@CA_K_K00@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Eos@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAX_K@Z DD imagerel $LN27
	DD	imagerel $LN27+171
	DD	imagerel $unwind$?_Eos@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAX_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DD imagerel $LN62
	DD	imagerel $LN62+266
	DD	imagerel $unwind$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Reallocate_for@V<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@Z DD imagerel $LN178
	DD	imagerel $LN178+615
	DD	imagerel $unwind$??$_Reallocate_for@V<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z DD imagerel $LN7
	DD	imagerel $LN7+150
	DD	imagerel $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0BurningStaminaUserInfo@mu2@@QEAA@XZ DD imagerel $LN46
	DD	imagerel $LN46+48
	DD	imagerel $unwind$??0BurningStaminaUserInfo@mu2@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1BurningStaminaUserInfo@mu2@@QEAA@XZ DD imagerel $LN87
	DD	imagerel $LN87+32
	DD	imagerel $unwind$??1BurningStaminaUserInfo@mu2@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?SetUserInfo@BurningStaminaUserInfo@mu2@@QEAAXIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z DD imagerel $LN236
	DD	imagerel $LN236+57
	DD	imagerel $unwind$?SetUserInfo@BurningStaminaUserInfo@mu2@@QEAAXIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?ResetUserInfo@BurningStaminaUserInfo@mu2@@QEAAXXZ DD imagerel $LN36
	DD	imagerel $LN36+43
	DD	imagerel $unwind$?ResetUserInfo@BurningStaminaUserInfo@mu2@@QEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUBurningStaminaContinentSimepleInfo@2@@Z DD imagerel $LN236
	DD	imagerel $LN236+46
	DD	imagerel $unwind$?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUBurningStaminaContinentSimepleInfo@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUBurningStaminaBurningInfo@2@@Z DD imagerel $LN236
	DD	imagerel $LN236+46
	DD	imagerel $unwind$?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUBurningStaminaBurningInfo@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUDBBurningStaminaInfo@2@@Z DD imagerel $LN236
	DD	imagerel $LN236+61
	DD	imagerel $unwind$?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUDBBurningStaminaInfo@2@@Z
pdata	ENDS
;	COMDAT ??_R4bad_alloc@std@@6B@
rdata$r	SEGMENT
??_R4bad_alloc@std@@6B@ DD 01H				; std::bad_alloc::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVbad_alloc@std@@@8
	DD	imagerel ??_R3bad_alloc@std@@8
	DD	imagerel ??_R4bad_alloc@std@@6B@
rdata$r	ENDS
;	COMDAT ??_R2bad_alloc@std@@8
rdata$r	SEGMENT
??_R2bad_alloc@std@@8 DD imagerel ??_R1A@?0A@EA@bad_alloc@std@@8 ; std::bad_alloc::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@exception@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3bad_alloc@std@@8
rdata$r	SEGMENT
??_R3bad_alloc@std@@8 DD 00H				; std::bad_alloc::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2bad_alloc@std@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@bad_alloc@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@bad_alloc@std@@8 DD imagerel ??_R0?AVbad_alloc@std@@@8 ; std::bad_alloc::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3bad_alloc@std@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@bad_array_new_length@std@@8 DD imagerel ??_R0?AVbad_array_new_length@std@@@8 ; std::bad_array_new_length::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	02H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3bad_array_new_length@std@@8
rdata$r	ENDS
;	COMDAT ??_R2bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R2bad_array_new_length@std@@8 DD imagerel ??_R1A@?0A@EA@bad_array_new_length@std@@8 ; std::bad_array_new_length::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@bad_alloc@std@@8
	DD	imagerel ??_R1A@?0A@EA@exception@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R3bad_array_new_length@std@@8 DD 00H			; std::bad_array_new_length::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	03H
	DD	imagerel ??_R2bad_array_new_length@std@@8
rdata$r	ENDS
;	COMDAT ??_R4bad_array_new_length@std@@6B@
rdata$r	SEGMENT
??_R4bad_array_new_length@std@@6B@ DD 01H		; std::bad_array_new_length::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVbad_array_new_length@std@@@8
	DD	imagerel ??_R3bad_array_new_length@std@@8
	DD	imagerel ??_R4bad_array_new_length@std@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@exception@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@exception@std@@8 DD imagerel ??_R0?AVexception@std@@@8 ; std::exception::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3exception@std@@8
rdata$r	ENDS
;	COMDAT ??_R2exception@std@@8
rdata$r	SEGMENT
??_R2exception@std@@8 DD imagerel ??_R1A@?0A@EA@exception@std@@8 ; std::exception::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3exception@std@@8
rdata$r	SEGMENT
??_R3exception@std@@8 DD 00H				; std::exception::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2exception@std@@8
rdata$r	ENDS
;	COMDAT ??_R4exception@std@@6B@
rdata$r	SEGMENT
??_R4exception@std@@6B@ DD 01H				; std::exception::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVexception@std@@@8
	DD	imagerel ??_R3exception@std@@8
	DD	imagerel ??_R4exception@std@@6B@
rdata$r	ENDS
;	COMDAT ??_C@_0BA@JFNIOLAK@string?5too?5long@
CONST	SEGMENT
??_C@_0BA@JFNIOLAK@string?5too?5long@ DB 'string too long', 00H ; `string'
CONST	ENDS
;	COMDAT _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 DD 010H
	DD	imagerel ??_R0?AVbad_alloc@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0bad_alloc@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVbad_alloc@std@@@8
data$r	SEGMENT
??_R0?AVbad_alloc@std@@@8 DQ FLAT:??_7type_info@@6B@	; std::bad_alloc `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVbad_alloc@std@@', 00H
data$r	ENDS
;	COMDAT _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 DD 00H
	DD	imagerel ??_R0?AVbad_array_new_length@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0bad_array_new_length@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVbad_array_new_length@std@@@8
data$r	SEGMENT
??_R0?AVbad_array_new_length@std@@@8 DQ FLAT:??_7type_info@@6B@ ; std::bad_array_new_length `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVbad_array_new_length@std@@', 00H
data$r	ENDS
;	COMDAT _CTA3?AVbad_array_new_length@std@@
xdata$x	SEGMENT
_CTA3?AVbad_array_new_length@std@@ DD 03H
	DD	imagerel _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
	DD	imagerel _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
	DD	imagerel _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
xdata$x	ENDS
;	COMDAT _TI3?AVbad_array_new_length@std@@
xdata$x	SEGMENT
_TI3?AVbad_array_new_length@std@@ DD 00H
	DD	imagerel ??1bad_array_new_length@std@@UEAA@XZ
	DD	00H
	DD	imagerel _CTA3?AVbad_array_new_length@std@@
xdata$x	ENDS
;	COMDAT _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 DD 00H
	DD	imagerel ??_R0?AVexception@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0exception@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVexception@std@@@8
data$r	SEGMENT
??_R0?AVexception@std@@@8 DQ FLAT:??_7type_info@@6B@	; std::exception `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVexception@std@@', 00H
data$r	ENDS
;	COMDAT ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@
CONST	SEGMENT
??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ DB 'bad array new length', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7bad_array_new_length@std@@6B@
CONST	SEGMENT
??_7bad_array_new_length@std@@6B@ DQ FLAT:??_R4bad_array_new_length@std@@6B@ ; std::bad_array_new_length::`vftable'
	DQ	FLAT:??_Ebad_array_new_length@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT ??_7bad_alloc@std@@6B@
CONST	SEGMENT
??_7bad_alloc@std@@6B@ DQ FLAT:??_R4bad_alloc@std@@6B@	; std::bad_alloc::`vftable'
	DQ	FLAT:??_Ebad_alloc@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT ??_C@_0BC@EOODALEL@Unknown?5exception@
CONST	SEGMENT
??_C@_0BC@EOODALEL@Unknown?5exception@ DB 'Unknown exception', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7exception@std@@6B@
CONST	SEGMENT
??_7exception@std@@6B@ DQ FLAT:??_R4exception@std@@6B@	; std::exception::`vftable'
	DQ	FLAT:??_Eexception@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUDBBurningStaminaInfo@2@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUBurningStaminaBurningInfo@2@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUBurningStaminaContinentSimepleInfo@2@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?ResetUserInfo@BurningStaminaUserInfo@mu2@@QEAAXXZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?SetUserInfo@BurningStaminaUserInfo@mu2@@QEAAXIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z DD 011201H
	DD	04212H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1BurningStaminaUserInfo@mu2@@QEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0BurningStaminaUserInfo@mu2@@QEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z DD 010901H
	DD	0a209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$_Reallocate_for@V<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@Z DB 06H
	DB	00H
	DB	00H
	DB	01dH, 07H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$_Reallocate_for@V<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@Z DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$_Reallocate_for@V<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@Z DB 028H
	DD	imagerel $stateUnwindMap$??$_Reallocate_for@V<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@Z
	DD	imagerel $ip2state$??$_Reallocate_for@V<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Reallocate_for@V<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@Z DD 021b11H
	DD	01b011bH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$_Reallocate_for@V<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DB 06H
	DB	00H
	DB	00H
	DB	089H, 02H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DB 068H
	DD	imagerel $stateUnwindMap$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
	DD	imagerel $ip2state$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DD 010919H
	DD	0e209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Eos@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAX_K@Z DD 010e01H
	DD	0620eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Calculate_growth@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@CA_K_K00@Z DD 011301H
	DD	06213H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ DD 020c01H
	DD	011010cH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?clear@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAXXZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?assign@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV12@QEB_W_K@Z DD 021401H
	DD	070105214H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV01@AEBV01@@Z DD 021101H
	DD	0130111H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DD 020a01H
	DD	07006120aH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ DB 02H
	DB	00H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ DB 060H
	DD	imagerel $ip2state$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ DD 020a19H
	DD	07006720aH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEAAPEA_WXZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Xlen_string@std@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Throw_bad_array_new_length@std@@YAXXZ DD 010401H
	DD	08204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1bad_array_new_length@std@@UEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_array_new_length@std@@QEAA@XZ DD 010601H
	DD	07006H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z DD 020f01H
	DD	0700b320fH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gexception@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?what@exception@std@@UEBAPEBDXZ DD 010901H
	DD	02209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0exception@std@@QEAA@AEBV01@@Z DD 020f01H
	DD	0700b320fH
xdata	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaUserInfo.cpp
;	COMDAT ?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUDBBurningStaminaInfo@2@@Z
_TEXT	SEGMENT
this$ = 48
out$ = 56
?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUDBBurningStaminaInfo@2@@Z PROC ; mu2::BurningStaminaUserInfo::FillUp, COMDAT

; 49   : {

$LN236:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 50   : 	out.awakenCharId	= m_charId;

  0000e	48 8b 44 24 38	 mov	 rax, QWORD PTR out$[rsp]
  00013	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00018	8b 09		 mov	 ecx, DWORD PTR [rcx]
  0001a	89 48 14	 mov	 DWORD PTR [rax+20], ecx

; 51   : 	out.awakenCharName	= m_charName;

  0001d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00022	48 83 c0 08	 add	 rax, 8
  00026	48 8b 4c 24 38	 mov	 rcx, QWORD PTR out$[rsp]
  0002b	48 83 c1 18	 add	 rcx, 24
  0002f	48 8b d0	 mov	 rdx, rax
  00032	e8 00 00 00 00	 call	 ??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV01@AEBV01@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::operator=
  00037	90		 npad	 1

; 52   : }

  00038	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0003c	c3		 ret	 0
?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUDBBurningStaminaInfo@2@@Z ENDP ; mu2::BurningStaminaUserInfo::FillUp
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaUserInfo.cpp
;	COMDAT ?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUBurningStaminaZoneServerInfo@2@@Z
_TEXT	SEGMENT
this$ = 8
out$ = 16
?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUBurningStaminaZoneServerInfo@2@@Z PROC ; mu2::BurningStaminaUserInfo::FillUp, COMDAT

; 44   : {

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 45   : 	out.awakenCharId = m_charId;

  0000a	48 8b 44 24 10	 mov	 rax, QWORD PTR out$[rsp]
  0000f	48 8b 4c 24 08	 mov	 rcx, QWORD PTR this$[rsp]
  00014	8b 09		 mov	 ecx, DWORD PTR [rcx]
  00016	89 48 10	 mov	 DWORD PTR [rax+16], ecx

; 46   : }

  00019	c3		 ret	 0
?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUBurningStaminaZoneServerInfo@2@@Z ENDP ; mu2::BurningStaminaUserInfo::FillUp
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaUserInfo.cpp
;	COMDAT ?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUBurningStaminaInstanceDungeonInfo@2@@Z
_TEXT	SEGMENT
this$ = 8
out$ = 16
?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUBurningStaminaInstanceDungeonInfo@2@@Z PROC ; mu2::BurningStaminaUserInfo::FillUp, COMDAT

; 39   : {

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 40   : 	out.awakenCharId = m_charId;

  0000a	48 8b 44 24 10	 mov	 rax, QWORD PTR out$[rsp]
  0000f	48 8b 4c 24 08	 mov	 rcx, QWORD PTR this$[rsp]
  00014	8b 09		 mov	 ecx, DWORD PTR [rcx]
  00016	89 48 14	 mov	 DWORD PTR [rax+20], ecx

; 41   : }

  00019	c3		 ret	 0
?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUBurningStaminaInstanceDungeonInfo@2@@Z ENDP ; mu2::BurningStaminaUserInfo::FillUp
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaUserInfo.cpp
;	COMDAT ?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUBurningStaminaBurningInfo@2@@Z
_TEXT	SEGMENT
this$ = 48
out$ = 56
?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUBurningStaminaBurningInfo@2@@Z PROC ; mu2::BurningStaminaUserInfo::FillUp, COMDAT

; 34   : {

$LN236:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 35   : 	out.name = m_charName;

  0000e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00013	48 83 c0 08	 add	 rax, 8
  00017	48 8b 4c 24 38	 mov	 rcx, QWORD PTR out$[rsp]
  0001c	48 83 c1 10	 add	 rcx, 16
  00020	48 8b d0	 mov	 rdx, rax
  00023	e8 00 00 00 00	 call	 ??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV01@AEBV01@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::operator=
  00028	90		 npad	 1

; 36   : }

  00029	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0002d	c3		 ret	 0
?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUBurningStaminaBurningInfo@2@@Z ENDP ; mu2::BurningStaminaUserInfo::FillUp
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaUserInfo.cpp
;	COMDAT ?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUBurningStaminaContinentSimepleInfo@2@@Z
_TEXT	SEGMENT
this$ = 48
out$ = 56
?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUBurningStaminaContinentSimepleInfo@2@@Z PROC ; mu2::BurningStaminaUserInfo::FillUp, COMDAT

; 29   : {

$LN236:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 30   : 	out.awakenCharName = m_charName;

  0000e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00013	48 83 c0 08	 add	 rax, 8
  00017	48 8b 4c 24 38	 mov	 rcx, QWORD PTR out$[rsp]
  0001c	48 83 c1 18	 add	 rcx, 24
  00020	48 8b d0	 mov	 rdx, rax
  00023	e8 00 00 00 00	 call	 ??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV01@AEBV01@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::operator=
  00028	90		 npad	 1

; 31   : }

  00029	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0002d	c3		 ret	 0
?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUBurningStaminaContinentSimepleInfo@2@@Z ENDP ; mu2::BurningStaminaUserInfo::FillUp
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaUserInfo.cpp
;	COMDAT ?ResetUserInfo@BurningStaminaUserInfo@mu2@@QEAAXXZ
_TEXT	SEGMENT
this$ = 48
?ResetUserInfo@BurningStaminaUserInfo@mu2@@QEAAXXZ PROC	; mu2::BurningStaminaUserInfo::ResetUserInfo, COMDAT

; 23   : {

$LN36:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 24   : 	m_charId = 0;

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	c7 00 00 00 00
	00		 mov	 DWORD PTR [rax], 0

; 25   : 	m_charName.clear();

  00014	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00019	48 83 c0 08	 add	 rax, 8
  0001d	48 8b c8	 mov	 rcx, rax
  00020	e8 00 00 00 00	 call	 ?clear@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAXXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::clear
  00025	90		 npad	 1

; 26   : }

  00026	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0002a	c3		 ret	 0
?ResetUserInfo@BurningStaminaUserInfo@mu2@@QEAAXXZ ENDP	; mu2::BurningStaminaUserInfo::ResetUserInfo
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaUserInfo.cpp
;	COMDAT ?SetUserInfo@BurningStaminaUserInfo@mu2@@QEAAXIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z
_TEXT	SEGMENT
this$ = 48
charId$ = 56
charName$ = 64
?SetUserInfo@BurningStaminaUserInfo@mu2@@QEAAXIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z PROC ; mu2::BurningStaminaUserInfo::SetUserInfo, COMDAT

; 17   : {

$LN236:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00009	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000e	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 18   : 	m_charId	= charId;

  00012	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00017	8b 4c 24 38	 mov	 ecx, DWORD PTR charId$[rsp]
  0001b	89 08		 mov	 DWORD PTR [rax], ecx

; 19   : 	m_charName	= charName;

  0001d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00022	48 83 c0 08	 add	 rax, 8
  00026	48 8b 54 24 40	 mov	 rdx, QWORD PTR charName$[rsp]
  0002b	48 8b c8	 mov	 rcx, rax
  0002e	e8 00 00 00 00	 call	 ??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV01@AEBV01@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::operator=
  00033	90		 npad	 1

; 20   : }

  00034	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00038	c3		 ret	 0
?SetUserInfo@BurningStaminaUserInfo@mu2@@QEAAXIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z ENDP ; mu2::BurningStaminaUserInfo::SetUserInfo
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaUserInfo.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaUserInfo.cpp
;	COMDAT ??1BurningStaminaUserInfo@mu2@@QEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1BurningStaminaUserInfo@mu2@@QEAA@XZ PROC		; mu2::BurningStaminaUserInfo::~BurningStaminaUserInfo, COMDAT

; 13   : {

$LN87:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 14   : }

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 c0 08	 add	 rax, 8
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1383 :         _Tidy_deallocate();

  00012	48 8b c8	 mov	 rcx, rax
  00015	e8 00 00 00 00	 call	 ?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate
  0001a	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaUserInfo.cpp

; 14   : }

  0001b	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0001f	c3		 ret	 0
??1BurningStaminaUserInfo@mu2@@QEAA@XZ ENDP		; mu2::BurningStaminaUserInfo::~BurningStaminaUserInfo
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaUserInfo.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaUserInfo.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaUserInfo.cpp
;	COMDAT ??0BurningStaminaUserInfo@mu2@@QEAA@XZ
_TEXT	SEGMENT
this$ = 48
??0BurningStaminaUserInfo@mu2@@QEAA@XZ PROC		; mu2::BurningStaminaUserInfo::BurningStaminaUserInfo, COMDAT

; 9    : {

$LN46:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaUserInfo.h

; 24   : 	CharId				m_charId	= 0;

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	c7 00 00 00 00
	00		 mov	 DWORD PTR [rax], 0
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaUserInfo.cpp

; 9    : {

  00014	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00019	48 83 c0 08	 add	 rax, 8
  0001d	48 8b c8	 mov	 rcx, rax
  00020	e8 00 00 00 00	 call	 ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  00025	90		 npad	 1

; 10   : }

  00026	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0002b	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0002f	c3		 ret	 0
??0BurningStaminaUserInfo@mu2@@QEAA@XZ ENDP		; mu2::BurningStaminaUserInfo::BurningStaminaUserInfo
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
_TEXT	SEGMENT
_Ptr_container$ = 48
_Block_size$ = 56
_Ptr$ = 64
$T1 = 72
_Bytes$ = 96
??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z PROC ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>, COMDAT

; 182  : __declspec(allocator) void* _Allocate_manually_vector_aligned(const size_t _Bytes) {

$LN7:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 183  :     // allocate _Bytes manually aligned to at least _Big_allocation_alignment
; 184  :     const size_t _Block_size = _Non_user_size + _Bytes;

  00009	48 8b 44 24 60	 mov	 rax, QWORD PTR _Bytes$[rsp]
  0000e	48 83 c0 27	 add	 rax, 39			; 00000027H
  00012	48 89 44 24 38	 mov	 QWORD PTR _Block_size$[rsp], rax

; 185  :     if (_Block_size <= _Bytes) {

  00017	48 8b 44 24 60	 mov	 rax, QWORD PTR _Bytes$[rsp]
  0001c	48 39 44 24 38	 cmp	 QWORD PTR _Block_size$[rsp], rax
  00021	77 06		 ja	 SHORT $LN2@Allocate_m

; 186  :         _Throw_bad_array_new_length(); // add overflow

  00023	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00028	90		 npad	 1
$LN2@Allocate_m:

; 136  :         return ::operator new(_Bytes);

  00029	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Block_size$[rsp]
  0002e	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00033	48 89 44 24 48	 mov	 QWORD PTR $T1[rsp], rax

; 187  :     }
; 188  : 
; 189  :     const uintptr_t _Ptr_container = reinterpret_cast<uintptr_t>(_Traits::_Allocate(_Block_size));

  00038	48 8b 44 24 48	 mov	 rax, QWORD PTR $T1[rsp]
  0003d	48 89 44 24 30	 mov	 QWORD PTR _Ptr_container$[rsp], rax

; 190  :     _STL_VERIFY(_Ptr_container != 0, "invalid argument"); // validate even in release since we're doing p[-1]

  00042	48 83 7c 24 30
	00		 cmp	 QWORD PTR _Ptr_container$[rsp], 0
  00048	75 19		 jne	 SHORT $LN3@Allocate_m
  0004a	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR [rsp+32], 0
  00053	45 33 c9	 xor	 r9d, r9d
  00056	45 33 c0	 xor	 r8d, r8d
  00059	33 d2		 xor	 edx, edx
  0005b	33 c9		 xor	 ecx, ecx
  0005d	e8 00 00 00 00	 call	 _invoke_watson
  00062	90		 npad	 1
$LN3@Allocate_m:

; 191  :     void* const _Ptr = reinterpret_cast<void*>((_Ptr_container + _Non_user_size) & ~(_Big_allocation_alignment - 1));

  00063	48 8b 44 24 30	 mov	 rax, QWORD PTR _Ptr_container$[rsp]
  00068	48 83 c0 27	 add	 rax, 39			; 00000027H
  0006c	48 83 e0 e0	 and	 rax, -32		; ffffffffffffffe0H
  00070	48 89 44 24 40	 mov	 QWORD PTR _Ptr$[rsp], rax

; 192  :     static_cast<uintptr_t*>(_Ptr)[-1] = _Ptr_container;

  00075	b8 08 00 00 00	 mov	 eax, 8
  0007a	48 6b c0 ff	 imul	 rax, rax, -1
  0007e	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  00083	48 8b 54 24 30	 mov	 rdx, QWORD PTR _Ptr_container$[rsp]
  00088	48 89 14 01	 mov	 QWORD PTR [rcx+rax], rdx

; 193  : 
; 194  : #ifdef _DEBUG
; 195  :     static_cast<uintptr_t*>(_Ptr)[-2] = _Big_allocation_sentinel;
; 196  : #endif // defined(_DEBUG)
; 197  :     return _Ptr;

  0008c	48 8b 44 24 40	 mov	 rax, QWORD PTR _Ptr$[rsp]
$LN4@Allocate_m:

; 198  : }

  00091	48 83 c4 58	 add	 rsp, 88			; 00000058H
  00095	c3		 ret	 0
??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ENDP ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??$_Reallocate_for@V<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@Z
_TEXT	SEGMENT
$T1 = 32
_New_capacity$ = 40
_Bytes$ = 48
_New_ptr$ = 56
_Fancy_ptr$2 = 64
_New_ptr$ = 72
_Old_capacity$ = 80
_Ptr$ = 88
$T3 = 96
$T4 = 104
$T5 = 112
_Al$ = 120
$T6 = 128
$T7 = 136
_Ptr$ = 144
$T8 = 152
_Old_ptr$ = 160
$T9 = 168
$T10 = 176
$T11 = 184
$T12 = 192
this$ = 224
_New_size$ = 232
_Fn$ = 240
<_Args_0>$ = 248
??$_Reallocate_for@V<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@Z PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Reallocate_for<<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>,wchar_t const *>, COMDAT

; 2995 :     _CONSTEXPR20 basic_string& _Reallocate_for(const size_type _New_size, _Fty _Fn, _ArgTys... _Args) {

$LN178:
  00000	4c 89 4c 24 20	 mov	 QWORD PTR [rsp+32], r9
  00005	44 88 44 24 18	 mov	 BYTE PTR [rsp+24], r8b
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	48 81 ec d8 00
	00 00		 sub	 rsp, 216		; 000000d8H

; 2996 :         // reallocate to store exactly _New_size elements, new buffer prepared by
; 2997 :         // _Fn(_New_ptr, _New_size, _Args...)
; 2998 :         if (_New_size > max_size()) {

  0001b	48 8b 8c 24 e0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00023	e8 00 00 00 00	 call	 ?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
  00028	48 39 84 24 e8
	00 00 00	 cmp	 QWORD PTR _New_size$[rsp], rax
  00030	76 06		 jbe	 SHORT $LN2@Reallocate

; 2999 :             _Xlen_string(); // result too long

  00032	e8 00 00 00 00	 call	 ?_Xlen_string@std@@YAXXZ ; std::_Xlen_string
  00037	90		 npad	 1
$LN2@Reallocate:

; 3000 :         }
; 3001 : 
; 3002 :         const size_type _Old_capacity = _Mypair._Myval2._Myres;

  00038	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00040	48 8b 40 18	 mov	 rax, QWORD PTR [rax+24]
  00044	48 89 44 24 50	 mov	 QWORD PTR _Old_capacity$[rsp], rax

; 2991 :         return _Calculate_growth(_Requested, _Mypair._Myval2._Myres, max_size());

  00049	48 8b 8c 24 e0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00051	e8 00 00 00 00	 call	 ?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
  00056	4c 8b c0	 mov	 r8, rax
  00059	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00061	48 8b 50 18	 mov	 rdx, QWORD PTR [rax+24]
  00065	48 8b 8c 24 e8
	00 00 00	 mov	 rcx, QWORD PTR _New_size$[rsp]
  0006d	e8 00 00 00 00	 call	 ?_Calculate_growth@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@CA_K_K00@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Calculate_growth
  00072	48 89 44 24 60	 mov	 QWORD PTR $T3[rsp], rax

; 3003 :         size_type _New_capacity       = _Calculate_growth(_New_size);

  00077	48 8b 44 24 60	 mov	 rax, QWORD PTR $T3[rsp]
  0007c	48 89 44 24 28	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 3107 :         return _Mypair._Get_first();

  00081	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00089	48 89 44 24 68	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3107 :         return _Mypair._Get_first();

  0008e	48 8b 44 24 68	 mov	 rax, QWORD PTR $T4[rsp]
  00093	48 89 44 24 70	 mov	 QWORD PTR $T5[rsp], rax

; 3004 :         auto& _Al                     = _Getal();

  00098	48 8b 44 24 70	 mov	 rax, QWORD PTR $T5[rsp]
  0009d	48 89 44 24 78	 mov	 QWORD PTR _Al$[rsp], rax

; 825  :         ++_Capacity; // Take null terminator into consideration

  000a2	48 8b 44 24 28	 mov	 rax, QWORD PTR _New_capacity$[rsp]
  000a7	48 ff c0	 inc	 rax
  000aa	48 89 44 24 28	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 826  : 
; 827  :         pointer _Fancy_ptr = nullptr;

  000af	48 c7 44 24 40
	00 00 00 00	 mov	 QWORD PTR _Fancy_ptr$2[rsp], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 2303 :         return _Al.allocate(_Count);

  000b8	48 8b 54 24 28	 mov	 rdx, QWORD PTR _New_capacity$[rsp]
  000bd	48 8b 4c 24 78	 mov	 rcx, QWORD PTR _Al$[rsp]
  000c2	e8 00 00 00 00	 call	 ?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z ; std::allocator<wchar_t>::allocate
  000c7	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 829  :             _Fancy_ptr = _Allocate_at_least_helper(_Al, _Capacity);

  000cf	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR $T6[rsp]
  000d7	48 89 44 24 40	 mov	 QWORD PTR _Fancy_ptr$2[rsp], rax

; 830  :         } else {
; 831  :             _STL_INTERNAL_STATIC_ASSERT(_Policy == _Allocation_policy::_Exactly);
; 832  :             _Fancy_ptr = _Al.allocate(_Capacity);
; 833  :         }
; 834  : 
; 835  : #if _HAS_CXX20
; 836  :         // Start element lifetimes to avoid UB. This is a more general mechanism than _String_val::_Activate_SSO_buffer,
; 837  :         // but likely more impactful to throughput.
; 838  :         if (_STD is_constant_evaluated()) {
; 839  :             _Elem* const _Ptr = _Unfancy(_Fancy_ptr);
; 840  :             for (size_type _Idx = 0; _Idx < _Capacity; ++_Idx) {
; 841  :                 _STD construct_at(_Ptr + _Idx);
; 842  :             }
; 843  :         }
; 844  : #endif // _HAS_CXX20
; 845  :         --_Capacity;

  000dc	48 8b 44 24 28	 mov	 rax, QWORD PTR _New_capacity$[rsp]
  000e1	48 ff c8	 dec	 rax
  000e4	48 89 44 24 28	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 846  :         return _Fancy_ptr;

  000e9	48 8b 44 24 40	 mov	 rax, QWORD PTR _Fancy_ptr$2[rsp]
  000ee	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T7[rsp], rax

; 3005 :         const pointer _New_ptr        = _Allocate_for_capacity(_Al, _New_capacity); // throws

  000f6	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T7[rsp]
  000fe	48 89 44 24 38	 mov	 QWORD PTR _New_ptr$[rsp], rax

; 3006 : 
; 3007 :         _Mypair._Myval2._Orphan_all();
; 3008 :         _ASAN_STRING_REMOVE(*this);
; 3009 :         _Mypair._Myval2._Mysize = _New_size;

  00103	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0010b	48 8b 8c 24 e8
	00 00 00	 mov	 rcx, QWORD PTR _New_size$[rsp]
  00113	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 3010 :         _Mypair._Myval2._Myres  = _New_capacity;

  00117	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0011f	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _New_capacity$[rsp]
  00124	48 89 48 18	 mov	 QWORD PTR [rax+24], rcx

; 3011 :         _Fn(_Unfancy(_New_ptr), _New_size, _Args...);

  00128	48 8b 44 24 38	 mov	 rax, QWORD PTR _New_ptr$[rsp]
  0012d	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  00135	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR _Ptr$[rsp]
  0013d	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3011 :         _Fn(_Unfancy(_New_ptr), _New_size, _Args...);

  00145	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR $T8[rsp]
  0014d	48 89 44 24 48	 mov	 QWORD PTR _New_ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  00152	48 8b 84 24 e8
	00 00 00	 mov	 rax, QWORD PTR _New_size$[rsp]
  0015a	48 03 c0	 add	 rax, rax
  0015d	4c 8b c0	 mov	 r8, rax
  00160	48 8b 94 24 f8
	00 00 00	 mov	 rdx, QWORD PTR <_Args_0>$[rsp]
  00168	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _New_ptr$[rsp]
  0016d	e8 00 00 00 00	 call	 memcpy
  00172	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1632 :                 _Traits::assign(_New_ptr[_Count], _Elem());

  00173	33 c0		 xor	 eax, eax
  00175	66 89 44 24 20	 mov	 WORD PTR $T1[rsp], ax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 347  :         _Left = _Right;

  0017a	48 8b 44 24 48	 mov	 rax, QWORD PTR _New_ptr$[rsp]
  0017f	48 8b 8c 24 e8
	00 00 00	 mov	 rcx, QWORD PTR _New_size$[rsp]
  00187	0f b7 54 24 20	 movzx	 edx, WORD PTR $T1[rsp]
  0018c	66 89 14 48	 mov	 WORD PTR [rax+rcx*2], dx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3012 :         if (_Old_capacity > _Small_string_capacity) {

  00190	48 83 7c 24 50
	07		 cmp	 QWORD PTR _Old_capacity$[rsp], 7
  00196	76 6c		 jbe	 SHORT $LN3@Reallocate

; 3013 :             _Deallocate_for_capacity(_Al, _Mypair._Myval2._Bx._Ptr, _Old_capacity);

  00198	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  001a0	48 8b 00	 mov	 rax, QWORD PTR [rax]
  001a3	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR _Old_ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  001ab	48 8b 44 24 50	 mov	 rax, QWORD PTR _Old_capacity$[rsp]
  001b0	48 8d 44 00 02	 lea	 rax, QWORD PTR [rax+rax+2]
  001b5	48 89 44 24 30	 mov	 QWORD PTR _Bytes$[rsp], rax
  001ba	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR _Old_ptr$[rsp]
  001c2	48 89 44 24 58	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  001c7	48 81 7c 24 30
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  001d0	72 10		 jb	 SHORT $LN148@Reallocate

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  001d2	48 8d 54 24 30	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  001d7	48 8d 4c 24 58	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  001dc	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  001e1	90		 npad	 1
$LN148@Reallocate:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  001e2	48 8b 54 24 30	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  001e7	48 8b 4c 24 58	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  001ec	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  001f1	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3014 :             _Mypair._Myval2._Bx._Ptr = _New_ptr;

  001f2	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  001fa	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _New_ptr$[rsp]
  001ff	48 89 08	 mov	 QWORD PTR [rax], rcx

; 3015 :         } else {

  00202	eb 53		 jmp	 SHORT $LN4@Reallocate
$LN3@Reallocate:

; 3016 :             _Construct_in_place(_Mypair._Myval2._Bx._Ptr, _New_ptr);

  00204	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0020c	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00214	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T9[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  0021c	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00224	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  0022c	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00234	48 8d 44 24 38	 lea	 rax, QWORD PTR _New_ptr$[rsp]
  00239	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00241	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  00249	48 8b 8c 24 c0
	00 00 00	 mov	 rcx, QWORD PTR $T12[rsp]
  00251	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00254	48 89 08	 mov	 QWORD PTR [rax], rcx
$LN4@Reallocate:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3020 :         return *this;

  00257	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]

; 3021 :     }

  0025f	48 81 c4 d8 00
	00 00		 add	 rsp, 216		; 000000d8H
  00266	c3		 ret	 0
$LN177@Reallocate:
??$_Reallocate_for@V<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@Z ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Reallocate_for<<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>,wchar_t const *>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
_TEXT	SEGMENT
$T1 = 32
$T2 = 34
_My_data$ = 40
tv94 = 48
_Bytes$ = 56
_Ptr$ = 64
$T3 = 72
$T4 = 80
_Capacity$ = 88
_Old_ptr$ = 96
_Al$5 = 104
this$ = 128
?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate, COMDAT

; 3080 :     _CONSTEXPR20 void _Tidy_deallocate() noexcept { // initialize buffer, deallocating any storage

$LN62:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 78	 sub	 rsp, 120		; 00000078H

; 3081 :         auto& _My_data = _Mypair._Myval2;

  00009	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00011	48 89 44 24 28	 mov	 QWORD PTR _My_data$[rsp], rax

; 453  :         return _Myres > _Small_string_capacity;

  00016	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  0001b	48 83 78 18 07	 cmp	 QWORD PTR [rax+24], 7
  00020	76 0a		 jbe	 SHORT $LN12@Tidy_deall
  00022	c7 44 24 30 01
	00 00 00	 mov	 DWORD PTR tv94[rsp], 1
  0002a	eb 08		 jmp	 SHORT $LN13@Tidy_deall
$LN12@Tidy_deall:
  0002c	c7 44 24 30 00
	00 00 00	 mov	 DWORD PTR tv94[rsp], 0
$LN13@Tidy_deall:
  00034	0f b6 44 24 30	 movzx	 eax, BYTE PTR tv94[rsp]
  00039	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al

; 3082 :         _My_data._Orphan_all();
; 3083 :         if (_My_data._Large_mode_engaged()) {

  0003d	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  00042	0f b6 c0	 movzx	 eax, al
  00045	85 c0		 test	 eax, eax
  00047	0f 84 80 00 00
	00		 je	 $LN2@Tidy_deall

; 3107 :         return _Mypair._Get_first();

  0004d	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00055	48 89 44 24 48	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3107 :         return _Mypair._Get_first();

  0005a	48 8b 44 24 48	 mov	 rax, QWORD PTR $T3[rsp]
  0005f	48 89 44 24 50	 mov	 QWORD PTR $T4[rsp], rax

; 3084 :             _ASAN_STRING_REMOVE(*this);
; 3085 :             auto& _Al = _Getal();

  00064	48 8b 44 24 50	 mov	 rax, QWORD PTR $T4[rsp]
  00069	48 89 44 24 68	 mov	 QWORD PTR _Al$5[rsp], rax

; 3086 :             _Deallocate_for_capacity(_Al, _My_data._Bx._Ptr, _My_data._Myres);

  0006e	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00073	48 8b 40 18	 mov	 rax, QWORD PTR [rax+24]
  00077	48 89 44 24 58	 mov	 QWORD PTR _Capacity$[rsp], rax
  0007c	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00081	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00084	48 89 44 24 60	 mov	 QWORD PTR _Old_ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  00089	48 8b 44 24 58	 mov	 rax, QWORD PTR _Capacity$[rsp]
  0008e	48 8d 44 00 02	 lea	 rax, QWORD PTR [rax+rax+2]
  00093	48 89 44 24 38	 mov	 QWORD PTR _Bytes$[rsp], rax
  00098	48 8b 44 24 60	 mov	 rax, QWORD PTR _Old_ptr$[rsp]
  0009d	48 89 44 24 40	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  000a2	48 81 7c 24 38
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  000ab	72 10		 jb	 SHORT $LN38@Tidy_deall

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  000ad	48 8d 54 24 38	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  000b2	48 8d 4c 24 40	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  000b7	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  000bc	90		 npad	 1
$LN38@Tidy_deall:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  000bd	48 8b 54 24 38	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  000c2	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  000c7	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  000cc	90		 npad	 1
$LN2@Tidy_deall:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3090 :         _My_data._Mysize = 0;

  000cd	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000d2	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 3091 :         _My_data._Myres  = _Small_string_capacity;

  000da	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000df	48 c7 40 18 07
	00 00 00	 mov	 QWORD PTR [rax+24], 7

; 3092 :         // the _Traits::assign is last so the codegen doesn't think the char write can alias this
; 3093 :         _Traits::assign(_My_data._Bx._Buf[0], _Elem());

  000e7	33 c0		 xor	 eax, eax
  000e9	66 89 44 24 22	 mov	 WORD PTR $T2[rsp], ax
  000ee	b8 02 00 00 00	 mov	 eax, 2
  000f3	48 6b c0 00	 imul	 rax, rax, 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 347  :         _Left = _Right;

  000f7	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _My_data$[rsp]
  000fc	0f b7 54 24 22	 movzx	 edx, WORD PTR $T2[rsp]
  00101	66 89 14 01	 mov	 WORD PTR [rcx+rax], dx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3094 :     }

  00105	48 83 c4 78	 add	 rsp, 120		; 00000078H
  00109	c3		 ret	 0
?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Eos@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAX_K@Z
_TEXT	SEGMENT
$T1 = 0
$T2 = 2
tv87 = 4
this$ = 8
_Result$3 = 16
_Ptr$ = 24
$T4 = 32
$T5 = 40
this$ = 64
_New_size$ = 72
?_Eos@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAX_K@Z PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Eos, COMDAT

; 3074 :     _CONSTEXPR20 void _Eos(const size_type _New_size) noexcept { // set new length and null terminator

$LN27:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 3075 :         _ASAN_STRING_MODIFY(*this, _Mypair._Myval2._Mysize, _New_size);
; 3076 :         _Mypair._Myval2._Mysize = _New_size;

  0000e	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00013	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _New_size$[rsp]
  00018	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 3077 :         _Traits::assign(_Mypair._Myval2._Myptr()[_New_size], _Elem());

  0001c	33 c0		 xor	 eax, eax
  0001e	66 89 44 24 02	 mov	 WORD PTR $T2[rsp], ax
  00023	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00028	48 89 44 24 08	 mov	 QWORD PTR this$[rsp], rax

; 435  :         value_type* _Result = _Bx._Buf;

  0002d	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00032	48 89 44 24 10	 mov	 QWORD PTR _Result$3[rsp], rax

; 453  :         return _Myres > _Small_string_capacity;

  00037	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0003c	48 83 78 18 07	 cmp	 QWORD PTR [rax+24], 7
  00041	76 0a		 jbe	 SHORT $LN12@Eos
  00043	c7 44 24 04 01
	00 00 00	 mov	 DWORD PTR tv87[rsp], 1
  0004b	eb 08		 jmp	 SHORT $LN13@Eos
$LN12@Eos:
  0004d	c7 44 24 04 00
	00 00 00	 mov	 DWORD PTR tv87[rsp], 0
$LN13@Eos:
  00055	0f b6 44 24 04	 movzx	 eax, BYTE PTR tv87[rsp]
  0005a	88 04 24	 mov	 BYTE PTR $T1[rsp], al

; 436  :         if (_Large_mode_engaged()) {

  0005d	0f b6 04 24	 movzx	 eax, BYTE PTR $T1[rsp]
  00061	0f b6 c0	 movzx	 eax, al
  00064	85 c0		 test	 eax, eax
  00066	74 21		 je	 SHORT $LN5@Eos

; 437  :             _Result = _Unfancy(_Bx._Ptr);

  00068	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0006d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00070	48 89 44 24 18	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  00075	48 8b 44 24 18	 mov	 rax, QWORD PTR _Ptr$[rsp]
  0007a	48 89 44 24 20	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 437  :             _Result = _Unfancy(_Bx._Ptr);

  0007f	48 8b 44 24 20	 mov	 rax, QWORD PTR $T4[rsp]
  00084	48 89 44 24 10	 mov	 QWORD PTR _Result$3[rsp], rax
$LN5@Eos:

; 438  :         }
; 439  : 
; 440  :         return _Result;

  00089	48 8b 44 24 10	 mov	 rax, QWORD PTR _Result$3[rsp]
  0008e	48 89 44 24 28	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 347  :         _Left = _Right;

  00093	48 8b 44 24 28	 mov	 rax, QWORD PTR $T5[rsp]
  00098	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _New_size$[rsp]
  0009d	0f b7 54 24 02	 movzx	 edx, WORD PTR $T2[rsp]
  000a2	66 89 14 48	 mov	 WORD PTR [rax+rcx*2], dx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3078 :     }

  000a6	48 83 c4 38	 add	 rsp, 56			; 00000038H
  000aa	c3		 ret	 0
?_Eos@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAX_K@Z ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Eos
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Calculate_growth@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@CA_K_K00@Z
_TEXT	SEGMENT
_Masked$ = 0
$T1 = 8
tv75 = 16
$T2 = 24
$T3 = 32
_Requested$ = 64
_Old$ = 72
_Max$ = 80
?_Calculate_growth@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@CA_K_K00@Z PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Calculate_growth, COMDAT

; 2977 :         const size_type _Requested, const size_type _Old, const size_type _Max) noexcept {

$LN13:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 2978 :         const size_type _Masked = _Requested | _Alloc_mask;

  00013	48 8b 44 24 40	 mov	 rax, QWORD PTR _Requested$[rsp]
  00018	48 83 c8 07	 or	 rax, 7
  0001c	48 89 04 24	 mov	 QWORD PTR _Masked$[rsp], rax

; 2979 :         if (_Masked > _Max) { // the mask overflows, settle for max_size()

  00020	48 8b 44 24 50	 mov	 rax, QWORD PTR _Max$[rsp]
  00025	48 39 04 24	 cmp	 QWORD PTR _Masked$[rsp], rax
  00029	76 0a		 jbe	 SHORT $LN2@Calculate_

; 2980 :             return _Max;

  0002b	48 8b 44 24 50	 mov	 rax, QWORD PTR _Max$[rsp]
  00030	e9 83 00 00 00	 jmp	 $LN1@Calculate_
$LN2@Calculate_:

; 2981 :         }
; 2982 : 
; 2983 :         if (_Old > _Max - _Old / 2) { // similarly, geometric overflows

  00035	33 d2		 xor	 edx, edx
  00037	48 8b 44 24 48	 mov	 rax, QWORD PTR _Old$[rsp]
  0003c	b9 02 00 00 00	 mov	 ecx, 2
  00041	48 f7 f1	 div	 rcx
  00044	48 8b 4c 24 50	 mov	 rcx, QWORD PTR _Max$[rsp]
  00049	48 2b c8	 sub	 rcx, rax
  0004c	48 8b c1	 mov	 rax, rcx
  0004f	48 39 44 24 48	 cmp	 QWORD PTR _Old$[rsp], rax
  00054	76 07		 jbe	 SHORT $LN3@Calculate_

; 2984 :             return _Max;

  00056	48 8b 44 24 50	 mov	 rax, QWORD PTR _Max$[rsp]
  0005b	eb 5b		 jmp	 SHORT $LN1@Calculate_
$LN3@Calculate_:

; 2987 :         return (_STD max)(_Masked, _Old + _Old / 2);

  0005d	33 d2		 xor	 edx, edx
  0005f	48 8b 44 24 48	 mov	 rax, QWORD PTR _Old$[rsp]
  00064	b9 02 00 00 00	 mov	 ecx, 2
  00069	48 f7 f1	 div	 rcx
  0006c	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _Old$[rsp]
  00071	48 03 c8	 add	 rcx, rax
  00074	48 8b c1	 mov	 rax, rcx
  00077	48 89 44 24 08	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 77   :     return _Left < _Right ? _Right : _Left;

  0007c	48 8b 44 24 08	 mov	 rax, QWORD PTR $T1[rsp]
  00081	48 39 04 24	 cmp	 QWORD PTR _Masked$[rsp], rax
  00085	73 0c		 jae	 SHORT $LN8@Calculate_
  00087	48 8d 44 24 08	 lea	 rax, QWORD PTR $T1[rsp]
  0008c	48 89 44 24 10	 mov	 QWORD PTR tv75[rsp], rax
  00091	eb 09		 jmp	 SHORT $LN9@Calculate_
$LN8@Calculate_:
  00093	48 8d 04 24	 lea	 rax, QWORD PTR _Masked$[rsp]
  00097	48 89 44 24 10	 mov	 QWORD PTR tv75[rsp], rax
$LN9@Calculate_:
  0009c	48 8b 44 24 10	 mov	 rax, QWORD PTR tv75[rsp]
  000a1	48 89 44 24 18	 mov	 QWORD PTR $T2[rsp], rax
  000a6	48 8b 44 24 18	 mov	 rax, QWORD PTR $T2[rsp]
  000ab	48 89 44 24 20	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2987 :         return (_STD max)(_Masked, _Old + _Old / 2);

  000b0	48 8b 44 24 20	 mov	 rax, QWORD PTR $T3[rsp]
  000b5	48 8b 00	 mov	 rax, QWORD PTR [rax]
$LN1@Calculate_:

; 2988 :     }

  000b8	48 83 c4 38	 add	 rsp, 56			; 00000038H
  000bc	c3		 ret	 0
?_Calculate_growth@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@CA_K_K00@Z ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Calculate_growth
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ
_TEXT	SEGMENT
$T1 = 0
_Alloc_max$ = 8
tv66 = 16
$T2 = 24
$T3 = 32
tv70 = 40
$T4 = 48
$T5 = 56
$T6 = 64
$T7 = 72
_Storage_max$ = 80
$T8 = 88
$T9 = 96
$T10 = 104
$T11 = 112
_Unsigned_max$12 = 120
this$ = 144
?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size, COMDAT

; 2377 :     _NODISCARD _CONSTEXPR20 size_type max_size() const noexcept {

$LN38:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H

; 3111 :         return _Mypair._Get_first();

  0000c	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1543 :         return *this;

  00014	48 89 44 24 30	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3111 :         return _Mypair._Get_first();

  00019	48 8b 44 24 30	 mov	 rax, QWORD PTR $T4[rsp]
  0001e	48 89 44 24 70	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 746  :         return static_cast<size_t>(-1) / sizeof(value_type);

  00023	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  0002d	48 89 44 24 38	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2378 :         const size_type _Alloc_max   = _Alty_traits::max_size(_Getal());

  00032	48 8b 44 24 38	 mov	 rax, QWORD PTR $T5[rsp]
  00037	48 89 44 24 08	 mov	 QWORD PTR _Alloc_max$[rsp], rax

; 2379 :         const size_type _Storage_max = // can always store small string

  0003c	48 c7 04 24 08
	00 00 00	 mov	 QWORD PTR $T1[rsp], 8
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 77   :     return _Left < _Right ? _Right : _Left;

  00044	48 8b 04 24	 mov	 rax, QWORD PTR $T1[rsp]
  00048	48 39 44 24 08	 cmp	 QWORD PTR _Alloc_max$[rsp], rax
  0004d	73 0b		 jae	 SHORT $LN21@max_size
  0004f	48 8d 04 24	 lea	 rax, QWORD PTR $T1[rsp]
  00053	48 89 44 24 10	 mov	 QWORD PTR tv66[rsp], rax
  00058	eb 0a		 jmp	 SHORT $LN22@max_size
$LN21@max_size:
  0005a	48 8d 44 24 08	 lea	 rax, QWORD PTR _Alloc_max$[rsp]
  0005f	48 89 44 24 10	 mov	 QWORD PTR tv66[rsp], rax
$LN22@max_size:
  00064	48 8b 44 24 10	 mov	 rax, QWORD PTR tv66[rsp]
  00069	48 89 44 24 40	 mov	 QWORD PTR $T6[rsp], rax
  0006e	48 8b 44 24 40	 mov	 rax, QWORD PTR $T6[rsp]
  00073	48 89 44 24 48	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2379 :         const size_type _Storage_max = // can always store small string

  00078	48 8b 44 24 48	 mov	 rax, QWORD PTR $T7[rsp]
  0007d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00080	48 89 44 24 50	 mov	 QWORD PTR _Storage_max$[rsp], rax

; 2381 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()),

  00085	48 8b 44 24 50	 mov	 rax, QWORD PTR _Storage_max$[rsp]
  0008a	48 ff c8	 dec	 rax
  0008d	48 89 44 24 18	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 866  :         constexpr auto _Unsigned_max = static_cast<make_unsigned_t<_Ty>>(-1);

  00092	48 c7 44 24 78
	ff ff ff ff	 mov	 QWORD PTR _Unsigned_max$12[rsp], -1

; 867  :         return static_cast<_Ty>(_Unsigned_max >> 1);

  0009b	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  000a5	48 89 44 24 58	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2381 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()),

  000aa	48 8b 44 24 58	 mov	 rax, QWORD PTR $T8[rsp]
  000af	48 89 44 24 20	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 101  :     return _Right < _Left ? _Right : _Left;

  000b4	48 8b 44 24 20	 mov	 rax, QWORD PTR $T3[rsp]
  000b9	48 39 44 24 18	 cmp	 QWORD PTR $T2[rsp], rax
  000be	73 0c		 jae	 SHORT $LN33@max_size
  000c0	48 8d 44 24 18	 lea	 rax, QWORD PTR $T2[rsp]
  000c5	48 89 44 24 28	 mov	 QWORD PTR tv70[rsp], rax
  000ca	eb 0a		 jmp	 SHORT $LN34@max_size
$LN33@max_size:
  000cc	48 8d 44 24 20	 lea	 rax, QWORD PTR $T3[rsp]
  000d1	48 89 44 24 28	 mov	 QWORD PTR tv70[rsp], rax
$LN34@max_size:
  000d6	48 8b 44 24 28	 mov	 rax, QWORD PTR tv70[rsp]
  000db	48 89 44 24 60	 mov	 QWORD PTR $T9[rsp], rax
  000e0	48 8b 44 24 60	 mov	 rax, QWORD PTR $T9[rsp]
  000e5	48 89 44 24 68	 mov	 QWORD PTR $T10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2381 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()),

  000ea	48 8b 44 24 68	 mov	 rax, QWORD PTR $T10[rsp]
  000ef	48 8b 00	 mov	 rax, QWORD PTR [rax]

; 2382 :             _Storage_max - 1 // -1 is for null terminator and/or npos
; 2383 :         );
; 2384 :     }

  000f2	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  000f9	c3		 ret	 0
?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?clear@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAXXZ
_TEXT	SEGMENT
this$ = 48
?clear@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAXXZ PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::clear, COMDAT

; 1915 :     _CONSTEXPR20 void clear() noexcept { // erase all

$LN32:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 1916 :         _Eos(0);

  00009	33 d2		 xor	 edx, edx
  0000b	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00010	e8 00 00 00 00	 call	 ?_Eos@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAX_K@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Eos
  00015	90		 npad	 1

; 1917 :     }

  00016	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0001a	c3		 ret	 0
?clear@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAXXZ ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::clear
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?assign@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV12@QEB_W_K@Z
_TEXT	SEGMENT
$T1 = 32
$T2 = 34
_Old_ptr$3 = 40
this$ = 64
_Ptr$ = 72
_Count$ = 80
?assign@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV12@QEB_W_K@Z PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::assign, COMDAT

; 1616 :         _In_reads_(_Count) const _Elem* const _Ptr, _CRT_GUARDOVERFLOW const size_type _Count) {

$LN208:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	57		 push	 rdi
  00010	48 83 ec 30	 sub	 rsp, 48			; 00000030H

; 1617 :         // assign [_Ptr, _Ptr + _Count)
; 1618 :         if (_Count <= _Mypair._Myval2._Myres) {

  00014	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00019	48 8b 40 18	 mov	 rax, QWORD PTR [rax+24]
  0001d	48 39 44 24 50	 cmp	 QWORD PTR _Count$[rsp], rax
  00022	77 5c		 ja	 SHORT $LN2@assign

; 1619 :             _ASAN_STRING_REMOVE(*this);
; 1620 :             _Elem* const _Old_ptr   = _Mypair._Myval2._Myptr();

  00024	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00029	48 8b c8	 mov	 rcx, rax
  0002c	e8 00 00 00 00	 call	 ?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEAAPEA_WXZ ; std::_String_val<std::_Simple_types<wchar_t> >::_Myptr
  00031	48 89 44 24 28	 mov	 QWORD PTR _Old_ptr$3[rsp], rax

; 1621 :             _Mypair._Myval2._Mysize = _Count;

  00036	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0003b	48 8b 4c 24 50	 mov	 rcx, QWORD PTR _Count$[rsp]
  00040	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 174  :         _CSTD memmove(_First1, _First2, _Count * sizeof(_Elem));

  00044	48 8b 44 24 50	 mov	 rax, QWORD PTR _Count$[rsp]
  00049	48 03 c0	 add	 rax, rax
  0004c	4c 8b c0	 mov	 r8, rax
  0004f	48 8b 54 24 48	 mov	 rdx, QWORD PTR _Ptr$[rsp]
  00054	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Old_ptr$3[rsp]
  00059	e8 00 00 00 00	 call	 memmove
  0005e	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1623 :             _Traits::assign(_Old_ptr[_Count], _Elem());

  0005f	33 c0		 xor	 eax, eax
  00061	66 89 44 24 22	 mov	 WORD PTR $T2[rsp], ax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 347  :         _Left = _Right;

  00066	48 8b 44 24 28	 mov	 rax, QWORD PTR _Old_ptr$3[rsp]
  0006b	48 8b 4c 24 50	 mov	 rcx, QWORD PTR _Count$[rsp]
  00070	0f b7 54 24 22	 movzx	 edx, WORD PTR $T2[rsp]
  00075	66 89 14 48	 mov	 WORD PTR [rax+rcx*2], dx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1625 :             return *this;

  00079	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0007e	eb 2b		 jmp	 SHORT $LN1@assign
$LN2@assign:

; 1626 :         }
; 1627 : 
; 1628 :         return _Reallocate_for(

  00080	48 8d 44 24 20	 lea	 rax, QWORD PTR $T1[rsp]
  00085	48 8b f8	 mov	 rdi, rax
  00088	33 c0		 xor	 eax, eax
  0008a	b9 01 00 00 00	 mov	 ecx, 1
  0008f	f3 aa		 rep stosb
  00091	4c 8b 4c 24 48	 mov	 r9, QWORD PTR _Ptr$[rsp]
  00096	44 0f b6 44 24
	20		 movzx	 r8d, BYTE PTR $T1[rsp]
  0009c	48 8b 54 24 50	 mov	 rdx, QWORD PTR _Count$[rsp]
  000a1	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  000a6	e8 00 00 00 00	 call	 ??$_Reallocate_for@V<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Reallocate_for<<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>,wchar_t const *>
$LN1@assign:

; 1629 :             _Count,
; 1630 :             [](_Elem* const _New_ptr, const size_type _Count, const _Elem* const _Ptr) _STATIC_LAMBDA {
; 1631 :                 _Traits::copy(_New_ptr, _Ptr, _Count);
; 1632 :                 _Traits::assign(_New_ptr[_Count], _Elem());
; 1633 :             },
; 1634 :             _Ptr);
; 1635 :     }

  000ab	48 83 c4 30	 add	 rsp, 48			; 00000030H
  000af	5f		 pop	 rdi
  000b0	c3		 ret	 0
?assign@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV12@QEB_W_K@Z ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::assign
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV01@AEBV01@@Z
_TEXT	SEGMENT
$T1 = 32
tv134 = 36
this$ = 40
_Result$2 = 48
$T3 = 56
$T4 = 64
$T5 = 72
$T6 = 80
$T7 = 88
_Ptr$ = 96
$T8 = 104
$T9 = 112
_Al$ = 120
_Right_al$ = 128
this$ = 160
_Right$ = 168
??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV01@AEBV01@@Z PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::operator=, COMDAT

; 1394 :     _CONSTEXPR20 basic_string& operator=(const basic_string& _Right) {

$LN259:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 81 ec 98 00
	00 00		 sub	 rsp, 152		; 00000098H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00011	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR _Right$[rsp]
  00019	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1395 :         if (this == _STD addressof(_Right)) {

  0001e	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
  00023	48 39 84 24 a0
	00 00 00	 cmp	 QWORD PTR this$[rsp], rax
  0002b	75 0d		 jne	 SHORT $LN2@operator

; 1396 :             return *this;

  0002d	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00035	e9 e3 00 00 00	 jmp	 $LN1@operator
$LN2@operator:

; 3107 :         return _Mypair._Get_first();

  0003a	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00042	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3107 :         return _Mypair._Get_first();

  00047	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  0004c	48 89 44 24 48	 mov	 QWORD PTR $T5[rsp], rax

; 1397 :         }
; 1398 : 
; 1399 :         auto& _Al             = _Getal();

  00051	48 8b 44 24 48	 mov	 rax, QWORD PTR $T5[rsp]
  00056	48 89 44 24 78	 mov	 QWORD PTR _Al$[rsp], rax

; 3111 :         return _Mypair._Get_first();

  0005b	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR _Right$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1543 :         return *this;

  00063	48 89 44 24 50	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3111 :         return _Mypair._Get_first();

  00068	48 8b 44 24 50	 mov	 rax, QWORD PTR $T6[rsp]
  0006d	48 89 44 24 58	 mov	 QWORD PTR $T7[rsp], rax

; 1400 :         const auto& _Right_al = _Right._Getal();

  00072	48 8b 44 24 58	 mov	 rax, QWORD PTR $T7[rsp]
  00077	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _Right_al$[rsp], rax

; 1401 :         if constexpr (_Choose_pocca_v<_Alty>) {
; 1402 :             if (_Al != _Right_al) {
; 1403 :                 auto&& _Alproxy       = _GET_PROXY_ALLOCATOR(_Alty, _Al);
; 1404 :                 auto&& _Right_alproxy = _GET_PROXY_ALLOCATOR(_Alty, _Right_al);
; 1405 :                 _Container_proxy_ptr<_Alty> _New_proxy(_Right_alproxy, _Leave_proxy_unbound{}); // throws
; 1406 : 
; 1407 :                 const size_type _Right_size   = _Right._Mypair._Myval2._Mysize;
; 1408 :                 const _Elem* const _Right_ptr = _Right._Mypair._Myval2._Myptr();
; 1409 :                 if (_Right_size > _Small_string_capacity) {
; 1410 :                     size_type _New_capacity = _Calculate_growth(_Right_size, _Small_string_capacity, _Right.max_size());
; 1411 :                     auto _Right_al_non_const = _Right_al;
; 1412 :                     const pointer _New_ptr   = _Allocate_for_capacity(_Right_al_non_const, _New_capacity); // throws
; 1413 :                     _Traits::copy(_Unfancy(_New_ptr), _Right_ptr, _Right_size + 1);
; 1414 : 
; 1415 :                     _Tidy_deallocate();
; 1416 :                     _Construct_in_place(_Mypair._Myval2._Bx._Ptr, _New_ptr);
; 1417 :                     _Mypair._Myval2._Mysize = _Right_size;
; 1418 :                     _Mypair._Myval2._Myres  = _New_capacity;
; 1419 :                     _ASAN_STRING_CREATE(*this);
; 1420 :                 } else {
; 1421 :                     _Tidy_deallocate();
; 1422 :                     _Traits::copy(_Mypair._Myval2._Bx._Buf, _Right_ptr, _Right_size + 1);
; 1423 :                     _Mypair._Myval2._Mysize = _Right_size;
; 1424 :                     _Mypair._Myval2._Myres  = _Small_string_capacity;
; 1425 :                 }
; 1426 : 
; 1427 :                 _Pocca(_Al, _Right_al);
; 1428 :                 _New_proxy._Bind(_Alproxy, _STD addressof(_Mypair._Myval2));
; 1429 :                 return *this;
; 1430 :             }
; 1431 :         }
; 1432 : 
; 1433 :         _Pocca(_Al, _Right_al);
; 1434 :         assign(_Right._Mypair._Myval2._Myptr(), _Right._Mypair._Myval2._Mysize);

  0007f	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR _Right$[rsp]
  00087	48 89 44 24 28	 mov	 QWORD PTR this$[rsp], rax

; 444  :         const value_type* _Result = _Bx._Buf;

  0008c	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00091	48 89 44 24 30	 mov	 QWORD PTR _Result$2[rsp], rax

; 453  :         return _Myres > _Small_string_capacity;

  00096	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  0009b	48 83 78 18 07	 cmp	 QWORD PTR [rax+24], 7
  000a0	76 0a		 jbe	 SHORT $LN42@operator
  000a2	c7 44 24 24 01
	00 00 00	 mov	 DWORD PTR tv134[rsp], 1
  000aa	eb 08		 jmp	 SHORT $LN43@operator
$LN42@operator:
  000ac	c7 44 24 24 00
	00 00 00	 mov	 DWORD PTR tv134[rsp], 0
$LN43@operator:
  000b4	0f b6 44 24 24	 movzx	 eax, BYTE PTR tv134[rsp]
  000b9	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al

; 445  :         if (_Large_mode_engaged()) {

  000bd	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  000c2	0f b6 c0	 movzx	 eax, al
  000c5	85 c0		 test	 eax, eax
  000c7	74 21		 je	 SHORT $LN35@operator

; 446  :             _Result = _Unfancy(_Bx._Ptr);

  000c9	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  000ce	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000d1	48 89 44 24 60	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  000d6	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  000db	48 89 44 24 68	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 446  :             _Result = _Unfancy(_Bx._Ptr);

  000e0	48 8b 44 24 68	 mov	 rax, QWORD PTR $T8[rsp]
  000e5	48 89 44 24 30	 mov	 QWORD PTR _Result$2[rsp], rax
$LN35@operator:

; 447  :         }
; 448  : 
; 449  :         return _Result;

  000ea	48 8b 44 24 30	 mov	 rax, QWORD PTR _Result$2[rsp]
  000ef	48 89 44 24 70	 mov	 QWORD PTR $T9[rsp], rax

; 1401 :         if constexpr (_Choose_pocca_v<_Alty>) {
; 1402 :             if (_Al != _Right_al) {
; 1403 :                 auto&& _Alproxy       = _GET_PROXY_ALLOCATOR(_Alty, _Al);
; 1404 :                 auto&& _Right_alproxy = _GET_PROXY_ALLOCATOR(_Alty, _Right_al);
; 1405 :                 _Container_proxy_ptr<_Alty> _New_proxy(_Right_alproxy, _Leave_proxy_unbound{}); // throws
; 1406 : 
; 1407 :                 const size_type _Right_size   = _Right._Mypair._Myval2._Mysize;
; 1408 :                 const _Elem* const _Right_ptr = _Right._Mypair._Myval2._Myptr();
; 1409 :                 if (_Right_size > _Small_string_capacity) {
; 1410 :                     size_type _New_capacity = _Calculate_growth(_Right_size, _Small_string_capacity, _Right.max_size());
; 1411 :                     auto _Right_al_non_const = _Right_al;
; 1412 :                     const pointer _New_ptr   = _Allocate_for_capacity(_Right_al_non_const, _New_capacity); // throws
; 1413 :                     _Traits::copy(_Unfancy(_New_ptr), _Right_ptr, _Right_size + 1);
; 1414 : 
; 1415 :                     _Tidy_deallocate();
; 1416 :                     _Construct_in_place(_Mypair._Myval2._Bx._Ptr, _New_ptr);
; 1417 :                     _Mypair._Myval2._Mysize = _Right_size;
; 1418 :                     _Mypair._Myval2._Myres  = _New_capacity;
; 1419 :                     _ASAN_STRING_CREATE(*this);
; 1420 :                 } else {
; 1421 :                     _Tidy_deallocate();
; 1422 :                     _Traits::copy(_Mypair._Myval2._Bx._Buf, _Right_ptr, _Right_size + 1);
; 1423 :                     _Mypair._Myval2._Mysize = _Right_size;
; 1424 :                     _Mypair._Myval2._Myres  = _Small_string_capacity;
; 1425 :                 }
; 1426 : 
; 1427 :                 _Pocca(_Al, _Right_al);
; 1428 :                 _New_proxy._Bind(_Alproxy, _STD addressof(_Mypair._Myval2));
; 1429 :                 return *this;
; 1430 :             }
; 1431 :         }
; 1432 : 
; 1433 :         _Pocca(_Al, _Right_al);
; 1434 :         assign(_Right._Mypair._Myval2._Myptr(), _Right._Mypair._Myval2._Mysize);

  000f4	48 8b 44 24 70	 mov	 rax, QWORD PTR $T9[rsp]
  000f9	48 8b 8c 24 a8
	00 00 00	 mov	 rcx, QWORD PTR _Right$[rsp]
  00101	4c 8b 41 10	 mov	 r8, QWORD PTR [rcx+16]
  00105	48 8b d0	 mov	 rdx, rax
  00108	48 8b 8c 24 a0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00110	e8 00 00 00 00	 call	 ?assign@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV12@QEB_W_K@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::assign

; 1435 :         return *this;

  00115	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
$LN1@operator:

; 1436 :     }

  0011d	48 81 c4 98 00
	00 00		 add	 rsp, 152		; 00000098H
  00124	c3		 ret	 0
??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV01@AEBV01@@Z ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::operator=
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
_TEXT	SEGMENT
$T1 = 0
$T2 = 2
_My_data$ = 8
this$ = 32
?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_empty, COMDAT

; 855  :     _CONSTEXPR20 void _Construct_empty() {

$LN18:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi
  00006	48 83 ec 10	 sub	 rsp, 16

; 856  :         auto& _My_data = _Mypair._Myval2;

  0000a	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0000f	48 89 44 24 08	 mov	 QWORD PTR _My_data$[rsp], rax

; 857  :         _My_data._Alloc_proxy(_GET_PROXY_ALLOCATOR(_Alty, _Getal()));

  00014	48 8d 04 24	 lea	 rax, QWORD PTR $T1[rsp]
  00018	48 8b f8	 mov	 rdi, rax
  0001b	33 c0		 xor	 eax, eax
  0001d	b9 01 00 00 00	 mov	 ecx, 1
  00022	f3 aa		 rep stosb

; 858  : 
; 859  :         // initialize basic_string data members
; 860  :         _My_data._Mysize = 0;

  00024	48 8b 44 24 08	 mov	 rax, QWORD PTR _My_data$[rsp]
  00029	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 861  :         _My_data._Myres  = _Small_string_capacity;

  00031	48 8b 44 24 08	 mov	 rax, QWORD PTR _My_data$[rsp]
  00036	48 c7 40 18 07
	00 00 00	 mov	 QWORD PTR [rax+24], 7

; 862  :         _My_data._Activate_SSO_buffer();
; 863  : 
; 864  :         // the _Traits::assign is last so the codegen doesn't think the char write can alias this
; 865  :         _Traits::assign(_My_data._Bx._Buf[0], _Elem());

  0003e	33 c0		 xor	 eax, eax
  00040	66 89 44 24 02	 mov	 WORD PTR $T2[rsp], ax
  00045	b8 02 00 00 00	 mov	 eax, 2
  0004a	48 6b c0 00	 imul	 rax, rax, 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 347  :         _Left = _Right;

  0004e	48 8b 4c 24 08	 mov	 rcx, QWORD PTR _My_data$[rsp]
  00053	0f b7 54 24 02	 movzx	 edx, WORD PTR $T2[rsp]
  00058	66 89 14 01	 mov	 WORD PTR [rcx+rax], dx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 866  :     }

  0005c	48 83 c4 10	 add	 rsp, 16
  00060	5f		 pop	 rdi
  00061	c3		 ret	 0
?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_empty
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 32
this$ = 40
this$ = 48
this$ = 80
??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >, COMDAT

; 708  :     basic_string() noexcept(is_nothrow_default_constructible_v<_Alty>) : _Mypair(_Zero_then_variadic_args_t{}) {

$LN41:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi
  00006	48 83 ec 40	 sub	 rsp, 64			; 00000040H
  0000a	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0000f	48 89 44 24 30	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1531 :         : _Ty1(), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  00014	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00019	48 89 44 24 20	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 402  :     _CONSTEXPR20 _String_val() noexcept : _Bx() {}

  0001e	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 89 44 24 28	 mov	 QWORD PTR this$[rsp], rax

; 493  :         _CONSTEXPR20 _Bxty() noexcept : _Buf() {} // user-provided, for fancy pointers

  00028	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  0002d	48 8b 7c 24 28	 mov	 rdi, QWORD PTR this$[rsp]
  00032	33 c0		 xor	 eax, eax
  00034	b9 10 00 00 00	 mov	 ecx, 16
  00039	f3 aa		 rep stosb

; 517  :     size_type _Mysize = 0; // current length of string (size)

  0003b	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00040	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 518  :     size_type _Myres  = 0; // current storage reserved for string (capacity)

  00048	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0004d	48 c7 40 18 00
	00 00 00	 mov	 QWORD PTR [rax+24], 0

; 709  :         _Construct_empty();

  00055	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  0005a	e8 00 00 00 00	 call	 ?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_empty
  0005f	90		 npad	 1

; 710  :     }

  00060	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00065	48 83 c4 40	 add	 rsp, 64			; 00000040H
  00069	5f		 pop	 rdi
  0006a	c3		 ret	 0
??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEAAPEA_WXZ
_TEXT	SEGMENT
$T1 = 0
tv76 = 4
_Result$ = 8
_Ptr$ = 16
$T2 = 24
this$ = 48
?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEAAPEA_WXZ PROC ; std::_String_val<std::_Simple_types<wchar_t> >::_Myptr, COMDAT

; 434  :     _NODISCARD _CONSTEXPR20 value_type* _Myptr() noexcept {

$LN17:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 435  :         value_type* _Result = _Bx._Buf;

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 89 44 24 08	 mov	 QWORD PTR _Result$[rsp], rax

; 453  :         return _Myres > _Small_string_capacity;

  00013	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00018	48 83 78 18 07	 cmp	 QWORD PTR [rax+24], 7
  0001d	76 0a		 jbe	 SHORT $LN7@Myptr
  0001f	c7 44 24 04 01
	00 00 00	 mov	 DWORD PTR tv76[rsp], 1
  00027	eb 08		 jmp	 SHORT $LN8@Myptr
$LN7@Myptr:
  00029	c7 44 24 04 00
	00 00 00	 mov	 DWORD PTR tv76[rsp], 0
$LN8@Myptr:
  00031	0f b6 44 24 04	 movzx	 eax, BYTE PTR tv76[rsp]
  00036	88 04 24	 mov	 BYTE PTR $T1[rsp], al

; 436  :         if (_Large_mode_engaged()) {

  00039	0f b6 04 24	 movzx	 eax, BYTE PTR $T1[rsp]
  0003d	0f b6 c0	 movzx	 eax, al
  00040	85 c0		 test	 eax, eax
  00042	74 21		 je	 SHORT $LN2@Myptr

; 437  :             _Result = _Unfancy(_Bx._Ptr);

  00044	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00049	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0004c	48 89 44 24 10	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  00051	48 8b 44 24 10	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00056	48 89 44 24 18	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 437  :             _Result = _Unfancy(_Bx._Ptr);

  0005b	48 8b 44 24 18	 mov	 rax, QWORD PTR $T2[rsp]
  00060	48 89 44 24 08	 mov	 QWORD PTR _Result$[rsp], rax
$LN2@Myptr:

; 438  :         }
; 439  : 
; 440  :         return _Result;

  00065	48 8b 44 24 08	 mov	 rax, QWORD PTR _Result$[rsp]

; 441  :     }

  0006a	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0006e	c3		 ret	 0
?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEAAPEA_WXZ ENDP ; std::_String_val<std::_Simple_types<wchar_t> >::_Myptr
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z
_TEXT	SEGMENT
_Overflow_is_possible$1 = 32
_Bytes$ = 40
$T2 = 48
$T3 = 56
$T4 = 64
_Max_possible$5 = 72
this$ = 96
_Count$ = 104
?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z PROC	; std::allocator<wchar_t>::allocate, COMDAT

; 988  :     _NODISCARD_RAW_PTR_ALLOC _CONSTEXPR20 __declspec(allocator) _Ty* allocate(_CRT_GUARDOVERFLOW const size_t _Count) {

$LN13:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 113  :     constexpr bool _Overflow_is_possible = _Ty_size > 1;

  0000e	c6 44 24 20 01	 mov	 BYTE PTR _Overflow_is_possible$1[rsp], 1

; 114  : 
; 115  :     if constexpr (_Overflow_is_possible) {
; 116  :         constexpr size_t _Max_possible = static_cast<size_t>(-1) / _Ty_size;

  00013	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  0001d	48 89 44 24 48	 mov	 QWORD PTR _Max_possible$5[rsp], rax

; 117  :         if (_Count > _Max_possible) {

  00022	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  0002c	48 39 44 24 68	 cmp	 QWORD PTR _Count$[rsp], rax
  00031	76 06		 jbe	 SHORT $LN4@allocate

; 118  :             _Throw_bad_array_new_length(); // multiply overflow

  00033	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00038	90		 npad	 1
$LN4@allocate:

; 119  :         }
; 120  :     }
; 121  : 
; 122  :     return _Count * _Ty_size;

  00039	48 8b 44 24 68	 mov	 rax, QWORD PTR _Count$[rsp]
  0003e	48 d1 e0	 shl	 rax, 1
  00041	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00046	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
  0004b	48 89 44 24 28	 mov	 QWORD PTR _Bytes$[rsp], rax

; 227  :     if (_Bytes == 0) {

  00050	48 83 7c 24 28
	00		 cmp	 QWORD PTR _Bytes$[rsp], 0
  00056	75 0b		 jne	 SHORT $LN8@allocate

; 228  :         return nullptr;

  00058	48 c7 44 24 30
	00 00 00 00	 mov	 QWORD PTR $T2[rsp], 0
  00061	eb 35		 jmp	 SHORT $LN7@allocate
$LN8@allocate:

; 229  :     }
; 230  : 
; 231  : #if _HAS_CXX20 // TRANSITION, GH-1532
; 232  :     if (_STD is_constant_evaluated()) {
; 233  :         return _Traits::_Allocate(_Bytes);
; 234  :     }
; 235  : #endif // _HAS_CXX20
; 236  : 
; 237  : #ifdef __cpp_aligned_new
; 238  :     if constexpr (_Align > __STDCPP_DEFAULT_NEW_ALIGNMENT__) {
; 239  :         size_t _Passed_align = _Align;
; 240  : #if defined(_M_IX86) || defined(_M_X64)
; 241  :         if (_Bytes >= _Big_allocation_threshold) {
; 242  :             // boost the alignment of big allocations to help autovectorization
; 243  :             _Passed_align = (_STD max)(_Align, _Big_allocation_alignment);
; 244  :         }
; 245  : #endif // defined(_M_IX86) || defined(_M_X64)
; 246  :         return _Traits::_Allocate_aligned(_Bytes, _Passed_align);
; 247  :     } else
; 248  : #endif // defined(__cpp_aligned_new)
; 249  :     {
; 250  : #if defined(_M_IX86) || defined(_M_X64)
; 251  :         if (_Bytes >= _Big_allocation_threshold) {

  00063	48 81 7c 24 28
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  0006c	72 11		 jb	 SHORT $LN9@allocate

; 252  :             // boost the alignment of big allocations to help autovectorization
; 253  :             return _Allocate_manually_vector_aligned<_Traits>(_Bytes);

  0006e	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00073	e8 00 00 00 00	 call	 ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
  00078	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
  0007d	eb 19		 jmp	 SHORT $LN7@allocate
$LN9@allocate:

; 136  :         return ::operator new(_Bytes);

  0007f	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00084	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00089	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax

; 256  :         return _Traits::_Allocate(_Bytes);

  0008e	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  00093	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
$LN7@allocate:

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00098	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
$LN6@allocate:

; 991  :     }

  0009d	48 83 c4 58	 add	 rsp, 88			; 00000058H
  000a1	c3		 ret	 0
?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z ENDP	; std::allocator<wchar_t>::allocate
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Xlen_string@std@@YAXXZ
_TEXT	SEGMENT
?_Xlen_string@std@@YAXXZ PROC				; std::_Xlen_string, COMDAT

; 530  : [[noreturn]] inline void _Xlen_string() {

$LN3:
  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 531  :     _Xlength_error("string too long");

  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_0BA@JFNIOLAK@string?5too?5long@
  0000b	e8 00 00 00 00	 call	 ?_Xlength_error@std@@YAXPEBD@Z ; std::_Xlength_error
  00010	90		 npad	 1
$LN2@Xlen_strin:

; 532  : }

  00011	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00015	c3		 ret	 0
?_Xlen_string@std@@YAXXZ ENDP				; std::_Xlen_string
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z
_TEXT	SEGMENT
_Back_shift$ = 48
_Ptr_container$ = 56
_Ptr_user$ = 64
_Min_back_shift$ = 72
_Ptr$ = 96
_Bytes$ = 104
?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z PROC ; std::_Adjust_manually_vector_aligned, COMDAT

; 200  : inline void _Adjust_manually_vector_aligned(void*& _Ptr, size_t& _Bytes) {

$LN5:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 201  :     // adjust parameters from _Allocate_manually_vector_aligned to pass to operator delete
; 202  :     _Bytes += _Non_user_size;

  0000e	48 8b 44 24 68	 mov	 rax, QWORD PTR _Bytes$[rsp]
  00013	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00016	48 83 c0 27	 add	 rax, 39			; 00000027H
  0001a	48 8b 4c 24 68	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  0001f	48 89 01	 mov	 QWORD PTR [rcx], rax

; 203  : 
; 204  :     const uintptr_t* const _Ptr_user = static_cast<uintptr_t*>(_Ptr);

  00022	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00027	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0002a	48 89 44 24 40	 mov	 QWORD PTR _Ptr_user$[rsp], rax

; 205  :     const uintptr_t _Ptr_container   = _Ptr_user[-1];

  0002f	b8 08 00 00 00	 mov	 eax, 8
  00034	48 6b c0 ff	 imul	 rax, rax, -1
  00038	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr_user$[rsp]
  0003d	48 8b 04 01	 mov	 rax, QWORD PTR [rcx+rax]
  00041	48 89 44 24 38	 mov	 QWORD PTR _Ptr_container$[rsp], rax

; 206  : 
; 207  :     // If the following asserts, it likely means that we are performing
; 208  :     // an aligned delete on memory coming from an unaligned allocation.
; 209  :     _STL_ASSERT(_Ptr_user[-2] == _Big_allocation_sentinel, "invalid argument");
; 210  : 
; 211  :     // Extra paranoia on aligned allocation/deallocation; ensure _Ptr_container is
; 212  :     // in range [_Min_back_shift, _Non_user_size]
; 213  : #ifdef _DEBUG
; 214  :     constexpr uintptr_t _Min_back_shift = 2 * sizeof(void*);
; 215  : #else // ^^^ defined(_DEBUG) / !defined(_DEBUG) vvv
; 216  :     constexpr uintptr_t _Min_back_shift = sizeof(void*);

  00046	48 c7 44 24 48
	08 00 00 00	 mov	 QWORD PTR _Min_back_shift$[rsp], 8

; 217  : #endif // ^^^ !defined(_DEBUG) ^^^
; 218  :     const uintptr_t _Back_shift = reinterpret_cast<uintptr_t>(_Ptr) - _Ptr_container;

  0004f	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00054	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr_container$[rsp]
  00059	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0005c	48 2b c1	 sub	 rax, rcx
  0005f	48 89 44 24 30	 mov	 QWORD PTR _Back_shift$[rsp], rax

; 219  :     _STL_VERIFY(_Back_shift >= _Min_back_shift && _Back_shift <= _Non_user_size, "invalid argument");

  00064	48 83 7c 24 30
	08		 cmp	 QWORD PTR _Back_shift$[rsp], 8
  0006a	72 08		 jb	 SHORT $LN3@Adjust_man
  0006c	48 83 7c 24 30
	27		 cmp	 QWORD PTR _Back_shift$[rsp], 39 ; 00000027H
  00072	76 19		 jbe	 SHORT $LN2@Adjust_man
$LN3@Adjust_man:
  00074	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR [rsp+32], 0
  0007d	45 33 c9	 xor	 r9d, r9d
  00080	45 33 c0	 xor	 r8d, r8d
  00083	33 d2		 xor	 edx, edx
  00085	33 c9		 xor	 ecx, ecx
  00087	e8 00 00 00 00	 call	 _invoke_watson
  0008c	90		 npad	 1
$LN2@Adjust_man:

; 220  :     _Ptr = reinterpret_cast<void*>(_Ptr_container);

  0008d	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00092	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr_container$[rsp]
  00097	48 89 08	 mov	 QWORD PTR [rax], rcx
$LN4@Adjust_man:

; 221  : }

  0009a	48 83 c4 58	 add	 rsp, 88			; 00000058H
  0009e	c3		 ret	 0
?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ENDP ; std::_Adjust_manually_vector_aligned
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?_Throw_bad_array_new_length@std@@YAXXZ
_TEXT	SEGMENT
$T1 = 32
?_Throw_bad_array_new_length@std@@YAXXZ PROC		; std::_Throw_bad_array_new_length, COMDAT

; 107  : [[noreturn]] inline void _Throw_bad_array_new_length() {

$LN3:
  00000	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 108  :     _THROW(bad_array_new_length{});

  00004	48 8d 4c 24 20	 lea	 rcx, QWORD PTR $T1[rsp]
  00009	e8 00 00 00 00	 call	 ??0bad_array_new_length@std@@QEAA@XZ ; std::bad_array_new_length::bad_array_new_length
  0000e	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:_TI3?AVbad_array_new_length@std@@
  00015	48 8d 4c 24 20	 lea	 rcx, QWORD PTR $T1[rsp]
  0001a	e8 00 00 00 00	 call	 _CxxThrowException
  0001f	90		 npad	 1
$LN2@Throw_bad_:

; 109  : }

  00020	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00024	c3		 ret	 0
?_Throw_bad_array_new_length@std@@YAXXZ ENDP		; std::_Throw_bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gbad_array_new_length@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gbad_array_new_length@std@@UEAAPEAXI@Z PROC		; std::bad_array_new_length::`scalar deleting destructor', COMDAT
$LN20:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gbad_array_new_length@std@@UEAAPEAXI@Z ENDP		; std::bad_array_new_length::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??0bad_array_new_length@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
__that$ = 56
??0bad_array_new_length@std@@QEAA@AEBV01@@Z PROC	; std::bad_array_new_length::bad_array_new_length, COMDAT
$LN14:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000e	48 8b 54 24 38	 mov	 rdx, QWORD PTR __that$[rsp]
  00013	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00018	e8 00 00 00 00	 call	 ??0bad_alloc@std@@QEAA@AEBV01@@Z
  0001d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00022	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_array_new_length@std@@6B@
  00029	48 89 08	 mov	 QWORD PTR [rax], rcx
  0002c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00031	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00035	c3		 ret	 0
??0bad_array_new_length@std@@QEAA@AEBV01@@Z ENDP	; std::bad_array_new_length::bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??1bad_array_new_length@std@@UEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1bad_array_new_length@std@@UEAA@XZ PROC		; std::bad_array_new_length::~bad_array_new_length, COMDAT
$LN14:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  00018	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 83 c0 08	 add	 rax, 8
  00021	48 8b c8	 mov	 rcx, rax
  00024	e8 00 00 00 00	 call	 __std_exception_destroy
  00029	90		 npad	 1
  0002a	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0002e	c3		 ret	 0
??1bad_array_new_length@std@@UEAA@XZ ENDP		; std::bad_array_new_length::~bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0bad_array_new_length@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 16
??0bad_array_new_length@std@@QEAA@XZ PROC		; std::bad_array_new_length::bad_array_new_length, COMDAT

; 144  :     {

$LN14:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi

; 67   :     {

  00006	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0000b	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00012	48 89 08	 mov	 QWORD PTR [rax], rcx

; 66   :         : _Data()

  00015	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 83 c0 08	 add	 rax, 8
  0001e	48 8b f8	 mov	 rdi, rax
  00021	33 c0		 xor	 eax, eax
  00023	b9 10 00 00 00	 mov	 ecx, 16
  00028	f3 aa		 rep stosb

; 68   :         _Data._What = _Message;

  0002a	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0002f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_0BF@KINCDENJ@bad?5array?5new?5length@
  00036	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 133  :     {

  0003a	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0003f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_alloc@std@@6B@
  00046	48 89 08	 mov	 QWORD PTR [rax], rcx

; 144  :     {

  00049	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_array_new_length@std@@6B@
  00055	48 89 08	 mov	 QWORD PTR [rax], rcx

; 145  :     }

  00058	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0005d	5f		 pop	 rdi
  0005e	c3		 ret	 0
??0bad_array_new_length@std@@QEAA@XZ ENDP		; std::bad_array_new_length::bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gbad_alloc@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gbad_alloc@std@@UEAAPEAXI@Z PROC			; std::bad_alloc::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gbad_alloc@std@@UEAAPEAXI@Z ENDP			; std::bad_alloc::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0bad_alloc@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
__that$ = 56
??0bad_alloc@std@@QEAA@AEBV01@@Z PROC			; std::bad_alloc::bad_alloc, COMDAT
$LN9:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 20	 sub	 rsp, 32			; 00000020H

; 73   :     {

  0000f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  0001b	48 89 08	 mov	 QWORD PTR [rax], rcx

; 72   :         : _Data()

  0001e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 83 c0 08	 add	 rax, 8
  00027	48 8b f8	 mov	 rdi, rax
  0002a	33 c0		 xor	 eax, eax
  0002c	b9 10 00 00 00	 mov	 ecx, 16
  00031	f3 aa		 rep stosb

; 74   :         __std_exception_copy(&_Other._Data, &_Data);

  00033	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 83 c0 08	 add	 rax, 8
  0003c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR __that$[rsp]
  00041	48 83 c1 08	 add	 rcx, 8
  00045	48 8b d0	 mov	 rdx, rax
  00048	e8 00 00 00 00	 call	 __std_exception_copy
  0004d	90		 npad	 1
  0004e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_alloc@std@@6B@
  0005a	48 89 08	 mov	 QWORD PTR [rax], rcx
  0005d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00062	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00066	5f		 pop	 rdi
  00067	c3		 ret	 0
??0bad_alloc@std@@QEAA@AEBV01@@Z ENDP			; std::bad_alloc::bad_alloc
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gexception@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gexception@std@@UEAAPEAXI@Z PROC			; std::exception::`scalar deleting destructor', COMDAT
$LN10:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gexception@std@@UEAAPEAXI@Z ENDP			; std::exception::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ?what@exception@std@@UEBAPEBDXZ
_TEXT	SEGMENT
tv69 = 0
this$ = 32
?what@exception@std@@UEBAPEBDXZ PROC			; std::exception::what, COMDAT

; 95   :     {

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 18	 sub	 rsp, 24

; 96   :         return _Data._What ? _Data._What : "Unknown exception";

  00009	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00013	74 0f		 je	 SHORT $LN3@what
  00015	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0001e	48 89 04 24	 mov	 QWORD PTR tv69[rsp], rax
  00022	eb 0b		 jmp	 SHORT $LN4@what
$LN3@what:
  00024	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BC@EOODALEL@Unknown?5exception@
  0002b	48 89 04 24	 mov	 QWORD PTR tv69[rsp], rax
$LN4@what:
  0002f	48 8b 04 24	 mov	 rax, QWORD PTR tv69[rsp]

; 97   :     }

  00033	48 83 c4 18	 add	 rsp, 24
  00037	c3		 ret	 0
?what@exception@std@@UEBAPEBDXZ ENDP			; std::exception::what
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0exception@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
_Other$ = 56
??0exception@std@@QEAA@AEBV01@@Z PROC			; std::exception::exception, COMDAT

; 73   :     {

$LN4:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  0000f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  0001b	48 89 08	 mov	 QWORD PTR [rax], rcx

; 72   :         : _Data()

  0001e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 83 c0 08	 add	 rax, 8
  00027	48 8b f8	 mov	 rdi, rax
  0002a	33 c0		 xor	 eax, eax
  0002c	b9 10 00 00 00	 mov	 ecx, 16
  00031	f3 aa		 rep stosb

; 74   :         __std_exception_copy(&_Other._Data, &_Data);

  00033	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 83 c0 08	 add	 rax, 8
  0003c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Other$[rsp]
  00041	48 83 c1 08	 add	 rcx, 8
  00045	48 8b d0	 mov	 rdx, rax
  00048	e8 00 00 00 00	 call	 __std_exception_copy
  0004d	90		 npad	 1

; 75   :     }

  0004e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00057	5f		 pop	 rdi
  00058	c3		 ret	 0
??0exception@std@@QEAA@AEBV01@@Z ENDP			; std::exception::exception
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaUserInfo.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaUserInfo.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
