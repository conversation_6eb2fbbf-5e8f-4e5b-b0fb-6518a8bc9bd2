﻿#pragma once

#include <Backend/Zone/Element/Item/InvenSlotable.h>

namespace mu2
{

	//====================================================================================================
	//
	//====================================================================================================

	class alignas(32) InvenStorageKnightage : public InvenStorage
	{
	private:
		KnightageId	m_knightageId;
	public:
		explicit InvenStorageKnightage(EntityPlayer& owner, eInvenType eType, const SlotSizeType maxSlot, Bool isActiveAdd, Bool isActiveSub)
			: InvenStorage(owner, eType, maxSlot, isActiveAdd, isActiveSub)
			, m_knightageId(0)
		{
			m_direction[SOURCE] = true;
			m_direction[TARGET] = true;

			m_emergencyRange = KnightStorages;
		}
		virtual ~InvenStorageKnightage() override {}

	public:
		void				SetKnightageId(KnightageId id) { m_knightageId = id; }
		KnightageId			GetOwnerKnightageId() const { return m_knightageId;  }
		void				PackBagForRemove(ItemBag& bag);
		virtual void		fillUpWithPos(const eSlotType& slot, ItemData& data) override;

	protected:
		virtual Bool		IsStorageKnight() const override { return true; }		
		virtual Bool		IsPushable(const eSlotType& toSlot, const ItemConstPtr& item, Bool isAllowBlockNExpired = false) const override;
		virtual Bool		IsPopable(const ItemConstPtr& item) const override;
		virtual void		OnNotifyPushed(const ItemPtr& item) override;
		virtual void		OnNotifyPoped(const ItemPtr& item) override;
	};
}