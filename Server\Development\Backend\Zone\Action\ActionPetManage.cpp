﻿#include "stdafx.h"
#include <Backend/Zone/ZoneServer.h>
#include <Backend/Zone/Action/ActionPlayerInventory.h>
#include <Backend/Zone/Action/ActionAchievement.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerCombatPower.h>
#include <Backend/Zone/Action/ActionPassivity.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerMembership.h>
#include <Backend/Zone/Action/ActionPlayerMailBox.h>
#include "ActionPetManage.h"
#include <Backend/Zone/System/NotifierGameContents.h>
namespace mu2
{
	class SpawnPetAbilityModifier
	{
	public:
		SpawnPetAbilityModifier() = delete;

		SpawnPetAbilityModifier(PetPtr pet, EntityPlayer* owner)
			: m_pet(pet), m_ownerPlayer(owner)
		{
			if (nullptr == m_ownerPlayer || nullptr == pet)
			{
				return;
			}

			auto actPet = m_ownerPlayer->GetAction<ActionPlayerPetManage>();
			if (actPet->m_spawnedPet == m_pet)
			{
				m_ownerPlayer->GetAction<ActionPlayerPetManage>()->updateDespawndPet();
				m_ownerPlayer->GetAction<ActionPlayerPetManage>()->calcCombatPower();
			}

			CounterInc( "SpawnPetAbilityModifier" );
		}

		~SpawnPetAbilityModifier()
		{
			if (nullptr == m_ownerPlayer || nullptr == m_pet)
			{
				return;
			}

			auto actPet = m_ownerPlayer->GetAction<ActionPlayerPetManage>();
			if (actPet->m_spawnedPet == m_pet)
			{
				m_ownerPlayer->GetAction<ActionPlayerPetManage>()->updateSpawnedPet();
				m_ownerPlayer->GetAction<ActionPlayerPetManage>()->calcCombatPower();
			}

			CounterDec( "SpawnPetAbilityModifier" );
		}

		PetPtr	m_pet;
		EntityPlayer* m_ownerPlayer;
	};

ActionPlayerPetManage::ActionPlayerPetManage(EntityPlayer* player )
	: Action4Player( player, ID )
	, m_manager( player )
	//, m_error( ErrorPet::SUCCESS)
{
	CounterInc("ActionPetManage");

	m_playerPet.m_petBoosterSkillIDList.resize(PetGrowthBoosterElem::MAX_BOOSTER_COUNT);
}

ActionPlayerPetManage::~ActionPlayerPetManage()
{
	CounterDec("ActionPetManage");

	EntityNpc* spawnedEntity = GetPetSpawnedEntity();
	if ( spawnedEntity )
	{
		spawnedEntity->GetHsm().Tran(EntityState::STATE_NPC_DEAD);
	}

	if (m_spawnedPet)
	{
		m_spawnedPet->SetSpawnedNpcId(0);
	}
}

void ActionPlayerPetManage::OnEnterZoneAndSendList()
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	if (m_playerPet.m_currentSpawnPetIndex == (0))
	{
		calcCombatPower();
		return;
	}

	PetSpawnState state;
	state.petUnique = m_playerPet.m_currentSpawnPetIndex;
	state.state = PetSpawnState::SPAWN;
	updateSpawnState(state);
}

void ActionPlayerPetManage::OnExitZone()
{
	if ( nullptr == m_spawnedPet)
	{
		return;
	}
	
	PetUnique tempId = m_spawnedPet->GetData().petId.unique;

	PetSpawnState state;
	state.petUnique = m_spawnedPet->GetData().petId.unique;
	state.state = PetSpawnState::DESPAWN;
	updateSpawnState(state);

	m_playerPet.m_currentSpawnPetIndex = std::move(tempId);
}

ErrorPet::Error ActionPlayerPetManage::InitializePetList(PetList& list)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorPet::playerNotFound);

	VALID_RETURN(m_manager.Init(list), ErrorPet::E_LOAD_FAILED);

	bool foundSpawnedPet = false;
	Byte maxSlotSize = m_playerPet.m_maxFellowSlot;

	for (auto& i : list)
	{
		if (m_playerPet.m_currentSpawnPetIndex == i.petId.unique) {
			foundSpawnedPet = true;
		}

		PetPtr petPtr = m_manager.Get(i.petId);
		if (nullptr == petPtr) {
			MU2_ERROR_LOG( LogCategory::CONTENTS
				         , "Not found Pet object !!! : PetID:%I64d - CharID:%d"
				         , i.petId.unique, i.petId.charId );
			continue;
		}

		// 지역 펫 정보 설정
		const PetInfoElem* petInfo = petPtr->GetElem();
		if (nullptr == petInfo) {
			MU2_ERROR_LOG( LogCategory::CONTENTS
						 , "Not found PetInfoElem !!! : PetIndex:%d, PetLevel:%d - CharID:%d"
						 , i.type, i.level, i.petId.charId );
			continue;
		}
		else {
			this->AddPetToRegion(petPtr);
		}

		if (0 < i.growthSlotNum) {
			ActionPlayerMembership* actMembership = GetEntityAction(GetOwnerPlayer());
			const PetXMLScript* petXMLScript = SCRIPTS.GetScript<PetXMLScript>();
			UInt16 maxSlotCount = static_cast<UInt16>(petXMLScript->petGrowthSlotBasicCount + actMembership->GetValueAtBenefitType(EMembershipBenefitType::eValue_PetGrowthOpenSlot));

			if (m_petGrowthSlotList.size() >= maxSlotCount) {
				MU2_ERROR_LOG( LogCategory::CONTENTS
							 , "Growth Slot Count Limit Over, the Slot is forcibly Reset 0 !!! : Count:%d > Limit:%d - PetID:%I64d - CharID:%d"
							 , m_petGrowthSlotList.size(), maxSlotCount
							 , i.petId.unique, i.petId.charId );
				petPtr->ChangeGrowthSlotNumber(0);
				this->updateDbPetInfo(petPtr);
			}
			else {
				auto it = m_petGrowthSlotList.find(i.growthSlotNum);
				if (it != m_petGrowthSlotList.end()) {
					MU2_ERROR_LOG( LogCategory::CONTENTS
								 , "Already registered Growth Slot number, the Slot is forcibly Reset 0 !!! : SlotNo:%d - PetID:%I64d - CharID:%d"
								 , i.growthSlotNum
								 , i.petId.unique, i.petId.charId );
					petPtr->ChangeGrowthSlotNumber(0);
					this->updateDbPetInfo(petPtr);
				}
				else {
					m_petGrowthSlotList.emplace(i.growthSlotNum, petPtr);
				}
			}
		}

		if (i.fellowSlotNumber == 0) {
			continue;
		}

		//MU2_SET_ERR_RETURN_IF((m_fellowSlots.size() < i.fellowSlotNumber), ErrorPet::E_LOAD_FAILED);
		if (m_fellowSlots.size() < i.fellowSlotNumber) {
			maxSlotSize = std::max(maxSlotSize, i.fellowSlotNumber);
			MU2_FATAL_LOG(core::LogCategory::DEFAULT, "[Not match fellowSlotNumber with m_fellowSlots.size()][charid:%d][m_fellowSlots.size():%d]", i.petId.charId, m_fellowSlots.size());
		}

		m_fellowSlots[i.fellowSlotNumber] = petPtr;

		changeBonusOption(true, PetBonusOptionType::PET_FELLOW, petPtr->GetElem()->fellowType, petPtr->GetElem()->grade, petPtr->GetElem()->tier);
	}

	if (m_playerPet.m_maxFellowSlot < maxSlotSize)
	{
		m_playerPet.m_maxFellowSlot = maxSlotSize;

		EzcResGamePetExpandFellowSlot* res = NEW EzcResGamePetExpandFellowSlot;
		res->maxSlotNumber = maxSlotSize;


		SERVER.SendToClient( player, EventPtr( res ) );

		updateDbPetPlayerInfo();
	}

	if (   0 < m_playerPet.m_currentSpawnPetIndex
		&& true != foundSpawnedPet) {
		
		PetData petData;
		petData.petId.unique = m_playerPet.m_currentSpawnPetIndex;

		MU2_ERROR_LOG( LogCategory::CONTENTS
					 , "Failed to found Spawned Pet Info !!! : PetIndex:%d, PetUniqueID:%I64d - CharID:%d"
			         , petData.petId.index
			         , petData.petId.unique, petData.petId.charId );

		m_playerPet.m_currentSpawnPetIndex = 0;
		updateDbPetPlayerInfo();
	}

	// 펫 모험 정보 초기화 시간 경과한 경우 초기화 & DB 갱신
	this->CheckAndUpdatePetQuestInfoReset();

	return ErrorPet::SUCCESS;
}

void ActionPlayerPetManage::InitializePlayerPetInfo( const UInt64 currentSpawnedPet, const Byte maxFellowSlotCount
											 	   , UInt32 petLastQuestIndex
												   , Byte petLastQuestState
												   , Byte petQuestPlayCount
												   , DateTimeEx& petQuestResetDate
												   , std::vector<IndexSkill>& petBoosterSkillList )
{
	m_playerPet.m_currentSpawnPetIndex = currentSpawnedPet;
	m_playerPet.m_petLastQuestIndex = petLastQuestIndex;
	m_playerPet.m_petLastQuestState = petLastQuestState;
	m_playerPet.m_petQuestPlayCount = petQuestPlayCount;
	m_playerPet.m_petQuestResetDate = petQuestResetDate;

	m_playerPet.m_petBoosterSkillIDList = petBoosterSkillList;


	if (maxFellowSlotCount == 0)
	{
		const auto& shopPack = SCRIPTS.GetPetShopPack();
		Byte maxSlotCount = 0;
		for (const auto& fellowSlot : shopPack.fellowSlots)
		{
			if (fellowSlot.isPay)
			{
				break;;
			}
			++maxSlotCount;
		}

		expandFellowSlot(maxSlotCount, false);
		updateDbPetPlayerInfo();
	}
	else
	{
		expandFellowSlot(maxFellowSlotCount, false);
	}
}

void ActionPlayerPetManage::GetPetRegionList(OUT PetRegionList& petRegionList)
{
	for (auto itPair : m_petRegionList) {
		petRegionList.push_back(itPair.second);
	}
}


ErrorPet::Error ActionPlayerPetManage::UpdateSpawnState(const PetSpawnState& spawnState, const Int32 removeIndex /*= 0*/ )
{
	ErrorPet::Error eError = updateSpawnState(spawnState, removeIndex);
	VALID_RETURN(eError == ErrorPet::SUCCESS, eError);

	updateDbPetPlayerInfo();

	return ErrorPet::SUCCESS;
}

ErrorPet::Error ActionPlayerPetManage::updateSpawnState(const PetSpawnState &spawnState, const Int32 removeIndex /*= 0*/ )
{
	ErrorPet::Error eError = ErrorPet::SUCCESS;

	PetSpawnStates states;

	switch (spawnState.state)
	{
	case PetSpawnState::SPAWN:
		if (nullptr != m_spawnedPet)
		{
			if (m_spawnedPet->GetData().petId.unique == spawnState.petUnique)
			{
				break;
			}
			else
			{
				eError = despawn(m_spawnedPet->GetData().petId, states, removeIndex);
				VALID_RETURN(eError == ErrorPet::SUCCESS, eError);
			}
		}

		eError = spawn(PetId(spawnState.petUnique), states);		
		VALID_RETURN(eError == ErrorPet::SUCCESS, eError);
		break;

	case  PetSpawnState::DESPAWN:
	
		eError = despawn(PetId(spawnState.petUnique), states, removeIndex);
		VALID_RETURN(eError == ErrorPet::SUCCESS, eError);
		break;

	default:
		MU2_ASSERT(false);
		return ErrorPet::E_INVAILD_SPAWN_STATE;
	}

	calcCombatPower();

	EResPetUpdateSpawnState* res = NEW EResPetUpdateSpawnState;
	res->spawnStates = std::move(states);
	res->eError = eError;
	SERVER.SendToClient(GetOwnerPlayer(), EventPtr(res));

	return eError;
}

ErrorPet::Error ActionPlayerPetManage::spawn( const PetId& id, PetSpawnStates& states )
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), ErrorPet::playerNotFound);

	PetPtr pet = m_manager.Get( id );
	VALID_RETURN(pet, ErrorPet::E_NOT_FOUND_PET_ID);
	VALID_RETURN(pet->GetData().fellowSlotNumber == 0, ErrorPet::E_NOT_FOUND_PET_ID);
	VALID_RETURN(0 == pet->GetSpawnedNpcId(), ErrorPet::E_NOT_DESPAWNED);
	VALID_RETURN(pet != m_spawnedPet, ErrorPet::E_NOT_DESPAWNED);
	VALID_RETURN(pet->GetElem()->useType == PetInfoElem::NORMAL, ErrorPet::E_NOT_DESPAWNED);
	VALID_RETURN(owner->GetSkillControlAction().SpawnPets(pet), ErrorPet::E_FAILED_INSERT);

	m_playerPet.m_currentSpawnPetIndex = id.unique;

	m_spawnedPet			= pet;

	updateSpawnedPet();
	
	PetSpawnState s;
	s.state = PetSpawnState::SPAWN;
	s.petUnique = pet->GetData().petId.unique;
	states.push_back( std::move( s ) );

	return ErrorPet::SUCCESS;
}

ErrorPet::Error ActionPlayerPetManage::despawn( const PetId& id, PetSpawnStates& states, const Int32 removeIndex /*= 0*/)
{
	PetPtr pet = m_manager.Get( id );
	if (pet == nullptr)
	{
		pet = m_manager.GetRemovedPet( removeIndex, id );
	}

	VALID_RETURN(pet, ErrorPet::E_NOT_FOUND_PET_ID);
	VALID_RETURN(m_spawnedPet, ErrorPet::E_NOT_SPAWNED);
	VALID_RETURN(id == PetId(m_spawnedPet->GetData().petId), ErrorPet::E_NOT_SPAWNED);

	EntityNpc* spawnedPetEntity = GetPetSpawnedEntity();
	if ( spawnedPetEntity )
	{
		spawnedPetEntity->GetHsm().Tran(EntityState::STATE_NPC_DEAD );
	}

	updateDbPetInfo( pet );
	updateDespawndPet();

	m_spawnedPet->SetSpawnedNpcId( 0 );
	m_spawnedPet = nullptr;

	m_playerPet.m_currentSpawnPetIndex = 0;

	PetSpawnState s;
	s.state = PetSpawnState::DESPAWN;
	s.petUnique = pet->GetData().petId.unique;
	states.push_back( std::move( s ) );

	return ErrorPet::SUCCESS;
}

void ActionPlayerPetManage::updateSpawnedPet()
{
	VERIFY_RETURN(m_spawnedPet, );

	GetOwnerPlayer()->GetAction<ActionPlayerAbility>()->PetSpawnAbility( m_spawnedPet );
	GetOwnerPlayer()->GetAction<ActionPlayerAbility>()->Calculate();
}

void ActionPlayerPetManage::updateDespawndPet()
{
	VERIFY_RETURN(m_spawnedPet, );
	GetOwnerPlayer()->GetAction<ActionPlayerAbility>()->PetDespawnAbility( m_spawnedPet );
	GetOwnerPlayer()->GetAction<ActionPlayerAbility>()->Calculate();
}

Bool ActionPlayerPetManage::CanRegister( Int32 addedCount )
{
	if ( SCRIPTS.GetScript<ItemConfigScript>()->GetPetShopPack().maxSlotCount < m_manager.GetSize() + addedCount)
	{
		SendSystemMsg( GetOwnerPlayer(), SystemMessage( L"sys", L"Msg_pet_create_fail" ) );
		return false;
	}

	return true;
}

ErrorPet::Error ActionPlayerPetManage::Register( const IndexPet type, PetData& data )
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), ErrorPet::playerNotFound);

	VALID_RETURN(SCRIPTS.GetScript<ItemConfigScript>()->GetPetShopPack().maxSlotCount > m_manager.GetSize(), ErrorPet::E_OVER_HAVING_PET_SIZE );

	auto petPtr = m_manager.Create(type, 0 );
	VALID_RETURN(petPtr, ErrorPet::E_FAILED_INSERT );

	AddPetToRegion(petPtr);

	EResPetRegister* res = NEW EResPetRegister;
	res->eError = ErrorPet::SUCCESS;
	data = res->petData = petPtr->GetData();
	SERVER.SendToClient(owner, EventPtr( res ) );

	ActionPlayerAchievement* aa = GetEntityAction(owner);
	AchievementPetRegisterSuccessEvent achievement( owner, petPtr->GetData() );
	aa->OnEvent( &achievement );

	/*
	// JPMUT-262 수정을 하면서 모든 버젼에 적용을 하기로 함. 
	// 클라이언트와 동시에 업데이트 할 것. 1차 적용은 source & JPN 버젼
	SystemMessage sysMsg( L"sys", L"Msg_pet_create_Success" );
	sysMsg.SetParam( L"str", petPtr->GetData().name );
	SendSystemMsg(owner, sysMsg );
	*/
	return ErrorPet::SUCCESS;
}

PetPtr ActionPlayerPetManage::Find( const PetId &petId )
{
	PetPtr pet = m_manager.Get( petId );
	return pet;
}

void ActionPlayerPetManage::CreateData( const IndexPet type, PetData &petData )
{
	m_manager.CreateData( type, 0, petData );
}

Bool ActionPlayerPetManage::AddPet( const PetData &petData )
{
	EntityPlayer* owner = GetOwnerPlayer();

	PetPtr pet = m_manager.Get( petData.petId );
	
	if (pet)
	{
		pet->UpdateData( petData );
	}
	else
	{
		pet = m_manager.Insert( petData );
	}

	VERIFY_RETURN(pet, false);

	AddPetToRegion(pet);
	
	ActionPlayerAchievement* aa = GetEntityAction(owner);
	AchievementPetRegisterSuccessEvent achievement(owner, petData );
	aa->OnEvent( &achievement );

	return true;
}

ErrorPet::Error ActionPlayerPetManage::UpdateGrowPet( const PetData &petData )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorPet::playerNotFound);

	PetPtr pet = m_manager.Get( petData.petId );
	VALID_RETURN(pet, ErrorPet::E_NOT_FOUND_PET_ID );
	auto modifier = std::make_shared<SpawnPetAbilityModifier>( pet, player); // 착용 중 펫일 경우 능력치 변경

	pet->UpdateData( petData );
	pet->Initialize();
	updateDbPetInfo( pet );

	theNotifierGameContents.OnPet_Grew(*player, petData);

	return ErrorPet::SUCCESS;
}


ErrorPet::Error ActionPlayerPetManage::Unregister( const PetId& petId)
{
	petId;
	/*PetPtr pet = m_manager.Get( id );
	MU2_SET_ERR_RETURN_IF( nullptr == pet, ErrorPet::E_NOT_FOUND_PET_ID );

	// 동료펫 지울 경우
	if (pet->GetData().fellowSlotNumber != 0)
	{
		MU2_SET_ERR_RETURN_IF(false == DelFellow(id), GetError());
	}

	// 소환 중인 펫 지울 경우
	if ( m_spawnedPet == pet )
	{
		PetSpawnState state;
		state.petId = pet->GetData().id;
		state.state = PetSpawnState::DESPAWN;
		MU2_SET_ERR_RETURN_IF( false == UpdateSpawnState( state ), GetError() );
	}

	MU2_SET_ERR_RETURN_IF( false == m_manager.Remove( id ), ErrorPet::E_FAILED_REMOVE );

	EResPetUnregister* res = NEW EResPetUnregister;
	res->result = ErrorPet::SUCCESS;
	SERVER.SendToWorld( GetOwnerPlayer(), EventPtr( res ) );

	SendSystemMsg( GetOwnerPlayer(), SystemMessage( L"sys", L"Msg_pet_delete_Success" ) );*/

	return ErrorPet::SUCCESS;
}

ErrorPet::Error ActionPlayerPetManage::Rename(const PetId& id, const std::wstring& name)
{
	PetPtr pet = m_manager.Get(id);
	VALID_RETURN(pet, ErrorPet::E_NOT_FOUND_PET_ID);
	VALID_RETURN(pet->GetData().name != name, ErrorPet::E_ERROR);

	VALID_RETURN( Limits::MAX_CHAR_NAME_LENGTH >= name.length(), ErrorPet::E_ERROR );

	const FilterData & filter = SCRIPTS.GetFilterDatas();
	VALID_RETURN(filter.Check( FilterData::IMPOSSIBLE_NAME, name ), ErrorPet::E_ERROR );

	PetData data = pet->GetData();
	data.name = name;
	++data.renameCnt;

	const PetShopPack& petShopPack = SCRIPTS.GetPetShopPack();

	ErrorPet::Error eError = pay(pet, data, PetUpdateType::RENAME, petShopPack.GetRename(pet->GetData().renameCnt));
	VALID_RETURN(eError == ErrorPet::SUCCESS, eError);

	return ErrorPet::SUCCESS;
}

void ActionPlayerPetManage::RenameResult(const PetData& petData)
{
	PetPtr petPtr = m_manager.Get(petData.petId);
	if (nullptr == petPtr)
	{
		MU2_ASSERT(false);
		return;
	}

	if ( petPtr == m_spawnedPet )
	{
		EntityNpc* spawnedEntity = GetPetSpawnedEntity();
		if ( spawnedEntity)
		{
			auto attrNpc = spawnedEntity->GetAttribute<AttributeNpc>();
			attrNpc->nickName = petData.name;
	
			ENtfPetRename* ntf = NEW ENtfPetRename;
			ntf->entityId = spawnedEntity->GetId();
			ntf->nickName = petData.name;
			spawnedEntity->GetAction<ActionSector>()->NearGridCast( EventPtr( ntf ) );
		}
	}

	petPtr->UpdateData( petData );
	updateDbPetInfo(petPtr);

	EResPetRename* res = NEW EResPetRename;
	res->eError = ErrorPet::SUCCESS;
	res->petData = petData;
	SERVER.SendToClient(GetOwnerPlayer(), EventPtr(res));

	SendSystemMsg(GetOwnerPlayer(), SystemMessage(L"sys", L"Msg_pet_name_change_Success"));
}

ErrorPet::Error ActionPlayerPetManage::Grow(const PetId& id, const UInt64Vector &materialPetId)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorPet::playerNotFound);

	VALID_RETURN(0 < materialPetId.size() && materialPetId.size() <= 5, ErrorPet::E_MATERIAL_ERROR );

	Zen subZen = 0;
	GreenZen subGreenZen = 0;
	UInt32 addExp = 0;

	PetPtr petPtr = m_manager.Get( id );

	VALID_RETURN(petPtr, ErrorPet::E_NOT_FOUND_PET_ID );
	VALID_RETURN(petPtr->CanGrow(), ErrorPet::E_NOT_FOUND_PET_ID);

	UInt32Vector materialType;
	std::vector<UInt8> materialLevel;

	for (auto iter : materialPetId)
	{
		VALID_RETURN(id.unique != iter, ErrorPet::E_MATERIAL_ERROR );

		PetPtr petMaterial = m_manager.Get( PetId(iter) );
		VALID_RETURN(petMaterial, ErrorPet::E_NOT_FOUND_PET_ID );
		VALID_RETURN( m_spawnedPet == nullptr || m_spawnedPet->GetData().petId != petMaterial->GetData().petId, ErrorPet::E_NOT_DESPAWNED );
		VALID_RETURN(checkFellowPet( petMaterial ), ErrorPet::E_FELLOW_PET );

		VALID_RETURN(0 == petMaterial->GetData().growthSlotNum, ErrorPet::E_GROWTH_SLOT_REGISTERED_PET);

		addExp += petMaterial->GetLevelElem()->materialExp;
		subZen += petMaterial->GetLevelElem()->materialZen;
		subGreenZen += petMaterial->GetLevelElem()->materialgreenZen;

		materialType.push_back(petMaterial->GetData().type);
		materialLevel.push_back(petMaterial->GetData().level);
	}
	

	VALID_RETURN(player->IsEnoughZen( subZen), ErrorPet::E_NOT_ENOUGH_MONEY);
	VALID_RETURN(player->IsEnoughGreenZen( subGreenZen), ErrorPet::E_NOT_ENOUGH_GREEN_ZEN);

	auto levelElem = petPtr->GetLevelElem();
	VALID_RETURN(levelElem, ErrorPet::E_NOT_FOUND_PET_ID);
	
	PetData data;
	petPtr->Copy(&data);
	
	Pet tempPet( data, player);

	tempPet.Initialize();
	tempPet.AddExp( addExp );
	tempPet.Copy( &data );

	ItemPetGrowTransaction* trans = NEW ItemPetGrowTransaction(__FUNCTION__, __LINE__, player, LogCode::E_ITEM_GROW_PET, petPtr->GetData().level, addExp, data, materialPetId, materialType, materialLevel, subZen, subGreenZen);
	TransactionPtr transPtr(trans);
	{
		transPtr->Bind(MoneyProcedure(player, DbChangedMoney(player->GetCharId(), subZen, ChangedMoney::SUB, ChangedMoney::ZEN)));
		transPtr->Bind(MoneyProcedure(player, DbChangedMoney(player->GetCharId(), subGreenZen, ChangedMoney::SUB, ChangedMoney::GREENZEN)));
		
		VALID_RETURN(TransactionSystem::Register(transPtr), ErrorPet::TranRegisterFailed);
	}

	Int32 bufferIdx = m_manager.RemovedBuffer(materialPetId);
	trans->SetBufferIdx( bufferIdx );

	return ErrorPet::SUCCESS;
}

void ActionPlayerPetManage::AddExp(PetPtr petPtr, const PetExp exp)
{
	auto modifier = std::make_shared<SpawnPetAbilityModifier>(petPtr, GetOwnerPlayer()); // 착용 중 펫일 경우 능력치 변경

	Byte beforeLevel = petPtr->GetData().level;

	petPtr->AddExp(exp);

	// if ( 처리할 업적이 활성화 되어 있다면 ? )
	{
		CheckPointupAchievement(static_cast<UInt32>(exp));
	}

	if (beforeLevel != petPtr->GetData().level)
	{
		CheckLevelupAchievement( petPtr->GetData() );
		SendPetData( petPtr->GetData() );

		updateDbPetInfo( petPtr );
	}

	EResGamePetGrow* ntf = NEW EResGamePetGrow;
	ntf->petUnique = petPtr->GetData().petId.unique;
	ntf->curExp = petPtr->GetData().exp;
	ntf->addExp = exp;
	ntf->curLevel = petPtr->GetData().level;

	SERVER.SendToClient(GetOwnerPlayer(), EventPtr(ntf));
}

void ActionPlayerPetManage::AddExpSpwendPet(const PetExp exp)
{
	VALID_RETURN(exp, );
	VALID_RETURN(m_spawnedPet, );

	AddExp(m_spawnedPet, exp);
}

void ActionPlayerPetManage::AddExpGrowthPet(const PetExp exp)
{
	VALID_RETURN(exp, );

	ActionPlayerMembership* actMembership = GetEntityAction(GetOwnerPlayer());
	const PetXMLScript* petXMLScript = SCRIPTS.GetScript<PetXMLScript>();
	UInt16 maxSlotCount = static_cast<UInt16>(petXMLScript->petGrowthSlotBasicCount + actMembership->GetValueAtBenefitType(EMembershipBenefitType::eValue_PetGrowthOpenSlot));

	for ( auto itPos = m_petGrowthSlotList.begin();
		  itPos != m_petGrowthSlotList.end(); ) {

		PetPtr petPtr = itPos->second;
		VALID_DO(petPtr, continue);

		if (petPtr->GetData().growthSlotNum > maxSlotCount) {
			petPtr->ChangeGrowthSlotNumber(0);
			this->updateDbPetInfo(petPtr);

			ENtfPetSyncInfo* ntf = NEW ENtfPetSyncInfo;
			ntf->petData = petPtr->GetData();
			SERVER.SendToClient(this->GetOwnerPlayer(), EventPtr(ntf));

			itPos = m_petGrowthSlotList.erase(itPos);
		}
		else {
			++itPos;

			AddExp(petPtr, exp);
		}
	}
}

void ActionPlayerPetManage::AddPetToRegion(PetPtr petPtr)
{
	VALID_RETURN(petPtr, );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	PetRegionElem petRegionElem;
	SCRIPTS.GetScript<PetRegionScript>()->GetPetRegionElemByPetGroupID( petPtr->GetElem()->petGroupID
		                                                              , OUT petRegionElem );
	std::vector<PetCollectionElem*> petCollectionElemList;
	SCRIPTS.GetScript<PetCollectionScript>()->GetPetCollectionElemListByParam( petRegionElem.m_petRegionIndex
		                                                                     , owner->GetClassType()
	                                                                         , OUT petCollectionElemList);

	// 지역 정보 갱신
	auto getRegionData = [&](IndexPetRegion regionIdx) -> PetRegionData& {
		auto itFound = m_petRegionList.find(regionIdx);
		if (m_petRegionList.end() == itFound) {
			PetRegionData regionData;
			regionData.petRegionIndex = regionIdx;
			m_petRegionList.emplace(regionIdx, regionData);
			itFound = m_petRegionList.find(regionIdx);
		}
		return itFound->second;
	};

	PetRegionData& petRegionData = getRegionData(petRegionElem.m_petRegionIndex);

	Byte beforeLevel = petRegionData.level;
	UInt16 beforeCount = static_cast<UInt16>(petRegionData.petGoupIDList.size());
	UInt64 beforeExp = petRegionData.petTotalExp;

	petRegionData.AddPetGroupInfo(petPtr->GetElem()->petGroupID);
	petRegionData.petTotalExp += petPtr->GetElem()->petPoint;

	PetGrowthElem petGrowthElem;
	if (true == SCRIPTS.GetScript<PetGrowthScript>()->GetPetGrowthElem( petRegionData.petRegionIndex
																	  , petRegionData.level, petRegionData.petTotalExp
																	  , OUT petGrowthElem)) {
		petRegionData.level = static_cast<Byte>(petGrowthElem.m_petGrowthLevel);
	}

	EzcNtfPetRegionInfo *ntfPetRegionInfo = NEW EzcNtfPetRegionInfo;
	ntfPetRegionInfo->petRegionList.push_back(petRegionData);
	SERVER.SendToClient(GetOwnerPlayer(), EventPtr(ntfPetRegionInfo));


	std::vector<IndexSkill> applySkillIDList;
	SCRIPTS.GetScript<PetCollectionScript>()->GetApplySkillIDListByAttachPet( petRegionElem.m_petRegionIndex
																		    , owner->GetClassType()
																		    , beforeCount, static_cast<UInt16>(petRegionData.petGoupIDList.size())
		                                                                    , beforeLevel, petRegionData.level
		                                                                    , m_petRegionRewardApplySkillList
																		    , OUT applySkillIDList
	);

	std::vector<IndexSkill> logSkillIDList;

	auto actPassivity = owner->GetAction<ActionPassivity>();
	for( auto itSkillID = applySkillIDList.begin();
	     itSkillID != applySkillIDList.end();
		 ++itSkillID ) {

		if ( true != actPassivity->Apply(*itSkillID) ) {
			MU2_WARN_LOG( LogCategory::CONTENTS
						, "Failed to add Passive Skill !!! : SkillIndex:%d - CharID:%d"
						, *itSkillID, owner->GetCharId() );
		}
		else {
			m_petRegionRewardApplySkillList.emplace(*itSkillID);
			logSkillIDList.emplace_back(*itSkillID);
		}
	}

	// 펫 지역 레벨 변경 로그 작성
	EReqLogDb2* log = NEW EReqLogDb2;
	EventPtr logEventPtr(log);

	EventSetter::FillLogForEntity(LogCode::E_PET_REGION_LEVEL_CHANGE, GetOwnerPlayer(), log->action);
	log->action.var32s.push_back(static_cast<Int32>(petRegionData.petRegionIndex));
	
	log->action.var32s.push_back(static_cast<Int32>(beforeLevel));
	log->action.var32s.push_back(static_cast<Int32>(petRegionData.level));

	log->action.var32s.push_back(static_cast<Int32>(beforeCount));
	log->action.var32s.push_back(static_cast<Int32>(petRegionData.petGoupIDList.size()));

	log->action.var64s.push_back(static_cast<Int64>(beforeExp));
	log->action.var64s.push_back(static_cast<Int64>(petRegionData.petTotalExp));

	for (auto& v : logSkillIDList) {
		log->action.var32s.push_back(static_cast<Int32>(v));
	}

	SendDataCenter(logEventPtr);
}

void ActionPlayerPetManage::DeletePetToRegion(PetPtr petPtr)
{
	VERIFY_RETURN(petPtr, );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	PetRegionElem petRegionElem;
	SCRIPTS.GetScript<PetRegionScript>()->GetPetRegionElemByPetGroupID( petPtr->GetElem()->petGroupID
		                                                              , OUT petRegionElem );

	auto itFound = m_petRegionList.find(petRegionElem.m_petRegionIndex);
	if (m_petRegionList.end() == itFound) {
		MU2_INFO_LOG( LogCategory::CONTENTS
					, "Not found PetRegionElem !!! : PetGroupID:%d - PetID:%I64d, CharID:%d"
					, petPtr->GetElem()->petGroupID, GetOwnerPlayer()->GetCharId()
		);
		return;
	}

	PetRegionData& petRegionData = itFound->second;

	UInt16 beforeCount = static_cast<UInt16>(petRegionData.petGoupIDList.size());
	Byte beforeLevel = petRegionData.level;
	UInt64 beforeExp = petRegionData.petTotalExp;
	
	petRegionData.DeletePetGroupInfo(petPtr->GetElem()->petGroupID);
	petRegionData.petTotalExp -= petPtr->GetElem()->petPoint;

	PetGrowthElem petGrowthElem;
	if (true == SCRIPTS.GetScript<PetGrowthScript>()->GetPetGrowthElem( petRegionData.petRegionIndex
																	  , 0, petRegionData.petTotalExp
																	  , OUT petGrowthElem)) {
		petRegionData.level = static_cast<Byte>(petGrowthElem.m_petGrowthLevel);
	}

	EzcNtfPetRegionInfo *ntfPetRegionInfo = NEW EzcNtfPetRegionInfo;
	ntfPetRegionInfo->petRegionList.push_back(petRegionData);
	SERVER.SendToClient(GetOwnerPlayer(), EventPtr(ntfPetRegionInfo));


	std::vector<IndexSkill> cancelSkillIDList;
	SCRIPTS.GetScript<PetCollectionScript>()->GetCancelSkillIDListByDetachPet( petRegionElem.m_petRegionIndex
																		     , owner->GetClassType()
																			 , beforeCount, static_cast<UInt16>(petRegionData.petGoupIDList.size())
		                                                                     , beforeLevel, petRegionData.level
		                                                                     , m_petRegionRewardApplySkillList
																			 , OUT cancelSkillIDList
	);

	std::vector<IndexSkill> logSkillIDList;

	auto actPassivity = owner->GetAction<ActionPassivity>();
	for( auto itSkillID = cancelSkillIDList.begin();
	     itSkillID != cancelSkillIDList.end();
		 ++itSkillID ) {

		if ( true != actPassivity->Cancel(*itSkillID) ) {
			MU2_WARN_LOG( LogCategory::CONTENTS
						, "Failed to cancel Passive Skill !!! : SkillIndex:%d - CharID:%d"
						, *itSkillID, owner->GetCharId() );
		}
		else {
			auto itErase = m_petRegionRewardApplySkillList.find(*itSkillID);
			if (m_petRegionRewardApplySkillList.end() != itErase) {
				m_petRegionRewardApplySkillList.erase(itErase);

				logSkillIDList.emplace_back(*itSkillID);
			}
		}
	}

	// 펫 지역 레벨 변경 로그 작성
	EReqLogDb2* log = NEW EReqLogDb2;
	EventPtr logEventPtr(log);

	EventSetter::FillLogForEntity(LogCode::E_PET_REGION_LEVEL_CHANGE, GetOwnerPlayer(), log->action);
	log->action.var32s.push_back(static_cast<Int32>(petRegionData.petRegionIndex));

	log->action.var32s.push_back(static_cast<Int32>(beforeLevel));
	log->action.var32s.push_back(static_cast<Int32>(petRegionData.level));

	log->action.var32s.push_back(static_cast<Int32>(beforeCount));
	log->action.var32s.push_back(static_cast<Int32>(petRegionData.petGoupIDList.size()));

	log->action.var64s.push_back(static_cast<Int64>(beforeExp));
	log->action.var64s.push_back(static_cast<Int64>(petRegionData.petTotalExp));

	for (auto& v : logSkillIDList) {
		log->action.var32s.push_back(static_cast<Int32>(v));
	}

	SendDataCenter(logEventPtr);
}


void ActionPlayerPetManage::RemovePetBoosterSkill(IndexSkill skillID)
{
	Bool isPetBossterUpdate = false;

	for ( int i = 0;
		  i < PetGrowthBoosterElem::MAX_BOOSTER_COUNT;
		  ++i ) {
		if ( 0 < skillID
			 && m_playerPet.m_petBoosterSkillIDList[i] == skillID ) {

			m_playerPet.m_petBoosterSkillIDList[i] = 0;
			isPetBossterUpdate = true;

			EzcNtfPetBoosterStop* ntf = NEW EzcNtfPetBoosterStop;
			ntf->petBoosterSlotNum = static_cast<Byte>(i + 1);
			ntf->skillIndex = skillID;
			SERVER.SendToClient(GetOwnerPlayer(), EventPtr(ntf));
		}
	}

	if (true == isPetBossterUpdate) {
		this->UpdateDbPetBoosterSkillList();
	}
}

void ActionPlayerPetManage::ResetPetBoosterSkillList()
{
	Bool bUpdate = false;

	for (int i = 0;
		i < PetGrowthBoosterElem::MAX_BOOSTER_COUNT;
		++i) {
		if (m_playerPet.m_petBoosterSkillIDList[i] != 0) {

			bUpdate = true;

			IndexSkill skillID = m_playerPet.m_petBoosterSkillIDList[i];
			m_playerPet.m_petBoosterSkillIDList[i] = 0;

			EzcNtfPetBoosterStop* ntf = NEW EzcNtfPetBoosterStop;
			ntf->petBoosterSlotNum = static_cast<Byte>(i + 1);
			ntf->skillIndex = skillID;
			SERVER.SendToClient(GetOwnerPlayer(), EventPtr(ntf));
		}
	}

	if (true == bUpdate) {
		this->UpdateDbPetBoosterSkillList();
	}
}

void ActionPlayerPetManage::ResetPetQuest()
{
	DateTimeEx currDateTime = DateTime::GetPresentTime();

	const PetXMLScript* petXMLScript = SCRIPTS.GetScript<PetXMLScript>();

	DateTimeEx resetDate = petXMLScript->GetPetQuestResetDateTime();

	MU2_INFO_LOG( LogCategory::CONTENTS
				, "Pet Quest Reset !!! : ResetDate:%d-%d-%d %d:00 > LastResetDate:%d-%d-%d %d:00 - CharID:%d"
				, resetDate.GetTime().year, resetDate.GetTime().month , resetDate.GetTime().day
				, resetDate.GetTime().hour
				, m_playerPet.m_petQuestResetDate.GetTime().year, m_playerPet.m_petQuestResetDate.GetTime().month , m_playerPet.m_petQuestResetDate.GetTime().day
				, m_playerPet.m_petQuestResetDate.GetTime().hour
				, GetOwnerPlayer()->GetCharId()
	);

	// 펫 모험 정보 초기화 설정
	m_playerPet.m_petLastQuestIndex = 0;
	m_playerPet.m_petLastQuestState = static_cast<Byte>(PetQuestElem::QuestResultType::None);
	m_playerPet.m_petQuestPlayCount = 0;
	m_playerPet.m_petQuestResetDate = currDateTime;

	// 펫 모험 관련 DB 갱신
	updateDbPetPlayerInfo();

	PetQuestData petQuest;
	this->GetPetQuestInfo(OUT petQuest);

	EzcResPetQuestInfo* resSuccess = NEW EzcResPetQuestInfo;
	resSuccess->result = ErrorPet::SUCCESS;
	resSuccess->petQuestData = petQuest;

	SERVER.SendToClient(GetOwnerPlayer(), EventPtr(resSuccess));

	// 각각의 펫 모험 보낸 상태 초기화 및 통지
	this->UpdatePetQuestSummonStateAll();
}

ErrorPet::Error ActionPlayerPetManage::Upgrade( const PetId& id, const UInt64Vector &materialId )
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), ErrorPet::playerNotFound);

	PetPtr petPtr = m_manager.Get( id );
	VALID_RETURN(petPtr, ErrorPet::E_NOT_FOUND_PET_ID );

	VALID_RETURN(petPtr->CanUpgrade(), ErrorPet::E_PET_INFO_ELEM );

	const PetShopPack &pack = SCRIPTS.GetPetShopPack();

#ifdef __Patch_Pet_Upgrade_Grade_check_by_kangms_2019_3_22
	const auto upgradeElem = pack.PetUpgradePacks.find( PetUpgrade::Key(petPtr->GetElem()->grade, petPtr->GetElem()->tier + 1) );
#else//__Patch_Pet_Upgrade_Grade_check_by_kangms_2019_3_22
	const auto upgradeElem = pack.PetUpgradePacks.find( petPtr->GetElem()->tier + 1 );
#endif//__Patch_Pet_Upgrade_Grade_check_by_kangms_2019_3_22

	VALID_RETURN(pack.PetUpgradePacks.end() != upgradeElem, ErrorPet::E_PET_INFO_ELEM );

	
	InvenRange ranges(InvenWithStorages);

	UInt32Vector materialType;
	std::vector<CharLevel> materialLevel;

	VALID_RETURN(owner->IsEnoughZen(upgradeElem->second.zenAmount), ErrorPet::E_NOT_ENOUGH_MONEY);
	VALID_RETURN(owner->IsEnoughGreenZen(upgradeElem->second.greenGenAmount), ErrorPet::E_NOT_ENOUGH_GREEN_ZEN);
	VALID_RETURN(owner->GetInventoryAction().IsEnoughItem(ranges, upgradeElem->second.itemIndex, upgradeElem->second.itemAmount), ErrorPet::E_NOT_ENOUGH_ITEMS );
	VALID_RETURN( upgradeElem->second.materialPetAmount == materialId.size(), ErrorPet::E_MATERIAL_ERROR );

	for (auto petId : materialId)
	{
		PetPtr material = m_manager.Get( PetId(petId) );
		VALID_RETURN(petId != id.unique, ErrorPet::E_MATERIAL_ERROR );
		VALID_RETURN(material, ErrorPet::E_NOT_FOUND_PET_ID );
		VALID_RETURN(checkFellowPet( material ), ErrorPet::E_FELLOW_PET );
		VALID_RETURN( m_spawnedPet == nullptr || m_spawnedPet->GetData().petId != material->GetData().petId, ErrorPet::E_NOT_DESPAWNED );
		VALID_RETURN( upgradeElem->second.materialPetLevel == 0 || material->GetData().level == upgradeElem->second.materialPetLevel, ErrorPet::E_INVALID_LEVEL );
		VALID_RETURN( petPtr->GetElem()->tier == material->GetElem()->tier, ErrorPet::E_INVALID_LEVEL );
		VALID_RETURN( material->GetElem()->useType != PetInfoElem::GROW, ErrorPet::E_MATERIAL_ERROR );

		VALID_RETURN(0 == material->GetData().growthSlotNum, ErrorPet::E_GROWTH_SLOT_REGISTERED_PET);

		materialType.push_back(material->GetData().type);
		materialLevel.push_back(material->GetData().level);
	}
	
	UInt64Vector removedPets;
	removedPets.insert( removedPets.begin(), materialId.begin(), materialId.end() );
	removedPets.push_back( id.unique );

	PetData data;
	m_manager.CreateData( petPtr->GetElem()->nextPetIndex, 0, data );


	ItemBag bag(*owner);


	bag.PushTargetInvenRange(InvenWithStorages);

	 
	VALID_RETURN( ErrorItem::SUCCESS == owner->GetInventoryAction().RemoveByIndex(ranges, bag, upgradeElem->second.itemIndex, upgradeElem->second.itemAmount), ErrorPet::E_TRANSACION_FAILED);

	TransactionPtr transPtr = std::make_shared<ItemPetTierTransaction >(__FUNCTION__, __LINE__, owner, LogCode::E_ITEM_TIER_UPGRADE_PET, id, data
		, removedPets, materialType, materialLevel
		, upgradeElem->second.zenAmount, upgradeElem->second.greenGenAmount);

	transPtr->Bind( MoneyProcedure(owner,
		DbChangedMoney( owner->GetCharId(), upgradeElem->second.zenAmount, ChangedMoney::SUB, ChangedMoney::ZEN ) ) );

	transPtr->Bind( MoneyProcedure(owner,
		DbChangedMoney( owner->GetCharId(), upgradeElem->second.greenGenAmount, ChangedMoney::SUB, ChangedMoney::GREENZEN ) ) );

	VALID_RETURN(bag.Write( transPtr ), ErrorPet::E_TRANSACION_FAILED);

	Bool result = TransactionSystem::Register( transPtr );
	VALID_RETURN(result, ErrorPet::E_TRANSACION_FAILED);

	Int32 bufferIdx = m_manager.RemovedBuffer( removedPets );

	static_pointer_cast<ItemPetTierTransaction>(transPtr)->SetBufferIdx( bufferIdx );
	
	return ErrorPet::SUCCESS;
}


ErrorPet::Error ActionPlayerPetManage::AddFellow(const PetId& id, const Byte slotNumber)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), ErrorPet::playerNotFound);

	PetPtr petPtr = m_manager.Get(id);
	VALID_RETURN(petPtr, ErrorPet::E_NOT_FOUND_PET_ID);
	VALID_RETURN(m_spawnedPet != petPtr, ErrorPet::E_NOT_FOUND_PET_ID);

	auto it = m_fellowSlots.find(slotNumber);
	VALID_RETURN(it != m_fellowSlots.end(), ErrorPet::E_NOT_FOUND_PET_ID);
	VALID_RETURN(NULL == it->second , ErrorPet::E_NOT_FOUND_PET_ID);
	VALID_RETURN( petPtr->GetElem()->useType == PetInfoElem::NORMAL, ErrorPet::E_PET_INFO_ELEM );

	changeBonusOption(true, PetBonusOptionType::PET_FELLOW, petPtr->GetElem()->fellowType, petPtr->GetElem()->grade, petPtr->GetElem()->tier);

	petPtr->ChangeSlotNumber(slotNumber);
	it->second = petPtr;

	updateDbPetInfo(petPtr);
	calcCombatPower();

	EzcResGamePetChangeFellow* res = NEW EzcResGamePetChangeFellow;
	res->petId			  = id.unique;
	res->fellowSlotNumber = slotNumber;
	SERVER.SendToClient(owner, EventPtr(res));

	return ErrorPet::SUCCESS;
}

ErrorPet::Error ActionPlayerPetManage::DelFellow(const PetId& id, const Int32 removeIndex/* = 0*/ )
{
	PetPtr petPtr = m_manager.Get(id);

	if (petPtr == nullptr)
	{
		petPtr = m_manager.GetRemovedPet( removeIndex, id );
	}

	VALID_RETURN(petPtr, ErrorPet::E_NOT_FOUND_PET_ID);

	auto it = m_fellowSlots.find(petPtr->GetData().fellowSlotNumber);

	VALID_RETURN(it != m_fellowSlots.end(), ErrorPet::E_NOT_FOUND_PET_ID);
	VALID_RETURN(it->second, ErrorPet::E_NOT_FOUND_PET_ID);

	changeBonusOption(false, PetBonusOptionType::PET_FELLOW, petPtr->GetElem()->fellowType, petPtr->GetElem()->grade, petPtr->GetElem()->tier);

	petPtr->ChangeSlotNumber(0);
	it->second = nullptr;

	updateDbPetInfo(petPtr);
	calcCombatPower();

	EzcResGamePetChangeFellow* res = NEW EzcResGamePetChangeFellow;
	res->petId = id.unique;
	res->fellowSlotNumber = 0;
	SERVER.SendToClient(GetOwnerPlayer(), EventPtr(res));

	return ErrorPet::SUCCESS;
}

ErrorPet::Error ActionPlayerPetManage::ExpandFellowSlot(Byte size /*= 0*/)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), ErrorPet::playerNotFound);

	if (size == 0)
	{
		size = m_playerPet.m_maxFellowSlot + 1;
	}

	const auto& shopPack = SCRIPTS.GetPetShopPack();
	
	ShopPacks shopPacks;
	for (const auto& fellowSlot : shopPack.fellowSlots)
	{
		if (fellowSlot.slotNumber > size)
		{
			break;
		}

		if ( false == fellowSlot.isPay)
		{
			continue;
		}

		if (m_fellowSlots.end() != m_fellowSlots.find(fellowSlot.slotNumber))
		{
			continue;
		}

		std::copy(fellowSlot.shopPacks.begin(), fellowSlot.shopPacks.end(), std::back_inserter(shopPacks));
	}

	VALID_RETURN(shopPacks.size(), ErrorPet::E_FAILED_INSERT);
	
	ItemBag bag(*owner);
	VALID_RETURN(owner->GetInventoryAction().Pay(shopPacks, bag, nullptr), ErrorPet::E_NOT_ENOUGH_TOKEN);

	expandFellowSlot(size);
	updateDbPetPlayerInfo();

	return ErrorPet::SUCCESS;
}

ErrorPet::Error ActionPlayerPetManage::AddGrowthPet(const PetId& petId, const Byte slotNumber)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), ErrorPet::playerNotFound);

	PetPtr petPtr = m_manager.Get(petId);
	VALID_RETURN(petPtr, ErrorPet::E_NOT_FOUND_PET);

	ActionPlayerMembership& actMembership = owner->GetMembershipAction();
	const PetXMLScript* petXMLScript = SCRIPTS.GetScript<PetXMLScript>();
	UInt16 maxSlotCount = static_cast<UInt16>(petXMLScript->petGrowthSlotBasicCount + actMembership.GetValueAtBenefitType(EMembershipBenefitType::eValue_PetGrowthOpenSlot));

	VALID_RETURN((slotNumber >= 1 && maxSlotCount >= slotNumber), ErrorPet::E_PET_GROWTH_SLOT_COUNT_EXCEED_LIMIT);
	VALID_RETURN(m_petGrowthSlotList.size() < maxSlotCount, ErrorPet::E_PET_GROWTH_SLOT_COUNT_EXCEED_LIMIT);
	VALID_RETURN(m_petGrowthSlotList.find(slotNumber) == m_petGrowthSlotList.end(), ErrorPet::E_INVALID_PET_GROWTH_SLOT_NUMBER);

	m_petGrowthSlotList.emplace(slotNumber, petPtr);

	petPtr->ChangeGrowthSlotNumber(slotNumber);
	updateDbPetInfo(petPtr);

	EzcResPetGrowthRegister* res = NEW EzcResPetGrowthRegister;
	res->petID = petId.unique;
	res->slotIndex = slotNumber;
	owner->SendToClient(EventPtr(res));

	return ErrorPet::SUCCESS;
}

#ifdef __Hotfix_Delete_GrowthPet_by_kangms_2019_2_7
ErrorPet::Error ActionPlayerPetManage::DeleteGrowthPet(const PetId& petId, const Int32 removeIndex)
#else//__Hotfix_Delete_GrowthPet_by_kangms_2019_2_7
ErrorPet::Error ActionPlayerPetManage::DeleteGrowthPet(const PetId& petId, const Byte removeIndex)
#endif//__Hotfix_Delete_GrowthPet_by_kangms_2019_2_7
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), ErrorPet::playerNotFound);

	PetPtr petPtr = m_manager.Get(petId);

	if (petPtr == nullptr)
	{
		petPtr = m_manager.GetRemovedPet(removeIndex, petId);
	}

	VALID_RETURN(petPtr, ErrorPet::E_NOT_FOUND_PET);

	auto it = m_petGrowthSlotList.find(petPtr->GetData().growthSlotNum);

	VALID_RETURN(it != m_petGrowthSlotList.end(), ErrorPet::E_NOT_REGISTER_PET_GROWTH_SLOT_NUMBER);
	VALID_RETURN(it->second, ErrorPet::E_EMPTY_PET_GROWTH_SLOT);

	m_petGrowthSlotList.erase(it);

	petPtr->ChangeGrowthSlotNumber(0);

	updateDbPetInfo(petPtr);

	EzcResPetGrowthRegister* res = NEW EzcResPetGrowthRegister;
	res->petID = petId.unique;
	res->slotIndex = 0;
	owner->SendToClient(EventPtr(res));

	return ErrorPet::SUCCESS;
}

void ActionPlayerPetManage::AddGrowPoint(const UInt32 point)
{
	/*
	m_growPoint += point;

	EzcNtfGamePetChangeGrowPoint* ntf = NEW EzcNtfGamePetChangeGrowPoint;
	ntf->petGrowPoint = m_growPoint;
	SERVER.SendToWorld(GetOwnerPlayer(), EventPtr(ntf));

	updateDbPetPlayerInfo();
	*/
	point;
}

void ActionPlayerPetManage::DelGrowPoint(const UInt32 point)
{
	/*
	m_growPoint = std::max(0, (Int32)(m_growPoint - point));

	EzcNtfGamePetChangeGrowPoint* ntf = NEW EzcNtfGamePetChangeGrowPoint;
	ntf->petGrowPoint = m_growPoint;
	SERVER.SendToWorld(GetOwnerPlayer(), EventPtr(ntf));

	updateDbPetPlayerInfo();
	*/
	point;
}

void ActionPlayerPetManage::GetPetInfoList(OUT PetList& petList)
{
	m_manager.GetPetList(OUT petList);
}

void ActionPlayerPetManage::GetPetQuestInfo(OUT PetQuestData& petQuest)
{
	m_playerPet.GetPetQuestInfo(OUT petQuest);

	ActionPlayerMembership* actMembership = GetEntityAction(GetOwnerPlayer());
	const PetXMLScript* petXMLScript = SCRIPTS.GetScript<PetXMLScript>();

	int maxPlayCount = std::max<int>(petXMLScript->petQuestBasicPlayCount - m_playerPet.m_petQuestPlayCount, 0);
	maxPlayCount += static_cast<int>(actMembership->GetMembershipUsableCount(EMembershipBenefitType::eDaily_PetQuestCount, 0));

	petQuest.petQuestPlayableCount = static_cast<Byte>(maxPlayCount);
}

Bool ActionPlayerPetManage::CheckAndUpdatePetQuestInfoReset()
{
	DateTimeEx currDateTime = DateTime::GetPresentTime();

	const PetXMLScript* petXMLScript = SCRIPTS.GetScript<PetXMLScript>();

	// 펫 모험 초기화 시간을 경과 했는지 체크
	if ( petXMLScript->IsPetQuestResetTime(m_playerPet.m_petQuestResetDate) ) {

		// 펫 모험 초기화 및 DB 갱신
		ResetPetQuest();

		return true;
	}

	return false;
}

void ActionPlayerPetManage::UpdatePetQuestSummonStateAll()
{
	DateTimeEx currDateTime = DateTime::GetPresentTime();

	PetList petList;
	m_manager.GetPetList(OUT petList);

	for (auto pet : petList) {

		if (static_cast<Byte>(PetQuestElem::QuestStateType::Used) == pet.petQuestUseState) {

			PetPtr petPtr = m_manager.Get(pet.petId);
			petPtr->SetPetQuestUseState(PetQuestElem::QuestStateType::Unused);
			updateDbPetInfo(petPtr);

			ENtfPetSyncInfo *ntfPetSync = NEW ENtfPetSyncInfo;
			ntfPetSync->petData = petPtr->GetData();

			SERVER.SendToClient(GetOwnerPlayer(), EventPtr(ntfPetSync));
		}
	}

}

Bool ActionPlayerPetManage::IsChangeablePetQuestInfo(PetQuestElem::QuestOpenType openType)
{
	if (   PetQuestElem::QuestOpenType::First == openType
		&& 0 != m_playerPet.m_petLastQuestIndex
	) {
		return false;
	}

	return true;
}

ErrorPet::Error ActionPlayerPetManage::CheckStartablePetQuest(std::vector<UInt64> summonPetIDList, OUT PetList& summonPetList)
{
	// 선택된 펫 모험 정보 체크
	if (0 == m_playerPet.m_petLastQuestIndex) {
		return ErrorPet::E_PET_QUEST_NOT_SELECTED;
	}
	else {
		if (static_cast<Byte>(PetQuestElem::QuestResultType::Success) == m_playerPet.m_petLastQuestState) {
			return ErrorPet::E_ALREADY_SUCCESSED_PET_QUEST;
		}
	}
	
	// 펫 모험 가능 횟수 체크
	ActionPlayerMembership* actMembership = GetEntityAction(GetOwnerPlayer());
	const PetXMLScript* petXMLScript = SCRIPTS.GetScript<PetXMLScript>();

	UInt16 membershipUsableCount = static_cast<UInt16>(actMembership->GetMembershipUsableCount(EMembershipBenefitType::eDaily_PetQuestCount, 0));
	if ( 0 >= membershipUsableCount
		 && (petXMLScript->petQuestBasicPlayCount <= m_playerPet.m_petQuestPlayCount)
	) {
		MU2_WARN_LOG( LogCategory::CONTENTS
					, "Failed to start PetQuest cause Playable Count limit over !!! : MemberShipCount:%d, MaxBasicCount:%d <= CurrPlayCount:%d - CharID%d"
			        , membershipUsableCount, petXMLScript->petQuestBasicPlayCount, m_playerPet.m_petQuestPlayCount
					, GetOwnerPlayer()->GetCharId()
		);
		return ErrorPet::E_PET_QUEST_PLAY_COUNT_EXCEED_LIMIT;
	}

	// 펫 모험 소환수 개수 제한 체크
	if (summonPetIDList.size() > PetQuestElem::MAX_PET_QUEST_SUMMON_PET_COUNT) {
		return ErrorPet::E_PET_QUEST_MAX_SUMMON_PET_COUNT_EXCEED_LIMIT;
	}

	std::set<UInt64> petList;

	// 펫 모험 소환수 목록 설정
	for (auto petID : summonPetIDList) {

		// 펫 소환 중복 체크
		auto itFound = petList.find(petID);
		if (petList.end() != itFound) {
			return ErrorPet::E_ALREADY_REGISTERED_PET;
		}
		petList.emplace(petID);

		PetPtr petPtr = this->GetPet(PetUnique(petID));
		if (nullptr == petPtr) {
			return ErrorPet::E_NOT_FOUND_PET;
		}
		// 펫 모험 소환 상태 체크
		if (static_cast<Byte>(PetQuestElem::QuestStateType::Used) == petPtr->GetData().petQuestUseState) {
			return ErrorPet::E_ALREADY_SUMMONED_PET;
		}

		summonPetList.emplace_back(petPtr->GetData());
	}

	return ErrorPet::SUCCESS;
}

ErrorPet::Error ActionPlayerPetManage::StartPetQuest(PetList& summonPetList)
{
	const PetQuestScript* petQuestScript = SCRIPTS.GetScript<PetQuestScript>();
	const PetQuestElem* petQuestElem = petQuestScript->GetPetQuestElem(m_playerPet.m_petLastQuestIndex);
	if (nullptr == petQuestElem) {
		return ErrorPet::E_NOT_FOUND_PET_SCRIPT;
	}

	// 펫 모험 소환수 경험치 계산 및 소환 상태 설정 및 DB 갱신
	UInt16 hasLeaderPetCount = 0;
	UInt32 hasOtherPetTotalPoint = 0;
	for (auto pet : summonPetList) {
		PetPtr petPtr = m_manager.Get(pet.petId);

		if (petPtr->GetElem()->petGroupID == petQuestElem->m_leaderPet) {
			++hasLeaderPetCount;
		}
		else {
			hasOtherPetTotalPoint += petPtr->GetElem()->petPoint;
		}

		petPtr->SetPetQuestUseState(PetQuestElem::QuestStateType::Used);
		updateDbPetInfo(petPtr);

		ENtfPetSyncInfo *ntfPetSync = NEW ENtfPetSyncInfo;
		ntfPetSync->petData = petPtr->GetData();

		// 펫 모험 소환수 목록 소환 상태 통지
		SERVER.SendToClient(GetOwnerPlayer(), EventPtr(ntfPetSync));
	}

	PetQuestData petQuest;
	this->GetPetQuestInfo(OUT petQuest);

	// 펫 모험 보내기 로그 작성
	{
		EReqLogDb2* log = NEW EReqLogDb2;
		EventPtr logEventPtr(log);

		EventSetter::FillLogForEntity(LogCode::E_PET_QUEST_SEND, GetOwnerPlayer(), log->action);
		log->action.var32s.push_back(static_cast<Int32>(m_playerPet.m_petLastQuestIndex));
		log->action.var32s.push_back(static_cast<Int32>(petQuestElem->m_leaderPet));
		log->action.var32s.push_back(static_cast<Int32>(petQuestElem->m_demandExp));
		log->action.var32s.push_back(static_cast<Int32>(m_playerPet.m_petQuestPlayCount));
		log->action.var32s.push_back(static_cast<Int32>(petQuest.petQuestPlayableCount));
		for (UInt32 i = 0; i < summonPetList.size(); ++i) {
			log->action.var32s.push_back(static_cast<Int32>(summonPetList[i].type));
			log->action.var64s.push_back(static_cast<Int64>(summonPetList[i].petId.unique));
		}
		for (UInt32 i = 0; i < PetQuestElem::MAX_PET_QUEST_SUMMON_PET_COUNT - summonPetList.size(); ++i) {
			log->action.var32s.push_back(0);
			log->action.var64s.push_back(0);
		}

		SendDataCenter(logEventPtr);
	}

	SimpleItems rewardItemList;

	// 펫 모험 완료 요구 경험치 계산
	const UInt32 giveTotalExp = petQuestScript->CalculatePetQuestClearExp( m_playerPet.m_petLastQuestIndex
	                                                                     , hasLeaderPetCount
																         , hasOtherPetTotalPoint
	);

	// 펫 모험 완료 경험치 체크
	if (petQuestElem->m_demandExp > giveTotalExp) {
		m_playerPet.m_petLastQuestState = static_cast<Byte>(PetQuestElem::QuestResultType::Failure);

		MU2_INFO_LOG( LogCategory::CONTENTS
					, "Lost PetQuest !!! : ReqExp:%d > GiveExp:%d - EC:%d, CharID:%d"
			        , petQuestElem->m_demandExp, giveTotalExp
					, ErrorPet::E_PET_QUEST_NOT_ENOUGH_EXP
			        , GetOwnerPlayer()->GetCharId()
		);
	}
	else {
		// 펫 모험 플레이 횟수 갱신
		m_playerPet.m_petLastQuestState = static_cast<Byte>(PetQuestElem::QuestResultType::Success);
		m_playerPet.m_petQuestPlayCount += 1;

		MU2_INFO_LOG( LogCategory::CONTENTS
					, "Success PetQuest : ReqExp:%d < GiveExp:%d, PetQuestIndex:%d - PetQuestPlayCount:%d, CharID:%d"
				    , petQuestElem->m_demandExp, giveTotalExp, petQuestElem->m_petQuestIndex
					, m_playerPet.m_petQuestPlayCount
			        , GetOwnerPlayer()->GetCharId()
		);

		ActionPlayerMembership* actMembership = GetEntityAction(GetOwnerPlayer());
		const PetXMLScript* petXMLScript = SCRIPTS.GetScript<PetXMLScript>();

		// 펫 모험 보내기 기본 사용 횟수 초과일 경우
		// 맴버쉽을 통해 사용하는 것으로 처리 !!!
		if (m_playerPet.m_petQuestPlayCount > petXMLScript->petQuestBasicPlayCount) {
			// 펫 모험 보내기 횟수를 맴버쉽 정보 갱신
			actMembership->UseMembershipCount(EMembershipBenefitType::eDaily_PetQuestCount, 0, 1);
		}

		// 펫 모험 완료 아이템 보상 - 우편 발송
		ActionPlayerMailBox *actPlayerMailBox = GetEntityAction(GetOwnerPlayer());

		MailData mail;
		mail.senderName = L"Msg_Mailbox_PetQuest_SENDER";
		mail.subject = L"Msg_Mailbox_PetQuest_SUBJECT";
		mail.content = L"Msg_Mailbox_PetQuest_CONTENT";

		for (auto &reward : petQuestElem->m_rewardList) {
			SimpleItem simpleItem;
			simpleItem.itemIndex = reward.m_rewardItemIndex;
			simpleItem.itemCount = reward.m_rewardCount;
			rewardItemList.push_back(simpleItem);
		}

		ErrorMail::Error errorMail = actPlayerMailBox->SendSystemMail(mail, rewardItemList);
		if (ErrorMail::SUCCESS != errorMail) {
			MU2_ERROR_LOG( LogCategory::CONTENTS
						 , "Failed to send Mail for PetQuest Reward !!! : ErrorCode:%d, PetQuestIndex:%d - CharID:%d"
						 , errorMail, petQuestElem->m_petQuestIndex
						 , GetOwnerPlayer()->GetCharId()
			);
		}
	}

	updateDbPetPlayerInfo();

	this->GetPetQuestInfo(OUT petQuest);

	// 펫 모험 결과 로그 작성
	{
		EReqLogDb2* log = NEW EReqLogDb2;
		EventPtr logEventPtr(log);

		EventSetter::FillLogForEntity(LogCode::E_PET_QUEST_RESULT, GetOwnerPlayer(), log->action);
		log->action.var32s.push_back(static_cast<Int32>(m_playerPet.m_petLastQuestIndex));
		log->action.var32s.push_back(static_cast<Int32>(m_playerPet.m_petLastQuestState));
		log->action.var32s.push_back(static_cast<Int32>(petQuestElem->m_demandExp));
		log->action.var32s.push_back(static_cast<Int32>(giveTotalExp));
		log->action.var32s.push_back(static_cast<Int32>(m_playerPet.m_petQuestPlayCount));
		log->action.var32s.push_back(static_cast<Int32>(petQuest.petQuestPlayableCount));
		for (auto& reward : rewardItemList) {
			log->action.var32s.push_back(static_cast<Int32>(reward.itemIndex));
			log->action.var32s.push_back(static_cast<Int32>(reward.itemCount));
		}
		for (UInt32 i = 0; i < PetQuestElem::MAX_PET_QUEST_REWARD_COUNT - rewardItemList.size(); ++i) {
			log->action.var32s.push_back(0);
			log->action.var32s.push_back(0);
		}

		SendDataCenter(logEventPtr);
	}

	EzcResPetQuestStart* resSucces = NEW EzcResPetQuestStart;
	resSucces->result = ErrorPet::SUCCESS;
	resSucces->petQuestData = petQuest;
	SERVER.SendToClient(this->GetOwnerPlayer(), EventPtr(resSucces));

	return ErrorPet::SUCCESS;
}


void ActionPlayerPetManage::RollbankDbTrans( const Int32 bufferIdx )
{
	m_manager.RollBack( bufferIdx );
}

void ActionPlayerPetManage::UnApply( const Int32 bufferIdx, __out Byte &sendType, __out Byte &followerIdx )
{
	PetBuffer &buffer = m_manager.GetRemovedBuffer();

	followerIdx = 0;
	sendType = 0;
	auto iter = buffer.find( bufferIdx );
	if (iter == buffer.end())
		return;

	for(auto pet : iter->second)
	{
		if (pet == m_spawnedPet)
		{
			PetSpawnState state;
			state.petUnique = pet->GetData().petId.unique;
			state.state = PetSpawnState::DESPAWN;
			UpdateSpawnState( state, bufferIdx);

			sendType = 1;
		}

		auto fellowSlotNumber = pet->GetData().fellowSlotNumber;
		if (fellowSlotNumber > 0)
		{
			DelFellow( pet->GetData().petId, bufferIdx );
			followerIdx = fellowSlotNumber;
			sendType = 2;
		}

#ifdef __Hotfix_Delete_GrowthPet_by_kangms_2019_2_7
		this->DeleteGrowthPet(pet->GetPetId(), bufferIdx);
#else//__Hotfix_Delete_GrowthPet_by_kangms_2019_2_7
		this->DeleteGrowthPet(pet->GetPetId());
#endif//__Hotfix_Delete_GrowthPet_by_kangms_2019_2_7

		this->DeletePetToRegion(pet);
	}

	m_manager.Commit( bufferIdx );
}

void ActionPlayerPetManage::Apply( Byte sendType, Byte followerIdx, PetId newPetId )
{
	switch (sendType)
	{
	case 1:
		{
			PetSpawnState state;
			state.petUnique = newPetId.unique;
			state.state = PetSpawnState::SPAWN;
			UpdateSpawnState( state );
		}
	break;
	case 2:
		{
			AddFellow( newPetId, followerIdx );
		}
	break;
	}
}

void ActionPlayerPetManage::CheckLevelupAchievement( const PetData& petData )
{
	EntityPlayer* owner = GetOwnerPlayer();

	auto aa = owner->GetAction<ActionPlayerAchievement>();
	{
		AchievementPetLevelUpEvent achievement( owner, petData );
		aa->OnEvent( &achievement );
	}

	{
		AchievementPetLevelupSuccessEvent achievement( owner );
		aa->OnEvent( &achievement );
	}
}

void ActionPlayerPetManage::CheckPointupAchievement(UInt32 point)
{
	EntityPlayer* owner = GetOwnerPlayer();

	auto aa = owner->GetAction<ActionPlayerAchievement>();
	{
		AchievementPetPointupSuccessEvent achievement( owner, point);
		aa->OnEvent(&achievement);
	}
}

void ActionPlayerPetManage::SendPetData( const PetData& petData )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	SendSystemMsg(player, SystemMessage( L"sys", L"Msg_pet_levelup" ) );

	ENtfPetSyncInfo* ntf = NEW ENtfPetSyncInfo;
	ntf->petData = petData;
	SERVER.SendToClient(player, EventPtr( ntf ) );

	ActionPlayerPetManage& actPet = player->GetPetManageAction();
	EntityNpc* pet = actPet.GetPetSpawnedEntity();
	if (pet)
	{
		if (actPet.GetSpawnedPetId().unique == petData.petId.unique)
		{
			ENtfPetLevelUp* ntfLevelUp = NEW ENtfPetLevelUp;
			ntfLevelUp->petEntityId = pet->GetId();
			player->GetSectorAction().NearGridCast( EventPtr( ntfLevelUp ) );
		}
	}
}

void ActionPlayerPetManage::UpdatePetInfoAll()
{
	ActionPlayerMembership* actMembership = GetEntityAction(GetOwnerPlayer());
	const PetXMLScript* petXMLScript = SCRIPTS.GetScript<PetXMLScript>();
	UInt16 maxSlotCount = static_cast<UInt16>(petXMLScript->petGrowthSlotBasicCount + actMembership->GetValueAtBenefitType(EMembershipBenefitType::eValue_PetGrowthOpenSlot));

	for ( auto itPos = m_petGrowthSlotList.begin();
	 	  itPos != m_petGrowthSlotList.end(); ) {

		PetPtr petPtr = itPos->second;
		VALID_DO(petPtr, continue);

		if (petPtr->GetData().growthSlotNum > maxSlotCount) {
			petPtr->ChangeGrowthSlotNumber(0);
			this->updateDbPetInfo(petPtr);

			ENtfPetSyncInfo* ntf = NEW ENtfPetSyncInfo;
			ntf->petData = petPtr->GetData();
			SERVER.SendToClient(this->GetOwnerPlayer(), EventPtr(ntf));

			itPos = m_petGrowthSlotList.erase(itPos);
		}
		else {
			++itPos;
		}
	}
}

ErrorPet::Error ActionPlayerPetManage::pay(PetPtr pet, PetData& data, PetUpdateType type, const ShopPacks& petShopPack)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), ErrorPet::playerNotFound);

	VALID_RETURN(pet, ErrorPet::E_ERROR);

	TransactionPtr transPtr( NEW ItemPetUpdateTransaction(__FUNCTION__, __LINE__, owner, LogCode::E_ITEM_PET_UPDATE, data, type) );

	ItemBag bag(*owner);
	VALID_RETURN(owner->GetInventoryAction().Pay(petShopPack, bag, transPtr), ErrorPet::E_NOT_ENOUGH_TOKEN);

	if ( 0 >= bag.GetSize() )
	{
		UpdateResult(data, type);
	}

	return ErrorPet::SUCCESS;
}

void ActionPlayerPetManage::UpdateResult(const PetData& data, PetUpdateType type)
{
	switch (type)
	{
	case PetUpdateType::NONE:
		MU2_ASSERT(false);
		break;
	case PetUpdateType::RENAME:
		RenameResult(data);
		break;
	default:
		break;
	}
}

void ActionPlayerPetManage::FillUpPlayerPetDataList( CharacterInfo& charInfo )
{
	if ( false == m_spawnedPet )
	{
		return;
	}

	charInfo.petData = m_spawnedPet->GetData();

	for (const auto& i : m_fellowSlots)
	{
		if (i.second)
		{
			charInfo.fellowPetDataList.emplace_back(i.second->GetData());
		}
	}
}

void ActionPlayerPetManage::calcCombatPower()
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	auto getBasePetCombatPower = [this]()
	{
		UInt32 basePetCombatPower = 0;
		if (m_spawnedPet)
		{
			basePetCombatPower += m_spawnedPet->GetElem()->combatBase;
		}

		for (const auto& fellowPet : m_fellowSlots)
		{
			auto fellowPetPtr = fellowPet.second;
			if (nullptr == fellowPetPtr)
			{
				continue;
			}
			basePetCombatPower += fellowPetPtr->GetElem()->combatBase;
		}
		return basePetCombatPower;
	};

	auto getTotalPetCombatPower = [this]()
	{
		UInt32 combatPower = 0;
		auto combatPowerBonusOptionElem = SCRIPTS.GetScript<PetBonusOptionScript>()->Get(PetBonusOptionType::PET_POWER, m_basePetCombatPower, 0, 0);
		if (combatPowerBonusOptionElem)
		{
			combatPower += combatPowerBonusOptionElem->combatPower;
		}

		if (m_spawnedPet)
		{
			combatPower += m_spawnedPet->GetElem()->combatBase;
		}

		for (const auto& fellowPet : m_fellowSlots)
		{
			auto fellowPetPtr = fellowPet.second;
			if (nullptr == fellowPetPtr)
			{
				continue;
			}

			auto fellowBonusOptionElem = SCRIPTS.GetScript<PetBonusOptionScript>()->Get(PetBonusOptionType::PET_FELLOW, fellowPetPtr->GetElem()->fellowType, fellowPetPtr->GetLevelElem()->grade, fellowPetPtr->GetLevelElem()->tier);
			if (nullptr == fellowBonusOptionElem)
			{
				continue;
			}

			combatPower += fellowBonusOptionElem->combatPower;
		}
		return combatPower;
	};

	UInt32 basePetCombatPower = getBasePetCombatPower();
	if (m_basePetCombatPower != basePetCombatPower)
	{
		changeBonusOption(false, PetBonusOptionType::PET_POWER, m_basePetCombatPower);
		changeBonusOption(true,  PetBonusOptionType::PET_POWER, basePetCombatPower);

		m_basePetCombatPower = basePetCombatPower;
	}

	player->SetPetCombatPower(getTotalPetCombatPower());
	player->GetCombatPowerAction().UpdateCombatPower(CombatPowerType::PET);
	player->GetAbilityAction().Calculate();
	return;
}

void ActionPlayerPetManage::GetPetIds( UInt64Vector &pIds )
{
	m_manager.GetIds( pIds );
}

PetPtr ActionPlayerPetManage::GetPet( PetUnique petUnique ) const
{
	return m_manager.Get( PetId(petUnique) );
}


EntityNpc* ActionPlayerPetManage::GetPetSpawnedEntity() const
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner /*&& owner->IsValid()*/, NULL);//소멸자에서 호출되어서 IsValid를 주석처리

	VALID_RETURN(m_spawnedPet && m_spawnedPet->GetSpawnedNpcId(), NULL);

	Sector* sector = owner->GetSector();
	VERIFY_RETURN(sector, NULL);

	EntityNpc* spawnedEntity = sector->FindNpc( m_spawnedPet->GetSpawnedNpcId() );
	VALID_RETURN(spawnedEntity && spawnedEntity->IsValid(), NULL);

	return spawnedEntity;

}

PetId ActionPlayerPetManage::GetSpawnedPetId() const
{
	if (m_spawnedPet != nullptr)
		return m_spawnedPet->GetData().petId;

	return PetId(0);
}

void ActionPlayerPetManage::expandFellowSlot(const Byte size, bool isSend /*true*/ )
{
	m_playerPet.m_maxFellowSlot = size;
	for (Byte i = 1; i <= size; ++i)
	{
		auto it = m_fellowSlots.find(i);
		if (it != m_fellowSlots.end())
		{
			continue;
		}

		m_fellowSlots.emplace(i, nullptr);
	}

	if (isSend)
	{
		EResGamePetExpandFellowSlot* res = NEW EResGamePetExpandFellowSlot;
		res->maxSlotNumber = size;
		SERVER.SendToClient( GetOwnerPlayer(), EventPtr( res ) );
	}
}

Bool ActionPlayerPetManage::changeBonusOption(Bool apply, const PetBonusOptionType bonusType, const UInt32 typeValue, const Byte fellowGrade /*= 0*/, const Byte fellowTier/*= 0*/)
{
	auto bonusOptionElem = SCRIPTS.GetScript<PetBonusOptionScript>()->Get(bonusType, typeValue, fellowGrade, fellowTier );
	if (nullptr == bonusOptionElem)
	{
		return false;
	}

#ifdef __Patch_Pet_Bonus_Reward_update_by_kangms_2019_3_18
	AbilityParam param;
	param.isOnOff = true;

	for (auto& itPair : bonusOptionElem->optionList) {

		param.type = itPair.second.type;
		param.section = EffectSection::PERCENT;

		if (itPair.second.point != 0) {
			param.section = EffectSection::POINT;
#ifdef ABILITY_RENEWAL_20180508
			param.value = itPair.second.point;
#else
			param.value = itPair.second.base + itPair.second.point;
#endif
		}
		else if (itPair.second.percent != 0) {
			param.section = EffectSection::PERCENT;
			param.value = itPair.second.percent;
		}

		param.value *= (apply) ? 1 : -1;
		auto act = GetOwnerPlayer()->GetAction<ActionAbility>();

		act->ChangedSkillAbility(param);
	}

#else//__Patch_Pet_Bonus_Reward_update_by_kangms_2019_3_18
	AbilityParam param;
	param.isOnOff = true;

	param.type = bonusOptionElem->option.type;
	param.section = EffectSection::PERCENT;
	if (bonusOptionElem->option.point != 0) {
		param.section = EffectSection::POINT;
#ifdef ABILITY_RENEWAL_20180508
		param.value = bonusOptionElem->option.point;
#else
		param.value = bonusOptionElem->option.base + bonusOptionElem->option.point;
#endif
	}
	else if (bonusOptionElem->option.percent != 0) {
		param.section = EffectSection::PERCENT;
		param.value = bonusOptionElem->option.percent;
	}

	param.value *= (apply) ? 1 : -1;
	auto act = GetOwnerPlayer()->GetAction<ActionAbility>();

	act->ChangedSkillAbility(param);

#endif//__Patch_Pet_Bonus_Reward_update_by_kangms_2019_3_18

	return true;
}

void ActionPlayerPetManage::updateDbPetPlayerInfo() const
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	auto attr = player->GetAttribute<AttributePlayer>();

	EReqDbUpdatePlayerPetInfo* req = NEW EReqDbUpdatePlayerPetInfo;
	req->charId = attr->charId;
	req->currentSpawnPet = m_playerPet.m_currentSpawnPetIndex;
	req->petLastQuestIndex = m_playerPet.m_petLastQuestIndex;
	req->petLastQuestState = m_playerPet.m_petLastQuestState;
	req->petQuestPlayCount = m_playerPet.m_petQuestPlayCount;
	req->petQuestResetDate = m_playerPet.m_petQuestResetDate;
	req->maxFellowSlot = m_playerPet.m_maxFellowSlot;

	SERVER.SendToDb(player, EventPtr(req));
}

void ActionPlayerPetManage::updateDbPetInfo(PetPtr pet) const
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	EReqDbUpdatePet* req = NEW EReqDbUpdatePet;
	req->petData = pet->GetData();
	SERVER.SendToDb( player, EventPtr( req ) );
}

void ActionPlayerPetManage::SaveSpawnePetDb()
{
	if (m_spawnedPet) {
		updateDbPetInfo( m_spawnedPet );
	}

	for (auto itPair : m_petGrowthSlotList) 
	{
		VALID_DO(itPair.second, continue);
		this->updateDbPetInfo(itPair.second);
	}
}

void ActionPlayerPetManage::UpdateDbPetBoosterSkillList()
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	EReqDbUpdatePetBoosterSkillList* req = NEW EReqDbUpdatePetBoosterSkillList;
	req->petBoosterSkillIDList = m_playerPet.m_petBoosterSkillIDList;

	SERVER.SendToDb(player, EventPtr(req));
}

Bool ActionPlayerPetManage::checkFellowPet( PetPtr pet )
{
	if (pet == nullptr)
		return false;

	for (auto iter : m_fellowSlots)
	{
		if (iter.second == nullptr)
			continue;

		if (iter.second->GetData().petId == pet->GetData().petId)
			return false;
	}

	return true;
}

}
