; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	?IsInstance@Execution@mu2@@UEBA_NXZ		; mu2::Execution::IsInstance
PUBLIC	?IsWorldcross@Execution@mu2@@UEBA_NXZ		; mu2::Execution::IsWorldcross
PUBLIC	?IsTournamentEnter@Execution@mu2@@UEBA_NXZ	; mu2::Execution::IsTournamentEnter
PUBLIC	?IsWorldcrossing@Execution@mu2@@UEBA_NXZ	; mu2::Execution::IsWorldcrossing
PUBLIC	?IsSing<PERSON>@Execution@mu2@@UEBA_NXZ		; mu2::Execution::IsSingle
PUBLIC	?IsGroup@Execution@mu2@@UEBA_NXZ		; mu2::Execution::IsGroup
PUBLIC	?IsNull@Execution@mu2@@UEBA_NXZ			; mu2::Execution::IsNull
PUBLIC	?GetDamageMeterKey@Execution@mu2@@UEBA?BIXZ	; mu2::Execution::GetDamageMeterKey
PUBLIC	?IsChannel@Channel@mu2@@UEBA_NXZ		; mu2::Channel::IsChannel
PUBLIC	?CanUseFoothold@Channel@mu2@@UEBA_NXZ		; mu2::Channel::CanUseFoothold
PUBLIC	?IsValid@Channel@mu2@@UEBA?B_NXZ		; mu2::Channel::IsValid
PUBLIC	??0ChannelFieldpoint@mu2@@QEAA@PEAVManagerExecutionBase@1@AEAUExecutionCreateInfo@1@@Z ; mu2::ChannelFieldpoint::ChannelFieldpoint
PUBLIC	??1ChannelFieldpoint@mu2@@UEAA@XZ		; mu2::ChannelFieldpoint::~ChannelFieldpoint
PUBLIC	??_GChannelFieldpoint@mu2@@UEAAPEAXI@Z		; mu2::ChannelFieldpoint::`scalar deleting destructor'
PUBLIC	?changemapDo@ChannelFieldPointController@mu2@@MEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@@Z ; mu2::ChannelFieldPointController::changemapDo
PUBLIC	??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@ ; `string'
PUBLIC	??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@		; `string'
PUBLIC	??_C@_06BALNJMNP@player@			; `string'
PUBLIC	??_7ChannelFieldpoint@mu2@@6B@			; mu2::ChannelFieldpoint::`vftable'
PUBLIC	?inst@?$ISingleton@VFieldPointManager@mu2@@@mu2@@1VFieldPointManager@2@A ; mu2::ISingleton<mu2::FieldPointManager>::inst
PUBLIC	?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A ; mu2::ISingleton<mu2::ExecutionManager>::inst
PUBLIC	??_C@_0FA@MKGOCPEI@F?3?2Release_Branch?2Server?2Develo@ ; `string'
PUBLIC	??_C@_0CO@MKDIPEJO@mu2?3?3ChannelFieldPointControlle@ ; `string'
PUBLIC	??_C@_0P@DFHMFPIE@toPositionElem@		; `string'
PUBLIC	??_C@_0EC@GFLIAFOP@execlFieldPoint?4IsChannel?$CI?$CJ?5?$CG?$CG?5@ ; `string'
PUBLIC	??_C@_0BN@PLNLCFGM@eError?5?$DN?$DN?5ErrorJoin?3?3SUCCESS@ ; `string'
PUBLIC	??_C@_0DF@OFOBMJJH@pProperChannel?5?$CG?$CG?5pProperChanne@ ; `string'
PUBLIC	??_R4ChannelFieldpoint@mu2@@6B@			; mu2::ChannelFieldpoint::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVChannelFieldpoint@mu2@@@8		; mu2::ChannelFieldpoint `RTTI Type Descriptor'
PUBLIC	??_R3ChannelFieldpoint@mu2@@8			; mu2::ChannelFieldpoint::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2ChannelFieldpoint@mu2@@8			; mu2::ChannelFieldpoint::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@ChannelFieldpoint@mu2@@8		; mu2::ChannelFieldpoint::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@Channel@mu2@@8			; mu2::Channel::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R0?AVChannel@mu2@@@8				; mu2::Channel `RTTI Type Descriptor'
PUBLIC	??_R3Channel@mu2@@8				; mu2::Channel::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2Channel@mu2@@8				; mu2::Channel::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@Execution@mu2@@8			; mu2::Execution::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R0?AVExecution@mu2@@@8			; mu2::Execution `RTTI Type Descriptor'
PUBLIC	??_R3Execution@mu2@@8				; mu2::Execution::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2Execution@mu2@@8				; mu2::Execution::`RTTI Base Class Array'
EXTRN	??3@YAXPEAX_K@Z:PROC				; operator delete
EXTRN	atexit:PROC
EXTRN	__imp_GetStdHandle:PROC
EXTRN	__imp_SetConsoleTextAttribute:PROC
EXTRN	?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ:PROC	; mu2::Logger::Logging
EXTRN	?GetZoneIndex@ExecutionZoneId@mu2@@QEBAGXZ:PROC	; mu2::ExecutionZoneId::GetZoneIndex
EXTRN	?TryJoin@Execution@mu2@@QEBA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@W4Enum@JoinContext@2@AEBVVector3@2@HPEBUInstanceDungeonInfo@2@@Z:PROC ; mu2::Execution::TryJoin
EXTRN	?OnDestroyedFailed@Execution@mu2@@UEAAXXZ:PROC	; mu2::Execution::OnDestroyedFailed
EXTRN	?OnCreatedFailed@Execution@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::Execution::OnCreatedFailed
EXTRN	?OnResGameReady@Execution@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::Execution::OnResGameReady
EXTRN	?EnterExecutionPost@Execution@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::Execution::EnterExecutionPost
EXTRN	?OnShutdownZone@Execution@mu2@@UEAAXXZ:PROC	; mu2::Execution::OnShutdownZone
EXTRN	?SendReqJoinExecution@Execution@mu2@@UEBA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@@Z:PROC ; mu2::Execution::SendReqJoinExecution
EXTRN	?IsValid@Execution@mu2@@UEBA?B_NXZ:PROC		; mu2::Execution::IsValid
EXTRN	?IsDestroyable@Execution@mu2@@UEBA_NXZ:PROC	; mu2::Execution::IsDestroyable
EXTRN	?GetRelayServerId@Execution@mu2@@UEBA?BIXZ:PROC	; mu2::Execution::GetRelayServerId
EXTRN	?GetToPositionElem@ExecutionController@mu2@@QEBAPEBUPositionElem@2@XZ:PROC ; mu2::ExecutionController::GetToPositionElem
EXTRN	?GetToIndexZone@ExecutionController@mu2@@QEBAGXZ:PROC ; mu2::ExecutionController::GetToIndexZone
EXTRN	?GetCharId@EntityPlayer@mu2@@QEBAIXZ:PROC	; mu2::EntityPlayer::GetCharId
EXTRN	?GetCurrExecId@EntityPlayer@mu2@@QEBAAEBVExecutionZoneId@2@XZ:PROC ; mu2::EntityPlayer::GetCurrExecId
EXTRN	?GetCurrExecution@EntityPlayer@mu2@@QEBAAEBVExecution@2@XZ:PROC ; mu2::EntityPlayer::GetCurrExecution
EXTRN	??0Channel@mu2@@QEAA@PEAVManagerExecutionBase@1@AEAUExecutionCreateInfo@1@@Z:PROC ; mu2::Channel::Channel
EXTRN	??1Channel@mu2@@UEAA@XZ:PROC			; mu2::Channel::~Channel
EXTRN	?IsRunning@Channel@mu2@@UEBA_NXZ:PROC		; mu2::Channel::IsRunning
EXTRN	?Init@Channel@mu2@@UEAA_NAEAUExecutionCreateInfo@2@@Z:PROC ; mu2::Channel::Init
EXTRN	?ToString@Channel@mu2@@UEBA?BV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ:PROC ; mu2::Channel::ToString
EXTRN	?IsJoinable@Channel@mu2@@UEBA_NPEAVEntityPlayer@2@@Z:PROC ; mu2::Channel::IsJoinable
EXTRN	?EnterExecutionPrev@Channel@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::Channel::EnterExecutionPrev
EXTRN	?EnterExecution@Channel@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::Channel::EnterExecution
EXTRN	?ExitExecutionPost@Channel@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::Channel::ExitExecutionPost
EXTRN	?OnCreatedSucceed@Channel@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::Channel::OnCreatedSucceed
EXTRN	?OnDestroyed@Channel@mu2@@UEAAXXZ:PROC		; mu2::Channel::OnDestroyed
EXTRN	?Update@Channel@mu2@@UEAAXXZ:PROC		; mu2::Channel::Update
EXTRN	?Fillup@Channel@mu2@@UEAAXAEAUChannelInfo@2@@Z:PROC ; mu2::Channel::Fillup
EXTRN	?Fillup@Channel@mu2@@UEAAXAEAPEAUEwzReqCreateExecution@2@@Z:PROC ; mu2::Channel::Fillup
EXTRN	?Fillup@Channel@mu2@@UEAAXAEAPEAUEwzReqDestroyExecution@2@@Z:PROC ; mu2::Channel::Fillup
EXTRN	?Fillup@Channel@mu2@@UEBAXAEAVJoinZoneContextDest@2@@Z:PROC ; mu2::Channel::Fillup
EXTRN	?Fillup@Channel@mu2@@MEBA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAPEAUEwzReqJoinExecution@2@@Z:PROC ; mu2::Channel::Fillup
EXTRN	??_EChannelFieldpoint@mu2@@UEAAPEAXI@Z:PROC	; mu2::ChannelFieldpoint::`vector deleting destructor'
EXTRN	??1FieldPointManager@mu2@@UEAA@XZ:PROC		; mu2::FieldPointManager::~FieldPointManager
EXTRN	?IsEnterable@FieldPointManager@mu2@@QEAA_NGI@Z:PROC ; mu2::FieldPointManager::IsEnterable
EXTRN	??0FieldPointManager@mu2@@AEAA@XZ:PROC		; mu2::FieldPointManager::FieldPointManager
EXTRN	??1ExecutionManager@mu2@@UEAA@XZ:PROC		; mu2::ExecutionManager::~ExecutionManager
EXTRN	?FindProperChannelWithPartyChannel@ExecutionManager@mu2@@QEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@GAEAPEAVChannel@2@@Z:PROC ; mu2::ExecutionManager::FindProperChannelWithPartyChannel
EXTRN	??0ExecutionManager@mu2@@AEAA@XZ:PROC		; mu2::ExecutionManager::ExecutionManager
EXTRN	??_7type_info@@6B@:BYTE				; type_info::`vftable'
;	COMDAT ?inst@?$ISingleton@VFieldPointManager@mu2@@@mu2@@1VFieldPointManager@2@A
_BSS	SEGMENT
?inst@?$ISingleton@VFieldPointManager@mu2@@@mu2@@1VFieldPointManager@2@A DB 018H DUP (?) ; mu2::ISingleton<mu2::FieldPointManager>::inst
_BSS	ENDS
;	COMDAT ?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A
_BSS	SEGMENT
?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A DB 01e0H DUP (?) ; mu2::ISingleton<mu2::ExecutionManager>::inst
_BSS	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?IsValid@Channel@mu2@@UEBA?B_NXZ DD imagerel $LN5
	DD	imagerel $LN5+62
	DD	imagerel $unwind$?IsValid@Channel@mu2@@UEBA?B_NXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0ChannelFieldpoint@mu2@@QEAA@PEAVManagerExecutionBase@1@AEAUExecutionCreateInfo@1@@Z DD imagerel $LN4
	DD	imagerel $LN4+64
	DD	imagerel $unwind$??0ChannelFieldpoint@mu2@@QEAA@PEAVManagerExecutionBase@1@AEAUExecutionCreateInfo@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1ChannelFieldpoint@mu2@@UEAA@XZ DD imagerel $LN4
	DD	imagerel $LN4+40
	DD	imagerel $unwind$??1ChannelFieldpoint@mu2@@UEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GChannelFieldpoint@mu2@@UEAAPEAXI@Z DD imagerel $LN5
	DD	imagerel $LN5+60
	DD	imagerel $unwind$??_GChannelFieldpoint@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?changemapDo@ChannelFieldPointController@mu2@@MEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@@Z DD imagerel $LN16
	DD	imagerel $LN16+1359
	DD	imagerel $unwind$?changemapDo@ChannelFieldPointController@mu2@@MEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__E?inst@?$ISingleton@VFieldPointManager@mu2@@@mu2@@1VFieldPointManager@2@A@@YAXXZ DD imagerel ??__E?inst@?$ISingleton@VFieldPointManager@mu2@@@mu2@@1VFieldPointManager@2@A@@YAXXZ
	DD	imagerel ??__E?inst@?$ISingleton@VFieldPointManager@mu2@@@mu2@@1VFieldPointManager@2@A@@YAXXZ+34
	DD	imagerel $unwind$??__E?inst@?$ISingleton@VFieldPointManager@mu2@@@mu2@@1VFieldPointManager@2@A@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__F?inst@?$ISingleton@VFieldPointManager@mu2@@@mu2@@1VFieldPointManager@2@A@@YAXXZ DD imagerel ??__F?inst@?$ISingleton@VFieldPointManager@mu2@@@mu2@@1VFieldPointManager@2@A@@YAXXZ
	DD	imagerel ??__F?inst@?$ISingleton@VFieldPointManager@mu2@@@mu2@@1VFieldPointManager@2@A@@YAXXZ+22
	DD	imagerel $unwind$??__F?inst@?$ISingleton@VFieldPointManager@mu2@@@mu2@@1VFieldPointManager@2@A@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__E?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ DD imagerel ??__E?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ
	DD	imagerel ??__E?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ+34
	DD	imagerel $unwind$??__E?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__F?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ DD imagerel ??__F?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ
	DD	imagerel ??__F?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ+22
	DD	imagerel $unwind$??__F?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ
pdata	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
??inst$initializer$@?$ISingleton@VFieldPointManager@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA DQ FLAT:??__E?inst@?$ISingleton@VFieldPointManager@mu2@@@mu2@@1VFieldPointManager@2@A@@YAXXZ ; ??inst$initializer$@?$ISingleton@VFieldPointManager@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA
CRT$XCU	ENDS
;	COMDAT ??_R2Execution@mu2@@8
rdata$r	SEGMENT
??_R2Execution@mu2@@8 DD imagerel ??_R1A@?0A@EA@Execution@mu2@@8 ; mu2::Execution::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3Execution@mu2@@8
rdata$r	SEGMENT
??_R3Execution@mu2@@8 DD 00H				; mu2::Execution::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2Execution@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVExecution@mu2@@@8
data$rs	SEGMENT
??_R0?AVExecution@mu2@@@8 DQ FLAT:??_7type_info@@6B@	; mu2::Execution `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVExecution@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R1A@?0A@EA@Execution@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@Execution@mu2@@8 DD imagerel ??_R0?AVExecution@mu2@@@8 ; mu2::Execution::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3Execution@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2Channel@mu2@@8
rdata$r	SEGMENT
??_R2Channel@mu2@@8 DD imagerel ??_R1A@?0A@EA@Channel@mu2@@8 ; mu2::Channel::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@Execution@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3Channel@mu2@@8
rdata$r	SEGMENT
??_R3Channel@mu2@@8 DD 00H				; mu2::Channel::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2Channel@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVChannel@mu2@@@8
data$rs	SEGMENT
??_R0?AVChannel@mu2@@@8 DQ FLAT:??_7type_info@@6B@	; mu2::Channel `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVChannel@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R1A@?0A@EA@Channel@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@Channel@mu2@@8 DD imagerel ??_R0?AVChannel@mu2@@@8 ; mu2::Channel::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3Channel@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@ChannelFieldpoint@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@ChannelFieldpoint@mu2@@8 DD imagerel ??_R0?AVChannelFieldpoint@mu2@@@8 ; mu2::ChannelFieldpoint::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	02H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3ChannelFieldpoint@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2ChannelFieldpoint@mu2@@8
rdata$r	SEGMENT
??_R2ChannelFieldpoint@mu2@@8 DD imagerel ??_R1A@?0A@EA@ChannelFieldpoint@mu2@@8 ; mu2::ChannelFieldpoint::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@Channel@mu2@@8
	DD	imagerel ??_R1A@?0A@EA@Execution@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3ChannelFieldpoint@mu2@@8
rdata$r	SEGMENT
??_R3ChannelFieldpoint@mu2@@8 DD 00H			; mu2::ChannelFieldpoint::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	03H
	DD	imagerel ??_R2ChannelFieldpoint@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVChannelFieldpoint@mu2@@@8
data$rs	SEGMENT
??_R0?AVChannelFieldpoint@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::ChannelFieldpoint `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVChannelFieldpoint@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4ChannelFieldpoint@mu2@@6B@
rdata$r	SEGMENT
??_R4ChannelFieldpoint@mu2@@6B@ DD 01H			; mu2::ChannelFieldpoint::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVChannelFieldpoint@mu2@@@8
	DD	imagerel ??_R3ChannelFieldpoint@mu2@@8
	DD	imagerel ??_R4ChannelFieldpoint@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_C@_0DF@OFOBMJJH@pProperChannel?5?$CG?$CG?5pProperChanne@
CONST	SEGMENT
??_C@_0DF@OFOBMJJH@pProperChannel?5?$CG?$CG?5pProperChanne@ DB 'pProperCh'
	DB	'annel && pProperChannel->IsJoinable(player)', 00H ; `string'
CONST	ENDS
;	COMDAT ??_C@_0BN@PLNLCFGM@eError?5?$DN?$DN?5ErrorJoin?3?3SUCCESS@
CONST	SEGMENT
??_C@_0BN@PLNLCFGM@eError?5?$DN?$DN?5ErrorJoin?3?3SUCCESS@ DB 'eError == '
	DB	'ErrorJoin::SUCCESS', 00H			; `string'
CONST	ENDS
;	COMDAT ??_C@_0EC@GFLIAFOP@execlFieldPoint?4IsChannel?$CI?$CJ?5?$CG?$CG?5@
CONST	SEGMENT
??_C@_0EC@GFLIAFOP@execlFieldPoint?4IsChannel?$CI?$CJ?5?$CG?$CG?5@ DB 'ex'
	DB	'eclFieldPoint.IsChannel() && execlFieldPoint.IsJoinable(playe'
	DB	'r)', 00H					; `string'
CONST	ENDS
;	COMDAT ??_C@_0P@DFHMFPIE@toPositionElem@
CONST	SEGMENT
??_C@_0P@DFHMFPIE@toPositionElem@ DB 'toPositionElem', 00H ; `string'
CONST	ENDS
;	COMDAT ??_C@_0CO@MKDIPEJO@mu2?3?3ChannelFieldPointControlle@
CONST	SEGMENT
??_C@_0CO@MKDIPEJO@mu2?3?3ChannelFieldPointControlle@ DB 'mu2::ChannelFie'
	DB	'ldPointController::changemapDo', 00H	; `string'
CONST	ENDS
;	COMDAT ??_C@_0FA@MKGOCPEI@F?3?2Release_Branch?2Server?2Develo@
CONST	SEGMENT
??_C@_0FA@MKGOCPEI@F?3?2Release_Branch?2Server?2Develo@ DB 'F:\Release_Br'
	DB	'anch\Server\Development\Frontend\WorldServer\ChannelFieldpoin'
	DB	't.cpp', 00H					; `string'
CONST	ENDS
;	COMDAT ??_7ChannelFieldpoint@mu2@@6B@
CONST	SEGMENT
??_7ChannelFieldpoint@mu2@@6B@ DQ FLAT:??_R4ChannelFieldpoint@mu2@@6B@ ; mu2::ChannelFieldpoint::`vftable'
	DQ	FLAT:??_EChannelFieldpoint@mu2@@UEAAPEAXI@Z
	DQ	FLAT:?OnDestroyed@Channel@mu2@@UEAAXXZ
	DQ	FLAT:?OnDestroyedFailed@Execution@mu2@@UEAAXXZ
	DQ	FLAT:?OnCreatedSucceed@Channel@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?OnCreatedFailed@Execution@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?OnResGameReady@Execution@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?EnterExecutionPrev@Channel@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?EnterExecution@Channel@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?EnterExecutionPost@Execution@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?ExitExecutionPost@Channel@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?Init@Channel@mu2@@UEAA_NAEAUExecutionCreateInfo@2@@Z
	DQ	FLAT:?Update@Channel@mu2@@UEAAXXZ
	DQ	FLAT:?OnShutdownZone@Execution@mu2@@UEAAXXZ
	DQ	FLAT:?SendReqJoinExecution@Execution@mu2@@UEBA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@@Z
	DQ	FLAT:?IsInstance@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsChannel@Channel@mu2@@UEBA_NXZ
	DQ	FLAT:?IsWorldcross@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsTournamentEnter@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsWorldcrossing@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsSingle@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsGroup@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsNull@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsValid@Channel@mu2@@UEBA?B_NXZ
	DQ	FLAT:?IsRunning@Channel@mu2@@UEBA_NXZ
	DQ	FLAT:?IsJoinable@Channel@mu2@@UEBA_NPEAVEntityPlayer@2@@Z
	DQ	FLAT:?IsDestroyable@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?GetDamageMeterKey@Execution@mu2@@UEBA?BIXZ
	DQ	FLAT:?ToString@Channel@mu2@@UEBA?BV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ
	DQ	FLAT:?GetRelayServerId@Execution@mu2@@UEBA?BIXZ
	DQ	FLAT:?Fillup@Channel@mu2@@MEBA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAPEAUEwzReqJoinExecution@2@@Z
	DQ	FLAT:?Fillup@Channel@mu2@@UEBAXAEAVJoinZoneContextDest@2@@Z
	DQ	FLAT:?Fillup@Channel@mu2@@UEAAXAEAPEAUEwzReqDestroyExecution@2@@Z
	DQ	FLAT:?Fillup@Channel@mu2@@UEAAXAEAPEAUEwzReqCreateExecution@2@@Z
	DQ	FLAT:?CanUseFoothold@Channel@mu2@@UEBA_NXZ
	DQ	FLAT:?Fillup@Channel@mu2@@UEAAXAEAUChannelInfo@2@@Z
CONST	ENDS
;	COMDAT ??_C@_06BALNJMNP@player@
CONST	SEGMENT
??_C@_06BALNJMNP@player@ DB 'player', 00H		; `string'
CONST	ENDS
;	COMDAT ??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
CONST	SEGMENT
??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@ DB 'g', 00H, 'a', 00H, 'm', 00H, 'e'
	DB	00H, 00H, 00H				; `string'
CONST	ENDS
;	COMDAT ??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
CONST	SEGMENT
??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@ DB '%'
	DB	's> ASSERT - %s, %s(%d)', 00H		; `string'
CONST	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__F?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__E?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__F?inst@?$ISingleton@VFieldPointManager@mu2@@@mu2@@1VFieldPointManager@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__E?inst@?$ISingleton@VFieldPointManager@mu2@@@mu2@@1VFieldPointManager@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?changemapDo@ChannelFieldPointController@mu2@@MEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@@Z DD 021101H
	DD	0190111H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GChannelFieldpoint@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1ChannelFieldpoint@mu2@@UEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0ChannelFieldpoint@mu2@@QEAA@PEAVManagerExecutionBase@1@AEAUExecutionCreateInfo@1@@Z DD 011301H
	DD	04213H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?IsValid@Channel@mu2@@UEBA?B_NXZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
??inst$initializer$@?$ISingleton@VExecutionManager@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA DQ FLAT:??__E?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ ; ??inst$initializer$@?$ISingleton@VExecutionManager@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA
CRT$XCU	ENDS
; Function compile flags: /Odtp
;	COMDAT ??__F?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ
text$yd	SEGMENT
??__F?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ PROC ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::ExecutionManager>::inst'', COMDAT
  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A ; mu2::ISingleton<mu2::ExecutionManager>::inst
  0000b	e8 00 00 00 00	 call	 ??1ExecutionManager@mu2@@UEAA@XZ ; mu2::ExecutionManager::~ExecutionManager
  00010	90		 npad	 1
  00011	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00015	c3		 ret	 0
??__F?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ ENDP ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::ExecutionManager>::inst''
text$yd	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??__E?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ
text$di	SEGMENT
??__E?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ PROC ; `dynamic initializer for 'mu2::ISingleton<mu2::ExecutionManager>::inst'', COMDAT

; 90   : template<typename T> T ISingleton<T>::inst;

  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A ; mu2::ISingleton<mu2::ExecutionManager>::inst
  0000b	e8 00 00 00 00	 call	 ??0ExecutionManager@mu2@@AEAA@XZ ; mu2::ExecutionManager::ExecutionManager
  00010	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??__F?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::ExecutionManager>::inst''
  00017	e8 00 00 00 00	 call	 atexit
  0001c	90		 npad	 1
  0001d	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00021	c3		 ret	 0
??__E?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ ENDP ; `dynamic initializer for 'mu2::ISingleton<mu2::ExecutionManager>::inst''
text$di	ENDS
; Function compile flags: /Odtp
;	COMDAT ??__F?inst@?$ISingleton@VFieldPointManager@mu2@@@mu2@@1VFieldPointManager@2@A@@YAXXZ
text$yd	SEGMENT
??__F?inst@?$ISingleton@VFieldPointManager@mu2@@@mu2@@1VFieldPointManager@2@A@@YAXXZ PROC ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::FieldPointManager>::inst'', COMDAT
  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?inst@?$ISingleton@VFieldPointManager@mu2@@@mu2@@1VFieldPointManager@2@A ; mu2::ISingleton<mu2::FieldPointManager>::inst
  0000b	e8 00 00 00 00	 call	 ??1FieldPointManager@mu2@@UEAA@XZ ; mu2::FieldPointManager::~FieldPointManager
  00010	90		 npad	 1
  00011	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00015	c3		 ret	 0
??__F?inst@?$ISingleton@VFieldPointManager@mu2@@@mu2@@1VFieldPointManager@2@A@@YAXXZ ENDP ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::FieldPointManager>::inst''
text$yd	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??__E?inst@?$ISingleton@VFieldPointManager@mu2@@@mu2@@1VFieldPointManager@2@A@@YAXXZ
text$di	SEGMENT
??__E?inst@?$ISingleton@VFieldPointManager@mu2@@@mu2@@1VFieldPointManager@2@A@@YAXXZ PROC ; `dynamic initializer for 'mu2::ISingleton<mu2::FieldPointManager>::inst'', COMDAT

; 90   : template<typename T> T ISingleton<T>::inst;

  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?inst@?$ISingleton@VFieldPointManager@mu2@@@mu2@@1VFieldPointManager@2@A ; mu2::ISingleton<mu2::FieldPointManager>::inst
  0000b	e8 00 00 00 00	 call	 ??0FieldPointManager@mu2@@AEAA@XZ ; mu2::FieldPointManager::FieldPointManager
  00010	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??__F?inst@?$ISingleton@VFieldPointManager@mu2@@@mu2@@1VFieldPointManager@2@A@@YAXXZ ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::FieldPointManager>::inst''
  00017	e8 00 00 00 00	 call	 atexit
  0001c	90		 npad	 1
  0001d	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00021	c3		 ret	 0
??__E?inst@?$ISingleton@VFieldPointManager@mu2@@@mu2@@1VFieldPointManager@2@A@@YAXXZ ENDP ; `dynamic initializer for 'mu2::ISingleton<mu2::FieldPointManager>::inst''
text$di	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ChannelFieldpoint.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ChannelFieldpoint.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ChannelFieldpoint.cpp
;	COMDAT ?changemapDo@ChannelFieldPointController@mu2@@MEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@@Z
_TEXT	SEGMENT
toZoneIndex$ = 96
eError$ = 100
execlFieldPoint$1 = 104
toPositionElem$ = 112
pProperChannel$ = 120
hConsole$2 = 128
hConsole$3 = 136
hConsole$4 = 144
hConsole$5 = 152
hConsole$6 = 160
$T7 = 168
$T8 = 176
this$ = 208
player$ = 216
?changemapDo@ChannelFieldPointController@mu2@@MEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@@Z PROC ; mu2::ChannelFieldPointController::changemapDo, COMDAT

; 26   : 	{

$LN16:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 81 ec c8 00
	00 00		 sub	 rsp, 200		; 000000c8H

; 27   : 		VERIFY_RETURN(player, ErrorJoin::playerNotFound);

  00011	48 83 bc 24 d8
	00 00 00 00	 cmp	 QWORD PTR player$[rsp], 0
  0001a	0f 85 ac 00 00
	00		 jne	 $LN2@changemapD
  00020	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  00025	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  0002b	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR hConsole$2[rsp], rax
  00033	66 ba 0d 00	 mov	 dx, 13
  00037	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR hConsole$2[rsp]
  0003f	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  00045	c7 44 24 50 1b
	00 00 00	 mov	 DWORD PTR [rsp+80], 27
  0004d	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FA@MKGOCPEI@F?3?2Release_Branch?2Server?2Develo@
  00054	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  00059	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_06BALNJMNP@player@
  00060	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  00065	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CO@MKDIPEJO@mu2?3?3ChannelFieldPointControlle@
  0006c	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  00071	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  00078	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  0007d	c7 44 24 28 1b
	00 00 00	 mov	 DWORD PTR [rsp+40], 27
  00085	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FA@MKGOCPEI@F?3?2Release_Branch?2Server?2Develo@
  0008c	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  00091	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CO@MKDIPEJO@mu2?3?3ChannelFieldPointControlle@
  00098	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  0009e	ba 02 00 00 00	 mov	 edx, 2
  000a3	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  000aa	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  000af	66 ba 07 00	 mov	 dx, 7
  000b3	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR hConsole$2[rsp]
  000bb	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000c1	90		 npad	 1
  000c2	b8 01 1b 06 00	 mov	 eax, 400129		; 00061b01H
  000c7	e9 7b 04 00 00	 jmp	 $LN1@changemapD
$LN2@changemapD:

; 28   : 
; 29   : 		const PositionElem* toPositionElem = GetToPositionElem();

  000cc	48 8b 8c 24 d0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  000d4	e8 00 00 00 00	 call	 ?GetToPositionElem@ExecutionController@mu2@@QEBAPEBUPositionElem@2@XZ ; mu2::ExecutionController::GetToPositionElem
  000d9	48 89 44 24 70	 mov	 QWORD PTR toPositionElem$[rsp], rax

; 30   : 		VERIFY_RETURN(toPositionElem, ErrorJoin::invalidPositionElem);

  000de	48 83 7c 24 70
	00		 cmp	 QWORD PTR toPositionElem$[rsp], 0
  000e4	0f 85 ac 00 00
	00		 jne	 $LN3@changemapD
  000ea	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  000ef	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  000f5	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR hConsole$3[rsp], rax
  000fd	66 ba 0d 00	 mov	 dx, 13
  00101	48 8b 8c 24 88
	00 00 00	 mov	 rcx, QWORD PTR hConsole$3[rsp]
  00109	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  0010f	c7 44 24 50 1e
	00 00 00	 mov	 DWORD PTR [rsp+80], 30
  00117	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FA@MKGOCPEI@F?3?2Release_Branch?2Server?2Develo@
  0011e	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  00123	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0P@DFHMFPIE@toPositionElem@
  0012a	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  0012f	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CO@MKDIPEJO@mu2?3?3ChannelFieldPointControlle@
  00136	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  0013b	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  00142	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  00147	c7 44 24 28 1e
	00 00 00	 mov	 DWORD PTR [rsp+40], 30
  0014f	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FA@MKGOCPEI@F?3?2Release_Branch?2Server?2Develo@
  00156	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  0015b	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CO@MKDIPEJO@mu2?3?3ChannelFieldPointControlle@
  00162	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  00168	ba 02 00 00 00	 mov	 edx, 2
  0016d	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  00174	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  00179	66 ba 07 00	 mov	 dx, 7
  0017d	48 8b 8c 24 88
	00 00 00	 mov	 rcx, QWORD PTR hConsole$3[rsp]
  00185	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  0018b	90		 npad	 1
  0018c	b8 20 1b 06 00	 mov	 eax, 400160		; 00061b20H
  00191	e9 b1 03 00 00	 jmp	 $LN1@changemapD
$LN3@changemapD:

; 31   : 
; 32   : 		IndexZone toZoneIndex = GetToIndexZone();

  00196	48 8b 8c 24 d0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0019e	e8 00 00 00 00	 call	 ?GetToIndexZone@ExecutionController@mu2@@QEBAGXZ ; mu2::ExecutionController::GetToIndexZone
  001a3	66 89 44 24 60	 mov	 WORD PTR toZoneIndex$[rsp], ax

; 33   : 
; 34   : 		//=======================================================================
; 35   : 		// 필드기여도맵 -> 필드기여도맵 - 기존 채널 그대로 사용
; 36   : 		//=======================================================================
; 37   : 
; 38   : 		if (player->GetCurrExecId().GetZoneIndex() == toZoneIndex)

  001a8	48 8b 8c 24 d8
	00 00 00	 mov	 rcx, QWORD PTR player$[rsp]
  001b0	e8 00 00 00 00	 call	 ?GetCurrExecId@EntityPlayer@mu2@@QEBAAEBVExecutionZoneId@2@XZ ; mu2::EntityPlayer::GetCurrExecId
  001b5	48 8b c8	 mov	 rcx, rax
  001b8	e8 00 00 00 00	 call	 ?GetZoneIndex@ExecutionZoneId@mu2@@QEBAGXZ ; mu2::ExecutionZoneId::GetZoneIndex
  001bd	0f b7 c0	 movzx	 eax, ax
  001c0	0f b7 4c 24 60	 movzx	 ecx, WORD PTR toZoneIndex$[rsp]
  001c5	3b c1		 cmp	 eax, ecx
  001c7	0f 85 36 01 00
	00		 jne	 $LN4@changemapD

; 39   : 		{
; 40   : 			const Execution& execlFieldPoint = player->GetCurrExecution();

  001cd	48 8b 8c 24 d8
	00 00 00	 mov	 rcx, QWORD PTR player$[rsp]
  001d5	e8 00 00 00 00	 call	 ?GetCurrExecution@EntityPlayer@mu2@@QEBAAEBVExecution@2@XZ ; mu2::EntityPlayer::GetCurrExecution
  001da	48 89 44 24 68	 mov	 QWORD PTR execlFieldPoint$1[rsp], rax

; 41   : 			VERIFY_RETURN(execlFieldPoint.IsChannel() && execlFieldPoint.IsJoinable(player), ErrorJoin::NotfoundFieldPointChannel);

  001df	48 8b 44 24 68	 mov	 rax, QWORD PTR execlFieldPoint$1[rsp]
  001e4	48 8b 00	 mov	 rax, QWORD PTR [rax]
  001e7	48 8b 4c 24 68	 mov	 rcx, QWORD PTR execlFieldPoint$1[rsp]
  001ec	ff 50 78	 call	 QWORD PTR [rax+120]
  001ef	0f b6 c0	 movzx	 eax, al
  001f2	85 c0		 test	 eax, eax
  001f4	74 26		 je	 SHORT $LN6@changemapD
  001f6	48 8b 44 24 68	 mov	 rax, QWORD PTR execlFieldPoint$1[rsp]
  001fb	48 8b 00	 mov	 rax, QWORD PTR [rax]
  001fe	48 8b 94 24 d8
	00 00 00	 mov	 rdx, QWORD PTR player$[rsp]
  00206	48 8b 4c 24 68	 mov	 rcx, QWORD PTR execlFieldPoint$1[rsp]
  0020b	ff 90 c0 00 00
	00		 call	 QWORD PTR [rax+192]
  00211	0f b6 c0	 movzx	 eax, al
  00214	85 c0		 test	 eax, eax
  00216	0f 85 ac 00 00
	00		 jne	 $LN5@changemapD
$LN6@changemapD:
  0021c	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  00221	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  00227	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR hConsole$4[rsp], rax
  0022f	66 ba 0d 00	 mov	 dx, 13
  00233	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR hConsole$4[rsp]
  0023b	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  00241	c7 44 24 50 29
	00 00 00	 mov	 DWORD PTR [rsp+80], 41	; 00000029H
  00249	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FA@MKGOCPEI@F?3?2Release_Branch?2Server?2Develo@
  00250	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  00255	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EC@GFLIAFOP@execlFieldPoint?4IsChannel?$CI?$CJ?5?$CG?$CG?5@
  0025c	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  00261	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CO@MKDIPEJO@mu2?3?3ChannelFieldPointControlle@
  00268	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  0026d	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  00274	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  00279	c7 44 24 28 29
	00 00 00	 mov	 DWORD PTR [rsp+40], 41	; 00000029H
  00281	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FA@MKGOCPEI@F?3?2Release_Branch?2Server?2Develo@
  00288	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  0028d	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CO@MKDIPEJO@mu2?3?3ChannelFieldPointControlle@
  00294	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  0029a	ba 02 00 00 00	 mov	 edx, 2
  0029f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  002a6	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  002ab	66 ba 07 00	 mov	 dx, 7
  002af	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR hConsole$4[rsp]
  002b7	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  002bd	90		 npad	 1
  002be	b8 03 1b 06 00	 mov	 eax, 400131		; 00061b03H
  002c3	e9 7f 02 00 00	 jmp	 $LN1@changemapD
$LN5@changemapD:

; 42   : 
; 43   : 			return execlFieldPoint.TryJoin(player, JoinContext::JC_CHANGE_MAP, toPositionElem->pos, toPositionElem->indexPosition);

  002c8	48 8b 44 24 70	 mov	 rax, QWORD PTR toPositionElem$[rsp]
  002cd	48 83 c0 08	 add	 rax, 8
  002d1	48 c7 44 24 28
	00 00 00 00	 mov	 QWORD PTR [rsp+40], 0
  002da	48 8b 4c 24 70	 mov	 rcx, QWORD PTR toPositionElem$[rsp]
  002df	8b 49 04	 mov	 ecx, DWORD PTR [rcx+4]
  002e2	89 4c 24 20	 mov	 DWORD PTR [rsp+32], ecx
  002e6	4c 8b c8	 mov	 r9, rax
  002e9	41 b0 02	 mov	 r8b, 2
  002ec	48 8b 94 24 d8
	00 00 00	 mov	 rdx, QWORD PTR player$[rsp]
  002f4	48 8b 4c 24 68	 mov	 rcx, QWORD PTR execlFieldPoint$1[rsp]
  002f9	e8 00 00 00 00	 call	 ?TryJoin@Execution@mu2@@QEBA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@W4Enum@JoinContext@2@AEBVVector3@2@HPEBUInstanceDungeonInfo@2@@Z ; mu2::Execution::TryJoin
  002fe	e9 44 02 00 00	 jmp	 $LN1@changemapD
$LN4@changemapD:
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  00303	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VFieldPointManager@mu2@@@mu2@@1VFieldPointManager@2@A ; mu2::ISingleton<mu2::FieldPointManager>::inst
  0030a	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T7[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ChannelFieldpoint.cpp

; 51   : 		VALID_RETURN(theFieldPointManager.IsEnterable(toZoneIndex, player->GetCharId()), ErrorJoin::InvalidState);

  00312	48 8b 8c 24 d8
	00 00 00	 mov	 rcx, QWORD PTR player$[rsp]
  0031a	e8 00 00 00 00	 call	 ?GetCharId@EntityPlayer@mu2@@QEBAIXZ ; mu2::EntityPlayer::GetCharId
  0031f	48 8b 8c 24 a8
	00 00 00	 mov	 rcx, QWORD PTR $T7[rsp]
  00327	44 8b c0	 mov	 r8d, eax
  0032a	0f b7 54 24 60	 movzx	 edx, WORD PTR toZoneIndex$[rsp]
  0032f	e8 00 00 00 00	 call	 ?IsEnterable@FieldPointManager@mu2@@QEAA_NGI@Z ; mu2::FieldPointManager::IsEnterable
  00334	0f b6 c0	 movzx	 eax, al
  00337	85 c0		 test	 eax, eax
  00339	75 0a		 jne	 SHORT $LN7@changemapD
  0033b	b8 2d 1b 06 00	 mov	 eax, 400173		; 00061b2dH
  00340	e9 02 02 00 00	 jmp	 $LN1@changemapD
$LN7@changemapD:

; 52   : 
; 53   : 		Channel* pProperChannel = NULL;

  00345	48 c7 44 24 78
	00 00 00 00	 mov	 QWORD PTR pProperChannel$[rsp], 0
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  0034e	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A ; mu2::ISingleton<mu2::ExecutionManager>::inst
  00355	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T8[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ChannelFieldpoint.cpp

; 54   : 		ErrorJoin::Error eError = theExecutionManager.FindProperChannelWithPartyChannel(player, toZoneIndex, __out pProperChannel);

  0035d	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR $T8[rsp]
  00365	4c 8d 4c 24 78	 lea	 r9, QWORD PTR pProperChannel$[rsp]
  0036a	44 0f b7 44 24
	60		 movzx	 r8d, WORD PTR toZoneIndex$[rsp]
  00370	48 8b 94 24 d8
	00 00 00	 mov	 rdx, QWORD PTR player$[rsp]
  00378	48 8b c8	 mov	 rcx, rax
  0037b	e8 00 00 00 00	 call	 ?FindProperChannelWithPartyChannel@ExecutionManager@mu2@@QEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@GAEAPEAVChannel@2@@Z ; mu2::ExecutionManager::FindProperChannelWithPartyChannel
  00380	89 44 24 64	 mov	 DWORD PTR eError$[rsp], eax

; 55   : 		VERIFY_RETURN(eError == ErrorJoin::SUCCESS, eError);

  00384	83 7c 24 64 00	 cmp	 DWORD PTR eError$[rsp], 0
  00389	0f 84 ab 00 00
	00		 je	 $LN8@changemapD
  0038f	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  00394	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  0039a	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR hConsole$5[rsp], rax
  003a2	66 ba 0d 00	 mov	 dx, 13
  003a6	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR hConsole$5[rsp]
  003ae	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  003b4	c7 44 24 50 37
	00 00 00	 mov	 DWORD PTR [rsp+80], 55	; 00000037H
  003bc	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FA@MKGOCPEI@F?3?2Release_Branch?2Server?2Develo@
  003c3	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  003c8	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BN@PLNLCFGM@eError?5?$DN?$DN?5ErrorJoin?3?3SUCCESS@
  003cf	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  003d4	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CO@MKDIPEJO@mu2?3?3ChannelFieldPointControlle@
  003db	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  003e0	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  003e7	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  003ec	c7 44 24 28 37
	00 00 00	 mov	 DWORD PTR [rsp+40], 55	; 00000037H
  003f4	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FA@MKGOCPEI@F?3?2Release_Branch?2Server?2Develo@
  003fb	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  00400	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CO@MKDIPEJO@mu2?3?3ChannelFieldPointControlle@
  00407	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  0040d	ba 02 00 00 00	 mov	 edx, 2
  00412	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  00419	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  0041e	66 ba 07 00	 mov	 dx, 7
  00422	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR hConsole$5[rsp]
  0042a	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  00430	90		 npad	 1
  00431	8b 44 24 64	 mov	 eax, DWORD PTR eError$[rsp]
  00435	e9 0d 01 00 00	 jmp	 $LN1@changemapD
$LN8@changemapD:

; 56   : 		VERIFY_RETURN(pProperChannel && pProperChannel->IsJoinable(player), ErrorJoin::InvalidExecution);

  0043a	48 83 7c 24 78
	00		 cmp	 QWORD PTR pProperChannel$[rsp], 0
  00440	74 26		 je	 SHORT $LN10@changemapD
  00442	48 8b 44 24 78	 mov	 rax, QWORD PTR pProperChannel$[rsp]
  00447	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0044a	48 8b 94 24 d8
	00 00 00	 mov	 rdx, QWORD PTR player$[rsp]
  00452	48 8b 4c 24 78	 mov	 rcx, QWORD PTR pProperChannel$[rsp]
  00457	ff 90 c0 00 00
	00		 call	 QWORD PTR [rax+192]
  0045d	0f b6 c0	 movzx	 eax, al
  00460	85 c0		 test	 eax, eax
  00462	0f 85 a9 00 00
	00		 jne	 $LN9@changemapD
$LN10@changemapD:
  00468	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  0046d	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  00473	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR hConsole$6[rsp], rax
  0047b	66 ba 0d 00	 mov	 dx, 13
  0047f	48 8b 8c 24 a0
	00 00 00	 mov	 rcx, QWORD PTR hConsole$6[rsp]
  00487	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  0048d	c7 44 24 50 38
	00 00 00	 mov	 DWORD PTR [rsp+80], 56	; 00000038H
  00495	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FA@MKGOCPEI@F?3?2Release_Branch?2Server?2Develo@
  0049c	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  004a1	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0DF@OFOBMJJH@pProperChannel?5?$CG?$CG?5pProperChanne@
  004a8	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  004ad	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CO@MKDIPEJO@mu2?3?3ChannelFieldPointControlle@
  004b4	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  004b9	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  004c0	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  004c5	c7 44 24 28 38
	00 00 00	 mov	 DWORD PTR [rsp+40], 56	; 00000038H
  004cd	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FA@MKGOCPEI@F?3?2Release_Branch?2Server?2Develo@
  004d4	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  004d9	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CO@MKDIPEJO@mu2?3?3ChannelFieldPointControlle@
  004e0	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  004e6	ba 02 00 00 00	 mov	 edx, 2
  004eb	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  004f2	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  004f7	66 ba 07 00	 mov	 dx, 7
  004fb	48 8b 8c 24 a0
	00 00 00	 mov	 rcx, QWORD PTR hConsole$6[rsp]
  00503	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  00509	90		 npad	 1
  0050a	b8 0c 1b 06 00	 mov	 eax, 400140		; 00061b0cH
  0050f	eb 36		 jmp	 SHORT $LN1@changemapD
$LN9@changemapD:

; 57   : 
; 58   : 		return pProperChannel->TryJoin(player, JoinContext::JC_CHANGE_MAP, toPositionElem->pos, toPositionElem->indexPosition);

  00511	48 8b 44 24 70	 mov	 rax, QWORD PTR toPositionElem$[rsp]
  00516	48 83 c0 08	 add	 rax, 8
  0051a	48 c7 44 24 28
	00 00 00 00	 mov	 QWORD PTR [rsp+40], 0
  00523	48 8b 4c 24 70	 mov	 rcx, QWORD PTR toPositionElem$[rsp]
  00528	8b 49 04	 mov	 ecx, DWORD PTR [rcx+4]
  0052b	89 4c 24 20	 mov	 DWORD PTR [rsp+32], ecx
  0052f	4c 8b c8	 mov	 r9, rax
  00532	41 b0 02	 mov	 r8b, 2
  00535	48 8b 94 24 d8
	00 00 00	 mov	 rdx, QWORD PTR player$[rsp]
  0053d	48 8b 4c 24 78	 mov	 rcx, QWORD PTR pProperChannel$[rsp]
  00542	e8 00 00 00 00	 call	 ?TryJoin@Execution@mu2@@QEBA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@W4Enum@JoinContext@2@AEBVVector3@2@HPEBUInstanceDungeonInfo@2@@Z ; mu2::Execution::TryJoin
$LN1@changemapD:

; 59   : 	}

  00547	48 81 c4 c8 00
	00 00		 add	 rsp, 200		; 000000c8H
  0054e	c3		 ret	 0
?changemapDo@ChannelFieldPointController@mu2@@MEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@@Z ENDP ; mu2::ChannelFieldPointController::changemapDo
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??_GChannelFieldpoint@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GChannelFieldpoint@mu2@@UEAAPEAXI@Z PROC		; mu2::ChannelFieldpoint::`scalar deleting destructor', COMDAT
$LN5:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00012	e8 00 00 00 00	 call	 ??1ChannelFieldpoint@mu2@@UEAA@XZ ; mu2::ChannelFieldpoint::~ChannelFieldpoint
  00017	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  0001b	83 e0 01	 and	 eax, 1
  0001e	85 c0		 test	 eax, eax
  00020	74 10		 je	 SHORT $LN2@scalar
  00022	ba 90 00 00 00	 mov	 edx, 144		; 00000090H
  00027	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0002c	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00031	90		 npad	 1
$LN2@scalar:
  00032	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00037	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0003b	c3		 ret	 0
??_GChannelFieldpoint@mu2@@UEAAPEAXI@Z ENDP		; mu2::ChannelFieldpoint::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ChannelFieldpoint.cpp
;	COMDAT ??1ChannelFieldpoint@mu2@@UEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1ChannelFieldpoint@mu2@@UEAA@XZ PROC			; mu2::ChannelFieldpoint::~ChannelFieldpoint, COMDAT

; 18   : 	{

$LN4:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ChannelFieldpoint@mu2@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx

; 19   : 	}	

  00018	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0001d	e8 00 00 00 00	 call	 ??1Channel@mu2@@UEAA@XZ	; mu2::Channel::~Channel
  00022	90		 npad	 1
  00023	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00027	c3		 ret	 0
??1ChannelFieldpoint@mu2@@UEAA@XZ ENDP			; mu2::ChannelFieldpoint::~ChannelFieldpoint
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ChannelFieldpoint.cpp
;	COMDAT ??0ChannelFieldpoint@mu2@@QEAA@PEAVManagerExecutionBase@1@AEAUExecutionCreateInfo@1@@Z
_TEXT	SEGMENT
this$ = 48
pOwnerManager$ = 56
cInfo$ = 64
??0ChannelFieldpoint@mu2@@QEAA@PEAVManagerExecutionBase@1@AEAUExecutionCreateInfo@1@@Z PROC ; mu2::ChannelFieldpoint::ChannelFieldpoint, COMDAT

; 14   : 	{

$LN4:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 13   : 		: Channel(pOwnerManager, cInfo)		

  00013	4c 8b 44 24 40	 mov	 r8, QWORD PTR cInfo$[rsp]
  00018	48 8b 54 24 38	 mov	 rdx, QWORD PTR pOwnerManager$[rsp]
  0001d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00022	e8 00 00 00 00	 call	 ??0Channel@mu2@@QEAA@PEAVManagerExecutionBase@1@AEAUExecutionCreateInfo@1@@Z ; mu2::Channel::Channel

; 14   : 	{

  00027	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0002c	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ChannelFieldpoint@mu2@@6B@
  00033	48 89 08	 mov	 QWORD PTR [rax], rcx

; 15   : 	}

  00036	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003b	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0003f	c3		 ret	 0
??0ChannelFieldpoint@mu2@@QEAA@PEAVManagerExecutionBase@1@AEAUExecutionCreateInfo@1@@Z ENDP ; mu2::ChannelFieldpoint::ChannelFieldpoint
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Channel.h
;	COMDAT ?IsValid@Channel@mu2@@UEBA?B_NXZ
_TEXT	SEGMENT
tv74 = 32
this$ = 64
?IsValid@Channel@mu2@@UEBA?B_NXZ PROC			; mu2::Channel::IsValid, COMDAT

; 35   : 		virtual const Bool			IsValid() const override { return m_channelId > 0 && __super::IsValid(); }

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H
  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	83 b8 80 00 00
	00 00		 cmp	 DWORD PTR [rax+128], 0
  00015	76 18		 jbe	 SHORT $LN3@IsValid
  00017	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0001c	e8 00 00 00 00	 call	 ?IsValid@Execution@mu2@@UEBA?B_NXZ ; mu2::Execution::IsValid
  00021	0f b6 c0	 movzx	 eax, al
  00024	85 c0		 test	 eax, eax
  00026	74 07		 je	 SHORT $LN3@IsValid
  00028	c6 44 24 20 01	 mov	 BYTE PTR tv74[rsp], 1
  0002d	eb 05		 jmp	 SHORT $LN4@IsValid
$LN3@IsValid:
  0002f	c6 44 24 20 00	 mov	 BYTE PTR tv74[rsp], 0
$LN4@IsValid:
  00034	0f b6 44 24 20	 movzx	 eax, BYTE PTR tv74[rsp]
  00039	48 83 c4 38	 add	 rsp, 56			; 00000038H
  0003d	c3		 ret	 0
?IsValid@Channel@mu2@@UEBA?B_NXZ ENDP			; mu2::Channel::IsValid
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Channel.h
;	COMDAT ?CanUseFoothold@Channel@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?CanUseFoothold@Channel@mu2@@UEBA_NXZ PROC		; mu2::Channel::CanUseFoothold, COMDAT

; 31   : 		virtual Bool				CanUseFoothold() const { return true; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	b0 01		 mov	 al, 1
  00007	c3		 ret	 0
?CanUseFoothold@Channel@mu2@@UEBA_NXZ ENDP		; mu2::Channel::CanUseFoothold
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Channel.h
;	COMDAT ?IsChannel@Channel@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsChannel@Channel@mu2@@UEBA_NXZ PROC			; mu2::Channel::IsChannel, COMDAT

; 28   : 		virtual Bool				IsChannel() const final { return true; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	b0 01		 mov	 al, 1
  00007	c3		 ret	 0
?IsChannel@Channel@mu2@@UEBA_NXZ ENDP			; mu2::Channel::IsChannel
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?GetDamageMeterKey@Execution@mu2@@UEBA?BIXZ
_TEXT	SEGMENT
this$ = 8
?GetDamageMeterKey@Execution@mu2@@UEBA?BIXZ PROC	; mu2::Execution::GetDamageMeterKey, COMDAT

; 95   : 		virtual const DamageMeterKey	GetDamageMeterKey() const { return 0; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	33 c0		 xor	 eax, eax
  00007	c3		 ret	 0
?GetDamageMeterKey@Execution@mu2@@UEBA?BIXZ ENDP	; mu2::Execution::GetDamageMeterKey
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?IsNull@Execution@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsNull@Execution@mu2@@UEBA_NXZ PROC			; mu2::Execution::IsNull, COMDAT

; 86   : 		virtual Bool					IsNull() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsNull@Execution@mu2@@UEBA_NXZ ENDP			; mu2::Execution::IsNull
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?IsGroup@Execution@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsGroup@Execution@mu2@@UEBA_NXZ PROC			; mu2::Execution::IsGroup, COMDAT

; 85   : 		virtual Bool					IsGroup() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsGroup@Execution@mu2@@UEBA_NXZ ENDP			; mu2::Execution::IsGroup
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?IsSingle@Execution@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsSingle@Execution@mu2@@UEBA_NXZ PROC			; mu2::Execution::IsSingle, COMDAT

; 84   : 		virtual Bool					IsSingle() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsSingle@Execution@mu2@@UEBA_NXZ ENDP			; mu2::Execution::IsSingle
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?IsWorldcrossing@Execution@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsWorldcrossing@Execution@mu2@@UEBA_NXZ PROC		; mu2::Execution::IsWorldcrossing, COMDAT

; 83   : 		virtual Bool					IsWorldcrossing() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsWorldcrossing@Execution@mu2@@UEBA_NXZ ENDP		; mu2::Execution::IsWorldcrossing
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?IsTournamentEnter@Execution@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsTournamentEnter@Execution@mu2@@UEBA_NXZ PROC		; mu2::Execution::IsTournamentEnter, COMDAT

; 82   : 		virtual Bool					IsTournamentEnter() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsTournamentEnter@Execution@mu2@@UEBA_NXZ ENDP		; mu2::Execution::IsTournamentEnter
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?IsWorldcross@Execution@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsWorldcross@Execution@mu2@@UEBA_NXZ PROC		; mu2::Execution::IsWorldcross, COMDAT

; 81   : 		virtual Bool					IsWorldcross() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsWorldcross@Execution@mu2@@UEBA_NXZ ENDP		; mu2::Execution::IsWorldcross
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?IsInstance@Execution@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsInstance@Execution@mu2@@UEBA_NXZ PROC		; mu2::Execution::IsInstance, COMDAT

; 79   : 		virtual Bool					IsInstance() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsInstance@Execution@mu2@@UEBA_NXZ ENDP		; mu2::Execution::IsInstance
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ChannelFieldpoint.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ChannelFieldpoint.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
