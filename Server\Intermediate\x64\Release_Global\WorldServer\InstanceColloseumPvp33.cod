; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	?IsChannel@Execution@mu2@@UEBA_NXZ		; mu2::Execution::IsChannel
PUBLIC	?IsTournamentEnter@Execution@mu2@@UEBA_NXZ	; mu2::Execution::IsTournamentEnter
PUBLIC	?IsGroup@Execution@mu2@@UEBA_NXZ		; mu2::Execution::IsGroup
PUBLIC	?IsNull@Execution@mu2@@UEBA_NXZ			; mu2::Execution::IsNull
PUBLIC	?IsInstance@Instance@mu2@@UEBA_NXZ		; mu2::Instance::IsInstance
PUBLIC	?IsValid@Instance@mu2@@UEBA?B_NXZ		; mu2::Instance::IsValid
PUBLIC	?GetGroupKey@Instance@mu2@@UEBA?BIXZ		; mu2::Instance::GetGroupKey
PUBLIC	?IsSingle@InstanceSinglestage@mu2@@UEBA_NXZ	; mu2::InstanceSinglestage::IsSingle
PUBLIC	?IsWorldcross@InstanceWorldcross@mu2@@UEBA_NXZ	; mu2::InstanceWorldcross::IsWorldcross
PUBLIC	?EnterExecution@InstanceColosseum33@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ; mu2::InstanceColosseum33::EnterExecution
PUBLIC	??_GInstanceColosseum33@mu2@@UEAAPEAXI@Z	; mu2::InstanceColosseum33::`scalar deleting destructor'
PUBLIC	?AllocExecution@InstanceColosseum33Controller@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z ; mu2::InstanceColosseum33Controller::AllocExecution
PUBLIC	?EnterExecution@InstancePVPChaosCastle@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ; mu2::InstancePVPChaosCastle::EnterExecution
PUBLIC	??_GInstancePVPChaosCastle@mu2@@UEAAPEAXI@Z	; mu2::InstancePVPChaosCastle::`scalar deleting destructor'
PUBLIC	?AllocExecution@InstancePVPChaosCastleController@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z ; mu2::InstancePVPChaosCastleController::AllocExecution
PUBLIC	??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@ ; `string'
PUBLIC	??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@		; `string'
PUBLIC	??_7InstanceColosseum33@mu2@@6B@		; mu2::InstanceColosseum33::`vftable'
PUBLIC	??_7InstancePVPChaosCastle@mu2@@6B@		; mu2::InstancePVPChaosCastle::`vftable'
PUBLIC	??_C@_0FF@PBMKLIIH@F?3?2Release_Branch?2Server?2Develo@ ; `string'
PUBLIC	??_C@_0BN@PLNLCFGM@eError?5?$DN?$DN?5ErrorJoin?3?3SUCCESS@ ; `string'
PUBLIC	??_C@_0CJ@KGPMEDIC@mu2?3?3InstanceColosseum33?3?3Enter@ ; `string'
PUBLIC	??_C@_0CM@OEPMHGEG@mu2?3?3InstancePVPChaosCastle?3?3En@ ; `string'
PUBLIC	??_R0?AVInstanceSinglestage@mu2@@@8		; mu2::InstanceSinglestage `RTTI Type Descriptor'
PUBLIC	??_R3InstanceSinglestage@mu2@@8			; mu2::InstanceSinglestage::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2InstanceSinglestage@mu2@@8			; mu2::InstanceSinglestage::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@InstanceSinglestage@mu2@@8	; mu2::InstanceSinglestage::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@Instance@mu2@@8			; mu2::Instance::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R0?AVInstance@mu2@@@8			; mu2::Instance `RTTI Type Descriptor'
PUBLIC	??_R3Instance@mu2@@8				; mu2::Instance::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2Instance@mu2@@8				; mu2::Instance::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@Execution@mu2@@8			; mu2::Execution::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R0?AVExecution@mu2@@@8			; mu2::Execution `RTTI Type Descriptor'
PUBLIC	??_R3Execution@mu2@@8				; mu2::Execution::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2Execution@mu2@@8				; mu2::Execution::`RTTI Base Class Array'
PUBLIC	??_R4InstanceColosseum33@mu2@@6B@		; mu2::InstanceColosseum33::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVInstanceColosseum33@mu2@@@8		; mu2::InstanceColosseum33 `RTTI Type Descriptor'
PUBLIC	??_R3InstanceColosseum33@mu2@@8			; mu2::InstanceColosseum33::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2InstanceColosseum33@mu2@@8			; mu2::InstanceColosseum33::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@InstanceColosseum33@mu2@@8	; mu2::InstanceColosseum33::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@InstanceWorldcross@mu2@@8		; mu2::InstanceWorldcross::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R0?AVInstanceWorldcross@mu2@@@8		; mu2::InstanceWorldcross `RTTI Type Descriptor'
PUBLIC	??_R3InstanceWorldcross@mu2@@8			; mu2::InstanceWorldcross::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2InstanceWorldcross@mu2@@8			; mu2::InstanceWorldcross::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@InstancePvp@mu2@@8		; mu2::InstancePvp::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R0?AVInstancePvp@mu2@@@8			; mu2::InstancePvp `RTTI Type Descriptor'
PUBLIC	??_R3InstancePvp@mu2@@8				; mu2::InstancePvp::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2InstancePvp@mu2@@8				; mu2::InstancePvp::`RTTI Base Class Array'
PUBLIC	??_R4InstancePVPChaosCastle@mu2@@6B@		; mu2::InstancePVPChaosCastle::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVInstancePVPChaosCastle@mu2@@@8		; mu2::InstancePVPChaosCastle `RTTI Type Descriptor'
PUBLIC	??_R3InstancePVPChaosCastle@mu2@@8		; mu2::InstancePVPChaosCastle::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2InstancePVPChaosCastle@mu2@@8		; mu2::InstancePVPChaosCastle::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@InstancePVPChaosCastle@mu2@@8	; mu2::InstancePVPChaosCastle::`RTTI Base Class Descriptor at (0,-1,0,64)'
EXTRN	??2@YAPEAX_K@Z:PROC				; operator new
EXTRN	??3@YAXPEAX_K@Z:PROC				; operator delete
EXTRN	__imp_GetStdHandle:PROC
EXTRN	__imp_SetConsoleTextAttribute:PROC
EXTRN	?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ:PROC	; mu2::Logger::Logging
EXTRN	?OnDestroyedFailed@Execution@mu2@@UEAAXXZ:PROC	; mu2::Execution::OnDestroyedFailed
EXTRN	?OnCreatedFailed@Execution@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::Execution::OnCreatedFailed
EXTRN	?EnterExecutionPrev@Execution@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::Execution::EnterExecutionPrev
EXTRN	?OnShutdownZone@Execution@mu2@@UEAAXXZ:PROC	; mu2::Execution::OnShutdownZone
EXTRN	?IsValid@Execution@mu2@@UEBA?B_NXZ:PROC		; mu2::Execution::IsValid
EXTRN	?IsJoinable@Execution@mu2@@UEBA_NPEAVEntityPlayer@2@@Z:PROC ; mu2::Execution::IsJoinable
EXTRN	?Fillup@Execution@mu2@@MEBA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAPEAUEwzReqJoinExecution@2@@Z:PROC ; mu2::Execution::Fillup
EXTRN	?SendReqJoinExecution@Instance@mu2@@UEBA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@@Z:PROC ; mu2::Instance::SendReqJoinExecution
EXTRN	?EnterExecution@Instance@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::Instance::EnterExecution
EXTRN	?OnResGameReady@Instance@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::Instance::OnResGameReady
EXTRN	?IsRunning@Instance@mu2@@UEBA_NXZ:PROC		; mu2::Instance::IsRunning
EXTRN	?GetDamageMeterKey@Instance@mu2@@UEBA?BIXZ:PROC	; mu2::Instance::GetDamageMeterKey
EXTRN	?Init@Instance@mu2@@UEAA_NAEAUExecutionCreateInfo@2@@Z:PROC ; mu2::Instance::Init
EXTRN	?Fillup@Instance@mu2@@UEAAXAEAPEAUEwzReqCreateExecution@2@@Z:PROC ; mu2::Instance::Fillup
EXTRN	?Fillup@Instance@mu2@@UEAAXAEAPEAUEwzReqDestroyExecution@2@@Z:PROC ; mu2::Instance::Fillup
EXTRN	?Manager@InstanceController@mu2@@IEAAAEAVInstanceManager@2@XZ:PROC ; mu2::InstanceController::Manager
EXTRN	?Update@InstanceSinglestage@mu2@@UEAAXXZ:PROC	; mu2::InstanceSinglestage::Update
EXTRN	?OnCreatedSucceed@InstanceWorldcross@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::InstanceWorldcross::OnCreatedSucceed
EXTRN	?OnDestroyed@InstanceWorldcross@mu2@@UEAAXXZ:PROC ; mu2::InstanceWorldcross::OnDestroyed
EXTRN	?EnterExecutionPost@InstanceWorldcross@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::InstanceWorldcross::EnterExecutionPost
EXTRN	?ExitExecutionPost@InstanceWorldcross@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::InstanceWorldcross::ExitExecutionPost
EXTRN	??0InstanceWorldcross@mu2@@QEAA@PEAVManagerExecutionBase@1@AEAUExecutionCreateInfo@1@@Z:PROC ; mu2::InstanceWorldcross::InstanceWorldcross
EXTRN	??1InstanceWorldcross@mu2@@UEAA@XZ:PROC		; mu2::InstanceWorldcross::~InstanceWorldcross
EXTRN	?Fillup@InstanceWorldcross@mu2@@UEBAXAEAVJoinZoneContextDest@2@@Z:PROC ; mu2::InstanceWorldcross::Fillup
EXTRN	?IsDestroyable@InstanceWorldcross@mu2@@UEBA_NXZ:PROC ; mu2::InstanceWorldcross::IsDestroyable
EXTRN	?IsWorldcrossing@InstanceWorldcross@mu2@@UEBA_NXZ:PROC ; mu2::InstanceWorldcross::IsWorldcrossing
EXTRN	?GetRelayServerId@InstanceWorldcross@mu2@@UEBA?BIXZ:PROC ; mu2::InstanceWorldcross::GetRelayServerId
EXTRN	?ToString@InstanceWorldcross@mu2@@UEBA?BV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ:PROC ; mu2::InstanceWorldcross::ToString
EXTRN	??_EInstanceColosseum33@mu2@@UEAAPEAXI@Z:PROC	; mu2::InstanceColosseum33::`vector deleting destructor'
EXTRN	??_EInstancePVPChaosCastle@mu2@@UEAAPEAXI@Z:PROC ; mu2::InstancePVPChaosCastle::`vector deleting destructor'
EXTRN	__CxxFrameHandler4:PROC
EXTRN	??_7type_info@@6B@:BYTE				; type_info::`vftable'
EXTRN	?Instance@Server@mu2@@2PEAV12@EA:QWORD		; mu2::Server::Instance
;	COMDAT pdata
pdata	SEGMENT
$pdata$?IsValid@Instance@mu2@@UEBA?B_NXZ DD imagerel $LN5
	DD	imagerel $LN5+62
	DD	imagerel $unwind$?IsValid@Instance@mu2@@UEBA?B_NXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?EnterExecution@InstanceColosseum33@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z DD imagerel $LN11
	DD	imagerel $LN11+274
	DD	imagerel $unwind$?EnterExecution@InstanceColosseum33@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GInstanceColosseum33@mu2@@UEAAPEAXI@Z DD imagerel $LN10
	DD	imagerel $LN10+76
	DD	imagerel $unwind$??_GInstanceColosseum33@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?AllocExecution@InstanceColosseum33Controller@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z DD imagerel $LN19
	DD	imagerel $LN19+325
	DD	imagerel $unwind$?AllocExecution@InstanceColosseum33Controller@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0??AllocExecution@InstanceColosseum33Controller@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z@4HA DD imagerel ?dtor$0@?0??AllocExecution@InstanceColosseum33Controller@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z@4HA
	DD	imagerel ?dtor$0@?0??AllocExecution@InstanceColosseum33Controller@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z@4HA+29
	DD	imagerel $unwind$?dtor$0@?0??AllocExecution@InstanceColosseum33Controller@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$1@?0??AllocExecution@InstanceColosseum33Controller@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z@4HA DD imagerel ?dtor$1@?0??AllocExecution@InstanceColosseum33Controller@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z@4HA
	DD	imagerel ?dtor$1@?0??AllocExecution@InstanceColosseum33Controller@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z@4HA+29
	DD	imagerel $unwind$?dtor$1@?0??AllocExecution@InstanceColosseum33Controller@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?EnterExecution@InstancePVPChaosCastle@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z DD imagerel $LN11
	DD	imagerel $LN11+274
	DD	imagerel $unwind$?EnterExecution@InstancePVPChaosCastle@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GInstancePVPChaosCastle@mu2@@UEAAPEAXI@Z DD imagerel $LN10
	DD	imagerel $LN10+76
	DD	imagerel $unwind$??_GInstancePVPChaosCastle@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?AllocExecution@InstancePVPChaosCastleController@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z DD imagerel $LN19
	DD	imagerel $LN19+325
	DD	imagerel $unwind$?AllocExecution@InstancePVPChaosCastleController@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0??AllocExecution@InstancePVPChaosCastleController@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z@4HA DD imagerel ?dtor$0@?0??AllocExecution@InstancePVPChaosCastleController@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z@4HA
	DD	imagerel ?dtor$0@?0??AllocExecution@InstancePVPChaosCastleController@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z@4HA+29
	DD	imagerel $unwind$?dtor$0@?0??AllocExecution@InstancePVPChaosCastleController@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$1@?0??AllocExecution@InstancePVPChaosCastleController@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z@4HA DD imagerel ?dtor$1@?0??AllocExecution@InstancePVPChaosCastleController@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z@4HA
	DD	imagerel ?dtor$1@?0??AllocExecution@InstancePVPChaosCastleController@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z@4HA+29
	DD	imagerel $unwind$?dtor$1@?0??AllocExecution@InstancePVPChaosCastleController@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z@4HA
pdata	ENDS
;	COMDAT ??_R1A@?0A@EA@InstancePVPChaosCastle@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@InstancePVPChaosCastle@mu2@@8 DD imagerel ??_R0?AVInstancePVPChaosCastle@mu2@@@8 ; mu2::InstancePVPChaosCastle::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	05H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3InstancePVPChaosCastle@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2InstancePVPChaosCastle@mu2@@8
rdata$r	SEGMENT
??_R2InstancePVPChaosCastle@mu2@@8 DD imagerel ??_R1A@?0A@EA@InstancePVPChaosCastle@mu2@@8 ; mu2::InstancePVPChaosCastle::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@InstanceWorldcross@mu2@@8
	DD	imagerel ??_R1A@?0A@EA@InstancePvp@mu2@@8
	DD	imagerel ??_R1A@?0A@EA@InstanceSinglestage@mu2@@8
	DD	imagerel ??_R1A@?0A@EA@Instance@mu2@@8
	DD	imagerel ??_R1A@?0A@EA@Execution@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3InstancePVPChaosCastle@mu2@@8
rdata$r	SEGMENT
??_R3InstancePVPChaosCastle@mu2@@8 DD 00H		; mu2::InstancePVPChaosCastle::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	06H
	DD	imagerel ??_R2InstancePVPChaosCastle@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVInstancePVPChaosCastle@mu2@@@8
data$rs	SEGMENT
??_R0?AVInstancePVPChaosCastle@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::InstancePVPChaosCastle `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVInstancePVPChaosCastle@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4InstancePVPChaosCastle@mu2@@6B@
rdata$r	SEGMENT
??_R4InstancePVPChaosCastle@mu2@@6B@ DD 01H		; mu2::InstancePVPChaosCastle::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVInstancePVPChaosCastle@mu2@@@8
	DD	imagerel ??_R3InstancePVPChaosCastle@mu2@@8
	DD	imagerel ??_R4InstancePVPChaosCastle@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R2InstancePvp@mu2@@8
rdata$r	SEGMENT
??_R2InstancePvp@mu2@@8 DD imagerel ??_R1A@?0A@EA@InstancePvp@mu2@@8 ; mu2::InstancePvp::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@InstanceSinglestage@mu2@@8
	DD	imagerel ??_R1A@?0A@EA@Instance@mu2@@8
	DD	imagerel ??_R1A@?0A@EA@Execution@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3InstancePvp@mu2@@8
rdata$r	SEGMENT
??_R3InstancePvp@mu2@@8 DD 00H				; mu2::InstancePvp::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	04H
	DD	imagerel ??_R2InstancePvp@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVInstancePvp@mu2@@@8
data$rs	SEGMENT
??_R0?AVInstancePvp@mu2@@@8 DQ FLAT:??_7type_info@@6B@	; mu2::InstancePvp `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVInstancePvp@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R1A@?0A@EA@InstancePvp@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@InstancePvp@mu2@@8 DD imagerel ??_R0?AVInstancePvp@mu2@@@8 ; mu2::InstancePvp::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	03H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3InstancePvp@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2InstanceWorldcross@mu2@@8
rdata$r	SEGMENT
??_R2InstanceWorldcross@mu2@@8 DD imagerel ??_R1A@?0A@EA@InstanceWorldcross@mu2@@8 ; mu2::InstanceWorldcross::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@InstancePvp@mu2@@8
	DD	imagerel ??_R1A@?0A@EA@InstanceSinglestage@mu2@@8
	DD	imagerel ??_R1A@?0A@EA@Instance@mu2@@8
	DD	imagerel ??_R1A@?0A@EA@Execution@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3InstanceWorldcross@mu2@@8
rdata$r	SEGMENT
??_R3InstanceWorldcross@mu2@@8 DD 00H			; mu2::InstanceWorldcross::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	05H
	DD	imagerel ??_R2InstanceWorldcross@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVInstanceWorldcross@mu2@@@8
data$rs	SEGMENT
??_R0?AVInstanceWorldcross@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::InstanceWorldcross `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVInstanceWorldcross@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R1A@?0A@EA@InstanceWorldcross@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@InstanceWorldcross@mu2@@8 DD imagerel ??_R0?AVInstanceWorldcross@mu2@@@8 ; mu2::InstanceWorldcross::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	04H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3InstanceWorldcross@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@InstanceColosseum33@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@InstanceColosseum33@mu2@@8 DD imagerel ??_R0?AVInstanceColosseum33@mu2@@@8 ; mu2::InstanceColosseum33::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	05H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3InstanceColosseum33@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2InstanceColosseum33@mu2@@8
rdata$r	SEGMENT
??_R2InstanceColosseum33@mu2@@8 DD imagerel ??_R1A@?0A@EA@InstanceColosseum33@mu2@@8 ; mu2::InstanceColosseum33::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@InstanceWorldcross@mu2@@8
	DD	imagerel ??_R1A@?0A@EA@InstancePvp@mu2@@8
	DD	imagerel ??_R1A@?0A@EA@InstanceSinglestage@mu2@@8
	DD	imagerel ??_R1A@?0A@EA@Instance@mu2@@8
	DD	imagerel ??_R1A@?0A@EA@Execution@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3InstanceColosseum33@mu2@@8
rdata$r	SEGMENT
??_R3InstanceColosseum33@mu2@@8 DD 00H			; mu2::InstanceColosseum33::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	06H
	DD	imagerel ??_R2InstanceColosseum33@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVInstanceColosseum33@mu2@@@8
data$rs	SEGMENT
??_R0?AVInstanceColosseum33@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::InstanceColosseum33 `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVInstanceColosseum33@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4InstanceColosseum33@mu2@@6B@
rdata$r	SEGMENT
??_R4InstanceColosseum33@mu2@@6B@ DD 01H		; mu2::InstanceColosseum33::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVInstanceColosseum33@mu2@@@8
	DD	imagerel ??_R3InstanceColosseum33@mu2@@8
	DD	imagerel ??_R4InstanceColosseum33@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R2Execution@mu2@@8
rdata$r	SEGMENT
??_R2Execution@mu2@@8 DD imagerel ??_R1A@?0A@EA@Execution@mu2@@8 ; mu2::Execution::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3Execution@mu2@@8
rdata$r	SEGMENT
??_R3Execution@mu2@@8 DD 00H				; mu2::Execution::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2Execution@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVExecution@mu2@@@8
data$rs	SEGMENT
??_R0?AVExecution@mu2@@@8 DQ FLAT:??_7type_info@@6B@	; mu2::Execution `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVExecution@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R1A@?0A@EA@Execution@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@Execution@mu2@@8 DD imagerel ??_R0?AVExecution@mu2@@@8 ; mu2::Execution::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3Execution@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2Instance@mu2@@8
rdata$r	SEGMENT
??_R2Instance@mu2@@8 DD imagerel ??_R1A@?0A@EA@Instance@mu2@@8 ; mu2::Instance::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@Execution@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3Instance@mu2@@8
rdata$r	SEGMENT
??_R3Instance@mu2@@8 DD 00H				; mu2::Instance::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2Instance@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVInstance@mu2@@@8
data$rs	SEGMENT
??_R0?AVInstance@mu2@@@8 DQ FLAT:??_7type_info@@6B@	; mu2::Instance `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVInstance@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R1A@?0A@EA@Instance@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@Instance@mu2@@8 DD imagerel ??_R0?AVInstance@mu2@@@8 ; mu2::Instance::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3Instance@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@InstanceSinglestage@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@InstanceSinglestage@mu2@@8 DD imagerel ??_R0?AVInstanceSinglestage@mu2@@@8 ; mu2::InstanceSinglestage::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	02H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3InstanceSinglestage@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2InstanceSinglestage@mu2@@8
rdata$r	SEGMENT
??_R2InstanceSinglestage@mu2@@8 DD imagerel ??_R1A@?0A@EA@InstanceSinglestage@mu2@@8 ; mu2::InstanceSinglestage::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@Instance@mu2@@8
	DD	imagerel ??_R1A@?0A@EA@Execution@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3InstanceSinglestage@mu2@@8
rdata$r	SEGMENT
??_R3InstanceSinglestage@mu2@@8 DD 00H			; mu2::InstanceSinglestage::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	03H
	DD	imagerel ??_R2InstanceSinglestage@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVInstanceSinglestage@mu2@@@8
data$rs	SEGMENT
??_R0?AVInstanceSinglestage@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::InstanceSinglestage `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVInstanceSinglestage@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_C@_0CM@OEPMHGEG@mu2?3?3InstancePVPChaosCastle?3?3En@
CONST	SEGMENT
??_C@_0CM@OEPMHGEG@mu2?3?3InstancePVPChaosCastle?3?3En@ DB 'mu2::Instance'
	DB	'PVPChaosCastle::EnterExecution', 00H	; `string'
CONST	ENDS
;	COMDAT ??_C@_0CJ@KGPMEDIC@mu2?3?3InstanceColosseum33?3?3Enter@
CONST	SEGMENT
??_C@_0CJ@KGPMEDIC@mu2?3?3InstanceColosseum33?3?3Enter@ DB 'mu2::Instance'
	DB	'Colosseum33::EnterExecution', 00H		; `string'
CONST	ENDS
;	COMDAT ??_C@_0BN@PLNLCFGM@eError?5?$DN?$DN?5ErrorJoin?3?3SUCCESS@
CONST	SEGMENT
??_C@_0BN@PLNLCFGM@eError?5?$DN?$DN?5ErrorJoin?3?3SUCCESS@ DB 'eError == '
	DB	'ErrorJoin::SUCCESS', 00H			; `string'
CONST	ENDS
;	COMDAT ??_C@_0FF@PBMKLIIH@F?3?2Release_Branch?2Server?2Develo@
CONST	SEGMENT
??_C@_0FF@PBMKLIIH@F?3?2Release_Branch?2Server?2Develo@ DB 'F:\Release_Br'
	DB	'anch\Server\Development\Frontend\WorldServer\InstanceColloseu'
	DB	'mPvp33.cpp', 00H				; `string'
CONST	ENDS
;	COMDAT ??_7InstancePVPChaosCastle@mu2@@6B@
CONST	SEGMENT
??_7InstancePVPChaosCastle@mu2@@6B@ DQ FLAT:??_R4InstancePVPChaosCastle@mu2@@6B@ ; mu2::InstancePVPChaosCastle::`vftable'
	DQ	FLAT:??_EInstancePVPChaosCastle@mu2@@UEAAPEAXI@Z
	DQ	FLAT:?OnDestroyed@InstanceWorldcross@mu2@@UEAAXXZ
	DQ	FLAT:?OnDestroyedFailed@Execution@mu2@@UEAAXXZ
	DQ	FLAT:?OnCreatedSucceed@InstanceWorldcross@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?OnCreatedFailed@Execution@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?OnResGameReady@Instance@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?EnterExecutionPrev@Execution@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?EnterExecution@InstancePVPChaosCastle@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?EnterExecutionPost@InstanceWorldcross@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?ExitExecutionPost@InstanceWorldcross@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?Init@Instance@mu2@@UEAA_NAEAUExecutionCreateInfo@2@@Z
	DQ	FLAT:?Update@InstanceSinglestage@mu2@@UEAAXXZ
	DQ	FLAT:?OnShutdownZone@Execution@mu2@@UEAAXXZ
	DQ	FLAT:?SendReqJoinExecution@Instance@mu2@@UEBA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@@Z
	DQ	FLAT:?IsInstance@Instance@mu2@@UEBA_NXZ
	DQ	FLAT:?IsChannel@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsWorldcross@InstanceWorldcross@mu2@@UEBA_NXZ
	DQ	FLAT:?IsTournamentEnter@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsWorldcrossing@InstanceWorldcross@mu2@@UEBA_NXZ
	DQ	FLAT:?IsSingle@InstanceSinglestage@mu2@@UEBA_NXZ
	DQ	FLAT:?IsGroup@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsNull@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsValid@Instance@mu2@@UEBA?B_NXZ
	DQ	FLAT:?IsRunning@Instance@mu2@@UEBA_NXZ
	DQ	FLAT:?IsJoinable@Execution@mu2@@UEBA_NPEAVEntityPlayer@2@@Z
	DQ	FLAT:?IsDestroyable@InstanceWorldcross@mu2@@UEBA_NXZ
	DQ	FLAT:?GetDamageMeterKey@Instance@mu2@@UEBA?BIXZ
	DQ	FLAT:?ToString@InstanceWorldcross@mu2@@UEBA?BV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ
	DQ	FLAT:?GetRelayServerId@InstanceWorldcross@mu2@@UEBA?BIXZ
	DQ	FLAT:?Fillup@Execution@mu2@@MEBA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAPEAUEwzReqJoinExecution@2@@Z
	DQ	FLAT:?Fillup@InstanceWorldcross@mu2@@UEBAXAEAVJoinZoneContextDest@2@@Z
	DQ	FLAT:?Fillup@Instance@mu2@@UEAAXAEAPEAUEwzReqDestroyExecution@2@@Z
	DQ	FLAT:?Fillup@Instance@mu2@@UEAAXAEAPEAUEwzReqCreateExecution@2@@Z
	DQ	FLAT:?GetGroupKey@Instance@mu2@@UEBA?BIXZ
CONST	ENDS
;	COMDAT ??_7InstanceColosseum33@mu2@@6B@
CONST	SEGMENT
??_7InstanceColosseum33@mu2@@6B@ DQ FLAT:??_R4InstanceColosseum33@mu2@@6B@ ; mu2::InstanceColosseum33::`vftable'
	DQ	FLAT:??_EInstanceColosseum33@mu2@@UEAAPEAXI@Z
	DQ	FLAT:?OnDestroyed@InstanceWorldcross@mu2@@UEAAXXZ
	DQ	FLAT:?OnDestroyedFailed@Execution@mu2@@UEAAXXZ
	DQ	FLAT:?OnCreatedSucceed@InstanceWorldcross@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?OnCreatedFailed@Execution@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?OnResGameReady@Instance@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?EnterExecutionPrev@Execution@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?EnterExecution@InstanceColosseum33@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?EnterExecutionPost@InstanceWorldcross@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?ExitExecutionPost@InstanceWorldcross@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?Init@Instance@mu2@@UEAA_NAEAUExecutionCreateInfo@2@@Z
	DQ	FLAT:?Update@InstanceSinglestage@mu2@@UEAAXXZ
	DQ	FLAT:?OnShutdownZone@Execution@mu2@@UEAAXXZ
	DQ	FLAT:?SendReqJoinExecution@Instance@mu2@@UEBA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@@Z
	DQ	FLAT:?IsInstance@Instance@mu2@@UEBA_NXZ
	DQ	FLAT:?IsChannel@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsWorldcross@InstanceWorldcross@mu2@@UEBA_NXZ
	DQ	FLAT:?IsTournamentEnter@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsWorldcrossing@InstanceWorldcross@mu2@@UEBA_NXZ
	DQ	FLAT:?IsSingle@InstanceSinglestage@mu2@@UEBA_NXZ
	DQ	FLAT:?IsGroup@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsNull@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsValid@Instance@mu2@@UEBA?B_NXZ
	DQ	FLAT:?IsRunning@Instance@mu2@@UEBA_NXZ
	DQ	FLAT:?IsJoinable@Execution@mu2@@UEBA_NPEAVEntityPlayer@2@@Z
	DQ	FLAT:?IsDestroyable@InstanceWorldcross@mu2@@UEBA_NXZ
	DQ	FLAT:?GetDamageMeterKey@Instance@mu2@@UEBA?BIXZ
	DQ	FLAT:?ToString@InstanceWorldcross@mu2@@UEBA?BV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ
	DQ	FLAT:?GetRelayServerId@InstanceWorldcross@mu2@@UEBA?BIXZ
	DQ	FLAT:?Fillup@Execution@mu2@@MEBA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAPEAUEwzReqJoinExecution@2@@Z
	DQ	FLAT:?Fillup@InstanceWorldcross@mu2@@UEBAXAEAVJoinZoneContextDest@2@@Z
	DQ	FLAT:?Fillup@Instance@mu2@@UEAAXAEAPEAUEwzReqDestroyExecution@2@@Z
	DQ	FLAT:?Fillup@Instance@mu2@@UEAAXAEAPEAUEwzReqCreateExecution@2@@Z
	DQ	FLAT:?GetGroupKey@Instance@mu2@@UEBA?BIXZ
CONST	ENDS
;	COMDAT ??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
CONST	SEGMENT
??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@ DB 'g', 00H, 'a', 00H, 'm', 00H, 'e'
	DB	00H, 00H, 00H				; `string'
CONST	ENDS
;	COMDAT ??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
CONST	SEGMENT
??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@ DB '%'
	DB	's> ASSERT - %s, %s(%d)', 00H		; `string'
CONST	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$1@?0??AllocExecution@InstancePVPChaosCastleController@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0??AllocExecution@InstancePVPChaosCastleController@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?AllocExecution@InstancePVPChaosCastleController@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z DB 0aH
	DB	00H
	DB	00H
	DB	09cH
	DB	02H
	DB	0d2H
	DB	00H
	DB	'0'
	DB	04H
	DB	0d2H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?AllocExecution@InstancePVPChaosCastleController@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z DB 04H
	DB	0eH
	DD	imagerel ?dtor$0@?0??AllocExecution@InstancePVPChaosCastleController@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z@4HA
	DB	036H
	DD	imagerel ?dtor$1@?0??AllocExecution@InstancePVPChaosCastleController@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?AllocExecution@InstancePVPChaosCastleController@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z DB 028H
	DD	imagerel $stateUnwindMap$?AllocExecution@InstancePVPChaosCastleController@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z
	DD	imagerel $ip2state$?AllocExecution@InstancePVPChaosCastleController@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?AllocExecution@InstancePVPChaosCastleController@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z DD 021111H
	DD	0110111H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?AllocExecution@InstancePVPChaosCastleController@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GInstancePVPChaosCastle@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?EnterExecution@InstancePVPChaosCastle@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z DD 021601H
	DD	0130116H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$1@?0??AllocExecution@InstanceColosseum33Controller@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0??AllocExecution@InstanceColosseum33Controller@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?AllocExecution@InstanceColosseum33Controller@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z DB 0aH
	DB	00H
	DB	00H
	DB	09cH
	DB	02H
	DB	0d2H
	DB	00H
	DB	'0'
	DB	04H
	DB	0d2H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?AllocExecution@InstanceColosseum33Controller@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z DB 04H
	DB	0eH
	DD	imagerel ?dtor$0@?0??AllocExecution@InstanceColosseum33Controller@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z@4HA
	DB	036H
	DD	imagerel ?dtor$1@?0??AllocExecution@InstanceColosseum33Controller@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?AllocExecution@InstanceColosseum33Controller@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z DB 028H
	DD	imagerel $stateUnwindMap$?AllocExecution@InstanceColosseum33Controller@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z
	DD	imagerel $ip2state$?AllocExecution@InstanceColosseum33Controller@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?AllocExecution@InstanceColosseum33Controller@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z DD 021111H
	DD	0110111H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?AllocExecution@InstanceColosseum33Controller@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GInstanceColosseum33@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?EnterExecution@InstanceColosseum33@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z DD 021601H
	DD	0130116H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?IsValid@Instance@mu2@@UEBA?B_NXZ DD 010901H
	DD	06209H
xdata	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceColloseumPvp33.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldServer.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceColloseumPvp33.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceColloseumPvp33.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceColloseumPvp33.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceColloseumPvp33.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceColloseumPvp33.cpp
;	COMDAT ?AllocExecution@InstancePVPChaosCastleController@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z
_TEXT	SEGMENT
$T1 = 32
$T2 = 40
$T3 = 48
tv128 = 56
tv151 = 64
pOwnerManager$ = 72
$T4 = 80
$T5 = 88
pOwnerManager$ = 96
$T6 = 104
$T7 = 112
this$ = 144
cInfo$ = 152
?AllocExecution@InstancePVPChaosCastleController@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z PROC ; mu2::InstancePVPChaosCastleController::AllocExecution, COMDAT

; 86   : 	{

$LN19:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldServer.h

; 90   : 	WorldId	GetWorldId() { return m_worldInfo.worldId; }	

  00011	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR ?Instance@Server@mu2@@2PEAV12@EA ; mu2::Server::Instance
  00018	0f b6 80 08 04
	00 00		 movzx	 eax, BYTE PTR [rax+1032]
  0001f	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceColloseumPvp33.cpp

; 87   : 		if (cInfo.worldId == SERVER.GetWorldId())

  00023	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR cInfo$[rsp]
  0002b	0f b6 40 0c	 movzx	 eax, BYTE PTR [rax+12]
  0002f	0f b6 4c 24 20	 movzx	 ecx, BYTE PTR $T1[rsp]
  00034	0f b6 c9	 movzx	 ecx, cl
  00037	3b c1		 cmp	 eax, ecx
  00039	0f 85 81 00 00
	00		 jne	 $LN2@AllocExecu

; 89   : 			return new InstancePVPChaosCastle((ManagerExecutionBase*)&Manager(), cInfo);

  0003f	b9 78 01 00 00	 mov	 ecx, 376		; 00000178H
  00044	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00049	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax
  0004e	48 83 7c 24 28
	00		 cmp	 QWORD PTR $T2[rsp], 0
  00054	74 4e		 je	 SHORT $LN5@AllocExecu
  00056	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0005e	e8 00 00 00 00	 call	 ?Manager@InstanceController@mu2@@IEAAAEAVInstanceManager@2@XZ ; mu2::InstanceController::Manager
  00063	48 89 44 24 48	 mov	 QWORD PTR pOwnerManager$[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceColloseumPvp33.h

; 42   : 			: InstanceWorldcross(pOwnerManager, cInfo)

  00068	4c 8b 84 24 98
	00 00 00	 mov	 r8, QWORD PTR cInfo$[rsp]
  00070	48 8b 54 24 48	 mov	 rdx, QWORD PTR pOwnerManager$[rsp]
  00075	48 8b 4c 24 28	 mov	 rcx, QWORD PTR $T2[rsp]
  0007a	e8 00 00 00 00	 call	 ??0InstanceWorldcross@mu2@@QEAA@PEAVManagerExecutionBase@1@AEAUExecutionCreateInfo@1@@Z ; mu2::InstanceWorldcross::InstanceWorldcross

; 43   : 		{

  0007f	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
  00084	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7InstancePVPChaosCastle@mu2@@6B@
  0008b	48 89 08	 mov	 QWORD PTR [rax], rcx

; 44   : 		}

  0008e	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
  00093	48 89 44 24 50	 mov	 QWORD PTR $T4[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceColloseumPvp33.cpp

; 89   : 			return new InstancePVPChaosCastle((ManagerExecutionBase*)&Manager(), cInfo);

  00098	48 8b 44 24 50	 mov	 rax, QWORD PTR $T4[rsp]
  0009d	48 89 44 24 38	 mov	 QWORD PTR tv128[rsp], rax
  000a2	eb 09		 jmp	 SHORT $LN6@AllocExecu
$LN5@AllocExecu:
  000a4	48 c7 44 24 38
	00 00 00 00	 mov	 QWORD PTR tv128[rsp], 0
$LN6@AllocExecu:
  000ad	48 8b 44 24 38	 mov	 rax, QWORD PTR tv128[rsp]
  000b2	48 89 44 24 58	 mov	 QWORD PTR $T5[rsp], rax
  000b7	48 8b 44 24 58	 mov	 rax, QWORD PTR $T5[rsp]
  000bc	eb 7f		 jmp	 SHORT $LN1@AllocExecu

; 90   : 		}

  000be	eb 7d		 jmp	 SHORT $LN1@AllocExecu
$LN2@AllocExecu:

; 93   : 			return new InstancePVPChaosCastle((ManagerExecutionBase*)&Manager(), cInfo);

  000c0	b9 78 01 00 00	 mov	 ecx, 376		; 00000178H
  000c5	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  000ca	48 89 44 24 30	 mov	 QWORD PTR $T3[rsp], rax
  000cf	48 83 7c 24 30
	00		 cmp	 QWORD PTR $T3[rsp], 0
  000d5	74 4e		 je	 SHORT $LN7@AllocExecu
  000d7	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  000df	e8 00 00 00 00	 call	 ?Manager@InstanceController@mu2@@IEAAAEAVInstanceManager@2@XZ ; mu2::InstanceController::Manager
  000e4	48 89 44 24 60	 mov	 QWORD PTR pOwnerManager$[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceColloseumPvp33.h

; 42   : 			: InstanceWorldcross(pOwnerManager, cInfo)

  000e9	4c 8b 84 24 98
	00 00 00	 mov	 r8, QWORD PTR cInfo$[rsp]
  000f1	48 8b 54 24 60	 mov	 rdx, QWORD PTR pOwnerManager$[rsp]
  000f6	48 8b 4c 24 30	 mov	 rcx, QWORD PTR $T3[rsp]
  000fb	e8 00 00 00 00	 call	 ??0InstanceWorldcross@mu2@@QEAA@PEAVManagerExecutionBase@1@AEAUExecutionCreateInfo@1@@Z ; mu2::InstanceWorldcross::InstanceWorldcross

; 43   : 		{

  00100	48 8b 44 24 30	 mov	 rax, QWORD PTR $T3[rsp]
  00105	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7InstancePVPChaosCastle@mu2@@6B@
  0010c	48 89 08	 mov	 QWORD PTR [rax], rcx

; 44   : 		}

  0010f	48 8b 44 24 30	 mov	 rax, QWORD PTR $T3[rsp]
  00114	48 89 44 24 68	 mov	 QWORD PTR $T6[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceColloseumPvp33.cpp

; 93   : 			return new InstancePVPChaosCastle((ManagerExecutionBase*)&Manager(), cInfo);

  00119	48 8b 44 24 68	 mov	 rax, QWORD PTR $T6[rsp]
  0011e	48 89 44 24 40	 mov	 QWORD PTR tv151[rsp], rax
  00123	eb 09		 jmp	 SHORT $LN8@AllocExecu
$LN7@AllocExecu:
  00125	48 c7 44 24 40
	00 00 00 00	 mov	 QWORD PTR tv151[rsp], 0
$LN8@AllocExecu:
  0012e	48 8b 44 24 40	 mov	 rax, QWORD PTR tv151[rsp]
  00133	48 89 44 24 70	 mov	 QWORD PTR $T7[rsp], rax
  00138	48 8b 44 24 70	 mov	 rax, QWORD PTR $T7[rsp]
$LN1@AllocExecu:

; 94   : 		}
; 95   : 	}

  0013d	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  00144	c3		 ret	 0
?AllocExecution@InstancePVPChaosCastleController@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z ENDP ; mu2::InstancePVPChaosCastleController::AllocExecution
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 40
$T3 = 48
tv128 = 56
tv151 = 64
pOwnerManager$ = 72
$T4 = 80
$T5 = 88
pOwnerManager$ = 96
$T6 = 104
$T7 = 112
this$ = 144
cInfo$ = 152
?dtor$0@?0??AllocExecution@InstancePVPChaosCastleController@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z@4HA PROC ; `mu2::InstancePVPChaosCastleController::AllocExecution'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	ba 78 01 00 00	 mov	 edx, 376		; 00000178H
  0000e	48 8b 4d 28	 mov	 rcx, QWORD PTR $T2[rbp]
  00012	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00017	48 83 c4 20	 add	 rsp, 32			; 00000020H
  0001b	5d		 pop	 rbp
  0001c	c3		 ret	 0
?dtor$0@?0??AllocExecution@InstancePVPChaosCastleController@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z@4HA ENDP ; `mu2::InstancePVPChaosCastleController::AllocExecution'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 40
$T3 = 48
tv128 = 56
tv151 = 64
pOwnerManager$ = 72
$T4 = 80
$T5 = 88
pOwnerManager$ = 96
$T6 = 104
$T7 = 112
this$ = 144
cInfo$ = 152
?dtor$1@?0??AllocExecution@InstancePVPChaosCastleController@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z@4HA PROC ; `mu2::InstancePVPChaosCastleController::AllocExecution'::`1'::dtor$1
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	ba 78 01 00 00	 mov	 edx, 376		; 00000178H
  0000e	48 8b 4d 30	 mov	 rcx, QWORD PTR $T3[rbp]
  00012	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00017	48 83 c4 20	 add	 rsp, 32			; 00000020H
  0001b	5d		 pop	 rbp
  0001c	c3		 ret	 0
?dtor$1@?0??AllocExecution@InstancePVPChaosCastleController@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z@4HA ENDP ; `mu2::InstancePVPChaosCastleController::AllocExecution'::`1'::dtor$1
text$x	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceColloseumPvp33.h
;	COMDAT ??_GInstancePVPChaosCastle@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GInstancePVPChaosCastle@mu2@@UEAAPEAXI@Z PROC	; mu2::InstancePVPChaosCastle::`scalar deleting destructor', COMDAT
$LN10:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 45   : 		virtual~InstancePVPChaosCastle() override {}

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7InstancePVPChaosCastle@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx
  0001c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00021	e8 00 00 00 00	 call	 ??1InstanceWorldcross@mu2@@UEAA@XZ ; mu2::InstanceWorldcross::~InstanceWorldcross
  00026	90		 npad	 1
  00027	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  0002b	83 e0 01	 and	 eax, 1
  0002e	85 c0		 test	 eax, eax
  00030	74 10		 je	 SHORT $LN2@scalar
  00032	ba 78 01 00 00	 mov	 edx, 376		; 00000178H
  00037	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0003c	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00041	90		 npad	 1
$LN2@scalar:
  00042	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00047	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0004b	c3		 ret	 0
??_GInstancePVPChaosCastle@mu2@@UEAAPEAXI@Z ENDP	; mu2::InstancePVPChaosCastle::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceColloseumPvp33.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceColloseumPvp33.cpp
;	COMDAT ?EnterExecution@InstancePVPChaosCastle@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
_TEXT	SEGMENT
eError$ = 96
hConsole$1 = 104
$T2 = 112
$T3 = 120
res$ = 128
this$ = 160
player$ = 168
e$ = 176
?EnterExecution@InstancePVPChaosCastle@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z PROC ; mu2::InstancePVPChaosCastle::EnterExecution, COMDAT

; 59   : 	{

$LN11:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 81 ec 98 00
	00 00		 sub	 rsp, 152		; 00000098H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1309 :         return _Ptr;

  00016	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR e$[rsp]
  0001e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00021	48 89 44 24 70	 mov	 QWORD PTR $T2[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h

; 191  : 		return ptr.get();

  00026	48 8b 44 24 70	 mov	 rax, QWORD PTR $T2[rsp]
  0002b	48 89 44 24 78	 mov	 QWORD PTR $T3[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceColloseumPvp33.cpp

; 60   : 		EzwResJoinExecution* res = static_cast<EzwResJoinExecution*>(e.RawPtr());

  00030	48 8b 44 24 78	 mov	 rax, QWORD PTR $T3[rsp]
  00035	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR res$[rsp], rax

; 61   : 		UNREFERENCED_PARAMETER(res);
; 62   : 
; 63   : 		ErrorJoin::Error eError = __super::EnterExecution(player, e);

  0003d	4c 8b 84 24 b0
	00 00 00	 mov	 r8, QWORD PTR e$[rsp]
  00045	48 8b 94 24 a8
	00 00 00	 mov	 rdx, QWORD PTR player$[rsp]
  0004d	48 8b 8c 24 a0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00055	e8 00 00 00 00	 call	 ?EnterExecution@Instance@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ; mu2::Instance::EnterExecution
  0005a	89 44 24 60	 mov	 DWORD PTR eError$[rsp], eax

; 64   : 		VERIFY_RETURN(eError == ErrorJoin::SUCCESS, eError);

  0005e	83 7c 24 60 00	 cmp	 DWORD PTR eError$[rsp], 0
  00063	0f 84 9f 00 00
	00		 je	 $LN2@EnterExecu
  00069	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  0006e	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  00074	48 89 44 24 68	 mov	 QWORD PTR hConsole$1[rsp], rax
  00079	66 ba 0d 00	 mov	 dx, 13
  0007d	48 8b 4c 24 68	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  00082	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  00088	c7 44 24 50 40
	00 00 00	 mov	 DWORD PTR [rsp+80], 64	; 00000040H
  00090	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FF@PBMKLIIH@F?3?2Release_Branch?2Server?2Develo@
  00097	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  0009c	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BN@PLNLCFGM@eError?5?$DN?$DN?5ErrorJoin?3?3SUCCESS@
  000a3	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  000a8	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CM@OEPMHGEG@mu2?3?3InstancePVPChaosCastle?3?3En@
  000af	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  000b4	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  000bb	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  000c0	c7 44 24 28 40
	00 00 00	 mov	 DWORD PTR [rsp+40], 64	; 00000040H
  000c8	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FF@PBMKLIIH@F?3?2Release_Branch?2Server?2Develo@
  000cf	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  000d4	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CM@OEPMHGEG@mu2?3?3InstancePVPChaosCastle?3?3En@
  000db	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  000e1	ba 02 00 00 00	 mov	 edx, 2
  000e6	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  000ed	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  000f2	66 ba 07 00	 mov	 dx, 7
  000f6	48 8b 4c 24 68	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  000fb	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  00101	90		 npad	 1
  00102	8b 44 24 60	 mov	 eax, DWORD PTR eError$[rsp]
  00106	eb 02		 jmp	 SHORT $LN1@EnterExecu
$LN2@EnterExecu:

; 65   : 
; 66   : 		//====================================================================================
; 67   : 		// 기획적으로 재진입 불가, 코드상으로는 재진입 가능하게 하였으나, 
; 68   : 		// "모든 캐릭터의 입장을 기다리고 있습니다." 라는 메세지만 뜨고 진행이 안됨
; 69   : 		//====================================================================================
; 70   : 
; 71   : 		//theMissionMapJoinManager.AddMissionMapJoin(player->GetCharId(), GetExecId(), res->arrivedLocation.indexPosition);
; 72   : 
; 73   : 
; 74   : 
; 75   : 
; 76   : 
; 77   : 		return ErrorJoin::SUCCESS;

  00108	33 c0		 xor	 eax, eax
$LN1@EnterExecu:

; 78   : 	}

  0010a	48 81 c4 98 00
	00 00		 add	 rsp, 152		; 00000098H
  00111	c3		 ret	 0
?EnterExecution@InstancePVPChaosCastle@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ENDP ; mu2::InstancePVPChaosCastle::EnterExecution
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceColloseumPvp33.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldServer.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceColloseumPvp33.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceColloseumPvp33.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceColloseumPvp33.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceColloseumPvp33.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceColloseumPvp33.cpp
;	COMDAT ?AllocExecution@InstanceColosseum33Controller@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z
_TEXT	SEGMENT
$T1 = 32
$T2 = 40
$T3 = 48
tv128 = 56
tv151 = 64
pOwnerManager$ = 72
$T4 = 80
$T5 = 88
pOwnerManager$ = 96
$T6 = 104
$T7 = 112
this$ = 144
cInfo$ = 152
?AllocExecution@InstanceColosseum33Controller@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z PROC ; mu2::InstanceColosseum33Controller::AllocExecution, COMDAT

; 42   : 	{ 

$LN19:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldServer.h

; 90   : 	WorldId	GetWorldId() { return m_worldInfo.worldId; }	

  00011	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR ?Instance@Server@mu2@@2PEAV12@EA ; mu2::Server::Instance
  00018	0f b6 80 08 04
	00 00		 movzx	 eax, BYTE PTR [rax+1032]
  0001f	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceColloseumPvp33.cpp

; 43   : 		if(cInfo.worldId == SERVER.GetWorldId())

  00023	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR cInfo$[rsp]
  0002b	0f b6 40 0c	 movzx	 eax, BYTE PTR [rax+12]
  0002f	0f b6 4c 24 20	 movzx	 ecx, BYTE PTR $T1[rsp]
  00034	0f b6 c9	 movzx	 ecx, cl
  00037	3b c1		 cmp	 eax, ecx
  00039	0f 85 81 00 00
	00		 jne	 $LN2@AllocExecu

; 45   : 			return new InstanceColosseum33((ManagerExecutionBase*)&Manager(), cInfo);

  0003f	b9 78 01 00 00	 mov	 ecx, 376		; 00000178H
  00044	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00049	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax
  0004e	48 83 7c 24 28
	00		 cmp	 QWORD PTR $T2[rsp], 0
  00054	74 4e		 je	 SHORT $LN5@AllocExecu
  00056	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0005e	e8 00 00 00 00	 call	 ?Manager@InstanceController@mu2@@IEAAAEAVInstanceManager@2@XZ ; mu2::InstanceController::Manager
  00063	48 89 44 24 48	 mov	 QWORD PTR pOwnerManager$[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceColloseumPvp33.h

; 15   : 			: InstanceWorldcross(pOwnerManager, cInfo)

  00068	4c 8b 84 24 98
	00 00 00	 mov	 r8, QWORD PTR cInfo$[rsp]
  00070	48 8b 54 24 48	 mov	 rdx, QWORD PTR pOwnerManager$[rsp]
  00075	48 8b 4c 24 28	 mov	 rcx, QWORD PTR $T2[rsp]
  0007a	e8 00 00 00 00	 call	 ??0InstanceWorldcross@mu2@@QEAA@PEAVManagerExecutionBase@1@AEAUExecutionCreateInfo@1@@Z ; mu2::InstanceWorldcross::InstanceWorldcross

; 16   : 		{

  0007f	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
  00084	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7InstanceColosseum33@mu2@@6B@
  0008b	48 89 08	 mov	 QWORD PTR [rax], rcx

; 17   : 		}

  0008e	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
  00093	48 89 44 24 50	 mov	 QWORD PTR $T4[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceColloseumPvp33.cpp

; 45   : 			return new InstanceColosseum33((ManagerExecutionBase*)&Manager(), cInfo);

  00098	48 8b 44 24 50	 mov	 rax, QWORD PTR $T4[rsp]
  0009d	48 89 44 24 38	 mov	 QWORD PTR tv128[rsp], rax
  000a2	eb 09		 jmp	 SHORT $LN6@AllocExecu
$LN5@AllocExecu:
  000a4	48 c7 44 24 38
	00 00 00 00	 mov	 QWORD PTR tv128[rsp], 0
$LN6@AllocExecu:
  000ad	48 8b 44 24 38	 mov	 rax, QWORD PTR tv128[rsp]
  000b2	48 89 44 24 58	 mov	 QWORD PTR $T5[rsp], rax
  000b7	48 8b 44 24 58	 mov	 rax, QWORD PTR $T5[rsp]
  000bc	eb 7f		 jmp	 SHORT $LN1@AllocExecu

; 46   : 		}

  000be	eb 7d		 jmp	 SHORT $LN1@AllocExecu
$LN2@AllocExecu:

; 49   : 			return new InstanceColosseum33((ManagerExecutionBase*)&Manager(), cInfo);

  000c0	b9 78 01 00 00	 mov	 ecx, 376		; 00000178H
  000c5	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  000ca	48 89 44 24 30	 mov	 QWORD PTR $T3[rsp], rax
  000cf	48 83 7c 24 30
	00		 cmp	 QWORD PTR $T3[rsp], 0
  000d5	74 4e		 je	 SHORT $LN7@AllocExecu
  000d7	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  000df	e8 00 00 00 00	 call	 ?Manager@InstanceController@mu2@@IEAAAEAVInstanceManager@2@XZ ; mu2::InstanceController::Manager
  000e4	48 89 44 24 60	 mov	 QWORD PTR pOwnerManager$[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceColloseumPvp33.h

; 15   : 			: InstanceWorldcross(pOwnerManager, cInfo)

  000e9	4c 8b 84 24 98
	00 00 00	 mov	 r8, QWORD PTR cInfo$[rsp]
  000f1	48 8b 54 24 60	 mov	 rdx, QWORD PTR pOwnerManager$[rsp]
  000f6	48 8b 4c 24 30	 mov	 rcx, QWORD PTR $T3[rsp]
  000fb	e8 00 00 00 00	 call	 ??0InstanceWorldcross@mu2@@QEAA@PEAVManagerExecutionBase@1@AEAUExecutionCreateInfo@1@@Z ; mu2::InstanceWorldcross::InstanceWorldcross

; 16   : 		{

  00100	48 8b 44 24 30	 mov	 rax, QWORD PTR $T3[rsp]
  00105	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7InstanceColosseum33@mu2@@6B@
  0010c	48 89 08	 mov	 QWORD PTR [rax], rcx

; 17   : 		}

  0010f	48 8b 44 24 30	 mov	 rax, QWORD PTR $T3[rsp]
  00114	48 89 44 24 68	 mov	 QWORD PTR $T6[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceColloseumPvp33.cpp

; 49   : 			return new InstanceColosseum33((ManagerExecutionBase*)&Manager(), cInfo);

  00119	48 8b 44 24 68	 mov	 rax, QWORD PTR $T6[rsp]
  0011e	48 89 44 24 40	 mov	 QWORD PTR tv151[rsp], rax
  00123	eb 09		 jmp	 SHORT $LN8@AllocExecu
$LN7@AllocExecu:
  00125	48 c7 44 24 40
	00 00 00 00	 mov	 QWORD PTR tv151[rsp], 0
$LN8@AllocExecu:
  0012e	48 8b 44 24 40	 mov	 rax, QWORD PTR tv151[rsp]
  00133	48 89 44 24 70	 mov	 QWORD PTR $T7[rsp], rax
  00138	48 8b 44 24 70	 mov	 rax, QWORD PTR $T7[rsp]
$LN1@AllocExecu:

; 50   : 		}		
; 51   : 	}

  0013d	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  00144	c3		 ret	 0
?AllocExecution@InstanceColosseum33Controller@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z ENDP ; mu2::InstanceColosseum33Controller::AllocExecution
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 40
$T3 = 48
tv128 = 56
tv151 = 64
pOwnerManager$ = 72
$T4 = 80
$T5 = 88
pOwnerManager$ = 96
$T6 = 104
$T7 = 112
this$ = 144
cInfo$ = 152
?dtor$0@?0??AllocExecution@InstanceColosseum33Controller@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z@4HA PROC ; `mu2::InstanceColosseum33Controller::AllocExecution'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	ba 78 01 00 00	 mov	 edx, 376		; 00000178H
  0000e	48 8b 4d 28	 mov	 rcx, QWORD PTR $T2[rbp]
  00012	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00017	48 83 c4 20	 add	 rsp, 32			; 00000020H
  0001b	5d		 pop	 rbp
  0001c	c3		 ret	 0
?dtor$0@?0??AllocExecution@InstanceColosseum33Controller@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z@4HA ENDP ; `mu2::InstanceColosseum33Controller::AllocExecution'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 40
$T3 = 48
tv128 = 56
tv151 = 64
pOwnerManager$ = 72
$T4 = 80
$T5 = 88
pOwnerManager$ = 96
$T6 = 104
$T7 = 112
this$ = 144
cInfo$ = 152
?dtor$1@?0??AllocExecution@InstanceColosseum33Controller@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z@4HA PROC ; `mu2::InstanceColosseum33Controller::AllocExecution'::`1'::dtor$1
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	ba 78 01 00 00	 mov	 edx, 376		; 00000178H
  0000e	48 8b 4d 30	 mov	 rcx, QWORD PTR $T3[rbp]
  00012	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00017	48 83 c4 20	 add	 rsp, 32			; 00000020H
  0001b	5d		 pop	 rbp
  0001c	c3		 ret	 0
?dtor$1@?0??AllocExecution@InstanceColosseum33Controller@mu2@@UEAAPEAVExecution@2@AEAUExecutionCreateInfo@2@@Z@4HA ENDP ; `mu2::InstanceColosseum33Controller::AllocExecution'::`1'::dtor$1
text$x	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceColloseumPvp33.h
;	COMDAT ??_GInstanceColosseum33@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GInstanceColosseum33@mu2@@UEAAPEAXI@Z PROC		; mu2::InstanceColosseum33::`scalar deleting destructor', COMDAT
$LN10:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 18   : 		virtual~InstanceColosseum33() override {}

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7InstanceColosseum33@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx
  0001c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00021	e8 00 00 00 00	 call	 ??1InstanceWorldcross@mu2@@UEAA@XZ ; mu2::InstanceWorldcross::~InstanceWorldcross
  00026	90		 npad	 1
  00027	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  0002b	83 e0 01	 and	 eax, 1
  0002e	85 c0		 test	 eax, eax
  00030	74 10		 je	 SHORT $LN2@scalar
  00032	ba 78 01 00 00	 mov	 edx, 376		; 00000178H
  00037	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0003c	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00041	90		 npad	 1
$LN2@scalar:
  00042	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00047	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0004b	c3		 ret	 0
??_GInstanceColosseum33@mu2@@UEAAPEAXI@Z ENDP		; mu2::InstanceColosseum33::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceColloseumPvp33.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceColloseumPvp33.cpp
;	COMDAT ?EnterExecution@InstanceColosseum33@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
_TEXT	SEGMENT
eError$ = 96
hConsole$1 = 104
$T2 = 112
$T3 = 120
res$ = 128
this$ = 160
player$ = 168
e$ = 176
?EnterExecution@InstanceColosseum33@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z PROC ; mu2::InstanceColosseum33::EnterExecution, COMDAT

; 13   : 	{

$LN11:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 81 ec 98 00
	00 00		 sub	 rsp, 152		; 00000098H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1309 :         return _Ptr;

  00016	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR e$[rsp]
  0001e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00021	48 89 44 24 70	 mov	 QWORD PTR $T2[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h

; 191  : 		return ptr.get();

  00026	48 8b 44 24 70	 mov	 rax, QWORD PTR $T2[rsp]
  0002b	48 89 44 24 78	 mov	 QWORD PTR $T3[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceColloseumPvp33.cpp

; 14   : 		EzwResJoinExecution* res = static_cast<EzwResJoinExecution*>(e.RawPtr());

  00030	48 8b 44 24 78	 mov	 rax, QWORD PTR $T3[rsp]
  00035	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR res$[rsp], rax

; 15   : 		UNREFERENCED_PARAMETER(res);
; 16   : 
; 17   : 		ErrorJoin::Error eError = __super::EnterExecution(player, e);

  0003d	4c 8b 84 24 b0
	00 00 00	 mov	 r8, QWORD PTR e$[rsp]
  00045	48 8b 94 24 a8
	00 00 00	 mov	 rdx, QWORD PTR player$[rsp]
  0004d	48 8b 8c 24 a0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00055	e8 00 00 00 00	 call	 ?EnterExecution@Instance@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ; mu2::Instance::EnterExecution
  0005a	89 44 24 60	 mov	 DWORD PTR eError$[rsp], eax

; 18   : 		VERIFY_RETURN(eError == ErrorJoin::SUCCESS, eError);		

  0005e	83 7c 24 60 00	 cmp	 DWORD PTR eError$[rsp], 0
  00063	0f 84 9f 00 00
	00		 je	 $LN2@EnterExecu
  00069	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  0006e	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  00074	48 89 44 24 68	 mov	 QWORD PTR hConsole$1[rsp], rax
  00079	66 ba 0d 00	 mov	 dx, 13
  0007d	48 8b 4c 24 68	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  00082	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  00088	c7 44 24 50 12
	00 00 00	 mov	 DWORD PTR [rsp+80], 18
  00090	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FF@PBMKLIIH@F?3?2Release_Branch?2Server?2Develo@
  00097	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  0009c	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BN@PLNLCFGM@eError?5?$DN?$DN?5ErrorJoin?3?3SUCCESS@
  000a3	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  000a8	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CJ@KGPMEDIC@mu2?3?3InstanceColosseum33?3?3Enter@
  000af	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  000b4	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  000bb	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  000c0	c7 44 24 28 12
	00 00 00	 mov	 DWORD PTR [rsp+40], 18
  000c8	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FF@PBMKLIIH@F?3?2Release_Branch?2Server?2Develo@
  000cf	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  000d4	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CJ@KGPMEDIC@mu2?3?3InstanceColosseum33?3?3Enter@
  000db	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  000e1	ba 02 00 00 00	 mov	 edx, 2
  000e6	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  000ed	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  000f2	66 ba 07 00	 mov	 dx, 7
  000f6	48 8b 4c 24 68	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  000fb	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  00101	90		 npad	 1
  00102	8b 44 24 60	 mov	 eax, DWORD PTR eError$[rsp]
  00106	eb 02		 jmp	 SHORT $LN1@EnterExecu
$LN2@EnterExecu:

; 19   : 
; 20   : 		//====================================================================================
; 21   : 		// 기획적으로 재진입 불가, 코드상으로는 재진입 가능하게 하였으나, 
; 22   : 		// "모든 캐릭터의 입장을 기다리고 있습니다." 라는 메세지만 뜨고 진행이 안됨
; 23   : 		//====================================================================================
; 24   : 
; 25   : 		//theMissionMapJoinManager.AddMissionMapJoin(player->GetCharId(), GetExecId(), res->arrivedLocation.indexPosition);
; 26   : 
; 27   : 		
; 28   : 
; 29   : 
; 30   : 
; 31   : 		return ErrorJoin::SUCCESS;

  00108	33 c0		 xor	 eax, eax
$LN1@EnterExecu:

; 32   : 	}

  0010a	48 81 c4 98 00
	00 00		 add	 rsp, 152		; 00000098H
  00111	c3		 ret	 0
?EnterExecution@InstanceColosseum33@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ENDP ; mu2::InstanceColosseum33::EnterExecution
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceWorldcross.h
;	COMDAT ?IsWorldcross@InstanceWorldcross@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsWorldcross@InstanceWorldcross@mu2@@UEBA_NXZ PROC	; mu2::InstanceWorldcross::IsWorldcross, COMDAT

; 31   : 		virtual Bool				IsWorldcross() const override { return true; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	b0 01		 mov	 al, 1
  00007	c3		 ret	 0
?IsWorldcross@InstanceWorldcross@mu2@@UEBA_NXZ ENDP	; mu2::InstanceWorldcross::IsWorldcross
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceSinglestage.h
;	COMDAT ?IsSingle@InstanceSinglestage@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsSingle@InstanceSinglestage@mu2@@UEBA_NXZ PROC	; mu2::InstanceSinglestage::IsSingle, COMDAT

; 16   : 		virtual Bool					IsSingle() const { return true; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	b0 01		 mov	 al, 1
  00007	c3		 ret	 0
?IsSingle@InstanceSinglestage@mu2@@UEBA_NXZ ENDP	; mu2::InstanceSinglestage::IsSingle
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Instance.h
;	COMDAT ?GetGroupKey@Instance@mu2@@UEBA?BIXZ
_TEXT	SEGMENT
this$ = 8
?GetGroupKey@Instance@mu2@@UEBA?BIXZ PROC		; mu2::Instance::GetGroupKey, COMDAT

; 83   : 		virtual const InstanceGroupKey	GetGroupKey() const { return 0; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	33 c0		 xor	 eax, eax
  00007	c3		 ret	 0
?GetGroupKey@Instance@mu2@@UEBA?BIXZ ENDP		; mu2::Instance::GetGroupKey
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Instance.h
;	COMDAT ?IsValid@Instance@mu2@@UEBA?B_NXZ
_TEXT	SEGMENT
tv74 = 32
this$ = 64
?IsValid@Instance@mu2@@UEBA?B_NXZ PROC			; mu2::Instance::IsValid, COMDAT

; 82   : 		virtual const Bool				IsValid() const override { return m_instanceId > 0 && __super::IsValid(); }

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H
  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	83 b8 84 00 00
	00 00		 cmp	 DWORD PTR [rax+132], 0
  00015	76 18		 jbe	 SHORT $LN3@IsValid
  00017	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0001c	e8 00 00 00 00	 call	 ?IsValid@Execution@mu2@@UEBA?B_NXZ ; mu2::Execution::IsValid
  00021	0f b6 c0	 movzx	 eax, al
  00024	85 c0		 test	 eax, eax
  00026	74 07		 je	 SHORT $LN3@IsValid
  00028	c6 44 24 20 01	 mov	 BYTE PTR tv74[rsp], 1
  0002d	eb 05		 jmp	 SHORT $LN4@IsValid
$LN3@IsValid:
  0002f	c6 44 24 20 00	 mov	 BYTE PTR tv74[rsp], 0
$LN4@IsValid:
  00034	0f b6 44 24 20	 movzx	 eax, BYTE PTR tv74[rsp]
  00039	48 83 c4 38	 add	 rsp, 56			; 00000038H
  0003d	c3		 ret	 0
?IsValid@Instance@mu2@@UEBA?B_NXZ ENDP			; mu2::Instance::IsValid
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Instance.h
;	COMDAT ?IsInstance@Instance@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsInstance@Instance@mu2@@UEBA_NXZ PROC			; mu2::Instance::IsInstance, COMDAT

; 80   : 		virtual Bool					IsInstance() const final { return true; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	b0 01		 mov	 al, 1
  00007	c3		 ret	 0
?IsInstance@Instance@mu2@@UEBA_NXZ ENDP			; mu2::Instance::IsInstance
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?IsNull@Execution@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsNull@Execution@mu2@@UEBA_NXZ PROC			; mu2::Execution::IsNull, COMDAT

; 86   : 		virtual Bool					IsNull() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsNull@Execution@mu2@@UEBA_NXZ ENDP			; mu2::Execution::IsNull
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?IsGroup@Execution@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsGroup@Execution@mu2@@UEBA_NXZ PROC			; mu2::Execution::IsGroup, COMDAT

; 85   : 		virtual Bool					IsGroup() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsGroup@Execution@mu2@@UEBA_NXZ ENDP			; mu2::Execution::IsGroup
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?IsTournamentEnter@Execution@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsTournamentEnter@Execution@mu2@@UEBA_NXZ PROC		; mu2::Execution::IsTournamentEnter, COMDAT

; 82   : 		virtual Bool					IsTournamentEnter() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsTournamentEnter@Execution@mu2@@UEBA_NXZ ENDP		; mu2::Execution::IsTournamentEnter
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?IsChannel@Execution@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsChannel@Execution@mu2@@UEBA_NXZ PROC			; mu2::Execution::IsChannel, COMDAT

; 80   : 		virtual Bool					IsChannel() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsChannel@Execution@mu2@@UEBA_NXZ ENDP			; mu2::Execution::IsChannel
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceColloseumPvp33.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceColloseumPvp33.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
