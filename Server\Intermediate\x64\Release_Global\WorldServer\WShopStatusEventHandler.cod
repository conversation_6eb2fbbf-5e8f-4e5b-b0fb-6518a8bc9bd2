; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	?onConflictProductVersion@IAsyncReceiverStatus@fcsa@@UEAAXXZ ; fcsa::IAsyncReceiverStatus::onConflictProductVersion
PUBLIC	??_GIAsyncReceiverStatus@fcsa@@UEAAPEAXI@Z	; fcsa::IAsyncReceiverStatus::`scalar deleting destructor'
PUBLIC	??0WShopStatusEventHandler@mu2@@QEAA@XZ		; mu2::WShopStatusEventHandler::WShopStatusEventHandler
PUBLIC	??1WShopStatusEventHandler@mu2@@UEAA@XZ		; mu2::WShopStatusEventHandler::~WShopStatusEventHandler
PUBLIC	?RequestWShopVersion@WShopStatusEventHandler@mu2@@QEAAXXZ ; mu2::WShopStatusEventHandler::RequestWShopVersion
PUBLIC	?onConflictProductVersion@WShopStatusEventHandler@mu2@@UEAAXXZ ; mu2::WShopStatusEventHandler::onConflictProductVersion
PUBLIC	??_GWShopStatusEventHandler@mu2@@UEAAPEAXI@Z	; mu2::WShopStatusEventHandler::`scalar deleting destructor'
PUBLIC	??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@		; `string'
PUBLIC	??_7IAsyncReceiverStatus@fcsa@@6B@		; fcsa::IAsyncReceiverStatus::`vftable'
PUBLIC	??_R4IAsyncReceiverStatus@fcsa@@6B@		; fcsa::IAsyncReceiverStatus::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVIAsyncReceiverStatus@fcsa@@@8		; fcsa::IAsyncReceiverStatus `RTTI Type Descriptor'
PUBLIC	??_R3IAsyncReceiverStatus@fcsa@@8		; fcsa::IAsyncReceiverStatus::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2IAsyncReceiverStatus@fcsa@@8		; fcsa::IAsyncReceiverStatus::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@IAsyncReceiverStatus@fcsa@@8	; fcsa::IAsyncReceiverStatus::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_7WShopStatusEventHandler@mu2@@6B@		; mu2::WShopStatusEventHandler::`vftable'
PUBLIC	?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A ; mu2::ISingleton<mu2::WShop>::inst
PUBLIC	??_C@_0DI@IKGHPNLP@WSHOP?$CI?$CJ?5?3?5IRequestWShopGetVersi@ ; `string'
PUBLIC	??_C@_0FM@PFDGGNI@F?3?2Release_Branch?2Server?2Develo@ ; `string'
PUBLIC	??_C@_0DC@BBFAGGCN@mu2?3?3WShopStatusEventHandler?3?3R@ ; `string'
PUBLIC	??_R4WShopStatusEventHandler@mu2@@6B@		; mu2::WShopStatusEventHandler::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVWShopStatusEventHandler@mu2@@@8		; mu2::WShopStatusEventHandler `RTTI Type Descriptor'
PUBLIC	??_R3WShopStatusEventHandler@mu2@@8		; mu2::WShopStatusEventHandler::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2WShopStatusEventHandler@mu2@@8		; mu2::WShopStatusEventHandler::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@WShopStatusEventHandler@mu2@@8	; mu2::WShopStatusEventHandler::`RTTI Base Class Descriptor at (0,-1,0,64)'
EXTRN	??3@YAXPEAX_K@Z:PROC				; operator delete
EXTRN	atexit:PROC
EXTRN	__imp_GetStdHandle:PROC
EXTRN	__imp_SetConsoleTextAttribute:PROC
EXTRN	?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ:PROC	; mu2::Logger::Logging
EXTRN	??_EIAsyncReceiverStatus@fcsa@@UEAAPEAXI@Z:PROC	; fcsa::IAsyncReceiverStatus::`vector deleting destructor'
EXTRN	??_EWShopStatusEventHandler@mu2@@UEAAPEAXI@Z:PROC ; mu2::WShopStatusEventHandler::`vector deleting destructor'
EXTRN	??1WShop@mu2@@UEAA@XZ:PROC			; mu2::WShop::~WShop
EXTRN	??0WShop@mu2@@AEAA@XZ:PROC			; mu2::WShop::WShop
EXTRN	??_7type_info@@6B@:BYTE				; type_info::`vftable'
;	COMDAT ?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A
_BSS	SEGMENT
?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A DB 068H DUP (?) ; mu2::ISingleton<mu2::WShop>::inst
_BSS	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GIAsyncReceiverStatus@fcsa@@UEAAPEAXI@Z DD imagerel $LN10
	DD	imagerel $LN10+65
	DD	imagerel $unwind$??_GIAsyncReceiverStatus@fcsa@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?RequestWShopVersion@WShopStatusEventHandler@mu2@@QEAAXXZ DD imagerel $LN12
	DD	imagerel $LN12+270
	DD	imagerel $unwind$?RequestWShopVersion@WShopStatusEventHandler@mu2@@QEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?onConflictProductVersion@WShopStatusEventHandler@mu2@@UEAAXXZ DD imagerel $LN3
	DD	imagerel $LN3+25
	DD	imagerel $unwind$?onConflictProductVersion@WShopStatusEventHandler@mu2@@UEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GWShopStatusEventHandler@mu2@@UEAAPEAXI@Z DD imagerel $LN5
	DD	imagerel $LN5+60
	DD	imagerel $unwind$??_GWShopStatusEventHandler@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__E?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A@@YAXXZ DD imagerel ??__E?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A@@YAXXZ
	DD	imagerel ??__E?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A@@YAXXZ+34
	DD	imagerel $unwind$??__E?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__F?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A@@YAXXZ DD imagerel ??__F?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A@@YAXXZ
	DD	imagerel ??__F?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A@@YAXXZ+22
	DD	imagerel $unwind$??__F?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A@@YAXXZ
pdata	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
??inst$initializer$@?$ISingleton@VWShop@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA DQ FLAT:??__E?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A@@YAXXZ ; ??inst$initializer$@?$ISingleton@VWShop@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA
CRT$XCU	ENDS
;	COMDAT ??_R1A@?0A@EA@WShopStatusEventHandler@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@WShopStatusEventHandler@mu2@@8 DD imagerel ??_R0?AVWShopStatusEventHandler@mu2@@@8 ; mu2::WShopStatusEventHandler::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3WShopStatusEventHandler@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2WShopStatusEventHandler@mu2@@8
rdata$r	SEGMENT
??_R2WShopStatusEventHandler@mu2@@8 DD imagerel ??_R1A@?0A@EA@WShopStatusEventHandler@mu2@@8 ; mu2::WShopStatusEventHandler::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@IAsyncReceiverStatus@fcsa@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3WShopStatusEventHandler@mu2@@8
rdata$r	SEGMENT
??_R3WShopStatusEventHandler@mu2@@8 DD 00H		; mu2::WShopStatusEventHandler::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2WShopStatusEventHandler@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVWShopStatusEventHandler@mu2@@@8
data$rs	SEGMENT
??_R0?AVWShopStatusEventHandler@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::WShopStatusEventHandler `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVWShopStatusEventHandler@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4WShopStatusEventHandler@mu2@@6B@
rdata$r	SEGMENT
??_R4WShopStatusEventHandler@mu2@@6B@ DD 01H		; mu2::WShopStatusEventHandler::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVWShopStatusEventHandler@mu2@@@8
	DD	imagerel ??_R3WShopStatusEventHandler@mu2@@8
	DD	imagerel ??_R4WShopStatusEventHandler@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_C@_0DC@BBFAGGCN@mu2?3?3WShopStatusEventHandler?3?3R@
CONST	SEGMENT
??_C@_0DC@BBFAGGCN@mu2?3?3WShopStatusEventHandler?3?3R@ DB 'mu2::WShopSta'
	DB	'tusEventHandler::RequestWShopVersion', 00H	; `string'
CONST	ENDS
;	COMDAT ??_C@_0FM@PFDGGNI@F?3?2Release_Branch?2Server?2Develo@
CONST	SEGMENT
??_C@_0FM@PFDGGNI@F?3?2Release_Branch?2Server?2Develo@ DB 'F:\Release_Bra'
	DB	'nch\Server\Development\Frontend\WorldServer\WShop\WShopStatus'
	DB	'EventHandler.cpp', 00H			; `string'
CONST	ENDS
;	COMDAT ??_C@_0DI@IKGHPNLP@WSHOP?$CI?$CJ?5?3?5IRequestWShopGetVersi@
CONST	SEGMENT
??_C@_0DI@IKGHPNLP@WSHOP?$CI?$CJ?5?3?5IRequestWShopGetVersi@ DB 'WSHOP() '
	DB	': IRequestWShopGetVersion> sendAsync() failed.!', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7WShopStatusEventHandler@mu2@@6B@
CONST	SEGMENT
??_7WShopStatusEventHandler@mu2@@6B@ DQ FLAT:??_R4WShopStatusEventHandler@mu2@@6B@ ; mu2::WShopStatusEventHandler::`vftable'
	DQ	FLAT:??_EWShopStatusEventHandler@mu2@@UEAAPEAXI@Z
	DQ	FLAT:?onConflictProductVersion@WShopStatusEventHandler@mu2@@UEAAXXZ
CONST	ENDS
;	COMDAT ??_R1A@?0A@EA@IAsyncReceiverStatus@fcsa@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@IAsyncReceiverStatus@fcsa@@8 DD imagerel ??_R0?AVIAsyncReceiverStatus@fcsa@@@8 ; fcsa::IAsyncReceiverStatus::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3IAsyncReceiverStatus@fcsa@@8
rdata$r	ENDS
;	COMDAT ??_R2IAsyncReceiverStatus@fcsa@@8
rdata$r	SEGMENT
??_R2IAsyncReceiverStatus@fcsa@@8 DD imagerel ??_R1A@?0A@EA@IAsyncReceiverStatus@fcsa@@8 ; fcsa::IAsyncReceiverStatus::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3IAsyncReceiverStatus@fcsa@@8
rdata$r	SEGMENT
??_R3IAsyncReceiverStatus@fcsa@@8 DD 00H		; fcsa::IAsyncReceiverStatus::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2IAsyncReceiverStatus@fcsa@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVIAsyncReceiverStatus@fcsa@@@8
data$rs	SEGMENT
??_R0?AVIAsyncReceiverStatus@fcsa@@@8 DQ FLAT:??_7type_info@@6B@ ; fcsa::IAsyncReceiverStatus `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVIAsyncReceiverStatus@fcsa@@', 00H
data$rs	ENDS
;	COMDAT ??_R4IAsyncReceiverStatus@fcsa@@6B@
rdata$r	SEGMENT
??_R4IAsyncReceiverStatus@fcsa@@6B@ DD 01H		; fcsa::IAsyncReceiverStatus::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVIAsyncReceiverStatus@fcsa@@@8
	DD	imagerel ??_R3IAsyncReceiverStatus@fcsa@@8
	DD	imagerel ??_R4IAsyncReceiverStatus@fcsa@@6B@
rdata$r	ENDS
;	COMDAT ??_7IAsyncReceiverStatus@fcsa@@6B@
CONST	SEGMENT
??_7IAsyncReceiverStatus@fcsa@@6B@ DQ FLAT:??_R4IAsyncReceiverStatus@fcsa@@6B@ ; fcsa::IAsyncReceiverStatus::`vftable'
	DQ	FLAT:??_EIAsyncReceiverStatus@fcsa@@UEAAPEAXI@Z
	DQ	FLAT:?onConflictProductVersion@IAsyncReceiverStatus@fcsa@@UEAAXXZ
CONST	ENDS
;	COMDAT ??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
CONST	SEGMENT
??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@ DB 'g', 00H, 'a', 00H, 'm', 00H, 'e'
	DB	00H, 00H, 00H				; `string'
CONST	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__F?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__E?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GWShopStatusEventHandler@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?onConflictProductVersion@WShopStatusEventHandler@mu2@@UEAAXXZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?RequestWShopVersion@WShopStatusEventHandler@mu2@@QEAAXXZ DD 020c01H
	DD	011010cH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GIAsyncReceiverStatus@fcsa@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
; Function compile flags: /Odtp
;	COMDAT ??__F?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A@@YAXXZ
text$yd	SEGMENT
??__F?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A@@YAXXZ PROC ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::WShop>::inst'', COMDAT
  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A ; mu2::ISingleton<mu2::WShop>::inst
  0000b	e8 00 00 00 00	 call	 ??1WShop@mu2@@UEAA@XZ	; mu2::WShop::~WShop
  00010	90		 npad	 1
  00011	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00015	c3		 ret	 0
??__F?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A@@YAXXZ ENDP ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::WShop>::inst''
text$yd	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??__E?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A@@YAXXZ
text$di	SEGMENT
??__E?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A@@YAXXZ PROC ; `dynamic initializer for 'mu2::ISingleton<mu2::WShop>::inst'', COMDAT

; 90   : template<typename T> T ISingleton<T>::inst;

  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A ; mu2::ISingleton<mu2::WShop>::inst
  0000b	e8 00 00 00 00	 call	 ??0WShop@mu2@@AEAA@XZ	; mu2::WShop::WShop
  00010	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??__F?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A@@YAXXZ ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::WShop>::inst''
  00017	e8 00 00 00 00	 call	 atexit
  0001c	90		 npad	 1
  0001d	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00021	c3		 ret	 0
??__E?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A@@YAXXZ ENDP ; `dynamic initializer for 'mu2::ISingleton<mu2::WShop>::inst''
text$di	ENDS
; Function compile flags: /Odtp
;	COMDAT ??_GWShopStatusEventHandler@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GWShopStatusEventHandler@mu2@@UEAAPEAXI@Z PROC	; mu2::WShopStatusEventHandler::`scalar deleting destructor', COMDAT
$LN5:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00012	e8 00 00 00 00	 call	 ??1WShopStatusEventHandler@mu2@@UEAA@XZ ; mu2::WShopStatusEventHandler::~WShopStatusEventHandler
  00017	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  0001b	83 e0 01	 and	 eax, 1
  0001e	85 c0		 test	 eax, eax
  00020	74 10		 je	 SHORT $LN2@scalar
  00022	ba 10 00 00 00	 mov	 edx, 16
  00027	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0002c	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00031	90		 npad	 1
$LN2@scalar:
  00032	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00037	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0003b	c3		 ret	 0
??_GWShopStatusEventHandler@mu2@@UEAAPEAXI@Z ENDP	; mu2::WShopStatusEventHandler::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopStatusEventHandler.cpp
;	COMDAT ?onConflictProductVersion@WShopStatusEventHandler@mu2@@UEAAXXZ
_TEXT	SEGMENT
this$ = 48
?onConflictProductVersion@WShopStatusEventHandler@mu2@@UEAAXXZ PROC ; mu2::WShopStatusEventHandler::onConflictProductVersion, COMDAT

; 32   : {

$LN3:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 33   : 	RequestWShopVersion();	

  00009	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0000e	e8 00 00 00 00	 call	 ?RequestWShopVersion@WShopStatusEventHandler@mu2@@QEAAXXZ ; mu2::WShopStatusEventHandler::RequestWShopVersion
  00013	90		 npad	 1

; 34   : }

  00014	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00018	c3		 ret	 0
?onConflictProductVersion@WShopStatusEventHandler@mu2@@UEAAXXZ ENDP ; mu2::WShopStatusEventHandler::onConflictProductVersion
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopStatusEventHandler.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopStatusEventHandler.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopStatusEventHandler.cpp
;	COMDAT ?RequestWShopVersion@WShopStatusEventHandler@mu2@@QEAAXXZ
_TEXT	SEGMENT
fcsReq$ = 64
tv71 = 72
wShopBilling$ = 80
hConsole$1 = 88
$T2 = 96
$T3 = 104
$T4 = 112
$T5 = 120
this$ = 144
?RequestWShopVersion@WShopStatusEventHandler@mu2@@QEAAXXZ PROC ; mu2::WShopStatusEventHandler::RequestWShopVersion, COMDAT

; 19   : {

$LN12:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  0000c	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A ; mu2::ISingleton<mu2::WShop>::inst
  00013	48 89 44 24 60	 mov	 QWORD PTR $T2[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.h

; 34   : 	fcsa::IWShopBilling*	GetBilling() { return m_billing; }

  00018	48 8b 44 24 60	 mov	 rax, QWORD PTR $T2[rsp]
  0001d	48 8b 40 28	 mov	 rax, QWORD PTR [rax+40]
  00021	48 89 44 24 68	 mov	 QWORD PTR $T3[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopStatusEventHandler.cpp

; 20   : fcsa::IWShopBilling* wShopBilling = theWShop.GetBilling();

  00026	48 8b 44 24 68	 mov	 rax, QWORD PTR $T3[rsp]
  0002b	48 89 44 24 50	 mov	 QWORD PTR wShopBilling$[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  00030	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VWShop@mu2@@@mu2@@1VWShop@2@A ; mu2::ISingleton<mu2::WShop>::inst
  00037	48 89 44 24 70	 mov	 QWORD PTR $T4[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShop.h

; 34   : 	fcsa::IWShopBilling*	GetBilling() { return m_billing; }

  0003c	48 8b 44 24 70	 mov	 rax, QWORD PTR $T4[rsp]
  00041	48 8b 40 28	 mov	 rax, QWORD PTR [rax+40]
  00045	48 89 44 24 78	 mov	 QWORD PTR $T5[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopStatusEventHandler.cpp

; 22   : 	fcsa::IRequestWShopGetVersion* fcsReq = theWShop.GetBilling()->createRequestWShopGetVersion();

  0004a	48 8b 44 24 78	 mov	 rax, QWORD PTR $T5[rsp]
  0004f	48 89 44 24 48	 mov	 QWORD PTR tv71[rsp], rax
  00054	48 8b 44 24 48	 mov	 rax, QWORD PTR tv71[rsp]
  00059	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0005c	48 8b 4c 24 48	 mov	 rcx, QWORD PTR tv71[rsp]
  00061	ff 90 48 01 00
	00		 call	 QWORD PTR [rax+328]
  00067	48 89 44 24 40	 mov	 QWORD PTR fcsReq$[rsp], rax

; 23   : 
; 24   : 	if (false == wShopBilling->sendAsync(fcsReq))

  0006c	48 8b 44 24 50	 mov	 rax, QWORD PTR wShopBilling$[rsp]
  00071	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00074	48 8b 54 24 40	 mov	 rdx, QWORD PTR fcsReq$[rsp]
  00079	48 8b 4c 24 50	 mov	 rcx, QWORD PTR wShopBilling$[rsp]
  0007e	ff 50 78	 call	 QWORD PTR [rax+120]
  00081	0f b6 c0	 movzx	 eax, al
  00084	85 c0		 test	 eax, eax
  00086	75 6d		 jne	 SHORT $LN2@RequestWSh

; 25   : 	{
; 26   : 		MU2_ERROR_LOG(LogCategory::WSHOP, "WSHOP() : IRequestWShopGetVersion> sendAsync() failed.!");

  00088	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  0008d	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  00093	48 89 44 24 58	 mov	 QWORD PTR hConsole$1[rsp], rax
  00098	66 ba 0d 00	 mov	 dx, 13
  0009c	48 8b 4c 24 58	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  000a1	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000a7	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0DI@IKGHPNLP@WSHOP?$CI?$CJ?5?3?5IRequestWShopGetVersi@
  000ae	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  000b3	c7 44 24 28 1a
	00 00 00	 mov	 DWORD PTR [rsp+40], 26
  000bb	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FM@PFDGGNI@F?3?2Release_Branch?2Server?2Develo@
  000c2	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  000c7	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0DC@BBFAGGCN@mu2?3?3WShopStatusEventHandler?3?3R@
  000ce	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  000d4	ba 14 00 00 00	 mov	 edx, 20
  000d9	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  000e0	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  000e5	66 ba 07 00	 mov	 dx, 7
  000e9	48 8b 4c 24 58	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  000ee	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000f4	90		 npad	 1
$LN2@RequestWSh:

; 27   : 	}
; 28   : 	fcsReq->release();

  000f5	48 8b 44 24 40	 mov	 rax, QWORD PTR fcsReq$[rsp]
  000fa	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000fd	48 8b 4c 24 40	 mov	 rcx, QWORD PTR fcsReq$[rsp]
  00102	ff 50 08	 call	 QWORD PTR [rax+8]
  00105	90		 npad	 1

; 29   : }

  00106	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  0010d	c3		 ret	 0
?RequestWShopVersion@WShopStatusEventHandler@mu2@@QEAAXXZ ENDP ; mu2::WShopStatusEventHandler::RequestWShopVersion
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopStatusEventHandler.cpp
; File F:\Release_Branch\Vendor\FCSAdapter\include\interface\fcs_adapter_interfaces.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopStatusEventHandler.cpp
;	COMDAT ??1WShopStatusEventHandler@mu2@@UEAA@XZ
_TEXT	SEGMENT
this$ = 8
??1WShopStatusEventHandler@mu2@@UEAA@XZ PROC		; mu2::WShopStatusEventHandler::~WShopStatusEventHandler, COMDAT

; 15   : {

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0000a	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7WShopStatusEventHandler@mu2@@6B@
  00011	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Vendor\FCSAdapter\include\interface\fcs_adapter_interfaces.h

; 954  : 		virtual ~IAsyncReceiverStatus(){}

  00014	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00019	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7IAsyncReceiverStatus@fcsa@@6B@
  00020	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopStatusEventHandler.cpp

; 16   : }

  00023	c3		 ret	 0
??1WShopStatusEventHandler@mu2@@UEAA@XZ ENDP		; mu2::WShopStatusEventHandler::~WShopStatusEventHandler
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopStatusEventHandler.cpp
; File F:\Release_Branch\Vendor\FCSAdapter\include\interface\fcs_adapter_interfaces.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopStatusEventHandler.cpp
;	COMDAT ??0WShopStatusEventHandler@mu2@@QEAA@XZ
_TEXT	SEGMENT
this$ = 8
??0WShopStatusEventHandler@mu2@@QEAA@XZ PROC		; mu2::WShopStatusEventHandler::WShopStatusEventHandler, COMDAT

; 11   : {

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
; File F:\Release_Branch\Vendor\FCSAdapter\include\interface\fcs_adapter_interfaces.h

; 953  : 		IAsyncReceiverStatus(){}

  00005	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0000a	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7IAsyncReceiverStatus@fcsa@@6B@
  00011	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopStatusEventHandler.cpp

; 11   : {

  00014	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00019	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7WShopStatusEventHandler@mu2@@6B@
  00020	48 89 08	 mov	 QWORD PTR [rax], rcx

; 10   : 	: m_wShopVersion(0)

  00023	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00028	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0

; 12   : }

  00030	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00035	c3		 ret	 0
??0WShopStatusEventHandler@mu2@@QEAA@XZ ENDP		; mu2::WShopStatusEventHandler::WShopStatusEventHandler
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Vendor\FCSAdapter\include\interface\fcs_adapter_interfaces.h
;	COMDAT ??_GIAsyncReceiverStatus@fcsa@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GIAsyncReceiverStatus@fcsa@@UEAAPEAXI@Z PROC		; fcsa::IAsyncReceiverStatus::`scalar deleting destructor', COMDAT
$LN10:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 954  : 		virtual ~IAsyncReceiverStatus(){}

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7IAsyncReceiverStatus@fcsa@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx
  0001c	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00020	83 e0 01	 and	 eax, 1
  00023	85 c0		 test	 eax, eax
  00025	74 10		 je	 SHORT $LN2@scalar
  00027	ba 08 00 00 00	 mov	 edx, 8
  0002c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00031	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00036	90		 npad	 1
$LN2@scalar:
  00037	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003c	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00040	c3		 ret	 0
??_GIAsyncReceiverStatus@fcsa@@UEAAPEAXI@Z ENDP		; fcsa::IAsyncReceiverStatus::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Vendor\FCSAdapter\include\interface\fcs_adapter_interfaces.h
;	COMDAT ?onConflictProductVersion@IAsyncReceiverStatus@fcsa@@UEAAXXZ
_TEXT	SEGMENT
this$ = 8
?onConflictProductVersion@IAsyncReceiverStatus@fcsa@@UEAAXXZ PROC ; fcsa::IAsyncReceiverStatus::onConflictProductVersion, COMDAT

; 957  : 		virtual void onConflictProductVersion() {}

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?onConflictProductVersion@IAsyncReceiverStatus@fcsa@@UEAAXXZ ENDP ; fcsa::IAsyncReceiverStatus::onConflictProductVersion
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopStatusEventHandler.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopStatusEventHandler.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
