; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	??0exception@std@@QEAA@AEBV01@@Z		; std::exception::exception
PUBLIC	?what@exception@std@@UEBAPEBDXZ			; std::exception::what
PUBLIC	??_Gexception@std@@UEAAPEAXI@Z			; std::exception::`scalar deleting destructor'
PUBLIC	??0bad_alloc@std@@QEAA@AEBV01@@Z		; std::bad_alloc::bad_alloc
PUBLIC	??_Gbad_alloc@std@@UEAAPEAXI@Z			; std::bad_alloc::`scalar deleting destructor'
PUBL<PERSON>	??0bad_array_new_length@std@@QEAA@XZ		; std::bad_array_new_length::bad_array_new_length
PUBLIC	??1bad_array_new_length@std@@UEAA@XZ		; std::bad_array_new_length::~bad_array_new_length
PUBLIC	??0bad_array_new_length@std@@QEAA@AEBV01@@Z	; std::bad_array_new_length::bad_array_new_length
PUBLIC	??_Gbad_array_new_length@std@@UEAAPEAXI@Z	; std::bad_array_new_length::`scalar deleting destructor'
PUBLIC	?_Throw_bad_array_new_length@std@@YAXXZ		; std::_Throw_bad_array_new_length
PUBLIC	?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
PUBLIC	?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Tidy_deallocate
PUBLIC	??1?$ISingleton@VtheManagerCounter@mu2@@@mu2@@UEAA@XZ ; mu2::ISingleton<mu2::theManagerCounter>::~ISingleton<mu2::theManagerCounter>
PUBLIC	??_G?$ISingleton@VtheManagerCounter@mu2@@@mu2@@UEAAPEAXI@Z ; mu2::ISingleton<mu2::theManagerCounter>::`scalar deleting destructor'
PUBLIC	?allocate@?$allocator@U_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@QEAAPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@_K@Z ; std::allocator<Concurrency::details::_Split_order_list_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >::_Node>::allocate
PUBLIC	??0?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAA@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@std@@@Z ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >
PUBLIC	??1?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAA@XZ ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >::~_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >
PUBLIC	?clear@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAAXXZ ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >::clear
PUBLIC	?_Erase@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAAXPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@23@@Z ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >::_Erase
PUBLIC	??1?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAA@XZ ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > >,0> >::~_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > >,0> >
PUBLIC	?clear@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAXXZ ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > >,0> >::clear
PUBLIC	?rehash@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAX_K@Z ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > >,0> >::rehash
PUBLIC	?_Init@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAXXZ ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > >,0> >::_Init
PUBLIC	?_Set_bucket@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAX_KV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@Z ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > >,0> >::_Set_bucket
PUBLIC	?allocate@?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@QEAAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@2@_K@Z ; std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > > > >::allocate
PUBLIC	??0?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@_KAEBU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@AEBU?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@3@AEBV?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@3@@Z ; Concurrency::concurrent_unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >::concurrent_unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >
PUBLIC	??1?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@XZ ; Concurrency::concurrent_unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >::~concurrent_unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >
PUBLIC	?allocate@?$allocator@U_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@QEAAPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@_K@Z ; std::allocator<Concurrency::details::_Split_order_list_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > >::_Node>::allocate
PUBLIC	??0?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAA@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@std@@@Z ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > >::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > >
PUBLIC	??1?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAA@XZ ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > >::~_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > >
PUBLIC	?clear@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAAXXZ ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > >::clear
PUBLIC	?_Erase@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAAXPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@23@@Z ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > >::_Erase
PUBLIC	??1?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAA@XZ ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,__int64 volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > >,0> >::~_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,__int64 volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > >,0> >
PUBLIC	?clear@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAXXZ ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,__int64 volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > >,0> >::clear
PUBLIC	?rehash@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAX_K@Z ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,__int64 volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > >,0> >::rehash
PUBLIC	?_Init@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAXXZ ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,__int64 volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > >,0> >::_Init
PUBLIC	?_Set_bucket@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAX_KV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@Z ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,__int64 volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > >,0> >::_Set_bucket
PUBLIC	?allocate@?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@QEAAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@2@_K@Z ; std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > > > >::allocate
PUBLIC	?Init@theManagerCounter@mu2@@UEAA_NPEAX@Z	; mu2::theManagerCounter::Init
PUBLIC	?UnInit@theManagerCounter@mu2@@UEAA_NXZ		; mu2::theManagerCounter::UnInit
PUBLIC	??0theManagerCounter@mu2@@QEAA@XZ		; mu2::theManagerCounter::theManagerCounter
PUBLIC	??_GtheManagerCounter@mu2@@UEAAPEAXI@Z		; mu2::theManagerCounter::`scalar deleting destructor'
PUBLIC	??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
PUBLIC	??$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@0@@Z ; std::_Uninitialized_value_construct_n<std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > > > > >
PUBLIC	??$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@0@@Z ; std::_Uninitialized_value_construct_n<std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > > > > >
PUBLIC	??1?$_Uninitialized_backout_al@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@QEAA@XZ ; std::_Uninitialized_backout_al<std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > > > > >::~_Uninitialized_backout_al<std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > > > > >
PUBLIC	??$_Emplace_back@$$V@?$_Uninitialized_backout_al@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@QEAAXXZ ; std::_Uninitialized_backout_al<std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > > > > >::_Emplace_back<>
PUBLIC	??1?$_Uninitialized_backout_al@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@QEAA@XZ ; std::_Uninitialized_backout_al<std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > > > > >::~_Uninitialized_backout_al<std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > > > > >
PUBLIC	??$_Emplace_back@$$V@?$_Uninitialized_backout_al@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@QEAAXXZ ; std::_Uninitialized_backout_al<std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > > > > >::_Emplace_back<>
PUBLIC	??_7exception@std@@6B@				; std::exception::`vftable'
PUBLIC	??_C@_0BC@EOODALEL@Unknown?5exception@		; `string'
PUBLIC	??_7bad_alloc@std@@6B@				; std::bad_alloc::`vftable'
PUBLIC	??_7bad_array_new_length@std@@6B@		; std::bad_array_new_length::`vftable'
PUBLIC	??_C@_0BF@KINCDENJ@bad?5array?5new?5length@	; `string'
PUBLIC	??_R0?AVexception@std@@@8			; std::exception `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
PUBLIC	_TI3?AVbad_array_new_length@std@@
PUBLIC	_CTA3?AVbad_array_new_length@std@@
PUBLIC	??_R0?AVbad_array_new_length@std@@@8		; std::bad_array_new_length `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
PUBLIC	??_R0?AVbad_alloc@std@@@8			; std::bad_alloc `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
PUBLIC	?inst@?$ISingleton@VtheManagerCounter@mu2@@@mu2@@1VtheManagerCounter@2@A ; mu2::ISingleton<mu2::theManagerCounter>::inst
PUBLIC	??_7?$ISingleton@VtheManagerCounter@mu2@@@mu2@@6B@ ; mu2::ISingleton<mu2::theManagerCounter>::`vftable'
PUBLIC	??_7theManagerCounter@mu2@@6B@			; mu2::theManagerCounter::`vftable'
PUBLIC	??_C@_0BK@KDCGEAPC@invalid?5number?5of?5buckets@ ; `string'
PUBLIC	??_R4exception@std@@6B@				; std::exception::`RTTI Complete Object Locator'
PUBLIC	??_R3exception@std@@8				; std::exception::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2exception@std@@8				; std::exception::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@exception@std@@8			; std::exception::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4bad_array_new_length@std@@6B@		; std::bad_array_new_length::`RTTI Complete Object Locator'
PUBLIC	??_R3bad_array_new_length@std@@8		; std::bad_array_new_length::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2bad_array_new_length@std@@8		; std::bad_array_new_length::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@bad_array_new_length@std@@8	; std::bad_array_new_length::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@bad_alloc@std@@8			; std::bad_alloc::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R3bad_alloc@std@@8				; std::bad_alloc::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2bad_alloc@std@@8				; std::bad_alloc::`RTTI Base Class Array'
PUBLIC	??_R4bad_alloc@std@@6B@				; std::bad_alloc::`RTTI Complete Object Locator'
PUBLIC	??_R4theManagerCounter@mu2@@6B@			; mu2::theManagerCounter::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVtheManagerCounter@mu2@@@8		; mu2::theManagerCounter `RTTI Type Descriptor'
PUBLIC	??_R3theManagerCounter@mu2@@8			; mu2::theManagerCounter::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2theManagerCounter@mu2@@8			; mu2::theManagerCounter::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@theManagerCounter@mu2@@8		; mu2::theManagerCounter::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@?$ISingleton@VtheManagerCounter@mu2@@@mu2@@8 ; mu2::ISingleton<mu2::theManagerCounter>::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R0?AV?$ISingleton@VtheManagerCounter@mu2@@@mu2@@@8 ; mu2::ISingleton<mu2::theManagerCounter> `RTTI Type Descriptor'
PUBLIC	??_R3?$ISingleton@VtheManagerCounter@mu2@@@mu2@@8 ; mu2::ISingleton<mu2::theManagerCounter>::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2?$ISingleton@VtheManagerCounter@mu2@@@mu2@@8 ; mu2::ISingleton<mu2::theManagerCounter>::`RTTI Base Class Array'
PUBLIC	??_R4?$ISingleton@VtheManagerCounter@mu2@@@mu2@@6B@ ; mu2::ISingleton<mu2::theManagerCounter>::`RTTI Complete Object Locator'
PUBLIC	__real@40800000
EXTRN	_purecall:PROC
EXTRN	??2@YAPEAX_K@Z:PROC				; operator new
EXTRN	??3@YAXPEAX_K@Z:PROC				; operator delete
EXTRN	atexit:PROC
EXTRN	__std_terminate:PROC
EXTRN	_invoke_watson:PROC
EXTRN	memset:PROC
EXTRN	__std_exception_copy:PROC
EXTRN	__std_exception_destroy:PROC
EXTRN	??_Eexception@std@@UEAAPEAXI@Z:PROC		; std::exception::`vector deleting destructor'
EXTRN	??_Ebad_alloc@std@@UEAAPEAXI@Z:PROC		; std::bad_alloc::`vector deleting destructor'
EXTRN	??_Ebad_array_new_length@std@@UEAAPEAXI@Z:PROC	; std::bad_array_new_length::`vector deleting destructor'
EXTRN	?_Xout_of_range@std@@YAXPEBD@Z:PROC		; std::_Xout_of_range
EXTRN	??_E?$ISingleton@VtheManagerCounter@mu2@@@mu2@@UEAAPEAXI@Z:PROC ; mu2::ISingleton<mu2::theManagerCounter>::`vector deleting destructor'
EXTRN	??_EtheManagerCounter@mu2@@UEAAPEAXI@Z:PROC	; mu2::theManagerCounter::`vector deleting destructor'
EXTRN	_CxxThrowException:PROC
EXTRN	__CxxFrameHandler4:PROC
EXTRN	??_7type_info@@6B@:BYTE				; type_info::`vftable'
EXTRN	_fltused:DWORD
;	COMDAT ?inst@?$ISingleton@VtheManagerCounter@mu2@@@mu2@@1VtheManagerCounter@2@A
_BSS	SEGMENT
?inst@?$ISingleton@VtheManagerCounter@mu2@@@mu2@@1VtheManagerCounter@2@A DB 0480H DUP (?) ; mu2::ISingleton<mu2::theManagerCounter>::inst
_BSS	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0exception@std@@QEAA@AEBV01@@Z DD imagerel $LN4
	DD	imagerel $LN4+89
	DD	imagerel $unwind$??0exception@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?what@exception@std@@UEBAPEBDXZ DD imagerel $LN5
	DD	imagerel $LN5+56
	DD	imagerel $unwind$?what@exception@std@@UEBAPEBDXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gexception@std@@UEAAPEAXI@Z DD imagerel $LN10
	DD	imagerel $LN10+83
	DD	imagerel $unwind$??_Gexception@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z DD imagerel $LN9
	DD	imagerel $LN9+104
	DD	imagerel $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+83
	DD	imagerel $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_array_new_length@std@@QEAA@XZ DD imagerel $LN14
	DD	imagerel $LN14+95
	DD	imagerel $unwind$??0bad_array_new_length@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1bad_array_new_length@std@@UEAA@XZ DD imagerel $LN14
	DD	imagerel $LN14+47
	DD	imagerel $unwind$??1bad_array_new_length@std@@UEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z DD imagerel $LN14
	DD	imagerel $LN14+54
	DD	imagerel $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z DD imagerel $LN20
	DD	imagerel $LN20+83
	DD	imagerel $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Throw_bad_array_new_length@std@@YAXXZ DD imagerel $LN3
	DD	imagerel $LN3+37
	DD	imagerel $unwind$?_Throw_bad_array_new_length@std@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z DD imagerel $LN5
	DD	imagerel $LN5+159
	DD	imagerel $unwind$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ DD imagerel $LN62
	DD	imagerel $LN62+257
	DD	imagerel $unwind$?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_G?$ISingleton@VtheManagerCounter@mu2@@@mu2@@UEAAPEAXI@Z DD imagerel $LN10
	DD	imagerel $LN10+65
	DD	imagerel $unwind$??_G?$ISingleton@VtheManagerCounter@mu2@@@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?allocate@?$allocator@U_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@QEAAPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@_K@Z DD imagerel $LN13
	DD	imagerel $LN13+160
	DD	imagerel $unwind$?allocate@?$allocator@U_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@QEAAPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAA@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@std@@@Z DD imagerel $LN29
	DD	imagerel $LN29+119
	DD	imagerel $unwind$??0?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAA@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@std@@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAA@XZ DD imagerel $LN251
	DD	imagerel $LN251+65
	DD	imagerel $unwind$??1?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?clear@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAAXXZ DD imagerel $LN124
	DD	imagerel $LN124+122
	DD	imagerel $unwind$?clear@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Erase@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAAXPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@23@@Z DD imagerel $LN124
	DD	imagerel $LN124+186
	DD	imagerel $unwind$?_Erase@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAAXPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@23@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAA@XZ DD imagerel $LN225
	DD	imagerel $LN225+354
	DD	imagerel $unwind$??1?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?clear@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAXXZ DD imagerel $LN209
	DD	imagerel $LN209+315
	DD	imagerel $unwind$?clear@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?rehash@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAX_K@Z DD imagerel $LN15
	DD	imagerel $LN15+217
	DD	imagerel $unwind$?rehash@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAX_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Init@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAXXZ DD imagerel $LN115
	DD	imagerel $LN115+84
	DD	imagerel $unwind$?_Init@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Set_bucket@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAX_KV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@Z DD imagerel $LN91
	DD	imagerel $LN91+479
	DD	imagerel $unwind$?_Set_bucket@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAX_KV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?allocate@?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@QEAAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@2@_K@Z DD imagerel $LN13
	DD	imagerel $LN13+163
	DD	imagerel $unwind$?allocate@?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@QEAAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@2@_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@_KAEBU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@AEBU?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@3@AEBV?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@3@@Z DD imagerel $LN150
	DD	imagerel $LN150+153
	DD	imagerel $unwind$??0?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@_KAEBU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@AEBU?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@3@AEBV?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@3@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$1@?0???0?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@_KAEBU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@AEBU?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@3@AEBV?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@3@@Z@4HA DD imagerel ?dtor$1@?0???0?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@_KAEBU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@AEBU?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@3@AEBV?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@3@@Z@4HA
	DD	imagerel ?dtor$1@?0???0?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@_KAEBU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@AEBU?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@3@AEBV?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@3@@Z@4HA+31
	DD	imagerel $unwind$?dtor$1@?0???0?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@_KAEBU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@AEBU?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@3@AEBV?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@3@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???0?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@_KAEBU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@AEBU?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@3@AEBV?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@3@@Z@4HA DD imagerel ?dtor$0@?0???0?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@_KAEBU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@AEBU?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@3@AEBV?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@3@@Z@4HA
	DD	imagerel ?dtor$0@?0???0?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@_KAEBU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@AEBU?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@3@AEBV?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@3@@Z@4HA+24
	DD	imagerel $unwind$?dtor$0@?0???0?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@_KAEBU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@AEBU?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@3@AEBV?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@3@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@XZ DD imagerel $LN230
	DD	imagerel $LN230+25
	DD	imagerel $unwind$??1?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?allocate@?$allocator@U_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@QEAAPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@_K@Z DD imagerel $LN13
	DD	imagerel $LN13+160
	DD	imagerel $unwind$?allocate@?$allocator@U_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@QEAAPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAA@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@std@@@Z DD imagerel $LN29
	DD	imagerel $LN29+119
	DD	imagerel $unwind$??0?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAA@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@std@@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAA@XZ DD imagerel $LN251
	DD	imagerel $LN251+65
	DD	imagerel $unwind$??1?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?clear@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAAXXZ DD imagerel $LN124
	DD	imagerel $LN124+122
	DD	imagerel $unwind$?clear@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Erase@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAAXPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@23@@Z DD imagerel $LN124
	DD	imagerel $LN124+186
	DD	imagerel $unwind$?_Erase@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAAXPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@23@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAA@XZ DD imagerel $LN225
	DD	imagerel $LN225+354
	DD	imagerel $unwind$??1?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?clear@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAXXZ DD imagerel $LN209
	DD	imagerel $LN209+315
	DD	imagerel $unwind$?clear@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?rehash@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAX_K@Z DD imagerel $LN15
	DD	imagerel $LN15+217
	DD	imagerel $unwind$?rehash@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAX_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Init@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAXXZ DD imagerel $LN115
	DD	imagerel $LN115+84
	DD	imagerel $unwind$?_Init@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Set_bucket@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAX_KV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@Z DD imagerel $LN91
	DD	imagerel $LN91+479
	DD	imagerel $unwind$?_Set_bucket@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAX_KV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?allocate@?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@QEAAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@2@_K@Z DD imagerel $LN13
	DD	imagerel $LN13+163
	DD	imagerel $unwind$?allocate@?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@QEAAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@2@_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?UnInit@theManagerCounter@mu2@@UEAA_NXZ DD imagerel $LN393
	DD	imagerel $LN393+52
	DD	imagerel $unwind$?UnInit@theManagerCounter@mu2@@UEAA_NXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0theManagerCounter@mu2@@QEAA@XZ DD imagerel $LN213
	DD	imagerel $LN213+255
	DD	imagerel $unwind$??0theManagerCounter@mu2@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???0theManagerCounter@mu2@@QEAA@XZ@4HA DD imagerel ?dtor$0@?0???0theManagerCounter@mu2@@QEAA@XZ@4HA
	DD	imagerel ?dtor$0@?0???0theManagerCounter@mu2@@QEAA@XZ@4HA+24
	DD	imagerel $unwind$?dtor$0@?0???0theManagerCounter@mu2@@QEAA@XZ@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$1@?0???0theManagerCounter@mu2@@QEAA@XZ@4HA DD imagerel ?dtor$1@?0???0theManagerCounter@mu2@@QEAA@XZ@4HA
	DD	imagerel ?dtor$1@?0???0theManagerCounter@mu2@@QEAA@XZ@4HA+28
	DD	imagerel $unwind$?dtor$1@?0???0theManagerCounter@mu2@@QEAA@XZ@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$12@?0???0theManagerCounter@mu2@@QEAA@XZ@4HA DD imagerel ?dtor$12@?0???0theManagerCounter@mu2@@QEAA@XZ@4HA
	DD	imagerel ?dtor$12@?0???0theManagerCounter@mu2@@QEAA@XZ@4HA+31
	DD	imagerel $unwind$?dtor$12@?0???0theManagerCounter@mu2@@QEAA@XZ@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$11@?0???0theManagerCounter@mu2@@QEAA@XZ@4HA DD imagerel ?dtor$11@?0???0theManagerCounter@mu2@@QEAA@XZ@4HA
	DD	imagerel ?dtor$11@?0???0theManagerCounter@mu2@@QEAA@XZ@4HA+24
	DD	imagerel $unwind$?dtor$11@?0???0theManagerCounter@mu2@@QEAA@XZ@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GtheManagerCounter@mu2@@UEAAPEAXI@Z DD imagerel $LN175
	DD	imagerel $LN175+103
	DD	imagerel $unwind$??_GtheManagerCounter@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__E?inst@?$ISingleton@VtheManagerCounter@mu2@@@mu2@@1VtheManagerCounter@2@A@@YAXXZ DD imagerel ??__E?inst@?$ISingleton@VtheManagerCounter@mu2@@@mu2@@1VtheManagerCounter@2@A@@YAXXZ
	DD	imagerel ??__E?inst@?$ISingleton@VtheManagerCounter@mu2@@@mu2@@1VtheManagerCounter@2@A@@YAXXZ+34
	DD	imagerel $unwind$??__E?inst@?$ISingleton@VtheManagerCounter@mu2@@@mu2@@1VtheManagerCounter@2@A@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__F?inst@?$ISingleton@VtheManagerCounter@mu2@@@mu2@@1VtheManagerCounter@2@A@@YAXXZ DD imagerel ??__F?inst@?$ISingleton@VtheManagerCounter@mu2@@@mu2@@1VtheManagerCounter@2@A@@YAXXZ
	DD	imagerel ??__F?inst@?$ISingleton@VtheManagerCounter@mu2@@@mu2@@1VtheManagerCounter@2@A@@YAXXZ+68
	DD	imagerel $unwind$??__F?inst@?$ISingleton@VtheManagerCounter@mu2@@@mu2@@1VtheManagerCounter@2@A@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z DD imagerel $LN7
	DD	imagerel $LN7+150
	DD	imagerel $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@0@@Z DD imagerel $LN50
	DD	imagerel $LN50+158
	DD	imagerel $unwind$??$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@0@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@0@@Z@4HA DD imagerel ?dtor$0@?0???$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@0@@Z@4HA
	DD	imagerel ?dtor$0@?0???$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@0@@Z@4HA+24
	DD	imagerel $unwind$?dtor$0@?0???$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@0@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@0@@Z DD imagerel $LN50
	DD	imagerel $LN50+158
	DD	imagerel $unwind$??$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@0@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@0@@Z@4HA DD imagerel ?dtor$0@?0???$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@0@@Z@4HA
	DD	imagerel ?dtor$0@?0???$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@0@@Z@4HA+24
	DD	imagerel $unwind$?dtor$0@?0???$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@0@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$_Uninitialized_backout_al@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@QEAA@XZ DD imagerel $LN9
	DD	imagerel $LN9+54
	DD	imagerel $unwind$??1?$_Uninitialized_backout_al@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Emplace_back@$$V@?$_Uninitialized_backout_al@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@QEAAXXZ DD imagerel $LN30
	DD	imagerel $LN30+119
	DD	imagerel $unwind$??$_Emplace_back@$$V@?$_Uninitialized_backout_al@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@QEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$_Uninitialized_backout_al@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@QEAA@XZ DD imagerel $LN9
	DD	imagerel $LN9+54
	DD	imagerel $unwind$??1?$_Uninitialized_backout_al@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Emplace_back@$$V@?$_Uninitialized_backout_al@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@QEAAXXZ DD imagerel $LN30
	DD	imagerel $LN30+119
	DD	imagerel $unwind$??$_Emplace_back@$$V@?$_Uninitialized_backout_al@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@QEAAXXZ
pdata	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
??inst$initializer$@?$ISingleton@VtheManagerCounter@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA DQ FLAT:??__E?inst@?$ISingleton@VtheManagerCounter@mu2@@@mu2@@1VtheManagerCounter@2@A@@YAXXZ ; ??inst$initializer$@?$ISingleton@VtheManagerCounter@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA
CRT$XCU	ENDS
;	COMDAT __real@40800000
CONST	SEGMENT
__real@40800000 DD 040800000r			; 4
CONST	ENDS
;	COMDAT ??_R4?$ISingleton@VtheManagerCounter@mu2@@@mu2@@6B@
rdata$r	SEGMENT
??_R4?$ISingleton@VtheManagerCounter@mu2@@@mu2@@6B@ DD 01H ; mu2::ISingleton<mu2::theManagerCounter>::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AV?$ISingleton@VtheManagerCounter@mu2@@@mu2@@@8
	DD	imagerel ??_R3?$ISingleton@VtheManagerCounter@mu2@@@mu2@@8
	DD	imagerel ??_R4?$ISingleton@VtheManagerCounter@mu2@@@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R2?$ISingleton@VtheManagerCounter@mu2@@@mu2@@8
rdata$r	SEGMENT
??_R2?$ISingleton@VtheManagerCounter@mu2@@@mu2@@8 DD imagerel ??_R1A@?0A@EA@?$ISingleton@VtheManagerCounter@mu2@@@mu2@@8 ; mu2::ISingleton<mu2::theManagerCounter>::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3?$ISingleton@VtheManagerCounter@mu2@@@mu2@@8
rdata$r	SEGMENT
??_R3?$ISingleton@VtheManagerCounter@mu2@@@mu2@@8 DD 00H ; mu2::ISingleton<mu2::theManagerCounter>::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2?$ISingleton@VtheManagerCounter@mu2@@@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AV?$ISingleton@VtheManagerCounter@mu2@@@mu2@@@8
data$rs	SEGMENT
??_R0?AV?$ISingleton@VtheManagerCounter@mu2@@@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::ISingleton<mu2::theManagerCounter> `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AV?$ISingleton@VtheManagerCounter@mu2@@@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R1A@?0A@EA@?$ISingleton@VtheManagerCounter@mu2@@@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@?$ISingleton@VtheManagerCounter@mu2@@@mu2@@8 DD imagerel ??_R0?AV?$ISingleton@VtheManagerCounter@mu2@@@mu2@@@8 ; mu2::ISingleton<mu2::theManagerCounter>::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3?$ISingleton@VtheManagerCounter@mu2@@@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@theManagerCounter@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@theManagerCounter@mu2@@8 DD imagerel ??_R0?AVtheManagerCounter@mu2@@@8 ; mu2::theManagerCounter::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3theManagerCounter@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2theManagerCounter@mu2@@8
rdata$r	SEGMENT
??_R2theManagerCounter@mu2@@8 DD imagerel ??_R1A@?0A@EA@theManagerCounter@mu2@@8 ; mu2::theManagerCounter::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@?$ISingleton@VtheManagerCounter@mu2@@@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3theManagerCounter@mu2@@8
rdata$r	SEGMENT
??_R3theManagerCounter@mu2@@8 DD 00H			; mu2::theManagerCounter::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2theManagerCounter@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVtheManagerCounter@mu2@@@8
data$rs	SEGMENT
??_R0?AVtheManagerCounter@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::theManagerCounter `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVtheManagerCounter@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4theManagerCounter@mu2@@6B@
rdata$r	SEGMENT
??_R4theManagerCounter@mu2@@6B@ DD 01H			; mu2::theManagerCounter::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVtheManagerCounter@mu2@@@8
	DD	imagerel ??_R3theManagerCounter@mu2@@8
	DD	imagerel ??_R4theManagerCounter@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R4bad_alloc@std@@6B@
rdata$r	SEGMENT
??_R4bad_alloc@std@@6B@ DD 01H				; std::bad_alloc::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVbad_alloc@std@@@8
	DD	imagerel ??_R3bad_alloc@std@@8
	DD	imagerel ??_R4bad_alloc@std@@6B@
rdata$r	ENDS
;	COMDAT ??_R2bad_alloc@std@@8
rdata$r	SEGMENT
??_R2bad_alloc@std@@8 DD imagerel ??_R1A@?0A@EA@bad_alloc@std@@8 ; std::bad_alloc::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@exception@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3bad_alloc@std@@8
rdata$r	SEGMENT
??_R3bad_alloc@std@@8 DD 00H				; std::bad_alloc::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2bad_alloc@std@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@bad_alloc@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@bad_alloc@std@@8 DD imagerel ??_R0?AVbad_alloc@std@@@8 ; std::bad_alloc::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3bad_alloc@std@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@bad_array_new_length@std@@8 DD imagerel ??_R0?AVbad_array_new_length@std@@@8 ; std::bad_array_new_length::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	02H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3bad_array_new_length@std@@8
rdata$r	ENDS
;	COMDAT ??_R2bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R2bad_array_new_length@std@@8 DD imagerel ??_R1A@?0A@EA@bad_array_new_length@std@@8 ; std::bad_array_new_length::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@bad_alloc@std@@8
	DD	imagerel ??_R1A@?0A@EA@exception@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R3bad_array_new_length@std@@8 DD 00H			; std::bad_array_new_length::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	03H
	DD	imagerel ??_R2bad_array_new_length@std@@8
rdata$r	ENDS
;	COMDAT ??_R4bad_array_new_length@std@@6B@
rdata$r	SEGMENT
??_R4bad_array_new_length@std@@6B@ DD 01H		; std::bad_array_new_length::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVbad_array_new_length@std@@@8
	DD	imagerel ??_R3bad_array_new_length@std@@8
	DD	imagerel ??_R4bad_array_new_length@std@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@exception@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@exception@std@@8 DD imagerel ??_R0?AVexception@std@@@8 ; std::exception::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3exception@std@@8
rdata$r	ENDS
;	COMDAT ??_R2exception@std@@8
rdata$r	SEGMENT
??_R2exception@std@@8 DD imagerel ??_R1A@?0A@EA@exception@std@@8 ; std::exception::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3exception@std@@8
rdata$r	SEGMENT
??_R3exception@std@@8 DD 00H				; std::exception::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2exception@std@@8
rdata$r	ENDS
;	COMDAT ??_R4exception@std@@6B@
rdata$r	SEGMENT
??_R4exception@std@@6B@ DD 01H				; std::exception::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVexception@std@@@8
	DD	imagerel ??_R3exception@std@@8
	DD	imagerel ??_R4exception@std@@6B@
rdata$r	ENDS
;	COMDAT ??_C@_0BK@KDCGEAPC@invalid?5number?5of?5buckets@
CONST	SEGMENT
??_C@_0BK@KDCGEAPC@invalid?5number?5of?5buckets@ DB 'invalid number of bu'
	DB	'ckets', 00H					; `string'
CONST	ENDS
;	COMDAT ??_7theManagerCounter@mu2@@6B@
CONST	SEGMENT
??_7theManagerCounter@mu2@@6B@ DQ FLAT:??_R4theManagerCounter@mu2@@6B@ ; mu2::theManagerCounter::`vftable'
	DQ	FLAT:?Init@theManagerCounter@mu2@@UEAA_NPEAX@Z
	DQ	FLAT:?UnInit@theManagerCounter@mu2@@UEAA_NXZ
	DQ	FLAT:??_EtheManagerCounter@mu2@@UEAAPEAXI@Z
CONST	ENDS
;	COMDAT ??_7?$ISingleton@VtheManagerCounter@mu2@@@mu2@@6B@
CONST	SEGMENT
??_7?$ISingleton@VtheManagerCounter@mu2@@@mu2@@6B@ DQ FLAT:??_R4?$ISingleton@VtheManagerCounter@mu2@@@mu2@@6B@ ; mu2::ISingleton<mu2::theManagerCounter>::`vftable'
	DQ	FLAT:_purecall
	DQ	FLAT:_purecall
	DQ	FLAT:??_E?$ISingleton@VtheManagerCounter@mu2@@@mu2@@UEAAPEAXI@Z
CONST	ENDS
;	COMDAT _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 DD 010H
	DD	imagerel ??_R0?AVbad_alloc@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0bad_alloc@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVbad_alloc@std@@@8
data$r	SEGMENT
??_R0?AVbad_alloc@std@@@8 DQ FLAT:??_7type_info@@6B@	; std::bad_alloc `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVbad_alloc@std@@', 00H
data$r	ENDS
;	COMDAT _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 DD 00H
	DD	imagerel ??_R0?AVbad_array_new_length@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0bad_array_new_length@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVbad_array_new_length@std@@@8
data$r	SEGMENT
??_R0?AVbad_array_new_length@std@@@8 DQ FLAT:??_7type_info@@6B@ ; std::bad_array_new_length `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVbad_array_new_length@std@@', 00H
data$r	ENDS
;	COMDAT _CTA3?AVbad_array_new_length@std@@
xdata$x	SEGMENT
_CTA3?AVbad_array_new_length@std@@ DD 03H
	DD	imagerel _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
	DD	imagerel _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
	DD	imagerel _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
xdata$x	ENDS
;	COMDAT _TI3?AVbad_array_new_length@std@@
xdata$x	SEGMENT
_TI3?AVbad_array_new_length@std@@ DD 00H
	DD	imagerel ??1bad_array_new_length@std@@UEAA@XZ
	DD	00H
	DD	imagerel _CTA3?AVbad_array_new_length@std@@
xdata$x	ENDS
;	COMDAT _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 DD 00H
	DD	imagerel ??_R0?AVexception@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0exception@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVexception@std@@@8
data$r	SEGMENT
??_R0?AVexception@std@@@8 DQ FLAT:??_7type_info@@6B@	; std::exception `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVexception@std@@', 00H
data$r	ENDS
;	COMDAT ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@
CONST	SEGMENT
??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ DB 'bad array new length', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7bad_array_new_length@std@@6B@
CONST	SEGMENT
??_7bad_array_new_length@std@@6B@ DQ FLAT:??_R4bad_array_new_length@std@@6B@ ; std::bad_array_new_length::`vftable'
	DQ	FLAT:??_Ebad_array_new_length@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT ??_7bad_alloc@std@@6B@
CONST	SEGMENT
??_7bad_alloc@std@@6B@ DQ FLAT:??_R4bad_alloc@std@@6B@	; std::bad_alloc::`vftable'
	DQ	FLAT:??_Ebad_alloc@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT ??_C@_0BC@EOODALEL@Unknown?5exception@
CONST	SEGMENT
??_C@_0BC@EOODALEL@Unknown?5exception@ DB 'Unknown exception', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7exception@std@@6B@
CONST	SEGMENT
??_7exception@std@@6B@ DQ FLAT:??_R4exception@std@@6B@	; std::exception::`vftable'
	DQ	FLAT:??_Eexception@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Emplace_back@$$V@?$_Uninitialized_backout_al@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@QEAAXXZ DD 020a01H
	DD	07006520aH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$_Uninitialized_backout_al@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@QEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Emplace_back@$$V@?$_Uninitialized_backout_al@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@QEAAXXZ DD 020a01H
	DD	07006520aH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$_Uninitialized_backout_al@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@QEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@0@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@0@@Z DB 06H
	DB	00H
	DB	00H
	DB	'h'
	DB	02H
	DB	084H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@0@@Z DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@0@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@0@@Z DB 028H
	DD	imagerel $stateUnwindMap$??$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@0@@Z
	DD	imagerel $ip2state$??$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@0@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@0@@Z DD 011311H
	DD	0c213H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@0@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@0@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@0@@Z DB 06H
	DB	00H
	DB	00H
	DB	'h'
	DB	02H
	DB	084H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@0@@Z DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@0@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@0@@Z DB 028H
	DD	imagerel $stateUnwindMap$??$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@0@@Z
	DD	imagerel $ip2state$??$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@0@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@0@@Z DD 011311H
	DD	0c213H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@0@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z DD 010901H
	DD	0a209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__F?inst@?$ISingleton@VtheManagerCounter@mu2@@@mu2@@1VtheManagerCounter@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__E?inst@?$ISingleton@VtheManagerCounter@mu2@@@mu2@@1VtheManagerCounter@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GtheManagerCounter@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$11@?0???0theManagerCounter@mu2@@QEAA@XZ@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$12@?0???0theManagerCounter@mu2@@QEAA@XZ@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$1@?0???0theManagerCounter@mu2@@QEAA@XZ@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???0theManagerCounter@mu2@@QEAA@XZ@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??0theManagerCounter@mu2@@QEAA@XZ DB 0eH
	DB	00H
	DB	00H
	DB	'0'
	DB	02H
	DB	082H
	DB	04H
	DB	0a0H
	DB	06H
	DB	'`'
	DB	08H
	DB	' '
	DB	04H
	DB	018H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??0theManagerCounter@mu2@@QEAA@XZ DB 08H
	DB	0eH
	DD	imagerel ?dtor$0@?0???0theManagerCounter@mu2@@QEAA@XZ@4HA
	DB	02eH
	DD	imagerel ?dtor$1@?0???0theManagerCounter@mu2@@QEAA@XZ@4HA
	DB	02eH
	DD	imagerel ?dtor$12@?0???0theManagerCounter@mu2@@QEAA@XZ@4HA
	DB	056H
	DD	imagerel ?dtor$11@?0???0theManagerCounter@mu2@@QEAA@XZ@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??0theManagerCounter@mu2@@QEAA@XZ DB 028H
	DD	imagerel $stateUnwindMap$??0theManagerCounter@mu2@@QEAA@XZ
	DD	imagerel $ip2state$??0theManagerCounter@mu2@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0theManagerCounter@mu2@@QEAA@XZ DD 010911H
	DD	0c209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??0theManagerCounter@mu2@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?UnInit@theManagerCounter@mu2@@UEAA_NXZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?allocate@?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@QEAAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@2@_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DW	0164H
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?_Set_bucket@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAX_KV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@Z DB 06H
	DB	00H
	DB	00H
	DB	019H, 06H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?_Set_bucket@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAX_KV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@Z DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?_Set_bucket@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAX_KV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@Z DB 028H
	DD	imagerel $stateUnwindMap$?_Set_bucket@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAX_KV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@Z
	DD	imagerel $ip2state$?_Set_bucket@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAX_KV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Set_bucket@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAX_KV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@Z DD 021611H
	DD	0130116H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?_Set_bucket@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAX_KV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Init@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAXXZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?rehash@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAX_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?clear@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAXXZ DB 06H
	DB	00H
	DB	00H
	DB	0e1H, 03H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?clear@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAXXZ DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?clear@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAXXZ DB 028H
	DD	imagerel $stateUnwindMap$?clear@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAXXZ
	DD	imagerel $ip2state$?clear@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?clear@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAXXZ DD 010911H
	DD	0e209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?clear@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??1?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAA@XZ DB 0aH
	DB	00H
	DB	00H
	DB	091H, 03H
	DB	02H
	DB	'V'
	DB	00H
	DB	'0'
	DB	02H
	DB	'f'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??1?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAA@XZ DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??1?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAA@XZ DB 068H
	DD	imagerel $stateUnwindMap$??1?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAA@XZ
	DD	imagerel $ip2state$??1?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAA@XZ DD 020c19H
	DD	011010cH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??1?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?_Erase@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAAXPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@23@@Z DB 06H
	DB	00H
	DB	00H
	DB	')', 02H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?_Erase@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAAXPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@23@@Z DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?_Erase@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAAXPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@23@@Z DB 028H
	DD	imagerel $stateUnwindMap$?_Erase@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAAXPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@23@@Z
	DD	imagerel $ip2state$?_Erase@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAAXPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@23@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Erase@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAAXPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@23@@Z DD 010e11H
	DD	0820eH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?_Erase@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAAXPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@23@@Z
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DB	06eH
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?clear@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAAXXZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??1?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAA@XZ DB 02H
	DB	00H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??1?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAA@XZ DB 060H
	DD	imagerel $ip2state$??1?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAA@XZ DD 010919H
	DD	06209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??1?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAA@XZ
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DB	066H
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAA@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@std@@@Z DD 010d01H
	DD	0820dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?allocate@?$allocator@U_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@QEAAPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???0?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@_KAEBU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@AEBU?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@3@AEBV?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@3@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$1@?0???0?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@_KAEBU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@AEBU?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@3@AEBV?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@3@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??0?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@_KAEBU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@AEBU?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@3@AEBV?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@3@@Z DB 08H
	DB	00H
	DB	00H
	DB	09cH
	DB	02H
	DB	'b'
	DB	04H
	DB	' '
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??0?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@_KAEBU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@AEBU?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@3@AEBV?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@3@@Z DB 04H
	DB	0eH
	DD	imagerel ?dtor$1@?0???0?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@_KAEBU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@AEBU?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@3@AEBV?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@3@@Z@4HA
	DB	036H
	DD	imagerel ?dtor$0@?0???0?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@_KAEBU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@AEBU?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@3@AEBV?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@3@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??0?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@_KAEBU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@AEBU?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@3@AEBV?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@3@@Z DB 028H
	DD	imagerel $stateUnwindMap$??0?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@_KAEBU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@AEBU?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@3@AEBV?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@3@@Z
	DD	imagerel $ip2state$??0?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@_KAEBU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@AEBU?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@3@AEBV?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@3@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@_KAEBU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@AEBU?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@3@AEBV?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@3@@Z DD 011811H
	DD	06218H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??0?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@_KAEBU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@AEBU?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@3@AEBV?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@3@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?allocate@?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@QEAAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@2@_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DW	0164H
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?_Set_bucket@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAX_KV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@Z DB 06H
	DB	00H
	DB	00H
	DB	019H, 06H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?_Set_bucket@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAX_KV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@Z DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?_Set_bucket@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAX_KV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@Z DB 028H
	DD	imagerel $stateUnwindMap$?_Set_bucket@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAX_KV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@Z
	DD	imagerel $ip2state$?_Set_bucket@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAX_KV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Set_bucket@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAX_KV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@Z DD 021611H
	DD	0130116H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?_Set_bucket@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAX_KV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Init@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAXXZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?rehash@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAX_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?clear@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAXXZ DB 06H
	DB	00H
	DB	00H
	DB	0e1H, 03H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?clear@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAXXZ DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?clear@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAXXZ DB 028H
	DD	imagerel $stateUnwindMap$?clear@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAXXZ
	DD	imagerel $ip2state$?clear@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?clear@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAXXZ DD 010911H
	DD	0e209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?clear@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??1?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAA@XZ DB 0aH
	DB	00H
	DB	00H
	DB	091H, 03H
	DB	02H
	DB	'V'
	DB	00H
	DB	'0'
	DB	02H
	DB	'f'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??1?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAA@XZ DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??1?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAA@XZ DB 068H
	DD	imagerel $stateUnwindMap$??1?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAA@XZ
	DD	imagerel $ip2state$??1?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAA@XZ DD 020c19H
	DD	011010cH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??1?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?_Erase@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAAXPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@23@@Z DB 06H
	DB	00H
	DB	00H
	DB	')', 02H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?_Erase@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAAXPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@23@@Z DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?_Erase@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAAXPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@23@@Z DB 028H
	DD	imagerel $stateUnwindMap$?_Erase@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAAXPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@23@@Z
	DD	imagerel $ip2state$?_Erase@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAAXPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@23@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Erase@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAAXPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@23@@Z DD 010e11H
	DD	0820eH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?_Erase@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAAXPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@23@@Z
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DB	06eH
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?clear@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAAXXZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??1?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAA@XZ DB 02H
	DB	00H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??1?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAA@XZ DB 060H
	DD	imagerel $ip2state$??1?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAA@XZ DD 010919H
	DD	06209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??1?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAA@XZ
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DB	066H
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAA@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@std@@@Z DD 010d01H
	DD	0820dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?allocate@?$allocator@U_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@QEAAPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_G?$ISingleton@VtheManagerCounter@mu2@@@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ DB 06H
	DB	00H
	DB	00H
	DB	'q', 02H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ DB 068H
	DD	imagerel $stateUnwindMap$?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ
	DD	imagerel $ip2state$?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ DD 010919H
	DD	0e209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Throw_bad_array_new_length@std@@YAXXZ DD 010401H
	DD	08204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1bad_array_new_length@std@@UEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_array_new_length@std@@QEAA@XZ DD 010601H
	DD	07006H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z DD 020f01H
	DD	0700b320fH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gexception@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?what@exception@std@@UEBAPEBDXZ DD 010901H
	DD	02209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0exception@std@@QEAA@AEBV01@@Z DD 020f01H
	DD	0700b320fH
xdata	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\forward_list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??$_Emplace_back@$$V@?$_Uninitialized_backout_al@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@QEAAXXZ
_TEXT	SEGMENT
$T1 = 0
_Ptr$ = 8
$T2 = 16
$T3 = 24
__formal$ = 32
this$ = 64
??$_Emplace_back@$$V@?$_Uninitialized_backout_al@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@QEAAXXZ PROC ; std::_Uninitialized_backout_al<std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > > > > >::_Emplace_back<>, COMDAT

; 1843 :     _CONSTEXPR20 void _Emplace_back(_Types&&... _Vals) { // construct a new element at *_Last and increment

$LN30:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi
  00006	48 83 ec 30	 sub	 rsp, 48			; 00000030H

; 1844 :         allocator_traits<_Alloc>::construct(_Al, _STD _Unfancy(_Last), _STD forward<_Types>(_Vals)...);

  0000a	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000f	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00013	48 89 44 24 08	 mov	 QWORD PTR _Ptr$[rsp], rax

; 69   :     return _Ptr;

  00018	48 8b 44 24 08	 mov	 rax, QWORD PTR _Ptr$[rsp]
  0001d	48 89 44 24 10	 mov	 QWORD PTR $T2[rsp], rax

; 1844 :         allocator_traits<_Alloc>::construct(_Al, _STD _Unfancy(_Last), _STD forward<_Types>(_Vals)...);

  00022	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00027	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0002b	48 89 44 24 20	 mov	 QWORD PTR __formal$[rsp], rax
  00030	48 8b 44 24 10	 mov	 rax, QWORD PTR $T2[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  00035	48 89 44 24 18	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 732  :         ::new (const_cast<void*>(static_cast<const volatile void*>(_Ptr))) _Objty(_STD forward<_Types>(_Args)...);

  0003a	48 8b 44 24 18	 mov	 rax, QWORD PTR $T3[rsp]
  0003f	48 89 04 24	 mov	 QWORD PTR $T1[rsp], rax
  00043	48 8b 3c 24	 mov	 rdi, QWORD PTR $T1[rsp]
  00047	33 c0		 xor	 eax, eax
  00049	b9 08 00 00 00	 mov	 ecx, 8
  0004e	f3 aa		 rep stosb
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\forward_list

; 35   :     _Flist_unchecked_const_iterator() noexcept : _Ptr() {}

  00050	48 8b 04 24	 mov	 rax, QWORD PTR $T1[rsp]
  00054	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1845 :         ++_Last;

  0005b	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00060	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00064	48 83 c0 08	 add	 rax, 8
  00068	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0006d	48 89 41 08	 mov	 QWORD PTR [rcx+8], rax

; 1846 :     }

  00071	48 83 c4 30	 add	 rsp, 48			; 00000030H
  00075	5f		 pop	 rdi
  00076	c3		 ret	 0
??$_Emplace_back@$$V@?$_Uninitialized_backout_al@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@QEAAXXZ ENDP ; std::_Uninitialized_backout_al<std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > > > > >::_Emplace_back<>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??1?$_Uninitialized_backout_al@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@QEAA@XZ
_TEXT	SEGMENT
_Al$ = 0
_Last$ = 8
_First$ = 16
this$ = 48
??1?$_Uninitialized_backout_al@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@QEAA@XZ PROC ; std::_Uninitialized_backout_al<std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > > > > >::~_Uninitialized_backout_al<std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > > > > >, COMDAT

; 1838 :     _CONSTEXPR20 ~_Uninitialized_backout_al() {

$LN9:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 1839 :         _STD _Destroy_range(_First, _Last, _Al);

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00012	48 89 04 24	 mov	 QWORD PTR _Al$[rsp], rax
  00016	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001b	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0001f	48 89 44 24 08	 mov	 QWORD PTR _Last$[rsp], rax
  00024	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00029	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0002c	48 89 44 24 10	 mov	 QWORD PTR _First$[rsp], rax

; 1840 :     }

  00031	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00035	c3		 ret	 0
??1?$_Uninitialized_backout_al@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@QEAA@XZ ENDP ; std::_Uninitialized_backout_al<std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > > > > >::~_Uninitialized_backout_al<std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > > > > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\forward_list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??$_Emplace_back@$$V@?$_Uninitialized_backout_al@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@QEAAXXZ
_TEXT	SEGMENT
$T1 = 0
_Ptr$ = 8
$T2 = 16
$T3 = 24
__formal$ = 32
this$ = 64
??$_Emplace_back@$$V@?$_Uninitialized_backout_al@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@QEAAXXZ PROC ; std::_Uninitialized_backout_al<std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > > > > >::_Emplace_back<>, COMDAT

; 1843 :     _CONSTEXPR20 void _Emplace_back(_Types&&... _Vals) { // construct a new element at *_Last and increment

$LN30:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi
  00006	48 83 ec 30	 sub	 rsp, 48			; 00000030H

; 1844 :         allocator_traits<_Alloc>::construct(_Al, _STD _Unfancy(_Last), _STD forward<_Types>(_Vals)...);

  0000a	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000f	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00013	48 89 44 24 08	 mov	 QWORD PTR _Ptr$[rsp], rax

; 69   :     return _Ptr;

  00018	48 8b 44 24 08	 mov	 rax, QWORD PTR _Ptr$[rsp]
  0001d	48 89 44 24 10	 mov	 QWORD PTR $T2[rsp], rax

; 1844 :         allocator_traits<_Alloc>::construct(_Al, _STD _Unfancy(_Last), _STD forward<_Types>(_Vals)...);

  00022	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00027	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0002b	48 89 44 24 20	 mov	 QWORD PTR __formal$[rsp], rax
  00030	48 8b 44 24 10	 mov	 rax, QWORD PTR $T2[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  00035	48 89 44 24 18	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 732  :         ::new (const_cast<void*>(static_cast<const volatile void*>(_Ptr))) _Objty(_STD forward<_Types>(_Args)...);

  0003a	48 8b 44 24 18	 mov	 rax, QWORD PTR $T3[rsp]
  0003f	48 89 04 24	 mov	 QWORD PTR $T1[rsp], rax
  00043	48 8b 3c 24	 mov	 rdi, QWORD PTR $T1[rsp]
  00047	33 c0		 xor	 eax, eax
  00049	b9 08 00 00 00	 mov	 ecx, 8
  0004e	f3 aa		 rep stosb
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\forward_list

; 35   :     _Flist_unchecked_const_iterator() noexcept : _Ptr() {}

  00050	48 8b 04 24	 mov	 rax, QWORD PTR $T1[rsp]
  00054	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1845 :         ++_Last;

  0005b	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00060	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00064	48 83 c0 08	 add	 rax, 8
  00068	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0006d	48 89 41 08	 mov	 QWORD PTR [rcx+8], rax

; 1846 :     }

  00071	48 83 c4 30	 add	 rsp, 48			; 00000030H
  00075	5f		 pop	 rdi
  00076	c3		 ret	 0
??$_Emplace_back@$$V@?$_Uninitialized_backout_al@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@QEAAXXZ ENDP ; std::_Uninitialized_backout_al<std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > > > > >::_Emplace_back<>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??1?$_Uninitialized_backout_al@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@QEAA@XZ
_TEXT	SEGMENT
_Al$ = 0
_Last$ = 8
_First$ = 16
this$ = 48
??1?$_Uninitialized_backout_al@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@QEAA@XZ PROC ; std::_Uninitialized_backout_al<std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > > > > >::~_Uninitialized_backout_al<std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > > > > >, COMDAT

; 1838 :     _CONSTEXPR20 ~_Uninitialized_backout_al() {

$LN9:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 1839 :         _STD _Destroy_range(_First, _Last, _Al);

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00012	48 89 04 24	 mov	 QWORD PTR _Al$[rsp], rax
  00016	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001b	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0001f	48 89 44 24 08	 mov	 QWORD PTR _Last$[rsp], rax
  00024	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00029	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0002c	48 89 44 24 10	 mov	 QWORD PTR _First$[rsp], rax

; 1840 :     }

  00031	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00035	c3		 ret	 0
??1?$_Uninitialized_backout_al@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@QEAA@XZ ENDP ; std::_Uninitialized_backout_al<std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > > > > >::~_Uninitialized_backout_al<std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > > > > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@0@@Z
_TEXT	SEGMENT
$T1 = 32
$T2 = 40
_Al$ = 48
_Last$ = 56
_First$ = 64
_Backout$ = 72
_First$ = 112
_Count$ = 120
_Al$ = 128
??$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@0@@Z PROC ; std::_Uninitialized_value_construct_n<std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > > > > >, COMDAT

; 2078 :     _Alloc_ptr_t<_Alloc> _First, _Alloc_size_t<_Alloc> _Count, _Alloc& _Al) {

$LN50:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 68	 sub	 rsp, 104		; 00000068H

; 1833 :     _CONSTEXPR20 _Uninitialized_backout_al(pointer _Dest, _Alloc& _Al_) : _First(_Dest), _Last(_Dest), _Al(_Al_) {}

  00013	48 8b 44 24 70	 mov	 rax, QWORD PTR _First$[rsp]
  00018	48 89 44 24 48	 mov	 QWORD PTR _Backout$[rsp], rax
  0001d	48 8b 44 24 70	 mov	 rax, QWORD PTR _First$[rsp]
  00022	48 89 44 24 50	 mov	 QWORD PTR _Backout$[rsp+8], rax
  00027	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _Al$[rsp]
  0002f	48 89 44 24 58	 mov	 QWORD PTR _Backout$[rsp+16], rax

; 2079 :     // value-initialize _Count objects to raw _First, using _Al
; 2080 :     using _Ptrty = typename _Alloc::value_type*;
; 2081 :     if constexpr (_Use_memset_value_construct_v<_Ptrty> && _Uses_default_construct<_Alloc, _Ptrty>::value) {
; 2082 : #if _HAS_CXX20
; 2083 :         if (!_STD is_constant_evaluated())
; 2084 : #endif // _HAS_CXX20
; 2085 :         {
; 2086 :             auto _PFirst = _Unfancy(_First);
; 2087 :             _Zero_range(_PFirst, _PFirst + _Count);
; 2088 :             return _First + _Count;
; 2089 :         }
; 2090 :     }
; 2091 : 
; 2092 :     _Uninitialized_backout_al<_Alloc> _Backout{_First, _Al};
; 2093 :     for (; 0 < _Count; --_Count) {

  00034	eb 0d		 jmp	 SHORT $LN4@Uninitiali
$LN2@Uninitiali:
  00036	48 8b 44 24 78	 mov	 rax, QWORD PTR _Count$[rsp]
  0003b	48 ff c8	 dec	 rax
  0003e	48 89 44 24 78	 mov	 QWORD PTR _Count$[rsp], rax
$LN4@Uninitiali:
  00043	48 83 7c 24 78
	00		 cmp	 QWORD PTR _Count$[rsp], 0
  00049	76 0d		 jbe	 SHORT $LN3@Uninitiali

; 2094 :         _Backout._Emplace_back();

  0004b	48 8d 4c 24 48	 lea	 rcx, QWORD PTR _Backout$[rsp]
  00050	e8 00 00 00 00	 call	 ??$_Emplace_back@$$V@?$_Uninitialized_backout_al@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@QEAAXXZ ; std::_Uninitialized_backout_al<std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > > > > >::_Emplace_back<>
  00055	90		 npad	 1

; 2095 :     }

  00056	eb de		 jmp	 SHORT $LN2@Uninitiali
$LN3@Uninitiali:

; 1849 :         _First = _Last;

  00058	48 8b 44 24 50	 mov	 rax, QWORD PTR _Backout$[rsp+8]
  0005d	48 89 44 24 48	 mov	 QWORD PTR _Backout$[rsp], rax

; 1850 :         return _Last;

  00062	48 8b 44 24 50	 mov	 rax, QWORD PTR _Backout$[rsp+8]
  00067	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax

; 2096 : 
; 2097 :     return _Backout._Release();

  0006c	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00071	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax

; 1839 :         _STD _Destroy_range(_First, _Last, _Al);

  00076	48 8b 44 24 58	 mov	 rax, QWORD PTR _Backout$[rsp+16]
  0007b	48 89 44 24 30	 mov	 QWORD PTR _Al$[rsp], rax
  00080	48 8b 44 24 50	 mov	 rax, QWORD PTR _Backout$[rsp+8]
  00085	48 89 44 24 38	 mov	 QWORD PTR _Last$[rsp], rax
  0008a	48 8b 44 24 48	 mov	 rax, QWORD PTR _Backout$[rsp]
  0008f	48 89 44 24 40	 mov	 QWORD PTR _First$[rsp], rax

; 2096 : 
; 2097 :     return _Backout._Release();

  00094	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]

; 2098 : }

  00099	48 83 c4 68	 add	 rsp, 104		; 00000068H
  0009d	c3		 ret	 0
??$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@0@@Z ENDP ; std::_Uninitialized_value_construct_n<std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > > > > >
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 40
_Al$ = 48
_Last$ = 56
_First$ = 64
_Backout$ = 72
_First$ = 112
_Count$ = 120
_Al$ = 128
?dtor$0@?0???$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@0@@Z@4HA PROC ; `std::_Uninitialized_value_construct_n<std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > > > > >'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8d 4d 48	 lea	 rcx, QWORD PTR _Backout$[rbp]
  0000d	e8 00 00 00 00	 call	 ??1?$_Uninitialized_backout_al@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@QEAA@XZ ; std::_Uninitialized_backout_al<std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > > > > >::~_Uninitialized_backout_al<std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > > > > >
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$0@?0???$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@0@@Z@4HA ENDP ; `std::_Uninitialized_value_construct_n<std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > > > > >'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@0@@Z
_TEXT	SEGMENT
$T1 = 32
$T2 = 40
_Al$ = 48
_Last$ = 56
_First$ = 64
_Backout$ = 72
_First$ = 112
_Count$ = 120
_Al$ = 128
??$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@0@@Z PROC ; std::_Uninitialized_value_construct_n<std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > > > > >, COMDAT

; 2078 :     _Alloc_ptr_t<_Alloc> _First, _Alloc_size_t<_Alloc> _Count, _Alloc& _Al) {

$LN50:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 68	 sub	 rsp, 104		; 00000068H

; 1833 :     _CONSTEXPR20 _Uninitialized_backout_al(pointer _Dest, _Alloc& _Al_) : _First(_Dest), _Last(_Dest), _Al(_Al_) {}

  00013	48 8b 44 24 70	 mov	 rax, QWORD PTR _First$[rsp]
  00018	48 89 44 24 48	 mov	 QWORD PTR _Backout$[rsp], rax
  0001d	48 8b 44 24 70	 mov	 rax, QWORD PTR _First$[rsp]
  00022	48 89 44 24 50	 mov	 QWORD PTR _Backout$[rsp+8], rax
  00027	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _Al$[rsp]
  0002f	48 89 44 24 58	 mov	 QWORD PTR _Backout$[rsp+16], rax

; 2079 :     // value-initialize _Count objects to raw _First, using _Al
; 2080 :     using _Ptrty = typename _Alloc::value_type*;
; 2081 :     if constexpr (_Use_memset_value_construct_v<_Ptrty> && _Uses_default_construct<_Alloc, _Ptrty>::value) {
; 2082 : #if _HAS_CXX20
; 2083 :         if (!_STD is_constant_evaluated())
; 2084 : #endif // _HAS_CXX20
; 2085 :         {
; 2086 :             auto _PFirst = _Unfancy(_First);
; 2087 :             _Zero_range(_PFirst, _PFirst + _Count);
; 2088 :             return _First + _Count;
; 2089 :         }
; 2090 :     }
; 2091 : 
; 2092 :     _Uninitialized_backout_al<_Alloc> _Backout{_First, _Al};
; 2093 :     for (; 0 < _Count; --_Count) {

  00034	eb 0d		 jmp	 SHORT $LN4@Uninitiali
$LN2@Uninitiali:
  00036	48 8b 44 24 78	 mov	 rax, QWORD PTR _Count$[rsp]
  0003b	48 ff c8	 dec	 rax
  0003e	48 89 44 24 78	 mov	 QWORD PTR _Count$[rsp], rax
$LN4@Uninitiali:
  00043	48 83 7c 24 78
	00		 cmp	 QWORD PTR _Count$[rsp], 0
  00049	76 0d		 jbe	 SHORT $LN3@Uninitiali

; 2094 :         _Backout._Emplace_back();

  0004b	48 8d 4c 24 48	 lea	 rcx, QWORD PTR _Backout$[rsp]
  00050	e8 00 00 00 00	 call	 ??$_Emplace_back@$$V@?$_Uninitialized_backout_al@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@QEAAXXZ ; std::_Uninitialized_backout_al<std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > > > > >::_Emplace_back<>
  00055	90		 npad	 1

; 2095 :     }

  00056	eb de		 jmp	 SHORT $LN2@Uninitiali
$LN3@Uninitiali:

; 1849 :         _First = _Last;

  00058	48 8b 44 24 50	 mov	 rax, QWORD PTR _Backout$[rsp+8]
  0005d	48 89 44 24 48	 mov	 QWORD PTR _Backout$[rsp], rax

; 1850 :         return _Last;

  00062	48 8b 44 24 50	 mov	 rax, QWORD PTR _Backout$[rsp+8]
  00067	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax

; 2096 : 
; 2097 :     return _Backout._Release();

  0006c	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00071	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax

; 1839 :         _STD _Destroy_range(_First, _Last, _Al);

  00076	48 8b 44 24 58	 mov	 rax, QWORD PTR _Backout$[rsp+16]
  0007b	48 89 44 24 30	 mov	 QWORD PTR _Al$[rsp], rax
  00080	48 8b 44 24 50	 mov	 rax, QWORD PTR _Backout$[rsp+8]
  00085	48 89 44 24 38	 mov	 QWORD PTR _Last$[rsp], rax
  0008a	48 8b 44 24 48	 mov	 rax, QWORD PTR _Backout$[rsp]
  0008f	48 89 44 24 40	 mov	 QWORD PTR _First$[rsp], rax

; 2096 : 
; 2097 :     return _Backout._Release();

  00094	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]

; 2098 : }

  00099	48 83 c4 68	 add	 rsp, 104		; 00000068H
  0009d	c3		 ret	 0
??$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@0@@Z ENDP ; std::_Uninitialized_value_construct_n<std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > > > > >
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 40
_Al$ = 48
_Last$ = 56
_First$ = 64
_Backout$ = 72
_First$ = 112
_Count$ = 120
_Al$ = 128
?dtor$0@?0???$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@0@@Z@4HA PROC ; `std::_Uninitialized_value_construct_n<std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > > > > >'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8d 4d 48	 lea	 rcx, QWORD PTR _Backout$[rbp]
  0000d	e8 00 00 00 00	 call	 ??1?$_Uninitialized_backout_al@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@QEAA@XZ ; std::_Uninitialized_backout_al<std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > > > > >::~_Uninitialized_backout_al<std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > > > > >
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$0@?0???$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@0@@Z@4HA ENDP ; `std::_Uninitialized_value_construct_n<std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > > > > >'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
_TEXT	SEGMENT
_Ptr_container$ = 48
_Block_size$ = 56
_Ptr$ = 64
$T1 = 72
_Bytes$ = 96
??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z PROC ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>, COMDAT

; 182  : __declspec(allocator) void* _Allocate_manually_vector_aligned(const size_t _Bytes) {

$LN7:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 183  :     // allocate _Bytes manually aligned to at least _Big_allocation_alignment
; 184  :     const size_t _Block_size = _Non_user_size + _Bytes;

  00009	48 8b 44 24 60	 mov	 rax, QWORD PTR _Bytes$[rsp]
  0000e	48 83 c0 27	 add	 rax, 39			; 00000027H
  00012	48 89 44 24 38	 mov	 QWORD PTR _Block_size$[rsp], rax

; 185  :     if (_Block_size <= _Bytes) {

  00017	48 8b 44 24 60	 mov	 rax, QWORD PTR _Bytes$[rsp]
  0001c	48 39 44 24 38	 cmp	 QWORD PTR _Block_size$[rsp], rax
  00021	77 06		 ja	 SHORT $LN2@Allocate_m

; 186  :         _Throw_bad_array_new_length(); // add overflow

  00023	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00028	90		 npad	 1
$LN2@Allocate_m:

; 136  :         return ::operator new(_Bytes);

  00029	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Block_size$[rsp]
  0002e	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00033	48 89 44 24 48	 mov	 QWORD PTR $T1[rsp], rax

; 187  :     }
; 188  : 
; 189  :     const uintptr_t _Ptr_container = reinterpret_cast<uintptr_t>(_Traits::_Allocate(_Block_size));

  00038	48 8b 44 24 48	 mov	 rax, QWORD PTR $T1[rsp]
  0003d	48 89 44 24 30	 mov	 QWORD PTR _Ptr_container$[rsp], rax

; 190  :     _STL_VERIFY(_Ptr_container != 0, "invalid argument"); // validate even in release since we're doing p[-1]

  00042	48 83 7c 24 30
	00		 cmp	 QWORD PTR _Ptr_container$[rsp], 0
  00048	75 19		 jne	 SHORT $LN3@Allocate_m
  0004a	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR [rsp+32], 0
  00053	45 33 c9	 xor	 r9d, r9d
  00056	45 33 c0	 xor	 r8d, r8d
  00059	33 d2		 xor	 edx, edx
  0005b	33 c9		 xor	 ecx, ecx
  0005d	e8 00 00 00 00	 call	 _invoke_watson
  00062	90		 npad	 1
$LN3@Allocate_m:

; 191  :     void* const _Ptr = reinterpret_cast<void*>((_Ptr_container + _Non_user_size) & ~(_Big_allocation_alignment - 1));

  00063	48 8b 44 24 30	 mov	 rax, QWORD PTR _Ptr_container$[rsp]
  00068	48 83 c0 27	 add	 rax, 39			; 00000027H
  0006c	48 83 e0 e0	 and	 rax, -32		; ffffffffffffffe0H
  00070	48 89 44 24 40	 mov	 QWORD PTR _Ptr$[rsp], rax

; 192  :     static_cast<uintptr_t*>(_Ptr)[-1] = _Ptr_container;

  00075	b8 08 00 00 00	 mov	 eax, 8
  0007a	48 6b c0 ff	 imul	 rax, rax, -1
  0007e	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  00083	48 8b 54 24 30	 mov	 rdx, QWORD PTR _Ptr_container$[rsp]
  00088	48 89 14 01	 mov	 QWORD PTR [rcx+rax], rdx

; 193  : 
; 194  : #ifdef _DEBUG
; 195  :     static_cast<uintptr_t*>(_Ptr)[-2] = _Big_allocation_sentinel;
; 196  : #endif // defined(_DEBUG)
; 197  :     return _Ptr;

  0008c	48 8b 44 24 40	 mov	 rax, QWORD PTR _Ptr$[rsp]
$LN4@Allocate_m:

; 198  : }

  00091	48 83 c4 58	 add	 rsp, 88			; 00000058H
  00095	c3		 ret	 0
??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ENDP ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??__F?inst@?$ISingleton@VtheManagerCounter@mu2@@@mu2@@1VtheManagerCounter@2@A@@YAXXZ
text$yd	SEGMENT
??__F?inst@?$ISingleton@VtheManagerCounter@mu2@@@mu2@@1VtheManagerCounter@2@A@@YAXXZ PROC ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::theManagerCounter>::inst'', COMDAT
  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00004	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VtheManagerCounter@mu2@@@mu2@@1VtheManagerCounter@2@A ; mu2::ISingleton<mu2::theManagerCounter>::inst
  0000b	48 05 40 02 00
	00		 add	 rax, 576		; 00000240H
  00011	48 8b c8	 mov	 rcx, rax
  00014	e8 00 00 00 00	 call	 ??1?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAA@XZ ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,__int64 volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > >,0> >::~_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,__int64 volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > >,0> >
  00019	90		 npad	 1
  0001a	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VtheManagerCounter@mu2@@@mu2@@1VtheManagerCounter@2@A ; mu2::ISingleton<mu2::theManagerCounter>::inst
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 ??1?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAA@XZ ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > >,0> >::~_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > >,0> >
  0002d	90		 npad	 1

; 80   : 	virtual~ISingleton() {}

  0002e	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VtheManagerCounter@mu2@@@mu2@@1VtheManagerCounter@2@A ; mu2::ISingleton<mu2::theManagerCounter>::inst
  00035	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$ISingleton@VtheManagerCounter@mu2@@@mu2@@6B@
  0003c	48 89 08	 mov	 QWORD PTR [rax], rcx
  0003f	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00043	c3		 ret	 0
??__F?inst@?$ISingleton@VtheManagerCounter@mu2@@@mu2@@1VtheManagerCounter@2@A@@YAXXZ ENDP ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::theManagerCounter>::inst''
text$yd	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??__E?inst@?$ISingleton@VtheManagerCounter@mu2@@@mu2@@1VtheManagerCounter@2@A@@YAXXZ
text$di	SEGMENT
??__E?inst@?$ISingleton@VtheManagerCounter@mu2@@@mu2@@1VtheManagerCounter@2@A@@YAXXZ PROC ; `dynamic initializer for 'mu2::ISingleton<mu2::theManagerCounter>::inst'', COMDAT

; 90   : template<typename T> T ISingleton<T>::inst;

  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?inst@?$ISingleton@VtheManagerCounter@mu2@@@mu2@@1VtheManagerCounter@2@A ; mu2::ISingleton<mu2::theManagerCounter>::inst
  0000b	e8 00 00 00 00	 call	 ??0theManagerCounter@mu2@@QEAA@XZ
  00010	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??__F?inst@?$ISingleton@VtheManagerCounter@mu2@@@mu2@@1VtheManagerCounter@2@A@@YAXXZ ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::theManagerCounter>::inst''
  00017	e8 00 00 00 00	 call	 atexit
  0001c	90		 npad	 1
  0001d	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00021	c3		 ret	 0
??__E?inst@?$ISingleton@VtheManagerCounter@mu2@@@mu2@@1VtheManagerCounter@2@A@@YAXXZ ENDP ; `dynamic initializer for 'mu2::ISingleton<mu2::theManagerCounter>::inst''
text$di	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??_GtheManagerCounter@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GtheManagerCounter@mu2@@UEAAPEAXI@Z PROC		; mu2::theManagerCounter::`scalar deleting destructor', COMDAT
$LN175:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 05 40 02 00
	00		 add	 rax, 576		; 00000240H
  00018	48 8b c8	 mov	 rcx, rax
  0001b	e8 00 00 00 00	 call	 ??1?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAA@XZ ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,__int64 volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > >,0> >::~_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,__int64 volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > >,0> >
  00020	90		 npad	 1
  00021	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00026	48 83 c0 08	 add	 rax, 8
  0002a	48 8b c8	 mov	 rcx, rax
  0002d	e8 00 00 00 00	 call	 ??1?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAA@XZ ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > >,0> >::~_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > >,0> >
  00032	90		 npad	 1

; 80   : 	virtual~ISingleton() {}

  00033	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$ISingleton@VtheManagerCounter@mu2@@@mu2@@6B@
  0003f	48 89 08	 mov	 QWORD PTR [rax], rcx
  00042	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00046	83 e0 01	 and	 eax, 1
  00049	85 c0		 test	 eax, eax
  0004b	74 10		 je	 SHORT $LN2@scalar
  0004d	ba 80 04 00 00	 mov	 edx, 1152		; 00000480H
  00052	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00057	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  0005c	90		 npad	 1
$LN2@scalar:
  0005d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00062	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00066	c3		 ret	 0
??_GtheManagerCounter@mu2@@UEAAPEAXI@Z ENDP		; mu2::theManagerCounter::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\concurrent_unordered_map.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\concurrent_unordered_map.h
;	COMDAT ??0theManagerCounter@mu2@@QEAA@XZ
_TEXT	SEGMENT
$T1 = 48
$T2 = 49
$T3 = 50
$T4 = 51
this$ = 56
$T5 = 64
$T6 = 72
$T7 = 80
$T8 = 88
this$ = 112
??0theManagerCounter@mu2@@QEAA@XZ PROC			; mu2::theManagerCounter::theManagerCounter, COMDAT
$LN213:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 68	 sub	 rsp, 104		; 00000068H
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 84   : 	ISingleton() {}	

  00009	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$ISingleton@VtheManagerCounter@mu2@@@mu2@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx
  00018	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7theManagerCounter@mu2@@6B@
  00024	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 974  :     constexpr allocator() noexcept {}

  00027	48 8d 44 24 30	 lea	 rax, QWORD PTR $T1[rsp]
  0002c	48 89 44 24 48	 mov	 QWORD PTR $T6[rsp], rax
  00031	48 8b 44 24 48	 mov	 rax, QWORD PTR $T6[rsp]
  00036	48 8b 4c 24 70	 mov	 rcx, QWORD PTR this$[rsp]
  0003b	48 83 c1 08	 add	 rcx, 8
  0003f	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  00044	4c 8d 4c 24 31	 lea	 r9, QWORD PTR $T2[rsp]
  00049	4c 8d 44 24 32	 lea	 r8, QWORD PTR $T3[rsp]
  0004e	ba 08 00 00 00	 mov	 edx, 8
  00053	e8 00 00 00 00	 call	 ??0?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@_KAEBU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@AEBU?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@3@AEBV?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@3@@Z ; Concurrency::concurrent_unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >::concurrent_unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >
  00058	90		 npad	 1
  00059	48 8d 44 24 33	 lea	 rax, QWORD PTR $T4[rsp]
  0005e	48 89 44 24 58	 mov	 QWORD PTR $T8[rsp], rax
  00063	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  00068	48 05 40 02 00
	00		 add	 rax, 576		; 00000240H
  0006e	48 89 44 24 38	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h

; 42   :     }

  00073	48 8d 44 24 40	 lea	 rax, QWORD PTR $T5[rsp]
  00078	48 89 44 24 50	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\concurrent_unordered_map.h

; 56   :     _Concurrent_unordered_map_traits(const _Key_compare& _Traits) : _M_comparator(_Traits)

  0007d	48 8b 44 24 50	 mov	 rax, QWORD PTR $T7[rsp]
  00082	0f b7 00	 movzx	 eax, WORD PTR [rax]
  00085	48 8b 4c 24 38	 mov	 rcx, QWORD PTR this$[rsp]
  0008a	66 89 01	 mov	 WORD PTR [rcx], ax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h

; 128  :         : _Traits(_Parg), _M_split_ordered_list(_Allocator), _M_allocator(_Allocator), _M_number_of_buckets(_Number_of_buckets), _M_maximum_bucket_size((float) _Initial_bucket_load)

  0008d	48 8b 44 24 38	 mov	 rax, QWORD PTR this$[rsp]
  00092	48 05 08 02 00
	00		 add	 rax, 520		; 00000208H
  00098	48 8b 4c 24 58	 mov	 rcx, QWORD PTR $T8[rsp]
  0009d	0f b6 11	 movzx	 edx, BYTE PTR [rcx]
  000a0	48 8b c8	 mov	 rcx, rax
  000a3	e8 00 00 00 00	 call	 ??0?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAA@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@std@@@Z ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > >::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > >
  000a8	90		 npad	 1
  000a9	48 8b 44 24 38	 mov	 rax, QWORD PTR this$[rsp]
  000ae	48 c7 80 28 02
	00 00 08 00 00
	00		 mov	 QWORD PTR [rax+552], 8
  000b9	48 8b 44 24 38	 mov	 rax, QWORD PTR this$[rsp]
  000be	f3 0f 10 05 00
	00 00 00	 movss	 xmm0, DWORD PTR __real@40800000
  000c6	f3 0f 11 80 30
	02 00 00	 movss	 DWORD PTR [rax+560], xmm0

; 129  :     {
; 130  :         _Init();

  000ce	48 8b 4c 24 38	 mov	 rcx, QWORD PTR this$[rsp]
  000d3	e8 00 00 00 00	 call	 ?_Init@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAXXZ ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,__int64 volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > >,0> >::_Init
  000d8	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\concurrent_unordered_map.h

; 254  :         this->rehash(_Number_of_buckets);

  000d9	ba 08 00 00 00	 mov	 edx, 8
  000de	48 8b 4c 24 38	 mov	 rcx, QWORD PTR this$[rsp]
  000e3	e8 00 00 00 00	 call	 ?rehash@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAX_K@Z ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,__int64 volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > >,0> >::rehash
  000e8	90		 npad	 1
  000e9	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  000ee	c6 80 78 04 00
	00 00		 mov	 BYTE PTR [rax+1144], 0
  000f5	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  000fa	48 83 c4 68	 add	 rsp, 104		; 00000068H
  000fe	c3		 ret	 0
??0theManagerCounter@mu2@@QEAA@XZ ENDP			; mu2::theManagerCounter::theManagerCounter
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 48
$T2 = 49
$T3 = 50
$T4 = 51
this$ = 56
$T5 = 64
$T6 = 72
$T7 = 80
$T8 = 88
this$ = 112
?dtor$0@?0???0theManagerCounter@mu2@@QEAA@XZ@4HA PROC	; `mu2::theManagerCounter::theManagerCounter'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 70	 mov	 rcx, QWORD PTR this$[rbp]
  0000d	e8 00 00 00 00	 call	 ??1?$ISingleton@VtheManagerCounter@mu2@@@mu2@@UEAA@XZ ; mu2::ISingleton<mu2::theManagerCounter>::~ISingleton<mu2::theManagerCounter>
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$0@?0???0theManagerCounter@mu2@@QEAA@XZ@4HA ENDP	; `mu2::theManagerCounter::theManagerCounter'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 48
$T2 = 49
$T3 = 50
$T4 = 51
this$ = 56
$T5 = 64
$T6 = 72
$T7 = 80
$T8 = 88
this$ = 112
?dtor$1@?0???0theManagerCounter@mu2@@QEAA@XZ@4HA PROC	; `mu2::theManagerCounter::theManagerCounter'::`1'::dtor$1
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 70	 mov	 rcx, QWORD PTR this$[rbp]
  0000d	48 83 c1 08	 add	 rcx, 8
  00011	e8 00 00 00 00	 call	 ??1?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@XZ
  00016	48 83 c4 20	 add	 rsp, 32			; 00000020H
  0001a	5d		 pop	 rbp
  0001b	c3		 ret	 0
?dtor$1@?0???0theManagerCounter@mu2@@QEAA@XZ@4HA ENDP	; `mu2::theManagerCounter::theManagerCounter'::`1'::dtor$1
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 48
$T2 = 49
$T3 = 50
$T4 = 51
this$ = 56
$T5 = 64
$T6 = 72
$T7 = 80
$T8 = 88
this$ = 112
?dtor$12@?0???0theManagerCounter@mu2@@QEAA@XZ@4HA PROC	; `mu2::theManagerCounter::theManagerCounter'::`1'::dtor$12
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 38	 mov	 rcx, QWORD PTR this$[rbp]
  0000d	48 81 c1 08 02
	00 00		 add	 rcx, 520		; 00000208H
  00014	e8 00 00 00 00	 call	 ??1?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAA@XZ ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > >::~_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > >
  00019	48 83 c4 20	 add	 rsp, 32			; 00000020H
  0001d	5d		 pop	 rbp
  0001e	c3		 ret	 0
?dtor$12@?0???0theManagerCounter@mu2@@QEAA@XZ@4HA ENDP	; `mu2::theManagerCounter::theManagerCounter'::`1'::dtor$12
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 48
$T2 = 49
$T3 = 50
$T4 = 51
this$ = 56
$T5 = 64
$T6 = 72
$T7 = 80
$T8 = 88
this$ = 112
?dtor$11@?0???0theManagerCounter@mu2@@QEAA@XZ@4HA PROC	; `mu2::theManagerCounter::theManagerCounter'::`1'::dtor$11
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 38	 mov	 rcx, QWORD PTR this$[rbp]
  0000d	e8 00 00 00 00	 call	 ??1?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAA@XZ ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,__int64 volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > >,0> >::~_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,__int64 volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > >,0> >
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$11@?0???0theManagerCounter@mu2@@QEAA@XZ@4HA ENDP	; `mu2::theManagerCounter::theManagerCounter'::`1'::dtor$11
text$x	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Counter.h
;	COMDAT ?UnInit@theManagerCounter@mu2@@UEAA_NXZ
_TEXT	SEGMENT
this$ = 48
?UnInit@theManagerCounter@mu2@@UEAA_NXZ PROC		; mu2::theManagerCounter::UnInit, COMDAT

; 71   : 		{

$LN393:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 72   : 			mapCounter.clear();

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 c0 08	 add	 rax, 8
  00012	48 8b c8	 mov	 rcx, rax
  00015	e8 00 00 00 00	 call	 ?clear@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAXXZ ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > >,0> >::clear

; 73   : 			mapAccumulateCounter.clear();

  0001a	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001f	48 05 40 02 00
	00		 add	 rax, 576		; 00000240H
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 ?clear@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAXXZ ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,__int64 volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > >,0> >::clear

; 74   : 
; 75   : 			return true;

  0002d	b0 01		 mov	 al, 1

; 76   : 		}

  0002f	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00033	c3		 ret	 0
?UnInit@theManagerCounter@mu2@@UEAA_NXZ ENDP		; mu2::theManagerCounter::UnInit
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Counter.h
;	COMDAT ?Init@theManagerCounter@mu2@@UEAA_NPEAX@Z
_TEXT	SEGMENT
this$ = 8
param$ = 16
?Init@theManagerCounter@mu2@@UEAA_NPEAX@Z PROC		; mu2::theManagerCounter::Init, COMDAT

; 65   : 		{

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 66   : 			UNREFERENCED_PARAMETER(param);
; 67   : 			isInitialized = true;

  0000a	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0000f	c6 80 78 04 00
	00 01		 mov	 BYTE PTR [rax+1144], 1

; 68   : 			return true;

  00016	b0 01		 mov	 al, 1

; 69   : 		}

  00018	c3		 ret	 0
?Init@theManagerCounter@mu2@@UEAA_NPEAX@Z ENDP		; mu2::theManagerCounter::Init
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?allocate@?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@QEAAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@2@_K@Z
_TEXT	SEGMENT
_Overflow_is_possible$1 = 32
_Bytes$ = 40
$T2 = 48
$T3 = 56
$T4 = 64
_Max_possible$5 = 72
this$ = 96
_Count$ = 104
?allocate@?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@QEAAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@2@_K@Z PROC ; std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > > > >::allocate, COMDAT

; 988  :     _NODISCARD_RAW_PTR_ALLOC _CONSTEXPR20 __declspec(allocator) _Ty* allocate(_CRT_GUARDOVERFLOW const size_t _Count) {

$LN13:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 113  :     constexpr bool _Overflow_is_possible = _Ty_size > 1;

  0000e	c6 44 24 20 01	 mov	 BYTE PTR _Overflow_is_possible$1[rsp], 1

; 114  : 
; 115  :     if constexpr (_Overflow_is_possible) {
; 116  :         constexpr size_t _Max_possible = static_cast<size_t>(-1) / _Ty_size;

  00013	48 b8 ff ff ff
	ff ff ff ff 1f	 mov	 rax, 2305843009213693951 ; 1fffffffffffffffH
  0001d	48 89 44 24 48	 mov	 QWORD PTR _Max_possible$5[rsp], rax

; 117  :         if (_Count > _Max_possible) {

  00022	48 b8 ff ff ff
	ff ff ff ff 1f	 mov	 rax, 2305843009213693951 ; 1fffffffffffffffH
  0002c	48 39 44 24 68	 cmp	 QWORD PTR _Count$[rsp], rax
  00031	76 06		 jbe	 SHORT $LN4@allocate

; 118  :             _Throw_bad_array_new_length(); // multiply overflow

  00033	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00038	90		 npad	 1
$LN4@allocate:

; 119  :         }
; 120  :     }
; 121  : 
; 122  :     return _Count * _Ty_size;

  00039	48 8b 44 24 68	 mov	 rax, QWORD PTR _Count$[rsp]
  0003e	48 c1 e0 03	 shl	 rax, 3
  00042	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00047	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
  0004c	48 89 44 24 28	 mov	 QWORD PTR _Bytes$[rsp], rax

; 227  :     if (_Bytes == 0) {

  00051	48 83 7c 24 28
	00		 cmp	 QWORD PTR _Bytes$[rsp], 0
  00057	75 0b		 jne	 SHORT $LN8@allocate

; 228  :         return nullptr;

  00059	48 c7 44 24 30
	00 00 00 00	 mov	 QWORD PTR $T2[rsp], 0
  00062	eb 35		 jmp	 SHORT $LN7@allocate
$LN8@allocate:

; 229  :     }
; 230  : 
; 231  : #if _HAS_CXX20 // TRANSITION, GH-1532
; 232  :     if (_STD is_constant_evaluated()) {
; 233  :         return _Traits::_Allocate(_Bytes);
; 234  :     }
; 235  : #endif // _HAS_CXX20
; 236  : 
; 237  : #ifdef __cpp_aligned_new
; 238  :     if constexpr (_Align > __STDCPP_DEFAULT_NEW_ALIGNMENT__) {
; 239  :         size_t _Passed_align = _Align;
; 240  : #if defined(_M_IX86) || defined(_M_X64)
; 241  :         if (_Bytes >= _Big_allocation_threshold) {
; 242  :             // boost the alignment of big allocations to help autovectorization
; 243  :             _Passed_align = (_STD max)(_Align, _Big_allocation_alignment);
; 244  :         }
; 245  : #endif // defined(_M_IX86) || defined(_M_X64)
; 246  :         return _Traits::_Allocate_aligned(_Bytes, _Passed_align);
; 247  :     } else
; 248  : #endif // defined(__cpp_aligned_new)
; 249  :     {
; 250  : #if defined(_M_IX86) || defined(_M_X64)
; 251  :         if (_Bytes >= _Big_allocation_threshold) {

  00064	48 81 7c 24 28
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  0006d	72 11		 jb	 SHORT $LN9@allocate

; 252  :             // boost the alignment of big allocations to help autovectorization
; 253  :             return _Allocate_manually_vector_aligned<_Traits>(_Bytes);

  0006f	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00074	e8 00 00 00 00	 call	 ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
  00079	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
  0007e	eb 19		 jmp	 SHORT $LN7@allocate
$LN9@allocate:

; 136  :         return ::operator new(_Bytes);

  00080	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00085	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  0008a	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax

; 256  :         return _Traits::_Allocate(_Bytes);

  0008f	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  00094	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
$LN7@allocate:

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00099	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
$LN6@allocate:

; 991  :     }

  0009e	48 83 c4 58	 add	 rsp, 88			; 00000058H
  000a2	c3		 ret	 0
?allocate@?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@QEAAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@2@_K@Z ENDP ; std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > > > >::allocate
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h
;	COMDAT ?_Set_bucket@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAX_KV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@Z
_TEXT	SEGMENT
$T1 = 32
_Segment$ = 40
_Index$2 = 48
_Seg_size$3 = 56
_New_segment$4 = 64
_Bytes$ = 72
tv142 = 80
tv95 = 88
_Ptr$ = 96
$T5 = 104
tv157 = 112
$T6 = 120
tv168 = 128
$T7 = 136
this$ = 160
_Bucket$ = 168
_Dummy_head$ = 176
?_Set_bucket@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAX_KV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@Z PROC ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,__int64 volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > >,0> >::_Set_bucket, COMDAT

; 1248 :     {

$LN91:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 81 ec 98 00
	00 00		 sub	 rsp, 152		; 00000098H

; 73   :     unsigned long _Index = 0;

  00016	c7 44 24 30 00
	00 00 00	 mov	 DWORD PTR _Index$2[rsp], 0

; 191  :         return size_type( _Get_msb( _Index|1 ) );

  0001e	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR _Bucket$[rsp]
  00026	48 83 c8 01	 or	 rax, 1

; 78   :     _BitScanReverse64(&_Index, _Mask);

  0002a	48 0f bd c0	 bsr	 rax, rax
  0002e	89 44 24 30	 mov	 DWORD PTR _Index$2[rsp], eax

; 79   : #endif  /* (defined (_M_IX86) || defined (_M_ARM)) */
; 80   : 
; 81   :     return (unsigned char) _Index;

  00032	0f b6 44 24 30	 movzx	 eax, BYTE PTR _Index$2[rsp]
  00037	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al

; 191  :         return size_type( _Get_msb( _Index|1 ) );

  0003b	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  00040	0f b6 c0	 movzx	 eax, al
  00043	48 89 44 24 68	 mov	 QWORD PTR $T5[rsp], rax

; 1249 :         size_type _Segment = _Segment_index_of(_Bucket);

  00048	48 8b 44 24 68	 mov	 rax, QWORD PTR $T5[rsp]
  0004d	48 89 44 24 28	 mov	 QWORD PTR _Segment$[rsp], rax

; 196  :         return (size_type(1)<<_K & ~size_type(1));

  00052	48 8b 44 24 28	 mov	 rax, QWORD PTR _Segment$[rsp]
  00057	b9 01 00 00 00	 mov	 ecx, 1
  0005c	48 89 4c 24 70	 mov	 QWORD PTR tv157[rsp], rcx
  00061	0f b6 c8	 movzx	 ecx, al
  00064	48 8b 44 24 70	 mov	 rax, QWORD PTR tv157[rsp]
  00069	48 d3 e0	 shl	 rax, cl
  0006c	48 83 e0 fe	 and	 rax, -2
  00070	48 89 44 24 78	 mov	 QWORD PTR $T6[rsp], rax

; 1250 :         _Bucket -= _Segment_base(_Segment);

  00075	48 8b 44 24 78	 mov	 rax, QWORD PTR $T6[rsp]
  0007a	48 8b 8c 24 a8
	00 00 00	 mov	 rcx, QWORD PTR _Bucket$[rsp]
  00082	48 2b c8	 sub	 rcx, rax
  00085	48 8b c1	 mov	 rax, rcx
  00088	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR _Bucket$[rsp], rax

; 1251 : 
; 1252 :         if (_M_buckets[_Segment] == nullptr)

  00090	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00098	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Segment$[rsp]
  0009d	48 83 7c c8 08
	00		 cmp	 QWORD PTR [rax+rcx*8+8], 0
  000a3	0f 85 08 01 00
	00		 jne	 $LN2@Set_bucket

; 201  :         return _K ? size_type(1)<<_K : 2;

  000a9	48 83 7c 24 28
	00		 cmp	 QWORD PTR _Segment$[rsp], 0
  000af	74 27		 je	 SHORT $LN13@Set_bucket
  000b1	48 8b 44 24 28	 mov	 rax, QWORD PTR _Segment$[rsp]
  000b6	b9 01 00 00 00	 mov	 ecx, 1
  000bb	48 89 8c 24 80
	00 00 00	 mov	 QWORD PTR tv168[rsp], rcx
  000c3	0f b6 c8	 movzx	 ecx, al
  000c6	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR tv168[rsp]
  000ce	48 d3 e0	 shl	 rax, cl
  000d1	48 89 44 24 50	 mov	 QWORD PTR tv142[rsp], rax
  000d6	eb 09		 jmp	 SHORT $LN14@Set_bucket
$LN13@Set_bucket:
  000d8	48 c7 44 24 50
	02 00 00 00	 mov	 QWORD PTR tv142[rsp], 2
$LN14@Set_bucket:
  000e1	48 8b 44 24 50	 mov	 rax, QWORD PTR tv142[rsp]
  000e6	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T7[rsp], rax

; 1253 :         {
; 1254 :             size_type _Seg_size = _Segment_size(_Segment);

  000ee	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T7[rsp]
  000f6	48 89 44 24 38	 mov	 QWORD PTR _Seg_size$3[rsp], rax

; 1255 :             _Full_iterator * _New_segment = _M_allocator.allocate(_Seg_size);

  000fb	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00103	48 05 20 02 00
	00		 add	 rax, 544		; 00000220H
  00109	48 8b 54 24 38	 mov	 rdx, QWORD PTR _Seg_size$3[rsp]
  0010e	48 8b c8	 mov	 rcx, rax
  00111	e8 00 00 00 00	 call	 ?allocate@?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@QEAAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@2@_K@Z ; std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > > > >::allocate
  00116	48 89 44 24 40	 mov	 QWORD PTR _New_segment$4[rsp], rax

; 1256 :             ::std::_Uninitialized_value_construct_n(_New_segment, _Seg_size, _M_allocator);

  0011b	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00123	48 05 20 02 00
	00		 add	 rax, 544		; 00000220H
  00129	4c 8b c0	 mov	 r8, rax
  0012c	48 8b 54 24 38	 mov	 rdx, QWORD PTR _Seg_size$3[rsp]
  00131	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _New_segment$4[rsp]
  00136	e8 00 00 00 00	 call	 ??$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@0@@Z ; std::_Uninitialized_value_construct_n<std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > > > > >
  0013b	90		 npad	 1

; 1257 :             if (_InterlockedCompareExchangePointer((void * volatile *) &_M_buckets[_Segment], _New_segment, nullptr) != nullptr)

  0013c	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00144	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Segment$[rsp]
  00149	48 8d 44 c8 08	 lea	 rax, QWORD PTR [rax+rcx*8+8]
  0014e	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _New_segment$4[rsp]
  00153	48 89 44 24 58	 mov	 QWORD PTR tv95[rsp], rax
  00158	33 c0		 xor	 eax, eax
  0015a	48 8b 54 24 58	 mov	 rdx, QWORD PTR tv95[rsp]
  0015f	48 8b 54 24 58	 mov	 rdx, QWORD PTR tv95[rsp]
  00164	f0 48 0f b1 0a	 lock cmpxchg QWORD PTR [rdx], rcx
  00169	48 85 c0	 test	 rax, rax
  0016c	74 43		 je	 SHORT $LN2@Set_bucket
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  0016e	48 8b 44 24 38	 mov	 rax, QWORD PTR _Seg_size$3[rsp]
  00173	48 c1 e0 03	 shl	 rax, 3
  00177	48 89 44 24 48	 mov	 QWORD PTR _Bytes$[rsp], rax
  0017c	48 8b 44 24 40	 mov	 rax, QWORD PTR _New_segment$4[rsp]
  00181	48 89 44 24 60	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  00186	48 81 7c 24 48
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  0018f	72 10		 jb	 SHORT $LN82@Set_bucket

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  00191	48 8d 54 24 48	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  00196	48 8d 4c 24 60	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  0019b	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  001a0	90		 npad	 1
$LN82@Set_bucket:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  001a1	48 8b 54 24 48	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  001a6	48 8b 4c 24 60	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  001ab	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  001b0	90		 npad	 1
$LN2@Set_bucket:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h

; 1262 :         _M_buckets[_Segment][_Bucket] = _Dummy_head;

  001b1	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  001b9	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Segment$[rsp]
  001be	48 8b 44 c8 08	 mov	 rax, QWORD PTR [rax+rcx*8+8]
  001c3	48 8b 8c 24 a8
	00 00 00	 mov	 rcx, QWORD PTR _Bucket$[rsp]
  001cb	48 8b 94 24 b0
	00 00 00	 mov	 rdx, QWORD PTR _Dummy_head$[rsp]
  001d3	48 89 14 c8	 mov	 QWORD PTR [rax+rcx*8], rdx

; 1263 :     }

  001d7	48 81 c4 98 00
	00 00		 add	 rsp, 152		; 00000098H
  001de	c3		 ret	 0
?_Set_bucket@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAX_KV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@Z ENDP ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,__int64 volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > >,0> >::_Set_bucket
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_split_ordered_list.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\forward_list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h
;	COMDAT ?_Init@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAXXZ
_TEXT	SEGMENT
__param0$ = 32
_Dummy_node$ = 40
this$ = 64
?_Init@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAXXZ PROC ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,__int64 volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > >,0> >::_Init, COMDAT

; 942  :     {

$LN115:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 943  :         // Allocate an array of segment pointers
; 944  :         memset(_M_buckets, 0, _Pointers_per_table * sizeof(void *));

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 c0 08	 add	 rax, 8
  00012	41 b8 00 02 00
	00		 mov	 r8d, 512		; 00000200H
  00018	33 d2		 xor	 edx, edx
  0001a	48 8b c8	 mov	 rcx, rax
  0001d	e8 00 00 00 00	 call	 memset
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_split_ordered_list.h

; 465  :         return _Full_iterator(this->_Myhead, this);

  00022	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00027	48 8b 80 08 02
	00 00		 mov	 rax, QWORD PTR [rax+520]
  0002e	48 89 44 24 20	 mov	 QWORD PTR __param0$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\forward_list

; 37   :     _Flist_unchecked_const_iterator(_Nodeptr _Pnode, const _Mylist* _Plist) noexcept : _Ptr(_Pnode) {

  00033	48 8b 44 24 20	 mov	 rax, QWORD PTR __param0$[rsp]
  00038	48 89 44 24 28	 mov	 QWORD PTR _Dummy_node$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h

; 948  :         _Set_bucket(0, _Dummy_node);

  0003d	4c 8b 44 24 28	 mov	 r8, QWORD PTR _Dummy_node$[rsp]
  00042	33 d2		 xor	 edx, edx
  00044	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00049	e8 00 00 00 00	 call	 ?_Set_bucket@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAX_KV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@@Z ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,__int64 volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > >,0> >::_Set_bucket
  0004e	90		 npad	 1

; 949  :     }

  0004f	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00053	c3		 ret	 0
?_Init@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAXXZ ENDP ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,__int64 volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > >,0> >::_Init
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h
;	COMDAT ?rehash@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAX_K@Z
_TEXT	SEGMENT
$T1 = 32
_Index$2 = 36
tv82 = 40
_Current_buckets$ = 48
$T3 = 56
$T4 = 64
tv95 = 72
this$ = 96
_Buckets$ = 104
?rehash@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAX_K@Z PROC ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,__int64 volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > >,0> >::rehash, COMDAT

; 847  :     {

$LN15:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 848  :         size_type _Current_buckets = _M_number_of_buckets;

  0000e	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00013	48 8b 80 28 02
	00 00		 mov	 rax, QWORD PTR [rax+552]
  0001a	48 89 44 24 30	 mov	 QWORD PTR _Current_buckets$[rsp], rax

; 849  : 
; 850  :         if (_Current_buckets > _Buckets)

  0001f	48 8b 44 24 68	 mov	 rax, QWORD PTR _Buckets$[rsp]
  00024	48 39 44 24 30	 cmp	 QWORD PTR _Current_buckets$[rsp], rax
  00029	76 07		 jbe	 SHORT $LN2@rehash

; 851  :         {
; 852  :             return;

  0002b	e9 a4 00 00 00	 jmp	 $LN1@rehash

; 853  :         }

  00030	eb 56		 jmp	 SHORT $LN3@rehash
$LN2@rehash:

; 854  :         else if (_Buckets <= 0 || _Buckets > unsafe_max_bucket_count())

  00032	48 83 7c 24 68
	00		 cmp	 QWORD PTR _Buckets$[rsp], 0
  00038	76 41		 jbe	 SHORT $LN5@rehash

; 201  :         return _K ? size_type(1)<<_K : 2;

  0003a	33 c0		 xor	 eax, eax
  0003c	48 83 f8 3f	 cmp	 rax, 63			; 0000003fH
  00040	74 10		 je	 SHORT $LN11@rehash
  00042	b8 01 00 00 00	 mov	 eax, 1
  00047	48 c1 e0 3f	 shl	 rax, 63			; 0000003fH
  0004b	48 89 44 24 28	 mov	 QWORD PTR tv82[rsp], rax
  00050	eb 09		 jmp	 SHORT $LN12@rehash
$LN11@rehash:
  00052	48 c7 44 24 28
	02 00 00 00	 mov	 QWORD PTR tv82[rsp], 2
$LN12@rehash:
  0005b	48 8b 44 24 28	 mov	 rax, QWORD PTR tv82[rsp]
  00060	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax

; 581  :         return _Segment_size(_Pointers_per_table-1);

  00065	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
  0006a	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax

; 854  :         else if (_Buckets <= 0 || _Buckets > unsafe_max_bucket_count())

  0006f	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  00074	48 39 44 24 68	 cmp	 QWORD PTR _Buckets$[rsp], rax
  00079	76 0d		 jbe	 SHORT $LN4@rehash
$LN5@rehash:

; 855  :         {
; 856  :             _STD _Xout_of_range("invalid number of buckets");

  0007b	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_0BK@KDCGEAPC@invalid?5number?5of?5buckets@
  00082	e8 00 00 00 00	 call	 ?_Xout_of_range@std@@YAXPEBD@Z ; std::_Xout_of_range
  00087	90		 npad	 1
$LN4@rehash:
$LN3@rehash:

; 73   :     unsigned long _Index = 0;

  00088	c7 44 24 24 00
	00 00 00	 mov	 DWORD PTR _Index$2[rsp], 0

; 857  :         }
; 858  :         // Round up the number of buckets to the next largest power of 2
; 859  :         _M_number_of_buckets = ((size_type) 1) << _Get_msb(_Buckets*2-1);

  00090	48 8b 44 24 68	 mov	 rax, QWORD PTR _Buckets$[rsp]
  00095	48 8d 44 00 ff	 lea	 rax, QWORD PTR [rax+rax-1]

; 78   :     _BitScanReverse64(&_Index, _Mask);

  0009a	48 0f bd c0	 bsr	 rax, rax
  0009e	89 44 24 24	 mov	 DWORD PTR _Index$2[rsp], eax

; 79   : #endif  /* (defined (_M_IX86) || defined (_M_ARM)) */
; 80   : 
; 81   :     return (unsigned char) _Index;

  000a2	0f b6 44 24 24	 movzx	 eax, BYTE PTR _Index$2[rsp]
  000a7	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al

; 857  :         }
; 858  :         // Round up the number of buckets to the next largest power of 2
; 859  :         _M_number_of_buckets = ((size_type) 1) << _Get_msb(_Buckets*2-1);

  000ab	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  000b0	0f b6 c0	 movzx	 eax, al
  000b3	b9 01 00 00 00	 mov	 ecx, 1
  000b8	48 89 4c 24 48	 mov	 QWORD PTR tv95[rsp], rcx
  000bd	0f b6 c8	 movzx	 ecx, al
  000c0	48 8b 44 24 48	 mov	 rax, QWORD PTR tv95[rsp]
  000c5	48 d3 e0	 shl	 rax, cl
  000c8	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  000cd	48 89 81 28 02
	00 00		 mov	 QWORD PTR [rcx+552], rax
$LN1@rehash:
$LN6@rehash:

; 860  :     }

  000d4	48 83 c4 58	 add	 rsp, 88			; 00000058H
  000d8	c3		 ret	 0
?rehash@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAX_K@Z ENDP ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,__int64 volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > >,0> >::rehash
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h
;	COMDAT ?clear@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAXXZ
_TEXT	SEGMENT
_Index$1 = 32
_Index2$2 = 40
_Bytes$ = 48
tv134 = 56
_Seg_size$3 = 64
_Ptr$ = 72
tv84 = 80
$T4 = 88
_Ptr$ = 96
this$ = 128
?clear@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAXXZ PROC ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,__int64 volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > >,0> >::clear, COMDAT

; 445  :     {

$LN209:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 78	 sub	 rsp, 120		; 00000078H

; 446  :         // Clear list
; 447  :         _M_split_ordered_list.clear();

  00009	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00011	48 05 08 02 00
	00		 add	 rax, 520		; 00000208H
  00017	48 8b c8	 mov	 rcx, rax
  0001a	e8 00 00 00 00	 call	 ?clear@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAAXXZ ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > >::clear
  0001f	90		 npad	 1

; 448  : 
; 449  :         // Clear buckets
; 450  :         for (size_type _Index = 0; _Index < _Pointers_per_table; ++_Index)

  00020	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR _Index$1[rsp], 0
  00029	eb 0d		 jmp	 SHORT $LN4@clear
$LN2@clear:
  0002b	48 8b 44 24 20	 mov	 rax, QWORD PTR _Index$1[rsp]
  00030	48 ff c0	 inc	 rax
  00033	48 89 44 24 20	 mov	 QWORD PTR _Index$1[rsp], rax
$LN4@clear:
  00038	48 83 7c 24 20
	40		 cmp	 QWORD PTR _Index$1[rsp], 64 ; 00000040H
  0003e	0f 83 e4 00 00
	00		 jae	 $LN3@clear

; 451  :         {
; 452  :             if (_M_buckets[_Index] != nullptr)

  00044	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0004c	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Index$1[rsp]
  00051	48 83 7c c8 08
	00		 cmp	 QWORD PTR [rax+rcx*8+8], 0
  00057	0f 84 c6 00 00
	00		 je	 $LN8@clear

; 201  :         return _K ? size_type(1)<<_K : 2;

  0005d	48 83 7c 24 20
	00		 cmp	 QWORD PTR _Index$1[rsp], 0
  00063	74 21		 je	 SHORT $LN76@clear
  00065	48 8b 44 24 20	 mov	 rax, QWORD PTR _Index$1[rsp]
  0006a	b9 01 00 00 00	 mov	 ecx, 1
  0006f	48 89 4c 24 50	 mov	 QWORD PTR tv84[rsp], rcx
  00074	0f b6 c8	 movzx	 ecx, al
  00077	48 8b 44 24 50	 mov	 rax, QWORD PTR tv84[rsp]
  0007c	48 d3 e0	 shl	 rax, cl
  0007f	48 89 44 24 38	 mov	 QWORD PTR tv134[rsp], rax
  00084	eb 09		 jmp	 SHORT $LN77@clear
$LN76@clear:
  00086	48 c7 44 24 38
	02 00 00 00	 mov	 QWORD PTR tv134[rsp], 2
$LN77@clear:
  0008f	48 8b 44 24 38	 mov	 rax, QWORD PTR tv134[rsp]
  00094	48 89 44 24 58	 mov	 QWORD PTR $T4[rsp], rax

; 453  :             {
; 454  :                 size_type _Seg_size = _Segment_size(_Index);

  00099	48 8b 44 24 58	 mov	 rax, QWORD PTR $T4[rsp]
  0009e	48 89 44 24 40	 mov	 QWORD PTR _Seg_size$3[rsp], rax

; 455  :                 for (size_type _Index2 = 0; _Index2 < _Seg_size; ++_Index2)

  000a3	48 c7 44 24 28
	00 00 00 00	 mov	 QWORD PTR _Index2$2[rsp], 0
  000ac	eb 0d		 jmp	 SHORT $LN7@clear
$LN5@clear:
  000ae	48 8b 44 24 28	 mov	 rax, QWORD PTR _Index2$2[rsp]
  000b3	48 ff c0	 inc	 rax
  000b6	48 89 44 24 28	 mov	 QWORD PTR _Index2$2[rsp], rax
$LN7@clear:
  000bb	48 8b 44 24 40	 mov	 rax, QWORD PTR _Seg_size$3[rsp]
  000c0	48 39 44 24 28	 cmp	 QWORD PTR _Index2$2[rsp], rax
  000c5	73 02		 jae	 SHORT $LN6@clear

; 456  :                 {
; 457  :                     _Alfi_traits::destroy(_M_allocator, &_M_buckets[_Index][_Index2]);
; 458  :                 }

  000c7	eb e5		 jmp	 SHORT $LN5@clear
$LN6@clear:

; 459  :                 _M_allocator.deallocate(_M_buckets[_Index], _Seg_size);

  000c9	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000d1	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Index$1[rsp]
  000d6	48 8b 44 c8 08	 mov	 rax, QWORD PTR [rax+rcx*8+8]
  000db	48 89 44 24 60	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  000e0	48 8b 44 24 40	 mov	 rax, QWORD PTR _Seg_size$3[rsp]
  000e5	48 c1 e0 03	 shl	 rax, 3
  000e9	48 89 44 24 30	 mov	 QWORD PTR _Bytes$[rsp], rax
  000ee	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  000f3	48 89 44 24 48	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  000f8	48 81 7c 24 30
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  00101	72 10		 jb	 SHORT $LN86@clear

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  00103	48 8d 54 24 30	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  00108	48 8d 4c 24 48	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  0010d	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  00112	90		 npad	 1
$LN86@clear:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  00113	48 8b 54 24 30	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  00118	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  0011d	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00122	90		 npad	 1
$LN8@clear:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h

; 461  :         }

  00123	e9 03 ff ff ff	 jmp	 $LN2@clear
$LN3@clear:

; 462  : 
; 463  :         // memset all the buckets to zero and initialize the dummy node 0
; 464  :         _Init();

  00128	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00130	e8 00 00 00 00	 call	 ?_Init@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAXXZ ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,__int64 volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > >,0> >::_Init
  00135	90		 npad	 1

; 465  :     }

  00136	48 83 c4 78	 add	 rsp, 120		; 00000078H
  0013a	c3		 ret	 0
?clear@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAXXZ ENDP ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,__int64 volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > >,0> >::clear
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_split_ordered_list.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h
;	COMDAT ??1?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAA@XZ
_TEXT	SEGMENT
_Index$1 = 32
this$ = 40
_Index2$2 = 48
_Bytes$ = 56
tv93 = 64
_Seg_size$3 = 72
_Ptr$ = 80
tv148 = 88
$T4 = 96
_Ptr$ = 104
_Pnode$5 = 112
this$ = 144
??1?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAA@XZ PROC ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,__int64 volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > >,0> >::~_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,__int64 volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > >,0> >, COMDAT

; 173  :     {

$LN225:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H

; 174  :         // Delete all node segments
; 175  :         for (size_type _Index = 0; _Index < _Pointers_per_table; ++_Index)

  0000c	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR _Index$1[rsp], 0
  00015	eb 0d		 jmp	 SHORT $LN4@Concurrent
$LN2@Concurrent:
  00017	48 8b 44 24 20	 mov	 rax, QWORD PTR _Index$1[rsp]
  0001c	48 ff c0	 inc	 rax
  0001f	48 89 44 24 20	 mov	 QWORD PTR _Index$1[rsp], rax
$LN4@Concurrent:
  00024	48 83 7c 24 20
	40		 cmp	 QWORD PTR _Index$1[rsp], 64 ; 00000040H
  0002a	0f 83 e4 00 00
	00		 jae	 $LN3@Concurrent

; 176  :         {
; 177  :             if (_M_buckets[_Index] != nullptr)

  00030	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Index$1[rsp]
  0003d	48 83 7c c8 08
	00		 cmp	 QWORD PTR [rax+rcx*8+8], 0
  00043	0f 84 c6 00 00
	00		 je	 $LN8@Concurrent

; 201  :         return _K ? size_type(1)<<_K : 2;

  00049	48 83 7c 24 20
	00		 cmp	 QWORD PTR _Index$1[rsp], 0
  0004f	74 21		 je	 SHORT $LN13@Concurrent
  00051	48 8b 44 24 20	 mov	 rax, QWORD PTR _Index$1[rsp]
  00056	b9 01 00 00 00	 mov	 ecx, 1
  0005b	48 89 4c 24 58	 mov	 QWORD PTR tv148[rsp], rcx
  00060	0f b6 c8	 movzx	 ecx, al
  00063	48 8b 44 24 58	 mov	 rax, QWORD PTR tv148[rsp]
  00068	48 d3 e0	 shl	 rax, cl
  0006b	48 89 44 24 40	 mov	 QWORD PTR tv93[rsp], rax
  00070	eb 09		 jmp	 SHORT $LN14@Concurrent
$LN13@Concurrent:
  00072	48 c7 44 24 40
	02 00 00 00	 mov	 QWORD PTR tv93[rsp], 2
$LN14@Concurrent:
  0007b	48 8b 44 24 40	 mov	 rax, QWORD PTR tv93[rsp]
  00080	48 89 44 24 60	 mov	 QWORD PTR $T4[rsp], rax

; 178  :             {
; 179  :                 size_type _Seg_size = _Segment_size(_Index);

  00085	48 8b 44 24 60	 mov	 rax, QWORD PTR $T4[rsp]
  0008a	48 89 44 24 48	 mov	 QWORD PTR _Seg_size$3[rsp], rax

; 180  :                 for (size_type _Index2 = 0; _Index2 < _Seg_size; ++_Index2)

  0008f	48 c7 44 24 30
	00 00 00 00	 mov	 QWORD PTR _Index2$2[rsp], 0
  00098	eb 0d		 jmp	 SHORT $LN7@Concurrent
$LN5@Concurrent:
  0009a	48 8b 44 24 30	 mov	 rax, QWORD PTR _Index2$2[rsp]
  0009f	48 ff c0	 inc	 rax
  000a2	48 89 44 24 30	 mov	 QWORD PTR _Index2$2[rsp], rax
$LN7@Concurrent:
  000a7	48 8b 44 24 48	 mov	 rax, QWORD PTR _Seg_size$3[rsp]
  000ac	48 39 44 24 30	 cmp	 QWORD PTR _Index2$2[rsp], rax
  000b1	73 02		 jae	 SHORT $LN6@Concurrent

; 181  :                 {
; 182  :                     _Alfi_traits::destroy(_M_allocator, &_M_buckets[_Index][_Index2]);
; 183  :                 }

  000b3	eb e5		 jmp	 SHORT $LN5@Concurrent
$LN6@Concurrent:

; 184  :                 _M_allocator.deallocate(_M_buckets[_Index], _Seg_size);

  000b5	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000bd	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Index$1[rsp]
  000c2	48 8b 44 c8 08	 mov	 rax, QWORD PTR [rax+rcx*8+8]
  000c7	48 89 44 24 68	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  000cc	48 8b 44 24 48	 mov	 rax, QWORD PTR _Seg_size$3[rsp]
  000d1	48 c1 e0 03	 shl	 rax, 3
  000d5	48 89 44 24 38	 mov	 QWORD PTR _Bytes$[rsp], rax
  000da	48 8b 44 24 68	 mov	 rax, QWORD PTR _Ptr$[rsp]
  000df	48 89 44 24 50	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  000e4	48 81 7c 24 38
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  000ed	72 10		 jb	 SHORT $LN23@Concurrent

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  000ef	48 8d 54 24 38	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  000f4	48 8d 4c 24 50	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  000f9	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  000fe	90		 npad	 1
$LN23@Concurrent:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  000ff	48 8b 54 24 38	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  00104	48 8b 4c 24 50	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  00109	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  0010e	90		 npad	 1
$LN8@Concurrent:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h

; 186  :         }

  0010f	e9 03 ff ff ff	 jmp	 $LN2@Concurrent
$LN3@Concurrent:

; 187  :     }

  00114	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0011c	48 05 08 02 00
	00		 add	 rax, 520		; 00000208H
  00122	48 89 44 24 28	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_split_ordered_list.h

; 342  :         clear();

  00127	48 8b 4c 24 28	 mov	 rcx, QWORD PTR this$[rsp]
  0012c	e8 00 00 00 00	 call	 ?clear@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAAXXZ ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > >::clear

; 343  : 
; 344  :         // Remove the head element which is not cleared by clear()
; 345  :         _Nodeptr _Pnode = this->_Myhead;

  00131	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00136	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00139	48 89 44 24 70	 mov	 QWORD PTR _Pnode$5[rsp], rax

; 346  :         this->_Myhead = nullptr;

  0013e	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00143	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0

; 347  : 
; 348  :         _ASSERT_EXPR(_Pnode != nullptr && _Pnode->_Next == nullptr, L"Invalid head list node");
; 349  : 
; 350  :         _Erase(_Pnode);

  0014a	48 8b 54 24 70	 mov	 rdx, QWORD PTR _Pnode$5[rsp]
  0014f	48 8b 4c 24 28	 mov	 rcx, QWORD PTR this$[rsp]
  00154	e8 00 00 00 00	 call	 ?_Erase@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAAXPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@23@@Z ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > >::_Erase
  00159	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h

; 187  :     }

  0015a	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  00161	c3		 ret	 0
??1?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_JV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAA@XZ ENDP ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,__int64 volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > >,0> >::~_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,__int64 volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > >,0> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_split_ordered_list.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_split_ordered_list.h
;	COMDAT ?_Erase@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAAXPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@23@@Z
_TEXT	SEGMENT
$T1 = 32
tv86 = 36
_Bytes$ = 40
_Ptr$ = 48
this$ = 80
_Delete_node$ = 88
?_Erase@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAAXPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@23@@Z PROC ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > >::_Erase, COMDAT

; 545  :     {

$LN124:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 201  :             return (_M_order_key & 0x1) == 0;

  0000e	48 8b 44 24 58	 mov	 rax, QWORD PTR _Delete_node$[rsp]
  00013	48 8b 40 30	 mov	 rax, QWORD PTR [rax+48]
  00017	48 83 e0 01	 and	 rax, 1
  0001b	48 85 c0	 test	 rax, rax
  0001e	75 0a		 jne	 SHORT $LN6@Erase
  00020	c7 44 24 24 01
	00 00 00	 mov	 DWORD PTR tv86[rsp], 1
  00028	eb 08		 jmp	 SHORT $LN7@Erase
$LN6@Erase:
  0002a	c7 44 24 24 00
	00 00 00	 mov	 DWORD PTR tv86[rsp], 0
$LN7@Erase:
  00032	0f b6 44 24 24	 movzx	 eax, BYTE PTR tv86[rsp]
  00037	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al

; 546  :         if (!_Delete_node->_Is_dummy())

  0003b	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  00040	0f b6 c0	 movzx	 eax, al
  00043	85 c0		 test	 eax, eax
  00045	75 2b		 jne	 SHORT $LN2@Erase
  00047	48 8b 44 24 58	 mov	 rax, QWORD PTR _Delete_node$[rsp]
  0004c	48 83 c0 08	 add	 rax, 8
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1383 :         _Tidy_deallocate();

  00050	48 8b c8	 mov	 rcx, rax
  00053	e8 00 00 00 00	 call	 ?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Tidy_deallocate
  00058	90		 npad	 1
  00059	33 c0		 xor	 eax, eax
  0005b	83 e0 01	 and	 eax, 1
  0005e	85 c0		 test	 eax, eax
  00060	74 10		 je	 SHORT $LN2@Erase
  00062	ba 38 00 00 00	 mov	 edx, 56			; 00000038H
  00067	48 8b 4c 24 58	 mov	 rcx, QWORD PTR _Delete_node$[rsp]
  0006c	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00071	90		 npad	 1
$LN2@Erase:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  00072	b8 01 00 00 00	 mov	 eax, 1
  00077	48 6b c0 38	 imul	 rax, rax, 56		; 00000038H
  0007b	48 89 44 24 28	 mov	 QWORD PTR _Bytes$[rsp], rax
  00080	48 8b 44 24 58	 mov	 rax, QWORD PTR _Delete_node$[rsp]
  00085	48 89 44 24 30	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  0008a	48 81 7c 24 28
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  00093	72 10		 jb	 SHORT $LN115@Erase

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  00095	48 8d 54 24 28	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  0009a	48 8d 4c 24 30	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  0009f	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  000a4	90		 npad	 1
$LN115@Erase:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  000a5	48 8b 54 24 28	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  000aa	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  000af	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  000b4	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_split_ordered_list.h

; 552  :     }

  000b5	48 83 c4 48	 add	 rsp, 72			; 00000048H
  000b9	c3		 ret	 0
?_Erase@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAAXPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@23@@Z ENDP ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > >::_Erase
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_split_ordered_list.h
;	COMDAT ?clear@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAAXXZ
_TEXT	SEGMENT
_Pnode$ = 32
_Pnext$ = 40
this$ = 64
?clear@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAAXXZ PROC ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > >::clear, COMDAT

; 361  :     {

$LN124:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 362  : #if _ITERATOR_DEBUG_LEVEL == 2
; 363  :         _Orphan_ptr(*this, 0);
; 364  : #endif  /* _ITERATOR_DEBUG_LEVEL == 2 */
; 365  : 
; 366  :         _Nodeptr _Pnext;
; 367  :         _Nodeptr _Pnode = this->_Myhead;

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00011	48 89 44 24 20	 mov	 QWORD PTR _Pnode$[rsp], rax

; 368  : 
; 369  :         _ASSERT_EXPR(this->_Myhead != nullptr, L"Invalid head list node");
; 370  :         _Pnext = _Pnode->_Next;

  00016	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0001b	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0001e	48 89 44 24 28	 mov	 QWORD PTR _Pnext$[rsp], rax

; 371  :         _Pnode->_Next = nullptr;

  00023	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00028	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0

; 372  :         _Pnode = _Pnext;

  0002f	48 8b 44 24 28	 mov	 rax, QWORD PTR _Pnext$[rsp]
  00034	48 89 44 24 20	 mov	 QWORD PTR _Pnode$[rsp], rax
$LN2@clear:

; 373  : 
; 374  :         while (_Pnode != nullptr)

  00039	48 83 7c 24 20
	00		 cmp	 QWORD PTR _Pnode$[rsp], 0
  0003f	74 28		 je	 SHORT $LN3@clear

; 375  :         {
; 376  :             _Pnext = _Pnode->_Next;

  00041	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00046	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00049	48 89 44 24 28	 mov	 QWORD PTR _Pnext$[rsp], rax

; 377  :             _Erase(_Pnode);

  0004e	48 8b 54 24 20	 mov	 rdx, QWORD PTR _Pnode$[rsp]
  00053	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00058	e8 00 00 00 00	 call	 ?_Erase@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAAXPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@23@@Z ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > >::_Erase

; 378  :             _Pnode = _Pnext;

  0005d	48 8b 44 24 28	 mov	 rax, QWORD PTR _Pnext$[rsp]
  00062	48 89 44 24 20	 mov	 QWORD PTR _Pnode$[rsp], rax

; 379  :         }

  00067	eb d0		 jmp	 SHORT $LN2@clear
$LN3@clear:

; 380  : 
; 381  :         _M_element_count = 0;

  00069	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0006e	c7 40 10 00 00
	00 00		 mov	 DWORD PTR [rax+16], 0

; 382  :     }

  00075	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00079	c3		 ret	 0
?clear@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAAXXZ ENDP ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > >::clear
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_split_ordered_list.h
;	COMDAT ??1?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAA@XZ
_TEXT	SEGMENT
_Pnode$ = 32
this$ = 64
??1?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAA@XZ PROC ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > >::~_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > >, COMDAT

; 340  :     {

$LN251:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 341  :         // Clear the list
; 342  :         clear();

  00009	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0000e	e8 00 00 00 00	 call	 ?clear@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAAXXZ ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > >::clear

; 343  : 
; 344  :         // Remove the head element which is not cleared by clear()
; 345  :         _Nodeptr _Pnode = this->_Myhead;

  00013	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00018	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0001b	48 89 44 24 20	 mov	 QWORD PTR _Pnode$[rsp], rax

; 346  :         this->_Myhead = nullptr;

  00020	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00025	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0

; 347  : 
; 348  :         _ASSERT_EXPR(_Pnode != nullptr && _Pnode->_Next == nullptr, L"Invalid head list node");
; 349  : 
; 350  :         _Erase(_Pnode);

  0002c	48 8b 54 24 20	 mov	 rdx, QWORD PTR _Pnode$[rsp]
  00031	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00036	e8 00 00 00 00	 call	 ?_Erase@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAAXPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@23@@Z ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > >::_Erase
  0003b	90		 npad	 1

; 351  :     }

  0003c	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00040	c3		 ret	 0
??1?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAA@XZ ENDP ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > >::~_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_split_ordered_list.h
;	COMDAT ??0?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAA@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@std@@@Z
_TEXT	SEGMENT
_Allocator$ = 32
_Pnode$1 = 40
$T2 = 48
this$ = 80
_Allocator$ = 88
??0?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAA@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@std@@@Z PROC ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > >::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > >, COMDAT

; 336  :     {

$LN29:
  00000	88 54 24 10	 mov	 BYTE PTR [rsp+16], dl
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 266  :     _Split_order_list_value(_Allocator_type _Allocator = _Allocator_type()) : _Mybase(_Allocator)

  0000d	0f b6 44 24 58	 movzx	 eax, BYTE PTR _Allocator$[rsp]
  00012	88 44 24 20	 mov	 BYTE PTR _Allocator$[rsp], al

; 301  :         _Nodeptr _Pnode = this->_M_node_allocator.allocate(1);

  00016	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0001b	48 83 c0 08	 add	 rax, 8
  0001f	ba 01 00 00 00	 mov	 edx, 1
  00024	48 8b c8	 mov	 rcx, rax
  00027	e8 00 00 00 00	 call	 ?allocate@?$allocator@U_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@QEAAPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@_K@Z ; std::allocator<Concurrency::details::_Split_order_list_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > >::_Node>::allocate
  0002c	48 89 44 24 28	 mov	 QWORD PTR _Pnode$1[rsp], rax

; 169  :             _M_order_key = _Order_key;

  00031	48 8b 44 24 28	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  00036	48 c7 40 30 00
	00 00 00	 mov	 QWORD PTR [rax+48], 0

; 170  :             _Next = nullptr;

  0003e	48 8b 44 24 28	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  00043	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0

; 304  :         return (_Pnode);

  0004a	48 8b 44 24 28	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  0004f	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax

; 270  :         this->_Myhead = _Buynode(0);

  00054	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
  00059	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  0005e	48 89 01	 mov	 QWORD PTR [rcx], rax

; 305  :     }
; 306  : };
; 307  : 
; 308  : // Forward list in which elements are sorted in a split-order
; 309  : template <typename _Element_type, typename _Element_allocator_type = ::std::allocator<_Element_type>>
; 310  : class _Split_ordered_list : _Split_order_list_value<_Element_type, _Element_allocator_type>
; 311  : {
; 312  : public:
; 313  :     typedef _Split_ordered_list<_Element_type, _Element_allocator_type> _Mytype;
; 314  :     typedef _Split_order_list_value<_Element_type, _Element_allocator_type> _Mybase;
; 315  :     using typename _Mybase::_Allocator_type;
; 316  :     using typename _Mybase::_Al_traits;
; 317  :     using typename _Mybase::_Nodeptr;
; 318  :     using typename _Mybase::_Alnode_traits;
; 319  : 
; 320  :     typedef _Allocator_type allocator_type;
; 321  :     using typename _Mybase::size_type;
; 322  :     using typename _Mybase::difference_type;
; 323  :     using typename _Mybase::pointer;
; 324  :     using typename _Mybase::const_pointer;
; 325  :     using typename _Mybase::reference;
; 326  :     using typename _Mybase::const_reference;
; 327  :     using typename _Mybase::value_type;
; 328  : 
; 329  :     typedef _Solist_const_iterator<_Mybase> const_iterator;
; 330  :     typedef _Solist_iterator<_Mybase> iterator;
; 331  :     typedef ::std::_Flist_const_iterator<_Mybase> _Full_const_iterator;
; 332  :     typedef ::std::_Flist_iterator<_Mybase> _Full_iterator;
; 333  :     typedef ::std::pair<iterator, bool> _Pairib;
; 334  :     using _Mybase::_Buynode;
; 335  :     _Split_ordered_list(allocator_type _Allocator = allocator_type()) : _Mybase(_Allocator), _M_element_count(0)

  00061	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00066	c7 40 10 00 00
	00 00		 mov	 DWORD PTR [rax+16], 0

; 337  :     }

  0006d	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00072	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00076	c3		 ret	 0
??0?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@QEAA@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@std@@@Z ENDP ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > >::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?allocate@?$allocator@U_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@QEAAPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@_K@Z
_TEXT	SEGMENT
_Overflow_is_possible$1 = 32
_Bytes$ = 40
$T2 = 48
$T3 = 56
$T4 = 64
_Max_possible$5 = 72
this$ = 96
_Count$ = 104
?allocate@?$allocator@U_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@QEAAPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@_K@Z PROC ; std::allocator<Concurrency::details::_Split_order_list_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > >::_Node>::allocate, COMDAT

; 988  :     _NODISCARD_RAW_PTR_ALLOC _CONSTEXPR20 __declspec(allocator) _Ty* allocate(_CRT_GUARDOVERFLOW const size_t _Count) {

$LN13:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 113  :     constexpr bool _Overflow_is_possible = _Ty_size > 1;

  0000e	c6 44 24 20 01	 mov	 BYTE PTR _Overflow_is_possible$1[rsp], 1

; 114  : 
; 115  :     if constexpr (_Overflow_is_possible) {
; 116  :         constexpr size_t _Max_possible = static_cast<size_t>(-1) / _Ty_size;

  00013	48 b8 92 24 49
	92 24 49 92 04	 mov	 rax, 329406144173384850	; 0492492492492492H
  0001d	48 89 44 24 48	 mov	 QWORD PTR _Max_possible$5[rsp], rax

; 117  :         if (_Count > _Max_possible) {

  00022	48 b8 92 24 49
	92 24 49 92 04	 mov	 rax, 329406144173384850	; 0492492492492492H
  0002c	48 39 44 24 68	 cmp	 QWORD PTR _Count$[rsp], rax
  00031	76 06		 jbe	 SHORT $LN4@allocate

; 118  :             _Throw_bad_array_new_length(); // multiply overflow

  00033	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00038	90		 npad	 1
$LN4@allocate:

; 119  :         }
; 120  :     }
; 121  : 
; 122  :     return _Count * _Ty_size;

  00039	48 6b 44 24 68
	38		 imul	 rax, QWORD PTR _Count$[rsp], 56 ; 00000038H
  0003f	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00044	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
  00049	48 89 44 24 28	 mov	 QWORD PTR _Bytes$[rsp], rax

; 227  :     if (_Bytes == 0) {

  0004e	48 83 7c 24 28
	00		 cmp	 QWORD PTR _Bytes$[rsp], 0
  00054	75 0b		 jne	 SHORT $LN8@allocate

; 228  :         return nullptr;

  00056	48 c7 44 24 30
	00 00 00 00	 mov	 QWORD PTR $T2[rsp], 0
  0005f	eb 35		 jmp	 SHORT $LN7@allocate
$LN8@allocate:

; 229  :     }
; 230  : 
; 231  : #if _HAS_CXX20 // TRANSITION, GH-1532
; 232  :     if (_STD is_constant_evaluated()) {
; 233  :         return _Traits::_Allocate(_Bytes);
; 234  :     }
; 235  : #endif // _HAS_CXX20
; 236  : 
; 237  : #ifdef __cpp_aligned_new
; 238  :     if constexpr (_Align > __STDCPP_DEFAULT_NEW_ALIGNMENT__) {
; 239  :         size_t _Passed_align = _Align;
; 240  : #if defined(_M_IX86) || defined(_M_X64)
; 241  :         if (_Bytes >= _Big_allocation_threshold) {
; 242  :             // boost the alignment of big allocations to help autovectorization
; 243  :             _Passed_align = (_STD max)(_Align, _Big_allocation_alignment);
; 244  :         }
; 245  : #endif // defined(_M_IX86) || defined(_M_X64)
; 246  :         return _Traits::_Allocate_aligned(_Bytes, _Passed_align);
; 247  :     } else
; 248  : #endif // defined(__cpp_aligned_new)
; 249  :     {
; 250  : #if defined(_M_IX86) || defined(_M_X64)
; 251  :         if (_Bytes >= _Big_allocation_threshold) {

  00061	48 81 7c 24 28
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  0006a	72 11		 jb	 SHORT $LN9@allocate

; 252  :             // boost the alignment of big allocations to help autovectorization
; 253  :             return _Allocate_manually_vector_aligned<_Traits>(_Bytes);

  0006c	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00071	e8 00 00 00 00	 call	 ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
  00076	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
  0007b	eb 19		 jmp	 SHORT $LN7@allocate
$LN9@allocate:

; 136  :         return ::operator new(_Bytes);

  0007d	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00082	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00087	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax

; 256  :         return _Traits::_Allocate(_Bytes);

  0008c	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  00091	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
$LN7@allocate:

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00096	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
$LN6@allocate:

; 991  :     }

  0009b	48 83 c4 58	 add	 rsp, 88			; 00000058H
  0009f	c3		 ret	 0
?allocate@?$allocator@U_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@@std@@QEAAPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CC_J@std@@@2@@details@Concurrency@@_K@Z ENDP ; std::allocator<Concurrency::details::_Split_order_list_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,__int64 volatile > > >::_Node>::allocate
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??1?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@XZ PROC ; Concurrency::concurrent_unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >::~concurrent_unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >, COMDAT
$LN230:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00009	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0000e	e8 00 00 00 00	 call	 ??1?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAA@XZ ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > >,0> >::~_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > >,0> >
  00013	90		 npad	 1
  00014	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00018	c3		 ret	 0
??1?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@XZ ENDP ; Concurrency::concurrent_unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >::~concurrent_unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\concurrent_unordered_map.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\concurrent_unordered_map.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\concurrent_unordered_map.h
;	COMDAT ??0?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@_KAEBU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@AEBU?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@3@AEBV?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@3@@Z
_TEXT	SEGMENT
$T1 = 32
$T2 = 40
this$ = 64
_Number_of_buckets$ = 72
_Hasharg$ = 80
_Keyeqarg$ = 88
_Allocator$ = 96
??0?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@_KAEBU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@AEBU?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@3@AEBV?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@3@@Z PROC ; Concurrency::concurrent_unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >::concurrent_unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >, COMDAT

; 253  :     {

$LN150:
  00000	4c 89 4c 24 20	 mov	 QWORD PTR [rsp+32], r9
  00005	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	48 83 ec 38	 sub	 rsp, 56			; 00000038H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h

; 42   :     }

  00018	48 8d 44 24 20	 lea	 rax, QWORD PTR $T1[rsp]
  0001d	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\concurrent_unordered_map.h

; 56   :     _Concurrent_unordered_map_traits(const _Key_compare& _Traits) : _M_comparator(_Traits)

  00022	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
  00027	0f b7 00	 movzx	 eax, WORD PTR [rax]
  0002a	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0002f	66 89 01	 mov	 WORD PTR [rcx], ax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h

; 128  :         : _Traits(_Parg), _M_split_ordered_list(_Allocator), _M_allocator(_Allocator), _M_number_of_buckets(_Number_of_buckets), _M_maximum_bucket_size((float) _Initial_bucket_load)

  00032	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00037	48 05 08 02 00
	00		 add	 rax, 520		; 00000208H
  0003d	48 8b 4c 24 60	 mov	 rcx, QWORD PTR _Allocator$[rsp]
  00042	0f b6 11	 movzx	 edx, BYTE PTR [rcx]
  00045	48 8b c8	 mov	 rcx, rax
  00048	e8 00 00 00 00	 call	 ??0?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAA@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@std@@@Z ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >
  0004d	90		 npad	 1
  0004e	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _Number_of_buckets$[rsp]
  00058	48 89 88 28 02
	00 00		 mov	 QWORD PTR [rax+552], rcx
  0005f	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00064	f3 0f 10 05 00
	00 00 00	 movss	 xmm0, DWORD PTR __real@40800000
  0006c	f3 0f 11 80 30
	02 00 00	 movss	 DWORD PTR [rax+560], xmm0

; 129  :     {
; 130  :         _Init();

  00074	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00079	e8 00 00 00 00	 call	 ?_Init@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAXXZ ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > >,0> >::_Init
  0007e	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\concurrent_unordered_map.h

; 254  :         this->rehash(_Number_of_buckets);

  0007f	48 8b 54 24 48	 mov	 rdx, QWORD PTR _Number_of_buckets$[rsp]
  00084	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00089	e8 00 00 00 00	 call	 ?rehash@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAX_K@Z ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > >,0> >::rehash
  0008e	90		 npad	 1

; 255  :     }

  0008f	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00094	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00098	c3		 ret	 0
??0?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@_KAEBU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@AEBU?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@3@AEBV?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@3@@Z ENDP ; Concurrency::concurrent_unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >::concurrent_unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 40
this$ = 64
_Number_of_buckets$ = 72
_Hasharg$ = 80
_Keyeqarg$ = 88
_Allocator$ = 96
?dtor$1@?0???0?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@_KAEBU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@AEBU?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@3@AEBV?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@3@@Z@4HA PROC ; `Concurrency::concurrent_unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >::concurrent_unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >'::`1'::dtor$1
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 40	 mov	 rcx, QWORD PTR this$[rbp]
  0000d	48 81 c1 08 02
	00 00		 add	 rcx, 520		; 00000208H
  00014	e8 00 00 00 00	 call	 ??1?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAA@XZ ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >::~_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >
  00019	48 83 c4 20	 add	 rsp, 32			; 00000020H
  0001d	5d		 pop	 rbp
  0001e	c3		 ret	 0
?dtor$1@?0???0?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@_KAEBU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@AEBU?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@3@AEBV?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@3@@Z@4HA ENDP ; `Concurrency::concurrent_unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >::concurrent_unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >'::`1'::dtor$1
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 40
this$ = 64
_Number_of_buckets$ = 72
_Hasharg$ = 80
_Keyeqarg$ = 88
_Allocator$ = 96
?dtor$0@?0???0?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@_KAEBU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@AEBU?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@3@AEBV?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@3@@Z@4HA PROC ; `Concurrency::concurrent_unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >::concurrent_unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 40	 mov	 rcx, QWORD PTR this$[rbp]
  0000d	e8 00 00 00 00	 call	 ??1?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAA@XZ ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > >,0> >::~_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > >,0> >
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$0@?0???0?$concurrent_unordered_map@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@Concurrency@@QEAA@_KAEBU?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@std@@AEBU?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@3@AEBV?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@3@@Z@4HA ENDP ; `Concurrency::concurrent_unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >::concurrent_unordered_map<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?allocate@?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@QEAAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@2@_K@Z
_TEXT	SEGMENT
_Overflow_is_possible$1 = 32
_Bytes$ = 40
$T2 = 48
$T3 = 56
$T4 = 64
_Max_possible$5 = 72
this$ = 96
_Count$ = 104
?allocate@?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@QEAAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@2@_K@Z PROC ; std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > > > >::allocate, COMDAT

; 988  :     _NODISCARD_RAW_PTR_ALLOC _CONSTEXPR20 __declspec(allocator) _Ty* allocate(_CRT_GUARDOVERFLOW const size_t _Count) {

$LN13:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 113  :     constexpr bool _Overflow_is_possible = _Ty_size > 1;

  0000e	c6 44 24 20 01	 mov	 BYTE PTR _Overflow_is_possible$1[rsp], 1

; 114  : 
; 115  :     if constexpr (_Overflow_is_possible) {
; 116  :         constexpr size_t _Max_possible = static_cast<size_t>(-1) / _Ty_size;

  00013	48 b8 ff ff ff
	ff ff ff ff 1f	 mov	 rax, 2305843009213693951 ; 1fffffffffffffffH
  0001d	48 89 44 24 48	 mov	 QWORD PTR _Max_possible$5[rsp], rax

; 117  :         if (_Count > _Max_possible) {

  00022	48 b8 ff ff ff
	ff ff ff ff 1f	 mov	 rax, 2305843009213693951 ; 1fffffffffffffffH
  0002c	48 39 44 24 68	 cmp	 QWORD PTR _Count$[rsp], rax
  00031	76 06		 jbe	 SHORT $LN4@allocate

; 118  :             _Throw_bad_array_new_length(); // multiply overflow

  00033	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00038	90		 npad	 1
$LN4@allocate:

; 119  :         }
; 120  :     }
; 121  : 
; 122  :     return _Count * _Ty_size;

  00039	48 8b 44 24 68	 mov	 rax, QWORD PTR _Count$[rsp]
  0003e	48 c1 e0 03	 shl	 rax, 3
  00042	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00047	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
  0004c	48 89 44 24 28	 mov	 QWORD PTR _Bytes$[rsp], rax

; 227  :     if (_Bytes == 0) {

  00051	48 83 7c 24 28
	00		 cmp	 QWORD PTR _Bytes$[rsp], 0
  00057	75 0b		 jne	 SHORT $LN8@allocate

; 228  :         return nullptr;

  00059	48 c7 44 24 30
	00 00 00 00	 mov	 QWORD PTR $T2[rsp], 0
  00062	eb 35		 jmp	 SHORT $LN7@allocate
$LN8@allocate:

; 229  :     }
; 230  : 
; 231  : #if _HAS_CXX20 // TRANSITION, GH-1532
; 232  :     if (_STD is_constant_evaluated()) {
; 233  :         return _Traits::_Allocate(_Bytes);
; 234  :     }
; 235  : #endif // _HAS_CXX20
; 236  : 
; 237  : #ifdef __cpp_aligned_new
; 238  :     if constexpr (_Align > __STDCPP_DEFAULT_NEW_ALIGNMENT__) {
; 239  :         size_t _Passed_align = _Align;
; 240  : #if defined(_M_IX86) || defined(_M_X64)
; 241  :         if (_Bytes >= _Big_allocation_threshold) {
; 242  :             // boost the alignment of big allocations to help autovectorization
; 243  :             _Passed_align = (_STD max)(_Align, _Big_allocation_alignment);
; 244  :         }
; 245  : #endif // defined(_M_IX86) || defined(_M_X64)
; 246  :         return _Traits::_Allocate_aligned(_Bytes, _Passed_align);
; 247  :     } else
; 248  : #endif // defined(__cpp_aligned_new)
; 249  :     {
; 250  : #if defined(_M_IX86) || defined(_M_X64)
; 251  :         if (_Bytes >= _Big_allocation_threshold) {

  00064	48 81 7c 24 28
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  0006d	72 11		 jb	 SHORT $LN9@allocate

; 252  :             // boost the alignment of big allocations to help autovectorization
; 253  :             return _Allocate_manually_vector_aligned<_Traits>(_Bytes);

  0006f	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00074	e8 00 00 00 00	 call	 ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
  00079	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
  0007e	eb 19		 jmp	 SHORT $LN7@allocate
$LN9@allocate:

; 136  :         return ::operator new(_Bytes);

  00080	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00085	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  0008a	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax

; 256  :         return _Traits::_Allocate(_Bytes);

  0008f	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  00094	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
$LN7@allocate:

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00099	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
$LN6@allocate:

; 991  :     }

  0009e	48 83 c4 58	 add	 rsp, 88			; 00000058H
  000a2	c3		 ret	 0
?allocate@?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@QEAAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@2@_K@Z ENDP ; std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > > > >::allocate
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h
;	COMDAT ?_Set_bucket@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAX_KV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@Z
_TEXT	SEGMENT
$T1 = 32
_Segment$ = 40
_Index$2 = 48
_Seg_size$3 = 56
_New_segment$4 = 64
_Bytes$ = 72
tv142 = 80
tv95 = 88
_Ptr$ = 96
$T5 = 104
tv157 = 112
$T6 = 120
tv168 = 128
$T7 = 136
this$ = 160
_Bucket$ = 168
_Dummy_head$ = 176
?_Set_bucket@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAX_KV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@Z PROC ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > >,0> >::_Set_bucket, COMDAT

; 1248 :     {

$LN91:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 81 ec 98 00
	00 00		 sub	 rsp, 152		; 00000098H

; 73   :     unsigned long _Index = 0;

  00016	c7 44 24 30 00
	00 00 00	 mov	 DWORD PTR _Index$2[rsp], 0

; 191  :         return size_type( _Get_msb( _Index|1 ) );

  0001e	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR _Bucket$[rsp]
  00026	48 83 c8 01	 or	 rax, 1

; 78   :     _BitScanReverse64(&_Index, _Mask);

  0002a	48 0f bd c0	 bsr	 rax, rax
  0002e	89 44 24 30	 mov	 DWORD PTR _Index$2[rsp], eax

; 79   : #endif  /* (defined (_M_IX86) || defined (_M_ARM)) */
; 80   : 
; 81   :     return (unsigned char) _Index;

  00032	0f b6 44 24 30	 movzx	 eax, BYTE PTR _Index$2[rsp]
  00037	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al

; 191  :         return size_type( _Get_msb( _Index|1 ) );

  0003b	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  00040	0f b6 c0	 movzx	 eax, al
  00043	48 89 44 24 68	 mov	 QWORD PTR $T5[rsp], rax

; 1249 :         size_type _Segment = _Segment_index_of(_Bucket);

  00048	48 8b 44 24 68	 mov	 rax, QWORD PTR $T5[rsp]
  0004d	48 89 44 24 28	 mov	 QWORD PTR _Segment$[rsp], rax

; 196  :         return (size_type(1)<<_K & ~size_type(1));

  00052	48 8b 44 24 28	 mov	 rax, QWORD PTR _Segment$[rsp]
  00057	b9 01 00 00 00	 mov	 ecx, 1
  0005c	48 89 4c 24 70	 mov	 QWORD PTR tv157[rsp], rcx
  00061	0f b6 c8	 movzx	 ecx, al
  00064	48 8b 44 24 70	 mov	 rax, QWORD PTR tv157[rsp]
  00069	48 d3 e0	 shl	 rax, cl
  0006c	48 83 e0 fe	 and	 rax, -2
  00070	48 89 44 24 78	 mov	 QWORD PTR $T6[rsp], rax

; 1250 :         _Bucket -= _Segment_base(_Segment);

  00075	48 8b 44 24 78	 mov	 rax, QWORD PTR $T6[rsp]
  0007a	48 8b 8c 24 a8
	00 00 00	 mov	 rcx, QWORD PTR _Bucket$[rsp]
  00082	48 2b c8	 sub	 rcx, rax
  00085	48 8b c1	 mov	 rax, rcx
  00088	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR _Bucket$[rsp], rax

; 1251 : 
; 1252 :         if (_M_buckets[_Segment] == nullptr)

  00090	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00098	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Segment$[rsp]
  0009d	48 83 7c c8 08
	00		 cmp	 QWORD PTR [rax+rcx*8+8], 0
  000a3	0f 85 08 01 00
	00		 jne	 $LN2@Set_bucket

; 201  :         return _K ? size_type(1)<<_K : 2;

  000a9	48 83 7c 24 28
	00		 cmp	 QWORD PTR _Segment$[rsp], 0
  000af	74 27		 je	 SHORT $LN13@Set_bucket
  000b1	48 8b 44 24 28	 mov	 rax, QWORD PTR _Segment$[rsp]
  000b6	b9 01 00 00 00	 mov	 ecx, 1
  000bb	48 89 8c 24 80
	00 00 00	 mov	 QWORD PTR tv168[rsp], rcx
  000c3	0f b6 c8	 movzx	 ecx, al
  000c6	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR tv168[rsp]
  000ce	48 d3 e0	 shl	 rax, cl
  000d1	48 89 44 24 50	 mov	 QWORD PTR tv142[rsp], rax
  000d6	eb 09		 jmp	 SHORT $LN14@Set_bucket
$LN13@Set_bucket:
  000d8	48 c7 44 24 50
	02 00 00 00	 mov	 QWORD PTR tv142[rsp], 2
$LN14@Set_bucket:
  000e1	48 8b 44 24 50	 mov	 rax, QWORD PTR tv142[rsp]
  000e6	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T7[rsp], rax

; 1253 :         {
; 1254 :             size_type _Seg_size = _Segment_size(_Segment);

  000ee	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T7[rsp]
  000f6	48 89 44 24 38	 mov	 QWORD PTR _Seg_size$3[rsp], rax

; 1255 :             _Full_iterator * _New_segment = _M_allocator.allocate(_Seg_size);

  000fb	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00103	48 05 20 02 00
	00		 add	 rax, 544		; 00000220H
  00109	48 8b 54 24 38	 mov	 rdx, QWORD PTR _Seg_size$3[rsp]
  0010e	48 8b c8	 mov	 rcx, rax
  00111	e8 00 00 00 00	 call	 ?allocate@?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@QEAAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@2@_K@Z ; std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > > > >::allocate
  00116	48 89 44 24 40	 mov	 QWORD PTR _New_segment$4[rsp], rax

; 1256 :             ::std::_Uninitialized_value_construct_n(_New_segment, _Seg_size, _M_allocator);

  0011b	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00123	48 05 20 02 00
	00		 add	 rax, 544		; 00000220H
  00129	4c 8b c0	 mov	 r8, rax
  0012c	48 8b 54 24 38	 mov	 rdx, QWORD PTR _Seg_size$3[rsp]
  00131	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _New_segment$4[rsp]
  00136	e8 00 00 00 00	 call	 ??$_Uninitialized_value_construct_n@V?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@std@@@std@@YAPEAV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@0@PEAV10@_KAEAV?$allocator@V?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@0@@Z ; std::_Uninitialized_value_construct_n<std::allocator<std::_Flist_iterator<Concurrency::details::_Split_order_list_value<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > > > > >
  0013b	90		 npad	 1

; 1257 :             if (_InterlockedCompareExchangePointer((void * volatile *) &_M_buckets[_Segment], _New_segment, nullptr) != nullptr)

  0013c	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00144	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Segment$[rsp]
  00149	48 8d 44 c8 08	 lea	 rax, QWORD PTR [rax+rcx*8+8]
  0014e	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _New_segment$4[rsp]
  00153	48 89 44 24 58	 mov	 QWORD PTR tv95[rsp], rax
  00158	33 c0		 xor	 eax, eax
  0015a	48 8b 54 24 58	 mov	 rdx, QWORD PTR tv95[rsp]
  0015f	48 8b 54 24 58	 mov	 rdx, QWORD PTR tv95[rsp]
  00164	f0 48 0f b1 0a	 lock cmpxchg QWORD PTR [rdx], rcx
  00169	48 85 c0	 test	 rax, rax
  0016c	74 43		 je	 SHORT $LN2@Set_bucket
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  0016e	48 8b 44 24 38	 mov	 rax, QWORD PTR _Seg_size$3[rsp]
  00173	48 c1 e0 03	 shl	 rax, 3
  00177	48 89 44 24 48	 mov	 QWORD PTR _Bytes$[rsp], rax
  0017c	48 8b 44 24 40	 mov	 rax, QWORD PTR _New_segment$4[rsp]
  00181	48 89 44 24 60	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  00186	48 81 7c 24 48
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  0018f	72 10		 jb	 SHORT $LN82@Set_bucket

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  00191	48 8d 54 24 48	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  00196	48 8d 4c 24 60	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  0019b	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  001a0	90		 npad	 1
$LN82@Set_bucket:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  001a1	48 8b 54 24 48	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  001a6	48 8b 4c 24 60	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  001ab	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  001b0	90		 npad	 1
$LN2@Set_bucket:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h

; 1262 :         _M_buckets[_Segment][_Bucket] = _Dummy_head;

  001b1	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  001b9	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Segment$[rsp]
  001be	48 8b 44 c8 08	 mov	 rax, QWORD PTR [rax+rcx*8+8]
  001c3	48 8b 8c 24 a8
	00 00 00	 mov	 rcx, QWORD PTR _Bucket$[rsp]
  001cb	48 8b 94 24 b0
	00 00 00	 mov	 rdx, QWORD PTR _Dummy_head$[rsp]
  001d3	48 89 14 c8	 mov	 QWORD PTR [rax+rcx*8], rdx

; 1263 :     }

  001d7	48 81 c4 98 00
	00 00		 add	 rsp, 152		; 00000098H
  001de	c3		 ret	 0
?_Set_bucket@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAX_KV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@Z ENDP ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > >,0> >::_Set_bucket
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_split_ordered_list.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\forward_list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h
;	COMDAT ?_Init@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAXXZ
_TEXT	SEGMENT
__param0$ = 32
_Dummy_node$ = 40
this$ = 64
?_Init@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAXXZ PROC ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > >,0> >::_Init, COMDAT

; 942  :     {

$LN115:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 943  :         // Allocate an array of segment pointers
; 944  :         memset(_M_buckets, 0, _Pointers_per_table * sizeof(void *));

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 c0 08	 add	 rax, 8
  00012	41 b8 00 02 00
	00		 mov	 r8d, 512		; 00000200H
  00018	33 d2		 xor	 edx, edx
  0001a	48 8b c8	 mov	 rcx, rax
  0001d	e8 00 00 00 00	 call	 memset
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_split_ordered_list.h

; 465  :         return _Full_iterator(this->_Myhead, this);

  00022	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00027	48 8b 80 08 02
	00 00		 mov	 rax, QWORD PTR [rax+520]
  0002e	48 89 44 24 20	 mov	 QWORD PTR __param0$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\forward_list

; 37   :     _Flist_unchecked_const_iterator(_Nodeptr _Pnode, const _Mylist* _Plist) noexcept : _Ptr(_Pnode) {

  00033	48 8b 44 24 20	 mov	 rax, QWORD PTR __param0$[rsp]
  00038	48 89 44 24 28	 mov	 QWORD PTR _Dummy_node$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h

; 948  :         _Set_bucket(0, _Dummy_node);

  0003d	4c 8b 44 24 28	 mov	 r8, QWORD PTR _Dummy_node$[rsp]
  00042	33 d2		 xor	 edx, edx
  00044	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00049	e8 00 00 00 00	 call	 ?_Set_bucket@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAX_KV?$_Flist_iterator@V?$_Split_order_list_value@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@@Z ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > >,0> >::_Set_bucket
  0004e	90		 npad	 1

; 949  :     }

  0004f	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00053	c3		 ret	 0
?_Init@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAXXZ ENDP ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > >,0> >::_Init
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h
;	COMDAT ?rehash@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAX_K@Z
_TEXT	SEGMENT
$T1 = 32
_Index$2 = 36
tv82 = 40
_Current_buckets$ = 48
$T3 = 56
$T4 = 64
tv95 = 72
this$ = 96
_Buckets$ = 104
?rehash@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAX_K@Z PROC ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > >,0> >::rehash, COMDAT

; 847  :     {

$LN15:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 848  :         size_type _Current_buckets = _M_number_of_buckets;

  0000e	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00013	48 8b 80 28 02
	00 00		 mov	 rax, QWORD PTR [rax+552]
  0001a	48 89 44 24 30	 mov	 QWORD PTR _Current_buckets$[rsp], rax

; 849  : 
; 850  :         if (_Current_buckets > _Buckets)

  0001f	48 8b 44 24 68	 mov	 rax, QWORD PTR _Buckets$[rsp]
  00024	48 39 44 24 30	 cmp	 QWORD PTR _Current_buckets$[rsp], rax
  00029	76 07		 jbe	 SHORT $LN2@rehash

; 851  :         {
; 852  :             return;

  0002b	e9 a4 00 00 00	 jmp	 $LN1@rehash

; 853  :         }

  00030	eb 56		 jmp	 SHORT $LN3@rehash
$LN2@rehash:

; 854  :         else if (_Buckets <= 0 || _Buckets > unsafe_max_bucket_count())

  00032	48 83 7c 24 68
	00		 cmp	 QWORD PTR _Buckets$[rsp], 0
  00038	76 41		 jbe	 SHORT $LN5@rehash

; 201  :         return _K ? size_type(1)<<_K : 2;

  0003a	33 c0		 xor	 eax, eax
  0003c	48 83 f8 3f	 cmp	 rax, 63			; 0000003fH
  00040	74 10		 je	 SHORT $LN11@rehash
  00042	b8 01 00 00 00	 mov	 eax, 1
  00047	48 c1 e0 3f	 shl	 rax, 63			; 0000003fH
  0004b	48 89 44 24 28	 mov	 QWORD PTR tv82[rsp], rax
  00050	eb 09		 jmp	 SHORT $LN12@rehash
$LN11@rehash:
  00052	48 c7 44 24 28
	02 00 00 00	 mov	 QWORD PTR tv82[rsp], 2
$LN12@rehash:
  0005b	48 8b 44 24 28	 mov	 rax, QWORD PTR tv82[rsp]
  00060	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax

; 581  :         return _Segment_size(_Pointers_per_table-1);

  00065	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
  0006a	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax

; 854  :         else if (_Buckets <= 0 || _Buckets > unsafe_max_bucket_count())

  0006f	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  00074	48 39 44 24 68	 cmp	 QWORD PTR _Buckets$[rsp], rax
  00079	76 0d		 jbe	 SHORT $LN4@rehash
$LN5@rehash:

; 855  :         {
; 856  :             _STD _Xout_of_range("invalid number of buckets");

  0007b	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_0BK@KDCGEAPC@invalid?5number?5of?5buckets@
  00082	e8 00 00 00 00	 call	 ?_Xout_of_range@std@@YAXPEBD@Z ; std::_Xout_of_range
  00087	90		 npad	 1
$LN4@rehash:
$LN3@rehash:

; 73   :     unsigned long _Index = 0;

  00088	c7 44 24 24 00
	00 00 00	 mov	 DWORD PTR _Index$2[rsp], 0

; 857  :         }
; 858  :         // Round up the number of buckets to the next largest power of 2
; 859  :         _M_number_of_buckets = ((size_type) 1) << _Get_msb(_Buckets*2-1);

  00090	48 8b 44 24 68	 mov	 rax, QWORD PTR _Buckets$[rsp]
  00095	48 8d 44 00 ff	 lea	 rax, QWORD PTR [rax+rax-1]

; 78   :     _BitScanReverse64(&_Index, _Mask);

  0009a	48 0f bd c0	 bsr	 rax, rax
  0009e	89 44 24 24	 mov	 DWORD PTR _Index$2[rsp], eax

; 79   : #endif  /* (defined (_M_IX86) || defined (_M_ARM)) */
; 80   : 
; 81   :     return (unsigned char) _Index;

  000a2	0f b6 44 24 24	 movzx	 eax, BYTE PTR _Index$2[rsp]
  000a7	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al

; 857  :         }
; 858  :         // Round up the number of buckets to the next largest power of 2
; 859  :         _M_number_of_buckets = ((size_type) 1) << _Get_msb(_Buckets*2-1);

  000ab	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  000b0	0f b6 c0	 movzx	 eax, al
  000b3	b9 01 00 00 00	 mov	 ecx, 1
  000b8	48 89 4c 24 48	 mov	 QWORD PTR tv95[rsp], rcx
  000bd	0f b6 c8	 movzx	 ecx, al
  000c0	48 8b 44 24 48	 mov	 rax, QWORD PTR tv95[rsp]
  000c5	48 d3 e0	 shl	 rax, cl
  000c8	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  000cd	48 89 81 28 02
	00 00		 mov	 QWORD PTR [rcx+552], rax
$LN1@rehash:
$LN6@rehash:

; 860  :     }

  000d4	48 83 c4 58	 add	 rsp, 88			; 00000058H
  000d8	c3		 ret	 0
?rehash@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAX_K@Z ENDP ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > >,0> >::rehash
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h
;	COMDAT ?clear@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAXXZ
_TEXT	SEGMENT
_Index$1 = 32
_Index2$2 = 40
_Bytes$ = 48
tv134 = 56
_Seg_size$3 = 64
_Ptr$ = 72
tv84 = 80
$T4 = 88
_Ptr$ = 96
this$ = 128
?clear@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAXXZ PROC ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > >,0> >::clear, COMDAT

; 445  :     {

$LN209:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 78	 sub	 rsp, 120		; 00000078H

; 446  :         // Clear list
; 447  :         _M_split_ordered_list.clear();

  00009	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00011	48 05 08 02 00
	00		 add	 rax, 520		; 00000208H
  00017	48 8b c8	 mov	 rcx, rax
  0001a	e8 00 00 00 00	 call	 ?clear@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAAXXZ ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >::clear
  0001f	90		 npad	 1

; 448  : 
; 449  :         // Clear buckets
; 450  :         for (size_type _Index = 0; _Index < _Pointers_per_table; ++_Index)

  00020	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR _Index$1[rsp], 0
  00029	eb 0d		 jmp	 SHORT $LN4@clear
$LN2@clear:
  0002b	48 8b 44 24 20	 mov	 rax, QWORD PTR _Index$1[rsp]
  00030	48 ff c0	 inc	 rax
  00033	48 89 44 24 20	 mov	 QWORD PTR _Index$1[rsp], rax
$LN4@clear:
  00038	48 83 7c 24 20
	40		 cmp	 QWORD PTR _Index$1[rsp], 64 ; 00000040H
  0003e	0f 83 e4 00 00
	00		 jae	 $LN3@clear

; 451  :         {
; 452  :             if (_M_buckets[_Index] != nullptr)

  00044	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0004c	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Index$1[rsp]
  00051	48 83 7c c8 08
	00		 cmp	 QWORD PTR [rax+rcx*8+8], 0
  00057	0f 84 c6 00 00
	00		 je	 $LN8@clear

; 201  :         return _K ? size_type(1)<<_K : 2;

  0005d	48 83 7c 24 20
	00		 cmp	 QWORD PTR _Index$1[rsp], 0
  00063	74 21		 je	 SHORT $LN76@clear
  00065	48 8b 44 24 20	 mov	 rax, QWORD PTR _Index$1[rsp]
  0006a	b9 01 00 00 00	 mov	 ecx, 1
  0006f	48 89 4c 24 50	 mov	 QWORD PTR tv84[rsp], rcx
  00074	0f b6 c8	 movzx	 ecx, al
  00077	48 8b 44 24 50	 mov	 rax, QWORD PTR tv84[rsp]
  0007c	48 d3 e0	 shl	 rax, cl
  0007f	48 89 44 24 38	 mov	 QWORD PTR tv134[rsp], rax
  00084	eb 09		 jmp	 SHORT $LN77@clear
$LN76@clear:
  00086	48 c7 44 24 38
	02 00 00 00	 mov	 QWORD PTR tv134[rsp], 2
$LN77@clear:
  0008f	48 8b 44 24 38	 mov	 rax, QWORD PTR tv134[rsp]
  00094	48 89 44 24 58	 mov	 QWORD PTR $T4[rsp], rax

; 453  :             {
; 454  :                 size_type _Seg_size = _Segment_size(_Index);

  00099	48 8b 44 24 58	 mov	 rax, QWORD PTR $T4[rsp]
  0009e	48 89 44 24 40	 mov	 QWORD PTR _Seg_size$3[rsp], rax

; 455  :                 for (size_type _Index2 = 0; _Index2 < _Seg_size; ++_Index2)

  000a3	48 c7 44 24 28
	00 00 00 00	 mov	 QWORD PTR _Index2$2[rsp], 0
  000ac	eb 0d		 jmp	 SHORT $LN7@clear
$LN5@clear:
  000ae	48 8b 44 24 28	 mov	 rax, QWORD PTR _Index2$2[rsp]
  000b3	48 ff c0	 inc	 rax
  000b6	48 89 44 24 28	 mov	 QWORD PTR _Index2$2[rsp], rax
$LN7@clear:
  000bb	48 8b 44 24 40	 mov	 rax, QWORD PTR _Seg_size$3[rsp]
  000c0	48 39 44 24 28	 cmp	 QWORD PTR _Index2$2[rsp], rax
  000c5	73 02		 jae	 SHORT $LN6@clear

; 456  :                 {
; 457  :                     _Alfi_traits::destroy(_M_allocator, &_M_buckets[_Index][_Index2]);
; 458  :                 }

  000c7	eb e5		 jmp	 SHORT $LN5@clear
$LN6@clear:

; 459  :                 _M_allocator.deallocate(_M_buckets[_Index], _Seg_size);

  000c9	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000d1	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Index$1[rsp]
  000d6	48 8b 44 c8 08	 mov	 rax, QWORD PTR [rax+rcx*8+8]
  000db	48 89 44 24 60	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  000e0	48 8b 44 24 40	 mov	 rax, QWORD PTR _Seg_size$3[rsp]
  000e5	48 c1 e0 03	 shl	 rax, 3
  000e9	48 89 44 24 30	 mov	 QWORD PTR _Bytes$[rsp], rax
  000ee	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  000f3	48 89 44 24 48	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  000f8	48 81 7c 24 30
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  00101	72 10		 jb	 SHORT $LN86@clear

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  00103	48 8d 54 24 30	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  00108	48 8d 4c 24 48	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  0010d	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  00112	90		 npad	 1
$LN86@clear:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  00113	48 8b 54 24 30	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  00118	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  0011d	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00122	90		 npad	 1
$LN8@clear:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h

; 461  :         }

  00123	e9 03 ff ff ff	 jmp	 $LN2@clear
$LN3@clear:

; 462  : 
; 463  :         // memset all the buckets to zero and initialize the dummy node 0
; 464  :         _Init();

  00128	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00130	e8 00 00 00 00	 call	 ?_Init@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@AEAAXXZ ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > >,0> >::_Init
  00135	90		 npad	 1

; 465  :     }

  00136	48 83 c4 78	 add	 rsp, 120		; 00000078H
  0013a	c3		 ret	 0
?clear@?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAAXXZ ENDP ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > >,0> >::clear
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_split_ordered_list.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h
;	COMDAT ??1?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAA@XZ
_TEXT	SEGMENT
_Index$1 = 32
this$ = 40
_Index2$2 = 48
_Bytes$ = 56
tv93 = 64
_Seg_size$3 = 72
_Ptr$ = 80
tv148 = 88
$T4 = 96
_Ptr$ = 104
_Pnode$5 = 112
this$ = 144
??1?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAA@XZ PROC ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > >,0> >::~_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > >,0> >, COMDAT

; 173  :     {

$LN225:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H

; 174  :         // Delete all node segments
; 175  :         for (size_type _Index = 0; _Index < _Pointers_per_table; ++_Index)

  0000c	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR _Index$1[rsp], 0
  00015	eb 0d		 jmp	 SHORT $LN4@Concurrent
$LN2@Concurrent:
  00017	48 8b 44 24 20	 mov	 rax, QWORD PTR _Index$1[rsp]
  0001c	48 ff c0	 inc	 rax
  0001f	48 89 44 24 20	 mov	 QWORD PTR _Index$1[rsp], rax
$LN4@Concurrent:
  00024	48 83 7c 24 20
	40		 cmp	 QWORD PTR _Index$1[rsp], 64 ; 00000040H
  0002a	0f 83 e4 00 00
	00		 jae	 $LN3@Concurrent

; 176  :         {
; 177  :             if (_M_buckets[_Index] != nullptr)

  00030	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Index$1[rsp]
  0003d	48 83 7c c8 08
	00		 cmp	 QWORD PTR [rax+rcx*8+8], 0
  00043	0f 84 c6 00 00
	00		 je	 $LN8@Concurrent

; 201  :         return _K ? size_type(1)<<_K : 2;

  00049	48 83 7c 24 20
	00		 cmp	 QWORD PTR _Index$1[rsp], 0
  0004f	74 21		 je	 SHORT $LN13@Concurrent
  00051	48 8b 44 24 20	 mov	 rax, QWORD PTR _Index$1[rsp]
  00056	b9 01 00 00 00	 mov	 ecx, 1
  0005b	48 89 4c 24 58	 mov	 QWORD PTR tv148[rsp], rcx
  00060	0f b6 c8	 movzx	 ecx, al
  00063	48 8b 44 24 58	 mov	 rax, QWORD PTR tv148[rsp]
  00068	48 d3 e0	 shl	 rax, cl
  0006b	48 89 44 24 40	 mov	 QWORD PTR tv93[rsp], rax
  00070	eb 09		 jmp	 SHORT $LN14@Concurrent
$LN13@Concurrent:
  00072	48 c7 44 24 40
	02 00 00 00	 mov	 QWORD PTR tv93[rsp], 2
$LN14@Concurrent:
  0007b	48 8b 44 24 40	 mov	 rax, QWORD PTR tv93[rsp]
  00080	48 89 44 24 60	 mov	 QWORD PTR $T4[rsp], rax

; 178  :             {
; 179  :                 size_type _Seg_size = _Segment_size(_Index);

  00085	48 8b 44 24 60	 mov	 rax, QWORD PTR $T4[rsp]
  0008a	48 89 44 24 48	 mov	 QWORD PTR _Seg_size$3[rsp], rax

; 180  :                 for (size_type _Index2 = 0; _Index2 < _Seg_size; ++_Index2)

  0008f	48 c7 44 24 30
	00 00 00 00	 mov	 QWORD PTR _Index2$2[rsp], 0
  00098	eb 0d		 jmp	 SHORT $LN7@Concurrent
$LN5@Concurrent:
  0009a	48 8b 44 24 30	 mov	 rax, QWORD PTR _Index2$2[rsp]
  0009f	48 ff c0	 inc	 rax
  000a2	48 89 44 24 30	 mov	 QWORD PTR _Index2$2[rsp], rax
$LN7@Concurrent:
  000a7	48 8b 44 24 48	 mov	 rax, QWORD PTR _Seg_size$3[rsp]
  000ac	48 39 44 24 30	 cmp	 QWORD PTR _Index2$2[rsp], rax
  000b1	73 02		 jae	 SHORT $LN6@Concurrent

; 181  :                 {
; 182  :                     _Alfi_traits::destroy(_M_allocator, &_M_buckets[_Index][_Index2]);
; 183  :                 }

  000b3	eb e5		 jmp	 SHORT $LN5@Concurrent
$LN6@Concurrent:

; 184  :                 _M_allocator.deallocate(_M_buckets[_Index], _Seg_size);

  000b5	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000bd	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Index$1[rsp]
  000c2	48 8b 44 c8 08	 mov	 rax, QWORD PTR [rax+rcx*8+8]
  000c7	48 89 44 24 68	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  000cc	48 8b 44 24 48	 mov	 rax, QWORD PTR _Seg_size$3[rsp]
  000d1	48 c1 e0 03	 shl	 rax, 3
  000d5	48 89 44 24 38	 mov	 QWORD PTR _Bytes$[rsp], rax
  000da	48 8b 44 24 68	 mov	 rax, QWORD PTR _Ptr$[rsp]
  000df	48 89 44 24 50	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  000e4	48 81 7c 24 38
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  000ed	72 10		 jb	 SHORT $LN23@Concurrent

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  000ef	48 8d 54 24 38	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  000f4	48 8d 4c 24 50	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  000f9	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  000fe	90		 npad	 1
$LN23@Concurrent:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  000ff	48 8b 54 24 38	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  00104	48 8b 4c 24 50	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  00109	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  0010e	90		 npad	 1
$LN8@Concurrent:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h

; 186  :         }

  0010f	e9 03 ff ff ff	 jmp	 $LN2@Concurrent
$LN3@Concurrent:

; 187  :     }

  00114	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0011c	48 05 08 02 00
	00		 add	 rax, 520		; 00000208H
  00122	48 89 44 24 28	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_split_ordered_list.h

; 342  :         clear();

  00127	48 8b 4c 24 28	 mov	 rcx, QWORD PTR this$[rsp]
  0012c	e8 00 00 00 00	 call	 ?clear@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAAXXZ ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >::clear

; 343  : 
; 344  :         // Remove the head element which is not cleared by clear()
; 345  :         _Nodeptr _Pnode = this->_Myhead;

  00131	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00136	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00139	48 89 44 24 70	 mov	 QWORD PTR _Pnode$5[rsp], rax

; 346  :         this->_Myhead = nullptr;

  0013e	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00143	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0

; 347  : 
; 348  :         _ASSERT_EXPR(_Pnode != nullptr && _Pnode->_Next == nullptr, L"Invalid head list node");
; 349  : 
; 350  :         _Erase(_Pnode);

  0014a	48 8b 54 24 70	 mov	 rdx, QWORD PTR _Pnode$5[rsp]
  0014f	48 8b 4c 24 28	 mov	 rcx, QWORD PTR this$[rsp]
  00154	e8 00 00 00 00	 call	 ?_Erase@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAAXPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@23@@Z ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >::_Erase
  00159	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_concurrent_hash.h

; 187  :     }

  0015a	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  00161	c3		 ret	 0
??1?$_Concurrent_hash@V?$_Concurrent_unordered_map_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJV?$_Hash_compare@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@U?$hash@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@U?$equal_to@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@details@Concurrency@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@$0A@@details@Concurrency@@@details@Concurrency@@QEAA@XZ ENDP ; Concurrency::details::_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > >,0> >::~_Concurrent_hash<Concurrency::details::_Concurrent_unordered_map_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,long volatile ,Concurrency::details::_Hash_compare<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::hash<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::equal_to<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > >,0> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_split_ordered_list.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_split_ordered_list.h
;	COMDAT ?_Erase@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAAXPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@23@@Z
_TEXT	SEGMENT
$T1 = 32
tv86 = 36
_Bytes$ = 40
_Ptr$ = 48
this$ = 80
_Delete_node$ = 88
?_Erase@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAAXPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@23@@Z PROC ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >::_Erase, COMDAT

; 545  :     {

$LN124:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 201  :             return (_M_order_key & 0x1) == 0;

  0000e	48 8b 44 24 58	 mov	 rax, QWORD PTR _Delete_node$[rsp]
  00013	48 8b 40 30	 mov	 rax, QWORD PTR [rax+48]
  00017	48 83 e0 01	 and	 rax, 1
  0001b	48 85 c0	 test	 rax, rax
  0001e	75 0a		 jne	 SHORT $LN6@Erase
  00020	c7 44 24 24 01
	00 00 00	 mov	 DWORD PTR tv86[rsp], 1
  00028	eb 08		 jmp	 SHORT $LN7@Erase
$LN6@Erase:
  0002a	c7 44 24 24 00
	00 00 00	 mov	 DWORD PTR tv86[rsp], 0
$LN7@Erase:
  00032	0f b6 44 24 24	 movzx	 eax, BYTE PTR tv86[rsp]
  00037	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al

; 546  :         if (!_Delete_node->_Is_dummy())

  0003b	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  00040	0f b6 c0	 movzx	 eax, al
  00043	85 c0		 test	 eax, eax
  00045	75 2b		 jne	 SHORT $LN2@Erase
  00047	48 8b 44 24 58	 mov	 rax, QWORD PTR _Delete_node$[rsp]
  0004c	48 83 c0 08	 add	 rax, 8
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1383 :         _Tidy_deallocate();

  00050	48 8b c8	 mov	 rcx, rax
  00053	e8 00 00 00 00	 call	 ?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Tidy_deallocate
  00058	90		 npad	 1
  00059	33 c0		 xor	 eax, eax
  0005b	83 e0 01	 and	 eax, 1
  0005e	85 c0		 test	 eax, eax
  00060	74 10		 je	 SHORT $LN2@Erase
  00062	ba 38 00 00 00	 mov	 edx, 56			; 00000038H
  00067	48 8b 4c 24 58	 mov	 rcx, QWORD PTR _Delete_node$[rsp]
  0006c	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00071	90		 npad	 1
$LN2@Erase:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  00072	b8 01 00 00 00	 mov	 eax, 1
  00077	48 6b c0 38	 imul	 rax, rax, 56		; 00000038H
  0007b	48 89 44 24 28	 mov	 QWORD PTR _Bytes$[rsp], rax
  00080	48 8b 44 24 58	 mov	 rax, QWORD PTR _Delete_node$[rsp]
  00085	48 89 44 24 30	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  0008a	48 81 7c 24 28
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  00093	72 10		 jb	 SHORT $LN115@Erase

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  00095	48 8d 54 24 28	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  0009a	48 8d 4c 24 30	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  0009f	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  000a4	90		 npad	 1
$LN115@Erase:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  000a5	48 8b 54 24 28	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  000aa	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  000af	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  000b4	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_split_ordered_list.h

; 552  :     }

  000b5	48 83 c4 48	 add	 rsp, 72			; 00000048H
  000b9	c3		 ret	 0
?_Erase@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAAXPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@23@@Z ENDP ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >::_Erase
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_split_ordered_list.h
;	COMDAT ?clear@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAAXXZ
_TEXT	SEGMENT
_Pnode$ = 32
_Pnext$ = 40
this$ = 64
?clear@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAAXXZ PROC ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >::clear, COMDAT

; 361  :     {

$LN124:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 362  : #if _ITERATOR_DEBUG_LEVEL == 2
; 363  :         _Orphan_ptr(*this, 0);
; 364  : #endif  /* _ITERATOR_DEBUG_LEVEL == 2 */
; 365  : 
; 366  :         _Nodeptr _Pnext;
; 367  :         _Nodeptr _Pnode = this->_Myhead;

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00011	48 89 44 24 20	 mov	 QWORD PTR _Pnode$[rsp], rax

; 368  : 
; 369  :         _ASSERT_EXPR(this->_Myhead != nullptr, L"Invalid head list node");
; 370  :         _Pnext = _Pnode->_Next;

  00016	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0001b	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0001e	48 89 44 24 28	 mov	 QWORD PTR _Pnext$[rsp], rax

; 371  :         _Pnode->_Next = nullptr;

  00023	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00028	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0

; 372  :         _Pnode = _Pnext;

  0002f	48 8b 44 24 28	 mov	 rax, QWORD PTR _Pnext$[rsp]
  00034	48 89 44 24 20	 mov	 QWORD PTR _Pnode$[rsp], rax
$LN2@clear:

; 373  : 
; 374  :         while (_Pnode != nullptr)

  00039	48 83 7c 24 20
	00		 cmp	 QWORD PTR _Pnode$[rsp], 0
  0003f	74 28		 je	 SHORT $LN3@clear

; 375  :         {
; 376  :             _Pnext = _Pnode->_Next;

  00041	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00046	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00049	48 89 44 24 28	 mov	 QWORD PTR _Pnext$[rsp], rax

; 377  :             _Erase(_Pnode);

  0004e	48 8b 54 24 20	 mov	 rdx, QWORD PTR _Pnode$[rsp]
  00053	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00058	e8 00 00 00 00	 call	 ?_Erase@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAAXPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@23@@Z ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >::_Erase

; 378  :             _Pnode = _Pnext;

  0005d	48 8b 44 24 28	 mov	 rax, QWORD PTR _Pnext$[rsp]
  00062	48 89 44 24 20	 mov	 QWORD PTR _Pnode$[rsp], rax

; 379  :         }

  00067	eb d0		 jmp	 SHORT $LN2@clear
$LN3@clear:

; 380  : 
; 381  :         _M_element_count = 0;

  00069	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0006e	c7 40 10 00 00
	00 00		 mov	 DWORD PTR [rax+16], 0

; 382  :     }

  00075	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00079	c3		 ret	 0
?clear@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAAXXZ ENDP ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >::clear
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_split_ordered_list.h
;	COMDAT ??1?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAA@XZ
_TEXT	SEGMENT
_Pnode$ = 32
this$ = 64
??1?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAA@XZ PROC ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >::~_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >, COMDAT

; 340  :     {

$LN251:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 341  :         // Clear the list
; 342  :         clear();

  00009	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0000e	e8 00 00 00 00	 call	 ?clear@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAAXXZ ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >::clear

; 343  : 
; 344  :         // Remove the head element which is not cleared by clear()
; 345  :         _Nodeptr _Pnode = this->_Myhead;

  00013	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00018	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0001b	48 89 44 24 20	 mov	 QWORD PTR _Pnode$[rsp], rax

; 346  :         this->_Myhead = nullptr;

  00020	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00025	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0

; 347  : 
; 348  :         _ASSERT_EXPR(_Pnode != nullptr && _Pnode->_Next == nullptr, L"Invalid head list node");
; 349  : 
; 350  :         _Erase(_Pnode);

  0002c	48 8b 54 24 20	 mov	 rdx, QWORD PTR _Pnode$[rsp]
  00031	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00036	e8 00 00 00 00	 call	 ?_Erase@?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAAXPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@23@@Z ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >::_Erase
  0003b	90		 npad	 1

; 351  :     }

  0003c	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00040	c3		 ret	 0
??1?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAA@XZ ENDP ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >::~_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\internal_split_ordered_list.h
;	COMDAT ??0?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAA@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@std@@@Z
_TEXT	SEGMENT
_Allocator$ = 32
_Pnode$1 = 40
$T2 = 48
this$ = 80
_Allocator$ = 88
??0?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAA@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@std@@@Z PROC ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >, COMDAT

; 336  :     {

$LN29:
  00000	88 54 24 10	 mov	 BYTE PTR [rsp+16], dl
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 266  :     _Split_order_list_value(_Allocator_type _Allocator = _Allocator_type()) : _Mybase(_Allocator)

  0000d	0f b6 44 24 58	 movzx	 eax, BYTE PTR _Allocator$[rsp]
  00012	88 44 24 20	 mov	 BYTE PTR _Allocator$[rsp], al

; 301  :         _Nodeptr _Pnode = this->_M_node_allocator.allocate(1);

  00016	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0001b	48 83 c0 08	 add	 rax, 8
  0001f	ba 01 00 00 00	 mov	 edx, 1
  00024	48 8b c8	 mov	 rcx, rax
  00027	e8 00 00 00 00	 call	 ?allocate@?$allocator@U_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@QEAAPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@_K@Z ; std::allocator<Concurrency::details::_Split_order_list_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >::_Node>::allocate
  0002c	48 89 44 24 28	 mov	 QWORD PTR _Pnode$1[rsp], rax

; 169  :             _M_order_key = _Order_key;

  00031	48 8b 44 24 28	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  00036	48 c7 40 30 00
	00 00 00	 mov	 QWORD PTR [rax+48], 0

; 170  :             _Next = nullptr;

  0003e	48 8b 44 24 28	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  00043	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0

; 304  :         return (_Pnode);

  0004a	48 8b 44 24 28	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  0004f	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax

; 270  :         this->_Myhead = _Buynode(0);

  00054	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
  00059	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  0005e	48 89 01	 mov	 QWORD PTR [rcx], rax

; 305  :     }
; 306  : };
; 307  : 
; 308  : // Forward list in which elements are sorted in a split-order
; 309  : template <typename _Element_type, typename _Element_allocator_type = ::std::allocator<_Element_type>>
; 310  : class _Split_ordered_list : _Split_order_list_value<_Element_type, _Element_allocator_type>
; 311  : {
; 312  : public:
; 313  :     typedef _Split_ordered_list<_Element_type, _Element_allocator_type> _Mytype;
; 314  :     typedef _Split_order_list_value<_Element_type, _Element_allocator_type> _Mybase;
; 315  :     using typename _Mybase::_Allocator_type;
; 316  :     using typename _Mybase::_Al_traits;
; 317  :     using typename _Mybase::_Nodeptr;
; 318  :     using typename _Mybase::_Alnode_traits;
; 319  : 
; 320  :     typedef _Allocator_type allocator_type;
; 321  :     using typename _Mybase::size_type;
; 322  :     using typename _Mybase::difference_type;
; 323  :     using typename _Mybase::pointer;
; 324  :     using typename _Mybase::const_pointer;
; 325  :     using typename _Mybase::reference;
; 326  :     using typename _Mybase::const_reference;
; 327  :     using typename _Mybase::value_type;
; 328  : 
; 329  :     typedef _Solist_const_iterator<_Mybase> const_iterator;
; 330  :     typedef _Solist_iterator<_Mybase> iterator;
; 331  :     typedef ::std::_Flist_const_iterator<_Mybase> _Full_const_iterator;
; 332  :     typedef ::std::_Flist_iterator<_Mybase> _Full_iterator;
; 333  :     typedef ::std::pair<iterator, bool> _Pairib;
; 334  :     using _Mybase::_Buynode;
; 335  :     _Split_ordered_list(allocator_type _Allocator = allocator_type()) : _Mybase(_Allocator), _M_element_count(0)

  00061	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00066	c7 40 10 00 00
	00 00		 mov	 DWORD PTR [rax+16], 0

; 337  :     }

  0006d	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00072	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00076	c3		 ret	 0
??0?$_Split_ordered_list@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@QEAA@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@std@@@Z ENDP ; Concurrency::details::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >::_Split_ordered_list<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?allocate@?$allocator@U_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@QEAAPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@_K@Z
_TEXT	SEGMENT
_Overflow_is_possible$1 = 32
_Bytes$ = 40
$T2 = 48
$T3 = 56
$T4 = 64
_Max_possible$5 = 72
this$ = 96
_Count$ = 104
?allocate@?$allocator@U_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@QEAAPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@_K@Z PROC ; std::allocator<Concurrency::details::_Split_order_list_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >::_Node>::allocate, COMDAT

; 988  :     _NODISCARD_RAW_PTR_ALLOC _CONSTEXPR20 __declspec(allocator) _Ty* allocate(_CRT_GUARDOVERFLOW const size_t _Count) {

$LN13:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 113  :     constexpr bool _Overflow_is_possible = _Ty_size > 1;

  0000e	c6 44 24 20 01	 mov	 BYTE PTR _Overflow_is_possible$1[rsp], 1

; 114  : 
; 115  :     if constexpr (_Overflow_is_possible) {
; 116  :         constexpr size_t _Max_possible = static_cast<size_t>(-1) / _Ty_size;

  00013	48 b8 92 24 49
	92 24 49 92 04	 mov	 rax, 329406144173384850	; 0492492492492492H
  0001d	48 89 44 24 48	 mov	 QWORD PTR _Max_possible$5[rsp], rax

; 117  :         if (_Count > _Max_possible) {

  00022	48 b8 92 24 49
	92 24 49 92 04	 mov	 rax, 329406144173384850	; 0492492492492492H
  0002c	48 39 44 24 68	 cmp	 QWORD PTR _Count$[rsp], rax
  00031	76 06		 jbe	 SHORT $LN4@allocate

; 118  :             _Throw_bad_array_new_length(); // multiply overflow

  00033	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00038	90		 npad	 1
$LN4@allocate:

; 119  :         }
; 120  :     }
; 121  : 
; 122  :     return _Count * _Ty_size;

  00039	48 6b 44 24 68
	38		 imul	 rax, QWORD PTR _Count$[rsp], 56 ; 00000038H
  0003f	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00044	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
  00049	48 89 44 24 28	 mov	 QWORD PTR _Bytes$[rsp], rax

; 227  :     if (_Bytes == 0) {

  0004e	48 83 7c 24 28
	00		 cmp	 QWORD PTR _Bytes$[rsp], 0
  00054	75 0b		 jne	 SHORT $LN8@allocate

; 228  :         return nullptr;

  00056	48 c7 44 24 30
	00 00 00 00	 mov	 QWORD PTR $T2[rsp], 0
  0005f	eb 35		 jmp	 SHORT $LN7@allocate
$LN8@allocate:

; 229  :     }
; 230  : 
; 231  : #if _HAS_CXX20 // TRANSITION, GH-1532
; 232  :     if (_STD is_constant_evaluated()) {
; 233  :         return _Traits::_Allocate(_Bytes);
; 234  :     }
; 235  : #endif // _HAS_CXX20
; 236  : 
; 237  : #ifdef __cpp_aligned_new
; 238  :     if constexpr (_Align > __STDCPP_DEFAULT_NEW_ALIGNMENT__) {
; 239  :         size_t _Passed_align = _Align;
; 240  : #if defined(_M_IX86) || defined(_M_X64)
; 241  :         if (_Bytes >= _Big_allocation_threshold) {
; 242  :             // boost the alignment of big allocations to help autovectorization
; 243  :             _Passed_align = (_STD max)(_Align, _Big_allocation_alignment);
; 244  :         }
; 245  : #endif // defined(_M_IX86) || defined(_M_X64)
; 246  :         return _Traits::_Allocate_aligned(_Bytes, _Passed_align);
; 247  :     } else
; 248  : #endif // defined(__cpp_aligned_new)
; 249  :     {
; 250  : #if defined(_M_IX86) || defined(_M_X64)
; 251  :         if (_Bytes >= _Big_allocation_threshold) {

  00061	48 81 7c 24 28
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  0006a	72 11		 jb	 SHORT $LN9@allocate

; 252  :             // boost the alignment of big allocations to help autovectorization
; 253  :             return _Allocate_manually_vector_aligned<_Traits>(_Bytes);

  0006c	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00071	e8 00 00 00 00	 call	 ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
  00076	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
  0007b	eb 19		 jmp	 SHORT $LN7@allocate
$LN9@allocate:

; 136  :         return ::operator new(_Bytes);

  0007d	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00082	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00087	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax

; 256  :         return _Traits::_Allocate(_Bytes);

  0008c	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  00091	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
$LN7@allocate:

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00096	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
$LN6@allocate:

; 991  :     }

  0009b	48 83 c4 58	 add	 rsp, 88			; 00000058H
  0009f	c3		 ret	 0
?allocate@?$allocator@U_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@@std@@QEAAPEAU_Node@?$_Split_order_list_node@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@$$CCJ@std@@@2@@details@Concurrency@@_K@Z ENDP ; std::allocator<Concurrency::details::_Split_order_list_node<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,long volatile > > >::_Node>::allocate
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??_G?$ISingleton@VtheManagerCounter@mu2@@@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_G?$ISingleton@VtheManagerCounter@mu2@@@mu2@@UEAAPEAXI@Z PROC ; mu2::ISingleton<mu2::theManagerCounter>::`scalar deleting destructor', COMDAT
$LN10:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 80   : 	virtual~ISingleton() {}

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$ISingleton@VtheManagerCounter@mu2@@@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx
  0001c	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00020	83 e0 01	 and	 eax, 1
  00023	85 c0		 test	 eax, eax
  00025	74 10		 je	 SHORT $LN2@scalar
  00027	ba 08 00 00 00	 mov	 edx, 8
  0002c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00031	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00036	90		 npad	 1
$LN2@scalar:
  00037	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003c	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00040	c3		 ret	 0
??_G?$ISingleton@VtheManagerCounter@mu2@@@mu2@@UEAAPEAXI@Z ENDP ; mu2::ISingleton<mu2::theManagerCounter>::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??1?$ISingleton@VtheManagerCounter@mu2@@@mu2@@UEAA@XZ
_TEXT	SEGMENT
this$ = 8
??1?$ISingleton@VtheManagerCounter@mu2@@@mu2@@UEAA@XZ PROC ; mu2::ISingleton<mu2::theManagerCounter>::~ISingleton<mu2::theManagerCounter>, COMDAT

; 80   : 	virtual~ISingleton() {}

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0000a	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$ISingleton@VtheManagerCounter@mu2@@@mu2@@6B@
  00011	48 89 08	 mov	 QWORD PTR [rax], rcx
  00014	c3		 ret	 0
??1?$ISingleton@VtheManagerCounter@mu2@@@mu2@@UEAA@XZ ENDP ; mu2::ISingleton<mu2::theManagerCounter>::~ISingleton<mu2::theManagerCounter>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ
_TEXT	SEGMENT
$T1 = 32
$T2 = 33
_My_data$ = 40
tv94 = 48
_Bytes$ = 56
_Ptr$ = 64
$T3 = 72
$T4 = 80
_Capacity$ = 88
_Old_ptr$ = 96
_Al$5 = 104
this$ = 128
?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ PROC ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Tidy_deallocate, COMDAT

; 3080 :     _CONSTEXPR20 void _Tidy_deallocate() noexcept { // initialize buffer, deallocating any storage

$LN62:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 78	 sub	 rsp, 120		; 00000078H

; 3081 :         auto& _My_data = _Mypair._Myval2;

  00009	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00011	48 89 44 24 28	 mov	 QWORD PTR _My_data$[rsp], rax

; 453  :         return _Myres > _Small_string_capacity;

  00016	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  0001b	48 83 78 18 0f	 cmp	 QWORD PTR [rax+24], 15
  00020	76 0a		 jbe	 SHORT $LN12@Tidy_deall
  00022	c7 44 24 30 01
	00 00 00	 mov	 DWORD PTR tv94[rsp], 1
  0002a	eb 08		 jmp	 SHORT $LN13@Tidy_deall
$LN12@Tidy_deall:
  0002c	c7 44 24 30 00
	00 00 00	 mov	 DWORD PTR tv94[rsp], 0
$LN13@Tidy_deall:
  00034	0f b6 44 24 30	 movzx	 eax, BYTE PTR tv94[rsp]
  00039	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al

; 3082 :         _My_data._Orphan_all();
; 3083 :         if (_My_data._Large_mode_engaged()) {

  0003d	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  00042	0f b6 c0	 movzx	 eax, al
  00045	85 c0		 test	 eax, eax
  00047	74 7e		 je	 SHORT $LN2@Tidy_deall

; 3107 :         return _Mypair._Get_first();

  00049	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00051	48 89 44 24 48	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3107 :         return _Mypair._Get_first();

  00056	48 8b 44 24 48	 mov	 rax, QWORD PTR $T3[rsp]
  0005b	48 89 44 24 50	 mov	 QWORD PTR $T4[rsp], rax

; 3084 :             _ASAN_STRING_REMOVE(*this);
; 3085 :             auto& _Al = _Getal();

  00060	48 8b 44 24 50	 mov	 rax, QWORD PTR $T4[rsp]
  00065	48 89 44 24 68	 mov	 QWORD PTR _Al$5[rsp], rax

; 3086 :             _Deallocate_for_capacity(_Al, _My_data._Bx._Ptr, _My_data._Myres);

  0006a	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  0006f	48 8b 40 18	 mov	 rax, QWORD PTR [rax+24]
  00073	48 89 44 24 58	 mov	 QWORD PTR _Capacity$[rsp], rax
  00078	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  0007d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00080	48 89 44 24 60	 mov	 QWORD PTR _Old_ptr$[rsp], rax

; 852  :         _Al.deallocate(_Old_ptr, _Capacity + 1); // +1 for null terminator

  00085	48 8b 44 24 58	 mov	 rax, QWORD PTR _Capacity$[rsp]
  0008a	48 ff c0	 inc	 rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  0008d	48 89 44 24 38	 mov	 QWORD PTR _Bytes$[rsp], rax
  00092	48 8b 44 24 60	 mov	 rax, QWORD PTR _Old_ptr$[rsp]
  00097	48 89 44 24 40	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  0009c	48 81 7c 24 38
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  000a5	72 10		 jb	 SHORT $LN38@Tidy_deall

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  000a7	48 8d 54 24 38	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  000ac	48 8d 4c 24 40	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  000b1	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  000b6	90		 npad	 1
$LN38@Tidy_deall:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  000b7	48 8b 54 24 38	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  000bc	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  000c1	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  000c6	90		 npad	 1
$LN2@Tidy_deall:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3090 :         _My_data._Mysize = 0;

  000c7	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000cc	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 3091 :         _My_data._Myres  = _Small_string_capacity;

  000d4	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000d9	48 c7 40 18 0f
	00 00 00	 mov	 QWORD PTR [rax+24], 15

; 3092 :         // the _Traits::assign is last so the codegen doesn't think the char write can alias this
; 3093 :         _Traits::assign(_My_data._Bx._Buf[0], _Elem());

  000e1	c6 44 24 21 00	 mov	 BYTE PTR $T2[rsp], 0
  000e6	b8 01 00 00 00	 mov	 eax, 1
  000eb	48 6b c0 00	 imul	 rax, rax, 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 502  :         _Left = _Right;

  000ef	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _My_data$[rsp]
  000f4	0f b6 54 24 21	 movzx	 edx, BYTE PTR $T2[rsp]
  000f9	88 14 01	 mov	 BYTE PTR [rcx+rax], dl
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3094 :     }

  000fc	48 83 c4 78	 add	 rsp, 120		; 00000078H
  00100	c3		 ret	 0
?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ ENDP ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Tidy_deallocate
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z
_TEXT	SEGMENT
_Back_shift$ = 48
_Ptr_container$ = 56
_Ptr_user$ = 64
_Min_back_shift$ = 72
_Ptr$ = 96
_Bytes$ = 104
?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z PROC ; std::_Adjust_manually_vector_aligned, COMDAT

; 200  : inline void _Adjust_manually_vector_aligned(void*& _Ptr, size_t& _Bytes) {

$LN5:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 201  :     // adjust parameters from _Allocate_manually_vector_aligned to pass to operator delete
; 202  :     _Bytes += _Non_user_size;

  0000e	48 8b 44 24 68	 mov	 rax, QWORD PTR _Bytes$[rsp]
  00013	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00016	48 83 c0 27	 add	 rax, 39			; 00000027H
  0001a	48 8b 4c 24 68	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  0001f	48 89 01	 mov	 QWORD PTR [rcx], rax

; 203  : 
; 204  :     const uintptr_t* const _Ptr_user = static_cast<uintptr_t*>(_Ptr);

  00022	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00027	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0002a	48 89 44 24 40	 mov	 QWORD PTR _Ptr_user$[rsp], rax

; 205  :     const uintptr_t _Ptr_container   = _Ptr_user[-1];

  0002f	b8 08 00 00 00	 mov	 eax, 8
  00034	48 6b c0 ff	 imul	 rax, rax, -1
  00038	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr_user$[rsp]
  0003d	48 8b 04 01	 mov	 rax, QWORD PTR [rcx+rax]
  00041	48 89 44 24 38	 mov	 QWORD PTR _Ptr_container$[rsp], rax

; 206  : 
; 207  :     // If the following asserts, it likely means that we are performing
; 208  :     // an aligned delete on memory coming from an unaligned allocation.
; 209  :     _STL_ASSERT(_Ptr_user[-2] == _Big_allocation_sentinel, "invalid argument");
; 210  : 
; 211  :     // Extra paranoia on aligned allocation/deallocation; ensure _Ptr_container is
; 212  :     // in range [_Min_back_shift, _Non_user_size]
; 213  : #ifdef _DEBUG
; 214  :     constexpr uintptr_t _Min_back_shift = 2 * sizeof(void*);
; 215  : #else // ^^^ defined(_DEBUG) / !defined(_DEBUG) vvv
; 216  :     constexpr uintptr_t _Min_back_shift = sizeof(void*);

  00046	48 c7 44 24 48
	08 00 00 00	 mov	 QWORD PTR _Min_back_shift$[rsp], 8

; 217  : #endif // ^^^ !defined(_DEBUG) ^^^
; 218  :     const uintptr_t _Back_shift = reinterpret_cast<uintptr_t>(_Ptr) - _Ptr_container;

  0004f	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00054	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr_container$[rsp]
  00059	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0005c	48 2b c1	 sub	 rax, rcx
  0005f	48 89 44 24 30	 mov	 QWORD PTR _Back_shift$[rsp], rax

; 219  :     _STL_VERIFY(_Back_shift >= _Min_back_shift && _Back_shift <= _Non_user_size, "invalid argument");

  00064	48 83 7c 24 30
	08		 cmp	 QWORD PTR _Back_shift$[rsp], 8
  0006a	72 08		 jb	 SHORT $LN3@Adjust_man
  0006c	48 83 7c 24 30
	27		 cmp	 QWORD PTR _Back_shift$[rsp], 39 ; 00000027H
  00072	76 19		 jbe	 SHORT $LN2@Adjust_man
$LN3@Adjust_man:
  00074	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR [rsp+32], 0
  0007d	45 33 c9	 xor	 r9d, r9d
  00080	45 33 c0	 xor	 r8d, r8d
  00083	33 d2		 xor	 edx, edx
  00085	33 c9		 xor	 ecx, ecx
  00087	e8 00 00 00 00	 call	 _invoke_watson
  0008c	90		 npad	 1
$LN2@Adjust_man:

; 220  :     _Ptr = reinterpret_cast<void*>(_Ptr_container);

  0008d	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00092	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr_container$[rsp]
  00097	48 89 08	 mov	 QWORD PTR [rax], rcx
$LN4@Adjust_man:

; 221  : }

  0009a	48 83 c4 58	 add	 rsp, 88			; 00000058H
  0009e	c3		 ret	 0
?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ENDP ; std::_Adjust_manually_vector_aligned
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?_Throw_bad_array_new_length@std@@YAXXZ
_TEXT	SEGMENT
$T1 = 32
?_Throw_bad_array_new_length@std@@YAXXZ PROC		; std::_Throw_bad_array_new_length, COMDAT

; 107  : [[noreturn]] inline void _Throw_bad_array_new_length() {

$LN3:
  00000	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 108  :     _THROW(bad_array_new_length{});

  00004	48 8d 4c 24 20	 lea	 rcx, QWORD PTR $T1[rsp]
  00009	e8 00 00 00 00	 call	 ??0bad_array_new_length@std@@QEAA@XZ ; std::bad_array_new_length::bad_array_new_length
  0000e	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:_TI3?AVbad_array_new_length@std@@
  00015	48 8d 4c 24 20	 lea	 rcx, QWORD PTR $T1[rsp]
  0001a	e8 00 00 00 00	 call	 _CxxThrowException
  0001f	90		 npad	 1
$LN2@Throw_bad_:

; 109  : }

  00020	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00024	c3		 ret	 0
?_Throw_bad_array_new_length@std@@YAXXZ ENDP		; std::_Throw_bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gbad_array_new_length@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gbad_array_new_length@std@@UEAAPEAXI@Z PROC		; std::bad_array_new_length::`scalar deleting destructor', COMDAT
$LN20:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gbad_array_new_length@std@@UEAAPEAXI@Z ENDP		; std::bad_array_new_length::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??0bad_array_new_length@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
__that$ = 56
??0bad_array_new_length@std@@QEAA@AEBV01@@Z PROC	; std::bad_array_new_length::bad_array_new_length, COMDAT
$LN14:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000e	48 8b 54 24 38	 mov	 rdx, QWORD PTR __that$[rsp]
  00013	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00018	e8 00 00 00 00	 call	 ??0bad_alloc@std@@QEAA@AEBV01@@Z
  0001d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00022	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_array_new_length@std@@6B@
  00029	48 89 08	 mov	 QWORD PTR [rax], rcx
  0002c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00031	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00035	c3		 ret	 0
??0bad_array_new_length@std@@QEAA@AEBV01@@Z ENDP	; std::bad_array_new_length::bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??1bad_array_new_length@std@@UEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1bad_array_new_length@std@@UEAA@XZ PROC		; std::bad_array_new_length::~bad_array_new_length, COMDAT
$LN14:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  00018	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 83 c0 08	 add	 rax, 8
  00021	48 8b c8	 mov	 rcx, rax
  00024	e8 00 00 00 00	 call	 __std_exception_destroy
  00029	90		 npad	 1
  0002a	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0002e	c3		 ret	 0
??1bad_array_new_length@std@@UEAA@XZ ENDP		; std::bad_array_new_length::~bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0bad_array_new_length@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 16
??0bad_array_new_length@std@@QEAA@XZ PROC		; std::bad_array_new_length::bad_array_new_length, COMDAT

; 144  :     {

$LN14:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi

; 67   :     {

  00006	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0000b	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00012	48 89 08	 mov	 QWORD PTR [rax], rcx

; 66   :         : _Data()

  00015	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 83 c0 08	 add	 rax, 8
  0001e	48 8b f8	 mov	 rdi, rax
  00021	33 c0		 xor	 eax, eax
  00023	b9 10 00 00 00	 mov	 ecx, 16
  00028	f3 aa		 rep stosb

; 68   :         _Data._What = _Message;

  0002a	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0002f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_0BF@KINCDENJ@bad?5array?5new?5length@
  00036	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 133  :     {

  0003a	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0003f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_alloc@std@@6B@
  00046	48 89 08	 mov	 QWORD PTR [rax], rcx

; 144  :     {

  00049	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_array_new_length@std@@6B@
  00055	48 89 08	 mov	 QWORD PTR [rax], rcx

; 145  :     }

  00058	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0005d	5f		 pop	 rdi
  0005e	c3		 ret	 0
??0bad_array_new_length@std@@QEAA@XZ ENDP		; std::bad_array_new_length::bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gbad_alloc@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gbad_alloc@std@@UEAAPEAXI@Z PROC			; std::bad_alloc::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gbad_alloc@std@@UEAAPEAXI@Z ENDP			; std::bad_alloc::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0bad_alloc@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
__that$ = 56
??0bad_alloc@std@@QEAA@AEBV01@@Z PROC			; std::bad_alloc::bad_alloc, COMDAT
$LN9:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 20	 sub	 rsp, 32			; 00000020H

; 73   :     {

  0000f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  0001b	48 89 08	 mov	 QWORD PTR [rax], rcx

; 72   :         : _Data()

  0001e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 83 c0 08	 add	 rax, 8
  00027	48 8b f8	 mov	 rdi, rax
  0002a	33 c0		 xor	 eax, eax
  0002c	b9 10 00 00 00	 mov	 ecx, 16
  00031	f3 aa		 rep stosb

; 74   :         __std_exception_copy(&_Other._Data, &_Data);

  00033	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 83 c0 08	 add	 rax, 8
  0003c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR __that$[rsp]
  00041	48 83 c1 08	 add	 rcx, 8
  00045	48 8b d0	 mov	 rdx, rax
  00048	e8 00 00 00 00	 call	 __std_exception_copy
  0004d	90		 npad	 1
  0004e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_alloc@std@@6B@
  0005a	48 89 08	 mov	 QWORD PTR [rax], rcx
  0005d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00062	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00066	5f		 pop	 rdi
  00067	c3		 ret	 0
??0bad_alloc@std@@QEAA@AEBV01@@Z ENDP			; std::bad_alloc::bad_alloc
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gexception@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gexception@std@@UEAAPEAXI@Z PROC			; std::exception::`scalar deleting destructor', COMDAT
$LN10:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gexception@std@@UEAAPEAXI@Z ENDP			; std::exception::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ?what@exception@std@@UEBAPEBDXZ
_TEXT	SEGMENT
tv69 = 0
this$ = 32
?what@exception@std@@UEBAPEBDXZ PROC			; std::exception::what, COMDAT

; 95   :     {

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 18	 sub	 rsp, 24

; 96   :         return _Data._What ? _Data._What : "Unknown exception";

  00009	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00013	74 0f		 je	 SHORT $LN3@what
  00015	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0001e	48 89 04 24	 mov	 QWORD PTR tv69[rsp], rax
  00022	eb 0b		 jmp	 SHORT $LN4@what
$LN3@what:
  00024	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BC@EOODALEL@Unknown?5exception@
  0002b	48 89 04 24	 mov	 QWORD PTR tv69[rsp], rax
$LN4@what:
  0002f	48 8b 04 24	 mov	 rax, QWORD PTR tv69[rsp]

; 97   :     }

  00033	48 83 c4 18	 add	 rsp, 24
  00037	c3		 ret	 0
?what@exception@std@@UEBAPEBDXZ ENDP			; std::exception::what
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0exception@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
_Other$ = 56
??0exception@std@@QEAA@AEBV01@@Z PROC			; std::exception::exception, COMDAT

; 73   :     {

$LN4:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  0000f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  0001b	48 89 08	 mov	 QWORD PTR [rax], rcx

; 72   :         : _Data()

  0001e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 83 c0 08	 add	 rax, 8
  00027	48 8b f8	 mov	 rdi, rax
  0002a	33 c0		 xor	 eax, eax
  0002c	b9 10 00 00 00	 mov	 ecx, 16
  00031	f3 aa		 rep stosb

; 74   :         __std_exception_copy(&_Other._Data, &_Data);

  00033	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 83 c0 08	 add	 rax, 8
  0003c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Other$[rsp]
  00041	48 83 c1 08	 add	 rcx, 8
  00045	48 8b d0	 mov	 rdx, rax
  00048	e8 00 00 00 00	 call	 __std_exception_copy
  0004d	90		 npad	 1

; 75   :     }

  0004e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00057	5f		 pop	 rdi
  00058	c3		 ret	 0
??0exception@std@@QEAA@AEBV01@@Z ENDP			; std::exception::exception
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Fcs\WorldFcsAsyncReceiverAuth.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Fcs\WorldFcsAsyncReceiverAuth.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
