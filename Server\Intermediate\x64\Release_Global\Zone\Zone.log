﻿  ActionSkillControl.cpp
  ActionPlayerInventory.cpp
  ActionPlayerStorage.cpp
  ActionPlayerKnightageStorage.cpp
  ActionPlayerSetCard.cpp
  ActionPlayerTalisman.cpp
  GMCommandSystem.cpp
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): error C2220: 以下警告被视为错误
  (编译源文件“/Action/ActionPlayerStorage.cpp”)
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316: “std::_Ref_count_obj2<_Ty>”: 在堆上分配的对象可能不是对齐 32
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316:         with
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316:         [
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316:             _Ty=mu2::InvenStorageAccount
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316:         ]
  (编译源文件“/Action/ActionPlayerStorage.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46):
      模板实例化上下文(最早的实例化上下文)为
          F:\Release_Branch\Server\Development\Backend\Zone\Action\ActionPlayerStorage.cpp(81,45):
          查看对正在编译的函数 模板 实例化“T *mu2::ActionPlayerInventory::CreateInven<mu2::InvenStorageAccount>(const mu2::eInvenType &,const SlotSizeType,Bool,Bool)”的引用
          with
          [
              T=mu2::InvenStorageAccount
          ]
          F:\Release_Branch\Server\Development\Backend\Zone\Action\ActionPlayerInventory.h(436,27):
          查看对正在编译的函数 模板 实例化“std::shared_ptr<T> std::make_shared<T,mu2::EntityPlayer&,const mu2::eInvenType&,const SlotSizeType&,Bool&,Bool&>(mu2::EntityPlayer &,const mu2::eInvenType &,const SlotSizeType &,Bool &,Bool &)”的引用
          with
          [
              T=mu2::InvenStorageAccount
          ]
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): error C2220: 以下警告被视为错误
  (编译源文件“/Action/ActionPlayer/ActionPlayerKnightageStorage.cpp”)
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316: “std::_Ref_count_obj2<_Ty>”: 在堆上分配的对象可能不是对齐 32
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316:         with
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316:         [
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316:             _Ty=mu2::InvenStorageKnightage
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316:         ]
  (编译源文件“/Action/ActionPlayer/ActionPlayerKnightageStorage.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46):
      模板实例化上下文(最早的实例化上下文)为
          F:\Release_Branch\Server\Development\Backend\Zone\Action\ActionPlayer\ActionPlayerKnightageStorage.cpp(318,29):
          查看对正在编译的函数 模板 实例化“T *mu2::ActionPlayerInventory::CreateInven<mu2::InvenStorageKnightage>(const mu2::eInvenType &,const SlotSizeType,Bool,Bool)”的引用
          with
          [
              T=mu2::InvenStorageKnightage
          ]
          F:\Release_Branch\Server\Development\Backend\Zone\Action\ActionPlayerInventory.h(436,27):
          查看对正在编译的函数 模板 实例化“std::shared_ptr<T> std::make_shared<T,mu2::EntityPlayer&,const mu2::eInvenType&,const SlotSizeType&,Bool&,Bool&>(mu2::EntityPlayer &,const mu2::eInvenType &,const SlotSizeType &,Bool &,Bool &)”的引用
          with
          [
              T=mu2::InvenStorageKnightage
          ]
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): error C2220: 以下警告被视为错误
  (编译源文件“/Action/ActionCommon/ActionSkillControl.cpp”)
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244: “初始化”: 从“_Ty”转换到“_Ty2”，可能丢失数据
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244:         with
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244:         [
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244:             _Ty=int
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244:         ]
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244:         and
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244:         [
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244:             _Ty2=Byte
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244:         ]
  (编译源文件“/Action/ActionCommon/ActionSkillControl.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54):
      模板实例化上下文(最早的实例化上下文)为
          F:\Release_Branch\Server\Development\Backend\Zone\Action\ActionCommon\ActionSkillControl.cpp(622,58):
          查看对正在编译的函数 模板 实例化“std::pair<const UInt32,Byte>::pair<const IndexSkill&,int,0>(_Other1,_Other2 &&) noexcept”的引用
          with
          [
              _Other1=const IndexSkill &,
              _Other2=int
          ]
              F:\Release_Branch\Server\Development\Backend\Zone\Action\ActionCommon\ActionSkillControl.cpp(622,25):
              请参阅 "mu2::ActionSkillControl::GetCurAniIndex" 中对 "std::pair<const UInt32,Byte>::pair" 的第一个引用
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): error C2220: 以下警告被视为错误
  (编译源文件“/Action/ActionPlayer/ActionPlayerSetCard.cpp”)
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244: “初始化”: 从“const int”转换到“_Ty2”，可能丢失数据
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244:         with
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244:         [
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244:             _Ty2=UInt16
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244:         ]
  (编译源文件“/Action/ActionPlayer/ActionPlayerSetCard.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54):
      模板实例化上下文(最早的实例化上下文)为
          F:\Release_Branch\Server\Development\Backend\Zone\Action\ActionPlayer\ActionPlayerSetCard.cpp(311,27):
          查看对正在编译的函数 模板 实例化“std::pair<std::_Tree_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<const IndexItem,UInt16>>>>,bool> std::_Tree<std::_Tmap_traits<_Kty,_Ty,_Pr,_Alloc,false>>::emplace<_Ty1&,const _Ty2&>(_Ty1 &,const _Ty2 &)”的引用
          with
          [
              _Kty=IndexItem,
              _Ty=UInt16,
              _Pr=std::less<int>,
              _Alloc=std::allocator<std::pair<const IndexItem,UInt16>>,
              _Ty1=const IndexItem,
              _Ty2=Int32
          ]
              F:\Release_Branch\Server\Development\Backend\Zone\Action\ActionPlayer\ActionPlayerSetCard.cpp(311,34):
              请参阅 "mu2::ActionPlayerSetCard::checkOpenSetCard" 中对 "std::_Tree<std::_Tmap_traits<_Kty,_Ty,_Pr,_Alloc,false>>::emplace" 的第一个引用
          with
          [
              _Kty=IndexItem,
              _Ty=UInt16,
              _Pr=std::less<int>,
              _Alloc=std::allocator<std::pair<const IndexItem,UInt16>>
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree(1048,30):
          查看对正在编译的函数 模板 实例化“std::pair<std::_Tree_node<std::pair<const IndexItem,UInt16>,std::_Default_allocator_traits<_Alloc>::void_pointer> *,bool> std::_Tree<std::_Tmap_traits<_Kty,_Ty,_Pr,_Alloc,false>>::_Emplace<const int&,const int&>(const int &,const int &)”的引用
          with
          [
              _Alloc=std::allocator<std::pair<const IndexItem,UInt16>>,
              _Kty=IndexItem,
              _Ty=UInt16,
              _Pr=std::less<int>
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree(1023,49):
          查看对正在编译的函数 模板 实例化“std::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<const IndexItem,UInt16>,std::_Default_allocator_traits<_Alloc>::void_pointer>>>::_Tree_temp_node<const int&,const int&>(_Alnode &,std::_Tree_node<std::pair<const IndexItem,UInt16>,std::_Default_allocator_traits<_Alloc>::void_pointer> *,const int &,const int &)”的引用
          with
          [
              _Alloc=std::allocator<std::pair<const IndexItem,UInt16>>,
              _Alnode=std::allocator<std::_Tree_node<std::pair<const IndexItem,UInt16>,std::_Default_allocator_traits<std::allocator<std::pair<const IndexItem,UInt16>>>::void_pointer>>
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree(830,25):
          查看对正在编译的函数 模板 实例化“void std::_Default_allocator_traits<_Alloc>::construct<_Ty,const int&,const int&>(_Alloc &,_Objty *const ,const int &,const int &)”的引用
          with
          [
              _Alloc=std::allocator<std::_Tree_node<std::pair<const IndexItem,UInt16>,std::_Default_allocator_traits<std::allocator<std::pair<const IndexItem,UInt16>>>::void_pointer>>,
              _Ty=std::pair<const IndexItem,UInt16>,
              _Objty=std::pair<const IndexItem,UInt16>
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory(732,82):
          查看对正在编译的函数 模板 实例化“std::pair<const IndexItem,UInt16>::pair<const int&,const int&,0>(_Other1,_Other2) noexcept”的引用
          with
          [
              _Other1=const int &,
              _Other2=const int &
          ]
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): error C2220: 以下警告被视为错误
  (编译源文件“/Action/ActionPlayer/ActionPlayerTalisman.cpp”)
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244: “初始化”: 从“_Ty”转换到“_Ty2”，可能丢失数据
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244:         with
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244:         [
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244:             _Ty=int
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244:         ]
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244:         and
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244:         [
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244:             _Ty2=UInt16
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244:         ]
  (编译源文件“/Action/ActionPlayer/ActionPlayerTalisman.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54):
      模板实例化上下文(最早的实例化上下文)为
          F:\Release_Branch\Server\Development\Backend\Zone\Action\ActionPlayer\ActionPlayerTalisman.cpp(1945,17):
          查看对正在编译的函数 模板 实例化“std::pair<std::_Tree_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<const IndexItem,UInt16>>>>,bool> std::_Tree<std::_Tmap_traits<_Kty,_Ty,_Pr,_Alloc,false>>::emplace<const IndexItem&,Int32>(const IndexItem &,Int32 &&)”的引用
          with
          [
              _Kty=IndexItem,
              _Ty=UInt16,
              _Pr=std::less<int>,
              _Alloc=std::allocator<std::pair<const IndexItem,UInt16>>
          ]
              F:\Release_Branch\Server\Development\Backend\Zone\Action\ActionPlayer\ActionPlayerTalisman.cpp(1945,24):
              请参阅 "mu2::ActionPlayerTalisman::checkTalismanCore::<lambda_db62a7ebadf6859db82c9e198d0af964>::operator ()" 中对 "std::_Tree<std::_Tmap_traits<_Kty,_Ty,_Pr,_Alloc,false>>::emplace" 的第一个引用
          with
          [
              _Kty=IndexItem,
              _Ty=UInt16,
              _Pr=std::less<int>,
              _Alloc=std::allocator<std::pair<const IndexItem,UInt16>>
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree(1048,30):
          查看对正在编译的函数 模板 实例化“std::pair<std::_Tree_node<std::pair<const IndexItem,UInt16>,std::_Default_allocator_traits<_Alloc>::void_pointer> *,bool> std::_Tree<std::_Tmap_traits<_Kty,_Ty,_Pr,_Alloc,false>>::_Emplace<const int&,int>(const int &,int &&)”的引用
          with
          [
              _Alloc=std::allocator<std::pair<const IndexItem,UInt16>>,
              _Kty=IndexItem,
              _Ty=UInt16,
              _Pr=std::less<int>
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree(1023,49):
          查看对正在编译的函数 模板 实例化“std::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<const IndexItem,UInt16>,std::_Default_allocator_traits<_Alloc>::void_pointer>>>::_Tree_temp_node<const int&,_Ty>(_Alnode &,std::_Tree_node<std::pair<const IndexItem,UInt16>,std::_Default_allocator_traits<_Alloc>::void_pointer> *,const int &,_Ty &&)”的引用
          with
          [
              _Alloc=std::allocator<std::pair<const IndexItem,UInt16>>,
              _Ty=int,
              _Alnode=std::allocator<std::_Tree_node<std::pair<const IndexItem,UInt16>,std::_Default_allocator_traits<std::allocator<std::pair<const IndexItem,UInt16>>>::void_pointer>>
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree(830,25):
          查看对正在编译的函数 模板 实例化“void std::_Default_allocator_traits<_Alloc>::construct<_Ty,const int&,int>(_Alloc &,_Objty *const ,const int &,int &&)”的引用
          with
          [
              _Alloc=std::allocator<std::_Tree_node<std::pair<const IndexItem,UInt16>,std::_Default_allocator_traits<std::allocator<std::pair<const IndexItem,UInt16>>>::void_pointer>>,
              _Ty=std::pair<const IndexItem,UInt16>,
              _Objty=std::pair<const IndexItem,UInt16>
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory(732,82):
          查看对正在编译的函数 模板 实例化“std::pair<const IndexItem,UInt16>::pair<const int&,_Ty,0>(_Other1,_Other2 &&) noexcept”的引用
          with
          [
              _Ty=int,
              _Other1=const int &,
              _Other2=int
          ]
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): error C2220: 以下警告被视为错误
  (编译源文件“/Action/ActionPlayerInventory.cpp”)
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316: “std::_Ref_count_obj2<_Ty>”: 在堆上分配的对象可能不是对齐 32
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316:         with
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316:         [
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316:             _Ty=mu2::InvenEquip
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316:         ]
  (编译源文件“/Action/ActionPlayerInventory.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46):
      模板实例化上下文(最早的实例化上下文)为
          F:\Release_Branch\Server\Development\Backend\Zone\Action\ActionPlayerInventory.cpp(108,2):
          查看对正在编译的函数 模板 实例化“T *mu2::ActionPlayerInventory::CreateInven<mu2::InvenEquip>(const mu2::eInvenType &,const SlotSizeType,Bool,Bool)”的引用
          with
          [
              T=mu2::InvenEquip
          ]
          F:\Release_Branch\Server\Development\Backend\Zone\Action\ActionPlayerInventory.h(436,27):
          查看对正在编译的函数 模板 实例化“std::shared_ptr<T> std::make_shared<T,mu2::EntityPlayer&,const mu2::eInvenType&,const SlotSizeType&,Bool&,Bool&>(mu2::EntityPlayer &,const mu2::eInvenType &,const SlotSizeType &,Bool &,Bool &)”的引用
          with
          [
              T=mu2::InvenEquip
          ]
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316: “std::_Ref_count_obj2<_Ty>”: 在堆上分配的对象可能不是对齐 32
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316:         with
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316:         [
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316:             _Ty=mu2::InvenDefaultBag
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316:         ]
  (编译源文件“/Action/ActionPlayerInventory.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46):
      模板实例化上下文(最早的实例化上下文)为
          F:\Release_Branch\Server\Development\Backend\Zone\Action\ActionPlayerInventory.cpp(109,2):
          查看对正在编译的函数 模板 实例化“T *mu2::ActionPlayerInventory::CreateInven<mu2::InvenDefaultBag>(const mu2::eInvenType &,const SlotSizeType,Bool,Bool)”的引用
          with
          [
              T=mu2::InvenDefaultBag
          ]
          F:\Release_Branch\Server\Development\Backend\Zone\Action\ActionPlayerInventory.h(436,27):
          查看对正在编译的函数 模板 实例化“std::shared_ptr<T> std::make_shared<T,mu2::EntityPlayer&,const mu2::eInvenType&,const SlotSizeType&,Bool&,Bool&>(mu2::EntityPlayer &,const mu2::eInvenType &,const SlotSizeType &,Bool &,Bool &)”的引用
          with
          [
              T=mu2::InvenDefaultBag
          ]
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316: “std::_Ref_count_obj2<_Ty>”: 在堆上分配的对象可能不是对齐 32
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316:         with
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316:         [
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316:             _Ty=mu2::InvenPlatinum
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316:         ]
  (编译源文件“/Action/ActionPlayerInventory.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46):
      模板实例化上下文(最早的实例化上下文)为
          F:\Release_Branch\Server\Development\Backend\Zone\Action\ActionPlayerInventory.cpp(112,2):
          查看对正在编译的函数 模板 实例化“T *mu2::ActionPlayerInventory::CreateInven<mu2::InvenPlatinum>(const mu2::eInvenType &,const SlotSizeType,Bool,Bool)”的引用
          with
          [
              T=mu2::InvenPlatinum
          ]
          F:\Release_Branch\Server\Development\Backend\Zone\Action\ActionPlayerInventory.h(436,27):
          查看对正在编译的函数 模板 实例化“std::shared_ptr<T> std::make_shared<T,mu2::EntityPlayer&,const mu2::eInvenType&,const SlotSizeType&,Bool&,Bool&>(mu2::EntityPlayer &,const mu2::eInvenType &,const SlotSizeType &,Bool &,Bool &)”的引用
          with
          [
              T=mu2::InvenPlatinum
          ]
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316: “std::_Ref_count_obj2<_Ty>”: 在堆上分配的对象可能不是对齐 32
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316:         with
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316:         [
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316:             _Ty=mu2::InvenPremium
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316:         ]
  (编译源文件“/Action/ActionPlayerInventory.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46):
      模板实例化上下文(最早的实例化上下文)为
          F:\Release_Branch\Server\Development\Backend\Zone\Action\ActionPlayerInventory.cpp(117,3):
          查看对正在编译的函数 模板 实例化“T *mu2::ActionPlayerInventory::CreateInven<mu2::InvenPremium>(const mu2::eInvenType &,const SlotSizeType,Bool,Bool)”的引用
          with
          [
              T=mu2::InvenPremium
          ]
          F:\Release_Branch\Server\Development\Backend\Zone\Action\ActionPlayerInventory.h(436,27):
          查看对正在编译的函数 模板 实例化“std::shared_ptr<T> std::make_shared<T,mu2::EntityPlayer&,const mu2::eInvenType&,const SlotSizeType&,Bool&,Bool&>(mu2::EntityPlayer &,const mu2::eInvenType &,const SlotSizeType &,Bool &,Bool &)”的引用
          with
          [
              T=mu2::InvenPremium
          ]
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316: “std::_Ref_count_obj2<_Ty>”: 在堆上分配的对象可能不是对齐 32
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316:         with
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316:         [
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316:             _Ty=mu2::InvenStoragePrivate
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316:         ]
  (编译源文件“/Action/ActionPlayerInventory.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46):
      模板实例化上下文(最早的实例化上下文)为
          F:\Release_Branch\Server\Development\Backend\Zone\Action\ActionPlayerInventory.cpp(122,3):
          查看对正在编译的函数 模板 实例化“T *mu2::ActionPlayerInventory::CreateInven<mu2::InvenStoragePrivate>(const mu2::eInvenType &,const SlotSizeType,Bool,Bool)”的引用
          with
          [
              T=mu2::InvenStoragePrivate
          ]
          F:\Release_Branch\Server\Development\Backend\Zone\Action\ActionPlayerInventory.h(436,27):
          查看对正在编译的函数 模板 实例化“std::shared_ptr<T> std::make_shared<T,mu2::EntityPlayer&,const mu2::eInvenType&,const SlotSizeType&,Bool&,Bool&>(mu2::EntityPlayer &,const mu2::eInvenType &,const SlotSizeType &,Bool &,Bool &)”的引用
          with
          [
              T=mu2::InvenStoragePrivate
          ]
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316: “std::_Ref_count_obj2<_Ty>”: 在堆上分配的对象可能不是对齐 32
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316:         with
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316:         [
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316:             _Ty=mu2::InvenReBuy
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316:         ]
  (编译源文件“/Action/ActionPlayerInventory.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46):
      模板实例化上下文(最早的实例化上下文)为
          F:\Release_Branch\Server\Development\Backend\Zone\Action\ActionPlayerInventory.cpp(129,2):
          查看对正在编译的函数 模板 实例化“T *mu2::ActionPlayerInventory::CreateInven<mu2::InvenReBuy>(const mu2::eInvenType &,const SlotSizeType,Bool,Bool)”的引用
          with
          [
              T=mu2::InvenReBuy
          ]
          F:\Release_Branch\Server\Development\Backend\Zone\Action\ActionPlayerInventory.h(436,27):
          查看对正在编译的函数 模板 实例化“std::shared_ptr<T> std::make_shared<T,mu2::EntityPlayer&,const mu2::eInvenType&,const SlotSizeType&,Bool&,Bool&>(mu2::EntityPlayer &,const mu2::eInvenType &,const SlotSizeType &,Bool &,Bool &)”的引用
          with
          [
              T=mu2::InvenReBuy
          ]
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316: “std::_Ref_count_obj2<_Ty>”: 在堆上分配的对象可能不是对齐 32
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316:         with
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316:         [
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316:             _Ty=mu2::InvenMail
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46): warning C4316:         ]
  (编译源文件“/Action/ActionPlayerInventory.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory(2913,46):
      模板实例化上下文(最早的实例化上下文)为
          F:\Release_Branch\Server\Development\Backend\Zone\Action\ActionPlayerInventory.cpp(130,2):
          查看对正在编译的函数 模板 实例化“T *mu2::ActionPlayerInventory::CreateInven<mu2::InvenMail>(const mu2::eInvenType &,const SlotSizeType,Bool,Bool)”的引用
          with
          [
              T=mu2::InvenMail
          ]
          F:\Release_Branch\Server\Development\Backend\Zone\Action\ActionPlayerInventory.h(436,27):
          查看对正在编译的函数 模板 实例化“std::shared_ptr<T> std::make_shared<T,mu2::EntityPlayer&,const mu2::eInvenType&,const SlotSizeType&,Bool&,Bool&>(mu2::EntityPlayer &,const mu2::eInvenType &,const SlotSizeType &,Bool &,Bool &)”的引用
          with
          [
              T=mu2::InvenMail
          ]
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18): error C2220: 以下警告被视为错误
  (编译源文件“/System/GMCommandSystem.cpp”)
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18): warning C4244: “=”: 从“wchar_t”转换到“char”，可能丢失数据
  (编译源文件“/System/GMCommandSystem.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility(4813,18):
      模板实例化上下文(最早的实例化上下文)为
          F:\Release_Branch\Server\Development\Backend\Zone\System\GMCommandSystem.cpp(3163,9):
          查看对正在编译的函数 模板 实例化“std::basic_string<char,std::char_traits<char>,std::allocator<char>> &std::basic_string<char,std::char_traits<char>,std::allocator<char>>::assign<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,0>(const _Iter,const _Iter)”的引用
          with
          [
              _Elem=wchar_t,
              _Iter=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>
          ]
              F:\Release_Branch\Server\Development\Backend\Zone\System\GMCommandSystem.cpp(3163,15):
              请参阅 "mu2::GMCommandSystem::onSectorLuaScriptSetValue" 中对 "std::basic_string<char,std::char_traits<char>,std::allocator<char>>::assign" 的第一个引用
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(1669,32):
          查看对正在编译的函数 模板 实例化“std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<wchar_t*,0>(_Iter,_Iter,const _Alloc &)”的引用
          with
          [
              _Iter=wchar_t *,
              _Alloc=std::allocator<char>
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(812,17):
          查看对正在编译的函数 模板 实例化“void std::basic_string<char,std::char_traits<char>,std::allocator<char>>::_Construct_from_iter<wchar_t*,wchar_t*,_Size_type>(_Iter,const _Sent,_Size)”的引用
          with
          [
              _Size_type=unsigned __int64,
              _Iter=wchar_t *,
              _Sent=wchar_t *,
              _Size=unsigned __int64
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(968,18):
          查看对正在编译的函数 模板 实例化“_OutIt *std::_Copy_n_unchecked4<wchar_t*,_Size,char*>(_InIt,_SizeTy,_OutIt)”的引用
          with
          [
              _OutIt=char *,
              _Size=unsigned __int64,
              _InIt=wchar_t *,
              _SizeTy=unsigned __int64
          ]
  
