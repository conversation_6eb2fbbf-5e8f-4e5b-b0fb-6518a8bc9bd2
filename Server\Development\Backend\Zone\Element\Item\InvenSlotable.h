﻿#pragma once

#include "InvenSlot.h"

namespace mu2
{
	//====================================================================================================
	//
	//====================================================================================================

	class InvenSlotable : public Inven
	{
	public:
		explicit InvenSlotable(EntityPlayer& owner, eInvenType eType, const SlotSizeType maxSlot, Bool isActiveAdd, Bool isActiveSub) 
			: Inven(owner, eType, maxSlot, isActiveAdd, isActiveSub) 
		{
		}
		virtual ~InvenSlotable() override {}

	public:
		virtual Bool				IsSlotBags() const override { return true; }
		virtual void				Initialize() override;
		virtual Bool				IsPushable(const eSlotType& toSlot, const ItemConstPtr& item, Bool isAllowBlockNExpired = false) const override;
		virtual ItemPtr				GetItemByPlace(const ItemPlace& place) const override;
		virtual Bool				IsFreeSlot(const eSlotType& slot) const override;

	protected:		
		virtual ErrorItem::Error	setUpItemFromProcedure(const ItemPlace toPlace, const ItemPtr& item, ProcedureType::Enum eProcType) override;
		virtual void				setItemPos(const ItemPlace toPlace, const ItemPtr& pushableItem) override;
		virtual void				OnNotifyPushed(const ItemPtr& item) override;
		virtual void				OnNotifyPoped(const ItemPtr& item) override;
		virtual void				fillUpWithPos(const eSlotType& slot, ItemData& data) override;
		virtual Bool				IsFixableItem(const ItemData& itemData) const;
	};

	//====================================================================================================
	//
	//====================================================================================================

	class InvenStorage : public InvenSlotable
	{
	public:
		explicit InvenStorage(EntityPlayer& owner, eInvenType eType, const SlotSizeType maxSlot, Bool isActiveAdd, Bool isActiveSub) 
			: InvenSlotable(owner, eType, maxSlot, isActiveAdd, isActiveSub)
		{
			m_direction[SOURCE] = true;
			m_direction[TARGET] = true;
		}
		virtual ~InvenStorage() override {}
		virtual Bool IsStorage() const { return true; }
	};

	//====================================================================================================
	//
	//====================================================================================================

	class InvenBasicBag : public InvenSlotable
	{
	public:
		explicit InvenBasicBag(EntityPlayer& owner, eInvenType eType, const SlotSizeType maxSlot, Bool isActiveAdd, Bool isActiveSub) 
			: InvenSlotable(owner, eType, maxSlot, isActiveAdd, isActiveSub)
		{
			m_direction[SOURCE] = true;
			m_direction[TARGET] = true;

			m_emergencyRange = InvenBags;
		}
		virtual ~InvenBasicBag() override {}

		virtual Bool IsInvenBags() const { return true; }
		virtual Bool IsPushable(const eSlotType& toSlot, const ItemConstPtr& item, Bool isAllowBlockNExpired = false) const override;
	};

	//====================================================================================================
	//
	//====================================================================================================

	class alignas(32) InvenDefaultBag : public InvenBasicBag
	{
	public:
		explicit InvenDefaultBag(EntityPlayer& owner, eInvenType eType, const SlotSizeType maxSlot, Bool isActiveAdd, Bool isActiveSub)
			: InvenBasicBag(owner, eType, maxSlot, isActiveAdd, isActiveSub)
		{
		}
		virtual ~InvenDefaultBag() override {}
		virtual Bool IsDefaultBag() const { return true; }
	};

	//====================================================================================================
	//
	//====================================================================================================

	class alignas(32) InvenPlatinum : public InvenBasicBag
	{
	public:
		explicit InvenPlatinum(EntityPlayer& owner, eInvenType eType, const SlotSizeType maxSlot, Bool isActiveAdd, Bool isActiveSub)
			: InvenBasicBag(owner, eType, maxSlot, isActiveAdd, isActiveSub)
		{
		}
		virtual ~InvenPlatinum() override {}
		virtual Bool IsPlatinumBag() const override { return true; }
	};

	//====================================================================================================
	//
	//====================================================================================================

	class alignas(32) InvenPremium : public InvenSlotable
	{
	public:
		explicit InvenPremium(EntityPlayer& owner, eInvenType eType, const SlotSizeType maxSlot, Bool isActiveAdd, Bool isActiveSub)
			: InvenSlotable(owner, eType, maxSlot, isActiveAdd, isActiveSub)
		{
			m_direction[SOURCE] = true;
			m_direction[TARGET] = true;

			m_emergencyRange = PremiumBags;
		}
		virtual ~InvenPremium() override {}

		virtual Bool		IsPremiumBag() const override { return true; }
		virtual Bool		IsPushable(const eSlotType& toSlot, const ItemConstPtr& item, Bool isAllowBlockNExpired = false) const override;
	};

	//====================================================================================================
	//
	//====================================================================================================
		
	//class InvenQuest : public InvenSlotable
	//{
	//public:
	//	explicit InvenQuest(EntityPlayer& owner, eInvenType eType, const SlotSizeType maxSlot, Bool isActiveAdd, Bool isActiveSub) 
	//		: InvenSlotable(owner, eType, maxSlot, isActiveAdd, isActiveSub)
	//	{
	//		m_direction[SOURCE] = true;
	//		m_direction[TARGET] = true;

	//		m_emergencyRange = QuestBags;
	//	}
	//	virtual ~InvenQuest() override {}
	//	virtual Bool IsQuestBag() const override { return true; }
	//};

}