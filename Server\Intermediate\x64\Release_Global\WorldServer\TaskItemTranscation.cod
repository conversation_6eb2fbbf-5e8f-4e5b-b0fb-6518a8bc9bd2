; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	??0exception@std@@QEAA@AEBV01@@Z		; std::exception::exception
PUBLIC	?what@exception@std@@UEBAPEBDXZ			; std::exception::what
PUBLIC	??_Gexception@std@@UEAAPEAXI@Z			; std::exception::`scalar deleting destructor'
PUBLIC	??0bad_alloc@std@@QEAA@AEBV01@@Z		; std::bad_alloc::bad_alloc
PUBLIC	??_Gbad_alloc@std@@UEAAPEAXI@Z			; std::bad_alloc::`scalar deleting destructor'
PUBL<PERSON>	??0bad_array_new_length@std@@QEAA@XZ		; std::bad_array_new_length::bad_array_new_length
PUBLIC	??1bad_array_new_length@std@@UEAA@XZ		; std::bad_array_new_length::~bad_array_new_length
PUBLIC	??0bad_array_new_length@std@@QEAA@AEBV01@@Z	; std::bad_array_new_length::bad_array_new_length
PUBLIC	??_Gbad_array_new_length@std@@UEAAPEAXI@Z	; std::bad_array_new_length::`scalar deleting destructor'
PUBLIC	?_Throw_bad_array_new_length@std@@YAXXZ		; std::_Throw_bad_array_new_length
PUBLIC	?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
PUBLIC	?_Xlen_string@std@@YAXXZ			; std::_Xlen_string
PUBLIC	?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z	; std::allocator<wchar_t>::allocate
PUBLIC	?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEAAPEA_WXZ ; std::_String_val<std::_Simple_types<wchar_t> >::_Myptr
PUBLIC	??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV01@AEBV01@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::operator=
PUBLIC	?assign@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV12@QEB_W_K@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::assign
PUBLIC	?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
PUBLIC	?_Calculate_growth@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@CA_K_K00@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Calculate_growth
PUBLIC	?_Decref@_Ref_count_base@std@@QEAAXXZ		; std::_Ref_count_base::_Decref
PUBLIC	?_Get_deleter@_Ref_count_base@std@@UEBAPEAXAEBVtype_info@@@Z ; std::_Ref_count_base::_Get_deleter
PUBLIC	?Write@PacketStream@mu2@@QEAAIPEBEI@Z		; mu2::PacketStream::Write
PUBLIC	?Read@PacketStream@mu2@@QEAAIPEAEI@Z		; mu2::PacketStream::Read
PUBLIC	??$rw@_J@PacketStream@mu2@@QEAA_NAEA_J@Z	; mu2::PacketStream::rw<__int64>
PUBLIC	?checkingBuffer@PacketStream@mu2@@AEAA_NXZ	; mu2::PacketStream::checkingBuffer
PUBLIC	??$swapEndian@_J@PacketStream@mu2@@AEAA_JAEB_J@Z ; mu2::PacketStream::swapEndian<__int64>
PUBLIC	??_GISerializer@mu2@@UEAAPEAXI@Z		; mu2::ISerializer::`scalar deleting destructor'
PUBLIC	??0DateTimeEx@mu2@@QEAA@XZ			; mu2::DateTimeEx::DateTimeEx
PUBLIC	??4DateTimeEx@mu2@@QEAAAEAU01@AEB_J@Z		; mu2::DateTimeEx::operator=
PUBLIC	??BDateTimeEx@mu2@@QEBA?B_JXZ			; mu2::DateTimeEx::operator __int64 const 
PUBLIC	?PackUnpack@DateTimeEx@mu2@@UEAA_NAEAVPacketStream@2@@Z ; mu2::DateTimeEx::PackUnpack
PUBLIC	?Reset@DateTimeEx@mu2@@UEAAXXZ			; mu2::DateTimeEx::Reset
PUBLIC	??_GDateTimeEx@mu2@@UEAAPEAXI@Z			; mu2::DateTimeEx::`scalar deleting destructor'
PUBLIC	?Reset@ChangedMoney@mu2@@UEAAXXZ		; mu2::ChangedMoney::Reset
PUBLIC	?PackUnpack@ChangedMoney@mu2@@UEAA_NAEAVPacketStream@2@@Z ; mu2::ChangedMoney::PackUnpack
PUBLIC	??1ChangedMoney@mu2@@UEAA@XZ			; mu2::ChangedMoney::~ChangedMoney
PUBLIC	??_GChangedMoney@mu2@@UEAAPEAXI@Z		; mu2::ChangedMoney::`scalar deleting destructor'
PUBLIC	??$em@W4CalcType@ChangedMoney@mu2@@@PacketStream@mu2@@QEAA_NAEAW4CalcType@ChangedMoney@1@@Z ; mu2::PacketStream::em<enum mu2::ChangedMoney::CalcType>
PUBLIC	??$em@W4MoneyType@ChangedMoney@mu2@@@PacketStream@mu2@@QEAA_NAEAW4MoneyType@ChangedMoney@1@@Z ; mu2::PacketStream::em<enum mu2::ChangedMoney::MoneyType>
PUBLIC	??4WShopDbUser@mu2@@QEAAAEAU01@AEBU01@@Z	; mu2::WShopDbUser::operator=
PUBLIC	??1?$SmartPtrEx@UEvent@mu2@@@mu2@@QEAA@XZ	; mu2::SmartPtrEx<mu2::Event>::~SmartPtrEx<mu2::Event>
PUBLIC	?_Swap@?$_Ptr_base@UEvent@mu2@@@std@@IEAAXAEAV12@@Z ; std::_Ptr_base<mu2::Event>::_Swap
PUBLIC	??4?$shared_ptr@UEvent@mu2@@@std@@QEAAAEAV01@AEBV01@@Z ; std::shared_ptr<mu2::Event>::operator=
PUBLIC	?fillItemResult@WShopTask@mu2@@MEAAXXZ		; mu2::WShopTask::fillItemResult
PUBLIC	??$swapEndian@W4CalcType@ChangedMoney@mu2@@@PacketStream@mu2@@AEAA?AW4CalcType@ChangedMoney@1@AEBW4231@@Z ; mu2::PacketStream::swapEndian<enum mu2::ChangedMoney::CalcType>
PUBLIC	??$swapEndian@W4MoneyType@ChangedMoney@mu2@@@PacketStream@mu2@@AEAA?AW4MoneyType@ChangedMoney@1@AEBW4231@@Z ; mu2::PacketStream::swapEndian<enum mu2::ChangedMoney::MoneyType>
PUBLIC	??$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z ; std::shared_ptr<mu2::Event>::shared_ptr<mu2::Event><mu2::Event,0>
PUBLIC	??1?$_Temporary_owner@UEvent@mu2@@@std@@QEAA@XZ	; std::_Temporary_owner<mu2::Event>::~_Temporary_owner<mu2::Event>
PUBLIC	?_Destroy@?$_Ref_count@UEvent@mu2@@@std@@EEAAXXZ ; std::_Ref_count<mu2::Event>::_Destroy
PUBLIC	?_Delete_this@?$_Ref_count@UEvent@mu2@@@std@@EEAAXXZ ; std::_Ref_count<mu2::Event>::_Delete_this
PUBLIC	??_G?$_Ref_count@UEvent@mu2@@@std@@UEAAPEAXI@Z	; std::_Ref_count<mu2::Event>::`scalar deleting destructor'
PUBLIC	??$_Reallocate_for@V<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Reallocate_for<<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>,wchar_t const *>
PUBLIC	??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
PUBLIC	?Begin@BaseTrans@mu2@@UEAA?AW4Error@ErrorItem@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ; mu2::BaseTrans::Begin
PUBLIC	?Commit@BaseTrans@mu2@@UEAA?AW4Error@ErrorItem@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ; mu2::BaseTrans::Commit
PUBLIC	?RollBack@BaseTrans@mu2@@UEAA?AW4Error@ErrorItem@2@XZ ; mu2::BaseTrans::RollBack
PUBLIC	??_GCharacterSlotExtendTrans@mu2@@UEAAPEAXI@Z	; mu2::CharacterSlotExtendTrans::`scalar deleting destructor'
PUBLIC	??0KnightageBaseTrans@mu2@@QEAA@W4ID@BaseTrans@1@IAEAUChangedMoney@1@HH@Z ; mu2::KnightageBaseTrans::KnightageBaseTrans
PUBLIC	??_GKnightageBaseTrans@mu2@@UEAAPEAXI@Z		; mu2::KnightageBaseTrans::`scalar deleting destructor'
PUBLIC	??_GKnightageGiveTrophyTrans@mu2@@UEAAPEAXI@Z	; mu2::KnightageGiveTrophyTrans::`scalar deleting destructor'
PUBLIC	??_GKnightageFollowerTrans@mu2@@UEAAPEAXI@Z	; mu2::KnightageFollowerTrans::`scalar deleting destructor'
PUBLIC	??0TaskItemTranscation@mu2@@QEAA@PEAVWShopJob@1@@Z ; mu2::TaskItemTranscation::TaskItemTranscation
PUBLIC	??1TaskItemTranscation@mu2@@UEAA@XZ		; mu2::TaskItemTranscation::~TaskItemTranscation
PUBLIC	?Response@TaskItemTranscation@mu2@@UEAA?AW4Error@ErrorWShop@2@PEAVIResponseParent@fcsa@@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ; mu2::TaskItemTranscation::Response
PUBLIC	?onResDbWShopItemTranscation@TaskItemTranscation@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z ; mu2::TaskItemTranscation::onResDbWShopItemTranscation
PUBLIC	?Setup@TaskItemTranscation@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z ; mu2::TaskItemTranscation::Setup
PUBLIC	?createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ; mu2::TaskItemTranscation::createTrans
PUBLIC	?sendBeginRequest@TaskItemTranscation@mu2@@EEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ; mu2::TaskItemTranscation::sendBeginRequest
PUBLIC	?sendDbRequest@TaskItemTranscation@mu2@@AEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ; mu2::TaskItemTranscation::sendDbRequest
PUBLIC	?_Swap@?$_Ptr_base@VBaseTrans@mu2@@@std@@IEAAXAEAV12@@Z ; std::_Ptr_base<mu2::BaseTrans>::_Swap
PUBLIC	??4?$shared_ptr@VBaseTrans@mu2@@@std@@QEAAAEAV01@$$QEAV01@@Z ; std::shared_ptr<mu2::BaseTrans>::operator=
PUBLIC	??_GTaskItemTranscation@mu2@@UEAAPEAXI@Z	; mu2::TaskItemTranscation::`scalar deleting destructor'
PUBLIC	??$?0VKnightageFollowerTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageFollowerTrans@mu2@@@Z ; std::shared_ptr<mu2::BaseTrans>::shared_ptr<mu2::BaseTrans><mu2::KnightageFollowerTrans,0>
PUBLIC	??$?0VKnightageGiveTrophyTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageGiveTrophyTrans@mu2@@@Z ; std::shared_ptr<mu2::BaseTrans>::shared_ptr<mu2::BaseTrans><mu2::KnightageGiveTrophyTrans,0>
PUBLIC	??$?0VCharacterSlotExtendTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVCharacterSlotExtendTrans@mu2@@@Z ; std::shared_ptr<mu2::BaseTrans>::shared_ptr<mu2::BaseTrans><mu2::CharacterSlotExtendTrans,0>
PUBLIC	??1?$_Temporary_owner@VKnightageFollowerTrans@mu2@@@std@@QEAA@XZ ; std::_Temporary_owner<mu2::KnightageFollowerTrans>::~_Temporary_owner<mu2::KnightageFollowerTrans>
PUBLIC	?_Destroy@?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@EEAAXXZ ; std::_Ref_count<mu2::KnightageFollowerTrans>::_Destroy
PUBLIC	?_Delete_this@?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@EEAAXXZ ; std::_Ref_count<mu2::KnightageFollowerTrans>::_Delete_this
PUBLIC	??_G?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@UEAAPEAXI@Z ; std::_Ref_count<mu2::KnightageFollowerTrans>::`scalar deleting destructor'
PUBLIC	??1?$_Temporary_owner@VKnightageGiveTrophyTrans@mu2@@@std@@QEAA@XZ ; std::_Temporary_owner<mu2::KnightageGiveTrophyTrans>::~_Temporary_owner<mu2::KnightageGiveTrophyTrans>
PUBLIC	?_Destroy@?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@EEAAXXZ ; std::_Ref_count<mu2::KnightageGiveTrophyTrans>::_Destroy
PUBLIC	?_Delete_this@?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@EEAAXXZ ; std::_Ref_count<mu2::KnightageGiveTrophyTrans>::_Delete_this
PUBLIC	??_G?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@UEAAPEAXI@Z ; std::_Ref_count<mu2::KnightageGiveTrophyTrans>::`scalar deleting destructor'
PUBLIC	??1?$_Temporary_owner@VCharacterSlotExtendTrans@mu2@@@std@@QEAA@XZ ; std::_Temporary_owner<mu2::CharacterSlotExtendTrans>::~_Temporary_owner<mu2::CharacterSlotExtendTrans>
PUBLIC	?_Destroy@?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@EEAAXXZ ; std::_Ref_count<mu2::CharacterSlotExtendTrans>::_Destroy
PUBLIC	?_Delete_this@?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@EEAAXXZ ; std::_Ref_count<mu2::CharacterSlotExtendTrans>::_Delete_this
PUBLIC	??_G?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@UEAAPEAXI@Z ; std::_Ref_count<mu2::CharacterSlotExtendTrans>::`scalar deleting destructor'
PUBLIC	??_7exception@std@@6B@				; std::exception::`vftable'
PUBLIC	??_C@_0BC@EOODALEL@Unknown?5exception@		; `string'
PUBLIC	??_7bad_alloc@std@@6B@				; std::bad_alloc::`vftable'
PUBLIC	??_7bad_array_new_length@std@@6B@		; std::bad_array_new_length::`vftable'
PUBLIC	??_C@_0BF@KINCDENJ@bad?5array?5new?5length@	; `string'
PUBLIC	??_R0?AVexception@std@@@8			; std::exception `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
PUBLIC	_TI3?AVbad_array_new_length@std@@
PUBLIC	_CTA3?AVbad_array_new_length@std@@
PUBLIC	??_R0?AVbad_array_new_length@std@@@8		; std::bad_array_new_length `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
PUBLIC	??_R0?AVbad_alloc@std@@@8			; std::bad_alloc `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
PUBLIC	??_C@_0BA@JFNIOLAK@string?5too?5long@		; `string'
PUBLIC	??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@ ; `string'
PUBLIC	??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@		; `string'
PUBLIC	??_7ISerializer@mu2@@6B@			; mu2::ISerializer::`vftable'
PUBLIC	??_C@_0EJ@IFEHPKPB@F?3?2Release_Branch?2Server?2Develo@ ; `string'
PUBLIC	??_C@_0BB@HAIDOJLN@checkingBuffer?$CI?$CJ@	; `string'
PUBLIC	??_C@_0BJ@NEGBBJOE@mu2?3?3PacketStream?3?3Write@ ; `string'
PUBLIC	??_C@_05IOMEMJEC@count@				; `string'
PUBLIC	??_C@_0BI@KGAODAAL@mu2?3?3PacketStream?3?3Read@	; `string'
PUBLIC	??_C@_03COAJHJPB@buf@				; `string'
PUBLIC	??_7DateTimeEx@mu2@@6B@				; mu2::DateTimeEx::`vftable'
PUBLIC	??_7ChangedMoney@mu2@@6B@			; mu2::ChangedMoney::`vftable'
PUBLIC	??_7?$_Ref_count@UEvent@mu2@@@std@@6B@		; std::_Ref_count<mu2::Event>::`vftable'
PUBLIC	??_R4exception@std@@6B@				; std::exception::`RTTI Complete Object Locator'
PUBLIC	??_R3exception@std@@8				; std::exception::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2exception@std@@8				; std::exception::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@exception@std@@8			; std::exception::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4bad_array_new_length@std@@6B@		; std::bad_array_new_length::`RTTI Complete Object Locator'
PUBLIC	??_R3bad_array_new_length@std@@8		; std::bad_array_new_length::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2bad_array_new_length@std@@8		; std::bad_array_new_length::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@bad_array_new_length@std@@8	; std::bad_array_new_length::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@bad_alloc@std@@8			; std::bad_alloc::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R3bad_alloc@std@@8				; std::bad_alloc::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2bad_alloc@std@@8				; std::bad_alloc::`RTTI Base Class Array'
PUBLIC	??_R4bad_alloc@std@@6B@				; std::bad_alloc::`RTTI Complete Object Locator'
PUBLIC	??_R0?AV_Ref_count_base@std@@@8			; std::_Ref_count_base `RTTI Type Descriptor'
PUBLIC	??_R3_Ref_count_base@std@@8			; std::_Ref_count_base::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2_Ref_count_base@std@@8			; std::_Ref_count_base::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@_Ref_count_base@std@@8		; std::_Ref_count_base::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4ISerializer@mu2@@6B@			; mu2::ISerializer::`RTTI Complete Object Locator'
PUBLIC	??_R0?AUISerializer@mu2@@@8			; mu2::ISerializer `RTTI Type Descriptor'
PUBLIC	??_R3ISerializer@mu2@@8				; mu2::ISerializer::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2ISerializer@mu2@@8				; mu2::ISerializer::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@ISerializer@mu2@@8		; mu2::ISerializer::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4DateTimeEx@mu2@@6B@			; mu2::DateTimeEx::`RTTI Complete Object Locator'
PUBLIC	??_R0?AUDateTimeEx@mu2@@@8			; mu2::DateTimeEx `RTTI Type Descriptor'
PUBLIC	??_R3DateTimeEx@mu2@@8				; mu2::DateTimeEx::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2DateTimeEx@mu2@@8				; mu2::DateTimeEx::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@DateTimeEx@mu2@@8			; mu2::DateTimeEx::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4ChangedMoney@mu2@@6B@			; mu2::ChangedMoney::`RTTI Complete Object Locator'
PUBLIC	??_R0?AUChangedMoney@mu2@@@8			; mu2::ChangedMoney `RTTI Type Descriptor'
PUBLIC	??_R3ChangedMoney@mu2@@8			; mu2::ChangedMoney::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2ChangedMoney@mu2@@8			; mu2::ChangedMoney::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@ChangedMoney@mu2@@8		; mu2::ChangedMoney::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4?$_Ref_count@UEvent@mu2@@@std@@6B@		; std::_Ref_count<mu2::Event>::`RTTI Complete Object Locator'
PUBLIC	??_R0?AV?$_Ref_count@UEvent@mu2@@@std@@@8	; std::_Ref_count<mu2::Event> `RTTI Type Descriptor'
PUBLIC	??_R3?$_Ref_count@UEvent@mu2@@@std@@8		; std::_Ref_count<mu2::Event>::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2?$_Ref_count@UEvent@mu2@@@std@@8		; std::_Ref_count<mu2::Event>::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@?$_Ref_count@UEvent@mu2@@@std@@8	; std::_Ref_count<mu2::Event>::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_7BaseTrans@mu2@@6B@				; mu2::BaseTrans::`vftable'
PUBLIC	??_7CharacterSlotExtendTrans@mu2@@6B@		; mu2::CharacterSlotExtendTrans::`vftable'
PUBLIC	??_7KnightageBaseTrans@mu2@@6B@			; mu2::KnightageBaseTrans::`vftable'
PUBLIC	??_7KnightageGiveTrophyTrans@mu2@@6B@		; mu2::KnightageGiveTrophyTrans::`vftable'
PUBLIC	??_7KnightageFollowerTrans@mu2@@6B@		; mu2::KnightageFollowerTrans::`vftable'
PUBLIC	??_7TaskItemTranscation@mu2@@6B@		; mu2::TaskItemTranscation::`vftable'
PUBLIC	??_C@_1EM@JHJBJGKM@?$AAW?$AAS?$AAH?$AAO?$AAP?$AA?$CI?$AA?$CF?$AAu?$AA?$CJ?$AA?0?$AA?$CF?$AAl?$AAl?$AAu?$AA?5@ ; `string'
PUBLIC	??_C@_0FI@OJCHCMJH@F?3?2Release_Branch?2Server?2Develo@ ; `string'
PUBLIC	??_C@_0CI@HOAIHMKD@mu2?3?3TaskItemTranscation?3?3sendD@ ; `string'
PUBLIC	??_C@_1GI@DJAOHGGM@?$AAW?$AAS?$AAH?$AAO?$AAP?$AA?$CI?$AA?$CF?$AAu?$AA?$CJ?$AA?0?$AA?$CF?$AAl?$AAl?$AAu?$AA?5@ ; `string'
PUBLIC	??_C@_0DG@HKGKMPPL@mu2?3?3TaskItemTranscation?3?3onRes@ ; `string'
PUBLIC	??_7?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@6B@ ; std::_Ref_count<mu2::KnightageFollowerTrans>::`vftable'
PUBLIC	??_7?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@6B@ ; std::_Ref_count<mu2::KnightageGiveTrophyTrans>::`vftable'
PUBLIC	??_7?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@6B@ ; std::_Ref_count<mu2::CharacterSlotExtendTrans>::`vftable'
PUBLIC	??_R4BaseTrans@mu2@@6B@				; mu2::BaseTrans::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVBaseTrans@mu2@@@8			; mu2::BaseTrans `RTTI Type Descriptor'
PUBLIC	??_R3BaseTrans@mu2@@8				; mu2::BaseTrans::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2BaseTrans@mu2@@8				; mu2::BaseTrans::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@BaseTrans@mu2@@8			; mu2::BaseTrans::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4CharacterSlotExtendTrans@mu2@@6B@		; mu2::CharacterSlotExtendTrans::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVCharacterSlotExtendTrans@mu2@@@8	; mu2::CharacterSlotExtendTrans `RTTI Type Descriptor'
PUBLIC	??_R3CharacterSlotExtendTrans@mu2@@8		; mu2::CharacterSlotExtendTrans::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2CharacterSlotExtendTrans@mu2@@8		; mu2::CharacterSlotExtendTrans::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@CharacterSlotExtendTrans@mu2@@8	; mu2::CharacterSlotExtendTrans::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4KnightageBaseTrans@mu2@@6B@		; mu2::KnightageBaseTrans::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVKnightageBaseTrans@mu2@@@8		; mu2::KnightageBaseTrans `RTTI Type Descriptor'
PUBLIC	??_R3KnightageBaseTrans@mu2@@8			; mu2::KnightageBaseTrans::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2KnightageBaseTrans@mu2@@8			; mu2::KnightageBaseTrans::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@KnightageBaseTrans@mu2@@8		; mu2::KnightageBaseTrans::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4KnightageGiveTrophyTrans@mu2@@6B@		; mu2::KnightageGiveTrophyTrans::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVKnightageGiveTrophyTrans@mu2@@@8	; mu2::KnightageGiveTrophyTrans `RTTI Type Descriptor'
PUBLIC	??_R3KnightageGiveTrophyTrans@mu2@@8		; mu2::KnightageGiveTrophyTrans::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2KnightageGiveTrophyTrans@mu2@@8		; mu2::KnightageGiveTrophyTrans::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@KnightageGiveTrophyTrans@mu2@@8	; mu2::KnightageGiveTrophyTrans::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4KnightageFollowerTrans@mu2@@6B@		; mu2::KnightageFollowerTrans::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVKnightageFollowerTrans@mu2@@@8		; mu2::KnightageFollowerTrans `RTTI Type Descriptor'
PUBLIC	??_R3KnightageFollowerTrans@mu2@@8		; mu2::KnightageFollowerTrans::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2KnightageFollowerTrans@mu2@@8		; mu2::KnightageFollowerTrans::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@KnightageFollowerTrans@mu2@@8	; mu2::KnightageFollowerTrans::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4TaskItemTranscation@mu2@@6B@		; mu2::TaskItemTranscation::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVTaskItemTranscation@mu2@@@8		; mu2::TaskItemTranscation `RTTI Type Descriptor'
PUBLIC	??_R3TaskItemTranscation@mu2@@8			; mu2::TaskItemTranscation::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2TaskItemTranscation@mu2@@8			; mu2::TaskItemTranscation::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@TaskItemTranscation@mu2@@8	; mu2::TaskItemTranscation::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@WShopTask@mu2@@8			; mu2::WShopTask::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R0?AVWShopTask@mu2@@@8			; mu2::WShopTask `RTTI Type Descriptor'
PUBLIC	??_R3WShopTask@mu2@@8				; mu2::WShopTask::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2WShopTask@mu2@@8				; mu2::WShopTask::`RTTI Base Class Array'
PUBLIC	??_R4?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@6B@ ; std::_Ref_count<mu2::KnightageFollowerTrans>::`RTTI Complete Object Locator'
PUBLIC	??_R0?AV?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@@8 ; std::_Ref_count<mu2::KnightageFollowerTrans> `RTTI Type Descriptor'
PUBLIC	??_R3?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@8 ; std::_Ref_count<mu2::KnightageFollowerTrans>::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@8 ; std::_Ref_count<mu2::KnightageFollowerTrans>::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@8 ; std::_Ref_count<mu2::KnightageFollowerTrans>::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@6B@ ; std::_Ref_count<mu2::KnightageGiveTrophyTrans>::`RTTI Complete Object Locator'
PUBLIC	??_R0?AV?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@@8 ; std::_Ref_count<mu2::KnightageGiveTrophyTrans> `RTTI Type Descriptor'
PUBLIC	??_R3?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@8 ; std::_Ref_count<mu2::KnightageGiveTrophyTrans>::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@8 ; std::_Ref_count<mu2::KnightageGiveTrophyTrans>::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@8 ; std::_Ref_count<mu2::KnightageGiveTrophyTrans>::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@6B@ ; std::_Ref_count<mu2::CharacterSlotExtendTrans>::`RTTI Complete Object Locator'
PUBLIC	??_R0?AV?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@@8 ; std::_Ref_count<mu2::CharacterSlotExtendTrans> `RTTI Type Descriptor'
PUBLIC	??_R3?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@8 ; std::_Ref_count<mu2::CharacterSlotExtendTrans>::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@8 ; std::_Ref_count<mu2::CharacterSlotExtendTrans>::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@8 ; std::_Ref_count<mu2::CharacterSlotExtendTrans>::`RTTI Base Class Descriptor at (0,-1,0,64)'
EXTRN	_purecall:PROC
EXTRN	??2@YAPEAX_K@Z:PROC				; operator new
EXTRN	??3@YAXPEAX_K@Z:PROC				; operator delete
EXTRN	__std_terminate:PROC
EXTRN	_invoke_watson:PROC
EXTRN	memcpy:PROC
EXTRN	memmove:PROC
EXTRN	__imp_GetStdHandle:PROC
EXTRN	__imp_GetLocalTime:PROC
EXTRN	__imp_SetConsoleTextAttribute:PROC
EXTRN	__std_exception_copy:PROC
EXTRN	__std_exception_destroy:PROC
EXTRN	??_Eexception@std@@UEAAPEAXI@Z:PROC		; std::exception::`vector deleting destructor'
EXTRN	??_Ebad_alloc@std@@UEAAPEAXI@Z:PROC		; std::bad_alloc::`vector deleting destructor'
EXTRN	??_Ebad_array_new_length@std@@UEAAPEAXI@Z:PROC	; std::bad_array_new_length::`vector deleting destructor'
EXTRN	?_Xlength_error@std@@YAXPEBD@Z:PROC		; std::_Xlength_error
EXTRN	?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ:PROC	; mu2::Logger::Logging
EXTRN	?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H0ZZ:PROC	; mu2::Logger::Logging
EXTRN	?isValid@PacketStream@mu2@@QEBA_NXZ:PROC	; mu2::PacketStream::isValid
EXTRN	?remains@PacketStream@mu2@@AEAAIXZ:PROC		; mu2::PacketStream::remains
EXTRN	?SetError@PacketStream@mu2@@AEAAXW4Error@ErrorNet@2@@Z:PROC ; mu2::PacketStream::SetError
EXTRN	??_EISerializer@mu2@@UEAAPEAXI@Z:PROC		; mu2::ISerializer::`vector deleting destructor'
EXTRN	??0DateTime@mu2@@QEAA@HHHHHHHHH@Z:PROC		; mu2::DateTime::DateTime
EXTRN	??4DateTime@mu2@@QEAAAEBV01@AEBU_SYSTEMTIME@@@Z:PROC ; mu2::DateTime::operator=
EXTRN	?GetYear@DateTime@mu2@@QEBAHXZ:PROC		; mu2::DateTime::GetYear
EXTRN	?GetMonth@DateTime@mu2@@QEBAHXZ:PROC		; mu2::DateTime::GetMonth
EXTRN	?GetDay@DateTime@mu2@@QEBAHXZ:PROC		; mu2::DateTime::GetDay
EXTRN	?GetHour@DateTime@mu2@@QEBAHXZ:PROC		; mu2::DateTime::GetHour
EXTRN	?GetMinute@DateTime@mu2@@QEBAHXZ:PROC		; mu2::DateTime::GetMinute
EXTRN	?GetSecond@DateTime@mu2@@QEBAHXZ:PROC		; mu2::DateTime::GetSecond
EXTRN	?GetTime@DateTime@mu2@@QEBA_JXZ:PROC		; mu2::DateTime::GetTime
EXTRN	?SetTime@DateTime@mu2@@QEAAX_J@Z:PROC		; mu2::DateTime::SetTime
EXTRN	??_EDateTimeEx@mu2@@UEAAPEAXI@Z:PROC		; mu2::DateTimeEx::`vector deleting destructor'
EXTRN	??_EChangedMoney@mu2@@UEAAPEAXI@Z:PROC		; mu2::ChangedMoney::`vector deleting destructor'
EXTRN	??0WShopTask@mu2@@QEAA@PEAVWShopJob@1@@Z:PROC	; mu2::WShopTask::WShopTask
EXTRN	??1WShopTask@mu2@@UEAA@XZ:PROC			; mu2::WShopTask::~WShopTask
EXTRN	?SetBeginRequest@WShopTask@mu2@@QEAAXPEAVIRequestParent@fcsa@@@Z:PROC ; mu2::WShopTask::SetBeginRequest
EXTRN	?reqWShopItemJobBeginZone@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@XZ:PROC ; mu2::WShopTask::reqWShopItemJobBeginZone
EXTRN	?onResWShopItemJobBeginZone@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z:PROC ; mu2::WShopTask::onResWShopItemJobBeginZone
EXTRN	?reqWShopItemJobEndZone@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@_N@Z:PROC ; mu2::WShopTask::reqWShopItemJobEndZone
EXTRN	?onResWShopItemJobEndZone@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z:PROC ; mu2::WShopTask::onResWShopItemJobEndZone
EXTRN	?reqDbInsertWShopJob@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@XZ:PROC ; mu2::WShopTask::reqDbInsertWShopJob
EXTRN	?onResDbInsertWShopJob@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z:PROC ; mu2::WShopTask::onResDbInsertWShopJob
EXTRN	?reqDbRemoveWShopJob@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@XZ:PROC ; mu2::WShopTask::reqDbRemoveWShopJob
EXTRN	?onResDbRemoveWShopJob@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z:PROC ; mu2::WShopTask::onResDbRemoveWShopJob
EXTRN	?onResDbWShopItemJobEnd@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z:PROC ; mu2::WShopTask::onResDbWShopItemJobEnd
EXTRN	?onResDbItemComfirmTransaction@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z:PROC ; mu2::WShopTask::onResDbItemComfirmTransaction
EXTRN	?SetResultAndReturn@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@W4342@@Z:PROC ; mu2::WShopTask::SetResultAndReturn
EXTRN	?TimeoutPorcess@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@XZ:PROC ; mu2::WShopTask::TimeoutPorcess
EXTRN	?GetParentJobNo@WShopTask@mu2@@QEAA_KXZ:PROC	; mu2::WShopTask::GetParentJobNo
EXTRN	?sendBeginCheck@WShopTask@mu2@@MEAA_NAEAUWShopDbJob@2@AEAV?$vector@UItemData@mu2@@V?$allocator@UItemData@mu2@@@std@@@std@@@Z:PROC ; mu2::WShopTask::sendBeginCheck
EXTRN	?sendConfirmRequest@WShopTask@mu2@@MEAA?AW4Error@ErrorWShop@2@XZ:PROC ; mu2::WShopTask::sendConfirmRequest
EXTRN	?sendCancelRequest@WShopTask@mu2@@MEAA?AW4Error@ErrorWShop@2@XZ:PROC ; mu2::WShopTask::sendCancelRequest
EXTRN	?SendToDb@WorldServer@mu2@@QEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::WorldServer::SendToDb
EXTRN	??_E?$_Ref_count@UEvent@mu2@@@std@@UEAAPEAXI@Z:PROC ; std::_Ref_count<mu2::Event>::`vector deleting destructor'
EXTRN	?Begin@CharacterSlotExtendTrans@mu2@@UEAA?AW4Error@ErrorItem@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::CharacterSlotExtendTrans::Begin
EXTRN	?Commit@CharacterSlotExtendTrans@mu2@@UEAA?AW4Error@ErrorItem@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::CharacterSlotExtendTrans::Commit
EXTRN	?RollBack@CharacterSlotExtendTrans@mu2@@UEAA?AW4Error@ErrorItem@2@XZ:PROC ; mu2::CharacterSlotExtendTrans::RollBack
EXTRN	??_ECharacterSlotExtendTrans@mu2@@UEAAPEAXI@Z:PROC ; mu2::CharacterSlotExtendTrans::`vector deleting destructor'
EXTRN	?Begin@KnightageBaseTrans@mu2@@UEAA?AW4Error@ErrorItem@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::KnightageBaseTrans::Begin
EXTRN	?Commit@KnightageBaseTrans@mu2@@UEAA?AW4Error@ErrorItem@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::KnightageBaseTrans::Commit
EXTRN	?RollBack@KnightageBaseTrans@mu2@@UEAA?AW4Error@ErrorItem@2@XZ:PROC ; mu2::KnightageBaseTrans::RollBack
EXTRN	??_EKnightageBaseTrans@mu2@@UEAAPEAXI@Z:PROC	; mu2::KnightageBaseTrans::`vector deleting destructor'
EXTRN	?Begin@KnightageGiveTrophyTrans@mu2@@UEAA?AW4Error@ErrorItem@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::KnightageGiveTrophyTrans::Begin
EXTRN	?Commit@KnightageGiveTrophyTrans@mu2@@UEAA?AW4Error@ErrorItem@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::KnightageGiveTrophyTrans::Commit
EXTRN	?RollBack@KnightageGiveTrophyTrans@mu2@@UEAA?AW4Error@ErrorItem@2@XZ:PROC ; mu2::KnightageGiveTrophyTrans::RollBack
EXTRN	??_EKnightageGiveTrophyTrans@mu2@@UEAAPEAXI@Z:PROC ; mu2::KnightageGiveTrophyTrans::`vector deleting destructor'
EXTRN	?Begin@KnightageFollowerTrans@mu2@@UEAA?AW4Error@ErrorItem@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::KnightageFollowerTrans::Begin
EXTRN	?Commit@KnightageFollowerTrans@mu2@@UEAA?AW4Error@ErrorItem@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::KnightageFollowerTrans::Commit
EXTRN	?RollBack@KnightageFollowerTrans@mu2@@UEAA?AW4Error@ErrorItem@2@XZ:PROC ; mu2::KnightageFollowerTrans::RollBack
EXTRN	??_EKnightageFollowerTrans@mu2@@UEAAPEAXI@Z:PROC ; mu2::KnightageFollowerTrans::`vector deleting destructor'
EXTRN	??_ETaskItemTranscation@mu2@@UEAAPEAXI@Z:PROC	; mu2::TaskItemTranscation::`vector deleting destructor'
EXTRN	??_E?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@UEAAPEAXI@Z:PROC ; std::_Ref_count<mu2::KnightageFollowerTrans>::`vector deleting destructor'
EXTRN	??_E?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@UEAAPEAXI@Z:PROC ; std::_Ref_count<mu2::KnightageGiveTrophyTrans>::`vector deleting destructor'
EXTRN	??_E?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@UEAAPEAXI@Z:PROC ; std::_Ref_count<mu2::CharacterSlotExtendTrans>::`vector deleting destructor'
EXTRN	_CxxThrowException:PROC
EXTRN	__CxxFrameHandler4:PROC
EXTRN	__GSHandlerCheck:PROC
EXTRN	__security_check_cookie:PROC
EXTRN	??_7type_info@@6B@:BYTE				; type_info::`vftable'
EXTRN	?isBigEndian@PacketStream@mu2@@2_NA:BYTE	; mu2::PacketStream::isBigEndian
EXTRN	?Instance@Server@mu2@@2PEAV12@EA:QWORD		; mu2::Server::Instance
EXTRN	__security_cookie:QWORD
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0exception@std@@QEAA@AEBV01@@Z DD imagerel $LN4
	DD	imagerel $LN4+89
	DD	imagerel $unwind$??0exception@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?what@exception@std@@UEBAPEBDXZ DD imagerel $LN5
	DD	imagerel $LN5+56
	DD	imagerel $unwind$?what@exception@std@@UEBAPEBDXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gexception@std@@UEAAPEAXI@Z DD imagerel $LN10
	DD	imagerel $LN10+83
	DD	imagerel $unwind$??_Gexception@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z DD imagerel $LN9
	DD	imagerel $LN9+104
	DD	imagerel $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+83
	DD	imagerel $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_array_new_length@std@@QEAA@XZ DD imagerel $LN14
	DD	imagerel $LN14+95
	DD	imagerel $unwind$??0bad_array_new_length@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1bad_array_new_length@std@@UEAA@XZ DD imagerel $LN14
	DD	imagerel $LN14+47
	DD	imagerel $unwind$??1bad_array_new_length@std@@UEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z DD imagerel $LN14
	DD	imagerel $LN14+54
	DD	imagerel $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z DD imagerel $LN20
	DD	imagerel $LN20+83
	DD	imagerel $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Throw_bad_array_new_length@std@@YAXXZ DD imagerel $LN3
	DD	imagerel $LN3+37
	DD	imagerel $unwind$?_Throw_bad_array_new_length@std@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z DD imagerel $LN5
	DD	imagerel $LN5+159
	DD	imagerel $unwind$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Xlen_string@std@@YAXXZ DD imagerel $LN3
	DD	imagerel $LN3+22
	DD	imagerel $unwind$?_Xlen_string@std@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z DD imagerel $LN13
	DD	imagerel $LN13+162
	DD	imagerel $unwind$?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEAAPEA_WXZ DD imagerel $LN17
	DD	imagerel $LN17+111
	DD	imagerel $unwind$?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEAAPEA_WXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV01@AEBV01@@Z DD imagerel $LN259
	DD	imagerel $LN259+293
	DD	imagerel $unwind$??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV01@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?assign@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV12@QEB_W_K@Z DD imagerel $LN208
	DD	imagerel $LN208+177
	DD	imagerel $unwind$?assign@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV12@QEB_W_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ DD imagerel $LN38
	DD	imagerel $LN38+250
	DD	imagerel $unwind$?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Calculate_growth@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@CA_K_K00@Z DD imagerel $LN13
	DD	imagerel $LN13+189
	DD	imagerel $unwind$?_Calculate_growth@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@CA_K_K00@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Decref@_Ref_count_base@std@@QEAAXXZ DD imagerel $LN11
	DD	imagerel $LN11+98
	DD	imagerel $unwind$?_Decref@_Ref_count_base@std@@QEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Write@PacketStream@mu2@@QEAAIPEBEI@Z DD imagerel $LN10
	DD	imagerel $LN10+426
	DD	imagerel $unwind$?Write@PacketStream@mu2@@QEAAIPEBEI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Read@PacketStream@mu2@@QEAAIPEAEI@Z DD imagerel $LN10
	DD	imagerel $LN10+602
	DD	imagerel $unwind$?Read@PacketStream@mu2@@QEAAIPEAEI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$rw@_J@PacketStream@mu2@@QEAA_NAEA_J@Z DD imagerel $LN47
	DD	imagerel $LN47+234
	DD	imagerel $unwind$??$rw@_J@PacketStream@mu2@@QEAA_NAEA_J@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?checkingBuffer@PacketStream@mu2@@AEAA_NXZ DD imagerel $LN5
	DD	imagerel $LN5+78
	DD	imagerel $unwind$?checkingBuffer@PacketStream@mu2@@AEAA_NXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$swapEndian@_J@PacketStream@mu2@@AEAA_JAEB_J@Z DD imagerel $LN6
	DD	imagerel $LN6+130
	DD	imagerel $unwind$??$swapEndian@_J@PacketStream@mu2@@AEAA_JAEB_J@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GISerializer@mu2@@UEAAPEAXI@Z DD imagerel $LN10
	DD	imagerel $LN10+65
	DD	imagerel $unwind$??_GISerializer@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0DateTimeEx@mu2@@QEAA@XZ DD imagerel $LN11
	DD	imagerel $LN11+141
	DD	imagerel $unwind$??0DateTimeEx@mu2@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??4DateTimeEx@mu2@@QEAAAEAU01@AEB_J@Z DD imagerel $LN5
	DD	imagerel $LN5+237
	DD	imagerel $unwind$??4DateTimeEx@mu2@@QEAAAEAU01@AEB_J@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??BDateTimeEx@mu2@@QEBA?B_JXZ DD imagerel $LN3
	DD	imagerel $LN3+163
	DD	imagerel $unwind$??BDateTimeEx@mu2@@QEBA?B_JXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?PackUnpack@DateTimeEx@mu2@@UEAA_NAEAVPacketStream@2@@Z DD imagerel $LN60
	DD	imagerel $LN60+118
	DD	imagerel $unwind$?PackUnpack@DateTimeEx@mu2@@UEAA_NAEAVPacketStream@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Reset@DateTimeEx@mu2@@UEAAXXZ DD imagerel $LN5
	DD	imagerel $LN5+106
	DD	imagerel $unwind$?Reset@DateTimeEx@mu2@@UEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GDateTimeEx@mu2@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+65
	DD	imagerel $unwind$??_GDateTimeEx@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?PackUnpack@ChangedMoney@mu2@@UEAA_NAEAVPacketStream@2@@Z DD imagerel $LN49
	DD	imagerel $LN49+95
	DD	imagerel $unwind$?PackUnpack@ChangedMoney@mu2@@UEAA_NAEAVPacketStream@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GChangedMoney@mu2@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+65
	DD	imagerel $unwind$??_GChangedMoney@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$em@W4CalcType@ChangedMoney@mu2@@@PacketStream@mu2@@QEAA_NAEAW4CalcType@ChangedMoney@1@@Z DD imagerel $LN47
	DD	imagerel $LN47+247
	DD	imagerel $unwind$??$em@W4CalcType@ChangedMoney@mu2@@@PacketStream@mu2@@QEAA_NAEAW4CalcType@ChangedMoney@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$em@W4MoneyType@ChangedMoney@mu2@@@PacketStream@mu2@@QEAA_NAEAW4MoneyType@ChangedMoney@1@@Z DD imagerel $LN47
	DD	imagerel $LN47+247
	DD	imagerel $unwind$??$em@W4MoneyType@ChangedMoney@mu2@@@PacketStream@mu2@@QEAA_NAEAW4MoneyType@ChangedMoney@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??4WShopDbUser@mu2@@QEAAAEAU01@AEBU01@@Z DD imagerel $LN649
	DD	imagerel $LN649+223
	DD	imagerel $unwind$??4WShopDbUser@mu2@@QEAAAEAU01@AEBU01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$SmartPtrEx@UEvent@mu2@@@mu2@@QEAA@XZ DD imagerel $LN27
	DD	imagerel $LN27+51
	DD	imagerel $unwind$??1?$SmartPtrEx@UEvent@mu2@@@mu2@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Swap@?$_Ptr_base@UEvent@mu2@@@std@@IEAAXAEAV12@@Z DD imagerel $LN44
	DD	imagerel $LN44+214
	DD	imagerel $unwind$?_Swap@?$_Ptr_base@UEvent@mu2@@@std@@IEAAXAEAV12@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??4?$shared_ptr@UEvent@mu2@@@std@@QEAAAEAV01@AEBV01@@Z DD imagerel $LN103
	DD	imagerel $LN103+155
	DD	imagerel $unwind$??4?$shared_ptr@UEvent@mu2@@@std@@QEAAAEAV01@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$swapEndian@W4CalcType@ChangedMoney@mu2@@@PacketStream@mu2@@AEAA?AW4CalcType@ChangedMoney@1@AEBW4231@@Z DD imagerel $LN6
	DD	imagerel $LN6+102
	DD	imagerel $unwind$??$swapEndian@W4CalcType@ChangedMoney@mu2@@@PacketStream@mu2@@AEAA?AW4CalcType@ChangedMoney@1@AEBW4231@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$swapEndian@W4MoneyType@ChangedMoney@mu2@@@PacketStream@mu2@@AEAA?AW4MoneyType@ChangedMoney@1@AEBW4231@@Z DD imagerel $LN6
	DD	imagerel $LN6+102
	DD	imagerel $unwind$??$swapEndian@W4MoneyType@ChangedMoney@mu2@@@PacketStream@mu2@@AEAA?AW4MoneyType@ChangedMoney@1@AEBW4231@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z DD imagerel $LN37
	DD	imagerel $LN37+320
	DD	imagerel $unwind$??$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z@4HA DD imagerel ?dtor$0@?0???$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z@4HA
	DD	imagerel ?dtor$0@?0???$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z@4HA+24
	DD	imagerel $unwind$?dtor$0@?0???$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$_Temporary_owner@UEvent@mu2@@@std@@QEAA@XZ DD imagerel $LN6
	DD	imagerel $LN6+71
	DD	imagerel $unwind$??1?$_Temporary_owner@UEvent@mu2@@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Destroy@?$_Ref_count@UEvent@mu2@@@std@@EEAAXXZ DD imagerel $LN6
	DD	imagerel $LN6+72
	DD	imagerel $unwind$?_Destroy@?$_Ref_count@UEvent@mu2@@@std@@EEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Delete_this@?$_Ref_count@UEvent@mu2@@@std@@EEAAXXZ DD imagerel $LN6
	DD	imagerel $LN6+69
	DD	imagerel $unwind$?_Delete_this@?$_Ref_count@UEvent@mu2@@@std@@EEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_G?$_Ref_count@UEvent@mu2@@@std@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+50
	DD	imagerel $unwind$??_G?$_Ref_count@UEvent@mu2@@@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Reallocate_for@V<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@Z DD imagerel $LN178
	DD	imagerel $LN178+615
	DD	imagerel $unwind$??$_Reallocate_for@V<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z DD imagerel $LN7
	DD	imagerel $LN7+150
	DD	imagerel $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GCharacterSlotExtendTrans@mu2@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+80
	DD	imagerel $unwind$??_GCharacterSlotExtendTrans@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0KnightageBaseTrans@mu2@@QEAA@W4ID@BaseTrans@1@IAEAUChangedMoney@1@HH@Z DD imagerel $LN17
	DD	imagerel $LN17+213
	DD	imagerel $unwind$??0KnightageBaseTrans@mu2@@QEAA@W4ID@BaseTrans@1@IAEAUChangedMoney@1@HH@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GKnightageBaseTrans@mu2@@UEAAPEAXI@Z DD imagerel $LN25
	DD	imagerel $LN25+96
	DD	imagerel $unwind$??_GKnightageBaseTrans@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GKnightageGiveTrophyTrans@mu2@@UEAAPEAXI@Z DD imagerel $LN30
	DD	imagerel $LN30+111
	DD	imagerel $unwind$??_GKnightageGiveTrophyTrans@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GKnightageFollowerTrans@mu2@@UEAAPEAXI@Z DD imagerel $LN30
	DD	imagerel $LN30+111
	DD	imagerel $unwind$??_GKnightageFollowerTrans@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0TaskItemTranscation@mu2@@QEAA@PEAVWShopJob@1@@Z DD imagerel $LN66
	DD	imagerel $LN66+147
	DD	imagerel $unwind$??0TaskItemTranscation@mu2@@QEAA@PEAVWShopJob@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???0TaskItemTranscation@mu2@@QEAA@PEAVWShopJob@1@@Z@4HA DD imagerel ?dtor$0@?0???0TaskItemTranscation@mu2@@QEAA@PEAVWShopJob@1@@Z@4HA
	DD	imagerel ?dtor$0@?0???0TaskItemTranscation@mu2@@QEAA@PEAVWShopJob@1@@Z@4HA+24
	DD	imagerel $unwind$?dtor$0@?0???0TaskItemTranscation@mu2@@QEAA@PEAVWShopJob@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$1@?0???0TaskItemTranscation@mu2@@QEAA@PEAVWShopJob@1@@Z@4HA DD imagerel ?dtor$1@?0???0TaskItemTranscation@mu2@@QEAA@PEAVWShopJob@1@@Z@4HA
	DD	imagerel ?dtor$1@?0???0TaskItemTranscation@mu2@@QEAA@PEAVWShopJob@1@@Z@4HA+31
	DD	imagerel $unwind$?dtor$1@?0???0TaskItemTranscation@mu2@@QEAA@PEAVWShopJob@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1TaskItemTranscation@mu2@@UEAA@XZ DD imagerel $LN65
	DD	imagerel $LN65+158
	DD	imagerel $unwind$??1TaskItemTranscation@mu2@@UEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Response@TaskItemTranscation@mu2@@UEAA?AW4Error@ErrorWShop@2@PEAVIResponseParent@fcsa@@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z DD imagerel $LN8
	DD	imagerel $LN8+40
	DD	imagerel $unwind$?Response@TaskItemTranscation@mu2@@UEAA?AW4Error@ErrorWShop@2@PEAVIResponseParent@fcsa@@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?onResDbWShopItemTranscation@TaskItemTranscation@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z DD imagerel $LN41
	DD	imagerel $LN41+500
	DD	imagerel $unwind$?onResDbWShopItemTranscation@TaskItemTranscation@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Setup@TaskItemTranscation@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z DD imagerel $LN683
	DD	imagerel $LN683+541
	DD	imagerel $unwind$?Setup@TaskItemTranscation@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z DD imagerel $LN507
	DD	imagerel $LN507+1625
	DD	imagerel $unwind$?createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0??createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z@4HA DD imagerel ?dtor$0@?0??createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z@4HA
	DD	imagerel ?dtor$0@?0??createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z@4HA+29
	DD	imagerel $unwind$?dtor$0@?0??createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$12@?0??createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z@4HA DD imagerel ?dtor$12@?0??createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z@4HA
	DD	imagerel ?dtor$12@?0??createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z@4HA+27
	DD	imagerel $unwind$?dtor$12@?0??createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$3@?0??createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z@4HA DD imagerel ?dtor$3@?0??createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z@4HA
	DD	imagerel ?dtor$3@?0??createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z@4HA+29
	DD	imagerel $unwind$?dtor$3@?0??createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$44@?0??createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z@4HA DD imagerel ?dtor$44@?0??createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z@4HA
	DD	imagerel ?dtor$44@?0??createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z@4HA+27
	DD	imagerel $unwind$?dtor$44@?0??createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?sendBeginRequest@TaskItemTranscation@mu2@@EEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z DD imagerel $LN3
	DD	imagerel $LN3+36
	DD	imagerel $unwind$?sendBeginRequest@TaskItemTranscation@mu2@@EEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?sendDbRequest@TaskItemTranscation@mu2@@AEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z DD imagerel $LN269
	DD	imagerel $LN269+299
	DD	imagerel $unwind$?sendDbRequest@TaskItemTranscation@mu2@@AEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Swap@?$_Ptr_base@VBaseTrans@mu2@@@std@@IEAAXAEAV12@@Z DD imagerel $LN44
	DD	imagerel $LN44+214
	DD	imagerel $unwind$?_Swap@?$_Ptr_base@VBaseTrans@mu2@@@std@@IEAAXAEAV12@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??4?$shared_ptr@VBaseTrans@mu2@@@std@@QEAAAEAV01@$$QEAV01@@Z DD imagerel $LN102
	DD	imagerel $LN102+172
	DD	imagerel $unwind$??4?$shared_ptr@VBaseTrans@mu2@@@std@@QEAAAEAV01@$$QEAV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GTaskItemTranscation@mu2@@UEAAPEAXI@Z DD imagerel $LN5
	DD	imagerel $LN5+60
	DD	imagerel $unwind$??_GTaskItemTranscation@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$?0VKnightageFollowerTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageFollowerTrans@mu2@@@Z DD imagerel $LN37
	DD	imagerel $LN37+321
	DD	imagerel $unwind$??$?0VKnightageFollowerTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageFollowerTrans@mu2@@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???$?0VKnightageFollowerTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageFollowerTrans@mu2@@@Z@4HA DD imagerel ?dtor$0@?0???$?0VKnightageFollowerTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageFollowerTrans@mu2@@@Z@4HA
	DD	imagerel ?dtor$0@?0???$?0VKnightageFollowerTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageFollowerTrans@mu2@@@Z@4HA+24
	DD	imagerel $unwind$?dtor$0@?0???$?0VKnightageFollowerTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageFollowerTrans@mu2@@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$?0VKnightageGiveTrophyTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageGiveTrophyTrans@mu2@@@Z DD imagerel $LN37
	DD	imagerel $LN37+321
	DD	imagerel $unwind$??$?0VKnightageGiveTrophyTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageGiveTrophyTrans@mu2@@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???$?0VKnightageGiveTrophyTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageGiveTrophyTrans@mu2@@@Z@4HA DD imagerel ?dtor$0@?0???$?0VKnightageGiveTrophyTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageGiveTrophyTrans@mu2@@@Z@4HA
	DD	imagerel ?dtor$0@?0???$?0VKnightageGiveTrophyTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageGiveTrophyTrans@mu2@@@Z@4HA+24
	DD	imagerel $unwind$?dtor$0@?0???$?0VKnightageGiveTrophyTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageGiveTrophyTrans@mu2@@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$?0VCharacterSlotExtendTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVCharacterSlotExtendTrans@mu2@@@Z DD imagerel $LN37
	DD	imagerel $LN37+321
	DD	imagerel $unwind$??$?0VCharacterSlotExtendTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVCharacterSlotExtendTrans@mu2@@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???$?0VCharacterSlotExtendTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVCharacterSlotExtendTrans@mu2@@@Z@4HA DD imagerel ?dtor$0@?0???$?0VCharacterSlotExtendTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVCharacterSlotExtendTrans@mu2@@@Z@4HA
	DD	imagerel ?dtor$0@?0???$?0VCharacterSlotExtendTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVCharacterSlotExtendTrans@mu2@@@Z@4HA+24
	DD	imagerel $unwind$?dtor$0@?0???$?0VCharacterSlotExtendTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVCharacterSlotExtendTrans@mu2@@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$_Temporary_owner@VKnightageFollowerTrans@mu2@@@std@@QEAA@XZ DD imagerel $LN6
	DD	imagerel $LN6+72
	DD	imagerel $unwind$??1?$_Temporary_owner@VKnightageFollowerTrans@mu2@@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Destroy@?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@EEAAXXZ DD imagerel $LN6
	DD	imagerel $LN6+73
	DD	imagerel $unwind$?_Destroy@?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@EEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Delete_this@?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@EEAAXXZ DD imagerel $LN6
	DD	imagerel $LN6+69
	DD	imagerel $unwind$?_Delete_this@?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@EEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_G?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+50
	DD	imagerel $unwind$??_G?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$_Temporary_owner@VKnightageGiveTrophyTrans@mu2@@@std@@QEAA@XZ DD imagerel $LN6
	DD	imagerel $LN6+72
	DD	imagerel $unwind$??1?$_Temporary_owner@VKnightageGiveTrophyTrans@mu2@@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Destroy@?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@EEAAXXZ DD imagerel $LN6
	DD	imagerel $LN6+73
	DD	imagerel $unwind$?_Destroy@?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@EEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Delete_this@?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@EEAAXXZ DD imagerel $LN6
	DD	imagerel $LN6+69
	DD	imagerel $unwind$?_Delete_this@?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@EEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_G?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+50
	DD	imagerel $unwind$??_G?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$_Temporary_owner@VCharacterSlotExtendTrans@mu2@@@std@@QEAA@XZ DD imagerel $LN6
	DD	imagerel $LN6+72
	DD	imagerel $unwind$??1?$_Temporary_owner@VCharacterSlotExtendTrans@mu2@@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Destroy@?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@EEAAXXZ DD imagerel $LN6
	DD	imagerel $LN6+73
	DD	imagerel $unwind$?_Destroy@?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@EEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Delete_this@?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@EEAAXXZ DD imagerel $LN6
	DD	imagerel $LN6+69
	DD	imagerel $unwind$?_Delete_this@?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@EEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_G?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+50
	DD	imagerel $unwind$??_G?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT ??_R1A@?0A@EA@?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@8 DD imagerel ??_R0?AV?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@@8 ; std::_Ref_count<mu2::CharacterSlotExtendTrans>::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@8
rdata$r	ENDS
;	COMDAT ??_R2?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@8
rdata$r	SEGMENT
??_R2?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@8 DD imagerel ??_R1A@?0A@EA@?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@8 ; std::_Ref_count<mu2::CharacterSlotExtendTrans>::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@_Ref_count_base@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@8
rdata$r	SEGMENT
??_R3?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@8 DD 00H ; std::_Ref_count<mu2::CharacterSlotExtendTrans>::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@8
rdata$r	ENDS
;	COMDAT ??_R0?AV?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@@8
data$rs	SEGMENT
??_R0?AV?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@@8 DQ FLAT:??_7type_info@@6B@ ; std::_Ref_count<mu2::CharacterSlotExtendTrans> `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AV?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@', 00H
data$rs	ENDS
;	COMDAT ??_R4?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@6B@
rdata$r	SEGMENT
??_R4?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@6B@ DD 01H ; std::_Ref_count<mu2::CharacterSlotExtendTrans>::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AV?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@@8
	DD	imagerel ??_R3?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@8
	DD	imagerel ??_R4?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@8 DD imagerel ??_R0?AV?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@@8 ; std::_Ref_count<mu2::KnightageGiveTrophyTrans>::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@8
rdata$r	ENDS
;	COMDAT ??_R2?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@8
rdata$r	SEGMENT
??_R2?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@8 DD imagerel ??_R1A@?0A@EA@?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@8 ; std::_Ref_count<mu2::KnightageGiveTrophyTrans>::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@_Ref_count_base@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@8
rdata$r	SEGMENT
??_R3?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@8 DD 00H ; std::_Ref_count<mu2::KnightageGiveTrophyTrans>::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@8
rdata$r	ENDS
;	COMDAT ??_R0?AV?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@@8
data$rs	SEGMENT
??_R0?AV?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@@8 DQ FLAT:??_7type_info@@6B@ ; std::_Ref_count<mu2::KnightageGiveTrophyTrans> `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AV?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@', 00H
data$rs	ENDS
;	COMDAT ??_R4?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@6B@
rdata$r	SEGMENT
??_R4?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@6B@ DD 01H ; std::_Ref_count<mu2::KnightageGiveTrophyTrans>::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AV?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@@8
	DD	imagerel ??_R3?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@8
	DD	imagerel ??_R4?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@8 DD imagerel ??_R0?AV?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@@8 ; std::_Ref_count<mu2::KnightageFollowerTrans>::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@8
rdata$r	ENDS
;	COMDAT ??_R2?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@8
rdata$r	SEGMENT
??_R2?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@8 DD imagerel ??_R1A@?0A@EA@?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@8 ; std::_Ref_count<mu2::KnightageFollowerTrans>::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@_Ref_count_base@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@8
rdata$r	SEGMENT
??_R3?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@8 DD 00H ; std::_Ref_count<mu2::KnightageFollowerTrans>::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@8
rdata$r	ENDS
;	COMDAT ??_R0?AV?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@@8
data$rs	SEGMENT
??_R0?AV?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@@8 DQ FLAT:??_7type_info@@6B@ ; std::_Ref_count<mu2::KnightageFollowerTrans> `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AV?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@', 00H
data$rs	ENDS
;	COMDAT ??_R4?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@6B@
rdata$r	SEGMENT
??_R4?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@6B@ DD 01H ; std::_Ref_count<mu2::KnightageFollowerTrans>::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AV?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@@8
	DD	imagerel ??_R3?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@8
	DD	imagerel ??_R4?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@6B@
rdata$r	ENDS
;	COMDAT ??_R2WShopTask@mu2@@8
rdata$r	SEGMENT
??_R2WShopTask@mu2@@8 DD imagerel ??_R1A@?0A@EA@WShopTask@mu2@@8 ; mu2::WShopTask::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3WShopTask@mu2@@8
rdata$r	SEGMENT
??_R3WShopTask@mu2@@8 DD 00H				; mu2::WShopTask::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2WShopTask@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVWShopTask@mu2@@@8
data$rs	SEGMENT
??_R0?AVWShopTask@mu2@@@8 DQ FLAT:??_7type_info@@6B@	; mu2::WShopTask `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVWShopTask@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R1A@?0A@EA@WShopTask@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@WShopTask@mu2@@8 DD imagerel ??_R0?AVWShopTask@mu2@@@8 ; mu2::WShopTask::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3WShopTask@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@TaskItemTranscation@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@TaskItemTranscation@mu2@@8 DD imagerel ??_R0?AVTaskItemTranscation@mu2@@@8 ; mu2::TaskItemTranscation::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3TaskItemTranscation@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2TaskItemTranscation@mu2@@8
rdata$r	SEGMENT
??_R2TaskItemTranscation@mu2@@8 DD imagerel ??_R1A@?0A@EA@TaskItemTranscation@mu2@@8 ; mu2::TaskItemTranscation::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@WShopTask@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3TaskItemTranscation@mu2@@8
rdata$r	SEGMENT
??_R3TaskItemTranscation@mu2@@8 DD 00H			; mu2::TaskItemTranscation::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2TaskItemTranscation@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVTaskItemTranscation@mu2@@@8
data$rs	SEGMENT
??_R0?AVTaskItemTranscation@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::TaskItemTranscation `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVTaskItemTranscation@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4TaskItemTranscation@mu2@@6B@
rdata$r	SEGMENT
??_R4TaskItemTranscation@mu2@@6B@ DD 01H		; mu2::TaskItemTranscation::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVTaskItemTranscation@mu2@@@8
	DD	imagerel ??_R3TaskItemTranscation@mu2@@8
	DD	imagerel ??_R4TaskItemTranscation@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@KnightageFollowerTrans@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@KnightageFollowerTrans@mu2@@8 DD imagerel ??_R0?AVKnightageFollowerTrans@mu2@@@8 ; mu2::KnightageFollowerTrans::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	02H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3KnightageFollowerTrans@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2KnightageFollowerTrans@mu2@@8
rdata$r	SEGMENT
??_R2KnightageFollowerTrans@mu2@@8 DD imagerel ??_R1A@?0A@EA@KnightageFollowerTrans@mu2@@8 ; mu2::KnightageFollowerTrans::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@KnightageBaseTrans@mu2@@8
	DD	imagerel ??_R1A@?0A@EA@BaseTrans@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3KnightageFollowerTrans@mu2@@8
rdata$r	SEGMENT
??_R3KnightageFollowerTrans@mu2@@8 DD 00H		; mu2::KnightageFollowerTrans::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	03H
	DD	imagerel ??_R2KnightageFollowerTrans@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVKnightageFollowerTrans@mu2@@@8
data$rs	SEGMENT
??_R0?AVKnightageFollowerTrans@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::KnightageFollowerTrans `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVKnightageFollowerTrans@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4KnightageFollowerTrans@mu2@@6B@
rdata$r	SEGMENT
??_R4KnightageFollowerTrans@mu2@@6B@ DD 01H		; mu2::KnightageFollowerTrans::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVKnightageFollowerTrans@mu2@@@8
	DD	imagerel ??_R3KnightageFollowerTrans@mu2@@8
	DD	imagerel ??_R4KnightageFollowerTrans@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@KnightageGiveTrophyTrans@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@KnightageGiveTrophyTrans@mu2@@8 DD imagerel ??_R0?AVKnightageGiveTrophyTrans@mu2@@@8 ; mu2::KnightageGiveTrophyTrans::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	02H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3KnightageGiveTrophyTrans@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2KnightageGiveTrophyTrans@mu2@@8
rdata$r	SEGMENT
??_R2KnightageGiveTrophyTrans@mu2@@8 DD imagerel ??_R1A@?0A@EA@KnightageGiveTrophyTrans@mu2@@8 ; mu2::KnightageGiveTrophyTrans::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@KnightageBaseTrans@mu2@@8
	DD	imagerel ??_R1A@?0A@EA@BaseTrans@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3KnightageGiveTrophyTrans@mu2@@8
rdata$r	SEGMENT
??_R3KnightageGiveTrophyTrans@mu2@@8 DD 00H		; mu2::KnightageGiveTrophyTrans::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	03H
	DD	imagerel ??_R2KnightageGiveTrophyTrans@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVKnightageGiveTrophyTrans@mu2@@@8
data$rs	SEGMENT
??_R0?AVKnightageGiveTrophyTrans@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::KnightageGiveTrophyTrans `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVKnightageGiveTrophyTrans@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4KnightageGiveTrophyTrans@mu2@@6B@
rdata$r	SEGMENT
??_R4KnightageGiveTrophyTrans@mu2@@6B@ DD 01H		; mu2::KnightageGiveTrophyTrans::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVKnightageGiveTrophyTrans@mu2@@@8
	DD	imagerel ??_R3KnightageGiveTrophyTrans@mu2@@8
	DD	imagerel ??_R4KnightageGiveTrophyTrans@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@KnightageBaseTrans@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@KnightageBaseTrans@mu2@@8 DD imagerel ??_R0?AVKnightageBaseTrans@mu2@@@8 ; mu2::KnightageBaseTrans::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3KnightageBaseTrans@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2KnightageBaseTrans@mu2@@8
rdata$r	SEGMENT
??_R2KnightageBaseTrans@mu2@@8 DD imagerel ??_R1A@?0A@EA@KnightageBaseTrans@mu2@@8 ; mu2::KnightageBaseTrans::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@BaseTrans@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3KnightageBaseTrans@mu2@@8
rdata$r	SEGMENT
??_R3KnightageBaseTrans@mu2@@8 DD 00H			; mu2::KnightageBaseTrans::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2KnightageBaseTrans@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVKnightageBaseTrans@mu2@@@8
data$rs	SEGMENT
??_R0?AVKnightageBaseTrans@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::KnightageBaseTrans `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVKnightageBaseTrans@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4KnightageBaseTrans@mu2@@6B@
rdata$r	SEGMENT
??_R4KnightageBaseTrans@mu2@@6B@ DD 01H			; mu2::KnightageBaseTrans::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVKnightageBaseTrans@mu2@@@8
	DD	imagerel ??_R3KnightageBaseTrans@mu2@@8
	DD	imagerel ??_R4KnightageBaseTrans@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@CharacterSlotExtendTrans@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@CharacterSlotExtendTrans@mu2@@8 DD imagerel ??_R0?AVCharacterSlotExtendTrans@mu2@@@8 ; mu2::CharacterSlotExtendTrans::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3CharacterSlotExtendTrans@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2CharacterSlotExtendTrans@mu2@@8
rdata$r	SEGMENT
??_R2CharacterSlotExtendTrans@mu2@@8 DD imagerel ??_R1A@?0A@EA@CharacterSlotExtendTrans@mu2@@8 ; mu2::CharacterSlotExtendTrans::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@BaseTrans@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3CharacterSlotExtendTrans@mu2@@8
rdata$r	SEGMENT
??_R3CharacterSlotExtendTrans@mu2@@8 DD 00H		; mu2::CharacterSlotExtendTrans::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2CharacterSlotExtendTrans@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVCharacterSlotExtendTrans@mu2@@@8
data$rs	SEGMENT
??_R0?AVCharacterSlotExtendTrans@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::CharacterSlotExtendTrans `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVCharacterSlotExtendTrans@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4CharacterSlotExtendTrans@mu2@@6B@
rdata$r	SEGMENT
??_R4CharacterSlotExtendTrans@mu2@@6B@ DD 01H		; mu2::CharacterSlotExtendTrans::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVCharacterSlotExtendTrans@mu2@@@8
	DD	imagerel ??_R3CharacterSlotExtendTrans@mu2@@8
	DD	imagerel ??_R4CharacterSlotExtendTrans@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@BaseTrans@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@BaseTrans@mu2@@8 DD imagerel ??_R0?AVBaseTrans@mu2@@@8 ; mu2::BaseTrans::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3BaseTrans@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2BaseTrans@mu2@@8
rdata$r	SEGMENT
??_R2BaseTrans@mu2@@8 DD imagerel ??_R1A@?0A@EA@BaseTrans@mu2@@8 ; mu2::BaseTrans::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3BaseTrans@mu2@@8
rdata$r	SEGMENT
??_R3BaseTrans@mu2@@8 DD 00H				; mu2::BaseTrans::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2BaseTrans@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVBaseTrans@mu2@@@8
data$rs	SEGMENT
??_R0?AVBaseTrans@mu2@@@8 DQ FLAT:??_7type_info@@6B@	; mu2::BaseTrans `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVBaseTrans@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4BaseTrans@mu2@@6B@
rdata$r	SEGMENT
??_R4BaseTrans@mu2@@6B@ DD 01H				; mu2::BaseTrans::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVBaseTrans@mu2@@@8
	DD	imagerel ??_R3BaseTrans@mu2@@8
	DD	imagerel ??_R4BaseTrans@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_7?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@6B@
CONST	SEGMENT
??_7?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@6B@ DQ FLAT:??_R4?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@6B@ ; std::_Ref_count<mu2::CharacterSlotExtendTrans>::`vftable'
	DQ	FLAT:?_Destroy@?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@EEAAXXZ
	DQ	FLAT:?_Delete_this@?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@EEAAXXZ
	DQ	FLAT:??_E?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@UEAAPEAXI@Z
	DQ	FLAT:?_Get_deleter@_Ref_count_base@std@@UEBAPEAXAEBVtype_info@@@Z
CONST	ENDS
;	COMDAT ??_7?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@6B@
CONST	SEGMENT
??_7?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@6B@ DQ FLAT:??_R4?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@6B@ ; std::_Ref_count<mu2::KnightageGiveTrophyTrans>::`vftable'
	DQ	FLAT:?_Destroy@?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@EEAAXXZ
	DQ	FLAT:?_Delete_this@?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@EEAAXXZ
	DQ	FLAT:??_E?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@UEAAPEAXI@Z
	DQ	FLAT:?_Get_deleter@_Ref_count_base@std@@UEBAPEAXAEBVtype_info@@@Z
CONST	ENDS
;	COMDAT ??_7?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@6B@
CONST	SEGMENT
??_7?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@6B@ DQ FLAT:??_R4?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@6B@ ; std::_Ref_count<mu2::KnightageFollowerTrans>::`vftable'
	DQ	FLAT:?_Destroy@?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@EEAAXXZ
	DQ	FLAT:?_Delete_this@?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@EEAAXXZ
	DQ	FLAT:??_E?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@UEAAPEAXI@Z
	DQ	FLAT:?_Get_deleter@_Ref_count_base@std@@UEBAPEAXAEBVtype_info@@@Z
CONST	ENDS
;	COMDAT ??_C@_0DG@HKGKMPPL@mu2?3?3TaskItemTranscation?3?3onRes@
CONST	SEGMENT
??_C@_0DG@HKGKMPPL@mu2?3?3TaskItemTranscation?3?3onRes@ DB 'mu2::TaskItem'
	DB	'Transcation::onResDbWShopItemTranscation', 00H ; `string'
CONST	ENDS
;	COMDAT ??_C@_1GI@DJAOHGGM@?$AAW?$AAS?$AAH?$AAO?$AAP?$AA?$CI?$AA?$CF?$AAu?$AA?$CJ?$AA?0?$AA?$CF?$AAl?$AAl?$AAu?$AA?5@
CONST	SEGMENT
??_C@_1GI@DJAOHGGM@?$AAW?$AAS?$AAH?$AAO?$AAP?$AA?$CI?$AA?$CF?$AAu?$AA?$CJ?$AA?0?$AA?$CF?$AAl?$AAl?$AAu?$AA?5@ DB 'W'
	DB	00H, 'S', 00H, 'H', 00H, 'O', 00H, 'P', 00H, '(', 00H, '%', 00H
	DB	'u', 00H, ')', 00H, ',', 00H, '%', 00H, 'l', 00H, 'l', 00H, 'u'
	DB	00H, ' ', 00H, ':', 00H, ' ', 00H, 'o', 00H, 'n', 00H, 'R', 00H
	DB	'e', 00H, 's', 00H, 'D', 00H, 'b', 00H, 'W', 00H, 'S', 00H, 'h'
	DB	00H, 'o', 00H, 'p', 00H, 'I', 00H, 't', 00H, 'e', 00H, 'm', 00H
	DB	'T', 00H, 'r', 00H, 'a', 00H, 'n', 00H, 's', 00H, 'c', 00H, 'a'
	DB	00H, 't', 00H, 'i', 00H, 'o', 00H, 'n', 00H, '>', 00H, ' ', 00H
	DB	'S', 00H, 't', 00H, 'a', 00H, 'r', 00H, 't', 00H, 00H, 00H ; `string'
CONST	ENDS
;	COMDAT ??_C@_0CI@HOAIHMKD@mu2?3?3TaskItemTranscation?3?3sendD@
CONST	SEGMENT
??_C@_0CI@HOAIHMKD@mu2?3?3TaskItemTranscation?3?3sendD@ DB 'mu2::TaskItem'
	DB	'Transcation::sendDbRequest', 00H		; `string'
CONST	ENDS
;	COMDAT ??_C@_0FI@OJCHCMJH@F?3?2Release_Branch?2Server?2Develo@
CONST	SEGMENT
??_C@_0FI@OJCHCMJH@F?3?2Release_Branch?2Server?2Develo@ DB 'F:\Release_Br'
	DB	'anch\Server\Development\Frontend\WorldServer\WShop\TaskItemTr'
	DB	'anscation.cpp', 00H				; `string'
CONST	ENDS
;	COMDAT ??_C@_1EM@JHJBJGKM@?$AAW?$AAS?$AAH?$AAO?$AAP?$AA?$CI?$AA?$CF?$AAu?$AA?$CJ?$AA?0?$AA?$CF?$AAl?$AAl?$AAu?$AA?5@
CONST	SEGMENT
??_C@_1EM@JHJBJGKM@?$AAW?$AAS?$AAH?$AAO?$AAP?$AA?$CI?$AA?$CF?$AAu?$AA?$CJ?$AA?0?$AA?$CF?$AAl?$AAl?$AAu?$AA?5@ DB 'W'
	DB	00H, 'S', 00H, 'H', 00H, 'O', 00H, 'P', 00H, '(', 00H, '%', 00H
	DB	'u', 00H, ')', 00H, ',', 00H, '%', 00H, 'l', 00H, 'l', 00H, 'u'
	DB	00H, ' ', 00H, ':', 00H, ' ', 00H, 's', 00H, 'e', 00H, 'n', 00H
	DB	'd', 00H, 'D', 00H, 'b', 00H, 'R', 00H, 'e', 00H, 'q', 00H, 'u'
	DB	00H, 'e', 00H, 's', 00H, 't', 00H, '>', 00H, ' ', 00H, 'S', 00H
	DB	't', 00H, 'a', 00H, 'r', 00H, 't', 00H, 00H, 00H ; `string'
CONST	ENDS
;	COMDAT ??_7TaskItemTranscation@mu2@@6B@
CONST	SEGMENT
??_7TaskItemTranscation@mu2@@6B@ DQ FLAT:??_R4TaskItemTranscation@mu2@@6B@ ; mu2::TaskItemTranscation::`vftable'
	DQ	FLAT:??_ETaskItemTranscation@mu2@@UEAAPEAXI@Z
	DQ	FLAT:?Response@TaskItemTranscation@mu2@@UEAA?AW4Error@ErrorWShop@2@PEAVIResponseParent@fcsa@@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?reqWShopItemJobBeginZone@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@XZ
	DQ	FLAT:?onResWShopItemJobBeginZone@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z
	DQ	FLAT:?reqWShopItemJobEndZone@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@_N@Z
	DQ	FLAT:?onResWShopItemJobEndZone@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z
	DQ	FLAT:?reqDbInsertWShopJob@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@XZ
	DQ	FLAT:?onResDbInsertWShopJob@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z
	DQ	FLAT:?reqDbRemoveWShopJob@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@XZ
	DQ	FLAT:?onResDbRemoveWShopJob@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z
	DQ	FLAT:?onResDbWShopItemJobEnd@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z
	DQ	FLAT:?onResDbWShopItemTranscation@TaskItemTranscation@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z
	DQ	FLAT:?onResDbItemComfirmTransaction@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z
	DQ	FLAT:?SetResultAndReturn@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@W4342@@Z
	DQ	FLAT:?TimeoutPorcess@WShopTask@mu2@@UEAA?AW4Error@ErrorWShop@2@XZ
	DQ	FLAT:?sendBeginRequest@TaskItemTranscation@mu2@@EEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?sendBeginCheck@WShopTask@mu2@@MEAA_NAEAUWShopDbJob@2@AEAV?$vector@UItemData@mu2@@V?$allocator@UItemData@mu2@@@std@@@std@@@Z
	DQ	FLAT:?sendConfirmRequest@WShopTask@mu2@@MEAA?AW4Error@ErrorWShop@2@XZ
	DQ	FLAT:?sendCancelRequest@WShopTask@mu2@@MEAA?AW4Error@ErrorWShop@2@XZ
	DQ	FLAT:?fillItemResult@WShopTask@mu2@@MEAAXXZ
CONST	ENDS
;	COMDAT ??_7KnightageFollowerTrans@mu2@@6B@
CONST	SEGMENT
??_7KnightageFollowerTrans@mu2@@6B@ DQ FLAT:??_R4KnightageFollowerTrans@mu2@@6B@ ; mu2::KnightageFollowerTrans::`vftable'
	DQ	FLAT:?Begin@KnightageFollowerTrans@mu2@@UEAA?AW4Error@ErrorItem@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?Commit@KnightageFollowerTrans@mu2@@UEAA?AW4Error@ErrorItem@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?RollBack@KnightageFollowerTrans@mu2@@UEAA?AW4Error@ErrorItem@2@XZ
	DQ	FLAT:??_EKnightageFollowerTrans@mu2@@UEAAPEAXI@Z
CONST	ENDS
;	COMDAT ??_7KnightageGiveTrophyTrans@mu2@@6B@
CONST	SEGMENT
??_7KnightageGiveTrophyTrans@mu2@@6B@ DQ FLAT:??_R4KnightageGiveTrophyTrans@mu2@@6B@ ; mu2::KnightageGiveTrophyTrans::`vftable'
	DQ	FLAT:?Begin@KnightageGiveTrophyTrans@mu2@@UEAA?AW4Error@ErrorItem@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?Commit@KnightageGiveTrophyTrans@mu2@@UEAA?AW4Error@ErrorItem@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?RollBack@KnightageGiveTrophyTrans@mu2@@UEAA?AW4Error@ErrorItem@2@XZ
	DQ	FLAT:??_EKnightageGiveTrophyTrans@mu2@@UEAAPEAXI@Z
CONST	ENDS
;	COMDAT ??_7KnightageBaseTrans@mu2@@6B@
CONST	SEGMENT
??_7KnightageBaseTrans@mu2@@6B@ DQ FLAT:??_R4KnightageBaseTrans@mu2@@6B@ ; mu2::KnightageBaseTrans::`vftable'
	DQ	FLAT:?Begin@KnightageBaseTrans@mu2@@UEAA?AW4Error@ErrorItem@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?Commit@KnightageBaseTrans@mu2@@UEAA?AW4Error@ErrorItem@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?RollBack@KnightageBaseTrans@mu2@@UEAA?AW4Error@ErrorItem@2@XZ
	DQ	FLAT:??_EKnightageBaseTrans@mu2@@UEAAPEAXI@Z
CONST	ENDS
;	COMDAT ??_7CharacterSlotExtendTrans@mu2@@6B@
CONST	SEGMENT
??_7CharacterSlotExtendTrans@mu2@@6B@ DQ FLAT:??_R4CharacterSlotExtendTrans@mu2@@6B@ ; mu2::CharacterSlotExtendTrans::`vftable'
	DQ	FLAT:?Begin@CharacterSlotExtendTrans@mu2@@UEAA?AW4Error@ErrorItem@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?Commit@CharacterSlotExtendTrans@mu2@@UEAA?AW4Error@ErrorItem@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?RollBack@CharacterSlotExtendTrans@mu2@@UEAA?AW4Error@ErrorItem@2@XZ
	DQ	FLAT:??_ECharacterSlotExtendTrans@mu2@@UEAAPEAXI@Z
CONST	ENDS
;	COMDAT ??_7BaseTrans@mu2@@6B@
CONST	SEGMENT
??_7BaseTrans@mu2@@6B@ DQ FLAT:??_R4BaseTrans@mu2@@6B@	; mu2::BaseTrans::`vftable'
	DQ	FLAT:?Begin@BaseTrans@mu2@@UEAA?AW4Error@ErrorItem@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?Commit@BaseTrans@mu2@@UEAA?AW4Error@ErrorItem@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?RollBack@BaseTrans@mu2@@UEAA?AW4Error@ErrorItem@2@XZ
CONST	ENDS
;	COMDAT ??_R1A@?0A@EA@?$_Ref_count@UEvent@mu2@@@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@?$_Ref_count@UEvent@mu2@@@std@@8 DD imagerel ??_R0?AV?$_Ref_count@UEvent@mu2@@@std@@@8 ; std::_Ref_count<mu2::Event>::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3?$_Ref_count@UEvent@mu2@@@std@@8
rdata$r	ENDS
;	COMDAT ??_R2?$_Ref_count@UEvent@mu2@@@std@@8
rdata$r	SEGMENT
??_R2?$_Ref_count@UEvent@mu2@@@std@@8 DD imagerel ??_R1A@?0A@EA@?$_Ref_count@UEvent@mu2@@@std@@8 ; std::_Ref_count<mu2::Event>::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@_Ref_count_base@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3?$_Ref_count@UEvent@mu2@@@std@@8
rdata$r	SEGMENT
??_R3?$_Ref_count@UEvent@mu2@@@std@@8 DD 00H		; std::_Ref_count<mu2::Event>::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2?$_Ref_count@UEvent@mu2@@@std@@8
rdata$r	ENDS
;	COMDAT ??_R0?AV?$_Ref_count@UEvent@mu2@@@std@@@8
data$rs	SEGMENT
??_R0?AV?$_Ref_count@UEvent@mu2@@@std@@@8 DQ FLAT:??_7type_info@@6B@ ; std::_Ref_count<mu2::Event> `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AV?$_Ref_count@UEvent@mu2@@@std@@', 00H
data$rs	ENDS
;	COMDAT ??_R4?$_Ref_count@UEvent@mu2@@@std@@6B@
rdata$r	SEGMENT
??_R4?$_Ref_count@UEvent@mu2@@@std@@6B@ DD 01H		; std::_Ref_count<mu2::Event>::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AV?$_Ref_count@UEvent@mu2@@@std@@@8
	DD	imagerel ??_R3?$_Ref_count@UEvent@mu2@@@std@@8
	DD	imagerel ??_R4?$_Ref_count@UEvent@mu2@@@std@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@ChangedMoney@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@ChangedMoney@mu2@@8 DD imagerel ??_R0?AUChangedMoney@mu2@@@8 ; mu2::ChangedMoney::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3ChangedMoney@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2ChangedMoney@mu2@@8
rdata$r	SEGMENT
??_R2ChangedMoney@mu2@@8 DD imagerel ??_R1A@?0A@EA@ChangedMoney@mu2@@8 ; mu2::ChangedMoney::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@ISerializer@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3ChangedMoney@mu2@@8
rdata$r	SEGMENT
??_R3ChangedMoney@mu2@@8 DD 00H				; mu2::ChangedMoney::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2ChangedMoney@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AUChangedMoney@mu2@@@8
data$rs	SEGMENT
??_R0?AUChangedMoney@mu2@@@8 DQ FLAT:??_7type_info@@6B@	; mu2::ChangedMoney `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AUChangedMoney@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4ChangedMoney@mu2@@6B@
rdata$r	SEGMENT
??_R4ChangedMoney@mu2@@6B@ DD 01H			; mu2::ChangedMoney::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AUChangedMoney@mu2@@@8
	DD	imagerel ??_R3ChangedMoney@mu2@@8
	DD	imagerel ??_R4ChangedMoney@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@DateTimeEx@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@DateTimeEx@mu2@@8 DD imagerel ??_R0?AUDateTimeEx@mu2@@@8 ; mu2::DateTimeEx::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3DateTimeEx@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2DateTimeEx@mu2@@8
rdata$r	SEGMENT
??_R2DateTimeEx@mu2@@8 DD imagerel ??_R1A@?0A@EA@DateTimeEx@mu2@@8 ; mu2::DateTimeEx::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@ISerializer@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3DateTimeEx@mu2@@8
rdata$r	SEGMENT
??_R3DateTimeEx@mu2@@8 DD 00H				; mu2::DateTimeEx::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2DateTimeEx@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AUDateTimeEx@mu2@@@8
data$rs	SEGMENT
??_R0?AUDateTimeEx@mu2@@@8 DQ FLAT:??_7type_info@@6B@	; mu2::DateTimeEx `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AUDateTimeEx@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4DateTimeEx@mu2@@6B@
rdata$r	SEGMENT
??_R4DateTimeEx@mu2@@6B@ DD 01H				; mu2::DateTimeEx::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AUDateTimeEx@mu2@@@8
	DD	imagerel ??_R3DateTimeEx@mu2@@8
	DD	imagerel ??_R4DateTimeEx@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@ISerializer@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@ISerializer@mu2@@8 DD imagerel ??_R0?AUISerializer@mu2@@@8 ; mu2::ISerializer::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3ISerializer@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2ISerializer@mu2@@8
rdata$r	SEGMENT
??_R2ISerializer@mu2@@8 DD imagerel ??_R1A@?0A@EA@ISerializer@mu2@@8 ; mu2::ISerializer::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3ISerializer@mu2@@8
rdata$r	SEGMENT
??_R3ISerializer@mu2@@8 DD 00H				; mu2::ISerializer::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2ISerializer@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AUISerializer@mu2@@@8
data$rs	SEGMENT
??_R0?AUISerializer@mu2@@@8 DQ FLAT:??_7type_info@@6B@	; mu2::ISerializer `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AUISerializer@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4ISerializer@mu2@@6B@
rdata$r	SEGMENT
??_R4ISerializer@mu2@@6B@ DD 01H			; mu2::ISerializer::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AUISerializer@mu2@@@8
	DD	imagerel ??_R3ISerializer@mu2@@8
	DD	imagerel ??_R4ISerializer@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@_Ref_count_base@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@_Ref_count_base@std@@8 DD imagerel ??_R0?AV_Ref_count_base@std@@@8 ; std::_Ref_count_base::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3_Ref_count_base@std@@8
rdata$r	ENDS
;	COMDAT ??_R2_Ref_count_base@std@@8
rdata$r	SEGMENT
??_R2_Ref_count_base@std@@8 DD imagerel ??_R1A@?0A@EA@_Ref_count_base@std@@8 ; std::_Ref_count_base::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3_Ref_count_base@std@@8
rdata$r	SEGMENT
??_R3_Ref_count_base@std@@8 DD 00H			; std::_Ref_count_base::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2_Ref_count_base@std@@8
rdata$r	ENDS
;	COMDAT ??_R0?AV_Ref_count_base@std@@@8
data$rs	SEGMENT
??_R0?AV_Ref_count_base@std@@@8 DQ FLAT:??_7type_info@@6B@ ; std::_Ref_count_base `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AV_Ref_count_base@std@@', 00H
data$rs	ENDS
;	COMDAT ??_R4bad_alloc@std@@6B@
rdata$r	SEGMENT
??_R4bad_alloc@std@@6B@ DD 01H				; std::bad_alloc::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVbad_alloc@std@@@8
	DD	imagerel ??_R3bad_alloc@std@@8
	DD	imagerel ??_R4bad_alloc@std@@6B@
rdata$r	ENDS
;	COMDAT ??_R2bad_alloc@std@@8
rdata$r	SEGMENT
??_R2bad_alloc@std@@8 DD imagerel ??_R1A@?0A@EA@bad_alloc@std@@8 ; std::bad_alloc::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@exception@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3bad_alloc@std@@8
rdata$r	SEGMENT
??_R3bad_alloc@std@@8 DD 00H				; std::bad_alloc::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2bad_alloc@std@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@bad_alloc@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@bad_alloc@std@@8 DD imagerel ??_R0?AVbad_alloc@std@@@8 ; std::bad_alloc::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3bad_alloc@std@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@bad_array_new_length@std@@8 DD imagerel ??_R0?AVbad_array_new_length@std@@@8 ; std::bad_array_new_length::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	02H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3bad_array_new_length@std@@8
rdata$r	ENDS
;	COMDAT ??_R2bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R2bad_array_new_length@std@@8 DD imagerel ??_R1A@?0A@EA@bad_array_new_length@std@@8 ; std::bad_array_new_length::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@bad_alloc@std@@8
	DD	imagerel ??_R1A@?0A@EA@exception@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R3bad_array_new_length@std@@8 DD 00H			; std::bad_array_new_length::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	03H
	DD	imagerel ??_R2bad_array_new_length@std@@8
rdata$r	ENDS
;	COMDAT ??_R4bad_array_new_length@std@@6B@
rdata$r	SEGMENT
??_R4bad_array_new_length@std@@6B@ DD 01H		; std::bad_array_new_length::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVbad_array_new_length@std@@@8
	DD	imagerel ??_R3bad_array_new_length@std@@8
	DD	imagerel ??_R4bad_array_new_length@std@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@exception@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@exception@std@@8 DD imagerel ??_R0?AVexception@std@@@8 ; std::exception::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3exception@std@@8
rdata$r	ENDS
;	COMDAT ??_R2exception@std@@8
rdata$r	SEGMENT
??_R2exception@std@@8 DD imagerel ??_R1A@?0A@EA@exception@std@@8 ; std::exception::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3exception@std@@8
rdata$r	SEGMENT
??_R3exception@std@@8 DD 00H				; std::exception::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2exception@std@@8
rdata$r	ENDS
;	COMDAT ??_R4exception@std@@6B@
rdata$r	SEGMENT
??_R4exception@std@@6B@ DD 01H				; std::exception::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVexception@std@@@8
	DD	imagerel ??_R3exception@std@@8
	DD	imagerel ??_R4exception@std@@6B@
rdata$r	ENDS
;	COMDAT ??_7?$_Ref_count@UEvent@mu2@@@std@@6B@
CONST	SEGMENT
??_7?$_Ref_count@UEvent@mu2@@@std@@6B@ DQ FLAT:??_R4?$_Ref_count@UEvent@mu2@@@std@@6B@ ; std::_Ref_count<mu2::Event>::`vftable'
	DQ	FLAT:?_Destroy@?$_Ref_count@UEvent@mu2@@@std@@EEAAXXZ
	DQ	FLAT:?_Delete_this@?$_Ref_count@UEvent@mu2@@@std@@EEAAXXZ
	DQ	FLAT:??_E?$_Ref_count@UEvent@mu2@@@std@@UEAAPEAXI@Z
	DQ	FLAT:?_Get_deleter@_Ref_count_base@std@@UEBAPEAXAEBVtype_info@@@Z
CONST	ENDS
;	COMDAT ??_7ChangedMoney@mu2@@6B@
CONST	SEGMENT
??_7ChangedMoney@mu2@@6B@ DQ FLAT:??_R4ChangedMoney@mu2@@6B@ ; mu2::ChangedMoney::`vftable'
	DQ	FLAT:?PackUnpack@ChangedMoney@mu2@@UEAA_NAEAVPacketStream@2@@Z
	DQ	FLAT:?Reset@ChangedMoney@mu2@@UEAAXXZ
	DQ	FLAT:??_EChangedMoney@mu2@@UEAAPEAXI@Z
CONST	ENDS
;	COMDAT ??_7DateTimeEx@mu2@@6B@
CONST	SEGMENT
??_7DateTimeEx@mu2@@6B@ DQ FLAT:??_R4DateTimeEx@mu2@@6B@ ; mu2::DateTimeEx::`vftable'
	DQ	FLAT:?PackUnpack@DateTimeEx@mu2@@UEAA_NAEAVPacketStream@2@@Z
	DQ	FLAT:?Reset@DateTimeEx@mu2@@UEAAXXZ
	DQ	FLAT:??_EDateTimeEx@mu2@@UEAAPEAXI@Z
CONST	ENDS
;	COMDAT ??_C@_03COAJHJPB@buf@
CONST	SEGMENT
??_C@_03COAJHJPB@buf@ DB 'buf', 00H			; `string'
CONST	ENDS
;	COMDAT ??_C@_0BI@KGAODAAL@mu2?3?3PacketStream?3?3Read@
CONST	SEGMENT
??_C@_0BI@KGAODAAL@mu2?3?3PacketStream?3?3Read@ DB 'mu2::PacketStream::Re'
	DB	'ad', 00H					; `string'
CONST	ENDS
;	COMDAT ??_C@_05IOMEMJEC@count@
CONST	SEGMENT
??_C@_05IOMEMJEC@count@ DB 'count', 00H			; `string'
CONST	ENDS
;	COMDAT ??_C@_0BJ@NEGBBJOE@mu2?3?3PacketStream?3?3Write@
CONST	SEGMENT
??_C@_0BJ@NEGBBJOE@mu2?3?3PacketStream?3?3Write@ DB 'mu2::PacketStream::W'
	DB	'rite', 00H					; `string'
CONST	ENDS
;	COMDAT ??_C@_0BB@HAIDOJLN@checkingBuffer?$CI?$CJ@
CONST	SEGMENT
??_C@_0BB@HAIDOJLN@checkingBuffer?$CI?$CJ@ DB 'checkingBuffer()', 00H ; `string'
CONST	ENDS
;	COMDAT ??_C@_0EJ@IFEHPKPB@F?3?2Release_Branch?2Server?2Develo@
CONST	SEGMENT
??_C@_0EJ@IFEHPKPB@F?3?2Release_Branch?2Server?2Develo@ DB 'F:\Release_Br'
	DB	'anch\Server\Development\Framework\Net\Common\PacketStream.h', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7ISerializer@mu2@@6B@
CONST	SEGMENT
??_7ISerializer@mu2@@6B@ DQ FLAT:??_R4ISerializer@mu2@@6B@ ; mu2::ISerializer::`vftable'
	DQ	FLAT:_purecall
	DQ	FLAT:_purecall
	DQ	FLAT:??_EISerializer@mu2@@UEAAPEAXI@Z
CONST	ENDS
;	COMDAT ??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
CONST	SEGMENT
??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@ DB 'g', 00H, 'a', 00H, 'm', 00H, 'e'
	DB	00H, 00H, 00H				; `string'
CONST	ENDS
;	COMDAT ??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
CONST	SEGMENT
??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@ DB '%'
	DB	's> ASSERT - %s, %s(%d)', 00H		; `string'
CONST	ENDS
;	COMDAT ??_C@_0BA@JFNIOLAK@string?5too?5long@
CONST	SEGMENT
??_C@_0BA@JFNIOLAK@string?5too?5long@ DB 'string too long', 00H ; `string'
CONST	ENDS
;	COMDAT _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 DD 010H
	DD	imagerel ??_R0?AVbad_alloc@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0bad_alloc@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVbad_alloc@std@@@8
data$r	SEGMENT
??_R0?AVbad_alloc@std@@@8 DQ FLAT:??_7type_info@@6B@	; std::bad_alloc `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVbad_alloc@std@@', 00H
data$r	ENDS
;	COMDAT _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 DD 00H
	DD	imagerel ??_R0?AVbad_array_new_length@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0bad_array_new_length@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVbad_array_new_length@std@@@8
data$r	SEGMENT
??_R0?AVbad_array_new_length@std@@@8 DQ FLAT:??_7type_info@@6B@ ; std::bad_array_new_length `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVbad_array_new_length@std@@', 00H
data$r	ENDS
;	COMDAT _CTA3?AVbad_array_new_length@std@@
xdata$x	SEGMENT
_CTA3?AVbad_array_new_length@std@@ DD 03H
	DD	imagerel _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
	DD	imagerel _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
	DD	imagerel _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
xdata$x	ENDS
;	COMDAT _TI3?AVbad_array_new_length@std@@
xdata$x	SEGMENT
_TI3?AVbad_array_new_length@std@@ DD 00H
	DD	imagerel ??1bad_array_new_length@std@@UEAA@XZ
	DD	00H
	DD	imagerel _CTA3?AVbad_array_new_length@std@@
xdata$x	ENDS
;	COMDAT _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 DD 00H
	DD	imagerel ??_R0?AVexception@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0exception@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVexception@std@@@8
data$r	SEGMENT
??_R0?AVexception@std@@@8 DQ FLAT:??_7type_info@@6B@	; std::exception `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVexception@std@@', 00H
data$r	ENDS
;	COMDAT ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@
CONST	SEGMENT
??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ DB 'bad array new length', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7bad_array_new_length@std@@6B@
CONST	SEGMENT
??_7bad_array_new_length@std@@6B@ DQ FLAT:??_R4bad_array_new_length@std@@6B@ ; std::bad_array_new_length::`vftable'
	DQ	FLAT:??_Ebad_array_new_length@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT ??_7bad_alloc@std@@6B@
CONST	SEGMENT
??_7bad_alloc@std@@6B@ DQ FLAT:??_R4bad_alloc@std@@6B@	; std::bad_alloc::`vftable'
	DQ	FLAT:??_Ebad_alloc@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT ??_C@_0BC@EOODALEL@Unknown?5exception@
CONST	SEGMENT
??_C@_0BC@EOODALEL@Unknown?5exception@ DB 'Unknown exception', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7exception@std@@6B@
CONST	SEGMENT
??_7exception@std@@6B@ DQ FLAT:??_R4exception@std@@6B@	; std::exception::`vftable'
	DQ	FLAT:??_Eexception@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_G?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Delete_this@?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@EEAAXXZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Destroy@?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@EEAAXXZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$_Temporary_owner@VCharacterSlotExtendTrans@mu2@@@std@@QEAA@XZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_G?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Delete_this@?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@EEAAXXZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Destroy@?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@EEAAXXZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$_Temporary_owner@VKnightageGiveTrophyTrans@mu2@@@std@@QEAA@XZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_G?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Delete_this@?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@EEAAXXZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Destroy@?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@EEAAXXZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$_Temporary_owner@VKnightageFollowerTrans@mu2@@@std@@QEAA@XZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???$?0VCharacterSlotExtendTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVCharacterSlotExtendTrans@mu2@@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$?0VCharacterSlotExtendTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVCharacterSlotExtendTrans@mu2@@@Z DB 06H
	DB	00H
	DB	00H
	DB	'v'
	DB	02H
	DB	05H, 03H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$?0VCharacterSlotExtendTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVCharacterSlotExtendTrans@mu2@@@Z DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???$?0VCharacterSlotExtendTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVCharacterSlotExtendTrans@mu2@@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$?0VCharacterSlotExtendTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVCharacterSlotExtendTrans@mu2@@@Z DB 028H
	DD	imagerel $stateUnwindMap$??$?0VCharacterSlotExtendTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVCharacterSlotExtendTrans@mu2@@@Z
	DD	imagerel $ip2state$??$?0VCharacterSlotExtendTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVCharacterSlotExtendTrans@mu2@@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$?0VCharacterSlotExtendTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVCharacterSlotExtendTrans@mu2@@@Z DD 020f11H
	DD	0700bd20fH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$?0VCharacterSlotExtendTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVCharacterSlotExtendTrans@mu2@@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???$?0VKnightageGiveTrophyTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageGiveTrophyTrans@mu2@@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$?0VKnightageGiveTrophyTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageGiveTrophyTrans@mu2@@@Z DB 06H
	DB	00H
	DB	00H
	DB	'v'
	DB	02H
	DB	05H, 03H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$?0VKnightageGiveTrophyTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageGiveTrophyTrans@mu2@@@Z DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???$?0VKnightageGiveTrophyTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageGiveTrophyTrans@mu2@@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$?0VKnightageGiveTrophyTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageGiveTrophyTrans@mu2@@@Z DB 028H
	DD	imagerel $stateUnwindMap$??$?0VKnightageGiveTrophyTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageGiveTrophyTrans@mu2@@@Z
	DD	imagerel $ip2state$??$?0VKnightageGiveTrophyTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageGiveTrophyTrans@mu2@@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$?0VKnightageGiveTrophyTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageGiveTrophyTrans@mu2@@@Z DD 020f11H
	DD	0700bd20fH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$?0VKnightageGiveTrophyTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageGiveTrophyTrans@mu2@@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???$?0VKnightageFollowerTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageFollowerTrans@mu2@@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$?0VKnightageFollowerTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageFollowerTrans@mu2@@@Z DB 06H
	DB	00H
	DB	00H
	DB	'v'
	DB	02H
	DB	05H, 03H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$?0VKnightageFollowerTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageFollowerTrans@mu2@@@Z DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???$?0VKnightageFollowerTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageFollowerTrans@mu2@@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$?0VKnightageFollowerTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageFollowerTrans@mu2@@@Z DB 028H
	DD	imagerel $stateUnwindMap$??$?0VKnightageFollowerTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageFollowerTrans@mu2@@@Z
	DD	imagerel $ip2state$??$?0VKnightageFollowerTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageFollowerTrans@mu2@@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$?0VKnightageFollowerTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageFollowerTrans@mu2@@@Z DD 020f11H
	DD	0700bd20fH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$?0VKnightageFollowerTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageFollowerTrans@mu2@@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GTaskItemTranscation@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??4?$shared_ptr@VBaseTrans@mu2@@@std@@QEAAAEAV01@$$QEAV01@@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Swap@?$_Ptr_base@VBaseTrans@mu2@@@std@@IEAAXAEAV12@@Z DD 010e01H
	DD	0c20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?sendDbRequest@TaskItemTranscation@mu2@@AEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z DD 010e01H
	DD	0e20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?sendBeginRequest@TaskItemTranscation@mu2@@EEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$44@?0??createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$3@?0??createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$12@?0??createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0??createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z DB 012H
	DB	00H
	DB	00H
	DB	'9', 03H
	DB	02H
	DB	0e5H, 02H
	DB	04H
	DB	08eH
	DB	02H
	DB	09aH
	DB	00H
	DB	'a', 02H
	DB	06H
	DB	'1', 04H
	DB	08H
	DB	0a0H
	DB	06H
	DB	09aH
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z DB 08H
	DB	0eH
	DD	imagerel ?dtor$0@?0??createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z@4HA
	DB	02eH
	DD	imagerel ?dtor$12@?0??createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z@4HA
	DB	05eH
	DD	imagerel ?dtor$3@?0??createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z@4HA
	DB	02eH
	DD	imagerel ?dtor$44@?0??createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z DB 028H
	DD	imagerel $stateUnwindMap$?createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DD	imagerel $ip2state$?createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z DD 021111H
	DD	0430111H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Setup@TaskItemTranscation@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z DD 021601H
	DD	0130116H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?onResDbWShopItemTranscation@TaskItemTranscation@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z DD 021601H
	DD	0190116H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Response@TaskItemTranscation@mu2@@UEAA?AW4Error@ErrorWShop@2@PEAVIResponseParent@fcsa@@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z DD 011301H
	DD	02213H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??1TaskItemTranscation@mu2@@UEAA@XZ DB 02H
	DB	00H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??1TaskItemTranscation@mu2@@UEAA@XZ DB 060H
	DD	imagerel $ip2state$??1TaskItemTranscation@mu2@@UEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1TaskItemTranscation@mu2@@UEAA@XZ DD 010919H
	DD	06209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??1TaskItemTranscation@mu2@@UEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$1@?0???0TaskItemTranscation@mu2@@QEAA@PEAVWShopJob@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???0TaskItemTranscation@mu2@@QEAA@PEAVWShopJob@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??0TaskItemTranscation@mu2@@QEAA@PEAVWShopJob@1@@Z DB 08H
	DB	00H
	DB	00H
	DB	'<'
	DB	02H
	DB	'^'
	DB	04H
	DB	'x'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??0TaskItemTranscation@mu2@@QEAA@PEAVWShopJob@1@@Z DB 04H
	DB	0eH
	DD	imagerel ?dtor$0@?0???0TaskItemTranscation@mu2@@QEAA@PEAVWShopJob@1@@Z@4HA
	DB	02eH
	DD	imagerel ?dtor$1@?0???0TaskItemTranscation@mu2@@QEAA@PEAVWShopJob@1@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??0TaskItemTranscation@mu2@@QEAA@PEAVWShopJob@1@@Z DB 028H
	DD	imagerel $stateUnwindMap$??0TaskItemTranscation@mu2@@QEAA@PEAVWShopJob@1@@Z
	DD	imagerel $ip2state$??0TaskItemTranscation@mu2@@QEAA@PEAVWShopJob@1@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0TaskItemTranscation@mu2@@QEAA@PEAVWShopJob@1@@Z DD 010e11H
	DD	0620eH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??0TaskItemTranscation@mu2@@QEAA@PEAVWShopJob@1@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GKnightageFollowerTrans@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GKnightageGiveTrophyTrans@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GKnightageBaseTrans@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0KnightageBaseTrans@mu2@@QEAA@W4ID@BaseTrans@1@IAEAUChangedMoney@1@HH@Z DD 011701H
	DD	02217H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GCharacterSlotExtendTrans@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z DD 010901H
	DD	0a209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$_Reallocate_for@V<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@Z DB 06H
	DB	00H
	DB	00H
	DB	01dH, 07H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$_Reallocate_for@V<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@Z DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$_Reallocate_for@V<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@Z DB 028H
	DD	imagerel $stateUnwindMap$??$_Reallocate_for@V<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@Z
	DD	imagerel $ip2state$??$_Reallocate_for@V<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Reallocate_for@V<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@Z DD 021b11H
	DD	01b011bH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$_Reallocate_for@V<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_G?$_Ref_count@UEvent@mu2@@@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Delete_this@?$_Ref_count@UEvent@mu2@@@std@@EEAAXXZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Destroy@?$_Ref_count@UEvent@mu2@@@std@@EEAAXXZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$_Temporary_owner@UEvent@mu2@@@std@@QEAA@XZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z DB 06H
	DB	00H
	DB	00H
	DB	'v'
	DB	02H
	DB	05H, 03H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z DB 028H
	DD	imagerel $stateUnwindMap$??$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z
	DD	imagerel $ip2state$??$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z DD 020f11H
	DD	0700bd20fH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$swapEndian@W4MoneyType@ChangedMoney@mu2@@@PacketStream@mu2@@AEAA?AW4MoneyType@ChangedMoney@1@AEBW4231@@Z DD 010e01H
	DD	0220eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$swapEndian@W4CalcType@ChangedMoney@mu2@@@PacketStream@mu2@@AEAA?AW4CalcType@ChangedMoney@1@AEBW4231@@Z DD 010e01H
	DD	0220eH
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DB	043H
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??4?$shared_ptr@UEvent@mu2@@@std@@QEAAAEAV01@AEBV01@@Z DD 010e01H
	DD	0820eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Swap@?$_Ptr_base@UEvent@mu2@@@std@@IEAAXAEAV12@@Z DD 010e01H
	DD	0c20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$SmartPtrEx@UEvent@mu2@@@mu2@@QEAA@XZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??4WShopDbUser@mu2@@QEAAAEAU01@AEBU01@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$em@W4MoneyType@ChangedMoney@mu2@@@PacketStream@mu2@@QEAA_NAEAW4MoneyType@ChangedMoney@1@@Z DD 010e01H
	DD	0620eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$em@W4CalcType@ChangedMoney@mu2@@@PacketStream@mu2@@QEAA_NAEAW4CalcType@ChangedMoney@1@@Z DD 010e01H
	DD	0620eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GChangedMoney@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?PackUnpack@ChangedMoney@mu2@@UEAA_NAEAVPacketStream@2@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GDateTimeEx@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Reset@DateTimeEx@mu2@@UEAAXXZ DD 010901H
	DD	02209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?PackUnpack@DateTimeEx@mu2@@UEAA_NAEAVPacketStream@2@@Z DD 010e01H
	DD	0820eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??BDateTimeEx@mu2@@QEBA?B_JXZ DD 010901H
	DD	0e209H
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DB	018H
	DB	0dbH
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??4DateTimeEx@mu2@@QEAAAEAU01@AEB_J@Z DD 011d19H
	DD	0a20eH
	DD	imagerel __GSHandlerCheck
	DD	040H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0DateTimeEx@mu2@@QEAA@XZ DD 010901H
	DD	02209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GISerializer@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DB	018H
	DB	070H
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$swapEndian@_J@PacketStream@mu2@@AEAA_JAEB_J@Z DD 011d19H
	DD	0420eH
	DD	imagerel __GSHandlerCheck
	DD	018H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?checkingBuffer@PacketStream@mu2@@AEAA_NXZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$rw@_J@PacketStream@mu2@@QEAA_NAEA_J@Z DD 010e01H
	DD	0620eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Read@PacketStream@mu2@@QEAAIPEAEI@Z DD 021601H
	DD	0110116H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Write@PacketStream@mu2@@QEAAIPEBEI@Z DD 021601H
	DD	0110116H
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DB	017H
	DB	040H
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Decref@_Ref_count_base@std@@QEAAXXZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Calculate_growth@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@CA_K_K00@Z DD 011301H
	DD	06213H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ DD 020c01H
	DD	011010cH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?assign@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV12@QEB_W_K@Z DD 021401H
	DD	070105214H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV01@AEBV01@@Z DD 021101H
	DD	0130111H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEAAPEA_WXZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Xlen_string@std@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Throw_bad_array_new_length@std@@YAXXZ DD 010401H
	DD	08204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1bad_array_new_length@std@@UEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_array_new_length@std@@QEAA@XZ DD 010601H
	DD	07006H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z DD 020f01H
	DD	0700b320fH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gexception@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?what@exception@std@@UEBAPEBDXZ DD 010901H
	DD	02209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0exception@std@@QEAA@AEBV01@@Z DD 020f01H
	DD	0700b320fH
xdata	ENDS
; Function compile flags: /Odtp
;	COMDAT ??_G?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_G?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@UEAAPEAXI@Z PROC ; std::_Ref_count<mu2::CharacterSlotExtendTrans>::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000d	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00011	83 e0 01	 and	 eax, 1
  00014	85 c0		 test	 eax, eax
  00016	74 10		 je	 SHORT $LN2@scalar
  00018	ba 18 00 00 00	 mov	 edx, 24
  0001d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00022	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00027	90		 npad	 1
$LN2@scalar:
  00028	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0002d	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00031	c3		 ret	 0
??_G?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@UEAAPEAXI@Z ENDP ; std::_Ref_count<mu2::CharacterSlotExtendTrans>::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ?_Delete_this@?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@EEAAXXZ
_TEXT	SEGMENT
$T1 = 32
tv74 = 40
this$ = 64
?_Delete_this@?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@EEAAXXZ PROC ; std::_Ref_count<mu2::CharacterSlotExtendTrans>::_Delete_this, COMDAT

; 1190 :     void _Delete_this() noexcept override { // destroy self

$LN6:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 1191 :         delete this;

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
  00013	48 83 7c 24 20
	00		 cmp	 QWORD PTR $T1[rsp], 0
  00019	74 1c		 je	 SHORT $LN3@Delete_thi
  0001b	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00020	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00023	ba 01 00 00 00	 mov	 edx, 1
  00028	48 8b 4c 24 20	 mov	 rcx, QWORD PTR $T1[rsp]
  0002d	ff 50 10	 call	 QWORD PTR [rax+16]
  00030	48 89 44 24 28	 mov	 QWORD PTR tv74[rsp], rax
  00035	eb 09		 jmp	 SHORT $LN4@Delete_thi
$LN3@Delete_thi:
  00037	48 c7 44 24 28
	00 00 00 00	 mov	 QWORD PTR tv74[rsp], 0
$LN4@Delete_thi:

; 1192 :     }

  00040	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00044	c3		 ret	 0
?_Delete_this@?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@EEAAXXZ ENDP ; std::_Ref_count<mu2::CharacterSlotExtendTrans>::_Delete_this
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ?_Destroy@?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@EEAAXXZ
_TEXT	SEGMENT
$T1 = 32
tv71 = 40
this$ = 64
?_Destroy@?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@EEAAXXZ PROC ; std::_Ref_count<mu2::CharacterSlotExtendTrans>::_Destroy, COMDAT

; 1186 :     void _Destroy() noexcept override { // destroy managed resource

$LN6:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 1187 :         delete _Ptr;

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00012	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
  00017	48 83 7c 24 20
	00		 cmp	 QWORD PTR $T1[rsp], 0
  0001d	74 1c		 je	 SHORT $LN3@Destroy
  0001f	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00024	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00027	ba 01 00 00 00	 mov	 edx, 1
  0002c	48 8b 4c 24 20	 mov	 rcx, QWORD PTR $T1[rsp]
  00031	ff 50 18	 call	 QWORD PTR [rax+24]
  00034	48 89 44 24 28	 mov	 QWORD PTR tv71[rsp], rax
  00039	eb 09		 jmp	 SHORT $LN4@Destroy
$LN3@Destroy:
  0003b	48 c7 44 24 28
	00 00 00 00	 mov	 QWORD PTR tv71[rsp], 0
$LN4@Destroy:

; 1188 :     }

  00044	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00048	c3		 ret	 0
?_Destroy@?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@EEAAXXZ ENDP ; std::_Ref_count<mu2::CharacterSlotExtendTrans>::_Destroy
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ??1?$_Temporary_owner@VCharacterSlotExtendTrans@mu2@@@std@@QEAA@XZ
_TEXT	SEGMENT
$T1 = 32
tv71 = 40
this$ = 64
??1?$_Temporary_owner@VCharacterSlotExtendTrans@mu2@@@std@@QEAA@XZ PROC ; std::_Temporary_owner<mu2::CharacterSlotExtendTrans>::~_Temporary_owner<mu2::CharacterSlotExtendTrans>, COMDAT

; 1529 :     ~_Temporary_owner() {

$LN6:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 1530 :         delete _Ptr;

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00011	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
  00016	48 83 7c 24 20
	00		 cmp	 QWORD PTR $T1[rsp], 0
  0001c	74 1c		 je	 SHORT $LN3@Temporary_
  0001e	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00023	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00026	ba 01 00 00 00	 mov	 edx, 1
  0002b	48 8b 4c 24 20	 mov	 rcx, QWORD PTR $T1[rsp]
  00030	ff 50 18	 call	 QWORD PTR [rax+24]
  00033	48 89 44 24 28	 mov	 QWORD PTR tv71[rsp], rax
  00038	eb 09		 jmp	 SHORT $LN4@Temporary_
$LN3@Temporary_:
  0003a	48 c7 44 24 28
	00 00 00 00	 mov	 QWORD PTR tv71[rsp], 0
$LN4@Temporary_:

; 1531 :     }

  00043	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00047	c3		 ret	 0
??1?$_Temporary_owner@VCharacterSlotExtendTrans@mu2@@@std@@QEAA@XZ ENDP ; std::_Temporary_owner<mu2::CharacterSlotExtendTrans>::~_Temporary_owner<mu2::CharacterSlotExtendTrans>
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??_G?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_G?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@UEAAPEAXI@Z PROC ; std::_Ref_count<mu2::KnightageGiveTrophyTrans>::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000d	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00011	83 e0 01	 and	 eax, 1
  00014	85 c0		 test	 eax, eax
  00016	74 10		 je	 SHORT $LN2@scalar
  00018	ba 18 00 00 00	 mov	 edx, 24
  0001d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00022	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00027	90		 npad	 1
$LN2@scalar:
  00028	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0002d	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00031	c3		 ret	 0
??_G?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@UEAAPEAXI@Z ENDP ; std::_Ref_count<mu2::KnightageGiveTrophyTrans>::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ?_Delete_this@?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@EEAAXXZ
_TEXT	SEGMENT
$T1 = 32
tv74 = 40
this$ = 64
?_Delete_this@?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@EEAAXXZ PROC ; std::_Ref_count<mu2::KnightageGiveTrophyTrans>::_Delete_this, COMDAT

; 1190 :     void _Delete_this() noexcept override { // destroy self

$LN6:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 1191 :         delete this;

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
  00013	48 83 7c 24 20
	00		 cmp	 QWORD PTR $T1[rsp], 0
  00019	74 1c		 je	 SHORT $LN3@Delete_thi
  0001b	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00020	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00023	ba 01 00 00 00	 mov	 edx, 1
  00028	48 8b 4c 24 20	 mov	 rcx, QWORD PTR $T1[rsp]
  0002d	ff 50 10	 call	 QWORD PTR [rax+16]
  00030	48 89 44 24 28	 mov	 QWORD PTR tv74[rsp], rax
  00035	eb 09		 jmp	 SHORT $LN4@Delete_thi
$LN3@Delete_thi:
  00037	48 c7 44 24 28
	00 00 00 00	 mov	 QWORD PTR tv74[rsp], 0
$LN4@Delete_thi:

; 1192 :     }

  00040	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00044	c3		 ret	 0
?_Delete_this@?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@EEAAXXZ ENDP ; std::_Ref_count<mu2::KnightageGiveTrophyTrans>::_Delete_this
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ?_Destroy@?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@EEAAXXZ
_TEXT	SEGMENT
$T1 = 32
tv75 = 40
this$ = 64
?_Destroy@?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@EEAAXXZ PROC ; std::_Ref_count<mu2::KnightageGiveTrophyTrans>::_Destroy, COMDAT

; 1186 :     void _Destroy() noexcept override { // destroy managed resource

$LN6:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 1187 :         delete _Ptr;

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00012	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
  00017	48 83 7c 24 20
	00		 cmp	 QWORD PTR $T1[rsp], 0
  0001d	74 1c		 je	 SHORT $LN3@Destroy
  0001f	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00024	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00027	ba 01 00 00 00	 mov	 edx, 1
  0002c	48 8b 4c 24 20	 mov	 rcx, QWORD PTR $T1[rsp]
  00031	ff 50 18	 call	 QWORD PTR [rax+24]
  00034	48 89 44 24 28	 mov	 QWORD PTR tv75[rsp], rax
  00039	eb 09		 jmp	 SHORT $LN4@Destroy
$LN3@Destroy:
  0003b	48 c7 44 24 28
	00 00 00 00	 mov	 QWORD PTR tv75[rsp], 0
$LN4@Destroy:

; 1188 :     }

  00044	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00048	c3		 ret	 0
?_Destroy@?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@EEAAXXZ ENDP ; std::_Ref_count<mu2::KnightageGiveTrophyTrans>::_Destroy
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ??1?$_Temporary_owner@VKnightageGiveTrophyTrans@mu2@@@std@@QEAA@XZ
_TEXT	SEGMENT
$T1 = 32
tv75 = 40
this$ = 64
??1?$_Temporary_owner@VKnightageGiveTrophyTrans@mu2@@@std@@QEAA@XZ PROC ; std::_Temporary_owner<mu2::KnightageGiveTrophyTrans>::~_Temporary_owner<mu2::KnightageGiveTrophyTrans>, COMDAT

; 1529 :     ~_Temporary_owner() {

$LN6:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 1530 :         delete _Ptr;

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00011	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
  00016	48 83 7c 24 20
	00		 cmp	 QWORD PTR $T1[rsp], 0
  0001c	74 1c		 je	 SHORT $LN3@Temporary_
  0001e	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00023	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00026	ba 01 00 00 00	 mov	 edx, 1
  0002b	48 8b 4c 24 20	 mov	 rcx, QWORD PTR $T1[rsp]
  00030	ff 50 18	 call	 QWORD PTR [rax+24]
  00033	48 89 44 24 28	 mov	 QWORD PTR tv75[rsp], rax
  00038	eb 09		 jmp	 SHORT $LN4@Temporary_
$LN3@Temporary_:
  0003a	48 c7 44 24 28
	00 00 00 00	 mov	 QWORD PTR tv75[rsp], 0
$LN4@Temporary_:

; 1531 :     }

  00043	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00047	c3		 ret	 0
??1?$_Temporary_owner@VKnightageGiveTrophyTrans@mu2@@@std@@QEAA@XZ ENDP ; std::_Temporary_owner<mu2::KnightageGiveTrophyTrans>::~_Temporary_owner<mu2::KnightageGiveTrophyTrans>
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??_G?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_G?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@UEAAPEAXI@Z PROC ; std::_Ref_count<mu2::KnightageFollowerTrans>::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000d	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00011	83 e0 01	 and	 eax, 1
  00014	85 c0		 test	 eax, eax
  00016	74 10		 je	 SHORT $LN2@scalar
  00018	ba 18 00 00 00	 mov	 edx, 24
  0001d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00022	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00027	90		 npad	 1
$LN2@scalar:
  00028	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0002d	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00031	c3		 ret	 0
??_G?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@UEAAPEAXI@Z ENDP ; std::_Ref_count<mu2::KnightageFollowerTrans>::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ?_Delete_this@?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@EEAAXXZ
_TEXT	SEGMENT
$T1 = 32
tv74 = 40
this$ = 64
?_Delete_this@?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@EEAAXXZ PROC ; std::_Ref_count<mu2::KnightageFollowerTrans>::_Delete_this, COMDAT

; 1190 :     void _Delete_this() noexcept override { // destroy self

$LN6:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 1191 :         delete this;

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
  00013	48 83 7c 24 20
	00		 cmp	 QWORD PTR $T1[rsp], 0
  00019	74 1c		 je	 SHORT $LN3@Delete_thi
  0001b	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00020	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00023	ba 01 00 00 00	 mov	 edx, 1
  00028	48 8b 4c 24 20	 mov	 rcx, QWORD PTR $T1[rsp]
  0002d	ff 50 10	 call	 QWORD PTR [rax+16]
  00030	48 89 44 24 28	 mov	 QWORD PTR tv74[rsp], rax
  00035	eb 09		 jmp	 SHORT $LN4@Delete_thi
$LN3@Delete_thi:
  00037	48 c7 44 24 28
	00 00 00 00	 mov	 QWORD PTR tv74[rsp], 0
$LN4@Delete_thi:

; 1192 :     }

  00040	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00044	c3		 ret	 0
?_Delete_this@?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@EEAAXXZ ENDP ; std::_Ref_count<mu2::KnightageFollowerTrans>::_Delete_this
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ?_Destroy@?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@EEAAXXZ
_TEXT	SEGMENT
$T1 = 32
tv75 = 40
this$ = 64
?_Destroy@?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@EEAAXXZ PROC ; std::_Ref_count<mu2::KnightageFollowerTrans>::_Destroy, COMDAT

; 1186 :     void _Destroy() noexcept override { // destroy managed resource

$LN6:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 1187 :         delete _Ptr;

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00012	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
  00017	48 83 7c 24 20
	00		 cmp	 QWORD PTR $T1[rsp], 0
  0001d	74 1c		 je	 SHORT $LN3@Destroy
  0001f	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00024	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00027	ba 01 00 00 00	 mov	 edx, 1
  0002c	48 8b 4c 24 20	 mov	 rcx, QWORD PTR $T1[rsp]
  00031	ff 50 18	 call	 QWORD PTR [rax+24]
  00034	48 89 44 24 28	 mov	 QWORD PTR tv75[rsp], rax
  00039	eb 09		 jmp	 SHORT $LN4@Destroy
$LN3@Destroy:
  0003b	48 c7 44 24 28
	00 00 00 00	 mov	 QWORD PTR tv75[rsp], 0
$LN4@Destroy:

; 1188 :     }

  00044	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00048	c3		 ret	 0
?_Destroy@?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@EEAAXXZ ENDP ; std::_Ref_count<mu2::KnightageFollowerTrans>::_Destroy
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ??1?$_Temporary_owner@VKnightageFollowerTrans@mu2@@@std@@QEAA@XZ
_TEXT	SEGMENT
$T1 = 32
tv75 = 40
this$ = 64
??1?$_Temporary_owner@VKnightageFollowerTrans@mu2@@@std@@QEAA@XZ PROC ; std::_Temporary_owner<mu2::KnightageFollowerTrans>::~_Temporary_owner<mu2::KnightageFollowerTrans>, COMDAT

; 1529 :     ~_Temporary_owner() {

$LN6:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 1530 :         delete _Ptr;

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00011	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
  00016	48 83 7c 24 20
	00		 cmp	 QWORD PTR $T1[rsp], 0
  0001c	74 1c		 je	 SHORT $LN3@Temporary_
  0001e	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00023	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00026	ba 01 00 00 00	 mov	 edx, 1
  0002b	48 8b 4c 24 20	 mov	 rcx, QWORD PTR $T1[rsp]
  00030	ff 50 18	 call	 QWORD PTR [rax+24]
  00033	48 89 44 24 28	 mov	 QWORD PTR tv75[rsp], rax
  00038	eb 09		 jmp	 SHORT $LN4@Temporary_
$LN3@Temporary_:
  0003a	48 c7 44 24 28
	00 00 00 00	 mov	 QWORD PTR tv75[rsp], 0
$LN4@Temporary_:

; 1531 :     }

  00043	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00047	c3		 ret	 0
??1?$_Temporary_owner@VKnightageFollowerTrans@mu2@@@std@@QEAA@XZ ENDP ; std::_Temporary_owner<mu2::KnightageFollowerTrans>::~_Temporary_owner<mu2::KnightageFollowerTrans>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ??$?0VCharacterSlotExtendTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVCharacterSlotExtendTrans@mu2@@@Z
_TEXT	SEGMENT
$T1 = 32
_Owner$2 = 40
$T3 = 48
tv89 = 56
_Px$ = 64
$T4 = 72
_Px$ = 80
$T5 = 88
tv170 = 96
this$ = 128
_Px$ = 136
??$?0VCharacterSlotExtendTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVCharacterSlotExtendTrans@mu2@@@Z PROC ; std::shared_ptr<mu2::BaseTrans>::shared_ptr<mu2::BaseTrans><mu2::CharacterSlotExtendTrans,0>, COMDAT

; 1584 :     explicit shared_ptr(_Ux* _Px) { // construct shared_ptr object that owns _Px

$LN37:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 70	 sub	 rsp, 112		; 00000070H

; 1452 :     element_type* _Ptr{nullptr};

  0000f	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00017	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0

; 1453 :     _Ref_count_base* _Rep{nullptr};

  0001e	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00026	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0

; 1526 :     explicit _Temporary_owner(_Ux* const _Ptr_) noexcept : _Ptr(_Ptr_) {}

  0002e	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR _Px$[rsp]
  00036	48 89 44 24 28	 mov	 QWORD PTR _Owner$2[rsp], rax

; 1585 :         if constexpr (is_array_v<_Ty>) {
; 1586 :             _Setpd(_Px, default_delete<_Ux[]>{});
; 1587 :         } else {
; 1588 :             _Temporary_owner<_Ux> _Owner(_Px);
; 1589 :             _Set_ptr_rep_and_enable_shared(_Owner._Ptr, new _Ref_count<_Ux>(_Owner._Ptr));

  0003b	b9 18 00 00 00	 mov	 ecx, 24
  00040	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00045	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
  0004a	48 83 7c 24 20
	00		 cmp	 QWORD PTR $T1[rsp], 0
  00050	74 63		 je	 SHORT $LN3@BaseTrans
  00052	48 8b 44 24 28	 mov	 rax, QWORD PTR _Owner$2[rsp]
  00057	48 89 44 24 40	 mov	 QWORD PTR _Px$[rsp], rax

; 1183 :     explicit _Ref_count(_Ty* _Px) : _Ref_count_base(), _Ptr(_Px) {}

  0005c	48 8b 7c 24 20	 mov	 rdi, QWORD PTR $T1[rsp]
  00061	33 c0		 xor	 eax, eax
  00063	b9 10 00 00 00	 mov	 ecx, 16
  00068	f3 aa		 rep stosb

; 1119 :     _Atomic_counter_t _Uses  = 1;

  0006a	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  0006f	c7 40 08 01 00
	00 00		 mov	 DWORD PTR [rax+8], 1

; 1120 :     _Atomic_counter_t _Weaks = 1;

  00076	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  0007b	c7 40 0c 01 00
	00 00		 mov	 DWORD PTR [rax+12], 1

; 1183 :     explicit _Ref_count(_Ty* _Px) : _Ref_count_base(), _Ptr(_Px) {}

  00082	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00087	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$_Ref_count@VCharacterSlotExtendTrans@mu2@@@std@@6B@
  0008e	48 89 08	 mov	 QWORD PTR [rax], rcx
  00091	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00096	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Px$[rsp]
  0009b	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx
  0009f	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  000a4	48 89 44 24 48	 mov	 QWORD PTR $T4[rsp], rax

; 1585 :         if constexpr (is_array_v<_Ty>) {
; 1586 :             _Setpd(_Px, default_delete<_Ux[]>{});
; 1587 :         } else {
; 1588 :             _Temporary_owner<_Ux> _Owner(_Px);
; 1589 :             _Set_ptr_rep_and_enable_shared(_Owner._Ptr, new _Ref_count<_Ux>(_Owner._Ptr));

  000a9	48 8b 44 24 48	 mov	 rax, QWORD PTR $T4[rsp]
  000ae	48 89 44 24 38	 mov	 QWORD PTR tv89[rsp], rax
  000b3	eb 09		 jmp	 SHORT $LN4@BaseTrans
$LN3@BaseTrans:
  000b5	48 c7 44 24 38
	00 00 00 00	 mov	 QWORD PTR tv89[rsp], 0
$LN4@BaseTrans:
  000be	48 8b 44 24 38	 mov	 rax, QWORD PTR tv89[rsp]
  000c3	48 89 44 24 58	 mov	 QWORD PTR $T5[rsp], rax
  000c8	48 8b 44 24 28	 mov	 rax, QWORD PTR _Owner$2[rsp]
  000cd	48 89 44 24 50	 mov	 QWORD PTR _Px$[rsp], rax

; 1856 :         this->_Ptr = _Px;

  000d2	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000da	48 8b 4c 24 50	 mov	 rcx, QWORD PTR _Px$[rsp]
  000df	48 89 08	 mov	 QWORD PTR [rax], rcx

; 1857 :         this->_Rep = _Rx;

  000e2	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000ea	48 8b 4c 24 58	 mov	 rcx, QWORD PTR $T5[rsp]
  000ef	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 1590 :             _Owner._Ptr = nullptr;

  000f3	48 c7 44 24 28
	00 00 00 00	 mov	 QWORD PTR _Owner$2[rsp], 0

; 1530 :         delete _Ptr;

  000fc	48 8b 44 24 28	 mov	 rax, QWORD PTR _Owner$2[rsp]
  00101	48 89 44 24 30	 mov	 QWORD PTR $T3[rsp], rax
  00106	48 83 7c 24 30
	00		 cmp	 QWORD PTR $T3[rsp], 0
  0010c	74 1c		 je	 SHORT $LN32@BaseTrans
  0010e	48 8b 44 24 30	 mov	 rax, QWORD PTR $T3[rsp]
  00113	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00116	ba 01 00 00 00	 mov	 edx, 1
  0011b	48 8b 4c 24 30	 mov	 rcx, QWORD PTR $T3[rsp]
  00120	ff 50 18	 call	 QWORD PTR [rax+24]
  00123	48 89 44 24 60	 mov	 QWORD PTR tv170[rsp], rax
  00128	eb 09		 jmp	 SHORT $LN33@BaseTrans
$LN32@BaseTrans:
  0012a	48 c7 44 24 60
	00 00 00 00	 mov	 QWORD PTR tv170[rsp], 0
$LN33@BaseTrans:

; 1591 :         }
; 1592 :     }

  00133	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0013b	48 83 c4 70	 add	 rsp, 112		; 00000070H
  0013f	5f		 pop	 rdi
  00140	c3		 ret	 0
??$?0VCharacterSlotExtendTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVCharacterSlotExtendTrans@mu2@@@Z ENDP ; std::shared_ptr<mu2::BaseTrans>::shared_ptr<mu2::BaseTrans><mu2::CharacterSlotExtendTrans,0>
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
_Owner$2 = 40
$T3 = 48
tv89 = 56
_Px$ = 64
$T4 = 72
_Px$ = 80
$T5 = 88
tv170 = 96
this$ = 128
_Px$ = 136
?dtor$0@?0???$?0VCharacterSlotExtendTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVCharacterSlotExtendTrans@mu2@@@Z@4HA PROC ; `std::shared_ptr<mu2::BaseTrans>::shared_ptr<mu2::BaseTrans><mu2::CharacterSlotExtendTrans,0>'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8d 4d 28	 lea	 rcx, QWORD PTR _Owner$2[rbp]
  0000d	e8 00 00 00 00	 call	 ??1?$_Temporary_owner@VCharacterSlotExtendTrans@mu2@@@std@@QEAA@XZ ; std::_Temporary_owner<mu2::CharacterSlotExtendTrans>::~_Temporary_owner<mu2::CharacterSlotExtendTrans>
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$0@?0???$?0VCharacterSlotExtendTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVCharacterSlotExtendTrans@mu2@@@Z@4HA ENDP ; `std::shared_ptr<mu2::BaseTrans>::shared_ptr<mu2::BaseTrans><mu2::CharacterSlotExtendTrans,0>'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ??$?0VKnightageGiveTrophyTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageGiveTrophyTrans@mu2@@@Z
_TEXT	SEGMENT
$T1 = 32
_Owner$2 = 40
$T3 = 48
tv89 = 56
_Px$ = 64
$T4 = 72
_Px$ = 80
$T5 = 88
tv174 = 96
this$ = 128
_Px$ = 136
??$?0VKnightageGiveTrophyTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageGiveTrophyTrans@mu2@@@Z PROC ; std::shared_ptr<mu2::BaseTrans>::shared_ptr<mu2::BaseTrans><mu2::KnightageGiveTrophyTrans,0>, COMDAT

; 1584 :     explicit shared_ptr(_Ux* _Px) { // construct shared_ptr object that owns _Px

$LN37:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 70	 sub	 rsp, 112		; 00000070H

; 1452 :     element_type* _Ptr{nullptr};

  0000f	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00017	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0

; 1453 :     _Ref_count_base* _Rep{nullptr};

  0001e	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00026	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0

; 1526 :     explicit _Temporary_owner(_Ux* const _Ptr_) noexcept : _Ptr(_Ptr_) {}

  0002e	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR _Px$[rsp]
  00036	48 89 44 24 28	 mov	 QWORD PTR _Owner$2[rsp], rax

; 1585 :         if constexpr (is_array_v<_Ty>) {
; 1586 :             _Setpd(_Px, default_delete<_Ux[]>{});
; 1587 :         } else {
; 1588 :             _Temporary_owner<_Ux> _Owner(_Px);
; 1589 :             _Set_ptr_rep_and_enable_shared(_Owner._Ptr, new _Ref_count<_Ux>(_Owner._Ptr));

  0003b	b9 18 00 00 00	 mov	 ecx, 24
  00040	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00045	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
  0004a	48 83 7c 24 20
	00		 cmp	 QWORD PTR $T1[rsp], 0
  00050	74 63		 je	 SHORT $LN3@BaseTrans
  00052	48 8b 44 24 28	 mov	 rax, QWORD PTR _Owner$2[rsp]
  00057	48 89 44 24 40	 mov	 QWORD PTR _Px$[rsp], rax

; 1183 :     explicit _Ref_count(_Ty* _Px) : _Ref_count_base(), _Ptr(_Px) {}

  0005c	48 8b 7c 24 20	 mov	 rdi, QWORD PTR $T1[rsp]
  00061	33 c0		 xor	 eax, eax
  00063	b9 10 00 00 00	 mov	 ecx, 16
  00068	f3 aa		 rep stosb

; 1119 :     _Atomic_counter_t _Uses  = 1;

  0006a	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  0006f	c7 40 08 01 00
	00 00		 mov	 DWORD PTR [rax+8], 1

; 1120 :     _Atomic_counter_t _Weaks = 1;

  00076	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  0007b	c7 40 0c 01 00
	00 00		 mov	 DWORD PTR [rax+12], 1

; 1183 :     explicit _Ref_count(_Ty* _Px) : _Ref_count_base(), _Ptr(_Px) {}

  00082	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00087	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$_Ref_count@VKnightageGiveTrophyTrans@mu2@@@std@@6B@
  0008e	48 89 08	 mov	 QWORD PTR [rax], rcx
  00091	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00096	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Px$[rsp]
  0009b	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx
  0009f	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  000a4	48 89 44 24 48	 mov	 QWORD PTR $T4[rsp], rax

; 1585 :         if constexpr (is_array_v<_Ty>) {
; 1586 :             _Setpd(_Px, default_delete<_Ux[]>{});
; 1587 :         } else {
; 1588 :             _Temporary_owner<_Ux> _Owner(_Px);
; 1589 :             _Set_ptr_rep_and_enable_shared(_Owner._Ptr, new _Ref_count<_Ux>(_Owner._Ptr));

  000a9	48 8b 44 24 48	 mov	 rax, QWORD PTR $T4[rsp]
  000ae	48 89 44 24 38	 mov	 QWORD PTR tv89[rsp], rax
  000b3	eb 09		 jmp	 SHORT $LN4@BaseTrans
$LN3@BaseTrans:
  000b5	48 c7 44 24 38
	00 00 00 00	 mov	 QWORD PTR tv89[rsp], 0
$LN4@BaseTrans:
  000be	48 8b 44 24 38	 mov	 rax, QWORD PTR tv89[rsp]
  000c3	48 89 44 24 58	 mov	 QWORD PTR $T5[rsp], rax
  000c8	48 8b 44 24 28	 mov	 rax, QWORD PTR _Owner$2[rsp]
  000cd	48 89 44 24 50	 mov	 QWORD PTR _Px$[rsp], rax

; 1856 :         this->_Ptr = _Px;

  000d2	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000da	48 8b 4c 24 50	 mov	 rcx, QWORD PTR _Px$[rsp]
  000df	48 89 08	 mov	 QWORD PTR [rax], rcx

; 1857 :         this->_Rep = _Rx;

  000e2	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000ea	48 8b 4c 24 58	 mov	 rcx, QWORD PTR $T5[rsp]
  000ef	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 1590 :             _Owner._Ptr = nullptr;

  000f3	48 c7 44 24 28
	00 00 00 00	 mov	 QWORD PTR _Owner$2[rsp], 0

; 1530 :         delete _Ptr;

  000fc	48 8b 44 24 28	 mov	 rax, QWORD PTR _Owner$2[rsp]
  00101	48 89 44 24 30	 mov	 QWORD PTR $T3[rsp], rax
  00106	48 83 7c 24 30
	00		 cmp	 QWORD PTR $T3[rsp], 0
  0010c	74 1c		 je	 SHORT $LN32@BaseTrans
  0010e	48 8b 44 24 30	 mov	 rax, QWORD PTR $T3[rsp]
  00113	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00116	ba 01 00 00 00	 mov	 edx, 1
  0011b	48 8b 4c 24 30	 mov	 rcx, QWORD PTR $T3[rsp]
  00120	ff 50 18	 call	 QWORD PTR [rax+24]
  00123	48 89 44 24 60	 mov	 QWORD PTR tv174[rsp], rax
  00128	eb 09		 jmp	 SHORT $LN33@BaseTrans
$LN32@BaseTrans:
  0012a	48 c7 44 24 60
	00 00 00 00	 mov	 QWORD PTR tv174[rsp], 0
$LN33@BaseTrans:

; 1591 :         }
; 1592 :     }

  00133	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0013b	48 83 c4 70	 add	 rsp, 112		; 00000070H
  0013f	5f		 pop	 rdi
  00140	c3		 ret	 0
??$?0VKnightageGiveTrophyTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageGiveTrophyTrans@mu2@@@Z ENDP ; std::shared_ptr<mu2::BaseTrans>::shared_ptr<mu2::BaseTrans><mu2::KnightageGiveTrophyTrans,0>
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
_Owner$2 = 40
$T3 = 48
tv89 = 56
_Px$ = 64
$T4 = 72
_Px$ = 80
$T5 = 88
tv174 = 96
this$ = 128
_Px$ = 136
?dtor$0@?0???$?0VKnightageGiveTrophyTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageGiveTrophyTrans@mu2@@@Z@4HA PROC ; `std::shared_ptr<mu2::BaseTrans>::shared_ptr<mu2::BaseTrans><mu2::KnightageGiveTrophyTrans,0>'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8d 4d 28	 lea	 rcx, QWORD PTR _Owner$2[rbp]
  0000d	e8 00 00 00 00	 call	 ??1?$_Temporary_owner@VKnightageGiveTrophyTrans@mu2@@@std@@QEAA@XZ ; std::_Temporary_owner<mu2::KnightageGiveTrophyTrans>::~_Temporary_owner<mu2::KnightageGiveTrophyTrans>
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$0@?0???$?0VKnightageGiveTrophyTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageGiveTrophyTrans@mu2@@@Z@4HA ENDP ; `std::shared_ptr<mu2::BaseTrans>::shared_ptr<mu2::BaseTrans><mu2::KnightageGiveTrophyTrans,0>'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ??$?0VKnightageFollowerTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageFollowerTrans@mu2@@@Z
_TEXT	SEGMENT
$T1 = 32
_Owner$2 = 40
$T3 = 48
tv89 = 56
_Px$ = 64
$T4 = 72
_Px$ = 80
$T5 = 88
tv174 = 96
this$ = 128
_Px$ = 136
??$?0VKnightageFollowerTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageFollowerTrans@mu2@@@Z PROC ; std::shared_ptr<mu2::BaseTrans>::shared_ptr<mu2::BaseTrans><mu2::KnightageFollowerTrans,0>, COMDAT

; 1584 :     explicit shared_ptr(_Ux* _Px) { // construct shared_ptr object that owns _Px

$LN37:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 70	 sub	 rsp, 112		; 00000070H

; 1452 :     element_type* _Ptr{nullptr};

  0000f	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00017	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0

; 1453 :     _Ref_count_base* _Rep{nullptr};

  0001e	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00026	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0

; 1526 :     explicit _Temporary_owner(_Ux* const _Ptr_) noexcept : _Ptr(_Ptr_) {}

  0002e	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR _Px$[rsp]
  00036	48 89 44 24 28	 mov	 QWORD PTR _Owner$2[rsp], rax

; 1585 :         if constexpr (is_array_v<_Ty>) {
; 1586 :             _Setpd(_Px, default_delete<_Ux[]>{});
; 1587 :         } else {
; 1588 :             _Temporary_owner<_Ux> _Owner(_Px);
; 1589 :             _Set_ptr_rep_and_enable_shared(_Owner._Ptr, new _Ref_count<_Ux>(_Owner._Ptr));

  0003b	b9 18 00 00 00	 mov	 ecx, 24
  00040	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00045	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
  0004a	48 83 7c 24 20
	00		 cmp	 QWORD PTR $T1[rsp], 0
  00050	74 63		 je	 SHORT $LN3@BaseTrans
  00052	48 8b 44 24 28	 mov	 rax, QWORD PTR _Owner$2[rsp]
  00057	48 89 44 24 40	 mov	 QWORD PTR _Px$[rsp], rax

; 1183 :     explicit _Ref_count(_Ty* _Px) : _Ref_count_base(), _Ptr(_Px) {}

  0005c	48 8b 7c 24 20	 mov	 rdi, QWORD PTR $T1[rsp]
  00061	33 c0		 xor	 eax, eax
  00063	b9 10 00 00 00	 mov	 ecx, 16
  00068	f3 aa		 rep stosb

; 1119 :     _Atomic_counter_t _Uses  = 1;

  0006a	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  0006f	c7 40 08 01 00
	00 00		 mov	 DWORD PTR [rax+8], 1

; 1120 :     _Atomic_counter_t _Weaks = 1;

  00076	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  0007b	c7 40 0c 01 00
	00 00		 mov	 DWORD PTR [rax+12], 1

; 1183 :     explicit _Ref_count(_Ty* _Px) : _Ref_count_base(), _Ptr(_Px) {}

  00082	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00087	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$_Ref_count@VKnightageFollowerTrans@mu2@@@std@@6B@
  0008e	48 89 08	 mov	 QWORD PTR [rax], rcx
  00091	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00096	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Px$[rsp]
  0009b	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx
  0009f	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  000a4	48 89 44 24 48	 mov	 QWORD PTR $T4[rsp], rax

; 1585 :         if constexpr (is_array_v<_Ty>) {
; 1586 :             _Setpd(_Px, default_delete<_Ux[]>{});
; 1587 :         } else {
; 1588 :             _Temporary_owner<_Ux> _Owner(_Px);
; 1589 :             _Set_ptr_rep_and_enable_shared(_Owner._Ptr, new _Ref_count<_Ux>(_Owner._Ptr));

  000a9	48 8b 44 24 48	 mov	 rax, QWORD PTR $T4[rsp]
  000ae	48 89 44 24 38	 mov	 QWORD PTR tv89[rsp], rax
  000b3	eb 09		 jmp	 SHORT $LN4@BaseTrans
$LN3@BaseTrans:
  000b5	48 c7 44 24 38
	00 00 00 00	 mov	 QWORD PTR tv89[rsp], 0
$LN4@BaseTrans:
  000be	48 8b 44 24 38	 mov	 rax, QWORD PTR tv89[rsp]
  000c3	48 89 44 24 58	 mov	 QWORD PTR $T5[rsp], rax
  000c8	48 8b 44 24 28	 mov	 rax, QWORD PTR _Owner$2[rsp]
  000cd	48 89 44 24 50	 mov	 QWORD PTR _Px$[rsp], rax

; 1856 :         this->_Ptr = _Px;

  000d2	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000da	48 8b 4c 24 50	 mov	 rcx, QWORD PTR _Px$[rsp]
  000df	48 89 08	 mov	 QWORD PTR [rax], rcx

; 1857 :         this->_Rep = _Rx;

  000e2	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000ea	48 8b 4c 24 58	 mov	 rcx, QWORD PTR $T5[rsp]
  000ef	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 1590 :             _Owner._Ptr = nullptr;

  000f3	48 c7 44 24 28
	00 00 00 00	 mov	 QWORD PTR _Owner$2[rsp], 0

; 1530 :         delete _Ptr;

  000fc	48 8b 44 24 28	 mov	 rax, QWORD PTR _Owner$2[rsp]
  00101	48 89 44 24 30	 mov	 QWORD PTR $T3[rsp], rax
  00106	48 83 7c 24 30
	00		 cmp	 QWORD PTR $T3[rsp], 0
  0010c	74 1c		 je	 SHORT $LN32@BaseTrans
  0010e	48 8b 44 24 30	 mov	 rax, QWORD PTR $T3[rsp]
  00113	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00116	ba 01 00 00 00	 mov	 edx, 1
  0011b	48 8b 4c 24 30	 mov	 rcx, QWORD PTR $T3[rsp]
  00120	ff 50 18	 call	 QWORD PTR [rax+24]
  00123	48 89 44 24 60	 mov	 QWORD PTR tv174[rsp], rax
  00128	eb 09		 jmp	 SHORT $LN33@BaseTrans
$LN32@BaseTrans:
  0012a	48 c7 44 24 60
	00 00 00 00	 mov	 QWORD PTR tv174[rsp], 0
$LN33@BaseTrans:

; 1591 :         }
; 1592 :     }

  00133	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0013b	48 83 c4 70	 add	 rsp, 112		; 00000070H
  0013f	5f		 pop	 rdi
  00140	c3		 ret	 0
??$?0VKnightageFollowerTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageFollowerTrans@mu2@@@Z ENDP ; std::shared_ptr<mu2::BaseTrans>::shared_ptr<mu2::BaseTrans><mu2::KnightageFollowerTrans,0>
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
_Owner$2 = 40
$T3 = 48
tv89 = 56
_Px$ = 64
$T4 = 72
_Px$ = 80
$T5 = 88
tv174 = 96
this$ = 128
_Px$ = 136
?dtor$0@?0???$?0VKnightageFollowerTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageFollowerTrans@mu2@@@Z@4HA PROC ; `std::shared_ptr<mu2::BaseTrans>::shared_ptr<mu2::BaseTrans><mu2::KnightageFollowerTrans,0>'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8d 4d 28	 lea	 rcx, QWORD PTR _Owner$2[rbp]
  0000d	e8 00 00 00 00	 call	 ??1?$_Temporary_owner@VKnightageFollowerTrans@mu2@@@std@@QEAA@XZ ; std::_Temporary_owner<mu2::KnightageFollowerTrans>::~_Temporary_owner<mu2::KnightageFollowerTrans>
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$0@?0???$?0VKnightageFollowerTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageFollowerTrans@mu2@@@Z@4HA ENDP ; `std::shared_ptr<mu2::BaseTrans>::shared_ptr<mu2::BaseTrans><mu2::KnightageFollowerTrans,0>'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT ??_GTaskItemTranscation@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GTaskItemTranscation@mu2@@UEAAPEAXI@Z PROC		; mu2::TaskItemTranscation::`scalar deleting destructor', COMDAT
$LN5:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00012	e8 00 00 00 00	 call	 ??1TaskItemTranscation@mu2@@UEAA@XZ ; mu2::TaskItemTranscation::~TaskItemTranscation
  00017	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  0001b	83 e0 01	 and	 eax, 1
  0001e	85 c0		 test	 eax, eax
  00020	74 10		 je	 SHORT $LN2@scalar
  00022	ba 38 03 00 00	 mov	 edx, 824		; 00000338H
  00027	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0002c	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00031	90		 npad	 1
$LN2@scalar:
  00032	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00037	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0003b	c3		 ret	 0
??_GTaskItemTranscation@mu2@@UEAAPEAXI@Z ENDP		; mu2::TaskItemTranscation::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ??4?$shared_ptr@VBaseTrans@mu2@@@std@@QEAAAEAV01@$$QEAV01@@Z
_TEXT	SEGMENT
_Right$ = 32
$T1 = 40
$T2 = 48
$T3 = 56
$T4 = 64
this$ = 96
_Right$ = 104
??4?$shared_ptr@VBaseTrans@mu2@@@std@@QEAAAEAV01@$$QEAV01@@Z PROC ; std::shared_ptr<mu2::BaseTrans>::operator=, COMDAT

; 1704 :     shared_ptr& operator=(shared_ptr&& _Right) noexcept { // take resource from _Right

$LN102:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  0000e	48 8b 44 24 68	 mov	 rax, QWORD PTR _Right$[rsp]
  00013	48 89 44 24 28	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1452 :     element_type* _Ptr{nullptr};

  00018	48 c7 44 24 40
	00 00 00 00	 mov	 QWORD PTR $T4[rsp], 0

; 1453 :     _Ref_count_base* _Rep{nullptr};

  00021	48 c7 44 24 48
	00 00 00 00	 mov	 QWORD PTR $T4[rsp+8], 0

; 1705 :         shared_ptr(_STD move(_Right)).swap(*this);

  0002a	48 8b 44 24 28	 mov	 rax, QWORD PTR $T1[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  0002f	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1645 :         this->_Move_construct_from(_STD move(_Right));

  00034	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
  00039	48 89 44 24 20	 mov	 QWORD PTR _Right$[rsp], rax

; 1327 :         _Ptr = _Right._Ptr;

  0003e	48 8b 44 24 20	 mov	 rax, QWORD PTR _Right$[rsp]
  00043	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00046	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax

; 1328 :         _Rep = _Right._Rep;

  0004b	48 8b 44 24 20	 mov	 rax, QWORD PTR _Right$[rsp]
  00050	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00054	48 89 44 24 48	 mov	 QWORD PTR $T4[rsp+8], rax

; 1329 : 
; 1330 :         _Right._Ptr = nullptr;

  00059	48 8b 44 24 20	 mov	 rax, QWORD PTR _Right$[rsp]
  0005e	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0

; 1331 :         _Right._Rep = nullptr;

  00065	48 8b 44 24 20	 mov	 rax, QWORD PTR _Right$[rsp]
  0006a	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0

; 1646 :     }

  00072	48 8d 44 24 40	 lea	 rax, QWORD PTR $T4[rsp]
  00077	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax

; 1705 :         shared_ptr(_STD move(_Right)).swap(*this);

  0007c	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]

; 1733 :         this->_Swap(_Other);

  00081	48 8b 54 24 60	 mov	 rdx, QWORD PTR this$[rsp]
  00086	48 8b c8	 mov	 rcx, rax
  00089	e8 00 00 00 00	 call	 ?_Swap@?$_Ptr_base@VBaseTrans@mu2@@@std@@IEAAXAEAV12@@Z ; std::_Ptr_base<mu2::BaseTrans>::_Swap
  0008e	90		 npad	 1

; 1384 :         if (_Rep) {

  0008f	48 83 7c 24 48
	00		 cmp	 QWORD PTR $T4[rsp+8], 0
  00095	74 0b		 je	 SHORT $LN85@operator

; 1385 :             _Rep->_Decref();

  00097	48 8b 4c 24 48	 mov	 rcx, QWORD PTR $T4[rsp+8]
  0009c	e8 00 00 00 00	 call	 ?_Decref@_Ref_count_base@std@@QEAAXXZ ; std::_Ref_count_base::_Decref
  000a1	90		 npad	 1
$LN85@operator:

; 1706 :         return *this;

  000a2	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]

; 1707 :     }

  000a7	48 83 c4 58	 add	 rsp, 88			; 00000058H
  000ab	c3		 ret	 0
??4?$shared_ptr@VBaseTrans@mu2@@@std@@QEAAAEAV01@$$QEAV01@@Z ENDP ; std::shared_ptr<mu2::BaseTrans>::operator=
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ?_Swap@?$_Ptr_base@VBaseTrans@mu2@@@std@@IEAAXAEAV12@@Z
_TEXT	SEGMENT
_Left$ = 0
_Right$ = 8
_Left$ = 16
_Right$ = 24
$T1 = 32
$T2 = 40
_Tmp$3 = 48
$T4 = 56
$T5 = 64
$T6 = 72
_Tmp$7 = 80
$T8 = 88
this$ = 112
_Right$ = 120
?_Swap@?$_Ptr_base@VBaseTrans@mu2@@@std@@IEAAXAEAV12@@Z PROC ; std::_Ptr_base<mu2::BaseTrans>::_Swap, COMDAT

; 1389 :     void _Swap(_Ptr_base& _Right) noexcept { // swap pointers

$LN44:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 68	 sub	 rsp, 104		; 00000068H

; 1390 :         _STD swap(_Ptr, _Right._Ptr);

  0000e	48 8b 44 24 78	 mov	 rax, QWORD PTR _Right$[rsp]
  00013	48 89 44 24 08	 mov	 QWORD PTR _Right$[rsp], rax
  00018	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 89 04 24	 mov	 QWORD PTR _Left$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00021	48 8b 04 24	 mov	 rax, QWORD PTR _Left$[rsp]
  00025	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 139  :     _Ty _Tmp = _STD move(_Left);

  0002a	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  0002f	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00032	48 89 44 24 30	 mov	 QWORD PTR _Tmp$3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00037	48 8b 44 24 08	 mov	 rax, QWORD PTR _Right$[rsp]
  0003c	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 140  :     _Left    = _STD move(_Right);

  00041	48 8b 04 24	 mov	 rax, QWORD PTR _Left$[rsp]
  00045	48 8b 4c 24 28	 mov	 rcx, QWORD PTR $T2[rsp]
  0004a	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0004d	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00050	48 8d 44 24 30	 lea	 rax, QWORD PTR _Tmp$3[rsp]
  00055	48 89 44 24 38	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 141  :     _Right   = _STD move(_Tmp);

  0005a	48 8b 44 24 08	 mov	 rax, QWORD PTR _Right$[rsp]
  0005f	48 8b 4c 24 38	 mov	 rcx, QWORD PTR $T4[rsp]
  00064	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00067	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1391 :         _STD swap(_Rep, _Right._Rep);

  0006a	48 8b 44 24 78	 mov	 rax, QWORD PTR _Right$[rsp]
  0006f	48 83 c0 08	 add	 rax, 8
  00073	48 89 44 24 18	 mov	 QWORD PTR _Right$[rsp], rax
  00078	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0007d	48 83 c0 08	 add	 rax, 8
  00081	48 89 44 24 10	 mov	 QWORD PTR _Left$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00086	48 8b 44 24 10	 mov	 rax, QWORD PTR _Left$[rsp]
  0008b	48 89 44 24 40	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 139  :     _Ty _Tmp = _STD move(_Left);

  00090	48 8b 44 24 40	 mov	 rax, QWORD PTR $T5[rsp]
  00095	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00098	48 89 44 24 50	 mov	 QWORD PTR _Tmp$7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  0009d	48 8b 44 24 18	 mov	 rax, QWORD PTR _Right$[rsp]
  000a2	48 89 44 24 48	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 140  :     _Left    = _STD move(_Right);

  000a7	48 8b 44 24 10	 mov	 rax, QWORD PTR _Left$[rsp]
  000ac	48 8b 4c 24 48	 mov	 rcx, QWORD PTR $T6[rsp]
  000b1	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  000b4	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  000b7	48 8d 44 24 50	 lea	 rax, QWORD PTR _Tmp$7[rsp]
  000bc	48 89 44 24 58	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 141  :     _Right   = _STD move(_Tmp);

  000c1	48 8b 44 24 18	 mov	 rax, QWORD PTR _Right$[rsp]
  000c6	48 8b 4c 24 58	 mov	 rcx, QWORD PTR $T8[rsp]
  000cb	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  000ce	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1392 :     }

  000d1	48 83 c4 68	 add	 rsp, 104		; 00000068H
  000d5	c3		 ret	 0
?_Swap@?$_Ptr_base@VBaseTrans@mu2@@@std@@IEAAXAEAV12@@Z ENDP ; std::_Ptr_base<mu2::BaseTrans>::_Swap
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
;	COMDAT ?sendDbRequest@TaskItemTranscation@mu2@@AEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
_TEXT	SEGMENT
hConsole$1 = 80
$T2 = 88
$T3 = 96
this$ = 128
resPtr$ = 136
?sendDbRequest@TaskItemTranscation@mu2@@AEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z PROC ; mu2::TaskItemTranscation::sendDbRequest, COMDAT

; 112  : {

$LN269:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 78	 sub	 rsp, 120		; 00000078H

; 113  : 	UNREFERENCED_PARAMETER( resPtr );
; 114  : 
; 115  : 	m_dbJob.jobNo = 0;

  0000e	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00016	48 c7 40 20 00
	00 00 00	 mov	 QWORD PTR [rax+32], 0

; 116  : 	m_dbJob.jobType = EWShopJobType::ITEM_KNIGHTAGE_TROPHY_TRANS;

  0001e	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00026	c6 40 28 0f	 mov	 BYTE PTR [rax+40], 15
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h

; 30   : 	WShopDbUser& GetOwnerInfo() { return m_ownerInfo; }

  0002a	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00032	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00036	48 83 c0 38	 add	 rax, 56			; 00000038H
  0003a	48 89 44 24 58	 mov	 QWORD PTR $T2[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp

; 118  : 	m_dbJob.owner = m_parentJob->GetOwnerInfo();

  0003f	48 8b 44 24 58	 mov	 rax, QWORD PTR $T2[rsp]
  00044	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0004c	48 83 c1 30	 add	 rcx, 48			; 00000030H
  00050	48 8b d0	 mov	 rdx, rax
  00053	e8 00 00 00 00	 call	 ??4WShopDbUser@mu2@@QEAAAEAU01@AEBU01@@Z

; 119  : 
; 120  : #ifdef __Hotfix_World_To_Db_by_jjangmo_180823
; 121  : 	EventSetter::SetKeyByCharId( m_req, m_dbJob.owner.charId );
; 122  : #endif // __Hotfix_World_To_Db_by_jjangmo_180823
; 123  : 	SERVER.SendToDb( m_req );

  00058	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00060	48 05 f0 02 00
	00		 add	 rax, 752		; 000002f0H
  00066	48 8b d0	 mov	 rdx, rax
  00069	48 8b 0d 00 00
	00 00		 mov	 rcx, QWORD PTR ?Instance@Server@mu2@@2PEAV12@EA ; mu2::Server::Instance
  00070	e8 00 00 00 00	 call	 ?SendToDb@WorldServer@mu2@@QEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ; mu2::WorldServer::SendToDb

; 124  : 	m_state = WShopTask::ZONE_ITEM_BEGIN;

  00075	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0007d	c7 40 08 03 00
	00 00		 mov	 DWORD PTR [rax+8], 3

; 126  : 	MU2_ERROR_LOG( LogCategory::WSHOP, L"WSHOP(%u),%llu : sendDbRequest> Start",

  00084	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  00089	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  0008f	48 89 44 24 50	 mov	 QWORD PTR hConsole$1[rsp], rax
  00094	66 ba 0d 00	 mov	 dx, 13
  00098	48 8b 4c 24 50	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  0009d	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h

; 30   : 	WShopDbUser& GetOwnerInfo() { return m_ownerInfo; }

  000a3	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000ab	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  000af	48 83 c0 38	 add	 rax, 56			; 00000038H
  000b3	48 89 44 24 60	 mov	 QWORD PTR $T3[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp

; 126  : 	MU2_ERROR_LOG( LogCategory::WSHOP, L"WSHOP(%u),%llu : sendDbRequest> Start",

  000b8	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  000c0	e8 00 00 00 00	 call	 ?GetParentJobNo@WShopTask@mu2@@QEAA_KXZ ; mu2::WShopTask::GetParentJobNo
  000c5	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  000ca	48 8b 44 24 60	 mov	 rax, QWORD PTR $T3[rsp]
  000cf	8b 40 0c	 mov	 eax, DWORD PTR [rax+12]
  000d2	89 44 24 38	 mov	 DWORD PTR [rsp+56], eax
  000d6	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_1EM@JHJBJGKM@?$AAW?$AAS?$AAH?$AAO?$AAP?$AA?$CI?$AA?$CF?$AAu?$AA?$CJ?$AA?0?$AA?$CF?$AAl?$AAl?$AAu?$AA?5@
  000dd	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  000e2	c7 44 24 28 7f
	00 00 00	 mov	 DWORD PTR [rsp+40], 127	; 0000007fH
  000ea	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FI@OJCHCMJH@F?3?2Release_Branch?2Server?2Develo@
  000f1	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  000f6	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CI@HOAIHMKD@mu2?3?3TaskItemTranscation?3?3sendD@
  000fd	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  00103	ba 14 00 00 00	 mov	 edx, 20
  00108	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  0010f	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H0ZZ ; mu2::Logger::Logging
  00114	66 ba 07 00	 mov	 dx, 7
  00118	48 8b 4c 24 50	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  0011d	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  00123	90		 npad	 1

; 127  : 		m_parentJob->GetOwnerInfo().fcsAccountId, GetParentJobNo() );
; 128  : 
; 129  : 	return ErrorWShop::SUCCESS;

  00124	33 c0		 xor	 eax, eax

; 130  : }

  00126	48 83 c4 78	 add	 rsp, 120		; 00000078H
  0012a	c3		 ret	 0
?sendDbRequest@TaskItemTranscation@mu2@@AEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ENDP ; mu2::TaskItemTranscation::sendDbRequest
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
;	COMDAT ?sendBeginRequest@TaskItemTranscation@mu2@@EEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
_TEXT	SEGMENT
this$ = 48
resPtr$ = 56
?sendBeginRequest@TaskItemTranscation@mu2@@EEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z PROC ; mu2::TaskItemTranscation::sendBeginRequest, COMDAT

; 104  : {

$LN3:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 105  : 	UNREFERENCED_PARAMETER( resPtr );
; 106  : 
; 107  : 	sendDbRequest( resPtr );

  0000e	48 8b 54 24 38	 mov	 rdx, QWORD PTR resPtr$[rsp]
  00013	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00018	e8 00 00 00 00	 call	 ?sendDbRequest@TaskItemTranscation@mu2@@AEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ; mu2::TaskItemTranscation::sendDbRequest

; 108  : 	return true;

  0001d	b0 01		 mov	 al, 1

; 109  : }

  0001f	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00023	c3		 ret	 0
?sendBeginRequest@TaskItemTranscation@mu2@@EEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ENDP ; mu2::TaskItemTranscation::sendBeginRequest
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\KnightageTranscation.h
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\KnightageTranscation.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
; File F:\Release_Branch\Server\Development\Framework\Net\Common\Event.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\KnightageTranscation.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\KnightageTranscation.h
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\KnightageTranscation.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
; File F:\Release_Branch\Server\Development\Framework\Net\Common\Event.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\BaseItemTranscation.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\BaseItemTranscation.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
;	COMDAT ?createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
_TEXT	SEGMENT
$T1 = 48
tv72 = 52
$T2 = 56
$T3 = 64
$T4 = 72
tv555 = 80
$T5 = 88
$T6 = 96
dbreq$7 = 104
knightageId$ = 112
contributionPoint$ = 120
trophyCount$ = 128
$T8 = 136
$T9 = 140
knightageId$ = 144
__that$ = 152
__that$ = 160
tv67 = 168
dbreq$10 = 176
$T11 = 184
tv131 = 192
$T12 = 200
tv183 = 208
tv222 = 216
$T13 = 224
$T14 = 232
$T15 = 240
$T16 = 248
$T17 = 256
$T18 = 264
$T19 = 272
tv238 = 280
$T20 = 288
$T21 = 296
$T22 = 304
$T23 = 312
$T24 = 320
$T25 = 328
$T26 = 336
tv244 = 344
$T27 = 352
$T28 = 360
$T29 = 368
$T30 = 376
$T31 = 384
$T32 = 392
tv248 = 400
$T33 = 408
$T34 = 416
$T35 = 432
$T36 = 448
dbreq$37 = 464
$T38 = 472
$T39 = 496
this$ = 544
reqPtr$ = 552
?createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z PROC ; mu2::TaskItemTranscation::createTrans, COMDAT

; 56   : {

$LN507:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 81 ec 18 02
	00 00		 sub	 rsp, 536		; 00000218H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1309 :         return _Ptr;

  00011	48 8b 84 24 28
	02 00 00	 mov	 rax, QWORD PTR reqPtr$[rsp]
  00019	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0001c	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h

; 186  : 		return ptr.get();

  00024	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR $T13[rsp]
  0002c	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR $T14[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp

; 57   : 	switch (reqPtr->GetProtocol())

  00034	48 8b 84 24 e8
	00 00 00	 mov	 rax, QWORD PTR $T14[rsp]
  0003c	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR tv67[rsp], rax
  00044	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR tv67[rsp]
  0004c	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0004f	48 8b 8c 24 a8
	00 00 00	 mov	 rcx, QWORD PTR tv67[rsp]
  00057	ff 50 20	 call	 QWORD PTR [rax+32]
  0005a	0f b7 c0	 movzx	 eax, ax
  0005d	89 44 24 34	 mov	 DWORD PTR tv72[rsp], eax
  00061	81 7c 24 34 ed
	30 00 00	 cmp	 DWORD PTR tv72[rsp], 12525 ; 000030edH
  00069	0f 84 05 02 00
	00		 je	 $LN5@createTran
  0006f	81 7c 24 34 ee
	30 00 00	 cmp	 DWORD PTR tv72[rsp], 12526 ; 000030eeH
  00077	74 13		 je	 SHORT $LN4@createTran
  00079	81 7c 24 34 4d
	31 00 00	 cmp	 DWORD PTR tv72[rsp], 12621 ; 0000314dH
  00081	0f 84 2e 04 00
	00		 je	 $LN6@createTran
  00087	e9 74 05 00 00	 jmp	 $LN2@createTran
$LN4@createTran:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1309 :         return _Ptr;

  0008c	48 8b 84 24 28
	02 00 00	 mov	 rax, QWORD PTR reqPtr$[rsp]
  00094	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00097	48 89 84 24 f0
	00 00 00	 mov	 QWORD PTR $T15[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h

; 191  : 		return ptr.get();

  0009f	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR $T15[rsp]
  000a7	48 89 84 24 f8
	00 00 00	 mov	 QWORD PTR $T16[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp

; 61   : 		EReqDbKnightageFollowerTranscation *dbreq = static_cast<EReqDbKnightageFollowerTranscation*>(reqPtr.RawPtr());

  000af	48 8b 84 24 f8
	00 00 00	 mov	 rax, QWORD PTR $T16[rsp]
  000b7	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR dbreq$10[rsp], rax

; 62   : 		m_trans = BaseTransPtr( new KnightageFollowerTrans( dbreq->knightageId, dbreq->trophyworkInfo ) );

  000bf	b9 40 00 00 00	 mov	 ecx, 64			; 00000040H
  000c4	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  000c9	48 89 44 24 58	 mov	 QWORD PTR $T5[rsp], rax
  000ce	48 83 7c 24 58
	00		 cmp	 QWORD PTR $T5[rsp], 0
  000d4	0f 84 25 01 00
	00		 je	 $LN9@createTran
  000da	48 8d 84 24 d8
	01 00 00	 lea	 rax, QWORD PTR $T38[rsp]
  000e2	48 89 44 24 38	 mov	 QWORD PTR $T2[rsp], rax
  000e7	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR dbreq$10[rsp]
  000ef	48 05 a0 00 00
	00		 add	 rax, 160		; 000000a0H
  000f5	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR __that$[rsp], rax
  000fd	48 8b 44 24 38	 mov	 rax, QWORD PTR $T2[rsp]
  00102	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  00109	48 89 08	 mov	 QWORD PTR [rax], rcx
  0010c	48 8b 44 24 38	 mov	 rax, QWORD PTR $T2[rsp]
  00111	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ChangedMoney@mu2@@6B@
  00118	48 89 08	 mov	 QWORD PTR [rax], rcx
  0011b	48 8b 44 24 38	 mov	 rax, QWORD PTR $T2[rsp]
  00120	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR __that$[rsp]
  00128	48 8b 49 08	 mov	 rcx, QWORD PTR [rcx+8]
  0012c	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx
  00130	48 8b 44 24 38	 mov	 rax, QWORD PTR $T2[rsp]
  00135	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR __that$[rsp]
  0013d	0f b6 49 10	 movzx	 ecx, BYTE PTR [rcx+16]
  00141	88 48 10	 mov	 BYTE PTR [rax+16], cl
  00144	48 8b 44 24 38	 mov	 rax, QWORD PTR $T2[rsp]
  00149	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR __that$[rsp]
  00151	0f b6 49 11	 movzx	 ecx, BYTE PTR [rcx+17]
  00155	88 48 11	 mov	 BYTE PTR [rax+17], cl
  00158	48 8b 44 24 38	 mov	 rax, QWORD PTR $T2[rsp]
  0015d	48 89 84 24 00
	01 00 00	 mov	 QWORD PTR $T17[rsp], rax
  00165	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR $T17[rsp]
  0016d	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
  00175	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR dbreq$10[rsp]
  0017d	8b 80 b8 00 00
	00		 mov	 eax, DWORD PTR [rax+184]
  00183	89 44 24 70	 mov	 DWORD PTR knightageId$[rsp], eax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\KnightageTranscation.h

; 62   : 			KnightageBaseTrans( ID::KnightageFollower_Trans, knightageId, money )

  00187	c7 44 24 28 00
	00 00 00	 mov	 DWORD PTR [rsp+40], 0
  0018f	c7 44 24 20 00
	00 00 00	 mov	 DWORD PTR [rsp+32], 0
  00197	4c 8b 8c 24 b8
	00 00 00	 mov	 r9, QWORD PTR $T11[rsp]
  0019f	44 8b 44 24 70	 mov	 r8d, DWORD PTR knightageId$[rsp]
  001a4	ba 02 00 00 00	 mov	 edx, 2
  001a9	48 8b 4c 24 58	 mov	 rcx, QWORD PTR $T5[rsp]
  001ae	e8 00 00 00 00	 call	 ??0KnightageBaseTrans@mu2@@QEAA@W4ID@BaseTrans@1@IAEAUChangedMoney@1@HH@Z ; mu2::KnightageBaseTrans::KnightageBaseTrans

; 64   : 		{}

  001b3	48 8b 44 24 58	 mov	 rax, QWORD PTR $T5[rsp]
  001b8	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7KnightageFollowerTrans@mu2@@6B@
  001bf	48 89 08	 mov	 QWORD PTR [rax], rcx

; 63   : 			, m_workType(0)

  001c2	48 8b 44 24 58	 mov	 rax, QWORD PTR $T5[rsp]
  001c7	c7 40 38 00 00
	00 00		 mov	 DWORD PTR [rax+56], 0
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h

; 172  : 	virtual~ISerializer() {}

  001ce	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  001d6	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  001dd	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\KnightageTranscation.h

; 64   : 		{}

  001e0	48 8b 44 24 58	 mov	 rax, QWORD PTR $T5[rsp]
  001e5	48 89 84 24 08
	01 00 00	 mov	 QWORD PTR $T18[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp

; 62   : 		m_trans = BaseTransPtr( new KnightageFollowerTrans( dbreq->knightageId, dbreq->trophyworkInfo ) );

  001ed	48 8b 84 24 08
	01 00 00	 mov	 rax, QWORD PTR $T18[rsp]
  001f5	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR tv131[rsp], rax
  001fd	eb 0c		 jmp	 SHORT $LN10@createTran
$LN9@createTran:
  001ff	48 c7 84 24 c0
	00 00 00 00 00
	00 00		 mov	 QWORD PTR tv131[rsp], 0
$LN10@createTran:
  0020b	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR tv131[rsp]
  00213	48 89 84 24 10
	01 00 00	 mov	 QWORD PTR $T19[rsp], rax
  0021b	48 8b 94 24 10
	01 00 00	 mov	 rdx, QWORD PTR $T19[rsp]
  00223	48 8d 8c 24 a0
	01 00 00	 lea	 rcx, QWORD PTR $T34[rsp]
  0022b	e8 00 00 00 00	 call	 ??$?0VKnightageFollowerTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageFollowerTrans@mu2@@@Z ; std::shared_ptr<mu2::BaseTrans>::shared_ptr<mu2::BaseTrans><mu2::KnightageFollowerTrans,0>
  00230	48 89 84 24 18
	01 00 00	 mov	 QWORD PTR tv238[rsp], rax
  00238	48 8b 84 24 20
	02 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00240	48 05 28 03 00
	00		 add	 rax, 808		; 00000328H
  00246	48 8b 94 24 18
	01 00 00	 mov	 rdx, QWORD PTR tv238[rsp]
  0024e	48 8b c8	 mov	 rcx, rax
  00251	e8 00 00 00 00	 call	 ??4?$shared_ptr@VBaseTrans@mu2@@@std@@QEAAAEAV01@$$QEAV01@@Z ; std::shared_ptr<mu2::BaseTrans>::operator=
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1384 :         if (_Rep) {

  00256	48 83 bc 24 a8
	01 00 00 00	 cmp	 QWORD PTR $T34[rsp+8], 0
  0025f	74 0e		 je	 SHORT $LN166@createTran

; 1385 :             _Rep->_Decref();

  00261	48 8b 8c 24 a8
	01 00 00	 mov	 rcx, QWORD PTR $T34[rsp+8]
  00269	e8 00 00 00 00	 call	 ?_Decref@_Ref_count_base@std@@QEAAXXZ ; std::_Ref_count_base::_Decref
  0026e	90		 npad	 1
$LN166@createTran:
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp

; 63   : 		break;

  0026f	e9 8c 03 00 00	 jmp	 $LN2@createTran
$LN5@createTran:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1309 :         return _Ptr;

  00274	48 8b 84 24 28
	02 00 00	 mov	 rax, QWORD PTR reqPtr$[rsp]
  0027c	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0027f	48 89 84 24 20
	01 00 00	 mov	 QWORD PTR $T20[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h

; 191  : 		return ptr.get();

  00287	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR $T20[rsp]
  0028f	48 89 84 24 28
	01 00 00	 mov	 QWORD PTR $T21[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp

; 67   : 		EReqDbKnightageTrophyItemTranscation *dbreq = static_cast<EReqDbKnightageTrophyItemTranscation*>(reqPtr.RawPtr());

  00297	48 8b 84 24 28
	01 00 00	 mov	 rax, QWORD PTR $T21[rsp]
  0029f	48 89 44 24 68	 mov	 QWORD PTR dbreq$7[rsp], rax

; 68   : 		m_trans = BaseTransPtr(new KnightageGiveTrophyTrans(dbreq->knightageId, dbreq->trophyworkInfo, reqPtr->GetKey(), dbreq->donationCount, static_cast<Int32>(dbreq->contributionInfo.money)));

  002a4	b9 40 00 00 00	 mov	 ecx, 64			; 00000040H
  002a9	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  002ae	48 89 44 24 60	 mov	 QWORD PTR $T6[rsp], rax
  002b3	48 83 7c 24 60
	00		 cmp	 QWORD PTR $T6[rsp], 0
  002b9	0f 84 81 01 00
	00		 je	 $LN11@createTran
  002bf	48 8d 84 24 f0
	01 00 00	 lea	 rax, QWORD PTR $T39[rsp]
  002c7	48 89 44 24 40	 mov	 QWORD PTR $T3[rsp], rax
  002cc	48 8b 44 24 68	 mov	 rax, QWORD PTR dbreq$7[rsp]
  002d1	48 05 a8 00 00
	00		 add	 rax, 168		; 000000a8H
  002d7	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR __that$[rsp], rax
  002df	48 8b 44 24 40	 mov	 rax, QWORD PTR $T3[rsp]
  002e4	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  002eb	48 89 08	 mov	 QWORD PTR [rax], rcx
  002ee	48 8b 44 24 40	 mov	 rax, QWORD PTR $T3[rsp]
  002f3	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ChangedMoney@mu2@@6B@
  002fa	48 89 08	 mov	 QWORD PTR [rax], rcx
  002fd	48 8b 44 24 40	 mov	 rax, QWORD PTR $T3[rsp]
  00302	48 8b 8c 24 a0
	00 00 00	 mov	 rcx, QWORD PTR __that$[rsp]
  0030a	48 8b 49 08	 mov	 rcx, QWORD PTR [rcx+8]
  0030e	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx
  00312	48 8b 44 24 40	 mov	 rax, QWORD PTR $T3[rsp]
  00317	48 8b 8c 24 a0
	00 00 00	 mov	 rcx, QWORD PTR __that$[rsp]
  0031f	0f b6 49 10	 movzx	 ecx, BYTE PTR [rcx+16]
  00323	88 48 10	 mov	 BYTE PTR [rax+16], cl
  00326	48 8b 44 24 40	 mov	 rax, QWORD PTR $T3[rsp]
  0032b	48 8b 8c 24 a0
	00 00 00	 mov	 rcx, QWORD PTR __that$[rsp]
  00333	0f b6 49 11	 movzx	 ecx, BYTE PTR [rcx+17]
  00337	88 48 11	 mov	 BYTE PTR [rax+17], cl
  0033a	48 8b 44 24 40	 mov	 rax, QWORD PTR $T3[rsp]
  0033f	48 89 84 24 30
	01 00 00	 mov	 QWORD PTR $T22[rsp], rax
  00347	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR $T22[rsp]
  0034f	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1309 :         return _Ptr;

  00357	48 8b 84 24 28
	02 00 00	 mov	 rax, QWORD PTR reqPtr$[rsp]
  0035f	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00362	48 89 84 24 38
	01 00 00	 mov	 QWORD PTR $T23[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h

; 186  : 		return ptr.get();

  0036a	48 8b 84 24 38
	01 00 00	 mov	 rax, QWORD PTR $T23[rsp]
  00372	48 89 84 24 40
	01 00 00	 mov	 QWORD PTR $T24[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Net\Common\Event.h

; 144  : 	inline UInt32	GetKey()  { return key; }

  0037a	48 8b 84 24 40
	01 00 00	 mov	 rax, QWORD PTR $T24[rsp]
  00382	8b 40 14	 mov	 eax, DWORD PTR [rax+20]
  00385	89 84 24 88 00
	00 00		 mov	 DWORD PTR $T8[rsp], eax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp

; 68   : 		m_trans = BaseTransPtr(new KnightageGiveTrophyTrans(dbreq->knightageId, dbreq->trophyworkInfo, reqPtr->GetKey(), dbreq->donationCount, static_cast<Int32>(dbreq->contributionInfo.money)));

  0038c	48 8b 44 24 68	 mov	 rax, QWORD PTR dbreq$7[rsp]
  00391	8b 80 c8 00 00
	00		 mov	 eax, DWORD PTR [rax+200]
  00397	89 44 24 78	 mov	 DWORD PTR contributionPoint$[rsp], eax
  0039b	48 8b 44 24 68	 mov	 rax, QWORD PTR dbreq$7[rsp]
  003a0	8b 80 e0 00 00
	00		 mov	 eax, DWORD PTR [rax+224]
  003a6	89 84 24 80 00
	00 00		 mov	 DWORD PTR trophyCount$[rsp], eax
  003ad	48 8b 44 24 68	 mov	 rax, QWORD PTR dbreq$7[rsp]
  003b2	8b 80 a0 00 00
	00		 mov	 eax, DWORD PTR [rax+160]
  003b8	89 84 24 90 00
	00 00		 mov	 DWORD PTR knightageId$[rsp], eax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\KnightageTranscation.h

; 41   : 			KnightageBaseTrans(ID::KnightageGiveTrophy_Trans, knightageId, money, trophyCount, contributionPoint)

  003bf	8b 44 24 78	 mov	 eax, DWORD PTR contributionPoint$[rsp]
  003c3	89 44 24 28	 mov	 DWORD PTR [rsp+40], eax
  003c7	8b 84 24 80 00
	00 00		 mov	 eax, DWORD PTR trophyCount$[rsp]
  003ce	89 44 24 20	 mov	 DWORD PTR [rsp+32], eax
  003d2	4c 8b 8c 24 c8
	00 00 00	 mov	 r9, QWORD PTR $T12[rsp]
  003da	44 8b 84 24 90
	00 00 00	 mov	 r8d, DWORD PTR knightageId$[rsp]
  003e2	ba 01 00 00 00	 mov	 edx, 1
  003e7	48 8b 4c 24 60	 mov	 rcx, QWORD PTR $T6[rsp]
  003ec	e8 00 00 00 00	 call	 ??0KnightageBaseTrans@mu2@@QEAA@W4ID@BaseTrans@1@IAEAUChangedMoney@1@HH@Z ; mu2::KnightageBaseTrans::KnightageBaseTrans

; 43   : 		{

  003f1	48 8b 44 24 60	 mov	 rax, QWORD PTR $T6[rsp]
  003f6	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7KnightageGiveTrophyTrans@mu2@@6B@
  003fd	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp

; 68   : 		m_trans = BaseTransPtr(new KnightageGiveTrophyTrans(dbreq->knightageId, dbreq->trophyworkInfo, reqPtr->GetKey(), dbreq->donationCount, static_cast<Int32>(dbreq->contributionInfo.money)));

  00400	8b 84 24 88 00
	00 00		 mov	 eax, DWORD PTR $T8[rsp]
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\KnightageTranscation.h

; 42   : 			, m_charId(charId)

  00407	48 8b 4c 24 60	 mov	 rcx, QWORD PTR $T6[rsp]
  0040c	89 41 38	 mov	 DWORD PTR [rcx+56], eax
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h

; 172  : 	virtual~ISerializer() {}

  0040f	48 8b 84 24 c8
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
  00417	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  0041e	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\KnightageTranscation.h

; 44   : 		}

  00421	48 8b 44 24 60	 mov	 rax, QWORD PTR $T6[rsp]
  00426	48 89 84 24 48
	01 00 00	 mov	 QWORD PTR $T25[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp

; 68   : 		m_trans = BaseTransPtr(new KnightageGiveTrophyTrans(dbreq->knightageId, dbreq->trophyworkInfo, reqPtr->GetKey(), dbreq->donationCount, static_cast<Int32>(dbreq->contributionInfo.money)));

  0042e	48 8b 84 24 48
	01 00 00	 mov	 rax, QWORD PTR $T25[rsp]
  00436	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR tv183[rsp], rax
  0043e	eb 0c		 jmp	 SHORT $LN12@createTran
$LN11@createTran:
  00440	48 c7 84 24 d0
	00 00 00 00 00
	00 00		 mov	 QWORD PTR tv183[rsp], 0
$LN12@createTran:
  0044c	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR tv183[rsp]
  00454	48 89 84 24 50
	01 00 00	 mov	 QWORD PTR $T26[rsp], rax
  0045c	48 8b 94 24 50
	01 00 00	 mov	 rdx, QWORD PTR $T26[rsp]
  00464	48 8d 8c 24 b0
	01 00 00	 lea	 rcx, QWORD PTR $T35[rsp]
  0046c	e8 00 00 00 00	 call	 ??$?0VKnightageGiveTrophyTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVKnightageGiveTrophyTrans@mu2@@@Z ; std::shared_ptr<mu2::BaseTrans>::shared_ptr<mu2::BaseTrans><mu2::KnightageGiveTrophyTrans,0>
  00471	48 89 84 24 58
	01 00 00	 mov	 QWORD PTR tv244[rsp], rax
  00479	48 8b 84 24 20
	02 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00481	48 05 28 03 00
	00		 add	 rax, 808		; 00000328H
  00487	48 8b 94 24 58
	01 00 00	 mov	 rdx, QWORD PTR tv244[rsp]
  0048f	48 8b c8	 mov	 rcx, rax
  00492	e8 00 00 00 00	 call	 ??4?$shared_ptr@VBaseTrans@mu2@@@std@@QEAAAEAV01@$$QEAV01@@Z ; std::shared_ptr<mu2::BaseTrans>::operator=
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1384 :         if (_Rep) {

  00497	48 83 bc 24 b8
	01 00 00 00	 cmp	 QWORD PTR $T35[rsp+8], 0
  004a0	74 0e		 je	 SHORT $LN328@createTran

; 1385 :             _Rep->_Decref();

  004a2	48 8b 8c 24 b8
	01 00 00	 mov	 rcx, QWORD PTR $T35[rsp+8]
  004aa	e8 00 00 00 00	 call	 ?_Decref@_Ref_count_base@std@@QEAAXXZ ; std::_Ref_count_base::_Decref
  004af	90		 npad	 1
$LN328@createTran:
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp

; 69   : 		break;

  004b0	e9 4b 01 00 00	 jmp	 $LN2@createTran
$LN6@createTran:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1309 :         return _Ptr;

  004b5	48 8b 84 24 28
	02 00 00	 mov	 rax, QWORD PTR reqPtr$[rsp]
  004bd	48 8b 00	 mov	 rax, QWORD PTR [rax]
  004c0	48 89 84 24 60
	01 00 00	 mov	 QWORD PTR $T27[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h

; 191  : 		return ptr.get();

  004c8	48 8b 84 24 60
	01 00 00	 mov	 rax, QWORD PTR $T27[rsp]
  004d0	48 89 84 24 68
	01 00 00	 mov	 QWORD PTR $T28[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp

; 73   : 		EReqDbItemCharacterSlotTransaction *dbreq = static_cast<EReqDbItemCharacterSlotTransaction*>(reqPtr.RawPtr());

  004d8	48 8b 84 24 68
	01 00 00	 mov	 rax, QWORD PTR $T28[rsp]
  004e0	48 89 84 24 d0
	01 00 00	 mov	 QWORD PTR dbreq$37[rsp], rax

; 75   : 		m_trans = BaseTransPtr( new CharacterSlotExtendTrans( reqPtr->GetKey() ) );

  004e8	b9 18 00 00 00	 mov	 ecx, 24
  004ed	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  004f2	48 89 44 24 48	 mov	 QWORD PTR $T4[rsp], rax
  004f7	48 83 7c 24 48
	00		 cmp	 QWORD PTR $T4[rsp], 0
  004fd	0f 84 8d 00 00
	00		 je	 $LN13@createTran
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1309 :         return _Ptr;

  00503	48 8b 84 24 28
	02 00 00	 mov	 rax, QWORD PTR reqPtr$[rsp]
  0050b	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0050e	48 89 84 24 70
	01 00 00	 mov	 QWORD PTR $T29[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h

; 186  : 		return ptr.get();

  00516	48 8b 84 24 70
	01 00 00	 mov	 rax, QWORD PTR $T29[rsp]
  0051e	48 89 84 24 78
	01 00 00	 mov	 QWORD PTR $T30[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Net\Common\Event.h

; 144  : 	inline UInt32	GetKey()  { return key; }

  00526	48 8b 84 24 78
	01 00 00	 mov	 rax, QWORD PTR $T30[rsp]
  0052e	8b 40 14	 mov	 eax, DWORD PTR [rax+20]
  00531	89 84 24 8c 00
	00 00		 mov	 DWORD PTR $T9[rsp], eax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\BaseItemTranscation.h

; 18   : 		{}

  00538	48 8b 44 24 48	 mov	 rax, QWORD PTR $T4[rsp]
  0053d	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7BaseTrans@mu2@@6B@
  00544	48 89 08	 mov	 QWORD PTR [rax], rcx

; 17   : 			m_id( id )

  00547	48 8b 44 24 48	 mov	 rax, QWORD PTR $T4[rsp]
  0054c	c7 40 08 03 00
	00 00		 mov	 DWORD PTR [rax+8], 3

; 38   : 		{}

  00553	48 8b 44 24 48	 mov	 rax, QWORD PTR $T4[rsp]
  00558	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7CharacterSlotExtendTrans@mu2@@6B@
  0055f	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp

; 75   : 		m_trans = BaseTransPtr( new CharacterSlotExtendTrans( reqPtr->GetKey() ) );

  00562	8b 84 24 8c 00
	00 00		 mov	 eax, DWORD PTR $T9[rsp]
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\BaseItemTranscation.h

; 37   : 			m_charId(charId)

  00569	48 8b 4c 24 48	 mov	 rcx, QWORD PTR $T4[rsp]
  0056e	89 41 10	 mov	 DWORD PTR [rcx+16], eax

; 38   : 		{}

  00571	48 8b 44 24 48	 mov	 rax, QWORD PTR $T4[rsp]
  00576	48 89 84 24 80
	01 00 00	 mov	 QWORD PTR $T31[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp

; 75   : 		m_trans = BaseTransPtr( new CharacterSlotExtendTrans( reqPtr->GetKey() ) );

  0057e	48 8b 84 24 80
	01 00 00	 mov	 rax, QWORD PTR $T31[rsp]
  00586	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR tv222[rsp], rax
  0058e	eb 0c		 jmp	 SHORT $LN14@createTran
$LN13@createTran:
  00590	48 c7 84 24 d8
	00 00 00 00 00
	00 00		 mov	 QWORD PTR tv222[rsp], 0
$LN14@createTran:
  0059c	48 8b 84 24 d8
	00 00 00	 mov	 rax, QWORD PTR tv222[rsp]
  005a4	48 89 84 24 88
	01 00 00	 mov	 QWORD PTR $T32[rsp], rax
  005ac	48 8b 94 24 88
	01 00 00	 mov	 rdx, QWORD PTR $T32[rsp]
  005b4	48 8d 8c 24 c0
	01 00 00	 lea	 rcx, QWORD PTR $T36[rsp]
  005bc	e8 00 00 00 00	 call	 ??$?0VCharacterSlotExtendTrans@mu2@@$0A@@?$shared_ptr@VBaseTrans@mu2@@@std@@QEAA@PEAVCharacterSlotExtendTrans@mu2@@@Z ; std::shared_ptr<mu2::BaseTrans>::shared_ptr<mu2::BaseTrans><mu2::CharacterSlotExtendTrans,0>
  005c1	48 89 84 24 90
	01 00 00	 mov	 QWORD PTR tv248[rsp], rax
  005c9	48 8b 84 24 20
	02 00 00	 mov	 rax, QWORD PTR this$[rsp]
  005d1	48 05 28 03 00
	00		 add	 rax, 808		; 00000328H
  005d7	48 8b 94 24 90
	01 00 00	 mov	 rdx, QWORD PTR tv248[rsp]
  005df	48 8b c8	 mov	 rcx, rax
  005e2	e8 00 00 00 00	 call	 ??4?$shared_ptr@VBaseTrans@mu2@@@std@@QEAAAEAV01@$$QEAV01@@Z ; std::shared_ptr<mu2::BaseTrans>::operator=
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1384 :         if (_Rep) {

  005e7	48 83 bc 24 c8
	01 00 00 00	 cmp	 QWORD PTR $T36[rsp+8], 0
  005f0	74 0e		 je	 SHORT $LN2@createTran

; 1385 :             _Rep->_Decref();

  005f2	48 8b 8c 24 c8
	01 00 00	 mov	 rcx, QWORD PTR $T36[rsp+8]
  005fa	e8 00 00 00 00	 call	 ?_Decref@_Ref_count_base@std@@QEAAXXZ ; std::_Ref_count_base::_Decref
  005ff	90		 npad	 1
$LN2@createTran:

; 1309 :         return _Ptr;

  00600	48 8b 84 24 20
	02 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00608	48 8b 80 28 03
	00 00		 mov	 rax, QWORD PTR [rax+808]
  0060f	48 89 84 24 98
	01 00 00	 mov	 QWORD PTR $T33[rsp], rax

; 1789 :         return get() != nullptr;

  00617	48 8b 84 24 98
	01 00 00	 mov	 rax, QWORD PTR $T33[rsp]
  0061f	48 85 c0	 test	 rax, rax
  00622	74 0a		 je	 SHORT $LN497@createTran
  00624	c7 44 24 50 01
	00 00 00	 mov	 DWORD PTR tv555[rsp], 1
  0062c	eb 08		 jmp	 SHORT $LN498@createTran
$LN497@createTran:
  0062e	c7 44 24 50 00
	00 00 00	 mov	 DWORD PTR tv555[rsp], 0
$LN498@createTran:
  00636	0f b6 44 24 50	 movzx	 eax, BYTE PTR tv555[rsp]
  0063b	88 44 24 30	 mov	 BYTE PTR $T1[rsp], al
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp

; 80   : 	VALID_RETURN( m_trans, false );

  0063f	0f b6 44 24 30	 movzx	 eax, BYTE PTR $T1[rsp]
  00644	0f b6 c0	 movzx	 eax, al
  00647	85 c0		 test	 eax, eax
  00649	75 04		 jne	 SHORT $LN7@createTran
  0064b	32 c0		 xor	 al, al
  0064d	eb 02		 jmp	 SHORT $LN1@createTran
$LN7@createTran:

; 81   : 
; 82   : 	return true;

  0064f	b0 01		 mov	 al, 1
$LN1@createTran:

; 83   : }

  00651	48 81 c4 18 02
	00 00		 add	 rsp, 536		; 00000218H
  00658	c3		 ret	 0
?createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ENDP ; mu2::TaskItemTranscation::createTrans
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 48
tv72 = 52
$T2 = 56
$T3 = 64
$T4 = 72
tv555 = 80
$T5 = 88
$T6 = 96
dbreq$7 = 104
knightageId$ = 112
contributionPoint$ = 120
trophyCount$ = 128
$T8 = 136
$T9 = 140
knightageId$ = 144
__that$ = 152
__that$ = 160
tv67 = 168
dbreq$10 = 176
$T11 = 184
tv131 = 192
$T12 = 200
tv183 = 208
tv222 = 216
$T13 = 224
$T14 = 232
$T15 = 240
$T16 = 248
$T17 = 256
$T18 = 264
$T19 = 272
tv238 = 280
$T20 = 288
$T21 = 296
$T22 = 304
$T23 = 312
$T24 = 320
$T25 = 328
$T26 = 336
tv244 = 344
$T27 = 352
$T28 = 360
$T29 = 368
$T30 = 376
$T31 = 384
$T32 = 392
tv248 = 400
$T33 = 408
$T34 = 416
$T35 = 432
$T36 = 448
dbreq$37 = 464
$T38 = 472
$T39 = 496
this$ = 544
reqPtr$ = 552
?dtor$0@?0??createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z@4HA PROC ; `mu2::TaskItemTranscation::createTrans'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	ba 40 00 00 00	 mov	 edx, 64			; 00000040H
  0000e	48 8b 4d 58	 mov	 rcx, QWORD PTR $T5[rbp]
  00012	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00017	48 83 c4 20	 add	 rsp, 32			; 00000020H
  0001b	5d		 pop	 rbp
  0001c	c3		 ret	 0
?dtor$0@?0??createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z@4HA ENDP ; `mu2::TaskItemTranscation::createTrans'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 48
tv72 = 52
$T2 = 56
$T3 = 64
$T4 = 72
tv555 = 80
$T5 = 88
$T6 = 96
dbreq$7 = 104
knightageId$ = 112
contributionPoint$ = 120
trophyCount$ = 128
$T8 = 136
$T9 = 140
knightageId$ = 144
__that$ = 152
__that$ = 160
tv67 = 168
dbreq$10 = 176
$T11 = 184
tv131 = 192
$T12 = 200
tv183 = 208
tv222 = 216
$T13 = 224
$T14 = 232
$T15 = 240
$T16 = 248
$T17 = 256
$T18 = 264
$T19 = 272
tv238 = 280
$T20 = 288
$T21 = 296
$T22 = 304
$T23 = 312
$T24 = 320
$T25 = 328
$T26 = 336
tv244 = 344
$T27 = 352
$T28 = 360
$T29 = 368
$T30 = 376
$T31 = 384
$T32 = 392
tv248 = 400
$T33 = 408
$T34 = 416
$T35 = 432
$T36 = 448
dbreq$37 = 464
$T38 = 472
$T39 = 496
this$ = 544
reqPtr$ = 552
?dtor$12@?0??createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z@4HA PROC ; `mu2::TaskItemTranscation::createTrans'::`1'::dtor$12
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 8d b8 00
	00 00		 mov	 rcx, QWORD PTR $T11[rbp]
  00010	e8 00 00 00 00	 call	 ??1ChangedMoney@mu2@@UEAA@XZ
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$12@?0??createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z@4HA ENDP ; `mu2::TaskItemTranscation::createTrans'::`1'::dtor$12
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 48
tv72 = 52
$T2 = 56
$T3 = 64
$T4 = 72
tv555 = 80
$T5 = 88
$T6 = 96
dbreq$7 = 104
knightageId$ = 112
contributionPoint$ = 120
trophyCount$ = 128
$T8 = 136
$T9 = 140
knightageId$ = 144
__that$ = 152
__that$ = 160
tv67 = 168
dbreq$10 = 176
$T11 = 184
tv131 = 192
$T12 = 200
tv183 = 208
tv222 = 216
$T13 = 224
$T14 = 232
$T15 = 240
$T16 = 248
$T17 = 256
$T18 = 264
$T19 = 272
tv238 = 280
$T20 = 288
$T21 = 296
$T22 = 304
$T23 = 312
$T24 = 320
$T25 = 328
$T26 = 336
tv244 = 344
$T27 = 352
$T28 = 360
$T29 = 368
$T30 = 376
$T31 = 384
$T32 = 392
tv248 = 400
$T33 = 408
$T34 = 416
$T35 = 432
$T36 = 448
dbreq$37 = 464
$T38 = 472
$T39 = 496
this$ = 544
reqPtr$ = 552
?dtor$3@?0??createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z@4HA PROC ; `mu2::TaskItemTranscation::createTrans'::`1'::dtor$3
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	ba 40 00 00 00	 mov	 edx, 64			; 00000040H
  0000e	48 8b 4d 60	 mov	 rcx, QWORD PTR $T6[rbp]
  00012	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00017	48 83 c4 20	 add	 rsp, 32			; 00000020H
  0001b	5d		 pop	 rbp
  0001c	c3		 ret	 0
?dtor$3@?0??createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z@4HA ENDP ; `mu2::TaskItemTranscation::createTrans'::`1'::dtor$3
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 48
tv72 = 52
$T2 = 56
$T3 = 64
$T4 = 72
tv555 = 80
$T5 = 88
$T6 = 96
dbreq$7 = 104
knightageId$ = 112
contributionPoint$ = 120
trophyCount$ = 128
$T8 = 136
$T9 = 140
knightageId$ = 144
__that$ = 152
__that$ = 160
tv67 = 168
dbreq$10 = 176
$T11 = 184
tv131 = 192
$T12 = 200
tv183 = 208
tv222 = 216
$T13 = 224
$T14 = 232
$T15 = 240
$T16 = 248
$T17 = 256
$T18 = 264
$T19 = 272
tv238 = 280
$T20 = 288
$T21 = 296
$T22 = 304
$T23 = 312
$T24 = 320
$T25 = 328
$T26 = 336
tv244 = 344
$T27 = 352
$T28 = 360
$T29 = 368
$T30 = 376
$T31 = 384
$T32 = 392
tv248 = 400
$T33 = 408
$T34 = 416
$T35 = 432
$T36 = 448
dbreq$37 = 464
$T38 = 472
$T39 = 496
this$ = 544
reqPtr$ = 552
?dtor$44@?0??createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z@4HA PROC ; `mu2::TaskItemTranscation::createTrans'::`1'::dtor$44
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 8d c8 00
	00 00		 mov	 rcx, QWORD PTR $T12[rbp]
  00010	e8 00 00 00 00	 call	 ??1ChangedMoney@mu2@@UEAA@XZ
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$44@?0??createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z@4HA ENDP ; `mu2::TaskItemTranscation::createTrans'::`1'::dtor$44
text$x	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
;	COMDAT ?Setup@TaskItemTranscation@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z
_TEXT	SEGMENT
$T1 = 32
$T2 = 33
error$ = 36
req$ = 40
tv161 = 48
res$ = 56
$T3 = 64
$T4 = 72
$T5 = 80
$T6 = 88
$T7 = 96
this$ = 104
$T8 = 112
$T9 = 120
$T10 = 128
$T11 = 136
this$ = 160
reqPtr$ = 168
resPtr$ = 176
?Setup@TaskItemTranscation@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z PROC ; mu2::TaskItemTranscation::Setup, COMDAT

; 26   : {	

$LN683:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 81 ec 98 00
	00 00		 sub	 rsp, 152		; 00000098H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1309 :         return _Ptr;

  00016	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR resPtr$[rsp]
  0001e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00021	48 89 44 24 40	 mov	 QWORD PTR $T3[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h

; 191  : 		return ptr.get();

  00026	48 8b 44 24 40	 mov	 rax, QWORD PTR $T3[rsp]
  0002b	48 89 44 24 48	 mov	 QWORD PTR $T4[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp

; 27   : 	EResDbItemTransaction *res = static_cast<EResDbItemTransaction*>(resPtr.RawPtr());

  00030	48 8b 44 24 48	 mov	 rax, QWORD PTR $T4[rsp]
  00035	48 89 44 24 38	 mov	 QWORD PTR res$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1309 :         return _Ptr;

  0003a	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR reqPtr$[rsp]
  00042	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00045	48 89 44 24 50	 mov	 QWORD PTR $T5[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h

; 191  : 		return ptr.get();

  0004a	48 8b 44 24 50	 mov	 rax, QWORD PTR $T5[rsp]
  0004f	48 89 44 24 58	 mov	 QWORD PTR $T6[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp

; 28   : 	EReqDbItemTransaction *req = static_cast<EReqDbItemTransaction*>(reqPtr.RawPtr());

  00054	48 8b 44 24 58	 mov	 rax, QWORD PTR $T6[rsp]
  00059	48 89 44 24 28	 mov	 QWORD PTR req$[rsp], rax

; 29   : 
; 30   : 	req->jobNo = GetParentJobNo();

  0005e	48 8b 8c 24 a0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00066	e8 00 00 00 00	 call	 ?GetParentJobNo@WShopTask@mu2@@QEAA_KXZ ; mu2::WShopTask::GetParentJobNo
  0006b	48 8b 4c 24 28	 mov	 rcx, QWORD PTR req$[rsp]
  00070	48 89 41 50	 mov	 QWORD PTR [rcx+80], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h

; 30   : 	WShopDbUser& GetOwnerInfo() { return m_ownerInfo; }

  00074	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0007c	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00080	48 83 c0 38	 add	 rax, 56			; 00000038H
  00084	48 89 44 24 60	 mov	 QWORD PTR $T7[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp

; 31   : 	req->fcsAccountId = m_parentJob->GetOwnerInfo().fcsAccountId;

  00089	48 8b 44 24 28	 mov	 rax, QWORD PTR req$[rsp]
  0008e	48 8b 4c 24 60	 mov	 rcx, QWORD PTR $T7[rsp]
  00093	8b 49 0c	 mov	 ecx, DWORD PTR [rcx+12]
  00096	89 48 68	 mov	 DWORD PTR [rax+104], ecx
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h

; 35   : 	EWShopJobType GetJobType() { return m_eWShopJobType; }

  00099	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000a1	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  000a5	0f b6 40 30	 movzx	 eax, BYTE PTR [rax+48]
  000a9	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp

; 32   : 	req->jobType = static_cast<Byte>(m_parentJob->GetJobType());

  000ad	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  000b2	48 8b 4c 24 28	 mov	 rcx, QWORD PTR req$[rsp]
  000b7	88 41 58	 mov	 BYTE PTR [rcx+88], al

; 33   : 
; 34   : 	m_req = reqPtr;

  000ba	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000c2	48 05 f0 02 00
	00		 add	 rax, 752		; 000002f0H
  000c8	48 89 44 24 68	 mov	 QWORD PTR this$[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h

; 175  : 		this->ptr = r.ptr;

  000cd	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR reqPtr$[rsp]
  000d5	48 8b 4c 24 68	 mov	 rcx, QWORD PTR this$[rsp]
  000da	48 8b d0	 mov	 rdx, rax
  000dd	e8 00 00 00 00	 call	 ??4?$shared_ptr@UEvent@mu2@@@std@@QEAAAEAV01@AEBV01@@Z ; std::shared_ptr<mu2::Event>::operator=
  000e2	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp

; 36   : 	ErrorItem::Error error = ErrorItem::SUCCESS;

  000e3	c7 44 24 24 00
	00 00 00	 mov	 DWORD PTR error$[rsp], 0

; 37   : 
; 38   : 	VALID_DO(createTrans( reqPtr ), res->eError = ErrorDB::E_FAILED; 

  000eb	48 8b 94 24 a8
	00 00 00	 mov	 rdx, QWORD PTR reqPtr$[rsp]
  000f3	48 8b 8c 24 a0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  000fb	e8 00 00 00 00	 call	 ?createTrans@TaskItemTranscation@mu2@@AEAA_NAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ; mu2::TaskItemTranscation::createTrans
  00100	0f b6 c0	 movzx	 eax, al
  00103	85 c0		 test	 eax, eax
  00105	75 16		 jne	 SHORT $LN2@Setup
  00107	48 8b 44 24 38	 mov	 rax, QWORD PTR res$[rsp]
  0010c	c7 40 28 e7 03
	00 00		 mov	 DWORD PTR [rax+40], 999	; 000003e7H
  00113	b8 63 cc 05 00	 mov	 eax, 380003		; 0005cc63H
  00118	e9 f8 00 00 00	 jmp	 $LN1@Setup
$LN2@Setup:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1309 :         return _Ptr;

  0011d	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00125	48 8b 80 28 03
	00 00		 mov	 rax, QWORD PTR [rax+808]
  0012c	48 89 44 24 70	 mov	 QWORD PTR $T8[rsp], rax

; 1773 :         return get();

  00131	48 8b 44 24 70	 mov	 rax, QWORD PTR $T8[rsp]
  00136	48 89 44 24 78	 mov	 QWORD PTR $T9[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp

; 41   : 	error = m_trans->Begin( reqPtr );

  0013b	48 8b 44 24 78	 mov	 rax, QWORD PTR $T9[rsp]
  00140	48 89 44 24 30	 mov	 QWORD PTR tv161[rsp], rax
  00145	48 8b 44 24 30	 mov	 rax, QWORD PTR tv161[rsp]
  0014a	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0014d	48 8b 94 24 a8
	00 00 00	 mov	 rdx, QWORD PTR reqPtr$[rsp]
  00155	48 8b 4c 24 30	 mov	 rcx, QWORD PTR tv161[rsp]
  0015a	ff 10		 call	 QWORD PTR [rax]
  0015c	89 44 24 24	 mov	 DWORD PTR error$[rsp], eax

; 42   : 	VALID_DO( error == ErrorItem::SUCCESS, res->eError = ErrorDB::E_FAILED;

  00160	83 7c 24 24 00	 cmp	 DWORD PTR error$[rsp], 0
  00165	74 16		 je	 SHORT $LN3@Setup
  00167	48 8b 44 24 38	 mov	 rax, QWORD PTR res$[rsp]
  0016c	c7 40 28 e7 03
	00 00		 mov	 DWORD PTR [rax+40], 999	; 000003e7H
  00173	b8 63 cc 05 00	 mov	 eax, 380003		; 0005cc63H
  00178	e9 98 00 00 00	 jmp	 $LN1@Setup
$LN3@Setup:
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h

; 19   : 	UInt64 GetJobNo() { return m_jobNo; }

  0017d	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00185	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00189	48 8b 80 00 01
	00 00		 mov	 rax, QWORD PTR [rax+256]
  00190	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp

; 45   : 	m_dbJob.jobNo = m_parentJob->GetJobNo();

  00198	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  001a0	48 8b 8c 24 a0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  001a8	48 89 41 20	 mov	 QWORD PTR [rcx+32], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h

; 35   : 	EWShopJobType GetJobType() { return m_eWShopJobType; }

  001ac	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  001b4	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  001b8	0f b6 40 30	 movzx	 eax, BYTE PTR [rax+48]
  001bc	88 44 24 21	 mov	 BYTE PTR $T2[rsp], al
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp

; 46   : 	m_dbJob.jobType = m_parentJob->GetJobType();

  001c0	0f b6 44 24 21	 movzx	 eax, BYTE PTR $T2[rsp]
  001c5	48 8b 8c 24 a0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  001cd	88 41 28	 mov	 BYTE PTR [rcx+40], al
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h

; 30   : 	WShopDbUser& GetOwnerInfo() { return m_ownerInfo; }

  001d0	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  001d8	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  001dc	48 83 c0 38	 add	 rax, 56			; 00000038H
  001e0	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp

; 48   : 	m_dbJob.owner = m_parentJob->GetOwnerInfo();

  001e8	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  001f0	48 8b 8c 24 a0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  001f8	48 83 c1 30	 add	 rcx, 48			; 00000030H
  001fc	48 8b d0	 mov	 rdx, rax
  001ff	e8 00 00 00 00	 call	 ??4WShopDbUser@mu2@@QEAAAEAU01@AEBU01@@Z

; 49   : 
; 50   : 	SetBeginRequest( nullptr );

  00204	33 d2		 xor	 edx, edx
  00206	48 8b 8c 24 a0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0020e	e8 00 00 00 00	 call	 ?SetBeginRequest@WShopTask@mu2@@QEAAXPEAVIRequestParent@fcsa@@@Z ; mu2::WShopTask::SetBeginRequest

; 51   : 
; 52   : 	return ErrorWShop::SUCCESS;

  00213	33 c0		 xor	 eax, eax
$LN1@Setup:

; 53   : }

  00215	48 81 c4 98 00
	00 00		 add	 rsp, 152		; 00000098H
  0021c	c3		 ret	 0
?Setup@TaskItemTranscation@mu2@@QEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z ENDP ; mu2::TaskItemTranscation::Setup
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
;	COMDAT ?onResDbWShopItemTranscation@TaskItemTranscation@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z
_TEXT	SEGMENT
hConsole$1 = 80
tv147 = 88
res$ = 96
tv175 = 104
$T2 = 112
$T3 = 120
$T4 = 128
$T5 = 136
$T6 = 144
dbRes$ = 152
$T7 = 160
$T8 = 168
$T9 = 176
$T10 = 184
this$ = 208
dbResPtr$ = 216
resPtr$ = 224
?onResDbWShopItemTranscation@TaskItemTranscation@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z PROC ; mu2::TaskItemTranscation::onResDbWShopItemTranscation, COMDAT

; 133  : {

$LN41:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 81 ec c8 00
	00 00		 sub	 rsp, 200		; 000000c8H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1309 :         return _Ptr;

  00016	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR resPtr$[rsp]
  0001e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00021	48 89 44 24 70	 mov	 QWORD PTR $T2[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h

; 191  : 		return ptr.get();

  00026	48 8b 44 24 70	 mov	 rax, QWORD PTR $T2[rsp]
  0002b	48 89 44 24 78	 mov	 QWORD PTR $T3[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp

; 135  : 	EResDbItemTransaction* res = static_cast<EResDbItemTransaction*>(resPtr.RawPtr());

  00030	48 8b 44 24 78	 mov	 rax, QWORD PTR $T3[rsp]
  00035	48 89 44 24 60	 mov	 QWORD PTR res$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1309 :         return _Ptr;

  0003a	48 8b 84 24 d8
	00 00 00	 mov	 rax, QWORD PTR dbResPtr$[rsp]
  00042	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00045	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T4[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h

; 191  : 		return ptr.get();

  0004d	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR $T4[rsp]
  00055	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T5[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp

; 137  : 	EResDbItemTransaction* dbRes = static_cast<EResDbItemTransaction*>(dbResPtr.RawPtr());

  0005d	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T5[rsp]
  00065	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR dbRes$[rsp], rax

; 139  : 	MU2_ERROR_LOG( LogCategory::WSHOP, L"WSHOP(%u),%llu : onResDbWShopItemTranscation> Start",

  0006d	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  00072	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  00078	48 89 44 24 50	 mov	 QWORD PTR hConsole$1[rsp], rax
  0007d	66 ba 0d 00	 mov	 dx, 13
  00081	48 8b 4c 24 50	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  00086	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopJob.h

; 30   : 	WShopDbUser& GetOwnerInfo() { return m_ownerInfo; }

  0008c	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00094	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00098	48 83 c0 38	 add	 rax, 56			; 00000038H
  0009c	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T6[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp

; 139  : 	MU2_ERROR_LOG( LogCategory::WSHOP, L"WSHOP(%u),%llu : onResDbWShopItemTranscation> Start",

  000a4	48 8b 8c 24 d0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  000ac	e8 00 00 00 00	 call	 ?GetParentJobNo@WShopTask@mu2@@QEAA_KXZ ; mu2::WShopTask::GetParentJobNo
  000b1	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  000b6	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR $T6[rsp]
  000be	8b 40 0c	 mov	 eax, DWORD PTR [rax+12]
  000c1	89 44 24 38	 mov	 DWORD PTR [rsp+56], eax
  000c5	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_1GI@DJAOHGGM@?$AAW?$AAS?$AAH?$AAO?$AAP?$AA?$CI?$AA?$CF?$AAu?$AA?$CJ?$AA?0?$AA?$CF?$AAl?$AAl?$AAu?$AA?5@
  000cc	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  000d1	c7 44 24 28 8c
	00 00 00	 mov	 DWORD PTR [rsp+40], 140	; 0000008cH
  000d9	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FI@OJCHCMJH@F?3?2Release_Branch?2Server?2Develo@
  000e0	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  000e5	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0DG@HKGKMPPL@mu2?3?3TaskItemTranscation?3?3onRes@
  000ec	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  000f2	ba 14 00 00 00	 mov	 edx, 20
  000f7	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  000fe	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H0ZZ ; mu2::Logger::Logging
  00103	66 ba 07 00	 mov	 dx, 7
  00107	48 8b 4c 24 50	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  0010c	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  00112	90		 npad	 1

; 140  : 		m_parentJob->GetOwnerInfo().fcsAccountId, GetParentJobNo() );
; 141  : 
; 142  : 	if (dbRes->eError == ErrorDB::SUCCESS)

  00113	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR dbRes$[rsp]
  0011b	83 78 28 00	 cmp	 DWORD PTR [rax+40], 0
  0011f	75 69		 jne	 SHORT $LN2@onResDbWSh
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1309 :         return _Ptr;

  00121	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00129	48 8b 80 28 03
	00 00		 mov	 rax, QWORD PTR [rax+808]
  00130	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T7[rsp], rax

; 1773 :         return get();

  00138	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR $T7[rsp]
  00140	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T8[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp

; 144  : 		m_trans->Commit( resPtr );

  00148	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T8[rsp]
  00150	48 89 44 24 58	 mov	 QWORD PTR tv147[rsp], rax
  00155	48 8b 44 24 58	 mov	 rax, QWORD PTR tv147[rsp]
  0015a	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0015d	48 8b 94 24 e0
	00 00 00	 mov	 rdx, QWORD PTR resPtr$[rsp]
  00165	48 8b 4c 24 58	 mov	 rcx, QWORD PTR tv147[rsp]
  0016a	ff 50 08	 call	 QWORD PTR [rax+8]

; 145  : 
; 146  : 		res->eError = ErrorDB::SUCCESS;

  0016d	48 8b 44 24 60	 mov	 rax, QWORD PTR res$[rsp]
  00172	c7 40 28 00 00
	00 00		 mov	 DWORD PTR [rax+40], 0

; 147  : 		m_state = WShopTask::FINISHED;

  00179	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00181	c7 40 08 08 00
	00 00		 mov	 DWORD PTR [rax+8], 8

; 148  : 	}

  00188	eb 60		 jmp	 SHORT $LN3@onResDbWSh
$LN2@onResDbWSh:

; 149  : 	else
; 150  : 	{
; 151  : 		res->eError = ErrorDB::E_TRANSACION_FAILED;

  0018a	48 8b 44 24 60	 mov	 rax, QWORD PTR res$[rsp]
  0018f	c7 40 28 fc 03
	00 00		 mov	 DWORD PTR [rax+40], 1020 ; 000003fcH

; 152  : 		m_state = WShopTask::FINISHED;

  00196	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0019e	c7 40 08 08 00
	00 00		 mov	 DWORD PTR [rax+8], 8
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1309 :         return _Ptr;

  001a5	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  001ad	48 8b 80 28 03
	00 00		 mov	 rax, QWORD PTR [rax+808]
  001b4	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax

; 1773 :         return get();

  001bc	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR $T9[rsp]
  001c4	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp

; 154  : 		m_trans->RollBack();

  001cc	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  001d4	48 89 44 24 68	 mov	 QWORD PTR tv175[rsp], rax
  001d9	48 8b 44 24 68	 mov	 rax, QWORD PTR tv175[rsp]
  001de	48 8b 00	 mov	 rax, QWORD PTR [rax]
  001e1	48 8b 4c 24 68	 mov	 rcx, QWORD PTR tv175[rsp]
  001e6	ff 50 10	 call	 QWORD PTR [rax+16]
  001e9	90		 npad	 1
$LN3@onResDbWSh:

; 155  : 	}
; 156  : 
; 157  : 	return ErrorWShop::SUCCESS;

  001ea	33 c0		 xor	 eax, eax

; 158  : }

  001ec	48 81 c4 c8 00
	00 00		 add	 rsp, 200		; 000000c8H
  001f3	c3		 ret	 0
?onResDbWShopItemTranscation@TaskItemTranscation@mu2@@UEAA?AW4Error@ErrorWShop@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@0@Z ENDP ; mu2::TaskItemTranscation::onResDbWShopItemTranscation
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
;	COMDAT ?Response@TaskItemTranscation@mu2@@UEAA?AW4Error@ErrorWShop@2@PEAVIResponseParent@fcsa@@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
_TEXT	SEGMENT
tv69 = 0
this$ = 32
fcsRes$ = 40
resPtr$ = 48
?Response@TaskItemTranscation@mu2@@UEAA?AW4Error@ErrorWShop@2@PEAVIResponseParent@fcsa@@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z PROC ; mu2::TaskItemTranscation::Response, COMDAT

; 86   : {

$LN8:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 18	 sub	 rsp, 24

; 87   : 	UNREFERENCED_PARAMETER( fcsRes );
; 88   : 	UNREFERENCED_PARAMETER( resPtr );
; 89   : 
; 90   : 	switch (m_state)

  00013	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00018	8b 40 08	 mov	 eax, DWORD PTR [rax+8]
  0001b	89 04 24	 mov	 DWORD PTR tv69[rsp], eax

; 91   : 	{
; 92   : 	case WShopTask::REQUEST:
; 93   : 		break;
; 94   : 	case WShopTask::CHECK:
; 95   : 		break;
; 96   : 	case WShopTask::CONFIRM:
; 97   : 		break;
; 98   : 	};
; 99   : 
; 100  : 	return 	ErrorWShop::E_RETURN_FAIL_ON_FCSA;

  0001e	b8 68 cc 05 00	 mov	 eax, 380008		; 0005cc68H

; 101  : }

  00023	48 83 c4 18	 add	 rsp, 24
  00027	c3		 ret	 0
?Response@TaskItemTranscation@mu2@@UEAA?AW4Error@ErrorWShop@2@PEAVIResponseParent@fcsa@@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ENDP ; mu2::TaskItemTranscation::Response
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
;	COMDAT ??1TaskItemTranscation@mu2@@UEAA@XZ
_TEXT	SEGMENT
this$ = 32
this$ = 40
this$ = 64
??1TaskItemTranscation@mu2@@UEAA@XZ PROC		; mu2::TaskItemTranscation::~TaskItemTranscation, COMDAT

; 21   : {

$LN65:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H
  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7TaskItemTranscation@mu2@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx

; 22   : 	SetBeginRequest(nullptr);

  00018	33 d2		 xor	 edx, edx
  0001a	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0001f	e8 00 00 00 00	 call	 ?SetBeginRequest@WShopTask@mu2@@QEAAXPEAVIRequestParent@fcsa@@@Z ; mu2::WShopTask::SetBeginRequest
  00024	90		 npad	 1

; 23   : }

  00025	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0002a	48 05 28 03 00
	00		 add	 rax, 808		; 00000328H
  00030	48 89 44 24 20	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1384 :         if (_Rep) {

  00035	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0003a	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  0003f	74 0f		 je	 SHORT $LN10@TaskItemTr

; 1385 :             _Rep->_Decref();

  00041	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00046	48 8b 48 08	 mov	 rcx, QWORD PTR [rax+8]
  0004a	e8 00 00 00 00	 call	 ?_Decref@_Ref_count_base@std@@QEAAXXZ ; std::_Ref_count_base::_Decref
  0004f	90		 npad	 1
$LN10@TaskItemTr:
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h

; 172  : 	virtual~ISerializer() {}

  00050	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00055	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  0005c	48 89 88 08 03
	00 00		 mov	 QWORD PTR [rax+776], rcx
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h

; 166  : 	}

  00063	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00068	48 05 f0 02 00
	00		 add	 rax, 752		; 000002f0H
  0006e	48 89 44 24 28	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1384 :         if (_Rep) {

  00073	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00078	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  0007d	74 0f		 je	 SHORT $LN48@TaskItemTr

; 1385 :             _Rep->_Decref();

  0007f	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00084	48 8b 48 08	 mov	 rcx, QWORD PTR [rax+8]
  00088	e8 00 00 00 00	 call	 ?_Decref@_Ref_count_base@std@@QEAAXXZ ; std::_Ref_count_base::_Decref
  0008d	90		 npad	 1
$LN48@TaskItemTr:
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp

; 23   : }

  0008e	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00093	e8 00 00 00 00	 call	 ??1WShopTask@mu2@@UEAA@XZ ; mu2::WShopTask::~WShopTask
  00098	90		 npad	 1
  00099	48 83 c4 38	 add	 rsp, 56			; 00000038H
  0009d	c3		 ret	 0
??1TaskItemTranscation@mu2@@UEAA@XZ ENDP		; mu2::TaskItemTranscation::~TaskItemTranscation
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
;	COMDAT ??0TaskItemTranscation@mu2@@QEAA@PEAVWShopJob@1@@Z
_TEXT	SEGMENT
this$ = 32
this$ = 40
this$ = 64
parent$ = 72
??0TaskItemTranscation@mu2@@QEAA@PEAVWShopJob@1@@Z PROC	; mu2::TaskItemTranscation::TaskItemTranscation, COMDAT

; 16   : {

$LN66:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 15   : 	: WShopTask(parent)

  0000e	48 8b 54 24 48	 mov	 rdx, QWORD PTR parent$[rsp]
  00013	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00018	e8 00 00 00 00	 call	 ??0WShopTask@mu2@@QEAA@PEAVWShopJob@1@@Z ; mu2::WShopTask::WShopTask
  0001d	90		 npad	 1

; 16   : {

  0001e	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7TaskItemTranscation@mu2@@6B@
  0002a	48 89 08	 mov	 QWORD PTR [rax], rcx
  0002d	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00032	48 05 f0 02 00
	00		 add	 rax, 752		; 000002f0H
  00038	48 89 44 24 28	 mov	 QWORD PTR this$[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h

; 161  : 		: ptr(p)

  0003d	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00042	33 d2		 xor	 edx, edx
  00044	48 8b c8	 mov	 rcx, rax
  00047	e8 00 00 00 00	 call	 ??$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z ; std::shared_ptr<mu2::Event>::shared_ptr<mu2::Event><mu2::Event,0>
  0004c	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp

; 16   : {

  0004d	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00052	48 05 08 03 00
	00		 add	 rax, 776		; 00000308H
  00058	48 8b c8	 mov	 rcx, rax
  0005b	e8 00 00 00 00	 call	 ??0DateTimeEx@mu2@@QEAA@XZ ; mu2::DateTimeEx::DateTimeEx
  00060	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00065	48 05 28 03 00
	00		 add	 rax, 808		; 00000328H
  0006b	48 89 44 24 20	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1452 :     element_type* _Ptr{nullptr};

  00070	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00075	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0

; 1453 :     _Ref_count_base* _Rep{nullptr};

  0007c	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00081	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp

; 17   : }

  00089	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0008e	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00092	c3		 ret	 0
??0TaskItemTranscation@mu2@@QEAA@PEAVWShopJob@1@@Z ENDP	; mu2::TaskItemTranscation::TaskItemTranscation
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
this$ = 32
this$ = 40
this$ = 64
parent$ = 72
?dtor$0@?0???0TaskItemTranscation@mu2@@QEAA@PEAVWShopJob@1@@Z@4HA PROC ; `mu2::TaskItemTranscation::TaskItemTranscation'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 40	 mov	 rcx, QWORD PTR this$[rbp]
  0000d	e8 00 00 00 00	 call	 ??1WShopTask@mu2@@UEAA@XZ ; mu2::WShopTask::~WShopTask
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$0@?0???0TaskItemTranscation@mu2@@QEAA@PEAVWShopJob@1@@Z@4HA ENDP ; `mu2::TaskItemTranscation::TaskItemTranscation'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
this$ = 32
this$ = 40
this$ = 64
parent$ = 72
?dtor$1@?0???0TaskItemTranscation@mu2@@QEAA@PEAVWShopJob@1@@Z@4HA PROC ; `mu2::TaskItemTranscation::TaskItemTranscation'::`1'::dtor$1
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 40	 mov	 rcx, QWORD PTR this$[rbp]
  0000d	48 81 c1 f0 02
	00 00		 add	 rcx, 752		; 000002f0H
  00014	e8 00 00 00 00	 call	 ??1?$SmartPtrEx@UEvent@mu2@@@mu2@@QEAA@XZ ; mu2::SmartPtrEx<mu2::Event>::~SmartPtrEx<mu2::Event>
  00019	48 83 c4 20	 add	 rsp, 32			; 00000020H
  0001d	5d		 pop	 rbp
  0001e	c3		 ret	 0
?dtor$1@?0???0TaskItemTranscation@mu2@@QEAA@PEAVWShopJob@1@@Z@4HA ENDP ; `mu2::TaskItemTranscation::TaskItemTranscation'::`1'::dtor$1
text$x	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\KnightageTranscation.h
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\BaseItemTranscation.h
;	COMDAT ??_GKnightageFollowerTrans@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GKnightageFollowerTrans@mu2@@UEAAPEAXI@Z PROC	; mu2::KnightageFollowerTrans::`scalar deleting destructor', COMDAT
$LN30:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\KnightageTranscation.h

; 67   : 		{}

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7KnightageFollowerTrans@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 20   : 		{}

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7KnightageBaseTrans@mu2@@6B@
  00028	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h

; 172  : 	virtual~ISerializer() {}

  0002b	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00030	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  00037	48 89 48 20	 mov	 QWORD PTR [rax+32], rcx
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\BaseItemTranscation.h

; 20   : 		{}

  0003b	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00040	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7BaseTrans@mu2@@6B@
  00047	48 89 08	 mov	 QWORD PTR [rax], rcx
  0004a	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  0004e	83 e0 01	 and	 eax, 1
  00051	85 c0		 test	 eax, eax
  00053	74 10		 je	 SHORT $LN2@scalar
  00055	ba 40 00 00 00	 mov	 edx, 64			; 00000040H
  0005a	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0005f	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00064	90		 npad	 1
$LN2@scalar:
  00065	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0006a	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0006e	c3		 ret	 0
??_GKnightageFollowerTrans@mu2@@UEAAPEAXI@Z ENDP	; mu2::KnightageFollowerTrans::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\KnightageTranscation.h
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\BaseItemTranscation.h
;	COMDAT ??_GKnightageGiveTrophyTrans@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GKnightageGiveTrophyTrans@mu2@@UEAAPEAXI@Z PROC	; mu2::KnightageGiveTrophyTrans::`scalar deleting destructor', COMDAT
$LN30:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\KnightageTranscation.h

; 47   : 		{}

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7KnightageGiveTrophyTrans@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 20   : 		{}

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7KnightageBaseTrans@mu2@@6B@
  00028	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h

; 172  : 	virtual~ISerializer() {}

  0002b	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00030	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  00037	48 89 48 20	 mov	 QWORD PTR [rax+32], rcx
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\BaseItemTranscation.h

; 20   : 		{}

  0003b	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00040	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7BaseTrans@mu2@@6B@
  00047	48 89 08	 mov	 QWORD PTR [rax], rcx
  0004a	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  0004e	83 e0 01	 and	 eax, 1
  00051	85 c0		 test	 eax, eax
  00053	74 10		 je	 SHORT $LN2@scalar
  00055	ba 40 00 00 00	 mov	 edx, 64			; 00000040H
  0005a	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0005f	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00064	90		 npad	 1
$LN2@scalar:
  00065	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0006a	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0006e	c3		 ret	 0
??_GKnightageGiveTrophyTrans@mu2@@UEAAPEAXI@Z ENDP	; mu2::KnightageGiveTrophyTrans::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\KnightageTranscation.h
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\BaseItemTranscation.h
;	COMDAT ??_GKnightageBaseTrans@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GKnightageBaseTrans@mu2@@UEAAPEAXI@Z PROC		; mu2::KnightageBaseTrans::`scalar deleting destructor', COMDAT
$LN25:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\KnightageTranscation.h

; 20   : 		{}

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7KnightageBaseTrans@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h

; 172  : 	virtual~ISerializer() {}

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  00028	48 89 48 20	 mov	 QWORD PTR [rax+32], rcx
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\BaseItemTranscation.h

; 20   : 		{}

  0002c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00031	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7BaseTrans@mu2@@6B@
  00038	48 89 08	 mov	 QWORD PTR [rax], rcx
  0003b	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  0003f	83 e0 01	 and	 eax, 1
  00042	85 c0		 test	 eax, eax
  00044	74 10		 je	 SHORT $LN2@scalar
  00046	ba 38 00 00 00	 mov	 edx, 56			; 00000038H
  0004b	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00050	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00055	90		 npad	 1
$LN2@scalar:
  00056	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0005b	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0005f	c3		 ret	 0
??_GKnightageBaseTrans@mu2@@UEAAPEAXI@Z ENDP		; mu2::KnightageBaseTrans::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\KnightageTranscation.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\BaseItemTranscation.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\KnightageTranscation.h
;	COMDAT ??0KnightageBaseTrans@mu2@@QEAA@W4ID@BaseTrans@1@IAEAUChangedMoney@1@HH@Z
_TEXT	SEGMENT
this$ = 0
this$ = 32
id$ = 40
knightageId$ = 48
money$ = 56
trophyCount$ = 64
contributionPoint$ = 72
??0KnightageBaseTrans@mu2@@QEAA@W4ID@BaseTrans@1@IAEAUChangedMoney@1@HH@Z PROC ; mu2::KnightageBaseTrans::KnightageBaseTrans, COMDAT

; 17   : 		{}

$LN17:
  00000	4c 89 4c 24 20	 mov	 QWORD PTR [rsp+32], r9
  00005	44 89 44 24 18	 mov	 DWORD PTR [rsp+24], r8d
  0000a	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  0000e	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00013	48 83 ec 18	 sub	 rsp, 24
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\BaseItemTranscation.h

; 18   : 		{}

  00017	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0001c	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7BaseTrans@mu2@@6B@
  00023	48 89 08	 mov	 QWORD PTR [rax], rcx

; 17   : 			m_id( id )

  00026	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0002b	8b 4c 24 28	 mov	 ecx, DWORD PTR id$[rsp]
  0002f	89 48 08	 mov	 DWORD PTR [rax+8], ecx
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\KnightageTranscation.h

; 17   : 		{}

  00032	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00037	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7KnightageBaseTrans@mu2@@6B@
  0003e	48 89 08	 mov	 QWORD PTR [rax], rcx

; 12   : 			, m_knightageId( knightageId )

  00041	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00046	8b 4c 24 30	 mov	 ecx, DWORD PTR knightageId$[rsp]
  0004a	89 48 10	 mov	 DWORD PTR [rax+16], ecx

; 13   : 			, m_updatePoint( 0 )

  0004d	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00052	c7 40 14 00 00
	00 00		 mov	 DWORD PTR [rax+20], 0

; 14   : 			, m_trophyCount(trophyCount)

  00059	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0005e	8b 4c 24 40	 mov	 ecx, DWORD PTR trophyCount$[rsp]
  00062	89 48 18	 mov	 DWORD PTR [rax+24], ecx

; 15   : 			, m_contributionPoint(contributionPoint)

  00065	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0006a	8b 4c 24 48	 mov	 ecx, DWORD PTR contributionPoint$[rsp]
  0006e	89 48 1c	 mov	 DWORD PTR [rax+28], ecx

; 16   : 			, m_money(money)

  00071	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00076	48 83 c0 20	 add	 rax, 32			; 00000020H
  0007a	48 89 04 24	 mov	 QWORD PTR this$[rsp], rax
  0007e	48 8b 04 24	 mov	 rax, QWORD PTR this$[rsp]
  00082	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  00089	48 89 08	 mov	 QWORD PTR [rax], rcx
  0008c	48 8b 04 24	 mov	 rax, QWORD PTR this$[rsp]
  00090	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ChangedMoney@mu2@@6B@
  00097	48 89 08	 mov	 QWORD PTR [rax], rcx
  0009a	48 8b 04 24	 mov	 rax, QWORD PTR this$[rsp]
  0009e	48 8b 4c 24 38	 mov	 rcx, QWORD PTR money$[rsp]
  000a3	48 8b 49 08	 mov	 rcx, QWORD PTR [rcx+8]
  000a7	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx
  000ab	48 8b 04 24	 mov	 rax, QWORD PTR this$[rsp]
  000af	48 8b 4c 24 38	 mov	 rcx, QWORD PTR money$[rsp]
  000b4	0f b6 49 10	 movzx	 ecx, BYTE PTR [rcx+16]
  000b8	88 48 10	 mov	 BYTE PTR [rax+16], cl
  000bb	48 8b 04 24	 mov	 rax, QWORD PTR this$[rsp]
  000bf	48 8b 4c 24 38	 mov	 rcx, QWORD PTR money$[rsp]
  000c4	0f b6 49 11	 movzx	 ecx, BYTE PTR [rcx+17]
  000c8	88 48 11	 mov	 BYTE PTR [rax+17], cl

; 17   : 		{}

  000cb	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  000d0	48 83 c4 18	 add	 rsp, 24
  000d4	c3		 ret	 0
??0KnightageBaseTrans@mu2@@QEAA@W4ID@BaseTrans@1@IAEAUChangedMoney@1@HH@Z ENDP ; mu2::KnightageBaseTrans::KnightageBaseTrans
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\BaseItemTranscation.h
;	COMDAT ??_GCharacterSlotExtendTrans@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GCharacterSlotExtendTrans@mu2@@UEAAPEAXI@Z PROC	; mu2::CharacterSlotExtendTrans::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 40   : 		virtual ~CharacterSlotExtendTrans() {}

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7CharacterSlotExtendTrans@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 20   : 		{}

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7BaseTrans@mu2@@6B@
  00028	48 89 08	 mov	 QWORD PTR [rax], rcx
  0002b	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  0002f	83 e0 01	 and	 eax, 1
  00032	85 c0		 test	 eax, eax
  00034	74 10		 je	 SHORT $LN2@scalar
  00036	ba 18 00 00 00	 mov	 edx, 24
  0003b	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00040	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00045	90		 npad	 1
$LN2@scalar:
  00046	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004b	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0004f	c3		 ret	 0
??_GCharacterSlotExtendTrans@mu2@@UEAAPEAXI@Z ENDP	; mu2::CharacterSlotExtendTrans::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\BaseItemTranscation.h
;	COMDAT ?RollBack@BaseTrans@mu2@@UEAA?AW4Error@ErrorItem@2@XZ
_TEXT	SEGMENT
this$ = 8
?RollBack@BaseTrans@mu2@@UEAA?AW4Error@ErrorItem@2@XZ PROC ; mu2::BaseTrans::RollBack, COMDAT

; 24   : 		virtual ErrorItem::Error RollBack() { return ErrorItem::SUCCESS; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	33 c0		 xor	 eax, eax
  00007	c3		 ret	 0
?RollBack@BaseTrans@mu2@@UEAA?AW4Error@ErrorItem@2@XZ ENDP ; mu2::BaseTrans::RollBack
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\BaseItemTranscation.h
;	COMDAT ?Commit@BaseTrans@mu2@@UEAA?AW4Error@ErrorItem@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
_TEXT	SEGMENT
this$ = 8
reqPtr$ = 16
?Commit@BaseTrans@mu2@@UEAA?AW4Error@ErrorItem@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z PROC ; mu2::BaseTrans::Commit, COMDAT

; 23   : 		virtual ErrorItem::Error Commit( EventPtr& reqPtr ) { reqPtr; return ErrorItem::SUCCESS; }

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	33 c0		 xor	 eax, eax
  0000c	c3		 ret	 0
?Commit@BaseTrans@mu2@@UEAA?AW4Error@ErrorItem@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ENDP ; mu2::BaseTrans::Commit
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\BaseItemTranscation.h
;	COMDAT ?Begin@BaseTrans@mu2@@UEAA?AW4Error@ErrorItem@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
_TEXT	SEGMENT
this$ = 8
reqPtr$ = 16
?Begin@BaseTrans@mu2@@UEAA?AW4Error@ErrorItem@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z PROC ; mu2::BaseTrans::Begin, COMDAT

; 22   : 		virtual ErrorItem::Error Begin( EventPtr& reqPtr ) { reqPtr; return ErrorItem::SUCCESS; }

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	33 c0		 xor	 eax, eax
  0000c	c3		 ret	 0
?Begin@BaseTrans@mu2@@UEAA?AW4Error@ErrorItem@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ENDP ; mu2::BaseTrans::Begin
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
_TEXT	SEGMENT
_Ptr_container$ = 48
_Block_size$ = 56
_Ptr$ = 64
$T1 = 72
_Bytes$ = 96
??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z PROC ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>, COMDAT

; 182  : __declspec(allocator) void* _Allocate_manually_vector_aligned(const size_t _Bytes) {

$LN7:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 183  :     // allocate _Bytes manually aligned to at least _Big_allocation_alignment
; 184  :     const size_t _Block_size = _Non_user_size + _Bytes;

  00009	48 8b 44 24 60	 mov	 rax, QWORD PTR _Bytes$[rsp]
  0000e	48 83 c0 27	 add	 rax, 39			; 00000027H
  00012	48 89 44 24 38	 mov	 QWORD PTR _Block_size$[rsp], rax

; 185  :     if (_Block_size <= _Bytes) {

  00017	48 8b 44 24 60	 mov	 rax, QWORD PTR _Bytes$[rsp]
  0001c	48 39 44 24 38	 cmp	 QWORD PTR _Block_size$[rsp], rax
  00021	77 06		 ja	 SHORT $LN2@Allocate_m

; 186  :         _Throw_bad_array_new_length(); // add overflow

  00023	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00028	90		 npad	 1
$LN2@Allocate_m:

; 136  :         return ::operator new(_Bytes);

  00029	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Block_size$[rsp]
  0002e	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00033	48 89 44 24 48	 mov	 QWORD PTR $T1[rsp], rax

; 187  :     }
; 188  : 
; 189  :     const uintptr_t _Ptr_container = reinterpret_cast<uintptr_t>(_Traits::_Allocate(_Block_size));

  00038	48 8b 44 24 48	 mov	 rax, QWORD PTR $T1[rsp]
  0003d	48 89 44 24 30	 mov	 QWORD PTR _Ptr_container$[rsp], rax

; 190  :     _STL_VERIFY(_Ptr_container != 0, "invalid argument"); // validate even in release since we're doing p[-1]

  00042	48 83 7c 24 30
	00		 cmp	 QWORD PTR _Ptr_container$[rsp], 0
  00048	75 19		 jne	 SHORT $LN3@Allocate_m
  0004a	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR [rsp+32], 0
  00053	45 33 c9	 xor	 r9d, r9d
  00056	45 33 c0	 xor	 r8d, r8d
  00059	33 d2		 xor	 edx, edx
  0005b	33 c9		 xor	 ecx, ecx
  0005d	e8 00 00 00 00	 call	 _invoke_watson
  00062	90		 npad	 1
$LN3@Allocate_m:

; 191  :     void* const _Ptr = reinterpret_cast<void*>((_Ptr_container + _Non_user_size) & ~(_Big_allocation_alignment - 1));

  00063	48 8b 44 24 30	 mov	 rax, QWORD PTR _Ptr_container$[rsp]
  00068	48 83 c0 27	 add	 rax, 39			; 00000027H
  0006c	48 83 e0 e0	 and	 rax, -32		; ffffffffffffffe0H
  00070	48 89 44 24 40	 mov	 QWORD PTR _Ptr$[rsp], rax

; 192  :     static_cast<uintptr_t*>(_Ptr)[-1] = _Ptr_container;

  00075	b8 08 00 00 00	 mov	 eax, 8
  0007a	48 6b c0 ff	 imul	 rax, rax, -1
  0007e	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  00083	48 8b 54 24 30	 mov	 rdx, QWORD PTR _Ptr_container$[rsp]
  00088	48 89 14 01	 mov	 QWORD PTR [rcx+rax], rdx

; 193  : 
; 194  : #ifdef _DEBUG
; 195  :     static_cast<uintptr_t*>(_Ptr)[-2] = _Big_allocation_sentinel;
; 196  : #endif // defined(_DEBUG)
; 197  :     return _Ptr;

  0008c	48 8b 44 24 40	 mov	 rax, QWORD PTR _Ptr$[rsp]
$LN4@Allocate_m:

; 198  : }

  00091	48 83 c4 58	 add	 rsp, 88			; 00000058H
  00095	c3		 ret	 0
??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ENDP ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??$_Reallocate_for@V<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@Z
_TEXT	SEGMENT
$T1 = 32
_New_capacity$ = 40
_Bytes$ = 48
_New_ptr$ = 56
_Fancy_ptr$2 = 64
_New_ptr$ = 72
_Old_capacity$ = 80
_Ptr$ = 88
$T3 = 96
$T4 = 104
$T5 = 112
_Al$ = 120
$T6 = 128
$T7 = 136
_Ptr$ = 144
$T8 = 152
_Old_ptr$ = 160
$T9 = 168
$T10 = 176
$T11 = 184
$T12 = 192
this$ = 224
_New_size$ = 232
_Fn$ = 240
<_Args_0>$ = 248
??$_Reallocate_for@V<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@Z PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Reallocate_for<<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>,wchar_t const *>, COMDAT

; 2995 :     _CONSTEXPR20 basic_string& _Reallocate_for(const size_type _New_size, _Fty _Fn, _ArgTys... _Args) {

$LN178:
  00000	4c 89 4c 24 20	 mov	 QWORD PTR [rsp+32], r9
  00005	44 88 44 24 18	 mov	 BYTE PTR [rsp+24], r8b
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	48 81 ec d8 00
	00 00		 sub	 rsp, 216		; 000000d8H

; 2996 :         // reallocate to store exactly _New_size elements, new buffer prepared by
; 2997 :         // _Fn(_New_ptr, _New_size, _Args...)
; 2998 :         if (_New_size > max_size()) {

  0001b	48 8b 8c 24 e0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00023	e8 00 00 00 00	 call	 ?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
  00028	48 39 84 24 e8
	00 00 00	 cmp	 QWORD PTR _New_size$[rsp], rax
  00030	76 06		 jbe	 SHORT $LN2@Reallocate

; 2999 :             _Xlen_string(); // result too long

  00032	e8 00 00 00 00	 call	 ?_Xlen_string@std@@YAXXZ ; std::_Xlen_string
  00037	90		 npad	 1
$LN2@Reallocate:

; 3000 :         }
; 3001 : 
; 3002 :         const size_type _Old_capacity = _Mypair._Myval2._Myres;

  00038	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00040	48 8b 40 18	 mov	 rax, QWORD PTR [rax+24]
  00044	48 89 44 24 50	 mov	 QWORD PTR _Old_capacity$[rsp], rax

; 2991 :         return _Calculate_growth(_Requested, _Mypair._Myval2._Myres, max_size());

  00049	48 8b 8c 24 e0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00051	e8 00 00 00 00	 call	 ?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
  00056	4c 8b c0	 mov	 r8, rax
  00059	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00061	48 8b 50 18	 mov	 rdx, QWORD PTR [rax+24]
  00065	48 8b 8c 24 e8
	00 00 00	 mov	 rcx, QWORD PTR _New_size$[rsp]
  0006d	e8 00 00 00 00	 call	 ?_Calculate_growth@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@CA_K_K00@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Calculate_growth
  00072	48 89 44 24 60	 mov	 QWORD PTR $T3[rsp], rax

; 3003 :         size_type _New_capacity       = _Calculate_growth(_New_size);

  00077	48 8b 44 24 60	 mov	 rax, QWORD PTR $T3[rsp]
  0007c	48 89 44 24 28	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 3107 :         return _Mypair._Get_first();

  00081	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00089	48 89 44 24 68	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3107 :         return _Mypair._Get_first();

  0008e	48 8b 44 24 68	 mov	 rax, QWORD PTR $T4[rsp]
  00093	48 89 44 24 70	 mov	 QWORD PTR $T5[rsp], rax

; 3004 :         auto& _Al                     = _Getal();

  00098	48 8b 44 24 70	 mov	 rax, QWORD PTR $T5[rsp]
  0009d	48 89 44 24 78	 mov	 QWORD PTR _Al$[rsp], rax

; 825  :         ++_Capacity; // Take null terminator into consideration

  000a2	48 8b 44 24 28	 mov	 rax, QWORD PTR _New_capacity$[rsp]
  000a7	48 ff c0	 inc	 rax
  000aa	48 89 44 24 28	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 826  : 
; 827  :         pointer _Fancy_ptr = nullptr;

  000af	48 c7 44 24 40
	00 00 00 00	 mov	 QWORD PTR _Fancy_ptr$2[rsp], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 2303 :         return _Al.allocate(_Count);

  000b8	48 8b 54 24 28	 mov	 rdx, QWORD PTR _New_capacity$[rsp]
  000bd	48 8b 4c 24 78	 mov	 rcx, QWORD PTR _Al$[rsp]
  000c2	e8 00 00 00 00	 call	 ?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z ; std::allocator<wchar_t>::allocate
  000c7	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 829  :             _Fancy_ptr = _Allocate_at_least_helper(_Al, _Capacity);

  000cf	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR $T6[rsp]
  000d7	48 89 44 24 40	 mov	 QWORD PTR _Fancy_ptr$2[rsp], rax

; 830  :         } else {
; 831  :             _STL_INTERNAL_STATIC_ASSERT(_Policy == _Allocation_policy::_Exactly);
; 832  :             _Fancy_ptr = _Al.allocate(_Capacity);
; 833  :         }
; 834  : 
; 835  : #if _HAS_CXX20
; 836  :         // Start element lifetimes to avoid UB. This is a more general mechanism than _String_val::_Activate_SSO_buffer,
; 837  :         // but likely more impactful to throughput.
; 838  :         if (_STD is_constant_evaluated()) {
; 839  :             _Elem* const _Ptr = _Unfancy(_Fancy_ptr);
; 840  :             for (size_type _Idx = 0; _Idx < _Capacity; ++_Idx) {
; 841  :                 _STD construct_at(_Ptr + _Idx);
; 842  :             }
; 843  :         }
; 844  : #endif // _HAS_CXX20
; 845  :         --_Capacity;

  000dc	48 8b 44 24 28	 mov	 rax, QWORD PTR _New_capacity$[rsp]
  000e1	48 ff c8	 dec	 rax
  000e4	48 89 44 24 28	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 846  :         return _Fancy_ptr;

  000e9	48 8b 44 24 40	 mov	 rax, QWORD PTR _Fancy_ptr$2[rsp]
  000ee	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T7[rsp], rax

; 3005 :         const pointer _New_ptr        = _Allocate_for_capacity(_Al, _New_capacity); // throws

  000f6	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T7[rsp]
  000fe	48 89 44 24 38	 mov	 QWORD PTR _New_ptr$[rsp], rax

; 3006 : 
; 3007 :         _Mypair._Myval2._Orphan_all();
; 3008 :         _ASAN_STRING_REMOVE(*this);
; 3009 :         _Mypair._Myval2._Mysize = _New_size;

  00103	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0010b	48 8b 8c 24 e8
	00 00 00	 mov	 rcx, QWORD PTR _New_size$[rsp]
  00113	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 3010 :         _Mypair._Myval2._Myres  = _New_capacity;

  00117	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0011f	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _New_capacity$[rsp]
  00124	48 89 48 18	 mov	 QWORD PTR [rax+24], rcx

; 3011 :         _Fn(_Unfancy(_New_ptr), _New_size, _Args...);

  00128	48 8b 44 24 38	 mov	 rax, QWORD PTR _New_ptr$[rsp]
  0012d	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  00135	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR _Ptr$[rsp]
  0013d	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3011 :         _Fn(_Unfancy(_New_ptr), _New_size, _Args...);

  00145	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR $T8[rsp]
  0014d	48 89 44 24 48	 mov	 QWORD PTR _New_ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  00152	48 8b 84 24 e8
	00 00 00	 mov	 rax, QWORD PTR _New_size$[rsp]
  0015a	48 03 c0	 add	 rax, rax
  0015d	4c 8b c0	 mov	 r8, rax
  00160	48 8b 94 24 f8
	00 00 00	 mov	 rdx, QWORD PTR <_Args_0>$[rsp]
  00168	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _New_ptr$[rsp]
  0016d	e8 00 00 00 00	 call	 memcpy
  00172	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1632 :                 _Traits::assign(_New_ptr[_Count], _Elem());

  00173	33 c0		 xor	 eax, eax
  00175	66 89 44 24 20	 mov	 WORD PTR $T1[rsp], ax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 347  :         _Left = _Right;

  0017a	48 8b 44 24 48	 mov	 rax, QWORD PTR _New_ptr$[rsp]
  0017f	48 8b 8c 24 e8
	00 00 00	 mov	 rcx, QWORD PTR _New_size$[rsp]
  00187	0f b7 54 24 20	 movzx	 edx, WORD PTR $T1[rsp]
  0018c	66 89 14 48	 mov	 WORD PTR [rax+rcx*2], dx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3012 :         if (_Old_capacity > _Small_string_capacity) {

  00190	48 83 7c 24 50
	07		 cmp	 QWORD PTR _Old_capacity$[rsp], 7
  00196	76 6c		 jbe	 SHORT $LN3@Reallocate

; 3013 :             _Deallocate_for_capacity(_Al, _Mypair._Myval2._Bx._Ptr, _Old_capacity);

  00198	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  001a0	48 8b 00	 mov	 rax, QWORD PTR [rax]
  001a3	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR _Old_ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  001ab	48 8b 44 24 50	 mov	 rax, QWORD PTR _Old_capacity$[rsp]
  001b0	48 8d 44 00 02	 lea	 rax, QWORD PTR [rax+rax+2]
  001b5	48 89 44 24 30	 mov	 QWORD PTR _Bytes$[rsp], rax
  001ba	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR _Old_ptr$[rsp]
  001c2	48 89 44 24 58	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  001c7	48 81 7c 24 30
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  001d0	72 10		 jb	 SHORT $LN148@Reallocate

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  001d2	48 8d 54 24 30	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  001d7	48 8d 4c 24 58	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  001dc	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  001e1	90		 npad	 1
$LN148@Reallocate:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  001e2	48 8b 54 24 30	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  001e7	48 8b 4c 24 58	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  001ec	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  001f1	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3014 :             _Mypair._Myval2._Bx._Ptr = _New_ptr;

  001f2	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  001fa	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _New_ptr$[rsp]
  001ff	48 89 08	 mov	 QWORD PTR [rax], rcx

; 3015 :         } else {

  00202	eb 53		 jmp	 SHORT $LN4@Reallocate
$LN3@Reallocate:

; 3016 :             _Construct_in_place(_Mypair._Myval2._Bx._Ptr, _New_ptr);

  00204	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0020c	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00214	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T9[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  0021c	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00224	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  0022c	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00234	48 8d 44 24 38	 lea	 rax, QWORD PTR _New_ptr$[rsp]
  00239	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00241	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  00249	48 8b 8c 24 c0
	00 00 00	 mov	 rcx, QWORD PTR $T12[rsp]
  00251	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00254	48 89 08	 mov	 QWORD PTR [rax], rcx
$LN4@Reallocate:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3020 :         return *this;

  00257	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]

; 3021 :     }

  0025f	48 81 c4 d8 00
	00 00		 add	 rsp, 216		; 000000d8H
  00266	c3		 ret	 0
$LN177@Reallocate:
??$_Reallocate_for@V<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@Z ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Reallocate_for<<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>,wchar_t const *>
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??_G?$_Ref_count@UEvent@mu2@@@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_G?$_Ref_count@UEvent@mu2@@@std@@UEAAPEAXI@Z PROC	; std::_Ref_count<mu2::Event>::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000d	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00011	83 e0 01	 and	 eax, 1
  00014	85 c0		 test	 eax, eax
  00016	74 10		 je	 SHORT $LN2@scalar
  00018	ba 18 00 00 00	 mov	 edx, 24
  0001d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00022	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00027	90		 npad	 1
$LN2@scalar:
  00028	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0002d	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00031	c3		 ret	 0
??_G?$_Ref_count@UEvent@mu2@@@std@@UEAAPEAXI@Z ENDP	; std::_Ref_count<mu2::Event>::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ?_Delete_this@?$_Ref_count@UEvent@mu2@@@std@@EEAAXXZ
_TEXT	SEGMENT
$T1 = 32
tv74 = 40
this$ = 64
?_Delete_this@?$_Ref_count@UEvent@mu2@@@std@@EEAAXXZ PROC ; std::_Ref_count<mu2::Event>::_Delete_this, COMDAT

; 1190 :     void _Delete_this() noexcept override { // destroy self

$LN6:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 1191 :         delete this;

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
  00013	48 83 7c 24 20
	00		 cmp	 QWORD PTR $T1[rsp], 0
  00019	74 1c		 je	 SHORT $LN3@Delete_thi
  0001b	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00020	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00023	ba 01 00 00 00	 mov	 edx, 1
  00028	48 8b 4c 24 20	 mov	 rcx, QWORD PTR $T1[rsp]
  0002d	ff 50 10	 call	 QWORD PTR [rax+16]
  00030	48 89 44 24 28	 mov	 QWORD PTR tv74[rsp], rax
  00035	eb 09		 jmp	 SHORT $LN4@Delete_thi
$LN3@Delete_thi:
  00037	48 c7 44 24 28
	00 00 00 00	 mov	 QWORD PTR tv74[rsp], 0
$LN4@Delete_thi:

; 1192 :     }

  00040	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00044	c3		 ret	 0
?_Delete_this@?$_Ref_count@UEvent@mu2@@@std@@EEAAXXZ ENDP ; std::_Ref_count<mu2::Event>::_Delete_this
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ?_Destroy@?$_Ref_count@UEvent@mu2@@@std@@EEAAXXZ
_TEXT	SEGMENT
$T1 = 32
tv71 = 40
this$ = 64
?_Destroy@?$_Ref_count@UEvent@mu2@@@std@@EEAAXXZ PROC	; std::_Ref_count<mu2::Event>::_Destroy, COMDAT

; 1186 :     void _Destroy() noexcept override { // destroy managed resource

$LN6:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 1187 :         delete _Ptr;

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00012	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
  00017	48 83 7c 24 20
	00		 cmp	 QWORD PTR $T1[rsp], 0
  0001d	74 1b		 je	 SHORT $LN3@Destroy
  0001f	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00024	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00027	ba 01 00 00 00	 mov	 edx, 1
  0002c	48 8b 4c 24 20	 mov	 rcx, QWORD PTR $T1[rsp]
  00031	ff 10		 call	 QWORD PTR [rax]
  00033	48 89 44 24 28	 mov	 QWORD PTR tv71[rsp], rax
  00038	eb 09		 jmp	 SHORT $LN4@Destroy
$LN3@Destroy:
  0003a	48 c7 44 24 28
	00 00 00 00	 mov	 QWORD PTR tv71[rsp], 0
$LN4@Destroy:

; 1188 :     }

  00043	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00047	c3		 ret	 0
?_Destroy@?$_Ref_count@UEvent@mu2@@@std@@EEAAXXZ ENDP	; std::_Ref_count<mu2::Event>::_Destroy
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ??1?$_Temporary_owner@UEvent@mu2@@@std@@QEAA@XZ
_TEXT	SEGMENT
$T1 = 32
tv71 = 40
this$ = 64
??1?$_Temporary_owner@UEvent@mu2@@@std@@QEAA@XZ PROC	; std::_Temporary_owner<mu2::Event>::~_Temporary_owner<mu2::Event>, COMDAT

; 1529 :     ~_Temporary_owner() {

$LN6:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 1530 :         delete _Ptr;

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00011	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
  00016	48 83 7c 24 20
	00		 cmp	 QWORD PTR $T1[rsp], 0
  0001c	74 1b		 je	 SHORT $LN3@Temporary_
  0001e	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00023	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00026	ba 01 00 00 00	 mov	 edx, 1
  0002b	48 8b 4c 24 20	 mov	 rcx, QWORD PTR $T1[rsp]
  00030	ff 10		 call	 QWORD PTR [rax]
  00032	48 89 44 24 28	 mov	 QWORD PTR tv71[rsp], rax
  00037	eb 09		 jmp	 SHORT $LN4@Temporary_
$LN3@Temporary_:
  00039	48 c7 44 24 28
	00 00 00 00	 mov	 QWORD PTR tv71[rsp], 0
$LN4@Temporary_:

; 1531 :     }

  00042	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00046	c3		 ret	 0
??1?$_Temporary_owner@UEvent@mu2@@@std@@QEAA@XZ ENDP	; std::_Temporary_owner<mu2::Event>::~_Temporary_owner<mu2::Event>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ??$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z
_TEXT	SEGMENT
$T1 = 32
_Owner$2 = 40
$T3 = 48
tv89 = 56
_Px$ = 64
$T4 = 72
_Px$ = 80
$T5 = 88
tv166 = 96
this$ = 128
_Px$ = 136
??$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z PROC ; std::shared_ptr<mu2::Event>::shared_ptr<mu2::Event><mu2::Event,0>, COMDAT

; 1584 :     explicit shared_ptr(_Ux* _Px) { // construct shared_ptr object that owns _Px

$LN37:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 70	 sub	 rsp, 112		; 00000070H

; 1452 :     element_type* _Ptr{nullptr};

  0000f	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00017	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0

; 1453 :     _Ref_count_base* _Rep{nullptr};

  0001e	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00026	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0

; 1526 :     explicit _Temporary_owner(_Ux* const _Ptr_) noexcept : _Ptr(_Ptr_) {}

  0002e	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR _Px$[rsp]
  00036	48 89 44 24 28	 mov	 QWORD PTR _Owner$2[rsp], rax

; 1585 :         if constexpr (is_array_v<_Ty>) {
; 1586 :             _Setpd(_Px, default_delete<_Ux[]>{});
; 1587 :         } else {
; 1588 :             _Temporary_owner<_Ux> _Owner(_Px);
; 1589 :             _Set_ptr_rep_and_enable_shared(_Owner._Ptr, new _Ref_count<_Ux>(_Owner._Ptr));

  0003b	b9 18 00 00 00	 mov	 ecx, 24
  00040	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00045	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
  0004a	48 83 7c 24 20
	00		 cmp	 QWORD PTR $T1[rsp], 0
  00050	74 63		 je	 SHORT $LN3@Event
  00052	48 8b 44 24 28	 mov	 rax, QWORD PTR _Owner$2[rsp]
  00057	48 89 44 24 40	 mov	 QWORD PTR _Px$[rsp], rax

; 1183 :     explicit _Ref_count(_Ty* _Px) : _Ref_count_base(), _Ptr(_Px) {}

  0005c	48 8b 7c 24 20	 mov	 rdi, QWORD PTR $T1[rsp]
  00061	33 c0		 xor	 eax, eax
  00063	b9 10 00 00 00	 mov	 ecx, 16
  00068	f3 aa		 rep stosb

; 1119 :     _Atomic_counter_t _Uses  = 1;

  0006a	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  0006f	c7 40 08 01 00
	00 00		 mov	 DWORD PTR [rax+8], 1

; 1120 :     _Atomic_counter_t _Weaks = 1;

  00076	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  0007b	c7 40 0c 01 00
	00 00		 mov	 DWORD PTR [rax+12], 1

; 1183 :     explicit _Ref_count(_Ty* _Px) : _Ref_count_base(), _Ptr(_Px) {}

  00082	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00087	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$_Ref_count@UEvent@mu2@@@std@@6B@
  0008e	48 89 08	 mov	 QWORD PTR [rax], rcx
  00091	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00096	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Px$[rsp]
  0009b	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx
  0009f	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  000a4	48 89 44 24 48	 mov	 QWORD PTR $T4[rsp], rax

; 1585 :         if constexpr (is_array_v<_Ty>) {
; 1586 :             _Setpd(_Px, default_delete<_Ux[]>{});
; 1587 :         } else {
; 1588 :             _Temporary_owner<_Ux> _Owner(_Px);
; 1589 :             _Set_ptr_rep_and_enable_shared(_Owner._Ptr, new _Ref_count<_Ux>(_Owner._Ptr));

  000a9	48 8b 44 24 48	 mov	 rax, QWORD PTR $T4[rsp]
  000ae	48 89 44 24 38	 mov	 QWORD PTR tv89[rsp], rax
  000b3	eb 09		 jmp	 SHORT $LN4@Event
$LN3@Event:
  000b5	48 c7 44 24 38
	00 00 00 00	 mov	 QWORD PTR tv89[rsp], 0
$LN4@Event:
  000be	48 8b 44 24 38	 mov	 rax, QWORD PTR tv89[rsp]
  000c3	48 89 44 24 58	 mov	 QWORD PTR $T5[rsp], rax
  000c8	48 8b 44 24 28	 mov	 rax, QWORD PTR _Owner$2[rsp]
  000cd	48 89 44 24 50	 mov	 QWORD PTR _Px$[rsp], rax

; 1856 :         this->_Ptr = _Px;

  000d2	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000da	48 8b 4c 24 50	 mov	 rcx, QWORD PTR _Px$[rsp]
  000df	48 89 08	 mov	 QWORD PTR [rax], rcx

; 1857 :         this->_Rep = _Rx;

  000e2	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000ea	48 8b 4c 24 58	 mov	 rcx, QWORD PTR $T5[rsp]
  000ef	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 1590 :             _Owner._Ptr = nullptr;

  000f3	48 c7 44 24 28
	00 00 00 00	 mov	 QWORD PTR _Owner$2[rsp], 0

; 1530 :         delete _Ptr;

  000fc	48 8b 44 24 28	 mov	 rax, QWORD PTR _Owner$2[rsp]
  00101	48 89 44 24 30	 mov	 QWORD PTR $T3[rsp], rax
  00106	48 83 7c 24 30
	00		 cmp	 QWORD PTR $T3[rsp], 0
  0010c	74 1b		 je	 SHORT $LN32@Event
  0010e	48 8b 44 24 30	 mov	 rax, QWORD PTR $T3[rsp]
  00113	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00116	ba 01 00 00 00	 mov	 edx, 1
  0011b	48 8b 4c 24 30	 mov	 rcx, QWORD PTR $T3[rsp]
  00120	ff 10		 call	 QWORD PTR [rax]
  00122	48 89 44 24 60	 mov	 QWORD PTR tv166[rsp], rax
  00127	eb 09		 jmp	 SHORT $LN33@Event
$LN32@Event:
  00129	48 c7 44 24 60
	00 00 00 00	 mov	 QWORD PTR tv166[rsp], 0
$LN33@Event:

; 1591 :         }
; 1592 :     }

  00132	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0013a	48 83 c4 70	 add	 rsp, 112		; 00000070H
  0013e	5f		 pop	 rdi
  0013f	c3		 ret	 0
??$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z ENDP ; std::shared_ptr<mu2::Event>::shared_ptr<mu2::Event><mu2::Event,0>
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
_Owner$2 = 40
$T3 = 48
tv89 = 56
_Px$ = 64
$T4 = 72
_Px$ = 80
$T5 = 88
tv166 = 96
this$ = 128
_Px$ = 136
?dtor$0@?0???$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z@4HA PROC ; `std::shared_ptr<mu2::Event>::shared_ptr<mu2::Event><mu2::Event,0>'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8d 4d 28	 lea	 rcx, QWORD PTR _Owner$2[rbp]
  0000d	e8 00 00 00 00	 call	 ??1?$_Temporary_owner@UEvent@mu2@@@std@@QEAA@XZ ; std::_Temporary_owner<mu2::Event>::~_Temporary_owner<mu2::Event>
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$0@?0???$?0UEvent@mu2@@$0A@@?$shared_ptr@UEvent@mu2@@@std@@QEAA@PEAUEvent@mu2@@@Z@4HA ENDP ; `std::shared_ptr<mu2::Event>::shared_ptr<mu2::Event><mu2::Event,0>'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ??$swapEndian@W4MoneyType@ChangedMoney@mu2@@@PacketStream@mu2@@AEAA?AW4MoneyType@ChangedMoney@1@AEBW4231@@Z
_TEXT	SEGMENT
dest$ = 0
source$ = 1
k$1 = 8
this$ = 32
u$ = 40
??$swapEndian@W4MoneyType@ChangedMoney@mu2@@@PacketStream@mu2@@AEAA?AW4MoneyType@ChangedMoney@1@AEBW4231@@Z PROC ; mu2::PacketStream::swapEndian<enum mu2::ChangedMoney::MoneyType>, COMDAT

; 1103 : {

$LN6:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 18	 sub	 rsp, 24

; 1104 : 	static_assert(std::is_fundamental<T>::value || std::is_enum<T>::value, "swapEndian> Type must be fundamental");
; 1105 : 
; 1106 : 	union
; 1107 : 	{
; 1108 : 		T u;
; 1109 : 		unsigned char u8[sizeof(T)];
; 1110 : 	} source, dest;
; 1111 : 
; 1112 : 	source.u = u;

  0000e	48 8b 44 24 28	 mov	 rax, QWORD PTR u$[rsp]
  00013	0f b6 00	 movzx	 eax, BYTE PTR [rax]
  00016	88 44 24 01	 mov	 BYTE PTR source$[rsp], al

; 1113 : 	dest.u = u;

  0001a	48 8b 44 24 28	 mov	 rax, QWORD PTR u$[rsp]
  0001f	0f b6 00	 movzx	 eax, BYTE PTR [rax]
  00022	88 04 24	 mov	 BYTE PTR dest$[rsp], al

; 1114 : 
; 1115 : 	for (size_t k = 0; k < sizeof(T); k++)

  00025	48 c7 44 24 08
	00 00 00 00	 mov	 QWORD PTR k$1[rsp], 0
  0002e	eb 0d		 jmp	 SHORT $LN4@swapEndian
$LN2@swapEndian:
  00030	48 8b 44 24 08	 mov	 rax, QWORD PTR k$1[rsp]
  00035	48 ff c0	 inc	 rax
  00038	48 89 44 24 08	 mov	 QWORD PTR k$1[rsp], rax
$LN4@swapEndian:
  0003d	48 83 7c 24 08
	01		 cmp	 QWORD PTR k$1[rsp], 1
  00043	73 18		 jae	 SHORT $LN3@swapEndian

; 1116 : 		dest.u8[k] = source.u8[sizeof(T) - k - 1];

  00045	b8 01 00 00 00	 mov	 eax, 1
  0004a	48 2b 44 24 08	 sub	 rax, QWORD PTR k$1[rsp]
  0004f	48 8b 4c 24 08	 mov	 rcx, QWORD PTR k$1[rsp]
  00054	0f b6 04 04	 movzx	 eax, BYTE PTR source$[rsp+rax-1]
  00058	88 04 0c	 mov	 BYTE PTR dest$[rsp+rcx], al
  0005b	eb d3		 jmp	 SHORT $LN2@swapEndian
$LN3@swapEndian:

; 1117 : 
; 1118 : 	return dest.u;

  0005d	0f b6 04 24	 movzx	 eax, BYTE PTR dest$[rsp]

; 1119 : }

  00061	48 83 c4 18	 add	 rsp, 24
  00065	c3		 ret	 0
??$swapEndian@W4MoneyType@ChangedMoney@mu2@@@PacketStream@mu2@@AEAA?AW4MoneyType@ChangedMoney@1@AEBW4231@@Z ENDP ; mu2::PacketStream::swapEndian<enum mu2::ChangedMoney::MoneyType>
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ??$swapEndian@W4CalcType@ChangedMoney@mu2@@@PacketStream@mu2@@AEAA?AW4CalcType@ChangedMoney@1@AEBW4231@@Z
_TEXT	SEGMENT
dest$ = 0
source$ = 1
k$1 = 8
this$ = 32
u$ = 40
??$swapEndian@W4CalcType@ChangedMoney@mu2@@@PacketStream@mu2@@AEAA?AW4CalcType@ChangedMoney@1@AEBW4231@@Z PROC ; mu2::PacketStream::swapEndian<enum mu2::ChangedMoney::CalcType>, COMDAT

; 1103 : {

$LN6:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 18	 sub	 rsp, 24

; 1104 : 	static_assert(std::is_fundamental<T>::value || std::is_enum<T>::value, "swapEndian> Type must be fundamental");
; 1105 : 
; 1106 : 	union
; 1107 : 	{
; 1108 : 		T u;
; 1109 : 		unsigned char u8[sizeof(T)];
; 1110 : 	} source, dest;
; 1111 : 
; 1112 : 	source.u = u;

  0000e	48 8b 44 24 28	 mov	 rax, QWORD PTR u$[rsp]
  00013	0f b6 00	 movzx	 eax, BYTE PTR [rax]
  00016	88 44 24 01	 mov	 BYTE PTR source$[rsp], al

; 1113 : 	dest.u = u;

  0001a	48 8b 44 24 28	 mov	 rax, QWORD PTR u$[rsp]
  0001f	0f b6 00	 movzx	 eax, BYTE PTR [rax]
  00022	88 04 24	 mov	 BYTE PTR dest$[rsp], al

; 1114 : 
; 1115 : 	for (size_t k = 0; k < sizeof(T); k++)

  00025	48 c7 44 24 08
	00 00 00 00	 mov	 QWORD PTR k$1[rsp], 0
  0002e	eb 0d		 jmp	 SHORT $LN4@swapEndian
$LN2@swapEndian:
  00030	48 8b 44 24 08	 mov	 rax, QWORD PTR k$1[rsp]
  00035	48 ff c0	 inc	 rax
  00038	48 89 44 24 08	 mov	 QWORD PTR k$1[rsp], rax
$LN4@swapEndian:
  0003d	48 83 7c 24 08
	01		 cmp	 QWORD PTR k$1[rsp], 1
  00043	73 18		 jae	 SHORT $LN3@swapEndian

; 1116 : 		dest.u8[k] = source.u8[sizeof(T) - k - 1];

  00045	b8 01 00 00 00	 mov	 eax, 1
  0004a	48 2b 44 24 08	 sub	 rax, QWORD PTR k$1[rsp]
  0004f	48 8b 4c 24 08	 mov	 rcx, QWORD PTR k$1[rsp]
  00054	0f b6 04 04	 movzx	 eax, BYTE PTR source$[rsp+rax-1]
  00058	88 04 0c	 mov	 BYTE PTR dest$[rsp+rcx], al
  0005b	eb d3		 jmp	 SHORT $LN2@swapEndian
$LN3@swapEndian:

; 1117 : 
; 1118 : 	return dest.u;

  0005d	0f b6 04 24	 movzx	 eax, BYTE PTR dest$[rsp]

; 1119 : }

  00061	48 83 c4 18	 add	 rsp, 24
  00065	c3		 ret	 0
??$swapEndian@W4CalcType@ChangedMoney@mu2@@@PacketStream@mu2@@AEAA?AW4CalcType@ChangedMoney@1@AEBW4231@@Z ENDP ; mu2::PacketStream::swapEndian<enum mu2::ChangedMoney::CalcType>
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\WShopTask.h
;	COMDAT ?fillItemResult@WShopTask@mu2@@MEAAXXZ
_TEXT	SEGMENT
this$ = 8
?fillItemResult@WShopTask@mu2@@MEAAXXZ PROC		; mu2::WShopTask::fillItemResult, COMDAT

; 78   : 	virtual void fillItemResult() {}	//	아이템 구매 성공을 

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?fillItemResult@WShopTask@mu2@@MEAAXXZ ENDP		; mu2::WShopTask::fillItemResult
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ??4?$shared_ptr@UEvent@mu2@@@std@@QEAAAEAV01@AEBV01@@Z
_TEXT	SEGMENT
this$ = 32
$T1 = 40
$T2 = 48
this$ = 80
_Right$ = 88
??4?$shared_ptr@UEvent@mu2@@@std@@QEAAAEAV01@AEBV01@@Z PROC ; std::shared_ptr<mu2::Event>::operator=, COMDAT

; 1693 :     shared_ptr& operator=(const shared_ptr& _Right) noexcept {

$LN103:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 1452 :     element_type* _Ptr{nullptr};

  0000e	48 c7 44 24 30
	00 00 00 00	 mov	 QWORD PTR $T2[rsp], 0

; 1453 :     _Ref_count_base* _Rep{nullptr};

  00017	48 c7 44 24 38
	00 00 00 00	 mov	 QWORD PTR $T2[rsp+8], 0

; 1378 :         if (_Rep) {

  00020	48 8b 44 24 58	 mov	 rax, QWORD PTR _Right$[rsp]
  00025	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  0002a	74 1a		 je	 SHORT $LN20@operator

; 1379 :             _Rep->_Incref();

  0002c	48 8b 44 24 58	 mov	 rax, QWORD PTR _Right$[rsp]
  00031	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00035	48 89 44 24 20	 mov	 QWORD PTR this$[rsp], rax

; 1151 :         _MT_INCR(_Uses);

  0003a	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0003f	48 83 c0 08	 add	 rax, 8
  00043	f0 ff 00	 lock inc DWORD PTR [rax]
$LN20@operator:

; 1339 :         _Ptr = _Other._Ptr;

  00046	48 8b 44 24 58	 mov	 rax, QWORD PTR _Right$[rsp]
  0004b	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0004e	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax

; 1340 :         _Rep = _Other._Rep;

  00053	48 8b 44 24 58	 mov	 rax, QWORD PTR _Right$[rsp]
  00058	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0005c	48 89 44 24 38	 mov	 QWORD PTR $T2[rsp+8], rax

; 1636 :     }

  00061	48 8d 44 24 30	 lea	 rax, QWORD PTR $T2[rsp]
  00066	48 89 44 24 28	 mov	 QWORD PTR $T1[rsp], rax

; 1694 :         shared_ptr(_Right).swap(*this);

  0006b	48 8b 44 24 28	 mov	 rax, QWORD PTR $T1[rsp]

; 1733 :         this->_Swap(_Other);

  00070	48 8b 54 24 50	 mov	 rdx, QWORD PTR this$[rsp]
  00075	48 8b c8	 mov	 rcx, rax
  00078	e8 00 00 00 00	 call	 ?_Swap@?$_Ptr_base@UEvent@mu2@@@std@@IEAAXAEAV12@@Z ; std::_Ptr_base<mu2::Event>::_Swap
  0007d	90		 npad	 1

; 1384 :         if (_Rep) {

  0007e	48 83 7c 24 38
	00		 cmp	 QWORD PTR $T2[rsp+8], 0
  00084	74 0b		 je	 SHORT $LN86@operator

; 1385 :             _Rep->_Decref();

  00086	48 8b 4c 24 38	 mov	 rcx, QWORD PTR $T2[rsp+8]
  0008b	e8 00 00 00 00	 call	 ?_Decref@_Ref_count_base@std@@QEAAXXZ ; std::_Ref_count_base::_Decref
  00090	90		 npad	 1
$LN86@operator:

; 1695 :         return *this;

  00091	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]

; 1696 :     }

  00096	48 83 c4 48	 add	 rsp, 72			; 00000048H
  0009a	c3		 ret	 0
??4?$shared_ptr@UEvent@mu2@@@std@@QEAAAEAV01@AEBV01@@Z ENDP ; std::shared_ptr<mu2::Event>::operator=
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ?_Swap@?$_Ptr_base@UEvent@mu2@@@std@@IEAAXAEAV12@@Z
_TEXT	SEGMENT
_Left$ = 0
_Right$ = 8
_Left$ = 16
_Right$ = 24
$T1 = 32
$T2 = 40
_Tmp$3 = 48
$T4 = 56
$T5 = 64
$T6 = 72
_Tmp$7 = 80
$T8 = 88
this$ = 112
_Right$ = 120
?_Swap@?$_Ptr_base@UEvent@mu2@@@std@@IEAAXAEAV12@@Z PROC ; std::_Ptr_base<mu2::Event>::_Swap, COMDAT

; 1389 :     void _Swap(_Ptr_base& _Right) noexcept { // swap pointers

$LN44:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 68	 sub	 rsp, 104		; 00000068H

; 1390 :         _STD swap(_Ptr, _Right._Ptr);

  0000e	48 8b 44 24 78	 mov	 rax, QWORD PTR _Right$[rsp]
  00013	48 89 44 24 08	 mov	 QWORD PTR _Right$[rsp], rax
  00018	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 89 04 24	 mov	 QWORD PTR _Left$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00021	48 8b 04 24	 mov	 rax, QWORD PTR _Left$[rsp]
  00025	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 139  :     _Ty _Tmp = _STD move(_Left);

  0002a	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  0002f	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00032	48 89 44 24 30	 mov	 QWORD PTR _Tmp$3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00037	48 8b 44 24 08	 mov	 rax, QWORD PTR _Right$[rsp]
  0003c	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 140  :     _Left    = _STD move(_Right);

  00041	48 8b 04 24	 mov	 rax, QWORD PTR _Left$[rsp]
  00045	48 8b 4c 24 28	 mov	 rcx, QWORD PTR $T2[rsp]
  0004a	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0004d	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00050	48 8d 44 24 30	 lea	 rax, QWORD PTR _Tmp$3[rsp]
  00055	48 89 44 24 38	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 141  :     _Right   = _STD move(_Tmp);

  0005a	48 8b 44 24 08	 mov	 rax, QWORD PTR _Right$[rsp]
  0005f	48 8b 4c 24 38	 mov	 rcx, QWORD PTR $T4[rsp]
  00064	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00067	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1391 :         _STD swap(_Rep, _Right._Rep);

  0006a	48 8b 44 24 78	 mov	 rax, QWORD PTR _Right$[rsp]
  0006f	48 83 c0 08	 add	 rax, 8
  00073	48 89 44 24 18	 mov	 QWORD PTR _Right$[rsp], rax
  00078	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0007d	48 83 c0 08	 add	 rax, 8
  00081	48 89 44 24 10	 mov	 QWORD PTR _Left$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00086	48 8b 44 24 10	 mov	 rax, QWORD PTR _Left$[rsp]
  0008b	48 89 44 24 40	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 139  :     _Ty _Tmp = _STD move(_Left);

  00090	48 8b 44 24 40	 mov	 rax, QWORD PTR $T5[rsp]
  00095	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00098	48 89 44 24 50	 mov	 QWORD PTR _Tmp$7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  0009d	48 8b 44 24 18	 mov	 rax, QWORD PTR _Right$[rsp]
  000a2	48 89 44 24 48	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 140  :     _Left    = _STD move(_Right);

  000a7	48 8b 44 24 10	 mov	 rax, QWORD PTR _Left$[rsp]
  000ac	48 8b 4c 24 48	 mov	 rcx, QWORD PTR $T6[rsp]
  000b1	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  000b4	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  000b7	48 8d 44 24 50	 lea	 rax, QWORD PTR _Tmp$7[rsp]
  000bc	48 89 44 24 58	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 141  :     _Right   = _STD move(_Tmp);

  000c1	48 8b 44 24 18	 mov	 rax, QWORD PTR _Right$[rsp]
  000c6	48 8b 4c 24 58	 mov	 rcx, QWORD PTR $T8[rsp]
  000cb	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  000ce	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1392 :     }

  000d1	48 83 c4 68	 add	 rsp, 104		; 00000068H
  000d5	c3		 ret	 0
?_Swap@?$_Ptr_base@UEvent@mu2@@@std@@IEAAXAEAV12@@Z ENDP ; std::_Ptr_base<mu2::Event>::_Swap
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
;	COMDAT ??1?$SmartPtrEx@UEvent@mu2@@@mu2@@QEAA@XZ
_TEXT	SEGMENT
this$ = 32
this$ = 64
??1?$SmartPtrEx@UEvent@mu2@@@mu2@@QEAA@XZ PROC		; mu2::SmartPtrEx<mu2::Event>::~SmartPtrEx<mu2::Event>, COMDAT

; 165  : 	{

$LN27:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 166  : 	}

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 89 44 24 20	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1384 :         if (_Rep) {

  00013	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00018	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  0001d	74 0f		 je	 SHORT $LN10@SmartPtrEx

; 1385 :             _Rep->_Decref();

  0001f	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00024	48 8b 48 08	 mov	 rcx, QWORD PTR [rax+8]
  00028	e8 00 00 00 00	 call	 ?_Decref@_Ref_count_base@std@@QEAAXXZ ; std::_Ref_count_base::_Decref
  0002d	90		 npad	 1
$LN10@SmartPtrEx:
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h

; 166  : 	}

  0002e	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00032	c3		 ret	 0
??1?$SmartPtrEx@UEvent@mu2@@@mu2@@QEAA@XZ ENDP		; mu2::SmartPtrEx<mu2::Event>::~SmartPtrEx<mu2::Event>
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??4WShopDbUser@mu2@@QEAAAEAU01@AEBU01@@Z
_TEXT	SEGMENT
this$ = 48
__that$ = 56
??4WShopDbUser@mu2@@QEAAAEAU01@AEBU01@@Z PROC		; mu2::WShopDbUser::operator=, COMDAT
$LN649:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00013	48 8b 4c 24 38	 mov	 rcx, QWORD PTR __that$[rsp]
  00018	8b 49 08	 mov	 ecx, DWORD PTR [rcx+8]
  0001b	89 48 08	 mov	 DWORD PTR [rax+8], ecx
  0001e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 8b 4c 24 38	 mov	 rcx, QWORD PTR __that$[rsp]
  00028	8b 49 0c	 mov	 ecx, DWORD PTR [rcx+12]
  0002b	89 48 0c	 mov	 DWORD PTR [rax+12], ecx
  0002e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00033	48 8b 4c 24 38	 mov	 rcx, QWORD PTR __that$[rsp]
  00038	8b 49 10	 mov	 ecx, DWORD PTR [rcx+16]
  0003b	89 48 10	 mov	 DWORD PTR [rax+16], ecx
  0003e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00043	48 8b 4c 24 38	 mov	 rcx, QWORD PTR __that$[rsp]
  00048	8b 49 14	 mov	 ecx, DWORD PTR [rcx+20]
  0004b	89 48 14	 mov	 DWORD PTR [rax+20], ecx
  0004e	48 8b 44 24 38	 mov	 rax, QWORD PTR __that$[rsp]
  00053	48 83 c0 18	 add	 rax, 24
  00057	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0005c	48 83 c1 18	 add	 rcx, 24
  00060	48 8b d0	 mov	 rdx, rax
  00063	e8 00 00 00 00	 call	 ??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV01@AEBV01@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::operator=
  00068	48 8b 44 24 38	 mov	 rax, QWORD PTR __that$[rsp]
  0006d	48 83 c0 38	 add	 rax, 56			; 00000038H
  00071	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00076	48 83 c1 38	 add	 rcx, 56			; 00000038H
  0007a	48 8b d0	 mov	 rdx, rax
  0007d	e8 00 00 00 00	 call	 ??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV01@AEBV01@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::operator=
  00082	48 8b 44 24 38	 mov	 rax, QWORD PTR __that$[rsp]
  00087	48 83 c0 58	 add	 rax, 88			; 00000058H
  0008b	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00090	48 83 c1 58	 add	 rcx, 88			; 00000058H
  00094	48 8b d0	 mov	 rdx, rax
  00097	e8 00 00 00 00	 call	 ??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV01@AEBV01@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::operator=
  0009c	48 8b 44 24 38	 mov	 rax, QWORD PTR __that$[rsp]
  000a1	48 83 c0 78	 add	 rax, 120		; 00000078H
  000a5	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  000aa	48 83 c1 78	 add	 rcx, 120		; 00000078H
  000ae	48 8b d0	 mov	 rdx, rax
  000b1	e8 00 00 00 00	 call	 ??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV01@AEBV01@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::operator=
  000b6	48 8b 44 24 38	 mov	 rax, QWORD PTR __that$[rsp]
  000bb	48 05 98 00 00
	00		 add	 rax, 152		; 00000098H
  000c1	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  000c6	48 81 c1 98 00
	00 00		 add	 rcx, 152		; 00000098H
  000cd	48 8b d0	 mov	 rdx, rax
  000d0	e8 00 00 00 00	 call	 ??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV01@AEBV01@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::operator=
  000d5	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  000da	48 83 c4 28	 add	 rsp, 40			; 00000028H
  000de	c3		 ret	 0
??4WShopDbUser@mu2@@QEAAAEAU01@AEBU01@@Z ENDP		; mu2::WShopDbUser::operator=
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ??$em@W4MoneyType@ChangedMoney@mu2@@@PacketStream@mu2@@QEAA_NAEAW4MoneyType@ChangedMoney@1@@Z
_TEXT	SEGMENT
swapped$1 = 32
org$2 = 33
this$ = 64
s$ = 72
??$em@W4MoneyType@ChangedMoney@mu2@@@PacketStream@mu2@@QEAA_NAEAW4MoneyType@ChangedMoney@1@@Z PROC ; mu2::PacketStream::em<enum mu2::ChangedMoney::MoneyType>, COMDAT

; 243  : {

$LN47:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 244  : 	static_assert( std::is_fundamental<T>::value || std::is_enum<T>::value, "em> Type must be fundamental");
; 245  : 
; 246  : 	if (isValid() == false)

  0000e	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00013	e8 00 00 00 00	 call	 ?isValid@PacketStream@mu2@@QEBA_NXZ ; mu2::PacketStream::isValid
  00018	0f b6 c0	 movzx	 eax, al
  0001b	85 c0		 test	 eax, eax
  0001d	75 07		 jne	 SHORT $LN2@em

; 247  : 		return false;

  0001f	32 c0		 xor	 al, al
  00021	e9 cc 00 00 00	 jmp	 $LN1@em
$LN2@em:

; 248  : 
; 249  : 	if (false == isBigEndian)									

  00026	0f b6 05 00 00
	00 00		 movzx	 eax, BYTE PTR ?isBigEndian@PacketStream@mu2@@2_NA ; mu2::PacketStream::isBigEndian
  0002d	85 c0		 test	 eax, eax
  0002f	75 3d		 jne	 SHORT $LN3@em

; 250  : 	{															
; 251  : 		if (m_ePackType == ePack)										

  00031	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00036	0f b6 40 28	 movzx	 eax, BYTE PTR [rax+40]
  0003a	85 c0		 test	 eax, eax
  0003c	75 18		 jne	 SHORT $LN5@em

; 252  : 		{														
; 253  : 			Write((Byte*)&s, sizeof(T));

  0003e	41 b8 01 00 00
	00		 mov	 r8d, 1
  00044	48 8b 54 24 48	 mov	 rdx, QWORD PTR s$[rsp]
  00049	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0004e	e8 00 00 00 00	 call	 ?Write@PacketStream@mu2@@QEAAIPEBEI@Z ; mu2::PacketStream::Write
  00053	90		 npad	 1

; 254  : 		}														

  00054	eb 16		 jmp	 SHORT $LN6@em
$LN5@em:

; 255  : 		else													
; 256  : 		{														
; 257  : 			Read((Byte*)&s, sizeof(T));

  00056	41 b8 01 00 00
	00		 mov	 r8d, 1
  0005c	48 8b 54 24 48	 mov	 rdx, QWORD PTR s$[rsp]
  00061	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00066	e8 00 00 00 00	 call	 ?Read@PacketStream@mu2@@QEAAIPEAEI@Z ; mu2::PacketStream::Read
  0006b	90		 npad	 1
$LN6@em:

; 258  : 		}														
; 259  : 	}															

  0006c	eb 7a		 jmp	 SHORT $LN4@em
$LN3@em:

; 260  : 	else														
; 261  : 	{														
; 262  : 		if (m_ePackType == ePack)

  0006e	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00073	0f b6 40 28	 movzx	 eax, BYTE PTR [rax+40]
  00077	85 c0		 test	 eax, eax
  00079	75 2b		 jne	 SHORT $LN7@em

; 263  : 		{														
; 264  : 			T swapped = swapEndian(s);

  0007b	48 8b 54 24 48	 mov	 rdx, QWORD PTR s$[rsp]
  00080	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00085	e8 00 00 00 00	 call	 ??$swapEndian@W4MoneyType@ChangedMoney@mu2@@@PacketStream@mu2@@AEAA?AW4MoneyType@ChangedMoney@1@AEBW4231@@Z ; mu2::PacketStream::swapEndian<enum mu2::ChangedMoney::MoneyType>
  0008a	88 44 24 20	 mov	 BYTE PTR swapped$1[rsp], al

; 265  : 			Write((Byte*)&swapped, sizeof(T));

  0008e	41 b8 01 00 00
	00		 mov	 r8d, 1
  00094	48 8d 54 24 20	 lea	 rdx, QWORD PTR swapped$1[rsp]
  00099	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0009e	e8 00 00 00 00	 call	 ?Write@PacketStream@mu2@@QEAAIPEBEI@Z ; mu2::PacketStream::Write
  000a3	90		 npad	 1

; 266  : 		}														

  000a4	eb 42		 jmp	 SHORT $LN8@em
$LN7@em:

; 267  : 		else													
; 268  : 		{														
; 269  : 			T org;
; 270  : 			if (0 == Read((Byte*)&org, sizeof(T)))

  000a6	41 b8 01 00 00
	00		 mov	 r8d, 1
  000ac	48 8d 54 24 21	 lea	 rdx, QWORD PTR org$2[rsp]
  000b1	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  000b6	e8 00 00 00 00	 call	 ?Read@PacketStream@mu2@@QEAAIPEAEI@Z ; mu2::PacketStream::Read
  000bb	85 c0		 test	 eax, eax
  000bd	75 13		 jne	 SHORT $LN9@em

; 271  : 			{													
; 272  : 				SetError(ErrorNet::ReadSizeZero);

  000bf	ba 66 00 00 00	 mov	 edx, 102		; 00000066H
  000c4	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  000c9	e8 00 00 00 00	 call	 ?SetError@PacketStream@mu2@@AEAAXW4Error@ErrorNet@2@@Z ; mu2::PacketStream::SetError

; 273  : 				return false;									

  000ce	32 c0		 xor	 al, al
  000d0	eb 20		 jmp	 SHORT $LN1@em
$LN9@em:

; 274  : 			}													
; 275  : 			s = swapEndian(org);							

  000d2	48 8d 54 24 21	 lea	 rdx, QWORD PTR org$2[rsp]
  000d7	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  000dc	e8 00 00 00 00	 call	 ??$swapEndian@W4MoneyType@ChangedMoney@mu2@@@PacketStream@mu2@@AEAA?AW4MoneyType@ChangedMoney@1@AEBW4231@@Z ; mu2::PacketStream::swapEndian<enum mu2::ChangedMoney::MoneyType>
  000e1	48 8b 4c 24 48	 mov	 rcx, QWORD PTR s$[rsp]
  000e6	88 01		 mov	 BYTE PTR [rcx], al
$LN8@em:
$LN4@em:

; 276  : 		}														
; 277  : 	}
; 278  : 	return isValid();

  000e8	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  000ed	e8 00 00 00 00	 call	 ?isValid@PacketStream@mu2@@QEBA_NXZ ; mu2::PacketStream::isValid
$LN1@em:

; 279  : }

  000f2	48 83 c4 38	 add	 rsp, 56			; 00000038H
  000f6	c3		 ret	 0
??$em@W4MoneyType@ChangedMoney@mu2@@@PacketStream@mu2@@QEAA_NAEAW4MoneyType@ChangedMoney@1@@Z ENDP ; mu2::PacketStream::em<enum mu2::ChangedMoney::MoneyType>
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ??$em@W4CalcType@ChangedMoney@mu2@@@PacketStream@mu2@@QEAA_NAEAW4CalcType@ChangedMoney@1@@Z
_TEXT	SEGMENT
swapped$1 = 32
org$2 = 33
this$ = 64
s$ = 72
??$em@W4CalcType@ChangedMoney@mu2@@@PacketStream@mu2@@QEAA_NAEAW4CalcType@ChangedMoney@1@@Z PROC ; mu2::PacketStream::em<enum mu2::ChangedMoney::CalcType>, COMDAT

; 243  : {

$LN47:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 244  : 	static_assert( std::is_fundamental<T>::value || std::is_enum<T>::value, "em> Type must be fundamental");
; 245  : 
; 246  : 	if (isValid() == false)

  0000e	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00013	e8 00 00 00 00	 call	 ?isValid@PacketStream@mu2@@QEBA_NXZ ; mu2::PacketStream::isValid
  00018	0f b6 c0	 movzx	 eax, al
  0001b	85 c0		 test	 eax, eax
  0001d	75 07		 jne	 SHORT $LN2@em

; 247  : 		return false;

  0001f	32 c0		 xor	 al, al
  00021	e9 cc 00 00 00	 jmp	 $LN1@em
$LN2@em:

; 248  : 
; 249  : 	if (false == isBigEndian)									

  00026	0f b6 05 00 00
	00 00		 movzx	 eax, BYTE PTR ?isBigEndian@PacketStream@mu2@@2_NA ; mu2::PacketStream::isBigEndian
  0002d	85 c0		 test	 eax, eax
  0002f	75 3d		 jne	 SHORT $LN3@em

; 250  : 	{															
; 251  : 		if (m_ePackType == ePack)										

  00031	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00036	0f b6 40 28	 movzx	 eax, BYTE PTR [rax+40]
  0003a	85 c0		 test	 eax, eax
  0003c	75 18		 jne	 SHORT $LN5@em

; 252  : 		{														
; 253  : 			Write((Byte*)&s, sizeof(T));

  0003e	41 b8 01 00 00
	00		 mov	 r8d, 1
  00044	48 8b 54 24 48	 mov	 rdx, QWORD PTR s$[rsp]
  00049	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0004e	e8 00 00 00 00	 call	 ?Write@PacketStream@mu2@@QEAAIPEBEI@Z ; mu2::PacketStream::Write
  00053	90		 npad	 1

; 254  : 		}														

  00054	eb 16		 jmp	 SHORT $LN6@em
$LN5@em:

; 255  : 		else													
; 256  : 		{														
; 257  : 			Read((Byte*)&s, sizeof(T));

  00056	41 b8 01 00 00
	00		 mov	 r8d, 1
  0005c	48 8b 54 24 48	 mov	 rdx, QWORD PTR s$[rsp]
  00061	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00066	e8 00 00 00 00	 call	 ?Read@PacketStream@mu2@@QEAAIPEAEI@Z ; mu2::PacketStream::Read
  0006b	90		 npad	 1
$LN6@em:

; 258  : 		}														
; 259  : 	}															

  0006c	eb 7a		 jmp	 SHORT $LN4@em
$LN3@em:

; 260  : 	else														
; 261  : 	{														
; 262  : 		if (m_ePackType == ePack)

  0006e	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00073	0f b6 40 28	 movzx	 eax, BYTE PTR [rax+40]
  00077	85 c0		 test	 eax, eax
  00079	75 2b		 jne	 SHORT $LN7@em

; 263  : 		{														
; 264  : 			T swapped = swapEndian(s);

  0007b	48 8b 54 24 48	 mov	 rdx, QWORD PTR s$[rsp]
  00080	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00085	e8 00 00 00 00	 call	 ??$swapEndian@W4CalcType@ChangedMoney@mu2@@@PacketStream@mu2@@AEAA?AW4CalcType@ChangedMoney@1@AEBW4231@@Z ; mu2::PacketStream::swapEndian<enum mu2::ChangedMoney::CalcType>
  0008a	88 44 24 20	 mov	 BYTE PTR swapped$1[rsp], al

; 265  : 			Write((Byte*)&swapped, sizeof(T));

  0008e	41 b8 01 00 00
	00		 mov	 r8d, 1
  00094	48 8d 54 24 20	 lea	 rdx, QWORD PTR swapped$1[rsp]
  00099	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0009e	e8 00 00 00 00	 call	 ?Write@PacketStream@mu2@@QEAAIPEBEI@Z ; mu2::PacketStream::Write
  000a3	90		 npad	 1

; 266  : 		}														

  000a4	eb 42		 jmp	 SHORT $LN8@em
$LN7@em:

; 267  : 		else													
; 268  : 		{														
; 269  : 			T org;
; 270  : 			if (0 == Read((Byte*)&org, sizeof(T)))

  000a6	41 b8 01 00 00
	00		 mov	 r8d, 1
  000ac	48 8d 54 24 21	 lea	 rdx, QWORD PTR org$2[rsp]
  000b1	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  000b6	e8 00 00 00 00	 call	 ?Read@PacketStream@mu2@@QEAAIPEAEI@Z ; mu2::PacketStream::Read
  000bb	85 c0		 test	 eax, eax
  000bd	75 13		 jne	 SHORT $LN9@em

; 271  : 			{													
; 272  : 				SetError(ErrorNet::ReadSizeZero);

  000bf	ba 66 00 00 00	 mov	 edx, 102		; 00000066H
  000c4	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  000c9	e8 00 00 00 00	 call	 ?SetError@PacketStream@mu2@@AEAAXW4Error@ErrorNet@2@@Z ; mu2::PacketStream::SetError

; 273  : 				return false;									

  000ce	32 c0		 xor	 al, al
  000d0	eb 20		 jmp	 SHORT $LN1@em
$LN9@em:

; 274  : 			}													
; 275  : 			s = swapEndian(org);							

  000d2	48 8d 54 24 21	 lea	 rdx, QWORD PTR org$2[rsp]
  000d7	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  000dc	e8 00 00 00 00	 call	 ??$swapEndian@W4CalcType@ChangedMoney@mu2@@@PacketStream@mu2@@AEAA?AW4CalcType@ChangedMoney@1@AEBW4231@@Z ; mu2::PacketStream::swapEndian<enum mu2::ChangedMoney::CalcType>
  000e1	48 8b 4c 24 48	 mov	 rcx, QWORD PTR s$[rsp]
  000e6	88 01		 mov	 BYTE PTR [rcx], al
$LN8@em:
$LN4@em:

; 276  : 		}														
; 277  : 	}
; 278  : 	return isValid();

  000e8	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  000ed	e8 00 00 00 00	 call	 ?isValid@PacketStream@mu2@@QEBA_NXZ ; mu2::PacketStream::isValid
$LN1@em:

; 279  : }

  000f2	48 83 c4 38	 add	 rsp, 56			; 00000038H
  000f6	c3		 ret	 0
??$em@W4CalcType@ChangedMoney@mu2@@@PacketStream@mu2@@QEAA_NAEAW4CalcType@ChangedMoney@1@@Z ENDP ; mu2::PacketStream::em<enum mu2::ChangedMoney::CalcType>
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ??_GChangedMoney@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GChangedMoney@mu2@@UEAAPEAXI@Z PROC			; mu2::ChangedMoney::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 172  : 	virtual~ISerializer() {}

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx
  0001c	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00020	83 e0 01	 and	 eax, 1
  00023	85 c0		 test	 eax, eax
  00025	74 10		 je	 SHORT $LN2@scalar
  00027	ba 18 00 00 00	 mov	 edx, 24
  0002c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00031	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00036	90		 npad	 1
$LN2@scalar:
  00037	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003c	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00040	c3		 ret	 0
??_GChangedMoney@mu2@@UEAAPEAXI@Z ENDP			; mu2::ChangedMoney::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ??1ChangedMoney@mu2@@UEAA@XZ
_TEXT	SEGMENT
this$ = 8
??1ChangedMoney@mu2@@UEAA@XZ PROC			; mu2::ChangedMoney::~ChangedMoney, COMDAT
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 172  : 	virtual~ISerializer() {}

  00005	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0000a	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  00011	48 89 08	 mov	 QWORD PTR [rax], rcx
  00014	c3		 ret	 0
??1ChangedMoney@mu2@@UEAA@XZ ENDP			; mu2::ChangedMoney::~ChangedMoney
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Shared\Protocol\Common\StructuresItem.h
;	COMDAT ?PackUnpack@ChangedMoney@mu2@@UEAA_NAEAVPacketStream@2@@Z
_TEXT	SEGMENT
this$ = 48
bs$ = 56
?PackUnpack@ChangedMoney@mu2@@UEAA_NAEAVPacketStream@2@@Z PROC ; mu2::ChangedMoney::PackUnpack, COMDAT

; 2014 : 	{

$LN49:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 2015 : 		bs.rw(money );

  0000e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00013	48 83 c0 08	 add	 rax, 8
  00017	48 8b d0	 mov	 rdx, rax
  0001a	48 8b 4c 24 38	 mov	 rcx, QWORD PTR bs$[rsp]
  0001f	e8 00 00 00 00	 call	 ??$rw@_J@PacketStream@mu2@@QEAA_NAEA_J@Z ; mu2::PacketStream::rw<__int64>

; 2016 : 		bs.em(addSub );

  00024	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00029	48 83 c0 10	 add	 rax, 16
  0002d	48 8b d0	 mov	 rdx, rax
  00030	48 8b 4c 24 38	 mov	 rcx, QWORD PTR bs$[rsp]
  00035	e8 00 00 00 00	 call	 ??$em@W4CalcType@ChangedMoney@mu2@@@PacketStream@mu2@@QEAA_NAEAW4CalcType@ChangedMoney@1@@Z ; mu2::PacketStream::em<enum mu2::ChangedMoney::CalcType>

; 2017 : 		bs.em(moneyType );

  0003a	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003f	48 83 c0 11	 add	 rax, 17
  00043	48 8b d0	 mov	 rdx, rax
  00046	48 8b 4c 24 38	 mov	 rcx, QWORD PTR bs$[rsp]
  0004b	e8 00 00 00 00	 call	 ??$em@W4MoneyType@ChangedMoney@mu2@@@PacketStream@mu2@@QEAA_NAEAW4MoneyType@ChangedMoney@1@@Z ; mu2::PacketStream::em<enum mu2::ChangedMoney::MoneyType>

; 2018 : 
; 2019 : 		return bs.isValid();

  00050	48 8b 4c 24 38	 mov	 rcx, QWORD PTR bs$[rsp]
  00055	e8 00 00 00 00	 call	 ?isValid@PacketStream@mu2@@QEBA_NXZ ; mu2::PacketStream::isValid

; 2020 : 	}

  0005a	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0005e	c3		 ret	 0
?PackUnpack@ChangedMoney@mu2@@UEAA_NAEAVPacketStream@2@@Z ENDP ; mu2::ChangedMoney::PackUnpack
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Shared\Protocol\Common\StructuresItem.h
;	COMDAT ?Reset@ChangedMoney@mu2@@UEAAXXZ
_TEXT	SEGMENT
this$ = 8
?Reset@ChangedMoney@mu2@@UEAAXXZ PROC			; mu2::ChangedMoney::Reset, COMDAT

; 2007 : 	{

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 2008 : 		money = 0;

  00005	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0000a	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0

; 2009 : 		addSub = CalcType::NONE;

  00012	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00017	c6 40 10 00	 mov	 BYTE PTR [rax+16], 0

; 2010 : 		moneyType = MoneyType::ZEN;

  0001b	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00020	c6 40 11 01	 mov	 BYTE PTR [rax+17], 1

; 2011 : 	}

  00024	c3		 ret	 0
?Reset@ChangedMoney@mu2@@UEAAXXZ ENDP			; mu2::ChangedMoney::Reset
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ??_GDateTimeEx@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GDateTimeEx@mu2@@UEAAPEAXI@Z PROC			; mu2::DateTimeEx::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 172  : 	virtual~ISerializer() {}

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx
  0001c	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00020	83 e0 01	 and	 eax, 1
  00023	85 c0		 test	 eax, eax
  00025	74 10		 je	 SHORT $LN2@scalar
  00027	ba 18 00 00 00	 mov	 edx, 24
  0002c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00031	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00036	90		 npad	 1
$LN2@scalar:
  00037	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003c	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00040	c3		 ret	 0
??_GDateTimeEx@mu2@@UEAAPEAXI@Z ENDP			; mu2::DateTimeEx::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Shared\Protocol\Common\StructDateTimeEx.h
;	COMDAT ?Reset@DateTimeEx@mu2@@UEAAXXZ
_TEXT	SEGMENT
time$ = 0
this$ = 32
?Reset@DateTimeEx@mu2@@UEAAXXZ PROC			; mu2::DateTimeEx::Reset, COMDAT

; 335  : 	{

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 18	 sub	 rsp, 24

; 336  : 		setDefaultDateTime(m_time);

  00009	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 c0 08	 add	 rax, 8
  00012	48 89 04 24	 mov	 QWORD PTR time$[rsp], rax

; 356  : 		time.year = 1900;

  00016	b8 6c 07 00 00	 mov	 eax, 1900		; 0000076cH
  0001b	48 8b 0c 24	 mov	 rcx, QWORD PTR time$[rsp]
  0001f	66 89 01	 mov	 WORD PTR [rcx], ax

; 357  : 		time.month = 1;

  00022	b8 01 00 00 00	 mov	 eax, 1
  00027	48 8b 0c 24	 mov	 rcx, QWORD PTR time$[rsp]
  0002b	66 89 41 02	 mov	 WORD PTR [rcx+2], ax

; 358  : 		time.day = 1;

  0002f	b8 01 00 00 00	 mov	 eax, 1
  00034	48 8b 0c 24	 mov	 rcx, QWORD PTR time$[rsp]
  00038	66 89 41 04	 mov	 WORD PTR [rcx+4], ax

; 359  : 		time.hour = 0;

  0003c	33 c0		 xor	 eax, eax
  0003e	48 8b 0c 24	 mov	 rcx, QWORD PTR time$[rsp]
  00042	66 89 41 06	 mov	 WORD PTR [rcx+6], ax

; 360  : 		time.minute = 0;

  00046	33 c0		 xor	 eax, eax
  00048	48 8b 0c 24	 mov	 rcx, QWORD PTR time$[rsp]
  0004c	66 89 41 08	 mov	 WORD PTR [rcx+8], ax

; 361  : 		time.second = 0;

  00050	33 c0		 xor	 eax, eax
  00052	48 8b 0c 24	 mov	 rcx, QWORD PTR time$[rsp]
  00056	66 89 41 0a	 mov	 WORD PTR [rcx+10], ax

; 362  : 		time.fraction = 0;

  0005a	48 8b 04 24	 mov	 rax, QWORD PTR time$[rsp]
  0005e	c7 40 0c 00 00
	00 00		 mov	 DWORD PTR [rax+12], 0

; 337  : 	}

  00065	48 83 c4 18	 add	 rsp, 24
  00069	c3		 ret	 0
?Reset@DateTimeEx@mu2@@UEAAXXZ ENDP			; mu2::DateTimeEx::Reset
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Shared\Protocol\Common\StructDateTimeEx.h
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
; File F:\Release_Branch\Shared\Protocol\Common\StructDateTimeEx.h
;	COMDAT ?PackUnpack@DateTimeEx@mu2@@UEAA_NAEAVPacketStream@2@@Z
_TEXT	SEGMENT
$T1 = 32
t$2 = 40
t$3 = 48
this$ = 80
bs$ = 88
?PackUnpack@DateTimeEx@mu2@@UEAA_NAEAVPacketStream@2@@Z PROC ; mu2::DateTimeEx::PackUnpack, COMDAT

; 318  : 	{

$LN60:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 48	 sub	 rsp, 72			; 00000048H
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h

; 107  : 	ePackType		GetPackType() { return m_ePackType; }

  0000e	48 8b 44 24 58	 mov	 rax, QWORD PTR bs$[rsp]
  00013	0f b6 40 28	 movzx	 eax, BYTE PTR [rax+40]
  00017	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al
; File F:\Release_Branch\Shared\Protocol\Common\StructDateTimeEx.h

; 319  : 		if (bs.GetPackType() == PacketStream::ePack)

  0001b	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  00020	0f b6 c0	 movzx	 eax, al
  00023	85 c0		 test	 eax, eax
  00025	75 21		 jne	 SHORT $LN2@PackUnpack

; 320  : 		{
; 321  : 			time_t t = *this;

  00027	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  0002c	e8 00 00 00 00	 call	 ??BDateTimeEx@mu2@@QEBA?B_JXZ ; mu2::DateTimeEx::operator __int64 const 
  00031	48 89 44 24 28	 mov	 QWORD PTR t$2[rsp], rax

; 322  : 			bs.rw(t);

  00036	48 8d 54 24 28	 lea	 rdx, QWORD PTR t$2[rsp]
  0003b	48 8b 4c 24 58	 mov	 rcx, QWORD PTR bs$[rsp]
  00040	e8 00 00 00 00	 call	 ??$rw@_J@PacketStream@mu2@@QEAA_NAEA_J@Z ; mu2::PacketStream::rw<__int64>
  00045	90		 npad	 1

; 323  : 		}

  00046	eb 1f		 jmp	 SHORT $LN3@PackUnpack
$LN2@PackUnpack:

; 324  : 		else
; 325  : 		{
; 326  : 			time_t t;
; 327  : 			bs.rw(t);

  00048	48 8d 54 24 30	 lea	 rdx, QWORD PTR t$3[rsp]
  0004d	48 8b 4c 24 58	 mov	 rcx, QWORD PTR bs$[rsp]
  00052	e8 00 00 00 00	 call	 ??$rw@_J@PacketStream@mu2@@QEAA_NAEA_J@Z ; mu2::PacketStream::rw<__int64>

; 328  : 			*this = t;

  00057	48 8d 54 24 30	 lea	 rdx, QWORD PTR t$3[rsp]
  0005c	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  00061	e8 00 00 00 00	 call	 ??4DateTimeEx@mu2@@QEAAAEAU01@AEB_J@Z ; mu2::DateTimeEx::operator=
  00066	90		 npad	 1
$LN3@PackUnpack:

; 329  : 		}
; 330  : 
; 331  : 		return bs.isValid();

  00067	48 8b 4c 24 58	 mov	 rcx, QWORD PTR bs$[rsp]
  0006c	e8 00 00 00 00	 call	 ?isValid@PacketStream@mu2@@QEBA_NXZ ; mu2::PacketStream::isValid

; 332  : 	}

  00071	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00075	c3		 ret	 0
?PackUnpack@DateTimeEx@mu2@@UEAA_NAEAVPacketStream@2@@Z ENDP ; mu2::DateTimeEx::PackUnpack
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Shared\Protocol\Common\StructDateTimeEx.h
;	COMDAT ??BDateTimeEx@mu2@@QEBA?B_JXZ
_TEXT	SEGMENT
tv87 = 80
dateTime$ = 88
this$ = 128
??BDateTimeEx@mu2@@QEBA?B_JXZ PROC			; mu2::DateTimeEx::operator __int64 const , COMDAT

; 292  : 	{

$LN3:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 78	 sub	 rsp, 120		; 00000078H

; 293  : 		mu2::DateTime dateTime(m_time.year, m_time.month, m_time.day, m_time.hour, m_time.minute, m_time.second);

  00009	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00011	0f b7 40 12	 movzx	 eax, WORD PTR [rax+18]
  00015	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0001d	0f b7 49 10	 movzx	 ecx, WORD PTR [rcx+16]
  00021	48 8b 94 24 80
	00 00 00	 mov	 rdx, QWORD PTR this$[rsp]
  00029	0f b7 52 0e	 movzx	 edx, WORD PTR [rdx+14]
  0002d	4c 8b 84 24 80
	00 00 00	 mov	 r8, QWORD PTR this$[rsp]
  00035	45 0f b7 40 0c	 movzx	 r8d, WORD PTR [r8+12]
  0003a	4c 8b 8c 24 80
	00 00 00	 mov	 r9, QWORD PTR this$[rsp]
  00042	45 0f b7 49 0a	 movzx	 r9d, WORD PTR [r9+10]
  00047	44 89 4c 24 50	 mov	 DWORD PTR tv87[rsp], r9d
  0004c	4c 8b 94 24 80
	00 00 00	 mov	 r10, QWORD PTR this$[rsp]
  00054	45 0f bf 52 08	 movsx	 r10d, WORD PTR [r10+8]
  00059	c7 44 24 48 00
	00 00 00	 mov	 DWORD PTR [rsp+72], 0
  00061	c7 44 24 40 00
	00 00 00	 mov	 DWORD PTR [rsp+64], 0
  00069	c7 44 24 38 00
	00 00 00	 mov	 DWORD PTR [rsp+56], 0
  00071	89 44 24 30	 mov	 DWORD PTR [rsp+48], eax
  00075	89 4c 24 28	 mov	 DWORD PTR [rsp+40], ecx
  00079	89 54 24 20	 mov	 DWORD PTR [rsp+32], edx
  0007d	45 8b c8	 mov	 r9d, r8d
  00080	8b 44 24 50	 mov	 eax, DWORD PTR tv87[rsp]
  00084	44 8b c0	 mov	 r8d, eax
  00087	41 8b d2	 mov	 edx, r10d
  0008a	48 8d 4c 24 58	 lea	 rcx, QWORD PTR dateTime$[rsp]
  0008f	e8 00 00 00 00	 call	 ??0DateTime@mu2@@QEAA@HHHHHHHHH@Z ; mu2::DateTime::DateTime

; 294  : 		return dateTime.GetTime();

  00094	48 8d 4c 24 58	 lea	 rcx, QWORD PTR dateTime$[rsp]
  00099	e8 00 00 00 00	 call	 ?GetTime@DateTime@mu2@@QEBA_JXZ ; mu2::DateTime::GetTime

; 295  : 	}

  0009e	48 83 c4 78	 add	 rsp, 120		; 00000078H
  000a2	c3		 ret	 0
??BDateTimeEx@mu2@@QEBA?B_JXZ ENDP			; mu2::DateTimeEx::operator __int64 const 
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Shared\Protocol\Common\StructDateTimeEx.h
; File F:\Release_Branch\Server\Development\Framework\Core\DateTime.h
; File F:\Release_Branch\Shared\Protocol\Common\StructDateTimeEx.h
;	COMDAT ??4DateTimeEx@mu2@@QEAAAEAU01@AEB_J@Z
_TEXT	SEGMENT
dateTime$ = 32
systime$1 = 48
__$ArrayPad$ = 64
this$ = 96
timet$ = 104
??4DateTimeEx@mu2@@QEAAAEAU01@AEB_J@Z PROC		; mu2::DateTimeEx::operator=, COMDAT

; 276  : 	{

$LN5:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H
  0000e	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR __security_cookie
  00015	48 33 c4	 xor	 rax, rsp
  00018	48 89 44 24 40	 mov	 QWORD PTR __$ArrayPad$[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\DateTime.h

; 217  : : status_(valid)

  0001d	c6 44 24 20 00	 mov	 BYTE PTR dateTime$[rsp], 0

; 218  : {	
; 219  : 	time_.QuadPart = 0;    // 1 Jan-1601, 00:00, 100ns clicks 

  00022	48 c7 44 24 28
	00 00 00 00	 mov	 QWORD PTR dateTime$[rsp+8], 0

; 220  : 
; 221  : 	SYSTEMTIME systime;
; 222  : 	::GetLocalTime( &systime );

  0002b	48 8d 4c 24 30	 lea	 rcx, QWORD PTR systime$1[rsp]
  00030	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetLocalTime

; 223  : 	*this = systime;

  00036	48 8d 54 24 30	 lea	 rdx, QWORD PTR systime$1[rsp]
  0003b	48 8d 4c 24 20	 lea	 rcx, QWORD PTR dateTime$[rsp]
  00040	e8 00 00 00 00	 call	 ??4DateTime@mu2@@QEAAAEBV01@AEBU_SYSTEMTIME@@@Z ; mu2::DateTime::operator=
  00045	90		 npad	 1
; File F:\Release_Branch\Shared\Protocol\Common\StructDateTimeEx.h

; 278  : 		dateTime.SetTime(timet);

  00046	48 8b 44 24 68	 mov	 rax, QWORD PTR timet$[rsp]
  0004b	48 8b 10	 mov	 rdx, QWORD PTR [rax]
  0004e	48 8d 4c 24 20	 lea	 rcx, QWORD PTR dateTime$[rsp]
  00053	e8 00 00 00 00	 call	 ?SetTime@DateTime@mu2@@QEAAX_J@Z ; mu2::DateTime::SetTime

; 279  : 
; 280  : 		m_time.year = (Short)dateTime.GetYear();

  00058	48 8d 4c 24 20	 lea	 rcx, QWORD PTR dateTime$[rsp]
  0005d	e8 00 00 00 00	 call	 ?GetYear@DateTime@mu2@@QEBAHXZ ; mu2::DateTime::GetYear
  00062	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  00067	66 89 41 08	 mov	 WORD PTR [rcx+8], ax

; 281  : 		m_time.month = (Short)dateTime.GetMonth();

  0006b	48 8d 4c 24 20	 lea	 rcx, QWORD PTR dateTime$[rsp]
  00070	e8 00 00 00 00	 call	 ?GetMonth@DateTime@mu2@@QEBAHXZ ; mu2::DateTime::GetMonth
  00075	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  0007a	66 89 41 0a	 mov	 WORD PTR [rcx+10], ax

; 282  : 		m_time.day = (Short)dateTime.GetDay();

  0007e	48 8d 4c 24 20	 lea	 rcx, QWORD PTR dateTime$[rsp]
  00083	e8 00 00 00 00	 call	 ?GetDay@DateTime@mu2@@QEBAHXZ ; mu2::DateTime::GetDay
  00088	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  0008d	66 89 41 0c	 mov	 WORD PTR [rcx+12], ax

; 283  : 		m_time.hour = (Short)dateTime.GetHour();

  00091	48 8d 4c 24 20	 lea	 rcx, QWORD PTR dateTime$[rsp]
  00096	e8 00 00 00 00	 call	 ?GetHour@DateTime@mu2@@QEBAHXZ ; mu2::DateTime::GetHour
  0009b	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  000a0	66 89 41 0e	 mov	 WORD PTR [rcx+14], ax

; 284  : 		m_time.minute = (Short)dateTime.GetMinute();

  000a4	48 8d 4c 24 20	 lea	 rcx, QWORD PTR dateTime$[rsp]
  000a9	e8 00 00 00 00	 call	 ?GetMinute@DateTime@mu2@@QEBAHXZ ; mu2::DateTime::GetMinute
  000ae	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  000b3	66 89 41 10	 mov	 WORD PTR [rcx+16], ax

; 285  : 		m_time.second = (Short)dateTime.GetSecond();

  000b7	48 8d 4c 24 20	 lea	 rcx, QWORD PTR dateTime$[rsp]
  000bc	e8 00 00 00 00	 call	 ?GetSecond@DateTime@mu2@@QEBAHXZ ; mu2::DateTime::GetSecond
  000c1	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  000c6	66 89 41 12	 mov	 WORD PTR [rcx+18], ax

; 286  : 		m_time.fraction = 0;

  000ca	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  000cf	c7 40 14 00 00
	00 00		 mov	 DWORD PTR [rax+20], 0

; 287  : 
; 288  : 		return *this;

  000d6	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]

; 289  : 	}

  000db	48 8b 4c 24 40	 mov	 rcx, QWORD PTR __$ArrayPad$[rsp]
  000e0	48 33 cc	 xor	 rcx, rsp
  000e3	e8 00 00 00 00	 call	 __security_check_cookie
  000e8	48 83 c4 58	 add	 rsp, 88			; 00000058H
  000ec	c3		 ret	 0
??4DateTimeEx@mu2@@QEAAAEAU01@AEB_J@Z ENDP		; mu2::DateTimeEx::operator=
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Shared\Protocol\Common\StructDateTimeEx.h
;	COMDAT ??0DateTimeEx@mu2@@QEAA@XZ
_TEXT	SEGMENT
time$ = 0
this$ = 32
??0DateTimeEx@mu2@@QEAA@XZ PROC				; mu2::DateTimeEx::DateTimeEx, COMDAT

; 75   : 	DateTimeEx() 

$LN11:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 18	 sub	 rsp, 24
  00009	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx

; 76   : 	{

  00018	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7DateTimeEx@mu2@@6B@
  00024	48 89 08	 mov	 QWORD PTR [rax], rcx

; 77   : 		setDefaultDateTime(m_time);

  00027	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0002c	48 83 c0 08	 add	 rax, 8
  00030	48 89 04 24	 mov	 QWORD PTR time$[rsp], rax

; 356  : 		time.year = 1900;

  00034	b8 6c 07 00 00	 mov	 eax, 1900		; 0000076cH
  00039	48 8b 0c 24	 mov	 rcx, QWORD PTR time$[rsp]
  0003d	66 89 01	 mov	 WORD PTR [rcx], ax

; 357  : 		time.month = 1;

  00040	b8 01 00 00 00	 mov	 eax, 1
  00045	48 8b 0c 24	 mov	 rcx, QWORD PTR time$[rsp]
  00049	66 89 41 02	 mov	 WORD PTR [rcx+2], ax

; 358  : 		time.day = 1;

  0004d	b8 01 00 00 00	 mov	 eax, 1
  00052	48 8b 0c 24	 mov	 rcx, QWORD PTR time$[rsp]
  00056	66 89 41 04	 mov	 WORD PTR [rcx+4], ax

; 359  : 		time.hour = 0;

  0005a	33 c0		 xor	 eax, eax
  0005c	48 8b 0c 24	 mov	 rcx, QWORD PTR time$[rsp]
  00060	66 89 41 06	 mov	 WORD PTR [rcx+6], ax

; 360  : 		time.minute = 0;

  00064	33 c0		 xor	 eax, eax
  00066	48 8b 0c 24	 mov	 rcx, QWORD PTR time$[rsp]
  0006a	66 89 41 08	 mov	 WORD PTR [rcx+8], ax

; 361  : 		time.second = 0;

  0006e	33 c0		 xor	 eax, eax
  00070	48 8b 0c 24	 mov	 rcx, QWORD PTR time$[rsp]
  00074	66 89 41 0a	 mov	 WORD PTR [rcx+10], ax

; 362  : 		time.fraction = 0;

  00078	48 8b 04 24	 mov	 rax, QWORD PTR time$[rsp]
  0007c	c7 40 0c 00 00
	00 00		 mov	 DWORD PTR [rax+12], 0

; 78   : 	}

  00083	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00088	48 83 c4 18	 add	 rsp, 24
  0008c	c3		 ret	 0
??0DateTimeEx@mu2@@QEAA@XZ ENDP				; mu2::DateTimeEx::DateTimeEx
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ??_GISerializer@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GISerializer@mu2@@UEAAPEAXI@Z PROC			; mu2::ISerializer::`scalar deleting destructor', COMDAT
$LN10:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 172  : 	virtual~ISerializer() {}

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx
  0001c	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00020	83 e0 01	 and	 eax, 1
  00023	85 c0		 test	 eax, eax
  00025	74 10		 je	 SHORT $LN2@scalar
  00027	ba 08 00 00 00	 mov	 edx, 8
  0002c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00031	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00036	90		 npad	 1
$LN2@scalar:
  00037	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003c	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00040	c3		 ret	 0
??_GISerializer@mu2@@UEAAPEAXI@Z ENDP			; mu2::ISerializer::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ??$swapEndian@_J@PacketStream@mu2@@AEAA_JAEB_J@Z
_TEXT	SEGMENT
k$1 = 0
dest$ = 8
source$ = 16
__$ArrayPad$ = 24
this$ = 48
u$ = 56
??$swapEndian@_J@PacketStream@mu2@@AEAA_JAEB_J@Z PROC	; mu2::PacketStream::swapEndian<__int64>, COMDAT

; 1103 : {

$LN6:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000e	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR __security_cookie
  00015	48 33 c4	 xor	 rax, rsp
  00018	48 89 44 24 18	 mov	 QWORD PTR __$ArrayPad$[rsp], rax

; 1104 : 	static_assert(std::is_fundamental<T>::value || std::is_enum<T>::value, "swapEndian> Type must be fundamental");
; 1105 : 
; 1106 : 	union
; 1107 : 	{
; 1108 : 		T u;
; 1109 : 		unsigned char u8[sizeof(T)];
; 1110 : 	} source, dest;
; 1111 : 
; 1112 : 	source.u = u;

  0001d	48 8b 44 24 38	 mov	 rax, QWORD PTR u$[rsp]
  00022	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00025	48 89 44 24 10	 mov	 QWORD PTR source$[rsp], rax

; 1113 : 	dest.u = u;

  0002a	48 8b 44 24 38	 mov	 rax, QWORD PTR u$[rsp]
  0002f	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00032	48 89 44 24 08	 mov	 QWORD PTR dest$[rsp], rax

; 1114 : 
; 1115 : 	for (size_t k = 0; k < sizeof(T); k++)

  00037	48 c7 04 24 00
	00 00 00	 mov	 QWORD PTR k$1[rsp], 0
  0003f	eb 0b		 jmp	 SHORT $LN4@swapEndian
$LN2@swapEndian:
  00041	48 8b 04 24	 mov	 rax, QWORD PTR k$1[rsp]
  00045	48 ff c0	 inc	 rax
  00048	48 89 04 24	 mov	 QWORD PTR k$1[rsp], rax
$LN4@swapEndian:
  0004c	48 83 3c 24 08	 cmp	 QWORD PTR k$1[rsp], 8
  00051	73 18		 jae	 SHORT $LN3@swapEndian

; 1116 : 		dest.u8[k] = source.u8[sizeof(T) - k - 1];

  00053	b8 08 00 00 00	 mov	 eax, 8
  00058	48 2b 04 24	 sub	 rax, QWORD PTR k$1[rsp]
  0005c	48 8b 0c 24	 mov	 rcx, QWORD PTR k$1[rsp]
  00060	0f b6 44 04 0f	 movzx	 eax, BYTE PTR source$[rsp+rax-1]
  00065	88 44 0c 08	 mov	 BYTE PTR dest$[rsp+rcx], al
  00069	eb d6		 jmp	 SHORT $LN2@swapEndian
$LN3@swapEndian:

; 1117 : 
; 1118 : 	return dest.u;

  0006b	48 8b 44 24 08	 mov	 rax, QWORD PTR dest$[rsp]

; 1119 : }

  00070	48 8b 4c 24 18	 mov	 rcx, QWORD PTR __$ArrayPad$[rsp]
  00075	48 33 cc	 xor	 rcx, rsp
  00078	e8 00 00 00 00	 call	 __security_check_cookie
  0007d	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00081	c3		 ret	 0
??$swapEndian@_J@PacketStream@mu2@@AEAA_JAEB_J@Z ENDP	; mu2::PacketStream::swapEndian<__int64>
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ?checkingBuffer@PacketStream@mu2@@AEAA_NXZ
_TEXT	SEGMENT
this$ = 48
?checkingBuffer@PacketStream@mu2@@AEAA_NXZ PROC		; mu2::PacketStream::checkingBuffer, COMDAT

; 182  : {

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 183  : 	if (m_data == NULL)

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00013	75 32		 jne	 SHORT $LN2@checkingBu

; 184  : 	{
; 185  : 		if (false == resize(m_initSize))

  00015	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0001d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00022	8b 51 2c	 mov	 edx, DWORD PTR [rcx+44]
  00025	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0002a	ff 50 08	 call	 QWORD PTR [rax+8]
  0002d	0f b6 c0	 movzx	 eax, al
  00030	85 c0		 test	 eax, eax
  00032	75 13		 jne	 SHORT $LN3@checkingBu

; 186  : 		{
; 187  : 			SetError(ErrorNet::ResizeFailed);

  00034	ba 54 00 00 00	 mov	 edx, 84			; 00000054H
  00039	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0003e	e8 00 00 00 00	 call	 ?SetError@PacketStream@mu2@@AEAAXW4Error@ErrorNet@2@@Z ; mu2::PacketStream::SetError

; 188  : 			return false;

  00043	32 c0		 xor	 al, al
  00045	eb 02		 jmp	 SHORT $LN1@checkingBu
$LN3@checkingBu:
$LN2@checkingBu:

; 189  : 		}
; 190  : 	}
; 191  : 
; 192  : 	return true;

  00047	b0 01		 mov	 al, 1
$LN1@checkingBu:

; 193  : }

  00049	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0004d	c3		 ret	 0
?checkingBuffer@PacketStream@mu2@@AEAA_NXZ ENDP		; mu2::PacketStream::checkingBuffer
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ??$rw@_J@PacketStream@mu2@@QEAA_NAEA_J@Z
_TEXT	SEGMENT
swapped$1 = 32
org$2 = 40
this$ = 64
s$ = 72
??$rw@_J@PacketStream@mu2@@QEAA_NAEA_J@Z PROC		; mu2::PacketStream::rw<__int64>, COMDAT

; 141  : 	DECLARE_PACKUNPACK(Int64)

$LN47:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 38	 sub	 rsp, 56			; 00000038H
  0000e	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00013	e8 00 00 00 00	 call	 ?isValid@PacketStream@mu2@@QEBA_NXZ ; mu2::PacketStream::isValid
  00018	0f b6 c0	 movzx	 eax, al
  0001b	85 c0		 test	 eax, eax
  0001d	75 07		 jne	 SHORT $LN2@rw
  0001f	32 c0		 xor	 al, al
  00021	e9 bf 00 00 00	 jmp	 $LN1@rw
$LN2@rw:
  00026	0f b6 05 00 00
	00 00		 movzx	 eax, BYTE PTR ?isBigEndian@PacketStream@mu2@@2_NA ; mu2::PacketStream::isBigEndian
  0002d	85 c0		 test	 eax, eax
  0002f	75 3d		 jne	 SHORT $LN3@rw
  00031	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00036	0f b6 40 28	 movzx	 eax, BYTE PTR [rax+40]
  0003a	85 c0		 test	 eax, eax
  0003c	75 18		 jne	 SHORT $LN5@rw
  0003e	41 b8 08 00 00
	00		 mov	 r8d, 8
  00044	48 8b 54 24 48	 mov	 rdx, QWORD PTR s$[rsp]
  00049	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0004e	e8 00 00 00 00	 call	 ?Write@PacketStream@mu2@@QEAAIPEBEI@Z ; mu2::PacketStream::Write
  00053	90		 npad	 1
  00054	eb 16		 jmp	 SHORT $LN6@rw
$LN5@rw:
  00056	41 b8 08 00 00
	00		 mov	 r8d, 8
  0005c	48 8b 54 24 48	 mov	 rdx, QWORD PTR s$[rsp]
  00061	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00066	e8 00 00 00 00	 call	 ?Read@PacketStream@mu2@@QEAAIPEAEI@Z ; mu2::PacketStream::Read
  0006b	90		 npad	 1
$LN6@rw:
  0006c	eb 6d		 jmp	 SHORT $LN4@rw
$LN3@rw:
  0006e	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00073	0f b6 40 28	 movzx	 eax, BYTE PTR [rax+40]
  00077	85 c0		 test	 eax, eax
  00079	75 2c		 jne	 SHORT $LN7@rw
  0007b	48 8b 54 24 48	 mov	 rdx, QWORD PTR s$[rsp]
  00080	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00085	e8 00 00 00 00	 call	 ??$swapEndian@_J@PacketStream@mu2@@AEAA_JAEB_J@Z ; mu2::PacketStream::swapEndian<__int64>
  0008a	48 89 44 24 20	 mov	 QWORD PTR swapped$1[rsp], rax
  0008f	41 b8 08 00 00
	00		 mov	 r8d, 8
  00095	48 8d 54 24 20	 lea	 rdx, QWORD PTR swapped$1[rsp]
  0009a	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0009f	e8 00 00 00 00	 call	 ?Write@PacketStream@mu2@@QEAAIPEBEI@Z ; mu2::PacketStream::Write
  000a4	90		 npad	 1
  000a5	eb 34		 jmp	 SHORT $LN8@rw
$LN7@rw:
  000a7	41 b8 08 00 00
	00		 mov	 r8d, 8
  000ad	48 8d 54 24 28	 lea	 rdx, QWORD PTR org$2[rsp]
  000b2	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  000b7	e8 00 00 00 00	 call	 ?Read@PacketStream@mu2@@QEAAIPEAEI@Z ; mu2::PacketStream::Read
  000bc	85 c0		 test	 eax, eax
  000be	75 04		 jne	 SHORT $LN9@rw
  000c0	32 c0		 xor	 al, al
  000c2	eb 21		 jmp	 SHORT $LN1@rw
$LN9@rw:
  000c4	48 8d 54 24 28	 lea	 rdx, QWORD PTR org$2[rsp]
  000c9	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  000ce	e8 00 00 00 00	 call	 ??$swapEndian@_J@PacketStream@mu2@@AEAA_JAEB_J@Z ; mu2::PacketStream::swapEndian<__int64>
  000d3	48 8b 4c 24 48	 mov	 rcx, QWORD PTR s$[rsp]
  000d8	48 89 01	 mov	 QWORD PTR [rcx], rax
$LN8@rw:
$LN4@rw:
  000db	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  000e0	e8 00 00 00 00	 call	 ?isValid@PacketStream@mu2@@QEBA_NXZ ; mu2::PacketStream::isValid
$LN1@rw:
  000e5	48 83 c4 38	 add	 rsp, 56			; 00000038H
  000e9	c3		 ret	 0
??$rw@_J@PacketStream@mu2@@QEAA_NAEA_J@Z ENDP		; mu2::PacketStream::rw<__int64>
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ?Read@PacketStream@mu2@@QEAAIPEAEI@Z
_TEXT	SEGMENT
cnt$ = 96
hConsole$1 = 104
hConsole$2 = 112
readCnt$3 = 120
this$ = 144
buf$ = 152
count$ = 160
?Read@PacketStream@mu2@@QEAAIPEAEI@Z PROC		; mu2::PacketStream::Read, COMDAT

; 1066 : {

$LN10:
  00000	44 89 44 24 18	 mov	 DWORD PTR [rsp+24], r8d
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H

; 1067 : 	VERIFY_RETURN(count, 0);

  00016	83 bc 24 a0 00
	00 00 00	 cmp	 DWORD PTR count$[rsp], 0
  0001e	0f 85 a0 00 00
	00		 jne	 $LN2@Read
  00024	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  00029	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  0002f	48 89 44 24 68	 mov	 QWORD PTR hConsole$1[rsp], rax
  00034	66 ba 0d 00	 mov	 dx, 13
  00038	48 8b 4c 24 68	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  0003d	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  00043	c7 44 24 50 2b
	04 00 00	 mov	 DWORD PTR [rsp+80], 1067 ; 0000042bH
  0004b	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EJ@IFEHPKPB@F?3?2Release_Branch?2Server?2Develo@
  00052	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  00057	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_05IOMEMJEC@count@
  0005e	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  00063	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@KGAODAAL@mu2?3?3PacketStream?3?3Read@
  0006a	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  0006f	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  00076	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  0007b	c7 44 24 28 2b
	04 00 00	 mov	 DWORD PTR [rsp+40], 1067 ; 0000042bH
  00083	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EJ@IFEHPKPB@F?3?2Release_Branch?2Server?2Develo@
  0008a	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  0008f	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0BI@KGAODAAL@mu2?3?3PacketStream?3?3Read@
  00096	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  0009c	ba 02 00 00 00	 mov	 edx, 2
  000a1	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  000a8	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  000ad	66 ba 07 00	 mov	 dx, 7
  000b1	48 8b 4c 24 68	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  000b6	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000bc	90		 npad	 1
  000bd	33 c0		 xor	 eax, eax
  000bf	e9 8e 01 00 00	 jmp	 $LN1@Read
$LN2@Read:

; 1068 : 	VERIFY_RETURN(buf, 0);

  000c4	48 83 bc 24 98
	00 00 00 00	 cmp	 QWORD PTR buf$[rsp], 0
  000cd	0f 85 a0 00 00
	00		 jne	 $LN3@Read
  000d3	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  000d8	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  000de	48 89 44 24 70	 mov	 QWORD PTR hConsole$2[rsp], rax
  000e3	66 ba 0d 00	 mov	 dx, 13
  000e7	48 8b 4c 24 70	 mov	 rcx, QWORD PTR hConsole$2[rsp]
  000ec	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000f2	c7 44 24 50 2c
	04 00 00	 mov	 DWORD PTR [rsp+80], 1068 ; 0000042cH
  000fa	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EJ@IFEHPKPB@F?3?2Release_Branch?2Server?2Develo@
  00101	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  00106	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_03COAJHJPB@buf@
  0010d	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  00112	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@KGAODAAL@mu2?3?3PacketStream?3?3Read@
  00119	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  0011e	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  00125	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  0012a	c7 44 24 28 2c
	04 00 00	 mov	 DWORD PTR [rsp+40], 1068 ; 0000042cH
  00132	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EJ@IFEHPKPB@F?3?2Release_Branch?2Server?2Develo@
  00139	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  0013e	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0BI@KGAODAAL@mu2?3?3PacketStream?3?3Read@
  00145	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  0014b	ba 02 00 00 00	 mov	 edx, 2
  00150	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  00157	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  0015c	66 ba 07 00	 mov	 dx, 7
  00160	48 8b 4c 24 70	 mov	 rcx, QWORD PTR hConsole$2[rsp]
  00165	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  0016b	90		 npad	 1
  0016c	33 c0		 xor	 eax, eax
  0016e	e9 df 00 00 00	 jmp	 $LN1@Read
$LN3@Read:

; 1069 : 
; 1070 : 	if (m_pos == NULL)

  00173	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0017b	48 83 78 10 00	 cmp	 QWORD PTR [rax+16], 0
  00180	75 07		 jne	 SHORT $LN4@Read

; 1071 : 		return 0;

  00182	33 c0		 xor	 eax, eax
  00184	e9 c9 00 00 00	 jmp	 $LN1@Read
$LN4@Read:

; 1072 : 
; 1073 : 	if( isValid() == false ) 

  00189	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00191	e8 00 00 00 00	 call	 ?isValid@PacketStream@mu2@@QEBA_NXZ ; mu2::PacketStream::isValid
  00196	0f b6 c0	 movzx	 eax, al
  00199	85 c0		 test	 eax, eax
  0019b	75 07		 jne	 SHORT $LN5@Read

; 1074 : 		return 0;

  0019d	33 c0		 xor	 eax, eax
  0019f	e9 ae 00 00 00	 jmp	 $LN1@Read
$LN5@Read:

; 1075 : 
; 1076 : 	UInt32 cnt = count;

  001a4	8b 84 24 a0 00
	00 00		 mov	 eax, DWORD PTR count$[rsp]
  001ab	89 44 24 60	 mov	 DWORD PTR cnt$[rsp], eax

; 1077 : 
; 1078 : 	if (m_pos + cnt > m_end)

  001af	8b 44 24 60	 mov	 eax, DWORD PTR cnt$[rsp]
  001b3	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  001bb	48 03 41 10	 add	 rax, QWORD PTR [rcx+16]
  001bf	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  001c7	48 3b 41 18	 cmp	 rax, QWORD PTR [rcx+24]
  001cb	76 3a		 jbe	 SHORT $LN6@Read

; 1079 : 	{
; 1080 : 		SetError(ErrorNet::NotEnoughReadableSize);

  001cd	ba 61 00 00 00	 mov	 edx, 97			; 00000061H
  001d2	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  001da	e8 00 00 00 00	 call	 ?SetError@PacketStream@mu2@@AEAAXW4Error@ErrorNet@2@@Z ; mu2::PacketStream::SetError

; 1081 : 
; 1082 : 		Int64 readCnt = remains();

  001df	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  001e7	e8 00 00 00 00	 call	 ?remains@PacketStream@mu2@@AEAAIXZ ; mu2::PacketStream::remains
  001ec	8b c0		 mov	 eax, eax
  001ee	48 89 44 24 78	 mov	 QWORD PTR readCnt$3[rsp], rax

; 1083 : 		if ( readCnt <= 0 )

  001f3	48 83 7c 24 78
	00		 cmp	 QWORD PTR readCnt$3[rsp], 0
  001f9	7f 04		 jg	 SHORT $LN7@Read

; 1084 : 			return 0;

  001fb	33 c0		 xor	 eax, eax
  001fd	eb 53		 jmp	 SHORT $LN1@Read
$LN7@Read:

; 1085 : 
; 1086 : 		cnt = static_cast<UInt32>(readCnt);

  001ff	8b 44 24 78	 mov	 eax, DWORD PTR readCnt$3[rsp]
  00203	89 44 24 60	 mov	 DWORD PTR cnt$[rsp], eax
$LN6@Read:

; 1087 : 	}
; 1088 : 
; 1089 : 	if (cnt == 0 )

  00207	83 7c 24 60 00	 cmp	 DWORD PTR cnt$[rsp], 0
  0020c	75 04		 jne	 SHORT $LN8@Read

; 1090 : 		return 0;

  0020e	33 c0		 xor	 eax, eax
  00210	eb 40		 jmp	 SHORT $LN1@Read
$LN8@Read:

; 1091 : 
; 1092 : 	assert (cnt<=count);
; 1093 : 
; 1094 : 	memcpy(buf, m_pos, cnt);

  00212	8b 44 24 60	 mov	 eax, DWORD PTR cnt$[rsp]
  00216	44 8b c0	 mov	 r8d, eax
  00219	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00221	48 8b 50 10	 mov	 rdx, QWORD PTR [rax+16]
  00225	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR buf$[rsp]
  0022d	e8 00 00 00 00	 call	 memcpy

; 1095 : 	
; 1096 : 	m_pos += cnt;

  00232	8b 44 24 60	 mov	 eax, DWORD PTR cnt$[rsp]
  00236	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0023e	48 03 41 10	 add	 rax, QWORD PTR [rcx+16]
  00242	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0024a	48 89 41 10	 mov	 QWORD PTR [rcx+16], rax

; 1097 : 
; 1098 : 	return cnt;

  0024e	8b 44 24 60	 mov	 eax, DWORD PTR cnt$[rsp]
$LN1@Read:

; 1099 : }

  00252	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  00259	c3		 ret	 0
?Read@PacketStream@mu2@@QEAAIPEAEI@Z ENDP		; mu2::PacketStream::Read
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ?Write@PacketStream@mu2@@QEAAIPEBEI@Z
_TEXT	SEGMENT
written$ = 96
hConsole$1 = 104
tv157 = 112
this$ = 144
buf$ = 152
count$ = 160
?Write@PacketStream@mu2@@QEAAIPEBEI@Z PROC		; mu2::PacketStream::Write, COMDAT

; 1045 : {	

$LN10:
  00000	44 89 44 24 18	 mov	 DWORD PTR [rsp+24], r8d
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H

; 1046 : 	VERIFY_RETURN(checkingBuffer(), 0);

  00016	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0001e	e8 00 00 00 00	 call	 ?checkingBuffer@PacketStream@mu2@@AEAA_NXZ ; mu2::PacketStream::checkingBuffer
  00023	0f b6 c0	 movzx	 eax, al
  00026	85 c0		 test	 eax, eax
  00028	0f 85 a0 00 00
	00		 jne	 $LN2@Write
  0002e	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  00033	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  00039	48 89 44 24 68	 mov	 QWORD PTR hConsole$1[rsp], rax
  0003e	66 ba 0d 00	 mov	 dx, 13
  00042	48 8b 4c 24 68	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  00047	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  0004d	c7 44 24 50 16
	04 00 00	 mov	 DWORD PTR [rsp+80], 1046 ; 00000416H
  00055	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EJ@IFEHPKPB@F?3?2Release_Branch?2Server?2Develo@
  0005c	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  00061	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BB@HAIDOJLN@checkingBuffer?$CI?$CJ@
  00068	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  0006d	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BJ@NEGBBJOE@mu2?3?3PacketStream?3?3Write@
  00074	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  00079	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  00080	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  00085	c7 44 24 28 16
	04 00 00	 mov	 DWORD PTR [rsp+40], 1046 ; 00000416H
  0008d	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EJ@IFEHPKPB@F?3?2Release_Branch?2Server?2Develo@
  00094	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  00099	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0BJ@NEGBBJOE@mu2?3?3PacketStream?3?3Write@
  000a0	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  000a6	ba 02 00 00 00	 mov	 edx, 2
  000ab	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  000b2	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  000b7	66 ba 07 00	 mov	 dx, 7
  000bb	48 8b 4c 24 68	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  000c0	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000c6	90		 npad	 1
  000c7	33 c0		 xor	 eax, eax
  000c9	e9 d4 00 00 00	 jmp	 $LN1@Write
$LN2@Write:

; 1047 : 
; 1048 : 	UInt32 written = count;

  000ce	8b 84 24 a0 00
	00 00		 mov	 eax, DWORD PTR count$[rsp]
  000d5	89 44 24 60	 mov	 DWORD PTR written$[rsp], eax

; 1049 : 
; 1050 : 	if (m_pos + written > m_end)

  000d9	8b 44 24 60	 mov	 eax, DWORD PTR written$[rsp]
  000dd	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  000e5	48 03 41 10	 add	 rax, QWORD PTR [rcx+16]
  000e9	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  000f1	48 3b 41 18	 cmp	 rax, QWORD PTR [rcx+24]
  000f5	76 62		 jbe	 SHORT $LN3@Write

; 1051 : 	{
; 1052 : 		written = static_cast<UInt32>( (m_pos + written) - m_end );

  000f7	8b 44 24 60	 mov	 eax, DWORD PTR written$[rsp]
  000fb	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00103	48 8b 49 10	 mov	 rcx, QWORD PTR [rcx+16]
  00107	48 03 c8	 add	 rcx, rax
  0010a	48 8b c1	 mov	 rax, rcx
  0010d	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00115	48 2b 41 18	 sub	 rax, QWORD PTR [rcx+24]
  00119	89 44 24 60	 mov	 DWORD PTR written$[rsp], eax

; 1053 : 		if (false == resize(m_capacity + written))

  0011d	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00125	8b 40 20	 mov	 eax, DWORD PTR [rax+32]
  00128	03 44 24 60	 add	 eax, DWORD PTR written$[rsp]
  0012c	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00134	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00137	48 89 4c 24 70	 mov	 QWORD PTR tv157[rsp], rcx
  0013c	8b d0		 mov	 edx, eax
  0013e	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00146	48 8b 44 24 70	 mov	 rax, QWORD PTR tv157[rsp]
  0014b	ff 50 08	 call	 QWORD PTR [rax+8]
  0014e	0f b6 c0	 movzx	 eax, al
  00151	85 c0		 test	 eax, eax
  00153	75 04		 jne	 SHORT $LN4@Write

; 1054 : 		{
; 1055 : 			return 0;

  00155	33 c0		 xor	 eax, eax
  00157	eb 49		 jmp	 SHORT $LN1@Write
$LN4@Write:
$LN3@Write:

; 1056 : 		}
; 1057 : 	}
; 1058 : 
; 1059 : 	memcpy(m_pos, buf, count);

  00159	8b 84 24 a0 00
	00 00		 mov	 eax, DWORD PTR count$[rsp]
  00160	44 8b c0	 mov	 r8d, eax
  00163	48 8b 94 24 98
	00 00 00	 mov	 rdx, QWORD PTR buf$[rsp]
  0016b	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00173	48 8b 48 10	 mov	 rcx, QWORD PTR [rax+16]
  00177	e8 00 00 00 00	 call	 memcpy

; 1060 : 	m_pos += count;

  0017c	8b 84 24 a0 00
	00 00		 mov	 eax, DWORD PTR count$[rsp]
  00183	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0018b	48 03 41 10	 add	 rax, QWORD PTR [rcx+16]
  0018f	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00197	48 89 41 10	 mov	 QWORD PTR [rcx+16], rax

; 1061 : 
; 1062 : 	return count;

  0019b	8b 84 24 a0 00
	00 00		 mov	 eax, DWORD PTR count$[rsp]
$LN1@Write:

; 1063 : }

  001a2	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  001a9	c3		 ret	 0
?Write@PacketStream@mu2@@QEAAIPEBEI@Z ENDP		; mu2::PacketStream::Write
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ?_Get_deleter@_Ref_count_base@std@@UEBAPEAXAEBVtype_info@@@Z
_TEXT	SEGMENT
this$ = 8
__formal$ = 16
?_Get_deleter@_Ref_count_base@std@@UEBAPEAXAEBVtype_info@@@Z PROC ; std::_Ref_count_base::_Get_deleter, COMDAT

; 1175 :     virtual void* _Get_deleter(const type_info&) const noexcept {

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 1176 :         return nullptr;

  0000a	33 c0		 xor	 eax, eax

; 1177 :     }

  0000c	c3		 ret	 0
?_Get_deleter@_Ref_count_base@std@@UEBAPEAXAEBVtype_info@@@Z ENDP ; std::_Ref_count_base::_Get_deleter
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ?_Decref@_Ref_count_base@std@@QEAAXXZ
_TEXT	SEGMENT
this$ = 48
?_Decref@_Ref_count_base@std@@QEAAXXZ PROC		; std::_Ref_count_base::_Decref, COMDAT

; 1158 :     void _Decref() noexcept { // decrement use count

$LN11:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 1159 :         if (_MT_DECR(_Uses) == 0) {

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 c0 08	 add	 rax, 8
  00012	b9 ff ff ff ff	 mov	 ecx, -1
  00017	f0 0f c1 08	 lock xadd DWORD PTR [rax], ecx
  0001b	ff c9		 dec	 ecx
  0001d	8b c1		 mov	 eax, ecx
  0001f	85 c0		 test	 eax, eax
  00021	75 3a		 jne	 SHORT $LN2@Decref

; 1160 :             _Destroy();

  00023	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00028	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0002b	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00030	ff 10		 call	 QWORD PTR [rax]

; 1166 :         if (_MT_DECR(_Weaks) == 0) {

  00032	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00037	48 83 c0 0c	 add	 rax, 12
  0003b	b9 ff ff ff ff	 mov	 ecx, -1
  00040	f0 0f c1 08	 lock xadd DWORD PTR [rax], ecx
  00044	ff c9		 dec	 ecx
  00046	8b c1		 mov	 eax, ecx
  00048	85 c0		 test	 eax, eax
  0004a	75 11		 jne	 SHORT $LN2@Decref

; 1167 :             _Delete_this();

  0004c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00051	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00054	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00059	ff 50 08	 call	 QWORD PTR [rax+8]
  0005c	90		 npad	 1
$LN2@Decref:

; 1161 :             _Decwref();
; 1162 :         }
; 1163 :     }

  0005d	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00061	c3		 ret	 0
?_Decref@_Ref_count_base@std@@QEAAXXZ ENDP		; std::_Ref_count_base::_Decref
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Calculate_growth@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@CA_K_K00@Z
_TEXT	SEGMENT
_Masked$ = 0
$T1 = 8
tv75 = 16
$T2 = 24
$T3 = 32
_Requested$ = 64
_Old$ = 72
_Max$ = 80
?_Calculate_growth@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@CA_K_K00@Z PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Calculate_growth, COMDAT

; 2977 :         const size_type _Requested, const size_type _Old, const size_type _Max) noexcept {

$LN13:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 2978 :         const size_type _Masked = _Requested | _Alloc_mask;

  00013	48 8b 44 24 40	 mov	 rax, QWORD PTR _Requested$[rsp]
  00018	48 83 c8 07	 or	 rax, 7
  0001c	48 89 04 24	 mov	 QWORD PTR _Masked$[rsp], rax

; 2979 :         if (_Masked > _Max) { // the mask overflows, settle for max_size()

  00020	48 8b 44 24 50	 mov	 rax, QWORD PTR _Max$[rsp]
  00025	48 39 04 24	 cmp	 QWORD PTR _Masked$[rsp], rax
  00029	76 0a		 jbe	 SHORT $LN2@Calculate_

; 2980 :             return _Max;

  0002b	48 8b 44 24 50	 mov	 rax, QWORD PTR _Max$[rsp]
  00030	e9 83 00 00 00	 jmp	 $LN1@Calculate_
$LN2@Calculate_:

; 2981 :         }
; 2982 : 
; 2983 :         if (_Old > _Max - _Old / 2) { // similarly, geometric overflows

  00035	33 d2		 xor	 edx, edx
  00037	48 8b 44 24 48	 mov	 rax, QWORD PTR _Old$[rsp]
  0003c	b9 02 00 00 00	 mov	 ecx, 2
  00041	48 f7 f1	 div	 rcx
  00044	48 8b 4c 24 50	 mov	 rcx, QWORD PTR _Max$[rsp]
  00049	48 2b c8	 sub	 rcx, rax
  0004c	48 8b c1	 mov	 rax, rcx
  0004f	48 39 44 24 48	 cmp	 QWORD PTR _Old$[rsp], rax
  00054	76 07		 jbe	 SHORT $LN3@Calculate_

; 2984 :             return _Max;

  00056	48 8b 44 24 50	 mov	 rax, QWORD PTR _Max$[rsp]
  0005b	eb 5b		 jmp	 SHORT $LN1@Calculate_
$LN3@Calculate_:

; 2987 :         return (_STD max)(_Masked, _Old + _Old / 2);

  0005d	33 d2		 xor	 edx, edx
  0005f	48 8b 44 24 48	 mov	 rax, QWORD PTR _Old$[rsp]
  00064	b9 02 00 00 00	 mov	 ecx, 2
  00069	48 f7 f1	 div	 rcx
  0006c	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _Old$[rsp]
  00071	48 03 c8	 add	 rcx, rax
  00074	48 8b c1	 mov	 rax, rcx
  00077	48 89 44 24 08	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 77   :     return _Left < _Right ? _Right : _Left;

  0007c	48 8b 44 24 08	 mov	 rax, QWORD PTR $T1[rsp]
  00081	48 39 04 24	 cmp	 QWORD PTR _Masked$[rsp], rax
  00085	73 0c		 jae	 SHORT $LN8@Calculate_
  00087	48 8d 44 24 08	 lea	 rax, QWORD PTR $T1[rsp]
  0008c	48 89 44 24 10	 mov	 QWORD PTR tv75[rsp], rax
  00091	eb 09		 jmp	 SHORT $LN9@Calculate_
$LN8@Calculate_:
  00093	48 8d 04 24	 lea	 rax, QWORD PTR _Masked$[rsp]
  00097	48 89 44 24 10	 mov	 QWORD PTR tv75[rsp], rax
$LN9@Calculate_:
  0009c	48 8b 44 24 10	 mov	 rax, QWORD PTR tv75[rsp]
  000a1	48 89 44 24 18	 mov	 QWORD PTR $T2[rsp], rax
  000a6	48 8b 44 24 18	 mov	 rax, QWORD PTR $T2[rsp]
  000ab	48 89 44 24 20	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2987 :         return (_STD max)(_Masked, _Old + _Old / 2);

  000b0	48 8b 44 24 20	 mov	 rax, QWORD PTR $T3[rsp]
  000b5	48 8b 00	 mov	 rax, QWORD PTR [rax]
$LN1@Calculate_:

; 2988 :     }

  000b8	48 83 c4 38	 add	 rsp, 56			; 00000038H
  000bc	c3		 ret	 0
?_Calculate_growth@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@CA_K_K00@Z ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Calculate_growth
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ
_TEXT	SEGMENT
$T1 = 0
_Alloc_max$ = 8
tv66 = 16
$T2 = 24
$T3 = 32
tv70 = 40
$T4 = 48
$T5 = 56
$T6 = 64
$T7 = 72
_Storage_max$ = 80
$T8 = 88
$T9 = 96
$T10 = 104
$T11 = 112
_Unsigned_max$12 = 120
this$ = 144
?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size, COMDAT

; 2377 :     _NODISCARD _CONSTEXPR20 size_type max_size() const noexcept {

$LN38:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H

; 3111 :         return _Mypair._Get_first();

  0000c	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1543 :         return *this;

  00014	48 89 44 24 30	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3111 :         return _Mypair._Get_first();

  00019	48 8b 44 24 30	 mov	 rax, QWORD PTR $T4[rsp]
  0001e	48 89 44 24 70	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 746  :         return static_cast<size_t>(-1) / sizeof(value_type);

  00023	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  0002d	48 89 44 24 38	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2378 :         const size_type _Alloc_max   = _Alty_traits::max_size(_Getal());

  00032	48 8b 44 24 38	 mov	 rax, QWORD PTR $T5[rsp]
  00037	48 89 44 24 08	 mov	 QWORD PTR _Alloc_max$[rsp], rax

; 2379 :         const size_type _Storage_max = // can always store small string

  0003c	48 c7 04 24 08
	00 00 00	 mov	 QWORD PTR $T1[rsp], 8
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 77   :     return _Left < _Right ? _Right : _Left;

  00044	48 8b 04 24	 mov	 rax, QWORD PTR $T1[rsp]
  00048	48 39 44 24 08	 cmp	 QWORD PTR _Alloc_max$[rsp], rax
  0004d	73 0b		 jae	 SHORT $LN21@max_size
  0004f	48 8d 04 24	 lea	 rax, QWORD PTR $T1[rsp]
  00053	48 89 44 24 10	 mov	 QWORD PTR tv66[rsp], rax
  00058	eb 0a		 jmp	 SHORT $LN22@max_size
$LN21@max_size:
  0005a	48 8d 44 24 08	 lea	 rax, QWORD PTR _Alloc_max$[rsp]
  0005f	48 89 44 24 10	 mov	 QWORD PTR tv66[rsp], rax
$LN22@max_size:
  00064	48 8b 44 24 10	 mov	 rax, QWORD PTR tv66[rsp]
  00069	48 89 44 24 40	 mov	 QWORD PTR $T6[rsp], rax
  0006e	48 8b 44 24 40	 mov	 rax, QWORD PTR $T6[rsp]
  00073	48 89 44 24 48	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2379 :         const size_type _Storage_max = // can always store small string

  00078	48 8b 44 24 48	 mov	 rax, QWORD PTR $T7[rsp]
  0007d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00080	48 89 44 24 50	 mov	 QWORD PTR _Storage_max$[rsp], rax

; 2381 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()),

  00085	48 8b 44 24 50	 mov	 rax, QWORD PTR _Storage_max$[rsp]
  0008a	48 ff c8	 dec	 rax
  0008d	48 89 44 24 18	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 866  :         constexpr auto _Unsigned_max = static_cast<make_unsigned_t<_Ty>>(-1);

  00092	48 c7 44 24 78
	ff ff ff ff	 mov	 QWORD PTR _Unsigned_max$12[rsp], -1

; 867  :         return static_cast<_Ty>(_Unsigned_max >> 1);

  0009b	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  000a5	48 89 44 24 58	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2381 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()),

  000aa	48 8b 44 24 58	 mov	 rax, QWORD PTR $T8[rsp]
  000af	48 89 44 24 20	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 101  :     return _Right < _Left ? _Right : _Left;

  000b4	48 8b 44 24 20	 mov	 rax, QWORD PTR $T3[rsp]
  000b9	48 39 44 24 18	 cmp	 QWORD PTR $T2[rsp], rax
  000be	73 0c		 jae	 SHORT $LN33@max_size
  000c0	48 8d 44 24 18	 lea	 rax, QWORD PTR $T2[rsp]
  000c5	48 89 44 24 28	 mov	 QWORD PTR tv70[rsp], rax
  000ca	eb 0a		 jmp	 SHORT $LN34@max_size
$LN33@max_size:
  000cc	48 8d 44 24 20	 lea	 rax, QWORD PTR $T3[rsp]
  000d1	48 89 44 24 28	 mov	 QWORD PTR tv70[rsp], rax
$LN34@max_size:
  000d6	48 8b 44 24 28	 mov	 rax, QWORD PTR tv70[rsp]
  000db	48 89 44 24 60	 mov	 QWORD PTR $T9[rsp], rax
  000e0	48 8b 44 24 60	 mov	 rax, QWORD PTR $T9[rsp]
  000e5	48 89 44 24 68	 mov	 QWORD PTR $T10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2381 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()),

  000ea	48 8b 44 24 68	 mov	 rax, QWORD PTR $T10[rsp]
  000ef	48 8b 00	 mov	 rax, QWORD PTR [rax]

; 2382 :             _Storage_max - 1 // -1 is for null terminator and/or npos
; 2383 :         );
; 2384 :     }

  000f2	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  000f9	c3		 ret	 0
?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?assign@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV12@QEB_W_K@Z
_TEXT	SEGMENT
$T1 = 32
$T2 = 34
_Old_ptr$3 = 40
this$ = 64
_Ptr$ = 72
_Count$ = 80
?assign@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV12@QEB_W_K@Z PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::assign, COMDAT

; 1616 :         _In_reads_(_Count) const _Elem* const _Ptr, _CRT_GUARDOVERFLOW const size_type _Count) {

$LN208:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	57		 push	 rdi
  00010	48 83 ec 30	 sub	 rsp, 48			; 00000030H

; 1617 :         // assign [_Ptr, _Ptr + _Count)
; 1618 :         if (_Count <= _Mypair._Myval2._Myres) {

  00014	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00019	48 8b 40 18	 mov	 rax, QWORD PTR [rax+24]
  0001d	48 39 44 24 50	 cmp	 QWORD PTR _Count$[rsp], rax
  00022	77 5c		 ja	 SHORT $LN2@assign

; 1619 :             _ASAN_STRING_REMOVE(*this);
; 1620 :             _Elem* const _Old_ptr   = _Mypair._Myval2._Myptr();

  00024	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00029	48 8b c8	 mov	 rcx, rax
  0002c	e8 00 00 00 00	 call	 ?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEAAPEA_WXZ ; std::_String_val<std::_Simple_types<wchar_t> >::_Myptr
  00031	48 89 44 24 28	 mov	 QWORD PTR _Old_ptr$3[rsp], rax

; 1621 :             _Mypair._Myval2._Mysize = _Count;

  00036	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0003b	48 8b 4c 24 50	 mov	 rcx, QWORD PTR _Count$[rsp]
  00040	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 174  :         _CSTD memmove(_First1, _First2, _Count * sizeof(_Elem));

  00044	48 8b 44 24 50	 mov	 rax, QWORD PTR _Count$[rsp]
  00049	48 03 c0	 add	 rax, rax
  0004c	4c 8b c0	 mov	 r8, rax
  0004f	48 8b 54 24 48	 mov	 rdx, QWORD PTR _Ptr$[rsp]
  00054	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Old_ptr$3[rsp]
  00059	e8 00 00 00 00	 call	 memmove
  0005e	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1623 :             _Traits::assign(_Old_ptr[_Count], _Elem());

  0005f	33 c0		 xor	 eax, eax
  00061	66 89 44 24 22	 mov	 WORD PTR $T2[rsp], ax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 347  :         _Left = _Right;

  00066	48 8b 44 24 28	 mov	 rax, QWORD PTR _Old_ptr$3[rsp]
  0006b	48 8b 4c 24 50	 mov	 rcx, QWORD PTR _Count$[rsp]
  00070	0f b7 54 24 22	 movzx	 edx, WORD PTR $T2[rsp]
  00075	66 89 14 48	 mov	 WORD PTR [rax+rcx*2], dx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1625 :             return *this;

  00079	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0007e	eb 2b		 jmp	 SHORT $LN1@assign
$LN2@assign:

; 1626 :         }
; 1627 : 
; 1628 :         return _Reallocate_for(

  00080	48 8d 44 24 20	 lea	 rax, QWORD PTR $T1[rsp]
  00085	48 8b f8	 mov	 rdi, rax
  00088	33 c0		 xor	 eax, eax
  0008a	b9 01 00 00 00	 mov	 ecx, 1
  0008f	f3 aa		 rep stosb
  00091	4c 8b 4c 24 48	 mov	 r9, QWORD PTR _Ptr$[rsp]
  00096	44 0f b6 44 24
	20		 movzx	 r8d, BYTE PTR $T1[rsp]
  0009c	48 8b 54 24 50	 mov	 rdx, QWORD PTR _Count$[rsp]
  000a1	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  000a6	e8 00 00 00 00	 call	 ??$_Reallocate_for@V<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>@@PEB_W@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Reallocate_for<<lambda_3fa8b2c8193a0f3144fc4b1b8f243931>,wchar_t const *>
$LN1@assign:

; 1629 :             _Count,
; 1630 :             [](_Elem* const _New_ptr, const size_type _Count, const _Elem* const _Ptr) _STATIC_LAMBDA {
; 1631 :                 _Traits::copy(_New_ptr, _Ptr, _Count);
; 1632 :                 _Traits::assign(_New_ptr[_Count], _Elem());
; 1633 :             },
; 1634 :             _Ptr);
; 1635 :     }

  000ab	48 83 c4 30	 add	 rsp, 48			; 00000030H
  000af	5f		 pop	 rdi
  000b0	c3		 ret	 0
?assign@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV12@QEB_W_K@Z ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::assign
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV01@AEBV01@@Z
_TEXT	SEGMENT
$T1 = 32
tv134 = 36
this$ = 40
_Result$2 = 48
$T3 = 56
$T4 = 64
$T5 = 72
$T6 = 80
$T7 = 88
_Ptr$ = 96
$T8 = 104
$T9 = 112
_Al$ = 120
_Right_al$ = 128
this$ = 160
_Right$ = 168
??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV01@AEBV01@@Z PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::operator=, COMDAT

; 1394 :     _CONSTEXPR20 basic_string& operator=(const basic_string& _Right) {

$LN259:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 81 ec 98 00
	00 00		 sub	 rsp, 152		; 00000098H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00011	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR _Right$[rsp]
  00019	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1395 :         if (this == _STD addressof(_Right)) {

  0001e	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
  00023	48 39 84 24 a0
	00 00 00	 cmp	 QWORD PTR this$[rsp], rax
  0002b	75 0d		 jne	 SHORT $LN2@operator

; 1396 :             return *this;

  0002d	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00035	e9 e3 00 00 00	 jmp	 $LN1@operator
$LN2@operator:

; 3107 :         return _Mypair._Get_first();

  0003a	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00042	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3107 :         return _Mypair._Get_first();

  00047	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  0004c	48 89 44 24 48	 mov	 QWORD PTR $T5[rsp], rax

; 1397 :         }
; 1398 : 
; 1399 :         auto& _Al             = _Getal();

  00051	48 8b 44 24 48	 mov	 rax, QWORD PTR $T5[rsp]
  00056	48 89 44 24 78	 mov	 QWORD PTR _Al$[rsp], rax

; 3111 :         return _Mypair._Get_first();

  0005b	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR _Right$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1543 :         return *this;

  00063	48 89 44 24 50	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3111 :         return _Mypair._Get_first();

  00068	48 8b 44 24 50	 mov	 rax, QWORD PTR $T6[rsp]
  0006d	48 89 44 24 58	 mov	 QWORD PTR $T7[rsp], rax

; 1400 :         const auto& _Right_al = _Right._Getal();

  00072	48 8b 44 24 58	 mov	 rax, QWORD PTR $T7[rsp]
  00077	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _Right_al$[rsp], rax

; 1401 :         if constexpr (_Choose_pocca_v<_Alty>) {
; 1402 :             if (_Al != _Right_al) {
; 1403 :                 auto&& _Alproxy       = _GET_PROXY_ALLOCATOR(_Alty, _Al);
; 1404 :                 auto&& _Right_alproxy = _GET_PROXY_ALLOCATOR(_Alty, _Right_al);
; 1405 :                 _Container_proxy_ptr<_Alty> _New_proxy(_Right_alproxy, _Leave_proxy_unbound{}); // throws
; 1406 : 
; 1407 :                 const size_type _Right_size   = _Right._Mypair._Myval2._Mysize;
; 1408 :                 const _Elem* const _Right_ptr = _Right._Mypair._Myval2._Myptr();
; 1409 :                 if (_Right_size > _Small_string_capacity) {
; 1410 :                     size_type _New_capacity = _Calculate_growth(_Right_size, _Small_string_capacity, _Right.max_size());
; 1411 :                     auto _Right_al_non_const = _Right_al;
; 1412 :                     const pointer _New_ptr   = _Allocate_for_capacity(_Right_al_non_const, _New_capacity); // throws
; 1413 :                     _Traits::copy(_Unfancy(_New_ptr), _Right_ptr, _Right_size + 1);
; 1414 : 
; 1415 :                     _Tidy_deallocate();
; 1416 :                     _Construct_in_place(_Mypair._Myval2._Bx._Ptr, _New_ptr);
; 1417 :                     _Mypair._Myval2._Mysize = _Right_size;
; 1418 :                     _Mypair._Myval2._Myres  = _New_capacity;
; 1419 :                     _ASAN_STRING_CREATE(*this);
; 1420 :                 } else {
; 1421 :                     _Tidy_deallocate();
; 1422 :                     _Traits::copy(_Mypair._Myval2._Bx._Buf, _Right_ptr, _Right_size + 1);
; 1423 :                     _Mypair._Myval2._Mysize = _Right_size;
; 1424 :                     _Mypair._Myval2._Myres  = _Small_string_capacity;
; 1425 :                 }
; 1426 : 
; 1427 :                 _Pocca(_Al, _Right_al);
; 1428 :                 _New_proxy._Bind(_Alproxy, _STD addressof(_Mypair._Myval2));
; 1429 :                 return *this;
; 1430 :             }
; 1431 :         }
; 1432 : 
; 1433 :         _Pocca(_Al, _Right_al);
; 1434 :         assign(_Right._Mypair._Myval2._Myptr(), _Right._Mypair._Myval2._Mysize);

  0007f	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR _Right$[rsp]
  00087	48 89 44 24 28	 mov	 QWORD PTR this$[rsp], rax

; 444  :         const value_type* _Result = _Bx._Buf;

  0008c	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00091	48 89 44 24 30	 mov	 QWORD PTR _Result$2[rsp], rax

; 453  :         return _Myres > _Small_string_capacity;

  00096	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  0009b	48 83 78 18 07	 cmp	 QWORD PTR [rax+24], 7
  000a0	76 0a		 jbe	 SHORT $LN42@operator
  000a2	c7 44 24 24 01
	00 00 00	 mov	 DWORD PTR tv134[rsp], 1
  000aa	eb 08		 jmp	 SHORT $LN43@operator
$LN42@operator:
  000ac	c7 44 24 24 00
	00 00 00	 mov	 DWORD PTR tv134[rsp], 0
$LN43@operator:
  000b4	0f b6 44 24 24	 movzx	 eax, BYTE PTR tv134[rsp]
  000b9	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al

; 445  :         if (_Large_mode_engaged()) {

  000bd	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  000c2	0f b6 c0	 movzx	 eax, al
  000c5	85 c0		 test	 eax, eax
  000c7	74 21		 je	 SHORT $LN35@operator

; 446  :             _Result = _Unfancy(_Bx._Ptr);

  000c9	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  000ce	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000d1	48 89 44 24 60	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  000d6	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  000db	48 89 44 24 68	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 446  :             _Result = _Unfancy(_Bx._Ptr);

  000e0	48 8b 44 24 68	 mov	 rax, QWORD PTR $T8[rsp]
  000e5	48 89 44 24 30	 mov	 QWORD PTR _Result$2[rsp], rax
$LN35@operator:

; 447  :         }
; 448  : 
; 449  :         return _Result;

  000ea	48 8b 44 24 30	 mov	 rax, QWORD PTR _Result$2[rsp]
  000ef	48 89 44 24 70	 mov	 QWORD PTR $T9[rsp], rax

; 1401 :         if constexpr (_Choose_pocca_v<_Alty>) {
; 1402 :             if (_Al != _Right_al) {
; 1403 :                 auto&& _Alproxy       = _GET_PROXY_ALLOCATOR(_Alty, _Al);
; 1404 :                 auto&& _Right_alproxy = _GET_PROXY_ALLOCATOR(_Alty, _Right_al);
; 1405 :                 _Container_proxy_ptr<_Alty> _New_proxy(_Right_alproxy, _Leave_proxy_unbound{}); // throws
; 1406 : 
; 1407 :                 const size_type _Right_size   = _Right._Mypair._Myval2._Mysize;
; 1408 :                 const _Elem* const _Right_ptr = _Right._Mypair._Myval2._Myptr();
; 1409 :                 if (_Right_size > _Small_string_capacity) {
; 1410 :                     size_type _New_capacity = _Calculate_growth(_Right_size, _Small_string_capacity, _Right.max_size());
; 1411 :                     auto _Right_al_non_const = _Right_al;
; 1412 :                     const pointer _New_ptr   = _Allocate_for_capacity(_Right_al_non_const, _New_capacity); // throws
; 1413 :                     _Traits::copy(_Unfancy(_New_ptr), _Right_ptr, _Right_size + 1);
; 1414 : 
; 1415 :                     _Tidy_deallocate();
; 1416 :                     _Construct_in_place(_Mypair._Myval2._Bx._Ptr, _New_ptr);
; 1417 :                     _Mypair._Myval2._Mysize = _Right_size;
; 1418 :                     _Mypair._Myval2._Myres  = _New_capacity;
; 1419 :                     _ASAN_STRING_CREATE(*this);
; 1420 :                 } else {
; 1421 :                     _Tidy_deallocate();
; 1422 :                     _Traits::copy(_Mypair._Myval2._Bx._Buf, _Right_ptr, _Right_size + 1);
; 1423 :                     _Mypair._Myval2._Mysize = _Right_size;
; 1424 :                     _Mypair._Myval2._Myres  = _Small_string_capacity;
; 1425 :                 }
; 1426 : 
; 1427 :                 _Pocca(_Al, _Right_al);
; 1428 :                 _New_proxy._Bind(_Alproxy, _STD addressof(_Mypair._Myval2));
; 1429 :                 return *this;
; 1430 :             }
; 1431 :         }
; 1432 : 
; 1433 :         _Pocca(_Al, _Right_al);
; 1434 :         assign(_Right._Mypair._Myval2._Myptr(), _Right._Mypair._Myval2._Mysize);

  000f4	48 8b 44 24 70	 mov	 rax, QWORD PTR $T9[rsp]
  000f9	48 8b 8c 24 a8
	00 00 00	 mov	 rcx, QWORD PTR _Right$[rsp]
  00101	4c 8b 41 10	 mov	 r8, QWORD PTR [rcx+16]
  00105	48 8b d0	 mov	 rdx, rax
  00108	48 8b 8c 24 a0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00110	e8 00 00 00 00	 call	 ?assign@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV12@QEB_W_K@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::assign

; 1435 :         return *this;

  00115	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
$LN1@operator:

; 1436 :     }

  0011d	48 81 c4 98 00
	00 00		 add	 rsp, 152		; 00000098H
  00124	c3		 ret	 0
??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV01@AEBV01@@Z ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::operator=
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEAAPEA_WXZ
_TEXT	SEGMENT
$T1 = 0
tv76 = 4
_Result$ = 8
_Ptr$ = 16
$T2 = 24
this$ = 48
?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEAAPEA_WXZ PROC ; std::_String_val<std::_Simple_types<wchar_t> >::_Myptr, COMDAT

; 434  :     _NODISCARD _CONSTEXPR20 value_type* _Myptr() noexcept {

$LN17:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 435  :         value_type* _Result = _Bx._Buf;

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 89 44 24 08	 mov	 QWORD PTR _Result$[rsp], rax

; 453  :         return _Myres > _Small_string_capacity;

  00013	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00018	48 83 78 18 07	 cmp	 QWORD PTR [rax+24], 7
  0001d	76 0a		 jbe	 SHORT $LN7@Myptr
  0001f	c7 44 24 04 01
	00 00 00	 mov	 DWORD PTR tv76[rsp], 1
  00027	eb 08		 jmp	 SHORT $LN8@Myptr
$LN7@Myptr:
  00029	c7 44 24 04 00
	00 00 00	 mov	 DWORD PTR tv76[rsp], 0
$LN8@Myptr:
  00031	0f b6 44 24 04	 movzx	 eax, BYTE PTR tv76[rsp]
  00036	88 04 24	 mov	 BYTE PTR $T1[rsp], al

; 436  :         if (_Large_mode_engaged()) {

  00039	0f b6 04 24	 movzx	 eax, BYTE PTR $T1[rsp]
  0003d	0f b6 c0	 movzx	 eax, al
  00040	85 c0		 test	 eax, eax
  00042	74 21		 je	 SHORT $LN2@Myptr

; 437  :             _Result = _Unfancy(_Bx._Ptr);

  00044	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00049	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0004c	48 89 44 24 10	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  00051	48 8b 44 24 10	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00056	48 89 44 24 18	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 437  :             _Result = _Unfancy(_Bx._Ptr);

  0005b	48 8b 44 24 18	 mov	 rax, QWORD PTR $T2[rsp]
  00060	48 89 44 24 08	 mov	 QWORD PTR _Result$[rsp], rax
$LN2@Myptr:

; 438  :         }
; 439  : 
; 440  :         return _Result;

  00065	48 8b 44 24 08	 mov	 rax, QWORD PTR _Result$[rsp]

; 441  :     }

  0006a	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0006e	c3		 ret	 0
?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEAAPEA_WXZ ENDP ; std::_String_val<std::_Simple_types<wchar_t> >::_Myptr
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z
_TEXT	SEGMENT
_Overflow_is_possible$1 = 32
_Bytes$ = 40
$T2 = 48
$T3 = 56
$T4 = 64
_Max_possible$5 = 72
this$ = 96
_Count$ = 104
?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z PROC	; std::allocator<wchar_t>::allocate, COMDAT

; 988  :     _NODISCARD_RAW_PTR_ALLOC _CONSTEXPR20 __declspec(allocator) _Ty* allocate(_CRT_GUARDOVERFLOW const size_t _Count) {

$LN13:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 113  :     constexpr bool _Overflow_is_possible = _Ty_size > 1;

  0000e	c6 44 24 20 01	 mov	 BYTE PTR _Overflow_is_possible$1[rsp], 1

; 114  : 
; 115  :     if constexpr (_Overflow_is_possible) {
; 116  :         constexpr size_t _Max_possible = static_cast<size_t>(-1) / _Ty_size;

  00013	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  0001d	48 89 44 24 48	 mov	 QWORD PTR _Max_possible$5[rsp], rax

; 117  :         if (_Count > _Max_possible) {

  00022	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  0002c	48 39 44 24 68	 cmp	 QWORD PTR _Count$[rsp], rax
  00031	76 06		 jbe	 SHORT $LN4@allocate

; 118  :             _Throw_bad_array_new_length(); // multiply overflow

  00033	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00038	90		 npad	 1
$LN4@allocate:

; 119  :         }
; 120  :     }
; 121  : 
; 122  :     return _Count * _Ty_size;

  00039	48 8b 44 24 68	 mov	 rax, QWORD PTR _Count$[rsp]
  0003e	48 d1 e0	 shl	 rax, 1
  00041	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00046	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
  0004b	48 89 44 24 28	 mov	 QWORD PTR _Bytes$[rsp], rax

; 227  :     if (_Bytes == 0) {

  00050	48 83 7c 24 28
	00		 cmp	 QWORD PTR _Bytes$[rsp], 0
  00056	75 0b		 jne	 SHORT $LN8@allocate

; 228  :         return nullptr;

  00058	48 c7 44 24 30
	00 00 00 00	 mov	 QWORD PTR $T2[rsp], 0
  00061	eb 35		 jmp	 SHORT $LN7@allocate
$LN8@allocate:

; 229  :     }
; 230  : 
; 231  : #if _HAS_CXX20 // TRANSITION, GH-1532
; 232  :     if (_STD is_constant_evaluated()) {
; 233  :         return _Traits::_Allocate(_Bytes);
; 234  :     }
; 235  : #endif // _HAS_CXX20
; 236  : 
; 237  : #ifdef __cpp_aligned_new
; 238  :     if constexpr (_Align > __STDCPP_DEFAULT_NEW_ALIGNMENT__) {
; 239  :         size_t _Passed_align = _Align;
; 240  : #if defined(_M_IX86) || defined(_M_X64)
; 241  :         if (_Bytes >= _Big_allocation_threshold) {
; 242  :             // boost the alignment of big allocations to help autovectorization
; 243  :             _Passed_align = (_STD max)(_Align, _Big_allocation_alignment);
; 244  :         }
; 245  : #endif // defined(_M_IX86) || defined(_M_X64)
; 246  :         return _Traits::_Allocate_aligned(_Bytes, _Passed_align);
; 247  :     } else
; 248  : #endif // defined(__cpp_aligned_new)
; 249  :     {
; 250  : #if defined(_M_IX86) || defined(_M_X64)
; 251  :         if (_Bytes >= _Big_allocation_threshold) {

  00063	48 81 7c 24 28
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  0006c	72 11		 jb	 SHORT $LN9@allocate

; 252  :             // boost the alignment of big allocations to help autovectorization
; 253  :             return _Allocate_manually_vector_aligned<_Traits>(_Bytes);

  0006e	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00073	e8 00 00 00 00	 call	 ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
  00078	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
  0007d	eb 19		 jmp	 SHORT $LN7@allocate
$LN9@allocate:

; 136  :         return ::operator new(_Bytes);

  0007f	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00084	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00089	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax

; 256  :         return _Traits::_Allocate(_Bytes);

  0008e	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  00093	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
$LN7@allocate:

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00098	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
$LN6@allocate:

; 991  :     }

  0009d	48 83 c4 58	 add	 rsp, 88			; 00000058H
  000a1	c3		 ret	 0
?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z ENDP	; std::allocator<wchar_t>::allocate
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Xlen_string@std@@YAXXZ
_TEXT	SEGMENT
?_Xlen_string@std@@YAXXZ PROC				; std::_Xlen_string, COMDAT

; 530  : [[noreturn]] inline void _Xlen_string() {

$LN3:
  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 531  :     _Xlength_error("string too long");

  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_0BA@JFNIOLAK@string?5too?5long@
  0000b	e8 00 00 00 00	 call	 ?_Xlength_error@std@@YAXPEBD@Z ; std::_Xlength_error
  00010	90		 npad	 1
$LN2@Xlen_strin:

; 532  : }

  00011	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00015	c3		 ret	 0
?_Xlen_string@std@@YAXXZ ENDP				; std::_Xlen_string
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z
_TEXT	SEGMENT
_Back_shift$ = 48
_Ptr_container$ = 56
_Ptr_user$ = 64
_Min_back_shift$ = 72
_Ptr$ = 96
_Bytes$ = 104
?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z PROC ; std::_Adjust_manually_vector_aligned, COMDAT

; 200  : inline void _Adjust_manually_vector_aligned(void*& _Ptr, size_t& _Bytes) {

$LN5:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 201  :     // adjust parameters from _Allocate_manually_vector_aligned to pass to operator delete
; 202  :     _Bytes += _Non_user_size;

  0000e	48 8b 44 24 68	 mov	 rax, QWORD PTR _Bytes$[rsp]
  00013	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00016	48 83 c0 27	 add	 rax, 39			; 00000027H
  0001a	48 8b 4c 24 68	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  0001f	48 89 01	 mov	 QWORD PTR [rcx], rax

; 203  : 
; 204  :     const uintptr_t* const _Ptr_user = static_cast<uintptr_t*>(_Ptr);

  00022	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00027	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0002a	48 89 44 24 40	 mov	 QWORD PTR _Ptr_user$[rsp], rax

; 205  :     const uintptr_t _Ptr_container   = _Ptr_user[-1];

  0002f	b8 08 00 00 00	 mov	 eax, 8
  00034	48 6b c0 ff	 imul	 rax, rax, -1
  00038	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr_user$[rsp]
  0003d	48 8b 04 01	 mov	 rax, QWORD PTR [rcx+rax]
  00041	48 89 44 24 38	 mov	 QWORD PTR _Ptr_container$[rsp], rax

; 206  : 
; 207  :     // If the following asserts, it likely means that we are performing
; 208  :     // an aligned delete on memory coming from an unaligned allocation.
; 209  :     _STL_ASSERT(_Ptr_user[-2] == _Big_allocation_sentinel, "invalid argument");
; 210  : 
; 211  :     // Extra paranoia on aligned allocation/deallocation; ensure _Ptr_container is
; 212  :     // in range [_Min_back_shift, _Non_user_size]
; 213  : #ifdef _DEBUG
; 214  :     constexpr uintptr_t _Min_back_shift = 2 * sizeof(void*);
; 215  : #else // ^^^ defined(_DEBUG) / !defined(_DEBUG) vvv
; 216  :     constexpr uintptr_t _Min_back_shift = sizeof(void*);

  00046	48 c7 44 24 48
	08 00 00 00	 mov	 QWORD PTR _Min_back_shift$[rsp], 8

; 217  : #endif // ^^^ !defined(_DEBUG) ^^^
; 218  :     const uintptr_t _Back_shift = reinterpret_cast<uintptr_t>(_Ptr) - _Ptr_container;

  0004f	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00054	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr_container$[rsp]
  00059	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0005c	48 2b c1	 sub	 rax, rcx
  0005f	48 89 44 24 30	 mov	 QWORD PTR _Back_shift$[rsp], rax

; 219  :     _STL_VERIFY(_Back_shift >= _Min_back_shift && _Back_shift <= _Non_user_size, "invalid argument");

  00064	48 83 7c 24 30
	08		 cmp	 QWORD PTR _Back_shift$[rsp], 8
  0006a	72 08		 jb	 SHORT $LN3@Adjust_man
  0006c	48 83 7c 24 30
	27		 cmp	 QWORD PTR _Back_shift$[rsp], 39 ; 00000027H
  00072	76 19		 jbe	 SHORT $LN2@Adjust_man
$LN3@Adjust_man:
  00074	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR [rsp+32], 0
  0007d	45 33 c9	 xor	 r9d, r9d
  00080	45 33 c0	 xor	 r8d, r8d
  00083	33 d2		 xor	 edx, edx
  00085	33 c9		 xor	 ecx, ecx
  00087	e8 00 00 00 00	 call	 _invoke_watson
  0008c	90		 npad	 1
$LN2@Adjust_man:

; 220  :     _Ptr = reinterpret_cast<void*>(_Ptr_container);

  0008d	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00092	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr_container$[rsp]
  00097	48 89 08	 mov	 QWORD PTR [rax], rcx
$LN4@Adjust_man:

; 221  : }

  0009a	48 83 c4 58	 add	 rsp, 88			; 00000058H
  0009e	c3		 ret	 0
?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ENDP ; std::_Adjust_manually_vector_aligned
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?_Throw_bad_array_new_length@std@@YAXXZ
_TEXT	SEGMENT
$T1 = 32
?_Throw_bad_array_new_length@std@@YAXXZ PROC		; std::_Throw_bad_array_new_length, COMDAT

; 107  : [[noreturn]] inline void _Throw_bad_array_new_length() {

$LN3:
  00000	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 108  :     _THROW(bad_array_new_length{});

  00004	48 8d 4c 24 20	 lea	 rcx, QWORD PTR $T1[rsp]
  00009	e8 00 00 00 00	 call	 ??0bad_array_new_length@std@@QEAA@XZ ; std::bad_array_new_length::bad_array_new_length
  0000e	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:_TI3?AVbad_array_new_length@std@@
  00015	48 8d 4c 24 20	 lea	 rcx, QWORD PTR $T1[rsp]
  0001a	e8 00 00 00 00	 call	 _CxxThrowException
  0001f	90		 npad	 1
$LN2@Throw_bad_:

; 109  : }

  00020	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00024	c3		 ret	 0
?_Throw_bad_array_new_length@std@@YAXXZ ENDP		; std::_Throw_bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gbad_array_new_length@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gbad_array_new_length@std@@UEAAPEAXI@Z PROC		; std::bad_array_new_length::`scalar deleting destructor', COMDAT
$LN20:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gbad_array_new_length@std@@UEAAPEAXI@Z ENDP		; std::bad_array_new_length::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??0bad_array_new_length@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
__that$ = 56
??0bad_array_new_length@std@@QEAA@AEBV01@@Z PROC	; std::bad_array_new_length::bad_array_new_length, COMDAT
$LN14:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000e	48 8b 54 24 38	 mov	 rdx, QWORD PTR __that$[rsp]
  00013	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00018	e8 00 00 00 00	 call	 ??0bad_alloc@std@@QEAA@AEBV01@@Z
  0001d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00022	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_array_new_length@std@@6B@
  00029	48 89 08	 mov	 QWORD PTR [rax], rcx
  0002c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00031	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00035	c3		 ret	 0
??0bad_array_new_length@std@@QEAA@AEBV01@@Z ENDP	; std::bad_array_new_length::bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??1bad_array_new_length@std@@UEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1bad_array_new_length@std@@UEAA@XZ PROC		; std::bad_array_new_length::~bad_array_new_length, COMDAT
$LN14:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  00018	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 83 c0 08	 add	 rax, 8
  00021	48 8b c8	 mov	 rcx, rax
  00024	e8 00 00 00 00	 call	 __std_exception_destroy
  00029	90		 npad	 1
  0002a	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0002e	c3		 ret	 0
??1bad_array_new_length@std@@UEAA@XZ ENDP		; std::bad_array_new_length::~bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0bad_array_new_length@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 16
??0bad_array_new_length@std@@QEAA@XZ PROC		; std::bad_array_new_length::bad_array_new_length, COMDAT

; 144  :     {

$LN14:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi

; 67   :     {

  00006	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0000b	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00012	48 89 08	 mov	 QWORD PTR [rax], rcx

; 66   :         : _Data()

  00015	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 83 c0 08	 add	 rax, 8
  0001e	48 8b f8	 mov	 rdi, rax
  00021	33 c0		 xor	 eax, eax
  00023	b9 10 00 00 00	 mov	 ecx, 16
  00028	f3 aa		 rep stosb

; 68   :         _Data._What = _Message;

  0002a	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0002f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_0BF@KINCDENJ@bad?5array?5new?5length@
  00036	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 133  :     {

  0003a	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0003f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_alloc@std@@6B@
  00046	48 89 08	 mov	 QWORD PTR [rax], rcx

; 144  :     {

  00049	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_array_new_length@std@@6B@
  00055	48 89 08	 mov	 QWORD PTR [rax], rcx

; 145  :     }

  00058	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0005d	5f		 pop	 rdi
  0005e	c3		 ret	 0
??0bad_array_new_length@std@@QEAA@XZ ENDP		; std::bad_array_new_length::bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gbad_alloc@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gbad_alloc@std@@UEAAPEAXI@Z PROC			; std::bad_alloc::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gbad_alloc@std@@UEAAPEAXI@Z ENDP			; std::bad_alloc::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0bad_alloc@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
__that$ = 56
??0bad_alloc@std@@QEAA@AEBV01@@Z PROC			; std::bad_alloc::bad_alloc, COMDAT
$LN9:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 20	 sub	 rsp, 32			; 00000020H

; 73   :     {

  0000f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  0001b	48 89 08	 mov	 QWORD PTR [rax], rcx

; 72   :         : _Data()

  0001e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 83 c0 08	 add	 rax, 8
  00027	48 8b f8	 mov	 rdi, rax
  0002a	33 c0		 xor	 eax, eax
  0002c	b9 10 00 00 00	 mov	 ecx, 16
  00031	f3 aa		 rep stosb

; 74   :         __std_exception_copy(&_Other._Data, &_Data);

  00033	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 83 c0 08	 add	 rax, 8
  0003c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR __that$[rsp]
  00041	48 83 c1 08	 add	 rcx, 8
  00045	48 8b d0	 mov	 rdx, rax
  00048	e8 00 00 00 00	 call	 __std_exception_copy
  0004d	90		 npad	 1
  0004e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_alloc@std@@6B@
  0005a	48 89 08	 mov	 QWORD PTR [rax], rcx
  0005d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00062	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00066	5f		 pop	 rdi
  00067	c3		 ret	 0
??0bad_alloc@std@@QEAA@AEBV01@@Z ENDP			; std::bad_alloc::bad_alloc
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gexception@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gexception@std@@UEAAPEAXI@Z PROC			; std::exception::`scalar deleting destructor', COMDAT
$LN10:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gexception@std@@UEAAPEAXI@Z ENDP			; std::exception::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ?what@exception@std@@UEBAPEBDXZ
_TEXT	SEGMENT
tv69 = 0
this$ = 32
?what@exception@std@@UEBAPEBDXZ PROC			; std::exception::what, COMDAT

; 95   :     {

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 18	 sub	 rsp, 24

; 96   :         return _Data._What ? _Data._What : "Unknown exception";

  00009	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00013	74 0f		 je	 SHORT $LN3@what
  00015	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0001e	48 89 04 24	 mov	 QWORD PTR tv69[rsp], rax
  00022	eb 0b		 jmp	 SHORT $LN4@what
$LN3@what:
  00024	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BC@EOODALEL@Unknown?5exception@
  0002b	48 89 04 24	 mov	 QWORD PTR tv69[rsp], rax
$LN4@what:
  0002f	48 8b 04 24	 mov	 rax, QWORD PTR tv69[rsp]

; 97   :     }

  00033	48 83 c4 18	 add	 rsp, 24
  00037	c3		 ret	 0
?what@exception@std@@UEBAPEBDXZ ENDP			; std::exception::what
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0exception@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
_Other$ = 56
??0exception@std@@QEAA@AEBV01@@Z PROC			; std::exception::exception, COMDAT

; 73   :     {

$LN4:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  0000f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  0001b	48 89 08	 mov	 QWORD PTR [rax], rcx

; 72   :         : _Data()

  0001e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 83 c0 08	 add	 rax, 8
  00027	48 8b f8	 mov	 rdi, rax
  0002a	33 c0		 xor	 eax, eax
  0002c	b9 10 00 00 00	 mov	 ecx, 16
  00031	f3 aa		 rep stosb

; 74   :         __std_exception_copy(&_Other._Data, &_Data);

  00033	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 83 c0 08	 add	 rax, 8
  0003c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Other$[rsp]
  00041	48 83 c1 08	 add	 rcx, 8
  00045	48 8b d0	 mov	 rdx, rax
  00048	e8 00 00 00 00	 call	 __std_exception_copy
  0004d	90		 npad	 1

; 75   :     }

  0004e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00057	5f		 pop	 rdi
  00058	c3		 ret	 0
??0exception@std@@QEAA@AEBV01@@Z ENDP			; std::exception::exception
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WShop\TaskItemTranscation.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
