﻿#pragma once


#include <string>
#include <list>

namespace mu2
{
	class RedisPublish;
	class RedisSubscript;
	class RedistMessage;

	namespace RedisPacket
	{
		const char Tail[] = "\r\n";
		const Int32 TailSize = 2;

		const char Starter = '*';
		const char argFilter = '$';
		const Int32 StarterSize = 1;
		const Int32 argFileterSize = 1;

		const UInt32 HeaderSize = 2;
	}
	

	class RedisParser
	{
	public:
		RedisParser(void * pTr, Int32 pTrSz);
		~RedisParser() {}

		Bool Parse();
		
		void SetFailed() { m_failed = true;  }
		Bool IsFailed()		{ return m_failed; }

		UInt32 GetParseSize()	{ return m_parseSz; }
		void * GetData()		{ return m_curPos; }

		Bool SkipToStart(char * pos, const char * end, Int32 &parseLength);
		Int32 ParseArgument(char * pos, const char * end);

		Int32 GetLength(char * pos, const char * end, Int32 &length);
		Int32 GetString(char * pos, const char * end, Int32 strLength, std::string &str);

		void GetCommands(std::vector<std::string> &out)
		{
			out = std::move(m_arguments);
		}

		Bool Move(Int32 length);

	protected:
		char *				m_curPos;
		Int32				m_packetSz;

		Bool				m_failed = false;
		Int32				m_parseSz = 0;

		std::vector<std::string> m_arguments;
	};


	inline RedisParser::RedisParser(void * pTr, Int32 pTrSz)
		: m_curPos(static_cast<char *>(pTr)), m_packetSz(pTrSz)
	{}

	inline Bool RedisParser::Parse()
	{
		m_arguments.clear();

		char * end = m_curPos + m_packetSz;
		Int32 parseLength = 0;
		if (false == SkipToStart(m_curPos, end, parseLength))
		{
			m_parseSz = parseLength;
			return true;		// 데이터 pop 해야 한다.
		}

		char * pos = m_curPos + parseLength;

		Int32 argCount = 0;
		Int32 readBytes = GetLength(pos, end, argCount);
		if (readBytes == 0)
		{
			return false;		// 데이터 pop 을 하면 안됨.
		}
		if (argCount == 0)
		{
			return true;		// 데이터 pop
		}

		parseLength += readBytes;
		pos += readBytes;

		Int32 parseArg = 0;
		while (parseArg < argCount)
		{
			readBytes = ParseArgument(pos, end);
			if (readBytes > 0)
			{
				pos += readBytes;
				parseLength += readBytes;
				++parseArg;
			}
			else
				break;
		}

		if (parseArg == argCount)
		{
			m_parseSz = parseLength;
			return true;
		}

		return false;

	}

	inline Int32 RedisParser::ParseArgument(char * pos, const char * end)
	{
		enum
		{
			NONE,
			LENGTH,
			STRING,
			DBNUMBER

		};

		Int32 parseLength = 0;
		Int32 strLength = 0;
		Int32 readLength = 0;
		std::string str;

		while (pos < end)
		{
			switch (*pos)
			{
			case '$':
				++parseLength;
				++pos;
				readLength = GetLength(pos, end, strLength);
				if (readLength > 0)
				{
					pos += readLength;
					parseLength += readLength;

					readLength = GetString(pos, end, strLength, str);
					if (readLength > 0)
					{
						parseLength += readLength;

						return parseLength;
					}
				}
				break;
			case ':':
				++parseLength;
				++pos;
				readLength = GetLength(pos, end, strLength);
				if (readLength > 0)
				{
					return readLength;
				}
				break;
			}
		}
		return 0;
	}

	inline Int32 RedisParser::GetLength(char * pos, const char * end, Int32 &length)
	{
		Int32 ret = 0;
		if (pos + 1 < end)
		{
			length = atoi(pos);

			while (pos < end)
			{
				if (*pos == '\n')
					return ret + 1;

				++ret;
				++pos;
			}

		}
		return 0;
	}

	inline Int32 RedisParser::GetString(char * pos, const char * end, Int32 strLength, std::string &str)
	{
		if (strLength > 256)
			return 0;

		char buf[256] = { 0, };
		if (pos + strLength + 2 <= end)
		{
			memcpy(&buf[0], pos, strLength);
			str = buf;
			m_arguments.push_back(str);
			return strLength + 2;
		}

		return 0;
		
	}

	inline Bool RedisParser::SkipToStart(char * pos, const char * end, Int32 &parseLength)
	{
		parseLength = 0;
		while (pos < end)
		{
			if (*pos == '*')
			{
				++parseLength;
				break;
			}

			if (*pos == '-')
			{
				//! \n\r 이 나올때까지 가야 한다. 아니면, false
				while (pos < end)
				{
					if (*pos == '\n')
					{
						++parseLength;
						return false;
					}

					++parseLength;
					++pos;
				}
			}

			++parseLength;
			++pos;
		}

		if (pos == end)
			return false;

		return true;
	}


	inline Bool RedisParser::Move(Int32 length)
	{
		if (length <= m_parseSz)
		{
			m_curPos += m_parseSz;
			m_packetSz -= m_parseSz;
		}
		else
		{
			m_curPos += m_parseSz;
			m_packetSz = 0;
		}

		return (m_packetSz == 0);

	}


	class RedisEventPacket : public RedisParser
	{
	public:

		RedisEventPacket(void * pTr, const Int32 pTrSz)
			: RedisParser(pTr, pTrSz)
		{
		}

	private:
		UInt32				m_argCount = 0;		// 인자 개수
		
	};
}
