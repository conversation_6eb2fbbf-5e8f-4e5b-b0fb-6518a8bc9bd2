; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	??0BurningStaminaInfo@mu2@@QEAA@XZ		; mu2::BurningStaminaInfo::BurningStaminaInfo
PUBLIC	??1BurningStaminaInfo@mu2@@QEAA@XZ		; mu2::BurningStaminaInfo::~BurningStaminaInfo
PUBLIC	?Initialize@BurningStaminaInfo@mu2@@QEAAXAEBUDBBurningStaminaInfo@2@@Z ; mu2::BurningStaminaInfo::Initialize
PUBLIC	?SetBurning@BurningStaminaInfo@mu2@@QEAAXPEAVEntityPlayer@2@I@Z ; mu2::BurningStaminaInfo::SetBurning
PUBLIC	?ResetBurning@BurningStaminaInfo@mu2@@QEAAXXZ	; mu2::BurningStaminaInfo::ResetBurning
PUBLIC	?IsBurning@BurningStaminaInfo@mu2@@QEBA_NXZ	; mu2::BurningStaminaInfo::IsBurning
PUBLIC	?FillUp@BurningStaminaInfo@mu2@@QEBAXAEAUBurningStaminaContinentSimepleInfo@2@@Z ; mu2::BurningStaminaInfo::FillUp
PUBLIC	?FillUp@BurningStaminaInfo@mu2@@QEBAXAEAUBurningStaminaBurningInfo@2@@Z ; mu2::BurningStaminaInfo::FillUp
PUBLIC	?FillUp@BurningStaminaInfo@mu2@@QEBAXAEAUBurningStaminaInstanceDungeonInfo@2@@Z ; mu2::BurningStaminaInfo::FillUp
PUBLIC	?FillUp@BurningStaminaInfo@mu2@@QEBAXAEAUBurningStaminaZoneServerInfo@2@@Z ; mu2::BurningStaminaInfo::FillUp
PUBLIC	?FillUp@BurningStaminaInfo@mu2@@QEBAXAEAUDBBurningStaminaInfo@2@@Z ; mu2::BurningStaminaInfo::FillUp
PUBLIC	??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@ ; `string'
PUBLIC	??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@		; `string'
PUBLIC	??_C@_06BALNJMNP@player@			; `string'
PUBLIC	??_C@_0FB@GNJAOHNP@F?3?2Release_Branch?2Server?2Develo@ ; `string'
PUBLIC	??_C@_0CE@MMBFKALH@mu2?3?3BurningStaminaInfo?3?3SetBur@ ; `string'
EXTRN	__imp_GetStdHandle:PROC
EXTRN	__imp_SetConsoleTextAttribute:PROC
EXTRN	?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ:PROC	; mu2::Logger::Logging
EXTRN	?GetCharId@EntityPlayer@mu2@@QEBAIXZ:PROC	; mu2::EntityPlayer::GetCharId
EXTRN	?GetCharName@EntityPlayer@mu2@@QEBAAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ:PROC ; mu2::EntityPlayer::GetCharName
EXTRN	??0BurningStaminaTimeInfo@mu2@@QEAA@XZ:PROC	; mu2::BurningStaminaTimeInfo::BurningStaminaTimeInfo
EXTRN	??1BurningStaminaTimeInfo@mu2@@QEAA@XZ:PROC	; mu2::BurningStaminaTimeInfo::~BurningStaminaTimeInfo
EXTRN	?Initialize@BurningStaminaTimeInfo@mu2@@QEAAXAEBU_SYSTEMTIME@@@Z:PROC ; mu2::BurningStaminaTimeInfo::Initialize
EXTRN	?IsBurning@BurningStaminaTimeInfo@mu2@@QEBA_NXZ:PROC ; mu2::BurningStaminaTimeInfo::IsBurning
EXTRN	?SetBurning@BurningStaminaTimeInfo@mu2@@QEAAXI@Z:PROC ; mu2::BurningStaminaTimeInfo::SetBurning
EXTRN	?ResetBurning@BurningStaminaTimeInfo@mu2@@QEAAXXZ:PROC ; mu2::BurningStaminaTimeInfo::ResetBurning
EXTRN	?FillUp@BurningStaminaTimeInfo@mu2@@QEBAXAEAUBurningStaminaContinentSimepleInfo@2@@Z:PROC ; mu2::BurningStaminaTimeInfo::FillUp
EXTRN	?FillUp@BurningStaminaTimeInfo@mu2@@QEBAXAEAUBurningStaminaBurningInfo@2@@Z:PROC ; mu2::BurningStaminaTimeInfo::FillUp
EXTRN	?FillUp@BurningStaminaTimeInfo@mu2@@QEBAXAEAUDBBurningStaminaInfo@2@@Z:PROC ; mu2::BurningStaminaTimeInfo::FillUp
EXTRN	??0BurningStaminaUserInfo@mu2@@QEAA@XZ:PROC	; mu2::BurningStaminaUserInfo::BurningStaminaUserInfo
EXTRN	??1BurningStaminaUserInfo@mu2@@QEAA@XZ:PROC	; mu2::BurningStaminaUserInfo::~BurningStaminaUserInfo
EXTRN	?SetUserInfo@BurningStaminaUserInfo@mu2@@QEAAXIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z:PROC ; mu2::BurningStaminaUserInfo::SetUserInfo
EXTRN	?ResetUserInfo@BurningStaminaUserInfo@mu2@@QEAAXXZ:PROC ; mu2::BurningStaminaUserInfo::ResetUserInfo
EXTRN	?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUBurningStaminaContinentSimepleInfo@2@@Z:PROC ; mu2::BurningStaminaUserInfo::FillUp
EXTRN	?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUBurningStaminaBurningInfo@2@@Z:PROC ; mu2::BurningStaminaUserInfo::FillUp
EXTRN	?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUBurningStaminaInstanceDungeonInfo@2@@Z:PROC ; mu2::BurningStaminaUserInfo::FillUp
EXTRN	?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUBurningStaminaZoneServerInfo@2@@Z:PROC ; mu2::BurningStaminaUserInfo::FillUp
EXTRN	?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUDBBurningStaminaInfo@2@@Z:PROC ; mu2::BurningStaminaUserInfo::FillUp
EXTRN	__CxxFrameHandler4:PROC
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0BurningStaminaInfo@mu2@@QEAA@XZ DD imagerel $LN5
	DD	imagerel $LN5+51
	DD	imagerel $unwind$??0BurningStaminaInfo@mu2@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???0BurningStaminaInfo@mu2@@QEAA@XZ@4HA DD imagerel ?dtor$0@?0???0BurningStaminaInfo@mu2@@QEAA@XZ@4HA
	DD	imagerel ?dtor$0@?0???0BurningStaminaInfo@mu2@@QEAA@XZ@4HA+24
	DD	imagerel $unwind$?dtor$0@?0???0BurningStaminaInfo@mu2@@QEAA@XZ@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1BurningStaminaInfo@mu2@@QEAA@XZ DD imagerel $LN4
	DD	imagerel $LN4+45
	DD	imagerel $unwind$??1BurningStaminaInfo@mu2@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Initialize@BurningStaminaInfo@mu2@@QEAAXAEBUDBBurningStaminaInfo@2@@Z DD imagerel $LN4
	DD	imagerel $LN4+94
	DD	imagerel $unwind$?Initialize@BurningStaminaInfo@mu2@@QEAAXAEBUDBBurningStaminaInfo@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?SetBurning@BurningStaminaInfo@mu2@@QEAAXPEAVEntityPlayer@2@I@Z DD imagerel $LN5
	DD	imagerel $LN5+297
	DD	imagerel $unwind$?SetBurning@BurningStaminaInfo@mu2@@QEAAXPEAVEntityPlayer@2@I@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?ResetBurning@BurningStaminaInfo@mu2@@QEAAXXZ DD imagerel $LN3
	DD	imagerel $LN3+45
	DD	imagerel $unwind$?ResetBurning@BurningStaminaInfo@mu2@@QEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?IsBurning@BurningStaminaInfo@mu2@@QEBA_NXZ DD imagerel $LN3
	DD	imagerel $LN3+31
	DD	imagerel $unwind$?IsBurning@BurningStaminaInfo@mu2@@QEBA_NXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?FillUp@BurningStaminaInfo@mu2@@QEBAXAEAUBurningStaminaContinentSimepleInfo@2@@Z DD imagerel $LN3
	DD	imagerel $LN3+60
	DD	imagerel $unwind$?FillUp@BurningStaminaInfo@mu2@@QEBAXAEAUBurningStaminaContinentSimepleInfo@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?FillUp@BurningStaminaInfo@mu2@@QEBAXAEAUBurningStaminaBurningInfo@2@@Z DD imagerel $LN3
	DD	imagerel $LN3+60
	DD	imagerel $unwind$?FillUp@BurningStaminaInfo@mu2@@QEBAXAEAUBurningStaminaBurningInfo@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?FillUp@BurningStaminaInfo@mu2@@QEBAXAEAUBurningStaminaInstanceDungeonInfo@2@@Z DD imagerel $LN3
	DD	imagerel $LN3+38
	DD	imagerel $unwind$?FillUp@BurningStaminaInfo@mu2@@QEBAXAEAUBurningStaminaInstanceDungeonInfo@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?FillUp@BurningStaminaInfo@mu2@@QEBAXAEAUBurningStaminaZoneServerInfo@2@@Z DD imagerel $LN3
	DD	imagerel $LN3+38
	DD	imagerel $unwind$?FillUp@BurningStaminaInfo@mu2@@QEBAXAEAUBurningStaminaZoneServerInfo@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?FillUp@BurningStaminaInfo@mu2@@QEBAXAEAUDBBurningStaminaInfo@2@@Z DD imagerel $LN3
	DD	imagerel $LN3+60
	DD	imagerel $unwind$?FillUp@BurningStaminaInfo@mu2@@QEBAXAEAUDBBurningStaminaInfo@2@@Z
pdata	ENDS
;	COMDAT ??_C@_0CE@MMBFKALH@mu2?3?3BurningStaminaInfo?3?3SetBur@
CONST	SEGMENT
??_C@_0CE@MMBFKALH@mu2?3?3BurningStaminaInfo?3?3SetBur@ DB 'mu2::BurningS'
	DB	'taminaInfo::SetBurning', 00H		; `string'
CONST	ENDS
;	COMDAT ??_C@_0FB@GNJAOHNP@F?3?2Release_Branch?2Server?2Develo@
CONST	SEGMENT
??_C@_0FB@GNJAOHNP@F?3?2Release_Branch?2Server?2Develo@ DB 'F:\Release_Br'
	DB	'anch\Server\Development\Frontend\WorldServer\BurningStaminaIn'
	DB	'fo.cpp', 00H				; `string'
CONST	ENDS
;	COMDAT ??_C@_06BALNJMNP@player@
CONST	SEGMENT
??_C@_06BALNJMNP@player@ DB 'player', 00H		; `string'
CONST	ENDS
;	COMDAT ??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
CONST	SEGMENT
??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@ DB 'g', 00H, 'a', 00H, 'm', 00H, 'e'
	DB	00H, 00H, 00H				; `string'
CONST	ENDS
;	COMDAT ??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
CONST	SEGMENT
??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@ DB '%'
	DB	's> ASSERT - %s, %s(%d)', 00H		; `string'
CONST	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?FillUp@BurningStaminaInfo@mu2@@QEBAXAEAUDBBurningStaminaInfo@2@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?FillUp@BurningStaminaInfo@mu2@@QEBAXAEAUBurningStaminaZoneServerInfo@2@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?FillUp@BurningStaminaInfo@mu2@@QEBAXAEAUBurningStaminaInstanceDungeonInfo@2@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?FillUp@BurningStaminaInfo@mu2@@QEBAXAEAUBurningStaminaBurningInfo@2@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?FillUp@BurningStaminaInfo@mu2@@QEBAXAEAUBurningStaminaContinentSimepleInfo@2@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?IsBurning@BurningStaminaInfo@mu2@@QEBA_NXZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?ResetBurning@BurningStaminaInfo@mu2@@QEAAXXZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?SetBurning@BurningStaminaInfo@mu2@@QEAAXPEAVEntityPlayer@2@I@Z DD 011301H
	DD	0e213H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Initialize@BurningStaminaInfo@mu2@@QEAAXAEBUDBBurningStaminaInfo@2@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1BurningStaminaInfo@mu2@@QEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???0BurningStaminaInfo@mu2@@QEAA@XZ@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??0BurningStaminaInfo@mu2@@QEAA@XZ DB 06H
	DB	00H
	DB	00H
	DB	'.'
	DB	02H
	DB	'$'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??0BurningStaminaInfo@mu2@@QEAA@XZ DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???0BurningStaminaInfo@mu2@@QEAA@XZ@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??0BurningStaminaInfo@mu2@@QEAA@XZ DB 028H
	DD	imagerel $stateUnwindMap$??0BurningStaminaInfo@mu2@@QEAA@XZ
	DD	imagerel $ip2state$??0BurningStaminaInfo@mu2@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0BurningStaminaInfo@mu2@@QEAA@XZ DD 010911H
	DD	04209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??0BurningStaminaInfo@mu2@@QEAA@XZ
xdata	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaInfo.cpp
;	COMDAT ?FillUp@BurningStaminaInfo@mu2@@QEBAXAEAUDBBurningStaminaInfo@2@@Z
_TEXT	SEGMENT
this$ = 48
out$ = 56
?FillUp@BurningStaminaInfo@mu2@@QEBAXAEAUDBBurningStaminaInfo@2@@Z PROC ; mu2::BurningStaminaInfo::FillUp, COMDAT

; 71   : {

$LN3:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 72   : 	m_userInfo.FillUp( out );

  0000e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00013	48 8b 54 24 38	 mov	 rdx, QWORD PTR out$[rsp]
  00018	48 8b c8	 mov	 rcx, rax
  0001b	e8 00 00 00 00	 call	 ?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUDBBurningStaminaInfo@2@@Z ; mu2::BurningStaminaUserInfo::FillUp

; 73   : 	m_timeInfo.FillUp( out );

  00020	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00025	48 83 c0 28	 add	 rax, 40			; 00000028H
  00029	48 8b 54 24 38	 mov	 rdx, QWORD PTR out$[rsp]
  0002e	48 8b c8	 mov	 rcx, rax
  00031	e8 00 00 00 00	 call	 ?FillUp@BurningStaminaTimeInfo@mu2@@QEBAXAEAUDBBurningStaminaInfo@2@@Z ; mu2::BurningStaminaTimeInfo::FillUp
  00036	90		 npad	 1

; 74   : }

  00037	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0003b	c3		 ret	 0
?FillUp@BurningStaminaInfo@mu2@@QEBAXAEAUDBBurningStaminaInfo@2@@Z ENDP ; mu2::BurningStaminaInfo::FillUp
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaInfo.cpp
;	COMDAT ?FillUp@BurningStaminaInfo@mu2@@QEBAXAEAUBurningStaminaZoneServerInfo@2@@Z
_TEXT	SEGMENT
this$ = 48
out$ = 56
?FillUp@BurningStaminaInfo@mu2@@QEBAXAEAUBurningStaminaZoneServerInfo@2@@Z PROC ; mu2::BurningStaminaInfo::FillUp, COMDAT

; 66   : {

$LN3:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 67   : 	m_userInfo.FillUp( out );

  0000e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00013	48 8b 54 24 38	 mov	 rdx, QWORD PTR out$[rsp]
  00018	48 8b c8	 mov	 rcx, rax
  0001b	e8 00 00 00 00	 call	 ?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUBurningStaminaZoneServerInfo@2@@Z ; mu2::BurningStaminaUserInfo::FillUp
  00020	90		 npad	 1

; 68   : }

  00021	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00025	c3		 ret	 0
?FillUp@BurningStaminaInfo@mu2@@QEBAXAEAUBurningStaminaZoneServerInfo@2@@Z ENDP ; mu2::BurningStaminaInfo::FillUp
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaInfo.cpp
;	COMDAT ?FillUp@BurningStaminaInfo@mu2@@QEBAXAEAUBurningStaminaInstanceDungeonInfo@2@@Z
_TEXT	SEGMENT
this$ = 48
out$ = 56
?FillUp@BurningStaminaInfo@mu2@@QEBAXAEAUBurningStaminaInstanceDungeonInfo@2@@Z PROC ; mu2::BurningStaminaInfo::FillUp, COMDAT

; 61   : {

$LN3:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 62   : 	m_userInfo.FillUp(out);

  0000e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00013	48 8b 54 24 38	 mov	 rdx, QWORD PTR out$[rsp]
  00018	48 8b c8	 mov	 rcx, rax
  0001b	e8 00 00 00 00	 call	 ?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUBurningStaminaInstanceDungeonInfo@2@@Z ; mu2::BurningStaminaUserInfo::FillUp
  00020	90		 npad	 1

; 63   : }

  00021	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00025	c3		 ret	 0
?FillUp@BurningStaminaInfo@mu2@@QEBAXAEAUBurningStaminaInstanceDungeonInfo@2@@Z ENDP ; mu2::BurningStaminaInfo::FillUp
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaInfo.cpp
;	COMDAT ?FillUp@BurningStaminaInfo@mu2@@QEBAXAEAUBurningStaminaBurningInfo@2@@Z
_TEXT	SEGMENT
this$ = 48
out$ = 56
?FillUp@BurningStaminaInfo@mu2@@QEBAXAEAUBurningStaminaBurningInfo@2@@Z PROC ; mu2::BurningStaminaInfo::FillUp, COMDAT

; 55   : {

$LN3:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 56   : 	m_timeInfo.FillUp( out );

  0000e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00013	48 83 c0 28	 add	 rax, 40			; 00000028H
  00017	48 8b 54 24 38	 mov	 rdx, QWORD PTR out$[rsp]
  0001c	48 8b c8	 mov	 rcx, rax
  0001f	e8 00 00 00 00	 call	 ?FillUp@BurningStaminaTimeInfo@mu2@@QEBAXAEAUBurningStaminaBurningInfo@2@@Z ; mu2::BurningStaminaTimeInfo::FillUp

; 57   : 	m_userInfo.FillUp( out );

  00024	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00029	48 8b 54 24 38	 mov	 rdx, QWORD PTR out$[rsp]
  0002e	48 8b c8	 mov	 rcx, rax
  00031	e8 00 00 00 00	 call	 ?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUBurningStaminaBurningInfo@2@@Z ; mu2::BurningStaminaUserInfo::FillUp
  00036	90		 npad	 1

; 58   : }

  00037	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0003b	c3		 ret	 0
?FillUp@BurningStaminaInfo@mu2@@QEBAXAEAUBurningStaminaBurningInfo@2@@Z ENDP ; mu2::BurningStaminaInfo::FillUp
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaInfo.cpp
;	COMDAT ?FillUp@BurningStaminaInfo@mu2@@QEBAXAEAUBurningStaminaContinentSimepleInfo@2@@Z
_TEXT	SEGMENT
this$ = 48
out$ = 56
?FillUp@BurningStaminaInfo@mu2@@QEBAXAEAUBurningStaminaContinentSimepleInfo@2@@Z PROC ; mu2::BurningStaminaInfo::FillUp, COMDAT

; 49   : {

$LN3:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 50   : 	m_timeInfo.FillUp( out );

  0000e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00013	48 83 c0 28	 add	 rax, 40			; 00000028H
  00017	48 8b 54 24 38	 mov	 rdx, QWORD PTR out$[rsp]
  0001c	48 8b c8	 mov	 rcx, rax
  0001f	e8 00 00 00 00	 call	 ?FillUp@BurningStaminaTimeInfo@mu2@@QEBAXAEAUBurningStaminaContinentSimepleInfo@2@@Z ; mu2::BurningStaminaTimeInfo::FillUp

; 51   : 	m_userInfo.FillUp( out );

  00024	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00029	48 8b 54 24 38	 mov	 rdx, QWORD PTR out$[rsp]
  0002e	48 8b c8	 mov	 rcx, rax
  00031	e8 00 00 00 00	 call	 ?FillUp@BurningStaminaUserInfo@mu2@@QEBAXAEAUBurningStaminaContinentSimepleInfo@2@@Z ; mu2::BurningStaminaUserInfo::FillUp
  00036	90		 npad	 1

; 52   : }

  00037	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0003b	c3		 ret	 0
?FillUp@BurningStaminaInfo@mu2@@QEBAXAEAUBurningStaminaContinentSimepleInfo@2@@Z ENDP ; mu2::BurningStaminaInfo::FillUp
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaInfo.cpp
;	COMDAT ?IsBurning@BurningStaminaInfo@mu2@@QEBA_NXZ
_TEXT	SEGMENT
this$ = 48
?IsBurning@BurningStaminaInfo@mu2@@QEBA_NXZ PROC	; mu2::BurningStaminaInfo::IsBurning, COMDAT

; 44   : {

$LN3:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 45   : 	return m_timeInfo.IsBurning();

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 c0 28	 add	 rax, 40			; 00000028H
  00012	48 8b c8	 mov	 rcx, rax
  00015	e8 00 00 00 00	 call	 ?IsBurning@BurningStaminaTimeInfo@mu2@@QEBA_NXZ ; mu2::BurningStaminaTimeInfo::IsBurning

; 46   : }

  0001a	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0001e	c3		 ret	 0
?IsBurning@BurningStaminaInfo@mu2@@QEBA_NXZ ENDP	; mu2::BurningStaminaInfo::IsBurning
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaInfo.cpp
;	COMDAT ?ResetBurning@BurningStaminaInfo@mu2@@QEAAXXZ
_TEXT	SEGMENT
this$ = 48
?ResetBurning@BurningStaminaInfo@mu2@@QEAAXXZ PROC	; mu2::BurningStaminaInfo::ResetBurning, COMDAT

; 38   : {

$LN3:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 39   : 	m_timeInfo.ResetBurning();

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 c0 28	 add	 rax, 40			; 00000028H
  00012	48 8b c8	 mov	 rcx, rax
  00015	e8 00 00 00 00	 call	 ?ResetBurning@BurningStaminaTimeInfo@mu2@@QEAAXXZ ; mu2::BurningStaminaTimeInfo::ResetBurning

; 40   : 	m_userInfo.ResetUserInfo();

  0001a	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001f	48 8b c8	 mov	 rcx, rax
  00022	e8 00 00 00 00	 call	 ?ResetUserInfo@BurningStaminaUserInfo@mu2@@QEAAXXZ ; mu2::BurningStaminaUserInfo::ResetUserInfo
  00027	90		 npad	 1

; 41   : }

  00028	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0002c	c3		 ret	 0
?ResetBurning@BurningStaminaInfo@mu2@@QEAAXXZ ENDP	; mu2::BurningStaminaInfo::ResetBurning
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaInfo.cpp
;	COMDAT ?SetBurning@BurningStaminaInfo@mu2@@QEAAXPEAVEntityPlayer@2@I@Z
_TEXT	SEGMENT
hConsole$1 = 96
tv92 = 104
this$ = 128
player$ = 136
maintainTick$ = 144
?SetBurning@BurningStaminaInfo@mu2@@QEAAXPEAVEntityPlayer@2@I@Z PROC ; mu2::BurningStaminaInfo::SetBurning, COMDAT

; 26   : {

$LN5:
  00000	44 89 44 24 18	 mov	 DWORD PTR [rsp+24], r8d
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 78	 sub	 rsp, 120		; 00000078H

; 27   : 	VERIFY_RETURN(player, );

  00013	48 83 bc 24 88
	00 00 00 00	 cmp	 QWORD PTR player$[rsp], 0
  0001c	0f 85 9b 00 00
	00		 jne	 $LN2@SetBurning
  00022	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  00027	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  0002d	48 89 44 24 60	 mov	 QWORD PTR hConsole$1[rsp], rax
  00032	66 ba 0d 00	 mov	 dx, 13
  00036	48 8b 4c 24 60	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  0003b	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  00041	c7 44 24 50 1b
	00 00 00	 mov	 DWORD PTR [rsp+80], 27
  00049	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FB@GNJAOHNP@F?3?2Release_Branch?2Server?2Develo@
  00050	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  00055	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_06BALNJMNP@player@
  0005c	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  00061	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CE@MMBFKALH@mu2?3?3BurningStaminaInfo?3?3SetBur@
  00068	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  0006d	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  00074	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  00079	c7 44 24 28 1b
	00 00 00	 mov	 DWORD PTR [rsp+40], 27
  00081	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FB@GNJAOHNP@F?3?2Release_Branch?2Server?2Develo@
  00088	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  0008d	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CE@MMBFKALH@mu2?3?3BurningStaminaInfo?3?3SetBur@
  00094	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  0009a	ba 02 00 00 00	 mov	 edx, 2
  0009f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  000a6	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  000ab	66 ba 07 00	 mov	 dx, 7
  000af	48 8b 4c 24 60	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  000b4	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000ba	90		 npad	 1
  000bb	eb 67		 jmp	 SHORT $LN1@SetBurning
$LN2@SetBurning:

; 28   : 
; 29   : 	m_timeInfo.SetBurning( maintainTick);

  000bd	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000c5	48 83 c0 28	 add	 rax, 40			; 00000028H
  000c9	8b 94 24 90 00
	00 00		 mov	 edx, DWORD PTR maintainTick$[rsp]
  000d0	48 8b c8	 mov	 rcx, rax
  000d3	e8 00 00 00 00	 call	 ?SetBurning@BurningStaminaTimeInfo@mu2@@QEAAXI@Z ; mu2::BurningStaminaTimeInfo::SetBurning
  000d8	90		 npad	 1

; 30   : 
; 31   : 	if( IsBurning() )

  000d9	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  000e1	e8 00 00 00 00	 call	 ?IsBurning@BurningStaminaInfo@mu2@@QEBA_NXZ ; mu2::BurningStaminaInfo::IsBurning
  000e6	0f b6 c0	 movzx	 eax, al
  000e9	85 c0		 test	 eax, eax
  000eb	74 37		 je	 SHORT $LN3@SetBurning

; 32   : 	{		
; 33   : 		m_userInfo.SetUserInfo(player->GetCharId(), player->GetCharName() );

  000ed	48 8b 8c 24 88
	00 00 00	 mov	 rcx, QWORD PTR player$[rsp]
  000f5	e8 00 00 00 00	 call	 ?GetCharName@EntityPlayer@mu2@@QEBAAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ ; mu2::EntityPlayer::GetCharName
  000fa	48 89 44 24 68	 mov	 QWORD PTR tv92[rsp], rax
  000ff	48 8b 8c 24 88
	00 00 00	 mov	 rcx, QWORD PTR player$[rsp]
  00107	e8 00 00 00 00	 call	 ?GetCharId@EntityPlayer@mu2@@QEBAIXZ ; mu2::EntityPlayer::GetCharId
  0010c	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00114	48 8b 54 24 68	 mov	 rdx, QWORD PTR tv92[rsp]
  00119	4c 8b c2	 mov	 r8, rdx
  0011c	8b d0		 mov	 edx, eax
  0011e	e8 00 00 00 00	 call	 ?SetUserInfo@BurningStaminaUserInfo@mu2@@QEAAXIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z ; mu2::BurningStaminaUserInfo::SetUserInfo
  00123	90		 npad	 1
$LN3@SetBurning:
$LN1@SetBurning:

; 34   : 	}
; 35   : }

  00124	48 83 c4 78	 add	 rsp, 120		; 00000078H
  00128	c3		 ret	 0
?SetBurning@BurningStaminaInfo@mu2@@QEAAXPEAVEntityPlayer@2@I@Z ENDP ; mu2::BurningStaminaInfo::SetBurning
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaInfo.cpp
;	COMDAT ?Initialize@BurningStaminaInfo@mu2@@QEAAXAEBUDBBurningStaminaInfo@2@@Z
_TEXT	SEGMENT
this$ = 48
info$ = 56
?Initialize@BurningStaminaInfo@mu2@@QEAAXAEBUDBBurningStaminaInfo@2@@Z PROC ; mu2::BurningStaminaInfo::Initialize, COMDAT

; 16   : {

$LN4:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 17   : 	m_timeInfo.Initialize( info.expirationDate );

  0000e	48 8b 44 24 38	 mov	 rax, QWORD PTR info$[rsp]
  00013	48 83 c0 38	 add	 rax, 56			; 00000038H
  00017	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0001c	48 83 c1 28	 add	 rcx, 40			; 00000028H
  00020	48 8b d0	 mov	 rdx, rax
  00023	e8 00 00 00 00	 call	 ?Initialize@BurningStaminaTimeInfo@mu2@@QEAAXAEBU_SYSTEMTIME@@@Z ; mu2::BurningStaminaTimeInfo::Initialize
  00028	90		 npad	 1

; 18   : 
; 19   : 	if( IsBurning() )

  00029	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0002e	e8 00 00 00 00	 call	 ?IsBurning@BurningStaminaInfo@mu2@@QEBA_NXZ ; mu2::BurningStaminaInfo::IsBurning
  00033	0f b6 c0	 movzx	 eax, al
  00036	85 c0		 test	 eax, eax
  00038	74 1f		 je	 SHORT $LN2@Initialize

; 20   : 	{
; 21   : 		m_userInfo.SetUserInfo( info.awakenCharId, info.awakenCharName );

  0003a	48 8b 44 24 38	 mov	 rax, QWORD PTR info$[rsp]
  0003f	48 83 c0 18	 add	 rax, 24
  00043	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00048	4c 8b c0	 mov	 r8, rax
  0004b	48 8b 44 24 38	 mov	 rax, QWORD PTR info$[rsp]
  00050	8b 50 14	 mov	 edx, DWORD PTR [rax+20]
  00053	e8 00 00 00 00	 call	 ?SetUserInfo@BurningStaminaUserInfo@mu2@@QEAAXIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@@Z ; mu2::BurningStaminaUserInfo::SetUserInfo
  00058	90		 npad	 1
$LN2@Initialize:

; 22   : 	}
; 23   : }

  00059	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0005d	c3		 ret	 0
?Initialize@BurningStaminaInfo@mu2@@QEAAXAEBUDBBurningStaminaInfo@2@@Z ENDP ; mu2::BurningStaminaInfo::Initialize
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaInfo.cpp
;	COMDAT ??1BurningStaminaInfo@mu2@@QEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1BurningStaminaInfo@mu2@@QEAA@XZ PROC			; mu2::BurningStaminaInfo::~BurningStaminaInfo, COMDAT

; 12   : {

$LN4:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 13   : }

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 c0 28	 add	 rax, 40			; 00000028H
  00012	48 8b c8	 mov	 rcx, rax
  00015	e8 00 00 00 00	 call	 ??1BurningStaminaTimeInfo@mu2@@QEAA@XZ ; mu2::BurningStaminaTimeInfo::~BurningStaminaTimeInfo
  0001a	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001f	48 8b c8	 mov	 rcx, rax
  00022	e8 00 00 00 00	 call	 ??1BurningStaminaUserInfo@mu2@@QEAA@XZ ; mu2::BurningStaminaUserInfo::~BurningStaminaUserInfo
  00027	90		 npad	 1
  00028	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0002c	c3		 ret	 0
??1BurningStaminaInfo@mu2@@QEAA@XZ ENDP			; mu2::BurningStaminaInfo::~BurningStaminaInfo
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaInfo.cpp
;	COMDAT ??0BurningStaminaInfo@mu2@@QEAA@XZ
_TEXT	SEGMENT
this$ = 48
??0BurningStaminaInfo@mu2@@QEAA@XZ PROC			; mu2::BurningStaminaInfo::BurningStaminaInfo, COMDAT

; 8    : {

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8b c8	 mov	 rcx, rax
  00011	e8 00 00 00 00	 call	 ??0BurningStaminaUserInfo@mu2@@QEAA@XZ ; mu2::BurningStaminaUserInfo::BurningStaminaUserInfo
  00016	90		 npad	 1
  00017	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001c	48 83 c0 28	 add	 rax, 40			; 00000028H
  00020	48 8b c8	 mov	 rcx, rax
  00023	e8 00 00 00 00	 call	 ??0BurningStaminaTimeInfo@mu2@@QEAA@XZ ; mu2::BurningStaminaTimeInfo::BurningStaminaTimeInfo
  00028	90		 npad	 1

; 9    : }

  00029	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0002e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00032	c3		 ret	 0
??0BurningStaminaInfo@mu2@@QEAA@XZ ENDP			; mu2::BurningStaminaInfo::BurningStaminaInfo
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
this$ = 48
?dtor$0@?0???0BurningStaminaInfo@mu2@@QEAA@XZ@4HA PROC	; `mu2::BurningStaminaInfo::BurningStaminaInfo'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 30	 mov	 rcx, QWORD PTR this$[rbp]
  0000d	e8 00 00 00 00	 call	 ??1BurningStaminaUserInfo@mu2@@QEAA@XZ ; mu2::BurningStaminaUserInfo::~BurningStaminaUserInfo
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$0@?0???0BurningStaminaInfo@mu2@@QEAA@XZ@4HA ENDP	; `mu2::BurningStaminaInfo::BurningStaminaInfo'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaInfo.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaInfo.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
