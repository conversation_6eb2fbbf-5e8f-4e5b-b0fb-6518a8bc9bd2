; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	?IsChannel@Execution@mu2@@UEBA_NXZ		; mu2::Execution::IsChannel
PUBLIC	?IsWorldcross@Execution@mu2@@UEBA_NXZ		; mu2::Execution::IsWorldcross
PUBLIC	?IsTournamentEnter@Execution@mu2@@UEBA_NXZ	; mu2::Execution::IsTournamentEnter
PUBLIC	?IsWorldcrossing@Execution@mu2@@UEBA_NXZ	; mu2::Execution::IsWorldcrossing
PUBLIC	?IsSing<PERSON>@Execution@mu2@@UEBA_NXZ		; mu2::Execution::IsSingle
PUBLIC	?IsGroup@Execution@mu2@@UEBA_NXZ		; mu2::Execution::IsGroup
PUBLIC	?IsInstance@Instance@mu2@@UEBA_NXZ		; mu2::Instance::IsInstance
PUBLIC	?IsValid@Instance@mu2@@UEBA?B_NXZ		; mu2::Instance::IsValid
PUBLIC	?GetGroupKey@Instance@mu2@@UEBA?BIXZ		; mu2::Instance::GetGroupKey
PUBLIC	?IsNull@NullInstance@mu2@@UEBA_NXZ		; mu2::NullInstance::IsNull
PUBLIC	?Init@NullInstance@mu2@@UEAA_NAEAUExecutionCreateInfo@2@@Z ; mu2::NullInstance::Init
PUBLIC	?EnterExecution@NullInstance@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ; mu2::NullInstance::EnterExecution
PUBLIC	?ExitExecutionPost@NullInstance@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ; mu2::NullInstance::ExitExecutionPost
PUBLIC	??_GNullInstance@mu2@@UEAAPEAXI@Z		; mu2::NullInstance::`scalar deleting destructor'
PUBLIC	??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@ ; `string'
PUBLIC	??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@		; `string'
PUBLIC	?execution@NullInstance@mu2@@2V12@A		; mu2::NullInstance::execution
PUBLIC	??_7NullInstance@mu2@@6B@			; mu2::NullInstance::`vftable'
PUBLIC	??_C@_0BD@PKBPACOM@NullInstance?3?3Init@	; `string'
PUBLIC	??_C@_0EL@OPCOPAMI@F?3?2Release_Branch?2Server?2Develo@ ; `string'
PUBLIC	??_C@_0BG@EFLDAPLK@?$CB?$CCNullInstance?3?3Init?$CC@ ; `string'
PUBLIC	??_C@_0BI@EDINGPCE@mu2?3?3NullInstance?3?3Init@	; `string'
PUBLIC	??_R4NullInstance@mu2@@6B@			; mu2::NullInstance::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVNullInstance@mu2@@@8			; mu2::NullInstance `RTTI Type Descriptor'
PUBLIC	??_R3NullInstance@mu2@@8			; mu2::NullInstance::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2NullInstance@mu2@@8			; mu2::NullInstance::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@NullInstance@mu2@@8		; mu2::NullInstance::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@Instance@mu2@@8			; mu2::Instance::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R0?AVInstance@mu2@@@8			; mu2::Instance `RTTI Type Descriptor'
PUBLIC	??_R3Instance@mu2@@8				; mu2::Instance::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2Instance@mu2@@8				; mu2::Instance::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@Execution@mu2@@8			; mu2::Execution::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R0?AVExecution@mu2@@@8			; mu2::Execution `RTTI Type Descriptor'
PUBLIC	??_R3Execution@mu2@@8				; mu2::Execution::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2Execution@mu2@@8				; mu2::Execution::`RTTI Base Class Array'
EXTRN	??3@YAXPEAX_K@Z:PROC				; operator delete
EXTRN	atexit:PROC
EXTRN	__imp_GetStdHandle:PROC
EXTRN	__imp_SetConsoleTextAttribute:PROC
EXTRN	?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ:PROC	; mu2::Logger::Logging
EXTRN	?OnDestroyedFailed@Execution@mu2@@UEAAXXZ:PROC	; mu2::Execution::OnDestroyedFailed
EXTRN	?OnCreatedFailed@Execution@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::Execution::OnCreatedFailed
EXTRN	?EnterExecutionPrev@Execution@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::Execution::EnterExecutionPrev
EXTRN	?EnterExecutionPost@Execution@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::Execution::EnterExecutionPost
EXTRN	?Update@Execution@mu2@@UEAAXXZ:PROC		; mu2::Execution::Update
EXTRN	?OnShutdownZone@Execution@mu2@@UEAAXXZ:PROC	; mu2::Execution::OnShutdownZone
EXTRN	?IsValid@Execution@mu2@@UEBA?B_NXZ:PROC		; mu2::Execution::IsValid
EXTRN	?IsJoinable@Execution@mu2@@UEBA_NPEAVEntityPlayer@2@@Z:PROC ; mu2::Execution::IsJoinable
EXTRN	?GetRelayServerId@Execution@mu2@@UEBA?BIXZ:PROC	; mu2::Execution::GetRelayServerId
EXTRN	?Fillup@Execution@mu2@@MEBA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAPEAUEwzReqJoinExecution@2@@Z:PROC ; mu2::Execution::Fillup
EXTRN	??0Instance@mu2@@QEAA@PEAVManagerExecutionBase@1@@Z:PROC ; mu2::Instance::Instance
EXTRN	??1Instance@mu2@@UEAA@XZ:PROC			; mu2::Instance::~Instance
EXTRN	?SendReqJoinExecution@Instance@mu2@@UEBA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@@Z:PROC ; mu2::Instance::SendReqJoinExecution
EXTRN	?OnResGameReady@Instance@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::Instance::OnResGameReady
EXTRN	?OnCreatedSucceed@Instance@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z:PROC ; mu2::Instance::OnCreatedSucceed
EXTRN	?OnDestroyed@Instance@mu2@@UEAAXXZ:PROC		; mu2::Instance::OnDestroyed
EXTRN	?IsDestroyable@Instance@mu2@@UEBA_NXZ:PROC	; mu2::Instance::IsDestroyable
EXTRN	?IsRunning@Instance@mu2@@UEBA_NXZ:PROC		; mu2::Instance::IsRunning
EXTRN	?GetDamageMeterKey@Instance@mu2@@UEBA?BIXZ:PROC	; mu2::Instance::GetDamageMeterKey
EXTRN	?ToString@Instance@mu2@@UEBA?BV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ:PROC ; mu2::Instance::ToString
EXTRN	?Fillup@Instance@mu2@@UEAAXAEAPEAUEwzReqCreateExecution@2@@Z:PROC ; mu2::Instance::Fillup
EXTRN	?Fillup@Instance@mu2@@UEAAXAEAPEAUEwzReqDestroyExecution@2@@Z:PROC ; mu2::Instance::Fillup
EXTRN	?Fillup@Instance@mu2@@UEBAXAEAVJoinZoneContextDest@2@@Z:PROC ; mu2::Instance::Fillup
EXTRN	??_ENullInstance@mu2@@UEAAPEAXI@Z:PROC		; mu2::NullInstance::`vector deleting destructor'
EXTRN	??_7type_info@@6B@:BYTE				; type_info::`vftable'
_BSS	SEGMENT
?execution@NullInstance@mu2@@2V12@A DB 0170H DUP (?)	; mu2::NullInstance::execution
_BSS	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?IsValid@Instance@mu2@@UEBA?B_NXZ DD imagerel $LN5
	DD	imagerel $LN5+62
	DD	imagerel $unwind$?IsValid@Instance@mu2@@UEBA?B_NXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Init@NullInstance@mu2@@UEAA_NAEAUExecutionCreateInfo@2@@Z DD imagerel $LN4
	DD	imagerel $LN4+194
	DD	imagerel $unwind$?Init@NullInstance@mu2@@UEAA_NAEAUExecutionCreateInfo@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GNullInstance@mu2@@UEAAPEAXI@Z DD imagerel $LN10
	DD	imagerel $LN10+76
	DD	imagerel $unwind$??_GNullInstance@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__E?execution@NullInstance@mu2@@2V12@A@@YAXXZ DD imagerel ??__E?execution@NullInstance@mu2@@2V12@A@@YAXXZ
	DD	imagerel ??__E?execution@NullInstance@mu2@@2V12@A@@YAXXZ+50
	DD	imagerel $unwind$??__E?execution@NullInstance@mu2@@2V12@A@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__F?execution@NullInstance@mu2@@2V12@A@@YAXXZ DD imagerel ??__F?execution@NullInstance@mu2@@2V12@A@@YAXXZ
	DD	imagerel ??__F?execution@NullInstance@mu2@@2V12@A@@YAXXZ+36
	DD	imagerel $unwind$??__F?execution@NullInstance@mu2@@2V12@A@@YAXXZ
pdata	ENDS
CRT$XCU	SEGMENT
??execution$initializer$@NullInstance@mu2@@2P6AXXZEA@@3P6AXXZEA DQ FLAT:??__E?execution@NullInstance@mu2@@2V12@A@@YAXXZ ; ??execution$initializer$@NullInstance@mu2@@2P6AXXZEA@@3P6AXXZEA
CRT$XCU	ENDS
;	COMDAT ??_R2Execution@mu2@@8
rdata$r	SEGMENT
??_R2Execution@mu2@@8 DD imagerel ??_R1A@?0A@EA@Execution@mu2@@8 ; mu2::Execution::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3Execution@mu2@@8
rdata$r	SEGMENT
??_R3Execution@mu2@@8 DD 00H				; mu2::Execution::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2Execution@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVExecution@mu2@@@8
data$rs	SEGMENT
??_R0?AVExecution@mu2@@@8 DQ FLAT:??_7type_info@@6B@	; mu2::Execution `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVExecution@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R1A@?0A@EA@Execution@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@Execution@mu2@@8 DD imagerel ??_R0?AVExecution@mu2@@@8 ; mu2::Execution::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3Execution@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2Instance@mu2@@8
rdata$r	SEGMENT
??_R2Instance@mu2@@8 DD imagerel ??_R1A@?0A@EA@Instance@mu2@@8 ; mu2::Instance::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@Execution@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3Instance@mu2@@8
rdata$r	SEGMENT
??_R3Instance@mu2@@8 DD 00H				; mu2::Instance::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2Instance@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVInstance@mu2@@@8
data$rs	SEGMENT
??_R0?AVInstance@mu2@@@8 DQ FLAT:??_7type_info@@6B@	; mu2::Instance `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVInstance@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R1A@?0A@EA@Instance@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@Instance@mu2@@8 DD imagerel ??_R0?AVInstance@mu2@@@8 ; mu2::Instance::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3Instance@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@NullInstance@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@NullInstance@mu2@@8 DD imagerel ??_R0?AVNullInstance@mu2@@@8 ; mu2::NullInstance::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	02H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3NullInstance@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2NullInstance@mu2@@8
rdata$r	SEGMENT
??_R2NullInstance@mu2@@8 DD imagerel ??_R1A@?0A@EA@NullInstance@mu2@@8 ; mu2::NullInstance::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@Instance@mu2@@8
	DD	imagerel ??_R1A@?0A@EA@Execution@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3NullInstance@mu2@@8
rdata$r	SEGMENT
??_R3NullInstance@mu2@@8 DD 00H				; mu2::NullInstance::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	03H
	DD	imagerel ??_R2NullInstance@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVNullInstance@mu2@@@8
data$rs	SEGMENT
??_R0?AVNullInstance@mu2@@@8 DQ FLAT:??_7type_info@@6B@	; mu2::NullInstance `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVNullInstance@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4NullInstance@mu2@@6B@
rdata$r	SEGMENT
??_R4NullInstance@mu2@@6B@ DD 01H			; mu2::NullInstance::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVNullInstance@mu2@@@8
	DD	imagerel ??_R3NullInstance@mu2@@8
	DD	imagerel ??_R4NullInstance@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_C@_0BI@EDINGPCE@mu2?3?3NullInstance?3?3Init@
CONST	SEGMENT
??_C@_0BI@EDINGPCE@mu2?3?3NullInstance?3?3Init@ DB 'mu2::NullInstance::In'
	DB	'it', 00H					; `string'
CONST	ENDS
;	COMDAT ??_C@_0BG@EFLDAPLK@?$CB?$CCNullInstance?3?3Init?$CC@
CONST	SEGMENT
??_C@_0BG@EFLDAPLK@?$CB?$CCNullInstance?3?3Init?$CC@ DB '!"NullInstance::'
	DB	'Init"', 00H					; `string'
CONST	ENDS
;	COMDAT ??_C@_0EL@OPCOPAMI@F?3?2Release_Branch?2Server?2Develo@
CONST	SEGMENT
??_C@_0EL@OPCOPAMI@F?3?2Release_Branch?2Server?2Develo@ DB 'F:\Release_Br'
	DB	'anch\Server\Development\Frontend\WorldServer\InstanceNull.cpp'
	DB	00H						; `string'
CONST	ENDS
;	COMDAT ??_C@_0BD@PKBPACOM@NullInstance?3?3Init@
CONST	SEGMENT
??_C@_0BD@PKBPACOM@NullInstance?3?3Init@ DB 'NullInstance::Init', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7NullInstance@mu2@@6B@
CONST	SEGMENT
??_7NullInstance@mu2@@6B@ DQ FLAT:??_R4NullInstance@mu2@@6B@ ; mu2::NullInstance::`vftable'
	DQ	FLAT:??_ENullInstance@mu2@@UEAAPEAXI@Z
	DQ	FLAT:?OnDestroyed@Instance@mu2@@UEAAXXZ
	DQ	FLAT:?OnDestroyedFailed@Execution@mu2@@UEAAXXZ
	DQ	FLAT:?OnCreatedSucceed@Instance@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?OnCreatedFailed@Execution@mu2@@UEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?OnResGameReady@Instance@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?EnterExecutionPrev@Execution@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?EnterExecution@NullInstance@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?EnterExecutionPost@Execution@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?ExitExecutionPost@NullInstance@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?Init@NullInstance@mu2@@UEAA_NAEAUExecutionCreateInfo@2@@Z
	DQ	FLAT:?Update@Execution@mu2@@UEAAXXZ
	DQ	FLAT:?OnShutdownZone@Execution@mu2@@UEAAXXZ
	DQ	FLAT:?SendReqJoinExecution@Instance@mu2@@UEBA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@@Z
	DQ	FLAT:?IsInstance@Instance@mu2@@UEBA_NXZ
	DQ	FLAT:?IsChannel@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsWorldcross@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsTournamentEnter@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsWorldcrossing@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsSingle@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsGroup@Execution@mu2@@UEBA_NXZ
	DQ	FLAT:?IsNull@NullInstance@mu2@@UEBA_NXZ
	DQ	FLAT:?IsValid@Instance@mu2@@UEBA?B_NXZ
	DQ	FLAT:?IsRunning@Instance@mu2@@UEBA_NXZ
	DQ	FLAT:?IsJoinable@Execution@mu2@@UEBA_NPEAVEntityPlayer@2@@Z
	DQ	FLAT:?IsDestroyable@Instance@mu2@@UEBA_NXZ
	DQ	FLAT:?GetDamageMeterKey@Instance@mu2@@UEBA?BIXZ
	DQ	FLAT:?ToString@Instance@mu2@@UEBA?BV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ
	DQ	FLAT:?GetRelayServerId@Execution@mu2@@UEBA?BIXZ
	DQ	FLAT:?Fillup@Execution@mu2@@MEBA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAPEAUEwzReqJoinExecution@2@@Z
	DQ	FLAT:?Fillup@Instance@mu2@@UEBAXAEAVJoinZoneContextDest@2@@Z
	DQ	FLAT:?Fillup@Instance@mu2@@UEAAXAEAPEAUEwzReqDestroyExecution@2@@Z
	DQ	FLAT:?Fillup@Instance@mu2@@UEAAXAEAPEAUEwzReqCreateExecution@2@@Z
	DQ	FLAT:?GetGroupKey@Instance@mu2@@UEBA?BIXZ
CONST	ENDS
;	COMDAT ??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
CONST	SEGMENT
??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@ DB 'g', 00H, 'a', 00H, 'm', 00H, 'e'
	DB	00H, 00H, 00H				; `string'
CONST	ENDS
;	COMDAT ??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
CONST	SEGMENT
??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@ DB '%'
	DB	's> ASSERT - %s, %s(%d)', 00H		; `string'
CONST	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__F?execution@NullInstance@mu2@@2V12@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__E?execution@NullInstance@mu2@@2V12@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GNullInstance@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Init@NullInstance@mu2@@UEAA_NAEAUExecutionCreateInfo@2@@Z DD 010e01H
	DD	0e20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?IsValid@Instance@mu2@@UEBA?B_NXZ DD 010901H
	DD	06209H
xdata	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceNull.h
;	COMDAT ??__F?execution@NullInstance@mu2@@2V12@A@@YAXXZ
text$yd	SEGMENT
??__F?execution@NullInstance@mu2@@2V12@A@@YAXXZ PROC	; `dynamic atexit destructor for 'mu2::NullInstance::execution'', COMDAT
  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 18   : 		virtual~NullInstance() {}

  00004	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_7NullInstance@mu2@@6B@
  0000b	48 89 05 00 00
	00 00		 mov	 QWORD PTR ?execution@NullInstance@mu2@@2V12@A, rax
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?execution@NullInstance@mu2@@2V12@A ; mu2::NullInstance::execution
  00019	e8 00 00 00 00	 call	 ??1Instance@mu2@@UEAA@XZ ; mu2::Instance::~Instance
  0001e	90		 npad	 1
  0001f	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00023	c3		 ret	 0
??__F?execution@NullInstance@mu2@@2V12@A@@YAXXZ ENDP	; `dynamic atexit destructor for 'mu2::NullInstance::execution''
text$yd	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceNull.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceNull.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceNull.cpp
;	COMDAT ??__E?execution@NullInstance@mu2@@2V12@A@@YAXXZ
text$di	SEGMENT
??__E?execution@NullInstance@mu2@@2V12@A@@YAXXZ PROC	; `dynamic initializer for 'mu2::NullInstance::execution'', COMDAT

; 13   : 	NullInstance NullInstance::execution(NULL);

  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceNull.h

; 17   : 		NullInstance(ManagerExecutionBase* pOwnerManager) : Instance(pOwnerManager) {}

  00004	33 d2		 xor	 edx, edx
  00006	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?execution@NullInstance@mu2@@2V12@A ; mu2::NullInstance::execution
  0000d	e8 00 00 00 00	 call	 ??0Instance@mu2@@QEAA@PEAVManagerExecutionBase@1@@Z ; mu2::Instance::Instance
  00012	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_7NullInstance@mu2@@6B@
  00019	48 89 05 00 00
	00 00		 mov	 QWORD PTR ?execution@NullInstance@mu2@@2V12@A, rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceNull.cpp

; 13   : 	NullInstance NullInstance::execution(NULL);

  00020	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??__F?execution@NullInstance@mu2@@2V12@A@@YAXXZ ; `dynamic atexit destructor for 'mu2::NullInstance::execution''
  00027	e8 00 00 00 00	 call	 atexit
  0002c	90		 npad	 1
  0002d	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00031	c3		 ret	 0
??__E?execution@NullInstance@mu2@@2V12@A@@YAXXZ ENDP	; `dynamic initializer for 'mu2::NullInstance::execution''
text$di	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceNull.h
;	COMDAT ??_GNullInstance@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GNullInstance@mu2@@UEAAPEAXI@Z PROC			; mu2::NullInstance::`scalar deleting destructor', COMDAT
$LN10:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 18   : 		virtual~NullInstance() {}

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7NullInstance@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx
  0001c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00021	e8 00 00 00 00	 call	 ??1Instance@mu2@@UEAA@XZ ; mu2::Instance::~Instance
  00026	90		 npad	 1
  00027	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  0002b	83 e0 01	 and	 eax, 1
  0002e	85 c0		 test	 eax, eax
  00030	74 10		 je	 SHORT $LN2@scalar
  00032	ba 70 01 00 00	 mov	 edx, 368		; 00000170H
  00037	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0003c	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00041	90		 npad	 1
$LN2@scalar:
  00042	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00047	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0004b	c3		 ret	 0
??_GNullInstance@mu2@@UEAAPEAXI@Z ENDP			; mu2::NullInstance::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceNull.h
;	COMDAT ?ExitExecutionPost@NullInstance@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
_TEXT	SEGMENT
this$ = 8
__formal$ = 16
__formal$ = 24
?ExitExecutionPost@NullInstance@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z PROC ; mu2::NullInstance::ExitExecutionPost, COMDAT

; 22   : 		virtual ErrorJoin::Error ExitExecutionPost(EntityPlayer*, EventPtr&) override { return ErrorJoin::NullInstanceFailed; }

  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	b8 f6 1a 06 00	 mov	 eax, 400118		; 00061af6H
  00014	c3		 ret	 0
?ExitExecutionPost@NullInstance@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ENDP ; mu2::NullInstance::ExitExecutionPost
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceNull.h
;	COMDAT ?EnterExecution@NullInstance@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
_TEXT	SEGMENT
this$ = 8
__formal$ = 16
__formal$ = 24
?EnterExecution@NullInstance@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z PROC ; mu2::NullInstance::EnterExecution, COMDAT

; 21   : 		virtual ErrorJoin::Error EnterExecution(EntityPlayer*, EventPtr&) override { return ErrorJoin::NullInstanceFailed; }

  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	b8 f6 1a 06 00	 mov	 eax, 400118		; 00061af6H
  00014	c3		 ret	 0
?EnterExecution@NullInstance@mu2@@UEAA?AW4Error@ErrorJoin@2@PEAVEntityPlayer@2@AEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ENDP ; mu2::NullInstance::EnterExecution
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceNull.cpp
;	COMDAT ?Init@NullInstance@mu2@@UEAA_NAEAUExecutionCreateInfo@2@@Z
_TEXT	SEGMENT
hConsole$1 = 96
this$ = 128
__formal$ = 136
?Init@NullInstance@mu2@@UEAA_NAEAUExecutionCreateInfo@2@@Z PROC ; mu2::NullInstance::Init, COMDAT

; 16   : 	{

$LN4:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 78	 sub	 rsp, 120		; 00000078H

; 17   : 		VERIFY_RETURN(!"NullInstance::Init", false);

  0000e	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BD@PKBPACOM@NullInstance?3?3Init@
  00015	48 85 c0	 test	 rax, rax
  00018	0f 84 9d 00 00
	00		 je	 $LN2@Init
  0001e	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  00023	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  00029	48 89 44 24 60	 mov	 QWORD PTR hConsole$1[rsp], rax
  0002e	66 ba 0d 00	 mov	 dx, 13
  00032	48 8b 4c 24 60	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  00037	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  0003d	c7 44 24 50 11
	00 00 00	 mov	 DWORD PTR [rsp+80], 17
  00045	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EL@OPCOPAMI@F?3?2Release_Branch?2Server?2Develo@
  0004c	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  00051	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BG@EFLDAPLK@?$CB?$CCNullInstance?3?3Init?$CC@
  00058	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  0005d	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@EDINGPCE@mu2?3?3NullInstance?3?3Init@
  00064	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  00069	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  00070	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  00075	c7 44 24 28 11
	00 00 00	 mov	 DWORD PTR [rsp+40], 17
  0007d	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EL@OPCOPAMI@F?3?2Release_Branch?2Server?2Develo@
  00084	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  00089	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0BI@EDINGPCE@mu2?3?3NullInstance?3?3Init@
  00090	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  00096	ba 02 00 00 00	 mov	 edx, 2
  0009b	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  000a2	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  000a7	66 ba 07 00	 mov	 dx, 7
  000ab	48 8b 4c 24 60	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  000b0	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000b6	90		 npad	 1
  000b7	32 c0		 xor	 al, al
  000b9	eb 02		 jmp	 SHORT $LN1@Init
$LN2@Init:

; 18   : 		return false;

  000bb	32 c0		 xor	 al, al
$LN1@Init:

; 19   : 	}

  000bd	48 83 c4 78	 add	 rsp, 120		; 00000078H
  000c1	c3		 ret	 0
?Init@NullInstance@mu2@@UEAA_NAEAUExecutionCreateInfo@2@@Z ENDP ; mu2::NullInstance::Init
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceNull.h
;	COMDAT ?IsNull@NullInstance@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsNull@NullInstance@mu2@@UEBA_NXZ PROC			; mu2::NullInstance::IsNull, COMDAT

; 19   : 		virtual Bool IsNull() const override { return true; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	b0 01		 mov	 al, 1
  00007	c3		 ret	 0
?IsNull@NullInstance@mu2@@UEBA_NXZ ENDP			; mu2::NullInstance::IsNull
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Instance.h
;	COMDAT ?GetGroupKey@Instance@mu2@@UEBA?BIXZ
_TEXT	SEGMENT
this$ = 8
?GetGroupKey@Instance@mu2@@UEBA?BIXZ PROC		; mu2::Instance::GetGroupKey, COMDAT

; 83   : 		virtual const InstanceGroupKey	GetGroupKey() const { return 0; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	33 c0		 xor	 eax, eax
  00007	c3		 ret	 0
?GetGroupKey@Instance@mu2@@UEBA?BIXZ ENDP		; mu2::Instance::GetGroupKey
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Instance.h
;	COMDAT ?IsValid@Instance@mu2@@UEBA?B_NXZ
_TEXT	SEGMENT
tv74 = 32
this$ = 64
?IsValid@Instance@mu2@@UEBA?B_NXZ PROC			; mu2::Instance::IsValid, COMDAT

; 82   : 		virtual const Bool				IsValid() const override { return m_instanceId > 0 && __super::IsValid(); }

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H
  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	83 b8 84 00 00
	00 00		 cmp	 DWORD PTR [rax+132], 0
  00015	76 18		 jbe	 SHORT $LN3@IsValid
  00017	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0001c	e8 00 00 00 00	 call	 ?IsValid@Execution@mu2@@UEBA?B_NXZ ; mu2::Execution::IsValid
  00021	0f b6 c0	 movzx	 eax, al
  00024	85 c0		 test	 eax, eax
  00026	74 07		 je	 SHORT $LN3@IsValid
  00028	c6 44 24 20 01	 mov	 BYTE PTR tv74[rsp], 1
  0002d	eb 05		 jmp	 SHORT $LN4@IsValid
$LN3@IsValid:
  0002f	c6 44 24 20 00	 mov	 BYTE PTR tv74[rsp], 0
$LN4@IsValid:
  00034	0f b6 44 24 20	 movzx	 eax, BYTE PTR tv74[rsp]
  00039	48 83 c4 38	 add	 rsp, 56			; 00000038H
  0003d	c3		 ret	 0
?IsValid@Instance@mu2@@UEBA?B_NXZ ENDP			; mu2::Instance::IsValid
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Instance.h
;	COMDAT ?IsInstance@Instance@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsInstance@Instance@mu2@@UEBA_NXZ PROC			; mu2::Instance::IsInstance, COMDAT

; 80   : 		virtual Bool					IsInstance() const final { return true; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	b0 01		 mov	 al, 1
  00007	c3		 ret	 0
?IsInstance@Instance@mu2@@UEBA_NXZ ENDP			; mu2::Instance::IsInstance
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?IsGroup@Execution@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsGroup@Execution@mu2@@UEBA_NXZ PROC			; mu2::Execution::IsGroup, COMDAT

; 85   : 		virtual Bool					IsGroup() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsGroup@Execution@mu2@@UEBA_NXZ ENDP			; mu2::Execution::IsGroup
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?IsSingle@Execution@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsSingle@Execution@mu2@@UEBA_NXZ PROC			; mu2::Execution::IsSingle, COMDAT

; 84   : 		virtual Bool					IsSingle() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsSingle@Execution@mu2@@UEBA_NXZ ENDP			; mu2::Execution::IsSingle
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?IsWorldcrossing@Execution@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsWorldcrossing@Execution@mu2@@UEBA_NXZ PROC		; mu2::Execution::IsWorldcrossing, COMDAT

; 83   : 		virtual Bool					IsWorldcrossing() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsWorldcrossing@Execution@mu2@@UEBA_NXZ ENDP		; mu2::Execution::IsWorldcrossing
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?IsTournamentEnter@Execution@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsTournamentEnter@Execution@mu2@@UEBA_NXZ PROC		; mu2::Execution::IsTournamentEnter, COMDAT

; 82   : 		virtual Bool					IsTournamentEnter() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsTournamentEnter@Execution@mu2@@UEBA_NXZ ENDP		; mu2::Execution::IsTournamentEnter
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?IsWorldcross@Execution@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsWorldcross@Execution@mu2@@UEBA_NXZ PROC		; mu2::Execution::IsWorldcross, COMDAT

; 81   : 		virtual Bool					IsWorldcross() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsWorldcross@Execution@mu2@@UEBA_NXZ ENDP		; mu2::Execution::IsWorldcross
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\Execution.h
;	COMDAT ?IsChannel@Execution@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsChannel@Execution@mu2@@UEBA_NXZ PROC			; mu2::Execution::IsChannel, COMDAT

; 80   : 		virtual Bool					IsChannel() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsChannel@Execution@mu2@@UEBA_NXZ ENDP			; mu2::Execution::IsChannel
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceNull.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\InstanceNull.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
