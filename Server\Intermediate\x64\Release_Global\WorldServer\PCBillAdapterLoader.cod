; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	??0CPCBillAdapterLoader@@QEAA@XZ		; CPCBillAdapterLoader::CPCBillAdapterLoader
PUBLIC	??1CPCBillAdapterLoader@@QEAA@XZ		; CPCBillAdapterLoader::~CPCBillAdapterLoader
PUBLIC	?Load@CPCBillAdapterLoader@@QEAA_NPEB_W@Z	; CPCBillAdapterLoader::Load
PUBLIC	?Unload@CPCBillAdapterLoader@@QEAAXXZ		; CPCBillAdapterLoader::Unload
PUBLIC	??_C@_0P@CGDAIOOJ@GetMainHandler@		; `string'
EXTRN	__imp_FreeLibrary:PROC
EXTRN	__imp_GetProcAddress:PROC
EXTRN	__imp_LoadLibraryW:PROC
EXTRN	__CxxFrameHandler4:PROC
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1CPCBillAdapterLoader@@QEAA@XZ DD imagerel $LN4
	DD	imagerel $LN4+25
	DD	imagerel $unwind$??1CPCBillAdapterLoader@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Load@CPCBillAdapterLoader@@QEAA_NPEB_W@Z DD imagerel $LN9
	DD	imagerel $LN9+209
	DD	imagerel $unwind$?Load@CPCBillAdapterLoader@@QEAA_NPEB_W@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Unload@CPCBillAdapterLoader@@QEAAXXZ DD imagerel $LN4
	DD	imagerel $LN4+66
	DD	imagerel $unwind$?Unload@CPCBillAdapterLoader@@QEAAXXZ
pdata	ENDS
;	COMDAT ??_C@_0P@CGDAIOOJ@GetMainHandler@
CONST	SEGMENT
??_C@_0P@CGDAIOOJ@GetMainHandler@ DB 'GetMainHandler', 00H ; `string'
CONST	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Unload@CPCBillAdapterLoader@@QEAAXXZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Load@CPCBillAdapterLoader@@QEAA_NPEB_W@Z DD 010e01H
	DD	0620eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??1CPCBillAdapterLoader@@QEAA@XZ DB 02H
	DB	00H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??1CPCBillAdapterLoader@@QEAA@XZ DB 060H
	DD	imagerel $ip2state$??1CPCBillAdapterLoader@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1CPCBillAdapterLoader@@QEAA@XZ DD 010919H
	DD	04209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??1CPCBillAdapterLoader@@QEAA@XZ
xdata	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PCRoom\Export\PCBillAdapterLoader.cpp
;	COMDAT ?Unload@CPCBillAdapterLoader@@QEAAXXZ
_TEXT	SEGMENT
this$ = 48
?Unload@CPCBillAdapterLoader@@QEAAXXZ PROC		; CPCBillAdapterLoader::Unload, COMDAT

; 61   : {

$LN4:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 62   : 	if(NULL == m_hLib)

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 38 00	 cmp	 QWORD PTR [rax], 0
  00012	75 02		 jne	 SHORT $LN2@Unload

; 63   : 		return;

  00014	eb 27		 jmp	 SHORT $LN1@Unload
$LN2@Unload:

; 64   : 
; 65   : 	m_pBillingManager = NULL;

  00016	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001b	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0

; 66   : 
; 67   : 	::FreeLibrary(m_hLib);

  00023	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00028	48 8b 08	 mov	 rcx, QWORD PTR [rax]
  0002b	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_FreeLibrary

; 68   : 	m_hLib = NULL;

  00031	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00036	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0
$LN1@Unload:

; 69   : }

  0003d	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00041	c3		 ret	 0
?Unload@CPCBillAdapterLoader@@QEAAXXZ ENDP		; CPCBillAdapterLoader::Unload
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PCRoom\Export\PCBillAdapterLoader.cpp
;	COMDAT ?Load@CPCBillAdapterLoader@@QEAA_NPEB_W@Z
_TEXT	SEGMENT
lpGetMainHandler$ = 32
lpMainHandler$ = 40
this$ = 64
_lpszFilePath$ = 72
?Load@CPCBillAdapterLoader@@QEAA_NPEB_W@Z PROC		; CPCBillAdapterLoader::Load, COMDAT

; 22   : {

$LN9:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 23   : 	if(NULL != m_hLib)

  0000e	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00013	48 83 38 00	 cmp	 QWORD PTR [rax], 0
  00017	74 07		 je	 SHORT $LN2@Load

; 24   : 		return true;

  00019	b0 01		 mov	 al, 1
  0001b	e9 ac 00 00 00	 jmp	 $LN1@Load
$LN2@Load:

; 25   : 
; 26   : 	if(NULL == _lpszFilePath)

  00020	48 83 7c 24 48
	00		 cmp	 QWORD PTR _lpszFilePath$[rsp], 0
  00026	75 07		 jne	 SHORT $LN3@Load

; 27   : 		return false;

  00028	32 c0		 xor	 al, al
  0002a	e9 9d 00 00 00	 jmp	 $LN1@Load
$LN3@Load:

; 28   : 
; 29   : 	m_hLib = ::LoadLibrary(_lpszFilePath);

  0002f	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _lpszFilePath$[rsp]
  00034	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_LoadLibraryW
  0003a	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0003f	48 89 01	 mov	 QWORD PTR [rcx], rax

; 30   : 	if(NULL == m_hLib)

  00042	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00047	48 83 38 00	 cmp	 QWORD PTR [rax], 0
  0004b	75 04		 jne	 SHORT $LN4@Load

; 31   : 		return false;

  0004d	32 c0		 xor	 al, al
  0004f	eb 7b		 jmp	 SHORT $LN1@Load
$LN4@Load:

; 32   : 
; 33   : 	LPFN_GET_MAINHANDLER lpGetMainHandler = (LPFN_GET_MAINHANDLER)::GetProcAddress(m_hLib, "GetMainHandler");

  00051	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:??_C@_0P@CGDAIOOJ@GetMainHandler@
  00058	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0005d	48 8b 08	 mov	 rcx, QWORD PTR [rax]
  00060	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetProcAddress
  00066	48 89 44 24 20	 mov	 QWORD PTR lpGetMainHandler$[rsp], rax

; 34   : 	if(NULL == lpGetMainHandler)

  0006b	48 83 7c 24 20
	00		 cmp	 QWORD PTR lpGetMainHandler$[rsp], 0
  00071	75 0e		 jne	 SHORT $LN5@Load

; 35   : 	{
; 36   : 		Unload();

  00073	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00078	e8 00 00 00 00	 call	 ?Unload@CPCBillAdapterLoader@@QEAAXXZ ; CPCBillAdapterLoader::Unload

; 37   : 
; 38   : 		return false;

  0007d	32 c0		 xor	 al, al
  0007f	eb 4b		 jmp	 SHORT $LN1@Load
$LN5@Load:

; 39   : 	}
; 40   : 
; 41   : 	LPMAINHANDLER lpMainHandler = lpGetMainHandler();

  00081	ff 54 24 20	 call	 QWORD PTR lpGetMainHandler$[rsp]
  00085	48 89 44 24 28	 mov	 QWORD PTR lpMainHandler$[rsp], rax

; 42   : 	if(NULL == lpMainHandler)

  0008a	48 83 7c 24 28
	00		 cmp	 QWORD PTR lpMainHandler$[rsp], 0
  00090	75 0e		 jne	 SHORT $LN6@Load

; 43   : 	{
; 44   : 		Unload();

  00092	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00097	e8 00 00 00 00	 call	 ?Unload@CPCBillAdapterLoader@@QEAAXXZ ; CPCBillAdapterLoader::Unload

; 45   : 
; 46   : 		return false;

  0009c	32 c0		 xor	 al, al
  0009e	eb 2c		 jmp	 SHORT $LN1@Load
$LN6@Load:

; 47   : 	}
; 48   : 
; 49   : 	m_pBillingManager = lpMainHandler->lpfnGetBillingManager();

  000a0	48 8b 44 24 28	 mov	 rax, QWORD PTR lpMainHandler$[rsp]
  000a5	ff 10		 call	 QWORD PTR [rax]
  000a7	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  000ac	48 89 41 08	 mov	 QWORD PTR [rcx+8], rax

; 50   : 	if(NULL == m_pBillingManager)

  000b0	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  000b5	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  000ba	75 0e		 jne	 SHORT $LN7@Load

; 51   : 	{
; 52   : 		Unload();

  000bc	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  000c1	e8 00 00 00 00	 call	 ?Unload@CPCBillAdapterLoader@@QEAAXXZ ; CPCBillAdapterLoader::Unload

; 53   : 
; 54   : 		return false;

  000c6	32 c0		 xor	 al, al
  000c8	eb 02		 jmp	 SHORT $LN1@Load
$LN7@Load:

; 55   : 	}
; 56   : 
; 57   : 	return true;

  000ca	b0 01		 mov	 al, 1
$LN1@Load:

; 58   : }

  000cc	48 83 c4 38	 add	 rsp, 56			; 00000038H
  000d0	c3		 ret	 0
?Load@CPCBillAdapterLoader@@QEAA_NPEB_W@Z ENDP		; CPCBillAdapterLoader::Load
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PCRoom\Export\PCBillAdapterLoader.cpp
;	COMDAT ??1CPCBillAdapterLoader@@QEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1CPCBillAdapterLoader@@QEAA@XZ PROC			; CPCBillAdapterLoader::~CPCBillAdapterLoader, COMDAT

; 17   : {

$LN4:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 18   : 	Unload();

  00009	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0000e	e8 00 00 00 00	 call	 ?Unload@CPCBillAdapterLoader@@QEAAXXZ ; CPCBillAdapterLoader::Unload
  00013	90		 npad	 1

; 19   : }

  00014	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00018	c3		 ret	 0
??1CPCBillAdapterLoader@@QEAA@XZ ENDP			; CPCBillAdapterLoader::~CPCBillAdapterLoader
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PCRoom\Export\PCBillAdapterLoader.cpp
;	COMDAT ??0CPCBillAdapterLoader@@QEAA@XZ
_TEXT	SEGMENT
this$ = 8
??0CPCBillAdapterLoader@@QEAA@XZ PROC			; CPCBillAdapterLoader::CPCBillAdapterLoader, COMDAT

; 12   : {

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 10   : 	: m_hLib(NULL)

  00005	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0000a	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0

; 11   : 	, m_pBillingManager(NULL)

  00011	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00016	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0

; 13   : }

  0001e	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00023	c3		 ret	 0
??0CPCBillAdapterLoader@@QEAA@XZ ENDP			; CPCBillAdapterLoader::CPCBillAdapterLoader
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PCRoom\Export\PCBillAdapterLoader.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\PCRoom\Export\PCBillAdapterLoader.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
