; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	??0JoinRankTimeCalculator@mu2@@QEAA@XZ		; mu2::JoinRankTimeCalculator::JoinRankTimeCalculator
PUBLIC	??1JoinRankTimeCalculator@mu2@@QEAA@XZ		; mu2::JoinRankTimeCalculator::~Join<PERSON>ankTimeCalculator
PUBLIC	?EnterWait@JoinRankTimeCalculator@mu2@@QEAAII@Z	; mu2::JoinRankTimeCalculator::EnterWait
PUBLIC	?LeaveWait@JoinRankTimeCalculator@mu2@@QEAAXXZ	; mu2::JoinRankTimeCalculator::Leave<PERSON>ait
PUBLIC	?GetTime@JoinRankTimeCalculator@mu2@@QEBAII@Z	; mu2::JoinRankTimeCalculator::GetTime
PUBLIC	?updateLastEnterWaitTick@JoinRankTimeCalculator@mu2@@AEAAXI@Z ; mu2::JoinRankTimeCalculator::updateLastEnterWaitTick
PUBLIC	?updateLastLeaveWaitTick@JoinRankTimeCalculator@mu2@@AEAAXI@Z ; mu2::JoinRankTimeCalculator::updateLastLeaveWaitTick
PUBLIC	?updateTimeCapture@JoinRankTimeCalculator@mu2@@AEAAXI@Z ; mu2::JoinRankTimeCalculator::updateTimeCapture
PUBLIC	?checkReset@JoinRankTimeCalculator@mu2@@AEAA_NXZ ; mu2::JoinRankTimeCalculator::checkReset
PUBLIC	?reset@JoinRankTimeCalculator@mu2@@AEAAXXZ	; mu2::JoinRankTimeCalculator::reset
PUBLIC	?calc@JoinRankTimeCalculator@mu2@@AEAAXXZ	; mu2::JoinRankTimeCalculator::calc
PUBLIC	?getTimeCaptureIndex@JoinRankTimeCalculator@mu2@@AEBA_KXZ ; mu2::JoinRankTimeCalculator::getTimeCaptureIndex
PUBLIC	??1?$unique_ptr@UJoinRankTimeCalculatorImpl@JoinRankTimeCalculator@mu2@@U?$default_delete@UJoinRankTimeCalculatorImpl@JoinRankTimeCalculator@mu2@@@std@@@std@@QEAA@XZ ; std::unique_ptr<mu2::JoinRankTimeCalculator::JoinRankTimeCalculatorImpl,std::default_delete<mu2::JoinRankTimeCalculator::JoinRankTimeCalculatorImpl> >::~unique_ptr<mu2::JoinRankTimeCalculator::JoinRankTimeCalculatorImpl,std::default_delete<mu2::JoinRankTimeCalculator::JoinRankTimeCalculatorImpl> >
PUBLIC	??$fill_n@PEAI_KI@std@@YAPEAIPEAI_KAEBI@Z	; std::fill_n<unsigned int *,unsigned __int64,unsigned int>
PUBLIC	??$_Is_all_bits_zero@I@std@@YA_NAEBI@Z		; std::_Is_all_bits_zero<unsigned int>
PUBLIC	??$_Fill_zero_memset@PEAI@std@@YAXPEAI_K@Z	; std::_Fill_zero_memset<unsigned int *>
PUBLIC	??0JoinRankTimeCalculatorImpl@JoinRankTimeCalculator@mu2@@QEAA@XZ ; mu2::JoinRankTimeCalculator::JoinRankTimeCalculatorImpl::JoinRankTimeCalculatorImpl
PUBLIC	??_C@_09OLBBGJEO@Assertion@			; `string'
PUBLIC	??_C@_0FF@EEEJICA@F?3?2Release_Branch?2Server?2Develo@ ; `string'
PUBLIC	??_C@_08LECHLKNC@0?5?$DM?5time@			; `string'
PUBLIC	??_C@_0CH@LKLFFGBB@index?5?$DM?5m_impl?9?$DOtimeCapture?4max@ ; `string'
PUBLIC	__real@3a83126f
EXTRN	??2@YAPEAX_K@Z:PROC				; operator new
EXTRN	??3@YAXPEAX_K@Z:PROC				; operator delete
EXTRN	memcmp:PROC
EXTRN	memset:PROC
EXTRN	__imp_GetTickCount:PROC
EXTRN	?LogRuntimeAssertionFailure@mu2@@YAXPEBD00_K@Z:PROC ; mu2::LogRuntimeAssertionFailure
EXTRN	__CxxFrameHandler4:PROC
EXTRN	_fltused:DWORD
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0JoinRankTimeCalculator@mu2@@QEAA@XZ DD imagerel $LN22
	DD	imagerel $LN22+134
	DD	imagerel $unwind$??0JoinRankTimeCalculator@mu2@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???0JoinRankTimeCalculator@mu2@@QEAA@XZ@4HA DD imagerel ?dtor$0@?0???0JoinRankTimeCalculator@mu2@@QEAA@XZ@4HA
	DD	imagerel ?dtor$0@?0???0JoinRankTimeCalculator@mu2@@QEAA@XZ@4HA+29
	DD	imagerel $unwind$?dtor$0@?0???0JoinRankTimeCalculator@mu2@@QEAA@XZ@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1JoinRankTimeCalculator@mu2@@QEAA@XZ DD imagerel $LN20
	DD	imagerel $LN20+28
	DD	imagerel $unwind$??1JoinRankTimeCalculator@mu2@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?EnterWait@JoinRankTimeCalculator@mu2@@QEAAII@Z DD imagerel $LN4
	DD	imagerel $LN4+92
	DD	imagerel $unwind$?EnterWait@JoinRankTimeCalculator@mu2@@QEAAII@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?LeaveWait@JoinRankTimeCalculator@mu2@@QEAAXXZ DD imagerel $LN15
	DD	imagerel $LN15+187
	DD	imagerel $unwind$?LeaveWait@JoinRankTimeCalculator@mu2@@QEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?GetTime@JoinRankTimeCalculator@mu2@@QEBAII@Z DD imagerel $LN8
	DD	imagerel $LN8+42
	DD	imagerel $unwind$?GetTime@JoinRankTimeCalculator@mu2@@QEBAII@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?updateLastEnterWaitTick@JoinRankTimeCalculator@mu2@@AEAAXI@Z DD imagerel $LN8
	DD	imagerel $LN8+40
	DD	imagerel $unwind$?updateLastEnterWaitTick@JoinRankTimeCalculator@mu2@@AEAAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?updateLastLeaveWaitTick@JoinRankTimeCalculator@mu2@@AEAAXI@Z DD imagerel $LN8
	DD	imagerel $LN8+41
	DD	imagerel $unwind$?updateLastLeaveWaitTick@JoinRankTimeCalculator@mu2@@AEAAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?updateTimeCapture@JoinRankTimeCalculator@mu2@@AEAAXI@Z DD imagerel $LN25
	DD	imagerel $LN25+184
	DD	imagerel $unwind$?updateTimeCapture@JoinRankTimeCalculator@mu2@@AEAAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?checkReset@JoinRankTimeCalculator@mu2@@AEAA_NXZ DD imagerel $LN21
	DD	imagerel $LN21+112
	DD	imagerel $unwind$?checkReset@JoinRankTimeCalculator@mu2@@AEAA_NXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?reset@JoinRankTimeCalculator@mu2@@AEAAXXZ DD imagerel $LN48
	DD	imagerel $LN48+139
	DD	imagerel $unwind$?reset@JoinRankTimeCalculator@mu2@@AEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?calc@JoinRankTimeCalculator@mu2@@AEAAXXZ DD imagerel $LN28
	DD	imagerel $LN28+225
	DD	imagerel $unwind$?calc@JoinRankTimeCalculator@mu2@@AEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?getTimeCaptureIndex@JoinRankTimeCalculator@mu2@@AEBA_KXZ DD imagerel $LN13
	DD	imagerel $LN13+83
	DD	imagerel $unwind$?getTimeCaptureIndex@JoinRankTimeCalculator@mu2@@AEBA_KXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$unique_ptr@UJoinRankTimeCalculatorImpl@JoinRankTimeCalculator@mu2@@U?$default_delete@UJoinRankTimeCalculatorImpl@JoinRankTimeCalculator@mu2@@@std@@@std@@QEAA@XZ DD imagerel $LN15
	DD	imagerel $LN15+74
	DD	imagerel $unwind$??1?$unique_ptr@UJoinRankTimeCalculatorImpl@JoinRankTimeCalculator@mu2@@U?$default_delete@UJoinRankTimeCalculatorImpl@JoinRankTimeCalculator@mu2@@@std@@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$fill_n@PEAI_KI@std@@YAPEAIPEAI_KAEBI@Z DD imagerel $LN24
	DD	imagerel $LN24+229
	DD	imagerel $unwind$??$fill_n@PEAI_KI@std@@YAPEAIPEAI_KAEBI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Is_all_bits_zero@I@std@@YA_NAEBI@Z DD imagerel $LN5
	DD	imagerel $LN5+70
	DD	imagerel $unwind$??$_Is_all_bits_zero@I@std@@YA_NAEBI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Fill_zero_memset@PEAI@std@@YAXPEAI_K@Z DD imagerel $LN8
	DD	imagerel $LN8+54
	DD	imagerel $unwind$??$_Fill_zero_memset@PEAI@std@@YAXPEAI_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0JoinRankTimeCalculatorImpl@JoinRankTimeCalculator@mu2@@QEAA@XZ DD imagerel $LN28
	DD	imagerel $LN28+103
	DD	imagerel $unwind$??0JoinRankTimeCalculatorImpl@JoinRankTimeCalculator@mu2@@QEAA@XZ
pdata	ENDS
;	COMDAT __real@3a83126f
CONST	SEGMENT
__real@3a83126f DD 03a83126fr			; 0.001
CONST	ENDS
;	COMDAT ??_C@_0CH@LKLFFGBB@index?5?$DM?5m_impl?9?$DOtimeCapture?4max@
CONST	SEGMENT
??_C@_0CH@LKLFFGBB@index?5?$DM?5m_impl?9?$DOtimeCapture?4max@ DB 'index <'
	DB	' m_impl->timeCapture.max_size()', 00H	; `string'
CONST	ENDS
;	COMDAT ??_C@_08LECHLKNC@0?5?$DM?5time@
CONST	SEGMENT
??_C@_08LECHLKNC@0?5?$DM?5time@ DB '0 < time', 00H	; `string'
CONST	ENDS
;	COMDAT ??_C@_0FF@EEEJICA@F?3?2Release_Branch?2Server?2Develo@
CONST	SEGMENT
??_C@_0FF@EEEJICA@F?3?2Release_Branch?2Server?2Develo@ DB 'F:\Release_Bra'
	DB	'nch\Server\Development\Frontend\WorldServer\JoinRankTimeCalcu'
	DB	'lator.cpp', 00H				; `string'
CONST	ENDS
;	COMDAT ??_C@_09OLBBGJEO@Assertion@
CONST	SEGMENT
??_C@_09OLBBGJEO@Assertion@ DB 'Assertion', 00H		; `string'
CONST	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0JoinRankTimeCalculatorImpl@JoinRankTimeCalculator@mu2@@QEAA@XZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Fill_zero_memset@PEAI@std@@YAXPEAI_K@Z DD 010e01H
	DD	0620eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Is_all_bits_zero@I@std@@YA_NAEBI@Z DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$fill_n@PEAI_KI@std@@YAPEAIPEAI_KAEBI@Z DD 011301H
	DD	0a213H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$unique_ptr@UJoinRankTimeCalculatorImpl@JoinRankTimeCalculator@mu2@@U?$default_delete@UJoinRankTimeCalculatorImpl@JoinRankTimeCalculator@mu2@@@std@@@std@@QEAA@XZ DD 010901H
	DD	08209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?getTimeCaptureIndex@JoinRankTimeCalculator@mu2@@AEBA_KXZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?calc@JoinRankTimeCalculator@mu2@@AEAAXXZ DD 010901H
	DD	0a209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?reset@JoinRankTimeCalculator@mu2@@AEAAXXZ DD 010901H
	DD	0a209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?checkReset@JoinRankTimeCalculator@mu2@@AEAA_NXZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?updateTimeCapture@JoinRankTimeCalculator@mu2@@AEAAXI@Z DD 010d01H
	DD	0a20dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?updateLastLeaveWaitTick@JoinRankTimeCalculator@mu2@@AEAAXI@Z DD 010d01H
	DD	0220dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?updateLastEnterWaitTick@JoinRankTimeCalculator@mu2@@AEAAXI@Z DD 010d01H
	DD	0220dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?GetTime@JoinRankTimeCalculator@mu2@@QEBAII@Z DD 010d01H
	DD	0220dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?LeaveWait@JoinRankTimeCalculator@mu2@@QEAAXXZ DD 010901H
	DD	0a209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?EnterWait@JoinRankTimeCalculator@mu2@@QEAAII@Z DD 010d01H
	DD	0620dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1JoinRankTimeCalculator@mu2@@QEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???0JoinRankTimeCalculator@mu2@@QEAA@XZ@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??0JoinRankTimeCalculator@mu2@@QEAA@XZ DB 06H
	DB	00H
	DB	00H
	DB	'0'
	DB	02H
	DB	'X'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??0JoinRankTimeCalculator@mu2@@QEAA@XZ DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???0JoinRankTimeCalculator@mu2@@QEAA@XZ@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??0JoinRankTimeCalculator@mu2@@QEAA@XZ DB 028H
	DD	imagerel $stateUnwindMap$??0JoinRankTimeCalculator@mu2@@QEAA@XZ
	DD	imagerel $ip2state$??0JoinRankTimeCalculator@mu2@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0JoinRankTimeCalculator@mu2@@QEAA@XZ DD 010911H
	DD	0c209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??0JoinRankTimeCalculator@mu2@@QEAA@XZ
xdata	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\array
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp
;	COMDAT ??0JoinRankTimeCalculatorImpl@JoinRankTimeCalculator@mu2@@QEAA@XZ
_TEXT	SEGMENT
$T1 = 32
this$ = 64
??0JoinRankTimeCalculatorImpl@JoinRankTimeCalculator@mu2@@QEAA@XZ PROC ; mu2::JoinRankTimeCalculator::JoinRankTimeCalculatorImpl::JoinRankTimeCalculatorImpl, COMDAT

; 27   : {

$LN28:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 19   : 	Tick				lastEnterWaitTick	= 0;

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	c7 00 00 00 00
	00		 mov	 DWORD PTR [rax], 0

; 20   : 	Tick				lastLeaveWaitTick	= 0;

  00014	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00019	c7 40 04 00 00
	00 00		 mov	 DWORD PTR [rax+4], 0

; 21   : 	UInt32				averageJoinWaitSec	= 10;

  00020	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00025	c7 40 08 0a 00
	00 00		 mov	 DWORD PTR [rax+8], 10

; 22   : 	size_t				timeCaptureIndex	= 0;

  0002c	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00031	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 28   : 	timeCapture.fill( 0 );

  00039	c7 44 24 20 00
	00 00 00	 mov	 DWORD PTR $T1[rsp], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\array

; 430  :         _STD fill_n(_Elems, _Size, _Value);

  00041	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00046	48 83 c0 18	 add	 rax, 24
  0004a	4c 8d 44 24 20	 lea	 r8, QWORD PTR $T1[rsp]
  0004f	ba 05 00 00 00	 mov	 edx, 5
  00054	48 8b c8	 mov	 rcx, rax
  00057	e8 00 00 00 00	 call	 ??$fill_n@PEAI_KI@std@@YAPEAIPEAI_KAEBI@Z ; std::fill_n<unsigned int *,unsigned __int64,unsigned int>
  0005c	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp

; 29   : }

  0005d	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00062	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00066	c3		 ret	 0
??0JoinRankTimeCalculatorImpl@JoinRankTimeCalculator@mu2@@QEAA@XZ ENDP ; mu2::JoinRankTimeCalculator::JoinRankTimeCalculatorImpl::JoinRankTimeCalculatorImpl
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
;	COMDAT ??$_Fill_zero_memset@PEAI@std@@YAXPEAI_K@Z
_TEXT	SEGMENT
$T1 = 32
_Dest$ = 64
_Count$ = 72
??$_Fill_zero_memset@PEAI@std@@YAXPEAI_K@Z PROC		; std::_Fill_zero_memset<unsigned int *>, COMDAT

; 5269 : void _Fill_zero_memset(_CtgIt _Dest, const size_t _Count) {

$LN8:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 4627 :     return _Val;

  0000e	48 8b 44 24 40	 mov	 rax, QWORD PTR _Dest$[rsp]
  00013	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax

; 5270 :     _CSTD memset(_STD _To_address(_Dest), 0, _Count * sizeof(_Iter_value_t<_CtgIt>));

  00018	48 8b 44 24 48	 mov	 rax, QWORD PTR _Count$[rsp]
  0001d	48 c1 e0 02	 shl	 rax, 2
  00021	48 8b 4c 24 20	 mov	 rcx, QWORD PTR $T1[rsp]
  00026	4c 8b c0	 mov	 r8, rax
  00029	33 d2		 xor	 edx, edx
  0002b	e8 00 00 00 00	 call	 memset
  00030	90		 npad	 1

; 5271 : }

  00031	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00035	c3		 ret	 0
??$_Fill_zero_memset@PEAI@std@@YAXPEAI_K@Z ENDP		; std::_Fill_zero_memset<unsigned int *>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
;	COMDAT ??$_Is_all_bits_zero@I@std@@YA_NAEBI@Z
_TEXT	SEGMENT
tv69 = 32
_Zero$1 = 36
_Val$ = 64
??$_Is_all_bits_zero@I@std@@YA_NAEBI@Z PROC		; std::_Is_all_bits_zero<unsigned int>, COMDAT

; 5274 : _NODISCARD bool _Is_all_bits_zero(const _Ty& _Val) {

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 5275 :     // checks if scalar type has all bits set to zero
; 5276 :     _STL_INTERNAL_STATIC_ASSERT(is_scalar_v<_Ty> && !is_member_pointer_v<_Ty>);
; 5277 :     if constexpr (is_same_v<_Ty, nullptr_t>) {
; 5278 :         return true;
; 5279 :     } else {
; 5280 :         constexpr _Ty _Zero{};

  00009	c7 44 24 24 00
	00 00 00	 mov	 DWORD PTR _Zero$1[rsp], 0

; 5281 :         return _CSTD memcmp(&_Val, &_Zero, sizeof(_Ty)) == 0;

  00011	41 b8 04 00 00
	00		 mov	 r8d, 4
  00017	48 8d 54 24 24	 lea	 rdx, QWORD PTR _Zero$1[rsp]
  0001c	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Val$[rsp]
  00021	e8 00 00 00 00	 call	 memcmp
  00026	85 c0		 test	 eax, eax
  00028	75 0a		 jne	 SHORT $LN3@Is_all_bit
  0002a	c7 44 24 20 01
	00 00 00	 mov	 DWORD PTR tv69[rsp], 1
  00032	eb 08		 jmp	 SHORT $LN4@Is_all_bit
$LN3@Is_all_bit:
  00034	c7 44 24 20 00
	00 00 00	 mov	 DWORD PTR tv69[rsp], 0
$LN4@Is_all_bit:
  0003c	0f b6 44 24 20	 movzx	 eax, BYTE PTR tv69[rsp]

; 5282 :     }
; 5283 : }

  00041	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00045	c3		 ret	 0
??$_Is_all_bits_zero@I@std@@YA_NAEBI@Z ENDP		; std::_Is_all_bits_zero<unsigned int>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
;	COMDAT ??$fill_n@PEAI_KI@std@@YAPEAIPEAI_KAEBI@Z
_TEXT	SEGMENT
_Count$ = 32
_UDest$1 = 40
$T2 = 48
$T3 = 56
$T4 = 64
$T5 = 72
_Dest$ = 96
_Count_raw$ = 104
_Val$ = 112
??$fill_n@PEAI_KI@std@@YAPEAIPEAI_KAEBI@Z PROC		; std::fill_n<unsigned int *,unsigned __int64,unsigned int>, COMDAT

; 5326 : _CONSTEXPR20 _OutIt fill_n(_OutIt _Dest, const _Diff _Count_raw, const _Ty& _Val) {

$LN24:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 5327 :     // copy _Val _Count times through [_Dest, ...)
; 5328 :     _Algorithm_int_t<_Diff> _Count = _Count_raw;

  00013	48 8b 44 24 68	 mov	 rax, QWORD PTR _Count_raw$[rsp]
  00018	48 89 44 24 20	 mov	 QWORD PTR _Count$[rsp], rax

; 5329 :     if (0 < _Count) {

  0001d	48 83 7c 24 20
	00		 cmp	 QWORD PTR _Count$[rsp], 0
  00023	0f 86 b2 00 00
	00		 jbe	 $LN5@fill_n

; 1445 :         return _It + 0;

  00029	48 8b 44 24 60	 mov	 rax, QWORD PTR _Dest$[rsp]
  0002e	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax

; 5330 :         if constexpr (_Is_vb_iterator<_OutIt, true>) {
; 5331 :             const auto _Last = _Dest + static_cast<typename _OutIt::difference_type>(_Count);
; 5332 :             _STD _Fill_vbool(_Dest, _Last, _Val);
; 5333 :             return _Last;
; 5334 :         } else {
; 5335 :             auto _UDest = _STD _Get_unwrapped_n(_Dest, _Count);

  00033	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
  00038	48 89 44 24 28	 mov	 QWORD PTR _UDest$1[rsp], rax

; 5336 : #if _HAS_CXX20
; 5337 :             if (!_STD is_constant_evaluated())
; 5338 : #endif // _HAS_CXX20
; 5339 :             {
; 5340 :                 if constexpr (_Fill_memset_is_safe<decltype(_UDest), _Ty>) {
; 5341 :                     _STD _Fill_memset(_UDest, _Val, static_cast<size_t>(_Count));
; 5342 :                     _STD _Seek_wrapped(_Dest, _UDest + _Count);
; 5343 :                     return _Dest;
; 5344 :                 } else if constexpr (_Fill_zero_memset_is_safe<decltype(_UDest), _Ty>) {
; 5345 :                     if (_STD _Is_all_bits_zero(_Val)) {

  0003d	48 8b 4c 24 70	 mov	 rcx, QWORD PTR _Val$[rsp]
  00042	e8 00 00 00 00	 call	 ??$_Is_all_bits_zero@I@std@@YA_NAEBI@Z ; std::_Is_all_bits_zero<unsigned int>
  00047	0f b6 c0	 movzx	 eax, al
  0004a	85 c0		 test	 eax, eax
  0004c	74 41		 je	 SHORT $LN6@fill_n

; 5346 :                         _STD _Fill_zero_memset(_UDest, static_cast<size_t>(_Count));

  0004e	48 8b 54 24 20	 mov	 rdx, QWORD PTR _Count$[rsp]
  00053	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _UDest$1[rsp]
  00058	e8 00 00 00 00	 call	 ??$_Fill_zero_memset@PEAI@std@@YAXPEAI_K@Z ; std::_Fill_zero_memset<unsigned int *>
  0005d	90		 npad	 1

; 5347 :                         _STD _Seek_wrapped(_Dest, _UDest + _Count);

  0005e	48 8b 44 24 28	 mov	 rax, QWORD PTR _UDest$1[rsp]
  00063	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Count$[rsp]
  00068	48 8d 04 88	 lea	 rax, QWORD PTR [rax+rcx*4]
  0006c	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00071	48 8d 44 24 38	 lea	 rax, QWORD PTR $T3[rsp]
  00076	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 1481 :         _It = _STD forward<_UIter>(_UIt);

  0007b	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  00080	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00083	48 89 44 24 60	 mov	 QWORD PTR _Dest$[rsp], rax

; 5348 :                         return _Dest;

  00088	48 8b 44 24 60	 mov	 rax, QWORD PTR _Dest$[rsp]
  0008d	eb 51		 jmp	 SHORT $LN1@fill_n
$LN6@fill_n:

; 5349 :                     }
; 5350 :                 }
; 5351 :             }
; 5352 : 
; 5353 :             for (; 0 < _Count; --_Count, (void) ++_UDest) {

  0008f	eb 1b		 jmp	 SHORT $LN4@fill_n
$LN2@fill_n:
  00091	48 8b 44 24 20	 mov	 rax, QWORD PTR _Count$[rsp]
  00096	48 ff c8	 dec	 rax
  00099	48 89 44 24 20	 mov	 QWORD PTR _Count$[rsp], rax
  0009e	48 8b 44 24 28	 mov	 rax, QWORD PTR _UDest$1[rsp]
  000a3	48 83 c0 04	 add	 rax, 4
  000a7	48 89 44 24 28	 mov	 QWORD PTR _UDest$1[rsp], rax
$LN4@fill_n:
  000ac	48 83 7c 24 20
	00		 cmp	 QWORD PTR _Count$[rsp], 0
  000b2	76 10		 jbe	 SHORT $LN3@fill_n

; 5354 :                 *_UDest = _Val;

  000b4	48 8b 44 24 28	 mov	 rax, QWORD PTR _UDest$1[rsp]
  000b9	48 8b 4c 24 70	 mov	 rcx, QWORD PTR _Val$[rsp]
  000be	8b 09		 mov	 ecx, DWORD PTR [rcx]
  000c0	89 08		 mov	 DWORD PTR [rax], ecx

; 5355 :             }

  000c2	eb cd		 jmp	 SHORT $LN2@fill_n
$LN3@fill_n:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  000c4	48 8d 44 24 28	 lea	 rax, QWORD PTR _UDest$1[rsp]
  000c9	48 89 44 24 48	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 1481 :         _It = _STD forward<_UIter>(_UIt);

  000ce	48 8b 44 24 48	 mov	 rax, QWORD PTR $T5[rsp]
  000d3	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000d6	48 89 44 24 60	 mov	 QWORD PTR _Dest$[rsp], rax
$LN5@fill_n:

; 5356 : 
; 5357 :             _STD _Seek_wrapped(_Dest, _UDest);
; 5358 :         }
; 5359 :     }
; 5360 :     return _Dest;

  000db	48 8b 44 24 60	 mov	 rax, QWORD PTR _Dest$[rsp]
$LN1@fill_n:

; 5361 : }

  000e0	48 83 c4 58	 add	 rsp, 88			; 00000058H
  000e4	c3		 ret	 0
??$fill_n@PEAI_KI@std@@YAPEAIPEAI_KAEBI@Z ENDP		; std::fill_n<unsigned int *,unsigned __int64,unsigned int>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ??1?$unique_ptr@UJoinRankTimeCalculatorImpl@JoinRankTimeCalculator@mu2@@U?$default_delete@UJoinRankTimeCalculatorImpl@JoinRankTimeCalculator@mu2@@@std@@@std@@QEAA@XZ
_TEXT	SEGMENT
_Ptr$ = 32
$T1 = 40
$T2 = 48
this$ = 80
??1?$unique_ptr@UJoinRankTimeCalculatorImpl@JoinRankTimeCalculator@mu2@@U?$default_delete@UJoinRankTimeCalculatorImpl@JoinRankTimeCalculator@mu2@@@std@@@std@@QEAA@XZ PROC ; std::unique_ptr<mu2::JoinRankTimeCalculator::JoinRankTimeCalculatorImpl,std::default_delete<mu2::JoinRankTimeCalculator::JoinRankTimeCalculatorImpl> >::~unique_ptr<mu2::JoinRankTimeCalculator::JoinRankTimeCalculatorImpl,std::default_delete<mu2::JoinRankTimeCalculator::JoinRankTimeCalculatorImpl> >, COMDAT

; 3425 :     _CONSTEXPR23 ~unique_ptr() noexcept {

$LN15:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 3426 :         if (_Mypair._Myval2) {

  00009	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 38 00	 cmp	 QWORD PTR [rax], 0
  00012	74 31		 je	 SHORT $LN2@unique_ptr

; 3427 :             _Mypair._Get_first()(_Mypair._Myval2);

  00014	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00019	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3427 :             _Mypair._Get_first()(_Mypair._Myval2);

  0001e	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00026	48 89 44 24 20	 mov	 QWORD PTR _Ptr$[rsp], rax

; 3309 :         delete _Ptr;

  0002b	48 8b 44 24 20	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00030	48 89 44 24 28	 mov	 QWORD PTR $T1[rsp], rax
  00035	ba 30 00 00 00	 mov	 edx, 48			; 00000030H
  0003a	48 8b 4c 24 28	 mov	 rcx, QWORD PTR $T1[rsp]
  0003f	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00044	90		 npad	 1
$LN2@unique_ptr:

; 3428 :         }
; 3429 : 
; 3430 : #if _MSVC_STL_DESTRUCTOR_TOMBSTONES
; 3431 :         if constexpr (is_pointer_v<pointer>) {
; 3432 :             if (!_STD _Is_constant_evaluated()) {
; 3433 :                 const auto _Tombstone{reinterpret_cast<pointer>(_MSVC_STL_UINTPTR_TOMBSTONE_VALUE)};
; 3434 :                 _Mypair._Myval2 = _Tombstone;
; 3435 :             }
; 3436 :         }
; 3437 : #endif // _MSVC_STL_DESTRUCTOR_TOMBSTONES
; 3438 :     }

  00045	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00049	c3		 ret	 0
??1?$unique_ptr@UJoinRankTimeCalculatorImpl@JoinRankTimeCalculator@mu2@@U?$default_delete@UJoinRankTimeCalculatorImpl@JoinRankTimeCalculator@mu2@@@std@@@std@@QEAA@XZ ENDP ; std::unique_ptr<mu2::JoinRankTimeCalculator::JoinRankTimeCalculatorImpl,std::default_delete<mu2::JoinRankTimeCalculator::JoinRankTimeCalculatorImpl> >::~unique_ptr<mu2::JoinRankTimeCalculator::JoinRankTimeCalculatorImpl,std::default_delete<mu2::JoinRankTimeCalculator::JoinRankTimeCalculatorImpl> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp
;	COMDAT ?getTimeCaptureIndex@JoinRankTimeCalculator@mu2@@AEBA_KXZ
_TEXT	SEGMENT
tv73 = 0
$T1 = 8
$T2 = 16
this$ = 48
?getTimeCaptureIndex@JoinRankTimeCalculator@mu2@@AEBA_KXZ PROC ; mu2::JoinRankTimeCalculator::getTimeCaptureIndex, COMDAT

; 156  : {

$LN13:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3453 :         return _Mypair._Myval2;

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00011	48 89 44 24 08	 mov	 QWORD PTR $T1[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp

; 157  : 	return (m_impl->timeCaptureIndex = ((m_impl->timeCaptureIndex + 1) % Limits::JOIN_RANK_TIME_CALC_CAPTURE_COUNT));

  00016	48 8b 44 24 08	 mov	 rax, QWORD PTR $T1[rsp]
  0001b	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0001f	48 ff c0	 inc	 rax
  00022	33 d2		 xor	 edx, edx
  00024	b9 05 00 00 00	 mov	 ecx, 5
  00029	48 f7 f1	 div	 rcx
  0002c	48 89 14 24	 mov	 QWORD PTR tv73[rsp], rdx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3453 :         return _Mypair._Myval2;

  00030	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00035	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00038	48 89 44 24 10	 mov	 QWORD PTR $T2[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp

; 157  : 	return (m_impl->timeCaptureIndex = ((m_impl->timeCaptureIndex + 1) % Limits::JOIN_RANK_TIME_CALC_CAPTURE_COUNT));

  0003d	48 8b 44 24 10	 mov	 rax, QWORD PTR $T2[rsp]
  00042	48 8b 0c 24	 mov	 rcx, QWORD PTR tv73[rsp]
  00046	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx
  0004a	48 8b 04 24	 mov	 rax, QWORD PTR tv73[rsp]

; 158  : }

  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
?getTimeCaptureIndex@JoinRankTimeCalculator@mu2@@AEBA_KXZ ENDP ; mu2::JoinRankTimeCalculator::getTimeCaptureIndex
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\array
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\array
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp
;	COMDAT ?calc@JoinRankTimeCalculator@mu2@@AEAAXXZ
_TEXT	SEGMENT
<begin>$L0$1 = 0
count$ = 8
sum$ = 16
<range>$L0$2 = 24
it$3 = 32
$T4 = 40
$T5 = 48
$T6 = 56
<end>$L0$7 = 64
$T8 = 72
this$ = 96
?calc@JoinRankTimeCalculator@mu2@@AEAAXXZ PROC		; mu2::JoinRankTimeCalculator::calc, COMDAT

; 122  : {

$LN28:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 123  : 	UInt64 sum = 0;

  00009	48 c7 44 24 10
	00 00 00 00	 mov	 QWORD PTR sum$[rsp], 0

; 124  : 	UInt64 count = 0;

  00012	48 c7 44 24 08
	00 00 00 00	 mov	 QWORD PTR count$[rsp], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3453 :         return _Mypair._Myval2;

  0001b	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00020	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00023	48 89 44 24 28	 mov	 QWORD PTR $T4[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp

; 125  : 	for (const auto& it : m_impl->timeCapture)

  00028	48 8b 44 24 28	 mov	 rax, QWORD PTR $T4[rsp]
  0002d	48 83 c0 18	 add	 rax, 24
  00031	48 89 44 24 18	 mov	 QWORD PTR <range>$L0$2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\array

; 486  :         return _Elems;

  00036	48 8b 44 24 18	 mov	 rax, QWORD PTR <range>$L0$2[rsp]
  0003b	48 89 44 24 30	 mov	 QWORD PTR $T5[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp

; 125  : 	for (const auto& it : m_impl->timeCapture)

  00040	48 8b 44 24 30	 mov	 rax, QWORD PTR $T5[rsp]
  00045	48 89 04 24	 mov	 QWORD PTR <begin>$L0$1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\array

; 494  :         return _Elems + _Size;

  00049	48 8b 44 24 18	 mov	 rax, QWORD PTR <range>$L0$2[rsp]
  0004e	48 83 c0 14	 add	 rax, 20
  00052	48 89 44 24 38	 mov	 QWORD PTR $T6[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp

; 125  : 	for (const auto& it : m_impl->timeCapture)

  00057	48 8b 44 24 38	 mov	 rax, QWORD PTR $T6[rsp]
  0005c	48 89 44 24 40	 mov	 QWORD PTR <end>$L0$7[rsp], rax
  00061	eb 0c		 jmp	 SHORT $LN4@calc
$LN2@calc:
  00063	48 8b 04 24	 mov	 rax, QWORD PTR <begin>$L0$1[rsp]
  00067	48 83 c0 04	 add	 rax, 4
  0006b	48 89 04 24	 mov	 QWORD PTR <begin>$L0$1[rsp], rax
$LN4@calc:
  0006f	48 8b 44 24 40	 mov	 rax, QWORD PTR <end>$L0$7[rsp]
  00074	48 39 04 24	 cmp	 QWORD PTR <begin>$L0$1[rsp], rax
  00078	74 39		 je	 SHORT $LN3@calc
  0007a	48 8b 04 24	 mov	 rax, QWORD PTR <begin>$L0$1[rsp]
  0007e	48 89 44 24 20	 mov	 QWORD PTR it$3[rsp], rax

; 126  : 	{
; 127  : 		if (it)

  00083	48 8b 44 24 20	 mov	 rax, QWORD PTR it$3[rsp]
  00088	83 38 00	 cmp	 DWORD PTR [rax], 0
  0008b	74 24		 je	 SHORT $LN5@calc

; 128  : 		{
; 129  : 			sum += it;

  0008d	48 8b 44 24 20	 mov	 rax, QWORD PTR it$3[rsp]
  00092	8b 00		 mov	 eax, DWORD PTR [rax]
  00094	48 8b 4c 24 10	 mov	 rcx, QWORD PTR sum$[rsp]
  00099	48 03 c8	 add	 rcx, rax
  0009c	48 8b c1	 mov	 rax, rcx
  0009f	48 89 44 24 10	 mov	 QWORD PTR sum$[rsp], rax

; 130  : 			++count;

  000a4	48 8b 44 24 08	 mov	 rax, QWORD PTR count$[rsp]
  000a9	48 ff c0	 inc	 rax
  000ac	48 89 44 24 08	 mov	 QWORD PTR count$[rsp], rax
$LN5@calc:

; 131  : 		}
; 132  : 	}

  000b1	eb b0		 jmp	 SHORT $LN2@calc
$LN3@calc:

; 133  : 
; 134  : 	if (count > 0)

  000b3	48 83 7c 24 08
	00		 cmp	 QWORD PTR count$[rsp], 0
  000b9	76 21		 jbe	 SHORT $LN6@calc
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3453 :         return _Mypair._Myval2;

  000bb	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  000c0	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000c3	48 89 44 24 48	 mov	 QWORD PTR $T8[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp

; 136  : 		m_impl->averageJoinWaitSec = static_cast<UInt32>( sum / count);

  000c8	33 d2		 xor	 edx, edx
  000ca	48 8b 44 24 10	 mov	 rax, QWORD PTR sum$[rsp]
  000cf	48 f7 74 24 08	 div	 QWORD PTR count$[rsp]
  000d4	48 8b 4c 24 48	 mov	 rcx, QWORD PTR $T8[rsp]
  000d9	89 41 08	 mov	 DWORD PTR [rcx+8], eax
$LN6@calc:

; 137  : 	}
; 138  : 
; 139  : 	/*
; 140  : 	auto it = std::find( m_impl->timeCapture.begin(), m_impl->timeCapture.end(), 0 );
; 141  : 
; 142  : 	UInt32 value = std::accumulate( m_impl->timeCapture.begin(), it, 0, std::bind( std::plus< UInt32 >(), std::placeholders::_1, std::placeholders::_2 ) );
; 143  : 	if( 0 < value )
; 144  : 	{
; 145  : 	UInt32 count = static_cast< UInt32 >( std::distance( m_impl->timeCapture.begin(), it ) );
; 146  : 	MU2_ASSERT( 0 < count );
; 147  : 
; 148  : 	value /= count;
; 149  : 	}
; 150  : 
; 151  : 	m_impl->averageJoinWaitSec = value;
; 152  : 	*/
; 153  : }

  000dc	48 83 c4 58	 add	 rsp, 88			; 00000058H
  000e0	c3		 ret	 0
?calc@JoinRankTimeCalculator@mu2@@AEAAXXZ ENDP		; mu2::JoinRankTimeCalculator::calc
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\array
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp
;	COMDAT ?reset@JoinRankTimeCalculator@mu2@@AEAAXXZ
_TEXT	SEGMENT
$T1 = 32
$T2 = 40
$T3 = 48
$T4 = 56
$T5 = 64
this$ = 96
?reset@JoinRankTimeCalculator@mu2@@AEAAXXZ PROC		; mu2::JoinRankTimeCalculator::reset, COMDAT

; 113  : {

$LN48:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 58	 sub	 rsp, 88			; 00000058H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3453 :         return _Mypair._Myval2;

  00009	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00011	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp

; 114  : 	m_impl->lastLeaveWaitTick	= 0;

  00016	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
  0001b	c7 40 04 00 00
	00 00		 mov	 DWORD PTR [rax+4], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3453 :         return _Mypair._Myval2;

  00022	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00027	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0002a	48 89 44 24 30	 mov	 QWORD PTR $T3[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp

; 115  : 	m_impl->averageJoinWaitSec	= 0;

  0002f	48 8b 44 24 30	 mov	 rax, QWORD PTR $T3[rsp]
  00034	c7 40 08 00 00
	00 00		 mov	 DWORD PTR [rax+8], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3453 :         return _Mypair._Myval2;

  0003b	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00040	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00043	48 89 44 24 38	 mov	 QWORD PTR $T4[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp

; 116  : 	m_impl->timeCaptureIndex	= Limits::JOIN_RANK_TIME_CALC_CAPTURE_COUNT - 1;

  00048	48 8b 44 24 38	 mov	 rax, QWORD PTR $T4[rsp]
  0004d	48 c7 40 10 04
	00 00 00	 mov	 QWORD PTR [rax+16], 4

; 117  : 
; 118  : 	m_impl->timeCapture.fill( 0 );

  00055	c7 44 24 20 00
	00 00 00	 mov	 DWORD PTR $T1[rsp], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3453 :         return _Mypair._Myval2;

  0005d	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00062	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00065	48 89 44 24 40	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\array

; 430  :         _STD fill_n(_Elems, _Size, _Value);

  0006a	48 8b 44 24 40	 mov	 rax, QWORD PTR $T5[rsp]
  0006f	48 83 c0 18	 add	 rax, 24
  00073	4c 8d 44 24 20	 lea	 r8, QWORD PTR $T1[rsp]
  00078	ba 05 00 00 00	 mov	 edx, 5
  0007d	48 8b c8	 mov	 rcx, rax
  00080	e8 00 00 00 00	 call	 ??$fill_n@PEAI_KI@std@@YAPEAIPEAI_KAEBI@Z ; std::fill_n<unsigned int *,unsigned __int64,unsigned int>
  00085	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp

; 119  : }

  00086	48 83 c4 58	 add	 rsp, 88			; 00000058H
  0008a	c3		 ret	 0
?reset@JoinRankTimeCalculator@mu2@@AEAAXXZ ENDP		; mu2::JoinRankTimeCalculator::reset
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp
;	COMDAT ?checkReset@JoinRankTimeCalculator@mu2@@AEAA_NXZ
_TEXT	SEGMENT
tv79 = 0
$T1 = 8
$T2 = 16
$T3 = 24
this$ = 48
?checkReset@JoinRankTimeCalculator@mu2@@AEAA_NXZ PROC	; mu2::JoinRankTimeCalculator::checkReset, COMDAT

; 101  : {

$LN21:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3453 :         return _Mypair._Myval2;

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00011	48 89 44 24 08	 mov	 QWORD PTR $T1[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp

; 103  : 	if( 0 == m_impl->lastLeaveWaitTick )

  00016	48 8b 44 24 08	 mov	 rax, QWORD PTR $T1[rsp]
  0001b	83 78 04 00	 cmp	 DWORD PTR [rax+4], 0
  0001f	75 04		 jne	 SHORT $LN2@checkReset

; 104  : 	{
; 105  : 		return false;

  00021	32 c0		 xor	 al, al
  00023	eb 46		 jmp	 SHORT $LN1@checkReset
$LN2@checkReset:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3453 :         return _Mypair._Myval2;

  00025	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0002a	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0002d	48 89 44 24 10	 mov	 QWORD PTR $T2[rsp], rax
  00032	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00037	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0003a	48 89 44 24 18	 mov	 QWORD PTR $T3[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp

; 109  : 	return Limits::JOIN_RANK_TIME_CALC_RESET_INTERVAL < m_impl->lastEnterWaitTick - m_impl->lastLeaveWaitTick;

  0003f	48 8b 44 24 10	 mov	 rax, QWORD PTR $T2[rsp]
  00044	48 8b 4c 24 18	 mov	 rcx, QWORD PTR $T3[rsp]
  00049	8b 49 04	 mov	 ecx, DWORD PTR [rcx+4]
  0004c	8b 00		 mov	 eax, DWORD PTR [rax]
  0004e	2b c1		 sub	 eax, ecx
  00050	3d 80 cb a4 00	 cmp	 eax, 10800000		; 00a4cb80H
  00055	76 09		 jbe	 SHORT $LN4@checkReset
  00057	c7 04 24 01 00
	00 00		 mov	 DWORD PTR tv79[rsp], 1
  0005e	eb 07		 jmp	 SHORT $LN5@checkReset
$LN4@checkReset:
  00060	c7 04 24 00 00
	00 00		 mov	 DWORD PTR tv79[rsp], 0
$LN5@checkReset:
  00067	0f b6 04 24	 movzx	 eax, BYTE PTR tv79[rsp]
$LN1@checkReset:

; 110  : }

  0006b	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0006f	c3		 ret	 0
?checkReset@JoinRankTimeCalculator@mu2@@AEAA_NXZ ENDP	; mu2::JoinRankTimeCalculator::checkReset
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\array
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\array
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp
;	COMDAT ?updateTimeCapture@JoinRankTimeCalculator@mu2@@AEAAXI@Z
_TEXT	SEGMENT
index$ = 32
$T1 = 40
$T2 = 48
$T3 = 56
$T4 = 64
this$ = 96
time$ = 104
?updateTimeCapture@JoinRankTimeCalculator@mu2@@AEAAXI@Z PROC ; mu2::JoinRankTimeCalculator::updateTimeCapture, COMDAT

; 91   : {

$LN25:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 92   : 	MU2_ASSERT( 0 < time );

  0000d	83 7c 24 68 00	 cmp	 DWORD PTR time$[rsp], 0
  00012	77 21		 ja	 SHORT $LN2@updateTime
  00014	41 b9 5c 00 00
	00		 mov	 r9d, 92			; 0000005cH
  0001a	4c 8d 05 00 00
	00 00		 lea	 r8, OFFSET FLAT:??_C@_0FF@EEEJICA@F?3?2Release_Branch?2Server?2Develo@
  00021	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:??_C@_08LECHLKNC@0?5?$DM?5time@
  00028	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_09OLBBGJEO@Assertion@
  0002f	e8 00 00 00 00	 call	 ?LogRuntimeAssertionFailure@mu2@@YAXPEBD00_K@Z ; mu2::LogRuntimeAssertionFailure
  00034	90		 npad	 1
$LN2@updateTime:

; 93   : 
; 94   : 	size_t index = getTimeCaptureIndex();

  00035	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  0003a	e8 00 00 00 00	 call	 ?getTimeCaptureIndex@JoinRankTimeCalculator@mu2@@AEBA_KXZ ; mu2::JoinRankTimeCalculator::getTimeCaptureIndex
  0003f	48 89 44 24 20	 mov	 QWORD PTR index$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3453 :         return _Mypair._Myval2;

  00044	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00049	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0004c	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\array

; 506  :         return _Size;

  00051	48 c7 44 24 28
	05 00 00 00	 mov	 QWORD PTR $T1[rsp], 5
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp

; 95   : 	MU2_ASSERT( index < m_impl->timeCapture.max_size() );

  0005a	48 8b 44 24 28	 mov	 rax, QWORD PTR $T1[rsp]
  0005f	48 39 44 24 20	 cmp	 QWORD PTR index$[rsp], rax
  00064	72 21		 jb	 SHORT $LN3@updateTime
  00066	41 b9 5f 00 00
	00		 mov	 r9d, 95			; 0000005fH
  0006c	4c 8d 05 00 00
	00 00		 lea	 r8, OFFSET FLAT:??_C@_0FF@EEEJICA@F?3?2Release_Branch?2Server?2Develo@
  00073	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:??_C@_0CH@LKLFFGBB@index?5?$DM?5m_impl?9?$DOtimeCapture?4max@
  0007a	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_09OLBBGJEO@Assertion@
  00081	e8 00 00 00 00	 call	 ?LogRuntimeAssertionFailure@mu2@@YAXPEBD00_K@Z ; mu2::LogRuntimeAssertionFailure
  00086	90		 npad	 1
$LN3@updateTime:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3453 :         return _Mypair._Myval2;

  00087	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  0008c	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0008f	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\array

; 534  :         return _Elems[_Pos];

  00094	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
  00099	48 8b 4c 24 20	 mov	 rcx, QWORD PTR index$[rsp]
  0009e	48 8d 44 88 18	 lea	 rax, QWORD PTR [rax+rcx*4+24]
  000a3	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp

; 97   : 	m_impl->timeCapture[ index ] = time;

  000a8	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
  000ad	8b 4c 24 68	 mov	 ecx, DWORD PTR time$[rsp]
  000b1	89 08		 mov	 DWORD PTR [rax], ecx

; 98   : }

  000b3	48 83 c4 58	 add	 rsp, 88			; 00000058H
  000b7	c3		 ret	 0
?updateTimeCapture@JoinRankTimeCalculator@mu2@@AEAAXI@Z ENDP ; mu2::JoinRankTimeCalculator::updateTimeCapture
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp
;	COMDAT ?updateLastLeaveWaitTick@JoinRankTimeCalculator@mu2@@AEAAXI@Z
_TEXT	SEGMENT
$T1 = 0
this$ = 32
tick$ = 40
?updateLastLeaveWaitTick@JoinRankTimeCalculator@mu2@@AEAAXI@Z PROC ; mu2::JoinRankTimeCalculator::updateLastLeaveWaitTick, COMDAT

; 86   : {

$LN8:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 18	 sub	 rsp, 24
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3453 :         return _Mypair._Myval2;

  0000d	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00015	48 89 04 24	 mov	 QWORD PTR $T1[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp

; 87   : 	m_impl->lastLeaveWaitTick	= tick;

  00019	48 8b 04 24	 mov	 rax, QWORD PTR $T1[rsp]
  0001d	8b 4c 24 28	 mov	 ecx, DWORD PTR tick$[rsp]
  00021	89 48 04	 mov	 DWORD PTR [rax+4], ecx

; 88   : }

  00024	48 83 c4 18	 add	 rsp, 24
  00028	c3		 ret	 0
?updateLastLeaveWaitTick@JoinRankTimeCalculator@mu2@@AEAAXI@Z ENDP ; mu2::JoinRankTimeCalculator::updateLastLeaveWaitTick
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp
;	COMDAT ?updateLastEnterWaitTick@JoinRankTimeCalculator@mu2@@AEAAXI@Z
_TEXT	SEGMENT
$T1 = 0
this$ = 32
tick$ = 40
?updateLastEnterWaitTick@JoinRankTimeCalculator@mu2@@AEAAXI@Z PROC ; mu2::JoinRankTimeCalculator::updateLastEnterWaitTick, COMDAT

; 81   : {

$LN8:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 18	 sub	 rsp, 24
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3453 :         return _Mypair._Myval2;

  0000d	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00015	48 89 04 24	 mov	 QWORD PTR $T1[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp

; 82   : 	m_impl->lastEnterWaitTick	= tick;

  00019	48 8b 04 24	 mov	 rax, QWORD PTR $T1[rsp]
  0001d	8b 4c 24 28	 mov	 ecx, DWORD PTR tick$[rsp]
  00021	89 08		 mov	 DWORD PTR [rax], ecx

; 83   : }

  00023	48 83 c4 18	 add	 rsp, 24
  00027	c3		 ret	 0
?updateLastEnterWaitTick@JoinRankTimeCalculator@mu2@@AEAAXI@Z ENDP ; mu2::JoinRankTimeCalculator::updateLastEnterWaitTick
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp
;	COMDAT ?GetTime@JoinRankTimeCalculator@mu2@@QEBAII@Z
_TEXT	SEGMENT
$T1 = 0
this$ = 32
index$ = 40
?GetTime@JoinRankTimeCalculator@mu2@@QEBAII@Z PROC	; mu2::JoinRankTimeCalculator::GetTime, COMDAT

; 76   : {

$LN8:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 18	 sub	 rsp, 24
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3453 :         return _Mypair._Myval2;

  0000d	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00015	48 89 04 24	 mov	 QWORD PTR $T1[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp

; 77   : 	return m_impl->averageJoinWaitSec * index;

  00019	48 8b 04 24	 mov	 rax, QWORD PTR $T1[rsp]
  0001d	8b 40 08	 mov	 eax, DWORD PTR [rax+8]
  00020	0f af 44 24 28	 imul	 eax, DWORD PTR index$[rsp]

; 78   : }

  00025	48 83 c4 18	 add	 rsp, 24
  00029	c3		 ret	 0
?GetTime@JoinRankTimeCalculator@mu2@@QEBAII@Z ENDP	; mu2::JoinRankTimeCalculator::GetTime
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp
;	COMDAT ?LeaveWait@JoinRankTimeCalculator@mu2@@QEAAXXZ
_TEXT	SEGMENT
tick$ = 32
$T1 = 36
$T2 = 40
time$ = 44
tv83 = 48
$T3 = 56
$T4 = 64
$T5 = 72
this$ = 96
?LeaveWait@JoinRankTimeCalculator@mu2@@QEAAXXZ PROC	; mu2::JoinRankTimeCalculator::LeaveWait, COMDAT

; 59   : {

$LN15:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 60   : 	Tick tick = GetTickCount();

  00009	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetTickCount
  0000f	89 44 24 20	 mov	 DWORD PTR tick$[rsp], eax

; 61   : 
; 62   : 	// 마지막에 게임에 입장한 틱 저장
; 63   : 	updateLastLeaveWaitTick( tick );

  00013	8b 54 24 20	 mov	 edx, DWORD PTR tick$[rsp]
  00017	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  0001c	e8 00 00 00 00	 call	 ?updateLastLeaveWaitTick@JoinRankTimeCalculator@mu2@@AEAAXI@Z ; mu2::JoinRankTimeCalculator::updateLastLeaveWaitTick
  00021	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3453 :         return _Mypair._Myval2;

  00022	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00027	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0002a	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp

; 66   : 	UInt32 time = std::max< UInt32 >( 1, static_cast< UInt32 >( ( tick - m_impl->lastEnterWaitTick ) * 0.001f ) );

  0002f	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
  00034	8b 00		 mov	 eax, DWORD PTR [rax]
  00036	8b 4c 24 20	 mov	 ecx, DWORD PTR tick$[rsp]
  0003a	2b c8		 sub	 ecx, eax
  0003c	8b c1		 mov	 eax, ecx
  0003e	8b c0		 mov	 eax, eax
  00040	f3 48 0f 2a c0	 cvtsi2ss xmm0, rax
  00045	f3 0f 59 05 00
	00 00 00	 mulss	 xmm0, DWORD PTR __real@3a83126f
  0004d	f3 48 0f 2c c0	 cvttss2si rax, xmm0
  00052	89 44 24 24	 mov	 DWORD PTR $T1[rsp], eax
  00056	c7 44 24 28 01
	00 00 00	 mov	 DWORD PTR $T2[rsp], 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 77   :     return _Left < _Right ? _Right : _Left;

  0005e	8b 44 24 24	 mov	 eax, DWORD PTR $T1[rsp]
  00062	39 44 24 28	 cmp	 DWORD PTR $T2[rsp], eax
  00066	73 0c		 jae	 SHORT $LN10@LeaveWait
  00068	48 8d 44 24 24	 lea	 rax, QWORD PTR $T1[rsp]
  0006d	48 89 44 24 30	 mov	 QWORD PTR tv83[rsp], rax
  00072	eb 0a		 jmp	 SHORT $LN11@LeaveWait
$LN10@LeaveWait:
  00074	48 8d 44 24 28	 lea	 rax, QWORD PTR $T2[rsp]
  00079	48 89 44 24 30	 mov	 QWORD PTR tv83[rsp], rax
$LN11@LeaveWait:
  0007e	48 8b 44 24 30	 mov	 rax, QWORD PTR tv83[rsp]
  00083	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax
  00088	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  0008d	48 89 44 24 48	 mov	 QWORD PTR $T5[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp

; 66   : 	UInt32 time = std::max< UInt32 >( 1, static_cast< UInt32 >( ( tick - m_impl->lastEnterWaitTick ) * 0.001f ) );

  00092	48 8b 44 24 48	 mov	 rax, QWORD PTR $T5[rsp]
  00097	8b 00		 mov	 eax, DWORD PTR [rax]
  00099	89 44 24 2c	 mov	 DWORD PTR time$[rsp], eax

; 67   : 
; 68   : 	// 캡쳐 리스트에 반영
; 69   : 	updateTimeCapture( time );

  0009d	8b 54 24 2c	 mov	 edx, DWORD PTR time$[rsp]
  000a1	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  000a6	e8 00 00 00 00	 call	 ?updateTimeCapture@JoinRankTimeCalculator@mu2@@AEAAXI@Z ; mu2::JoinRankTimeCalculator::updateTimeCapture

; 70   : 
; 71   : 	// 평균 시간 계산
; 72   : 	calc();

  000ab	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  000b0	e8 00 00 00 00	 call	 ?calc@JoinRankTimeCalculator@mu2@@AEAAXXZ ; mu2::JoinRankTimeCalculator::calc
  000b5	90		 npad	 1

; 73   : }

  000b6	48 83 c4 58	 add	 rsp, 88			; 00000058H
  000ba	c3		 ret	 0
?LeaveWait@JoinRankTimeCalculator@mu2@@QEAAXXZ ENDP	; mu2::JoinRankTimeCalculator::LeaveWait
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp
;	COMDAT ?EnterWait@JoinRankTimeCalculator@mu2@@QEAAII@Z
_TEXT	SEGMENT
tick$ = 32
this$ = 64
index$ = 72
?EnterWait@JoinRankTimeCalculator@mu2@@QEAAII@Z PROC	; mu2::JoinRankTimeCalculator::EnterWait, COMDAT

; 43   : {

$LN4:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 44   : 	Tick tick = GetTickCount();

  0000d	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetTickCount
  00013	89 44 24 20	 mov	 DWORD PTR tick$[rsp], eax

; 45   : 
; 46   : 	// 마지막으로 대기열에 입장한 틱 저장
; 47   : 	updateLastEnterWaitTick( tick );

  00017	8b 54 24 20	 mov	 edx, DWORD PTR tick$[rsp]
  0001b	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00020	e8 00 00 00 00	 call	 ?updateLastEnterWaitTick@JoinRankTimeCalculator@mu2@@AEAAXI@Z ; mu2::JoinRankTimeCalculator::updateLastEnterWaitTick
  00025	90		 npad	 1

; 48   : 
; 49   : 	// 이미 입장 대기중인 인원이 있다면 초기화 체크를 진행한다.
; 50   : 	if( 1 < index && checkReset() )

  00026	83 7c 24 48 01	 cmp	 DWORD PTR index$[rsp], 1
  0002b	76 1c		 jbe	 SHORT $LN2@EnterWait
  0002d	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00032	e8 00 00 00 00	 call	 ?checkReset@JoinRankTimeCalculator@mu2@@AEAA_NXZ ; mu2::JoinRankTimeCalculator::checkReset
  00037	0f b6 c0	 movzx	 eax, al
  0003a	85 c0		 test	 eax, eax
  0003c	74 0b		 je	 SHORT $LN2@EnterWait

; 51   : 	{
; 52   : 		reset();

  0003e	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ?reset@JoinRankTimeCalculator@mu2@@AEAAXXZ ; mu2::JoinRankTimeCalculator::reset
  00048	90		 npad	 1
$LN2@EnterWait:

; 53   : 	}
; 54   : 
; 55   : 	return GetTime( index );

  00049	8b 54 24 48	 mov	 edx, DWORD PTR index$[rsp]
  0004d	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00052	e8 00 00 00 00	 call	 ?GetTime@JoinRankTimeCalculator@mu2@@QEBAII@Z ; mu2::JoinRankTimeCalculator::GetTime

; 56   : }

  00057	48 83 c4 38	 add	 rsp, 56			; 00000038H
  0005b	c3		 ret	 0
?EnterWait@JoinRankTimeCalculator@mu2@@QEAAII@Z ENDP	; mu2::JoinRankTimeCalculator::EnterWait
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp
;	COMDAT ??1JoinRankTimeCalculator@mu2@@QEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1JoinRankTimeCalculator@mu2@@QEAA@XZ PROC		; mu2::JoinRankTimeCalculator::~JoinRankTimeCalculator, COMDAT

; 39   : {

$LN20:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 40   : }

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8b c8	 mov	 rcx, rax
  00011	e8 00 00 00 00	 call	 ??1?$unique_ptr@UJoinRankTimeCalculatorImpl@JoinRankTimeCalculator@mu2@@U?$default_delete@UJoinRankTimeCalculatorImpl@JoinRankTimeCalculator@mu2@@@std@@@std@@QEAA@XZ ; std::unique_ptr<mu2::JoinRankTimeCalculator::JoinRankTimeCalculatorImpl,std::default_delete<mu2::JoinRankTimeCalculator::JoinRankTimeCalculatorImpl> >::~unique_ptr<mu2::JoinRankTimeCalculator::JoinRankTimeCalculatorImpl,std::default_delete<mu2::JoinRankTimeCalculator::JoinRankTimeCalculatorImpl> >
  00016	90		 npad	 1
  00017	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0001b	c3		 ret	 0
??1JoinRankTimeCalculator@mu2@@QEAA@XZ ENDP		; mu2::JoinRankTimeCalculator::~JoinRankTimeCalculator
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp
;	COMDAT ??0JoinRankTimeCalculator@mu2@@QEAA@XZ
_TEXT	SEGMENT
$T1 = 32
tv78 = 40
$T2 = 48
this$ = 56
_Ptr$ = 64
this$ = 72
$T3 = 80
this$ = 112
??0JoinRankTimeCalculator@mu2@@QEAA@XZ PROC		; mu2::JoinRankTimeCalculator::JoinRankTimeCalculator, COMDAT

; 35   : {

$LN22:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 68	 sub	 rsp, 104		; 00000068H

; 34   : 	: m_impl( new JoinRankTimeCalculatorImpl )

  00009	b9 30 00 00 00	 mov	 ecx, 48			; 00000030H
  0000e	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00013	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
  00018	48 83 7c 24 20
	00		 cmp	 QWORD PTR $T1[rsp], 0
  0001e	74 11		 je	 SHORT $LN3@JoinRankTi
  00020	48 8b 4c 24 20	 mov	 rcx, QWORD PTR $T1[rsp]
  00025	e8 00 00 00 00	 call	 ??0JoinRankTimeCalculatorImpl@JoinRankTimeCalculator@mu2@@QEAA@XZ ; mu2::JoinRankTimeCalculator::JoinRankTimeCalculatorImpl::JoinRankTimeCalculatorImpl
  0002a	48 89 44 24 28	 mov	 QWORD PTR tv78[rsp], rax
  0002f	eb 09		 jmp	 SHORT $LN4@JoinRankTi
$LN3@JoinRankTi:
  00031	48 c7 44 24 28
	00 00 00 00	 mov	 QWORD PTR tv78[rsp], 0
$LN4@JoinRankTi:
  0003a	48 8b 44 24 28	 mov	 rax, QWORD PTR tv78[rsp]
  0003f	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
  00044	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
  00049	48 89 44 24 40	 mov	 QWORD PTR _Ptr$[rsp], rax
  0004e	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 89 44 24 38	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3370 :     _CONSTEXPR23 explicit unique_ptr(pointer _Ptr) noexcept : _Mypair(_Zero_then_variadic_args_t{}, _Ptr) {}

  00058	48 8b 44 24 38	 mov	 rax, QWORD PTR this$[rsp]
  0005d	48 89 44 24 48	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00062	48 8d 44 24 40	 lea	 rax, QWORD PTR _Ptr$[rsp]
  00067	48 89 44 24 50	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1531 :         : _Ty1(), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  0006c	48 8b 44 24 48	 mov	 rax, QWORD PTR this$[rsp]
  00071	48 8b 4c 24 50	 mov	 rcx, QWORD PTR $T3[rsp]
  00076	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00079	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp

; 36   : }

  0007c	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  00081	48 83 c4 68	 add	 rsp, 104		; 00000068H
  00085	c3		 ret	 0
??0JoinRankTimeCalculator@mu2@@QEAA@XZ ENDP		; mu2::JoinRankTimeCalculator::JoinRankTimeCalculator
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
tv78 = 40
$T2 = 48
this$ = 56
_Ptr$ = 64
this$ = 72
$T3 = 80
this$ = 112
?dtor$0@?0???0JoinRankTimeCalculator@mu2@@QEAA@XZ@4HA PROC ; `mu2::JoinRankTimeCalculator::JoinRankTimeCalculator'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	ba 30 00 00 00	 mov	 edx, 48			; 00000030H
  0000e	48 8b 4d 20	 mov	 rcx, QWORD PTR $T1[rbp]
  00012	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00017	48 83 c4 20	 add	 rsp, 32			; 00000020H
  0001b	5d		 pop	 rbp
  0001c	c3		 ret	 0
?dtor$0@?0???0JoinRankTimeCalculator@mu2@@QEAA@XZ@4HA ENDP ; `mu2::JoinRankTimeCalculator::JoinRankTimeCalculator'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankTimeCalculator.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
