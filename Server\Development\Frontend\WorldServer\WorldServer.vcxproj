﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release_Build|x64">
      <Configuration>Release_Build</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release_Global|Win32">
      <Configuration>Release_Global</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release_Global|x64">
      <Configuration>Release_Global</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release_Japan|Win32">
      <Configuration>Release_Japan</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release_Japan|x64">
      <Configuration>Release_Japan</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Shipping_Global|Win32">
      <Configuration>Shipping_Global</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Shipping_Global|x64">
      <Configuration>Shipping_Global</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Shipping_Japan|Win32">
      <Configuration>Shipping_Japan</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Shipping_Japan|x64">
      <Configuration>Shipping_Japan</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Shipping|Win32">
      <Configuration>Shipping</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Shipping|x64">
      <Configuration>Shipping</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{28C37A66-9F90-4C98-82B4-AF87A17B7CAB}</ProjectGuid>
    <RootNamespace>WorldServer</RootNamespace>
    <Keyword>Win32Proj</Keyword>
    <SccProjectName>
    </SccProjectName>
    <SccAuxPath>
    </SccAuxPath>
    <SccLocalPath>
    </SccLocalPath>
    <SccProvider>
    </SccProvider>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release_Build|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release_Global|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Shipping_Global|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Shipping_Japan|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release_Japan|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Shipping|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release_Build|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release_Global|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Shipping_Global|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Shipping_Japan|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release_Japan|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Shipping|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>false</WholeProgramOptimization>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\..\_Build\Mu2_property_$(Platform)_$(Configuration).props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release_Build|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\..\_Build\Mu2_property_$(Platform)_$(Configuration).props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release_Global|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\..\_Build\Mu2_property_$(Platform)_$(Configuration).props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Shipping_Global|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\..\_Build\Mu2_property_$(Platform)_$(Configuration).props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Shipping_Japan|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\..\_Build\Mu2_property_$(Platform)_$(Configuration).props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release_Japan|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\..\_Build\Mu2_property_$(Platform)_$(Configuration).props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Shipping|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\..\_Build\Mu2_property_$(Platform)_$(Configuration).props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\..\_Build\Mu2_property_$(Platform)_$(Configuration).props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release_Build|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\..\_Build\Mu2_property_$(Platform)_$(Configuration).props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release_Global|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\..\_Build\Mu2_property_$(Platform)_$(Configuration).props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Shipping_Global|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\..\_Build\Mu2_property_$(Platform)_$(Configuration).props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Shipping_Japan|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\..\_Build\Mu2_property_$(Platform)_$(Configuration).props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release_Japan|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\..\_Build\Mu2_property_$(Platform)_$(Configuration).props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Shipping|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\..\_Build\Mu2_property_$(Platform)_$(Configuration).props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\..\_Build\Mu2_property_$(Platform)_$(Configuration).props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="..\..\_Build\Mu2_property_$(Platform)_$(Configuration).props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.40219.1</_ProjectFileVersion>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Mu2_$(ProjectName)D</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Mu2_$(ProjectName)D</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Mu2_$(ProjectName)</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release_Build|Win32'">Mu2_$(ProjectName)</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release_Global|Win32'">Mu2_$(ProjectName)</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Shipping_Global|Win32'">Mu2_$(ProjectName)</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Shipping_Japan|Win32'">Mu2_$(ProjectName)</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release_Japan|Win32'">Mu2_$(ProjectName)</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Shipping|Win32'">Mu2_$(ProjectName)</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Mu2_$(ProjectName)R</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release_Build|x64'">Mu2_$(ProjectName)</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release_Global|x64'">Mu2_$(ProjectName)R</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Shipping_Global|x64'">Mu2_$(ProjectName)</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Shipping_Japan|x64'">Mu2_$(ProjectName)</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release_Japan|x64'">Mu2_$(ProjectName)R</TargetName>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Shipping|x64'">Mu2_$(ProjectName)</TargetName>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(SolutionDir)..\..\Vendor\GameGuard\include;$(SolutionDir)..\..\Vendor\log4cplus\include;$(SolutionDir)..\..\Vendor\boost\;$(SolutionDir)..\..\Vendor\Cryptopp563\;$(SolutionDir)..\..\Vendor\DirectX\Include;$(SolutionDir)..\..\Vendor\Microsoft SQL Server 2008 R2 Native Client SDK\Include;$(SolutionDir)..\..\Vendor\vld\include;$(SolutionDir)..\..\Vendor\jsoncpp\include;$(SolutionDir)..\..\Vendor\yaml-cpp\include;$(IncludePath)</IncludePath>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(SolutionDir)..\..\Vendor\GameGuard\include;$(SolutionDir)..\..\Vendor\log4cplus\include;$(SolutionDir)..\Include;$(SolutionDir)..\..\Vendor\boost\;$(SolutionDir)..\..\Vendor\Cryptopp563\;$(SolutionDir)..\..\Vendor\DirectX\Include;$(SolutionDir)..\..\Vendor\Microsoft SQL Server 2008 R2 Native Client SDK\Include;$(SolutionDir)..\..\Vendor\vld\include;$(SolutionDir)..\..\Vendor\jsoncpp\include;$(SolutionDir)..\..\Vendor\yaml-cpp\include;$(IncludePath)</IncludePath>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='Release_Build|x64'">$(SolutionDir)..\..\Vendor\GameGuard\include;$(SolutionDir)..\..\Vendor\log4cplus\include;$(SolutionDir)..\Include;$(SolutionDir)..\..\Vendor\boost\;$(SolutionDir)..\..\Vendor\Cryptopp563\;$(SolutionDir)..\..\Vendor\DirectX\Include;$(SolutionDir)..\..\Vendor\Microsoft SQL Server 2008 R2 Native Client SDK\Include;$(SolutionDir)..\..\Vendor\vld\include;$(SolutionDir)..\..\Vendor\jsoncpp\include;$(SolutionDir)..\..\Vendor\yaml-cpp\include;$(IncludePath)</IncludePath>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='Release_Global|x64'">$(SolutionDir)..\..\Vendor\GameGuard\include;$(SolutionDir)..\..\Vendor\log4cplus\include;$(SolutionDir)..\Include;$(SolutionDir)..\..\Vendor\boost\;$(SolutionDir)..\..\Vendor\Cryptopp563\;$(SolutionDir)..\..\Vendor\DirectX\Include;$(SolutionDir)..\..\Vendor\Microsoft SQL Server 2008 R2 Native Client SDK\Include;$(SolutionDir)..\..\Vendor\vld\include;$(SolutionDir)..\..\Vendor\jsoncpp\include;$(SolutionDir)..\..\Vendor\yaml-cpp\include;$(IncludePath)</IncludePath>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='Shipping_Global|x64'">$(SolutionDir)..\..\Vendor\GameGuard\include;$(SolutionDir)..\..\Vendor\log4cplus\include;$(SolutionDir)..\Include;$(SolutionDir)..\..\Vendor\boost\;$(SolutionDir)..\..\Vendor\Cryptopp563\;$(SolutionDir)..\..\Vendor\DirectX\Include;$(SolutionDir)..\..\Vendor\Microsoft SQL Server 2008 R2 Native Client SDK\Include;$(SolutionDir)..\..\Vendor\vld\include;$(SolutionDir)..\..\Vendor\jsoncpp\include;$(SolutionDir)..\..\Vendor\yaml-cpp\include;$(IncludePath)</IncludePath>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='Shipping_Japan|x64'">$(SolutionDir)..\..\Vendor\GameGuard\include;$(SolutionDir)..\..\Vendor\log4cplus\include;$(SolutionDir)..\Include;$(SolutionDir)..\..\Vendor\boost\;$(SolutionDir)..\..\Vendor\Cryptopp563\;$(SolutionDir)..\..\Vendor\DirectX\Include;$(SolutionDir)..\..\Vendor\Microsoft SQL Server 2008 R2 Native Client SDK\Include;$(SolutionDir)..\..\Vendor\vld\include;$(SolutionDir)..\..\Vendor\jsoncpp\include;$(SolutionDir)..\..\Vendor\yaml-cpp\include;$(IncludePath)</IncludePath>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='Release_Japan|x64'">$(SolutionDir)..\..\Vendor\GameGuard\include;$(SolutionDir)..\..\Vendor\log4cplus\include;$(SolutionDir)..\Include;$(SolutionDir)..\..\Vendor\boost\;$(SolutionDir)..\..\Vendor\Cryptopp563\;$(SolutionDir)..\..\Vendor\DirectX\Include;$(SolutionDir)..\..\Vendor\Microsoft SQL Server 2008 R2 Native Client SDK\Include;$(SolutionDir)..\..\Vendor\vld\include;$(SolutionDir)..\..\Vendor\jsoncpp\include;$(SolutionDir)..\..\Vendor\yaml-cpp\include;$(IncludePath)</IncludePath>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='Shipping|x64'">$(SolutionDir)..\..\Vendor\GameGuard\include;$(SolutionDir)..\..\Vendor\log4cplus\include;$(SolutionDir)..\Include;$(SolutionDir)..\..\Vendor\boost\;$(SolutionDir)..\..\Vendor\Cryptopp563\;$(SolutionDir)..\..\Vendor\DirectX\Include;$(SolutionDir)..\..\Vendor\Microsoft SQL Server 2008 R2 Native Client SDK\Include;$(SolutionDir)..\..\Vendor\vld\include;$(SolutionDir)..\..\Vendor\jsoncpp\include;$(SolutionDir)..\..\Vendor\yaml-cpp\include;$(IncludePath)</IncludePath>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">$(SolutionDir)..\..\Vendor\log4cplus\include;$(SolutionDir)..\..;$(SolutionDir)..\..\Vendor\boost\;$(SolutionDir)..\..\Vendor\Cryptopp563\;$(SolutionDir)..\..\Vendor\DirectX\Include;$(SolutionDir)..\..\Vendor\Microsoft SQL Server 2008 R2 Native Client SDK\Include;$(SolutionDir)..\..\Vendor\vld\include;$(SolutionDir)..\..\Vendor\jsoncpp\include;$(IncludePath)</IncludePath>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">$(SolutionDir)..\..\Vendor\log4cplus\include;$(SolutionDir)..\..;$(SolutionDir)..\..\Vendor\boost\;$(SolutionDir)..\..\Vendor\Cryptopp563\;$(SolutionDir)..\..\Vendor\DirectX\Include;$(SolutionDir)..\..\Vendor\Microsoft SQL Server 2008 R2 Native Client SDK\Include;$(SolutionDir)..\..\Vendor\vld\include;$(IncludePath)</IncludePath>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='Release_Build|Win32'">$(SolutionDir)..\..\Vendor\log4cplus\include;$(SolutionDir)..\..;$(SolutionDir)..\..\Vendor\boost\;$(SolutionDir)..\..\Vendor\Cryptopp563\;$(SolutionDir)..\..\Vendor\DirectX\Include;$(SolutionDir)..\..\Vendor\Microsoft SQL Server 2008 R2 Native Client SDK\Include;$(SolutionDir)..\..\Vendor\vld\include;$(IncludePath)</IncludePath>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='Release_Global|Win32'">$(SolutionDir)..\..\Vendor\log4cplus\include;$(SolutionDir)..\..;$(SolutionDir)..\..\Vendor\boost\;$(SolutionDir)..\..\Vendor\Cryptopp563\;$(SolutionDir)..\..\Vendor\DirectX\Include;$(SolutionDir)..\..\Vendor\Microsoft SQL Server 2008 R2 Native Client SDK\Include;$(SolutionDir)..\..\Vendor\vld\include;$(IncludePath)</IncludePath>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='Shipping_Global|Win32'">$(SolutionDir)..\..\Vendor\log4cplus\include;$(SolutionDir)..\..;$(SolutionDir)..\..\Vendor\boost\;$(SolutionDir)..\..\Vendor\Cryptopp563\;$(SolutionDir)..\..\Vendor\DirectX\Include;$(SolutionDir)..\..\Vendor\Microsoft SQL Server 2008 R2 Native Client SDK\Include;$(SolutionDir)..\..\Vendor\vld\include;$(IncludePath)</IncludePath>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='Shipping_Japan|Win32'">$(SolutionDir)..\..\Vendor\log4cplus\include;$(SolutionDir)..\..;$(SolutionDir)..\..\Vendor\boost\;$(SolutionDir)..\..\Vendor\Cryptopp563\;$(SolutionDir)..\..\Vendor\DirectX\Include;$(SolutionDir)..\..\Vendor\Microsoft SQL Server 2008 R2 Native Client SDK\Include;$(SolutionDir)..\..\Vendor\vld\include;$(IncludePath)</IncludePath>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='Release_Japan|Win32'">$(SolutionDir)..\..\Vendor\log4cplus\include;$(SolutionDir)..\..;$(SolutionDir)..\..\Vendor\boost\;$(SolutionDir)..\..\Vendor\Cryptopp563\;$(SolutionDir)..\..\Vendor\DirectX\Include;$(SolutionDir)..\..\Vendor\Microsoft SQL Server 2008 R2 Native Client SDK\Include;$(SolutionDir)..\..\Vendor\vld\include;$(IncludePath)</IncludePath>
    <IncludePath Condition="'$(Configuration)|$(Platform)'=='Shipping|Win32'">$(SolutionDir)..\..\Vendor\log4cplus\include;$(SolutionDir)..\..;$(SolutionDir)..\..\Vendor\boost\;$(SolutionDir)..\..\Vendor\Cryptopp563\;$(SolutionDir)..\..\Vendor\DirectX\Include;$(SolutionDir)..\..\Vendor\Microsoft SQL Server 2008 R2 Native Client SDK\Include;$(SolutionDir)..\..\Vendor\vld\include;$(IncludePath)</IncludePath>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release_Build|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release_Global|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Shipping_Global|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Shipping_Japan|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release_Japan|x64'">false</LinkIncremental>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Shipping|x64'">false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <OutDir>$(SolutionDir)\..\bin\x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Shipping|x64'">
    <OutDir>$(SolutionDir)\..\bin\x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release_Global|x64'">
    <OutDir>$(SolutionDir)\..\bin\x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Shipping_Global|x64'">
    <OutDir>$(SolutionDir)\..\bin\x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Shipping_Japan|x64'">
    <OutDir>$(SolutionDir)\..\bin\x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release_Japan|x64'">
    <OutDir>$(SolutionDir)\..\bin\x64\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release_Build|x64'">
    <OutDir>$(SolutionDir)\..\bin\x64\</OutDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>../../;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_WINDOWS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <PrecompiledHeader>Create</PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <DebugInformationFormat>EditAndContinue</DebugInformationFormat>
      <TreatWarningAsError>false</TreatWarningAsError>
      <AdditionalOptions>-Zm735 %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <AdditionalDependencies>yamlD.lib;cryptlib_mt_d.lib;User32.lib;ws2_32.lib;mswsock.lib;shlwapi.lib;CoreD.lib;DataBaseD.lib;EntityD.lib;MathD.lib;Mu2CommonD.lib;ServerNetworkD.lib</AdditionalDependencies>
      <SubSystem>Console</SubSystem>
      <TargetMachine>MachineX86</TargetMachine>
      <AdditionalLibraryDirectories>../../../Lib</AdditionalLibraryDirectories>
      <ImageHasSafeExceptionHandlers>false</ImageHasSafeExceptionHandlers>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>$(ProjectDir)/.;../../;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_ONLY_SERVER_CORE;_WINDOWS;%(PreprocessorDefinitions);_SCL_SECURE_NO_WARNINGS;MU2_DEBUG</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <TreatWarningAsError>true</TreatWarningAsError>
      <AdditionalOptions>/Zm780 /bigobj %(AdditionalOptions)</AdditionalOptions>
    </ClCompile>
    <Link>
      <AdditionalDependencies>yamlD.lib;cryptlib_mt_d.lib;ws2_32.lib;mswsock.lib;Core_64D.lib;DataBaseD.lib;EntityD.lib;MathD.lib;Mu2CommonD.lib;ServerNetworkD.lib;lib_json_mt_d.lib;ggsrv30lib_x64_MT.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <SubSystem>Console</SubSystem>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <PreprocessorDefinitions>_WINDOWS;MU2_USE_RUNTIME_ASSERTION;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <AdditionalIncludeDirectories>../../;../../../../Vendor/mongo/client;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>-Zm735 %(AdditionalOptions)</AdditionalOptions>
      <Optimization>MaxSpeed</Optimization>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <TargetMachine>MachineX86</TargetMachine>
      <AdditionalDependencies>yaml.lib;cryptlib_mt.lib;ws2_32.lib;Core.lib;DataBase.lib;Entity.lib;Math.lib;Mu2Common.lib;ServerNetwork.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release_Build|Win32'">
    <ClCompile>
      <PreprocessorDefinitions>_WINDOWS;MU2_USE_RUNTIME_ASSERTION;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <AdditionalIncludeDirectories>../../;../../../../Vendor/mongo/client;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>-Zm735 %(AdditionalOptions)</AdditionalOptions>
      <Optimization>MaxSpeed</Optimization>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <TargetMachine>MachineX86</TargetMachine>
      <AdditionalDependencies>yaml.lib;cryptlib_mt.lib;ws2_32.lib;Core.lib;DataBase.lib;Entity.lib;Math.lib;Mu2Common.lib;ServerNetwork.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release_Global|Win32'">
    <ClCompile>
      <PreprocessorDefinitions>_WINDOWS;MU2_USE_RUNTIME_ASSERTION;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <AdditionalIncludeDirectories>../../;../../../../Vendor/mongo/client;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>-Zm735 %(AdditionalOptions)</AdditionalOptions>
      <Optimization>MaxSpeed</Optimization>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <TargetMachine>MachineX86</TargetMachine>
      <AdditionalDependencies>yaml.lib;cryptlib_mt.lib;ws2_32.lib;Core.lib;DataBase.lib;Entity.lib;Math.lib;Mu2Common.lib;ServerNetwork.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Shipping_Global|Win32'">
    <ClCompile>
      <PreprocessorDefinitions>_WINDOWS;MU2_USE_RUNTIME_ASSERTION;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <AdditionalIncludeDirectories>../../;../../../../Vendor/mongo/client;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>-Zm735 %(AdditionalOptions)</AdditionalOptions>
      <Optimization>MaxSpeed</Optimization>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <TargetMachine>MachineX86</TargetMachine>
      <AdditionalDependencies>yaml.lib;cryptlib_mt.lib;ws2_32.lib;Core.lib;DataBase.lib;Entity.lib;Math.lib;Mu2Common.lib;ServerNetwork.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Shipping_Japan|Win32'">
    <ClCompile>
      <PreprocessorDefinitions>_WINDOWS;MU2_USE_RUNTIME_ASSERTION;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <AdditionalIncludeDirectories>../../;../../../../Vendor/mongo/client;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>-Zm735 %(AdditionalOptions)</AdditionalOptions>
      <Optimization>MaxSpeed</Optimization>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <TargetMachine>MachineX86</TargetMachine>
      <AdditionalDependencies>yaml.lib;cryptlib_mt.lib;ws2_32.lib;Core.lib;DataBase.lib;Entity.lib;Math.lib;Mu2Common.lib;ServerNetwork.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release_Japan|Win32'">
    <ClCompile>
      <PreprocessorDefinitions>_WINDOWS;MU2_USE_RUNTIME_ASSERTION;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <AdditionalIncludeDirectories>../../;../../../../Vendor/mongo/client;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>-Zm735 %(AdditionalOptions)</AdditionalOptions>
      <Optimization>MaxSpeed</Optimization>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <TargetMachine>MachineX86</TargetMachine>
      <AdditionalDependencies>yaml.lib;cryptlib_mt.lib;ws2_32.lib;Core.lib;DataBase.lib;Entity.lib;Math.lib;Mu2Common.lib;ServerNetwork.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Shipping|Win32'">
    <ClCompile>
      <PreprocessorDefinitions>_WINDOWS;MU2_USE_RUNTIME_ASSERTION;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <AdditionalIncludeDirectories>../../;../../../../Vendor/mongo/client;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>-Zm735 %(AdditionalOptions)</AdditionalOptions>
      <Optimization>MaxSpeed</Optimization>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <TargetMachine>MachineX86</TargetMachine>
      <AdditionalDependencies>yaml.lib;cryptlib_mt.lib;ws2_32.lib;Core.lib;DataBase.lib;Entity.lib;Math.lib;Mu2Common.lib;ServerNetwork.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <PreprocessorDefinitions>_ONLY_SERVER_CORE;_WINDOWS;MU2_USE_RUNTIME_ASSERTION;%(PreprocessorDefinitions);_SCL_SECURE_NO_WARNINGS;MU2_RELEASE</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <AdditionalIncludeDirectories>$(ProjectDir)/.;../../;../../../../Vendor/mongo/client;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerOutput>All</AssemblerOutput>
      <Optimization>Disabled</Optimization>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <OmitFramePointers>false</OmitFramePointers>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <TreatWarningAsError>true</TreatWarningAsError>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <EnableFiberSafeOptimizations>true</EnableFiberSafeOptimizations>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <AdditionalDependencies>yaml.lib;cryptlib_mt.lib;ws2_32.lib;Core_64.lib;DataBase.lib;Entity.lib;Math.lib;Mu2CommonR.lib;SharedR.lib;ServerNetwork.lib;lib_json_mt.lib;ggsrv30lib_x64_MT.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <GenerateMapFile>true</GenerateMapFile>
      <MapFileName>$(TargetDir)$(TargetName).map</MapFileName>
      <MapExports>true</MapExports>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release_Build|x64'">
    <ClCompile>
      <PreprocessorDefinitions>_ONLY_SERVER_CORE;_WINDOWS;MU2_USE_RUNTIME_ASSERTION;%(PreprocessorDefinitions);_SCL_SECURE_NO_WARNINGS;MU2_RELEASE;WIN32;_WIN64;NDEBUG;MU2_SERVER</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <AdditionalIncludeDirectories>$(ProjectDir)/.;../../;../../../../Vendor/mongo/client;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerOutput>All</AssemblerOutput>
      <Optimization>Disabled</Optimization>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <OmitFramePointers>false</OmitFramePointers>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <TreatWarningAsError>true</TreatWarningAsError>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <EnableFiberSafeOptimizations>true</EnableFiberSafeOptimizations>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <AdditionalDependencies>yaml.lib;cryptlib_mt.lib;ws2_32.lib;Core_64.lib;DataBase.lib;Entity.lib;Math.lib;Mu2CommonR.lib;SharedR.lib;ServerNetwork.lib;lib_json_mt.lib;ggsrv30lib_x64_MT.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <GenerateMapFile>true</GenerateMapFile>
      <MapFileName>$(TargetDir)$(TargetName).map</MapFileName>
      <MapExports>true</MapExports>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release_Global|x64'">
    <ClCompile>
      <PreprocessorDefinitions>MU2_RELEASE;NDEBUG;MU2_SERVER;_ONLY_SERVER_CORE;MU2_USE_RUNTIME_ASSERTION;FOR_GLOBAL_JAPAN;FOR_GLOBAL;FOR_JAPAN_SHOP;WIN32;_WIN64;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <AdditionalIncludeDirectories>$(ProjectDir)/.;../../;../../../../Vendor/mongo/client;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerOutput>All</AssemblerOutput>
      <Optimization>Disabled</Optimization>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <OmitFramePointers>false</OmitFramePointers>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <TreatWarningAsError>true</TreatWarningAsError>
      <IntrinsicFunctions>false</IntrinsicFunctions>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <DisableSpecificWarnings>4566;4189;4091</DisableSpecificWarnings>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <AdditionalDependencies>yaml.lib;cryptlib_mt.lib;ws2_32.lib;Core_64.lib;DataBase.lib;Entity.lib;Math.lib;Mu2CommonR.lib;SharedR.lib;ServerNetwork.lib;lib_json_mt.lib;ggsrv30lib_x64_MT.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <GenerateMapFile>true</GenerateMapFile>
      <MapFileName>$(TargetDir)$(TargetName).map</MapFileName>
      <MapExports>true</MapExports>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Shipping_Global|x64'">
    <ClCompile>
      <PreprocessorDefinitions>WIN32;_WIN64;NDEBUG;MU2_SERVER;_ONLY_SERVER_CORE;_WINDOWS;MU2_USE_RUNTIME_ASSERTION;_SCL_SECURE_NO_WARNINGS;FOR_GLOBAL_JAPAN;FOR_GLOBAL;FOR_JAPAN_SHOP;MU2_SHIPPING;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <AdditionalIncludeDirectories>$(ProjectDir)/.;../../;../../../../Vendor/mongo/client;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerOutput>All</AssemblerOutput>
      <Optimization>Disabled</Optimization>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <OmitFramePointers>false</OmitFramePointers>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <TreatWarningAsError>true</TreatWarningAsError>
      <AdditionalOptions>-Zm760 %(AdditionalOptions)</AdditionalOptions>
      <IntrinsicFunctions>false</IntrinsicFunctions>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <AdditionalDependencies>yaml.lib;cryptlib_mt.lib;ws2_32.lib;Core_64.lib;DataBase.lib;Entity.lib;Math.lib;Mu2Common.lib;ServerNetwork.lib;lib_json_mt.lib;ggsrv30lib_x64_MT.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <GenerateMapFile>true</GenerateMapFile>
      <MapFileName>$(TargetDir)$(TargetName).map</MapFileName>
      <MapExports>true</MapExports>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Shipping_Japan|x64'">
    <ClCompile>
      <PreprocessorDefinitions>WIN32;_WIN64;NDEBUG;MU2_SERVER;_ONLY_SERVER_CORE;_WINDOWS;MU2_USE_RUNTIME_ASSERTION;_SCL_SECURE_NO_WARNINGS;FOR_GLOBAL_JAPAN;FOR_JAPAN;MU2_SHIPPING;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <AdditionalIncludeDirectories>$(ProjectDir)/.;../../;../../../../Vendor/mongo/client;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerOutput>All</AssemblerOutput>
      <Optimization>Disabled</Optimization>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <OmitFramePointers>false</OmitFramePointers>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <TreatWarningAsError>true</TreatWarningAsError>
      <IntrinsicFunctions>false</IntrinsicFunctions>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <AdditionalDependencies>yaml.lib;cryptlib_mt.lib;ws2_32.lib;Core_64.lib;DataBase.lib;Entity.lib;Math.lib;Mu2Common.lib;ServerNetwork.lib;lib_json_mt.lib;ggsrv30lib_x64_MT.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <GenerateMapFile>true</GenerateMapFile>
      <MapFileName>$(TargetDir)$(TargetName).map</MapFileName>
      <MapExports>true</MapExports>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release_Japan|x64'">
    <ClCompile>
      <PreprocessorDefinitions>_ONLY_SERVER_CORE;_WINDOWS;MU2_USE_RUNTIME_ASSERTION;_SCL_SECURE_NO_WARNINGS;MU2_RELEASE;WIN32;_WIN64;NDEBUG;MU2_SERVER;_UNICODE;UNICODE;FOR_GLOBAL_JAPAN;FOR_JAPAN;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <AdditionalIncludeDirectories>$(ProjectDir)/.;../../;../../../../Vendor/mongo/client;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerOutput>All</AssemblerOutput>
      <Optimization>Disabled</Optimization>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <OmitFramePointers>false</OmitFramePointers>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <TreatWarningAsError>true</TreatWarningAsError>
      <AdditionalOptions>-Zm760 %(AdditionalOptions)</AdditionalOptions>
      <IntrinsicFunctions>false</IntrinsicFunctions>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <AdditionalDependencies>yaml.lib;cryptlib_mt.lib;ws2_32.lib;Core_64.lib;DataBase.lib;Entity.lib;Math.lib;Mu2CommonR.lib;SharedR.lib;ServerNetwork.lib;lib_json_mt.lib;ggsrv30lib_x64_MT.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <GenerateMapFile>true</GenerateMapFile>
      <MapFileName>$(TargetDir)$(TargetName).map</MapFileName>
      <MapExports>true</MapExports>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Shipping|x64'">
    <ClCompile>
      <PreprocessorDefinitions>MU2_SHIPPING_SERVER;WIN32;_WIN64;NDEBUG;MU2_SERVER;_ONLY_SERVER_CORE;_WINDOWS;MU2_USE_RUNTIME_ASSERTION;%(PreprocessorDefinitions);_SCL_SECURE_NO_WARNINGS</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <AdditionalIncludeDirectories>$(ProjectDir)/.;../../;../../../../Vendor/mongo/client;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerOutput>All</AssemblerOutput>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <OmitFramePointers>false</OmitFramePointers>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <TreatWarningAsError>true</TreatWarningAsError>
      <TreatWarningAsError>true</TreatWarningAsError>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <IntrinsicFunctions>false</IntrinsicFunctions>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <AdditionalDependencies>yaml.lib;cryptlib_mt.lib;ws2_32.lib;Core_64.lib;DataBase.lib;Entity.lib;Math.lib;Mu2Common.lib;ServerNetwork.lib;lib_json_mt.lib;ggsrv30lib_x64_MT.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <GenerateMapFile>true</GenerateMapFile>
      <MapFileName>$(TargetDir)$(TargetName).map</MapFileName>
      <MapExports>true</MapExports>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Shipping|x64'">
    <ClCompile>
      <PreprocessorDefinitions>WIN32;_WIN64;NDEBUG;MU2_SERVER;_ONLY_SERVER_CORE;_WINDOWS;MU2_USE_RUNTIME_ASSERTION;_SCL_SECURE_NO_WARNINGS;MU2_SHIPPING;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <AdditionalIncludeDirectories>$(ProjectDir)/.;../../;../../../../Vendor/mongo/client;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerOutput>All</AssemblerOutput>
      <Optimization>MaxSpeed</Optimization>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <OmitFramePointers>false</OmitFramePointers>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <TreatWarningAsError>true</TreatWarningAsError>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <EnableFiberSafeOptimizations>true</EnableFiberSafeOptimizations>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <OptimizeReferences>true</OptimizeReferences>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <AdditionalDependencies>yaml.lib;cryptlib_mt.lib;ws2_32.lib;Core_64.lib;DataBase.lib;Entity.lib;Math.lib;Mu2Common.lib;ServerNetwork.lib;lib_json_mt.lib;ggsrv30lib_x64_MT.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <GenerateMapFile>true</GenerateMapFile>
      <MapFileName>$(TargetDir)$(TargetName).map</MapFileName>
      <MapExports>true</MapExports>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="AchievementHandler.cpp" />
    <ClCompile Include="AchievementJobLimitAchievementCache.cpp" />
    <ClCompile Include="AchievementManager.cpp" />
    <ClCompile Include="AppChat.cpp" />
    <ClCompile Include="AttributePlayer.cpp" />
    <ClCompile Include="BurningStaminaContinetInfo.cpp" />
    <ClCompile Include="BurningStaminaHandler.cpp" />
    <ClCompile Include="BurningStaminaInfo.cpp" />
    <ClCompile Include="BurningStaminaManager.cpp" />
    <ClCompile Include="BurningStaminaTimeInfo.cpp" />
    <ClCompile Include="BurningStaminaUserInfo.cpp" />
    <ClCompile Include="Channel.cpp" />
    <ClCompile Include="ChannelDominion.cpp" />
    <ClCompile Include="ChannelFieldpoint.cpp" />
    <ClCompile Include="ChannelKnightageFieldRaid.cpp" />
    <ClCompile Include="ChannelGroup.cpp" />
    <ClCompile Include="ChannelManager.cpp" />
    <ClCompile Include="ChannelNull.cpp" />
    <ClCompile Include="ChatHandler.cpp" />
    <ClCompile Include="ColosseumHandler.cpp" />
    <ClCompile Include="CommunityFriend.cpp" />
    <ClCompile Include="CommunitySearchChar.cpp" />
    <ClCompile Include="CommunityCharBlock.cpp" />
    <ClCompile Include="CommunityTeacherAndStudent.cpp" />
    <ClCompile Include="ContentsDeactivator.cpp" />
    <ClCompile Include="DailyComparer.cpp" />
    <ClCompile Include="DailyMissionHandler.cpp" />
    <ClCompile Include="DailyMissionManager.cpp" />
    <ClCompile Include="DailyTimeConnectionManager.cpp" />
    <ClCompile Include="Entity4WorldServer.cpp" />
    <ClCompile Include="Dominion\Dominion.cpp" />
    <ClCompile Include="Dominion\DominionManager.cpp" />
    <ClCompile Include="EntityParty.cpp" />
    <ClCompile Include="EntityPlayer.cpp" />
    <ClCompile Include="EventSchedule.cpp" />
    <ClCompile Include="EventScheduleManager.cpp" />
    <ClCompile Include="Event\EventSystem.cpp" />
    <ClCompile Include="Event\PlayerReturnEventAction.cpp" />
    <ClCompile Include="ExecInstanceBase.cpp" />
    <ClCompile Include="Execution.cpp" />
    <ClCompile Include="ExecutionGroup.cpp" />
    <ClCompile Include="ExecutionNull.cpp" />
    <ClCompile Include="Fcs\WorldFcsAsyncReceiverAuth.cpp" />
    <ClCompile Include="FieldPoint\FieldPointForwarder.cpp" />
    <ClCompile Include="FieldPoint\FieldPointHandler.cpp" />
    <ClCompile Include="FieldPoint\FieldPointManager.cpp" />
    <ClCompile Include="FieldPoint\FieldPointProcessor.cpp" />
    <ClCompile Include="Instance.cpp" />
    <ClCompile Include="InstanceColloseumPvp33.cpp" />
    <ClCompile Include="InstanceGroup.cpp" />
    <ClCompile Include="InstanceMultisage.cpp" />
    <ClCompile Include="InstanceNormal.cpp" />
    <ClCompile Include="InstanceNull.cpp" />
    <ClCompile Include="InstancePartyInfo.cpp" />
    <ClCompile Include="InstancePvp.cpp" />
    <ClCompile Include="InstanceSinglestage.cpp" />
    <ClCompile Include="InstanceTournament.cpp" />
    <ClCompile Include="InstanceWorldcross.cpp" />
    <ClCompile Include="JoinZoneContext.cpp" />
    <ClCompile Include="JoinZoneContextBase.cpp" />
    <ClCompile Include="JoinZoneContextChangeChannel.cpp" />
    <ClCompile Include="JoinZoneContextChangeMap.cpp" />
    <ClCompile Include="JoinZoneContextCheckIn.cpp" />
    <ClCompile Include="JoinZoneContextLogout.cpp" />
    <ClCompile Include="JoinZoneContextTutorial.cpp" />
    <ClCompile Include="JoinZoneContextWarpMap.cpp" />
    <ClCompile Include="Knightage\KnightageFieldRaidScheduler.cpp" />
    <ClCompile Include="Knightage\KnightagePaymentScheduler.cpp" />
    <ClCompile Include="Liberation\LiberationManager.cpp" />
    <ClCompile Include="LuckyMonster\LuckyMonsterHandler.cpp" />
    <ClCompile Include="LuckyMonster\LuckyMonsterManager.cpp" />
    <ClCompile Include="ManagerInvasionContext.cpp" />
    <ClCompile Include="ManagerJoinZoneContext.cpp" />
    <ClCompile Include="Membership\PlayerMembershipAction.cpp" />
    <ClCompile Include="MissionMapJoinManager.cpp" />
    <ClCompile Include="MopupHandler.cpp" />
    <ClCompile Include="Mopup\MopupManager.cpp" />
    <ClCompile Include="OhrdorTreasureHunt.cpp" />
    <ClCompile Include="PCRoom\PCRoomBillingEventLoopBackHandler.cpp" />
    <ClCompile Include="PlayerDailyLoginAction.cpp" />
    <ClCompile Include="DailyLoginHandler.cpp" />
    <ClCompile Include="DailyLoginManager.cpp" />
    <ClCompile Include="DamageMeterHandler.cpp" />
    <ClCompile Include="DamageMeterManager.cpp" />
    <ClCompile Include="DungeonLog.cpp" />
    <ClCompile Include="DungeonLogManager.cpp" />
    <ClCompile Include="GlobalGameConfigInfo.cpp" />
    <ClCompile Include="GlobalGameConfigManager.cpp" />
    <ClCompile Include="Invasion\InvasionAction.cpp" />
    <ClCompile Include="JoinRankTimeCalculator.cpp" />
    <ClCompile Include="KISS\KISSApiWrapper.cpp" />
    <ClCompile Include="Knightage\Knightage.cpp" />
    <ClCompile Include="Knightage\KnightageAbility.cpp" />
    <ClCompile Include="Knightage\KnightageHandler.cpp" />
    <ClCompile Include="Knightage\KnightageLoader.cpp" />
    <ClCompile Include="Knightage\KnightageManager.cpp" />
    <ClCompile Include="Knightage\KnightageMember.cpp" />
    <ClCompile Include="Knightage\KnightageSaveChecker.cpp" />
    <ClCompile Include="Knightage\PlayerKnightageAction.cpp" />
    <ClCompile Include="MonitorWorld.cpp" />
    <ClCompile Include="PlayerDailyMissionAction.cpp" />
    <ClCompile Include="PlayerDailyTimeConnectionAction.cpp" />
    <ClCompile Include="PlayerTeacherAndStudentAction.cpp" />
    <ClCompile Include="PVPFieldSchedule.cpp" />
    <ClCompile Include="QuestSystem.cpp" />
    <ClCompile Include="Raid\RaidHandler.cpp" />
    <ClCompile Include="Raid\RaidManager.cpp" />
    <ClCompile Include="Raid\TournamentRaid.cpp" />
    <ClCompile Include="Ranking\Ranking.cpp" />
    <ClCompile Include="ReLoginGetFcsAuthInfoHandler.cpp" />
    <ClCompile Include="Season\SeasonHandler.cpp" />
    <ClCompile Include="SwitchInvasion.cpp" />
    <ClCompile Include="SwitchNonInvasion.cpp" />
    <ClCompile Include="Season\SeasonManager.cpp" />
    <ClCompile Include="Tournament\Tournament.cpp" />
    <ClCompile Include="Tournament\TournamentManager.cpp" />
    <ClCompile Include="WOPS\Event\WOPSAutoHackDetectionEvent.cpp" />
    <ClCompile Include="WOPS\Event\WOPSAutoHackRemoveDebuffEvent.cpp" />
    <ClCompile Include="WOPS\Event\WOPSChatBlockCharacterEvent.cpp" />
    <ClCompile Include="WOPS\Event\WOPSCmdKnightage.cpp" />
    <ClCompile Include="WOPS\Event\WOPSCommandItemEnchantDiscount.cpp" />
    <ClCompile Include="WOPS\Event\WOPSEditKnightageTrophyPoint.cpp" />
    <ClCompile Include="WOPS\Event\WOPSExchangeItemEvent.cpp" />
    <ClCompile Include="WOPS\Event\WOPSKickAccountEvent.cpp" />
    <ClCompile Include="WOPS\Event\WOPSKickCharacterEvent.cpp" />
    <ClCompile Include="WOPS\Event\WOPSLoginBlockAccountEvent.cpp" />
    <ClCompile Include="WOPS\Event\WOPSMinigameRewardEvent.cpp" />
    <ClCompile Include="WOPS\Event\WOPSMopupEvent.cpp" />
    <ClCompile Include="WOPS\Event\WOPSNoticeInstantEvent.cpp" />
    <ClCompile Include="WOPS\Event\WOPSNoticeReservationEvent.cpp" />
    <ClCompile Include="WOPS\Event\WOPSWorldCloseEvent.cpp" />
    <ClCompile Include="WOPS\Event\WOPSWorldOpenEvent.cpp" />
    <ClCompile Include="WShop\BaseItemTranscation.cpp" />
    <ClCompile Include="WShop\KnightageTranscation.cpp" />
    <ClCompile Include="WShop\PlayerWShopAction.cpp" />
    <ClCompile Include="WShop\TaskItemTranscation.cpp" />
    <ClCompile Include="WShop\WShop.cpp" />
    <ClCompile Include="WShop\WShopBillingEventHandler.cpp" />
    <ClCompile Include="WShop\WShopInventoryEventHandler.cpp" />
    <ClCompile Include="WShop\WShopJewelEventHandler.cpp" />
    <ClCompile Include="WShop\WShopJob.cpp" />
    <ClCompile Include="WShop\WShopJobManager.cpp" />
    <ClCompile Include="WShop\WShopStatusEventHandler.cpp" />
    <ClCompile Include="WShop\WShopTask.cpp" />
    <ClCompile Include="WShop\WShopTaskChargeJewel.cpp" />
    <ClCompile Include="WShop\WShopTaskCheckBalance.cpp" />
    <ClCompile Include="WShop\WShopTaskGift.cpp" />
    <ClCompile Include="WShop\WShopTaskInventory.cpp" />
    <ClCompile Include="WShop\WShopTaskItemTranscation.cpp" />
    <ClCompile Include="WShop\WShopTaskMopupPurchaseJewel.cpp" />
    <ClCompile Include="WShop\WShopTaskPickup.cpp" />
    <ClCompile Include="WShop\WShopTaskPickupTradeJewel.cpp" />
    <ClCompile Include="WShop\WShopTaskPurchase.cpp" />
    <ClCompile Include="WShop\WShopTaskPurchaseCharacSlot.cpp" />
    <ClCompile Include="WShop\WShopTaskPurchaseJewel.cpp" />
    <ClCompile Include="WShop\WShopTaskTradeJewel.cpp" />
    <ClCompile Include="ZoneLoadBalancer.cpp" />
    <ClCompile Include="MultistageDungeonHandler.cpp" />
    <ClCompile Include="MultistageDungeonManager.cpp" />
    <ClCompile Include="PartyDungeonManager.cpp" />
    <ClCompile Include="ExecutionManager.cpp" />
    <ClCompile Include="CommunityHandler.cpp" />
    <ClCompile Include="GloryRankManager.cpp" />
    <ClCompile Include="GloryRankHandler.cpp" />
    <ClCompile Include="GMCommandSystem.cpp" />
    <ClCompile Include="InstanceManager.cpp" />
    <ClCompile Include="JoinRankManager.cpp" />
    <ClCompile Include="JoinWaitHandler.cpp" />
    <ClCompile Include="main.cpp" />
    <ClCompile Include="MissionMapHandler.cpp" />
    <ClCompile Include="PartyAction.cpp" />
    <ClCompile Include="PartyEntityFactoryImpl.cpp" />
    <ClCompile Include="PartyEntityManager.cpp" />
    <ClCompile Include="PartyHandler.cpp" />
    <ClCompile Include="PartySearchManager.cpp" />
    <ClCompile Include="PCRoom\Export\PCBillAdapterLoader.cpp" />
    <ClCompile Include="PCRoom\PCRoomBillingEventHandler.cpp" />
    <ClCompile Include="PCRoom\PCRoomBillingManager.cpp" />
    <ClCompile Include="PlayerBlockAction.cpp" />
    <ClCompile Include="PlayerChatPenaltyAction.cpp" />
    <ClCompile Include="PlayerDamageMeterAction.cpp" />
    <ClCompile Include="PlayerFriendAction.cpp" />
    <ClCompile Include="PlayerHandler.cpp" />
    <ClCompile Include="PlayerJoinAction.cpp" />
    <ClCompile Include="PlayerMissionmapAction.cpp" />
    <ClCompile Include="PlayerPartyAction.cpp" />
    <ClCompile Include="PlayerSearchPartyAction.cpp" />
    <ClCompile Include="PlayerPortalAction.cpp" />
    <ClCompile Include="PlayerServantAction.cpp" />
    <ClCompile Include="PlayerServantHandler.cpp" />
    <ClCompile Include="PlayerStateJoinExecution.cpp" />
    <ClCompile Include="PlayerStateWait.cpp" />
    <ClCompile Include="PlayerStateZone.cpp" />
    <ClCompile Include="PlayerSearchCharAction.cpp" />
    <ClCompile Include="DungeonMatchHandler.cpp" />
    <ClCompile Include="DungeonMatchManager.cpp" />
    <ClCompile Include="DungeonMatchRoom.cpp" />
    <ClCompile Include="Ranking\PlayerRankingAction.cpp" />
    <ClCompile Include="Ranking\RankingHandler.cpp" />
    <ClCompile Include="Ranking\RankingManager.cpp" />
    <ClCompile Include="stdafx.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release_Build|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release_Global|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Shipping_Global|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Shipping_Japan|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release_Japan|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Shipping|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release_Build|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release_Global|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Shipping_Global|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Shipping_Japan|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release_Japan|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Shipping|Win32'">Create</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="EntityManager.cpp" />
    <ClCompile Include="PlayerLobbyAction.cpp" />
    <ClCompile Include="PlayerStateLobby.cpp" />
    <ClCompile Include="SyncSystem.cpp" />
    <ClCompile Include="WOPS\Event\WOPSEvent.cpp" />
    <ClCompile Include="WOPS\Event\WOPSGoldenTimeBuffEvent.cpp" />
    <ClCompile Include="WOPS\Event\WOPSGoldenTimeGiftEvent.cpp" />
    <ClCompile Include="WOPS\WOPSEventNotifier.cpp" />
    <ClCompile Include="WOPS\WOPSHandler.cpp" />
    <ClCompile Include="WOPS\WOPSEventManager.cpp" />
    <ClCompile Include="WorldEntityFactoryImpl.cpp" />
    <ClCompile Include="WorldHandler.cpp" />
    <ClCompile Include="WorldRunner.cpp" />
    <ClCompile Include="WorldServer.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="AchievementHandler.h" />
    <ClInclude Include="AchievementJobLimitAchievementCache.h" />
    <ClInclude Include="AchievementManager.h" />
    <ClInclude Include="AppChat.h" />
    <ClInclude Include="AttributeBlock.h" />
    <ClInclude Include="AttributeFriend.h" />
    <ClInclude Include="AttributeParty.h" />
    <ClInclude Include="AttributeSearchParty.h" />
    <ClInclude Include="AttributeSearchChar.h" />
    <ClInclude Include="BurningStaminaHandler.h" />
    <ClInclude Include="BurningStaminaInfo.h" />
    <ClInclude Include="BurningStaminaManager.h" />
    <ClInclude Include="BurningStaminaTimeInfo.h" />
    <ClInclude Include="BurningStaminaUserInfo.h" />
    <ClInclude Include="Channel.h" />
    <ClInclude Include="ChannelDominion.h" />
    <ClInclude Include="ChannelFieldpoint.h" />
    <ClInclude Include="ChannelKnightageFieldRaid.h" />
    <ClInclude Include="ChannelGroup.h" />
    <ClInclude Include="ChannelManager.h" />
    <ClInclude Include="ChannelNull.h" />
    <ClInclude Include="ChatHandler.h" />
    <ClInclude Include="BurningStaminaContinetInfo.h" />
    <ClInclude Include="ColosseumHandler.h" />
    <ClInclude Include="ContentsDeactivator.h" />
    <ClInclude Include="DailyComparer.h" />
    <ClInclude Include="DailyLoginErrorEnum.h" />
    <ClInclude Include="DailyMissionHandler.h" />
    <ClInclude Include="DailyMissionManager.h" />
    <ClInclude Include="DailyTimeConnectionManager.h" />
    <ClInclude Include="Entity4WorldServer.h" />
    <ClInclude Include="Dominion\Dominion.h" />
    <ClInclude Include="Dominion\DominionManager.h" />
    <ClInclude Include="EntityParty.h" />
    <ClInclude Include="EntityPlayer.h" />
    <ClInclude Include="EventSchedule.h" />
    <ClInclude Include="EventScheduleManager.h" />
    <ClInclude Include="Event\EventSystem.h" />
    <ClInclude Include="Event\PlayerReturnEventAction.h" />
    <ClInclude Include="ExecInstanceBase.h" />
    <ClInclude Include="Execution.h" />
    <ClInclude Include="ExecutionGroup.h" />
    <ClInclude Include="ExecutionNull.h" />
    <ClInclude Include="Fcs\WorldFcsAsyncReceiverAuth.h" />
    <ClInclude Include="FieldPoint\FieldPointForwarder.h" />
    <ClInclude Include="FieldPoint\FieldPointHandler.h" />
    <ClInclude Include="FieldPoint\FieldPointManager.h" />
    <ClInclude Include="FieldPoint\FieldPointProcessor.h" />
    <ClInclude Include="FieldPoint\FieldPointStates.h" />
    <ClInclude Include="Instance.h" />
    <ClInclude Include="InstanceAltarElement.h" />
    <ClInclude Include="InstanceBattlefieldSky.h" />
    <ClInclude Include="InstanceBloodCastle.h" />
    <ClInclude Include="InstanceChaosCastle.h" />
    <ClInclude Include="InstanceColloseumPvp33.h" />
    <ClInclude Include="InstanceColosseumPve.h" />
    <ClInclude Include="InstanceEndlessTower.h" />
    <ClInclude Include="InstanceGroup.h" />
    <ClInclude Include="InstanceMultisage.h" />
    <ClInclude Include="InstanceNormal.h" />
    <ClInclude Include="InstanceNull.h" />
    <ClInclude Include="InstancePartyInfo.h" />
    <ClInclude Include="InstancePvp.h" />
    <ClInclude Include="InstanceSinglestage.h" />
    <ClInclude Include="InstanceTournament.h" />
    <ClInclude Include="InstanceWorldcross.h" />
    <ClInclude Include="Knightage\KnightageFieldRaidScheduler.h" />
    <ClInclude Include="Knightage\KnightagePaymentScheduler.h" />
    <ClInclude Include="Liberation\LiberationManager.h" />
    <ClInclude Include="ManagerInvasionContext.h" />
    <ClInclude Include="Membership\PlayerMembershipAction.h" />
    <ClInclude Include="Mopup\MopupHandler.h" />
    <ClInclude Include="OhrdorTreasureHunt.h" />
    <ClInclude Include="PlayerDailyTimeConnectionAction.h" />
    <ClInclude Include="PlayerTeacherAndStudentAction.h" />
    <ClInclude Include="PVPFieldSchedule.h" />
    <ClInclude Include="QuestSystem.h" />
    <ClInclude Include="ReLoginGetFcsAuthInfoHandler.h" />
    <ClInclude Include="JoinZoneContext.h" />
    <ClInclude Include="JoinZoneContextBase.h" />
    <ClInclude Include="JoinZoneContextChangeChannel.h" />
    <ClInclude Include="JoinZoneContextChangeMap.h" />
    <ClInclude Include="JoinZoneContextCheckIn.h" />
    <ClInclude Include="JoinZoneContextLogout.h" />
    <ClInclude Include="JoinZoneContextNone.h" />
    <ClInclude Include="JoinZoneContextTutorial.h" />
    <ClInclude Include="JoinZoneContextWarpMap.h" />
    <ClInclude Include="LoopbackEvent\EventLoopback.h" />
    <ClInclude Include="LuckyMonster\LuckyMonsterHandler.h" />
    <ClInclude Include="LuckyMonster\LuckyMonsterManager.h" />
    <ClInclude Include="managerAuthToken.h" />
    <ClInclude Include="ManagerJoinZoneContext.h" />
    <ClInclude Include="MissionMapJoinManager.h" />
    <ClInclude Include="Mopup\MopupManager.h" />
    <ClInclude Include="PCRoom\PCRoomBillingEventLoopBackHandler.h" />
    <ClInclude Include="PlayerDailyLoginAction.h" />
    <ClInclude Include="DailyLoginHandler.h" />
    <ClInclude Include="DailyLoginManager.h" />
    <ClInclude Include="DamageMeterHandler.h" />
    <ClInclude Include="DamageMeterManager.h" />
    <ClInclude Include="DataCache.h" />
    <ClInclude Include="DungeonLog.h" />
    <ClInclude Include="DungeonLogManager.h" />
    <ClInclude Include="EventSetter.h" />
    <ClInclude Include="GlobalGameConfigInfo.h" />
    <ClInclude Include="GlobalGameConfigManager.h" />
    <ClInclude Include="Invasion\AttributeInvasion.h" />
    <ClInclude Include="Invasion\InvasionAction.h" />
    <ClInclude Include="JoinRankTimeCalculator.h" />
    <ClInclude Include="KISS\KISSApi.h" />
    <ClInclude Include="KISS\KISSApiWrapper.h" />
    <ClInclude Include="Knightage\Knightage.h" />
    <ClInclude Include="Knightage\KnightageAbility.h" />
    <ClInclude Include="Knightage\KnightageHandler.h" />
    <ClInclude Include="Knightage\KnightageLoader.h" />
    <ClInclude Include="Knightage\KnightageManager.h" />
    <ClInclude Include="Knightage\KnightageMember.h" />
    <ClInclude Include="Knightage\KnightageSaveChecker.h" />
    <ClInclude Include="Knightage\PlayerKnightageAction.h" />
    <ClInclude Include="MonitorWorld.h" />
    <ClInclude Include="PlayerDailyMissionAction.h" />
    <ClInclude Include="Raid\BaseRaid.h" />
    <ClInclude Include="Raid\RaidHandler.h" />
    <ClInclude Include="Raid\RaidManager.h" />
    <ClInclude Include="Raid\TournamentRaid.h" />
    <ClInclude Include="Ranking\Ranking.h" />
    <ClInclude Include="Season\SeasonHandler.h" />
    <ClInclude Include="SwitchInvasion.h" />
    <ClInclude Include="SwitchInvasionBase.h" />
    <ClInclude Include="SwitchNonInvasion.h" />
    <ClInclude Include="Season\SeasonManager.h" />
    <ClInclude Include="Tournament\Tournament.h" />
    <ClInclude Include="Tournament\TournamentManager.h" />
    <ClInclude Include="WOPS\Event\WOPSAutoHackDetectionEvent.h" />
    <ClInclude Include="WOPS\Event\WOPSAutoHackRemoveDebuffEvent.h" />
    <ClInclude Include="WOPS\Event\WOPSChatBlockCharacterEvent.h" />
    <ClInclude Include="WOPS\Event\WOPSCmdKnightage.h" />
    <ClInclude Include="WOPS\Event\WOPSCommandItemEnchantDiscount.h" />
    <ClInclude Include="WOPS\Event\WOPSEditKnightageTrophyPoint.h" />
    <ClInclude Include="WOPS\Event\WOPSExchangeItemEvent.h" />
    <ClInclude Include="WOPS\Event\WOPSKickAccountEvent.h" />
    <ClInclude Include="WOPS\Event\WOPSKickCharacterEvent.h" />
    <ClInclude Include="WOPS\Event\WOPSLoginBlockAccountEvent.h" />
    <ClInclude Include="WOPS\Event\WOPSMinigameRewardEvent.h" />
    <ClInclude Include="WOPS\Event\WOPSMopupEvent.h" />
    <ClInclude Include="WOPS\Event\WOPSNoticeInstantEvent.h" />
    <ClInclude Include="WOPS\Event\WOPSNoticeReservationEvent.h" />
    <ClInclude Include="WOPS\Event\WOPSWorldCloseEvent.h" />
    <ClInclude Include="WOPS\Event\WOPSWorldOpenEvent.h" />
    <ClInclude Include="WShop\BaseItemTranscation.h" />
    <ClInclude Include="WShop\KnightageTranscation.h" />
    <ClInclude Include="WShop\PlayerWShopAction.h" />
    <ClInclude Include="WShop\TaskItemTranscation.h" />
    <ClInclude Include="WShop\WShop.h" />
    <ClInclude Include="WShop\WShopBillingEventHandler.h" />
    <ClInclude Include="WShop\WShopInventoryEventHandler.h" />
    <ClInclude Include="WShop\WShopJewelEventHandler.h" />
    <ClInclude Include="WShop\WShopJob.h" />
    <ClInclude Include="WShop\WShopJobManager.h" />
    <ClInclude Include="WShop\WShopStatusEventHandler.h" />
    <ClInclude Include="WShop\WShopTask.h" />
    <ClInclude Include="WShop\WShopTaskChargeJewel.h" />
    <ClInclude Include="WShop\WShopTaskCheckBalance.h" />
    <ClInclude Include="WShop\WShopTaskGift.h" />
    <ClInclude Include="WShop\WShopTaskInventory.h" />
    <ClInclude Include="WShop\WShopTaskItemTranscation.h" />
    <ClInclude Include="WShop\WShopTaskMopupPurchaseJewel.h" />
    <ClInclude Include="WShop\WShopTaskPickup.h" />
    <ClInclude Include="WShop\WShopTaskPickupTradeJewel.h" />
    <ClInclude Include="WShop\WShopTaskPurchase.h" />
    <ClInclude Include="WShop\WShopTaskPurchaseCharacSlot.h" />
    <ClInclude Include="WShop\WShopTaskPurchaseJewel.h" />
    <ClInclude Include="WShop\WShopTaskTradeJewel.h" />
    <ClInclude Include="ZoneLoadBalancer.h" />
    <ClInclude Include="MultistageDungeonHandler.h" />
    <ClInclude Include="MultistageDungeonManager.h" />
    <ClInclude Include="PartyDungeonManager.h" />
    <ClInclude Include="ExecutionManager.h" />
    <ClInclude Include="CommunityHandler.h" />
    <ClInclude Include="GloryRankManager.h" />
    <ClInclude Include="GloryRankHandler.h" />
    <ClInclude Include="GMCommandSystem.h" />
    <ClInclude Include="InstanceBinding.h" />
    <ClInclude Include="InstanceManager.h" />
    <ClInclude Include="JoinRankManager.h" />
    <ClInclude Include="JoinWaitHandler.h" />
    <ClInclude Include="MissionMapHandler.h" />
    <ClInclude Include="PartyAction.h" />
    <ClInclude Include="PartyEntityFactoryImpl.h" />
    <ClInclude Include="PartyEntityManager.h" />
    <ClInclude Include="PartyHandler.h" />
    <ClInclude Include="PartySearchManager.h" />
    <ClInclude Include="PCRoom\Export\PCBillAdapter.h" />
    <ClInclude Include="PCRoom\Export\PCBillAdapterInterfaces.h" />
    <ClInclude Include="PCRoom\Export\PCBillAdapterLoader.h" />
    <ClInclude Include="PCRoom\PCRoomBillingEventHandler.h" />
    <ClInclude Include="PCRoom\PCRoomBillingManager.h" />
    <ClInclude Include="PlayerBlockAction.h" />
    <ClInclude Include="PlayerChatPenaltyAction.h" />
    <ClInclude Include="PlayerDamageMeterAction.h" />
    <ClInclude Include="PlayerFriendAction.h" />
    <ClInclude Include="PlayerHandler.h" />
    <ClInclude Include="PlayerJoinAction.h" />
    <ClInclude Include="PlayerMissionmapAction.h" />
    <ClInclude Include="PlayerPartyAction.h" />
    <ClInclude Include="PlayerSearchPartyAction.h" />
    <ClInclude Include="PlayerPortalAction.h" />
    <ClInclude Include="PlayerServantAction.h" />
    <ClInclude Include="PlayerServantHandler.h" />
    <ClInclude Include="PlayerStateJoinExecution.h" />
    <ClInclude Include="PlayerStateWait.h" />
    <ClInclude Include="PlayerStateZone.h" />
    <ClInclude Include="PlayerSearchCharAction.h" />
    <ClInclude Include="DungeonMatchHandler.h" />
    <ClInclude Include="DungeonMatchManager.h" />
    <ClInclude Include="DungeonMatchRoom.h" />
    <ClInclude Include="Ranking\PlayerRankingAction.h" />
    <ClInclude Include="Ranking\RankingHandler.h" />
    <ClInclude Include="Ranking\RankingManager.h" />
    <ClInclude Include="stdafx.h" />
    <ClInclude Include="AttributePlayer.h" />
    <ClInclude Include="EntityManager.h" />
    <ClInclude Include="EntityWorldDefine.h" />
    <ClInclude Include="PlayerLobbyAction.h" />
    <ClInclude Include="PlayerStateLobby.h" />
    <ClInclude Include="SyncSystem.h" />
    <ClInclude Include="WOPS\Event\WOPSEvent.h" />
    <ClInclude Include="WOPS\Event\WOPSGoldenTimeBuffEvent.h" />
    <ClInclude Include="WOPS\Event\WOPSGoldenTimeGiftEvent.h" />
    <ClInclude Include="WOPS\WOPSEventNotifier.h" />
    <ClInclude Include="WOPS\WOPSHandler.h" />
    <ClInclude Include="WOPS\WOPSEventManager.h" />
    <ClInclude Include="WorldEntityFactoryImpl.h" />
    <ClInclude Include="WorldHandler.h" />
    <ClInclude Include="WorldRunner.h" />
    <ClInclude Include="WorldServer.h" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\..\Shared\Shared.vcxproj">
      <Project>{33ac5b3d-1dc9-40a6-aa37-ecce1e059d43}</Project>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>