﻿#include "stdafx.h"
#include <Backend/Zone/ZoneServer.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerColosseum.h>



namespace mu2
{

ActionPlayerColosseum::ActionPlayerColosseum( EntityPlayer* owner )
	: Action4Player( owner, ID )
{
	CounterInc("ActionPlayerColosseum");
}

ActionPlayerColosseum::~ActionPlayerColosseum()
{
	CounterDec("ActionPlayerColosseum");
}

void ActionPlayerColosseum::SetupColosseumHistories( const ColosseumHistories& histories )
{
	auto attr = GetOwnerPlayer()->GetAttribute< AttributePlayer >();
	attr->colosseumHistories.clear();

	for( auto& i : histories )
	{
		if( !attr->colosseumHistories.emplace( i.serialHistory, i ).second )
		{
			MU2_ASSERT( 0 );
			MU2_WARN_LOG( LogCategory::CONTENTS, "ActionPlayerColosseum::SetupColosseumHistories > emplace failed. Char(%s:%u), index(%llu)", attr->charName.c_str(), attr->charId, i.serialHistory);
		}
	}
}

void	ActionPlayerColosseum::IncVictoryCount()
{
	GetOwnerPlayer()->SetColosseumVictoryCount( GetOwnerPlayer()->GetColosseumVictoryCount() + 1 );

	updateToDbColosseumBattleResult();
}

void	ActionPlayerColosseum::IncDefeatCount()
{
	GetOwnerPlayer()->SetColosseumDefeatCount( GetOwnerPlayer()->GetColosseumDefeatCount() + 1 );

	updateToDbColosseumBattleResult();
}

void	ActionPlayerColosseum::AddColosseumPoint( UInt32 point )
{
	VERIFY_RETURN( point,  );

	GetOwnerPlayer()->SetColosseumPoint(GetOwnerPlayer()->GetColosseumPoint() + point);

	updateToDbColosseumPoint();	
}
void	ActionPlayerColosseum::RemoveColosseumPoint(UInt32 point)
{
	VERIFY_RETURN(point, );

	GetOwnerPlayer()->SetColosseumPoint(std::max<Int32>(0, GetOwnerPlayer()->GetColosseumPoint() - point));
	
	updateToDbColosseumPoint();
}

void	ActionPlayerColosseum::UpdateColosseumTitle( UInt8 CWCount, UInt8 titleId )
{
	if(GetOwnerPlayer()->GetColosseumCWCount() < CWCount)
	{
		GetOwnerPlayer()->SetColosseumCWCount(CWCount);
		GetOwnerPlayer()->SetColosseumTitleId(titleId);

		notifyColosseumTitleInfoToClient();
		updateToDbColosseumTitleId();
	}
}

void	ActionPlayerColosseum::ResetColosseumTitle()
{
	GetOwnerPlayer()->SetColosseumCWCount(0);
	GetOwnerPlayer()->SetColosseumTitleId(0);

	notifyColosseumTitleInfoToClient();
	updateToDbColosseumTitleId();
}

void	ActionPlayerColosseum::UpdateColossumRevenge(SerialColosseumhistory index, UInt8 result )
{
	AttributePlayer &attr = GetOwnerPlayer()->GetPlayerAttr();

	auto it = attr.colosseumHistories.find( index );
	if( attr.colosseumHistories.end() == it )
	{
		MU2_ASSERT(0);
		MU2_WARN_LOG( LogCategory::CONTENTS, "ActionPlayerColosseum::UpdateColossumRevenge >> revenge info is not exist. char(%s:%d) index(%llu)", GetOwnerPlayer()->GetCharName().c_str(), GetOwnerPlayer()->GetCharId(), index );
		return;
	}

	VERIFY_RETURN( ColosseumRevengeState::NONE == it->second.revenge, );

	it->second.revenge = result;

	ENtfGameUpdateCharColosseumRevengeInfo* ntf = NEW ENtfGameUpdateCharColosseumRevengeInfo;
	ntf->index	= index;
	ntf->state	= result;
	SERVER.SendToClient( GetOwnerPlayer(), EventPtr( ntf ) );

	updateToDbColosseumRevengeInfo( index, result );
}

void	ActionPlayerColosseum::UpdateAIPCResult( const ColosseumAIPCResult& info )
{
	AttributePlayer &attr = GetOwnerPlayer()->GetPlayerAttr();

	AddColosseumPoint( info.history.point );
	
	MU2_ASSERT( attr.colosseumHistories.emplace(info.history.serialHistory, info.history ).second );

	if( ColosseumConstant::HISTORY_LIST_COUNT < attr.colosseumHistories.size() )
	{
		attr.colosseumHistories.erase( attr.colosseumHistories.begin() );
	}

	ENtfGameAddColosseumHistory* ntf = NEW ENtfGameAddColosseumHistory;
	ntf->history	= info.history;
	SERVER.SendToClient( GetOwnerPlayer(), EventPtr( ntf ) );

	EReqDbUpdateColosseumAIPCResult* req = NEW EReqDbUpdateColosseumAIPCResult;
	req->info = info;
	SERVER.SendToDb( GetOwnerPlayer(), EventPtr( req ) );

	SendColosseumInfoToClient();
}

void	ActionPlayerColosseum::SendColosseumInfoToClient() const
{
	ENtfGameUpdateCharColosseumInfo* ntf = NEW ENtfGameUpdateCharColosseumInfo;

	ntf->colosseumPoint			= GetOwnerPlayer()->GetColosseumPoint();
	ntf->colosseumVictoryCount	= GetOwnerPlayer()->GetColosseumVictoryCount();
	ntf->colosseumDefeatCount	= GetOwnerPlayer()->GetColosseumDefeatCount();
	ntf->colosseumTitleId		= GetOwnerPlayer()->GetColosseumTitleId();
	ntf->colosseumCWCount		= GetOwnerPlayer()->GetColosseumCWCount();

	SERVER.SendToClient( GetOwnerPlayer(), EventPtr( ntf ) );
}

void	ActionPlayerColosseum::notifyColosseumTitleInfoToClient() const
{
	ENtfGameColosseumTitle* ntf = NEW ENtfGameColosseumTitle;
	ntf->CWCount = GetOwnerPlayer()->GetColosseumCWCount();
	ntf->titleId = GetOwnerPlayer()->GetColosseumTitleId();
	SERVER.SendToClient( GetOwnerPlayer(), EventPtr( ntf ) );
}

void	ActionPlayerColosseum::updateToDbColosseumPoint() const
{
	EReqDbUpdateCharColosseumPoint* req = NEW EReqDbUpdateCharColosseumPoint;	
	req->charId = GetOwnerPlayer()->GetCharId();
	req->point	= GetOwnerPlayer()->GetColosseumPoint();
	SERVER.SendToDb( GetOwnerPlayer(), EventPtr( req ) );
}

void	ActionPlayerColosseum::updateToDbColosseumBattleResult() const
{
	EReqDbUpdateCharColosseumBattleResult* req = NEW EReqDbUpdateCharColosseumBattleResult;	
	req->charId		= GetOwnerPlayer()->GetCharId();
	req->victory	= GetOwnerPlayer()->GetColosseumVictoryCount();
	req->defeat		= GetOwnerPlayer()->GetColosseumDefeatCount();
	SERVER.SendToDb(GetOwnerPlayer(), EventPtr(req));
}

void	ActionPlayerColosseum::updateToDbColosseumTitleId() const
{
	EReqDbUpdateCharColosseumTitleId* req = NEW EReqDbUpdateCharColosseumTitleId;	
	req->charId		= GetOwnerPlayer()->GetCharId();
	req->CWCount	= GetOwnerPlayer()->GetColosseumCWCount();
	req->titleId	= GetOwnerPlayer()->GetColosseumTitleId();
	SERVER.SendToDb( GetOwnerPlayer(), EventPtr( req ) );
}

void	ActionPlayerColosseum::updateToDbColosseumRevengeInfo( UInt64 index, UInt8 result ) const
{
	EReqDbUpdateCharColosseumHistory* req = NEW EReqDbUpdateCharColosseumHistory;
	req->charId	= GetOwnerPlayer()->GetCharId();
	req->index	= index;
	req->state	= result;
	SERVER.SendToDb( GetOwnerPlayer(), EventPtr( req ) );
}

}
