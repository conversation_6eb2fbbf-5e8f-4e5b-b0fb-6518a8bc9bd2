; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	??0exception@std@@QEAA@AEBV01@@Z		; std::exception::exception
PUBLIC	?what@exception@std@@UEBAPEBDXZ			; std::exception::what
PUBLIC	??_Gexception@std@@UEAAPEAXI@Z			; std::exception::`scalar deleting destructor'
PUBLIC	??0bad_alloc@std@@QEAA@AEBV01@@Z		; std::bad_alloc::bad_alloc
PUBLIC	??_Gbad_alloc@std@@UEAAPEAXI@Z			; std::bad_alloc::`scalar deleting destructor'
PUBL<PERSON>	??0bad_array_new_length@std@@QEAA@XZ		; std::bad_array_new_length::bad_array_new_length
PUBLIC	??1bad_array_new_length@std@@UEAA@XZ		; std::bad_array_new_length::~bad_array_new_length
PUBLIC	??0bad_array_new_length@std@@QEAA@AEBV01@@Z	; std::bad_array_new_length::bad_array_new_length
PUBLIC	??_Gbad_array_new_length@std@@UEAAPEAXI@Z	; std::bad_array_new_length::`scalar deleting destructor'
PUBLIC	?_Throw_bad_array_new_length@std@@YAXXZ		; std::_Throw_bad_array_new_length
PUBLIC	?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
PUBLIC	?_Xlen_string@std@@YAXXZ			; std::_Xlen_string
PUBLIC	?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z	; std::allocator<wchar_t>::allocate
PUBLIC	?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEBAPEB_WXZ ; std::_String_val<std::_Simple_types<wchar_t> >::_Myptr
PUBLIC	??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
PUBLIC	??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
PUBLIC	??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
PUBLIC	?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_empty
PUBLIC	??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV01@$$QEAV01@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::operator=
PUBLIC	?_Take_contents@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXAEAV12@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Take_contents
PUBLIC	?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
PUBLIC	?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate
PUBLIC	??1?$_Compressed_pair@V?$allocator@_W@std@@V?$_String_val@U?$_Simple_types@_W@std@@@2@$00@std@@QEAA@XZ ; std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>::~_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>
PUBLIC	?Write@PacketStream@mu2@@QEAAIPEBEI@Z		; mu2::PacketStream::Write
PUBLIC	?Read@PacketStream@mu2@@QEAAIPEAEI@Z		; mu2::PacketStream::Read
PUBLIC	??$rw@I@PacketStream@mu2@@QEAA_NAEAI@Z		; mu2::PacketStream::rw<unsigned int>
PUBLIC	?checkingBuffer@PacketStream@mu2@@AEAA_NXZ	; mu2::PacketStream::checkingBuffer
PUBLIC	??$swapEndian@I@PacketStream@mu2@@AEAAIAEBI@Z	; mu2::PacketStream::swapEndian<unsigned int>
PUBLIC	??_GISerializer@mu2@@UEAAPEAXI@Z		; mu2::ISerializer::`scalar deleting destructor'
PUBLIC	?PackUnpack@ItemMakingInfo@mu2@@UEAA_NAEAVPacketStream@2@@Z ; mu2::ItemMakingInfo::PackUnpack
PUBLIC	?Reset@ItemMakingInfo@mu2@@UEAAXXZ		; mu2::ItemMakingInfo::Reset
PUBLIC	??1ItemMakingInfo@mu2@@UEAA@XZ			; mu2::ItemMakingInfo::~ItemMakingInfo
PUBLIC	??_GItemMakingInfo@mu2@@UEAAPEAXI@Z		; mu2::ItemMakingInfo::`scalar deleting destructor'
PUBLIC	?getValue@stWOPSCommand@mu2@@AEBAAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@H@Z ; mu2::stWOPSCommand::getValue
PUBLIC	??1WOPSCommand@mu2@@UEAA@XZ			; mu2::WOPSCommand::~WOPSCommand
PUBLIC	?OnBegin@WOPSCommand@mu2@@UEBAXXZ		; mu2::WOPSCommand::OnBegin
PUBLIC	?OnEnd@WOPSCommand@mu2@@UEBAXXZ			; mu2::WOPSCommand::OnEnd
PUBLIC	?IsOverlapable@WOPSCommand@mu2@@UEBA_NXZ	; mu2::WOPSCommand::IsOverlapable
PUBLIC	?ToString@WOPSCommand@mu2@@UEBA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ ; mu2::WOPSCommand::ToString
PUBLIC	??$_Construct@$00PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<1,wchar_t const *>
PUBLIC	??$_Construct@$01PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<2,wchar_t const *>
PUBLIC	??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
PUBLIC	??0WOPSCommandGoldenTimeGift@mu2@@QEAA@XZ	; mu2::WOPSCommandGoldenTimeGift::WOPSCommandGoldenTimeGift
PUBLIC	??1WOPSCommandGoldenTimeGift@mu2@@UEAA@XZ	; mu2::WOPSCommandGoldenTimeGift::~WOPSCommandGoldenTimeGift
PUBLIC	?OnEnter@WOPSCommandGoldenTimeGift@mu2@@UEBAXPEAVEntityPlayer@2@@Z ; mu2::WOPSCommandGoldenTimeGift::OnEnter
PUBLIC	?parse@WOPSCommandGoldenTimeGift@mu2@@MEAA_NAEBUstWOPSCommand@2@@Z ; mu2::WOPSCommandGoldenTimeGift::parse
PUBLIC	??1?$unique_ptr@UWOPSGoldenTimeGiftEventImpl@mu2@@U?$default_delete@UWOPSGoldenTimeGiftEventImpl@mu2@@@std@@@std@@QEAA@XZ ; std::unique_ptr<mu2::WOPSGoldenTimeGiftEventImpl,std::default_delete<mu2::WOPSGoldenTimeGiftEventImpl> >::~unique_ptr<mu2::WOPSGoldenTimeGiftEventImpl,std::default_delete<mu2::WOPSGoldenTimeGiftEventImpl> >
PUBLIC	??_GWOPSCommandGoldenTimeGift@mu2@@UEAAPEAXI@Z	; mu2::WOPSCommandGoldenTimeGift::`scalar deleting destructor'
PUBLIC	?allocate@?$allocator@UItemMakingInfo@mu2@@@std@@QEAAPEAUItemMakingInfo@mu2@@_K@Z ; std::allocator<mu2::ItemMakingInfo>::allocate
PUBLIC	?_Calculate_growth@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEBA_K_K@Z ; std::vector<mu2::ItemMakingInfo,std::allocator<mu2::ItemMakingInfo> >::_Calculate_growth
PUBLIC	?_Change_array@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAXQEAUItemMakingInfo@mu2@@_K1@Z ; std::vector<mu2::ItemMakingInfo,std::allocator<mu2::ItemMakingInfo> >::_Change_array
PUBLIC	?_Tidy@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAXXZ ; std::vector<mu2::ItemMakingInfo,std::allocator<mu2::ItemMakingInfo> >::_Tidy
PUBLIC	?_Xlength@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@CAXXZ ; std::vector<mu2::ItemMakingInfo,std::allocator<mu2::ItemMakingInfo> >::_Xlength
PUBLIC	??0WOPSGoldenTimeGiftEventImpl@mu2@@QEAA@XZ	; mu2::WOPSGoldenTimeGiftEventImpl::WOPSGoldenTimeGiftEventImpl
PUBLIC	??1WOPSGoldenTimeGiftEventImpl@mu2@@QEAA@XZ	; mu2::WOPSGoldenTimeGiftEventImpl::~WOPSGoldenTimeGiftEventImpl
PUBLIC	??$Val@I@stWOPSCommand@mu2@@QEBAIH@Z		; mu2::stWOPSCommand::Val<unsigned int>
PUBLIC	??$_Emplace_one_at_back@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAAEAUItemMakingInfo@mu2@@AEBU23@@Z ; std::vector<mu2::ItemMakingInfo,std::allocator<mu2::ItemMakingInfo> >::_Emplace_one_at_back<mu2::ItemMakingInfo const &>
PUBLIC	??$_Emplace_back_with_unused_capacity@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAAEAUItemMakingInfo@mu2@@AEBU23@@Z ; std::vector<mu2::ItemMakingInfo,std::allocator<mu2::ItemMakingInfo> >::_Emplace_back_with_unused_capacity<mu2::ItemMakingInfo const &>
PUBLIC	??$_Emplace_reallocate@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAPEAUItemMakingInfo@mu2@@QEAU23@AEBU23@@Z ; std::vector<mu2::ItemMakingInfo,std::allocator<mu2::ItemMakingInfo> >::_Emplace_reallocate<mu2::ItemMakingInfo const &>
PUBLIC	??1_Reallocation_guard@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@QEAA@XZ ; std::vector<mu2::ItemMakingInfo,std::allocator<mu2::ItemMakingInfo> >::_Reallocation_guard::~_Reallocation_guard
PUBLIC	??$_Uninitialized_move@PEAUItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@YAPEAUItemMakingInfo@mu2@@QEAU12@0PEAU12@AEAV?$allocator@UItemMakingInfo@mu2@@@0@@Z ; std::_Uninitialized_move<mu2::ItemMakingInfo *,std::allocator<mu2::ItemMakingInfo> >
PUBLIC	??1?$_Uninitialized_backout_al@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@QEAA@XZ ; std::_Uninitialized_backout_al<std::allocator<mu2::ItemMakingInfo> >::~_Uninitialized_backout_al<std::allocator<mu2::ItemMakingInfo> >
PUBLIC	??$_Emplace_back@UItemMakingInfo@mu2@@@?$_Uninitialized_backout_al@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@QEAAX$$QEAUItemMakingInfo@mu2@@@Z ; std::_Uninitialized_backout_al<std::allocator<mu2::ItemMakingInfo> >::_Emplace_back<mu2::ItemMakingInfo>
PUBLIC	??_7exception@std@@6B@				; std::exception::`vftable'
PUBLIC	??_C@_0BC@EOODALEL@Unknown?5exception@		; `string'
PUBLIC	??_7bad_alloc@std@@6B@				; std::bad_alloc::`vftable'
PUBLIC	??_7bad_array_new_length@std@@6B@		; std::bad_array_new_length::`vftable'
PUBLIC	??_C@_0BF@KINCDENJ@bad?5array?5new?5length@	; `string'
PUBLIC	??_R0?AVexception@std@@@8			; std::exception `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
PUBLIC	_TI3?AVbad_array_new_length@std@@
PUBLIC	_CTA3?AVbad_array_new_length@std@@
PUBLIC	??_R0?AVbad_array_new_length@std@@@8		; std::bad_array_new_length `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
PUBLIC	??_R0?AVbad_alloc@std@@@8			; std::bad_alloc `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
PUBLIC	??_C@_0BA@JFNIOLAK@string?5too?5long@		; `string'
PUBLIC	??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@ ; `string'
PUBLIC	??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@		; `string'
PUBLIC	??_7ISerializer@mu2@@6B@			; mu2::ISerializer::`vftable'
PUBLIC	??_C@_0EJ@IFEHPKPB@F?3?2Release_Branch?2Server?2Develo@ ; `string'
PUBLIC	??_C@_0BB@HAIDOJLN@checkingBuffer?$CI?$CJ@	; `string'
PUBLIC	??_C@_0BJ@NEGBBJOE@mu2?3?3PacketStream?3?3Write@ ; `string'
PUBLIC	??_C@_05IOMEMJEC@count@				; `string'
PUBLIC	??_C@_0BI@KGAODAAL@mu2?3?3PacketStream?3?3Read@	; `string'
PUBLIC	??_C@_03COAJHJPB@buf@				; `string'
PUBLIC	??_C@_11LOCGONAA@@				; `string'
PUBLIC	??_7ItemMakingInfo@mu2@@6B@			; mu2::ItemMakingInfo::`vftable'
PUBLIC	??_C@_0EB@JGAFMFJE@F?3?2Release_Branch?2Server?2Develo@ ; `string'
PUBLIC	??_C@_0BK@FMPHEHPK@arryIndex?5?$DM?5values?4size?$CI?$CJ@ ; `string'
PUBLIC	??_C@_0BN@FHEEOEA@mu2?3?3stWOPSCommand?3?3getValue@ ; `string'
PUBLIC	??_C@_0BA@FOIKENOD@vector?5too?5long@		; `string'
PUBLIC	??_R4exception@std@@6B@				; std::exception::`RTTI Complete Object Locator'
PUBLIC	??_R3exception@std@@8				; std::exception::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2exception@std@@8				; std::exception::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@exception@std@@8			; std::exception::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4bad_array_new_length@std@@6B@		; std::bad_array_new_length::`RTTI Complete Object Locator'
PUBLIC	??_R3bad_array_new_length@std@@8		; std::bad_array_new_length::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2bad_array_new_length@std@@8		; std::bad_array_new_length::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@bad_array_new_length@std@@8	; std::bad_array_new_length::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@bad_alloc@std@@8			; std::bad_alloc::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R3bad_alloc@std@@8				; std::bad_alloc::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2bad_alloc@std@@8				; std::bad_alloc::`RTTI Base Class Array'
PUBLIC	??_R4bad_alloc@std@@6B@				; std::bad_alloc::`RTTI Complete Object Locator'
PUBLIC	??_R4ISerializer@mu2@@6B@			; mu2::ISerializer::`RTTI Complete Object Locator'
PUBLIC	??_R0?AUISerializer@mu2@@@8			; mu2::ISerializer `RTTI Type Descriptor'
PUBLIC	??_R3ISerializer@mu2@@8				; mu2::ISerializer::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2ISerializer@mu2@@8				; mu2::ISerializer::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@ISerializer@mu2@@8		; mu2::ISerializer::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_7WOPSCommandGoldenTimeGift@mu2@@6B@		; mu2::WOPSCommandGoldenTimeGift::`vftable'
PUBLIC	??_C@_0GB@PMIBDBHN@F?3?2Release_Branch?2Server?2Develo@ ; `string'
PUBLIC	??_C@_0BO@EHLCCMDK@13?5?$DM?$DN?5command?4GetValueCount?$CI?$CJ@ ; `string'
PUBLIC	??_C@_0CG@IJGMIACC@mu2?3?3WOPSCommandGoldenTimeGift?3@ ; `string'
PUBLIC	??_C@_0EK@BGEBHHHP@WOPSGoldenTimeGiftEvent?3?3isVali@ ; `string'
PUBLIC	??_C@_0EM@OPKCLJNH@WOPSGoldenTimeGiftEvent?3?3isVali@ ; `string'
PUBLIC	??_C@_0EN@BIINNPBA@WOPSGoldenTimeGiftEvent?3?3isVali@ ; `string'
PUBLIC	??_C@_0EN@LJLBNMLN@WOPSGoldenTimeGiftEvent?3?3isVali@ ; `string'
PUBLIC	??_R4ItemMakingInfo@mu2@@6B@			; mu2::ItemMakingInfo::`RTTI Complete Object Locator'
PUBLIC	??_R0?AUItemMakingInfo@mu2@@@8			; mu2::ItemMakingInfo `RTTI Type Descriptor'
PUBLIC	??_R3ItemMakingInfo@mu2@@8			; mu2::ItemMakingInfo::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2ItemMakingInfo@mu2@@8			; mu2::ItemMakingInfo::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@ItemMakingInfo@mu2@@8		; mu2::ItemMakingInfo::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4WOPSCommandGoldenTimeGift@mu2@@6B@		; mu2::WOPSCommandGoldenTimeGift::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVWOPSCommandGoldenTimeGift@mu2@@@8	; mu2::WOPSCommandGoldenTimeGift `RTTI Type Descriptor'
PUBLIC	??_R3WOPSCommandGoldenTimeGift@mu2@@8		; mu2::WOPSCommandGoldenTimeGift::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2WOPSCommandGoldenTimeGift@mu2@@8		; mu2::WOPSCommandGoldenTimeGift::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@WOPSCommandGoldenTimeGift@mu2@@8	; mu2::WOPSCommandGoldenTimeGift::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@WOPSCommand@mu2@@8		; mu2::WOPSCommand::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R0?AVWOPSCommand@mu2@@@8			; mu2::WOPSCommand `RTTI Type Descriptor'
PUBLIC	??_R3WOPSCommand@mu2@@8				; mu2::WOPSCommand::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2WOPSCommand@mu2@@8				; mu2::WOPSCommand::`RTTI Base Class Array'
EXTRN	_purecall:PROC
EXTRN	??2@YAPEAX_K@Z:PROC				; operator new
EXTRN	??3@YAXPEAX_K@Z:PROC				; operator delete
EXTRN	__std_terminate:PROC
EXTRN	_invoke_watson:PROC
EXTRN	memcpy:PROC
EXTRN	wcslen:PROC
EXTRN	__imp_GetStdHandle:PROC
EXTRN	__imp_GetLocalTime:PROC
EXTRN	__imp_SetConsoleTextAttribute:PROC
EXTRN	_wtoi64:PROC
EXTRN	__std_exception_copy:PROC
EXTRN	__std_exception_destroy:PROC
EXTRN	??_Eexception@std@@UEAAPEAXI@Z:PROC		; std::exception::`vector deleting destructor'
EXTRN	??_Ebad_alloc@std@@UEAAPEAXI@Z:PROC		; std::bad_alloc::`vector deleting destructor'
EXTRN	??_Ebad_array_new_length@std@@UEAAPEAXI@Z:PROC	; std::bad_array_new_length::`vector deleting destructor'
EXTRN	?_Xlength_error@std@@YAXPEBD@Z:PROC		; std::_Xlength_error
EXTRN	?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ:PROC	; mu2::Logger::Logging
EXTRN	?isValid@PacketStream@mu2@@QEBA_NXZ:PROC	; mu2::PacketStream::isValid
EXTRN	?remains@PacketStream@mu2@@AEAAIXZ:PROC		; mu2::PacketStream::remains
EXTRN	?SetError@PacketStream@mu2@@AEAAXW4Error@ErrorNet@2@@Z:PROC ; mu2::PacketStream::SetError
EXTRN	??_EISerializer@mu2@@UEAAPEAXI@Z:PROC		; mu2::ISerializer::`vector deleting destructor'
EXTRN	??4DateTime@mu2@@QEAAAEBV01@AEBU_SYSTEMTIME@@@Z:PROC ; mu2::DateTime::operator=
EXTRN	??_EItemMakingInfo@mu2@@UEAAPEAXI@Z:PROC	; mu2::ItemMakingInfo::`vector deleting destructor'
EXTRN	??0WOPSCommand@mu2@@IEAA@W4Enum@WOPSCommandCode@1@@Z:PROC ; mu2::WOPSCommand::WOPSCommand
EXTRN	?isAvailablePeriod@WOPSCommand@mu2@@MEBA_NXZ:PROC ; mu2::WOPSCommand::isAvailablePeriod
EXTRN	?releaseCommand@WOPSCommand@mu2@@MEAAX_N@Z:PROC	; mu2::WOPSCommand::releaseCommand
EXTRN	?checkRelease@WOPSCommand@mu2@@MEAA_NXZ:PROC	; mu2::WOPSCommand::checkRelease
EXTRN	?GetCommandCode@WOPSCommand@mu2@@QEBA?AW4Enum@WOPSCommandCode@2@XZ:PROC ; mu2::WOPSCommand::GetCommandCode
EXTRN	?GetCommandId@WOPSCommand@mu2@@QEBA_KXZ:PROC	; mu2::WOPSCommand::GetCommandId
EXTRN	??_EWOPSCommandGoldenTimeGift@mu2@@UEAAPEAXI@Z:PROC ; mu2::WOPSCommandGoldenTimeGift::`vector deleting destructor'
EXTRN	_CxxThrowException:PROC
EXTRN	__CxxFrameHandler4:PROC
EXTRN	__GSHandlerCheck:PROC
EXTRN	__GSHandlerCheck_EH4:PROC
EXTRN	__security_check_cookie:PROC
EXTRN	??_7type_info@@6B@:BYTE				; type_info::`vftable'
EXTRN	?isBigEndian@PacketStream@mu2@@2_NA:BYTE	; mu2::PacketStream::isBigEndian
EXTRN	?InvaldValue@stWOPSCommand@mu2@@0V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@B:BYTE ; mu2::stWOPSCommand::InvaldValue
EXTRN	__security_cookie:QWORD
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0exception@std@@QEAA@AEBV01@@Z DD imagerel $LN4
	DD	imagerel $LN4+89
	DD	imagerel $unwind$??0exception@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?what@exception@std@@UEBAPEBDXZ DD imagerel $LN5
	DD	imagerel $LN5+56
	DD	imagerel $unwind$?what@exception@std@@UEBAPEBDXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gexception@std@@UEAAPEAXI@Z DD imagerel $LN10
	DD	imagerel $LN10+83
	DD	imagerel $unwind$??_Gexception@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z DD imagerel $LN9
	DD	imagerel $LN9+104
	DD	imagerel $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+83
	DD	imagerel $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_array_new_length@std@@QEAA@XZ DD imagerel $LN14
	DD	imagerel $LN14+95
	DD	imagerel $unwind$??0bad_array_new_length@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1bad_array_new_length@std@@UEAA@XZ DD imagerel $LN14
	DD	imagerel $LN14+47
	DD	imagerel $unwind$??1bad_array_new_length@std@@UEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z DD imagerel $LN14
	DD	imagerel $LN14+54
	DD	imagerel $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z DD imagerel $LN20
	DD	imagerel $LN20+83
	DD	imagerel $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Throw_bad_array_new_length@std@@YAXXZ DD imagerel $LN3
	DD	imagerel $LN3+37
	DD	imagerel $unwind$?_Throw_bad_array_new_length@std@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z DD imagerel $LN5
	DD	imagerel $LN5+159
	DD	imagerel $unwind$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Xlen_string@std@@YAXXZ DD imagerel $LN3
	DD	imagerel $LN3+22
	DD	imagerel $unwind$?_Xlen_string@std@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z DD imagerel $LN13
	DD	imagerel $LN13+162
	DD	imagerel $unwind$?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEBAPEB_WXZ DD imagerel $LN17
	DD	imagerel $LN17+111
	DD	imagerel $unwind$?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEBAPEB_WXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ DD imagerel $LN41
	DD	imagerel $LN41+107
	DD	imagerel $unwind$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z DD imagerel $LN226
	DD	imagerel $LN226+287
	DD	imagerel $unwind$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z@4HA DD imagerel ?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z@4HA
	DD	imagerel ?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z@4HA+27
	DD	imagerel $unwind$?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z DD imagerel $LN221
	DD	imagerel $LN221+150
	DD	imagerel $unwind$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z@4HA DD imagerel ?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z@4HA
	DD	imagerel ?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z@4HA+24
	DD	imagerel $unwind$?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DD imagerel $LN18
	DD	imagerel $LN18+98
	DD	imagerel $unwind$?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV01@$$QEAV01@@Z DD imagerel $LN197
	DD	imagerel $LN197+147
	DD	imagerel $unwind$??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV01@$$QEAV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Take_contents@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXAEAV12@@Z DD imagerel $LN93
	DD	imagerel $LN93+449
	DD	imagerel $unwind$?_Take_contents@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXAEAV12@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ DD imagerel $LN38
	DD	imagerel $LN38+250
	DD	imagerel $unwind$?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DD imagerel $LN62
	DD	imagerel $LN62+266
	DD	imagerel $unwind$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Write@PacketStream@mu2@@QEAAIPEBEI@Z DD imagerel $LN10
	DD	imagerel $LN10+426
	DD	imagerel $unwind$?Write@PacketStream@mu2@@QEAAIPEBEI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Read@PacketStream@mu2@@QEAAIPEAEI@Z DD imagerel $LN10
	DD	imagerel $LN10+602
	DD	imagerel $unwind$?Read@PacketStream@mu2@@QEAAIPEAEI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$rw@I@PacketStream@mu2@@QEAA_NAEAI@Z DD imagerel $LN47
	DD	imagerel $LN47+232
	DD	imagerel $unwind$??$rw@I@PacketStream@mu2@@QEAA_NAEAI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?checkingBuffer@PacketStream@mu2@@AEAA_NXZ DD imagerel $LN5
	DD	imagerel $LN5+78
	DD	imagerel $unwind$?checkingBuffer@PacketStream@mu2@@AEAA_NXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$swapEndian@I@PacketStream@mu2@@AEAAIAEBI@Z DD imagerel $LN6
	DD	imagerel $LN6+100
	DD	imagerel $unwind$??$swapEndian@I@PacketStream@mu2@@AEAAIAEBI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GISerializer@mu2@@UEAAPEAXI@Z DD imagerel $LN10
	DD	imagerel $LN10+65
	DD	imagerel $unwind$??_GISerializer@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?PackUnpack@ItemMakingInfo@mu2@@UEAA_NAEAVPacketStream@2@@Z DD imagerel $LN77
	DD	imagerel $LN77+73
	DD	imagerel $unwind$?PackUnpack@ItemMakingInfo@mu2@@UEAA_NAEAVPacketStream@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GItemMakingInfo@mu2@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+65
	DD	imagerel $unwind$??_GItemMakingInfo@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?getValue@stWOPSCommand@mu2@@AEBAAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@H@Z DD imagerel $LN14
	DD	imagerel $LN14+307
	DD	imagerel $unwind$?getValue@stWOPSCommand@mu2@@AEBAAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@H@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?ToString@WOPSCommand@mu2@@UEBA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ DD imagerel $LN225
	DD	imagerel $LN225+60
	DD	imagerel $unwind$?ToString@WOPSCommand@mu2@@UEBA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Construct@$00PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z DD imagerel $LN188
	DD	imagerel $LN188+836
	DD	imagerel $unwind$??$_Construct@$00PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Construct@$01PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z DD imagerel $LN173
	DD	imagerel $LN173+746
	DD	imagerel $unwind$??$_Construct@$01PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z DD imagerel $LN7
	DD	imagerel $LN7+150
	DD	imagerel $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0WOPSCommandGoldenTimeGift@mu2@@QEAA@XZ DD imagerel $LN103
	DD	imagerel $LN103+166
	DD	imagerel $unwind$??0WOPSCommandGoldenTimeGift@mu2@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???0WOPSCommandGoldenTimeGift@mu2@@QEAA@XZ@4HA DD imagerel ?dtor$0@?0???0WOPSCommandGoldenTimeGift@mu2@@QEAA@XZ@4HA
	DD	imagerel ?dtor$0@?0???0WOPSCommandGoldenTimeGift@mu2@@QEAA@XZ@4HA+24
	DD	imagerel $unwind$?dtor$0@?0???0WOPSCommandGoldenTimeGift@mu2@@QEAA@XZ@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$1@?0???0WOPSCommandGoldenTimeGift@mu2@@QEAA@XZ@4HA DD imagerel ?dtor$1@?0???0WOPSCommandGoldenTimeGift@mu2@@QEAA@XZ@4HA
	DD	imagerel ?dtor$1@?0???0WOPSCommandGoldenTimeGift@mu2@@QEAA@XZ@4HA+29
	DD	imagerel $unwind$?dtor$1@?0???0WOPSCommandGoldenTimeGift@mu2@@QEAA@XZ@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1WOPSCommandGoldenTimeGift@mu2@@UEAA@XZ DD imagerel $LN327
	DD	imagerel $LN327+47
	DD	imagerel $unwind$??1WOPSCommandGoldenTimeGift@mu2@@UEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?parse@WOPSCommandGoldenTimeGift@mu2@@MEAA_NAEBUstWOPSCommand@2@@Z DD imagerel $LN330
	DD	imagerel $LN330+1789
	DD	imagerel $unwind$?parse@WOPSCommandGoldenTimeGift@mu2@@MEAA_NAEBUstWOPSCommand@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0??parse@WOPSCommandGoldenTimeGift@mu2@@MEAA_NAEBUstWOPSCommand@2@@Z@4HA DD imagerel ?dtor$0@?0??parse@WOPSCommandGoldenTimeGift@mu2@@MEAA_NAEBUstWOPSCommand@2@@Z@4HA
	DD	imagerel ?dtor$0@?0??parse@WOPSCommandGoldenTimeGift@mu2@@MEAA_NAEBUstWOPSCommand@2@@Z@4HA+27
	DD	imagerel $unwind$?dtor$0@?0??parse@WOPSCommandGoldenTimeGift@mu2@@MEAA_NAEBUstWOPSCommand@2@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$unique_ptr@UWOPSGoldenTimeGiftEventImpl@mu2@@U?$default_delete@UWOPSGoldenTimeGiftEventImpl@mu2@@@std@@@std@@QEAA@XZ DD imagerel $LN325
	DD	imagerel $LN325+135
	DD	imagerel $unwind$??1?$unique_ptr@UWOPSGoldenTimeGiftEventImpl@mu2@@U?$default_delete@UWOPSGoldenTimeGiftEventImpl@mu2@@@std@@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GWOPSCommandGoldenTimeGift@mu2@@UEAAPEAXI@Z DD imagerel $LN5
	DD	imagerel $LN5+60
	DD	imagerel $unwind$??_GWOPSCommandGoldenTimeGift@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?allocate@?$allocator@UItemMakingInfo@mu2@@@std@@QEAAPEAUItemMakingInfo@mu2@@_K@Z DD imagerel $LN13
	DD	imagerel $LN13+160
	DD	imagerel $unwind$?allocate@?$allocator@UItemMakingInfo@mu2@@@std@@QEAAPEAUItemMakingInfo@mu2@@_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Calculate_growth@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEBA_K_K@Z DD imagerel $LN42
	DD	imagerel $LN42+332
	DD	imagerel $unwind$?_Calculate_growth@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEBA_K_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Change_array@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAXQEAUItemMakingInfo@mu2@@_K1@Z DD imagerel $LN50
	DD	imagerel $LN50+427
	DD	imagerel $unwind$?_Change_array@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAXQEAUItemMakingInfo@mu2@@_K1@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Tidy@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAXXZ DD imagerel $LN50
	DD	imagerel $LN50+370
	DD	imagerel $unwind$?_Tidy@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Xlength@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@CAXXZ DD imagerel $LN3
	DD	imagerel $LN3+22
	DD	imagerel $unwind$?_Xlength@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@CAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0WOPSGoldenTimeGiftEventImpl@mu2@@QEAA@XZ DD imagerel $LN162
	DD	imagerel $LN162+338
	DD	imagerel $unwind$??0WOPSGoldenTimeGiftEventImpl@mu2@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1WOPSGoldenTimeGiftEventImpl@mu2@@QEAA@XZ DD imagerel $LN301
	DD	imagerel $LN301+88
	DD	imagerel $unwind$??1WOPSGoldenTimeGiftEventImpl@mu2@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$Val@I@stWOPSCommand@mu2@@QEBAIH@Z DD imagerel $LN41
	DD	imagerel $LN41+78
	DD	imagerel $unwind$??$Val@I@stWOPSCommand@mu2@@QEBAIH@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Emplace_one_at_back@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAAEAUItemMakingInfo@mu2@@AEBU23@@Z DD imagerel $LN482
	DD	imagerel $LN482+128
	DD	imagerel $unwind$??$_Emplace_one_at_back@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAAEAUItemMakingInfo@mu2@@AEBU23@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Emplace_back_with_unused_capacity@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAAEAUItemMakingInfo@mu2@@AEBU23@@Z DD imagerel $LN40
	DD	imagerel $LN40+211
	DD	imagerel $unwind$??$_Emplace_back_with_unused_capacity@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAAEAUItemMakingInfo@mu2@@AEBU23@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Emplace_reallocate@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAPEAUItemMakingInfo@mu2@@QEAU23@AEBU23@@Z DD imagerel $LN431
	DD	imagerel $LN431+1002
	DD	imagerel $unwind$??$_Emplace_reallocate@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAPEAUItemMakingInfo@mu2@@QEAU23@AEBU23@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???$_Emplace_reallocate@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAPEAUItemMakingInfo@mu2@@QEAU23@AEBU23@@Z@4HA DD imagerel ?dtor$0@?0???$_Emplace_reallocate@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAPEAUItemMakingInfo@mu2@@QEAU23@AEBU23@@Z@4HA
	DD	imagerel ?dtor$0@?0???$_Emplace_reallocate@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAPEAUItemMakingInfo@mu2@@QEAU23@AEBU23@@Z@4HA+27
	DD	imagerel $unwind$?dtor$0@?0???$_Emplace_reallocate@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAPEAUItemMakingInfo@mu2@@QEAU23@AEBU23@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1_Reallocation_guard@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@QEAA@XZ DD imagerel $LN35
	DD	imagerel $LN35+266
	DD	imagerel $unwind$??1_Reallocation_guard@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Uninitialized_move@PEAUItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@YAPEAUItemMakingInfo@mu2@@QEAU12@0PEAU12@AEAV?$allocator@UItemMakingInfo@mu2@@@0@@Z DD imagerel $LN80
	DD	imagerel $LN80+218
	DD	imagerel $unwind$??$_Uninitialized_move@PEAUItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@YAPEAUItemMakingInfo@mu2@@QEAU12@0PEAU12@AEAV?$allocator@UItemMakingInfo@mu2@@@0@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???$_Uninitialized_move@PEAUItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@YAPEAUItemMakingInfo@mu2@@QEAU12@0PEAU12@AEAV?$allocator@UItemMakingInfo@mu2@@@0@@Z@4HA DD imagerel ?dtor$0@?0???$_Uninitialized_move@PEAUItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@YAPEAUItemMakingInfo@mu2@@QEAU12@0PEAU12@AEAV?$allocator@UItemMakingInfo@mu2@@@0@@Z@4HA
	DD	imagerel ?dtor$0@?0???$_Uninitialized_move@PEAUItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@YAPEAUItemMakingInfo@mu2@@QEAU12@0PEAU12@AEAV?$allocator@UItemMakingInfo@mu2@@@0@@Z@4HA+24
	DD	imagerel $unwind$?dtor$0@?0???$_Uninitialized_move@PEAUItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@YAPEAUItemMakingInfo@mu2@@QEAU12@0PEAU12@AEAV?$allocator@UItemMakingInfo@mu2@@@0@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$_Uninitialized_backout_al@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@QEAA@XZ DD imagerel $LN19
	DD	imagerel $LN19+124
	DD	imagerel $unwind$??1?$_Uninitialized_backout_al@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Emplace_back@UItemMakingInfo@mu2@@@?$_Uninitialized_backout_al@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@QEAAX$$QEAUItemMakingInfo@mu2@@@Z DD imagerel $LN35
	DD	imagerel $LN35+186
	DD	imagerel $unwind$??$_Emplace_back@UItemMakingInfo@mu2@@@?$_Uninitialized_backout_al@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@QEAAX$$QEAUItemMakingInfo@mu2@@@Z
pdata	ENDS
;	COMDAT ??_R2WOPSCommand@mu2@@8
rdata$r	SEGMENT
??_R2WOPSCommand@mu2@@8 DD imagerel ??_R1A@?0A@EA@WOPSCommand@mu2@@8 ; mu2::WOPSCommand::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3WOPSCommand@mu2@@8
rdata$r	SEGMENT
??_R3WOPSCommand@mu2@@8 DD 00H				; mu2::WOPSCommand::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2WOPSCommand@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVWOPSCommand@mu2@@@8
data$rs	SEGMENT
??_R0?AVWOPSCommand@mu2@@@8 DQ FLAT:??_7type_info@@6B@	; mu2::WOPSCommand `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVWOPSCommand@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R1A@?0A@EA@WOPSCommand@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@WOPSCommand@mu2@@8 DD imagerel ??_R0?AVWOPSCommand@mu2@@@8 ; mu2::WOPSCommand::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3WOPSCommand@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@WOPSCommandGoldenTimeGift@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@WOPSCommandGoldenTimeGift@mu2@@8 DD imagerel ??_R0?AVWOPSCommandGoldenTimeGift@mu2@@@8 ; mu2::WOPSCommandGoldenTimeGift::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3WOPSCommandGoldenTimeGift@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2WOPSCommandGoldenTimeGift@mu2@@8
rdata$r	SEGMENT
??_R2WOPSCommandGoldenTimeGift@mu2@@8 DD imagerel ??_R1A@?0A@EA@WOPSCommandGoldenTimeGift@mu2@@8 ; mu2::WOPSCommandGoldenTimeGift::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@WOPSCommand@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3WOPSCommandGoldenTimeGift@mu2@@8
rdata$r	SEGMENT
??_R3WOPSCommandGoldenTimeGift@mu2@@8 DD 00H		; mu2::WOPSCommandGoldenTimeGift::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2WOPSCommandGoldenTimeGift@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVWOPSCommandGoldenTimeGift@mu2@@@8
data$rs	SEGMENT
??_R0?AVWOPSCommandGoldenTimeGift@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::WOPSCommandGoldenTimeGift `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVWOPSCommandGoldenTimeGift@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4WOPSCommandGoldenTimeGift@mu2@@6B@
rdata$r	SEGMENT
??_R4WOPSCommandGoldenTimeGift@mu2@@6B@ DD 01H		; mu2::WOPSCommandGoldenTimeGift::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVWOPSCommandGoldenTimeGift@mu2@@@8
	DD	imagerel ??_R3WOPSCommandGoldenTimeGift@mu2@@8
	DD	imagerel ??_R4WOPSCommandGoldenTimeGift@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@ItemMakingInfo@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@ItemMakingInfo@mu2@@8 DD imagerel ??_R0?AUItemMakingInfo@mu2@@@8 ; mu2::ItemMakingInfo::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3ItemMakingInfo@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2ItemMakingInfo@mu2@@8
rdata$r	SEGMENT
??_R2ItemMakingInfo@mu2@@8 DD imagerel ??_R1A@?0A@EA@ItemMakingInfo@mu2@@8 ; mu2::ItemMakingInfo::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@ISerializer@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3ItemMakingInfo@mu2@@8
rdata$r	SEGMENT
??_R3ItemMakingInfo@mu2@@8 DD 00H			; mu2::ItemMakingInfo::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2ItemMakingInfo@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AUItemMakingInfo@mu2@@@8
data$rs	SEGMENT
??_R0?AUItemMakingInfo@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::ItemMakingInfo `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AUItemMakingInfo@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4ItemMakingInfo@mu2@@6B@
rdata$r	SEGMENT
??_R4ItemMakingInfo@mu2@@6B@ DD 01H			; mu2::ItemMakingInfo::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AUItemMakingInfo@mu2@@@8
	DD	imagerel ??_R3ItemMakingInfo@mu2@@8
	DD	imagerel ??_R4ItemMakingInfo@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_C@_0EN@LJLBNMLN@WOPSGoldenTimeGiftEvent?3?3isVali@
CONST	SEGMENT
??_C@_0EN@LJLBNMLN@WOPSGoldenTimeGiftEvent?3?3isVali@ DB 'WOPSGoldenTimeG'
	DB	'iftEvent::isValid > content info is empty. Type(%u), id(%llu)'
	DB	00H						; `string'
CONST	ENDS
;	COMDAT ??_C@_0EN@BIINNPBA@WOPSGoldenTimeGiftEvent?3?3isVali@
CONST	SEGMENT
??_C@_0EN@BIINNPBA@WOPSGoldenTimeGiftEvent?3?3isVali@ DB 'WOPSGoldenTimeG'
	DB	'iftEvent::isValid > subject info is empty. Type(%u), id(%llu)'
	DB	00H						; `string'
CONST	ENDS
;	COMDAT ??_C@_0EM@OPKCLJNH@WOPSGoldenTimeGiftEvent?3?3isVali@
CONST	SEGMENT
??_C@_0EM@OPKCLJNH@WOPSGoldenTimeGiftEvent?3?3isVali@ DB 'WOPSGoldenTimeG'
	DB	'iftEvent::isValid > sender info is empty. Type(%u), id(%llu)', 00H ; `string'
CONST	ENDS
;	COMDAT ??_C@_0EK@BGEBHHHP@WOPSGoldenTimeGiftEvent?3?3isVali@
CONST	SEGMENT
??_C@_0EK@BGEBHHHP@WOPSGoldenTimeGiftEvent?3?3isVali@ DB 'WOPSGoldenTimeG'
	DB	'iftEvent::isValid > gift item is empty. Type(%u), id(%llu)', 00H ; `string'
CONST	ENDS
;	COMDAT ??_C@_0CG@IJGMIACC@mu2?3?3WOPSCommandGoldenTimeGift?3@
CONST	SEGMENT
??_C@_0CG@IJGMIACC@mu2?3?3WOPSCommandGoldenTimeGift?3@ DB 'mu2::WOPSComma'
	DB	'ndGoldenTimeGift::parse', 00H		; `string'
CONST	ENDS
;	COMDAT ??_C@_0BO@EHLCCMDK@13?5?$DM?$DN?5command?4GetValueCount?$CI?$CJ@
CONST	SEGMENT
??_C@_0BO@EHLCCMDK@13?5?$DM?$DN?5command?4GetValueCount?$CI?$CJ@ DB '13 <'
	DB	'= command.GetValueCount()', 00H		; `string'
CONST	ENDS
;	COMDAT ??_C@_0GB@PMIBDBHN@F?3?2Release_Branch?2Server?2Develo@
CONST	SEGMENT
??_C@_0GB@PMIBDBHN@F?3?2Release_Branch?2Server?2Develo@ DB 'F:\Release_Br'
	DB	'anch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSG'
	DB	'oldenTimeGiftEvent.cpp', 00H		; `string'
CONST	ENDS
;	COMDAT ??_7WOPSCommandGoldenTimeGift@mu2@@6B@
CONST	SEGMENT
??_7WOPSCommandGoldenTimeGift@mu2@@6B@ DQ FLAT:??_R4WOPSCommandGoldenTimeGift@mu2@@6B@ ; mu2::WOPSCommandGoldenTimeGift::`vftable'
	DQ	FLAT:??_EWOPSCommandGoldenTimeGift@mu2@@UEAAPEAXI@Z
	DQ	FLAT:?OnEnter@WOPSCommandGoldenTimeGift@mu2@@UEBAXPEAVEntityPlayer@2@@Z
	DQ	FLAT:?OnBegin@WOPSCommand@mu2@@UEBAXXZ
	DQ	FLAT:?OnEnd@WOPSCommand@mu2@@UEBAXXZ
	DQ	FLAT:?IsOverlapable@WOPSCommand@mu2@@UEBA_NXZ
	DQ	FLAT:?ToString@WOPSCommand@mu2@@UEBA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ
	DQ	FLAT:?parse@WOPSCommandGoldenTimeGift@mu2@@MEAA_NAEBUstWOPSCommand@2@@Z
	DQ	FLAT:?isAvailablePeriod@WOPSCommand@mu2@@MEBA_NXZ
	DQ	FLAT:_purecall
	DQ	FLAT:?releaseCommand@WOPSCommand@mu2@@MEAAX_N@Z
	DQ	FLAT:?checkRelease@WOPSCommand@mu2@@MEAA_NXZ
CONST	ENDS
;	COMDAT ??_R1A@?0A@EA@ISerializer@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@ISerializer@mu2@@8 DD imagerel ??_R0?AUISerializer@mu2@@@8 ; mu2::ISerializer::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3ISerializer@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2ISerializer@mu2@@8
rdata$r	SEGMENT
??_R2ISerializer@mu2@@8 DD imagerel ??_R1A@?0A@EA@ISerializer@mu2@@8 ; mu2::ISerializer::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3ISerializer@mu2@@8
rdata$r	SEGMENT
??_R3ISerializer@mu2@@8 DD 00H				; mu2::ISerializer::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2ISerializer@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AUISerializer@mu2@@@8
data$rs	SEGMENT
??_R0?AUISerializer@mu2@@@8 DQ FLAT:??_7type_info@@6B@	; mu2::ISerializer `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AUISerializer@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4ISerializer@mu2@@6B@
rdata$r	SEGMENT
??_R4ISerializer@mu2@@6B@ DD 01H			; mu2::ISerializer::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AUISerializer@mu2@@@8
	DD	imagerel ??_R3ISerializer@mu2@@8
	DD	imagerel ??_R4ISerializer@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R4bad_alloc@std@@6B@
rdata$r	SEGMENT
??_R4bad_alloc@std@@6B@ DD 01H				; std::bad_alloc::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVbad_alloc@std@@@8
	DD	imagerel ??_R3bad_alloc@std@@8
	DD	imagerel ??_R4bad_alloc@std@@6B@
rdata$r	ENDS
;	COMDAT ??_R2bad_alloc@std@@8
rdata$r	SEGMENT
??_R2bad_alloc@std@@8 DD imagerel ??_R1A@?0A@EA@bad_alloc@std@@8 ; std::bad_alloc::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@exception@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3bad_alloc@std@@8
rdata$r	SEGMENT
??_R3bad_alloc@std@@8 DD 00H				; std::bad_alloc::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2bad_alloc@std@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@bad_alloc@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@bad_alloc@std@@8 DD imagerel ??_R0?AVbad_alloc@std@@@8 ; std::bad_alloc::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3bad_alloc@std@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@bad_array_new_length@std@@8 DD imagerel ??_R0?AVbad_array_new_length@std@@@8 ; std::bad_array_new_length::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	02H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3bad_array_new_length@std@@8
rdata$r	ENDS
;	COMDAT ??_R2bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R2bad_array_new_length@std@@8 DD imagerel ??_R1A@?0A@EA@bad_array_new_length@std@@8 ; std::bad_array_new_length::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@bad_alloc@std@@8
	DD	imagerel ??_R1A@?0A@EA@exception@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R3bad_array_new_length@std@@8 DD 00H			; std::bad_array_new_length::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	03H
	DD	imagerel ??_R2bad_array_new_length@std@@8
rdata$r	ENDS
;	COMDAT ??_R4bad_array_new_length@std@@6B@
rdata$r	SEGMENT
??_R4bad_array_new_length@std@@6B@ DD 01H		; std::bad_array_new_length::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVbad_array_new_length@std@@@8
	DD	imagerel ??_R3bad_array_new_length@std@@8
	DD	imagerel ??_R4bad_array_new_length@std@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@exception@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@exception@std@@8 DD imagerel ??_R0?AVexception@std@@@8 ; std::exception::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3exception@std@@8
rdata$r	ENDS
;	COMDAT ??_R2exception@std@@8
rdata$r	SEGMENT
??_R2exception@std@@8 DD imagerel ??_R1A@?0A@EA@exception@std@@8 ; std::exception::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3exception@std@@8
rdata$r	SEGMENT
??_R3exception@std@@8 DD 00H				; std::exception::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2exception@std@@8
rdata$r	ENDS
;	COMDAT ??_R4exception@std@@6B@
rdata$r	SEGMENT
??_R4exception@std@@6B@ DD 01H				; std::exception::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVexception@std@@@8
	DD	imagerel ??_R3exception@std@@8
	DD	imagerel ??_R4exception@std@@6B@
rdata$r	ENDS
;	COMDAT ??_C@_0BA@FOIKENOD@vector?5too?5long@
CONST	SEGMENT
??_C@_0BA@FOIKENOD@vector?5too?5long@ DB 'vector too long', 00H ; `string'
CONST	ENDS
;	COMDAT ??_C@_0BN@FHEEOEA@mu2?3?3stWOPSCommand?3?3getValue@
CONST	SEGMENT
??_C@_0BN@FHEEOEA@mu2?3?3stWOPSCommand?3?3getValue@ DB 'mu2::stWOPSComman'
	DB	'd::getValue', 00H				; `string'
CONST	ENDS
;	COMDAT ??_C@_0BK@FMPHEHPK@arryIndex?5?$DM?5values?4size?$CI?$CJ@
CONST	SEGMENT
??_C@_0BK@FMPHEHPK@arryIndex?5?$DM?5values?4size?$CI?$CJ@ DB 'arryIndex <'
	DB	' values.size()', 00H			; `string'
CONST	ENDS
;	COMDAT ??_C@_0EB@JGAFMFJE@F?3?2Release_Branch?2Server?2Develo@
CONST	SEGMENT
??_C@_0EB@JGAFMFJE@F?3?2Release_Branch?2Server?2Develo@ DB 'F:\Release_Br'
	DB	'anch\Server\Development\Mu2Common\Protocol\Common.h', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7ItemMakingInfo@mu2@@6B@
CONST	SEGMENT
??_7ItemMakingInfo@mu2@@6B@ DQ FLAT:??_R4ItemMakingInfo@mu2@@6B@ ; mu2::ItemMakingInfo::`vftable'
	DQ	FLAT:?PackUnpack@ItemMakingInfo@mu2@@UEAA_NAEAVPacketStream@2@@Z
	DQ	FLAT:?Reset@ItemMakingInfo@mu2@@UEAAXXZ
	DQ	FLAT:??_EItemMakingInfo@mu2@@UEAAPEAXI@Z
CONST	ENDS
;	COMDAT ??_C@_11LOCGONAA@@
CONST	SEGMENT
??_C@_11LOCGONAA@@ DB 00H, 00H				; `string'
CONST	ENDS
;	COMDAT ??_C@_03COAJHJPB@buf@
CONST	SEGMENT
??_C@_03COAJHJPB@buf@ DB 'buf', 00H			; `string'
CONST	ENDS
;	COMDAT ??_C@_0BI@KGAODAAL@mu2?3?3PacketStream?3?3Read@
CONST	SEGMENT
??_C@_0BI@KGAODAAL@mu2?3?3PacketStream?3?3Read@ DB 'mu2::PacketStream::Re'
	DB	'ad', 00H					; `string'
CONST	ENDS
;	COMDAT ??_C@_05IOMEMJEC@count@
CONST	SEGMENT
??_C@_05IOMEMJEC@count@ DB 'count', 00H			; `string'
CONST	ENDS
;	COMDAT ??_C@_0BJ@NEGBBJOE@mu2?3?3PacketStream?3?3Write@
CONST	SEGMENT
??_C@_0BJ@NEGBBJOE@mu2?3?3PacketStream?3?3Write@ DB 'mu2::PacketStream::W'
	DB	'rite', 00H					; `string'
CONST	ENDS
;	COMDAT ??_C@_0BB@HAIDOJLN@checkingBuffer?$CI?$CJ@
CONST	SEGMENT
??_C@_0BB@HAIDOJLN@checkingBuffer?$CI?$CJ@ DB 'checkingBuffer()', 00H ; `string'
CONST	ENDS
;	COMDAT ??_C@_0EJ@IFEHPKPB@F?3?2Release_Branch?2Server?2Develo@
CONST	SEGMENT
??_C@_0EJ@IFEHPKPB@F?3?2Release_Branch?2Server?2Develo@ DB 'F:\Release_Br'
	DB	'anch\Server\Development\Framework\Net\Common\PacketStream.h', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7ISerializer@mu2@@6B@
CONST	SEGMENT
??_7ISerializer@mu2@@6B@ DQ FLAT:??_R4ISerializer@mu2@@6B@ ; mu2::ISerializer::`vftable'
	DQ	FLAT:_purecall
	DQ	FLAT:_purecall
	DQ	FLAT:??_EISerializer@mu2@@UEAAPEAXI@Z
CONST	ENDS
;	COMDAT ??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
CONST	SEGMENT
??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@ DB 'g', 00H, 'a', 00H, 'm', 00H, 'e'
	DB	00H, 00H, 00H				; `string'
CONST	ENDS
;	COMDAT ??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
CONST	SEGMENT
??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@ DB '%'
	DB	's> ASSERT - %s, %s(%d)', 00H		; `string'
CONST	ENDS
;	COMDAT ??_C@_0BA@JFNIOLAK@string?5too?5long@
CONST	SEGMENT
??_C@_0BA@JFNIOLAK@string?5too?5long@ DB 'string too long', 00H ; `string'
CONST	ENDS
;	COMDAT _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 DD 010H
	DD	imagerel ??_R0?AVbad_alloc@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0bad_alloc@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVbad_alloc@std@@@8
data$r	SEGMENT
??_R0?AVbad_alloc@std@@@8 DQ FLAT:??_7type_info@@6B@	; std::bad_alloc `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVbad_alloc@std@@', 00H
data$r	ENDS
;	COMDAT _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 DD 00H
	DD	imagerel ??_R0?AVbad_array_new_length@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0bad_array_new_length@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVbad_array_new_length@std@@@8
data$r	SEGMENT
??_R0?AVbad_array_new_length@std@@@8 DQ FLAT:??_7type_info@@6B@ ; std::bad_array_new_length `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVbad_array_new_length@std@@', 00H
data$r	ENDS
;	COMDAT _CTA3?AVbad_array_new_length@std@@
xdata$x	SEGMENT
_CTA3?AVbad_array_new_length@std@@ DD 03H
	DD	imagerel _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
	DD	imagerel _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
	DD	imagerel _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
xdata$x	ENDS
;	COMDAT _TI3?AVbad_array_new_length@std@@
xdata$x	SEGMENT
_TI3?AVbad_array_new_length@std@@ DD 00H
	DD	imagerel ??1bad_array_new_length@std@@UEAA@XZ
	DD	00H
	DD	imagerel _CTA3?AVbad_array_new_length@std@@
xdata$x	ENDS
;	COMDAT _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 DD 00H
	DD	imagerel ??_R0?AVexception@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0exception@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVexception@std@@@8
data$r	SEGMENT
??_R0?AVexception@std@@@8 DQ FLAT:??_7type_info@@6B@	; std::exception `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVexception@std@@', 00H
data$r	ENDS
;	COMDAT ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@
CONST	SEGMENT
??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ DB 'bad array new length', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7bad_array_new_length@std@@6B@
CONST	SEGMENT
??_7bad_array_new_length@std@@6B@ DQ FLAT:??_R4bad_array_new_length@std@@6B@ ; std::bad_array_new_length::`vftable'
	DQ	FLAT:??_Ebad_array_new_length@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT ??_7bad_alloc@std@@6B@
CONST	SEGMENT
??_7bad_alloc@std@@6B@ DQ FLAT:??_R4bad_alloc@std@@6B@	; std::bad_alloc::`vftable'
	DQ	FLAT:??_Ebad_alloc@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT ??_C@_0BC@EOODALEL@Unknown?5exception@
CONST	SEGMENT
??_C@_0BC@EOODALEL@Unknown?5exception@ DB 'Unknown exception', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7exception@std@@6B@
CONST	SEGMENT
??_7exception@std@@6B@ DQ FLAT:??_R4exception@std@@6B@	; std::exception::`vftable'
	DQ	FLAT:??_Eexception@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Emplace_back@UItemMakingInfo@mu2@@@?$_Uninitialized_backout_al@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@QEAAX$$QEAUItemMakingInfo@mu2@@@Z DD 010e01H
	DD	0820eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$_Uninitialized_backout_al@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@QEAA@XZ DD 010901H
	DD	0a209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???$_Uninitialized_move@PEAUItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@YAPEAUItemMakingInfo@mu2@@QEAU12@0PEAU12@AEAV?$allocator@UItemMakingInfo@mu2@@@0@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$_Uninitialized_move@PEAUItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@YAPEAUItemMakingInfo@mu2@@QEAU12@0PEAU12@AEAV?$allocator@UItemMakingInfo@mu2@@@0@@Z DB 06H
	DB	00H
	DB	00H
	DB	0daH
	DB	02H
	DB	0b2H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$_Uninitialized_move@PEAUItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@YAPEAUItemMakingInfo@mu2@@QEAU12@0PEAU12@AEAV?$allocator@UItemMakingInfo@mu2@@@0@@Z DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???$_Uninitialized_move@PEAUItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@YAPEAUItemMakingInfo@mu2@@QEAU12@0PEAU12@AEAV?$allocator@UItemMakingInfo@mu2@@@0@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$_Uninitialized_move@PEAUItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@YAPEAUItemMakingInfo@mu2@@QEAU12@0PEAU12@AEAV?$allocator@UItemMakingInfo@mu2@@@0@@Z DB 028H
	DD	imagerel $stateUnwindMap$??$_Uninitialized_move@PEAUItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@YAPEAUItemMakingInfo@mu2@@QEAU12@0PEAU12@AEAV?$allocator@UItemMakingInfo@mu2@@@0@@Z
	DD	imagerel $ip2state$??$_Uninitialized_move@PEAUItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@YAPEAUItemMakingInfo@mu2@@QEAU12@0PEAU12@AEAV?$allocator@UItemMakingInfo@mu2@@@0@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Uninitialized_move@PEAUItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@YAPEAUItemMakingInfo@mu2@@QEAU12@0PEAU12@AEAV?$allocator@UItemMakingInfo@mu2@@@0@@Z DD 011811H
	DD	0e218H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$_Uninitialized_move@PEAUItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@YAPEAUItemMakingInfo@mu2@@QEAU12@0PEAU12@AEAV?$allocator@UItemMakingInfo@mu2@@@0@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??1_Reallocation_guard@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@QEAA@XZ DB 06H
	DB	00H
	DB	00H
	DB	'i', 03H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??1_Reallocation_guard@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@QEAA@XZ DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??1_Reallocation_guard@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@QEAA@XZ DB 068H
	DD	imagerel $stateUnwindMap$??1_Reallocation_guard@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@QEAA@XZ
	DD	imagerel $ip2state$??1_Reallocation_guard@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1_Reallocation_guard@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@QEAA@XZ DD 010919H
	DD	0e209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??1_Reallocation_guard@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???$_Emplace_reallocate@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAPEAUItemMakingInfo@mu2@@QEAU23@AEBU23@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$_Emplace_reallocate@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAPEAUItemMakingInfo@mu2@@QEAU23@AEBU23@@Z DB 06H
	DB	00H
	DB	00H
	DB	085H, 08H
	DB	02H
	DB	0b1H, 06H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$_Emplace_reallocate@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAPEAUItemMakingInfo@mu2@@QEAU23@AEBU23@@Z DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???$_Emplace_reallocate@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAPEAUItemMakingInfo@mu2@@QEAU23@AEBU23@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$_Emplace_reallocate@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAPEAUItemMakingInfo@mu2@@QEAU23@AEBU23@@Z DB 028H
	DD	imagerel $stateUnwindMap$??$_Emplace_reallocate@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAPEAUItemMakingInfo@mu2@@QEAU23@AEBU23@@Z
	DD	imagerel $ip2state$??$_Emplace_reallocate@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAPEAUItemMakingInfo@mu2@@QEAU23@AEBU23@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Emplace_reallocate@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAPEAUItemMakingInfo@mu2@@QEAU23@AEBU23@@Z DD 021611H
	DD	02b0116H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$_Emplace_reallocate@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAPEAUItemMakingInfo@mu2@@QEAU23@AEBU23@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Emplace_back_with_unused_capacity@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAAEAUItemMakingInfo@mu2@@AEBU23@@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Emplace_one_at_back@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAAEAUItemMakingInfo@mu2@@AEBU23@@Z DD 010e01H
	DD	0820eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$Val@I@stWOPSCommand@mu2@@QEBAIH@Z DD 010d01H
	DD	0820dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1WOPSGoldenTimeGiftEventImpl@mu2@@QEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DW	017H
	DW	013cH
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0WOPSGoldenTimeGiftEventImpl@mu2@@QEAA@XZ DD 021c19H
	DD	07006f20dH
	DD	imagerel __GSHandlerCheck
	DD	070H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Xlength@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@CAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?_Tidy@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAXXZ DB 06H
	DB	00H
	DB	00H
	DB	'm', 04H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?_Tidy@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAXXZ DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?_Tidy@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAXXZ DB 068H
	DD	imagerel $stateUnwindMap$?_Tidy@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAXXZ
	DD	imagerel $ip2state$?_Tidy@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Tidy@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAXXZ DD 020c19H
	DD	015010cH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?_Tidy@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?_Change_array@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAXQEAUItemMakingInfo@mu2@@_K1@Z DB 06H
	DB	00H
	DB	00H
	DB	0a9H, 04H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?_Change_array@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAXQEAUItemMakingInfo@mu2@@_K1@Z DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?_Change_array@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAXQEAUItemMakingInfo@mu2@@_K1@Z DB 068H
	DD	imagerel $stateUnwindMap$?_Change_array@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAXQEAUItemMakingInfo@mu2@@_K1@Z
	DD	imagerel $ip2state$?_Change_array@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAXQEAUItemMakingInfo@mu2@@_K1@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Change_array@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAXQEAUItemMakingInfo@mu2@@_K1@Z DD 021b19H
	DD	015011bH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?_Change_array@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAXQEAUItemMakingInfo@mu2@@_K1@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Calculate_growth@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEBA_K_K@Z DD 021101H
	DD	0110111H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?allocate@?$allocator@UItemMakingInfo@mu2@@@std@@QEAAPEAUItemMakingInfo@mu2@@_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GWOPSCommandGoldenTimeGift@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$unique_ptr@UWOPSGoldenTimeGiftEventImpl@mu2@@U?$default_delete@UWOPSGoldenTimeGiftEventImpl@mu2@@@std@@@std@@QEAA@XZ DD 010901H
	DD	0a209H
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DW	01bH
	DW	06e5H
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0??parse@WOPSCommandGoldenTimeGift@mu2@@MEAA_NAEBUstWOPSCommand@2@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?parse@WOPSCommandGoldenTimeGift@mu2@@MEAA_NAEBUstWOPSCommand@2@@Z DB 06H
	DB	00H
	DB	00H
	DB	0dH, 06H
	DB	02H
	DB	085H, 02H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?parse@WOPSCommandGoldenTimeGift@mu2@@MEAA_NAEBUstWOPSCommand@2@@Z DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0??parse@WOPSCommandGoldenTimeGift@mu2@@MEAA_NAEBUstWOPSCommand@2@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?parse@WOPSCommandGoldenTimeGift@mu2@@MEAA_NAEBUstWOPSCommand@2@@Z DB 028H
	DD	imagerel $stateUnwindMap$?parse@WOPSCommandGoldenTimeGift@mu2@@MEAA_NAEBUstWOPSCommand@2@@Z
	DD	imagerel $ip2state$?parse@WOPSCommandGoldenTimeGift@mu2@@MEAA_NAEBUstWOPSCommand@2@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?parse@WOPSCommandGoldenTimeGift@mu2@@MEAA_NAEBUstWOPSCommand@2@@Z DD 022319H
	DD	0350111H
	DD	imagerel __GSHandlerCheck_EH4
	DD	imagerel $cppxdata$?parse@WOPSCommandGoldenTimeGift@mu2@@MEAA_NAEBUstWOPSCommand@2@@Z
	DD	019aH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1WOPSCommandGoldenTimeGift@mu2@@UEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$1@?0???0WOPSCommandGoldenTimeGift@mu2@@QEAA@XZ@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???0WOPSCommandGoldenTimeGift@mu2@@QEAA@XZ@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??0WOPSCommandGoldenTimeGift@mu2@@QEAA@XZ DB 0aH
	DB	00H
	DB	00H
	DB	','
	DB	02H
	DB	'<'
	DB	04H
	DB	'X'
	DB	02H
	DB	'x'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??0WOPSCommandGoldenTimeGift@mu2@@QEAA@XZ DB 04H
	DB	0eH
	DD	imagerel ?dtor$0@?0???0WOPSCommandGoldenTimeGift@mu2@@QEAA@XZ@4HA
	DB	02eH
	DD	imagerel ?dtor$1@?0???0WOPSCommandGoldenTimeGift@mu2@@QEAA@XZ@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??0WOPSCommandGoldenTimeGift@mu2@@QEAA@XZ DB 028H
	DD	imagerel $stateUnwindMap$??0WOPSCommandGoldenTimeGift@mu2@@QEAA@XZ
	DD	imagerel $ip2state$??0WOPSCommandGoldenTimeGift@mu2@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0WOPSCommandGoldenTimeGift@mu2@@QEAA@XZ DD 010911H
	DD	0c209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??0WOPSCommandGoldenTimeGift@mu2@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z DD 010901H
	DD	0a209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Construct@$01PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z DD 031701H
	DD	01e0117H
	DD	07010H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Construct@$00PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z DD 031701H
	DD	0200117H
	DD	07010H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?ToString@WOPSCommand@mu2@@UEBA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ DD 010e01H
	DD	0620eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?getValue@stWOPSCommand@mu2@@AEBAAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@H@Z DD 021001H
	DD	0130110H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GItemMakingInfo@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?PackUnpack@ItemMakingInfo@mu2@@UEAA_NAEAVPacketStream@2@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GISerializer@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$swapEndian@I@PacketStream@mu2@@AEAAIAEBI@Z DD 010e01H
	DD	0220eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?checkingBuffer@PacketStream@mu2@@AEAA_NXZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$rw@I@PacketStream@mu2@@QEAA_NAEAI@Z DD 010e01H
	DD	0620eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Read@PacketStream@mu2@@QEAAIPEAEI@Z DD 021601H
	DD	0110116H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Write@PacketStream@mu2@@QEAAIPEBEI@Z DD 021601H
	DD	0110116H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DB 06H
	DB	00H
	DB	00H
	DB	089H, 02H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DB 068H
	DD	imagerel $stateUnwindMap$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
	DD	imagerel $ip2state$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DD 010919H
	DD	0e209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ DD 020c01H
	DD	011010cH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Take_contents@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXAEAV12@@Z DD 021101H
	DD	0130111H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV01@$$QEAV01@@Z DD 010e01H
	DD	0c20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DD 020a01H
	DD	07006120aH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z DB 06H
	DB	00H
	DB	00H
	DB	0b4H
	DB	02H
	DB	'b'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z DB 028H
	DD	imagerel $stateUnwindMap$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z
	DD	imagerel $ip2state$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z DD 020f11H
	DD	0700b920fH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z DB 06H
	DB	00H
	DB	00H
	DB	0eeH
	DB	02H
	DB	']', 02H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z DB 028H
	DD	imagerel $stateUnwindMap$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z
	DD	imagerel $ip2state$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z DD 021211H
	DD	0700bf212H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ DB 02H
	DB	00H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ DB 060H
	DD	imagerel $ip2state$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ DD 020a19H
	DD	07006720aH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEBAPEB_WXZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Xlen_string@std@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Throw_bad_array_new_length@std@@YAXXZ DD 010401H
	DD	08204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1bad_array_new_length@std@@UEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_array_new_length@std@@QEAA@XZ DD 010601H
	DD	07006H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z DD 020f01H
	DD	0700b320fH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gexception@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?what@exception@std@@UEBAPEBDXZ DD 010901H
	DD	02209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0exception@std@@QEAA@AEBV01@@Z DD 020f01H
	DD	0700b320fH
xdata	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??$_Emplace_back@UItemMakingInfo@mu2@@@?$_Uninitialized_backout_al@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@QEAAX$$QEAUItemMakingInfo@mu2@@@Z
_TEXT	SEGMENT
$T1 = 0
__that$ = 8
_Ptr$ = 16
$T2 = 24
$T3 = 32
$T4 = 40
$T5 = 48
__formal$ = 56
this$ = 80
<_Vals_0>$ = 88
??$_Emplace_back@UItemMakingInfo@mu2@@@?$_Uninitialized_backout_al@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@QEAAX$$QEAUItemMakingInfo@mu2@@@Z PROC ; std::_Uninitialized_backout_al<std::allocator<mu2::ItemMakingInfo> >::_Emplace_back<mu2::ItemMakingInfo>, COMDAT

; 1843 :     _CONSTEXPR20 void _Emplace_back(_Types&&... _Vals) { // construct a new element at *_Last and increment

$LN35:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 48	 sub	 rsp, 72			; 00000048H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  0000e	48 8b 44 24 58	 mov	 rax, QWORD PTR <_Vals_0>$[rsp]
  00013	48 89 44 24 28	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1844 :         allocator_traits<_Alloc>::construct(_Al, _STD _Unfancy(_Last), _STD forward<_Types>(_Vals)...);

  00018	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00021	48 89 44 24 10	 mov	 QWORD PTR _Ptr$[rsp], rax

; 69   :     return _Ptr;

  00026	48 8b 44 24 10	 mov	 rax, QWORD PTR _Ptr$[rsp]
  0002b	48 89 44 24 18	 mov	 QWORD PTR $T2[rsp], rax

; 1844 :         allocator_traits<_Alloc>::construct(_Al, _STD _Unfancy(_Last), _STD forward<_Types>(_Vals)...);

  00030	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00035	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00039	48 89 44 24 38	 mov	 QWORD PTR __formal$[rsp], rax
  0003e	48 8b 44 24 18	 mov	 rax, QWORD PTR $T2[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  00043	48 89 44 24 20	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 732  :         ::new (const_cast<void*>(static_cast<const volatile void*>(_Ptr))) _Objty(_STD forward<_Types>(_Args)...);

  00048	48 8b 44 24 20	 mov	 rax, QWORD PTR $T3[rsp]
  0004d	48 89 04 24	 mov	 QWORD PTR $T1[rsp], rax

; 1844 :         allocator_traits<_Alloc>::construct(_Al, _STD _Unfancy(_Last), _STD forward<_Types>(_Vals)...);

  00051	48 8b 44 24 28	 mov	 rax, QWORD PTR $T4[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00056	48 89 44 24 30	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 732  :         ::new (const_cast<void*>(static_cast<const volatile void*>(_Ptr))) _Objty(_STD forward<_Types>(_Args)...);

  0005b	48 8b 44 24 30	 mov	 rax, QWORD PTR $T5[rsp]
  00060	48 89 44 24 08	 mov	 QWORD PTR __that$[rsp], rax
  00065	48 8b 04 24	 mov	 rax, QWORD PTR $T1[rsp]
  00069	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  00070	48 89 08	 mov	 QWORD PTR [rax], rcx
  00073	48 8b 04 24	 mov	 rax, QWORD PTR $T1[rsp]
  00077	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ItemMakingInfo@mu2@@6B@
  0007e	48 89 08	 mov	 QWORD PTR [rax], rcx
  00081	48 8b 04 24	 mov	 rax, QWORD PTR $T1[rsp]
  00085	48 8b 4c 24 08	 mov	 rcx, QWORD PTR __that$[rsp]
  0008a	8b 49 08	 mov	 ecx, DWORD PTR [rcx+8]
  0008d	89 48 08	 mov	 DWORD PTR [rax+8], ecx
  00090	48 8b 04 24	 mov	 rax, QWORD PTR $T1[rsp]
  00094	48 8b 4c 24 08	 mov	 rcx, QWORD PTR __that$[rsp]
  00099	8b 49 0c	 mov	 ecx, DWORD PTR [rcx+12]
  0009c	89 48 0c	 mov	 DWORD PTR [rax+12], ecx

; 1845 :         ++_Last;

  0009f	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  000a4	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  000a8	48 83 c0 10	 add	 rax, 16
  000ac	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  000b1	48 89 41 08	 mov	 QWORD PTR [rcx+8], rax

; 1846 :     }

  000b5	48 83 c4 48	 add	 rsp, 72			; 00000048H
  000b9	c3		 ret	 0
??$_Emplace_back@UItemMakingInfo@mu2@@@?$_Uninitialized_backout_al@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@QEAAX$$QEAUItemMakingInfo@mu2@@@Z ENDP ; std::_Uninitialized_backout_al<std::allocator<mu2::ItemMakingInfo> >::_Emplace_back<mu2::ItemMakingInfo>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??1?$_Uninitialized_backout_al@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@QEAA@XZ
_TEXT	SEGMENT
_First$ = 32
_Ptr$ = 40
_Last$ = 48
$T1 = 56
_Al$ = 64
this$ = 96
??1?$_Uninitialized_backout_al@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@QEAA@XZ PROC ; std::_Uninitialized_backout_al<std::allocator<mu2::ItemMakingInfo> >::~_Uninitialized_backout_al<std::allocator<mu2::ItemMakingInfo> >, COMDAT

; 1838 :     _CONSTEXPR20 ~_Uninitialized_backout_al() {

$LN19:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 1839 :         _STD _Destroy_range(_First, _Last, _Al);

  00009	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00012	48 89 44 24 40	 mov	 QWORD PTR _Al$[rsp], rax
  00017	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  0001c	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00020	48 89 44 24 30	 mov	 QWORD PTR _Last$[rsp], rax
  00025	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  0002a	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0002d	48 89 44 24 20	 mov	 QWORD PTR _First$[rsp], rax

; 1102 :         for (; _First != _Last; ++_First) {

  00032	eb 0e		 jmp	 SHORT $LN7@Uninitiali
$LN5@Uninitiali:
  00034	48 8b 44 24 20	 mov	 rax, QWORD PTR _First$[rsp]
  00039	48 83 c0 10	 add	 rax, 16
  0003d	48 89 44 24 20	 mov	 QWORD PTR _First$[rsp], rax
$LN7@Uninitiali:
  00042	48 8b 44 24 30	 mov	 rax, QWORD PTR _Last$[rsp]
  00047	48 39 44 24 20	 cmp	 QWORD PTR _First$[rsp], rax
  0004c	74 29		 je	 SHORT $LN6@Uninitiali

; 69   :     return _Ptr;

  0004e	48 8b 44 24 20	 mov	 rax, QWORD PTR _First$[rsp]
  00053	48 89 44 24 38	 mov	 QWORD PTR $T1[rsp], rax

; 1103 :             allocator_traits<_Alloc>::destroy(_Al, _STD _Unfancy(_First));

  00058	48 8b 44 24 38	 mov	 rax, QWORD PTR $T1[rsp]
  0005d	48 89 44 24 28	 mov	 QWORD PTR _Ptr$[rsp], rax

; 741  :         _Ptr->~_Uty();

  00062	48 8b 44 24 28	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00067	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0006a	33 d2		 xor	 edx, edx
  0006c	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  00071	ff 50 10	 call	 QWORD PTR [rax+16]
  00074	90		 npad	 1

; 1104 :         }

  00075	eb bd		 jmp	 SHORT $LN5@Uninitiali
$LN6@Uninitiali:

; 1840 :     }

  00077	48 83 c4 58	 add	 rsp, 88			; 00000058H
  0007b	c3		 ret	 0
??1?$_Uninitialized_backout_al@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@QEAA@XZ ENDP ; std::_Uninitialized_backout_al<std::allocator<mu2::ItemMakingInfo> >::~_Uninitialized_backout_al<std::allocator<mu2::ItemMakingInfo> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??$_Uninitialized_move@PEAUItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@YAPEAUItemMakingInfo@mu2@@QEAU12@0PEAU12@AEAV?$allocator@UItemMakingInfo@mu2@@@0@@Z
_TEXT	SEGMENT
_UFirst$ = 32
_Backout$ = 40
$T1 = 64
$T2 = 72
_ULast$ = 80
$T3 = 88
$T4 = 96
$T5 = 104
_First$ = 128
_Last$ = 136
_Dest$ = 144
_Al$ = 152
??$_Uninitialized_move@PEAUItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@YAPEAUItemMakingInfo@mu2@@QEAU12@0PEAU12@AEAV?$allocator@UItemMakingInfo@mu2@@@0@@Z PROC ; std::_Uninitialized_move<mu2::ItemMakingInfo *,std::allocator<mu2::ItemMakingInfo> >, COMDAT

; 1977 :     const _InIt _First, const _InIt _Last, _Alloc_ptr_t<_Alloc> _Dest, _Alloc& _Al) {

$LN80:
  00000	4c 89 4c 24 20	 mov	 QWORD PTR [rsp+32], r9
  00005	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	48 83 ec 78	 sub	 rsp, 120		; 00000078H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 1382 :         return _It + 0;

  00018	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _First$[rsp]
  00020	48 89 44 24 40	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1984 :     auto _UFirst      = _STD _Get_unwrapped(_First);

  00025	48 8b 44 24 40	 mov	 rax, QWORD PTR $T1[rsp]
  0002a	48 89 44 24 20	 mov	 QWORD PTR _UFirst$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 1382 :         return _It + 0;

  0002f	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR _Last$[rsp]
  00037	48 89 44 24 48	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1985 :     const auto _ULast = _STD _Get_unwrapped(_Last);

  0003c	48 8b 44 24 48	 mov	 rax, QWORD PTR $T2[rsp]
  00041	48 89 44 24 50	 mov	 QWORD PTR _ULast$[rsp], rax

; 1833 :     _CONSTEXPR20 _Uninitialized_backout_al(pointer _Dest, _Alloc& _Al_) : _First(_Dest), _Last(_Dest), _Al(_Al_) {}

  00046	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR _Dest$[rsp]
  0004e	48 89 44 24 28	 mov	 QWORD PTR _Backout$[rsp], rax
  00053	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR _Dest$[rsp]
  0005b	48 89 44 24 30	 mov	 QWORD PTR _Backout$[rsp+8], rax
  00060	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR _Al$[rsp]
  00068	48 89 44 24 38	 mov	 QWORD PTR _Backout$[rsp+16], rax

; 1986 :     if constexpr (conjunction_v<bool_constant<_Iter_move_cat<decltype(_UFirst), _Ptrval>::_Bitcopy_constructible>,
; 1987 :                       _Uses_default_construct<_Alloc, _Ptrval, decltype(_STD move(*_UFirst))>>) {
; 1988 : #if _HAS_CXX20
; 1989 :         if (!_STD is_constant_evaluated())
; 1990 : #endif // _HAS_CXX20
; 1991 :         {
; 1992 :             _STD _Copy_memmove(_UFirst, _ULast, _STD _Unfancy(_Dest));
; 1993 :             return _Dest + (_ULast - _UFirst);
; 1994 :         }
; 1995 :     }
; 1996 : 
; 1997 :     _Uninitialized_backout_al<_Alloc> _Backout{_Dest, _Al};
; 1998 :     for (; _UFirst != _ULast; ++_UFirst) {

  0006d	eb 0e		 jmp	 SHORT $LN4@Uninitiali
$LN2@Uninitiali:
  0006f	48 8b 44 24 20	 mov	 rax, QWORD PTR _UFirst$[rsp]
  00074	48 83 c0 10	 add	 rax, 16
  00078	48 89 44 24 20	 mov	 QWORD PTR _UFirst$[rsp], rax
$LN4@Uninitiali:
  0007d	48 8b 44 24 50	 mov	 rax, QWORD PTR _ULast$[rsp]
  00082	48 39 44 24 20	 cmp	 QWORD PTR _UFirst$[rsp], rax
  00087	74 1f		 je	 SHORT $LN3@Uninitiali
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00089	48 8b 44 24 20	 mov	 rax, QWORD PTR _UFirst$[rsp]
  0008e	48 89 44 24 58	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1999 :         _Backout._Emplace_back(_STD move(*_UFirst));

  00093	48 8b 44 24 58	 mov	 rax, QWORD PTR $T3[rsp]
  00098	48 8b d0	 mov	 rdx, rax
  0009b	48 8d 4c 24 28	 lea	 rcx, QWORD PTR _Backout$[rsp]
  000a0	e8 00 00 00 00	 call	 ??$_Emplace_back@UItemMakingInfo@mu2@@@?$_Uninitialized_backout_al@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@QEAAX$$QEAUItemMakingInfo@mu2@@@Z ; std::_Uninitialized_backout_al<std::allocator<mu2::ItemMakingInfo> >::_Emplace_back<mu2::ItemMakingInfo>
  000a5	90		 npad	 1

; 2000 :     }

  000a6	eb c7		 jmp	 SHORT $LN2@Uninitiali
$LN3@Uninitiali:

; 1849 :         _First = _Last;

  000a8	48 8b 44 24 30	 mov	 rax, QWORD PTR _Backout$[rsp+8]
  000ad	48 89 44 24 28	 mov	 QWORD PTR _Backout$[rsp], rax

; 1850 :         return _Last;

  000b2	48 8b 44 24 30	 mov	 rax, QWORD PTR _Backout$[rsp+8]
  000b7	48 89 44 24 60	 mov	 QWORD PTR $T4[rsp], rax

; 2001 : 
; 2002 :     return _Backout._Release();

  000bc	48 8b 44 24 60	 mov	 rax, QWORD PTR $T4[rsp]
  000c1	48 89 44 24 68	 mov	 QWORD PTR $T5[rsp], rax
  000c6	48 8d 4c 24 28	 lea	 rcx, QWORD PTR _Backout$[rsp]
  000cb	e8 00 00 00 00	 call	 ??1?$_Uninitialized_backout_al@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@QEAA@XZ ; std::_Uninitialized_backout_al<std::allocator<mu2::ItemMakingInfo> >::~_Uninitialized_backout_al<std::allocator<mu2::ItemMakingInfo> >
  000d0	48 8b 44 24 68	 mov	 rax, QWORD PTR $T5[rsp]

; 2003 : }

  000d5	48 83 c4 78	 add	 rsp, 120		; 00000078H
  000d9	c3		 ret	 0
??$_Uninitialized_move@PEAUItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@YAPEAUItemMakingInfo@mu2@@QEAU12@0PEAU12@AEAV?$allocator@UItemMakingInfo@mu2@@@0@@Z ENDP ; std::_Uninitialized_move<mu2::ItemMakingInfo *,std::allocator<mu2::ItemMakingInfo> >
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
_UFirst$ = 32
_Backout$ = 40
$T1 = 64
$T2 = 72
_ULast$ = 80
$T3 = 88
$T4 = 96
$T5 = 104
_First$ = 128
_Last$ = 136
_Dest$ = 144
_Al$ = 152
?dtor$0@?0???$_Uninitialized_move@PEAUItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@YAPEAUItemMakingInfo@mu2@@QEAU12@0PEAU12@AEAV?$allocator@UItemMakingInfo@mu2@@@0@@Z@4HA PROC ; `std::_Uninitialized_move<mu2::ItemMakingInfo *,std::allocator<mu2::ItemMakingInfo> >'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8d 4d 28	 lea	 rcx, QWORD PTR _Backout$[rbp]
  0000d	e8 00 00 00 00	 call	 ??1?$_Uninitialized_backout_al@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@QEAA@XZ ; std::_Uninitialized_backout_al<std::allocator<mu2::ItemMakingInfo> >::~_Uninitialized_backout_al<std::allocator<mu2::ItemMakingInfo> >
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$0@?0???$_Uninitialized_move@PEAUItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@YAPEAUItemMakingInfo@mu2@@QEAU12@0PEAU12@AEAV?$allocator@UItemMakingInfo@mu2@@@0@@Z@4HA ENDP ; `std::_Uninitialized_move<mu2::ItemMakingInfo *,std::allocator<mu2::ItemMakingInfo> >'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ??1_Reallocation_guard@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@QEAA@XZ
_TEXT	SEGMENT
_First$ = 32
_Bytes$ = 40
_Ptr$ = 48
_Ptr$ = 56
_Last$ = 64
$T1 = 72
_Count$ = 80
_Ptr$ = 88
_Al$ = 96
this$ = 104
this$ = 128
??1_Reallocation_guard@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@QEAA@XZ PROC ; std::vector<mu2::ItemMakingInfo,std::allocator<mu2::ItemMakingInfo> >::_Reallocation_guard::~_Reallocation_guard, COMDAT

; 620  :         _CONSTEXPR20 ~_Reallocation_guard() noexcept {

$LN35:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 78	 sub	 rsp, 120		; 00000078H

; 621  :             if (_New_begin != nullptr) {

  00009	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00011	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00016	0f 84 e9 00 00
	00		 je	 $LN2@Reallocati

; 622  :                 _STD _Destroy_range(_Constructed_first, _Constructed_last, _Al);

  0001c	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00024	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00027	48 89 44 24 60	 mov	 QWORD PTR _Al$[rsp], rax
  0002c	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00034	48 8b 40 20	 mov	 rax, QWORD PTR [rax+32]
  00038	48 89 44 24 40	 mov	 QWORD PTR _Last$[rsp], rax
  0003d	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00045	48 8b 40 18	 mov	 rax, QWORD PTR [rax+24]
  00049	48 89 44 24 20	 mov	 QWORD PTR _First$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1102 :         for (; _First != _Last; ++_First) {

  0004e	eb 0e		 jmp	 SHORT $LN8@Reallocati
$LN6@Reallocati:
  00050	48 8b 44 24 20	 mov	 rax, QWORD PTR _First$[rsp]
  00055	48 83 c0 10	 add	 rax, 16
  00059	48 89 44 24 20	 mov	 QWORD PTR _First$[rsp], rax
$LN8@Reallocati:
  0005e	48 8b 44 24 40	 mov	 rax, QWORD PTR _Last$[rsp]
  00063	48 39 44 24 20	 cmp	 QWORD PTR _First$[rsp], rax
  00068	74 29		 je	 SHORT $LN7@Reallocati

; 69   :     return _Ptr;

  0006a	48 8b 44 24 20	 mov	 rax, QWORD PTR _First$[rsp]
  0006f	48 89 44 24 48	 mov	 QWORD PTR $T1[rsp], rax

; 1103 :             allocator_traits<_Alloc>::destroy(_Al, _STD _Unfancy(_First));

  00074	48 8b 44 24 48	 mov	 rax, QWORD PTR $T1[rsp]
  00079	48 89 44 24 30	 mov	 QWORD PTR _Ptr$[rsp], rax

; 741  :         _Ptr->~_Uty();

  0007e	48 8b 44 24 30	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00083	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00086	33 d2		 xor	 edx, edx
  00088	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  0008d	ff 50 10	 call	 QWORD PTR [rax+16]
  00090	90		 npad	 1

; 1104 :         }

  00091	eb bd		 jmp	 SHORT $LN6@Reallocati
$LN7@Reallocati:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 623  :                 _Al.deallocate(_New_begin, _New_capacity);

  00093	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0009b	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0009f	48 89 44 24 50	 mov	 QWORD PTR _Count$[rsp], rax
  000a4	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000ac	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  000b0	48 89 44 24 58	 mov	 QWORD PTR _Ptr$[rsp], rax
  000b5	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000bd	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000c0	48 89 44 24 68	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  000c5	48 6b 44 24 50
	10		 imul	 rax, QWORD PTR _Count$[rsp], 16
  000cb	48 89 44 24 28	 mov	 QWORD PTR _Bytes$[rsp], rax
  000d0	48 8b 44 24 58	 mov	 rax, QWORD PTR _Ptr$[rsp]
  000d5	48 89 44 24 38	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  000da	48 81 7c 24 28
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  000e3	72 10		 jb	 SHORT $LN26@Reallocati

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  000e5	48 8d 54 24 28	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  000ea	48 8d 4c 24 38	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  000ef	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  000f4	90		 npad	 1
$LN26@Reallocati:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  000f5	48 8b 54 24 28	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  000fa	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  000ff	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00104	90		 npad	 1
$LN2@Reallocati:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 625  :         }

  00105	48 83 c4 78	 add	 rsp, 120		; 00000078H
  00109	c3		 ret	 0
??1_Reallocation_guard@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@QEAA@XZ ENDP ; std::vector<mu2::ItemMakingInfo,std::allocator<mu2::ItemMakingInfo> >::_Reallocation_guard::~_Reallocation_guard
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ??$_Emplace_reallocate@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAPEAUItemMakingInfo@mu2@@QEAU23@AEBU23@@Z
_TEXT	SEGMENT
_Newvec$ = 32
_Al$ = 40
_Whereoff$ = 48
$T1 = 56
_Myfirst$ = 64
_Mylast$ = 72
_Newcapacity$ = 80
_My_data$ = 88
$T2 = 96
$T3 = 104
tv163 = 112
_Oldsize$ = 120
_Constructed_last$ = 128
__that$ = 136
_Constructed_first$ = 144
_Newsize$ = 152
$T4 = 160
$T5 = 168
$T6 = 176
$T7 = 184
$T8 = 192
$T9 = 200
$T10 = 208
$T11 = 216
$T12 = 224
$T13 = 232
$T14 = 240
$T15 = 248
$T16 = 256
$T17 = 264
_Guard$ = 272
$T18 = 312
_Unsigned_max$19 = 320
this$ = 352
_Whereptr$ = 360
<_Val_0>$ = 368
??$_Emplace_reallocate@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAPEAUItemMakingInfo@mu2@@QEAU23@AEBU23@@Z PROC ; std::vector<mu2::ItemMakingInfo,std::allocator<mu2::ItemMakingInfo> >::_Emplace_reallocate<mu2::ItemMakingInfo const &>, COMDAT

; 875  :     _CONSTEXPR20 pointer _Emplace_reallocate(const pointer _Whereptr, _Valty&&... _Val) {

$LN431:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 81 ec 58 01
	00 00		 sub	 rsp, 344		; 00000158H

; 2227 :         return _Mypair._Get_first();

  00016	48 8b 84 24 60
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  0001e	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2227 :         return _Mypair._Get_first();

  00026	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR $T4[rsp]
  0002e	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T5[rsp], rax

; 876  :         // reallocate and insert by perfectly forwarding _Val at _Whereptr
; 877  :         _Alty& _Al        = _Getal();

  00036	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T5[rsp]
  0003e	48 89 44 24 28	 mov	 QWORD PTR _Al$[rsp], rax

; 878  :         auto& _My_data    = _Mypair._Myval2;

  00043	48 8b 84 24 60
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0004b	48 89 44 24 58	 mov	 QWORD PTR _My_data$[rsp], rax

; 879  :         pointer& _Myfirst = _My_data._Myfirst;

  00050	48 8b 44 24 58	 mov	 rax, QWORD PTR _My_data$[rsp]
  00055	48 89 44 24 40	 mov	 QWORD PTR _Myfirst$[rsp], rax

; 880  :         pointer& _Mylast  = _My_data._Mylast;

  0005a	48 8b 44 24 58	 mov	 rax, QWORD PTR _My_data$[rsp]
  0005f	48 83 c0 08	 add	 rax, 8
  00063	48 89 44 24 48	 mov	 QWORD PTR _Mylast$[rsp], rax

; 881  : 
; 882  :         _STL_INTERNAL_CHECK(_Mylast == _My_data._Myend); // check that we have no unused capacity
; 883  : 
; 884  :         const auto _Whereoff = static_cast<size_type>(_Whereptr - _Myfirst);

  00068	48 8b 44 24 40	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  0006d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00070	48 8b 8c 24 68
	01 00 00	 mov	 rcx, QWORD PTR _Whereptr$[rsp]
  00078	48 2b c8	 sub	 rcx, rax
  0007b	48 8b c1	 mov	 rax, rcx
  0007e	48 c1 f8 04	 sar	 rax, 4
  00082	48 89 44 24 30	 mov	 QWORD PTR _Whereoff$[rsp], rax

; 885  :         const auto _Oldsize  = static_cast<size_type>(_Mylast - _Myfirst);

  00087	48 8b 44 24 48	 mov	 rax, QWORD PTR _Mylast$[rsp]
  0008c	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Myfirst$[rsp]
  00091	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00094	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00097	48 2b c1	 sub	 rax, rcx
  0009a	48 c1 f8 04	 sar	 rax, 4
  0009e	48 89 44 24 78	 mov	 QWORD PTR _Oldsize$[rsp], rax

; 2231 :         return _Mypair._Get_first();

  000a3	48 8b 84 24 60
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1543 :         return *this;

  000ab	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2231 :         return _Mypair._Get_first();

  000b3	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR $T6[rsp]
  000bb	48 89 84 24 38
	01 00 00	 mov	 QWORD PTR $T18[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 746  :         return static_cast<size_t>(-1) / sizeof(value_type);

  000c3	48 b8 ff ff ff
	ff ff ff ff 0f	 mov	 rax, 1152921504606846975 ; 0fffffffffffffffH
  000cd	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1919 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()), _Alty_traits::max_size(_Getal()));

  000d5	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T7[rsp]
  000dd	48 89 44 24 60	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 866  :         constexpr auto _Unsigned_max = static_cast<make_unsigned_t<_Ty>>(-1);

  000e2	48 c7 84 24 40
	01 00 00 ff ff
	ff ff		 mov	 QWORD PTR _Unsigned_max$19[rsp], -1

; 867  :         return static_cast<_Ty>(_Unsigned_max >> 1);

  000ee	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  000f8	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1919 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()), _Alty_traits::max_size(_Getal()));

  00100	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR $T8[rsp]
  00108	48 89 44 24 68	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 101  :     return _Right < _Left ? _Right : _Left;

  0010d	48 8b 44 24 68	 mov	 rax, QWORD PTR $T3[rsp]
  00112	48 39 44 24 60	 cmp	 QWORD PTR $T2[rsp], rax
  00117	73 0c		 jae	 SHORT $LN44@Emplace_re
  00119	48 8d 44 24 60	 lea	 rax, QWORD PTR $T2[rsp]
  0011e	48 89 44 24 70	 mov	 QWORD PTR tv163[rsp], rax
  00123	eb 0a		 jmp	 SHORT $LN45@Emplace_re
$LN44@Emplace_re:
  00125	48 8d 44 24 68	 lea	 rax, QWORD PTR $T3[rsp]
  0012a	48 89 44 24 70	 mov	 QWORD PTR tv163[rsp], rax
$LN45@Emplace_re:
  0012f	48 8b 44 24 70	 mov	 rax, QWORD PTR tv163[rsp]
  00134	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax
  0013c	48 8b 84 24 c8
	00 00 00	 mov	 rax, QWORD PTR $T9[rsp]
  00144	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1919 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()), _Alty_traits::max_size(_Getal()));

  0014c	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  00154	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00157	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax

; 886  : 
; 887  :         if (_Oldsize == max_size()) {

  0015f	48 8b 84 24 d8
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  00167	48 39 44 24 78	 cmp	 QWORD PTR _Oldsize$[rsp], rax
  0016c	75 06		 jne	 SHORT $LN2@Emplace_re

; 888  :             _Xlength();

  0016e	e8 00 00 00 00	 call	 ?_Xlength@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@CAXXZ ; std::vector<mu2::ItemMakingInfo,std::allocator<mu2::ItemMakingInfo> >::_Xlength
  00173	90		 npad	 1
$LN2@Emplace_re:

; 889  :         }
; 890  : 
; 891  :         const size_type _Newsize = _Oldsize + 1;

  00174	48 8b 44 24 78	 mov	 rax, QWORD PTR _Oldsize$[rsp]
  00179	48 ff c0	 inc	 rax
  0017c	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR _Newsize$[rsp], rax

; 892  :         size_type _Newcapacity   = _Calculate_growth(_Newsize);

  00184	48 8b 94 24 98
	00 00 00	 mov	 rdx, QWORD PTR _Newsize$[rsp]
  0018c	48 8b 8c 24 60
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00194	e8 00 00 00 00	 call	 ?_Calculate_growth@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEBA_K_K@Z ; std::vector<mu2::ItemMakingInfo,std::allocator<mu2::ItemMakingInfo> >::_Calculate_growth
  00199	48 89 44 24 50	 mov	 QWORD PTR _Newcapacity$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 2303 :         return _Al.allocate(_Count);

  0019e	48 8b 54 24 50	 mov	 rdx, QWORD PTR _Newcapacity$[rsp]
  001a3	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Al$[rsp]
  001a8	e8 00 00 00 00	 call	 ?allocate@?$allocator@UItemMakingInfo@mu2@@@std@@QEAAPEAUItemMakingInfo@mu2@@_K@Z ; std::allocator<mu2::ItemMakingInfo>::allocate
  001ad	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 894  :         const pointer _Newvec           = _STD _Allocate_at_least_helper(_Al, _Newcapacity);

  001b5	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
  001bd	48 89 44 24 20	 mov	 QWORD PTR _Newvec$[rsp], rax

; 895  :         const pointer _Constructed_last = _Newvec + _Whereoff + 1;

  001c2	48 6b 44 24 30
	10		 imul	 rax, QWORD PTR _Whereoff$[rsp], 16
  001c8	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Newvec$[rsp]
  001cd	48 8d 44 01 10	 lea	 rax, QWORD PTR [rcx+rax+16]
  001d2	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _Constructed_last$[rsp], rax

; 896  : 
; 897  :         _Reallocation_guard _Guard{_Al, _Newvec, _Newcapacity, _Constructed_last, _Constructed_last};

  001da	48 8b 44 24 28	 mov	 rax, QWORD PTR _Al$[rsp]
  001df	48 89 84 24 10
	01 00 00	 mov	 QWORD PTR _Guard$[rsp], rax
  001e7	48 8b 44 24 20	 mov	 rax, QWORD PTR _Newvec$[rsp]
  001ec	48 89 84 24 18
	01 00 00	 mov	 QWORD PTR _Guard$[rsp+8], rax
  001f4	48 8b 44 24 50	 mov	 rax, QWORD PTR _Newcapacity$[rsp]
  001f9	48 89 84 24 20
	01 00 00	 mov	 QWORD PTR _Guard$[rsp+16], rax
  00201	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _Constructed_last$[rsp]
  00209	48 89 84 24 28
	01 00 00	 mov	 QWORD PTR _Guard$[rsp+24], rax
  00211	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _Constructed_last$[rsp]
  00219	48 89 84 24 30
	01 00 00	 mov	 QWORD PTR _Guard$[rsp+32], rax

; 898  :         auto& _Constructed_first = _Guard._Constructed_first;

  00221	48 8d 84 24 28
	01 00 00	 lea	 rax, QWORD PTR _Guard$[rsp+24]
  00229	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR _Constructed_first$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00231	48 8b 84 24 70
	01 00 00	 mov	 rax, QWORD PTR <_Val_0>$[rsp]
  00239	48 89 84 24 f8
	00 00 00	 mov	 QWORD PTR $T15[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 900  :         _Alty_traits::construct(_Al, _STD _Unfancy(_Newvec + _Whereoff), _STD forward<_Valty>(_Val)...);

  00241	48 6b 44 24 30
	10		 imul	 rax, QWORD PTR _Whereoff$[rsp], 16
  00247	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Newvec$[rsp]
  0024c	48 03 c8	 add	 rcx, rax
  0024f	48 8b c1	 mov	 rax, rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  00252	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 900  :         _Alty_traits::construct(_Al, _STD _Unfancy(_Newvec + _Whereoff), _STD forward<_Valty>(_Val)...);

  0025a	48 8b 84 24 e8
	00 00 00	 mov	 rax, QWORD PTR $T13[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  00262	48 89 84 24 f0
	00 00 00	 mov	 QWORD PTR $T14[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 732  :         ::new (const_cast<void*>(static_cast<const volatile void*>(_Ptr))) _Objty(_STD forward<_Types>(_Args)...);

  0026a	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR $T14[rsp]
  00272	48 89 44 24 38	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 900  :         _Alty_traits::construct(_Al, _STD _Unfancy(_Newvec + _Whereoff), _STD forward<_Valty>(_Val)...);

  00277	48 8b 84 24 f8
	00 00 00	 mov	 rax, QWORD PTR $T15[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  0027f	48 89 84 24 00
	01 00 00	 mov	 QWORD PTR $T16[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 732  :         ::new (const_cast<void*>(static_cast<const volatile void*>(_Ptr))) _Objty(_STD forward<_Types>(_Args)...);

  00287	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR $T16[rsp]
  0028f	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR __that$[rsp], rax
  00297	48 8b 44 24 38	 mov	 rax, QWORD PTR $T1[rsp]
  0029c	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  002a3	48 89 08	 mov	 QWORD PTR [rax], rcx
  002a6	48 8b 44 24 38	 mov	 rax, QWORD PTR $T1[rsp]
  002ab	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ItemMakingInfo@mu2@@6B@
  002b2	48 89 08	 mov	 QWORD PTR [rax], rcx
  002b5	48 8b 44 24 38	 mov	 rax, QWORD PTR $T1[rsp]
  002ba	48 8b 8c 24 88
	00 00 00	 mov	 rcx, QWORD PTR __that$[rsp]
  002c2	8b 49 08	 mov	 ecx, DWORD PTR [rcx+8]
  002c5	89 48 08	 mov	 DWORD PTR [rax+8], ecx
  002c8	48 8b 44 24 38	 mov	 rax, QWORD PTR $T1[rsp]
  002cd	48 8b 8c 24 88
	00 00 00	 mov	 rcx, QWORD PTR __that$[rsp]
  002d5	8b 49 0c	 mov	 ecx, DWORD PTR [rcx+12]
  002d8	89 48 0c	 mov	 DWORD PTR [rax+12], ecx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 901  :         _Constructed_first = _Newvec + _Whereoff;

  002db	48 6b 44 24 30
	10		 imul	 rax, QWORD PTR _Whereoff$[rsp], 16
  002e1	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Newvec$[rsp]
  002e6	48 03 c8	 add	 rcx, rax
  002e9	48 8b c1	 mov	 rax, rcx
  002ec	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR _Constructed_first$[rsp]
  002f4	48 89 01	 mov	 QWORD PTR [rcx], rax

; 902  : 
; 903  :         if (_Whereptr == _Mylast) { // at back, provide strong guarantee

  002f7	48 8b 44 24 48	 mov	 rax, QWORD PTR _Mylast$[rsp]
  002fc	48 8b 00	 mov	 rax, QWORD PTR [rax]
  002ff	48 39 84 24 68
	01 00 00	 cmp	 QWORD PTR _Whereptr$[rsp], rax
  00307	75 22		 jne	 SHORT $LN3@Emplace_re

; 904  :             if constexpr (is_nothrow_move_constructible_v<_Ty> || !is_copy_constructible_v<_Ty>) {
; 905  :                 _STD _Uninitialized_move(_Myfirst, _Mylast, _Newvec, _Al);

  00309	4c 8b 4c 24 28	 mov	 r9, QWORD PTR _Al$[rsp]
  0030e	4c 8b 44 24 20	 mov	 r8, QWORD PTR _Newvec$[rsp]
  00313	48 8b 44 24 48	 mov	 rax, QWORD PTR _Mylast$[rsp]
  00318	48 8b 10	 mov	 rdx, QWORD PTR [rax]
  0031b	48 8b 44 24 40	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  00320	48 8b 08	 mov	 rcx, QWORD PTR [rax]
  00323	e8 00 00 00 00	 call	 ??$_Uninitialized_move@PEAUItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@YAPEAUItemMakingInfo@mu2@@QEAU12@0PEAU12@AEAV?$allocator@UItemMakingInfo@mu2@@@0@@Z ; std::_Uninitialized_move<mu2::ItemMakingInfo *,std::allocator<mu2::ItemMakingInfo> >
  00328	90		 npad	 1

; 906  :             } else {
; 907  :                 _STD _Uninitialized_copy(_Myfirst, _Mylast, _Newvec, _Al);
; 908  :             }
; 909  :         } else { // provide basic guarantee

  00329	eb 5d		 jmp	 SHORT $LN4@Emplace_re
$LN3@Emplace_re:

; 910  :             _STD _Uninitialized_move(_Myfirst, _Whereptr, _Newvec, _Al);

  0032b	4c 8b 4c 24 28	 mov	 r9, QWORD PTR _Al$[rsp]
  00330	4c 8b 44 24 20	 mov	 r8, QWORD PTR _Newvec$[rsp]
  00335	48 8b 94 24 68
	01 00 00	 mov	 rdx, QWORD PTR _Whereptr$[rsp]
  0033d	48 8b 44 24 40	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  00342	48 8b 08	 mov	 rcx, QWORD PTR [rax]
  00345	e8 00 00 00 00	 call	 ??$_Uninitialized_move@PEAUItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@YAPEAUItemMakingInfo@mu2@@QEAU12@0PEAU12@AEAV?$allocator@UItemMakingInfo@mu2@@@0@@Z ; std::_Uninitialized_move<mu2::ItemMakingInfo *,std::allocator<mu2::ItemMakingInfo> >

; 911  :             _Constructed_first = _Newvec;

  0034a	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR _Constructed_first$[rsp]
  00352	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Newvec$[rsp]
  00357	48 89 08	 mov	 QWORD PTR [rax], rcx

; 912  :             _STD _Uninitialized_move(_Whereptr, _Mylast, _Newvec + _Whereoff + 1, _Al);

  0035a	48 6b 44 24 30
	10		 imul	 rax, QWORD PTR _Whereoff$[rsp], 16
  00360	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Newvec$[rsp]
  00365	48 8d 44 01 10	 lea	 rax, QWORD PTR [rcx+rax+16]
  0036a	4c 8b 4c 24 28	 mov	 r9, QWORD PTR _Al$[rsp]
  0036f	4c 8b c0	 mov	 r8, rax
  00372	48 8b 44 24 48	 mov	 rax, QWORD PTR _Mylast$[rsp]
  00377	48 8b 10	 mov	 rdx, QWORD PTR [rax]
  0037a	48 8b 8c 24 68
	01 00 00	 mov	 rcx, QWORD PTR _Whereptr$[rsp]
  00382	e8 00 00 00 00	 call	 ??$_Uninitialized_move@PEAUItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@YAPEAUItemMakingInfo@mu2@@QEAU12@0PEAU12@AEAV?$allocator@UItemMakingInfo@mu2@@@0@@Z ; std::_Uninitialized_move<mu2::ItemMakingInfo *,std::allocator<mu2::ItemMakingInfo> >
  00387	90		 npad	 1
$LN4@Emplace_re:

; 913  :         }
; 914  : 
; 915  :         _Guard._New_begin = nullptr;

  00388	48 c7 84 24 18
	01 00 00 00 00
	00 00		 mov	 QWORD PTR _Guard$[rsp+8], 0

; 916  :         _Change_array(_Newvec, _Newsize, _Newcapacity);

  00394	4c 8b 4c 24 50	 mov	 r9, QWORD PTR _Newcapacity$[rsp]
  00399	4c 8b 84 24 98
	00 00 00	 mov	 r8, QWORD PTR _Newsize$[rsp]
  003a1	48 8b 54 24 20	 mov	 rdx, QWORD PTR _Newvec$[rsp]
  003a6	48 8b 8c 24 60
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  003ae	e8 00 00 00 00	 call	 ?_Change_array@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAXQEAUItemMakingInfo@mu2@@_K1@Z ; std::vector<mu2::ItemMakingInfo,std::allocator<mu2::ItemMakingInfo> >::_Change_array
  003b3	90		 npad	 1

; 917  :         return _Newvec + _Whereoff;

  003b4	48 6b 44 24 30
	10		 imul	 rax, QWORD PTR _Whereoff$[rsp], 16
  003ba	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Newvec$[rsp]
  003bf	48 03 c8	 add	 rcx, rax
  003c2	48 8b c1	 mov	 rax, rcx
  003c5	48 89 84 24 08
	01 00 00	 mov	 QWORD PTR $T17[rsp], rax
  003cd	48 8d 8c 24 10
	01 00 00	 lea	 rcx, QWORD PTR _Guard$[rsp]
  003d5	e8 00 00 00 00	 call	 ??1_Reallocation_guard@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@QEAA@XZ ; std::vector<mu2::ItemMakingInfo,std::allocator<mu2::ItemMakingInfo> >::_Reallocation_guard::~_Reallocation_guard
  003da	48 8b 84 24 08
	01 00 00	 mov	 rax, QWORD PTR $T17[rsp]

; 918  :     }

  003e2	48 81 c4 58 01
	00 00		 add	 rsp, 344		; 00000158H
  003e9	c3		 ret	 0
$LN430@Emplace_re:
??$_Emplace_reallocate@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAPEAUItemMakingInfo@mu2@@QEAU23@AEBU23@@Z ENDP ; std::vector<mu2::ItemMakingInfo,std::allocator<mu2::ItemMakingInfo> >::_Emplace_reallocate<mu2::ItemMakingInfo const &>
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
_Newvec$ = 32
_Al$ = 40
_Whereoff$ = 48
$T1 = 56
_Myfirst$ = 64
_Mylast$ = 72
_Newcapacity$ = 80
_My_data$ = 88
$T2 = 96
$T3 = 104
tv163 = 112
_Oldsize$ = 120
_Constructed_last$ = 128
__that$ = 136
_Constructed_first$ = 144
_Newsize$ = 152
$T4 = 160
$T5 = 168
$T6 = 176
$T7 = 184
$T8 = 192
$T9 = 200
$T10 = 208
$T11 = 216
$T12 = 224
$T13 = 232
$T14 = 240
$T15 = 248
$T16 = 256
$T17 = 264
_Guard$ = 272
$T18 = 312
_Unsigned_max$19 = 320
this$ = 352
_Whereptr$ = 360
<_Val_0>$ = 368
?dtor$0@?0???$_Emplace_reallocate@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAPEAUItemMakingInfo@mu2@@QEAU23@AEBU23@@Z@4HA PROC ; `std::vector<mu2::ItemMakingInfo,std::allocator<mu2::ItemMakingInfo> >::_Emplace_reallocate<mu2::ItemMakingInfo const &>'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8d 8d 10 01
	00 00		 lea	 rcx, QWORD PTR _Guard$[rbp]
  00010	e8 00 00 00 00	 call	 ??1_Reallocation_guard@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@QEAA@XZ ; std::vector<mu2::ItemMakingInfo,std::allocator<mu2::ItemMakingInfo> >::_Reallocation_guard::~_Reallocation_guard
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$0@?0???$_Emplace_reallocate@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAPEAUItemMakingInfo@mu2@@QEAU23@AEBU23@@Z@4HA ENDP ; `std::vector<mu2::ItemMakingInfo,std::allocator<mu2::ItemMakingInfo> >::_Emplace_reallocate<mu2::ItemMakingInfo const &>'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ??$_Emplace_back_with_unused_capacity@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAAEAUItemMakingInfo@mu2@@AEBU23@@Z
_TEXT	SEGMENT
$T1 = 0
_Mylast$ = 8
__that$ = 16
_My_data$ = 24
_Obj$ = 32
$T2 = 40
$T3 = 48
$T4 = 56
$T5 = 64
_Result$ = 72
this$ = 96
<_Val_0>$ = 104
??$_Emplace_back_with_unused_capacity@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAAEAUItemMakingInfo@mu2@@AEBU23@@Z PROC ; std::vector<mu2::ItemMakingInfo,std::allocator<mu2::ItemMakingInfo> >::_Emplace_back_with_unused_capacity<mu2::ItemMakingInfo const &>, COMDAT

; 852  :     _CONSTEXPR20 _Ty& _Emplace_back_with_unused_capacity(_Valty&&... _Val) {

$LN40:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 853  :         // insert by perfectly forwarding into element at end, provide strong guarantee
; 854  :         auto& _My_data   = _Mypair._Myval2;

  0000e	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00013	48 89 44 24 18	 mov	 QWORD PTR _My_data$[rsp], rax

; 855  :         pointer& _Mylast = _My_data._Mylast;

  00018	48 8b 44 24 18	 mov	 rax, QWORD PTR _My_data$[rsp]
  0001d	48 83 c0 08	 add	 rax, 8
  00021	48 89 44 24 08	 mov	 QWORD PTR _Mylast$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00026	48 8b 44 24 68	 mov	 rax, QWORD PTR <_Val_0>$[rsp]
  0002b	48 89 44 24 38	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 860  :             _STD _Construct_in_place(*_Mylast, _STD forward<_Valty>(_Val)...);

  00030	48 8b 44 24 08	 mov	 rax, QWORD PTR _Mylast$[rsp]
  00035	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00038	48 89 44 24 20	 mov	 QWORD PTR _Obj$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0003d	48 8b 44 24 20	 mov	 rax, QWORD PTR _Obj$[rsp]
  00042	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00047	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  0004c	48 89 44 24 30	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00051	48 8b 44 24 30	 mov	 rax, QWORD PTR $T3[rsp]
  00056	48 89 04 24	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 860  :             _STD _Construct_in_place(*_Mylast, _STD forward<_Valty>(_Val)...);

  0005a	48 8b 44 24 38	 mov	 rax, QWORD PTR $T4[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  0005f	48 89 44 24 40	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00064	48 8b 44 24 40	 mov	 rax, QWORD PTR $T5[rsp]
  00069	48 89 44 24 10	 mov	 QWORD PTR __that$[rsp], rax
  0006e	48 8b 04 24	 mov	 rax, QWORD PTR $T1[rsp]
  00072	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  00079	48 89 08	 mov	 QWORD PTR [rax], rcx
  0007c	48 8b 04 24	 mov	 rax, QWORD PTR $T1[rsp]
  00080	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ItemMakingInfo@mu2@@6B@
  00087	48 89 08	 mov	 QWORD PTR [rax], rcx
  0008a	48 8b 04 24	 mov	 rax, QWORD PTR $T1[rsp]
  0008e	48 8b 4c 24 10	 mov	 rcx, QWORD PTR __that$[rsp]
  00093	8b 49 08	 mov	 ecx, DWORD PTR [rcx+8]
  00096	89 48 08	 mov	 DWORD PTR [rax+8], ecx
  00099	48 8b 04 24	 mov	 rax, QWORD PTR $T1[rsp]
  0009d	48 8b 4c 24 10	 mov	 rcx, QWORD PTR __that$[rsp]
  000a2	8b 49 0c	 mov	 ecx, DWORD PTR [rcx+12]
  000a5	89 48 0c	 mov	 DWORD PTR [rax+12], ecx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 868  :         _Ty& _Result = *_Mylast;

  000a8	48 8b 44 24 08	 mov	 rax, QWORD PTR _Mylast$[rsp]
  000ad	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000b0	48 89 44 24 48	 mov	 QWORD PTR _Result$[rsp], rax

; 869  :         ++_Mylast;

  000b5	48 8b 44 24 08	 mov	 rax, QWORD PTR _Mylast$[rsp]
  000ba	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000bd	48 83 c0 10	 add	 rax, 16
  000c1	48 8b 4c 24 08	 mov	 rcx, QWORD PTR _Mylast$[rsp]
  000c6	48 89 01	 mov	 QWORD PTR [rcx], rax

; 870  : 
; 871  :         return _Result;

  000c9	48 8b 44 24 48	 mov	 rax, QWORD PTR _Result$[rsp]

; 872  :     }

  000ce	48 83 c4 58	 add	 rsp, 88			; 00000058H
  000d2	c3		 ret	 0
??$_Emplace_back_with_unused_capacity@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAAEAUItemMakingInfo@mu2@@AEBU23@@Z ENDP ; std::vector<mu2::ItemMakingInfo,std::allocator<mu2::ItemMakingInfo> >::_Emplace_back_with_unused_capacity<mu2::ItemMakingInfo const &>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ??$_Emplace_one_at_back@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAAEAUItemMakingInfo@mu2@@AEBU23@@Z
_TEXT	SEGMENT
_My_data$ = 32
_Mylast$ = 40
$T1 = 48
$T2 = 56
this$ = 80
<_Val_0>$ = 88
??$_Emplace_one_at_back@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAAEAUItemMakingInfo@mu2@@AEBU23@@Z PROC ; std::vector<mu2::ItemMakingInfo,std::allocator<mu2::ItemMakingInfo> >::_Emplace_one_at_back<mu2::ItemMakingInfo const &>, COMDAT

; 839  :     _CONSTEXPR20 _Ty& _Emplace_one_at_back(_Valty&&... _Val) {

$LN482:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 840  :         // insert by perfectly forwarding into element at end, provide strong guarantee
; 841  :         auto& _My_data   = _Mypair._Myval2;

  0000e	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00013	48 89 44 24 20	 mov	 QWORD PTR _My_data$[rsp], rax

; 842  :         pointer& _Mylast = _My_data._Mylast;

  00018	48 8b 44 24 20	 mov	 rax, QWORD PTR _My_data$[rsp]
  0001d	48 83 c0 08	 add	 rax, 8
  00021	48 89 44 24 28	 mov	 QWORD PTR _Mylast$[rsp], rax

; 843  : 
; 844  :         if (_Mylast != _My_data._Myend) {

  00026	48 8b 44 24 28	 mov	 rax, QWORD PTR _Mylast$[rsp]
  0002b	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _My_data$[rsp]
  00030	48 8b 49 10	 mov	 rcx, QWORD PTR [rcx+16]
  00034	48 39 08	 cmp	 QWORD PTR [rax], rcx
  00037	74 1e		 je	 SHORT $LN2@Emplace_on
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00039	48 8b 44 24 58	 mov	 rax, QWORD PTR <_Val_0>$[rsp]
  0003e	48 89 44 24 30	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 845  :             return _Emplace_back_with_unused_capacity(_STD forward<_Valty>(_Val)...);

  00043	48 8b 44 24 30	 mov	 rax, QWORD PTR $T1[rsp]
  00048	48 8b d0	 mov	 rdx, rax
  0004b	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  00050	e8 00 00 00 00	 call	 ??$_Emplace_back_with_unused_capacity@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAAEAUItemMakingInfo@mu2@@AEBU23@@Z ; std::vector<mu2::ItemMakingInfo,std::allocator<mu2::ItemMakingInfo> >::_Emplace_back_with_unused_capacity<mu2::ItemMakingInfo const &>
  00055	eb 24		 jmp	 SHORT $LN1@Emplace_on
$LN2@Emplace_on:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00057	48 8b 44 24 58	 mov	 rax, QWORD PTR <_Val_0>$[rsp]
  0005c	48 89 44 24 38	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 848  :         return *_Emplace_reallocate(_Mylast, _STD forward<_Valty>(_Val)...);

  00061	48 8b 44 24 38	 mov	 rax, QWORD PTR $T2[rsp]
  00066	4c 8b c0	 mov	 r8, rax
  00069	48 8b 44 24 28	 mov	 rax, QWORD PTR _Mylast$[rsp]
  0006e	48 8b 10	 mov	 rdx, QWORD PTR [rax]
  00071	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  00076	e8 00 00 00 00	 call	 ??$_Emplace_reallocate@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAPEAUItemMakingInfo@mu2@@QEAU23@AEBU23@@Z ; std::vector<mu2::ItemMakingInfo,std::allocator<mu2::ItemMakingInfo> >::_Emplace_reallocate<mu2::ItemMakingInfo const &>
$LN1@Emplace_on:

; 849  :     }

  0007b	48 83 c4 48	 add	 rsp, 72			; 00000048H
  0007f	c3		 ret	 0
??$_Emplace_one_at_back@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAAEAUItemMakingInfo@mu2@@AEBU23@@Z ENDP ; std::vector<mu2::ItemMakingInfo,std::allocator<mu2::ItemMakingInfo> >::_Emplace_one_at_back<mu2::ItemMakingInfo const &>
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Mu2Common\Protocol\Common.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File F:\Release_Branch\Server\Development\Mu2Common\Protocol\Common.h
;	COMDAT ??$Val@I@stWOPSCommand@mu2@@QEBAIH@Z
_TEXT	SEGMENT
this$ = 32
$T1 = 40
$T2 = 48
this$ = 80
arryIndex$ = 88
??$Val@I@stWOPSCommand@mu2@@QEBAIH@Z PROC		; mu2::stWOPSCommand::Val<unsigned int>, COMDAT

; 178  : 		{ 

$LN41:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 203  : 			return static_cast<Int64>(_wtoi64(getValue(arryIndex).c_str()));

  0000d	8b 54 24 58	 mov	 edx, DWORD PTR arryIndex$[rsp]
  00011	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  00016	e8 00 00 00 00	 call	 ?getValue@stWOPSCommand@mu2@@AEBAAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@H@Z ; mu2::stWOPSCommand::getValue
  0001b	48 89 44 24 20	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2356 :         return _Mypair._Myval2._Myptr();

  00020	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 ?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEBAPEB_WXZ ; std::_String_val<std::_Simple_types<wchar_t> >::_Myptr
  0002d	48 89 44 24 28	 mov	 QWORD PTR $T1[rsp], rax
; File F:\Release_Branch\Server\Development\Mu2Common\Protocol\Common.h

; 203  : 			return static_cast<Int64>(_wtoi64(getValue(arryIndex).c_str()));

  00032	48 8b 44 24 28	 mov	 rax, QWORD PTR $T1[rsp]
  00037	48 8b c8	 mov	 rcx, rax
  0003a	e8 00 00 00 00	 call	 _wtoi64
  0003f	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax

; 179  : 			static_assert(std::is_fundamental<T>::value || std::is_enum<T>::value, "Type must be fundamental");
; 180  : 			return static_cast<T>(val2Int64(arryIndex)); 

  00044	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]

; 181  : 		}

  00049	48 83 c4 48	 add	 rsp, 72			; 00000048H
  0004d	c3		 ret	 0
??$Val@I@stWOPSCommand@mu2@@QEBAIH@Z ENDP		; mu2::stWOPSCommand::Val<unsigned int>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??1WOPSGoldenTimeGiftEventImpl@mu2@@QEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1WOPSGoldenTimeGiftEventImpl@mu2@@QEAA@XZ PROC	; mu2::WOPSGoldenTimeGiftEventImpl::~WOPSGoldenTimeGiftEventImpl, COMDAT
$LN301:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 05 80 00 00
	00		 add	 rax, 128		; 00000080H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 830  :         _Tidy();

  00014	48 8b c8	 mov	 rcx, rax
  00017	e8 00 00 00 00	 call	 ?_Tidy@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAXXZ ; std::vector<mu2::ItemMakingInfo,std::allocator<mu2::ItemMakingInfo> >::_Tidy
  0001c	90		 npad	 1
  0001d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00022	48 83 c0 60	 add	 rax, 96			; 00000060H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1383 :         _Tidy_deallocate();

  00026	48 8b c8	 mov	 rcx, rax
  00029	e8 00 00 00 00	 call	 ?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate
  0002e	90		 npad	 1
  0002f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00034	48 83 c0 40	 add	 rax, 64			; 00000040H
  00038	48 8b c8	 mov	 rcx, rax
  0003b	e8 00 00 00 00	 call	 ?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate
  00040	90		 npad	 1
  00041	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00046	48 83 c0 20	 add	 rax, 32			; 00000020H
  0004a	48 8b c8	 mov	 rcx, rax
  0004d	e8 00 00 00 00	 call	 ?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate
  00052	90		 npad	 1
  00053	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00057	c3		 ret	 0
??1WOPSGoldenTimeGiftEventImpl@mu2@@QEAA@XZ ENDP	; mu2::WOPSGoldenTimeGiftEventImpl::~WOPSGoldenTimeGiftEventImpl
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\DateTime.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ??0WOPSGoldenTimeGiftEventImpl@mu2@@QEAA@XZ
_TEXT	SEGMENT
$T1 = 32
this$ = 40
this$ = 48
this$ = 56
this$ = 64
this$ = 72
systime$2 = 80
systime$3 = 96
__$ArrayPad$ = 112
this$ = 144
??0WOPSGoldenTimeGiftEventImpl@mu2@@QEAA@XZ PROC	; mu2::WOPSGoldenTimeGiftEventImpl::WOPSGoldenTimeGiftEventImpl, COMDAT
$LN162:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi
  00006	48 81 ec 80 00
	00 00		 sub	 rsp, 128		; 00000080H
  0000d	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR __security_cookie
  00014	48 33 c4	 xor	 rax, rsp
  00017	48 89 44 24 70	 mov	 QWORD PTR __$ArrayPad$[rsp], rax
  0001c	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00024	48 89 44 24 28	 mov	 QWORD PTR this$[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\DateTime.h

; 217  : : status_(valid)

  00029	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  0002e	c6 00 00	 mov	 BYTE PTR [rax], 0

; 218  : {	
; 219  : 	time_.QuadPart = 0;    // 1 Jan-1601, 00:00, 100ns clicks 

  00031	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00036	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0

; 220  : 
; 221  : 	SYSTEMTIME systime;
; 222  : 	::GetLocalTime( &systime );

  0003e	48 8d 4c 24 50	 lea	 rcx, QWORD PTR systime$2[rsp]
  00043	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetLocalTime

; 223  : 	*this = systime;

  00049	48 8d 54 24 50	 lea	 rdx, QWORD PTR systime$2[rsp]
  0004e	48 8b 4c 24 28	 mov	 rcx, QWORD PTR this$[rsp]
  00053	e8 00 00 00 00	 call	 ??4DateTime@mu2@@QEAAAEBV01@AEBU_SYSTEMTIME@@@Z ; mu2::DateTime::operator=
  00058	90		 npad	 1
  00059	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00061	48 83 c0 10	 add	 rax, 16
  00065	48 89 44 24 30	 mov	 QWORD PTR this$[rsp], rax

; 217  : : status_(valid)

  0006a	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0006f	c6 00 00	 mov	 BYTE PTR [rax], 0

; 218  : {	
; 219  : 	time_.QuadPart = 0;    // 1 Jan-1601, 00:00, 100ns clicks 

  00072	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00077	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0

; 220  : 
; 221  : 	SYSTEMTIME systime;
; 222  : 	::GetLocalTime( &systime );

  0007f	48 8d 4c 24 60	 lea	 rcx, QWORD PTR systime$3[rsp]
  00084	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetLocalTime

; 223  : 	*this = systime;

  0008a	48 8d 54 24 60	 lea	 rdx, QWORD PTR systime$3[rsp]
  0008f	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00094	e8 00 00 00 00	 call	 ??4DateTime@mu2@@QEAAAEBV01@AEBU_SYSTEMTIME@@@Z ; mu2::DateTime::operator=
  00099	90		 npad	 1
  0009a	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000a2	48 83 c0 20	 add	 rax, 32			; 00000020H
  000a6	48 8b c8	 mov	 rcx, rax
  000a9	e8 00 00 00 00	 call	 ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  000ae	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000b6	48 83 c0 40	 add	 rax, 64			; 00000040H
  000ba	48 8b c8	 mov	 rcx, rax
  000bd	e8 00 00 00 00	 call	 ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  000c2	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000ca	48 83 c0 60	 add	 rax, 96			; 00000060H
  000ce	48 8b c8	 mov	 rcx, rax
  000d1	e8 00 00 00 00	 call	 ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  000d6	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000de	48 05 80 00 00
	00		 add	 rax, 128		; 00000080H
  000e4	48 89 44 24 40	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 670  :     _CONSTEXPR20 vector() noexcept(is_nothrow_default_constructible_v<_Alty>) : _Mypair(_Zero_then_variadic_args_t{}) {

  000e9	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  000ee	48 89 44 24 48	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1531 :         : _Ty1(), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  000f3	48 8b 44 24 48	 mov	 rax, QWORD PTR this$[rsp]
  000f8	48 89 44 24 38	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 400  :     _CONSTEXPR20 _Vector_val() noexcept : _Myfirst(), _Mylast(), _Myend() {}

  000fd	48 8b 44 24 38	 mov	 rax, QWORD PTR this$[rsp]
  00102	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0
  00109	48 8b 44 24 38	 mov	 rax, QWORD PTR this$[rsp]
  0010e	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0
  00116	48 8b 44 24 38	 mov	 rax, QWORD PTR this$[rsp]
  0011b	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 671  :         _Mypair._Myval2._Alloc_proxy(_GET_PROXY_ALLOCATOR(_Alty, _Getal()));

  00123	48 8d 44 24 20	 lea	 rax, QWORD PTR $T1[rsp]
  00128	48 8b f8	 mov	 rdi, rax
  0012b	33 c0		 xor	 eax, eax
  0012d	b9 01 00 00 00	 mov	 ecx, 1
  00132	f3 aa		 rep stosb
  00134	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0013c	48 8b 4c 24 70	 mov	 rcx, QWORD PTR __$ArrayPad$[rsp]
  00141	48 33 cc	 xor	 rcx, rsp
  00144	e8 00 00 00 00	 call	 __security_check_cookie
  00149	48 81 c4 80 00
	00 00		 add	 rsp, 128		; 00000080H
  00150	5f		 pop	 rdi
  00151	c3		 ret	 0
??0WOPSGoldenTimeGiftEventImpl@mu2@@QEAA@XZ ENDP	; mu2::WOPSGoldenTimeGiftEventImpl::WOPSGoldenTimeGiftEventImpl
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ?_Xlength@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@CAXXZ
_TEXT	SEGMENT
?_Xlength@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@CAXXZ PROC ; std::vector<mu2::ItemMakingInfo,std::allocator<mu2::ItemMakingInfo> >::_Xlength, COMDAT

; 2183 :     [[noreturn]] static void _Xlength() {

$LN3:
  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 2184 :         _Xlength_error("vector too long");

  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_0BA@FOIKENOD@vector?5too?5long@
  0000b	e8 00 00 00 00	 call	 ?_Xlength_error@std@@YAXPEBD@Z ; std::_Xlength_error
  00010	90		 npad	 1
$LN2@Xlength:

; 2185 :     }

  00011	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00015	c3		 ret	 0
?_Xlength@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@CAXXZ ENDP ; std::vector<mu2::ItemMakingInfo,std::allocator<mu2::ItemMakingInfo> >::_Xlength
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ?_Tidy@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAXXZ
_TEXT	SEGMENT
_Myfirst$ = 32
_First$ = 40
_My_data$ = 48
_Bytes$ = 56
_Ptr$ = 64
_Ptr$ = 72
_Mylast$ = 80
_Myend$ = 88
$T1 = 96
$T2 = 104
_Last$ = 112
$T3 = 120
_Count$ = 128
_Ptr$ = 136
_Al$ = 144
this$ = 176
?_Tidy@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAXXZ PROC ; std::vector<mu2::ItemMakingInfo,std::allocator<mu2::ItemMakingInfo> >::_Tidy, COMDAT

; 2081 :     _CONSTEXPR20 void _Tidy() noexcept { // free all storage

$LN50:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec a8 00
	00 00		 sub	 rsp, 168		; 000000a8H

; 2227 :         return _Mypair._Get_first();

  0000c	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00014	48 89 44 24 60	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2227 :         return _Mypair._Get_first();

  00019	48 8b 44 24 60	 mov	 rax, QWORD PTR $T1[rsp]
  0001e	48 89 44 24 68	 mov	 QWORD PTR $T2[rsp], rax

; 2082 :         auto& _Al         = _Getal();

  00023	48 8b 44 24 68	 mov	 rax, QWORD PTR $T2[rsp]
  00028	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR _Al$[rsp], rax

; 2083 :         auto& _My_data    = _Mypair._Myval2;

  00030	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 89 44 24 30	 mov	 QWORD PTR _My_data$[rsp], rax

; 2084 :         pointer& _Myfirst = _My_data._Myfirst;

  0003d	48 8b 44 24 30	 mov	 rax, QWORD PTR _My_data$[rsp]
  00042	48 89 44 24 20	 mov	 QWORD PTR _Myfirst$[rsp], rax

; 2085 :         pointer& _Mylast  = _My_data._Mylast;

  00047	48 8b 44 24 30	 mov	 rax, QWORD PTR _My_data$[rsp]
  0004c	48 83 c0 08	 add	 rax, 8
  00050	48 89 44 24 50	 mov	 QWORD PTR _Mylast$[rsp], rax

; 2086 :         pointer& _Myend   = _My_data._Myend;

  00055	48 8b 44 24 30	 mov	 rax, QWORD PTR _My_data$[rsp]
  0005a	48 83 c0 10	 add	 rax, 16
  0005e	48 89 44 24 58	 mov	 QWORD PTR _Myend$[rsp], rax

; 2087 : 
; 2088 :         _My_data._Orphan_all();
; 2089 : 
; 2090 :         if (_Myfirst) { // destroy and deallocate old array

  00063	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  00068	48 83 38 00	 cmp	 QWORD PTR [rax], 0
  0006c	0f 84 f8 00 00
	00		 je	 $LN2@Tidy

; 2091 :             _STD _Destroy_range(_Myfirst, _Mylast, _Al);

  00072	48 8b 44 24 50	 mov	 rax, QWORD PTR _Mylast$[rsp]
  00077	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0007a	48 89 44 24 70	 mov	 QWORD PTR _Last$[rsp], rax
  0007f	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  00084	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00087	48 89 44 24 28	 mov	 QWORD PTR _First$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1102 :         for (; _First != _Last; ++_First) {

  0008c	eb 0e		 jmp	 SHORT $LN23@Tidy
$LN21@Tidy:
  0008e	48 8b 44 24 28	 mov	 rax, QWORD PTR _First$[rsp]
  00093	48 83 c0 10	 add	 rax, 16
  00097	48 89 44 24 28	 mov	 QWORD PTR _First$[rsp], rax
$LN23@Tidy:
  0009c	48 8b 44 24 70	 mov	 rax, QWORD PTR _Last$[rsp]
  000a1	48 39 44 24 28	 cmp	 QWORD PTR _First$[rsp], rax
  000a6	74 29		 je	 SHORT $LN22@Tidy

; 69   :     return _Ptr;

  000a8	48 8b 44 24 28	 mov	 rax, QWORD PTR _First$[rsp]
  000ad	48 89 44 24 78	 mov	 QWORD PTR $T3[rsp], rax

; 1103 :             allocator_traits<_Alloc>::destroy(_Al, _STD _Unfancy(_First));

  000b2	48 8b 44 24 78	 mov	 rax, QWORD PTR $T3[rsp]
  000b7	48 89 44 24 40	 mov	 QWORD PTR _Ptr$[rsp], rax

; 741  :         _Ptr->~_Uty();

  000bc	48 8b 44 24 40	 mov	 rax, QWORD PTR _Ptr$[rsp]
  000c1	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000c4	33 d2		 xor	 edx, edx
  000c6	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  000cb	ff 50 10	 call	 QWORD PTR [rax+16]
  000ce	90		 npad	 1

; 1104 :         }

  000cf	eb bd		 jmp	 SHORT $LN21@Tidy
$LN22@Tidy:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2093 :             _Al.deallocate(_Myfirst, static_cast<size_type>(_Myend - _Myfirst));

  000d1	48 8b 44 24 58	 mov	 rax, QWORD PTR _Myend$[rsp]
  000d6	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Myfirst$[rsp]
  000db	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  000de	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000e1	48 2b c1	 sub	 rax, rcx
  000e4	48 c1 f8 04	 sar	 rax, 4
  000e8	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _Count$[rsp], rax
  000f0	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  000f5	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000f8	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  00100	48 6b 84 24 80
	00 00 00 10	 imul	 rax, QWORD PTR _Count$[rsp], 16
  00109	48 89 44 24 38	 mov	 QWORD PTR _Bytes$[rsp], rax
  0010e	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00116	48 89 44 24 48	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  0011b	48 81 7c 24 38
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  00124	72 10		 jb	 SHORT $LN41@Tidy

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  00126	48 8d 54 24 38	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  0012b	48 8d 4c 24 48	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  00130	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  00135	90		 npad	 1
$LN41@Tidy:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  00136	48 8b 54 24 38	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  0013b	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  00140	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00145	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2095 :             _Myfirst = nullptr;

  00146	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  0014b	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0

; 2096 :             _Mylast  = nullptr;

  00152	48 8b 44 24 50	 mov	 rax, QWORD PTR _Mylast$[rsp]
  00157	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0

; 2097 :             _Myend   = nullptr;

  0015e	48 8b 44 24 58	 mov	 rax, QWORD PTR _Myend$[rsp]
  00163	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0
$LN2@Tidy:

; 2098 :         }
; 2099 :     }

  0016a	48 81 c4 a8 00
	00 00		 add	 rsp, 168		; 000000a8H
  00171	c3		 ret	 0
?_Tidy@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAXXZ ENDP ; std::vector<mu2::ItemMakingInfo,std::allocator<mu2::ItemMakingInfo> >::_Tidy
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ?_Change_array@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAXQEAUItemMakingInfo@mu2@@_K1@Z
_TEXT	SEGMENT
_Myfirst$ = 32
_First$ = 40
_My_data$ = 48
_Bytes$ = 56
_Ptr$ = 64
_Ptr$ = 72
_Mylast$ = 80
_Myend$ = 88
$T1 = 96
$T2 = 104
_Last$ = 112
$T3 = 120
_Count$ = 128
_Ptr$ = 136
_Al$ = 144
this$ = 176
_Newvec$ = 184
_Newsize$ = 192
_Newcapacity$ = 200
?_Change_array@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAXQEAUItemMakingInfo@mu2@@_K1@Z PROC ; std::vector<mu2::ItemMakingInfo,std::allocator<mu2::ItemMakingInfo> >::_Change_array, COMDAT

; 2059 :         const pointer _Newvec, const size_type _Newsize, const size_type _Newcapacity) noexcept {

$LN50:
  00000	4c 89 4c 24 20	 mov	 QWORD PTR [rsp+32], r9
  00005	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	48 81 ec a8 00
	00 00		 sub	 rsp, 168		; 000000a8H

; 2227 :         return _Mypair._Get_first();

  0001b	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00023	48 89 44 24 60	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2227 :         return _Mypair._Get_first();

  00028	48 8b 44 24 60	 mov	 rax, QWORD PTR $T1[rsp]
  0002d	48 89 44 24 68	 mov	 QWORD PTR $T2[rsp], rax

; 2060 :         // orphan all iterators, discard old array, acquire new array
; 2061 :         auto& _Al         = _Getal();

  00032	48 8b 44 24 68	 mov	 rax, QWORD PTR $T2[rsp]
  00037	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR _Al$[rsp], rax

; 2062 :         auto& _My_data    = _Mypair._Myval2;

  0003f	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00047	48 89 44 24 30	 mov	 QWORD PTR _My_data$[rsp], rax

; 2063 :         pointer& _Myfirst = _My_data._Myfirst;

  0004c	48 8b 44 24 30	 mov	 rax, QWORD PTR _My_data$[rsp]
  00051	48 89 44 24 20	 mov	 QWORD PTR _Myfirst$[rsp], rax

; 2064 :         pointer& _Mylast  = _My_data._Mylast;

  00056	48 8b 44 24 30	 mov	 rax, QWORD PTR _My_data$[rsp]
  0005b	48 83 c0 08	 add	 rax, 8
  0005f	48 89 44 24 50	 mov	 QWORD PTR _Mylast$[rsp], rax

; 2065 :         pointer& _Myend   = _My_data._Myend;

  00064	48 8b 44 24 30	 mov	 rax, QWORD PTR _My_data$[rsp]
  00069	48 83 c0 10	 add	 rax, 16
  0006d	48 89 44 24 58	 mov	 QWORD PTR _Myend$[rsp], rax

; 2066 : 
; 2067 :         _My_data._Orphan_all();
; 2068 : 
; 2069 :         if (_Myfirst) { // destroy and deallocate old array

  00072	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  00077	48 83 38 00	 cmp	 QWORD PTR [rax], 0
  0007b	0f 84 d4 00 00
	00		 je	 $LN2@Change_arr

; 2070 :             _STD _Destroy_range(_Myfirst, _Mylast, _Al);

  00081	48 8b 44 24 50	 mov	 rax, QWORD PTR _Mylast$[rsp]
  00086	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00089	48 89 44 24 70	 mov	 QWORD PTR _Last$[rsp], rax
  0008e	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  00093	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00096	48 89 44 24 28	 mov	 QWORD PTR _First$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1102 :         for (; _First != _Last; ++_First) {

  0009b	eb 0e		 jmp	 SHORT $LN23@Change_arr
$LN21@Change_arr:
  0009d	48 8b 44 24 28	 mov	 rax, QWORD PTR _First$[rsp]
  000a2	48 83 c0 10	 add	 rax, 16
  000a6	48 89 44 24 28	 mov	 QWORD PTR _First$[rsp], rax
$LN23@Change_arr:
  000ab	48 8b 44 24 70	 mov	 rax, QWORD PTR _Last$[rsp]
  000b0	48 39 44 24 28	 cmp	 QWORD PTR _First$[rsp], rax
  000b5	74 29		 je	 SHORT $LN22@Change_arr

; 69   :     return _Ptr;

  000b7	48 8b 44 24 28	 mov	 rax, QWORD PTR _First$[rsp]
  000bc	48 89 44 24 78	 mov	 QWORD PTR $T3[rsp], rax

; 1103 :             allocator_traits<_Alloc>::destroy(_Al, _STD _Unfancy(_First));

  000c1	48 8b 44 24 78	 mov	 rax, QWORD PTR $T3[rsp]
  000c6	48 89 44 24 40	 mov	 QWORD PTR _Ptr$[rsp], rax

; 741  :         _Ptr->~_Uty();

  000cb	48 8b 44 24 40	 mov	 rax, QWORD PTR _Ptr$[rsp]
  000d0	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000d3	33 d2		 xor	 edx, edx
  000d5	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  000da	ff 50 10	 call	 QWORD PTR [rax+16]
  000dd	90		 npad	 1

; 1104 :         }

  000de	eb bd		 jmp	 SHORT $LN21@Change_arr
$LN22@Change_arr:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2072 :             _Al.deallocate(_Myfirst, static_cast<size_type>(_Myend - _Myfirst));

  000e0	48 8b 44 24 58	 mov	 rax, QWORD PTR _Myend$[rsp]
  000e5	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Myfirst$[rsp]
  000ea	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  000ed	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000f0	48 2b c1	 sub	 rax, rcx
  000f3	48 c1 f8 04	 sar	 rax, 4
  000f7	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _Count$[rsp], rax
  000ff	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  00104	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00107	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  0010f	48 6b 84 24 80
	00 00 00 10	 imul	 rax, QWORD PTR _Count$[rsp], 16
  00118	48 89 44 24 38	 mov	 QWORD PTR _Bytes$[rsp], rax
  0011d	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00125	48 89 44 24 48	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  0012a	48 81 7c 24 38
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  00133	72 10		 jb	 SHORT $LN41@Change_arr

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  00135	48 8d 54 24 38	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  0013a	48 8d 4c 24 48	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  0013f	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  00144	90		 npad	 1
$LN41@Change_arr:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  00145	48 8b 54 24 38	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  0014a	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  0014f	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00154	90		 npad	 1
$LN2@Change_arr:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2075 :         _Myfirst = _Newvec;

  00155	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  0015a	48 8b 8c 24 b8
	00 00 00	 mov	 rcx, QWORD PTR _Newvec$[rsp]
  00162	48 89 08	 mov	 QWORD PTR [rax], rcx

; 2076 :         _Mylast  = _Newvec + _Newsize;

  00165	48 6b 84 24 c0
	00 00 00 10	 imul	 rax, QWORD PTR _Newsize$[rsp], 16
  0016e	48 8b 8c 24 b8
	00 00 00	 mov	 rcx, QWORD PTR _Newvec$[rsp]
  00176	48 03 c8	 add	 rcx, rax
  00179	48 8b c1	 mov	 rax, rcx
  0017c	48 8b 4c 24 50	 mov	 rcx, QWORD PTR _Mylast$[rsp]
  00181	48 89 01	 mov	 QWORD PTR [rcx], rax

; 2077 :         _Myend   = _Newvec + _Newcapacity;

  00184	48 6b 84 24 c8
	00 00 00 10	 imul	 rax, QWORD PTR _Newcapacity$[rsp], 16
  0018d	48 8b 8c 24 b8
	00 00 00	 mov	 rcx, QWORD PTR _Newvec$[rsp]
  00195	48 03 c8	 add	 rcx, rax
  00198	48 8b c1	 mov	 rax, rcx
  0019b	48 8b 4c 24 58	 mov	 rcx, QWORD PTR _Myend$[rsp]
  001a0	48 89 01	 mov	 QWORD PTR [rcx], rax

; 2078 :         _ASAN_VECTOR_CREATE;
; 2079 :     }

  001a3	48 81 c4 a8 00
	00 00		 add	 rsp, 168		; 000000a8H
  001aa	c3		 ret	 0
?_Change_array@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAXQEAUItemMakingInfo@mu2@@_K1@Z ENDP ; std::vector<mu2::ItemMakingInfo,std::allocator<mu2::ItemMakingInfo> >::_Change_array
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ?_Calculate_growth@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEBA_K_K@Z
_TEXT	SEGMENT
_Oldcapacity$ = 0
_My_data$1 = 8
$T2 = 16
$T3 = 24
tv82 = 32
_Max$ = 40
_Geometric$ = 48
$T4 = 56
$T5 = 64
$T6 = 72
$T7 = 80
$T8 = 88
$T9 = 96
$T10 = 104
$T11 = 112
_Unsigned_max$12 = 120
this$ = 144
_Newsize$ = 152
?_Calculate_growth@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEBA_K_K@Z PROC ; std::vector<mu2::ItemMakingInfo,std::allocator<mu2::ItemMakingInfo> >::_Calculate_growth, COMDAT

; 2006 :     _CONSTEXPR20 size_type _Calculate_growth(const size_type _Newsize) const {

$LN42:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H

; 1923 :         auto& _My_data = _Mypair._Myval2;

  00011	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00019	48 89 44 24 08	 mov	 QWORD PTR _My_data$1[rsp], rax

; 1924 :         return static_cast<size_type>(_My_data._Myend - _My_data._Myfirst);

  0001e	48 8b 44 24 08	 mov	 rax, QWORD PTR _My_data$1[rsp]
  00023	48 8b 4c 24 08	 mov	 rcx, QWORD PTR _My_data$1[rsp]
  00028	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0002b	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0002f	48 2b c1	 sub	 rax, rcx
  00032	48 c1 f8 04	 sar	 rax, 4
  00036	48 89 44 24 38	 mov	 QWORD PTR $T4[rsp], rax

; 2007 :         // given _Oldcapacity and _Newsize, calculate geometric growth
; 2008 :         const size_type _Oldcapacity = capacity();

  0003b	48 8b 44 24 38	 mov	 rax, QWORD PTR $T4[rsp]
  00040	48 89 04 24	 mov	 QWORD PTR _Oldcapacity$[rsp], rax

; 2231 :         return _Mypair._Get_first();

  00044	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1543 :         return *this;

  0004c	48 89 44 24 40	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2231 :         return _Mypair._Get_first();

  00051	48 8b 44 24 40	 mov	 rax, QWORD PTR $T5[rsp]
  00056	48 89 44 24 70	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 746  :         return static_cast<size_t>(-1) / sizeof(value_type);

  0005b	48 b8 ff ff ff
	ff ff ff ff 0f	 mov	 rax, 1152921504606846975 ; 0fffffffffffffffH
  00065	48 89 44 24 48	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1919 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()), _Alty_traits::max_size(_Getal()));

  0006a	48 8b 44 24 48	 mov	 rax, QWORD PTR $T6[rsp]
  0006f	48 89 44 24 10	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 866  :         constexpr auto _Unsigned_max = static_cast<make_unsigned_t<_Ty>>(-1);

  00074	48 c7 44 24 78
	ff ff ff ff	 mov	 QWORD PTR _Unsigned_max$12[rsp], -1

; 867  :         return static_cast<_Ty>(_Unsigned_max >> 1);

  0007d	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  00087	48 89 44 24 50	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1919 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()), _Alty_traits::max_size(_Getal()));

  0008c	48 8b 44 24 50	 mov	 rax, QWORD PTR $T7[rsp]
  00091	48 89 44 24 18	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 101  :     return _Right < _Left ? _Right : _Left;

  00096	48 8b 44 24 18	 mov	 rax, QWORD PTR $T3[rsp]
  0009b	48 39 44 24 10	 cmp	 QWORD PTR $T2[rsp], rax
  000a0	73 0c		 jae	 SHORT $LN37@Calculate_
  000a2	48 8d 44 24 10	 lea	 rax, QWORD PTR $T2[rsp]
  000a7	48 89 44 24 20	 mov	 QWORD PTR tv82[rsp], rax
  000ac	eb 0a		 jmp	 SHORT $LN38@Calculate_
$LN37@Calculate_:
  000ae	48 8d 44 24 18	 lea	 rax, QWORD PTR $T3[rsp]
  000b3	48 89 44 24 20	 mov	 QWORD PTR tv82[rsp], rax
$LN38@Calculate_:
  000b8	48 8b 44 24 20	 mov	 rax, QWORD PTR tv82[rsp]
  000bd	48 89 44 24 58	 mov	 QWORD PTR $T8[rsp], rax
  000c2	48 8b 44 24 58	 mov	 rax, QWORD PTR $T8[rsp]
  000c7	48 89 44 24 60	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1919 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()), _Alty_traits::max_size(_Getal()));

  000cc	48 8b 44 24 60	 mov	 rax, QWORD PTR $T9[rsp]
  000d1	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000d4	48 89 44 24 68	 mov	 QWORD PTR $T10[rsp], rax

; 2009 :         const auto _Max              = max_size();

  000d9	48 8b 44 24 68	 mov	 rax, QWORD PTR $T10[rsp]
  000de	48 89 44 24 28	 mov	 QWORD PTR _Max$[rsp], rax

; 2010 : 
; 2011 :         if (_Oldcapacity > _Max - _Oldcapacity / 2) {

  000e3	33 d2		 xor	 edx, edx
  000e5	48 8b 04 24	 mov	 rax, QWORD PTR _Oldcapacity$[rsp]
  000e9	b9 02 00 00 00	 mov	 ecx, 2
  000ee	48 f7 f1	 div	 rcx
  000f1	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Max$[rsp]
  000f6	48 2b c8	 sub	 rcx, rax
  000f9	48 8b c1	 mov	 rax, rcx
  000fc	48 39 04 24	 cmp	 QWORD PTR _Oldcapacity$[rsp], rax
  00100	76 07		 jbe	 SHORT $LN2@Calculate_

; 2012 :             return _Max; // geometric growth would overflow

  00102	48 8b 44 24 28	 mov	 rax, QWORD PTR _Max$[rsp]
  00107	eb 3b		 jmp	 SHORT $LN1@Calculate_
$LN2@Calculate_:

; 2013 :         }
; 2014 : 
; 2015 :         const size_type _Geometric = _Oldcapacity + _Oldcapacity / 2;

  00109	33 d2		 xor	 edx, edx
  0010b	48 8b 04 24	 mov	 rax, QWORD PTR _Oldcapacity$[rsp]
  0010f	b9 02 00 00 00	 mov	 ecx, 2
  00114	48 f7 f1	 div	 rcx
  00117	48 8b 0c 24	 mov	 rcx, QWORD PTR _Oldcapacity$[rsp]
  0011b	48 03 c8	 add	 rcx, rax
  0011e	48 8b c1	 mov	 rax, rcx
  00121	48 89 44 24 30	 mov	 QWORD PTR _Geometric$[rsp], rax

; 2016 : 
; 2017 :         if (_Geometric < _Newsize) {

  00126	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR _Newsize$[rsp]
  0012e	48 39 44 24 30	 cmp	 QWORD PTR _Geometric$[rsp], rax
  00133	73 0a		 jae	 SHORT $LN3@Calculate_

; 2018 :             return _Newsize; // geometric growth would be insufficient

  00135	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR _Newsize$[rsp]
  0013d	eb 05		 jmp	 SHORT $LN1@Calculate_
$LN3@Calculate_:

; 2019 :         }
; 2020 : 
; 2021 :         return _Geometric; // geometric growth is sufficient

  0013f	48 8b 44 24 30	 mov	 rax, QWORD PTR _Geometric$[rsp]
$LN1@Calculate_:

; 2022 :     }

  00144	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  0014b	c3		 ret	 0
?_Calculate_growth@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEBA_K_K@Z ENDP ; std::vector<mu2::ItemMakingInfo,std::allocator<mu2::ItemMakingInfo> >::_Calculate_growth
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?allocate@?$allocator@UItemMakingInfo@mu2@@@std@@QEAAPEAUItemMakingInfo@mu2@@_K@Z
_TEXT	SEGMENT
_Overflow_is_possible$1 = 32
_Bytes$ = 40
$T2 = 48
$T3 = 56
$T4 = 64
_Max_possible$5 = 72
this$ = 96
_Count$ = 104
?allocate@?$allocator@UItemMakingInfo@mu2@@@std@@QEAAPEAUItemMakingInfo@mu2@@_K@Z PROC ; std::allocator<mu2::ItemMakingInfo>::allocate, COMDAT

; 988  :     _NODISCARD_RAW_PTR_ALLOC _CONSTEXPR20 __declspec(allocator) _Ty* allocate(_CRT_GUARDOVERFLOW const size_t _Count) {

$LN13:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 113  :     constexpr bool _Overflow_is_possible = _Ty_size > 1;

  0000e	c6 44 24 20 01	 mov	 BYTE PTR _Overflow_is_possible$1[rsp], 1

; 114  : 
; 115  :     if constexpr (_Overflow_is_possible) {
; 116  :         constexpr size_t _Max_possible = static_cast<size_t>(-1) / _Ty_size;

  00013	48 b8 ff ff ff
	ff ff ff ff 0f	 mov	 rax, 1152921504606846975 ; 0fffffffffffffffH
  0001d	48 89 44 24 48	 mov	 QWORD PTR _Max_possible$5[rsp], rax

; 117  :         if (_Count > _Max_possible) {

  00022	48 b8 ff ff ff
	ff ff ff ff 0f	 mov	 rax, 1152921504606846975 ; 0fffffffffffffffH
  0002c	48 39 44 24 68	 cmp	 QWORD PTR _Count$[rsp], rax
  00031	76 06		 jbe	 SHORT $LN4@allocate

; 118  :             _Throw_bad_array_new_length(); // multiply overflow

  00033	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00038	90		 npad	 1
$LN4@allocate:

; 119  :         }
; 120  :     }
; 121  : 
; 122  :     return _Count * _Ty_size;

  00039	48 6b 44 24 68
	10		 imul	 rax, QWORD PTR _Count$[rsp], 16
  0003f	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00044	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
  00049	48 89 44 24 28	 mov	 QWORD PTR _Bytes$[rsp], rax

; 227  :     if (_Bytes == 0) {

  0004e	48 83 7c 24 28
	00		 cmp	 QWORD PTR _Bytes$[rsp], 0
  00054	75 0b		 jne	 SHORT $LN8@allocate

; 228  :         return nullptr;

  00056	48 c7 44 24 30
	00 00 00 00	 mov	 QWORD PTR $T2[rsp], 0
  0005f	eb 35		 jmp	 SHORT $LN7@allocate
$LN8@allocate:

; 229  :     }
; 230  : 
; 231  : #if _HAS_CXX20 // TRANSITION, GH-1532
; 232  :     if (_STD is_constant_evaluated()) {
; 233  :         return _Traits::_Allocate(_Bytes);
; 234  :     }
; 235  : #endif // _HAS_CXX20
; 236  : 
; 237  : #ifdef __cpp_aligned_new
; 238  :     if constexpr (_Align > __STDCPP_DEFAULT_NEW_ALIGNMENT__) {
; 239  :         size_t _Passed_align = _Align;
; 240  : #if defined(_M_IX86) || defined(_M_X64)
; 241  :         if (_Bytes >= _Big_allocation_threshold) {
; 242  :             // boost the alignment of big allocations to help autovectorization
; 243  :             _Passed_align = (_STD max)(_Align, _Big_allocation_alignment);
; 244  :         }
; 245  : #endif // defined(_M_IX86) || defined(_M_X64)
; 246  :         return _Traits::_Allocate_aligned(_Bytes, _Passed_align);
; 247  :     } else
; 248  : #endif // defined(__cpp_aligned_new)
; 249  :     {
; 250  : #if defined(_M_IX86) || defined(_M_X64)
; 251  :         if (_Bytes >= _Big_allocation_threshold) {

  00061	48 81 7c 24 28
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  0006a	72 11		 jb	 SHORT $LN9@allocate

; 252  :             // boost the alignment of big allocations to help autovectorization
; 253  :             return _Allocate_manually_vector_aligned<_Traits>(_Bytes);

  0006c	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00071	e8 00 00 00 00	 call	 ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
  00076	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
  0007b	eb 19		 jmp	 SHORT $LN7@allocate
$LN9@allocate:

; 136  :         return ::operator new(_Bytes);

  0007d	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00082	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00087	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax

; 256  :         return _Traits::_Allocate(_Bytes);

  0008c	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  00091	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
$LN7@allocate:

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00096	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
$LN6@allocate:

; 991  :     }

  0009b	48 83 c4 58	 add	 rsp, 88			; 00000058H
  0009f	c3		 ret	 0
?allocate@?$allocator@UItemMakingInfo@mu2@@@std@@QEAAPEAUItemMakingInfo@mu2@@_K@Z ENDP ; std::allocator<mu2::ItemMakingInfo>::allocate
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??_GWOPSCommandGoldenTimeGift@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GWOPSCommandGoldenTimeGift@mu2@@UEAAPEAXI@Z PROC	; mu2::WOPSCommandGoldenTimeGift::`scalar deleting destructor', COMDAT
$LN5:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00012	e8 00 00 00 00	 call	 ??1WOPSCommandGoldenTimeGift@mu2@@UEAA@XZ ; mu2::WOPSCommandGoldenTimeGift::~WOPSCommandGoldenTimeGift
  00017	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  0001b	83 e0 01	 and	 eax, 1
  0001e	85 c0		 test	 eax, eax
  00020	74 10		 je	 SHORT $LN2@scalar
  00022	ba 48 00 00 00	 mov	 edx, 72			; 00000048H
  00027	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0002c	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00031	90		 npad	 1
$LN2@scalar:
  00032	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00037	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0003b	c3		 ret	 0
??_GWOPSCommandGoldenTimeGift@mu2@@UEAAPEAXI@Z ENDP	; mu2::WOPSCommandGoldenTimeGift::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
;	COMDAT ??1?$unique_ptr@UWOPSGoldenTimeGiftEventImpl@mu2@@U?$default_delete@UWOPSGoldenTimeGiftEventImpl@mu2@@@std@@@std@@QEAA@XZ
_TEXT	SEGMENT
$T1 = 32
_Ptr$ = 40
$T2 = 48
tv81 = 56
$T3 = 64
this$ = 96
??1?$unique_ptr@UWOPSGoldenTimeGiftEventImpl@mu2@@U?$default_delete@UWOPSGoldenTimeGiftEventImpl@mu2@@@std@@@std@@QEAA@XZ PROC ; std::unique_ptr<mu2::WOPSGoldenTimeGiftEventImpl,std::default_delete<mu2::WOPSGoldenTimeGiftEventImpl> >::~unique_ptr<mu2::WOPSGoldenTimeGiftEventImpl,std::default_delete<mu2::WOPSGoldenTimeGiftEventImpl> >, COMDAT

; 3425 :     _CONSTEXPR23 ~unique_ptr() noexcept {

$LN325:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 3426 :         if (_Mypair._Myval2) {

  00009	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 38 00	 cmp	 QWORD PTR [rax], 0
  00012	74 6e		 je	 SHORT $LN2@unique_ptr

; 3427 :             _Mypair._Get_first()(_Mypair._Myval2);

  00014	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00019	48 89 44 24 40	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3427 :             _Mypair._Get_first()(_Mypair._Myval2);

  0001e	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00026	48 89 44 24 28	 mov	 QWORD PTR _Ptr$[rsp], rax

; 3309 :         delete _Ptr;

  0002b	48 8b 44 24 28	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00030	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
  00035	48 83 7c 24 20
	00		 cmp	 QWORD PTR $T1[rsp], 0
  0003b	74 3c		 je	 SHORT $LN12@unique_ptr
  0003d	48 8b 4c 24 20	 mov	 rcx, QWORD PTR $T1[rsp]
  00042	e8 00 00 00 00	 call	 ??1WOPSGoldenTimeGiftEventImpl@mu2@@QEAA@XZ
  00047	b8 01 00 00 00	 mov	 eax, 1
  0004c	83 e0 01	 and	 eax, 1
  0004f	85 c0		 test	 eax, eax
  00051	74 10		 je	 SHORT $LN18@unique_ptr
  00053	ba 98 00 00 00	 mov	 edx, 152		; 00000098H
  00058	48 8b 4c 24 20	 mov	 rcx, QWORD PTR $T1[rsp]
  0005d	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00062	90		 npad	 1
$LN18@unique_ptr:
  00063	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00068	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
  0006d	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
  00072	48 89 44 24 38	 mov	 QWORD PTR tv81[rsp], rax
  00077	eb 09		 jmp	 SHORT $LN2@unique_ptr
$LN12@unique_ptr:
  00079	48 c7 44 24 38
	00 00 00 00	 mov	 QWORD PTR tv81[rsp], 0
$LN2@unique_ptr:

; 3428 :         }
; 3429 : 
; 3430 : #if _MSVC_STL_DESTRUCTOR_TOMBSTONES
; 3431 :         if constexpr (is_pointer_v<pointer>) {
; 3432 :             if (!_STD _Is_constant_evaluated()) {
; 3433 :                 const auto _Tombstone{reinterpret_cast<pointer>(_MSVC_STL_UINTPTR_TOMBSTONE_VALUE)};
; 3434 :                 _Mypair._Myval2 = _Tombstone;
; 3435 :             }
; 3436 :         }
; 3437 : #endif // _MSVC_STL_DESTRUCTOR_TOMBSTONES
; 3438 :     }

  00082	48 83 c4 58	 add	 rsp, 88			; 00000058H
  00086	c3		 ret	 0
??1?$unique_ptr@UWOPSGoldenTimeGiftEventImpl@mu2@@U?$default_delete@UWOPSGoldenTimeGiftEventImpl@mu2@@@std@@@std@@QEAA@XZ ENDP ; std::unique_ptr<mu2::WOPSGoldenTimeGiftEventImpl,std::default_delete<mu2::WOPSGoldenTimeGiftEventImpl> >::~unique_ptr<mu2::WOPSGoldenTimeGiftEventImpl,std::default_delete<mu2::WOPSGoldenTimeGiftEventImpl> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeGiftEvent.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Mu2Common\Protocol\Common.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeGiftEvent.cpp
; File F:\Release_Branch\Shared\Protocol\Common\StructuresItem.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeGiftEvent.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeGiftEvent.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeGiftEvent.cpp
; File F:\Release_Branch\Server\Development\Mu2Common\Protocol\Common.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeGiftEvent.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeGiftEvent.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File F:\Release_Branch\Server\Development\Mu2Common\Protocol\Common.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeGiftEvent.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeGiftEvent.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File F:\Release_Branch\Server\Development\Mu2Common\Protocol\Common.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeGiftEvent.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeGiftEvent.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeGiftEvent.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeGiftEvent.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeGiftEvent.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeGiftEvent.cpp
;	COMDAT ?parse@WOPSCommandGoldenTimeGift@mu2@@MEAA_NAEBUstWOPSCommand@2@@Z
_TEXT	SEGMENT
$T1 = 96
$T2 = 97
$T3 = 98
$T4 = 99
$T5 = 100
$T6 = 104
i$7 = 108
tv330 = 112
tv366 = 116
tv383 = 120
tv432 = 124
tv438 = 128
info$8 = 136
_My_data$9 = 152
hConsole$10 = 160
_My_data$11 = 168
$T12 = 176
$T13 = 184
$T14 = 192
tv296 = 200
$T15 = 208
tv298 = 216
$T16 = 224
tv300 = 232
$T17 = 240
tv187 = 248
$T18 = 256
tv217 = 264
$T19 = 272
tv247 = 280
$T20 = 288
tv277 = 296
$T21 = 304
$T22 = 312
$T23 = 344
$T24 = 376
__$ArrayPad$ = 408
this$ = 432
command$ = 440
?parse@WOPSCommandGoldenTimeGift@mu2@@MEAA_NAEBUstWOPSCommand@2@@Z PROC ; mu2::WOPSCommandGoldenTimeGift::parse, COMDAT

; 36   : {

$LN330:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 81 ec a8 01
	00 00		 sub	 rsp, 424		; 000001a8H
  00011	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR __security_cookie
  00018	48 33 c4	 xor	 rax, rsp
  0001b	48 89 84 24 98
	01 00 00	 mov	 QWORD PTR __$ArrayPad$[rsp], rax
  00023	c7 44 24 68 00
	00 00 00	 mov	 DWORD PTR $T6[rsp], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1914 :         auto& _My_data = _Mypair._Myval2;

  0002b	48 8b 84 24 b8
	01 00 00	 mov	 rax, QWORD PTR command$[rsp]
  00033	48 83 c0 38	 add	 rax, 56			; 00000038H
  00037	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR _My_data$9[rsp], rax

; 1915 :         return static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst);

  0003f	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR _My_data$9[rsp]
  00047	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR _My_data$9[rsp]
  0004f	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00052	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00056	48 2b c1	 sub	 rax, rcx
  00059	48 c1 f8 05	 sar	 rax, 5
  0005d	48 89 84 24 30
	01 00 00	 mov	 QWORD PTR $T21[rsp], rax
; File F:\Release_Branch\Server\Development\Mu2Common\Protocol\Common.h

; 132  : 			return values.size();

  00065	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR $T21[rsp]
  0006d	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeGiftEvent.cpp

; 37   : 	VERIFY_RETURN(13 <= command.GetValueCount(), false);

  00075	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
  0007d	48 83 f8 0d	 cmp	 rax, 13
  00081	0f 83 a9 00 00
	00		 jae	 $LN5@parse
  00087	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  0008c	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  00092	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR hConsole$10[rsp], rax
  0009a	66 ba 0d 00	 mov	 dx, 13
  0009e	48 8b 8c 24 a0
	00 00 00	 mov	 rcx, QWORD PTR hConsole$10[rsp]
  000a6	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000ac	c7 44 24 50 25
	00 00 00	 mov	 DWORD PTR [rsp+80], 37	; 00000025H
  000b4	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0GB@PMIBDBHN@F?3?2Release_Branch?2Server?2Develo@
  000bb	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  000c0	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BO@EHLCCMDK@13?5?$DM?$DN?5command?4GetValueCount?$CI?$CJ@
  000c7	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  000cc	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CG@IJGMIACC@mu2?3?3WOPSCommandGoldenTimeGift?3@
  000d3	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  000d8	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  000df	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  000e4	c7 44 24 28 25
	00 00 00	 mov	 DWORD PTR [rsp+40], 37	; 00000025H
  000ec	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0GB@PMIBDBHN@F?3?2Release_Branch?2Server?2Develo@
  000f3	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  000f8	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CG@IJGMIACC@mu2?3?3WOPSCommandGoldenTimeGift?3@
  000ff	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  00105	ba 02 00 00 00	 mov	 edx, 2
  0010a	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  00111	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  00116	66 ba 07 00	 mov	 dx, 7
  0011a	48 8b 8c 24 a0
	00 00 00	 mov	 rcx, QWORD PTR hConsole$10[rsp]
  00122	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  00128	90		 npad	 1
  00129	32 c0		 xor	 al, al
  0012b	e9 b5 05 00 00	 jmp	 $LN1@parse
$LN5@parse:

; 38   : 	
; 39   : 	// 최대 5개 아이템 지원
; 40   : 	for( auto i = 0; i < 5; ++ i )

  00130	c7 44 24 6c 00
	00 00 00	 mov	 DWORD PTR i$7[rsp], 0
  00138	eb 0a		 jmp	 SHORT $LN4@parse
$LN2@parse:
  0013a	8b 44 24 6c	 mov	 eax, DWORD PTR i$7[rsp]
  0013e	ff c0		 inc	 eax
  00140	89 44 24 6c	 mov	 DWORD PTR i$7[rsp], eax
$LN4@parse:
  00144	83 7c 24 6c 05	 cmp	 DWORD PTR i$7[rsp], 5
  00149	0f 8d e9 00 00
	00		 jge	 $LN3@parse
  0014f	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  00156	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR info$8[rsp], rax
  0015e	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_7ItemMakingInfo@mu2@@6B@
  00165	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR info$8[rsp], rax
  0016d	c7 84 24 90 00
	00 00 00 00 00
	00		 mov	 DWORD PTR info$8[rsp+8], 0
  00178	c7 84 24 94 00
	00 00 00 00 00
	00		 mov	 DWORD PTR info$8[rsp+12], 0

; 41   : 	{
; 42   : 		ItemMakingInfo	info;
; 43   : 		info.index	= command.Val<UInt32>(0 * i);

  00183	6b 44 24 6c 00	 imul	 eax, DWORD PTR i$7[rsp], 0
  00188	8b d0		 mov	 edx, eax
  0018a	48 8b 8c 24 b8
	01 00 00	 mov	 rcx, QWORD PTR command$[rsp]
  00192	e8 00 00 00 00	 call	 ??$Val@I@stWOPSCommand@mu2@@QEBAIH@Z ; mu2::stWOPSCommand::Val<unsigned int>
  00197	89 84 24 90 00
	00 00		 mov	 DWORD PTR info$8[rsp+8], eax

; 44   : 		info.stack	= command.Val<UInt32>(1 * i);

  0019e	8b 54 24 6c	 mov	 edx, DWORD PTR i$7[rsp]
  001a2	48 8b 8c 24 b8
	01 00 00	 mov	 rcx, QWORD PTR command$[rsp]
  001aa	e8 00 00 00 00	 call	 ??$Val@I@stWOPSCommand@mu2@@QEBAIH@Z ; mu2::stWOPSCommand::Val<unsigned int>
  001af	89 84 24 94 00
	00 00		 mov	 DWORD PTR info$8[rsp+12], eax
; File F:\Release_Branch\Shared\Protocol\Common\StructuresItem.h

; 1621 : 		return 0 < index && 0 < stack;

  001b6	83 bc 24 90 00
	00 00 00	 cmp	 DWORD PTR info$8[rsp+8], 0
  001be	76 14		 jbe	 SHORT $LN53@parse
  001c0	83 bc 24 94 00
	00 00 00	 cmp	 DWORD PTR info$8[rsp+12], 0
  001c8	76 0a		 jbe	 SHORT $LN53@parse
  001ca	c7 44 24 70 01
	00 00 00	 mov	 DWORD PTR tv330[rsp], 1
  001d2	eb 08		 jmp	 SHORT $LN54@parse
$LN53@parse:
  001d4	c7 44 24 70 00
	00 00 00	 mov	 DWORD PTR tv330[rsp], 0
$LN54@parse:
  001dc	0f b6 44 24 70	 movzx	 eax, BYTE PTR tv330[rsp]
  001e1	88 44 24 60	 mov	 BYTE PTR $T1[rsp], al
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeGiftEvent.cpp

; 46   : 		if( info.IsValid() )

  001e5	0f b6 44 24 60	 movzx	 eax, BYTE PTR $T1[rsp]
  001ea	0f b6 c0	 movzx	 eax, al
  001ed	85 c0		 test	 eax, eax
  001ef	74 33		 je	 SHORT $LN6@parse
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3453 :         return _Mypair._Myval2;

  001f1	48 8b 84 24 b0
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  001f9	48 8b 40 40	 mov	 rax, QWORD PTR [rax+64]
  001fd	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeGiftEvent.cpp

; 48   : 			m_impl->items.push_back( info );

  00205	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T13[rsp]
  0020d	48 05 80 00 00
	00		 add	 rax, 128		; 00000080H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 933  :         _Emplace_one_at_back(_Val);

  00213	48 8d 94 24 88
	00 00 00	 lea	 rdx, QWORD PTR info$8[rsp]
  0021b	48 8b c8	 mov	 rcx, rax
  0021e	e8 00 00 00 00	 call	 ??$_Emplace_one_at_back@AEBUItemMakingInfo@mu2@@@?$vector@UItemMakingInfo@mu2@@V?$allocator@UItemMakingInfo@mu2@@@std@@@std@@AEAAAEAUItemMakingInfo@mu2@@AEBU23@@Z ; std::vector<mu2::ItemMakingInfo,std::allocator<mu2::ItemMakingInfo> >::_Emplace_one_at_back<mu2::ItemMakingInfo const &>
  00223	90		 npad	 1
$LN6@parse:
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h

; 172  : 	virtual~ISerializer() {}

  00224	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  0022b	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR info$8[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeGiftEvent.cpp

; 50   : 	}

  00233	e9 02 ff ff ff	 jmp	 $LN2@parse
$LN3@parse:
; File F:\Release_Branch\Server\Development\Mu2Common\Protocol\Common.h

; 186  : 			return getValue(arryIndex);

  00238	ba 0a 00 00 00	 mov	 edx, 10
  0023d	48 8b 8c 24 b8
	01 00 00	 mov	 rcx, QWORD PTR command$[rsp]
  00245	e8 00 00 00 00	 call	 ?getValue@stWOPSCommand@mu2@@AEBAAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@H@Z ; mu2::stWOPSCommand::getValue
  0024a	48 8b d0	 mov	 rdx, rax
  0024d	48 8d 8c 24 38
	01 00 00	 lea	 rcx, QWORD PTR $T22[rsp]
  00255	e8 00 00 00 00	 call	 ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  0025a	8b 44 24 68	 mov	 eax, DWORD PTR $T6[rsp]
  0025e	83 c8 01	 or	 eax, 1
  00261	89 44 24 68	 mov	 DWORD PTR $T6[rsp], eax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeGiftEvent.cpp

; 52   : 	m_impl->sender		= command.Val<std::wstring>(10);

  00265	48 8d 84 24 38
	01 00 00	 lea	 rax, QWORD PTR $T22[rsp]
  0026d	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR tv296[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3453 :         return _Mypair._Myval2;

  00275	48 8b 84 24 b0
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0027d	48 8b 40 40	 mov	 rax, QWORD PTR [rax+64]
  00281	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T14[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeGiftEvent.cpp

; 52   : 	m_impl->sender		= command.Val<std::wstring>(10);

  00289	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR $T14[rsp]
  00291	48 83 c0 20	 add	 rax, 32			; 00000020H
  00295	48 8b 94 24 c8
	00 00 00	 mov	 rdx, QWORD PTR tv296[rsp]
  0029d	48 8b c8	 mov	 rcx, rax
  002a0	e8 00 00 00 00	 call	 ??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV01@$$QEAV01@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::operator=
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1383 :         _Tidy_deallocate();

  002a5	48 8d 8c 24 38
	01 00 00	 lea	 rcx, QWORD PTR $T22[rsp]
  002ad	e8 00 00 00 00	 call	 ?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate
  002b2	90		 npad	 1
; File F:\Release_Branch\Server\Development\Mu2Common\Protocol\Common.h

; 186  : 			return getValue(arryIndex);

  002b3	ba 0b 00 00 00	 mov	 edx, 11
  002b8	48 8b 8c 24 b8
	01 00 00	 mov	 rcx, QWORD PTR command$[rsp]
  002c0	e8 00 00 00 00	 call	 ?getValue@stWOPSCommand@mu2@@AEBAAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@H@Z ; mu2::stWOPSCommand::getValue
  002c5	48 8b d0	 mov	 rdx, rax
  002c8	48 8d 8c 24 58
	01 00 00	 lea	 rcx, QWORD PTR $T23[rsp]
  002d0	e8 00 00 00 00	 call	 ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  002d5	8b 44 24 68	 mov	 eax, DWORD PTR $T6[rsp]
  002d9	83 c8 02	 or	 eax, 2
  002dc	89 44 24 68	 mov	 DWORD PTR $T6[rsp], eax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeGiftEvent.cpp

; 53   : 	m_impl->subject	= command.Val<std::wstring>(11);

  002e0	48 8d 84 24 58
	01 00 00	 lea	 rax, QWORD PTR $T23[rsp]
  002e8	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR tv298[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3453 :         return _Mypair._Myval2;

  002f0	48 8b 84 24 b0
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  002f8	48 8b 40 40	 mov	 rax, QWORD PTR [rax+64]
  002fc	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR $T15[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeGiftEvent.cpp

; 53   : 	m_impl->subject	= command.Val<std::wstring>(11);

  00304	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR $T15[rsp]
  0030c	48 83 c0 40	 add	 rax, 64			; 00000040H
  00310	48 8b 94 24 d8
	00 00 00	 mov	 rdx, QWORD PTR tv298[rsp]
  00318	48 8b c8	 mov	 rcx, rax
  0031b	e8 00 00 00 00	 call	 ??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV01@$$QEAV01@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::operator=
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1383 :         _Tidy_deallocate();

  00320	48 8d 8c 24 58
	01 00 00	 lea	 rcx, QWORD PTR $T23[rsp]
  00328	e8 00 00 00 00	 call	 ?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate
  0032d	90		 npad	 1
; File F:\Release_Branch\Server\Development\Mu2Common\Protocol\Common.h

; 186  : 			return getValue(arryIndex);

  0032e	ba 0c 00 00 00	 mov	 edx, 12
  00333	48 8b 8c 24 b8
	01 00 00	 mov	 rcx, QWORD PTR command$[rsp]
  0033b	e8 00 00 00 00	 call	 ?getValue@stWOPSCommand@mu2@@AEBAAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@H@Z ; mu2::stWOPSCommand::getValue
  00340	48 8b d0	 mov	 rdx, rax
  00343	48 8d 8c 24 78
	01 00 00	 lea	 rcx, QWORD PTR $T24[rsp]
  0034b	e8 00 00 00 00	 call	 ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  00350	8b 44 24 68	 mov	 eax, DWORD PTR $T6[rsp]
  00354	83 c8 04	 or	 eax, 4
  00357	89 44 24 68	 mov	 DWORD PTR $T6[rsp], eax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeGiftEvent.cpp

; 54   : 	m_impl->content	= command.Val<std::wstring>(12);

  0035b	48 8d 84 24 78
	01 00 00	 lea	 rax, QWORD PTR $T24[rsp]
  00363	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR tv300[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3453 :         return _Mypair._Myval2;

  0036b	48 8b 84 24 b0
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00373	48 8b 40 40	 mov	 rax, QWORD PTR [rax+64]
  00377	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR $T16[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeGiftEvent.cpp

; 54   : 	m_impl->content	= command.Val<std::wstring>(12);

  0037f	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR $T16[rsp]
  00387	48 83 c0 60	 add	 rax, 96			; 00000060H
  0038b	48 8b 94 24 e8
	00 00 00	 mov	 rdx, QWORD PTR tv300[rsp]
  00393	48 8b c8	 mov	 rcx, rax
  00396	e8 00 00 00 00	 call	 ??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV01@$$QEAV01@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::operator=
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1383 :         _Tidy_deallocate();

  0039b	48 8d 8c 24 78
	01 00 00	 lea	 rcx, QWORD PTR $T24[rsp]
  003a3	e8 00 00 00 00	 call	 ?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate
  003a8	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3453 :         return _Mypair._Myval2;

  003a9	48 8b 84 24 b0
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  003b1	48 8b 40 40	 mov	 rax, QWORD PTR [rax+64]
  003b5	48 89 84 24 f0
	00 00 00	 mov	 QWORD PTR $T17[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1909 :         auto& _My_data = _Mypair._Myval2;

  003bd	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR $T17[rsp]
  003c5	48 05 80 00 00
	00		 add	 rax, 128		; 00000080H
  003cb	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR _My_data$11[rsp], rax

; 1910 :         return _My_data._Myfirst == _My_data._Mylast;

  003d3	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR _My_data$11[rsp]
  003db	48 8b 8c 24 a8
	00 00 00	 mov	 rcx, QWORD PTR _My_data$11[rsp]
  003e3	48 8b 49 08	 mov	 rcx, QWORD PTR [rcx+8]
  003e7	48 39 08	 cmp	 QWORD PTR [rax], rcx
  003ea	75 0a		 jne	 SHORT $LN289@parse
  003ec	c7 44 24 74 01
	00 00 00	 mov	 DWORD PTR tv366[rsp], 1
  003f4	eb 08		 jmp	 SHORT $LN290@parse
$LN289@parse:
  003f6	c7 44 24 74 00
	00 00 00	 mov	 DWORD PTR tv366[rsp], 0
$LN290@parse:
  003fe	0f b6 44 24 74	 movzx	 eax, BYTE PTR tv366[rsp]
  00403	88 44 24 61	 mov	 BYTE PTR $T2[rsp], al
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeGiftEvent.cpp

; 59   : 	if (m_impl->items.empty())

  00407	0f b6 44 24 61	 movzx	 eax, BYTE PTR $T2[rsp]
  0040c	0f b6 c0	 movzx	 eax, al
  0040f	85 c0		 test	 eax, eax
  00411	74 7b		 je	 SHORT $LN7@parse

; 60   : 	{
; 61   : 		MU2_WARN_LOG(LogCategory::CONTENTS, "WOPSGoldenTimeGiftEvent::isValid > gift item is empty. Type(%u), id(%llu)", GetCommandCode(), GetCommandId());

  00413	48 8b 8c 24 b0
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0041b	e8 00 00 00 00	 call	 ?GetCommandId@WOPSCommand@mu2@@QEBA_KXZ ; mu2::WOPSCommand::GetCommandId
  00420	48 89 84 24 f8
	00 00 00	 mov	 QWORD PTR tv187[rsp], rax
  00428	48 8b 8c 24 b0
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00430	e8 00 00 00 00	 call	 ?GetCommandCode@WOPSCommand@mu2@@QEBA?AW4Enum@WOPSCommandCode@2@XZ ; mu2::WOPSCommand::GetCommandCode
  00435	0f b7 c0	 movzx	 eax, ax
  00438	48 8b 8c 24 f8
	00 00 00	 mov	 rcx, QWORD PTR tv187[rsp]
  00440	48 89 4c 24 40	 mov	 QWORD PTR [rsp+64], rcx
  00445	89 44 24 38	 mov	 DWORD PTR [rsp+56], eax
  00449	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EK@BGEBHHHP@WOPSGoldenTimeGiftEvent?3?3isVali@
  00450	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  00455	c7 44 24 28 3d
	00 00 00	 mov	 DWORD PTR [rsp+40], 61	; 0000003dH
  0045d	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0GB@PMIBDBHN@F?3?2Release_Branch?2Server?2Develo@
  00464	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  00469	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CG@IJGMIACC@mu2?3?3WOPSCommandGoldenTimeGift?3@
  00470	41 b8 30 75 00
	00		 mov	 r8d, 30000		; 00007530H
  00476	ba 19 00 00 00	 mov	 edx, 25
  0047b	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  00482	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging

; 62   : 		return false;

  00487	32 c0		 xor	 al, al
  00489	e9 57 02 00 00	 jmp	 $LN1@parse
$LN7@parse:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3453 :         return _Mypair._Myval2;

  0048e	48 8b 84 24 b0
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00496	48 8b 40 40	 mov	 rax, QWORD PTR [rax+64]
  0049a	48 89 84 24 00
	01 00 00	 mov	 QWORD PTR $T18[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2494 :         return _Mypair._Myval2._Mysize == 0;

  004a2	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR $T18[rsp]
  004aa	48 83 78 30 00	 cmp	 QWORD PTR [rax+48], 0
  004af	75 0a		 jne	 SHORT $LN301@parse
  004b1	c7 44 24 78 01
	00 00 00	 mov	 DWORD PTR tv383[rsp], 1
  004b9	eb 08		 jmp	 SHORT $LN302@parse
$LN301@parse:
  004bb	c7 44 24 78 00
	00 00 00	 mov	 DWORD PTR tv383[rsp], 0
$LN302@parse:
  004c3	0f b6 44 24 78	 movzx	 eax, BYTE PTR tv383[rsp]
  004c8	88 44 24 62	 mov	 BYTE PTR $T3[rsp], al
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeGiftEvent.cpp

; 65   : 	if (m_impl->sender.empty())

  004cc	0f b6 44 24 62	 movzx	 eax, BYTE PTR $T3[rsp]
  004d1	0f b6 c0	 movzx	 eax, al
  004d4	85 c0		 test	 eax, eax
  004d6	74 7b		 je	 SHORT $LN8@parse

; 66   : 	{
; 67   : 		MU2_WARN_LOG(LogCategory::CONTENTS, "WOPSGoldenTimeGiftEvent::isValid > sender info is empty. Type(%u), id(%llu)", GetCommandCode(), GetCommandId());

  004d8	48 8b 8c 24 b0
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  004e0	e8 00 00 00 00	 call	 ?GetCommandId@WOPSCommand@mu2@@QEBA_KXZ ; mu2::WOPSCommand::GetCommandId
  004e5	48 89 84 24 08
	01 00 00	 mov	 QWORD PTR tv217[rsp], rax
  004ed	48 8b 8c 24 b0
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  004f5	e8 00 00 00 00	 call	 ?GetCommandCode@WOPSCommand@mu2@@QEBA?AW4Enum@WOPSCommandCode@2@XZ ; mu2::WOPSCommand::GetCommandCode
  004fa	0f b7 c0	 movzx	 eax, ax
  004fd	48 8b 8c 24 08
	01 00 00	 mov	 rcx, QWORD PTR tv217[rsp]
  00505	48 89 4c 24 40	 mov	 QWORD PTR [rsp+64], rcx
  0050a	89 44 24 38	 mov	 DWORD PTR [rsp+56], eax
  0050e	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EM@OPKCLJNH@WOPSGoldenTimeGiftEvent?3?3isVali@
  00515	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  0051a	c7 44 24 28 43
	00 00 00	 mov	 DWORD PTR [rsp+40], 67	; 00000043H
  00522	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0GB@PMIBDBHN@F?3?2Release_Branch?2Server?2Develo@
  00529	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  0052e	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CG@IJGMIACC@mu2?3?3WOPSCommandGoldenTimeGift?3@
  00535	41 b8 30 75 00
	00		 mov	 r8d, 30000		; 00007530H
  0053b	ba 19 00 00 00	 mov	 edx, 25
  00540	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  00547	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging

; 68   : 		return false;

  0054c	32 c0		 xor	 al, al
  0054e	e9 92 01 00 00	 jmp	 $LN1@parse
$LN8@parse:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3453 :         return _Mypair._Myval2;

  00553	48 8b 84 24 b0
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0055b	48 8b 40 40	 mov	 rax, QWORD PTR [rax+64]
  0055f	48 89 84 24 10
	01 00 00	 mov	 QWORD PTR $T19[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2494 :         return _Mypair._Myval2._Mysize == 0;

  00567	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR $T19[rsp]
  0056f	48 83 78 50 00	 cmp	 QWORD PTR [rax+80], 0
  00574	75 0a		 jne	 SHORT $LN313@parse
  00576	c7 44 24 7c 01
	00 00 00	 mov	 DWORD PTR tv432[rsp], 1
  0057e	eb 08		 jmp	 SHORT $LN314@parse
$LN313@parse:
  00580	c7 44 24 7c 00
	00 00 00	 mov	 DWORD PTR tv432[rsp], 0
$LN314@parse:
  00588	0f b6 44 24 7c	 movzx	 eax, BYTE PTR tv432[rsp]
  0058d	88 44 24 63	 mov	 BYTE PTR $T4[rsp], al
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeGiftEvent.cpp

; 71   : 	if (m_impl->subject.empty())

  00591	0f b6 44 24 63	 movzx	 eax, BYTE PTR $T4[rsp]
  00596	0f b6 c0	 movzx	 eax, al
  00599	85 c0		 test	 eax, eax
  0059b	74 7b		 je	 SHORT $LN9@parse

; 72   : 	{
; 73   : 		MU2_WARN_LOG(LogCategory::CONTENTS, "WOPSGoldenTimeGiftEvent::isValid > subject info is empty. Type(%u), id(%llu)", GetCommandCode(), GetCommandId());

  0059d	48 8b 8c 24 b0
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  005a5	e8 00 00 00 00	 call	 ?GetCommandId@WOPSCommand@mu2@@QEBA_KXZ ; mu2::WOPSCommand::GetCommandId
  005aa	48 89 84 24 18
	01 00 00	 mov	 QWORD PTR tv247[rsp], rax
  005b2	48 8b 8c 24 b0
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  005ba	e8 00 00 00 00	 call	 ?GetCommandCode@WOPSCommand@mu2@@QEBA?AW4Enum@WOPSCommandCode@2@XZ ; mu2::WOPSCommand::GetCommandCode
  005bf	0f b7 c0	 movzx	 eax, ax
  005c2	48 8b 8c 24 18
	01 00 00	 mov	 rcx, QWORD PTR tv247[rsp]
  005ca	48 89 4c 24 40	 mov	 QWORD PTR [rsp+64], rcx
  005cf	89 44 24 38	 mov	 DWORD PTR [rsp+56], eax
  005d3	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EN@BIINNPBA@WOPSGoldenTimeGiftEvent?3?3isVali@
  005da	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  005df	c7 44 24 28 49
	00 00 00	 mov	 DWORD PTR [rsp+40], 73	; 00000049H
  005e7	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0GB@PMIBDBHN@F?3?2Release_Branch?2Server?2Develo@
  005ee	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  005f3	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CG@IJGMIACC@mu2?3?3WOPSCommandGoldenTimeGift?3@
  005fa	41 b8 30 75 00
	00		 mov	 r8d, 30000		; 00007530H
  00600	ba 19 00 00 00	 mov	 edx, 25
  00605	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  0060c	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging

; 74   : 		return false;

  00611	32 c0		 xor	 al, al
  00613	e9 cd 00 00 00	 jmp	 $LN1@parse
$LN9@parse:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3453 :         return _Mypair._Myval2;

  00618	48 8b 84 24 b0
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00620	48 8b 40 40	 mov	 rax, QWORD PTR [rax+64]
  00624	48 89 84 24 20
	01 00 00	 mov	 QWORD PTR $T20[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2494 :         return _Mypair._Myval2._Mysize == 0;

  0062c	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR $T20[rsp]
  00634	48 83 78 70 00	 cmp	 QWORD PTR [rax+112], 0
  00639	75 0d		 jne	 SHORT $LN325@parse
  0063b	c7 84 24 80 00
	00 00 01 00 00
	00		 mov	 DWORD PTR tv438[rsp], 1
  00646	eb 0b		 jmp	 SHORT $LN326@parse
$LN325@parse:
  00648	c7 84 24 80 00
	00 00 00 00 00
	00		 mov	 DWORD PTR tv438[rsp], 0
$LN326@parse:
  00653	0f b6 84 24 80
	00 00 00	 movzx	 eax, BYTE PTR tv438[rsp]
  0065b	88 44 24 64	 mov	 BYTE PTR $T5[rsp], al
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeGiftEvent.cpp

; 77   : 	if (m_impl->content.empty())

  0065f	0f b6 44 24 64	 movzx	 eax, BYTE PTR $T5[rsp]
  00664	0f b6 c0	 movzx	 eax, al
  00667	85 c0		 test	 eax, eax
  00669	74 78		 je	 SHORT $LN10@parse

; 78   : 	{
; 79   : 		MU2_WARN_LOG(LogCategory::CONTENTS, "WOPSGoldenTimeGiftEvent::isValid > content info is empty. Type(%u), id(%llu)", GetCommandCode(), GetCommandId());

  0066b	48 8b 8c 24 b0
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00673	e8 00 00 00 00	 call	 ?GetCommandId@WOPSCommand@mu2@@QEBA_KXZ ; mu2::WOPSCommand::GetCommandId
  00678	48 89 84 24 28
	01 00 00	 mov	 QWORD PTR tv277[rsp], rax
  00680	48 8b 8c 24 b0
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00688	e8 00 00 00 00	 call	 ?GetCommandCode@WOPSCommand@mu2@@QEBA?AW4Enum@WOPSCommandCode@2@XZ ; mu2::WOPSCommand::GetCommandCode
  0068d	0f b7 c0	 movzx	 eax, ax
  00690	48 8b 8c 24 28
	01 00 00	 mov	 rcx, QWORD PTR tv277[rsp]
  00698	48 89 4c 24 40	 mov	 QWORD PTR [rsp+64], rcx
  0069d	89 44 24 38	 mov	 DWORD PTR [rsp+56], eax
  006a1	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EN@LJLBNMLN@WOPSGoldenTimeGiftEvent?3?3isVali@
  006a8	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  006ad	c7 44 24 28 4f
	00 00 00	 mov	 DWORD PTR [rsp+40], 79	; 0000004fH
  006b5	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0GB@PMIBDBHN@F?3?2Release_Branch?2Server?2Develo@
  006bc	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  006c1	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CG@IJGMIACC@mu2?3?3WOPSCommandGoldenTimeGift?3@
  006c8	41 b8 30 75 00
	00		 mov	 r8d, 30000		; 00007530H
  006ce	ba 19 00 00 00	 mov	 edx, 25
  006d3	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  006da	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging

; 80   : 		return false;

  006df	32 c0		 xor	 al, al
  006e1	eb 02		 jmp	 SHORT $LN1@parse
$LN10@parse:

; 81   : 	}	
; 82   : 
; 83   : 	return true;

  006e3	b0 01		 mov	 al, 1
$LN1@parse:

; 84   : }

  006e5	48 8b 8c 24 98
	01 00 00	 mov	 rcx, QWORD PTR __$ArrayPad$[rsp]
  006ed	48 33 cc	 xor	 rcx, rsp
  006f0	e8 00 00 00 00	 call	 __security_check_cookie
  006f5	48 81 c4 a8 01
	00 00		 add	 rsp, 424		; 000001a8H
  006fc	c3		 ret	 0
?parse@WOPSCommandGoldenTimeGift@mu2@@MEAA_NAEBUstWOPSCommand@2@@Z ENDP ; mu2::WOPSCommandGoldenTimeGift::parse
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 96
$T2 = 97
$T3 = 98
$T4 = 99
$T5 = 100
$T6 = 104
i$7 = 108
tv330 = 112
tv366 = 116
tv383 = 120
tv432 = 124
tv438 = 128
info$8 = 136
_My_data$9 = 152
hConsole$10 = 160
_My_data$11 = 168
$T12 = 176
$T13 = 184
$T14 = 192
tv296 = 200
$T15 = 208
tv298 = 216
$T16 = 224
tv300 = 232
$T17 = 240
tv187 = 248
$T18 = 256
tv217 = 264
$T19 = 272
tv247 = 280
$T20 = 288
tv277 = 296
$T21 = 304
$T22 = 312
$T23 = 344
$T24 = 376
__$ArrayPad$ = 408
this$ = 432
command$ = 440
?dtor$0@?0??parse@WOPSCommandGoldenTimeGift@mu2@@MEAA_NAEBUstWOPSCommand@2@@Z@4HA PROC ; `mu2::WOPSCommandGoldenTimeGift::parse'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8d 8d 88 00
	00 00		 lea	 rcx, QWORD PTR info$8[rbp]
  00010	e8 00 00 00 00	 call	 ??1ItemMakingInfo@mu2@@UEAA@XZ
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$0@?0??parse@WOPSCommandGoldenTimeGift@mu2@@MEAA_NAEBUstWOPSCommand@2@@Z@4HA ENDP ; `mu2::WOPSCommandGoldenTimeGift::parse'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeGiftEvent.cpp
;	COMDAT ?OnEnter@WOPSCommandGoldenTimeGift@mu2@@UEBAXPEAVEntityPlayer@2@@Z
_TEXT	SEGMENT
this$ = 8
player$ = 16
?OnEnter@WOPSCommandGoldenTimeGift@mu2@@UEBAXPEAVEntityPlayer@2@@Z PROC ; mu2::WOPSCommandGoldenTimeGift::OnEnter, COMDAT

; 31   : {

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 32   : 	UNREFERENCED_PARAMETER( player );
; 33   : }

  0000a	c3		 ret	 0
?OnEnter@WOPSCommandGoldenTimeGift@mu2@@UEBAXPEAVEntityPlayer@2@@Z ENDP ; mu2::WOPSCommandGoldenTimeGift::OnEnter
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeGiftEvent.cpp
;	COMDAT ??1WOPSCommandGoldenTimeGift@mu2@@UEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1WOPSCommandGoldenTimeGift@mu2@@UEAA@XZ PROC		; mu2::WOPSCommandGoldenTimeGift::~WOPSCommandGoldenTimeGift, COMDAT

; 27   : {

$LN327:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7WOPSCommandGoldenTimeGift@mu2@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx

; 28   : }

  00018	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 83 c0 40	 add	 rax, 64			; 00000040H
  00021	48 8b c8	 mov	 rcx, rax
  00024	e8 00 00 00 00	 call	 ??1?$unique_ptr@UWOPSGoldenTimeGiftEventImpl@mu2@@U?$default_delete@UWOPSGoldenTimeGiftEventImpl@mu2@@@std@@@std@@QEAA@XZ ; std::unique_ptr<mu2::WOPSGoldenTimeGiftEventImpl,std::default_delete<mu2::WOPSGoldenTimeGiftEventImpl> >::~unique_ptr<mu2::WOPSGoldenTimeGiftEventImpl,std::default_delete<mu2::WOPSGoldenTimeGiftEventImpl> >
  00029	90		 npad	 1
  0002a	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0002e	c3		 ret	 0
??1WOPSCommandGoldenTimeGift@mu2@@UEAA@XZ ENDP		; mu2::WOPSCommandGoldenTimeGift::~WOPSCommandGoldenTimeGift
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeGiftEvent.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeGiftEvent.cpp
;	COMDAT ??0WOPSCommandGoldenTimeGift@mu2@@QEAA@XZ
_TEXT	SEGMENT
$T1 = 32
tv95 = 40
$T2 = 48
this$ = 56
_Ptr$ = 64
this$ = 72
$T3 = 80
this$ = 112
??0WOPSCommandGoldenTimeGift@mu2@@QEAA@XZ PROC		; mu2::WOPSCommandGoldenTimeGift::WOPSCommandGoldenTimeGift, COMDAT

; 23   : {

$LN103:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 68	 sub	 rsp, 104		; 00000068H

; 21   : 	: WOPSCommand( ACTIONCODE )

  00009	33 d2		 xor	 edx, edx
  0000b	48 8b 4c 24 70	 mov	 rcx, QWORD PTR this$[rsp]
  00010	e8 00 00 00 00	 call	 ??0WOPSCommand@mu2@@IEAA@W4Enum@WOPSCommandCode@1@@Z ; mu2::WOPSCommand::WOPSCommand
  00015	90		 npad	 1

; 23   : {

  00016	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0001b	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7WOPSCommandGoldenTimeGift@mu2@@6B@
  00022	48 89 08	 mov	 QWORD PTR [rax], rcx

; 22   : 	, m_impl( new WOPSGoldenTimeGiftEventImpl )

  00025	b9 98 00 00 00	 mov	 ecx, 152		; 00000098H
  0002a	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  0002f	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
  00034	48 83 7c 24 20
	00		 cmp	 QWORD PTR $T1[rsp], 0
  0003a	74 11		 je	 SHORT $LN3@WOPSComman
  0003c	48 8b 4c 24 20	 mov	 rcx, QWORD PTR $T1[rsp]
  00041	e8 00 00 00 00	 call	 ??0WOPSGoldenTimeGiftEventImpl@mu2@@QEAA@XZ
  00046	48 89 44 24 28	 mov	 QWORD PTR tv95[rsp], rax
  0004b	eb 09		 jmp	 SHORT $LN4@WOPSComman
$LN3@WOPSComman:
  0004d	48 c7 44 24 28
	00 00 00 00	 mov	 QWORD PTR tv95[rsp], 0
$LN4@WOPSComman:
  00056	48 8b 44 24 28	 mov	 rax, QWORD PTR tv95[rsp]
  0005b	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
  00060	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
  00065	48 89 44 24 40	 mov	 QWORD PTR _Ptr$[rsp], rax
  0006a	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0006f	48 83 c0 40	 add	 rax, 64			; 00000040H
  00073	48 89 44 24 38	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 3370 :     _CONSTEXPR23 explicit unique_ptr(pointer _Ptr) noexcept : _Mypair(_Zero_then_variadic_args_t{}, _Ptr) {}

  00078	48 8b 44 24 38	 mov	 rax, QWORD PTR this$[rsp]
  0007d	48 89 44 24 48	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00082	48 8d 44 24 40	 lea	 rax, QWORD PTR _Ptr$[rsp]
  00087	48 89 44 24 50	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1531 :         : _Ty1(), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  0008c	48 8b 44 24 48	 mov	 rax, QWORD PTR this$[rsp]
  00091	48 8b 4c 24 50	 mov	 rcx, QWORD PTR $T3[rsp]
  00096	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00099	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeGiftEvent.cpp

; 24   : }

  0009c	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  000a1	48 83 c4 68	 add	 rsp, 104		; 00000068H
  000a5	c3		 ret	 0
??0WOPSCommandGoldenTimeGift@mu2@@QEAA@XZ ENDP		; mu2::WOPSCommandGoldenTimeGift::WOPSCommandGoldenTimeGift
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
tv95 = 40
$T2 = 48
this$ = 56
_Ptr$ = 64
this$ = 72
$T3 = 80
this$ = 112
?dtor$0@?0???0WOPSCommandGoldenTimeGift@mu2@@QEAA@XZ@4HA PROC ; `mu2::WOPSCommandGoldenTimeGift::WOPSCommandGoldenTimeGift'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 70	 mov	 rcx, QWORD PTR this$[rbp]
  0000d	e8 00 00 00 00	 call	 ??1WOPSCommand@mu2@@UEAA@XZ ; mu2::WOPSCommand::~WOPSCommand
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$0@?0???0WOPSCommandGoldenTimeGift@mu2@@QEAA@XZ@4HA ENDP ; `mu2::WOPSCommandGoldenTimeGift::WOPSCommandGoldenTimeGift'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
tv95 = 40
$T2 = 48
this$ = 56
_Ptr$ = 64
this$ = 72
$T3 = 80
this$ = 112
?dtor$1@?0???0WOPSCommandGoldenTimeGift@mu2@@QEAA@XZ@4HA PROC ; `mu2::WOPSCommandGoldenTimeGift::WOPSCommandGoldenTimeGift'::`1'::dtor$1
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	ba 98 00 00 00	 mov	 edx, 152		; 00000098H
  0000e	48 8b 4d 20	 mov	 rcx, QWORD PTR $T1[rbp]
  00012	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00017	48 83 c4 20	 add	 rsp, 32			; 00000020H
  0001b	5d		 pop	 rbp
  0001c	c3		 ret	 0
?dtor$1@?0???0WOPSCommandGoldenTimeGift@mu2@@QEAA@XZ@4HA ENDP ; `mu2::WOPSCommandGoldenTimeGift::WOPSCommandGoldenTimeGift'::`1'::dtor$1
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
_TEXT	SEGMENT
_Ptr_container$ = 48
_Block_size$ = 56
_Ptr$ = 64
$T1 = 72
_Bytes$ = 96
??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z PROC ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>, COMDAT

; 182  : __declspec(allocator) void* _Allocate_manually_vector_aligned(const size_t _Bytes) {

$LN7:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 183  :     // allocate _Bytes manually aligned to at least _Big_allocation_alignment
; 184  :     const size_t _Block_size = _Non_user_size + _Bytes;

  00009	48 8b 44 24 60	 mov	 rax, QWORD PTR _Bytes$[rsp]
  0000e	48 83 c0 27	 add	 rax, 39			; 00000027H
  00012	48 89 44 24 38	 mov	 QWORD PTR _Block_size$[rsp], rax

; 185  :     if (_Block_size <= _Bytes) {

  00017	48 8b 44 24 60	 mov	 rax, QWORD PTR _Bytes$[rsp]
  0001c	48 39 44 24 38	 cmp	 QWORD PTR _Block_size$[rsp], rax
  00021	77 06		 ja	 SHORT $LN2@Allocate_m

; 186  :         _Throw_bad_array_new_length(); // add overflow

  00023	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00028	90		 npad	 1
$LN2@Allocate_m:

; 136  :         return ::operator new(_Bytes);

  00029	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Block_size$[rsp]
  0002e	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00033	48 89 44 24 48	 mov	 QWORD PTR $T1[rsp], rax

; 187  :     }
; 188  : 
; 189  :     const uintptr_t _Ptr_container = reinterpret_cast<uintptr_t>(_Traits::_Allocate(_Block_size));

  00038	48 8b 44 24 48	 mov	 rax, QWORD PTR $T1[rsp]
  0003d	48 89 44 24 30	 mov	 QWORD PTR _Ptr_container$[rsp], rax

; 190  :     _STL_VERIFY(_Ptr_container != 0, "invalid argument"); // validate even in release since we're doing p[-1]

  00042	48 83 7c 24 30
	00		 cmp	 QWORD PTR _Ptr_container$[rsp], 0
  00048	75 19		 jne	 SHORT $LN3@Allocate_m
  0004a	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR [rsp+32], 0
  00053	45 33 c9	 xor	 r9d, r9d
  00056	45 33 c0	 xor	 r8d, r8d
  00059	33 d2		 xor	 edx, edx
  0005b	33 c9		 xor	 ecx, ecx
  0005d	e8 00 00 00 00	 call	 _invoke_watson
  00062	90		 npad	 1
$LN3@Allocate_m:

; 191  :     void* const _Ptr = reinterpret_cast<void*>((_Ptr_container + _Non_user_size) & ~(_Big_allocation_alignment - 1));

  00063	48 8b 44 24 30	 mov	 rax, QWORD PTR _Ptr_container$[rsp]
  00068	48 83 c0 27	 add	 rax, 39			; 00000027H
  0006c	48 83 e0 e0	 and	 rax, -32		; ffffffffffffffe0H
  00070	48 89 44 24 40	 mov	 QWORD PTR _Ptr$[rsp], rax

; 192  :     static_cast<uintptr_t*>(_Ptr)[-1] = _Ptr_container;

  00075	b8 08 00 00 00	 mov	 eax, 8
  0007a	48 6b c0 ff	 imul	 rax, rax, -1
  0007e	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  00083	48 8b 54 24 30	 mov	 rdx, QWORD PTR _Ptr_container$[rsp]
  00088	48 89 14 01	 mov	 QWORD PTR [rcx+rax], rdx

; 193  : 
; 194  : #ifdef _DEBUG
; 195  :     static_cast<uintptr_t*>(_Ptr)[-2] = _Big_allocation_sentinel;
; 196  : #endif // defined(_DEBUG)
; 197  :     return _Ptr;

  0008c	48 8b 44 24 40	 mov	 rax, QWORD PTR _Ptr$[rsp]
$LN4@Allocate_m:

; 198  : }

  00091	48 83 c4 58	 add	 rsp, 88			; 00000058H
  00095	c3		 ret	 0
??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ENDP ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??$_Construct@$01PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z
_TEXT	SEGMENT
$T1 = 32
$S27$ = 33
_My_data$ = 40
_New_capacity$ = 48
_Max$ = 56
_Masked$2 = 64
$T3 = 72
$T4 = 80
tv154 = 88
_Fancy_ptr$5 = 96
_New_ptr$ = 104
$T6 = 112
$T7 = 120
_First1$ = 128
$T8 = 136
$T9 = 144
_Al$ = 152
$T10 = 160
$T11 = 168
$T12 = 176
$T13 = 184
$T14 = 192
$T15 = 200
_Ptr$ = 208
$T16 = 216
_First1$ = 224
_Alproxy$ = 232
this$ = 256
_Arg$ = 264
_Count$ = 272
??$_Construct@$01PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<2,wchar_t const *>, COMDAT

; 871  :     _CONSTEXPR20 void _Construct(const _Char_or_ptr _Arg, _CRT_GUARDOVERFLOW const size_type _Count) {

$LN173:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	57		 push	 rdi
  00010	48 81 ec f0 00
	00 00		 sub	 rsp, 240		; 000000f0H

; 872  :         auto& _My_data = _Mypair._Myval2;

  00017	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0001f	48 89 44 24 28	 mov	 QWORD PTR _My_data$[rsp], rax

; 873  :         _STL_INTERNAL_CHECK(!_My_data._Large_mode_engaged());
; 874  : 
; 875  :         if constexpr (_Strat == _Construct_strategy::_From_char) {
; 876  :             _STL_INTERNAL_STATIC_ASSERT(is_same_v<_Char_or_ptr, _Elem>);
; 877  :         } else {
; 878  :             _STL_INTERNAL_STATIC_ASSERT(_Is_elem_cptr<_Char_or_ptr>::value);
; 879  :         }
; 880  : 
; 881  :         if (_Count > max_size()) {

  00024	48 8b 8c 24 00
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0002c	e8 00 00 00 00	 call	 ?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
  00031	48 39 84 24 10
	01 00 00	 cmp	 QWORD PTR _Count$[rsp], rax
  00039	76 06		 jbe	 SHORT $LN2@Construct

; 882  :             _Xlen_string(); // result too long

  0003b	e8 00 00 00 00	 call	 ?_Xlen_string@std@@YAXXZ ; std::_Xlen_string
  00040	90		 npad	 1
$LN2@Construct:

; 3107 :         return _Mypair._Get_first();

  00041	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00049	48 89 44 24 70	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3107 :         return _Mypair._Get_first();

  0004e	48 8b 44 24 70	 mov	 rax, QWORD PTR $T6[rsp]
  00053	48 89 44 24 78	 mov	 QWORD PTR $T7[rsp], rax

; 883  :         }
; 884  : 
; 885  :         auto& _Al       = _Getal();

  00058	48 8b 44 24 78	 mov	 rax, QWORD PTR $T7[rsp]
  0005d	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR _Al$[rsp], rax

; 886  :         auto&& _Alproxy = _GET_PROXY_ALLOCATOR(_Alty, _Al);

  00065	48 8d 44 24 20	 lea	 rax, QWORD PTR $T1[rsp]
  0006a	48 8b f8	 mov	 rdi, rax
  0006d	33 c0		 xor	 eax, eax
  0006f	b9 01 00 00 00	 mov	 ecx, 1
  00074	f3 aa		 rep stosb
  00076	48 8d 44 24 21	 lea	 rax, QWORD PTR $S27$[rsp]
  0007b	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR _Alproxy$[rsp], rax

; 887  :         _Container_proxy_ptr<_Alty> _Proxy(_Alproxy, _My_data);
; 888  : 
; 889  :         if (_Count <= _Small_string_capacity) {

  00083	48 83 bc 24 10
	01 00 00 07	 cmp	 QWORD PTR _Count$[rsp], 7
  0008c	77 52		 ja	 SHORT $LN3@Construct

; 890  :             _My_data._Mysize = _Count;

  0008e	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00093	48 8b 8c 24 10
	01 00 00	 mov	 rcx, QWORD PTR _Count$[rsp]
  0009b	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 891  :             _My_data._Myres  = _Small_string_capacity;

  0009f	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000a4	48 c7 40 18 07
	00 00 00	 mov	 QWORD PTR [rax+24], 7

; 892  : 
; 893  :             if constexpr (_Strat == _Construct_strategy::_From_char) {
; 894  :                 _Traits::assign(_My_data._Bx._Buf, _Count, _Arg);
; 895  :                 _Traits::assign(_My_data._Bx._Buf[_Count], _Elem());
; 896  :             } else if constexpr (_Strat == _Construct_strategy::_From_ptr) {
; 897  :                 _Traits::copy(_My_data._Bx._Buf, _Arg, _Count);
; 898  :                 _Traits::assign(_My_data._Bx._Buf[_Count], _Elem());
; 899  :             } else { // _Strat == _Construct_strategy::_From_string
; 900  : #ifdef _INSERT_STRING_ANNOTATION
; 901  :                 _Traits::copy(_My_data._Bx._Buf, _Arg, _Count + 1);
; 902  : #else // ^^^ _INSERT_STRING_ANNOTATION / !_INSERT_STRING_ANNOTATION vvv
; 903  :                 _Traits::copy(_My_data._Bx._Buf, _Arg, _BUF_SIZE);

  000ac	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000b1	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _First1$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  000b9	b8 08 00 00 00	 mov	 eax, 8
  000be	48 6b c0 02	 imul	 rax, rax, 2
  000c2	4c 8b c0	 mov	 r8, rax
  000c5	48 8b 94 24 08
	01 00 00	 mov	 rdx, QWORD PTR _Arg$[rsp]
  000cd	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR _First1$[rsp]
  000d5	e8 00 00 00 00	 call	 memcpy
  000da	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 908  :             return;

  000db	e9 01 02 00 00	 jmp	 $LN4@Construct
$LN3@Construct:

; 909  :         }
; 910  : 
; 911  :         size_type _New_capacity = _Calculate_growth(_Count, _Small_string_capacity, max_size());

  000e0	48 8b 8c 24 00
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  000e8	e8 00 00 00 00	 call	 ?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
  000ed	48 89 44 24 38	 mov	 QWORD PTR _Max$[rsp], rax

; 2978 :         const size_type _Masked = _Requested | _Alloc_mask;

  000f2	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  000fa	48 83 c8 07	 or	 rax, 7
  000fe	48 89 44 24 40	 mov	 QWORD PTR _Masked$2[rsp], rax

; 2979 :         if (_Masked > _Max) { // the mask overflows, settle for max_size()

  00103	48 8b 44 24 38	 mov	 rax, QWORD PTR _Max$[rsp]
  00108	48 39 44 24 40	 cmp	 QWORD PTR _Masked$2[rsp], rax
  0010d	76 0f		 jbe	 SHORT $LN109@Construct

; 2980 :             return _Max;

  0010f	48 8b 44 24 38	 mov	 rax, QWORD PTR _Max$[rsp]
  00114	48 89 44 24 48	 mov	 QWORD PTR $T3[rsp], rax
  00119	e9 93 00 00 00	 jmp	 $LN108@Construct
$LN109@Construct:

; 2981 :         }
; 2982 : 
; 2983 :         if (_Old > _Max - _Old / 2) { // similarly, geometric overflows

  0011e	33 d2		 xor	 edx, edx
  00120	b8 07 00 00 00	 mov	 eax, 7
  00125	b9 02 00 00 00	 mov	 ecx, 2
  0012a	48 f7 f1	 div	 rcx
  0012d	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Max$[rsp]
  00132	48 2b c8	 sub	 rcx, rax
  00135	48 8b c1	 mov	 rax, rcx
  00138	48 83 f8 07	 cmp	 rax, 7
  0013c	73 0c		 jae	 SHORT $LN110@Construct

; 2984 :             return _Max;

  0013e	48 8b 44 24 38	 mov	 rax, QWORD PTR _Max$[rsp]
  00143	48 89 44 24 48	 mov	 QWORD PTR $T3[rsp], rax
  00148	eb 67		 jmp	 SHORT $LN108@Construct
$LN110@Construct:

; 2987 :         return (_STD max)(_Masked, _Old + _Old / 2);

  0014a	33 d2		 xor	 edx, edx
  0014c	b8 07 00 00 00	 mov	 eax, 7
  00151	b9 02 00 00 00	 mov	 ecx, 2
  00156	48 f7 f1	 div	 rcx
  00159	48 83 c0 07	 add	 rax, 7
  0015d	48 89 44 24 50	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 77   :     return _Left < _Right ? _Right : _Left;

  00162	48 8b 44 24 50	 mov	 rax, QWORD PTR $T4[rsp]
  00167	48 39 44 24 40	 cmp	 QWORD PTR _Masked$2[rsp], rax
  0016c	73 0c		 jae	 SHORT $LN117@Construct
  0016e	48 8d 44 24 50	 lea	 rax, QWORD PTR $T4[rsp]
  00173	48 89 44 24 58	 mov	 QWORD PTR tv154[rsp], rax
  00178	eb 0a		 jmp	 SHORT $LN118@Construct
$LN117@Construct:
  0017a	48 8d 44 24 40	 lea	 rax, QWORD PTR _Masked$2[rsp]
  0017f	48 89 44 24 58	 mov	 QWORD PTR tv154[rsp], rax
$LN118@Construct:
  00184	48 8b 44 24 58	 mov	 rax, QWORD PTR tv154[rsp]
  00189	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T8[rsp], rax
  00191	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T8[rsp]
  00199	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2987 :         return (_STD max)(_Masked, _Old + _Old / 2);

  001a1	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR $T9[rsp]
  001a9	48 8b 00	 mov	 rax, QWORD PTR [rax]
  001ac	48 89 44 24 48	 mov	 QWORD PTR $T3[rsp], rax
$LN108@Construct:

; 909  :         }
; 910  : 
; 911  :         size_type _New_capacity = _Calculate_growth(_Count, _Small_string_capacity, max_size());

  001b1	48 8b 44 24 48	 mov	 rax, QWORD PTR $T3[rsp]
  001b6	48 89 44 24 30	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 825  :         ++_Capacity; // Take null terminator into consideration

  001bb	48 8b 44 24 30	 mov	 rax, QWORD PTR _New_capacity$[rsp]
  001c0	48 ff c0	 inc	 rax
  001c3	48 89 44 24 30	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 826  : 
; 827  :         pointer _Fancy_ptr = nullptr;

  001c8	48 c7 44 24 60
	00 00 00 00	 mov	 QWORD PTR _Fancy_ptr$5[rsp], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 2303 :         return _Al.allocate(_Count);

  001d1	48 8b 54 24 30	 mov	 rdx, QWORD PTR _New_capacity$[rsp]
  001d6	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR _Al$[rsp]
  001de	e8 00 00 00 00	 call	 ?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z ; std::allocator<wchar_t>::allocate
  001e3	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 829  :             _Fancy_ptr = _Allocate_at_least_helper(_Al, _Capacity);

  001eb	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  001f3	48 89 44 24 60	 mov	 QWORD PTR _Fancy_ptr$5[rsp], rax

; 830  :         } else {
; 831  :             _STL_INTERNAL_STATIC_ASSERT(_Policy == _Allocation_policy::_Exactly);
; 832  :             _Fancy_ptr = _Al.allocate(_Capacity);
; 833  :         }
; 834  : 
; 835  : #if _HAS_CXX20
; 836  :         // Start element lifetimes to avoid UB. This is a more general mechanism than _String_val::_Activate_SSO_buffer,
; 837  :         // but likely more impactful to throughput.
; 838  :         if (_STD is_constant_evaluated()) {
; 839  :             _Elem* const _Ptr = _Unfancy(_Fancy_ptr);
; 840  :             for (size_type _Idx = 0; _Idx < _Capacity; ++_Idx) {
; 841  :                 _STD construct_at(_Ptr + _Idx);
; 842  :             }
; 843  :         }
; 844  : #endif // _HAS_CXX20
; 845  :         --_Capacity;

  001f8	48 8b 44 24 30	 mov	 rax, QWORD PTR _New_capacity$[rsp]
  001fd	48 ff c8	 dec	 rax
  00200	48 89 44 24 30	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 846  :         return _Fancy_ptr;

  00205	48 8b 44 24 60	 mov	 rax, QWORD PTR _Fancy_ptr$5[rsp]
  0020a	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax

; 912  :         const pointer _New_ptr  = _Allocate_for_capacity(_Al, _New_capacity); // throws

  00212	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  0021a	48 89 44 24 68	 mov	 QWORD PTR _New_ptr$[rsp], rax

; 913  :         _Construct_in_place(_My_data._Bx._Ptr, _New_ptr);

  0021f	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00224	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0022c	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  00234	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0023c	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T13[rsp]
  00244	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T14[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  0024c	48 8d 44 24 68	 lea	 rax, QWORD PTR _New_ptr$[rsp]
  00251	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T15[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00259	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR $T14[rsp]
  00261	48 8b 8c 24 c8
	00 00 00	 mov	 rcx, QWORD PTR $T15[rsp]
  00269	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0026c	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 915  :         _My_data._Mysize = _Count;

  0026f	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00274	48 8b 8c 24 10
	01 00 00	 mov	 rcx, QWORD PTR _Count$[rsp]
  0027c	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 916  :         _My_data._Myres  = _New_capacity;

  00280	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00285	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _New_capacity$[rsp]
  0028a	48 89 48 18	 mov	 QWORD PTR [rax+24], rcx

; 924  :             _Traits::copy(_Unfancy(_New_ptr), _Arg, _Count + 1);

  0028e	48 8b 44 24 68	 mov	 rax, QWORD PTR _New_ptr$[rsp]
  00293	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  0029b	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR _Ptr$[rsp]
  002a3	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR $T16[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 924  :             _Traits::copy(_Unfancy(_New_ptr), _Arg, _Count + 1);

  002ab	48 8b 84 24 d8
	00 00 00	 mov	 rax, QWORD PTR $T16[rsp]
  002b3	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR _First1$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  002bb	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  002c3	48 8d 44 00 02	 lea	 rax, QWORD PTR [rax+rax+2]
  002c8	4c 8b c0	 mov	 r8, rax
  002cb	48 8b 94 24 08
	01 00 00	 mov	 rdx, QWORD PTR _Arg$[rsp]
  002d3	48 8b 8c 24 e0
	00 00 00	 mov	 rcx, QWORD PTR _First1$[rsp]
  002db	e8 00 00 00 00	 call	 memcpy
  002e0	90		 npad	 1
$LN4@Construct:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 929  :     }

  002e1	48 81 c4 f0 00
	00 00		 add	 rsp, 240		; 000000f0H
  002e8	5f		 pop	 rdi
  002e9	c3		 ret	 0
??$_Construct@$01PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<2,wchar_t const *>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??$_Construct@$00PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z
_TEXT	SEGMENT
$T1 = 32
$S26$ = 33
$T2 = 34
$T3 = 36
_My_data$ = 40
_New_capacity$ = 48
_Max$ = 56
_Masked$4 = 64
$T5 = 72
_New_ptr$ = 80
$T6 = 88
tv170 = 96
_Fancy_ptr$7 = 104
$T8 = 112
$T9 = 120
_First1$ = 128
$T10 = 136
$T11 = 144
_Al$ = 152
$T12 = 160
$T13 = 168
$T14 = 176
$T15 = 184
$T16 = 192
$T17 = 200
_Ptr$ = 208
$T18 = 216
_First1$ = 224
_Ptr$ = 232
$T19 = 240
_Alproxy$ = 248
this$ = 272
_Arg$ = 280
_Count$ = 288
??$_Construct@$00PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<1,wchar_t const *>, COMDAT

; 871  :     _CONSTEXPR20 void _Construct(const _Char_or_ptr _Arg, _CRT_GUARDOVERFLOW const size_type _Count) {

$LN188:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	57		 push	 rdi
  00010	48 81 ec 00 01
	00 00		 sub	 rsp, 256		; 00000100H

; 872  :         auto& _My_data = _Mypair._Myval2;

  00017	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0001f	48 89 44 24 28	 mov	 QWORD PTR _My_data$[rsp], rax

; 873  :         _STL_INTERNAL_CHECK(!_My_data._Large_mode_engaged());
; 874  : 
; 875  :         if constexpr (_Strat == _Construct_strategy::_From_char) {
; 876  :             _STL_INTERNAL_STATIC_ASSERT(is_same_v<_Char_or_ptr, _Elem>);
; 877  :         } else {
; 878  :             _STL_INTERNAL_STATIC_ASSERT(_Is_elem_cptr<_Char_or_ptr>::value);
; 879  :         }
; 880  : 
; 881  :         if (_Count > max_size()) {

  00024	48 8b 8c 24 10
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0002c	e8 00 00 00 00	 call	 ?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
  00031	48 39 84 24 20
	01 00 00	 cmp	 QWORD PTR _Count$[rsp], rax
  00039	76 06		 jbe	 SHORT $LN2@Construct

; 882  :             _Xlen_string(); // result too long

  0003b	e8 00 00 00 00	 call	 ?_Xlen_string@std@@YAXXZ ; std::_Xlen_string
  00040	90		 npad	 1
$LN2@Construct:

; 3107 :         return _Mypair._Get_first();

  00041	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00049	48 89 44 24 70	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3107 :         return _Mypair._Get_first();

  0004e	48 8b 44 24 70	 mov	 rax, QWORD PTR $T8[rsp]
  00053	48 89 44 24 78	 mov	 QWORD PTR $T9[rsp], rax

; 883  :         }
; 884  : 
; 885  :         auto& _Al       = _Getal();

  00058	48 8b 44 24 78	 mov	 rax, QWORD PTR $T9[rsp]
  0005d	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR _Al$[rsp], rax

; 886  :         auto&& _Alproxy = _GET_PROXY_ALLOCATOR(_Alty, _Al);

  00065	48 8d 44 24 20	 lea	 rax, QWORD PTR $T1[rsp]
  0006a	48 8b f8	 mov	 rdi, rax
  0006d	33 c0		 xor	 eax, eax
  0006f	b9 01 00 00 00	 mov	 ecx, 1
  00074	f3 aa		 rep stosb
  00076	48 8d 44 24 21	 lea	 rax, QWORD PTR $S26$[rsp]
  0007b	48 89 84 24 f8
	00 00 00	 mov	 QWORD PTR _Alproxy$[rsp], rax

; 887  :         _Container_proxy_ptr<_Alty> _Proxy(_Alproxy, _My_data);
; 888  : 
; 889  :         if (_Count <= _Small_string_capacity) {

  00083	48 83 bc 24 20
	01 00 00 07	 cmp	 QWORD PTR _Count$[rsp], 7
  0008c	77 71		 ja	 SHORT $LN3@Construct

; 890  :             _My_data._Mysize = _Count;

  0008e	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00093	48 8b 8c 24 20
	01 00 00	 mov	 rcx, QWORD PTR _Count$[rsp]
  0009b	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 891  :             _My_data._Myres  = _Small_string_capacity;

  0009f	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000a4	48 c7 40 18 07
	00 00 00	 mov	 QWORD PTR [rax+24], 7

; 892  : 
; 893  :             if constexpr (_Strat == _Construct_strategy::_From_char) {
; 894  :                 _Traits::assign(_My_data._Bx._Buf, _Count, _Arg);
; 895  :                 _Traits::assign(_My_data._Bx._Buf[_Count], _Elem());
; 896  :             } else if constexpr (_Strat == _Construct_strategy::_From_ptr) {
; 897  :                 _Traits::copy(_My_data._Bx._Buf, _Arg, _Count);

  000ac	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000b1	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _First1$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  000b9	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  000c1	48 03 c0	 add	 rax, rax
  000c4	4c 8b c0	 mov	 r8, rax
  000c7	48 8b 94 24 18
	01 00 00	 mov	 rdx, QWORD PTR _Arg$[rsp]
  000cf	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR _First1$[rsp]
  000d7	e8 00 00 00 00	 call	 memcpy
  000dc	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 898  :                 _Traits::assign(_My_data._Bx._Buf[_Count], _Elem());

  000dd	33 c0		 xor	 eax, eax
  000df	66 89 44 24 22	 mov	 WORD PTR $T2[rsp], ax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 347  :         _Left = _Right;

  000e4	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000e9	48 8b 8c 24 20
	01 00 00	 mov	 rcx, QWORD PTR _Count$[rsp]
  000f1	0f b7 54 24 22	 movzx	 edx, WORD PTR $T2[rsp]
  000f6	66 89 14 48	 mov	 WORD PTR [rax+rcx*2], dx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 908  :             return;

  000fa	e9 3c 02 00 00	 jmp	 $LN4@Construct
$LN3@Construct:

; 909  :         }
; 910  : 
; 911  :         size_type _New_capacity = _Calculate_growth(_Count, _Small_string_capacity, max_size());

  000ff	48 8b 8c 24 10
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00107	e8 00 00 00 00	 call	 ?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
  0010c	48 89 44 24 38	 mov	 QWORD PTR _Max$[rsp], rax

; 2978 :         const size_type _Masked = _Requested | _Alloc_mask;

  00111	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  00119	48 83 c8 07	 or	 rax, 7
  0011d	48 89 44 24 40	 mov	 QWORD PTR _Masked$4[rsp], rax

; 2979 :         if (_Masked > _Max) { // the mask overflows, settle for max_size()

  00122	48 8b 44 24 38	 mov	 rax, QWORD PTR _Max$[rsp]
  00127	48 39 44 24 40	 cmp	 QWORD PTR _Masked$4[rsp], rax
  0012c	76 0f		 jbe	 SHORT $LN114@Construct

; 2980 :             return _Max;

  0012e	48 8b 44 24 38	 mov	 rax, QWORD PTR _Max$[rsp]
  00133	48 89 44 24 48	 mov	 QWORD PTR $T5[rsp], rax
  00138	e9 93 00 00 00	 jmp	 $LN113@Construct
$LN114@Construct:

; 2981 :         }
; 2982 : 
; 2983 :         if (_Old > _Max - _Old / 2) { // similarly, geometric overflows

  0013d	33 d2		 xor	 edx, edx
  0013f	b8 07 00 00 00	 mov	 eax, 7
  00144	b9 02 00 00 00	 mov	 ecx, 2
  00149	48 f7 f1	 div	 rcx
  0014c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Max$[rsp]
  00151	48 2b c8	 sub	 rcx, rax
  00154	48 8b c1	 mov	 rax, rcx
  00157	48 83 f8 07	 cmp	 rax, 7
  0015b	73 0c		 jae	 SHORT $LN115@Construct

; 2984 :             return _Max;

  0015d	48 8b 44 24 38	 mov	 rax, QWORD PTR _Max$[rsp]
  00162	48 89 44 24 48	 mov	 QWORD PTR $T5[rsp], rax
  00167	eb 67		 jmp	 SHORT $LN113@Construct
$LN115@Construct:

; 2987 :         return (_STD max)(_Masked, _Old + _Old / 2);

  00169	33 d2		 xor	 edx, edx
  0016b	b8 07 00 00 00	 mov	 eax, 7
  00170	b9 02 00 00 00	 mov	 ecx, 2
  00175	48 f7 f1	 div	 rcx
  00178	48 83 c0 07	 add	 rax, 7
  0017c	48 89 44 24 58	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 77   :     return _Left < _Right ? _Right : _Left;

  00181	48 8b 44 24 58	 mov	 rax, QWORD PTR $T6[rsp]
  00186	48 39 44 24 40	 cmp	 QWORD PTR _Masked$4[rsp], rax
  0018b	73 0c		 jae	 SHORT $LN122@Construct
  0018d	48 8d 44 24 58	 lea	 rax, QWORD PTR $T6[rsp]
  00192	48 89 44 24 60	 mov	 QWORD PTR tv170[rsp], rax
  00197	eb 0a		 jmp	 SHORT $LN123@Construct
$LN122@Construct:
  00199	48 8d 44 24 40	 lea	 rax, QWORD PTR _Masked$4[rsp]
  0019e	48 89 44 24 60	 mov	 QWORD PTR tv170[rsp], rax
$LN123@Construct:
  001a3	48 8b 44 24 60	 mov	 rax, QWORD PTR tv170[rsp]
  001a8	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
  001b0	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  001b8	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2987 :         return (_STD max)(_Masked, _Old + _Old / 2);

  001c0	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  001c8	48 8b 00	 mov	 rax, QWORD PTR [rax]
  001cb	48 89 44 24 48	 mov	 QWORD PTR $T5[rsp], rax
$LN113@Construct:

; 909  :         }
; 910  : 
; 911  :         size_type _New_capacity = _Calculate_growth(_Count, _Small_string_capacity, max_size());

  001d0	48 8b 44 24 48	 mov	 rax, QWORD PTR $T5[rsp]
  001d5	48 89 44 24 30	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 825  :         ++_Capacity; // Take null terminator into consideration

  001da	48 8b 44 24 30	 mov	 rax, QWORD PTR _New_capacity$[rsp]
  001df	48 ff c0	 inc	 rax
  001e2	48 89 44 24 30	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 826  : 
; 827  :         pointer _Fancy_ptr = nullptr;

  001e7	48 c7 44 24 68
	00 00 00 00	 mov	 QWORD PTR _Fancy_ptr$7[rsp], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 2303 :         return _Al.allocate(_Count);

  001f0	48 8b 54 24 30	 mov	 rdx, QWORD PTR _New_capacity$[rsp]
  001f5	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR _Al$[rsp]
  001fd	e8 00 00 00 00	 call	 ?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z ; std::allocator<wchar_t>::allocate
  00202	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 829  :             _Fancy_ptr = _Allocate_at_least_helper(_Al, _Capacity);

  0020a	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
  00212	48 89 44 24 68	 mov	 QWORD PTR _Fancy_ptr$7[rsp], rax

; 830  :         } else {
; 831  :             _STL_INTERNAL_STATIC_ASSERT(_Policy == _Allocation_policy::_Exactly);
; 832  :             _Fancy_ptr = _Al.allocate(_Capacity);
; 833  :         }
; 834  : 
; 835  : #if _HAS_CXX20
; 836  :         // Start element lifetimes to avoid UB. This is a more general mechanism than _String_val::_Activate_SSO_buffer,
; 837  :         // but likely more impactful to throughput.
; 838  :         if (_STD is_constant_evaluated()) {
; 839  :             _Elem* const _Ptr = _Unfancy(_Fancy_ptr);
; 840  :             for (size_type _Idx = 0; _Idx < _Capacity; ++_Idx) {
; 841  :                 _STD construct_at(_Ptr + _Idx);
; 842  :             }
; 843  :         }
; 844  : #endif // _HAS_CXX20
; 845  :         --_Capacity;

  00217	48 8b 44 24 30	 mov	 rax, QWORD PTR _New_capacity$[rsp]
  0021c	48 ff c8	 dec	 rax
  0021f	48 89 44 24 30	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 846  :         return _Fancy_ptr;

  00224	48 8b 44 24 68	 mov	 rax, QWORD PTR _Fancy_ptr$7[rsp]
  00229	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax

; 912  :         const pointer _New_ptr  = _Allocate_for_capacity(_Al, _New_capacity); // throws

  00231	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T13[rsp]
  00239	48 89 44 24 50	 mov	 QWORD PTR _New_ptr$[rsp], rax

; 913  :         _Construct_in_place(_My_data._Bx._Ptr, _New_ptr);

  0023e	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00243	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T14[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0024b	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR $T14[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  00253	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T15[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0025b	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T15[rsp]
  00263	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T16[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  0026b	48 8d 44 24 50	 lea	 rax, QWORD PTR _New_ptr$[rsp]
  00270	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T17[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00278	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR $T16[rsp]
  00280	48 8b 8c 24 c8
	00 00 00	 mov	 rcx, QWORD PTR $T17[rsp]
  00288	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0028b	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 915  :         _My_data._Mysize = _Count;

  0028e	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00293	48 8b 8c 24 20
	01 00 00	 mov	 rcx, QWORD PTR _Count$[rsp]
  0029b	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 916  :         _My_data._Myres  = _New_capacity;

  0029f	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  002a4	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _New_capacity$[rsp]
  002a9	48 89 48 18	 mov	 QWORD PTR [rax+24], rcx

; 921  :             _Traits::copy(_Unfancy(_New_ptr), _Arg, _Count);

  002ad	48 8b 44 24 50	 mov	 rax, QWORD PTR _New_ptr$[rsp]
  002b2	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  002ba	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR _Ptr$[rsp]
  002c2	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR $T18[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 921  :             _Traits::copy(_Unfancy(_New_ptr), _Arg, _Count);

  002ca	48 8b 84 24 d8
	00 00 00	 mov	 rax, QWORD PTR $T18[rsp]
  002d2	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR _First1$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  002da	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  002e2	48 03 c0	 add	 rax, rax
  002e5	4c 8b c0	 mov	 r8, rax
  002e8	48 8b 94 24 18
	01 00 00	 mov	 rdx, QWORD PTR _Arg$[rsp]
  002f0	48 8b 8c 24 e0
	00 00 00	 mov	 rcx, QWORD PTR _First1$[rsp]
  002f8	e8 00 00 00 00	 call	 memcpy
  002fd	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 922  :             _Traits::assign(_Unfancy(_New_ptr)[_Count], _Elem());

  002fe	33 c0		 xor	 eax, eax
  00300	66 89 44 24 24	 mov	 WORD PTR $T3[rsp], ax
  00305	48 8b 44 24 50	 mov	 rax, QWORD PTR _New_ptr$[rsp]
  0030a	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  00312	48 8b 84 24 e8
	00 00 00	 mov	 rax, QWORD PTR _Ptr$[rsp]
  0031a	48 89 84 24 f0
	00 00 00	 mov	 QWORD PTR $T19[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 347  :         _Left = _Right;

  00322	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR $T19[rsp]
  0032a	48 8b 8c 24 20
	01 00 00	 mov	 rcx, QWORD PTR _Count$[rsp]
  00332	0f b7 54 24 24	 movzx	 edx, WORD PTR $T3[rsp]
  00337	66 89 14 48	 mov	 WORD PTR [rax+rcx*2], dx
$LN4@Construct:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 929  :     }

  0033b	48 81 c4 00 01
	00 00		 add	 rsp, 256		; 00000100H
  00342	5f		 pop	 rdi
  00343	c3		 ret	 0
??$_Construct@$00PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<1,wchar_t const *>
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEvent.h
;	COMDAT ?ToString@WOPSCommand@mu2@@UEBA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ
_TEXT	SEGMENT
$T1 = 32
this$ = 64
__$ReturnUdt$ = 72
?ToString@WOPSCommand@mu2@@UEBA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ PROC ; mu2::WOPSCommand::ToString, COMDAT

; 23   : 	virtual std::wstring	ToString() const { return L""; }		// 

$LN225:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 38	 sub	 rsp, 56			; 00000038H
  0000e	c7 44 24 20 00
	00 00 00	 mov	 DWORD PTR $T1[rsp], 0
  00016	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:??_C@_11LOCGONAA@@
  0001d	48 8b 4c 24 48	 mov	 rcx, QWORD PTR __$ReturnUdt$[rsp]
  00022	e8 00 00 00 00	 call	 ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  00027	8b 44 24 20	 mov	 eax, DWORD PTR $T1[rsp]
  0002b	83 c8 01	 or	 eax, 1
  0002e	89 44 24 20	 mov	 DWORD PTR $T1[rsp], eax
  00032	48 8b 44 24 48	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]
  00037	48 83 c4 38	 add	 rsp, 56			; 00000038H
  0003b	c3		 ret	 0
?ToString@WOPSCommand@mu2@@UEBA?AV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@XZ ENDP ; mu2::WOPSCommand::ToString
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEvent.h
;	COMDAT ?IsOverlapable@WOPSCommand@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsOverlapable@WOPSCommand@mu2@@UEBA_NXZ PROC		; mu2::WOPSCommand::IsOverlapable, COMDAT

; 22   : 	virtual Bool			IsOverlapable() const { return true; }	// 중복가능한 명령어인가?

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	b0 01		 mov	 al, 1
  00007	c3		 ret	 0
?IsOverlapable@WOPSCommand@mu2@@UEBA_NXZ ENDP		; mu2::WOPSCommand::IsOverlapable
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEvent.h
;	COMDAT ?OnEnd@WOPSCommand@mu2@@UEBAXXZ
_TEXT	SEGMENT
this$ = 8
?OnEnd@WOPSCommand@mu2@@UEBAXXZ PROC			; mu2::WOPSCommand::OnEnd, COMDAT

; 20   : 	virtual void		OnEnd() const {};

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?OnEnd@WOPSCommand@mu2@@UEBAXXZ ENDP			; mu2::WOPSCommand::OnEnd
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEvent.h
;	COMDAT ?OnBegin@WOPSCommand@mu2@@UEBAXXZ
_TEXT	SEGMENT
this$ = 8
?OnBegin@WOPSCommand@mu2@@UEBAXXZ PROC			; mu2::WOPSCommand::OnBegin, COMDAT

; 19   : 	virtual void		OnBegin() const {};

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?OnBegin@WOPSCommand@mu2@@UEBAXXZ ENDP			; mu2::WOPSCommand::OnBegin
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSEvent.h
;	COMDAT ??1WOPSCommand@mu2@@UEAA@XZ
_TEXT	SEGMENT
this$ = 8
??1WOPSCommand@mu2@@UEAA@XZ PROC			; mu2::WOPSCommand::~WOPSCommand, COMDAT

; 13   : 	virtual ~WOPSCommand() = default;

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
??1WOPSCommand@mu2@@UEAA@XZ ENDP			; mu2::WOPSCommand::~WOPSCommand
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Mu2Common\Protocol\Common.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Mu2Common\Protocol\Common.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Mu2Common\Protocol\Common.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Mu2Common\Protocol\Common.h
;	COMDAT ?getValue@stWOPSCommand@mu2@@AEBAAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@H@Z
_TEXT	SEGMENT
_My_data$1 = 96
hConsole$2 = 104
$T3 = 112
_My_data$4 = 120
$T5 = 128
this$ = 160
arryIndex$ = 168
?getValue@stWOPSCommand@mu2@@AEBAAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@H@Z PROC ; mu2::stWOPSCommand::getValue, COMDAT

; 196  : 		{

$LN14:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 81 ec 98 00
	00 00		 sub	 rsp, 152		; 00000098H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1914 :         auto& _My_data = _Mypair._Myval2;

  00010	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00018	48 83 c0 38	 add	 rax, 56			; 00000038H
  0001c	48 89 44 24 60	 mov	 QWORD PTR _My_data$1[rsp], rax

; 1915 :         return static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst);

  00021	48 8b 44 24 60	 mov	 rax, QWORD PTR _My_data$1[rsp]
  00026	48 8b 4c 24 60	 mov	 rcx, QWORD PTR _My_data$1[rsp]
  0002b	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0002e	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00032	48 2b c1	 sub	 rax, rcx
  00035	48 c1 f8 05	 sar	 rax, 5
  00039	48 89 44 24 70	 mov	 QWORD PTR $T3[rsp], rax
; File F:\Release_Branch\Server\Development\Mu2Common\Protocol\Common.h

; 197  : 			VERIFY_RETURN(arryIndex < values.size(), InvaldValue);

  0003e	48 63 84 24 a8
	00 00 00	 movsxd	 rax, DWORD PTR arryIndex$[rsp]
  00046	48 8b 4c 24 70	 mov	 rcx, QWORD PTR $T3[rsp]
  0004b	48 3b c1	 cmp	 rax, rcx
  0004e	0f 82 a2 00 00
	00		 jb	 $LN2@getValue
  00054	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  00059	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  0005f	48 89 44 24 68	 mov	 QWORD PTR hConsole$2[rsp], rax
  00064	66 ba 0d 00	 mov	 dx, 13
  00068	48 8b 4c 24 68	 mov	 rcx, QWORD PTR hConsole$2[rsp]
  0006d	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  00073	c7 44 24 50 c5
	00 00 00	 mov	 DWORD PTR [rsp+80], 197	; 000000c5H
  0007b	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EB@JGAFMFJE@F?3?2Release_Branch?2Server?2Develo@
  00082	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  00087	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BK@FMPHEHPK@arryIndex?5?$DM?5values?4size?$CI?$CJ@
  0008e	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  00093	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BN@FHEEOEA@mu2?3?3stWOPSCommand?3?3getValue@
  0009a	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  0009f	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  000a6	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  000ab	c7 44 24 28 c5
	00 00 00	 mov	 DWORD PTR [rsp+40], 197	; 000000c5H
  000b3	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EB@JGAFMFJE@F?3?2Release_Branch?2Server?2Develo@
  000ba	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  000bf	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0BN@FHEEOEA@mu2?3?3stWOPSCommand?3?3getValue@
  000c6	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  000cc	ba 02 00 00 00	 mov	 edx, 2
  000d1	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  000d8	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  000dd	66 ba 07 00	 mov	 dx, 7
  000e1	48 8b 4c 24 68	 mov	 rcx, QWORD PTR hConsole$2[rsp]
  000e6	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000ec	90		 npad	 1
  000ed	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?InvaldValue@stWOPSCommand@mu2@@0V?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@B ; mu2::stWOPSCommand::InvaldValue
  000f4	eb 35		 jmp	 SHORT $LN1@getValue
$LN2@getValue:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1938 :         auto& _My_data = _Mypair._Myval2;

  000f6	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000fe	48 83 c0 38	 add	 rax, 56			; 00000038H
  00102	48 89 44 24 78	 mov	 QWORD PTR _My_data$4[rsp], rax
; File F:\Release_Branch\Server\Development\Mu2Common\Protocol\Common.h

; 198  : 			return values[arryIndex];

  00107	48 63 84 24 a8
	00 00 00	 movsxd	 rax, DWORD PTR arryIndex$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1944 :         return _My_data._Myfirst[_Pos];

  0010f	48 6b c0 20	 imul	 rax, rax, 32		; 00000020H
  00113	48 8b 4c 24 78	 mov	 rcx, QWORD PTR _My_data$4[rsp]
  00118	48 03 01	 add	 rax, QWORD PTR [rcx]
  0011b	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T5[rsp], rax
; File F:\Release_Branch\Server\Development\Mu2Common\Protocol\Common.h

; 198  : 			return values[arryIndex];

  00123	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR $T5[rsp]
$LN1@getValue:

; 199  : 		}

  0012b	48 81 c4 98 00
	00 00		 add	 rsp, 152		; 00000098H
  00132	c3		 ret	 0
?getValue@stWOPSCommand@mu2@@AEBAAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@H@Z ENDP ; mu2::stWOPSCommand::getValue
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ??_GItemMakingInfo@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GItemMakingInfo@mu2@@UEAAPEAXI@Z PROC		; mu2::ItemMakingInfo::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 172  : 	virtual~ISerializer() {}

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx
  0001c	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00020	83 e0 01	 and	 eax, 1
  00023	85 c0		 test	 eax, eax
  00025	74 10		 je	 SHORT $LN2@scalar
  00027	ba 10 00 00 00	 mov	 edx, 16
  0002c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00031	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00036	90		 npad	 1
$LN2@scalar:
  00037	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003c	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00040	c3		 ret	 0
??_GItemMakingInfo@mu2@@UEAAPEAXI@Z ENDP		; mu2::ItemMakingInfo::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ??1ItemMakingInfo@mu2@@UEAA@XZ
_TEXT	SEGMENT
this$ = 8
??1ItemMakingInfo@mu2@@UEAA@XZ PROC			; mu2::ItemMakingInfo::~ItemMakingInfo, COMDAT
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 172  : 	virtual~ISerializer() {}

  00005	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0000a	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  00011	48 89 08	 mov	 QWORD PTR [rax], rcx
  00014	c3		 ret	 0
??1ItemMakingInfo@mu2@@UEAA@XZ ENDP			; mu2::ItemMakingInfo::~ItemMakingInfo
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Shared\Protocol\Common\StructuresItem.h
;	COMDAT ?Reset@ItemMakingInfo@mu2@@UEAAXXZ
_TEXT	SEGMENT
this$ = 8
?Reset@ItemMakingInfo@mu2@@UEAAXXZ PROC			; mu2::ItemMakingInfo::Reset, COMDAT

; 1615 : 	{

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 1616 : 
; 1617 : 	}

  00005	c3		 ret	 0
?Reset@ItemMakingInfo@mu2@@UEAAXXZ ENDP			; mu2::ItemMakingInfo::Reset
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Shared\Protocol\Common\StructuresItem.h
;	COMDAT ?PackUnpack@ItemMakingInfo@mu2@@UEAA_NAEAVPacketStream@2@@Z
_TEXT	SEGMENT
this$ = 48
bs$ = 56
?PackUnpack@ItemMakingInfo@mu2@@UEAA_NAEAVPacketStream@2@@Z PROC ; mu2::ItemMakingInfo::PackUnpack, COMDAT

; 1607 : 	{

$LN77:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 1608 : 		bs.rw( index);

  0000e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00013	48 83 c0 08	 add	 rax, 8
  00017	48 8b d0	 mov	 rdx, rax
  0001a	48 8b 4c 24 38	 mov	 rcx, QWORD PTR bs$[rsp]
  0001f	e8 00 00 00 00	 call	 ??$rw@I@PacketStream@mu2@@QEAA_NAEAI@Z ; mu2::PacketStream::rw<unsigned int>

; 1609 : 		bs.rw( stack);

  00024	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00029	48 83 c0 0c	 add	 rax, 12
  0002d	48 8b d0	 mov	 rdx, rax
  00030	48 8b 4c 24 38	 mov	 rcx, QWORD PTR bs$[rsp]
  00035	e8 00 00 00 00	 call	 ??$rw@I@PacketStream@mu2@@QEAA_NAEAI@Z ; mu2::PacketStream::rw<unsigned int>

; 1610 : 
; 1611 : 		return bs.isValid();

  0003a	48 8b 4c 24 38	 mov	 rcx, QWORD PTR bs$[rsp]
  0003f	e8 00 00 00 00	 call	 ?isValid@PacketStream@mu2@@QEBA_NXZ ; mu2::PacketStream::isValid

; 1612 : 	}

  00044	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00048	c3		 ret	 0
?PackUnpack@ItemMakingInfo@mu2@@UEAA_NAEAVPacketStream@2@@Z ENDP ; mu2::ItemMakingInfo::PackUnpack
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ??_GISerializer@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GISerializer@mu2@@UEAAPEAXI@Z PROC			; mu2::ISerializer::`scalar deleting destructor', COMDAT
$LN10:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 172  : 	virtual~ISerializer() {}

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx
  0001c	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00020	83 e0 01	 and	 eax, 1
  00023	85 c0		 test	 eax, eax
  00025	74 10		 je	 SHORT $LN2@scalar
  00027	ba 08 00 00 00	 mov	 edx, 8
  0002c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00031	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00036	90		 npad	 1
$LN2@scalar:
  00037	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003c	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00040	c3		 ret	 0
??_GISerializer@mu2@@UEAAPEAXI@Z ENDP			; mu2::ISerializer::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ??$swapEndian@I@PacketStream@mu2@@AEAAIAEBI@Z
_TEXT	SEGMENT
dest$ = 0
source$ = 4
k$1 = 8
this$ = 32
u$ = 40
??$swapEndian@I@PacketStream@mu2@@AEAAIAEBI@Z PROC	; mu2::PacketStream::swapEndian<unsigned int>, COMDAT

; 1103 : {

$LN6:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 18	 sub	 rsp, 24

; 1104 : 	static_assert(std::is_fundamental<T>::value || std::is_enum<T>::value, "swapEndian> Type must be fundamental");
; 1105 : 
; 1106 : 	union
; 1107 : 	{
; 1108 : 		T u;
; 1109 : 		unsigned char u8[sizeof(T)];
; 1110 : 	} source, dest;
; 1111 : 
; 1112 : 	source.u = u;

  0000e	48 8b 44 24 28	 mov	 rax, QWORD PTR u$[rsp]
  00013	8b 00		 mov	 eax, DWORD PTR [rax]
  00015	89 44 24 04	 mov	 DWORD PTR source$[rsp], eax

; 1113 : 	dest.u = u;

  00019	48 8b 44 24 28	 mov	 rax, QWORD PTR u$[rsp]
  0001e	8b 00		 mov	 eax, DWORD PTR [rax]
  00020	89 04 24	 mov	 DWORD PTR dest$[rsp], eax

; 1114 : 
; 1115 : 	for (size_t k = 0; k < sizeof(T); k++)

  00023	48 c7 44 24 08
	00 00 00 00	 mov	 QWORD PTR k$1[rsp], 0
  0002c	eb 0d		 jmp	 SHORT $LN4@swapEndian
$LN2@swapEndian:
  0002e	48 8b 44 24 08	 mov	 rax, QWORD PTR k$1[rsp]
  00033	48 ff c0	 inc	 rax
  00036	48 89 44 24 08	 mov	 QWORD PTR k$1[rsp], rax
$LN4@swapEndian:
  0003b	48 83 7c 24 08
	04		 cmp	 QWORD PTR k$1[rsp], 4
  00041	73 19		 jae	 SHORT $LN3@swapEndian

; 1116 : 		dest.u8[k] = source.u8[sizeof(T) - k - 1];

  00043	b8 04 00 00 00	 mov	 eax, 4
  00048	48 2b 44 24 08	 sub	 rax, QWORD PTR k$1[rsp]
  0004d	48 8b 4c 24 08	 mov	 rcx, QWORD PTR k$1[rsp]
  00052	0f b6 44 04 03	 movzx	 eax, BYTE PTR source$[rsp+rax-1]
  00057	88 04 0c	 mov	 BYTE PTR dest$[rsp+rcx], al
  0005a	eb d2		 jmp	 SHORT $LN2@swapEndian
$LN3@swapEndian:

; 1117 : 
; 1118 : 	return dest.u;

  0005c	8b 04 24	 mov	 eax, DWORD PTR dest$[rsp]

; 1119 : }

  0005f	48 83 c4 18	 add	 rsp, 24
  00063	c3		 ret	 0
??$swapEndian@I@PacketStream@mu2@@AEAAIAEBI@Z ENDP	; mu2::PacketStream::swapEndian<unsigned int>
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ?checkingBuffer@PacketStream@mu2@@AEAA_NXZ
_TEXT	SEGMENT
this$ = 48
?checkingBuffer@PacketStream@mu2@@AEAA_NXZ PROC		; mu2::PacketStream::checkingBuffer, COMDAT

; 182  : {

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 183  : 	if (m_data == NULL)

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00013	75 32		 jne	 SHORT $LN2@checkingBu

; 184  : 	{
; 185  : 		if (false == resize(m_initSize))

  00015	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0001d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00022	8b 51 2c	 mov	 edx, DWORD PTR [rcx+44]
  00025	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0002a	ff 50 08	 call	 QWORD PTR [rax+8]
  0002d	0f b6 c0	 movzx	 eax, al
  00030	85 c0		 test	 eax, eax
  00032	75 13		 jne	 SHORT $LN3@checkingBu

; 186  : 		{
; 187  : 			SetError(ErrorNet::ResizeFailed);

  00034	ba 54 00 00 00	 mov	 edx, 84			; 00000054H
  00039	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0003e	e8 00 00 00 00	 call	 ?SetError@PacketStream@mu2@@AEAAXW4Error@ErrorNet@2@@Z ; mu2::PacketStream::SetError

; 188  : 			return false;

  00043	32 c0		 xor	 al, al
  00045	eb 02		 jmp	 SHORT $LN1@checkingBu
$LN3@checkingBu:
$LN2@checkingBu:

; 189  : 		}
; 190  : 	}
; 191  : 
; 192  : 	return true;

  00047	b0 01		 mov	 al, 1
$LN1@checkingBu:

; 193  : }

  00049	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0004d	c3		 ret	 0
?checkingBuffer@PacketStream@mu2@@AEAA_NXZ ENDP		; mu2::PacketStream::checkingBuffer
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ??$rw@I@PacketStream@mu2@@QEAA_NAEAI@Z
_TEXT	SEGMENT
swapped$1 = 32
org$2 = 36
this$ = 64
s$ = 72
??$rw@I@PacketStream@mu2@@QEAA_NAEAI@Z PROC		; mu2::PacketStream::rw<unsigned int>, COMDAT

; 140  : 	DECLARE_PACKUNPACK(UInt32)

$LN47:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 38	 sub	 rsp, 56			; 00000038H
  0000e	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00013	e8 00 00 00 00	 call	 ?isValid@PacketStream@mu2@@QEBA_NXZ ; mu2::PacketStream::isValid
  00018	0f b6 c0	 movzx	 eax, al
  0001b	85 c0		 test	 eax, eax
  0001d	75 07		 jne	 SHORT $LN2@rw
  0001f	32 c0		 xor	 al, al
  00021	e9 bd 00 00 00	 jmp	 $LN1@rw
$LN2@rw:
  00026	0f b6 05 00 00
	00 00		 movzx	 eax, BYTE PTR ?isBigEndian@PacketStream@mu2@@2_NA ; mu2::PacketStream::isBigEndian
  0002d	85 c0		 test	 eax, eax
  0002f	75 3d		 jne	 SHORT $LN3@rw
  00031	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00036	0f b6 40 28	 movzx	 eax, BYTE PTR [rax+40]
  0003a	85 c0		 test	 eax, eax
  0003c	75 18		 jne	 SHORT $LN5@rw
  0003e	41 b8 04 00 00
	00		 mov	 r8d, 4
  00044	48 8b 54 24 48	 mov	 rdx, QWORD PTR s$[rsp]
  00049	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0004e	e8 00 00 00 00	 call	 ?Write@PacketStream@mu2@@QEAAIPEBEI@Z ; mu2::PacketStream::Write
  00053	90		 npad	 1
  00054	eb 16		 jmp	 SHORT $LN6@rw
$LN5@rw:
  00056	41 b8 04 00 00
	00		 mov	 r8d, 4
  0005c	48 8b 54 24 48	 mov	 rdx, QWORD PTR s$[rsp]
  00061	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00066	e8 00 00 00 00	 call	 ?Read@PacketStream@mu2@@QEAAIPEAEI@Z ; mu2::PacketStream::Read
  0006b	90		 npad	 1
$LN6@rw:
  0006c	eb 6b		 jmp	 SHORT $LN4@rw
$LN3@rw:
  0006e	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00073	0f b6 40 28	 movzx	 eax, BYTE PTR [rax+40]
  00077	85 c0		 test	 eax, eax
  00079	75 2b		 jne	 SHORT $LN7@rw
  0007b	48 8b 54 24 48	 mov	 rdx, QWORD PTR s$[rsp]
  00080	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00085	e8 00 00 00 00	 call	 ??$swapEndian@I@PacketStream@mu2@@AEAAIAEBI@Z ; mu2::PacketStream::swapEndian<unsigned int>
  0008a	89 44 24 20	 mov	 DWORD PTR swapped$1[rsp], eax
  0008e	41 b8 04 00 00
	00		 mov	 r8d, 4
  00094	48 8d 54 24 20	 lea	 rdx, QWORD PTR swapped$1[rsp]
  00099	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0009e	e8 00 00 00 00	 call	 ?Write@PacketStream@mu2@@QEAAIPEBEI@Z ; mu2::PacketStream::Write
  000a3	90		 npad	 1
  000a4	eb 33		 jmp	 SHORT $LN8@rw
$LN7@rw:
  000a6	41 b8 04 00 00
	00		 mov	 r8d, 4
  000ac	48 8d 54 24 24	 lea	 rdx, QWORD PTR org$2[rsp]
  000b1	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  000b6	e8 00 00 00 00	 call	 ?Read@PacketStream@mu2@@QEAAIPEAEI@Z ; mu2::PacketStream::Read
  000bb	85 c0		 test	 eax, eax
  000bd	75 04		 jne	 SHORT $LN9@rw
  000bf	32 c0		 xor	 al, al
  000c1	eb 20		 jmp	 SHORT $LN1@rw
$LN9@rw:
  000c3	48 8d 54 24 24	 lea	 rdx, QWORD PTR org$2[rsp]
  000c8	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  000cd	e8 00 00 00 00	 call	 ??$swapEndian@I@PacketStream@mu2@@AEAAIAEBI@Z ; mu2::PacketStream::swapEndian<unsigned int>
  000d2	48 8b 4c 24 48	 mov	 rcx, QWORD PTR s$[rsp]
  000d7	89 01		 mov	 DWORD PTR [rcx], eax
$LN8@rw:
$LN4@rw:
  000d9	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  000de	e8 00 00 00 00	 call	 ?isValid@PacketStream@mu2@@QEBA_NXZ ; mu2::PacketStream::isValid
$LN1@rw:
  000e3	48 83 c4 38	 add	 rsp, 56			; 00000038H
  000e7	c3		 ret	 0
??$rw@I@PacketStream@mu2@@QEAA_NAEAI@Z ENDP		; mu2::PacketStream::rw<unsigned int>
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ?Read@PacketStream@mu2@@QEAAIPEAEI@Z
_TEXT	SEGMENT
cnt$ = 96
hConsole$1 = 104
hConsole$2 = 112
readCnt$3 = 120
this$ = 144
buf$ = 152
count$ = 160
?Read@PacketStream@mu2@@QEAAIPEAEI@Z PROC		; mu2::PacketStream::Read, COMDAT

; 1066 : {

$LN10:
  00000	44 89 44 24 18	 mov	 DWORD PTR [rsp+24], r8d
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H

; 1067 : 	VERIFY_RETURN(count, 0);

  00016	83 bc 24 a0 00
	00 00 00	 cmp	 DWORD PTR count$[rsp], 0
  0001e	0f 85 a0 00 00
	00		 jne	 $LN2@Read
  00024	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  00029	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  0002f	48 89 44 24 68	 mov	 QWORD PTR hConsole$1[rsp], rax
  00034	66 ba 0d 00	 mov	 dx, 13
  00038	48 8b 4c 24 68	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  0003d	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  00043	c7 44 24 50 2b
	04 00 00	 mov	 DWORD PTR [rsp+80], 1067 ; 0000042bH
  0004b	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EJ@IFEHPKPB@F?3?2Release_Branch?2Server?2Develo@
  00052	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  00057	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_05IOMEMJEC@count@
  0005e	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  00063	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@KGAODAAL@mu2?3?3PacketStream?3?3Read@
  0006a	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  0006f	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  00076	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  0007b	c7 44 24 28 2b
	04 00 00	 mov	 DWORD PTR [rsp+40], 1067 ; 0000042bH
  00083	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EJ@IFEHPKPB@F?3?2Release_Branch?2Server?2Develo@
  0008a	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  0008f	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0BI@KGAODAAL@mu2?3?3PacketStream?3?3Read@
  00096	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  0009c	ba 02 00 00 00	 mov	 edx, 2
  000a1	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  000a8	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  000ad	66 ba 07 00	 mov	 dx, 7
  000b1	48 8b 4c 24 68	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  000b6	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000bc	90		 npad	 1
  000bd	33 c0		 xor	 eax, eax
  000bf	e9 8e 01 00 00	 jmp	 $LN1@Read
$LN2@Read:

; 1068 : 	VERIFY_RETURN(buf, 0);

  000c4	48 83 bc 24 98
	00 00 00 00	 cmp	 QWORD PTR buf$[rsp], 0
  000cd	0f 85 a0 00 00
	00		 jne	 $LN3@Read
  000d3	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  000d8	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  000de	48 89 44 24 70	 mov	 QWORD PTR hConsole$2[rsp], rax
  000e3	66 ba 0d 00	 mov	 dx, 13
  000e7	48 8b 4c 24 70	 mov	 rcx, QWORD PTR hConsole$2[rsp]
  000ec	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000f2	c7 44 24 50 2c
	04 00 00	 mov	 DWORD PTR [rsp+80], 1068 ; 0000042cH
  000fa	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EJ@IFEHPKPB@F?3?2Release_Branch?2Server?2Develo@
  00101	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  00106	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_03COAJHJPB@buf@
  0010d	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  00112	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@KGAODAAL@mu2?3?3PacketStream?3?3Read@
  00119	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  0011e	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  00125	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  0012a	c7 44 24 28 2c
	04 00 00	 mov	 DWORD PTR [rsp+40], 1068 ; 0000042cH
  00132	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EJ@IFEHPKPB@F?3?2Release_Branch?2Server?2Develo@
  00139	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  0013e	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0BI@KGAODAAL@mu2?3?3PacketStream?3?3Read@
  00145	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  0014b	ba 02 00 00 00	 mov	 edx, 2
  00150	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  00157	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  0015c	66 ba 07 00	 mov	 dx, 7
  00160	48 8b 4c 24 70	 mov	 rcx, QWORD PTR hConsole$2[rsp]
  00165	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  0016b	90		 npad	 1
  0016c	33 c0		 xor	 eax, eax
  0016e	e9 df 00 00 00	 jmp	 $LN1@Read
$LN3@Read:

; 1069 : 
; 1070 : 	if (m_pos == NULL)

  00173	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0017b	48 83 78 10 00	 cmp	 QWORD PTR [rax+16], 0
  00180	75 07		 jne	 SHORT $LN4@Read

; 1071 : 		return 0;

  00182	33 c0		 xor	 eax, eax
  00184	e9 c9 00 00 00	 jmp	 $LN1@Read
$LN4@Read:

; 1072 : 
; 1073 : 	if( isValid() == false ) 

  00189	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00191	e8 00 00 00 00	 call	 ?isValid@PacketStream@mu2@@QEBA_NXZ ; mu2::PacketStream::isValid
  00196	0f b6 c0	 movzx	 eax, al
  00199	85 c0		 test	 eax, eax
  0019b	75 07		 jne	 SHORT $LN5@Read

; 1074 : 		return 0;

  0019d	33 c0		 xor	 eax, eax
  0019f	e9 ae 00 00 00	 jmp	 $LN1@Read
$LN5@Read:

; 1075 : 
; 1076 : 	UInt32 cnt = count;

  001a4	8b 84 24 a0 00
	00 00		 mov	 eax, DWORD PTR count$[rsp]
  001ab	89 44 24 60	 mov	 DWORD PTR cnt$[rsp], eax

; 1077 : 
; 1078 : 	if (m_pos + cnt > m_end)

  001af	8b 44 24 60	 mov	 eax, DWORD PTR cnt$[rsp]
  001b3	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  001bb	48 03 41 10	 add	 rax, QWORD PTR [rcx+16]
  001bf	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  001c7	48 3b 41 18	 cmp	 rax, QWORD PTR [rcx+24]
  001cb	76 3a		 jbe	 SHORT $LN6@Read

; 1079 : 	{
; 1080 : 		SetError(ErrorNet::NotEnoughReadableSize);

  001cd	ba 61 00 00 00	 mov	 edx, 97			; 00000061H
  001d2	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  001da	e8 00 00 00 00	 call	 ?SetError@PacketStream@mu2@@AEAAXW4Error@ErrorNet@2@@Z ; mu2::PacketStream::SetError

; 1081 : 
; 1082 : 		Int64 readCnt = remains();

  001df	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  001e7	e8 00 00 00 00	 call	 ?remains@PacketStream@mu2@@AEAAIXZ ; mu2::PacketStream::remains
  001ec	8b c0		 mov	 eax, eax
  001ee	48 89 44 24 78	 mov	 QWORD PTR readCnt$3[rsp], rax

; 1083 : 		if ( readCnt <= 0 )

  001f3	48 83 7c 24 78
	00		 cmp	 QWORD PTR readCnt$3[rsp], 0
  001f9	7f 04		 jg	 SHORT $LN7@Read

; 1084 : 			return 0;

  001fb	33 c0		 xor	 eax, eax
  001fd	eb 53		 jmp	 SHORT $LN1@Read
$LN7@Read:

; 1085 : 
; 1086 : 		cnt = static_cast<UInt32>(readCnt);

  001ff	8b 44 24 78	 mov	 eax, DWORD PTR readCnt$3[rsp]
  00203	89 44 24 60	 mov	 DWORD PTR cnt$[rsp], eax
$LN6@Read:

; 1087 : 	}
; 1088 : 
; 1089 : 	if (cnt == 0 )

  00207	83 7c 24 60 00	 cmp	 DWORD PTR cnt$[rsp], 0
  0020c	75 04		 jne	 SHORT $LN8@Read

; 1090 : 		return 0;

  0020e	33 c0		 xor	 eax, eax
  00210	eb 40		 jmp	 SHORT $LN1@Read
$LN8@Read:

; 1091 : 
; 1092 : 	assert (cnt<=count);
; 1093 : 
; 1094 : 	memcpy(buf, m_pos, cnt);

  00212	8b 44 24 60	 mov	 eax, DWORD PTR cnt$[rsp]
  00216	44 8b c0	 mov	 r8d, eax
  00219	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00221	48 8b 50 10	 mov	 rdx, QWORD PTR [rax+16]
  00225	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR buf$[rsp]
  0022d	e8 00 00 00 00	 call	 memcpy

; 1095 : 	
; 1096 : 	m_pos += cnt;

  00232	8b 44 24 60	 mov	 eax, DWORD PTR cnt$[rsp]
  00236	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0023e	48 03 41 10	 add	 rax, QWORD PTR [rcx+16]
  00242	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0024a	48 89 41 10	 mov	 QWORD PTR [rcx+16], rax

; 1097 : 
; 1098 : 	return cnt;

  0024e	8b 44 24 60	 mov	 eax, DWORD PTR cnt$[rsp]
$LN1@Read:

; 1099 : }

  00252	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  00259	c3		 ret	 0
?Read@PacketStream@mu2@@QEAAIPEAEI@Z ENDP		; mu2::PacketStream::Read
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ?Write@PacketStream@mu2@@QEAAIPEBEI@Z
_TEXT	SEGMENT
written$ = 96
hConsole$1 = 104
tv157 = 112
this$ = 144
buf$ = 152
count$ = 160
?Write@PacketStream@mu2@@QEAAIPEBEI@Z PROC		; mu2::PacketStream::Write, COMDAT

; 1045 : {	

$LN10:
  00000	44 89 44 24 18	 mov	 DWORD PTR [rsp+24], r8d
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H

; 1046 : 	VERIFY_RETURN(checkingBuffer(), 0);

  00016	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0001e	e8 00 00 00 00	 call	 ?checkingBuffer@PacketStream@mu2@@AEAA_NXZ ; mu2::PacketStream::checkingBuffer
  00023	0f b6 c0	 movzx	 eax, al
  00026	85 c0		 test	 eax, eax
  00028	0f 85 a0 00 00
	00		 jne	 $LN2@Write
  0002e	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  00033	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  00039	48 89 44 24 68	 mov	 QWORD PTR hConsole$1[rsp], rax
  0003e	66 ba 0d 00	 mov	 dx, 13
  00042	48 8b 4c 24 68	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  00047	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  0004d	c7 44 24 50 16
	04 00 00	 mov	 DWORD PTR [rsp+80], 1046 ; 00000416H
  00055	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EJ@IFEHPKPB@F?3?2Release_Branch?2Server?2Develo@
  0005c	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  00061	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BB@HAIDOJLN@checkingBuffer?$CI?$CJ@
  00068	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  0006d	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BJ@NEGBBJOE@mu2?3?3PacketStream?3?3Write@
  00074	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  00079	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  00080	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  00085	c7 44 24 28 16
	04 00 00	 mov	 DWORD PTR [rsp+40], 1046 ; 00000416H
  0008d	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0EJ@IFEHPKPB@F?3?2Release_Branch?2Server?2Develo@
  00094	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  00099	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0BJ@NEGBBJOE@mu2?3?3PacketStream?3?3Write@
  000a0	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  000a6	ba 02 00 00 00	 mov	 edx, 2
  000ab	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  000b2	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  000b7	66 ba 07 00	 mov	 dx, 7
  000bb	48 8b 4c 24 68	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  000c0	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000c6	90		 npad	 1
  000c7	33 c0		 xor	 eax, eax
  000c9	e9 d4 00 00 00	 jmp	 $LN1@Write
$LN2@Write:

; 1047 : 
; 1048 : 	UInt32 written = count;

  000ce	8b 84 24 a0 00
	00 00		 mov	 eax, DWORD PTR count$[rsp]
  000d5	89 44 24 60	 mov	 DWORD PTR written$[rsp], eax

; 1049 : 
; 1050 : 	if (m_pos + written > m_end)

  000d9	8b 44 24 60	 mov	 eax, DWORD PTR written$[rsp]
  000dd	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  000e5	48 03 41 10	 add	 rax, QWORD PTR [rcx+16]
  000e9	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  000f1	48 3b 41 18	 cmp	 rax, QWORD PTR [rcx+24]
  000f5	76 62		 jbe	 SHORT $LN3@Write

; 1051 : 	{
; 1052 : 		written = static_cast<UInt32>( (m_pos + written) - m_end );

  000f7	8b 44 24 60	 mov	 eax, DWORD PTR written$[rsp]
  000fb	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00103	48 8b 49 10	 mov	 rcx, QWORD PTR [rcx+16]
  00107	48 03 c8	 add	 rcx, rax
  0010a	48 8b c1	 mov	 rax, rcx
  0010d	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00115	48 2b 41 18	 sub	 rax, QWORD PTR [rcx+24]
  00119	89 44 24 60	 mov	 DWORD PTR written$[rsp], eax

; 1053 : 		if (false == resize(m_capacity + written))

  0011d	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00125	8b 40 20	 mov	 eax, DWORD PTR [rax+32]
  00128	03 44 24 60	 add	 eax, DWORD PTR written$[rsp]
  0012c	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00134	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00137	48 89 4c 24 70	 mov	 QWORD PTR tv157[rsp], rcx
  0013c	8b d0		 mov	 edx, eax
  0013e	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00146	48 8b 44 24 70	 mov	 rax, QWORD PTR tv157[rsp]
  0014b	ff 50 08	 call	 QWORD PTR [rax+8]
  0014e	0f b6 c0	 movzx	 eax, al
  00151	85 c0		 test	 eax, eax
  00153	75 04		 jne	 SHORT $LN4@Write

; 1054 : 		{
; 1055 : 			return 0;

  00155	33 c0		 xor	 eax, eax
  00157	eb 49		 jmp	 SHORT $LN1@Write
$LN4@Write:
$LN3@Write:

; 1056 : 		}
; 1057 : 	}
; 1058 : 
; 1059 : 	memcpy(m_pos, buf, count);

  00159	8b 84 24 a0 00
	00 00		 mov	 eax, DWORD PTR count$[rsp]
  00160	44 8b c0	 mov	 r8d, eax
  00163	48 8b 94 24 98
	00 00 00	 mov	 rdx, QWORD PTR buf$[rsp]
  0016b	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00173	48 8b 48 10	 mov	 rcx, QWORD PTR [rax+16]
  00177	e8 00 00 00 00	 call	 memcpy

; 1060 : 	m_pos += count;

  0017c	8b 84 24 a0 00
	00 00		 mov	 eax, DWORD PTR count$[rsp]
  00183	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0018b	48 03 41 10	 add	 rax, QWORD PTR [rcx+16]
  0018f	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00197	48 89 41 10	 mov	 QWORD PTR [rcx+16], rax

; 1061 : 
; 1062 : 	return count;

  0019b	8b 84 24 a0 00
	00 00		 mov	 eax, DWORD PTR count$[rsp]
$LN1@Write:

; 1063 : }

  001a2	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  001a9	c3		 ret	 0
?Write@PacketStream@mu2@@QEAAIPEBEI@Z ENDP		; mu2::PacketStream::Write
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??1?$_Compressed_pair@V?$allocator@_W@std@@V?$_String_val@U?$_Simple_types@_W@std@@@2@$00@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 8
??1?$_Compressed_pair@V?$allocator@_W@std@@V?$_String_val@U?$_Simple_types@_W@std@@@2@$00@std@@QEAA@XZ PROC ; std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>::~_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>, COMDAT
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
??1?$_Compressed_pair@V?$allocator@_W@std@@V?$_String_val@U?$_Simple_types@_W@std@@@2@$00@std@@QEAA@XZ ENDP ; std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>::~_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
_TEXT	SEGMENT
$T1 = 32
$T2 = 34
_My_data$ = 40
tv94 = 48
_Bytes$ = 56
_Ptr$ = 64
$T3 = 72
$T4 = 80
_Capacity$ = 88
_Old_ptr$ = 96
_Al$5 = 104
this$ = 128
?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate, COMDAT

; 3080 :     _CONSTEXPR20 void _Tidy_deallocate() noexcept { // initialize buffer, deallocating any storage

$LN62:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 78	 sub	 rsp, 120		; 00000078H

; 3081 :         auto& _My_data = _Mypair._Myval2;

  00009	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00011	48 89 44 24 28	 mov	 QWORD PTR _My_data$[rsp], rax

; 453  :         return _Myres > _Small_string_capacity;

  00016	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  0001b	48 83 78 18 07	 cmp	 QWORD PTR [rax+24], 7
  00020	76 0a		 jbe	 SHORT $LN12@Tidy_deall
  00022	c7 44 24 30 01
	00 00 00	 mov	 DWORD PTR tv94[rsp], 1
  0002a	eb 08		 jmp	 SHORT $LN13@Tidy_deall
$LN12@Tidy_deall:
  0002c	c7 44 24 30 00
	00 00 00	 mov	 DWORD PTR tv94[rsp], 0
$LN13@Tidy_deall:
  00034	0f b6 44 24 30	 movzx	 eax, BYTE PTR tv94[rsp]
  00039	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al

; 3082 :         _My_data._Orphan_all();
; 3083 :         if (_My_data._Large_mode_engaged()) {

  0003d	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  00042	0f b6 c0	 movzx	 eax, al
  00045	85 c0		 test	 eax, eax
  00047	0f 84 80 00 00
	00		 je	 $LN2@Tidy_deall

; 3107 :         return _Mypair._Get_first();

  0004d	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00055	48 89 44 24 48	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3107 :         return _Mypair._Get_first();

  0005a	48 8b 44 24 48	 mov	 rax, QWORD PTR $T3[rsp]
  0005f	48 89 44 24 50	 mov	 QWORD PTR $T4[rsp], rax

; 3084 :             _ASAN_STRING_REMOVE(*this);
; 3085 :             auto& _Al = _Getal();

  00064	48 8b 44 24 50	 mov	 rax, QWORD PTR $T4[rsp]
  00069	48 89 44 24 68	 mov	 QWORD PTR _Al$5[rsp], rax

; 3086 :             _Deallocate_for_capacity(_Al, _My_data._Bx._Ptr, _My_data._Myres);

  0006e	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00073	48 8b 40 18	 mov	 rax, QWORD PTR [rax+24]
  00077	48 89 44 24 58	 mov	 QWORD PTR _Capacity$[rsp], rax
  0007c	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00081	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00084	48 89 44 24 60	 mov	 QWORD PTR _Old_ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  00089	48 8b 44 24 58	 mov	 rax, QWORD PTR _Capacity$[rsp]
  0008e	48 8d 44 00 02	 lea	 rax, QWORD PTR [rax+rax+2]
  00093	48 89 44 24 38	 mov	 QWORD PTR _Bytes$[rsp], rax
  00098	48 8b 44 24 60	 mov	 rax, QWORD PTR _Old_ptr$[rsp]
  0009d	48 89 44 24 40	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  000a2	48 81 7c 24 38
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  000ab	72 10		 jb	 SHORT $LN38@Tidy_deall

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  000ad	48 8d 54 24 38	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  000b2	48 8d 4c 24 40	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  000b7	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  000bc	90		 npad	 1
$LN38@Tidy_deall:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  000bd	48 8b 54 24 38	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  000c2	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  000c7	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  000cc	90		 npad	 1
$LN2@Tidy_deall:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3090 :         _My_data._Mysize = 0;

  000cd	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000d2	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 3091 :         _My_data._Myres  = _Small_string_capacity;

  000da	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000df	48 c7 40 18 07
	00 00 00	 mov	 QWORD PTR [rax+24], 7

; 3092 :         // the _Traits::assign is last so the codegen doesn't think the char write can alias this
; 3093 :         _Traits::assign(_My_data._Bx._Buf[0], _Elem());

  000e7	33 c0		 xor	 eax, eax
  000e9	66 89 44 24 22	 mov	 WORD PTR $T2[rsp], ax
  000ee	b8 02 00 00 00	 mov	 eax, 2
  000f3	48 6b c0 00	 imul	 rax, rax, 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 347  :         _Left = _Right;

  000f7	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _My_data$[rsp]
  000fc	0f b7 54 24 22	 movzx	 edx, WORD PTR $T2[rsp]
  00101	66 89 14 01	 mov	 WORD PTR [rcx+rax], dx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3094 :     }

  00105	48 83 c4 78	 add	 rsp, 120		; 00000078H
  00109	c3		 ret	 0
?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ
_TEXT	SEGMENT
$T1 = 0
_Alloc_max$ = 8
tv66 = 16
$T2 = 24
$T3 = 32
tv70 = 40
$T4 = 48
$T5 = 56
$T6 = 64
$T7 = 72
_Storage_max$ = 80
$T8 = 88
$T9 = 96
$T10 = 104
$T11 = 112
_Unsigned_max$12 = 120
this$ = 144
?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size, COMDAT

; 2377 :     _NODISCARD _CONSTEXPR20 size_type max_size() const noexcept {

$LN38:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H

; 3111 :         return _Mypair._Get_first();

  0000c	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1543 :         return *this;

  00014	48 89 44 24 30	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3111 :         return _Mypair._Get_first();

  00019	48 8b 44 24 30	 mov	 rax, QWORD PTR $T4[rsp]
  0001e	48 89 44 24 70	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 746  :         return static_cast<size_t>(-1) / sizeof(value_type);

  00023	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  0002d	48 89 44 24 38	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2378 :         const size_type _Alloc_max   = _Alty_traits::max_size(_Getal());

  00032	48 8b 44 24 38	 mov	 rax, QWORD PTR $T5[rsp]
  00037	48 89 44 24 08	 mov	 QWORD PTR _Alloc_max$[rsp], rax

; 2379 :         const size_type _Storage_max = // can always store small string

  0003c	48 c7 04 24 08
	00 00 00	 mov	 QWORD PTR $T1[rsp], 8
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 77   :     return _Left < _Right ? _Right : _Left;

  00044	48 8b 04 24	 mov	 rax, QWORD PTR $T1[rsp]
  00048	48 39 44 24 08	 cmp	 QWORD PTR _Alloc_max$[rsp], rax
  0004d	73 0b		 jae	 SHORT $LN21@max_size
  0004f	48 8d 04 24	 lea	 rax, QWORD PTR $T1[rsp]
  00053	48 89 44 24 10	 mov	 QWORD PTR tv66[rsp], rax
  00058	eb 0a		 jmp	 SHORT $LN22@max_size
$LN21@max_size:
  0005a	48 8d 44 24 08	 lea	 rax, QWORD PTR _Alloc_max$[rsp]
  0005f	48 89 44 24 10	 mov	 QWORD PTR tv66[rsp], rax
$LN22@max_size:
  00064	48 8b 44 24 10	 mov	 rax, QWORD PTR tv66[rsp]
  00069	48 89 44 24 40	 mov	 QWORD PTR $T6[rsp], rax
  0006e	48 8b 44 24 40	 mov	 rax, QWORD PTR $T6[rsp]
  00073	48 89 44 24 48	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2379 :         const size_type _Storage_max = // can always store small string

  00078	48 8b 44 24 48	 mov	 rax, QWORD PTR $T7[rsp]
  0007d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00080	48 89 44 24 50	 mov	 QWORD PTR _Storage_max$[rsp], rax

; 2381 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()),

  00085	48 8b 44 24 50	 mov	 rax, QWORD PTR _Storage_max$[rsp]
  0008a	48 ff c8	 dec	 rax
  0008d	48 89 44 24 18	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 866  :         constexpr auto _Unsigned_max = static_cast<make_unsigned_t<_Ty>>(-1);

  00092	48 c7 44 24 78
	ff ff ff ff	 mov	 QWORD PTR _Unsigned_max$12[rsp], -1

; 867  :         return static_cast<_Ty>(_Unsigned_max >> 1);

  0009b	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  000a5	48 89 44 24 58	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2381 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()),

  000aa	48 8b 44 24 58	 mov	 rax, QWORD PTR $T8[rsp]
  000af	48 89 44 24 20	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 101  :     return _Right < _Left ? _Right : _Left;

  000b4	48 8b 44 24 20	 mov	 rax, QWORD PTR $T3[rsp]
  000b9	48 39 44 24 18	 cmp	 QWORD PTR $T2[rsp], rax
  000be	73 0c		 jae	 SHORT $LN33@max_size
  000c0	48 8d 44 24 18	 lea	 rax, QWORD PTR $T2[rsp]
  000c5	48 89 44 24 28	 mov	 QWORD PTR tv70[rsp], rax
  000ca	eb 0a		 jmp	 SHORT $LN34@max_size
$LN33@max_size:
  000cc	48 8d 44 24 20	 lea	 rax, QWORD PTR $T3[rsp]
  000d1	48 89 44 24 28	 mov	 QWORD PTR tv70[rsp], rax
$LN34@max_size:
  000d6	48 8b 44 24 28	 mov	 rax, QWORD PTR tv70[rsp]
  000db	48 89 44 24 60	 mov	 QWORD PTR $T9[rsp], rax
  000e0	48 8b 44 24 60	 mov	 rax, QWORD PTR $T9[rsp]
  000e5	48 89 44 24 68	 mov	 QWORD PTR $T10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2381 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()),

  000ea	48 8b 44 24 68	 mov	 rax, QWORD PTR $T10[rsp]
  000ef	48 8b 00	 mov	 rax, QWORD PTR [rax]

; 2382 :             _Storage_max - 1 // -1 is for null terminator and/or npos
; 2383 :         );
; 2384 :     }

  000f2	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  000f9	c3		 ret	 0
?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Take_contents@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXAEAV12@@Z
_TEXT	SEGMENT
$T1 = 32
_Right_data$ = 40
$T2 = 48
$T3 = 50
tv81 = 52
_My_data$ = 56
$T4 = 64
$T5 = 72
_Right_data_mem$6 = 80
_My_data_mem$7 = 88
$T8 = 96
$T9 = 104
$T10 = 112
$T11 = 120
_Count$ = 128
_First1$ = 136
this$ = 160
_Right$ = 168
?_Take_contents@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXAEAV12@@Z PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Take_contents, COMDAT

; 1258 :     _CONSTEXPR20 void _Take_contents(basic_string& _Right) noexcept {

$LN93:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 81 ec 98 00
	00 00		 sub	 rsp, 152		; 00000098H

; 1259 :         // assign by stealing _Right's buffer
; 1260 :         // pre: this != &_Right
; 1261 :         // pre: allocator propagation (POCMA) from _Right, if necessary, is complete
; 1262 :         // pre: *this owns no memory, iterators orphaned
; 1263 :         // (note: _Buf/_Ptr/_Mysize/_Myres may be garbage init)
; 1264 :         auto& _My_data    = _Mypair._Myval2;

  00011	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00019	48 89 44 24 38	 mov	 QWORD PTR _My_data$[rsp], rax

; 1265 :         auto& _Right_data = _Right._Mypair._Myval2;

  0001e	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR _Right$[rsp]
  00026	48 89 44 24 28	 mov	 QWORD PTR _Right_data$[rsp], rax

; 1282 :                 const auto _My_data_mem =

  0002b	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00033	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1282 :                 const auto _My_data_mem =

  00038	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  0003d	48 89 44 24 58	 mov	 QWORD PTR _My_data_mem$7[rsp], rax

; 1284 :                 const auto _Right_data_mem =

  00042	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR _Right$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0004a	48 89 44 24 48	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1284 :                 const auto _Right_data_mem =

  0004f	48 8b 44 24 48	 mov	 rax, QWORD PTR $T5[rsp]
  00054	48 89 44 24 50	 mov	 QWORD PTR _Right_data_mem$6[rsp], rax

; 1285 :                     reinterpret_cast<const unsigned char*>(_STD addressof(_Right._Mypair._Myval2)) + _Memcpy_val_offset;
; 1286 :                 _CSTD memcpy(_My_data_mem, _Right_data_mem, _Memcpy_val_size);

  00059	41 b8 20 00 00
	00		 mov	 r8d, 32			; 00000020H
  0005f	48 8b 54 24 50	 mov	 rdx, QWORD PTR _Right_data_mem$6[rsp]
  00064	48 8b 4c 24 58	 mov	 rcx, QWORD PTR _My_data_mem$7[rsp]
  00069	e8 00 00 00 00	 call	 memcpy

; 1287 : 
; 1288 :                 _Right_data._Mysize = 0;

  0006e	48 8b 44 24 28	 mov	 rax, QWORD PTR _Right_data$[rsp]
  00073	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 1289 :                 _Right_data._Myres  = _Small_string_capacity;

  0007b	48 8b 44 24 28	 mov	 rax, QWORD PTR _Right_data$[rsp]
  00080	48 c7 40 18 07
	00 00 00	 mov	 QWORD PTR [rax+24], 7

; 1290 :                 _Right_data._Activate_SSO_buffer();
; 1291 :                 _Traits::assign(_Right_data._Bx._Buf[0], _Elem());

  00088	33 c0		 xor	 eax, eax
  0008a	66 89 44 24 30	 mov	 WORD PTR $T2[rsp], ax
  0008f	b8 02 00 00 00	 mov	 eax, 2
  00094	48 6b c0 00	 imul	 rax, rax, 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 347  :         _Left = _Right;

  00098	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Right_data$[rsp]
  0009d	0f b7 54 24 30	 movzx	 edx, WORD PTR $T2[rsp]
  000a2	66 89 14 01	 mov	 WORD PTR [rcx+rax], dx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1292 :                 return;

  000a6	e9 0e 01 00 00	 jmp	 $LN1@Take_conte

; 453  :         return _Myres > _Small_string_capacity;

  000ab	48 8b 44 24 28	 mov	 rax, QWORD PTR _Right_data$[rsp]
  000b0	48 83 78 18 07	 cmp	 QWORD PTR [rax+24], 7
  000b5	76 0a		 jbe	 SHORT $LN28@Take_conte
  000b7	c7 44 24 34 01
	00 00 00	 mov	 DWORD PTR tv81[rsp], 1
  000bf	eb 08		 jmp	 SHORT $LN29@Take_conte
$LN28@Take_conte:
  000c1	c7 44 24 34 00
	00 00 00	 mov	 DWORD PTR tv81[rsp], 0
$LN29@Take_conte:
  000c9	0f b6 44 24 34	 movzx	 eax, BYTE PTR tv81[rsp]
  000ce	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al

; 1293 :             }
; 1294 :         }
; 1295 : #endif // !defined(_INSERT_STRING_ANNOTATION)
; 1296 : 
; 1297 :         if (_Right_data._Large_mode_engaged()) { // steal buffer

  000d2	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  000d7	0f b6 c0	 movzx	 eax, al
  000da	85 c0		 test	 eax, eax
  000dc	74 3a		 je	 SHORT $LN2@Take_conte

; 1300 :             _Construct_in_place(_My_data._Bx._Ptr, _Right_data._Bx._Ptr);

  000de	48 8b 44 24 38	 mov	 rax, QWORD PTR _My_data$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  000e3	48 89 44 24 60	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  000e8	48 8b 44 24 60	 mov	 rax, QWORD PTR $T8[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  000ed	48 89 44 24 68	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  000f2	48 8b 44 24 68	 mov	 rax, QWORD PTR $T9[rsp]
  000f7	48 89 44 24 70	 mov	 QWORD PTR $T10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1300 :             _Construct_in_place(_My_data._Bx._Ptr, _Right_data._Bx._Ptr);

  000fc	48 8b 44 24 28	 mov	 rax, QWORD PTR _Right_data$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00101	48 89 44 24 78	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00106	48 8b 44 24 70	 mov	 rax, QWORD PTR $T10[rsp]
  0010b	48 8b 4c 24 78	 mov	 rcx, QWORD PTR $T11[rsp]
  00110	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00113	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1302 :         } else { // copy small string buffer

  00116	eb 45		 jmp	 SHORT $LN3@Take_conte
$LN2@Take_conte:

; 1306 :             _Traits::copy(_My_data._Bx._Buf, _Right_data._Bx._Buf, _Right_data._Mysize + 1);

  00118	48 8b 44 24 28	 mov	 rax, QWORD PTR _Right_data$[rsp]
  0011d	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00121	48 ff c0	 inc	 rax
  00124	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _Count$[rsp], rax
  0012c	48 8b 44 24 38	 mov	 rax, QWORD PTR _My_data$[rsp]
  00131	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR _First1$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  00139	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  00141	48 d1 e0	 shl	 rax, 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1306 :             _Traits::copy(_My_data._Bx._Buf, _Right_data._Bx._Buf, _Right_data._Mysize + 1);

  00144	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Right_data$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  00149	4c 8b c0	 mov	 r8, rax
  0014c	48 8b d1	 mov	 rdx, rcx
  0014f	48 8b 8c 24 88
	00 00 00	 mov	 rcx, QWORD PTR _First1$[rsp]
  00157	e8 00 00 00 00	 call	 memcpy
  0015c	90		 npad	 1
$LN3@Take_conte:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1309 :         _My_data._Myres  = _Right_data._Myres;

  0015d	48 8b 44 24 38	 mov	 rax, QWORD PTR _My_data$[rsp]
  00162	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Right_data$[rsp]
  00167	48 8b 49 18	 mov	 rcx, QWORD PTR [rcx+24]
  0016b	48 89 48 18	 mov	 QWORD PTR [rax+24], rcx

; 1310 :         _My_data._Mysize = _Right_data._Mysize;

  0016f	48 8b 44 24 38	 mov	 rax, QWORD PTR _My_data$[rsp]
  00174	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Right_data$[rsp]
  00179	48 8b 49 10	 mov	 rcx, QWORD PTR [rcx+16]
  0017d	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 1311 : 
; 1312 :         _Right_data._Mysize = 0;

  00181	48 8b 44 24 28	 mov	 rax, QWORD PTR _Right_data$[rsp]
  00186	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 1313 :         _Right_data._Myres  = _Small_string_capacity;

  0018e	48 8b 44 24 28	 mov	 rax, QWORD PTR _Right_data$[rsp]
  00193	48 c7 40 18 07
	00 00 00	 mov	 QWORD PTR [rax+24], 7

; 1314 :         _Traits::assign(_Right_data._Bx._Buf[0], _Elem());

  0019b	33 c0		 xor	 eax, eax
  0019d	66 89 44 24 32	 mov	 WORD PTR $T3[rsp], ax
  001a2	b8 02 00 00 00	 mov	 eax, 2
  001a7	48 6b c0 00	 imul	 rax, rax, 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 347  :         _Left = _Right;

  001ab	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Right_data$[rsp]
  001b0	0f b7 54 24 32	 movzx	 edx, WORD PTR $T3[rsp]
  001b5	66 89 14 01	 mov	 WORD PTR [rcx+rax], dx
$LN1@Take_conte:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1315 :     }

  001b9	48 81 c4 98 00
	00 00		 add	 rsp, 152		; 00000098H
  001c0	c3		 ret	 0
?_Take_contents@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXAEAV12@@Z ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Take_contents
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV01@$$QEAV01@@Z
_TEXT	SEGMENT
_Pocma_val$ = 32
$T1 = 40
$T2 = 48
$T3 = 56
$T4 = 64
$T5 = 72
_Al$ = 80
_Right_al$ = 88
this$ = 112
_Right$ = 120
??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV01@$$QEAV01@@Z PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::operator=, COMDAT

; 1225 :         noexcept(_Choose_pocma_v<_Alty> != _Pocma_values::_No_propagate_allocators) {

$LN197:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 68	 sub	 rsp, 104		; 00000068H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0000e	48 8b 44 24 78	 mov	 rax, QWORD PTR _Right$[rsp]
  00013	48 89 44 24 28	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1226 :         if (this == _STD addressof(_Right)) {

  00018	48 8b 44 24 28	 mov	 rax, QWORD PTR $T1[rsp]
  0001d	48 39 44 24 70	 cmp	 QWORD PTR this$[rsp], rax
  00022	75 07		 jne	 SHORT $LN2@operator

; 1227 :             return *this;

  00024	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  00029	eb 63		 jmp	 SHORT $LN1@operator
$LN2@operator:

; 3107 :         return _Mypair._Get_first();

  0002b	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00030	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3107 :         return _Mypair._Get_first();

  00035	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
  0003a	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax

; 1228 :         }
; 1229 : 
; 1230 :         auto& _Al                 = _Getal();

  0003f	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
  00044	48 89 44 24 50	 mov	 QWORD PTR _Al$[rsp], rax

; 3107 :         return _Mypair._Get_first();

  00049	48 8b 44 24 78	 mov	 rax, QWORD PTR _Right$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  0004e	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3107 :         return _Mypair._Get_first();

  00053	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  00058	48 89 44 24 48	 mov	 QWORD PTR $T5[rsp], rax

; 1231 :         auto& _Right_al           = _Right._Getal();

  0005d	48 8b 44 24 48	 mov	 rax, QWORD PTR $T5[rsp]
  00062	48 89 44 24 58	 mov	 QWORD PTR _Right_al$[rsp], rax

; 1232 :         constexpr auto _Pocma_val = _Choose_pocma_v<_Alty>;

  00067	c7 44 24 20 00
	00 00 00	 mov	 DWORD PTR _Pocma_val$[rsp], 0

; 1233 :         if constexpr (_Pocma_val == _Pocma_values::_Propagate_allocators) {
; 1234 :             if (_Al != _Right_al) {
; 1235 :                 // intentionally slams into noexcept on OOM, TRANSITION, VSO-466800
; 1236 :                 _Mypair._Myval2._Orphan_all();
; 1237 :                 _Mypair._Myval2._Reload_proxy(_GET_PROXY_ALLOCATOR(_Alty, _Al), _GET_PROXY_ALLOCATOR(_Alty, _Right_al));
; 1238 :             }
; 1239 :         } else if constexpr (_Pocma_val == _Pocma_values::_No_propagate_allocators) {
; 1240 :             if (_Al != _Right_al) {
; 1241 :                 assign(_Right._Mypair._Myval2._Myptr(), _Right._Mypair._Myval2._Mysize);
; 1242 :                 return *this;
; 1243 :             }
; 1244 :         }
; 1245 : 
; 1246 :         _Tidy_deallocate();

  0006f	48 8b 4c 24 70	 mov	 rcx, QWORD PTR this$[rsp]
  00074	e8 00 00 00 00	 call	 ?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate
  00079	90		 npad	 1

; 1247 :         _Pocma(_Al, _Right_al);
; 1248 :         _Take_contents(_Right);

  0007a	48 8b 54 24 78	 mov	 rdx, QWORD PTR _Right$[rsp]
  0007f	48 8b 4c 24 70	 mov	 rcx, QWORD PTR this$[rsp]
  00084	e8 00 00 00 00	 call	 ?_Take_contents@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXAEAV12@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Take_contents

; 1249 :         return *this;

  00089	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
$LN1@operator:

; 1250 :     }

  0008e	48 83 c4 68	 add	 rsp, 104		; 00000068H
  00092	c3		 ret	 0
??4?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV01@$$QEAV01@@Z ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::operator=
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
_TEXT	SEGMENT
$T1 = 0
$T2 = 2
_My_data$ = 8
this$ = 32
?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_empty, COMDAT

; 855  :     _CONSTEXPR20 void _Construct_empty() {

$LN18:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi
  00006	48 83 ec 10	 sub	 rsp, 16

; 856  :         auto& _My_data = _Mypair._Myval2;

  0000a	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0000f	48 89 44 24 08	 mov	 QWORD PTR _My_data$[rsp], rax

; 857  :         _My_data._Alloc_proxy(_GET_PROXY_ALLOCATOR(_Alty, _Getal()));

  00014	48 8d 04 24	 lea	 rax, QWORD PTR $T1[rsp]
  00018	48 8b f8	 mov	 rdi, rax
  0001b	33 c0		 xor	 eax, eax
  0001d	b9 01 00 00 00	 mov	 ecx, 1
  00022	f3 aa		 rep stosb

; 858  : 
; 859  :         // initialize basic_string data members
; 860  :         _My_data._Mysize = 0;

  00024	48 8b 44 24 08	 mov	 rax, QWORD PTR _My_data$[rsp]
  00029	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 861  :         _My_data._Myres  = _Small_string_capacity;

  00031	48 8b 44 24 08	 mov	 rax, QWORD PTR _My_data$[rsp]
  00036	48 c7 40 18 07
	00 00 00	 mov	 QWORD PTR [rax+24], 7

; 862  :         _My_data._Activate_SSO_buffer();
; 863  : 
; 864  :         // the _Traits::assign is last so the codegen doesn't think the char write can alias this
; 865  :         _Traits::assign(_My_data._Bx._Buf[0], _Elem());

  0003e	33 c0		 xor	 eax, eax
  00040	66 89 44 24 02	 mov	 WORD PTR $T2[rsp], ax
  00045	b8 02 00 00 00	 mov	 eax, 2
  0004a	48 6b c0 00	 imul	 rax, rax, 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 347  :         _Left = _Right;

  0004e	48 8b 4c 24 08	 mov	 rcx, QWORD PTR _My_data$[rsp]
  00053	0f b7 54 24 02	 movzx	 edx, WORD PTR $T2[rsp]
  00058	66 89 14 01	 mov	 WORD PTR [rcx+rax], dx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 866  :     }

  0005c	48 83 c4 10	 add	 rsp, 16
  00060	5f		 pop	 rdi
  00061	c3		 ret	 0
?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_empty
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z
_TEXT	SEGMENT
this$ = 32
this$ = 40
this$ = 48
$T1 = 56
$T2 = 64
this$ = 96
_Ptr$ = 104
??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >, COMDAT

; 768  :     _CONSTEXPR20 basic_string(_In_z_ const _Elem* const _Ptr) : _Mypair(_Zero_then_variadic_args_t{}) {

$LN221:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 50	 sub	 rsp, 80			; 00000050H
  0000f	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 89 44 24 30	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1531 :         : _Ty1(), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  00019	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001e	48 89 44 24 20	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 402  :     _CONSTEXPR20 _String_val() noexcept : _Bx() {}

  00023	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00028	48 89 44 24 28	 mov	 QWORD PTR this$[rsp], rax

; 493  :         _CONSTEXPR20 _Bxty() noexcept : _Buf() {} // user-provided, for fancy pointers

  0002d	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00032	48 8b 7c 24 28	 mov	 rdi, QWORD PTR this$[rsp]
  00037	33 c0		 xor	 eax, eax
  00039	b9 10 00 00 00	 mov	 ecx, 16
  0003e	f3 aa		 rep stosb

; 517  :     size_type _Mysize = 0; // current length of string (size)

  00040	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00045	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 518  :     size_type _Myres  = 0; // current storage reserved for string (capacity)

  0004d	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00052	48 c7 40 18 00
	00 00 00	 mov	 QWORD PTR [rax+24], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 310  :         return _CSTD wcslen(reinterpret_cast<const wchar_t*>(_First));

  0005a	48 8b 4c 24 68	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  0005f	e8 00 00 00 00	 call	 wcslen
  00064	48 89 44 24 38	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 769  :         _Construct<_Construct_strategy::_From_ptr>(_Ptr, _Convert_size<size_type>(_Traits::length(_Ptr)));

  00069	48 8b 44 24 38	 mov	 rax, QWORD PTR $T1[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1131 :     return static_cast<_Size_type>(_Len);

  0006e	48 89 44 24 40	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 769  :         _Construct<_Construct_strategy::_From_ptr>(_Ptr, _Convert_size<size_type>(_Traits::length(_Ptr)));

  00073	48 8b 44 24 40	 mov	 rax, QWORD PTR $T2[rsp]
  00078	4c 8b c0	 mov	 r8, rax
  0007b	48 8b 54 24 68	 mov	 rdx, QWORD PTR _Ptr$[rsp]
  00080	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  00085	e8 00 00 00 00	 call	 ??$_Construct@$00PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<1,wchar_t const *>
  0008a	90		 npad	 1

; 770  :     }

  0008b	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00090	48 83 c4 50	 add	 rsp, 80			; 00000050H
  00094	5f		 pop	 rdi
  00095	c3		 ret	 0
??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
this$ = 32
this$ = 40
this$ = 48
$T1 = 56
$T2 = 64
this$ = 96
_Ptr$ = 104
?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z@4HA PROC ; `std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 60	 mov	 rcx, QWORD PTR this$[rbp]
  0000d	e8 00 00 00 00	 call	 ??1?$_Compressed_pair@V?$allocator@_W@std@@V?$_String_val@U?$_Simple_types@_W@std@@@2@$00@std@@QEAA@XZ
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@QEB_W@Z@4HA ENDP ; `std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
$T1 = 32
tv152 = 36
this$ = 40
this$ = 48
this$ = 56
_Result$2 = 64
$T3 = 72
this$ = 80
_Ptr$ = 88
$T4 = 96
$T5 = 104
$T6 = 112
this$ = 144
_Right$ = 152
??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >, COMDAT

; 717  :         : _Mypair(_One_then_variadic_args_t{}, _Alty_traits::select_on_container_copy_construction(_Right._Getal())) {

$LN226:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 81 ec 80 00
	00 00		 sub	 rsp, 128		; 00000080H

; 3111 :         return _Mypair._Get_first();

  00012	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR _Right$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1543 :         return *this;

  0001a	48 89 44 24 48	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3111 :         return _Mypair._Get_first();

  0001f	48 8b 44 24 48	 mov	 rax, QWORD PTR $T3[rsp]
  00024	48 89 44 24 70	 mov	 QWORD PTR $T6[rsp], rax

; 717  :         : _Mypair(_One_then_variadic_args_t{}, _Alty_traits::select_on_container_copy_construction(_Right._Getal())) {

  00029	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00031	48 89 44 24 50	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1536 :         : _Ty1(_STD forward<_Other1>(_Val1)), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  00036	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0003b	48 89 44 24 28	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 402  :     _CONSTEXPR20 _String_val() noexcept : _Bx() {}

  00040	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00045	48 89 44 24 38	 mov	 QWORD PTR this$[rsp], rax

; 493  :         _CONSTEXPR20 _Bxty() noexcept : _Buf() {} // user-provided, for fancy pointers

  0004a	48 8b 44 24 38	 mov	 rax, QWORD PTR this$[rsp]
  0004f	48 8b 7c 24 38	 mov	 rdi, QWORD PTR this$[rsp]
  00054	33 c0		 xor	 eax, eax
  00056	b9 10 00 00 00	 mov	 ecx, 16
  0005b	f3 aa		 rep stosb

; 517  :     size_type _Mysize = 0; // current length of string (size)

  0005d	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00062	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 518  :     size_type _Myres  = 0; // current storage reserved for string (capacity)

  0006a	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  0006f	48 c7 40 18 00
	00 00 00	 mov	 QWORD PTR [rax+24], 0

; 718  :         _Construct<_Construct_strategy::_From_string>(_Right._Mypair._Myval2._Myptr(), _Right._Mypair._Myval2._Mysize);

  00077	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR _Right$[rsp]
  0007f	48 89 44 24 30	 mov	 QWORD PTR this$[rsp], rax

; 444  :         const value_type* _Result = _Bx._Buf;

  00084	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00089	48 89 44 24 40	 mov	 QWORD PTR _Result$2[rsp], rax

; 453  :         return _Myres > _Small_string_capacity;

  0008e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00093	48 83 78 18 07	 cmp	 QWORD PTR [rax+24], 7
  00098	76 0a		 jbe	 SHORT $LN44@basic_stri
  0009a	c7 44 24 24 01
	00 00 00	 mov	 DWORD PTR tv152[rsp], 1
  000a2	eb 08		 jmp	 SHORT $LN45@basic_stri
$LN44@basic_stri:
  000a4	c7 44 24 24 00
	00 00 00	 mov	 DWORD PTR tv152[rsp], 0
$LN45@basic_stri:
  000ac	0f b6 44 24 24	 movzx	 eax, BYTE PTR tv152[rsp]
  000b1	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al

; 445  :         if (_Large_mode_engaged()) {

  000b5	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  000ba	0f b6 c0	 movzx	 eax, al
  000bd	85 c0		 test	 eax, eax
  000bf	74 21		 je	 SHORT $LN37@basic_stri

; 446  :             _Result = _Unfancy(_Bx._Ptr);

  000c1	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  000c6	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000c9	48 89 44 24 58	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  000ce	48 8b 44 24 58	 mov	 rax, QWORD PTR _Ptr$[rsp]
  000d3	48 89 44 24 60	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 446  :             _Result = _Unfancy(_Bx._Ptr);

  000d8	48 8b 44 24 60	 mov	 rax, QWORD PTR $T4[rsp]
  000dd	48 89 44 24 40	 mov	 QWORD PTR _Result$2[rsp], rax
$LN37@basic_stri:

; 447  :         }
; 448  : 
; 449  :         return _Result;

  000e2	48 8b 44 24 40	 mov	 rax, QWORD PTR _Result$2[rsp]
  000e7	48 89 44 24 68	 mov	 QWORD PTR $T5[rsp], rax

; 718  :         _Construct<_Construct_strategy::_From_string>(_Right._Mypair._Myval2._Myptr(), _Right._Mypair._Myval2._Mysize);

  000ec	48 8b 44 24 68	 mov	 rax, QWORD PTR $T5[rsp]
  000f1	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR _Right$[rsp]
  000f9	4c 8b 41 10	 mov	 r8, QWORD PTR [rcx+16]
  000fd	48 8b d0	 mov	 rdx, rax
  00100	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00108	e8 00 00 00 00	 call	 ??$_Construct@$01PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<2,wchar_t const *>
  0010d	90		 npad	 1

; 719  :     }

  0010e	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00116	48 81 c4 80 00
	00 00		 add	 rsp, 128		; 00000080H
  0011d	5f		 pop	 rdi
  0011e	c3		 ret	 0
??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
tv152 = 36
this$ = 40
this$ = 48
this$ = 56
_Result$2 = 64
$T3 = 72
this$ = 80
_Ptr$ = 88
$T4 = 96
$T5 = 104
$T6 = 112
this$ = 144
_Right$ = 152
?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z@4HA PROC ; `std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 8d 90 00
	00 00		 mov	 rcx, QWORD PTR this$[rbp]
  00010	e8 00 00 00 00	 call	 ??1?$_Compressed_pair@V?$allocator@_W@std@@V?$_String_val@U?$_Simple_types@_W@std@@@2@$00@std@@QEAA@XZ
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z@4HA ENDP ; `std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 32
this$ = 40
this$ = 48
this$ = 80
??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >, COMDAT

; 708  :     basic_string() noexcept(is_nothrow_default_constructible_v<_Alty>) : _Mypair(_Zero_then_variadic_args_t{}) {

$LN41:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi
  00006	48 83 ec 40	 sub	 rsp, 64			; 00000040H
  0000a	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0000f	48 89 44 24 30	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1531 :         : _Ty1(), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  00014	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00019	48 89 44 24 20	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 402  :     _CONSTEXPR20 _String_val() noexcept : _Bx() {}

  0001e	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 89 44 24 28	 mov	 QWORD PTR this$[rsp], rax

; 493  :         _CONSTEXPR20 _Bxty() noexcept : _Buf() {} // user-provided, for fancy pointers

  00028	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  0002d	48 8b 7c 24 28	 mov	 rdi, QWORD PTR this$[rsp]
  00032	33 c0		 xor	 eax, eax
  00034	b9 10 00 00 00	 mov	 ecx, 16
  00039	f3 aa		 rep stosb

; 517  :     size_type _Mysize = 0; // current length of string (size)

  0003b	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00040	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 518  :     size_type _Myres  = 0; // current storage reserved for string (capacity)

  00048	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0004d	48 c7 40 18 00
	00 00 00	 mov	 QWORD PTR [rax+24], 0

; 709  :         _Construct_empty();

  00055	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  0005a	e8 00 00 00 00	 call	 ?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_empty
  0005f	90		 npad	 1

; 710  :     }

  00060	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00065	48 83 c4 40	 add	 rsp, 64			; 00000040H
  00069	5f		 pop	 rdi
  0006a	c3		 ret	 0
??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEBAPEB_WXZ
_TEXT	SEGMENT
$T1 = 0
tv76 = 4
_Result$ = 8
_Ptr$ = 16
$T2 = 24
this$ = 48
?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEBAPEB_WXZ PROC ; std::_String_val<std::_Simple_types<wchar_t> >::_Myptr, COMDAT

; 443  :     _NODISCARD _CONSTEXPR20 const value_type* _Myptr() const noexcept {

$LN17:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 444  :         const value_type* _Result = _Bx._Buf;

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 89 44 24 08	 mov	 QWORD PTR _Result$[rsp], rax

; 453  :         return _Myres > _Small_string_capacity;

  00013	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00018	48 83 78 18 07	 cmp	 QWORD PTR [rax+24], 7
  0001d	76 0a		 jbe	 SHORT $LN7@Myptr
  0001f	c7 44 24 04 01
	00 00 00	 mov	 DWORD PTR tv76[rsp], 1
  00027	eb 08		 jmp	 SHORT $LN8@Myptr
$LN7@Myptr:
  00029	c7 44 24 04 00
	00 00 00	 mov	 DWORD PTR tv76[rsp], 0
$LN8@Myptr:
  00031	0f b6 44 24 04	 movzx	 eax, BYTE PTR tv76[rsp]
  00036	88 04 24	 mov	 BYTE PTR $T1[rsp], al

; 445  :         if (_Large_mode_engaged()) {

  00039	0f b6 04 24	 movzx	 eax, BYTE PTR $T1[rsp]
  0003d	0f b6 c0	 movzx	 eax, al
  00040	85 c0		 test	 eax, eax
  00042	74 21		 je	 SHORT $LN2@Myptr

; 446  :             _Result = _Unfancy(_Bx._Ptr);

  00044	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00049	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0004c	48 89 44 24 10	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  00051	48 8b 44 24 10	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00056	48 89 44 24 18	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 446  :             _Result = _Unfancy(_Bx._Ptr);

  0005b	48 8b 44 24 18	 mov	 rax, QWORD PTR $T2[rsp]
  00060	48 89 44 24 08	 mov	 QWORD PTR _Result$[rsp], rax
$LN2@Myptr:

; 447  :         }
; 448  : 
; 449  :         return _Result;

  00065	48 8b 44 24 08	 mov	 rax, QWORD PTR _Result$[rsp]

; 450  :     }

  0006a	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0006e	c3		 ret	 0
?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEBAPEB_WXZ ENDP ; std::_String_val<std::_Simple_types<wchar_t> >::_Myptr
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z
_TEXT	SEGMENT
_Overflow_is_possible$1 = 32
_Bytes$ = 40
$T2 = 48
$T3 = 56
$T4 = 64
_Max_possible$5 = 72
this$ = 96
_Count$ = 104
?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z PROC	; std::allocator<wchar_t>::allocate, COMDAT

; 988  :     _NODISCARD_RAW_PTR_ALLOC _CONSTEXPR20 __declspec(allocator) _Ty* allocate(_CRT_GUARDOVERFLOW const size_t _Count) {

$LN13:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 113  :     constexpr bool _Overflow_is_possible = _Ty_size > 1;

  0000e	c6 44 24 20 01	 mov	 BYTE PTR _Overflow_is_possible$1[rsp], 1

; 114  : 
; 115  :     if constexpr (_Overflow_is_possible) {
; 116  :         constexpr size_t _Max_possible = static_cast<size_t>(-1) / _Ty_size;

  00013	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  0001d	48 89 44 24 48	 mov	 QWORD PTR _Max_possible$5[rsp], rax

; 117  :         if (_Count > _Max_possible) {

  00022	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  0002c	48 39 44 24 68	 cmp	 QWORD PTR _Count$[rsp], rax
  00031	76 06		 jbe	 SHORT $LN4@allocate

; 118  :             _Throw_bad_array_new_length(); // multiply overflow

  00033	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00038	90		 npad	 1
$LN4@allocate:

; 119  :         }
; 120  :     }
; 121  : 
; 122  :     return _Count * _Ty_size;

  00039	48 8b 44 24 68	 mov	 rax, QWORD PTR _Count$[rsp]
  0003e	48 d1 e0	 shl	 rax, 1
  00041	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00046	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
  0004b	48 89 44 24 28	 mov	 QWORD PTR _Bytes$[rsp], rax

; 227  :     if (_Bytes == 0) {

  00050	48 83 7c 24 28
	00		 cmp	 QWORD PTR _Bytes$[rsp], 0
  00056	75 0b		 jne	 SHORT $LN8@allocate

; 228  :         return nullptr;

  00058	48 c7 44 24 30
	00 00 00 00	 mov	 QWORD PTR $T2[rsp], 0
  00061	eb 35		 jmp	 SHORT $LN7@allocate
$LN8@allocate:

; 229  :     }
; 230  : 
; 231  : #if _HAS_CXX20 // TRANSITION, GH-1532
; 232  :     if (_STD is_constant_evaluated()) {
; 233  :         return _Traits::_Allocate(_Bytes);
; 234  :     }
; 235  : #endif // _HAS_CXX20
; 236  : 
; 237  : #ifdef __cpp_aligned_new
; 238  :     if constexpr (_Align > __STDCPP_DEFAULT_NEW_ALIGNMENT__) {
; 239  :         size_t _Passed_align = _Align;
; 240  : #if defined(_M_IX86) || defined(_M_X64)
; 241  :         if (_Bytes >= _Big_allocation_threshold) {
; 242  :             // boost the alignment of big allocations to help autovectorization
; 243  :             _Passed_align = (_STD max)(_Align, _Big_allocation_alignment);
; 244  :         }
; 245  : #endif // defined(_M_IX86) || defined(_M_X64)
; 246  :         return _Traits::_Allocate_aligned(_Bytes, _Passed_align);
; 247  :     } else
; 248  : #endif // defined(__cpp_aligned_new)
; 249  :     {
; 250  : #if defined(_M_IX86) || defined(_M_X64)
; 251  :         if (_Bytes >= _Big_allocation_threshold) {

  00063	48 81 7c 24 28
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  0006c	72 11		 jb	 SHORT $LN9@allocate

; 252  :             // boost the alignment of big allocations to help autovectorization
; 253  :             return _Allocate_manually_vector_aligned<_Traits>(_Bytes);

  0006e	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00073	e8 00 00 00 00	 call	 ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
  00078	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
  0007d	eb 19		 jmp	 SHORT $LN7@allocate
$LN9@allocate:

; 136  :         return ::operator new(_Bytes);

  0007f	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00084	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00089	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax

; 256  :         return _Traits::_Allocate(_Bytes);

  0008e	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  00093	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
$LN7@allocate:

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00098	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
$LN6@allocate:

; 991  :     }

  0009d	48 83 c4 58	 add	 rsp, 88			; 00000058H
  000a1	c3		 ret	 0
?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z ENDP	; std::allocator<wchar_t>::allocate
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Xlen_string@std@@YAXXZ
_TEXT	SEGMENT
?_Xlen_string@std@@YAXXZ PROC				; std::_Xlen_string, COMDAT

; 530  : [[noreturn]] inline void _Xlen_string() {

$LN3:
  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 531  :     _Xlength_error("string too long");

  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_0BA@JFNIOLAK@string?5too?5long@
  0000b	e8 00 00 00 00	 call	 ?_Xlength_error@std@@YAXPEBD@Z ; std::_Xlength_error
  00010	90		 npad	 1
$LN2@Xlen_strin:

; 532  : }

  00011	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00015	c3		 ret	 0
?_Xlen_string@std@@YAXXZ ENDP				; std::_Xlen_string
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z
_TEXT	SEGMENT
_Back_shift$ = 48
_Ptr_container$ = 56
_Ptr_user$ = 64
_Min_back_shift$ = 72
_Ptr$ = 96
_Bytes$ = 104
?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z PROC ; std::_Adjust_manually_vector_aligned, COMDAT

; 200  : inline void _Adjust_manually_vector_aligned(void*& _Ptr, size_t& _Bytes) {

$LN5:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 201  :     // adjust parameters from _Allocate_manually_vector_aligned to pass to operator delete
; 202  :     _Bytes += _Non_user_size;

  0000e	48 8b 44 24 68	 mov	 rax, QWORD PTR _Bytes$[rsp]
  00013	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00016	48 83 c0 27	 add	 rax, 39			; 00000027H
  0001a	48 8b 4c 24 68	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  0001f	48 89 01	 mov	 QWORD PTR [rcx], rax

; 203  : 
; 204  :     const uintptr_t* const _Ptr_user = static_cast<uintptr_t*>(_Ptr);

  00022	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00027	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0002a	48 89 44 24 40	 mov	 QWORD PTR _Ptr_user$[rsp], rax

; 205  :     const uintptr_t _Ptr_container   = _Ptr_user[-1];

  0002f	b8 08 00 00 00	 mov	 eax, 8
  00034	48 6b c0 ff	 imul	 rax, rax, -1
  00038	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr_user$[rsp]
  0003d	48 8b 04 01	 mov	 rax, QWORD PTR [rcx+rax]
  00041	48 89 44 24 38	 mov	 QWORD PTR _Ptr_container$[rsp], rax

; 206  : 
; 207  :     // If the following asserts, it likely means that we are performing
; 208  :     // an aligned delete on memory coming from an unaligned allocation.
; 209  :     _STL_ASSERT(_Ptr_user[-2] == _Big_allocation_sentinel, "invalid argument");
; 210  : 
; 211  :     // Extra paranoia on aligned allocation/deallocation; ensure _Ptr_container is
; 212  :     // in range [_Min_back_shift, _Non_user_size]
; 213  : #ifdef _DEBUG
; 214  :     constexpr uintptr_t _Min_back_shift = 2 * sizeof(void*);
; 215  : #else // ^^^ defined(_DEBUG) / !defined(_DEBUG) vvv
; 216  :     constexpr uintptr_t _Min_back_shift = sizeof(void*);

  00046	48 c7 44 24 48
	08 00 00 00	 mov	 QWORD PTR _Min_back_shift$[rsp], 8

; 217  : #endif // ^^^ !defined(_DEBUG) ^^^
; 218  :     const uintptr_t _Back_shift = reinterpret_cast<uintptr_t>(_Ptr) - _Ptr_container;

  0004f	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00054	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr_container$[rsp]
  00059	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0005c	48 2b c1	 sub	 rax, rcx
  0005f	48 89 44 24 30	 mov	 QWORD PTR _Back_shift$[rsp], rax

; 219  :     _STL_VERIFY(_Back_shift >= _Min_back_shift && _Back_shift <= _Non_user_size, "invalid argument");

  00064	48 83 7c 24 30
	08		 cmp	 QWORD PTR _Back_shift$[rsp], 8
  0006a	72 08		 jb	 SHORT $LN3@Adjust_man
  0006c	48 83 7c 24 30
	27		 cmp	 QWORD PTR _Back_shift$[rsp], 39 ; 00000027H
  00072	76 19		 jbe	 SHORT $LN2@Adjust_man
$LN3@Adjust_man:
  00074	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR [rsp+32], 0
  0007d	45 33 c9	 xor	 r9d, r9d
  00080	45 33 c0	 xor	 r8d, r8d
  00083	33 d2		 xor	 edx, edx
  00085	33 c9		 xor	 ecx, ecx
  00087	e8 00 00 00 00	 call	 _invoke_watson
  0008c	90		 npad	 1
$LN2@Adjust_man:

; 220  :     _Ptr = reinterpret_cast<void*>(_Ptr_container);

  0008d	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00092	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr_container$[rsp]
  00097	48 89 08	 mov	 QWORD PTR [rax], rcx
$LN4@Adjust_man:

; 221  : }

  0009a	48 83 c4 58	 add	 rsp, 88			; 00000058H
  0009e	c3		 ret	 0
?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ENDP ; std::_Adjust_manually_vector_aligned
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?_Throw_bad_array_new_length@std@@YAXXZ
_TEXT	SEGMENT
$T1 = 32
?_Throw_bad_array_new_length@std@@YAXXZ PROC		; std::_Throw_bad_array_new_length, COMDAT

; 107  : [[noreturn]] inline void _Throw_bad_array_new_length() {

$LN3:
  00000	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 108  :     _THROW(bad_array_new_length{});

  00004	48 8d 4c 24 20	 lea	 rcx, QWORD PTR $T1[rsp]
  00009	e8 00 00 00 00	 call	 ??0bad_array_new_length@std@@QEAA@XZ ; std::bad_array_new_length::bad_array_new_length
  0000e	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:_TI3?AVbad_array_new_length@std@@
  00015	48 8d 4c 24 20	 lea	 rcx, QWORD PTR $T1[rsp]
  0001a	e8 00 00 00 00	 call	 _CxxThrowException
  0001f	90		 npad	 1
$LN2@Throw_bad_:

; 109  : }

  00020	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00024	c3		 ret	 0
?_Throw_bad_array_new_length@std@@YAXXZ ENDP		; std::_Throw_bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gbad_array_new_length@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gbad_array_new_length@std@@UEAAPEAXI@Z PROC		; std::bad_array_new_length::`scalar deleting destructor', COMDAT
$LN20:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gbad_array_new_length@std@@UEAAPEAXI@Z ENDP		; std::bad_array_new_length::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??0bad_array_new_length@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
__that$ = 56
??0bad_array_new_length@std@@QEAA@AEBV01@@Z PROC	; std::bad_array_new_length::bad_array_new_length, COMDAT
$LN14:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000e	48 8b 54 24 38	 mov	 rdx, QWORD PTR __that$[rsp]
  00013	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00018	e8 00 00 00 00	 call	 ??0bad_alloc@std@@QEAA@AEBV01@@Z
  0001d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00022	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_array_new_length@std@@6B@
  00029	48 89 08	 mov	 QWORD PTR [rax], rcx
  0002c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00031	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00035	c3		 ret	 0
??0bad_array_new_length@std@@QEAA@AEBV01@@Z ENDP	; std::bad_array_new_length::bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??1bad_array_new_length@std@@UEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1bad_array_new_length@std@@UEAA@XZ PROC		; std::bad_array_new_length::~bad_array_new_length, COMDAT
$LN14:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  00018	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 83 c0 08	 add	 rax, 8
  00021	48 8b c8	 mov	 rcx, rax
  00024	e8 00 00 00 00	 call	 __std_exception_destroy
  00029	90		 npad	 1
  0002a	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0002e	c3		 ret	 0
??1bad_array_new_length@std@@UEAA@XZ ENDP		; std::bad_array_new_length::~bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0bad_array_new_length@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 16
??0bad_array_new_length@std@@QEAA@XZ PROC		; std::bad_array_new_length::bad_array_new_length, COMDAT

; 144  :     {

$LN14:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi

; 67   :     {

  00006	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0000b	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00012	48 89 08	 mov	 QWORD PTR [rax], rcx

; 66   :         : _Data()

  00015	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 83 c0 08	 add	 rax, 8
  0001e	48 8b f8	 mov	 rdi, rax
  00021	33 c0		 xor	 eax, eax
  00023	b9 10 00 00 00	 mov	 ecx, 16
  00028	f3 aa		 rep stosb

; 68   :         _Data._What = _Message;

  0002a	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0002f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_0BF@KINCDENJ@bad?5array?5new?5length@
  00036	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 133  :     {

  0003a	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0003f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_alloc@std@@6B@
  00046	48 89 08	 mov	 QWORD PTR [rax], rcx

; 144  :     {

  00049	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_array_new_length@std@@6B@
  00055	48 89 08	 mov	 QWORD PTR [rax], rcx

; 145  :     }

  00058	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0005d	5f		 pop	 rdi
  0005e	c3		 ret	 0
??0bad_array_new_length@std@@QEAA@XZ ENDP		; std::bad_array_new_length::bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gbad_alloc@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gbad_alloc@std@@UEAAPEAXI@Z PROC			; std::bad_alloc::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gbad_alloc@std@@UEAAPEAXI@Z ENDP			; std::bad_alloc::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0bad_alloc@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
__that$ = 56
??0bad_alloc@std@@QEAA@AEBV01@@Z PROC			; std::bad_alloc::bad_alloc, COMDAT
$LN9:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 20	 sub	 rsp, 32			; 00000020H

; 73   :     {

  0000f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  0001b	48 89 08	 mov	 QWORD PTR [rax], rcx

; 72   :         : _Data()

  0001e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 83 c0 08	 add	 rax, 8
  00027	48 8b f8	 mov	 rdi, rax
  0002a	33 c0		 xor	 eax, eax
  0002c	b9 10 00 00 00	 mov	 ecx, 16
  00031	f3 aa		 rep stosb

; 74   :         __std_exception_copy(&_Other._Data, &_Data);

  00033	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 83 c0 08	 add	 rax, 8
  0003c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR __that$[rsp]
  00041	48 83 c1 08	 add	 rcx, 8
  00045	48 8b d0	 mov	 rdx, rax
  00048	e8 00 00 00 00	 call	 __std_exception_copy
  0004d	90		 npad	 1
  0004e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_alloc@std@@6B@
  0005a	48 89 08	 mov	 QWORD PTR [rax], rcx
  0005d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00062	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00066	5f		 pop	 rdi
  00067	c3		 ret	 0
??0bad_alloc@std@@QEAA@AEBV01@@Z ENDP			; std::bad_alloc::bad_alloc
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gexception@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gexception@std@@UEAAPEAXI@Z PROC			; std::exception::`scalar deleting destructor', COMDAT
$LN10:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gexception@std@@UEAAPEAXI@Z ENDP			; std::exception::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ?what@exception@std@@UEBAPEBDXZ
_TEXT	SEGMENT
tv69 = 0
this$ = 32
?what@exception@std@@UEBAPEBDXZ PROC			; std::exception::what, COMDAT

; 95   :     {

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 18	 sub	 rsp, 24

; 96   :         return _Data._What ? _Data._What : "Unknown exception";

  00009	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00013	74 0f		 je	 SHORT $LN3@what
  00015	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0001e	48 89 04 24	 mov	 QWORD PTR tv69[rsp], rax
  00022	eb 0b		 jmp	 SHORT $LN4@what
$LN3@what:
  00024	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BC@EOODALEL@Unknown?5exception@
  0002b	48 89 04 24	 mov	 QWORD PTR tv69[rsp], rax
$LN4@what:
  0002f	48 8b 04 24	 mov	 rax, QWORD PTR tv69[rsp]

; 97   :     }

  00033	48 83 c4 18	 add	 rsp, 24
  00037	c3		 ret	 0
?what@exception@std@@UEBAPEBDXZ ENDP			; std::exception::what
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0exception@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
_Other$ = 56
??0exception@std@@QEAA@AEBV01@@Z PROC			; std::exception::exception, COMDAT

; 73   :     {

$LN4:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  0000f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  0001b	48 89 08	 mov	 QWORD PTR [rax], rcx

; 72   :         : _Data()

  0001e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 83 c0 08	 add	 rax, 8
  00027	48 8b f8	 mov	 rdi, rax
  0002a	33 c0		 xor	 eax, eax
  0002c	b9 10 00 00 00	 mov	 ecx, 16
  00031	f3 aa		 rep stosb

; 74   :         __std_exception_copy(&_Other._Data, &_Data);

  00033	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 83 c0 08	 add	 rax, 8
  0003c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Other$[rsp]
  00041	48 83 c1 08	 add	 rcx, 8
  00045	48 8b d0	 mov	 rdx, rax
  00048	e8 00 00 00 00	 call	 __std_exception_copy
  0004d	90		 npad	 1

; 75   :     }

  0004e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00057	5f		 pop	 rdi
  00058	c3		 ret	 0
??0exception@std@@QEAA@AEBV01@@Z ENDP			; std::exception::exception
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeGiftEvent.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WOPS\Event\WOPSGoldenTimeGiftEvent.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
