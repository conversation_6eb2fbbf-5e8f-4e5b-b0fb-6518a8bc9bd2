; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	__local_stdio_printf_options
PUBLIC	sprintf_s
PUBLIC	??0exception@std@@QEAA@AEBV01@@Z		; std::exception::exception
PUBLIC	?what@exception@std@@UEBAPEBDXZ			; std::exception::what
PUBLIC	??_Gexception@std@@UEAAPEAXI@Z			; std::exception::`scalar deleting destructor'
PUBLIC	??0bad_alloc@std@@QEAA@AEBV01@@Z		; std::bad_alloc::bad_alloc
PUBLIC	??_Gbad_alloc@std@@UEAAPEAXI@Z			; std::bad_alloc::`scalar deleting destructor'
PUBL<PERSON>	??0bad_array_new_length@std@@QEAA@XZ		; std::bad_array_new_length::bad_array_new_length
PUBLIC	??1bad_array_new_length@std@@UEAA@XZ		; std::bad_array_new_length::~bad_array_new_length
PUBLIC	??0bad_array_new_length@std@@QEAA@AEBV01@@Z	; std::bad_array_new_length::bad_array_new_length
PUBLIC	??_Gbad_array_new_length@std@@UEAAPEAXI@Z	; std::bad_array_new_length::`scalar deleting destructor'
PUBLIC	?_Throw_bad_array_new_length@std@@YAXXZ		; std::_Throw_bad_array_new_length
PUBLIC	?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
PUBLIC	?_Xlen_string@std@@YAXXZ			; std::_Xlen_string
PUBLIC	?allocate@?$allocator@D@std@@QEAAPEAD_K@Z	; std::allocator<char>::allocate
PUBLIC	??0?$_String_val@U?$_Simple_types@D@std@@@std@@QEAA@XZ ; std::_String_val<std::_Simple_types<char> >::_String_val<std::_Simple_types<char> >
PUBLIC	?_Myptr@?$_String_val@U?$_Simple_types@D@std@@@std@@QEAAPEADXZ ; std::_String_val<std::_Simple_types<char> >::_Myptr
PUBLIC	?_Myptr@?$_String_val@U?$_Simple_types@D@std@@@std@@QEBAPEBDXZ ; std::_String_val<std::_Simple_types<char> >::_Myptr
PUBLIC	?_Xran@?$_String_val@U?$_Simple_types@D@std@@@std@@SAXXZ ; std::_String_val<std::_Simple_types<char> >::_Xran
PUBLIC	??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
PUBLIC	??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
PUBLIC	??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@_K1AEBV?$allocator@D@1@@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
PUBLIC	??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@QEBD@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
PUBLIC	?_Construct_empty@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_empty
PUBLIC	??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
PUBLIC	?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@AEBV12@@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::append
PUBLIC	?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::append
PUBLIC	?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@_KD@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::append
PUBLIC	?c_str@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBAPEBDXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::c_str
PUBLIC	?max_size@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBA_KXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::max_size
PUBLIC	?find@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBA_KD_K@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::find
PUBLIC	?compare@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBAHQEBD@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::compare
PUBLIC	?_Calculate_growth@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@CA_K_K00@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Calculate_growth
PUBLIC	?_Eos@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAX_K@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Eos
PUBLIC	?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Tidy_deallocate
PUBLIC	??1?$_Compressed_pair@V?$allocator@D@std@@V?$_String_val@U?$_Simple_types@D@std@@@2@$00@std@@QEAA@XZ ; std::_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1>::~_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1>
PUBLIC	?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z	; std::allocator<wchar_t>::allocate
PUBLIC	?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEAAPEA_WXZ ; std::_String_val<std::_Simple_types<wchar_t> >::_Myptr
PUBLIC	??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
PUBLIC	??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
PUBLIC	?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_empty
PUBLIC	??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
PUBLIC	?append@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV12@_K_W@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::append
PUBLIC	?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
PUBLIC	?_Calculate_growth@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@CA_K_K00@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Calculate_growth
PUBLIC	?_Eos@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAX_K@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Eos
PUBLIC	?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate
PUBLIC	??1?$_Compressed_pair@V?$allocator@_W@std@@V?$_String_val@U?$_Simple_types@_W@std@@@2@$00@std@@QEAA@XZ ; std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>::~_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>
PUBLIC	??1?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QEAA@XZ ; std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::~vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >
PUBLIC	?_Tidy@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@AEAAXXZ ; std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Tidy
PUBLIC	??0AppChatParser@RedisPacket@mu2@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ; mu2::RedisPacket::AppChatParser::AppChatParser
PUBLIC	??0AppChatParser@RedisPacket@mu2@@QEAA@IIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z ; mu2::RedisPacket::AppChatParser::AppChatParser
PUBLIC	?Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ	; mu2::RedisPacket::AppChatParser::Parse
PUBLIC	?ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ ; mu2::RedisPacket::AppChatParser::ConvertToAppChat
PUBLIC	?GetRedisEventCode@RedisPacket@mu2@@YAGAEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ; mu2::RedisPacket::GetRedisEventCode
PUBLIC	?GetKnightageId@RedisPacket@mu2@@YAIAEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ; mu2::RedisPacket::GetKnightageId
PUBLIC	??$_Construct@$01PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<2,wchar_t const *>
PUBLIC	??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct<1,char const *>
PUBLIC	??$_Construct@$01PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct<2,char const *>
PUBLIC	??$_Reallocate_grow_by@V<lambda_65e615be2a453ca0576c979606f46740>@@PEBD_K@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_65e615be2a453ca0576c979606f46740>@@PEBD_K@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_65e615be2a453ca0576c979606f46740>,char const *,unsigned __int64>
PUBLIC	??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
PUBLIC	??$_Reallocate_grow_by@V<lambda_e1befb086ad3257e3f042a63030725f7>@@_KD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_e1befb086ad3257e3f042a63030725f7>@@_KD@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_e1befb086ad3257e3f042a63030725f7>,unsigned __int64,char>
PUBLIC	??$_Traits_find_ch@U?$char_traits@D@std@@@std@@YA_KQEBD_K1D@Z ; std::_Traits_find_ch<std::char_traits<char> >
PUBLIC	??$_Find_vectorized@$$CBDD@std@@YAPEBDQEBD0D@Z	; std::_Find_vectorized<char const ,char>
PUBLIC	??$_Reallocate_grow_by@V<lambda_a3050a43f3157934f354774ab3dd2e02>@@_K_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_a3050a43f3157934f354774ab3dd2e02>@@_K_W@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Reallocate_grow_by<<lambda_a3050a43f3157934f354774ab3dd2e02>,unsigned __int64,wchar_t>
PUBLIC	?_OptionsStorage@?1??__local_stdio_printf_options@@9@4_KA ; `__local_stdio_printf_options'::`2'::_OptionsStorage
PUBLIC	??_7exception@std@@6B@				; std::exception::`vftable'
PUBLIC	??_C@_0BC@EOODALEL@Unknown?5exception@		; `string'
PUBLIC	??_7bad_alloc@std@@6B@				; std::bad_alloc::`vftable'
PUBLIC	??_7bad_array_new_length@std@@6B@		; std::bad_array_new_length::`vftable'
PUBLIC	??_C@_0BF@KINCDENJ@bad?5array?5new?5length@	; `string'
PUBLIC	??_R0?AVexception@std@@@8			; std::exception `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
PUBLIC	_TI3?AVbad_array_new_length@std@@
PUBLIC	_CTA3?AVbad_array_new_length@std@@
PUBLIC	??_R0?AVbad_array_new_length@std@@@8		; std::bad_array_new_length `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
PUBLIC	??_R0?AVbad_alloc@std@@@8			; std::bad_alloc `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
PUBLIC	??_C@_0BA@JFNIOLAK@string?5too?5long@		; `string'
PUBLIC	??_C@_0BI@CFPLBAOH@invalid?5string?5position@	; `string'
PUBLIC	??_R4exception@std@@6B@				; std::exception::`RTTI Complete Object Locator'
PUBLIC	??_R3exception@std@@8				; std::exception::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2exception@std@@8				; std::exception::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@exception@std@@8			; std::exception::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4bad_array_new_length@std@@6B@		; std::bad_array_new_length::`RTTI Complete Object Locator'
PUBLIC	??_R3bad_array_new_length@std@@8		; std::bad_array_new_length::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2bad_array_new_length@std@@8		; std::bad_array_new_length::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@bad_array_new_length@std@@8	; std::bad_array_new_length::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@bad_alloc@std@@8			; std::bad_alloc::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R3bad_alloc@std@@8				; std::bad_alloc::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2bad_alloc@std@@8				; std::bad_alloc::`RTTI Base Class Array'
PUBLIC	??_R4bad_alloc@std@@6B@				; std::bad_alloc::`RTTI Complete Object Locator'
PUBLIC	??_C@_01GPOEFGEJ@?7@				; `string'
PUBLIC	??_C@_03NHELKHFK@?$CFd?7@			; `string'
PUBLIC	??_C@_08PODIDHJA@frompc?$CFd@			; `string'
PUBLIC	??_C@_0CE@IHFNLKB@?$CK3?$AN?6$7?$AN?6PUBLISH?$AN?6$?$CFd?$AN?6?$CFs?$AN?6$?$CFd?$AN?6@ ; `string'
PUBLIC	??_C@_09OMPELEIG@subscribe@			; `string'
PUBLIC	??_C@_07ONPBMBOP@message@			; `string'
EXTRN	??2@YAPEAX_K@Z:PROC				; operator new
EXTRN	??3@YAXPEAX_K@Z:PROC				; operator delete
EXTRN	__std_terminate:PROC
EXTRN	_invoke_watson:PROC
EXTRN	memchr:PROC
EXTRN	memcmp:PROC
EXTRN	memcpy:PROC
EXTRN	memmove:PROC
EXTRN	memset:PROC
EXTRN	strlen:PROC
EXTRN	atoi:PROC
EXTRN	__stdio_common_vsprintf_s:PROC
EXTRN	__std_exception_copy:PROC
EXTRN	__std_exception_destroy:PROC
EXTRN	??_Eexception@std@@UEAAPEAXI@Z:PROC		; std::exception::`vector deleting destructor'
EXTRN	??_Ebad_alloc@std@@UEAAPEAXI@Z:PROC		; std::bad_alloc::`vector deleting destructor'
EXTRN	??_Ebad_array_new_length@std@@UEAAPEAXI@Z:PROC	; std::bad_array_new_length::`vector deleting destructor'
EXTRN	__std_find_trivial_1:PROC
EXTRN	?_Xlength_error@std@@YAXPEBD@Z:PROC		; std::_Xlength_error
EXTRN	?_Xout_of_range@std@@YAXPEBD@Z:PROC		; std::_Xout_of_range
EXTRN	?Convert@StringUtil@mu2@@SA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@4@H@Z:PROC ; mu2::StringUtil::Convert
EXTRN	?Convert@StringUtil@mu2@@SA_NAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@4@H@Z:PROC ; mu2::StringUtil::Convert
EXTRN	?split@StringUtil@mu2@@SA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@4@0I@Z:PROC ; mu2::StringUtil::split
EXTRN	_CxxThrowException:PROC
EXTRN	__CxxFrameHandler4:PROC
EXTRN	__GSHandlerCheck:PROC
EXTRN	__GSHandlerCheck_EH4:PROC
EXTRN	__security_check_cookie:PROC
EXTRN	??_7type_info@@6B@:BYTE				; type_info::`vftable'
EXTRN	?Instance@Server@mu2@@2PEAV12@EA:QWORD		; mu2::Server::Instance
EXTRN	__security_cookie:QWORD
;	COMDAT ?_OptionsStorage@?1??__local_stdio_printf_options@@9@4_KA
_BSS	SEGMENT
?_OptionsStorage@?1??__local_stdio_printf_options@@9@4_KA DQ 01H DUP (?) ; `__local_stdio_printf_options'::`2'::_OptionsStorage
_BSS	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$sprintf_s DD imagerel $LN7
	DD	imagerel $LN7+170
	DD	imagerel $unwind$sprintf_s
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0exception@std@@QEAA@AEBV01@@Z DD imagerel $LN4
	DD	imagerel $LN4+89
	DD	imagerel $unwind$??0exception@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?what@exception@std@@UEBAPEBDXZ DD imagerel $LN5
	DD	imagerel $LN5+56
	DD	imagerel $unwind$?what@exception@std@@UEBAPEBDXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gexception@std@@UEAAPEAXI@Z DD imagerel $LN10
	DD	imagerel $LN10+83
	DD	imagerel $unwind$??_Gexception@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z DD imagerel $LN9
	DD	imagerel $LN9+104
	DD	imagerel $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+83
	DD	imagerel $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_array_new_length@std@@QEAA@XZ DD imagerel $LN14
	DD	imagerel $LN14+95
	DD	imagerel $unwind$??0bad_array_new_length@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1bad_array_new_length@std@@UEAA@XZ DD imagerel $LN14
	DD	imagerel $LN14+47
	DD	imagerel $unwind$??1bad_array_new_length@std@@UEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z DD imagerel $LN14
	DD	imagerel $LN14+54
	DD	imagerel $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z DD imagerel $LN20
	DD	imagerel $LN20+83
	DD	imagerel $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Throw_bad_array_new_length@std@@YAXXZ DD imagerel $LN3
	DD	imagerel $LN3+37
	DD	imagerel $unwind$?_Throw_bad_array_new_length@std@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z DD imagerel $LN5
	DD	imagerel $LN5+159
	DD	imagerel $unwind$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Xlen_string@std@@YAXXZ DD imagerel $LN3
	DD	imagerel $LN3+22
	DD	imagerel $unwind$?_Xlen_string@std@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?allocate@?$allocator@D@std@@QEAAPEAD_K@Z DD imagerel $LN11
	DD	imagerel $LN11+121
	DD	imagerel $unwind$?allocate@?$allocator@D@std@@QEAAPEAD_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0?$_String_val@U?$_Simple_types@D@std@@@std@@QEAA@XZ DD imagerel $LN9
	DD	imagerel $LN9+73
	DD	imagerel $unwind$??0?$_String_val@U?$_Simple_types@D@std@@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Myptr@?$_String_val@U?$_Simple_types@D@std@@@std@@QEAAPEADXZ DD imagerel $LN17
	DD	imagerel $LN17+111
	DD	imagerel $unwind$?_Myptr@?$_String_val@U?$_Simple_types@D@std@@@std@@QEAAPEADXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Myptr@?$_String_val@U?$_Simple_types@D@std@@@std@@QEBAPEBDXZ DD imagerel $LN17
	DD	imagerel $LN17+111
	DD	imagerel $unwind$?_Myptr@?$_String_val@U?$_Simple_types@D@std@@@std@@QEBAPEBDXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Xran@?$_String_val@U?$_Simple_types@D@std@@@std@@SAXXZ DD imagerel $LN3
	DD	imagerel $LN3+22
	DD	imagerel $unwind$?_Xran@?$_String_val@U?$_Simple_types@D@std@@@std@@SAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ DD imagerel $LN41
	DD	imagerel $LN41+107
	DD	imagerel $unwind$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@@Z DD imagerel $LN224
	DD	imagerel $LN224+287
	DD	imagerel $unwind$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@@Z@4HA DD imagerel ?dtor$0@?0???0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@@Z@4HA
	DD	imagerel ?dtor$0@?0???0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@@Z@4HA+27
	DD	imagerel $unwind$?dtor$0@?0???0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@_K1AEBV?$allocator@D@1@@Z DD imagerel $LN244
	DD	imagerel $LN244+438
	DD	imagerel $unwind$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@_K1AEBV?$allocator@D@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@_K1AEBV?$allocator@D@1@@Z@4HA DD imagerel ?dtor$0@?0???0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@_K1AEBV?$allocator@D@1@@Z@4HA
	DD	imagerel ?dtor$0@?0???0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@_K1AEBV?$allocator@D@1@@Z@4HA+27
	DD	imagerel $unwind$?dtor$0@?0???0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@_K1AEBV?$allocator@D@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@QEBD@Z DD imagerel $LN219
	DD	imagerel $LN219+150
	DD	imagerel $unwind$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@QEBD@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@QEBD@Z@4HA DD imagerel ?dtor$0@?0???0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@QEBD@Z@4HA
	DD	imagerel ?dtor$0@?0???0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@QEBD@Z@4HA+24
	DD	imagerel $unwind$?dtor$0@?0???0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@QEBD@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Construct_empty@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ DD imagerel $LN18
	DD	imagerel $LN18+94
	DD	imagerel $unwind$?_Construct_empty@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ DD imagerel $LN82
	DD	imagerel $LN82+25
	DD	imagerel $unwind$??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@AEBV12@@Z DD imagerel $LN214
	DD	imagerel $LN214+54
	DD	imagerel $unwind$?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@AEBV12@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z DD imagerel $LN233
	DD	imagerel $LN233+243
	DD	imagerel $unwind$?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@_KD@Z DD imagerel $LN233
	DD	imagerel $LN233+234
	DD	imagerel $unwind$?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@_KD@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?c_str@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBAPEBDXZ DD imagerel $LN22
	DD	imagerel $LN22+131
	DD	imagerel $unwind$?c_str@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBAPEBDXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?max_size@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBA_KXZ DD imagerel $LN38
	DD	imagerel $LN38+244
	DD	imagerel $unwind$?max_size@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBA_KXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?find@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBA_KD_K@Z DD imagerel $LN42
	DD	imagerel $LN42+170
	DD	imagerel $unwind$?find@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBA_KD_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?compare@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBAHQEBD@Z DD imagerel $LN47
	DD	imagerel $LN47+383
	DD	imagerel $unwind$?compare@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBAHQEBD@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Calculate_growth@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@CA_K_K00@Z DD imagerel $LN13
	DD	imagerel $LN13+189
	DD	imagerel $unwind$?_Calculate_growth@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@CA_K_K00@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Eos@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAX_K@Z DD imagerel $LN27
	DD	imagerel $LN27+173
	DD	imagerel $unwind$?_Eos@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAX_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ DD imagerel $LN62
	DD	imagerel $LN62+257
	DD	imagerel $unwind$?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z DD imagerel $LN13
	DD	imagerel $LN13+162
	DD	imagerel $unwind$?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEAAPEA_WXZ DD imagerel $LN17
	DD	imagerel $LN17+111
	DD	imagerel $unwind$?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEAAPEA_WXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ DD imagerel $LN41
	DD	imagerel $LN41+107
	DD	imagerel $unwind$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z DD imagerel $LN226
	DD	imagerel $LN226+287
	DD	imagerel $unwind$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z@4HA DD imagerel ?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z@4HA
	DD	imagerel ?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z@4HA+27
	DD	imagerel $unwind$?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DD imagerel $LN18
	DD	imagerel $LN18+98
	DD	imagerel $unwind$?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ DD imagerel $LN82
	DD	imagerel $LN82+25
	DD	imagerel $unwind$??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?append@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV12@_K_W@Z DD imagerel $LN250
	DD	imagerel $LN250+352
	DD	imagerel $unwind$?append@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV12@_K_W@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ DD imagerel $LN38
	DD	imagerel $LN38+250
	DD	imagerel $unwind$?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Calculate_growth@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@CA_K_K00@Z DD imagerel $LN13
	DD	imagerel $LN13+189
	DD	imagerel $unwind$?_Calculate_growth@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@CA_K_K00@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Eos@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAX_K@Z DD imagerel $LN27
	DD	imagerel $LN27+171
	DD	imagerel $unwind$?_Eos@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAX_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DD imagerel $LN62
	DD	imagerel $LN62+266
	DD	imagerel $unwind$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QEAA@XZ DD imagerel $LN140
	DD	imagerel $LN140+25
	DD	imagerel $unwind$??1?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Tidy@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@AEAAXXZ DD imagerel $LN139
	DD	imagerel $LN139+387
	DD	imagerel $unwind$?_Tidy@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@AEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0AppChatParser@RedisPacket@mu2@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z DD imagerel $LN313
	DD	imagerel $LN313+108
	DD	imagerel $unwind$??0AppChatParser@RedisPacket@mu2@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???0AppChatParser@RedisPacket@mu2@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@4HA DD imagerel ?dtor$0@?0???0AppChatParser@RedisPacket@mu2@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@4HA
	DD	imagerel ?dtor$0@?0???0AppChatParser@RedisPacket@mu2@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@4HA+28
	DD	imagerel $unwind$?dtor$0@?0???0AppChatParser@RedisPacket@mu2@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$1@?0???0AppChatParser@RedisPacket@mu2@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@4HA DD imagerel ?dtor$1@?0???0AppChatParser@RedisPacket@mu2@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@4HA
	DD	imagerel ?dtor$1@?0???0AppChatParser@RedisPacket@mu2@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@4HA+28
	DD	imagerel $unwind$?dtor$1@?0???0AppChatParser@RedisPacket@mu2@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$2@?0???0AppChatParser@RedisPacket@mu2@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@4HA DD imagerel ?dtor$2@?0???0AppChatParser@RedisPacket@mu2@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@4HA
	DD	imagerel ?dtor$2@?0???0AppChatParser@RedisPacket@mu2@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@4HA+31
	DD	imagerel $unwind$?dtor$2@?0???0AppChatParser@RedisPacket@mu2@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0AppChatParser@RedisPacket@mu2@@QEAA@IIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z DD imagerel $LN253
	DD	imagerel $LN253+145
	DD	imagerel $unwind$??0AppChatParser@RedisPacket@mu2@@QEAA@IIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???0AppChatParser@RedisPacket@mu2@@QEAA@IIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z@4HA DD imagerel ?dtor$0@?0???0AppChatParser@RedisPacket@mu2@@QEAA@IIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z@4HA
	DD	imagerel ?dtor$0@?0???0AppChatParser@RedisPacket@mu2@@QEAA@IIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z@4HA+28
	DD	imagerel $unwind$?dtor$0@?0???0AppChatParser@RedisPacket@mu2@@QEAA@IIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$1@?0???0AppChatParser@RedisPacket@mu2@@QEAA@IIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z@4HA DD imagerel ?dtor$1@?0???0AppChatParser@RedisPacket@mu2@@QEAA@IIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z@4HA
	DD	imagerel ?dtor$1@?0???0AppChatParser@RedisPacket@mu2@@QEAA@IIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z@4HA+28
	DD	imagerel $unwind$?dtor$1@?0???0AppChatParser@RedisPacket@mu2@@QEAA@IIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$2@?0???0AppChatParser@RedisPacket@mu2@@QEAA@IIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z@4HA DD imagerel ?dtor$2@?0???0AppChatParser@RedisPacket@mu2@@QEAA@IIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z@4HA
	DD	imagerel ?dtor$2@?0???0AppChatParser@RedisPacket@mu2@@QEAA@IIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z@4HA+31
	DD	imagerel $unwind$?dtor$2@?0???0AppChatParser@RedisPacket@mu2@@QEAA@IIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ DD imagerel $LN412
	DD	imagerel $LN412+544
	DD	imagerel $unwind$?Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0??Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA DD imagerel ?dtor$0@?0??Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA
	DD	imagerel ?dtor$0@?0??Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA+27
	DD	imagerel $unwind$?dtor$0@?0??Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$1@?0??Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA DD imagerel ?dtor$1@?0??Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA
	DD	imagerel ?dtor$1@?0??Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA+27
	DD	imagerel $unwind$?dtor$1@?0??Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$2@?0??Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA DD imagerel ?dtor$2@?0??Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA
	DD	imagerel ?dtor$2@?0??Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA+24
	DD	imagerel $unwind$?dtor$2@?0??Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ DD imagerel $LN480
	DD	imagerel $LN480+1145
	DD	imagerel $unwind$?ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0??ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA DD imagerel ?dtor$0@?0??ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA
	DD	imagerel ?dtor$0@?0??ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA+27
	DD	imagerel $unwind$?dtor$0@?0??ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$1@?0??ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA DD imagerel ?dtor$1@?0??ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA
	DD	imagerel ?dtor$1@?0??ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA+27
	DD	imagerel $unwind$?dtor$1@?0??ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$2@?0??ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA DD imagerel ?dtor$2@?0??ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA
	DD	imagerel ?dtor$2@?0??ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA+27
	DD	imagerel $unwind$?dtor$2@?0??ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?GetRedisEventCode@RedisPacket@mu2@@YAGAEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z DD imagerel $LN102
	DD	imagerel $LN102+90
	DD	imagerel $unwind$?GetRedisEventCode@RedisPacket@mu2@@YAGAEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?GetKnightageId@RedisPacket@mu2@@YAIAEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z DD imagerel $LN210
	DD	imagerel $LN210+203
	DD	imagerel $unwind$?GetKnightageId@RedisPacket@mu2@@YAIAEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Construct@$01PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z DD imagerel $LN173
	DD	imagerel $LN173+746
	DD	imagerel $unwind$??$_Construct@$01PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z DD imagerel $LN186
	DD	imagerel $LN186+828
	DD	imagerel $unwind$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Construct@$01PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z DD imagerel $LN171
	DD	imagerel $LN171+738
	DD	imagerel $unwind$??$_Construct@$01PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Reallocate_grow_by@V<lambda_65e615be2a453ca0576c979606f46740>@@PEBD_K@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_65e615be2a453ca0576c979606f46740>@@PEBD_K@Z DD imagerel $LN203
	DD	imagerel $LN203+854
	DD	imagerel $unwind$??$_Reallocate_grow_by@V<lambda_65e615be2a453ca0576c979606f46740>@@PEBD_K@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_65e615be2a453ca0576c979606f46740>@@PEBD_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z DD imagerel $LN7
	DD	imagerel $LN7+150
	DD	imagerel $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Reallocate_grow_by@V<lambda_e1befb086ad3257e3f042a63030725f7>@@_KD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_e1befb086ad3257e3f042a63030725f7>@@_KD@Z DD imagerel $LN203
	DD	imagerel $LN203+826
	DD	imagerel $unwind$??$_Reallocate_grow_by@V<lambda_e1befb086ad3257e3f042a63030725f7>@@_KD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_e1befb086ad3257e3f042a63030725f7>@@_KD@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Traits_find_ch@U?$char_traits@D@std@@@std@@YA_KQEBD_K1D@Z DD imagerel $LN19
	DD	imagerel $LN19+280
	DD	imagerel $unwind$??$_Traits_find_ch@U?$char_traits@D@std@@@std@@YA_KQEBD_K1D@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Find_vectorized@$$CBDD@std@@YAPEBDQEBD0D@Z DD imagerel $LN4
	DD	imagerel $LN4+45
	DD	imagerel $unwind$??$_Find_vectorized@$$CBDD@std@@YAPEBDQEBD0D@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Reallocate_grow_by@V<lambda_a3050a43f3157934f354774ab3dd2e02>@@_K_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_a3050a43f3157934f354774ab3dd2e02>@@_K_W@Z DD imagerel $LN215
	DD	imagerel $LN215+1040
	DD	imagerel $unwind$??$_Reallocate_grow_by@V<lambda_a3050a43f3157934f354774ab3dd2e02>@@_K_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_a3050a43f3157934f354774ab3dd2e02>@@_K_W@Z
pdata	ENDS
;	COMDAT ??_C@_07ONPBMBOP@message@
CONST	SEGMENT
??_C@_07ONPBMBOP@message@ DB 'message', 00H		; `string'
CONST	ENDS
;	COMDAT ??_C@_09OMPELEIG@subscribe@
CONST	SEGMENT
??_C@_09OMPELEIG@subscribe@ DB 'subscribe', 00H		; `string'
CONST	ENDS
;	COMDAT ??_C@_0CE@IHFNLKB@?$CK3?$AN?6$7?$AN?6PUBLISH?$AN?6$?$CFd?$AN?6?$CFs?$AN?6$?$CFd?$AN?6@
CONST	SEGMENT
??_C@_0CE@IHFNLKB@?$CK3?$AN?6$7?$AN?6PUBLISH?$AN?6$?$CFd?$AN?6?$CFs?$AN?6$?$CFd?$AN?6@ DB '*'
	DB	'3', 0dH, 0aH, '$7', 0dH, 0aH, 'PUBLISH', 0dH, 0aH, '$%d', 0dH
	DB	0aH, '%s', 0dH, 0aH, '$%d', 0dH, 0aH, '%s', 0dH, 0aH, 00H ; `string'
CONST	ENDS
;	COMDAT ??_C@_08PODIDHJA@frompc?$CFd@
CONST	SEGMENT
??_C@_08PODIDHJA@frompc?$CFd@ DB 'frompc%d', 00H	; `string'
CONST	ENDS
;	COMDAT ??_C@_03NHELKHFK@?$CFd?7@
CONST	SEGMENT
??_C@_03NHELKHFK@?$CFd?7@ DB '%d', 09H, 00H		; `string'
CONST	ENDS
;	COMDAT ??_C@_01GPOEFGEJ@?7@
CONST	SEGMENT
??_C@_01GPOEFGEJ@?7@ DB 09H, 00H			; `string'
CONST	ENDS
;	COMDAT ??_R4bad_alloc@std@@6B@
rdata$r	SEGMENT
??_R4bad_alloc@std@@6B@ DD 01H				; std::bad_alloc::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVbad_alloc@std@@@8
	DD	imagerel ??_R3bad_alloc@std@@8
	DD	imagerel ??_R4bad_alloc@std@@6B@
rdata$r	ENDS
;	COMDAT ??_R2bad_alloc@std@@8
rdata$r	SEGMENT
??_R2bad_alloc@std@@8 DD imagerel ??_R1A@?0A@EA@bad_alloc@std@@8 ; std::bad_alloc::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@exception@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3bad_alloc@std@@8
rdata$r	SEGMENT
??_R3bad_alloc@std@@8 DD 00H				; std::bad_alloc::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2bad_alloc@std@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@bad_alloc@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@bad_alloc@std@@8 DD imagerel ??_R0?AVbad_alloc@std@@@8 ; std::bad_alloc::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3bad_alloc@std@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@bad_array_new_length@std@@8 DD imagerel ??_R0?AVbad_array_new_length@std@@@8 ; std::bad_array_new_length::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	02H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3bad_array_new_length@std@@8
rdata$r	ENDS
;	COMDAT ??_R2bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R2bad_array_new_length@std@@8 DD imagerel ??_R1A@?0A@EA@bad_array_new_length@std@@8 ; std::bad_array_new_length::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@bad_alloc@std@@8
	DD	imagerel ??_R1A@?0A@EA@exception@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R3bad_array_new_length@std@@8 DD 00H			; std::bad_array_new_length::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	03H
	DD	imagerel ??_R2bad_array_new_length@std@@8
rdata$r	ENDS
;	COMDAT ??_R4bad_array_new_length@std@@6B@
rdata$r	SEGMENT
??_R4bad_array_new_length@std@@6B@ DD 01H		; std::bad_array_new_length::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVbad_array_new_length@std@@@8
	DD	imagerel ??_R3bad_array_new_length@std@@8
	DD	imagerel ??_R4bad_array_new_length@std@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@exception@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@exception@std@@8 DD imagerel ??_R0?AVexception@std@@@8 ; std::exception::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3exception@std@@8
rdata$r	ENDS
;	COMDAT ??_R2exception@std@@8
rdata$r	SEGMENT
??_R2exception@std@@8 DD imagerel ??_R1A@?0A@EA@exception@std@@8 ; std::exception::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3exception@std@@8
rdata$r	SEGMENT
??_R3exception@std@@8 DD 00H				; std::exception::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2exception@std@@8
rdata$r	ENDS
;	COMDAT ??_R4exception@std@@6B@
rdata$r	SEGMENT
??_R4exception@std@@6B@ DD 01H				; std::exception::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVexception@std@@@8
	DD	imagerel ??_R3exception@std@@8
	DD	imagerel ??_R4exception@std@@6B@
rdata$r	ENDS
;	COMDAT ??_C@_0BI@CFPLBAOH@invalid?5string?5position@
CONST	SEGMENT
??_C@_0BI@CFPLBAOH@invalid?5string?5position@ DB 'invalid string position'
	DB	00H						; `string'
CONST	ENDS
;	COMDAT ??_C@_0BA@JFNIOLAK@string?5too?5long@
CONST	SEGMENT
??_C@_0BA@JFNIOLAK@string?5too?5long@ DB 'string too long', 00H ; `string'
CONST	ENDS
;	COMDAT _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 DD 010H
	DD	imagerel ??_R0?AVbad_alloc@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0bad_alloc@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVbad_alloc@std@@@8
data$r	SEGMENT
??_R0?AVbad_alloc@std@@@8 DQ FLAT:??_7type_info@@6B@	; std::bad_alloc `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVbad_alloc@std@@', 00H
data$r	ENDS
;	COMDAT _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 DD 00H
	DD	imagerel ??_R0?AVbad_array_new_length@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0bad_array_new_length@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVbad_array_new_length@std@@@8
data$r	SEGMENT
??_R0?AVbad_array_new_length@std@@@8 DQ FLAT:??_7type_info@@6B@ ; std::bad_array_new_length `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVbad_array_new_length@std@@', 00H
data$r	ENDS
;	COMDAT _CTA3?AVbad_array_new_length@std@@
xdata$x	SEGMENT
_CTA3?AVbad_array_new_length@std@@ DD 03H
	DD	imagerel _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
	DD	imagerel _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
	DD	imagerel _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
xdata$x	ENDS
;	COMDAT _TI3?AVbad_array_new_length@std@@
xdata$x	SEGMENT
_TI3?AVbad_array_new_length@std@@ DD 00H
	DD	imagerel ??1bad_array_new_length@std@@UEAA@XZ
	DD	00H
	DD	imagerel _CTA3?AVbad_array_new_length@std@@
xdata$x	ENDS
;	COMDAT _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 DD 00H
	DD	imagerel ??_R0?AVexception@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0exception@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVexception@std@@@8
data$r	SEGMENT
??_R0?AVexception@std@@@8 DQ FLAT:??_7type_info@@6B@	; std::exception `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVexception@std@@', 00H
data$r	ENDS
;	COMDAT ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@
CONST	SEGMENT
??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ DB 'bad array new length', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7bad_array_new_length@std@@6B@
CONST	SEGMENT
??_7bad_array_new_length@std@@6B@ DQ FLAT:??_R4bad_array_new_length@std@@6B@ ; std::bad_array_new_length::`vftable'
	DQ	FLAT:??_Ebad_array_new_length@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT ??_7bad_alloc@std@@6B@
CONST	SEGMENT
??_7bad_alloc@std@@6B@ DQ FLAT:??_R4bad_alloc@std@@6B@	; std::bad_alloc::`vftable'
	DQ	FLAT:??_Ebad_alloc@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT ??_C@_0BC@EOODALEL@Unknown?5exception@
CONST	SEGMENT
??_C@_0BC@EOODALEL@Unknown?5exception@ DB 'Unknown exception', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7exception@std@@6B@
CONST	SEGMENT
??_7exception@std@@6B@ DQ FLAT:??_R4exception@std@@6B@	; std::exception::`vftable'
	DQ	FLAT:??_Eexception@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$_Reallocate_grow_by@V<lambda_a3050a43f3157934f354774ab3dd2e02>@@_K_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_a3050a43f3157934f354774ab3dd2e02>@@_K_W@Z DB 06H
	DB	00H
	DB	00H
	DB	0c1H, 0aH
	DB	02H
	DB	'b'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$_Reallocate_grow_by@V<lambda_a3050a43f3157934f354774ab3dd2e02>@@_K_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_a3050a43f3157934f354774ab3dd2e02>@@_K_W@Z DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$_Reallocate_grow_by@V<lambda_a3050a43f3157934f354774ab3dd2e02>@@_K_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_a3050a43f3157934f354774ab3dd2e02>@@_K_W@Z DB 028H
	DD	imagerel $stateUnwindMap$??$_Reallocate_grow_by@V<lambda_a3050a43f3157934f354774ab3dd2e02>@@_K_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_a3050a43f3157934f354774ab3dd2e02>@@_K_W@Z
	DD	imagerel $ip2state$??$_Reallocate_grow_by@V<lambda_a3050a43f3157934f354774ab3dd2e02>@@_K_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_a3050a43f3157934f354774ab3dd2e02>@@_K_W@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Reallocate_grow_by@V<lambda_a3050a43f3157934f354774ab3dd2e02>@@_K_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_a3050a43f3157934f354774ab3dd2e02>@@_K_W@Z DD 021b11H
	DD	027011bH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$_Reallocate_grow_by@V<lambda_a3050a43f3157934f354774ab3dd2e02>@@_K_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_a3050a43f3157934f354774ab3dd2e02>@@_K_W@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Find_vectorized@$$CBDD@std@@YAPEBDQEBD0D@Z DD 011301H
	DD	04213H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Traits_find_ch@U?$char_traits@D@std@@@std@@YA_KQEBD_K1D@Z DD 011801H
	DD	0a218H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$_Reallocate_grow_by@V<lambda_e1befb086ad3257e3f042a63030725f7>@@_KD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_e1befb086ad3257e3f042a63030725f7>@@_KD@Z DB 06H
	DB	00H
	DB	00H
	DB	0e1H, 08H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$_Reallocate_grow_by@V<lambda_e1befb086ad3257e3f042a63030725f7>@@_KD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_e1befb086ad3257e3f042a63030725f7>@@_KD@Z DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$_Reallocate_grow_by@V<lambda_e1befb086ad3257e3f042a63030725f7>@@_KD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_e1befb086ad3257e3f042a63030725f7>@@_KD@Z DB 028H
	DD	imagerel $stateUnwindMap$??$_Reallocate_grow_by@V<lambda_e1befb086ad3257e3f042a63030725f7>@@_KD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_e1befb086ad3257e3f042a63030725f7>@@_KD@Z
	DD	imagerel $ip2state$??$_Reallocate_grow_by@V<lambda_e1befb086ad3257e3f042a63030725f7>@@_KD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_e1befb086ad3257e3f042a63030725f7>@@_KD@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Reallocate_grow_by@V<lambda_e1befb086ad3257e3f042a63030725f7>@@_KD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_e1befb086ad3257e3f042a63030725f7>@@_KD@Z DD 021b11H
	DD	01f011bH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$_Reallocate_grow_by@V<lambda_e1befb086ad3257e3f042a63030725f7>@@_KD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_e1befb086ad3257e3f042a63030725f7>@@_KD@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z DD 010901H
	DD	0a209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$_Reallocate_grow_by@V<lambda_65e615be2a453ca0576c979606f46740>@@PEBD_K@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_65e615be2a453ca0576c979606f46740>@@PEBD_K@Z DB 06H
	DB	00H
	DB	00H
	DB	019H, 09H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$_Reallocate_grow_by@V<lambda_65e615be2a453ca0576c979606f46740>@@PEBD_K@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_65e615be2a453ca0576c979606f46740>@@PEBD_K@Z DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$_Reallocate_grow_by@V<lambda_65e615be2a453ca0576c979606f46740>@@PEBD_K@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_65e615be2a453ca0576c979606f46740>@@PEBD_K@Z DB 028H
	DD	imagerel $stateUnwindMap$??$_Reallocate_grow_by@V<lambda_65e615be2a453ca0576c979606f46740>@@PEBD_K@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_65e615be2a453ca0576c979606f46740>@@PEBD_K@Z
	DD	imagerel $ip2state$??$_Reallocate_grow_by@V<lambda_65e615be2a453ca0576c979606f46740>@@PEBD_K@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_65e615be2a453ca0576c979606f46740>@@PEBD_K@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Reallocate_grow_by@V<lambda_65e615be2a453ca0576c979606f46740>@@PEBD_K@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_65e615be2a453ca0576c979606f46740>@@PEBD_K@Z DD 021b11H
	DD	021011bH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$_Reallocate_grow_by@V<lambda_65e615be2a453ca0576c979606f46740>@@PEBD_K@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_65e615be2a453ca0576c979606f46740>@@PEBD_K@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Construct@$01PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z DD 031701H
	DD	01e0117H
	DD	07010H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z DD 031701H
	DD	0200117H
	DD	07010H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Construct@$01PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z DD 031701H
	DD	01e0117H
	DD	07010H
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DB	016H
	DB	0b6H
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?GetKnightageId@RedisPacket@mu2@@YAIAEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z DD 021b19H
	DD	011010cH
	DD	imagerel __GSHandlerCheck
	DD	078H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?GetRedisEventCode@RedisPacket@mu2@@YAGAEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DW	017H
	DW	0460H
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$2@?0??ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$1@?0??ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0??ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ DB 014H
	DB	00H
	DB	00H
	DB	0b5H, 02H
	DB	02H
	DB	'R'
	DB	04H
	DB	'@'
	DB	06H
	DB	'R'
	DB	08H
	DB	'}', 02H
	DB	0aH
	DB	'^'
	DB	0cH
	DB	'%', 09H
	DB	08H
	DB	01cH
	DB	04H
	DB	01cH
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ DB 0cH
	DB	0eH
	DD	imagerel __std_terminate
	DB	036H
	DD	imagerel ?dtor$0@?0??ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA
	DB	02eH
	DD	imagerel __std_terminate
	DB	056H
	DD	imagerel ?dtor$1@?0??ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA
	DB	02eH
	DD	imagerel __std_terminate
	DB	056H
	DD	imagerel ?dtor$2@?0??ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ DB 028H
	DD	imagerel $stateUnwindMap$?ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ
	DD	imagerel $ip2state$?ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ DD 031f19H
	DD	030010dH
	DD	07006H
	DD	imagerel __GSHandlerCheck_EH4
	DD	imagerel $cppxdata$?ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ
	DD	0172H
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DW	016H
	DW	0208H
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$2@?0??Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$1@?0??Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0??Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ DB 014H
	DB	00H
	DB	00H
	DB	'f'
	DB	02H
	DB	'N'
	DB	04H
	DB	'<'
	DB	08H
	DB	01cH
	DB	0aH
	DB	09eH
	DB	00H
	DB	' '
	DB	0aH
	DB	092H
	DB	00H
	DB	' '
	DB	0aH
	DB	0fdH, 02H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ DB 0aH
	DB	0eH
	DD	imagerel ?dtor$0@?0??Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA
	DB	02eH
	DD	imagerel ?dtor$1@?0??Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA
	DB	02eH
	DD	imagerel ?dtor$2@?0??Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA
	DB	07eH
	DD	imagerel ?dtor$2@?0??Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA
	DB	0aeH
	DD	imagerel ?dtor$2@?0??Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ DB 028H
	DD	imagerel $stateUnwindMap$?Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ
	DD	imagerel $ip2state$?Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ DD 021e19H
	DD	01b010cH
	DD	imagerel __GSHandlerCheck_EH4
	DD	imagerel $cppxdata$?Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ
	DD	0caH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$2@?0???0AppChatParser@RedisPacket@mu2@@QEAA@IIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$1@?0???0AppChatParser@RedisPacket@mu2@@QEAA@IIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???0AppChatParser@RedisPacket@mu2@@QEAA@IIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??0AppChatParser@RedisPacket@mu2@@QEAA@IIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z DB 0aH
	DB	00H
	DB	00H
	DB	08aH
	DB	02H
	DB	'.'
	DB	04H
	DB	'('
	DB	06H
	DB	'.'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??0AppChatParser@RedisPacket@mu2@@QEAA@IIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z DB 06H
	DB	0eH
	DD	imagerel ?dtor$0@?0???0AppChatParser@RedisPacket@mu2@@QEAA@IIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z@4HA
	DB	02eH
	DD	imagerel ?dtor$1@?0???0AppChatParser@RedisPacket@mu2@@QEAA@IIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z@4HA
	DB	02eH
	DD	imagerel ?dtor$2@?0???0AppChatParser@RedisPacket@mu2@@QEAA@IIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??0AppChatParser@RedisPacket@mu2@@QEAA@IIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z DB 028H
	DD	imagerel $stateUnwindMap$??0AppChatParser@RedisPacket@mu2@@QEAA@IIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z
	DD	imagerel $ip2state$??0AppChatParser@RedisPacket@mu2@@QEAA@IIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0AppChatParser@RedisPacket@mu2@@QEAA@IIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z DD 011711H
	DD	04217H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??0AppChatParser@RedisPacket@mu2@@QEAA@IIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$2@?0???0AppChatParser@RedisPacket@mu2@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$1@?0???0AppChatParser@RedisPacket@mu2@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???0AppChatParser@RedisPacket@mu2@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??0AppChatParser@RedisPacket@mu2@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z DB 0aH
	DB	00H
	DB	00H
	DB	'@'
	DB	02H
	DB	'$'
	DB	04H
	DB	'2'
	DB	06H
	DB	'.'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??0AppChatParser@RedisPacket@mu2@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z DB 06H
	DB	0eH
	DD	imagerel ?dtor$0@?0???0AppChatParser@RedisPacket@mu2@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@4HA
	DB	02eH
	DD	imagerel ?dtor$1@?0???0AppChatParser@RedisPacket@mu2@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@4HA
	DB	02eH
	DD	imagerel ?dtor$2@?0???0AppChatParser@RedisPacket@mu2@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??0AppChatParser@RedisPacket@mu2@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z DB 028H
	DD	imagerel $stateUnwindMap$??0AppChatParser@RedisPacket@mu2@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
	DD	imagerel $ip2state$??0AppChatParser@RedisPacket@mu2@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0AppChatParser@RedisPacket@mu2@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z DD 010e11H
	DD	0420eH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??0AppChatParser@RedisPacket@mu2@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?_Tidy@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@AEAAXXZ DB 06H
	DB	00H
	DB	00H
	DB	0b1H, 04H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?_Tidy@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@AEAAXXZ DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?_Tidy@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@AEAAXXZ DB 068H
	DD	imagerel $stateUnwindMap$?_Tidy@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@AEAAXXZ
	DD	imagerel $ip2state$?_Tidy@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@AEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Tidy@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@AEAAXXZ DD 020c19H
	DD	015010cH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?_Tidy@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@AEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DB 06H
	DB	00H
	DB	00H
	DB	089H, 02H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DB 068H
	DD	imagerel $stateUnwindMap$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
	DD	imagerel $ip2state$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DD 010919H
	DD	0e209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Eos@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAX_K@Z DD 010e01H
	DD	0620eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Calculate_growth@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@CA_K_K00@Z DD 011301H
	DD	06213H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ DD 020c01H
	DD	011010cH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?append@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV12@_K_W@Z DD 021501H
	DD	07011d215H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ DD 020a01H
	DD	07006120aH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z DB 06H
	DB	00H
	DB	00H
	DB	0eeH
	DB	02H
	DB	']', 02H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z DB 028H
	DD	imagerel $stateUnwindMap$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z
	DD	imagerel $ip2state$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z DD 021211H
	DD	0700bf212H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ DB 02H
	DB	00H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ DB 060H
	DD	imagerel $ip2state$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ DD 020a19H
	DD	07006720aH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEAAPEA_WXZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ DB 06H
	DB	00H
	DB	00H
	DB	'q', 02H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ DB 068H
	DD	imagerel $stateUnwindMap$?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ
	DD	imagerel $ip2state$?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ DD 010919H
	DD	0e209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Eos@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAX_K@Z DD 010e01H
	DD	0620eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Calculate_growth@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@CA_K_K00@Z DD 011301H
	DD	06213H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?compare@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBAHQEBD@Z DD 021101H
	DD	0150111H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?find@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBA_KD_K@Z DD 011201H
	DD	0a212H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?max_size@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBA_KXZ DD 020c01H
	DD	011010cH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?c_str@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBAPEBDXZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@_KD@Z DD 021401H
	DD	070109214H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z DD 021401H
	DD	070109214H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@AEBV12@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Construct_empty@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ DD 020a01H
	DD	07006120aH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@QEBD@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@QEBD@Z DB 06H
	DB	00H
	DB	00H
	DB	0b4H
	DB	02H
	DB	'b'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@QEBD@Z DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@QEBD@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@QEBD@Z DB 028H
	DD	imagerel $stateUnwindMap$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@QEBD@Z
	DD	imagerel $ip2state$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@QEBD@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@QEBD@Z DD 020f11H
	DD	0700b920fH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@QEBD@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@_K1AEBV?$allocator@D@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@_K1AEBV?$allocator@D@1@@Z DB 06H
	DB	00H
	DB	00H
	DB	0d4H
	DB	02H
	DB	0edH, 04H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@_K1AEBV?$allocator@D@1@@Z DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@_K1AEBV?$allocator@D@1@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@_K1AEBV?$allocator@D@1@@Z DB 028H
	DD	imagerel $stateUnwindMap$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@_K1AEBV?$allocator@D@1@@Z
	DD	imagerel $ip2state$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@_K1AEBV?$allocator@D@1@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@_K1AEBV?$allocator@D@1@@Z DD 031c11H
	DD	014011cH
	DD	07015H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@_K1AEBV?$allocator@D@1@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@@Z DB 06H
	DB	00H
	DB	00H
	DB	0eeH
	DB	02H
	DB	']', 02H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@@Z DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@@Z DB 028H
	DD	imagerel $stateUnwindMap$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@@Z
	DD	imagerel $ip2state$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@@Z DD 021211H
	DD	0700bf212H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ DB 02H
	DB	00H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ DB 060H
	DD	imagerel $ip2state$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ DD 020a19H
	DD	07006720aH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Xran@?$_String_val@U?$_Simple_types@D@std@@@std@@SAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Myptr@?$_String_val@U?$_Simple_types@D@std@@@std@@QEBAPEBDXZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Myptr@?$_String_val@U?$_Simple_types@D@std@@@std@@QEAAPEADXZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0?$_String_val@U?$_Simple_types@D@std@@@std@@QEAA@XZ DD 020a01H
	DD	07006120aH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?allocate@?$allocator@D@std@@QEAAPEAD_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Xlen_string@std@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Throw_bad_array_new_length@std@@YAXXZ DD 010401H
	DD	08204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1bad_array_new_length@std@@UEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_array_new_length@std@@QEAA@XZ DD 010601H
	DD	07006H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z DD 020f01H
	DD	0700b320fH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gexception@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?what@exception@std@@UEBAPEBDXZ DD 010901H
	DD	02209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0exception@std@@QEAA@AEBV01@@Z DD 020f01H
	DD	0700b320fH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$sprintf_s DD 011801H
	DD	0c218H
xdata	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\wchar.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\wchar.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??$_Reallocate_grow_by@V<lambda_a3050a43f3157934f354774ab3dd2e02>@@_K_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_a3050a43f3157934f354774ab3dd2e02>@@_K_W@Z
_TEXT	SEGMENT
_Old_size$ = 32
_My_data$ = 40
$T1 = 48
$T2 = 50
_New_capacity$ = 56
_Raw_new$ = 64
_N$ = 72
_Su$3 = 80
_Bytes$ = 88
_N$ = 96
_Su$4 = 104
_New_ptr$ = 112
_Fancy_ptr$5 = 120
_New_size$ = 128
_First$ = 136
_Old_capacity$ = 144
_Old_ptr$6 = 152
_Ptr$ = 160
_First$ = 168
$T7 = 176
$T8 = 184
$T9 = 192
_Al$ = 200
$T10 = 208
$T11 = 216
_Ptr$ = 224
$T12 = 232
$T13 = 240
$T14 = 248
$T15 = 256
$T16 = 264
$T17 = 272
$T18 = 280
$T19 = 288
this$ = 320
_Size_increase$ = 328
_Fn$ = 336
<_Args_0>$ = 344
<_Args_1>$ = 352
??$_Reallocate_grow_by@V<lambda_a3050a43f3157934f354774ab3dd2e02>@@_K_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_a3050a43f3157934f354774ab3dd2e02>@@_K_W@Z PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Reallocate_grow_by<<lambda_a3050a43f3157934f354774ab3dd2e02>,unsigned __int64,wchar_t>, COMDAT

; 3024 :     _CONSTEXPR20 basic_string& _Reallocate_grow_by(const size_type _Size_increase, _Fty _Fn, _ArgTys... _Args) {

$LN215:
  00000	4c 89 4c 24 20	 mov	 QWORD PTR [rsp+32], r9
  00005	44 88 44 24 18	 mov	 BYTE PTR [rsp+24], r8b
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	48 81 ec 38 01
	00 00		 sub	 rsp, 312		; 00000138H

; 3025 :         // reallocate to increase size by _Size_increase elements, new buffer prepared by
; 3026 :         // _Fn(_New_ptr, _Old_ptr, _Old_size, _Args...)
; 3027 :         auto& _My_data            = _Mypair._Myval2;

  0001b	48 8b 84 24 40
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 89 44 24 28	 mov	 QWORD PTR _My_data$[rsp], rax

; 3028 :         const size_type _Old_size = _My_data._Mysize;

  00028	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  0002d	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00031	48 89 44 24 20	 mov	 QWORD PTR _Old_size$[rsp], rax

; 3029 :         if (max_size() - _Old_size < _Size_increase) {

  00036	48 8b 8c 24 40
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0003e	e8 00 00 00 00	 call	 ?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
  00043	48 2b 44 24 20	 sub	 rax, QWORD PTR _Old_size$[rsp]
  00048	48 3b 84 24 48
	01 00 00	 cmp	 rax, QWORD PTR _Size_increase$[rsp]
  00050	73 06		 jae	 SHORT $LN2@Reallocate

; 3030 :             _Xlen_string(); // result too long

  00052	e8 00 00 00 00	 call	 ?_Xlen_string@std@@YAXXZ ; std::_Xlen_string
  00057	90		 npad	 1
$LN2@Reallocate:

; 3031 :         }
; 3032 : 
; 3033 :         const size_type _New_size     = _Old_size + _Size_increase;

  00058	48 8b 84 24 48
	01 00 00	 mov	 rax, QWORD PTR _Size_increase$[rsp]
  00060	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Old_size$[rsp]
  00065	48 03 c8	 add	 rcx, rax
  00068	48 8b c1	 mov	 rax, rcx
  0006b	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _New_size$[rsp], rax

; 3034 :         const size_type _Old_capacity = _My_data._Myres;

  00073	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00078	48 8b 40 18	 mov	 rax, QWORD PTR [rax+24]
  0007c	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR _Old_capacity$[rsp], rax

; 2991 :         return _Calculate_growth(_Requested, _Mypair._Myval2._Myres, max_size());

  00084	48 8b 8c 24 40
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0008c	e8 00 00 00 00	 call	 ?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
  00091	4c 8b c0	 mov	 r8, rax
  00094	48 8b 84 24 40
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0009c	48 8b 50 18	 mov	 rdx, QWORD PTR [rax+24]
  000a0	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR _New_size$[rsp]
  000a8	e8 00 00 00 00	 call	 ?_Calculate_growth@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@CA_K_K00@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Calculate_growth
  000ad	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T7[rsp], rax

; 3035 :         size_type _New_capacity       = _Calculate_growth(_New_size);

  000b5	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR $T7[rsp]
  000bd	48 89 44 24 38	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 3107 :         return _Mypair._Get_first();

  000c2	48 8b 84 24 40
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  000ca	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3107 :         return _Mypair._Get_first();

  000d2	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T8[rsp]
  000da	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax

; 3036 :         auto& _Al                     = _Getal();

  000e2	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR $T9[rsp]
  000ea	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR _Al$[rsp], rax

; 825  :         ++_Capacity; // Take null terminator into consideration

  000f2	48 8b 44 24 38	 mov	 rax, QWORD PTR _New_capacity$[rsp]
  000f7	48 ff c0	 inc	 rax
  000fa	48 89 44 24 38	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 826  : 
; 827  :         pointer _Fancy_ptr = nullptr;

  000ff	48 c7 44 24 78
	00 00 00 00	 mov	 QWORD PTR _Fancy_ptr$5[rsp], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 2303 :         return _Al.allocate(_Count);

  00108	48 8b 54 24 38	 mov	 rdx, QWORD PTR _New_capacity$[rsp]
  0010d	48 8b 8c 24 c8
	00 00 00	 mov	 rcx, QWORD PTR _Al$[rsp]
  00115	e8 00 00 00 00	 call	 ?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z ; std::allocator<wchar_t>::allocate
  0011a	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 829  :             _Fancy_ptr = _Allocate_at_least_helper(_Al, _Capacity);

  00122	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  0012a	48 89 44 24 78	 mov	 QWORD PTR _Fancy_ptr$5[rsp], rax

; 830  :         } else {
; 831  :             _STL_INTERNAL_STATIC_ASSERT(_Policy == _Allocation_policy::_Exactly);
; 832  :             _Fancy_ptr = _Al.allocate(_Capacity);
; 833  :         }
; 834  : 
; 835  : #if _HAS_CXX20
; 836  :         // Start element lifetimes to avoid UB. This is a more general mechanism than _String_val::_Activate_SSO_buffer,
; 837  :         // but likely more impactful to throughput.
; 838  :         if (_STD is_constant_evaluated()) {
; 839  :             _Elem* const _Ptr = _Unfancy(_Fancy_ptr);
; 840  :             for (size_type _Idx = 0; _Idx < _Capacity; ++_Idx) {
; 841  :                 _STD construct_at(_Ptr + _Idx);
; 842  :             }
; 843  :         }
; 844  : #endif // _HAS_CXX20
; 845  :         --_Capacity;

  0012f	48 8b 44 24 38	 mov	 rax, QWORD PTR _New_capacity$[rsp]
  00134	48 ff c8	 dec	 rax
  00137	48 89 44 24 38	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 846  :         return _Fancy_ptr;

  0013c	48 8b 44 24 78	 mov	 rax, QWORD PTR _Fancy_ptr$5[rsp]
  00141	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax

; 3037 :         const pointer _New_ptr        = _Allocate_for_capacity(_Al, _New_capacity); // throws

  00149	48 8b 84 24 d8
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  00151	48 89 44 24 70	 mov	 QWORD PTR _New_ptr$[rsp], rax

; 3038 : 
; 3039 :         _My_data._Orphan_all();
; 3040 :         _ASAN_STRING_REMOVE(*this);
; 3041 :         _My_data._Mysize      = _New_size;

  00156	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  0015b	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR _New_size$[rsp]
  00163	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 3042 :         _My_data._Myres       = _New_capacity;

  00167	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  0016c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _New_capacity$[rsp]
  00171	48 89 48 18	 mov	 QWORD PTR [rax+24], rcx

; 3043 :         _Elem* const _Raw_new = _Unfancy(_New_ptr);

  00175	48 8b 44 24 70	 mov	 rax, QWORD PTR _New_ptr$[rsp]
  0017a	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  00182	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR _Ptr$[rsp]
  0018a	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3043 :         _Elem* const _Raw_new = _Unfancy(_New_ptr);

  00192	48 8b 84 24 e8
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
  0019a	48 89 44 24 40	 mov	 QWORD PTR _Raw_new$[rsp], rax

; 3044 :         if (_Old_capacity > _Small_string_capacity) {

  0019f	48 83 bc 24 90
	00 00 00 07	 cmp	 QWORD PTR _Old_capacity$[rsp], 7
  001a8	0f 86 45 01 00
	00		 jbe	 $LN3@Reallocate

; 3045 :             const pointer _Old_ptr = _My_data._Bx._Ptr;

  001ae	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  001b3	48 8b 00	 mov	 rax, QWORD PTR [rax]
  001b6	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR _Old_ptr$6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  001be	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR _Old_ptr$6[rsp]
  001c6	48 89 84 24 f0
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  001ce	48 8b 44 24 20	 mov	 rax, QWORD PTR _Old_size$[rsp]
  001d3	48 03 c0	 add	 rax, rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3046 :             _Fn(_Raw_new, _Unfancy(_Old_ptr), _Old_size, _Args...);

  001d6	48 8b 8c 24 f0
	00 00 00	 mov	 rcx, QWORD PTR $T13[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  001de	4c 8b c0	 mov	 r8, rax
  001e1	48 8b d1	 mov	 rdx, rcx
  001e4	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Raw_new$[rsp]
  001e9	e8 00 00 00 00	 call	 memcpy
  001ee	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1554 :                 _Traits::assign(_New_ptr + _Old_size, _Count, _Ch);

  001ef	48 8b 44 24 40	 mov	 rax, QWORD PTR _Raw_new$[rsp]
  001f4	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Old_size$[rsp]
  001f9	48 8d 04 48	 lea	 rax, QWORD PTR [rax+rcx*2]
  001fd	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR _First$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 338  :         return reinterpret_cast<_Elem*>(_CSTD wmemset(reinterpret_cast<wchar_t*>(_First), _Ch, _Count));

  00205	48 8b 84 24 58
	01 00 00	 mov	 rax, QWORD PTR <_Args_0>$[rsp]
  0020d	48 89 44 24 48	 mov	 QWORD PTR _N$[rsp], rax
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\wchar.h

; 510  :         wchar_t *_Su = _S;

  00212	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR _First$[rsp]
  0021a	48 89 44 24 50	 mov	 QWORD PTR _Su$3[rsp], rax

; 511  :         for (; 0 < _N; ++_Su, --_N)

  0021f	eb 1b		 jmp	 SHORT $LN145@Reallocate
$LN143@Reallocate:
  00221	48 8b 44 24 50	 mov	 rax, QWORD PTR _Su$3[rsp]
  00226	48 83 c0 02	 add	 rax, 2
  0022a	48 89 44 24 50	 mov	 QWORD PTR _Su$3[rsp], rax
  0022f	48 8b 44 24 48	 mov	 rax, QWORD PTR _N$[rsp]
  00234	48 ff c8	 dec	 rax
  00237	48 89 44 24 48	 mov	 QWORD PTR _N$[rsp], rax
$LN145@Reallocate:
  0023c	48 83 7c 24 48
	00		 cmp	 QWORD PTR _N$[rsp], 0
  00242	76 12		 jbe	 SHORT $LN144@Reallocate

; 512  :         {
; 513  :             *_Su = _C;

  00244	48 8b 44 24 50	 mov	 rax, QWORD PTR _Su$3[rsp]
  00249	0f b7 8c 24 60
	01 00 00	 movzx	 ecx, WORD PTR <_Args_1>$[rsp]
  00251	66 89 08	 mov	 WORD PTR [rax], cx

; 514  :         }

  00254	eb cb		 jmp	 SHORT $LN143@Reallocate
$LN144@Reallocate:

; 515  :         return _S;

  00256	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR _First$[rsp]
  0025e	48 89 84 24 18
	01 00 00	 mov	 QWORD PTR $T18[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1555 :                 _Traits::assign(_New_ptr[_Old_size + _Count], _Elem());

  00266	33 c0		 xor	 eax, eax
  00268	66 89 44 24 30	 mov	 WORD PTR $T1[rsp], ax
  0026d	48 8b 84 24 58
	01 00 00	 mov	 rax, QWORD PTR <_Args_0>$[rsp]
  00275	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Old_size$[rsp]
  0027a	48 03 c8	 add	 rcx, rax
  0027d	48 8b c1	 mov	 rax, rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 347  :         _Left = _Right;

  00280	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Raw_new$[rsp]
  00285	0f b7 54 24 30	 movzx	 edx, WORD PTR $T1[rsp]
  0028a	66 89 14 41	 mov	 WORD PTR [rcx+rax*2], dx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  0028e	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR _Old_capacity$[rsp]
  00296	48 8d 44 00 02	 lea	 rax, QWORD PTR [rax+rax+2]
  0029b	48 89 44 24 58	 mov	 QWORD PTR _Bytes$[rsp], rax
  002a0	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR _Old_ptr$6[rsp]
  002a8	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  002b0	48 81 7c 24 58
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  002b9	72 13		 jb	 SHORT $LN163@Reallocate

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  002bb	48 8d 54 24 58	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  002c0	48 8d 8c 24 a0
	00 00 00	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  002c8	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  002cd	90		 npad	 1
$LN163@Reallocate:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  002ce	48 8b 54 24 58	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  002d3	48 8b 8c 24 a0
	00 00 00	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  002db	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  002e0	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3048 :             _My_data._Bx._Ptr = _New_ptr;

  002e1	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  002e6	48 8b 4c 24 70	 mov	 rcx, QWORD PTR _New_ptr$[rsp]
  002eb	48 89 08	 mov	 QWORD PTR [rax], rcx

; 3049 :         } else {

  002ee	e9 0d 01 00 00	 jmp	 $LN4@Reallocate
$LN3@Reallocate:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  002f3	48 8b 44 24 20	 mov	 rax, QWORD PTR _Old_size$[rsp]
  002f8	48 03 c0	 add	 rax, rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3050 :             _Fn(_Raw_new, _My_data._Bx._Buf, _Old_size, _Args...);

  002fb	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _My_data$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  00300	4c 8b c0	 mov	 r8, rax
  00303	48 8b d1	 mov	 rdx, rcx
  00306	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Raw_new$[rsp]
  0030b	e8 00 00 00 00	 call	 memcpy
  00310	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1554 :                 _Traits::assign(_New_ptr + _Old_size, _Count, _Ch);

  00311	48 8b 44 24 40	 mov	 rax, QWORD PTR _Raw_new$[rsp]
  00316	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Old_size$[rsp]
  0031b	48 8d 04 48	 lea	 rax, QWORD PTR [rax+rcx*2]
  0031f	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR _First$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 338  :         return reinterpret_cast<_Elem*>(_CSTD wmemset(reinterpret_cast<wchar_t*>(_First), _Ch, _Count));

  00327	48 8b 84 24 58
	01 00 00	 mov	 rax, QWORD PTR <_Args_0>$[rsp]
  0032f	48 89 44 24 60	 mov	 QWORD PTR _N$[rsp], rax
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\wchar.h

; 510  :         wchar_t *_Su = _S;

  00334	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR _First$[rsp]
  0033c	48 89 44 24 68	 mov	 QWORD PTR _Su$4[rsp], rax

; 511  :         for (; 0 < _N; ++_Su, --_N)

  00341	eb 1b		 jmp	 SHORT $LN187@Reallocate
$LN185@Reallocate:
  00343	48 8b 44 24 68	 mov	 rax, QWORD PTR _Su$4[rsp]
  00348	48 83 c0 02	 add	 rax, 2
  0034c	48 89 44 24 68	 mov	 QWORD PTR _Su$4[rsp], rax
  00351	48 8b 44 24 60	 mov	 rax, QWORD PTR _N$[rsp]
  00356	48 ff c8	 dec	 rax
  00359	48 89 44 24 60	 mov	 QWORD PTR _N$[rsp], rax
$LN187@Reallocate:
  0035e	48 83 7c 24 60
	00		 cmp	 QWORD PTR _N$[rsp], 0
  00364	76 12		 jbe	 SHORT $LN186@Reallocate

; 512  :         {
; 513  :             *_Su = _C;

  00366	48 8b 44 24 68	 mov	 rax, QWORD PTR _Su$4[rsp]
  0036b	0f b7 8c 24 60
	01 00 00	 movzx	 ecx, WORD PTR <_Args_1>$[rsp]
  00373	66 89 08	 mov	 WORD PTR [rax], cx

; 514  :         }

  00376	eb cb		 jmp	 SHORT $LN185@Reallocate
$LN186@Reallocate:

; 515  :         return _S;

  00378	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR _First$[rsp]
  00380	48 89 84 24 20
	01 00 00	 mov	 QWORD PTR $T19[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1555 :                 _Traits::assign(_New_ptr[_Old_size + _Count], _Elem());

  00388	33 c0		 xor	 eax, eax
  0038a	66 89 44 24 32	 mov	 WORD PTR $T2[rsp], ax
  0038f	48 8b 84 24 58
	01 00 00	 mov	 rax, QWORD PTR <_Args_0>$[rsp]
  00397	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Old_size$[rsp]
  0039c	48 03 c8	 add	 rcx, rax
  0039f	48 8b c1	 mov	 rax, rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 347  :         _Left = _Right;

  003a2	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Raw_new$[rsp]
  003a7	0f b7 54 24 32	 movzx	 edx, WORD PTR $T2[rsp]
  003ac	66 89 14 41	 mov	 WORD PTR [rcx+rax*2], dx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3051 :             _Construct_in_place(_My_data._Bx._Ptr, _New_ptr);

  003b0	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  003b5	48 89 84 24 f8
	00 00 00	 mov	 QWORD PTR $T14[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  003bd	48 8b 84 24 f8
	00 00 00	 mov	 rax, QWORD PTR $T14[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  003c5	48 89 84 24 00
	01 00 00	 mov	 QWORD PTR $T15[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  003cd	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR $T15[rsp]
  003d5	48 89 84 24 08
	01 00 00	 mov	 QWORD PTR $T16[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  003dd	48 8d 44 24 70	 lea	 rax, QWORD PTR _New_ptr$[rsp]
  003e2	48 89 84 24 10
	01 00 00	 mov	 QWORD PTR $T17[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  003ea	48 8b 84 24 08
	01 00 00	 mov	 rax, QWORD PTR $T16[rsp]
  003f2	48 8b 8c 24 10
	01 00 00	 mov	 rcx, QWORD PTR $T17[rsp]
  003fa	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  003fd	48 89 08	 mov	 QWORD PTR [rax], rcx
$LN4@Reallocate:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3055 :         return *this;

  00400	48 8b 84 24 40
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]

; 3056 :     }

  00408	48 81 c4 38 01
	00 00		 add	 rsp, 312		; 00000138H
  0040f	c3		 ret	 0
$LN214@Reallocate:
??$_Reallocate_grow_by@V<lambda_a3050a43f3157934f354774ab3dd2e02>@@_K_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_a3050a43f3157934f354774ab3dd2e02>@@_K_W@Z ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Reallocate_grow_by<<lambda_a3050a43f3157934f354774ab3dd2e02>,unsigned __int64,wchar_t>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
;	COMDAT ??$_Find_vectorized@$$CBDD@std@@YAPEBDQEBD0D@Z
_TEXT	SEGMENT
_First$ = 48
_Last$ = 56
_Val$ = 64
??$_Find_vectorized@$$CBDD@std@@YAPEBDQEBD0D@Z PROC	; std::_Find_vectorized<char const ,char>, COMDAT

; 190  : _Ty* _Find_vectorized(_Ty* const _First, _Ty* const _Last, const _TVal _Val) noexcept {

$LN4:
  00000	44 88 44 24 18	 mov	 BYTE PTR [rsp+24], r8b
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 191  :     if constexpr (is_pointer_v<_TVal> || is_null_pointer_v<_TVal>) {
; 192  : #ifdef _WIN64
; 193  :         return const_cast<_Ty*>(
; 194  :             static_cast<const _Ty*>(::__std_find_trivial_8(_First, _Last, reinterpret_cast<uint64_t>(_Val))));
; 195  : #else
; 196  :         return const_cast<_Ty*>(
; 197  :             static_cast<const _Ty*>(::__std_find_trivial_4(_First, _Last, reinterpret_cast<uint32_t>(_Val))));
; 198  : #endif
; 199  :     } else if constexpr (sizeof(_Ty) == 1) {
; 200  :         return const_cast<_Ty*>(

  00013	44 0f b6 44 24
	40		 movzx	 r8d, BYTE PTR _Val$[rsp]
  00019	48 8b 54 24 38	 mov	 rdx, QWORD PTR _Last$[rsp]
  0001e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _First$[rsp]
  00023	e8 00 00 00 00	 call	 __std_find_trivial_1

; 201  :             static_cast<const _Ty*>(::__std_find_trivial_1(_First, _Last, static_cast<uint8_t>(_Val))));
; 202  :     } else if constexpr (sizeof(_Ty) == 2) {
; 203  :         return const_cast<_Ty*>(
; 204  :             static_cast<const _Ty*>(::__std_find_trivial_2(_First, _Last, static_cast<uint16_t>(_Val))));
; 205  :     } else if constexpr (sizeof(_Ty) == 4) {
; 206  :         return const_cast<_Ty*>(
; 207  :             static_cast<const _Ty*>(::__std_find_trivial_4(_First, _Last, static_cast<uint32_t>(_Val))));
; 208  :     } else if constexpr (sizeof(_Ty) == 8) {
; 209  :         return const_cast<_Ty*>(
; 210  :             static_cast<const _Ty*>(::__std_find_trivial_8(_First, _Last, static_cast<uint64_t>(_Val))));
; 211  :     } else {
; 212  :         _STL_INTERNAL_STATIC_ASSERT(false); // unexpected size
; 213  :     }
; 214  : }

  00028	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0002c	c3		 ret	 0
??$_Find_vectorized@$$CBDD@std@@YAPEBDQEBD0D@Z ENDP	; std::_Find_vectorized<char const ,char>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtr1common
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
;	COMDAT ??$_Traits_find_ch@U?$char_traits@D@std@@@std@@YA_KQEBD_K1D@Z
_TEXT	SEGMENT
$T1 = 32
_End$2 = 40
_Ptr$3 = 48
_Found_at$ = 56
tv80 = 64
$T4 = 72
_Haystack$ = 96
_Hay_size$ = 104
_Start_at$ = 112
_Ch$ = 120
??$_Traits_find_ch@U?$char_traits@D@std@@@std@@YA_KQEBD_K1D@Z PROC ; std::_Traits_find_ch<std::char_traits<char> >, COMDAT

; 687  :     const size_t _Start_at, const _Traits_ch_t<_Traits> _Ch) noexcept {

$LN19:
  00000	44 88 4c 24 20	 mov	 BYTE PTR [rsp+32], r9b
  00005	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 688  :     // search [_Haystack, _Haystack + _Hay_size) for _Ch, at/after _Start_at
; 689  :     if (_Start_at >= _Hay_size) {

  00018	48 8b 44 24 68	 mov	 rax, QWORD PTR _Hay_size$[rsp]
  0001d	48 39 44 24 70	 cmp	 QWORD PTR _Start_at$[rsp], rax
  00022	72 0c		 jb	 SHORT $LN2@Traits_fin

; 690  :         return static_cast<size_t>(-1); // (npos) no room for match

  00024	48 c7 c0 ff ff
	ff ff		 mov	 rax, -1
  0002b	e9 e3 00 00 00	 jmp	 $LN1@Traits_fin
$LN2@Traits_fin:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtr1common

; 178  :     return __builtin_is_constant_evaluated();

  00030	c6 44 24 20 00	 mov	 BYTE PTR $T1[rsp], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 695  :         if (!_STD _Is_constant_evaluated()) {

  00035	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  0003a	0f b6 c0	 movzx	 eax, al
  0003d	85 c0		 test	 eax, eax
  0003f	75 66		 jne	 SHORT $LN3@Traits_fin

; 696  :             const auto _End = _Haystack + _Hay_size;

  00041	48 8b 44 24 68	 mov	 rax, QWORD PTR _Hay_size$[rsp]
  00046	48 8b 4c 24 60	 mov	 rcx, QWORD PTR _Haystack$[rsp]
  0004b	48 03 c8	 add	 rcx, rax
  0004e	48 8b c1	 mov	 rax, rcx
  00051	48 89 44 24 28	 mov	 QWORD PTR _End$2[rsp], rax

; 697  :             const auto _Ptr = _STD _Find_vectorized(_Haystack + _Start_at, _End, _Ch);

  00056	48 8b 44 24 70	 mov	 rax, QWORD PTR _Start_at$[rsp]
  0005b	48 8b 4c 24 60	 mov	 rcx, QWORD PTR _Haystack$[rsp]
  00060	48 03 c8	 add	 rcx, rax
  00063	48 8b c1	 mov	 rax, rcx
  00066	44 0f b6 44 24
	78		 movzx	 r8d, BYTE PTR _Ch$[rsp]
  0006c	48 8b 54 24 28	 mov	 rdx, QWORD PTR _End$2[rsp]
  00071	48 8b c8	 mov	 rcx, rax
  00074	e8 00 00 00 00	 call	 ??$_Find_vectorized@$$CBDD@std@@YAPEBDQEBD0D@Z ; std::_Find_vectorized<char const ,char>
  00079	48 89 44 24 30	 mov	 QWORD PTR _Ptr$3[rsp], rax

; 698  : 
; 699  :             if (_Ptr != _End) {

  0007e	48 8b 44 24 28	 mov	 rax, QWORD PTR _End$2[rsp]
  00083	48 39 44 24 30	 cmp	 QWORD PTR _Ptr$3[rsp], rax
  00088	74 14		 je	 SHORT $LN4@Traits_fin

; 700  :                 return static_cast<size_t>(_Ptr - _Haystack);

  0008a	48 8b 44 24 60	 mov	 rax, QWORD PTR _Haystack$[rsp]
  0008f	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Ptr$3[rsp]
  00094	48 2b c8	 sub	 rcx, rax
  00097	48 8b c1	 mov	 rax, rcx
  0009a	eb 77		 jmp	 SHORT $LN1@Traits_fin

; 701  :             } else {

  0009c	eb 09		 jmp	 SHORT $LN3@Traits_fin
$LN4@Traits_fin:

; 702  :                 return static_cast<size_t>(-1); // (npos) no match

  0009e	48 c7 c0 ff ff
	ff ff		 mov	 rax, -1
  000a5	eb 6c		 jmp	 SHORT $LN1@Traits_fin
$LN3@Traits_fin:

; 703  :             }
; 704  :         }
; 705  :     }
; 706  : #endif // _USE_STD_VECTOR_ALGORITHMS
; 707  : 
; 708  :     const auto _Found_at = _Traits::find(_Haystack + _Start_at, _Hay_size - _Start_at, _Ch);

  000a7	48 8b 44 24 70	 mov	 rax, QWORD PTR _Start_at$[rsp]
  000ac	48 8b 4c 24 68	 mov	 rcx, QWORD PTR _Hay_size$[rsp]
  000b1	48 2b c8	 sub	 rcx, rax
  000b4	48 8b c1	 mov	 rax, rcx

; 480  :         return static_cast<const _Elem*>(_CSTD memchr(_First, _Ch, _Count));

  000b7	0f be 4c 24 78	 movsx	 ecx, BYTE PTR _Ch$[rsp]

; 703  :             }
; 704  :         }
; 705  :     }
; 706  : #endif // _USE_STD_VECTOR_ALGORITHMS
; 707  : 
; 708  :     const auto _Found_at = _Traits::find(_Haystack + _Start_at, _Hay_size - _Start_at, _Ch);

  000bc	48 8b 54 24 70	 mov	 rdx, QWORD PTR _Start_at$[rsp]
  000c1	4c 8b 44 24 60	 mov	 r8, QWORD PTR _Haystack$[rsp]
  000c6	4c 03 c2	 add	 r8, rdx
  000c9	49 8b d0	 mov	 rdx, r8
  000cc	48 89 54 24 40	 mov	 QWORD PTR tv80[rsp], rdx

; 480  :         return static_cast<const _Elem*>(_CSTD memchr(_First, _Ch, _Count));

  000d1	4c 8b c0	 mov	 r8, rax
  000d4	8b d1		 mov	 edx, ecx
  000d6	48 8b 44 24 40	 mov	 rax, QWORD PTR tv80[rsp]
  000db	48 8b c8	 mov	 rcx, rax
  000de	e8 00 00 00 00	 call	 memchr
  000e3	48 89 44 24 48	 mov	 QWORD PTR $T4[rsp], rax

; 703  :             }
; 704  :         }
; 705  :     }
; 706  : #endif // _USE_STD_VECTOR_ALGORITHMS
; 707  : 
; 708  :     const auto _Found_at = _Traits::find(_Haystack + _Start_at, _Hay_size - _Start_at, _Ch);

  000e8	48 8b 44 24 48	 mov	 rax, QWORD PTR $T4[rsp]
  000ed	48 89 44 24 38	 mov	 QWORD PTR _Found_at$[rsp], rax

; 709  :     if (_Found_at) {

  000f2	48 83 7c 24 38
	00		 cmp	 QWORD PTR _Found_at$[rsp], 0
  000f8	74 12		 je	 SHORT $LN6@Traits_fin

; 710  :         return static_cast<size_t>(_Found_at - _Haystack);

  000fa	48 8b 44 24 60	 mov	 rax, QWORD PTR _Haystack$[rsp]
  000ff	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Found_at$[rsp]
  00104	48 2b c8	 sub	 rcx, rax
  00107	48 8b c1	 mov	 rax, rcx
  0010a	eb 07		 jmp	 SHORT $LN1@Traits_fin
$LN6@Traits_fin:

; 711  :     }
; 712  : 
; 713  :     return static_cast<size_t>(-1); // (npos) no match

  0010c	48 c7 c0 ff ff
	ff ff		 mov	 rax, -1
$LN1@Traits_fin:

; 714  : }

  00113	48 83 c4 58	 add	 rsp, 88			; 00000058H
  00117	c3		 ret	 0
??$_Traits_find_ch@U?$char_traits@D@std@@@std@@YA_KQEBD_K1D@Z ENDP ; std::_Traits_find_ch<std::char_traits<char> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??$_Reallocate_grow_by@V<lambda_e1befb086ad3257e3f042a63030725f7>@@_KD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_e1befb086ad3257e3f042a63030725f7>@@_KD@Z
_TEXT	SEGMENT
$T1 = 32
$T2 = 33
_Old_size$ = 40
_My_data$ = 48
_New_capacity$ = 56
_Raw_new$ = 64
_Bytes$ = 72
_New_ptr$ = 80
_Fancy_ptr$3 = 88
_New_size$ = 96
_Old_capacity$ = 104
_Old_ptr$4 = 112
_Ptr$ = 120
$T5 = 128
$T6 = 136
$T7 = 144
_Al$ = 152
$T8 = 160
$T9 = 168
_Ptr$ = 176
$T10 = 184
$T11 = 192
$T12 = 200
$T13 = 208
$T14 = 216
$T15 = 224
this$ = 256
_Size_increase$ = 264
_Fn$ = 272
<_Args_0>$ = 280
<_Args_1>$ = 288
??$_Reallocate_grow_by@V<lambda_e1befb086ad3257e3f042a63030725f7>@@_KD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_e1befb086ad3257e3f042a63030725f7>@@_KD@Z PROC ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_e1befb086ad3257e3f042a63030725f7>,unsigned __int64,char>, COMDAT

; 3024 :     _CONSTEXPR20 basic_string& _Reallocate_grow_by(const size_type _Size_increase, _Fty _Fn, _ArgTys... _Args) {

$LN203:
  00000	4c 89 4c 24 20	 mov	 QWORD PTR [rsp+32], r9
  00005	44 88 44 24 18	 mov	 BYTE PTR [rsp+24], r8b
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	48 81 ec f8 00
	00 00		 sub	 rsp, 248		; 000000f8H

; 3025 :         // reallocate to increase size by _Size_increase elements, new buffer prepared by
; 3026 :         // _Fn(_New_ptr, _Old_ptr, _Old_size, _Args...)
; 3027 :         auto& _My_data            = _Mypair._Myval2;

  0001b	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 89 44 24 30	 mov	 QWORD PTR _My_data$[rsp], rax

; 3028 :         const size_type _Old_size = _My_data._Mysize;

  00028	48 8b 44 24 30	 mov	 rax, QWORD PTR _My_data$[rsp]
  0002d	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00031	48 89 44 24 28	 mov	 QWORD PTR _Old_size$[rsp], rax

; 3029 :         if (max_size() - _Old_size < _Size_increase) {

  00036	48 8b 8c 24 00
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0003e	e8 00 00 00 00	 call	 ?max_size@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBA_KXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::max_size
  00043	48 2b 44 24 28	 sub	 rax, QWORD PTR _Old_size$[rsp]
  00048	48 3b 84 24 08
	01 00 00	 cmp	 rax, QWORD PTR _Size_increase$[rsp]
  00050	73 06		 jae	 SHORT $LN2@Reallocate

; 3030 :             _Xlen_string(); // result too long

  00052	e8 00 00 00 00	 call	 ?_Xlen_string@std@@YAXXZ ; std::_Xlen_string
  00057	90		 npad	 1
$LN2@Reallocate:

; 3031 :         }
; 3032 : 
; 3033 :         const size_type _New_size     = _Old_size + _Size_increase;

  00058	48 8b 84 24 08
	01 00 00	 mov	 rax, QWORD PTR _Size_increase$[rsp]
  00060	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Old_size$[rsp]
  00065	48 03 c8	 add	 rcx, rax
  00068	48 8b c1	 mov	 rax, rcx
  0006b	48 89 44 24 60	 mov	 QWORD PTR _New_size$[rsp], rax

; 3034 :         const size_type _Old_capacity = _My_data._Myres;

  00070	48 8b 44 24 30	 mov	 rax, QWORD PTR _My_data$[rsp]
  00075	48 8b 40 18	 mov	 rax, QWORD PTR [rax+24]
  00079	48 89 44 24 68	 mov	 QWORD PTR _Old_capacity$[rsp], rax

; 2991 :         return _Calculate_growth(_Requested, _Mypair._Myval2._Myres, max_size());

  0007e	48 8b 8c 24 00
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00086	e8 00 00 00 00	 call	 ?max_size@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBA_KXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::max_size
  0008b	4c 8b c0	 mov	 r8, rax
  0008e	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00096	48 8b 50 18	 mov	 rdx, QWORD PTR [rax+24]
  0009a	48 8b 4c 24 60	 mov	 rcx, QWORD PTR _New_size$[rsp]
  0009f	e8 00 00 00 00	 call	 ?_Calculate_growth@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@CA_K_K00@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Calculate_growth
  000a4	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T5[rsp], rax

; 3035 :         size_type _New_capacity       = _Calculate_growth(_New_size);

  000ac	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR $T5[rsp]
  000b4	48 89 44 24 38	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 3107 :         return _Mypair._Get_first();

  000b9	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  000c1	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3107 :         return _Mypair._Get_first();

  000c9	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T6[rsp]
  000d1	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T7[rsp], rax

; 3036 :         auto& _Al                     = _Getal();

  000d9	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR $T7[rsp]
  000e1	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR _Al$[rsp], rax

; 825  :         ++_Capacity; // Take null terminator into consideration

  000e9	48 8b 44 24 38	 mov	 rax, QWORD PTR _New_capacity$[rsp]
  000ee	48 ff c0	 inc	 rax
  000f1	48 89 44 24 38	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 826  : 
; 827  :         pointer _Fancy_ptr = nullptr;

  000f6	48 c7 44 24 58
	00 00 00 00	 mov	 QWORD PTR _Fancy_ptr$3[rsp], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 2303 :         return _Al.allocate(_Count);

  000ff	48 8b 54 24 38	 mov	 rdx, QWORD PTR _New_capacity$[rsp]
  00104	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR _Al$[rsp]
  0010c	e8 00 00 00 00	 call	 ?allocate@?$allocator@D@std@@QEAAPEAD_K@Z ; std::allocator<char>::allocate
  00111	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 829  :             _Fancy_ptr = _Allocate_at_least_helper(_Al, _Capacity);

  00119	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR $T8[rsp]
  00121	48 89 44 24 58	 mov	 QWORD PTR _Fancy_ptr$3[rsp], rax

; 830  :         } else {
; 831  :             _STL_INTERNAL_STATIC_ASSERT(_Policy == _Allocation_policy::_Exactly);
; 832  :             _Fancy_ptr = _Al.allocate(_Capacity);
; 833  :         }
; 834  : 
; 835  : #if _HAS_CXX20
; 836  :         // Start element lifetimes to avoid UB. This is a more general mechanism than _String_val::_Activate_SSO_buffer,
; 837  :         // but likely more impactful to throughput.
; 838  :         if (_STD is_constant_evaluated()) {
; 839  :             _Elem* const _Ptr = _Unfancy(_Fancy_ptr);
; 840  :             for (size_type _Idx = 0; _Idx < _Capacity; ++_Idx) {
; 841  :                 _STD construct_at(_Ptr + _Idx);
; 842  :             }
; 843  :         }
; 844  : #endif // _HAS_CXX20
; 845  :         --_Capacity;

  00126	48 8b 44 24 38	 mov	 rax, QWORD PTR _New_capacity$[rsp]
  0012b	48 ff c8	 dec	 rax
  0012e	48 89 44 24 38	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 846  :         return _Fancy_ptr;

  00133	48 8b 44 24 58	 mov	 rax, QWORD PTR _Fancy_ptr$3[rsp]
  00138	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax

; 3037 :         const pointer _New_ptr        = _Allocate_for_capacity(_Al, _New_capacity); // throws

  00140	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T9[rsp]
  00148	48 89 44 24 50	 mov	 QWORD PTR _New_ptr$[rsp], rax

; 3038 : 
; 3039 :         _My_data._Orphan_all();
; 3040 :         _ASAN_STRING_REMOVE(*this);
; 3041 :         _My_data._Mysize      = _New_size;

  0014d	48 8b 44 24 30	 mov	 rax, QWORD PTR _My_data$[rsp]
  00152	48 8b 4c 24 60	 mov	 rcx, QWORD PTR _New_size$[rsp]
  00157	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 3042 :         _My_data._Myres       = _New_capacity;

  0015b	48 8b 44 24 30	 mov	 rax, QWORD PTR _My_data$[rsp]
  00160	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _New_capacity$[rsp]
  00165	48 89 48 18	 mov	 QWORD PTR [rax+24], rcx

; 3043 :         _Elem* const _Raw_new = _Unfancy(_New_ptr);

  00169	48 8b 44 24 50	 mov	 rax, QWORD PTR _New_ptr$[rsp]
  0016e	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  00176	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR _Ptr$[rsp]
  0017e	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3043 :         _Elem* const _Raw_new = _Unfancy(_New_ptr);

  00186	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  0018e	48 89 44 24 40	 mov	 QWORD PTR _Raw_new$[rsp], rax

; 3044 :         if (_Old_capacity > _Small_string_capacity) {

  00193	48 83 7c 24 68
	0f		 cmp	 QWORD PTR _Old_capacity$[rsp], 15
  00199	0f 86 d6 00 00
	00		 jbe	 $LN3@Reallocate

; 3045 :             const pointer _Old_ptr = _My_data._Bx._Ptr;

  0019f	48 8b 44 24 30	 mov	 rax, QWORD PTR _My_data$[rsp]
  001a4	48 8b 00	 mov	 rax, QWORD PTR [rax]
  001a7	48 89 44 24 70	 mov	 QWORD PTR _Old_ptr$4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  001ac	48 8b 44 24 70	 mov	 rax, QWORD PTR _Old_ptr$4[rsp]
  001b1	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3046 :             _Fn(_Raw_new, _Unfancy(_Old_ptr), _Old_size, _Args...);

  001b9	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  001c1	4c 8b 44 24 28	 mov	 r8, QWORD PTR _Old_size$[rsp]
  001c6	48 8b d0	 mov	 rdx, rax
  001c9	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Raw_new$[rsp]
  001ce	e8 00 00 00 00	 call	 memcpy
  001d3	90		 npad	 1

; 493  :         return static_cast<_Elem*>(_CSTD memset(_First, _Ch, _Count));

  001d4	0f be 84 24 20
	01 00 00	 movsx	 eax, BYTE PTR <_Args_1>$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1554 :                 _Traits::assign(_New_ptr + _Old_size, _Count, _Ch);

  001dc	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Old_size$[rsp]
  001e1	48 8b 54 24 40	 mov	 rdx, QWORD PTR _Raw_new$[rsp]
  001e6	48 03 d1	 add	 rdx, rcx
  001e9	48 8b ca	 mov	 rcx, rdx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 493  :         return static_cast<_Elem*>(_CSTD memset(_First, _Ch, _Count));

  001ec	4c 8b 84 24 18
	01 00 00	 mov	 r8, QWORD PTR <_Args_0>$[rsp]
  001f4	8b d0		 mov	 edx, eax
  001f6	e8 00 00 00 00	 call	 memset
  001fb	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1555 :                 _Traits::assign(_New_ptr[_Old_size + _Count], _Elem());

  001fc	c6 44 24 20 00	 mov	 BYTE PTR $T1[rsp], 0
  00201	48 8b 84 24 18
	01 00 00	 mov	 rax, QWORD PTR <_Args_0>$[rsp]
  00209	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Old_size$[rsp]
  0020e	48 03 c8	 add	 rcx, rax
  00211	48 8b c1	 mov	 rax, rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 502  :         _Left = _Right;

  00214	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Raw_new$[rsp]
  00219	0f b6 54 24 20	 movzx	 edx, BYTE PTR $T1[rsp]
  0021e	88 14 01	 mov	 BYTE PTR [rcx+rax], dl
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 852  :         _Al.deallocate(_Old_ptr, _Capacity + 1); // +1 for null terminator

  00221	48 8b 44 24 68	 mov	 rax, QWORD PTR _Old_capacity$[rsp]
  00226	48 ff c0	 inc	 rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  00229	48 89 44 24 48	 mov	 QWORD PTR _Bytes$[rsp], rax
  0022e	48 8b 44 24 70	 mov	 rax, QWORD PTR _Old_ptr$4[rsp]
  00233	48 89 44 24 78	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  00238	48 81 7c 24 48
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  00241	72 10		 jb	 SHORT $LN156@Reallocate

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  00243	48 8d 54 24 48	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  00248	48 8d 4c 24 78	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  0024d	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  00252	90		 npad	 1
$LN156@Reallocate:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  00253	48 8b 54 24 48	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  00258	48 8b 4c 24 78	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  0025d	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00262	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3048 :             _My_data._Bx._Ptr = _New_ptr;

  00263	48 8b 44 24 30	 mov	 rax, QWORD PTR _My_data$[rsp]
  00268	48 8b 4c 24 50	 mov	 rcx, QWORD PTR _New_ptr$[rsp]
  0026d	48 89 08	 mov	 QWORD PTR [rax], rcx

; 3049 :         } else {

  00270	e9 b5 00 00 00	 jmp	 $LN4@Reallocate
$LN3@Reallocate:

; 3050 :             _Fn(_Raw_new, _My_data._Bx._Buf, _Old_size, _Args...);

  00275	48 8b 44 24 30	 mov	 rax, QWORD PTR _My_data$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  0027a	4c 8b 44 24 28	 mov	 r8, QWORD PTR _Old_size$[rsp]
  0027f	48 8b d0	 mov	 rdx, rax
  00282	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Raw_new$[rsp]
  00287	e8 00 00 00 00	 call	 memcpy
  0028c	90		 npad	 1

; 493  :         return static_cast<_Elem*>(_CSTD memset(_First, _Ch, _Count));

  0028d	0f be 84 24 20
	01 00 00	 movsx	 eax, BYTE PTR <_Args_1>$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1554 :                 _Traits::assign(_New_ptr + _Old_size, _Count, _Ch);

  00295	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Old_size$[rsp]
  0029a	48 8b 54 24 40	 mov	 rdx, QWORD PTR _Raw_new$[rsp]
  0029f	48 03 d1	 add	 rdx, rcx
  002a2	48 8b ca	 mov	 rcx, rdx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 493  :         return static_cast<_Elem*>(_CSTD memset(_First, _Ch, _Count));

  002a5	4c 8b 84 24 18
	01 00 00	 mov	 r8, QWORD PTR <_Args_0>$[rsp]
  002ad	8b d0		 mov	 edx, eax
  002af	e8 00 00 00 00	 call	 memset
  002b4	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1555 :                 _Traits::assign(_New_ptr[_Old_size + _Count], _Elem());

  002b5	c6 44 24 21 00	 mov	 BYTE PTR $T2[rsp], 0
  002ba	48 8b 84 24 18
	01 00 00	 mov	 rax, QWORD PTR <_Args_0>$[rsp]
  002c2	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Old_size$[rsp]
  002c7	48 03 c8	 add	 rcx, rax
  002ca	48 8b c1	 mov	 rax, rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 502  :         _Left = _Right;

  002cd	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Raw_new$[rsp]
  002d2	0f b6 54 24 21	 movzx	 edx, BYTE PTR $T2[rsp]
  002d7	88 14 01	 mov	 BYTE PTR [rcx+rax], dl
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3051 :             _Construct_in_place(_My_data._Bx._Ptr, _New_ptr);

  002da	48 8b 44 24 30	 mov	 rax, QWORD PTR _My_data$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  002df	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  002e7	48 8b 84 24 c8
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  002ef	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  002f7	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR $T13[rsp]
  002ff	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR $T14[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00307	48 8d 44 24 50	 lea	 rax, QWORD PTR _New_ptr$[rsp]
  0030c	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR $T15[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00314	48 8b 84 24 d8
	00 00 00	 mov	 rax, QWORD PTR $T14[rsp]
  0031c	48 8b 8c 24 e0
	00 00 00	 mov	 rcx, QWORD PTR $T15[rsp]
  00324	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00327	48 89 08	 mov	 QWORD PTR [rax], rcx
$LN4@Reallocate:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3055 :         return *this;

  0032a	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]

; 3056 :     }

  00332	48 81 c4 f8 00
	00 00		 add	 rsp, 248		; 000000f8H
  00339	c3		 ret	 0
$LN202@Reallocate:
??$_Reallocate_grow_by@V<lambda_e1befb086ad3257e3f042a63030725f7>@@_KD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_e1befb086ad3257e3f042a63030725f7>@@_KD@Z ENDP ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_e1befb086ad3257e3f042a63030725f7>,unsigned __int64,char>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
_TEXT	SEGMENT
_Ptr_container$ = 48
_Block_size$ = 56
_Ptr$ = 64
$T1 = 72
_Bytes$ = 96
??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z PROC ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>, COMDAT

; 182  : __declspec(allocator) void* _Allocate_manually_vector_aligned(const size_t _Bytes) {

$LN7:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 183  :     // allocate _Bytes manually aligned to at least _Big_allocation_alignment
; 184  :     const size_t _Block_size = _Non_user_size + _Bytes;

  00009	48 8b 44 24 60	 mov	 rax, QWORD PTR _Bytes$[rsp]
  0000e	48 83 c0 27	 add	 rax, 39			; 00000027H
  00012	48 89 44 24 38	 mov	 QWORD PTR _Block_size$[rsp], rax

; 185  :     if (_Block_size <= _Bytes) {

  00017	48 8b 44 24 60	 mov	 rax, QWORD PTR _Bytes$[rsp]
  0001c	48 39 44 24 38	 cmp	 QWORD PTR _Block_size$[rsp], rax
  00021	77 06		 ja	 SHORT $LN2@Allocate_m

; 186  :         _Throw_bad_array_new_length(); // add overflow

  00023	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00028	90		 npad	 1
$LN2@Allocate_m:

; 136  :         return ::operator new(_Bytes);

  00029	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Block_size$[rsp]
  0002e	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00033	48 89 44 24 48	 mov	 QWORD PTR $T1[rsp], rax

; 187  :     }
; 188  : 
; 189  :     const uintptr_t _Ptr_container = reinterpret_cast<uintptr_t>(_Traits::_Allocate(_Block_size));

  00038	48 8b 44 24 48	 mov	 rax, QWORD PTR $T1[rsp]
  0003d	48 89 44 24 30	 mov	 QWORD PTR _Ptr_container$[rsp], rax

; 190  :     _STL_VERIFY(_Ptr_container != 0, "invalid argument"); // validate even in release since we're doing p[-1]

  00042	48 83 7c 24 30
	00		 cmp	 QWORD PTR _Ptr_container$[rsp], 0
  00048	75 19		 jne	 SHORT $LN3@Allocate_m
  0004a	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR [rsp+32], 0
  00053	45 33 c9	 xor	 r9d, r9d
  00056	45 33 c0	 xor	 r8d, r8d
  00059	33 d2		 xor	 edx, edx
  0005b	33 c9		 xor	 ecx, ecx
  0005d	e8 00 00 00 00	 call	 _invoke_watson
  00062	90		 npad	 1
$LN3@Allocate_m:

; 191  :     void* const _Ptr = reinterpret_cast<void*>((_Ptr_container + _Non_user_size) & ~(_Big_allocation_alignment - 1));

  00063	48 8b 44 24 30	 mov	 rax, QWORD PTR _Ptr_container$[rsp]
  00068	48 83 c0 27	 add	 rax, 39			; 00000027H
  0006c	48 83 e0 e0	 and	 rax, -32		; ffffffffffffffe0H
  00070	48 89 44 24 40	 mov	 QWORD PTR _Ptr$[rsp], rax

; 192  :     static_cast<uintptr_t*>(_Ptr)[-1] = _Ptr_container;

  00075	b8 08 00 00 00	 mov	 eax, 8
  0007a	48 6b c0 ff	 imul	 rax, rax, -1
  0007e	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  00083	48 8b 54 24 30	 mov	 rdx, QWORD PTR _Ptr_container$[rsp]
  00088	48 89 14 01	 mov	 QWORD PTR [rcx+rax], rdx

; 193  : 
; 194  : #ifdef _DEBUG
; 195  :     static_cast<uintptr_t*>(_Ptr)[-2] = _Big_allocation_sentinel;
; 196  : #endif // defined(_DEBUG)
; 197  :     return _Ptr;

  0008c	48 8b 44 24 40	 mov	 rax, QWORD PTR _Ptr$[rsp]
$LN4@Allocate_m:

; 198  : }

  00091	48 83 c4 58	 add	 rsp, 88			; 00000058H
  00095	c3		 ret	 0
??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ENDP ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??$_Reallocate_grow_by@V<lambda_65e615be2a453ca0576c979606f46740>@@PEBD_K@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_65e615be2a453ca0576c979606f46740>@@PEBD_K@Z
_TEXT	SEGMENT
$T1 = 32
$T2 = 33
_Old_size$ = 40
_My_data$ = 48
_New_capacity$ = 56
_Raw_new$ = 64
_Bytes$ = 72
_New_ptr$ = 80
_Fancy_ptr$3 = 88
_New_size$ = 96
_Old_capacity$ = 104
_Old_ptr$4 = 112
_Ptr$ = 120
$T5 = 128
$T6 = 136
$T7 = 144
_Al$ = 152
$T8 = 160
$T9 = 168
_Ptr$ = 176
$T10 = 184
$T11 = 192
_First1$ = 200
_First1$ = 208
$T12 = 216
$T13 = 224
$T14 = 232
$T15 = 240
this$ = 272
_Size_increase$ = 280
_Fn$ = 288
<_Args_0>$ = 296
<_Args_1>$ = 304
??$_Reallocate_grow_by@V<lambda_65e615be2a453ca0576c979606f46740>@@PEBD_K@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_65e615be2a453ca0576c979606f46740>@@PEBD_K@Z PROC ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_65e615be2a453ca0576c979606f46740>,char const *,unsigned __int64>, COMDAT

; 3024 :     _CONSTEXPR20 basic_string& _Reallocate_grow_by(const size_type _Size_increase, _Fty _Fn, _ArgTys... _Args) {

$LN203:
  00000	4c 89 4c 24 20	 mov	 QWORD PTR [rsp+32], r9
  00005	44 88 44 24 18	 mov	 BYTE PTR [rsp+24], r8b
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	48 81 ec 08 01
	00 00		 sub	 rsp, 264		; 00000108H

; 3025 :         // reallocate to increase size by _Size_increase elements, new buffer prepared by
; 3026 :         // _Fn(_New_ptr, _Old_ptr, _Old_size, _Args...)
; 3027 :         auto& _My_data            = _Mypair._Myval2;

  0001b	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 89 44 24 30	 mov	 QWORD PTR _My_data$[rsp], rax

; 3028 :         const size_type _Old_size = _My_data._Mysize;

  00028	48 8b 44 24 30	 mov	 rax, QWORD PTR _My_data$[rsp]
  0002d	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00031	48 89 44 24 28	 mov	 QWORD PTR _Old_size$[rsp], rax

; 3029 :         if (max_size() - _Old_size < _Size_increase) {

  00036	48 8b 8c 24 10
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0003e	e8 00 00 00 00	 call	 ?max_size@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBA_KXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::max_size
  00043	48 2b 44 24 28	 sub	 rax, QWORD PTR _Old_size$[rsp]
  00048	48 3b 84 24 18
	01 00 00	 cmp	 rax, QWORD PTR _Size_increase$[rsp]
  00050	73 06		 jae	 SHORT $LN2@Reallocate

; 3030 :             _Xlen_string(); // result too long

  00052	e8 00 00 00 00	 call	 ?_Xlen_string@std@@YAXXZ ; std::_Xlen_string
  00057	90		 npad	 1
$LN2@Reallocate:

; 3031 :         }
; 3032 : 
; 3033 :         const size_type _New_size     = _Old_size + _Size_increase;

  00058	48 8b 84 24 18
	01 00 00	 mov	 rax, QWORD PTR _Size_increase$[rsp]
  00060	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Old_size$[rsp]
  00065	48 03 c8	 add	 rcx, rax
  00068	48 8b c1	 mov	 rax, rcx
  0006b	48 89 44 24 60	 mov	 QWORD PTR _New_size$[rsp], rax

; 3034 :         const size_type _Old_capacity = _My_data._Myres;

  00070	48 8b 44 24 30	 mov	 rax, QWORD PTR _My_data$[rsp]
  00075	48 8b 40 18	 mov	 rax, QWORD PTR [rax+24]
  00079	48 89 44 24 68	 mov	 QWORD PTR _Old_capacity$[rsp], rax

; 2991 :         return _Calculate_growth(_Requested, _Mypair._Myval2._Myres, max_size());

  0007e	48 8b 8c 24 10
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00086	e8 00 00 00 00	 call	 ?max_size@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBA_KXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::max_size
  0008b	4c 8b c0	 mov	 r8, rax
  0008e	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00096	48 8b 50 18	 mov	 rdx, QWORD PTR [rax+24]
  0009a	48 8b 4c 24 60	 mov	 rcx, QWORD PTR _New_size$[rsp]
  0009f	e8 00 00 00 00	 call	 ?_Calculate_growth@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@CA_K_K00@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Calculate_growth
  000a4	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T5[rsp], rax

; 3035 :         size_type _New_capacity       = _Calculate_growth(_New_size);

  000ac	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR $T5[rsp]
  000b4	48 89 44 24 38	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 3107 :         return _Mypair._Get_first();

  000b9	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  000c1	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3107 :         return _Mypair._Get_first();

  000c9	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T6[rsp]
  000d1	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T7[rsp], rax

; 3036 :         auto& _Al                     = _Getal();

  000d9	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR $T7[rsp]
  000e1	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR _Al$[rsp], rax

; 825  :         ++_Capacity; // Take null terminator into consideration

  000e9	48 8b 44 24 38	 mov	 rax, QWORD PTR _New_capacity$[rsp]
  000ee	48 ff c0	 inc	 rax
  000f1	48 89 44 24 38	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 826  : 
; 827  :         pointer _Fancy_ptr = nullptr;

  000f6	48 c7 44 24 58
	00 00 00 00	 mov	 QWORD PTR _Fancy_ptr$3[rsp], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 2303 :         return _Al.allocate(_Count);

  000ff	48 8b 54 24 38	 mov	 rdx, QWORD PTR _New_capacity$[rsp]
  00104	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR _Al$[rsp]
  0010c	e8 00 00 00 00	 call	 ?allocate@?$allocator@D@std@@QEAAPEAD_K@Z ; std::allocator<char>::allocate
  00111	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 829  :             _Fancy_ptr = _Allocate_at_least_helper(_Al, _Capacity);

  00119	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR $T8[rsp]
  00121	48 89 44 24 58	 mov	 QWORD PTR _Fancy_ptr$3[rsp], rax

; 830  :         } else {
; 831  :             _STL_INTERNAL_STATIC_ASSERT(_Policy == _Allocation_policy::_Exactly);
; 832  :             _Fancy_ptr = _Al.allocate(_Capacity);
; 833  :         }
; 834  : 
; 835  : #if _HAS_CXX20
; 836  :         // Start element lifetimes to avoid UB. This is a more general mechanism than _String_val::_Activate_SSO_buffer,
; 837  :         // but likely more impactful to throughput.
; 838  :         if (_STD is_constant_evaluated()) {
; 839  :             _Elem* const _Ptr = _Unfancy(_Fancy_ptr);
; 840  :             for (size_type _Idx = 0; _Idx < _Capacity; ++_Idx) {
; 841  :                 _STD construct_at(_Ptr + _Idx);
; 842  :             }
; 843  :         }
; 844  : #endif // _HAS_CXX20
; 845  :         --_Capacity;

  00126	48 8b 44 24 38	 mov	 rax, QWORD PTR _New_capacity$[rsp]
  0012b	48 ff c8	 dec	 rax
  0012e	48 89 44 24 38	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 846  :         return _Fancy_ptr;

  00133	48 8b 44 24 58	 mov	 rax, QWORD PTR _Fancy_ptr$3[rsp]
  00138	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax

; 3037 :         const pointer _New_ptr        = _Allocate_for_capacity(_Al, _New_capacity); // throws

  00140	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T9[rsp]
  00148	48 89 44 24 50	 mov	 QWORD PTR _New_ptr$[rsp], rax

; 3038 : 
; 3039 :         _My_data._Orphan_all();
; 3040 :         _ASAN_STRING_REMOVE(*this);
; 3041 :         _My_data._Mysize      = _New_size;

  0014d	48 8b 44 24 30	 mov	 rax, QWORD PTR _My_data$[rsp]
  00152	48 8b 4c 24 60	 mov	 rcx, QWORD PTR _New_size$[rsp]
  00157	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 3042 :         _My_data._Myres       = _New_capacity;

  0015b	48 8b 44 24 30	 mov	 rax, QWORD PTR _My_data$[rsp]
  00160	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _New_capacity$[rsp]
  00165	48 89 48 18	 mov	 QWORD PTR [rax+24], rcx

; 3043 :         _Elem* const _Raw_new = _Unfancy(_New_ptr);

  00169	48 8b 44 24 50	 mov	 rax, QWORD PTR _New_ptr$[rsp]
  0016e	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  00176	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR _Ptr$[rsp]
  0017e	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3043 :         _Elem* const _Raw_new = _Unfancy(_New_ptr);

  00186	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  0018e	48 89 44 24 40	 mov	 QWORD PTR _Raw_new$[rsp], rax

; 3044 :         if (_Old_capacity > _Small_string_capacity) {

  00193	48 83 7c 24 68
	0f		 cmp	 QWORD PTR _Old_capacity$[rsp], 15
  00199	0f 86 e4 00 00
	00		 jbe	 $LN3@Reallocate

; 3045 :             const pointer _Old_ptr = _My_data._Bx._Ptr;

  0019f	48 8b 44 24 30	 mov	 rax, QWORD PTR _My_data$[rsp]
  001a4	48 8b 00	 mov	 rax, QWORD PTR [rax]
  001a7	48 89 44 24 70	 mov	 QWORD PTR _Old_ptr$4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  001ac	48 8b 44 24 70	 mov	 rax, QWORD PTR _Old_ptr$4[rsp]
  001b1	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3046 :             _Fn(_Raw_new, _Unfancy(_Old_ptr), _Old_size, _Args...);

  001b9	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  001c1	4c 8b 44 24 28	 mov	 r8, QWORD PTR _Old_size$[rsp]
  001c6	48 8b d0	 mov	 rdx, rax
  001c9	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Raw_new$[rsp]
  001ce	e8 00 00 00 00	 call	 memcpy
  001d3	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1527 :                 _Traits::copy(_New_ptr + _Old_size, _Ptr, _Count);

  001d4	48 8b 44 24 28	 mov	 rax, QWORD PTR _Old_size$[rsp]
  001d9	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Raw_new$[rsp]
  001de	48 03 c8	 add	 rcx, rax
  001e1	48 8b c1	 mov	 rax, rcx
  001e4	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR _First1$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  001ec	4c 8b 84 24 30
	01 00 00	 mov	 r8, QWORD PTR <_Args_1>$[rsp]
  001f4	48 8b 94 24 28
	01 00 00	 mov	 rdx, QWORD PTR <_Args_0>$[rsp]
  001fc	48 8b 8c 24 c8
	00 00 00	 mov	 rcx, QWORD PTR _First1$[rsp]
  00204	e8 00 00 00 00	 call	 memcpy
  00209	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1528 :                 _Traits::assign(_New_ptr[_Old_size + _Count], _Elem());

  0020a	c6 44 24 20 00	 mov	 BYTE PTR $T1[rsp], 0
  0020f	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR <_Args_1>$[rsp]
  00217	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Old_size$[rsp]
  0021c	48 03 c8	 add	 rcx, rax
  0021f	48 8b c1	 mov	 rax, rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 502  :         _Left = _Right;

  00222	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Raw_new$[rsp]
  00227	0f b6 54 24 20	 movzx	 edx, BYTE PTR $T1[rsp]
  0022c	88 14 01	 mov	 BYTE PTR [rcx+rax], dl
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 852  :         _Al.deallocate(_Old_ptr, _Capacity + 1); // +1 for null terminator

  0022f	48 8b 44 24 68	 mov	 rax, QWORD PTR _Old_capacity$[rsp]
  00234	48 ff c0	 inc	 rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  00237	48 89 44 24 48	 mov	 QWORD PTR _Bytes$[rsp], rax
  0023c	48 8b 44 24 70	 mov	 rax, QWORD PTR _Old_ptr$4[rsp]
  00241	48 89 44 24 78	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  00246	48 81 7c 24 48
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  0024f	72 10		 jb	 SHORT $LN156@Reallocate

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  00251	48 8d 54 24 48	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  00256	48 8d 4c 24 78	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  0025b	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  00260	90		 npad	 1
$LN156@Reallocate:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  00261	48 8b 54 24 48	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  00266	48 8b 4c 24 78	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  0026b	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00270	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3048 :             _My_data._Bx._Ptr = _New_ptr;

  00271	48 8b 44 24 30	 mov	 rax, QWORD PTR _My_data$[rsp]
  00276	48 8b 4c 24 50	 mov	 rcx, QWORD PTR _New_ptr$[rsp]
  0027b	48 89 08	 mov	 QWORD PTR [rax], rcx

; 3049 :         } else {

  0027e	e9 c3 00 00 00	 jmp	 $LN4@Reallocate
$LN3@Reallocate:

; 3050 :             _Fn(_Raw_new, _My_data._Bx._Buf, _Old_size, _Args...);

  00283	48 8b 44 24 30	 mov	 rax, QWORD PTR _My_data$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  00288	4c 8b 44 24 28	 mov	 r8, QWORD PTR _Old_size$[rsp]
  0028d	48 8b d0	 mov	 rdx, rax
  00290	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Raw_new$[rsp]
  00295	e8 00 00 00 00	 call	 memcpy
  0029a	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1527 :                 _Traits::copy(_New_ptr + _Old_size, _Ptr, _Count);

  0029b	48 8b 44 24 28	 mov	 rax, QWORD PTR _Old_size$[rsp]
  002a0	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Raw_new$[rsp]
  002a5	48 03 c8	 add	 rcx, rax
  002a8	48 8b c1	 mov	 rax, rcx
  002ab	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR _First1$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  002b3	4c 8b 84 24 30
	01 00 00	 mov	 r8, QWORD PTR <_Args_1>$[rsp]
  002bb	48 8b 94 24 28
	01 00 00	 mov	 rdx, QWORD PTR <_Args_0>$[rsp]
  002c3	48 8b 8c 24 d0
	00 00 00	 mov	 rcx, QWORD PTR _First1$[rsp]
  002cb	e8 00 00 00 00	 call	 memcpy
  002d0	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1528 :                 _Traits::assign(_New_ptr[_Old_size + _Count], _Elem());

  002d1	c6 44 24 21 00	 mov	 BYTE PTR $T2[rsp], 0
  002d6	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR <_Args_1>$[rsp]
  002de	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Old_size$[rsp]
  002e3	48 03 c8	 add	 rcx, rax
  002e6	48 8b c1	 mov	 rax, rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 502  :         _Left = _Right;

  002e9	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Raw_new$[rsp]
  002ee	0f b6 54 24 21	 movzx	 edx, BYTE PTR $T2[rsp]
  002f3	88 14 01	 mov	 BYTE PTR [rcx+rax], dl
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3051 :             _Construct_in_place(_My_data._Bx._Ptr, _New_ptr);

  002f6	48 8b 44 24 30	 mov	 rax, QWORD PTR _My_data$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  002fb	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00303	48 8b 84 24 d8
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  0030b	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00313	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR $T13[rsp]
  0031b	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR $T14[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00323	48 8d 44 24 50	 lea	 rax, QWORD PTR _New_ptr$[rsp]
  00328	48 89 84 24 f0
	00 00 00	 mov	 QWORD PTR $T15[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00330	48 8b 84 24 e8
	00 00 00	 mov	 rax, QWORD PTR $T14[rsp]
  00338	48 8b 8c 24 f0
	00 00 00	 mov	 rcx, QWORD PTR $T15[rsp]
  00340	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00343	48 89 08	 mov	 QWORD PTR [rax], rcx
$LN4@Reallocate:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3055 :         return *this;

  00346	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]

; 3056 :     }

  0034e	48 81 c4 08 01
	00 00		 add	 rsp, 264		; 00000108H
  00355	c3		 ret	 0
$LN202@Reallocate:
??$_Reallocate_grow_by@V<lambda_65e615be2a453ca0576c979606f46740>@@PEBD_K@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_65e615be2a453ca0576c979606f46740>@@PEBD_K@Z ENDP ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_65e615be2a453ca0576c979606f46740>,char const *,unsigned __int64>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??$_Construct@$01PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z
_TEXT	SEGMENT
$T1 = 32
$S30$ = 33
_My_data$ = 40
_New_capacity$ = 48
_Max$ = 56
_Masked$2 = 64
$T3 = 72
$T4 = 80
tv157 = 88
_Fancy_ptr$5 = 96
_New_ptr$ = 104
$T6 = 112
$T7 = 120
_First1$ = 128
$T8 = 136
$T9 = 144
_Al$ = 152
$T10 = 160
$T11 = 168
$T12 = 176
$T13 = 184
$T14 = 192
$T15 = 200
_Ptr$ = 208
$T16 = 216
_First1$ = 224
_Alproxy$ = 232
this$ = 256
_Arg$ = 264
_Count$ = 272
??$_Construct@$01PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z PROC ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct<2,char const *>, COMDAT

; 871  :     _CONSTEXPR20 void _Construct(const _Char_or_ptr _Arg, _CRT_GUARDOVERFLOW const size_type _Count) {

$LN171:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	57		 push	 rdi
  00010	48 81 ec f0 00
	00 00		 sub	 rsp, 240		; 000000f0H

; 872  :         auto& _My_data = _Mypair._Myval2;

  00017	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0001f	48 89 44 24 28	 mov	 QWORD PTR _My_data$[rsp], rax

; 873  :         _STL_INTERNAL_CHECK(!_My_data._Large_mode_engaged());
; 874  : 
; 875  :         if constexpr (_Strat == _Construct_strategy::_From_char) {
; 876  :             _STL_INTERNAL_STATIC_ASSERT(is_same_v<_Char_or_ptr, _Elem>);
; 877  :         } else {
; 878  :             _STL_INTERNAL_STATIC_ASSERT(_Is_elem_cptr<_Char_or_ptr>::value);
; 879  :         }
; 880  : 
; 881  :         if (_Count > max_size()) {

  00024	48 8b 8c 24 00
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0002c	e8 00 00 00 00	 call	 ?max_size@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBA_KXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::max_size
  00031	48 39 84 24 10
	01 00 00	 cmp	 QWORD PTR _Count$[rsp], rax
  00039	76 06		 jbe	 SHORT $LN2@Construct

; 882  :             _Xlen_string(); // result too long

  0003b	e8 00 00 00 00	 call	 ?_Xlen_string@std@@YAXXZ ; std::_Xlen_string
  00040	90		 npad	 1
$LN2@Construct:

; 3107 :         return _Mypair._Get_first();

  00041	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00049	48 89 44 24 70	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3107 :         return _Mypair._Get_first();

  0004e	48 8b 44 24 70	 mov	 rax, QWORD PTR $T6[rsp]
  00053	48 89 44 24 78	 mov	 QWORD PTR $T7[rsp], rax

; 883  :         }
; 884  : 
; 885  :         auto& _Al       = _Getal();

  00058	48 8b 44 24 78	 mov	 rax, QWORD PTR $T7[rsp]
  0005d	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR _Al$[rsp], rax

; 886  :         auto&& _Alproxy = _GET_PROXY_ALLOCATOR(_Alty, _Al);

  00065	48 8d 44 24 20	 lea	 rax, QWORD PTR $T1[rsp]
  0006a	48 8b f8	 mov	 rdi, rax
  0006d	33 c0		 xor	 eax, eax
  0006f	b9 01 00 00 00	 mov	 ecx, 1
  00074	f3 aa		 rep stosb
  00076	48 8d 44 24 21	 lea	 rax, QWORD PTR $S30$[rsp]
  0007b	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR _Alproxy$[rsp], rax

; 887  :         _Container_proxy_ptr<_Alty> _Proxy(_Alproxy, _My_data);
; 888  : 
; 889  :         if (_Count <= _Small_string_capacity) {

  00083	48 83 bc 24 10
	01 00 00 0f	 cmp	 QWORD PTR _Count$[rsp], 15
  0008c	77 4c		 ja	 SHORT $LN3@Construct

; 890  :             _My_data._Mysize = _Count;

  0008e	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00093	48 8b 8c 24 10
	01 00 00	 mov	 rcx, QWORD PTR _Count$[rsp]
  0009b	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 891  :             _My_data._Myres  = _Small_string_capacity;

  0009f	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000a4	48 c7 40 18 0f
	00 00 00	 mov	 QWORD PTR [rax+24], 15

; 892  : 
; 893  :             if constexpr (_Strat == _Construct_strategy::_From_char) {
; 894  :                 _Traits::assign(_My_data._Bx._Buf, _Count, _Arg);
; 895  :                 _Traits::assign(_My_data._Bx._Buf[_Count], _Elem());
; 896  :             } else if constexpr (_Strat == _Construct_strategy::_From_ptr) {
; 897  :                 _Traits::copy(_My_data._Bx._Buf, _Arg, _Count);
; 898  :                 _Traits::assign(_My_data._Bx._Buf[_Count], _Elem());
; 899  :             } else { // _Strat == _Construct_strategy::_From_string
; 900  : #ifdef _INSERT_STRING_ANNOTATION
; 901  :                 _Traits::copy(_My_data._Bx._Buf, _Arg, _Count + 1);
; 902  : #else // ^^^ _INSERT_STRING_ANNOTATION / !_INSERT_STRING_ANNOTATION vvv
; 903  :                 _Traits::copy(_My_data._Bx._Buf, _Arg, _BUF_SIZE);

  000ac	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000b1	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _First1$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  000b9	41 b8 10 00 00
	00		 mov	 r8d, 16
  000bf	48 8b 94 24 08
	01 00 00	 mov	 rdx, QWORD PTR _Arg$[rsp]
  000c7	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR _First1$[rsp]
  000cf	e8 00 00 00 00	 call	 memcpy
  000d4	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 908  :             return;

  000d5	e9 ff 01 00 00	 jmp	 $LN4@Construct
$LN3@Construct:

; 909  :         }
; 910  : 
; 911  :         size_type _New_capacity = _Calculate_growth(_Count, _Small_string_capacity, max_size());

  000da	48 8b 8c 24 00
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  000e2	e8 00 00 00 00	 call	 ?max_size@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBA_KXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::max_size
  000e7	48 89 44 24 38	 mov	 QWORD PTR _Max$[rsp], rax

; 2978 :         const size_type _Masked = _Requested | _Alloc_mask;

  000ec	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  000f4	48 83 c8 0f	 or	 rax, 15
  000f8	48 89 44 24 40	 mov	 QWORD PTR _Masked$2[rsp], rax

; 2979 :         if (_Masked > _Max) { // the mask overflows, settle for max_size()

  000fd	48 8b 44 24 38	 mov	 rax, QWORD PTR _Max$[rsp]
  00102	48 39 44 24 40	 cmp	 QWORD PTR _Masked$2[rsp], rax
  00107	76 0f		 jbe	 SHORT $LN109@Construct

; 2980 :             return _Max;

  00109	48 8b 44 24 38	 mov	 rax, QWORD PTR _Max$[rsp]
  0010e	48 89 44 24 48	 mov	 QWORD PTR $T3[rsp], rax
  00113	e9 93 00 00 00	 jmp	 $LN108@Construct
$LN109@Construct:

; 2981 :         }
; 2982 : 
; 2983 :         if (_Old > _Max - _Old / 2) { // similarly, geometric overflows

  00118	33 d2		 xor	 edx, edx
  0011a	b8 0f 00 00 00	 mov	 eax, 15
  0011f	b9 02 00 00 00	 mov	 ecx, 2
  00124	48 f7 f1	 div	 rcx
  00127	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Max$[rsp]
  0012c	48 2b c8	 sub	 rcx, rax
  0012f	48 8b c1	 mov	 rax, rcx
  00132	48 83 f8 0f	 cmp	 rax, 15
  00136	73 0c		 jae	 SHORT $LN110@Construct

; 2984 :             return _Max;

  00138	48 8b 44 24 38	 mov	 rax, QWORD PTR _Max$[rsp]
  0013d	48 89 44 24 48	 mov	 QWORD PTR $T3[rsp], rax
  00142	eb 67		 jmp	 SHORT $LN108@Construct
$LN110@Construct:

; 2987 :         return (_STD max)(_Masked, _Old + _Old / 2);

  00144	33 d2		 xor	 edx, edx
  00146	b8 0f 00 00 00	 mov	 eax, 15
  0014b	b9 02 00 00 00	 mov	 ecx, 2
  00150	48 f7 f1	 div	 rcx
  00153	48 83 c0 0f	 add	 rax, 15
  00157	48 89 44 24 50	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 77   :     return _Left < _Right ? _Right : _Left;

  0015c	48 8b 44 24 50	 mov	 rax, QWORD PTR $T4[rsp]
  00161	48 39 44 24 40	 cmp	 QWORD PTR _Masked$2[rsp], rax
  00166	73 0c		 jae	 SHORT $LN117@Construct
  00168	48 8d 44 24 50	 lea	 rax, QWORD PTR $T4[rsp]
  0016d	48 89 44 24 58	 mov	 QWORD PTR tv157[rsp], rax
  00172	eb 0a		 jmp	 SHORT $LN118@Construct
$LN117@Construct:
  00174	48 8d 44 24 40	 lea	 rax, QWORD PTR _Masked$2[rsp]
  00179	48 89 44 24 58	 mov	 QWORD PTR tv157[rsp], rax
$LN118@Construct:
  0017e	48 8b 44 24 58	 mov	 rax, QWORD PTR tv157[rsp]
  00183	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T8[rsp], rax
  0018b	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T8[rsp]
  00193	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2987 :         return (_STD max)(_Masked, _Old + _Old / 2);

  0019b	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR $T9[rsp]
  001a3	48 8b 00	 mov	 rax, QWORD PTR [rax]
  001a6	48 89 44 24 48	 mov	 QWORD PTR $T3[rsp], rax
$LN108@Construct:

; 909  :         }
; 910  : 
; 911  :         size_type _New_capacity = _Calculate_growth(_Count, _Small_string_capacity, max_size());

  001ab	48 8b 44 24 48	 mov	 rax, QWORD PTR $T3[rsp]
  001b0	48 89 44 24 30	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 825  :         ++_Capacity; // Take null terminator into consideration

  001b5	48 8b 44 24 30	 mov	 rax, QWORD PTR _New_capacity$[rsp]
  001ba	48 ff c0	 inc	 rax
  001bd	48 89 44 24 30	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 826  : 
; 827  :         pointer _Fancy_ptr = nullptr;

  001c2	48 c7 44 24 60
	00 00 00 00	 mov	 QWORD PTR _Fancy_ptr$5[rsp], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 2303 :         return _Al.allocate(_Count);

  001cb	48 8b 54 24 30	 mov	 rdx, QWORD PTR _New_capacity$[rsp]
  001d0	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR _Al$[rsp]
  001d8	e8 00 00 00 00	 call	 ?allocate@?$allocator@D@std@@QEAAPEAD_K@Z ; std::allocator<char>::allocate
  001dd	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 829  :             _Fancy_ptr = _Allocate_at_least_helper(_Al, _Capacity);

  001e5	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  001ed	48 89 44 24 60	 mov	 QWORD PTR _Fancy_ptr$5[rsp], rax

; 830  :         } else {
; 831  :             _STL_INTERNAL_STATIC_ASSERT(_Policy == _Allocation_policy::_Exactly);
; 832  :             _Fancy_ptr = _Al.allocate(_Capacity);
; 833  :         }
; 834  : 
; 835  : #if _HAS_CXX20
; 836  :         // Start element lifetimes to avoid UB. This is a more general mechanism than _String_val::_Activate_SSO_buffer,
; 837  :         // but likely more impactful to throughput.
; 838  :         if (_STD is_constant_evaluated()) {
; 839  :             _Elem* const _Ptr = _Unfancy(_Fancy_ptr);
; 840  :             for (size_type _Idx = 0; _Idx < _Capacity; ++_Idx) {
; 841  :                 _STD construct_at(_Ptr + _Idx);
; 842  :             }
; 843  :         }
; 844  : #endif // _HAS_CXX20
; 845  :         --_Capacity;

  001f2	48 8b 44 24 30	 mov	 rax, QWORD PTR _New_capacity$[rsp]
  001f7	48 ff c8	 dec	 rax
  001fa	48 89 44 24 30	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 846  :         return _Fancy_ptr;

  001ff	48 8b 44 24 60	 mov	 rax, QWORD PTR _Fancy_ptr$5[rsp]
  00204	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax

; 912  :         const pointer _New_ptr  = _Allocate_for_capacity(_Al, _New_capacity); // throws

  0020c	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  00214	48 89 44 24 68	 mov	 QWORD PTR _New_ptr$[rsp], rax

; 913  :         _Construct_in_place(_My_data._Bx._Ptr, _New_ptr);

  00219	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0021e	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00226	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  0022e	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00236	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T13[rsp]
  0023e	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T14[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00246	48 8d 44 24 68	 lea	 rax, QWORD PTR _New_ptr$[rsp]
  0024b	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T15[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00253	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR $T14[rsp]
  0025b	48 8b 8c 24 c8
	00 00 00	 mov	 rcx, QWORD PTR $T15[rsp]
  00263	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00266	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 915  :         _My_data._Mysize = _Count;

  00269	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  0026e	48 8b 8c 24 10
	01 00 00	 mov	 rcx, QWORD PTR _Count$[rsp]
  00276	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 916  :         _My_data._Myres  = _New_capacity;

  0027a	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  0027f	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _New_capacity$[rsp]
  00284	48 89 48 18	 mov	 QWORD PTR [rax+24], rcx

; 924  :             _Traits::copy(_Unfancy(_New_ptr), _Arg, _Count + 1);

  00288	48 8b 44 24 68	 mov	 rax, QWORD PTR _New_ptr$[rsp]
  0028d	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  00295	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR _Ptr$[rsp]
  0029d	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR $T16[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 924  :             _Traits::copy(_Unfancy(_New_ptr), _Arg, _Count + 1);

  002a5	48 8b 84 24 d8
	00 00 00	 mov	 rax, QWORD PTR $T16[rsp]
  002ad	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR _First1$[rsp], rax
  002b5	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  002bd	48 ff c0	 inc	 rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  002c0	4c 8b c0	 mov	 r8, rax
  002c3	48 8b 94 24 08
	01 00 00	 mov	 rdx, QWORD PTR _Arg$[rsp]
  002cb	48 8b 8c 24 e0
	00 00 00	 mov	 rcx, QWORD PTR _First1$[rsp]
  002d3	e8 00 00 00 00	 call	 memcpy
  002d8	90		 npad	 1
$LN4@Construct:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 929  :     }

  002d9	48 81 c4 f0 00
	00 00		 add	 rsp, 240		; 000000f0H
  002e0	5f		 pop	 rdi
  002e1	c3		 ret	 0
??$_Construct@$01PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z ENDP ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct<2,char const *>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z
_TEXT	SEGMENT
$T1 = 32
$T2 = 33
$T3 = 34
$S29$ = 35
_My_data$ = 40
_New_capacity$ = 48
_Max$ = 56
_Masked$4 = 64
$T5 = 72
_New_ptr$ = 80
$T6 = 88
tv156 = 96
_Fancy_ptr$7 = 104
$T8 = 112
$T9 = 120
_First1$ = 128
$T10 = 136
$T11 = 144
_Al$ = 152
$T12 = 160
$T13 = 168
$T14 = 176
$T15 = 184
$T16 = 192
$T17 = 200
_Ptr$ = 208
$T18 = 216
_First1$ = 224
_Ptr$ = 232
$T19 = 240
_Alproxy$ = 248
this$ = 272
_Arg$ = 280
_Count$ = 288
??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z PROC ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct<1,char const *>, COMDAT

; 871  :     _CONSTEXPR20 void _Construct(const _Char_or_ptr _Arg, _CRT_GUARDOVERFLOW const size_type _Count) {

$LN186:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	57		 push	 rdi
  00010	48 81 ec 00 01
	00 00		 sub	 rsp, 256		; 00000100H

; 872  :         auto& _My_data = _Mypair._Myval2;

  00017	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0001f	48 89 44 24 28	 mov	 QWORD PTR _My_data$[rsp], rax

; 873  :         _STL_INTERNAL_CHECK(!_My_data._Large_mode_engaged());
; 874  : 
; 875  :         if constexpr (_Strat == _Construct_strategy::_From_char) {
; 876  :             _STL_INTERNAL_STATIC_ASSERT(is_same_v<_Char_or_ptr, _Elem>);
; 877  :         } else {
; 878  :             _STL_INTERNAL_STATIC_ASSERT(_Is_elem_cptr<_Char_or_ptr>::value);
; 879  :         }
; 880  : 
; 881  :         if (_Count > max_size()) {

  00024	48 8b 8c 24 10
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0002c	e8 00 00 00 00	 call	 ?max_size@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBA_KXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::max_size
  00031	48 39 84 24 20
	01 00 00	 cmp	 QWORD PTR _Count$[rsp], rax
  00039	76 06		 jbe	 SHORT $LN2@Construct

; 882  :             _Xlen_string(); // result too long

  0003b	e8 00 00 00 00	 call	 ?_Xlen_string@std@@YAXXZ ; std::_Xlen_string
  00040	90		 npad	 1
$LN2@Construct:

; 3107 :         return _Mypair._Get_first();

  00041	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00049	48 89 44 24 70	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3107 :         return _Mypair._Get_first();

  0004e	48 8b 44 24 70	 mov	 rax, QWORD PTR $T8[rsp]
  00053	48 89 44 24 78	 mov	 QWORD PTR $T9[rsp], rax

; 883  :         }
; 884  : 
; 885  :         auto& _Al       = _Getal();

  00058	48 8b 44 24 78	 mov	 rax, QWORD PTR $T9[rsp]
  0005d	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR _Al$[rsp], rax

; 886  :         auto&& _Alproxy = _GET_PROXY_ALLOCATOR(_Alty, _Al);

  00065	48 8d 44 24 22	 lea	 rax, QWORD PTR $T3[rsp]
  0006a	48 8b f8	 mov	 rdi, rax
  0006d	33 c0		 xor	 eax, eax
  0006f	b9 01 00 00 00	 mov	 ecx, 1
  00074	f3 aa		 rep stosb
  00076	48 8d 44 24 23	 lea	 rax, QWORD PTR $S29$[rsp]
  0007b	48 89 84 24 f8
	00 00 00	 mov	 QWORD PTR _Alproxy$[rsp], rax

; 887  :         _Container_proxy_ptr<_Alty> _Proxy(_Alproxy, _My_data);
; 888  : 
; 889  :         if (_Count <= _Small_string_capacity) {

  00083	48 83 bc 24 20
	01 00 00 0f	 cmp	 QWORD PTR _Count$[rsp], 15
  0008c	77 6d		 ja	 SHORT $LN3@Construct

; 890  :             _My_data._Mysize = _Count;

  0008e	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00093	48 8b 8c 24 20
	01 00 00	 mov	 rcx, QWORD PTR _Count$[rsp]
  0009b	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 891  :             _My_data._Myres  = _Small_string_capacity;

  0009f	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000a4	48 c7 40 18 0f
	00 00 00	 mov	 QWORD PTR [rax+24], 15

; 892  : 
; 893  :             if constexpr (_Strat == _Construct_strategy::_From_char) {
; 894  :                 _Traits::assign(_My_data._Bx._Buf, _Count, _Arg);
; 895  :                 _Traits::assign(_My_data._Bx._Buf[_Count], _Elem());
; 896  :             } else if constexpr (_Strat == _Construct_strategy::_From_ptr) {
; 897  :                 _Traits::copy(_My_data._Bx._Buf, _Arg, _Count);

  000ac	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000b1	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _First1$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  000b9	4c 8b 84 24 20
	01 00 00	 mov	 r8, QWORD PTR _Count$[rsp]
  000c1	48 8b 94 24 18
	01 00 00	 mov	 rdx, QWORD PTR _Arg$[rsp]
  000c9	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR _First1$[rsp]
  000d1	e8 00 00 00 00	 call	 memcpy
  000d6	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 898  :                 _Traits::assign(_My_data._Bx._Buf[_Count], _Elem());

  000d7	c6 44 24 20 00	 mov	 BYTE PTR $T1[rsp], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 502  :         _Left = _Right;

  000dc	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  000e4	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _My_data$[rsp]
  000e9	48 03 c8	 add	 rcx, rax
  000ec	48 8b c1	 mov	 rax, rcx
  000ef	0f b6 4c 24 20	 movzx	 ecx, BYTE PTR $T1[rsp]
  000f4	88 08		 mov	 BYTE PTR [rax], cl
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 908  :             return;

  000f6	e9 38 02 00 00	 jmp	 $LN4@Construct
$LN3@Construct:

; 909  :         }
; 910  : 
; 911  :         size_type _New_capacity = _Calculate_growth(_Count, _Small_string_capacity, max_size());

  000fb	48 8b 8c 24 10
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00103	e8 00 00 00 00	 call	 ?max_size@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBA_KXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::max_size
  00108	48 89 44 24 38	 mov	 QWORD PTR _Max$[rsp], rax

; 2978 :         const size_type _Masked = _Requested | _Alloc_mask;

  0010d	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  00115	48 83 c8 0f	 or	 rax, 15
  00119	48 89 44 24 40	 mov	 QWORD PTR _Masked$4[rsp], rax

; 2979 :         if (_Masked > _Max) { // the mask overflows, settle for max_size()

  0011e	48 8b 44 24 38	 mov	 rax, QWORD PTR _Max$[rsp]
  00123	48 39 44 24 40	 cmp	 QWORD PTR _Masked$4[rsp], rax
  00128	76 0f		 jbe	 SHORT $LN114@Construct

; 2980 :             return _Max;

  0012a	48 8b 44 24 38	 mov	 rax, QWORD PTR _Max$[rsp]
  0012f	48 89 44 24 48	 mov	 QWORD PTR $T5[rsp], rax
  00134	e9 93 00 00 00	 jmp	 $LN113@Construct
$LN114@Construct:

; 2981 :         }
; 2982 : 
; 2983 :         if (_Old > _Max - _Old / 2) { // similarly, geometric overflows

  00139	33 d2		 xor	 edx, edx
  0013b	b8 0f 00 00 00	 mov	 eax, 15
  00140	b9 02 00 00 00	 mov	 ecx, 2
  00145	48 f7 f1	 div	 rcx
  00148	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Max$[rsp]
  0014d	48 2b c8	 sub	 rcx, rax
  00150	48 8b c1	 mov	 rax, rcx
  00153	48 83 f8 0f	 cmp	 rax, 15
  00157	73 0c		 jae	 SHORT $LN115@Construct

; 2984 :             return _Max;

  00159	48 8b 44 24 38	 mov	 rax, QWORD PTR _Max$[rsp]
  0015e	48 89 44 24 48	 mov	 QWORD PTR $T5[rsp], rax
  00163	eb 67		 jmp	 SHORT $LN113@Construct
$LN115@Construct:

; 2987 :         return (_STD max)(_Masked, _Old + _Old / 2);

  00165	33 d2		 xor	 edx, edx
  00167	b8 0f 00 00 00	 mov	 eax, 15
  0016c	b9 02 00 00 00	 mov	 ecx, 2
  00171	48 f7 f1	 div	 rcx
  00174	48 83 c0 0f	 add	 rax, 15
  00178	48 89 44 24 58	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 77   :     return _Left < _Right ? _Right : _Left;

  0017d	48 8b 44 24 58	 mov	 rax, QWORD PTR $T6[rsp]
  00182	48 39 44 24 40	 cmp	 QWORD PTR _Masked$4[rsp], rax
  00187	73 0c		 jae	 SHORT $LN122@Construct
  00189	48 8d 44 24 58	 lea	 rax, QWORD PTR $T6[rsp]
  0018e	48 89 44 24 60	 mov	 QWORD PTR tv156[rsp], rax
  00193	eb 0a		 jmp	 SHORT $LN123@Construct
$LN122@Construct:
  00195	48 8d 44 24 40	 lea	 rax, QWORD PTR _Masked$4[rsp]
  0019a	48 89 44 24 60	 mov	 QWORD PTR tv156[rsp], rax
$LN123@Construct:
  0019f	48 8b 44 24 60	 mov	 rax, QWORD PTR tv156[rsp]
  001a4	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
  001ac	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  001b4	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2987 :         return (_STD max)(_Masked, _Old + _Old / 2);

  001bc	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  001c4	48 8b 00	 mov	 rax, QWORD PTR [rax]
  001c7	48 89 44 24 48	 mov	 QWORD PTR $T5[rsp], rax
$LN113@Construct:

; 909  :         }
; 910  : 
; 911  :         size_type _New_capacity = _Calculate_growth(_Count, _Small_string_capacity, max_size());

  001cc	48 8b 44 24 48	 mov	 rax, QWORD PTR $T5[rsp]
  001d1	48 89 44 24 30	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 825  :         ++_Capacity; // Take null terminator into consideration

  001d6	48 8b 44 24 30	 mov	 rax, QWORD PTR _New_capacity$[rsp]
  001db	48 ff c0	 inc	 rax
  001de	48 89 44 24 30	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 826  : 
; 827  :         pointer _Fancy_ptr = nullptr;

  001e3	48 c7 44 24 68
	00 00 00 00	 mov	 QWORD PTR _Fancy_ptr$7[rsp], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 2303 :         return _Al.allocate(_Count);

  001ec	48 8b 54 24 30	 mov	 rdx, QWORD PTR _New_capacity$[rsp]
  001f1	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR _Al$[rsp]
  001f9	e8 00 00 00 00	 call	 ?allocate@?$allocator@D@std@@QEAAPEAD_K@Z ; std::allocator<char>::allocate
  001fe	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 829  :             _Fancy_ptr = _Allocate_at_least_helper(_Al, _Capacity);

  00206	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
  0020e	48 89 44 24 68	 mov	 QWORD PTR _Fancy_ptr$7[rsp], rax

; 830  :         } else {
; 831  :             _STL_INTERNAL_STATIC_ASSERT(_Policy == _Allocation_policy::_Exactly);
; 832  :             _Fancy_ptr = _Al.allocate(_Capacity);
; 833  :         }
; 834  : 
; 835  : #if _HAS_CXX20
; 836  :         // Start element lifetimes to avoid UB. This is a more general mechanism than _String_val::_Activate_SSO_buffer,
; 837  :         // but likely more impactful to throughput.
; 838  :         if (_STD is_constant_evaluated()) {
; 839  :             _Elem* const _Ptr = _Unfancy(_Fancy_ptr);
; 840  :             for (size_type _Idx = 0; _Idx < _Capacity; ++_Idx) {
; 841  :                 _STD construct_at(_Ptr + _Idx);
; 842  :             }
; 843  :         }
; 844  : #endif // _HAS_CXX20
; 845  :         --_Capacity;

  00213	48 8b 44 24 30	 mov	 rax, QWORD PTR _New_capacity$[rsp]
  00218	48 ff c8	 dec	 rax
  0021b	48 89 44 24 30	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 846  :         return _Fancy_ptr;

  00220	48 8b 44 24 68	 mov	 rax, QWORD PTR _Fancy_ptr$7[rsp]
  00225	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax

; 912  :         const pointer _New_ptr  = _Allocate_for_capacity(_Al, _New_capacity); // throws

  0022d	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T13[rsp]
  00235	48 89 44 24 50	 mov	 QWORD PTR _New_ptr$[rsp], rax

; 913  :         _Construct_in_place(_My_data._Bx._Ptr, _New_ptr);

  0023a	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0023f	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T14[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00247	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR $T14[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  0024f	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T15[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00257	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T15[rsp]
  0025f	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T16[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00267	48 8d 44 24 50	 lea	 rax, QWORD PTR _New_ptr$[rsp]
  0026c	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T17[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00274	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR $T16[rsp]
  0027c	48 8b 8c 24 c8
	00 00 00	 mov	 rcx, QWORD PTR $T17[rsp]
  00284	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00287	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 915  :         _My_data._Mysize = _Count;

  0028a	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  0028f	48 8b 8c 24 20
	01 00 00	 mov	 rcx, QWORD PTR _Count$[rsp]
  00297	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 916  :         _My_data._Myres  = _New_capacity;

  0029b	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  002a0	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _New_capacity$[rsp]
  002a5	48 89 48 18	 mov	 QWORD PTR [rax+24], rcx

; 921  :             _Traits::copy(_Unfancy(_New_ptr), _Arg, _Count);

  002a9	48 8b 44 24 50	 mov	 rax, QWORD PTR _New_ptr$[rsp]
  002ae	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  002b6	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR _Ptr$[rsp]
  002be	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR $T18[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 921  :             _Traits::copy(_Unfancy(_New_ptr), _Arg, _Count);

  002c6	48 8b 84 24 d8
	00 00 00	 mov	 rax, QWORD PTR $T18[rsp]
  002ce	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR _First1$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  002d6	4c 8b 84 24 20
	01 00 00	 mov	 r8, QWORD PTR _Count$[rsp]
  002de	48 8b 94 24 18
	01 00 00	 mov	 rdx, QWORD PTR _Arg$[rsp]
  002e6	48 8b 8c 24 e0
	00 00 00	 mov	 rcx, QWORD PTR _First1$[rsp]
  002ee	e8 00 00 00 00	 call	 memcpy
  002f3	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 922  :             _Traits::assign(_Unfancy(_New_ptr)[_Count], _Elem());

  002f4	c6 44 24 21 00	 mov	 BYTE PTR $T2[rsp], 0
  002f9	48 8b 44 24 50	 mov	 rax, QWORD PTR _New_ptr$[rsp]
  002fe	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  00306	48 8b 84 24 e8
	00 00 00	 mov	 rax, QWORD PTR _Ptr$[rsp]
  0030e	48 89 84 24 f0
	00 00 00	 mov	 QWORD PTR $T19[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 502  :         _Left = _Right;

  00316	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  0031e	48 8b 8c 24 f0
	00 00 00	 mov	 rcx, QWORD PTR $T19[rsp]
  00326	48 03 c8	 add	 rcx, rax
  00329	48 8b c1	 mov	 rax, rcx
  0032c	0f b6 4c 24 21	 movzx	 ecx, BYTE PTR $T2[rsp]
  00331	88 08		 mov	 BYTE PTR [rax], cl
$LN4@Construct:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 929  :     }

  00333	48 81 c4 00 01
	00 00		 add	 rsp, 256		; 00000100H
  0033a	5f		 pop	 rdi
  0033b	c3		 ret	 0
??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z ENDP ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct<1,char const *>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??$_Construct@$01PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z
_TEXT	SEGMENT
$T1 = 32
$S27$ = 33
_My_data$ = 40
_New_capacity$ = 48
_Max$ = 56
_Masked$2 = 64
$T3 = 72
$T4 = 80
tv154 = 88
_Fancy_ptr$5 = 96
_New_ptr$ = 104
$T6 = 112
$T7 = 120
_First1$ = 128
$T8 = 136
$T9 = 144
_Al$ = 152
$T10 = 160
$T11 = 168
$T12 = 176
$T13 = 184
$T14 = 192
$T15 = 200
_Ptr$ = 208
$T16 = 216
_First1$ = 224
_Alproxy$ = 232
this$ = 256
_Arg$ = 264
_Count$ = 272
??$_Construct@$01PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<2,wchar_t const *>, COMDAT

; 871  :     _CONSTEXPR20 void _Construct(const _Char_or_ptr _Arg, _CRT_GUARDOVERFLOW const size_type _Count) {

$LN173:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	57		 push	 rdi
  00010	48 81 ec f0 00
	00 00		 sub	 rsp, 240		; 000000f0H

; 872  :         auto& _My_data = _Mypair._Myval2;

  00017	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0001f	48 89 44 24 28	 mov	 QWORD PTR _My_data$[rsp], rax

; 873  :         _STL_INTERNAL_CHECK(!_My_data._Large_mode_engaged());
; 874  : 
; 875  :         if constexpr (_Strat == _Construct_strategy::_From_char) {
; 876  :             _STL_INTERNAL_STATIC_ASSERT(is_same_v<_Char_or_ptr, _Elem>);
; 877  :         } else {
; 878  :             _STL_INTERNAL_STATIC_ASSERT(_Is_elem_cptr<_Char_or_ptr>::value);
; 879  :         }
; 880  : 
; 881  :         if (_Count > max_size()) {

  00024	48 8b 8c 24 00
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0002c	e8 00 00 00 00	 call	 ?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
  00031	48 39 84 24 10
	01 00 00	 cmp	 QWORD PTR _Count$[rsp], rax
  00039	76 06		 jbe	 SHORT $LN2@Construct

; 882  :             _Xlen_string(); // result too long

  0003b	e8 00 00 00 00	 call	 ?_Xlen_string@std@@YAXXZ ; std::_Xlen_string
  00040	90		 npad	 1
$LN2@Construct:

; 3107 :         return _Mypair._Get_first();

  00041	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00049	48 89 44 24 70	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3107 :         return _Mypair._Get_first();

  0004e	48 8b 44 24 70	 mov	 rax, QWORD PTR $T6[rsp]
  00053	48 89 44 24 78	 mov	 QWORD PTR $T7[rsp], rax

; 883  :         }
; 884  : 
; 885  :         auto& _Al       = _Getal();

  00058	48 8b 44 24 78	 mov	 rax, QWORD PTR $T7[rsp]
  0005d	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR _Al$[rsp], rax

; 886  :         auto&& _Alproxy = _GET_PROXY_ALLOCATOR(_Alty, _Al);

  00065	48 8d 44 24 20	 lea	 rax, QWORD PTR $T1[rsp]
  0006a	48 8b f8	 mov	 rdi, rax
  0006d	33 c0		 xor	 eax, eax
  0006f	b9 01 00 00 00	 mov	 ecx, 1
  00074	f3 aa		 rep stosb
  00076	48 8d 44 24 21	 lea	 rax, QWORD PTR $S27$[rsp]
  0007b	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR _Alproxy$[rsp], rax

; 887  :         _Container_proxy_ptr<_Alty> _Proxy(_Alproxy, _My_data);
; 888  : 
; 889  :         if (_Count <= _Small_string_capacity) {

  00083	48 83 bc 24 10
	01 00 00 07	 cmp	 QWORD PTR _Count$[rsp], 7
  0008c	77 52		 ja	 SHORT $LN3@Construct

; 890  :             _My_data._Mysize = _Count;

  0008e	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00093	48 8b 8c 24 10
	01 00 00	 mov	 rcx, QWORD PTR _Count$[rsp]
  0009b	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 891  :             _My_data._Myres  = _Small_string_capacity;

  0009f	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000a4	48 c7 40 18 07
	00 00 00	 mov	 QWORD PTR [rax+24], 7

; 892  : 
; 893  :             if constexpr (_Strat == _Construct_strategy::_From_char) {
; 894  :                 _Traits::assign(_My_data._Bx._Buf, _Count, _Arg);
; 895  :                 _Traits::assign(_My_data._Bx._Buf[_Count], _Elem());
; 896  :             } else if constexpr (_Strat == _Construct_strategy::_From_ptr) {
; 897  :                 _Traits::copy(_My_data._Bx._Buf, _Arg, _Count);
; 898  :                 _Traits::assign(_My_data._Bx._Buf[_Count], _Elem());
; 899  :             } else { // _Strat == _Construct_strategy::_From_string
; 900  : #ifdef _INSERT_STRING_ANNOTATION
; 901  :                 _Traits::copy(_My_data._Bx._Buf, _Arg, _Count + 1);
; 902  : #else // ^^^ _INSERT_STRING_ANNOTATION / !_INSERT_STRING_ANNOTATION vvv
; 903  :                 _Traits::copy(_My_data._Bx._Buf, _Arg, _BUF_SIZE);

  000ac	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000b1	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _First1$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  000b9	b8 08 00 00 00	 mov	 eax, 8
  000be	48 6b c0 02	 imul	 rax, rax, 2
  000c2	4c 8b c0	 mov	 r8, rax
  000c5	48 8b 94 24 08
	01 00 00	 mov	 rdx, QWORD PTR _Arg$[rsp]
  000cd	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR _First1$[rsp]
  000d5	e8 00 00 00 00	 call	 memcpy
  000da	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 908  :             return;

  000db	e9 01 02 00 00	 jmp	 $LN4@Construct
$LN3@Construct:

; 909  :         }
; 910  : 
; 911  :         size_type _New_capacity = _Calculate_growth(_Count, _Small_string_capacity, max_size());

  000e0	48 8b 8c 24 00
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  000e8	e8 00 00 00 00	 call	 ?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
  000ed	48 89 44 24 38	 mov	 QWORD PTR _Max$[rsp], rax

; 2978 :         const size_type _Masked = _Requested | _Alloc_mask;

  000f2	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  000fa	48 83 c8 07	 or	 rax, 7
  000fe	48 89 44 24 40	 mov	 QWORD PTR _Masked$2[rsp], rax

; 2979 :         if (_Masked > _Max) { // the mask overflows, settle for max_size()

  00103	48 8b 44 24 38	 mov	 rax, QWORD PTR _Max$[rsp]
  00108	48 39 44 24 40	 cmp	 QWORD PTR _Masked$2[rsp], rax
  0010d	76 0f		 jbe	 SHORT $LN109@Construct

; 2980 :             return _Max;

  0010f	48 8b 44 24 38	 mov	 rax, QWORD PTR _Max$[rsp]
  00114	48 89 44 24 48	 mov	 QWORD PTR $T3[rsp], rax
  00119	e9 93 00 00 00	 jmp	 $LN108@Construct
$LN109@Construct:

; 2981 :         }
; 2982 : 
; 2983 :         if (_Old > _Max - _Old / 2) { // similarly, geometric overflows

  0011e	33 d2		 xor	 edx, edx
  00120	b8 07 00 00 00	 mov	 eax, 7
  00125	b9 02 00 00 00	 mov	 ecx, 2
  0012a	48 f7 f1	 div	 rcx
  0012d	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Max$[rsp]
  00132	48 2b c8	 sub	 rcx, rax
  00135	48 8b c1	 mov	 rax, rcx
  00138	48 83 f8 07	 cmp	 rax, 7
  0013c	73 0c		 jae	 SHORT $LN110@Construct

; 2984 :             return _Max;

  0013e	48 8b 44 24 38	 mov	 rax, QWORD PTR _Max$[rsp]
  00143	48 89 44 24 48	 mov	 QWORD PTR $T3[rsp], rax
  00148	eb 67		 jmp	 SHORT $LN108@Construct
$LN110@Construct:

; 2987 :         return (_STD max)(_Masked, _Old + _Old / 2);

  0014a	33 d2		 xor	 edx, edx
  0014c	b8 07 00 00 00	 mov	 eax, 7
  00151	b9 02 00 00 00	 mov	 ecx, 2
  00156	48 f7 f1	 div	 rcx
  00159	48 83 c0 07	 add	 rax, 7
  0015d	48 89 44 24 50	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 77   :     return _Left < _Right ? _Right : _Left;

  00162	48 8b 44 24 50	 mov	 rax, QWORD PTR $T4[rsp]
  00167	48 39 44 24 40	 cmp	 QWORD PTR _Masked$2[rsp], rax
  0016c	73 0c		 jae	 SHORT $LN117@Construct
  0016e	48 8d 44 24 50	 lea	 rax, QWORD PTR $T4[rsp]
  00173	48 89 44 24 58	 mov	 QWORD PTR tv154[rsp], rax
  00178	eb 0a		 jmp	 SHORT $LN118@Construct
$LN117@Construct:
  0017a	48 8d 44 24 40	 lea	 rax, QWORD PTR _Masked$2[rsp]
  0017f	48 89 44 24 58	 mov	 QWORD PTR tv154[rsp], rax
$LN118@Construct:
  00184	48 8b 44 24 58	 mov	 rax, QWORD PTR tv154[rsp]
  00189	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T8[rsp], rax
  00191	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T8[rsp]
  00199	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2987 :         return (_STD max)(_Masked, _Old + _Old / 2);

  001a1	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR $T9[rsp]
  001a9	48 8b 00	 mov	 rax, QWORD PTR [rax]
  001ac	48 89 44 24 48	 mov	 QWORD PTR $T3[rsp], rax
$LN108@Construct:

; 909  :         }
; 910  : 
; 911  :         size_type _New_capacity = _Calculate_growth(_Count, _Small_string_capacity, max_size());

  001b1	48 8b 44 24 48	 mov	 rax, QWORD PTR $T3[rsp]
  001b6	48 89 44 24 30	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 825  :         ++_Capacity; // Take null terminator into consideration

  001bb	48 8b 44 24 30	 mov	 rax, QWORD PTR _New_capacity$[rsp]
  001c0	48 ff c0	 inc	 rax
  001c3	48 89 44 24 30	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 826  : 
; 827  :         pointer _Fancy_ptr = nullptr;

  001c8	48 c7 44 24 60
	00 00 00 00	 mov	 QWORD PTR _Fancy_ptr$5[rsp], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 2303 :         return _Al.allocate(_Count);

  001d1	48 8b 54 24 30	 mov	 rdx, QWORD PTR _New_capacity$[rsp]
  001d6	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR _Al$[rsp]
  001de	e8 00 00 00 00	 call	 ?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z ; std::allocator<wchar_t>::allocate
  001e3	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 829  :             _Fancy_ptr = _Allocate_at_least_helper(_Al, _Capacity);

  001eb	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  001f3	48 89 44 24 60	 mov	 QWORD PTR _Fancy_ptr$5[rsp], rax

; 830  :         } else {
; 831  :             _STL_INTERNAL_STATIC_ASSERT(_Policy == _Allocation_policy::_Exactly);
; 832  :             _Fancy_ptr = _Al.allocate(_Capacity);
; 833  :         }
; 834  : 
; 835  : #if _HAS_CXX20
; 836  :         // Start element lifetimes to avoid UB. This is a more general mechanism than _String_val::_Activate_SSO_buffer,
; 837  :         // but likely more impactful to throughput.
; 838  :         if (_STD is_constant_evaluated()) {
; 839  :             _Elem* const _Ptr = _Unfancy(_Fancy_ptr);
; 840  :             for (size_type _Idx = 0; _Idx < _Capacity; ++_Idx) {
; 841  :                 _STD construct_at(_Ptr + _Idx);
; 842  :             }
; 843  :         }
; 844  : #endif // _HAS_CXX20
; 845  :         --_Capacity;

  001f8	48 8b 44 24 30	 mov	 rax, QWORD PTR _New_capacity$[rsp]
  001fd	48 ff c8	 dec	 rax
  00200	48 89 44 24 30	 mov	 QWORD PTR _New_capacity$[rsp], rax

; 846  :         return _Fancy_ptr;

  00205	48 8b 44 24 60	 mov	 rax, QWORD PTR _Fancy_ptr$5[rsp]
  0020a	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax

; 912  :         const pointer _New_ptr  = _Allocate_for_capacity(_Al, _New_capacity); // throws

  00212	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  0021a	48 89 44 24 68	 mov	 QWORD PTR _New_ptr$[rsp], rax

; 913  :         _Construct_in_place(_My_data._Bx._Ptr, _New_ptr);

  0021f	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00224	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0022c	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  00234	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0023c	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T13[rsp]
  00244	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T14[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  0024c	48 8d 44 24 68	 lea	 rax, QWORD PTR _New_ptr$[rsp]
  00251	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T15[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00259	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR $T14[rsp]
  00261	48 8b 8c 24 c8
	00 00 00	 mov	 rcx, QWORD PTR $T15[rsp]
  00269	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0026c	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 915  :         _My_data._Mysize = _Count;

  0026f	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00274	48 8b 8c 24 10
	01 00 00	 mov	 rcx, QWORD PTR _Count$[rsp]
  0027c	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 916  :         _My_data._Myres  = _New_capacity;

  00280	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00285	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _New_capacity$[rsp]
  0028a	48 89 48 18	 mov	 QWORD PTR [rax+24], rcx

; 924  :             _Traits::copy(_Unfancy(_New_ptr), _Arg, _Count + 1);

  0028e	48 8b 44 24 68	 mov	 rax, QWORD PTR _New_ptr$[rsp]
  00293	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  0029b	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR _Ptr$[rsp]
  002a3	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR $T16[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 924  :             _Traits::copy(_Unfancy(_New_ptr), _Arg, _Count + 1);

  002ab	48 8b 84 24 d8
	00 00 00	 mov	 rax, QWORD PTR $T16[rsp]
  002b3	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR _First1$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 121  :         _CSTD memcpy(_First1, _First2, _Count * sizeof(_Elem));

  002bb	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  002c3	48 8d 44 00 02	 lea	 rax, QWORD PTR [rax+rax+2]
  002c8	4c 8b c0	 mov	 r8, rax
  002cb	48 8b 94 24 08
	01 00 00	 mov	 rdx, QWORD PTR _Arg$[rsp]
  002d3	48 8b 8c 24 e0
	00 00 00	 mov	 rcx, QWORD PTR _First1$[rsp]
  002db	e8 00 00 00 00	 call	 memcpy
  002e0	90		 npad	 1
$LN4@Construct:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 929  :     }

  002e1	48 81 c4 f0 00
	00 00		 add	 rsp, 240		; 000000f0H
  002e8	5f		 pop	 rdi
  002e9	c3		 ret	 0
??$_Construct@$01PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<2,wchar_t const *>
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp
;	COMDAT ?GetKnightageId@RedisPacket@mu2@@YAIAEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
_TEXT	SEGMENT
$T1 = 48
$T2 = 52
id$ = 56
off$ = 64
$T3 = 72
tv81 = 80
$T4 = 88
__$ArrayPad$ = 120
kstr$ = 144
?GetKnightageId@RedisPacket@mu2@@YAIAEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z PROC ; mu2::RedisPacket::GetKnightageId, COMDAT

; 111  : 		{

$LN210:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H
  0000c	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR __security_cookie
  00013	48 33 c4	 xor	 rax, rsp
  00016	48 89 44 24 78	 mov	 QWORD PTR __$ArrayPad$[rsp], rax
  0001b	c7 44 24 34 00
	00 00 00	 mov	 DWORD PTR $T2[rsp], 0

; 112  : 			KnightageId id = 0;

  00023	c7 44 24 38 00
	00 00 00	 mov	 DWORD PTR id$[rsp], 0

; 113  : 			std::size_t off = kstr.find('.');

  0002b	45 33 c0	 xor	 r8d, r8d
  0002e	b2 2e		 mov	 dl, 46			; 0000002eH
  00030	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR kstr$[rsp]
  00038	e8 00 00 00 00	 call	 ?find@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBA_KD_K@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::find
  0003d	48 89 44 24 40	 mov	 QWORD PTR off$[rsp], rax

; 114  : 			if (std::string::npos != off)

  00042	48 83 7c 24 40
	ff		 cmp	 QWORD PTR off$[rsp], -1
  00048	74 68		 je	 SHORT $LN2@GetKnighta
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 974  :     constexpr allocator() noexcept {}

  0004a	48 8d 44 24 30	 lea	 rax, QWORD PTR $T1[rsp]
  0004f	48 89 44 24 48	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2836 :         return basic_string{*this, _Off, _Count};

  00054	48 8b 44 24 48	 mov	 rax, QWORD PTR $T3[rsp]
  00059	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  0005e	49 c7 c1 ff ff
	ff ff		 mov	 r9, -1
  00065	4c 8b 44 24 40	 mov	 r8, QWORD PTR off$[rsp]
  0006a	48 8b 94 24 90
	00 00 00	 mov	 rdx, QWORD PTR kstr$[rsp]
  00072	48 8d 4c 24 58	 lea	 rcx, QWORD PTR $T4[rsp]
  00077	e8 00 00 00 00	 call	 ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@_K1AEBV?$allocator@D@1@@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
  0007c	8b 44 24 34	 mov	 eax, DWORD PTR $T2[rsp]
  00080	83 c8 01	 or	 eax, 1
  00083	89 44 24 34	 mov	 DWORD PTR $T2[rsp], eax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp

; 115  : 				id = atoi(kstr.substr(off, std::string::npos).c_str());

  00087	48 8d 44 24 58	 lea	 rax, QWORD PTR $T4[rsp]
  0008c	48 89 44 24 50	 mov	 QWORD PTR tv81[rsp], rax
  00091	48 8b 4c 24 50	 mov	 rcx, QWORD PTR tv81[rsp]
  00096	e8 00 00 00 00	 call	 ?c_str@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBAPEBDXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::c_str
  0009b	48 8b c8	 mov	 rcx, rax
  0009e	e8 00 00 00 00	 call	 atoi
  000a3	89 44 24 38	 mov	 DWORD PTR id$[rsp], eax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1383 :         _Tidy_deallocate();

  000a7	48 8d 4c 24 58	 lea	 rcx, QWORD PTR $T4[rsp]
  000ac	e8 00 00 00 00	 call	 ?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Tidy_deallocate
  000b1	90		 npad	 1
$LN2@GetKnighta:
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp

; 116  : 			return id;

  000b2	8b 44 24 38	 mov	 eax, DWORD PTR id$[rsp]

; 117  : 
; 118  : 		}

  000b6	48 8b 4c 24 78	 mov	 rcx, QWORD PTR __$ArrayPad$[rsp]
  000bb	48 33 cc	 xor	 rcx, rsp
  000be	e8 00 00 00 00	 call	 __security_check_cookie
  000c3	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  000ca	c3		 ret	 0
?GetKnightageId@RedisPacket@mu2@@YAIAEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ENDP ; mu2::RedisPacket::GetKnightageId
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp
;	COMDAT ?GetRedisEventCode@RedisPacket@mu2@@YAGAEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
_TEXT	SEGMENT
ret$ = 32
cmd$ = 64
?GetRedisEventCode@RedisPacket@mu2@@YAGAEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z PROC ; mu2::RedisPacket::GetRedisEventCode, COMDAT

; 101  : 		{

$LN102:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 102  : 			UShort ret = SKIP;

  00009	b8 03 00 00 00	 mov	 eax, 3
  0000e	66 89 44 24 20	 mov	 WORD PTR ret$[rsp], ax

; 103  : 			if (0 == cmd.compare("subscribe"))

  00013	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:??_C@_09OMPELEIG@subscribe@
  0001a	48 8b 4c 24 40	 mov	 rcx, QWORD PTR cmd$[rsp]
  0001f	e8 00 00 00 00	 call	 ?compare@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBAHQEBD@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::compare
  00024	85 c0		 test	 eax, eax
  00026	75 0c		 jne	 SHORT $LN2@GetRedisEv

; 104  : 				ret = SUBSCRIBE;

  00028	b8 02 00 00 00	 mov	 eax, 2
  0002d	66 89 44 24 20	 mov	 WORD PTR ret$[rsp], ax
  00032	eb 1c		 jmp	 SHORT $LN3@GetRedisEv
$LN2@GetRedisEv:

; 105  : 			else if (0 == cmd.compare("message"))

  00034	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:??_C@_07ONPBMBOP@message@
  0003b	48 8b 4c 24 40	 mov	 rcx, QWORD PTR cmd$[rsp]
  00040	e8 00 00 00 00	 call	 ?compare@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBAHQEBD@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::compare
  00045	85 c0		 test	 eax, eax
  00047	75 07		 jne	 SHORT $LN4@GetRedisEv

; 106  : 				ret = MESSAGE;

  00049	33 c0		 xor	 eax, eax
  0004b	66 89 44 24 20	 mov	 WORD PTR ret$[rsp], ax
$LN4@GetRedisEv:
$LN3@GetRedisEv:

; 107  : 			return ret;

  00050	0f b7 44 24 20	 movzx	 eax, WORD PTR ret$[rsp]

; 108  : 		}

  00055	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00059	c3		 ret	 0
?GetRedisEventCode@RedisPacket@mu2@@YAGAEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ENDP ; mu2::RedisPacket::GetRedisEventCode
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldServer.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp
;	COMDAT ?ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ
_TEXT	SEGMENT
$T1 = 64
this$ = 72
_Old_size$2 = 80
_Old_size$3 = 88
this$ = 96
this$ = 104
$T4 = 112
$T5 = 120
this$ = 128
$T6 = 136
$T7 = 144
$T8 = 152
$T9 = 160
$T10 = 168
$T11 = 176
$T12 = 184
$T13 = 192
$T14 = 200
wslen$ = 208
tv188 = 216
msg$ = 224
chat$ = 256
cname$ = 288
kidbuf$ = 320
cidbuf$ = 336
wsname$ = 352
__$ArrayPad$ = 368
this$ = 400
?ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ PROC ; mu2::RedisPacket::AppChatParser::ConvertToAppChat, COMDAT

; 59   : 		{

$LN480:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi
  00006	48 81 ec 80 01
	00 00		 sub	 rsp, 384		; 00000180H
  0000d	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR __security_cookie
  00014	48 33 c4	 xor	 rax, rsp
  00017	48 89 84 24 70
	01 00 00	 mov	 QWORD PTR __$ArrayPad$[rsp], rax

; 60   : 			// 결과 m_convertResult 에 쓸것
; 61   : 			memset(m_buf, 0, sizeof(m_buf));

  0001f	48 8b 84 24 90
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00027	48 83 c0 48	 add	 rax, 72			; 00000048H
  0002b	41 b8 50 08 00
	00		 mov	 r8d, 2128		; 00000850H
  00031	33 d2		 xor	 edx, edx
  00033	48 8b c8	 mov	 rcx, rax
  00036	e8 00 00 00 00	 call	 memset

; 62   : 
; 63   : 			char kidbuf[16] = { 0, };

  0003b	48 8d 84 24 40
	01 00 00	 lea	 rax, QWORD PTR kidbuf$[rsp]
  00043	48 8b f8	 mov	 rdi, rax
  00046	33 c0		 xor	 eax, eax
  00048	b9 10 00 00 00	 mov	 ecx, 16
  0004d	f3 aa		 rep stosb

; 64   : 			sprintf_s(kidbuf, sizeof(kidbuf), "%d\t", m_knightageId);

  0004f	48 8b 84 24 90
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00057	44 8b 08	 mov	 r9d, DWORD PTR [rax]
  0005a	4c 8d 05 00 00
	00 00		 lea	 r8, OFFSET FLAT:??_C@_03NHELKHFK@?$CFd?7@
  00061	ba 10 00 00 00	 mov	 edx, 16
  00066	48 8d 8c 24 40
	01 00 00	 lea	 rcx, QWORD PTR kidbuf$[rsp]
  0006e	e8 00 00 00 00	 call	 sprintf_s

; 65   : 
; 66   : 			char cidbuf[16] = { 0, };

  00073	48 8d 84 24 50
	01 00 00	 lea	 rax, QWORD PTR cidbuf$[rsp]
  0007b	48 8b f8	 mov	 rdi, rax
  0007e	33 c0		 xor	 eax, eax
  00080	b9 10 00 00 00	 mov	 ecx, 16
  00085	f3 aa		 rep stosb

; 67   : 			sprintf_s(cidbuf, sizeof(cidbuf), "%d\t", m_charId);

  00087	48 8b 84 24 90
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0008f	44 8b 48 04	 mov	 r9d, DWORD PTR [rax+4]
  00093	4c 8d 05 00 00
	00 00		 lea	 r8, OFFSET FLAT:??_C@_03NHELKHFK@?$CFd?7@
  0009a	ba 10 00 00 00	 mov	 edx, 16
  0009f	48 8d 8c 24 50
	01 00 00	 lea	 rcx, QWORD PTR cidbuf$[rsp]
  000a7	e8 00 00 00 00	 call	 sprintf_s
  000ac	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 708  :     basic_string() noexcept(is_nothrow_default_constructible_v<_Alty>) : _Mypair(_Zero_then_variadic_args_t{}) {

  000ad	48 8d 84 24 20
	01 00 00	 lea	 rax, QWORD PTR cname$[rsp]
  000b5	48 89 44 24 60	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1531 :         : _Ty1(), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  000ba	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  000bf	48 8b c8	 mov	 rcx, rax
  000c2	e8 00 00 00 00	 call	 ??0?$_String_val@U?$_Simple_types@D@std@@@std@@QEAA@XZ ; std::_String_val<std::_Simple_types<char> >::_String_val<std::_Simple_types<char> >
  000c7	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 709  :         _Construct_empty();

  000c8	48 8d 8c 24 20
	01 00 00	 lea	 rcx, QWORD PTR cname$[rsp]
  000d0	e8 00 00 00 00	 call	 ?_Construct_empty@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_empty
  000d5	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp

; 70   : 			StringUtil::Convert(m_charName, cname);

  000d6	48 8b 84 24 90
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000de	48 83 c0 08	 add	 rax, 8
  000e2	45 33 c0	 xor	 r8d, r8d
  000e5	48 8d 94 24 20
	01 00 00	 lea	 rdx, QWORD PTR cname$[rsp]
  000ed	48 8b c8	 mov	 rcx, rax
  000f0	e8 00 00 00 00	 call	 ?Convert@StringUtil@mu2@@SA_NAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@4@H@Z ; mu2::StringUtil::Convert
  000f5	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 708  :     basic_string() noexcept(is_nothrow_default_constructible_v<_Alty>) : _Mypair(_Zero_then_variadic_args_t{}) {

  000f6	48 8d 84 24 00
	01 00 00	 lea	 rax, QWORD PTR chat$[rsp]
  000fe	48 89 44 24 68	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1531 :         : _Ty1(), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  00103	48 8b 44 24 68	 mov	 rax, QWORD PTR this$[rsp]
  00108	48 8b c8	 mov	 rcx, rax
  0010b	e8 00 00 00 00	 call	 ??0?$_String_val@U?$_Simple_types@D@std@@@std@@QEAA@XZ ; std::_String_val<std::_Simple_types<char> >::_String_val<std::_Simple_types<char> >
  00110	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 709  :         _Construct_empty();

  00111	48 8d 8c 24 00
	01 00 00	 lea	 rcx, QWORD PTR chat$[rsp]
  00119	e8 00 00 00 00	 call	 ?_Construct_empty@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_empty
  0011e	90		 npad	 1

; 2370 :         return _Mypair._Myval2._Mysize;

  0011f	48 8b 84 24 90
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00127	48 8b 40 38	 mov	 rax, QWORD PTR [rax+56]
  0012b	48 89 44 24 70	 mov	 QWORD PTR $T4[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp

; 74   : 			if (m_chat.length() > Limits::MAX_CHAT_LENGTH)

  00130	48 8b 44 24 70	 mov	 rax, QWORD PTR $T4[rsp]
  00135	48 3d e8 03 00
	00		 cmp	 rax, 1000		; 000003e8H
  0013b	76 61		 jbe	 SHORT $LN2@ConvertToA

; 75   : 			{
; 76   : 				m_chat.resize(Limits::MAX_CHAT_LENGTH);

  0013d	48 8b 84 24 90
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00145	48 83 c0 28	 add	 rax, 40			; 00000028H
  00149	48 89 44 24 48	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2374 :         return _Mypair._Myval2._Mysize;

  0014e	48 8b 44 24 48	 mov	 rax, QWORD PTR this$[rsp]
  00153	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00157	48 89 44 24 78	 mov	 QWORD PTR $T5[rsp], rax

; 2388 :         const size_type _Old_size = size();

  0015c	48 8b 44 24 78	 mov	 rax, QWORD PTR $T5[rsp]
  00161	48 89 44 24 50	 mov	 QWORD PTR _Old_size$2[rsp], rax

; 2389 :         if (_New_size <= _Old_size) {

  00166	48 81 7c 24 50
	e8 03 00 00	 cmp	 QWORD PTR _Old_size$2[rsp], 1000 ; 000003e8H
  0016f	72 12		 jb	 SHORT $LN44@ConvertToA

; 2390 :             _Eos(_New_size);

  00171	ba e8 03 00 00	 mov	 edx, 1000		; 000003e8H
  00176	48 8b 4c 24 48	 mov	 rcx, QWORD PTR this$[rsp]
  0017b	e8 00 00 00 00	 call	 ?_Eos@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAX_K@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Eos
  00180	90		 npad	 1

; 2391 :         } else {

  00181	eb 1b		 jmp	 SHORT $LN2@ConvertToA
$LN44@ConvertToA:

; 2392 :             append(_New_size - _Old_size, _Ch);

  00183	b8 e8 03 00 00	 mov	 eax, 1000		; 000003e8H
  00188	48 2b 44 24 50	 sub	 rax, QWORD PTR _Old_size$2[rsp]
  0018d	45 33 c0	 xor	 r8d, r8d
  00190	48 8b d0	 mov	 rdx, rax
  00193	48 8b 4c 24 48	 mov	 rcx, QWORD PTR this$[rsp]
  00198	e8 00 00 00 00	 call	 ?append@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV12@_K_W@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::append
  0019d	90		 npad	 1
$LN2@ConvertToA:
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp

; 79   : 			StringUtil::Convert(m_chat, chat);

  0019e	48 8b 84 24 90
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  001a6	48 83 c0 28	 add	 rax, 40			; 00000028H
  001aa	45 33 c0	 xor	 r8d, r8d
  001ad	48 8d 94 24 00
	01 00 00	 lea	 rdx, QWORD PTR chat$[rsp]
  001b5	48 8b c8	 mov	 rcx, rax
  001b8	e8 00 00 00 00	 call	 ?Convert@StringUtil@mu2@@SA_NAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@4@H@Z ; mu2::StringUtil::Convert
  001bd	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 708  :     basic_string() noexcept(is_nothrow_default_constructible_v<_Alty>) : _Mypair(_Zero_then_variadic_args_t{}) {

  001be	48 8d 84 24 e0
	00 00 00	 lea	 rax, QWORD PTR msg$[rsp]
  001c6	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1531 :         : _Ty1(), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  001ce	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  001d6	48 8b c8	 mov	 rcx, rax
  001d9	e8 00 00 00 00	 call	 ??0?$_String_val@U?$_Simple_types@D@std@@@std@@QEAA@XZ ; std::_String_val<std::_Simple_types<char> >::_String_val<std::_Simple_types<char> >
  001de	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 709  :         _Construct_empty();

  001df	48 8d 8c 24 e0
	00 00 00	 lea	 rcx, QWORD PTR msg$[rsp]
  001e7	e8 00 00 00 00	 call	 ?_Construct_empty@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_empty
  001ec	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 459  :         return _CSTD strlen(reinterpret_cast<const char*>(_First));

  001ed	48 8d 8c 24 40
	01 00 00	 lea	 rcx, QWORD PTR kidbuf$[rsp]
  001f5	e8 00 00 00 00	 call	 strlen
  001fa	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1534 :         return append(_Ptr, _Convert_size<size_type>(_Traits::length(_Ptr)));

  00202	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T6[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1131 :     return static_cast<_Size_type>(_Len);

  0020a	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1534 :         return append(_Ptr, _Convert_size<size_type>(_Traits::length(_Ptr)));

  00212	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR $T7[rsp]
  0021a	4c 8b c0	 mov	 r8, rax
  0021d	48 8d 94 24 40
	01 00 00	 lea	 rdx, QWORD PTR kidbuf$[rsp]
  00225	48 8d 8c 24 e0
	00 00 00	 lea	 rcx, QWORD PTR msg$[rsp]
  0022d	e8 00 00 00 00	 call	 ?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::append
  00232	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 459  :         return _CSTD strlen(reinterpret_cast<const char*>(_First));

  00233	48 8d 8c 24 50
	01 00 00	 lea	 rcx, QWORD PTR cidbuf$[rsp]
  0023b	e8 00 00 00 00	 call	 strlen
  00240	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1534 :         return append(_Ptr, _Convert_size<size_type>(_Traits::length(_Ptr)));

  00248	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR $T8[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1131 :     return static_cast<_Size_type>(_Len);

  00250	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1534 :         return append(_Ptr, _Convert_size<size_type>(_Traits::length(_Ptr)));

  00258	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR $T9[rsp]
  00260	4c 8b c0	 mov	 r8, rax
  00263	48 8d 94 24 50
	01 00 00	 lea	 rdx, QWORD PTR cidbuf$[rsp]
  0026b	48 8d 8c 24 e0
	00 00 00	 lea	 rcx, QWORD PTR msg$[rsp]
  00273	e8 00 00 00 00	 call	 ?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::append
  00278	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp

; 84   : 			msg.append(cname);

  00279	48 8d 94 24 20
	01 00 00	 lea	 rdx, QWORD PTR cname$[rsp]
  00281	48 8d 8c 24 e0
	00 00 00	 lea	 rcx, QWORD PTR msg$[rsp]
  00289	e8 00 00 00 00	 call	 ?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@AEBV12@@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::append
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 459  :         return _CSTD strlen(reinterpret_cast<const char*>(_First));

  0028e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_01GPOEFGEJ@?7@
  00295	e8 00 00 00 00	 call	 strlen
  0029a	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1534 :         return append(_Ptr, _Convert_size<size_type>(_Traits::length(_Ptr)));

  002a2	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1131 :     return static_cast<_Size_type>(_Len);

  002aa	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1534 :         return append(_Ptr, _Convert_size<size_type>(_Traits::length(_Ptr)));

  002b2	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  002ba	4c 8b c0	 mov	 r8, rax
  002bd	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:??_C@_01GPOEFGEJ@?7@
  002c4	48 8d 8c 24 e0
	00 00 00	 lea	 rcx, QWORD PTR msg$[rsp]
  002cc	e8 00 00 00 00	 call	 ?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::append
  002d1	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp

; 86   : 			msg.append(chat);

  002d2	48 8d 94 24 00
	01 00 00	 lea	 rdx, QWORD PTR chat$[rsp]
  002da	48 8d 8c 24 e0
	00 00 00	 lea	 rcx, QWORD PTR msg$[rsp]
  002e2	e8 00 00 00 00	 call	 ?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@AEBV12@@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::append
  002e7	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2370 :         return _Mypair._Myval2._Mysize;

  002e8	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR chat$[rsp+16]
  002f0	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp

; 88   : 			if (chat.length() > Limits::MAX_CHAT_LENGTH * 2)

  002f8	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
  00300	48 3d d0 07 00
	00		 cmp	 rax, 2000		; 000007d0H
  00306	76 5b		 jbe	 SHORT $LN3@ConvertToA
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2374 :         return _Mypair._Myval2._Mysize;

  00308	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR chat$[rsp+16]
  00310	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax

; 2388 :         const size_type _Old_size = size();

  00318	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR $T13[rsp]
  00320	48 89 44 24 58	 mov	 QWORD PTR _Old_size$3[rsp], rax

; 2389 :         if (_New_size <= _Old_size) {

  00325	48 81 7c 24 58
	d0 07 00 00	 cmp	 QWORD PTR _Old_size$3[rsp], 2000 ; 000007d0H
  0032e	72 15		 jb	 SHORT $LN177@ConvertToA

; 2390 :             _Eos(_New_size);

  00330	ba d0 07 00 00	 mov	 edx, 2000		; 000007d0H
  00335	48 8d 8c 24 00
	01 00 00	 lea	 rcx, QWORD PTR chat$[rsp]
  0033d	e8 00 00 00 00	 call	 ?_Eos@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAX_K@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Eos
  00342	90		 npad	 1

; 2391 :         } else {

  00343	eb 1e		 jmp	 SHORT $LN3@ConvertToA
$LN177@ConvertToA:

; 2392 :             append(_New_size - _Old_size, _Ch);

  00345	b8 d0 07 00 00	 mov	 eax, 2000		; 000007d0H
  0034a	48 2b 44 24 58	 sub	 rax, QWORD PTR _Old_size$3[rsp]
  0034f	45 33 c0	 xor	 r8d, r8d
  00352	48 8b d0	 mov	 rdx, rax
  00355	48 8d 8c 24 00
	01 00 00	 lea	 rcx, QWORD PTR chat$[rsp]
  0035d	e8 00 00 00 00	 call	 ?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@_KD@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::append
  00362	90		 npad	 1
$LN3@ConvertToA:
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp

; 92   : 			char wsname[16] = { 0, };

  00363	48 8d 84 24 60
	01 00 00	 lea	 rax, QWORD PTR wsname$[rsp]
  0036b	48 8b f8	 mov	 rdi, rax
  0036e	33 c0		 xor	 eax, eax
  00370	b9 10 00 00 00	 mov	 ecx, 16
  00375	f3 aa		 rep stosb
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldServer.h

; 90   : 	WorldId	GetWorldId() { return m_worldInfo.worldId; }	

  00377	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR ?Instance@Server@mu2@@2PEAV12@EA ; mu2::Server::Instance
  0037e	0f b6 80 08 04
	00 00		 movzx	 eax, BYTE PTR [rax+1032]
  00385	88 44 24 40	 mov	 BYTE PTR $T1[rsp], al
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp

; 93   : 			sprintf_s(wsname, sizeof(wsname), "frompc%d", SERVER.GetWorldId());

  00389	0f b6 44 24 40	 movzx	 eax, BYTE PTR $T1[rsp]
  0038e	0f b6 c0	 movzx	 eax, al
  00391	44 8b c8	 mov	 r9d, eax
  00394	4c 8d 05 00 00
	00 00		 lea	 r8, OFFSET FLAT:??_C@_08PODIDHJA@frompc?$CFd@
  0039b	ba 10 00 00 00	 mov	 edx, 16
  003a0	48 8d 8c 24 60
	01 00 00	 lea	 rcx, QWORD PTR wsname$[rsp]
  003a8	e8 00 00 00 00	 call	 sprintf_s

; 94   : 			size_t wslen = strlen(wsname);

  003ad	48 8d 8c 24 60
	01 00 00	 lea	 rcx, QWORD PTR wsname$[rsp]
  003b5	e8 00 00 00 00	 call	 strlen
  003ba	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR wslen$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2370 :         return _Mypair._Myval2._Mysize;

  003c2	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR msg$[rsp+16]
  003ca	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T14[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp

; 96   : 			sprintf_s(m_buf, sizeof(m_buf), "*3\r\n$7\r\nPUBLISH\r\n$%d\r\n%s\r\n$%d\r\n%s\r\n", static_cast<int>(wslen), wsname, static_cast<Int32>(msg.length()), msg.c_str());

  003d2	48 8d 8c 24 e0
	00 00 00	 lea	 rcx, QWORD PTR msg$[rsp]
  003da	e8 00 00 00 00	 call	 ?c_str@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBAPEBDXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::c_str
  003df	48 8b 8c 24 c8
	00 00 00	 mov	 rcx, QWORD PTR $T14[rsp]
  003e7	48 8b 94 24 90
	01 00 00	 mov	 rdx, QWORD PTR this$[rsp]
  003ef	48 83 c2 48	 add	 rdx, 72			; 00000048H
  003f3	48 89 94 24 d8
	00 00 00	 mov	 QWORD PTR tv188[rsp], rdx
  003fb	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  00400	89 4c 24 28	 mov	 DWORD PTR [rsp+40], ecx
  00404	48 8d 84 24 60
	01 00 00	 lea	 rax, QWORD PTR wsname$[rsp]
  0040c	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  00411	44 8b 8c 24 d0
	00 00 00	 mov	 r9d, DWORD PTR wslen$[rsp]
  00419	4c 8d 05 00 00
	00 00		 lea	 r8, OFFSET FLAT:??_C@_0CE@IHFNLKB@?$CK3?$AN?6$7?$AN?6PUBLISH?$AN?6$?$CFd?$AN?6?$CFs?$AN?6$?$CFd?$AN?6@
  00420	ba 50 08 00 00	 mov	 edx, 2128		; 00000850H
  00425	48 8b 84 24 d8
	00 00 00	 mov	 rax, QWORD PTR tv188[rsp]
  0042d	48 8b c8	 mov	 rcx, rax
  00430	e8 00 00 00 00	 call	 sprintf_s
  00435	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1383 :         _Tidy_deallocate();

  00436	48 8d 8c 24 e0
	00 00 00	 lea	 rcx, QWORD PTR msg$[rsp]
  0043e	e8 00 00 00 00	 call	 ?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Tidy_deallocate
  00443	90		 npad	 1
  00444	48 8d 8c 24 00
	01 00 00	 lea	 rcx, QWORD PTR chat$[rsp]
  0044c	e8 00 00 00 00	 call	 ?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Tidy_deallocate
  00451	90		 npad	 1
  00452	48 8d 8c 24 20
	01 00 00	 lea	 rcx, QWORD PTR cname$[rsp]
  0045a	e8 00 00 00 00	 call	 ?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Tidy_deallocate
  0045f	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp

; 98   : 		}

  00460	48 8b 8c 24 70
	01 00 00	 mov	 rcx, QWORD PTR __$ArrayPad$[rsp]
  00468	48 33 cc	 xor	 rcx, rsp
  0046b	e8 00 00 00 00	 call	 __security_check_cookie
  00470	48 81 c4 80 01
	00 00		 add	 rsp, 384		; 00000180H
  00477	5f		 pop	 rdi
  00478	c3		 ret	 0
?ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ ENDP ; mu2::RedisPacket::AppChatParser::ConvertToAppChat
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 64
this$ = 72
_Old_size$2 = 80
_Old_size$3 = 88
this$ = 96
this$ = 104
$T4 = 112
$T5 = 120
this$ = 128
$T6 = 136
$T7 = 144
$T8 = 152
$T9 = 160
$T10 = 168
$T11 = 176
$T12 = 184
$T13 = 192
$T14 = 200
wslen$ = 208
tv188 = 216
msg$ = 224
chat$ = 256
cname$ = 288
kidbuf$ = 320
cidbuf$ = 336
wsname$ = 352
__$ArrayPad$ = 368
this$ = 400
?dtor$0@?0??ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA PROC ; `mu2::RedisPacket::AppChatParser::ConvertToAppChat'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8d 8d 20 01
	00 00		 lea	 rcx, QWORD PTR cname$[rbp]
  00010	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$0@?0??ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA ENDP ; `mu2::RedisPacket::AppChatParser::ConvertToAppChat'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 64
this$ = 72
_Old_size$2 = 80
_Old_size$3 = 88
this$ = 96
this$ = 104
$T4 = 112
$T5 = 120
this$ = 128
$T6 = 136
$T7 = 144
$T8 = 152
$T9 = 160
$T10 = 168
$T11 = 176
$T12 = 184
$T13 = 192
$T14 = 200
wslen$ = 208
tv188 = 216
msg$ = 224
chat$ = 256
cname$ = 288
kidbuf$ = 320
cidbuf$ = 336
wsname$ = 352
__$ArrayPad$ = 368
this$ = 400
?dtor$1@?0??ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA PROC ; `mu2::RedisPacket::AppChatParser::ConvertToAppChat'::`1'::dtor$1
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8d 8d 00 01
	00 00		 lea	 rcx, QWORD PTR chat$[rbp]
  00010	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$1@?0??ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA ENDP ; `mu2::RedisPacket::AppChatParser::ConvertToAppChat'::`1'::dtor$1
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 64
this$ = 72
_Old_size$2 = 80
_Old_size$3 = 88
this$ = 96
this$ = 104
$T4 = 112
$T5 = 120
this$ = 128
$T6 = 136
$T7 = 144
$T8 = 152
$T9 = 160
$T10 = 168
$T11 = 176
$T12 = 184
$T13 = 192
$T14 = 200
wslen$ = 208
tv188 = 216
msg$ = 224
chat$ = 256
cname$ = 288
kidbuf$ = 320
cidbuf$ = 336
wsname$ = 352
__$ArrayPad$ = 368
this$ = 400
?dtor$2@?0??ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA PROC ; `mu2::RedisPacket::AppChatParser::ConvertToAppChat'::`1'::dtor$2
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8d 8d e0 00
	00 00		 lea	 rcx, QWORD PTR msg$[rbp]
  00010	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$2@?0??ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA ENDP ; `mu2::RedisPacket::AppChatParser::ConvertToAppChat'::`1'::dtor$2
text$x	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp
;	COMDAT ?Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ
_TEXT	SEGMENT
_My_data$1 = 32
tempSplit$ = 40
$T2 = 64
_My_data$3 = 72
$T4 = 80
_My_data$5 = 88
$T6 = 96
_My_data$7 = 104
$T8 = 112
_My_data$9 = 120
$T10 = 128
$T11 = 136
$T12 = 168
__$ArrayPad$ = 200
this$ = 224
?Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ PROC	; mu2::RedisPacket::AppChatParser::Parse, COMDAT

; 33   : 		{

$LN412:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec d8 00
	00 00		 sub	 rsp, 216		; 000000d8H
  0000c	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR __security_cookie
  00013	48 33 c4	 xor	 rax, rsp
  00016	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR __$ArrayPad$[rsp], rax

; 34   : 			// 결과 m_convertResult 에 쓸것
; 35   : 			StringUtil::StringVector tempSplit = StringUtil::split(m_original.c_str(), "\t");

  0001e	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:??_C@_01GPOEFGEJ@?7@
  00025	48 8d 8c 24 a8
	00 00 00	 lea	 rcx, QWORD PTR $T12[rsp]
  0002d	e8 00 00 00 00	 call	 ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@QEBD@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
  00032	90		 npad	 1
  00033	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0003b	48 05 98 08 00
	00		 add	 rax, 2200		; 00000898H
  00041	48 8b c8	 mov	 rcx, rax
  00044	e8 00 00 00 00	 call	 ?c_str@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBAPEBDXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::c_str
  00049	48 8b d0	 mov	 rdx, rax
  0004c	48 8d 8c 24 88
	00 00 00	 lea	 rcx, QWORD PTR $T11[rsp]
  00054	e8 00 00 00 00	 call	 ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@QEBD@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
  00059	90		 npad	 1
  0005a	45 33 c9	 xor	 r9d, r9d
  0005d	4c 8d 84 24 a8
	00 00 00	 lea	 r8, QWORD PTR $T12[rsp]
  00065	48 8d 94 24 88
	00 00 00	 lea	 rdx, QWORD PTR $T11[rsp]
  0006d	48 8d 4c 24 28	 lea	 rcx, QWORD PTR tempSplit$[rsp]
  00072	e8 00 00 00 00	 call	 ?split@StringUtil@mu2@@SA?AV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@4@0I@Z ; mu2::StringUtil::split
  00077	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1383 :         _Tidy_deallocate();

  00078	48 8d 8c 24 88
	00 00 00	 lea	 rcx, QWORD PTR $T11[rsp]
  00080	e8 00 00 00 00	 call	 ?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Tidy_deallocate
  00085	90		 npad	 1
  00086	48 8d 8c 24 a8
	00 00 00	 lea	 rcx, QWORD PTR $T12[rsp]
  0008e	e8 00 00 00 00	 call	 ?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Tidy_deallocate
  00093	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1914 :         auto& _My_data = _Mypair._Myval2;

  00094	48 8d 44 24 28	 lea	 rax, QWORD PTR tempSplit$[rsp]
  00099	48 89 44 24 20	 mov	 QWORD PTR _My_data$1[rsp], rax

; 1915 :         return static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst);

  0009e	48 8b 44 24 20	 mov	 rax, QWORD PTR _My_data$1[rsp]
  000a3	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _My_data$1[rsp]
  000a8	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  000ab	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  000af	48 2b c1	 sub	 rax, rcx
  000b2	48 c1 f8 05	 sar	 rax, 5
  000b6	48 89 44 24 40	 mov	 QWORD PTR $T2[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp

; 38   : 			if (tempSplit.size() < 4)

  000bb	48 8b 44 24 40	 mov	 rax, QWORD PTR $T2[rsp]
  000c0	48 83 f8 04	 cmp	 rax, 4
  000c4	73 1f		 jae	 SHORT $LN2@Parse

; 39   : 			{
; 40   : 				m_convertResult = false;

  000c6	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000ce	c6 80 b8 08 00
	00 00		 mov	 BYTE PTR [rax+2232], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 830  :         _Tidy();

  000d5	48 8d 4c 24 28	 lea	 rcx, QWORD PTR tempSplit$[rsp]
  000da	e8 00 00 00 00	 call	 ?_Tidy@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@AEAAXXZ ; std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Tidy
  000df	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp

; 41   : 				return;

  000e0	e9 23 01 00 00	 jmp	 $LN1@Parse
$LN2@Parse:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1928 :         auto& _My_data = _Mypair._Myval2;

  000e5	48 8d 44 24 28	 lea	 rax, QWORD PTR tempSplit$[rsp]
  000ea	48 89 44 24 48	 mov	 QWORD PTR _My_data$3[rsp], rax

; 1929 : #if _MSVC_STL_HARDENING_VECTOR || _ITERATOR_DEBUG_LEVEL != 0
; 1930 :         _STL_VERIFY(
; 1931 :             _Pos < static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst), "vector subscript out of range");
; 1932 : #endif
; 1933 : 
; 1934 :         return _My_data._Myfirst[_Pos];

  000ef	33 c0		 xor	 eax, eax
  000f1	48 6b c0 20	 imul	 rax, rax, 32		; 00000020H
  000f5	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _My_data$3[rsp]
  000fa	48 03 01	 add	 rax, QWORD PTR [rcx]
  000fd	48 89 44 24 50	 mov	 QWORD PTR $T4[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp

; 44   : 			m_knightageId = atoi(tempSplit[0].c_str());

  00102	48 8b 44 24 50	 mov	 rax, QWORD PTR $T4[rsp]
  00107	48 8b c8	 mov	 rcx, rax
  0010a	e8 00 00 00 00	 call	 ?c_str@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBAPEBDXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::c_str
  0010f	48 8b c8	 mov	 rcx, rax
  00112	e8 00 00 00 00	 call	 atoi
  00117	48 8b 8c 24 e0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0011f	89 01		 mov	 DWORD PTR [rcx], eax

; 45   : 			if (0 == m_knightageId)

  00121	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00129	83 38 00	 cmp	 DWORD PTR [rax], 0
  0012c	75 10		 jne	 SHORT $LN3@Parse
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 830  :         _Tidy();

  0012e	48 8d 4c 24 28	 lea	 rcx, QWORD PTR tempSplit$[rsp]
  00133	e8 00 00 00 00	 call	 ?_Tidy@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@AEAAXXZ ; std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Tidy
  00138	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp

; 46   : 				return;

  00139	e9 ca 00 00 00	 jmp	 $LN1@Parse
$LN3@Parse:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1928 :         auto& _My_data = _Mypair._Myval2;

  0013e	48 8d 44 24 28	 lea	 rax, QWORD PTR tempSplit$[rsp]
  00143	48 89 44 24 58	 mov	 QWORD PTR _My_data$5[rsp], rax

; 1929 : #if _MSVC_STL_HARDENING_VECTOR || _ITERATOR_DEBUG_LEVEL != 0
; 1930 :         _STL_VERIFY(
; 1931 :             _Pos < static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst), "vector subscript out of range");
; 1932 : #endif
; 1933 : 
; 1934 :         return _My_data._Myfirst[_Pos];

  00148	b8 01 00 00 00	 mov	 eax, 1
  0014d	48 6b c0 20	 imul	 rax, rax, 32		; 00000020H
  00151	48 8b 4c 24 58	 mov	 rcx, QWORD PTR _My_data$5[rsp]
  00156	48 03 01	 add	 rax, QWORD PTR [rcx]
  00159	48 89 44 24 60	 mov	 QWORD PTR $T6[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp

; 47   : 			m_charId = atoi(tempSplit[1].c_str());

  0015e	48 8b 44 24 60	 mov	 rax, QWORD PTR $T6[rsp]
  00163	48 8b c8	 mov	 rcx, rax
  00166	e8 00 00 00 00	 call	 ?c_str@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBAPEBDXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::c_str
  0016b	48 8b c8	 mov	 rcx, rax
  0016e	e8 00 00 00 00	 call	 atoi
  00173	48 8b 8c 24 e0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0017b	89 41 04	 mov	 DWORD PTR [rcx+4], eax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1928 :         auto& _My_data = _Mypair._Myval2;

  0017e	48 8d 44 24 28	 lea	 rax, QWORD PTR tempSplit$[rsp]
  00183	48 89 44 24 68	 mov	 QWORD PTR _My_data$7[rsp], rax

; 1929 : #if _MSVC_STL_HARDENING_VECTOR || _ITERATOR_DEBUG_LEVEL != 0
; 1930 :         _STL_VERIFY(
; 1931 :             _Pos < static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst), "vector subscript out of range");
; 1932 : #endif
; 1933 : 
; 1934 :         return _My_data._Myfirst[_Pos];

  00188	b8 02 00 00 00	 mov	 eax, 2
  0018d	48 6b c0 20	 imul	 rax, rax, 32		; 00000020H
  00191	48 8b 4c 24 68	 mov	 rcx, QWORD PTR _My_data$7[rsp]
  00196	48 03 01	 add	 rax, QWORD PTR [rcx]
  00199	48 89 44 24 70	 mov	 QWORD PTR $T8[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp

; 52   : 			StringUtil::Convert(tempSplit[2], m_charName);

  0019e	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  001a6	48 83 c0 08	 add	 rax, 8
  001aa	48 8b 4c 24 70	 mov	 rcx, QWORD PTR $T8[rsp]
  001af	45 33 c0	 xor	 r8d, r8d
  001b2	48 8b d0	 mov	 rdx, rax
  001b5	e8 00 00 00 00	 call	 ?Convert@StringUtil@mu2@@SA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@4@H@Z ; mu2::StringUtil::Convert
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1928 :         auto& _My_data = _Mypair._Myval2;

  001ba	48 8d 44 24 28	 lea	 rax, QWORD PTR tempSplit$[rsp]
  001bf	48 89 44 24 78	 mov	 QWORD PTR _My_data$9[rsp], rax

; 1929 : #if _MSVC_STL_HARDENING_VECTOR || _ITERATOR_DEBUG_LEVEL != 0
; 1930 :         _STL_VERIFY(
; 1931 :             _Pos < static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst), "vector subscript out of range");
; 1932 : #endif
; 1933 : 
; 1934 :         return _My_data._Myfirst[_Pos];

  001c4	b8 03 00 00 00	 mov	 eax, 3
  001c9	48 6b c0 20	 imul	 rax, rax, 32		; 00000020H
  001cd	48 8b 4c 24 78	 mov	 rcx, QWORD PTR _My_data$9[rsp]
  001d2	48 03 01	 add	 rax, QWORD PTR [rcx]
  001d5	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp

; 53   : 			StringUtil::Convert(tempSplit[3], m_chat);

  001dd	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  001e5	48 83 c0 28	 add	 rax, 40			; 00000028H
  001e9	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR $T10[rsp]
  001f1	45 33 c0	 xor	 r8d, r8d
  001f4	48 8b d0	 mov	 rdx, rax
  001f7	e8 00 00 00 00	 call	 ?Convert@StringUtil@mu2@@SA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@4@H@Z ; mu2::StringUtil::Convert
  001fc	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 830  :         _Tidy();

  001fd	48 8d 4c 24 28	 lea	 rcx, QWORD PTR tempSplit$[rsp]
  00202	e8 00 00 00 00	 call	 ?_Tidy@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@AEAAXXZ ; std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Tidy
  00207	90		 npad	 1
$LN1@Parse:
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp

; 55   : 		}

  00208	48 8b 8c 24 c8
	00 00 00	 mov	 rcx, QWORD PTR __$ArrayPad$[rsp]
  00210	48 33 cc	 xor	 rcx, rsp
  00213	e8 00 00 00 00	 call	 __security_check_cookie
  00218	48 81 c4 d8 00
	00 00		 add	 rsp, 216		; 000000d8H
  0021f	c3		 ret	 0
?Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ ENDP	; mu2::RedisPacket::AppChatParser::Parse
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
_My_data$1 = 32
tempSplit$ = 40
$T2 = 64
_My_data$3 = 72
$T4 = 80
_My_data$5 = 88
$T6 = 96
_My_data$7 = 104
$T8 = 112
_My_data$9 = 120
$T10 = 128
$T11 = 136
$T12 = 168
__$ArrayPad$ = 200
this$ = 224
?dtor$0@?0??Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA PROC ; `mu2::RedisPacket::AppChatParser::Parse'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8d 8d a8 00
	00 00		 lea	 rcx, QWORD PTR $T12[rbp]
  00010	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$0@?0??Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA ENDP ; `mu2::RedisPacket::AppChatParser::Parse'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
_My_data$1 = 32
tempSplit$ = 40
$T2 = 64
_My_data$3 = 72
$T4 = 80
_My_data$5 = 88
$T6 = 96
_My_data$7 = 104
$T8 = 112
_My_data$9 = 120
$T10 = 128
$T11 = 136
$T12 = 168
__$ArrayPad$ = 200
this$ = 224
?dtor$1@?0??Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA PROC ; `mu2::RedisPacket::AppChatParser::Parse'::`1'::dtor$1
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8d 8d 88 00
	00 00		 lea	 rcx, QWORD PTR $T11[rbp]
  00010	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$1@?0??Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA ENDP ; `mu2::RedisPacket::AppChatParser::Parse'::`1'::dtor$1
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
_My_data$1 = 32
tempSplit$ = 40
$T2 = 64
_My_data$3 = 72
$T4 = 80
_My_data$5 = 88
$T6 = 96
_My_data$7 = 104
$T8 = 112
_My_data$9 = 120
$T10 = 128
$T11 = 136
$T12 = 168
__$ArrayPad$ = 200
this$ = 224
?dtor$2@?0??Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA PROC ; `mu2::RedisPacket::AppChatParser::Parse'::`1'::dtor$2
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8d 4d 28	 lea	 rcx, QWORD PTR tempSplit$[rbp]
  0000d	e8 00 00 00 00	 call	 ??1?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QEAA@XZ ; std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::~vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$2@?0??Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ@4HA ENDP ; `mu2::RedisPacket::AppChatParser::Parse'::`1'::dtor$2
text$x	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp
;	COMDAT ??0AppChatParser@RedisPacket@mu2@@QEAA@IIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z
_TEXT	SEGMENT
this$ = 48
kid$ = 56
cid$ = 64
name$ = 72
msg$ = 80
??0AppChatParser@RedisPacket@mu2@@QEAA@IIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z PROC ; mu2::RedisPacket::AppChatParser::AppChatParser, COMDAT

; 28   : 		{

$LN253:
  00000	4c 89 4c 24 20	 mov	 QWORD PTR [rsp+32], r9
  00005	44 89 44 24 18	 mov	 DWORD PTR [rsp+24], r8d
  0000a	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  0000e	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00013	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 27   : 			: m_knightageId(kid), m_charId(cid), m_charName(name), m_chat(msg)

  00017	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001c	8b 4c 24 38	 mov	 ecx, DWORD PTR kid$[rsp]
  00020	89 08		 mov	 DWORD PTR [rax], ecx
  00022	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00027	8b 4c 24 40	 mov	 ecx, DWORD PTR cid$[rsp]
  0002b	89 48 04	 mov	 DWORD PTR [rax+4], ecx
  0002e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00033	48 83 c0 08	 add	 rax, 8
  00037	48 8b 54 24 48	 mov	 rdx, QWORD PTR name$[rsp]
  0003c	48 8b c8	 mov	 rcx, rax
  0003f	e8 00 00 00 00	 call	 ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  00044	90		 npad	 1
  00045	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004a	48 83 c0 28	 add	 rax, 40			; 00000028H
  0004e	48 8b 54 24 50	 mov	 rdx, QWORD PTR msg$[rsp]
  00053	48 8b c8	 mov	 rcx, rax
  00056	e8 00 00 00 00	 call	 ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  0005b	90		 npad	 1

; 28   : 		{

  0005c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00061	48 05 98 08 00
	00		 add	 rax, 2200		; 00000898H
  00067	48 8b c8	 mov	 rcx, rax
  0006a	e8 00 00 00 00	 call	 ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
  0006f	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.h

; 45   : 			Bool m_convertResult = false;

  00070	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00075	c6 80 b8 08 00
	00 00		 mov	 BYTE PTR [rax+2232], 0
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp

; 29   : 			ConvertToAppChat();

  0007c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00081	e8 00 00 00 00	 call	 ?ConvertToAppChat@AppChatParser@RedisPacket@mu2@@AEAAXXZ ; mu2::RedisPacket::AppChatParser::ConvertToAppChat
  00086	90		 npad	 1

; 30   : 		}

  00087	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0008c	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00090	c3		 ret	 0
??0AppChatParser@RedisPacket@mu2@@QEAA@IIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z ENDP ; mu2::RedisPacket::AppChatParser::AppChatParser
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
this$ = 48
kid$ = 56
cid$ = 64
name$ = 72
msg$ = 80
?dtor$0@?0???0AppChatParser@RedisPacket@mu2@@QEAA@IIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z@4HA PROC ; `mu2::RedisPacket::AppChatParser::AppChatParser'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 30	 mov	 rcx, QWORD PTR this$[rbp]
  0000d	48 83 c1 08	 add	 rcx, 8
  00011	e8 00 00 00 00	 call	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  00016	48 83 c4 20	 add	 rsp, 32			; 00000020H
  0001a	5d		 pop	 rbp
  0001b	c3		 ret	 0
?dtor$0@?0???0AppChatParser@RedisPacket@mu2@@QEAA@IIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z@4HA ENDP ; `mu2::RedisPacket::AppChatParser::AppChatParser'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
this$ = 48
kid$ = 56
cid$ = 64
name$ = 72
msg$ = 80
?dtor$1@?0???0AppChatParser@RedisPacket@mu2@@QEAA@IIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z@4HA PROC ; `mu2::RedisPacket::AppChatParser::AppChatParser'::`1'::dtor$1
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 30	 mov	 rcx, QWORD PTR this$[rbp]
  0000d	48 83 c1 28	 add	 rcx, 40			; 00000028H
  00011	e8 00 00 00 00	 call	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  00016	48 83 c4 20	 add	 rsp, 32			; 00000020H
  0001a	5d		 pop	 rbp
  0001b	c3		 ret	 0
?dtor$1@?0???0AppChatParser@RedisPacket@mu2@@QEAA@IIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z@4HA ENDP ; `mu2::RedisPacket::AppChatParser::AppChatParser'::`1'::dtor$1
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
this$ = 48
kid$ = 56
cid$ = 64
name$ = 72
msg$ = 80
?dtor$2@?0???0AppChatParser@RedisPacket@mu2@@QEAA@IIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z@4HA PROC ; `mu2::RedisPacket::AppChatParser::AppChatParser'::`1'::dtor$2
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 30	 mov	 rcx, QWORD PTR this$[rbp]
  0000d	48 81 c1 98 08
	00 00		 add	 rcx, 2200		; 00000898H
  00014	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
  00019	48 83 c4 20	 add	 rsp, 32			; 00000020H
  0001d	5d		 pop	 rbp
  0001e	c3		 ret	 0
?dtor$2@?0???0AppChatParser@RedisPacket@mu2@@QEAA@IIAEBV?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@0@Z@4HA ENDP ; `mu2::RedisPacket::AppChatParser::AppChatParser'::`1'::dtor$2
text$x	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp
;	COMDAT ??0AppChatParser@RedisPacket@mu2@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
_TEXT	SEGMENT
this$ = 48
msg$ = 56
??0AppChatParser@RedisPacket@mu2@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z PROC ; mu2::RedisPacket::AppChatParser::AppChatParser, COMDAT

; 21   : 		{

$LN313:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00013	48 83 c0 08	 add	 rax, 8
  00017	48 8b c8	 mov	 rcx, rax
  0001a	e8 00 00 00 00	 call	 ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  0001f	90		 npad	 1
  00020	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00025	48 83 c0 28	 add	 rax, 40			; 00000028H
  00029	48 8b c8	 mov	 rcx, rax
  0002c	e8 00 00 00 00	 call	 ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  00031	90		 npad	 1

; 20   : 			: m_original(msg)

  00032	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00037	48 05 98 08 00
	00		 add	 rax, 2200		; 00000898H
  0003d	48 8b 54 24 38	 mov	 rdx, QWORD PTR msg$[rsp]
  00042	48 8b c8	 mov	 rcx, rax
  00045	e8 00 00 00 00	 call	 ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
  0004a	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.h

; 45   : 			Bool m_convertResult = false;

  0004b	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00050	c6 80 b8 08 00
	00 00		 mov	 BYTE PTR [rax+2232], 0
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp

; 22   : 			Parse();

  00057	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0005c	e8 00 00 00 00	 call	 ?Parse@AppChatParser@RedisPacket@mu2@@AEAAXXZ ; mu2::RedisPacket::AppChatParser::Parse
  00061	90		 npad	 1

; 23   : 		}

  00062	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00067	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0006b	c3		 ret	 0
??0AppChatParser@RedisPacket@mu2@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z ENDP ; mu2::RedisPacket::AppChatParser::AppChatParser
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
this$ = 48
msg$ = 56
?dtor$0@?0???0AppChatParser@RedisPacket@mu2@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@4HA PROC ; `mu2::RedisPacket::AppChatParser::AppChatParser'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 30	 mov	 rcx, QWORD PTR this$[rbp]
  0000d	48 83 c1 08	 add	 rcx, 8
  00011	e8 00 00 00 00	 call	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  00016	48 83 c4 20	 add	 rsp, 32			; 00000020H
  0001a	5d		 pop	 rbp
  0001b	c3		 ret	 0
?dtor$0@?0???0AppChatParser@RedisPacket@mu2@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@4HA ENDP ; `mu2::RedisPacket::AppChatParser::AppChatParser'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
this$ = 48
msg$ = 56
?dtor$1@?0???0AppChatParser@RedisPacket@mu2@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@4HA PROC ; `mu2::RedisPacket::AppChatParser::AppChatParser'::`1'::dtor$1
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 30	 mov	 rcx, QWORD PTR this$[rbp]
  0000d	48 83 c1 28	 add	 rcx, 40			; 00000028H
  00011	e8 00 00 00 00	 call	 ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
  00016	48 83 c4 20	 add	 rsp, 32			; 00000020H
  0001a	5d		 pop	 rbp
  0001b	c3		 ret	 0
?dtor$1@?0???0AppChatParser@RedisPacket@mu2@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@4HA ENDP ; `mu2::RedisPacket::AppChatParser::AppChatParser'::`1'::dtor$1
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
this$ = 48
msg$ = 56
?dtor$2@?0???0AppChatParser@RedisPacket@mu2@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@4HA PROC ; `mu2::RedisPacket::AppChatParser::AppChatParser'::`1'::dtor$2
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 30	 mov	 rcx, QWORD PTR this$[rbp]
  0000d	48 81 c1 98 08
	00 00		 add	 rcx, 2200		; 00000898H
  00014	e8 00 00 00 00	 call	 ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
  00019	48 83 c4 20	 add	 rsp, 32			; 00000020H
  0001d	5d		 pop	 rbp
  0001e	c3		 ret	 0
?dtor$2@?0???0AppChatParser@RedisPacket@mu2@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z@4HA ENDP ; `mu2::RedisPacket::AppChatParser::AppChatParser'::`1'::dtor$2
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ?_Tidy@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@AEAAXXZ
_TEXT	SEGMENT
_Myfirst$ = 32
_First$ = 40
_My_data$ = 48
_Bytes$ = 56
_Ptr$ = 64
_Ptr$ = 72
_Mylast$ = 80
_Myend$ = 88
$T1 = 96
$T2 = 104
_Last$ = 112
$T3 = 120
_Count$ = 128
_Ptr$ = 136
_Al$ = 144
this$ = 176
?_Tidy@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@AEAAXXZ PROC ; std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Tidy, COMDAT

; 2081 :     _CONSTEXPR20 void _Tidy() noexcept { // free all storage

$LN139:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec a8 00
	00 00		 sub	 rsp, 168		; 000000a8H

; 2227 :         return _Mypair._Get_first();

  0000c	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00014	48 89 44 24 60	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2227 :         return _Mypair._Get_first();

  00019	48 8b 44 24 60	 mov	 rax, QWORD PTR $T1[rsp]
  0001e	48 89 44 24 68	 mov	 QWORD PTR $T2[rsp], rax

; 2082 :         auto& _Al         = _Getal();

  00023	48 8b 44 24 68	 mov	 rax, QWORD PTR $T2[rsp]
  00028	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR _Al$[rsp], rax

; 2083 :         auto& _My_data    = _Mypair._Myval2;

  00030	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 89 44 24 30	 mov	 QWORD PTR _My_data$[rsp], rax

; 2084 :         pointer& _Myfirst = _My_data._Myfirst;

  0003d	48 8b 44 24 30	 mov	 rax, QWORD PTR _My_data$[rsp]
  00042	48 89 44 24 20	 mov	 QWORD PTR _Myfirst$[rsp], rax

; 2085 :         pointer& _Mylast  = _My_data._Mylast;

  00047	48 8b 44 24 30	 mov	 rax, QWORD PTR _My_data$[rsp]
  0004c	48 83 c0 08	 add	 rax, 8
  00050	48 89 44 24 50	 mov	 QWORD PTR _Mylast$[rsp], rax

; 2086 :         pointer& _Myend   = _My_data._Myend;

  00055	48 8b 44 24 30	 mov	 rax, QWORD PTR _My_data$[rsp]
  0005a	48 83 c0 10	 add	 rax, 16
  0005e	48 89 44 24 58	 mov	 QWORD PTR _Myend$[rsp], rax

; 2087 : 
; 2088 :         _My_data._Orphan_all();
; 2089 : 
; 2090 :         if (_Myfirst) { // destroy and deallocate old array

  00063	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  00068	48 83 38 00	 cmp	 QWORD PTR [rax], 0
  0006c	0f 84 09 01 00
	00		 je	 $LN2@Tidy

; 2091 :             _STD _Destroy_range(_Myfirst, _Mylast, _Al);

  00072	48 8b 44 24 50	 mov	 rax, QWORD PTR _Mylast$[rsp]
  00077	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0007a	48 89 44 24 70	 mov	 QWORD PTR _Last$[rsp], rax
  0007f	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  00084	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00087	48 89 44 24 28	 mov	 QWORD PTR _First$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1102 :         for (; _First != _Last; ++_First) {

  0008c	eb 0e		 jmp	 SHORT $LN23@Tidy
$LN21@Tidy:
  0008e	48 8b 44 24 28	 mov	 rax, QWORD PTR _First$[rsp]
  00093	48 83 c0 20	 add	 rax, 32			; 00000020H
  00097	48 89 44 24 28	 mov	 QWORD PTR _First$[rsp], rax
$LN23@Tidy:
  0009c	48 8b 44 24 70	 mov	 rax, QWORD PTR _Last$[rsp]
  000a1	48 39 44 24 28	 cmp	 QWORD PTR _First$[rsp], rax
  000a6	74 3a		 je	 SHORT $LN22@Tidy

; 69   :     return _Ptr;

  000a8	48 8b 44 24 28	 mov	 rax, QWORD PTR _First$[rsp]
  000ad	48 89 44 24 78	 mov	 QWORD PTR $T3[rsp], rax

; 1103 :             allocator_traits<_Alloc>::destroy(_Al, _STD _Unfancy(_First));

  000b2	48 8b 44 24 78	 mov	 rax, QWORD PTR $T3[rsp]
  000b7	48 89 44 24 40	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1383 :         _Tidy_deallocate();

  000bc	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  000c1	e8 00 00 00 00	 call	 ?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Tidy_deallocate
  000c6	90		 npad	 1
  000c7	33 c0		 xor	 eax, eax
  000c9	83 e0 01	 and	 eax, 1
  000cc	85 c0		 test	 eax, eax
  000ce	74 10		 je	 SHORT $LN36@Tidy
  000d0	ba 20 00 00 00	 mov	 edx, 32			; 00000020H
  000d5	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  000da	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  000df	90		 npad	 1
$LN36@Tidy:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1104 :         }

  000e0	eb ac		 jmp	 SHORT $LN21@Tidy
$LN22@Tidy:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2093 :             _Al.deallocate(_Myfirst, static_cast<size_type>(_Myend - _Myfirst));

  000e2	48 8b 44 24 58	 mov	 rax, QWORD PTR _Myend$[rsp]
  000e7	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Myfirst$[rsp]
  000ec	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  000ef	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000f2	48 2b c1	 sub	 rax, rcx
  000f5	48 c1 f8 05	 sar	 rax, 5
  000f9	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _Count$[rsp], rax
  00101	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  00106	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00109	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  00111	48 6b 84 24 80
	00 00 00 20	 imul	 rax, QWORD PTR _Count$[rsp], 32 ; 00000020H
  0011a	48 89 44 24 38	 mov	 QWORD PTR _Bytes$[rsp], rax
  0011f	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00127	48 89 44 24 48	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  0012c	48 81 7c 24 38
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  00135	72 10		 jb	 SHORT $LN130@Tidy

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  00137	48 8d 54 24 38	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  0013c	48 8d 4c 24 48	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  00141	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  00146	90		 npad	 1
$LN130@Tidy:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  00147	48 8b 54 24 38	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  0014c	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  00151	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00156	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2095 :             _Myfirst = nullptr;

  00157	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  0015c	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0

; 2096 :             _Mylast  = nullptr;

  00163	48 8b 44 24 50	 mov	 rax, QWORD PTR _Mylast$[rsp]
  00168	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0

; 2097 :             _Myend   = nullptr;

  0016f	48 8b 44 24 58	 mov	 rax, QWORD PTR _Myend$[rsp]
  00174	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0
$LN2@Tidy:

; 2098 :         }
; 2099 :     }

  0017b	48 81 c4 a8 00
	00 00		 add	 rsp, 168		; 000000a8H
  00182	c3		 ret	 0
?_Tidy@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@AEAAXXZ ENDP ; std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Tidy
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ??1?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QEAA@XZ PROC ; std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::~vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >, COMDAT

; 829  :     _CONSTEXPR20 ~vector() noexcept {

$LN140:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 830  :         _Tidy();

  00009	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0000e	e8 00 00 00 00	 call	 ?_Tidy@?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@AEAAXXZ ; std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::_Tidy
  00013	90		 npad	 1

; 831  : #if _ITERATOR_DEBUG_LEVEL != 0
; 832  :         auto&& _Alproxy = _GET_PROXY_ALLOCATOR(_Alty, _Getal());
; 833  :         _Delete_plain_internal(_Alproxy, _STD exchange(_Mypair._Myval2._Myproxy, nullptr));
; 834  : #endif // _ITERATOR_DEBUG_LEVEL != 0
; 835  :     }

  00014	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00018	c3		 ret	 0
??1?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@QEAA@XZ ENDP ; std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >::~vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > >
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??1?$_Compressed_pair@V?$allocator@_W@std@@V?$_String_val@U?$_Simple_types@_W@std@@@2@$00@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 8
??1?$_Compressed_pair@V?$allocator@_W@std@@V?$_String_val@U?$_Simple_types@_W@std@@@2@$00@std@@QEAA@XZ PROC ; std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>::~_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>, COMDAT
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
??1?$_Compressed_pair@V?$allocator@_W@std@@V?$_String_val@U?$_Simple_types@_W@std@@@2@$00@std@@QEAA@XZ ENDP ; std::_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>::~_Compressed_pair<std::allocator<wchar_t>,std::_String_val<std::_Simple_types<wchar_t> >,1>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
_TEXT	SEGMENT
$T1 = 32
$T2 = 34
_My_data$ = 40
tv94 = 48
_Bytes$ = 56
_Ptr$ = 64
$T3 = 72
$T4 = 80
_Capacity$ = 88
_Old_ptr$ = 96
_Al$5 = 104
this$ = 128
?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate, COMDAT

; 3080 :     _CONSTEXPR20 void _Tidy_deallocate() noexcept { // initialize buffer, deallocating any storage

$LN62:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 78	 sub	 rsp, 120		; 00000078H

; 3081 :         auto& _My_data = _Mypair._Myval2;

  00009	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00011	48 89 44 24 28	 mov	 QWORD PTR _My_data$[rsp], rax

; 453  :         return _Myres > _Small_string_capacity;

  00016	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  0001b	48 83 78 18 07	 cmp	 QWORD PTR [rax+24], 7
  00020	76 0a		 jbe	 SHORT $LN12@Tidy_deall
  00022	c7 44 24 30 01
	00 00 00	 mov	 DWORD PTR tv94[rsp], 1
  0002a	eb 08		 jmp	 SHORT $LN13@Tidy_deall
$LN12@Tidy_deall:
  0002c	c7 44 24 30 00
	00 00 00	 mov	 DWORD PTR tv94[rsp], 0
$LN13@Tidy_deall:
  00034	0f b6 44 24 30	 movzx	 eax, BYTE PTR tv94[rsp]
  00039	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al

; 3082 :         _My_data._Orphan_all();
; 3083 :         if (_My_data._Large_mode_engaged()) {

  0003d	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  00042	0f b6 c0	 movzx	 eax, al
  00045	85 c0		 test	 eax, eax
  00047	0f 84 80 00 00
	00		 je	 $LN2@Tidy_deall

; 3107 :         return _Mypair._Get_first();

  0004d	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00055	48 89 44 24 48	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3107 :         return _Mypair._Get_first();

  0005a	48 8b 44 24 48	 mov	 rax, QWORD PTR $T3[rsp]
  0005f	48 89 44 24 50	 mov	 QWORD PTR $T4[rsp], rax

; 3084 :             _ASAN_STRING_REMOVE(*this);
; 3085 :             auto& _Al = _Getal();

  00064	48 8b 44 24 50	 mov	 rax, QWORD PTR $T4[rsp]
  00069	48 89 44 24 68	 mov	 QWORD PTR _Al$5[rsp], rax

; 3086 :             _Deallocate_for_capacity(_Al, _My_data._Bx._Ptr, _My_data._Myres);

  0006e	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00073	48 8b 40 18	 mov	 rax, QWORD PTR [rax+24]
  00077	48 89 44 24 58	 mov	 QWORD PTR _Capacity$[rsp], rax
  0007c	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00081	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00084	48 89 44 24 60	 mov	 QWORD PTR _Old_ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  00089	48 8b 44 24 58	 mov	 rax, QWORD PTR _Capacity$[rsp]
  0008e	48 8d 44 00 02	 lea	 rax, QWORD PTR [rax+rax+2]
  00093	48 89 44 24 38	 mov	 QWORD PTR _Bytes$[rsp], rax
  00098	48 8b 44 24 60	 mov	 rax, QWORD PTR _Old_ptr$[rsp]
  0009d	48 89 44 24 40	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  000a2	48 81 7c 24 38
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  000ab	72 10		 jb	 SHORT $LN38@Tidy_deall

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  000ad	48 8d 54 24 38	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  000b2	48 8d 4c 24 40	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  000b7	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  000bc	90		 npad	 1
$LN38@Tidy_deall:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  000bd	48 8b 54 24 38	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  000c2	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  000c7	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  000cc	90		 npad	 1
$LN2@Tidy_deall:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3090 :         _My_data._Mysize = 0;

  000cd	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000d2	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 3091 :         _My_data._Myres  = _Small_string_capacity;

  000da	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000df	48 c7 40 18 07
	00 00 00	 mov	 QWORD PTR [rax+24], 7

; 3092 :         // the _Traits::assign is last so the codegen doesn't think the char write can alias this
; 3093 :         _Traits::assign(_My_data._Bx._Buf[0], _Elem());

  000e7	33 c0		 xor	 eax, eax
  000e9	66 89 44 24 22	 mov	 WORD PTR $T2[rsp], ax
  000ee	b8 02 00 00 00	 mov	 eax, 2
  000f3	48 6b c0 00	 imul	 rax, rax, 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 347  :         _Left = _Right;

  000f7	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _My_data$[rsp]
  000fc	0f b7 54 24 22	 movzx	 edx, WORD PTR $T2[rsp]
  00101	66 89 14 01	 mov	 WORD PTR [rcx+rax], dx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3094 :     }

  00105	48 83 c4 78	 add	 rsp, 120		; 00000078H
  00109	c3		 ret	 0
?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Eos@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAX_K@Z
_TEXT	SEGMENT
$T1 = 0
$T2 = 2
tv87 = 4
this$ = 8
_Result$3 = 16
_Ptr$ = 24
$T4 = 32
$T5 = 40
this$ = 64
_New_size$ = 72
?_Eos@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAX_K@Z PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Eos, COMDAT

; 3074 :     _CONSTEXPR20 void _Eos(const size_type _New_size) noexcept { // set new length and null terminator

$LN27:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 3075 :         _ASAN_STRING_MODIFY(*this, _Mypair._Myval2._Mysize, _New_size);
; 3076 :         _Mypair._Myval2._Mysize = _New_size;

  0000e	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00013	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _New_size$[rsp]
  00018	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 3077 :         _Traits::assign(_Mypair._Myval2._Myptr()[_New_size], _Elem());

  0001c	33 c0		 xor	 eax, eax
  0001e	66 89 44 24 02	 mov	 WORD PTR $T2[rsp], ax
  00023	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00028	48 89 44 24 08	 mov	 QWORD PTR this$[rsp], rax

; 435  :         value_type* _Result = _Bx._Buf;

  0002d	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00032	48 89 44 24 10	 mov	 QWORD PTR _Result$3[rsp], rax

; 453  :         return _Myres > _Small_string_capacity;

  00037	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0003c	48 83 78 18 07	 cmp	 QWORD PTR [rax+24], 7
  00041	76 0a		 jbe	 SHORT $LN12@Eos
  00043	c7 44 24 04 01
	00 00 00	 mov	 DWORD PTR tv87[rsp], 1
  0004b	eb 08		 jmp	 SHORT $LN13@Eos
$LN12@Eos:
  0004d	c7 44 24 04 00
	00 00 00	 mov	 DWORD PTR tv87[rsp], 0
$LN13@Eos:
  00055	0f b6 44 24 04	 movzx	 eax, BYTE PTR tv87[rsp]
  0005a	88 04 24	 mov	 BYTE PTR $T1[rsp], al

; 436  :         if (_Large_mode_engaged()) {

  0005d	0f b6 04 24	 movzx	 eax, BYTE PTR $T1[rsp]
  00061	0f b6 c0	 movzx	 eax, al
  00064	85 c0		 test	 eax, eax
  00066	74 21		 je	 SHORT $LN5@Eos

; 437  :             _Result = _Unfancy(_Bx._Ptr);

  00068	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0006d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00070	48 89 44 24 18	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  00075	48 8b 44 24 18	 mov	 rax, QWORD PTR _Ptr$[rsp]
  0007a	48 89 44 24 20	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 437  :             _Result = _Unfancy(_Bx._Ptr);

  0007f	48 8b 44 24 20	 mov	 rax, QWORD PTR $T4[rsp]
  00084	48 89 44 24 10	 mov	 QWORD PTR _Result$3[rsp], rax
$LN5@Eos:

; 438  :         }
; 439  : 
; 440  :         return _Result;

  00089	48 8b 44 24 10	 mov	 rax, QWORD PTR _Result$3[rsp]
  0008e	48 89 44 24 28	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 347  :         _Left = _Right;

  00093	48 8b 44 24 28	 mov	 rax, QWORD PTR $T5[rsp]
  00098	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _New_size$[rsp]
  0009d	0f b7 54 24 02	 movzx	 edx, WORD PTR $T2[rsp]
  000a2	66 89 14 48	 mov	 WORD PTR [rax+rcx*2], dx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3078 :     }

  000a6	48 83 c4 38	 add	 rsp, 56			; 00000038H
  000aa	c3		 ret	 0
?_Eos@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAX_K@Z ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Eos
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Calculate_growth@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@CA_K_K00@Z
_TEXT	SEGMENT
_Masked$ = 0
$T1 = 8
tv75 = 16
$T2 = 24
$T3 = 32
_Requested$ = 64
_Old$ = 72
_Max$ = 80
?_Calculate_growth@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@CA_K_K00@Z PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Calculate_growth, COMDAT

; 2977 :         const size_type _Requested, const size_type _Old, const size_type _Max) noexcept {

$LN13:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 2978 :         const size_type _Masked = _Requested | _Alloc_mask;

  00013	48 8b 44 24 40	 mov	 rax, QWORD PTR _Requested$[rsp]
  00018	48 83 c8 07	 or	 rax, 7
  0001c	48 89 04 24	 mov	 QWORD PTR _Masked$[rsp], rax

; 2979 :         if (_Masked > _Max) { // the mask overflows, settle for max_size()

  00020	48 8b 44 24 50	 mov	 rax, QWORD PTR _Max$[rsp]
  00025	48 39 04 24	 cmp	 QWORD PTR _Masked$[rsp], rax
  00029	76 0a		 jbe	 SHORT $LN2@Calculate_

; 2980 :             return _Max;

  0002b	48 8b 44 24 50	 mov	 rax, QWORD PTR _Max$[rsp]
  00030	e9 83 00 00 00	 jmp	 $LN1@Calculate_
$LN2@Calculate_:

; 2981 :         }
; 2982 : 
; 2983 :         if (_Old > _Max - _Old / 2) { // similarly, geometric overflows

  00035	33 d2		 xor	 edx, edx
  00037	48 8b 44 24 48	 mov	 rax, QWORD PTR _Old$[rsp]
  0003c	b9 02 00 00 00	 mov	 ecx, 2
  00041	48 f7 f1	 div	 rcx
  00044	48 8b 4c 24 50	 mov	 rcx, QWORD PTR _Max$[rsp]
  00049	48 2b c8	 sub	 rcx, rax
  0004c	48 8b c1	 mov	 rax, rcx
  0004f	48 39 44 24 48	 cmp	 QWORD PTR _Old$[rsp], rax
  00054	76 07		 jbe	 SHORT $LN3@Calculate_

; 2984 :             return _Max;

  00056	48 8b 44 24 50	 mov	 rax, QWORD PTR _Max$[rsp]
  0005b	eb 5b		 jmp	 SHORT $LN1@Calculate_
$LN3@Calculate_:

; 2987 :         return (_STD max)(_Masked, _Old + _Old / 2);

  0005d	33 d2		 xor	 edx, edx
  0005f	48 8b 44 24 48	 mov	 rax, QWORD PTR _Old$[rsp]
  00064	b9 02 00 00 00	 mov	 ecx, 2
  00069	48 f7 f1	 div	 rcx
  0006c	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _Old$[rsp]
  00071	48 03 c8	 add	 rcx, rax
  00074	48 8b c1	 mov	 rax, rcx
  00077	48 89 44 24 08	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 77   :     return _Left < _Right ? _Right : _Left;

  0007c	48 8b 44 24 08	 mov	 rax, QWORD PTR $T1[rsp]
  00081	48 39 04 24	 cmp	 QWORD PTR _Masked$[rsp], rax
  00085	73 0c		 jae	 SHORT $LN8@Calculate_
  00087	48 8d 44 24 08	 lea	 rax, QWORD PTR $T1[rsp]
  0008c	48 89 44 24 10	 mov	 QWORD PTR tv75[rsp], rax
  00091	eb 09		 jmp	 SHORT $LN9@Calculate_
$LN8@Calculate_:
  00093	48 8d 04 24	 lea	 rax, QWORD PTR _Masked$[rsp]
  00097	48 89 44 24 10	 mov	 QWORD PTR tv75[rsp], rax
$LN9@Calculate_:
  0009c	48 8b 44 24 10	 mov	 rax, QWORD PTR tv75[rsp]
  000a1	48 89 44 24 18	 mov	 QWORD PTR $T2[rsp], rax
  000a6	48 8b 44 24 18	 mov	 rax, QWORD PTR $T2[rsp]
  000ab	48 89 44 24 20	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2987 :         return (_STD max)(_Masked, _Old + _Old / 2);

  000b0	48 8b 44 24 20	 mov	 rax, QWORD PTR $T3[rsp]
  000b5	48 8b 00	 mov	 rax, QWORD PTR [rax]
$LN1@Calculate_:

; 2988 :     }

  000b8	48 83 c4 38	 add	 rsp, 56			; 00000038H
  000bc	c3		 ret	 0
?_Calculate_growth@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@CA_K_K00@Z ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Calculate_growth
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ
_TEXT	SEGMENT
$T1 = 0
_Alloc_max$ = 8
tv66 = 16
$T2 = 24
$T3 = 32
tv70 = 40
$T4 = 48
$T5 = 56
$T6 = 64
$T7 = 72
_Storage_max$ = 80
$T8 = 88
$T9 = 96
$T10 = 104
$T11 = 112
_Unsigned_max$12 = 120
this$ = 144
?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size, COMDAT

; 2377 :     _NODISCARD _CONSTEXPR20 size_type max_size() const noexcept {

$LN38:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H

; 3111 :         return _Mypair._Get_first();

  0000c	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1543 :         return *this;

  00014	48 89 44 24 30	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3111 :         return _Mypair._Get_first();

  00019	48 8b 44 24 30	 mov	 rax, QWORD PTR $T4[rsp]
  0001e	48 89 44 24 70	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 746  :         return static_cast<size_t>(-1) / sizeof(value_type);

  00023	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  0002d	48 89 44 24 38	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2378 :         const size_type _Alloc_max   = _Alty_traits::max_size(_Getal());

  00032	48 8b 44 24 38	 mov	 rax, QWORD PTR $T5[rsp]
  00037	48 89 44 24 08	 mov	 QWORD PTR _Alloc_max$[rsp], rax

; 2379 :         const size_type _Storage_max = // can always store small string

  0003c	48 c7 04 24 08
	00 00 00	 mov	 QWORD PTR $T1[rsp], 8
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 77   :     return _Left < _Right ? _Right : _Left;

  00044	48 8b 04 24	 mov	 rax, QWORD PTR $T1[rsp]
  00048	48 39 44 24 08	 cmp	 QWORD PTR _Alloc_max$[rsp], rax
  0004d	73 0b		 jae	 SHORT $LN21@max_size
  0004f	48 8d 04 24	 lea	 rax, QWORD PTR $T1[rsp]
  00053	48 89 44 24 10	 mov	 QWORD PTR tv66[rsp], rax
  00058	eb 0a		 jmp	 SHORT $LN22@max_size
$LN21@max_size:
  0005a	48 8d 44 24 08	 lea	 rax, QWORD PTR _Alloc_max$[rsp]
  0005f	48 89 44 24 10	 mov	 QWORD PTR tv66[rsp], rax
$LN22@max_size:
  00064	48 8b 44 24 10	 mov	 rax, QWORD PTR tv66[rsp]
  00069	48 89 44 24 40	 mov	 QWORD PTR $T6[rsp], rax
  0006e	48 8b 44 24 40	 mov	 rax, QWORD PTR $T6[rsp]
  00073	48 89 44 24 48	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2379 :         const size_type _Storage_max = // can always store small string

  00078	48 8b 44 24 48	 mov	 rax, QWORD PTR $T7[rsp]
  0007d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00080	48 89 44 24 50	 mov	 QWORD PTR _Storage_max$[rsp], rax

; 2381 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()),

  00085	48 8b 44 24 50	 mov	 rax, QWORD PTR _Storage_max$[rsp]
  0008a	48 ff c8	 dec	 rax
  0008d	48 89 44 24 18	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 866  :         constexpr auto _Unsigned_max = static_cast<make_unsigned_t<_Ty>>(-1);

  00092	48 c7 44 24 78
	ff ff ff ff	 mov	 QWORD PTR _Unsigned_max$12[rsp], -1

; 867  :         return static_cast<_Ty>(_Unsigned_max >> 1);

  0009b	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  000a5	48 89 44 24 58	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2381 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()),

  000aa	48 8b 44 24 58	 mov	 rax, QWORD PTR $T8[rsp]
  000af	48 89 44 24 20	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 101  :     return _Right < _Left ? _Right : _Left;

  000b4	48 8b 44 24 20	 mov	 rax, QWORD PTR $T3[rsp]
  000b9	48 39 44 24 18	 cmp	 QWORD PTR $T2[rsp], rax
  000be	73 0c		 jae	 SHORT $LN33@max_size
  000c0	48 8d 44 24 18	 lea	 rax, QWORD PTR $T2[rsp]
  000c5	48 89 44 24 28	 mov	 QWORD PTR tv70[rsp], rax
  000ca	eb 0a		 jmp	 SHORT $LN34@max_size
$LN33@max_size:
  000cc	48 8d 44 24 20	 lea	 rax, QWORD PTR $T3[rsp]
  000d1	48 89 44 24 28	 mov	 QWORD PTR tv70[rsp], rax
$LN34@max_size:
  000d6	48 8b 44 24 28	 mov	 rax, QWORD PTR tv70[rsp]
  000db	48 89 44 24 60	 mov	 QWORD PTR $T9[rsp], rax
  000e0	48 8b 44 24 60	 mov	 rax, QWORD PTR $T9[rsp]
  000e5	48 89 44 24 68	 mov	 QWORD PTR $T10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2381 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()),

  000ea	48 8b 44 24 68	 mov	 rax, QWORD PTR $T10[rsp]
  000ef	48 8b 00	 mov	 rax, QWORD PTR [rax]

; 2382 :             _Storage_max - 1 // -1 is for null terminator and/or npos
; 2383 :         );
; 2384 :     }

  000f2	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  000f9	c3		 ret	 0
?max_size@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEBA_KXZ ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::max_size
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\wchar.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?append@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV12@_K_W@Z
_TEXT	SEGMENT
$T1 = 48
$T2 = 50
_Old_size$ = 56
_N$ = 64
_Su$3 = 72
_First$ = 80
_Old_ptr$4 = 88
$T5 = 96
this$ = 128
_Count$ = 136
_Ch$ = 144
?append@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV12@_K_W@Z PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::append, COMDAT

; 1537 :     _CONSTEXPR20 basic_string& append(_CRT_GUARDOVERFLOW const size_type _Count, const _Elem _Ch) {

$LN250:
  00000	66 44 89 44 24
	18		 mov	 WORD PTR [rsp+24], r8w
  00006	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000b	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00010	57		 push	 rdi
  00011	48 83 ec 70	 sub	 rsp, 112		; 00000070H

; 1538 :         // append _Count * _Ch
; 1539 :         const size_type _Old_size = _Mypair._Myval2._Mysize;

  00015	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00021	48 89 44 24 38	 mov	 QWORD PTR _Old_size$[rsp], rax

; 1540 :         if (_Count <= _Mypair._Myval2._Myres - _Old_size) {

  00026	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0002e	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Old_size$[rsp]
  00033	48 8b 40 18	 mov	 rax, QWORD PTR [rax+24]
  00037	48 2b c1	 sub	 rax, rcx
  0003a	48 39 84 24 88
	00 00 00	 cmp	 QWORD PTR _Count$[rsp], rax
  00042	0f 87 d1 00 00
	00		 ja	 $LN2@append

; 1541 :             _ASAN_STRING_MODIFY(*this, _Old_size, _Old_size + _Count);
; 1542 :             _Mypair._Myval2._Mysize = _Old_size + _Count;

  00048	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  00050	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Old_size$[rsp]
  00055	48 03 c8	 add	 rcx, rax
  00058	48 8b c1	 mov	 rax, rcx
  0005b	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00063	48 89 41 10	 mov	 QWORD PTR [rcx+16], rax

; 1543 :             _Elem* const _Old_ptr   = _Mypair._Myval2._Myptr();

  00067	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0006f	48 8b c8	 mov	 rcx, rax
  00072	e8 00 00 00 00	 call	 ?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEAAPEA_WXZ ; std::_String_val<std::_Simple_types<wchar_t> >::_Myptr
  00077	48 89 44 24 58	 mov	 QWORD PTR _Old_ptr$4[rsp], rax

; 1544 :             _Traits::assign(_Old_ptr + _Old_size, _Count, _Ch);

  0007c	48 8b 44 24 58	 mov	 rax, QWORD PTR _Old_ptr$4[rsp]
  00081	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Old_size$[rsp]
  00086	48 8d 04 48	 lea	 rax, QWORD PTR [rax+rcx*2]
  0008a	48 89 44 24 50	 mov	 QWORD PTR _First$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 338  :         return reinterpret_cast<_Elem*>(_CSTD wmemset(reinterpret_cast<wchar_t*>(_First), _Ch, _Count));

  0008f	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  00097	48 89 44 24 40	 mov	 QWORD PTR _N$[rsp], rax
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\wchar.h

; 510  :         wchar_t *_Su = _S;

  0009c	48 8b 44 24 50	 mov	 rax, QWORD PTR _First$[rsp]
  000a1	48 89 44 24 48	 mov	 QWORD PTR _Su$3[rsp], rax

; 511  :         for (; 0 < _N; ++_Su, --_N)

  000a6	eb 1b		 jmp	 SHORT $LN30@append
$LN28@append:
  000a8	48 8b 44 24 48	 mov	 rax, QWORD PTR _Su$3[rsp]
  000ad	48 83 c0 02	 add	 rax, 2
  000b1	48 89 44 24 48	 mov	 QWORD PTR _Su$3[rsp], rax
  000b6	48 8b 44 24 40	 mov	 rax, QWORD PTR _N$[rsp]
  000bb	48 ff c8	 dec	 rax
  000be	48 89 44 24 40	 mov	 QWORD PTR _N$[rsp], rax
$LN30@append:
  000c3	48 83 7c 24 40
	00		 cmp	 QWORD PTR _N$[rsp], 0
  000c9	76 12		 jbe	 SHORT $LN29@append

; 512  :         {
; 513  :             *_Su = _C;

  000cb	48 8b 44 24 48	 mov	 rax, QWORD PTR _Su$3[rsp]
  000d0	0f b7 8c 24 90
	00 00 00	 movzx	 ecx, WORD PTR _Ch$[rsp]
  000d8	66 89 08	 mov	 WORD PTR [rax], cx

; 514  :         }

  000db	eb cb		 jmp	 SHORT $LN28@append
$LN29@append:

; 515  :         return _S;

  000dd	48 8b 44 24 50	 mov	 rax, QWORD PTR _First$[rsp]
  000e2	48 89 44 24 60	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1545 :             _Traits::assign(_Old_ptr[_Old_size + _Count], _Elem());

  000e7	33 c0		 xor	 eax, eax
  000e9	66 89 44 24 32	 mov	 WORD PTR $T2[rsp], ax
  000ee	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  000f6	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Old_size$[rsp]
  000fb	48 03 c8	 add	 rcx, rax
  000fe	48 8b c1	 mov	 rax, rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 347  :         _Left = _Right;

  00101	48 8b 4c 24 58	 mov	 rcx, QWORD PTR _Old_ptr$4[rsp]
  00106	0f b7 54 24 32	 movzx	 edx, WORD PTR $T2[rsp]
  0010b	66 89 14 41	 mov	 WORD PTR [rcx+rax*2], dx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1546 :             return *this;

  0010f	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00117	eb 41		 jmp	 SHORT $LN1@append
$LN2@append:

; 1547 :         }
; 1548 : 
; 1549 :         return _Reallocate_grow_by(

  00119	48 8d 44 24 30	 lea	 rax, QWORD PTR $T1[rsp]
  0011e	48 8b f8	 mov	 rdi, rax
  00121	33 c0		 xor	 eax, eax
  00123	b9 01 00 00 00	 mov	 ecx, 1
  00128	f3 aa		 rep stosb
  0012a	0f b7 84 24 90
	00 00 00	 movzx	 eax, WORD PTR _Ch$[rsp]
  00132	66 89 44 24 20	 mov	 WORD PTR [rsp+32], ax
  00137	4c 8b 8c 24 88
	00 00 00	 mov	 r9, QWORD PTR _Count$[rsp]
  0013f	44 0f b6 44 24
	30		 movzx	 r8d, BYTE PTR $T1[rsp]
  00145	48 8b 94 24 88
	00 00 00	 mov	 rdx, QWORD PTR _Count$[rsp]
  0014d	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00155	e8 00 00 00 00	 call	 ??$_Reallocate_grow_by@V<lambda_a3050a43f3157934f354774ab3dd2e02>@@_K_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAAEAV01@_KV<lambda_a3050a43f3157934f354774ab3dd2e02>@@_K_W@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Reallocate_grow_by<<lambda_a3050a43f3157934f354774ab3dd2e02>,unsigned __int64,wchar_t>
$LN1@append:

; 1550 :             _Count,
; 1551 :             [](_Elem* const _New_ptr, const _Elem* const _Old_ptr, const size_type _Old_size, const size_type _Count,
; 1552 :                 const _Elem _Ch) _STATIC_LAMBDA {
; 1553 :                 _Traits::copy(_New_ptr, _Old_ptr, _Old_size);
; 1554 :                 _Traits::assign(_New_ptr + _Old_size, _Count, _Ch);
; 1555 :                 _Traits::assign(_New_ptr[_Old_size + _Count], _Elem());
; 1556 :             },
; 1557 :             _Count, _Ch);
; 1558 :     }

  0015a	48 83 c4 70	 add	 rsp, 112		; 00000070H
  0015e	5f		 pop	 rdi
  0015f	c3		 ret	 0
?append@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAAAEAV12@_K_W@Z ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::append
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >, COMDAT

; 1382 :     _CONSTEXPR20 ~basic_string() noexcept {

$LN82:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 1383 :         _Tidy_deallocate();

  00009	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0000e	e8 00 00 00 00	 call	 ?_Tidy_deallocate@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Tidy_deallocate
  00013	90		 npad	 1

; 1384 : #if _ITERATOR_DEBUG_LEVEL != 0
; 1385 :         auto&& _Alproxy          = _GET_PROXY_ALLOCATOR(_Alty, _Getal());
; 1386 :         const auto _To_delete    = _Mypair._Myval2._Myproxy;
; 1387 :         _Mypair._Myval2._Myproxy = nullptr;
; 1388 :         _Delete_plain_internal(_Alproxy, _To_delete);
; 1389 : #endif // _ITERATOR_DEBUG_LEVEL != 0
; 1390 :     }

  00014	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00018	c3		 ret	 0
??1?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::~basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ
_TEXT	SEGMENT
$T1 = 0
$T2 = 2
_My_data$ = 8
this$ = 32
?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_empty, COMDAT

; 855  :     _CONSTEXPR20 void _Construct_empty() {

$LN18:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi
  00006	48 83 ec 10	 sub	 rsp, 16

; 856  :         auto& _My_data = _Mypair._Myval2;

  0000a	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0000f	48 89 44 24 08	 mov	 QWORD PTR _My_data$[rsp], rax

; 857  :         _My_data._Alloc_proxy(_GET_PROXY_ALLOCATOR(_Alty, _Getal()));

  00014	48 8d 04 24	 lea	 rax, QWORD PTR $T1[rsp]
  00018	48 8b f8	 mov	 rdi, rax
  0001b	33 c0		 xor	 eax, eax
  0001d	b9 01 00 00 00	 mov	 ecx, 1
  00022	f3 aa		 rep stosb

; 858  : 
; 859  :         // initialize basic_string data members
; 860  :         _My_data._Mysize = 0;

  00024	48 8b 44 24 08	 mov	 rax, QWORD PTR _My_data$[rsp]
  00029	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 861  :         _My_data._Myres  = _Small_string_capacity;

  00031	48 8b 44 24 08	 mov	 rax, QWORD PTR _My_data$[rsp]
  00036	48 c7 40 18 07
	00 00 00	 mov	 QWORD PTR [rax+24], 7

; 862  :         _My_data._Activate_SSO_buffer();
; 863  : 
; 864  :         // the _Traits::assign is last so the codegen doesn't think the char write can alias this
; 865  :         _Traits::assign(_My_data._Bx._Buf[0], _Elem());

  0003e	33 c0		 xor	 eax, eax
  00040	66 89 44 24 02	 mov	 WORD PTR $T2[rsp], ax
  00045	b8 02 00 00 00	 mov	 eax, 2
  0004a	48 6b c0 00	 imul	 rax, rax, 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 347  :         _Left = _Right;

  0004e	48 8b 4c 24 08	 mov	 rcx, QWORD PTR _My_data$[rsp]
  00053	0f b7 54 24 02	 movzx	 edx, WORD PTR $T2[rsp]
  00058	66 89 14 01	 mov	 WORD PTR [rcx+rax], dx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 866  :     }

  0005c	48 83 c4 10	 add	 rsp, 16
  00060	5f		 pop	 rdi
  00061	c3		 ret	 0
?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_empty
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
$T1 = 32
tv152 = 36
this$ = 40
this$ = 48
this$ = 56
_Result$2 = 64
$T3 = 72
this$ = 80
_Ptr$ = 88
$T4 = 96
$T5 = 104
$T6 = 112
this$ = 144
_Right$ = 152
??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >, COMDAT

; 717  :         : _Mypair(_One_then_variadic_args_t{}, _Alty_traits::select_on_container_copy_construction(_Right._Getal())) {

$LN226:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 81 ec 80 00
	00 00		 sub	 rsp, 128		; 00000080H

; 3111 :         return _Mypair._Get_first();

  00012	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR _Right$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1543 :         return *this;

  0001a	48 89 44 24 48	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3111 :         return _Mypair._Get_first();

  0001f	48 8b 44 24 48	 mov	 rax, QWORD PTR $T3[rsp]
  00024	48 89 44 24 70	 mov	 QWORD PTR $T6[rsp], rax

; 717  :         : _Mypair(_One_then_variadic_args_t{}, _Alty_traits::select_on_container_copy_construction(_Right._Getal())) {

  00029	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00031	48 89 44 24 50	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1536 :         : _Ty1(_STD forward<_Other1>(_Val1)), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  00036	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0003b	48 89 44 24 28	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 402  :     _CONSTEXPR20 _String_val() noexcept : _Bx() {}

  00040	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00045	48 89 44 24 38	 mov	 QWORD PTR this$[rsp], rax

; 493  :         _CONSTEXPR20 _Bxty() noexcept : _Buf() {} // user-provided, for fancy pointers

  0004a	48 8b 44 24 38	 mov	 rax, QWORD PTR this$[rsp]
  0004f	48 8b 7c 24 38	 mov	 rdi, QWORD PTR this$[rsp]
  00054	33 c0		 xor	 eax, eax
  00056	b9 10 00 00 00	 mov	 ecx, 16
  0005b	f3 aa		 rep stosb

; 517  :     size_type _Mysize = 0; // current length of string (size)

  0005d	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00062	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 518  :     size_type _Myres  = 0; // current storage reserved for string (capacity)

  0006a	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  0006f	48 c7 40 18 00
	00 00 00	 mov	 QWORD PTR [rax+24], 0

; 718  :         _Construct<_Construct_strategy::_From_string>(_Right._Mypair._Myval2._Myptr(), _Right._Mypair._Myval2._Mysize);

  00077	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR _Right$[rsp]
  0007f	48 89 44 24 30	 mov	 QWORD PTR this$[rsp], rax

; 444  :         const value_type* _Result = _Bx._Buf;

  00084	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00089	48 89 44 24 40	 mov	 QWORD PTR _Result$2[rsp], rax

; 453  :         return _Myres > _Small_string_capacity;

  0008e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00093	48 83 78 18 07	 cmp	 QWORD PTR [rax+24], 7
  00098	76 0a		 jbe	 SHORT $LN44@basic_stri
  0009a	c7 44 24 24 01
	00 00 00	 mov	 DWORD PTR tv152[rsp], 1
  000a2	eb 08		 jmp	 SHORT $LN45@basic_stri
$LN44@basic_stri:
  000a4	c7 44 24 24 00
	00 00 00	 mov	 DWORD PTR tv152[rsp], 0
$LN45@basic_stri:
  000ac	0f b6 44 24 24	 movzx	 eax, BYTE PTR tv152[rsp]
  000b1	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al

; 445  :         if (_Large_mode_engaged()) {

  000b5	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  000ba	0f b6 c0	 movzx	 eax, al
  000bd	85 c0		 test	 eax, eax
  000bf	74 21		 je	 SHORT $LN37@basic_stri

; 446  :             _Result = _Unfancy(_Bx._Ptr);

  000c1	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  000c6	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000c9	48 89 44 24 58	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  000ce	48 8b 44 24 58	 mov	 rax, QWORD PTR _Ptr$[rsp]
  000d3	48 89 44 24 60	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 446  :             _Result = _Unfancy(_Bx._Ptr);

  000d8	48 8b 44 24 60	 mov	 rax, QWORD PTR $T4[rsp]
  000dd	48 89 44 24 40	 mov	 QWORD PTR _Result$2[rsp], rax
$LN37@basic_stri:

; 447  :         }
; 448  : 
; 449  :         return _Result;

  000e2	48 8b 44 24 40	 mov	 rax, QWORD PTR _Result$2[rsp]
  000e7	48 89 44 24 68	 mov	 QWORD PTR $T5[rsp], rax

; 718  :         _Construct<_Construct_strategy::_From_string>(_Right._Mypair._Myval2._Myptr(), _Right._Mypair._Myval2._Mysize);

  000ec	48 8b 44 24 68	 mov	 rax, QWORD PTR $T5[rsp]
  000f1	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR _Right$[rsp]
  000f9	4c 8b 41 10	 mov	 r8, QWORD PTR [rcx+16]
  000fd	48 8b d0	 mov	 rdx, rax
  00100	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00108	e8 00 00 00 00	 call	 ??$_Construct@$01PEB_W@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXQEB_W_K@Z ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct<2,wchar_t const *>
  0010d	90		 npad	 1

; 719  :     }

  0010e	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00116	48 81 c4 80 00
	00 00		 add	 rsp, 128		; 00000080H
  0011d	5f		 pop	 rdi
  0011e	c3		 ret	 0
??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
tv152 = 36
this$ = 40
this$ = 48
this$ = 56
_Result$2 = 64
$T3 = 72
this$ = 80
_Ptr$ = 88
$T4 = 96
$T5 = 104
$T6 = 112
this$ = 144
_Right$ = 152
?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z@4HA PROC ; `std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 8d 90 00
	00 00		 mov	 rcx, QWORD PTR this$[rbp]
  00010	e8 00 00 00 00	 call	 ??1?$_Compressed_pair@V?$allocator@_W@std@@V?$_String_val@U?$_Simple_types@_W@std@@@2@$00@std@@QEAA@XZ
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$0@?0???0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@AEBV01@@Z@4HA ENDP ; `std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 32
this$ = 40
this$ = 48
this$ = 80
??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ PROC ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >, COMDAT

; 708  :     basic_string() noexcept(is_nothrow_default_constructible_v<_Alty>) : _Mypair(_Zero_then_variadic_args_t{}) {

$LN41:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi
  00006	48 83 ec 40	 sub	 rsp, 64			; 00000040H
  0000a	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0000f	48 89 44 24 30	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1531 :         : _Ty1(), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  00014	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00019	48 89 44 24 20	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 402  :     _CONSTEXPR20 _String_val() noexcept : _Bx() {}

  0001e	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 89 44 24 28	 mov	 QWORD PTR this$[rsp], rax

; 493  :         _CONSTEXPR20 _Bxty() noexcept : _Buf() {} // user-provided, for fancy pointers

  00028	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  0002d	48 8b 7c 24 28	 mov	 rdi, QWORD PTR this$[rsp]
  00032	33 c0		 xor	 eax, eax
  00034	b9 10 00 00 00	 mov	 ecx, 16
  00039	f3 aa		 rep stosb

; 517  :     size_type _Mysize = 0; // current length of string (size)

  0003b	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00040	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 518  :     size_type _Myres  = 0; // current storage reserved for string (capacity)

  00048	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0004d	48 c7 40 18 00
	00 00 00	 mov	 QWORD PTR [rax+24], 0

; 709  :         _Construct_empty();

  00055	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  0005a	e8 00 00 00 00	 call	 ?_Construct_empty@?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@AEAAXXZ ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::_Construct_empty
  0005f	90		 npad	 1

; 710  :     }

  00060	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00065	48 83 c4 40	 add	 rsp, 64			; 00000040H
  00069	5f		 pop	 rdi
  0006a	c3		 ret	 0
??0?$basic_string@_WU?$char_traits@_W@std@@V?$allocator@_W@2@@std@@QEAA@XZ ENDP ; std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEAAPEA_WXZ
_TEXT	SEGMENT
$T1 = 0
tv76 = 4
_Result$ = 8
_Ptr$ = 16
$T2 = 24
this$ = 48
?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEAAPEA_WXZ PROC ; std::_String_val<std::_Simple_types<wchar_t> >::_Myptr, COMDAT

; 434  :     _NODISCARD _CONSTEXPR20 value_type* _Myptr() noexcept {

$LN17:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 435  :         value_type* _Result = _Bx._Buf;

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 89 44 24 08	 mov	 QWORD PTR _Result$[rsp], rax

; 453  :         return _Myres > _Small_string_capacity;

  00013	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00018	48 83 78 18 07	 cmp	 QWORD PTR [rax+24], 7
  0001d	76 0a		 jbe	 SHORT $LN7@Myptr
  0001f	c7 44 24 04 01
	00 00 00	 mov	 DWORD PTR tv76[rsp], 1
  00027	eb 08		 jmp	 SHORT $LN8@Myptr
$LN7@Myptr:
  00029	c7 44 24 04 00
	00 00 00	 mov	 DWORD PTR tv76[rsp], 0
$LN8@Myptr:
  00031	0f b6 44 24 04	 movzx	 eax, BYTE PTR tv76[rsp]
  00036	88 04 24	 mov	 BYTE PTR $T1[rsp], al

; 436  :         if (_Large_mode_engaged()) {

  00039	0f b6 04 24	 movzx	 eax, BYTE PTR $T1[rsp]
  0003d	0f b6 c0	 movzx	 eax, al
  00040	85 c0		 test	 eax, eax
  00042	74 21		 je	 SHORT $LN2@Myptr

; 437  :             _Result = _Unfancy(_Bx._Ptr);

  00044	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00049	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0004c	48 89 44 24 10	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  00051	48 8b 44 24 10	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00056	48 89 44 24 18	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 437  :             _Result = _Unfancy(_Bx._Ptr);

  0005b	48 8b 44 24 18	 mov	 rax, QWORD PTR $T2[rsp]
  00060	48 89 44 24 08	 mov	 QWORD PTR _Result$[rsp], rax
$LN2@Myptr:

; 438  :         }
; 439  : 
; 440  :         return _Result;

  00065	48 8b 44 24 08	 mov	 rax, QWORD PTR _Result$[rsp]

; 441  :     }

  0006a	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0006e	c3		 ret	 0
?_Myptr@?$_String_val@U?$_Simple_types@_W@std@@@std@@QEAAPEA_WXZ ENDP ; std::_String_val<std::_Simple_types<wchar_t> >::_Myptr
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z
_TEXT	SEGMENT
_Overflow_is_possible$1 = 32
_Bytes$ = 40
$T2 = 48
$T3 = 56
$T4 = 64
_Max_possible$5 = 72
this$ = 96
_Count$ = 104
?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z PROC	; std::allocator<wchar_t>::allocate, COMDAT

; 988  :     _NODISCARD_RAW_PTR_ALLOC _CONSTEXPR20 __declspec(allocator) _Ty* allocate(_CRT_GUARDOVERFLOW const size_t _Count) {

$LN13:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 113  :     constexpr bool _Overflow_is_possible = _Ty_size > 1;

  0000e	c6 44 24 20 01	 mov	 BYTE PTR _Overflow_is_possible$1[rsp], 1

; 114  : 
; 115  :     if constexpr (_Overflow_is_possible) {
; 116  :         constexpr size_t _Max_possible = static_cast<size_t>(-1) / _Ty_size;

  00013	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  0001d	48 89 44 24 48	 mov	 QWORD PTR _Max_possible$5[rsp], rax

; 117  :         if (_Count > _Max_possible) {

  00022	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  0002c	48 39 44 24 68	 cmp	 QWORD PTR _Count$[rsp], rax
  00031	76 06		 jbe	 SHORT $LN4@allocate

; 118  :             _Throw_bad_array_new_length(); // multiply overflow

  00033	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00038	90		 npad	 1
$LN4@allocate:

; 119  :         }
; 120  :     }
; 121  : 
; 122  :     return _Count * _Ty_size;

  00039	48 8b 44 24 68	 mov	 rax, QWORD PTR _Count$[rsp]
  0003e	48 d1 e0	 shl	 rax, 1
  00041	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00046	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
  0004b	48 89 44 24 28	 mov	 QWORD PTR _Bytes$[rsp], rax

; 227  :     if (_Bytes == 0) {

  00050	48 83 7c 24 28
	00		 cmp	 QWORD PTR _Bytes$[rsp], 0
  00056	75 0b		 jne	 SHORT $LN8@allocate

; 228  :         return nullptr;

  00058	48 c7 44 24 30
	00 00 00 00	 mov	 QWORD PTR $T2[rsp], 0
  00061	eb 35		 jmp	 SHORT $LN7@allocate
$LN8@allocate:

; 229  :     }
; 230  : 
; 231  : #if _HAS_CXX20 // TRANSITION, GH-1532
; 232  :     if (_STD is_constant_evaluated()) {
; 233  :         return _Traits::_Allocate(_Bytes);
; 234  :     }
; 235  : #endif // _HAS_CXX20
; 236  : 
; 237  : #ifdef __cpp_aligned_new
; 238  :     if constexpr (_Align > __STDCPP_DEFAULT_NEW_ALIGNMENT__) {
; 239  :         size_t _Passed_align = _Align;
; 240  : #if defined(_M_IX86) || defined(_M_X64)
; 241  :         if (_Bytes >= _Big_allocation_threshold) {
; 242  :             // boost the alignment of big allocations to help autovectorization
; 243  :             _Passed_align = (_STD max)(_Align, _Big_allocation_alignment);
; 244  :         }
; 245  : #endif // defined(_M_IX86) || defined(_M_X64)
; 246  :         return _Traits::_Allocate_aligned(_Bytes, _Passed_align);
; 247  :     } else
; 248  : #endif // defined(__cpp_aligned_new)
; 249  :     {
; 250  : #if defined(_M_IX86) || defined(_M_X64)
; 251  :         if (_Bytes >= _Big_allocation_threshold) {

  00063	48 81 7c 24 28
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  0006c	72 11		 jb	 SHORT $LN9@allocate

; 252  :             // boost the alignment of big allocations to help autovectorization
; 253  :             return _Allocate_manually_vector_aligned<_Traits>(_Bytes);

  0006e	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00073	e8 00 00 00 00	 call	 ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
  00078	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
  0007d	eb 19		 jmp	 SHORT $LN7@allocate
$LN9@allocate:

; 136  :         return ::operator new(_Bytes);

  0007f	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00084	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00089	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax

; 256  :         return _Traits::_Allocate(_Bytes);

  0008e	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  00093	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
$LN7@allocate:

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00098	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
$LN6@allocate:

; 991  :     }

  0009d	48 83 c4 58	 add	 rsp, 88			; 00000058H
  000a1	c3		 ret	 0
?allocate@?$allocator@_W@std@@QEAAPEA_W_K@Z ENDP	; std::allocator<wchar_t>::allocate
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??1?$_Compressed_pair@V?$allocator@D@std@@V?$_String_val@U?$_Simple_types@D@std@@@2@$00@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 8
??1?$_Compressed_pair@V?$allocator@D@std@@V?$_String_val@U?$_Simple_types@D@std@@@2@$00@std@@QEAA@XZ PROC ; std::_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1>::~_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1>, COMDAT
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
??1?$_Compressed_pair@V?$allocator@D@std@@V?$_String_val@U?$_Simple_types@D@std@@@2@$00@std@@QEAA@XZ ENDP ; std::_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1>::~_Compressed_pair<std::allocator<char>,std::_String_val<std::_Simple_types<char> >,1>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ
_TEXT	SEGMENT
$T1 = 32
$T2 = 33
_My_data$ = 40
tv94 = 48
_Bytes$ = 56
_Ptr$ = 64
$T3 = 72
$T4 = 80
_Capacity$ = 88
_Old_ptr$ = 96
_Al$5 = 104
this$ = 128
?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ PROC ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Tidy_deallocate, COMDAT

; 3080 :     _CONSTEXPR20 void _Tidy_deallocate() noexcept { // initialize buffer, deallocating any storage

$LN62:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 78	 sub	 rsp, 120		; 00000078H

; 3081 :         auto& _My_data = _Mypair._Myval2;

  00009	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00011	48 89 44 24 28	 mov	 QWORD PTR _My_data$[rsp], rax

; 453  :         return _Myres > _Small_string_capacity;

  00016	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  0001b	48 83 78 18 0f	 cmp	 QWORD PTR [rax+24], 15
  00020	76 0a		 jbe	 SHORT $LN12@Tidy_deall
  00022	c7 44 24 30 01
	00 00 00	 mov	 DWORD PTR tv94[rsp], 1
  0002a	eb 08		 jmp	 SHORT $LN13@Tidy_deall
$LN12@Tidy_deall:
  0002c	c7 44 24 30 00
	00 00 00	 mov	 DWORD PTR tv94[rsp], 0
$LN13@Tidy_deall:
  00034	0f b6 44 24 30	 movzx	 eax, BYTE PTR tv94[rsp]
  00039	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al

; 3082 :         _My_data._Orphan_all();
; 3083 :         if (_My_data._Large_mode_engaged()) {

  0003d	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  00042	0f b6 c0	 movzx	 eax, al
  00045	85 c0		 test	 eax, eax
  00047	74 7e		 je	 SHORT $LN2@Tidy_deall

; 3107 :         return _Mypair._Get_first();

  00049	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00051	48 89 44 24 48	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3107 :         return _Mypair._Get_first();

  00056	48 8b 44 24 48	 mov	 rax, QWORD PTR $T3[rsp]
  0005b	48 89 44 24 50	 mov	 QWORD PTR $T4[rsp], rax

; 3084 :             _ASAN_STRING_REMOVE(*this);
; 3085 :             auto& _Al = _Getal();

  00060	48 8b 44 24 50	 mov	 rax, QWORD PTR $T4[rsp]
  00065	48 89 44 24 68	 mov	 QWORD PTR _Al$5[rsp], rax

; 3086 :             _Deallocate_for_capacity(_Al, _My_data._Bx._Ptr, _My_data._Myres);

  0006a	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  0006f	48 8b 40 18	 mov	 rax, QWORD PTR [rax+24]
  00073	48 89 44 24 58	 mov	 QWORD PTR _Capacity$[rsp], rax
  00078	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  0007d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00080	48 89 44 24 60	 mov	 QWORD PTR _Old_ptr$[rsp], rax

; 852  :         _Al.deallocate(_Old_ptr, _Capacity + 1); // +1 for null terminator

  00085	48 8b 44 24 58	 mov	 rax, QWORD PTR _Capacity$[rsp]
  0008a	48 ff c0	 inc	 rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  0008d	48 89 44 24 38	 mov	 QWORD PTR _Bytes$[rsp], rax
  00092	48 8b 44 24 60	 mov	 rax, QWORD PTR _Old_ptr$[rsp]
  00097	48 89 44 24 40	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  0009c	48 81 7c 24 38
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  000a5	72 10		 jb	 SHORT $LN38@Tidy_deall

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  000a7	48 8d 54 24 38	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  000ac	48 8d 4c 24 40	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  000b1	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  000b6	90		 npad	 1
$LN38@Tidy_deall:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  000b7	48 8b 54 24 38	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  000bc	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  000c1	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  000c6	90		 npad	 1
$LN2@Tidy_deall:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3090 :         _My_data._Mysize = 0;

  000c7	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000cc	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 3091 :         _My_data._Myres  = _Small_string_capacity;

  000d4	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000d9	48 c7 40 18 0f
	00 00 00	 mov	 QWORD PTR [rax+24], 15

; 3092 :         // the _Traits::assign is last so the codegen doesn't think the char write can alias this
; 3093 :         _Traits::assign(_My_data._Bx._Buf[0], _Elem());

  000e1	c6 44 24 21 00	 mov	 BYTE PTR $T2[rsp], 0
  000e6	b8 01 00 00 00	 mov	 eax, 1
  000eb	48 6b c0 00	 imul	 rax, rax, 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 502  :         _Left = _Right;

  000ef	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _My_data$[rsp]
  000f4	0f b6 54 24 21	 movzx	 edx, BYTE PTR $T2[rsp]
  000f9	88 14 01	 mov	 BYTE PTR [rcx+rax], dl
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3094 :     }

  000fc	48 83 c4 78	 add	 rsp, 120		; 00000078H
  00100	c3		 ret	 0
?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ ENDP ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Tidy_deallocate
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Eos@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAX_K@Z
_TEXT	SEGMENT
$T1 = 0
$T2 = 1
tv87 = 4
this$ = 8
_Result$3 = 16
_Ptr$ = 24
$T4 = 32
$T5 = 40
this$ = 64
_New_size$ = 72
?_Eos@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAX_K@Z PROC ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Eos, COMDAT

; 3074 :     _CONSTEXPR20 void _Eos(const size_type _New_size) noexcept { // set new length and null terminator

$LN27:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 3075 :         _ASAN_STRING_MODIFY(*this, _Mypair._Myval2._Mysize, _New_size);
; 3076 :         _Mypair._Myval2._Mysize = _New_size;

  0000e	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00013	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _New_size$[rsp]
  00018	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 3077 :         _Traits::assign(_Mypair._Myval2._Myptr()[_New_size], _Elem());

  0001c	c6 44 24 01 00	 mov	 BYTE PTR $T2[rsp], 0
  00021	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00026	48 89 44 24 08	 mov	 QWORD PTR this$[rsp], rax

; 435  :         value_type* _Result = _Bx._Buf;

  0002b	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00030	48 89 44 24 10	 mov	 QWORD PTR _Result$3[rsp], rax

; 453  :         return _Myres > _Small_string_capacity;

  00035	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0003a	48 83 78 18 0f	 cmp	 QWORD PTR [rax+24], 15
  0003f	76 0a		 jbe	 SHORT $LN12@Eos
  00041	c7 44 24 04 01
	00 00 00	 mov	 DWORD PTR tv87[rsp], 1
  00049	eb 08		 jmp	 SHORT $LN13@Eos
$LN12@Eos:
  0004b	c7 44 24 04 00
	00 00 00	 mov	 DWORD PTR tv87[rsp], 0
$LN13@Eos:
  00053	0f b6 44 24 04	 movzx	 eax, BYTE PTR tv87[rsp]
  00058	88 04 24	 mov	 BYTE PTR $T1[rsp], al

; 436  :         if (_Large_mode_engaged()) {

  0005b	0f b6 04 24	 movzx	 eax, BYTE PTR $T1[rsp]
  0005f	0f b6 c0	 movzx	 eax, al
  00062	85 c0		 test	 eax, eax
  00064	74 21		 je	 SHORT $LN5@Eos

; 437  :             _Result = _Unfancy(_Bx._Ptr);

  00066	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0006b	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0006e	48 89 44 24 18	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  00073	48 8b 44 24 18	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00078	48 89 44 24 20	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 437  :             _Result = _Unfancy(_Bx._Ptr);

  0007d	48 8b 44 24 20	 mov	 rax, QWORD PTR $T4[rsp]
  00082	48 89 44 24 10	 mov	 QWORD PTR _Result$3[rsp], rax
$LN5@Eos:

; 438  :         }
; 439  : 
; 440  :         return _Result;

  00087	48 8b 44 24 10	 mov	 rax, QWORD PTR _Result$3[rsp]
  0008c	48 89 44 24 28	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 502  :         _Left = _Right;

  00091	48 8b 44 24 48	 mov	 rax, QWORD PTR _New_size$[rsp]
  00096	48 8b 4c 24 28	 mov	 rcx, QWORD PTR $T5[rsp]
  0009b	48 03 c8	 add	 rcx, rax
  0009e	48 8b c1	 mov	 rax, rcx
  000a1	0f b6 4c 24 01	 movzx	 ecx, BYTE PTR $T2[rsp]
  000a6	88 08		 mov	 BYTE PTR [rax], cl
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3078 :     }

  000a8	48 83 c4 38	 add	 rsp, 56			; 00000038H
  000ac	c3		 ret	 0
?_Eos@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAX_K@Z ENDP ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Eos
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Calculate_growth@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@CA_K_K00@Z
_TEXT	SEGMENT
_Masked$ = 0
$T1 = 8
tv75 = 16
$T2 = 24
$T3 = 32
_Requested$ = 64
_Old$ = 72
_Max$ = 80
?_Calculate_growth@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@CA_K_K00@Z PROC ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Calculate_growth, COMDAT

; 2977 :         const size_type _Requested, const size_type _Old, const size_type _Max) noexcept {

$LN13:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 2978 :         const size_type _Masked = _Requested | _Alloc_mask;

  00013	48 8b 44 24 40	 mov	 rax, QWORD PTR _Requested$[rsp]
  00018	48 83 c8 0f	 or	 rax, 15
  0001c	48 89 04 24	 mov	 QWORD PTR _Masked$[rsp], rax

; 2979 :         if (_Masked > _Max) { // the mask overflows, settle for max_size()

  00020	48 8b 44 24 50	 mov	 rax, QWORD PTR _Max$[rsp]
  00025	48 39 04 24	 cmp	 QWORD PTR _Masked$[rsp], rax
  00029	76 0a		 jbe	 SHORT $LN2@Calculate_

; 2980 :             return _Max;

  0002b	48 8b 44 24 50	 mov	 rax, QWORD PTR _Max$[rsp]
  00030	e9 83 00 00 00	 jmp	 $LN1@Calculate_
$LN2@Calculate_:

; 2981 :         }
; 2982 : 
; 2983 :         if (_Old > _Max - _Old / 2) { // similarly, geometric overflows

  00035	33 d2		 xor	 edx, edx
  00037	48 8b 44 24 48	 mov	 rax, QWORD PTR _Old$[rsp]
  0003c	b9 02 00 00 00	 mov	 ecx, 2
  00041	48 f7 f1	 div	 rcx
  00044	48 8b 4c 24 50	 mov	 rcx, QWORD PTR _Max$[rsp]
  00049	48 2b c8	 sub	 rcx, rax
  0004c	48 8b c1	 mov	 rax, rcx
  0004f	48 39 44 24 48	 cmp	 QWORD PTR _Old$[rsp], rax
  00054	76 07		 jbe	 SHORT $LN3@Calculate_

; 2984 :             return _Max;

  00056	48 8b 44 24 50	 mov	 rax, QWORD PTR _Max$[rsp]
  0005b	eb 5b		 jmp	 SHORT $LN1@Calculate_
$LN3@Calculate_:

; 2987 :         return (_STD max)(_Masked, _Old + _Old / 2);

  0005d	33 d2		 xor	 edx, edx
  0005f	48 8b 44 24 48	 mov	 rax, QWORD PTR _Old$[rsp]
  00064	b9 02 00 00 00	 mov	 ecx, 2
  00069	48 f7 f1	 div	 rcx
  0006c	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _Old$[rsp]
  00071	48 03 c8	 add	 rcx, rax
  00074	48 8b c1	 mov	 rax, rcx
  00077	48 89 44 24 08	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 77   :     return _Left < _Right ? _Right : _Left;

  0007c	48 8b 44 24 08	 mov	 rax, QWORD PTR $T1[rsp]
  00081	48 39 04 24	 cmp	 QWORD PTR _Masked$[rsp], rax
  00085	73 0c		 jae	 SHORT $LN8@Calculate_
  00087	48 8d 44 24 08	 lea	 rax, QWORD PTR $T1[rsp]
  0008c	48 89 44 24 10	 mov	 QWORD PTR tv75[rsp], rax
  00091	eb 09		 jmp	 SHORT $LN9@Calculate_
$LN8@Calculate_:
  00093	48 8d 04 24	 lea	 rax, QWORD PTR _Masked$[rsp]
  00097	48 89 44 24 10	 mov	 QWORD PTR tv75[rsp], rax
$LN9@Calculate_:
  0009c	48 8b 44 24 10	 mov	 rax, QWORD PTR tv75[rsp]
  000a1	48 89 44 24 18	 mov	 QWORD PTR $T2[rsp], rax
  000a6	48 8b 44 24 18	 mov	 rax, QWORD PTR $T2[rsp]
  000ab	48 89 44 24 20	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2987 :         return (_STD max)(_Masked, _Old + _Old / 2);

  000b0	48 8b 44 24 20	 mov	 rax, QWORD PTR $T3[rsp]
  000b5	48 8b 00	 mov	 rax, QWORD PTR [rax]
$LN1@Calculate_:

; 2988 :     }

  000b8	48 83 c4 38	 add	 rsp, 56			; 00000038H
  000bc	c3		 ret	 0
?_Calculate_growth@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@CA_K_K00@Z ENDP ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Calculate_growth
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?compare@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBAHQEBD@Z
_TEXT	SEGMENT
$T1 = 32
$T2 = 36
tv91 = 40
_Ans$3 = 44
_Right_size$ = 48
_Left_size$ = 56
$T4 = 64
this$ = 72
_Result$5 = 80
tv133 = 88
_Ptr$ = 96
$T6 = 104
$T7 = 112
$T8 = 120
$T9 = 128
$T10 = 136
_Count$ = 144
this$ = 176
_Ptr$ = 184
?compare@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBAHQEBD@Z PROC ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::compare, COMDAT

; 2910 :     _NODISCARD _CONSTEXPR20 int compare(_In_z_ const _Elem* const _Ptr) const noexcept /* strengthened */ {

$LN47:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 81 ec a8 00
	00 00		 sub	 rsp, 168		; 000000a8H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 459  :         return _CSTD strlen(reinterpret_cast<const char*>(_First));

  00011	48 8b 8c 24 b8
	00 00 00	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  00019	e8 00 00 00 00	 call	 strlen
  0001e	48 89 44 24 70	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2912 :         return _Traits_compare<_Traits>(_Mypair._Myval2._Myptr(), _Mypair._Myval2._Mysize, _Ptr, _Traits::length(_Ptr));

  00023	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0002b	48 89 44 24 48	 mov	 QWORD PTR this$[rsp], rax

; 444  :         const value_type* _Result = _Bx._Buf;

  00030	48 8b 44 24 48	 mov	 rax, QWORD PTR this$[rsp]
  00035	48 89 44 24 50	 mov	 QWORD PTR _Result$5[rsp], rax

; 453  :         return _Myres > _Small_string_capacity;

  0003a	48 8b 44 24 48	 mov	 rax, QWORD PTR this$[rsp]
  0003f	48 83 78 18 0f	 cmp	 QWORD PTR [rax+24], 15
  00044	76 0a		 jbe	 SHORT $LN17@compare
  00046	c7 44 24 28 01
	00 00 00	 mov	 DWORD PTR tv91[rsp], 1
  0004e	eb 08		 jmp	 SHORT $LN18@compare
$LN17@compare:
  00050	c7 44 24 28 00
	00 00 00	 mov	 DWORD PTR tv91[rsp], 0
$LN18@compare:
  00058	0f b6 44 24 28	 movzx	 eax, BYTE PTR tv91[rsp]
  0005d	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al

; 445  :         if (_Large_mode_engaged()) {

  00061	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  00066	0f b6 c0	 movzx	 eax, al
  00069	85 c0		 test	 eax, eax
  0006b	74 21		 je	 SHORT $LN10@compare

; 446  :             _Result = _Unfancy(_Bx._Ptr);

  0006d	48 8b 44 24 48	 mov	 rax, QWORD PTR this$[rsp]
  00072	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00075	48 89 44 24 60	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  0007a	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  0007f	48 89 44 24 68	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 446  :             _Result = _Unfancy(_Bx._Ptr);

  00084	48 8b 44 24 68	 mov	 rax, QWORD PTR $T6[rsp]
  00089	48 89 44 24 50	 mov	 QWORD PTR _Result$5[rsp], rax
$LN10@compare:

; 447  :         }
; 448  : 
; 449  :         return _Result;

  0008e	48 8b 44 24 50	 mov	 rax, QWORD PTR _Result$5[rsp]
  00093	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax

; 2912 :         return _Traits_compare<_Traits>(_Mypair._Myval2._Myptr(), _Mypair._Myval2._Mysize, _Ptr, _Traits::length(_Ptr));

  0009b	48 8b 44 24 70	 mov	 rax, QWORD PTR $T7[rsp]
  000a0	48 89 44 24 30	 mov	 QWORD PTR _Right_size$[rsp], rax
  000a5	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000ad	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  000b1	48 89 44 24 38	 mov	 QWORD PTR _Left_size$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 101  :     return _Right < _Left ? _Right : _Left;

  000b6	48 8b 44 24 38	 mov	 rax, QWORD PTR _Left_size$[rsp]
  000bb	48 39 44 24 30	 cmp	 QWORD PTR _Right_size$[rsp], rax
  000c0	73 0c		 jae	 SHORT $LN37@compare
  000c2	48 8d 44 24 30	 lea	 rax, QWORD PTR _Right_size$[rsp]
  000c7	48 89 44 24 58	 mov	 QWORD PTR tv133[rsp], rax
  000cc	eb 0a		 jmp	 SHORT $LN38@compare
$LN37@compare:
  000ce	48 8d 44 24 38	 lea	 rax, QWORD PTR _Left_size$[rsp]
  000d3	48 89 44 24 58	 mov	 QWORD PTR tv133[rsp], rax
$LN38@compare:
  000d8	48 8b 44 24 58	 mov	 rax, QWORD PTR tv133[rsp]
  000dd	48 89 44 24 78	 mov	 QWORD PTR $T8[rsp], rax
  000e2	48 8b 44 24 78	 mov	 rax, QWORD PTR $T8[rsp]
  000e7	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 619  :     const int _Ans = _Traits::compare(_Left, _Right, (_STD min)(_Left_size, _Right_size));

  000ef	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR $T9[rsp]
  000f7	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000fa	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR _Count$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2912 :         return _Traits_compare<_Traits>(_Mypair._Myval2._Myptr(), _Mypair._Myval2._Mysize, _Ptr, _Traits::length(_Ptr));

  00102	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 439  :         return _CSTD memcmp(_First1, _First2, _Count);

  0010a	4c 8b 84 24 90
	00 00 00	 mov	 r8, QWORD PTR _Count$[rsp]
  00112	48 8b 94 24 b8
	00 00 00	 mov	 rdx, QWORD PTR _Ptr$[rsp]
  0011a	48 8b c8	 mov	 rcx, rax
  0011d	e8 00 00 00 00	 call	 memcmp
  00122	89 44 24 40	 mov	 DWORD PTR $T4[rsp], eax

; 619  :     const int _Ans = _Traits::compare(_Left, _Right, (_STD min)(_Left_size, _Right_size));

  00126	8b 44 24 40	 mov	 eax, DWORD PTR $T4[rsp]
  0012a	89 44 24 2c	 mov	 DWORD PTR _Ans$3[rsp], eax

; 620  : 
; 621  :     if (_Ans != 0) {

  0012e	83 7c 24 2c 00	 cmp	 DWORD PTR _Ans$3[rsp], 0
  00133	74 0a		 je	 SHORT $LN28@compare

; 622  :         return _Ans;

  00135	8b 44 24 2c	 mov	 eax, DWORD PTR _Ans$3[rsp]
  00139	89 44 24 24	 mov	 DWORD PTR $T2[rsp], eax
  0013d	eb 34		 jmp	 SHORT $LN27@compare
$LN28@compare:

; 623  :     }
; 624  : 
; 625  :     if (_Left_size < _Right_size) {

  0013f	48 8b 44 24 30	 mov	 rax, QWORD PTR _Right_size$[rsp]
  00144	48 39 44 24 38	 cmp	 QWORD PTR _Left_size$[rsp], rax
  00149	73 0a		 jae	 SHORT $LN29@compare

; 626  :         return -1;

  0014b	c7 44 24 24 ff
	ff ff ff	 mov	 DWORD PTR $T2[rsp], -1
  00153	eb 1e		 jmp	 SHORT $LN27@compare
$LN29@compare:

; 627  :     }
; 628  : 
; 629  :     if (_Left_size > _Right_size) {

  00155	48 8b 44 24 30	 mov	 rax, QWORD PTR _Right_size$[rsp]
  0015a	48 39 44 24 38	 cmp	 QWORD PTR _Left_size$[rsp], rax
  0015f	76 0a		 jbe	 SHORT $LN30@compare

; 630  :         return 1;

  00161	c7 44 24 24 01
	00 00 00	 mov	 DWORD PTR $T2[rsp], 1
  00169	eb 08		 jmp	 SHORT $LN27@compare
$LN30@compare:

; 631  :     }
; 632  : 
; 633  :     return 0;

  0016b	c7 44 24 24 00
	00 00 00	 mov	 DWORD PTR $T2[rsp], 0
$LN27@compare:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2912 :         return _Traits_compare<_Traits>(_Mypair._Myval2._Myptr(), _Mypair._Myval2._Mysize, _Ptr, _Traits::length(_Ptr));

  00173	8b 44 24 24	 mov	 eax, DWORD PTR $T2[rsp]

; 2913 :     }

  00177	48 81 c4 a8 00
	00 00		 add	 rsp, 168		; 000000a8H
  0017e	c3		 ret	 0
?compare@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBAHQEBD@Z ENDP ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::compare
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?find@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBA_KD_K@Z
_TEXT	SEGMENT
$T1 = 32
tv88 = 36
this$ = 40
_Result$2 = 48
_Ptr$ = 56
$T3 = 64
$T4 = 72
this$ = 96
_Ch$ = 104
_Off$ = 112
?find@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBA_KD_K@Z PROC ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::find, COMDAT

; 2625 :     _NODISCARD _CONSTEXPR20 size_type find(const _Elem _Ch, const size_type _Off = 0) const noexcept {

$LN42:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	88 54 24 10	 mov	 BYTE PTR [rsp+16], dl
  00009	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000e	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 2626 :         // look for _Ch at or after _Off
; 2627 :         return static_cast<size_type>(

  00012	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00017	48 89 44 24 28	 mov	 QWORD PTR this$[rsp], rax

; 444  :         const value_type* _Result = _Bx._Buf;

  0001c	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 89 44 24 30	 mov	 QWORD PTR _Result$2[rsp], rax

; 453  :         return _Myres > _Small_string_capacity;

  00026	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  0002b	48 83 78 18 0f	 cmp	 QWORD PTR [rax+24], 15
  00030	76 0a		 jbe	 SHORT $LN12@find
  00032	c7 44 24 24 01
	00 00 00	 mov	 DWORD PTR tv88[rsp], 1
  0003a	eb 08		 jmp	 SHORT $LN13@find
$LN12@find:
  0003c	c7 44 24 24 00
	00 00 00	 mov	 DWORD PTR tv88[rsp], 0
$LN13@find:
  00044	0f b6 44 24 24	 movzx	 eax, BYTE PTR tv88[rsp]
  00049	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al

; 445  :         if (_Large_mode_engaged()) {

  0004d	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  00052	0f b6 c0	 movzx	 eax, al
  00055	85 c0		 test	 eax, eax
  00057	74 21		 je	 SHORT $LN5@find

; 446  :             _Result = _Unfancy(_Bx._Ptr);

  00059	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  0005e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00061	48 89 44 24 38	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  00066	48 8b 44 24 38	 mov	 rax, QWORD PTR _Ptr$[rsp]
  0006b	48 89 44 24 40	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 446  :             _Result = _Unfancy(_Bx._Ptr);

  00070	48 8b 44 24 40	 mov	 rax, QWORD PTR $T3[rsp]
  00075	48 89 44 24 30	 mov	 QWORD PTR _Result$2[rsp], rax
$LN5@find:

; 447  :         }
; 448  : 
; 449  :         return _Result;

  0007a	48 8b 44 24 30	 mov	 rax, QWORD PTR _Result$2[rsp]
  0007f	48 89 44 24 48	 mov	 QWORD PTR $T4[rsp], rax

; 2626 :         // look for _Ch at or after _Off
; 2627 :         return static_cast<size_type>(

  00084	48 8b 44 24 48	 mov	 rax, QWORD PTR $T4[rsp]
  00089	44 0f b6 4c 24
	68		 movzx	 r9d, BYTE PTR _Ch$[rsp]
  0008f	4c 8b 44 24 70	 mov	 r8, QWORD PTR _Off$[rsp]
  00094	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  00099	48 8b 51 10	 mov	 rdx, QWORD PTR [rcx+16]
  0009d	48 8b c8	 mov	 rcx, rax
  000a0	e8 00 00 00 00	 call	 ??$_Traits_find_ch@U?$char_traits@D@std@@@std@@YA_KQEBD_K1D@Z ; std::_Traits_find_ch<std::char_traits<char> >

; 2628 :             _Traits_find_ch<_Traits>(_Mypair._Myval2._Myptr(), _Mypair._Myval2._Mysize, _Off, _Ch));
; 2629 :     }

  000a5	48 83 c4 58	 add	 rsp, 88			; 00000058H
  000a9	c3		 ret	 0
?find@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBA_KD_K@Z ENDP ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::find
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?max_size@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBA_KXZ
_TEXT	SEGMENT
$T1 = 0
_Alloc_max$ = 8
tv66 = 16
$T2 = 24
$T3 = 32
tv70 = 40
$T4 = 48
$T5 = 56
$T6 = 64
$T7 = 72
_Storage_max$ = 80
$T8 = 88
$T9 = 96
$T10 = 104
$T11 = 112
_Unsigned_max$12 = 120
this$ = 144
?max_size@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBA_KXZ PROC ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::max_size, COMDAT

; 2377 :     _NODISCARD _CONSTEXPR20 size_type max_size() const noexcept {

$LN38:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H

; 3111 :         return _Mypair._Get_first();

  0000c	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1543 :         return *this;

  00014	48 89 44 24 30	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3111 :         return _Mypair._Get_first();

  00019	48 8b 44 24 30	 mov	 rax, QWORD PTR $T4[rsp]
  0001e	48 89 44 24 70	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 746  :         return static_cast<size_t>(-1) / sizeof(value_type);

  00023	48 c7 44 24 38
	ff ff ff ff	 mov	 QWORD PTR $T5[rsp], -1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2378 :         const size_type _Alloc_max   = _Alty_traits::max_size(_Getal());

  0002c	48 8b 44 24 38	 mov	 rax, QWORD PTR $T5[rsp]
  00031	48 89 44 24 08	 mov	 QWORD PTR _Alloc_max$[rsp], rax

; 2379 :         const size_type _Storage_max = // can always store small string

  00036	48 c7 04 24 10
	00 00 00	 mov	 QWORD PTR $T1[rsp], 16
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 77   :     return _Left < _Right ? _Right : _Left;

  0003e	48 8b 04 24	 mov	 rax, QWORD PTR $T1[rsp]
  00042	48 39 44 24 08	 cmp	 QWORD PTR _Alloc_max$[rsp], rax
  00047	73 0b		 jae	 SHORT $LN21@max_size
  00049	48 8d 04 24	 lea	 rax, QWORD PTR $T1[rsp]
  0004d	48 89 44 24 10	 mov	 QWORD PTR tv66[rsp], rax
  00052	eb 0a		 jmp	 SHORT $LN22@max_size
$LN21@max_size:
  00054	48 8d 44 24 08	 lea	 rax, QWORD PTR _Alloc_max$[rsp]
  00059	48 89 44 24 10	 mov	 QWORD PTR tv66[rsp], rax
$LN22@max_size:
  0005e	48 8b 44 24 10	 mov	 rax, QWORD PTR tv66[rsp]
  00063	48 89 44 24 40	 mov	 QWORD PTR $T6[rsp], rax
  00068	48 8b 44 24 40	 mov	 rax, QWORD PTR $T6[rsp]
  0006d	48 89 44 24 48	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2379 :         const size_type _Storage_max = // can always store small string

  00072	48 8b 44 24 48	 mov	 rax, QWORD PTR $T7[rsp]
  00077	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0007a	48 89 44 24 50	 mov	 QWORD PTR _Storage_max$[rsp], rax

; 2381 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()),

  0007f	48 8b 44 24 50	 mov	 rax, QWORD PTR _Storage_max$[rsp]
  00084	48 ff c8	 dec	 rax
  00087	48 89 44 24 18	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 866  :         constexpr auto _Unsigned_max = static_cast<make_unsigned_t<_Ty>>(-1);

  0008c	48 c7 44 24 78
	ff ff ff ff	 mov	 QWORD PTR _Unsigned_max$12[rsp], -1

; 867  :         return static_cast<_Ty>(_Unsigned_max >> 1);

  00095	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  0009f	48 89 44 24 58	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2381 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()),

  000a4	48 8b 44 24 58	 mov	 rax, QWORD PTR $T8[rsp]
  000a9	48 89 44 24 20	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 101  :     return _Right < _Left ? _Right : _Left;

  000ae	48 8b 44 24 20	 mov	 rax, QWORD PTR $T3[rsp]
  000b3	48 39 44 24 18	 cmp	 QWORD PTR $T2[rsp], rax
  000b8	73 0c		 jae	 SHORT $LN33@max_size
  000ba	48 8d 44 24 18	 lea	 rax, QWORD PTR $T2[rsp]
  000bf	48 89 44 24 28	 mov	 QWORD PTR tv70[rsp], rax
  000c4	eb 0a		 jmp	 SHORT $LN34@max_size
$LN33@max_size:
  000c6	48 8d 44 24 20	 lea	 rax, QWORD PTR $T3[rsp]
  000cb	48 89 44 24 28	 mov	 QWORD PTR tv70[rsp], rax
$LN34@max_size:
  000d0	48 8b 44 24 28	 mov	 rax, QWORD PTR tv70[rsp]
  000d5	48 89 44 24 60	 mov	 QWORD PTR $T9[rsp], rax
  000da	48 8b 44 24 60	 mov	 rax, QWORD PTR $T9[rsp]
  000df	48 89 44 24 68	 mov	 QWORD PTR $T10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 2381 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()),

  000e4	48 8b 44 24 68	 mov	 rax, QWORD PTR $T10[rsp]
  000e9	48 8b 00	 mov	 rax, QWORD PTR [rax]

; 2382 :             _Storage_max - 1 // -1 is for null terminator and/or npos
; 2383 :         );
; 2384 :     }

  000ec	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  000f3	c3		 ret	 0
?max_size@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBA_KXZ ENDP ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::max_size
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?c_str@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBAPEBDXZ
_TEXT	SEGMENT
$T1 = 0
tv80 = 4
this$ = 8
_Result$2 = 16
_Ptr$ = 24
$T3 = 32
$T4 = 40
this$ = 64
?c_str@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBAPEBDXZ PROC ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::c_str, COMDAT

; 2355 :     _NODISCARD _CONSTEXPR20 _Ret_z_ const _Elem* c_str() const noexcept {

$LN22:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 2356 :         return _Mypair._Myval2._Myptr();

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 89 44 24 08	 mov	 QWORD PTR this$[rsp], rax

; 444  :         const value_type* _Result = _Bx._Buf;

  00013	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00018	48 89 44 24 10	 mov	 QWORD PTR _Result$2[rsp], rax

; 453  :         return _Myres > _Small_string_capacity;

  0001d	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00022	48 83 78 18 0f	 cmp	 QWORD PTR [rax+24], 15
  00027	76 0a		 jbe	 SHORT $LN12@c_str
  00029	c7 44 24 04 01
	00 00 00	 mov	 DWORD PTR tv80[rsp], 1
  00031	eb 08		 jmp	 SHORT $LN13@c_str
$LN12@c_str:
  00033	c7 44 24 04 00
	00 00 00	 mov	 DWORD PTR tv80[rsp], 0
$LN13@c_str:
  0003b	0f b6 44 24 04	 movzx	 eax, BYTE PTR tv80[rsp]
  00040	88 04 24	 mov	 BYTE PTR $T1[rsp], al

; 445  :         if (_Large_mode_engaged()) {

  00043	0f b6 04 24	 movzx	 eax, BYTE PTR $T1[rsp]
  00047	0f b6 c0	 movzx	 eax, al
  0004a	85 c0		 test	 eax, eax
  0004c	74 21		 je	 SHORT $LN5@c_str

; 446  :             _Result = _Unfancy(_Bx._Ptr);

  0004e	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00056	48 89 44 24 18	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  0005b	48 8b 44 24 18	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00060	48 89 44 24 20	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 446  :             _Result = _Unfancy(_Bx._Ptr);

  00065	48 8b 44 24 20	 mov	 rax, QWORD PTR $T3[rsp]
  0006a	48 89 44 24 10	 mov	 QWORD PTR _Result$2[rsp], rax
$LN5@c_str:

; 447  :         }
; 448  : 
; 449  :         return _Result;

  0006f	48 8b 44 24 10	 mov	 rax, QWORD PTR _Result$2[rsp]
  00074	48 89 44 24 28	 mov	 QWORD PTR $T4[rsp], rax

; 2356 :         return _Mypair._Myval2._Myptr();

  00079	48 8b 44 24 28	 mov	 rax, QWORD PTR $T4[rsp]

; 2357 :     }

  0007e	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00082	c3		 ret	 0
?c_str@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEBAPEBDXZ ENDP ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::c_str
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@_KD@Z
_TEXT	SEGMENT
$T1 = 48
$T2 = 49
_Old_size$ = 56
_Old_ptr$3 = 64
this$ = 96
_Count$ = 104
_Ch$ = 112
?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@_KD@Z PROC ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::append, COMDAT

; 1537 :     _CONSTEXPR20 basic_string& append(_CRT_GUARDOVERFLOW const size_type _Count, const _Elem _Ch) {

$LN233:
  00000	44 88 44 24 18	 mov	 BYTE PTR [rsp+24], r8b
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	57		 push	 rdi
  00010	48 83 ec 50	 sub	 rsp, 80			; 00000050H

; 1538 :         // append _Count * _Ch
; 1539 :         const size_type _Old_size = _Mypair._Myval2._Mysize;

  00014	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00019	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0001d	48 89 44 24 38	 mov	 QWORD PTR _Old_size$[rsp], rax

; 1540 :         if (_Count <= _Mypair._Myval2._Myres - _Old_size) {

  00022	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00027	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Old_size$[rsp]
  0002c	48 8b 40 18	 mov	 rax, QWORD PTR [rax+24]
  00030	48 2b c1	 sub	 rax, rcx
  00033	48 39 44 24 68	 cmp	 QWORD PTR _Count$[rsp], rax
  00038	77 76		 ja	 SHORT $LN2@append

; 1541 :             _ASAN_STRING_MODIFY(*this, _Old_size, _Old_size + _Count);
; 1542 :             _Mypair._Myval2._Mysize = _Old_size + _Count;

  0003a	48 8b 44 24 68	 mov	 rax, QWORD PTR _Count$[rsp]
  0003f	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Old_size$[rsp]
  00044	48 03 c8	 add	 rcx, rax
  00047	48 8b c1	 mov	 rax, rcx
  0004a	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  0004f	48 89 41 10	 mov	 QWORD PTR [rcx+16], rax

; 1543 :             _Elem* const _Old_ptr   = _Mypair._Myval2._Myptr();

  00053	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00058	48 8b c8	 mov	 rcx, rax
  0005b	e8 00 00 00 00	 call	 ?_Myptr@?$_String_val@U?$_Simple_types@D@std@@@std@@QEAAPEADXZ ; std::_String_val<std::_Simple_types<char> >::_Myptr
  00060	48 89 44 24 40	 mov	 QWORD PTR _Old_ptr$3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 493  :         return static_cast<_Elem*>(_CSTD memset(_First, _Ch, _Count));

  00065	0f be 44 24 70	 movsx	 eax, BYTE PTR _Ch$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1544 :             _Traits::assign(_Old_ptr + _Old_size, _Count, _Ch);

  0006a	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Old_size$[rsp]
  0006f	48 8b 54 24 40	 mov	 rdx, QWORD PTR _Old_ptr$3[rsp]
  00074	48 03 d1	 add	 rdx, rcx
  00077	48 8b ca	 mov	 rcx, rdx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 493  :         return static_cast<_Elem*>(_CSTD memset(_First, _Ch, _Count));

  0007a	4c 8b 44 24 68	 mov	 r8, QWORD PTR _Count$[rsp]
  0007f	8b d0		 mov	 edx, eax
  00081	e8 00 00 00 00	 call	 memset
  00086	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1545 :             _Traits::assign(_Old_ptr[_Old_size + _Count], _Elem());

  00087	c6 44 24 30 00	 mov	 BYTE PTR $T1[rsp], 0
  0008c	48 8b 44 24 68	 mov	 rax, QWORD PTR _Count$[rsp]
  00091	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Old_size$[rsp]
  00096	48 03 c8	 add	 rcx, rax
  00099	48 8b c1	 mov	 rax, rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 502  :         _Left = _Right;

  0009c	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Old_ptr$3[rsp]
  000a1	0f b6 54 24 30	 movzx	 edx, BYTE PTR $T1[rsp]
  000a6	88 14 01	 mov	 BYTE PTR [rcx+rax], dl
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1546 :             return *this;

  000a9	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  000ae	eb 34		 jmp	 SHORT $LN1@append
$LN2@append:

; 1547 :         }
; 1548 : 
; 1549 :         return _Reallocate_grow_by(

  000b0	48 8d 44 24 31	 lea	 rax, QWORD PTR $T2[rsp]
  000b5	48 8b f8	 mov	 rdi, rax
  000b8	33 c0		 xor	 eax, eax
  000ba	b9 01 00 00 00	 mov	 ecx, 1
  000bf	f3 aa		 rep stosb
  000c1	0f b6 44 24 70	 movzx	 eax, BYTE PTR _Ch$[rsp]
  000c6	88 44 24 20	 mov	 BYTE PTR [rsp+32], al
  000ca	4c 8b 4c 24 68	 mov	 r9, QWORD PTR _Count$[rsp]
  000cf	44 0f b6 44 24
	31		 movzx	 r8d, BYTE PTR $T2[rsp]
  000d5	48 8b 54 24 68	 mov	 rdx, QWORD PTR _Count$[rsp]
  000da	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  000df	e8 00 00 00 00	 call	 ??$_Reallocate_grow_by@V<lambda_e1befb086ad3257e3f042a63030725f7>@@_KD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_e1befb086ad3257e3f042a63030725f7>@@_KD@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_e1befb086ad3257e3f042a63030725f7>,unsigned __int64,char>
$LN1@append:

; 1550 :             _Count,
; 1551 :             [](_Elem* const _New_ptr, const _Elem* const _Old_ptr, const size_type _Old_size, const size_type _Count,
; 1552 :                 const _Elem _Ch) _STATIC_LAMBDA {
; 1553 :                 _Traits::copy(_New_ptr, _Old_ptr, _Old_size);
; 1554 :                 _Traits::assign(_New_ptr + _Old_size, _Count, _Ch);
; 1555 :                 _Traits::assign(_New_ptr[_Old_size + _Count], _Elem());
; 1556 :             },
; 1557 :             _Count, _Ch);
; 1558 :     }

  000e4	48 83 c4 50	 add	 rsp, 80			; 00000050H
  000e8	5f		 pop	 rdi
  000e9	c3		 ret	 0
?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@_KD@Z ENDP ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::append
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z
_TEXT	SEGMENT
$T1 = 48
$T2 = 49
_Old_size$ = 56
_Old_ptr$3 = 64
_First1$ = 72
this$ = 96
_Ptr$ = 104
_Count$ = 112
?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z PROC ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::append, COMDAT

; 1510 :         _In_reads_(_Count) const _Elem* const _Ptr, _CRT_GUARDOVERFLOW const size_type _Count) {

$LN233:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	57		 push	 rdi
  00010	48 83 ec 50	 sub	 rsp, 80			; 00000050H

; 1511 :         // append [_Ptr, _Ptr + _Count)
; 1512 :         const size_type _Old_size = _Mypair._Myval2._Mysize;

  00014	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00019	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0001d	48 89 44 24 38	 mov	 QWORD PTR _Old_size$[rsp], rax

; 1513 :         if (_Count <= _Mypair._Myval2._Myres - _Old_size) {

  00022	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00027	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Old_size$[rsp]
  0002c	48 8b 40 18	 mov	 rax, QWORD PTR [rax+24]
  00030	48 2b c1	 sub	 rax, rcx
  00033	48 39 44 24 70	 cmp	 QWORD PTR _Count$[rsp], rax
  00038	77 7e		 ja	 SHORT $LN2@append

; 1514 :             _ASAN_STRING_MODIFY(*this, _Old_size, _Old_size + _Count);
; 1515 :             _Mypair._Myval2._Mysize = _Old_size + _Count;

  0003a	48 8b 44 24 70	 mov	 rax, QWORD PTR _Count$[rsp]
  0003f	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Old_size$[rsp]
  00044	48 03 c8	 add	 rcx, rax
  00047	48 8b c1	 mov	 rax, rcx
  0004a	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  0004f	48 89 41 10	 mov	 QWORD PTR [rcx+16], rax

; 1516 :             _Elem* const _Old_ptr   = _Mypair._Myval2._Myptr();

  00053	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00058	48 8b c8	 mov	 rcx, rax
  0005b	e8 00 00 00 00	 call	 ?_Myptr@?$_String_val@U?$_Simple_types@D@std@@@std@@QEAAPEADXZ ; std::_String_val<std::_Simple_types<char> >::_Myptr
  00060	48 89 44 24 40	 mov	 QWORD PTR _Old_ptr$3[rsp], rax

; 1517 :             _Traits::move(_Old_ptr + _Old_size, _Ptr, _Count);

  00065	48 8b 44 24 38	 mov	 rax, QWORD PTR _Old_size$[rsp]
  0006a	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Old_ptr$3[rsp]
  0006f	48 03 c8	 add	 rcx, rax
  00072	48 8b c1	 mov	 rax, rcx
  00075	48 89 44 24 48	 mov	 QWORD PTR _First1$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 174  :         _CSTD memmove(_First1, _First2, _Count * sizeof(_Elem));

  0007a	4c 8b 44 24 70	 mov	 r8, QWORD PTR _Count$[rsp]
  0007f	48 8b 54 24 68	 mov	 rdx, QWORD PTR _Ptr$[rsp]
  00084	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _First1$[rsp]
  00089	e8 00 00 00 00	 call	 memmove
  0008e	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1518 :             _Traits::assign(_Old_ptr[_Old_size + _Count], _Elem());

  0008f	c6 44 24 30 00	 mov	 BYTE PTR $T1[rsp], 0
  00094	48 8b 44 24 70	 mov	 rax, QWORD PTR _Count$[rsp]
  00099	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Old_size$[rsp]
  0009e	48 03 c8	 add	 rcx, rax
  000a1	48 8b c1	 mov	 rax, rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 502  :         _Left = _Right;

  000a4	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Old_ptr$3[rsp]
  000a9	0f b6 54 24 30	 movzx	 edx, BYTE PTR $T1[rsp]
  000ae	88 14 01	 mov	 BYTE PTR [rcx+rax], dl
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 1519 :             return *this;

  000b1	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  000b6	eb 35		 jmp	 SHORT $LN1@append
$LN2@append:

; 1520 :         }
; 1521 : 
; 1522 :         return _Reallocate_grow_by(

  000b8	48 8d 44 24 31	 lea	 rax, QWORD PTR $T2[rsp]
  000bd	48 8b f8	 mov	 rdi, rax
  000c0	33 c0		 xor	 eax, eax
  000c2	b9 01 00 00 00	 mov	 ecx, 1
  000c7	f3 aa		 rep stosb
  000c9	48 8b 44 24 70	 mov	 rax, QWORD PTR _Count$[rsp]
  000ce	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  000d3	4c 8b 4c 24 68	 mov	 r9, QWORD PTR _Ptr$[rsp]
  000d8	44 0f b6 44 24
	31		 movzx	 r8d, BYTE PTR $T2[rsp]
  000de	48 8b 54 24 70	 mov	 rdx, QWORD PTR _Count$[rsp]
  000e3	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  000e8	e8 00 00 00 00	 call	 ??$_Reallocate_grow_by@V<lambda_65e615be2a453ca0576c979606f46740>@@PEBD_K@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAAEAV01@_KV<lambda_65e615be2a453ca0576c979606f46740>@@PEBD_K@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Reallocate_grow_by<<lambda_65e615be2a453ca0576c979606f46740>,char const *,unsigned __int64>
$LN1@append:

; 1523 :             _Count,
; 1524 :             [](_Elem* const _New_ptr, const _Elem* const _Old_ptr, const size_type _Old_size, const _Elem* const _Ptr,
; 1525 :                 const size_type _Count) _STATIC_LAMBDA {
; 1526 :                 _Traits::copy(_New_ptr, _Old_ptr, _Old_size);
; 1527 :                 _Traits::copy(_New_ptr + _Old_size, _Ptr, _Count);
; 1528 :                 _Traits::assign(_New_ptr[_Old_size + _Count], _Elem());
; 1529 :             },
; 1530 :             _Ptr, _Count);
; 1531 :     }

  000ed	48 83 c4 50	 add	 rsp, 80			; 00000050H
  000f1	5f		 pop	 rdi
  000f2	c3		 ret	 0
?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z ENDP ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::append
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@AEBV12@@Z
_TEXT	SEGMENT
this$ = 48
_Right$ = 56
?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@AEBV12@@Z PROC ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::append, COMDAT

; 1482 :     _CONSTEXPR20 basic_string& append(const basic_string& _Right) {

$LN214:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 1483 :         return append(_Right._Mypair._Myval2._Myptr(), _Right._Mypair._Myval2._Mysize);

  0000e	48 8b 44 24 38	 mov	 rax, QWORD PTR _Right$[rsp]
  00013	48 8b c8	 mov	 rcx, rax
  00016	e8 00 00 00 00	 call	 ?_Myptr@?$_String_val@U?$_Simple_types@D@std@@@std@@QEBAPEBDXZ ; std::_String_val<std::_Simple_types<char> >::_Myptr
  0001b	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Right$[rsp]
  00020	4c 8b 41 10	 mov	 r8, QWORD PTR [rcx+16]
  00024	48 8b d0	 mov	 rdx, rax
  00027	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0002c	e8 00 00 00 00	 call	 ?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@QEBD_K@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::append

; 1484 :     }

  00031	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00035	c3		 ret	 0
?append@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAAAEAV12@AEBV12@@Z ENDP ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::append
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ PROC ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >, COMDAT

; 1382 :     _CONSTEXPR20 ~basic_string() noexcept {

$LN82:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 1383 :         _Tidy_deallocate();

  00009	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0000e	e8 00 00 00 00	 call	 ?_Tidy_deallocate@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Tidy_deallocate
  00013	90		 npad	 1

; 1384 : #if _ITERATOR_DEBUG_LEVEL != 0
; 1385 :         auto&& _Alproxy          = _GET_PROXY_ALLOCATOR(_Alty, _Getal());
; 1386 :         const auto _To_delete    = _Mypair._Myval2._Myproxy;
; 1387 :         _Mypair._Myval2._Myproxy = nullptr;
; 1388 :         _Delete_plain_internal(_Alproxy, _To_delete);
; 1389 : #endif // _ITERATOR_DEBUG_LEVEL != 0
; 1390 :     }

  00014	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00018	c3		 ret	 0
??1?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ ENDP ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::~basic_string<char,std::char_traits<char>,std::allocator<char> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Construct_empty@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ
_TEXT	SEGMENT
$T1 = 0
$T2 = 1
_My_data$ = 8
this$ = 32
?_Construct_empty@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ PROC ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_empty, COMDAT

; 855  :     _CONSTEXPR20 void _Construct_empty() {

$LN18:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi
  00006	48 83 ec 10	 sub	 rsp, 16

; 856  :         auto& _My_data = _Mypair._Myval2;

  0000a	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0000f	48 89 44 24 08	 mov	 QWORD PTR _My_data$[rsp], rax

; 857  :         _My_data._Alloc_proxy(_GET_PROXY_ALLOCATOR(_Alty, _Getal()));

  00014	48 8d 44 24 01	 lea	 rax, QWORD PTR $T2[rsp]
  00019	48 8b f8	 mov	 rdi, rax
  0001c	33 c0		 xor	 eax, eax
  0001e	b9 01 00 00 00	 mov	 ecx, 1
  00023	f3 aa		 rep stosb

; 858  : 
; 859  :         // initialize basic_string data members
; 860  :         _My_data._Mysize = 0;

  00025	48 8b 44 24 08	 mov	 rax, QWORD PTR _My_data$[rsp]
  0002a	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 861  :         _My_data._Myres  = _Small_string_capacity;

  00032	48 8b 44 24 08	 mov	 rax, QWORD PTR _My_data$[rsp]
  00037	48 c7 40 18 0f
	00 00 00	 mov	 QWORD PTR [rax+24], 15

; 862  :         _My_data._Activate_SSO_buffer();
; 863  : 
; 864  :         // the _Traits::assign is last so the codegen doesn't think the char write can alias this
; 865  :         _Traits::assign(_My_data._Bx._Buf[0], _Elem());

  0003f	c6 04 24 00	 mov	 BYTE PTR $T1[rsp], 0
  00043	b8 01 00 00 00	 mov	 eax, 1
  00048	48 6b c0 00	 imul	 rax, rax, 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 502  :         _Left = _Right;

  0004c	48 8b 4c 24 08	 mov	 rcx, QWORD PTR _My_data$[rsp]
  00051	0f b6 14 24	 movzx	 edx, BYTE PTR $T1[rsp]
  00055	88 14 01	 mov	 BYTE PTR [rcx+rax], dl
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 866  :     }

  00058	48 83 c4 10	 add	 rsp, 16
  0005c	5f		 pop	 rdi
  0005d	c3		 ret	 0
?_Construct_empty@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ ENDP ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_empty
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@QEBD@Z
_TEXT	SEGMENT
this$ = 32
this$ = 40
this$ = 48
$T1 = 56
$T2 = 64
this$ = 96
_Ptr$ = 104
??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@QEBD@Z PROC ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >, COMDAT

; 768  :     _CONSTEXPR20 basic_string(_In_z_ const _Elem* const _Ptr) : _Mypair(_Zero_then_variadic_args_t{}) {

$LN219:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 50	 sub	 rsp, 80			; 00000050H
  0000f	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 89 44 24 30	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1531 :         : _Ty1(), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  00019	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001e	48 89 44 24 20	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 402  :     _CONSTEXPR20 _String_val() noexcept : _Bx() {}

  00023	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00028	48 89 44 24 28	 mov	 QWORD PTR this$[rsp], rax

; 493  :         _CONSTEXPR20 _Bxty() noexcept : _Buf() {} // user-provided, for fancy pointers

  0002d	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00032	48 8b 7c 24 28	 mov	 rdi, QWORD PTR this$[rsp]
  00037	33 c0		 xor	 eax, eax
  00039	b9 10 00 00 00	 mov	 ecx, 16
  0003e	f3 aa		 rep stosb

; 517  :     size_type _Mysize = 0; // current length of string (size)

  00040	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00045	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 518  :     size_type _Myres  = 0; // current storage reserved for string (capacity)

  0004d	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00052	48 c7 40 18 00
	00 00 00	 mov	 QWORD PTR [rax+24], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\__msvc_string_view.hpp

; 459  :         return _CSTD strlen(reinterpret_cast<const char*>(_First));

  0005a	48 8b 4c 24 68	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  0005f	e8 00 00 00 00	 call	 strlen
  00064	48 89 44 24 38	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 769  :         _Construct<_Construct_strategy::_From_ptr>(_Ptr, _Convert_size<size_type>(_Traits::length(_Ptr)));

  00069	48 8b 44 24 38	 mov	 rax, QWORD PTR $T1[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1131 :     return static_cast<_Size_type>(_Len);

  0006e	48 89 44 24 40	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 769  :         _Construct<_Construct_strategy::_From_ptr>(_Ptr, _Convert_size<size_type>(_Traits::length(_Ptr)));

  00073	48 8b 44 24 40	 mov	 rax, QWORD PTR $T2[rsp]
  00078	4c 8b c0	 mov	 r8, rax
  0007b	48 8b 54 24 68	 mov	 rdx, QWORD PTR _Ptr$[rsp]
  00080	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  00085	e8 00 00 00 00	 call	 ??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct<1,char const *>
  0008a	90		 npad	 1

; 770  :     }

  0008b	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00090	48 83 c4 50	 add	 rsp, 80			; 00000050H
  00094	5f		 pop	 rdi
  00095	c3		 ret	 0
??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@QEBD@Z ENDP ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
this$ = 32
this$ = 40
this$ = 48
$T1 = 56
$T2 = 64
this$ = 96
_Ptr$ = 104
?dtor$0@?0???0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@QEBD@Z@4HA PROC ; `std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 60	 mov	 rcx, QWORD PTR this$[rbp]
  0000d	e8 00 00 00 00	 call	 ??1?$_Compressed_pair@V?$allocator@D@std@@V?$_String_val@U?$_Simple_types@D@std@@@2@$00@std@@QEAA@XZ
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$0@?0???0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@QEBD@Z@4HA ENDP ; `std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@_K1AEBV?$allocator@D@1@@Z
_TEXT	SEGMENT
$T1 = 32
tv155 = 36
this$ = 40
this$ = 48
this$ = 56
$T2 = 64
_Size$ = 72
tv147 = 80
_Result$3 = 88
this$ = 96
$T4 = 104
$T5 = 112
_Ptr$ = 120
$T6 = 128
$T7 = 136
$T8 = 144
this$ = 176
_Right$ = 184
_Roff$ = 192
_Count$ = 200
_Al$ = 208
??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@_K1AEBV?$allocator@D@1@@Z PROC ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >, COMDAT

; 735  :         : _Mypair(_One_then_variadic_args_t{}, _Al) { // construct from _Right [_Roff, _Roff + _Count)

$LN244:
  00000	4c 89 4c 24 20	 mov	 QWORD PTR [rsp+32], r9
  00005	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	57		 push	 rdi
  00015	48 81 ec a0 00
	00 00		 sub	 rsp, 160		; 000000a0H
  0001c	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00024	48 89 44 24 60	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1536 :         : _Ty1(_STD forward<_Other1>(_Val1)), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  00029	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  0002e	48 89 44 24 28	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 402  :     _CONSTEXPR20 _String_val() noexcept : _Bx() {}

  00033	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 89 44 24 38	 mov	 QWORD PTR this$[rsp], rax

; 493  :         _CONSTEXPR20 _Bxty() noexcept : _Buf() {} // user-provided, for fancy pointers

  0003d	48 8b 44 24 38	 mov	 rax, QWORD PTR this$[rsp]
  00042	48 8b 7c 24 38	 mov	 rdi, QWORD PTR this$[rsp]
  00047	33 c0		 xor	 eax, eax
  00049	b9 10 00 00 00	 mov	 ecx, 16
  0004e	f3 aa		 rep stosb

; 517  :     size_type _Mysize = 0; // current length of string (size)

  00050	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00055	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 518  :     size_type _Myres  = 0; // current storage reserved for string (capacity)

  0005d	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00062	48 c7 40 18 00
	00 00 00	 mov	 QWORD PTR [rax+24], 0

; 469  :         if (_Mysize < _Off) {

  0006a	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR _Right$[rsp]
  00072	48 8b 8c 24 c0
	00 00 00	 mov	 rcx, QWORD PTR _Roff$[rsp]
  0007a	48 39 48 10	 cmp	 QWORD PTR [rax+16], rcx
  0007e	73 06		 jae	 SHORT $LN25@basic_stri

; 470  :             _Xran();

  00080	e8 00 00 00 00	 call	 ?_Xran@?$_String_val@U?$_Simple_types@D@std@@@std@@SAXXZ ; std::_String_val<std::_Simple_types<char> >::_Xran
  00085	90		 npad	 1
$LN25@basic_stri:

; 736  :         _Right._Mypair._Myval2._Check_offset(_Roff);
; 737  :         _Construct<_Construct_strategy::_From_ptr>(

  00086	48 8b 84 24 c8
	00 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  0008e	48 89 44 24 48	 mov	 QWORD PTR _Size$[rsp], rax

; 487  :         return (_STD min)(_Size, _Mysize - _Off);

  00093	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR _Right$[rsp]
  0009b	48 8b 8c 24 c0
	00 00 00	 mov	 rcx, QWORD PTR _Roff$[rsp]
  000a3	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  000a7	48 2b c1	 sub	 rax, rcx
  000aa	48 89 44 24 40	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 101  :     return _Right < _Left ? _Right : _Left;

  000af	48 8b 44 24 48	 mov	 rax, QWORD PTR _Size$[rsp]
  000b4	48 39 44 24 40	 cmp	 QWORD PTR $T2[rsp], rax
  000b9	73 0c		 jae	 SHORT $LN35@basic_stri
  000bb	48 8d 44 24 40	 lea	 rax, QWORD PTR $T2[rsp]
  000c0	48 89 44 24 50	 mov	 QWORD PTR tv147[rsp], rax
  000c5	eb 0a		 jmp	 SHORT $LN36@basic_stri
$LN35@basic_stri:
  000c7	48 8d 44 24 48	 lea	 rax, QWORD PTR _Size$[rsp]
  000cc	48 89 44 24 50	 mov	 QWORD PTR tv147[rsp], rax
$LN36@basic_stri:
  000d1	48 8b 44 24 50	 mov	 rax, QWORD PTR tv147[rsp]
  000d6	48 89 44 24 68	 mov	 QWORD PTR $T4[rsp], rax
  000db	48 8b 44 24 68	 mov	 rax, QWORD PTR $T4[rsp]
  000e0	48 89 44 24 70	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 487  :         return (_STD min)(_Size, _Mysize - _Off);

  000e5	48 8b 44 24 70	 mov	 rax, QWORD PTR $T5[rsp]
  000ea	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000ed	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T7[rsp], rax

; 736  :         _Right._Mypair._Myval2._Check_offset(_Roff);
; 737  :         _Construct<_Construct_strategy::_From_ptr>(

  000f5	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR _Right$[rsp]
  000fd	48 89 44 24 30	 mov	 QWORD PTR this$[rsp], rax

; 444  :         const value_type* _Result = _Bx._Buf;

  00102	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00107	48 89 44 24 58	 mov	 QWORD PTR _Result$3[rsp], rax

; 453  :         return _Myres > _Small_string_capacity;

  0010c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00111	48 83 78 18 0f	 cmp	 QWORD PTR [rax+24], 15
  00116	76 0a		 jbe	 SHORT $LN48@basic_stri
  00118	c7 44 24 24 01
	00 00 00	 mov	 DWORD PTR tv155[rsp], 1
  00120	eb 08		 jmp	 SHORT $LN49@basic_stri
$LN48@basic_stri:
  00122	c7 44 24 24 00
	00 00 00	 mov	 DWORD PTR tv155[rsp], 0
$LN49@basic_stri:
  0012a	0f b6 44 24 24	 movzx	 eax, BYTE PTR tv155[rsp]
  0012f	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al

; 445  :         if (_Large_mode_engaged()) {

  00133	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  00138	0f b6 c0	 movzx	 eax, al
  0013b	85 c0		 test	 eax, eax
  0013d	74 27		 je	 SHORT $LN41@basic_stri

; 446  :             _Result = _Unfancy(_Bx._Ptr);

  0013f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00144	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00147	48 89 44 24 78	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  0014c	48 8b 44 24 78	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00151	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 446  :             _Result = _Unfancy(_Bx._Ptr);

  00159	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR $T6[rsp]
  00161	48 89 44 24 58	 mov	 QWORD PTR _Result$3[rsp], rax
$LN41@basic_stri:

; 447  :         }
; 448  : 
; 449  :         return _Result;

  00166	48 8b 44 24 58	 mov	 rax, QWORD PTR _Result$3[rsp]
  0016b	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T8[rsp], rax

; 736  :         _Right._Mypair._Myval2._Check_offset(_Roff);
; 737  :         _Construct<_Construct_strategy::_From_ptr>(

  00173	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T7[rsp]
  0017b	48 8b 8c 24 c0
	00 00 00	 mov	 rcx, QWORD PTR _Roff$[rsp]
  00183	48 8b 94 24 90
	00 00 00	 mov	 rdx, QWORD PTR $T8[rsp]
  0018b	48 03 d1	 add	 rdx, rcx
  0018e	48 8b ca	 mov	 rcx, rdx
  00191	4c 8b c0	 mov	 r8, rax
  00194	48 8b d1	 mov	 rdx, rcx
  00197	48 8b 8c 24 b0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0019f	e8 00 00 00 00	 call	 ??$_Construct@$00PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct<1,char const *>
  001a4	90		 npad	 1

; 738  :             _Right._Mypair._Myval2._Myptr() + _Roff, _Right._Mypair._Myval2._Clamp_suffix_size(_Roff, _Count));
; 739  :     }

  001a5	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  001ad	48 81 c4 a0 00
	00 00		 add	 rsp, 160		; 000000a0H
  001b4	5f		 pop	 rdi
  001b5	c3		 ret	 0
$LN243@basic_stri:
??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@_K1AEBV?$allocator@D@1@@Z ENDP ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
tv155 = 36
this$ = 40
this$ = 48
this$ = 56
$T2 = 64
_Size$ = 72
tv147 = 80
_Result$3 = 88
this$ = 96
$T4 = 104
$T5 = 112
_Ptr$ = 120
$T6 = 128
$T7 = 136
$T8 = 144
this$ = 176
_Right$ = 184
_Roff$ = 192
_Count$ = 200
_Al$ = 208
?dtor$0@?0???0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@_K1AEBV?$allocator@D@1@@Z@4HA PROC ; `std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 8d b0 00
	00 00		 mov	 rcx, QWORD PTR this$[rbp]
  00010	e8 00 00 00 00	 call	 ??1?$_Compressed_pair@V?$allocator@D@std@@V?$_String_val@U?$_Simple_types@D@std@@@2@$00@std@@QEAA@XZ
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$0@?0???0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@_K1AEBV?$allocator@D@1@@Z@4HA ENDP ; `std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
$T1 = 32
tv152 = 36
this$ = 40
this$ = 48
this$ = 56
_Result$2 = 64
$T3 = 72
this$ = 80
_Ptr$ = 88
$T4 = 96
$T5 = 104
$T6 = 112
this$ = 144
_Right$ = 152
??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@@Z PROC ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >, COMDAT

; 717  :         : _Mypair(_One_then_variadic_args_t{}, _Alty_traits::select_on_container_copy_construction(_Right._Getal())) {

$LN224:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 81 ec 80 00
	00 00		 sub	 rsp, 128		; 00000080H

; 3111 :         return _Mypair._Get_first();

  00012	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR _Right$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1543 :         return *this;

  0001a	48 89 44 24 48	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 3111 :         return _Mypair._Get_first();

  0001f	48 8b 44 24 48	 mov	 rax, QWORD PTR $T3[rsp]
  00024	48 89 44 24 70	 mov	 QWORD PTR $T6[rsp], rax

; 717  :         : _Mypair(_One_then_variadic_args_t{}, _Alty_traits::select_on_container_copy_construction(_Right._Getal())) {

  00029	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00031	48 89 44 24 50	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1536 :         : _Ty1(_STD forward<_Other1>(_Val1)), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  00036	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0003b	48 89 44 24 28	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 402  :     _CONSTEXPR20 _String_val() noexcept : _Bx() {}

  00040	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00045	48 89 44 24 38	 mov	 QWORD PTR this$[rsp], rax

; 493  :         _CONSTEXPR20 _Bxty() noexcept : _Buf() {} // user-provided, for fancy pointers

  0004a	48 8b 44 24 38	 mov	 rax, QWORD PTR this$[rsp]
  0004f	48 8b 7c 24 38	 mov	 rdi, QWORD PTR this$[rsp]
  00054	33 c0		 xor	 eax, eax
  00056	b9 10 00 00 00	 mov	 ecx, 16
  0005b	f3 aa		 rep stosb

; 517  :     size_type _Mysize = 0; // current length of string (size)

  0005d	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00062	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 518  :     size_type _Myres  = 0; // current storage reserved for string (capacity)

  0006a	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  0006f	48 c7 40 18 00
	00 00 00	 mov	 QWORD PTR [rax+24], 0

; 718  :         _Construct<_Construct_strategy::_From_string>(_Right._Mypair._Myval2._Myptr(), _Right._Mypair._Myval2._Mysize);

  00077	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR _Right$[rsp]
  0007f	48 89 44 24 30	 mov	 QWORD PTR this$[rsp], rax

; 444  :         const value_type* _Result = _Bx._Buf;

  00084	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00089	48 89 44 24 40	 mov	 QWORD PTR _Result$2[rsp], rax

; 453  :         return _Myres > _Small_string_capacity;

  0008e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00093	48 83 78 18 0f	 cmp	 QWORD PTR [rax+24], 15
  00098	76 0a		 jbe	 SHORT $LN44@basic_stri
  0009a	c7 44 24 24 01
	00 00 00	 mov	 DWORD PTR tv152[rsp], 1
  000a2	eb 08		 jmp	 SHORT $LN45@basic_stri
$LN44@basic_stri:
  000a4	c7 44 24 24 00
	00 00 00	 mov	 DWORD PTR tv152[rsp], 0
$LN45@basic_stri:
  000ac	0f b6 44 24 24	 movzx	 eax, BYTE PTR tv152[rsp]
  000b1	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al

; 445  :         if (_Large_mode_engaged()) {

  000b5	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  000ba	0f b6 c0	 movzx	 eax, al
  000bd	85 c0		 test	 eax, eax
  000bf	74 21		 je	 SHORT $LN37@basic_stri

; 446  :             _Result = _Unfancy(_Bx._Ptr);

  000c1	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  000c6	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000c9	48 89 44 24 58	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  000ce	48 8b 44 24 58	 mov	 rax, QWORD PTR _Ptr$[rsp]
  000d3	48 89 44 24 60	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 446  :             _Result = _Unfancy(_Bx._Ptr);

  000d8	48 8b 44 24 60	 mov	 rax, QWORD PTR $T4[rsp]
  000dd	48 89 44 24 40	 mov	 QWORD PTR _Result$2[rsp], rax
$LN37@basic_stri:

; 447  :         }
; 448  : 
; 449  :         return _Result;

  000e2	48 8b 44 24 40	 mov	 rax, QWORD PTR _Result$2[rsp]
  000e7	48 89 44 24 68	 mov	 QWORD PTR $T5[rsp], rax

; 718  :         _Construct<_Construct_strategy::_From_string>(_Right._Mypair._Myval2._Myptr(), _Right._Mypair._Myval2._Mysize);

  000ec	48 8b 44 24 68	 mov	 rax, QWORD PTR $T5[rsp]
  000f1	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR _Right$[rsp]
  000f9	4c 8b 41 10	 mov	 r8, QWORD PTR [rcx+16]
  000fd	48 8b d0	 mov	 rdx, rax
  00100	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00108	e8 00 00 00 00	 call	 ??$_Construct@$01PEBD@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXQEBD_K@Z ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct<2,char const *>
  0010d	90		 npad	 1

; 719  :     }

  0010e	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00116	48 81 c4 80 00
	00 00		 add	 rsp, 128		; 00000080H
  0011d	5f		 pop	 rdi
  0011e	c3		 ret	 0
??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@@Z ENDP ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
tv152 = 36
this$ = 40
this$ = 48
this$ = 56
_Result$2 = 64
$T3 = 72
this$ = 80
_Ptr$ = 88
$T4 = 96
$T5 = 104
$T6 = 112
this$ = 144
_Right$ = 152
?dtor$0@?0???0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@@Z@4HA PROC ; `std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 8d 90 00
	00 00		 mov	 rcx, QWORD PTR this$[rbp]
  00010	e8 00 00 00 00	 call	 ??1?$_Compressed_pair@V?$allocator@D@std@@V?$_String_val@U?$_Simple_types@D@std@@@2@$00@std@@QEAA@XZ
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$0@?0???0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@AEBV01@@Z@4HA ENDP ; `std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 32
this$ = 40
this$ = 48
this$ = 80
??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ PROC ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >, COMDAT

; 708  :     basic_string() noexcept(is_nothrow_default_constructible_v<_Alty>) : _Mypair(_Zero_then_variadic_args_t{}) {

$LN41:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi
  00006	48 83 ec 40	 sub	 rsp, 64			; 00000040H
  0000a	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0000f	48 89 44 24 30	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1531 :         : _Ty1(), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  00014	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00019	48 89 44 24 20	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 402  :     _CONSTEXPR20 _String_val() noexcept : _Bx() {}

  0001e	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 89 44 24 28	 mov	 QWORD PTR this$[rsp], rax

; 493  :         _CONSTEXPR20 _Bxty() noexcept : _Buf() {} // user-provided, for fancy pointers

  00028	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  0002d	48 8b 7c 24 28	 mov	 rdi, QWORD PTR this$[rsp]
  00032	33 c0		 xor	 eax, eax
  00034	b9 10 00 00 00	 mov	 ecx, 16
  00039	f3 aa		 rep stosb

; 517  :     size_type _Mysize = 0; // current length of string (size)

  0003b	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00040	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 518  :     size_type _Myres  = 0; // current storage reserved for string (capacity)

  00048	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0004d	48 c7 40 18 00
	00 00 00	 mov	 QWORD PTR [rax+24], 0

; 709  :         _Construct_empty();

  00055	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  0005a	e8 00 00 00 00	 call	 ?_Construct_empty@?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@AEAAXXZ ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::_Construct_empty
  0005f	90		 npad	 1

; 710  :     }

  00060	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00065	48 83 c4 40	 add	 rsp, 64			; 00000040H
  00069	5f		 pop	 rdi
  0006a	c3		 ret	 0
??0?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@QEAA@XZ ENDP ; std::basic_string<char,std::char_traits<char>,std::allocator<char> >::basic_string<char,std::char_traits<char>,std::allocator<char> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Xran@?$_String_val@U?$_Simple_types@D@std@@@std@@SAXXZ
_TEXT	SEGMENT
?_Xran@?$_String_val@U?$_Simple_types@D@std@@@std@@SAXXZ PROC ; std::_String_val<std::_Simple_types<char> >::_Xran, COMDAT

; 481  :     [[noreturn]] static void _Xran() {

$LN3:
  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 482  :         _Xout_of_range("invalid string position");

  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_0BI@CFPLBAOH@invalid?5string?5position@
  0000b	e8 00 00 00 00	 call	 ?_Xout_of_range@std@@YAXPEBD@Z ; std::_Xout_of_range
  00010	90		 npad	 1
$LN2@Xran:

; 483  :     }

  00011	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00015	c3		 ret	 0
?_Xran@?$_String_val@U?$_Simple_types@D@std@@@std@@SAXXZ ENDP ; std::_String_val<std::_Simple_types<char> >::_Xran
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Myptr@?$_String_val@U?$_Simple_types@D@std@@@std@@QEBAPEBDXZ
_TEXT	SEGMENT
$T1 = 0
tv76 = 4
_Result$ = 8
_Ptr$ = 16
$T2 = 24
this$ = 48
?_Myptr@?$_String_val@U?$_Simple_types@D@std@@@std@@QEBAPEBDXZ PROC ; std::_String_val<std::_Simple_types<char> >::_Myptr, COMDAT

; 443  :     _NODISCARD _CONSTEXPR20 const value_type* _Myptr() const noexcept {

$LN17:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 444  :         const value_type* _Result = _Bx._Buf;

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 89 44 24 08	 mov	 QWORD PTR _Result$[rsp], rax

; 453  :         return _Myres > _Small_string_capacity;

  00013	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00018	48 83 78 18 0f	 cmp	 QWORD PTR [rax+24], 15
  0001d	76 0a		 jbe	 SHORT $LN7@Myptr
  0001f	c7 44 24 04 01
	00 00 00	 mov	 DWORD PTR tv76[rsp], 1
  00027	eb 08		 jmp	 SHORT $LN8@Myptr
$LN7@Myptr:
  00029	c7 44 24 04 00
	00 00 00	 mov	 DWORD PTR tv76[rsp], 0
$LN8@Myptr:
  00031	0f b6 44 24 04	 movzx	 eax, BYTE PTR tv76[rsp]
  00036	88 04 24	 mov	 BYTE PTR $T1[rsp], al

; 445  :         if (_Large_mode_engaged()) {

  00039	0f b6 04 24	 movzx	 eax, BYTE PTR $T1[rsp]
  0003d	0f b6 c0	 movzx	 eax, al
  00040	85 c0		 test	 eax, eax
  00042	74 21		 je	 SHORT $LN2@Myptr

; 446  :             _Result = _Unfancy(_Bx._Ptr);

  00044	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00049	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0004c	48 89 44 24 10	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  00051	48 8b 44 24 10	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00056	48 89 44 24 18	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 446  :             _Result = _Unfancy(_Bx._Ptr);

  0005b	48 8b 44 24 18	 mov	 rax, QWORD PTR $T2[rsp]
  00060	48 89 44 24 08	 mov	 QWORD PTR _Result$[rsp], rax
$LN2@Myptr:

; 447  :         }
; 448  : 
; 449  :         return _Result;

  00065	48 8b 44 24 08	 mov	 rax, QWORD PTR _Result$[rsp]

; 450  :     }

  0006a	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0006e	c3		 ret	 0
?_Myptr@?$_String_val@U?$_Simple_types@D@std@@@std@@QEBAPEBDXZ ENDP ; std::_String_val<std::_Simple_types<char> >::_Myptr
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Myptr@?$_String_val@U?$_Simple_types@D@std@@@std@@QEAAPEADXZ
_TEXT	SEGMENT
$T1 = 0
tv76 = 4
_Result$ = 8
_Ptr$ = 16
$T2 = 24
this$ = 48
?_Myptr@?$_String_val@U?$_Simple_types@D@std@@@std@@QEAAPEADXZ PROC ; std::_String_val<std::_Simple_types<char> >::_Myptr, COMDAT

; 434  :     _NODISCARD _CONSTEXPR20 value_type* _Myptr() noexcept {

$LN17:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 435  :         value_type* _Result = _Bx._Buf;

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 89 44 24 08	 mov	 QWORD PTR _Result$[rsp], rax

; 453  :         return _Myres > _Small_string_capacity;

  00013	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00018	48 83 78 18 0f	 cmp	 QWORD PTR [rax+24], 15
  0001d	76 0a		 jbe	 SHORT $LN7@Myptr
  0001f	c7 44 24 04 01
	00 00 00	 mov	 DWORD PTR tv76[rsp], 1
  00027	eb 08		 jmp	 SHORT $LN8@Myptr
$LN7@Myptr:
  00029	c7 44 24 04 00
	00 00 00	 mov	 DWORD PTR tv76[rsp], 0
$LN8@Myptr:
  00031	0f b6 44 24 04	 movzx	 eax, BYTE PTR tv76[rsp]
  00036	88 04 24	 mov	 BYTE PTR $T1[rsp], al

; 436  :         if (_Large_mode_engaged()) {

  00039	0f b6 04 24	 movzx	 eax, BYTE PTR $T1[rsp]
  0003d	0f b6 c0	 movzx	 eax, al
  00040	85 c0		 test	 eax, eax
  00042	74 21		 je	 SHORT $LN2@Myptr

; 437  :             _Result = _Unfancy(_Bx._Ptr);

  00044	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00049	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0004c	48 89 44 24 10	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 69   :     return _Ptr;

  00051	48 8b 44 24 10	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00056	48 89 44 24 18	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring

; 437  :             _Result = _Unfancy(_Bx._Ptr);

  0005b	48 8b 44 24 18	 mov	 rax, QWORD PTR $T2[rsp]
  00060	48 89 44 24 08	 mov	 QWORD PTR _Result$[rsp], rax
$LN2@Myptr:

; 438  :         }
; 439  : 
; 440  :         return _Result;

  00065	48 8b 44 24 08	 mov	 rax, QWORD PTR _Result$[rsp]

; 441  :     }

  0006a	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0006e	c3		 ret	 0
?_Myptr@?$_String_val@U?$_Simple_types@D@std@@@std@@QEAAPEADXZ ENDP ; std::_String_val<std::_Simple_types<char> >::_Myptr
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ??0?$_String_val@U?$_Simple_types@D@std@@@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 0
this$ = 32
??0?$_String_val@U?$_Simple_types@D@std@@@std@@QEAA@XZ PROC ; std::_String_val<std::_Simple_types<char> >::_String_val<std::_Simple_types<char> >, COMDAT

; 402  :     _CONSTEXPR20 _String_val() noexcept : _Bx() {}

$LN9:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi
  00006	48 83 ec 10	 sub	 rsp, 16
  0000a	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0000f	48 89 04 24	 mov	 QWORD PTR this$[rsp], rax

; 493  :         _CONSTEXPR20 _Bxty() noexcept : _Buf() {} // user-provided, for fancy pointers

  00013	48 8b 04 24	 mov	 rax, QWORD PTR this$[rsp]
  00017	48 8b 3c 24	 mov	 rdi, QWORD PTR this$[rsp]
  0001b	33 c0		 xor	 eax, eax
  0001d	b9 10 00 00 00	 mov	 ecx, 16
  00022	f3 aa		 rep stosb

; 494  :         _CONSTEXPR20 ~_Bxty() noexcept {} // user-provided, for fancy pointers
; 495  : 
; 496  :         value_type _Buf[_BUF_SIZE];
; 497  :         pointer _Ptr;
; 498  :         char _Alias[_BUF_SIZE]; // TRANSITION, ABI: _Alias is preserved for binary compatibility (especially /clr)
; 499  : 
; 500  :         _CONSTEXPR20 void _Switch_to_buf() noexcept {
; 501  :             _STD _Destroy_in_place(_Ptr);
; 502  : 
; 503  : #if _HAS_CXX20
; 504  :             // start the lifetime of the array elements
; 505  :             if (_STD is_constant_evaluated()) {
; 506  :                 for (size_type _Idx = 0; _Idx < _BUF_SIZE; ++_Idx) {
; 507  :                     _Buf[_Idx] = value_type();
; 508  :                 }
; 509  :             }
; 510  : #endif // _HAS_CXX20
; 511  :         }
; 512  :     };
; 513  :     _Bxty _Bx;
; 514  : 
; 515  :     // invariant: _Myres >= _Mysize, and _Myres >= _Small_string_capacity (after string's construction)
; 516  :     // neither _Mysize nor _Myres takes account of the extra null terminator
; 517  :     size_type _Mysize = 0; // current length of string (size)

  00024	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00029	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 518  :     size_type _Myres  = 0; // current storage reserved for string (capacity)

  00031	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00036	48 c7 40 18 00
	00 00 00	 mov	 QWORD PTR [rax+24], 0

; 402  :     _CONSTEXPR20 _String_val() noexcept : _Bx() {}

  0003e	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00043	48 83 c4 10	 add	 rsp, 16
  00047	5f		 pop	 rdi
  00048	c3		 ret	 0
??0?$_String_val@U?$_Simple_types@D@std@@@std@@QEAA@XZ ENDP ; std::_String_val<std::_Simple_types<char> >::_String_val<std::_Simple_types<char> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?allocate@?$allocator@D@std@@QEAAPEAD_K@Z
_TEXT	SEGMENT
_Overflow_is_possible$1 = 32
_Bytes$ = 40
$T2 = 48
$T3 = 56
$T4 = 64
this$ = 96
_Count$ = 104
?allocate@?$allocator@D@std@@QEAAPEAD_K@Z PROC		; std::allocator<char>::allocate, COMDAT

; 988  :     _NODISCARD_RAW_PTR_ALLOC _CONSTEXPR20 __declspec(allocator) _Ty* allocate(_CRT_GUARDOVERFLOW const size_t _Count) {

$LN11:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 113  :     constexpr bool _Overflow_is_possible = _Ty_size > 1;

  0000e	c6 44 24 20 00	 mov	 BYTE PTR _Overflow_is_possible$1[rsp], 0

; 114  : 
; 115  :     if constexpr (_Overflow_is_possible) {
; 116  :         constexpr size_t _Max_possible = static_cast<size_t>(-1) / _Ty_size;
; 117  :         if (_Count > _Max_possible) {
; 118  :             _Throw_bad_array_new_length(); // multiply overflow
; 119  :         }
; 120  :     }
; 121  : 
; 122  :     return _Count * _Ty_size;

  00013	48 8b 44 24 68	 mov	 rax, QWORD PTR _Count$[rsp]
  00018	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  0001d	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
  00022	48 89 44 24 28	 mov	 QWORD PTR _Bytes$[rsp], rax

; 227  :     if (_Bytes == 0) {

  00027	48 83 7c 24 28
	00		 cmp	 QWORD PTR _Bytes$[rsp], 0
  0002d	75 0b		 jne	 SHORT $LN6@allocate

; 228  :         return nullptr;

  0002f	48 c7 44 24 30
	00 00 00 00	 mov	 QWORD PTR $T2[rsp], 0
  00038	eb 35		 jmp	 SHORT $LN5@allocate
$LN6@allocate:

; 229  :     }
; 230  : 
; 231  : #if _HAS_CXX20 // TRANSITION, GH-1532
; 232  :     if (_STD is_constant_evaluated()) {
; 233  :         return _Traits::_Allocate(_Bytes);
; 234  :     }
; 235  : #endif // _HAS_CXX20
; 236  : 
; 237  : #ifdef __cpp_aligned_new
; 238  :     if constexpr (_Align > __STDCPP_DEFAULT_NEW_ALIGNMENT__) {
; 239  :         size_t _Passed_align = _Align;
; 240  : #if defined(_M_IX86) || defined(_M_X64)
; 241  :         if (_Bytes >= _Big_allocation_threshold) {
; 242  :             // boost the alignment of big allocations to help autovectorization
; 243  :             _Passed_align = (_STD max)(_Align, _Big_allocation_alignment);
; 244  :         }
; 245  : #endif // defined(_M_IX86) || defined(_M_X64)
; 246  :         return _Traits::_Allocate_aligned(_Bytes, _Passed_align);
; 247  :     } else
; 248  : #endif // defined(__cpp_aligned_new)
; 249  :     {
; 250  : #if defined(_M_IX86) || defined(_M_X64)
; 251  :         if (_Bytes >= _Big_allocation_threshold) {

  0003a	48 81 7c 24 28
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  00043	72 11		 jb	 SHORT $LN7@allocate

; 252  :             // boost the alignment of big allocations to help autovectorization
; 253  :             return _Allocate_manually_vector_aligned<_Traits>(_Bytes);

  00045	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  0004a	e8 00 00 00 00	 call	 ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
  0004f	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
  00054	eb 19		 jmp	 SHORT $LN5@allocate
$LN7@allocate:

; 136  :         return ::operator new(_Bytes);

  00056	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  0005b	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00060	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax

; 256  :         return _Traits::_Allocate(_Bytes);

  00065	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  0006a	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
$LN5@allocate:

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  0006f	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]

; 991  :     }

  00074	48 83 c4 58	 add	 rsp, 88			; 00000058H
  00078	c3		 ret	 0
?allocate@?$allocator@D@std@@QEAAPEAD_K@Z ENDP		; std::allocator<char>::allocate
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring
;	COMDAT ?_Xlen_string@std@@YAXXZ
_TEXT	SEGMENT
?_Xlen_string@std@@YAXXZ PROC				; std::_Xlen_string, COMDAT

; 530  : [[noreturn]] inline void _Xlen_string() {

$LN3:
  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 531  :     _Xlength_error("string too long");

  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_0BA@JFNIOLAK@string?5too?5long@
  0000b	e8 00 00 00 00	 call	 ?_Xlength_error@std@@YAXPEBD@Z ; std::_Xlength_error
  00010	90		 npad	 1
$LN2@Xlen_strin:

; 532  : }

  00011	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00015	c3		 ret	 0
?_Xlen_string@std@@YAXXZ ENDP				; std::_Xlen_string
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z
_TEXT	SEGMENT
_Back_shift$ = 48
_Ptr_container$ = 56
_Ptr_user$ = 64
_Min_back_shift$ = 72
_Ptr$ = 96
_Bytes$ = 104
?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z PROC ; std::_Adjust_manually_vector_aligned, COMDAT

; 200  : inline void _Adjust_manually_vector_aligned(void*& _Ptr, size_t& _Bytes) {

$LN5:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 201  :     // adjust parameters from _Allocate_manually_vector_aligned to pass to operator delete
; 202  :     _Bytes += _Non_user_size;

  0000e	48 8b 44 24 68	 mov	 rax, QWORD PTR _Bytes$[rsp]
  00013	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00016	48 83 c0 27	 add	 rax, 39			; 00000027H
  0001a	48 8b 4c 24 68	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  0001f	48 89 01	 mov	 QWORD PTR [rcx], rax

; 203  : 
; 204  :     const uintptr_t* const _Ptr_user = static_cast<uintptr_t*>(_Ptr);

  00022	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00027	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0002a	48 89 44 24 40	 mov	 QWORD PTR _Ptr_user$[rsp], rax

; 205  :     const uintptr_t _Ptr_container   = _Ptr_user[-1];

  0002f	b8 08 00 00 00	 mov	 eax, 8
  00034	48 6b c0 ff	 imul	 rax, rax, -1
  00038	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr_user$[rsp]
  0003d	48 8b 04 01	 mov	 rax, QWORD PTR [rcx+rax]
  00041	48 89 44 24 38	 mov	 QWORD PTR _Ptr_container$[rsp], rax

; 206  : 
; 207  :     // If the following asserts, it likely means that we are performing
; 208  :     // an aligned delete on memory coming from an unaligned allocation.
; 209  :     _STL_ASSERT(_Ptr_user[-2] == _Big_allocation_sentinel, "invalid argument");
; 210  : 
; 211  :     // Extra paranoia on aligned allocation/deallocation; ensure _Ptr_container is
; 212  :     // in range [_Min_back_shift, _Non_user_size]
; 213  : #ifdef _DEBUG
; 214  :     constexpr uintptr_t _Min_back_shift = 2 * sizeof(void*);
; 215  : #else // ^^^ defined(_DEBUG) / !defined(_DEBUG) vvv
; 216  :     constexpr uintptr_t _Min_back_shift = sizeof(void*);

  00046	48 c7 44 24 48
	08 00 00 00	 mov	 QWORD PTR _Min_back_shift$[rsp], 8

; 217  : #endif // ^^^ !defined(_DEBUG) ^^^
; 218  :     const uintptr_t _Back_shift = reinterpret_cast<uintptr_t>(_Ptr) - _Ptr_container;

  0004f	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00054	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr_container$[rsp]
  00059	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0005c	48 2b c1	 sub	 rax, rcx
  0005f	48 89 44 24 30	 mov	 QWORD PTR _Back_shift$[rsp], rax

; 219  :     _STL_VERIFY(_Back_shift >= _Min_back_shift && _Back_shift <= _Non_user_size, "invalid argument");

  00064	48 83 7c 24 30
	08		 cmp	 QWORD PTR _Back_shift$[rsp], 8
  0006a	72 08		 jb	 SHORT $LN3@Adjust_man
  0006c	48 83 7c 24 30
	27		 cmp	 QWORD PTR _Back_shift$[rsp], 39 ; 00000027H
  00072	76 19		 jbe	 SHORT $LN2@Adjust_man
$LN3@Adjust_man:
  00074	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR [rsp+32], 0
  0007d	45 33 c9	 xor	 r9d, r9d
  00080	45 33 c0	 xor	 r8d, r8d
  00083	33 d2		 xor	 edx, edx
  00085	33 c9		 xor	 ecx, ecx
  00087	e8 00 00 00 00	 call	 _invoke_watson
  0008c	90		 npad	 1
$LN2@Adjust_man:

; 220  :     _Ptr = reinterpret_cast<void*>(_Ptr_container);

  0008d	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00092	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr_container$[rsp]
  00097	48 89 08	 mov	 QWORD PTR [rax], rcx
$LN4@Adjust_man:

; 221  : }

  0009a	48 83 c4 58	 add	 rsp, 88			; 00000058H
  0009e	c3		 ret	 0
?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ENDP ; std::_Adjust_manually_vector_aligned
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?_Throw_bad_array_new_length@std@@YAXXZ
_TEXT	SEGMENT
$T1 = 32
?_Throw_bad_array_new_length@std@@YAXXZ PROC		; std::_Throw_bad_array_new_length, COMDAT

; 107  : [[noreturn]] inline void _Throw_bad_array_new_length() {

$LN3:
  00000	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 108  :     _THROW(bad_array_new_length{});

  00004	48 8d 4c 24 20	 lea	 rcx, QWORD PTR $T1[rsp]
  00009	e8 00 00 00 00	 call	 ??0bad_array_new_length@std@@QEAA@XZ ; std::bad_array_new_length::bad_array_new_length
  0000e	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:_TI3?AVbad_array_new_length@std@@
  00015	48 8d 4c 24 20	 lea	 rcx, QWORD PTR $T1[rsp]
  0001a	e8 00 00 00 00	 call	 _CxxThrowException
  0001f	90		 npad	 1
$LN2@Throw_bad_:

; 109  : }

  00020	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00024	c3		 ret	 0
?_Throw_bad_array_new_length@std@@YAXXZ ENDP		; std::_Throw_bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gbad_array_new_length@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gbad_array_new_length@std@@UEAAPEAXI@Z PROC		; std::bad_array_new_length::`scalar deleting destructor', COMDAT
$LN20:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gbad_array_new_length@std@@UEAAPEAXI@Z ENDP		; std::bad_array_new_length::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??0bad_array_new_length@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
__that$ = 56
??0bad_array_new_length@std@@QEAA@AEBV01@@Z PROC	; std::bad_array_new_length::bad_array_new_length, COMDAT
$LN14:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000e	48 8b 54 24 38	 mov	 rdx, QWORD PTR __that$[rsp]
  00013	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00018	e8 00 00 00 00	 call	 ??0bad_alloc@std@@QEAA@AEBV01@@Z
  0001d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00022	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_array_new_length@std@@6B@
  00029	48 89 08	 mov	 QWORD PTR [rax], rcx
  0002c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00031	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00035	c3		 ret	 0
??0bad_array_new_length@std@@QEAA@AEBV01@@Z ENDP	; std::bad_array_new_length::bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??1bad_array_new_length@std@@UEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1bad_array_new_length@std@@UEAA@XZ PROC		; std::bad_array_new_length::~bad_array_new_length, COMDAT
$LN14:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  00018	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 83 c0 08	 add	 rax, 8
  00021	48 8b c8	 mov	 rcx, rax
  00024	e8 00 00 00 00	 call	 __std_exception_destroy
  00029	90		 npad	 1
  0002a	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0002e	c3		 ret	 0
??1bad_array_new_length@std@@UEAA@XZ ENDP		; std::bad_array_new_length::~bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0bad_array_new_length@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 16
??0bad_array_new_length@std@@QEAA@XZ PROC		; std::bad_array_new_length::bad_array_new_length, COMDAT

; 144  :     {

$LN14:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi

; 67   :     {

  00006	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0000b	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00012	48 89 08	 mov	 QWORD PTR [rax], rcx

; 66   :         : _Data()

  00015	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 83 c0 08	 add	 rax, 8
  0001e	48 8b f8	 mov	 rdi, rax
  00021	33 c0		 xor	 eax, eax
  00023	b9 10 00 00 00	 mov	 ecx, 16
  00028	f3 aa		 rep stosb

; 68   :         _Data._What = _Message;

  0002a	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0002f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_0BF@KINCDENJ@bad?5array?5new?5length@
  00036	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 133  :     {

  0003a	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0003f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_alloc@std@@6B@
  00046	48 89 08	 mov	 QWORD PTR [rax], rcx

; 144  :     {

  00049	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_array_new_length@std@@6B@
  00055	48 89 08	 mov	 QWORD PTR [rax], rcx

; 145  :     }

  00058	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0005d	5f		 pop	 rdi
  0005e	c3		 ret	 0
??0bad_array_new_length@std@@QEAA@XZ ENDP		; std::bad_array_new_length::bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gbad_alloc@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gbad_alloc@std@@UEAAPEAXI@Z PROC			; std::bad_alloc::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gbad_alloc@std@@UEAAPEAXI@Z ENDP			; std::bad_alloc::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0bad_alloc@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
__that$ = 56
??0bad_alloc@std@@QEAA@AEBV01@@Z PROC			; std::bad_alloc::bad_alloc, COMDAT
$LN9:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 20	 sub	 rsp, 32			; 00000020H

; 73   :     {

  0000f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  0001b	48 89 08	 mov	 QWORD PTR [rax], rcx

; 72   :         : _Data()

  0001e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 83 c0 08	 add	 rax, 8
  00027	48 8b f8	 mov	 rdi, rax
  0002a	33 c0		 xor	 eax, eax
  0002c	b9 10 00 00 00	 mov	 ecx, 16
  00031	f3 aa		 rep stosb

; 74   :         __std_exception_copy(&_Other._Data, &_Data);

  00033	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 83 c0 08	 add	 rax, 8
  0003c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR __that$[rsp]
  00041	48 83 c1 08	 add	 rcx, 8
  00045	48 8b d0	 mov	 rdx, rax
  00048	e8 00 00 00 00	 call	 __std_exception_copy
  0004d	90		 npad	 1
  0004e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_alloc@std@@6B@
  0005a	48 89 08	 mov	 QWORD PTR [rax], rcx
  0005d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00062	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00066	5f		 pop	 rdi
  00067	c3		 ret	 0
??0bad_alloc@std@@QEAA@AEBV01@@Z ENDP			; std::bad_alloc::bad_alloc
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gexception@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gexception@std@@UEAAPEAXI@Z PROC			; std::exception::`scalar deleting destructor', COMDAT
$LN10:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gexception@std@@UEAAPEAXI@Z ENDP			; std::exception::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ?what@exception@std@@UEBAPEBDXZ
_TEXT	SEGMENT
tv69 = 0
this$ = 32
?what@exception@std@@UEBAPEBDXZ PROC			; std::exception::what, COMDAT

; 95   :     {

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 18	 sub	 rsp, 24

; 96   :         return _Data._What ? _Data._What : "Unknown exception";

  00009	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00013	74 0f		 je	 SHORT $LN3@what
  00015	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0001e	48 89 04 24	 mov	 QWORD PTR tv69[rsp], rax
  00022	eb 0b		 jmp	 SHORT $LN4@what
$LN3@what:
  00024	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BC@EOODALEL@Unknown?5exception@
  0002b	48 89 04 24	 mov	 QWORD PTR tv69[rsp], rax
$LN4@what:
  0002f	48 8b 04 24	 mov	 rax, QWORD PTR tv69[rsp]

; 97   :     }

  00033	48 83 c4 18	 add	 rsp, 24
  00037	c3		 ret	 0
?what@exception@std@@UEBAPEBDXZ ENDP			; std::exception::what
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0exception@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
_Other$ = 56
??0exception@std@@QEAA@AEBV01@@Z PROC			; std::exception::exception, COMDAT

; 73   :     {

$LN4:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  0000f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  0001b	48 89 08	 mov	 QWORD PTR [rax], rcx

; 72   :         : _Data()

  0001e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 83 c0 08	 add	 rax, 8
  00027	48 8b f8	 mov	 rdi, rax
  0002a	33 c0		 xor	 eax, eax
  0002c	b9 10 00 00 00	 mov	 ecx, 16
  00031	f3 aa		 rep stosb

; 74   :         __std_exception_copy(&_Other._Data, &_Data);

  00033	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 83 c0 08	 add	 rax, 8
  0003c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Other$[rsp]
  00041	48 83 c1 08	 add	 rcx, 8
  00045	48 8b d0	 mov	 rdx, rax
  00048	e8 00 00 00 00	 call	 __std_exception_copy
  0004d	90		 npad	 1

; 75   :     }

  0004e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00057	5f		 pop	 rdi
  00058	c3		 ret	 0
??0exception@std@@QEAA@AEBV01@@Z ENDP			; std::exception::exception
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\stdio.h
;	COMDAT sprintf_s
_TEXT	SEGMENT
_Result$1 = 48
tv82 = 52
$T2 = 56
_Result$ = 60
_ArgList$ = 64
_ArgList$ = 72
_Format$ = 80
_Buffer$ = 112
_BufferCount$ = 120
_Format$ = 128
sprintf_s PROC						; COMDAT

; 1823 :         {

$LN7:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	4c 89 4c 24 20	 mov	 QWORD PTR [rsp+32], r9
  00014	48 83 ec 68	 sub	 rsp, 104		; 00000068H

; 1824 :             int _Result;
; 1825 :             va_list _ArgList;
; 1826 :             __crt_va_start(_ArgList, _Format);

  00018	48 8d 84 24 88
	00 00 00	 lea	 rax, QWORD PTR _Format$[rsp+8]
  00020	48 89 44 24 40	 mov	 QWORD PTR _ArgList$[rsp], rax

; 1827 :             _Result = _vsprintf_s_l(_Buffer, _BufferCount, _Format, NULL, _ArgList);

  00025	48 8b 44 24 40	 mov	 rax, QWORD PTR _ArgList$[rsp]
  0002a	48 89 44 24 48	 mov	 QWORD PTR _ArgList$[rsp], rax
  0002f	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _Format$[rsp]
  00037	48 89 44 24 50	 mov	 QWORD PTR _Format$[rsp], rax

; 1491 :         int const _Result = __stdio_common_vsprintf_s(

  0003c	e8 00 00 00 00	 call	 __local_stdio_printf_options
  00041	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _ArgList$[rsp]
  00046	48 89 4c 24 28	 mov	 QWORD PTR [rsp+40], rcx
  0004b	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR [rsp+32], 0
  00054	4c 8b 4c 24 50	 mov	 r9, QWORD PTR _Format$[rsp]
  00059	4c 8b 44 24 78	 mov	 r8, QWORD PTR _BufferCount$[rsp]
  0005e	48 8b 54 24 70	 mov	 rdx, QWORD PTR _Buffer$[rsp]
  00063	48 8b 08	 mov	 rcx, QWORD PTR [rax]
  00066	e8 00 00 00 00	 call	 __stdio_common_vsprintf_s
  0006b	89 44 24 30	 mov	 DWORD PTR _Result$1[rsp], eax

; 1492 :             _CRT_INTERNAL_LOCAL_PRINTF_OPTIONS,
; 1493 :             _Buffer, _BufferCount, _Format, _Locale, _ArgList);
; 1494 : 
; 1495 :         return _Result < 0 ? -1 : _Result;

  0006f	83 7c 24 30 00	 cmp	 DWORD PTR _Result$1[rsp], 0
  00074	7d 0a		 jge	 SHORT $LN5@sprintf_s
  00076	c7 44 24 34 ff
	ff ff ff	 mov	 DWORD PTR tv82[rsp], -1
  0007e	eb 08		 jmp	 SHORT $LN6@sprintf_s
$LN5@sprintf_s:
  00080	8b 44 24 30	 mov	 eax, DWORD PTR _Result$1[rsp]
  00084	89 44 24 34	 mov	 DWORD PTR tv82[rsp], eax
$LN6@sprintf_s:
  00088	8b 44 24 34	 mov	 eax, DWORD PTR tv82[rsp]
  0008c	89 44 24 38	 mov	 DWORD PTR $T2[rsp], eax

; 1827 :             _Result = _vsprintf_s_l(_Buffer, _BufferCount, _Format, NULL, _ArgList);

  00090	8b 44 24 38	 mov	 eax, DWORD PTR $T2[rsp]
  00094	89 44 24 3c	 mov	 DWORD PTR _Result$[rsp], eax

; 1828 :             __crt_va_end(_ArgList);

  00098	48 c7 44 24 40
	00 00 00 00	 mov	 QWORD PTR _ArgList$[rsp], 0

; 1829 :             return _Result;

  000a1	8b 44 24 3c	 mov	 eax, DWORD PTR _Result$[rsp]

; 1830 :         }

  000a5	48 83 c4 68	 add	 rsp, 104		; 00000068H
  000a9	c3		 ret	 0
sprintf_s ENDP
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_stdio_config.h
;	COMDAT __local_stdio_printf_options
_TEXT	SEGMENT
__local_stdio_printf_options PROC			; COMDAT

; 91   :         static unsigned __int64 _OptionsStorage;
; 92   :         return &_OptionsStorage;

  00000	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?_OptionsStorage@?1??__local_stdio_printf_options@@9@4_KA ; `__local_stdio_printf_options'::`2'::_OptionsStorage

; 93   :     }

  00007	c3		 ret	 0
__local_stdio_printf_options ENDP
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\AppChat.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
