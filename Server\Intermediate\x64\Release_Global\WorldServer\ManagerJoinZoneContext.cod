; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

CONST	SEGMENT
?_1@placeholders@std@@3U?$_Ph@$00@2@B	ORG $+1		; std::placeholders::_1
CONST	ENDS
PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	?SendResJoinResult@JoinContextBase@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z ; mu2::JoinContextBase::SendR<PERSON>JoinResult
PUBLIC	?ReqJoinExecutionPrev@JoinContextBase@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@@Z ; mu2::JoinContextBase::ReqJoinExecutionPrev
PUBLIC	?IsNotifyFriendToClient@JoinContextBase@mu2@@UEBA_NXZ ; mu2::JoinContextBase::IsNotifyFriendToClient
PUBLIC	??_GJoinContextBase@mu2@@UEAAPEAXI@Z		; mu2::JoinContextBase::`scalar deleting destructor'
PUBLIC	?Init@theManagerJoinContext@mu2@@UEAA_NPEAX@Z	; mu2::theManagerJoinContext::Init
PUBLIC	?UnInit@theManagerJoinContext@mu2@@UEAA_NXZ	; mu2::theManagerJoinContext::UnInit
PUBLIC	??AtheManagerJoinContext@mu2@@QEBAPEAVJoinContextBase@1@W4Enum@JoinContext@1@@Z ; mu2::theManagerJoinContext::operator[]
PUBLIC	?GetContext@JoinContextCheckIn@mu2@@UEBA?AW4Enum@JoinContext@2@XZ ; mu2::JoinContextCheckIn::GetContext
PUBLIC	?IsNotifyFriendToClient@JoinContextCheckIn@mu2@@UEBA_NXZ ; mu2::JoinContextCheckIn::IsNotifyFriendToClient
PUBLIC	?IsSyncMemberAtPartyCheckIn@JoinContextCheckIn@mu2@@UEBA_NXZ ; mu2::JoinContextCheckIn::IsSyncMemberAtPartyCheckIn
PUBLIC	?IsNotifyAtPartyCheckIn@JoinContextCheckIn@mu2@@UEBA_NXZ ; mu2::JoinContextCheckIn::IsNotifyAtPartyCheckIn
PUBLIC	??_GJoinContextCheckIn@mu2@@UEAAPEAXI@Z		; mu2::JoinContextCheckIn::`scalar deleting destructor'
PUBLIC	?GetContext@JoinContextMapChange@mu2@@UEBA?AW4Enum@JoinContext@2@XZ ; mu2::JoinContextMapChange::GetContext
PUBLIC	?IsSyncMemberAtPartyCheckIn@JoinContextMapChange@mu2@@UEBA_NXZ ; mu2::JoinContextMapChange::IsSyncMemberAtPartyCheckIn
PUBLIC	?IsNotifyAtPartyCheckIn@JoinContextMapChange@mu2@@UEBA_NXZ ; mu2::JoinContextMapChange::IsNotifyAtPartyCheckIn
PUBLIC	??_GJoinContextMapChange@mu2@@UEAAPEAXI@Z	; mu2::JoinContextMapChange::`scalar deleting destructor'
PUBLIC	?GetContext@JoinContextMapWarp@mu2@@UEBA?AW4Enum@JoinContext@2@XZ ; mu2::JoinContextMapWarp::GetContext
PUBLIC	?IsSyncMemberAtPartyCheckIn@JoinContextMapWarp@mu2@@UEBA_NXZ ; mu2::JoinContextMapWarp::IsSyncMemberAtPartyCheckIn
PUBLIC	?IsNotifyAtPartyCheckIn@JoinContextMapWarp@mu2@@UEBA_NXZ ; mu2::JoinContextMapWarp::IsNotifyAtPartyCheckIn
PUBLIC	??_GJoinContextMapWarp@mu2@@UEAAPEAXI@Z		; mu2::JoinContextMapWarp::`scalar deleting destructor'
PUBLIC	?GetContext@JoinContextChannelChange@mu2@@UEBA?AW4Enum@JoinContext@2@XZ ; mu2::JoinContextChannelChange::GetContext
PUBLIC	?IsSyncMemberAtPartyCheckIn@JoinContextChannelChange@mu2@@UEBA_NXZ ; mu2::JoinContextChannelChange::IsSyncMemberAtPartyCheckIn
PUBLIC	?IsNotifyAtPartyCheckIn@JoinContextChannelChange@mu2@@UEBA_NXZ ; mu2::JoinContextChannelChange::IsNotifyAtPartyCheckIn
PUBLIC	??_GJoinContextChannelChange@mu2@@UEAAPEAXI@Z	; mu2::JoinContextChannelChange::`scalar deleting destructor'
PUBLIC	?GetContext@JoinContextLogout@mu2@@UEBA?AW4Enum@JoinContext@2@XZ ; mu2::JoinContextLogout::GetContext
PUBLIC	?IsSyncMemberAtPartyCheckIn@JoinContextLogout@mu2@@UEBA_NXZ ; mu2::JoinContextLogout::IsSyncMemberAtPartyCheckIn
PUBLIC	?IsNotifyAtPartyCheckIn@JoinContextLogout@mu2@@UEBA_NXZ ; mu2::JoinContextLogout::IsNotifyAtPartyCheckIn
PUBLIC	??_GJoinContextLogout@mu2@@UEAAPEAXI@Z		; mu2::JoinContextLogout::`scalar deleting destructor'
PUBLIC	?GetContext@JoinContextTutorial@mu2@@UEBA?AW4Enum@JoinContext@2@XZ ; mu2::JoinContextTutorial::GetContext
PUBLIC	?IsSyncMemberAtPartyCheckIn@JoinContextTutorial@mu2@@UEBA_NXZ ; mu2::JoinContextTutorial::IsSyncMemberAtPartyCheckIn
PUBLIC	?IsNotifyAtPartyCheckIn@JoinContextTutorial@mu2@@UEBA_NXZ ; mu2::JoinContextTutorial::IsNotifyAtPartyCheckIn
PUBLIC	??_GJoinContextTutorial@mu2@@UEAAPEAXI@Z	; mu2::JoinContextTutorial::`scalar deleting destructor'
PUBLIC	?GetContext@JoinContextNone@mu2@@UEBA?AW4Enum@JoinContext@2@XZ ; mu2::JoinContextNone::GetContext
PUBLIC	?SendResJoinResult@JoinContextNone@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z ; mu2::JoinContextNone::SendResJoinResult
PUBLIC	?IsSyncMemberAtPartyCheckIn@JoinContextNone@mu2@@UEBA_NXZ ; mu2::JoinContextNone::IsSyncMemberAtPartyCheckIn
PUBLIC	?IsNotifyAtPartyCheckIn@JoinContextNone@mu2@@UEBA_NXZ ; mu2::JoinContextNone::IsNotifyAtPartyCheckIn
PUBLIC	??_GJoinContextNone@mu2@@UEAAPEAXI@Z		; mu2::JoinContextNone::`scalar deleting destructor'
PUBLIC	??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@ ; `string'
PUBLIC	??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@		; `string'
PUBLIC	??_7JoinContextBase@mu2@@6B@			; mu2::JoinContextBase::`vftable'
PUBLIC	??_7JoinContextCheckIn@mu2@@6B@			; mu2::JoinContextCheckIn::`vftable'
PUBLIC	??_7JoinContextMapChange@mu2@@6B@		; mu2::JoinContextMapChange::`vftable'
PUBLIC	??_7JoinContextMapWarp@mu2@@6B@			; mu2::JoinContextMapWarp::`vftable'
PUBLIC	??_7JoinContextChannelChange@mu2@@6B@		; mu2::JoinContextChannelChange::`vftable'
PUBLIC	??_7JoinContextLogout@mu2@@6B@			; mu2::JoinContextLogout::`vftable'
PUBLIC	??_7JoinContextTutorial@mu2@@6B@		; mu2::JoinContextTutorial::`vftable'
PUBLIC	??_7JoinContextNone@mu2@@6B@			; mu2::JoinContextNone::`vftable'
PUBLIC	??_C@_0BO@CFEAKOHI@SendResJoinResult?5?9?5?$DP?$DP?$DP?5?$DP?$DP?5?$DP?$DP@ ; `string'
PUBLIC	??_C@_0FA@HCBECIPM@F?3?2Release_Branch?2Server?2Develo@ ; `string'
PUBLIC	??_C@_0CB@LGFOCIKM@?$CB?$CCSendResJoinResult?5?9?5?$DP?$DP?$DP?5?$DP?$DP?5?$DP?$DP@ ; `string'
PUBLIC	??_C@_0CI@FMPKNJOC@mu2?3?3JoinContextNone?3?3SendResJo@ ; `string'
PUBLIC	??_C@_0CH@GHLDOJOM@IsSyncMemberAtPartyCheckIn?5?9?5?$DP?$DP@ ; `string'
PUBLIC	??_C@_0CK@DKADEPNB@?$CB?$CCIsSyncMemberAtPartyCheckIn?5?9?5@ ; `string'
PUBLIC	??_C@_0DB@GDEJEJOP@mu2?3?3JoinContextNone?3?3IsSyncMem@ ; `string'
PUBLIC	??_C@_0CN@GOBIHNDP@mu2?3?3JoinContextNone?3?3IsNotifyA@ ; `string'
PUBLIC	??_R4JoinContextBase@mu2@@6B@			; mu2::JoinContextBase::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVJoinContextBase@mu2@@@8			; mu2::JoinContextBase `RTTI Type Descriptor'
PUBLIC	??_R3JoinContextBase@mu2@@8			; mu2::JoinContextBase::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2JoinContextBase@mu2@@8			; mu2::JoinContextBase::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@JoinContextBase@mu2@@8		; mu2::JoinContextBase::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4JoinContextCheckIn@mu2@@6B@		; mu2::JoinContextCheckIn::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVJoinContextCheckIn@mu2@@@8		; mu2::JoinContextCheckIn `RTTI Type Descriptor'
PUBLIC	??_R3JoinContextCheckIn@mu2@@8			; mu2::JoinContextCheckIn::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2JoinContextCheckIn@mu2@@8			; mu2::JoinContextCheckIn::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@JoinContextCheckIn@mu2@@8		; mu2::JoinContextCheckIn::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4JoinContextMapChange@mu2@@6B@		; mu2::JoinContextMapChange::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVJoinContextMapChange@mu2@@@8		; mu2::JoinContextMapChange `RTTI Type Descriptor'
PUBLIC	??_R3JoinContextMapChange@mu2@@8		; mu2::JoinContextMapChange::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2JoinContextMapChange@mu2@@8		; mu2::JoinContextMapChange::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@JoinContextMapChange@mu2@@8	; mu2::JoinContextMapChange::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4JoinContextMapWarp@mu2@@6B@		; mu2::JoinContextMapWarp::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVJoinContextMapWarp@mu2@@@8		; mu2::JoinContextMapWarp `RTTI Type Descriptor'
PUBLIC	??_R3JoinContextMapWarp@mu2@@8			; mu2::JoinContextMapWarp::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2JoinContextMapWarp@mu2@@8			; mu2::JoinContextMapWarp::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@JoinContextMapWarp@mu2@@8		; mu2::JoinContextMapWarp::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4JoinContextChannelChange@mu2@@6B@		; mu2::JoinContextChannelChange::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVJoinContextChannelChange@mu2@@@8	; mu2::JoinContextChannelChange `RTTI Type Descriptor'
PUBLIC	??_R3JoinContextChannelChange@mu2@@8		; mu2::JoinContextChannelChange::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2JoinContextChannelChange@mu2@@8		; mu2::JoinContextChannelChange::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@JoinContextChannelChange@mu2@@8	; mu2::JoinContextChannelChange::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4JoinContextLogout@mu2@@6B@			; mu2::JoinContextLogout::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVJoinContextLogout@mu2@@@8		; mu2::JoinContextLogout `RTTI Type Descriptor'
PUBLIC	??_R3JoinContextLogout@mu2@@8			; mu2::JoinContextLogout::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2JoinContextLogout@mu2@@8			; mu2::JoinContextLogout::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@JoinContextLogout@mu2@@8		; mu2::JoinContextLogout::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4JoinContextTutorial@mu2@@6B@		; mu2::JoinContextTutorial::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVJoinContextTutorial@mu2@@@8		; mu2::JoinContextTutorial `RTTI Type Descriptor'
PUBLIC	??_R3JoinContextTutorial@mu2@@8			; mu2::JoinContextTutorial::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2JoinContextTutorial@mu2@@8			; mu2::JoinContextTutorial::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@JoinContextTutorial@mu2@@8	; mu2::JoinContextTutorial::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4JoinContextNone@mu2@@6B@			; mu2::JoinContextNone::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVJoinContextNone@mu2@@@8			; mu2::JoinContextNone `RTTI Type Descriptor'
PUBLIC	??_R3JoinContextNone@mu2@@8			; mu2::JoinContextNone::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2JoinContextNone@mu2@@8			; mu2::JoinContextNone::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@JoinContextNone@mu2@@8		; mu2::JoinContextNone::`RTTI Base Class Descriptor at (0,-1,0,64)'
EXTRN	_purecall:PROC
EXTRN	??2@YAPEAX_K@Z:PROC				; operator new
EXTRN	??3@YAXPEAX_K@Z:PROC				; operator delete
EXTRN	__imp_GetStdHandle:PROC
EXTRN	__imp_SetConsoleTextAttribute:PROC
EXTRN	?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ:PROC	; mu2::Logger::Logging
EXTRN	?OnKnightageGameReady@JoinContextBase@mu2@@UEBAXAEAPEAVEntityPlayer@2@@Z:PROC ; mu2::JoinContextBase::OnKnightageGameReady
EXTRN	??_EJoinContextBase@mu2@@UEAAPEAXI@Z:PROC	; mu2::JoinContextBase::`vector deleting destructor'
EXTRN	?SendResJoinResult@JoinContextCheckIn@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z:PROC ; mu2::JoinContextCheckIn::SendResJoinResult
EXTRN	?ReqJoinExecutionPrev@JoinContextCheckIn@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@@Z:PROC ; mu2::JoinContextCheckIn::ReqJoinExecutionPrev
EXTRN	?OnKnightageGameReady@JoinContextCheckIn@mu2@@UEBAXAEAPEAVEntityPlayer@2@@Z:PROC ; mu2::JoinContextCheckIn::OnKnightageGameReady
EXTRN	??_EJoinContextCheckIn@mu2@@UEAAPEAXI@Z:PROC	; mu2::JoinContextCheckIn::`vector deleting destructor'
EXTRN	?SendResJoinResult@JoinContextMapChange@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z:PROC ; mu2::JoinContextMapChange::SendResJoinResult
EXTRN	??_EJoinContextMapChange@mu2@@UEAAPEAXI@Z:PROC	; mu2::JoinContextMapChange::`vector deleting destructor'
EXTRN	?SendResJoinResult@JoinContextMapWarp@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z:PROC ; mu2::JoinContextMapWarp::SendResJoinResult
EXTRN	??_EJoinContextMapWarp@mu2@@UEAAPEAXI@Z:PROC	; mu2::JoinContextMapWarp::`vector deleting destructor'
EXTRN	?ReqJoinExecutionPrev@JoinContextChannelChange@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@@Z:PROC ; mu2::JoinContextChannelChange::ReqJoinExecutionPrev
EXTRN	?SendResJoinResult@JoinContextChannelChange@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z:PROC ; mu2::JoinContextChannelChange::SendResJoinResult
EXTRN	??_EJoinContextChannelChange@mu2@@UEAAPEAXI@Z:PROC ; mu2::JoinContextChannelChange::`vector deleting destructor'
EXTRN	??_EJoinContextLogout@mu2@@UEAAPEAXI@Z:PROC	; mu2::JoinContextLogout::`vector deleting destructor'
EXTRN	?SendResJoinResult@JoinContextTutorial@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z:PROC ; mu2::JoinContextTutorial::SendResJoinResult
EXTRN	?ReqJoinExecutionPrev@JoinContextTutorial@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@@Z:PROC ; mu2::JoinContextTutorial::ReqJoinExecutionPrev
EXTRN	??_EJoinContextTutorial@mu2@@UEAAPEAXI@Z:PROC	; mu2::JoinContextTutorial::`vector deleting destructor'
EXTRN	??_EJoinContextNone@mu2@@UEAAPEAXI@Z:PROC	; mu2::JoinContextNone::`vector deleting destructor'
EXTRN	??_7type_info@@6B@:BYTE				; type_info::`vftable'
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GJoinContextBase@mu2@@UEAAPEAXI@Z DD imagerel $LN10
	DD	imagerel $LN10+65
	DD	imagerel $unwind$??_GJoinContextBase@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Init@theManagerJoinContext@mu2@@UEAA_NPEAX@Z DD imagerel $LN59
	DD	imagerel $LN59+976
	DD	imagerel $unwind$?Init@theManagerJoinContext@mu2@@UEAA_NPEAX@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?UnInit@theManagerJoinContext@mu2@@UEAA_NXZ DD imagerel $LN9
	DD	imagerel $LN9+146
	DD	imagerel $unwind$?UnInit@theManagerJoinContext@mu2@@UEAA_NXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GJoinContextCheckIn@mu2@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+65
	DD	imagerel $unwind$??_GJoinContextCheckIn@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GJoinContextMapChange@mu2@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+65
	DD	imagerel $unwind$??_GJoinContextMapChange@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GJoinContextMapWarp@mu2@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+65
	DD	imagerel $unwind$??_GJoinContextMapWarp@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GJoinContextChannelChange@mu2@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+65
	DD	imagerel $unwind$??_GJoinContextChannelChange@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GJoinContextLogout@mu2@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+65
	DD	imagerel $unwind$??_GJoinContextLogout@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GJoinContextTutorial@mu2@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+65
	DD	imagerel $unwind$??_GJoinContextTutorial@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?SendResJoinResult@JoinContextNone@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z DD imagerel $LN4
	DD	imagerel $LN4+198
	DD	imagerel $unwind$?SendResJoinResult@JoinContextNone@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?IsSyncMemberAtPartyCheckIn@JoinContextNone@mu2@@UEBA_NXZ DD imagerel $LN4
	DD	imagerel $LN4+189
	DD	imagerel $unwind$?IsSyncMemberAtPartyCheckIn@JoinContextNone@mu2@@UEBA_NXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?IsNotifyAtPartyCheckIn@JoinContextNone@mu2@@UEBA_NXZ DD imagerel $LN4
	DD	imagerel $LN4+189
	DD	imagerel $unwind$?IsNotifyAtPartyCheckIn@JoinContextNone@mu2@@UEBA_NXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GJoinContextNone@mu2@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+65
	DD	imagerel $unwind$??_GJoinContextNone@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT ??_R1A@?0A@EA@JoinContextNone@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@JoinContextNone@mu2@@8 DD imagerel ??_R0?AVJoinContextNone@mu2@@@8 ; mu2::JoinContextNone::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3JoinContextNone@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2JoinContextNone@mu2@@8
rdata$r	SEGMENT
??_R2JoinContextNone@mu2@@8 DD imagerel ??_R1A@?0A@EA@JoinContextNone@mu2@@8 ; mu2::JoinContextNone::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@JoinContextBase@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3JoinContextNone@mu2@@8
rdata$r	SEGMENT
??_R3JoinContextNone@mu2@@8 DD 00H			; mu2::JoinContextNone::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2JoinContextNone@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVJoinContextNone@mu2@@@8
data$rs	SEGMENT
??_R0?AVJoinContextNone@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::JoinContextNone `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVJoinContextNone@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4JoinContextNone@mu2@@6B@
rdata$r	SEGMENT
??_R4JoinContextNone@mu2@@6B@ DD 01H			; mu2::JoinContextNone::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVJoinContextNone@mu2@@@8
	DD	imagerel ??_R3JoinContextNone@mu2@@8
	DD	imagerel ??_R4JoinContextNone@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@JoinContextTutorial@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@JoinContextTutorial@mu2@@8 DD imagerel ??_R0?AVJoinContextTutorial@mu2@@@8 ; mu2::JoinContextTutorial::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3JoinContextTutorial@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2JoinContextTutorial@mu2@@8
rdata$r	SEGMENT
??_R2JoinContextTutorial@mu2@@8 DD imagerel ??_R1A@?0A@EA@JoinContextTutorial@mu2@@8 ; mu2::JoinContextTutorial::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@JoinContextBase@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3JoinContextTutorial@mu2@@8
rdata$r	SEGMENT
??_R3JoinContextTutorial@mu2@@8 DD 00H			; mu2::JoinContextTutorial::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2JoinContextTutorial@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVJoinContextTutorial@mu2@@@8
data$rs	SEGMENT
??_R0?AVJoinContextTutorial@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::JoinContextTutorial `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVJoinContextTutorial@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4JoinContextTutorial@mu2@@6B@
rdata$r	SEGMENT
??_R4JoinContextTutorial@mu2@@6B@ DD 01H		; mu2::JoinContextTutorial::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVJoinContextTutorial@mu2@@@8
	DD	imagerel ??_R3JoinContextTutorial@mu2@@8
	DD	imagerel ??_R4JoinContextTutorial@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@JoinContextLogout@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@JoinContextLogout@mu2@@8 DD imagerel ??_R0?AVJoinContextLogout@mu2@@@8 ; mu2::JoinContextLogout::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3JoinContextLogout@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2JoinContextLogout@mu2@@8
rdata$r	SEGMENT
??_R2JoinContextLogout@mu2@@8 DD imagerel ??_R1A@?0A@EA@JoinContextLogout@mu2@@8 ; mu2::JoinContextLogout::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@JoinContextBase@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3JoinContextLogout@mu2@@8
rdata$r	SEGMENT
??_R3JoinContextLogout@mu2@@8 DD 00H			; mu2::JoinContextLogout::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2JoinContextLogout@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVJoinContextLogout@mu2@@@8
data$rs	SEGMENT
??_R0?AVJoinContextLogout@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::JoinContextLogout `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVJoinContextLogout@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4JoinContextLogout@mu2@@6B@
rdata$r	SEGMENT
??_R4JoinContextLogout@mu2@@6B@ DD 01H			; mu2::JoinContextLogout::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVJoinContextLogout@mu2@@@8
	DD	imagerel ??_R3JoinContextLogout@mu2@@8
	DD	imagerel ??_R4JoinContextLogout@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@JoinContextChannelChange@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@JoinContextChannelChange@mu2@@8 DD imagerel ??_R0?AVJoinContextChannelChange@mu2@@@8 ; mu2::JoinContextChannelChange::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3JoinContextChannelChange@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2JoinContextChannelChange@mu2@@8
rdata$r	SEGMENT
??_R2JoinContextChannelChange@mu2@@8 DD imagerel ??_R1A@?0A@EA@JoinContextChannelChange@mu2@@8 ; mu2::JoinContextChannelChange::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@JoinContextBase@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3JoinContextChannelChange@mu2@@8
rdata$r	SEGMENT
??_R3JoinContextChannelChange@mu2@@8 DD 00H		; mu2::JoinContextChannelChange::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2JoinContextChannelChange@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVJoinContextChannelChange@mu2@@@8
data$rs	SEGMENT
??_R0?AVJoinContextChannelChange@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::JoinContextChannelChange `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVJoinContextChannelChange@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4JoinContextChannelChange@mu2@@6B@
rdata$r	SEGMENT
??_R4JoinContextChannelChange@mu2@@6B@ DD 01H		; mu2::JoinContextChannelChange::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVJoinContextChannelChange@mu2@@@8
	DD	imagerel ??_R3JoinContextChannelChange@mu2@@8
	DD	imagerel ??_R4JoinContextChannelChange@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@JoinContextMapWarp@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@JoinContextMapWarp@mu2@@8 DD imagerel ??_R0?AVJoinContextMapWarp@mu2@@@8 ; mu2::JoinContextMapWarp::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3JoinContextMapWarp@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2JoinContextMapWarp@mu2@@8
rdata$r	SEGMENT
??_R2JoinContextMapWarp@mu2@@8 DD imagerel ??_R1A@?0A@EA@JoinContextMapWarp@mu2@@8 ; mu2::JoinContextMapWarp::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@JoinContextBase@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3JoinContextMapWarp@mu2@@8
rdata$r	SEGMENT
??_R3JoinContextMapWarp@mu2@@8 DD 00H			; mu2::JoinContextMapWarp::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2JoinContextMapWarp@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVJoinContextMapWarp@mu2@@@8
data$rs	SEGMENT
??_R0?AVJoinContextMapWarp@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::JoinContextMapWarp `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVJoinContextMapWarp@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4JoinContextMapWarp@mu2@@6B@
rdata$r	SEGMENT
??_R4JoinContextMapWarp@mu2@@6B@ DD 01H			; mu2::JoinContextMapWarp::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVJoinContextMapWarp@mu2@@@8
	DD	imagerel ??_R3JoinContextMapWarp@mu2@@8
	DD	imagerel ??_R4JoinContextMapWarp@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@JoinContextMapChange@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@JoinContextMapChange@mu2@@8 DD imagerel ??_R0?AVJoinContextMapChange@mu2@@@8 ; mu2::JoinContextMapChange::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3JoinContextMapChange@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2JoinContextMapChange@mu2@@8
rdata$r	SEGMENT
??_R2JoinContextMapChange@mu2@@8 DD imagerel ??_R1A@?0A@EA@JoinContextMapChange@mu2@@8 ; mu2::JoinContextMapChange::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@JoinContextBase@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3JoinContextMapChange@mu2@@8
rdata$r	SEGMENT
??_R3JoinContextMapChange@mu2@@8 DD 00H			; mu2::JoinContextMapChange::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2JoinContextMapChange@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVJoinContextMapChange@mu2@@@8
data$rs	SEGMENT
??_R0?AVJoinContextMapChange@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::JoinContextMapChange `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVJoinContextMapChange@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4JoinContextMapChange@mu2@@6B@
rdata$r	SEGMENT
??_R4JoinContextMapChange@mu2@@6B@ DD 01H		; mu2::JoinContextMapChange::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVJoinContextMapChange@mu2@@@8
	DD	imagerel ??_R3JoinContextMapChange@mu2@@8
	DD	imagerel ??_R4JoinContextMapChange@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@JoinContextCheckIn@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@JoinContextCheckIn@mu2@@8 DD imagerel ??_R0?AVJoinContextCheckIn@mu2@@@8 ; mu2::JoinContextCheckIn::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3JoinContextCheckIn@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2JoinContextCheckIn@mu2@@8
rdata$r	SEGMENT
??_R2JoinContextCheckIn@mu2@@8 DD imagerel ??_R1A@?0A@EA@JoinContextCheckIn@mu2@@8 ; mu2::JoinContextCheckIn::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@JoinContextBase@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3JoinContextCheckIn@mu2@@8
rdata$r	SEGMENT
??_R3JoinContextCheckIn@mu2@@8 DD 00H			; mu2::JoinContextCheckIn::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2JoinContextCheckIn@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVJoinContextCheckIn@mu2@@@8
data$rs	SEGMENT
??_R0?AVJoinContextCheckIn@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::JoinContextCheckIn `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVJoinContextCheckIn@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4JoinContextCheckIn@mu2@@6B@
rdata$r	SEGMENT
??_R4JoinContextCheckIn@mu2@@6B@ DD 01H			; mu2::JoinContextCheckIn::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVJoinContextCheckIn@mu2@@@8
	DD	imagerel ??_R3JoinContextCheckIn@mu2@@8
	DD	imagerel ??_R4JoinContextCheckIn@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@JoinContextBase@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@JoinContextBase@mu2@@8 DD imagerel ??_R0?AVJoinContextBase@mu2@@@8 ; mu2::JoinContextBase::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3JoinContextBase@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2JoinContextBase@mu2@@8
rdata$r	SEGMENT
??_R2JoinContextBase@mu2@@8 DD imagerel ??_R1A@?0A@EA@JoinContextBase@mu2@@8 ; mu2::JoinContextBase::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3JoinContextBase@mu2@@8
rdata$r	SEGMENT
??_R3JoinContextBase@mu2@@8 DD 00H			; mu2::JoinContextBase::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2JoinContextBase@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVJoinContextBase@mu2@@@8
data$rs	SEGMENT
??_R0?AVJoinContextBase@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::JoinContextBase `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVJoinContextBase@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4JoinContextBase@mu2@@6B@
rdata$r	SEGMENT
??_R4JoinContextBase@mu2@@6B@ DD 01H			; mu2::JoinContextBase::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVJoinContextBase@mu2@@@8
	DD	imagerel ??_R3JoinContextBase@mu2@@8
	DD	imagerel ??_R4JoinContextBase@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_C@_0CN@GOBIHNDP@mu2?3?3JoinContextNone?3?3IsNotifyA@
CONST	SEGMENT
??_C@_0CN@GOBIHNDP@mu2?3?3JoinContextNone?3?3IsNotifyA@ DB 'mu2::JoinCont'
	DB	'extNone::IsNotifyAtPartyCheckIn', 00H	; `string'
CONST	ENDS
;	COMDAT ??_C@_0DB@GDEJEJOP@mu2?3?3JoinContextNone?3?3IsSyncMem@
CONST	SEGMENT
??_C@_0DB@GDEJEJOP@mu2?3?3JoinContextNone?3?3IsSyncMem@ DB 'mu2::JoinCont'
	DB	'extNone::IsSyncMemberAtPartyCheckIn', 00H	; `string'
CONST	ENDS
;	COMDAT ??_C@_0CK@DKADEPNB@?$CB?$CCIsSyncMemberAtPartyCheckIn?5?9?5@
CONST	SEGMENT
??_C@_0CK@DKADEPNB@?$CB?$CCIsSyncMemberAtPartyCheckIn?5?9?5@ DB '!"IsSync'
	DB	'MemberAtPartyCheckIn - ??? ?? ??"', 00H	; `string'
CONST	ENDS
;	COMDAT ??_C@_0CH@GHLDOJOM@IsSyncMemberAtPartyCheckIn?5?9?5?$DP?$DP@
CONST	SEGMENT
??_C@_0CH@GHLDOJOM@IsSyncMemberAtPartyCheckIn?5?9?5?$DP?$DP@ DB 'IsSyncMe'
	DB	'mberAtPartyCheckIn - ??? ?? ??', 00H	; `string'
CONST	ENDS
;	COMDAT ??_C@_0CI@FMPKNJOC@mu2?3?3JoinContextNone?3?3SendResJo@
CONST	SEGMENT
??_C@_0CI@FMPKNJOC@mu2?3?3JoinContextNone?3?3SendResJo@ DB 'mu2::JoinCont'
	DB	'extNone::SendResJoinResult', 00H		; `string'
CONST	ENDS
;	COMDAT ??_C@_0CB@LGFOCIKM@?$CB?$CCSendResJoinResult?5?9?5?$DP?$DP?$DP?5?$DP?$DP?5?$DP?$DP@
CONST	SEGMENT
??_C@_0CB@LGFOCIKM@?$CB?$CCSendResJoinResult?5?9?5?$DP?$DP?$DP?5?$DP?$DP?5?$DP?$DP@ DB '!'
	DB	'"SendResJoinResult - ??? ?? ??"', 00H	; `string'
CONST	ENDS
;	COMDAT ??_C@_0FA@HCBECIPM@F?3?2Release_Branch?2Server?2Develo@
CONST	SEGMENT
??_C@_0FA@HCBECIPM@F?3?2Release_Branch?2Server?2Develo@ DB 'F:\Release_Br'
	DB	'anch\Server\Development\Frontend\WorldServer\JoinZoneContextN'
	DB	'one.h', 00H					; `string'
CONST	ENDS
;	COMDAT ??_C@_0BO@CFEAKOHI@SendResJoinResult?5?9?5?$DP?$DP?$DP?5?$DP?$DP?5?$DP?$DP@
CONST	SEGMENT
??_C@_0BO@CFEAKOHI@SendResJoinResult?5?9?5?$DP?$DP?$DP?5?$DP?$DP?5?$DP?$DP@ DB 'S'
	DB	'endResJoinResult - ??? ?? ??', 00H		; `string'
CONST	ENDS
;	COMDAT ??_7JoinContextNone@mu2@@6B@
CONST	SEGMENT
??_7JoinContextNone@mu2@@6B@ DQ FLAT:??_R4JoinContextNone@mu2@@6B@ ; mu2::JoinContextNone::`vftable'
	DQ	FLAT:??_EJoinContextNone@mu2@@UEAAPEAXI@Z
	DQ	FLAT:?GetContext@JoinContextNone@mu2@@UEBA?AW4Enum@JoinContext@2@XZ
	DQ	FLAT:?SendResJoinResult@JoinContextNone@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z
	DQ	FLAT:?ReqJoinExecutionPrev@JoinContextBase@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@@Z
	DQ	FLAT:?OnKnightageGameReady@JoinContextBase@mu2@@UEBAXAEAPEAVEntityPlayer@2@@Z
	DQ	FLAT:?IsNotifyFriendToClient@JoinContextBase@mu2@@UEBA_NXZ
	DQ	FLAT:?IsSyncMemberAtPartyCheckIn@JoinContextNone@mu2@@UEBA_NXZ
	DQ	FLAT:?IsNotifyAtPartyCheckIn@JoinContextNone@mu2@@UEBA_NXZ
CONST	ENDS
;	COMDAT ??_7JoinContextTutorial@mu2@@6B@
CONST	SEGMENT
??_7JoinContextTutorial@mu2@@6B@ DQ FLAT:??_R4JoinContextTutorial@mu2@@6B@ ; mu2::JoinContextTutorial::`vftable'
	DQ	FLAT:??_EJoinContextTutorial@mu2@@UEAAPEAXI@Z
	DQ	FLAT:?GetContext@JoinContextTutorial@mu2@@UEBA?AW4Enum@JoinContext@2@XZ
	DQ	FLAT:?SendResJoinResult@JoinContextTutorial@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z
	DQ	FLAT:?ReqJoinExecutionPrev@JoinContextTutorial@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@@Z
	DQ	FLAT:?OnKnightageGameReady@JoinContextBase@mu2@@UEBAXAEAPEAVEntityPlayer@2@@Z
	DQ	FLAT:?IsNotifyFriendToClient@JoinContextBase@mu2@@UEBA_NXZ
	DQ	FLAT:?IsSyncMemberAtPartyCheckIn@JoinContextTutorial@mu2@@UEBA_NXZ
	DQ	FLAT:?IsNotifyAtPartyCheckIn@JoinContextTutorial@mu2@@UEBA_NXZ
CONST	ENDS
;	COMDAT ??_7JoinContextLogout@mu2@@6B@
CONST	SEGMENT
??_7JoinContextLogout@mu2@@6B@ DQ FLAT:??_R4JoinContextLogout@mu2@@6B@ ; mu2::JoinContextLogout::`vftable'
	DQ	FLAT:??_EJoinContextLogout@mu2@@UEAAPEAXI@Z
	DQ	FLAT:?GetContext@JoinContextLogout@mu2@@UEBA?AW4Enum@JoinContext@2@XZ
	DQ	FLAT:?SendResJoinResult@JoinContextBase@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z
	DQ	FLAT:?ReqJoinExecutionPrev@JoinContextBase@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@@Z
	DQ	FLAT:?OnKnightageGameReady@JoinContextBase@mu2@@UEBAXAEAPEAVEntityPlayer@2@@Z
	DQ	FLAT:?IsNotifyFriendToClient@JoinContextBase@mu2@@UEBA_NXZ
	DQ	FLAT:?IsSyncMemberAtPartyCheckIn@JoinContextLogout@mu2@@UEBA_NXZ
	DQ	FLAT:?IsNotifyAtPartyCheckIn@JoinContextLogout@mu2@@UEBA_NXZ
CONST	ENDS
;	COMDAT ??_7JoinContextChannelChange@mu2@@6B@
CONST	SEGMENT
??_7JoinContextChannelChange@mu2@@6B@ DQ FLAT:??_R4JoinContextChannelChange@mu2@@6B@ ; mu2::JoinContextChannelChange::`vftable'
	DQ	FLAT:??_EJoinContextChannelChange@mu2@@UEAAPEAXI@Z
	DQ	FLAT:?GetContext@JoinContextChannelChange@mu2@@UEBA?AW4Enum@JoinContext@2@XZ
	DQ	FLAT:?SendResJoinResult@JoinContextChannelChange@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z
	DQ	FLAT:?ReqJoinExecutionPrev@JoinContextChannelChange@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@@Z
	DQ	FLAT:?OnKnightageGameReady@JoinContextBase@mu2@@UEBAXAEAPEAVEntityPlayer@2@@Z
	DQ	FLAT:?IsNotifyFriendToClient@JoinContextBase@mu2@@UEBA_NXZ
	DQ	FLAT:?IsSyncMemberAtPartyCheckIn@JoinContextChannelChange@mu2@@UEBA_NXZ
	DQ	FLAT:?IsNotifyAtPartyCheckIn@JoinContextChannelChange@mu2@@UEBA_NXZ
CONST	ENDS
;	COMDAT ??_7JoinContextMapWarp@mu2@@6B@
CONST	SEGMENT
??_7JoinContextMapWarp@mu2@@6B@ DQ FLAT:??_R4JoinContextMapWarp@mu2@@6B@ ; mu2::JoinContextMapWarp::`vftable'
	DQ	FLAT:??_EJoinContextMapWarp@mu2@@UEAAPEAXI@Z
	DQ	FLAT:?GetContext@JoinContextMapWarp@mu2@@UEBA?AW4Enum@JoinContext@2@XZ
	DQ	FLAT:?SendResJoinResult@JoinContextMapWarp@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z
	DQ	FLAT:?ReqJoinExecutionPrev@JoinContextBase@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@@Z
	DQ	FLAT:?OnKnightageGameReady@JoinContextBase@mu2@@UEBAXAEAPEAVEntityPlayer@2@@Z
	DQ	FLAT:?IsNotifyFriendToClient@JoinContextBase@mu2@@UEBA_NXZ
	DQ	FLAT:?IsSyncMemberAtPartyCheckIn@JoinContextMapWarp@mu2@@UEBA_NXZ
	DQ	FLAT:?IsNotifyAtPartyCheckIn@JoinContextMapWarp@mu2@@UEBA_NXZ
CONST	ENDS
;	COMDAT ??_7JoinContextMapChange@mu2@@6B@
CONST	SEGMENT
??_7JoinContextMapChange@mu2@@6B@ DQ FLAT:??_R4JoinContextMapChange@mu2@@6B@ ; mu2::JoinContextMapChange::`vftable'
	DQ	FLAT:??_EJoinContextMapChange@mu2@@UEAAPEAXI@Z
	DQ	FLAT:?GetContext@JoinContextMapChange@mu2@@UEBA?AW4Enum@JoinContext@2@XZ
	DQ	FLAT:?SendResJoinResult@JoinContextMapChange@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z
	DQ	FLAT:?ReqJoinExecutionPrev@JoinContextBase@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@@Z
	DQ	FLAT:?OnKnightageGameReady@JoinContextBase@mu2@@UEBAXAEAPEAVEntityPlayer@2@@Z
	DQ	FLAT:?IsNotifyFriendToClient@JoinContextBase@mu2@@UEBA_NXZ
	DQ	FLAT:?IsSyncMemberAtPartyCheckIn@JoinContextMapChange@mu2@@UEBA_NXZ
	DQ	FLAT:?IsNotifyAtPartyCheckIn@JoinContextMapChange@mu2@@UEBA_NXZ
CONST	ENDS
;	COMDAT ??_7JoinContextCheckIn@mu2@@6B@
CONST	SEGMENT
??_7JoinContextCheckIn@mu2@@6B@ DQ FLAT:??_R4JoinContextCheckIn@mu2@@6B@ ; mu2::JoinContextCheckIn::`vftable'
	DQ	FLAT:??_EJoinContextCheckIn@mu2@@UEAAPEAXI@Z
	DQ	FLAT:?GetContext@JoinContextCheckIn@mu2@@UEBA?AW4Enum@JoinContext@2@XZ
	DQ	FLAT:?SendResJoinResult@JoinContextCheckIn@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z
	DQ	FLAT:?ReqJoinExecutionPrev@JoinContextCheckIn@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@@Z
	DQ	FLAT:?OnKnightageGameReady@JoinContextCheckIn@mu2@@UEBAXAEAPEAVEntityPlayer@2@@Z
	DQ	FLAT:?IsNotifyFriendToClient@JoinContextCheckIn@mu2@@UEBA_NXZ
	DQ	FLAT:?IsSyncMemberAtPartyCheckIn@JoinContextCheckIn@mu2@@UEBA_NXZ
	DQ	FLAT:?IsNotifyAtPartyCheckIn@JoinContextCheckIn@mu2@@UEBA_NXZ
CONST	ENDS
;	COMDAT ??_7JoinContextBase@mu2@@6B@
CONST	SEGMENT
??_7JoinContextBase@mu2@@6B@ DQ FLAT:??_R4JoinContextBase@mu2@@6B@ ; mu2::JoinContextBase::`vftable'
	DQ	FLAT:??_EJoinContextBase@mu2@@UEAAPEAXI@Z
	DQ	FLAT:_purecall
	DQ	FLAT:?SendResJoinResult@JoinContextBase@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z
	DQ	FLAT:?ReqJoinExecutionPrev@JoinContextBase@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@@Z
	DQ	FLAT:?OnKnightageGameReady@JoinContextBase@mu2@@UEBAXAEAPEAVEntityPlayer@2@@Z
	DQ	FLAT:?IsNotifyFriendToClient@JoinContextBase@mu2@@UEBA_NXZ
	DQ	FLAT:_purecall
	DQ	FLAT:_purecall
CONST	ENDS
;	COMDAT ??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
CONST	SEGMENT
??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@ DB 'g', 00H, 'a', 00H, 'm', 00H, 'e'
	DB	00H, 00H, 00H				; `string'
CONST	ENDS
;	COMDAT ??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
CONST	SEGMENT
??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@ DB '%'
	DB	's> ASSERT - %s, %s(%d)', 00H		; `string'
CONST	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GJoinContextNone@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?IsNotifyAtPartyCheckIn@JoinContextNone@mu2@@UEBA_NXZ DD 010901H
	DD	0e209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?IsSyncMemberAtPartyCheckIn@JoinContextNone@mu2@@UEBA_NXZ DD 010901H
	DD	0e209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?SendResJoinResult@JoinContextNone@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z DD 011801H
	DD	0e218H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GJoinContextTutorial@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GJoinContextLogout@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GJoinContextChannelChange@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GJoinContextMapWarp@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GJoinContextMapChange@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GJoinContextCheckIn@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?UnInit@theManagerJoinContext@mu2@@UEAA_NXZ DD 010901H
	DD	08209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Init@theManagerJoinContext@mu2@@UEAA_NPEAX@Z DD 021101H
	DD	0210111H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GJoinContextBase@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextBase.h
;	COMDAT ??_GJoinContextNone@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GJoinContextNone@mu2@@UEAAPEAXI@Z PROC		; mu2::JoinContextNone::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 21   : 		virtual~JoinContextBase() {};

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7JoinContextBase@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx
  0001c	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00020	83 e0 01	 and	 eax, 1
  00023	85 c0		 test	 eax, eax
  00025	74 10		 je	 SHORT $LN2@scalar
  00027	ba 08 00 00 00	 mov	 edx, 8
  0002c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00031	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00036	90		 npad	 1
$LN2@scalar:
  00037	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003c	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00040	c3		 ret	 0
??_GJoinContextNone@mu2@@UEAAPEAXI@Z ENDP		; mu2::JoinContextNone::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextNone.h
;	COMDAT ?IsNotifyAtPartyCheckIn@JoinContextNone@mu2@@UEBA_NXZ
_TEXT	SEGMENT
hConsole$1 = 96
this$ = 128
?IsNotifyAtPartyCheckIn@JoinContextNone@mu2@@UEBA_NXZ PROC ; mu2::JoinContextNone::IsNotifyAtPartyCheckIn, COMDAT

; 19   : 		virtual Bool IsNotifyAtPartyCheckIn() const override { VERIFY_RETURN(!"IsSyncMemberAtPartyCheckIn - 문맥이 맞지 않음", false); return false; }		

$LN4:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 78	 sub	 rsp, 120		; 00000078H
  00009	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CH@GHLDOJOM@IsSyncMemberAtPartyCheckIn?5?9?5?$DP?$DP@
  00010	48 85 c0	 test	 rax, rax
  00013	0f 84 9d 00 00
	00		 je	 $LN2@IsNotifyAt
  00019	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  0001e	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  00024	48 89 44 24 60	 mov	 QWORD PTR hConsole$1[rsp], rax
  00029	66 ba 0d 00	 mov	 dx, 13
  0002d	48 8b 4c 24 60	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  00032	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  00038	c7 44 24 50 13
	00 00 00	 mov	 DWORD PTR [rsp+80], 19
  00040	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FA@HCBECIPM@F?3?2Release_Branch?2Server?2Develo@
  00047	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  0004c	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CK@DKADEPNB@?$CB?$CCIsSyncMemberAtPartyCheckIn?5?9?5@
  00053	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  00058	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CN@GOBIHNDP@mu2?3?3JoinContextNone?3?3IsNotifyA@
  0005f	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  00064	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  0006b	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  00070	c7 44 24 28 13
	00 00 00	 mov	 DWORD PTR [rsp+40], 19
  00078	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FA@HCBECIPM@F?3?2Release_Branch?2Server?2Develo@
  0007f	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  00084	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CN@GOBIHNDP@mu2?3?3JoinContextNone?3?3IsNotifyA@
  0008b	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  00091	ba 02 00 00 00	 mov	 edx, 2
  00096	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  0009d	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  000a2	66 ba 07 00	 mov	 dx, 7
  000a6	48 8b 4c 24 60	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  000ab	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000b1	90		 npad	 1
  000b2	32 c0		 xor	 al, al
  000b4	eb 02		 jmp	 SHORT $LN1@IsNotifyAt
$LN2@IsNotifyAt:
  000b6	32 c0		 xor	 al, al
$LN1@IsNotifyAt:
  000b8	48 83 c4 78	 add	 rsp, 120		; 00000078H
  000bc	c3		 ret	 0
?IsNotifyAtPartyCheckIn@JoinContextNone@mu2@@UEBA_NXZ ENDP ; mu2::JoinContextNone::IsNotifyAtPartyCheckIn
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextNone.h
;	COMDAT ?IsSyncMemberAtPartyCheckIn@JoinContextNone@mu2@@UEBA_NXZ
_TEXT	SEGMENT
hConsole$1 = 96
this$ = 128
?IsSyncMemberAtPartyCheckIn@JoinContextNone@mu2@@UEBA_NXZ PROC ; mu2::JoinContextNone::IsSyncMemberAtPartyCheckIn, COMDAT

; 18   : 		virtual Bool IsSyncMemberAtPartyCheckIn() const override { VERIFY_RETURN(!"IsSyncMemberAtPartyCheckIn - 문맥이 맞지 않음", false); return false; }

$LN4:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 78	 sub	 rsp, 120		; 00000078H
  00009	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CH@GHLDOJOM@IsSyncMemberAtPartyCheckIn?5?9?5?$DP?$DP@
  00010	48 85 c0	 test	 rax, rax
  00013	0f 84 9d 00 00
	00		 je	 $LN2@IsSyncMemb
  00019	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  0001e	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  00024	48 89 44 24 60	 mov	 QWORD PTR hConsole$1[rsp], rax
  00029	66 ba 0d 00	 mov	 dx, 13
  0002d	48 8b 4c 24 60	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  00032	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  00038	c7 44 24 50 12
	00 00 00	 mov	 DWORD PTR [rsp+80], 18
  00040	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FA@HCBECIPM@F?3?2Release_Branch?2Server?2Develo@
  00047	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  0004c	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CK@DKADEPNB@?$CB?$CCIsSyncMemberAtPartyCheckIn?5?9?5@
  00053	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  00058	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0DB@GDEJEJOP@mu2?3?3JoinContextNone?3?3IsSyncMem@
  0005f	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  00064	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  0006b	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  00070	c7 44 24 28 12
	00 00 00	 mov	 DWORD PTR [rsp+40], 18
  00078	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FA@HCBECIPM@F?3?2Release_Branch?2Server?2Develo@
  0007f	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  00084	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0DB@GDEJEJOP@mu2?3?3JoinContextNone?3?3IsSyncMem@
  0008b	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  00091	ba 02 00 00 00	 mov	 edx, 2
  00096	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  0009d	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  000a2	66 ba 07 00	 mov	 dx, 7
  000a6	48 8b 4c 24 60	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  000ab	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000b1	90		 npad	 1
  000b2	32 c0		 xor	 al, al
  000b4	eb 02		 jmp	 SHORT $LN1@IsSyncMemb
$LN2@IsSyncMemb:
  000b6	32 c0		 xor	 al, al
$LN1@IsSyncMemb:
  000b8	48 83 c4 78	 add	 rsp, 120		; 00000078H
  000bc	c3		 ret	 0
?IsSyncMemberAtPartyCheckIn@JoinContextNone@mu2@@UEBA_NXZ ENDP ; mu2::JoinContextNone::IsSyncMemberAtPartyCheckIn
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextNone.h
;	COMDAT ?SendResJoinResult@JoinContextNone@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z
_TEXT	SEGMENT
hConsole$1 = 96
this$ = 128
__formal$ = 136
__formal$ = 144
__formal$ = 152
?SendResJoinResult@JoinContextNone@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z PROC ; mu2::JoinContextNone::SendResJoinResult, COMDAT

; 17   : 		virtual void SendResJoinResult(EntityPlayer*&, __in JoinZoneContextDest&, ErrorJoin::Error) const override {VERIFY_RETURN(!"SendResJoinResult - 문맥이 맞지 않음", );}

$LN4:
  00000	44 89 4c 24 20	 mov	 DWORD PTR [rsp+32], r9d
  00005	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	48 83 ec 78	 sub	 rsp, 120		; 00000078H
  00018	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BO@CFEAKOHI@SendResJoinResult?5?9?5?$DP?$DP?$DP?5?$DP?$DP?5?$DP?$DP@
  0001f	48 85 c0	 test	 rax, rax
  00022	0f 84 99 00 00
	00		 je	 $LN2@SendResJoi
  00028	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  0002d	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  00033	48 89 44 24 60	 mov	 QWORD PTR hConsole$1[rsp], rax
  00038	66 ba 0d 00	 mov	 dx, 13
  0003c	48 8b 4c 24 60	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  00041	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  00047	c7 44 24 50 11
	00 00 00	 mov	 DWORD PTR [rsp+80], 17
  0004f	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FA@HCBECIPM@F?3?2Release_Branch?2Server?2Develo@
  00056	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  0005b	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CB@LGFOCIKM@?$CB?$CCSendResJoinResult?5?9?5?$DP?$DP?$DP?5?$DP?$DP?5?$DP?$DP@
  00062	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  00067	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CI@FMPKNJOC@mu2?3?3JoinContextNone?3?3SendResJo@
  0006e	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  00073	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  0007a	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  0007f	c7 44 24 28 11
	00 00 00	 mov	 DWORD PTR [rsp+40], 17
  00087	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FA@HCBECIPM@F?3?2Release_Branch?2Server?2Develo@
  0008e	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  00093	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CI@FMPKNJOC@mu2?3?3JoinContextNone?3?3SendResJo@
  0009a	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  000a0	ba 02 00 00 00	 mov	 edx, 2
  000a5	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  000ac	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  000b1	66 ba 07 00	 mov	 dx, 7
  000b5	48 8b 4c 24 60	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  000ba	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000c0	90		 npad	 1
$LN2@SendResJoi:
  000c1	48 83 c4 78	 add	 rsp, 120		; 00000078H
  000c5	c3		 ret	 0
?SendResJoinResult@JoinContextNone@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z ENDP ; mu2::JoinContextNone::SendResJoinResult
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextNone.h
;	COMDAT ?GetContext@JoinContextNone@mu2@@UEBA?AW4Enum@JoinContext@2@XZ
_TEXT	SEGMENT
this$ = 8
?GetContext@JoinContextNone@mu2@@UEBA?AW4Enum@JoinContext@2@XZ PROC ; mu2::JoinContextNone::GetContext, COMDAT

; 16   : 		virtual JoinContext::Enum GetContext() const override { return eJoinContext; }		

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?GetContext@JoinContextNone@mu2@@UEBA?AW4Enum@JoinContext@2@XZ ENDP ; mu2::JoinContextNone::GetContext
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextBase.h
;	COMDAT ??_GJoinContextTutorial@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GJoinContextTutorial@mu2@@UEAAPEAXI@Z PROC		; mu2::JoinContextTutorial::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 21   : 		virtual~JoinContextBase() {};

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7JoinContextBase@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx
  0001c	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00020	83 e0 01	 and	 eax, 1
  00023	85 c0		 test	 eax, eax
  00025	74 10		 je	 SHORT $LN2@scalar
  00027	ba 08 00 00 00	 mov	 edx, 8
  0002c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00031	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00036	90		 npad	 1
$LN2@scalar:
  00037	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003c	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00040	c3		 ret	 0
??_GJoinContextTutorial@mu2@@UEAAPEAXI@Z ENDP		; mu2::JoinContextTutorial::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextTutorial.h
;	COMDAT ?IsNotifyAtPartyCheckIn@JoinContextTutorial@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsNotifyAtPartyCheckIn@JoinContextTutorial@mu2@@UEBA_NXZ PROC ; mu2::JoinContextTutorial::IsNotifyAtPartyCheckIn, COMDAT

; 20   : 		virtual Bool IsNotifyAtPartyCheckIn() const { return true; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	b0 01		 mov	 al, 1
  00007	c3		 ret	 0
?IsNotifyAtPartyCheckIn@JoinContextTutorial@mu2@@UEBA_NXZ ENDP ; mu2::JoinContextTutorial::IsNotifyAtPartyCheckIn
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextTutorial.h
;	COMDAT ?IsSyncMemberAtPartyCheckIn@JoinContextTutorial@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsSyncMemberAtPartyCheckIn@JoinContextTutorial@mu2@@UEBA_NXZ PROC ; mu2::JoinContextTutorial::IsSyncMemberAtPartyCheckIn, COMDAT

; 19   : 		virtual Bool IsSyncMemberAtPartyCheckIn() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsSyncMemberAtPartyCheckIn@JoinContextTutorial@mu2@@UEBA_NXZ ENDP ; mu2::JoinContextTutorial::IsSyncMemberAtPartyCheckIn
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextTutorial.h
;	COMDAT ?GetContext@JoinContextTutorial@mu2@@UEBA?AW4Enum@JoinContext@2@XZ
_TEXT	SEGMENT
this$ = 8
?GetContext@JoinContextTutorial@mu2@@UEBA?AW4Enum@JoinContext@2@XZ PROC ; mu2::JoinContextTutorial::GetContext, COMDAT

; 16   : 		virtual JoinContext::Enum GetContext() const override { return eJoinContext; }		

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	b0 05		 mov	 al, 5
  00007	c3		 ret	 0
?GetContext@JoinContextTutorial@mu2@@UEBA?AW4Enum@JoinContext@2@XZ ENDP ; mu2::JoinContextTutorial::GetContext
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextBase.h
;	COMDAT ??_GJoinContextLogout@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GJoinContextLogout@mu2@@UEAAPEAXI@Z PROC		; mu2::JoinContextLogout::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 21   : 		virtual~JoinContextBase() {};

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7JoinContextBase@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx
  0001c	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00020	83 e0 01	 and	 eax, 1
  00023	85 c0		 test	 eax, eax
  00025	74 10		 je	 SHORT $LN2@scalar
  00027	ba 08 00 00 00	 mov	 edx, 8
  0002c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00031	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00036	90		 npad	 1
$LN2@scalar:
  00037	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003c	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00040	c3		 ret	 0
??_GJoinContextLogout@mu2@@UEAAPEAXI@Z ENDP		; mu2::JoinContextLogout::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextLogout.h
;	COMDAT ?IsNotifyAtPartyCheckIn@JoinContextLogout@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsNotifyAtPartyCheckIn@JoinContextLogout@mu2@@UEBA_NXZ PROC ; mu2::JoinContextLogout::IsNotifyAtPartyCheckIn, COMDAT

; 18   : 		virtual Bool IsNotifyAtPartyCheckIn() const { return true; }		

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	b0 01		 mov	 al, 1
  00007	c3		 ret	 0
?IsNotifyAtPartyCheckIn@JoinContextLogout@mu2@@UEBA_NXZ ENDP ; mu2::JoinContextLogout::IsNotifyAtPartyCheckIn
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextLogout.h
;	COMDAT ?IsSyncMemberAtPartyCheckIn@JoinContextLogout@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsSyncMemberAtPartyCheckIn@JoinContextLogout@mu2@@UEBA_NXZ PROC ; mu2::JoinContextLogout::IsSyncMemberAtPartyCheckIn, COMDAT

; 17   : 		virtual Bool IsSyncMemberAtPartyCheckIn() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsSyncMemberAtPartyCheckIn@JoinContextLogout@mu2@@UEBA_NXZ ENDP ; mu2::JoinContextLogout::IsSyncMemberAtPartyCheckIn
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextLogout.h
;	COMDAT ?GetContext@JoinContextLogout@mu2@@UEBA?AW4Enum@JoinContext@2@XZ
_TEXT	SEGMENT
this$ = 8
?GetContext@JoinContextLogout@mu2@@UEBA?AW4Enum@JoinContext@2@XZ PROC ; mu2::JoinContextLogout::GetContext, COMDAT

; 16   : 		virtual JoinContext::Enum GetContext() const override { return eJoinContext; }		

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	b0 06		 mov	 al, 6
  00007	c3		 ret	 0
?GetContext@JoinContextLogout@mu2@@UEBA?AW4Enum@JoinContext@2@XZ ENDP ; mu2::JoinContextLogout::GetContext
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextBase.h
;	COMDAT ??_GJoinContextChannelChange@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GJoinContextChannelChange@mu2@@UEAAPEAXI@Z PROC	; mu2::JoinContextChannelChange::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 21   : 		virtual~JoinContextBase() {};

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7JoinContextBase@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx
  0001c	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00020	83 e0 01	 and	 eax, 1
  00023	85 c0		 test	 eax, eax
  00025	74 10		 je	 SHORT $LN2@scalar
  00027	ba 08 00 00 00	 mov	 edx, 8
  0002c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00031	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00036	90		 npad	 1
$LN2@scalar:
  00037	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003c	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00040	c3		 ret	 0
??_GJoinContextChannelChange@mu2@@UEAAPEAXI@Z ENDP	; mu2::JoinContextChannelChange::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextChangeChannel.h
;	COMDAT ?IsNotifyAtPartyCheckIn@JoinContextChannelChange@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsNotifyAtPartyCheckIn@JoinContextChannelChange@mu2@@UEBA_NXZ PROC ; mu2::JoinContextChannelChange::IsNotifyAtPartyCheckIn, COMDAT

; 21   : 		virtual Bool IsNotifyAtPartyCheckIn() const { return false; }		

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsNotifyAtPartyCheckIn@JoinContextChannelChange@mu2@@UEBA_NXZ ENDP ; mu2::JoinContextChannelChange::IsNotifyAtPartyCheckIn
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextChangeChannel.h
;	COMDAT ?IsSyncMemberAtPartyCheckIn@JoinContextChannelChange@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsSyncMemberAtPartyCheckIn@JoinContextChannelChange@mu2@@UEBA_NXZ PROC ; mu2::JoinContextChannelChange::IsSyncMemberAtPartyCheckIn, COMDAT

; 20   : 		virtual Bool IsSyncMemberAtPartyCheckIn() const { return true; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	b0 01		 mov	 al, 1
  00007	c3		 ret	 0
?IsSyncMemberAtPartyCheckIn@JoinContextChannelChange@mu2@@UEBA_NXZ ENDP ; mu2::JoinContextChannelChange::IsSyncMemberAtPartyCheckIn
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextChangeChannel.h
;	COMDAT ?GetContext@JoinContextChannelChange@mu2@@UEBA?AW4Enum@JoinContext@2@XZ
_TEXT	SEGMENT
this$ = 8
?GetContext@JoinContextChannelChange@mu2@@UEBA?AW4Enum@JoinContext@2@XZ PROC ; mu2::JoinContextChannelChange::GetContext, COMDAT

; 17   : 		virtual JoinContext::Enum GetContext() const override {return eJoinContext;}		

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	b0 04		 mov	 al, 4
  00007	c3		 ret	 0
?GetContext@JoinContextChannelChange@mu2@@UEBA?AW4Enum@JoinContext@2@XZ ENDP ; mu2::JoinContextChannelChange::GetContext
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextBase.h
;	COMDAT ??_GJoinContextMapWarp@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GJoinContextMapWarp@mu2@@UEAAPEAXI@Z PROC		; mu2::JoinContextMapWarp::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 21   : 		virtual~JoinContextBase() {};

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7JoinContextBase@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx
  0001c	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00020	83 e0 01	 and	 eax, 1
  00023	85 c0		 test	 eax, eax
  00025	74 10		 je	 SHORT $LN2@scalar
  00027	ba 08 00 00 00	 mov	 edx, 8
  0002c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00031	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00036	90		 npad	 1
$LN2@scalar:
  00037	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003c	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00040	c3		 ret	 0
??_GJoinContextMapWarp@mu2@@UEAAPEAXI@Z ENDP		; mu2::JoinContextMapWarp::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextWarpMap.h
;	COMDAT ?IsNotifyAtPartyCheckIn@JoinContextMapWarp@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsNotifyAtPartyCheckIn@JoinContextMapWarp@mu2@@UEBA_NXZ PROC ; mu2::JoinContextMapWarp::IsNotifyAtPartyCheckIn, COMDAT

; 21   : 		virtual Bool IsNotifyAtPartyCheckIn() const { return false; }		

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsNotifyAtPartyCheckIn@JoinContextMapWarp@mu2@@UEBA_NXZ ENDP ; mu2::JoinContextMapWarp::IsNotifyAtPartyCheckIn
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextWarpMap.h
;	COMDAT ?IsSyncMemberAtPartyCheckIn@JoinContextMapWarp@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsSyncMemberAtPartyCheckIn@JoinContextMapWarp@mu2@@UEBA_NXZ PROC ; mu2::JoinContextMapWarp::IsSyncMemberAtPartyCheckIn, COMDAT

; 20   : 		virtual Bool IsSyncMemberAtPartyCheckIn() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsSyncMemberAtPartyCheckIn@JoinContextMapWarp@mu2@@UEBA_NXZ ENDP ; mu2::JoinContextMapWarp::IsSyncMemberAtPartyCheckIn
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextWarpMap.h
;	COMDAT ?GetContext@JoinContextMapWarp@mu2@@UEBA?AW4Enum@JoinContext@2@XZ
_TEXT	SEGMENT
this$ = 8
?GetContext@JoinContextMapWarp@mu2@@UEBA?AW4Enum@JoinContext@2@XZ PROC ; mu2::JoinContextMapWarp::GetContext, COMDAT

; 18   : 		virtual JoinContext::Enum GetContext() const override { return eJoinContext; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	b0 03		 mov	 al, 3
  00007	c3		 ret	 0
?GetContext@JoinContextMapWarp@mu2@@UEBA?AW4Enum@JoinContext@2@XZ ENDP ; mu2::JoinContextMapWarp::GetContext
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextBase.h
;	COMDAT ??_GJoinContextMapChange@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GJoinContextMapChange@mu2@@UEAAPEAXI@Z PROC		; mu2::JoinContextMapChange::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 21   : 		virtual~JoinContextBase() {};

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7JoinContextBase@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx
  0001c	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00020	83 e0 01	 and	 eax, 1
  00023	85 c0		 test	 eax, eax
  00025	74 10		 je	 SHORT $LN2@scalar
  00027	ba 08 00 00 00	 mov	 edx, 8
  0002c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00031	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00036	90		 npad	 1
$LN2@scalar:
  00037	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003c	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00040	c3		 ret	 0
??_GJoinContextMapChange@mu2@@UEAAPEAXI@Z ENDP		; mu2::JoinContextMapChange::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextChangeMap.h
;	COMDAT ?IsNotifyAtPartyCheckIn@JoinContextMapChange@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsNotifyAtPartyCheckIn@JoinContextMapChange@mu2@@UEBA_NXZ PROC ; mu2::JoinContextMapChange::IsNotifyAtPartyCheckIn, COMDAT

; 20   : 		virtual Bool IsNotifyAtPartyCheckIn() const { return true; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	b0 01		 mov	 al, 1
  00007	c3		 ret	 0
?IsNotifyAtPartyCheckIn@JoinContextMapChange@mu2@@UEBA_NXZ ENDP ; mu2::JoinContextMapChange::IsNotifyAtPartyCheckIn
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextChangeMap.h
;	COMDAT ?IsSyncMemberAtPartyCheckIn@JoinContextMapChange@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsSyncMemberAtPartyCheckIn@JoinContextMapChange@mu2@@UEBA_NXZ PROC ; mu2::JoinContextMapChange::IsSyncMemberAtPartyCheckIn, COMDAT

; 19   : 		virtual Bool IsSyncMemberAtPartyCheckIn() const { return true; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	b0 01		 mov	 al, 1
  00007	c3		 ret	 0
?IsSyncMemberAtPartyCheckIn@JoinContextMapChange@mu2@@UEBA_NXZ ENDP ; mu2::JoinContextMapChange::IsSyncMemberAtPartyCheckIn
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextChangeMap.h
;	COMDAT ?GetContext@JoinContextMapChange@mu2@@UEBA?AW4Enum@JoinContext@2@XZ
_TEXT	SEGMENT
this$ = 8
?GetContext@JoinContextMapChange@mu2@@UEBA?AW4Enum@JoinContext@2@XZ PROC ; mu2::JoinContextMapChange::GetContext, COMDAT

; 17   : 		virtual JoinContext::Enum GetContext() const override { return eJoinContext; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	b0 02		 mov	 al, 2
  00007	c3		 ret	 0
?GetContext@JoinContextMapChange@mu2@@UEBA?AW4Enum@JoinContext@2@XZ ENDP ; mu2::JoinContextMapChange::GetContext
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextBase.h
;	COMDAT ??_GJoinContextCheckIn@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GJoinContextCheckIn@mu2@@UEAAPEAXI@Z PROC		; mu2::JoinContextCheckIn::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 21   : 		virtual~JoinContextBase() {};

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7JoinContextBase@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx
  0001c	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00020	83 e0 01	 and	 eax, 1
  00023	85 c0		 test	 eax, eax
  00025	74 10		 je	 SHORT $LN2@scalar
  00027	ba 08 00 00 00	 mov	 edx, 8
  0002c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00031	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00036	90		 npad	 1
$LN2@scalar:
  00037	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003c	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00040	c3		 ret	 0
??_GJoinContextCheckIn@mu2@@UEAAPEAXI@Z ENDP		; mu2::JoinContextCheckIn::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextCheckIn.h
;	COMDAT ?IsNotifyAtPartyCheckIn@JoinContextCheckIn@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsNotifyAtPartyCheckIn@JoinContextCheckIn@mu2@@UEBA_NXZ PROC ; mu2::JoinContextCheckIn::IsNotifyAtPartyCheckIn, COMDAT

; 22   : 		virtual Bool IsNotifyAtPartyCheckIn() const { return true; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	b0 01		 mov	 al, 1
  00007	c3		 ret	 0
?IsNotifyAtPartyCheckIn@JoinContextCheckIn@mu2@@UEBA_NXZ ENDP ; mu2::JoinContextCheckIn::IsNotifyAtPartyCheckIn
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextCheckIn.h
;	COMDAT ?IsSyncMemberAtPartyCheckIn@JoinContextCheckIn@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsSyncMemberAtPartyCheckIn@JoinContextCheckIn@mu2@@UEBA_NXZ PROC ; mu2::JoinContextCheckIn::IsSyncMemberAtPartyCheckIn, COMDAT

; 21   : 		virtual Bool IsSyncMemberAtPartyCheckIn() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsSyncMemberAtPartyCheckIn@JoinContextCheckIn@mu2@@UEBA_NXZ ENDP ; mu2::JoinContextCheckIn::IsSyncMemberAtPartyCheckIn
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextCheckIn.h
;	COMDAT ?IsNotifyFriendToClient@JoinContextCheckIn@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsNotifyFriendToClient@JoinContextCheckIn@mu2@@UEBA_NXZ PROC ; mu2::JoinContextCheckIn::IsNotifyFriendToClient, COMDAT

; 20   : 		virtual Bool IsNotifyFriendToClient() const override { return true; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	b0 01		 mov	 al, 1
  00007	c3		 ret	 0
?IsNotifyFriendToClient@JoinContextCheckIn@mu2@@UEBA_NXZ ENDP ; mu2::JoinContextCheckIn::IsNotifyFriendToClient
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextCheckIn.h
;	COMDAT ?GetContext@JoinContextCheckIn@mu2@@UEBA?AW4Enum@JoinContext@2@XZ
_TEXT	SEGMENT
this$ = 8
?GetContext@JoinContextCheckIn@mu2@@UEBA?AW4Enum@JoinContext@2@XZ PROC ; mu2::JoinContextCheckIn::GetContext, COMDAT

; 16   : 		virtual JoinContext::Enum GetContext() const override { return eJoinContext; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	b0 01		 mov	 al, 1
  00007	c3		 ret	 0
?GetContext@JoinContextCheckIn@mu2@@UEBA?AW4Enum@JoinContext@2@XZ ENDP ; mu2::JoinContextCheckIn::GetContext
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ManagerJoinZoneContext.cpp
;	COMDAT ??AtheManagerJoinContext@mu2@@QEBAPEAVJoinContextBase@1@W4Enum@JoinContext@1@@Z
_TEXT	SEGMENT
this$ = 8
eJoinContext$ = 16
??AtheManagerJoinContext@mu2@@QEBAPEAVJoinContextBase@1@W4Enum@JoinContext@1@@Z PROC ; mu2::theManagerJoinContext::operator[], COMDAT

; 67   : 	{

  00000	88 54 24 10	 mov	 BYTE PTR [rsp+16], dl
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 68   : 		if (eJoinContext < JoinContext::JC_NONE || eJoinContext >= JoinContext::JC_MAX)

  00009	0f b6 44 24 10	 movzx	 eax, BYTE PTR eJoinContext$[rsp]
  0000e	85 c0		 test	 eax, eax
  00010	7c 0a		 jl	 SHORT $LN3@operator
  00012	0f b6 44 24 10	 movzx	 eax, BYTE PTR eJoinContext$[rsp]
  00017	83 f8 07	 cmp	 eax, 7
  0001a	7c 15		 jl	 SHORT $LN2@operator
$LN3@operator:

; 69   : 			return context[JoinContext::JC_NONE];

  0001c	b8 08 00 00 00	 mov	 eax, 8
  00021	48 6b c0 00	 imul	 rax, rax, 0
  00025	48 8b 4c 24 08	 mov	 rcx, QWORD PTR this$[rsp]
  0002a	48 8b 44 01 08	 mov	 rax, QWORD PTR [rcx+rax+8]
  0002f	eb 0f		 jmp	 SHORT $LN1@operator
$LN2@operator:

; 70   : 		return context[eJoinContext];

  00031	0f b6 44 24 10	 movzx	 eax, BYTE PTR eJoinContext$[rsp]
  00036	48 8b 4c 24 08	 mov	 rcx, QWORD PTR this$[rsp]
  0003b	48 8b 44 c1 08	 mov	 rax, QWORD PTR [rcx+rax*8+8]
$LN1@operator:

; 71   : 	}

  00040	c3		 ret	 0
??AtheManagerJoinContext@mu2@@QEBAPEAVJoinContextBase@1@W4Enum@JoinContext@1@@Z ENDP ; mu2::theManagerJoinContext::operator[]
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ManagerJoinZoneContext.cpp
;	COMDAT ?UnInit@theManagerJoinContext@mu2@@UEAA_NXZ
_TEXT	SEGMENT
i$1 = 32
$T2 = 40
tv81 = 48
this$ = 80
?UnInit@theManagerJoinContext@mu2@@UEAA_NXZ PROC	; mu2::theManagerJoinContext::UnInit, COMDAT

; 54   : 	{

$LN9:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 55   : 		for (int i = 0; i < JoinContext::JC_MAX; ++i)

  00009	c7 44 24 20 00
	00 00 00	 mov	 DWORD PTR i$1[rsp], 0
  00011	eb 0a		 jmp	 SHORT $LN4@UnInit
$LN2@UnInit:
  00013	8b 44 24 20	 mov	 eax, DWORD PTR i$1[rsp]
  00017	ff c0		 inc	 eax
  00019	89 44 24 20	 mov	 DWORD PTR i$1[rsp], eax
$LN4@UnInit:
  0001d	83 7c 24 20 07	 cmp	 DWORD PTR i$1[rsp], 7
  00022	7d 67		 jge	 SHORT $LN3@UnInit

; 56   : 		{
; 57   : 			if (context[i])

  00024	48 63 44 24 20	 movsxd	 rax, DWORD PTR i$1[rsp]
  00029	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  0002e	48 83 7c c1 08
	00		 cmp	 QWORD PTR [rcx+rax*8+8], 0
  00034	74 53		 je	 SHORT $LN5@UnInit

; 58   : 			{
; 59   : 				delete context[i];

  00036	48 63 44 24 20	 movsxd	 rax, DWORD PTR i$1[rsp]
  0003b	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  00040	48 8b 44 c1 08	 mov	 rax, QWORD PTR [rcx+rax*8+8]
  00045	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax
  0004a	48 83 7c 24 28
	00		 cmp	 QWORD PTR $T2[rsp], 0
  00050	74 1b		 je	 SHORT $LN7@UnInit
  00052	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
  00057	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0005a	ba 01 00 00 00	 mov	 edx, 1
  0005f	48 8b 4c 24 28	 mov	 rcx, QWORD PTR $T2[rsp]
  00064	ff 10		 call	 QWORD PTR [rax]
  00066	48 89 44 24 30	 mov	 QWORD PTR tv81[rsp], rax
  0006b	eb 09		 jmp	 SHORT $LN8@UnInit
$LN7@UnInit:
  0006d	48 c7 44 24 30
	00 00 00 00	 mov	 QWORD PTR tv81[rsp], 0
$LN8@UnInit:

; 60   : 				context[i] = NULL;

  00076	48 63 44 24 20	 movsxd	 rax, DWORD PTR i$1[rsp]
  0007b	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  00080	48 c7 44 c1 08
	00 00 00 00	 mov	 QWORD PTR [rcx+rax*8+8], 0
$LN5@UnInit:

; 61   : 			}
; 62   : 		}

  00089	eb 88		 jmp	 SHORT $LN2@UnInit
$LN3@UnInit:

; 63   : 		return true;

  0008b	b0 01		 mov	 al, 1

; 64   : 	}

  0008d	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00091	c3		 ret	 0
?UnInit@theManagerJoinContext@mu2@@UEAA_NXZ ENDP	; mu2::theManagerJoinContext::UnInit
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ManagerJoinZoneContext.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextBase.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ManagerJoinZoneContext.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextBase.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ManagerJoinZoneContext.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextBase.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ManagerJoinZoneContext.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextBase.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ManagerJoinZoneContext.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextBase.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ManagerJoinZoneContext.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextBase.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ManagerJoinZoneContext.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextBase.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ManagerJoinZoneContext.cpp
;	COMDAT ?Init@theManagerJoinContext@mu2@@UEAA_NPEAX@Z
_TEXT	SEGMENT
$T1 = 32
$T2 = 40
$T3 = 48
$T4 = 56
$T5 = 64
$T6 = 72
$T7 = 80
tv81 = 88
tv131 = 96
tv149 = 104
tv167 = 112
tv185 = 120
tv203 = 128
tv221 = 136
$T8 = 144
$T9 = 152
$T10 = 160
$T11 = 168
$T12 = 176
$T13 = 184
$T14 = 192
$T15 = 200
$T16 = 208
$T17 = 216
$T18 = 224
$T19 = 232
$T20 = 240
$T21 = 248
this$ = 272
param$ = 280
?Init@theManagerJoinContext@mu2@@UEAA_NPEAX@Z PROC	; mu2::theManagerJoinContext::Init, COMDAT

; 39   : 	{

$LN59:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 81 ec 08 01
	00 00		 sub	 rsp, 264		; 00000108H

; 42   : 		context[JoinContextNone::eJoinContext]			= new JoinContextNone;

  00011	b9 08 00 00 00	 mov	 ecx, 8
  00016	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  0001b	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
  00020	48 83 7c 24 20
	00		 cmp	 QWORD PTR $T1[rsp], 0
  00026	74 3a		 je	 SHORT $LN3@Init
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextBase.h

; 20   : 		JoinContextBase() {};

  00028	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  0002d	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7JoinContextBase@mu2@@6B@
  00034	48 89 08	 mov	 QWORD PTR [rax], rcx
  00037	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  0003c	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7JoinContextNone@mu2@@6B@
  00043	48 89 08	 mov	 QWORD PTR [rax], rcx
  00046	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  0004b	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T8[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ManagerJoinZoneContext.cpp

; 42   : 		context[JoinContextNone::eJoinContext]			= new JoinContextNone;

  00053	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR $T8[rsp]
  0005b	48 89 44 24 58	 mov	 QWORD PTR tv81[rsp], rax
  00060	eb 09		 jmp	 SHORT $LN4@Init
$LN3@Init:
  00062	48 c7 44 24 58
	00 00 00 00	 mov	 QWORD PTR tv81[rsp], 0
$LN4@Init:
  0006b	48 8b 44 24 58	 mov	 rax, QWORD PTR tv81[rsp]
  00070	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax
  00078	b8 08 00 00 00	 mov	 eax, 8
  0007d	48 6b c0 00	 imul	 rax, rax, 0
  00081	48 8b 8c 24 10
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00089	48 8b 94 24 98
	00 00 00	 mov	 rdx, QWORD PTR $T9[rsp]
  00091	48 89 54 01 08	 mov	 QWORD PTR [rcx+rax+8], rdx

; 43   : 		context[JoinContextCheckIn::eJoinContext]		= new JoinContextCheckIn;

  00096	b9 08 00 00 00	 mov	 ecx, 8
  0009b	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  000a0	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax
  000a5	48 83 7c 24 28
	00		 cmp	 QWORD PTR $T2[rsp], 0
  000ab	74 3a		 je	 SHORT $LN5@Init
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextBase.h

; 20   : 		JoinContextBase() {};

  000ad	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
  000b2	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7JoinContextBase@mu2@@6B@
  000b9	48 89 08	 mov	 QWORD PTR [rax], rcx
  000bc	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
  000c1	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7JoinContextCheckIn@mu2@@6B@
  000c8	48 89 08	 mov	 QWORD PTR [rax], rcx
  000cb	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
  000d0	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ManagerJoinZoneContext.cpp

; 43   : 		context[JoinContextCheckIn::eJoinContext]		= new JoinContextCheckIn;

  000d8	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  000e0	48 89 44 24 60	 mov	 QWORD PTR tv131[rsp], rax
  000e5	eb 09		 jmp	 SHORT $LN6@Init
$LN5@Init:
  000e7	48 c7 44 24 60
	00 00 00 00	 mov	 QWORD PTR tv131[rsp], 0
$LN6@Init:
  000f0	48 8b 44 24 60	 mov	 rax, QWORD PTR tv131[rsp]
  000f5	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
  000fd	b8 08 00 00 00	 mov	 eax, 8
  00102	48 6b c0 01	 imul	 rax, rax, 1
  00106	48 8b 8c 24 10
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0010e	48 8b 94 24 a8
	00 00 00	 mov	 rdx, QWORD PTR $T11[rsp]
  00116	48 89 54 01 08	 mov	 QWORD PTR [rcx+rax+8], rdx

; 44   : 		context[JoinContextMapChange::eJoinContext]		= new JoinContextMapChange;

  0011b	b9 08 00 00 00	 mov	 ecx, 8
  00120	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00125	48 89 44 24 30	 mov	 QWORD PTR $T3[rsp], rax
  0012a	48 83 7c 24 30
	00		 cmp	 QWORD PTR $T3[rsp], 0
  00130	74 3a		 je	 SHORT $LN7@Init
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextBase.h

; 20   : 		JoinContextBase() {};

  00132	48 8b 44 24 30	 mov	 rax, QWORD PTR $T3[rsp]
  00137	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7JoinContextBase@mu2@@6B@
  0013e	48 89 08	 mov	 QWORD PTR [rax], rcx
  00141	48 8b 44 24 30	 mov	 rax, QWORD PTR $T3[rsp]
  00146	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7JoinContextMapChange@mu2@@6B@
  0014d	48 89 08	 mov	 QWORD PTR [rax], rcx
  00150	48 8b 44 24 30	 mov	 rax, QWORD PTR $T3[rsp]
  00155	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ManagerJoinZoneContext.cpp

; 44   : 		context[JoinContextMapChange::eJoinContext]		= new JoinContextMapChange;

  0015d	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
  00165	48 89 44 24 68	 mov	 QWORD PTR tv149[rsp], rax
  0016a	eb 09		 jmp	 SHORT $LN8@Init
$LN7@Init:
  0016c	48 c7 44 24 68
	00 00 00 00	 mov	 QWORD PTR tv149[rsp], 0
$LN8@Init:
  00175	48 8b 44 24 68	 mov	 rax, QWORD PTR tv149[rsp]
  0017a	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax
  00182	b8 08 00 00 00	 mov	 eax, 8
  00187	48 6b c0 02	 imul	 rax, rax, 2
  0018b	48 8b 8c 24 10
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00193	48 8b 94 24 b8
	00 00 00	 mov	 rdx, QWORD PTR $T13[rsp]
  0019b	48 89 54 01 08	 mov	 QWORD PTR [rcx+rax+8], rdx

; 45   : 		context[JoinContextMapWarp::eJoinContext]		= new JoinContextMapWarp;

  001a0	b9 08 00 00 00	 mov	 ecx, 8
  001a5	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  001aa	48 89 44 24 38	 mov	 QWORD PTR $T4[rsp], rax
  001af	48 83 7c 24 38
	00		 cmp	 QWORD PTR $T4[rsp], 0
  001b5	74 3a		 je	 SHORT $LN9@Init
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextBase.h

; 20   : 		JoinContextBase() {};

  001b7	48 8b 44 24 38	 mov	 rax, QWORD PTR $T4[rsp]
  001bc	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7JoinContextBase@mu2@@6B@
  001c3	48 89 08	 mov	 QWORD PTR [rax], rcx
  001c6	48 8b 44 24 38	 mov	 rax, QWORD PTR $T4[rsp]
  001cb	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7JoinContextMapWarp@mu2@@6B@
  001d2	48 89 08	 mov	 QWORD PTR [rax], rcx
  001d5	48 8b 44 24 38	 mov	 rax, QWORD PTR $T4[rsp]
  001da	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T14[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ManagerJoinZoneContext.cpp

; 45   : 		context[JoinContextMapWarp::eJoinContext]		= new JoinContextMapWarp;

  001e2	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR $T14[rsp]
  001ea	48 89 44 24 70	 mov	 QWORD PTR tv167[rsp], rax
  001ef	eb 09		 jmp	 SHORT $LN10@Init
$LN9@Init:
  001f1	48 c7 44 24 70
	00 00 00 00	 mov	 QWORD PTR tv167[rsp], 0
$LN10@Init:
  001fa	48 8b 44 24 70	 mov	 rax, QWORD PTR tv167[rsp]
  001ff	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T15[rsp], rax
  00207	b8 08 00 00 00	 mov	 eax, 8
  0020c	48 6b c0 03	 imul	 rax, rax, 3
  00210	48 8b 8c 24 10
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00218	48 8b 94 24 c8
	00 00 00	 mov	 rdx, QWORD PTR $T15[rsp]
  00220	48 89 54 01 08	 mov	 QWORD PTR [rcx+rax+8], rdx

; 46   : 		context[JoinContextChannelChange::eJoinContext] = new JoinContextChannelChange;

  00225	b9 08 00 00 00	 mov	 ecx, 8
  0022a	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  0022f	48 89 44 24 40	 mov	 QWORD PTR $T5[rsp], rax
  00234	48 83 7c 24 40
	00		 cmp	 QWORD PTR $T5[rsp], 0
  0023a	74 3a		 je	 SHORT $LN11@Init
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextBase.h

; 20   : 		JoinContextBase() {};

  0023c	48 8b 44 24 40	 mov	 rax, QWORD PTR $T5[rsp]
  00241	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7JoinContextBase@mu2@@6B@
  00248	48 89 08	 mov	 QWORD PTR [rax], rcx
  0024b	48 8b 44 24 40	 mov	 rax, QWORD PTR $T5[rsp]
  00250	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7JoinContextChannelChange@mu2@@6B@
  00257	48 89 08	 mov	 QWORD PTR [rax], rcx
  0025a	48 8b 44 24 40	 mov	 rax, QWORD PTR $T5[rsp]
  0025f	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR $T16[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ManagerJoinZoneContext.cpp

; 46   : 		context[JoinContextChannelChange::eJoinContext] = new JoinContextChannelChange;

  00267	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR $T16[rsp]
  0026f	48 89 44 24 78	 mov	 QWORD PTR tv185[rsp], rax
  00274	eb 09		 jmp	 SHORT $LN12@Init
$LN11@Init:
  00276	48 c7 44 24 78
	00 00 00 00	 mov	 QWORD PTR tv185[rsp], 0
$LN12@Init:
  0027f	48 8b 44 24 78	 mov	 rax, QWORD PTR tv185[rsp]
  00284	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR $T17[rsp], rax
  0028c	b8 08 00 00 00	 mov	 eax, 8
  00291	48 6b c0 04	 imul	 rax, rax, 4
  00295	48 8b 8c 24 10
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0029d	48 8b 94 24 d8
	00 00 00	 mov	 rdx, QWORD PTR $T17[rsp]
  002a5	48 89 54 01 08	 mov	 QWORD PTR [rcx+rax+8], rdx

; 47   : 		context[JoinContextTutorial::eJoinContext]		= new JoinContextTutorial;

  002aa	b9 08 00 00 00	 mov	 ecx, 8
  002af	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  002b4	48 89 44 24 48	 mov	 QWORD PTR $T6[rsp], rax
  002b9	48 83 7c 24 48
	00		 cmp	 QWORD PTR $T6[rsp], 0
  002bf	74 3d		 je	 SHORT $LN13@Init
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextBase.h

; 20   : 		JoinContextBase() {};

  002c1	48 8b 44 24 48	 mov	 rax, QWORD PTR $T6[rsp]
  002c6	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7JoinContextBase@mu2@@6B@
  002cd	48 89 08	 mov	 QWORD PTR [rax], rcx
  002d0	48 8b 44 24 48	 mov	 rax, QWORD PTR $T6[rsp]
  002d5	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7JoinContextTutorial@mu2@@6B@
  002dc	48 89 08	 mov	 QWORD PTR [rax], rcx
  002df	48 8b 44 24 48	 mov	 rax, QWORD PTR $T6[rsp]
  002e4	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR $T18[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ManagerJoinZoneContext.cpp

; 47   : 		context[JoinContextTutorial::eJoinContext]		= new JoinContextTutorial;

  002ec	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR $T18[rsp]
  002f4	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR tv203[rsp], rax
  002fc	eb 0c		 jmp	 SHORT $LN14@Init
$LN13@Init:
  002fe	48 c7 84 24 80
	00 00 00 00 00
	00 00		 mov	 QWORD PTR tv203[rsp], 0
$LN14@Init:
  0030a	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR tv203[rsp]
  00312	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR $T19[rsp], rax
  0031a	b8 08 00 00 00	 mov	 eax, 8
  0031f	48 6b c0 05	 imul	 rax, rax, 5
  00323	48 8b 8c 24 10
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0032b	48 8b 94 24 e8
	00 00 00	 mov	 rdx, QWORD PTR $T19[rsp]
  00333	48 89 54 01 08	 mov	 QWORD PTR [rcx+rax+8], rdx

; 48   : 		context[JoinContextLogout::eJoinContext]		= new JoinContextLogout;

  00338	b9 08 00 00 00	 mov	 ecx, 8
  0033d	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00342	48 89 44 24 50	 mov	 QWORD PTR $T7[rsp], rax
  00347	48 83 7c 24 50
	00		 cmp	 QWORD PTR $T7[rsp], 0
  0034d	74 3d		 je	 SHORT $LN15@Init
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextBase.h

; 20   : 		JoinContextBase() {};

  0034f	48 8b 44 24 50	 mov	 rax, QWORD PTR $T7[rsp]
  00354	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7JoinContextBase@mu2@@6B@
  0035b	48 89 08	 mov	 QWORD PTR [rax], rcx
  0035e	48 8b 44 24 50	 mov	 rax, QWORD PTR $T7[rsp]
  00363	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7JoinContextLogout@mu2@@6B@
  0036a	48 89 08	 mov	 QWORD PTR [rax], rcx
  0036d	48 8b 44 24 50	 mov	 rax, QWORD PTR $T7[rsp]
  00372	48 89 84 24 f0
	00 00 00	 mov	 QWORD PTR $T20[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ManagerJoinZoneContext.cpp

; 48   : 		context[JoinContextLogout::eJoinContext]		= new JoinContextLogout;

  0037a	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR $T20[rsp]
  00382	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR tv221[rsp], rax
  0038a	eb 0c		 jmp	 SHORT $LN16@Init
$LN15@Init:
  0038c	48 c7 84 24 88
	00 00 00 00 00
	00 00		 mov	 QWORD PTR tv221[rsp], 0
$LN16@Init:
  00398	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR tv221[rsp]
  003a0	48 89 84 24 f8
	00 00 00	 mov	 QWORD PTR $T21[rsp], rax
  003a8	b8 08 00 00 00	 mov	 eax, 8
  003ad	48 6b c0 06	 imul	 rax, rax, 6
  003b1	48 8b 8c 24 10
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  003b9	48 8b 94 24 f8
	00 00 00	 mov	 rdx, QWORD PTR $T21[rsp]
  003c1	48 89 54 01 08	 mov	 QWORD PTR [rcx+rax+8], rdx

; 49   : 
; 50   : 		return true;

  003c6	b0 01		 mov	 al, 1

; 51   : 	}

  003c8	48 81 c4 08 01
	00 00		 add	 rsp, 264		; 00000108H
  003cf	c3		 ret	 0
?Init@theManagerJoinContext@mu2@@UEAA_NPEAX@Z ENDP	; mu2::theManagerJoinContext::Init
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextBase.h
;	COMDAT ??_GJoinContextBase@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GJoinContextBase@mu2@@UEAAPEAXI@Z PROC		; mu2::JoinContextBase::`scalar deleting destructor', COMDAT
$LN10:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 21   : 		virtual~JoinContextBase() {};

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7JoinContextBase@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx
  0001c	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00020	83 e0 01	 and	 eax, 1
  00023	85 c0		 test	 eax, eax
  00025	74 10		 je	 SHORT $LN2@scalar
  00027	ba 08 00 00 00	 mov	 edx, 8
  0002c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00031	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00036	90		 npad	 1
$LN2@scalar:
  00037	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003c	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00040	c3		 ret	 0
??_GJoinContextBase@mu2@@UEAAPEAXI@Z ENDP		; mu2::JoinContextBase::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextBase.h
;	COMDAT ?IsNotifyFriendToClient@JoinContextBase@mu2@@UEBA_NXZ
_TEXT	SEGMENT
this$ = 8
?IsNotifyFriendToClient@JoinContextBase@mu2@@UEBA_NXZ PROC ; mu2::JoinContextBase::IsNotifyFriendToClient, COMDAT

; 26   : 		virtual Bool IsNotifyFriendToClient() const { return false; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	32 c0		 xor	 al, al
  00007	c3		 ret	 0
?IsNotifyFriendToClient@JoinContextBase@mu2@@UEBA_NXZ ENDP ; mu2::JoinContextBase::IsNotifyFriendToClient
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextBase.h
;	COMDAT ?ReqJoinExecutionPrev@JoinContextBase@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@@Z
_TEXT	SEGMENT
this$ = 8
__formal$ = 16
__formal$ = 24
?ReqJoinExecutionPrev@JoinContextBase@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@@Z PROC ; mu2::JoinContextBase::ReqJoinExecutionPrev, COMDAT

; 24   : 		virtual void ReqJoinExecutionPrev(EntityPlayer*&, JoinZoneContextDest&) const {};

  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	c3		 ret	 0
?ReqJoinExecutionPrev@JoinContextBase@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@@Z ENDP ; mu2::JoinContextBase::ReqJoinExecutionPrev
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinZoneContextBase.h
;	COMDAT ?SendResJoinResult@JoinContextBase@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z
_TEXT	SEGMENT
this$ = 8
__formal$ = 16
__formal$ = 24
__formal$ = 32
?SendResJoinResult@JoinContextBase@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z PROC ; mu2::JoinContextBase::SendResJoinResult, COMDAT

; 23   : 		virtual void SendResJoinResult(EntityPlayer*&, __in JoinZoneContextDest&, ErrorJoin::Error) const { }

  00000	44 89 4c 24 20	 mov	 DWORD PTR [rsp+32], r9d
  00005	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	c3		 ret	 0
?SendResJoinResult@JoinContextBase@mu2@@UEBAXAEAPEAVEntityPlayer@2@AEAVJoinZoneContextDest@2@W4Error@ErrorJoin@2@@Z ENDP ; mu2::JoinContextBase::SendResJoinResult
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ManagerJoinZoneContext.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\ManagerJoinZoneContext.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
