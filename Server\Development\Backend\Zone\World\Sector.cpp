﻿#include "stdafx.h"
#include <Backend/Zone/ZoneServer.h>
#include <Backend/Zone/Action/ActionDynamicTagVolume.h>
#include <Backend/Zone/Action/ActionDroppedItem.h>
#include <Backend/Zone/Action/ActionNpcScript.h>
#include <Backend/Zone/Action/ActionNpc.h>
#include <Backend/Zone/Action/ActionMissionHero.h>
#include <Backend/Zone/Action/ActionParty.h>
#include <Backend/Zone/Action/ActionPlayer.h>
#include <Backend/Zone/Action/ActionPlayerInventory.h>
#include <Backend/Zone/Action/ActionMissionDungeon.h>
#include <Backend/Zone/Action/ActionBuff.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerCognition.h>
#include <Backend/Zone/Action/ActionEffectVolume/ActionEffectVolume.h>
#include <Backend/Zone/EntityFactory/NpcEntityFactoryImpl.h>
#include <Backend/Zone/EntityFactory/NpcEntityFactoryImpl.h>
#include <Backend/Zone/World/SectorGrid.h>
#include <Backend/Zone/World/Helper/IRSpawnHelper.h>
#include <Backend/Zone/World/Helper/VolumeSpawnHelper.h>
#include <Backend/Zone/SectorQuestMissionDecoratorManager.h>
#include <Backend/Zone/System/LogicEffectSystem.h>
#include <Backend/Zone/System/LuckyMonsterSpawnSystem.h>
#include <Backend/Zone/Test/TestAiServer.h>
#include <Backend/Zone/Action/ActionPlayerScript.h>
#include <Backend/Zone/World/MissionMap/NetherWorldProcessor.h>
#include <Backend/Zone/World/MissionMap/BloodcastleProcessor.h>
#include <Backend/Zone/World/SectorRunner.h>
#include <Backend/Zone/View/ViewPosition.h>
#include <Backend/Zone/Action/ActionNpcAggro.h>
#include <Backend/Zone/Action/ActionPlayerAggro.h>
#include <Backend/Zone/System/NotifierGameContents.h>
namespace mu2 
{
	
Sector::Sector(SectorRunner* runner, const WorldElem& worldElem)
: m_execZoneId(0)
, m_runner(runner)
, m_runnerId( 0 )
, m_instanceType( InstanceSectorType::NONE )
, m_levelConfig()
, m_name()
, m_seedNumber( 1 )
, m_grid( nullptr )
, m_accumTime(0.0f)
, m_deltaTime(0.0f)
, m_realworldSector( NULL )
, m_pvpEnabled( false )
, m_npcDebug( nullptr )
, m_sectorController( nullptr )
, m_sectorControllerTimer()
, m_remove(false)
, m_removeForced(false)
, m_removeTick(0)
, m_hold_tickEnd(0)
, m_idleTime(0)
, m_missionHeroEntity( nullptr )
, m_battleDistance( Limits::MAX_TARGET_EXTRACT_AND_AGGROLIST_DISTANCE )
, m_endlessStage( 0 )
, m_actionLogGroupKey( 0 )
, m_testAiServer(nullptr)
, m_sectorLuaScript(nullptr)
, m_entryPortalIndex(0)
, m_MonsterCount(0)
, m_isBroadCastSector(false)
, m_dominionKnightageId(0)
, m_dominionId(0)
, m_lodDirector()
, m_worldElem(worldElem)
#ifdef __Renewal_Dungeon_Entrance_Cost_by_jason_20190114
, m_managerEntranceCost(*this)
#endif
{
	if (worldElem.instanceId == 0 )
	{
		CounterInc("SectorChannel");
	}
	else
	{
		CounterInc("SectorInstance");
	}

	m_testAiServer = NEW TestAiServer;
	m_sectorLuaScript = NEW SectorLuaScript;
	m_remainedAsyncJobQueue = 0;
	m_questMissionDecoratorManager = NEW SectorQuestMissionDecoratorManager;
	m_aliveTimer.Reset();

	m_sectorController = static_cast<EntitySectorController*>(theEntityFactory.Create(EntityTypes::SECTOR_CONTROLLER, GetNextSeq(EntityTypes::SECTOR_CONTROLLER)));	
}

Sector::~Sector()
{
	if (GetInstanceId() == 0 )
	{
		CounterDec("SectorChannel");
	}
	else
	{
		CounterDec("SectorInstance");
	}


	Finish();

	if (m_testAiServer)
	{
		delete m_testAiServer;
		m_testAiServer = nullptr;
	}

	if (m_sectorLuaScript)
	{
		delete m_sectorLuaScript;
		m_sectorLuaScript = nullptr;
	}

	if ( m_sectorController )
	{
		delete m_sectorController;
		m_sectorController = nullptr;
	}

	if (m_questMissionDecoratorManager)
	{
		delete m_questMissionDecoratorManager;
		m_questMissionDecoratorManager = nullptr;
	}

}

// 섹터의 생성에 필요한 모든 작업을 여기서 한다. SectorInitializer에 기능 분리	
ErrorJoin::Error Sector::Initialize(	
	UInt32 runnerId,
	const ExecutionCreateInfo& cinfo, 
	const ZoneConfiguration& zoneConfig,
	OUT Bool& isSuccessAddLayer ) 
{	
	VERIFY_RETURN(runnerId, ErrorJoin::InvalidRunnerId);

	isSuccessAddLayer = false;

	m_execZoneId			= cinfo.arrivableExecId;
	m_name					= zoneConfig.levelConfig.name; 
	m_runnerId				= runnerId; 
	m_instanceType			= zoneConfig.instanceType;
	m_levelConfig			= zoneConfig.levelConfig;
	m_entryPortalIndex		= cinfo.entryPortalIndex;
	m_dominionKnightageId	= cinfo.knightageId;
	m_dominionId			= cinfo.dominionId;
	m_creator				= cinfo.creator;

#ifdef __Patch_Knightage_Renewal_by_eunseok_20190128
	m_userVals				= cinfo.userVals;
#endif 

	if( !theSectorGridManager.AddLayer( this, zoneConfig ) )
	{
		MU2_ERROR_LOG( LogCategory::CONTENTS, L"Sector::Initialize > SectorGridManager::AddLayer Failed. exex(%s)", cinfo.arrivableExecId.ToString().c_str());
		return ErrorJoin::AddLayerFailed;
	}
	else
	{
		isSuccessAddLayer = true;
	}

	m_grid = theSectorGridManager.GetSectorGrid( cinfo.arrivableExecId );
	if( nullptr == m_grid )
	{
		MU2_ERROR_LOG( LogCategory::CONTENTS, L"Sector::Initialize > grid is nullptr - exec(%s)", cinfo.arrivableExecId.ToString().c_str());
		return ErrorJoin::GetSectorGridFailed;
	}
	
	m_gameConfig.Setup( cinfo.gameConfigInfos );

	m_actionLogGroupKey = cinfo.insInfo.actionLogGroupKey;

	if( false == initializeBurningStaminaInfo( cinfo.burningStaminaInfos ) )
	{
		return ErrorJoin::initializeBurningStaminaInfoFailed;
	}

	if ( ! initializeRealworld( zoneConfig.levelConfig ) )
	{
		return ErrorJoin::initializeRealworldFailed;
	}

	// 인던 추가 정보 지정
	m_dungeonInfo			= cinfo.insInfo;
	m_pvpMissionmapInfo		= cinfo.pvpMissionmapInfo;


	
	// 그리고 섹터의 루아 스크립트 로딩
	ErrorJoin::Error eError = loadSectorLuaScript(zoneConfig.levelConfig.path);
	if ( ErrorJoin::SUCCESS != eError )
	{	
		return eError;
	}

	// 각종 레벨 관련 데이터 로딩하고 몬스터 스폰까지 진행
	initialzeSpawn( zoneConfig.levelConfig );
		
	// 생성되는 sector에 layer가 적용 되어야 할 경우
	
	if( m_dungeonInfo.layer != 0 )
	{
		GetSectorController().GetControllerAction().SpawnLayerWithLevel(m_dungeonInfo.layer, false, static_cast<Byte>(m_dungeonInfo.level));
	}	

	MU2_INFO_LOG( LogCategory::CONTENTS, "Sector::Initialize> exec(%s)",  m_execZoneId.ToString().c_str());

	initializePerSectorSystems();

	m_testAiServer->Initialize( this );

	m_creationTime = DateTime::GetPresentTime();

	GetEntranceCostManager().GenerateChecker();

	return ErrorJoin::SUCCESS;
}

// tick 처리
Bool Sector::Update(const float& frameDelta)
{
	if( IsHolding() )
	{
		return true;
	}

	m_accumTime += frameDelta;

	beginUpdateSectorSystems();

	processEntities();
	processSectorController();
	updateSectorSystems();
	processStateLog();

	// 이벤트 처리 후, 다른 엔티티 / 시스템들 처리 후 호출해야 함.
	endUpdateSectorSystems();

	m_questMissionDecoratorManager->Update();
	m_testAiServer->Update();
	m_fieldPointSector.Update();

	return true;
}

// 리얼월드 업데이트
void Sector::UpdateRealworld(float frameDelta)
{
	if (m_realWorldUpdateTimer.Elapsed() > 50)
	{
		GetRealworldSector()->Update(frameDelta);

		m_realWorldUpdateTimer.Reset();
	}
}

void Sector::Finish()
{
	cleanup();

	// 항상 남도록 해서 체크 
	if (m_realworldSector != nullptr)
	{
		MU2_INFO_LOG( LogCategory::CONTENTS, "Sector Finish - exec(%s)", m_execZoneId.ToString().c_str() );
	}

	MU2_INFO_LOG(LogCategory::CONTENTS, "Sector Finish - exec(%s)", m_execZoneId.ToString().c_str());
}

void Sector::Nearcast( float x, float y, EventPtr& e )
{
	// XXX: 이렇게 매번 찾기 보다는 엔티티가 알고 Grid를 갖는 버전을 쓰는 게 좋다
	Grid* grid = FindGrid( x,  y );
	if ( nullptr == grid )
	{
		return ;
	}

	NearGrids grids;
	if( ! getNearGridsWithSelf( grid->GetIndexX(), grid->GetIndexY(), grids ) )
	{
		return ;
	}

	if( grids.empty() )
	{
		return ;
	}

	nearCast( grids, e );
}

void Sector::Nearcast( Grid* grid, EventPtr& e )
{
	VERIFY_RETURN(grid, );
	
	NearGrids grids;
	VALID_RETURN(getNearGridsWithSelf(grid->GetIndexX(), grid->GetIndexY(), grids), );
	VALID_RETURN(false == grids.empty(), );

	nearCast( grids, e );
}

void Sector::Broadcast( EventPtr& e )
{
	SectorEntityMap& mapSecoorEntity = m_entities[EntityTypes::PLAYER_ZONE];
	for(auto& itr : mapSecoorEntity )	
	{
		EntityPlayer* player = static_cast<EntityPlayer*>(itr.second);
		VALID_DO(player && player->IsValid(), continue);
		SERVER.SendToClient(player, e );
	}
}

#ifdef __Reincarnation__jason_180628__
void Sector::Broadcast(const SystemMessage& sysmsg)
{
	ENtfGameSystemMessage* evt = NEW ENtfGameSystemMessage;
	evt->msg = sysmsg.GetString();
	Broadcast(EventPtr(evt));
}
#endif

void Sector::NearGridCast ( Grid* grid , EventPtr& e )
{
	VERIFY_RETURN(grid, );

	NearGrids nearGrids;
	VALID_RETURN(GetNearGridsWithSelf(grid->GetIndexX(), grid->GetIndexY(), nearGrids), );
	VALID_RETURN(false == nearGrids.empty(), );

	Bool isDirectEvent = SERVER.IsDirectEvent( e->GetEvent() );
	stRelaySessionKeys relaySessionKeys;
		
	for ( auto* ngrid : nearGrids)
	{	
		VERIFY_DO(ngrid, continue);
		ngrid->DirectSendAndFillRelay( this, e, __out relaySessionKeys, isDirectEvent );
	}

	SERVER.RelayToOtherServer( e, relaySessionKeys );
}

void Sector::NearGridCast ( const Vector3& pos, EventPtr& e )
{
	Grid* grid = FindGrid( pos.x, pos.y );
	VERIFY_RETURN(grid, );
	NearGridCast( grid , e );
}

void Sector::SendLocal(EventPtr& ep)
{
	m_runner->Notify(ep);
}

// 가로/세로 좌표로 그리드를 찾는다.
Grid* Sector::FindGrid( Float x, Float y )
{
	return m_grid->FindGrid( x, y );
}

// 가로/세로 인덱스로 그리드를 찾는다.
Grid* Sector::FindGridWithIndex( GridIdx x, GridIdx y )
{
	return m_grid->FindGridWithIndex( x, y );
}

void Sector::GetWideGridsWithSelf( const Grid* grid, Grids& grids )
{
	return m_grid->GetWideGridsWithSelf( grid, grids );
}

Bool Sector::GetNearGridsWithSelf( const GridIdx gridX, const GridIdx gridY, NearGrids& grids )
{
	return getNearGridsWithSelf( gridX, gridY, grids );
}

Bool Sector::GetGridsWithSelfInRange( const GridIdx gridX, const GridIdx gridY, const UInt32 range, NearGrids& grids )
{
	if ( range >= 8 )
	{
		MU2_ASSERT( !"range值太大" );
		return false;
	}

	if ( range <= 3 && range >= 0 )
	{
		return getNearGridsWithSelf( gridX, gridY, grids );
	}

	return false;
}

void Sector::AddEntity( Entity4Zone* entity )
{
	addEntity(entity);
}

void Sector::RemoveEntity( Entity4Zone* entity )
{
	removeEntity(entity);
}

void Sector::SetMissionHeroEntity(EntityFunctional* pfunctional )
{
	m_missionHeroEntity = pfunctional;
}

void Sector::addEntity( Entity4Zone* entity )
{
	MU2_ASSERT(entity); 
	VALID_RETURN(entity, );

	auto& container = m_entities[entity->GetEntityType()];

	auto iter = container.find(entity->GetId());

	if (iter != container.end())
	{
		MU2_WARN_LOG(
			LogCategory::CONTENTS
			, "Duplicate entity. Type: %d, Id: %d"
			, entity->GetEntityType(), entity->GetId());

		// 즉시 지우고 아래서 교체됨

		//m_remainedAsyncJobQueue++;  // job count 유지

		//RemoveEntityJob(iter->second);
	}

	container.insert( SectorEntityMap::value_type( entity->GetId(), entity ) );


	if (entity->IsNpc())
	{
		EntityNpc* npc = static_cast<EntityNpc*>(entity);

		//AttributeNpc* attr = GetEntityAttribute(entity);
		//if (nullptr == attr || nullptr == attr->scriptElems.npcInfoElem)
		//	return;

		if (npc->IsMonsterType())
			++m_MonsterCount;
	}
}

void Sector::removeEntity( Entity4Zone* entity )
{
	VERIFY_RETURN(entity && entity->IsValid(), );

	if ( m_remove )
	{
		if ( entity->GetEntityType() != EntityTypes::PLAYER_ZONE )
		{
			return;
		}		
	}

	auto& container = m_entities[entity->GetEntityType()];
	auto iter = container.find(entity->GetId());

	if (iter == container.end())
	{
		MU2_WARN_LOG(
			LogCategory::CONTENTS
			, "Non-exist Entity4Zone. Type: %d, Id: %d"
			, entity->GetEntityType(), entity->GetId());

		// 로그만 남김
	}

	entity->Remove();

	if (entity->IsNpc())
	{
		EntityNpc* npc = static_cast<EntityNpc*>(entity);

		if (NpcJobType::IsMonsterType(npc->GetNpcJobType()))
			--m_MonsterCount;

	}

	//switch (entity->GetType())
	//{
	//	case EntityTypes::NPC:
	//	{
	//		AttributeNpc* attr = GetEntityAttribute(entity);
	//		if (nullptr == attr || nullptr == attr->scriptElems.npcInfoElem)
	//			return;

	//		if (NpcJobType::IsMonsterType(attr->scriptElems.npcInfoElem->type))
	//			--m_MonsterCount;
	//	}
	//	break;
	//	default:
	//		break;
	//}

	LAsyncJob->Push( std::bind(&SectorRunner::RemoveEntity, m_runner, m_execZoneId, entity));
	m_remainedAsyncJobQueue++;
}

void Sector::RemoveEntityJob( Entity4Zone* entity )
{
	if ( entity->IsNpc())
	{
		EntityNpc* npc = static_cast<EntityNpc*>(entity);

		npc->GetAction< ActionSkillControl >()->RemoveAllEffectVolume();

		auto actNpc = npc->GetAction< ActionNpc >();
		EntityUnit* master = actNpc->GetMasterUnit();
		if( nullptr != master )
		{
			master->GetSkillControlAction().RemoveServantNpc(npc->GetId() );
		}

		RemoveLeaderNpc(npc);

		if (actNpc->IsLuckyMonster())
		{
			theLuckyMonsterSystem.NotifyDead(npc);
		}

		ActionSector* asector = GetEntityAction(npc);
		if ( nullptr != asector )
		{
			asector->RemoveAgentFromRealworld();
			asector->RemoveFromMyGrid();
		}
	}	
	else if ( entity->IsEffect() )
	{
		EntityEffectVolume* effectVolume = static_cast<EntityEffectVolume*>(entity);

		ActionEffectVolume* aev = GetEntityAction(effectVolume);

		Grid* grid = aev->GetGrid();
		if ( grid )
		{
			ENtfGameRemoveEntity* ntf = NEW ENtfGameRemoveEntity;			
			ntf->removedEntityInfoList.emplace_back(effectVolume->GetId(), false );
			NearGridCast( grid, EventPtr( ntf ) );

			grid->Remove( this, effectVolume);
		}
		else
		{
			MU2_ASSERT( grid != nullptr );
		}
		
		aev->ResetSector();
	}
	else if ( entity->IsTagVolume() )
	{
		EntityDynamicTagVolume* tagVolume = static_cast<EntityDynamicTagVolume*>(entity);

		ActionSector* asector = GetEntityAction(tagVolume);

		ENtfGameRemoveEntity* ntf = NEW ENtfGameRemoveEntity;		
		ntf->removedEntityInfoList.emplace_back(tagVolume->GetId(), false );
		asector->NearGridCast( EventPtr( ntf ) );

		asector->RemoveFromMyGrid();
	}
	else if ( entity->IsPlayer() )
	{
		EntityPlayer* player = static_cast<EntityPlayer*>(entity);
		m_testAiServer->Unsubscribe(player->GetId());
		GetSharedQuestSystem()->LeaveSharedQuest(player);
	}

	m_entities[entity->GetEntityType()].erase( entity->GetId() );

	delete entity;
	entity = nullptr;

	m_remainedAsyncJobQueue--;
}

Bool Sector::PurgeEntityDirect( Entity4Zone* entity )
{
	if ( nullptr == entity )
	{
		return false;
	}

	// 주의: 현재 SectorRunner에서 들어오고 나갈 때만 사용한다. 
	// 다른 곳에서 사용하면 서버가 죽을 수 있다. 

	m_entities[entity->GetEntityType()].erase( entity->GetId() );

	if ( entity->IsNpc() )
	{
		RemoveLeaderNpc(static_cast<EntityNpc*>(entity));
	}

	return true;
}

EntityPlayer* Sector::FindPlayerByCharId( CharId charId )
{
	auto pos = m_entities[EntityTypes::PLAYER_ZONE].find(stEntityId(EntityTypes::PLAYER_ZONE, charId));
	if( m_entities[EntityTypes::PLAYER_ZONE].end() != pos )
	{
		return static_cast<EntityPlayer*>((*pos).second);		
	}

	return NULL;
}

EntityPlayer* Sector::FindPlayerByEntityId(const EntityId& playerEntityId)
{
	return FindPlayerByCharId(stEntityId(playerEntityId).GetSeq());
}

EntityPlayer* Sector::FindPlayerByName( const std::wstring& name)
{
	auto pos = m_entities[EntityTypes::PLAYER_ZONE].begin();

	while ( m_entities[EntityTypes::PLAYER_ZONE].end() != pos )
	{
		EntityPlayer* eachPlayer = static_cast<EntityPlayer*>(pos->second);

		if (StringUtil::CompareWithoutCaseSensitive(name, eachPlayer->GetCharName()) == 0)
		{
			return eachPlayer;
		}

		pos++;
	}

	return nullptr;
}

EntityNpc* Sector::FindLeaderNpc( IndexNpc leaderNpcIndex, IndexSpawnvolume spawnIndex )
{
	auto leadernpcRange = m_leadernpcEntities.equal_range(leaderNpcIndex);

	if( leadernpcRange.first == leadernpcRange.second )
	{
		return nullptr;
	}

	for(auto it = leadernpcRange.first; it != leadernpcRange.second; ++it)
	{
		if( it->second == nullptr )
		{
			continue;
		}

		AttributeNpc* attrLeader = GetEntityAttribute( it->second );
		if( nullptr == attrLeader )
		{
			continue;
		}

		if( attrLeader->GetVolumeSpawnIndex() == spawnIndex )
		{
			EntityNpc*  found = it->second;
			VERIFY_RETURN(found && found->IsNpc(), NULL);
			return static_cast<EntityNpc*>(found);
		}		
	}

	return nullptr;	
}

EntityDynamicTagVolume* Sector::FindDynamicTagVolume( UInt64 dynamicTagVolumeId )
{
	auto pos = m_entities[EntityTypes::DYNAMIC_TAG_VOLUME].begin();

	while( m_entities[EntityTypes::DYNAMIC_TAG_VOLUME].end() != pos )
	{
		EntityDynamicTagVolume* tagVolume = static_cast<EntityDynamicTagVolume*>(pos->second);

		if( tagVolume->GetTagVolumeAction().GetTagVolumeId() == dynamicTagVolumeId )
		{
			return static_cast<EntityDynamicTagVolume*>(pos->second);
		}

		pos++;
	}

	return nullptr;
}

EntityControlVolume* Sector::FindControlVolume(EntityId controlVolumeId)
{	
	VALID_RETURN(controlVolumeId, nullptr);
	Entity4Zone* found = FindEntity(controlVolumeId);
	VALID_RETURN(found && found->IsControlVolume(), nullptr);
	return static_cast<EntityControlVolume*>(found);
}


Bool Sector::RemoveLeaderNpc( EntityNpc* entity )
{
	if( nullptr == entity )
	{
		return false;
	}

	AttributeNpc* attrNpc = GetEntityAttribute( entity );
	if( nullptr == attrNpc )
	{
		return false;
	}

	std::pair<MMapSectorNpc::iterator, MMapSectorNpc::iterator> leadernpcRange;

	leadernpcRange = m_leadernpcEntities.equal_range(attrNpc->GetNpcIndex());

	if( leadernpcRange.first == leadernpcRange.second )
	{
		return nullptr;
	}

	for(auto it = leadernpcRange.first; it != leadernpcRange.second; ++it)
	{
		if( it->second == entity )
		{
			m_leadernpcEntities.erase(it);
			return true;
		}
	}

	return false;;	
}

// 전 npcEntity를 돌면서 내 npcId를 leaderNpcIndex로 갖는 npc를 subordinateId vector에 추가

Bool Sector::AddAllMySubordinateInNearGrid( IndexNpc leaderNpcIndex, UInt32 spawnIndex )
{
	EntityNpc* leader = FindLeaderNpc(leaderNpcIndex, spawnIndex);
	if( nullptr == leader )
	{
		return false;
	}	

	ActionNpc* actionLeaderNpc = GetEntityAction ( leader );
	if( nullptr == actionLeaderNpc )
	{
		return false;
	}

	ViewPosition* myPositionView = GetEntityView( leader );
	MU2_ASSERT( NULL != myPositionView );

	Vector3 myPos;
	myPositionView->GetRealPosition( myPos );

	Grid* grid = FindGrid( myPos.x, myPos.y );
	if( NULL == grid )
	{
		return false;
	}

	NearGrids nearGrids;
	if( ! GetNearGridsWithSelf( grid->GetIndexX(), grid->GetIndexY(), nearGrids ) )
	{
		return false;
	}

	if( nearGrids.empty() )
	{
		return false;
	}
		
	for (Grid* ngrid : nearGrids )
	{
		VERIFY_DO(ngrid, continue);

		VectorNpc vecNpc;
		ngrid->GetAllNpcs(this, vecNpc);

		if( vecNpc.empty() )
			continue;

		for( EntityNpc* npc : vecNpc )
		{
			//AttributeNpc* attrSubordinateNpc = GetEntityAttribute(npc);
			//if( nullptr == attrSubordinateNpc )
			//{
			//	continue;
			//}

			if( npc->GetMyLeaderNpcIndex() == leaderNpcIndex && npc->GetNpcIndex() != leaderNpcIndex )
			{					
				actionLeaderNpc->AddSubordinate(leaderNpcIndex, npc);
			}
		}
	}
	
	return true;
}

Entity4Zone* Sector::FindEntity( const EntityId& entityId )
{
	return findEntity(entityId);
}

EntityUnit*	Sector::FindUnit(const EntityId& entityId)
{
	Entity4Zone* entity = FindEntity(entityId);
	VALID_RETURN(entity && entity->IsUnit() && entity->IsValid(), NULL);
	return static_cast<EntityUnit*>(entity);
}


EntityPlayer* Sector::FindPlayer(const EntityId& entityId)
{
	Entity4Zone* found = FindEntity(entityId);
	VALID_RETURN(found && found->IsValid() && found->IsPlayer(), NULL);
	return static_cast<EntityPlayer*>(found);
}

EntityNpc* Sector::FindNpc(const EntityId& entityId)
{
	Entity4Zone* found = FindEntity(entityId);
	VALID_RETURN(found && found->IsValid() && found->IsNpc(), NULL);
	return static_cast<EntityNpc*>(found);
}

// 셀 목록 관리
Gcell::CellLayer* Sector::GetCellLayer( Gcell::Cell* cell ) const
{
	return addCellLayer( cell );
}

// 이미 사용 중인지 체크해서 실수하지 않도록 하기 위해 
Gcell::CellList& Sector::UseWorkingCellList() const
{
	MU2_ASSERT( false == m_cellInfo.m_usingWorkingCellList );
	if ( m_cellInfo.m_usingWorkingCellList )
	{
		MU2_WARN_LOG( LogCategory::CONTENTS, "[Gcell::useWorkingCellList] Fatal error. WorkingCellList is being used" );
	}

	m_cellInfo.m_workingCellList.clear();
	m_cellInfo.m_usingWorkingCellList = true;
	return m_cellInfo.m_workingCellList;
}

Bool Sector::AddGridLayer( Grid* grid)
{
	MU2_ASSERT( nullptr != grid );

	auto id = GetId();
	if( IsExistGridLayer( grid ) )
	{
		MU2_ERROR_LOG( LogCategory::CONTENTS, L"Grid::addLayer > already exsit layer. exec(%s)", id.ToString().c_str());
		return false;
	}
	
	auto it = m_gridLayers.find( grid );
	if( m_gridLayers.end() != it )
	{
		MU2_ASSERT( nullptr == it->second );
		it->second.reset();
		it->second = std::make_unique< Grid::GridLayer >();
	}
	else
	{
		auto inserted = m_gridLayers.emplace( grid, std::make_unique< Grid::GridLayer >() );
		if( !inserted.second )
		{
			MU2_ERROR_LOG( LogCategory::CONTENTS, L"Grid::addLayer > layer emplace failed. exec(%s)", id.ToString().c_str());
			return false;
		}
	}
	
	return true;
}

Bool Sector::RemoveGridLayer( Grid* grid)
{
	// 어차피 섹터 삭제될 때 같이 삭제 됨.
// 	auto id = GetId();
// 	if( !IsExistGridLayer( grid ) )
// 	{
// 		MU2_ERROR_LOG( LogCategory::CONTENTS, L"Grid::removeLayer > layer not exist. exeuctionid(%u), zoneId(%u)", id.executionKey, id.indexZone );
// 		return false;
// 	}
// 	
// 	auto it = m_gridLayers.find( grid );
// 	MU2_ASSERT( m_gridLayers.end() != it );
// 	it->second.reset();	
	return true;
}

Grid::GridLayer* Sector::GetGridLayer(const Grid* grid) const
{
	MU2_ASSERT( nullptr != grid );

	auto it = m_gridLayers.find( const_cast<Grid*>(grid) );
	if ( it == m_gridLayers.end() )
	{
		assert( m_gridLayers.end() != it );
		return nullptr;
	}
	
	if ( nullptr == it->second )
	{
		assert( nullptr != it->second );
		return nullptr;
	}	
	
	return it->second.get();
}

Bool Sector::IsExistGridLayer( const Grid* grid ) const
{
	auto it = m_gridLayers.find( const_cast<Grid*>( grid ) );
	if ( it != m_gridLayers.end() )
	{
		if ( it->second.get() != nullptr )
		{
			return true;
		}	
	}
	return false;
}

Gcell::CellLayer* Sector::addCellLayer( Gcell::Cell* cell ) const
{
	MU2_ASSERT( nullptr != cell );
	{
		auto it = m_cellLayers.find( cell );
		if ( m_cellLayers.end() != it && nullptr != it->second )
		{
			return it->second.get();
		}
	}

	Gcell::CellLayer* pointer = nullptr;
	{
		auto it = m_cellLayers.find( cell );
		if ( m_cellLayers.end() != it )
		{
			if ( nullptr != it->second )
			{
				return it->second.get();
			}
		}

		if ( m_cellLayers.end() == it )
		{
			auto inserted = m_cellLayers.emplace( cell, std::make_unique<Gcell::CellLayer>() );
			if ( !inserted.second )
			{
				MU2_ERROR_LOG( 
					LogCategory::CONTENTS, 
					L"Gcell::addLayer > layer emplace failed. exec(%s)", 
					GetExecId().ToWstring().c_str() );
				return nullptr;;
			}
			else
			{
#if 0
				MU2_TRACE_LOG( 
					LogCategory::CONTENTS, 
					L"Gcell::addLayer > . Added cell layer. exec(%s), x(%u), y(%u)", 
					GetExecId().ToWstring().c_str(), cell->xIdx, cell->yIdx );
#endif
			}

			it = inserted.first;
		}

		pointer = it->second.get();
		assert( nullptr != pointer );
	}

	pointer->records.reserve( Gcell::ECELL_RECORD_MAX + 1 );
	pointer->records.resize( Gcell::ECELL_RECORD_MAX + 1 );

	std::fill( pointer->records.begin(), pointer->records.end(), 0 );
	return pointer;
}

void Sector::removeCellLayer()
{
// 섹터 삭제될 떄 같이 삭제 됨
// 	for ( UInt32 i = 0; i < m_columnCount * m_rowCount; ++i )
// 	{
// 		Cell& cell = m_cells[i];
// 		auto it = cell.layers.find( id.executionKey );
// 		if ( cell.layers.end() != it )
// 		{
// 			it->second.reset();
// 		}
// 	}
}

Bool Sector::initializePerSectorSystems()
{
	Bool initializeAll = true;

	if ( false == m_sharedQuestSystem.Initialize( this ) )
	{
		MU2_ASSERT( false );
		initializeAll = false;
	}

	if ( false == m_questMissionDecoratorManager->Initialize( this ) )
	{
		MU2_ASSERT( false );
		initializeAll = false;
	}

	m_fieldPointSector.Initialize(this);

	return initializeAll;
}


void Sector::processStateLog()
{
	SIMPLE_TIMER_CHECK( m_logTimer, 1000*60 );

	MU2_INFO_LOG(LogCategory::CONTENTS, "Alive - exec(%s), Players(%d), Npcs(%d), AliveTime(%d s)",
		GetId().ToString().c_str()
		, m_entities[EntityTypes::PLAYER_ZONE].size()
		, m_entities[EntityTypes::NPC].size() 
		, m_aliveTimer.Elapsed() / 1000);
}

void Sector::processEntities()
{
	for (UInt32 type = EntityTypes::NONE; type < EntityTypes::MAX_ENTITY_TYPE; ++type)
	{
		switch (type)
		{
		case EntityTypes::DROPPED_ITEM:
			processDroppedItems();
			break;
		break;
		case EntityTypes::NPC:
			processNpcs();
			break;
		case EntityTypes::CONTROL_VOLUME:
			processControlVolumes();
			break;
		default:
			processEntities(type);
			break;
		}
	}
}

void Sector::processEntities(UInt32 type)
{
	for (const auto itr : m_entities[type])
	{
		Entity4Zone* entity = itr.second;
		if (entity && !entity->isRemove())
		{
			entity->OnTick(m_accumTime);
		}
	}
}

void Sector::processControlVolumes()
{
	PerformanceLodDirector::Lod lod;
	m_lodDirector.GetCurrentLod(PerformanceLodDirector::Id::ControlVolumeUpdate, lod);

	if ( m_contorlTimer.Elapsed() < lod.interval)
	{
		return;
	}

	m_contorlTimer.Reset();

	for (const auto value : m_entities[EntityTypes::CONTROL_VOLUME])
	{
		EntityControlVolume* controlVolume = static_cast<EntityControlVolume*>(value.second);
		if (controlVolume && !controlVolume->isRemove())
		{
			controlVolume->OnTick(m_accumTime);
		}
	}
}

void Sector::processDroppedItems()
{
	PerformanceLodDirector::Lod lod;
	m_lodDirector.GetCurrentLod(PerformanceLodDirector::Id::DropItemUpdate, lod);

	if (m_dropItemTimer.Elapsed() < lod.interval)
	{
		return;
	}

	m_dropItemTimer.Reset();

	for (const auto value : m_entities[EntityTypes::DROPPED_ITEM])
	{
		EntityDropped* dropped = static_cast<EntityDropped*>(value.second);
		if (dropped && !dropped->isRemove())
		{
			dropped->OnTick(m_accumTime);
		}
	}
}

void Sector::processNpcs()
{
	PerformanceLodDirector::Lod lod;
	m_lodDirector.GetCurrentLod(PerformanceLodDirector::Id::NpcUpdate, lod);

	if (m_npcUpdateTimer.Elapsed() < lod.interval)
	{
		return;
	}

	m_npcUpdateTimer.Reset();

	for (const auto value : m_entities[EntityTypes::NPC])
	{
		EntityNpc* npc = static_cast<EntityNpc*>(value.second);
		if (npc && !npc->isRemove())
		{
			npc->OnTick(m_accumTime);
		}
	}
}

Entity4Zone* Sector::findEntity( const EntityId& entityId ) 
{
	stEntityId entity(entityId);
	if (entity.Type == EntityTypes::NONE ||
		entity.Type >= EntityTypes::MAX_ENTITY_TYPE )
	{
		return nullptr;
	}

	auto pos = m_entities[entity.Type].find( entityId );
	if ( m_entities[entity.Type].end() != pos && false == pos->second->isRemove() )
	{
		return pos->second;
	}

	return nullptr;
}

void Sector::cleanup()
{
	for ( auto i = 0; i < EntityTypes::MAX_ENTITY_TYPE; ++i )
	{
		clearEntities( m_entities[i] );
	}

	if ( m_realworldSector != nullptr )
	{
		delete m_realworldSector; 
		m_realworldSector = nullptr;
	}

	m_cellLayers.clear(); 
	m_gridLayers.clear();

	m_questMarkSyncNpcInfos.clear();
}

void Sector::clearEntities(SectorEntityMap& entities)
{
	auto iter = entities.begin();
	auto end = entities.end();
	for (; iter != end; ++iter)
	{
		if (iter->second)
		{
			delete iter->second;
		}
	}
	entities.clear();
}


Bool Sector::getNearGridsWithSelf( const GridIdx indexX, const GridIdx indexY, NearGrids& grids )
{
	Grid* grid = FindGridWithIndex( indexX, indexY );

	if( NULL == grid )
	{
		return false;
	}

	// 인접한 그리드를 
	grid->GetNearGrids(grids);
	return true;
}

Bool Sector::nearCast( NearGrids& grids, EventPtr& e )
{
	Bool isDirectEvent = SERVER.IsDirectEvent( e->GetEvent() );
	stRelaySessionKeys relaySessionKeys;
	
	for ( auto& grid : grids )
	{
		VERIFY_DO( grid, continue);
		grid->DirectSendAndFillRelay( this, e, relaySessionKeys, isDirectEvent);
	}

	SERVER.RelayToOtherServer( e, relaySessionKeys );
	
	return true;
}

// 킵던의 잔몹을 제거 하기 위한 것으로 차후 부모 몹이 죽으면 같이 죽게 만들어야 한다.
void Sector::SetDisabledSpawnVolume(bool setDisabled)
{
	auto it = m_entities[EntityTypes::CONTROL_VOLUME].begin();
	auto end = m_entities[EntityTypes::CONTROL_VOLUME].end();

	for ( ; it != end; ++it )
	{
		if ( (it->second)->HasAttribute( ATTRIBUTE_SPAWN_VOLUME ) == false )
		{
			continue;
		}

		AttributeVolumeSpawn* attr = GetEntityAttribute( it->second );
		attr->disabled = setDisabled;			
	}		
}

void Sector::ResetSpawnVolume( )
{
	auto it = m_entities[EntityTypes::CONTROL_VOLUME].begin();
	auto end = m_entities[EntityTypes::CONTROL_VOLUME].end();

	for ( ; it != end; ++it )
	{
		if ( ( it->second )->HasAttribute( ATTRIBUTE_SPAWN_VOLUME ) == false )
		{
			continue;
		}

		AttributeVolumeSpawn* attr = GetEntityAttribute( it->second );
		if ( attr->elem->disabled )
		{
			attr->disabled = true;
		}
		else
		{
			attr->disabled = false;
		}
		
		attr->totalSpawnCount = 0;
		for ( auto &npcs : attr->npcs )
		{
			npcs.spawnedCount = 0;
			npcs.lastSpawnTime = 0;			
			npcs.spawnedTotalCount = 0;			
			npcs.lastPlayerNumber = 0;
			npcs.deadTimeList.clear();
			npcs.spawnedNpcs.clear();

			for ( UInt32 j = 0; j < npcs.unitCount; ++j )
			{
				npcs.deadTimeList.push_back( 0 );	// 최초 스폰을 위해 시간 조정
			}
		}

		it->second->Activate();
	}
}

void Sector::SetStopNewMission( Bool setNewMission )
{
	auto pos = m_entities[EntityTypes::FUNCTIONAL].begin();
	auto end = m_entities[EntityTypes::FUNCTIONAL].end();

	for ( ; pos != end; ++pos )
	{
		if ( (pos->second)->HasAction( ACTION_MISSION_HERO ) == false )
		{
			continue;
		}

		ActionMissionHero* actMissionHero = GetEntityAction( pos->second );
		actMissionHero->SetIsStopNewMission(setNewMission);

		break;
	}		
}


void Sector::TranDeadAllNpc()
{
	for ( auto& npc : m_entities[EntityTypes::NPC] )
	{
		if ( !npc.second->isRemove() )
		{
			npc.second->GetHsm().Tran(EntityState::STATE_NPC_DEAD );
		}
	}
}

void Sector::RemoveAllSpecificEntityType( EntityTypes::Enum type )
{
	if ( type < EntityTypes::NONE || type >= EntityTypes::MAX_ENTITY_TYPE )
	{
		return;
	}	

	for ( auto it = m_entities[type].begin(); it != m_entities[type].end(); ++it )
	{
		if ( !it->second->isRemove() )
		{
			if ( EntityTypes::DROPPED_ITEM == type )
			{
				ActionDroppedItem* droppedAction = GetEntityAction( it->second );
				if ( nullptr != droppedAction )
				{
					droppedAction->RemoveGrid();
				}
			}
			else
			{
				RemoveEntity( it->second );
			}			
		}								
	}
}

void Sector::GetEntities( UInt32 type, VectorEntity& entities )
{
	std::for_each(m_entities[type].begin(), m_entities[type].end(), 
		[&entities](SectorEntityMap::value_type& entity)
	{ 
		entities.push_back(entity.second); 
	});
}

void Sector::GetUnits(UInt32 type, VectorUnit& vecUnit)
{
	std::for_each(m_entities[type].begin(), m_entities[type].end(),
		[&vecUnit](SectorEntityMap::value_type& entity)
	{
		if (entity.second->IsValid() && entity.second->IsUnit())
		{
			vecUnit.push_back(static_cast<EntityUnit*>(entity.second));
		}
	});
}

void Sector::GetPlayers(VectorPlayer& vecPlayer)
{
	std::for_each(m_entities[EntityTypes::PLAYER_ZONE].begin(), m_entities[EntityTypes::PLAYER_ZONE].end(),
		[&vecPlayer](SectorEntityMap::value_type& entity)
	{
		vecPlayer.push_back( static_cast<EntityPlayer*>(entity.second));
	});
}

void Sector::GetPlayers(VecSessionKey& vecSessionKey)
{
	std::for_each(m_entities[EntityTypes::PLAYER_ZONE].begin(), m_entities[EntityTypes::PLAYER_ZONE].end(),
		[&vecSessionKey](SectorEntityMap::value_type& entity)
	{
		vecSessionKey.push_back(static_cast<EntityPlayer*>(entity.second)->GetClientSessionKey());
	});
}

void Sector::GetPlayers(VectorUnit& vecUnit)
{
	std::for_each(m_entities[EntityTypes::PLAYER_ZONE].begin(), m_entities[EntityTypes::PLAYER_ZONE].end(),
		[&vecUnit](SectorEntityMap::value_type& entity)
	{
		vecUnit.push_back(static_cast<EntityUnit*>(entity.second));
	});
}

void Sector::GetNpcs(VectorNpc& vecNpc)
{
	std::for_each(m_entities[EntityTypes::NPC].begin(), m_entities[EntityTypes::NPC].end(),
		[&vecNpc](SectorEntityMap::value_type& entity)
	{
		vecNpc.push_back(static_cast<EntityNpc*>(entity.second));
	});
}

void Sector::GetPortalNpcs(VectorNpc& vecNpc, IndexPortal indexPortal)
{
	std::for_each(m_entities[EntityTypes::NPC].begin(), m_entities[EntityTypes::NPC].end(),
		[&vecNpc, indexPortal](SectorEntityMap::value_type& entity)
	{
		EntityNpc* npc = static_cast<EntityNpc*>(entity.second);		
		VALID_RETURN(npc && npc->IsValid() && npc->IsOperateNpcType(), );
		VALID_RETURN(npc->GetObjectElem() && npc->GetObjectElem()->portalIndex == indexPortal, );
		vecNpc.push_back(npc);
	});
}
//
//void Sector::GetRankers( VectorEntityId& entitiIdList )
//{
//	std::for_each(m_rankerEntityIds.begin(), m_rankerEntityIds.end(), 
//		[&entitiIdList](VectorEntityId::value_type& entityId)
//	{ entitiIdList.push_back(entityId); });
//}
//
//Bool Sector::AddRankerEntityId( const EntityId& entityId )
//{
//	m_rankerEntityIds.push_back( entityId );
//	return true;
//}
//
//Bool Sector::RemoveRankerEntityId( const EntityId& entityId )
//{
//	auto it = std::find_if( m_rankerEntityIds.begin(), m_rankerEntityIds.end(),
//		[&]( const VectorEntityId::value_type& id ) { return ( id == entityId ); }
//		);
//	if ( it == m_rankerEntityIds.end() )
//	{
//		return false;
//	}
//	m_rankerEntityIds.erase( it );
//	return true;
//}

Bool Sector::CheckNpcSpawn()
{		
	return GetSectorController().GetControllerAction().CheckNpcSpawn();
}

UInt32 Sector::GetCurPlayerCount()
{
	return static_cast<UInt32>( m_entities[EntityTypes::PLAYER_ZONE].size() );
}

UInt32 Sector::GetCurAlivePlayerCount()
{
	UInt32 count = 0;
	for ( auto iter = m_entities[EntityTypes::PLAYER_ZONE].begin(); iter != m_entities[EntityTypes::PLAYER_ZONE].end(); ++iter )
	{
		EntityPlayer* player = static_cast<EntityPlayer*>(iter->second);
		VALID_DO(player && player->IsValid(), continue);
		
		if (player->IsAlive())
		{
			count++;
		}
	}

	return count;
}

UInt32 Sector::GetNotPlayerStateCount(EntityState playerState )
{
	UInt32 count = 0;
	for ( auto iter = m_entities[EntityTypes::PLAYER_ZONE].begin(); iter != m_entities[EntityTypes::PLAYER_ZONE].end(); ++iter )
	{
		EntityPlayer* player = static_cast<EntityPlayer*>(iter->second);
		VALID_DO(player && player->IsValid(), continue);
		
		if ( player->GetCurrStateId() != playerState )
		{
			count++;
		}
				
	}

	return count;
}

UInt32 Sector::GetCurNpcCount()
{
	UInt32 count = 0;
	for ( auto iter = m_entities[EntityTypes::NPC].begin(); iter != m_entities[EntityTypes::NPC].end(); ++iter )
	{
		EntityNpc* npc = static_cast<EntityNpc*>(iter->second);
		VALID_DO(npc && npc->IsValid(), continue);
		
		//AttributeNpc* attr = GetEntityAttribute( npc );
		//if (nullptr == attr || nullptr == attr->scriptElems.npcInfoElem)
		//{
		//	continue;
		//}

		if (npc->IsMonsterType())
		{
			count++;
		}
		
	}

	return count;
}

UInt32 Sector::GetAliveNpcCount()
{
	UInt32 count = 0;
	for ( auto iter = m_entities[EntityTypes::NPC].begin(); iter != m_entities[EntityTypes::NPC].end(); ++iter )
	{
		EntityNpc* npc = static_cast<EntityNpc*>(iter->second);
		VALID_DO(npc && npc->IsValid() && !npc->IsDead(), continue);		
		
		//AttributeNpc& attr = npc->GetNpcAttr();
		//VALID_DO(attr.scriptElems.npcInfoElem, continue);

		if (npc->IsMonsterType())
		{
			count++;
		}
		
	}

	return count;
}

UInt32 Sector::GetCurNpcCount( IndexNpc npcIndex )
{
	UInt32 count = 0;
	for ( auto iter = m_entities[EntityTypes::NPC].begin(); iter != m_entities[EntityTypes::NPC].end(); ++iter )
	{
		EntityNpc* npc = static_cast<EntityNpc*>(iter->second);
		if ( npc )
		{
			if( 0 == npcIndex || npc->GetNpcIndex() == npcIndex )			
			{
				count++;
			}
		}
	}

	return count;
}

UInt32 Sector::GetAliveNpcCount(IndexNpc npcIndex )
{
	UInt32 count = 0;
	for ( auto iter = m_entities[EntityTypes::NPC].begin(); iter != m_entities[EntityTypes::NPC].end(); ++iter )
	{
		EntityNpc* npc = static_cast<EntityNpc*>(iter->second);
		VALID_DO(npc && npc->IsValid() && !npc->IsDead(), continue);
		
		//AttributeNpc& attr = npc->GetNpcAttr();
		//VALID_DO(attr.scriptElems.npcInfoElem, continue);

		if ( npc->GetNpcIndex() == npcIndex && npc->IsMonsterType() )
		{
			//if (NpcJobType::IsMonsterType(attr.scriptElems.npcInfoElem->type))
			//{
				count++;
			//}
		}
		
	}

	return count;
}

UInt32 Sector::GetCurSpawnVolumeNpcCount(IndexSpawnvolume spawnVolumeIndex)
{
	UInt32 count = 0;
	for ( auto iter = m_entities[EntityTypes::NPC].begin(); iter != m_entities[EntityTypes::NPC].end(); ++iter )
	{
		EntityNpc* npc = static_cast<EntityNpc*>(iter->second);
		VALID_DO(npc && npc->IsValid(), continue);
		
		if ( npc->GetVolumeSpawnIndex() == spawnVolumeIndex)
		{
			count++;
		}
		
	}

	return count;
}


UInt32 Sector::GetTeamMemberCount( const TeamType::Enum eTeam ) const
{
	UInt32 count = 0;
	for ( auto iter = m_entities[EntityTypes::PLAYER_ZONE].begin(); iter != m_entities[EntityTypes::PLAYER_ZONE].end(); ++iter )
	{
		EntityPlayer* player = static_cast<EntityPlayer*>(iter->second);
		VALID_DO(player && player->IsValid(), continue);

		if ( player->GetMyTeam() == eTeam )
		{
			count++;
		}
	}

	return count;
}

void Sector::processSectorController()
{
	SIMPLE_TIMER_CHECK( m_sectorControllerTimer, 300 );

	GetSectorController().OnTick(m_accumTime);
}

void Sector::beginUpdateSectorSystems()
{
	m_sharedQuestSystem.BeginUpdate();
}

void Sector::updateSectorSystems()
{
	m_sharedQuestSystem.Update();
}

void Sector::endUpdateSectorSystems()
{
	m_sharedQuestSystem.EndUpdate();
}

//! 삭제 가능 여부
Bool Sector::IsDestroyable()
{
	if ( !m_remove )
	{
		return false;
	}

	// 강제 삭제할 때는 사용자 수 무시함.
	if ( m_removeForced )
	{
		return true;
	}

	if ( m_remainedAsyncJobQueue != 0 )
	{
		MU2_INFO_LOG(LogCategory::CONTENTS, "[Sector][Destroyable][remainjobqueue:%d][playerCount:%d]",
			m_remainedAsyncJobQueue, static_cast<UInt32>(m_entities[EntityTypes::PLAYER_ZONE].size()) );
		return false;
	}
		
	if ( m_entities[EntityTypes::PLAYER_ZONE].empty() )
	{
		return true;
	}
	
	Tick curTick = GetTickCount();
	if (curTick - m_removeTick > Limits::MAX_SECTOR_REMOVE_WAIT_TICK)
	{
		MU2_FATAL_LOG( LogCategory::CONTENTS, "[OVER MAX_SECTOR_REMOVE_WAIT_TICK][exex:%s][playerCount:%d]",
			m_execZoneId.ToString().c_str(),static_cast<UInt32>(m_entities[EntityTypes::PLAYER_ZONE].size()) );
		return true;
	}
		

	return false;
}

void Sector::Remove()
{
	m_remove = true;
	m_removeTick = ::GetTickCount();
}

// 개발용으로만 써주세요. 
void Sector::RemoveForced()
{
	m_remove = true;
	m_removeForced = true;
}

bool Sector::SubsribeTestAi( const EntityId& selfId )
{
	return m_testAiServer->Subscribe(selfId);
}

bool Sector::UnsubscribeTestAi( const EntityId& selfId )
{
	return m_testAiServer->Unsubscribe(selfId);
}


//Bool Sector::CreateAdvCandidateInfo(Entity4Zone* entity, UInt32 partyId, UInt32 portalInfoIndex, UInt32 volumeIndex, UInt32 stempIndex)
//{
//	ActionSector* asector = GetEntityAction( entity );
//	if( nullptr == asector )
//	{
//		MU2_ASSERT( asector != nullptr );
//		return false;
//	}
//
//	if( volumeIndex < 1)
//	{
//		volumeIndex = portalInfoIndex;
//	}	
//
//	if( false == asector->CheckSpecificPlayerInVolume(volumeIndex, entity) )
//	{
//		return false;
//	}
//
//	auto it = m_partyAdvDungeonCandidates.find(partyId);
//
//	if( it != m_partyAdvDungeonCandidates.end() )
//	{
//		auto itPartyAdvDungeonCandidateInfo = it->second.begin();
//		for( ; itPartyAdvDungeonCandidateInfo != it->second.end(); itPartyAdvDungeonCandidateInfo++)
//		{
//			if( itPartyAdvDungeonCandidateInfo->volumeIndex == volumeIndex )				
//			{
//				return false;
//			}
//
//			if(  itPartyAdvDungeonCandidateInfo->candidateUserUnique.end() != itPartyAdvDungeonCandidateInfo->candidateUserUnique.find(entity->GetId()) )
//			{
//				return false;
//			}
//		}
//
//		PartyAdvDungeonCandidateInfo temp;
//		temp.portalInfoIndex = portalInfoIndex;
//		temp.stempIndex = stempIndex;		
//		temp.volumeIndex = volumeIndex;
//		temp.touchUserUnique = entity->GetId();
//		temp.candidateUserUnique.insert(entity->GetId());
//
//		VectorEntity entities;
//		if( asector->GetPlayersInVolume(volumeIndex, entities) )
//		{
//			auto itEntityInVolume = entities.begin();
//			for(; itEntityInVolume != entities.end(); itEntityInVolume++)
//			{
//				ActionPlayerParty* actParty = GetEntityAction( (*itEntityInVolume) );
//				if( actParty != nullptr && actParty->GetPartyId() == partyId)
//				{
//					temp.candidateUserUnique.insert((*itEntityInVolume)->GetId());
//				}				
//			}
//		}		
//
//		it->second.push_back(temp);
//	}
//	else
//	{
//		vecPartyAdvDungeonCandidateInfo vecCandidateInfo;
//
//		PartyAdvDungeonCandidateInfo temp;
//		temp.portalInfoIndex = portalInfoIndex;
//		temp.stempIndex = stempIndex;		
//		temp.volumeIndex = volumeIndex;
//		temp.touchUserUnique = entity->GetId();
//		temp.candidateUserUnique.insert(entity->GetId());
//
//		VectorEntity entities;
//		if( asector->GetPlayersInVolume(volumeIndex, entities) )
//		{
//			auto itEntityInVolume = entities.begin();
//			for(; itEntityInVolume != entities.end(); itEntityInVolume++)
//			{
//				ActionPlayerParty* actParty = GetEntityAction( (*itEntityInVolume) );
//				if( actParty != nullptr && actParty->GetPartyId() == partyId)
//				{
//					temp.candidateUserUnique.insert((*itEntityInVolume)->GetId());
//				}						
//			}
//		}		
//		
//		vecCandidateInfo.push_back(temp);
//
//		m_partyAdvDungeonCandidates.insert(std::make_pair(partyId, vecCandidateInfo));
//	}
//
//	ActionPlayerParty* actParty = GetEntityAction(entity);
//	if( actParty != nullptr )
//	{
//		// volume index 추가
//		actParty->SyncAdvEnterEventVolumeList(volumeIndex, 1);
//	}
//
//	return true;
//}

Bool Sector::CheckAndEraseCandidateInfo(EntityPlayer* player, PartyId partyId)
{
	auto it = m_partyAdvDungeonCandidates.find(partyId);

	if( it != m_partyAdvDungeonCandidates.end() )
	{
		auto itPartyAdvDungeonCandidateInfo = it->second.begin();
		for( ;itPartyAdvDungeonCandidateInfo != it->second.end() ;itPartyAdvDungeonCandidateInfo++)
		{
			if( itPartyAdvDungeonCandidateInfo->candidateUserUnique.empty() )
			{
				ActionPlayerParty* actParty = GetEntityAction(player);
				if( actParty != nullptr )
				{
					// volume index 제거
					actParty->SyncAdvEnterEventVolumeList(itPartyAdvDungeonCandidateInfo->volumeIndex, 2);
				}

				itPartyAdvDungeonCandidateInfo = it->second.erase(itPartyAdvDungeonCandidateInfo);				

				break;
			}						
		}		

		if( it->second.empty() )
		{
			m_partyAdvDungeonCandidates.erase(it);
		}
	}

	return true;
}

Bool Sector::DeleteAdvCandidateInfoByDestroyParty(PartyId partyId)
{
	auto it = m_partyAdvDungeonCandidates.find(partyId);
	if( it != m_partyAdvDungeonCandidates.end() )
	{
		m_partyAdvDungeonCandidates.erase(it);

		return true;
	}

	return false;
}

Bool Sector::DeleteAdvCandidateInfo(EntityPlayer* leaveUser, PartyId partyId)
{
	auto it = m_partyAdvDungeonCandidates.find(partyId);

	if( it != m_partyAdvDungeonCandidates.end() )
	{
		auto itPartyAdvDungeonCandidateInfo = it->second.begin();
		for( ; itPartyAdvDungeonCandidateInfo != it->second.end(); itPartyAdvDungeonCandidateInfo++)
		{
			auto matchUserIt = itPartyAdvDungeonCandidateInfo->candidateUserUnique.find(leaveUser->GetId());
			if( matchUserIt != itPartyAdvDungeonCandidateInfo->candidateUserUnique.end() )
			{
				itPartyAdvDungeonCandidateInfo->candidateUserUnique.erase(matchUserIt);												
				return true;
			}
		}		
	}

	return false;
}


Bool Sector::DeleteAllAdvCandidatesInMyVolume(EntityPlayer* entity, PartyId partyId)
{
	auto it = m_partyAdvDungeonCandidates.find(partyId);

	if( it != m_partyAdvDungeonCandidates.end() )
	{
		auto itPartyAdvDungeonCandidateInfo = it->second.begin();
		for( ; itPartyAdvDungeonCandidateInfo != it->second.end(); itPartyAdvDungeonCandidateInfo++)
		{
			auto matchUserIt = itPartyAdvDungeonCandidateInfo->candidateUserUnique.find(entity->GetId());
			if( matchUserIt != itPartyAdvDungeonCandidateInfo->candidateUserUnique.end() )
			{
				itPartyAdvDungeonCandidateInfo->candidateUserUnique.clear();								
				
				return true;
			}
		}		
	}

	return false;
}

Bool Sector::CheckEnterVolumeByLeaveExecution(EntityPlayer* player, PartyId partyId)
{
	auto it = m_partyAdvDungeonCandidates.find(partyId);

	if( it != m_partyAdvDungeonCandidates.end() )
	{
		auto itPartyAdvDungeonCandidateInfo = it->second.begin();
		for( ; itPartyAdvDungeonCandidateInfo != it->second.end(); itPartyAdvDungeonCandidateInfo++)
		{
			if( itPartyAdvDungeonCandidateInfo->touchUserUnique == player->GetId())
			{
				itPartyAdvDungeonCandidateInfo->candidateUserUnique.clear();								
				return true;
			}
			else
			{
				auto matchUserIt = itPartyAdvDungeonCandidateInfo->candidateUserUnique.find(player->GetId());
				if( matchUserIt != itPartyAdvDungeonCandidateInfo->candidateUserUnique.end() )
				{
					itPartyAdvDungeonCandidateInfo->candidateUserUnique.erase(matchUserIt);		
					return true;
				}
			}			
		}		
	}

	return false;
}

Bool Sector::CheckEnterVolumeByJoinExecution(EntityPlayer* player, PartyId partyId)
{
	ActionSector* asector = GetEntityAction( player );
	if( nullptr == asector )
	{
		MU2_ASSERT( asector != nullptr );
		return false;
	}

	auto it = m_partyAdvDungeonCandidates.find(partyId);

	if( it != m_partyAdvDungeonCandidates.end() )
	{
		auto itPartyAdvDungeonCandidateInfo = it->second.begin();
		for( ; itPartyAdvDungeonCandidateInfo != it->second.end(); itPartyAdvDungeonCandidateInfo++)
		{
			Bool isTrue = asector->CheckSpecificPlayerInVolume(itPartyAdvDungeonCandidateInfo->volumeIndex, player);
			if( isTrue )
			{
				itPartyAdvDungeonCandidateInfo->candidateUserUnique.insert(player->GetId());
				return true;
			}			
		}		
	}

	return false;
}

Bool Sector::ChangeAdvCandidateInfo(EntityPlayer* player, PartyId partyId, UInt32 volumeIndex, UInt32 stempIndex, Bool isInvolume)
{
	auto it = m_partyAdvDungeonCandidates.find(partyId);

	if( it != m_partyAdvDungeonCandidates.end() )
	{
		ActionSector* asector = GetEntityAction( player );
		if( nullptr == asector )
		{
			MU2_ASSERT( asector != nullptr );
			return false;
		}

		auto itPartyAdvDungeonCandidateInfo = it->second.begin();
		for( ; itPartyAdvDungeonCandidateInfo != it->second.end(); itPartyAdvDungeonCandidateInfo++)
		{
			if( itPartyAdvDungeonCandidateInfo->volumeIndex == volumeIndex )
			{
				if( stempIndex != 0 && itPartyAdvDungeonCandidateInfo->touchUserUnique == player->GetId())
				{
					itPartyAdvDungeonCandidateInfo->stempIndex = stempIndex;
				}
				else
				{					
					//Bool result = asector->CheckSpecificPlayerInVolume(volumeIndex, entity, addRadiusValue);
					if( isInvolume )
					{
						itPartyAdvDungeonCandidateInfo->candidateUserUnique.insert(player->GetId());
					}
					else
					{
						itPartyAdvDungeonCandidateInfo->candidateUserUnique.erase(player->GetId());
					}

					if( itPartyAdvDungeonCandidateInfo->touchUserUnique == player->GetId())
					{
						itPartyAdvDungeonCandidateInfo->stempIndex = stempIndex;
					}
				}				

				return true;
			}
		}
	}

	return false;
}

// 외부에서 영웅 미션을 실행함.
Bool Sector::StartHeroMission( UInt32 missionId )
{
	auto pos = m_entities[EntityTypes::FUNCTIONAL].begin();
	auto end = m_entities[EntityTypes::FUNCTIONAL].end();

	for ( ; pos != end; ++pos )
	{
		if ( (pos->second)->HasAction( ACTION_MISSION_HERO ) == false )
		{
			continue;
		}

		ActionMissionHero* actMissionHero = GetEntityAction( pos->second );
		return actMissionHero->StartHeroMission( missionId );
	}		

	return false;
}

void Sector::SetHold( UInt32 time_second )
{
	m_hold_tickEnd = ::GetTickCount() + ( time_second * 1000 );
}

Bool Sector::IsHolding()
{
	if( m_hold_tickEnd == 0)
	{
		return false;
	}

	if( m_hold_tickEnd > ::GetTickCount() )
	{
		return true;
	}
	
	m_hold_tickEnd = 0;
	
	ENtfGameEndPlayScene* ntf = NEW ENtfGameEndPlayScene;
	Broadcast( EventPtr( ntf ) );

	return false;
}

IndexPortal Sector::GetMissionVolumeNpcPortalIndex( UInt32 missionId )
{
	auto pos = m_entities[EntityTypes::FUNCTIONAL].begin();
	auto end = m_entities[EntityTypes::FUNCTIONAL].end();

	for ( ; pos != end; ++pos )
	{
		if ( (pos->second)->HasAction( ACTION_MISSION_HERO ) == false )
		{
			continue;
		}

		ActionMissionHero* actMissionHero = GetEntityAction( pos->second );
		return actMissionHero->GetMissionVolumeNpcPortalIndex( missionId );
	}		

	return 0;
}

void Sector::UpdateCurrentFrameRate(float frameRate)
{
	m_lodDirector.Update(frameRate);
}

const BurningStaminaZoneServerInfo*	Sector::getBurningStaminaInfo(IndexContinent continent ) const
{
	auto iter = m_burningStaminaInfos.find( continent );
	return m_burningStaminaInfos.end() == iter ? nullptr : &iter->second;
}

BurningStaminaZoneServerInfo*		Sector::getBurningStaminaInfo(IndexContinent continent )
{
	auto iter = m_burningStaminaInfos.find( continent );
	return m_burningStaminaInfos.end() == iter ? nullptr : &iter->second;
}

void Sector::ApplyBurningStaminaEffect( const BurningStaminaInstanceDungeonInfo& info )
{
	VectorPlayer vecPlayer;
	GetPlayers(vecPlayer );

	for( auto& i : vecPlayer )
	{
		ActionBuff* actBuff = GetEntityAction( i );
		MU2_ASSERT( nullptr != actBuff );

		actBuff->ChangeZoneBuffCast( info.explorerBuffIndex );
		actBuff->BurningStaminaAwakenBuffCast();
	}
}

void Sector::EnableBurningStamina( const BurningStaminaZoneServerInfo& info )
{
	auto infoPtr = getBurningStaminaInfo( info.continent );
	if( nullptr == infoPtr )
	{
		MU2_ASSERT(0);
		MU2_ERROR_LOG( LogCategory::DEBUG_BURNING_STAMINA, "Sector::EnableBurningStamina > cant find info. continent(%u)", info.continent );
		return;
	}

	*infoPtr = info;

	theNotifierGameContents.OnBurningStamina_Enable(info.continent);
}

void Sector::DisableBurningStamina(IndexContinent continent )
{
	auto found = getBurningStaminaInfo( continent );
	if( nullptr == found )
	{
		MU2_ASSERT(0);
		MU2_ERROR_LOG( LogCategory::DEBUG_BURNING_STAMINA, "Sector::DisableBurningStamina > cant find info. continent(%u)", continent );
		return;
	}

	EntityPlayer* awakenChar = FindPlayerByCharId(found->awakenCharId);
	if (nullptr != awakenChar)
	{
		// 다른 대륙에도 폭주를 시켰는지 체크
		Bool awakenOtherContinent = false;
		for (const auto& it : m_burningStaminaInfos)
		{
			if (it.second.continent != found->continent &&
				it.second.awakenCharId == found->awakenCharId)
			{
				awakenOtherContinent = true;
				break;
			}
		}

		// 다른 지역에 폭주를 시키지 않았으면 버프를 해제한다.
		if (!awakenOtherContinent)
		{
			ActionBuff* actBuff = GetEntityAction(awakenChar);
			if (nullptr != actBuff)
			{
				actBuff->CancelBurningStaminaAwakenFieldBuff();
			}
		}
	}
	found->Disable();

	theNotifierGameContents.OnBurningStamina_Disable(continent);
}

UInt32	Sector::GetAdvBonusFloor(IndexContinent continent ) const
{
	VALID_RETURN(continent, 0 );

	auto info = getBurningStaminaInfo( continent );
	if( nullptr == info )
	{
		MU2_ASSERT(0);
		MU2_ERROR_LOG( LogCategory::DEBUG_BURNING_STAMINA, "Sector::GetAdvBonusFloor > cant find info. continent(%u)", continent);
		return 0;
	}

	return info->advBonusFloor;
}

Bool	Sector::IsAdvStaminaBuff(IndexContinent continent ) const
{
	VALID_RETURN(continent, false );

	auto info = getBurningStaminaInfo( continent );
	VERIFY_RETURN(info, false);

	return info->isStaminaBuff;
}

UInt32	Sector::GetBurningStaminaBuff( ) const
{
	return m_dungeonInfo.burningStaminaInfo.explorerBuffIndex;
}

CharId	Sector::GetBurningStaminaAwakenCharId() const
{
	return m_dungeonInfo.burningStaminaInfo.awakenCharId;
}

UInt32	Sector::GetBurningStaminaAwakenBuff() const
{
	return m_dungeonInfo.burningStaminaInfo.awakenBuffIndex;
}

UInt32	Sector::GetBurningAdvCardBuff() const
{
	return m_dungeonInfo.burningStaminaInfo.advCardBuff;
}

void Sector::UpdateBurningStaminaInstanceDungeonInfo( const BurningStaminaInstanceDungeonInfo& info )
{
	m_dungeonInfo.burningStaminaInfo = info;
	ApplyBurningStaminaEffect( info );
}

Bool Sector::IsAllMonsterTypeNpcDead() const
{
	Bool spawnNpcAlive = false;

	const auto& map = m_entities[EntityTypes::NPC];
	for ( const auto& v : map)
	{
		EntityNpc* npc = static_cast<EntityNpc*>(v.second);
		VALID_DO(npc && npc->IsValid(), continue);

		ActionNpc& actNpc = npc->GetNpcAction();

		EntityUnit* master = actNpc.GetMasterUnit();
		if ( master && master->IsPlayer() )
		{
			continue;
		}

		if ( npc->IsMonsterType() == true )
		{
			if ( !npc->IsDead() )
			{
				spawnNpcAlive = true;
				break;
			}
		}
	}

	return ( !spawnNpcAlive );
}

Bool Sector::IsAllNpcSpawned() const
{
	Bool spawnNotCompleted = false;

	const auto& map = m_entities[EntityTypes::CONTROL_VOLUME];
	for ( const auto& v : map )
	{
		EntityControlVolume* controlVolume = static_cast<EntityControlVolume*>(v.second);
		VALID_DO(controlVolume && controlVolume->IsValid(), continue);
		
		if ( controlVolume->HasAttribute( ATTRIBUTE_SPAWN_VOLUME ) == false )
		{
			continue;
		}

		auto attr = controlVolume->GetAttribute<AttributeVolumeSpawn>();
		const Int32 layer = attr->elem->layer;
		if ( layer != 0 )
		{
			if ( !attr->disabled )
			{
				spawnNotCompleted = true;
				break;
			}
		}
	}
	
	return ( !spawnNotCompleted );
}

UInt32	Sector::GetPassTime() const
{
	ActionSectorController& asc = GetSectorController().GetControllerAction();

	if ( nullptr == asc.GetSectorProcess() )
	{
		DateTime cur = DateTime::GetPresentTime();
		MU2_ASSERT( m_creationTime <= cur );

		auto diff = cur - m_creationTime;
		return static_cast<UInt32>( diff.GetTotalSeconds() );
	}

	SectorProcessor* sectorProcessor = asc.GetSectorProcess();
	switch ( sectorProcessor->GetType() )
	{
	case SectorProcessorType::ENDLESS_TOWER:
	case SectorProcessorType::BLOOD_CASTLE:
	{
		return sectorProcessor->GetClearTimeSecond();
	}
	break;
	default:
	{
		DateTime cur = DateTime::GetPresentTime();
		MU2_ASSERT( m_creationTime <= cur );

		auto diff = cur - m_creationTime;
		return static_cast<UInt32>( diff.GetTotalSeconds() );
	}
	break;
	}
}

UInt32	Sector::GetGameConfigValue( GameConfigType type ) const
{
	return m_gameConfig.GetValue( type );
}

UInt64	Sector::GetGameConfigValue64(GameConfigType type) const
{
	return m_gameConfig.GetValue64(type);
}

void	Sector::UpdateGameConcig( const GameConfigInfoList& infos )
{
	m_gameConfig.Setup( infos );
}

// 섹터의 루아 VM을 돌려줌
lua_State* Sector::GetLuaState()
{
	return m_sectorLuaScript->GetLuaState();
}

UInt32	Sector::GetSectorFixedCost() const
{
	return SERVER.GetSectorCost( m_execZoneId.GetZoneIndex());
}
void Sector::LuaReload()
{
	m_sectorLuaScript->Reload();

	GetSectorController().GetControllerAction().Setup(this);

	auto Npc_itr = m_entities[EntityTypes::NPC].begin();
	auto Npc_end = m_entities[EntityTypes::NPC].end();
	for (; Npc_itr != Npc_end; ++Npc_itr)
	{
		EntityNpc* npc = static_cast<EntityNpc*>(Npc_itr->second);		
		npc->GetScriptAction().Reload();
	}

	auto User_itr = m_entities[EntityTypes::PLAYER_ZONE].begin();
	auto User_end = m_entities[EntityTypes::PLAYER_ZONE].end();
	for (; User_itr != User_end; ++User_itr)
	{
		EntityPlayer* player = static_cast<EntityPlayer*>(User_itr->second);
		player->GetScriptAction().Reload();
		
	}
	MU2_TRACE_LOG(LogCategory::CONTENTS, "Sector(%d) LuaScript Reload Complete!", m_execZoneId.GetZoneIndex());;

}

UInt32 Sector::GetBurningStaminaFieldBuffIndex(CharId charId)
{
	for (auto it : m_burningStaminaInfos)
	{
		if (it.second.awakenCharId == charId)
		{
			return it.second.fieldBuffId;
		}
	}

	return 0;
}

void Sector::CancelTheFirstClassRankingBuff( CharId charId, IndexSkill skillIndex )
{
	EntityPlayer* player = FindPlayerByCharId( charId );
	if (nullptr != player)
	{
		theLogicEffectSystem.OnCancelBuffCast( player, skillIndex );
	}
}

void Sector::FireTheFirstClassRankingBuff( CharId charId, IndexSkill skillIndex )
{
	EntityPlayer* player = FindPlayerByCharId( charId );
	if (nullptr != player)
	{
		theLogicEffectSystem.OnSingleCast( player, skillIndex );
	}

}

Bool Sector::IsMythologyDifficult()
{
	VALID_RETURN(GetEntryPortalIndex(), false);

	auto portalElem = SCRIPTS.GetPortalInfoScript(GetEntryPortalIndex());
	VALID_RETURN(portalElem, false);

	const WorldElem* elem = SCRIPTS.GetScript<WorldScript>()->Get(GetId().GetZoneIndex());
	VALID_RETURN(elem, false);

	Byte gradeLevel = DungeonGradeLevel::GetDungeonGradeLevel(elem->dungeonDifficultType, portalElem->mythology, m_dungeonInfo.dungeonDifficultyLevel);
	if (gradeLevel != DungeonGradeLevel::MYTHOLOGY)
		return false;

	return true;
}
Bool Sector::isMaxMythologyDifficult()
{
	const WorldElem* worldElem = SCRIPTS.GetWorldScript(m_dungeonInfo.indexZone);
	VERIFY_RETURN(worldElem, false);

	if (4 == worldElem->dungeonDifficultType)
	{
		VALID_RETURN(DungeonDifficultyLevelType::MAX_LEVEL70_MYTHOLOGY_LEVEL <= m_dungeonInfo.dungeonDifficultyLevel, false);
	}
	else
	{
		VALID_RETURN(DungeonDifficultyLevelType::MAX_MYTHOLOGY_LEVEL <= m_dungeonInfo.dungeonDifficultyLevel, false);
	}

	return true;
}

UInt8 Sector::GetNetherWorldStage()
{
	ActionSectorController& asc = GetSectorController().GetControllerAction();

	if ( nullptr == asc.GetSectorProcess() )
		return 0;

	SectorProcessor* sectorProcessor = asc.GetSectorProcess();
	if ( SectorProcessorType::Nether_World == sectorProcessor->GetType() )
	{
		NetherWorldProcessor* netherWorldProcessor = static_cast<NetherWorldProcessor*>(sectorProcessor);
		return netherWorldProcessor->GetStage();
	}

	return 0;
}

Bool Sector::IsFirstCheckinGroup() const
{
	if ( GetExecId().GetUnique() != GetInstanceGroupKey())
		return false;

	return GetInstanceDungeonInfo().elapsedTime == 0;
}

Bool Sector::IsValid() const
{
	VERIFY_RETURN(m_execZoneId.IsValidStong() && m_runnerId && m_runner && m_grid, false);
	return true;
}

UInt32 Sector::GetEntityCount(EntityTypes::Enum entityType)
{
	return static_cast<UInt32>(m_entities[entityType].size());
}

// 이 섹터의 적합한 인구. zone.csv
UInt32 Sector::GetBasePopulation() const
{
	return 500;
	// TODO: 이 작업을 하기 전에 현재 용어부터 복잡한 Zone / Sector / 인던 / 채널 
	// 구조를 명확하게 한 다음에 진행한다. 
}

Bool Sector::IsMaxEpicDifficult() const
{
	VALID_RETURN(GetEntryPortalIndex(), false);

	auto portalElem = SCRIPTS.GetPortalInfoScript(GetEntryPortalIndex());
	VALID_RETURN(portalElem, false);

	auto worldElem = SCRIPTS.GetWorldScript(GetId().GetZoneIndex());
	VALID_RETURN(worldElem, false);

	auto gradeLevel = DungeonGradeLevel::GetDungeonGradeLevel(worldElem->dungeonDifficultType, portalElem->mythology, m_dungeonInfo.dungeonDifficultyLevel);
	VALID_RETURN(DungeonGradeLevel::EPIC == gradeLevel, false);

	if (5 == worldElem->dungeonDifficultType)
	{
		VALID_RETURN(DungeonDifficultyLevelType::MAX_LEVEL70_EPIC_LEVEL <= m_dungeonInfo.dungeonDifficultyLevel, false);
	}
	else
	{
		VALID_RETURN(DungeonDifficultyLevelType::MAX_EPIC_LEVEL <= m_dungeonInfo.dungeonDifficultyLevel, false);
	}

	return true;
}


void Sector::EraseEntityAggro(EntityUnit* pUnit)
{
	VERIFY_RETURN(pUnit, );

	// 모든 ENtity를 가지고 와서 해당 캐릭터의 어그로를 삭제한다.

	SectorEntityMap& npcs = m_entities[EntityTypes::NPC];
	SectorEntityMap& players = m_entities[EntityTypes::PLAYER_ZONE];

	for (auto& itr : npcs)
	{
		EntityNpc* npc = static_cast<EntityNpc*>(itr.second);
		VALID_DO(npc && npc->IsValid(), continue);

		ActionNpcAggro* npcAggro = GetEntityAction(npc);
		VALID_DO(npcAggro, continue);
		if (pUnit->GetId() == npc->GetId())
		{
			npcAggro->ClearAggro();
		}
		else
		{
			npcAggro->EraseEntityAggro(pUnit->GetId());
		}
	}

	for (auto& itr : players)
	{
		EntityPlayer* player = static_cast<EntityPlayer*>(itr.second);
		VALID_DO(player && player->IsValid(), continue);

		ActionPlayerAggro* playerAggro = GetEntityAction(player);
		VALID_DO(playerAggro, continue);
		if (pUnit->GetId() == player->GetId())
		{
			playerAggro->ClearAggro();
		}
		else
		{
			playerAggro->EraseEntityAggro(pUnit->GetId());
		}
		
	}

}

void Sector::AddQuestMarkSyncInfo(IndexNpc indexNpc, bool disable)
{
	VERIFY_RETURN(indexNpc, );

	auto itr = m_questMarkSyncNpcInfos.find(indexNpc);
	if(m_questMarkSyncNpcInfos.end() == itr)
	{
		m_questMarkSyncNpcInfos.insert(std::make_pair(indexNpc, disable));
	}
}

bool Sector::CheckQuestMarkSyncNpc(IndexNpc indexNpc)
{
	auto itr = m_questMarkSyncNpcInfos.find(indexNpc);
	if(m_questMarkSyncNpcInfos.end() != itr)
	{
		return true;
	}

	return false;
}

void Sector::ChangeQuestMarkSyncInfo(IndexNpc indexNpc, bool disable)
{
	auto itr = m_questMarkSyncNpcInfos.find(indexNpc);
	if(m_questMarkSyncNpcInfos.end() != itr)
	{
		itr->second = disable;
	}
}

void Sector::GetQuestMarkSyncInfos(ENtfQuestMarkSyncInfo::syncNpcInfos& infos)
{	
	for (auto& itr : m_questMarkSyncNpcInfos)
	{
		if(true == itr.second) // 퀘스트 마크를 끄는 것만 알려주면 됨
		{
			ENtfQuestMarkSyncInfo::SyncNpcInfo syncNpcInfo;
			syncNpcInfo.npcIndex = itr.first;
			syncNpcInfo.disable = true;

			infos.push_back(syncNpcInfo);
		}
	}
}

bool Sector::IsExistQuestSync()
{
	if(0 < m_questMarkSyncNpcInfos.size())
	{
		return true;
	}

	return false;
}

#ifdef __Patch_WebOfGod_by_jason_20181213
UInt32 Sector::GetGridSize() const
{
	VERIFY_RETURN(m_grid, 0);
	return m_grid->GetGridSize();
}
#endif

SectorProcessor* Sector::GetSectorProcess() const
{
	return GetSectorController().GetControllerAction().GetSectorProcess();
}

#ifdef __Patch_Add_User_Emotion_Quest_Mission_Type_ch_20190117
Bool Sector::CheckPositionInVolume(UINT32 volumeIndex, const Vector3& position)
{
	VolumeScript* vscript = GetZoneScript<VolumeScript>(GetIndexZone());
	VERIFY_RETURN(vscript, false);

	const VolumeElem* elem = vscript->Get(volumeIndex);
	VALID_RETURN(elem, false);

	Float diff = Distance2D(position, elem->pos);		

	return elem->radius < diff? false : true;
}
#endif

#ifdef __Patch_Lua_InstanceGroup_Param_by_jason_20190109
InstanceSectorGroupEx* Sector::GetInstanceGroup() const
{
	SectorRunner* runner = GetSectorRunner();
	VERIFY_RETURN(runner, NULL);
	return runner->GetInstanceGroupManager().Find(GetInstanceGroupKey());
}

Byte Sector::GetEntranceUserCount() const
{
	if (GetInstanceDungeonInfo().IsFirstFloor())
	{
		return (Byte)GetInstanceDungeonInfo().firstJoinUserCount;
	}

	InstanceSectorGroupEx* group = GetInstanceGroup();
	VERIFY_RETURN(group, 0);

	return group->GetEntranceUserCount();
}

IndexItem Sector::GetEntranceUsedItemIndex() const
{
	if (GetInstanceDungeonInfo().IsFirstFloor())
	{
		return GetInstanceDungeonInfo().GetUsedItemIndex();
	}

	InstanceSectorGroupEx* group = GetInstanceGroup();
	VERIFY_RETURN(group, 0);

	return group->GetEntranceUsedItemIndex();
}

#endif

#ifdef __Patch_Knightage_Renewal_by_eunseok_20190128
__int64 Sector::GetUserVal(UInt32 idx)
{
	if (idx >= m_userVals.size())
		return 0;
	return m_userVals[idx];

}
#endif 
} // namespace mu2

