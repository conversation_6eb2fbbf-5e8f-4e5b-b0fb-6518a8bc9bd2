; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	??MDateTime@mu2@@QEBA_NAEBV01@@Z		; mu2::DateTime::operator<
PUBLIC	??0BurningStaminaTimeInfo@mu2@@QEAA@XZ		; mu2::BurningStaminaTimeInfo::BurningStaminaTimeInfo
PUBLIC	??1BurningStaminaTimeInfo@mu2@@QEAA@XZ		; mu2::BurningStaminaTimeInfo::~BurningStaminaTimeInfo
PUBLIC	?Initialize@BurningStaminaTimeInfo@mu2@@QEAAXAEBU_SYSTEMTIME@@@Z ; mu2::BurningStaminaTimeInfo::Initialize
PUBLIC	?IsBurning@BurningStaminaTimeInfo@mu2@@QEBA_NXZ	; mu2::BurningStaminaTimeInfo::IsBurning
PUBLIC	?SetBurning@BurningStaminaTimeInfo@mu2@@QEAAXI@Z ; mu2::BurningStaminaTimeInfo::SetBurning
PUBLIC	?ResetBurning@BurningStaminaTimeInfo@mu2@@QEAAXXZ ; mu2::BurningStaminaTimeInfo::ResetBurning
PUBLIC	?GetRemainTick@BurningStaminaTimeInfo@mu2@@QEBAIXZ ; mu2::BurningStaminaTimeInfo::GetRemainTick
PUBLIC	?GetExpirationDateTime@BurningStaminaTimeInfo@mu2@@QEBA_NAEAU_SYSTEMTIME@@@Z ; mu2::BurningStaminaTimeInfo::GetExpirationDateTime
PUBLIC	?FillUp@BurningStaminaTimeInfo@mu2@@QEBAXAEAUBurningStaminaContinentSimepleInfo@2@@Z ; mu2::BurningStaminaTimeInfo::FillUp
PUBLIC	?FillUp@BurningStaminaTimeInfo@mu2@@QEBAXAEAUBurningStaminaBurningInfo@2@@Z ; mu2::BurningStaminaTimeInfo::FillUp
PUBLIC	?FillUp@BurningStaminaTimeInfo@mu2@@QEBAXAEAUDBBurningStaminaInfo@2@@Z ; mu2::BurningStaminaTimeInfo::FillUp
PUBLIC	??_C@_09OLBBGJEO@Assertion@			; `string'
PUBLIC	??_C@_0DP@JBNFIAHF@F?3?2Release_Branch?2Server?2Develo@ ; `string'
PUBLIC	??_C@_0BF@EBMOOKLB@GetStatus?$CI?$CJ?5?$DN?$DN?5valid@ ; `string'
PUBLIC	??_C@_0BK@HBHPMAGO@date?4GetStatus?$CI?$CJ?5?$DN?$DN?5valid@ ; `string'
PUBLIC	??_C@_01GBGANLPD@0@				; `string'
PUBLIC	??_C@_0FF@FKENPIJL@F?3?2Release_Branch?2Server?2Develo@ ; `string'
EXTRN	__imp_GetTickCount:PROC
EXTRN	?LogRuntimeAssertionFailure@mu2@@YAXPEBD00_K@Z:PROC ; mu2::LogRuntimeAssertionFailure
EXTRN	?GetPresentTime@DateTime@mu2@@SA?BV12@XZ:PROC	; mu2::DateTime::GetPresentTime
EXTRN	??4DateTime@mu2@@QEAAAEBV01@AEBU_SYSTEMTIME@@@Z:PROC ; mu2::DateTime::operator=
EXTRN	??HDateTime@mu2@@QEBA?AV01@AEBVDateTimeSpan@1@@Z:PROC ; mu2::DateTime::operator+
EXTRN	??GDateTime@mu2@@QEBA?AVDateTimeSpan@1@AEBV01@@Z:PROC ; mu2::DateTime::operator-
EXTRN	?GetAsSystemTime@DateTime@mu2@@QEBAHAEAU_SYSTEMTIME@@@Z:PROC ; mu2::DateTime::GetAsSystemTime
EXTRN	?SetHighTimeSpan@DateTimeSpan@mu2@@QEAAXJHHHHHH@Z:PROC ; mu2::DateTimeSpan::SetHighTimeSpan
EXTRN	?GetTotalMilliSeconds@DateTimeSpan@mu2@@QEBA_JXZ:PROC ; mu2::DateTimeSpan::GetTotalMilliSeconds
EXTRN	__GSHandlerCheck:PROC
EXTRN	__security_check_cookie:PROC
EXTRN	__security_cookie:QWORD
;	COMDAT pdata
pdata	SEGMENT
$pdata$??MDateTime@mu2@@QEBA_NAEBV01@@Z DD imagerel $LN11
	DD	imagerel $LN11+176
	DD	imagerel $unwind$??MDateTime@mu2@@QEBA_NAEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Initialize@BurningStaminaTimeInfo@mu2@@QEAAXAEBU_SYSTEMTIME@@@Z DD imagerel $LN22
	DD	imagerel $LN22+169
	DD	imagerel $unwind$?Initialize@BurningStaminaTimeInfo@mu2@@QEAAXAEBU_SYSTEMTIME@@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?IsBurning@BurningStaminaTimeInfo@mu2@@QEBA_NXZ DD imagerel $LN5
	DD	imagerel $LN5+60
	DD	imagerel $unwind$?IsBurning@BurningStaminaTimeInfo@mu2@@QEBA_NXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?SetBurning@BurningStaminaTimeInfo@mu2@@QEAAXI@Z DD imagerel $LN3
	DD	imagerel $LN3+35
	DD	imagerel $unwind$?SetBurning@BurningStaminaTimeInfo@mu2@@QEAAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?GetRemainTick@BurningStaminaTimeInfo@mu2@@QEBAIXZ DD imagerel $LN5
	DD	imagerel $LN5+66
	DD	imagerel $unwind$?GetRemainTick@BurningStaminaTimeInfo@mu2@@QEBAIXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?GetExpirationDateTime@BurningStaminaTimeInfo@mu2@@QEBA_NAEAU_SYSTEMTIME@@@Z DD imagerel $LN9
	DD	imagerel $LN9+306
	DD	imagerel $unwind$?GetExpirationDateTime@BurningStaminaTimeInfo@mu2@@QEBA_NAEAU_SYSTEMTIME@@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?FillUp@BurningStaminaTimeInfo@mu2@@QEBAXAEAUBurningStaminaContinentSimepleInfo@2@@Z DD imagerel $LN3
	DD	imagerel $LN3+37
	DD	imagerel $unwind$?FillUp@BurningStaminaTimeInfo@mu2@@QEBAXAEAUBurningStaminaContinentSimepleInfo@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?FillUp@BurningStaminaTimeInfo@mu2@@QEBAXAEAUBurningStaminaBurningInfo@2@@Z DD imagerel $LN3
	DD	imagerel $LN3+37
	DD	imagerel $unwind$?FillUp@BurningStaminaTimeInfo@mu2@@QEBAXAEAUBurningStaminaBurningInfo@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?FillUp@BurningStaminaTimeInfo@mu2@@QEBAXAEAUDBBurningStaminaInfo@2@@Z DD imagerel $LN3
	DD	imagerel $LN3+42
	DD	imagerel $unwind$?FillUp@BurningStaminaTimeInfo@mu2@@QEBAXAEAUDBBurningStaminaInfo@2@@Z
pdata	ENDS
;	COMDAT ??_C@_0FF@FKENPIJL@F?3?2Release_Branch?2Server?2Develo@
CONST	SEGMENT
??_C@_0FF@FKENPIJL@F?3?2Release_Branch?2Server?2Develo@ DB 'F:\Release_Br'
	DB	'anch\Server\Development\Frontend\WorldServer\BurningStaminaTi'
	DB	'meInfo.cpp', 00H				; `string'
CONST	ENDS
;	COMDAT ??_C@_01GBGANLPD@0@
CONST	SEGMENT
??_C@_01GBGANLPD@0@ DB '0', 00H				; `string'
CONST	ENDS
;	COMDAT ??_C@_0BK@HBHPMAGO@date?4GetStatus?$CI?$CJ?5?$DN?$DN?5valid@
CONST	SEGMENT
??_C@_0BK@HBHPMAGO@date?4GetStatus?$CI?$CJ?5?$DN?$DN?5valid@ DB 'date.Get'
	DB	'Status() == valid', 00H			; `string'
CONST	ENDS
;	COMDAT ??_C@_0BF@EBMOOKLB@GetStatus?$CI?$CJ?5?$DN?$DN?5valid@
CONST	SEGMENT
??_C@_0BF@EBMOOKLB@GetStatus?$CI?$CJ?5?$DN?$DN?5valid@ DB 'GetStatus() =='
	DB	' valid', 00H				; `string'
CONST	ENDS
;	COMDAT ??_C@_0DP@JBNFIAHF@F?3?2Release_Branch?2Server?2Develo@
CONST	SEGMENT
??_C@_0DP@JBNFIAHF@F?3?2Release_Branch?2Server?2Develo@ DB 'F:\Release_Br'
	DB	'anch\Server\Development\Framework\Core\DateTime.h', 00H ; `string'
CONST	ENDS
;	COMDAT ??_C@_09OLBBGJEO@Assertion@
CONST	SEGMENT
??_C@_09OLBBGJEO@Assertion@ DB 'Assertion', 00H		; `string'
CONST	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?FillUp@BurningStaminaTimeInfo@mu2@@QEBAXAEAUDBBurningStaminaInfo@2@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?FillUp@BurningStaminaTimeInfo@mu2@@QEBAXAEAUBurningStaminaBurningInfo@2@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?FillUp@BurningStaminaTimeInfo@mu2@@QEBAXAEAUBurningStaminaContinentSimepleInfo@2@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DW	01dH
	DW	0118H
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?GetExpirationDateTime@BurningStaminaTimeInfo@mu2@@QEBA_NAEAU_SYSTEMTIME@@@Z DD 042519H
	DD	0150113H
	DD	0600b700cH
	DD	imagerel __GSHandlerCheck
	DD	098H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?GetRemainTick@BurningStaminaTimeInfo@mu2@@QEBAIXZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?SetBurning@BurningStaminaTimeInfo@mu2@@QEAAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?IsBurning@BurningStaminaTimeInfo@mu2@@QEBA_NXZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Initialize@BurningStaminaTimeInfo@mu2@@QEAAXAEBU_SYSTEMTIME@@@Z DD 010e01H
	DD	0e20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??MDateTime@mu2@@QEBA_NAEBV01@@Z DD 010e01H
	DD	0620eH
xdata	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaTimeInfo.cpp
;	COMDAT ?FillUp@BurningStaminaTimeInfo@mu2@@QEBAXAEAUDBBurningStaminaInfo@2@@Z
_TEXT	SEGMENT
this$ = 48
out$ = 56
?FillUp@BurningStaminaTimeInfo@mu2@@QEBAXAEAUDBBurningStaminaInfo@2@@Z PROC ; mu2::BurningStaminaTimeInfo::FillUp, COMDAT

; 85   : {

$LN3:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 86   : 	GetExpirationDateTime(out.expirationDate);

  0000e	48 8b 44 24 38	 mov	 rax, QWORD PTR out$[rsp]
  00013	48 83 c0 38	 add	 rax, 56			; 00000038H
  00017	48 8b d0	 mov	 rdx, rax
  0001a	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0001f	e8 00 00 00 00	 call	 ?GetExpirationDateTime@BurningStaminaTimeInfo@mu2@@QEBA_NAEAU_SYSTEMTIME@@@Z ; mu2::BurningStaminaTimeInfo::GetExpirationDateTime
  00024	90		 npad	 1

; 87   : }

  00025	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00029	c3		 ret	 0
?FillUp@BurningStaminaTimeInfo@mu2@@QEBAXAEAUDBBurningStaminaInfo@2@@Z ENDP ; mu2::BurningStaminaTimeInfo::FillUp
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaTimeInfo.cpp
;	COMDAT ?FillUp@BurningStaminaTimeInfo@mu2@@QEBAXAEAUBurningStaminaBurningInfo@2@@Z
_TEXT	SEGMENT
this$ = 48
out$ = 56
?FillUp@BurningStaminaTimeInfo@mu2@@QEBAXAEAUBurningStaminaBurningInfo@2@@Z PROC ; mu2::BurningStaminaTimeInfo::FillUp, COMDAT

; 80   : {

$LN3:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 81   : 	out.remainTick = GetRemainTick();

  0000e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00013	e8 00 00 00 00	 call	 ?GetRemainTick@BurningStaminaTimeInfo@mu2@@QEBAIXZ ; mu2::BurningStaminaTimeInfo::GetRemainTick
  00018	48 8b 4c 24 38	 mov	 rcx, QWORD PTR out$[rsp]
  0001d	89 41 0c	 mov	 DWORD PTR [rcx+12], eax

; 82   : }

  00020	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00024	c3		 ret	 0
?FillUp@BurningStaminaTimeInfo@mu2@@QEBAXAEAUBurningStaminaBurningInfo@2@@Z ENDP ; mu2::BurningStaminaTimeInfo::FillUp
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaTimeInfo.cpp
;	COMDAT ?FillUp@BurningStaminaTimeInfo@mu2@@QEBAXAEAUBurningStaminaContinentSimepleInfo@2@@Z
_TEXT	SEGMENT
this$ = 48
out$ = 56
?FillUp@BurningStaminaTimeInfo@mu2@@QEBAXAEAUBurningStaminaContinentSimepleInfo@2@@Z PROC ; mu2::BurningStaminaTimeInfo::FillUp, COMDAT

; 75   : {

$LN3:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 76   : 	out.remainTick = GetRemainTick();

  0000e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00013	e8 00 00 00 00	 call	 ?GetRemainTick@BurningStaminaTimeInfo@mu2@@QEBAIXZ ; mu2::BurningStaminaTimeInfo::GetRemainTick
  00018	48 8b 4c 24 38	 mov	 rcx, QWORD PTR out$[rsp]
  0001d	89 41 0c	 mov	 DWORD PTR [rcx+12], eax

; 77   : }

  00020	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00024	c3		 ret	 0
?FillUp@BurningStaminaTimeInfo@mu2@@QEBAXAEAUBurningStaminaContinentSimepleInfo@2@@Z ENDP ; mu2::BurningStaminaTimeInfo::FillUp
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaTimeInfo.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\DateTime.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaTimeInfo.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\DateTime.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaTimeInfo.cpp
;	COMDAT ?GetExpirationDateTime@BurningStaminaTimeInfo@mu2@@QEBA_NAEAU_SYSTEMTIME@@@Z
_TEXT	SEGMENT
this$ = 64
span$ = 72
$T1 = 88
$T2 = 104
systime$3 = 120
$T4 = 136
__$ArrayPad$ = 152
this$ = 192
out$ = 200
?GetExpirationDateTime@BurningStaminaTimeInfo@mu2@@QEBA_NAEAU_SYSTEMTIME@@@Z PROC ; mu2::BurningStaminaTimeInfo::GetExpirationDateTime, COMDAT

; 59   : {

$LN9:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	56		 push	 rsi
  0000b	57		 push	 rdi
  0000c	48 81 ec a8 00
	00 00		 sub	 rsp, 168		; 000000a8H
  00013	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR __security_cookie
  0001a	48 33 c4	 xor	 rax, rsp
  0001d	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR __$ArrayPad$[rsp], rax

; 60   : 	if( !IsBurning() )

  00025	48 8b 8c 24 c0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0002d	e8 00 00 00 00	 call	 ?IsBurning@BurningStaminaTimeInfo@mu2@@QEBA_NXZ ; mu2::BurningStaminaTimeInfo::IsBurning
  00032	0f b6 c0	 movzx	 eax, al
  00035	85 c0		 test	 eax, eax
  00037	75 2f		 jne	 SHORT $LN2@GetExpirat

; 61   : 	{
; 62   : 		MU2_ASSERT(0);

  00039	33 c0		 xor	 eax, eax
  0003b	83 f8 01	 cmp	 eax, 1
  0003e	74 21		 je	 SHORT $LN3@GetExpirat
  00040	41 b9 3e 00 00
	00		 mov	 r9d, 62			; 0000003eH
  00046	4c 8d 05 00 00
	00 00		 lea	 r8, OFFSET FLAT:??_C@_0FF@FKENPIJL@F?3?2Release_Branch?2Server?2Develo@
  0004d	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:??_C@_01GBGANLPD@0@
  00054	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_09OLBBGJEO@Assertion@
  0005b	e8 00 00 00 00	 call	 ?LogRuntimeAssertionFailure@mu2@@YAXPEBD00_K@Z ; mu2::LogRuntimeAssertionFailure
  00060	90		 npad	 1
$LN3@GetExpirat:

; 63   : 		return false;

  00061	32 c0		 xor	 al, al
  00063	e9 b0 00 00 00	 jmp	 $LN1@GetExpirat
$LN2@GetExpirat:
; File F:\Release_Branch\Server\Development\Framework\Core\DateTime.h

; 390  : 	status_ = valid;

  00068	c6 44 24 48 00	 mov	 BYTE PTR span$[rsp], 0

; 391  : 	span_.QuadPart = 0;

  0006d	48 c7 44 24 50
	00 00 00 00	 mov	 QWORD PTR span$[rsp+8], 0
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaTimeInfo.cpp

; 67   : 	span.SetHighTimeSpan( 0, 0, 0, 0, GetRemainTick() );

  00076	48 8b 8c 24 c0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0007e	e8 00 00 00 00	 call	 ?GetRemainTick@BurningStaminaTimeInfo@mu2@@QEBAIXZ ; mu2::BurningStaminaTimeInfo::GetRemainTick
  00083	c7 44 24 38 00
	00 00 00	 mov	 DWORD PTR [rsp+56], 0
  0008b	c7 44 24 30 00
	00 00 00	 mov	 DWORD PTR [rsp+48], 0
  00093	89 44 24 28	 mov	 DWORD PTR [rsp+40], eax
  00097	c7 44 24 20 00
	00 00 00	 mov	 DWORD PTR [rsp+32], 0
  0009f	45 33 c9	 xor	 r9d, r9d
  000a2	45 33 c0	 xor	 r8d, r8d
  000a5	33 d2		 xor	 edx, edx
  000a7	48 8d 4c 24 48	 lea	 rcx, QWORD PTR span$[rsp]
  000ac	e8 00 00 00 00	 call	 ?SetHighTimeSpan@DateTimeSpan@mu2@@QEAAXJHHHHHH@Z ; mu2::DateTimeSpan::SetHighTimeSpan
  000b1	90		 npad	 1

; 69   : 	out = SYSTEMTIME( DateTime::GetPresentTime() + span );

  000b2	48 8d 4c 24 58	 lea	 rcx, QWORD PTR $T1[rsp]
  000b7	e8 00 00 00 00	 call	 ?GetPresentTime@DateTime@mu2@@SA?BV12@XZ ; mu2::DateTime::GetPresentTime
  000bc	4c 8d 44 24 48	 lea	 r8, QWORD PTR span$[rsp]
  000c1	48 8d 54 24 68	 lea	 rdx, QWORD PTR $T2[rsp]
  000c6	48 8b c8	 mov	 rcx, rax
  000c9	e8 00 00 00 00	 call	 ??HDateTime@mu2@@QEBA?AV01@AEBVDateTimeSpan@1@@Z ; mu2::DateTime::operator+
  000ce	48 89 44 24 40	 mov	 QWORD PTR this$[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\DateTime.h

; 260  : 	GetAsSystemTime(systime);

  000d3	48 8d 54 24 78	 lea	 rdx, QWORD PTR systime$3[rsp]
  000d8	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  000dd	e8 00 00 00 00	 call	 ?GetAsSystemTime@DateTime@mu2@@QEBAHAEAU_SYSTEMTIME@@@Z ; mu2::DateTime::GetAsSystemTime

; 261  : 	return systime;

  000e2	48 8d 84 24 88
	00 00 00	 lea	 rax, QWORD PTR $T4[rsp]
  000ea	48 8d 4c 24 78	 lea	 rcx, QWORD PTR systime$3[rsp]
  000ef	48 8b f8	 mov	 rdi, rax
  000f2	48 8b f1	 mov	 rsi, rcx
  000f5	b9 10 00 00 00	 mov	 ecx, 16
  000fa	f3 a4		 rep movsb
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaTimeInfo.cpp

; 69   : 	out = SYSTEMTIME( DateTime::GetPresentTime() + span );

  000fc	48 8d 84 24 88
	00 00 00	 lea	 rax, QWORD PTR $T4[rsp]
  00104	48 8b bc 24 c8
	00 00 00	 mov	 rdi, QWORD PTR out$[rsp]
  0010c	48 8b f0	 mov	 rsi, rax
  0010f	b9 10 00 00 00	 mov	 ecx, 16
  00114	f3 a4		 rep movsb

; 70   : 
; 71   : 	return true;

  00116	b0 01		 mov	 al, 1
$LN1@GetExpirat:

; 72   : }

  00118	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR __$ArrayPad$[rsp]
  00120	48 33 cc	 xor	 rcx, rsp
  00123	e8 00 00 00 00	 call	 __security_check_cookie
  00128	48 81 c4 a8 00
	00 00		 add	 rsp, 168		; 000000a8H
  0012f	5f		 pop	 rdi
  00130	5e		 pop	 rsi
  00131	c3		 ret	 0
?GetExpirationDateTime@BurningStaminaTimeInfo@mu2@@QEBA_NAEAU_SYSTEMTIME@@@Z ENDP ; mu2::BurningStaminaTimeInfo::GetExpirationDateTime
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaTimeInfo.cpp
;	COMDAT ?GetRemainTick@BurningStaminaTimeInfo@mu2@@QEBAIXZ
_TEXT	SEGMENT
tv71 = 32
this$ = 64
?GetRemainTick@BurningStaminaTimeInfo@mu2@@QEBAIXZ PROC	; mu2::BurningStaminaTimeInfo::GetRemainTick, COMDAT

; 54   : {

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 55   : 	return IsBurning() ? m_expirationTick - GetTickCount() : 0;

  00009	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0000e	e8 00 00 00 00	 call	 ?IsBurning@BurningStaminaTimeInfo@mu2@@QEBA_NXZ ; mu2::BurningStaminaTimeInfo::IsBurning
  00013	0f b6 c0	 movzx	 eax, al
  00016	85 c0		 test	 eax, eax
  00018	74 17		 je	 SHORT $LN3@GetRemainT
  0001a	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetTickCount
  00020	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00025	8b 09		 mov	 ecx, DWORD PTR [rcx]
  00027	2b c8		 sub	 ecx, eax
  00029	8b c1		 mov	 eax, ecx
  0002b	89 44 24 20	 mov	 DWORD PTR tv71[rsp], eax
  0002f	eb 08		 jmp	 SHORT $LN4@GetRemainT
$LN3@GetRemainT:
  00031	c7 44 24 20 00
	00 00 00	 mov	 DWORD PTR tv71[rsp], 0
$LN4@GetRemainT:
  00039	8b 44 24 20	 mov	 eax, DWORD PTR tv71[rsp]

; 56   : }

  0003d	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00041	c3		 ret	 0
?GetRemainTick@BurningStaminaTimeInfo@mu2@@QEBAIXZ ENDP	; mu2::BurningStaminaTimeInfo::GetRemainTick
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaTimeInfo.cpp
;	COMDAT ?ResetBurning@BurningStaminaTimeInfo@mu2@@QEAAXXZ
_TEXT	SEGMENT
this$ = 8
?ResetBurning@BurningStaminaTimeInfo@mu2@@QEAAXXZ PROC	; mu2::BurningStaminaTimeInfo::ResetBurning, COMDAT

; 49   : {

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 50   : 	m_expirationTick = 0;

  00005	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0000a	c7 00 00 00 00
	00		 mov	 DWORD PTR [rax], 0

; 51   : }

  00010	c3		 ret	 0
?ResetBurning@BurningStaminaTimeInfo@mu2@@QEAAXXZ ENDP	; mu2::BurningStaminaTimeInfo::ResetBurning
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaTimeInfo.cpp
;	COMDAT ?SetBurning@BurningStaminaTimeInfo@mu2@@QEAAXI@Z
_TEXT	SEGMENT
this$ = 48
maintainTick$ = 56
?SetBurning@BurningStaminaTimeInfo@mu2@@QEAAXI@Z PROC	; mu2::BurningStaminaTimeInfo::SetBurning, COMDAT

; 44   : {

$LN3:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 45   : 	m_expirationTick = GetTickCount() + maintainTick;

  0000d	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetTickCount
  00013	03 44 24 38	 add	 eax, DWORD PTR maintainTick$[rsp]
  00017	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0001c	89 01		 mov	 DWORD PTR [rcx], eax

; 46   : }

  0001e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00022	c3		 ret	 0
?SetBurning@BurningStaminaTimeInfo@mu2@@QEAAXI@Z ENDP	; mu2::BurningStaminaTimeInfo::SetBurning
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaTimeInfo.cpp
;	COMDAT ?IsBurning@BurningStaminaTimeInfo@mu2@@QEBA_NXZ
_TEXT	SEGMENT
tv68 = 32
this$ = 64
?IsBurning@BurningStaminaTimeInfo@mu2@@QEBA_NXZ PROC	; mu2::BurningStaminaTimeInfo::IsBurning, COMDAT

; 39   : {

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 40   : 	return GetTickCount() < m_expirationTick + 10000;  // 10초정도 여유 시간을 갖는다.

  00009	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetTickCount
  0000f	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00014	8b 09		 mov	 ecx, DWORD PTR [rcx]
  00016	81 c1 10 27 00
	00		 add	 ecx, 10000		; 00002710H
  0001c	3b c1		 cmp	 eax, ecx
  0001e	73 0a		 jae	 SHORT $LN3@IsBurning
  00020	c7 44 24 20 01
	00 00 00	 mov	 DWORD PTR tv68[rsp], 1
  00028	eb 08		 jmp	 SHORT $LN4@IsBurning
$LN3@IsBurning:
  0002a	c7 44 24 20 00
	00 00 00	 mov	 DWORD PTR tv68[rsp], 0
$LN4@IsBurning:
  00032	0f b6 44 24 20	 movzx	 eax, BYTE PTR tv68[rsp]

; 41   : }

  00037	48 83 c4 38	 add	 rsp, 56			; 00000038H
  0003b	c3		 ret	 0
?IsBurning@BurningStaminaTimeInfo@mu2@@QEBA_NXZ ENDP	; mu2::BurningStaminaTimeInfo::IsBurning
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaTimeInfo.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\DateTime.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaTimeInfo.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\limits
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaTimeInfo.cpp
;	COMDAT ?Initialize@BurningStaminaTimeInfo@mu2@@QEAAXAEBU_SYSTEMTIME@@@Z
_TEXT	SEGMENT
$T1 = 32
curTick$2 = 36
diffTick$3 = 40
leftTick$4 = 48
cur$ = 56
time$ = 72
diff$5 = 88
this$ = 128
expirationTIme$ = 136
?Initialize@BurningStaminaTimeInfo@mu2@@QEAAXAEBU_SYSTEMTIME@@@Z PROC ; mu2::BurningStaminaTimeInfo::Initialize, COMDAT

; 20   : {

$LN22:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 78	 sub	 rsp, 120		; 00000078H
; File F:\Release_Branch\Server\Development\Framework\Core\DateTime.h

; 247  : 	*this = systimeSrc;

  0000e	48 8b 94 24 88
	00 00 00	 mov	 rdx, QWORD PTR expirationTIme$[rsp]
  00016	48 8d 4c 24 48	 lea	 rcx, QWORD PTR time$[rsp]
  0001b	e8 00 00 00 00	 call	 ??4DateTime@mu2@@QEAAAEBV01@AEBU_SYSTEMTIME@@@Z ; mu2::DateTime::operator=
  00020	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaTimeInfo.cpp

; 22   : 	DateTime cur = DateTime::GetPresentTime();

  00021	48 8d 4c 24 38	 lea	 rcx, QWORD PTR cur$[rsp]
  00026	e8 00 00 00 00	 call	 ?GetPresentTime@DateTime@mu2@@SA?BV12@XZ ; mu2::DateTime::GetPresentTime
  0002b	90		 npad	 1

; 23   : 
; 24   : 	if( cur < time )

  0002c	48 8d 54 24 48	 lea	 rdx, QWORD PTR time$[rsp]
  00031	48 8d 4c 24 38	 lea	 rcx, QWORD PTR cur$[rsp]
  00036	e8 00 00 00 00	 call	 ??MDateTime@mu2@@QEBA_NAEBV01@@Z ; mu2::DateTime::operator<
  0003b	0f b6 c0	 movzx	 eax, al
  0003e	85 c0		 test	 eax, eax
  00040	74 62		 je	 SHORT $LN2@Initialize

; 25   : 	{
; 26   : 		DateTimeSpan diff = time - cur;

  00042	4c 8d 44 24 38	 lea	 r8, QWORD PTR cur$[rsp]
  00047	48 8d 54 24 58	 lea	 rdx, QWORD PTR diff$5[rsp]
  0004c	48 8d 4c 24 48	 lea	 rcx, QWORD PTR time$[rsp]
  00051	e8 00 00 00 00	 call	 ??GDateTime@mu2@@QEBA?AVDateTimeSpan@1@AEBV01@@Z ; mu2::DateTime::operator-

; 27   : 		Tick	curTick	= GetTickCount();

  00056	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetTickCount
  0005c	89 44 24 24	 mov	 DWORD PTR curTick$2[rsp], eax

; 28   : 		UInt64	diffTick= diff.GetTotalMilliSeconds();

  00060	48 8d 4c 24 58	 lea	 rcx, QWORD PTR diff$5[rsp]
  00065	e8 00 00 00 00	 call	 ?GetTotalMilliSeconds@DateTimeSpan@mu2@@QEBA_JXZ ; mu2::DateTimeSpan::GetTotalMilliSeconds
  0006a	48 89 44 24 28	 mov	 QWORD PTR diffTick$3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\limits

; 726  :         return UINT_MAX;

  0006f	c7 44 24 20 ff
	ff ff ff	 mov	 DWORD PTR $T1[rsp], -1	; ffffffffH
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaTimeInfo.cpp

; 29   : 		UInt64	leftTick= static_cast< UInt64 >( std::numeric_limits< Tick >::max() - curTick );

  00077	8b 44 24 20	 mov	 eax, DWORD PTR $T1[rsp]
  0007b	2b 44 24 24	 sub	 eax, DWORD PTR curTick$2[rsp]
  0007f	8b c0		 mov	 eax, eax
  00081	48 89 44 24 30	 mov	 QWORD PTR leftTick$4[rsp], rax

; 30   : 
; 31   : 		if( diffTick < leftTick )

  00086	48 8b 44 24 30	 mov	 rax, QWORD PTR leftTick$4[rsp]
  0008b	48 39 44 24 28	 cmp	 QWORD PTR diffTick$3[rsp], rax
  00090	73 12		 jae	 SHORT $LN2@Initialize

; 32   : 		{
; 33   : 			SetBurning( static_cast< Tick >( diffTick ) );

  00092	8b 54 24 28	 mov	 edx, DWORD PTR diffTick$3[rsp]
  00096	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0009e	e8 00 00 00 00	 call	 ?SetBurning@BurningStaminaTimeInfo@mu2@@QEAAXI@Z ; mu2::BurningStaminaTimeInfo::SetBurning
  000a3	90		 npad	 1
$LN2@Initialize:

; 34   : 		}
; 35   : 	}
; 36   : }

  000a4	48 83 c4 78	 add	 rsp, 120		; 00000078H
  000a8	c3		 ret	 0
?Initialize@BurningStaminaTimeInfo@mu2@@QEAAXAEBU_SYSTEMTIME@@@Z ENDP ; mu2::BurningStaminaTimeInfo::Initialize
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaTimeInfo.cpp
;	COMDAT ??1BurningStaminaTimeInfo@mu2@@QEAA@XZ
_TEXT	SEGMENT
this$ = 8
??1BurningStaminaTimeInfo@mu2@@QEAA@XZ PROC		; mu2::BurningStaminaTimeInfo::~BurningStaminaTimeInfo, COMDAT

; 16   : {

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 17   : }

  00005	c3		 ret	 0
??1BurningStaminaTimeInfo@mu2@@QEAA@XZ ENDP		; mu2::BurningStaminaTimeInfo::~BurningStaminaTimeInfo
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaTimeInfo.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaTimeInfo.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaTimeInfo.cpp
;	COMDAT ??0BurningStaminaTimeInfo@mu2@@QEAA@XZ
_TEXT	SEGMENT
this$ = 8
??0BurningStaminaTimeInfo@mu2@@QEAA@XZ PROC		; mu2::BurningStaminaTimeInfo::BurningStaminaTimeInfo, COMDAT

; 12   : {

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaTimeInfo.h

; 26   : 	Tick			m_expirationTick = 0;

  00005	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0000a	c7 00 00 00 00
	00		 mov	 DWORD PTR [rax], 0
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaTimeInfo.cpp

; 13   : }

  00010	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00015	c3		 ret	 0
??0BurningStaminaTimeInfo@mu2@@QEAA@XZ ENDP		; mu2::BurningStaminaTimeInfo::BurningStaminaTimeInfo
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\DateTime.h
;	COMDAT ??MDateTime@mu2@@QEBA_NAEBV01@@Z
_TEXT	SEGMENT
$T1 = 32
$T2 = 33
tv85 = 36
this$ = 64
date$ = 72
??MDateTime@mu2@@QEBA_NAEBV01@@Z PROC			; mu2::DateTime::operator<, COMDAT

; 338  : {

$LN11:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 81   :     DateTimeStatus GetStatus() const {return status_;}

  0000e	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00013	0f b6 00	 movzx	 eax, BYTE PTR [rax]
  00016	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al

; 339  : 	MU2_ASSERT(GetStatus() == valid);

  0001a	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  0001f	0f b6 c0	 movzx	 eax, al
  00022	85 c0		 test	 eax, eax
  00024	74 21		 je	 SHORT $LN2@operator
  00026	41 b9 53 01 00
	00		 mov	 r9d, 339		; 00000153H
  0002c	4c 8d 05 00 00
	00 00		 lea	 r8, OFFSET FLAT:??_C@_0DP@JBNFIAHF@F?3?2Release_Branch?2Server?2Develo@
  00033	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:??_C@_0BF@EBMOOKLB@GetStatus?$CI?$CJ?5?$DN?$DN?5valid@
  0003a	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_09OLBBGJEO@Assertion@
  00041	e8 00 00 00 00	 call	 ?LogRuntimeAssertionFailure@mu2@@YAXPEBD00_K@Z ; mu2::LogRuntimeAssertionFailure
  00046	90		 npad	 1
$LN2@operator:

; 81   :     DateTimeStatus GetStatus() const {return status_;}

  00047	48 8b 44 24 48	 mov	 rax, QWORD PTR date$[rsp]
  0004c	0f b6 00	 movzx	 eax, BYTE PTR [rax]
  0004f	88 44 24 21	 mov	 BYTE PTR $T2[rsp], al

; 340  : 	MU2_ASSERT(date.GetStatus() == valid);

  00053	0f b6 44 24 21	 movzx	 eax, BYTE PTR $T2[rsp]
  00058	0f b6 c0	 movzx	 eax, al
  0005b	85 c0		 test	 eax, eax
  0005d	74 21		 je	 SHORT $LN3@operator
  0005f	41 b9 54 01 00
	00		 mov	 r9d, 340		; 00000154H
  00065	4c 8d 05 00 00
	00 00		 lea	 r8, OFFSET FLAT:??_C@_0DP@JBNFIAHF@F?3?2Release_Branch?2Server?2Develo@
  0006c	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:??_C@_0BK@HBHPMAGO@date?4GetStatus?$CI?$CJ?5?$DN?$DN?5valid@
  00073	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_09OLBBGJEO@Assertion@
  0007a	e8 00 00 00 00	 call	 ?LogRuntimeAssertionFailure@mu2@@YAXPEBD00_K@Z ; mu2::LogRuntimeAssertionFailure
  0007f	90		 npad	 1
$LN3@operator:

; 341  : 	return (time_.QuadPart < date.time_.QuadPart); 

  00080	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00085	48 8b 4c 24 48	 mov	 rcx, QWORD PTR date$[rsp]
  0008a	48 8b 49 08	 mov	 rcx, QWORD PTR [rcx+8]
  0008e	48 39 48 08	 cmp	 QWORD PTR [rax+8], rcx
  00092	7d 0a		 jge	 SHORT $LN5@operator
  00094	c7 44 24 24 01
	00 00 00	 mov	 DWORD PTR tv85[rsp], 1
  0009c	eb 08		 jmp	 SHORT $LN6@operator
$LN5@operator:
  0009e	c7 44 24 24 00
	00 00 00	 mov	 DWORD PTR tv85[rsp], 0
$LN6@operator:
  000a6	0f b6 44 24 24	 movzx	 eax, BYTE PTR tv85[rsp]

; 342  : } 

  000ab	48 83 c4 38	 add	 rsp, 56			; 00000038H
  000af	c3		 ret	 0
??MDateTime@mu2@@QEBA_NAEBV01@@Z ENDP			; mu2::DateTime::operator<
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaTimeInfo.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\BurningStaminaTimeInfo.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
