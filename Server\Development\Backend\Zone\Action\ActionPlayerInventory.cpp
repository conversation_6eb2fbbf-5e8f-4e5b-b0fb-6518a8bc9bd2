﻿#include "stdafx.h"
#include <Backend/Zone/Action/ActionAchievement.h>
#include <Backend/Zone/Action/ActionDroppedItem.h>
#include <Backend/Zone/Action/ActionExpiryItem.h>
#include <Backend/Zone/Action/ActionPlayer.h>
#include <Backend/Zone/Action/ActionParty.h>
#include <Backend/Zone/Action/ActionPlayerInventory.h>
#include <Backend/Zone/Action/ActionPlayerItemTrade.h>
#include <Backend/Zone/Action/ActionPlayer/ActionContact.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerCombatPower.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerKnightage.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerKnightageStorage.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerEvent.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerMembership.h>
#include <Backend/Zone/Action/ActionPlayer/ActionSkillManage.h>
#ifdef __Patch_LegendMarble_v4_raylee_2019_1_23
#include <Backend/Zone/Action/ActionUseItem.h>
#endif
#include <Backend/Zone/Action/ActionPlayerStorage.h>
#include <Backend/Zone/Action/ActionSetItemAbility.h>
#include <Backend/Zone/Action/ActionPassivity.h>
#include <Backend/Zone/Action/ActionSkillChanger.h>
#include <Backend/Zone/Action/ActionTutorial.h>
#include <Backend/Zone/Action/ActionAchievement.h>
#include <Backend/Zone/System/DropSystem.h>
#include <Backend/Zone/System/LogicEffectSystem.h>
#include <Backend/Zone/Transaction/ItemTransaction/ItemExtractTransaction.h>
#include <Backend/Zone/Transaction/ItemTransaction/ItemMoveTransaction.h>
#include <Backend/Zone/Transaction/ItemTransaction/ItemPushTransaction.h>
#include <Backend/Zone/Transaction/ItemTransaction/ItemUnequipTransaction.h>
#include <Backend/Zone/Transaction/ItemTransaction/ItemRemoveTransaction.h>
#include <Backend/Zone/Transaction/ItemTransaction/ItemEquipExchangeTransaction.h>
#include <Backend/Zone/Transaction/ItemTransaction/ItemSeperateTransaction.h>
#include <Backend/Zone/Transaction/ItemTransaction/ItemSortTransaction.h>
#include <Backend/Zone/Transaction/ItemTransaction/ItemOverlapTransaction.h>
#include <Backend/Zone/Transaction/ItemTransaction/ItemExchangeTransaction.h>
#include <Backend/Zone/Transaction/ItemTransaction/ItemStorageTransaction.h>
#include <Backend/Zone/Transaction/ItemTransaction/ItemMailTransaction.h>
#include <Backend/Zone/Transaction/ItemTransaction/ItemCustomizingTransaction.h>
#include <Backend/Zone/Transaction/ItemTransaction/WShopItemTransaction.h>
#include <Backend/Zone/Transaction/ItemTransaction/FreeMopupTransaction.h>
#ifdef __Patch_Mercler_Secret_Shop_by_ch_2019_04_24
#include <Backend/Zone/Transaction/ItemTransaction/ItemBuyTransaction.h>
#endif // __Patch_Mercler_Secret_Shop_by_ch_2019_04_24
#include <Backend/Zone/View/ViewPosition.h>
#include <Backend/Zone/System/StatueSystem.h>
#include "ZoneServer.h"
#include "Transaction/ItemTransaction/ItemArtifactTransaction.h"
#include "Transaction/ItemTransaction/ItemOptionChangeTransaction.h"
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerSeason.h>
#include "Transaction/ItemTransaction/PrivateStorageTransaction.h"
#include <Backend/Zone/Action/ActionQuest.h>
#include <Backend/Zone/System/GameEventSystem.h>
#include <Backend/Zone/System/MarbleSystem.h>
#include <Backend/Zone/Element/Item/InvenNonSlotable.h>
#include <Backend/Zone/Element/Item/InvenAccountStorage.h>
#include <Backend/Zone/Element/Item/InvenPrivateStorage.h>
#include <Backend/Zone/Element/Item/InvenEquip.h>
#include <Backend/Zone/Element/Item/ItemFinder.h>
#include <Backend/Zone/Action/ActionPlayerMailBox.h>

using namespace mu2::shared;

namespace mu2
{

ActionPlayerInventory::ActionPlayerInventory(EntityPlayer* owner )
	: Action4Player( owner, ID )
	, m_sortBagCoolTimer()
	, m_lock()
	, m_marbleTimeChecker(10*1000)
	, m_marblePlayingRedzen(false)
	, m_itemRune(*GetOwnerPlayer())


#ifdef __Reincarnation__jason_180628__
	, m_abilityParamCollectorCostume(*owner)
#endif
#ifdef __lockable_item_181001
	, m_itemLockableCheat(false)
#endif
#ifdef __Patch_Talismanbox_Ticket_Reward_System_by_ch_181224
	, m_talismanboxTicketRewardCondition(TalismanboxTicketRewardCondition::NONE)
	, m_isLoadDBTalismanboxTicketInfo(false)
#endif
{
	CounterInc("ActionPlayerInventory");
	Init();
}

ActionPlayerInventory::~ActionPlayerInventory()
{
	CounterDec("ActionPlayerInventory");
}

void ActionPlayerInventory::Init()
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	m_transactionQueue.clear();
	m_mapInventory.clear();
	
	//==========================================================================================
	// slot bag
	//==========================================================================================

	CreateInven<InvenEquip>		( InvenType::EQUIP,		SlotType::COUNT,			true, true);
	CreateInven<InvenDefaultBag>( InvenType::BAG_1ST,	Limits::MAX_INVENTORY_SLOT, true, true);
	CreateInven<InvenDefaultBag>( InvenType::BAG_2ND,	Limits::MAX_INVENTORY_SLOT, true, true);
	CreateInven<InvenDefaultBag>( InvenType::BAG_3RD,	Limits::MAX_INVENTORY_SLOT, true, true);
	CreateInven<InvenPlatinum>	( InvenType::BAG_4TH,	Limits::MAX_INVENTORY_SLOT, false, true);
	CreateInven<InvenPlatinum>	( InvenType::BAG_5TH,	Limits::MAX_INVENTORY_SLOT, false, true);
	
	for (const eInvenType& eInven : PremiumBags.ToInvens())
	{
		CreateInven<InvenPremium>(eInven, Limits::MAX_INVENTORY_SLOT, true, true);
	}

	for (const eInvenType& eInven : PrivateStorages.ToInvens())
	{
		CreateInven<InvenStoragePrivate>(eInven, Limits::MAX_PRIVATE_STORAGE_SLOT_COUNT, false, true);
	}

	//==========================================================================================
	// non slot bag
	//==========================================================================================

	CreateInven<InvenReBuy>	(InvenType::REBUY, Limits::MAX_REBUY_INVEN, true, true);
	CreateInven<InvenMail>	(InvenType::MAIL_BAG, Limits::MAX_MAILBAG_SLOT, true, true);

	m_amplifactionSystem.Reset();
	Clear4Fixing();

#ifdef __Patch_Talismanbox_Ticket_Reward_System_by_ch_181224
	m_talismanboxTicketRewardTime.Reset();
#endif
}

void ActionPlayerInventory::DestroyInven(const eInvenType& eInven)
{
	m_mapInventory.erase(eInven);
}

Inven* ActionPlayerInventory::GetInven(const eInvenType& invenType) const
{
	auto iter = m_mapInventory.find(invenType);
	VALID_RETURN( iter != m_mapInventory.end(), nullptr );

	return const_cast<Inven*>(iter->second.get());
}

Bool IsRequireEquipProtocol(const ItemPos& src, const ItemPos& tar)
{
	return src.IsEquipBag() && false == tar.IsEquipBag() || false == src.IsEquipBag()  && tar.IsEquipBag();
}

// 위치정보가 잘못된 아이템을 복구하기 위하여, 임시 컨테이너에 담아서 처리한다.
void ActionPlayerInventory::Append4Fixing(const InvenRange& range, const ItemData& data)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	VERIFY_RETURN(data.IsSlotBags(), );
	
	if (m_ssetIncorrectItem.end() != m_ssetIncorrectItem.find(data.GetItemId()))
	{
		MU2_ERROR_LOG(LogCategory::SYSTEM, "InCorrectPostion already checked - player(%s), item(%s)", owner->ToString().c_str(), data.ToString().c_str());
		return;
	}

	auto iter = m_mapIncorrectItem.find(range);
	if (iter != m_mapIncorrectItem.end())
	{
		iter->second.emplace_back(data);
	}
	else
	{
		ItemDatas buffer;
		buffer.push_back(data);
		m_mapIncorrectItem.emplace(range, buffer);
	}

	MU2_ERROR_LOG(LogCategory::SYSTEM, "InCorrectPostion - player(%s), item(%s)", owner->ToString().c_str(), data.ToString().c_str());
}

ErrorItem::Error ActionPlayerInventory::UnEquip(const ItemPlace& srcPlace, const ItemPos& targetPos)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	InvenEquip& srcInven = GetEquipInven();
	VERIFY_RETURN(srcInven.IsUsableRemoteNpcFunction(), ErrorItem::IsUsableRemoteNpcFunctionFailed);
	
	Inven* tarInven = GetInven(targetPos);
	VERIFY_RETURN(tarInven, ErrorItem::inventoryNotFound);
	VERIFY_RETURN(tarInven->IsFreeSlot(targetPos.GetSlot()), ErrorItem::E_EXIST_ITEM);	
	VERIFY_RETURN(tarInven->IsUsableRemoteNpcFunction(), ErrorItem::IsUsableRemoteNpcFunctionFailed);	

	const ItemConstPtr unEquipableItem = srcInven.GetItemByPlace(srcPlace);
	VERIFY_RETURN(unEquipableItem, ErrorItem::E_NULL_SLOT);	

	VERIFY_RETURN(IsPopable(unEquipableItem), ErrorItem::IsPopableFailed);
	VERIFY_RETURN(IsPushable(targetPos, unEquipableItem), ErrorItem::IsPushableFailed);

	TransactionPtr transPtr(NEW ItemUnequipTransaction(__FUNCTION__, __LINE__, player, LogCode::E_ITEM_UNEQUIP));
	{
		ItemData tarItemCloned = unEquipableItem->Clone();
		Inven::FillUpWithPos(player, targetPos, tarItemCloned);

		VERIFY_RETURN(transPtr->Bind(NEW ItemUpdateProceduere(player), unEquipableItem, tarItemCloned), ErrorItem::TranBindFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::TranRegisterFailed);
	}

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerInventory::EquipExchange(const ItemPlace& srcPlace, const ItemPlace& tarPlace)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::E_FAILED);

	// 둘다 착용 장비일 경우 => SwapSlot에서 처리해야 함
	//! src : Equip 장비를 기준으로 한다.

	VERIFY_RETURN(srcPlace.IsEquipBag() && srcPlace != tarPlace, ErrorItem::E_INVALID_SLOT);
	VERIFY_RETURN(player->IsNotTrading(), ErrorItem::E_TRADING);	

	Inven* srcInven = GetInven(srcPlace);
	VERIFY_RETURN(srcInven, ErrorItem::inventoryNotFound);
	VERIFY_RETURN(srcInven->IsUsableRemoteNpcFunction(), ErrorItem::IsUsableRemoteNpcFunctionFailed);	

	Inven* tarInven = GetInven(tarPlace);
	VERIFY_RETURN(tarInven, ErrorItem::inventoryNotFound);
	VERIFY_RETURN(tarInven->IsUsableRemoteNpcFunction(), ErrorItem::IsUsableRemoteNpcFunctionFailed);
	
	const ItemConstPtr source = GetItemByPlace(srcPlace);
	const ItemConstPtr target = GetItemByPlace(tarPlace);

	VERIFY_RETURN(source, ErrorItem::E_NULL_SLOT);
	VERIFY_RETURN(target, ErrorItem::E_NULL_SLOT);

	VERIFY_RETURN(IsPopable(source), ErrorItem::IsPopableFailed);
	VERIFY_RETURN(IsPopable(target), ErrorItem::IsPopableFailed);

	VERIFY_RETURN(IsPushable(srcPlace, target), ErrorItem::IsPushableFailed);
	VERIFY_RETURN(IsPushable(tarPlace, source), ErrorItem::IsPushableFailed);

	ItemConstPtr unEquipItem2(NULL);
	ItemPos unEquipFreeSlot2;
		
	const ItemConstPtr pLweapon = srcInven->GetItemBySlot(SlotType::LWEAPON);
	VALID_DO(pLweapon, AllowNull);
	
	if (pLweapon && target->IsTwoHandWeapon() )
	{
		VERIFY_RETURN(GetFreeSlot(pLweapon->GetInfoElem().GetInvenRange4Pushable(), OUT unEquipFreeSlot2 ), ErrorItem::E_INVEN_FULL)
		unEquipItem2 = pLweapon;
	}
	
	VERIFY_RETURN(target->IsIdentified(), ErrorItem::E_UNIDENTIFIED_ITEM);

	TransactionPtr transPtr(NEW ItemEquipExchangeTransaction(__FUNCTION__, __LINE__, player, LogCode::E_ITEM_EQUIP_EXCHANGE));
	{
#ifdef __delete_itemMove__hwayeong180822
#else
		ItemMoveInfo info;
		source->FillUp(info.source);		
		info.source.SetPos(tarPlace.GetPos());
		target->FillUp(info.target);		
		info.target.SetPos(srcPlace.GetPos());
		VERIFY_RETURN(transPtr->Bind(NEW MoveItemsProceduere(player), info), ErrorItem::TranBindFailed);
#endif

		ItemData equipItemCloned = source->Clone();
		Inven::FillUpWithPos(player, tarPlace, equipItemCloned);
		VERIFY_RETURN(transPtr->Bind(NEW ItemUpdateProceduere(player), source, equipItemCloned), ErrorItem::TranBindFailed);


		ItemData unEquipItem1Cloned = target->Clone();
		Inven::FillUpWithPos(player,srcPlace, unEquipItem1Cloned);
		VERIFY_RETURN(transPtr->Bind(NEW ItemUpdateProceduere(player), target, unEquipItem1Cloned), ErrorItem::TranBindFailed);
		

		if (unEquipItem2)
		{
			Inven* unEquip2Inven = GetInven(unEquipFreeSlot2);
			VERIFY_RETURN(unEquip2Inven, ErrorItem::inventoryNotFound);
			ItemData unEquipItem2Cloned = unEquipItem2->Clone();			
			Inven::FillUpWithPos(player, unEquipFreeSlot2, unEquipItem2Cloned);
			VERIFY_RETURN(transPtr->Bind(NEW ItemUpdateProceduere(player), unEquipItem2, unEquipItem2Cloned), ErrorItem::TranBindFailed);
		}

		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::E_TRANSACION_FAILED);
	}

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerInventory::Move(const ItemPlace& srcPlace, const ItemPos& tarPos)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);
	VERIFY_RETURN(player->IsTutorialCompleted(), ErrorItem::MustTutoralStateIsCompleted);
	VERIFY_RETURN(player->IsNotTrading(), ErrorItem::E_TRADING);

	if (IsRequireEquipProtocol(srcPlace, tarPos))
	{
		// equip, unequip 함수에서 처리함
		return ErrorItem::E_INVALID_EQUIPTYPE;
	}

	Inven* srcInven = GetInven(srcPlace);
	VERIFY_RETURN(srcInven, ErrorItem::inventoryNotFound);
	VERIFY_RETURN(srcInven->IsUsableRemoteNpcFunction(), ErrorItem::IsUsableRemoteNpcFunctionFailed);

	Inven* tarInven = GetInven(tarPos);
	VERIFY_RETURN(tarInven, ErrorItem::inventoryNotFound);
	VERIFY_RETURN(tarInven->IsUsableRemoteNpcFunction(), ErrorItem::IsUsableRemoteNpcFunctionFailed);

	const ItemConstPtr sourceItem = GetItemByPlace(srcPlace);
	VERIFY_RETURN(sourceItem, ErrorItem::itemNotFound);

	VERIFY_RETURN(IsPopable(sourceItem), ErrorItem::IsPopableFailed);	
	VERIFY_RETURN(IsPushable(tarPos, sourceItem), ErrorItem::IsPushableFailed);
	VERIFY_RETURN(IsFreeSlot(tarPos), ErrorItem::slotAlreadyExistedItem);
	
	TransactionPtr transPtr(NEW ItemMoveTransaction(__FUNCTION__, __LINE__, player, LogCode::E_ITEM_MOVE));
	{
		ItemData tarItemCloned(sourceItem->Clone());
		Inven::FillUpWithPos(player, tarPos, tarItemCloned);

		VERIFY_RETURN(transPtr->Bind(NEW ItemUpdateProceduere(player), sourceItem, tarItemCloned), ErrorItem::TranBindFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::E_TRANSACION_FAILED);
	}	

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerInventory::RemoveByPlace(const ItemPlace& itemPlace, ItemBag& bag, const Int32 removableCount) const
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	VALID_RETURN(removableCount > 0, ErrorItem::SUCCESS);

	const ItemConstPtr item = GetItemByPlace(itemPlace);
	VERIFY_RETURN(item, ErrorItem::itemNotFound);	
	VERIFY_RETURN(IsPopable(item), ErrorItem::IsPopableFailed);

	Int32 curStack = item->GetCurStackCount();
	VERIFY_RETURN(removableCount <= curStack, ErrorItem::stackCountNotEnought);

	if (curStack == removableCount)
	{
		VERIFY_RETURN(bag.Remove(*item), ErrorItem::BagRemoveFailed);
	}
	else
	{
		ItemData tarItemCloned = item->Clone();
		tarItemCloned.SetCurStackCount(curStack - removableCount);

		VERIFY_RETURN(bag.Update(item, tarItemCloned), ErrorItem::BagUpdateFailed);
	}

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerInventory::RemoveByIndex(const InvenRange& invenRange, ItemBag& bag, const IndexItem& indexItem, const Int32 removableCount) const
{
	VALID_RETURN(removableCount > 0, ErrorItem::SUCCESS);

	Int32 remainProcessedCount = removableCount;	
	
	for ( const eInvenType& eInven : invenRange.ToInvens())	
	{
		Inven* foundInven = GetInven(eInven);
		VALID_DO(foundInven && foundInven->IsActivated(Inven::SUB), continue);
		VALID_DO(foundInven->RemoveByIndex(bag, indexItem, remainProcessedCount), continue);
		break;
	}

	if (remainProcessedCount != 0)
		return ErrorItem::E_INVALID_STACK_VALUE;

	return ErrorItem::SUCCESS;
}



ErrorItem::Error ActionPlayerInventory::RemoveByDivision(const InvenRange& range, ItemDivisionType::Enum division, Int32 removableCount)
{
	VALID_RETURN(removableCount > 0, ErrorItem::SUCCESS);

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), ErrorItem::playerNotFound);

	constItems foundItems;
	ItemFinderByDivision(*owner, division).Find(range, foundItems);

	// 기간제(남은시간이 적은게 앞), 무기한 아이템 순으로 정렬한다.
	std::sort(foundItems.begin(), foundItems.end(), [](ItemConstPtr& left, ItemConstPtr& right)
	{
		VALID_RETURN(false == left->period.HasExpirationDate() || false == right->period.HasExpirationDate(), false);
		VALID_RETURN(false == left->period.HasExpirationDate(), false);
		VALID_RETURN(false == right->period.HasExpirationDate(), true);

		return left->period < right->period;
	});


	ItemBag removeBag(*owner);

	Int32 removeTotalStack = removableCount;
	Int32 removeStack = 0;
	for (const ItemConstPtr& item : foundItems)
	{
		if (removeTotalStack > item->GetCurStackCount())
		{
			removeStack = item->GetCurStackCount();
			removeTotalStack -= item->GetCurStackCount();
		}
		else
		{
			removeStack = removeTotalStack;
			removeTotalStack = 0;
		}

		ErrorItem::Error eError = RemoveByPlace(item->GetPlace(), removeBag, removeStack);
		VERIFY_RETURN(eError == ErrorItem::SUCCESS, eError);
		VALID_DO(removeTotalStack > 0, continue);
		
		break;
	}

	VERIFY_RETURN(removeTotalStack == 0, ErrorItem::invalidStackCount);

	ItemSystemRemoveTransaction* trans = NEW ItemSystemRemoveTransaction(__FUNCTION__, __LINE__, owner, LogCode::E_ITEM_SYSTEM_REMOVE, true);
	TransactionPtr transPtr = TransactionPtr(trans);
	{
		for (const ItemConstPtr&  removeItem : foundItems)
			trans->SetLogItem(ItemData(*removeItem));

		VERIFY_RETURN(removeBag.Write(transPtr), ErrorItem::BagWriteFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::TranRegisterFailed);
	}

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerInventory::RemoveByIndex(const InvenRange& range, const IndexItem& indexItem, Int32 removableCount, const Bool notice ) const
{
	VALID_RETURN(removableCount > 0, ErrorItem::SUCCESS);

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);	

	VERIFY_RETURN(IsEnoughItem(range, indexItem, removableCount), ErrorItem::CheckItemCountFailed);
		
	ItemSystemRemoveTransaction* tran = NEW ItemSystemRemoveTransaction(__FUNCTION__, __LINE__, player, LogCode::E_ITEM_SYSTEM_REMOVE, notice);
	TransactionPtr transPtr = TransactionPtr(tran);
	{
		tran->SetLogItem(ItemData(indexItem, removableCount));
		tran->Remove(ItemData(indexItem, removableCount));
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::TranRegisterFailed);
	}

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerInventory::SwapSlot(const ItemPlace& srcPlace, const ItemPlace& tarPlace)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), ErrorItem::playerNotFound);
	VERIFY_RETURN(owner->IsNotTrading(), ErrorItem::E_TRADING);
		
	if (IsRequireEquipProtocol(srcPlace, tarPlace))
	{
		return ErrorItem::E_INVALID_INVENTORY;
	}

	VERIFY_RETURN(tarPlace != srcPlace, ErrorItem::E_INVALID_SLOT);

	Inven* srcInven = GetInven(srcPlace);
	VERIFY_RETURN(srcInven, ErrorItem::inventoryNotFound);
	VERIFY_RETURN(srcInven->IsUsableRemoteNpcFunction(), ErrorItem::IsUsableRemoteNpcFunctionFailed);

	Inven* tarInven = GetInven(tarPlace);
	VERIFY_RETURN(tarInven, ErrorItem::inventoryNotFound);
	VERIFY_RETURN(tarInven->IsUsableRemoteNpcFunction(), ErrorItem::IsUsableRemoteNpcFunctionFailed);

	const ItemConstPtr sourceItem = GetItemByPlace(srcPlace);
	const ItemConstPtr targetItem = GetItemByPlace(tarPlace);
	VERIFY_RETURN(sourceItem && targetItem, ErrorItem::itemNotFound);

	VERIFY_RETURN(IsPopable(sourceItem), ErrorItem::IsPopableFailed);
	VERIFY_RETURN(IsPopable(targetItem), ErrorItem::IsPopableFailed);

	VERIFY_RETURN(IsPushable(tarPlace, sourceItem), ErrorItem::IsPushableFailed);
	VERIFY_RETURN(IsPushable(srcPlace, targetItem), ErrorItem::IsPushableFailed);

#ifdef __delete_itemMove__hwayeong180822
	ItemExchangeTransaction *tran = NEW ItemExchangeTransaction( __FUNCTION__, __LINE__, owner, LogCode::E_ITEM_EXCHANGE );
	TransactionPtr transPtr(tran);
	{
		ItemData from, to;
		from = sourceItem->Clone();
		to = targetItem->Clone();

		Inven::FillUpWithPos( owner, tarPlace, from );
		Inven::FillUpWithPos( owner, srcPlace, to );

		VERIFY_RETURN( transPtr->Bind( NEW ItemUpdateProceduere( owner ), sourceItem, from ), ErrorItem::TranBindFailed );
		VERIFY_RETURN( transPtr->Bind( NEW ItemUpdateProceduere( owner ), targetItem, to ), ErrorItem::TranBindFailed );

		tran->SetExchange( from, to );
#else
	TransactionPtr transPtr( NEW ItemExchangeTransaction( __FUNCTION__, __LINE__, owner, LogCode::E_ITEM_EXCHANGE ) );
	{
		ItemMoveInfo info;
		sourceItem->FillUp( info.source );
		info.source.SetPos( tarPlace.GetPos() );

		targetItem->FillUp( info.target );
		info.target.SetPos( srcPlace.GetPos() );

		VERIFY_RETURN(transPtr->Bind(NEW MoveItemsProceduere(owner), info), ErrorItem::TranBindFailed);
#endif
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::TranRegisterFailed);
	}
	
	return ErrorItem::SUCCESS;
}


ErrorItem::Error ActionPlayerInventory::Seperate(const ItemPlace& srcPlace, const ItemPos& tarPos, const Int32 seperatableCount, TransactionPtr transPtr /*= nullptr */)
{
	VERIFY_RETURN(seperatableCount > 0, ErrorItem::invalidStackCount);

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	Inven* srcInven = GetInven(srcPlace);
	VERIFY_RETURN(srcInven, ErrorItem::inventoryNotFound);
	VERIFY_RETURN(srcInven->IsUsableRemoteNpcFunction(), ErrorItem::IsUsableRemoteNpcFunctionFailed);

	Inven* tarInven = GetInven(tarPos);
	VERIFY_RETURN(tarInven, ErrorItem::inventoryNotFound);
	VERIFY_RETURN(tarInven->IsUsableRemoteNpcFunction(), ErrorItem::IsUsableRemoteNpcFunctionFailed);
	
	const ItemConstPtr source = GetItemByPlace(srcPlace);
	VERIFY_RETURN(source, ErrorItem::E_NULL_SLOT);
	VERIFY_RETURN(source->GetCurStackCount() > seperatableCount, ErrorItem::E_FAILED);
	VALID_RETURN( source->period.IsExpiredItem() == false, ErrorItem::E_EXPIRED_ITEM );
#ifdef __lockable_item_181001
	VALID_DO( source->IsUnLocakble(), SendSystemMsg( player, SystemMessage( L"sys", L"Msg_Item_locked_not_divide" ) ); return ErrorItem::E_ITEM_LOCKED );
#endif

	VERIFY_RETURN(IsPopable(source), ErrorItem::IsPopableFailed);
	VERIFY_RETURN(IsPushable(tarPos, source), ErrorItem::IsPushableFailed);

	if (false == tarInven->IsRebuyBag() && GetItem(tarPos) )
	{
		return ErrorItem::E_EXIST_ITEM;
	}

	Bool isSeperate = false;
	if (transPtr == nullptr)
	{
		isSeperate = true;
		transPtr = TransactionPtr(NEW ItemSeperateTransaction(__FUNCTION__, __LINE__, player, LogCode::E_ITEM_SEPARATE));
	}
	{
		ItemData sourceCloned(source->Clone());
		sourceCloned.SetCurStackCount(source->GetCurStackCount() - seperatableCount);

		ItemData targetCloned(source->GetItemIndex(), seperatableCount);		
		targetCloned.time = static_cast<UInt32>(time(NULL));
		Inven::FillUpWithPos(player, tarPos, targetCloned);

		VERIFY_RETURN(transPtr->Bind(NEW ItemUpdateProceduere(player), source, sourceCloned), ErrorItem::TranBindFailed);
		VERIFY_RETURN(transPtr->Bind(NEW ItemInsertProceduere(player), targetCloned), ErrorItem::TranBindFailed);

		if (isSeperate)
		{
			VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::E_TRANSACION_FAILED);
		}
	}

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerInventory::Overlap(const ItemPlace& srcPlace, const ItemPlace& tarPlace)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	Inven* srcInven = GetInven(srcPlace);
	VERIFY_RETURN(srcInven, ErrorItem::inventoryNotFound);
	VERIFY_RETURN(srcInven->IsUsableRemoteNpcFunction(), ErrorItem::IsUsableRemoteNpcFunctionFailed);

	Inven* tarInven = GetInven(tarPlace);
	VERIFY_RETURN(tarInven, ErrorItem::inventoryNotFound);
	VERIFY_RETURN(tarInven->IsUsableRemoteNpcFunction(), ErrorItem::IsUsableRemoteNpcFunctionFailed);	

	const ItemConstPtr source = GetItemByPlace(srcPlace);
	const ItemConstPtr target = GetItemByPlace(tarPlace);
	
	VERIFY_RETURN(source, ErrorItem::E_NULL_SLOT);
	VERIFY_RETURN(target, ErrorItem::E_NULL_SLOT);

	VALID_RETURN( source->period.IsExpiredItem() == false, ErrorItem::E_EXPIRED_ITEM );
	VALID_RETURN( target->period.IsExpiredItem() == false, ErrorItem::E_EXPIRED_ITEM );

#ifdef __lockable_item_181001
	VALID_DO( source->IsUnLocakble(), SendSystemMsg( player, SystemMessage( L"sys", L"Msg_Item_locked_not_stack" ) ); return ErrorItem::E_ITEM_LOCKED );
	VALID_DO( target->IsUnLocakble(), SendSystemMsg( player, SystemMessage( L"sys", L"Msg_Item_locked_not_stack" ) ); return ErrorItem::E_ITEM_LOCKED );
#endif

	VERIFY_RETURN(IsPopable(source), ErrorItem::IsPopableFailed);
	VERIFY_RETURN(IsPushable(tarPlace, source), ErrorItem::IsPushableFailed);

	VERIFY_RETURN(source != target, ErrorItem::MustNotSameItem);
	VERIFY_RETURN(source->GetItemIndex() == target->GetItemIndex(), ErrorItem::MustSameItemIndex);

	if (target->IsPeriodable())
	{
		VERIFY_RETURN(source->GetExpirationDate() == target->GetExpirationDate(), ErrorItem::E_DIFFRENT_COMPELTE_DATETIME);
	}

	Int32 srcStack = source->GetCurStackCount();
	Int32 tarStack = target->GetCurStackCount();
	Int32 maxStack = source->GetMaxStackCount();

	VERIFY_RETURN(srcStack > 0 && tarStack > 0, ErrorItem::E_INVALID_STACK_VALUE);
	VERIFY_RETURN(srcStack <= maxStack && tarStack <= maxStack, ErrorItem::E_MAX_STACK);	

	const Int32 totalStack = srcStack + tarStack;

	if (totalStack > maxStack)
	{
		srcStack = maxStack;
		tarStack = totalStack - maxStack;
	}	
	else
	{
		srcStack = totalStack;
		tarStack = 0;
	}


	TransactionPtr transPtr(NEW ItemOverlapTransaction(__FUNCTION__, __LINE__, player, LogCode::E_ITEM_OVERLAP));
	{
		ItemData sourceItemCloned = source->Clone();
		sourceItemCloned.SetCurStackCount(srcStack);
		VERIFY_RETURN(transPtr->Bind(NEW ItemUpdateProceduere(player), source, sourceItemCloned), ErrorItem::TranBindFailed);

		ItemData targetItemCloned = target->Clone();
		targetItemCloned.SetCurStackCount(tarStack);

		if (tarStack == 0)
		{
			VERIFY_RETURN(transPtr->Bind(NEW ItemDeleteProceduere(player), targetItemCloned), ErrorItem::TranBindFailed);
		}
		else
		{
			VERIFY_RETURN(transPtr->Bind(NEW ItemUpdateProceduere(player), target, targetItemCloned), ErrorItem::TranBindFailed);
		}

		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::E_TRANSACION_FAILED);
	}

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerInventory::PushToAcquirableBag(const IndexItem& indexItem, const Int32& count, const MailData& mail/* = MailData()*/)
{
	return PushToAcquirableBag(ItemData(indexItem, count), mail);
}

ErrorItem::Error ActionPlayerInventory::PushToAcquirableBag(const ItemData& pushItem, const MailData& mail /*= MailData()*/)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	ItemSystemPushTransaction* trans = NEW ItemSystemPushTransaction(__FUNCTION__, __LINE__, player, LogCode::E_ITEM_SYSTEM_PUSH, mail);
	TransactionPtr transPtr(trans);
	{
		trans->Push(pushItem);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::TranRegisterFailed);
	}
	
	return ErrorItem::SUCCESS;
}

Bool ActionPlayerInventory::HasItem(const InvenRange& range, const IndexItem& indexItem)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), false);

	Items founds;
	return ItemFinderByIndex(*owner, indexItem).Find(range, founds, 1);	
}

Bool ActionPlayerInventory::HasDivisionItem(const InvenRange& range, ItemDivisionType::Enum division) const
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), false);

	Items founds;
	return ItemFinderByDivision(*owner, division).Find(range, founds, 1);
}

Int32 ActionPlayerInventory::GetItemCount(const InvenRange& range, const IndexItem& indexItem)
{
	VALID_RETURN( 0 < indexItem, 0);

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), 0);

	const ItemInfoElem* elem = SCRIPTS.GetItemInfoScript(indexItem);
	VERIFY_RETURN(elem, 0);

	Items foundItems;
	ItemFinderByIndex(*owner, indexItem).Find(range, foundItems);

	Int32 foundStackCount = 0;
	for (const ItemConstPtr& found : foundItems)
	{
		foundStackCount += found->GetCurStackCount();
	}

	return foundStackCount;
}

// 원하는 갯수만큼 찾으면 리턴 true

Bool ActionPlayerInventory::IsEnoughItem(const InvenRange& range, const IndexItem& indexItem, Int32 findCount) const
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), false);

	Items founds;
	return ItemFinderByIndex(*owner, indexItem).Find(range, founds, findCount);
}

Bool ActionPlayerInventory::GetFreeSlot(const InvenRange& range, OUT ItemPos& pos) const
{	
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), false);

	Slots foundSlots;	
	VALID_RETURN(SlotFinderFree(*owner).Find(range, foundSlots, 1), false);
	pos = foundSlots.front();
	return true;
}

Int32 ActionPlayerInventory::GetFreeSlotCount(const InvenRange& range) const
{
	Int32 count = 0;

	for( const eInvenType& eInven : range.ToInvens() )
	{
		Inven* foundInven = GetInven(eInven);
		VALID_DO(foundInven && foundInven->IsActivated(Inven::ADD), continue);

		count += foundInven->GetFreeSlotCount();
	}

	return count;
}

Bool ActionPlayerInventory::IsEnoughFreeSlot(const InvenRange& range, Int32 requireSlotCount) const
{
	VALID_RETURN( 0 < requireSlotCount, true);

	for (const eInvenType& eInven : range.ToInvens())
	{
		Inven* foundInven = GetInven(eInven);
		VALID_DO(foundInven && foundInven->IsRightTarget(), continue);

		requireSlotCount -= foundInven->GetFreeSlotCount();
		VALID_DO(requireSlotCount <= 0, continue);

		return true;
	}

	return false;
}

// indexItem 아이템을 인벤에 넣기위해 추가적으로 필요한 슬롯갯수 계산
// 필요한 슬롯 갯수를 리턴
//[MUSTBE BY MOOK] - 리턴 0이 성공인지 실패인지 불확실하다.
// 아이템 엘름이 없어서 0을 리턴하는 경우와 필요슬롯이 필요없어서 0을 리턴하는 경우를 구분할 수 없다.

Int32 ActionPlayerInventory::GetFreeSlotCountByIndex(const InvenRange& range, const IndexItem indexItem, const Int32 requiredStackCount) const
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), 0);

	const ItemInfoElem* elem = SCRIPTS.GetItemInfoScript(indexItem);
	VERIFY_RETURN(elem, 0);

	Int32 needStackCount = 0;
	
	if (elem->GetMaxStackCount() == 1)
	{
		needStackCount = requiredStackCount;
	}
	else
	{
		Int32 canStackCount = 0;
		Items foundItems;
		if (true == ItemFinderStackCountByIndex(*owner, indexItem, canStackCount).Find(range, foundItems, requiredStackCount))
		{
			// 해당아이템을 스택하기에 충분하다. 빈슬롯이 필요없다.
			return 0;
		}
		needStackCount = requiredStackCount - canStackCount;
	}	

	return elem->GetNeedSlotCount(needStackCount);
}

ErrorItem::Error ActionPlayerInventory::Sell(const ItemSlotInfo& slotInfo, TransactionPtr transPtr, Bool isPushRebuyBag)
{
	VERIFY_RETURN(transPtr, ErrorItem::E_FAILED);

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	const ItemConstPtr item = GetItemByPlace(slotInfo);
	VERIFY_RETURN(item, ErrorItem::E_NULL_SLOT);
	VERIFY_RETURN(item->IsSellable(), ErrorItem::E_NOT_SELLABLE);
	
	Int32 curStackCount = item->GetCurStackCount();
	Int32 sellCount = slotInfo.GetCurStackCount();
	VERIFY_RETURN(0 < sellCount && sellCount <= curStackCount, ErrorItem::E_INVALID_STACK_VALUE);

	if (sellCount < curStackCount)
	{
		ErrorItem::Error eErrorItem = Seperate(slotInfo, ItemPosRebuy, sellCount, transPtr);
		VERIFY_RETURN(eErrorItem == ErrorItem::SUCCESS, eErrorItem);
	}
	else
	{

		if (isPushRebuyBag)
		{
			ItemData sellItemCloned = item->Clone();
			sellItemCloned.time = static_cast<UInt32>(time(NULL));
			sellItemCloned.SetPos(ItemPosRebuy);

			VERIFY_RETURN(transPtr->Bind(NEW ItemUpdateProceduere(player), item, sellItemCloned), ErrorItem::E_TRANSACION_FAILED);
		}
		else
		{
			VERIFY_RETURN(transPtr->Bind(NEW ItemDeleteProceduere(player), item->Clone()), ErrorItem::TranBindFailed);
		}
	}

	return ErrorItem::SUCCESS;
}

ItemPtr ActionPlayerInventory::GetItemById(const ItemPos& pos, ItemId itemId)
{
	Inven* foundInven = GetInven(pos);
	VALID_RETURN(foundInven && foundInven->IsActivated(Inven::SUB), nullptr);
	return foundInven->GetItemById(itemId);
}

ItemPtr ActionPlayerInventory::GetItemById(const InvenRange& range, const ItemId itemId)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), NULL);

	Items foundItems;
	VALID_RETURN(ItemFinderById(*owner,itemId, true, true).Find(range, foundItems, 1), NULL);
	return foundItems.front();
}

ErrorItem::Error ActionPlayerInventory::PickupItems(const ItemDatas& items, LogCode::Enum logCode)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	ItemUserPushTransaction* trans = NEW ItemUserPushTransaction(__FUNCTION__, __LINE__, player, logCode);
	TransactionPtr transPtr(trans);
	{
		for (const ItemData& item : items)
		{
			AchievementPickupItemIndexEvent	achievementEvent(player, item);
			player->GetAchievementAction().OnEvent(&achievementEvent);

			trans->Push(item);
			trans->SetLogItems(item);		// 180906 로그를 위해 추가. 이호석.
		}

		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::E_TRANSACION_FAILED);
	}
	
	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerInventory::PickupItem(const ItemData& item, LogCode::Enum logCode)
{
	ItemDatas items;
	items.push_back(item);
	return PickupItems(items, logCode);
}

ErrorItem::Error ActionPlayerInventory::CheckAndPickupZen(const Int32 zen, const Bool isBonus, __out Zen &totalZen)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN( owner && owner->IsValid(), ErrorItem::playerNotFound );

	const Int32 addGold = owner->GetAction< ActionAbility >()->GetAbilityValue(EAT::GET_GOLD_RATE);
	Int32 finalStack = 0;

	if (isBonus == TRUE)
		finalStack = std::max<Int32>(1, zen + static_cast<Int32>(zen * (addGold * DEFAULT_FLOATIZED_RATE)));
	else
		finalStack = zen;

	VALID_RETURN( owner->CheckZen( finalStack + totalZen, ChangedMoney::ADD ), ErrorItem::E_ZEN_LIMIT );

	totalZen += finalStack;

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerInventory::CheckAndPickupGreenZen(const Int32 greenzen, const Bool isBonus, __out GreenZen &totalGreenZen)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), ErrorItem::playerNotFound);

	// 기타 능력치 버프 참조,
	const Int32 addGreenZen = owner->GetAbilityAction().GetAbilityValue(EAT::GET_GREEN_ZEN_RATE);
	Int32 finalStack = 0;

	if (isBonus == TRUE)
		finalStack = std::max<Int32>(1, greenzen + static_cast<Int32>(greenzen * addGreenZen * DEFAULT_FLOATIZED_RATE));
	else
		finalStack = greenzen;

	VALID_RETURN( owner->CheckGreenZen( finalStack + totalGreenZen, ChangedMoney::ADD ), ErrorItem::E_GREENZEN_LIMIT );

	totalGreenZen += finalStack;

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerInventory::PickupZen(const Zen zen)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), ErrorItem::playerNotFound);

	if (owner->CheckZen(zen, ChangedMoney::ADD) == FALSE)
		return ErrorItem::E_ZEN_LIMIT;

	owner->AddMoney(zen);
	owner->SendChangeMoney(zen, ChangedMoney::ADD);

	auto aCasterpassivity = GetOwnerPlayer()->GetAction<ActionPassivity>();
	if (nullptr != aCasterpassivity)
	{
		PassivityCastParam param;
		param.type = PassiveCastType::GET_ZEN;
		param.ownerUnit = GetOwnerPlayer();
		param.isSendSkillEventResult = true;
		aCasterpassivity->Cast(param);
	}

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerInventory::PickupGreenZen(const GreenZen greenZen)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), ErrorItem::playerNotFound);

	if (owner->CheckGreenZen(greenZen, ChangedMoney::ADD) == FALSE)
		return ErrorItem::E_GREENZEN_LIMIT;

	owner->AddGreenZen(greenZen);
	owner->SendChangeGreenZen(greenZen, ChangedMoney::ADD );

	{
		PassivityCastParam param;
		param.type = PassiveCastType::GET_GREENZEN;
		param.ownerUnit = owner;
		param.isSendSkillEventResult = true;
		owner->GetPassivityAction().Cast(param);
	}

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerInventory::PickupZen(const Int32 zen, const Bool isBonus)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), ErrorItem::playerNotFound);

	Zen finalStack = 0;

	ErrorItem::Error error = ErrorItem::SUCCESS;

	error = CheckAndPickupZen(zen, isBonus, finalStack);
	if (error != ErrorItem::SUCCESS)
		return error;

	error = PickupZen(finalStack);
	if (error != ErrorItem::SUCCESS)
		return error;

	EReqLogDb2* log = NEW EReqLogDb2;
	EventPtr logEventPtr(log);
	EventSetter::FillLogForEntity(LogCode::E_ITEM_PICKUP, owner, log->action);
	log->action.var64s.push_back(owner->GetZen());
	log->action.var64s.push_back(owner->GetGreenZen());
	log->action.var64s.push_back(finalStack);
	log->action.var64s.push_back(0);
	SendDataCenter(logEventPtr);

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerInventory::PickupGreenZen(const Int32 greenzen, const Bool isBonus)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), ErrorItem::playerNotFound);

	GreenZen finalStack = 0;
	ErrorItem::Error error = ErrorItem::SUCCESS;

	error = CheckAndPickupGreenZen(greenzen, isBonus, finalStack);
	if (error != ErrorItem::SUCCESS)
		return error;

	error = PickupGreenZen(finalStack);
	if (error != ErrorItem::SUCCESS)
		return error;

	EReqLogDb2* log = NEW EReqLogDb2;
	EventPtr logEventPtr(log);

	EventSetter::FillLogForEntity(LogCode::E_ITEM_PICKUP, owner, log->action);
	log->action.var64s.push_back(owner->GetZen());		// 최종 젠
	log->action.var64s.push_back(owner->GetGreenZen());		// 최종 마정석
	log->action.var64s.push_back(0);				// 획득 젠
	log->action.var64s.push_back(finalStack);				// 획득 마정석

	SendDataCenter(logEventPtr);

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerInventory::PickupRiftPoint(const Int32 riftPoint, Int32& remainRiftPoint)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), ErrorItem::playerNotFound);

	if (0 > riftPoint)
	{
		return ErrorItem::E_FAILED;
	}

	const Int32 addRiftPointRate = owner->GetAbilityAction().GetAbilityValue(EAT::GET_RIFTPOINT_RATE);
	const Int32 finalRiftPoint = std::max<Int32>(1, riftPoint + static_cast< Int32 >(riftPoint * addRiftPointRate * DEFAULT_FLOATIZED_RATE));

	UInt64 addRiftPoint = finalRiftPoint;
	if (false == owner->AddRiftPoint(addRiftPoint))
	{
		return ErrorItem::E_FAILED;
	}

	remainRiftPoint = static_cast<Int32>(riftPoint - addRiftPointRate);

	owner->SendChangeRiftPoint(addRiftPointRate, ChangedMoney::ADD );

	auto aCasterpassivity = GetOwnerPlayer()->GetAction<ActionPassivity>();
	if (nullptr != aCasterpassivity)
	{
		PassivityCastParam param;
		param.type = PassiveCastType::GET_RIFT_POINT;
		param.ownerUnit = GetOwnerPlayer();
		param.isSendSkillEventResult = true;
		aCasterpassivity->Cast(param);
	}

	EReqLogDb2* log = NEW EReqLogDb2;
	EventPtr logEventPtr(log);

	EventSetter::FillLogForEntity(LogCode::E_ITEM_PICKUP, GetOwnerPlayer(), log->action);
	log->action.var64s.push_back(0); // 젠
	log->action.var64s.push_back(0); // 마정석
	log->action.var64s.push_back(riftPoint); // 시공의 조각
	SendDataCenter(logEventPtr);

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerInventory::Pickup(const ItemData& droppedItem)
{
	if (SCRIPTS.IsZenType(droppedItem.indexItem))
	{
		return PickupZen(droppedItem.GetCurStackCount(), TRUE);
	}
	else if (SCRIPTS.IsGreenZenType(droppedItem.indexItem))
	{
		return PickupGreenZen(droppedItem.GetCurStackCount(), TRUE);
	}
	else
	{
		return PickupItem(droppedItem, LogCode::E_ITEM_PICKUP);
	}
}

ErrorItem::Error ActionPlayerInventory::SortAllBagItem(const eInvenType eInvenBegin, const eInvenType eInvenEnd)
{
	// 클라이언트에서 잘못준건 에러 처리한다.

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);
	VERIFY_RETURN(player->IsNotTrading(), ErrorItem::E_TRADING);

	VERIFY_RETURN(player->GetManagerItemCube().IsWorking() == false, ErrorItem::E_TRANSACION_FAILED);

	Inven* endInven = GetInven(eInvenEnd);
	VERIFY_RETURN(endInven && endInven->IsActivated(Inven::SUB), ErrorItem::E_HAVE_NO_QUALIFICATION_FOR_INVENTORY);

	TransactionPtr transPtr(NEW ItemSortTransaction(__FUNCTION__, __LINE__, player, LogCode::E_ITEM_SORT, InvenRange(eInvenBegin, eInvenEnd)));
	{
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::E_TRANSACION_FAILED);
	}

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerInventory::ClearBagByGMCommand()
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	// check lock first
	if (isWorking())
	{
		return ErrorItem::E_INVEN_LOCKED;
	}

	Int32 clearedCount = 0;

	// proceed
	for (const eInvenType& eInven : (InvenBags << PremiumBags).ToInvens())
	{
		Inven* foundInven = GetInven(eInven);
		VALID_DO(foundInven, continue);

		clearedCount += foundInven->ClearSlot();	
	}

	if (clearedCount > 0)
	{
		EReqDbClearBag* reqDb = NEW EReqDbClearBag;
		reqDb->accountId = player->GetAccountId();
		reqDb->charId = player->GetCharId();
		SERVER.SendToDb(player, EventPtr(reqDb));

		ENtfClearBag* ntf = NEW ENtfClearBag;
		SERVER.SendToClient(player, EventPtr(ntf));
	}

	return ErrorItem::SUCCESS;
}

void ActionPlayerInventory::log(const Byte& logType, const Int32& itemType, const UInt32& stack, const UInt64& key, const char*desc)
{
	AttributePlayer& attrPlayer = GetOwnerPlayer()->GetPlayerAttr();

	MU2_INFO_LOG(logType, L"accountId=%d;charName=%s;charId=%d;money=%I64u;itemType=%d;itemCount=%d;itemKey=%I64u;desc=%s",
		attrPlayer.accountId, attrPlayer.charName.c_str(), attrPlayer.charId, static_cast<Zen>(attrPlayer.money), itemType, stack, key, desc);
}

ErrorItem::Error ActionPlayerInventory::Extracts(const Slots& posList)
{
	VERIFY_RETURN(posList.size(), ErrorItem::E_EMPTY_SLOT );

	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), ErrorItem::playerNotFound);
		
	VERIFY_RETURN(owner->GetMembershipAction().CanUseRemoteNpcFunction(NpcFunctionType::NPC_FUNCTION_ITEM_DISASSEMBLE, 0), ErrorItem::UseRemoteNpcFunctionFailed);

	ItemBag succeedBag(*owner);
	ItemDatas failed;
	ItemDatas rewards;
	UInt64 greenZen = 0;

	for (auto& targetPos : posList)
	{
		ErrorItem::Error eError = extract(succeedBag, targetPos, failed, rewards, greenZen);
		if (eError != ErrorItem::SUCCESS)
		{
			SystemMessage sysMsg(L"sys", L"Msg_ItemExtract_Err_InvalidType");
			SendSystemMsg(owner, sysMsg);
			return eError;
		}
	}

	ItemExtractTransaction* trans = NEW ItemExtractTransaction(__FUNCTION__, __LINE__, owner, LogCode::E_ITEM_EXTRACT, greenZen);
	TransactionPtr transPtr(trans);
	{
		trans->SetRewards(rewards);

		if (false == succeedBag.Write(transPtr))
		{
			SystemMessage sysMsg(L"sys", L"Msg_ItemExtract_Err_InvalidType");
			SendSystemMsg(owner, sysMsg);
			return ErrorItem::BagWriteFailed;
		}

		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::TranRegisterFailed);
	}


	for (auto& reward : failed)
	{
		// 바닦에 떨구기
		
		VERIFY_DO(theDropSystem.DropFromInventory(owner, reward.indexItem, reward.GetCurStackCount()), MustErrorCheck);
		// 이건뭐 스폰도 못하면 큰일남
	}

	return ErrorItem::SUCCESS;
}



ErrorItem::Error ActionPlayerInventory::EtherExtracts(const Slots& posList)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), ErrorItem::playerNotFound);
		
	VERIFY_RETURN(owner->GetMembershipAction().CanUseRemoteNpcFunction(NpcFunctionType::NPC_FUNCTION_ETHER_EXTRACT, 0), ErrorItem::UseRemoteNpcFunctionFailed);

	ItemBag succeed(*owner);	
	EmotionPoint ep = 0;

	for (auto& targetPos : posList)
	{
		ErrorItem::Error eError = etherExtract(succeed, targetPos, ep);
		VERIFY_RETURN(eError == ErrorItem::SUCCESS, eError);
	}

	ItemEtherExtractTransaction * trans = NEW ItemEtherExtractTransaction(__FUNCTION__, __LINE__, owner, LogCode::E_ETHER_EXTRACT, ep);
	TransactionPtr transPtr(trans);
	{
		VERIFY_RETURN(succeed.Write(transPtr), ErrorItem::BagWriteFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::E_TRANSACION_FAILED);
	}

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerInventory::etherExtract(ItemBag& bag, const ItemPos& slotPos, OUT EmotionPoint& ep)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), ErrorItem::playerNotFound);
		
	if (owner->IsTrading())
	{
		SystemMessage sysMsg(L"sys", L"Msg_UserTrade_Invalid_Not_Work");
		SendSystemMsg(owner, sysMsg);
		return ErrorItem::E_TRADING;
	}

	if (owner->IsDead())
	{
		SystemMessage sysMsg(L"sys", L"Msg_ItemExtract_Error_Dead");
		SendSystemMsg(owner, sysMsg);
		return ErrorItem::playerNotAlive;
	}

	if (false == slotPos.IsInvenBags())
	{
		SystemMessage sysMsg(L"sys", L"Msg_ItemExtract_Err_EquipItem");
		SendSystemMsg(owner, sysMsg);
		return ErrorItem::E_INVALID_INVENTORY;
	}

	Inven* foundInven = GetInven(slotPos);
	VERIFY_RETURN(foundInven, ErrorItem::inventoryNotFound);

	const ItemConstPtr item = foundInven->GetItemBySlot(slotPos.GetSlot());
	if (item == nullptr || item->GetCurStackCount() == 0)
	{
		SystemMessage sysMsg(L"sys", L"Msg_Item_Err_StackValue");
		SendSystemMsg(owner, sysMsg);
		return ErrorItem::E_NOT_EXIST_ITEM;
	}

#ifdef __lockable_item_181001
	VALID_DO( item->IsUnLocakble(), SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Item_locked_not_usable" ) ); return ErrorItem::E_ITEM_LOCKED );
#endif

	if (item->GetEmotionPoint() == 0)
	{
		SystemMessage sysMsg(L"sys", L"Msg_ItemExtract_Err_InvalidType");
		SendSystemMsg(owner, sysMsg);
		return ErrorItem::E_INVALID_SCRIPT;
	}

	ItemData itemData;
	item->FillUpItemData(itemData);
	bag.Remove(itemData);

	ep += (item->GetEmotionPoint() * item->GetCurStackCount());

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerInventory::RearrangeEmotionBox()
{
	VALID_RETURN(m_emotionBoxes.resetCount < EmotionBoxEnum::MaxResetCount, ErrorItem::E_EMOTION_BOX_MAX_RESET_COUNT);
	VALID_RETURN(m_emotionBoxes.CanReset(), ErrorItem::E_EMOTION_BOX_ALL_NOT_OPEN);
		
	// fcs 에 요청하기.

	VERIFY_RETURN(false == EmotionGetBusy(), ErrorItem::E_EMOTION_BOX_PROCESSING);
	
	RedZen fee = SCRIPTS.GetEmotionBoxResetCost(m_emotionBoxes.resetCount);
	VALID_RETURN(fee > 0, ErrorItem::E_EMOTION_BOX_RESET_INFO);

	// ??  레드젠 요청이 들어가야 한다 : 월드와 소통해야 한다.
	EzwReqEmotionBoxReset * req = NEW EzwReqEmotionBoxReset;
	req->fee = fee;

	SERVER.SendToWorldServer(GetOwnerPlayer(), EventPtr(req));

	MU2_INFO_LOG(LogCategory::CONTENTS, "[Emotion] Reset Req to World ( fee = %I64d )", fee);
	
	return ErrorItem::SUCCESS;
}

void ActionPlayerInventory::CompleteEmotionBoxReset(RedZen fee, RedZen leftRedzen)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player, );

	EmotionSetBusy(false);

	std::vector<IndexEmotionBox> boxes;
	EResEmotionBoxRearrange * res = NEW EResEmotionBoxRearrange;
	ErrorItem::Error result = RefreshEmotionBox(m_emotionBoxes.resetCount + 1);

	res->resetCount = m_emotionBoxes.resetCount;
	res->emotionBoxes = m_emotionBoxes.emotionBoxes;
	res->result = result;

	SERVER.SendToClient(GetOwner(), EventPtr(res));
}

void ActionPlayerInventory::SetEmotionBoxState(UInt8 pos, IndexItem itemIndex, UInt32 dropCount)
{
	VERIFY_RETURN(pos >= EmotionBoxEnum::BoxStartPos && pos <= EmotionBoxEnum::CenterBoxPos, );

	m_emotionBoxes.emotionBoxes[pos].openState = EmotionBoxOpenState::Open;
	m_emotionBoxes.emotionBoxes[pos].rewardItem = itemIndex;
	m_emotionBoxes.emotionBoxes[pos].rewardCount = dropCount;
}

void ActionPlayerInventory::ResetEmotionBoxState(UInt8 pos)
{
	VERIFY_RETURN(pos >= EmotionBoxEnum::BoxStartPos && pos <= EmotionBoxEnum::CenterBoxPos, );

	m_emotionBoxes.emotionBoxes[pos].openState = EmotionBoxOpenState::Closed;
	m_emotionBoxes.emotionBoxes[pos].rewardItem = 0;
	m_emotionBoxes.emotionBoxes[pos].rewardCount = 0;
}


ErrorItem::Error ActionPlayerInventory::RefreshEmotionBox(UInt8 reset/* = 0*/)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player, ErrorItem::E_EMOTION_BOX_RESET_INFO);

	const EmotionBoxScript * script = SCRIPTS.GetScript<EmotionBoxScript>();
	VERIFY_RETURN(script, ErrorItem::E_EMOTION_BOX_RESET_INFO);

	std::vector<IndexEmotionBox> boxes;

	if (script->GetNewEmotionBox(reset, boxes))
	{
		m_emotionBoxes.InitBoxes(boxes);
		m_emotionBoxes.resetCount = reset;

		DateTime date = DateTime::GetPresentTime();
		date.GetAsSystemTime(m_emotionBoxes.resetDate);

		EReqDbEmotionBoxInfo * dbReq = NEW EReqDbEmotionBoxInfo;
		dbReq->charId = player->GetCharId();
		dbReq->boxes.resetCount = m_emotionBoxes.resetCount;
		dbReq->boxes.resetDate = m_emotionBoxes.resetDate;

		for (UInt8 i = 0; i <= EmotionBoxEnum::CenterBoxPos; ++i)
			dbReq->boxes.boxes[i] = m_emotionBoxes.emotionBoxes.at(i);

		SERVER.SendToDb(player, EventPtr(dbReq));

		return ErrorItem::SUCCESS;
	}
	else
	{
		return ErrorItem::E_EMOTION_BOX_RESET_INFO;
	}
}

void ActionPlayerInventory::GMEmotionboxResetCount()
{
	m_emotionBoxes.resetCount = 0;

	theGameMsg.SendGameDebugMsg(GetOwnerPlayer(), L"Emotion Reset Count Set Zero\n");
}


void ActionPlayerInventory::ShowEmotionboxInfo()
{
	wstring msg;
	wchar_t buf[20];
	msg += L"\r\nEmotion box Information\r\n";
	msg += L"Reset Count = ";
	_itow_s(static_cast<Int32>(m_emotionBoxes.resetCount), buf, 10);
	msg += buf;
	msg += L"\r\n";

	for (auto& i : m_emotionBoxes.emotionBoxes)
	{
		wstring one;

		_itow_s(static_cast<Int32>(i.pos), buf, 20);
		one += buf;
		one += L"(";

		_itow_s(static_cast<Int32>(i.openState), buf, 20);
		one += buf;
		one += L") - ";

		_itow_s(static_cast<Int32>(i.boxIndex), buf, 20);
		one += buf;
		one += L"/";

		_itow_s(static_cast<Int32>(i.rewardItem), buf, 20);
		one += buf;
		one += L"/";

		_itow_s(static_cast<Int32>(i.rewardCount), buf, 20);
		one += buf;
		one += L"\r\n";

		msg += one;
	}

	MU2_INFO_LOG(LogCategory::CONTENTS, msg.c_str());
}

void ActionPlayerInventory::BackEmotionboxDate()
{
	DateTime present = DateTime::GetPresentTime();
	present -= DateTimeSpan(1, 0, 0, 0);

	m_emotionBoxes.resetDate = present;

	theGameMsg.SendGameDebugMsg(GetOwnerPlayer(), L"Emotion Reset Date Back\n");
}



void ActionPlayerInventory::MarbleSetFromDB(AccountMarbleInfo& info)
{
	if (false == theEventSystem.IsActiveEventType(GameEventElem::MARBLE_EVENT))
	{
		// 이벤트 중이 아니라면, 셋팅할 필요도 없다.
		return;
	}

	auto marbleScript = SCRIPTS.GetScript<MarbleConditionScript>();

	//! 셋팅 순서 중요. 스크립트에서 포인트 획득 조건을 기본값으로 먼저 채운후, 계정 데이터를 채운다. 
	m_marbleInfo.ResetPointConditionData();
	for (auto& itr : marbleScript->m_elems)
	{
		m_marbleInfo.InitPointConditionData(itr);
	}

	if (info.GetVersion() != theMarbleSystem.GetVersion())
	{
		//! 초기화한다.
		m_marbleInfo.Reset();
		MarbleRecvRewardReset();
		m_marbleInfo.SerVersion(theMarbleSystem.GetVersion());

		MarbleDailyReset(false);
	}
	else
		m_marbleInfo.SetData(info);

	Int32 pc_count = 0;
	MarbleRewardBoxInfo &rInfo = m_marbleInfo.GetRewardData();
	if (0 == m_marbleInfo.GetRewardData().m_boxIndex)
	{
		auto boxScript = SCRIPTS.GetScript<MarbleBoxScript>();
		MarbleBoxElem * firstBox = boxScript->GetFirstBox();
		VERIFY_RETURN(firstBox, );

		rInfo.m_boxIndex = firstBox->boxIndex;
		rInfo.m_boxGrade = firstBox->grade;
		rInfo.m_boxOpenCount = 0;
		rInfo.m_boxExp = 0;
		rInfo.m_nextBoxIndex = firstBox->nextBox;
		pc_count = 1;
	}
	else
	{
		auto boxScript = SCRIPTS.GetScript<MarbleBoxScript>();
		MarbleBoxElem * curBox = boxScript->GetBox(rInfo.m_boxGrade);
		
		VERIFY_RETURN(curBox, );

		rInfo.m_nextBoxIndex = curBox->nextBox;
	}

	EntityPlayer * player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	MarblePlayPoint maxPoint = theMarbleSystem.GetMaxPlayPoint(player->IsPcRoom());
	m_marbleInfo.SetMaxPlayPoint(maxPoint);

	Bool needRefresh = !UseItemLimitInfo::IsDailyReset(m_marbleInfo.GetResetDate(), theMarbleSystem.GetResetHour());
	if (needRefresh)
	{
		MarbleDailyReset(false);
	}
	else
	{

#ifdef __Patch_LegendMarble_v4_raylee_2019_1_23
		{
			MarbleConditionArg arg(PlayPointConditionType::FirstConnect, player->IsPcRoom());

			std::vector<MarblePointConditionInfo> conInfos;
			if (MarbleCheckCondition(arg, conInfos))
			{
				++pc_count;

				for (auto &conInfo : conInfos)
				{
					EReqDbMarbleConditionUpdate * con = NEW EReqDbMarbleConditionUpdate;
					EventPtr conPtr(con);
					con->accGuid = player->GetAccountId();
					con->condition = conInfo;
					SERVER.SendToDb(player, conPtr);
				}
			}
		}

#else
		std::vector<MarblePlayPointConditionElem *> outPointConditions;
		theMarbleSystem.GetFirstConPointCondition(outPointConditions, player->IsPcRoom());

		// 최초(하루) 접속 보상 체크
		for (MarblePlayPointConditionElem * conElem : outPointConditions)
		{
			MarblePointConditionInfo conInfo;
			if (MarbleCheckPointCondition(conElem->index, conInfo))
			{
				++pc_count;

				EReqDbMarbleConditionUpdate * con = NEW EReqDbMarbleConditionUpdate;
				EventPtr conPtr(con);
				con->accGuid = player->GetAccountId();
				con->condition = conInfo;
				SERVER.SendToDb(player, conPtr);
			}
		}

#endif
		if (pc_count > 0)
			MarbleInfoUpdate();
	}
}

void ActionPlayerInventory::MarbleSendInfo(MarbleInfoResult::Enum en)
{
	EResMarbleInfo * resInfo = NEW EResMarbleInfo;
	EventPtr pack(resInfo);

	resInfo->infoResult = en;
	if (false == theEventSystem.IsActiveEventType(GameEventElem::MARBLE_EVENT))
	{
		//! 실패 보내기.
		resInfo->result = ErrorMarbleSystem::E_NOT_EVENT_PERIOD;

		SERVER.SendToClient(GetOwner(), pack);

		return;
	}

	EntityPlayer* ownPlayer = GetOwnerPlayer();
	VERIFY_DO(ownPlayer && ownPlayer->IsValid(), return);

	if (ownPlayer->GetLevel() < theMarbleSystem.GetMinLevel())
	{
		//! 실패 보내기.
		resInfo->result = ErrorMarbleSystem::E_CHAR_LEVEL;

		SERVER.SendToClient(ownPlayer, pack);

		SystemMessage msg(L"sys", L"Msg_Error_Requirement_CharLevel");
		msg.SetParam(L"str", theMarbleSystem.GetMinLevel());
		SendSystemMsg(ownPlayer, msg);
		return;
	}

	Bool needRefresh = !UseItemLimitInfo::IsDailyReset(m_marbleInfo.GetResetDate(), theMarbleSystem.GetResetHour());
	if (needRefresh)
	{
		MarbleDailyReset(true);
	}
	

	resInfo->result = ErrorMarbleSystem::SUCCESS;

	resInfo->curSequence = m_marbleInfo.GetCurSequence();
	
	resInfo->curPlayPoint = m_marbleInfo.GetCurPlayPoint();
	resInfo->maxPlayPoint = m_marbleInfo.GetMaxPlayPoint();

	if(ownPlayer->IsPcRoom())
		resInfo->rewardBoxExpBonus = m_marbleInfo.GetBoxExpBonus() * theMarbleSystem.GetPcRoomRewardExpBonus() / 100;
	else
		resInfo->rewardBoxExpBonus = m_marbleInfo.GetBoxExpBonus();
	resInfo->rewardInfo = m_marbleInfo.GetRewardData();

	m_marbleInfo.GetConditions(resInfo->pointConditions);
	resInfo->larbaHistory = m_marbleInfo.GetHistory();

	resInfo->curRedzenPlayCount = m_marbleInfo.GetCurRedzenPlayCount();	// 현재 플레이 포인트
	resInfo->maxRedzenPlayCount = theMarbleSystem.GetRedzenMaxPlayCount();	// 최대 플레이 포인트

	SERVER.SendToClient(GetOwner(), pack);

	if (m_marbleInfo.GetDiceNum() != 0)
	{
		MarbleArriveArea(true);
	}
}


#ifdef	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320
void ActionPlayerInventory::MarbleThrowDice(Int32 dice, CostPack& costPack)
{
	EntityPlayer * player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	auto fSendDiceResult = [player](ErrorMarbleSystem::Error er)
	{
		EResMarbleDiceResult * resInfo = NEW EResMarbleDiceResult;
		EventPtr pack(resInfo);

		resInfo->result = er;

		SERVER.SendToClient(player, pack);
	};

	if (false == theEventSystem.IsActiveEventType(GameEventElem::MARBLE_EVENT))
	{
		//! 실패 보내기.
		fSendDiceResult(ErrorMarbleSystem::E_NOT_EVENT_PERIOD);
		return;
	}

	if (player->GetLevel() < theMarbleSystem.GetMinLevel())
	{
		//! 실패 보내기.
		fSendDiceResult(ErrorMarbleSystem::E_CHAR_LEVEL);

		SystemMessage msg(L"sys", L"Msg_Error_Requirement_CharLevel");
		msg.SetParam(L"str", theMarbleSystem.GetMinLevel());
		SendSystemMsg(player, msg);
		return;
	}

	Bool needRefresh = !UseItemLimitInfo::IsDailyReset(m_marbleInfo.GetResetDate(), theMarbleSystem.GetResetHour());
	if (needRefresh)
	{
		MarbleDailyReset(true);

		ENtfMarbleInit * init = NEW ENtfMarbleInit;
		EventPtr pack(init);
		SERVER.SendToClient(player, pack);

		return;
	}

	if (0 != m_marbleInfo.GetDiceNum())
	{
		MU2_WARN_LOG(LogCategory::CONTENTS, "Not receive arrive packet for last throw");

		//! 실패 보내기.
		fSendDiceResult(ErrorMarbleSystem::E_NOTRECV_ARRIVE);
		return;
	}

	MarblePlayPoint needPoint = theMarbleSystem.GetPointPerPlay();
	if (0 == needPoint)
		needPoint = 1;

#ifdef	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320
	// 재화를 사용 한다면
	if (costPack.HasAnyCoin())
	{
		// 재화를 사용하는데 현재 플레이 포인트가 모아져 있는 상태라면 에러
		if (m_marbleInfo.GetCurPlayPoint() >= needPoint)
		{
			fSendDiceResult(ErrorMarbleSystem::E_REDZEN_NO_PLAYCHANCE);
			return;
		}

		if (false == theMarbleSystem.GetCostPackSet().CheckCorrectPayment(costPack))
		{
			fSendDiceResult(ErrorMarbleSystem::E_INVALID_PAYMENT_TYPE);
			return;
		}

		// 재화를 사용하는 처리
		if (MarbleIsUsingRedzenPlay())
		{
			fSendDiceResult(ErrorMarbleSystem::E_FAIL_FCS);
		}
		else
		{
			MarbleSetUsingRedzenPlay();

			EzwReqDicePlayRedzen * req = NEW EzwReqDicePlayRedzen;
			req->costPack = costPack;
			//req->fee = theMarbleSystem.GetRedzenPlayCost();

			SERVER.SendToWorldServer(player, EventPtr(req));
		}

		return;
	}
	else
	{
		//! 레드젠이 아닌 경우, 포인트가 없으면 실패. 
		if (m_marbleInfo.GetCurPlayPoint() < needPoint)
		{
			//! 실패 보내기 - 플레이 포인트가 모자름
			fSendDiceResult(ErrorMarbleSystem::E_NEED_MORE_PLAY_POINT);
			return;
		}
	}
#else	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320
	if (true == useRedzen)
	{
		//! 레드젠인 경우, 포인트가 없어야 한다. 그렇지 않으면 실패.
		ErrorMarbleSystem::Error er = ErrorMarbleSystem::SUCCESS;
		if (m_marbleInfo.GetCurPlayPoint() < needPoint)
		{
			if (m_marbleInfo.GetCurRedzenPlayCount() < theMarbleSystem.GetRedzenMaxPlayCount()
				&& theMarbleSystem.GetRedzenPlayCost() > 0)
			{
				if (MarbleIsUsingRedzenPlay())
					er = ErrorMarbleSystem::E_FAIL_FCS;
				else
				{
					MarbleSetUsingRedzenPlay();

					EzwReqDicePlayRedzen * req = NEW EzwReqDicePlayRedzen;
					req->fee = theMarbleSystem.GetRedzenPlayCost();

					SERVER.SendToWorldServer(player, EventPtr(req));
					return;		// 리턴해야 함!
				}
			}
			else
			{
				er = ErrorMarbleSystem::E_REDZEN_NO_MORE;
			}
		}
		else
			er = ErrorMarbleSystem::E_REDZEN_NO_PLAYCHANCE;

		fSendDiceResult(er);
		return;
	}
	else
	{
		//! 레드젠이 아닌 경우, 포인트가 없으면 실패. 
		if (m_marbleInfo.GetCurPlayPoint() < needPoint)
		{
			//! 실패 보내기 - 플레이 포인트가 모자름
			fSendDiceResult(ErrorMarbleSystem::E_NEED_MORE_PLAY_POINT);
			return;
		}
	}
#endif	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320


	//! 주사위 던지기 처리.
	if (0 == dice)
	{
		dice = theMarbleSystem.GetDiceNum();
	}

	Int8 orgDice = static_cast<Int8>(dice);

	if (0 != m_marbleInfo.GetLastLarbaCard())
	{
		auto script = SCRIPTS.GetScript<MarbleCardScript>();
		const MarbleCardElem * cardElem = script->GetCardElem(m_marbleInfo.GetLastLarbaCard());
#ifdef __Patch_LegendMarble_v4_raylee_2019_1_23
		Int32 multi = 1;

		switch (cardElem->condition)
		{
		case MarbleCardActionType::BackMoving:
			orgDice = static_cast<Int8>(dice * (-1));		// 뒤로 가는 건, 음수로 줘야 한다.
			multi = cardElem->values.size() > 0 ? cardElem->values.at(0) : 1;		// 뒤로 가는 기본값은 1 이다. 
			dice *= (-multi);
			break;
		case MarbleCardActionType::FrontMoving:
			multi = cardElem->values.size() > 0 ? cardElem->values.at(0) : 2;		// 기본값은 2다. 앞으로 움직이니까.
			dice *= multi;
			break;
		}
#else

		switch (cardElem->condition)
		{
		case MarbleCardActionType::BackMoving:
			dice *= (-1);
			orgDice = static_cast<Int8>(dice);		// 뒤로 가는 건, 음수로 줘야 한다.
			break;
		case MarbleCardActionType::FrontMoving:
			dice *= 2;
			break;
		}

#endif
	}

	m_marbleInfo.SetDiceNum(dice);

	UInt32 curSeq = m_marbleInfo.GetCurSequence();
	Int32 rotateCount = 0;

	MarbleAreaElem * nextArea = theMarbleSystem.Next(curSeq, dice, rotateCount);
	if (nullptr == nextArea)
	{
		//! 실패 보내기.
		fSendDiceResult(ErrorMarbleSystem::E_AREA_INFO);
		return;
	}

	MarbleSendDiceResultNLog(curSeq, nextArea->sequence, orgDice, needPoint, costPack);
}

#else	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320
void ActionPlayerInventory::MarbleThrowDice(Int32 dice /* = 0 */, Bool useRedzen /* = false */)
{
	EntityPlayer * player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	auto fSendDiceResult = [player](ErrorMarbleSystem::Error er)
	{
		EResMarbleDiceResult * resInfo = NEW EResMarbleDiceResult;
		EventPtr pack(resInfo);

		resInfo->result = er;

		SERVER.SendToClient(player, pack);
	};

	if (false == theEventSystem.IsActiveEventType(GameEventElem::MARBLE_EVENT))
	{
		//! 실패 보내기.
		fSendDiceResult(ErrorMarbleSystem::E_NOT_EVENT_PERIOD);
		return;
	}

	if (player->GetLevel() < theMarbleSystem.GetMinLevel())
	{
		//! 실패 보내기.
		fSendDiceResult(ErrorMarbleSystem::E_CHAR_LEVEL);

		SystemMessage msg(L"sys", L"Msg_Error_Requirement_CharLevel");
		msg.SetParam(L"str", theMarbleSystem.GetMinLevel());
		SendSystemMsg(player, msg);
		return;
	}

	Bool needRefresh = !UseItemLimitInfo::IsDailyReset(m_marbleInfo.GetResetDate(), theMarbleSystem.GetResetHour());
	if (needRefresh)
	{
		MarbleDailyReset(true);

		ENtfMarbleInit * init = NEW ENtfMarbleInit;
		EventPtr pack(init);
		SERVER.SendToClient(player, pack);

		return;
	}

	if (0 != m_marbleInfo.GetDiceNum())
	{
		MU2_WARN_LOG(LogCategory::CONTENTS, "Not receive arrive packet for last throw");

		//! 실패 보내기.
		fSendDiceResult(ErrorMarbleSystem::E_NOTRECV_ARRIVE);
		return;
	}

	MarblePlayPoint needPoint = theMarbleSystem.GetPointPerPlay();
	if (0 == needPoint)
		needPoint = 1;

	if (true == useRedzen)
	{
		//! 레드젠인 경우, 포인트가 없어야 한다. 그렇지 않으면 실패.
		ErrorMarbleSystem::Error er = ErrorMarbleSystem::SUCCESS;
		if (m_marbleInfo.GetCurPlayPoint() < needPoint)
		{
			if (m_marbleInfo.GetCurRedzenPlayCount() < theMarbleSystem.GetRedzenMaxPlayCount()
				&& theMarbleSystem.GetRedzenPlayCost() > 0)
			{
				if (MarbleIsUsingRedzenPlay())
					er = ErrorMarbleSystem::E_FAIL_FCS;
				else
				{
					MarbleSetUsingRedzenPlay();

					EzwReqDicePlayRedzen * req = NEW EzwReqDicePlayRedzen;
					req->fee = theMarbleSystem.GetRedzenPlayCost();

					SERVER.SendToWorldServer(player, EventPtr(req));
					return;		// 리턴해야 함!
				}
			}
			else
			{
				er = ErrorMarbleSystem::E_REDZEN_NO_MORE;
			}
		}
		else
			er = ErrorMarbleSystem::E_REDZEN_NO_PLAYCHANCE;

		fSendDiceResult(er);
		return;
	}
	else
	{
		//! 레드젠이 아닌 경우, 포인트가 없으면 실패. 
		if (m_marbleInfo.GetCurPlayPoint() < needPoint)
		{
			//! 실패 보내기 - 플레이 포인트가 모자름
			fSendDiceResult(ErrorMarbleSystem::E_NEED_MORE_PLAY_POINT);
			return;
		}
	}

	//! 주사위 던지기 처리.
	if (0 == dice)
	{
		dice = theMarbleSystem.GetDiceNum();
	}

	Int8 orgDice = static_cast<Int8>(dice);

	if (0 != m_marbleInfo.GetLastLarbaCard())
	{
		auto script = SCRIPTS.GetScript<MarbleCardScript>();
		const MarbleCardElem * cardElem = script->GetCardElem(m_marbleInfo.GetLastLarbaCard());

#ifdef __Patch_LegendMarble_v4_raylee_2019_1_23
		Int32 multi = 1;

		switch (cardElem->condition)
		{
		case MarbleCardActionType::BackMoving:
			orgDice = static_cast<Int8>(dice * (-1));		// 뒤로 가는 건, 음수로 줘야 한다.
			multi = cardElem->values.size() > 0 ? cardElem->values.at(0) : 1;		// 뒤로 가는 기본값은 1 이다. 
			dice *= (-multi);
			break;
		case MarbleCardActionType::FrontMoving:
			multi = cardElem->values.size() > 0 ? cardElem->values.at(0) : 2;		// 기본값은 2다. 앞으로 움직이니까.
			dice *= multi;
			break;
		}
#else

		switch (cardElem->condition)
		{
		case MarbleCardActionType::BackMoving:
			dice *= (-1);
			orgDice = static_cast<Int8>(dice);		// 뒤로 가는 건, 음수로 줘야 한다.
			break;
		case MarbleCardActionType::FrontMoving:
			dice *= 2;
			break;
		}

#endif
	}

	m_marbleInfo.SetDiceNum(dice);

	UInt32 curSeq = m_marbleInfo.GetCurSequence();
	Int32 rotateCount = 0;

	MarbleAreaElem * nextArea = theMarbleSystem.Next(curSeq, dice, rotateCount);
	if (nullptr == nextArea)
	{
		//! 실패 보내기.
		fSendDiceResult(ErrorMarbleSystem::E_AREA_INFO);
		return;
	}

	MarbleSendDiceResultNLog(curSeq, nextArea->sequence, orgDice, needPoint, useRedzen);
}

#endif	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320

#ifdef	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320
void ActionPlayerInventory::MarbleSendDiceResultNLog(UInt32 curSeq, UInt32 nextSeq, UInt32 diceResult, MarblePlayPoint needPoint, const CostPack& costPack)
#else	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320
void ActionPlayerInventory::MarbleSendDiceResultNLog(UInt32 curSeq, UInt32 nextSeq, UInt32 diceResult, MarblePlayPoint needPoint, Bool useRedzen)
#endif	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320

{
	EntityPlayer * player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	EReqLogDb2* log = NEW EReqLogDb2;
	EventPtr logEventPtr(log);
	EventSetter::FillLogForEntity(LogCode::E_MARBLE_THROW_DICE, player, log->action);

	StringUtil::format(log->action.subInfo, L"%u", nextSeq);

	// var32_0
	if (player->IsPcRoom())
		log->action.var32s.push_back(0);
	else
		log->action.var32s.push_back(1);

	log->action.var32s.push_back(diceResult);		// var32_1
	log->action.var32s.push_back(m_marbleInfo.GetCurPlayPoint());	// var32_2
	log->action.var32s.push_back(curSeq);	// var32_3
	log->action.var32s.push_back(nextSeq);	// var32_4
	if (0 == nextSeq)
		log->action.var32s.push_back(0);	// var32_5
	else
		log->action.var32s.push_back(1);
	//! 레드젠 사용 유무를 로그에 남긴다. 
#ifdef	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320
	Cost fcsCost = const_cast<CostPack&>(costPack).GetFcsCost();
	if (costPack.HasFcsCoin())
	{
		log->action.var32s.push_back(1);
	}
	else
	{
		log->action.var32s.push_back(0);
	}
#else	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320
	if (useRedzen)
		log->action.var32s.push_back(1);	// var32_6
	else
		log->action.var32s.push_back(0);
#endif	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320
	log->action.var32s.push_back(static_cast<Int32>(m_marbleInfo.GetCurRedzenPlayCount())); // var32_7

	SendDataCenter(logEventPtr);


	//! 주사위 결과를 알려주고
	EResMarbleDiceResult * resInfo = NEW EResMarbleDiceResult;
	EventPtr pack(resInfo);

	resInfo->result = ErrorMarbleSystem::SUCCESS;
	resInfo->diceNumber = static_cast<Int8>(diceResult);
	resInfo->lastSequence = nextSeq;

	SERVER.SendToClient(player, pack);

	MarblePlayPoint lastPoint = m_marbleInfo.SubPlayPoint(needPoint);

	MarbleInfoUpdate();

	ENtfMarbleChangePoint * ntfPoint = NEW ENtfMarbleChangePoint;
	EventPtr ntfPack(ntfPoint);

	ntfPoint->curPlayPoint = lastPoint;
	ntfPoint->pointConditionChanged.conditionIndex = 0;
	ntfPoint->pointConditionChanged.point = static_cast<UInt8>(needPoint);

	SERVER.SendToClient(player, ntfPack);
}

#ifdef	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320
void ActionPlayerInventory::MarbleRedzenPlay(ErrorWShop::Error result, const CostPack& costPack)
#else	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320
void ActionPlayerInventory::MarbleRedzenPlay(ErrorWShop::Error result, RedZen fee)
#endif	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320
{
	EntityPlayer * player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	MarbleResetUsingRedzenPlay();

	auto fSendDiceResult = [player](ErrorMarbleSystem::Error er)
	{
		EResMarbleDiceResult * resInfo = NEW EResMarbleDiceResult;
		EventPtr pack(resInfo);

		resInfo->result = er;

		SERVER.SendToClient(player, pack);
	};

	if (result == ErrorWShop::SUCCESS)
	{
		//! 횟수를 늘리고, 던지기를 수행한다.
		Int32 dice = theMarbleSystem.GetDiceNum();
		Int8 orgDice = static_cast<Int8>(dice);

		if (0 != m_marbleInfo.GetLastLarbaCard())
		{
			auto script = SCRIPTS.GetScript<MarbleCardScript>();
			const MarbleCardElem * cardElem = script->GetCardElem(m_marbleInfo.GetLastLarbaCard());

#ifdef __Patch_LegendMarble_v4_raylee_2019_1_23
			Int32 multi = 1;

			switch (cardElem->condition)
			{
			case MarbleCardActionType::BackMoving:
				orgDice = static_cast<Int8>(dice * (-1));		// 뒤로 가는 건, 음수로 줘야 한다.
				multi = cardElem->values.size() > 0 ? cardElem->values.at(0) : 1;		// 뒤로 가는 기본값은 1 이다. 
				dice *= (-multi);
				break;
			case MarbleCardActionType::FrontMoving:
				multi = cardElem->values.size() > 0 ? cardElem->values.at(0) : 2;		// 기본값은 2다. 앞으로 움직이니까.
				dice *= multi;
				break;
			}
#else
			switch (cardElem->condition)
			{
			case MarbleCardActionType::BackMoving:
				dice *= (-1);
				orgDice = dice;		// 뒤로 가는 건, 음수로 줘야 한다.
				break;
			case MarbleCardActionType::FrontMoving:
				dice *= 2;
				break;
			}
#endif
		}

		m_marbleInfo.SetDiceNum(dice);

		UInt32 curSeq = m_marbleInfo.GetCurSequence();
		Int32 rotateCount = 0;

		MarbleAreaElem * nextArea = theMarbleSystem.Next(curSeq, dice, rotateCount);
		if (nullptr == nextArea)
		{
			//! 실패 보내기.
			fSendDiceResult(ErrorMarbleSystem::E_AREA_INFO);
			return;
		}

		MarblePlayPoint needPoint = theMarbleSystem.GetPointPerPlay();
		if (0 == needPoint)
			needPoint = 1;

		m_marbleInfo.IncRedzenPlayCount();

		ENtfMarbleChangeRedzenPlayCount * pkCount = NEW ENtfMarbleChangeRedzenPlayCount;
		pkCount->curRedzenPlayCount = m_marbleInfo.GetCurRedzenPlayCount();
		EventPtr packCount(pkCount);
		SERVER.SendToClient(player, packCount);

#ifdef	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320
		MarbleSendDiceResultNLog(curSeq, nextArea->sequence, orgDice, needPoint, costPack);
#else	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320
		MarbleSendDiceResultNLog(curSeq, nextArea->sequence, orgDice, needPoint, true);
#endif	__Patch_Renewal_Use_Cost_Type_by_robinhwp_20190320
	}
	else
	{
		//! 실패를 알려주고 리턴.
		fSendDiceResult(ErrorMarbleSystem::E_FAIL_FCS);
	}
}




void ActionPlayerInventory::MarbleArriveArea(Bool client)
{
	EntityPlayer* ownPlayer = GetOwnerPlayer();
	VERIFY_RETURN(ownPlayer && ownPlayer->IsValid(), );

	EResMarbleAreaResult * res = NEW EResMarbleAreaResult;
	EventPtr resPack(res);
	
	//! 던전 결과가 없다면, 순서가 맞지 않게 온것이다. 
	if (0 == m_marbleInfo.GetDiceNum())
	{
		//! 현재 정보를 다시 보내주고 리턴.
		SERVER.SendToClient(ownPlayer, resPack);
		return;
	}

	//! 다음 번 받는 업데이트 칸 보상 개수의 배수로, 기본값 1 로 설정해야 한다.
	Int32 itemAcquireLarbaBonus = 1;
	if (0 != m_marbleInfo.GetLastLarbaCard())
	{
		auto script = SCRIPTS.GetScript<MarbleCardScript>();
		const MarbleCardElem * cardElem = script->GetCardElem(m_marbleInfo.GetLastLarbaCard());

		switch (cardElem->condition)
		{
		case MarbleCardActionType::MultifulReward:
#ifdef __Patch_LegendMarble_v4_raylee_2019_1_23
			itemAcquireLarbaBonus = cardElem->values.size() > 0 ? cardElem->values[0] : 2;
#else
			itemAcquireLarbaBonus = cardElem->values[0];
#endif
			m_marbleInfo.SetLastLarbaCard(0);
			break;
		}
	}

	UInt32 curSeq = m_marbleInfo.GetCurSequence();
	Int32 rotateCount = 0;

	MarbleAreaElem * nextArea = theMarbleSystem.Next(curSeq, m_marbleInfo.GetDiceNum(), rotateCount);
	if (nullptr == nextArea)
	{
		//! 실패 보내기.
		EResMarbleDiceResult * resInfo = NEW EResMarbleDiceResult;
		EventPtr pack(resInfo);

		resInfo->result = ErrorMarbleSystem::E_AREA_INFO;

		SERVER.SendToClient(ownPlayer, pack);

		m_marbleInfo.SetDiceNum(0);

		return;
	}

	// expMulti -> check rotate 이 순서를 지켜야 한다.
	if(nextArea->expMulti > 1)
	{
		m_marbleInfo.AddBoxExpBonus(nextArea->expMulti);

		ENtfMarbleRewardBoxExpBonus * ntfBonus = NEW ENtfMarbleRewardBoxExpBonus;
		if (ownPlayer->IsPcRoom())
			ntfBonus->rewardBoxExpBonus = m_marbleInfo.GetBoxExpBonus() * theMarbleSystem.GetPcRoomRewardExpBonus() / 100;
		else
			ntfBonus->rewardBoxExpBonus = m_marbleInfo.GetBoxExpBonus();

		SERVER.SendToClient(ownPlayer, EventPtr(ntfBonus));
	}

	if (rotateCount > 0)
	{
		MarbleRewardOneCycle();
	}
	
	m_marbleInfo.SetCurSequence(nextArea->sequence);
	m_marbleInfo.SetDiceNum(0);

	if (nextArea->isAirship)				// 비공정 칸
	{
		m_marbleInfo.SetLastLarbaCard(0);
		// 비공정 칸일 경우, 서버 알림은 따로 없고, 클라에서 처리하기로 했음.
		m_marbleInfo.SetAreaReward(1);		// 비공정은 무조건 보상이 완료.
		res->lastCardId = 0;
	}
	else
	{
		//! 보상 주기
		MarbleAreaReward(nextArea, itemAcquireLarbaBonus, res);
	}

	SERVER.SendToClient(ownPlayer, resPack);

	MarbleInfoUpdate();
}


ErrorMarbleSystem::Error ActionPlayerInventory::MarbleSelectPos(UInt32 seq)
{
	MU2_INFO_LOG(LogCategory::CONTENTS, "Marble - Select Pos seq = %u", seq);

	EntityPlayer* ownPlayer = GetOwnerPlayer();
	VERIFY_RETURN(ownPlayer && ownPlayer->IsValid(), ErrorMarbleSystem::playerNotFound);

	if (ownPlayer->GetLevel() < theMarbleSystem.GetMinLevel())
	{
		SystemMessage msg(L"sys", L"Msg_Error_Requirement_CharLevel");
		msg.SetParam(L"str", theMarbleSystem.GetMinLevel());
		SendSystemMsg(ownPlayer, msg);
		//! 실패 보내기.
		return ErrorMarbleSystem::E_CHAR_LEVEL;
	}

	MarbleAreaElem * curArea = theMarbleSystem.GetArea(m_marbleInfo.GetCurSequence());
	VERIFY_RETURN(curArea, ErrorMarbleSystem::elemNotFound);

	EResMarbleSelectPos * res = NEW EResMarbleSelectPos;
	EventPtr resPtr(res);

	if (0 == curArea->isAirship)				// 비공정 칸
	{
		res->result = ErrorMarbleSystem::E_NOT_AIRSHIP_AREA;
		SERVER.SendToClient(ownPlayer, resPtr);
		return ErrorMarbleSystem::E_NOT_AIRSHIP_AREA;
	}

	if (seq >= MARBLE_AREA_COUNT || seq == curArea->sequence)
	{
		MU2_WARN_LOG(LogCategory::CONTENTS, "Marble - Select Pos Error : seq = %u( Cur = %u )", seq, curArea->sequence);

		res->result = ErrorMarbleSystem::E_CANNOT_GO_AIRSHIP_AREA;
		SERVER.SendToClient(ownPlayer, resPtr);
		return ErrorMarbleSystem::E_CANNOT_GO_AIRSHIP_AREA;
	}

	Int32 dice = 0;
	if (seq > m_marbleInfo.GetCurSequence())
		dice = seq - m_marbleInfo.GetCurSequence();
	else
		dice = seq + MARBLE_AREA_COUNT - m_marbleInfo.GetCurSequence();

	m_marbleInfo.SetDiceNum(dice);
	m_marbleInfo.SetLastLarbaCard(0);

	MarbleInfoUpdate();

	res->lastSequence = seq;
	res->result = ErrorMarbleSystem::SUCCESS;

	SERVER.SendToClient(ownPlayer, resPtr);

	EReqLogDb2* log = NEW EReqLogDb2;
	EventPtr logEventPtr(log);
	EventSetter::FillLogForEntity(LogCode::E_MARBLE_AIRSHIP, ownPlayer, log->action);

	
	if (ownPlayer->IsPcRoom())
		log->action.var32s.push_back(0);
	else
		log->action.var32s.push_back(1);

	log->action.var32s.push_back(seq);
	
	SendDataCenter(logEventPtr);



	return ErrorMarbleSystem::SUCCESS;
}


void ActionPlayerInventory::MarbleOpenBoxReq()
{
	EntityPlayer* ownPlayer = GetOwnerPlayer();
	VERIFY_RETURN(ownPlayer && ownPlayer->IsValid(), );

	if (false == theEventSystem.IsActiveEventType(GameEventElem::MARBLE_EVENT))
	{
		//! 실패 보내기.
		EResMarbleOpenRewardBox * resInfo = NEW EResMarbleOpenRewardBox;
		EventPtr pack(resInfo);

		resInfo->result = ErrorMarbleSystem::E_NOT_EVENT_PERIOD;
		resInfo->curSequence = m_marbleInfo.GetCurSequence();
		resInfo->rewardBoxInfo = m_marbleInfo.GetRewardData();

		SERVER.SendToClient(ownPlayer, pack);

		return;
	}

	if (ownPlayer->GetLevel() < theMarbleSystem.GetMinLevel())
	{
		EResMarbleOpenRewardBox * resInfo = NEW EResMarbleOpenRewardBox;
		EventPtr pack(resInfo);

		resInfo->result = ErrorMarbleSystem::E_CHAR_LEVEL;
		resInfo->curSequence = m_marbleInfo.GetCurSequence();
		resInfo->rewardBoxInfo = m_marbleInfo.GetRewardData();

		SERVER.SendToClient(ownPlayer, pack);

		SystemMessage msg(L"sys", L"Msg_Error_Requirement_CharLevel");
		msg.SetParam(L"str", theMarbleSystem.GetMinLevel());
		SendSystemMsg(ownPlayer, msg);

		return;
	}

	if (0 != m_marbleInfo.GetRewardData().m_boxOpenCount)
	{
		//! 실패 보내기.
		EResMarbleOpenRewardBox * resInfo = NEW EResMarbleOpenRewardBox;
		EventPtr pack(resInfo);

		resInfo->result = ErrorMarbleSystem::E_ALREADY_REWARD_BOX_OPEN;
		resInfo->curSequence = m_marbleInfo.GetCurSequence();
		resInfo->rewardBoxInfo = m_marbleInfo.GetRewardData();

		SERVER.SendToClient(ownPlayer, pack);

		SendSystemMsg(ownPlayer, SystemMessage(L"sys", L"Msg_RewardBox_getErrorMsg"));

		return;
	}

	auto boxScript = SCRIPTS.GetScript<MarbleBoxScript>();
	MarbleBoxElem * boxElem = boxScript->GetBox(m_marbleInfo.GetRewardData().m_boxGrade);
	if (nullptr == boxElem)
	{
		//! 실패 보내기.
		EResMarbleOpenRewardBox * resInfo = NEW EResMarbleOpenRewardBox;
		EventPtr pack(resInfo);

		resInfo->result = ErrorMarbleSystem::E_BOX_INFO_ERROR;
		resInfo->curSequence = m_marbleInfo.GetCurSequence();
		resInfo->rewardBoxInfo = m_marbleInfo.GetRewardData();

		SERVER.SendToClient(ownPlayer, pack);

		return;
	}

	SimpleItems items;

	for (auto &itr : boxElem->rewards)
	{
		if (itr.index && itr.count)
		{
			SimpleItem item;

			item.itemIndex = itr.index;
			item.itemCount = itr.count;

			items.push_back(item);
		}
	}

	ItemPushOrMailTransaction * trans = NEW ItemPushOrMailTransaction(__FUNCTION__, __LINE__, ownPlayer, LogCode::None
		, L"Msg_Mailbox_LegendOfDice_SENDER", L"Msg_Mailbox_LegendOfDice_SUBJECT", L"Msg_Mailbox_LegendOfDice_CONTENT");

	for (auto& itr : items)
	{
		trans->Push(ItemData(itr.itemIndex, itr.itemCount));
	}

	TransactionSystem::Register(TransactionPtr(trans));

	EReqLogDb2* log = NEW EReqLogDb2;
	EventPtr logEventPtr(log);
	EventSetter::FillLogForEntity(LogCode::E_MARBLE_REWARD_BOX_OPEN, ownPlayer, log->action);

	if (ownPlayer->IsPcRoom())
		log->action.var32s.push_back(0);
	else
		log->action.var32s.push_back(1);

	log->action.var32s.push_back(m_marbleInfo.GetRewardData().m_boxGrade);

	for (auto& itr : items)
	{
		log->action.var32s.push_back(itr.itemIndex);
		log->action.var32s.push_back(itr.itemCount);
	}
	
	SendDataCenter(logEventPtr);

	MarbleRecvRewardReset();

	MarbleBoxElem * firstBox = boxScript->GetFirstBox();
	VERIFY_RETURN(firstBox, );

	m_marbleInfo.GetRewardData().m_boxIndex = firstBox->boxIndex;
	m_marbleInfo.GetRewardData().m_boxGrade = firstBox->grade;
	m_marbleInfo.GetRewardData().m_boxOpenCount = 1;
	m_marbleInfo.GetRewardData().m_boxExp = 0;
	m_marbleInfo.GetRewardData().m_nextBoxIndex = firstBox->nextBox;


	MarbleInfoUpdate();

	EResMarbleOpenRewardBox * resInfo = NEW EResMarbleOpenRewardBox;
	EventPtr pack(resInfo);

	resInfo->result = ErrorMarbleSystem::SUCCESS;
	resInfo->curSequence = m_marbleInfo.GetCurSequence();
	resInfo->rewardBoxInfo = m_marbleInfo.GetRewardData();

	SERVER.SendToClient(ownPlayer, pack);

	MarbleSendInfo(MarbleInfoResult::OpenRewardBox);
}


void ActionPlayerInventory::MarbleResetCondition(UInt32 conditionIndex)
{
	m_marbleInfo.ResetPointCondition(conditionIndex);

	MarbleInfoUpdate();
}


#ifdef __Patch_LegendMarble_v4_raylee_2019_1_23
void ActionPlayerInventory::MarbleSetCondition(UInt32 conditionIndex, Int32 value)
{
	conditionIndex;
	value;

	/*
	EntityPlayer* ownPlayer = GetOwnerPlayer();
	VERIFY_RETURN(ownPlayer && ownPlayer->IsValid(), );

	std::vector<MarblePointConditionInfo> infos;
	if (MarbleCheckPointCondition(conditionIndex, infos))
	{
	EReqDbMarbleConditionUpdate * con = NEW EReqDbMarbleConditionUpdate;
	EventPtr conPtr(con);
	con->accGuid = ownPlayer->GetAccountId();
	con->condition = info;
	SERVER.SendToDb(ownPlayer, conPtr);

	ENtfMarbleChangePoint * change = NEW ENtfMarbleChangePoint;
	change->curPlayPoint = m_marbleInfo.GetCurPlayPoint();
	change->pointConditionChanged = info;

	SERVER.SendToClient(ownPlayer, EventPtr(change));

	MarbleInfoUpdate();
	}
	*/
}

#else

void ActionPlayerInventory::MarbleSetCondition(UInt32 conditionIndex)
{
	EntityPlayer* ownPlayer = GetOwnerPlayer();
	VERIFY_RETURN(ownPlayer && ownPlayer->IsValid(), );

	MarblePointConditionInfo info;
	if (MarbleCheckPointCondition(conditionIndex, info))
	{
		EReqDbMarbleConditionUpdate * con = NEW EReqDbMarbleConditionUpdate;
		EventPtr conPtr(con);
		con->accGuid = ownPlayer->GetAccountId();
		con->condition = info;
		SERVER.SendToDb(ownPlayer, conPtr);

		ENtfMarbleChangePoint * change = NEW ENtfMarbleChangePoint;
		change->curPlayPoint = m_marbleInfo.GetCurPlayPoint();
		change->pointConditionChanged = info;

		SERVER.SendToClient(ownPlayer, EventPtr(change));

		MarbleInfoUpdate();
	}
}

#endif

void ActionPlayerInventory::MarbleAreaReward(MarbleAreaElem * area, Int32 rewardBonus, EResMarbleAreaResult * areaResult)
{
	VERIFY_RETURN(area, );

	EntityPlayer* ownPlayer = GetOwnerPlayer();
	VERIFY_RETURN(ownPlayer && ownPlayer->IsValid(), );

	m_marbleInfo.SetLastLarbaCard(0);

	if (area->rewardBag)
	{
		const MarbleRewardElem * rewardElem = SCRIPTS.GetScript<MarbleRewardScript>()->Get(area->rewardBag);
		if (rewardElem)
		{
			Int32 repeat = 0;
			while (repeat < (rewardElem->repeatCount * rewardBonus))
			{
				SimpleItem item;

				if (true == rewardElem->SelectOne(item.itemIndex, item.itemCount))
					areaResult->items.push_back(item);

				++repeat;
			}

			m_marbleInfo.SetAreaReward(1);
			areaResult->lastCardId = 0;

			ItemPushOrMailTransaction * trans = NEW ItemPushOrMailTransaction(__FUNCTION__, __LINE__, ownPlayer, LogCode::None
				, L"Msg_Mailbox_LegendOfDice_SENDER", L"Msg_Mailbox_LegendOfDice_SUBJECT", L"Msg_Mailbox_LegendOfDice_CONTENT");

			for (auto& itr : areaResult->items)
			{
				trans->Push(ItemData(itr.itemIndex, itr.itemCount));
			}

			TransactionSystem::Register(TransactionPtr(trans));


			EReqLogDb2* log = NEW EReqLogDb2;
			EventPtr logEventPtr(log);
			EventSetter::FillLogForEntity(LogCode::E_MARBLE_REWARD_ITEM, ownPlayer, log->action);

			if (ownPlayer->IsPcRoom())
				log->action.var32s.push_back(0);
			else
				log->action.var32s.push_back(1);

			log->action.var32s.push_back(m_marbleInfo.GetCurSequence());

			for (auto& itr : areaResult->items)
			{
				log->action.var32s.push_back(itr.itemIndex);
				log->action.var32s.push_back(itr.itemCount);
			}

			SendDataCenter(logEventPtr);
		}
		else
		{
			//! 없다면 보상 실패.
			MU2_WARN_LOG(LogCategory::CONTENTS, "Marble - No info rewardBag(%u)", area->rewardBag);

			areaResult->lastCardId = 0;

		}

		m_marbleInfo.SetLastLarbaCard(0);
	}
	else if(area->cardBag)
	{
		//! 라르바의 찬스 : 하나 선택해서 알려준다.
		const MarbleCardBag * cardBag = SCRIPTS.GetScript<MarbleCardScript>()->GetCardBag(area->cardBag);
		if (cardBag)
		{
			const MarbleCardElem * cardElem = cardBag->SelectOne();
			if (cardElem)
			{
				m_marbleInfo.SetLastLarbaCard(cardElem->index);

				EReqLogDb2* log = NEW EReqLogDb2;
				EventPtr logEventPtr(log);
				EventSetter::FillLogForEntity(LogCode::E_MARBLE_LARBA_CARD, ownPlayer, log->action);

				log->action.subInfo = MarbleCardActionType::GetString(cardElem->condition);
				
				if (ownPlayer->IsPcRoom())
					log->action.var32s.push_back(0);
				else
					log->action.var32s.push_back(1);

				log->action.var32s.push_back(cardElem->index);
				log->action.var32s.push_back(m_marbleInfo.GetCurPlayPoint());

				SendDataCenter(logEventPtr);

				//! 비공정으로 보낸다.
				switch (cardElem->condition)
				{
				case MarbleCardActionType::GoAirshipTravel:
				{
					MarbleAreaElem * airshipArea = theMarbleSystem.GetAirshipArea();
					VERIFY_RETURN(airshipArea, );

					m_marbleInfo.SetLastLarbaCard(0);

					if (airshipArea->sequence < area->sequence)
					{
						//! 한바퀴 돈것이다. 
						MarbleRewardOneCycle();
					}

					m_marbleInfo.SetCurSequence(airshipArea->sequence);

					EResMarbleSelectPos * spos = NEW EResMarbleSelectPos;
					spos->result = ErrorMarbleSystem::SUCCESS;
					spos->lastSequence = m_marbleInfo.GetCurSequence();

					SERVER.SendToClient(ownPlayer, EventPtr(spos));
				}
				break;
				case MarbleCardActionType::RewardBoxExpUp:	// 보상 상자의 경험치 2배
					m_marbleInfo.AddBoxExpBonus(cardElem->values[0]);

					{
						ENtfMarbleRewardBoxExpBonus * ntfBonus = NEW ENtfMarbleRewardBoxExpBonus;
						if (ownPlayer->IsPcRoom())
							ntfBonus->rewardBoxExpBonus = m_marbleInfo.GetBoxExpBonus() * theMarbleSystem.GetPcRoomRewardExpBonus() / 100;
						else
							ntfBonus->rewardBoxExpBonus = m_marbleInfo.GetBoxExpBonus();

						SERVER.SendToClient(ownPlayer, EventPtr(ntfBonus));
					}

					m_marbleInfo.SetLastLarbaCard(0);

					break;

#ifdef __Patch_LegendMarble_v4_raylee_2019_1_23
				case MarbleCardActionType::GoStartPosition:
				{
					m_marbleInfo.SetLastLarbaCard(0);

					//! 한바퀴 돈것이다. 
					MarbleRewardOneCycle();

					m_marbleInfo.SetCurSequence(0);

					EResMarbleSelectPos * spos = NEW EResMarbleSelectPos;
					spos->result = ErrorMarbleSystem::SUCCESS;
					spos->lastSequence = m_marbleInfo.GetCurSequence();

					SERVER.SendToClient(ownPlayer, EventPtr(spos));
				}

				break;

				case MarbleCardActionType::AddDicePlayChance:
				{
					m_marbleInfo.SetLastLarbaCard(0);

					MarblePlayPoint addPoint = static_cast<MarblePlayPoint>(cardElem->values.size() > 0 ? cardElem->values.at(0) : 1);

					m_marbleInfo.AddPlayPoint(addPoint);

					MarbleInfoUpdate();
				}

				break;
#endif
				}

				areaResult->lastCardId = cardElem->index;
				areaResult->larbaActionType = cardElem->condition;

				//! 라르바 히스토리에 추가한다. ( m_marbleInfo.GetLastLarbaCard() 로 하지 않는다. 비공정일때 초기화됨 )
				m_marbleInfo.AddLarbaHistory(m_marbleInfo.GetCurSequence(), cardElem->index);

				EReqDbMarbleLarbaHistoryUpdate * larbaUpdate = NEW EReqDbMarbleLarbaHistoryUpdate;
				larbaUpdate->accGuid = ownPlayer->GetAccountId();
				larbaUpdate->history.sequence = m_marbleInfo.GetCurSequence();
				larbaUpdate->history.larbaCardIndex = cardElem->index;

				SERVER.SendToDb(ownPlayer, EventPtr(larbaUpdate));
			}
			else
			{
				MU2_WARN_LOG(LogCategory::CONTENTS, "Marble - No info cardElem(%u)", area->cardBag);
			}
		}
		else
		{
			//! 없다면 보상 실패.
			MU2_WARN_LOG(LogCategory::CONTENTS, "Marble - No info cardBag(%u)", area->cardBag);
		}
	}
}


#ifdef __Patch_LegendMarble_v4_raylee_2019_1_23

// 조건이 더 확장된다면, condition type 에 따른 event 클래스를 따로 만들어야 할 듯. 업적처럼. 
void ActionPlayerInventory::MarbleNoticeEvent(MarbleConditionArg& arg)
{
	if (false == theEventSystem.IsActiveEventType(GameEventElem::MARBLE_EVENT))
	{
		// 이벤트 중이 아니라면, 셋팅할 필요도 없다.
		return;
	}

	EntityPlayer* ownPlayer = GetOwnerPlayer();
	VERIFY_RETURN(ownPlayer && ownPlayer->IsValid(), );

	{
		std::vector<MarblePointConditionInfo> infos;
		if (MarbleCheckCondition(arg, infos))
		{
			for (auto &info : infos)
			{
				EReqDbMarbleConditionUpdate * con = NEW EReqDbMarbleConditionUpdate;
				EventPtr conPtr(con);
				con->accGuid = ownPlayer->GetAccountId();
				con->condition = info;
				SERVER.SendToDb(ownPlayer, conPtr);

				ENtfMarbleChangePoint * change = NEW ENtfMarbleChangePoint;
				change->curPlayPoint = m_marbleInfo.GetCurPlayPoint();
				change->pointConditionChanged = info;

				SERVER.SendToClient(ownPlayer, EventPtr(change));
			}

			MarbleInfoUpdate();
		}
	}
}

#else

void ActionPlayerInventory::MarbleNoticeEvent(PlayPointConditionType::Enum eventType)
{
	if (false == theEventSystem.IsActiveEventType(GameEventElem::MARBLE_EVENT))
	{
		// 이벤트 중이 아니라면, 셋팅할 필요도 없다.
		return;
	}

	EntityPlayer* ownPlayer = GetOwnerPlayer();
	VERIFY_RETURN(ownPlayer && ownPlayer->IsValid(), );


	// 피씨방 여부가 추가되어야 하는데, 시간이 없어서, 반복함.
	{
		MarblePointConditionInfo info;
		if (MarbleCheckPointCondition(eventType, info))
		{
			EReqDbMarbleConditionUpdate * con = NEW EReqDbMarbleConditionUpdate;
			EventPtr conPtr(con);
			con->accGuid = ownPlayer->GetAccountId();
			con->condition = info;
			SERVER.SendToDb(ownPlayer, conPtr);

			MarbleInfoUpdate();

			ENtfMarbleChangePoint * change = NEW ENtfMarbleChangePoint;
			change->curPlayPoint = m_marbleInfo.GetCurPlayPoint();
			change->pointConditionChanged = info;

			SERVER.SendToClient(ownPlayer, EventPtr(change));
		}
	}

	if (ownPlayer->IsPcRoom())
	{
		MarblePointConditionInfo info;
		if (MarbleCheckPointCondition(eventType, info, true))
		{
			EReqDbMarbleConditionUpdate * con = NEW EReqDbMarbleConditionUpdate;
			EventPtr conPtr(con);
			con->accGuid = ownPlayer->GetAccountId();
			con->condition = info;
			SERVER.SendToDb(ownPlayer, conPtr);

			MarbleInfoUpdate();

			ENtfMarbleChangePoint * change = NEW ENtfMarbleChangePoint;
			change->curPlayPoint = m_marbleInfo.GetCurPlayPoint();
			change->pointConditionChanged = info;

			SERVER.SendToClient(ownPlayer, EventPtr(change));
		}
	}
}

#endif


void ActionPlayerInventory::MarbleGMSetPoint(MarblePlayPoint point)
{
	m_marbleInfo.SetCurPlayPoint(point);

	MarbleInfoUpdate();
}


void ActionPlayerInventory::MarbleGMSetBox(UInt8 grade, MarbleBoxExp exp)
{
	auto script = SCRIPTS.GetScript<MarbleBoxScript>();

	MarbleRewardBoxInfo& info = m_marbleInfo.GetRewardData();

	if (grade < script->GetMinGrade())
		grade = script->GetMinGrade();
	else if (grade > script->GetMaxGrade())
		grade = script->GetMaxGrade();
	
	MarbleBoxElem * boxElem = script->GetBox(info.m_boxGrade);
	if (boxElem)
	{
		if (exp > boxElem->exp)
			exp = boxElem->exp;

		info.m_boxIndex = boxElem->boxIndex;
		info.m_boxGrade = grade;
		info.m_boxExp = exp;
	}

	info.m_nextBoxIndex = 0;
	if (info.m_boxGrade < script->GetMaxGrade())
	{
		MarbleBoxElem * nextElem = script->GetBox(info.m_boxGrade + 1);
		if (nextElem)
			info.m_nextBoxIndex = nextElem->boxIndex;
	}

	ENtfMarbleRewardBoxExp * boxExp = NEW ENtfMarbleRewardBoxExp;
	boxExp->rewardBoxInfo = info;

	EntityPlayer* ownPlayer = GetOwnerPlayer();
	VERIFY_RETURN(ownPlayer && ownPlayer->IsValid(), );

	SERVER.SendToClient(ownPlayer, EventPtr(boxExp));
}

void ActionPlayerInventory::MarbleGMSetRedzenCount(MarblePlayPoint count)
{
	EntityPlayer* ownPlayer = GetOwnerPlayer();
	VERIFY_RETURN(ownPlayer && ownPlayer->IsValid(), );

	m_marbleInfo.SetCurRedzenPlayCount(count);

	ENtfMarbleChangeRedzenPlayCount * pkCount = NEW ENtfMarbleChangeRedzenPlayCount;
	pkCount->curRedzenPlayCount = m_marbleInfo.GetCurRedzenPlayCount();
	EventPtr packCount(pkCount);
	SERVER.SendToClient(ownPlayer, packCount);

	MarbleInfoUpdate();
}


void ActionPlayerInventory::MarbleDailyReset(Bool bSendClient)
{
	//! 포인트만 초기화하고 나머지는 그대로 둔다.
	EntityPlayer* ownPlayer = GetOwnerPlayer();
	VERIFY_RETURN(ownPlayer && ownPlayer->IsValid(), );

	//! 리셋 시간을 업데이트한다.ㅇ
	DateTime date = DateTime::GetPresentTime();
	date.GetAsSystemTime(m_marbleInfo.GetResetDate());

	m_marbleLoginTime = date;
	m_marbleInfo.GetRewardData().m_boxOpenCount = 0;		// 보상을 받을 수 있도록 수정한다.

	m_marbleInfo.SetCurPlayPoint(0);
	m_marbleInfo.ResetPointConditions();
	m_marbleInfo.SetCurRedzenPlayCount(0);
	m_marbleInfo.SetDayLastConMinutes(0);		// 

	EReqDbMarbleConMinuteUpdate * keep = NEW EReqDbMarbleConMinuteUpdate;
	EventPtr keepPtr(keep);
	keep->accGuid = ownPlayer->GetAccountId();
	keep->conMinutes = 0;
	SERVER.SendToDb(ownPlayer, keepPtr);

	EReqDbMarbleConditionUpdate * conUpdate = NEW EReqDbMarbleConditionUpdate;
	EventPtr conUpdatePtr(conUpdate);
	conUpdate->accGuid = ownPlayer->GetAccountId();
	conUpdate->condition.conditionIndex = 0;
	SERVER.SendToDb(ownPlayer, conUpdatePtr);

#ifdef __Patch_LegendMarble_v4_raylee_2019_1_23

	{
		MarbleConditionArg arg(PlayPointConditionType::FirstConnect, ownPlayer->IsPcRoom());

		std::vector<MarblePointConditionInfo> conInfos;
		if (MarbleCheckCondition(arg, conInfos))
		{
			for (auto &conInfo : conInfos)
			{
				if (bSendClient)
				{
					ENtfMarbleChangePoint * change = NEW ENtfMarbleChangePoint;
					change->curPlayPoint = m_marbleInfo.GetCurPlayPoint();
					change->pointConditionChanged = conInfo;

					SERVER.SendToClient(ownPlayer, EventPtr(change));
				}

				EReqDbMarbleConditionUpdate * con = NEW EReqDbMarbleConditionUpdate;
				EventPtr conPtr(con);
				con->accGuid = ownPlayer->GetAccountId();
				con->condition = conInfo;
				SERVER.SendToDb(ownPlayer, conPtr);
			}
		}
	}

	MarbleInfoUpdate();

#else 
	std::vector<MarblePlayPointConditionElem *> outPointConditions;
	theMarbleSystem.GetFirstConPointCondition(outPointConditions, ownPlayer->IsPcRoom());

	// 최초(하루) 접속 보상 체크
	for (MarblePlayPointConditionElem * conElem : outPointConditions)
	{
		MarblePointConditionInfo conInfo;
		if (MarbleCheckPointCondition(conElem->index, conInfo))
		{
			if (bSendClient)
			{
				ENtfMarbleChangePoint * change = NEW ENtfMarbleChangePoint;
				change->curPlayPoint = m_marbleInfo.GetCurPlayPoint();
				change->pointConditionChanged = conInfo;

				SERVER.SendToClient(ownPlayer, EventPtr(change));
			}

			EReqDbMarbleConditionUpdate * con = NEW EReqDbMarbleConditionUpdate;
			EventPtr conPtr(con);
			con->accGuid = ownPlayer->GetAccountId();
			con->condition = conInfo;
			SERVER.SendToDb(ownPlayer, conPtr);
		}
	}

	MarbleInfoUpdate();

#endif
}


void ActionPlayerInventory::MarbleRecvRewardReset()
{
	EntityPlayer* ownPlayer = GetOwnerPlayer();
	VERIFY_RETURN(ownPlayer && ownPlayer->IsValid(), );

	//! 리셋 시간을 업데이트한다.ㅇ
	DateTime date = DateTime::GetPresentTime();
	date.GetAsSystemTime(m_marbleInfo.GetResetDate());

	m_marbleLoginTime = date;

	m_marbleInfo.SetCurSequence(MARBLE_START_SEQ);
	m_marbleInfo.SetLastLarbaCard(0);

	m_marbleInfo.ResetLarbeHistory();

	m_marbleInfo.SetDiceNum(0);

	m_marbleInfo.ResetRewardData();
}

void ActionPlayerInventory::MarbleInfoUpdate()
{
	EntityPlayer* ownPlayer = GetOwnerPlayer();
	VERIFY_DO(ownPlayer && ownPlayer->IsValid(), return);

	EReqDbMarbleInfoUpdate * dbReq = NEW EReqDbMarbleInfoUpdate;
	dbReq->accGuid = ownPlayer->GetAccountId();
	dbReq->marbleInfo = m_marbleInfo;

	SERVER.SendToDb(ownPlayer, EventPtr(dbReq));
}


void ActionPlayerInventory::MarbleRewardOneCycle()
{
	EntityPlayer* ownPlayer = GetOwnerPlayer();
	VERIFY_RETURN(ownPlayer && ownPlayer->IsValid(), );

	MarbleBoxExp baseExp = theMarbleSystem.GetBasicRewardExp();

	if (ownPlayer->IsPcRoom())
		baseExp = baseExp * theMarbleSystem.GetPcRoomRewardExpBonus() / 100;

	MarbleBoxExp expBonus = m_marbleInfo.GetBoxExpBonus();
	if (0 == expBonus)
		expBonus = 1;

	baseExp *= expBonus;
	
	auto script = SCRIPTS.GetScript<MarbleBoxScript>();

	MarbleRewardBoxInfo& info = m_marbleInfo.GetRewardData();

	UInt8 preGrade = info.m_boxGrade;
	info.m_boxExp += baseExp;

	MU2_INFO_LOG(LogCategory::CONTENTS, "[Legend Marble] One Cycle bonus : Add Exp %u / Bonus %u", baseExp, expBonus);

	while (info.m_boxGrade < script->GetMaxGrade())
	{
		MarbleBoxElem * boxElem = script->GetBox(info.m_boxGrade);
		if (boxElem)
		{
			if (info.m_boxExp >= boxElem->exp)
			{
				info.m_boxExp -= boxElem->exp;

				++info.m_boxGrade;
			}
			else
				break;
		}
		else
			break;
	}

	MarbleBoxElem * boxElem = script->GetBox(info.m_boxGrade);
	VERIFY_RETURN(boxElem, );

	info.m_boxIndex = boxElem->boxIndex;
	MarbleBoxElem * nextElem = script->GetBox(info.m_boxGrade + 1);
	if (nextElem)
		info.m_nextBoxIndex = nextElem->boxIndex;
	else
		info.m_nextBoxIndex = 0;
	
	EReqLogDb2* log = NEW EReqLogDb2;
	EventPtr logEventPtr(log);
	EventSetter::FillLogForEntity(LogCode::E_MARBLE_REWARD_BOX_EXP, ownPlayer, log->action);

 	if (ownPlayer->IsPcRoom())
		log->action.var32s.push_back(0);
	else
		log->action.var32s.push_back(1);

	log->action.var32s.push_back(preGrade);
	log->action.var32s.push_back(info.m_boxGrade);
	log->action.var32s.push_back(baseExp);
	log->action.var32s.push_back(info.m_boxExp);

	SendDataCenter(logEventPtr);

	MU2_INFO_LOG(LogCategory::CONTENTS, " Reward Box ( %u ) : L - %u, E - %u (OC %u, Next %u) \r\n"
		, m_marbleInfo.GetRewardData().m_boxIndex, m_marbleInfo.GetRewardData().m_boxGrade
		, m_marbleInfo.GetRewardData().m_boxExp, m_marbleInfo.GetRewardData().m_boxOpenCount, m_marbleInfo.GetRewardData().m_nextBoxIndex);

	ENtfMarbleRewardBoxExp * boxExp = NEW ENtfMarbleRewardBoxExp;
	boxExp->rewardBoxInfo = info;

	SERVER.SendToClient(ownPlayer, EventPtr(boxExp));

	m_marbleInfo.ResetBoxExpBonus();
	MarbleResetHistory();
}


void ActionPlayerInventory::MarbleResetHistory()
{
	EntityPlayer * player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	m_marbleInfo.ResetLarbeHistory();

	EReqDbMarbleLarbaHistoryUpdate * history = NEW EReqDbMarbleLarbaHistoryUpdate;
	history->accGuid = player->GetAccountId();
	history->history.sequence = 0;
	history->history.larbaCardIndex = 0;

	SERVER.SendToDb(player, EventPtr(history));

	ENtfMarbleResetLarbaHistory * reset = NEW ENtfMarbleResetLarbaHistory;
	SERVER.SendToClient(player, EventPtr(reset));
}


void ActionPlayerInventory::MarbleSetLoginTime(const DateTime& time)
{
	m_marbleLoginTime = time;
}


#ifdef __Patch_LegendMarble_v4_raylee_2019_1_23

Bool ActionPlayerInventory::MarbleCheckCondition(MarbleConditionArg& arg, std::vector<MarblePointConditionInfo>& outPointInfos)
{
	EntityPlayer * player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt8 addPoint = m_marbleInfo.CheckCondition(arg, outPointInfos);
	if (addPoint > 0)
	{
		for (auto &out : outPointInfos)
		{
			out;
			EReqLogDb2* log = NEW EReqLogDb2;
			EventPtr logEventPtr(log);
			EventSetter::FillLogForEntity(LogCode::E_MARBLE_PLAY_COUNT, GetOwnerPlayer(), log->action);

			log->action.subInfo = PlayPointConditionType::GetString(arg.condition);
			if (player->IsPcRoom())
				log->action.var32s.push_back(0);
			else
				log->action.var32s.push_back(1);
			log->action.var32s.push_back(addPoint);
			log->action.var32s.push_back(m_marbleInfo.GetCurPlayPoint());

			SendDataCenter(logEventPtr);
		}
	}

	return (addPoint > 0);
}

#else

Bool ActionPlayerInventory::MarbleCheckPointCondition(PlayPointConditionType::Enum ctype, MarblePointConditionInfo& outPointInfo, Bool isPcRoom /* = false */)
{
	EntityPlayer * player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt8 addPoint = m_marbleInfo.CheckPointCondition(ctype, outPointInfo, isPcRoom);
	if (addPoint > 0)
	{
		EReqLogDb2* log = NEW EReqLogDb2;
		EventPtr logEventPtr(log);
		EventSetter::FillLogForEntity(LogCode::E_MARBLE_PLAY_COUNT, GetOwnerPlayer(), log->action);

		log->action.subInfo = PlayPointConditionType::GetString(ctype);
		if (player->IsPcRoom())
			log->action.var32s.push_back(0);
		else
			log->action.var32s.push_back(1);
		log->action.var32s.push_back(addPoint);
		log->action.var32s.push_back(m_marbleInfo.GetCurPlayPoint());

		SendDataCenter(logEventPtr);
	}

	return (addPoint > 0);
}

Bool ActionPlayerInventory::MarbleCheckPointCondition(UInt32 cidx, MarblePointConditionInfo& outPointInfo)
{
	EntityPlayer * player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), false);

	UInt8 addPoint = m_marbleInfo.CheckPointCondition(cidx, outPointInfo);

	if (addPoint > 0)
	{
		EReqLogDb2* log = NEW EReqLogDb2;
		EventPtr logEventPtr(log);
		EventSetter::FillLogForEntity(LogCode::E_MARBLE_PLAY_COUNT, GetOwnerPlayer(), log->action);

		if (outPointInfo.conditionElem)
			log->action.subInfo = PlayPointConditionType::GetString(outPointInfo.conditionElem->condition);
		if (player->IsPcRoom())
			log->action.var32s.push_back(0);
		else
			log->action.var32s.push_back(1);
		log->action.var32s.push_back(addPoint);
		log->action.var32s.push_back(m_marbleInfo.GetCurPlayPoint());

		SendDataCenter(logEventPtr);
	}

	return (addPoint > 0);
}

#endif


void ActionPlayerInventory::Update()
{
	if (0 == m_marbleInfo.GetVersion())
		return;

	if (false == theEventSystem.IsActiveEventType(GameEventElem::MARBLE_EVENT))
		return;

	//! 10초에 한번씩 채크한다. 정보가 셋팅된 후부터.
	if (m_marbleTimeChecker.IsOn())
	{
		//! 
		EntityPlayer * player = GetOwnerPlayer();
		VERIFY_RETURN(player && player->IsValid(), );

		// 새벽 5시 리셋이 안되던 것 수정 : MUTWO-3531		
		Bool needRefresh = !UseItemLimitInfo::IsDailyReset(m_marbleInfo.GetResetDate(), theMarbleSystem.GetResetHour());
		if (needRefresh)
		{
			MarbleDailyReset(true);
		}

		DateTime curTime;

		if (curTime > m_marbleLoginTime)
		{

			DateTimeSpan span = curTime - m_marbleLoginTime;
			UInt32 elapseMin = static_cast<UInt32>(span.GetTotalMinutes() + m_marbleInfo.GetDayLastConMInutes());

			if (elapseMin)
			{
				// MU2_INFO_LOG(LogCategory::CONTENTS, "[Marble] A<%d> Connect Time = %d mins", player->GetAccountId(), elapseMin);
#ifdef __Patch_LegendMarble_v4_raylee_2019_1_23

				MarbleConditionArg arg(PlayPointConditionType::KeepConnect, player->IsPcRoom(), elapseMin);

				std::vector<MarblePointConditionInfo> conditionInfos;
				if (MarbleCheckCondition(arg, conditionInfos))
				{
					for (auto& conditionInfo : conditionInfos)
					{
						EReqDbMarbleConditionUpdate * con = NEW EReqDbMarbleConditionUpdate;
						EventPtr conPtr(con);
						con->accGuid = player->GetAccountId();
						con->condition = conditionInfo;
						SERVER.SendToDb(player, conPtr);


						ENtfMarbleChangePoint * change = NEW ENtfMarbleChangePoint;
						change->curPlayPoint = m_marbleInfo.GetCurPlayPoint();
						change->pointConditionChanged = conditionInfo;

						SERVER.SendToClient(player, EventPtr(change));
					}

					MarbleInfoUpdate();
				}


#else
				std::vector<MarblePlayPointConditionElem *> outPointConditions;
				theMarbleSystem.GetTimePointCondition(elapseMin, outPointConditions, player->IsPcRoom());

				Int32 applyCount = 0;

				for (MarblePlayPointConditionElem * pc : outPointConditions)
				{
					MarblePointConditionInfo conditionInfo;
					if (MarbleCheckPointCondition(pc->index, conditionInfo))
					{
						++applyCount;

						EReqDbMarbleConditionUpdate * con = NEW EReqDbMarbleConditionUpdate;
						EventPtr conPtr(con);
						con->accGuid = player->GetAccountId();
						con->condition = conditionInfo;
						SERVER.SendToDb(player, conPtr);


						ENtfMarbleChangePoint * change = NEW ENtfMarbleChangePoint;
						change->curPlayPoint = m_marbleInfo.GetCurPlayPoint();
						change->pointConditionChanged = conditionInfo;

						SERVER.SendToClient(player, EventPtr(change));
					}
				}

				if (applyCount > 0)
				{
					MarbleInfoUpdate();
				}
#endif
			}
		}
	}
}


void ActionPlayerInventory::MarbleWriteInfo()
{
	wstring msg = L"[[  Marble Information  ]]";
	wchar_t buf[1000];

	wsprintf(buf, L" Version : %d\r\n", m_marbleInfo.GetVersion());
	msg += buf;

	wsprintf(buf, L" Cur Point(Max) : %u(%u)\r\n", m_marbleInfo.GetCurPlayPoint(), m_marbleInfo.GetMaxPlayPoint());
	msg += buf;

	wsprintf(buf, L" Redzen Point(Max) : %u(%u)\r\n", m_marbleInfo.GetCurRedzenPlayCount(), theMarbleSystem.GetRedzenMaxPlayCount());
	msg += buf;

	wsprintf(buf, L" Cur Seq : %u( last dice %u )\r\n", m_marbleInfo.GetCurSequence(), m_marbleInfo.GetDiceNum());
	msg += buf;

	wsprintf(buf, L" Last Larba : %u\r\n", m_marbleInfo.GetLastLarbaCard());
	msg += buf;

	wsprintf(buf, L" Reward Box Exp : %u ( Pcroom : %d, BonusMulti : %d )\r\n", m_marbleInfo.GetBoxExpBonus(), theMarbleSystem.GetPcRoomRewardExpBonus(), m_marbleInfo.GetBoxExpBonus());
	msg += buf;

	wsprintf(buf, L" Reward Box ( %u ) : L - %u, E - %u (OC %u, Next %u) \r\n"
		, m_marbleInfo.GetRewardData().m_boxIndex, m_marbleInfo.GetRewardData().m_boxGrade
		, m_marbleInfo.GetRewardData().m_boxExp, m_marbleInfo.GetRewardData().m_boxOpenCount, m_marbleInfo.GetRewardData().m_nextBoxIndex);
	msg += buf;

	MU2_INFO_LOG(LogCategory::CONTENTS, L"\r\n%s\r\n", msg.c_str());
}


void ActionPlayerInventory::MarbleWriteCondition()
{
	const std::vector<MarblePointConditionInfo>& conditions = m_marbleInfo.GetConditions();

	wstring msg = L"[[  Marble Conditions  ]]\r\n";
	wchar_t buf[1000];


	Int32 i = 0;
	for (const auto &itr : conditions)
	{
		wsprintf(buf, L" [%d] - ID[%d] = %uP, Count %u\r\n", ++i, itr.conditionIndex, itr.point, itr.completeCount);
		msg += buf;
	}

	MU2_INFO_LOG(LogCategory::CONTENTS, L"\r\n%s\r\n", msg.c_str());
}

void ActionPlayerInventory::MarbleWriteHistory()
{
	const std::vector<MarbleLarbeHistory>& history = m_marbleInfo.GetHistory();

	wstring msg = L"[[  Marble History  ]]\r\n";
	wchar_t buf[1000];


	for (const auto &itr : history)
	{
		wsprintf(buf, L"Seq %u - Larbe %u\r\n", itr.sequence, itr.larbaCardIndex);
		msg += buf;
	}

	MU2_INFO_LOG(LogCategory::CONTENTS, L"\r\n%s\r\n", msg.c_str());
}


void ActionPlayerInventory::MarbleUpdatePlaytime()
{

	if (0 == m_marbleInfo.GetVersion())
		return;

	if (false == theEventSystem.IsActiveEventType(GameEventElem::MARBLE_EVENT))
		return;

	DateTime curTime;

	if (curTime > m_marbleLoginTime)
	{
		EntityPlayer * player = GetOwnerPlayer();
		VERIFY_RETURN(player && player->IsValid(), );

		DateTimeSpan span = curTime - m_marbleLoginTime;
		UInt32 elapseMin = static_cast<UInt32>(span.GetTotalMinutes() + m_marbleInfo.GetDayLastConMInutes());

		EReqDbMarbleConMinuteUpdate * keep = NEW EReqDbMarbleConMinuteUpdate;
		EventPtr keepPtr(keep);
		keep->accGuid = player->GetAccountId();
		keep->conMinutes = elapseMin;
		SERVER.SendToDb(player, keepPtr);

		MU2_INFO_LOG(LogCategory::CONTENTS, "[Marble] A<%d> Update Keep Connect Time = %d mins", player->GetAccountId(), elapseMin);
	}
}


#ifdef __Patch_LegendMarble_v4_raylee_2019_1_23

ErrorMarbleSystem::Error ActionPlayerInventory::MarbleUsePlayerCountItem(EventPtr& e)
{
	if (false == theEventSystem.IsActiveEventType(GameEventElem::MARBLE_EVENT))
	{
		// 이벤트 중이 아니라면, 셋팅할 필요도 없다.
		return ErrorMarbleSystem::E_NOT_EVENT_PERIOD;
	}

	EReqLegenddiceUsePlaycountItem * req = static_cast<EReqLegenddiceUsePlaycountItem *>(e.RawPtr());

	EntityPlayer* ownerPlayer = GetOwnerPlayer();
	VERIFY_RETURN(ownerPlayer && ownerPlayer->IsValid(), ErrorMarbleSystem::playerNotFound);

	const ItemConstPtr item = GetItem(req->itemPos);
	VERIFY_RETURN(item, ErrorMarbleSystem::E_NOT_LEGENDDICE_ITEM);

	// 레전드 오브 다이스 플레이 카운트 아이템만 가능
	VERIFY_RETURN(ItemDivisionType::IsLegendDiceAddPlaycoutItem(item->GetDivision()), ErrorMarbleSystem::E_NOT_LEGENDDICE_ITEM);

	ErrorItem::Error eError = ownerPlayer->GetUseItemAction().checkAndSet(item);
	VERIFY_RETURN(ErrorItem::SUCCESS == eError, ErrorMarbleSystem::E_NOT_LEGENDDICE_ITEM);

	ItemLegendPlaycountTransaction * itemTransaction = NEW ItemLegendPlaycountTransaction(__FUNCTION__, __LINE__, ownerPlayer, LogCode::E_MARBLE_USE_PLAYCOUNT_ITEM, *item, m_marbleInfo.GetCurPlayPoint());
	TransactionPtr transPtr(itemTransaction);
	{
		ItemBag bag(*ownerPlayer);

		eError = RemoveByPlace(item->GetPlace(), bag, 1);
		VERIFY_RETURN(eError == ErrorItem::SUCCESS, ErrorMarbleSystem::E_NOT_LEGENDDICE_ITEM);

		VERIFY_RETURN(bag.Write(transPtr), ErrorMarbleSystem::BagWriteFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorMarbleSystem::TranRegisterFailed);
	}

	return ErrorMarbleSystem::SUCCESS;
}


MarblePlayPoint ActionPlayerInventory::MarbleAddPoint(MarblePlayPoint point)
{
	return m_marbleInfo.AddPlayPoint(point);
}


#endif


ErrorItem::Error ActionPlayerInventory::Seal(const ItemPlace& toBeSealedItem, const ItemPlace& sealedOrderSheet, std::wstring& systemMessage)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), ErrorItem::playerNotFound);
	
	//재봉인주문서 위치 체크
	VERIFY_DO(	sealedOrderSheet.IsConsumeBags(), 	
				systemMessage.assign(L"Msg_Item_Err_Owndedseal_inventory");
				return ErrorItem::E_REFER_SYSTEM_MSG;);

	//재봉인주문서 : 정보 Load
	const ItemConstPtr keyItem = GetItem(sealedOrderSheet);
	VERIFY_DO(keyItem, systemMessage.assign(L"Msg_Item_Err_Null_Slot"); return ErrorItem::E_REFER_SYSTEM_MSG;);

#ifdef __lockable_item_181001
	// 아이템 잠금 체크
	VALID_DO( keyItem->IsUnLocakble(), SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Item_locked_not_usable" ) ); return ErrorItem::E_ITEM_LOCKED );
#endif
	//재봉인주문서 : 진짜 재봉인 주문서가 맞는가?
	VERIFY_DO(	keyItem->GetDivision() == ItemDivisionType::REALED_ORDER_SHEET, 
				systemMessage.assign(L"Msg_Item_Err_Null_Slot"); 
				return ErrorItem::E_REFER_SYSTEM_MSG;);

	//귀속 아이템 위치 체크
	VERIFY_DO(	toBeSealedItem.IsAcquireBags(), 
				systemMessage.assign(L"Msg_Item_Err_Owndedseal_inventory");
				return ErrorItem::E_REFER_SYSTEM_MSG;);

	//귀속 아이템 : 정보 LOAD
	const ItemConstPtr targetItem = GetItem(toBeSealedItem);
	VERIFY_DO(	targetItem,
				systemMessage.assign(L"Msg_Item_Err_Null_Slot");
				return ErrorItem::E_REFER_SYSTEM_MSG;);
	
#ifdef __lockable_item_181001
	// 아이템 잠금 체크
	VALID_DO( targetItem->IsUnLocakble(), SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Item_locked_not_usable" ) ); return ErrorItem::E_ITEM_LOCKED );
#endif

	//귀속 아이템 정보 : 요청아이템 ID와 실제 인벤에 있는 아이템 ID가 동일한지 체크
	VERIFY_DO(	targetItem->GetItemId() == toBeSealedItem.itemId,
				systemMessage.assign(L"Msg_Item_Err_Null_Slot");
				return ErrorItem::E_REFER_SYSTEM_MSG;);

	VERIFY_DO(	false == targetItem->GetBindTo().IsOnAcquire(),
				systemMessage.assign(L"Msg_Item_Err_Ownededseal");
				return ErrorItem::E_REFER_SYSTEM_MSG;);

	//기간만료 아이템은 재봉인 불가
	if (targetItem->IsPeriodable())
	{
		if (targetItem->period.IsExpiredItem())
		{
			systemMessage.assign(L"Msg_Item_Err_Owndedseal");
			return ErrorItem::E_REFER_SYSTEM_MSG;
		}
	}

	//재봉인한다.

	VERIFY_RETURN(targetItem->GetCurSealedCount() < targetItem->GetMaxSealableCount(), ErrorItem::sealableCountNotEnough);

	TransactionPtr transPtr(NEW ItemSealTransaction(__FUNCTION__, __LINE__, owner, LogCode::E_ITEM_SEAL));
	{
		ItemBag bag(*owner);

		ItemData sealItemCloned = targetItem->Clone();

		ErrorItem::Error eError = ItemFactory::SetBind(SlotChangeType::SEAL, targetItem->GetBindTo(), owner->GetCharId(), sealItemCloned);
		VERIFY_RETURN(eError == ErrorItem::SUCCESS, eError);

		++sealItemCloned.bindProperty.sealCount;

		VERIFY_RETURN(bag.Update(targetItem, sealItemCloned), ErrorItem::BagUpdateFailed);

		//재봉인 주문서 수량 감소	
		eError = RemoveByPlace(sealedOrderSheet, bag, 1);
		VERIFY_RETURN(eError == ErrorItem::SUCCESS, eError);

		VERIFY_RETURN(bag.Write(transPtr), ErrorItem::BagWriteFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::E_TRANSACION_FAILED);
	}
	

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerInventory::CanPickupDroppedEntity(EntityDropped* droppedEntity, const float manualRange /*= 0 */)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	VERIFY_RETURN(droppedEntity, ErrorItem::droppedNotFound);
	VERIFY_RETURN(droppedEntity->GetItemAction().IsValidOwnership(player->GetId()), ErrorItem::E_NOT_OWNERSHIP);

	if (droppedEntity->isRemove())
	{
		droppedEntity->GetItemAction().RemoveGrid();
		return ErrorItem::E_EXIST_ITEM;
	}
		
	const float diff = Distance(droppedEntity->GetCurrPos(), player->GetCurrPos());

	if (manualRange > 0)
	{
		if (diff > manualRange)
		{
			return ErrorItem::E_OUT_OF_RANGE;
		}
	}
	else
	{	
		const AbilityValue autoRange = player->GetAbilityAction().GetAbilityValue(EAT::ITEM_AUTO_ROOTING_RANGE);
		if (diff > (autoRange + 100))
		{
			return ErrorItem::E_OUT_OF_RANGE;
		}
	}

	const ItemConstPtr droppedItem = droppedEntity->GetItemAction().GetItem();
	VERIFY_RETURN(droppedItem, ErrorItem::itemNotFound);
	
	if (droppedItem->GetInfoElem().IsQuestable())
	{
		if (false == player->GetQuestAction().CanPlayerDoGetItemQuest(droppedItem->GetItemIndex()))
		{
			SendSystemMsg(player, SystemMessage(L"sys", L"Msg_Item_err_getQuestItem"));
			return ErrorItem::E_FAILED;
		}
	}

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerInventory::ReqPickupItem(const VectorEntityId& vecDroppedId, const float manualRange /*= 0 */)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), ErrorItem::playerNotFound);
	VALID_RETURN(vecDroppedId.size(), ErrorItem::SUCCESS);

	std::vector< EntityDropped* > vecDropped;
	ItemBag bag(*owner);

	ActionPlayerEvent& actEvent = owner->GetEventAction();

	ErrorItem::Error result = ErrorItem::SUCCESS;
	Bool sendLimitZen = FALSE, sendLimitGreenZen = FALSE;
	Bool sendEventMsg = FALSE;

	actEvent.BeginPickupJob();

	std::map<IndexItem, Int32> viewCount;

	Zen pickupZen = 0;
	GreenZen pickupGreenZen = 0;

	for (const EntityId& entityId : vecDroppedId)
	{
		Entity4Zone* found = owner->GetSectorAction().GetEntityInMySector(entityId);
		VALID_DO(found && found->IsValid() && found->IsDropped(), continue);
		EntityDropped* droppedEntity = static_cast<EntityDropped*>(found);
		
		VALID_DO(ErrorItem::SUCCESS == CanPickupDroppedEntity(droppedEntity, manualRange), continue);

		const ItemConstPtr droppedItem = droppedEntity->GetItemAction().GetItem();
		VERIFY_DO(droppedItem, continue);

		if (false == actEvent.PickupItem(droppedItem))
		{
			sendEventMsg = TRUE;
			continue;
		}

		if (SCRIPTS.IsZenType(droppedItem->GetItemIndex()))
		{
			result = CheckAndPickupZen(droppedItem->GetCurStackCount(), droppedEntity->GetItemAction().GetBuffBonus(), pickupZen);
			if (result == ErrorItem::SUCCESS)
			{
				droppedEntity->GetItemAction().RemoveGrid();
			}
			else
			{
				switch (result)
				{
				case ErrorItem::E_ZEN_LIMIT:
					sendLimitZen = TRUE;
					break;
				default:
					break;
				}
			}
			continue;
		}
		else if (SCRIPTS.IsGreenZenType(droppedItem->GetItemIndex()))
		{
			result = CheckAndPickupGreenZen(droppedItem->GetCurStackCount(), droppedEntity->GetItemAction().GetBuffBonus(), pickupGreenZen);
			if (result == ErrorItem::SUCCESS)
			{
				droppedEntity->GetItemAction().RemoveGrid();
			}
			else
			{
				switch (result)
				{
				case ErrorItem::E_ZEN_LIMIT:
					sendLimitGreenZen = TRUE;
					break;
				default:
					break;
				}
			}
			continue;
		}
		else if (SCRIPTS.IsRiftPointType(droppedItem->GetItemIndex()))
		{
			Int32 remainRiftPoint = 0;
			if (ErrorItem::SUCCESS == PickupRiftPoint(droppedItem->GetCurStackCount(), remainRiftPoint))
			{
				if (0 < remainRiftPoint)
				{
					// 시공의 조각을 다 습득하지 못한 경우 남은 개수만큼 재드랍 해준다
					owner->DropAgainRemainRiftPoint(droppedEntity, remainRiftPoint);
				}

				droppedEntity->GetItemAction().RemoveGrid();
			}
			continue;
		}
		else if (false == bag.Insert(ItemData(*droppedItem)))
		{
			AchievementPickupItemIndexEvent	achievementEvent(owner, *droppedItem);
			owner->GetAchievementAction().OnEvent(&achievementEvent);
			actEvent.LeavePickUpItem(droppedItem->GetItemIndex(), droppedItem->GetCurStackCount());

			continue;
		}

		vecDropped.push_back(droppedEntity);
	}

	if (pickupZen != 0)
	{
		PickupZen(pickupZen);
	}

	if (pickupGreenZen != 0)
	{
		PickupGreenZen(pickupGreenZen);
	}

	if (pickupZen != 0 ||
		pickupGreenZen != 0)
	{
		AttributePlayer& attrPlayer = GetOwnerPlayer()->GetPlayerAttr();

		EReqLogDb2* log = NEW EReqLogDb2;
		EventPtr logEventPtr(log);

		EventSetter::FillLogForEntity(LogCode::E_ITEM_PICKUP, owner, log->action);
		log->action.var64s.push_back(attrPlayer.money);		// 최종 젠
		log->action.var64s.push_back(attrPlayer.greenZen);	// 최종 마정석
		log->action.var64s.push_back(pickupZen);			// 획득 젠
		log->action.var64s.push_back(pickupGreenZen);		// 획득 마정석

		SendDataCenter(logEventPtr);
	}

	if (sendLimitZen)
	{
		SendSystemMsg(owner, SystemMessage(L"sys", L"Msg_Zen_Err_Max"));
	}
	else if (sendLimitGreenZen)
	{
		SendSystemMsg(owner, SystemMessage(L"sys", L"Msg_GreenZen_Err_Max"));
	}

	if (sendEventMsg)
	{
		SendSystemMsg(owner, SystemMessage(L"sys", L"Msg_Cannot_Get_Item_0"));
	}

	if (false == vecDropped.empty())
	{
		ItemUserPushTransaction * pushTr = NEW ItemUserPushTransaction(__FUNCTION__, __LINE__, owner, LogCode::E_ITEM_PICKUP);
		TransactionPtr transPtr(pushTr);
		{
			if (bag.Write(__out transPtr) == false)
			{
				actEvent.CommitPickupJob(false);
				return ErrorItem::E_TRANSACTION;
			}

			if (TransactionSystem::Register(transPtr) == false)
			{
				actEvent.CommitPickupJob(false);
				return ErrorItem::E_TRANSACION_FAILED;
			}
		}

		for (EntityDropped* dropped : vecDropped)
		{
			dropped->GetItemAction().RemoveGrid();

			// 180906 로그를 남기기위해 추가함. 이호석.
			const ItemConstPtr droppedItem = dropped->GetItemAction().GetItem();
			pushTr->SetLogItems(ItemData(*droppedItem));
		}
	}

	actEvent.CommitPickupJob(true);

	return ErrorItem::SUCCESS;
}

void ActionPlayerInventory::PickupAllRangeItems()
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	Vector3 pos;
	owner->GetPositionView().GetPosition(pos);

	VectorEntity droppedItmes;
	SectorCollidedHelper::GetEntitiesAround(
		owner->GetSector(),
		pos,
		Limits::MAX_PICK_UP_ITEM_RANGE,
		[](const Entity4Zone* entity) -> bool
	{
		return entity->IsDropped();
	},
		droppedItmes);

	VectorEntityId vecDropped;
	for (auto entity : droppedItmes)
	{
		vecDropped.push_back(entity->GetId());
	}

	ReqPickupItem(vecDropped, Limits::MAX_PICK_UP_ITEM_RANGE);
}

Bool ActionPlayerInventory::CheckShopPack(const ShopType shopType, const ShopPack& pack, Bool sendMsg /*= true */)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), false);

	switch (shopType)
	{
	case ShopType::BUY:
	case ShopType::REBUY:
	{
		switch (pack.type)
		{
		case ShopPack::Type::TOKEN:
		{
			InvenRange ranges(InvenBags << PrivateStorages << AccountStorages << PremiumBags);

			if (false == IsEnoughItem(ranges, pack.index, pack.quentity))
			{
				sendMsg ? SendSystemMsg(player, SystemMessage(L"sys", L"Msg_Itembuy_Err_Token")) : sendMsg;
				return false;
			}
			break;
		}
		case ShopPack::Type::ZEN:
		{
			if (false == player->IsEnoughZen(pack.quentity))
			{
				sendMsg ? SendSystemMsg(player, SystemMessage(L"sys", L"Msg_Item_Err_Enough_Money")) : sendMsg;
				return false;
			}
			break;
		}
		case ShopPack::Type::GREENZEN:
		{
			if (false == player->IsEnoughGreenZen(pack.quentity))
			{
				sendMsg ? SendSystemMsg(player, SystemMessage(L"sys", L"Msg_Item_Err_Enough_Greenzen")) : sendMsg;
				return false;
			}
			break;
		}
		case ShopPack::Type::RIFT_POINT:
		{	
			if (false == player->IsEnoughRiftPoint(pack.quentity))
			{
				// 시스템 메시지 추가 필요!
				return false;
			}
			break;
		}
		case ShopPack::Type::CONTRIBUTION:
		{
			if (false == player->IsEnoughContributionPoint(pack.quentity))
			{
				// 시스템 메시지 추가 필요!
				return false;
			}
			break;
		}
		default:
			MU2_ASSERT(false);
			return false;
		}
		break;
	}

	case ShopType::SELL:
		switch (pack.type)
		{
		case ShopPack::Type::ZEN:
		{	
			if (false == player->CheckZen(pack.quentity, ChangedMoney::ADD))
			{
				sendMsg ? SendSystemMsg(player, SystemMessage(L"sys", L"Msg_Shop_Err_ZenMax")) : sendMsg;
				return false;
			}
		}
		break;
		}
		break;

	default:
		MU2_ASSERT(false);
		return false;
	}

	return true;
}


Bool ActionPlayerInventory::PayShopPack(const ShopPack& pack, ItemBag& bag)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), false);

	switch (pack.type)
	{
	case ShopPack::Type::TOKEN:
	{
		VALID_RETURN( bag.Remove( ItemData( pack.index, pack.quentity )), false );
		break;
	}
	case ShopPack::Type::ZEN:
	{
		VERIFY_RETURN( owner->SubMoney( pack.quentity ), false );
		owner->SendChangeMoney(pack.quentity, ChangedMoney::SUB );
		break;
	}
	case ShopPack::Type::GREENZEN:
	{
		VERIFY_RETURN( owner->SubGreenZen( pack.quentity ), false );
		owner->SendChangeGreenZen(pack.quentity, ChangedMoney::SUB);
		break;
	}
	default:
		MU2_ASSERT(false);
		return false;
	}
	return true;
}


Bool ActionPlayerInventory::CheckSummonerEquipedWeaponType(WeaponType::Type eWeaponType)
{
	
	WeaponType::Type wtLeft = WeaponType::NONE;	
	WeaponType::Type wtRight = WeaponType::NONE;
	
	if (GetEquipInven().IsFullWeapons(wtLeft, wtRight))
	{
		if (eWeaponType != wtLeft  && eWeaponType != wtRight)
		{
			return true;
		}
	}
	else
	{
		// 양손무기를 착용한 경우 ( 활 )
		if (eWeaponType != wtRight)
		{
			return true;
		}
	}
	return false;

}

Bool ActionPlayerInventory::GetFreeSlots(const InvenRange& range, OUT Slots& foundSlots, Int32 findCount) const
{
	VALID_RETURN(findCount, true);
	
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), false);

	return SlotFinderFree(*owner).Find(range, foundSlots, findCount);
}

ErrorItem::Error ActionPlayerInventory::extract(ItemBag& bag, const ItemPos& pos, ItemDatas& failed, ItemDatas& itemRewards, UInt64& greenZen)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);
		
	if (player->IsTrading())
	{
		SystemMessage sysMsg(L"sys", L"Msg_UserTrade_Invalid_Not_Work");
		SendSystemMsg(player, sysMsg);
		return ErrorItem::E_TRADING;
	}

	if (player->IsDead())
	{
		SystemMessage sysMsg(L"sys", L"Msg_ItemExtract_Error_Dead");
		SendSystemMsg(player, sysMsg);
		return ErrorItem::playerNotAlive;
	}

	if (false == pos.IsInvenBags())
	{
		SystemMessage sysMsg(L"sys", L"Msg_ItemExtract_Err_EquipItem");
		SendSystemMsg(player, sysMsg);
		return ErrorItem::E_INVALID_INVENTORY;
	}

	Inven* foundInven = GetInven(pos);
	VERIFY_RETURN(foundInven, ErrorItem::inventoryNotFound);

	const ItemConstPtr item = GetItem(pos);
	if (item == nullptr)
	{
		SystemMessage sysMsg(L"sys", L"Msg_Item_Err_StackValue");
		SendSystemMsg(player, sysMsg);
		return ErrorItem::itemNotFound;
	}

#ifdef __lockable_item_181001
	VALID_DO( item->IsUnLocakble(), SendSystemMsg( player, SystemMessage( L"sys", L"Msg_Item_locked_not_usable" ) ); return ErrorItem::E_ITEM_LOCKED );
#endif

	if ( item->GetInfoElem().extractGroups.empty())
	{
		SystemMessage sysMsg(L"sys", L"Msg_ItemExtract_Err_InvalidType");
		SendSystemMsg(player, sysMsg);
		return ErrorItem::extractGroupsEmpty;
	}

	//std::map<UInt32, ItemData> itemLogs;

	ItemData itemData;
	item->FillUpItemData(OUT itemData);
	bag.Remove(itemData);

	greenZen = 0;

	for (const auto& extractGroup : item->GetInfoElem().extractGroups)
	{
		const ItemExtractGroupElem* elem = SCRIPTS.GetItemExtractGroupScript(extractGroup);
		if (elem == nullptr)
		{
			SystemMessage sysMsg(L"sys", L"Msg_ItemExtract_Err_InvalidType");
			SendSystemMsg(player, sysMsg);
			return ErrorItem::elemNotFound;
		}

		UInt32 maxRate = 0;
		std::for_each(elem->elems.begin(), elem->elems.end()
			, [&maxRate](const ItemExtractRewardElem& reward) {maxRate += reward.rate;});

		if (maxRate <= 0)
		{
			SystemMessage sysMsg(L"sys", L"Msg_ItemExtract_Err_InvalidType");
			SendSystemMsg(player, sysMsg);
			return ErrorItem::invalidRate;
		}

#ifdef ADD_EXTRACT_ITEM_RATE_TYPE_ALL_by_cheolhoo_180726
		for(auto iter = elem->rateAllElems.begin(); iter != elem->rateAllElems.end(); ++iter)
		{
			const ItemExtractRewardElem& reward = *iter;
			const ItemInfoElem* itemInfoElem = SCRIPTS.GetItemInfoScript(reward.index);
			if(itemInfoElem == nullptr)
			{
				SystemMessage sysMsg(L"sys", L"Msg_ItemExtract_Err_InvalidType");
				SendSystemMsg(player, sysMsg);
				return ErrorItem::elemNotFound;
			}

			Int32 totalcount = reward.min * item->GetCurStackCount();
			Int32 count = 0;
			while(totalcount > 0)
			{
				if(totalcount > itemInfoElem->GetMaxStackCount())
					count = itemInfoElem->GetMaxStackCount();
				else
					count = totalcount;

				ItemData rewardItem;
				rewardItem.indexItem = reward.index;
				rewardItem.SetCurStackCount(count);

				if(!bag.Insert(rewardItem))
				{
					failed.push_back(rewardItem);
				}
				else
				{
					auto findIter = std::find_if(itemRewards.begin(), itemRewards.end(), [&](const ItemData& item) { return item.indexItem == reward.index; });
					if(findIter != itemRewards.end())
					{
						findIter->IncCurStackCount(count);
					}
					else
					{
						rewardItem.indexItem = reward.index;
						rewardItem.SetCurStackCount(count);
						itemRewards.push_back(rewardItem);
					}
				}

				totalcount -= count;
			}
		}
#endif

		UInt32 accum = 0;
		UInt32 seed = GetRandBetween(1, maxRate);
		ItemExtractRewardElem rewardElem;
		for (auto iter = elem->elems.begin(); iter != elem->elems.end(); ++iter)
		{
			const ItemExtractRewardElem& reward = *iter;
			accum += reward.rate;
			if (seed <= accum)
			{
				rewardElem = reward;
				break;
			}
		}

		const ItemInfoElem* itemInfoElem = SCRIPTS.GetItemInfoScript(rewardElem.index);
		if (itemInfoElem == nullptr)
		{
			SystemMessage sysMsg(L"sys", L"Msg_ItemExtract_Err_InvalidType");
			SendSystemMsg(player, sysMsg);
			return ErrorItem::elemNotFound;
		}

		Int32 totalcount = rewardElem.min * item->GetCurStackCount();
		Int32 count = 0;

		while (totalcount > 0)
		{
			if (totalcount > itemInfoElem->GetMaxStackCount())
				count = itemInfoElem->GetMaxStackCount();
			else
				count = totalcount;


			ItemData rewardItem;
			rewardItem.indexItem = rewardElem.index;
			rewardItem.SetCurStackCount(count);

			if (!bag.Insert(rewardItem))
			{
				failed.push_back(rewardItem);
			}
			else
			{
				auto iter = std::find_if(itemRewards.begin(), itemRewards.end(), [&](const ItemData& item) { return item.indexItem == rewardElem.index; });
				if (iter != itemRewards.end())
				{
					iter->IncCurStackCount(count);
				}
				else
				{
					rewardItem.indexItem = rewardElem.index;
					rewardItem.SetCurStackCount(count);
					itemRewards.push_back(rewardItem);
				}
			}

			totalcount -= count;
		}
	}

	return ErrorItem::SUCCESS;
}

Bool ActionPlayerInventory::isWorking() const
{
	if (m_lock.lock == (long)0)
	{
		return false;
	}

	MU2_ASSERT(m_lock.lock > (long)0);
	return true;
}

Bool ActionPlayerInventory::SendTransaction(TransactionPtr trans)
{
	if (trans == nullptr)
	{
		MU2_ASSERT(false);
		return false;
	}

	if (!trans->BeginTransaction())
	{
		// 실패했을 때 처리
		trans->Unlock(m_lock);
		trans->OnBeginFailed();
		return false;
	}

	trans->SendToDb();
	return true;
}

void ActionPlayerInventory::ProcessTrasanctionQueue()
{
	while (!m_transactionQueue.empty())
	{
		TransactionPtr trans = m_transactionQueue.front();
		if (trans->IsBegun())
		{
			break;
		}

		if (!SendTransaction(trans))
		{
			m_transactionQueue.pop_front();
		}
	}
}

void ActionPlayerInventory::ProcessTranaction(EventPtr& e, const ErrorDB::Error eError, const Int32 fcsResult)
{
	if (m_transactionQueue.empty())
	{
		MU2_ASSERT(false);
		return;
	}

	TransactionPtr trans = m_transactionQueue.front();
	m_transactionQueue.pop_front();

	if (trans)
	{
		if (eError == ErrorDB::SUCCESS)
		{
			if (!trans->CommitTransaction(e))
			{
				// [fixed by ssaturn 2015-04-28 실패에 대한 올바른 처리가 필요]
				trans->OnCommitFailed();
			}
		}
		else
		{
			DBJobResult result;
			result.result = eError;
			result.fcsresult = fcsResult;
			trans->RollbackTransaction(e, result);
		}

		trans->Unlock(m_lock);
		GetOwnerPlayer()->GetKnightageStorageAction().CloseKnightageStorage();
	}

	ProcessTrasanctionQueue();
}

Bool ActionPlayerInventory::RegisterTransaction(TransactionPtr trans)
{
	if (!trans->Lock(m_lock))
	{
		return false;
	}

	if (m_transactionQueue.empty())
	{
		if (!SendTransaction(trans))
		{
			return false;
		}
	}

	m_transactionQueue.push_back(trans);
	return true;
}

Bool ActionPlayerInventory::IsFreeSlot(const ItemPos& pos) const
{
	Inven* found = GetInven(pos);
	VALID_RETURN(found, false);
	return found->IsFreeSlot(pos.GetSlot());
}

Bool ActionPlayerInventory::IsPushable(const ItemPos& toPos, const ItemConstPtr& item, Bool isAllowBlockNExpired)
{
	Inven* found = GetInven(toPos);
	VALID_RETURN(found, false);
	return found->IsPushable(toPos.GetSlot(), item, isAllowBlockNExpired);
}

Bool ActionPlayerInventory::IsPopable(const ItemConstPtr& item) const
{
	VERIFY_RETURN(item, false);

	Inven* found = GetInven(item->GetPos());
	VALID_RETURN(found, false);
	return found->IsPopable(item);
}

ItemPtr ActionPlayerInventory::GetItem(const ItemPos& pos) const
{
	Inven* found = GetInven(pos);
	VALID_RETURN(found, NULL);

	if (found->IsNonSlotBags())
	{
		VERIFY_DO(!"Must check ItmePos!!!!", MustErrorCheck);
	}

	return found->GetItemBySlot(pos.GetSlot());
}

ItemPtr ActionPlayerInventory::GetItemByPlace(const ItemPlace& place) const
{
	if (place.itemId == 0)
	{
		// 이런일이 있으면 안되는데, ItemPos, ItemPlace, ItemSlotInfo를 혼용하여 사용하면서
		// ItemId를 누락하는 경우가 많다. 그래서 어쩔수 없이 이렇게 예외처리하고
		// 로그를 남기자

		VERIFY_DO(!"ItemId is Zero", MustErrorCheck);
		return GetItem(place.GetPos());
	}
	else
	{
		Inven* found = GetInven(place.GetPos());
		VALID_RETURN(found, NULL);
		return found->GetItemByPlace(place);
	}
}

const ItemConstPtr ActionPlayerInventory::GetItemByIndex(const InvenRange& range, IndexItem indexItem) const
{	
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), NULL);

	Items foundItems;
	VALID_RETURN(ItemFinderByIndex(*owner, indexItem).Find(range, foundItems, 1), NULL);
	return foundItems.front();
}

ErrorItem::Error ActionPlayerInventory::StorageMove(const ItemPlace& srcSlotInfo, const ItemPos& _targetPos)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	ItemPos targetPos = _targetPos;
	VERIFY_RETURN(false == srcSlotInfo.IsEquipBag() && false == targetPos.IsEquipBag(), ErrorItem::E_WRONG_INVENTORY_TYPE);
	VERIFY_RETURN(srcSlotInfo.IsStoragableBags() != targetPos.IsStoragableBags(), ErrorItem::E_WRONG_INVENTORY_TYPE);

	Inven* srcInven = GetInven(srcSlotInfo);
	VERIFY_RETURN(srcInven, ErrorItem::inventoryNotFound);
	VERIFY_RETURN(srcInven->IsUsableRemoteNpcFunction(), ErrorItem::IsUsableRemoteNpcFunctionFailed);

	Inven* tarInven = GetInven(targetPos);	
	VERIFY_RETURN(tarInven, ErrorItem::inventoryNotFound);
	VERIFY_RETURN(tarInven->IsUsableRemoteNpcFunction(), ErrorItem::IsUsableRemoteNpcFunctionFailed);
	VERIFY_RETURN(srcInven->IsStorageAccount() != tarInven->IsStorageAccount(), ErrorItem::E_WRONG_INVENTORY_TYPE);
	

	const ItemConstPtr source = GetItemByPlace(srcSlotInfo);
	VERIFY_RETURN(source, ErrorItem::itemNotFound);

	VERIFY_RETURN(IsPopable(source), ErrorItem::IsPopableFailed);
	VERIFY_RETURN(IsPushable(targetPos, source), ErrorItem::IsPopableFailed);
	
	TransactionPtr transPtr(NEW ItemStorageTransaction(__FUNCTION__, __LINE__, player, LogCode::E_ITEM_STORAGE));
	{
		ItemBag bag(*player);
		
		const Bool slotNotDefined = (targetPos.GetSlot() == SlotType::NONE);	// 유저가 임의로 위치 지정하지 않은 경우
		Int32 remained = source->GetCurStackCount();
		InvenRange invenRange(targetPos);

		bag.PushTargetInvenRange(invenRange);

		if (source->IsStackable() && slotNotDefined)	// 쌓을 수 있는 아이템이고 위치지정이 안 되어 있을 경우, 쌓을 수 있는 아이템 찾아서 업데이트
		{
			const Int32 maxOverlap = source->GetMaxStackCount();

			Items items;
			ItemFinderByIndex(*player, source->GetItemIndex()).Find(invenRange, items);

			for (const auto& item : items)
			{
				Int32 stack = std::min(remained, maxOverlap - item->GetCurStackCount());
				if (stack > 0)
				{
					ItemData itemCloned = item->Clone();
					remained -= stack;
					itemCloned.IncCurStackCount(stack);
					VERIFY_RETURN(bag.Update(item, itemCloned), ErrorItem::E_TRANSACION_FAILED);
				}

				if (remained <= 0)
				{
					break;
				}
			}
		}

		if (remained > 0)		// 갯수가 남아있다면 이동
		{

			if (slotNotDefined)
			{
				VERIFY_RETURN(GetFreeSlot(invenRange, targetPos), ErrorItem::E_NOT_FREESLOT);
				tarInven = GetInven(targetPos);
				VERIFY_RETURN(tarInven, ErrorItem::inventoryNotFound);
			}
			
			VERIFY_RETURN(tarInven->IsPushable(targetPos.GetSlot(), source), ErrorItem::IsPushableFailed);
			VERIFY_RETURN(NULL == GetItem(targetPos), ErrorItem::E_NOT_FREESLOT);

			ItemData srcItemCloned = source->Clone();
			srcItemCloned.SetPos(targetPos);
			srcItemCloned.SetCurStackCount(remained);

			VERIFY_RETURN(bag.Update(source, srcItemCloned), ErrorItem::E_TRANSACION_FAILED);
		}
		else					// 갯수가 없다면 삭제
		{
			VERIFY_RETURN(bag.Remove(*source), ErrorItem::E_TRANSACION_FAILED);
		}

		VERIFY_RETURN(bag.Write(transPtr), ErrorItem::BagWriteFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::E_TRANSACION_FAILED);
	}

	return ErrorItem::SUCCESS;
}

Bool ActionPlayerInventory::SendItemInfoSystemMessage(const std::wstring& messageType, const ItemData& item, Int32 sendType)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), false);

	EReqfGameItemInfoSystemMsg* req = NEW EReqfGameItemInfoSystemMsg;
	req->sendType = sendType;
	req->ownerName = player->GetCharName();
	req->systemMsgKey = messageType;
	req->item = item;
	player->SendToClient(EventPtr(req));

	return true;
}

Bool ActionPlayerInventory::Pay(const ShopPacks& shopPacks, ItemBag& bag, TransactionPtr transPtr /*= nullptr */)
{
	for (const auto& shopPack : shopPacks)
	{
		if (false == CheckShopPack(ShopType::BUY, shopPack))
		{
			return false;
		}
	}

	for (const auto& shopPack : shopPacks)
	{
		if (false == PayShopPack(shopPack, bag))
		{
			return false;
		}
	}

	if (0 < bag.GetSize())
	{
		VERIFY_RETURN(transPtr, false);
		VERIFY_RETURN(bag.Write(transPtr), false);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), false);
	}
	return true;
}

ErrorItem::Error ActionPlayerInventory::CustomizeInsert(const ItemPlace& srcPlace, const ItemPlace& tarPlace)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	const ItemConstPtr srcItem = GetItemByPlace(srcPlace);
	const ItemConstPtr tarItem = GetItemByPlace(tarPlace);
	VERIFY_RETURN(srcItem && tarItem, ErrorItem::itemNotFound);
#ifdef __lockable_item_181001
	VALID_DO( srcItem->IsUnLocakble(), SendSystemMsg( player, SystemMessage( L"sys", L"Msg_Item_locked_not_usable" ) ); return ErrorItem::E_ITEM_LOCKED );
	VALID_DO( tarItem->IsUnLocakble(), SendSystemMsg( player, SystemMessage( L"sys", L"Msg_Item_locked_not_usable" ) ); return ErrorItem::E_ITEM_LOCKED );
#endif

	// 동일 외형 정보
	ItemCustomizingInfo srcCustomInfo, tarCustomInfo;
	srcItem->FillUpCustomizingInfo(srcCustomInfo);
	tarItem->FillUpCustomizingInfo(tarCustomInfo);
	VERIFY_RETURN(srcCustomInfo != tarCustomInfo, ErrorItem::E_FAILED);

	// 타입
	VERIFY_RETURN(ItemDivisionType::IsCustomizingType(tarItem->GetDivision()), ErrorItem::E_INVALID_DIVISION);
	VERIFY_RETURN((srcItem->GetDivision() == tarItem->GetDivision()), ErrorItem::E_INVALID_DIVISION);
	VERIFY_RETURN(tarItem->GetWeaponType() == srcItem->GetWeaponType(), ErrorItem::E_INVALID_TYPE);

	// 직업/레벨 착용조건 	
	VERIFY_RETURN(player->CheckRequestLevel(srcItem), ErrorItem::E_LIMIT_LEVEL);
	VERIFY_RETURN(player->CheckRequestClass(srcItem), ErrorItem::E_INVALID_CLASS);
	VERIFY_RETURN(player->CheckRequestLevel(tarItem), ErrorItem::E_LIMIT_LEVEL);
	VERIFY_RETURN(player->CheckRequestClass(tarItem), ErrorItem::E_INVALID_CLASS);

	// 기간제
	VERIFY_RETURN( false == srcItem->IsPeriodable(), ErrorItem::E_EXCEED_PERIOD);

	// 수수료
	const auto elem = SCRIPTS.GetItemCustomizingCost(tarItem->GetGrade(), tarItem->GetItemLevel());
	VERIFY_RETURN(elem, ErrorItem::E_FAILED);

	const auto zen = elem->insertZen, greenZen = elem->insertGreenZen;
	VERIFY_RETURN(zen == 0 || player->IsEnoughZen(zen), ErrorItem::E_NOT_ENOUGH_MONEY);
	VERIFY_RETURN(greenZen == 0 || player->IsEnoughGreenZen(greenZen), ErrorItem::E_NOT_ENOUGH_GREEN_ZEN);
	
	// 외형 변화--
	ItemData tarItemCloned = tarItem->Clone();
	tarItemCloned.customizingInfo = std::move(srcCustomInfo);

	ItemBag bag(*player);
	VERIFY_RETURN(bag.Update(tarItem, tarItemCloned), ErrorItem::BagUpdateFailed);
	VERIFY_RETURN(bag.Remove(*srcItem), ErrorItem::BagRemoveFailed);

	TransactionPtr transPtr(NEW ItemCustomizingTransaction(__FUNCTION__, __LINE__, player, LogCode::E_ITEM_CUSTOMIZING, CustomizingType::INSERT, zen, greenZen));
	{
		VERIFY_RETURN(bag.Write(transPtr), ErrorItem::E_TRANSACTION);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::TranRegisterFailed);
	}

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerInventory::CustomizeRemove(const ItemPlace& tarPlace)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	const ItemConstPtr tarItem = GetItemByPlace(tarPlace);
	VERIFY_RETURN(tarItem, ErrorItem::E_NOT_EXIST);
#ifdef __lockable_item_181001
	VALID_DO( tarItem->IsUnLocakble(), SendSystemMsg( player, SystemMessage( L"sys", L"Msg_Item_locked_not_usable" ) ); return ErrorItem::E_ITEM_LOCKED );
#endif
	// 커스터마이징 되어있지 않음
	
	VERIFY_RETURN(tarItem->GetCustomizingInfo().itemIndex, ErrorItem::E_FAILED);

	// 타입
	VERIFY_RETURN(ItemDivisionType::IsCustomizingType(tarItem->GetDivision()), ErrorItem::E_INVALID_TYPE);

	// 수수료
	const auto elem = SCRIPTS.GetItemCustomizingCost(tarItem->GetGrade(), tarItem->GetItemLevel());
	VERIFY_RETURN(elem, ErrorItem::E_FAILED);

	const auto zen = elem->removeZen, greenZen = elem->removeGreenZen;
	
	VERIFY_RETURN(zen <= 0 || player->IsEnoughZen(zen), ErrorItem::E_NOT_ENOUGH_MONEY);
	VERIFY_RETURN(greenZen <= 0 || player->IsEnoughGreenZen(greenZen), ErrorItem::E_NOT_ENOUGH_GREEN_ZEN);
	

	TransactionPtr transPtr(NEW ItemCustomizingTransaction(__FUNCTION__, __LINE__, player, LogCode::E_ITEM_CUSTOMIZING, CustomizingType::REMOVE, zen, greenZen));
	{
		ItemBag bag(*player);

		// 외형 제거--
		ItemData tarItemCloned = tarItem->Clone();
		tarItemCloned.customizingInfo = ItemCustomizingInfo();

		VERIFY_RETURN(bag.Update(tarItem, tarItemCloned), ErrorItem::BagUpdateFailed);

		VERIFY_RETURN(bag.Write(transPtr), ErrorItem::BagWriteFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::E_TRANSACTION);
	}

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerInventory::GetAbilityOptionChangeList(const ItemPos& selectedItemPos, const AbilityTypeSection& selectedAbilityOption)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	const ItemPtr item = GetItem(selectedItemPos);
	VERIFY_RETURN(item, ErrorItem::E_EXIST_ITEM);
#ifdef __lockable_item_181001
	VALID_DO( item->IsUnLocakble(), SendSystemMsg( player, SystemMessage( L"sys", L"Msg_Item_locked_not_usable" ) ); return ErrorItem::E_ITEM_LOCKED );
#endif
	VERIFY_RETURN(item->IsIdentified(), ErrorItem::E_UNIDENTIFIED_ITEM);
	// 이미 진행중인 아이템
	{
		auto changedAbilityOptionPtr = item->GetChangedAbilityOptionPtr();
		if (changedAbilityOptionPtr)
		{
			MU2_ASSERT(false);
			item->SetChangeAbilityOptionPtr(nullptr);
		}
	}

	ItemData itemCloned = item->Clone();

	// 바꿀수 있는 옵션인지?
	auto itemOptionDisableScript = SCRIPTS.GetScript<ItemOptionChangerDisableScript>();
	VERIFY_RETURN(itemOptionDisableScript, ErrorItem::E_NOT_EXIST);
	VERIFY_RETURN(itemOptionDisableScript->IsAvailable(selectedAbilityOption.type), ErrorItem::E_NOT_EXIST);

	// 고정 옵션인지?
	const ItemRandomOptionFixedScript* fixedOptionScript = SCRIPTS.GetScript<ItemRandomOptionFixedScript>();
	VERIFY_RETURN(fixedOptionScript, ErrorItem::E_NOT_EXIST);
	VERIFY_RETURN(false == fixedOptionScript->IsFixedOption(itemCloned.indexItem, selectedAbilityOption), ErrorItem::E_FIXED_OPTION);

	// 선택한 옵션이 존재하는지
	VERIFY_RETURN(item->CheckExistAbilityOption(selectedAbilityOption), ErrorItem::E_NOT_EXIST_ABILITY);

	// 이미 바꾼 옵션이 있다면 그 옵션만 바꿀 수 있다.
	VERIFY_RETURN(itemCloned.changedAbilityOption.section == EffectSection::NONE || itemCloned.changedAbilityOption == selectedAbilityOption, ErrorItem::E_ALREADY_CHANGE_ABILITY);	

	const ItemOptionChangerCostScript* itemOptionChangerCostScript = SCRIPTS.GetScript<ItemOptionChangerCostScript>();
	VERIFY_RETURN(itemOptionChangerCostScript, ErrorItem::E_NOT_EXIST);

	auto itemOptionChangeCostElems = itemOptionChangerCostScript->Get(item->GetWeaponType(), static_cast<Byte>(item->GetGrade()), static_cast<Byte>(item->GetItemLevel()));
	if (nullptr == itemOptionChangeCostElems)
	{
		MU2_WARN_LOG(LogCategory::CONTENTS, "%s> ItemOptionChangeCost.csv attr does not exist. proceed without cost. [weaponType:%d][grade:%d]",
			__FUNCTION__, item->GetWeaponType(), item->GetGrade());
	}

	// 바꿀 수 있는 옵션 목록을 모두 가져옴
	AbilityOptionIndexVector optionList;
	getEnableAbilityOptionChangeList(item, selectedAbilityOption, optionList);
	VALID_RETURN(optionList.size(), ErrorItem::E_NOT_EXIST_OPTION_LIST);


	Byte maxCnt = 1 + static_cast<Byte>(player->GetMembershipAction().GetValueAtBenefitType(EMembershipBenefitType::eValue_NpcOptionChange));

	AbilityOptionVector extractedOptionList;
	for (auto i = 0; i < maxCnt; ++i)
	{
		UInt32 randIdx = GetRandBetween(0, static_cast<Int32>(optionList.size() - 1));
		Int32 optionValue = ItemFactory::GetRandomOptionValue(optionList[randIdx].second, item);

		extractedOptionList.emplace_back(optionList[randIdx].first.type, optionList[randIdx].first.section, optionValue);
		theGameMsg.SendGameDebugMsg(player, L"옵션 목록 [type:%d][section:%d][optionvalue:%d].\n",
			optionList[randIdx].first.type, optionList[randIdx].first.section, optionValue);
	}

	auto changeAbilityOptionPtr = std::make_shared<ChangeAbilityOption>();
	changeAbilityOptionPtr->selectedAbilityOption = selectedAbilityOption;
	changeAbilityOptionPtr->extractedAbilityOptionList = std::move(extractedOptionList);

	// 현재 능력 재설정 변경 횟수
	const Byte curOptionChangeCount = static_cast<Byte>(itemCloned.itemOptionChangeCount);

	// 능력 재설정 변경 횟수 증가 처리 (옵션 목록을 가져오면 증가)
	itemCloned.itemOptionChangeCount = (Limits::MAX_ITEM_OPTION_CHANGE_INCREASE_COUMT < (itemCloned.itemOptionChangeCount + 1))
		? static_cast<Byte>(Limits::MAX_ITEM_OPTION_CHANGE_INCREASE_COUMT) : (itemCloned.itemOptionChangeCount + 1);

	// 능력 재설정을 하면 아이템이 귀속된다 (옵션 목록을 가져오면 귀속)
	ItemFactory::CheckAndBind(player, item, EquipBindState::CHANGE_OPTION, itemCloned.bindProperty);

	TransactionPtr transPtr = std::make_shared< ItemOptionChangeListTransaction >(__FUNCTION__, __LINE__, player, LogCode::E_ITEM_OPTION_CHANGE_LIST, changeAbilityOptionPtr, itemOptionChangeCostElems, item->GetItemId(), curOptionChangeCount);
	{
		ItemBag bag(*player);
		VERIFY_RETURN(bag.Update(item, itemCloned), ErrorItem::BagUpdateFailed);
		VERIFY_RETURN(bag.Write(transPtr), ErrorItem::BagWriteFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::E_TRANSACION_FAILED);
	}

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerInventory::SelectAbilityOptionChange(const ItemPos& selectedItemPos, const AbilityOption& changedAbilityOption)
{
	EntityPlayer* player = GetOwnerPlayer();

	const ItemPtr item = GetItem(selectedItemPos);
	VERIFY_RETURN(item, ErrorItem::E_EXIST_ITEM);
	VERIFY_RETURN(item->IsIdentified(), ErrorItem::E_UNIDENTIFIED_ITEM);
#ifdef __lockable_item_181001
	VALID_DO( item->IsUnLocakble(), SendSystemMsg( player, SystemMessage( L"sys", L"Msg_Item_locked_not_usable" ) ); return ErrorItem::E_ITEM_LOCKED );
#endif
	auto changedAbilityOptionPtr = item->GetChangedAbilityOptionPtr();
	VERIFY_RETURN(changedAbilityOptionPtr, ErrorItem::E_NOT_EXIST_OPTION_LIST);

	item->SetChangeAbilityOptionPtr(nullptr);

	// 재설정 선택하지 않음(에러 아님)
	if (changedAbilityOption.section == EffectSection::NONE)
	{
		EResItemAbilityOptionChangeSelect* res = NEW EResItemAbilityOptionChangeSelect;
		res->result = ErrorItem::SUCCESS;
		item->FillUpItemData(res->item);
		SERVER.SendToClient(player, EventPtr(res));
		return ErrorItem::SUCCESS;
	}

	auto it = std::find(changedAbilityOptionPtr->extractedAbilityOptionList.cbegin(),
		changedAbilityOptionPtr->extractedAbilityOptionList.cend(),
		changedAbilityOption);

	VERIFY_RETURN(it != changedAbilityOptionPtr->extractedAbilityOptionList.end(), ErrorItem::E_INVALID_SCRIPT);

	ItemData itemCloned = item->Clone();

	const AbilityTypeSection& selectedAbilityOption = changedAbilityOptionPtr->selectedAbilityOption;
	AbilityTypeSection::EraseAbilityOption(selectedAbilityOption, __inout itemCloned.abilityPackMap);


	AbilityPack pack = AbilityOption::ChangeToAbilityPack( changedAbilityOption );
	itemCloned.AppendOptionAbility<AbilityPack>( pack, itemCloned.abilityPackMap );

	itemCloned.changedAbilityOption.type = changedAbilityOption.type;
	itemCloned.changedAbilityOption.section = changedAbilityOption.section;


	TransactionPtr transPtr = std::make_shared< ItemOptionChangeSelectTransaction >(__FUNCTION__, __LINE__, player, LogCode::E_ITEM_OPTION_CHANGE_SELECT);
	{
		ItemBag bag(*player);
		VERIFY_RETURN(bag.Update(item, itemCloned), ErrorItem::BagUpdateFailed);

		VERIFY_RETURN(bag.Write(transPtr), ErrorItem::BagWriteFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::E_TRANSACION_FAILED);
	}

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerInventory::ItemIdentity(const ItemPos &targetItem)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	VERIFY_RETURN(targetItem.IsInvenBags(), ErrorItem::E_NOT_BAG_INVEN);

	const ItemConstPtr targetIem = GetItem(targetItem);
	VERIFY_RETURN(targetIem, ErrorItem::E_EXIST_ITEM);
	VERIFY_RETURN( false == targetIem->IsIdentified(), ErrorItem::E_IDENTIFY_ITEM);
#ifdef __lockable_item_181001
	VALID_DO( targetIem->IsUnLocakble(), SendSystemMsg( player, SystemMessage( L"sys", L"Msg_Item_locked_not_identified" ) ); return ErrorItem::E_ITEM_LOCKED );
#endif
	Zen priceZen = targetIem->GetIdentifyPriceZen();
	GreenZen priceGreenZen = targetIem->GetIdentifyPriceGreenZen();

	VERIFY_RETURN(priceGreenZen == 0 || player->IsEnoughGreenZen(priceGreenZen), ErrorItem::E_NOT_ENOUGH_GREEN_ZEN);
	VERIFY_RETURN(priceZen == 0 || player->IsEnoughZen(priceZen), ErrorItem::E_NOT_ENOUGH_MONEY);

	ItemData itemCloned = targetIem->Clone();

	ErrorItem::Error eError = ItemFactory::CreateItem4Identify(__inout itemCloned, targetIem->GetInfoElem(), targetIem->GetOptionElem());
	VERIFY_RETURN(eError == ErrorItem::SUCCESS, eError);

	TransactionPtr transPtr = std::make_shared< ItemIdentifyTransaction >(__FUNCTION__, __LINE__, player, LogCode::E_ITEM_IDENTIFY, targetItem);
	{
		if (priceGreenZen > 0)
		{
			transPtr->Bind(MoneyProcedure(player, DbChangedMoney(player->GetCharId(), priceGreenZen, ChangedMoney::SUB, ChangedMoney::GREENZEN)));
		}

		if (priceZen > 0)
		{
			transPtr->Bind(MoneyProcedure(player, DbChangedMoney(player->GetCharId(), priceZen, ChangedMoney::SUB, ChangedMoney::ZEN)));
		}

		ItemBag bag(*player);

		VERIFY_RETURN( bag.Update(targetIem, itemCloned), ErrorItem::BagUpdateFailed);
		VERIFY_RETURN( bag.Write(transPtr), ErrorItem::BagWriteFailed);
		VERIFY_RETURN( TransactionSystem::Register(transPtr), ErrorItem::E_TRANSACION_FAILED);
	}

	return ErrorItem::SUCCESS;
}

void ActionPlayerInventory::getEnableAbilityOptionChangeList(const ItemConstPtr& item, const AbilityTypeSection& selectedAbilityOption, AbilityOptionIndexVector &optionList)
{
	auto itemOptionChangerScript = SCRIPTS.GetScript<ItemOptionChangerScript>();
	VERIFY_RETURN(itemOptionChangerScript, );
	
	const ItemOptionChangerElems* itemOptionChangeElems = itemOptionChangerScript->Get(item->GetWeaponType(), item->GetGrade(), selectedAbilityOption);
	VERIFY_RETURN(itemOptionChangeElems, );

	for (const auto& itr : *itemOptionChangeElems)
	{
		const AbilityTypeSection& section = itr.first;
		const ItemOptionChangerElem& changerElem = itr.second;

		if (item->CheckExistAbilityOption(section) && section != selectedAbilityOption)
		{
			// 이미 존재하는 옵션은 또 나오면 안됨.
			continue;
		}

		optionList.emplace_back(section, changerElem.optionValueIdx);
	}
}

Bool ActionPlayerInventory::BeginPickUpItemFromWShop(WShopDbJob& wShopDbJob)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), false);

	ItemBag bag(*player);
	ItemDatas failed;

	for (auto& pickUpItem : wShopDbJob.pickupItems)
	{
		ItemData itemData(pickUpItem.itemCode, pickUpItem.itemQuantity, static_cast<UInt32>(time(nullptr)));
		itemData.period.SetInitMinuteOnActived(pickUpItem.itemMinute);

		if (!bag.Insert(itemData))
		{
			MU2_TRACE_LOG(LogCategory::WSHOP, L"bag.Insert Failed WSHOP(%u), charNo %d: JobNo: %llu : itemindex: %d",
				player->GetFcsAuthInfo().fcsAccountId, player->GetCharId(), wShopDbJob.jobNo, itemData.indexItem);
			return false;
		}
	}

	TransactionPtr transPtr(NEW WShopPickUpItemTransaction(__FUNCTION__, __LINE__, player, LogCode::E_ITEM_PICKUP_FROM_WSHOP, wShopDbJob));
	{
		VERIFY_RETURN(bag.Write(transPtr), false);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), false);
	}

	return true;
}

Bool ActionPlayerInventory::EndPickUpItemFromWShop(ItemTran transId, UInt64 addr, FcsAccountId fcsAccountId, UInt64 jobNo, std::vector<WShopDbGameItem>& gameItems, Bool confirm, ItemDatas& updateItemDatas, WShopMoney & wShopMoney)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), false);

	AttributePlayer& attrPlayer = player->GetPlayerAttr();

	EReqDbWShopPickUpItemEndTransaction* dbReq = NEW EReqDbWShopPickUpItemEndTransaction;
	dbReq->transId = transId;
	dbReq->addr = addr;
	dbReq->fcsAccountId = fcsAccountId;
	dbReq->jobNo = jobNo;
	dbReq->accountId = attrPlayer.accountId;
	dbReq->charId = attrPlayer.charId;
	dbReq->confirm = confirm;

	if (confirm == false)
	{
		// 롤백시에는 삭제될 아이템만 넣는다.
		dbReq->gameItems = gameItems;
		dbReq->updateItemDatas = updateItemDatas;
	}

	player->AddGreenZen(wShopMoney.greenZen, true);
	player->SendChangeGreenZen(wShopMoney.greenZen, ChangedMoney::ADD );

	player->AddMoney(wShopMoney.zen, true);
	player->SendChangeMoney(wShopMoney.zen, ChangedMoney::ADD );

	SERVER.SendToDb(player, EventPtr(dbReq));

	return true;
}

ErrorItem::Error ActionPlayerInventory::UpgradeTranscendStone(const ItemPos& sourcePos, const ItemPos &targetPos)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);
		
	VERIFY_RETURN(player->GetContactAction().CheckNpcDistance() && 
				  player->GetContactAction().HasNpcFunction(NpcFunctionType::NPC_FUNCTION_ENHANCE_TRANSCENDENCE_STONE), 
				  ErrorItem::E_NPC_DISTANCE_CHECK);

	const ItemConstPtr sourceItem = GetItem(sourcePos);
	VERIFY_RETURN(sourceItem, ErrorItem::itemNotFound);
	VERIFY_RETURN(sourceItem->GetDivision() == ItemDivisionType::E_STONE, ErrorItem::E_INVALID_DIVISION);
#ifdef __lockable_item_181001
	VALID_DO( sourceItem->IsUnLocakble(), SendSystemMsg( player, SystemMessage( L"sys", L"Msg_Item_locked_not_usable" ) ); return ErrorItem::E_ITEM_LOCKED );
#endif
	const ItemConstPtr targetItem = GetItem(targetPos);
	VERIFY_RETURN(targetItem, ErrorItem::itemNotFound);
#ifdef __lockable_item_181001
	VALID_DO( targetItem->IsUnLocakble(), SendSystemMsg( player, SystemMessage( L"sys", L"Msg_Item_locked_not_usable" ) ); return ErrorItem::E_ITEM_LOCKED );
#endif
	ItemDivisionType tarDivision(targetItem->GetDivision());
	VERIFY_RETURN(tarDivision.IsTstone(), ErrorItem::E_INVALID_DIVISION);	

	VERIFY_RETURN(player->GetKnightageAction().IsMyDominionWisdom(), ErrorItem::E_NOT_DOMINION);

	if (tarDivision.IsTstoneTier1() == TRUE)
	{
		VERIFY_RETURN(sourceItem->GetGrade() == ItemGrade::ANCIENT, ErrorItem::E_INVALID_DIVISION);
	}
	else if (tarDivision.IsTstoneTier2() == TRUE)
	{
		VERIFY_RETURN(sourceItem->GetGrade() == ItemGrade::MYTH, ErrorItem::E_INVALID_DIVISION);
	}
	else if (tarDivision.IsTstoneTier3() == TRUE)
	{
		VERIFY_RETURN(sourceItem->GetGrade() == ItemGrade::SET, ErrorItem::E_INVALID_DIVISION);
	}
	else
	{
		return ErrorItem::E_INVALID_DIVISION;
	}

	Sector* sector = player->GetSector();
	VERIFY_RETURN(sector, ErrorItem::sectorNotFound);

	Int32 dominionValue = 0;

	if (player->GetKnightageId() == sector->GetDominionKnightageId())
		dominionValue = player->GetKnightageAction().GetCapitalGrade();

	const DominionBenefitElem *elem = SCRIPTS.GetDominionBenefitElem(dominionValue);
	VERIFY_RETURN(elem, ErrorItem::elemNotFound);
	VERIFY_RETURN(targetItem->GetEnchantRawLevel() + 1 <= static_cast<EnchantLevel>(elem->max_additional_count), ErrorItem::E_CAN_NOT_UPGRADE);

	const TStoneElem * tStoneElem = SCRIPTS.GetTStoneElem(targetItem->GetItemIndex());
	VERIFY_RETURN(tStoneElem, ErrorItem::elemNotFound);

	AbilityValue outputValue = ItemFactory::SelectTransendStone(targetItem->GetItemIndex());

	ItemUpgradeTranscendStone* transaction = NEW ItemUpgradeTranscendStone(__FUNCTION__, __LINE__, player, LogCode::E_UPGRADE_TRANSEND_STONE, outputValue != 0 ? TRUE : FALSE);
	TransactionPtr transPtr(transaction);
	{
		ErrorItem::Error eError = costAmplicationExpense( tStoneElem->costType, tStoneElem->costValue, transaction );
		VALID_RETURN( eError == ErrorItem::SUCCESS, eError );

		ItemBag bag(*player);
		bag.PushTargetInvenRange(InvenRange(sourcePos));

		ItemData data;
		{
			targetItem->FillUpItemData(data);

			RemoveByPlace(sourceItem->GetPlace(), bag, 1);

			if (data.abilityPackMap.empty())
			{
#ifdef ABILITY_RENEWAL_20180508
				SelectAbilityPack pack;
				data.abilityPackMap.emplace( tStoneElem->optionType, pack );
#else
				AbilityPack pack;
				data.abilityPackMap.emplace(tStoneElem->optionType, pack);
#endif
			}

			auto iter = data.abilityPackMap.begin();

			iter->second.type = tStoneElem->optionType;
			iter->second.percent += outputValue;
			++data.enchant;

			if (outputValue == 0)
			{
				++data.enchantFailCount;
			}

			transaction->SetLogInfo(tStoneElem->costType, tStoneElem->costValue, outputValue, iter->second.percent, sourceItem->GetItemIndex(), 1);
			transaction->SetResultItemData(data);
		}

		VERIFY_RETURN(bag.Update(targetItem, data), ErrorItem::BagUpdateFailed);
		VERIFY_RETURN(bag.Write(transPtr), ErrorItem::BagWriteFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::TranRegisterFailed);
	}

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerInventory::GetAmplifactionStoneList()
{
	EntityPlayer* player = GetOwnerPlayer();
	VALID_RETURN(player && player->IsValid(), ErrorItem::E_FAILED);

	ActionPlayerKnightage *actKnight = GetEntityAction(player);
	VALID_RETURN(actKnight, ErrorItem::E_UNKNOWN);
	VALID_RETURN(actKnight->IsMyDominionWisdom(), ErrorItem::E_NOT_DOMINION);

	EResGetAmplifcationStoneList * res = NEW EResGetAmplifcationStoneList;
	EventPtr packetPtr(res);
	m_amplifactionSystem.Fillup(res->createAmplificationList);
	res->eError = ErrorItem::SUCCESS;
	SERVER.SendToClient(GetOwnerPlayer(), packetPtr);

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerInventory::CreateAmplifactionStone(const CreateAmplificationType createType, const Byte npcSlot, const Int32 productionIndex)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	VERIFY_RETURN(player->GetKnightageAction().IsMyDominionWisdom(), ErrorItem::E_NOT_DOMINION);	
	VERIFY_RETURN(player->GetContactAction().CheckNpcDistance() &&
				  player->GetContactAction().HasNpcFunction(NpcFunctionType::NPC_FUNCTION_PRODUCIBLE_ENHANCE_STONE), 
				  ErrorItem::E_NPC_DISTANCE_CHECK );

	Sector* pSector = player->GetSector();
	VERIFY_RETURN(pSector, ErrorItem::sectorNotFound);

	DominionGrade dominionGrade = 0;
	DominionAdjacentLv adjacentLv = 0;

	if (player->GetKnightageId() == pSector->GetDominionKnightageId())
	{
		dominionGrade = player->GetKnightageAction().GetCapitalGrade();
		adjacentLv = player->GetKnightageAction().GetAdjacentLv();
	}

	if (createType == CreateAmplificationType::BestDominion)
	{
		const DominionBenefitElem *elem = SCRIPTS.GetDominionBenefitElem( dominionGrade );
		VERIFY_RETURN(elem, ErrorItem::elemNotFound);
		VERIFY_RETURN(npcSlot < elem->production_Slot_Count, ErrorItem::E_NOT_OPEN_SLOT);
	}
	else
	{
		VALID_RETURN( player->GetKnightageId() == pSector->GetDominionKnightageId(), ErrorItem::E_NOT_FOLLWER );

		const ConnectionDominionBenefitElem *elem = SCRIPTS.GetConnectionDominionBenfitElem(adjacentLv);
		VERIFY_RETURN(elem, ErrorItem::scriptNotFound);
		VERIFY_RETURN(npcSlot < elem->connection_dominion_count, ErrorItem::E_NOT_OPEN_SLOT);
	}

	ErrorItem::Error eError = m_amplifactionSystem.CheckCreate(createType, npcSlot);
	VERIFY_RETURN(eError == ErrorItem::SUCCESS, eError);

	const EStoneProductionElem *eStoneElem = SCRIPTS.GetEStoneProductionElem(productionIndex);
	VERIFY_RETURN(eStoneElem, ErrorItem::scriptNotFound);

	CreateAmplificationDbInfo info;

	info.productionIndex = productionIndex;
	info.createType = createType;
	info.slot = npcSlot;

	DateTime presentTime = DateTime::GetPresentTime();
	info.completeTime = presentTime + DateTimeSpan(0, 0, eStoneElem->time, 0);
	info.state = CreateAmplificationState::InProgress;

	info.tournamentId = theStatueSystem.GetCurrentTournamentTurnId();

	KnightageId dominionKnightageId = (player->GetFollowerId() == 0) ? player->GetKnightageId() : 0;

	ItemCreateAmplifactionStone* transaction = NEW ItemCreateAmplifactionStone(__FUNCTION__, __LINE__, player, LogCode::E_CREATE_AMPLIFACTION_STONE, info, dominionKnightageId);
	TransactionPtr transPtr(transaction);
	{
		eError = costAmplicationExpense( eStoneElem->costType, eStoneElem->costValue, transaction );
		VALID_RETURN( eError == ErrorItem::SUCCESS, eError );
	
		transaction->SetLogInfo(eStoneElem->costType, eStoneElem->costValue, dominionGrade, adjacentLv);

		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::TranRegisterFailed);
	}

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerInventory::AccelerationAmplifactionStone(const CreateAmplificationType createType, const Byte npcSlot)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), ErrorItem::playerNotFound);

	Sector* pSector = owner->GetSector();
	VERIFY_RETURN(pSector, ErrorItem::sectorNotFound);

	VERIFY_RETURN(owner->GetKnightageAction().IsMyDominionWisdom(), ErrorItem::E_NOT_DOMINION);

	VERIFY_RETURN(owner->GetContactAction().CheckNpcDistance() && 
				  owner->GetContactAction().HasNpcFunction(NpcFunctionType::NPC_FUNCTION_PRODUCIBLE_ENHANCE_STONE), 
				  ErrorItem::E_NPC_DISTANCE_CHECK);

	CreateAmplificationDbInfo info;

	ErrorItem::Error eError = m_amplifactionSystem.GetInfo(createType, npcSlot, info);
	VERIFY_RETURN(eError == ErrorGame::SUCCESS, eError);
	VERIFY_RETURN(info.completeSecond, ErrorItem::E_EMPTY_SLOT);

	const EStoneProductionElem *eStoneElem = SCRIPTS.GetEStoneProductionElem(info.productionIndex);
	VERIFY_RETURN(eStoneElem, ErrorItem::E_INVALID_SCRIPT);
	VERIFY_RETURN(info.accelerationCount < eStoneElem->accelration_limit, ErrorItem::E_INVALID_SCRIPT);

	DateTimeSpan spen(0, 0, eStoneElem->accelration_time, 0);
	DateTime nowTime = DateTime::GetPresentTime();
	DateTime completeTime = info.completeTime;

	if (completeTime - spen <= nowTime)
	{
		info.completeSecond = 0;
		info.completeTime.Reset();
		info.state = CreateAmplificationState::Completed;
	}
	else
	{
		info.state = CreateAmplificationState::InProgress;
		info.completeTime = completeTime - spen;
	}

	++info.accelerationCount;

	KnightageId dominionKnightageId = (owner->GetFollowerId() == 0) ? owner->GetKnightageId() : 0;

	ItemAccelerationAmplifactionStone* transaction = NEW ItemAccelerationAmplifactionStone(__FUNCTION__, __LINE__, owner, LogCode::E_ACCELERAION_AMPLIFACTION_STONE, info, dominionKnightageId);
	TransactionPtr transPtr(transaction);
	{
		eError = costAmplicationExpense( eStoneElem->accelration_cost_type, eStoneElem->accelration_cost_value, transaction );
		VALID_RETURN( eError == ErrorItem::SUCCESS, eError );

		DominionGrade grade = 0;
		DominionAdjacentLv adjacentLv = 0;

		if (owner->GetKnightageId() == pSector->GetDominionKnightageId())
		{
			grade = owner->GetKnightageAction().GetCapitalGrade();
			adjacentLv = owner->GetKnightageAction().GetAdjacentLv();
		}

		transaction->SetLogInfo(eStoneElem->accelration_cost_value, grade, adjacentLv);

		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::TranRegisterFailed);
	}

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerInventory::ReceiveAmplifactionStone(const CreateAmplificationType createType, const Byte npcSlot)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	VERIFY_RETURN(player->GetKnightageAction().IsMyDominionWisdom(), ErrorItem::E_NOT_DOMINION);	
	VERIFY_RETURN(player->GetContactAction().CheckNpcDistance() &&
				  player->GetContactAction().HasNpcFunction(NpcFunctionType::NPC_FUNCTION_PRODUCIBLE_ENHANCE_STONE),
				  ErrorItem::E_NPC_DISTANCE_CHECK);

	CreateAmplificationDbInfo info;

	ErrorItem::Error eError = m_amplifactionSystem.GetInfo(createType, npcSlot, info);
	VERIFY_RETURN(eError == ErrorGame::SUCCESS, eError);

	const EStoneProductionElem *eStoneElem = SCRIPTS.GetEStoneProductionElem(info.productionIndex);
	VERIFY_RETURN(eStoneElem, ErrorItem::scriptNotFound);
	VERIFY_RETURN(info.state == CreateAmplificationState::Completed, ErrorItem::E_EMPTY_SLOT);

	info.Reset();

	info.createType = createType;
	info.slot = npcSlot;

	info.state = CreateAmplificationState::None;

	ItemReceiveAmplifactionStone* transaction = NEW ItemReceiveAmplifactionStone(__FUNCTION__, __LINE__, player, LogCode::E_RECEIVE_AMPLIFACTION_STONE, info);
	TransactionPtr transPtr(transaction);
	{
		ItemBag bag(*player);

		ItemData rewardItem;
		rewardItem.indexItem = eStoneElem->itemIndex;
		rewardItem.SetCurStackCount(1);
				
		Sector* pSector = player->GetSector();
		VERIFY_RETURN(pSector, ErrorItem::sectorNotFound);

		transaction->SetRewardInfo(rewardItem.indexItem, rewardItem.GetCurStackCount(), pSector->GetDominionId(), pSector->GetDominionKnightageId());

		VERIFY_RETURN(bag.Insert(rewardItem), ErrorItem::BagInsertFailed);
		VERIFY_RETURN(bag.Write(transPtr), ErrorItem::BagWriteFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::TranRegisterFailed);
	}
	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerInventory::ClearAmplifactionInfo(const Byte bestCapitalLimit, const Byte adjacentLimit)
{
	const DominionBenefitElem *bestElem = SCRIPTS.GetDominionBenefitElem(bestCapitalLimit);
	VERIFY_RETURN(bestElem, ErrorItem::elemNotFound);

	const ConnectionDominionBenefitElem *connectElem = SCRIPTS.GetConnectionDominionBenfitElem(adjacentLimit);
	VERIFY_RETURN(connectElem, ErrorItem::elemNotFound);

	CreateAmplificationDbInfo info;
	ErrorItem::Error error = ErrorItem::SUCCESS;
	for (UInt32 npcSlot = bestElem->production_Slot_Count; npcSlot < SCRIPTS.GetAmplifactionCapitalLimit(); ++npcSlot)
	{
		error = m_amplifactionSystem.GetInfo(CreateAmplificationType::BestDominion, static_cast<Byte>(npcSlot), info);

		if (error == ErrorItem::SUCCESS)
		{
			m_amplifactionSystem.Erase(CreateAmplificationType::BestDominion, static_cast<Byte>(npcSlot));
		}
	}

	for (Byte npcSlot = (Byte)connectElem->connection_dominion_count; npcSlot < (Byte)SCRIPTS.GetAmplifactionAdjacentLimit(); ++npcSlot)
	{
		error = m_amplifactionSystem.GetInfo(CreateAmplificationType::ConnectionDominion, npcSlot, info);

		if (error == ErrorItem::SUCCESS)
		{
			m_amplifactionSystem.Erase(CreateAmplificationType::ConnectionDominion, npcSlot);
		}
	}

	return ErrorItem::SUCCESS;
}

void ActionPlayerInventory::SetupAmplifactionFromDb(VecCreateAmplificationDbInfo& vec)
{
	m_amplifactionSystem.Setup(vec, GetOwnerPlayer());
}

void ActionPlayerInventory::AmplicationStoneAllClear()
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	ActionPlayer *actPlayer = GetEntityAction(player);
	VALID_RETURN(actPlayer, );

	ActionPlayerKnightage *actKnight = GetEntityAction(player);
	VALID_RETURN(actKnight, );

	m_amplifactionSystem.AllClear();

	EReqDbUpdateAmplifactions *req = NEW EReqDbUpdateAmplifactions;
	EventPtr eventPtr(req);
	m_amplifactionSystem.Fillup(req->updateList);

	VALID_RETURN( false == req->updateList.empty(), );

	req->dominionKnightageId = (player->GetFollowerId() == 0) ? 0 : player->GetKnightageId();

	SERVER.SendToDb(player, eventPtr);
}

ErrorItem::Error ActionPlayerInventory::costAmplicationExpense( const TStoneElem::CostType type, const UInt32 value, __out WShopItemTransaction* ptr )
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN( owner && owner->IsValid(), ErrorItem::playerNotFound );
	VERIFY_RETURN( ptr, ErrorItem::playerNotFound );

	switch (type)
	{
	case TStoneElem::CostType::Zen:
	{
		VERIFY_RETURN( owner->IsEnoughZen( value ), ErrorItem::E_NOT_ENOUGH_ZEN );
		ptr->BindMoney( owner->GetCharId(), value, ChangedMoney::SUB, ChangedMoney::ZEN );
	}
	break;
	case TStoneElem::CostType::GreenZen:
	{
		VERIFY_RETURN( owner->IsEnoughGreenZen( value ), ErrorItem::E_NOT_ENOUGH_GREEN_ZEN );
		ptr->BindMoney( owner->GetCharId(), value, ChangedMoney::SUB, ChangedMoney::GREENZEN );
	}
	break;
	case TStoneElem::CostType::ContributionPoint:
	{
		VERIFY_RETURN( owner->IsEnoughContributionPoint( value ), ErrorItem::E_NOT_ENOUGH_CONTRIBUTION_POINT );
		ptr->BindMoney( owner->GetCharId(), value, ChangedMoney::SUB, ChangedMoney::CONTRIBUTION );
	}
	break;
	case TStoneElem::CostType::RedZen:
	{
		ptr->SetCashInfo( value, ChangedMoney::SUB, ChangedMoney::REDZEN );
	}
	break;
	}

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerInventory::WeaponFusion(const Slots& materialPoss, Bool keepEnchant, UInt16 wiseStoneCount)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), ErrorItem::playerNotFound);
		
	VERIFY_RETURN(owner->GetContactAction().CheckNpcDistance() &&
				  owner->GetContactAction().HasNpcFunction(NpcFunctionType::NPC_FUNCTION_WEAPON_FUSION), 
				  ErrorItem::E_NPC_DISTANCE_CHECK);

	VERIFY_RETURN(owner->GetKnightageAction().IsMyDominionWisdom(), ErrorItem::E_NOT_DOMINION);

	Byte capitalDominion = 0;
	Byte adjacentLv = 0;
	IndexDominion capitalDominionId = 0;
	
	Sector* pSector = owner->GetSector();
	VERIFY_RETURN(pSector, ErrorItem::sectorNotFound);	

	KnightageId dominionKnightageId = pSector->GetDominionKnightageId();

	if (owner->GetKnightageId() != 0 && owner->GetKnightageId() == dominionKnightageId)
	{
		capitalDominion = owner->GetKnightageAction().GetCapitalGrade();
		capitalDominionId = owner->GetKnightageAction().GetCapitalDominionId();
		adjacentLv = owner->GetKnightageAction().GetAdjacentLv();
	}

	const mu2::WeaponFusion& info = SCRIPTS.GetWeaponFusionInfo();

	auto dominioniter = info.dominionInfoMap.find(capitalDominion);
	VERIFY_RETURN(dominioniter != info.dominionInfoMap.end(), ErrorItem::E_INVALID_SCRIPT);
	VERIFY_RETURN(materialPoss.size() == dominioniter->second.fusionValue, ErrorItem::E_CHECK_MATERIAL_COUNT);

	ItemDatas materials;

	Int32 itemLv = 0;
	ItemDivisionType::Enum selectedDivision = ItemDivisionType::NONE;
	ItemGrade::Enum eItemGrade = ItemGrade::NONE;
	ItemData::FusionGroup fusionGroup = ItemData::FusionGroup::NONE;
	Bool diffrentDivision = FALSE;
	Byte maxEnchantCount = 0;

	InvenRange ranges(InvenWithStorages);

	if (keepEnchant == TRUE)
	{
		VERIFY_RETURN(IsEnoughItem(ranges, info.enchantMoveInfo.itemIndex, 1), ErrorItem::E_NOT_ENOUGH_ITEMS);
	}

	if (wiseStoneCount > 0)
	{
		VERIFY_RETURN( wiseStoneCount <= info.wiseStoneInfo.wiseStoneMaxVlaue, ErrorItem::E_NOT_ENOUGH_ITEMS);
		VERIFY_RETURN( IsEnoughItem(ranges, info.wiseStoneInfo.wiseStoneIndex, wiseStoneCount), ErrorItem::E_NOT_ENOUGH_ITEMS);
	}

	
	// 업적 조건 체크용 변수
	Bool isGradeUp = false;
	Bool isGetWeaponOption = false;
	Bool isGetSameDivisionItem = true;
	Bool isAllDifferentClassMaterial = true;

	for (const ItemPos& pos : materialPoss)
	{
		const ItemConstPtr item = GetItem(pos);
		VERIFY_RETURN(item, ErrorItem::E_NOT_MATERIAL_ITEM);

#ifdef ADD_WEAPON_FUSION_LIMIT_LEVEL_GRADE_by_cheolhoon_180809		
		VERIFY_RETURN(item->GetItemLevel() >= info.fusionLimit.level, ErrorItem::E_NOT_MATERIAL_ITEM);
		VERIFY_RETURN(item->GetGrade() >= info.fusionLimit.grade, ErrorItem::E_NOT_MATERIAL_ITEM);
#endif
#ifdef __lockable_item_181001
		VALID_DO( item->IsUnLocakble(), SendSystemMsg( owner, SystemMessage( L"sys", L"Msg_Item_locked_not_usable" ) ); return ErrorItem::E_ITEM_LOCKED );
#endif
		if (itemLv != 0)
		{
			VERIFY_RETURN(itemLv == item->GetItemLevel(), ErrorItem::E_NOT_MATERIAL_ITEM);
		}
		else
		{
			itemLv = item->GetItemLevel();
		}

		if (eItemGrade != ItemGrade::NONE)
		{
			VERIFY_RETURN(eItemGrade == item->GetGrade(), ErrorItem::E_NOT_MATERIAL_ITEM);
		}
		else
		{
			eItemGrade = item->GetGrade();
		}

		if (item->GetEnchantRawLevel() > maxEnchantCount && keepEnchant == TRUE)
		{
			maxEnchantCount = item->GetEnchantRawLevel();
		}

		if (selectedDivision != ItemDivisionType::NONE)
		{
			VERIFY_RETURN(item->GetOptionElem(), ErrorItem::OptionElemNotFound);

			if (fusionGroup == ItemData::FusionGroup::EQUIP)
			{
				VERIFY_RETURN(item->GetFusionGroup() == ItemData::FusionGroup::EQUIP, ErrorItem::E_NOT_MATERIAL_ITEM);
			}
			else if (fusionGroup == ItemData::FusionGroup::ACCESSORY)
			{
				VERIFY_RETURN(item->GetFusionGroup() == fusionGroup, ErrorItem::E_NOT_MATERIAL_ITEM);
			}

			if (selectedDivision != item->GetDivision())
			{
				diffrentDivision = TRUE;
				isGetSameDivisionItem = false;
			}
		}
		else
		{
			VERIFY_RETURN(item->GetOptionElem(), ErrorItem::OptionElemNotFound);

			selectedDivision = item->GetDivision();
			fusionGroup = item->GetFusionGroup();

			VERIFY_RETURN(fusionGroup == ItemData::FusionGroup::EQUIP || fusionGroup == ItemData::FusionGroup::ACCESSORY, ErrorItem::E_NOT_MATERIAL_ITEM);

			if (item->GetEnchantRawLevel() > maxEnchantCount && true == keepEnchant)
			{
				maxEnchantCount = item->GetEnchantRawLevel();
			}
		}

		ItemData materialData;
		item->FillUpItemData(materialData);
		materials.push_back(materialData);
				
		if (owner->CheckRequestClass(item, false))
		{
			isAllDifferentClassMaterial = false;
		}
	}

	ItemBag bag(*owner);

	bag.PushTargetInvenRange(InvenWithStorages);

	ItemWeaponFusionTransaction *trans = NEW ItemWeaponFusionTransaction(__FUNCTION__, __LINE__, owner, LogCode::E_ITEM_EQUIPMENT_FUSION);
	TransactionPtr transPtr(trans);
	{
		ErrorItem::Error eError = ErrorItem::SUCCESS;

		const WeaponFusionCost * costElem = SCRIPTS.GetWeaponFusionCost(static_cast<Byte>(eItemGrade), static_cast<Byte>(itemLv));
		VERIFY_RETURN(costElem, ErrorItem::scriptNotFound);

		Int32 salePercent = 0;
		UInt32 costValue = costElem->costValue;

		if (owner->GetKnightageId() == pSector->GetDominionKnightageId())
		{
			auto adjacentElem = info.adjacentDominionMap.find(owner->GetKnightageAction().GetAdjacentLv());

			if (adjacentElem != info.adjacentDominionMap.end())
				salePercent = adjacentElem->second.cost;

			if (salePercent > 0 && salePercent <= Limits::MAX_DEFAULT_RAND)
				costValue = costValue - ((costValue * salePercent) / Limits::MAX_DEFAULT_RAND);
		}

		switch (costElem->costType)
		{
		case WeaponFusionCost::Type::ZEN:
			{
				if (false == owner->IsEnoughZen(costValue))
				{
					SendSystemMsg(owner, SystemMessage(L"sys", L"Msg_Item_Enough_Money"));
					return ErrorItem::E_NOT_ENOUGH_ZEN;
				}
				transPtr->BindMoney(owner->GetCharId(), costValue, ChangedMoney::SUB, ChangedMoney::ZEN);
			}
			break;
		case WeaponFusionCost::Type::GREENZEN:
			{
				if (false == owner->IsEnoughGreenZen(costValue))
				{
					SendSystemMsg(owner, SystemMessage(L"sys", L"Msg_Item_Err_Enough_Greenzen"));
					return ErrorItem::E_NOT_ENOUGH_GREEN_ZEN;
				}
				transPtr->BindMoney(owner->GetCharId(), costValue, ChangedMoney::SUB, ChangedMoney::GREENZEN);
			}
			break;
		case WeaponFusionCost::Type::CONTRIBUTION_POINT:
			{
				if (false == owner->IsEnoughContributionPoint(costValue))
				{
					return ErrorItem::E_NOT_ENOUGH_CONTRIBUTION_POINT;
				}
				transPtr->BindMoney(owner->GetCharId(), costValue, ChangedMoney::SUB, ChangedMoney::CONTRIBUTION);
			}
			break;
		case WeaponFusionCost::Type::REDZEN:
			{
				// 월드에서 체크함.
				trans->SetCashInfo( costValue, ChangedMoney::SUB, ChangedMoney::REDZEN );
			}
			break;
		}

		trans->SetLogInfo(costElem->costType, costElem->costValue, capitalDominion, adjacentLv, keepEnchant, wiseStoneCount, capitalDominionId, dominionKnightageId);

		if (diffrentDivision)
#ifdef ADD_WEAPON_FUSION_LIMIT_LEVEL_GRADE_by_cheolhoon_180809
		{
			UInt32 maxSize = static_cast<UInt32>(materialPoss.size());
			UInt32 seed = GetRandBetween(0, maxSize - 1);

			const ItemConstPtr item = GetItem(materialPoss[seed]);
			selectedDivision = item->GetDivision();
		}
#else
			selectedDivision = ItemDivisionType::NONE;
#endif

		if (keepEnchant)
		{
			eError = RemoveByIndex(ranges, bag, info.enchantMoveInfo.itemIndex, 1);
			VERIFY_RETURN(eError == ErrorItem::SUCCESS, eError);
		}

		if (wiseStoneCount)
		{
			eError = RemoveByIndex(ranges, bag, info.wiseStoneInfo.wiseStoneIndex, wiseStoneCount);
			VERIFY_RETURN(eError == ErrorItem::SUCCESS, eError);
		}

		ItemData resultData;
		eError = ItemFactory::SelectWeaponFusion(maxEnchantCount, wiseStoneCount, fusionGroup, selectedDivision, eItemGrade, static_cast<Byte>(itemLv), owner->GetClassType(), owner->GetCurrZoneIndex(), resultData, isGradeUp, isGetWeaponOption);
		VERIFY_RETURN(eError == ErrorItem::SUCCESS, eError);
		VERIFY_RETURN(bag.Insert(resultData), ErrorItem::BagInsertFailed);


		std::vector<IndexItem> vecMaterialIndexs;
		for (const ItemData& itemData : materials)
		{
			VERIFY_RETURN(bag.Remove(itemData), ErrorItem::BagRemoveFailed);
			vecMaterialIndexs.push_back(itemData.indexItem);
		}

		trans->SetLogMatrial(vecMaterialIndexs);
		trans->SetAchievementInfo(isGradeUp, isGetWeaponOption, isGetSameDivisionItem, isAllDifferentClassMaterial);

		VERIFY_RETURN(bag.Write(transPtr), ErrorItem::BagWriteFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::TranRegisterFailed);
	}

	return ErrorItem::SUCCESS;
}

void ActionPlayerInventory::SetupDailyShopItemInfo(VecDailyDBShopInfo& vec)
{
	m_dailyShopItemSystem.SetupDailyShopInfo(vec);
}

void ActionPlayerInventory::GetDailyBuyList(IndexNpc npcIndex)
{
	m_dailyShopItemSystem.CheckDailyAndClear();

	EResDailyBuyList *res = NEW EResDailyBuyList;
	EventPtr packetPtr(res);
	{
		res->infos = m_dailyShopItemSystem.GetDailyInfo(npcIndex);
		SERVER.SendToClient(GetOwnerPlayer(), packetPtr);
	}
}

void ActionPlayerInventory::ResetDailyShopInfo()
{
	m_dailyShopItemSystem.Reset();
}

#ifdef __Patch_DailyShop_Add_Npc_Group_by_ch_190212
Bool ActionPlayerInventory::CheckDailyBuyItem(IndexNpc npcIndex, IndexItem itemIndex, Int16 count, DailyShopInfo &ouput)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), false);

	Sector* pSector = player->GetSector();
	VERIFY_RETURN(pSector, false);

	ShopPack::ExclusiveUse bonusMember = ShopPack::ExclusiveUse::COMMON;
	if (player->GetFollowerId() == pSector->GetDominionKnightageId())
	{
		bonusMember = ShopPack::ExclusiveUse::FOLLOWER;
	}
	else if (player->GetKnightageId() == pSector->GetDominionKnightageId())
	{
		bonusMember = ShopPack::ExclusiveUse::KNIGHTAGE;
	}

	const ShopPack* buyPack = SCRIPTS.GetShopPack(npcIndex, itemIndex);
	VERIFY_RETURN(buyPack, false);
	VERIFY_RETURN(buyPack->exclusiveUse == ShopPack::ExclusiveUse::COMMON || buyPack->exclusiveUse == bonusMember, false);
	VALID_RETURN(buyPack->dailyBuyCount, true);

	// NpcGroup이 설정된 경우 NpcGroup 번호로 변경.
	const IndexNpc npcGroupIndex = SCRIPTS.GetScript<SellScript>()->GetNpcGroupIndex(npcIndex, itemIndex);
	if(0 < npcGroupIndex)
	{		
		Int16 nowCount = m_dailyShopItemSystem.GetDailyBuyCount(npcGroupIndex, itemIndex);
		VERIFY_RETURN(nowCount + count <= buyPack->dailyBuyCount, false);

		ouput.itemIndex = itemIndex;
		ouput.npcIndex = npcGroupIndex;
		ouput.nowBuyCount = nowCount + count;
		ouput.resetDate = m_dailyShopItemSystem.GetDailyDate(npcIndex, itemIndex);
	}
	else
	{
		Int16 nowCount = m_dailyShopItemSystem.GetDailyBuyCount(npcIndex, itemIndex);
		VERIFY_RETURN(nowCount + count <= buyPack->dailyBuyCount, false);

		ouput.itemIndex = itemIndex;
		ouput.npcIndex = npcIndex;
		ouput.nowBuyCount = nowCount + count;
		ouput.resetDate = m_dailyShopItemSystem.GetDailyDate(npcIndex, itemIndex);
	}

	return true;
}
#else
Bool ActionPlayerInventory::CheckDailyBuyItem(IndexNpc npcIndex, IndexItem itemIndex, Int16 count, DailyShopInfo &ouput)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), false);

	Sector* pSector = player->GetSector();
	VERIFY_RETURN(pSector, false);

	ShopPack::ExclusiveUse bonusMember = ShopPack::ExclusiveUse::COMMON;
	if (player->GetFollowerId() == pSector->GetDominionKnightageId())
	{
		bonusMember = ShopPack::ExclusiveUse::FOLLOWER;
	}
	else if (player->GetKnightageId() == pSector->GetDominionKnightageId())
	{
		bonusMember = ShopPack::ExclusiveUse::KNIGHTAGE;
	}

	const ShopPack* buyPack = SCRIPTS.GetShopPack(npcIndex, itemIndex);
	VERIFY_RETURN(buyPack, false);
	VERIFY_RETURN(buyPack->exclusiveUse == ShopPack::ExclusiveUse::COMMON || buyPack->exclusiveUse == bonusMember, false);
	VALID_RETURN(buyPack->dailyBuyCount, true);

#ifdef __Patch_Add_Daily_Shop_Limit_Option_NpcIndex_by_Ch_20181205                        
    Int16 nowCount = m_dailyShopItemSystem.GetDailyBuyCount(npcIndex, itemIndex);
#else
    Int16 nowCount = m_dailyShopItemSystem.GetDailyBuyCount(itemIndex);
#endif	
	VERIFY_RETURN(nowCount + count <= buyPack->dailyBuyCount, false);

	ouput.itemIndex = itemIndex;
#ifdef __Patch_Add_Daily_Shop_Limit_Option_NpcIndex_by_Ch_20181205
    ouput.npcIndex = npcIndex;
#else
    ouput.npcIndex = 0;
#endif
	ouput.nowBuyCount = nowCount + count;
	ouput.resetDate = m_dailyShopItemSystem.GetDailyDate(npcIndex, itemIndex);

	return true;
}
#endif // __Patch_DailyShop_Add_Npc_Group_by_ch_190212

void ActionPlayerInventory::UpdateDailyInfos(VecDailyShopInfo& infos)
{
	m_dailyShopItemSystem.ClearDailyBuyCount();

	for (const auto& iter : infos)
		m_dailyShopItemSystem.UpdateDailyInfo(iter);
}

void ActionPlayerInventory::CommitDailyJob(bool isRollback)
{
	m_dailyShopItemSystem.Commit(isRollback);
}

void ActionPlayerInventory::ChangeWeaponState()
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	const ItemConstPtr itemEquippedLeft = GetEquipInven().GetItemBySlot(SlotType::LWEAPON);
	const ItemConstPtr itemEquippedRight = GetEquipInven().GetItemBySlot(SlotType::RWEAPON);

	WeaponType::Type leftType = itemEquippedLeft == nullptr ? WeaponType::NONE : static_cast<WeaponType::Type>(itemEquippedLeft->GetWeaponType());
	WeaponType::Type rightType = itemEquippedRight == nullptr ? WeaponType::NONE : static_cast<WeaponType::Type>(itemEquippedRight->GetWeaponType());

	AttributePlayer& attPlayer = owner->GetPlayerAttr();

	attPlayer.weaponState = WeaponState::GetInstance().GetWeaponState(leftType, rightType);

	MU2_DEBUG_LOG(LogCategory::CONTENTS, L"changeWeaponState left %d, right %d state %d", leftType, rightType, attPlayer.weaponState);
}

UInt32 ActionPlayerInventory::GetDailyBuyCount()
{
	return m_dailyShopItemSystem.GetDailyBuyCount();
}

void ActionPlayerInventory::FixingInCorrectItems(__out ItemDatas& vecFixedItems, __out MapItemData& mapMissedItems)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	VALID_RETURN( m_mapIncorrectItem.size(), );

	ItemDBMoveTransaction * trans = NEW ItemDBMoveTransaction(__FUNCTION__, __LINE__, player, LogCode::E_DB_MOVE_ITEM );
	TransactionPtr transPtr( trans );
	{
		ItemBag bag(*player);

		for (auto iter : m_mapIncorrectItem)
		{
			const InvenRange& range = iter.first;
			ItemDatas& inCorrectItems = iter.second;

			VALID_DO(inCorrectItems.size(), continue);

			Slots freeSlots;
			VERIFY_DO(GetFreeSlots(range, OUT freeSlots, static_cast<Int32>(inCorrectItems.size())), 
				for (ItemData& fixableItem : inCorrectItems)
				{
					VERIFY_DO(mapMissedItems.insert(MapItemData::value_type(fixableItem.GetItemId(), fixableItem)).second, continue);
				}				
				continue);

			std::queue<ItemPos> qFreeslots;
			for (auto& i : freeSlots) qFreeslots.push(i);
			

			std::vector<ItemData> fixedItems;

			for( ItemData& fixableItem : inCorrectItems)			
			{
				if( qFreeslots.empty() )
				{
					VERIFY_DO(mapMissedItems.insert(MapItemData::value_type(fixableItem.GetItemId(), fixableItem)).second, continue);
					continue;
				}

				ItemPos& pos = qFreeslots.front();
				qFreeslots.pop();

				fixableItem.SetPos(pos);
				VERIFY_DO(transPtr->Bind(NEW ItemUpdateInsertProceduere(player), fixableItem, fixableItem),
					VERIFY_DO(mapMissedItems.insert(MapItemData::value_type(fixableItem.GetItemId(), fixableItem)).second, continue); continue);
				fixedItems.push_back(fixableItem);
				MU2_INFO_LOG(LogCategory::SYSTEM, "Fixed InCorrectPostion - player(%s), item(%s)", player->ToString().c_str(), fixableItem.ToString().c_str());
			}
			vecFixedItems.insert(vecFixedItems.end(), fixedItems.begin(), fixedItems.end());
			trans->Add(range, fixedItems);
		}

		Clear4Fixing();
		VERIFY_RETURN(TransactionSystem::Register(transPtr), );
	}
}

void ActionPlayerInventory::GetFixingInCorrectItems(__out std::set<ItemId>& ssettemIds)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );
	
	for (auto itr : m_mapIncorrectItem)
	{	
		ItemDatas& inCorrectItems = itr.second;
	
		for (ItemData& fixableItem : inCorrectItems)
		{
			ssettemIds.insert(fixableItem.GetItemId());
		}
	}	
}

void ActionPlayerInventory::SendMail4FixingMissedItem(const MapItemData& mapMissed)
{
	EntityPlayer* owner = GetOwnerPlayer();
	VERIFY_RETURN(owner && owner->IsValid(), );

	for (const auto& itr : mapMissed)
	{
		const ItemData& missItem = itr.second;
		MU2_INFO_LOG(LogCategory::SYSTEM, "send mail - player(%s), item(%s)", owner->ToString().c_str(), missItem.ToString().c_str());

		MailData mail;
		mail.senderName = L"Mag_Mailbox_ItemTypeChnage_ITEM_SENDER";
		mail.subject = L"Mag_Mailbox_ItemTypeChnage_ITEM_SUBJECT";
		mail.content = L"Mag_Mailbox_ItemTypeChnage_ITEM_CONTENT";
		ErrorMail::Error eErrorMail = owner->GetMailBoxAction().SendSystemMail(mail, missItem, true);
		VERIFY_DO(eErrorMail == ErrorMail::SUCCESS, NoAction);
	}
}

void ActionPlayerInventory::Clear4Fixing()
{
	m_mapIncorrectItem.clear();
	m_ssetIncorrectItem.clear();
}





void ActionPlayerInventory::SendEventShopItemBuyList(IndexNpc npcIndex)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	EResEventShopItemBuyList* res = NEW EResEventShopItemBuyList;
	EventPtr resPtr(res);
	{
		std::vector<SellElem*> sellElems;
		SCRIPTS.GetSellScript(npcIndex, sellElems);

		for (auto sellElem : sellElems)
		{
			EventShopItemInfo info;
			info.sellIndex = sellElem->index;
			info.maxBuyCount = sellElem->shopPack.dailyBuyCount;
#ifdef __Patch_Add_Daily_Shop_Limit_Option_NpcIndex_by_Ch_20181205
            info.curBuyCount = m_dailyShopItemSystem.GetDailyBuyCount(npcIndex, sellElem->listIndex);
#else
            info.curBuyCount = m_dailyShopItemSystem.GetDailyBuyCount(sellElem->listIndex);
#endif
#ifdef ADD_EVENT_SHOP_DAILY_RESETINFO_by_cheolhoon_180725
			info.resetDate = m_dailyShopItemSystem.GetDailyDate(npcIndex, sellElem->listIndex);
#endif
			res->eventItemInfos.push_back(info);
		}

		SERVER.SendToClient(player, resPtr);
	}
}

void ActionPlayerInventory::SendEventShopItemBuyCount(IndexNpc npcIndex)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	ENtfEventShopItemBuyCount* ntf = NEW ENtfEventShopItemBuyCount;
	EventPtr ntfPtr(ntf);
	{
		std::vector<SellElem*> sellElems;
		SCRIPTS.GetSellScript(npcIndex, sellElems);

		for (auto sellElem : sellElems)
		{
			VALID_DO(sellElem->shopPack.dailyBuyCount, continue);

			EventShopItemInfo info;
			info.sellIndex = sellElem->index;
			info.maxBuyCount = sellElem->shopPack.dailyBuyCount;
#ifdef __Patch_Add_Daily_Shop_Limit_Option_NpcIndex_by_Ch_20181205
            info.curBuyCount = m_dailyShopItemSystem.GetDailyBuyCount(npcIndex, sellElem->listIndex);
#else
            info.curBuyCount = m_dailyShopItemSystem.GetDailyBuyCount(sellElem->listIndex);
#endif
#ifdef ADD_EVENT_SHOP_DAILY_RESETINFO_by_cheolhoon_180725
			info.resetDate = m_dailyShopItemSystem.GetDailyDate(npcIndex, sellElem->listIndex);
#endif
			ntf->buyCountInfos.push_back(info);
		}

		SERVER.SendToClient(player, ntfPtr );
	}
	
}


void ActionPlayerInventory::SetupEmotionBoxInfoFromDb(CharDbEmotionBoxes& boxes)
{
	m_emotionBoxes.resetCount = boxes.resetCount;
	m_emotionBoxes.resetDate = boxes.resetDate;
	m_emotionBoxes.emotionBoxes.clear();

	auto xmlScript = SCRIPTS.GetScript<EmotionBoxXmlScript>();

	Bool needRefresh = !UseItemLimitInfo::IsDailyReset(m_emotionBoxes.resetDate, xmlScript->GetResetTime());
	
	if (0 == boxes.boxes[0].boxIndex || needRefresh)
	{
		RefreshEmotionBox(0);
	}
	else
	{
		for (Int32 i = 0; i <= EmotionBoxEnum::CenterBoxPos; ++i)
		{
			EmotionBoxInfo info(boxes.boxes[i]);
			m_emotionBoxes.emotionBoxes.emplace_back(info);
		}
	}
}

void ActionPlayerInventory::SendEmotionBoxInfo()
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	auto xmlScript = SCRIPTS.GetScript<EmotionBoxXmlScript>();
	VERIFY_RETURN(xmlScript, );

	Bool needRefresh = !UseItemLimitInfo::IsDailyReset(m_emotionBoxes.resetDate, xmlScript->GetResetTime());

	if (0 == m_emotionBoxes.emotionBoxes[0].boxIndex || needRefresh)
	{
		RefreshEmotionBox(0);
	}

	EResEmotionBoxInfo * req = NEW EResEmotionBoxInfo;

	req->result = ErrorItem::SUCCESS;
	req->repeatCount = m_emotionBoxes.resetCount;
	req->emotionBoxes = m_emotionBoxes.emotionBoxes;
	
	SERVER.SendToClient(player, EventPtr(req));
}


Bool ActionPlayerInventory::TestItemExpiry(const ItemConstPtr& targetItem)
{
	VERIFY_RETURN(targetItem, false);

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), false);

	VERIFY_RETURN(targetItem->GetPeriodType() != PeriodType::NONE, false);
	VERIFY_RETURN(false == targetItem->IsBlockLimitedExtension(), false);

	ItemData data;
	targetItem->FillUpItemData(data);
	data.period.SetExpirationDate(DateTime::GetPresentTime());

	ItemPeriodExpandTransaction* transaction = NEW ItemPeriodExpandTransaction(__FUNCTION__, __LINE__, player, LogCode::E_ITEM_EXPIRY);
	TransactionPtr transPtr(transaction);
	{
		ItemBag bag(*player);

		VERIFY_RETURN(bag.Update(targetItem, data), false);
		VERIFY_RETURN(bag.Write(transPtr), false);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), false);
	}

	return true;
}

Inven* ActionPlayerInventory::GetInven(const ItemPos& itemPos) const
{
	return GetInven(itemPos.GetInven());
}

InvenEquip& ActionPlayerInventory::GetEquipInven() const
{
	return *GetInven<InvenEquip>(InvenType::EQUIP);
}

ErrorMOPUP::Error ActionPlayerInventory::ReqFreeMopup( EReqFreeMopup* req )
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN( player && player->IsValid(), ErrorMOPUP::playerNotFound);

	ItemBag bag(*player );
	{
		for (const SimpleItem& rewarditem : req->rewardItems)
		{
			ItemData itemData(rewarditem.itemIndex, rewarditem.itemCount, static_cast<UInt32>(time(nullptr)));

			if (false == bag.Insert(itemData))
			{
				MU2_TRACE_LOG(LogCategory::WSHOP, L"ReqFreeMopup bag.Insert Failed  WSHOP(%u), charNo %d: itemindex: %d",
					player->GetFcsAuthInfo().fcsAccountId, player->GetCharId(), itemData.indexItem);
				return ErrorMOPUP::bagInsertFailed;
			}
		}
	}

	FreeMopupTransaction* transaction = NEW FreeMopupTransaction( __FUNCTION__, __LINE__, player, LogCode::E_DUNGEON_MOPUP, req->rewardZen, req->addExp, req->addSoulExp, req->mopupList );
	TransactionPtr transPtr( transaction );
	{
		VERIFY_RETURN(bag.Write(transPtr), ErrorMOPUP::bagWriteFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorMOPUP::TranRegisterFailed);
	}

	return ErrorMOPUP::SUCCESS;
}

void ActionPlayerInventory::ActiveItemRune(const ItemConstPtr& runeItem )
{
	m_itemRune.CheckItemRune( runeItem );
}

#ifdef __lockable_item_181001

void ActionPlayerInventory::SetupLockableItmFromDbAndSendToClient( ItemLockableInfos& vec, Bool isSend )
{
	for (auto &info : vec)
	{
		const ItemPtr item = GetItem( info );
		VALID_DO( item, continue );
		VERIFY_DO( item->GetPlace() == info, continue );

		item->SetLockable( true );
		info.lockable = true;
	}

	if (isSend)
	{
		ENtfLockableItems *ntf = new ENtfLockableItems;
		EventPtr eventPtr( ntf );
		ntf->lockalbleItems = std::move( vec );
		SERVER.SendToClient( GetOwnerPlayer(), eventPtr );
	}
}

ErrorItem::Error ActionPlayerInventory::UseLockableItem( const ItemPlace& selectItem, const Bool state )
{
	const ItemConstPtr item = GetItem(selectItem);
	VALID_RETURN( item, ErrorItem::E_NOT_EXIST_ITEM );
	VALID_RETURN( item->GetPlace() == selectItem, ErrorItem::E_DIFFERENT_ITEM );
	
	if (m_itemLockableCheat == false)
	{
		const SetLockableDivision& divisions = SCRIPTS.GetScript<ItemConfigScript>()->GetLockableDivsion();
		VALID_RETURN( divisions.find( item->GetDivision() ) != divisions.end() || state == false, ErrorItem::E_INVALID_DIVISION );
	}
	VALID_RETURN( state != item->GetLockable(), ErrorItem::E_ERROR );

	Inven * inven = GetOwnerPlayer()->GetInventoryAction().GetInven( item->GetInven() );
	VALID_RETURN( inven, ErrorItem::inventoryNotFound );
	VALID_RETURN( inven->IsInvenBags() || 
					inven->IsPlatinumBag() || 
					inven->IsPremiumBag() || 
					inven->IsPremiumBag() ||
					inven->IsStoragePrivate() ||
					inven->IsEquipBag(), ErrorItem::E_INVALID_INVENTORY );

	ItemBag bag( *GetOwnerPlayer() );

	ItemData tempData = item->Clone();
	tempData.SetLockable( state );
	bag.Update( item, tempData );

	ItemLockableTransation* trans = new ItemLockableTransation( __FUNCTION__, __LINE__, GetOwnerPlayer(), LogCode::E_UPDATE_ITEM_LOCKABLE );
	TransactionPtr transPtr( trans );
	{
		trans->Setup( tempData );
		VERIFY_RETURN( bag.Write( transPtr ), ErrorItem::BagWriteFailed );
		VERIFY_RETURN( TransactionSystem::Register( transPtr ), ErrorItem::TranRegisterFailed );
	}

	return ErrorItem::SUCCESS;
}

#endif

#ifdef __Patch_Legend_Of_Bingo_by_jaekwan_20181126
Int16 ActionPlayerInventory::GetLimitBingoBuyCount(IndexNpc npcIndex, IndexItem itemIndex)
{
	return m_dailyShopItemSystem.GetDailyBuyCount(npcIndex, itemIndex);
}
#endif

#ifdef __Patch_Add_Imputed_Talismanbox_Ticket_Reward_by_ch_20190513
ErrorItem::Error ActionPlayerInventory::ProvideTalismanboxTicketReward(const EntityId& npcId, Bool isImputedRedzen)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	auto actSector = player->GetAction<ActionSector>();
	EntityNpc* npc = actSector->GetNpcInMySector(npcId);
	VERIFY_RETURN(npc && npc->IsValid(), ErrorItem::InvalidNpcIndex);

	ActionPlayerContact *contact = GetEntityAction(player);
	if(false == contact->CheckNpcDistance()
		|| false == contact->HasNpcFunction(isImputedRedzen ? NpcFunctionType::NPC_FUNCTION_IMPUTED_TALISMANBOX_TICKET_REWARD : NpcFunctionType::NPC_FUCNTION_TALISMANBOX_TICKET_REWARD))
	{
		return ErrorItem::InvalidNpcIndex;
	}
	
	const TaismanboxTicketRewardScript* script = SCRIPTS.GetScript<TaismanboxTicketRewardScript>();
	VERIFY_RETURN(script, ErrorItem::NotFoundTalismanboxTicketInfo);
	const TalismanboxTicketRewardElem* elem = script->Get(player->GetWorldId(), player->GetAccountId(), isImputedRedzen);
	if(elem == nullptr)
	{
		return ErrorItem::NotFoundTalismanboxTicketInfo;
	}
	else
	{
		if(false == m_isLoadDBTalismanboxTicketInfo)
		{
			return ErrorItem::E_DB_ERROR;
		}
		
		if(isImputedRedzen)
		{
			if(TalismanboxTicketRewardCondition::RECIEVE_IMPUTED_REDZEN_REWARD & m_talismanboxTicketRewardCondition)
			{
				return ErrorItem::AlreadyProvideTalismanboxTicket;
			}			
		}
		else 
		{
			if(TalismanboxTicketRewardCondition::RECIEVE_REDZEN_REWARD & m_talismanboxTicketRewardCondition)
			{
				return ErrorItem::AlreadyProvideTalismanboxTicket;
			}			
		}
	}

	Int32 freeSlotCount = GetFreeSlotCountByIndex(InvenBags, elem->totalTicketIndex, elem->totalTicketCount);
	if(IsEnoughFreeSlot(InvenBags, freeSlotCount) == false)
	{
		return ErrorItem::FailedProvideTalismanboxTicketByInvenFull;
	}
	else
	{
		ItemBag bag(*player);
		ItemData talismanboxItemData;
		DateTimeEx rewardTime;
		DateTimeEx imputedRewardTime;
		TalismanboxTicketRewardCondition::Enum rollbackRewardFlag;

		talismanboxItemData.indexItem = elem->totalTicketIndex;
		talismanboxItemData.SetCurStackCount(elem->totalTicketCount);
		VERIFY_RETURN(bag.Insert(talismanboxItemData), ErrorItem::BagInsertFailed);
		
		rollbackRewardFlag = m_talismanboxTicketRewardCondition;

		if(isImputedRedzen)
		{
			rewardTime = m_talismanboxTicketRewardTime;
			imputedRewardTime = DateTime::GetPresentTime();
			m_talismanboxTicketRewardCondition = static_cast<TalismanboxTicketRewardCondition::Enum>(m_talismanboxTicketRewardCondition + TalismanboxTicketRewardCondition::RECIEVE_IMPUTED_REDZEN_REWARD);
		}
		else
		{
			imputedRewardTime = m_talismanboxTicketImputedRewardTime;
			rewardTime = DateTime::GetPresentTime();
			m_talismanboxTicketRewardCondition = static_cast<TalismanboxTicketRewardCondition::Enum>(m_talismanboxTicketRewardCondition + TalismanboxTicketRewardCondition::RECIEVE_REDZEN_REWARD);			
		}		

		ItemProvideTalismanboxTicketRewardTransaction* talismanboxTicketTrans = NEW ItemProvideTalismanboxTicketRewardTransaction
		(
			__FUNCTION__, __LINE__
			, player
			, LogCode::E_TALISMANBOX_TICKET_REWARD
			, player->GetAccountId()
			, rewardTime			
			, elem->talismanbox1Count
			, elem->talismanbox2Count
			, elem->totalTicketIndex
			, elem->totalTicketCount
			, isImputedRedzen
			, imputedRewardTime
			, m_talismanboxTicketRewardCondition
			, rollbackRewardFlag
		);

		TransactionPtr transPtr(talismanboxTicketTrans);
		{
			VERIFY_RETURN(bag.Write(transPtr), ErrorItem::BagWriteFailed);
			VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::E_TRANSACION_FAILED);
		}		
	}

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerInventory::CheckTalismanboxTicketRewardInfo(const EntityId& npcId, Bool isImputedRedzen)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	auto actSector = player->GetAction<ActionSector>();
	EntityNpc* npc = actSector->GetNpcInMySector(npcId);
	VERIFY_RETURN(npc && npc->IsValid(), ErrorItem::InvalidNpcIndex);

	ActionPlayerContact *contact = GetEntityAction(player);
	if(false == contact->CheckNpcDistance()
		|| false == contact->HasNpcFunction(isImputedRedzen ? NpcFunctionType::NPC_FUNCTION_IMPUTED_TALISMANBOX_TICKET_REWARD : NpcFunctionType::NPC_FUCNTION_TALISMANBOX_TICKET_REWARD))
	{
		return ErrorItem::InvalidNpcIndex;
	}

	const TaismanboxTicketRewardScript* script = SCRIPTS.GetScript<TaismanboxTicketRewardScript>();
	VERIFY_RETURN(script, ErrorItem::NotFoundTalismanboxTicketInfo);

	const TalismanboxTicketRewardElem* elem = script->Get(player->GetWorldId(), player->GetAccountId(), isImputedRedzen);
	if(elem == nullptr)
	{
		return ErrorItem::NotFoundTalismanboxTicketInfo;
	}
	else
	{
		if(m_isLoadDBTalismanboxTicketInfo == false)
		{
			EReqDbUspGetTalismanboxTicketRewardInfo* req = NEW EReqDbUspGetTalismanboxTicketRewardInfo;
			req->accountId = player->GetAccountId();
			req->imputedRedZen = isImputedRedzen;

			SERVER.SendToDb(player, EventPtr(req));
		}
		else
		{
			if(isImputedRedzen)
			{
				if(TalismanboxTicketRewardCondition::RECIEVE_IMPUTED_REDZEN_REWARD & m_talismanboxTicketRewardCondition)
				{
					return ErrorItem::AlreadyProvideTalismanboxTicket;
				}
			}
			else
			{
				if(TalismanboxTicketRewardCondition::RECIEVE_REDZEN_REWARD & m_talismanboxTicketRewardCondition)
				{
					return ErrorItem::AlreadyProvideTalismanboxTicket;
				}
			}

			EResTalismanboxTicketRewardInfo* res = NEW EResTalismanboxTicketRewardInfo;
			res->eError = ErrorItem::SUCCESS;
			res->talismanBoxIndex1 = elem->talismanbox1Index;
			res->talismanBoxIndex2 = elem->talismanbox2Index;
			res->talismanTicketIndex = elem->totalTicketIndex;
			res->talismanBoxCount1 = elem->talismanbox1Count;
			res->talismanBoxCount2 = elem->talismanbox2Count;
			res->talismanBoxRewardCount1 = elem->talismanbox1TicketCount;
			res->talismanBoxRewardCount2 = elem->talismanbox2TicketCount;
			res->talismanTicketCount = elem->totalTicketCount;

			SERVER.SendToClient(player, EventPtr(res));
		}
	}

	return ErrorItem::SUCCESS;
}

DateTimeEx& ActionPlayerInventory::GetProvideTalismanboxTicketRewardTime(Bool isImputedRedzen)
{
	return isImputedRedzen ? m_talismanboxTicketImputedRewardTime : m_talismanboxTicketRewardTime;
}

void ActionPlayerInventory::SetProvideTalismanboxTicketRewardTime(const DateTimeEx& rewardTime, Bool isImputedRedzen)
{
	if(isImputedRedzen)
	{
		m_talismanboxTicketImputedRewardTime = rewardTime;
	}
	else
	{
		m_talismanboxTicketRewardTime = rewardTime;
	}	
}
#else
#ifdef __Patch_Talismanbox_Ticket_Reward_System_by_ch_181224	
ErrorItem::Error ActionPlayerInventory::ProvideTalismanboxTicketReward(const EntityId& npcId)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	auto actSector = player->GetAction<ActionSector>();
	EntityNpc* npc = actSector->GetNpcInMySector(npcId);
	VERIFY_RETURN(npc && npc->IsValid(), ErrorItem::InvalidNpcIndex);

	ActionPlayerContact *contact = GetEntityAction(player);
	if (contact->CheckNpcDistance() == false ||
		contact->HasNpcFunction(NpcFunctionType::NPC_FUCNTION_TALISMANBOX_TICKET_REWARD) == false)
	{
		return ErrorItem::InvalidNpcIndex;
	}	

	const TaismanboxTicketRewardScript* script = SCRIPTS.GetScript<TaismanboxTicketRewardScript>();
	VERIFY_RETURN(script, ErrorItem::NotFoundTalismanboxTicketInfo);
#ifdef __Patch_Talismanbox_Ticket_Reward_Add_WorldId_by_ch_190405
	const TalismanboxTicketRewardElem* elem = script->Get(player->GetWorldId(), player->GetAccountId());
#else
	const TalismanboxTicketRewardElem* elem = script->Get(player->GetAccountId());
#endif // __Patch_Talismanbox_Ticket_Reward_Add_WorldId_by_ch_190405
	if(elem == nullptr)
	{		
		return ErrorItem::NotFoundTalismanboxTicketInfo;
	}
	else
	{
		switch (m_talismanboxTicketRewardCondition)
		{
			case TalismanboxTicketRewardCondition::NONE:
			case TalismanboxTicketRewardCondition::WAIT_DB:
			{
				return ErrorItem::E_DB_ERROR;
			}
			break;

			case TalismanboxTicketRewardCondition::NOT_RECIEVE_REWARD:
			{
				Int32 freeSlotCount = GetFreeSlotCountByIndex(InvenBags, elem->totalTicketIndex, elem->totalTicketCount);
				if (IsEnoughFreeSlot(InvenBags, freeSlotCount) == false)
				{					
					return ErrorItem::FailedProvideTalismanboxTicketByInvenFull;
				}
				else
				{
					ItemBag bag(*player);
					ItemData talismanboxItemData;
					DateTimeEx rewardTime;

					talismanboxItemData.indexItem = elem->totalTicketIndex;
					talismanboxItemData.SetCurStackCount(elem->totalTicketCount);
					VERIFY_RETURN(bag.Insert(talismanboxItemData), ErrorItem::BagInsertFailed);

					rewardTime = DateTime::GetPresentTime();

					ItemProvideTalismanboxTicketRewardTransaction* talismanboxTicketTrans = NEW ItemProvideTalismanboxTicketRewardTransaction
					(
						__FUNCTION__, __LINE__
						, player
						, LogCode::E_TALISMANBOX_TICKET_REWARD
						, player->GetAccountId()
						, rewardTime
						, elem->talismanbox1Count
						, elem->talismanbox2Count
						, elem->totalTicketIndex
						, elem->totalTicketCount
					);

					TransactionPtr transPtr(talismanboxTicketTrans);
					{
						VERIFY_RETURN(bag.Write(transPtr), ErrorItem::BagWriteFailed);
						VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::E_TRANSACION_FAILED);
					}

					SetTalismanboxTicketRewardCondition(TalismanboxTicketRewardCondition::WAIT_DB);
				}				
			}
			break;

			case TalismanboxTicketRewardCondition::COMPLETE_REWARD:
			{
				return ErrorItem::AlreadyProvideTalismanboxTicket;
			}
			break;
		}
	}

	return ErrorItem::SUCCESS;
}

ErrorItem::Error ActionPlayerInventory::CheckTalismanboxTicketRewardInfo(const EntityId& npcId)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	auto actSector = player->GetAction<ActionSector>();
	EntityNpc* npc = actSector->GetNpcInMySector(npcId);
	VERIFY_RETURN(npc && npc->IsValid(), ErrorItem::InvalidNpcIndex);

	ActionPlayerContact *contact = GetEntityAction(player);
	if (contact->CheckNpcDistance() == false ||
		contact->HasNpcFunction(NpcFunctionType::NPC_FUCNTION_TALISMANBOX_TICKET_REWARD) == false)
	{
		return ErrorItem::InvalidNpcIndex;
	}

	const TaismanboxTicketRewardScript* script = SCRIPTS.GetScript<TaismanboxTicketRewardScript>();
	VERIFY_RETURN(script, ErrorItem::NotFoundTalismanboxTicketInfo);	
#ifdef __Patch_Talismanbox_Ticket_Reward_Add_WorldId_by_ch_190405
	const TalismanboxTicketRewardElem* elem = script->Get(player->GetWorldId(), player->GetAccountId());
#else
	const TalismanboxTicketRewardElem* elem = script->Get(player->GetAccountId());
#endif // __Patch_Talismanbox_Ticket_Reward_Add_WorldId_by_ch_190405
	if (elem == nullptr)
	{
		return ErrorItem::NotFoundTalismanboxTicketInfo;
	}
	else
	{		
		switch (m_talismanboxTicketRewardCondition)
		{
			case TalismanboxTicketRewardCondition::NONE:
			{
				SetTalismanboxTicketRewardCondition(TalismanboxTicketRewardCondition::WAIT_DB);

				EReqDbUspGetTalismanboxTicketRewardInfo* req = NEW EReqDbUspGetTalismanboxTicketRewardInfo;
				req->accountId = player->GetAccountId();				

				SERVER.SendToDb(player, EventPtr(req));
			}
			break;

			case TalismanboxTicketRewardCondition::WAIT_DB:
			{
				// DB 결과 대기. 무시.
			}
			break;

			case TalismanboxTicketRewardCondition::NOT_RECIEVE_REWARD:
			{				
				EResTalismanboxTicketRewardInfo* res = NEW EResTalismanboxTicketRewardInfo;
				res->eError = ErrorItem::SUCCESS;
				res->talismanBoxIndex1 = elem->talismanbox1Index;
				res->talismanBoxIndex2 = elem->talismanbox2Index;
				res->talismanTicketIndex = elem->totalTicketIndex;
				res->talismanBoxCount1 = elem->talismanbox1Count;
				res->talismanBoxCount2 = elem->talismanbox2Count;
				res->talismanBoxRewardCount1 = elem->talismanbox1TicketCount;
				res->talismanBoxRewardCount2 = elem->talismanbox2TicketCount;
				res->talismanTicketCount = elem->totalTicketCount;
				
				SERVER.SendToClient(player, EventPtr(res));
			}
			break;

			case TalismanboxTicketRewardCondition::COMPLETE_REWARD:
			{
				return ErrorItem::AlreadyProvideTalismanboxTicket;
			}
			break;
		}
	}

	return ErrorItem::SUCCESS;
}

DateTimeEx& ActionPlayerInventory::GetProvideTalismanboxTicketRewardTime()
{
	return m_talismanboxTicketRewardTime;
}

void ActionPlayerInventory::SetProvideTalismanboxTicketRewardTime(const DateTimeEx& rewardTime)
{
	m_talismanboxTicketRewardTime = rewardTime;
}

void ActionPlayerInventory::SetTalismanboxTicketRewardCondition(const TalismanboxTicketRewardCondition::Enum talismanboxTicketRewardCondition)
{
	m_talismanboxTicketRewardCondition = talismanboxTicketRewardCondition;
}
#endif
#endif // __Patch_Add_Imputed_Talismanbox_Ticket_Reward_by_ch_20190513

#ifdef __Patch_Legend_Of_Bingo_v2_by_jaekwan_20190312
BingoEventError::Error ActionPlayerInventory::BingoUseResetPointItem(EventPtr& e)
{
	if (false == theEventSystem.IsActiveEventType(GameEventElem::LEGEND_OF_BINGO))
	{
		// 이벤트 중이 아니라면, 셋팅할 필요도 없다.
		return BingoEventError::E_GAME_EVENT_EXPIRY_DATE;
	}

	EReqLegendBingoUseResetPointItem * req = static_cast<EReqLegendBingoUseResetPointItem *>(e.RawPtr());

	EntityPlayer* ownerPlayer = GetOwnerPlayer();
	VERIFY_RETURN(ownerPlayer && ownerPlayer->IsValid(), BingoEventError::E_NOT_FOUND_PLAYER);

	const ItemConstPtr item = GetItem(req->itemPos);
	VERIFY_RETURN(item, BingoEventError::E_NOT_LEGEND_BINGO_ITEM);

	// 레전드 오브 빙고 리셋포인트 증가 아이템만 가능
	VERIFY_RETURN(ItemDivisionType::IsLegendBingoAddResetPointItem(item->GetDivision()), BingoEventError::E_NOT_LEGEND_BINGO_ITEM);

	ErrorItem::Error eError = ownerPlayer->GetUseItemAction().checkAndSet(item);
	VERIFY_RETURN(ErrorItem::SUCCESS == eError, BingoEventError::E_NOT_LEGEND_BINGO_ITEM);

	ItemBingoResetPointTransaction * itemTransaction = NEW ItemBingoResetPointTransaction(__FUNCTION__, __LINE__, ownerPlayer, LogCode::E_USE_ADD_BINGO_RESET_POINT_ITEM, *item, ownerPlayer->GetEventAction().GetBingoPointDbInfo());
	TransactionPtr transPtr(itemTransaction);
	{
		ItemBag bag(*ownerPlayer);

		eError = RemoveByPlace(item->GetPlace(), bag, 1);
		VERIFY_RETURN(eError == ErrorItem::SUCCESS, BingoEventError::E_NOT_LEGEND_BINGO_ITEM);

		VERIFY_RETURN(bag.Write(transPtr), BingoEventError::E_FAILED_ITEM_TRANSACTION);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), BingoEventError::E_TRANSACION_FAILED);
	}

	return BingoEventError::SUCCESS;
}
#endif

#ifdef __Patch_Mercler_Secret_Shop_by_ch_2019_04_24
ErrorItem::Error ActionPlayerInventory::BuyMerclerSecretShop(Index32 productIndex, CostPack& costPack, Bool isForced /* = false */)
{
	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), ErrorItem::playerNotFound);

	auto sellElem = SCRIPTS.GetScript<MerclerSecretShopSellScript>()->Get(productIndex);
	VALID_RETURN(sellElem, ErrorItem::E_INVALID_ID);

	if(productIndex == m_merclerShopInfo.lastBuyIndex)
	{
		return ErrorItem::AlreadyBoughtGoods;
	}

	DateTime curTime = DateTime::GetPresentTime();
	if(false == isForced && (sellElem->startTime > curTime || sellElem->endTime < curTime))
	{
		return ErrorItem::E_PERIOD_OVER;
	}
	
	if(false == sellElem->costPackSet.CheckCorrectPayment(costPack))
	{
		return ErrorItem::E_INVALID_PAYMENT_TYPE;
	}

	const auto& costPacks = costPack.GetCosts();
	for(auto itr : costPacks)
	{
		switch(itr.eType)
		{
		case CoinType::GREENZEN:
		{
			if(false == player->IsEnoughGreenZen(itr.value))
			{
				return ErrorItem::E_NOT_ENOUGH_GREEN_ZEN;
			}
		}
		break;

		case CoinType::ZEN:
		{
			if(false == player->IsEnoughZen(itr.value))
			{
				return ErrorItem::E_NOT_ENOUGH_ZEN;
			}
		}
		break;

		case CoinType::CONTRIBUTION:
		{
			if(false == player->IsEnoughContributionPoint(itr.value))
			{
				return ErrorItem::E_NOT_ENOUGH_CONTRIBUTION_POINT;
			}
		}
		break;
		}
	}

	// 일단 구매했다고 가정.
	// WShop에 레드젠, 귀속 레드젠 체크 및 소모 요청.
	MerclerSecretShopInfo rollbackShopInfo = m_merclerShopInfo;
	m_merclerShopInfo.lastBuyIndex = productIndex;

	// WShop체크 없이 진행.
	ItemBag bag(*player);		
	ItemDatas sendSellItems;
	ItemData sendBonusItem;

	// 재화 아이템.
	//InvenRange ranges(InvenBags << PrivateStorages << AccountStorages << PremiumBags);
	//VALID_RETURN(IsEnoughItem(ranges, sellElem->tokenIndex, sellElem->tokenPrice), ErrorItem::E_NOT_ENOUGH_ITEMS);
	//VALID_RETURN(ErrorItem::SUCCESS == RemoveByIndex(ranges, bag, sellElem->tokenIndex, sellElem->tokenPrice), ErrorItem::BagRemoveFailed);

	// 판매 아이템.
	for(int i = 0; i < Limits::MAX_MERCLER_SECRET_SHOP_SELL_ITEM_SIZE; i++)
	{
		if(0 == sellElem->sellItemIndex[i])
		{
			break;
		}

		Int32 freeSlotCount = GetFreeSlotCountByIndex(InvenBags, sellElem->sellItemIndex[i], sellElem->sellItemCount[i]);
		if(IsEnoughFreeSlot(InvenBags, freeSlotCount) == false)
		{
			// 메일로 전송.
			ItemData item;
			item.indexItem = sellElem->sellItemIndex[i];
			item.SetCurStackCount(sellElem->sellItemCount[i]);

			sendSellItems.push_back(item);
		}
		else
		{
			ItemData itemData;
			itemData.indexItem = sellElem->sellItemIndex[i];
			itemData.SetCurStackCount(sellElem->sellItemCount[i]);
			VERIFY_RETURN(bag.Insert(itemData), ErrorItem::BagInsertFailed);
		}
	}

	// bonus 확인.
	auto bonusElem = SCRIPTS.GetScript<MerclerSecretShopBonusScript>()->Get(sellElem->bonusIndex);
	if(nullptr != bonusElem && bonusElem->bonusMissionNum > m_merclerShopInfo.bonusCount)
	{
		sendBonusItem.indexItem = bonusElem->bonusItemIndex[m_merclerShopInfo.bonusCount];
		sendBonusItem.SetCurStackCount(bonusElem->bonusItemCount[m_merclerShopInfo.bonusCount]);

		m_merclerShopInfo.bonusIndex = bonusElem->index;
		m_merclerShopInfo.bonusCount++;
	}

	m_merclerShopInfo.lastBuyIndex = productIndex;

	// Transaction 생성.
	ItemBuyMerclerSecretShopTransaction* merclerSecretShopTrans = NEW ItemBuyMerclerSecretShopTransaction
	(
		__FUNCTION__, __LINE__
		, player
		, LogCode::E_MERCLER_SECRET_SHOP_BUY
		, sendSellItems
		, sendBonusItem
		, m_merclerShopInfo
		, rollbackShopInfo
		, costPack
	);

	TransactionPtr transPtr(merclerSecretShopTrans);
	{
		for(auto& cost : costPacks)
		{
			if(cost.GetFcsCostType() != fcsa::saleCoinType::kSCT_UNKNOWN)
			{
				merclerSecretShopTrans->SetCashInfo(cost.value, ChangedMoney::CalcType::SUB, cost.eType);
			}
			else
			{
				merclerSecretShopTrans->BindMoney(player->GetCharId(), cost.value, ChangedMoney::CalcType::SUB, cost.eType);
			}
		}

		VERIFY_RETURN(bag.Write(transPtr), ErrorItem::BagWriteFailed);
		VERIFY_RETURN(TransactionSystem::Register(transPtr), ErrorItem::E_TRANSACION_FAILED);
	}

	return ErrorItem::SUCCESS;
}

void ActionPlayerInventory::SetFromDBMerclerSecretShopInfo(MerclerSecretShopInfo& merclerSecretShopInfo)
{
	VERIFY_RETURN(SCRIPTS.IsActivateMerclerSecretShop(), );

	EntityPlayer* player = GetOwnerPlayer();
	VERIFY_RETURN(player && player->IsValid(), );

	if(0 < merclerSecretShopInfo.accountId)
	{
		m_merclerShopInfo = merclerSecretShopInfo;
	}
	else
	{
		m_merclerShopInfo.accountId = player->GetAccountId();
	}

	player->GetInventoryAction().CheckAndUpdateMerclerSecretShopInfo();
}

MerclerSecretShopInfo& ActionPlayerInventory::GetMerclerSecretShopInfo()
{
	return m_merclerShopInfo;
}

void ActionPlayerInventory::CheckAndUpdateMerclerSecretShopInfo(Bool identify /* = false */)
{
	Bool isUpdate = false;

	if(m_merclerShopInfo.identify == false && identify == true)
	{
		m_merclerShopInfo.identify = true;
		isUpdate = true;
	}

	auto sellElem = SCRIPTS.GetScript<MerclerSecretShopSellScript>()->Get();
	if(nullptr != sellElem)
	{
		if(m_merclerShopInfo.updateTime < sellElem->startTime) // 마지막 갱신 시간이 현재 판매 시작 시간 이전인 경우. 새로운 Sell정보 갱신이 이루어져야함.
		{
			m_merclerShopInfo.identify = false;
			m_merclerShopInfo.lastBuyIndex = 0;
			isUpdate = true;
		}
	}

	auto bonusElem = SCRIPTS.GetScript<MerclerSecretShopBonusScript>()->Get();
	if(nullptr != bonusElem)
	{
		if(m_merclerShopInfo.updateTime < bonusElem->startTime) // 마지막 갱신 시간이 현재 판매 시작 시간 이전인 경우. 새로운 Bonus정보 갱신이 이루어져야함.
		{						
			m_merclerShopInfo.bonusIndex = 0;
			m_merclerShopInfo.bonusCount = 0;
			isUpdate = true;
		}
	}
		
	// DB에 정보 업데이트.
	if(isUpdate)
	{
		m_merclerShopInfo.updateTime = DateTime::GetPresentTime(); // 업데이트 시간 갱신은 체크가 다 끝난 후.

		EntityPlayer* player = GetOwnerPlayer();
		VERIFY_RETURN(player && player->IsValid(), );

		EReqDbUspMerclerSecretShopInfoUpdate* req = NEW EReqDbUspMerclerSecretShopInfoUpdate;
		req->accountId = m_merclerShopInfo.accountId;
		req->updateTime = m_merclerShopInfo.updateTime;
		req->identify = m_merclerShopInfo.identify;
		req->productIndex = m_merclerShopInfo.lastBuyIndex;
		req->bonusIndex = m_merclerShopInfo.bonusIndex;
		req->bonusCount = m_merclerShopInfo.bonusCount;

		SERVER.SendToDb(player, EventPtr(req));
	}
}
#endif // __Patch_Mercler_Secret_Shop_by_ch_2019_04_24

} // mu2

