; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	??0exception@std@@QEAA@AEBV01@@Z		; std::exception::exception
PUBLIC	?what@exception@std@@UEBAPEBDXZ			; std::exception::what
PUBLIC	??_Gexception@std@@UEAAPEAXI@Z			; std::exception::`scalar deleting destructor'
PUBLIC	??0bad_alloc@std@@QEAA@AEBV01@@Z		; std::bad_alloc::bad_alloc
PUBLIC	??_Gbad_alloc@std@@UEAAPEAXI@Z			; std::bad_alloc::`scalar deleting destructor'
PUBL<PERSON>	??0bad_array_new_length@std@@QEAA@XZ		; std::bad_array_new_length::bad_array_new_length
PUBLIC	??1bad_array_new_length@std@@UEAA@XZ		; std::bad_array_new_length::~bad_array_new_length
PUBLIC	??0bad_array_new_length@std@@QEAA@AEBV01@@Z	; std::bad_array_new_length::bad_array_new_length
PUBLIC	??_Gbad_array_new_length@std@@UEAAPEAXI@Z	; std::bad_array_new_length::`scalar deleting destructor'
PUBLIC	?_Throw_bad_array_new_length@std@@YAXXZ		; std::_Throw_bad_array_new_length
PUBLIC	?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
PUBLIC	??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::operator delete
PUBLIC	?Notify@EventListener@mu2@@UEAAXAEBV?$SmartPtrEx@UEvent@mu2@@@2@@Z ; mu2::EventListener::Notify
PUBLIC	?OnUnregisteredEvent@EventListener@mu2@@UEAA_NPEAVTcpSocket@2@PEAXH@Z ; mu2::EventListener::OnUnregisteredEvent
PUBLIC	?OnRelayedEvent@EventListener@mu2@@UEAA_NPEAVTcpSocket@2@PEAXH@Z ; mu2::EventListener::OnRelayedEvent
PUBLIC	?OnAppChatEvent@EventListener@mu2@@UEAA_NAEAV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@@Z ; mu2::EventListener::OnAppChatEvent
PUBLIC	?destory@Thread@mu2@@UEAAXXZ			; mu2::Thread::destory
PUBLIC	?allocate@?$allocator@PEAVServerHandler@mu2@@@std@@QEAAPEAPEAVServerHandler@mu2@@_K@Z ; std::allocator<mu2::ServerHandler *>::allocate
PUBLIC	??1?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAA@XZ ; std::vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >::~vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >
PUBLIC	?_Calculate_growth@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEBA_K_K@Z ; std::vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >::_Calculate_growth
PUBLIC	?_Change_array@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAXQEAPEAVServerHandler@mu2@@_K1@Z ; std::vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >::_Change_array
PUBLIC	?_Tidy@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAXXZ ; std::vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >::_Tidy
PUBLIC	?_Xlength@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@CAXXZ ; std::vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >::_Xlength
PUBLIC	??0?$_Vector_val@U?$_Simple_types@PEAVServerHandler@mu2@@@std@@@std@@QEAA@XZ ; std::_Vector_val<std::_Simple_types<mu2::ServerHandler *> >::_Vector_val<std::_Simple_types<mu2::ServerHandler *> >
PUBLIC	??0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z	; mu2::WorldRunner::WorldRunner
PUBLIC	??1WorldRunner@mu2@@UEAA@XZ			; mu2::WorldRunner::~WorldRunner
PUBLIC	?initialize@WorldRunner@mu2@@EEAA_NXZ		; mu2::WorldRunner::initialize
PUBLIC	?update@WorldRunner@mu2@@EEAAXXZ		; mu2::WorldRunner::update
PUBLIC	?dispatch@WorldRunner@mu2@@EEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ; mu2::WorldRunner::dispatch
PUBLIC	?finish@WorldRunner@mu2@@EEAAXXZ		; mu2::WorldRunner::finish
PUBLIC	??_GWorldRunner@mu2@@UEAAPEAXI@Z		; mu2::WorldRunner::`scalar deleting destructor'
PUBLIC	??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
PUBLIC	??$_Resize@U_Value_init_tag@std@@@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z ; std::vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >::_Resize<std::_Value_init_tag>
PUBLIC	??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z ; std::vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >::_Resize_reallocate<std::_Value_init_tag>
PUBLIC	??$_Uninitialized_value_construct_n@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@YAPEAPEAVServerHandler@mu2@@PEAPEAV12@_KAEAV?$allocator@PEAVServerHandler@mu2@@@0@@Z ; std::_Uninitialized_value_construct_n<std::allocator<mu2::ServerHandler *> >
PUBLIC	??1_Reallocation_guard@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAA@XZ ; std::vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >::_Reallocation_guard::~_Reallocation_guard
PUBLIC	??$_Uninitialized_move@PEAPEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@YAPEAPEAVServerHandler@mu2@@QEAPEAV12@0PEAPEAV12@AEAV?$allocator@PEAVServerHandler@mu2@@@0@@Z ; std::_Uninitialized_move<mu2::ServerHandler * *,std::allocator<mu2::ServerHandler *> >
PUBLIC	??$_Zero_range@PEAPEAVServerHandler@mu2@@@std@@YAPEAPEAVServerHandler@mu2@@QEAPEAV12@0@Z ; std::_Zero_range<mu2::ServerHandler * *>
PUBLIC	??1?$_Uninitialized_backout_al@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAA@XZ ; std::_Uninitialized_backout_al<std::allocator<mu2::ServerHandler *> >::~_Uninitialized_backout_al<std::allocator<mu2::ServerHandler *> >
PUBLIC	??$_Emplace_back@$$V@?$_Uninitialized_backout_al@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAAXXZ ; std::_Uninitialized_backout_al<std::allocator<mu2::ServerHandler *> >::_Emplace_back<>
PUBLIC	??$_Copy_memmove@PEAPEAVServerHandler@mu2@@PEAPEAV12@@std@@YAPEAPEAVServerHandler@mu2@@PEAPEAV12@00@Z ; std::_Copy_memmove<mu2::ServerHandler * *,mu2::ServerHandler * *>
PUBLIC	??$_Copy_memmove_tail@PEAPEAVServerHandler@mu2@@@std@@YAPEAPEAVServerHandler@mu2@@QEBDQEAPEAV12@_K2@Z ; std::_Copy_memmove_tail<mu2::ServerHandler * *>
PUBLIC	??_EWorldRunner@mu2@@WEA@EAAPEAXI@Z		; [thunk]:mu2::WorldRunner::`vector deleting destructor'
PUBLIC	??_7exception@std@@6B@				; std::exception::`vftable'
PUBLIC	??_C@_0BC@EOODALEL@Unknown?5exception@		; `string'
PUBLIC	??_7bad_alloc@std@@6B@				; std::bad_alloc::`vftable'
PUBLIC	??_7bad_array_new_length@std@@6B@		; std::bad_array_new_length::`vftable'
PUBLIC	??_C@_0BF@KINCDENJ@bad?5array?5new?5length@	; `string'
PUBLIC	??_R0?AVexception@std@@@8			; std::exception `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
PUBLIC	_TI3?AVbad_array_new_length@std@@
PUBLIC	_CTA3?AVbad_array_new_length@std@@
PUBLIC	??_R0?AVbad_array_new_length@std@@@8		; std::bad_array_new_length `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
PUBLIC	??_R0?AVbad_alloc@std@@@8			; std::bad_alloc `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
PUBLIC	?inst@?$ISingleton@VtheServerUnionHandler@mu2@@@mu2@@1VtheServerUnionHandler@2@A ; mu2::ISingleton<mu2::theServerUnionHandler>::inst
PUBLIC	??_7WorldRunner@mu2@@6BServerHandler@1@@	; mu2::WorldRunner::`vftable'
PUBLIC	??_7WorldRunner@mu2@@6BThread@1@@		; mu2::WorldRunner::`vftable'
PUBLIC	??_C@_0BA@FOIKENOD@vector?5too?5long@		; `string'
PUBLIC	??_R4exception@std@@6B@				; std::exception::`RTTI Complete Object Locator'
PUBLIC	??_R3exception@std@@8				; std::exception::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2exception@std@@8				; std::exception::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@exception@std@@8			; std::exception::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4bad_array_new_length@std@@6B@		; std::bad_array_new_length::`RTTI Complete Object Locator'
PUBLIC	??_R3bad_array_new_length@std@@8		; std::bad_array_new_length::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2bad_array_new_length@std@@8		; std::bad_array_new_length::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@bad_array_new_length@std@@8	; std::bad_array_new_length::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@bad_alloc@std@@8			; std::bad_alloc::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R3bad_alloc@std@@8				; std::bad_alloc::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2bad_alloc@std@@8				; std::bad_alloc::`RTTI Base Class Array'
PUBLIC	??_R4bad_alloc@std@@6B@				; std::bad_alloc::`RTTI Complete Object Locator'
PUBLIC	??_R17?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Base Class Descriptor at (8,-1,0,64)'
PUBLIC	??_R0?AV?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> > `RTTI Type Descriptor'
PUBLIC	??_R3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R0?AVEventListener@mu2@@@8			; mu2::EventListener `RTTI Type Descriptor'
PUBLIC	??_R3EventListener@mu2@@8			; mu2::EventListener::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2EventListener@mu2@@8			; mu2::EventListener::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@EventListener@mu2@@8		; mu2::EventListener::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1CA@?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Base Class Descriptor at (32,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@ServerHandler@mu2@@8		; mu2::ServerHandler::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R0?AVServerHandler@mu2@@@8			; mu2::ServerHandler `RTTI Type Descriptor'
PUBLIC	??_R3ServerHandler@mu2@@8			; mu2::ServerHandler::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2ServerHandler@mu2@@8			; mu2::ServerHandler::`RTTI Base Class Array'
PUBLIC	??_C@_0M@FCOEFOPG@WorldRunner@			; `string'
PUBLIC	??_R4WorldRunner@mu2@@6BServerHandler@1@@	; mu2::WorldRunner::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVWorldRunner@mu2@@@8			; mu2::WorldRunner `RTTI Type Descriptor'
PUBLIC	??_R3WorldRunner@mu2@@8				; mu2::WorldRunner::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2WorldRunner@mu2@@8				; mu2::WorldRunner::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@WorldRunner@mu2@@8		; mu2::WorldRunner::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@ServerTask@mu2@@8			; mu2::ServerTask::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R0?AVServerTask@mu2@@@8			; mu2::ServerTask `RTTI Type Descriptor'
PUBLIC	??_R3ServerTask@mu2@@8				; mu2::ServerTask::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2ServerTask@mu2@@8				; mu2::ServerTask::`RTTI Base Class Array'
PUBLIC	??_R17?0A@EC@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Base Class Descriptor at (8,-1,0,66)'
PUBLIC	??_R1EA@?0A@EA@Thread@mu2@@8			; mu2::Thread::`RTTI Base Class Descriptor at (64,-1,0,64)'
PUBLIC	??_R0?AVThread@mu2@@@8				; mu2::Thread `RTTI Type Descriptor'
PUBLIC	??_R3Thread@mu2@@8				; mu2::Thread::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2Thread@mu2@@8				; mu2::Thread::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@Thread@mu2@@8			; mu2::Thread::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1GA@?0A@EC@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Base Class Descriptor at (96,-1,0,66)'
PUBLIC	??_R4WorldRunner@mu2@@6BThread@1@@		; mu2::WorldRunner::`RTTI Complete Object Locator'
EXTRN	??2@YAPEAX_K@Z:PROC				; operator new
EXTRN	??3@YAXPEAX_K@Z:PROC				; operator delete
EXTRN	?__global_delete@@YAXPEAX_K@Z:PROC		; __global_delete
EXTRN	atexit:PROC
EXTRN	__std_terminate:PROC
EXTRN	_invoke_watson:PROC
EXTRN	memmove:PROC
EXTRN	memset:PROC
EXTRN	free:PROC
EXTRN	malloc:PROC
EXTRN	__std_exception_copy:PROC
EXTRN	__std_exception_destroy:PROC
EXTRN	??_Eexception@std@@UEAAPEAXI@Z:PROC		; std::exception::`vector deleting destructor'
EXTRN	??_Ebad_alloc@std@@UEAAPEAXI@Z:PROC		; std::bad_alloc::`vector deleting destructor'
EXTRN	??_Ebad_array_new_length@std@@UEAAPEAXI@Z:PROC	; std::bad_array_new_length::`vector deleting destructor'
EXTRN	?_Xlength_error@std@@YAXPEBD@Z:PROC		; std::_Xlength_error
EXTRN	?_Internal_push@_Concurrent_queue_base_v4@details@Concurrency@@IEAAXPEBX@Z:PROC ; Concurrency::details::_Concurrent_queue_base_v4::_Internal_push
EXTRN	?Initialize@ServerHandler@mu2@@QEAA_NXZ:PROC	; mu2::ServerHandler::Initialize
EXTRN	?Finish@ServerHandler@mu2@@QEAAXXZ:PROC		; mu2::ServerHandler::Finish
EXTRN	??0ServerTask@mu2@@QEAA@PEAVServer@1@PEBD@Z:PROC ; mu2::ServerTask::ServerTask
EXTRN	??1ServerTask@mu2@@UEAA@XZ:PROC			; mu2::ServerTask::~ServerTask
EXTRN	?run@ServerTask@mu2@@UEAAIXZ:PROC		; mu2::ServerTask::run
EXTRN	?prepareDestroy@ServerTask@mu2@@EEAAXXZ:PROC	; mu2::ServerTask::prepareDestroy
EXTRN	??1theServerUnionHandler@mu2@@UEAA@XZ:PROC	; mu2::theServerUnionHandler::~theServerUnionHandler
EXTRN	?Update@theServerUnionHandler@mu2@@QEAAXXZ:PROC	; mu2::theServerUnionHandler::Update
EXTRN	??0theServerUnionHandler@mu2@@AEAA@XZ:PROC	; mu2::theServerUnionHandler::theServerUnionHandler
EXTRN	??0ChatHandler@mu2@@QEAA@PEAVServer@1@@Z:PROC	; mu2::ChatHandler::ChatHandler
EXTRN	??0PlayerHandler@mu2@@QEAA@PEAVServer@1@@Z:PROC	; mu2::PlayerHandler::PlayerHandler
EXTRN	??0PartyHandler@mu2@@QEAA@PEAVServer@1@@Z:PROC	; mu2::PartyHandler::PartyHandler
EXTRN	??0WorldHandler@mu2@@QEAA@PEAVServer@1@@Z:PROC	; mu2::WorldHandler::WorldHandler
EXTRN	??0JoinWaitHandler@mu2@@QEAA@PEAVServer@1@@Z:PROC ; mu2::JoinWaitHandler::JoinWaitHandler
EXTRN	??0CommunityHandler@mu2@@QEAA@PEAVServer@1@@Z:PROC ; mu2::CommunityHandler::CommunityHandler
EXTRN	??0MultistageDungeonHandler@mu2@@QEAA@PEAVServer@1@@Z:PROC ; mu2::MultistageDungeonHandler::MultistageDungeonHandler
EXTRN	??0BurningStaminaHandler@mu2@@QEAA@PEAVServer@1@@Z:PROC ; mu2::BurningStaminaHandler::BurningStaminaHandler
EXTRN	??0DungeonMatchHandler@mu2@@QEAA@PEAVServer@1@@Z:PROC ; mu2::DungeonMatchHandler::DungeonMatchHandler
EXTRN	??0RankingHandler@mu2@@QEAA@PEAVServer@1@@Z:PROC ; mu2::RankingHandler::RankingHandler
EXTRN	??0DamageMeterHandler@mu2@@QEAA@PEAVServer@1@@Z:PROC ; mu2::DamageMeterHandler::DamageMeterHandler
EXTRN	??0WOPSHandler@mu2@@QEAA@PEAVServer@1@@Z:PROC	; mu2::WOPSHandler::WOPSHandler
EXTRN	??0MissionmapHandler@mu2@@QEAA@PEAVServer@1@@Z:PROC ; mu2::MissionmapHandler::MissionmapHandler
EXTRN	??0DailyLoginHandler@mu2@@QEAA@PEAVServer@1@@Z:PROC ; mu2::DailyLoginHandler::DailyLoginHandler
EXTRN	??0KnightageHandler@mu2@@QEAA@PEAVServer@1@@Z:PROC ; mu2::KnightageHandler::KnightageHandler
EXTRN	??0DailyMissionHandler@mu2@@QEAA@PEAVServer@1@@Z:PROC ; mu2::DailyMissionHandler::DailyMissionHandler
EXTRN	??0LuckyMonsterHandler@mu2@@QEAA@PEAVServer@1@@Z:PROC ; mu2::LuckyMonsterHandler::LuckyMonsterHandler
EXTRN	??0FieldPointHandler@mu2@@QEAA@PEAVServer@1@@Z:PROC ; mu2::FieldPointHandler::FieldPointHandler
EXTRN	??0RaidHandler@mu2@@QEAA@PEAVServer@1@@Z:PROC	; mu2::RaidHandler::RaidHandler
EXTRN	??0PCRoomBillingEventLoopBackHandler@mu2@@QEAA@PEAVServer@1@@Z:PROC ; mu2::PCRoomBillingEventLoopBackHandler::PCRoomBillingEventLoopBackHandler
EXTRN	??0ReLoginGetFcsAuthInfoHandler@mu2@@QEAA@PEAVServer@1@@Z:PROC ; mu2::ReLoginGetFcsAuthInfoHandler::ReLoginGetFcsAuthInfoHandler
EXTRN	??0SeasonHandler@mu2@@QEAA@PEAVServer@1@@Z:PROC	; mu2::SeasonHandler::SeasonHandler
EXTRN	??_EWorldRunner@mu2@@UEAAPEAXI@Z:PROC		; mu2::WorldRunner::`vector deleting destructor'
EXTRN	_CxxThrowException:PROC
EXTRN	__CxxFrameHandler4:PROC
EXTRN	??_7type_info@@6B@:BYTE				; type_info::`vftable'
;	COMDAT ?inst@?$ISingleton@VtheServerUnionHandler@mu2@@@mu2@@1VtheServerUnionHandler@2@A
_BSS	SEGMENT
?inst@?$ISingleton@VtheServerUnionHandler@mu2@@@mu2@@1VtheServerUnionHandler@2@A DB 0b0H DUP (?) ; mu2::ISingleton<mu2::theServerUnionHandler>::inst
_BSS	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0exception@std@@QEAA@AEBV01@@Z DD imagerel $LN4
	DD	imagerel $LN4+89
	DD	imagerel $unwind$??0exception@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?what@exception@std@@UEBAPEBDXZ DD imagerel $LN5
	DD	imagerel $LN5+56
	DD	imagerel $unwind$?what@exception@std@@UEBAPEBDXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gexception@std@@UEAAPEAXI@Z DD imagerel $LN10
	DD	imagerel $LN10+83
	DD	imagerel $unwind$??_Gexception@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z DD imagerel $LN9
	DD	imagerel $LN9+104
	DD	imagerel $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+83
	DD	imagerel $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_array_new_length@std@@QEAA@XZ DD imagerel $LN14
	DD	imagerel $LN14+95
	DD	imagerel $unwind$??0bad_array_new_length@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1bad_array_new_length@std@@UEAA@XZ DD imagerel $LN14
	DD	imagerel $LN14+47
	DD	imagerel $unwind$??1bad_array_new_length@std@@UEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z DD imagerel $LN14
	DD	imagerel $LN14+54
	DD	imagerel $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z DD imagerel $LN20
	DD	imagerel $LN20+83
	DD	imagerel $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Throw_bad_array_new_length@std@@YAXXZ DD imagerel $LN3
	DD	imagerel $LN3+37
	DD	imagerel $unwind$?_Throw_bad_array_new_length@std@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z DD imagerel $LN5
	DD	imagerel $LN5+159
	DD	imagerel $unwind$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z DD imagerel $LN6
	DD	imagerel $LN6+25
	DD	imagerel $unwind$??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Notify@EventListener@mu2@@UEAAXAEBV?$SmartPtrEx@UEvent@mu2@@@2@@Z DD imagerel $LN7
	DD	imagerel $LN7+42
	DD	imagerel $unwind$?Notify@EventListener@mu2@@UEAAXAEBV?$SmartPtrEx@UEvent@mu2@@@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?allocate@?$allocator@PEAVServerHandler@mu2@@@std@@QEAAPEAPEAVServerHandler@mu2@@_K@Z DD imagerel $LN13
	DD	imagerel $LN13+163
	DD	imagerel $unwind$?allocate@?$allocator@PEAVServerHandler@mu2@@@std@@QEAAPEAPEAVServerHandler@mu2@@_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAA@XZ DD imagerel $LN45
	DD	imagerel $LN45+25
	DD	imagerel $unwind$??1?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Calculate_growth@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEBA_K_K@Z DD imagerel $LN42
	DD	imagerel $LN42+332
	DD	imagerel $unwind$?_Calculate_growth@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEBA_K_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Change_array@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAXQEAPEAVServerHandler@mu2@@_K1@Z DD imagerel $LN40
	DD	imagerel $LN40+343
	DD	imagerel $unwind$?_Change_array@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAXQEAPEAVServerHandler@mu2@@_K1@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Tidy@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAXXZ DD imagerel $LN40
	DD	imagerel $LN40+292
	DD	imagerel $unwind$?_Tidy@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Xlength@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@CAXXZ DD imagerel $LN3
	DD	imagerel $LN3+22
	DD	imagerel $unwind$?_Xlength@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@CAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z DD imagerel $LN291
	DD	imagerel $LN291+4262
	DD	imagerel $unwind$??0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD imagerel ?dtor$0@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DD	imagerel ?dtor$0@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA+27
	DD	imagerel $unwind$?dtor$0@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$1@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD imagerel ?dtor$1@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DD	imagerel ?dtor$1@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA+34
	DD	imagerel $unwind$?dtor$1@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$2@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD imagerel ?dtor$2@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DD	imagerel ?dtor$2@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA+24
	DD	imagerel $unwind$?dtor$2@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$3@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD imagerel ?dtor$3@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DD	imagerel ?dtor$3@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA+24
	DD	imagerel $unwind$?dtor$3@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$4@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD imagerel ?dtor$4@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DD	imagerel ?dtor$4@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA+24
	DD	imagerel $unwind$?dtor$4@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$5@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD imagerel ?dtor$5@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DD	imagerel ?dtor$5@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA+24
	DD	imagerel $unwind$?dtor$5@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$6@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD imagerel ?dtor$6@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DD	imagerel ?dtor$6@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA+24
	DD	imagerel $unwind$?dtor$6@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$7@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD imagerel ?dtor$7@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DD	imagerel ?dtor$7@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA+24
	DD	imagerel $unwind$?dtor$7@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$8@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD imagerel ?dtor$8@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DD	imagerel ?dtor$8@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA+27
	DD	imagerel $unwind$?dtor$8@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$9@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD imagerel ?dtor$9@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DD	imagerel ?dtor$9@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA+27
	DD	imagerel $unwind$?dtor$9@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$10@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD imagerel ?dtor$10@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DD	imagerel ?dtor$10@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA+27
	DD	imagerel $unwind$?dtor$10@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$11@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD imagerel ?dtor$11@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DD	imagerel ?dtor$11@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA+27
	DD	imagerel $unwind$?dtor$11@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$12@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD imagerel ?dtor$12@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DD	imagerel ?dtor$12@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA+27
	DD	imagerel $unwind$?dtor$12@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$13@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD imagerel ?dtor$13@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DD	imagerel ?dtor$13@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA+27
	DD	imagerel $unwind$?dtor$13@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$14@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD imagerel ?dtor$14@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DD	imagerel ?dtor$14@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA+27
	DD	imagerel $unwind$?dtor$14@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$15@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD imagerel ?dtor$15@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DD	imagerel ?dtor$15@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA+27
	DD	imagerel $unwind$?dtor$15@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$16@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD imagerel ?dtor$16@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DD	imagerel ?dtor$16@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA+27
	DD	imagerel $unwind$?dtor$16@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$17@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD imagerel ?dtor$17@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DD	imagerel ?dtor$17@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA+27
	DD	imagerel $unwind$?dtor$17@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$18@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD imagerel ?dtor$18@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DD	imagerel ?dtor$18@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA+27
	DD	imagerel $unwind$?dtor$18@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$19@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD imagerel ?dtor$19@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DD	imagerel ?dtor$19@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA+27
	DD	imagerel $unwind$?dtor$19@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$20@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD imagerel ?dtor$20@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DD	imagerel ?dtor$20@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA+27
	DD	imagerel $unwind$?dtor$20@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$21@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD imagerel ?dtor$21@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DD	imagerel ?dtor$21@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA+27
	DD	imagerel $unwind$?dtor$21@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$22@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD imagerel ?dtor$22@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DD	imagerel ?dtor$22@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA+27
	DD	imagerel $unwind$?dtor$22@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$23@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD imagerel ?dtor$23@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DD	imagerel ?dtor$23@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA+27
	DD	imagerel $unwind$?dtor$23@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1WorldRunner@mu2@@UEAA@XZ DD imagerel $LN76
	DD	imagerel $LN76+389
	DD	imagerel $unwind$??1WorldRunner@mu2@@UEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?initialize@WorldRunner@mu2@@EEAA_NXZ DD imagerel $LN11
	DD	imagerel $LN11+107
	DD	imagerel $unwind$?initialize@WorldRunner@mu2@@EEAA_NXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?update@WorldRunner@mu2@@EEAAXXZ DD imagerel $LN5
	DD	imagerel $LN5+40
	DD	imagerel $unwind$?update@WorldRunner@mu2@@EEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?finish@WorldRunner@mu2@@EEAAXXZ DD imagerel $LN49
	DD	imagerel $LN49+426
	DD	imagerel $unwind$?finish@WorldRunner@mu2@@EEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GWorldRunner@mu2@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+84
	DD	imagerel $unwind$??_GWorldRunner@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z DD imagerel $LN7
	DD	imagerel $LN7+150
	DD	imagerel $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Resize@U_Value_init_tag@std@@@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z DD imagerel $LN187
	DD	imagerel $LN187+352
	DD	imagerel $unwind$??$_Resize@U_Value_init_tag@std@@@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z DD imagerel $LN273
	DD	imagerel $LN273+646
	DD	imagerel $unwind$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA DD imagerel ?dtor$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA
	DD	imagerel ?dtor$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA+27
	DD	imagerel $unwind$?dtor$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Uninitialized_value_construct_n@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@YAPEAPEAVServerHandler@mu2@@PEAPEAV12@_KAEAV?$allocator@PEAVServerHandler@mu2@@@0@@Z DD imagerel $LN40
	DD	imagerel $LN40+251
	DD	imagerel $unwind$??$_Uninitialized_value_construct_n@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@YAPEAPEAVServerHandler@mu2@@PEAPEAV12@_KAEAV?$allocator@PEAVServerHandler@mu2@@@0@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???$_Uninitialized_value_construct_n@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@YAPEAPEAVServerHandler@mu2@@PEAPEAV12@_KAEAV?$allocator@PEAVServerHandler@mu2@@@0@@Z@4HA DD imagerel ?dtor$0@?0???$_Uninitialized_value_construct_n@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@YAPEAPEAVServerHandler@mu2@@PEAPEAV12@_KAEAV?$allocator@PEAVServerHandler@mu2@@@0@@Z@4HA
	DD	imagerel ?dtor$0@?0???$_Uninitialized_value_construct_n@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@YAPEAPEAVServerHandler@mu2@@PEAPEAV12@_KAEAV?$allocator@PEAVServerHandler@mu2@@@0@@Z@4HA+24
	DD	imagerel $unwind$?dtor$0@?0???$_Uninitialized_value_construct_n@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@YAPEAPEAVServerHandler@mu2@@PEAPEAV12@_KAEAV?$allocator@PEAVServerHandler@mu2@@@0@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__E?inst@?$ISingleton@VtheServerUnionHandler@mu2@@@mu2@@1VtheServerUnionHandler@2@A@@YAXXZ DD imagerel ??__E?inst@?$ISingleton@VtheServerUnionHandler@mu2@@@mu2@@1VtheServerUnionHandler@2@A@@YAXXZ
	DD	imagerel ??__E?inst@?$ISingleton@VtheServerUnionHandler@mu2@@@mu2@@1VtheServerUnionHandler@2@A@@YAXXZ+34
	DD	imagerel $unwind$??__E?inst@?$ISingleton@VtheServerUnionHandler@mu2@@@mu2@@1VtheServerUnionHandler@2@A@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__F?inst@?$ISingleton@VtheServerUnionHandler@mu2@@@mu2@@1VtheServerUnionHandler@2@A@@YAXXZ DD imagerel ??__F?inst@?$ISingleton@VtheServerUnionHandler@mu2@@@mu2@@1VtheServerUnionHandler@2@A@@YAXXZ
	DD	imagerel ??__F?inst@?$ISingleton@VtheServerUnionHandler@mu2@@@mu2@@1VtheServerUnionHandler@2@A@@YAXXZ+22
	DD	imagerel $unwind$??__F?inst@?$ISingleton@VtheServerUnionHandler@mu2@@@mu2@@1VtheServerUnionHandler@2@A@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1_Reallocation_guard@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAA@XZ DD imagerel $LN25
	DD	imagerel $LN25+179
	DD	imagerel $unwind$??1_Reallocation_guard@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Uninitialized_move@PEAPEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@YAPEAPEAVServerHandler@mu2@@QEAPEAV12@0PEAPEAV12@AEAV?$allocator@PEAVServerHandler@mu2@@@0@@Z DD imagerel $LN65
	DD	imagerel $LN65+447
	DD	imagerel $unwind$??$_Uninitialized_move@PEAPEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@YAPEAPEAVServerHandler@mu2@@QEAPEAV12@0PEAPEAV12@AEAV?$allocator@PEAVServerHandler@mu2@@@0@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Zero_range@PEAPEAVServerHandler@mu2@@@std@@YAPEAPEAVServerHandler@mu2@@QEAPEAV12@0@Z DD imagerel $LN13
	DD	imagerel $LN13+95
	DD	imagerel $unwind$??$_Zero_range@PEAPEAVServerHandler@mu2@@@std@@YAPEAPEAVServerHandler@mu2@@QEAPEAV12@0@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$_Uninitialized_backout_al@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAA@XZ DD imagerel $LN9
	DD	imagerel $LN9+54
	DD	imagerel $unwind$??1?$_Uninitialized_backout_al@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Emplace_back@$$V@?$_Uninitialized_backout_al@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAAXXZ DD imagerel $LN15
	DD	imagerel $LN15+117
	DD	imagerel $unwind$??$_Emplace_back@$$V@?$_Uninitialized_backout_al@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Copy_memmove@PEAPEAVServerHandler@mu2@@PEAPEAV12@@std@@YAPEAPEAVServerHandler@mu2@@PEAPEAV12@00@Z DD imagerel $LN18
	DD	imagerel $LN18+177
	DD	imagerel $unwind$??$_Copy_memmove@PEAPEAVServerHandler@mu2@@PEAPEAV12@@std@@YAPEAPEAVServerHandler@mu2@@PEAPEAV12@00@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Copy_memmove_tail@PEAPEAVServerHandler@mu2@@@std@@YAPEAPEAVServerHandler@mu2@@QEBDQEAPEAV12@_K2@Z DD imagerel $LN8
	DD	imagerel $LN8+96
	DD	imagerel $unwind$??$_Copy_memmove_tail@PEAPEAVServerHandler@mu2@@@std@@YAPEAPEAVServerHandler@mu2@@QEBDQEAPEAV12@_K2@Z
pdata	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
??inst$initializer$@?$ISingleton@VtheServerUnionHandler@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA DQ FLAT:??__E?inst@?$ISingleton@VtheServerUnionHandler@mu2@@@mu2@@1VtheServerUnionHandler@2@A@@YAXXZ ; ??inst$initializer$@?$ISingleton@VtheServerUnionHandler@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA
CRT$XCU	ENDS
;	COMDAT ??_R4WorldRunner@mu2@@6BThread@1@@
rdata$r	SEGMENT
??_R4WorldRunner@mu2@@6BThread@1@@ DD 01H		; mu2::WorldRunner::`RTTI Complete Object Locator'
	DD	040H
	DD	00H
	DD	imagerel ??_R0?AVWorldRunner@mu2@@@8
	DD	imagerel ??_R3WorldRunner@mu2@@8
	DD	imagerel ??_R4WorldRunner@mu2@@6BThread@1@@
rdata$r	ENDS
;	COMDAT ??_R1GA@?0A@EC@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
rdata$r	SEGMENT
??_R1GA@?0A@EC@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 DD imagerel ??_R0?AV?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Base Class Descriptor at (96,-1,0,66)'
	DD	00H
	DD	060H
	DD	0ffffffffH
	DD	00H
	DD	042H
	DD	imagerel ??_R3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@Thread@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@Thread@mu2@@8 DD imagerel ??_R0?AVThread@mu2@@@8 ; mu2::Thread::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3Thread@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2Thread@mu2@@8
rdata$r	SEGMENT
??_R2Thread@mu2@@8 DD imagerel ??_R1A@?0A@EA@Thread@mu2@@8 ; mu2::Thread::`RTTI Base Class Array'
	DD	imagerel ??_R1CA@?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3Thread@mu2@@8
rdata$r	SEGMENT
??_R3Thread@mu2@@8 DD 00H				; mu2::Thread::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2Thread@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVThread@mu2@@@8
data$rs	SEGMENT
??_R0?AVThread@mu2@@@8 DQ FLAT:??_7type_info@@6B@	; mu2::Thread `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVThread@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R1EA@?0A@EA@Thread@mu2@@8
rdata$r	SEGMENT
??_R1EA@?0A@EA@Thread@mu2@@8 DD imagerel ??_R0?AVThread@mu2@@@8 ; mu2::Thread::`RTTI Base Class Descriptor at (64,-1,0,64)'
	DD	01H
	DD	040H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3Thread@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R17?0A@EC@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
rdata$r	SEGMENT
??_R17?0A@EC@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 DD imagerel ??_R0?AV?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Base Class Descriptor at (8,-1,0,66)'
	DD	00H
	DD	08H
	DD	0ffffffffH
	DD	00H
	DD	042H
	DD	imagerel ??_R3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
rdata$r	ENDS
;	COMDAT ??_R2ServerTask@mu2@@8
rdata$r	SEGMENT
??_R2ServerTask@mu2@@8 DD imagerel ??_R1A@?0A@EA@ServerTask@mu2@@8 ; mu2::ServerTask::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@ServerHandler@mu2@@8
	DD	imagerel ??_R1A@?0A@EA@EventListener@mu2@@8
	DD	imagerel ??_R17?0A@EC@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
	DD	imagerel ??_R1EA@?0A@EA@Thread@mu2@@8
	DD	imagerel ??_R1GA@?0A@EC@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3ServerTask@mu2@@8
rdata$r	SEGMENT
??_R3ServerTask@mu2@@8 DD 00H				; mu2::ServerTask::`RTTI Class Hierarchy Descriptor'
	DD	05H
	DD	06H
	DD	imagerel ??_R2ServerTask@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVServerTask@mu2@@@8
data$rs	SEGMENT
??_R0?AVServerTask@mu2@@@8 DQ FLAT:??_7type_info@@6B@	; mu2::ServerTask `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVServerTask@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R1A@?0A@EA@ServerTask@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@ServerTask@mu2@@8 DD imagerel ??_R0?AVServerTask@mu2@@@8 ; mu2::ServerTask::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	05H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3ServerTask@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@WorldRunner@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@WorldRunner@mu2@@8 DD imagerel ??_R0?AVWorldRunner@mu2@@@8 ; mu2::WorldRunner::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	06H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3WorldRunner@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2WorldRunner@mu2@@8
rdata$r	SEGMENT
??_R2WorldRunner@mu2@@8 DD imagerel ??_R1A@?0A@EA@WorldRunner@mu2@@8 ; mu2::WorldRunner::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@ServerTask@mu2@@8
	DD	imagerel ??_R1A@?0A@EA@ServerHandler@mu2@@8
	DD	imagerel ??_R1A@?0A@EA@EventListener@mu2@@8
	DD	imagerel ??_R17?0A@EC@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
	DD	imagerel ??_R1EA@?0A@EA@Thread@mu2@@8
	DD	imagerel ??_R1GA@?0A@EC@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3WorldRunner@mu2@@8
rdata$r	SEGMENT
??_R3WorldRunner@mu2@@8 DD 00H				; mu2::WorldRunner::`RTTI Class Hierarchy Descriptor'
	DD	01H
	DD	07H
	DD	imagerel ??_R2WorldRunner@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVWorldRunner@mu2@@@8
data$rs	SEGMENT
??_R0?AVWorldRunner@mu2@@@8 DQ FLAT:??_7type_info@@6B@	; mu2::WorldRunner `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVWorldRunner@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4WorldRunner@mu2@@6BServerHandler@1@@
rdata$r	SEGMENT
??_R4WorldRunner@mu2@@6BServerHandler@1@@ DD 01H	; mu2::WorldRunner::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVWorldRunner@mu2@@@8
	DD	imagerel ??_R3WorldRunner@mu2@@8
	DD	imagerel ??_R4WorldRunner@mu2@@6BServerHandler@1@@
rdata$r	ENDS
;	COMDAT ??_C@_0M@FCOEFOPG@WorldRunner@
CONST	SEGMENT
??_C@_0M@FCOEFOPG@WorldRunner@ DB 'WorldRunner', 00H	; `string'
CONST	ENDS
;	COMDAT ??_R2ServerHandler@mu2@@8
rdata$r	SEGMENT
??_R2ServerHandler@mu2@@8 DD imagerel ??_R1A@?0A@EA@ServerHandler@mu2@@8 ; mu2::ServerHandler::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@EventListener@mu2@@8
	DD	imagerel ??_R17?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3ServerHandler@mu2@@8
rdata$r	SEGMENT
??_R3ServerHandler@mu2@@8 DD 00H			; mu2::ServerHandler::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	03H
	DD	imagerel ??_R2ServerHandler@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVServerHandler@mu2@@@8
data$rs	SEGMENT
??_R0?AVServerHandler@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::ServerHandler `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVServerHandler@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R1A@?0A@EA@ServerHandler@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@ServerHandler@mu2@@8 DD imagerel ??_R0?AVServerHandler@mu2@@@8 ; mu2::ServerHandler::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	02H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3ServerHandler@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R1CA@?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
rdata$r	SEGMENT
??_R1CA@?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 DD imagerel ??_R0?AV?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Base Class Descriptor at (32,-1,0,64)'
	DD	00H
	DD	020H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@EventListener@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@EventListener@mu2@@8 DD imagerel ??_R0?AVEventListener@mu2@@@8 ; mu2::EventListener::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3EventListener@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2EventListener@mu2@@8
rdata$r	SEGMENT
??_R2EventListener@mu2@@8 DD imagerel ??_R1A@?0A@EA@EventListener@mu2@@8 ; mu2::EventListener::`RTTI Base Class Array'
	DD	imagerel ??_R17?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3EventListener@mu2@@8
rdata$r	SEGMENT
??_R3EventListener@mu2@@8 DD 00H			; mu2::EventListener::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2EventListener@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVEventListener@mu2@@@8
data$rs	SEGMENT
??_R0?AVEventListener@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::EventListener `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVEventListener@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R1A@?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 DD imagerel ??_R0?AV?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
rdata$r	ENDS
;	COMDAT ??_R2?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
rdata$r	SEGMENT
??_R2?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 DD imagerel ??_R1A@?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
rdata$r	SEGMENT
??_R3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 DD 00H ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
rdata$r	ENDS
;	COMDAT ??_R0?AV?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@@8
data$rs	SEGMENT
??_R0?AV?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@@8 DQ FLAT:??_7type_info@@6B@ ; AllocatedObject<mu2::CategorisedAllocPolicy<0> > `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AV?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2'
	DB	'@@@@', 00H
data$rs	ENDS
;	COMDAT ??_R17?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
rdata$r	SEGMENT
??_R17?0A@EA@?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8 DD imagerel ??_R0?AV?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@@8 ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::`RTTI Base Class Descriptor at (8,-1,0,64)'
	DD	00H
	DD	08H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@8
rdata$r	ENDS
;	COMDAT ??_R4bad_alloc@std@@6B@
rdata$r	SEGMENT
??_R4bad_alloc@std@@6B@ DD 01H				; std::bad_alloc::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVbad_alloc@std@@@8
	DD	imagerel ??_R3bad_alloc@std@@8
	DD	imagerel ??_R4bad_alloc@std@@6B@
rdata$r	ENDS
;	COMDAT ??_R2bad_alloc@std@@8
rdata$r	SEGMENT
??_R2bad_alloc@std@@8 DD imagerel ??_R1A@?0A@EA@bad_alloc@std@@8 ; std::bad_alloc::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@exception@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3bad_alloc@std@@8
rdata$r	SEGMENT
??_R3bad_alloc@std@@8 DD 00H				; std::bad_alloc::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2bad_alloc@std@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@bad_alloc@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@bad_alloc@std@@8 DD imagerel ??_R0?AVbad_alloc@std@@@8 ; std::bad_alloc::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3bad_alloc@std@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@bad_array_new_length@std@@8 DD imagerel ??_R0?AVbad_array_new_length@std@@@8 ; std::bad_array_new_length::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	02H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3bad_array_new_length@std@@8
rdata$r	ENDS
;	COMDAT ??_R2bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R2bad_array_new_length@std@@8 DD imagerel ??_R1A@?0A@EA@bad_array_new_length@std@@8 ; std::bad_array_new_length::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@bad_alloc@std@@8
	DD	imagerel ??_R1A@?0A@EA@exception@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R3bad_array_new_length@std@@8 DD 00H			; std::bad_array_new_length::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	03H
	DD	imagerel ??_R2bad_array_new_length@std@@8
rdata$r	ENDS
;	COMDAT ??_R4bad_array_new_length@std@@6B@
rdata$r	SEGMENT
??_R4bad_array_new_length@std@@6B@ DD 01H		; std::bad_array_new_length::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVbad_array_new_length@std@@@8
	DD	imagerel ??_R3bad_array_new_length@std@@8
	DD	imagerel ??_R4bad_array_new_length@std@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@exception@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@exception@std@@8 DD imagerel ??_R0?AVexception@std@@@8 ; std::exception::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3exception@std@@8
rdata$r	ENDS
;	COMDAT ??_R2exception@std@@8
rdata$r	SEGMENT
??_R2exception@std@@8 DD imagerel ??_R1A@?0A@EA@exception@std@@8 ; std::exception::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3exception@std@@8
rdata$r	SEGMENT
??_R3exception@std@@8 DD 00H				; std::exception::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2exception@std@@8
rdata$r	ENDS
;	COMDAT ??_R4exception@std@@6B@
rdata$r	SEGMENT
??_R4exception@std@@6B@ DD 01H				; std::exception::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVexception@std@@@8
	DD	imagerel ??_R3exception@std@@8
	DD	imagerel ??_R4exception@std@@6B@
rdata$r	ENDS
;	COMDAT ??_C@_0BA@FOIKENOD@vector?5too?5long@
CONST	SEGMENT
??_C@_0BA@FOIKENOD@vector?5too?5long@ DB 'vector too long', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7WorldRunner@mu2@@6BThread@1@@
CONST	SEGMENT
??_7WorldRunner@mu2@@6BThread@1@@ DQ FLAT:??_R4WorldRunner@mu2@@6BThread@1@@ ; mu2::WorldRunner::`vftable'
	DQ	FLAT:??_EWorldRunner@mu2@@WEA@EAAPEAXI@Z
	DQ	FLAT:?run@ServerTask@mu2@@UEAAIXZ
	DQ	FLAT:?destory@Thread@mu2@@UEAAXXZ
	DQ	FLAT:?prepareDestroy@ServerTask@mu2@@EEAAXXZ
CONST	ENDS
;	COMDAT ??_7WorldRunner@mu2@@6BServerHandler@1@@
CONST	SEGMENT
??_7WorldRunner@mu2@@6BServerHandler@1@@ DQ FLAT:??_R4WorldRunner@mu2@@6BServerHandler@1@@ ; mu2::WorldRunner::`vftable'
	DQ	FLAT:??_EWorldRunner@mu2@@UEAAPEAXI@Z
	DQ	FLAT:?Notify@EventListener@mu2@@UEAAXAEBV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?OnUnregisteredEvent@EventListener@mu2@@UEAA_NPEAVTcpSocket@2@PEAXH@Z
	DQ	FLAT:?OnRelayedEvent@EventListener@mu2@@UEAA_NPEAVTcpSocket@2@PEAXH@Z
	DQ	FLAT:?OnAppChatEvent@EventListener@mu2@@UEAA_NAEAV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@@Z
	DQ	FLAT:?initialize@WorldRunner@mu2@@EEAA_NXZ
	DQ	FLAT:?update@WorldRunner@mu2@@EEAAXXZ
	DQ	FLAT:?dispatch@WorldRunner@mu2@@EEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
	DQ	FLAT:?finish@WorldRunner@mu2@@EEAAXXZ
CONST	ENDS
;	COMDAT _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 DD 010H
	DD	imagerel ??_R0?AVbad_alloc@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0bad_alloc@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVbad_alloc@std@@@8
data$r	SEGMENT
??_R0?AVbad_alloc@std@@@8 DQ FLAT:??_7type_info@@6B@	; std::bad_alloc `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVbad_alloc@std@@', 00H
data$r	ENDS
;	COMDAT _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 DD 00H
	DD	imagerel ??_R0?AVbad_array_new_length@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0bad_array_new_length@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVbad_array_new_length@std@@@8
data$r	SEGMENT
??_R0?AVbad_array_new_length@std@@@8 DQ FLAT:??_7type_info@@6B@ ; std::bad_array_new_length `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVbad_array_new_length@std@@', 00H
data$r	ENDS
;	COMDAT _CTA3?AVbad_array_new_length@std@@
xdata$x	SEGMENT
_CTA3?AVbad_array_new_length@std@@ DD 03H
	DD	imagerel _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
	DD	imagerel _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
	DD	imagerel _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
xdata$x	ENDS
;	COMDAT _TI3?AVbad_array_new_length@std@@
xdata$x	SEGMENT
_TI3?AVbad_array_new_length@std@@ DD 00H
	DD	imagerel ??1bad_array_new_length@std@@UEAA@XZ
	DD	00H
	DD	imagerel _CTA3?AVbad_array_new_length@std@@
xdata$x	ENDS
;	COMDAT _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 DD 00H
	DD	imagerel ??_R0?AVexception@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0exception@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVexception@std@@@8
data$r	SEGMENT
??_R0?AVexception@std@@@8 DQ FLAT:??_7type_info@@6B@	; std::exception `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVexception@std@@', 00H
data$r	ENDS
;	COMDAT ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@
CONST	SEGMENT
??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ DB 'bad array new length', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7bad_array_new_length@std@@6B@
CONST	SEGMENT
??_7bad_array_new_length@std@@6B@ DQ FLAT:??_R4bad_array_new_length@std@@6B@ ; std::bad_array_new_length::`vftable'
	DQ	FLAT:??_Ebad_array_new_length@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT ??_7bad_alloc@std@@6B@
CONST	SEGMENT
??_7bad_alloc@std@@6B@ DQ FLAT:??_R4bad_alloc@std@@6B@	; std::bad_alloc::`vftable'
	DQ	FLAT:??_Ebad_alloc@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT ??_C@_0BC@EOODALEL@Unknown?5exception@
CONST	SEGMENT
??_C@_0BC@EOODALEL@Unknown?5exception@ DB 'Unknown exception', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7exception@std@@6B@
CONST	SEGMENT
??_7exception@std@@6B@ DQ FLAT:??_R4exception@std@@6B@	; std::exception::`vftable'
	DQ	FLAT:??_Eexception@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Copy_memmove_tail@PEAPEAVServerHandler@mu2@@@std@@YAPEAPEAVServerHandler@mu2@@QEBDQEAPEAV12@_K2@Z DD 011801H
	DD	08218H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Copy_memmove@PEAPEAVServerHandler@mu2@@PEAPEAV12@@std@@YAPEAPEAVServerHandler@mu2@@PEAPEAV12@00@Z DD 011301H
	DD	0e213H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Emplace_back@$$V@?$_Uninitialized_backout_al@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAAXXZ DD 020a01H
	DD	07006520aH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$_Uninitialized_backout_al@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Zero_range@PEAPEAVServerHandler@mu2@@@std@@YAPEAPEAVServerHandler@mu2@@QEAPEAV12@0@Z DD 010e01H
	DD	0820eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Uninitialized_move@PEAPEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@YAPEAPEAVServerHandler@mu2@@QEAPEAV12@0PEAPEAV12@AEAV?$allocator@PEAVServerHandler@mu2@@@0@@Z DD 021b01H
	DD	01b011bH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??1_Reallocation_guard@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAA@XZ DB 06H
	DB	00H
	DB	00H
	DB	0dH, 02H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??1_Reallocation_guard@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAA@XZ DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??1_Reallocation_guard@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAA@XZ DB 068H
	DD	imagerel $stateUnwindMap$??1_Reallocation_guard@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAA@XZ
	DD	imagerel $ip2state$??1_Reallocation_guard@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1_Reallocation_guard@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAA@XZ DD 010919H
	DD	0c209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??1_Reallocation_guard@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__F?inst@?$ISingleton@VtheServerUnionHandler@mu2@@@mu2@@1VtheServerUnionHandler@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__E?inst@?$ISingleton@VtheServerUnionHandler@mu2@@@mu2@@1VtheServerUnionHandler@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???$_Uninitialized_value_construct_n@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@YAPEAPEAVServerHandler@mu2@@PEAPEAV12@_KAEAV?$allocator@PEAVServerHandler@mu2@@@0@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$_Uninitialized_value_construct_n@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@YAPEAPEAVServerHandler@mu2@@PEAPEAV12@_KAEAV?$allocator@PEAVServerHandler@mu2@@@0@@Z DB 02H
	DB	00H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$_Uninitialized_value_construct_n@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@YAPEAPEAVServerHandler@mu2@@PEAPEAV12@_KAEAV?$allocator@PEAVServerHandler@mu2@@@0@@Z DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???$_Uninitialized_value_construct_n@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@YAPEAPEAVServerHandler@mu2@@PEAPEAV12@_KAEAV?$allocator@PEAVServerHandler@mu2@@@0@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$_Uninitialized_value_construct_n@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@YAPEAPEAVServerHandler@mu2@@PEAPEAV12@_KAEAV?$allocator@PEAVServerHandler@mu2@@@0@@Z DB 028H
	DD	imagerel $stateUnwindMap$??$_Uninitialized_value_construct_n@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@YAPEAPEAVServerHandler@mu2@@PEAPEAV12@_KAEAV?$allocator@PEAVServerHandler@mu2@@@0@@Z
	DD	imagerel $ip2state$??$_Uninitialized_value_construct_n@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@YAPEAPEAVServerHandler@mu2@@PEAPEAV12@_KAEAV?$allocator@PEAVServerHandler@mu2@@@0@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Uninitialized_value_construct_n@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@YAPEAPEAVServerHandler@mu2@@PEAPEAV12@_KAEAV?$allocator@PEAVServerHandler@mu2@@@0@@Z DD 011311H
	DD	0e213H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$_Uninitialized_value_construct_n@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@YAPEAPEAVServerHandler@mu2@@PEAPEAV12@_KAEAV?$allocator@PEAVServerHandler@mu2@@@0@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z DB 06H
	DB	00H
	DB	00H
	DB	091H, 07H
	DB	02H
	DB	'1', 02H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z DB 028H
	DD	imagerel $stateUnwindMap$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z
	DD	imagerel $ip2state$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z DD 021611H
	DD	0210116H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Resize@U_Value_init_tag@std@@@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z DD 021601H
	DD	0110116H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z DD 010901H
	DD	0a209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GWorldRunner@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?finish@WorldRunner@mu2@@EEAAXXZ DD 020c01H
	DD	017010cH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?update@WorldRunner@mu2@@EEAAXXZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?initialize@WorldRunner@mu2@@EEAA_NXZ DD 010901H
	DD	08209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1WorldRunner@mu2@@UEAA@XZ DD 020c01H
	DD	011010cH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$23@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$22@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$21@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$20@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$19@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$18@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$17@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$16@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$15@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$14@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$13@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$12@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$11@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$10@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$9@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$8@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$7@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$6@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$5@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$4@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$3@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$2@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$1@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z DB '`'
	DB	00H
	DB	00H
	DB	'^'
	DB	02H
	DB	0daH
	DB	04H
	DB	0a0H
	DB	06H
	DB	'n'
	DB	04H
	DB	0e2H
	DB	08H
	DB	'n'
	DB	04H
	DB	0e8H
	DB	0aH
	DB	'n'
	DB	04H
	DB	0e8H
	DB	0cH
	DB	'n'
	DB	04H
	DB	0e8H
	DB	0eH
	DB	'n'
	DB	04H
	DB	0e8H
	DB	010H
	DB	080H
	DB	04H
	DB	0eeH
	DB	012H
	DB	08cH
	DB	04H
	DB	0eeH
	DB	014H
	DB	08cH
	DB	04H
	DB	0eeH
	DB	016H
	DB	08cH
	DB	04H
	DB	0eeH
	DB	018H
	DB	08cH
	DB	04H
	DB	0eeH
	DB	01aH
	DB	08cH
	DB	04H
	DB	0eeH
	DB	01cH
	DB	08cH
	DB	04H
	DB	0eeH
	DB	01eH
	DB	08cH
	DB	04H
	DB	0eeH
	DB	' '
	DB	08cH
	DB	04H
	DB	0eeH
	DB	'"'
	DB	08cH
	DB	04H
	DB	0eeH
	DB	'$'
	DB	08cH
	DB	04H
	DB	0eeH
	DB	'&'
	DB	08cH
	DB	04H
	DB	0eeH
	DB	'('
	DB	08cH
	DB	04H
	DB	0eeH
	DB	'*'
	DB	08cH
	DB	04H
	DB	0eeH
	DB	','
	DB	08cH
	DB	04H
	DB	0eeH
	DB	'.'
	DB	08cH
	DB	04H
	DB	0eeH
	DB	'0'
	DB	08cH
	DB	04H
	DB	08aH
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z DB 030H
	DB	0eH
	DD	imagerel ?dtor$0@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DB	02eH
	DD	imagerel ?dtor$1@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DB	02eH
	DD	imagerel ?dtor$2@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DB	056H
	DD	imagerel ?dtor$3@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DB	07eH
	DD	imagerel ?dtor$4@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DB	0a6H
	DD	imagerel ?dtor$5@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DB	0ceH
	DD	imagerel ?dtor$6@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DB	0f6H
	DD	imagerel ?dtor$7@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DB	03dH
	DB	02H
	DD	imagerel ?dtor$8@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DB	09dH
	DB	02H
	DD	imagerel ?dtor$9@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DB	0fdH
	DB	02H
	DD	imagerel ?dtor$10@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DB	05dH
	DB	03H
	DD	imagerel ?dtor$11@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DB	0bdH
	DB	03H
	DD	imagerel ?dtor$12@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DB	01dH
	DB	04H
	DD	imagerel ?dtor$13@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DB	07dH
	DB	04H
	DD	imagerel ?dtor$14@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DB	0ddH
	DB	04H
	DD	imagerel ?dtor$15@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DB	03dH
	DB	05H
	DD	imagerel ?dtor$16@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DB	09dH
	DB	05H
	DD	imagerel ?dtor$17@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DB	0fdH
	DB	05H
	DD	imagerel ?dtor$18@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DB	05dH
	DB	06H
	DD	imagerel ?dtor$19@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DB	0bdH
	DB	06H
	DD	imagerel ?dtor$20@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DB	01dH
	DB	07H
	DD	imagerel ?dtor$21@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DB	07dH
	DB	07H
	DD	imagerel ?dtor$22@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
	DB	0ddH
	DB	07H
	DD	imagerel ?dtor$23@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z DB 028H
	DD	imagerel $stateUnwindMap$??0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z
	DD	imagerel $ip2state$??0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z DD 031211H
	DD	0a20112H
	DD	0700bH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Xlength@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@CAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?_Tidy@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAXXZ DB 06H
	DB	00H
	DB	00H
	DB	'5', 03H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?_Tidy@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAXXZ DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?_Tidy@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAXXZ DB 068H
	DD	imagerel $stateUnwindMap$?_Tidy@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAXXZ
	DD	imagerel $ip2state$?_Tidy@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Tidy@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAXXZ DD 020c19H
	DD	013010cH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?_Tidy@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?_Change_array@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAXQEAPEAVServerHandler@mu2@@_K1@Z DB 06H
	DB	00H
	DB	00H
	DB	'q', 03H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?_Change_array@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAXQEAPEAVServerHandler@mu2@@_K1@Z DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?_Change_array@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAXQEAPEAVServerHandler@mu2@@_K1@Z DB 068H
	DD	imagerel $stateUnwindMap$?_Change_array@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAXQEAPEAVServerHandler@mu2@@_K1@Z
	DD	imagerel $ip2state$?_Change_array@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAXQEAPEAVServerHandler@mu2@@_K1@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Change_array@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAXQEAPEAVServerHandler@mu2@@_K1@Z DD 021b19H
	DD	013011bH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?_Change_array@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAXQEAPEAVServerHandler@mu2@@_K1@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Calculate_growth@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEBA_K_K@Z DD 021101H
	DD	0110111H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?allocate@?$allocator@PEAVServerHandler@mu2@@@std@@QEAAPEAPEAVServerHandler@mu2@@_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Notify@EventListener@mu2@@UEAAXAEBV?$SmartPtrEx@UEvent@mu2@@@2@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Throw_bad_array_new_length@std@@YAXXZ DD 010401H
	DD	08204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1bad_array_new_length@std@@UEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_array_new_length@std@@QEAA@XZ DD 010601H
	DD	07006H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z DD 020f01H
	DD	0700b320fH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gexception@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?what@exception@std@@UEBAPEBDXZ DD 010901H
	DD	02209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0exception@std@@QEAA@AEBV01@@Z DD 020f01H
	DD	0700b320fH
xdata	ENDS
; Function compile flags: /Odsp
;	COMDAT ??_EWorldRunner@mu2@@WEA@EAAPEAXI@Z
_TEXT	SEGMENT
??_EWorldRunner@mu2@@WEA@EAAPEAXI@Z PROC		; [thunk]:mu2::WorldRunner::`vector deleting destructor', COMDAT
  00000	48 83 e9 40	 sub	 rcx, 64			; 00000040H
  00004	e9 00 00 00 00	 jmp	 ??_EWorldRunner@mu2@@UEAAPEAXI@Z
??_EWorldRunner@mu2@@WEA@EAAPEAXI@Z ENDP		; [thunk]:mu2::WorldRunner::`vector deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
;	COMDAT ??$_Copy_memmove_tail@PEAPEAVServerHandler@mu2@@@std@@YAPEAPEAVServerHandler@mu2@@QEBDQEAPEAV12@_K2@Z
_TEXT	SEGMENT
_Dest_ch$ = 32
$T1 = 40
_Dest_ptr$ = 48
_First_ch$ = 80
_Dest$ = 88
_Byte_count$ = 96
_Object_count$ = 104
??$_Copy_memmove_tail@PEAPEAVServerHandler@mu2@@@std@@YAPEAPEAVServerHandler@mu2@@QEBDQEAPEAV12@_K2@Z PROC ; std::_Copy_memmove_tail<mu2::ServerHandler * *>, COMDAT

; 4747 :     const char* const _First_ch, const _OutCtgIt _Dest, const size_t _Byte_count, const size_t _Object_count) {

$LN8:
  00000	4c 89 4c 24 20	 mov	 QWORD PTR [rsp+32], r9
  00005	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 4627 :     return _Val;

  00018	48 8b 44 24 58	 mov	 rax, QWORD PTR _Dest$[rsp]
  0001d	48 89 44 24 28	 mov	 QWORD PTR $T1[rsp], rax

; 4748 :     _STL_INTERNAL_CHECK(_Byte_count == _Object_count * sizeof(*_Dest));
; 4749 :     const auto _Dest_ptr = _STD _To_address(_Dest);

  00022	48 8b 44 24 28	 mov	 rax, QWORD PTR $T1[rsp]
  00027	48 89 44 24 30	 mov	 QWORD PTR _Dest_ptr$[rsp], rax

; 4750 :     const auto _Dest_ch  = const_cast<char*>(reinterpret_cast<const volatile char*>(_Dest_ptr));

  0002c	48 8b 44 24 30	 mov	 rax, QWORD PTR _Dest_ptr$[rsp]
  00031	48 89 44 24 20	 mov	 QWORD PTR _Dest_ch$[rsp], rax

; 4751 :     _CSTD memmove(_Dest_ch, _First_ch, _Byte_count);

  00036	4c 8b 44 24 60	 mov	 r8, QWORD PTR _Byte_count$[rsp]
  0003b	48 8b 54 24 50	 mov	 rdx, QWORD PTR _First_ch$[rsp]
  00040	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Dest_ch$[rsp]
  00045	e8 00 00 00 00	 call	 memmove
  0004a	90		 npad	 1

; 4752 :     if constexpr (is_pointer_v<_OutCtgIt>) {
; 4753 :         (void) _Object_count;
; 4754 :         // CodeQL [SM02986] This cast is correct: we're bypassing pointer arithmetic for performance.
; 4755 :         return reinterpret_cast<_OutCtgIt>(_Dest_ch + _Byte_count);

  0004b	48 8b 44 24 60	 mov	 rax, QWORD PTR _Byte_count$[rsp]
  00050	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Dest_ch$[rsp]
  00055	48 03 c8	 add	 rcx, rax
  00058	48 8b c1	 mov	 rax, rcx

; 4756 :     } else {
; 4757 :         return _Dest + static_cast<_Iter_diff_t<_OutCtgIt>>(_Object_count);
; 4758 :     }
; 4759 : }

  0005b	48 83 c4 48	 add	 rsp, 72			; 00000048H
  0005f	c3		 ret	 0
??$_Copy_memmove_tail@PEAPEAVServerHandler@mu2@@@std@@YAPEAPEAVServerHandler@mu2@@QEBDQEAPEAV12@_K2@Z ENDP ; std::_Copy_memmove_tail<mu2::ServerHandler * *>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
;	COMDAT ??$_Copy_memmove@PEAPEAVServerHandler@mu2@@PEAPEAV12@@std@@YAPEAPEAVServerHandler@mu2@@PEAPEAV12@00@Z
_TEXT	SEGMENT
_First_ptr$ = 32
_Last_ptr$ = 40
_First_ch$ = 48
$T1 = 56
$T2 = 64
_Last_ch$ = 72
_Object_count$ = 80
_Byte_count$ = 88
$T3 = 96
_First$ = 128
_Last$ = 136
_Dest$ = 144
??$_Copy_memmove@PEAPEAVServerHandler@mu2@@PEAPEAV12@@std@@YAPEAPEAVServerHandler@mu2@@PEAPEAV12@00@Z PROC ; std::_Copy_memmove<mu2::ServerHandler * *,mu2::ServerHandler * *>, COMDAT

; 4762 : _OutCtgIt _Copy_memmove(_CtgIt _First, _CtgIt _Last, _OutCtgIt _Dest) {

$LN18:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 78	 sub	 rsp, 120		; 00000078H

; 4627 :     return _Val;

  00013	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _First$[rsp]
  0001b	48 89 44 24 38	 mov	 QWORD PTR $T1[rsp], rax

; 4763 :     _STL_INTERNAL_CHECK(_First <= _Last);
; 4764 :     const auto _First_ptr    = _STD _To_address(_First);

  00020	48 8b 44 24 38	 mov	 rax, QWORD PTR $T1[rsp]
  00025	48 89 44 24 20	 mov	 QWORD PTR _First_ptr$[rsp], rax

; 4627 :     return _Val;

  0002a	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR _Last$[rsp]
  00032	48 89 44 24 40	 mov	 QWORD PTR $T2[rsp], rax

; 4765 :     const auto _Last_ptr     = _STD _To_address(_Last);

  00037	48 8b 44 24 40	 mov	 rax, QWORD PTR $T2[rsp]
  0003c	48 89 44 24 28	 mov	 QWORD PTR _Last_ptr$[rsp], rax

; 4766 :     const auto _Object_count = static_cast<size_t>(_Last_ptr - _First_ptr);

  00041	48 8b 44 24 20	 mov	 rax, QWORD PTR _First_ptr$[rsp]
  00046	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Last_ptr$[rsp]
  0004b	48 2b c8	 sub	 rcx, rax
  0004e	48 8b c1	 mov	 rax, rcx
  00051	48 c1 f8 03	 sar	 rax, 3
  00055	48 89 44 24 50	 mov	 QWORD PTR _Object_count$[rsp], rax

; 4767 :     const auto _First_ch     = const_cast<const char*>(reinterpret_cast<const volatile char*>(_First_ptr));

  0005a	48 8b 44 24 20	 mov	 rax, QWORD PTR _First_ptr$[rsp]
  0005f	48 89 44 24 30	 mov	 QWORD PTR _First_ch$[rsp], rax

; 4768 :     const auto _Last_ch      = const_cast<const char*>(reinterpret_cast<const volatile char*>(_Last_ptr));

  00064	48 8b 44 24 28	 mov	 rax, QWORD PTR _Last_ptr$[rsp]
  00069	48 89 44 24 48	 mov	 QWORD PTR _Last_ch$[rsp], rax

; 4769 :     const auto _Byte_count   = static_cast<size_t>(_Last_ch - _First_ch);

  0006e	48 8b 44 24 30	 mov	 rax, QWORD PTR _First_ch$[rsp]
  00073	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _Last_ch$[rsp]
  00078	48 2b c8	 sub	 rcx, rax
  0007b	48 8b c1	 mov	 rax, rcx
  0007e	48 89 44 24 58	 mov	 QWORD PTR _Byte_count$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00083	48 8d 84 24 90
	00 00 00	 lea	 rax, QWORD PTR _Dest$[rsp]
  0008b	48 89 44 24 60	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 4770 :     return _STD _Copy_memmove_tail(_First_ch, _STD move(_Dest), _Byte_count, _Object_count);

  00090	4c 8b 4c 24 50	 mov	 r9, QWORD PTR _Object_count$[rsp]
  00095	4c 8b 44 24 58	 mov	 r8, QWORD PTR _Byte_count$[rsp]
  0009a	48 8b 44 24 60	 mov	 rax, QWORD PTR $T3[rsp]
  0009f	48 8b 10	 mov	 rdx, QWORD PTR [rax]
  000a2	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _First_ch$[rsp]
  000a7	e8 00 00 00 00	 call	 ??$_Copy_memmove_tail@PEAPEAVServerHandler@mu2@@@std@@YAPEAPEAVServerHandler@mu2@@QEBDQEAPEAV12@_K2@Z ; std::_Copy_memmove_tail<mu2::ServerHandler * *>

; 4771 : }

  000ac	48 83 c4 78	 add	 rsp, 120		; 00000078H
  000b0	c3		 ret	 0
??$_Copy_memmove@PEAPEAVServerHandler@mu2@@PEAPEAV12@@std@@YAPEAPEAVServerHandler@mu2@@PEAPEAV12@00@Z ENDP ; std::_Copy_memmove<mu2::ServerHandler * *,mu2::ServerHandler * *>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??$_Emplace_back@$$V@?$_Uninitialized_backout_al@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAAXXZ
_TEXT	SEGMENT
_Ptr$ = 0
$T1 = 8
$T2 = 16
$T3 = 24
$T4 = 32
__formal$ = 40
this$ = 64
??$_Emplace_back@$$V@?$_Uninitialized_backout_al@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAAXXZ PROC ; std::_Uninitialized_backout_al<std::allocator<mu2::ServerHandler *> >::_Emplace_back<>, COMDAT

; 1843 :     _CONSTEXPR20 void _Emplace_back(_Types&&... _Vals) { // construct a new element at *_Last and increment

$LN15:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi
  00006	48 83 ec 30	 sub	 rsp, 48			; 00000030H

; 1844 :         allocator_traits<_Alloc>::construct(_Al, _STD _Unfancy(_Last), _STD forward<_Types>(_Vals)...);

  0000a	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0000f	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00013	48 89 04 24	 mov	 QWORD PTR _Ptr$[rsp], rax

; 69   :     return _Ptr;

  00017	48 8b 04 24	 mov	 rax, QWORD PTR _Ptr$[rsp]
  0001b	48 89 44 24 08	 mov	 QWORD PTR $T1[rsp], rax

; 1844 :         allocator_traits<_Alloc>::construct(_Al, _STD _Unfancy(_Last), _STD forward<_Types>(_Vals)...);

  00020	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00025	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00029	48 89 44 24 28	 mov	 QWORD PTR __formal$[rsp], rax

; 732  :         ::new (const_cast<void*>(static_cast<const volatile void*>(_Ptr))) _Objty(_STD forward<_Types>(_Args)...);

  0002e	48 c7 44 24 20
	08 00 00 00	 mov	 QWORD PTR $T4[rsp], 8

; 1844 :         allocator_traits<_Alloc>::construct(_Al, _STD _Unfancy(_Last), _STD forward<_Types>(_Vals)...);

  00037	48 8b 44 24 08	 mov	 rax, QWORD PTR $T1[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  0003c	48 89 44 24 10	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 732  :         ::new (const_cast<void*>(static_cast<const volatile void*>(_Ptr))) _Objty(_STD forward<_Types>(_Args)...);

  00041	48 8b 44 24 10	 mov	 rax, QWORD PTR $T2[rsp]
  00046	48 89 44 24 18	 mov	 QWORD PTR $T3[rsp], rax
  0004b	48 8b 7c 24 18	 mov	 rdi, QWORD PTR $T3[rsp]
  00050	33 c0		 xor	 eax, eax
  00052	48 8b 4c 24 20	 mov	 rcx, QWORD PTR $T4[rsp]
  00057	f3 aa		 rep stosb

; 1845 :         ++_Last;

  00059	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0005e	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00062	48 83 c0 08	 add	 rax, 8
  00066	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  0006b	48 89 41 08	 mov	 QWORD PTR [rcx+8], rax

; 1846 :     }

  0006f	48 83 c4 30	 add	 rsp, 48			; 00000030H
  00073	5f		 pop	 rdi
  00074	c3		 ret	 0
??$_Emplace_back@$$V@?$_Uninitialized_backout_al@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAAXXZ ENDP ; std::_Uninitialized_backout_al<std::allocator<mu2::ServerHandler *> >::_Emplace_back<>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??1?$_Uninitialized_backout_al@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAA@XZ
_TEXT	SEGMENT
_Al$ = 0
_Last$ = 8
_First$ = 16
this$ = 48
??1?$_Uninitialized_backout_al@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAA@XZ PROC ; std::_Uninitialized_backout_al<std::allocator<mu2::ServerHandler *> >::~_Uninitialized_backout_al<std::allocator<mu2::ServerHandler *> >, COMDAT

; 1838 :     _CONSTEXPR20 ~_Uninitialized_backout_al() {

$LN9:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 1839 :         _STD _Destroy_range(_First, _Last, _Al);

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00012	48 89 04 24	 mov	 QWORD PTR _Al$[rsp], rax
  00016	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001b	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0001f	48 89 44 24 08	 mov	 QWORD PTR _Last$[rsp], rax
  00024	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00029	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0002c	48 89 44 24 10	 mov	 QWORD PTR _First$[rsp], rax

; 1840 :     }

  00031	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00035	c3		 ret	 0
??1?$_Uninitialized_backout_al@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAA@XZ ENDP ; std::_Uninitialized_backout_al<std::allocator<mu2::ServerHandler *> >::~_Uninitialized_backout_al<std::allocator<mu2::ServerHandler *> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??$_Zero_range@PEAPEAVServerHandler@mu2@@@std@@YAPEAPEAVServerHandler@mu2@@QEAPEAV12@0@Z
_TEXT	SEGMENT
_First_ch$ = 32
$T1 = 40
$T2 = 48
_Last_ch$ = 56
_First$ = 80
_Last$ = 88
??$_Zero_range@PEAPEAVServerHandler@mu2@@@std@@YAPEAPEAVServerHandler@mu2@@QEAPEAV12@0@Z PROC ; std::_Zero_range<mu2::ServerHandler * *>, COMDAT

; 2069 : _Ptr _Zero_range(const _Ptr _First, const _Ptr _Last) { // fill [_First, _Last) with zeroes

$LN13:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 48	 sub	 rsp, 72			; 00000048H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 4627 :     return _Val;

  0000e	48 8b 44 24 50	 mov	 rax, QWORD PTR _First$[rsp]
  00013	48 89 44 24 28	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 2070 :     char* const _First_ch = reinterpret_cast<char*>(_STD _To_address(_First));

  00018	48 8b 44 24 28	 mov	 rax, QWORD PTR $T1[rsp]
  0001d	48 89 44 24 20	 mov	 QWORD PTR _First_ch$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 4627 :     return _Val;

  00022	48 8b 44 24 58	 mov	 rax, QWORD PTR _Last$[rsp]
  00027	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 2071 :     char* const _Last_ch  = reinterpret_cast<char*>(_STD _To_address(_Last));

  0002c	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
  00031	48 89 44 24 38	 mov	 QWORD PTR _Last_ch$[rsp], rax

; 2072 :     _CSTD memset(_First_ch, 0, static_cast<size_t>(_Last_ch - _First_ch));

  00036	48 8b 44 24 20	 mov	 rax, QWORD PTR _First_ch$[rsp]
  0003b	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Last_ch$[rsp]
  00040	48 2b c8	 sub	 rcx, rax
  00043	48 8b c1	 mov	 rax, rcx
  00046	4c 8b c0	 mov	 r8, rax
  00049	33 d2		 xor	 edx, edx
  0004b	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _First_ch$[rsp]
  00050	e8 00 00 00 00	 call	 memset

; 2073 :     return _Last;

  00055	48 8b 44 24 58	 mov	 rax, QWORD PTR _Last$[rsp]

; 2074 : }

  0005a	48 83 c4 48	 add	 rsp, 72			; 00000048H
  0005e	c3		 ret	 0
??$_Zero_range@PEAPEAVServerHandler@mu2@@@std@@YAPEAPEAVServerHandler@mu2@@QEAPEAV12@0@Z ENDP ; std::_Zero_range<mu2::ServerHandler * *>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??$_Uninitialized_move@PEAPEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@YAPEAPEAVServerHandler@mu2@@QEAPEAV12@0PEAPEAV12@AEAV?$allocator@PEAVServerHandler@mu2@@@0@@Z
_TEXT	SEGMENT
_UFirst$ = 32
_Backout$ = 40
_ULast$ = 64
$T1 = 72
$T2 = 80
$T3 = 88
$T4 = 96
_Ptr$ = 104
$T5 = 112
$T6 = 120
$T7 = 128
$T8 = 136
$T9 = 144
$T10 = 152
$T11 = 160
__formal$ = 168
_Al$ = 176
_Last$ = 184
_First$ = 192
_First$ = 224
_Last$ = 232
_Dest$ = 240
_Al$ = 248
??$_Uninitialized_move@PEAPEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@YAPEAPEAVServerHandler@mu2@@QEAPEAV12@0PEAPEAV12@AEAV?$allocator@PEAVServerHandler@mu2@@@0@@Z PROC ; std::_Uninitialized_move<mu2::ServerHandler * *,std::allocator<mu2::ServerHandler *> >, COMDAT

; 1977 :     const _InIt _First, const _InIt _Last, _Alloc_ptr_t<_Alloc> _Dest, _Alloc& _Al) {

$LN65:
  00000	4c 89 4c 24 20	 mov	 QWORD PTR [rsp+32], r9
  00005	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	48 81 ec d8 00
	00 00		 sub	 rsp, 216		; 000000d8H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 1382 :         return _It + 0;

  0001b	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR _First$[rsp]
  00023	48 89 44 24 48	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1984 :     auto _UFirst      = _STD _Get_unwrapped(_First);

  00028	48 8b 44 24 48	 mov	 rax, QWORD PTR $T1[rsp]
  0002d	48 89 44 24 20	 mov	 QWORD PTR _UFirst$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 1382 :         return _It + 0;

  00032	48 8b 84 24 e8
	00 00 00	 mov	 rax, QWORD PTR _Last$[rsp]
  0003a	48 89 44 24 50	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1985 :     const auto _ULast = _STD _Get_unwrapped(_Last);

  0003f	48 8b 44 24 50	 mov	 rax, QWORD PTR $T2[rsp]
  00044	48 89 44 24 40	 mov	 QWORD PTR _ULast$[rsp], rax

; 69   :     return _Ptr;

  00049	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR _Dest$[rsp]
  00051	48 89 44 24 58	 mov	 QWORD PTR $T3[rsp], rax

; 1986 :     if constexpr (conjunction_v<bool_constant<_Iter_move_cat<decltype(_UFirst), _Ptrval>::_Bitcopy_constructible>,
; 1987 :                       _Uses_default_construct<_Alloc, _Ptrval, decltype(_STD move(*_UFirst))>>) {
; 1988 : #if _HAS_CXX20
; 1989 :         if (!_STD is_constant_evaluated())
; 1990 : #endif // _HAS_CXX20
; 1991 :         {
; 1992 :             _STD _Copy_memmove(_UFirst, _ULast, _STD _Unfancy(_Dest));

  00056	48 8b 44 24 58	 mov	 rax, QWORD PTR $T3[rsp]
  0005b	4c 8b c0	 mov	 r8, rax
  0005e	48 8b 54 24 40	 mov	 rdx, QWORD PTR _ULast$[rsp]
  00063	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _UFirst$[rsp]
  00068	e8 00 00 00 00	 call	 ??$_Copy_memmove@PEAPEAVServerHandler@mu2@@PEAPEAV12@@std@@YAPEAPEAVServerHandler@mu2@@PEAPEAV12@00@Z ; std::_Copy_memmove<mu2::ServerHandler * *,mu2::ServerHandler * *>

; 1993 :             return _Dest + (_ULast - _UFirst);

  0006d	48 8b 44 24 20	 mov	 rax, QWORD PTR _UFirst$[rsp]
  00072	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _ULast$[rsp]
  00077	48 2b c8	 sub	 rcx, rax
  0007a	48 8b c1	 mov	 rax, rcx
  0007d	48 c1 f8 03	 sar	 rax, 3
  00081	48 8b 8c 24 f0
	00 00 00	 mov	 rcx, QWORD PTR _Dest$[rsp]
  00089	48 8d 04 c1	 lea	 rax, QWORD PTR [rcx+rax*8]
  0008d	e9 25 01 00 00	 jmp	 $LN1@Uninitiali

; 1833 :     _CONSTEXPR20 _Uninitialized_backout_al(pointer _Dest, _Alloc& _Al_) : _First(_Dest), _Last(_Dest), _Al(_Al_) {}

  00092	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR _Dest$[rsp]
  0009a	48 89 44 24 28	 mov	 QWORD PTR _Backout$[rsp], rax
  0009f	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR _Dest$[rsp]
  000a7	48 89 44 24 30	 mov	 QWORD PTR _Backout$[rsp+8], rax
  000ac	48 8b 84 24 f8
	00 00 00	 mov	 rax, QWORD PTR _Al$[rsp]
  000b4	48 89 44 24 38	 mov	 QWORD PTR _Backout$[rsp+16], rax

; 1994 :         }
; 1995 :     }
; 1996 : 
; 1997 :     _Uninitialized_backout_al<_Alloc> _Backout{_Dest, _Al};
; 1998 :     for (; _UFirst != _ULast; ++_UFirst) {

  000b9	eb 0e		 jmp	 SHORT $LN4@Uninitiali
$LN2@Uninitiali:
  000bb	48 8b 44 24 20	 mov	 rax, QWORD PTR _UFirst$[rsp]
  000c0	48 83 c0 08	 add	 rax, 8
  000c4	48 89 44 24 20	 mov	 QWORD PTR _UFirst$[rsp], rax
$LN4@Uninitiali:
  000c9	48 8b 44 24 40	 mov	 rax, QWORD PTR _ULast$[rsp]
  000ce	48 39 44 24 20	 cmp	 QWORD PTR _UFirst$[rsp], rax
  000d3	0f 84 88 00 00
	00		 je	 $LN3@Uninitiali
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  000d9	48 8b 44 24 20	 mov	 rax, QWORD PTR _UFirst$[rsp]
  000de	48 89 44 24 60	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1999 :         _Backout._Emplace_back(_STD move(*_UFirst));

  000e3	48 8b 44 24 60	 mov	 rax, QWORD PTR $T4[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  000e8	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1844 :         allocator_traits<_Alloc>::construct(_Al, _STD _Unfancy(_Last), _STD forward<_Types>(_Vals)...);

  000f0	48 8b 44 24 30	 mov	 rax, QWORD PTR _Backout$[rsp+8]
  000f5	48 89 44 24 68	 mov	 QWORD PTR _Ptr$[rsp], rax

; 69   :     return _Ptr;

  000fa	48 8b 44 24 68	 mov	 rax, QWORD PTR _Ptr$[rsp]
  000ff	48 89 44 24 70	 mov	 QWORD PTR $T5[rsp], rax

; 1844 :         allocator_traits<_Alloc>::construct(_Al, _STD _Unfancy(_Last), _STD forward<_Types>(_Vals)...);

  00104	48 8b 44 24 38	 mov	 rax, QWORD PTR _Backout$[rsp+16]
  00109	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR __formal$[rsp], rax
  00111	48 8b 44 24 70	 mov	 rax, QWORD PTR $T5[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  00116	48 89 44 24 78	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 732  :         ::new (const_cast<void*>(static_cast<const volatile void*>(_Ptr))) _Objty(_STD forward<_Types>(_Args)...);

  0011b	48 8b 44 24 78	 mov	 rax, QWORD PTR $T6[rsp]
  00120	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T8[rsp], rax

; 1844 :         allocator_traits<_Alloc>::construct(_Al, _STD _Unfancy(_Last), _STD forward<_Types>(_Vals)...);

  00128	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR $T7[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00130	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 732  :         ::new (const_cast<void*>(static_cast<const volatile void*>(_Ptr))) _Objty(_STD forward<_Types>(_Args)...);

  00138	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T8[rsp]
  00140	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR $T9[rsp]
  00148	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0014b	48 89 08	 mov	 QWORD PTR [rax], rcx

; 1845 :         ++_Last;

  0014e	48 8b 44 24 30	 mov	 rax, QWORD PTR _Backout$[rsp+8]
  00153	48 83 c0 08	 add	 rax, 8
  00157	48 89 44 24 30	 mov	 QWORD PTR _Backout$[rsp+8], rax

; 2000 :     }

  0015c	e9 5a ff ff ff	 jmp	 $LN2@Uninitiali
$LN3@Uninitiali:

; 1849 :         _First = _Last;

  00161	48 8b 44 24 30	 mov	 rax, QWORD PTR _Backout$[rsp+8]
  00166	48 89 44 24 28	 mov	 QWORD PTR _Backout$[rsp], rax

; 1850 :         return _Last;

  0016b	48 8b 44 24 30	 mov	 rax, QWORD PTR _Backout$[rsp+8]
  00170	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax

; 2001 : 
; 2002 :     return _Backout._Release();

  00178	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  00180	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax

; 1839 :         _STD _Destroy_range(_First, _Last, _Al);

  00188	48 8b 44 24 38	 mov	 rax, QWORD PTR _Backout$[rsp+16]
  0018d	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR _Al$[rsp], rax
  00195	48 8b 44 24 30	 mov	 rax, QWORD PTR _Backout$[rsp+8]
  0019a	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR _Last$[rsp], rax
  001a2	48 8b 44 24 28	 mov	 rax, QWORD PTR _Backout$[rsp]
  001a7	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR _First$[rsp], rax

; 2001 : 
; 2002 :     return _Backout._Release();

  001af	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
$LN1@Uninitiali:

; 2003 : }

  001b7	48 81 c4 d8 00
	00 00		 add	 rsp, 216		; 000000d8H
  001be	c3		 ret	 0
??$_Uninitialized_move@PEAPEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@YAPEAPEAVServerHandler@mu2@@QEAPEAV12@0PEAPEAV12@AEAV?$allocator@PEAVServerHandler@mu2@@@0@@Z ENDP ; std::_Uninitialized_move<mu2::ServerHandler * *,std::allocator<mu2::ServerHandler *> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ??1_Reallocation_guard@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAA@XZ
_TEXT	SEGMENT
_Bytes$ = 32
_Ptr$ = 40
_Count$ = 48
_Ptr$ = 56
_Al$ = 64
_Last$ = 72
_First$ = 80
this$ = 88
this$ = 112
??1_Reallocation_guard@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAA@XZ PROC ; std::vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >::_Reallocation_guard::~_Reallocation_guard, COMDAT

; 620  :         _CONSTEXPR20 ~_Reallocation_guard() noexcept {

$LN25:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 68	 sub	 rsp, 104		; 00000068H

; 621  :             if (_New_begin != nullptr) {

  00009	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00013	0f 84 95 00 00
	00		 je	 $LN2@Reallocati

; 622  :                 _STD _Destroy_range(_Constructed_first, _Constructed_last, _Al);

  00019	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0001e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00021	48 89 44 24 40	 mov	 QWORD PTR _Al$[rsp], rax
  00026	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0002b	48 8b 40 20	 mov	 rax, QWORD PTR [rax+32]
  0002f	48 89 44 24 48	 mov	 QWORD PTR _Last$[rsp], rax
  00034	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  00039	48 8b 40 18	 mov	 rax, QWORD PTR [rax+24]
  0003d	48 89 44 24 50	 mov	 QWORD PTR _First$[rsp], rax

; 623  :                 _Al.deallocate(_New_begin, _New_capacity);

  00042	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  00047	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0004b	48 89 44 24 30	 mov	 QWORD PTR _Count$[rsp], rax
  00050	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  00055	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00059	48 89 44 24 38	 mov	 QWORD PTR _Ptr$[rsp], rax
  0005e	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  00063	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00066	48 89 44 24 58	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  0006b	48 8b 44 24 30	 mov	 rax, QWORD PTR _Count$[rsp]
  00070	48 c1 e0 03	 shl	 rax, 3
  00074	48 89 44 24 20	 mov	 QWORD PTR _Bytes$[rsp], rax
  00079	48 8b 44 24 38	 mov	 rax, QWORD PTR _Ptr$[rsp]
  0007e	48 89 44 24 28	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  00083	48 81 7c 24 20
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  0008c	72 10		 jb	 SHORT $LN16@Reallocati

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  0008e	48 8d 54 24 20	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  00093	48 8d 4c 24 28	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  00098	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  0009d	90		 npad	 1
$LN16@Reallocati:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  0009e	48 8b 54 24 20	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  000a3	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  000a8	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  000ad	90		 npad	 1
$LN2@Reallocati:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 625  :         }

  000ae	48 83 c4 68	 add	 rsp, 104		; 00000068H
  000b2	c3		 ret	 0
??1_Reallocation_guard@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAA@XZ ENDP ; std::vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >::_Reallocation_guard::~_Reallocation_guard
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??__F?inst@?$ISingleton@VtheServerUnionHandler@mu2@@@mu2@@1VtheServerUnionHandler@2@A@@YAXXZ
text$yd	SEGMENT
??__F?inst@?$ISingleton@VtheServerUnionHandler@mu2@@@mu2@@1VtheServerUnionHandler@2@A@@YAXXZ PROC ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::theServerUnionHandler>::inst'', COMDAT
  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?inst@?$ISingleton@VtheServerUnionHandler@mu2@@@mu2@@1VtheServerUnionHandler@2@A ; mu2::ISingleton<mu2::theServerUnionHandler>::inst
  0000b	e8 00 00 00 00	 call	 ??1theServerUnionHandler@mu2@@UEAA@XZ ; mu2::theServerUnionHandler::~theServerUnionHandler
  00010	90		 npad	 1
  00011	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00015	c3		 ret	 0
??__F?inst@?$ISingleton@VtheServerUnionHandler@mu2@@@mu2@@1VtheServerUnionHandler@2@A@@YAXXZ ENDP ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::theServerUnionHandler>::inst''
text$yd	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??__E?inst@?$ISingleton@VtheServerUnionHandler@mu2@@@mu2@@1VtheServerUnionHandler@2@A@@YAXXZ
text$di	SEGMENT
??__E?inst@?$ISingleton@VtheServerUnionHandler@mu2@@@mu2@@1VtheServerUnionHandler@2@A@@YAXXZ PROC ; `dynamic initializer for 'mu2::ISingleton<mu2::theServerUnionHandler>::inst'', COMDAT

; 90   : template<typename T> T ISingleton<T>::inst;

  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?inst@?$ISingleton@VtheServerUnionHandler@mu2@@@mu2@@1VtheServerUnionHandler@2@A ; mu2::ISingleton<mu2::theServerUnionHandler>::inst
  0000b	e8 00 00 00 00	 call	 ??0theServerUnionHandler@mu2@@AEAA@XZ ; mu2::theServerUnionHandler::theServerUnionHandler
  00010	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??__F?inst@?$ISingleton@VtheServerUnionHandler@mu2@@@mu2@@1VtheServerUnionHandler@2@A@@YAXXZ ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::theServerUnionHandler>::inst''
  00017	e8 00 00 00 00	 call	 atexit
  0001c	90		 npad	 1
  0001d	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00021	c3		 ret	 0
??__E?inst@?$ISingleton@VtheServerUnionHandler@mu2@@@mu2@@1VtheServerUnionHandler@2@A@@YAXXZ ENDP ; `dynamic initializer for 'mu2::ISingleton<mu2::theServerUnionHandler>::inst''
text$di	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??$_Uninitialized_value_construct_n@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@YAPEAPEAVServerHandler@mu2@@PEAPEAV12@_KAEAV?$allocator@PEAVServerHandler@mu2@@@0@@Z
_TEXT	SEGMENT
_Backout$ = 32
_PFirst$1 = 56
$T2 = 64
$T3 = 72
$T4 = 80
_Al$ = 88
_Last$ = 96
_First$ = 104
_First$ = 128
_Count$ = 136
_Al$ = 144
??$_Uninitialized_value_construct_n@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@YAPEAPEAVServerHandler@mu2@@PEAPEAV12@_KAEAV?$allocator@PEAVServerHandler@mu2@@@0@@Z PROC ; std::_Uninitialized_value_construct_n<std::allocator<mu2::ServerHandler *> >, COMDAT

; 2078 :     _Alloc_ptr_t<_Alloc> _First, _Alloc_size_t<_Alloc> _Count, _Alloc& _Al) {

$LN40:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 78	 sub	 rsp, 120		; 00000078H

; 69   :     return _Ptr;

  00013	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _First$[rsp]
  0001b	48 89 44 24 40	 mov	 QWORD PTR $T2[rsp], rax

; 2079 :     // value-initialize _Count objects to raw _First, using _Al
; 2080 :     using _Ptrty = typename _Alloc::value_type*;
; 2081 :     if constexpr (_Use_memset_value_construct_v<_Ptrty> && _Uses_default_construct<_Alloc, _Ptrty>::value) {
; 2082 : #if _HAS_CXX20
; 2083 :         if (!_STD is_constant_evaluated())
; 2084 : #endif // _HAS_CXX20
; 2085 :         {
; 2086 :             auto _PFirst = _Unfancy(_First);

  00020	48 8b 44 24 40	 mov	 rax, QWORD PTR $T2[rsp]
  00025	48 89 44 24 38	 mov	 QWORD PTR _PFirst$1[rsp], rax

; 2087 :             _Zero_range(_PFirst, _PFirst + _Count);

  0002a	48 8b 44 24 38	 mov	 rax, QWORD PTR _PFirst$1[rsp]
  0002f	48 8b 8c 24 88
	00 00 00	 mov	 rcx, QWORD PTR _Count$[rsp]
  00037	48 8d 04 c8	 lea	 rax, QWORD PTR [rax+rcx*8]
  0003b	48 8b d0	 mov	 rdx, rax
  0003e	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _PFirst$1[rsp]
  00043	e8 00 00 00 00	 call	 ??$_Zero_range@PEAPEAVServerHandler@mu2@@@std@@YAPEAPEAVServerHandler@mu2@@QEAPEAV12@0@Z ; std::_Zero_range<mu2::ServerHandler * *>

; 2088 :             return _First + _Count;

  00048	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _First$[rsp]
  00050	48 8b 8c 24 88
	00 00 00	 mov	 rcx, QWORD PTR _Count$[rsp]
  00058	48 8d 04 c8	 lea	 rax, QWORD PTR [rax+rcx*8]
  0005c	e9 95 00 00 00	 jmp	 $LN1@Uninitiali

; 1833 :     _CONSTEXPR20 _Uninitialized_backout_al(pointer _Dest, _Alloc& _Al_) : _First(_Dest), _Last(_Dest), _Al(_Al_) {}

  00061	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _First$[rsp]
  00069	48 89 44 24 20	 mov	 QWORD PTR _Backout$[rsp], rax
  0006e	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR _First$[rsp]
  00076	48 89 44 24 28	 mov	 QWORD PTR _Backout$[rsp+8], rax
  0007b	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR _Al$[rsp]
  00083	48 89 44 24 30	 mov	 QWORD PTR _Backout$[rsp+16], rax

; 2089 :         }
; 2090 :     }
; 2091 : 
; 2092 :     _Uninitialized_backout_al<_Alloc> _Backout{_First, _Al};
; 2093 :     for (; 0 < _Count; --_Count) {

  00088	eb 13		 jmp	 SHORT $LN4@Uninitiali
$LN2@Uninitiali:
  0008a	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR _Count$[rsp]
  00092	48 ff c8	 dec	 rax
  00095	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR _Count$[rsp], rax
$LN4@Uninitiali:
  0009d	48 83 bc 24 88
	00 00 00 00	 cmp	 QWORD PTR _Count$[rsp], 0
  000a6	76 0d		 jbe	 SHORT $LN3@Uninitiali

; 2094 :         _Backout._Emplace_back();

  000a8	48 8d 4c 24 20	 lea	 rcx, QWORD PTR _Backout$[rsp]
  000ad	e8 00 00 00 00	 call	 ??$_Emplace_back@$$V@?$_Uninitialized_backout_al@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAAXXZ ; std::_Uninitialized_backout_al<std::allocator<mu2::ServerHandler *> >::_Emplace_back<>
  000b2	90		 npad	 1

; 2095 :     }

  000b3	eb d5		 jmp	 SHORT $LN2@Uninitiali
$LN3@Uninitiali:

; 1849 :         _First = _Last;

  000b5	48 8b 44 24 28	 mov	 rax, QWORD PTR _Backout$[rsp+8]
  000ba	48 89 44 24 20	 mov	 QWORD PTR _Backout$[rsp], rax

; 1850 :         return _Last;

  000bf	48 8b 44 24 28	 mov	 rax, QWORD PTR _Backout$[rsp+8]
  000c4	48 89 44 24 48	 mov	 QWORD PTR $T3[rsp], rax

; 2096 : 
; 2097 :     return _Backout._Release();

  000c9	48 8b 44 24 48	 mov	 rax, QWORD PTR $T3[rsp]
  000ce	48 89 44 24 50	 mov	 QWORD PTR $T4[rsp], rax

; 1839 :         _STD _Destroy_range(_First, _Last, _Al);

  000d3	48 8b 44 24 30	 mov	 rax, QWORD PTR _Backout$[rsp+16]
  000d8	48 89 44 24 58	 mov	 QWORD PTR _Al$[rsp], rax
  000dd	48 8b 44 24 28	 mov	 rax, QWORD PTR _Backout$[rsp+8]
  000e2	48 89 44 24 60	 mov	 QWORD PTR _Last$[rsp], rax
  000e7	48 8b 44 24 20	 mov	 rax, QWORD PTR _Backout$[rsp]
  000ec	48 89 44 24 68	 mov	 QWORD PTR _First$[rsp], rax

; 2096 : 
; 2097 :     return _Backout._Release();

  000f1	48 8b 44 24 50	 mov	 rax, QWORD PTR $T4[rsp]
$LN1@Uninitiali:

; 2098 : }

  000f6	48 83 c4 78	 add	 rsp, 120		; 00000078H
  000fa	c3		 ret	 0
??$_Uninitialized_value_construct_n@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@YAPEAPEAVServerHandler@mu2@@PEAPEAV12@_KAEAV?$allocator@PEAVServerHandler@mu2@@@0@@Z ENDP ; std::_Uninitialized_value_construct_n<std::allocator<mu2::ServerHandler *> >
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
_Backout$ = 32
_PFirst$1 = 56
$T2 = 64
$T3 = 72
$T4 = 80
_Al$ = 88
_Last$ = 96
_First$ = 104
_First$ = 128
_Count$ = 136
_Al$ = 144
?dtor$0@?0???$_Uninitialized_value_construct_n@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@YAPEAPEAVServerHandler@mu2@@PEAPEAV12@_KAEAV?$allocator@PEAVServerHandler@mu2@@@0@@Z@4HA PROC ; `std::_Uninitialized_value_construct_n<std::allocator<mu2::ServerHandler *> >'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8d 4d 20	 lea	 rcx, QWORD PTR _Backout$[rbp]
  0000d	e8 00 00 00 00	 call	 ??1?$_Uninitialized_backout_al@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAA@XZ ; std::_Uninitialized_backout_al<std::allocator<mu2::ServerHandler *> >::~_Uninitialized_backout_al<std::allocator<mu2::ServerHandler *> >
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$0@?0???$_Uninitialized_value_construct_n@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@YAPEAPEAVServerHandler@mu2@@PEAPEAV12@_KAEAV?$allocator@PEAVServerHandler@mu2@@@0@@Z@4HA ENDP ; `std::_Uninitialized_value_construct_n<std::allocator<mu2::ServerHandler *> >'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z
_TEXT	SEGMENT
_Al$ = 32
_Newvec$ = 40
_Appended_first$ = 48
_Newcapacity$ = 56
$T1 = 64
$T2 = 72
tv133 = 80
_My_data$ = 88
_Oldsize$ = 96
_Mylast$ = 104
_Myfirst$ = 112
$T3 = 120
$T4 = 128
$T5 = 136
$T6 = 144
$T7 = 152
$T8 = 160
$T9 = 168
$T10 = 176
$T11 = 184
_Appended_last$ = 192
_Guard$ = 200
$T12 = 240
_Unsigned_max$13 = 248
this$ = 272
_Newsize$ = 280
_Val$ = 288
??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z PROC ; std::vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >::_Resize_reallocate<std::_Value_init_tag>, COMDAT

; 1552 :     _CONSTEXPR20 void _Resize_reallocate(const size_type _Newsize, const _Ty2& _Val) {

$LN273:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 81 ec 08 01
	00 00		 sub	 rsp, 264		; 00000108H

; 2231 :         return _Mypair._Get_first();

  00016	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1543 :         return *this;

  0001e	48 89 44 24 78	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2231 :         return _Mypair._Get_first();

  00023	48 8b 44 24 78	 mov	 rax, QWORD PTR $T3[rsp]
  00028	48 89 84 24 f0
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 746  :         return static_cast<size_t>(-1) / sizeof(value_type);

  00030	48 b8 ff ff ff
	ff ff ff ff 1f	 mov	 rax, 2305843009213693951 ; 1fffffffffffffffH
  0003a	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1919 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()), _Alty_traits::max_size(_Getal()));

  00042	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR $T4[rsp]
  0004a	48 89 44 24 40	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 866  :         constexpr auto _Unsigned_max = static_cast<make_unsigned_t<_Ty>>(-1);

  0004f	48 c7 84 24 f8
	00 00 00 ff ff
	ff ff		 mov	 QWORD PTR _Unsigned_max$13[rsp], -1

; 867  :         return static_cast<_Ty>(_Unsigned_max >> 1);

  0005b	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  00065	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1919 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()), _Alty_traits::max_size(_Getal()));

  0006d	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T5[rsp]
  00075	48 89 44 24 48	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 101  :     return _Right < _Left ? _Right : _Left;

  0007a	48 8b 44 24 48	 mov	 rax, QWORD PTR $T2[rsp]
  0007f	48 39 44 24 40	 cmp	 QWORD PTR $T1[rsp], rax
  00084	73 0c		 jae	 SHORT $LN32@Resize_rea
  00086	48 8d 44 24 40	 lea	 rax, QWORD PTR $T1[rsp]
  0008b	48 89 44 24 50	 mov	 QWORD PTR tv133[rsp], rax
  00090	eb 0a		 jmp	 SHORT $LN33@Resize_rea
$LN32@Resize_rea:
  00092	48 8d 44 24 48	 lea	 rax, QWORD PTR $T2[rsp]
  00097	48 89 44 24 50	 mov	 QWORD PTR tv133[rsp], rax
$LN33@Resize_rea:
  0009c	48 8b 44 24 50	 mov	 rax, QWORD PTR tv133[rsp]
  000a1	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T6[rsp], rax
  000a9	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR $T6[rsp]
  000b1	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1919 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()), _Alty_traits::max_size(_Getal()));

  000b9	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR $T7[rsp]
  000c1	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000c4	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T8[rsp], rax

; 1553 :         if (_Newsize > max_size()) {

  000cc	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR $T8[rsp]
  000d4	48 39 84 24 18
	01 00 00	 cmp	 QWORD PTR _Newsize$[rsp], rax
  000dc	76 06		 jbe	 SHORT $LN2@Resize_rea

; 1554 :             _Xlength();

  000de	e8 00 00 00 00	 call	 ?_Xlength@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@CAXXZ ; std::vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >::_Xlength
  000e3	90		 npad	 1
$LN2@Resize_rea:

; 2227 :         return _Mypair._Get_first();

  000e4	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  000ec	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2227 :         return _Mypair._Get_first();

  000f4	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T9[rsp]
  000fc	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax

; 1555 :         }
; 1556 : 
; 1557 :         auto& _Al         = _Getal();

  00104	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  0010c	48 89 44 24 20	 mov	 QWORD PTR _Al$[rsp], rax

; 1558 :         auto& _My_data    = _Mypair._Myval2;

  00111	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00119	48 89 44 24 58	 mov	 QWORD PTR _My_data$[rsp], rax

; 1559 :         pointer& _Myfirst = _My_data._Myfirst;

  0011e	48 8b 44 24 58	 mov	 rax, QWORD PTR _My_data$[rsp]
  00123	48 89 44 24 70	 mov	 QWORD PTR _Myfirst$[rsp], rax

; 1560 :         pointer& _Mylast  = _My_data._Mylast;

  00128	48 8b 44 24 58	 mov	 rax, QWORD PTR _My_data$[rsp]
  0012d	48 83 c0 08	 add	 rax, 8
  00131	48 89 44 24 68	 mov	 QWORD PTR _Mylast$[rsp], rax

; 1561 : 
; 1562 :         const auto _Oldsize    = static_cast<size_type>(_Mylast - _Myfirst);

  00136	48 8b 44 24 68	 mov	 rax, QWORD PTR _Mylast$[rsp]
  0013b	48 8b 4c 24 70	 mov	 rcx, QWORD PTR _Myfirst$[rsp]
  00140	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00143	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00146	48 2b c1	 sub	 rax, rcx
  00149	48 c1 f8 03	 sar	 rax, 3
  0014d	48 89 44 24 60	 mov	 QWORD PTR _Oldsize$[rsp], rax

; 1563 :         size_type _Newcapacity = _Calculate_growth(_Newsize);

  00152	48 8b 94 24 18
	01 00 00	 mov	 rdx, QWORD PTR _Newsize$[rsp]
  0015a	48 8b 8c 24 10
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00162	e8 00 00 00 00	 call	 ?_Calculate_growth@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEBA_K_K@Z ; std::vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >::_Calculate_growth
  00167	48 89 44 24 38	 mov	 QWORD PTR _Newcapacity$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 2303 :         return _Al.allocate(_Count);

  0016c	48 8b 54 24 38	 mov	 rdx, QWORD PTR _Newcapacity$[rsp]
  00171	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Al$[rsp]
  00176	e8 00 00 00 00	 call	 ?allocate@?$allocator@PEAVServerHandler@mu2@@@std@@QEAAPEAPEAVServerHandler@mu2@@_K@Z ; std::allocator<mu2::ServerHandler *>::allocate
  0017b	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1565 :         const pointer _Newvec         = _Allocate_at_least_helper(_Al, _Newcapacity);

  00183	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  0018b	48 89 44 24 28	 mov	 QWORD PTR _Newvec$[rsp], rax

; 1566 :         const pointer _Appended_first = _Newvec + _Oldsize;

  00190	48 8b 44 24 28	 mov	 rax, QWORD PTR _Newvec$[rsp]
  00195	48 8b 4c 24 60	 mov	 rcx, QWORD PTR _Oldsize$[rsp]
  0019a	48 8d 04 c8	 lea	 rax, QWORD PTR [rax+rcx*8]
  0019e	48 89 44 24 30	 mov	 QWORD PTR _Appended_first$[rsp], rax

; 1567 : 
; 1568 :         _Reallocation_guard _Guard{_Al, _Newvec, _Newcapacity, _Appended_first, _Appended_first};

  001a3	48 8b 44 24 20	 mov	 rax, QWORD PTR _Al$[rsp]
  001a8	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR _Guard$[rsp], rax
  001b0	48 8b 44 24 28	 mov	 rax, QWORD PTR _Newvec$[rsp]
  001b5	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR _Guard$[rsp+8], rax
  001bd	48 8b 44 24 38	 mov	 rax, QWORD PTR _Newcapacity$[rsp]
  001c2	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR _Guard$[rsp+16], rax
  001ca	48 8b 44 24 30	 mov	 rax, QWORD PTR _Appended_first$[rsp]
  001cf	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR _Guard$[rsp+24], rax
  001d7	48 8b 44 24 30	 mov	 rax, QWORD PTR _Appended_first$[rsp]
  001dc	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR _Guard$[rsp+32], rax

; 1569 :         auto& _Appended_last = _Guard._Constructed_last;

  001e4	48 8d 84 24 e8
	00 00 00	 lea	 rax, QWORD PTR _Guard$[rsp+32]
  001ec	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR _Appended_last$[rsp], rax

; 1570 : 
; 1571 :         if constexpr (is_same_v<_Ty2, _Ty>) {
; 1572 :             _Appended_last = _Uninitialized_fill_n(_Appended_first, _Newsize - _Oldsize, _Val, _Al);
; 1573 :         } else {
; 1574 :             _STL_INTERNAL_STATIC_ASSERT(is_same_v<_Ty2, _Value_init_tag>);
; 1575 :             _Appended_last = _Uninitialized_value_construct_n(_Appended_first, _Newsize - _Oldsize, _Al);

  001f4	48 8b 44 24 60	 mov	 rax, QWORD PTR _Oldsize$[rsp]
  001f9	48 8b 8c 24 18
	01 00 00	 mov	 rcx, QWORD PTR _Newsize$[rsp]
  00201	48 2b c8	 sub	 rcx, rax
  00204	48 8b c1	 mov	 rax, rcx
  00207	4c 8b 44 24 20	 mov	 r8, QWORD PTR _Al$[rsp]
  0020c	48 8b d0	 mov	 rdx, rax
  0020f	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Appended_first$[rsp]
  00214	e8 00 00 00 00	 call	 ??$_Uninitialized_value_construct_n@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@YAPEAPEAVServerHandler@mu2@@PEAPEAV12@_KAEAV?$allocator@PEAVServerHandler@mu2@@@0@@Z ; std::_Uninitialized_value_construct_n<std::allocator<mu2::ServerHandler *> >
  00219	48 8b 8c 24 c0
	00 00 00	 mov	 rcx, QWORD PTR _Appended_last$[rsp]
  00221	48 89 01	 mov	 QWORD PTR [rcx], rax

; 1576 :         }
; 1577 : 
; 1578 :         if constexpr (is_nothrow_move_constructible_v<_Ty> || !is_copy_constructible_v<_Ty>) {
; 1579 :             _Uninitialized_move(_Myfirst, _Mylast, _Newvec, _Al);

  00224	4c 8b 4c 24 20	 mov	 r9, QWORD PTR _Al$[rsp]
  00229	4c 8b 44 24 28	 mov	 r8, QWORD PTR _Newvec$[rsp]
  0022e	48 8b 44 24 68	 mov	 rax, QWORD PTR _Mylast$[rsp]
  00233	48 8b 10	 mov	 rdx, QWORD PTR [rax]
  00236	48 8b 44 24 70	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  0023b	48 8b 08	 mov	 rcx, QWORD PTR [rax]
  0023e	e8 00 00 00 00	 call	 ??$_Uninitialized_move@PEAPEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@YAPEAPEAVServerHandler@mu2@@QEAPEAV12@0PEAPEAV12@AEAV?$allocator@PEAVServerHandler@mu2@@@0@@Z ; std::_Uninitialized_move<mu2::ServerHandler * *,std::allocator<mu2::ServerHandler *> >
  00243	90		 npad	 1

; 1580 :         } else {
; 1581 :             _Uninitialized_copy(_Myfirst, _Mylast, _Newvec, _Al);
; 1582 :         }
; 1583 : 
; 1584 :         _Guard._New_begin = nullptr;

  00244	48 c7 84 24 d0
	00 00 00 00 00
	00 00		 mov	 QWORD PTR _Guard$[rsp+8], 0

; 1585 :         _Change_array(_Newvec, _Newsize, _Newcapacity);

  00250	4c 8b 4c 24 38	 mov	 r9, QWORD PTR _Newcapacity$[rsp]
  00255	4c 8b 84 24 18
	01 00 00	 mov	 r8, QWORD PTR _Newsize$[rsp]
  0025d	48 8b 54 24 28	 mov	 rdx, QWORD PTR _Newvec$[rsp]
  00262	48 8b 8c 24 10
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0026a	e8 00 00 00 00	 call	 ?_Change_array@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAXQEAPEAVServerHandler@mu2@@_K1@Z ; std::vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >::_Change_array
  0026f	90		 npad	 1

; 1586 :     }

  00270	48 8d 8c 24 c8
	00 00 00	 lea	 rcx, QWORD PTR _Guard$[rsp]
  00278	e8 00 00 00 00	 call	 ??1_Reallocation_guard@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAA@XZ ; std::vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >::_Reallocation_guard::~_Reallocation_guard
  0027d	90		 npad	 1
  0027e	48 81 c4 08 01
	00 00		 add	 rsp, 264		; 00000108H
  00285	c3		 ret	 0
$LN272@Resize_rea:
??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z ENDP ; std::vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >::_Resize_reallocate<std::_Value_init_tag>
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
_Al$ = 32
_Newvec$ = 40
_Appended_first$ = 48
_Newcapacity$ = 56
$T1 = 64
$T2 = 72
tv133 = 80
_My_data$ = 88
_Oldsize$ = 96
_Mylast$ = 104
_Myfirst$ = 112
$T3 = 120
$T4 = 128
$T5 = 136
$T6 = 144
$T7 = 152
$T8 = 160
$T9 = 168
$T10 = 176
$T11 = 184
_Appended_last$ = 192
_Guard$ = 200
$T12 = 240
_Unsigned_max$13 = 248
this$ = 272
_Newsize$ = 280
_Val$ = 288
?dtor$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA PROC ; `std::vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >::_Resize_reallocate<std::_Value_init_tag>'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8d 8d c8 00
	00 00		 lea	 rcx, QWORD PTR _Guard$[rbp]
  00010	e8 00 00 00 00	 call	 ??1_Reallocation_guard@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAA@XZ ; std::vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >::_Reallocation_guard::~_Reallocation_guard
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$0@?0???$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z@4HA ENDP ; `std::vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >::_Resize_reallocate<std::_Value_init_tag>'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ??$_Resize@U_Value_init_tag@std@@@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z
_TEXT	SEGMENT
_Mylast$ = 32
_My_data$ = 40
_Myfirst$ = 48
_Oldsize$ = 56
$T1 = 64
$T2 = 72
_Newlast$3 = 80
_Oldcapacity$4 = 88
_Al$ = 96
_Oldlast$5 = 104
_Last$ = 112
this$ = 144
_Newsize$ = 152
_Val$ = 160
??$_Resize@U_Value_init_tag@std@@@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z PROC ; std::vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >::_Resize<std::_Value_init_tag>, COMDAT

; 1589 :     _CONSTEXPR20 void _Resize(const size_type _Newsize, const _Ty2& _Val) {

$LN187:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H

; 2227 :         return _Mypair._Get_first();

  00016	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  0001e	48 89 44 24 40	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2227 :         return _Mypair._Get_first();

  00023	48 8b 44 24 40	 mov	 rax, QWORD PTR $T1[rsp]
  00028	48 89 44 24 48	 mov	 QWORD PTR $T2[rsp], rax

; 1590 :         // trim or append elements, provide strong guarantee
; 1591 :         auto& _Al           = _Getal();

  0002d	48 8b 44 24 48	 mov	 rax, QWORD PTR $T2[rsp]
  00032	48 89 44 24 60	 mov	 QWORD PTR _Al$[rsp], rax

; 1592 :         auto& _My_data      = _Mypair._Myval2;

  00037	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0003f	48 89 44 24 28	 mov	 QWORD PTR _My_data$[rsp], rax

; 1593 :         pointer& _Myfirst   = _My_data._Myfirst;

  00044	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00049	48 89 44 24 30	 mov	 QWORD PTR _Myfirst$[rsp], rax

; 1594 :         pointer& _Mylast    = _My_data._Mylast;

  0004e	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00053	48 83 c0 08	 add	 rax, 8
  00057	48 89 44 24 20	 mov	 QWORD PTR _Mylast$[rsp], rax

; 1595 :         const auto _Oldsize = static_cast<size_type>(_Mylast - _Myfirst);

  0005c	48 8b 44 24 20	 mov	 rax, QWORD PTR _Mylast$[rsp]
  00061	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Myfirst$[rsp]
  00066	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00069	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0006c	48 2b c1	 sub	 rax, rcx
  0006f	48 c1 f8 03	 sar	 rax, 3
  00073	48 89 44 24 38	 mov	 QWORD PTR _Oldsize$[rsp], rax

; 1596 :         if (_Newsize < _Oldsize) { // trim

  00078	48 8b 44 24 38	 mov	 rax, QWORD PTR _Oldsize$[rsp]
  0007d	48 39 84 24 98
	00 00 00	 cmp	 QWORD PTR _Newsize$[rsp], rax
  00085	73 38		 jae	 SHORT $LN2@Resize

; 1597 :             const pointer _Newlast = _Myfirst + _Newsize;

  00087	48 8b 44 24 30	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  0008c	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0008f	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR _Newsize$[rsp]
  00097	48 8d 04 c8	 lea	 rax, QWORD PTR [rax+rcx*8]
  0009b	48 89 44 24 50	 mov	 QWORD PTR _Newlast$3[rsp], rax

; 1598 :             _Orphan_range(_Newlast, _Mylast);
; 1599 :             _Destroy_range(_Newlast, _Mylast, _Al);

  000a0	48 8b 44 24 20	 mov	 rax, QWORD PTR _Mylast$[rsp]
  000a5	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000a8	48 89 44 24 70	 mov	 QWORD PTR _Last$[rsp], rax

; 1600 :             _ASAN_VECTOR_MODIFY(static_cast<difference_type>(_Newsize - _Oldsize));
; 1601 :             _Mylast = _Newlast;

  000ad	48 8b 44 24 20	 mov	 rax, QWORD PTR _Mylast$[rsp]
  000b2	48 8b 4c 24 50	 mov	 rcx, QWORD PTR _Newlast$3[rsp]
  000b7	48 89 08	 mov	 QWORD PTR [rax], rcx

; 1602 :             return;

  000ba	e9 99 00 00 00	 jmp	 $LN1@Resize
$LN2@Resize:

; 1603 :         }
; 1604 : 
; 1605 :         if (_Newsize > _Oldsize) { // append

  000bf	48 8b 44 24 38	 mov	 rax, QWORD PTR _Oldsize$[rsp]
  000c4	48 39 84 24 98
	00 00 00	 cmp	 QWORD PTR _Newsize$[rsp], rax
  000cc	0f 86 86 00 00
	00		 jbe	 $LN1@Resize

; 1606 :             const auto _Oldcapacity = static_cast<size_type>(_My_data._Myend - _Myfirst);

  000d2	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  000d7	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Myfirst$[rsp]
  000dc	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  000df	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  000e3	48 2b c1	 sub	 rax, rcx
  000e6	48 c1 f8 03	 sar	 rax, 3
  000ea	48 89 44 24 58	 mov	 QWORD PTR _Oldcapacity$4[rsp], rax

; 1607 :             if (_Newsize > _Oldcapacity) { // reallocate

  000ef	48 8b 44 24 58	 mov	 rax, QWORD PTR _Oldcapacity$4[rsp]
  000f4	48 39 84 24 98
	00 00 00	 cmp	 QWORD PTR _Newsize$[rsp], rax
  000fc	76 20		 jbe	 SHORT $LN4@Resize

; 1608 :                 _Resize_reallocate(_Newsize, _Val);

  000fe	4c 8b 84 24 a0
	00 00 00	 mov	 r8, QWORD PTR _Val$[rsp]
  00106	48 8b 94 24 98
	00 00 00	 mov	 rdx, QWORD PTR _Newsize$[rsp]
  0010e	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00116	e8 00 00 00 00	 call	 ??$_Resize_reallocate@U_Value_init_tag@std@@@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z ; std::vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >::_Resize_reallocate<std::_Value_init_tag>
  0011b	90		 npad	 1

; 1609 :                 return;

  0011c	eb 3a		 jmp	 SHORT $LN1@Resize
$LN4@Resize:

; 1610 :             }
; 1611 : 
; 1612 :             _ASAN_VECTOR_EXTEND_GUARD(_Newsize);
; 1613 :             const pointer _Oldlast = _Mylast;

  0011e	48 8b 44 24 20	 mov	 rax, QWORD PTR _Mylast$[rsp]
  00123	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00126	48 89 44 24 68	 mov	 QWORD PTR _Oldlast$5[rsp], rax

; 1614 :             if constexpr (is_same_v<_Ty2, _Ty>) {
; 1615 :                 _Mylast = _Uninitialized_fill_n(_Oldlast, _Newsize - _Oldsize, _Val, _Al);
; 1616 :             } else {
; 1617 :                 _STL_INTERNAL_STATIC_ASSERT(is_same_v<_Ty2, _Value_init_tag>);
; 1618 :                 _Mylast = _Uninitialized_value_construct_n(_Oldlast, _Newsize - _Oldsize, _Al);

  0012b	48 8b 44 24 38	 mov	 rax, QWORD PTR _Oldsize$[rsp]
  00130	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR _Newsize$[rsp]
  00138	48 2b c8	 sub	 rcx, rax
  0013b	48 8b c1	 mov	 rax, rcx
  0013e	4c 8b 44 24 60	 mov	 r8, QWORD PTR _Al$[rsp]
  00143	48 8b d0	 mov	 rdx, rax
  00146	48 8b 4c 24 68	 mov	 rcx, QWORD PTR _Oldlast$5[rsp]
  0014b	e8 00 00 00 00	 call	 ??$_Uninitialized_value_construct_n@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@YAPEAPEAVServerHandler@mu2@@PEAPEAV12@_KAEAV?$allocator@PEAVServerHandler@mu2@@@0@@Z ; std::_Uninitialized_value_construct_n<std::allocator<mu2::ServerHandler *> >
  00150	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Mylast$[rsp]
  00155	48 89 01	 mov	 QWORD PTR [rcx], rax
$LN1@Resize:

; 1619 :             }
; 1620 :             _ASAN_VECTOR_RELEASE_GUARD;
; 1621 :             _Orphan_range(_Oldlast, _Oldlast);
; 1622 :         }
; 1623 : 
; 1624 :         // if _Newsize == _Oldsize, do nothing; avoid invalidating iterators
; 1625 :     }

  00158	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  0015f	c3		 ret	 0
??$_Resize@U_Value_init_tag@std@@@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z ENDP ; std::vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >::_Resize<std::_Value_init_tag>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
_TEXT	SEGMENT
_Ptr_container$ = 48
_Block_size$ = 56
_Ptr$ = 64
$T1 = 72
_Bytes$ = 96
??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z PROC ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>, COMDAT

; 182  : __declspec(allocator) void* _Allocate_manually_vector_aligned(const size_t _Bytes) {

$LN7:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 183  :     // allocate _Bytes manually aligned to at least _Big_allocation_alignment
; 184  :     const size_t _Block_size = _Non_user_size + _Bytes;

  00009	48 8b 44 24 60	 mov	 rax, QWORD PTR _Bytes$[rsp]
  0000e	48 83 c0 27	 add	 rax, 39			; 00000027H
  00012	48 89 44 24 38	 mov	 QWORD PTR _Block_size$[rsp], rax

; 185  :     if (_Block_size <= _Bytes) {

  00017	48 8b 44 24 60	 mov	 rax, QWORD PTR _Bytes$[rsp]
  0001c	48 39 44 24 38	 cmp	 QWORD PTR _Block_size$[rsp], rax
  00021	77 06		 ja	 SHORT $LN2@Allocate_m

; 186  :         _Throw_bad_array_new_length(); // add overflow

  00023	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00028	90		 npad	 1
$LN2@Allocate_m:

; 136  :         return ::operator new(_Bytes);

  00029	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Block_size$[rsp]
  0002e	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00033	48 89 44 24 48	 mov	 QWORD PTR $T1[rsp], rax

; 187  :     }
; 188  : 
; 189  :     const uintptr_t _Ptr_container = reinterpret_cast<uintptr_t>(_Traits::_Allocate(_Block_size));

  00038	48 8b 44 24 48	 mov	 rax, QWORD PTR $T1[rsp]
  0003d	48 89 44 24 30	 mov	 QWORD PTR _Ptr_container$[rsp], rax

; 190  :     _STL_VERIFY(_Ptr_container != 0, "invalid argument"); // validate even in release since we're doing p[-1]

  00042	48 83 7c 24 30
	00		 cmp	 QWORD PTR _Ptr_container$[rsp], 0
  00048	75 19		 jne	 SHORT $LN3@Allocate_m
  0004a	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR [rsp+32], 0
  00053	45 33 c9	 xor	 r9d, r9d
  00056	45 33 c0	 xor	 r8d, r8d
  00059	33 d2		 xor	 edx, edx
  0005b	33 c9		 xor	 ecx, ecx
  0005d	e8 00 00 00 00	 call	 _invoke_watson
  00062	90		 npad	 1
$LN3@Allocate_m:

; 191  :     void* const _Ptr = reinterpret_cast<void*>((_Ptr_container + _Non_user_size) & ~(_Big_allocation_alignment - 1));

  00063	48 8b 44 24 30	 mov	 rax, QWORD PTR _Ptr_container$[rsp]
  00068	48 83 c0 27	 add	 rax, 39			; 00000027H
  0006c	48 83 e0 e0	 and	 rax, -32		; ffffffffffffffe0H
  00070	48 89 44 24 40	 mov	 QWORD PTR _Ptr$[rsp], rax

; 192  :     static_cast<uintptr_t*>(_Ptr)[-1] = _Ptr_container;

  00075	b8 08 00 00 00	 mov	 eax, 8
  0007a	48 6b c0 ff	 imul	 rax, rax, -1
  0007e	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  00083	48 8b 54 24 30	 mov	 rdx, QWORD PTR _Ptr_container$[rsp]
  00088	48 89 14 01	 mov	 QWORD PTR [rcx+rax], rdx

; 193  : 
; 194  : #ifdef _DEBUG
; 195  :     static_cast<uintptr_t*>(_Ptr)[-2] = _Big_allocation_sentinel;
; 196  : #endif // defined(_DEBUG)
; 197  :     return _Ptr;

  0008c	48 8b 44 24 40	 mov	 rax, QWORD PTR _Ptr$[rsp]
$LN4@Allocate_m:

; 198  : }

  00091	48 83 c4 58	 add	 rsp, 88			; 00000058H
  00095	c3		 ret	 0
??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ENDP ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h
;	COMDAT ??_GWorldRunner@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GWorldRunner@mu2@@UEAAPEAXI@Z PROC			; mu2::WorldRunner::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00012	e8 00 00 00 00	 call	 ??1WorldRunner@mu2@@UEAA@XZ ; mu2::WorldRunner::~WorldRunner
  00017	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  0001b	83 e0 01	 and	 eax, 1
  0001e	85 c0		 test	 eax, eax
  00020	74 28		 je	 SHORT $LN2@scalar
  00022	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00026	83 e0 04	 and	 eax, 4
  00029	85 c0		 test	 eax, eax
  0002b	75 0d		 jne	 SHORT $LN3@scalar

; 45   : 			::free(ptr);

  0002d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00032	e8 00 00 00 00	 call	 free
  00037	90		 npad	 1
  00038	eb 10		 jmp	 SHORT $LN2@scalar
$LN3@scalar:
  0003a	ba c0 01 00 00	 mov	 edx, 448		; 000001c0H
  0003f	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00044	e8 00 00 00 00	 call	 ?__global_delete@@YAXPEAX_K@Z ; __global_delete
  00049	90		 npad	 1
$LN2@scalar:
  0004a	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004f	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00053	c3		 ret	 0
??_GWorldRunner@mu2@@UEAAPEAXI@Z ENDP			; mu2::WorldRunner::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
;	COMDAT ?finish@WorldRunner@mu2@@EEAAXXZ
_TEXT	SEGMENT
i$1 = 32
length$ = 36
$T2 = 40
_Mylast$3 = 48
_Myfirst$4 = 56
_My_data$5 = 64
this$ = 72
_My_data$6 = 80
$T7 = 88
_My_data$8 = 96
$T9 = 104
tv87 = 112
_My_data$10 = 120
$T11 = 128
$T12 = 136
$T13 = 144
_Last$ = 152
_First$ = 160
this$ = 192
?finish@WorldRunner@mu2@@EEAAXXZ PROC			; mu2::WorldRunner::finish, COMDAT

; 82   : {

$LN49:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec b8 00
	00 00		 sub	 rsp, 184		; 000000b8H

; 83   : 	UInt32 length = Length<HandlerTypes>::value;

  0000c	c7 44 24 24 16
	00 00 00	 mov	 DWORD PTR length$[rsp], 22

; 84   : 	for ( UInt32 i = 0; i < length; ++i )

  00014	c7 44 24 20 00
	00 00 00	 mov	 DWORD PTR i$1[rsp], 0
  0001c	eb 0a		 jmp	 SHORT $LN4@finish
$LN2@finish:
  0001e	8b 44 24 20	 mov	 eax, DWORD PTR i$1[rsp]
  00022	ff c0		 inc	 eax
  00024	89 44 24 20	 mov	 DWORD PTR i$1[rsp], eax
$LN4@finish:
  00028	8b 44 24 24	 mov	 eax, DWORD PTR length$[rsp]
  0002c	39 44 24 20	 cmp	 DWORD PTR i$1[rsp], eax
  00030	0f 83 d6 00 00
	00		 jae	 $LN3@finish
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1928 :         auto& _My_data = _Mypair._Myval2;

  00036	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0003e	48 05 a0 01 00
	00		 add	 rax, 416		; 000001a0H
  00044	48 89 44 24 50	 mov	 QWORD PTR _My_data$6[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 86   : 		m_handlers[i]->Finish();

  00049	8b 44 24 20	 mov	 eax, DWORD PTR i$1[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1934 :         return _My_data._Myfirst[_Pos];

  0004d	48 8b 4c 24 50	 mov	 rcx, QWORD PTR _My_data$6[rsp]
  00052	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00055	48 8d 04 c1	 lea	 rax, QWORD PTR [rcx+rax*8]
  00059	48 89 44 24 58	 mov	 QWORD PTR $T7[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 86   : 		m_handlers[i]->Finish();

  0005e	48 8b 44 24 58	 mov	 rax, QWORD PTR $T7[rsp]
  00063	48 8b 08	 mov	 rcx, QWORD PTR [rax]
  00066	e8 00 00 00 00	 call	 ?Finish@ServerHandler@mu2@@QEAAXXZ ; mu2::ServerHandler::Finish
  0006b	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1928 :         auto& _My_data = _Mypair._Myval2;

  0006c	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00074	48 05 a0 01 00
	00		 add	 rax, 416		; 000001a0H
  0007a	48 89 44 24 60	 mov	 QWORD PTR _My_data$8[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 88   : 		delete m_handlers[i]; 

  0007f	8b 44 24 20	 mov	 eax, DWORD PTR i$1[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1934 :         return _My_data._Myfirst[_Pos];

  00083	48 8b 4c 24 60	 mov	 rcx, QWORD PTR _My_data$8[rsp]
  00088	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0008b	48 8d 04 c1	 lea	 rax, QWORD PTR [rcx+rax*8]
  0008f	48 89 44 24 68	 mov	 QWORD PTR $T9[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 88   : 		delete m_handlers[i]; 

  00094	48 8b 44 24 68	 mov	 rax, QWORD PTR $T9[rsp]
  00099	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0009c	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax
  000a1	48 83 7c 24 28
	00		 cmp	 QWORD PTR $T2[rsp], 0
  000a7	74 1b		 je	 SHORT $LN6@finish
  000a9	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
  000ae	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000b1	ba 01 00 00 00	 mov	 edx, 1
  000b6	48 8b 4c 24 28	 mov	 rcx, QWORD PTR $T2[rsp]
  000bb	ff 10		 call	 QWORD PTR [rax]
  000bd	48 89 44 24 70	 mov	 QWORD PTR tv87[rsp], rax
  000c2	eb 09		 jmp	 SHORT $LN7@finish
$LN6@finish:
  000c4	48 c7 44 24 70
	00 00 00 00	 mov	 QWORD PTR tv87[rsp], 0
$LN7@finish:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1928 :         auto& _My_data = _Mypair._Myval2;

  000cd	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000d5	48 05 a0 01 00
	00		 add	 rax, 416		; 000001a0H
  000db	48 89 44 24 78	 mov	 QWORD PTR _My_data$10[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 90   : 		m_handlers[i] = nullptr;

  000e0	8b 44 24 20	 mov	 eax, DWORD PTR i$1[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1934 :         return _My_data._Myfirst[_Pos];

  000e4	48 8b 4c 24 78	 mov	 rcx, QWORD PTR _My_data$10[rsp]
  000e9	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  000ec	48 8d 04 c1	 lea	 rax, QWORD PTR [rcx+rax*8]
  000f0	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 90   : 		m_handlers[i] = nullptr;

  000f8	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  00100	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0

; 91   : 	}

  00107	e9 12 ff ff ff	 jmp	 $LN2@finish
$LN3@finish:

; 92   : 
; 93   : 	m_handlers.clear();

  0010c	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00114	48 05 a0 01 00
	00		 add	 rax, 416		; 000001a0H
  0011a	48 89 44 24 48	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1808 :         auto& _My_data    = _Mypair._Myval2;

  0011f	48 8b 44 24 48	 mov	 rax, QWORD PTR this$[rsp]
  00124	48 89 44 24 40	 mov	 QWORD PTR _My_data$5[rsp], rax

; 1809 :         pointer& _Myfirst = _My_data._Myfirst;

  00129	48 8b 44 24 40	 mov	 rax, QWORD PTR _My_data$5[rsp]
  0012e	48 89 44 24 38	 mov	 QWORD PTR _Myfirst$4[rsp], rax

; 1810 :         pointer& _Mylast  = _My_data._Mylast;

  00133	48 8b 44 24 40	 mov	 rax, QWORD PTR _My_data$5[rsp]
  00138	48 83 c0 08	 add	 rax, 8
  0013c	48 89 44 24 30	 mov	 QWORD PTR _Mylast$3[rsp], rax

; 1811 : 
; 1812 :         if (_Myfirst == _Mylast) { // already empty, nothing to do

  00141	48 8b 44 24 38	 mov	 rax, QWORD PTR _Myfirst$4[rsp]
  00146	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Mylast$3[rsp]
  0014b	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0014e	48 39 08	 cmp	 QWORD PTR [rax], rcx
  00151	75 02		 jne	 SHORT $LN24@finish

; 1813 :             // This is an optimization for debug mode: we can avoid taking the debug lock to invalidate iterators.
; 1814 :             // Note that when clearing an empty vector, this will preserve past-the-end iterators, which is allowed by
; 1815 :             // N4950 [sequence.reqmts]/54 "a.clear() [...] may invalidate the past-the-end iterator".
; 1816 :             return;

  00153	eb 4d		 jmp	 SHORT $LN23@finish
$LN24@finish:

; 2227 :         return _Mypair._Get_first();

  00155	48 8b 44 24 48	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  0015a	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2227 :         return _Mypair._Get_first();

  00162	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
  0016a	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax

; 1820 :         _Destroy_range(_Myfirst, _Mylast, _Getal());

  00172	48 8b 44 24 30	 mov	 rax, QWORD PTR _Mylast$3[rsp]
  00177	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0017a	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR _Last$[rsp], rax
  00182	48 8b 44 24 38	 mov	 rax, QWORD PTR _Myfirst$4[rsp]
  00187	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0018a	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR _First$[rsp], rax

; 1821 :         _ASAN_VECTOR_MODIFY(static_cast<difference_type>(_Myfirst - _Mylast)); // negative when destroying elements
; 1822 :         _Mylast = _Myfirst;

  00192	48 8b 44 24 30	 mov	 rax, QWORD PTR _Mylast$3[rsp]
  00197	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Myfirst$4[rsp]
  0019c	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0019f	48 89 08	 mov	 QWORD PTR [rax], rcx
$LN23@finish:
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 94   : }

  001a2	48 81 c4 b8 00
	00 00		 add	 rsp, 184		; 000000b8H
  001a9	c3		 ret	 0
?finish@WorldRunner@mu2@@EEAAXXZ ENDP			; mu2::WorldRunner::finish
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.h
;	COMDAT ?dispatch@WorldRunner@mu2@@EEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z
_TEXT	SEGMENT
this$ = 8
__formal$ = 16
?dispatch@WorldRunner@mu2@@EEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z PROC ; mu2::WorldRunner::dispatch, COMDAT

; 122  : 	void dispatch(EventPtr&) {}

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?dispatch@WorldRunner@mu2@@EEAAXAEAV?$SmartPtrEx@UEvent@mu2@@@2@@Z ENDP ; mu2::WorldRunner::dispatch
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
;	COMDAT ?update@WorldRunner@mu2@@EEAAXXZ
_TEXT	SEGMENT
$T1 = 32
this$ = 64
?update@WorldRunner@mu2@@EEAAXXZ PROC			; mu2::WorldRunner::update, COMDAT

; 70   : {

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  00009	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VtheServerUnionHandler@mu2@@@mu2@@1VtheServerUnionHandler@2@A ; mu2::ISingleton<mu2::theServerUnionHandler>::inst
  00010	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 77   : 	theServerUnionHandler::GetInstance().Update();

  00015	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  0001a	48 8b c8	 mov	 rcx, rax
  0001d	e8 00 00 00 00	 call	 ?Update@theServerUnionHandler@mu2@@QEAAXXZ ; mu2::theServerUnionHandler::Update
  00022	90		 npad	 1

; 78   : 
; 79   : }

  00023	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00027	c3		 ret	 0
?update@WorldRunner@mu2@@EEAAXXZ ENDP			; mu2::WorldRunner::update
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
;	COMDAT ?initialize@WorldRunner@mu2@@EEAA_NXZ
_TEXT	SEGMENT
i$1 = 32
length$ = 36
_My_data$2 = 40
$T3 = 48
this$ = 80
?initialize@WorldRunner@mu2@@EEAA_NXZ PROC		; mu2::WorldRunner::initialize, COMDAT

; 58   : {

$LN11:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 59   : 	UInt32 length = Length<HandlerTypes>::value;

  00009	c7 44 24 24 16
	00 00 00	 mov	 DWORD PTR length$[rsp], 22

; 60   : 	for ( UInt32 i = 0; i < length; ++i )

  00011	c7 44 24 20 00
	00 00 00	 mov	 DWORD PTR i$1[rsp], 0
  00019	eb 0a		 jmp	 SHORT $LN4@initialize
$LN2@initialize:
  0001b	8b 44 24 20	 mov	 eax, DWORD PTR i$1[rsp]
  0001f	ff c0		 inc	 eax
  00021	89 44 24 20	 mov	 DWORD PTR i$1[rsp], eax
$LN4@initialize:
  00025	8b 44 24 24	 mov	 eax, DWORD PTR length$[rsp]
  00029	39 44 24 20	 cmp	 DWORD PTR i$1[rsp], eax
  0002d	73 35		 jae	 SHORT $LN3@initialize
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1928 :         auto& _My_data = _Mypair._Myval2;

  0002f	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00034	48 05 a0 01 00
	00		 add	 rax, 416		; 000001a0H
  0003a	48 89 44 24 28	 mov	 QWORD PTR _My_data$2[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 62   : 		m_handlers[i]->Initialize();

  0003f	8b 44 24 20	 mov	 eax, DWORD PTR i$1[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1934 :         return _My_data._Myfirst[_Pos];

  00043	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _My_data$2[rsp]
  00048	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0004b	48 8d 04 c1	 lea	 rax, QWORD PTR [rcx+rax*8]
  0004f	48 89 44 24 30	 mov	 QWORD PTR $T3[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 62   : 		m_handlers[i]->Initialize();

  00054	48 8b 44 24 30	 mov	 rax, QWORD PTR $T3[rsp]
  00059	48 8b 08	 mov	 rcx, QWORD PTR [rax]
  0005c	e8 00 00 00 00	 call	 ?Initialize@ServerHandler@mu2@@QEAA_NXZ ; mu2::ServerHandler::Initialize
  00061	90		 npad	 1

; 63   : 	}

  00062	eb b7		 jmp	 SHORT $LN2@initialize
$LN3@initialize:

; 64   : 
; 65   : 	return true;

  00064	b0 01		 mov	 al, 1

; 66   : }

  00066	48 83 c4 48	 add	 rsp, 72			; 00000048H
  0006a	c3		 ret	 0
?initialize@WorldRunner@mu2@@EEAA_NXZ ENDP		; mu2::WorldRunner::initialize
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
;	COMDAT ??1WorldRunner@mu2@@UEAA@XZ
_TEXT	SEGMENT
i$1 = 32
$T2 = 40
_My_data$3 = 48
$T4 = 56
_My_data$5 = 64
$T6 = 72
_My_data$7 = 80
$T8 = 88
tv140 = 96
_My_data$9 = 104
$T10 = 112
this$ = 144
??1WorldRunner@mu2@@UEAA@XZ PROC			; mu2::WorldRunner::~WorldRunner, COMDAT

; 45   : {

$LN76:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H
  0000c	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7WorldRunner@mu2@@6BServerHandler@1@@
  0001b	48 89 08	 mov	 QWORD PTR [rax], rcx
  0001e	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00026	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7WorldRunner@mu2@@6BThread@1@@
  0002d	48 89 48 40	 mov	 QWORD PTR [rax+64], rcx

; 46   : 	for ( UInt32 i = 0; i < m_handlers.size(); ++i )

  00031	c7 44 24 20 00
	00 00 00	 mov	 DWORD PTR i$1[rsp], 0
  00039	eb 0a		 jmp	 SHORT $LN4@WorldRunne
$LN2@WorldRunne:
  0003b	8b 44 24 20	 mov	 eax, DWORD PTR i$1[rsp]
  0003f	ff c0		 inc	 eax
  00041	89 44 24 20	 mov	 DWORD PTR i$1[rsp], eax
$LN4@WorldRunne:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1914 :         auto& _My_data = _Mypair._Myval2;

  00045	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0004d	48 05 a0 01 00
	00		 add	 rax, 416		; 000001a0H
  00053	48 89 44 24 30	 mov	 QWORD PTR _My_data$3[rsp], rax

; 1915 :         return static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst);

  00058	48 8b 44 24 30	 mov	 rax, QWORD PTR _My_data$3[rsp]
  0005d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _My_data$3[rsp]
  00062	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00065	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00069	48 2b c1	 sub	 rax, rcx
  0006c	48 c1 f8 03	 sar	 rax, 3
  00070	48 89 44 24 38	 mov	 QWORD PTR $T4[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 46   : 	for ( UInt32 i = 0; i < m_handlers.size(); ++i )

  00075	8b 44 24 20	 mov	 eax, DWORD PTR i$1[rsp]
  00079	48 8b 4c 24 38	 mov	 rcx, QWORD PTR $T4[rsp]
  0007e	48 3b c1	 cmp	 rax, rcx
  00081	0f 83 d1 00 00
	00		 jae	 $LN3@WorldRunne
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1928 :         auto& _My_data = _Mypair._Myval2;

  00087	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0008f	48 05 a0 01 00
	00		 add	 rax, 416		; 000001a0H
  00095	48 89 44 24 40	 mov	 QWORD PTR _My_data$5[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 48   : 		if ( m_handlers[i] != nullptr )

  0009a	8b 44 24 20	 mov	 eax, DWORD PTR i$1[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1934 :         return _My_data._Myfirst[_Pos];

  0009e	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _My_data$5[rsp]
  000a3	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  000a6	48 8d 04 c1	 lea	 rax, QWORD PTR [rcx+rax*8]
  000aa	48 89 44 24 48	 mov	 QWORD PTR $T6[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 48   : 		if ( m_handlers[i] != nullptr )

  000af	48 8b 44 24 48	 mov	 rax, QWORD PTR $T6[rsp]
  000b4	48 83 38 00	 cmp	 QWORD PTR [rax], 0
  000b8	0f 84 95 00 00
	00		 je	 $LN5@WorldRunne
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1928 :         auto& _My_data = _Mypair._Myval2;

  000be	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000c6	48 05 a0 01 00
	00		 add	 rax, 416		; 000001a0H
  000cc	48 89 44 24 50	 mov	 QWORD PTR _My_data$7[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 50   : 			delete m_handlers[i];

  000d1	8b 44 24 20	 mov	 eax, DWORD PTR i$1[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1934 :         return _My_data._Myfirst[_Pos];

  000d5	48 8b 4c 24 50	 mov	 rcx, QWORD PTR _My_data$7[rsp]
  000da	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  000dd	48 8d 04 c1	 lea	 rax, QWORD PTR [rcx+rax*8]
  000e1	48 89 44 24 58	 mov	 QWORD PTR $T8[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 50   : 			delete m_handlers[i];

  000e6	48 8b 44 24 58	 mov	 rax, QWORD PTR $T8[rsp]
  000eb	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000ee	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax
  000f3	48 83 7c 24 28
	00		 cmp	 QWORD PTR $T2[rsp], 0
  000f9	74 1b		 je	 SHORT $LN7@WorldRunne
  000fb	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
  00100	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00103	ba 01 00 00 00	 mov	 edx, 1
  00108	48 8b 4c 24 28	 mov	 rcx, QWORD PTR $T2[rsp]
  0010d	ff 10		 call	 QWORD PTR [rax]
  0010f	48 89 44 24 60	 mov	 QWORD PTR tv140[rsp], rax
  00114	eb 09		 jmp	 SHORT $LN8@WorldRunne
$LN7@WorldRunne:
  00116	48 c7 44 24 60
	00 00 00 00	 mov	 QWORD PTR tv140[rsp], 0
$LN8@WorldRunne:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1928 :         auto& _My_data = _Mypair._Myval2;

  0011f	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00127	48 05 a0 01 00
	00		 add	 rax, 416		; 000001a0H
  0012d	48 89 44 24 68	 mov	 QWORD PTR _My_data$9[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 51   : 			m_handlers[i] = nullptr;

  00132	8b 44 24 20	 mov	 eax, DWORD PTR i$1[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1934 :         return _My_data._Myfirst[_Pos];

  00136	48 8b 4c 24 68	 mov	 rcx, QWORD PTR _My_data$9[rsp]
  0013b	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0013e	48 8d 04 c1	 lea	 rax, QWORD PTR [rcx+rax*8]
  00142	48 89 44 24 70	 mov	 QWORD PTR $T10[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 51   : 			m_handlers[i] = nullptr;

  00147	48 8b 44 24 70	 mov	 rax, QWORD PTR $T10[rsp]
  0014c	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0
$LN5@WorldRunne:

; 52   : 		}
; 53   : 	}

  00153	e9 e3 fe ff ff	 jmp	 $LN2@WorldRunne
$LN3@WorldRunne:

; 54   : }

  00158	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00160	48 05 a0 01 00
	00		 add	 rax, 416		; 000001a0H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 830  :         _Tidy();

  00166	48 8b c8	 mov	 rcx, rax
  00169	e8 00 00 00 00	 call	 ?_Tidy@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAXXZ ; std::vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >::_Tidy
  0016e	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 54   : }

  0016f	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00177	e8 00 00 00 00	 call	 ??1ServerTask@mu2@@UEAA@XZ ; mu2::ServerTask::~ServerTask
  0017c	90		 npad	 1
  0017d	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  00184	c3		 ret	 0
??1WorldRunner@mu2@@UEAA@XZ ENDP			; mu2::WorldRunner::~WorldRunner
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
;	COMDAT ??0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z
_TEXT	SEGMENT
$T1 = 32
$T2 = 33
$T3 = 40
tv145 = 48
$T4 = 56
tv164 = 64
$T5 = 72
tv183 = 80
$T6 = 88
tv202 = 96
$T7 = 104
tv221 = 112
$T8 = 120
tv240 = 128
$T9 = 136
tv259 = 144
$T10 = 152
tv278 = 160
$T11 = 168
tv297 = 176
$T12 = 184
tv316 = 192
$T13 = 200
tv335 = 208
$T14 = 216
tv354 = 224
$T15 = 232
tv373 = 240
$T16 = 248
tv392 = 256
$T17 = 264
tv411 = 272
$T18 = 280
tv462 = 288
$T19 = 296
tv481 = 304
$T20 = 312
tv500 = 320
$T21 = 328
tv519 = 336
$T22 = 344
tv538 = 352
$T23 = 360
tv557 = 368
$T24 = 376
tv576 = 384
this$ = 392
this$ = 400
$T25 = 408
$T26 = 416
_My_data$27 = 424
$T28 = 432
$T29 = 440
$T30 = 448
$T31 = 456
_My_data$32 = 464
$T33 = 472
$T34 = 480
$T35 = 488
$T36 = 496
_My_data$37 = 504
$T38 = 512
$T39 = 520
$T40 = 528
$T41 = 536
_My_data$42 = 544
$T43 = 552
$T44 = 560
$T45 = 568
$T46 = 576
_My_data$47 = 584
$T48 = 592
$T49 = 600
$T50 = 608
$T51 = 616
_My_data$52 = 624
$T53 = 632
$T54 = 640
$T55 = 648
$T56 = 656
_My_data$57 = 664
$T58 = 672
$T59 = 680
$T60 = 688
$T61 = 696
_My_data$62 = 704
$T63 = 712
$T64 = 720
$T65 = 728
$T66 = 736
_My_data$67 = 744
$T68 = 752
$T69 = 760
$T70 = 768
$T71 = 776
_My_data$72 = 784
$T73 = 792
$T74 = 800
$T75 = 808
$T76 = 816
_My_data$77 = 824
$T78 = 832
$T79 = 840
$T80 = 848
$T81 = 856
_My_data$82 = 864
$T83 = 872
$T84 = 880
$T85 = 888
$T86 = 896
_My_data$87 = 904
$T88 = 912
$T89 = 920
$T90 = 928
$T91 = 936
_My_data$92 = 944
$T93 = 952
$T94 = 960
$T95 = 968
$T96 = 976
_My_data$97 = 984
$T98 = 992
$T99 = 1000
$T100 = 1008
$T101 = 1016
_My_data$102 = 1024
$T103 = 1032
$T104 = 1040
$T105 = 1048
$T106 = 1056
_My_data$107 = 1064
$T108 = 1072
$T109 = 1080
$T110 = 1088
$T111 = 1096
_My_data$112 = 1104
$T113 = 1112
$T114 = 1120
$T115 = 1128
$T116 = 1136
_My_data$117 = 1144
$T118 = 1152
$T119 = 1160
$T120 = 1168
$T121 = 1176
_My_data$122 = 1184
$T123 = 1192
$T124 = 1200
$T125 = 1208
$T126 = 1216
_My_data$127 = 1224
$T128 = 1232
$T129 = 1240
$T130 = 1248
$T131 = 1256
_My_data$132 = 1264
$T133 = 1272
$T134 = 1280
this$ = 1312
thisServer$ = 1320
??0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z PROC		; mu2::WorldRunner::WorldRunner, COMDAT

; 11   : {

$LN291:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 81 ec 10 05
	00 00		 sub	 rsp, 1296		; 00000510H

; 10   : : ServerTask(thisServer, "WorldRunner")

  00012	4c 8d 05 00 00
	00 00		 lea	 r8, OFFSET FLAT:??_C@_0M@FCOEFOPG@WorldRunner@
  00019	48 8b 94 24 28
	05 00 00	 mov	 rdx, QWORD PTR thisServer$[rsp]
  00021	48 8b 8c 24 20
	05 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00029	e8 00 00 00 00	 call	 ??0ServerTask@mu2@@QEAA@PEAVServer@1@PEBD@Z ; mu2::ServerTask::ServerTask
  0002e	90		 npad	 1

; 11   : {

  0002f	48 8b 84 24 20
	05 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00037	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7WorldRunner@mu2@@6BServerHandler@1@@
  0003e	48 89 08	 mov	 QWORD PTR [rax], rcx
  00041	48 8b 84 24 20
	05 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00049	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7WorldRunner@mu2@@6BThread@1@@
  00050	48 89 48 40	 mov	 QWORD PTR [rax+64], rcx
  00054	48 8b 84 24 20
	05 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0005c	48 05 a0 01 00
	00		 add	 rax, 416		; 000001a0H
  00062	48 89 84 24 88
	01 00 00	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 670  :     _CONSTEXPR20 vector() noexcept(is_nothrow_default_constructible_v<_Alty>) : _Mypair(_Zero_then_variadic_args_t{}) {

  0006a	48 8b 84 24 88
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00072	48 89 84 24 90
	01 00 00	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1531 :         : _Ty1(), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  0007a	48 8b 84 24 90
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00082	48 8b c8	 mov	 rcx, rax
  00085	e8 00 00 00 00	 call	 ??0?$_Vector_val@U?$_Simple_types@PEAVServerHandler@mu2@@@std@@@std@@QEAA@XZ ; std::_Vector_val<std::_Simple_types<mu2::ServerHandler *> >::_Vector_val<std::_Simple_types<mu2::ServerHandler *> >
  0008a	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 671  :         _Mypair._Myval2._Alloc_proxy(_GET_PROXY_ALLOCATOR(_Alty, _Getal()));

  0008b	48 8d 44 24 20	 lea	 rax, QWORD PTR $T1[rsp]
  00090	48 8b f8	 mov	 rdi, rax
  00093	33 c0		 xor	 eax, eax
  00095	b9 01 00 00 00	 mov	 ecx, 1
  0009a	f3 aa		 rep stosb
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 12   : 	m_handlers.resize(Length<HandlerTypes>::value);

  0009c	48 8b 84 24 20
	05 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000a4	48 05 a0 01 00
	00		 add	 rax, 416		; 000001a0H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1630 :         _Resize(_Newsize, _Value_init_tag{});

  000aa	4c 8d 44 24 21	 lea	 r8, QWORD PTR $T2[rsp]
  000af	ba 16 00 00 00	 mov	 edx, 22
  000b4	48 8b c8	 mov	 rcx, rax
  000b7	e8 00 00 00 00	 call	 ??$_Resize@U_Value_init_tag@std@@@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAX_KAEBU_Value_init_tag@1@@Z ; std::vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >::_Resize<std::_Value_init_tag>
  000bc	90		 npad	 1
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h

; 26   : 			return malloc(size);

  000bd	b9 88 00 00 00	 mov	 ecx, 136		; 00000088H
  000c2	e8 00 00 00 00	 call	 malloc
  000c7	48 89 84 24 98
	01 00 00	 mov	 QWORD PTR $T25[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h

; 26   : 		return Alloc::allocate(size);

  000cf	48 8b 84 24 98
	01 00 00	 mov	 rax, QWORD PTR $T25[rsp]
  000d7	48 89 84 24 a0
	01 00 00	 mov	 QWORD PTR $T26[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 14   : 	m_handlers[IndexOf<HandlerTypes, WorldHandler>::value]				= new WorldHandler(thisServer);

  000df	48 8b 84 24 a0
	01 00 00	 mov	 rax, QWORD PTR $T26[rsp]
  000e7	48 89 44 24 28	 mov	 QWORD PTR $T3[rsp], rax
  000ec	48 83 7c 24 28
	00		 cmp	 QWORD PTR $T3[rsp], 0
  000f2	74 19		 je	 SHORT $LN3@WorldRunne
  000f4	48 8b 94 24 28
	05 00 00	 mov	 rdx, QWORD PTR thisServer$[rsp]
  000fc	48 8b 4c 24 28	 mov	 rcx, QWORD PTR $T3[rsp]
  00101	e8 00 00 00 00	 call	 ??0WorldHandler@mu2@@QEAA@PEAVServer@1@@Z ; mu2::WorldHandler::WorldHandler
  00106	48 89 44 24 30	 mov	 QWORD PTR tv145[rsp], rax
  0010b	eb 09		 jmp	 SHORT $LN4@WorldRunne
$LN3@WorldRunne:
  0010d	48 c7 44 24 30
	00 00 00 00	 mov	 QWORD PTR tv145[rsp], 0
$LN4@WorldRunne:
  00116	48 8b 44 24 30	 mov	 rax, QWORD PTR tv145[rsp]
  0011b	48 89 84 24 b8
	01 00 00	 mov	 QWORD PTR $T29[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1928 :         auto& _My_data = _Mypair._Myval2;

  00123	48 8b 84 24 20
	05 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0012b	48 05 a0 01 00
	00		 add	 rax, 416		; 000001a0H
  00131	48 89 84 24 a8
	01 00 00	 mov	 QWORD PTR _My_data$27[rsp], rax

; 1929 : #if _MSVC_STL_HARDENING_VECTOR || _ITERATOR_DEBUG_LEVEL != 0
; 1930 :         _STL_VERIFY(
; 1931 :             _Pos < static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst), "vector subscript out of range");
; 1932 : #endif
; 1933 : 
; 1934 :         return _My_data._Myfirst[_Pos];

  00139	33 c0		 xor	 eax, eax
  0013b	48 6b c0 08	 imul	 rax, rax, 8
  0013f	48 8b 8c 24 a8
	01 00 00	 mov	 rcx, QWORD PTR _My_data$27[rsp]
  00147	48 03 01	 add	 rax, QWORD PTR [rcx]
  0014a	48 89 84 24 b0
	01 00 00	 mov	 QWORD PTR $T28[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 14   : 	m_handlers[IndexOf<HandlerTypes, WorldHandler>::value]				= new WorldHandler(thisServer);

  00152	48 8b 84 24 b0
	01 00 00	 mov	 rax, QWORD PTR $T28[rsp]
  0015a	48 8b 8c 24 b8
	01 00 00	 mov	 rcx, QWORD PTR $T29[rsp]
  00162	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h

; 26   : 			return malloc(size);

  00165	b9 08 01 00 00	 mov	 ecx, 264		; 00000108H
  0016a	e8 00 00 00 00	 call	 malloc
  0016f	48 89 84 24 c0
	01 00 00	 mov	 QWORD PTR $T30[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h

; 26   : 		return Alloc::allocate(size);

  00177	48 8b 84 24 c0
	01 00 00	 mov	 rax, QWORD PTR $T30[rsp]
  0017f	48 89 84 24 c8
	01 00 00	 mov	 QWORD PTR $T31[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 15   : 	m_handlers[IndexOf<HandlerTypes, PlayerHandler>::value]				= new PlayerHandler(thisServer);

  00187	48 8b 84 24 c8
	01 00 00	 mov	 rax, QWORD PTR $T31[rsp]
  0018f	48 89 44 24 38	 mov	 QWORD PTR $T4[rsp], rax
  00194	48 83 7c 24 38
	00		 cmp	 QWORD PTR $T4[rsp], 0
  0019a	74 19		 je	 SHORT $LN5@WorldRunne
  0019c	48 8b 94 24 28
	05 00 00	 mov	 rdx, QWORD PTR thisServer$[rsp]
  001a4	48 8b 4c 24 38	 mov	 rcx, QWORD PTR $T4[rsp]
  001a9	e8 00 00 00 00	 call	 ??0PlayerHandler@mu2@@QEAA@PEAVServer@1@@Z ; mu2::PlayerHandler::PlayerHandler
  001ae	48 89 44 24 40	 mov	 QWORD PTR tv164[rsp], rax
  001b3	eb 09		 jmp	 SHORT $LN6@WorldRunne
$LN5@WorldRunne:
  001b5	48 c7 44 24 40
	00 00 00 00	 mov	 QWORD PTR tv164[rsp], 0
$LN6@WorldRunne:
  001be	48 8b 44 24 40	 mov	 rax, QWORD PTR tv164[rsp]
  001c3	48 89 84 24 e0
	01 00 00	 mov	 QWORD PTR $T34[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1928 :         auto& _My_data = _Mypair._Myval2;

  001cb	48 8b 84 24 20
	05 00 00	 mov	 rax, QWORD PTR this$[rsp]
  001d3	48 05 a0 01 00
	00		 add	 rax, 416		; 000001a0H
  001d9	48 89 84 24 d0
	01 00 00	 mov	 QWORD PTR _My_data$32[rsp], rax

; 1929 : #if _MSVC_STL_HARDENING_VECTOR || _ITERATOR_DEBUG_LEVEL != 0
; 1930 :         _STL_VERIFY(
; 1931 :             _Pos < static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst), "vector subscript out of range");
; 1932 : #endif
; 1933 : 
; 1934 :         return _My_data._Myfirst[_Pos];

  001e1	b8 01 00 00 00	 mov	 eax, 1
  001e6	48 6b c0 08	 imul	 rax, rax, 8
  001ea	48 8b 8c 24 d0
	01 00 00	 mov	 rcx, QWORD PTR _My_data$32[rsp]
  001f2	48 03 01	 add	 rax, QWORD PTR [rcx]
  001f5	48 89 84 24 d8
	01 00 00	 mov	 QWORD PTR $T33[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 15   : 	m_handlers[IndexOf<HandlerTypes, PlayerHandler>::value]				= new PlayerHandler(thisServer);

  001fd	48 8b 84 24 d8
	01 00 00	 mov	 rax, QWORD PTR $T33[rsp]
  00205	48 8b 8c 24 e0
	01 00 00	 mov	 rcx, QWORD PTR $T34[rsp]
  0020d	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h

; 26   : 			return malloc(size);

  00210	b9 d8 00 00 00	 mov	 ecx, 216		; 000000d8H
  00215	e8 00 00 00 00	 call	 malloc
  0021a	48 89 84 24 e8
	01 00 00	 mov	 QWORD PTR $T35[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h

; 26   : 		return Alloc::allocate(size);

  00222	48 8b 84 24 e8
	01 00 00	 mov	 rax, QWORD PTR $T35[rsp]
  0022a	48 89 84 24 f0
	01 00 00	 mov	 QWORD PTR $T36[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 16   : 	m_handlers[IndexOf<HandlerTypes, PartyHandler>::value]				= new PartyHandler(thisServer);

  00232	48 8b 84 24 f0
	01 00 00	 mov	 rax, QWORD PTR $T36[rsp]
  0023a	48 89 44 24 48	 mov	 QWORD PTR $T5[rsp], rax
  0023f	48 83 7c 24 48
	00		 cmp	 QWORD PTR $T5[rsp], 0
  00245	74 19		 je	 SHORT $LN7@WorldRunne
  00247	48 8b 94 24 28
	05 00 00	 mov	 rdx, QWORD PTR thisServer$[rsp]
  0024f	48 8b 4c 24 48	 mov	 rcx, QWORD PTR $T5[rsp]
  00254	e8 00 00 00 00	 call	 ??0PartyHandler@mu2@@QEAA@PEAVServer@1@@Z ; mu2::PartyHandler::PartyHandler
  00259	48 89 44 24 50	 mov	 QWORD PTR tv183[rsp], rax
  0025e	eb 09		 jmp	 SHORT $LN8@WorldRunne
$LN7@WorldRunne:
  00260	48 c7 44 24 50
	00 00 00 00	 mov	 QWORD PTR tv183[rsp], 0
$LN8@WorldRunne:
  00269	48 8b 44 24 50	 mov	 rax, QWORD PTR tv183[rsp]
  0026e	48 89 84 24 08
	02 00 00	 mov	 QWORD PTR $T39[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1928 :         auto& _My_data = _Mypair._Myval2;

  00276	48 8b 84 24 20
	05 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0027e	48 05 a0 01 00
	00		 add	 rax, 416		; 000001a0H
  00284	48 89 84 24 f8
	01 00 00	 mov	 QWORD PTR _My_data$37[rsp], rax

; 1929 : #if _MSVC_STL_HARDENING_VECTOR || _ITERATOR_DEBUG_LEVEL != 0
; 1930 :         _STL_VERIFY(
; 1931 :             _Pos < static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst), "vector subscript out of range");
; 1932 : #endif
; 1933 : 
; 1934 :         return _My_data._Myfirst[_Pos];

  0028c	b8 02 00 00 00	 mov	 eax, 2
  00291	48 6b c0 08	 imul	 rax, rax, 8
  00295	48 8b 8c 24 f8
	01 00 00	 mov	 rcx, QWORD PTR _My_data$37[rsp]
  0029d	48 03 01	 add	 rax, QWORD PTR [rcx]
  002a0	48 89 84 24 00
	02 00 00	 mov	 QWORD PTR $T38[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 16   : 	m_handlers[IndexOf<HandlerTypes, PartyHandler>::value]				= new PartyHandler(thisServer);

  002a8	48 8b 84 24 00
	02 00 00	 mov	 rax, QWORD PTR $T38[rsp]
  002b0	48 8b 8c 24 08
	02 00 00	 mov	 rcx, QWORD PTR $T39[rsp]
  002b8	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h

; 26   : 			return malloc(size);

  002bb	b9 38 01 00 00	 mov	 ecx, 312		; 00000138H
  002c0	e8 00 00 00 00	 call	 malloc
  002c5	48 89 84 24 10
	02 00 00	 mov	 QWORD PTR $T40[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h

; 26   : 		return Alloc::allocate(size);

  002cd	48 8b 84 24 10
	02 00 00	 mov	 rax, QWORD PTR $T40[rsp]
  002d5	48 89 84 24 18
	02 00 00	 mov	 QWORD PTR $T41[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 17   : 	m_handlers[IndexOf<HandlerTypes, ChatHandler>::value]				= new ChatHandler(thisServer);

  002dd	48 8b 84 24 18
	02 00 00	 mov	 rax, QWORD PTR $T41[rsp]
  002e5	48 89 44 24 58	 mov	 QWORD PTR $T6[rsp], rax
  002ea	48 83 7c 24 58
	00		 cmp	 QWORD PTR $T6[rsp], 0
  002f0	74 19		 je	 SHORT $LN9@WorldRunne
  002f2	48 8b 94 24 28
	05 00 00	 mov	 rdx, QWORD PTR thisServer$[rsp]
  002fa	48 8b 4c 24 58	 mov	 rcx, QWORD PTR $T6[rsp]
  002ff	e8 00 00 00 00	 call	 ??0ChatHandler@mu2@@QEAA@PEAVServer@1@@Z ; mu2::ChatHandler::ChatHandler
  00304	48 89 44 24 60	 mov	 QWORD PTR tv202[rsp], rax
  00309	eb 09		 jmp	 SHORT $LN10@WorldRunne
$LN9@WorldRunne:
  0030b	48 c7 44 24 60
	00 00 00 00	 mov	 QWORD PTR tv202[rsp], 0
$LN10@WorldRunne:
  00314	48 8b 44 24 60	 mov	 rax, QWORD PTR tv202[rsp]
  00319	48 89 84 24 30
	02 00 00	 mov	 QWORD PTR $T44[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1928 :         auto& _My_data = _Mypair._Myval2;

  00321	48 8b 84 24 20
	05 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00329	48 05 a0 01 00
	00		 add	 rax, 416		; 000001a0H
  0032f	48 89 84 24 20
	02 00 00	 mov	 QWORD PTR _My_data$42[rsp], rax

; 1929 : #if _MSVC_STL_HARDENING_VECTOR || _ITERATOR_DEBUG_LEVEL != 0
; 1930 :         _STL_VERIFY(
; 1931 :             _Pos < static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst), "vector subscript out of range");
; 1932 : #endif
; 1933 : 
; 1934 :         return _My_data._Myfirst[_Pos];

  00337	b8 03 00 00 00	 mov	 eax, 3
  0033c	48 6b c0 08	 imul	 rax, rax, 8
  00340	48 8b 8c 24 20
	02 00 00	 mov	 rcx, QWORD PTR _My_data$42[rsp]
  00348	48 03 01	 add	 rax, QWORD PTR [rcx]
  0034b	48 89 84 24 28
	02 00 00	 mov	 QWORD PTR $T43[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 17   : 	m_handlers[IndexOf<HandlerTypes, ChatHandler>::value]				= new ChatHandler(thisServer);

  00353	48 8b 84 24 28
	02 00 00	 mov	 rax, QWORD PTR $T43[rsp]
  0035b	48 8b 8c 24 30
	02 00 00	 mov	 rcx, QWORD PTR $T44[rsp]
  00363	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h

; 26   : 			return malloc(size);

  00366	b9 98 00 00 00	 mov	 ecx, 152		; 00000098H
  0036b	e8 00 00 00 00	 call	 malloc
  00370	48 89 84 24 38
	02 00 00	 mov	 QWORD PTR $T45[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h

; 26   : 		return Alloc::allocate(size);

  00378	48 8b 84 24 38
	02 00 00	 mov	 rax, QWORD PTR $T45[rsp]
  00380	48 89 84 24 40
	02 00 00	 mov	 QWORD PTR $T46[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 18   : 	m_handlers[IndexOf<HandlerTypes, JoinWaitHandler>::value]			= new JoinWaitHandler(thisServer);

  00388	48 8b 84 24 40
	02 00 00	 mov	 rax, QWORD PTR $T46[rsp]
  00390	48 89 44 24 68	 mov	 QWORD PTR $T7[rsp], rax
  00395	48 83 7c 24 68
	00		 cmp	 QWORD PTR $T7[rsp], 0
  0039b	74 19		 je	 SHORT $LN11@WorldRunne
  0039d	48 8b 94 24 28
	05 00 00	 mov	 rdx, QWORD PTR thisServer$[rsp]
  003a5	48 8b 4c 24 68	 mov	 rcx, QWORD PTR $T7[rsp]
  003aa	e8 00 00 00 00	 call	 ??0JoinWaitHandler@mu2@@QEAA@PEAVServer@1@@Z ; mu2::JoinWaitHandler::JoinWaitHandler
  003af	48 89 44 24 70	 mov	 QWORD PTR tv221[rsp], rax
  003b4	eb 09		 jmp	 SHORT $LN12@WorldRunne
$LN11@WorldRunne:
  003b6	48 c7 44 24 70
	00 00 00 00	 mov	 QWORD PTR tv221[rsp], 0
$LN12@WorldRunne:
  003bf	48 8b 44 24 70	 mov	 rax, QWORD PTR tv221[rsp]
  003c4	48 89 84 24 58
	02 00 00	 mov	 QWORD PTR $T49[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1928 :         auto& _My_data = _Mypair._Myval2;

  003cc	48 8b 84 24 20
	05 00 00	 mov	 rax, QWORD PTR this$[rsp]
  003d4	48 05 a0 01 00
	00		 add	 rax, 416		; 000001a0H
  003da	48 89 84 24 48
	02 00 00	 mov	 QWORD PTR _My_data$47[rsp], rax

; 1929 : #if _MSVC_STL_HARDENING_VECTOR || _ITERATOR_DEBUG_LEVEL != 0
; 1930 :         _STL_VERIFY(
; 1931 :             _Pos < static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst), "vector subscript out of range");
; 1932 : #endif
; 1933 : 
; 1934 :         return _My_data._Myfirst[_Pos];

  003e2	b8 04 00 00 00	 mov	 eax, 4
  003e7	48 6b c0 08	 imul	 rax, rax, 8
  003eb	48 8b 8c 24 48
	02 00 00	 mov	 rcx, QWORD PTR _My_data$47[rsp]
  003f3	48 03 01	 add	 rax, QWORD PTR [rcx]
  003f6	48 89 84 24 50
	02 00 00	 mov	 QWORD PTR $T48[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 18   : 	m_handlers[IndexOf<HandlerTypes, JoinWaitHandler>::value]			= new JoinWaitHandler(thisServer);

  003fe	48 8b 84 24 50
	02 00 00	 mov	 rax, QWORD PTR $T48[rsp]
  00406	48 8b 8c 24 58
	02 00 00	 mov	 rcx, QWORD PTR $T49[rsp]
  0040e	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h

; 26   : 			return malloc(size);

  00411	b9 b0 00 00 00	 mov	 ecx, 176		; 000000b0H
  00416	e8 00 00 00 00	 call	 malloc
  0041b	48 89 84 24 60
	02 00 00	 mov	 QWORD PTR $T50[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h

; 26   : 		return Alloc::allocate(size);

  00423	48 8b 84 24 60
	02 00 00	 mov	 rax, QWORD PTR $T50[rsp]
  0042b	48 89 84 24 68
	02 00 00	 mov	 QWORD PTR $T51[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 19   : 	m_handlers[IndexOf<HandlerTypes, MissionmapHandler>::value]			= new MissionmapHandler(thisServer);

  00433	48 8b 84 24 68
	02 00 00	 mov	 rax, QWORD PTR $T51[rsp]
  0043b	48 89 44 24 78	 mov	 QWORD PTR $T8[rsp], rax
  00440	48 83 7c 24 78
	00		 cmp	 QWORD PTR $T8[rsp], 0
  00446	74 1c		 je	 SHORT $LN13@WorldRunne
  00448	48 8b 94 24 28
	05 00 00	 mov	 rdx, QWORD PTR thisServer$[rsp]
  00450	48 8b 4c 24 78	 mov	 rcx, QWORD PTR $T8[rsp]
  00455	e8 00 00 00 00	 call	 ??0MissionmapHandler@mu2@@QEAA@PEAVServer@1@@Z ; mu2::MissionmapHandler::MissionmapHandler
  0045a	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR tv240[rsp], rax
  00462	eb 0c		 jmp	 SHORT $LN14@WorldRunne
$LN13@WorldRunne:
  00464	48 c7 84 24 80
	00 00 00 00 00
	00 00		 mov	 QWORD PTR tv240[rsp], 0
$LN14@WorldRunne:
  00470	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR tv240[rsp]
  00478	48 89 84 24 80
	02 00 00	 mov	 QWORD PTR $T54[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1928 :         auto& _My_data = _Mypair._Myval2;

  00480	48 8b 84 24 20
	05 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00488	48 05 a0 01 00
	00		 add	 rax, 416		; 000001a0H
  0048e	48 89 84 24 70
	02 00 00	 mov	 QWORD PTR _My_data$52[rsp], rax

; 1929 : #if _MSVC_STL_HARDENING_VECTOR || _ITERATOR_DEBUG_LEVEL != 0
; 1930 :         _STL_VERIFY(
; 1931 :             _Pos < static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst), "vector subscript out of range");
; 1932 : #endif
; 1933 : 
; 1934 :         return _My_data._Myfirst[_Pos];

  00496	b8 05 00 00 00	 mov	 eax, 5
  0049b	48 6b c0 08	 imul	 rax, rax, 8
  0049f	48 8b 8c 24 70
	02 00 00	 mov	 rcx, QWORD PTR _My_data$52[rsp]
  004a7	48 03 01	 add	 rax, QWORD PTR [rcx]
  004aa	48 89 84 24 78
	02 00 00	 mov	 QWORD PTR $T53[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 19   : 	m_handlers[IndexOf<HandlerTypes, MissionmapHandler>::value]			= new MissionmapHandler(thisServer);

  004b2	48 8b 84 24 78
	02 00 00	 mov	 rax, QWORD PTR $T53[rsp]
  004ba	48 8b 8c 24 80
	02 00 00	 mov	 rcx, QWORD PTR $T54[rsp]
  004c2	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h

; 26   : 			return malloc(size);

  004c5	b9 88 00 00 00	 mov	 ecx, 136		; 00000088H
  004ca	e8 00 00 00 00	 call	 malloc
  004cf	48 89 84 24 88
	02 00 00	 mov	 QWORD PTR $T55[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h

; 26   : 		return Alloc::allocate(size);

  004d7	48 8b 84 24 88
	02 00 00	 mov	 rax, QWORD PTR $T55[rsp]
  004df	48 89 84 24 90
	02 00 00	 mov	 QWORD PTR $T56[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 20   : 	m_handlers[IndexOf<HandlerTypes, CommunityHandler>::value]			= new CommunityHandler(thisServer);

  004e7	48 8b 84 24 90
	02 00 00	 mov	 rax, QWORD PTR $T56[rsp]
  004ef	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax
  004f7	48 83 bc 24 88
	00 00 00 00	 cmp	 QWORD PTR $T9[rsp], 0
  00500	74 1f		 je	 SHORT $LN15@WorldRunne
  00502	48 8b 94 24 28
	05 00 00	 mov	 rdx, QWORD PTR thisServer$[rsp]
  0050a	48 8b 8c 24 88
	00 00 00	 mov	 rcx, QWORD PTR $T9[rsp]
  00512	e8 00 00 00 00	 call	 ??0CommunityHandler@mu2@@QEAA@PEAVServer@1@@Z ; mu2::CommunityHandler::CommunityHandler
  00517	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR tv259[rsp], rax
  0051f	eb 0c		 jmp	 SHORT $LN16@WorldRunne
$LN15@WorldRunne:
  00521	48 c7 84 24 90
	00 00 00 00 00
	00 00		 mov	 QWORD PTR tv259[rsp], 0
$LN16@WorldRunne:
  0052d	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR tv259[rsp]
  00535	48 89 84 24 a8
	02 00 00	 mov	 QWORD PTR $T59[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1928 :         auto& _My_data = _Mypair._Myval2;

  0053d	48 8b 84 24 20
	05 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00545	48 05 a0 01 00
	00		 add	 rax, 416		; 000001a0H
  0054b	48 89 84 24 98
	02 00 00	 mov	 QWORD PTR _My_data$57[rsp], rax

; 1929 : #if _MSVC_STL_HARDENING_VECTOR || _ITERATOR_DEBUG_LEVEL != 0
; 1930 :         _STL_VERIFY(
; 1931 :             _Pos < static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst), "vector subscript out of range");
; 1932 : #endif
; 1933 : 
; 1934 :         return _My_data._Myfirst[_Pos];

  00553	b8 06 00 00 00	 mov	 eax, 6
  00558	48 6b c0 08	 imul	 rax, rax, 8
  0055c	48 8b 8c 24 98
	02 00 00	 mov	 rcx, QWORD PTR _My_data$57[rsp]
  00564	48 03 01	 add	 rax, QWORD PTR [rcx]
  00567	48 89 84 24 a0
	02 00 00	 mov	 QWORD PTR $T58[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 20   : 	m_handlers[IndexOf<HandlerTypes, CommunityHandler>::value]			= new CommunityHandler(thisServer);

  0056f	48 8b 84 24 a0
	02 00 00	 mov	 rax, QWORD PTR $T58[rsp]
  00577	48 8b 8c 24 a8
	02 00 00	 mov	 rcx, QWORD PTR $T59[rsp]
  0057f	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h

; 26   : 			return malloc(size);

  00582	b9 88 00 00 00	 mov	 ecx, 136		; 00000088H
  00587	e8 00 00 00 00	 call	 malloc
  0058c	48 89 84 24 b0
	02 00 00	 mov	 QWORD PTR $T60[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h

; 26   : 		return Alloc::allocate(size);

  00594	48 8b 84 24 b0
	02 00 00	 mov	 rax, QWORD PTR $T60[rsp]
  0059c	48 89 84 24 b8
	02 00 00	 mov	 QWORD PTR $T61[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 21   : 	m_handlers[IndexOf<HandlerTypes, MultistageDungeonHandler>::value]	= new MultistageDungeonHandler(thisServer);

  005a4	48 8b 84 24 b8
	02 00 00	 mov	 rax, QWORD PTR $T61[rsp]
  005ac	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
  005b4	48 83 bc 24 98
	00 00 00 00	 cmp	 QWORD PTR $T10[rsp], 0
  005bd	74 1f		 je	 SHORT $LN17@WorldRunne
  005bf	48 8b 94 24 28
	05 00 00	 mov	 rdx, QWORD PTR thisServer$[rsp]
  005c7	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR $T10[rsp]
  005cf	e8 00 00 00 00	 call	 ??0MultistageDungeonHandler@mu2@@QEAA@PEAVServer@1@@Z ; mu2::MultistageDungeonHandler::MultistageDungeonHandler
  005d4	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR tv278[rsp], rax
  005dc	eb 0c		 jmp	 SHORT $LN18@WorldRunne
$LN17@WorldRunne:
  005de	48 c7 84 24 a0
	00 00 00 00 00
	00 00		 mov	 QWORD PTR tv278[rsp], 0
$LN18@WorldRunne:
  005ea	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR tv278[rsp]
  005f2	48 89 84 24 d0
	02 00 00	 mov	 QWORD PTR $T64[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1928 :         auto& _My_data = _Mypair._Myval2;

  005fa	48 8b 84 24 20
	05 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00602	48 05 a0 01 00
	00		 add	 rax, 416		; 000001a0H
  00608	48 89 84 24 c0
	02 00 00	 mov	 QWORD PTR _My_data$62[rsp], rax

; 1929 : #if _MSVC_STL_HARDENING_VECTOR || _ITERATOR_DEBUG_LEVEL != 0
; 1930 :         _STL_VERIFY(
; 1931 :             _Pos < static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst), "vector subscript out of range");
; 1932 : #endif
; 1933 : 
; 1934 :         return _My_data._Myfirst[_Pos];

  00610	b8 07 00 00 00	 mov	 eax, 7
  00615	48 6b c0 08	 imul	 rax, rax, 8
  00619	48 8b 8c 24 c0
	02 00 00	 mov	 rcx, QWORD PTR _My_data$62[rsp]
  00621	48 03 01	 add	 rax, QWORD PTR [rcx]
  00624	48 89 84 24 c8
	02 00 00	 mov	 QWORD PTR $T63[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 21   : 	m_handlers[IndexOf<HandlerTypes, MultistageDungeonHandler>::value]	= new MultistageDungeonHandler(thisServer);

  0062c	48 8b 84 24 c8
	02 00 00	 mov	 rax, QWORD PTR $T63[rsp]
  00634	48 8b 8c 24 d0
	02 00 00	 mov	 rcx, QWORD PTR $T64[rsp]
  0063c	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h

; 26   : 			return malloc(size);

  0063f	b9 98 00 00 00	 mov	 ecx, 152		; 00000098H
  00644	e8 00 00 00 00	 call	 malloc
  00649	48 89 84 24 d8
	02 00 00	 mov	 QWORD PTR $T65[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h

; 26   : 		return Alloc::allocate(size);

  00651	48 8b 84 24 d8
	02 00 00	 mov	 rax, QWORD PTR $T65[rsp]
  00659	48 89 84 24 e0
	02 00 00	 mov	 QWORD PTR $T66[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 22   : 	m_handlers[IndexOf<HandlerTypes, KnightageHandler>::value]			= new KnightageHandler(thisServer);

  00661	48 8b 84 24 e0
	02 00 00	 mov	 rax, QWORD PTR $T66[rsp]
  00669	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
  00671	48 83 bc 24 a8
	00 00 00 00	 cmp	 QWORD PTR $T11[rsp], 0
  0067a	74 1f		 je	 SHORT $LN19@WorldRunne
  0067c	48 8b 94 24 28
	05 00 00	 mov	 rdx, QWORD PTR thisServer$[rsp]
  00684	48 8b 8c 24 a8
	00 00 00	 mov	 rcx, QWORD PTR $T11[rsp]
  0068c	e8 00 00 00 00	 call	 ??0KnightageHandler@mu2@@QEAA@PEAVServer@1@@Z ; mu2::KnightageHandler::KnightageHandler
  00691	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR tv297[rsp], rax
  00699	eb 0c		 jmp	 SHORT $LN20@WorldRunne
$LN19@WorldRunne:
  0069b	48 c7 84 24 b0
	00 00 00 00 00
	00 00		 mov	 QWORD PTR tv297[rsp], 0
$LN20@WorldRunne:
  006a7	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR tv297[rsp]
  006af	48 89 84 24 f8
	02 00 00	 mov	 QWORD PTR $T69[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1928 :         auto& _My_data = _Mypair._Myval2;

  006b7	48 8b 84 24 20
	05 00 00	 mov	 rax, QWORD PTR this$[rsp]
  006bf	48 05 a0 01 00
	00		 add	 rax, 416		; 000001a0H
  006c5	48 89 84 24 e8
	02 00 00	 mov	 QWORD PTR _My_data$67[rsp], rax

; 1929 : #if _MSVC_STL_HARDENING_VECTOR || _ITERATOR_DEBUG_LEVEL != 0
; 1930 :         _STL_VERIFY(
; 1931 :             _Pos < static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst), "vector subscript out of range");
; 1932 : #endif
; 1933 : 
; 1934 :         return _My_data._Myfirst[_Pos];

  006cd	b8 08 00 00 00	 mov	 eax, 8
  006d2	48 6b c0 08	 imul	 rax, rax, 8
  006d6	48 8b 8c 24 e8
	02 00 00	 mov	 rcx, QWORD PTR _My_data$67[rsp]
  006de	48 03 01	 add	 rax, QWORD PTR [rcx]
  006e1	48 89 84 24 f0
	02 00 00	 mov	 QWORD PTR $T68[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 22   : 	m_handlers[IndexOf<HandlerTypes, KnightageHandler>::value]			= new KnightageHandler(thisServer);

  006e9	48 8b 84 24 f0
	02 00 00	 mov	 rax, QWORD PTR $T68[rsp]
  006f1	48 8b 8c 24 f8
	02 00 00	 mov	 rcx, QWORD PTR $T69[rsp]
  006f9	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h

; 26   : 			return malloc(size);

  006fc	b9 88 00 00 00	 mov	 ecx, 136		; 00000088H
  00701	e8 00 00 00 00	 call	 malloc
  00706	48 89 84 24 00
	03 00 00	 mov	 QWORD PTR $T70[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h

; 26   : 		return Alloc::allocate(size);

  0070e	48 8b 84 24 00
	03 00 00	 mov	 rax, QWORD PTR $T70[rsp]
  00716	48 89 84 24 08
	03 00 00	 mov	 QWORD PTR $T71[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 23   : 	m_handlers[IndexOf<HandlerTypes, BurningStaminaHandler>::value]		= new BurningStaminaHandler(thisServer);

  0071e	48 8b 84 24 08
	03 00 00	 mov	 rax, QWORD PTR $T71[rsp]
  00726	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
  0072e	48 83 bc 24 b8
	00 00 00 00	 cmp	 QWORD PTR $T12[rsp], 0
  00737	74 1f		 je	 SHORT $LN21@WorldRunne
  00739	48 8b 94 24 28
	05 00 00	 mov	 rdx, QWORD PTR thisServer$[rsp]
  00741	48 8b 8c 24 b8
	00 00 00	 mov	 rcx, QWORD PTR $T12[rsp]
  00749	e8 00 00 00 00	 call	 ??0BurningStaminaHandler@mu2@@QEAA@PEAVServer@1@@Z ; mu2::BurningStaminaHandler::BurningStaminaHandler
  0074e	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR tv316[rsp], rax
  00756	eb 0c		 jmp	 SHORT $LN22@WorldRunne
$LN21@WorldRunne:
  00758	48 c7 84 24 c0
	00 00 00 00 00
	00 00		 mov	 QWORD PTR tv316[rsp], 0
$LN22@WorldRunne:
  00764	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR tv316[rsp]
  0076c	48 89 84 24 20
	03 00 00	 mov	 QWORD PTR $T74[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1928 :         auto& _My_data = _Mypair._Myval2;

  00774	48 8b 84 24 20
	05 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0077c	48 05 a0 01 00
	00		 add	 rax, 416		; 000001a0H
  00782	48 89 84 24 10
	03 00 00	 mov	 QWORD PTR _My_data$72[rsp], rax

; 1929 : #if _MSVC_STL_HARDENING_VECTOR || _ITERATOR_DEBUG_LEVEL != 0
; 1930 :         _STL_VERIFY(
; 1931 :             _Pos < static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst), "vector subscript out of range");
; 1932 : #endif
; 1933 : 
; 1934 :         return _My_data._Myfirst[_Pos];

  0078a	b8 09 00 00 00	 mov	 eax, 9
  0078f	48 6b c0 08	 imul	 rax, rax, 8
  00793	48 8b 8c 24 10
	03 00 00	 mov	 rcx, QWORD PTR _My_data$72[rsp]
  0079b	48 03 01	 add	 rax, QWORD PTR [rcx]
  0079e	48 89 84 24 18
	03 00 00	 mov	 QWORD PTR $T73[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 23   : 	m_handlers[IndexOf<HandlerTypes, BurningStaminaHandler>::value]		= new BurningStaminaHandler(thisServer);

  007a6	48 8b 84 24 18
	03 00 00	 mov	 rax, QWORD PTR $T73[rsp]
  007ae	48 8b 8c 24 20
	03 00 00	 mov	 rcx, QWORD PTR $T74[rsp]
  007b6	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h

; 26   : 			return malloc(size);

  007b9	b9 88 00 00 00	 mov	 ecx, 136		; 00000088H
  007be	e8 00 00 00 00	 call	 malloc
  007c3	48 89 84 24 28
	03 00 00	 mov	 QWORD PTR $T75[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h

; 26   : 		return Alloc::allocate(size);

  007cb	48 8b 84 24 28
	03 00 00	 mov	 rax, QWORD PTR $T75[rsp]
  007d3	48 89 84 24 30
	03 00 00	 mov	 QWORD PTR $T76[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 24   : 	m_handlers[IndexOf<HandlerTypes, DungeonMatchHandler>::value]		= new DungeonMatchHandler(thisServer);

  007db	48 8b 84 24 30
	03 00 00	 mov	 rax, QWORD PTR $T76[rsp]
  007e3	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax
  007eb	48 83 bc 24 c8
	00 00 00 00	 cmp	 QWORD PTR $T13[rsp], 0
  007f4	74 1f		 je	 SHORT $LN23@WorldRunne
  007f6	48 8b 94 24 28
	05 00 00	 mov	 rdx, QWORD PTR thisServer$[rsp]
  007fe	48 8b 8c 24 c8
	00 00 00	 mov	 rcx, QWORD PTR $T13[rsp]
  00806	e8 00 00 00 00	 call	 ??0DungeonMatchHandler@mu2@@QEAA@PEAVServer@1@@Z ; mu2::DungeonMatchHandler::DungeonMatchHandler
  0080b	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR tv335[rsp], rax
  00813	eb 0c		 jmp	 SHORT $LN24@WorldRunne
$LN23@WorldRunne:
  00815	48 c7 84 24 d0
	00 00 00 00 00
	00 00		 mov	 QWORD PTR tv335[rsp], 0
$LN24@WorldRunne:
  00821	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR tv335[rsp]
  00829	48 89 84 24 48
	03 00 00	 mov	 QWORD PTR $T79[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1928 :         auto& _My_data = _Mypair._Myval2;

  00831	48 8b 84 24 20
	05 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00839	48 05 a0 01 00
	00		 add	 rax, 416		; 000001a0H
  0083f	48 89 84 24 38
	03 00 00	 mov	 QWORD PTR _My_data$77[rsp], rax

; 1929 : #if _MSVC_STL_HARDENING_VECTOR || _ITERATOR_DEBUG_LEVEL != 0
; 1930 :         _STL_VERIFY(
; 1931 :             _Pos < static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst), "vector subscript out of range");
; 1932 : #endif
; 1933 : 
; 1934 :         return _My_data._Myfirst[_Pos];

  00847	b8 0a 00 00 00	 mov	 eax, 10
  0084c	48 6b c0 08	 imul	 rax, rax, 8
  00850	48 8b 8c 24 38
	03 00 00	 mov	 rcx, QWORD PTR _My_data$77[rsp]
  00858	48 03 01	 add	 rax, QWORD PTR [rcx]
  0085b	48 89 84 24 40
	03 00 00	 mov	 QWORD PTR $T78[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 24   : 	m_handlers[IndexOf<HandlerTypes, DungeonMatchHandler>::value]		= new DungeonMatchHandler(thisServer);

  00863	48 8b 84 24 40
	03 00 00	 mov	 rax, QWORD PTR $T78[rsp]
  0086b	48 8b 8c 24 48
	03 00 00	 mov	 rcx, QWORD PTR $T79[rsp]
  00873	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h

; 26   : 			return malloc(size);

  00876	b9 88 00 00 00	 mov	 ecx, 136		; 00000088H
  0087b	e8 00 00 00 00	 call	 malloc
  00880	48 89 84 24 50
	03 00 00	 mov	 QWORD PTR $T80[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h

; 26   : 		return Alloc::allocate(size);

  00888	48 8b 84 24 50
	03 00 00	 mov	 rax, QWORD PTR $T80[rsp]
  00890	48 89 84 24 58
	03 00 00	 mov	 QWORD PTR $T81[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 25   : 	m_handlers[IndexOf<HandlerTypes, RankingHandler>::value]			= new RankingHandler(thisServer);

  00898	48 8b 84 24 58
	03 00 00	 mov	 rax, QWORD PTR $T81[rsp]
  008a0	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR $T14[rsp], rax
  008a8	48 83 bc 24 d8
	00 00 00 00	 cmp	 QWORD PTR $T14[rsp], 0
  008b1	74 1f		 je	 SHORT $LN25@WorldRunne
  008b3	48 8b 94 24 28
	05 00 00	 mov	 rdx, QWORD PTR thisServer$[rsp]
  008bb	48 8b 8c 24 d8
	00 00 00	 mov	 rcx, QWORD PTR $T14[rsp]
  008c3	e8 00 00 00 00	 call	 ??0RankingHandler@mu2@@QEAA@PEAVServer@1@@Z ; mu2::RankingHandler::RankingHandler
  008c8	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR tv354[rsp], rax
  008d0	eb 0c		 jmp	 SHORT $LN26@WorldRunne
$LN25@WorldRunne:
  008d2	48 c7 84 24 e0
	00 00 00 00 00
	00 00		 mov	 QWORD PTR tv354[rsp], 0
$LN26@WorldRunne:
  008de	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR tv354[rsp]
  008e6	48 89 84 24 70
	03 00 00	 mov	 QWORD PTR $T84[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1928 :         auto& _My_data = _Mypair._Myval2;

  008ee	48 8b 84 24 20
	05 00 00	 mov	 rax, QWORD PTR this$[rsp]
  008f6	48 05 a0 01 00
	00		 add	 rax, 416		; 000001a0H
  008fc	48 89 84 24 60
	03 00 00	 mov	 QWORD PTR _My_data$82[rsp], rax

; 1929 : #if _MSVC_STL_HARDENING_VECTOR || _ITERATOR_DEBUG_LEVEL != 0
; 1930 :         _STL_VERIFY(
; 1931 :             _Pos < static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst), "vector subscript out of range");
; 1932 : #endif
; 1933 : 
; 1934 :         return _My_data._Myfirst[_Pos];

  00904	b8 0b 00 00 00	 mov	 eax, 11
  00909	48 6b c0 08	 imul	 rax, rax, 8
  0090d	48 8b 8c 24 60
	03 00 00	 mov	 rcx, QWORD PTR _My_data$82[rsp]
  00915	48 03 01	 add	 rax, QWORD PTR [rcx]
  00918	48 89 84 24 68
	03 00 00	 mov	 QWORD PTR $T83[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 25   : 	m_handlers[IndexOf<HandlerTypes, RankingHandler>::value]			= new RankingHandler(thisServer);

  00920	48 8b 84 24 68
	03 00 00	 mov	 rax, QWORD PTR $T83[rsp]
  00928	48 8b 8c 24 70
	03 00 00	 mov	 rcx, QWORD PTR $T84[rsp]
  00930	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h

; 26   : 			return malloc(size);

  00933	b9 88 00 00 00	 mov	 ecx, 136		; 00000088H
  00938	e8 00 00 00 00	 call	 malloc
  0093d	48 89 84 24 78
	03 00 00	 mov	 QWORD PTR $T85[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h

; 26   : 		return Alloc::allocate(size);

  00945	48 8b 84 24 78
	03 00 00	 mov	 rax, QWORD PTR $T85[rsp]
  0094d	48 89 84 24 80
	03 00 00	 mov	 QWORD PTR $T86[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 26   : 	m_handlers[IndexOf<HandlerTypes, DamageMeterHandler>::value ]		= new DamageMeterHandler(thisServer);

  00955	48 8b 84 24 80
	03 00 00	 mov	 rax, QWORD PTR $T86[rsp]
  0095d	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR $T15[rsp], rax
  00965	48 83 bc 24 e8
	00 00 00 00	 cmp	 QWORD PTR $T15[rsp], 0
  0096e	74 1f		 je	 SHORT $LN27@WorldRunne
  00970	48 8b 94 24 28
	05 00 00	 mov	 rdx, QWORD PTR thisServer$[rsp]
  00978	48 8b 8c 24 e8
	00 00 00	 mov	 rcx, QWORD PTR $T15[rsp]
  00980	e8 00 00 00 00	 call	 ??0DamageMeterHandler@mu2@@QEAA@PEAVServer@1@@Z ; mu2::DamageMeterHandler::DamageMeterHandler
  00985	48 89 84 24 f0
	00 00 00	 mov	 QWORD PTR tv373[rsp], rax
  0098d	eb 0c		 jmp	 SHORT $LN28@WorldRunne
$LN27@WorldRunne:
  0098f	48 c7 84 24 f0
	00 00 00 00 00
	00 00		 mov	 QWORD PTR tv373[rsp], 0
$LN28@WorldRunne:
  0099b	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR tv373[rsp]
  009a3	48 89 84 24 98
	03 00 00	 mov	 QWORD PTR $T89[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1928 :         auto& _My_data = _Mypair._Myval2;

  009ab	48 8b 84 24 20
	05 00 00	 mov	 rax, QWORD PTR this$[rsp]
  009b3	48 05 a0 01 00
	00		 add	 rax, 416		; 000001a0H
  009b9	48 89 84 24 88
	03 00 00	 mov	 QWORD PTR _My_data$87[rsp], rax

; 1929 : #if _MSVC_STL_HARDENING_VECTOR || _ITERATOR_DEBUG_LEVEL != 0
; 1930 :         _STL_VERIFY(
; 1931 :             _Pos < static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst), "vector subscript out of range");
; 1932 : #endif
; 1933 : 
; 1934 :         return _My_data._Myfirst[_Pos];

  009c1	b8 0c 00 00 00	 mov	 eax, 12
  009c6	48 6b c0 08	 imul	 rax, rax, 8
  009ca	48 8b 8c 24 88
	03 00 00	 mov	 rcx, QWORD PTR _My_data$87[rsp]
  009d2	48 03 01	 add	 rax, QWORD PTR [rcx]
  009d5	48 89 84 24 90
	03 00 00	 mov	 QWORD PTR $T88[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 26   : 	m_handlers[IndexOf<HandlerTypes, DamageMeterHandler>::value ]		= new DamageMeterHandler(thisServer);

  009dd	48 8b 84 24 90
	03 00 00	 mov	 rax, QWORD PTR $T88[rsp]
  009e5	48 8b 8c 24 98
	03 00 00	 mov	 rcx, QWORD PTR $T89[rsp]
  009ed	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h

; 26   : 			return malloc(size);

  009f0	b9 88 00 00 00	 mov	 ecx, 136		; 00000088H
  009f5	e8 00 00 00 00	 call	 malloc
  009fa	48 89 84 24 a0
	03 00 00	 mov	 QWORD PTR $T90[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h

; 26   : 		return Alloc::allocate(size);

  00a02	48 8b 84 24 a0
	03 00 00	 mov	 rax, QWORD PTR $T90[rsp]
  00a0a	48 89 84 24 a8
	03 00 00	 mov	 QWORD PTR $T91[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 28   : 	m_handlers[IndexOf<HandlerTypes, WOPSHandler >::value ]				= new WOPSHandler(thisServer);

  00a12	48 8b 84 24 a8
	03 00 00	 mov	 rax, QWORD PTR $T91[rsp]
  00a1a	48 89 84 24 f8
	00 00 00	 mov	 QWORD PTR $T16[rsp], rax
  00a22	48 83 bc 24 f8
	00 00 00 00	 cmp	 QWORD PTR $T16[rsp], 0
  00a2b	74 1f		 je	 SHORT $LN29@WorldRunne
  00a2d	48 8b 94 24 28
	05 00 00	 mov	 rdx, QWORD PTR thisServer$[rsp]
  00a35	48 8b 8c 24 f8
	00 00 00	 mov	 rcx, QWORD PTR $T16[rsp]
  00a3d	e8 00 00 00 00	 call	 ??0WOPSHandler@mu2@@QEAA@PEAVServer@1@@Z ; mu2::WOPSHandler::WOPSHandler
  00a42	48 89 84 24 00
	01 00 00	 mov	 QWORD PTR tv392[rsp], rax
  00a4a	eb 0c		 jmp	 SHORT $LN30@WorldRunne
$LN29@WorldRunne:
  00a4c	48 c7 84 24 00
	01 00 00 00 00
	00 00		 mov	 QWORD PTR tv392[rsp], 0
$LN30@WorldRunne:
  00a58	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR tv392[rsp]
  00a60	48 89 84 24 c0
	03 00 00	 mov	 QWORD PTR $T94[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1928 :         auto& _My_data = _Mypair._Myval2;

  00a68	48 8b 84 24 20
	05 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00a70	48 05 a0 01 00
	00		 add	 rax, 416		; 000001a0H
  00a76	48 89 84 24 b0
	03 00 00	 mov	 QWORD PTR _My_data$92[rsp], rax

; 1929 : #if _MSVC_STL_HARDENING_VECTOR || _ITERATOR_DEBUG_LEVEL != 0
; 1930 :         _STL_VERIFY(
; 1931 :             _Pos < static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst), "vector subscript out of range");
; 1932 : #endif
; 1933 : 
; 1934 :         return _My_data._Myfirst[_Pos];

  00a7e	b8 0d 00 00 00	 mov	 eax, 13
  00a83	48 6b c0 08	 imul	 rax, rax, 8
  00a87	48 8b 8c 24 b0
	03 00 00	 mov	 rcx, QWORD PTR _My_data$92[rsp]
  00a8f	48 03 01	 add	 rax, QWORD PTR [rcx]
  00a92	48 89 84 24 b8
	03 00 00	 mov	 QWORD PTR $T93[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 28   : 	m_handlers[IndexOf<HandlerTypes, WOPSHandler >::value ]				= new WOPSHandler(thisServer);

  00a9a	48 8b 84 24 b8
	03 00 00	 mov	 rax, QWORD PTR $T93[rsp]
  00aa2	48 8b 8c 24 c0
	03 00 00	 mov	 rcx, QWORD PTR $T94[rsp]
  00aaa	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h

; 26   : 			return malloc(size);

  00aad	b9 88 00 00 00	 mov	 ecx, 136		; 00000088H
  00ab2	e8 00 00 00 00	 call	 malloc
  00ab7	48 89 84 24 c8
	03 00 00	 mov	 QWORD PTR $T95[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h

; 26   : 		return Alloc::allocate(size);

  00abf	48 8b 84 24 c8
	03 00 00	 mov	 rax, QWORD PTR $T95[rsp]
  00ac7	48 89 84 24 d0
	03 00 00	 mov	 QWORD PTR $T96[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 29   : 	m_handlers[IndexOf<HandlerTypes, DailyLoginHandler>::value]			= new DailyLoginHandler(thisServer);

  00acf	48 8b 84 24 d0
	03 00 00	 mov	 rax, QWORD PTR $T96[rsp]
  00ad7	48 89 84 24 08
	01 00 00	 mov	 QWORD PTR $T17[rsp], rax
  00adf	48 83 bc 24 08
	01 00 00 00	 cmp	 QWORD PTR $T17[rsp], 0
  00ae8	74 1f		 je	 SHORT $LN31@WorldRunne
  00aea	48 8b 94 24 28
	05 00 00	 mov	 rdx, QWORD PTR thisServer$[rsp]
  00af2	48 8b 8c 24 08
	01 00 00	 mov	 rcx, QWORD PTR $T17[rsp]
  00afa	e8 00 00 00 00	 call	 ??0DailyLoginHandler@mu2@@QEAA@PEAVServer@1@@Z ; mu2::DailyLoginHandler::DailyLoginHandler
  00aff	48 89 84 24 10
	01 00 00	 mov	 QWORD PTR tv411[rsp], rax
  00b07	eb 0c		 jmp	 SHORT $LN32@WorldRunne
$LN31@WorldRunne:
  00b09	48 c7 84 24 10
	01 00 00 00 00
	00 00		 mov	 QWORD PTR tv411[rsp], 0
$LN32@WorldRunne:
  00b15	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR tv411[rsp]
  00b1d	48 89 84 24 e8
	03 00 00	 mov	 QWORD PTR $T99[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1928 :         auto& _My_data = _Mypair._Myval2;

  00b25	48 8b 84 24 20
	05 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00b2d	48 05 a0 01 00
	00		 add	 rax, 416		; 000001a0H
  00b33	48 89 84 24 d8
	03 00 00	 mov	 QWORD PTR _My_data$97[rsp], rax

; 1929 : #if _MSVC_STL_HARDENING_VECTOR || _ITERATOR_DEBUG_LEVEL != 0
; 1930 :         _STL_VERIFY(
; 1931 :             _Pos < static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst), "vector subscript out of range");
; 1932 : #endif
; 1933 : 
; 1934 :         return _My_data._Myfirst[_Pos];

  00b3b	b8 0e 00 00 00	 mov	 eax, 14
  00b40	48 6b c0 08	 imul	 rax, rax, 8
  00b44	48 8b 8c 24 d8
	03 00 00	 mov	 rcx, QWORD PTR _My_data$97[rsp]
  00b4c	48 03 01	 add	 rax, QWORD PTR [rcx]
  00b4f	48 89 84 24 e0
	03 00 00	 mov	 QWORD PTR $T98[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 29   : 	m_handlers[IndexOf<HandlerTypes, DailyLoginHandler>::value]			= new DailyLoginHandler(thisServer);

  00b57	48 8b 84 24 e0
	03 00 00	 mov	 rax, QWORD PTR $T98[rsp]
  00b5f	48 8b 8c 24 e8
	03 00 00	 mov	 rcx, QWORD PTR $T99[rsp]
  00b67	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h

; 26   : 			return malloc(size);

  00b6a	b9 88 00 00 00	 mov	 ecx, 136		; 00000088H
  00b6f	e8 00 00 00 00	 call	 malloc
  00b74	48 89 84 24 f0
	03 00 00	 mov	 QWORD PTR $T100[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h

; 26   : 		return Alloc::allocate(size);

  00b7c	48 8b 84 24 f0
	03 00 00	 mov	 rax, QWORD PTR $T100[rsp]
  00b84	48 89 84 24 f8
	03 00 00	 mov	 QWORD PTR $T101[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 30   : 	m_handlers[IndexOf<HandlerTypes, DailyMissionHandler>::value]		= new DailyMissionHandler(thisServer);

  00b8c	48 8b 84 24 f8
	03 00 00	 mov	 rax, QWORD PTR $T101[rsp]
  00b94	48 89 84 24 18
	01 00 00	 mov	 QWORD PTR $T18[rsp], rax
  00b9c	48 83 bc 24 18
	01 00 00 00	 cmp	 QWORD PTR $T18[rsp], 0
  00ba5	74 1f		 je	 SHORT $LN33@WorldRunne
  00ba7	48 8b 94 24 28
	05 00 00	 mov	 rdx, QWORD PTR thisServer$[rsp]
  00baf	48 8b 8c 24 18
	01 00 00	 mov	 rcx, QWORD PTR $T18[rsp]
  00bb7	e8 00 00 00 00	 call	 ??0DailyMissionHandler@mu2@@QEAA@PEAVServer@1@@Z ; mu2::DailyMissionHandler::DailyMissionHandler
  00bbc	48 89 84 24 20
	01 00 00	 mov	 QWORD PTR tv462[rsp], rax
  00bc4	eb 0c		 jmp	 SHORT $LN34@WorldRunne
$LN33@WorldRunne:
  00bc6	48 c7 84 24 20
	01 00 00 00 00
	00 00		 mov	 QWORD PTR tv462[rsp], 0
$LN34@WorldRunne:
  00bd2	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR tv462[rsp]
  00bda	48 89 84 24 10
	04 00 00	 mov	 QWORD PTR $T104[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1928 :         auto& _My_data = _Mypair._Myval2;

  00be2	48 8b 84 24 20
	05 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00bea	48 05 a0 01 00
	00		 add	 rax, 416		; 000001a0H
  00bf0	48 89 84 24 00
	04 00 00	 mov	 QWORD PTR _My_data$102[rsp], rax

; 1929 : #if _MSVC_STL_HARDENING_VECTOR || _ITERATOR_DEBUG_LEVEL != 0
; 1930 :         _STL_VERIFY(
; 1931 :             _Pos < static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst), "vector subscript out of range");
; 1932 : #endif
; 1933 : 
; 1934 :         return _My_data._Myfirst[_Pos];

  00bf8	b8 0f 00 00 00	 mov	 eax, 15
  00bfd	48 6b c0 08	 imul	 rax, rax, 8
  00c01	48 8b 8c 24 00
	04 00 00	 mov	 rcx, QWORD PTR _My_data$102[rsp]
  00c09	48 03 01	 add	 rax, QWORD PTR [rcx]
  00c0c	48 89 84 24 08
	04 00 00	 mov	 QWORD PTR $T103[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 30   : 	m_handlers[IndexOf<HandlerTypes, DailyMissionHandler>::value]		= new DailyMissionHandler(thisServer);

  00c14	48 8b 84 24 08
	04 00 00	 mov	 rax, QWORD PTR $T103[rsp]
  00c1c	48 8b 8c 24 10
	04 00 00	 mov	 rcx, QWORD PTR $T104[rsp]
  00c24	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h

; 26   : 			return malloc(size);

  00c27	b9 88 00 00 00	 mov	 ecx, 136		; 00000088H
  00c2c	e8 00 00 00 00	 call	 malloc
  00c31	48 89 84 24 18
	04 00 00	 mov	 QWORD PTR $T105[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h

; 26   : 		return Alloc::allocate(size);

  00c39	48 8b 84 24 18
	04 00 00	 mov	 rax, QWORD PTR $T105[rsp]
  00c41	48 89 84 24 20
	04 00 00	 mov	 QWORD PTR $T106[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 31   : 	m_handlers[IndexOf<HandlerTypes, LuckyMonsterHandler>::value]		= new LuckyMonsterHandler(thisServer);

  00c49	48 8b 84 24 20
	04 00 00	 mov	 rax, QWORD PTR $T106[rsp]
  00c51	48 89 84 24 28
	01 00 00	 mov	 QWORD PTR $T19[rsp], rax
  00c59	48 83 bc 24 28
	01 00 00 00	 cmp	 QWORD PTR $T19[rsp], 0
  00c62	74 1f		 je	 SHORT $LN35@WorldRunne
  00c64	48 8b 94 24 28
	05 00 00	 mov	 rdx, QWORD PTR thisServer$[rsp]
  00c6c	48 8b 8c 24 28
	01 00 00	 mov	 rcx, QWORD PTR $T19[rsp]
  00c74	e8 00 00 00 00	 call	 ??0LuckyMonsterHandler@mu2@@QEAA@PEAVServer@1@@Z ; mu2::LuckyMonsterHandler::LuckyMonsterHandler
  00c79	48 89 84 24 30
	01 00 00	 mov	 QWORD PTR tv481[rsp], rax
  00c81	eb 0c		 jmp	 SHORT $LN36@WorldRunne
$LN35@WorldRunne:
  00c83	48 c7 84 24 30
	01 00 00 00 00
	00 00		 mov	 QWORD PTR tv481[rsp], 0
$LN36@WorldRunne:
  00c8f	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR tv481[rsp]
  00c97	48 89 84 24 38
	04 00 00	 mov	 QWORD PTR $T109[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1928 :         auto& _My_data = _Mypair._Myval2;

  00c9f	48 8b 84 24 20
	05 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00ca7	48 05 a0 01 00
	00		 add	 rax, 416		; 000001a0H
  00cad	48 89 84 24 28
	04 00 00	 mov	 QWORD PTR _My_data$107[rsp], rax

; 1929 : #if _MSVC_STL_HARDENING_VECTOR || _ITERATOR_DEBUG_LEVEL != 0
; 1930 :         _STL_VERIFY(
; 1931 :             _Pos < static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst), "vector subscript out of range");
; 1932 : #endif
; 1933 : 
; 1934 :         return _My_data._Myfirst[_Pos];

  00cb5	b8 10 00 00 00	 mov	 eax, 16
  00cba	48 6b c0 08	 imul	 rax, rax, 8
  00cbe	48 8b 8c 24 28
	04 00 00	 mov	 rcx, QWORD PTR _My_data$107[rsp]
  00cc6	48 03 01	 add	 rax, QWORD PTR [rcx]
  00cc9	48 89 84 24 30
	04 00 00	 mov	 QWORD PTR $T108[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 31   : 	m_handlers[IndexOf<HandlerTypes, LuckyMonsterHandler>::value]		= new LuckyMonsterHandler(thisServer);

  00cd1	48 8b 84 24 30
	04 00 00	 mov	 rax, QWORD PTR $T108[rsp]
  00cd9	48 8b 8c 24 38
	04 00 00	 mov	 rcx, QWORD PTR $T109[rsp]
  00ce1	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h

; 26   : 			return malloc(size);

  00ce4	b9 88 00 00 00	 mov	 ecx, 136		; 00000088H
  00ce9	e8 00 00 00 00	 call	 malloc
  00cee	48 89 84 24 40
	04 00 00	 mov	 QWORD PTR $T110[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h

; 26   : 		return Alloc::allocate(size);

  00cf6	48 8b 84 24 40
	04 00 00	 mov	 rax, QWORD PTR $T110[rsp]
  00cfe	48 89 84 24 48
	04 00 00	 mov	 QWORD PTR $T111[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 32   : 	m_handlers[IndexOf<HandlerTypes, FieldPointHandler>::value]			= new FieldPointHandler(thisServer);

  00d06	48 8b 84 24 48
	04 00 00	 mov	 rax, QWORD PTR $T111[rsp]
  00d0e	48 89 84 24 38
	01 00 00	 mov	 QWORD PTR $T20[rsp], rax
  00d16	48 83 bc 24 38
	01 00 00 00	 cmp	 QWORD PTR $T20[rsp], 0
  00d1f	74 1f		 je	 SHORT $LN37@WorldRunne
  00d21	48 8b 94 24 28
	05 00 00	 mov	 rdx, QWORD PTR thisServer$[rsp]
  00d29	48 8b 8c 24 38
	01 00 00	 mov	 rcx, QWORD PTR $T20[rsp]
  00d31	e8 00 00 00 00	 call	 ??0FieldPointHandler@mu2@@QEAA@PEAVServer@1@@Z ; mu2::FieldPointHandler::FieldPointHandler
  00d36	48 89 84 24 40
	01 00 00	 mov	 QWORD PTR tv500[rsp], rax
  00d3e	eb 0c		 jmp	 SHORT $LN38@WorldRunne
$LN37@WorldRunne:
  00d40	48 c7 84 24 40
	01 00 00 00 00
	00 00		 mov	 QWORD PTR tv500[rsp], 0
$LN38@WorldRunne:
  00d4c	48 8b 84 24 40
	01 00 00	 mov	 rax, QWORD PTR tv500[rsp]
  00d54	48 89 84 24 60
	04 00 00	 mov	 QWORD PTR $T114[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1928 :         auto& _My_data = _Mypair._Myval2;

  00d5c	48 8b 84 24 20
	05 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00d64	48 05 a0 01 00
	00		 add	 rax, 416		; 000001a0H
  00d6a	48 89 84 24 50
	04 00 00	 mov	 QWORD PTR _My_data$112[rsp], rax

; 1929 : #if _MSVC_STL_HARDENING_VECTOR || _ITERATOR_DEBUG_LEVEL != 0
; 1930 :         _STL_VERIFY(
; 1931 :             _Pos < static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst), "vector subscript out of range");
; 1932 : #endif
; 1933 : 
; 1934 :         return _My_data._Myfirst[_Pos];

  00d72	b8 11 00 00 00	 mov	 eax, 17
  00d77	48 6b c0 08	 imul	 rax, rax, 8
  00d7b	48 8b 8c 24 50
	04 00 00	 mov	 rcx, QWORD PTR _My_data$112[rsp]
  00d83	48 03 01	 add	 rax, QWORD PTR [rcx]
  00d86	48 89 84 24 58
	04 00 00	 mov	 QWORD PTR $T113[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 32   : 	m_handlers[IndexOf<HandlerTypes, FieldPointHandler>::value]			= new FieldPointHandler(thisServer);

  00d8e	48 8b 84 24 58
	04 00 00	 mov	 rax, QWORD PTR $T113[rsp]
  00d96	48 8b 8c 24 60
	04 00 00	 mov	 rcx, QWORD PTR $T114[rsp]
  00d9e	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h

; 26   : 			return malloc(size);

  00da1	b9 88 00 00 00	 mov	 ecx, 136		; 00000088H
  00da6	e8 00 00 00 00	 call	 malloc
  00dab	48 89 84 24 68
	04 00 00	 mov	 QWORD PTR $T115[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h

; 26   : 		return Alloc::allocate(size);

  00db3	48 8b 84 24 68
	04 00 00	 mov	 rax, QWORD PTR $T115[rsp]
  00dbb	48 89 84 24 70
	04 00 00	 mov	 QWORD PTR $T116[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 33   : 	m_handlers[IndexOf<HandlerTypes, RaidHandler>::value]				= new RaidHandler(thisServer);

  00dc3	48 8b 84 24 70
	04 00 00	 mov	 rax, QWORD PTR $T116[rsp]
  00dcb	48 89 84 24 48
	01 00 00	 mov	 QWORD PTR $T21[rsp], rax
  00dd3	48 83 bc 24 48
	01 00 00 00	 cmp	 QWORD PTR $T21[rsp], 0
  00ddc	74 1f		 je	 SHORT $LN39@WorldRunne
  00dde	48 8b 94 24 28
	05 00 00	 mov	 rdx, QWORD PTR thisServer$[rsp]
  00de6	48 8b 8c 24 48
	01 00 00	 mov	 rcx, QWORD PTR $T21[rsp]
  00dee	e8 00 00 00 00	 call	 ??0RaidHandler@mu2@@QEAA@PEAVServer@1@@Z ; mu2::RaidHandler::RaidHandler
  00df3	48 89 84 24 50
	01 00 00	 mov	 QWORD PTR tv519[rsp], rax
  00dfb	eb 0c		 jmp	 SHORT $LN40@WorldRunne
$LN39@WorldRunne:
  00dfd	48 c7 84 24 50
	01 00 00 00 00
	00 00		 mov	 QWORD PTR tv519[rsp], 0
$LN40@WorldRunne:
  00e09	48 8b 84 24 50
	01 00 00	 mov	 rax, QWORD PTR tv519[rsp]
  00e11	48 89 84 24 88
	04 00 00	 mov	 QWORD PTR $T119[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1928 :         auto& _My_data = _Mypair._Myval2;

  00e19	48 8b 84 24 20
	05 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00e21	48 05 a0 01 00
	00		 add	 rax, 416		; 000001a0H
  00e27	48 89 84 24 78
	04 00 00	 mov	 QWORD PTR _My_data$117[rsp], rax

; 1929 : #if _MSVC_STL_HARDENING_VECTOR || _ITERATOR_DEBUG_LEVEL != 0
; 1930 :         _STL_VERIFY(
; 1931 :             _Pos < static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst), "vector subscript out of range");
; 1932 : #endif
; 1933 : 
; 1934 :         return _My_data._Myfirst[_Pos];

  00e2f	b8 12 00 00 00	 mov	 eax, 18
  00e34	48 6b c0 08	 imul	 rax, rax, 8
  00e38	48 8b 8c 24 78
	04 00 00	 mov	 rcx, QWORD PTR _My_data$117[rsp]
  00e40	48 03 01	 add	 rax, QWORD PTR [rcx]
  00e43	48 89 84 24 80
	04 00 00	 mov	 QWORD PTR $T118[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 33   : 	m_handlers[IndexOf<HandlerTypes, RaidHandler>::value]				= new RaidHandler(thisServer);

  00e4b	48 8b 84 24 80
	04 00 00	 mov	 rax, QWORD PTR $T118[rsp]
  00e53	48 8b 8c 24 88
	04 00 00	 mov	 rcx, QWORD PTR $T119[rsp]
  00e5b	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h

; 26   : 			return malloc(size);

  00e5e	b9 88 00 00 00	 mov	 ecx, 136		; 00000088H
  00e63	e8 00 00 00 00	 call	 malloc
  00e68	48 89 84 24 90
	04 00 00	 mov	 QWORD PTR $T120[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h

; 26   : 		return Alloc::allocate(size);

  00e70	48 8b 84 24 90
	04 00 00	 mov	 rax, QWORD PTR $T120[rsp]
  00e78	48 89 84 24 98
	04 00 00	 mov	 QWORD PTR $T121[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 34   : 	m_handlers[IndexOf<HandlerTypes, PCRoomBillingEventLoopBackHandler>::value]			 = new PCRoomBillingEventLoopBackHandler( thisServer );

  00e80	48 8b 84 24 98
	04 00 00	 mov	 rax, QWORD PTR $T121[rsp]
  00e88	48 89 84 24 58
	01 00 00	 mov	 QWORD PTR $T22[rsp], rax
  00e90	48 83 bc 24 58
	01 00 00 00	 cmp	 QWORD PTR $T22[rsp], 0
  00e99	74 1f		 je	 SHORT $LN41@WorldRunne
  00e9b	48 8b 94 24 28
	05 00 00	 mov	 rdx, QWORD PTR thisServer$[rsp]
  00ea3	48 8b 8c 24 58
	01 00 00	 mov	 rcx, QWORD PTR $T22[rsp]
  00eab	e8 00 00 00 00	 call	 ??0PCRoomBillingEventLoopBackHandler@mu2@@QEAA@PEAVServer@1@@Z ; mu2::PCRoomBillingEventLoopBackHandler::PCRoomBillingEventLoopBackHandler
  00eb0	48 89 84 24 60
	01 00 00	 mov	 QWORD PTR tv538[rsp], rax
  00eb8	eb 0c		 jmp	 SHORT $LN42@WorldRunne
$LN41@WorldRunne:
  00eba	48 c7 84 24 60
	01 00 00 00 00
	00 00		 mov	 QWORD PTR tv538[rsp], 0
$LN42@WorldRunne:
  00ec6	48 8b 84 24 60
	01 00 00	 mov	 rax, QWORD PTR tv538[rsp]
  00ece	48 89 84 24 b0
	04 00 00	 mov	 QWORD PTR $T124[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1928 :         auto& _My_data = _Mypair._Myval2;

  00ed6	48 8b 84 24 20
	05 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00ede	48 05 a0 01 00
	00		 add	 rax, 416		; 000001a0H
  00ee4	48 89 84 24 a0
	04 00 00	 mov	 QWORD PTR _My_data$122[rsp], rax

; 1929 : #if _MSVC_STL_HARDENING_VECTOR || _ITERATOR_DEBUG_LEVEL != 0
; 1930 :         _STL_VERIFY(
; 1931 :             _Pos < static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst), "vector subscript out of range");
; 1932 : #endif
; 1933 : 
; 1934 :         return _My_data._Myfirst[_Pos];

  00eec	b8 13 00 00 00	 mov	 eax, 19
  00ef1	48 6b c0 08	 imul	 rax, rax, 8
  00ef5	48 8b 8c 24 a0
	04 00 00	 mov	 rcx, QWORD PTR _My_data$122[rsp]
  00efd	48 03 01	 add	 rax, QWORD PTR [rcx]
  00f00	48 89 84 24 a8
	04 00 00	 mov	 QWORD PTR $T123[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 34   : 	m_handlers[IndexOf<HandlerTypes, PCRoomBillingEventLoopBackHandler>::value]			 = new PCRoomBillingEventLoopBackHandler( thisServer );

  00f08	48 8b 84 24 a8
	04 00 00	 mov	 rax, QWORD PTR $T123[rsp]
  00f10	48 8b 8c 24 b0
	04 00 00	 mov	 rcx, QWORD PTR $T124[rsp]
  00f18	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h

; 26   : 			return malloc(size);

  00f1b	b9 88 00 00 00	 mov	 ecx, 136		; 00000088H
  00f20	e8 00 00 00 00	 call	 malloc
  00f25	48 89 84 24 b8
	04 00 00	 mov	 QWORD PTR $T125[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h

; 26   : 		return Alloc::allocate(size);

  00f2d	48 8b 84 24 b8
	04 00 00	 mov	 rax, QWORD PTR $T125[rsp]
  00f35	48 89 84 24 c0
	04 00 00	 mov	 QWORD PTR $T126[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 35   : 	m_handlers[IndexOf<HandlerTypes, ReLoginGetFcsAuthInfoHandler>::value] = new ReLoginGetFcsAuthInfoHandler( thisServer );

  00f3d	48 8b 84 24 c0
	04 00 00	 mov	 rax, QWORD PTR $T126[rsp]
  00f45	48 89 84 24 68
	01 00 00	 mov	 QWORD PTR $T23[rsp], rax
  00f4d	48 83 bc 24 68
	01 00 00 00	 cmp	 QWORD PTR $T23[rsp], 0
  00f56	74 1f		 je	 SHORT $LN43@WorldRunne
  00f58	48 8b 94 24 28
	05 00 00	 mov	 rdx, QWORD PTR thisServer$[rsp]
  00f60	48 8b 8c 24 68
	01 00 00	 mov	 rcx, QWORD PTR $T23[rsp]
  00f68	e8 00 00 00 00	 call	 ??0ReLoginGetFcsAuthInfoHandler@mu2@@QEAA@PEAVServer@1@@Z ; mu2::ReLoginGetFcsAuthInfoHandler::ReLoginGetFcsAuthInfoHandler
  00f6d	48 89 84 24 70
	01 00 00	 mov	 QWORD PTR tv557[rsp], rax
  00f75	eb 0c		 jmp	 SHORT $LN44@WorldRunne
$LN43@WorldRunne:
  00f77	48 c7 84 24 70
	01 00 00 00 00
	00 00		 mov	 QWORD PTR tv557[rsp], 0
$LN44@WorldRunne:
  00f83	48 8b 84 24 70
	01 00 00	 mov	 rax, QWORD PTR tv557[rsp]
  00f8b	48 89 84 24 d8
	04 00 00	 mov	 QWORD PTR $T129[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1928 :         auto& _My_data = _Mypair._Myval2;

  00f93	48 8b 84 24 20
	05 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00f9b	48 05 a0 01 00
	00		 add	 rax, 416		; 000001a0H
  00fa1	48 89 84 24 c8
	04 00 00	 mov	 QWORD PTR _My_data$127[rsp], rax

; 1929 : #if _MSVC_STL_HARDENING_VECTOR || _ITERATOR_DEBUG_LEVEL != 0
; 1930 :         _STL_VERIFY(
; 1931 :             _Pos < static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst), "vector subscript out of range");
; 1932 : #endif
; 1933 : 
; 1934 :         return _My_data._Myfirst[_Pos];

  00fa9	b8 14 00 00 00	 mov	 eax, 20
  00fae	48 6b c0 08	 imul	 rax, rax, 8
  00fb2	48 8b 8c 24 c8
	04 00 00	 mov	 rcx, QWORD PTR _My_data$127[rsp]
  00fba	48 03 01	 add	 rax, QWORD PTR [rcx]
  00fbd	48 89 84 24 d0
	04 00 00	 mov	 QWORD PTR $T128[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 35   : 	m_handlers[IndexOf<HandlerTypes, ReLoginGetFcsAuthInfoHandler>::value] = new ReLoginGetFcsAuthInfoHandler( thisServer );

  00fc5	48 8b 84 24 d0
	04 00 00	 mov	 rax, QWORD PTR $T128[rsp]
  00fcd	48 8b 8c 24 d8
	04 00 00	 mov	 rcx, QWORD PTR $T129[rsp]
  00fd5	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h

; 26   : 			return malloc(size);

  00fd8	b9 88 00 00 00	 mov	 ecx, 136		; 00000088H
  00fdd	e8 00 00 00 00	 call	 malloc
  00fe2	48 89 84 24 e0
	04 00 00	 mov	 QWORD PTR $T130[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h

; 26   : 		return Alloc::allocate(size);

  00fea	48 8b 84 24 e0
	04 00 00	 mov	 rax, QWORD PTR $T130[rsp]
  00ff2	48 89 84 24 e8
	04 00 00	 mov	 QWORD PTR $T131[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 40   : 	m_handlers[IndexOf<HandlerTypes, SeasonHandler>::value]				= new SeasonHandler( thisServer );

  00ffa	48 8b 84 24 e8
	04 00 00	 mov	 rax, QWORD PTR $T131[rsp]
  01002	48 89 84 24 78
	01 00 00	 mov	 QWORD PTR $T24[rsp], rax
  0100a	48 83 bc 24 78
	01 00 00 00	 cmp	 QWORD PTR $T24[rsp], 0
  01013	74 1f		 je	 SHORT $LN45@WorldRunne
  01015	48 8b 94 24 28
	05 00 00	 mov	 rdx, QWORD PTR thisServer$[rsp]
  0101d	48 8b 8c 24 78
	01 00 00	 mov	 rcx, QWORD PTR $T24[rsp]
  01025	e8 00 00 00 00	 call	 ??0SeasonHandler@mu2@@QEAA@PEAVServer@1@@Z ; mu2::SeasonHandler::SeasonHandler
  0102a	48 89 84 24 80
	01 00 00	 mov	 QWORD PTR tv576[rsp], rax
  01032	eb 0c		 jmp	 SHORT $LN46@WorldRunne
$LN45@WorldRunne:
  01034	48 c7 84 24 80
	01 00 00 00 00
	00 00		 mov	 QWORD PTR tv576[rsp], 0
$LN46@WorldRunne:
  01040	48 8b 84 24 80
	01 00 00	 mov	 rax, QWORD PTR tv576[rsp]
  01048	48 89 84 24 00
	05 00 00	 mov	 QWORD PTR $T134[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1928 :         auto& _My_data = _Mypair._Myval2;

  01050	48 8b 84 24 20
	05 00 00	 mov	 rax, QWORD PTR this$[rsp]
  01058	48 05 a0 01 00
	00		 add	 rax, 416		; 000001a0H
  0105e	48 89 84 24 f0
	04 00 00	 mov	 QWORD PTR _My_data$132[rsp], rax

; 1929 : #if _MSVC_STL_HARDENING_VECTOR || _ITERATOR_DEBUG_LEVEL != 0
; 1930 :         _STL_VERIFY(
; 1931 :             _Pos < static_cast<size_type>(_My_data._Mylast - _My_data._Myfirst), "vector subscript out of range");
; 1932 : #endif
; 1933 : 
; 1934 :         return _My_data._Myfirst[_Pos];

  01066	b8 15 00 00 00	 mov	 eax, 21
  0106b	48 6b c0 08	 imul	 rax, rax, 8
  0106f	48 8b 8c 24 f0
	04 00 00	 mov	 rcx, QWORD PTR _My_data$132[rsp]
  01077	48 03 01	 add	 rax, QWORD PTR [rcx]
  0107a	48 89 84 24 f8
	04 00 00	 mov	 QWORD PTR $T133[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp

; 40   : 	m_handlers[IndexOf<HandlerTypes, SeasonHandler>::value]				= new SeasonHandler( thisServer );

  01082	48 8b 84 24 f8
	04 00 00	 mov	 rax, QWORD PTR $T133[rsp]
  0108a	48 8b 8c 24 00
	05 00 00	 mov	 rcx, QWORD PTR $T134[rsp]
  01092	48 89 08	 mov	 QWORD PTR [rax], rcx

; 41   : }

  01095	48 8b 84 24 20
	05 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0109d	48 81 c4 10 05
	00 00		 add	 rsp, 1296		; 00000510H
  010a4	5f		 pop	 rdi
  010a5	c3		 ret	 0
??0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z ENDP		; mu2::WorldRunner::WorldRunner
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 33
$T3 = 40
tv145 = 48
$T4 = 56
tv164 = 64
$T5 = 72
tv183 = 80
$T6 = 88
tv202 = 96
$T7 = 104
tv221 = 112
$T8 = 120
tv240 = 128
$T9 = 136
tv259 = 144
$T10 = 152
tv278 = 160
$T11 = 168
tv297 = 176
$T12 = 184
tv316 = 192
$T13 = 200
tv335 = 208
$T14 = 216
tv354 = 224
$T15 = 232
tv373 = 240
$T16 = 248
tv392 = 256
$T17 = 264
tv411 = 272
$T18 = 280
tv462 = 288
$T19 = 296
tv481 = 304
$T20 = 312
tv500 = 320
$T21 = 328
tv519 = 336
$T22 = 344
tv538 = 352
$T23 = 360
tv557 = 368
$T24 = 376
tv576 = 384
this$ = 392
this$ = 400
$T25 = 408
$T26 = 416
_My_data$27 = 424
$T28 = 432
$T29 = 440
$T30 = 448
$T31 = 456
_My_data$32 = 464
$T33 = 472
$T34 = 480
$T35 = 488
$T36 = 496
_My_data$37 = 504
$T38 = 512
$T39 = 520
$T40 = 528
$T41 = 536
_My_data$42 = 544
$T43 = 552
$T44 = 560
$T45 = 568
$T46 = 576
_My_data$47 = 584
$T48 = 592
$T49 = 600
$T50 = 608
$T51 = 616
_My_data$52 = 624
$T53 = 632
$T54 = 640
$T55 = 648
$T56 = 656
_My_data$57 = 664
$T58 = 672
$T59 = 680
$T60 = 688
$T61 = 696
_My_data$62 = 704
$T63 = 712
$T64 = 720
$T65 = 728
$T66 = 736
_My_data$67 = 744
$T68 = 752
$T69 = 760
$T70 = 768
$T71 = 776
_My_data$72 = 784
$T73 = 792
$T74 = 800
$T75 = 808
$T76 = 816
_My_data$77 = 824
$T78 = 832
$T79 = 840
$T80 = 848
$T81 = 856
_My_data$82 = 864
$T83 = 872
$T84 = 880
$T85 = 888
$T86 = 896
_My_data$87 = 904
$T88 = 912
$T89 = 920
$T90 = 928
$T91 = 936
_My_data$92 = 944
$T93 = 952
$T94 = 960
$T95 = 968
$T96 = 976
_My_data$97 = 984
$T98 = 992
$T99 = 1000
$T100 = 1008
$T101 = 1016
_My_data$102 = 1024
$T103 = 1032
$T104 = 1040
$T105 = 1048
$T106 = 1056
_My_data$107 = 1064
$T108 = 1072
$T109 = 1080
$T110 = 1088
$T111 = 1096
_My_data$112 = 1104
$T113 = 1112
$T114 = 1120
$T115 = 1128
$T116 = 1136
_My_data$117 = 1144
$T118 = 1152
$T119 = 1160
$T120 = 1168
$T121 = 1176
_My_data$122 = 1184
$T123 = 1192
$T124 = 1200
$T125 = 1208
$T126 = 1216
_My_data$127 = 1224
$T128 = 1232
$T129 = 1240
$T130 = 1248
$T131 = 1256
_My_data$132 = 1264
$T133 = 1272
$T134 = 1280
this$ = 1312
thisServer$ = 1320
?dtor$0@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA PROC ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 8d 20 05
	00 00		 mov	 rcx, QWORD PTR this$[rbp]
  00010	e8 00 00 00 00	 call	 ??1ServerTask@mu2@@UEAA@XZ ; mu2::ServerTask::~ServerTask
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$0@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA ENDP ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 33
$T3 = 40
tv145 = 48
$T4 = 56
tv164 = 64
$T5 = 72
tv183 = 80
$T6 = 88
tv202 = 96
$T7 = 104
tv221 = 112
$T8 = 120
tv240 = 128
$T9 = 136
tv259 = 144
$T10 = 152
tv278 = 160
$T11 = 168
tv297 = 176
$T12 = 184
tv316 = 192
$T13 = 200
tv335 = 208
$T14 = 216
tv354 = 224
$T15 = 232
tv373 = 240
$T16 = 248
tv392 = 256
$T17 = 264
tv411 = 272
$T18 = 280
tv462 = 288
$T19 = 296
tv481 = 304
$T20 = 312
tv500 = 320
$T21 = 328
tv519 = 336
$T22 = 344
tv538 = 352
$T23 = 360
tv557 = 368
$T24 = 376
tv576 = 384
this$ = 392
this$ = 400
$T25 = 408
$T26 = 416
_My_data$27 = 424
$T28 = 432
$T29 = 440
$T30 = 448
$T31 = 456
_My_data$32 = 464
$T33 = 472
$T34 = 480
$T35 = 488
$T36 = 496
_My_data$37 = 504
$T38 = 512
$T39 = 520
$T40 = 528
$T41 = 536
_My_data$42 = 544
$T43 = 552
$T44 = 560
$T45 = 568
$T46 = 576
_My_data$47 = 584
$T48 = 592
$T49 = 600
$T50 = 608
$T51 = 616
_My_data$52 = 624
$T53 = 632
$T54 = 640
$T55 = 648
$T56 = 656
_My_data$57 = 664
$T58 = 672
$T59 = 680
$T60 = 688
$T61 = 696
_My_data$62 = 704
$T63 = 712
$T64 = 720
$T65 = 728
$T66 = 736
_My_data$67 = 744
$T68 = 752
$T69 = 760
$T70 = 768
$T71 = 776
_My_data$72 = 784
$T73 = 792
$T74 = 800
$T75 = 808
$T76 = 816
_My_data$77 = 824
$T78 = 832
$T79 = 840
$T80 = 848
$T81 = 856
_My_data$82 = 864
$T83 = 872
$T84 = 880
$T85 = 888
$T86 = 896
_My_data$87 = 904
$T88 = 912
$T89 = 920
$T90 = 928
$T91 = 936
_My_data$92 = 944
$T93 = 952
$T94 = 960
$T95 = 968
$T96 = 976
_My_data$97 = 984
$T98 = 992
$T99 = 1000
$T100 = 1008
$T101 = 1016
_My_data$102 = 1024
$T103 = 1032
$T104 = 1040
$T105 = 1048
$T106 = 1056
_My_data$107 = 1064
$T108 = 1072
$T109 = 1080
$T110 = 1088
$T111 = 1096
_My_data$112 = 1104
$T113 = 1112
$T114 = 1120
$T115 = 1128
$T116 = 1136
_My_data$117 = 1144
$T118 = 1152
$T119 = 1160
$T120 = 1168
$T121 = 1176
_My_data$122 = 1184
$T123 = 1192
$T124 = 1200
$T125 = 1208
$T126 = 1216
_My_data$127 = 1224
$T128 = 1232
$T129 = 1240
$T130 = 1248
$T131 = 1256
_My_data$132 = 1264
$T133 = 1272
$T134 = 1280
this$ = 1312
thisServer$ = 1320
?dtor$1@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA PROC ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$1
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 8d 20 05
	00 00		 mov	 rcx, QWORD PTR this$[rbp]
  00010	48 81 c1 a0 01
	00 00		 add	 rcx, 416		; 000001a0H
  00017	e8 00 00 00 00	 call	 ??1?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAA@XZ ; std::vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >::~vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >
  0001c	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00020	5d		 pop	 rbp
  00021	c3		 ret	 0
?dtor$1@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA ENDP ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$1
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 33
$T3 = 40
tv145 = 48
$T4 = 56
tv164 = 64
$T5 = 72
tv183 = 80
$T6 = 88
tv202 = 96
$T7 = 104
tv221 = 112
$T8 = 120
tv240 = 128
$T9 = 136
tv259 = 144
$T10 = 152
tv278 = 160
$T11 = 168
tv297 = 176
$T12 = 184
tv316 = 192
$T13 = 200
tv335 = 208
$T14 = 216
tv354 = 224
$T15 = 232
tv373 = 240
$T16 = 248
tv392 = 256
$T17 = 264
tv411 = 272
$T18 = 280
tv462 = 288
$T19 = 296
tv481 = 304
$T20 = 312
tv500 = 320
$T21 = 328
tv519 = 336
$T22 = 344
tv538 = 352
$T23 = 360
tv557 = 368
$T24 = 376
tv576 = 384
this$ = 392
this$ = 400
$T25 = 408
$T26 = 416
_My_data$27 = 424
$T28 = 432
$T29 = 440
$T30 = 448
$T31 = 456
_My_data$32 = 464
$T33 = 472
$T34 = 480
$T35 = 488
$T36 = 496
_My_data$37 = 504
$T38 = 512
$T39 = 520
$T40 = 528
$T41 = 536
_My_data$42 = 544
$T43 = 552
$T44 = 560
$T45 = 568
$T46 = 576
_My_data$47 = 584
$T48 = 592
$T49 = 600
$T50 = 608
$T51 = 616
_My_data$52 = 624
$T53 = 632
$T54 = 640
$T55 = 648
$T56 = 656
_My_data$57 = 664
$T58 = 672
$T59 = 680
$T60 = 688
$T61 = 696
_My_data$62 = 704
$T63 = 712
$T64 = 720
$T65 = 728
$T66 = 736
_My_data$67 = 744
$T68 = 752
$T69 = 760
$T70 = 768
$T71 = 776
_My_data$72 = 784
$T73 = 792
$T74 = 800
$T75 = 808
$T76 = 816
_My_data$77 = 824
$T78 = 832
$T79 = 840
$T80 = 848
$T81 = 856
_My_data$82 = 864
$T83 = 872
$T84 = 880
$T85 = 888
$T86 = 896
_My_data$87 = 904
$T88 = 912
$T89 = 920
$T90 = 928
$T91 = 936
_My_data$92 = 944
$T93 = 952
$T94 = 960
$T95 = 968
$T96 = 976
_My_data$97 = 984
$T98 = 992
$T99 = 1000
$T100 = 1008
$T101 = 1016
_My_data$102 = 1024
$T103 = 1032
$T104 = 1040
$T105 = 1048
$T106 = 1056
_My_data$107 = 1064
$T108 = 1072
$T109 = 1080
$T110 = 1088
$T111 = 1096
_My_data$112 = 1104
$T113 = 1112
$T114 = 1120
$T115 = 1128
$T116 = 1136
_My_data$117 = 1144
$T118 = 1152
$T119 = 1160
$T120 = 1168
$T121 = 1176
_My_data$122 = 1184
$T123 = 1192
$T124 = 1200
$T125 = 1208
$T126 = 1216
_My_data$127 = 1224
$T128 = 1232
$T129 = 1240
$T130 = 1248
$T131 = 1256
_My_data$132 = 1264
$T133 = 1272
$T134 = 1280
this$ = 1312
thisServer$ = 1320
?dtor$2@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA PROC ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$2
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 28	 mov	 rcx, QWORD PTR $T3[rbp]
  0000d	e8 00 00 00 00	 call	 ??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::operator delete
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$2@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA ENDP ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$2
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 33
$T3 = 40
tv145 = 48
$T4 = 56
tv164 = 64
$T5 = 72
tv183 = 80
$T6 = 88
tv202 = 96
$T7 = 104
tv221 = 112
$T8 = 120
tv240 = 128
$T9 = 136
tv259 = 144
$T10 = 152
tv278 = 160
$T11 = 168
tv297 = 176
$T12 = 184
tv316 = 192
$T13 = 200
tv335 = 208
$T14 = 216
tv354 = 224
$T15 = 232
tv373 = 240
$T16 = 248
tv392 = 256
$T17 = 264
tv411 = 272
$T18 = 280
tv462 = 288
$T19 = 296
tv481 = 304
$T20 = 312
tv500 = 320
$T21 = 328
tv519 = 336
$T22 = 344
tv538 = 352
$T23 = 360
tv557 = 368
$T24 = 376
tv576 = 384
this$ = 392
this$ = 400
$T25 = 408
$T26 = 416
_My_data$27 = 424
$T28 = 432
$T29 = 440
$T30 = 448
$T31 = 456
_My_data$32 = 464
$T33 = 472
$T34 = 480
$T35 = 488
$T36 = 496
_My_data$37 = 504
$T38 = 512
$T39 = 520
$T40 = 528
$T41 = 536
_My_data$42 = 544
$T43 = 552
$T44 = 560
$T45 = 568
$T46 = 576
_My_data$47 = 584
$T48 = 592
$T49 = 600
$T50 = 608
$T51 = 616
_My_data$52 = 624
$T53 = 632
$T54 = 640
$T55 = 648
$T56 = 656
_My_data$57 = 664
$T58 = 672
$T59 = 680
$T60 = 688
$T61 = 696
_My_data$62 = 704
$T63 = 712
$T64 = 720
$T65 = 728
$T66 = 736
_My_data$67 = 744
$T68 = 752
$T69 = 760
$T70 = 768
$T71 = 776
_My_data$72 = 784
$T73 = 792
$T74 = 800
$T75 = 808
$T76 = 816
_My_data$77 = 824
$T78 = 832
$T79 = 840
$T80 = 848
$T81 = 856
_My_data$82 = 864
$T83 = 872
$T84 = 880
$T85 = 888
$T86 = 896
_My_data$87 = 904
$T88 = 912
$T89 = 920
$T90 = 928
$T91 = 936
_My_data$92 = 944
$T93 = 952
$T94 = 960
$T95 = 968
$T96 = 976
_My_data$97 = 984
$T98 = 992
$T99 = 1000
$T100 = 1008
$T101 = 1016
_My_data$102 = 1024
$T103 = 1032
$T104 = 1040
$T105 = 1048
$T106 = 1056
_My_data$107 = 1064
$T108 = 1072
$T109 = 1080
$T110 = 1088
$T111 = 1096
_My_data$112 = 1104
$T113 = 1112
$T114 = 1120
$T115 = 1128
$T116 = 1136
_My_data$117 = 1144
$T118 = 1152
$T119 = 1160
$T120 = 1168
$T121 = 1176
_My_data$122 = 1184
$T123 = 1192
$T124 = 1200
$T125 = 1208
$T126 = 1216
_My_data$127 = 1224
$T128 = 1232
$T129 = 1240
$T130 = 1248
$T131 = 1256
_My_data$132 = 1264
$T133 = 1272
$T134 = 1280
this$ = 1312
thisServer$ = 1320
?dtor$3@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA PROC ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$3
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 38	 mov	 rcx, QWORD PTR $T4[rbp]
  0000d	e8 00 00 00 00	 call	 ??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::operator delete
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$3@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA ENDP ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$3
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 33
$T3 = 40
tv145 = 48
$T4 = 56
tv164 = 64
$T5 = 72
tv183 = 80
$T6 = 88
tv202 = 96
$T7 = 104
tv221 = 112
$T8 = 120
tv240 = 128
$T9 = 136
tv259 = 144
$T10 = 152
tv278 = 160
$T11 = 168
tv297 = 176
$T12 = 184
tv316 = 192
$T13 = 200
tv335 = 208
$T14 = 216
tv354 = 224
$T15 = 232
tv373 = 240
$T16 = 248
tv392 = 256
$T17 = 264
tv411 = 272
$T18 = 280
tv462 = 288
$T19 = 296
tv481 = 304
$T20 = 312
tv500 = 320
$T21 = 328
tv519 = 336
$T22 = 344
tv538 = 352
$T23 = 360
tv557 = 368
$T24 = 376
tv576 = 384
this$ = 392
this$ = 400
$T25 = 408
$T26 = 416
_My_data$27 = 424
$T28 = 432
$T29 = 440
$T30 = 448
$T31 = 456
_My_data$32 = 464
$T33 = 472
$T34 = 480
$T35 = 488
$T36 = 496
_My_data$37 = 504
$T38 = 512
$T39 = 520
$T40 = 528
$T41 = 536
_My_data$42 = 544
$T43 = 552
$T44 = 560
$T45 = 568
$T46 = 576
_My_data$47 = 584
$T48 = 592
$T49 = 600
$T50 = 608
$T51 = 616
_My_data$52 = 624
$T53 = 632
$T54 = 640
$T55 = 648
$T56 = 656
_My_data$57 = 664
$T58 = 672
$T59 = 680
$T60 = 688
$T61 = 696
_My_data$62 = 704
$T63 = 712
$T64 = 720
$T65 = 728
$T66 = 736
_My_data$67 = 744
$T68 = 752
$T69 = 760
$T70 = 768
$T71 = 776
_My_data$72 = 784
$T73 = 792
$T74 = 800
$T75 = 808
$T76 = 816
_My_data$77 = 824
$T78 = 832
$T79 = 840
$T80 = 848
$T81 = 856
_My_data$82 = 864
$T83 = 872
$T84 = 880
$T85 = 888
$T86 = 896
_My_data$87 = 904
$T88 = 912
$T89 = 920
$T90 = 928
$T91 = 936
_My_data$92 = 944
$T93 = 952
$T94 = 960
$T95 = 968
$T96 = 976
_My_data$97 = 984
$T98 = 992
$T99 = 1000
$T100 = 1008
$T101 = 1016
_My_data$102 = 1024
$T103 = 1032
$T104 = 1040
$T105 = 1048
$T106 = 1056
_My_data$107 = 1064
$T108 = 1072
$T109 = 1080
$T110 = 1088
$T111 = 1096
_My_data$112 = 1104
$T113 = 1112
$T114 = 1120
$T115 = 1128
$T116 = 1136
_My_data$117 = 1144
$T118 = 1152
$T119 = 1160
$T120 = 1168
$T121 = 1176
_My_data$122 = 1184
$T123 = 1192
$T124 = 1200
$T125 = 1208
$T126 = 1216
_My_data$127 = 1224
$T128 = 1232
$T129 = 1240
$T130 = 1248
$T131 = 1256
_My_data$132 = 1264
$T133 = 1272
$T134 = 1280
this$ = 1312
thisServer$ = 1320
?dtor$4@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA PROC ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$4
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 48	 mov	 rcx, QWORD PTR $T5[rbp]
  0000d	e8 00 00 00 00	 call	 ??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::operator delete
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$4@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA ENDP ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$4
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 33
$T3 = 40
tv145 = 48
$T4 = 56
tv164 = 64
$T5 = 72
tv183 = 80
$T6 = 88
tv202 = 96
$T7 = 104
tv221 = 112
$T8 = 120
tv240 = 128
$T9 = 136
tv259 = 144
$T10 = 152
tv278 = 160
$T11 = 168
tv297 = 176
$T12 = 184
tv316 = 192
$T13 = 200
tv335 = 208
$T14 = 216
tv354 = 224
$T15 = 232
tv373 = 240
$T16 = 248
tv392 = 256
$T17 = 264
tv411 = 272
$T18 = 280
tv462 = 288
$T19 = 296
tv481 = 304
$T20 = 312
tv500 = 320
$T21 = 328
tv519 = 336
$T22 = 344
tv538 = 352
$T23 = 360
tv557 = 368
$T24 = 376
tv576 = 384
this$ = 392
this$ = 400
$T25 = 408
$T26 = 416
_My_data$27 = 424
$T28 = 432
$T29 = 440
$T30 = 448
$T31 = 456
_My_data$32 = 464
$T33 = 472
$T34 = 480
$T35 = 488
$T36 = 496
_My_data$37 = 504
$T38 = 512
$T39 = 520
$T40 = 528
$T41 = 536
_My_data$42 = 544
$T43 = 552
$T44 = 560
$T45 = 568
$T46 = 576
_My_data$47 = 584
$T48 = 592
$T49 = 600
$T50 = 608
$T51 = 616
_My_data$52 = 624
$T53 = 632
$T54 = 640
$T55 = 648
$T56 = 656
_My_data$57 = 664
$T58 = 672
$T59 = 680
$T60 = 688
$T61 = 696
_My_data$62 = 704
$T63 = 712
$T64 = 720
$T65 = 728
$T66 = 736
_My_data$67 = 744
$T68 = 752
$T69 = 760
$T70 = 768
$T71 = 776
_My_data$72 = 784
$T73 = 792
$T74 = 800
$T75 = 808
$T76 = 816
_My_data$77 = 824
$T78 = 832
$T79 = 840
$T80 = 848
$T81 = 856
_My_data$82 = 864
$T83 = 872
$T84 = 880
$T85 = 888
$T86 = 896
_My_data$87 = 904
$T88 = 912
$T89 = 920
$T90 = 928
$T91 = 936
_My_data$92 = 944
$T93 = 952
$T94 = 960
$T95 = 968
$T96 = 976
_My_data$97 = 984
$T98 = 992
$T99 = 1000
$T100 = 1008
$T101 = 1016
_My_data$102 = 1024
$T103 = 1032
$T104 = 1040
$T105 = 1048
$T106 = 1056
_My_data$107 = 1064
$T108 = 1072
$T109 = 1080
$T110 = 1088
$T111 = 1096
_My_data$112 = 1104
$T113 = 1112
$T114 = 1120
$T115 = 1128
$T116 = 1136
_My_data$117 = 1144
$T118 = 1152
$T119 = 1160
$T120 = 1168
$T121 = 1176
_My_data$122 = 1184
$T123 = 1192
$T124 = 1200
$T125 = 1208
$T126 = 1216
_My_data$127 = 1224
$T128 = 1232
$T129 = 1240
$T130 = 1248
$T131 = 1256
_My_data$132 = 1264
$T133 = 1272
$T134 = 1280
this$ = 1312
thisServer$ = 1320
?dtor$5@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA PROC ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$5
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 58	 mov	 rcx, QWORD PTR $T6[rbp]
  0000d	e8 00 00 00 00	 call	 ??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::operator delete
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$5@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA ENDP ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$5
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 33
$T3 = 40
tv145 = 48
$T4 = 56
tv164 = 64
$T5 = 72
tv183 = 80
$T6 = 88
tv202 = 96
$T7 = 104
tv221 = 112
$T8 = 120
tv240 = 128
$T9 = 136
tv259 = 144
$T10 = 152
tv278 = 160
$T11 = 168
tv297 = 176
$T12 = 184
tv316 = 192
$T13 = 200
tv335 = 208
$T14 = 216
tv354 = 224
$T15 = 232
tv373 = 240
$T16 = 248
tv392 = 256
$T17 = 264
tv411 = 272
$T18 = 280
tv462 = 288
$T19 = 296
tv481 = 304
$T20 = 312
tv500 = 320
$T21 = 328
tv519 = 336
$T22 = 344
tv538 = 352
$T23 = 360
tv557 = 368
$T24 = 376
tv576 = 384
this$ = 392
this$ = 400
$T25 = 408
$T26 = 416
_My_data$27 = 424
$T28 = 432
$T29 = 440
$T30 = 448
$T31 = 456
_My_data$32 = 464
$T33 = 472
$T34 = 480
$T35 = 488
$T36 = 496
_My_data$37 = 504
$T38 = 512
$T39 = 520
$T40 = 528
$T41 = 536
_My_data$42 = 544
$T43 = 552
$T44 = 560
$T45 = 568
$T46 = 576
_My_data$47 = 584
$T48 = 592
$T49 = 600
$T50 = 608
$T51 = 616
_My_data$52 = 624
$T53 = 632
$T54 = 640
$T55 = 648
$T56 = 656
_My_data$57 = 664
$T58 = 672
$T59 = 680
$T60 = 688
$T61 = 696
_My_data$62 = 704
$T63 = 712
$T64 = 720
$T65 = 728
$T66 = 736
_My_data$67 = 744
$T68 = 752
$T69 = 760
$T70 = 768
$T71 = 776
_My_data$72 = 784
$T73 = 792
$T74 = 800
$T75 = 808
$T76 = 816
_My_data$77 = 824
$T78 = 832
$T79 = 840
$T80 = 848
$T81 = 856
_My_data$82 = 864
$T83 = 872
$T84 = 880
$T85 = 888
$T86 = 896
_My_data$87 = 904
$T88 = 912
$T89 = 920
$T90 = 928
$T91 = 936
_My_data$92 = 944
$T93 = 952
$T94 = 960
$T95 = 968
$T96 = 976
_My_data$97 = 984
$T98 = 992
$T99 = 1000
$T100 = 1008
$T101 = 1016
_My_data$102 = 1024
$T103 = 1032
$T104 = 1040
$T105 = 1048
$T106 = 1056
_My_data$107 = 1064
$T108 = 1072
$T109 = 1080
$T110 = 1088
$T111 = 1096
_My_data$112 = 1104
$T113 = 1112
$T114 = 1120
$T115 = 1128
$T116 = 1136
_My_data$117 = 1144
$T118 = 1152
$T119 = 1160
$T120 = 1168
$T121 = 1176
_My_data$122 = 1184
$T123 = 1192
$T124 = 1200
$T125 = 1208
$T126 = 1216
_My_data$127 = 1224
$T128 = 1232
$T129 = 1240
$T130 = 1248
$T131 = 1256
_My_data$132 = 1264
$T133 = 1272
$T134 = 1280
this$ = 1312
thisServer$ = 1320
?dtor$6@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA PROC ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$6
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 68	 mov	 rcx, QWORD PTR $T7[rbp]
  0000d	e8 00 00 00 00	 call	 ??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::operator delete
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$6@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA ENDP ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$6
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 33
$T3 = 40
tv145 = 48
$T4 = 56
tv164 = 64
$T5 = 72
tv183 = 80
$T6 = 88
tv202 = 96
$T7 = 104
tv221 = 112
$T8 = 120
tv240 = 128
$T9 = 136
tv259 = 144
$T10 = 152
tv278 = 160
$T11 = 168
tv297 = 176
$T12 = 184
tv316 = 192
$T13 = 200
tv335 = 208
$T14 = 216
tv354 = 224
$T15 = 232
tv373 = 240
$T16 = 248
tv392 = 256
$T17 = 264
tv411 = 272
$T18 = 280
tv462 = 288
$T19 = 296
tv481 = 304
$T20 = 312
tv500 = 320
$T21 = 328
tv519 = 336
$T22 = 344
tv538 = 352
$T23 = 360
tv557 = 368
$T24 = 376
tv576 = 384
this$ = 392
this$ = 400
$T25 = 408
$T26 = 416
_My_data$27 = 424
$T28 = 432
$T29 = 440
$T30 = 448
$T31 = 456
_My_data$32 = 464
$T33 = 472
$T34 = 480
$T35 = 488
$T36 = 496
_My_data$37 = 504
$T38 = 512
$T39 = 520
$T40 = 528
$T41 = 536
_My_data$42 = 544
$T43 = 552
$T44 = 560
$T45 = 568
$T46 = 576
_My_data$47 = 584
$T48 = 592
$T49 = 600
$T50 = 608
$T51 = 616
_My_data$52 = 624
$T53 = 632
$T54 = 640
$T55 = 648
$T56 = 656
_My_data$57 = 664
$T58 = 672
$T59 = 680
$T60 = 688
$T61 = 696
_My_data$62 = 704
$T63 = 712
$T64 = 720
$T65 = 728
$T66 = 736
_My_data$67 = 744
$T68 = 752
$T69 = 760
$T70 = 768
$T71 = 776
_My_data$72 = 784
$T73 = 792
$T74 = 800
$T75 = 808
$T76 = 816
_My_data$77 = 824
$T78 = 832
$T79 = 840
$T80 = 848
$T81 = 856
_My_data$82 = 864
$T83 = 872
$T84 = 880
$T85 = 888
$T86 = 896
_My_data$87 = 904
$T88 = 912
$T89 = 920
$T90 = 928
$T91 = 936
_My_data$92 = 944
$T93 = 952
$T94 = 960
$T95 = 968
$T96 = 976
_My_data$97 = 984
$T98 = 992
$T99 = 1000
$T100 = 1008
$T101 = 1016
_My_data$102 = 1024
$T103 = 1032
$T104 = 1040
$T105 = 1048
$T106 = 1056
_My_data$107 = 1064
$T108 = 1072
$T109 = 1080
$T110 = 1088
$T111 = 1096
_My_data$112 = 1104
$T113 = 1112
$T114 = 1120
$T115 = 1128
$T116 = 1136
_My_data$117 = 1144
$T118 = 1152
$T119 = 1160
$T120 = 1168
$T121 = 1176
_My_data$122 = 1184
$T123 = 1192
$T124 = 1200
$T125 = 1208
$T126 = 1216
_My_data$127 = 1224
$T128 = 1232
$T129 = 1240
$T130 = 1248
$T131 = 1256
_My_data$132 = 1264
$T133 = 1272
$T134 = 1280
this$ = 1312
thisServer$ = 1320
?dtor$7@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA PROC ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$7
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 78	 mov	 rcx, QWORD PTR $T8[rbp]
  0000d	e8 00 00 00 00	 call	 ??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::operator delete
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$7@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA ENDP ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$7
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 33
$T3 = 40
tv145 = 48
$T4 = 56
tv164 = 64
$T5 = 72
tv183 = 80
$T6 = 88
tv202 = 96
$T7 = 104
tv221 = 112
$T8 = 120
tv240 = 128
$T9 = 136
tv259 = 144
$T10 = 152
tv278 = 160
$T11 = 168
tv297 = 176
$T12 = 184
tv316 = 192
$T13 = 200
tv335 = 208
$T14 = 216
tv354 = 224
$T15 = 232
tv373 = 240
$T16 = 248
tv392 = 256
$T17 = 264
tv411 = 272
$T18 = 280
tv462 = 288
$T19 = 296
tv481 = 304
$T20 = 312
tv500 = 320
$T21 = 328
tv519 = 336
$T22 = 344
tv538 = 352
$T23 = 360
tv557 = 368
$T24 = 376
tv576 = 384
this$ = 392
this$ = 400
$T25 = 408
$T26 = 416
_My_data$27 = 424
$T28 = 432
$T29 = 440
$T30 = 448
$T31 = 456
_My_data$32 = 464
$T33 = 472
$T34 = 480
$T35 = 488
$T36 = 496
_My_data$37 = 504
$T38 = 512
$T39 = 520
$T40 = 528
$T41 = 536
_My_data$42 = 544
$T43 = 552
$T44 = 560
$T45 = 568
$T46 = 576
_My_data$47 = 584
$T48 = 592
$T49 = 600
$T50 = 608
$T51 = 616
_My_data$52 = 624
$T53 = 632
$T54 = 640
$T55 = 648
$T56 = 656
_My_data$57 = 664
$T58 = 672
$T59 = 680
$T60 = 688
$T61 = 696
_My_data$62 = 704
$T63 = 712
$T64 = 720
$T65 = 728
$T66 = 736
_My_data$67 = 744
$T68 = 752
$T69 = 760
$T70 = 768
$T71 = 776
_My_data$72 = 784
$T73 = 792
$T74 = 800
$T75 = 808
$T76 = 816
_My_data$77 = 824
$T78 = 832
$T79 = 840
$T80 = 848
$T81 = 856
_My_data$82 = 864
$T83 = 872
$T84 = 880
$T85 = 888
$T86 = 896
_My_data$87 = 904
$T88 = 912
$T89 = 920
$T90 = 928
$T91 = 936
_My_data$92 = 944
$T93 = 952
$T94 = 960
$T95 = 968
$T96 = 976
_My_data$97 = 984
$T98 = 992
$T99 = 1000
$T100 = 1008
$T101 = 1016
_My_data$102 = 1024
$T103 = 1032
$T104 = 1040
$T105 = 1048
$T106 = 1056
_My_data$107 = 1064
$T108 = 1072
$T109 = 1080
$T110 = 1088
$T111 = 1096
_My_data$112 = 1104
$T113 = 1112
$T114 = 1120
$T115 = 1128
$T116 = 1136
_My_data$117 = 1144
$T118 = 1152
$T119 = 1160
$T120 = 1168
$T121 = 1176
_My_data$122 = 1184
$T123 = 1192
$T124 = 1200
$T125 = 1208
$T126 = 1216
_My_data$127 = 1224
$T128 = 1232
$T129 = 1240
$T130 = 1248
$T131 = 1256
_My_data$132 = 1264
$T133 = 1272
$T134 = 1280
this$ = 1312
thisServer$ = 1320
?dtor$8@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA PROC ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$8
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 8d 88 00
	00 00		 mov	 rcx, QWORD PTR $T9[rbp]
  00010	e8 00 00 00 00	 call	 ??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::operator delete
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$8@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA ENDP ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$8
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 33
$T3 = 40
tv145 = 48
$T4 = 56
tv164 = 64
$T5 = 72
tv183 = 80
$T6 = 88
tv202 = 96
$T7 = 104
tv221 = 112
$T8 = 120
tv240 = 128
$T9 = 136
tv259 = 144
$T10 = 152
tv278 = 160
$T11 = 168
tv297 = 176
$T12 = 184
tv316 = 192
$T13 = 200
tv335 = 208
$T14 = 216
tv354 = 224
$T15 = 232
tv373 = 240
$T16 = 248
tv392 = 256
$T17 = 264
tv411 = 272
$T18 = 280
tv462 = 288
$T19 = 296
tv481 = 304
$T20 = 312
tv500 = 320
$T21 = 328
tv519 = 336
$T22 = 344
tv538 = 352
$T23 = 360
tv557 = 368
$T24 = 376
tv576 = 384
this$ = 392
this$ = 400
$T25 = 408
$T26 = 416
_My_data$27 = 424
$T28 = 432
$T29 = 440
$T30 = 448
$T31 = 456
_My_data$32 = 464
$T33 = 472
$T34 = 480
$T35 = 488
$T36 = 496
_My_data$37 = 504
$T38 = 512
$T39 = 520
$T40 = 528
$T41 = 536
_My_data$42 = 544
$T43 = 552
$T44 = 560
$T45 = 568
$T46 = 576
_My_data$47 = 584
$T48 = 592
$T49 = 600
$T50 = 608
$T51 = 616
_My_data$52 = 624
$T53 = 632
$T54 = 640
$T55 = 648
$T56 = 656
_My_data$57 = 664
$T58 = 672
$T59 = 680
$T60 = 688
$T61 = 696
_My_data$62 = 704
$T63 = 712
$T64 = 720
$T65 = 728
$T66 = 736
_My_data$67 = 744
$T68 = 752
$T69 = 760
$T70 = 768
$T71 = 776
_My_data$72 = 784
$T73 = 792
$T74 = 800
$T75 = 808
$T76 = 816
_My_data$77 = 824
$T78 = 832
$T79 = 840
$T80 = 848
$T81 = 856
_My_data$82 = 864
$T83 = 872
$T84 = 880
$T85 = 888
$T86 = 896
_My_data$87 = 904
$T88 = 912
$T89 = 920
$T90 = 928
$T91 = 936
_My_data$92 = 944
$T93 = 952
$T94 = 960
$T95 = 968
$T96 = 976
_My_data$97 = 984
$T98 = 992
$T99 = 1000
$T100 = 1008
$T101 = 1016
_My_data$102 = 1024
$T103 = 1032
$T104 = 1040
$T105 = 1048
$T106 = 1056
_My_data$107 = 1064
$T108 = 1072
$T109 = 1080
$T110 = 1088
$T111 = 1096
_My_data$112 = 1104
$T113 = 1112
$T114 = 1120
$T115 = 1128
$T116 = 1136
_My_data$117 = 1144
$T118 = 1152
$T119 = 1160
$T120 = 1168
$T121 = 1176
_My_data$122 = 1184
$T123 = 1192
$T124 = 1200
$T125 = 1208
$T126 = 1216
_My_data$127 = 1224
$T128 = 1232
$T129 = 1240
$T130 = 1248
$T131 = 1256
_My_data$132 = 1264
$T133 = 1272
$T134 = 1280
this$ = 1312
thisServer$ = 1320
?dtor$9@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA PROC ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$9
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 8d 98 00
	00 00		 mov	 rcx, QWORD PTR $T10[rbp]
  00010	e8 00 00 00 00	 call	 ??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::operator delete
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$9@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA ENDP ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$9
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 33
$T3 = 40
tv145 = 48
$T4 = 56
tv164 = 64
$T5 = 72
tv183 = 80
$T6 = 88
tv202 = 96
$T7 = 104
tv221 = 112
$T8 = 120
tv240 = 128
$T9 = 136
tv259 = 144
$T10 = 152
tv278 = 160
$T11 = 168
tv297 = 176
$T12 = 184
tv316 = 192
$T13 = 200
tv335 = 208
$T14 = 216
tv354 = 224
$T15 = 232
tv373 = 240
$T16 = 248
tv392 = 256
$T17 = 264
tv411 = 272
$T18 = 280
tv462 = 288
$T19 = 296
tv481 = 304
$T20 = 312
tv500 = 320
$T21 = 328
tv519 = 336
$T22 = 344
tv538 = 352
$T23 = 360
tv557 = 368
$T24 = 376
tv576 = 384
this$ = 392
this$ = 400
$T25 = 408
$T26 = 416
_My_data$27 = 424
$T28 = 432
$T29 = 440
$T30 = 448
$T31 = 456
_My_data$32 = 464
$T33 = 472
$T34 = 480
$T35 = 488
$T36 = 496
_My_data$37 = 504
$T38 = 512
$T39 = 520
$T40 = 528
$T41 = 536
_My_data$42 = 544
$T43 = 552
$T44 = 560
$T45 = 568
$T46 = 576
_My_data$47 = 584
$T48 = 592
$T49 = 600
$T50 = 608
$T51 = 616
_My_data$52 = 624
$T53 = 632
$T54 = 640
$T55 = 648
$T56 = 656
_My_data$57 = 664
$T58 = 672
$T59 = 680
$T60 = 688
$T61 = 696
_My_data$62 = 704
$T63 = 712
$T64 = 720
$T65 = 728
$T66 = 736
_My_data$67 = 744
$T68 = 752
$T69 = 760
$T70 = 768
$T71 = 776
_My_data$72 = 784
$T73 = 792
$T74 = 800
$T75 = 808
$T76 = 816
_My_data$77 = 824
$T78 = 832
$T79 = 840
$T80 = 848
$T81 = 856
_My_data$82 = 864
$T83 = 872
$T84 = 880
$T85 = 888
$T86 = 896
_My_data$87 = 904
$T88 = 912
$T89 = 920
$T90 = 928
$T91 = 936
_My_data$92 = 944
$T93 = 952
$T94 = 960
$T95 = 968
$T96 = 976
_My_data$97 = 984
$T98 = 992
$T99 = 1000
$T100 = 1008
$T101 = 1016
_My_data$102 = 1024
$T103 = 1032
$T104 = 1040
$T105 = 1048
$T106 = 1056
_My_data$107 = 1064
$T108 = 1072
$T109 = 1080
$T110 = 1088
$T111 = 1096
_My_data$112 = 1104
$T113 = 1112
$T114 = 1120
$T115 = 1128
$T116 = 1136
_My_data$117 = 1144
$T118 = 1152
$T119 = 1160
$T120 = 1168
$T121 = 1176
_My_data$122 = 1184
$T123 = 1192
$T124 = 1200
$T125 = 1208
$T126 = 1216
_My_data$127 = 1224
$T128 = 1232
$T129 = 1240
$T130 = 1248
$T131 = 1256
_My_data$132 = 1264
$T133 = 1272
$T134 = 1280
this$ = 1312
thisServer$ = 1320
?dtor$10@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA PROC ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$10
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 8d a8 00
	00 00		 mov	 rcx, QWORD PTR $T11[rbp]
  00010	e8 00 00 00 00	 call	 ??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::operator delete
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$10@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA ENDP ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$10
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 33
$T3 = 40
tv145 = 48
$T4 = 56
tv164 = 64
$T5 = 72
tv183 = 80
$T6 = 88
tv202 = 96
$T7 = 104
tv221 = 112
$T8 = 120
tv240 = 128
$T9 = 136
tv259 = 144
$T10 = 152
tv278 = 160
$T11 = 168
tv297 = 176
$T12 = 184
tv316 = 192
$T13 = 200
tv335 = 208
$T14 = 216
tv354 = 224
$T15 = 232
tv373 = 240
$T16 = 248
tv392 = 256
$T17 = 264
tv411 = 272
$T18 = 280
tv462 = 288
$T19 = 296
tv481 = 304
$T20 = 312
tv500 = 320
$T21 = 328
tv519 = 336
$T22 = 344
tv538 = 352
$T23 = 360
tv557 = 368
$T24 = 376
tv576 = 384
this$ = 392
this$ = 400
$T25 = 408
$T26 = 416
_My_data$27 = 424
$T28 = 432
$T29 = 440
$T30 = 448
$T31 = 456
_My_data$32 = 464
$T33 = 472
$T34 = 480
$T35 = 488
$T36 = 496
_My_data$37 = 504
$T38 = 512
$T39 = 520
$T40 = 528
$T41 = 536
_My_data$42 = 544
$T43 = 552
$T44 = 560
$T45 = 568
$T46 = 576
_My_data$47 = 584
$T48 = 592
$T49 = 600
$T50 = 608
$T51 = 616
_My_data$52 = 624
$T53 = 632
$T54 = 640
$T55 = 648
$T56 = 656
_My_data$57 = 664
$T58 = 672
$T59 = 680
$T60 = 688
$T61 = 696
_My_data$62 = 704
$T63 = 712
$T64 = 720
$T65 = 728
$T66 = 736
_My_data$67 = 744
$T68 = 752
$T69 = 760
$T70 = 768
$T71 = 776
_My_data$72 = 784
$T73 = 792
$T74 = 800
$T75 = 808
$T76 = 816
_My_data$77 = 824
$T78 = 832
$T79 = 840
$T80 = 848
$T81 = 856
_My_data$82 = 864
$T83 = 872
$T84 = 880
$T85 = 888
$T86 = 896
_My_data$87 = 904
$T88 = 912
$T89 = 920
$T90 = 928
$T91 = 936
_My_data$92 = 944
$T93 = 952
$T94 = 960
$T95 = 968
$T96 = 976
_My_data$97 = 984
$T98 = 992
$T99 = 1000
$T100 = 1008
$T101 = 1016
_My_data$102 = 1024
$T103 = 1032
$T104 = 1040
$T105 = 1048
$T106 = 1056
_My_data$107 = 1064
$T108 = 1072
$T109 = 1080
$T110 = 1088
$T111 = 1096
_My_data$112 = 1104
$T113 = 1112
$T114 = 1120
$T115 = 1128
$T116 = 1136
_My_data$117 = 1144
$T118 = 1152
$T119 = 1160
$T120 = 1168
$T121 = 1176
_My_data$122 = 1184
$T123 = 1192
$T124 = 1200
$T125 = 1208
$T126 = 1216
_My_data$127 = 1224
$T128 = 1232
$T129 = 1240
$T130 = 1248
$T131 = 1256
_My_data$132 = 1264
$T133 = 1272
$T134 = 1280
this$ = 1312
thisServer$ = 1320
?dtor$11@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA PROC ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$11
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 8d b8 00
	00 00		 mov	 rcx, QWORD PTR $T12[rbp]
  00010	e8 00 00 00 00	 call	 ??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::operator delete
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$11@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA ENDP ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$11
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 33
$T3 = 40
tv145 = 48
$T4 = 56
tv164 = 64
$T5 = 72
tv183 = 80
$T6 = 88
tv202 = 96
$T7 = 104
tv221 = 112
$T8 = 120
tv240 = 128
$T9 = 136
tv259 = 144
$T10 = 152
tv278 = 160
$T11 = 168
tv297 = 176
$T12 = 184
tv316 = 192
$T13 = 200
tv335 = 208
$T14 = 216
tv354 = 224
$T15 = 232
tv373 = 240
$T16 = 248
tv392 = 256
$T17 = 264
tv411 = 272
$T18 = 280
tv462 = 288
$T19 = 296
tv481 = 304
$T20 = 312
tv500 = 320
$T21 = 328
tv519 = 336
$T22 = 344
tv538 = 352
$T23 = 360
tv557 = 368
$T24 = 376
tv576 = 384
this$ = 392
this$ = 400
$T25 = 408
$T26 = 416
_My_data$27 = 424
$T28 = 432
$T29 = 440
$T30 = 448
$T31 = 456
_My_data$32 = 464
$T33 = 472
$T34 = 480
$T35 = 488
$T36 = 496
_My_data$37 = 504
$T38 = 512
$T39 = 520
$T40 = 528
$T41 = 536
_My_data$42 = 544
$T43 = 552
$T44 = 560
$T45 = 568
$T46 = 576
_My_data$47 = 584
$T48 = 592
$T49 = 600
$T50 = 608
$T51 = 616
_My_data$52 = 624
$T53 = 632
$T54 = 640
$T55 = 648
$T56 = 656
_My_data$57 = 664
$T58 = 672
$T59 = 680
$T60 = 688
$T61 = 696
_My_data$62 = 704
$T63 = 712
$T64 = 720
$T65 = 728
$T66 = 736
_My_data$67 = 744
$T68 = 752
$T69 = 760
$T70 = 768
$T71 = 776
_My_data$72 = 784
$T73 = 792
$T74 = 800
$T75 = 808
$T76 = 816
_My_data$77 = 824
$T78 = 832
$T79 = 840
$T80 = 848
$T81 = 856
_My_data$82 = 864
$T83 = 872
$T84 = 880
$T85 = 888
$T86 = 896
_My_data$87 = 904
$T88 = 912
$T89 = 920
$T90 = 928
$T91 = 936
_My_data$92 = 944
$T93 = 952
$T94 = 960
$T95 = 968
$T96 = 976
_My_data$97 = 984
$T98 = 992
$T99 = 1000
$T100 = 1008
$T101 = 1016
_My_data$102 = 1024
$T103 = 1032
$T104 = 1040
$T105 = 1048
$T106 = 1056
_My_data$107 = 1064
$T108 = 1072
$T109 = 1080
$T110 = 1088
$T111 = 1096
_My_data$112 = 1104
$T113 = 1112
$T114 = 1120
$T115 = 1128
$T116 = 1136
_My_data$117 = 1144
$T118 = 1152
$T119 = 1160
$T120 = 1168
$T121 = 1176
_My_data$122 = 1184
$T123 = 1192
$T124 = 1200
$T125 = 1208
$T126 = 1216
_My_data$127 = 1224
$T128 = 1232
$T129 = 1240
$T130 = 1248
$T131 = 1256
_My_data$132 = 1264
$T133 = 1272
$T134 = 1280
this$ = 1312
thisServer$ = 1320
?dtor$12@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA PROC ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$12
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 8d c8 00
	00 00		 mov	 rcx, QWORD PTR $T13[rbp]
  00010	e8 00 00 00 00	 call	 ??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::operator delete
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$12@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA ENDP ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$12
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 33
$T3 = 40
tv145 = 48
$T4 = 56
tv164 = 64
$T5 = 72
tv183 = 80
$T6 = 88
tv202 = 96
$T7 = 104
tv221 = 112
$T8 = 120
tv240 = 128
$T9 = 136
tv259 = 144
$T10 = 152
tv278 = 160
$T11 = 168
tv297 = 176
$T12 = 184
tv316 = 192
$T13 = 200
tv335 = 208
$T14 = 216
tv354 = 224
$T15 = 232
tv373 = 240
$T16 = 248
tv392 = 256
$T17 = 264
tv411 = 272
$T18 = 280
tv462 = 288
$T19 = 296
tv481 = 304
$T20 = 312
tv500 = 320
$T21 = 328
tv519 = 336
$T22 = 344
tv538 = 352
$T23 = 360
tv557 = 368
$T24 = 376
tv576 = 384
this$ = 392
this$ = 400
$T25 = 408
$T26 = 416
_My_data$27 = 424
$T28 = 432
$T29 = 440
$T30 = 448
$T31 = 456
_My_data$32 = 464
$T33 = 472
$T34 = 480
$T35 = 488
$T36 = 496
_My_data$37 = 504
$T38 = 512
$T39 = 520
$T40 = 528
$T41 = 536
_My_data$42 = 544
$T43 = 552
$T44 = 560
$T45 = 568
$T46 = 576
_My_data$47 = 584
$T48 = 592
$T49 = 600
$T50 = 608
$T51 = 616
_My_data$52 = 624
$T53 = 632
$T54 = 640
$T55 = 648
$T56 = 656
_My_data$57 = 664
$T58 = 672
$T59 = 680
$T60 = 688
$T61 = 696
_My_data$62 = 704
$T63 = 712
$T64 = 720
$T65 = 728
$T66 = 736
_My_data$67 = 744
$T68 = 752
$T69 = 760
$T70 = 768
$T71 = 776
_My_data$72 = 784
$T73 = 792
$T74 = 800
$T75 = 808
$T76 = 816
_My_data$77 = 824
$T78 = 832
$T79 = 840
$T80 = 848
$T81 = 856
_My_data$82 = 864
$T83 = 872
$T84 = 880
$T85 = 888
$T86 = 896
_My_data$87 = 904
$T88 = 912
$T89 = 920
$T90 = 928
$T91 = 936
_My_data$92 = 944
$T93 = 952
$T94 = 960
$T95 = 968
$T96 = 976
_My_data$97 = 984
$T98 = 992
$T99 = 1000
$T100 = 1008
$T101 = 1016
_My_data$102 = 1024
$T103 = 1032
$T104 = 1040
$T105 = 1048
$T106 = 1056
_My_data$107 = 1064
$T108 = 1072
$T109 = 1080
$T110 = 1088
$T111 = 1096
_My_data$112 = 1104
$T113 = 1112
$T114 = 1120
$T115 = 1128
$T116 = 1136
_My_data$117 = 1144
$T118 = 1152
$T119 = 1160
$T120 = 1168
$T121 = 1176
_My_data$122 = 1184
$T123 = 1192
$T124 = 1200
$T125 = 1208
$T126 = 1216
_My_data$127 = 1224
$T128 = 1232
$T129 = 1240
$T130 = 1248
$T131 = 1256
_My_data$132 = 1264
$T133 = 1272
$T134 = 1280
this$ = 1312
thisServer$ = 1320
?dtor$13@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA PROC ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$13
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 8d d8 00
	00 00		 mov	 rcx, QWORD PTR $T14[rbp]
  00010	e8 00 00 00 00	 call	 ??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::operator delete
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$13@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA ENDP ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$13
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 33
$T3 = 40
tv145 = 48
$T4 = 56
tv164 = 64
$T5 = 72
tv183 = 80
$T6 = 88
tv202 = 96
$T7 = 104
tv221 = 112
$T8 = 120
tv240 = 128
$T9 = 136
tv259 = 144
$T10 = 152
tv278 = 160
$T11 = 168
tv297 = 176
$T12 = 184
tv316 = 192
$T13 = 200
tv335 = 208
$T14 = 216
tv354 = 224
$T15 = 232
tv373 = 240
$T16 = 248
tv392 = 256
$T17 = 264
tv411 = 272
$T18 = 280
tv462 = 288
$T19 = 296
tv481 = 304
$T20 = 312
tv500 = 320
$T21 = 328
tv519 = 336
$T22 = 344
tv538 = 352
$T23 = 360
tv557 = 368
$T24 = 376
tv576 = 384
this$ = 392
this$ = 400
$T25 = 408
$T26 = 416
_My_data$27 = 424
$T28 = 432
$T29 = 440
$T30 = 448
$T31 = 456
_My_data$32 = 464
$T33 = 472
$T34 = 480
$T35 = 488
$T36 = 496
_My_data$37 = 504
$T38 = 512
$T39 = 520
$T40 = 528
$T41 = 536
_My_data$42 = 544
$T43 = 552
$T44 = 560
$T45 = 568
$T46 = 576
_My_data$47 = 584
$T48 = 592
$T49 = 600
$T50 = 608
$T51 = 616
_My_data$52 = 624
$T53 = 632
$T54 = 640
$T55 = 648
$T56 = 656
_My_data$57 = 664
$T58 = 672
$T59 = 680
$T60 = 688
$T61 = 696
_My_data$62 = 704
$T63 = 712
$T64 = 720
$T65 = 728
$T66 = 736
_My_data$67 = 744
$T68 = 752
$T69 = 760
$T70 = 768
$T71 = 776
_My_data$72 = 784
$T73 = 792
$T74 = 800
$T75 = 808
$T76 = 816
_My_data$77 = 824
$T78 = 832
$T79 = 840
$T80 = 848
$T81 = 856
_My_data$82 = 864
$T83 = 872
$T84 = 880
$T85 = 888
$T86 = 896
_My_data$87 = 904
$T88 = 912
$T89 = 920
$T90 = 928
$T91 = 936
_My_data$92 = 944
$T93 = 952
$T94 = 960
$T95 = 968
$T96 = 976
_My_data$97 = 984
$T98 = 992
$T99 = 1000
$T100 = 1008
$T101 = 1016
_My_data$102 = 1024
$T103 = 1032
$T104 = 1040
$T105 = 1048
$T106 = 1056
_My_data$107 = 1064
$T108 = 1072
$T109 = 1080
$T110 = 1088
$T111 = 1096
_My_data$112 = 1104
$T113 = 1112
$T114 = 1120
$T115 = 1128
$T116 = 1136
_My_data$117 = 1144
$T118 = 1152
$T119 = 1160
$T120 = 1168
$T121 = 1176
_My_data$122 = 1184
$T123 = 1192
$T124 = 1200
$T125 = 1208
$T126 = 1216
_My_data$127 = 1224
$T128 = 1232
$T129 = 1240
$T130 = 1248
$T131 = 1256
_My_data$132 = 1264
$T133 = 1272
$T134 = 1280
this$ = 1312
thisServer$ = 1320
?dtor$14@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA PROC ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$14
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 8d e8 00
	00 00		 mov	 rcx, QWORD PTR $T15[rbp]
  00010	e8 00 00 00 00	 call	 ??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::operator delete
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$14@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA ENDP ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$14
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 33
$T3 = 40
tv145 = 48
$T4 = 56
tv164 = 64
$T5 = 72
tv183 = 80
$T6 = 88
tv202 = 96
$T7 = 104
tv221 = 112
$T8 = 120
tv240 = 128
$T9 = 136
tv259 = 144
$T10 = 152
tv278 = 160
$T11 = 168
tv297 = 176
$T12 = 184
tv316 = 192
$T13 = 200
tv335 = 208
$T14 = 216
tv354 = 224
$T15 = 232
tv373 = 240
$T16 = 248
tv392 = 256
$T17 = 264
tv411 = 272
$T18 = 280
tv462 = 288
$T19 = 296
tv481 = 304
$T20 = 312
tv500 = 320
$T21 = 328
tv519 = 336
$T22 = 344
tv538 = 352
$T23 = 360
tv557 = 368
$T24 = 376
tv576 = 384
this$ = 392
this$ = 400
$T25 = 408
$T26 = 416
_My_data$27 = 424
$T28 = 432
$T29 = 440
$T30 = 448
$T31 = 456
_My_data$32 = 464
$T33 = 472
$T34 = 480
$T35 = 488
$T36 = 496
_My_data$37 = 504
$T38 = 512
$T39 = 520
$T40 = 528
$T41 = 536
_My_data$42 = 544
$T43 = 552
$T44 = 560
$T45 = 568
$T46 = 576
_My_data$47 = 584
$T48 = 592
$T49 = 600
$T50 = 608
$T51 = 616
_My_data$52 = 624
$T53 = 632
$T54 = 640
$T55 = 648
$T56 = 656
_My_data$57 = 664
$T58 = 672
$T59 = 680
$T60 = 688
$T61 = 696
_My_data$62 = 704
$T63 = 712
$T64 = 720
$T65 = 728
$T66 = 736
_My_data$67 = 744
$T68 = 752
$T69 = 760
$T70 = 768
$T71 = 776
_My_data$72 = 784
$T73 = 792
$T74 = 800
$T75 = 808
$T76 = 816
_My_data$77 = 824
$T78 = 832
$T79 = 840
$T80 = 848
$T81 = 856
_My_data$82 = 864
$T83 = 872
$T84 = 880
$T85 = 888
$T86 = 896
_My_data$87 = 904
$T88 = 912
$T89 = 920
$T90 = 928
$T91 = 936
_My_data$92 = 944
$T93 = 952
$T94 = 960
$T95 = 968
$T96 = 976
_My_data$97 = 984
$T98 = 992
$T99 = 1000
$T100 = 1008
$T101 = 1016
_My_data$102 = 1024
$T103 = 1032
$T104 = 1040
$T105 = 1048
$T106 = 1056
_My_data$107 = 1064
$T108 = 1072
$T109 = 1080
$T110 = 1088
$T111 = 1096
_My_data$112 = 1104
$T113 = 1112
$T114 = 1120
$T115 = 1128
$T116 = 1136
_My_data$117 = 1144
$T118 = 1152
$T119 = 1160
$T120 = 1168
$T121 = 1176
_My_data$122 = 1184
$T123 = 1192
$T124 = 1200
$T125 = 1208
$T126 = 1216
_My_data$127 = 1224
$T128 = 1232
$T129 = 1240
$T130 = 1248
$T131 = 1256
_My_data$132 = 1264
$T133 = 1272
$T134 = 1280
this$ = 1312
thisServer$ = 1320
?dtor$15@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA PROC ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$15
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 8d f8 00
	00 00		 mov	 rcx, QWORD PTR $T16[rbp]
  00010	e8 00 00 00 00	 call	 ??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::operator delete
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$15@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA ENDP ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$15
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 33
$T3 = 40
tv145 = 48
$T4 = 56
tv164 = 64
$T5 = 72
tv183 = 80
$T6 = 88
tv202 = 96
$T7 = 104
tv221 = 112
$T8 = 120
tv240 = 128
$T9 = 136
tv259 = 144
$T10 = 152
tv278 = 160
$T11 = 168
tv297 = 176
$T12 = 184
tv316 = 192
$T13 = 200
tv335 = 208
$T14 = 216
tv354 = 224
$T15 = 232
tv373 = 240
$T16 = 248
tv392 = 256
$T17 = 264
tv411 = 272
$T18 = 280
tv462 = 288
$T19 = 296
tv481 = 304
$T20 = 312
tv500 = 320
$T21 = 328
tv519 = 336
$T22 = 344
tv538 = 352
$T23 = 360
tv557 = 368
$T24 = 376
tv576 = 384
this$ = 392
this$ = 400
$T25 = 408
$T26 = 416
_My_data$27 = 424
$T28 = 432
$T29 = 440
$T30 = 448
$T31 = 456
_My_data$32 = 464
$T33 = 472
$T34 = 480
$T35 = 488
$T36 = 496
_My_data$37 = 504
$T38 = 512
$T39 = 520
$T40 = 528
$T41 = 536
_My_data$42 = 544
$T43 = 552
$T44 = 560
$T45 = 568
$T46 = 576
_My_data$47 = 584
$T48 = 592
$T49 = 600
$T50 = 608
$T51 = 616
_My_data$52 = 624
$T53 = 632
$T54 = 640
$T55 = 648
$T56 = 656
_My_data$57 = 664
$T58 = 672
$T59 = 680
$T60 = 688
$T61 = 696
_My_data$62 = 704
$T63 = 712
$T64 = 720
$T65 = 728
$T66 = 736
_My_data$67 = 744
$T68 = 752
$T69 = 760
$T70 = 768
$T71 = 776
_My_data$72 = 784
$T73 = 792
$T74 = 800
$T75 = 808
$T76 = 816
_My_data$77 = 824
$T78 = 832
$T79 = 840
$T80 = 848
$T81 = 856
_My_data$82 = 864
$T83 = 872
$T84 = 880
$T85 = 888
$T86 = 896
_My_data$87 = 904
$T88 = 912
$T89 = 920
$T90 = 928
$T91 = 936
_My_data$92 = 944
$T93 = 952
$T94 = 960
$T95 = 968
$T96 = 976
_My_data$97 = 984
$T98 = 992
$T99 = 1000
$T100 = 1008
$T101 = 1016
_My_data$102 = 1024
$T103 = 1032
$T104 = 1040
$T105 = 1048
$T106 = 1056
_My_data$107 = 1064
$T108 = 1072
$T109 = 1080
$T110 = 1088
$T111 = 1096
_My_data$112 = 1104
$T113 = 1112
$T114 = 1120
$T115 = 1128
$T116 = 1136
_My_data$117 = 1144
$T118 = 1152
$T119 = 1160
$T120 = 1168
$T121 = 1176
_My_data$122 = 1184
$T123 = 1192
$T124 = 1200
$T125 = 1208
$T126 = 1216
_My_data$127 = 1224
$T128 = 1232
$T129 = 1240
$T130 = 1248
$T131 = 1256
_My_data$132 = 1264
$T133 = 1272
$T134 = 1280
this$ = 1312
thisServer$ = 1320
?dtor$16@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA PROC ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$16
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 8d 08 01
	00 00		 mov	 rcx, QWORD PTR $T17[rbp]
  00010	e8 00 00 00 00	 call	 ??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::operator delete
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$16@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA ENDP ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$16
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 33
$T3 = 40
tv145 = 48
$T4 = 56
tv164 = 64
$T5 = 72
tv183 = 80
$T6 = 88
tv202 = 96
$T7 = 104
tv221 = 112
$T8 = 120
tv240 = 128
$T9 = 136
tv259 = 144
$T10 = 152
tv278 = 160
$T11 = 168
tv297 = 176
$T12 = 184
tv316 = 192
$T13 = 200
tv335 = 208
$T14 = 216
tv354 = 224
$T15 = 232
tv373 = 240
$T16 = 248
tv392 = 256
$T17 = 264
tv411 = 272
$T18 = 280
tv462 = 288
$T19 = 296
tv481 = 304
$T20 = 312
tv500 = 320
$T21 = 328
tv519 = 336
$T22 = 344
tv538 = 352
$T23 = 360
tv557 = 368
$T24 = 376
tv576 = 384
this$ = 392
this$ = 400
$T25 = 408
$T26 = 416
_My_data$27 = 424
$T28 = 432
$T29 = 440
$T30 = 448
$T31 = 456
_My_data$32 = 464
$T33 = 472
$T34 = 480
$T35 = 488
$T36 = 496
_My_data$37 = 504
$T38 = 512
$T39 = 520
$T40 = 528
$T41 = 536
_My_data$42 = 544
$T43 = 552
$T44 = 560
$T45 = 568
$T46 = 576
_My_data$47 = 584
$T48 = 592
$T49 = 600
$T50 = 608
$T51 = 616
_My_data$52 = 624
$T53 = 632
$T54 = 640
$T55 = 648
$T56 = 656
_My_data$57 = 664
$T58 = 672
$T59 = 680
$T60 = 688
$T61 = 696
_My_data$62 = 704
$T63 = 712
$T64 = 720
$T65 = 728
$T66 = 736
_My_data$67 = 744
$T68 = 752
$T69 = 760
$T70 = 768
$T71 = 776
_My_data$72 = 784
$T73 = 792
$T74 = 800
$T75 = 808
$T76 = 816
_My_data$77 = 824
$T78 = 832
$T79 = 840
$T80 = 848
$T81 = 856
_My_data$82 = 864
$T83 = 872
$T84 = 880
$T85 = 888
$T86 = 896
_My_data$87 = 904
$T88 = 912
$T89 = 920
$T90 = 928
$T91 = 936
_My_data$92 = 944
$T93 = 952
$T94 = 960
$T95 = 968
$T96 = 976
_My_data$97 = 984
$T98 = 992
$T99 = 1000
$T100 = 1008
$T101 = 1016
_My_data$102 = 1024
$T103 = 1032
$T104 = 1040
$T105 = 1048
$T106 = 1056
_My_data$107 = 1064
$T108 = 1072
$T109 = 1080
$T110 = 1088
$T111 = 1096
_My_data$112 = 1104
$T113 = 1112
$T114 = 1120
$T115 = 1128
$T116 = 1136
_My_data$117 = 1144
$T118 = 1152
$T119 = 1160
$T120 = 1168
$T121 = 1176
_My_data$122 = 1184
$T123 = 1192
$T124 = 1200
$T125 = 1208
$T126 = 1216
_My_data$127 = 1224
$T128 = 1232
$T129 = 1240
$T130 = 1248
$T131 = 1256
_My_data$132 = 1264
$T133 = 1272
$T134 = 1280
this$ = 1312
thisServer$ = 1320
?dtor$17@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA PROC ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$17
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 8d 18 01
	00 00		 mov	 rcx, QWORD PTR $T18[rbp]
  00010	e8 00 00 00 00	 call	 ??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::operator delete
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$17@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA ENDP ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$17
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 33
$T3 = 40
tv145 = 48
$T4 = 56
tv164 = 64
$T5 = 72
tv183 = 80
$T6 = 88
tv202 = 96
$T7 = 104
tv221 = 112
$T8 = 120
tv240 = 128
$T9 = 136
tv259 = 144
$T10 = 152
tv278 = 160
$T11 = 168
tv297 = 176
$T12 = 184
tv316 = 192
$T13 = 200
tv335 = 208
$T14 = 216
tv354 = 224
$T15 = 232
tv373 = 240
$T16 = 248
tv392 = 256
$T17 = 264
tv411 = 272
$T18 = 280
tv462 = 288
$T19 = 296
tv481 = 304
$T20 = 312
tv500 = 320
$T21 = 328
tv519 = 336
$T22 = 344
tv538 = 352
$T23 = 360
tv557 = 368
$T24 = 376
tv576 = 384
this$ = 392
this$ = 400
$T25 = 408
$T26 = 416
_My_data$27 = 424
$T28 = 432
$T29 = 440
$T30 = 448
$T31 = 456
_My_data$32 = 464
$T33 = 472
$T34 = 480
$T35 = 488
$T36 = 496
_My_data$37 = 504
$T38 = 512
$T39 = 520
$T40 = 528
$T41 = 536
_My_data$42 = 544
$T43 = 552
$T44 = 560
$T45 = 568
$T46 = 576
_My_data$47 = 584
$T48 = 592
$T49 = 600
$T50 = 608
$T51 = 616
_My_data$52 = 624
$T53 = 632
$T54 = 640
$T55 = 648
$T56 = 656
_My_data$57 = 664
$T58 = 672
$T59 = 680
$T60 = 688
$T61 = 696
_My_data$62 = 704
$T63 = 712
$T64 = 720
$T65 = 728
$T66 = 736
_My_data$67 = 744
$T68 = 752
$T69 = 760
$T70 = 768
$T71 = 776
_My_data$72 = 784
$T73 = 792
$T74 = 800
$T75 = 808
$T76 = 816
_My_data$77 = 824
$T78 = 832
$T79 = 840
$T80 = 848
$T81 = 856
_My_data$82 = 864
$T83 = 872
$T84 = 880
$T85 = 888
$T86 = 896
_My_data$87 = 904
$T88 = 912
$T89 = 920
$T90 = 928
$T91 = 936
_My_data$92 = 944
$T93 = 952
$T94 = 960
$T95 = 968
$T96 = 976
_My_data$97 = 984
$T98 = 992
$T99 = 1000
$T100 = 1008
$T101 = 1016
_My_data$102 = 1024
$T103 = 1032
$T104 = 1040
$T105 = 1048
$T106 = 1056
_My_data$107 = 1064
$T108 = 1072
$T109 = 1080
$T110 = 1088
$T111 = 1096
_My_data$112 = 1104
$T113 = 1112
$T114 = 1120
$T115 = 1128
$T116 = 1136
_My_data$117 = 1144
$T118 = 1152
$T119 = 1160
$T120 = 1168
$T121 = 1176
_My_data$122 = 1184
$T123 = 1192
$T124 = 1200
$T125 = 1208
$T126 = 1216
_My_data$127 = 1224
$T128 = 1232
$T129 = 1240
$T130 = 1248
$T131 = 1256
_My_data$132 = 1264
$T133 = 1272
$T134 = 1280
this$ = 1312
thisServer$ = 1320
?dtor$18@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA PROC ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$18
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 8d 28 01
	00 00		 mov	 rcx, QWORD PTR $T19[rbp]
  00010	e8 00 00 00 00	 call	 ??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::operator delete
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$18@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA ENDP ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$18
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 33
$T3 = 40
tv145 = 48
$T4 = 56
tv164 = 64
$T5 = 72
tv183 = 80
$T6 = 88
tv202 = 96
$T7 = 104
tv221 = 112
$T8 = 120
tv240 = 128
$T9 = 136
tv259 = 144
$T10 = 152
tv278 = 160
$T11 = 168
tv297 = 176
$T12 = 184
tv316 = 192
$T13 = 200
tv335 = 208
$T14 = 216
tv354 = 224
$T15 = 232
tv373 = 240
$T16 = 248
tv392 = 256
$T17 = 264
tv411 = 272
$T18 = 280
tv462 = 288
$T19 = 296
tv481 = 304
$T20 = 312
tv500 = 320
$T21 = 328
tv519 = 336
$T22 = 344
tv538 = 352
$T23 = 360
tv557 = 368
$T24 = 376
tv576 = 384
this$ = 392
this$ = 400
$T25 = 408
$T26 = 416
_My_data$27 = 424
$T28 = 432
$T29 = 440
$T30 = 448
$T31 = 456
_My_data$32 = 464
$T33 = 472
$T34 = 480
$T35 = 488
$T36 = 496
_My_data$37 = 504
$T38 = 512
$T39 = 520
$T40 = 528
$T41 = 536
_My_data$42 = 544
$T43 = 552
$T44 = 560
$T45 = 568
$T46 = 576
_My_data$47 = 584
$T48 = 592
$T49 = 600
$T50 = 608
$T51 = 616
_My_data$52 = 624
$T53 = 632
$T54 = 640
$T55 = 648
$T56 = 656
_My_data$57 = 664
$T58 = 672
$T59 = 680
$T60 = 688
$T61 = 696
_My_data$62 = 704
$T63 = 712
$T64 = 720
$T65 = 728
$T66 = 736
_My_data$67 = 744
$T68 = 752
$T69 = 760
$T70 = 768
$T71 = 776
_My_data$72 = 784
$T73 = 792
$T74 = 800
$T75 = 808
$T76 = 816
_My_data$77 = 824
$T78 = 832
$T79 = 840
$T80 = 848
$T81 = 856
_My_data$82 = 864
$T83 = 872
$T84 = 880
$T85 = 888
$T86 = 896
_My_data$87 = 904
$T88 = 912
$T89 = 920
$T90 = 928
$T91 = 936
_My_data$92 = 944
$T93 = 952
$T94 = 960
$T95 = 968
$T96 = 976
_My_data$97 = 984
$T98 = 992
$T99 = 1000
$T100 = 1008
$T101 = 1016
_My_data$102 = 1024
$T103 = 1032
$T104 = 1040
$T105 = 1048
$T106 = 1056
_My_data$107 = 1064
$T108 = 1072
$T109 = 1080
$T110 = 1088
$T111 = 1096
_My_data$112 = 1104
$T113 = 1112
$T114 = 1120
$T115 = 1128
$T116 = 1136
_My_data$117 = 1144
$T118 = 1152
$T119 = 1160
$T120 = 1168
$T121 = 1176
_My_data$122 = 1184
$T123 = 1192
$T124 = 1200
$T125 = 1208
$T126 = 1216
_My_data$127 = 1224
$T128 = 1232
$T129 = 1240
$T130 = 1248
$T131 = 1256
_My_data$132 = 1264
$T133 = 1272
$T134 = 1280
this$ = 1312
thisServer$ = 1320
?dtor$19@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA PROC ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$19
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 8d 38 01
	00 00		 mov	 rcx, QWORD PTR $T20[rbp]
  00010	e8 00 00 00 00	 call	 ??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::operator delete
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$19@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA ENDP ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$19
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 33
$T3 = 40
tv145 = 48
$T4 = 56
tv164 = 64
$T5 = 72
tv183 = 80
$T6 = 88
tv202 = 96
$T7 = 104
tv221 = 112
$T8 = 120
tv240 = 128
$T9 = 136
tv259 = 144
$T10 = 152
tv278 = 160
$T11 = 168
tv297 = 176
$T12 = 184
tv316 = 192
$T13 = 200
tv335 = 208
$T14 = 216
tv354 = 224
$T15 = 232
tv373 = 240
$T16 = 248
tv392 = 256
$T17 = 264
tv411 = 272
$T18 = 280
tv462 = 288
$T19 = 296
tv481 = 304
$T20 = 312
tv500 = 320
$T21 = 328
tv519 = 336
$T22 = 344
tv538 = 352
$T23 = 360
tv557 = 368
$T24 = 376
tv576 = 384
this$ = 392
this$ = 400
$T25 = 408
$T26 = 416
_My_data$27 = 424
$T28 = 432
$T29 = 440
$T30 = 448
$T31 = 456
_My_data$32 = 464
$T33 = 472
$T34 = 480
$T35 = 488
$T36 = 496
_My_data$37 = 504
$T38 = 512
$T39 = 520
$T40 = 528
$T41 = 536
_My_data$42 = 544
$T43 = 552
$T44 = 560
$T45 = 568
$T46 = 576
_My_data$47 = 584
$T48 = 592
$T49 = 600
$T50 = 608
$T51 = 616
_My_data$52 = 624
$T53 = 632
$T54 = 640
$T55 = 648
$T56 = 656
_My_data$57 = 664
$T58 = 672
$T59 = 680
$T60 = 688
$T61 = 696
_My_data$62 = 704
$T63 = 712
$T64 = 720
$T65 = 728
$T66 = 736
_My_data$67 = 744
$T68 = 752
$T69 = 760
$T70 = 768
$T71 = 776
_My_data$72 = 784
$T73 = 792
$T74 = 800
$T75 = 808
$T76 = 816
_My_data$77 = 824
$T78 = 832
$T79 = 840
$T80 = 848
$T81 = 856
_My_data$82 = 864
$T83 = 872
$T84 = 880
$T85 = 888
$T86 = 896
_My_data$87 = 904
$T88 = 912
$T89 = 920
$T90 = 928
$T91 = 936
_My_data$92 = 944
$T93 = 952
$T94 = 960
$T95 = 968
$T96 = 976
_My_data$97 = 984
$T98 = 992
$T99 = 1000
$T100 = 1008
$T101 = 1016
_My_data$102 = 1024
$T103 = 1032
$T104 = 1040
$T105 = 1048
$T106 = 1056
_My_data$107 = 1064
$T108 = 1072
$T109 = 1080
$T110 = 1088
$T111 = 1096
_My_data$112 = 1104
$T113 = 1112
$T114 = 1120
$T115 = 1128
$T116 = 1136
_My_data$117 = 1144
$T118 = 1152
$T119 = 1160
$T120 = 1168
$T121 = 1176
_My_data$122 = 1184
$T123 = 1192
$T124 = 1200
$T125 = 1208
$T126 = 1216
_My_data$127 = 1224
$T128 = 1232
$T129 = 1240
$T130 = 1248
$T131 = 1256
_My_data$132 = 1264
$T133 = 1272
$T134 = 1280
this$ = 1312
thisServer$ = 1320
?dtor$20@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA PROC ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$20
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 8d 48 01
	00 00		 mov	 rcx, QWORD PTR $T21[rbp]
  00010	e8 00 00 00 00	 call	 ??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::operator delete
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$20@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA ENDP ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$20
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 33
$T3 = 40
tv145 = 48
$T4 = 56
tv164 = 64
$T5 = 72
tv183 = 80
$T6 = 88
tv202 = 96
$T7 = 104
tv221 = 112
$T8 = 120
tv240 = 128
$T9 = 136
tv259 = 144
$T10 = 152
tv278 = 160
$T11 = 168
tv297 = 176
$T12 = 184
tv316 = 192
$T13 = 200
tv335 = 208
$T14 = 216
tv354 = 224
$T15 = 232
tv373 = 240
$T16 = 248
tv392 = 256
$T17 = 264
tv411 = 272
$T18 = 280
tv462 = 288
$T19 = 296
tv481 = 304
$T20 = 312
tv500 = 320
$T21 = 328
tv519 = 336
$T22 = 344
tv538 = 352
$T23 = 360
tv557 = 368
$T24 = 376
tv576 = 384
this$ = 392
this$ = 400
$T25 = 408
$T26 = 416
_My_data$27 = 424
$T28 = 432
$T29 = 440
$T30 = 448
$T31 = 456
_My_data$32 = 464
$T33 = 472
$T34 = 480
$T35 = 488
$T36 = 496
_My_data$37 = 504
$T38 = 512
$T39 = 520
$T40 = 528
$T41 = 536
_My_data$42 = 544
$T43 = 552
$T44 = 560
$T45 = 568
$T46 = 576
_My_data$47 = 584
$T48 = 592
$T49 = 600
$T50 = 608
$T51 = 616
_My_data$52 = 624
$T53 = 632
$T54 = 640
$T55 = 648
$T56 = 656
_My_data$57 = 664
$T58 = 672
$T59 = 680
$T60 = 688
$T61 = 696
_My_data$62 = 704
$T63 = 712
$T64 = 720
$T65 = 728
$T66 = 736
_My_data$67 = 744
$T68 = 752
$T69 = 760
$T70 = 768
$T71 = 776
_My_data$72 = 784
$T73 = 792
$T74 = 800
$T75 = 808
$T76 = 816
_My_data$77 = 824
$T78 = 832
$T79 = 840
$T80 = 848
$T81 = 856
_My_data$82 = 864
$T83 = 872
$T84 = 880
$T85 = 888
$T86 = 896
_My_data$87 = 904
$T88 = 912
$T89 = 920
$T90 = 928
$T91 = 936
_My_data$92 = 944
$T93 = 952
$T94 = 960
$T95 = 968
$T96 = 976
_My_data$97 = 984
$T98 = 992
$T99 = 1000
$T100 = 1008
$T101 = 1016
_My_data$102 = 1024
$T103 = 1032
$T104 = 1040
$T105 = 1048
$T106 = 1056
_My_data$107 = 1064
$T108 = 1072
$T109 = 1080
$T110 = 1088
$T111 = 1096
_My_data$112 = 1104
$T113 = 1112
$T114 = 1120
$T115 = 1128
$T116 = 1136
_My_data$117 = 1144
$T118 = 1152
$T119 = 1160
$T120 = 1168
$T121 = 1176
_My_data$122 = 1184
$T123 = 1192
$T124 = 1200
$T125 = 1208
$T126 = 1216
_My_data$127 = 1224
$T128 = 1232
$T129 = 1240
$T130 = 1248
$T131 = 1256
_My_data$132 = 1264
$T133 = 1272
$T134 = 1280
this$ = 1312
thisServer$ = 1320
?dtor$21@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA PROC ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$21
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 8d 58 01
	00 00		 mov	 rcx, QWORD PTR $T22[rbp]
  00010	e8 00 00 00 00	 call	 ??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::operator delete
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$21@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA ENDP ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$21
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 33
$T3 = 40
tv145 = 48
$T4 = 56
tv164 = 64
$T5 = 72
tv183 = 80
$T6 = 88
tv202 = 96
$T7 = 104
tv221 = 112
$T8 = 120
tv240 = 128
$T9 = 136
tv259 = 144
$T10 = 152
tv278 = 160
$T11 = 168
tv297 = 176
$T12 = 184
tv316 = 192
$T13 = 200
tv335 = 208
$T14 = 216
tv354 = 224
$T15 = 232
tv373 = 240
$T16 = 248
tv392 = 256
$T17 = 264
tv411 = 272
$T18 = 280
tv462 = 288
$T19 = 296
tv481 = 304
$T20 = 312
tv500 = 320
$T21 = 328
tv519 = 336
$T22 = 344
tv538 = 352
$T23 = 360
tv557 = 368
$T24 = 376
tv576 = 384
this$ = 392
this$ = 400
$T25 = 408
$T26 = 416
_My_data$27 = 424
$T28 = 432
$T29 = 440
$T30 = 448
$T31 = 456
_My_data$32 = 464
$T33 = 472
$T34 = 480
$T35 = 488
$T36 = 496
_My_data$37 = 504
$T38 = 512
$T39 = 520
$T40 = 528
$T41 = 536
_My_data$42 = 544
$T43 = 552
$T44 = 560
$T45 = 568
$T46 = 576
_My_data$47 = 584
$T48 = 592
$T49 = 600
$T50 = 608
$T51 = 616
_My_data$52 = 624
$T53 = 632
$T54 = 640
$T55 = 648
$T56 = 656
_My_data$57 = 664
$T58 = 672
$T59 = 680
$T60 = 688
$T61 = 696
_My_data$62 = 704
$T63 = 712
$T64 = 720
$T65 = 728
$T66 = 736
_My_data$67 = 744
$T68 = 752
$T69 = 760
$T70 = 768
$T71 = 776
_My_data$72 = 784
$T73 = 792
$T74 = 800
$T75 = 808
$T76 = 816
_My_data$77 = 824
$T78 = 832
$T79 = 840
$T80 = 848
$T81 = 856
_My_data$82 = 864
$T83 = 872
$T84 = 880
$T85 = 888
$T86 = 896
_My_data$87 = 904
$T88 = 912
$T89 = 920
$T90 = 928
$T91 = 936
_My_data$92 = 944
$T93 = 952
$T94 = 960
$T95 = 968
$T96 = 976
_My_data$97 = 984
$T98 = 992
$T99 = 1000
$T100 = 1008
$T101 = 1016
_My_data$102 = 1024
$T103 = 1032
$T104 = 1040
$T105 = 1048
$T106 = 1056
_My_data$107 = 1064
$T108 = 1072
$T109 = 1080
$T110 = 1088
$T111 = 1096
_My_data$112 = 1104
$T113 = 1112
$T114 = 1120
$T115 = 1128
$T116 = 1136
_My_data$117 = 1144
$T118 = 1152
$T119 = 1160
$T120 = 1168
$T121 = 1176
_My_data$122 = 1184
$T123 = 1192
$T124 = 1200
$T125 = 1208
$T126 = 1216
_My_data$127 = 1224
$T128 = 1232
$T129 = 1240
$T130 = 1248
$T131 = 1256
_My_data$132 = 1264
$T133 = 1272
$T134 = 1280
this$ = 1312
thisServer$ = 1320
?dtor$22@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA PROC ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$22
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 8d 68 01
	00 00		 mov	 rcx, QWORD PTR $T23[rbp]
  00010	e8 00 00 00 00	 call	 ??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::operator delete
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$22@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA ENDP ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$22
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 33
$T3 = 40
tv145 = 48
$T4 = 56
tv164 = 64
$T5 = 72
tv183 = 80
$T6 = 88
tv202 = 96
$T7 = 104
tv221 = 112
$T8 = 120
tv240 = 128
$T9 = 136
tv259 = 144
$T10 = 152
tv278 = 160
$T11 = 168
tv297 = 176
$T12 = 184
tv316 = 192
$T13 = 200
tv335 = 208
$T14 = 216
tv354 = 224
$T15 = 232
tv373 = 240
$T16 = 248
tv392 = 256
$T17 = 264
tv411 = 272
$T18 = 280
tv462 = 288
$T19 = 296
tv481 = 304
$T20 = 312
tv500 = 320
$T21 = 328
tv519 = 336
$T22 = 344
tv538 = 352
$T23 = 360
tv557 = 368
$T24 = 376
tv576 = 384
this$ = 392
this$ = 400
$T25 = 408
$T26 = 416
_My_data$27 = 424
$T28 = 432
$T29 = 440
$T30 = 448
$T31 = 456
_My_data$32 = 464
$T33 = 472
$T34 = 480
$T35 = 488
$T36 = 496
_My_data$37 = 504
$T38 = 512
$T39 = 520
$T40 = 528
$T41 = 536
_My_data$42 = 544
$T43 = 552
$T44 = 560
$T45 = 568
$T46 = 576
_My_data$47 = 584
$T48 = 592
$T49 = 600
$T50 = 608
$T51 = 616
_My_data$52 = 624
$T53 = 632
$T54 = 640
$T55 = 648
$T56 = 656
_My_data$57 = 664
$T58 = 672
$T59 = 680
$T60 = 688
$T61 = 696
_My_data$62 = 704
$T63 = 712
$T64 = 720
$T65 = 728
$T66 = 736
_My_data$67 = 744
$T68 = 752
$T69 = 760
$T70 = 768
$T71 = 776
_My_data$72 = 784
$T73 = 792
$T74 = 800
$T75 = 808
$T76 = 816
_My_data$77 = 824
$T78 = 832
$T79 = 840
$T80 = 848
$T81 = 856
_My_data$82 = 864
$T83 = 872
$T84 = 880
$T85 = 888
$T86 = 896
_My_data$87 = 904
$T88 = 912
$T89 = 920
$T90 = 928
$T91 = 936
_My_data$92 = 944
$T93 = 952
$T94 = 960
$T95 = 968
$T96 = 976
_My_data$97 = 984
$T98 = 992
$T99 = 1000
$T100 = 1008
$T101 = 1016
_My_data$102 = 1024
$T103 = 1032
$T104 = 1040
$T105 = 1048
$T106 = 1056
_My_data$107 = 1064
$T108 = 1072
$T109 = 1080
$T110 = 1088
$T111 = 1096
_My_data$112 = 1104
$T113 = 1112
$T114 = 1120
$T115 = 1128
$T116 = 1136
_My_data$117 = 1144
$T118 = 1152
$T119 = 1160
$T120 = 1168
$T121 = 1176
_My_data$122 = 1184
$T123 = 1192
$T124 = 1200
$T125 = 1208
$T126 = 1216
_My_data$127 = 1224
$T128 = 1232
$T129 = 1240
$T130 = 1248
$T131 = 1256
_My_data$132 = 1264
$T133 = 1272
$T134 = 1280
this$ = 1312
thisServer$ = 1320
?dtor$23@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA PROC ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$23
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 8d 78 01
	00 00		 mov	 rcx, QWORD PTR $T24[rbp]
  00010	e8 00 00 00 00	 call	 ??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::operator delete
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$23@?0???0WorldRunner@mu2@@QEAA@PEAVServer@1@@Z@4HA ENDP ; `mu2::WorldRunner::WorldRunner'::`1'::dtor$23
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ??0?$_Vector_val@U?$_Simple_types@PEAVServerHandler@mu2@@@std@@@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 8
??0?$_Vector_val@U?$_Simple_types@PEAVServerHandler@mu2@@@std@@@std@@QEAA@XZ PROC ; std::_Vector_val<std::_Simple_types<mu2::ServerHandler *> >::_Vector_val<std::_Simple_types<mu2::ServerHandler *> >, COMDAT

; 400  :     _CONSTEXPR20 _Vector_val() noexcept : _Myfirst(), _Mylast(), _Myend() {}

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0000a	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0
  00011	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00016	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0
  0001e	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0
  0002b	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00030	c3		 ret	 0
??0?$_Vector_val@U?$_Simple_types@PEAVServerHandler@mu2@@@std@@@std@@QEAA@XZ ENDP ; std::_Vector_val<std::_Simple_types<mu2::ServerHandler *> >::_Vector_val<std::_Simple_types<mu2::ServerHandler *> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ?_Xlength@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@CAXXZ
_TEXT	SEGMENT
?_Xlength@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@CAXXZ PROC ; std::vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >::_Xlength, COMDAT

; 2183 :     [[noreturn]] static void _Xlength() {

$LN3:
  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 2184 :         _Xlength_error("vector too long");

  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_0BA@FOIKENOD@vector?5too?5long@
  0000b	e8 00 00 00 00	 call	 ?_Xlength_error@std@@YAXPEBD@Z ; std::_Xlength_error
  00010	90		 npad	 1
$LN2@Xlength:

; 2185 :     }

  00011	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00015	c3		 ret	 0
?_Xlength@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@CAXXZ ENDP ; std::vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >::_Xlength
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ?_Tidy@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAXXZ
_TEXT	SEGMENT
_Myfirst$ = 32
_My_data$ = 40
_Bytes$ = 48
_Ptr$ = 56
_Mylast$ = 64
_Myend$ = 72
$T1 = 80
$T2 = 88
_Count$ = 96
_Ptr$ = 104
_Al$ = 112
_Last$ = 120
_First$ = 128
this$ = 160
?_Tidy@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAXXZ PROC ; std::vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >::_Tidy, COMDAT

; 2081 :     _CONSTEXPR20 void _Tidy() noexcept { // free all storage

$LN40:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec 98 00
	00 00		 sub	 rsp, 152		; 00000098H

; 2227 :         return _Mypair._Get_first();

  0000c	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00014	48 89 44 24 50	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2227 :         return _Mypair._Get_first();

  00019	48 8b 44 24 50	 mov	 rax, QWORD PTR $T1[rsp]
  0001e	48 89 44 24 58	 mov	 QWORD PTR $T2[rsp], rax

; 2082 :         auto& _Al         = _Getal();

  00023	48 8b 44 24 58	 mov	 rax, QWORD PTR $T2[rsp]
  00028	48 89 44 24 70	 mov	 QWORD PTR _Al$[rsp], rax

; 2083 :         auto& _My_data    = _Mypair._Myval2;

  0002d	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00035	48 89 44 24 28	 mov	 QWORD PTR _My_data$[rsp], rax

; 2084 :         pointer& _Myfirst = _My_data._Myfirst;

  0003a	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  0003f	48 89 44 24 20	 mov	 QWORD PTR _Myfirst$[rsp], rax

; 2085 :         pointer& _Mylast  = _My_data._Mylast;

  00044	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00049	48 83 c0 08	 add	 rax, 8
  0004d	48 89 44 24 40	 mov	 QWORD PTR _Mylast$[rsp], rax

; 2086 :         pointer& _Myend   = _My_data._Myend;

  00052	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00057	48 83 c0 10	 add	 rax, 16
  0005b	48 89 44 24 48	 mov	 QWORD PTR _Myend$[rsp], rax

; 2087 : 
; 2088 :         _My_data._Orphan_all();
; 2089 : 
; 2090 :         if (_Myfirst) { // destroy and deallocate old array

  00060	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  00065	48 83 38 00	 cmp	 QWORD PTR [rax], 0
  00069	0f 84 ad 00 00
	00		 je	 $LN2@Tidy

; 2091 :             _STD _Destroy_range(_Myfirst, _Mylast, _Al);

  0006f	48 8b 44 24 40	 mov	 rax, QWORD PTR _Mylast$[rsp]
  00074	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00077	48 89 44 24 78	 mov	 QWORD PTR _Last$[rsp], rax
  0007c	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  00081	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00084	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _First$[rsp], rax

; 2092 :             _ASAN_VECTOR_REMOVE;
; 2093 :             _Al.deallocate(_Myfirst, static_cast<size_type>(_Myend - _Myfirst));

  0008c	48 8b 44 24 48	 mov	 rax, QWORD PTR _Myend$[rsp]
  00091	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Myfirst$[rsp]
  00096	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00099	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0009c	48 2b c1	 sub	 rax, rcx
  0009f	48 c1 f8 03	 sar	 rax, 3
  000a3	48 89 44 24 60	 mov	 QWORD PTR _Count$[rsp], rax
  000a8	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  000ad	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000b0	48 89 44 24 68	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  000b5	48 8b 44 24 60	 mov	 rax, QWORD PTR _Count$[rsp]
  000ba	48 c1 e0 03	 shl	 rax, 3
  000be	48 89 44 24 30	 mov	 QWORD PTR _Bytes$[rsp], rax
  000c3	48 8b 44 24 68	 mov	 rax, QWORD PTR _Ptr$[rsp]
  000c8	48 89 44 24 38	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  000cd	48 81 7c 24 30
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  000d6	72 10		 jb	 SHORT $LN31@Tidy

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  000d8	48 8d 54 24 30	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  000dd	48 8d 4c 24 38	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  000e2	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  000e7	90		 npad	 1
$LN31@Tidy:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  000e8	48 8b 54 24 30	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  000ed	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  000f2	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  000f7	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2095 :             _Myfirst = nullptr;

  000f8	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  000fd	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0

; 2096 :             _Mylast  = nullptr;

  00104	48 8b 44 24 40	 mov	 rax, QWORD PTR _Mylast$[rsp]
  00109	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0

; 2097 :             _Myend   = nullptr;

  00110	48 8b 44 24 48	 mov	 rax, QWORD PTR _Myend$[rsp]
  00115	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0
$LN2@Tidy:

; 2098 :         }
; 2099 :     }

  0011c	48 81 c4 98 00
	00 00		 add	 rsp, 152		; 00000098H
  00123	c3		 ret	 0
?_Tidy@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAXXZ ENDP ; std::vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >::_Tidy
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ?_Change_array@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAXQEAPEAVServerHandler@mu2@@_K1@Z
_TEXT	SEGMENT
_Myfirst$ = 32
_My_data$ = 40
_Bytes$ = 48
_Ptr$ = 56
_Mylast$ = 64
_Myend$ = 72
$T1 = 80
$T2 = 88
_Count$ = 96
_Ptr$ = 104
_Al$ = 112
_Last$ = 120
_First$ = 128
this$ = 160
_Newvec$ = 168
_Newsize$ = 176
_Newcapacity$ = 184
?_Change_array@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAXQEAPEAVServerHandler@mu2@@_K1@Z PROC ; std::vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >::_Change_array, COMDAT

; 2059 :         const pointer _Newvec, const size_type _Newsize, const size_type _Newcapacity) noexcept {

$LN40:
  00000	4c 89 4c 24 20	 mov	 QWORD PTR [rsp+32], r9
  00005	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	48 81 ec 98 00
	00 00		 sub	 rsp, 152		; 00000098H

; 2227 :         return _Mypair._Get_first();

  0001b	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00023	48 89 44 24 50	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2227 :         return _Mypair._Get_first();

  00028	48 8b 44 24 50	 mov	 rax, QWORD PTR $T1[rsp]
  0002d	48 89 44 24 58	 mov	 QWORD PTR $T2[rsp], rax

; 2060 :         // orphan all iterators, discard old array, acquire new array
; 2061 :         auto& _Al         = _Getal();

  00032	48 8b 44 24 58	 mov	 rax, QWORD PTR $T2[rsp]
  00037	48 89 44 24 70	 mov	 QWORD PTR _Al$[rsp], rax

; 2062 :         auto& _My_data    = _Mypair._Myval2;

  0003c	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00044	48 89 44 24 28	 mov	 QWORD PTR _My_data$[rsp], rax

; 2063 :         pointer& _Myfirst = _My_data._Myfirst;

  00049	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  0004e	48 89 44 24 20	 mov	 QWORD PTR _Myfirst$[rsp], rax

; 2064 :         pointer& _Mylast  = _My_data._Mylast;

  00053	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00058	48 83 c0 08	 add	 rax, 8
  0005c	48 89 44 24 40	 mov	 QWORD PTR _Mylast$[rsp], rax

; 2065 :         pointer& _Myend   = _My_data._Myend;

  00061	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00066	48 83 c0 10	 add	 rax, 16
  0006a	48 89 44 24 48	 mov	 QWORD PTR _Myend$[rsp], rax

; 2066 : 
; 2067 :         _My_data._Orphan_all();
; 2068 : 
; 2069 :         if (_Myfirst) { // destroy and deallocate old array

  0006f	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  00074	48 83 38 00	 cmp	 QWORD PTR [rax], 0
  00078	0f 84 89 00 00
	00		 je	 $LN2@Change_arr

; 2070 :             _STD _Destroy_range(_Myfirst, _Mylast, _Al);

  0007e	48 8b 44 24 40	 mov	 rax, QWORD PTR _Mylast$[rsp]
  00083	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00086	48 89 44 24 78	 mov	 QWORD PTR _Last$[rsp], rax
  0008b	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  00090	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00093	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR _First$[rsp], rax

; 2071 :             _ASAN_VECTOR_REMOVE;
; 2072 :             _Al.deallocate(_Myfirst, static_cast<size_type>(_Myend - _Myfirst));

  0009b	48 8b 44 24 48	 mov	 rax, QWORD PTR _Myend$[rsp]
  000a0	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Myfirst$[rsp]
  000a5	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  000a8	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000ab	48 2b c1	 sub	 rax, rcx
  000ae	48 c1 f8 03	 sar	 rax, 3
  000b2	48 89 44 24 60	 mov	 QWORD PTR _Count$[rsp], rax
  000b7	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  000bc	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000bf	48 89 44 24 68	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  000c4	48 8b 44 24 60	 mov	 rax, QWORD PTR _Count$[rsp]
  000c9	48 c1 e0 03	 shl	 rax, 3
  000cd	48 89 44 24 30	 mov	 QWORD PTR _Bytes$[rsp], rax
  000d2	48 8b 44 24 68	 mov	 rax, QWORD PTR _Ptr$[rsp]
  000d7	48 89 44 24 38	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  000dc	48 81 7c 24 30
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  000e5	72 10		 jb	 SHORT $LN31@Change_arr

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  000e7	48 8d 54 24 30	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  000ec	48 8d 4c 24 38	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  000f1	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  000f6	90		 npad	 1
$LN31@Change_arr:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  000f7	48 8b 54 24 30	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  000fc	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  00101	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00106	90		 npad	 1
$LN2@Change_arr:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2075 :         _Myfirst = _Newvec;

  00107	48 8b 44 24 20	 mov	 rax, QWORD PTR _Myfirst$[rsp]
  0010c	48 8b 8c 24 a8
	00 00 00	 mov	 rcx, QWORD PTR _Newvec$[rsp]
  00114	48 89 08	 mov	 QWORD PTR [rax], rcx

; 2076 :         _Mylast  = _Newvec + _Newsize;

  00117	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR _Newvec$[rsp]
  0011f	48 8b 8c 24 b0
	00 00 00	 mov	 rcx, QWORD PTR _Newsize$[rsp]
  00127	48 8d 04 c8	 lea	 rax, QWORD PTR [rax+rcx*8]
  0012b	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Mylast$[rsp]
  00130	48 89 01	 mov	 QWORD PTR [rcx], rax

; 2077 :         _Myend   = _Newvec + _Newcapacity;

  00133	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR _Newvec$[rsp]
  0013b	48 8b 8c 24 b8
	00 00 00	 mov	 rcx, QWORD PTR _Newcapacity$[rsp]
  00143	48 8d 04 c8	 lea	 rax, QWORD PTR [rax+rcx*8]
  00147	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _Myend$[rsp]
  0014c	48 89 01	 mov	 QWORD PTR [rcx], rax

; 2078 :         _ASAN_VECTOR_CREATE;
; 2079 :     }

  0014f	48 81 c4 98 00
	00 00		 add	 rsp, 152		; 00000098H
  00156	c3		 ret	 0
?_Change_array@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAXQEAPEAVServerHandler@mu2@@_K1@Z ENDP ; std::vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >::_Change_array
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ?_Calculate_growth@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEBA_K_K@Z
_TEXT	SEGMENT
_Oldcapacity$ = 0
_My_data$1 = 8
$T2 = 16
$T3 = 24
tv82 = 32
_Max$ = 40
_Geometric$ = 48
$T4 = 56
$T5 = 64
$T6 = 72
$T7 = 80
$T8 = 88
$T9 = 96
$T10 = 104
$T11 = 112
_Unsigned_max$12 = 120
this$ = 144
_Newsize$ = 152
?_Calculate_growth@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEBA_K_K@Z PROC ; std::vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >::_Calculate_growth, COMDAT

; 2006 :     _CONSTEXPR20 size_type _Calculate_growth(const size_type _Newsize) const {

$LN42:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 81 ec 88 00
	00 00		 sub	 rsp, 136		; 00000088H

; 1923 :         auto& _My_data = _Mypair._Myval2;

  00011	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00019	48 89 44 24 08	 mov	 QWORD PTR _My_data$1[rsp], rax

; 1924 :         return static_cast<size_type>(_My_data._Myend - _My_data._Myfirst);

  0001e	48 8b 44 24 08	 mov	 rax, QWORD PTR _My_data$1[rsp]
  00023	48 8b 4c 24 08	 mov	 rcx, QWORD PTR _My_data$1[rsp]
  00028	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0002b	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0002f	48 2b c1	 sub	 rax, rcx
  00032	48 c1 f8 03	 sar	 rax, 3
  00036	48 89 44 24 38	 mov	 QWORD PTR $T4[rsp], rax

; 2007 :         // given _Oldcapacity and _Newsize, calculate geometric growth
; 2008 :         const size_type _Oldcapacity = capacity();

  0003b	48 8b 44 24 38	 mov	 rax, QWORD PTR $T4[rsp]
  00040	48 89 04 24	 mov	 QWORD PTR _Oldcapacity$[rsp], rax

; 2231 :         return _Mypair._Get_first();

  00044	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1543 :         return *this;

  0004c	48 89 44 24 40	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 2231 :         return _Mypair._Get_first();

  00051	48 8b 44 24 40	 mov	 rax, QWORD PTR $T5[rsp]
  00056	48 89 44 24 70	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 746  :         return static_cast<size_t>(-1) / sizeof(value_type);

  0005b	48 b8 ff ff ff
	ff ff ff ff 1f	 mov	 rax, 2305843009213693951 ; 1fffffffffffffffH
  00065	48 89 44 24 48	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1919 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()), _Alty_traits::max_size(_Getal()));

  0006a	48 8b 44 24 48	 mov	 rax, QWORD PTR $T6[rsp]
  0006f	48 89 44 24 10	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 866  :         constexpr auto _Unsigned_max = static_cast<make_unsigned_t<_Ty>>(-1);

  00074	48 c7 44 24 78
	ff ff ff ff	 mov	 QWORD PTR _Unsigned_max$12[rsp], -1

; 867  :         return static_cast<_Ty>(_Unsigned_max >> 1);

  0007d	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  00087	48 89 44 24 50	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1919 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()), _Alty_traits::max_size(_Getal()));

  0008c	48 8b 44 24 50	 mov	 rax, QWORD PTR $T7[rsp]
  00091	48 89 44 24 18	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 101  :     return _Right < _Left ? _Right : _Left;

  00096	48 8b 44 24 18	 mov	 rax, QWORD PTR $T3[rsp]
  0009b	48 39 44 24 10	 cmp	 QWORD PTR $T2[rsp], rax
  000a0	73 0c		 jae	 SHORT $LN37@Calculate_
  000a2	48 8d 44 24 10	 lea	 rax, QWORD PTR $T2[rsp]
  000a7	48 89 44 24 20	 mov	 QWORD PTR tv82[rsp], rax
  000ac	eb 0a		 jmp	 SHORT $LN38@Calculate_
$LN37@Calculate_:
  000ae	48 8d 44 24 18	 lea	 rax, QWORD PTR $T3[rsp]
  000b3	48 89 44 24 20	 mov	 QWORD PTR tv82[rsp], rax
$LN38@Calculate_:
  000b8	48 8b 44 24 20	 mov	 rax, QWORD PTR tv82[rsp]
  000bd	48 89 44 24 58	 mov	 QWORD PTR $T8[rsp], rax
  000c2	48 8b 44 24 58	 mov	 rax, QWORD PTR $T8[rsp]
  000c7	48 89 44 24 60	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector

; 1919 :         return (_STD min)(static_cast<size_type>(_STD _Max_limit<difference_type>()), _Alty_traits::max_size(_Getal()));

  000cc	48 8b 44 24 60	 mov	 rax, QWORD PTR $T9[rsp]
  000d1	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000d4	48 89 44 24 68	 mov	 QWORD PTR $T10[rsp], rax

; 2009 :         const auto _Max              = max_size();

  000d9	48 8b 44 24 68	 mov	 rax, QWORD PTR $T10[rsp]
  000de	48 89 44 24 28	 mov	 QWORD PTR _Max$[rsp], rax

; 2010 : 
; 2011 :         if (_Oldcapacity > _Max - _Oldcapacity / 2) {

  000e3	33 d2		 xor	 edx, edx
  000e5	48 8b 04 24	 mov	 rax, QWORD PTR _Oldcapacity$[rsp]
  000e9	b9 02 00 00 00	 mov	 ecx, 2
  000ee	48 f7 f1	 div	 rcx
  000f1	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Max$[rsp]
  000f6	48 2b c8	 sub	 rcx, rax
  000f9	48 8b c1	 mov	 rax, rcx
  000fc	48 39 04 24	 cmp	 QWORD PTR _Oldcapacity$[rsp], rax
  00100	76 07		 jbe	 SHORT $LN2@Calculate_

; 2012 :             return _Max; // geometric growth would overflow

  00102	48 8b 44 24 28	 mov	 rax, QWORD PTR _Max$[rsp]
  00107	eb 3b		 jmp	 SHORT $LN1@Calculate_
$LN2@Calculate_:

; 2013 :         }
; 2014 : 
; 2015 :         const size_type _Geometric = _Oldcapacity + _Oldcapacity / 2;

  00109	33 d2		 xor	 edx, edx
  0010b	48 8b 04 24	 mov	 rax, QWORD PTR _Oldcapacity$[rsp]
  0010f	b9 02 00 00 00	 mov	 ecx, 2
  00114	48 f7 f1	 div	 rcx
  00117	48 8b 0c 24	 mov	 rcx, QWORD PTR _Oldcapacity$[rsp]
  0011b	48 03 c8	 add	 rcx, rax
  0011e	48 8b c1	 mov	 rax, rcx
  00121	48 89 44 24 30	 mov	 QWORD PTR _Geometric$[rsp], rax

; 2016 : 
; 2017 :         if (_Geometric < _Newsize) {

  00126	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR _Newsize$[rsp]
  0012e	48 39 44 24 30	 cmp	 QWORD PTR _Geometric$[rsp], rax
  00133	73 0a		 jae	 SHORT $LN3@Calculate_

; 2018 :             return _Newsize; // geometric growth would be insufficient

  00135	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR _Newsize$[rsp]
  0013d	eb 05		 jmp	 SHORT $LN1@Calculate_
$LN3@Calculate_:

; 2019 :         }
; 2020 : 
; 2021 :         return _Geometric; // geometric growth is sufficient

  0013f	48 8b 44 24 30	 mov	 rax, QWORD PTR _Geometric$[rsp]
$LN1@Calculate_:

; 2022 :     }

  00144	48 81 c4 88 00
	00 00		 add	 rsp, 136		; 00000088H
  0014b	c3		 ret	 0
?_Calculate_growth@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEBA_K_K@Z ENDP ; std::vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >::_Calculate_growth
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vector
;	COMDAT ??1?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAA@XZ PROC ; std::vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >::~vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >, COMDAT

; 829  :     _CONSTEXPR20 ~vector() noexcept {

$LN45:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 830  :         _Tidy();

  00009	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0000e	e8 00 00 00 00	 call	 ?_Tidy@?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@AEAAXXZ ; std::vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >::_Tidy
  00013	90		 npad	 1

; 831  : #if _ITERATOR_DEBUG_LEVEL != 0
; 832  :         auto&& _Alproxy = _GET_PROXY_ALLOCATOR(_Alty, _Getal());
; 833  :         _Delete_plain_internal(_Alproxy, _STD exchange(_Mypair._Myval2._Myproxy, nullptr));
; 834  : #endif // _ITERATOR_DEBUG_LEVEL != 0
; 835  :     }

  00014	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00018	c3		 ret	 0
??1?$vector@PEAVServerHandler@mu2@@V?$allocator@PEAVServerHandler@mu2@@@std@@@std@@QEAA@XZ ENDP ; std::vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >::~vector<mu2::ServerHandler *,std::allocator<mu2::ServerHandler *> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?allocate@?$allocator@PEAVServerHandler@mu2@@@std@@QEAAPEAPEAVServerHandler@mu2@@_K@Z
_TEXT	SEGMENT
_Overflow_is_possible$1 = 32
_Bytes$ = 40
$T2 = 48
$T3 = 56
$T4 = 64
_Max_possible$5 = 72
this$ = 96
_Count$ = 104
?allocate@?$allocator@PEAVServerHandler@mu2@@@std@@QEAAPEAPEAVServerHandler@mu2@@_K@Z PROC ; std::allocator<mu2::ServerHandler *>::allocate, COMDAT

; 988  :     _NODISCARD_RAW_PTR_ALLOC _CONSTEXPR20 __declspec(allocator) _Ty* allocate(_CRT_GUARDOVERFLOW const size_t _Count) {

$LN13:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 113  :     constexpr bool _Overflow_is_possible = _Ty_size > 1;

  0000e	c6 44 24 20 01	 mov	 BYTE PTR _Overflow_is_possible$1[rsp], 1

; 114  : 
; 115  :     if constexpr (_Overflow_is_possible) {
; 116  :         constexpr size_t _Max_possible = static_cast<size_t>(-1) / _Ty_size;

  00013	48 b8 ff ff ff
	ff ff ff ff 1f	 mov	 rax, 2305843009213693951 ; 1fffffffffffffffH
  0001d	48 89 44 24 48	 mov	 QWORD PTR _Max_possible$5[rsp], rax

; 117  :         if (_Count > _Max_possible) {

  00022	48 b8 ff ff ff
	ff ff ff ff 1f	 mov	 rax, 2305843009213693951 ; 1fffffffffffffffH
  0002c	48 39 44 24 68	 cmp	 QWORD PTR _Count$[rsp], rax
  00031	76 06		 jbe	 SHORT $LN4@allocate

; 118  :             _Throw_bad_array_new_length(); // multiply overflow

  00033	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00038	90		 npad	 1
$LN4@allocate:

; 119  :         }
; 120  :     }
; 121  : 
; 122  :     return _Count * _Ty_size;

  00039	48 8b 44 24 68	 mov	 rax, QWORD PTR _Count$[rsp]
  0003e	48 c1 e0 03	 shl	 rax, 3
  00042	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00047	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
  0004c	48 89 44 24 28	 mov	 QWORD PTR _Bytes$[rsp], rax

; 227  :     if (_Bytes == 0) {

  00051	48 83 7c 24 28
	00		 cmp	 QWORD PTR _Bytes$[rsp], 0
  00057	75 0b		 jne	 SHORT $LN8@allocate

; 228  :         return nullptr;

  00059	48 c7 44 24 30
	00 00 00 00	 mov	 QWORD PTR $T2[rsp], 0
  00062	eb 35		 jmp	 SHORT $LN7@allocate
$LN8@allocate:

; 229  :     }
; 230  : 
; 231  : #if _HAS_CXX20 // TRANSITION, GH-1532
; 232  :     if (_STD is_constant_evaluated()) {
; 233  :         return _Traits::_Allocate(_Bytes);
; 234  :     }
; 235  : #endif // _HAS_CXX20
; 236  : 
; 237  : #ifdef __cpp_aligned_new
; 238  :     if constexpr (_Align > __STDCPP_DEFAULT_NEW_ALIGNMENT__) {
; 239  :         size_t _Passed_align = _Align;
; 240  : #if defined(_M_IX86) || defined(_M_X64)
; 241  :         if (_Bytes >= _Big_allocation_threshold) {
; 242  :             // boost the alignment of big allocations to help autovectorization
; 243  :             _Passed_align = (_STD max)(_Align, _Big_allocation_alignment);
; 244  :         }
; 245  : #endif // defined(_M_IX86) || defined(_M_X64)
; 246  :         return _Traits::_Allocate_aligned(_Bytes, _Passed_align);
; 247  :     } else
; 248  : #endif // defined(__cpp_aligned_new)
; 249  :     {
; 250  : #if defined(_M_IX86) || defined(_M_X64)
; 251  :         if (_Bytes >= _Big_allocation_threshold) {

  00064	48 81 7c 24 28
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  0006d	72 11		 jb	 SHORT $LN9@allocate

; 252  :             // boost the alignment of big allocations to help autovectorization
; 253  :             return _Allocate_manually_vector_aligned<_Traits>(_Bytes);

  0006f	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00074	e8 00 00 00 00	 call	 ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
  00079	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
  0007e	eb 19		 jmp	 SHORT $LN7@allocate
$LN9@allocate:

; 136  :         return ::operator new(_Bytes);

  00080	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00085	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  0008a	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax

; 256  :         return _Traits::_Allocate(_Bytes);

  0008f	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  00094	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
$LN7@allocate:

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00099	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
$LN6@allocate:

; 991  :     }

  0009e	48 83 c4 58	 add	 rsp, 88			; 00000058H
  000a2	c3		 ret	 0
?allocate@?$allocator@PEAVServerHandler@mu2@@@std@@QEAAPEAPEAVServerHandler@mu2@@_K@Z ENDP ; std::allocator<mu2::ServerHandler *>::allocate
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Thread.h
;	COMDAT ?destory@Thread@mu2@@UEAAXXZ
_TEXT	SEGMENT
this$ = 8
?destory@Thread@mu2@@UEAAXXZ PROC			; mu2::Thread::destory, COMDAT

; 79   : {

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 80   : 
; 81   : }

  00005	c3		 ret	 0
?destory@Thread@mu2@@UEAAXXZ ENDP			; mu2::Thread::destory
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\EventCallBack.h
;	COMDAT ?OnAppChatEvent@EventListener@mu2@@UEAA_NAEAV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@@Z
_TEXT	SEGMENT
this$ = 8
commands$ = 16
?OnAppChatEvent@EventListener@mu2@@UEAA_NAEAV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@@Z PROC ; mu2::EventListener::OnAppChatEvent, COMDAT

; 35   : 	virtual Bool OnAppChatEvent(std::vector<std::string>& commands) { commands; return true; }

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	b0 01		 mov	 al, 1
  0000c	c3		 ret	 0
?OnAppChatEvent@EventListener@mu2@@UEAA_NAEAV?$vector@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@@Z ENDP ; mu2::EventListener::OnAppChatEvent
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\EventCallBack.h
;	COMDAT ?OnRelayedEvent@EventListener@mu2@@UEAA_NPEAVTcpSocket@2@PEAXH@Z
_TEXT	SEGMENT
this$ = 8
tcpsocket$ = 16
pTr$ = 24
pTrSz$ = 32
?OnRelayedEvent@EventListener@mu2@@UEAA_NPEAVTcpSocket@2@PEAXH@Z PROC ; mu2::EventListener::OnRelayedEvent, COMDAT

; 33   : 	virtual Bool OnRelayedEvent(TcpSocket* tcpsocket, void* pTr, const Int32 pTrSz) { tcpsocket; pTr; pTrSz; return true; }

  00000	44 89 4c 24 20	 mov	 DWORD PTR [rsp+32], r9d
  00005	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	b0 01		 mov	 al, 1
  00016	c3		 ret	 0
?OnRelayedEvent@EventListener@mu2@@UEAA_NPEAVTcpSocket@2@PEAXH@Z ENDP ; mu2::EventListener::OnRelayedEvent
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\EventCallBack.h
;	COMDAT ?OnUnregisteredEvent@EventListener@mu2@@UEAA_NPEAVTcpSocket@2@PEAXH@Z
_TEXT	SEGMENT
this$ = 8
tcpsocket$ = 16
pTr$ = 24
pTrSz$ = 32
?OnUnregisteredEvent@EventListener@mu2@@UEAA_NPEAVTcpSocket@2@PEAXH@Z PROC ; mu2::EventListener::OnUnregisteredEvent, COMDAT

; 30   : 	virtual Bool OnUnregisteredEvent(TcpSocket* tcpsocket, void* pTr, const Int32 pTrSz) { tcpsocket; pTr; pTrSz; return true; }

  00000	44 89 4c 24 20	 mov	 DWORD PTR [rsp+32], r9d
  00005	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	b0 01		 mov	 al, 1
  00016	c3		 ret	 0
?OnUnregisteredEvent@EventListener@mu2@@UEAA_NPEAVTcpSocket@2@PEAXH@Z ENDP ; mu2::EventListener::OnUnregisteredEvent
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\EventCallBack.h
; File F:\Release_Branch\Server\Development\Framework\Core\MessageQueue.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\concurrent_queue.h
; File F:\Release_Branch\Server\Development\Framework\Net\Common\EventCallBack.h
;	COMDAT ?Notify@EventListener@mu2@@UEAAXAEBV?$SmartPtrEx@UEvent@mu2@@@2@@Z
_TEXT	SEGMENT
this$ = 48
ep$ = 56
?Notify@EventListener@mu2@@UEAAXAEBV?$SmartPtrEx@UEvent@mu2@@@2@@Z PROC ; mu2::EventListener::Notify, COMDAT

; 42   : {

$LN7:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H
; File F:\Release_Branch\Server\Development\Framework\Core\MessageQueue.h

; 69   : 		m_queue.push(val);

  0000e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00013	48 83 c0 08	 add	 rax, 8
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\concurrent_queue.h

; 575  :         _Internal_push( &_Src );

  00017	48 8b 54 24 38	 mov	 rdx, QWORD PTR ep$[rsp]
  0001c	48 8b c8	 mov	 rcx, rax
  0001f	e8 00 00 00 00	 call	 ?_Internal_push@_Concurrent_queue_base_v4@details@Concurrency@@IEAAXPEBX@Z ; Concurrency::details::_Concurrent_queue_base_v4::_Internal_push
  00024	90		 npad	 1
; File F:\Release_Branch\Server\Development\Framework\Net\Common\EventCallBack.h

; 45   : }

  00025	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00029	c3		 ret	 0
?Notify@EventListener@mu2@@UEAAXAEBV?$SmartPtrEx@UEvent@mu2@@@2@@Z ENDP ; mu2::EventListener::Notify
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h
;	COMDAT ??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z
_TEXT	SEGMENT
ptr$ = 48
??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z PROC ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::operator delete, COMDAT

; 57   : 	{

$LN6:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryStdAlloc.h

; 45   : 			::free(ptr);

  00009	48 8b 4c 24 30	 mov	 rcx, QWORD PTR ptr$[rsp]
  0000e	e8 00 00 00 00	 call	 free
  00013	90		 npad	 1
; File F:\Release_Branch\Server\Development\Framework\Core\MemoryAllocatedObject.h

; 59   : 	}

  00014	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00018	c3		 ret	 0
??3?$AllocatedObject@V?$CategorisedAllocPolicy@$0A@@mu2@@@@SAXPEAX@Z ENDP ; AllocatedObject<mu2::CategorisedAllocPolicy<0> >::operator delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z
_TEXT	SEGMENT
_Back_shift$ = 48
_Ptr_container$ = 56
_Ptr_user$ = 64
_Min_back_shift$ = 72
_Ptr$ = 96
_Bytes$ = 104
?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z PROC ; std::_Adjust_manually_vector_aligned, COMDAT

; 200  : inline void _Adjust_manually_vector_aligned(void*& _Ptr, size_t& _Bytes) {

$LN5:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 201  :     // adjust parameters from _Allocate_manually_vector_aligned to pass to operator delete
; 202  :     _Bytes += _Non_user_size;

  0000e	48 8b 44 24 68	 mov	 rax, QWORD PTR _Bytes$[rsp]
  00013	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00016	48 83 c0 27	 add	 rax, 39			; 00000027H
  0001a	48 8b 4c 24 68	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  0001f	48 89 01	 mov	 QWORD PTR [rcx], rax

; 203  : 
; 204  :     const uintptr_t* const _Ptr_user = static_cast<uintptr_t*>(_Ptr);

  00022	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00027	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0002a	48 89 44 24 40	 mov	 QWORD PTR _Ptr_user$[rsp], rax

; 205  :     const uintptr_t _Ptr_container   = _Ptr_user[-1];

  0002f	b8 08 00 00 00	 mov	 eax, 8
  00034	48 6b c0 ff	 imul	 rax, rax, -1
  00038	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr_user$[rsp]
  0003d	48 8b 04 01	 mov	 rax, QWORD PTR [rcx+rax]
  00041	48 89 44 24 38	 mov	 QWORD PTR _Ptr_container$[rsp], rax

; 206  : 
; 207  :     // If the following asserts, it likely means that we are performing
; 208  :     // an aligned delete on memory coming from an unaligned allocation.
; 209  :     _STL_ASSERT(_Ptr_user[-2] == _Big_allocation_sentinel, "invalid argument");
; 210  : 
; 211  :     // Extra paranoia on aligned allocation/deallocation; ensure _Ptr_container is
; 212  :     // in range [_Min_back_shift, _Non_user_size]
; 213  : #ifdef _DEBUG
; 214  :     constexpr uintptr_t _Min_back_shift = 2 * sizeof(void*);
; 215  : #else // ^^^ defined(_DEBUG) / !defined(_DEBUG) vvv
; 216  :     constexpr uintptr_t _Min_back_shift = sizeof(void*);

  00046	48 c7 44 24 48
	08 00 00 00	 mov	 QWORD PTR _Min_back_shift$[rsp], 8

; 217  : #endif // ^^^ !defined(_DEBUG) ^^^
; 218  :     const uintptr_t _Back_shift = reinterpret_cast<uintptr_t>(_Ptr) - _Ptr_container;

  0004f	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00054	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr_container$[rsp]
  00059	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0005c	48 2b c1	 sub	 rax, rcx
  0005f	48 89 44 24 30	 mov	 QWORD PTR _Back_shift$[rsp], rax

; 219  :     _STL_VERIFY(_Back_shift >= _Min_back_shift && _Back_shift <= _Non_user_size, "invalid argument");

  00064	48 83 7c 24 30
	08		 cmp	 QWORD PTR _Back_shift$[rsp], 8
  0006a	72 08		 jb	 SHORT $LN3@Adjust_man
  0006c	48 83 7c 24 30
	27		 cmp	 QWORD PTR _Back_shift$[rsp], 39 ; 00000027H
  00072	76 19		 jbe	 SHORT $LN2@Adjust_man
$LN3@Adjust_man:
  00074	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR [rsp+32], 0
  0007d	45 33 c9	 xor	 r9d, r9d
  00080	45 33 c0	 xor	 r8d, r8d
  00083	33 d2		 xor	 edx, edx
  00085	33 c9		 xor	 ecx, ecx
  00087	e8 00 00 00 00	 call	 _invoke_watson
  0008c	90		 npad	 1
$LN2@Adjust_man:

; 220  :     _Ptr = reinterpret_cast<void*>(_Ptr_container);

  0008d	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00092	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr_container$[rsp]
  00097	48 89 08	 mov	 QWORD PTR [rax], rcx
$LN4@Adjust_man:

; 221  : }

  0009a	48 83 c4 58	 add	 rsp, 88			; 00000058H
  0009e	c3		 ret	 0
?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ENDP ; std::_Adjust_manually_vector_aligned
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?_Throw_bad_array_new_length@std@@YAXXZ
_TEXT	SEGMENT
$T1 = 32
?_Throw_bad_array_new_length@std@@YAXXZ PROC		; std::_Throw_bad_array_new_length, COMDAT

; 107  : [[noreturn]] inline void _Throw_bad_array_new_length() {

$LN3:
  00000	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 108  :     _THROW(bad_array_new_length{});

  00004	48 8d 4c 24 20	 lea	 rcx, QWORD PTR $T1[rsp]
  00009	e8 00 00 00 00	 call	 ??0bad_array_new_length@std@@QEAA@XZ ; std::bad_array_new_length::bad_array_new_length
  0000e	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:_TI3?AVbad_array_new_length@std@@
  00015	48 8d 4c 24 20	 lea	 rcx, QWORD PTR $T1[rsp]
  0001a	e8 00 00 00 00	 call	 _CxxThrowException
  0001f	90		 npad	 1
$LN2@Throw_bad_:

; 109  : }

  00020	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00024	c3		 ret	 0
?_Throw_bad_array_new_length@std@@YAXXZ ENDP		; std::_Throw_bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gbad_array_new_length@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gbad_array_new_length@std@@UEAAPEAXI@Z PROC		; std::bad_array_new_length::`scalar deleting destructor', COMDAT
$LN20:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gbad_array_new_length@std@@UEAAPEAXI@Z ENDP		; std::bad_array_new_length::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??0bad_array_new_length@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
__that$ = 56
??0bad_array_new_length@std@@QEAA@AEBV01@@Z PROC	; std::bad_array_new_length::bad_array_new_length, COMDAT
$LN14:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000e	48 8b 54 24 38	 mov	 rdx, QWORD PTR __that$[rsp]
  00013	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00018	e8 00 00 00 00	 call	 ??0bad_alloc@std@@QEAA@AEBV01@@Z
  0001d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00022	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_array_new_length@std@@6B@
  00029	48 89 08	 mov	 QWORD PTR [rax], rcx
  0002c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00031	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00035	c3		 ret	 0
??0bad_array_new_length@std@@QEAA@AEBV01@@Z ENDP	; std::bad_array_new_length::bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??1bad_array_new_length@std@@UEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1bad_array_new_length@std@@UEAA@XZ PROC		; std::bad_array_new_length::~bad_array_new_length, COMDAT
$LN14:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  00018	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 83 c0 08	 add	 rax, 8
  00021	48 8b c8	 mov	 rcx, rax
  00024	e8 00 00 00 00	 call	 __std_exception_destroy
  00029	90		 npad	 1
  0002a	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0002e	c3		 ret	 0
??1bad_array_new_length@std@@UEAA@XZ ENDP		; std::bad_array_new_length::~bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0bad_array_new_length@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 16
??0bad_array_new_length@std@@QEAA@XZ PROC		; std::bad_array_new_length::bad_array_new_length, COMDAT

; 144  :     {

$LN14:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi

; 67   :     {

  00006	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0000b	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00012	48 89 08	 mov	 QWORD PTR [rax], rcx

; 66   :         : _Data()

  00015	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 83 c0 08	 add	 rax, 8
  0001e	48 8b f8	 mov	 rdi, rax
  00021	33 c0		 xor	 eax, eax
  00023	b9 10 00 00 00	 mov	 ecx, 16
  00028	f3 aa		 rep stosb

; 68   :         _Data._What = _Message;

  0002a	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0002f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_0BF@KINCDENJ@bad?5array?5new?5length@
  00036	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 133  :     {

  0003a	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0003f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_alloc@std@@6B@
  00046	48 89 08	 mov	 QWORD PTR [rax], rcx

; 144  :     {

  00049	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_array_new_length@std@@6B@
  00055	48 89 08	 mov	 QWORD PTR [rax], rcx

; 145  :     }

  00058	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0005d	5f		 pop	 rdi
  0005e	c3		 ret	 0
??0bad_array_new_length@std@@QEAA@XZ ENDP		; std::bad_array_new_length::bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gbad_alloc@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gbad_alloc@std@@UEAAPEAXI@Z PROC			; std::bad_alloc::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gbad_alloc@std@@UEAAPEAXI@Z ENDP			; std::bad_alloc::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0bad_alloc@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
__that$ = 56
??0bad_alloc@std@@QEAA@AEBV01@@Z PROC			; std::bad_alloc::bad_alloc, COMDAT
$LN9:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 20	 sub	 rsp, 32			; 00000020H

; 73   :     {

  0000f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  0001b	48 89 08	 mov	 QWORD PTR [rax], rcx

; 72   :         : _Data()

  0001e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 83 c0 08	 add	 rax, 8
  00027	48 8b f8	 mov	 rdi, rax
  0002a	33 c0		 xor	 eax, eax
  0002c	b9 10 00 00 00	 mov	 ecx, 16
  00031	f3 aa		 rep stosb

; 74   :         __std_exception_copy(&_Other._Data, &_Data);

  00033	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 83 c0 08	 add	 rax, 8
  0003c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR __that$[rsp]
  00041	48 83 c1 08	 add	 rcx, 8
  00045	48 8b d0	 mov	 rdx, rax
  00048	e8 00 00 00 00	 call	 __std_exception_copy
  0004d	90		 npad	 1
  0004e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_alloc@std@@6B@
  0005a	48 89 08	 mov	 QWORD PTR [rax], rcx
  0005d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00062	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00066	5f		 pop	 rdi
  00067	c3		 ret	 0
??0bad_alloc@std@@QEAA@AEBV01@@Z ENDP			; std::bad_alloc::bad_alloc
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gexception@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gexception@std@@UEAAPEAXI@Z PROC			; std::exception::`scalar deleting destructor', COMDAT
$LN10:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gexception@std@@UEAAPEAXI@Z ENDP			; std::exception::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ?what@exception@std@@UEBAPEBDXZ
_TEXT	SEGMENT
tv69 = 0
this$ = 32
?what@exception@std@@UEBAPEBDXZ PROC			; std::exception::what, COMDAT

; 95   :     {

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 18	 sub	 rsp, 24

; 96   :         return _Data._What ? _Data._What : "Unknown exception";

  00009	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00013	74 0f		 je	 SHORT $LN3@what
  00015	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0001e	48 89 04 24	 mov	 QWORD PTR tv69[rsp], rax
  00022	eb 0b		 jmp	 SHORT $LN4@what
$LN3@what:
  00024	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BC@EOODALEL@Unknown?5exception@
  0002b	48 89 04 24	 mov	 QWORD PTR tv69[rsp], rax
$LN4@what:
  0002f	48 8b 04 24	 mov	 rax, QWORD PTR tv69[rsp]

; 97   :     }

  00033	48 83 c4 18	 add	 rsp, 24
  00037	c3		 ret	 0
?what@exception@std@@UEBAPEBDXZ ENDP			; std::exception::what
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0exception@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
_Other$ = 56
??0exception@std@@QEAA@AEBV01@@Z PROC			; std::exception::exception, COMDAT

; 73   :     {

$LN4:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  0000f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  0001b	48 89 08	 mov	 QWORD PTR [rax], rcx

; 72   :         : _Data()

  0001e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 83 c0 08	 add	 rax, 8
  00027	48 8b f8	 mov	 rdi, rax
  0002a	33 c0		 xor	 eax, eax
  0002c	b9 10 00 00 00	 mov	 ecx, 16
  00031	f3 aa		 rep stosb

; 74   :         __std_exception_copy(&_Other._Data, &_Data);

  00033	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 83 c0 08	 add	 rax, 8
  0003c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Other$[rsp]
  00041	48 83 c1 08	 add	 rcx, 8
  00045	48 8b d0	 mov	 rdx, rax
  00048	e8 00 00 00 00	 call	 __std_exception_copy
  0004d	90		 npad	 1

; 75   :     }

  0004e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00057	5f		 pop	 rdi
  00058	c3		 ret	 0
??0exception@std@@QEAA@AEBV01@@Z ENDP			; std::exception::exception
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldRunner.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
