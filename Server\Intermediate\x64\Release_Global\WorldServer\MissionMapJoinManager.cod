; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

CONST	SEGMENT
?piecewise_construct@std@@3Upiecewise_construct_t@1@B	ORG $+1 ; std::piecewise_construct
CONST	ENDS
PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	??0exception@std@@QEAA@AEBV01@@Z		; std::exception::exception
PUBLIC	?what@exception@std@@UEBAPEBDXZ			; std::exception::what
PUBLIC	??_Gexception@std@@UEAAPEAXI@Z			; std::exception::`scalar deleting destructor'
PUBLIC	??0bad_alloc@std@@QEAA@AEBV01@@Z		; std::bad_alloc::bad_alloc
PUBLIC	??_Gbad_alloc@std@@UEAAPEAXI@Z			; std::bad_alloc::`scalar deleting destructor'
PUBLIC	??0bad_array_new_length@std@@QEAA@XZ		; std::bad_array_new_length::bad_array_new_length
PUBLIC	??1bad_array_new_length@std@@UEAA@XZ		; std::bad_array_new_length::~bad_array_new_length
PUBLIC	??0bad_array_new_length@std@@QEAA@AEBV01@@Z	; std::bad_array_new_length::bad_array_new_length
PUBLIC	??_Gbad_array_new_length@std@@UEAAPEAXI@Z	; std::bad_array_new_length::`scalar deleting destructor'
PUBLIC	?_Throw_bad_array_new_length@std@@YAXXZ		; std::_Throw_bad_array_new_length
PUBLIC	?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
PUBLIC	?_Throw_tree_length_error@std@@YAXXZ		; std::_Throw_tree_length_error
PUBLIC	??_GISerializer@mu2@@UEAAPEAXI@Z		; mu2::ISerializer::`scalar deleting destructor'
PUBLIC	??1?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@UEAA@XZ ; mu2::ISingleton<mu2::MissionMapJoinManager>::~ISingleton<mu2::MissionMapJoinManager>
PUBLIC	??_G?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@UEAAPEAXI@Z ; mu2::ISingleton<mu2::MissionMapJoinManager>::`scalar deleting destructor'
PUBLIC	??1MissionMapJoinInfo@MissionMapJoinManager@mu2@@QEAA@XZ ; mu2::MissionMapJoinManager::MissionMapJoinInfo::~MissionMapJoinInfo
PUBLIC	?allocate@?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@_K@Z ; std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> >::allocate
PUBLIC	??1?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@QEAA@XZ ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::~_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >
PUBLIC	?max_size@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@QEBA_KXZ ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::max_size
PUBLIC	?_Erase_unchecked@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@AEAAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::_Erase_unchecked
PUBLIC	?_Check_grow_by_1@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@IEAAXXZ ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::_Check_grow_by_1
PUBLIC	?_Alloc_sentinel_and_proxy@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@IEAAXXZ ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::_Alloc_sentinel_and_proxy
PUBLIC	?_Lrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::_Lrotate
PUBLIC	?_Rrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::_Rrotate
PUBLIC	?_Extract@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::_Extract
PUBLIC	?_Insert_node@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@U?$_Tree_id@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@2@QEAU32@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::_Insert_node
PUBLIC	??0?$map@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@@std@@QEAA@XZ ; std::map<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::map<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >
PUBLIC	??1?$map@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@@std@@QEAA@XZ ; std::map<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::~map<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >
PUBLIC	??1MissionMapJoinManager@mu2@@UEAA@XZ		; mu2::MissionMapJoinManager::~MissionMapJoinManager
PUBLIC	?Init@MissionMapJoinManager@mu2@@UEAA_NPEAX@Z	; mu2::MissionMapJoinManager::Init
PUBLIC	?UnInit@MissionMapJoinManager@mu2@@UEAA_NXZ	; mu2::MissionMapJoinManager::UnInit
PUBLIC	?AddMissionMapJoin@MissionMapJoinManager@mu2@@QEAAXIAEBVExecutionZoneId@2@H@Z ; mu2::MissionMapJoinManager::AddMissionMapJoin
PUBLIC	?FindMissionMapJoin@MissionMapJoinManager@mu2@@QEAA_NPEAVEntityPlayer@2@AEAUMissionMapJoinInfo@12@@Z ; mu2::MissionMapJoinManager::FindMissionMapJoin
PUBLIC	?Update@MissionMapJoinManager@mu2@@QEAAXXZ	; mu2::MissionMapJoinManager::Update
PUBLIC	??0MissionMapJoinManager@mu2@@AEAA@XZ		; mu2::MissionMapJoinManager::MissionMapJoinManager
PUBLIC	??_GMissionMapJoinManager@mu2@@UEAAPEAXI@Z	; mu2::MissionMapJoinManager::`scalar deleting destructor'
PUBLIC	??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
PUBLIC	??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ ; std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >,std::_Iterator_base0>::operator++
PUBLIC	??$erase@V?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@@std@@$0A@@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@QEAA?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@@1@V21@@Z ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::erase<std::_Tree_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > > >,0>
PUBLIC	??$_Find@I@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@AEBAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBI@Z ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::_Find<unsigned int>
PUBLIC	??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::_Erase_head<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >
PUBLIC	??$_Try_emplace@AEBI$$V@?$map@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@@std@@AEAA?AU?$pair@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@_N@1@AEBI@Z ; std::map<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::_Try_emplace<unsigned int const &>
PUBLIC	??$_Find_lower_bound@I@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@IEBA?AU?$_Tree_find_result@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@AEBI@Z ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::_Find_lower_bound<unsigned int>
PUBLIC	??$_Lower_bound_duplicate@I@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@IEBA_NQEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBI@Z ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::_Lower_bound_duplicate<unsigned int>
PUBLIC	??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >
PUBLIC	??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ ; std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >
PUBLIC	??1?$_Tree_temp_node_alloc@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ ; std::_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >::~_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >
PUBLIC	??1?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ ; std::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >::~_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >
PUBLIC	??$?0AEBUpiecewise_construct_t@std@@V?$tuple@AEBI@1@V?$tuple@$$V@1@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBUpiecewise_construct_t@1@$$QEAV?$tuple@AEBI@1@$$QEAV?$tuple@$$V@1@@Z ; std::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > ><std::piecewise_construct_t const &,std::tuple<unsigned int const &>,std::tuple<> >
PUBLIC	??$_Freenode@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@SAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU01@@Z ; std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *>::_Freenode<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >
PUBLIC	??$_Buyheadnode@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@@Z ; std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *>::_Buyheadnode<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >
PUBLIC	??$?0AEBI$$Z$$V@?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@QEAA@Upiecewise_construct_t@1@V?$tuple@AEBI@1@V?$tuple@$$V@1@@Z ; std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo><unsigned int const &>
PUBLIC	??_7exception@std@@6B@				; std::exception::`vftable'
PUBLIC	??_C@_0BC@EOODALEL@Unknown?5exception@		; `string'
PUBLIC	??_7bad_alloc@std@@6B@				; std::bad_alloc::`vftable'
PUBLIC	??_7bad_array_new_length@std@@6B@		; std::bad_array_new_length::`vftable'
PUBLIC	??_C@_0BF@KINCDENJ@bad?5array?5new?5length@	; `string'
PUBLIC	??_R0?AVexception@std@@@8			; std::exception `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
PUBLIC	_TI3?AVbad_array_new_length@std@@
PUBLIC	_CTA3?AVbad_array_new_length@std@@
PUBLIC	??_R0?AVbad_array_new_length@std@@@8		; std::bad_array_new_length `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
PUBLIC	??_R0?AVbad_alloc@std@@@8			; std::bad_alloc `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
PUBLIC	??_C@_0BB@GCADKGJO@map?1set?5too?5long@		; `string'
PUBLIC	??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@ ; `string'
PUBLIC	??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@		; `string'
PUBLIC	??_7ISerializer@mu2@@6B@			; mu2::ISerializer::`vftable'
PUBLIC	??_C@_06BALNJMNP@player@			; `string'
PUBLIC	??_7?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@6B@ ; mu2::ISingleton<mu2::MissionMapJoinManager>::`vftable'
PUBLIC	??_7MissionMapJoinManager@mu2@@6B@		; mu2::MissionMapJoinManager::`vftable'
PUBLIC	??_R4exception@std@@6B@				; std::exception::`RTTI Complete Object Locator'
PUBLIC	??_R3exception@std@@8				; std::exception::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2exception@std@@8				; std::exception::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@exception@std@@8			; std::exception::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4bad_array_new_length@std@@6B@		; std::bad_array_new_length::`RTTI Complete Object Locator'
PUBLIC	??_R3bad_array_new_length@std@@8		; std::bad_array_new_length::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2bad_array_new_length@std@@8		; std::bad_array_new_length::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@bad_array_new_length@std@@8	; std::bad_array_new_length::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@bad_alloc@std@@8			; std::bad_alloc::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R3bad_alloc@std@@8				; std::bad_alloc::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2bad_alloc@std@@8				; std::bad_alloc::`RTTI Base Class Array'
PUBLIC	??_R4bad_alloc@std@@6B@				; std::bad_alloc::`RTTI Complete Object Locator'
PUBLIC	??_R4ISerializer@mu2@@6B@			; mu2::ISerializer::`RTTI Complete Object Locator'
PUBLIC	??_R0?AUISerializer@mu2@@@8			; mu2::ISerializer `RTTI Type Descriptor'
PUBLIC	??_R3ISerializer@mu2@@8				; mu2::ISerializer::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2ISerializer@mu2@@8				; mu2::ISerializer::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@ISerializer@mu2@@8		; mu2::ISerializer::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@6B@ ; mu2::ISingleton<mu2::MissionMapJoinManager>::`RTTI Complete Object Locator'
PUBLIC	??_R0?AV?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@@8 ; mu2::ISingleton<mu2::MissionMapJoinManager> `RTTI Type Descriptor'
PUBLIC	??_R3?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@8 ; mu2::ISingleton<mu2::MissionMapJoinManager>::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@8 ; mu2::ISingleton<mu2::MissionMapJoinManager>::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@8 ; mu2::ISingleton<mu2::MissionMapJoinManager>::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A ; mu2::ISingleton<mu2::ExecutionManager>::inst
PUBLIC	??_C@_0FE@MIEGOCEM@F?3?2Release_Branch?2Server?2Develo@ ; `string'
PUBLIC	??_C@_0DJ@CHGGIMOF@charId?5?$CG?$CG?5execId?4IsValidStong?$CI?$CJ@ ; `string'
PUBLIC	??_C@_0CO@CJOILFDB@mu2?3?3MissionMapJoinManager?3?3Add@ ; `string'
PUBLIC	??_C@_0CP@OEPIMKPF@mu2?3?3MissionMapJoinManager?3?3Fin@ ; `string'
PUBLIC	??_R4MissionMapJoinManager@mu2@@6B@		; mu2::MissionMapJoinManager::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVMissionMapJoinManager@mu2@@@8		; mu2::MissionMapJoinManager `RTTI Type Descriptor'
PUBLIC	??_R3MissionMapJoinManager@mu2@@8		; mu2::MissionMapJoinManager::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2MissionMapJoinManager@mu2@@8		; mu2::MissionMapJoinManager::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@MissionMapJoinManager@mu2@@8	; mu2::MissionMapJoinManager::`RTTI Base Class Descriptor at (0,-1,0,64)'
EXTRN	??_L@YAXPEAX_K1P6AX0@Z2@Z:PROC			; `eh vector constructor iterator'
EXTRN	??_M@YAXPEAX_K1P6AX0@Z@Z:PROC			; `eh vector destructor iterator'
EXTRN	_purecall:PROC
EXTRN	??2@YAPEAX_K@Z:PROC				; operator new
EXTRN	??3@YAXPEAX_K@Z:PROC				; operator delete
EXTRN	atexit:PROC
EXTRN	__std_terminate:PROC
EXTRN	_invoke_watson:PROC
EXTRN	__imp_GetStdHandle:PROC
EXTRN	__imp_GetTickCount:PROC
EXTRN	__imp_SetConsoleTextAttribute:PROC
EXTRN	__std_exception_copy:PROC
EXTRN	__std_exception_destroy:PROC
EXTRN	??_Eexception@std@@UEAAPEAXI@Z:PROC		; std::exception::`vector deleting destructor'
EXTRN	??_Ebad_alloc@std@@UEAAPEAXI@Z:PROC		; std::bad_alloc::`vector deleting destructor'
EXTRN	??_Ebad_array_new_length@std@@UEAAPEAXI@Z:PROC	; std::bad_array_new_length::`vector deleting destructor'
EXTRN	?_Xlength_error@std@@YAXPEBD@Z:PROC		; std::_Xlength_error
EXTRN	?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ:PROC	; mu2::Logger::Logging
EXTRN	??_EISerializer@mu2@@UEAAPEAXI@Z:PROC		; mu2::ISerializer::`vector deleting destructor'
EXTRN	??0ExecutionZoneId@mu2@@QEAA@XZ:PROC		; mu2::ExecutionZoneId::ExecutionZoneId
EXTRN	?IsValidStong@ExecutionZoneId@mu2@@QEBA?B_NXZ:PROC ; mu2::ExecutionZoneId::IsValidStong
EXTRN	?GetCharId@EntityPlayer@mu2@@QEBAIXZ:PROC	; mu2::EntityPlayer::GetCharId
EXTRN	??_E?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@UEAAPEAXI@Z:PROC ; mu2::ISingleton<mu2::MissionMapJoinManager>::`vector deleting destructor'
EXTRN	??_EMissionMapJoinManager@mu2@@UEAAPEAXI@Z:PROC	; mu2::MissionMapJoinManager::`vector deleting destructor'
EXTRN	??1ExecutionManager@mu2@@UEAA@XZ:PROC		; mu2::ExecutionManager::~ExecutionManager
EXTRN	?GetExecution@ExecutionManager@mu2@@QEAAAEBVExecution@2@AEBVExecutionZoneId@2@@Z:PROC ; mu2::ExecutionManager::GetExecution
EXTRN	??0ExecutionManager@mu2@@AEAA@XZ:PROC		; mu2::ExecutionManager::ExecutionManager
EXTRN	_CxxThrowException:PROC
EXTRN	__CxxFrameHandler4:PROC
EXTRN	??_7type_info@@6B@:BYTE				; type_info::`vftable'
;	COMDAT ?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A
_BSS	SEGMENT
?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A DB 01e0H DUP (?) ; mu2::ISingleton<mu2::ExecutionManager>::inst
_BSS	ENDS
;	COMDAT ?tickUpdated@?1??Update@MissionMapJoinManager@mu2@@QEAAXXZ@4IA
_BSS	SEGMENT
?tickUpdated@?1??Update@MissionMapJoinManager@mu2@@QEAAXXZ@4IA DD 01H DUP (?) ; `mu2::MissionMapJoinManager::Update'::`2'::tickUpdated
_BSS	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0exception@std@@QEAA@AEBV01@@Z DD imagerel $LN4
	DD	imagerel $LN4+89
	DD	imagerel $unwind$??0exception@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?what@exception@std@@UEBAPEBDXZ DD imagerel $LN5
	DD	imagerel $LN5+56
	DD	imagerel $unwind$?what@exception@std@@UEBAPEBDXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gexception@std@@UEAAPEAXI@Z DD imagerel $LN10
	DD	imagerel $LN10+83
	DD	imagerel $unwind$??_Gexception@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z DD imagerel $LN9
	DD	imagerel $LN9+104
	DD	imagerel $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+83
	DD	imagerel $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_array_new_length@std@@QEAA@XZ DD imagerel $LN14
	DD	imagerel $LN14+95
	DD	imagerel $unwind$??0bad_array_new_length@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1bad_array_new_length@std@@UEAA@XZ DD imagerel $LN14
	DD	imagerel $LN14+47
	DD	imagerel $unwind$??1bad_array_new_length@std@@UEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z DD imagerel $LN14
	DD	imagerel $LN14+54
	DD	imagerel $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z DD imagerel $LN20
	DD	imagerel $LN20+83
	DD	imagerel $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Throw_bad_array_new_length@std@@YAXXZ DD imagerel $LN3
	DD	imagerel $LN3+37
	DD	imagerel $unwind$?_Throw_bad_array_new_length@std@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z DD imagerel $LN5
	DD	imagerel $LN5+159
	DD	imagerel $unwind$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Throw_tree_length_error@std@@YAXXZ DD imagerel $LN3
	DD	imagerel $LN3+22
	DD	imagerel $unwind$?_Throw_tree_length_error@std@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GISerializer@mu2@@UEAAPEAXI@Z DD imagerel $LN10
	DD	imagerel $LN10+65
	DD	imagerel $unwind$??_GISerializer@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_G?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@UEAAPEAXI@Z DD imagerel $LN10
	DD	imagerel $LN10+65
	DD	imagerel $unwind$??_G?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?allocate@?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@_K@Z DD imagerel $LN13
	DD	imagerel $LN13+160
	DD	imagerel $unwind$?allocate@?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@QEAA@XZ DD imagerel $LN154
	DD	imagerel $LN154+83
	DD	imagerel $unwind$??1?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?max_size@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@QEBA_KXZ DD imagerel $LN31
	DD	imagerel $LN31+152
	DD	imagerel $unwind$?max_size@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@QEBA_KXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Erase_unchecked@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@AEAAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z DD imagerel $LN200
	DD	imagerel $LN200+132
	DD	imagerel $unwind$?_Erase_unchecked@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@AEAAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Check_grow_by_1@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@IEAAXXZ DD imagerel $LN46
	DD	imagerel $LN46+61
	DD	imagerel $unwind$?_Check_grow_by_1@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@IEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Alloc_sentinel_and_proxy@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@IEAAXXZ DD imagerel $LN107
	DD	imagerel $LN107+114
	DD	imagerel $unwind$?_Alloc_sentinel_and_proxy@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@IEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Lrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@@Z DD imagerel $LN9
	DD	imagerel $LN9+212
	DD	imagerel $unwind$?_Lrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Rrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@@Z DD imagerel $LN9
	DD	imagerel $LN9+215
	DD	imagerel $unwind$?_Rrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Extract@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z DD imagerel $LN157
	DD	imagerel $LN157+1703
	DD	imagerel $unwind$?_Extract@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Insert_node@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@U?$_Tree_id@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@2@QEAU32@@Z DD imagerel $LN60
	DD	imagerel $LN60+744
	DD	imagerel $unwind$?_Insert_node@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@U?$_Tree_id@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@2@QEAU32@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0?$map@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@@std@@QEAA@XZ DD imagerel $LN142
	DD	imagerel $LN142+107
	DD	imagerel $unwind$??0?$map@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$map@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@@std@@QEAA@XZ DD imagerel $LN159
	DD	imagerel $LN159+25
	DD	imagerel $unwind$??1?$map@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1MissionMapJoinManager@mu2@@UEAA@XZ DD imagerel $LN14
	DD	imagerel $LN14+80
	DD	imagerel $unwind$??1MissionMapJoinManager@mu2@@UEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?AddMissionMapJoin@MissionMapJoinManager@mu2@@QEAAXIAEBVExecutionZoneId@2@H@Z DD imagerel $LN181
	DD	imagerel $LN181+544
	DD	imagerel $unwind$?AddMissionMapJoin@MissionMapJoinManager@mu2@@QEAAXIAEBVExecutionZoneId@2@H@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0??AddMissionMapJoin@MissionMapJoinManager@mu2@@QEAAXIAEBVExecutionZoneId@2@H@Z@4HA DD imagerel ?dtor$0@?0??AddMissionMapJoin@MissionMapJoinManager@mu2@@QEAAXIAEBVExecutionZoneId@2@H@Z@4HA
	DD	imagerel ?dtor$0@?0??AddMissionMapJoin@MissionMapJoinManager@mu2@@QEAAXIAEBVExecutionZoneId@2@H@Z@4HA+27
	DD	imagerel $unwind$?dtor$0@?0??AddMissionMapJoin@MissionMapJoinManager@mu2@@QEAAXIAEBVExecutionZoneId@2@H@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?FindMissionMapJoin@MissionMapJoinManager@mu2@@QEAA_NPEAVEntityPlayer@2@AEAUMissionMapJoinInfo@12@@Z DD imagerel $LN347
	DD	imagerel $LN347+958
	DD	imagerel $unwind$?FindMissionMapJoin@MissionMapJoinManager@mu2@@QEAA_NPEAVEntityPlayer@2@AEAUMissionMapJoinInfo@12@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Update@MissionMapJoinManager@mu2@@QEAAXXZ DD imagerel $LN545
	DD	imagerel $LN545+807
	DD	imagerel $unwind$?Update@MissionMapJoinManager@mu2@@QEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0MissionMapJoinManager@mu2@@AEAA@XZ DD imagerel $LN10
	DD	imagerel $LN10+119
	DD	imagerel $unwind$??0MissionMapJoinManager@mu2@@AEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???0MissionMapJoinManager@mu2@@AEAA@XZ@4HA DD imagerel ?dtor$0@?0???0MissionMapJoinManager@mu2@@AEAA@XZ@4HA
	DD	imagerel ?dtor$0@?0???0MissionMapJoinManager@mu2@@AEAA@XZ@4HA+24
	DD	imagerel $unwind$?dtor$0@?0???0MissionMapJoinManager@mu2@@AEAA@XZ@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GMissionMapJoinManager@mu2@@UEAAPEAXI@Z DD imagerel $LN5
	DD	imagerel $LN5+60
	DD	imagerel $unwind$??_GMissionMapJoinManager@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z DD imagerel $LN7
	DD	imagerel $LN7+150
	DD	imagerel $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ DD imagerel $LN15
	DD	imagerel $LN15+184
	DD	imagerel $unwind$??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$erase@V?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@@std@@$0A@@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@QEAA?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@@1@V21@@Z DD imagerel $LN178
	DD	imagerel $LN178+159
	DD	imagerel $unwind$??$erase@V?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@@std@@$0A@@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@QEAA?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@@1@V21@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Find@I@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@AEBAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBI@Z DD imagerel $LN72
	DD	imagerel $LN72+102
	DD	imagerel $unwind$??$_Find@I@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@AEBAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@@Z DD imagerel $LN129
	DD	imagerel $LN129+126
	DD	imagerel $unwind$??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Try_emplace@AEBI$$V@?$map@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@@std@@AEAA?AU?$pair@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@_N@1@AEBI@Z DD imagerel $LN375
	DD	imagerel $LN375+863
	DD	imagerel $unwind$??$_Try_emplace@AEBI$$V@?$map@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@@std@@AEAA?AU?$pair@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@_N@1@AEBI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Find_lower_bound@I@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@IEBA?AU?$_Tree_find_result@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@AEBI@Z DD imagerel $LN36
	DD	imagerel $LN36+320
	DD	imagerel $unwind$??$_Find_lower_bound@I@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@IEBA?AU?$_Tree_find_result@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@AEBI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Lower_bound_duplicate@I@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@IEBA_NQEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBI@Z DD imagerel $LN24
	DD	imagerel $LN24+147
	DD	imagerel $unwind$??$_Lower_bound_duplicate@I@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@IEBA_NQEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@@Z DD imagerel $LN84
	DD	imagerel $LN84+125
	DD	imagerel $unwind$??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ DD imagerel $LN20
	DD	imagerel $LN20+120
	DD	imagerel $unwind$??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$_Tree_temp_node_alloc@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ DD imagerel $LN25
	DD	imagerel $LN25+25
	DD	imagerel $unwind$??1?$_Tree_temp_node_alloc@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ DD imagerel $LN79
	DD	imagerel $LN79+183
	DD	imagerel $unwind$??1?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$?0AEBUpiecewise_construct_t@std@@V?$tuple@AEBI@1@V?$tuple@$$V@1@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBUpiecewise_construct_t@1@$$QEAV?$tuple@AEBI@1@$$QEAV?$tuple@$$V@1@@Z DD imagerel $LN159
	DD	imagerel $LN159+733
	DD	imagerel $unwind$??$?0AEBUpiecewise_construct_t@std@@V?$tuple@AEBI@1@V?$tuple@$$V@1@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBUpiecewise_construct_t@1@$$QEAV?$tuple@AEBI@1@$$QEAV?$tuple@$$V@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$1@?0???$?0AEBUpiecewise_construct_t@std@@V?$tuple@AEBI@1@V?$tuple@$$V@1@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBUpiecewise_construct_t@1@$$QEAV?$tuple@AEBI@1@$$QEAV?$tuple@$$V@1@@Z@4HA DD imagerel ?dtor$1@?0???$?0AEBUpiecewise_construct_t@std@@V?$tuple@AEBI@1@V?$tuple@$$V@1@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBUpiecewise_construct_t@1@$$QEAV?$tuple@AEBI@1@$$QEAV?$tuple@$$V@1@@Z@4HA
	DD	imagerel ?dtor$1@?0???$?0AEBUpiecewise_construct_t@std@@V?$tuple@AEBI@1@V?$tuple@$$V@1@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBUpiecewise_construct_t@1@$$QEAV?$tuple@AEBI@1@$$QEAV?$tuple@$$V@1@@Z@4HA+27
	DD	imagerel $unwind$?dtor$1@?0???$?0AEBUpiecewise_construct_t@std@@V?$tuple@AEBI@1@V?$tuple@$$V@1@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBUpiecewise_construct_t@1@$$QEAV?$tuple@AEBI@1@$$QEAV?$tuple@$$V@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???$?0AEBUpiecewise_construct_t@std@@V?$tuple@AEBI@1@V?$tuple@$$V@1@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBUpiecewise_construct_t@1@$$QEAV?$tuple@AEBI@1@$$QEAV?$tuple@$$V@1@@Z@4HA DD imagerel ?dtor$0@?0???$?0AEBUpiecewise_construct_t@std@@V?$tuple@AEBI@1@V?$tuple@$$V@1@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBUpiecewise_construct_t@1@$$QEAV?$tuple@AEBI@1@$$QEAV?$tuple@$$V@1@@Z@4HA
	DD	imagerel ?dtor$0@?0???$?0AEBUpiecewise_construct_t@std@@V?$tuple@AEBI@1@V?$tuple@$$V@1@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBUpiecewise_construct_t@1@$$QEAV?$tuple@AEBI@1@$$QEAV?$tuple@$$V@1@@Z@4HA+27
	DD	imagerel $unwind$?dtor$0@?0???$?0AEBUpiecewise_construct_t@std@@V?$tuple@AEBI@1@V?$tuple@$$V@1@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBUpiecewise_construct_t@1@$$QEAV?$tuple@AEBI@1@$$QEAV?$tuple@$$V@1@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__E?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ DD imagerel ??__E?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ
	DD	imagerel ??__E?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ+34
	DD	imagerel $unwind$??__E?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??__F?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ DD imagerel ??__F?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ
	DD	imagerel ??__F?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ+22
	DD	imagerel $unwind$??__F?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Freenode@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@SAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU01@@Z DD imagerel $LN72
	DD	imagerel $LN72+151
	DD	imagerel $unwind$??$_Freenode@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@SAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Buyheadnode@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@@Z DD imagerel $LN75
	DD	imagerel $LN75+296
	DD	imagerel $unwind$??$_Buyheadnode@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$?0AEBI$$Z$$V@?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@QEAA@Upiecewise_construct_t@1@V?$tuple@AEBI@1@V?$tuple@$$V@1@@Z DD imagerel $LN20
	DD	imagerel $LN20+177
	DD	imagerel $unwind$??$?0AEBI$$Z$$V@?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@QEAA@Upiecewise_construct_t@1@V?$tuple@AEBI@1@V?$tuple@$$V@1@@Z
pdata	ENDS
;	COMDAT CRT$XCU
CRT$XCU	SEGMENT
??inst$initializer$@?$ISingleton@VExecutionManager@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA DQ FLAT:??__E?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ ; ??inst$initializer$@?$ISingleton@VExecutionManager@mu2@@@mu2@@1P6AXXZEA@@3P6AXXZEA
CRT$XCU	ENDS
;	COMDAT ??_R1A@?0A@EA@MissionMapJoinManager@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@MissionMapJoinManager@mu2@@8 DD imagerel ??_R0?AVMissionMapJoinManager@mu2@@@8 ; mu2::MissionMapJoinManager::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3MissionMapJoinManager@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2MissionMapJoinManager@mu2@@8
rdata$r	SEGMENT
??_R2MissionMapJoinManager@mu2@@8 DD imagerel ??_R1A@?0A@EA@MissionMapJoinManager@mu2@@8 ; mu2::MissionMapJoinManager::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3MissionMapJoinManager@mu2@@8
rdata$r	SEGMENT
??_R3MissionMapJoinManager@mu2@@8 DD 00H		; mu2::MissionMapJoinManager::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2MissionMapJoinManager@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVMissionMapJoinManager@mu2@@@8
data$rs	SEGMENT
??_R0?AVMissionMapJoinManager@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::MissionMapJoinManager `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVMissionMapJoinManager@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4MissionMapJoinManager@mu2@@6B@
rdata$r	SEGMENT
??_R4MissionMapJoinManager@mu2@@6B@ DD 01H		; mu2::MissionMapJoinManager::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVMissionMapJoinManager@mu2@@@8
	DD	imagerel ??_R3MissionMapJoinManager@mu2@@8
	DD	imagerel ??_R4MissionMapJoinManager@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_C@_0CP@OEPIMKPF@mu2?3?3MissionMapJoinManager?3?3Fin@
CONST	SEGMENT
??_C@_0CP@OEPIMKPF@mu2?3?3MissionMapJoinManager?3?3Fin@ DB 'mu2::MissionM'
	DB	'apJoinManager::FindMissionMapJoin', 00H	; `string'
CONST	ENDS
;	COMDAT ??_C@_0CO@CJOILFDB@mu2?3?3MissionMapJoinManager?3?3Add@
CONST	SEGMENT
??_C@_0CO@CJOILFDB@mu2?3?3MissionMapJoinManager?3?3Add@ DB 'mu2::MissionM'
	DB	'apJoinManager::AddMissionMapJoin', 00H	; `string'
CONST	ENDS
;	COMDAT ??_C@_0DJ@CHGGIMOF@charId?5?$CG?$CG?5execId?4IsValidStong?$CI?$CJ@
CONST	SEGMENT
??_C@_0DJ@CHGGIMOF@charId?5?$CG?$CG?5execId?4IsValidStong?$CI?$CJ@ DB 'ch'
	DB	'arId && execId.IsValidStong() && startingPositionIndex', 00H ; `string'
CONST	ENDS
;	COMDAT ??_C@_0FE@MIEGOCEM@F?3?2Release_Branch?2Server?2Develo@
CONST	SEGMENT
??_C@_0FE@MIEGOCEM@F?3?2Release_Branch?2Server?2Develo@ DB 'F:\Release_Br'
	DB	'anch\Server\Development\Frontend\WorldServer\MissionMapJoinMa'
	DB	'nager.cpp', 00H				; `string'
CONST	ENDS
;	COMDAT ??_R1A@?0A@EA@?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@8 DD imagerel ??_R0?AV?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@@8 ; mu2::ISingleton<mu2::MissionMapJoinManager>::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@8
rdata$r	SEGMENT
??_R2?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@8 DD imagerel ??_R1A@?0A@EA@?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@8 ; mu2::ISingleton<mu2::MissionMapJoinManager>::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@8
rdata$r	SEGMENT
??_R3?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@8 DD 00H ; mu2::ISingleton<mu2::MissionMapJoinManager>::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AV?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@@8
data$rs	SEGMENT
??_R0?AV?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::ISingleton<mu2::MissionMapJoinManager> `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AV?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@6B@
rdata$r	SEGMENT
??_R4?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@6B@ DD 01H ; mu2::ISingleton<mu2::MissionMapJoinManager>::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AV?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@@8
	DD	imagerel ??_R3?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@8
	DD	imagerel ??_R4?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@ISerializer@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@ISerializer@mu2@@8 DD imagerel ??_R0?AUISerializer@mu2@@@8 ; mu2::ISerializer::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3ISerializer@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2ISerializer@mu2@@8
rdata$r	SEGMENT
??_R2ISerializer@mu2@@8 DD imagerel ??_R1A@?0A@EA@ISerializer@mu2@@8 ; mu2::ISerializer::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3ISerializer@mu2@@8
rdata$r	SEGMENT
??_R3ISerializer@mu2@@8 DD 00H				; mu2::ISerializer::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2ISerializer@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AUISerializer@mu2@@@8
data$rs	SEGMENT
??_R0?AUISerializer@mu2@@@8 DQ FLAT:??_7type_info@@6B@	; mu2::ISerializer `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AUISerializer@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4ISerializer@mu2@@6B@
rdata$r	SEGMENT
??_R4ISerializer@mu2@@6B@ DD 01H			; mu2::ISerializer::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AUISerializer@mu2@@@8
	DD	imagerel ??_R3ISerializer@mu2@@8
	DD	imagerel ??_R4ISerializer@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R4bad_alloc@std@@6B@
rdata$r	SEGMENT
??_R4bad_alloc@std@@6B@ DD 01H				; std::bad_alloc::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVbad_alloc@std@@@8
	DD	imagerel ??_R3bad_alloc@std@@8
	DD	imagerel ??_R4bad_alloc@std@@6B@
rdata$r	ENDS
;	COMDAT ??_R2bad_alloc@std@@8
rdata$r	SEGMENT
??_R2bad_alloc@std@@8 DD imagerel ??_R1A@?0A@EA@bad_alloc@std@@8 ; std::bad_alloc::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@exception@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3bad_alloc@std@@8
rdata$r	SEGMENT
??_R3bad_alloc@std@@8 DD 00H				; std::bad_alloc::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2bad_alloc@std@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@bad_alloc@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@bad_alloc@std@@8 DD imagerel ??_R0?AVbad_alloc@std@@@8 ; std::bad_alloc::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3bad_alloc@std@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@bad_array_new_length@std@@8 DD imagerel ??_R0?AVbad_array_new_length@std@@@8 ; std::bad_array_new_length::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	02H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3bad_array_new_length@std@@8
rdata$r	ENDS
;	COMDAT ??_R2bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R2bad_array_new_length@std@@8 DD imagerel ??_R1A@?0A@EA@bad_array_new_length@std@@8 ; std::bad_array_new_length::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@bad_alloc@std@@8
	DD	imagerel ??_R1A@?0A@EA@exception@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R3bad_array_new_length@std@@8 DD 00H			; std::bad_array_new_length::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	03H
	DD	imagerel ??_R2bad_array_new_length@std@@8
rdata$r	ENDS
;	COMDAT ??_R4bad_array_new_length@std@@6B@
rdata$r	SEGMENT
??_R4bad_array_new_length@std@@6B@ DD 01H		; std::bad_array_new_length::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVbad_array_new_length@std@@@8
	DD	imagerel ??_R3bad_array_new_length@std@@8
	DD	imagerel ??_R4bad_array_new_length@std@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@exception@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@exception@std@@8 DD imagerel ??_R0?AVexception@std@@@8 ; std::exception::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3exception@std@@8
rdata$r	ENDS
;	COMDAT ??_R2exception@std@@8
rdata$r	SEGMENT
??_R2exception@std@@8 DD imagerel ??_R1A@?0A@EA@exception@std@@8 ; std::exception::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3exception@std@@8
rdata$r	SEGMENT
??_R3exception@std@@8 DD 00H				; std::exception::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2exception@std@@8
rdata$r	ENDS
;	COMDAT ??_R4exception@std@@6B@
rdata$r	SEGMENT
??_R4exception@std@@6B@ DD 01H				; std::exception::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVexception@std@@@8
	DD	imagerel ??_R3exception@std@@8
	DD	imagerel ??_R4exception@std@@6B@
rdata$r	ENDS
;	COMDAT ??_7MissionMapJoinManager@mu2@@6B@
CONST	SEGMENT
??_7MissionMapJoinManager@mu2@@6B@ DQ FLAT:??_R4MissionMapJoinManager@mu2@@6B@ ; mu2::MissionMapJoinManager::`vftable'
	DQ	FLAT:?Init@MissionMapJoinManager@mu2@@UEAA_NPEAX@Z
	DQ	FLAT:?UnInit@MissionMapJoinManager@mu2@@UEAA_NXZ
	DQ	FLAT:??_EMissionMapJoinManager@mu2@@UEAAPEAXI@Z
CONST	ENDS
;	COMDAT ??_7?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@6B@
CONST	SEGMENT
??_7?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@6B@ DQ FLAT:??_R4?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@6B@ ; mu2::ISingleton<mu2::MissionMapJoinManager>::`vftable'
	DQ	FLAT:_purecall
	DQ	FLAT:_purecall
	DQ	FLAT:??_E?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@UEAAPEAXI@Z
CONST	ENDS
;	COMDAT ??_C@_06BALNJMNP@player@
CONST	SEGMENT
??_C@_06BALNJMNP@player@ DB 'player', 00H		; `string'
CONST	ENDS
;	COMDAT ??_7ISerializer@mu2@@6B@
CONST	SEGMENT
??_7ISerializer@mu2@@6B@ DQ FLAT:??_R4ISerializer@mu2@@6B@ ; mu2::ISerializer::`vftable'
	DQ	FLAT:_purecall
	DQ	FLAT:_purecall
	DQ	FLAT:??_EISerializer@mu2@@UEAAPEAXI@Z
CONST	ENDS
;	COMDAT ??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
CONST	SEGMENT
??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@ DB 'g', 00H, 'a', 00H, 'm', 00H, 'e'
	DB	00H, 00H, 00H				; `string'
CONST	ENDS
;	COMDAT ??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
CONST	SEGMENT
??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@ DB '%'
	DB	's> ASSERT - %s, %s(%d)', 00H		; `string'
CONST	ENDS
;	COMDAT ??_C@_0BB@GCADKGJO@map?1set?5too?5long@
CONST	SEGMENT
??_C@_0BB@GCADKGJO@map?1set?5too?5long@ DB 'map/set too long', 00H ; `string'
CONST	ENDS
;	COMDAT _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 DD 010H
	DD	imagerel ??_R0?AVbad_alloc@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0bad_alloc@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVbad_alloc@std@@@8
data$r	SEGMENT
??_R0?AVbad_alloc@std@@@8 DQ FLAT:??_7type_info@@6B@	; std::bad_alloc `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVbad_alloc@std@@', 00H
data$r	ENDS
;	COMDAT _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 DD 00H
	DD	imagerel ??_R0?AVbad_array_new_length@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0bad_array_new_length@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVbad_array_new_length@std@@@8
data$r	SEGMENT
??_R0?AVbad_array_new_length@std@@@8 DQ FLAT:??_7type_info@@6B@ ; std::bad_array_new_length `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVbad_array_new_length@std@@', 00H
data$r	ENDS
;	COMDAT _CTA3?AVbad_array_new_length@std@@
xdata$x	SEGMENT
_CTA3?AVbad_array_new_length@std@@ DD 03H
	DD	imagerel _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
	DD	imagerel _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
	DD	imagerel _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
xdata$x	ENDS
;	COMDAT _TI3?AVbad_array_new_length@std@@
xdata$x	SEGMENT
_TI3?AVbad_array_new_length@std@@ DD 00H
	DD	imagerel ??1bad_array_new_length@std@@UEAA@XZ
	DD	00H
	DD	imagerel _CTA3?AVbad_array_new_length@std@@
xdata$x	ENDS
;	COMDAT _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 DD 00H
	DD	imagerel ??_R0?AVexception@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0exception@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVexception@std@@@8
data$r	SEGMENT
??_R0?AVexception@std@@@8 DQ FLAT:??_7type_info@@6B@	; std::exception `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVexception@std@@', 00H
data$r	ENDS
;	COMDAT ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@
CONST	SEGMENT
??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ DB 'bad array new length', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7bad_array_new_length@std@@6B@
CONST	SEGMENT
??_7bad_array_new_length@std@@6B@ DQ FLAT:??_R4bad_array_new_length@std@@6B@ ; std::bad_array_new_length::`vftable'
	DQ	FLAT:??_Ebad_array_new_length@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT ??_7bad_alloc@std@@6B@
CONST	SEGMENT
??_7bad_alloc@std@@6B@ DQ FLAT:??_R4bad_alloc@std@@6B@	; std::bad_alloc::`vftable'
	DQ	FLAT:??_Ebad_alloc@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT ??_C@_0BC@EOODALEL@Unknown?5exception@
CONST	SEGMENT
??_C@_0BC@EOODALEL@Unknown?5exception@ DB 'Unknown exception', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7exception@std@@6B@
CONST	SEGMENT
??_7exception@std@@6B@ DQ FLAT:??_R4exception@std@@6B@	; std::exception::`vftable'
	DQ	FLAT:??_Eexception@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$?0AEBI$$Z$$V@?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@QEAA@Upiecewise_construct_t@1@V?$tuple@AEBI@1@V?$tuple@$$V@1@@Z DD 021801H
	DD	070149218H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Buyheadnode@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@@Z DD 020c01H
	DD	015010cH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$_Freenode@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@SAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU01@@Z DB 06H
	DB	00H
	DB	00H
	DB	0ceH
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$_Freenode@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@SAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU01@@Z DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$_Freenode@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@SAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU01@@Z DB 068H
	DD	imagerel $stateUnwindMap$??$_Freenode@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@SAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU01@@Z
	DD	imagerel $ip2state$??$_Freenode@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@SAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU01@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Freenode@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@SAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU01@@Z DD 010e19H
	DD	0820eH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$_Freenode@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@SAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU01@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__F?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??__E?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???$?0AEBUpiecewise_construct_t@std@@V?$tuple@AEBI@1@V?$tuple@$$V@1@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBUpiecewise_construct_t@1@$$QEAV?$tuple@AEBI@1@$$QEAV?$tuple@$$V@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$1@?0???$?0AEBUpiecewise_construct_t@std@@V?$tuple@AEBI@1@V?$tuple@$$V@1@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBUpiecewise_construct_t@1@$$QEAV?$tuple@AEBI@1@$$QEAV?$tuple@$$V@1@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$?0AEBUpiecewise_construct_t@std@@V?$tuple@AEBI@1@V?$tuple@$$V@1@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBUpiecewise_construct_t@1@$$QEAV?$tuple@AEBI@1@$$QEAV?$tuple@$$V@1@@Z DB 08H
	DB	00H
	DB	00H
	DB	'|'
	DB	02H
	DB	'b'
	DB	04H
	DB	'y', 09H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$?0AEBUpiecewise_construct_t@std@@V?$tuple@AEBI@1@V?$tuple@$$V@1@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBUpiecewise_construct_t@1@$$QEAV?$tuple@AEBI@1@$$QEAV?$tuple@$$V@1@@Z DB 04H
	DB	0eH
	DD	imagerel ?dtor$1@?0???$?0AEBUpiecewise_construct_t@std@@V?$tuple@AEBI@1@V?$tuple@$$V@1@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBUpiecewise_construct_t@1@$$QEAV?$tuple@AEBI@1@$$QEAV?$tuple@$$V@1@@Z@4HA
	DB	036H
	DD	imagerel ?dtor$0@?0???$?0AEBUpiecewise_construct_t@std@@V?$tuple@AEBI@1@V?$tuple@$$V@1@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBUpiecewise_construct_t@1@$$QEAV?$tuple@AEBI@1@$$QEAV?$tuple@$$V@1@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$?0AEBUpiecewise_construct_t@std@@V?$tuple@AEBI@1@V?$tuple@$$V@1@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBUpiecewise_construct_t@1@$$QEAV?$tuple@AEBI@1@$$QEAV?$tuple@$$V@1@@Z DB 028H
	DD	imagerel $stateUnwindMap$??$?0AEBUpiecewise_construct_t@std@@V?$tuple@AEBI@1@V?$tuple@$$V@1@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBUpiecewise_construct_t@1@$$QEAV?$tuple@AEBI@1@$$QEAV?$tuple@$$V@1@@Z
	DD	imagerel $ip2state$??$?0AEBUpiecewise_construct_t@std@@V?$tuple@AEBI@1@V?$tuple@$$V@1@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBUpiecewise_construct_t@1@$$QEAV?$tuple@AEBI@1@$$QEAV?$tuple@$$V@1@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$?0AEBUpiecewise_construct_t@std@@V?$tuple@AEBI@1@V?$tuple@$$V@1@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBUpiecewise_construct_t@1@$$QEAV?$tuple@AEBI@1@$$QEAV?$tuple@$$V@1@@Z DD 021b11H
	DD	025011bH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$?0AEBUpiecewise_construct_t@std@@V?$tuple@AEBI@1@V?$tuple@$$V@1@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBUpiecewise_construct_t@1@$$QEAV?$tuple@AEBI@1@$$QEAV?$tuple@$$V@1@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ DD 010901H
	DD	0c209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$_Tree_temp_node_alloc@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ DB 06H
	DB	00H
	DB	00H
	DB	090H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ DB 068H
	DD	imagerel $stateUnwindMap$??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ
	DD	imagerel $ip2state$??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ DD 010919H
	DD	08209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@@Z DD 011301H
	DD	08213H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Lower_bound_duplicate@I@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@IEBA_NQEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBI@Z DD 011301H
	DD	06213H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Find_lower_bound@I@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@IEBA?AU?$_Tree_find_result@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@AEBI@Z DD 031501H
	DD	07011c215H
	DD	06010H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Try_emplace@AEBI$$V@?$map@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@@std@@AEAA?AU?$pair@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@_N@1@AEBI@Z DD 041801H
	DD	02d0118H
	DD	060107011H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@@Z DB 06H
	DB	00H
	DB	00H
	DB	09cH
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@@Z DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@@Z DB 068H
	DD	imagerel $stateUnwindMap$??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@@Z
	DD	imagerel $ip2state$??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@@Z DD 010e19H
	DD	0820eH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Find@I@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@AEBAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBI@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$erase@V?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@@std@@$0A@@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@QEAA?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@@1@V21@@Z DD 011301H
	DD	0e213H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z DD 010901H
	DD	0a209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GMissionMapJoinManager@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???0MissionMapJoinManager@mu2@@AEAA@XZ@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??0MissionMapJoinManager@mu2@@AEAA@XZ DB 06H
	DB	00H
	DB	00H
	DB	'0'
	DB	02H
	DB	0aaH
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??0MissionMapJoinManager@mu2@@AEAA@XZ DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???0MissionMapJoinManager@mu2@@AEAA@XZ@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??0MissionMapJoinManager@mu2@@AEAA@XZ DB 028H
	DD	imagerel $stateUnwindMap$??0MissionMapJoinManager@mu2@@AEAA@XZ
	DD	imagerel $ip2state$??0MissionMapJoinManager@mu2@@AEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0MissionMapJoinManager@mu2@@AEAA@XZ DD 010911H
	DD	08209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??0MissionMapJoinManager@mu2@@AEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Update@MissionMapJoinManager@mu2@@QEAAXXZ DD 020c01H
	DD	023010cH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?FindMissionMapJoin@MissionMapJoinManager@mu2@@QEAA_NPEAVEntityPlayer@2@AEAUMissionMapJoinInfo@12@@Z DD 021601H
	DD	02d0116H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0??AddMissionMapJoin@MissionMapJoinManager@mu2@@QEAAXIAEBVExecutionZoneId@2@H@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?AddMissionMapJoin@MissionMapJoinManager@mu2@@QEAAXIAEBVExecutionZoneId@2@H@Z DB 06H
	DB	00H
	DB	00H
	DB	'-', 04H
	DB	02H
	DB	0f9H, 03H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?AddMissionMapJoin@MissionMapJoinManager@mu2@@QEAAXIAEBVExecutionZoneId@2@H@Z DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0??AddMissionMapJoin@MissionMapJoinManager@mu2@@QEAAXIAEBVExecutionZoneId@2@H@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?AddMissionMapJoin@MissionMapJoinManager@mu2@@QEAAXIAEBVExecutionZoneId@2@H@Z DB 028H
	DD	imagerel $stateUnwindMap$?AddMissionMapJoin@MissionMapJoinManager@mu2@@QEAAXIAEBVExecutionZoneId@2@H@Z
	DD	imagerel $ip2state$?AddMissionMapJoin@MissionMapJoinManager@mu2@@QEAAXIAEBVExecutionZoneId@2@H@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?AddMissionMapJoin@MissionMapJoinManager@mu2@@QEAAXIAEBVExecutionZoneId@2@H@Z DD 021a11H
	DD	01b011aH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?AddMissionMapJoin@MissionMapJoinManager@mu2@@QEAAXIAEBVExecutionZoneId@2@H@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??1MissionMapJoinManager@mu2@@UEAA@XZ DB 06H
	DB	00H
	DB	00H
	DB	'0'
	DB	02H
	DB	'H'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??1MissionMapJoinManager@mu2@@UEAA@XZ DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??1MissionMapJoinManager@mu2@@UEAA@XZ DB 068H
	DD	imagerel $stateUnwindMap$??1MissionMapJoinManager@mu2@@UEAA@XZ
	DD	imagerel $ip2state$??1MissionMapJoinManager@mu2@@UEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1MissionMapJoinManager@mu2@@UEAA@XZ DD 010919H
	DD	04209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??1MissionMapJoinManager@mu2@@UEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$map@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@@std@@QEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0?$map@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@@std@@QEAA@XZ DD 010901H
	DD	0a209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Insert_node@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@U?$_Tree_id@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@2@QEAU32@@Z DD 011301H
	DD	08213H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Extract@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z DD 021101H
	DD	0150111H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Rrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@@Z DD 010e01H
	DD	0220eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Lrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@@Z DD 010e01H
	DD	0220eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Alloc_sentinel_and_proxy@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@IEAAXXZ DD 020a01H
	DD	07006b20aH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Check_grow_by_1@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@IEAAXXZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Erase_unchecked@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@AEAAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z DD 010e01H
	DD	0c20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?max_size@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@QEBA_KXZ DD 010901H
	DD	0a209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@QEAA@XZ DD 010901H
	DD	0a209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?allocate@?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_G?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GISerializer@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Throw_tree_length_error@std@@YAXXZ DD 010401H
	DD	04204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Throw_bad_array_new_length@std@@YAXXZ DD 010401H
	DD	08204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1bad_array_new_length@std@@UEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_array_new_length@std@@QEAA@XZ DD 010601H
	DD	07006H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z DD 020f01H
	DD	0700b320fH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gexception@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?what@exception@std@@UEBAPEBDXZ DD 010901H
	DD	02209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0exception@std@@QEAA@AEBV01@@Z DD 020f01H
	DD	0700b320fH
xdata	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\tuple
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
;	COMDAT ??$?0AEBI$$Z$$V@?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@QEAA@Upiecewise_construct_t@1@V?$tuple@AEBI@1@V?$tuple@$$V@1@@Z
_TEXT	SEGMENT
$T1 = 32
$T2 = 33
__formal$ = 40
__formal$ = 48
$T3 = 56
$T4 = 64
this$ = 72
this$ = 96
__formal$ = 104
_Val1$ = 112
_Val2$ = 120
??$?0AEBI$$Z$$V@?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@QEAA@Upiecewise_construct_t@1@V?$tuple@AEBI@1@V?$tuple@$$V@1@@Z PROC ; std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo><unsigned int const &>, COMDAT

; 331  :         : pair(_Val1, _Val2, index_sequence_for<_Types1...>{}, index_sequence_for<_Types2...>{}) {}

$LN20:
  00000	4c 89 4c 24 20	 mov	 QWORD PTR [rsp+32], r9
  00005	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  0000a	88 54 24 10	 mov	 BYTE PTR [rsp+16], dl
  0000e	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00013	57		 push	 rdi
  00014	48 83 ec 50	 sub	 rsp, 80			; 00000050H
  00018	48 8d 44 24 20	 lea	 rax, QWORD PTR $T1[rsp]
  0001d	48 8b f8	 mov	 rdi, rax
  00020	33 c0		 xor	 eax, eax
  00022	b9 01 00 00 00	 mov	 ecx, 1
  00027	f3 aa		 rep stosb
  00029	48 8d 44 24 21	 lea	 rax, QWORD PTR $T2[rsp]
  0002e	48 8b f8	 mov	 rdi, rax
  00031	33 c0		 xor	 eax, eax
  00033	b9 01 00 00 00	 mov	 ecx, 1
  00038	f3 aa		 rep stosb
  0003a	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  0003f	88 44 24 28	 mov	 BYTE PTR __formal$[rsp], al
  00043	0f b6 44 24 21	 movzx	 eax, BYTE PTR $T2[rsp]
  00048	88 44 24 30	 mov	 BYTE PTR __formal$[rsp], al
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  0004c	48 8b 44 24 70	 mov	 rax, QWORD PTR _Val1$[rsp]
  00051	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\tuple

; 929  :     return static_cast<_Ty&&>(static_cast<_Ttype&>(_Tuple)._Myfirst._Val);

  00056	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
  0005b	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0005e	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 478  :         : first(_STD _Tuple_get<_Indices1>(_STD move(_Val1))...),

  00063	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00068	48 8b 4c 24 40	 mov	 rcx, QWORD PTR $T4[rsp]
  0006d	8b 09		 mov	 ecx, DWORD PTR [rcx]
  0006f	89 08		 mov	 DWORD PTR [rax], ecx

; 479  :           second(_STD _Tuple_get<_Indices2>(_STD move(_Val2))...) {}

  00071	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00076	48 83 c0 08	 add	 rax, 8
  0007a	48 8b f8	 mov	 rdi, rax
  0007d	33 c0		 xor	 eax, eax
  0007f	b9 20 00 00 00	 mov	 ecx, 32			; 00000020H
  00084	f3 aa		 rep stosb
  00086	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  0008b	48 83 c0 08	 add	 rax, 8
  0008f	48 89 44 24 48	 mov	 QWORD PTR this$[rsp], rax
  00094	48 8b 44 24 48	 mov	 rax, QWORD PTR this$[rsp]
  00099	48 83 c0 08	 add	 rax, 8
  0009d	48 8b c8	 mov	 rcx, rax
  000a0	e8 00 00 00 00	 call	 ??0ExecutionZoneId@mu2@@QEAA@XZ ; mu2::ExecutionZoneId::ExecutionZoneId
  000a5	90		 npad	 1

; 331  :         : pair(_Val1, _Val2, index_sequence_for<_Types1...>{}, index_sequence_for<_Types2...>{}) {}

  000a6	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  000ab	48 83 c4 50	 add	 rsp, 80			; 00000050H
  000af	5f		 pop	 rdi
  000b0	c3		 ret	 0
??$?0AEBI$$Z$$V@?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@QEAA@Upiecewise_construct_t@1@V?$tuple@AEBI@1@V?$tuple@$$V@1@@Z ENDP ; std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo><unsigned int const &>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??$_Buyheadnode@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@@Z
_TEXT	SEGMENT
_Pnode$ = 32
_Obj$ = 40
$T1 = 48
$T2 = 56
$T3 = 64
$T4 = 72
_Obj$ = 80
$T5 = 88
$T6 = 96
$T7 = 104
$T8 = 112
_Obj$ = 120
$T9 = 128
$T10 = 136
$T11 = 144
$T12 = 152
_Al$ = 176
??$_Buyheadnode@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@@Z PROC ; std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *>::_Buyheadnode<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >, COMDAT

; 343  :     static _Nodeptr _Buyheadnode(_Alloc& _Al) {

$LN75:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec a8 00
	00 00		 sub	 rsp, 168		; 000000a8H

; 344  :         static_assert(is_same_v<typename _Alloc::value_type, _Tree_node>, "Bad _Buyheadnode call");
; 345  :         const auto _Pnode = _Al.allocate(1);

  0000c	ba 01 00 00 00	 mov	 edx, 1
  00011	48 8b 8c 24 b0
	00 00 00	 mov	 rcx, QWORD PTR _Al$[rsp]
  00019	e8 00 00 00 00	 call	 ?allocate@?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@_K@Z ; std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> >::allocate
  0001e	48 89 44 24 20	 mov	 QWORD PTR _Pnode$[rsp], rax

; 346  :         _Construct_in_place(_Pnode->_Left, _Pnode);

  00023	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00028	48 89 44 24 28	 mov	 QWORD PTR _Obj$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0002d	48 8b 44 24 28	 mov	 rax, QWORD PTR _Obj$[rsp]
  00032	48 89 44 24 30	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00037	48 8b 44 24 30	 mov	 rax, QWORD PTR $T1[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  0003c	48 89 44 24 38	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00041	48 8b 44 24 38	 mov	 rax, QWORD PTR $T2[rsp]
  00046	48 89 44 24 40	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  0004b	48 8d 44 24 20	 lea	 rax, QWORD PTR _Pnode$[rsp]
  00050	48 89 44 24 48	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00055	48 8b 44 24 40	 mov	 rax, QWORD PTR $T3[rsp]
  0005a	48 8b 4c 24 48	 mov	 rcx, QWORD PTR $T4[rsp]
  0005f	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00062	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 347  :         _Construct_in_place(_Pnode->_Parent, _Pnode);

  00065	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0006a	48 83 c0 08	 add	 rax, 8
  0006e	48 89 44 24 50	 mov	 QWORD PTR _Obj$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00073	48 8b 44 24 50	 mov	 rax, QWORD PTR _Obj$[rsp]
  00078	48 89 44 24 58	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0007d	48 8b 44 24 58	 mov	 rax, QWORD PTR $T5[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  00082	48 89 44 24 60	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00087	48 8b 44 24 60	 mov	 rax, QWORD PTR $T6[rsp]
  0008c	48 89 44 24 68	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00091	48 8d 44 24 20	 lea	 rax, QWORD PTR _Pnode$[rsp]
  00096	48 89 44 24 70	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0009b	48 8b 44 24 68	 mov	 rax, QWORD PTR $T7[rsp]
  000a0	48 8b 4c 24 70	 mov	 rcx, QWORD PTR $T8[rsp]
  000a5	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  000a8	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 348  :         _Construct_in_place(_Pnode->_Right, _Pnode);

  000ab	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  000b0	48 83 c0 10	 add	 rax, 16
  000b4	48 89 44 24 78	 mov	 QWORD PTR _Obj$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  000b9	48 8b 44 24 78	 mov	 rax, QWORD PTR _Obj$[rsp]
  000be	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  000c6	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR $T9[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  000ce	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  000d6	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  000de	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  000e6	48 8d 44 24 20	 lea	 rax, QWORD PTR _Pnode$[rsp]
  000eb	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  000f3	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  000fb	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR $T12[rsp]
  00103	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00106	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 349  :         _Pnode->_Color = _Black;

  00109	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0010e	c6 40 18 01	 mov	 BYTE PTR [rax+24], 1

; 350  :         _Pnode->_Isnil = true;

  00112	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00117	c6 40 19 01	 mov	 BYTE PTR [rax+25], 1

; 351  :         return _Pnode;

  0011b	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]

; 352  :     }

  00120	48 81 c4 a8 00
	00 00		 add	 rsp, 168		; 000000a8H
  00127	c3		 ret	 0
??$_Buyheadnode@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@@Z ENDP ; std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *>::_Buyheadnode<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??$_Freenode@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@SAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU01@@Z
_TEXT	SEGMENT
_Bytes$ = 32
_Ptr$ = 40
_Ptr$ = 48
$T1 = 56
_Al$ = 80
_Ptr$ = 88
??$_Freenode@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@SAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU01@@Z PROC ; std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *>::_Freenode<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >, COMDAT

; 379  :     static void _Freenode(_Alloc& _Al, _Nodeptr _Ptr) noexcept {

$LN72:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 381  :         allocator_traits<_Alloc>::destroy(_Al, _STD addressof(_Ptr->_Myval));

  0000e	48 8b 44 24 58	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00013	48 83 c0 20	 add	 rax, 32			; 00000020H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00017	48 89 44 24 38	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 381  :         allocator_traits<_Alloc>::destroy(_Al, _STD addressof(_Ptr->_Myval));

  0001c	48 8b 44 24 38	 mov	 rax, QWORD PTR $T1[rsp]
  00021	48 89 44 24 28	 mov	 QWORD PTR _Ptr$[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h

; 172  : 	virtual~ISerializer() {}

  00026	48 8b 44 24 28	 mov	 rax, QWORD PTR _Ptr$[rsp]
  0002b	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  00032	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx
  00036	33 c0		 xor	 eax, eax
  00038	83 e0 01	 and	 eax, 1
  0003b	85 c0		 test	 eax, eax
  0003d	74 10		 je	 SHORT $LN12@Freenode
  0003f	ba 28 00 00 00	 mov	 edx, 40			; 00000028H
  00044	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  00049	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  0004e	90		 npad	 1
$LN12@Freenode:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 723  :             _STD _Deallocate<_New_alignof<value_type>>(_Ptr, sizeof(value_type) * _Count);

  0004f	b8 01 00 00 00	 mov	 eax, 1
  00054	48 6b c0 48	 imul	 rax, rax, 72		; 00000048H
  00058	48 89 44 24 20	 mov	 QWORD PTR _Bytes$[rsp], rax
  0005d	48 8b 44 24 58	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00062	48 89 44 24 30	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  00067	48 81 7c 24 20
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  00070	72 10		 jb	 SHORT $LN63@Freenode

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  00072	48 8d 54 24 20	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  00077	48 8d 4c 24 30	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  0007c	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  00081	90		 npad	 1
$LN63@Freenode:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  00082	48 8b 54 24 20	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  00087	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  0008c	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00091	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 383  :     }

  00092	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00096	c3		 ret	 0
??$_Freenode@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@SAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU01@@Z ENDP ; std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *>::_Freenode<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??__F?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ
text$yd	SEGMENT
??__F?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ PROC ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::ExecutionManager>::inst'', COMDAT
  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A ; mu2::ISingleton<mu2::ExecutionManager>::inst
  0000b	e8 00 00 00 00	 call	 ??1ExecutionManager@mu2@@UEAA@XZ ; mu2::ExecutionManager::~ExecutionManager
  00010	90		 npad	 1
  00011	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00015	c3		 ret	 0
??__F?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ ENDP ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::ExecutionManager>::inst''
text$yd	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??__E?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ
text$di	SEGMENT
??__E?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ PROC ; `dynamic initializer for 'mu2::ISingleton<mu2::ExecutionManager>::inst'', COMDAT

; 90   : template<typename T> T ISingleton<T>::inst;

  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A ; mu2::ISingleton<mu2::ExecutionManager>::inst
  0000b	e8 00 00 00 00	 call	 ??0ExecutionManager@mu2@@AEAA@XZ ; mu2::ExecutionManager::ExecutionManager
  00010	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??__F?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ ; `dynamic atexit destructor for 'mu2::ISingleton<mu2::ExecutionManager>::inst''
  00017	e8 00 00 00 00	 call	 atexit
  0001c	90		 npad	 1
  0001d	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00021	c3		 ret	 0
??__E?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A@@YAXXZ ENDP ; `dynamic initializer for 'mu2::ISingleton<mu2::ExecutionManager>::inst''
text$di	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\tuple
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\tuple
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??$?0AEBUpiecewise_construct_t@std@@V?$tuple@AEBI@1@V?$tuple@$$V@1@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBUpiecewise_construct_t@1@$$QEAV?$tuple@AEBI@1@$$QEAV?$tuple@$$V@1@@Z
_TEXT	SEGMENT
$T1 = 32
$T2 = 40
_Val$ = 48
$T3 = 56
$T4 = 64
$T5 = 72
$T6 = 80
$T7 = 88
$T8 = 96
$T9 = 104
$T10 = 112
$T11 = 120
$T12 = 128
$T13 = 136
_Obj$ = 144
$T14 = 152
$T15 = 160
$T16 = 168
$T17 = 176
_Obj$ = 184
$T18 = 192
$T19 = 200
$T20 = 208
$T21 = 216
_Obj$ = 224
$T22 = 232
$T23 = 240
$T24 = 248
$T25 = 256
__formal$ = 264
$T26 = 272
$T27 = 280
this$ = 304
_Al_$ = 312
_Myhead$ = 320
<_Vals_0>$ = 328
<_Vals_1>$ = 336
<_Vals_2>$ = 344
??$?0AEBUpiecewise_construct_t@std@@V?$tuple@AEBI@1@V?$tuple@$$V@1@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBUpiecewise_construct_t@1@$$QEAV?$tuple@AEBI@1@$$QEAV?$tuple@$$V@1@@Z PROC ; std::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > ><std::piecewise_construct_t const &,std::tuple<unsigned int const &>,std::tuple<> >, COMDAT

; 829  :         : _Tree_temp_node_alloc<_Alnode>(_Al_) {

$LN159:
  00000	4c 89 4c 24 20	 mov	 QWORD PTR [rsp+32], r9
  00005	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	48 81 ec 28 01
	00 00		 sub	 rsp, 296		; 00000128H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1160 :     _CONSTEXPR20 explicit _Alloc_construct_ptr(_Alloc& _Al_) : _Al(_Al_), _Ptr(nullptr) {}

  0001b	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 8b 8c 24 38
	01 00 00	 mov	 rcx, QWORD PTR _Al_$[rsp]
  0002b	48 89 08	 mov	 QWORD PTR [rax], rcx
  0002e	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00036	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0

; 1167 :         _Ptr = nullptr; // if allocate throws, prevents double-free

  0003e	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00046	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0

; 1168 :         _Ptr = _Al.allocate(1);

  0004e	ba 01 00 00 00	 mov	 edx, 1
  00053	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0005b	48 8b 08	 mov	 rcx, QWORD PTR [rax]
  0005e	e8 00 00 00 00	 call	 ?allocate@?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@_K@Z ; std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> >::allocate
  00063	48 8b 8c 24 30
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0006b	48 89 41 08	 mov	 QWORD PTR [rcx+8], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  0006f	48 8b 84 24 58
	01 00 00	 mov	 rax, QWORD PTR <_Vals_2>$[rsp]
  00077	48 89 44 24 48	 mov	 QWORD PTR $T5[rsp], rax
  0007c	48 8b 84 24 50
	01 00 00	 mov	 rax, QWORD PTR <_Vals_1>$[rsp]
  00084	48 89 44 24 58	 mov	 QWORD PTR $T7[rsp], rax
  00089	48 8b 84 24 48
	01 00 00	 mov	 rax, QWORD PTR <_Vals_0>$[rsp]
  00091	48 89 44 24 68	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 830  :         _Alnode_traits::construct(this->_Al, _STD addressof(this->_Ptr->_Myval), _STD forward<_Valtys>(_Vals)...);

  00096	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0009e	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  000a2	48 83 c0 20	 add	 rax, 32			; 00000020H
  000a6	48 89 44 24 30	 mov	 QWORD PTR _Val$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  000ab	48 8b 44 24 30	 mov	 rax, QWORD PTR _Val$[rsp]
  000b0	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 830  :         _Alnode_traits::construct(this->_Al, _STD addressof(this->_Ptr->_Myval), _STD forward<_Valtys>(_Vals)...);

  000b5	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000bd	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000c0	48 89 84 24 08
	01 00 00	 mov	 QWORD PTR __formal$[rsp], rax
  000c8	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  000cd	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 732  :         ::new (const_cast<void*>(static_cast<const volatile void*>(_Ptr))) _Objty(_STD forward<_Types>(_Args)...);

  000d2	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  000d7	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax
  000df	48 8d 44 24 20	 lea	 rax, QWORD PTR $T1[rsp]
  000e4	48 89 44 24 50	 mov	 QWORD PTR $T6[rsp], rax
  000e9	48 8d 84 24 10
	01 00 00	 lea	 rax, QWORD PTR $T26[rsp]
  000f1	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 830  :         _Alnode_traits::construct(this->_Al, _STD addressof(this->_Ptr->_Myval), _STD forward<_Valtys>(_Vals)...);

  000f6	48 8b 44 24 48	 mov	 rax, QWORD PTR $T5[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  000fb	48 89 84 24 18
	01 00 00	 mov	 QWORD PTR $T27[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\tuple

; 214  :     constexpr tuple(const tuple&) noexcept /* strengthened */ {} // TRANSITION, ABI: should be defaulted

  00103	48 8b 44 24 50	 mov	 rax, QWORD PTR $T6[rsp]
  00108	48 89 44 24 70	 mov	 QWORD PTR $T10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 830  :         _Alnode_traits::construct(this->_Al, _STD addressof(this->_Ptr->_Myval), _STD forward<_Valtys>(_Vals)...);

  0010d	48 8b 44 24 58	 mov	 rax, QWORD PTR $T7[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00112	48 89 44 24 60	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\tuple

; 352  :     tuple(tuple&&)      = default;

  00117	48 8b 44 24 60	 mov	 rax, QWORD PTR $T8[rsp]
  0011c	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0011f	48 8b 4c 24 28	 mov	 rcx, QWORD PTR $T2[rsp]
  00124	48 89 01	 mov	 QWORD PTR [rcx], rax
  00127	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
  0012c	48 89 44 24 78	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 830  :         _Alnode_traits::construct(this->_Al, _STD addressof(this->_Ptr->_Myval), _STD forward<_Valtys>(_Vals)...);

  00131	48 8b 44 24 68	 mov	 rax, QWORD PTR $T9[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00136	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 732  :         ::new (const_cast<void*>(static_cast<const volatile void*>(_Ptr))) _Objty(_STD forward<_Types>(_Args)...);

  0013e	48 8b 44 24 70	 mov	 rax, QWORD PTR $T10[rsp]
  00143	48 8b 4c 24 78	 mov	 rcx, QWORD PTR $T11[rsp]
  00148	4c 8b c8	 mov	 r9, rax
  0014b	4c 8b c1	 mov	 r8, rcx
  0014e	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
  00156	0f b6 10	 movzx	 edx, BYTE PTR [rax]
  00159	48 8b 8c 24 88
	00 00 00	 mov	 rcx, QWORD PTR $T13[rsp]
  00161	e8 00 00 00 00	 call	 ??$?0AEBI$$Z$$V@?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@QEAA@Upiecewise_construct_t@1@V?$tuple@AEBI@1@V?$tuple@$$V@1@@Z ; std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo><unsigned int const &>
  00166	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 831  :         _Construct_in_place(this->_Ptr->_Left, _Myhead);

  00167	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0016f	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00173	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR _Obj$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0017b	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR _Obj$[rsp]
  00183	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR $T14[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0018b	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR $T14[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  00193	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T15[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0019b	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR $T15[rsp]
  001a3	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T16[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  001ab	48 8d 84 24 40
	01 00 00	 lea	 rax, QWORD PTR _Myhead$[rsp]
  001b3	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T17[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  001bb	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T16[rsp]
  001c3	48 8b 8c 24 b0
	00 00 00	 mov	 rcx, QWORD PTR $T17[rsp]
  001cb	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  001ce	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 832  :         _Construct_in_place(this->_Ptr->_Parent, _Myhead);

  001d1	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  001d9	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  001dd	48 83 c0 08	 add	 rax, 8
  001e1	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR _Obj$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  001e9	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR _Obj$[rsp]
  001f1	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T18[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  001f9	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR $T18[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  00201	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T19[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00209	48 8b 84 24 c8
	00 00 00	 mov	 rax, QWORD PTR $T19[rsp]
  00211	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR $T20[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00219	48 8d 84 24 40
	01 00 00	 lea	 rax, QWORD PTR _Myhead$[rsp]
  00221	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR $T21[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00229	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR $T20[rsp]
  00231	48 8b 8c 24 d8
	00 00 00	 mov	 rcx, QWORD PTR $T21[rsp]
  00239	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0023c	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 833  :         _Construct_in_place(this->_Ptr->_Right, _Myhead);

  0023f	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00247	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0024b	48 83 c0 10	 add	 rax, 16
  0024f	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR _Obj$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00257	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR _Obj$[rsp]
  0025f	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR $T22[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00267	48 8b 84 24 e8
	00 00 00	 mov	 rax, QWORD PTR $T22[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  0026f	48 89 84 24 f0
	00 00 00	 mov	 QWORD PTR $T23[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00277	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR $T23[rsp]
  0027f	48 89 84 24 f8
	00 00 00	 mov	 QWORD PTR $T24[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00287	48 8d 84 24 40
	01 00 00	 lea	 rax, QWORD PTR _Myhead$[rsp]
  0028f	48 89 84 24 00
	01 00 00	 mov	 QWORD PTR $T25[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00297	48 8b 84 24 f8
	00 00 00	 mov	 rax, QWORD PTR $T24[rsp]
  0029f	48 8b 8c 24 00
	01 00 00	 mov	 rcx, QWORD PTR $T25[rsp]
  002a7	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  002aa	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 834  :         this->_Ptr->_Color = _Red;

  002ad	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  002b5	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  002b9	c6 40 18 00	 mov	 BYTE PTR [rax+24], 0

; 835  :         this->_Ptr->_Isnil = false;

  002bd	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  002c5	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  002c9	c6 40 19 00	 mov	 BYTE PTR [rax+25], 0

; 836  :     }

  002cd	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  002d5	48 81 c4 28 01
	00 00		 add	 rsp, 296		; 00000128H
  002dc	c3		 ret	 0
??$?0AEBUpiecewise_construct_t@std@@V?$tuple@AEBI@1@V?$tuple@$$V@1@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBUpiecewise_construct_t@1@$$QEAV?$tuple@AEBI@1@$$QEAV?$tuple@$$V@1@@Z ENDP ; std::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > ><std::piecewise_construct_t const &,std::tuple<unsigned int const &>,std::tuple<> >
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 40
_Val$ = 48
$T3 = 56
$T4 = 64
$T5 = 72
$T6 = 80
$T7 = 88
$T8 = 96
$T9 = 104
$T10 = 112
$T11 = 120
$T12 = 128
$T13 = 136
_Obj$ = 144
$T14 = 152
$T15 = 160
$T16 = 168
$T17 = 176
_Obj$ = 184
$T18 = 192
$T19 = 200
$T20 = 208
$T21 = 216
_Obj$ = 224
$T22 = 232
$T23 = 240
$T24 = 248
$T25 = 256
__formal$ = 264
$T26 = 272
$T27 = 280
this$ = 304
_Al_$ = 312
_Myhead$ = 320
<_Vals_0>$ = 328
<_Vals_1>$ = 336
<_Vals_2>$ = 344
?dtor$1@?0???$?0AEBUpiecewise_construct_t@std@@V?$tuple@AEBI@1@V?$tuple@$$V@1@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBUpiecewise_construct_t@1@$$QEAV?$tuple@AEBI@1@$$QEAV?$tuple@$$V@1@@Z@4HA PROC ; `std::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > ><std::piecewise_construct_t const &,std::tuple<unsigned int const &>,std::tuple<> >'::`1'::dtor$1
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 8d 30 01
	00 00		 mov	 rcx, QWORD PTR this$[rbp]
  00010	e8 00 00 00 00	 call	 ??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ ; std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$1@?0???$?0AEBUpiecewise_construct_t@std@@V?$tuple@AEBI@1@V?$tuple@$$V@1@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBUpiecewise_construct_t@1@$$QEAV?$tuple@AEBI@1@$$QEAV?$tuple@$$V@1@@Z@4HA ENDP ; `std::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > ><std::piecewise_construct_t const &,std::tuple<unsigned int const &>,std::tuple<> >'::`1'::dtor$1
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
$T1 = 32
$T2 = 40
_Val$ = 48
$T3 = 56
$T4 = 64
$T5 = 72
$T6 = 80
$T7 = 88
$T8 = 96
$T9 = 104
$T10 = 112
$T11 = 120
$T12 = 128
$T13 = 136
_Obj$ = 144
$T14 = 152
$T15 = 160
$T16 = 168
$T17 = 176
_Obj$ = 184
$T18 = 192
$T19 = 200
$T20 = 208
$T21 = 216
_Obj$ = 224
$T22 = 232
$T23 = 240
$T24 = 248
$T25 = 256
__formal$ = 264
$T26 = 272
$T27 = 280
this$ = 304
_Al_$ = 312
_Myhead$ = 320
<_Vals_0>$ = 328
<_Vals_1>$ = 336
<_Vals_2>$ = 344
?dtor$0@?0???$?0AEBUpiecewise_construct_t@std@@V?$tuple@AEBI@1@V?$tuple@$$V@1@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBUpiecewise_construct_t@1@$$QEAV?$tuple@AEBI@1@$$QEAV?$tuple@$$V@1@@Z@4HA PROC ; `std::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > ><std::piecewise_construct_t const &,std::tuple<unsigned int const &>,std::tuple<> >'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 8d 30 01
	00 00		 mov	 rcx, QWORD PTR this$[rbp]
  00010	e8 00 00 00 00	 call	 ??1?$_Tree_temp_node_alloc@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$0@?0???$?0AEBUpiecewise_construct_t@std@@V?$tuple@AEBI@1@V?$tuple@$$V@1@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBUpiecewise_construct_t@1@$$QEAV?$tuple@AEBI@1@$$QEAV?$tuple@$$V@1@@Z@4HA ENDP ; `std::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > ><std::piecewise_construct_t const &,std::tuple<unsigned int const &>,std::tuple<> >'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??1?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ
_TEXT	SEGMENT
_Ptr$ = 32
_Val$ = 40
$T1 = 48
_Obj$ = 56
_Obj$ = 64
_Obj$ = 72
__formal$ = 80
this$ = 112
??1?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ PROC ; std::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >::~_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >, COMDAT

; 841  :     ~_Tree_temp_node() {

$LN79:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 68	 sub	 rsp, 104		; 00000068H

; 842  :         if (this->_Ptr) {

  00009	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00013	0f 84 8e 00 00
	00		 je	 $LN2@Tree_temp_

; 843  :             _Destroy_in_place(this->_Ptr->_Left);

  00019	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0001e	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00022	48 89 44 24 38	 mov	 QWORD PTR _Obj$[rsp], rax

; 844  :             _Destroy_in_place(this->_Ptr->_Parent);

  00027	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0002c	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00030	48 83 c0 08	 add	 rax, 8
  00034	48 89 44 24 40	 mov	 QWORD PTR _Obj$[rsp], rax

; 845  :             _Destroy_in_place(this->_Ptr->_Right);

  00039	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0003e	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00042	48 83 c0 10	 add	 rax, 16
  00046	48 89 44 24 48	 mov	 QWORD PTR _Obj$[rsp], rax

; 846  :             _Alnode_traits::destroy(this->_Al, _STD addressof(this->_Ptr->_Myval));

  0004b	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  00050	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00054	48 83 c0 20	 add	 rax, 32			; 00000020H
  00058	48 89 44 24 28	 mov	 QWORD PTR _Val$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0005d	48 8b 44 24 28	 mov	 rax, QWORD PTR _Val$[rsp]
  00062	48 89 44 24 30	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 846  :             _Alnode_traits::destroy(this->_Al, _STD addressof(this->_Ptr->_Myval));

  00067	48 8b 44 24 30	 mov	 rax, QWORD PTR $T1[rsp]
  0006c	48 89 44 24 20	 mov	 QWORD PTR _Ptr$[rsp], rax
  00071	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  00076	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00079	48 89 44 24 50	 mov	 QWORD PTR __formal$[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h

; 172  : 	virtual~ISerializer() {}

  0007e	48 8b 44 24 20	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00083	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  0008a	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx
  0008e	33 c0		 xor	 eax, eax
  00090	83 e0 01	 and	 eax, 1
  00093	85 c0		 test	 eax, eax
  00095	74 10		 je	 SHORT $LN2@Tree_temp_
  00097	ba 28 00 00 00	 mov	 edx, 40			; 00000028H
  0009c	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  000a1	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  000a6	90		 npad	 1
$LN2@Tree_temp_:
  000a7	48 8b 4c 24 70	 mov	 rcx, QWORD PTR this$[rsp]
  000ac	e8 00 00 00 00	 call	 ??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ ; std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >
  000b1	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 848  :     }

  000b2	48 83 c4 68	 add	 rsp, 104		; 00000068H
  000b6	c3		 ret	 0
??1?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ ENDP ; std::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >::~_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??1?$_Tree_temp_node_alloc@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1?$_Tree_temp_node_alloc@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ PROC ; std::_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >::~_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >, COMDAT
$LN25:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00009	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0000e	e8 00 00 00 00	 call	 ??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ ; std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >
  00013	90		 npad	 1
  00014	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00018	c3		 ret	 0
??1?$_Tree_temp_node_alloc@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ ENDP ; std::_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >::~_Tree_temp_node_alloc<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ
_TEXT	SEGMENT
_Bytes$ = 32
_Ptr$ = 40
_Ptr$ = 48
this$ = 56
this$ = 80
??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ PROC ; std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >, COMDAT

; 1171 :     _CONSTEXPR20 ~_Alloc_construct_ptr() { // if this instance is engaged, deallocate storage

$LN20:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 1172 :         if (_Ptr) {

  00009	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00013	74 5e		 je	 SHORT $LN2@Alloc_cons

; 1173 :             _Al.deallocate(_Ptr, 1);

  00015	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0001e	48 89 44 24 30	 mov	 QWORD PTR _Ptr$[rsp], rax
  00023	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00028	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0002b	48 89 44 24 38	 mov	 QWORD PTR this$[rsp], rax

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  00030	b8 01 00 00 00	 mov	 eax, 1
  00035	48 6b c0 48	 imul	 rax, rax, 72		; 00000048H
  00039	48 89 44 24 20	 mov	 QWORD PTR _Bytes$[rsp], rax
  0003e	48 8b 44 24 30	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00043	48 89 44 24 28	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  00048	48 81 7c 24 20
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  00051	72 10		 jb	 SHORT $LN11@Alloc_cons

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  00053	48 8d 54 24 20	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  00058	48 8d 4c 24 28	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  0005d	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  00062	90		 npad	 1
$LN11@Alloc_cons:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  00063	48 8b 54 24 20	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  00068	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  0006d	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00072	90		 npad	 1
$LN2@Alloc_cons:

; 1174 :         }
; 1175 :     }

  00073	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00077	c3		 ret	 0
??1?$_Alloc_construct_ptr@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ ENDP ; std::_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@@Z
_TEXT	SEGMENT
_New_val$ = 32
_Old_val$1 = 40
$T2 = 48
this$ = 80
_Al$ = 88
_Rootnode$ = 96
??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@@Z PROC ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >, COMDAT

; 767  :     void _Erase_tree(_Alnode& _Al, _Nodeptr _Rootnode) noexcept {

$LN84:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 48	 sub	 rsp, 72			; 00000048H
$LN2@Erase_tree:

; 768  :         while (!_Rootnode->_Isnil) { // free subtrees, then node

  00013	48 8b 44 24 60	 mov	 rax, QWORD PTR _Rootnode$[rsp]
  00018	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  0001c	85 c0		 test	 eax, eax
  0001e	75 58		 jne	 SHORT $LN3@Erase_tree

; 769  :             _Erase_tree(_Al, _Rootnode->_Right);

  00020	48 8b 44 24 60	 mov	 rax, QWORD PTR _Rootnode$[rsp]
  00025	4c 8b 40 10	 mov	 r8, QWORD PTR [rax+16]
  00029	48 8b 54 24 58	 mov	 rdx, QWORD PTR _Al$[rsp]
  0002e	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  00033	e8 00 00 00 00	 call	 ??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >

; 770  :             _Alnode::value_type::_Freenode(_Al, _STD exchange(_Rootnode, _Rootnode->_Left));

  00038	48 8b 44 24 60	 mov	 rax, QWORD PTR _Rootnode$[rsp]
  0003d	48 89 44 24 20	 mov	 QWORD PTR _New_val$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 773  :     _Ty _Old_val = static_cast<_Ty&&>(_Val);

  00042	48 8b 44 24 60	 mov	 rax, QWORD PTR _Rootnode$[rsp]
  00047	48 89 44 24 28	 mov	 QWORD PTR _Old_val$1[rsp], rax

; 774  :     _Val         = static_cast<_Other&&>(_New_val);

  0004c	48 8b 44 24 20	 mov	 rax, QWORD PTR _New_val$[rsp]
  00051	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00054	48 89 44 24 60	 mov	 QWORD PTR _Rootnode$[rsp], rax

; 775  :     return _Old_val;

  00059	48 8b 44 24 28	 mov	 rax, QWORD PTR _Old_val$1[rsp]
  0005e	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 770  :             _Alnode::value_type::_Freenode(_Al, _STD exchange(_Rootnode, _Rootnode->_Left));

  00063	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
  00068	48 8b d0	 mov	 rdx, rax
  0006b	48 8b 4c 24 58	 mov	 rcx, QWORD PTR _Al$[rsp]
  00070	e8 00 00 00 00	 call	 ??$_Freenode@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@SAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU01@@Z ; std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *>::_Freenode<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >
  00075	90		 npad	 1

; 771  :         }

  00076	eb 9b		 jmp	 SHORT $LN2@Erase_tree
$LN3@Erase_tree:

; 772  :     }

  00078	48 83 c4 48	 add	 rsp, 72			; 00000048H
  0007c	c3		 ret	 0
??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@@Z ENDP ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??$_Lower_bound_duplicate@I@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@IEBA_NQEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBI@Z
_TEXT	SEGMENT
$T1 = 0
tv81 = 4
tv78 = 8
$T2 = 16
$T3 = 24
$T4 = 32
this$ = 64
_Bound$ = 72
_Keyval$ = 80
??$_Lower_bound_duplicate@I@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@IEBA_NQEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBI@Z PROC ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::_Lower_bound_duplicate<unsigned int>, COMDAT

; 1623 :     bool _Lower_bound_duplicate(const _Nodeptr _Bound, const _Keyty& _Keyval) const {

$LN24:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 1624 :         return !_Bound->_Isnil && !_DEBUG_LT_PRED(_Getcomp(), _Keyval, _Traits::_Kfn(_Bound->_Myval));

  00013	48 8b 44 24 48	 mov	 rax, QWORD PTR _Bound$[rsp]
  00018	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  0001c	85 c0		 test	 eax, eax
  0001e	75 61		 jne	 SHORT $LN3@Lower_boun
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map

; 67   :         return _Val.first;

  00020	48 8b 44 24 48	 mov	 rax, QWORD PTR _Bound$[rsp]
  00025	48 83 c0 20	 add	 rax, 32			; 00000020H
  00029	48 89 44 24 18	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1971 :         return _Mypair._Get_first();

  0002e	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1543 :         return *this;

  00033	48 89 44 24 10	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1971 :         return _Mypair._Get_first();

  00038	48 8b 44 24 10	 mov	 rax, QWORD PTR $T2[rsp]
  0003d	48 89 44 24 20	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 2380 :         return _Left < _Right;

  00042	48 8b 44 24 50	 mov	 rax, QWORD PTR _Keyval$[rsp]
  00047	48 8b 4c 24 18	 mov	 rcx, QWORD PTR $T3[rsp]
  0004c	8b 09		 mov	 ecx, DWORD PTR [rcx]
  0004e	39 08		 cmp	 DWORD PTR [rax], ecx
  00050	73 0a		 jae	 SHORT $LN19@Lower_boun
  00052	c7 44 24 04 01
	00 00 00	 mov	 DWORD PTR tv81[rsp], 1
  0005a	eb 08		 jmp	 SHORT $LN20@Lower_boun
$LN19@Lower_boun:
  0005c	c7 44 24 04 00
	00 00 00	 mov	 DWORD PTR tv81[rsp], 0
$LN20@Lower_boun:
  00064	0f b6 44 24 04	 movzx	 eax, BYTE PTR tv81[rsp]
  00069	88 04 24	 mov	 BYTE PTR $T1[rsp], al
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1624 :         return !_Bound->_Isnil && !_DEBUG_LT_PRED(_Getcomp(), _Keyval, _Traits::_Kfn(_Bound->_Myval));

  0006c	0f b6 04 24	 movzx	 eax, BYTE PTR $T1[rsp]
  00070	0f b6 c0	 movzx	 eax, al
  00073	85 c0		 test	 eax, eax
  00075	75 0a		 jne	 SHORT $LN3@Lower_boun
  00077	c7 44 24 08 01
	00 00 00	 mov	 DWORD PTR tv78[rsp], 1
  0007f	eb 08		 jmp	 SHORT $LN4@Lower_boun
$LN3@Lower_boun:
  00081	c7 44 24 08 00
	00 00 00	 mov	 DWORD PTR tv78[rsp], 0
$LN4@Lower_boun:
  00089	0f b6 44 24 08	 movzx	 eax, BYTE PTR tv78[rsp]

; 1625 :     }

  0008e	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00092	c3		 ret	 0
??$_Lower_bound_duplicate@I@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@IEBA_NQEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBI@Z ENDP ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::_Lower_bound_duplicate<unsigned int>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??$_Find_lower_bound@I@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@IEBA?AU?$_Tree_find_result@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@AEBI@Z
_TEXT	SEGMENT
$T1 = 0
_Trynode$ = 8
tv89 = 16
_Scary$ = 24
_Result$ = 32
$T2 = 56
$T3 = 64
$T4 = 72
$T5 = 80
$T6 = 88
this$ = 128
__$ReturnUdt$ = 136
_Keyval$ = 144
??$_Find_lower_bound@I@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@IEBA?AU?$_Tree_find_result@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@AEBI@Z PROC ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::_Find_lower_bound<unsigned int>, COMDAT

; 1628 :     _Tree_find_result<_Nodeptr> _Find_lower_bound(const _Keyty& _Keyval) const {

$LN36:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	56		 push	 rsi
  00010	57		 push	 rdi
  00011	48 83 ec 68	 sub	 rsp, 104		; 00000068H

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00015	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0001d	48 89 44 24 38	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00022	48 8b 44 24 38	 mov	 rax, QWORD PTR $T2[rsp]
  00027	48 89 44 24 40	 mov	 QWORD PTR $T3[rsp], rax

; 1629 :         const auto _Scary = _Get_scary();

  0002c	48 8b 44 24 40	 mov	 rax, QWORD PTR $T3[rsp]
  00031	48 89 44 24 18	 mov	 QWORD PTR _Scary$[rsp], rax

; 1630 :         _Tree_find_result<_Nodeptr> _Result{{_Scary->_Myhead->_Parent, _Tree_child::_Right}, _Scary->_Myhead};

  00036	48 8b 44 24 18	 mov	 rax, QWORD PTR _Scary$[rsp]
  0003b	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0003e	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00042	48 89 44 24 20	 mov	 QWORD PTR _Result$[rsp], rax
  00047	c7 44 24 28 00
	00 00 00	 mov	 DWORD PTR _Result$[rsp+8], 0
  0004f	48 8b 44 24 18	 mov	 rax, QWORD PTR _Scary$[rsp]
  00054	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00057	48 89 44 24 30	 mov	 QWORD PTR _Result$[rsp+16], rax

; 1631 :         _Nodeptr _Trynode = _Result._Location._Parent;

  0005c	48 8b 44 24 20	 mov	 rax, QWORD PTR _Result$[rsp]
  00061	48 89 44 24 08	 mov	 QWORD PTR _Trynode$[rsp], rax
$LN2@Find_lower:

; 1632 :         while (!_Trynode->_Isnil) {

  00066	48 8b 44 24 08	 mov	 rax, QWORD PTR _Trynode$[rsp]
  0006b	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  0006f	85 c0		 test	 eax, eax
  00071	0f 85 a3 00 00
	00		 jne	 $LN3@Find_lower

; 1633 :             _Result._Location._Parent = _Trynode;

  00077	48 8b 44 24 08	 mov	 rax, QWORD PTR _Trynode$[rsp]
  0007c	48 89 44 24 20	 mov	 QWORD PTR _Result$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map

; 67   :         return _Val.first;

  00081	48 8b 44 24 08	 mov	 rax, QWORD PTR _Trynode$[rsp]
  00086	48 83 c0 20	 add	 rax, 32			; 00000020H
  0008a	48 89 44 24 50	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1971 :         return _Mypair._Get_first();

  0008f	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1543 :         return *this;

  00097	48 89 44 24 48	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1971 :         return _Mypair._Get_first();

  0009c	48 8b 44 24 48	 mov	 rax, QWORD PTR $T4[rsp]
  000a1	48 89 44 24 58	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 2380 :         return _Left < _Right;

  000a6	48 8b 44 24 50	 mov	 rax, QWORD PTR $T5[rsp]
  000ab	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR _Keyval$[rsp]
  000b3	8b 09		 mov	 ecx, DWORD PTR [rcx]
  000b5	39 08		 cmp	 DWORD PTR [rax], ecx
  000b7	73 0a		 jae	 SHORT $LN31@Find_lower
  000b9	c7 44 24 10 01
	00 00 00	 mov	 DWORD PTR tv89[rsp], 1
  000c1	eb 08		 jmp	 SHORT $LN32@Find_lower
$LN31@Find_lower:
  000c3	c7 44 24 10 00
	00 00 00	 mov	 DWORD PTR tv89[rsp], 0
$LN32@Find_lower:
  000cb	0f b6 44 24 10	 movzx	 eax, BYTE PTR tv89[rsp]
  000d0	88 04 24	 mov	 BYTE PTR $T1[rsp], al
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1634 :             if (_DEBUG_LT_PRED(_Getcomp(), _Traits::_Kfn(_Trynode->_Myval), _Keyval)) {

  000d3	0f b6 04 24	 movzx	 eax, BYTE PTR $T1[rsp]
  000d7	0f b6 c0	 movzx	 eax, al
  000da	85 c0		 test	 eax, eax
  000dc	74 18		 je	 SHORT $LN4@Find_lower

; 1635 :                 _Result._Location._Child = _Tree_child::_Right;

  000de	c7 44 24 28 00
	00 00 00	 mov	 DWORD PTR _Result$[rsp+8], 0

; 1636 :                 _Trynode                 = _Trynode->_Right;

  000e6	48 8b 44 24 08	 mov	 rax, QWORD PTR _Trynode$[rsp]
  000eb	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  000ef	48 89 44 24 08	 mov	 QWORD PTR _Trynode$[rsp], rax

; 1637 :             } else {

  000f4	eb 1f		 jmp	 SHORT $LN5@Find_lower
$LN4@Find_lower:

; 1638 :                 _Result._Location._Child = _Tree_child::_Left;

  000f6	c7 44 24 28 01
	00 00 00	 mov	 DWORD PTR _Result$[rsp+8], 1

; 1639 :                 _Result._Bound           = _Trynode;

  000fe	48 8b 44 24 08	 mov	 rax, QWORD PTR _Trynode$[rsp]
  00103	48 89 44 24 30	 mov	 QWORD PTR _Result$[rsp+16], rax

; 1640 :                 _Trynode                 = _Trynode->_Left;

  00108	48 8b 44 24 08	 mov	 rax, QWORD PTR _Trynode$[rsp]
  0010d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00110	48 89 44 24 08	 mov	 QWORD PTR _Trynode$[rsp], rax
$LN5@Find_lower:

; 1641 :             }
; 1642 :         }

  00115	e9 4c ff ff ff	 jmp	 $LN2@Find_lower
$LN3@Find_lower:

; 1643 : 
; 1644 :         return _Result;

  0011a	48 8d 44 24 20	 lea	 rax, QWORD PTR _Result$[rsp]
  0011f	48 8b bc 24 88
	00 00 00	 mov	 rdi, QWORD PTR __$ReturnUdt$[rsp]
  00127	48 8b f0	 mov	 rsi, rax
  0012a	b9 18 00 00 00	 mov	 ecx, 24
  0012f	f3 a4		 rep movsb
  00131	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]

; 1645 :     }

  00139	48 83 c4 68	 add	 rsp, 104		; 00000068H
  0013d	5f		 pop	 rdi
  0013e	5e		 pop	 rsi
  0013f	c3		 ret	 0
??$_Find_lower_bound@I@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@IEBA?AU?$_Tree_find_result@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@AEBI@Z ENDP ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::_Find_lower_bound<unsigned int>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\tuple
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\tuple
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\tuple
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\tuple
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map
;	COMDAT ??$_Try_emplace@AEBI$$V@?$map@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@@std@@AEAA?AU?$pair@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@_N@1@AEBI@Z
_TEXT	SEGMENT
$T1 = 48
$T2 = 49
$T3 = 50
$T4 = 51
$T5 = 52
tv168 = 56
tv171 = 60
_Bound$ = 64
_Val$ = 72
_Scary$ = 80
$T6 = 88
$T7 = 96
$T8 = 104
$T9 = 112
$T10 = 120
$T11 = 128
$T12 = 136
$T13 = 144
$T14 = 152
$T15 = 160
this$ = 168
$T16 = 176
$T17 = 184
$T18 = 192
$T19 = 200
$T20 = 208
$T21 = 216
$T22 = 224
_Old_val$23 = 232
$T24 = 240
$T25 = 248
_Inserted$ = 256
$T26 = 264
$T27 = 272
$T28 = 280
_Loc$ = 288
$T29 = 312
$T30 = 320
$T31 = 336
this$ = 384
__$ReturnUdt$ = 392
_Keyval$ = 400
??$_Try_emplace@AEBI$$V@?$map@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@@std@@AEAA?AU?$pair@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@_N@1@AEBI@Z PROC ; std::map<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::_Try_emplace<unsigned int const &>, COMDAT

; 196  :     pair<_Nodeptr, bool> _Try_emplace(_Keyty&& _Keyval, _Mappedty&&... _Mapval) {

$LN375:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	56		 push	 rsi
  00010	57		 push	 rdi
  00011	48 81 ec 68 01
	00 00		 sub	 rsp, 360		; 00000168H

; 197  :         const auto _Loc = _Mybase::_Find_lower_bound(_Keyval);

  00018	4c 8b 84 24 90
	01 00 00	 mov	 r8, QWORD PTR _Keyval$[rsp]
  00020	48 8d 94 24 20
	01 00 00	 lea	 rdx, QWORD PTR _Loc$[rsp]
  00028	48 8b 8c 24 80
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00030	e8 00 00 00 00	 call	 ??$_Find_lower_bound@I@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@IEBA?AU?$_Tree_find_result@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@AEBI@Z ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::_Find_lower_bound<unsigned int>
  00035	90		 npad	 1

; 198  :         if (_Mybase::_Lower_bound_duplicate(_Loc._Bound, _Keyval)) {

  00036	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR _Loc$[rsp+16]
  0003e	48 89 44 24 40	 mov	 QWORD PTR _Bound$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1624 :         return !_Bound->_Isnil && !_DEBUG_LT_PRED(_Getcomp(), _Keyval, _Traits::_Kfn(_Bound->_Myval));

  00043	48 8b 44 24 40	 mov	 rax, QWORD PTR _Bound$[rsp]
  00048	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  0004c	85 c0		 test	 eax, eax
  0004e	75 6c		 jne	 SHORT $LN42@Try_emplac
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map

; 67   :         return _Val.first;

  00050	48 8b 44 24 40	 mov	 rax, QWORD PTR _Bound$[rsp]
  00055	48 83 c0 20	 add	 rax, 32			; 00000020H
  00059	48 89 44 24 60	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1971 :         return _Mypair._Get_first();

  0005e	48 8b 84 24 80
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1543 :         return *this;

  00066	48 89 44 24 58	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1971 :         return _Mypair._Get_first();

  0006b	48 8b 44 24 58	 mov	 rax, QWORD PTR $T6[rsp]
  00070	48 89 84 24 38
	01 00 00	 mov	 QWORD PTR $T29[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 2380 :         return _Left < _Right;

  00078	48 8b 84 24 90
	01 00 00	 mov	 rax, QWORD PTR _Keyval$[rsp]
  00080	48 8b 4c 24 60	 mov	 rcx, QWORD PTR $T7[rsp]
  00085	8b 09		 mov	 ecx, DWORD PTR [rcx]
  00087	39 08		 cmp	 DWORD PTR [rax], ecx
  00089	73 0a		 jae	 SHORT $LN58@Try_emplac
  0008b	c7 44 24 38 01
	00 00 00	 mov	 DWORD PTR tv168[rsp], 1
  00093	eb 08		 jmp	 SHORT $LN59@Try_emplac
$LN58@Try_emplac:
  00095	c7 44 24 38 00
	00 00 00	 mov	 DWORD PTR tv168[rsp], 0
$LN59@Try_emplac:
  0009d	0f b6 44 24 38	 movzx	 eax, BYTE PTR tv168[rsp]
  000a2	88 44 24 30	 mov	 BYTE PTR $T1[rsp], al
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1624 :         return !_Bound->_Isnil && !_DEBUG_LT_PRED(_Getcomp(), _Keyval, _Traits::_Kfn(_Bound->_Myval));

  000a6	0f b6 44 24 30	 movzx	 eax, BYTE PTR $T1[rsp]
  000ab	0f b6 c0	 movzx	 eax, al
  000ae	85 c0		 test	 eax, eax
  000b0	75 0a		 jne	 SHORT $LN42@Try_emplac
  000b2	c7 44 24 3c 01
	00 00 00	 mov	 DWORD PTR tv171[rsp], 1
  000ba	eb 08		 jmp	 SHORT $LN43@Try_emplac
$LN42@Try_emplac:
  000bc	c7 44 24 3c 00
	00 00 00	 mov	 DWORD PTR tv171[rsp], 0
$LN43@Try_emplac:
  000c4	0f b6 44 24 3c	 movzx	 eax, BYTE PTR tv171[rsp]
  000c9	88 44 24 31	 mov	 BYTE PTR $T2[rsp], al
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map

; 198  :         if (_Mybase::_Lower_bound_duplicate(_Loc._Bound, _Keyval)) {

  000cd	0f b6 44 24 31	 movzx	 eax, BYTE PTR $T2[rsp]
  000d2	0f b6 c0	 movzx	 eax, al
  000d5	85 c0		 test	 eax, eax
  000d7	74 4f		 je	 SHORT $LN2@Try_emplac

; 199  :             return {_Loc._Bound, false};

  000d9	c6 44 24 32 00	 mov	 BYTE PTR $T3[rsp], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  000de	48 8d 84 24 30
	01 00 00	 lea	 rax, QWORD PTR _Loc$[rsp+16]
  000e6	48 89 44 24 68	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 274  :         : first(_STD forward<_Other1>(_Val1)), second(_STD forward<_Other2>(_Val2)) {

  000eb	48 8b 84 24 88
	01 00 00	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]
  000f3	48 8b 4c 24 68	 mov	 rcx, QWORD PTR $T8[rsp]
  000f8	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  000fb	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  000fe	48 8d 44 24 32	 lea	 rax, QWORD PTR $T3[rsp]
  00103	48 89 44 24 70	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 274  :         : first(_STD forward<_Other1>(_Val1)), second(_STD forward<_Other2>(_Val2)) {

  00108	48 8b 84 24 88
	01 00 00	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]
  00110	48 8b 4c 24 70	 mov	 rcx, QWORD PTR $T9[rsp]
  00115	0f b6 09	 movzx	 ecx, BYTE PTR [rcx]
  00118	88 48 08	 mov	 BYTE PTR [rax+8], cl
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map

; 199  :             return {_Loc._Bound, false};

  0011b	48 8b 84 24 88
	01 00 00	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]
  00123	e9 2d 02 00 00	 jmp	 $LN1@Try_emplac
$LN2@Try_emplac:

; 200  :         }
; 201  : 
; 202  :         _Mybase::_Check_grow_by_1();

  00128	48 8b 8c 24 80
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00130	e8 00 00 00 00	 call	 ?_Check_grow_by_1@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@IEAAXXZ ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::_Check_grow_by_1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00135	48 8b 84 24 80
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0013d	48 89 44 24 78	 mov	 QWORD PTR $T10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00142	48 8b 44 24 78	 mov	 rax, QWORD PTR $T10[rsp]
  00147	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map

; 204  :         const auto _Scary    = _Mybase::_Get_scary();

  0014f	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  00157	48 89 44 24 50	 mov	 QWORD PTR _Scary$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\tuple

; 1005 :     return tuple<_Types&&...>(_STD forward<_Types>(_Args)...);

  0015c	48 8d 44 24 34	 lea	 rax, QWORD PTR $T5[rsp]
  00161	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T19[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00169	48 8b 84 24 90
	01 00 00	 mov	 rax, QWORD PTR _Keyval$[rsp]
  00171	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map

; 205  :         const auto _Inserted = _Tree_temp_node<_Alnode>(_Mybase::_Getal(), _Scary->_Myhead, piecewise_construct,

  00179	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00181	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\tuple

; 1005 :     return tuple<_Types&&...>(_STD forward<_Types>(_Args)...);

  00189	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR $T13[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00191	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR $T14[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\tuple

; 301  :         : _Mybase(_Exact_args_t{}, _STD forward<_Rest2>(_Rest_arg)...), _Myfirst(_STD forward<_This2>(_This_arg)) {}

  00199	48 8d 84 24 b0
	00 00 00	 lea	 rax, QWORD PTR $T16[rsp]
  001a1	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR this$[rsp], rax
  001a9	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR $T14[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  001b1	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T15[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\tuple

; 132  :     constexpr _Tuple_val(_Other&& _Arg) : _Val(_STD forward<_Other>(_Arg)) {}

  001b9	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR $T15[rsp]
  001c1	48 8b 8c 24 a8
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  001c9	48 89 01	 mov	 QWORD PTR [rcx], rax

; 1005 :     return tuple<_Types&&...>(_STD forward<_Types>(_Args)...);

  001cc	48 8d 84 24 b0
	00 00 00	 lea	 rax, QWORD PTR $T16[rsp]
  001d4	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR $T20[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1975 :         return _Mypair._Myval2._Get_first();

  001dc	48 8b 84 24 80
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  001e4	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T17[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1975 :         return _Mypair._Myval2._Get_first();

  001ec	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T17[rsp]
  001f4	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T18[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map

; 205  :         const auto _Inserted = _Tree_temp_node<_Alnode>(_Mybase::_Getal(), _Scary->_Myhead, piecewise_construct,

  001fc	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR $T18[rsp]
  00204	48 8b 8c 24 c8
	00 00 00	 mov	 rcx, QWORD PTR $T19[rsp]
  0020c	48 89 4c 24 28	 mov	 QWORD PTR [rsp+40], rcx
  00211	48 8b 8c 24 d0
	00 00 00	 mov	 rcx, QWORD PTR $T20[rsp]
  00219	48 89 4c 24 20	 mov	 QWORD PTR [rsp+32], rcx
  0021e	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:?piecewise_construct@std@@3Upiecewise_construct_t@1@B
  00225	48 8b 4c 24 50	 mov	 rcx, QWORD PTR _Scary$[rsp]
  0022a	4c 8b 01	 mov	 r8, QWORD PTR [rcx]
  0022d	48 8b d0	 mov	 rdx, rax
  00230	48 8d 8c 24 40
	01 00 00	 lea	 rcx, QWORD PTR $T30[rsp]
  00238	e8 00 00 00 00	 call	 ??$?0AEBUpiecewise_construct_t@std@@V?$tuple@AEBI@1@V?$tuple@$$V@1@@?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBUpiecewise_construct_t@1@$$QEAV?$tuple@AEBI@1@$$QEAV?$tuple@$$V@1@@Z ; std::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > ><std::piecewise_construct_t const &,std::tuple<unsigned int const &>,std::tuple<> >
  0023d	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR $T21[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1163 :         return _STD exchange(_Ptr, nullptr);

  00245	48 c7 84 24 e0
	00 00 00 00 00
	00 00		 mov	 QWORD PTR $T22[rsp], 0
  00251	48 8b 84 24 d8
	00 00 00	 mov	 rax, QWORD PTR $T21[rsp]
  00259	48 83 c0 08	 add	 rax, 8
  0025d	48 89 44 24 48	 mov	 QWORD PTR _Val$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 773  :     _Ty _Old_val = static_cast<_Ty&&>(_Val);

  00262	48 8b 44 24 48	 mov	 rax, QWORD PTR _Val$[rsp]
  00267	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0026a	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR _Old_val$23[rsp], rax

; 774  :     _Val         = static_cast<_Other&&>(_New_val);

  00272	48 8b 44 24 48	 mov	 rax, QWORD PTR _Val$[rsp]
  00277	48 8b 8c 24 e0
	00 00 00	 mov	 rcx, QWORD PTR $T22[rsp]
  0027f	48 89 08	 mov	 QWORD PTR [rax], rcx

; 775  :     return _Old_val;

  00282	48 8b 84 24 e8
	00 00 00	 mov	 rax, QWORD PTR _Old_val$23[rsp]
  0028a	48 89 84 24 f0
	00 00 00	 mov	 QWORD PTR $T24[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1163 :         return _STD exchange(_Ptr, nullptr);

  00292	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR $T24[rsp]
  0029a	48 89 84 24 f8
	00 00 00	 mov	 QWORD PTR $T25[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map

; 205  :         const auto _Inserted = _Tree_temp_node<_Alnode>(_Mybase::_Getal(), _Scary->_Myhead, piecewise_construct,

  002a2	48 8b 84 24 f8
	00 00 00	 mov	 rax, QWORD PTR $T25[rsp]
  002aa	48 89 84 24 00
	01 00 00	 mov	 QWORD PTR _Inserted$[rsp], rax
  002b2	48 8d 8c 24 40
	01 00 00	 lea	 rcx, QWORD PTR $T30[rsp]
  002ba	e8 00 00 00 00	 call	 ??1?$_Tree_temp_node@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@std@@QEAA@XZ ; std::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >::~_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >
  002bf	90		 npad	 1

; 211  :         return {_Scary->_Insert_node(_Loc._Location, _Inserted), true};

  002c0	48 8d 84 24 50
	01 00 00	 lea	 rax, QWORD PTR $T31[rsp]
  002c8	48 8d 8c 24 20
	01 00 00	 lea	 rcx, QWORD PTR _Loc$[rsp]
  002d0	48 8b f8	 mov	 rdi, rax
  002d3	48 8b f1	 mov	 rsi, rcx
  002d6	b9 10 00 00 00	 mov	 ecx, 16
  002db	f3 a4		 rep movsb
  002dd	4c 8b 84 24 00
	01 00 00	 mov	 r8, QWORD PTR _Inserted$[rsp]
  002e5	48 8d 94 24 50
	01 00 00	 lea	 rdx, QWORD PTR $T31[rsp]
  002ed	48 8b 4c 24 50	 mov	 rcx, QWORD PTR _Scary$[rsp]
  002f2	e8 00 00 00 00	 call	 ?_Insert_node@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@U?$_Tree_id@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@2@QEAU32@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::_Insert_node
  002f7	48 89 84 24 08
	01 00 00	 mov	 QWORD PTR $T26[rsp], rax
  002ff	c6 44 24 33 01	 mov	 BYTE PTR $T4[rsp], 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00304	48 8d 84 24 08
	01 00 00	 lea	 rax, QWORD PTR $T26[rsp]
  0030c	48 89 84 24 10
	01 00 00	 mov	 QWORD PTR $T27[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 274  :         : first(_STD forward<_Other1>(_Val1)), second(_STD forward<_Other2>(_Val2)) {

  00314	48 8b 84 24 88
	01 00 00	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]
  0031c	48 8b 8c 24 10
	01 00 00	 mov	 rcx, QWORD PTR $T27[rsp]
  00324	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00327	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  0032a	48 8d 44 24 33	 lea	 rax, QWORD PTR $T4[rsp]
  0032f	48 89 84 24 18
	01 00 00	 mov	 QWORD PTR $T28[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 274  :         : first(_STD forward<_Other1>(_Val1)), second(_STD forward<_Other2>(_Val2)) {

  00337	48 8b 84 24 88
	01 00 00	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]
  0033f	48 8b 8c 24 18
	01 00 00	 mov	 rcx, QWORD PTR $T28[rsp]
  00347	0f b6 09	 movzx	 ecx, BYTE PTR [rcx]
  0034a	88 48 08	 mov	 BYTE PTR [rax+8], cl
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map

; 211  :         return {_Scary->_Insert_node(_Loc._Location, _Inserted), true};

  0034d	48 8b 84 24 88
	01 00 00	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]
$LN1@Try_emplac:

; 212  :     }

  00355	48 81 c4 68 01
	00 00		 add	 rsp, 360		; 00000168H
  0035c	5f		 pop	 rdi
  0035d	5e		 pop	 rsi
  0035e	c3		 ret	 0
??$_Try_emplace@AEBI$$V@?$map@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@@std@@AEAA?AU?$pair@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@_N@1@AEBI@Z ENDP ; std::map<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::_Try_emplace<unsigned int const &>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@@Z
_TEXT	SEGMENT
_Bytes$ = 32
_Ptr$ = 40
_Ptr$ = 48
this$ = 80
_Al$ = 88
??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@@Z PROC ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::_Erase_head<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >, COMDAT

; 775  :     void _Erase_head(_Alnode& _Al) noexcept {

$LN129:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 776  :         this->_Orphan_all();
; 777  :         _Erase_tree(_Al, _Myhead->_Parent);

  0000e	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00013	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00016	4c 8b 40 08	 mov	 r8, QWORD PTR [rax+8]
  0001a	48 8b 54 24 58	 mov	 rdx, QWORD PTR _Al$[rsp]
  0001f	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  00024	e8 00 00 00 00	 call	 ??$_Erase_tree@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::_Erase_tree<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >

; 778  :         _Alnode::value_type::_Freenode0(_Al, _Myhead);

  00029	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0002e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00031	48 89 44 24 30	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 723  :             _STD _Deallocate<_New_alignof<value_type>>(_Ptr, sizeof(value_type) * _Count);

  00036	b8 01 00 00 00	 mov	 eax, 1
  0003b	48 6b c0 48	 imul	 rax, rax, 72		; 00000048H
  0003f	48 89 44 24 20	 mov	 QWORD PTR _Bytes$[rsp], rax
  00044	48 8b 44 24 30	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00049	48 89 44 24 28	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  0004e	48 81 7c 24 20
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  00057	72 10		 jb	 SHORT $LN120@Erase_head

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  00059	48 8d 54 24 20	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  0005e	48 8d 4c 24 28	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  00063	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  00068	90		 npad	 1
$LN120@Erase_head:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  00069	48 8b 54 24 20	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  0006e	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  00073	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00078	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 779  :     }

  00079	48 83 c4 48	 add	 rsp, 72			; 00000048H
  0007d	c3		 ret	 0
??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@@Z ENDP ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::_Erase_head<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??$_Find@I@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@AEBAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBI@Z
_TEXT	SEGMENT
$T1 = 32
$T2 = 40
_Loc$ = 48
this$ = 96
_Keyval$ = 104
??$_Find@I@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@AEBAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBI@Z PROC ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::_Find<unsigned int>, COMDAT

; 1383 :     _NODISCARD _Nodeptr _Find(const _Other& _Keyval) const {

$LN72:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 1384 :         const _Tree_find_result<_Nodeptr> _Loc = _Find_lower_bound(_Keyval);

  0000e	4c 8b 44 24 68	 mov	 r8, QWORD PTR _Keyval$[rsp]
  00013	48 8d 54 24 30	 lea	 rdx, QWORD PTR _Loc$[rsp]
  00018	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  0001d	e8 00 00 00 00	 call	 ??$_Find_lower_bound@I@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@IEBA?AU?$_Tree_find_result@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@AEBI@Z ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::_Find_lower_bound<unsigned int>
  00022	90		 npad	 1

; 1385 :         if (_Lower_bound_duplicate(_Loc._Bound, _Keyval)) {

  00023	4c 8b 44 24 68	 mov	 r8, QWORD PTR _Keyval$[rsp]
  00028	48 8b 54 24 40	 mov	 rdx, QWORD PTR _Loc$[rsp+16]
  0002d	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  00032	e8 00 00 00 00	 call	 ??$_Lower_bound_duplicate@I@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@IEBA_NQEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBI@Z ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::_Lower_bound_duplicate<unsigned int>
  00037	0f b6 c0	 movzx	 eax, al
  0003a	85 c0		 test	 eax, eax
  0003c	74 07		 je	 SHORT $LN2@Find

; 1386 :             return _Loc._Bound;

  0003e	48 8b 44 24 40	 mov	 rax, QWORD PTR _Loc$[rsp+16]
  00043	eb 1c		 jmp	 SHORT $LN1@Find
$LN2@Find:

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00045	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0004a	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1987 :         return _STD addressof(_Mypair._Myval2._Myval2);

  0004f	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00054	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax

; 1387 :         }
; 1388 : 
; 1389 :         return _Get_scary()->_Myhead;

  00059	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
  0005e	48 8b 00	 mov	 rax, QWORD PTR [rax]
$LN1@Find:

; 1390 :     }

  00061	48 83 c4 58	 add	 rsp, 88			; 00000058H
  00065	c3		 ret	 0
??$_Find@I@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@AEBAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBI@Z ENDP ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::_Find<unsigned int>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??$erase@V?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@@std@@$0A@@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@QEAA?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@@1@V21@@Z
_TEXT	SEGMENT
$T1 = 32
$T2 = 40
__param0$ = 48
$T3 = 56
$T4 = 64
$T5 = 72
__param0$ = 80
_Scary$ = 88
$T6 = 96
this$ = 128
__$ReturnUdt$ = 136
_Where$ = 144
??$erase@V?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@@std@@$0A@@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@QEAA?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@@1@V21@@Z PROC ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::erase<std::_Tree_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > > >,0>, COMDAT

; 1335 :     iterator erase(iterator _Where) noexcept /* strengthened */ {

$LN178:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 78	 sub	 rsp, 120		; 00000078H

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00013	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0001b	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00020	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00025	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax

; 1336 :         const auto _Scary = _Get_scary();

  0002a	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
  0002f	48 89 44 24 58	 mov	 QWORD PTR _Scary$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1193 :         return nullptr;

  00034	48 c7 44 24 60
	00 00 00 00	 mov	 QWORD PTR $T6[rsp], 0
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 306  :         return _Tree_unchecked_iterator<_Mytree>(this->_Ptr, static_cast<const _Mytree*>(this->_Getcont()));

  0003d	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR _Where$[rsp]
  00045	48 89 44 24 30	 mov	 QWORD PTR __param0$[rsp], rax

; 37   :     _Tree_unchecked_const_iterator(_Nodeptr _Pnode, const _Mytree* _Plist) noexcept : _Ptr(_Pnode) {

  0004a	48 8b 44 24 30	 mov	 rax, QWORD PTR __param0$[rsp]
  0004f	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax

; 306  :         return _Tree_unchecked_iterator<_Mytree>(this->_Ptr, static_cast<const _Mytree*>(this->_Getcont()));

  00054	48 8d 44 24 38	 lea	 rax, QWORD PTR $T3[rsp]
  00059	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax

; 1337 : #if _ITERATOR_DEBUG_LEVEL == 2
; 1338 :         _STL_VERIFY(_Where._Getcont() == _Scary, "map/set erase iterator from incorrect container");
; 1339 :         _STL_VERIFY(!_Where._Ptr->_Isnil, "cannot erase map/set end() iterator");
; 1340 : #endif // _ITERATOR_DEBUG_LEVEL == 2
; 1341 :         return iterator(_Erase_unchecked(_Where._Unwrapped()), _Scary);

  0005e	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  00063	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00066	48 89 44 24 48	 mov	 QWORD PTR $T5[rsp], rax
  0006b	48 8b 54 24 48	 mov	 rdx, QWORD PTR $T5[rsp]
  00070	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00078	e8 00 00 00 00	 call	 ?_Erase_unchecked@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@AEAAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::_Erase_unchecked
  0007d	48 89 44 24 50	 mov	 QWORD PTR __param0$[rsp], rax

; 37   :     _Tree_unchecked_const_iterator(_Nodeptr _Pnode, const _Mytree* _Plist) noexcept : _Ptr(_Pnode) {

  00082	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]
  0008a	48 8b 4c 24 50	 mov	 rcx, QWORD PTR __param0$[rsp]
  0008f	48 89 08	 mov	 QWORD PTR [rax], rcx

; 1337 : #if _ITERATOR_DEBUG_LEVEL == 2
; 1338 :         _STL_VERIFY(_Where._Getcont() == _Scary, "map/set erase iterator from incorrect container");
; 1339 :         _STL_VERIFY(!_Where._Ptr->_Isnil, "cannot erase map/set end() iterator");
; 1340 : #endif // _ITERATOR_DEBUG_LEVEL == 2
; 1341 :         return iterator(_Erase_unchecked(_Where._Unwrapped()), _Scary);

  00092	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]

; 1342 :     }

  0009a	48 83 c4 78	 add	 rsp, 120		; 00000078H
  0009e	c3		 ret	 0
??$erase@V?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@@std@@$0A@@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@QEAA?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@@1@V21@@Z ENDP ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::erase<std::_Tree_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > > >,0>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ
_TEXT	SEGMENT
_Pnode$1 = 0
_Pnode$ = 8
$T2 = 16
this$ = 48
??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ PROC ; std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >,std::_Iterator_base0>::operator++, COMDAT

; 49   :     _Tree_unchecked_const_iterator& operator++() noexcept {

$LN15:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 50   :         if (_Ptr->_Right->_Isnil) { // climb looking for right subtree

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00011	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00015	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  00019	85 c0		 test	 eax, eax
  0001b	74 4a		 je	 SHORT $LN4@operator
$LN2@operator:

; 51   :             _Nodeptr _Pnode;
; 52   :             while (!(_Pnode = _Ptr->_Parent)->_Isnil && _Ptr == _Pnode->_Right) {

  0001d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00022	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00025	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00029	48 89 04 24	 mov	 QWORD PTR _Pnode$1[rsp], rax
  0002d	48 8b 04 24	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  00031	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  00035	85 c0		 test	 eax, eax
  00037	75 20		 jne	 SHORT $LN3@operator
  00039	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003e	48 8b 0c 24	 mov	 rcx, QWORD PTR _Pnode$1[rsp]
  00042	48 8b 49 10	 mov	 rcx, QWORD PTR [rcx+16]
  00046	48 39 08	 cmp	 QWORD PTR [rax], rcx
  00049	75 0e		 jne	 SHORT $LN3@operator

; 53   :                 _Ptr = _Pnode; // ==> parent while right subtree

  0004b	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00050	48 8b 0c 24	 mov	 rcx, QWORD PTR _Pnode$1[rsp]
  00054	48 89 08	 mov	 QWORD PTR [rax], rcx

; 54   :             }

  00057	eb c4		 jmp	 SHORT $LN2@operator
$LN3@operator:

; 55   : 
; 56   :             _Ptr = _Pnode; // ==> parent (head if end())

  00059	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0005e	48 8b 0c 24	 mov	 rcx, QWORD PTR _Pnode$1[rsp]
  00062	48 89 08	 mov	 QWORD PTR [rax], rcx

; 57   :         } else {

  00065	eb 47		 jmp	 SHORT $LN5@operator
$LN4@operator:

; 58   :             _Ptr = _Mytree::_Min(_Ptr->_Right); // ==> smallest of right subtree

  00067	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0006c	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0006f	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00073	48 89 44 24 08	 mov	 QWORD PTR _Pnode$[rsp], rax
$LN9@operator:

; 476  :         while (!_Pnode->_Left->_Isnil) {

  00078	48 8b 44 24 08	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0007d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00080	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  00084	85 c0		 test	 eax, eax
  00086	75 0f		 jne	 SHORT $LN10@operator

; 477  :             _Pnode = _Pnode->_Left;

  00088	48 8b 44 24 08	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0008d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00090	48 89 44 24 08	 mov	 QWORD PTR _Pnode$[rsp], rax

; 478  :         }

  00095	eb e1		 jmp	 SHORT $LN9@operator
$LN10@operator:

; 479  : 
; 480  :         return _Pnode;

  00097	48 8b 44 24 08	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0009c	48 89 44 24 10	 mov	 QWORD PTR $T2[rsp], rax

; 58   :             _Ptr = _Mytree::_Min(_Ptr->_Right); // ==> smallest of right subtree

  000a1	48 8b 44 24 10	 mov	 rax, QWORD PTR $T2[rsp]
  000a6	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  000ab	48 89 01	 mov	 QWORD PTR [rcx], rax
$LN5@operator:

; 59   :         }
; 60   : 
; 61   :         return *this;

  000ae	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]

; 62   :     }

  000b3	48 83 c4 28	 add	 rsp, 40			; 00000028H
  000b7	c3		 ret	 0
??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ ENDP ; std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >,std::_Iterator_base0>::operator++
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
_TEXT	SEGMENT
_Ptr_container$ = 48
_Block_size$ = 56
_Ptr$ = 64
$T1 = 72
_Bytes$ = 96
??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z PROC ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>, COMDAT

; 182  : __declspec(allocator) void* _Allocate_manually_vector_aligned(const size_t _Bytes) {

$LN7:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 183  :     // allocate _Bytes manually aligned to at least _Big_allocation_alignment
; 184  :     const size_t _Block_size = _Non_user_size + _Bytes;

  00009	48 8b 44 24 60	 mov	 rax, QWORD PTR _Bytes$[rsp]
  0000e	48 83 c0 27	 add	 rax, 39			; 00000027H
  00012	48 89 44 24 38	 mov	 QWORD PTR _Block_size$[rsp], rax

; 185  :     if (_Block_size <= _Bytes) {

  00017	48 8b 44 24 60	 mov	 rax, QWORD PTR _Bytes$[rsp]
  0001c	48 39 44 24 38	 cmp	 QWORD PTR _Block_size$[rsp], rax
  00021	77 06		 ja	 SHORT $LN2@Allocate_m

; 186  :         _Throw_bad_array_new_length(); // add overflow

  00023	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00028	90		 npad	 1
$LN2@Allocate_m:

; 136  :         return ::operator new(_Bytes);

  00029	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Block_size$[rsp]
  0002e	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00033	48 89 44 24 48	 mov	 QWORD PTR $T1[rsp], rax

; 187  :     }
; 188  : 
; 189  :     const uintptr_t _Ptr_container = reinterpret_cast<uintptr_t>(_Traits::_Allocate(_Block_size));

  00038	48 8b 44 24 48	 mov	 rax, QWORD PTR $T1[rsp]
  0003d	48 89 44 24 30	 mov	 QWORD PTR _Ptr_container$[rsp], rax

; 190  :     _STL_VERIFY(_Ptr_container != 0, "invalid argument"); // validate even in release since we're doing p[-1]

  00042	48 83 7c 24 30
	00		 cmp	 QWORD PTR _Ptr_container$[rsp], 0
  00048	75 19		 jne	 SHORT $LN3@Allocate_m
  0004a	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR [rsp+32], 0
  00053	45 33 c9	 xor	 r9d, r9d
  00056	45 33 c0	 xor	 r8d, r8d
  00059	33 d2		 xor	 edx, edx
  0005b	33 c9		 xor	 ecx, ecx
  0005d	e8 00 00 00 00	 call	 _invoke_watson
  00062	90		 npad	 1
$LN3@Allocate_m:

; 191  :     void* const _Ptr = reinterpret_cast<void*>((_Ptr_container + _Non_user_size) & ~(_Big_allocation_alignment - 1));

  00063	48 8b 44 24 30	 mov	 rax, QWORD PTR _Ptr_container$[rsp]
  00068	48 83 c0 27	 add	 rax, 39			; 00000027H
  0006c	48 83 e0 e0	 and	 rax, -32		; ffffffffffffffe0H
  00070	48 89 44 24 40	 mov	 QWORD PTR _Ptr$[rsp], rax

; 192  :     static_cast<uintptr_t*>(_Ptr)[-1] = _Ptr_container;

  00075	b8 08 00 00 00	 mov	 eax, 8
  0007a	48 6b c0 ff	 imul	 rax, rax, -1
  0007e	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  00083	48 8b 54 24 30	 mov	 rdx, QWORD PTR _Ptr_container$[rsp]
  00088	48 89 14 01	 mov	 QWORD PTR [rcx+rax], rdx

; 193  : 
; 194  : #ifdef _DEBUG
; 195  :     static_cast<uintptr_t*>(_Ptr)[-2] = _Big_allocation_sentinel;
; 196  : #endif // defined(_DEBUG)
; 197  :     return _Ptr;

  0008c	48 8b 44 24 40	 mov	 rax, QWORD PTR _Ptr$[rsp]
$LN4@Allocate_m:

; 198  : }

  00091	48 83 c4 58	 add	 rsp, 88			; 00000058H
  00095	c3		 ret	 0
??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ENDP ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??_GMissionMapJoinManager@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GMissionMapJoinManager@mu2@@UEAAPEAXI@Z PROC		; mu2::MissionMapJoinManager::`scalar deleting destructor', COMDAT
$LN5:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00012	e8 00 00 00 00	 call	 ??1MissionMapJoinManager@mu2@@UEAA@XZ ; mu2::MissionMapJoinManager::~MissionMapJoinManager
  00017	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  0001b	83 e0 01	 and	 eax, 1
  0001e	85 c0		 test	 eax, eax
  00020	74 10		 je	 SHORT $LN2@scalar
  00022	ba b0 00 00 00	 mov	 edx, 176		; 000000b0H
  00027	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0002c	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00031	90		 npad	 1
$LN2@scalar:
  00032	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00037	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0003b	c3		 ret	 0
??_GMissionMapJoinManager@mu2@@UEAAPEAXI@Z ENDP		; mu2::MissionMapJoinManager::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.cpp
;	COMDAT ??0MissionMapJoinManager@mu2@@AEAA@XZ
_TEXT	SEGMENT
this$ = 48
this$ = 80
??0MissionMapJoinManager@mu2@@AEAA@XZ PROC		; mu2::MissionMapJoinManager::MissionMapJoinManager, COMDAT

; 10   : {

$LN10:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 48	 sub	 rsp, 72			; 00000048H
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 84   : 	ISingleton() {}	

  00009	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.cpp

; 10   : {

  00018	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7MissionMapJoinManager@mu2@@6B@
  00024	48 89 08	 mov	 QWORD PTR [rax], rcx
  00027	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0002c	48 83 c0 08	 add	 rax, 8
  00030	48 89 44 24 30	 mov	 QWORD PTR this$[rsp], rax
  00035	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003a	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??1?$map@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@@std@@QEAA@XZ
  00041	48 89 4c 24 20	 mov	 QWORD PTR [rsp+32], rcx
  00046	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??0?$map@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@@std@@QEAA@XZ ; std::map<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::map<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >
  0004d	41 b8 0a 00 00
	00		 mov	 r8d, 10
  00053	ba 10 00 00 00	 mov	 edx, 16
  00058	48 8b c8	 mov	 rcx, rax
  0005b	e8 00 00 00 00	 call	 ??_L@YAXPEAX_K1P6AX0@Z2@Z
  00060	90		 npad	 1

; 9    : :nextUpdateIndex(0) 

  00061	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00066	c6 80 a8 00 00
	00 00		 mov	 BYTE PTR [rax+168], 0

; 11   : }

  0006d	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00072	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00076	c3		 ret	 0
??0MissionMapJoinManager@mu2@@AEAA@XZ ENDP		; mu2::MissionMapJoinManager::MissionMapJoinManager
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
this$ = 48
this$ = 80
?dtor$0@?0???0MissionMapJoinManager@mu2@@AEAA@XZ@4HA PROC ; `mu2::MissionMapJoinManager::MissionMapJoinManager'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 50	 mov	 rcx, QWORD PTR this$[rbp]
  0000d	e8 00 00 00 00	 call	 ??1?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@UEAA@XZ ; mu2::ISingleton<mu2::MissionMapJoinManager>::~ISingleton<mu2::MissionMapJoinManager>
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$0@?0???0MissionMapJoinManager@mu2@@AEAA@XZ@4HA ENDP ; `mu2::MissionMapJoinManager::MissionMapJoinManager'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\array
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.cpp
;	COMDAT ?Update@MissionMapJoinManager@mu2@@QEAAXXZ
_TEXT	SEGMENT
$T1 = 32
$T2 = 33
itr$ = 40
tickCurr$ = 48
tv254 = 52
tv230 = 56
mapInfo$ = 64
exec$3 = 72
info$4 = 80
_Pos$ = 88
$T5 = 96
$T6 = 104
$T7 = 112
_Scary$8 = 120
__param0$ = 128
$T9 = 136
$T10 = 144
_Scary$11 = 152
__param0$ = 160
$T12 = 168
$T13 = 176
$T14 = 184
$T15 = 192
$T16 = 200
$T17 = 208
$T18 = 216
$T19 = 224
_Tmp$20 = 232
$T21 = 240
$T22 = 248
$T23 = 256
$T24 = 264
this$ = 288
?Update@MissionMapJoinManager@mu2@@QEAAXXZ PROC		; mu2::MissionMapJoinManager::Update, COMDAT

; 78   : {

$LN545:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 81 ec 18 01
	00 00		 sub	 rsp, 280		; 00000118H

; 79   : 	static Tick tickUpdated = 0;
; 80   : 
; 81   : 	Tick tickCurr = ::GetTickCount();

  0000c	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetTickCount
  00012	89 44 24 30	 mov	 DWORD PTR tickCurr$[rsp], eax

; 82   : 
; 83   : 	if (tickCurr - tickUpdated < UPDATE_TIME)

  00016	8b 05 00 00 00
	00		 mov	 eax, DWORD PTR ?tickUpdated@?1??Update@MissionMapJoinManager@mu2@@QEAAXXZ@4IA
  0001c	8b 4c 24 30	 mov	 ecx, DWORD PTR tickCurr$[rsp]
  00020	2b c8		 sub	 ecx, eax
  00022	8b c1		 mov	 eax, ecx
  00024	3d 88 13 00 00	 cmp	 eax, 5000		; 00001388H
  00029	73 05		 jae	 SHORT $LN4@Update

; 84   : 		return;

  0002b	e9 ef 02 00 00	 jmp	 $LN1@Update
$LN4@Update:

; 85   : 
; 86   : 	tickUpdated = tickCurr;

  00030	8b 44 24 30	 mov	 eax, DWORD PTR tickCurr$[rsp]
  00034	89 05 00 00 00
	00		 mov	 DWORD PTR ?tickUpdated@?1??Update@MissionMapJoinManager@mu2@@QEAAXXZ@4IA, eax

; 88   : 	MapMissionMapJoinInfo& mapInfo = m_arrayMissionMapJoinInfo[nextUpdateIndex];

  0003a	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00042	0f b6 80 a8 00
	00 00		 movzx	 eax, BYTE PTR [rax+168]
  00049	48 89 44 24 58	 mov	 QWORD PTR _Pos$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\array

; 534  :         return _Elems[_Pos];

  0004e	48 6b 44 24 58
	10		 imul	 rax, QWORD PTR _Pos$[rsp], 16
  00054	48 8b 8c 24 20
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0005c	48 8d 44 01 08	 lea	 rax, QWORD PTR [rcx+rax+8]
  00061	48 89 44 24 60	 mov	 QWORD PTR $T5[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.cpp

; 88   : 	MapMissionMapJoinInfo& mapInfo = m_arrayMissionMapJoinInfo[nextUpdateIndex];

  00066	48 8b 44 24 60	 mov	 rax, QWORD PTR $T5[rsp]
  0006b	48 89 44 24 40	 mov	 QWORD PTR mapInfo$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00070	48 8b 44 24 40	 mov	 rax, QWORD PTR mapInfo$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00075	48 89 44 24 68	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  0007a	48 8b 44 24 68	 mov	 rax, QWORD PTR $T6[rsp]
  0007f	48 89 44 24 70	 mov	 QWORD PTR $T7[rsp], rax

; 1141 :         const auto _Scary = _Get_scary();

  00084	48 8b 44 24 70	 mov	 rax, QWORD PTR $T7[rsp]
  00089	48 89 44 24 78	 mov	 QWORD PTR _Scary$8[rsp], rax

; 1142 :         return iterator(_Scary->_Myhead->_Left, _Scary);

  0008e	48 8b 44 24 78	 mov	 rax, QWORD PTR _Scary$8[rsp]
  00093	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00096	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00099	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR __param0$[rsp], rax

; 37   :     _Tree_unchecked_const_iterator(_Nodeptr _Pnode, const _Mytree* _Plist) noexcept : _Ptr(_Pnode) {

  000a1	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR __param0$[rsp]
  000a9	48 89 44 24 28	 mov	 QWORD PTR itr$[rsp], rax
$LN2@Update:

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  000ae	48 8b 44 24 40	 mov	 rax, QWORD PTR mapInfo$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  000b3	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  000bb	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T9[rsp]
  000c3	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax

; 1151 :         const auto _Scary = _Get_scary();

  000cb	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  000d3	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR _Scary$11[rsp], rax

; 1152 :         return iterator(_Scary->_Myhead, _Scary);

  000db	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR _Scary$11[rsp]
  000e3	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000e6	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR __param0$[rsp], rax

; 37   :     _Tree_unchecked_const_iterator(_Nodeptr _Pnode, const _Mytree* _Plist) noexcept : _Ptr(_Pnode) {

  000ee	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR __param0$[rsp]
  000f6	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax

; 1152 :         return iterator(_Scary->_Myhead, _Scary);

  000fe	48 8d 84 24 a8
	00 00 00	 lea	 rax, QWORD PTR $T12[rsp]
  00106	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax

; 232  :         return this->_Ptr == _Right._Ptr;

  0010e	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR $T13[rsp]
  00116	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00119	48 39 44 24 28	 cmp	 QWORD PTR itr$[rsp], rax
  0011e	75 0a		 jne	 SHORT $LN94@Update
  00120	c7 44 24 34 01
	00 00 00	 mov	 DWORD PTR tv254[rsp], 1
  00128	eb 08		 jmp	 SHORT $LN95@Update
$LN94@Update:
  0012a	c7 44 24 34 00
	00 00 00	 mov	 DWORD PTR tv254[rsp], 0
$LN95@Update:
  00132	0f b6 44 24 34	 movzx	 eax, BYTE PTR tv254[rsp]
  00137	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al

; 237  :         return !(*this == _Right);

  0013b	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  00140	0f b6 c0	 movzx	 eax, al
  00143	85 c0		 test	 eax, eax
  00145	75 0a		 jne	 SHORT $LN87@Update
  00147	c7 44 24 38 01
	00 00 00	 mov	 DWORD PTR tv230[rsp], 1
  0014f	eb 08		 jmp	 SHORT $LN88@Update
$LN87@Update:
  00151	c7 44 24 38 00
	00 00 00	 mov	 DWORD PTR tv230[rsp], 0
$LN88@Update:
  00159	0f b6 44 24 38	 movzx	 eax, BYTE PTR tv230[rsp]
  0015e	88 44 24 21	 mov	 BYTE PTR $T2[rsp], al
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.cpp

; 91   : 	while (itr != mapInfo.end())

  00162	0f b6 44 24 21	 movzx	 eax, BYTE PTR $T2[rsp]
  00167	0f b6 c0	 movzx	 eax, al
  0016a	85 c0		 test	 eax, eax
  0016c	0f 84 6b 01 00
	00		 je	 $LN3@Update
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 185  :         return this->_Ptr->_Myval;

  00172	48 8b 44 24 28	 mov	 rax, QWORD PTR itr$[rsp]
  00177	48 83 c0 20	 add	 rax, 32			; 00000020H
  0017b	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T14[rsp], rax

; 274  :         return const_cast<reference>(_Mybase::operator*());

  00183	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR $T14[rsp]
  0018b	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T15[rsp], rax

; 278  :         return pointer_traits<pointer>::pointer_to(**this);

  00193	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR $T15[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0019b	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T16[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 528  :         return _STD addressof(_Val);

  001a3	48 8b 84 24 c8
	00 00 00	 mov	 rax, QWORD PTR $T16[rsp]
  001ab	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR $T17[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 278  :         return pointer_traits<pointer>::pointer_to(**this);

  001b3	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR $T17[rsp]
  001bb	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR $T18[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.cpp

; 93   : 		MissionMapJoinInfo& info = itr->second;

  001c3	48 8b 84 24 d8
	00 00 00	 mov	 rax, QWORD PTR $T18[rsp]
  001cb	48 83 c0 08	 add	 rax, 8
  001cf	48 89 44 24 50	 mov	 QWORD PTR info$4[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  001d4	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A ; mu2::ISingleton<mu2::ExecutionManager>::inst
  001db	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR $T19[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.cpp

; 95   : 		const Execution& exec = theExecutionManager.GetExecution(info.execId);

  001e3	48 8b 44 24 50	 mov	 rax, QWORD PTR info$4[rsp]
  001e8	48 83 c0 08	 add	 rax, 8
  001ec	48 8b 8c 24 e0
	00 00 00	 mov	 rcx, QWORD PTR $T19[rsp]
  001f4	48 8b d0	 mov	 rdx, rax
  001f7	e8 00 00 00 00	 call	 ?GetExecution@ExecutionManager@mu2@@QEAAAEBVExecution@2@AEBVExecutionZoneId@2@@Z ; mu2::ExecutionManager::GetExecution
  001fc	48 89 44 24 48	 mov	 QWORD PTR exec$3[rsp], rax

; 96   : 		if (false == exec.IsValid())

  00201	48 8b 44 24 48	 mov	 rax, QWORD PTR exec$3[rsp]
  00206	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00209	48 8b 4c 24 48	 mov	 rcx, QWORD PTR exec$3[rsp]
  0020e	ff 90 b0 00 00
	00		 call	 QWORD PTR [rax+176]
  00214	0f b6 c0	 movzx	 eax, al
  00217	85 c0		 test	 eax, eax
  00219	75 24		 jne	 SHORT $LN5@Update

; 97   : 		{
; 98   : 			itr = mapInfo.erase(itr);

  0021b	4c 8b 44 24 28	 mov	 r8, QWORD PTR itr$[rsp]
  00220	48 8d 94 24 f0
	00 00 00	 lea	 rdx, QWORD PTR $T21[rsp]
  00228	48 8b 4c 24 40	 mov	 rcx, QWORD PTR mapInfo$[rsp]
  0022d	e8 00 00 00 00	 call	 ??$erase@V?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@@std@@$0A@@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@QEAA?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@@1@V21@@Z ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::erase<std::_Tree_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > > >,0>
  00232	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00235	48 89 44 24 28	 mov	 QWORD PTR itr$[rsp], rax

; 99   : 			continue;

  0023a	e9 6f fe ff ff	 jmp	 $LN2@Update
$LN5@Update:

; 100  : 		}
; 101  : 
; 102  : 		if (false == exec.IsJoinable(NULL))

  0023f	48 8b 44 24 48	 mov	 rax, QWORD PTR exec$3[rsp]
  00244	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00247	33 d2		 xor	 edx, edx
  00249	48 8b 4c 24 48	 mov	 rcx, QWORD PTR exec$3[rsp]
  0024e	ff 90 c0 00 00
	00		 call	 QWORD PTR [rax+192]
  00254	0f b6 c0	 movzx	 eax, al
  00257	85 c0		 test	 eax, eax
  00259	75 24		 jne	 SHORT $LN6@Update

; 103  : 		{
; 104  : 			itr = mapInfo.erase(itr);

  0025b	4c 8b 44 24 28	 mov	 r8, QWORD PTR itr$[rsp]
  00260	48 8d 94 24 f8
	00 00 00	 lea	 rdx, QWORD PTR $T22[rsp]
  00268	48 8b 4c 24 40	 mov	 rcx, QWORD PTR mapInfo$[rsp]
  0026d	e8 00 00 00 00	 call	 ??$erase@V?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@@std@@$0A@@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@QEAA?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@@1@V21@@Z ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::erase<std::_Tree_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > > >,0>
  00272	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00275	48 89 44 24 28	 mov	 QWORD PTR itr$[rsp], rax

; 105  : 			continue;;

  0027a	e9 2f fe ff ff	 jmp	 $LN2@Update
$LN6@Update:

; 106  : 		}		
; 107  : 		
; 108  : 		if (info.expireTick <= tickCurr)

  0027f	48 8b 44 24 50	 mov	 rax, QWORD PTR info$4[rsp]
  00284	8b 4c 24 30	 mov	 ecx, DWORD PTR tickCurr$[rsp]
  00288	39 08		 cmp	 DWORD PTR [rax], ecx
  0028a	77 24		 ja	 SHORT $LN7@Update

; 109  : 		{
; 110  : 			itr = mapInfo.erase(itr);

  0028c	4c 8b 44 24 28	 mov	 r8, QWORD PTR itr$[rsp]
  00291	48 8d 94 24 00
	01 00 00	 lea	 rdx, QWORD PTR $T23[rsp]
  00299	48 8b 4c 24 40	 mov	 rcx, QWORD PTR mapInfo$[rsp]
  0029e	e8 00 00 00 00	 call	 ??$erase@V?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@@std@@$0A@@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@QEAA?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@@1@V21@@Z ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::erase<std::_Tree_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > > >,0>
  002a3	48 8b 00	 mov	 rax, QWORD PTR [rax]
  002a6	48 89 44 24 28	 mov	 QWORD PTR itr$[rsp], rax

; 111  : 			continue;

  002ab	e9 fe fd ff ff	 jmp	 $LN2@Update
$LN7@Update:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 287  :         _Tree_iterator _Tmp = *this;

  002b0	48 8b 44 24 28	 mov	 rax, QWORD PTR itr$[rsp]
  002b5	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR _Tmp$20[rsp], rax

; 198  :         _Mybase::operator++();

  002bd	48 8d 4c 24 28	 lea	 rcx, QWORD PTR itr$[rsp]
  002c2	e8 00 00 00 00	 call	 ??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ ; std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >,std::_Iterator_base0>::operator++
  002c7	90		 npad	 1

; 289  :         return _Tmp;

  002c8	48 8b 84 24 e8
	00 00 00	 mov	 rax, QWORD PTR _Tmp$20[rsp]
  002d0	48 89 84 24 08
	01 00 00	 mov	 QWORD PTR $T24[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.cpp

; 115  : 	}

  002d8	e9 d1 fd ff ff	 jmp	 $LN2@Update
$LN3@Update:

; 116  : 
; 117  : 	nextUpdateIndex++;

  002dd	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  002e5	0f b6 80 a8 00
	00 00		 movzx	 eax, BYTE PTR [rax+168]
  002ec	fe c0		 inc	 al
  002ee	48 8b 8c 24 20
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  002f6	88 81 a8 00 00
	00		 mov	 BYTE PTR [rcx+168], al

; 118  : 	if (nextUpdateIndex >= BUCKET_SIZE)

  002fc	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00304	0f b6 80 a8 00
	00 00		 movzx	 eax, BYTE PTR [rax+168]
  0030b	83 f8 0a	 cmp	 eax, 10
  0030e	7c 0f		 jl	 SHORT $LN1@Update

; 119  : 	{
; 120  : 		nextUpdateIndex = 0;

  00310	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00318	c6 80 a8 00 00
	00 00		 mov	 BYTE PTR [rax+168], 0
$LN1@Update:

; 121  : 	}
; 122  : }

  0031f	48 81 c4 18 01
	00 00		 add	 rsp, 280		; 00000118H
  00326	c3		 ret	 0
?Update@MissionMapJoinManager@mu2@@QEAAXXZ ENDP		; mu2::MissionMapJoinManager::Update
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\array
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.cpp
;	COMDAT ?FindMissionMapJoin@MissionMapJoinManager@mu2@@QEAA_NPEAVEntityPlayer@2@AEAUMissionMapJoinInfo@12@@Z
_TEXT	SEGMENT
$T1 = 96
tv237 = 100
mapInfo$ = 104
itr$ = 112
index$ = 120
$T2 = 124
__that$ = 128
hConsole$3 = 136
exec$ = 144
this$ = 152
__that$ = 160
$T4 = 168
$T5 = 176
__param0$ = 184
$T6 = 192
$T7 = 200
_Scary$8 = 208
__param0$ = 216
$T9 = 224
$T10 = 232
$T11 = 240
$T12 = 248
$T13 = 256
$T14 = 264
$T15 = 272
$T16 = 280
$T17 = 288
$T18 = 296
$T19 = 304
$T20 = 312
$T21 = 320
$T22 = 328
$T23 = 336
this$ = 368
player$ = 376
info$ = 384
?FindMissionMapJoin@MissionMapJoinManager@mu2@@QEAA_NPEAVEntityPlayer@2@AEAUMissionMapJoinInfo@12@@Z PROC ; mu2::MissionMapJoinManager::FindMissionMapJoin, COMDAT

; 31   : {

$LN347:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 81 ec 68 01
	00 00		 sub	 rsp, 360		; 00000168H

; 32   : 	VERIFY_RETURN(player, false);

  00016	48 83 bc 24 78
	01 00 00 00	 cmp	 QWORD PTR player$[rsp], 0
  0001f	0f 85 a9 00 00
	00		 jne	 $LN2@FindMissio
  00025	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  0002a	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  00030	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR hConsole$3[rsp], rax
  00038	66 ba 0d 00	 mov	 dx, 13
  0003c	48 8b 8c 24 88
	00 00 00	 mov	 rcx, QWORD PTR hConsole$3[rsp]
  00044	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  0004a	c7 44 24 50 20
	00 00 00	 mov	 DWORD PTR [rsp+80], 32	; 00000020H
  00052	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FE@MIEGOCEM@F?3?2Release_Branch?2Server?2Develo@
  00059	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  0005e	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_06BALNJMNP@player@
  00065	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  0006a	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CP@OEPIMKPF@mu2?3?3MissionMapJoinManager?3?3Fin@
  00071	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  00076	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  0007d	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  00082	c7 44 24 28 20
	00 00 00	 mov	 DWORD PTR [rsp+40], 32	; 00000020H
  0008a	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FE@MIEGOCEM@F?3?2Release_Branch?2Server?2Develo@
  00091	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  00096	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CP@OEPIMKPF@mu2?3?3MissionMapJoinManager?3?3Fin@
  0009d	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  000a3	ba 02 00 00 00	 mov	 edx, 2
  000a8	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  000af	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  000b4	66 ba 07 00	 mov	 dx, 7
  000b8	48 8b 8c 24 88
	00 00 00	 mov	 rcx, QWORD PTR hConsole$3[rsp]
  000c0	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000c6	90		 npad	 1
  000c7	32 c0		 xor	 al, al
  000c9	e9 e8 02 00 00	 jmp	 $LN1@FindMissio
$LN2@FindMissio:

; 33   : 
; 34   : 	UInt32 index = player->GetCharId() % BUCKET_SIZE;

  000ce	48 8b 8c 24 78
	01 00 00	 mov	 rcx, QWORD PTR player$[rsp]
  000d6	e8 00 00 00 00	 call	 ?GetCharId@EntityPlayer@mu2@@QEBAIXZ ; mu2::EntityPlayer::GetCharId
  000db	33 d2		 xor	 edx, edx
  000dd	b9 0a 00 00 00	 mov	 ecx, 10
  000e2	f7 f1		 div	 ecx
  000e4	8b c2		 mov	 eax, edx
  000e6	89 44 24 78	 mov	 DWORD PTR index$[rsp], eax

; 36   : 	MapMissionMapJoinInfo& mapInfo = m_arrayMissionMapJoinInfo[index];

  000ea	8b 44 24 78	 mov	 eax, DWORD PTR index$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\array

; 534  :         return _Elems[_Pos];

  000ee	48 6b c0 10	 imul	 rax, rax, 16
  000f2	48 8b 8c 24 70
	01 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  000fa	48 8d 44 01 08	 lea	 rax, QWORD PTR [rcx+rax+8]
  000ff	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T4[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.cpp

; 36   : 	MapMissionMapJoinInfo& mapInfo = m_arrayMissionMapJoinInfo[index];

  00107	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T4[rsp]
  0010f	48 89 44 24 68	 mov	 QWORD PTR mapInfo$[rsp], rax

; 37   : 
; 38   : 	auto itr = mapInfo.find(player->GetCharId());

  00114	48 8b 8c 24 78
	01 00 00	 mov	 rcx, QWORD PTR player$[rsp]
  0011c	e8 00 00 00 00	 call	 ?GetCharId@EntityPlayer@mu2@@QEBAIXZ ; mu2::EntityPlayer::GetCharId
  00121	89 44 24 7c	 mov	 DWORD PTR $T2[rsp], eax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00125	48 8b 44 24 68	 mov	 rax, QWORD PTR mapInfo$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0012a	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00132	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR $T5[rsp]
  0013a	48 89 84 24 48
	01 00 00	 mov	 QWORD PTR $T22[rsp], rax

; 1394 :         return iterator(_Find(_Keyval), _Get_scary());

  00142	48 8d 54 24 7c	 lea	 rdx, QWORD PTR $T2[rsp]
  00147	48 8b 4c 24 68	 mov	 rcx, QWORD PTR mapInfo$[rsp]
  0014c	e8 00 00 00 00	 call	 ??$_Find@I@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@AEBAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@1@AEBI@Z ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::_Find<unsigned int>
  00151	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR __param0$[rsp], rax

; 37   :     _Tree_unchecked_const_iterator(_Nodeptr _Pnode, const _Mytree* _Plist) noexcept : _Ptr(_Pnode) {

  00159	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR __param0$[rsp]
  00161	48 89 44 24 70	 mov	 QWORD PTR itr$[rsp], rax

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00166	48 8b 44 24 68	 mov	 rax, QWORD PTR mapInfo$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0016b	48 89 84 24 c0
	00 00 00	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00173	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR $T6[rsp]
  0017b	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T7[rsp], rax

; 1151 :         const auto _Scary = _Get_scary();

  00183	48 8b 84 24 c8
	00 00 00	 mov	 rax, QWORD PTR $T7[rsp]
  0018b	48 89 84 24 d0
	00 00 00	 mov	 QWORD PTR _Scary$8[rsp], rax

; 1152 :         return iterator(_Scary->_Myhead, _Scary);

  00193	48 8b 84 24 d0
	00 00 00	 mov	 rax, QWORD PTR _Scary$8[rsp]
  0019b	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0019e	48 89 84 24 d8
	00 00 00	 mov	 QWORD PTR __param0$[rsp], rax

; 37   :     _Tree_unchecked_const_iterator(_Nodeptr _Pnode, const _Mytree* _Plist) noexcept : _Ptr(_Pnode) {

  001a6	48 8b 84 24 d8
	00 00 00	 mov	 rax, QWORD PTR __param0$[rsp]
  001ae	48 89 84 24 e0
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax

; 1152 :         return iterator(_Scary->_Myhead, _Scary);

  001b6	48 8d 84 24 e0
	00 00 00	 lea	 rax, QWORD PTR $T9[rsp]
  001be	48 89 84 24 e8
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax

; 232  :         return this->_Ptr == _Right._Ptr;

  001c6	48 8b 84 24 e8
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  001ce	48 8b 00	 mov	 rax, QWORD PTR [rax]
  001d1	48 39 44 24 70	 cmp	 QWORD PTR itr$[rsp], rax
  001d6	75 0a		 jne	 SHORT $LN116@FindMissio
  001d8	c7 44 24 64 01
	00 00 00	 mov	 DWORD PTR tv237[rsp], 1
  001e0	eb 08		 jmp	 SHORT $LN117@FindMissio
$LN116@FindMissio:
  001e2	c7 44 24 64 00
	00 00 00	 mov	 DWORD PTR tv237[rsp], 0
$LN117@FindMissio:
  001ea	0f b6 44 24 64	 movzx	 eax, BYTE PTR tv237[rsp]
  001ef	88 44 24 60	 mov	 BYTE PTR $T1[rsp], al
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.cpp

; 39   : 	if (itr == mapInfo.end())

  001f3	0f b6 44 24 60	 movzx	 eax, BYTE PTR $T1[rsp]
  001f8	0f b6 c0	 movzx	 eax, al
  001fb	85 c0		 test	 eax, eax
  001fd	74 07		 je	 SHORT $LN3@FindMissio

; 40   : 	{
; 41   : 		return false;

  001ff	32 c0		 xor	 al, al
  00201	e9 b0 01 00 00	 jmp	 $LN1@FindMissio
$LN3@FindMissio:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 185  :         return this->_Ptr->_Myval;

  00206	48 8b 44 24 70	 mov	 rax, QWORD PTR itr$[rsp]
  0020b	48 83 c0 20	 add	 rax, 32			; 00000020H
  0020f	48 89 84 24 f0
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax

; 274  :         return const_cast<reference>(_Mybase::operator*());

  00217	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  0021f	48 89 84 24 f8
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax

; 278  :         return pointer_traits<pointer>::pointer_to(**this);

  00227	48 8b 84 24 f8
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0022f	48 89 84 24 00
	01 00 00	 mov	 QWORD PTR $T13[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 528  :         return _STD addressof(_Val);

  00237	48 8b 84 24 00
	01 00 00	 mov	 rax, QWORD PTR $T13[rsp]
  0023f	48 89 84 24 08
	01 00 00	 mov	 QWORD PTR $T14[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 278  :         return pointer_traits<pointer>::pointer_to(**this);

  00247	48 8b 84 24 08
	01 00 00	 mov	 rax, QWORD PTR $T14[rsp]
  0024f	48 89 84 24 10
	01 00 00	 mov	 QWORD PTR $T15[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 74   : 		return inst;

  00257	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?inst@?$ISingleton@VExecutionManager@mu2@@@mu2@@1VExecutionManager@2@A ; mu2::ISingleton<mu2::ExecutionManager>::inst
  0025e	48 89 84 24 18
	01 00 00	 mov	 QWORD PTR $T16[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.cpp

; 44   : 	const Execution& exec = theExecutionManager.GetExecution(itr->second.execId);

  00266	48 8b 84 24 10
	01 00 00	 mov	 rax, QWORD PTR $T15[rsp]
  0026e	48 83 c0 10	 add	 rax, 16
  00272	48 8b 8c 24 18
	01 00 00	 mov	 rcx, QWORD PTR $T16[rsp]
  0027a	48 8b d0	 mov	 rdx, rax
  0027d	e8 00 00 00 00	 call	 ?GetExecution@ExecutionManager@mu2@@QEAAAEBVExecution@2@AEBVExecutionZoneId@2@@Z ; mu2::ExecutionManager::GetExecution
  00282	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR exec$[rsp], rax

; 45   : 	if (false == exec.IsJoinable(player))

  0028a	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR exec$[rsp]
  00292	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00295	48 8b 94 24 78
	01 00 00	 mov	 rdx, QWORD PTR player$[rsp]
  0029d	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR exec$[rsp]
  002a5	ff 90 c0 00 00
	00		 call	 QWORD PTR [rax+192]
  002ab	0f b6 c0	 movzx	 eax, al
  002ae	85 c0		 test	 eax, eax
  002b0	75 1f		 jne	 SHORT $LN4@FindMissio

; 46   : 	{
; 47   : 		mapInfo.erase(itr);

  002b2	4c 8b 44 24 70	 mov	 r8, QWORD PTR itr$[rsp]
  002b7	48 8d 94 24 50
	01 00 00	 lea	 rdx, QWORD PTR $T23[rsp]
  002bf	48 8b 4c 24 68	 mov	 rcx, QWORD PTR mapInfo$[rsp]
  002c4	e8 00 00 00 00	 call	 ??$erase@V?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@@std@@$0A@@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@QEAA?AV?$_Tree_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@@1@V21@@Z ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::erase<std::_Tree_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > > >,0>
  002c9	90		 npad	 1

; 48   : 		return false;

  002ca	32 c0		 xor	 al, al
  002cc	e9 e5 00 00 00	 jmp	 $LN1@FindMissio
$LN4@FindMissio:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 185  :         return this->_Ptr->_Myval;

  002d1	48 8b 44 24 70	 mov	 rax, QWORD PTR itr$[rsp]
  002d6	48 83 c0 20	 add	 rax, 32			; 00000020H
  002da	48 89 84 24 20
	01 00 00	 mov	 QWORD PTR $T17[rsp], rax

; 274  :         return const_cast<reference>(_Mybase::operator*());

  002e2	48 8b 84 24 20
	01 00 00	 mov	 rax, QWORD PTR $T17[rsp]
  002ea	48 89 84 24 28
	01 00 00	 mov	 QWORD PTR $T18[rsp], rax

; 278  :         return pointer_traits<pointer>::pointer_to(**this);

  002f2	48 8b 84 24 28
	01 00 00	 mov	 rax, QWORD PTR $T18[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  002fa	48 89 84 24 30
	01 00 00	 mov	 QWORD PTR $T19[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 528  :         return _STD addressof(_Val);

  00302	48 8b 84 24 30
	01 00 00	 mov	 rax, QWORD PTR $T19[rsp]
  0030a	48 89 84 24 38
	01 00 00	 mov	 QWORD PTR $T20[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 278  :         return pointer_traits<pointer>::pointer_to(**this);

  00312	48 8b 84 24 38
	01 00 00	 mov	 rax, QWORD PTR $T20[rsp]
  0031a	48 89 84 24 40
	01 00 00	 mov	 QWORD PTR $T21[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.cpp

; 51   : 	info = itr->second;

  00322	48 8b 84 24 40
	01 00 00	 mov	 rax, QWORD PTR $T21[rsp]
  0032a	48 83 c0 08	 add	 rax, 8
  0032e	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR __that$[rsp], rax
  00336	48 8b 84 24 80
	01 00 00	 mov	 rax, QWORD PTR info$[rsp]
  0033e	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR __that$[rsp]
  00346	8b 09		 mov	 ecx, DWORD PTR [rcx]
  00348	89 08		 mov	 DWORD PTR [rax], ecx
  0034a	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR __that$[rsp]
  00352	48 83 c0 08	 add	 rax, 8
  00356	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR __that$[rsp], rax
  0035e	48 8b 84 24 80
	01 00 00	 mov	 rax, QWORD PTR info$[rsp]
  00366	48 83 c0 08	 add	 rax, 8
  0036a	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR this$[rsp], rax
  00372	48 8b 84 24 a0
	00 00 00	 mov	 rax, QWORD PTR __that$[rsp]
  0037a	8b 40 08	 mov	 eax, DWORD PTR [rax+8]
  0037d	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00385	89 41 08	 mov	 DWORD PTR [rcx+8], eax
  00388	48 8b 84 24 98
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00390	48 8b 8c 24 a0
	00 00 00	 mov	 rcx, QWORD PTR __that$[rsp]
  00398	8b 49 08	 mov	 ecx, DWORD PTR [rcx+8]
  0039b	89 48 08	 mov	 DWORD PTR [rax+8], ecx
  0039e	48 8b 84 24 80
	01 00 00	 mov	 rax, QWORD PTR info$[rsp]
  003a6	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR __that$[rsp]
  003ae	8b 49 18	 mov	 ecx, DWORD PTR [rcx+24]
  003b1	89 48 18	 mov	 DWORD PTR [rax+24], ecx

; 52   : 	
; 53   : 	return true;

  003b4	b0 01		 mov	 al, 1
$LN1@FindMissio:

; 54   : }

  003b6	48 81 c4 68 01
	00 00		 add	 rsp, 360		; 00000168H
  003bd	c3		 ret	 0
?FindMissionMapJoin@MissionMapJoinManager@mu2@@QEAA_NPEAVEntityPlayer@2@AEAUMissionMapJoinInfo@12@@Z ENDP ; mu2::MissionMapJoinManager::FindMissionMapJoin
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\array
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.cpp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.cpp
;	COMDAT ?AddMissionMapJoin@MissionMapJoinManager@mu2@@QEAAXIAEBVExecutionZoneId@2@H@Z
_TEXT	SEGMENT
index$ = 96
this$ = 104
hConsole$1 = 112
this$ = 120
__that$ = 128
info$ = 136
$T2 = 168
$T3 = 176
$T4 = 184
this$ = 224
charId$ = 232
execId$ = 240
startingPositionIndex$ = 248
?AddMissionMapJoin@MissionMapJoinManager@mu2@@QEAAXIAEBVExecutionZoneId@2@H@Z PROC ; mu2::MissionMapJoinManager::AddMissionMapJoin, COMDAT

; 18   : {

$LN181:
  00000	44 89 4c 24 20	 mov	 DWORD PTR [rsp+32], r9d
  00005	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  0000a	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  0000e	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00013	48 81 ec d8 00
	00 00		 sub	 rsp, 216		; 000000d8H

; 19   : 	VERIFY_RETURN(charId && execId.IsValidStong() && startingPositionIndex, );

  0001a	83 bc 24 e8 00
	00 00 00	 cmp	 DWORD PTR charId$[rsp], 0
  00022	74 22		 je	 SHORT $LN3@AddMission
  00024	48 8b 8c 24 f0
	00 00 00	 mov	 rcx, QWORD PTR execId$[rsp]
  0002c	e8 00 00 00 00	 call	 ?IsValidStong@ExecutionZoneId@mu2@@QEBA?B_NXZ ; mu2::ExecutionZoneId::IsValidStong
  00031	0f b6 c0	 movzx	 eax, al
  00034	85 c0		 test	 eax, eax
  00036	74 0e		 je	 SHORT $LN3@AddMission
  00038	83 bc 24 f8 00
	00 00 00	 cmp	 DWORD PTR startingPositionIndex$[rsp], 0
  00040	0f 85 9e 00 00
	00		 jne	 $LN2@AddMission
$LN3@AddMission:
  00046	b9 f5 ff ff ff	 mov	 ecx, -11		; fffffff5H
  0004b	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetStdHandle
  00051	48 89 44 24 70	 mov	 QWORD PTR hConsole$1[rsp], rax
  00056	66 ba 0d 00	 mov	 dx, 13
  0005a	48 8b 4c 24 70	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  0005f	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  00065	c7 44 24 50 13
	00 00 00	 mov	 DWORD PTR [rsp+80], 19
  0006d	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FE@MIEGOCEM@F?3?2Release_Branch?2Server?2Develo@
  00074	48 89 44 24 48	 mov	 QWORD PTR [rsp+72], rax
  00079	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0DJ@CHGGIMOF@charId?5?$CG?$CG?5execId?4IsValidStong?$CI?$CJ@
  00080	48 89 44 24 40	 mov	 QWORD PTR [rsp+64], rax
  00085	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0CO@CJOILFDB@mu2?3?3MissionMapJoinManager?3?3Add@
  0008c	48 89 44 24 38	 mov	 QWORD PTR [rsp+56], rax
  00091	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BI@FNEJONPP@?$CFs?$DO?5ASSERT?5?9?5?$CFs?0?5?$CFs?$CI?$CFd?$CJ@
  00098	48 89 44 24 30	 mov	 QWORD PTR [rsp+48], rax
  0009d	c7 44 24 28 13
	00 00 00	 mov	 DWORD PTR [rsp+40], 19
  000a5	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0FE@MIEGOCEM@F?3?2Release_Branch?2Server?2Develo@
  000ac	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  000b1	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_0CO@CJOILFDB@mu2?3?3MissionMapJoinManager?3?3Add@
  000b8	41 b8 40 9c 00
	00		 mov	 r8d, 40000		; 00009c40H
  000be	ba 02 00 00 00	 mov	 edx, 2
  000c3	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_19ILJNDDBA@?$AAg?$AAa?$AAm?$AAe@
  000ca	e8 00 00 00 00	 call	 ?Logging@Logger@mu2@@SAXPEB_WEHPEBD1H1ZZ ; mu2::Logger::Logging
  000cf	66 ba 07 00	 mov	 dx, 7
  000d3	48 8b 4c 24 70	 mov	 rcx, QWORD PTR hConsole$1[rsp]
  000d8	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_SetConsoleTextAttribute
  000de	90		 npad	 1
  000df	e9 34 01 00 00	 jmp	 $LN1@AddMission
$LN2@AddMission:

; 20   : 	UInt32 index = charId % BUCKET_SIZE;

  000e4	33 d2		 xor	 edx, edx
  000e6	8b 84 24 e8 00
	00 00		 mov	 eax, DWORD PTR charId$[rsp]
  000ed	b9 0a 00 00 00	 mov	 ecx, 10
  000f2	f7 f1		 div	 ecx
  000f4	8b c2		 mov	 eax, edx
  000f6	89 44 24 60	 mov	 DWORD PTR index$[rsp], eax
  000fa	48 8d 84 24 90
	00 00 00	 lea	 rax, QWORD PTR info$[rsp+8]
  00102	48 8b c8	 mov	 rcx, rax
  00105	e8 00 00 00 00	 call	 ??0ExecutionZoneId@mu2@@QEAA@XZ ; mu2::ExecutionZoneId::ExecutionZoneId
  0010a	90		 npad	 1
  0010b	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR execId$[rsp]
  00113	8b 40 08	 mov	 eax, DWORD PTR [rax+8]
  00116	89 84 24 98 00
	00 00		 mov	 DWORD PTR info$[rsp+16], eax
  0011d	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR execId$[rsp]
  00125	8b 40 08	 mov	 eax, DWORD PTR [rax+8]
  00128	89 84 24 98 00
	00 00		 mov	 DWORD PTR info$[rsp+16], eax

; 21   : 
; 22   : 	MissionMapJoinInfo info;
; 23   : 	info.execId = execId;
; 24   : 	info.expireTick = GetTickCount() + DURATION_TIME;;

  0012f	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetTickCount
  00135	05 e0 93 04 00	 add	 eax, 300000		; 000493e0H
  0013a	89 84 24 88 00
	00 00		 mov	 DWORD PTR info$[rsp], eax

; 25   : 	info.startingPositionIndex = startingPositionIndex;

  00141	8b 84 24 f8 00
	00 00		 mov	 eax, DWORD PTR startingPositionIndex$[rsp]
  00148	89 84 24 a0 00
	00 00		 mov	 DWORD PTR info$[rsp+24], eax

; 27   : 	m_arrayMissionMapJoinInfo[index][charId] = info;

  0014f	8b 44 24 60	 mov	 eax, DWORD PTR index$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\array

; 534  :         return _Elems[_Pos];

  00153	48 6b c0 10	 imul	 rax, rax, 16
  00157	48 8b 8c 24 e0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0015f	48 8d 44 01 08	 lea	 rax, QWORD PTR [rcx+rax+8]
  00164	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR $T2[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.cpp

; 27   : 	m_arrayMissionMapJoinInfo[index][charId] = info;

  0016c	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR $T2[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map

; 346  :         return _Try_emplace(_Keyval).first->_Myval.second;

  00174	4c 8d 84 24 e8
	00 00 00	 lea	 r8, QWORD PTR charId$[rsp]
  0017c	48 8d 94 24 b8
	00 00 00	 lea	 rdx, QWORD PTR $T4[rsp]
  00184	48 8b c8	 mov	 rcx, rax
  00187	e8 00 00 00 00	 call	 ??$_Try_emplace@AEBI$$V@?$map@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@@std@@AEAA?AU?$pair@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@_N@1@AEBI@Z ; std::map<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::_Try_emplace<unsigned int const &>
  0018c	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0018f	48 83 c0 28	 add	 rax, 40			; 00000028H
  00193	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T3[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.cpp

; 27   : 	m_arrayMissionMapJoinInfo[index][charId] = info;

  0019b	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR $T3[rsp]
  001a3	48 89 44 24 68	 mov	 QWORD PTR this$[rsp], rax
  001a8	48 8b 44 24 68	 mov	 rax, QWORD PTR this$[rsp]
  001ad	8b 8c 24 88 00
	00 00		 mov	 ecx, DWORD PTR info$[rsp]
  001b4	89 08		 mov	 DWORD PTR [rax], ecx
  001b6	48 8d 84 24 90
	00 00 00	 lea	 rax, QWORD PTR info$[rsp+8]
  001be	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR __that$[rsp], rax
  001c6	48 8b 44 24 68	 mov	 rax, QWORD PTR this$[rsp]
  001cb	48 83 c0 08	 add	 rax, 8
  001cf	48 89 44 24 78	 mov	 QWORD PTR this$[rsp], rax
  001d4	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR __that$[rsp]
  001dc	8b 40 08	 mov	 eax, DWORD PTR [rax+8]
  001df	48 8b 4c 24 78	 mov	 rcx, QWORD PTR this$[rsp]
  001e4	89 41 08	 mov	 DWORD PTR [rcx+8], eax
  001e7	48 8b 44 24 78	 mov	 rax, QWORD PTR this$[rsp]
  001ec	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR __that$[rsp]
  001f4	8b 49 08	 mov	 ecx, DWORD PTR [rcx+8]
  001f7	89 48 08	 mov	 DWORD PTR [rax+8], ecx
  001fa	48 8b 44 24 68	 mov	 rax, QWORD PTR this$[rsp]
  001ff	8b 8c 24 a0 00
	00 00		 mov	 ecx, DWORD PTR info$[rsp+24]
  00206	89 48 18	 mov	 DWORD PTR [rax+24], ecx
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h

; 172  : 	virtual~ISerializer() {}

  00209	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  00210	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR info$[rsp+8], rax
$LN1@AddMission:
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.cpp

; 28   : }

  00218	48 81 c4 d8 00
	00 00		 add	 rsp, 216		; 000000d8H
  0021f	c3		 ret	 0
?AddMissionMapJoin@MissionMapJoinManager@mu2@@QEAAXIAEBVExecutionZoneId@2@H@Z ENDP ; mu2::MissionMapJoinManager::AddMissionMapJoin
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
index$ = 96
this$ = 104
hConsole$1 = 112
this$ = 120
__that$ = 128
info$ = 136
$T2 = 168
$T3 = 176
$T4 = 184
this$ = 224
charId$ = 232
execId$ = 240
startingPositionIndex$ = 248
?dtor$0@?0??AddMissionMapJoin@MissionMapJoinManager@mu2@@QEAAXIAEBVExecutionZoneId@2@H@Z@4HA PROC ; `mu2::MissionMapJoinManager::AddMissionMapJoin'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8d 8d 88 00
	00 00		 lea	 rcx, QWORD PTR info$[rbp]
  00010	e8 00 00 00 00	 call	 ??1MissionMapJoinInfo@MissionMapJoinManager@mu2@@QEAA@XZ
  00015	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00019	5d		 pop	 rbp
  0001a	c3		 ret	 0
?dtor$0@?0??AddMissionMapJoin@MissionMapJoinManager@mu2@@QEAAXIAEBVExecutionZoneId@2@H@Z@4HA ENDP ; `mu2::MissionMapJoinManager::AddMissionMapJoin'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.h
;	COMDAT ?UnInit@MissionMapJoinManager@mu2@@UEAA_NXZ
_TEXT	SEGMENT
this$ = 8
?UnInit@MissionMapJoinManager@mu2@@UEAA_NXZ PROC	; mu2::MissionMapJoinManager::UnInit, COMDAT

; 34   : 	virtual Bool UnInit() override { return true; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	b0 01		 mov	 al, 1
  00007	c3		 ret	 0
?UnInit@MissionMapJoinManager@mu2@@UEAA_NXZ ENDP	; mu2::MissionMapJoinManager::UnInit
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.h
;	COMDAT ?Init@MissionMapJoinManager@mu2@@UEAA_NPEAX@Z
_TEXT	SEGMENT
this$ = 8
param$ = 16
?Init@MissionMapJoinManager@mu2@@UEAA_NPEAX@Z PROC	; mu2::MissionMapJoinManager::Init, COMDAT

; 33   : 	virtual Bool Init(void*param = NULL) override { UNREFERENCED_PARAMETER(param); return true; }

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	b0 01		 mov	 al, 1
  0000c	c3		 ret	 0
?Init@MissionMapJoinManager@mu2@@UEAA_NPEAX@Z ENDP	; mu2::MissionMapJoinManager::Init
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.cpp
;	COMDAT ??1MissionMapJoinManager@mu2@@UEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1MissionMapJoinManager@mu2@@UEAA@XZ PROC		; mu2::MissionMapJoinManager::~MissionMapJoinManager, COMDAT

; 14   : {

$LN14:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7MissionMapJoinManager@mu2@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx
  00018	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 83 c0 08	 add	 rax, 8
  00021	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??1?$map@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@@std@@QEAA@XZ
  00028	41 b8 0a 00 00
	00		 mov	 r8d, 10
  0002e	ba 10 00 00 00	 mov	 edx, 16
  00033	48 8b c8	 mov	 rcx, rax
  00036	e8 00 00 00 00	 call	 ??_M@YAXPEAX_K1P6AX0@Z@Z
  0003b	90		 npad	 1
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 80   : 	virtual~ISingleton() {}

  0003c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00041	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@6B@
  00048	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.cpp

; 15   : }

  0004b	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0004f	c3		 ret	 0
??1MissionMapJoinManager@mu2@@UEAA@XZ ENDP		; mu2::MissionMapJoinManager::~MissionMapJoinManager
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??1?$map@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1?$map@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@@std@@QEAA@XZ PROC ; std::map<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::~map<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >, COMDAT
$LN159:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00009	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0000e	e8 00 00 00 00	 call	 ??1?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@QEAA@XZ ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::~_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >
  00013	90		 npad	 1
  00014	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00018	c3		 ret	 0
??1?$map@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@@std@@QEAA@XZ ENDP ; std::map<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::~map<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map
;	COMDAT ??0?$map@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@@std@@QEAA@XZ
_TEXT	SEGMENT
$T1 = 32
__formal$ = 40
this$ = 48
$T2 = 56
this$ = 64
this$ = 72
this$ = 96
??0?$map@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@@std@@QEAA@XZ PROC ; std::map<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::map<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >, COMDAT

; 106  :     map() : _Mybase(key_compare()) {}

$LN142:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 58	 sub	 rsp, 88			; 00000058H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 904  :     _Tree(const key_compare& _Parg) : _Mypair(_One_then_variadic_args_t{}, _Parg, _Zero_then_variadic_args_t{}) {

  00009	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 89 44 24 40	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00013	48 8d 44 24 20	 lea	 rax, QWORD PTR $T1[rsp]
  00018	48 89 44 24 38	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1536 :         : _Ty1(_STD forward<_Other1>(_Val1)), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  0001d	48 8b 44 24 38	 mov	 rax, QWORD PTR $T2[rsp]
  00022	0f b6 00	 movzx	 eax, BYTE PTR [rax]
  00025	88 44 24 28	 mov	 BYTE PTR __formal$[rsp], al
  00029	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0002e	48 89 44 24 48	 mov	 QWORD PTR this$[rsp], rax

; 1531 :         : _Ty1(), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  00033	48 8b 44 24 48	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 89 44 24 30	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 450  :     _Tree_val() noexcept : _Myhead(), _Mysize(0) {}

  0003d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00042	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0

; 905  :         _Alloc_sentinel_and_proxy();

  00056	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  0005b	e8 00 00 00 00	 call	 ?_Alloc_sentinel_and_proxy@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@IEAAXXZ ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::_Alloc_sentinel_and_proxy
  00060	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\map

; 106  :     map() : _Mybase(key_compare()) {}

  00061	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00066	48 83 c4 58	 add	 rsp, 88			; 00000058H
  0006a	c3		 ret	 0
??0?$map@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@@std@@QEAA@XZ ENDP ; std::map<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::map<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ?_Insert_node@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@U?$_Tree_id@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@2@QEAU32@@Z
_TEXT	SEGMENT
_Pnode$1 = 32
_Head$ = 40
_Parent_sibling$2 = 48
_Parent_sibling$3 = 56
this$ = 80
_Loc$ = 88
_Newnode$ = 96
?_Insert_node@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@U?$_Tree_id@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@2@QEAU32@@Z PROC ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::_Insert_node, COMDAT

; 669  :     _Nodeptr _Insert_node(const _Tree_id<_Nodeptr> _Loc, const _Nodeptr _Newnode) noexcept {

$LN60:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 670  :         ++_Mysize;

  00013	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00018	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0001c	48 ff c0	 inc	 rax
  0001f	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  00024	48 89 41 08	 mov	 QWORD PTR [rcx+8], rax

; 671  :         const auto _Head  = _Myhead;

  00028	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0002d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00030	48 89 44 24 28	 mov	 QWORD PTR _Head$[rsp], rax

; 672  :         _Newnode->_Parent = _Loc._Parent;

  00035	48 8b 44 24 60	 mov	 rax, QWORD PTR _Newnode$[rsp]
  0003a	48 8b 4c 24 58	 mov	 rcx, QWORD PTR _Loc$[rsp]
  0003f	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00042	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 673  :         if (_Loc._Parent == _Head) { // first node in tree, just set head values

  00046	48 8b 44 24 58	 mov	 rax, QWORD PTR _Loc$[rsp]
  0004b	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Head$[rsp]
  00050	48 39 08	 cmp	 QWORD PTR [rax], rcx
  00053	75 3c		 jne	 SHORT $LN5@Insert_nod

; 674  :             _Head->_Left     = _Newnode;

  00055	48 8b 44 24 28	 mov	 rax, QWORD PTR _Head$[rsp]
  0005a	48 8b 4c 24 60	 mov	 rcx, QWORD PTR _Newnode$[rsp]
  0005f	48 89 08	 mov	 QWORD PTR [rax], rcx

; 675  :             _Head->_Parent   = _Newnode;

  00062	48 8b 44 24 28	 mov	 rax, QWORD PTR _Head$[rsp]
  00067	48 8b 4c 24 60	 mov	 rcx, QWORD PTR _Newnode$[rsp]
  0006c	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 676  :             _Head->_Right    = _Newnode;

  00070	48 8b 44 24 28	 mov	 rax, QWORD PTR _Head$[rsp]
  00075	48 8b 4c 24 60	 mov	 rcx, QWORD PTR _Newnode$[rsp]
  0007a	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 677  :             _Newnode->_Color = _Black; // the root is black

  0007e	48 8b 44 24 60	 mov	 rax, QWORD PTR _Newnode$[rsp]
  00083	c6 40 18 01	 mov	 BYTE PTR [rax+24], 1

; 678  :             return _Newnode;

  00087	48 8b 44 24 60	 mov	 rax, QWORD PTR _Newnode$[rsp]
  0008c	e9 52 02 00 00	 jmp	 $LN1@Insert_nod
$LN5@Insert_nod:

; 679  :         }
; 680  : 
; 681  :         _STL_INTERNAL_CHECK(_Loc._Child != _Tree_child::_Unused);
; 682  :         if (_Loc._Child == _Tree_child::_Right) { // add to right of _Loc._Parent

  00091	48 8b 44 24 58	 mov	 rax, QWORD PTR _Loc$[rsp]
  00096	83 78 08 00	 cmp	 DWORD PTR [rax+8], 0
  0009a	75 34		 jne	 SHORT $LN6@Insert_nod

; 683  :             _STL_INTERNAL_CHECK(_Loc._Parent->_Right->_Isnil);
; 684  :             _Loc._Parent->_Right = _Newnode;

  0009c	48 8b 44 24 58	 mov	 rax, QWORD PTR _Loc$[rsp]
  000a1	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000a4	48 8b 4c 24 60	 mov	 rcx, QWORD PTR _Newnode$[rsp]
  000a9	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 685  :             if (_Loc._Parent == _Head->_Right) { // remember rightmost node

  000ad	48 8b 44 24 58	 mov	 rax, QWORD PTR _Loc$[rsp]
  000b2	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Head$[rsp]
  000b7	48 8b 49 10	 mov	 rcx, QWORD PTR [rcx+16]
  000bb	48 39 08	 cmp	 QWORD PTR [rax], rcx
  000be	75 0e		 jne	 SHORT $LN8@Insert_nod

; 686  :                 _Head->_Right = _Newnode;

  000c0	48 8b 44 24 28	 mov	 rax, QWORD PTR _Head$[rsp]
  000c5	48 8b 4c 24 60	 mov	 rcx, QWORD PTR _Newnode$[rsp]
  000ca	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx
$LN8@Insert_nod:

; 687  :             }
; 688  :         } else { // add to left of _Loc._Parent

  000ce	eb 2f		 jmp	 SHORT $LN7@Insert_nod
$LN6@Insert_nod:

; 689  :             _STL_INTERNAL_CHECK(_Loc._Parent->_Left->_Isnil);
; 690  :             _Loc._Parent->_Left = _Newnode;

  000d0	48 8b 44 24 58	 mov	 rax, QWORD PTR _Loc$[rsp]
  000d5	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000d8	48 8b 4c 24 60	 mov	 rcx, QWORD PTR _Newnode$[rsp]
  000dd	48 89 08	 mov	 QWORD PTR [rax], rcx

; 691  :             if (_Loc._Parent == _Head->_Left) { // remember leftmost node

  000e0	48 8b 44 24 58	 mov	 rax, QWORD PTR _Loc$[rsp]
  000e5	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Head$[rsp]
  000ea	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  000ed	48 39 08	 cmp	 QWORD PTR [rax], rcx
  000f0	75 0d		 jne	 SHORT $LN7@Insert_nod

; 692  :                 _Head->_Left = _Newnode;

  000f2	48 8b 44 24 28	 mov	 rax, QWORD PTR _Head$[rsp]
  000f7	48 8b 4c 24 60	 mov	 rcx, QWORD PTR _Newnode$[rsp]
  000fc	48 89 08	 mov	 QWORD PTR [rax], rcx
$LN7@Insert_nod:

; 693  :             }
; 694  :         }
; 695  : 
; 696  :         for (_Nodeptr _Pnode = _Newnode; _Pnode->_Parent->_Color == _Red;) {

  000ff	48 8b 44 24 60	 mov	 rax, QWORD PTR _Newnode$[rsp]
  00104	48 89 44 24 20	 mov	 QWORD PTR _Pnode$1[rsp], rax
$LN2@Insert_nod:
  00109	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  0010e	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00112	0f be 40 18	 movsx	 eax, BYTE PTR [rax+24]
  00116	85 c0		 test	 eax, eax
  00118	0f 85 b3 01 00
	00		 jne	 $LN3@Insert_nod

; 697  :             if (_Pnode->_Parent == _Pnode->_Parent->_Parent->_Left) { // fixup red-red in left subtree

  0011e	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  00123	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00127	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0012b	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Pnode$1[rsp]
  00130	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00133	48 39 41 08	 cmp	 QWORD PTR [rcx+8], rax
  00137	0f 85 cb 00 00
	00		 jne	 $LN10@Insert_nod

; 698  :                 const auto _Parent_sibling = _Pnode->_Parent->_Parent->_Right;

  0013d	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  00142	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00146	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0014a	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0014e	48 89 44 24 30	 mov	 QWORD PTR _Parent_sibling$2[rsp], rax

; 699  :                 if (_Parent_sibling->_Color == _Red) { // parent's sibling has two red children, blacken both

  00153	48 8b 44 24 30	 mov	 rax, QWORD PTR _Parent_sibling$2[rsp]
  00158	0f be 40 18	 movsx	 eax, BYTE PTR [rax+24]
  0015c	85 c0		 test	 eax, eax
  0015e	75 3b		 jne	 SHORT $LN12@Insert_nod

; 700  :                     _Pnode->_Parent->_Color          = _Black;

  00160	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  00165	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00169	c6 40 18 01	 mov	 BYTE PTR [rax+24], 1

; 701  :                     _Parent_sibling->_Color          = _Black;

  0016d	48 8b 44 24 30	 mov	 rax, QWORD PTR _Parent_sibling$2[rsp]
  00172	c6 40 18 01	 mov	 BYTE PTR [rax+24], 1

; 702  :                     _Pnode->_Parent->_Parent->_Color = _Red;

  00176	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  0017b	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0017f	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00183	c6 40 18 00	 mov	 BYTE PTR [rax+24], 0

; 703  :                     _Pnode                           = _Pnode->_Parent->_Parent;

  00187	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  0018c	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00190	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00194	48 89 44 24 20	 mov	 QWORD PTR _Pnode$1[rsp], rax

; 704  :                 } else { // parent's sibling has red and black children

  00199	eb 68		 jmp	 SHORT $LN13@Insert_nod
$LN12@Insert_nod:

; 705  :                     if (_Pnode == _Pnode->_Parent->_Right) { // rotate right child to left

  0019b	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  001a0	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  001a4	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  001a8	48 39 44 24 20	 cmp	 QWORD PTR _Pnode$1[rsp], rax
  001ad	75 1e		 jne	 SHORT $LN14@Insert_nod

; 706  :                         _Pnode = _Pnode->_Parent;

  001af	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  001b4	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  001b8	48 89 44 24 20	 mov	 QWORD PTR _Pnode$1[rsp], rax

; 707  :                         _Lrotate(_Pnode);

  001bd	48 8b 54 24 20	 mov	 rdx, QWORD PTR _Pnode$1[rsp]
  001c2	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  001c7	e8 00 00 00 00	 call	 ?_Lrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::_Lrotate
  001cc	90		 npad	 1
$LN14@Insert_nod:

; 708  :                     }
; 709  : 
; 710  :                     _Pnode->_Parent->_Color          = _Black; // propagate red up

  001cd	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  001d2	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  001d6	c6 40 18 01	 mov	 BYTE PTR [rax+24], 1

; 711  :                     _Pnode->_Parent->_Parent->_Color = _Red;

  001da	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  001df	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  001e3	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  001e7	c6 40 18 00	 mov	 BYTE PTR [rax+24], 0

; 712  :                     _Rrotate(_Pnode->_Parent->_Parent);

  001eb	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  001f0	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  001f4	48 8b 50 08	 mov	 rdx, QWORD PTR [rax+8]
  001f8	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  001fd	e8 00 00 00 00	 call	 ?_Rrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::_Rrotate
  00202	90		 npad	 1
$LN13@Insert_nod:

; 713  :                 }
; 714  :             } else { // fixup red-red in right subtree

  00203	e9 c4 00 00 00	 jmp	 $LN11@Insert_nod
$LN10@Insert_nod:

; 715  :                 const auto _Parent_sibling = _Pnode->_Parent->_Parent->_Left;

  00208	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  0020d	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00211	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00215	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00218	48 89 44 24 38	 mov	 QWORD PTR _Parent_sibling$3[rsp], rax

; 716  :                 if (_Parent_sibling->_Color == _Red) { // parent's sibling has two red children, blacken both

  0021d	48 8b 44 24 38	 mov	 rax, QWORD PTR _Parent_sibling$3[rsp]
  00222	0f be 40 18	 movsx	 eax, BYTE PTR [rax+24]
  00226	85 c0		 test	 eax, eax
  00228	75 3b		 jne	 SHORT $LN15@Insert_nod

; 717  :                     _Pnode->_Parent->_Color          = _Black;

  0022a	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  0022f	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00233	c6 40 18 01	 mov	 BYTE PTR [rax+24], 1

; 718  :                     _Parent_sibling->_Color          = _Black;

  00237	48 8b 44 24 38	 mov	 rax, QWORD PTR _Parent_sibling$3[rsp]
  0023c	c6 40 18 01	 mov	 BYTE PTR [rax+24], 1

; 719  :                     _Pnode->_Parent->_Parent->_Color = _Red;

  00240	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  00245	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00249	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0024d	c6 40 18 00	 mov	 BYTE PTR [rax+24], 0

; 720  :                     _Pnode                           = _Pnode->_Parent->_Parent;

  00251	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  00256	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0025a	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0025e	48 89 44 24 20	 mov	 QWORD PTR _Pnode$1[rsp], rax

; 721  :                 } else { // parent's sibling has red and black children

  00263	eb 67		 jmp	 SHORT $LN11@Insert_nod
$LN15@Insert_nod:

; 722  :                     if (_Pnode == _Pnode->_Parent->_Left) { // rotate left child to right

  00265	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  0026a	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0026e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00271	48 39 44 24 20	 cmp	 QWORD PTR _Pnode$1[rsp], rax
  00276	75 1e		 jne	 SHORT $LN17@Insert_nod

; 723  :                         _Pnode = _Pnode->_Parent;

  00278	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  0027d	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00281	48 89 44 24 20	 mov	 QWORD PTR _Pnode$1[rsp], rax

; 724  :                         _Rrotate(_Pnode);

  00286	48 8b 54 24 20	 mov	 rdx, QWORD PTR _Pnode$1[rsp]
  0028b	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  00290	e8 00 00 00 00	 call	 ?_Rrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::_Rrotate
  00295	90		 npad	 1
$LN17@Insert_nod:

; 725  :                     }
; 726  : 
; 727  :                     _Pnode->_Parent->_Color          = _Black; // propagate red up

  00296	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  0029b	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0029f	c6 40 18 01	 mov	 BYTE PTR [rax+24], 1

; 728  :                     _Pnode->_Parent->_Parent->_Color = _Red;

  002a3	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  002a8	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  002ac	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  002b0	c6 40 18 00	 mov	 BYTE PTR [rax+24], 0

; 729  :                     _Lrotate(_Pnode->_Parent->_Parent);

  002b4	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$1[rsp]
  002b9	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  002bd	48 8b 50 08	 mov	 rdx, QWORD PTR [rax+8]
  002c1	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  002c6	e8 00 00 00 00	 call	 ?_Lrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::_Lrotate
  002cb	90		 npad	 1
$LN11@Insert_nod:

; 730  :                 }
; 731  :             }
; 732  :         }

  002cc	e9 38 fe ff ff	 jmp	 $LN2@Insert_nod
$LN3@Insert_nod:

; 733  : 
; 734  :         _Head->_Parent->_Color = _Black; // root is always black

  002d1	48 8b 44 24 28	 mov	 rax, QWORD PTR _Head$[rsp]
  002d6	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  002da	c6 40 18 01	 mov	 BYTE PTR [rax+24], 1

; 735  :         return _Newnode;

  002de	48 8b 44 24 60	 mov	 rax, QWORD PTR _Newnode$[rsp]
$LN1@Insert_nod:

; 736  :     }

  002e3	48 83 c4 48	 add	 rsp, 72			; 00000048H
  002e7	c3		 ret	 0
?_Insert_node@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@U?$_Tree_id@PEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@2@QEAU32@@Z ENDP ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::_Insert_node
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ?_Extract@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z
_TEXT	SEGMENT
_Pnode$ = 32
_Fixnodeparent$ = 40
_Fixnode$ = 48
_Erasednode$ = 56
_Tmp$1 = 64
_Pnode$ = 72
_Pnode$ = 80
tv133 = 88
tv144 = 96
_Left$ = 104
_Right$ = 112
$T2 = 120
$T3 = 128
$T4 = 136
$T5 = 144
$T6 = 152
this$ = 176
_Where$ = 184
?_Extract@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z PROC ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::_Extract, COMDAT

; 527  :     _Nodeptr _Extract(_Unchecked_const_iterator _Where) noexcept {

$LN157:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 81 ec a8 00
	00 00		 sub	 rsp, 168		; 000000a8H

; 528  :         _Nodeptr _Erasednode = _Where._Ptr; // node to erase

  00011	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR _Where$[rsp]
  00019	48 89 44 24 38	 mov	 QWORD PTR _Erasednode$[rsp], rax

; 529  :         ++_Where; // save successor iterator for return

  0001e	48 8d 8c 24 b8
	00 00 00	 lea	 rcx, QWORD PTR _Where$[rsp]
  00026	e8 00 00 00 00	 call	 ??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ ; std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >,std::_Iterator_base0>::operator++

; 530  : 
; 531  :         _Nodeptr _Fixnode; // the node to recolor as needed
; 532  :         _Nodeptr _Fixnodeparent; // parent of _Fixnode (which may be nil)
; 533  :         _Nodeptr _Pnode = _Erasednode;

  0002b	48 8b 44 24 38	 mov	 rax, QWORD PTR _Erasednode$[rsp]
  00030	48 89 44 24 20	 mov	 QWORD PTR _Pnode$[rsp], rax

; 534  : 
; 535  :         if (_Pnode->_Left->_Isnil) {

  00035	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0003a	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0003d	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  00041	85 c0		 test	 eax, eax
  00043	74 10		 je	 SHORT $LN5@Extract

; 536  :             _Fixnode = _Pnode->_Right; // stitch up right subtree

  00045	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0004a	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0004e	48 89 44 24 30	 mov	 QWORD PTR _Fixnode$[rsp], rax
  00053	eb 3b		 jmp	 SHORT $LN6@Extract
$LN5@Extract:

; 537  :         } else if (_Pnode->_Right->_Isnil) {

  00055	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0005a	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0005e	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  00062	85 c0		 test	 eax, eax
  00064	74 0f		 je	 SHORT $LN7@Extract

; 538  :             _Fixnode = _Pnode->_Left; // stitch up left subtree

  00066	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0006b	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0006e	48 89 44 24 30	 mov	 QWORD PTR _Fixnode$[rsp], rax

; 539  :         } else { // two subtrees, must lift successor node to replace erased

  00073	eb 1b		 jmp	 SHORT $LN6@Extract
$LN7@Extract:

; 540  :             _Pnode   = _Where._Ptr; // _Pnode is successor node

  00075	48 8b 84 24 b8
	00 00 00	 mov	 rax, QWORD PTR _Where$[rsp]
  0007d	48 89 44 24 20	 mov	 QWORD PTR _Pnode$[rsp], rax

; 541  :             _Fixnode = _Pnode->_Right; // _Fixnode is only subtree

  00082	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00087	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0008b	48 89 44 24 30	 mov	 QWORD PTR _Fixnode$[rsp], rax
$LN6@Extract:

; 542  :         }
; 543  : 
; 544  :         if (_Pnode == _Erasednode) { // at most one subtree, relink it

  00090	48 8b 44 24 38	 mov	 rax, QWORD PTR _Erasednode$[rsp]
  00095	48 39 44 24 20	 cmp	 QWORD PTR _Pnode$[rsp], rax
  0009a	0f 85 8c 01 00
	00		 jne	 $LN9@Extract

; 545  :             _Fixnodeparent = _Erasednode->_Parent;

  000a0	48 8b 44 24 38	 mov	 rax, QWORD PTR _Erasednode$[rsp]
  000a5	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  000a9	48 89 44 24 28	 mov	 QWORD PTR _Fixnodeparent$[rsp], rax

; 546  :             if (!_Fixnode->_Isnil) {

  000ae	48 8b 44 24 30	 mov	 rax, QWORD PTR _Fixnode$[rsp]
  000b3	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  000b7	85 c0		 test	 eax, eax
  000b9	75 0e		 jne	 SHORT $LN11@Extract

; 547  :                 _Fixnode->_Parent = _Fixnodeparent; // link up

  000bb	48 8b 44 24 30	 mov	 rax, QWORD PTR _Fixnode$[rsp]
  000c0	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Fixnodeparent$[rsp]
  000c5	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx
$LN11@Extract:

; 548  :             }
; 549  : 
; 550  :             if (_Myhead->_Parent == _Erasednode) {

  000c9	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000d1	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000d4	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Erasednode$[rsp]
  000d9	48 39 48 08	 cmp	 QWORD PTR [rax+8], rcx
  000dd	75 16		 jne	 SHORT $LN12@Extract

; 551  :                 _Myhead->_Parent = _Fixnode; // link down from root

  000df	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000e7	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000ea	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Fixnode$[rsp]
  000ef	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx
  000f3	eb 2c		 jmp	 SHORT $LN13@Extract
$LN12@Extract:

; 552  :             } else if (_Fixnodeparent->_Left == _Erasednode) {

  000f5	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  000fa	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Erasednode$[rsp]
  000ff	48 39 08	 cmp	 QWORD PTR [rax], rcx
  00102	75 0f		 jne	 SHORT $LN14@Extract

; 553  :                 _Fixnodeparent->_Left = _Fixnode; // link down to left

  00104	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  00109	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Fixnode$[rsp]
  0010e	48 89 08	 mov	 QWORD PTR [rax], rcx

; 554  :             } else {

  00111	eb 0e		 jmp	 SHORT $LN13@Extract
$LN14@Extract:

; 555  :                 _Fixnodeparent->_Right = _Fixnode; // link down to right

  00113	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  00118	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Fixnode$[rsp]
  0011d	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx
$LN13@Extract:

; 556  :             }
; 557  : 
; 558  :             if (_Myhead->_Left == _Erasednode) {

  00121	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00129	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0012c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Erasednode$[rsp]
  00131	48 39 08	 cmp	 QWORD PTR [rax], rcx
  00134	75 69		 jne	 SHORT $LN16@Extract

; 559  :                 _Myhead->_Left = _Fixnode->_Isnil ? _Fixnodeparent // smallest is parent of erased node

  00136	48 8b 44 24 30	 mov	 rax, QWORD PTR _Fixnode$[rsp]
  0013b	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  0013f	85 c0		 test	 eax, eax
  00141	74 0c		 je	 SHORT $LN42@Extract
  00143	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  00148	48 89 44 24 58	 mov	 QWORD PTR tv133[rsp], rax
  0014d	eb 3d		 jmp	 SHORT $LN43@Extract
$LN42@Extract:
  0014f	48 8b 44 24 30	 mov	 rax, QWORD PTR _Fixnode$[rsp]
  00154	48 89 44 24 48	 mov	 QWORD PTR _Pnode$[rsp], rax
$LN64@Extract:

; 476  :         while (!_Pnode->_Left->_Isnil) {

  00159	48 8b 44 24 48	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0015e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00161	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  00165	85 c0		 test	 eax, eax
  00167	75 0f		 jne	 SHORT $LN65@Extract

; 477  :             _Pnode = _Pnode->_Left;

  00169	48 8b 44 24 48	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0016e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00171	48 89 44 24 48	 mov	 QWORD PTR _Pnode$[rsp], rax

; 478  :         }

  00176	eb e1		 jmp	 SHORT $LN64@Extract
$LN65@Extract:

; 479  : 
; 480  :         return _Pnode;

  00178	48 8b 44 24 48	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0017d	48 89 44 24 78	 mov	 QWORD PTR $T2[rsp], rax

; 559  :                 _Myhead->_Left = _Fixnode->_Isnil ? _Fixnodeparent // smallest is parent of erased node

  00182	48 8b 44 24 78	 mov	 rax, QWORD PTR $T2[rsp]
  00187	48 89 44 24 58	 mov	 QWORD PTR tv133[rsp], rax
$LN43@Extract:
  0018c	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00194	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00197	48 8b 4c 24 58	 mov	 rcx, QWORD PTR tv133[rsp]
  0019c	48 89 08	 mov	 QWORD PTR [rax], rcx
$LN16@Extract:

; 560  :                                                   : _Min(_Fixnode); // smallest in relinked subtree
; 561  :             }
; 562  : 
; 563  :             if (_Myhead->_Right == _Erasednode) {

  0019f	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  001a7	48 8b 00	 mov	 rax, QWORD PTR [rax]
  001aa	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Erasednode$[rsp]
  001af	48 39 48 10	 cmp	 QWORD PTR [rax+16], rcx
  001b3	75 72		 jne	 SHORT $LN17@Extract

; 564  :                 _Myhead->_Right = _Fixnode->_Isnil ? _Fixnodeparent // largest is parent of erased node

  001b5	48 8b 44 24 30	 mov	 rax, QWORD PTR _Fixnode$[rsp]
  001ba	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  001be	85 c0		 test	 eax, eax
  001c0	74 0c		 je	 SHORT $LN44@Extract
  001c2	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  001c7	48 89 44 24 60	 mov	 QWORD PTR tv144[rsp], rax
  001cc	eb 45		 jmp	 SHORT $LN45@Extract
$LN44@Extract:
  001ce	48 8b 44 24 30	 mov	 rax, QWORD PTR _Fixnode$[rsp]
  001d3	48 89 44 24 50	 mov	 QWORD PTR _Pnode$[rsp], rax
$LN71@Extract:

; 468  :         while (!_Pnode->_Right->_Isnil) {

  001d8	48 8b 44 24 50	 mov	 rax, QWORD PTR _Pnode$[rsp]
  001dd	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  001e1	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  001e5	85 c0		 test	 eax, eax
  001e7	75 10		 jne	 SHORT $LN72@Extract

; 469  :             _Pnode = _Pnode->_Right;

  001e9	48 8b 44 24 50	 mov	 rax, QWORD PTR _Pnode$[rsp]
  001ee	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  001f2	48 89 44 24 50	 mov	 QWORD PTR _Pnode$[rsp], rax

; 470  :         }

  001f7	eb df		 jmp	 SHORT $LN71@Extract
$LN72@Extract:

; 471  : 
; 472  :         return _Pnode;

  001f9	48 8b 44 24 50	 mov	 rax, QWORD PTR _Pnode$[rsp]
  001fe	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T3[rsp], rax

; 564  :                 _Myhead->_Right = _Fixnode->_Isnil ? _Fixnodeparent // largest is parent of erased node

  00206	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR $T3[rsp]
  0020e	48 89 44 24 60	 mov	 QWORD PTR tv144[rsp], rax
$LN45@Extract:
  00213	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0021b	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0021e	48 8b 4c 24 60	 mov	 rcx, QWORD PTR tv144[rsp]
  00223	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx
$LN17@Extract:

; 565  :                                                    : _Max(_Fixnode); // largest in relinked subtree
; 566  :             }
; 567  :         } else { // erased has two subtrees, _Pnode is successor to erased

  00227	e9 83 01 00 00	 jmp	 $LN10@Extract
$LN9@Extract:

; 568  :             _Erasednode->_Left->_Parent = _Pnode; // link left up

  0022c	48 8b 44 24 38	 mov	 rax, QWORD PTR _Erasednode$[rsp]
  00231	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00234	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Pnode$[rsp]
  00239	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 569  :             _Pnode->_Left               = _Erasednode->_Left; // link successor down

  0023d	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00242	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Erasednode$[rsp]
  00247	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0024a	48 89 08	 mov	 QWORD PTR [rax], rcx

; 570  : 
; 571  :             if (_Pnode == _Erasednode->_Right) {

  0024d	48 8b 44 24 38	 mov	 rax, QWORD PTR _Erasednode$[rsp]
  00252	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00256	48 39 44 24 20	 cmp	 QWORD PTR _Pnode$[rsp], rax
  0025b	75 0c		 jne	 SHORT $LN18@Extract

; 572  :                 _Fixnodeparent = _Pnode; // successor is next to erased

  0025d	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00262	48 89 44 24 28	 mov	 QWORD PTR _Fixnodeparent$[rsp], rax

; 573  :             } else { // successor further down, link in place of erased

  00267	eb 5a		 jmp	 SHORT $LN19@Extract
$LN18@Extract:

; 574  :                 _Fixnodeparent = _Pnode->_Parent; // parent is successor's

  00269	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0026e	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00272	48 89 44 24 28	 mov	 QWORD PTR _Fixnodeparent$[rsp], rax

; 575  :                 if (!_Fixnode->_Isnil) {

  00277	48 8b 44 24 30	 mov	 rax, QWORD PTR _Fixnode$[rsp]
  0027c	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  00280	85 c0		 test	 eax, eax
  00282	75 0e		 jne	 SHORT $LN20@Extract

; 576  :                     _Fixnode->_Parent = _Fixnodeparent; // link fix up

  00284	48 8b 44 24 30	 mov	 rax, QWORD PTR _Fixnode$[rsp]
  00289	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Fixnodeparent$[rsp]
  0028e	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx
$LN20@Extract:

; 577  :                 }
; 578  : 
; 579  :                 _Fixnodeparent->_Left        = _Fixnode; // link fix down

  00292	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  00297	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Fixnode$[rsp]
  0029c	48 89 08	 mov	 QWORD PTR [rax], rcx

; 580  :                 _Pnode->_Right               = _Erasednode->_Right; // link next down

  0029f	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  002a4	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Erasednode$[rsp]
  002a9	48 8b 49 10	 mov	 rcx, QWORD PTR [rcx+16]
  002ad	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 581  :                 _Erasednode->_Right->_Parent = _Pnode; // right up

  002b1	48 8b 44 24 38	 mov	 rax, QWORD PTR _Erasednode$[rsp]
  002b6	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  002ba	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Pnode$[rsp]
  002bf	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx
$LN19@Extract:

; 582  :             }
; 583  : 
; 584  :             if (_Myhead->_Parent == _Erasednode) {

  002c3	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  002cb	48 8b 00	 mov	 rax, QWORD PTR [rax]
  002ce	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Erasednode$[rsp]
  002d3	48 39 48 08	 cmp	 QWORD PTR [rax+8], rcx
  002d7	75 16		 jne	 SHORT $LN21@Extract

; 585  :                 _Myhead->_Parent = _Pnode; // link down from root

  002d9	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  002e1	48 8b 00	 mov	 rax, QWORD PTR [rax]
  002e4	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Pnode$[rsp]
  002e9	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx
  002ed	eb 38		 jmp	 SHORT $LN22@Extract
$LN21@Extract:

; 586  :             } else if (_Erasednode->_Parent->_Left == _Erasednode) {

  002ef	48 8b 44 24 38	 mov	 rax, QWORD PTR _Erasednode$[rsp]
  002f4	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  002f8	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Erasednode$[rsp]
  002fd	48 39 08	 cmp	 QWORD PTR [rax], rcx
  00300	75 13		 jne	 SHORT $LN23@Extract

; 587  :                 _Erasednode->_Parent->_Left = _Pnode; // link down to left

  00302	48 8b 44 24 38	 mov	 rax, QWORD PTR _Erasednode$[rsp]
  00307	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0030b	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Pnode$[rsp]
  00310	48 89 08	 mov	 QWORD PTR [rax], rcx

; 588  :             } else {

  00313	eb 12		 jmp	 SHORT $LN22@Extract
$LN23@Extract:

; 589  :                 _Erasednode->_Parent->_Right = _Pnode; // link down to right

  00315	48 8b 44 24 38	 mov	 rax, QWORD PTR _Erasednode$[rsp]
  0031a	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0031e	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Pnode$[rsp]
  00323	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx
$LN22@Extract:

; 590  :             }
; 591  : 
; 592  :             _Pnode->_Parent = _Erasednode->_Parent; // link successor up

  00327	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0032c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Erasednode$[rsp]
  00331	48 8b 49 08	 mov	 rcx, QWORD PTR [rcx+8]
  00335	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 593  :             _STD swap(_Pnode->_Color, _Erasednode->_Color); // recolor it

  00339	48 8b 44 24 38	 mov	 rax, QWORD PTR _Erasednode$[rsp]
  0033e	48 83 c0 18	 add	 rax, 24
  00342	48 89 44 24 70	 mov	 QWORD PTR _Right$[rsp], rax
  00347	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0034c	48 83 c0 18	 add	 rax, 24
  00350	48 89 44 24 68	 mov	 QWORD PTR _Left$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00355	48 8b 44 24 68	 mov	 rax, QWORD PTR _Left$[rsp]
  0035a	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 139  :     _Ty _Tmp = _STD move(_Left);

  00362	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T4[rsp]
  0036a	0f b6 00	 movzx	 eax, BYTE PTR [rax]
  0036d	88 44 24 40	 mov	 BYTE PTR _Tmp$1[rsp], al
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00371	48 8b 44 24 70	 mov	 rax, QWORD PTR _Right$[rsp]
  00376	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 140  :     _Left    = _STD move(_Right);

  0037e	48 8b 44 24 68	 mov	 rax, QWORD PTR _Left$[rsp]
  00383	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR $T5[rsp]
  0038b	0f b6 09	 movzx	 ecx, BYTE PTR [rcx]
  0038e	88 08		 mov	 BYTE PTR [rax], cl
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1513 :     return static_cast<remove_reference_t<_Ty>&&>(_Arg);

  00390	48 8d 44 24 40	 lea	 rax, QWORD PTR _Tmp$1[rsp]
  00395	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 141  :     _Right   = _STD move(_Tmp);

  0039d	48 8b 44 24 70	 mov	 rax, QWORD PTR _Right$[rsp]
  003a2	48 8b 8c 24 98
	00 00 00	 mov	 rcx, QWORD PTR $T6[rsp]
  003aa	0f b6 09	 movzx	 ecx, BYTE PTR [rcx]
  003ad	88 08		 mov	 BYTE PTR [rax], cl
$LN10@Extract:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 596  :         if (_Erasednode->_Color == _Black) { // erasing black link, must recolor/rebalance tree

  003af	48 8b 44 24 38	 mov	 rax, QWORD PTR _Erasednode$[rsp]
  003b4	0f be 40 18	 movsx	 eax, BYTE PTR [rax+24]
  003b8	83 f8 01	 cmp	 eax, 1
  003bb	0f 85 af 02 00
	00		 jne	 $LN25@Extract

; 597  :             for (; _Fixnode != _Myhead->_Parent && _Fixnode->_Color == _Black; _Fixnodeparent = _Fixnode->_Parent) {

  003c1	eb 0e		 jmp	 SHORT $LN4@Extract
$LN2@Extract:
  003c3	48 8b 44 24 30	 mov	 rax, QWORD PTR _Fixnode$[rsp]
  003c8	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  003cc	48 89 44 24 28	 mov	 QWORD PTR _Fixnodeparent$[rsp], rax
$LN4@Extract:
  003d1	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  003d9	48 8b 00	 mov	 rax, QWORD PTR [rax]
  003dc	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  003e0	48 39 44 24 30	 cmp	 QWORD PTR _Fixnode$[rsp], rax
  003e5	0f 84 7c 02 00
	00		 je	 $LN3@Extract
  003eb	48 8b 44 24 30	 mov	 rax, QWORD PTR _Fixnode$[rsp]
  003f0	0f be 40 18	 movsx	 eax, BYTE PTR [rax+24]
  003f4	83 f8 01	 cmp	 eax, 1
  003f7	0f 85 6a 02 00
	00		 jne	 $LN3@Extract

; 598  :                 if (_Fixnode == _Fixnodeparent->_Left) { // fixup left subtree

  003fd	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  00402	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00405	48 39 44 24 30	 cmp	 QWORD PTR _Fixnode$[rsp], rax
  0040a	0f 85 2f 01 00
	00		 jne	 $LN26@Extract

; 599  :                     _Pnode = _Fixnodeparent->_Right;

  00410	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  00415	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00419	48 89 44 24 20	 mov	 QWORD PTR _Pnode$[rsp], rax

; 600  :                     if (_Pnode->_Color == _Red) { // rotate red up from right subtree

  0041e	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00423	0f be 40 18	 movsx	 eax, BYTE PTR [rax+24]
  00427	85 c0		 test	 eax, eax
  00429	75 32		 jne	 SHORT $LN28@Extract

; 601  :                         _Pnode->_Color         = _Black;

  0042b	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00430	c6 40 18 01	 mov	 BYTE PTR [rax+24], 1

; 602  :                         _Fixnodeparent->_Color = _Red;

  00434	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  00439	c6 40 18 00	 mov	 BYTE PTR [rax+24], 0

; 603  :                         _Lrotate(_Fixnodeparent);

  0043d	48 8b 54 24 28	 mov	 rdx, QWORD PTR _Fixnodeparent$[rsp]
  00442	48 8b 8c 24 b0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0044a	e8 00 00 00 00	 call	 ?_Lrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::_Lrotate

; 604  :                         _Pnode = _Fixnodeparent->_Right;

  0044f	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  00454	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00458	48 89 44 24 20	 mov	 QWORD PTR _Pnode$[rsp], rax
$LN28@Extract:

; 605  :                     }
; 606  : 
; 607  :                     if (_Pnode->_Isnil) {

  0045d	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00462	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  00466	85 c0		 test	 eax, eax
  00468	74 0f		 je	 SHORT $LN29@Extract

; 608  :                         _Fixnode = _Fixnodeparent; // shouldn't happen

  0046a	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  0046f	48 89 44 24 30	 mov	 QWORD PTR _Fixnode$[rsp], rax

; 609  :                     } else if (_Pnode->_Left->_Color == _Black

  00474	e9 c1 00 00 00	 jmp	 $LN30@Extract
$LN29@Extract:

; 610  :                                && _Pnode->_Right->_Color == _Black) { // redden right subtree with black children

  00479	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0047e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00481	0f be 40 18	 movsx	 eax, BYTE PTR [rax+24]
  00485	83 f8 01	 cmp	 eax, 1
  00488	75 2a		 jne	 SHORT $LN31@Extract
  0048a	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0048f	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00493	0f be 40 18	 movsx	 eax, BYTE PTR [rax+24]
  00497	83 f8 01	 cmp	 eax, 1
  0049a	75 18		 jne	 SHORT $LN31@Extract

; 611  :                         _Pnode->_Color = _Red;

  0049c	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  004a1	c6 40 18 00	 mov	 BYTE PTR [rax+24], 0

; 612  :                         _Fixnode       = _Fixnodeparent;

  004a5	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  004aa	48 89 44 24 30	 mov	 QWORD PTR _Fixnode$[rsp], rax

; 613  :                     } else { // must rearrange right subtree

  004af	e9 86 00 00 00	 jmp	 $LN30@Extract
$LN31@Extract:

; 614  :                         if (_Pnode->_Right->_Color == _Black) { // rotate red up from left sub-subtree

  004b4	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  004b9	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  004bd	0f be 40 18	 movsx	 eax, BYTE PTR [rax+24]
  004c1	83 f8 01	 cmp	 eax, 1
  004c4	75 35		 jne	 SHORT $LN33@Extract

; 615  :                             _Pnode->_Left->_Color = _Black;

  004c6	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  004cb	48 8b 00	 mov	 rax, QWORD PTR [rax]
  004ce	c6 40 18 01	 mov	 BYTE PTR [rax+24], 1

; 616  :                             _Pnode->_Color        = _Red;

  004d2	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  004d7	c6 40 18 00	 mov	 BYTE PTR [rax+24], 0

; 617  :                             _Rrotate(_Pnode);

  004db	48 8b 54 24 20	 mov	 rdx, QWORD PTR _Pnode$[rsp]
  004e0	48 8b 8c 24 b0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  004e8	e8 00 00 00 00	 call	 ?_Rrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::_Rrotate

; 618  :                             _Pnode = _Fixnodeparent->_Right;

  004ed	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  004f2	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  004f6	48 89 44 24 20	 mov	 QWORD PTR _Pnode$[rsp], rax
$LN33@Extract:

; 619  :                         }
; 620  : 
; 621  :                         _Pnode->_Color         = _Fixnodeparent->_Color;

  004fb	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00500	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Fixnodeparent$[rsp]
  00505	0f b6 49 18	 movzx	 ecx, BYTE PTR [rcx+24]
  00509	88 48 18	 mov	 BYTE PTR [rax+24], cl

; 622  :                         _Fixnodeparent->_Color = _Black;

  0050c	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  00511	c6 40 18 01	 mov	 BYTE PTR [rax+24], 1

; 623  :                         _Pnode->_Right->_Color = _Black;

  00515	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0051a	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0051e	c6 40 18 01	 mov	 BYTE PTR [rax+24], 1

; 624  :                         _Lrotate(_Fixnodeparent);

  00522	48 8b 54 24 28	 mov	 rdx, QWORD PTR _Fixnodeparent$[rsp]
  00527	48 8b 8c 24 b0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0052f	e8 00 00 00 00	 call	 ?_Lrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::_Lrotate
  00534	90		 npad	 1

; 625  :                         break; // tree now recolored/rebalanced

  00535	e9 2d 01 00 00	 jmp	 $LN3@Extract
$LN30@Extract:

; 626  :                     }
; 627  :                 } else { // fixup right subtree

  0053a	e9 23 01 00 00	 jmp	 $LN27@Extract
$LN26@Extract:

; 628  :                     _Pnode = _Fixnodeparent->_Left;

  0053f	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  00544	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00547	48 89 44 24 20	 mov	 QWORD PTR _Pnode$[rsp], rax

; 629  :                     if (_Pnode->_Color == _Red) { // rotate red up from left subtree

  0054c	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00551	0f be 40 18	 movsx	 eax, BYTE PTR [rax+24]
  00555	85 c0		 test	 eax, eax
  00557	75 31		 jne	 SHORT $LN34@Extract

; 630  :                         _Pnode->_Color         = _Black;

  00559	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0055e	c6 40 18 01	 mov	 BYTE PTR [rax+24], 1

; 631  :                         _Fixnodeparent->_Color = _Red;

  00562	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  00567	c6 40 18 00	 mov	 BYTE PTR [rax+24], 0

; 632  :                         _Rrotate(_Fixnodeparent);

  0056b	48 8b 54 24 28	 mov	 rdx, QWORD PTR _Fixnodeparent$[rsp]
  00570	48 8b 8c 24 b0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00578	e8 00 00 00 00	 call	 ?_Rrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::_Rrotate

; 633  :                         _Pnode = _Fixnodeparent->_Left;

  0057d	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  00582	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00585	48 89 44 24 20	 mov	 QWORD PTR _Pnode$[rsp], rax
$LN34@Extract:

; 634  :                     }
; 635  : 
; 636  :                     if (_Pnode->_Isnil) {

  0058a	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0058f	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  00593	85 c0		 test	 eax, eax
  00595	74 0f		 je	 SHORT $LN35@Extract

; 637  :                         _Fixnode = _Fixnodeparent; // shouldn't happen

  00597	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  0059c	48 89 44 24 30	 mov	 QWORD PTR _Fixnode$[rsp], rax

; 638  :                     } else if (_Pnode->_Right->_Color == _Black

  005a1	e9 bc 00 00 00	 jmp	 $LN27@Extract
$LN35@Extract:

; 639  :                                && _Pnode->_Left->_Color == _Black) { // redden left subtree with black children

  005a6	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  005ab	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  005af	0f be 40 18	 movsx	 eax, BYTE PTR [rax+24]
  005b3	83 f8 01	 cmp	 eax, 1
  005b6	75 29		 jne	 SHORT $LN37@Extract
  005b8	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  005bd	48 8b 00	 mov	 rax, QWORD PTR [rax]
  005c0	0f be 40 18	 movsx	 eax, BYTE PTR [rax+24]
  005c4	83 f8 01	 cmp	 eax, 1
  005c7	75 18		 jne	 SHORT $LN37@Extract

; 640  :                         _Pnode->_Color = _Red;

  005c9	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  005ce	c6 40 18 00	 mov	 BYTE PTR [rax+24], 0

; 641  :                         _Fixnode       = _Fixnodeparent;

  005d2	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  005d7	48 89 44 24 30	 mov	 QWORD PTR _Fixnode$[rsp], rax

; 642  :                     } else { // must rearrange left subtree

  005dc	e9 81 00 00 00	 jmp	 $LN27@Extract
$LN37@Extract:

; 643  :                         if (_Pnode->_Left->_Color == _Black) { // rotate red up from right sub-subtree

  005e1	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  005e6	48 8b 00	 mov	 rax, QWORD PTR [rax]
  005e9	0f be 40 18	 movsx	 eax, BYTE PTR [rax+24]
  005ed	83 f8 01	 cmp	 eax, 1
  005f0	75 35		 jne	 SHORT $LN39@Extract

; 644  :                             _Pnode->_Right->_Color = _Black;

  005f2	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  005f7	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  005fb	c6 40 18 01	 mov	 BYTE PTR [rax+24], 1

; 645  :                             _Pnode->_Color         = _Red;

  005ff	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00604	c6 40 18 00	 mov	 BYTE PTR [rax+24], 0

; 646  :                             _Lrotate(_Pnode);

  00608	48 8b 54 24 20	 mov	 rdx, QWORD PTR _Pnode$[rsp]
  0060d	48 8b 8c 24 b0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00615	e8 00 00 00 00	 call	 ?_Lrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::_Lrotate

; 647  :                             _Pnode = _Fixnodeparent->_Left;

  0061a	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  0061f	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00622	48 89 44 24 20	 mov	 QWORD PTR _Pnode$[rsp], rax
$LN39@Extract:

; 648  :                         }
; 649  : 
; 650  :                         _Pnode->_Color         = _Fixnodeparent->_Color;

  00627	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0062c	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Fixnodeparent$[rsp]
  00631	0f b6 49 18	 movzx	 ecx, BYTE PTR [rcx+24]
  00635	88 48 18	 mov	 BYTE PTR [rax+24], cl

; 651  :                         _Fixnodeparent->_Color = _Black;

  00638	48 8b 44 24 28	 mov	 rax, QWORD PTR _Fixnodeparent$[rsp]
  0063d	c6 40 18 01	 mov	 BYTE PTR [rax+24], 1

; 652  :                         _Pnode->_Left->_Color  = _Black;

  00641	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00646	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00649	c6 40 18 01	 mov	 BYTE PTR [rax+24], 1

; 653  :                         _Rrotate(_Fixnodeparent);

  0064d	48 8b 54 24 28	 mov	 rdx, QWORD PTR _Fixnodeparent$[rsp]
  00652	48 8b 8c 24 b0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0065a	e8 00 00 00 00	 call	 ?_Rrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::_Rrotate
  0065f	90		 npad	 1

; 654  :                         break; // tree now recolored/rebalanced

  00660	eb 05		 jmp	 SHORT $LN3@Extract
$LN27@Extract:

; 655  :                     }
; 656  :                 }
; 657  :             }

  00662	e9 5c fd ff ff	 jmp	 $LN2@Extract
$LN3@Extract:

; 658  : 
; 659  :             _Fixnode->_Color = _Black; // stopping node is black

  00667	48 8b 44 24 30	 mov	 rax, QWORD PTR _Fixnode$[rsp]
  0066c	c6 40 18 01	 mov	 BYTE PTR [rax+24], 1
$LN25@Extract:

; 660  :         }
; 661  : 
; 662  :         if (0 < _Mysize) {

  00670	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00678	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  0067d	76 1b		 jbe	 SHORT $LN40@Extract

; 663  :             --_Mysize;

  0067f	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00687	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0068b	48 ff c8	 dec	 rax
  0068e	48 8b 8c 24 b0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00696	48 89 41 08	 mov	 QWORD PTR [rcx+8], rax
$LN40@Extract:

; 664  :         }
; 665  : 
; 666  :         return _Erasednode;

  0069a	48 8b 44 24 38	 mov	 rax, QWORD PTR _Erasednode$[rsp]

; 667  :     }

  0069f	48 81 c4 a8 00
	00 00		 add	 rsp, 168		; 000000a8H
  006a6	c3		 ret	 0
?_Extract@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z ENDP ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::_Extract
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ?_Rrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@@Z
_TEXT	SEGMENT
_Pnode$ = 0
this$ = 32
_Wherenode$ = 40
?_Rrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@@Z PROC ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::_Rrotate, COMDAT

; 505  :     void _Rrotate(_Nodeptr _Wherenode) noexcept { // promote left node to root of subtree

$LN9:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 18	 sub	 rsp, 24

; 506  :         _Nodeptr _Pnode   = _Wherenode->_Left;

  0000e	48 8b 44 24 28	 mov	 rax, QWORD PTR _Wherenode$[rsp]
  00013	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00016	48 89 04 24	 mov	 QWORD PTR _Pnode$[rsp], rax

; 507  :         _Wherenode->_Left = _Pnode->_Right;

  0001a	48 8b 44 24 28	 mov	 rax, QWORD PTR _Wherenode$[rsp]
  0001f	48 8b 0c 24	 mov	 rcx, QWORD PTR _Pnode$[rsp]
  00023	48 8b 49 10	 mov	 rcx, QWORD PTR [rcx+16]
  00027	48 89 08	 mov	 QWORD PTR [rax], rcx

; 508  : 
; 509  :         if (!_Pnode->_Right->_Isnil) {

  0002a	48 8b 04 24	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0002e	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00032	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  00036	85 c0		 test	 eax, eax
  00038	75 11		 jne	 SHORT $LN2@Rrotate

; 510  :             _Pnode->_Right->_Parent = _Wherenode;

  0003a	48 8b 04 24	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0003e	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00042	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Wherenode$[rsp]
  00047	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx
$LN2@Rrotate:

; 511  :         }
; 512  : 
; 513  :         _Pnode->_Parent = _Wherenode->_Parent;

  0004b	48 8b 04 24	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0004f	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Wherenode$[rsp]
  00054	48 8b 49 08	 mov	 rcx, QWORD PTR [rcx+8]
  00058	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 514  : 
; 515  :         if (_Wherenode == _Myhead->_Parent) {

  0005c	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00061	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00064	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00068	48 39 44 24 28	 cmp	 QWORD PTR _Wherenode$[rsp], rax
  0006d	75 12		 jne	 SHORT $LN3@Rrotate

; 516  :             _Myhead->_Parent = _Pnode;

  0006f	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00074	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00077	48 8b 0c 24	 mov	 rcx, QWORD PTR _Pnode$[rsp]
  0007b	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx
  0007f	eb 37		 jmp	 SHORT $LN4@Rrotate
$LN3@Rrotate:

; 517  :         } else if (_Wherenode == _Wherenode->_Parent->_Right) {

  00081	48 8b 44 24 28	 mov	 rax, QWORD PTR _Wherenode$[rsp]
  00086	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0008a	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0008e	48 39 44 24 28	 cmp	 QWORD PTR _Wherenode$[rsp], rax
  00093	75 13		 jne	 SHORT $LN5@Rrotate

; 518  :             _Wherenode->_Parent->_Right = _Pnode;

  00095	48 8b 44 24 28	 mov	 rax, QWORD PTR _Wherenode$[rsp]
  0009a	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0009e	48 8b 0c 24	 mov	 rcx, QWORD PTR _Pnode$[rsp]
  000a2	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 519  :         } else {

  000a6	eb 10		 jmp	 SHORT $LN4@Rrotate
$LN5@Rrotate:

; 520  :             _Wherenode->_Parent->_Left = _Pnode;

  000a8	48 8b 44 24 28	 mov	 rax, QWORD PTR _Wherenode$[rsp]
  000ad	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  000b1	48 8b 0c 24	 mov	 rcx, QWORD PTR _Pnode$[rsp]
  000b5	48 89 08	 mov	 QWORD PTR [rax], rcx
$LN4@Rrotate:

; 521  :         }
; 522  : 
; 523  :         _Pnode->_Right      = _Wherenode;

  000b8	48 8b 04 24	 mov	 rax, QWORD PTR _Pnode$[rsp]
  000bc	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Wherenode$[rsp]
  000c1	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 524  :         _Wherenode->_Parent = _Pnode;

  000c5	48 8b 44 24 28	 mov	 rax, QWORD PTR _Wherenode$[rsp]
  000ca	48 8b 0c 24	 mov	 rcx, QWORD PTR _Pnode$[rsp]
  000ce	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 525  :     }

  000d2	48 83 c4 18	 add	 rsp, 24
  000d6	c3		 ret	 0
?_Rrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@@Z ENDP ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::_Rrotate
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ?_Lrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@@Z
_TEXT	SEGMENT
_Pnode$ = 0
this$ = 32
_Wherenode$ = 40
?_Lrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@@Z PROC ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::_Lrotate, COMDAT

; 483  :     void _Lrotate(_Nodeptr _Wherenode) noexcept { // promote right node to root of subtree

$LN9:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 18	 sub	 rsp, 24

; 484  :         _Nodeptr _Pnode    = _Wherenode->_Right;

  0000e	48 8b 44 24 28	 mov	 rax, QWORD PTR _Wherenode$[rsp]
  00013	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00017	48 89 04 24	 mov	 QWORD PTR _Pnode$[rsp], rax

; 485  :         _Wherenode->_Right = _Pnode->_Left;

  0001b	48 8b 44 24 28	 mov	 rax, QWORD PTR _Wherenode$[rsp]
  00020	48 8b 0c 24	 mov	 rcx, QWORD PTR _Pnode$[rsp]
  00024	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00027	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx

; 486  : 
; 487  :         if (!_Pnode->_Left->_Isnil) {

  0002b	48 8b 04 24	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0002f	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00032	0f be 40 19	 movsx	 eax, BYTE PTR [rax+25]
  00036	85 c0		 test	 eax, eax
  00038	75 10		 jne	 SHORT $LN2@Lrotate

; 488  :             _Pnode->_Left->_Parent = _Wherenode;

  0003a	48 8b 04 24	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0003e	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00041	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Wherenode$[rsp]
  00046	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx
$LN2@Lrotate:

; 489  :         }
; 490  : 
; 491  :         _Pnode->_Parent = _Wherenode->_Parent;

  0004a	48 8b 04 24	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0004e	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Wherenode$[rsp]
  00053	48 8b 49 08	 mov	 rcx, QWORD PTR [rcx+8]
  00057	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 492  : 
; 493  :         if (_Wherenode == _Myhead->_Parent) {

  0005b	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00060	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00063	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00067	48 39 44 24 28	 cmp	 QWORD PTR _Wherenode$[rsp], rax
  0006c	75 12		 jne	 SHORT $LN3@Lrotate

; 494  :             _Myhead->_Parent = _Pnode;

  0006e	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00073	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00076	48 8b 0c 24	 mov	 rcx, QWORD PTR _Pnode$[rsp]
  0007a	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx
  0007e	eb 36		 jmp	 SHORT $LN4@Lrotate
$LN3@Lrotate:

; 495  :         } else if (_Wherenode == _Wherenode->_Parent->_Left) {

  00080	48 8b 44 24 28	 mov	 rax, QWORD PTR _Wherenode$[rsp]
  00085	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00089	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0008c	48 39 44 24 28	 cmp	 QWORD PTR _Wherenode$[rsp], rax
  00091	75 12		 jne	 SHORT $LN5@Lrotate

; 496  :             _Wherenode->_Parent->_Left = _Pnode;

  00093	48 8b 44 24 28	 mov	 rax, QWORD PTR _Wherenode$[rsp]
  00098	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0009c	48 8b 0c 24	 mov	 rcx, QWORD PTR _Pnode$[rsp]
  000a0	48 89 08	 mov	 QWORD PTR [rax], rcx

; 497  :         } else {

  000a3	eb 11		 jmp	 SHORT $LN4@Lrotate
$LN5@Lrotate:

; 498  :             _Wherenode->_Parent->_Right = _Pnode;

  000a5	48 8b 44 24 28	 mov	 rax, QWORD PTR _Wherenode$[rsp]
  000aa	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  000ae	48 8b 0c 24	 mov	 rcx, QWORD PTR _Pnode$[rsp]
  000b2	48 89 48 10	 mov	 QWORD PTR [rax+16], rcx
$LN4@Lrotate:

; 499  :         }
; 500  : 
; 501  :         _Pnode->_Left       = _Wherenode;

  000b6	48 8b 04 24	 mov	 rax, QWORD PTR _Pnode$[rsp]
  000ba	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Wherenode$[rsp]
  000bf	48 89 08	 mov	 QWORD PTR [rax], rcx

; 502  :         _Wherenode->_Parent = _Pnode;

  000c2	48 8b 44 24 28	 mov	 rax, QWORD PTR _Wherenode$[rsp]
  000c7	48 8b 0c 24	 mov	 rcx, QWORD PTR _Pnode$[rsp]
  000cb	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 503  :     }

  000cf	48 83 c4 18	 add	 rsp, 24
  000d3	c3		 ret	 0
?_Lrotate@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@@Z ENDP ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::_Lrotate
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ?_Alloc_sentinel_and_proxy@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@IEAAXXZ
_TEXT	SEGMENT
$T1 = 32
$S140$ = 33
$T2 = 40
$T3 = 48
$T4 = 56
$T5 = 64
_Scary$ = 72
_Alproxy$ = 80
this$ = 112
?_Alloc_sentinel_and_proxy@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@IEAAXXZ PROC ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::_Alloc_sentinel_and_proxy, COMDAT

; 1953 :     void _Alloc_sentinel_and_proxy() {

$LN107:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi
  00006	48 83 ec 60	 sub	 rsp, 96			; 00000060H

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  0000a	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0000f	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00014	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
  00019	48 89 44 24 30	 mov	 QWORD PTR $T3[rsp], rax

; 1954 :         const auto _Scary = _Get_scary();

  0001e	48 8b 44 24 30	 mov	 rax, QWORD PTR $T3[rsp]
  00023	48 89 44 24 48	 mov	 QWORD PTR _Scary$[rsp], rax

; 1955 :         auto&& _Alproxy   = _GET_PROXY_ALLOCATOR(_Alnode, _Getal());

  00028	48 8d 44 24 20	 lea	 rax, QWORD PTR $T1[rsp]
  0002d	48 8b f8	 mov	 rdi, rax
  00030	33 c0		 xor	 eax, eax
  00032	b9 01 00 00 00	 mov	 ecx, 1
  00037	f3 aa		 rep stosb
  00039	48 8d 44 24 21	 lea	 rax, QWORD PTR $S140$[rsp]
  0003e	48 89 44 24 50	 mov	 QWORD PTR _Alproxy$[rsp], rax

; 1975 :         return _Mypair._Myval2._Get_first();

  00043	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00048	48 89 44 24 38	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1975 :         return _Mypair._Myval2._Get_first();

  0004d	48 8b 44 24 38	 mov	 rax, QWORD PTR $T4[rsp]
  00052	48 89 44 24 40	 mov	 QWORD PTR $T5[rsp], rax

; 1956 :         _Container_proxy_ptr<_Alnode> _Proxy(_Alproxy, *_Scary);
; 1957 :         _Scary->_Myhead = _Node::_Buyheadnode(_Getal());

  00057	48 8b 44 24 40	 mov	 rax, QWORD PTR $T5[rsp]
  0005c	48 8b c8	 mov	 rcx, rax
  0005f	e8 00 00 00 00	 call	 ??$_Buyheadnode@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@SAPEAU01@AEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@@Z ; std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *>::_Buyheadnode<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >
  00064	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _Scary$[rsp]
  00069	48 89 01	 mov	 QWORD PTR [rcx], rax

; 1958 :         _Proxy._Release();
; 1959 :     }

  0006c	48 83 c4 60	 add	 rsp, 96			; 00000060H
  00070	5f		 pop	 rdi
  00071	c3		 ret	 0
?_Alloc_sentinel_and_proxy@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@IEAAXXZ ENDP ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::_Alloc_sentinel_and_proxy
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ?_Check_grow_by_1@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@IEAAXXZ
_TEXT	SEGMENT
$T1 = 32
$T2 = 40
this$ = 64
?_Check_grow_by_1@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@IEAAXXZ PROC ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::_Check_grow_by_1, COMDAT

; 1647 :     void _Check_grow_by_1() {

$LN46:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00009	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0000e	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00013	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00018	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax

; 1648 :         if (max_size() == _Get_scary()->_Mysize) {

  0001d	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00022	e8 00 00 00 00	 call	 ?max_size@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@QEBA_KXZ ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::max_size
  00027	48 8b 4c 24 28	 mov	 rcx, QWORD PTR $T2[rsp]
  0002c	48 3b 41 08	 cmp	 rax, QWORD PTR [rcx+8]
  00030	75 06		 jne	 SHORT $LN3@Check_grow

; 1649 :             _Throw_tree_length_error();

  00032	e8 00 00 00 00	 call	 ?_Throw_tree_length_error@std@@YAXXZ ; std::_Throw_tree_length_error
  00037	90		 npad	 1
$LN3@Check_grow:

; 1650 :         }
; 1651 :     }

  00038	48 83 c4 38	 add	 rsp, 56			; 00000038H
  0003c	c3		 ret	 0
?_Check_grow_by_1@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@IEAAXXZ ENDP ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::_Check_grow_by_1
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ?_Erase_unchecked@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@AEAAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z
_TEXT	SEGMENT
_Successor$ = 32
$T1 = 40
$T2 = 48
_Scary$ = 56
$T3 = 64
$T4 = 72
_Erasednode$ = 80
this$ = 112
_Where$ = 120
?_Erase_unchecked@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@AEAAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z PROC ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::_Erase_unchecked, COMDAT

; 1299 :     _Nodeptr _Erase_unchecked(_Unchecked_const_iterator _Where) noexcept {

$LN200:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 68	 sub	 rsp, 104		; 00000068H

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  0000e	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00013	48 89 44 24 28	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00018	48 8b 44 24 28	 mov	 rax, QWORD PTR $T1[rsp]
  0001d	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax

; 1300 :         const auto _Scary                    = _Get_scary();

  00022	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
  00027	48 89 44 24 38	 mov	 QWORD PTR _Scary$[rsp], rax

; 1301 :         _Unchecked_const_iterator _Successor = _Where;

  0002c	48 8b 44 24 78	 mov	 rax, QWORD PTR _Where$[rsp]
  00031	48 89 44 24 20	 mov	 QWORD PTR _Successor$[rsp], rax

; 1302 :         ++_Successor; // save successor iterator for return

  00036	48 8d 4c 24 20	 lea	 rcx, QWORD PTR _Successor$[rsp]
  0003b	e8 00 00 00 00	 call	 ??E?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@std@@QEAAAEAV01@XZ ; std::_Tree_unchecked_const_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >,std::_Iterator_base0>::operator++

; 1303 :         _Nodeptr _Erasednode = _Scary->_Extract(_Where); // node to erase

  00040	48 8b 54 24 78	 mov	 rdx, QWORD PTR _Where$[rsp]
  00045	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Scary$[rsp]
  0004a	e8 00 00 00 00	 call	 ?_Extract@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::_Extract
  0004f	48 89 44 24 50	 mov	 QWORD PTR _Erasednode$[rsp], rax

; 1975 :         return _Mypair._Myval2._Get_first();

  00054	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00059	48 89 44 24 40	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1975 :         return _Mypair._Myval2._Get_first();

  0005e	48 8b 44 24 40	 mov	 rax, QWORD PTR $T3[rsp]
  00063	48 89 44 24 48	 mov	 QWORD PTR $T4[rsp], rax

; 1304 :         _Scary->_Orphan_ptr(_Erasednode);
; 1305 :         _Node::_Freenode(_Getal(), _Erasednode); // delete erased node

  00068	48 8b 44 24 48	 mov	 rax, QWORD PTR $T4[rsp]
  0006d	48 8b 54 24 50	 mov	 rdx, QWORD PTR _Erasednode$[rsp]
  00072	48 8b c8	 mov	 rcx, rax
  00075	e8 00 00 00 00	 call	 ??$_Freenode@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@SAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@PEAU01@@Z ; std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *>::_Freenode<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >

; 1306 :         return _Successor._Ptr; // return successor nodeptr

  0007a	48 8b 44 24 20	 mov	 rax, QWORD PTR _Successor$[rsp]

; 1307 :     }

  0007f	48 83 c4 68	 add	 rsp, 104		; 00000068H
  00083	c3		 ret	 0
?_Erase_unchecked@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@AEAAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@V?$_Tree_unchecked_const_iterator@V?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@U_Iterator_base0@2@@2@@Z ENDP ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::_Erase_unchecked
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ?max_size@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@QEBA_KXZ
_TEXT	SEGMENT
$T1 = 0
$T2 = 8
tv67 = 16
$T3 = 24
$T4 = 32
$T5 = 40
$T6 = 48
$T7 = 56
$T8 = 64
_Unsigned_max$9 = 72
this$ = 96
?max_size@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@QEBA_KXZ PROC ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::max_size, COMDAT

; 1212 :     _NODISCARD size_type max_size() const noexcept {

$LN31:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 1979 :         return _Mypair._Myval2._Get_first();

  00009	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1543 :         return *this;

  0000e	48 89 44 24 18	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1979 :         return _Mypair._Myval2._Get_first();

  00013	48 8b 44 24 18	 mov	 rax, QWORD PTR $T3[rsp]
  00018	48 89 44 24 40	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 746  :         return static_cast<size_t>(-1) / sizeof(value_type);

  0001d	48 b8 8e e3 38
	8e e3 38 8e 03	 mov	 rax, 256204778801521550	; 038e38e38e38e38eH
  00027	48 89 44 24 20	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1213 :         return (_STD min)(

  0002c	48 8b 44 24 20	 mov	 rax, QWORD PTR $T4[rsp]
  00031	48 89 04 24	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 866  :         constexpr auto _Unsigned_max = static_cast<make_unsigned_t<_Ty>>(-1);

  00035	48 c7 44 24 48
	ff ff ff ff	 mov	 QWORD PTR _Unsigned_max$9[rsp], -1

; 867  :         return static_cast<_Ty>(_Unsigned_max >> 1);

  0003e	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  00048	48 89 44 24 28	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1213 :         return (_STD min)(

  0004d	48 8b 44 24 28	 mov	 rax, QWORD PTR $T5[rsp]
  00052	48 89 44 24 08	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 101  :     return _Right < _Left ? _Right : _Left;

  00057	48 8b 44 24 08	 mov	 rax, QWORD PTR $T2[rsp]
  0005c	48 39 04 24	 cmp	 QWORD PTR $T1[rsp], rax
  00060	73 0b		 jae	 SHORT $LN26@max_size
  00062	48 8d 04 24	 lea	 rax, QWORD PTR $T1[rsp]
  00066	48 89 44 24 10	 mov	 QWORD PTR tv67[rsp], rax
  0006b	eb 0a		 jmp	 SHORT $LN27@max_size
$LN26@max_size:
  0006d	48 8d 44 24 08	 lea	 rax, QWORD PTR $T2[rsp]
  00072	48 89 44 24 10	 mov	 QWORD PTR tv67[rsp], rax
$LN27@max_size:
  00077	48 8b 44 24 10	 mov	 rax, QWORD PTR tv67[rsp]
  0007c	48 89 44 24 30	 mov	 QWORD PTR $T6[rsp], rax
  00081	48 8b 44 24 30	 mov	 rax, QWORD PTR $T6[rsp]
  00086	48 89 44 24 38	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1213 :         return (_STD min)(

  0008b	48 8b 44 24 38	 mov	 rax, QWORD PTR $T7[rsp]
  00090	48 8b 00	 mov	 rax, QWORD PTR [rax]

; 1214 :             static_cast<size_type>(_STD _Max_limit<difference_type>()), _Alnode_traits::max_size(_Getal()));
; 1215 :     }

  00093	48 83 c4 58	 add	 rsp, 88			; 00000058H
  00097	c3		 ret	 0
?max_size@?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@QEBA_KXZ ENDP ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::max_size
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ??1?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@QEAA@XZ
_TEXT	SEGMENT
$T1 = 32
$T2 = 40
$T3 = 48
$T4 = 56
_Scary$ = 64
this$ = 96
??1?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@QEAA@XZ PROC ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::~_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >, COMDAT

; 1095 :     ~_Tree() noexcept {

$LN154:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00009	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0000e	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1983 :         return _STD addressof(_Mypair._Myval2._Myval2);

  00013	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00018	48 89 44 24 28	 mov	 QWORD PTR $T2[rsp], rax

; 1096 :         const auto _Scary = _Get_scary();

  0001d	48 8b 44 24 28	 mov	 rax, QWORD PTR $T2[rsp]
  00022	48 89 44 24 40	 mov	 QWORD PTR _Scary$[rsp], rax

; 1975 :         return _Mypair._Myval2._Get_first();

  00027	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  0002c	48 89 44 24 30	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree

; 1975 :         return _Mypair._Myval2._Get_first();

  00031	48 8b 44 24 30	 mov	 rax, QWORD PTR $T3[rsp]
  00036	48 89 44 24 38	 mov	 QWORD PTR $T4[rsp], rax

; 1097 :         _Scary->_Erase_head(_Getal());

  0003b	48 8b 44 24 38	 mov	 rax, QWORD PTR $T4[rsp]
  00040	48 8b d0	 mov	 rdx, rax
  00043	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Scary$[rsp]
  00048	e8 00 00 00 00	 call	 ??$_Erase_head@V?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@@?$_Tree_val@U?$_Tree_simple_types@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@std@@@std@@QEAAXAEAV?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@1@@Z ; std::_Tree_val<std::_Tree_simple_types<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> > >::_Erase_head<std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> > >
  0004d	90		 npad	 1

; 1098 : #if _ITERATOR_DEBUG_LEVEL != 0 // TRANSITION, ABI
; 1099 :         auto&& _Alproxy = _GET_PROXY_ALLOCATOR(_Alnode, _Getal());
; 1100 :         _Delete_plain_internal(_Alproxy, _Scary->_Myproxy);
; 1101 : #endif // _ITERATOR_DEBUG_LEVEL != 0
; 1102 :     }

  0004e	48 83 c4 58	 add	 rsp, 88			; 00000058H
  00052	c3		 ret	 0
??1?$_Tree@V?$_Tmap_traits@IUMissionMapJoinInfo@MissionMapJoinManager@mu2@@U?$less@I@std@@V?$allocator@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@@5@$0A@@std@@@std@@QEAA@XZ ENDP ; std::_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >::~_Tree<std::_Tmap_traits<unsigned int,mu2::MissionMapJoinManager::MissionMapJoinInfo,std::less<unsigned int>,std::allocator<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo> >,0> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?allocate@?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@_K@Z
_TEXT	SEGMENT
_Overflow_is_possible$1 = 32
_Bytes$ = 40
$T2 = 48
$T3 = 56
$T4 = 64
_Max_possible$5 = 72
this$ = 96
_Count$ = 104
?allocate@?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@_K@Z PROC ; std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> >::allocate, COMDAT

; 988  :     _NODISCARD_RAW_PTR_ALLOC _CONSTEXPR20 __declspec(allocator) _Ty* allocate(_CRT_GUARDOVERFLOW const size_t _Count) {

$LN13:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 113  :     constexpr bool _Overflow_is_possible = _Ty_size > 1;

  0000e	c6 44 24 20 01	 mov	 BYTE PTR _Overflow_is_possible$1[rsp], 1

; 114  : 
; 115  :     if constexpr (_Overflow_is_possible) {
; 116  :         constexpr size_t _Max_possible = static_cast<size_t>(-1) / _Ty_size;

  00013	48 b8 8e e3 38
	8e e3 38 8e 03	 mov	 rax, 256204778801521550	; 038e38e38e38e38eH
  0001d	48 89 44 24 48	 mov	 QWORD PTR _Max_possible$5[rsp], rax

; 117  :         if (_Count > _Max_possible) {

  00022	48 b8 8e e3 38
	8e e3 38 8e 03	 mov	 rax, 256204778801521550	; 038e38e38e38e38eH
  0002c	48 39 44 24 68	 cmp	 QWORD PTR _Count$[rsp], rax
  00031	76 06		 jbe	 SHORT $LN4@allocate

; 118  :             _Throw_bad_array_new_length(); // multiply overflow

  00033	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00038	90		 npad	 1
$LN4@allocate:

; 119  :         }
; 120  :     }
; 121  : 
; 122  :     return _Count * _Ty_size;

  00039	48 6b 44 24 68
	48		 imul	 rax, QWORD PTR _Count$[rsp], 72 ; 00000048H
  0003f	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00044	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
  00049	48 89 44 24 28	 mov	 QWORD PTR _Bytes$[rsp], rax

; 227  :     if (_Bytes == 0) {

  0004e	48 83 7c 24 28
	00		 cmp	 QWORD PTR _Bytes$[rsp], 0
  00054	75 0b		 jne	 SHORT $LN8@allocate

; 228  :         return nullptr;

  00056	48 c7 44 24 30
	00 00 00 00	 mov	 QWORD PTR $T2[rsp], 0
  0005f	eb 35		 jmp	 SHORT $LN7@allocate
$LN8@allocate:

; 229  :     }
; 230  : 
; 231  : #if _HAS_CXX20 // TRANSITION, GH-1532
; 232  :     if (_STD is_constant_evaluated()) {
; 233  :         return _Traits::_Allocate(_Bytes);
; 234  :     }
; 235  : #endif // _HAS_CXX20
; 236  : 
; 237  : #ifdef __cpp_aligned_new
; 238  :     if constexpr (_Align > __STDCPP_DEFAULT_NEW_ALIGNMENT__) {
; 239  :         size_t _Passed_align = _Align;
; 240  : #if defined(_M_IX86) || defined(_M_X64)
; 241  :         if (_Bytes >= _Big_allocation_threshold) {
; 242  :             // boost the alignment of big allocations to help autovectorization
; 243  :             _Passed_align = (_STD max)(_Align, _Big_allocation_alignment);
; 244  :         }
; 245  : #endif // defined(_M_IX86) || defined(_M_X64)
; 246  :         return _Traits::_Allocate_aligned(_Bytes, _Passed_align);
; 247  :     } else
; 248  : #endif // defined(__cpp_aligned_new)
; 249  :     {
; 250  : #if defined(_M_IX86) || defined(_M_X64)
; 251  :         if (_Bytes >= _Big_allocation_threshold) {

  00061	48 81 7c 24 28
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  0006a	72 11		 jb	 SHORT $LN9@allocate

; 252  :             // boost the alignment of big allocations to help autovectorization
; 253  :             return _Allocate_manually_vector_aligned<_Traits>(_Bytes);

  0006c	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00071	e8 00 00 00 00	 call	 ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
  00076	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
  0007b	eb 19		 jmp	 SHORT $LN7@allocate
$LN9@allocate:

; 136  :         return ::operator new(_Bytes);

  0007d	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00082	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00087	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax

; 256  :         return _Traits::_Allocate(_Bytes);

  0008c	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  00091	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
$LN7@allocate:

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00096	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
$LN6@allocate:

; 991  :     }

  0009b	48 83 c4 58	 add	 rsp, 88			; 00000058H
  0009f	c3		 ret	 0
?allocate@?$allocator@U?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@std@@@std@@QEAAPEAU?$_Tree_node@U?$pair@$$CBIUMissionMapJoinInfo@MissionMapJoinManager@mu2@@@std@@PEAX@2@_K@Z ENDP ; std::allocator<std::_Tree_node<std::pair<unsigned int const ,mu2::MissionMapJoinManager::MissionMapJoinInfo>,void *> >::allocate
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ??1MissionMapJoinInfo@MissionMapJoinManager@mu2@@QEAA@XZ
_TEXT	SEGMENT
this$ = 8
??1MissionMapJoinInfo@MissionMapJoinManager@mu2@@QEAA@XZ PROC ; mu2::MissionMapJoinManager::MissionMapJoinInfo::~MissionMapJoinInfo, COMDAT
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 172  : 	virtual~ISerializer() {}

  00005	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0000a	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  00011	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx
  00015	c3		 ret	 0
??1MissionMapJoinInfo@MissionMapJoinManager@mu2@@QEAA@XZ ENDP ; mu2::MissionMapJoinManager::MissionMapJoinInfo::~MissionMapJoinInfo
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??_G?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_G?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@UEAAPEAXI@Z PROC ; mu2::ISingleton<mu2::MissionMapJoinManager>::`scalar deleting destructor', COMDAT
$LN10:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 80   : 	virtual~ISingleton() {}

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx
  0001c	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00020	83 e0 01	 and	 eax, 1
  00023	85 c0		 test	 eax, eax
  00025	74 10		 je	 SHORT $LN2@scalar
  00027	ba 08 00 00 00	 mov	 edx, 8
  0002c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00031	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00036	90		 npad	 1
$LN2@scalar:
  00037	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003c	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00040	c3		 ret	 0
??_G?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@UEAAPEAXI@Z ENDP ; mu2::ISingleton<mu2::MissionMapJoinManager>::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??1?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@UEAA@XZ
_TEXT	SEGMENT
this$ = 8
??1?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@UEAA@XZ PROC ; mu2::ISingleton<mu2::MissionMapJoinManager>::~ISingleton<mu2::MissionMapJoinManager>, COMDAT

; 80   : 	virtual~ISingleton() {}

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0000a	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@6B@
  00011	48 89 08	 mov	 QWORD PTR [rax], rcx
  00014	c3		 ret	 0
??1?$ISingleton@VMissionMapJoinManager@mu2@@@mu2@@UEAA@XZ ENDP ; mu2::ISingleton<mu2::MissionMapJoinManager>::~ISingleton<mu2::MissionMapJoinManager>
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Net\Common\PacketStream.h
;	COMDAT ??_GISerializer@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GISerializer@mu2@@UEAAPEAXI@Z PROC			; mu2::ISerializer::`scalar deleting destructor', COMDAT
$LN10:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 172  : 	virtual~ISerializer() {}

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7ISerializer@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx
  0001c	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00020	83 e0 01	 and	 eax, 1
  00023	85 c0		 test	 eax, eax
  00025	74 10		 je	 SHORT $LN2@scalar
  00027	ba 08 00 00 00	 mov	 edx, 8
  0002c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00031	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00036	90		 npad	 1
$LN2@scalar:
  00037	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003c	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00040	c3		 ret	 0
??_GISerializer@mu2@@UEAAPEAXI@Z ENDP			; mu2::ISerializer::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree
;	COMDAT ?_Throw_tree_length_error@std@@YAXXZ
_TEXT	SEGMENT
?_Throw_tree_length_error@std@@YAXXZ PROC		; std::_Throw_tree_length_error, COMDAT

; 416  : [[noreturn]] inline void _Throw_tree_length_error() {

$LN3:
  00000	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 417  :     _Xlength_error("map/set too long");

  00004	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_0BB@GCADKGJO@map?1set?5too?5long@
  0000b	e8 00 00 00 00	 call	 ?_Xlength_error@std@@YAXPEBD@Z ; std::_Xlength_error
  00010	90		 npad	 1
$LN2@Throw_tree:

; 418  : }

  00011	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00015	c3		 ret	 0
?_Throw_tree_length_error@std@@YAXXZ ENDP		; std::_Throw_tree_length_error
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z
_TEXT	SEGMENT
_Back_shift$ = 48
_Ptr_container$ = 56
_Ptr_user$ = 64
_Min_back_shift$ = 72
_Ptr$ = 96
_Bytes$ = 104
?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z PROC ; std::_Adjust_manually_vector_aligned, COMDAT

; 200  : inline void _Adjust_manually_vector_aligned(void*& _Ptr, size_t& _Bytes) {

$LN5:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 201  :     // adjust parameters from _Allocate_manually_vector_aligned to pass to operator delete
; 202  :     _Bytes += _Non_user_size;

  0000e	48 8b 44 24 68	 mov	 rax, QWORD PTR _Bytes$[rsp]
  00013	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00016	48 83 c0 27	 add	 rax, 39			; 00000027H
  0001a	48 8b 4c 24 68	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  0001f	48 89 01	 mov	 QWORD PTR [rcx], rax

; 203  : 
; 204  :     const uintptr_t* const _Ptr_user = static_cast<uintptr_t*>(_Ptr);

  00022	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00027	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0002a	48 89 44 24 40	 mov	 QWORD PTR _Ptr_user$[rsp], rax

; 205  :     const uintptr_t _Ptr_container   = _Ptr_user[-1];

  0002f	b8 08 00 00 00	 mov	 eax, 8
  00034	48 6b c0 ff	 imul	 rax, rax, -1
  00038	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr_user$[rsp]
  0003d	48 8b 04 01	 mov	 rax, QWORD PTR [rcx+rax]
  00041	48 89 44 24 38	 mov	 QWORD PTR _Ptr_container$[rsp], rax

; 206  : 
; 207  :     // If the following asserts, it likely means that we are performing
; 208  :     // an aligned delete on memory coming from an unaligned allocation.
; 209  :     _STL_ASSERT(_Ptr_user[-2] == _Big_allocation_sentinel, "invalid argument");
; 210  : 
; 211  :     // Extra paranoia on aligned allocation/deallocation; ensure _Ptr_container is
; 212  :     // in range [_Min_back_shift, _Non_user_size]
; 213  : #ifdef _DEBUG
; 214  :     constexpr uintptr_t _Min_back_shift = 2 * sizeof(void*);
; 215  : #else // ^^^ defined(_DEBUG) / !defined(_DEBUG) vvv
; 216  :     constexpr uintptr_t _Min_back_shift = sizeof(void*);

  00046	48 c7 44 24 48
	08 00 00 00	 mov	 QWORD PTR _Min_back_shift$[rsp], 8

; 217  : #endif // ^^^ !defined(_DEBUG) ^^^
; 218  :     const uintptr_t _Back_shift = reinterpret_cast<uintptr_t>(_Ptr) - _Ptr_container;

  0004f	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00054	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr_container$[rsp]
  00059	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0005c	48 2b c1	 sub	 rax, rcx
  0005f	48 89 44 24 30	 mov	 QWORD PTR _Back_shift$[rsp], rax

; 219  :     _STL_VERIFY(_Back_shift >= _Min_back_shift && _Back_shift <= _Non_user_size, "invalid argument");

  00064	48 83 7c 24 30
	08		 cmp	 QWORD PTR _Back_shift$[rsp], 8
  0006a	72 08		 jb	 SHORT $LN3@Adjust_man
  0006c	48 83 7c 24 30
	27		 cmp	 QWORD PTR _Back_shift$[rsp], 39 ; 00000027H
  00072	76 19		 jbe	 SHORT $LN2@Adjust_man
$LN3@Adjust_man:
  00074	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR [rsp+32], 0
  0007d	45 33 c9	 xor	 r9d, r9d
  00080	45 33 c0	 xor	 r8d, r8d
  00083	33 d2		 xor	 edx, edx
  00085	33 c9		 xor	 ecx, ecx
  00087	e8 00 00 00 00	 call	 _invoke_watson
  0008c	90		 npad	 1
$LN2@Adjust_man:

; 220  :     _Ptr = reinterpret_cast<void*>(_Ptr_container);

  0008d	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00092	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr_container$[rsp]
  00097	48 89 08	 mov	 QWORD PTR [rax], rcx
$LN4@Adjust_man:

; 221  : }

  0009a	48 83 c4 58	 add	 rsp, 88			; 00000058H
  0009e	c3		 ret	 0
?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ENDP ; std::_Adjust_manually_vector_aligned
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?_Throw_bad_array_new_length@std@@YAXXZ
_TEXT	SEGMENT
$T1 = 32
?_Throw_bad_array_new_length@std@@YAXXZ PROC		; std::_Throw_bad_array_new_length, COMDAT

; 107  : [[noreturn]] inline void _Throw_bad_array_new_length() {

$LN3:
  00000	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 108  :     _THROW(bad_array_new_length{});

  00004	48 8d 4c 24 20	 lea	 rcx, QWORD PTR $T1[rsp]
  00009	e8 00 00 00 00	 call	 ??0bad_array_new_length@std@@QEAA@XZ ; std::bad_array_new_length::bad_array_new_length
  0000e	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:_TI3?AVbad_array_new_length@std@@
  00015	48 8d 4c 24 20	 lea	 rcx, QWORD PTR $T1[rsp]
  0001a	e8 00 00 00 00	 call	 _CxxThrowException
  0001f	90		 npad	 1
$LN2@Throw_bad_:

; 109  : }

  00020	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00024	c3		 ret	 0
?_Throw_bad_array_new_length@std@@YAXXZ ENDP		; std::_Throw_bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gbad_array_new_length@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gbad_array_new_length@std@@UEAAPEAXI@Z PROC		; std::bad_array_new_length::`scalar deleting destructor', COMDAT
$LN20:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gbad_array_new_length@std@@UEAAPEAXI@Z ENDP		; std::bad_array_new_length::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??0bad_array_new_length@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
__that$ = 56
??0bad_array_new_length@std@@QEAA@AEBV01@@Z PROC	; std::bad_array_new_length::bad_array_new_length, COMDAT
$LN14:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000e	48 8b 54 24 38	 mov	 rdx, QWORD PTR __that$[rsp]
  00013	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00018	e8 00 00 00 00	 call	 ??0bad_alloc@std@@QEAA@AEBV01@@Z
  0001d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00022	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_array_new_length@std@@6B@
  00029	48 89 08	 mov	 QWORD PTR [rax], rcx
  0002c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00031	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00035	c3		 ret	 0
??0bad_array_new_length@std@@QEAA@AEBV01@@Z ENDP	; std::bad_array_new_length::bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??1bad_array_new_length@std@@UEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1bad_array_new_length@std@@UEAA@XZ PROC		; std::bad_array_new_length::~bad_array_new_length, COMDAT
$LN14:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  00018	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 83 c0 08	 add	 rax, 8
  00021	48 8b c8	 mov	 rcx, rax
  00024	e8 00 00 00 00	 call	 __std_exception_destroy
  00029	90		 npad	 1
  0002a	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0002e	c3		 ret	 0
??1bad_array_new_length@std@@UEAA@XZ ENDP		; std::bad_array_new_length::~bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0bad_array_new_length@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 16
??0bad_array_new_length@std@@QEAA@XZ PROC		; std::bad_array_new_length::bad_array_new_length, COMDAT

; 144  :     {

$LN14:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi

; 67   :     {

  00006	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0000b	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00012	48 89 08	 mov	 QWORD PTR [rax], rcx

; 66   :         : _Data()

  00015	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 83 c0 08	 add	 rax, 8
  0001e	48 8b f8	 mov	 rdi, rax
  00021	33 c0		 xor	 eax, eax
  00023	b9 10 00 00 00	 mov	 ecx, 16
  00028	f3 aa		 rep stosb

; 68   :         _Data._What = _Message;

  0002a	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0002f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_0BF@KINCDENJ@bad?5array?5new?5length@
  00036	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 133  :     {

  0003a	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0003f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_alloc@std@@6B@
  00046	48 89 08	 mov	 QWORD PTR [rax], rcx

; 144  :     {

  00049	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_array_new_length@std@@6B@
  00055	48 89 08	 mov	 QWORD PTR [rax], rcx

; 145  :     }

  00058	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0005d	5f		 pop	 rdi
  0005e	c3		 ret	 0
??0bad_array_new_length@std@@QEAA@XZ ENDP		; std::bad_array_new_length::bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gbad_alloc@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gbad_alloc@std@@UEAAPEAXI@Z PROC			; std::bad_alloc::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gbad_alloc@std@@UEAAPEAXI@Z ENDP			; std::bad_alloc::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0bad_alloc@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
__that$ = 56
??0bad_alloc@std@@QEAA@AEBV01@@Z PROC			; std::bad_alloc::bad_alloc, COMDAT
$LN9:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 20	 sub	 rsp, 32			; 00000020H

; 73   :     {

  0000f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  0001b	48 89 08	 mov	 QWORD PTR [rax], rcx

; 72   :         : _Data()

  0001e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 83 c0 08	 add	 rax, 8
  00027	48 8b f8	 mov	 rdi, rax
  0002a	33 c0		 xor	 eax, eax
  0002c	b9 10 00 00 00	 mov	 ecx, 16
  00031	f3 aa		 rep stosb

; 74   :         __std_exception_copy(&_Other._Data, &_Data);

  00033	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 83 c0 08	 add	 rax, 8
  0003c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR __that$[rsp]
  00041	48 83 c1 08	 add	 rcx, 8
  00045	48 8b d0	 mov	 rdx, rax
  00048	e8 00 00 00 00	 call	 __std_exception_copy
  0004d	90		 npad	 1
  0004e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_alloc@std@@6B@
  0005a	48 89 08	 mov	 QWORD PTR [rax], rcx
  0005d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00062	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00066	5f		 pop	 rdi
  00067	c3		 ret	 0
??0bad_alloc@std@@QEAA@AEBV01@@Z ENDP			; std::bad_alloc::bad_alloc
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gexception@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gexception@std@@UEAAPEAXI@Z PROC			; std::exception::`scalar deleting destructor', COMDAT
$LN10:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gexception@std@@UEAAPEAXI@Z ENDP			; std::exception::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ?what@exception@std@@UEBAPEBDXZ
_TEXT	SEGMENT
tv69 = 0
this$ = 32
?what@exception@std@@UEBAPEBDXZ PROC			; std::exception::what, COMDAT

; 95   :     {

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 18	 sub	 rsp, 24

; 96   :         return _Data._What ? _Data._What : "Unknown exception";

  00009	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00013	74 0f		 je	 SHORT $LN3@what
  00015	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0001e	48 89 04 24	 mov	 QWORD PTR tv69[rsp], rax
  00022	eb 0b		 jmp	 SHORT $LN4@what
$LN3@what:
  00024	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BC@EOODALEL@Unknown?5exception@
  0002b	48 89 04 24	 mov	 QWORD PTR tv69[rsp], rax
$LN4@what:
  0002f	48 8b 04 24	 mov	 rax, QWORD PTR tv69[rsp]

; 97   :     }

  00033	48 83 c4 18	 add	 rsp, 24
  00037	c3		 ret	 0
?what@exception@std@@UEBAPEBDXZ ENDP			; std::exception::what
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0exception@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
_Other$ = 56
??0exception@std@@QEAA@AEBV01@@Z PROC			; std::exception::exception, COMDAT

; 73   :     {

$LN4:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  0000f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  0001b	48 89 08	 mov	 QWORD PTR [rax], rcx

; 72   :         : _Data()

  0001e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 83 c0 08	 add	 rax, 8
  00027	48 8b f8	 mov	 rdi, rax
  0002a	33 c0		 xor	 eax, eax
  0002c	b9 10 00 00 00	 mov	 ecx, 16
  00031	f3 aa		 rep stosb

; 74   :         __std_exception_copy(&_Other._Data, &_Data);

  00033	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 83 c0 08	 add	 rax, 8
  0003c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Other$[rsp]
  00041	48 83 c1 08	 add	 rcx, 8
  00045	48 8b d0	 mov	 rdx, rax
  00048	e8 00 00 00 00	 call	 __std_exception_copy
  0004d	90		 npad	 1

; 75   :     }

  0004e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00057	5f		 pop	 rdi
  00058	c3		 ret	 0
??0exception@std@@QEAA@AEBV01@@Z ENDP			; std::exception::exception
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\MissionMapJoinManager.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
