﻿#pragma once

#include <Backend/Zone/Element/Item/InvenSlotable.h>

namespace mu2
{

	//====================================================================================================
	//
	//====================================================================================================

	class alignas(32) InvenEquip : public InvenSlotable
	{
	public:
		explicit InvenEquip(EntityPlayer& owner, eInvenType eType, const SlotSizeType maxSlot, Bool isActiveAdd, Bool isActiveSub)
			: InvenSlotable(owner, eType, maxSlot, isActiveAdd, isActiveSub)
		{
			m_direction[SOURCE] = true;
			m_direction[TARGET] = true;

			m_emergencyRange = InvenBags;
		}
		virtual ~InvenEquip() override {}
		
	public:
		Bool						IsFullWeapons(WeaponType::Type& left, WeaponType::Type& right) const;	// 양손모두 무기를 장착했는가
		Bool						EnchantAllByGM(EnchantLevel tryEnchantLevel);
		eSlotType					CheckUnequipSlotToEquip(const ItemDivisionType::Enum division) const;
		eSlotType					CastEquipDivision2Slot(const ItemDivisionType::Enum division) const;
		virtual void				OnNotifyPushed(const ItemPtr& item) override;
		virtual void				OnNotifyPoped(const ItemPtr& item) override;
		void						FillupAppearanceInfo(std::vector<ItemAppearanceInfo>& datas) const;
		void						ReduceDurability(Bool isDead);
		void						Save2DB4Durability();		
		
		Bool						IsEquipedShield() const;
		AbilityValue				GetWeaponMinDamage(const BattlePvpAbilityCorElem* pvp) const;
		AbilityValue				GetWeaponMaxDamage(const BattlePvpAbilityCorElem* pvp) const;
		AbilityValue				GetDefenseAll(const BattlePvpAbilityCorElem* pvp) const;
		AbilityFloat				GetNormalized_DUAL_SWORD_COR() const;
		AbilityValue				GetWeaponAttackSpeed(const PlayerAbilityInfo &info, AbilityValue defaultValue) const;

	private:
		virtual Bool				IsEquipBag() const { return true; }
		virtual Bool				IsPushable(const eSlotType& toSlot, const ItemConstPtr& item, Bool isAllowBlockNExpired = false) const override;
		void						onEquiped(const ItemPlace& toPlace, const ItemPtr& equipableItem);
		void						onUnequiped(const ItemPlace& toPlace, const ItemPtr& unEquipedItem);
		Bool						checkEnableWeaponEquipSlot(eSlotType toSlot, const ItemConstPtr& sourceItem) const;
		Bool						checkItemPassiveSkill(const ItemConstPtr& item, eSlotType toSlot) const;
	};
}