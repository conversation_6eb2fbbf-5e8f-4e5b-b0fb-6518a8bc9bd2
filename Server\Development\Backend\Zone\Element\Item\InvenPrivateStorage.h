﻿#pragma once

#pragma once

#include <Backend/Zone/Element/Item/InvenSlotable.h>

namespace mu2
{
	//====================================================================================================
	//
	//====================================================================================================

	class alignas(32) InvenStoragePrivate : public InvenStorage
	{
	public:
		explicit InvenStoragePrivate(EntityPlayer& owner, eInvenType eType, const SlotSizeType maxSlot, Bool isActiveAdd, Bool isActiveSub)
			: InvenStorage(owner, eType, maxSlot, isActiveAdd, isActiveSub)
		{
			m_direction[SOURCE] = true;
			m_direction[TARGET] = true;

			m_emergencyRange = PrivateStorages;
		}
		virtual ~InvenStoragePrivate() override {}

		virtual Bool		IsStoragePrivate() const { return true; }
		virtual Bool		IsPushable(const eSlotType& toSlot, const ItemConstPtr& item, Bool isAllowBlockNExpired = false) const override;
		virtual Bool		IsUsableRemoteNpcFunction() const override;

		Int32				GetStorageNumber() const;	//1부터 시작
		Bool				IsOpened() const;

		ErrorItem::Error	CheckUnlockPrivateStorageTab();
		ErrorItem::Error	UnlockPrivateStorageTab();

		static Bool					TestClearItemPrivateStorageAll(EntityPlayer* player);
		static Bool					TestUnlockPrivateStorageTabAll(EntityPlayer* player);
		static Bool					TestResetPrivateStorageTab(EntityPlayer* player);
		static void					SetPrivateStorageUnlockedInvenType(EntityPlayer* player, eInvenType unlockedInvenType);
		static InvenStoragePrivate*	GetNextOpenablePrivateStorage(EntityPlayer* player);
		static eInvenType			GetLastOpenedPrivateStorage(EntityPlayer* player);
	};

}
