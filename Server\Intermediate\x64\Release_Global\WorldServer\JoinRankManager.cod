; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	??0exception@std@@QEAA@AEBV01@@Z		; std::exception::exception
PUBLIC	?what@exception@std@@UEBAPEBDXZ			; std::exception::what
PUBLIC	??_Gexception@std@@UEAAPEAXI@Z			; std::exception::`scalar deleting destructor'
PUBLIC	??0bad_alloc@std@@QEAA@AEBV01@@Z		; std::bad_alloc::bad_alloc
PUBLIC	??_Gbad_alloc@std@@UEAAPEAXI@Z			; std::bad_alloc::`scalar deleting destructor'
PUBL<PERSON>	??0bad_array_new_length@std@@QEAA@XZ		; std::bad_array_new_length::bad_array_new_length
PUBLIC	??1bad_array_new_length@std@@UEAA@XZ		; std::bad_array_new_length::~bad_array_new_length
PUBLIC	??0bad_array_new_length@std@@QEAA@AEBV01@@Z	; std::bad_array_new_length::bad_array_new_length
PUBLIC	??_Gbad_array_new_length@std@@UEAAPEAXI@Z	; std::bad_array_new_length::`scalar deleting destructor'
PUBLIC	?_Throw_bad_array_new_length@std@@YAXXZ		; std::_Throw_bad_array_new_length
PUBLIC	?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
PUBLIC	??1?$ISingleton@VJoinRankManager@mu2@@@mu2@@UEAA@XZ ; mu2::ISingleton<mu2::JoinRankManager>::~ISingleton<mu2::JoinRankManager>
PUBLIC	??_G?$ISingleton@VJoinRankManager@mu2@@@mu2@@UEAAPEAXI@Z ; mu2::ISingleton<mu2::JoinRankManager>::`scalar deleting destructor'
PUBLIC	?allocate@?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@QEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@2@_K@Z ; std::allocator<std::_List_node<mu2::JoinRankManager::RankInfo,void *> >::allocate
PUBLIC	??1?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@QEAA@XZ ; std::list<mu2::JoinRankManager::RankInfo,std::allocator<mu2::JoinRankManager::RankInfo> >::~list<mu2::JoinRankManager::RankInfo,std::allocator<mu2::JoinRankManager::RankInfo> >
PUBLIC	?erase@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@QEAA?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@URankInfo@JoinRankManager@mu2@@@std@@@std@@@2@V?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@URankInfo@JoinRankManager@mu2@@@std@@@std@@@2@@Z ; std::list<mu2::JoinRankManager::RankInfo,std::allocator<mu2::JoinRankManager::RankInfo> >::erase
PUBLIC	?_Unchecked_erase@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@AEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@2@QEAU32@@Z ; std::list<mu2::JoinRankManager::RankInfo,std::allocator<mu2::JoinRankManager::RankInfo> >::_Unchecked_erase
PUBLIC	?_Tidy@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@AEAAXXZ ; std::list<mu2::JoinRankManager::RankInfo,std::allocator<mu2::JoinRankManager::RankInfo> >::_Tidy
PUBLIC	?_Alloc_sentinel_and_proxy@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@AEAAXXZ ; std::list<mu2::JoinRankManager::RankInfo,std::allocator<mu2::JoinRankManager::RankInfo> >::_Alloc_sentinel_and_proxy
PUBLIC	??1JoinRankManager@mu2@@UEAA@XZ			; mu2::JoinRankManager::~JoinRankManager
PUBLIC	?Init@JoinRankManager@mu2@@UEAA_NPEAX@Z		; mu2::JoinRankManager::Init
PUBLIC	?UnInit@JoinRankManager@mu2@@UEAA_NXZ		; mu2::JoinRankManager::UnInit
PUBLIC	?PushPlayer@JoinRankManager@mu2@@QEAAIAEBV?$SmartPtrEx@VEntityPlayer@mu2@@@2@AEAI@Z ; mu2::JoinRankManager::PushPlayer
PUBLIC	?PopPlayer@JoinRankManager@mu2@@QEAAIXZ		; mu2::JoinRankManager::PopPlayer
PUBLIC	?UpdateJoinWaitSecond@JoinRankManager@mu2@@QEAAXXZ ; mu2::JoinRankManager::UpdateJoinWaitSecond
PUBLIC	?GetJoinWaitSecond@JoinRankManager@mu2@@QEAAII@Z ; mu2::JoinRankManager::GetJoinWaitSecond
PUBLIC	?LeaveWaitLine@JoinRankManager@mu2@@QEAAXI@Z	; mu2::JoinRankManager::LeaveWaitLine
PUBLIC	?GetWaitList@JoinRankManager@mu2@@QEAAAEAV?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@XZ ; mu2::JoinRankManager::GetWaitList
PUBLIC	?GetFirstWaitUserIndex@JoinRankManager@mu2@@QEAAIXZ ; mu2::JoinRankManager::GetFirstWaitUserIndex
PUBLIC	?GetWaitUserCount@JoinRankManager@mu2@@QEBAIXZ	; mu2::JoinRankManager::GetWaitUserCount
PUBLIC	??0JoinRankManager@mu2@@AEAA@XZ			; mu2::JoinRankManager::JoinRankManager
PUBLIC	??_GJoinRankManager@mu2@@UEAAPEAXI@Z		; mu2::JoinRankManager::`scalar deleting destructor'
PUBLIC	??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
PUBLIC	??$_Emplace@AEAURankInfo@JoinRankManager@mu2@@@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@QEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@1@QEAU21@AEAURankInfo@JoinRankManager@mu2@@@Z ; std::list<mu2::JoinRankManager::RankInfo,std::allocator<mu2::JoinRankManager::RankInfo> >::_Emplace<mu2::JoinRankManager::RankInfo &>
PUBLIC	??1?$_Alloc_construct_ptr@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAA@XZ ; std::_Alloc_construct_ptr<std::allocator<std::_List_node<mu2::JoinRankManager::RankInfo,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<mu2::JoinRankManager::RankInfo,void *> > >
PUBLIC	?_Transfer_before@?$_List_node_emplace_op2@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@2@QEAU32@@Z ; std::_List_node_emplace_op2<std::allocator<std::_List_node<mu2::JoinRankManager::RankInfo,void *> > >::_Transfer_before
PUBLIC	??$?0AEAURankInfo@JoinRankManager@mu2@@@?$_List_node_emplace_op2@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@1@AEAURankInfo@JoinRankManager@mu2@@@Z ; std::_List_node_emplace_op2<std::allocator<std::_List_node<mu2::JoinRankManager::RankInfo,void *> > >::_List_node_emplace_op2<std::allocator<std::_List_node<mu2::JoinRankManager::RankInfo,void *> > ><mu2::JoinRankManager::RankInfo &>
PUBLIC	??$_Free_non_head@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@SAXAEAV?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@1@PEAU01@@Z ; std::_List_node<mu2::JoinRankManager::RankInfo,void *>::_Free_non_head<std::allocator<std::_List_node<mu2::JoinRankManager::RankInfo,void *> > >
PUBLIC	??_7exception@std@@6B@				; std::exception::`vftable'
PUBLIC	??_C@_0BC@EOODALEL@Unknown?5exception@		; `string'
PUBLIC	??_7bad_alloc@std@@6B@				; std::bad_alloc::`vftable'
PUBLIC	??_7bad_array_new_length@std@@6B@		; std::bad_array_new_length::`vftable'
PUBLIC	??_C@_0BF@KINCDENJ@bad?5array?5new?5length@	; `string'
PUBLIC	??_R0?AVexception@std@@@8			; std::exception `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
PUBLIC	_TI3?AVbad_array_new_length@std@@
PUBLIC	_CTA3?AVbad_array_new_length@std@@
PUBLIC	??_R0?AVbad_array_new_length@std@@@8		; std::bad_array_new_length `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
PUBLIC	??_R0?AVbad_alloc@std@@@8			; std::bad_alloc `RTTI Type Descriptor'
PUBLIC	_CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
PUBLIC	??_C@_09OLBBGJEO@Assertion@			; `string'
PUBLIC	??_7?$ISingleton@VJoinRankManager@mu2@@@mu2@@6B@ ; mu2::ISingleton<mu2::JoinRankManager>::`vftable'
PUBLIC	??_7JoinRankManager@mu2@@6B@			; mu2::JoinRankManager::`vftable'
PUBLIC	??_C@_0O@NKNMEGII@list?5too?5long@		; `string'
PUBLIC	??_R4exception@std@@6B@				; std::exception::`RTTI Complete Object Locator'
PUBLIC	??_R3exception@std@@8				; std::exception::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2exception@std@@8				; std::exception::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@exception@std@@8			; std::exception::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R4bad_array_new_length@std@@6B@		; std::bad_array_new_length::`RTTI Complete Object Locator'
PUBLIC	??_R3bad_array_new_length@std@@8		; std::bad_array_new_length::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2bad_array_new_length@std@@8		; std::bad_array_new_length::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@bad_array_new_length@std@@8	; std::bad_array_new_length::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@bad_alloc@std@@8			; std::bad_alloc::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R3bad_alloc@std@@8				; std::bad_alloc::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2bad_alloc@std@@8				; std::bad_alloc::`RTTI Base Class Array'
PUBLIC	??_R4bad_alloc@std@@6B@				; std::bad_alloc::`RTTI Complete Object Locator'
PUBLIC	??_R4?$ISingleton@VJoinRankManager@mu2@@@mu2@@6B@ ; mu2::ISingleton<mu2::JoinRankManager>::`RTTI Complete Object Locator'
PUBLIC	??_R0?AV?$ISingleton@VJoinRankManager@mu2@@@mu2@@@8 ; mu2::ISingleton<mu2::JoinRankManager> `RTTI Type Descriptor'
PUBLIC	??_R3?$ISingleton@VJoinRankManager@mu2@@@mu2@@8	; mu2::ISingleton<mu2::JoinRankManager>::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2?$ISingleton@VJoinRankManager@mu2@@@mu2@@8	; mu2::ISingleton<mu2::JoinRankManager>::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@?$ISingleton@VJoinRankManager@mu2@@@mu2@@8 ; mu2::ISingleton<mu2::JoinRankManager>::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_C@_0EO@KGDNFAOG@F?3?2Release_Branch?2Server?2Develo@ ; `string'
PUBLIC	??_C@_0CI@OPIGHMJ@firstWaitUserIndex?5?$DM?$DN?5lastWaitU@ ; `string'
PUBLIC	??_R4JoinRankManager@mu2@@6B@			; mu2::JoinRankManager::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVJoinRankManager@mu2@@@8			; mu2::JoinRankManager `RTTI Type Descriptor'
PUBLIC	??_R3JoinRankManager@mu2@@8			; mu2::JoinRankManager::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2JoinRankManager@mu2@@8			; mu2::JoinRankManager::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@JoinRankManager@mu2@@8		; mu2::JoinRankManager::`RTTI Base Class Descriptor at (0,-1,0,64)'
EXTRN	_purecall:PROC
EXTRN	??2@YAPEAX_K@Z:PROC				; operator new
EXTRN	??3@YAXPEAX_K@Z:PROC				; operator delete
EXTRN	__std_terminate:PROC
EXTRN	_invoke_watson:PROC
EXTRN	__std_exception_copy:PROC
EXTRN	__std_exception_destroy:PROC
EXTRN	??_Eexception@std@@UEAAPEAXI@Z:PROC		; std::exception::`vector deleting destructor'
EXTRN	??_Ebad_alloc@std@@UEAAPEAXI@Z:PROC		; std::bad_alloc::`vector deleting destructor'
EXTRN	??_Ebad_array_new_length@std@@UEAAPEAXI@Z:PROC	; std::bad_array_new_length::`vector deleting destructor'
EXTRN	?_Xlength_error@std@@YAXPEBD@Z:PROC		; std::_Xlength_error
EXTRN	?LogRuntimeAssertionFailure@mu2@@YAXPEBD00_K@Z:PROC ; mu2::LogRuntimeAssertionFailure
EXTRN	?GetSessionKey@EntityPlayer@mu2@@QEBA?BIXZ:PROC	; mu2::EntityPlayer::GetSessionKey
EXTRN	??0JoinRankTimeCalculator@mu2@@QEAA@XZ:PROC	; mu2::JoinRankTimeCalculator::JoinRankTimeCalculator
EXTRN	??1JoinRankTimeCalculator@mu2@@QEAA@XZ:PROC	; mu2::JoinRankTimeCalculator::~JoinRankTimeCalculator
EXTRN	?EnterWait@JoinRankTimeCalculator@mu2@@QEAAII@Z:PROC ; mu2::JoinRankTimeCalculator::EnterWait
EXTRN	?LeaveWait@JoinRankTimeCalculator@mu2@@QEAAXXZ:PROC ; mu2::JoinRankTimeCalculator::LeaveWait
EXTRN	?GetTime@JoinRankTimeCalculator@mu2@@QEBAII@Z:PROC ; mu2::JoinRankTimeCalculator::GetTime
EXTRN	??_E?$ISingleton@VJoinRankManager@mu2@@@mu2@@UEAAPEAXI@Z:PROC ; mu2::ISingleton<mu2::JoinRankManager>::`vector deleting destructor'
EXTRN	??_EJoinRankManager@mu2@@UEAAPEAXI@Z:PROC	; mu2::JoinRankManager::`vector deleting destructor'
EXTRN	_CxxThrowException:PROC
EXTRN	__CxxFrameHandler4:PROC
EXTRN	??_7type_info@@6B@:BYTE				; type_info::`vftable'
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0exception@std@@QEAA@AEBV01@@Z DD imagerel $LN4
	DD	imagerel $LN4+89
	DD	imagerel $unwind$??0exception@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?what@exception@std@@UEBAPEBDXZ DD imagerel $LN5
	DD	imagerel $LN5+56
	DD	imagerel $unwind$?what@exception@std@@UEBAPEBDXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gexception@std@@UEAAPEAXI@Z DD imagerel $LN10
	DD	imagerel $LN10+83
	DD	imagerel $unwind$??_Gexception@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_alloc@std@@QEAA@AEBV01@@Z DD imagerel $LN9
	DD	imagerel $LN9+104
	DD	imagerel $unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gbad_alloc@std@@UEAAPEAXI@Z DD imagerel $LN15
	DD	imagerel $LN15+83
	DD	imagerel $unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_array_new_length@std@@QEAA@XZ DD imagerel $LN14
	DD	imagerel $LN14+95
	DD	imagerel $unwind$??0bad_array_new_length@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1bad_array_new_length@std@@UEAA@XZ DD imagerel $LN14
	DD	imagerel $LN14+47
	DD	imagerel $unwind$??1bad_array_new_length@std@@UEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0bad_array_new_length@std@@QEAA@AEBV01@@Z DD imagerel $LN14
	DD	imagerel $LN14+54
	DD	imagerel $unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_Gbad_array_new_length@std@@UEAAPEAXI@Z DD imagerel $LN20
	DD	imagerel $LN20+83
	DD	imagerel $unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Throw_bad_array_new_length@std@@YAXXZ DD imagerel $LN3
	DD	imagerel $LN3+37
	DD	imagerel $unwind$?_Throw_bad_array_new_length@std@@YAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z DD imagerel $LN5
	DD	imagerel $LN5+159
	DD	imagerel $unwind$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_G?$ISingleton@VJoinRankManager@mu2@@@mu2@@UEAAPEAXI@Z DD imagerel $LN10
	DD	imagerel $LN10+65
	DD	imagerel $unwind$??_G?$ISingleton@VJoinRankManager@mu2@@@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?allocate@?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@QEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@2@_K@Z DD imagerel $LN13
	DD	imagerel $LN13+160
	DD	imagerel $unwind$?allocate@?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@QEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@2@_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@QEAA@XZ DD imagerel $LN104
	DD	imagerel $LN104+25
	DD	imagerel $unwind$??1?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?erase@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@QEAA?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@URankInfo@JoinRankManager@mu2@@@std@@@std@@@2@V?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@URankInfo@JoinRankManager@mu2@@@std@@@std@@@2@@Z DD imagerel $LN96
	DD	imagerel $LN96+291
	DD	imagerel $unwind$?erase@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@QEAA?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@URankInfo@JoinRankManager@mu2@@@std@@@std@@@2@V?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@URankInfo@JoinRankManager@mu2@@@std@@@std@@@2@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Unchecked_erase@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@AEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@2@QEAU32@@Z DD imagerel $LN61
	DD	imagerel $LN61+194
	DD	imagerel $unwind$?_Unchecked_erase@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@AEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@2@QEAU32@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Tidy@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@AEAAXXZ DD imagerel $LN99
	DD	imagerel $LN99+152
	DD	imagerel $unwind$?_Tidy@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@AEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Alloc_sentinel_and_proxy@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@AEAAXXZ DD imagerel $LN75
	DD	imagerel $LN75+275
	DD	imagerel $unwind$?_Alloc_sentinel_and_proxy@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@AEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1JoinRankManager@mu2@@UEAA@XZ DD imagerel $LN114
	DD	imagerel $LN114+79
	DD	imagerel $unwind$??1JoinRankManager@mu2@@UEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?PushPlayer@JoinRankManager@mu2@@QEAAIAEBV?$SmartPtrEx@VEntityPlayer@mu2@@@2@AEAI@Z DD imagerel $LN195
	DD	imagerel $LN195+257
	DD	imagerel $unwind$?PushPlayer@JoinRankManager@mu2@@QEAAIAEBV?$SmartPtrEx@VEntityPlayer@mu2@@@2@AEAI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?PopPlayer@JoinRankManager@mu2@@QEAAIXZ DD imagerel $LN84
	DD	imagerel $LN84+170
	DD	imagerel $unwind$?PopPlayer@JoinRankManager@mu2@@QEAAIXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?UpdateJoinWaitSecond@JoinRankManager@mu2@@QEAAXXZ DD imagerel $LN3
	DD	imagerel $LN3+32
	DD	imagerel $unwind$?UpdateJoinWaitSecond@JoinRankManager@mu2@@QEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?GetJoinWaitSecond@JoinRankManager@mu2@@QEAAII@Z DD imagerel $LN3
	DD	imagerel $LN3+39
	DD	imagerel $unwind$?GetJoinWaitSecond@JoinRankManager@mu2@@QEAAII@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?LeaveWaitLine@JoinRankManager@mu2@@QEAAXI@Z DD imagerel $LN213
	DD	imagerel $LN213+418
	DD	imagerel $unwind$?LeaveWaitLine@JoinRankManager@mu2@@QEAAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?GetWaitUserCount@JoinRankManager@mu2@@QEBAIXZ DD imagerel $LN4
	DD	imagerel $LN4+83
	DD	imagerel $unwind$?GetWaitUserCount@JoinRankManager@mu2@@QEBAIXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??0JoinRankManager@mu2@@AEAA@XZ DD imagerel $LN99
	DD	imagerel $LN99+161
	DD	imagerel $unwind$??0JoinRankManager@mu2@@AEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???0JoinRankManager@mu2@@AEAA@XZ@4HA DD imagerel ?dtor$0@?0???0JoinRankManager@mu2@@AEAA@XZ@4HA
	DD	imagerel ?dtor$0@?0???0JoinRankManager@mu2@@AEAA@XZ@4HA+24
	DD	imagerel $unwind$?dtor$0@?0???0JoinRankManager@mu2@@AEAA@XZ@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$1@?0???0JoinRankManager@mu2@@AEAA@XZ@4HA DD imagerel ?dtor$1@?0???0JoinRankManager@mu2@@AEAA@XZ@4HA
	DD	imagerel ?dtor$1@?0???0JoinRankManager@mu2@@AEAA@XZ@4HA+28
	DD	imagerel $unwind$?dtor$1@?0???0JoinRankManager@mu2@@AEAA@XZ@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GJoinRankManager@mu2@@UEAAPEAXI@Z DD imagerel $LN5
	DD	imagerel $LN5+60
	DD	imagerel $unwind$??_GJoinRankManager@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z DD imagerel $LN7
	DD	imagerel $LN7+150
	DD	imagerel $unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Emplace@AEAURankInfo@JoinRankManager@mu2@@@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@QEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@1@QEAU21@AEAURankInfo@JoinRankManager@mu2@@@Z DD imagerel $LN172
	DD	imagerel $LN172+441
	DD	imagerel $unwind$??$_Emplace@AEAURankInfo@JoinRankManager@mu2@@@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@QEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@1@QEAU21@AEAURankInfo@JoinRankManager@mu2@@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1?$_Alloc_construct_ptr@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAA@XZ DD imagerel $LN20
	DD	imagerel $LN20+120
	DD	imagerel $unwind$??1?$_Alloc_construct_ptr@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?_Transfer_before@?$_List_node_emplace_op2@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@2@QEAU32@@Z DD imagerel $LN44
	DD	imagerel $LN44+232
	DD	imagerel $unwind$?_Transfer_before@?$_List_node_emplace_op2@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@2@QEAU32@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$?0AEAURankInfo@JoinRankManager@mu2@@@?$_List_node_emplace_op2@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@1@AEAURankInfo@JoinRankManager@mu2@@@Z DD imagerel $LN42
	DD	imagerel $LN42+195
	DD	imagerel $unwind$??$?0AEAURankInfo@JoinRankManager@mu2@@@?$_List_node_emplace_op2@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@1@AEAURankInfo@JoinRankManager@mu2@@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?dtor$0@?0???$?0AEAURankInfo@JoinRankManager@mu2@@@?$_List_node_emplace_op2@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@1@AEAURankInfo@JoinRankManager@mu2@@@Z@4HA DD imagerel ?dtor$0@?0???$?0AEAURankInfo@JoinRankManager@mu2@@@?$_List_node_emplace_op2@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@1@AEAURankInfo@JoinRankManager@mu2@@@Z@4HA
	DD	imagerel ?dtor$0@?0???$?0AEAURankInfo@JoinRankManager@mu2@@@?$_List_node_emplace_op2@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@1@AEAURankInfo@JoinRankManager@mu2@@@Z@4HA+24
	DD	imagerel $unwind$?dtor$0@?0???$?0AEAURankInfo@JoinRankManager@mu2@@@?$_List_node_emplace_op2@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@1@AEAURankInfo@JoinRankManager@mu2@@@Z@4HA
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??$_Free_non_head@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@SAXAEAV?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@1@PEAU01@@Z DD imagerel $LN49
	DD	imagerel $LN49+164
	DD	imagerel $unwind$??$_Free_non_head@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@SAXAEAV?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@1@PEAU01@@Z
pdata	ENDS
;	COMDAT ??_R1A@?0A@EA@JoinRankManager@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@JoinRankManager@mu2@@8 DD imagerel ??_R0?AVJoinRankManager@mu2@@@8 ; mu2::JoinRankManager::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3JoinRankManager@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2JoinRankManager@mu2@@8
rdata$r	SEGMENT
??_R2JoinRankManager@mu2@@8 DD imagerel ??_R1A@?0A@EA@JoinRankManager@mu2@@8 ; mu2::JoinRankManager::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@?$ISingleton@VJoinRankManager@mu2@@@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3JoinRankManager@mu2@@8
rdata$r	SEGMENT
??_R3JoinRankManager@mu2@@8 DD 00H			; mu2::JoinRankManager::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2JoinRankManager@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVJoinRankManager@mu2@@@8
data$rs	SEGMENT
??_R0?AVJoinRankManager@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::JoinRankManager `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVJoinRankManager@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4JoinRankManager@mu2@@6B@
rdata$r	SEGMENT
??_R4JoinRankManager@mu2@@6B@ DD 01H			; mu2::JoinRankManager::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVJoinRankManager@mu2@@@8
	DD	imagerel ??_R3JoinRankManager@mu2@@8
	DD	imagerel ??_R4JoinRankManager@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_C@_0CI@OPIGHMJ@firstWaitUserIndex?5?$DM?$DN?5lastWaitU@
CONST	SEGMENT
??_C@_0CI@OPIGHMJ@firstWaitUserIndex?5?$DM?$DN?5lastWaitU@ DB 'firstWaitU'
	DB	'serIndex <= lastWaitUserIndex', 00H		; `string'
CONST	ENDS
;	COMDAT ??_C@_0EO@KGDNFAOG@F?3?2Release_Branch?2Server?2Develo@
CONST	SEGMENT
??_C@_0EO@KGDNFAOG@F?3?2Release_Branch?2Server?2Develo@ DB 'F:\Release_Br'
	DB	'anch\Server\Development\Frontend\WorldServer\JoinRankManager.'
	DB	'cpp', 00H					; `string'
CONST	ENDS
;	COMDAT ??_R1A@?0A@EA@?$ISingleton@VJoinRankManager@mu2@@@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@?$ISingleton@VJoinRankManager@mu2@@@mu2@@8 DD imagerel ??_R0?AV?$ISingleton@VJoinRankManager@mu2@@@mu2@@@8 ; mu2::ISingleton<mu2::JoinRankManager>::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3?$ISingleton@VJoinRankManager@mu2@@@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2?$ISingleton@VJoinRankManager@mu2@@@mu2@@8
rdata$r	SEGMENT
??_R2?$ISingleton@VJoinRankManager@mu2@@@mu2@@8 DD imagerel ??_R1A@?0A@EA@?$ISingleton@VJoinRankManager@mu2@@@mu2@@8 ; mu2::ISingleton<mu2::JoinRankManager>::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3?$ISingleton@VJoinRankManager@mu2@@@mu2@@8
rdata$r	SEGMENT
??_R3?$ISingleton@VJoinRankManager@mu2@@@mu2@@8 DD 00H	; mu2::ISingleton<mu2::JoinRankManager>::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2?$ISingleton@VJoinRankManager@mu2@@@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AV?$ISingleton@VJoinRankManager@mu2@@@mu2@@@8
data$rs	SEGMENT
??_R0?AV?$ISingleton@VJoinRankManager@mu2@@@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::ISingleton<mu2::JoinRankManager> `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AV?$ISingleton@VJoinRankManager@mu2@@@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4?$ISingleton@VJoinRankManager@mu2@@@mu2@@6B@
rdata$r	SEGMENT
??_R4?$ISingleton@VJoinRankManager@mu2@@@mu2@@6B@ DD 01H ; mu2::ISingleton<mu2::JoinRankManager>::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AV?$ISingleton@VJoinRankManager@mu2@@@mu2@@@8
	DD	imagerel ??_R3?$ISingleton@VJoinRankManager@mu2@@@mu2@@8
	DD	imagerel ??_R4?$ISingleton@VJoinRankManager@mu2@@@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R4bad_alloc@std@@6B@
rdata$r	SEGMENT
??_R4bad_alloc@std@@6B@ DD 01H				; std::bad_alloc::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVbad_alloc@std@@@8
	DD	imagerel ??_R3bad_alloc@std@@8
	DD	imagerel ??_R4bad_alloc@std@@6B@
rdata$r	ENDS
;	COMDAT ??_R2bad_alloc@std@@8
rdata$r	SEGMENT
??_R2bad_alloc@std@@8 DD imagerel ??_R1A@?0A@EA@bad_alloc@std@@8 ; std::bad_alloc::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@exception@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3bad_alloc@std@@8
rdata$r	SEGMENT
??_R3bad_alloc@std@@8 DD 00H				; std::bad_alloc::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2bad_alloc@std@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@bad_alloc@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@bad_alloc@std@@8 DD imagerel ??_R0?AVbad_alloc@std@@@8 ; std::bad_alloc::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3bad_alloc@std@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@bad_array_new_length@std@@8 DD imagerel ??_R0?AVbad_array_new_length@std@@@8 ; std::bad_array_new_length::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	02H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3bad_array_new_length@std@@8
rdata$r	ENDS
;	COMDAT ??_R2bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R2bad_array_new_length@std@@8 DD imagerel ??_R1A@?0A@EA@bad_array_new_length@std@@8 ; std::bad_array_new_length::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@bad_alloc@std@@8
	DD	imagerel ??_R1A@?0A@EA@exception@std@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3bad_array_new_length@std@@8
rdata$r	SEGMENT
??_R3bad_array_new_length@std@@8 DD 00H			; std::bad_array_new_length::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	03H
	DD	imagerel ??_R2bad_array_new_length@std@@8
rdata$r	ENDS
;	COMDAT ??_R4bad_array_new_length@std@@6B@
rdata$r	SEGMENT
??_R4bad_array_new_length@std@@6B@ DD 01H		; std::bad_array_new_length::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVbad_array_new_length@std@@@8
	DD	imagerel ??_R3bad_array_new_length@std@@8
	DD	imagerel ??_R4bad_array_new_length@std@@6B@
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@exception@std@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@exception@std@@8 DD imagerel ??_R0?AVexception@std@@@8 ; std::exception::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3exception@std@@8
rdata$r	ENDS
;	COMDAT ??_R2exception@std@@8
rdata$r	SEGMENT
??_R2exception@std@@8 DD imagerel ??_R1A@?0A@EA@exception@std@@8 ; std::exception::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3exception@std@@8
rdata$r	SEGMENT
??_R3exception@std@@8 DD 00H				; std::exception::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2exception@std@@8
rdata$r	ENDS
;	COMDAT ??_R4exception@std@@6B@
rdata$r	SEGMENT
??_R4exception@std@@6B@ DD 01H				; std::exception::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVexception@std@@@8
	DD	imagerel ??_R3exception@std@@8
	DD	imagerel ??_R4exception@std@@6B@
rdata$r	ENDS
;	COMDAT ??_C@_0O@NKNMEGII@list?5too?5long@
CONST	SEGMENT
??_C@_0O@NKNMEGII@list?5too?5long@ DB 'list too long', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7JoinRankManager@mu2@@6B@
CONST	SEGMENT
??_7JoinRankManager@mu2@@6B@ DQ FLAT:??_R4JoinRankManager@mu2@@6B@ ; mu2::JoinRankManager::`vftable'
	DQ	FLAT:?Init@JoinRankManager@mu2@@UEAA_NPEAX@Z
	DQ	FLAT:?UnInit@JoinRankManager@mu2@@UEAA_NXZ
	DQ	FLAT:??_EJoinRankManager@mu2@@UEAAPEAXI@Z
CONST	ENDS
;	COMDAT ??_7?$ISingleton@VJoinRankManager@mu2@@@mu2@@6B@
CONST	SEGMENT
??_7?$ISingleton@VJoinRankManager@mu2@@@mu2@@6B@ DQ FLAT:??_R4?$ISingleton@VJoinRankManager@mu2@@@mu2@@6B@ ; mu2::ISingleton<mu2::JoinRankManager>::`vftable'
	DQ	FLAT:_purecall
	DQ	FLAT:_purecall
	DQ	FLAT:??_E?$ISingleton@VJoinRankManager@mu2@@@mu2@@UEAAPEAXI@Z
CONST	ENDS
;	COMDAT ??_C@_09OLBBGJEO@Assertion@
CONST	SEGMENT
??_C@_09OLBBGJEO@Assertion@ DB 'Assertion', 00H		; `string'
CONST	ENDS
;	COMDAT _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24 DD 010H
	DD	imagerel ??_R0?AVbad_alloc@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0bad_alloc@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVbad_alloc@std@@@8
data$r	SEGMENT
??_R0?AVbad_alloc@std@@@8 DQ FLAT:??_7type_info@@6B@	; std::bad_alloc `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVbad_alloc@std@@', 00H
data$r	ENDS
;	COMDAT _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24 DD 00H
	DD	imagerel ??_R0?AVbad_array_new_length@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0bad_array_new_length@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVbad_array_new_length@std@@@8
data$r	SEGMENT
??_R0?AVbad_array_new_length@std@@@8 DQ FLAT:??_7type_info@@6B@ ; std::bad_array_new_length `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVbad_array_new_length@std@@', 00H
data$r	ENDS
;	COMDAT _CTA3?AVbad_array_new_length@std@@
xdata$x	SEGMENT
_CTA3?AVbad_array_new_length@std@@ DD 03H
	DD	imagerel _CT??_R0?AVbad_array_new_length@std@@@8??0bad_array_new_length@std@@QEAA@AEBV01@@Z24
	DD	imagerel _CT??_R0?AVbad_alloc@std@@@8??0bad_alloc@std@@QEAA@AEBV01@@Z24
	DD	imagerel _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
xdata$x	ENDS
;	COMDAT _TI3?AVbad_array_new_length@std@@
xdata$x	SEGMENT
_TI3?AVbad_array_new_length@std@@ DD 00H
	DD	imagerel ??1bad_array_new_length@std@@UEAA@XZ
	DD	00H
	DD	imagerel _CTA3?AVbad_array_new_length@std@@
xdata$x	ENDS
;	COMDAT _CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24
xdata$x	SEGMENT
_CT??_R0?AVexception@std@@@8??0exception@std@@QEAA@AEBV01@@Z24 DD 00H
	DD	imagerel ??_R0?AVexception@std@@@8
	DD	00H
	DD	0ffffffffH
	ORG $+4
	DD	018H
	DD	imagerel ??0exception@std@@QEAA@AEBV01@@Z
xdata$x	ENDS
;	COMDAT ??_R0?AVexception@std@@@8
data$r	SEGMENT
??_R0?AVexception@std@@@8 DQ FLAT:??_7type_info@@6B@	; std::exception `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVexception@std@@', 00H
data$r	ENDS
;	COMDAT ??_C@_0BF@KINCDENJ@bad?5array?5new?5length@
CONST	SEGMENT
??_C@_0BF@KINCDENJ@bad?5array?5new?5length@ DB 'bad array new length', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7bad_array_new_length@std@@6B@
CONST	SEGMENT
??_7bad_array_new_length@std@@6B@ DQ FLAT:??_R4bad_array_new_length@std@@6B@ ; std::bad_array_new_length::`vftable'
	DQ	FLAT:??_Ebad_array_new_length@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT ??_7bad_alloc@std@@6B@
CONST	SEGMENT
??_7bad_alloc@std@@6B@ DQ FLAT:??_R4bad_alloc@std@@6B@	; std::bad_alloc::`vftable'
	DQ	FLAT:??_Ebad_alloc@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT ??_C@_0BC@EOODALEL@Unknown?5exception@
CONST	SEGMENT
??_C@_0BC@EOODALEL@Unknown?5exception@ DB 'Unknown exception', 00H ; `string'
CONST	ENDS
;	COMDAT ??_7exception@std@@6B@
CONST	SEGMENT
??_7exception@std@@6B@ DQ FLAT:??_R4exception@std@@6B@	; std::exception::`vftable'
	DQ	FLAT:??_Eexception@std@@UEAAPEAXI@Z
	DQ	FLAT:?what@exception@std@@UEBAPEBDXZ
CONST	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$_Free_non_head@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@SAXAEAV?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@1@PEAU01@@Z DB 06H
	DB	00H
	DB	00H
	DB	0e4H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$_Free_non_head@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@SAXAEAV?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@1@PEAU01@@Z DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$_Free_non_head@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@SAXAEAV?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@1@PEAU01@@Z DB 068H
	DD	imagerel $stateUnwindMap$??$_Free_non_head@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@SAXAEAV?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@1@PEAU01@@Z
	DD	imagerel $ip2state$??$_Free_non_head@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@SAXAEAV?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@1@PEAU01@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Free_non_head@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@SAXAEAV?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@1@PEAU01@@Z DD 010e19H
	DD	0a20eH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$_Free_non_head@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@SAXAEAV?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@1@PEAU01@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???$?0AEAURankInfo@JoinRankManager@mu2@@@?$_List_node_emplace_op2@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@1@AEAURankInfo@JoinRankManager@mu2@@@Z@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??$?0AEAURankInfo@JoinRankManager@mu2@@@?$_List_node_emplace_op2@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@1@AEAURankInfo@JoinRankManager@mu2@@@Z DB 06H
	DB	00H
	DB	00H
	DB	'Z'
	DB	02H
	DB	'1', 02H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??$?0AEAURankInfo@JoinRankManager@mu2@@@?$_List_node_emplace_op2@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@1@AEAURankInfo@JoinRankManager@mu2@@@Z DB 02H
	DB	0eH
	DD	imagerel ?dtor$0@?0???$?0AEAURankInfo@JoinRankManager@mu2@@@?$_List_node_emplace_op2@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@1@AEAURankInfo@JoinRankManager@mu2@@@Z@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??$?0AEAURankInfo@JoinRankManager@mu2@@@?$_List_node_emplace_op2@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@1@AEAURankInfo@JoinRankManager@mu2@@@Z DB 028H
	DD	imagerel $stateUnwindMap$??$?0AEAURankInfo@JoinRankManager@mu2@@@?$_List_node_emplace_op2@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@1@AEAURankInfo@JoinRankManager@mu2@@@Z
	DD	imagerel $ip2state$??$?0AEAURankInfo@JoinRankManager@mu2@@@?$_List_node_emplace_op2@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@1@AEAURankInfo@JoinRankManager@mu2@@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$?0AEAURankInfo@JoinRankManager@mu2@@@?$_List_node_emplace_op2@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@1@AEAURankInfo@JoinRankManager@mu2@@@Z DD 011311H
	DD	0c213H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??$?0AEAURankInfo@JoinRankManager@mu2@@@?$_List_node_emplace_op2@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@1@AEAURankInfo@JoinRankManager@mu2@@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Transfer_before@?$_List_node_emplace_op2@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@2@QEAU32@@Z DD 010e01H
	DD	0c20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??1?$_Alloc_construct_ptr@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAA@XZ DB 06H
	DB	00H
	DB	00H
	DB	090H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??1?$_Alloc_construct_ptr@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAA@XZ DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??1?$_Alloc_construct_ptr@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAA@XZ DB 068H
	DD	imagerel $stateUnwindMap$??1?$_Alloc_construct_ptr@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAA@XZ
	DD	imagerel $ip2state$??1?$_Alloc_construct_ptr@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$_Alloc_construct_ptr@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAA@XZ DD 010919H
	DD	08209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??1?$_Alloc_construct_ptr@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Emplace@AEAURankInfo@JoinRankManager@mu2@@@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@QEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@1@QEAU21@AEAURankInfo@JoinRankManager@mu2@@@Z DD 021601H
	DD	01b0116H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z DD 010901H
	DD	0a209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GJoinRankManager@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$1@?0???0JoinRankManager@mu2@@AEAA@XZ@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?dtor$0@?0???0JoinRankManager@mu2@@AEAA@XZ@4HA DD 020601H
	DD	050023206H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??0JoinRankManager@mu2@@AEAA@XZ DB 08H
	DB	00H
	DB	00H
	DB	'0'
	DB	02H
	DB	0daH
	DB	04H
	DB	'$'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$??0JoinRankManager@mu2@@AEAA@XZ DB 04H
	DB	0eH
	DD	imagerel ?dtor$0@?0???0JoinRankManager@mu2@@AEAA@XZ@4HA
	DB	02eH
	DD	imagerel ?dtor$1@?0???0JoinRankManager@mu2@@AEAA@XZ@4HA
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??0JoinRankManager@mu2@@AEAA@XZ DB 028H
	DD	imagerel $stateUnwindMap$??0JoinRankManager@mu2@@AEAA@XZ
	DD	imagerel $ip2state$??0JoinRankManager@mu2@@AEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0JoinRankManager@mu2@@AEAA@XZ DD 010911H
	DD	08209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??0JoinRankManager@mu2@@AEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?GetWaitUserCount@JoinRankManager@mu2@@QEBAIXZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?LeaveWaitLine@JoinRankManager@mu2@@QEAAXI@Z DD 021001H
	DD	0170110H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?GetJoinWaitSecond@JoinRankManager@mu2@@QEAAII@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?UpdateJoinWaitSecond@JoinRankManager@mu2@@QEAAXXZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?PopPlayer@JoinRankManager@mu2@@QEAAIXZ DD 010901H
	DD	08209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?PushPlayer@JoinRankManager@mu2@@QEAAIAEBV?$SmartPtrEx@VEntityPlayer@mu2@@@2@AEAI@Z DD 011301H
	DD	0e213H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1JoinRankManager@mu2@@UEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Alloc_sentinel_and_proxy@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@AEAAXXZ DD 030d01H
	DD	014010dH
	DD	07006H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?_Tidy@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@AEAAXXZ DB 06H
	DB	00H
	DB	00H
	DB	0d0H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?_Tidy@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@AEAAXXZ DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?_Tidy@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@AEAAXXZ DB 068H
	DD	imagerel $stateUnwindMap$?_Tidy@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@AEAAXXZ
	DD	imagerel $ip2state$?_Tidy@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@AEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Tidy@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@AEAAXXZ DD 010919H
	DD	0c209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?_Tidy@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@AEAAXXZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?_Unchecked_erase@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@AEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@2@QEAU32@@Z DB 06H
	DB	00H
	DB	00H
	DB	'5', 02H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?_Unchecked_erase@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@AEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@2@QEAU32@@Z DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?_Unchecked_erase@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@AEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@2@QEAU32@@Z DB 068H
	DD	imagerel $stateUnwindMap$?_Unchecked_erase@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@AEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@2@QEAU32@@Z
	DD	imagerel $ip2state$?_Unchecked_erase@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@AEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@2@QEAU32@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Unchecked_erase@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@AEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@2@QEAU32@@Z DD 010e19H
	DD	0a20eH
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?_Unchecked_erase@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@AEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@2@QEAU32@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$?erase@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@QEAA?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@URankInfo@JoinRankManager@mu2@@@std@@@std@@@2@V?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@URankInfo@JoinRankManager@mu2@@@std@@@std@@@2@@Z DB 06H
	DB	00H
	DB	00H
	DB	'9', 03H
	DB	02H
	DB	'V'
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$stateUnwindMap$?erase@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@QEAA?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@URankInfo@JoinRankManager@mu2@@@std@@@std@@@2@V?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@URankInfo@JoinRankManager@mu2@@@std@@@std@@@2@@Z DB 02H
	DB	0eH
	DD	imagerel __std_terminate
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$?erase@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@QEAA?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@URankInfo@JoinRankManager@mu2@@@std@@@std@@@2@V?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@URankInfo@JoinRankManager@mu2@@@std@@@std@@@2@@Z DB 068H
	DD	imagerel $stateUnwindMap$?erase@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@QEAA?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@URankInfo@JoinRankManager@mu2@@@std@@@std@@@2@V?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@URankInfo@JoinRankManager@mu2@@@std@@@std@@@2@@Z
	DD	imagerel $ip2state$?erase@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@QEAA?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@URankInfo@JoinRankManager@mu2@@@std@@@std@@@2@V?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@URankInfo@JoinRankManager@mu2@@@std@@@std@@@2@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?erase@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@QEAA?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@URankInfo@JoinRankManager@mu2@@@std@@@std@@@2@V?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@URankInfo@JoinRankManager@mu2@@@std@@@std@@@2@@Z DD 011319H
	DD	0e213H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$?erase@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@QEAA?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@URankInfo@JoinRankManager@mu2@@@std@@@std@@@2@V?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@URankInfo@JoinRankManager@mu2@@@std@@@std@@@2@@Z
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@QEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?allocate@?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@QEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@2@_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_G?$ISingleton@VJoinRankManager@mu2@@@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z DD 010e01H
	DD	0a20eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?_Throw_bad_array_new_length@std@@YAXXZ DD 010401H
	DD	08204H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gbad_array_new_length@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_array_new_length@std@@QEAA@AEBV01@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1bad_array_new_length@std@@UEAA@XZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_array_new_length@std@@QEAA@XZ DD 010601H
	DD	07006H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gbad_alloc@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0bad_alloc@std@@QEAA@AEBV01@@Z DD 020f01H
	DD	0700b320fH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_Gexception@std@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?what@exception@std@@UEBAPEBDXZ DD 010901H
	DD	02209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??0exception@std@@QEAA@AEBV01@@Z DD 020f01H
	DD	0700b320fH
xdata	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
;	COMDAT ??$_Free_non_head@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@SAXAEAV?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@1@PEAU01@@Z
_TEXT	SEGMENT
_Pnode$ = 32
_Bytes$ = 40
_Ptr$ = 48
_Pnext$1 = 56
$T2 = 64
_Al$ = 96
_Head$ = 104
??$_Free_non_head@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@SAXAEAV?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@1@PEAU01@@Z PROC ; std::_List_node<mu2::JoinRankManager::RankInfo,void *>::_Free_non_head<std::allocator<std::_List_node<mu2::JoinRankManager::RankInfo,void *> > >, COMDAT

; 323  :         _Alnode& _Al, _Nodeptr _Head) noexcept { // free a list starting at _First and terminated at nullptr

$LN49:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 324  :         _Head->_Prev->_Next = nullptr;

  0000e	48 8b 44 24 68	 mov	 rax, QWORD PTR _Head$[rsp]
  00013	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00017	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0

; 325  : 
; 326  :         auto _Pnode = _Head->_Next;

  0001e	48 8b 44 24 68	 mov	 rax, QWORD PTR _Head$[rsp]
  00023	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00026	48 89 44 24 20	 mov	 QWORD PTR _Pnode$[rsp], rax

; 327  :         for (_Nodeptr _Pnext; _Pnode; _Pnode = _Pnext) {

  0002b	eb 0a		 jmp	 SHORT $LN4@Free_non_h
$LN2@Free_non_h:
  0002d	48 8b 44 24 38	 mov	 rax, QWORD PTR _Pnext$1[rsp]
  00032	48 89 44 24 20	 mov	 QWORD PTR _Pnode$[rsp], rax
$LN4@Free_non_h:
  00037	48 83 7c 24 20
	00		 cmp	 QWORD PTR _Pnode$[rsp], 0
  0003d	74 60		 je	 SHORT $LN3@Free_non_h

; 328  :             _Pnext = _Pnode->_Next;

  0003f	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00044	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00047	48 89 44 24 38	 mov	 QWORD PTR _Pnext$1[rsp], rax

; 317  :         allocator_traits<_Alnode>::destroy(_Al, _STD addressof(_Ptr->_Myval));

  0004c	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00051	48 83 c0 10	 add	 rax, 16
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00055	48 89 44 24 40	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 723  :             _STD _Deallocate<_New_alignof<value_type>>(_Ptr, sizeof(value_type) * _Count);

  0005a	b8 01 00 00 00	 mov	 eax, 1
  0005f	48 6b c0 18	 imul	 rax, rax, 24
  00063	48 89 44 24 28	 mov	 QWORD PTR _Bytes$[rsp], rax
  00068	48 8b 44 24 20	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0006d	48 89 44 24 30	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  00072	48 81 7c 24 28
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  0007b	72 10		 jb	 SHORT $LN40@Free_non_h

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  0007d	48 8d 54 24 28	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  00082	48 8d 4c 24 30	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  00087	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  0008c	90		 npad	 1
$LN40@Free_non_h:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  0008d	48 8b 54 24 28	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  00092	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  00097	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  0009c	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 330  :         }

  0009d	eb 8e		 jmp	 SHORT $LN2@Free_non_h
$LN3@Free_non_h:

; 331  :     }

  0009f	48 83 c4 58	 add	 rsp, 88			; 00000058H
  000a3	c3		 ret	 0
??$_Free_non_head@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@SAXAEAV?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@1@PEAU01@@Z ENDP ; std::_List_node<mu2::JoinRankManager::RankInfo,void *>::_Free_non_head<std::allocator<std::_List_node<mu2::JoinRankManager::RankInfo,void *> > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
;	COMDAT ??$?0AEAURankInfo@JoinRankManager@mu2@@@?$_List_node_emplace_op2@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@1@AEAURankInfo@JoinRankManager@mu2@@@Z
_TEXT	SEGMENT
_Val$ = 32
$T1 = 40
$T2 = 48
$T3 = 56
$T4 = 64
$T5 = 72
__formal$ = 80
this$ = 112
_Al_$ = 120
<_Vals_0>$ = 128
??$?0AEAURankInfo@JoinRankManager@mu2@@@?$_List_node_emplace_op2@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@1@AEAURankInfo@JoinRankManager@mu2@@@Z PROC ; std::_List_node_emplace_op2<std::allocator<std::_List_node<mu2::JoinRankManager::RankInfo,void *> > >::_List_node_emplace_op2<std::allocator<std::_List_node<mu2::JoinRankManager::RankInfo,void *> > ><mu2::JoinRankManager::RankInfo &>, COMDAT

; 593  :     explicit _List_node_emplace_op2(_Alnode& _Al_, _Valtys&&... _Vals) : _Alloc_construct_ptr<_Alnode>(_Al_) {

$LN42:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 68	 sub	 rsp, 104		; 00000068H
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1160 :     _CONSTEXPR20 explicit _Alloc_construct_ptr(_Alloc& _Al_) : _Al(_Al_), _Ptr(nullptr) {}

  00013	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  00018	48 8b 4c 24 78	 mov	 rcx, QWORD PTR _Al_$[rsp]
  0001d	48 89 08	 mov	 QWORD PTR [rax], rcx
  00020	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  00025	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0

; 1167 :         _Ptr = nullptr; // if allocate throws, prevents double-free

  0002d	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  00032	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0

; 1168 :         _Ptr = _Al.allocate(1);

  0003a	ba 01 00 00 00	 mov	 edx, 1
  0003f	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  00044	48 8b 08	 mov	 rcx, QWORD PTR [rax]
  00047	e8 00 00 00 00	 call	 ?allocate@?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@QEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@2@_K@Z ; std::allocator<std::_List_node<mu2::JoinRankManager::RankInfo,void *> >::allocate
  0004c	48 8b 4c 24 70	 mov	 rcx, QWORD PTR this$[rsp]
  00051	48 89 41 08	 mov	 QWORD PTR [rcx+8], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00055	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR <_Vals_0>$[rsp]
  0005d	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 595  :         _Alnode_traits::construct(this->_Al, _STD addressof(this->_Ptr->_Myval), _STD forward<_Valtys>(_Vals)...);

  00062	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  00067	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0006b	48 83 c0 10	 add	 rax, 16
  0006f	48 89 44 24 20	 mov	 QWORD PTR _Val$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00074	48 8b 44 24 20	 mov	 rax, QWORD PTR _Val$[rsp]
  00079	48 89 44 24 28	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 595  :         _Alnode_traits::construct(this->_Al, _STD addressof(this->_Ptr->_Myval), _STD forward<_Valtys>(_Vals)...);

  0007e	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  00083	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00086	48 89 44 24 50	 mov	 QWORD PTR __formal$[rsp], rax
  0008b	48 8b 44 24 28	 mov	 rax, QWORD PTR $T1[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  00090	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 732  :         ::new (const_cast<void*>(static_cast<const volatile void*>(_Ptr))) _Objty(_STD forward<_Types>(_Args)...);

  00095	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
  0009a	48 89 44 24 48	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 595  :         _Alnode_traits::construct(this->_Al, _STD addressof(this->_Ptr->_Myval), _STD forward<_Valtys>(_Vals)...);

  0009f	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  000a4	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 732  :         ::new (const_cast<void*>(static_cast<const volatile void*>(_Ptr))) _Objty(_STD forward<_Types>(_Args)...);

  000a9	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  000ae	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000b1	48 8b 4c 24 48	 mov	 rcx, QWORD PTR $T5[rsp]
  000b6	48 89 01	 mov	 QWORD PTR [rcx], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 596  :     }

  000b9	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  000be	48 83 c4 68	 add	 rsp, 104		; 00000068H
  000c2	c3		 ret	 0
??$?0AEAURankInfo@JoinRankManager@mu2@@@?$_List_node_emplace_op2@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@1@AEAURankInfo@JoinRankManager@mu2@@@Z ENDP ; std::_List_node_emplace_op2<std::allocator<std::_List_node<mu2::JoinRankManager::RankInfo,void *> > >::_List_node_emplace_op2<std::allocator<std::_List_node<mu2::JoinRankManager::RankInfo,void *> > ><mu2::JoinRankManager::RankInfo &>
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
_Val$ = 32
$T1 = 40
$T2 = 48
$T3 = 56
$T4 = 64
$T5 = 72
__formal$ = 80
this$ = 112
_Al_$ = 120
<_Vals_0>$ = 128
?dtor$0@?0???$?0AEAURankInfo@JoinRankManager@mu2@@@?$_List_node_emplace_op2@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@1@AEAURankInfo@JoinRankManager@mu2@@@Z@4HA PROC ; `std::_List_node_emplace_op2<std::allocator<std::_List_node<mu2::JoinRankManager::RankInfo,void *> > >::_List_node_emplace_op2<std::allocator<std::_List_node<mu2::JoinRankManager::RankInfo,void *> > ><mu2::JoinRankManager::RankInfo &>'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 70	 mov	 rcx, QWORD PTR this$[rbp]
  0000d	e8 00 00 00 00	 call	 ??1?$_Alloc_construct_ptr@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAA@XZ ; std::_Alloc_construct_ptr<std::allocator<std::_List_node<mu2::JoinRankManager::RankInfo,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<mu2::JoinRankManager::RankInfo,void *> > >
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$0@?0???$?0AEAURankInfo@JoinRankManager@mu2@@@?$_List_node_emplace_op2@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@1@AEAURankInfo@JoinRankManager@mu2@@@Z@4HA ENDP ; `std::_List_node_emplace_op2<std::allocator<std::_List_node<mu2::JoinRankManager::RankInfo,void *> > >::_List_node_emplace_op2<std::allocator<std::_List_node<mu2::JoinRankManager::RankInfo,void *> > ><mu2::JoinRankManager::RankInfo &>'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
;	COMDAT ?_Transfer_before@?$_List_node_emplace_op2@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@2@QEAU32@@Z
_TEXT	SEGMENT
_Result$ = 0
_Insert_after$ = 8
_Obj$ = 16
$T1 = 24
$T2 = 32
$T3 = 40
$T4 = 48
_Obj$ = 56
$T5 = 64
$T6 = 72
$T7 = 80
$T8 = 88
this$ = 112
_Insert_before$ = 120
?_Transfer_before@?$_List_node_emplace_op2@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@2@QEAU32@@Z PROC ; std::_List_node_emplace_op2<std::allocator<std::_List_node<mu2::JoinRankManager::RankInfo,void *> > >::_Transfer_before, COMDAT

; 607  :     pointer _Transfer_before(const pointer _Insert_before) noexcept {

$LN44:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 68	 sub	 rsp, 104		; 00000068H

; 608  :         const pointer _Insert_after = _Insert_before->_Prev;

  0000e	48 8b 44 24 78	 mov	 rax, QWORD PTR _Insert_before$[rsp]
  00013	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00017	48 89 44 24 08	 mov	 QWORD PTR _Insert_after$[rsp], rax

; 609  :         _Construct_in_place(this->_Ptr->_Next, _Insert_before);

  0001c	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00025	48 89 44 24 10	 mov	 QWORD PTR _Obj$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0002a	48 8b 44 24 10	 mov	 rax, QWORD PTR _Obj$[rsp]
  0002f	48 89 44 24 18	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00034	48 8b 44 24 18	 mov	 rax, QWORD PTR $T1[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  00039	48 89 44 24 20	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0003e	48 8b 44 24 20	 mov	 rax, QWORD PTR $T2[rsp]
  00043	48 89 44 24 28	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00048	48 8d 44 24 78	 lea	 rax, QWORD PTR _Insert_before$[rsp]
  0004d	48 89 44 24 30	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00052	48 8b 44 24 28	 mov	 rax, QWORD PTR $T3[rsp]
  00057	48 8b 4c 24 30	 mov	 rcx, QWORD PTR $T4[rsp]
  0005c	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0005f	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 610  :         _Construct_in_place(this->_Ptr->_Prev, _Insert_after);

  00062	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  00067	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0006b	48 83 c0 08	 add	 rax, 8
  0006f	48 89 44 24 38	 mov	 QWORD PTR _Obj$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00074	48 8b 44 24 38	 mov	 rax, QWORD PTR _Obj$[rsp]
  00079	48 89 44 24 40	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0007e	48 8b 44 24 40	 mov	 rax, QWORD PTR $T5[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  00083	48 89 44 24 48	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00088	48 8b 44 24 48	 mov	 rax, QWORD PTR $T6[rsp]
  0008d	48 89 44 24 50	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00092	48 8d 44 24 08	 lea	 rax, QWORD PTR _Insert_after$[rsp]
  00097	48 89 44 24 58	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0009c	48 8b 44 24 50	 mov	 rax, QWORD PTR $T7[rsp]
  000a1	48 8b 4c 24 58	 mov	 rcx, QWORD PTR $T8[rsp]
  000a6	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  000a9	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 611  :         const auto _Result    = this->_Ptr;

  000ac	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  000b1	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  000b5	48 89 04 24	 mov	 QWORD PTR _Result$[rsp], rax

; 612  :         this->_Ptr            = pointer{};

  000b9	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  000be	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0

; 613  :         _Insert_before->_Prev = _Result;

  000c6	48 8b 44 24 78	 mov	 rax, QWORD PTR _Insert_before$[rsp]
  000cb	48 8b 0c 24	 mov	 rcx, QWORD PTR _Result$[rsp]
  000cf	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 614  :         _Insert_after->_Next  = _Result;

  000d3	48 8b 44 24 08	 mov	 rax, QWORD PTR _Insert_after$[rsp]
  000d8	48 8b 0c 24	 mov	 rcx, QWORD PTR _Result$[rsp]
  000dc	48 89 08	 mov	 QWORD PTR [rax], rcx

; 615  :         return _Result;

  000df	48 8b 04 24	 mov	 rax, QWORD PTR _Result$[rsp]

; 616  :     }

  000e3	48 83 c4 68	 add	 rsp, 104		; 00000068H
  000e7	c3		 ret	 0
?_Transfer_before@?$_List_node_emplace_op2@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@2@QEAU32@@Z ENDP ; std::_List_node_emplace_op2<std::allocator<std::_List_node<mu2::JoinRankManager::RankInfo,void *> > >::_Transfer_before
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??1?$_Alloc_construct_ptr@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAA@XZ
_TEXT	SEGMENT
_Bytes$ = 32
_Ptr$ = 40
_Ptr$ = 48
this$ = 56
this$ = 80
??1?$_Alloc_construct_ptr@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAA@XZ PROC ; std::_Alloc_construct_ptr<std::allocator<std::_List_node<mu2::JoinRankManager::RankInfo,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<mu2::JoinRankManager::RankInfo,void *> > >, COMDAT

; 1171 :     _CONSTEXPR20 ~_Alloc_construct_ptr() { // if this instance is engaged, deallocate storage

$LN20:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 1172 :         if (_Ptr) {

  00009	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00013	74 5e		 je	 SHORT $LN2@Alloc_cons

; 1173 :             _Al.deallocate(_Ptr, 1);

  00015	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0001e	48 89 44 24 30	 mov	 QWORD PTR _Ptr$[rsp], rax
  00023	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00028	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0002b	48 89 44 24 38	 mov	 QWORD PTR this$[rsp], rax

; 985  :         _STD _Deallocate<_New_alignof<_Ty>>(_Ptr, sizeof(_Ty) * _Count);

  00030	b8 01 00 00 00	 mov	 eax, 1
  00035	48 6b c0 18	 imul	 rax, rax, 24
  00039	48 89 44 24 20	 mov	 QWORD PTR _Bytes$[rsp], rax
  0003e	48 8b 44 24 30	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00043	48 89 44 24 28	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  00048	48 81 7c 24 20
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  00051	72 10		 jb	 SHORT $LN11@Alloc_cons

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  00053	48 8d 54 24 20	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  00058	48 8d 4c 24 28	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  0005d	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  00062	90		 npad	 1
$LN11@Alloc_cons:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  00063	48 8b 54 24 20	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  00068	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  0006d	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00072	90		 npad	 1
$LN2@Alloc_cons:

; 1174 :         }
; 1175 :     }

  00073	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00077	c3		 ret	 0
??1?$_Alloc_construct_ptr@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAA@XZ ENDP ; std::_Alloc_construct_ptr<std::allocator<std::_List_node<mu2::JoinRankManager::RankInfo,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<mu2::JoinRankManager::RankInfo,void *> > >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
;	COMDAT ??$_Emplace@AEAURankInfo@JoinRankManager@mu2@@@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@QEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@1@QEAU21@AEAURankInfo@JoinRankManager@mu2@@@Z
_TEXT	SEGMENT
_Mysize$ = 32
$T1 = 40
$T2 = 48
tv93 = 56
_Op$ = 64
$T3 = 80
$T4 = 88
$T5 = 96
$T6 = 104
$T7 = 112
$T8 = 120
$T9 = 128
$T10 = 136
$T11 = 144
tv82 = 152
tv80 = 160
_Val$ = 168
$T12 = 176
$T13 = 184
_Unsigned_max$14 = 192
$T15 = 200
this$ = 224
_Where$ = 232
<_Val_0>$ = 240
??$_Emplace@AEAURankInfo@JoinRankManager@mu2@@@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@QEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@1@QEAU21@AEAURankInfo@JoinRankManager@mu2@@@Z PROC ; std::list<mu2::JoinRankManager::RankInfo,std::allocator<mu2::JoinRankManager::RankInfo> >::_Emplace<mu2::JoinRankManager::RankInfo &>, COMDAT

; 1028 :     _Nodeptr _Emplace(const _Nodeptr _Where, _Valty&&... _Val) { // insert element at _Where

$LN172:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 81 ec d8 00
	00 00		 sub	 rsp, 216		; 000000d8H

; 1029 :         size_type& _Mysize = _Mypair._Myval2._Mysize;

  00016	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0001e	48 83 c0 08	 add	 rax, 8
  00022	48 89 44 24 20	 mov	 QWORD PTR _Mysize$[rsp], rax

; 1867 :         return _Mypair._Get_first();

  00027	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1543 :         return *this;

  0002f	48 89 44 24 50	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 1867 :         return _Mypair._Get_first();

  00034	48 8b 44 24 50	 mov	 rax, QWORD PTR $T3[rsp]
  00039	48 89 84 24 b8
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 746  :         return static_cast<size_t>(-1) / sizeof(value_type);

  00041	48 b8 aa aa aa
	aa aa aa aa 0a	 mov	 rax, 768614336404564650	; 0aaaaaaaaaaaaaaaH
  0004b	48 89 44 24 58	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 1208 :         return (_STD min)(

  00050	48 8b 44 24 58	 mov	 rax, QWORD PTR $T4[rsp]
  00055	48 89 44 24 28	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 866  :         constexpr auto _Unsigned_max = static_cast<make_unsigned_t<_Ty>>(-1);

  0005a	48 c7 84 24 c0
	00 00 00 ff ff
	ff ff		 mov	 QWORD PTR _Unsigned_max$14[rsp], -1

; 867  :         return static_cast<_Ty>(_Unsigned_max >> 1);

  00066	48 b8 ff ff ff
	ff ff ff ff 7f	 mov	 rax, 9223372036854775807 ; 7fffffffffffffffH
  00070	48 89 44 24 60	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 1208 :         return (_STD min)(

  00075	48 8b 44 24 60	 mov	 rax, QWORD PTR $T5[rsp]
  0007a	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility

; 101  :     return _Right < _Left ? _Right : _Left;

  0007f	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
  00084	48 39 44 24 28	 cmp	 QWORD PTR $T1[rsp], rax
  00089	73 0c		 jae	 SHORT $LN32@Emplace
  0008b	48 8d 44 24 28	 lea	 rax, QWORD PTR $T1[rsp]
  00090	48 89 44 24 38	 mov	 QWORD PTR tv93[rsp], rax
  00095	eb 0a		 jmp	 SHORT $LN33@Emplace
$LN32@Emplace:
  00097	48 8d 44 24 30	 lea	 rax, QWORD PTR $T2[rsp]
  0009c	48 89 44 24 38	 mov	 QWORD PTR tv93[rsp], rax
$LN33@Emplace:
  000a1	48 8b 44 24 38	 mov	 rax, QWORD PTR tv93[rsp]
  000a6	48 89 44 24 68	 mov	 QWORD PTR $T6[rsp], rax
  000ab	48 8b 44 24 68	 mov	 rax, QWORD PTR $T6[rsp]
  000b0	48 89 44 24 70	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 1208 :         return (_STD min)(

  000b5	48 8b 44 24 70	 mov	 rax, QWORD PTR $T7[rsp]
  000ba	48 8b 00	 mov	 rax, QWORD PTR [rax]
  000bd	48 89 44 24 78	 mov	 QWORD PTR $T8[rsp], rax

; 1030 :         if (_Mysize == max_size()) {

  000c2	48 8b 44 24 78	 mov	 rax, QWORD PTR $T8[rsp]
  000c7	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Mysize$[rsp]
  000cc	48 39 01	 cmp	 QWORD PTR [rcx], rax
  000cf	75 0d		 jne	 SHORT $LN2@Emplace

; 1031 :             _Xlength_error("list too long");

  000d1	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_0O@NKNMEGII@list?5too?5long@
  000d8	e8 00 00 00 00	 call	 ?_Xlength_error@std@@YAXPEBD@Z ; std::_Xlength_error
  000dd	90		 npad	 1
$LN2@Emplace:

; 1863 :         return _Mypair._Get_first();

  000de	48 8b 84 24 e0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  000e6	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 1863 :         return _Mypair._Get_first();

  000ee	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR $T9[rsp]
  000f6	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax

; 1034 :         _List_node_emplace_op2<_Alnode> _Op{_Getal(), _STD forward<_Valty>(_Val)...};

  000fe	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  00106	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR tv80[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  0010e	48 8b 84 24 f0
	00 00 00	 mov	 rax, QWORD PTR <_Val_0>$[rsp]
  00116	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 1034 :         _List_node_emplace_op2<_Alnode> _Op{_Getal(), _STD forward<_Valty>(_Val)...};

  0011e	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR $T11[rsp]
  00126	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR tv82[rsp], rax
  0012e	4c 8b 84 24 98
	00 00 00	 mov	 r8, QWORD PTR tv82[rsp]
  00136	48 8b 94 24 a0
	00 00 00	 mov	 rdx, QWORD PTR tv80[rsp]
  0013e	48 8d 4c 24 40	 lea	 rcx, QWORD PTR _Op$[rsp]
  00143	e8 00 00 00 00	 call	 ??$?0AEAURankInfo@JoinRankManager@mu2@@@?$_List_node_emplace_op2@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAA@AEAV?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@1@AEAURankInfo@JoinRankManager@mu2@@@Z ; std::_List_node_emplace_op2<std::allocator<std::_List_node<mu2::JoinRankManager::RankInfo,void *> > >::_List_node_emplace_op2<std::allocator<std::_List_node<mu2::JoinRankManager::RankInfo,void *> > ><mu2::JoinRankManager::RankInfo &>

; 1035 :         ++_Mysize;

  00148	48 8b 44 24 20	 mov	 rax, QWORD PTR _Mysize$[rsp]
  0014d	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00150	48 ff c0	 inc	 rax
  00153	48 8b 4c 24 20	 mov	 rcx, QWORD PTR _Mysize$[rsp]
  00158	48 89 01	 mov	 QWORD PTR [rcx], rax

; 1036 :         return _Op._Transfer_before(_Where);

  0015b	48 8b 94 24 e8
	00 00 00	 mov	 rdx, QWORD PTR _Where$[rsp]
  00163	48 8d 4c 24 40	 lea	 rcx, QWORD PTR _Op$[rsp]
  00168	e8 00 00 00 00	 call	 ?_Transfer_before@?$_List_node_emplace_op2@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@2@QEAU32@@Z ; std::_List_node_emplace_op2<std::allocator<std::_List_node<mu2::JoinRankManager::RankInfo,void *> > >::_Transfer_before
  0016d	48 89 84 24 b0
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax

; 599  :         if (this->_Ptr != pointer{}) {

  00175	48 83 7c 24 48
	00		 cmp	 QWORD PTR _Op$[rsp+8], 0
  0017b	74 21		 je	 SHORT $LN139@Emplace

; 600  :             _Alnode_traits::destroy(this->_Al, _STD addressof(this->_Ptr->_Myval));

  0017d	48 8b 44 24 48	 mov	 rax, QWORD PTR _Op$[rsp+8]
  00182	48 83 c0 10	 add	 rax, 16
  00186	48 89 84 24 a8
	00 00 00	 mov	 QWORD PTR _Val$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0018e	48 8b 84 24 a8
	00 00 00	 mov	 rax, QWORD PTR _Val$[rsp]
  00196	48 89 84 24 c8
	00 00 00	 mov	 QWORD PTR $T15[rsp], rax
$LN139@Emplace:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 602  :     }

  0019e	48 8d 4c 24 40	 lea	 rcx, QWORD PTR _Op$[rsp]
  001a3	e8 00 00 00 00	 call	 ??1?$_Alloc_construct_ptr@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@std@@QEAA@XZ ; std::_Alloc_construct_ptr<std::allocator<std::_List_node<mu2::JoinRankManager::RankInfo,void *> > >::~_Alloc_construct_ptr<std::allocator<std::_List_node<mu2::JoinRankManager::RankInfo,void *> > >
  001a8	90		 npad	 1

; 1036 :         return _Op._Transfer_before(_Where);

  001a9	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR $T12[rsp]

; 1037 :     }

  001b1	48 81 c4 d8 00
	00 00		 add	 rsp, 216		; 000000d8H
  001b8	c3		 ret	 0
??$_Emplace@AEAURankInfo@JoinRankManager@mu2@@@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@QEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@1@QEAU21@AEAURankInfo@JoinRankManager@mu2@@@Z ENDP ; std::list<mu2::JoinRankManager::RankInfo,std::allocator<mu2::JoinRankManager::RankInfo> >::_Emplace<mu2::JoinRankManager::RankInfo &>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z
_TEXT	SEGMENT
_Ptr_container$ = 48
_Block_size$ = 56
_Ptr$ = 64
$T1 = 72
_Bytes$ = 96
??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z PROC ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>, COMDAT

; 182  : __declspec(allocator) void* _Allocate_manually_vector_aligned(const size_t _Bytes) {

$LN7:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 183  :     // allocate _Bytes manually aligned to at least _Big_allocation_alignment
; 184  :     const size_t _Block_size = _Non_user_size + _Bytes;

  00009	48 8b 44 24 60	 mov	 rax, QWORD PTR _Bytes$[rsp]
  0000e	48 83 c0 27	 add	 rax, 39			; 00000027H
  00012	48 89 44 24 38	 mov	 QWORD PTR _Block_size$[rsp], rax

; 185  :     if (_Block_size <= _Bytes) {

  00017	48 8b 44 24 60	 mov	 rax, QWORD PTR _Bytes$[rsp]
  0001c	48 39 44 24 38	 cmp	 QWORD PTR _Block_size$[rsp], rax
  00021	77 06		 ja	 SHORT $LN2@Allocate_m

; 186  :         _Throw_bad_array_new_length(); // add overflow

  00023	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00028	90		 npad	 1
$LN2@Allocate_m:

; 136  :         return ::operator new(_Bytes);

  00029	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Block_size$[rsp]
  0002e	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00033	48 89 44 24 48	 mov	 QWORD PTR $T1[rsp], rax

; 187  :     }
; 188  : 
; 189  :     const uintptr_t _Ptr_container = reinterpret_cast<uintptr_t>(_Traits::_Allocate(_Block_size));

  00038	48 8b 44 24 48	 mov	 rax, QWORD PTR $T1[rsp]
  0003d	48 89 44 24 30	 mov	 QWORD PTR _Ptr_container$[rsp], rax

; 190  :     _STL_VERIFY(_Ptr_container != 0, "invalid argument"); // validate even in release since we're doing p[-1]

  00042	48 83 7c 24 30
	00		 cmp	 QWORD PTR _Ptr_container$[rsp], 0
  00048	75 19		 jne	 SHORT $LN3@Allocate_m
  0004a	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR [rsp+32], 0
  00053	45 33 c9	 xor	 r9d, r9d
  00056	45 33 c0	 xor	 r8d, r8d
  00059	33 d2		 xor	 edx, edx
  0005b	33 c9		 xor	 ecx, ecx
  0005d	e8 00 00 00 00	 call	 _invoke_watson
  00062	90		 npad	 1
$LN3@Allocate_m:

; 191  :     void* const _Ptr = reinterpret_cast<void*>((_Ptr_container + _Non_user_size) & ~(_Big_allocation_alignment - 1));

  00063	48 8b 44 24 30	 mov	 rax, QWORD PTR _Ptr_container$[rsp]
  00068	48 83 c0 27	 add	 rax, 39			; 00000027H
  0006c	48 83 e0 e0	 and	 rax, -32		; ffffffffffffffe0H
  00070	48 89 44 24 40	 mov	 QWORD PTR _Ptr$[rsp], rax

; 192  :     static_cast<uintptr_t*>(_Ptr)[-1] = _Ptr_container;

  00075	b8 08 00 00 00	 mov	 eax, 8
  0007a	48 6b c0 ff	 imul	 rax, rax, -1
  0007e	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  00083	48 8b 54 24 30	 mov	 rdx, QWORD PTR _Ptr_container$[rsp]
  00088	48 89 14 01	 mov	 QWORD PTR [rcx+rax], rdx

; 193  : 
; 194  : #ifdef _DEBUG
; 195  :     static_cast<uintptr_t*>(_Ptr)[-2] = _Big_allocation_sentinel;
; 196  : #endif // defined(_DEBUG)
; 197  :     return _Ptr;

  0008c	48 8b 44 24 40	 mov	 rax, QWORD PTR _Ptr$[rsp]
$LN4@Allocate_m:

; 198  : }

  00091	48 83 c4 58	 add	 rsp, 88			; 00000058H
  00095	c3		 ret	 0
??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ENDP ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??_GJoinRankManager@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GJoinRankManager@mu2@@UEAAPEAXI@Z PROC		; mu2::JoinRankManager::`scalar deleting destructor', COMDAT
$LN5:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00012	e8 00 00 00 00	 call	 ??1JoinRankManager@mu2@@UEAA@XZ ; mu2::JoinRankManager::~JoinRankManager
  00017	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  0001b	83 e0 01	 and	 eax, 1
  0001e	85 c0		 test	 eax, eax
  00020	74 10		 je	 SHORT $LN2@scalar
  00022	ba 28 00 00 00	 mov	 edx, 40			; 00000028H
  00027	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0002c	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00031	90		 npad	 1
$LN2@scalar:
  00032	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00037	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0003b	c3		 ret	 0
??_GJoinRankManager@mu2@@UEAAPEAXI@Z ENDP		; mu2::JoinRankManager::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp
;	COMDAT ??0JoinRankManager@mu2@@AEAA@XZ
_TEXT	SEGMENT
this$ = 32
this$ = 40
this$ = 48
this$ = 80
??0JoinRankManager@mu2@@AEAA@XZ PROC			; mu2::JoinRankManager::JoinRankManager, COMDAT

; 10   : {

$LN99:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 48	 sub	 rsp, 72			; 00000048H
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 84   : 	ISingleton() {}	

  00009	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$ISingleton@VJoinRankManager@mu2@@@mu2@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp

; 10   : {

  00018	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7JoinRankManager@mu2@@6B@
  00024	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.h

; 21   : 	UInt32					firstWaitUserIndex = 0;

  00027	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0002c	c7 40 08 00 00
	00 00		 mov	 DWORD PTR [rax+8], 0

; 22   : 	UInt32					lastWaitUserIndex = 0;

  00033	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00038	c7 40 0c 00 00
	00 00		 mov	 DWORD PTR [rax+12], 0
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp

; 10   : {

  0003f	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00044	48 83 c0 10	 add	 rax, 16
  00048	48 89 44 24 28	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 812  :     list() : _Mypair(_Zero_then_variadic_args_t{}) {

  0004d	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  00052	48 89 44 24 30	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1531 :         : _Ty1(), _Myval2(_STD forward<_Other2>(_Val2)...) {}

  00057	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0005c	48 89 44 24 20	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 353  :     _List_val() noexcept : _Myhead(), _Mysize(0) {} // initialize data

  00061	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00066	48 c7 00 00 00
	00 00		 mov	 QWORD PTR [rax], 0
  0006d	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  00072	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0

; 813  :         _Alloc_sentinel_and_proxy();

  0007a	48 8b 4c 24 28	 mov	 rcx, QWORD PTR this$[rsp]
  0007f	e8 00 00 00 00	 call	 ?_Alloc_sentinel_and_proxy@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@AEAAXXZ ; std::list<mu2::JoinRankManager::RankInfo,std::allocator<mu2::JoinRankManager::RankInfo> >::_Alloc_sentinel_and_proxy
  00084	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp

; 10   : {

  00085	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0008a	48 83 c0 20	 add	 rax, 32			; 00000020H
  0008e	48 8b c8	 mov	 rcx, rax
  00091	e8 00 00 00 00	 call	 ??0JoinRankTimeCalculator@mu2@@QEAA@XZ ; mu2::JoinRankTimeCalculator::JoinRankTimeCalculator
  00096	90		 npad	 1

; 11   : }

  00097	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0009c	48 83 c4 48	 add	 rsp, 72			; 00000048H
  000a0	c3		 ret	 0
??0JoinRankManager@mu2@@AEAA@XZ ENDP			; mu2::JoinRankManager::JoinRankManager
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
this$ = 32
this$ = 40
this$ = 48
this$ = 80
?dtor$0@?0???0JoinRankManager@mu2@@AEAA@XZ@4HA PROC	; `mu2::JoinRankManager::JoinRankManager'::`1'::dtor$0
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 50	 mov	 rcx, QWORD PTR this$[rbp]
  0000d	e8 00 00 00 00	 call	 ??1?$ISingleton@VJoinRankManager@mu2@@@mu2@@UEAA@XZ ; mu2::ISingleton<mu2::JoinRankManager>::~ISingleton<mu2::JoinRankManager>
  00012	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00016	5d		 pop	 rbp
  00017	c3		 ret	 0
?dtor$0@?0???0JoinRankManager@mu2@@AEAA@XZ@4HA ENDP	; `mu2::JoinRankManager::JoinRankManager'::`1'::dtor$0
text$x	ENDS
; Function compile flags: /Odtp
;	COMDAT text$x
text$x	SEGMENT
this$ = 32
this$ = 40
this$ = 48
this$ = 80
?dtor$1@?0???0JoinRankManager@mu2@@AEAA@XZ@4HA PROC	; `mu2::JoinRankManager::JoinRankManager'::`1'::dtor$1
  00000	40 55		 push	 rbp
  00002	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  00006	48 8b ea	 mov	 rbp, rdx
  00009	48 8b 4d 50	 mov	 rcx, QWORD PTR this$[rbp]
  0000d	48 83 c1 10	 add	 rcx, 16
  00011	e8 00 00 00 00	 call	 ??1?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@QEAA@XZ ; std::list<mu2::JoinRankManager::RankInfo,std::allocator<mu2::JoinRankManager::RankInfo> >::~list<mu2::JoinRankManager::RankInfo,std::allocator<mu2::JoinRankManager::RankInfo> >
  00016	48 83 c4 20	 add	 rsp, 32			; 00000020H
  0001a	5d		 pop	 rbp
  0001b	c3		 ret	 0
?dtor$1@?0???0JoinRankManager@mu2@@AEAA@XZ@4HA ENDP	; `mu2::JoinRankManager::JoinRankManager'::`1'::dtor$1
text$x	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp
;	COMDAT ?GetWaitUserCount@JoinRankManager@mu2@@QEBAIXZ
_TEXT	SEGMENT
this$ = 48
?GetWaitUserCount@JoinRankManager@mu2@@QEBAIXZ PROC	; mu2::JoinRankManager::GetWaitUserCount, COMDAT

; 82   : {

$LN4:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 83   : 	MU2_ASSERT(firstWaitUserIndex <= lastWaitUserIndex); 

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00013	8b 49 0c	 mov	 ecx, DWORD PTR [rcx+12]
  00016	39 48 08	 cmp	 DWORD PTR [rax+8], ecx
  00019	76 21		 jbe	 SHORT $LN2@GetWaitUse
  0001b	41 b9 53 00 00
	00		 mov	 r9d, 83			; 00000053H
  00021	4c 8d 05 00 00
	00 00		 lea	 r8, OFFSET FLAT:??_C@_0EO@KGDNFAOG@F?3?2Release_Branch?2Server?2Develo@
  00028	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:??_C@_0CI@OPIGHMJ@firstWaitUserIndex?5?$DM?$DN?5lastWaitU@
  0002f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_09OLBBGJEO@Assertion@
  00036	e8 00 00 00 00	 call	 ?LogRuntimeAssertionFailure@mu2@@YAXPEBD00_K@Z ; mu2::LogRuntimeAssertionFailure
  0003b	90		 npad	 1
$LN2@GetWaitUse:

; 84   : 	return lastWaitUserIndex - firstWaitUserIndex;

  0003c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00041	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00046	8b 49 08	 mov	 ecx, DWORD PTR [rcx+8]
  00049	8b 40 0c	 mov	 eax, DWORD PTR [rax+12]
  0004c	2b c1		 sub	 eax, ecx

; 85   : }

  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
?GetWaitUserCount@JoinRankManager@mu2@@QEBAIXZ ENDP	; mu2::JoinRankManager::GetWaitUserCount
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp
;	COMDAT ?GetFirstWaitUserIndex@JoinRankManager@mu2@@QEAAIXZ
_TEXT	SEGMENT
this$ = 8
?GetFirstWaitUserIndex@JoinRankManager@mu2@@QEAAIXZ PROC ; mu2::JoinRankManager::GetFirstWaitUserIndex, COMDAT

; 77   : { 

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 78   : 	return firstWaitUserIndex; 

  00005	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0000a	8b 40 08	 mov	 eax, DWORD PTR [rax+8]

; 79   : }

  0000d	c3		 ret	 0
?GetFirstWaitUserIndex@JoinRankManager@mu2@@QEAAIXZ ENDP ; mu2::JoinRankManager::GetFirstWaitUserIndex
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp
;	COMDAT ?GetWaitList@JoinRankManager@mu2@@QEAAAEAV?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@XZ
_TEXT	SEGMENT
this$ = 8
?GetWaitList@JoinRankManager@mu2@@QEAAAEAV?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@XZ PROC ; mu2::JoinRankManager::GetWaitList, COMDAT

; 72   : { 

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 73   : 	return waitList; 

  00005	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0000a	48 83 c0 10	 add	 rax, 16

; 74   : }

  0000e	c3		 ret	 0
?GetWaitList@JoinRankManager@mu2@@QEAAAEAV?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@XZ ENDP ; mu2::JoinRankManager::GetWaitList
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp
;	COMDAT ?LeaveWaitLine@JoinRankManager@mu2@@QEAAXI@Z
_TEXT	SEGMENT
$T1 = 32
$T2 = 33
tv206 = 36
tv66 = 40
it$3 = 48
this$ = 56
this$ = 64
__param0$ = 72
__param0$ = 80
$T4 = 88
$T5 = 96
$T6 = 104
$T7 = 112
$T8 = 120
$T9 = 128
$T10 = 136
$T11 = 144
$T12 = 152
$T13 = 160
$T14 = 168
this$ = 192
sessionKey$ = 200
?LeaveWaitLine@JoinRankManager@mu2@@QEAAXI@Z PROC	; mu2::JoinRankManager::LeaveWaitLine, COMDAT

; 59   : {

$LN213:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 81 ec b8 00
	00 00		 sub	 rsp, 184		; 000000b8H

; 60   : 	for ( auto it = waitList.begin(); it != waitList.end(); ++it )

  00010	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00018	48 83 c0 10	 add	 rax, 16
  0001c	48 89 44 24 38	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 1105 :         return iterator(_Mypair._Myval2._Myhead->_Next, _STD addressof(_Mypair._Myval2));

  00021	48 8b 44 24 38	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00026	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR $T12[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 1105 :         return iterator(_Mypair._Myval2._Myhead->_Next, _STD addressof(_Mypair._Myval2));

  0002e	48 8b 44 24 38	 mov	 rax, QWORD PTR this$[rsp]
  00033	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00036	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00039	48 89 44 24 48	 mov	 QWORD PTR __param0$[rsp], rax

; 37   :     _List_unchecked_const_iterator(_Nodeptr _Pnode, const _Mylist* _Plist) noexcept : _Ptr(_Pnode) {

  0003e	48 8b 44 24 48	 mov	 rax, QWORD PTR __param0$[rsp]
  00043	48 89 44 24 30	 mov	 QWORD PTR it$3[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp

; 60   : 	for ( auto it = waitList.begin(); it != waitList.end(); ++it )

  00048	eb 0d		 jmp	 SHORT $LN4@LeaveWaitL
$LN2@LeaveWaitL:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 164  :         this->_Ptr = this->_Ptr->_Next;

  0004a	48 8b 44 24 30	 mov	 rax, QWORD PTR it$3[rsp]
  0004f	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00052	48 89 44 24 30	 mov	 QWORD PTR it$3[rsp], rax
$LN4@LeaveWaitL:
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp

; 60   : 	for ( auto it = waitList.begin(); it != waitList.end(); ++it )

  00057	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0005f	48 83 c0 10	 add	 rax, 16
  00063	48 89 44 24 40	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 1113 :         return iterator(_Mypair._Myval2._Myhead, _STD addressof(_Mypair._Myval2));

  00068	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0006d	48 89 84 24 a0
	00 00 00	 mov	 QWORD PTR $T13[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 1113 :         return iterator(_Mypair._Myval2._Myhead, _STD addressof(_Mypair._Myval2));

  00075	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0007a	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0007d	48 89 44 24 50	 mov	 QWORD PTR __param0$[rsp], rax

; 37   :     _List_unchecked_const_iterator(_Nodeptr _Pnode, const _Mylist* _Plist) noexcept : _Ptr(_Pnode) {

  00082	48 8b 44 24 50	 mov	 rax, QWORD PTR __param0$[rsp]
  00087	48 89 44 24 58	 mov	 QWORD PTR $T4[rsp], rax

; 1113 :         return iterator(_Mypair._Myval2._Myhead, _STD addressof(_Mypair._Myval2));

  0008c	48 8d 44 24 58	 lea	 rax, QWORD PTR $T4[rsp]
  00091	48 89 44 24 60	 mov	 QWORD PTR $T5[rsp], rax

; 197  :         return this->_Ptr == _Right._Ptr;

  00096	48 8b 44 24 60	 mov	 rax, QWORD PTR $T5[rsp]
  0009b	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0009e	48 39 44 24 30	 cmp	 QWORD PTR it$3[rsp], rax
  000a3	75 0a		 jne	 SHORT $LN86@LeaveWaitL
  000a5	c7 44 24 24 01
	00 00 00	 mov	 DWORD PTR tv206[rsp], 1
  000ad	eb 08		 jmp	 SHORT $LN87@LeaveWaitL
$LN86@LeaveWaitL:
  000af	c7 44 24 24 00
	00 00 00	 mov	 DWORD PTR tv206[rsp], 0
$LN87@LeaveWaitL:
  000b7	0f b6 44 24 24	 movzx	 eax, BYTE PTR tv206[rsp]
  000bc	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al

; 202  :         return !(*this == _Right);

  000c0	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  000c5	0f b6 c0	 movzx	 eax, al
  000c8	85 c0		 test	 eax, eax
  000ca	75 0a		 jne	 SHORT $LN79@LeaveWaitL
  000cc	c7 44 24 28 01
	00 00 00	 mov	 DWORD PTR tv66[rsp], 1
  000d4	eb 08		 jmp	 SHORT $LN80@LeaveWaitL
$LN79@LeaveWaitL:
  000d6	c7 44 24 28 00
	00 00 00	 mov	 DWORD PTR tv66[rsp], 0
$LN80@LeaveWaitL:
  000de	0f b6 44 24 28	 movzx	 eax, BYTE PTR tv66[rsp]
  000e3	88 44 24 21	 mov	 BYTE PTR $T2[rsp], al
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp

; 60   : 	for ( auto it = waitList.begin(); it != waitList.end(); ++it )

  000e7	0f b6 44 24 21	 movzx	 eax, BYTE PTR $T2[rsp]
  000ec	0f b6 c0	 movzx	 eax, al
  000ef	85 c0		 test	 eax, eax
  000f1	0f 84 a3 00 00
	00		 je	 $LN3@LeaveWaitL
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 150  :         return this->_Ptr->_Myval;

  000f7	48 8b 44 24 30	 mov	 rax, QWORD PTR it$3[rsp]
  000fc	48 83 c0 10	 add	 rax, 16
  00100	48 89 44 24 68	 mov	 QWORD PTR $T6[rsp], rax

; 238  :         return const_cast<reference>(_Mybase::operator*());

  00105	48 8b 44 24 68	 mov	 rax, QWORD PTR $T6[rsp]
  0010a	48 89 44 24 70	 mov	 QWORD PTR $T7[rsp], rax

; 242  :         return pointer_traits<pointer>::pointer_to(**this);

  0010f	48 8b 44 24 70	 mov	 rax, QWORD PTR $T7[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00114	48 89 44 24 78	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 528  :         return _STD addressof(_Val);

  00119	48 8b 44 24 78	 mov	 rax, QWORD PTR $T8[rsp]
  0011e	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 242  :         return pointer_traits<pointer>::pointer_to(**this);

  00126	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR $T9[rsp]
  0012e	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp

; 62   : 		if ( it->sessionKey == sessionKey)

  00136	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  0013e	8b 8c 24 c8 00
	00 00		 mov	 ecx, DWORD PTR sessionKey$[rsp]
  00145	39 08		 cmp	 DWORD PTR [rax], ecx
  00147	75 4c		 jne	 SHORT $LN5@LeaveWaitL

; 63   : 		{
; 64   : 			firstWaitUserIndex++;

  00149	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00151	8b 40 08	 mov	 eax, DWORD PTR [rax+8]
  00154	ff c0		 inc	 eax
  00156	48 8b 8c 24 c0
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  0015e	89 41 08	 mov	 DWORD PTR [rcx+8], eax

; 65   : 			waitList.erase( it );

  00161	48 8b 44 24 30	 mov	 rax, QWORD PTR it$3[rsp]
  00166	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
  0016e	48 8b 84 24 c0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00176	48 83 c0 10	 add	 rax, 16
  0017a	4c 8b 84 24 90
	00 00 00	 mov	 r8, QWORD PTR $T11[rsp]
  00182	48 8d 94 24 a8
	00 00 00	 lea	 rdx, QWORD PTR $T14[rsp]
  0018a	48 8b c8	 mov	 rcx, rax
  0018d	e8 00 00 00 00	 call	 ?erase@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@QEAA?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@URankInfo@JoinRankManager@mu2@@@std@@@std@@@2@V?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@URankInfo@JoinRankManager@mu2@@@std@@@std@@@2@@Z ; std::list<mu2::JoinRankManager::RankInfo,std::allocator<mu2::JoinRankManager::RankInfo> >::erase
  00192	90		 npad	 1

; 66   : 			break;

  00193	eb 05		 jmp	 SHORT $LN3@LeaveWaitL
$LN5@LeaveWaitL:

; 67   : 		}
; 68   : 	}	

  00195	e9 b0 fe ff ff	 jmp	 $LN2@LeaveWaitL
$LN3@LeaveWaitL:

; 69   : }

  0019a	48 81 c4 b8 00
	00 00		 add	 rsp, 184		; 000000b8H
  001a1	c3		 ret	 0
?LeaveWaitLine@JoinRankManager@mu2@@QEAAXI@Z ENDP	; mu2::JoinRankManager::LeaveWaitLine
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp
;	COMDAT ?GetJoinWaitSecond@JoinRankManager@mu2@@QEAAII@Z
_TEXT	SEGMENT
this$ = 48
index$ = 56
?GetJoinWaitSecond@JoinRankManager@mu2@@QEAAII@Z PROC	; mu2::JoinRankManager::GetJoinWaitSecond, COMDAT

; 54   : {

$LN3:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 55   : 	return timeCalculator.GetTime( index );

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 83 c0 20	 add	 rax, 32			; 00000020H
  00016	8b 54 24 38	 mov	 edx, DWORD PTR index$[rsp]
  0001a	48 8b c8	 mov	 rcx, rax
  0001d	e8 00 00 00 00	 call	 ?GetTime@JoinRankTimeCalculator@mu2@@QEBAII@Z ; mu2::JoinRankTimeCalculator::GetTime

; 56   : }

  00022	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00026	c3		 ret	 0
?GetJoinWaitSecond@JoinRankManager@mu2@@QEAAII@Z ENDP	; mu2::JoinRankManager::GetJoinWaitSecond
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp
;	COMDAT ?UpdateJoinWaitSecond@JoinRankManager@mu2@@QEAAXXZ
_TEXT	SEGMENT
this$ = 48
?UpdateJoinWaitSecond@JoinRankManager@mu2@@QEAAXXZ PROC	; mu2::JoinRankManager::UpdateJoinWaitSecond, COMDAT

; 49   : {

$LN3:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 50   : 	timeCalculator.LeaveWait();

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 c0 20	 add	 rax, 32			; 00000020H
  00012	48 8b c8	 mov	 rcx, rax
  00015	e8 00 00 00 00	 call	 ?LeaveWait@JoinRankTimeCalculator@mu2@@QEAAXXZ ; mu2::JoinRankTimeCalculator::LeaveWait
  0001a	90		 npad	 1

; 51   : }

  0001b	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0001f	c3		 ret	 0
?UpdateJoinWaitSecond@JoinRankManager@mu2@@QEAAXXZ ENDP	; mu2::JoinRankManager::UpdateJoinWaitSecond
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp
;	COMDAT ?PopPlayer@JoinRankManager@mu2@@QEAAIXZ
_TEXT	SEGMENT
$T1 = 32
tv83 = 36
playerSeq$ = 40
this$ = 48
$T2 = 56
this$ = 80
?PopPlayer@JoinRankManager@mu2@@QEAAIXZ PROC		; mu2::JoinRankManager::PopPlayer, COMDAT

; 31   : {

$LN84:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 32   : 	UInt32 playerSeq = 0;

  00009	c7 44 24 28 00
	00 00 00	 mov	 DWORD PTR playerSeq$[rsp], 0
$LN2@PopPlayer:
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 1213 :         return _Mypair._Myval2._Mysize == 0;

  00011	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00016	48 83 78 18 00	 cmp	 QWORD PTR [rax+24], 0
  0001b	75 0a		 jne	 SHORT $LN7@PopPlayer
  0001d	c7 44 24 24 01
	00 00 00	 mov	 DWORD PTR tv83[rsp], 1
  00025	eb 08		 jmp	 SHORT $LN8@PopPlayer
$LN7@PopPlayer:
  00027	c7 44 24 24 00
	00 00 00	 mov	 DWORD PTR tv83[rsp], 0
$LN8@PopPlayer:
  0002f	0f b6 44 24 24	 movzx	 eax, BYTE PTR tv83[rsp]
  00034	88 44 24 20	 mov	 BYTE PTR $T1[rsp], al
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp

; 34   : 	while ( false == waitList.empty() )

  00038	0f b6 44 24 20	 movzx	 eax, BYTE PTR $T1[rsp]
  0003d	0f b6 c0	 movzx	 eax, al
  00040	85 c0		 test	 eax, eax
  00042	75 5d		 jne	 SHORT $LN3@PopPlayer

; 35   : 	{
; 36   : 		firstWaitUserIndex++;		

  00044	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  00049	8b 40 08	 mov	 eax, DWORD PTR [rax+8]
  0004c	ff c0		 inc	 eax
  0004e	48 8b 4c 24 50	 mov	 rcx, QWORD PTR this$[rsp]
  00053	89 41 08	 mov	 DWORD PTR [rcx+8], eax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 1225 :         return _Mypair._Myval2._Myhead->_Next->_Myval;

  00056	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0005b	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0005f	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00062	48 83 c0 10	 add	 rax, 16
  00066	48 89 44 24 38	 mov	 QWORD PTR $T2[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp

; 38   : 		playerSeq = waitList.front().sessionKey;

  0006b	48 8b 44 24 38	 mov	 rax, QWORD PTR $T2[rsp]
  00070	8b 00		 mov	 eax, DWORD PTR [rax]
  00072	89 44 24 28	 mov	 DWORD PTR playerSeq$[rsp], eax

; 39   : 
; 40   : 		waitList.pop_front();

  00076	48 8b 44 24 50	 mov	 rax, QWORD PTR this$[rsp]
  0007b	48 83 c0 10	 add	 rax, 16
  0007f	48 89 44 24 30	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 1270 :         _Unchecked_erase(_Mypair._Myval2._Myhead->_Next);

  00084	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00089	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0008c	48 8b 10	 mov	 rdx, QWORD PTR [rax]
  0008f	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00094	e8 00 00 00 00	 call	 ?_Unchecked_erase@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@AEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@2@QEAU32@@Z ; std::list<mu2::JoinRankManager::RankInfo,std::allocator<mu2::JoinRankManager::RankInfo> >::_Unchecked_erase
  00099	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp

; 42   : 		break;

  0009a	eb 05		 jmp	 SHORT $LN3@PopPlayer

; 43   : 	}		

  0009c	e9 70 ff ff ff	 jmp	 $LN2@PopPlayer
$LN3@PopPlayer:

; 44   : 
; 45   : 	return playerSeq;

  000a1	8b 44 24 28	 mov	 eax, DWORD PTR playerSeq$[rsp]

; 46   : }

  000a5	48 83 c4 48	 add	 rsp, 72			; 00000048H
  000a9	c3		 ret	 0
?PopPlayer@JoinRankManager@mu2@@QEAAIXZ ENDP		; mu2::JoinRankManager::PopPlayer
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp
;	COMDAT ?PushPlayer@JoinRankManager@mu2@@QEAAIAEBV?$SmartPtrEx@VEntityPlayer@mu2@@@2@AEAI@Z
_TEXT	SEGMENT
key$ = 32
cur$ = 40
rank$ = 48
this$ = 56
$T1 = 64
$T2 = 72
$T3 = 80
$T4 = 88
_Result$5 = 96
this$ = 128
playerEntity$ = 136
joinWaitSec$ = 144
?PushPlayer@JoinRankManager@mu2@@QEAAIAEBV?$SmartPtrEx@VEntityPlayer@mu2@@@2@AEAI@Z PROC ; mu2::JoinRankManager::PushPlayer, COMDAT

; 18   : {

$LN195:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 78	 sub	 rsp, 120		; 00000078H

; 19   : 	lastWaitUserIndex++;

  00013	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0001b	8b 40 0c	 mov	 eax, DWORD PTR [rax+12]
  0001e	ff c0		 inc	 eax
  00020	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00028	89 41 0c	 mov	 DWORD PTR [rcx+12], eax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\memory

; 1309 :         return _Ptr;

  0002b	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR playerEntity$[rsp]
  00033	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00036	48 89 44 24 40	 mov	 QWORD PTR $T1[rsp], rax
; File F:\Release_Branch\Server\Development\Framework\Core\SmartPtr.h

; 186  : 		return ptr.get();

  0003b	48 8b 44 24 40	 mov	 rax, QWORD PTR $T1[rsp]
  00040	48 89 44 24 48	 mov	 QWORD PTR $T2[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp

; 21   : 	RankInfo rank( playerEntity->GetSessionKey(), lastWaitUserIndex );

  00045	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0004d	8b 40 0c	 mov	 eax, DWORD PTR [rax+12]
  00050	89 44 24 28	 mov	 DWORD PTR cur$[rsp], eax
  00054	48 8b 44 24 48	 mov	 rax, QWORD PTR $T2[rsp]
  00059	48 8b c8	 mov	 rcx, rax
  0005c	e8 00 00 00 00	 call	 ?GetSessionKey@EntityPlayer@mu2@@QEBA?BIXZ ; mu2::EntityPlayer::GetSessionKey
  00061	89 44 24 20	 mov	 DWORD PTR key$[rsp], eax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.h

; 14   : 		RankInfo(SessionKey key, UInt32 cur): sessionKey( key ), currentMyIndex( cur ){}

  00065	8b 44 24 20	 mov	 eax, DWORD PTR key$[rsp]
  00069	89 44 24 30	 mov	 DWORD PTR rank$[rsp], eax
  0006d	8b 44 24 28	 mov	 eax, DWORD PTR cur$[rsp]
  00071	89 44 24 34	 mov	 DWORD PTR rank$[rsp+4], eax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 1204 :         return _Mypair._Myval2._Mysize;

  00075	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0007d	48 8b 40 18	 mov	 rax, QWORD PTR [rax+24]
  00081	48 89 44 24 50	 mov	 QWORD PTR $T3[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp

; 23   : 	joinWaitSec = timeCalculator.EnterWait(static_cast<UInt32>(waitList.size() + 1));

  00086	48 8b 44 24 50	 mov	 rax, QWORD PTR $T3[rsp]
  0008b	48 ff c0	 inc	 rax
  0008e	48 8b 8c 24 80
	00 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00096	48 83 c1 20	 add	 rcx, 32			; 00000020H
  0009a	8b d0		 mov	 edx, eax
  0009c	e8 00 00 00 00	 call	 ?EnterWait@JoinRankTimeCalculator@mu2@@QEAAII@Z ; mu2::JoinRankTimeCalculator::EnterWait
  000a1	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR joinWaitSec$[rsp]
  000a9	89 01		 mov	 DWORD PTR [rcx], eax

; 24   : 
; 25   : 	waitList.emplace_back(rank);

  000ab	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000b3	48 83 c0 10	 add	 rax, 16
  000b7	48 89 44 24 38	 mov	 QWORD PTR this$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  000bc	48 8d 44 24 30	 lea	 rax, QWORD PTR rank$[rsp]
  000c1	48 89 44 24 58	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 1009 :         reference _Result = _Emplace(_Mypair._Myval2._Myhead, _STD forward<_Valty>(_Val)...)->_Myval;

  000c6	48 8b 44 24 58	 mov	 rax, QWORD PTR $T4[rsp]
  000cb	4c 8b c0	 mov	 r8, rax
  000ce	48 8b 44 24 38	 mov	 rax, QWORD PTR this$[rsp]
  000d3	48 8b 10	 mov	 rdx, QWORD PTR [rax]
  000d6	48 8b 4c 24 38	 mov	 rcx, QWORD PTR this$[rsp]
  000db	e8 00 00 00 00	 call	 ??$_Emplace@AEAURankInfo@JoinRankManager@mu2@@@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@QEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@1@QEAU21@AEAURankInfo@JoinRankManager@mu2@@@Z ; std::list<mu2::JoinRankManager::RankInfo,std::allocator<mu2::JoinRankManager::RankInfo> >::_Emplace<mu2::JoinRankManager::RankInfo &>
  000e0	48 83 c0 10	 add	 rax, 16
  000e4	48 89 44 24 60	 mov	 QWORD PTR _Result$5[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp

; 27   : 	return (rank.currentMyIndex - firstWaitUserIndex);

  000e9	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000f1	8b 40 08	 mov	 eax, DWORD PTR [rax+8]
  000f4	8b 4c 24 34	 mov	 ecx, DWORD PTR rank$[rsp+4]
  000f8	2b c8		 sub	 ecx, eax
  000fa	8b c1		 mov	 eax, ecx

; 28   : }

  000fc	48 83 c4 78	 add	 rsp, 120		; 00000078H
  00100	c3		 ret	 0
?PushPlayer@JoinRankManager@mu2@@QEAAIAEBV?$SmartPtrEx@VEntityPlayer@mu2@@@2@AEAI@Z ENDP ; mu2::JoinRankManager::PushPlayer
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.h
;	COMDAT ?UnInit@JoinRankManager@mu2@@UEAA_NXZ
_TEXT	SEGMENT
this$ = 8
?UnInit@JoinRankManager@mu2@@UEAA_NXZ PROC		; mu2::JoinRankManager::UnInit, COMDAT

; 30   : 	virtual Bool	UnInit() override { return true; }

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	b0 01		 mov	 al, 1
  00007	c3		 ret	 0
?UnInit@JoinRankManager@mu2@@UEAA_NXZ ENDP		; mu2::JoinRankManager::UnInit
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.h
;	COMDAT ?Init@JoinRankManager@mu2@@UEAA_NPEAX@Z
_TEXT	SEGMENT
this$ = 8
param$ = 16
?Init@JoinRankManager@mu2@@UEAA_NPEAX@Z PROC		; mu2::JoinRankManager::Init, COMDAT

; 29   : 	virtual Bool	Init(void* param = NULL) override { UNREFERENCED_PARAMETER(param); return true; }

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	b0 01		 mov	 al, 1
  0000c	c3		 ret	 0
?Init@JoinRankManager@mu2@@UEAA_NPEAX@Z ENDP		; mu2::JoinRankManager::Init
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp
;	COMDAT ??1JoinRankManager@mu2@@UEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1JoinRankManager@mu2@@UEAA@XZ PROC			; mu2::JoinRankManager::~JoinRankManager, COMDAT

; 14   : {

$LN114:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7JoinRankManager@mu2@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx

; 15   : }

  00018	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 83 c0 20	 add	 rax, 32			; 00000020H
  00021	48 8b c8	 mov	 rcx, rax
  00024	e8 00 00 00 00	 call	 ??1JoinRankTimeCalculator@mu2@@QEAA@XZ ; mu2::JoinRankTimeCalculator::~JoinRankTimeCalculator
  00029	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0002e	48 83 c0 10	 add	 rax, 16
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 1061 :         _Tidy();

  00032	48 8b c8	 mov	 rcx, rax
  00035	e8 00 00 00 00	 call	 ?_Tidy@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@AEAAXXZ ; std::list<mu2::JoinRankManager::RankInfo,std::allocator<mu2::JoinRankManager::RankInfo> >::_Tidy
  0003a	90		 npad	 1
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 80   : 	virtual~ISingleton() {}

  0003b	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00040	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$ISingleton@VJoinRankManager@mu2@@@mu2@@6B@
  00047	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp

; 15   : }

  0004a	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0004e	c3		 ret	 0
??1JoinRankManager@mu2@@UEAA@XZ ENDP			; mu2::JoinRankManager::~JoinRankManager
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
;	COMDAT ?_Alloc_sentinel_and_proxy@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@AEAAXXZ
_TEXT	SEGMENT
$T1 = 32
$S139$ = 33
_Newhead$ = 40
$T2 = 48
$T3 = 56
_Al$ = 64
_Obj$ = 72
$T4 = 80
$T5 = 88
$T6 = 96
$T7 = 104
_Obj$ = 112
$T8 = 120
$T9 = 128
$T10 = 136
$T11 = 144
_Alproxy$ = 152
this$ = 176
?_Alloc_sentinel_and_proxy@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@AEAAXXZ PROC ; std::list<mu2::JoinRankManager::RankInfo,std::allocator<mu2::JoinRankManager::RankInfo> >::_Alloc_sentinel_and_proxy, COMDAT

; 1847 :     void _Alloc_sentinel_and_proxy() {

$LN75:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi
  00006	48 81 ec a0 00
	00 00		 sub	 rsp, 160		; 000000a0H

; 1848 :         auto&& _Alproxy = _GET_PROXY_ALLOCATOR(_Alnode, _Getal());

  0000d	48 8d 44 24 20	 lea	 rax, QWORD PTR $T1[rsp]
  00012	48 8b f8	 mov	 rdi, rax
  00015	33 c0		 xor	 eax, eax
  00017	b9 01 00 00 00	 mov	 ecx, 1
  0001c	f3 aa		 rep stosb
  0001e	48 8d 44 24 21	 lea	 rax, QWORD PTR $S139$[rsp]
  00023	48 89 84 24 98
	00 00 00	 mov	 QWORD PTR _Alproxy$[rsp], rax

; 1863 :         return _Mypair._Get_first();

  0002b	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00033	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 1863 :         return _Mypair._Get_first();

  00038	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
  0003d	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax

; 1849 :         _Container_proxy_ptr<_Alty> _Proxy(_Alproxy, _Mypair._Myval2);
; 1850 :         auto& _Al     = _Getal();

  00042	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
  00047	48 89 44 24 40	 mov	 QWORD PTR _Al$[rsp], rax

; 1851 :         auto _Newhead = _Al.allocate(1);

  0004c	ba 01 00 00 00	 mov	 edx, 1
  00051	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Al$[rsp]
  00056	e8 00 00 00 00	 call	 ?allocate@?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@QEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@2@_K@Z ; std::allocator<std::_List_node<mu2::JoinRankManager::RankInfo,void *> >::allocate
  0005b	48 89 44 24 28	 mov	 QWORD PTR _Newhead$[rsp], rax

; 1852 :         _Construct_in_place(_Newhead->_Next, _Newhead);

  00060	48 8b 44 24 28	 mov	 rax, QWORD PTR _Newhead$[rsp]
  00065	48 89 44 24 48	 mov	 QWORD PTR _Obj$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  0006a	48 8b 44 24 48	 mov	 rax, QWORD PTR _Obj$[rsp]
  0006f	48 89 44 24 50	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00074	48 8b 44 24 50	 mov	 rax, QWORD PTR $T4[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  00079	48 89 44 24 58	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  0007e	48 8b 44 24 58	 mov	 rax, QWORD PTR $T5[rsp]
  00083	48 89 44 24 60	 mov	 QWORD PTR $T6[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  00088	48 8d 44 24 28	 lea	 rax, QWORD PTR _Newhead$[rsp]
  0008d	48 89 44 24 68	 mov	 QWORD PTR $T7[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  00092	48 8b 44 24 60	 mov	 rax, QWORD PTR $T6[rsp]
  00097	48 8b 4c 24 68	 mov	 rcx, QWORD PTR $T7[rsp]
  0009c	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  0009f	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 1853 :         _Construct_in_place(_Newhead->_Prev, _Newhead);

  000a2	48 8b 44 24 28	 mov	 rax, QWORD PTR _Newhead$[rsp]
  000a7	48 83 c0 08	 add	 rax, 8
  000ab	48 89 44 24 70	 mov	 QWORD PTR _Obj$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  000b0	48 8b 44 24 70	 mov	 rax, QWORD PTR _Obj$[rsp]
  000b5	48 89 44 24 78	 mov	 QWORD PTR $T8[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  000ba	48 8b 44 24 78	 mov	 rax, QWORD PTR $T8[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_new.h

; 166  :         return _Where;

  000bf	48 89 84 24 80
	00 00 00	 mov	 QWORD PTR $T9[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  000c7	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR $T9[rsp]
  000cf	48 89 84 24 88
	00 00 00	 mov	 QWORD PTR $T10[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1502 :     return static_cast<_Ty&&>(_Arg);

  000d7	48 8d 44 24 28	 lea	 rax, QWORD PTR _Newhead$[rsp]
  000dc	48 89 84 24 90
	00 00 00	 mov	 QWORD PTR $T11[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xutility

; 476  :         ::new (static_cast<void*>(_STD addressof(_Obj))) _Ty(_STD forward<_Types>(_Args)...);

  000e4	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR $T10[rsp]
  000ec	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR $T11[rsp]
  000f4	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  000f7	48 89 08	 mov	 QWORD PTR [rax], rcx
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 1854 :         _Mypair._Myval2._Myhead = _Newhead;

  000fa	48 8b 84 24 b0
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00102	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Newhead$[rsp]
  00107	48 89 08	 mov	 QWORD PTR [rax], rcx

; 1855 :         _Proxy._Release();
; 1856 :     }

  0010a	48 81 c4 a0 00
	00 00		 add	 rsp, 160		; 000000a0H
  00111	5f		 pop	 rdi
  00112	c3		 ret	 0
?_Alloc_sentinel_and_proxy@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@AEAAXXZ ENDP ; std::list<mu2::JoinRankManager::RankInfo,std::allocator<mu2::JoinRankManager::RankInfo> >::_Alloc_sentinel_and_proxy
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
;	COMDAT ?_Tidy@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@AEAAXXZ
_TEXT	SEGMENT
_Bytes$ = 32
_My_data$ = 40
_Ptr$ = 48
$T1 = 56
$T2 = 64
_Al$ = 72
_Ptr$ = 80
this$ = 112
?_Tidy@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@AEAAXXZ PROC ; std::list<mu2::JoinRankManager::RankInfo,std::allocator<mu2::JoinRankManager::RankInfo> >::_Tidy, COMDAT

; 1514 :     void _Tidy() noexcept {

$LN99:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 68	 sub	 rsp, 104		; 00000068H

; 1863 :         return _Mypair._Get_first();

  00009	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  0000e	48 89 44 24 38	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 1863 :         return _Mypair._Get_first();

  00013	48 8b 44 24 38	 mov	 rax, QWORD PTR $T1[rsp]
  00018	48 89 44 24 40	 mov	 QWORD PTR $T2[rsp], rax

; 1515 :         auto& _Al      = _Getal();

  0001d	48 8b 44 24 40	 mov	 rax, QWORD PTR $T2[rsp]
  00022	48 89 44 24 48	 mov	 QWORD PTR _Al$[rsp], rax

; 1516 :         auto& _My_data = _Mypair._Myval2;

  00027	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0002c	48 89 44 24 28	 mov	 QWORD PTR _My_data$[rsp], rax

; 1517 :         _My_data._Orphan_all();
; 1518 :         _Node::_Free_non_head(_Al, _My_data._Myhead);

  00031	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00036	48 8b 10	 mov	 rdx, QWORD PTR [rax]
  00039	48 8b 4c 24 48	 mov	 rcx, QWORD PTR _Al$[rsp]
  0003e	e8 00 00 00 00	 call	 ??$_Free_non_head@V?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@@?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@SAXAEAV?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@1@PEAU01@@Z ; std::_List_node<mu2::JoinRankManager::RankInfo,void *>::_Free_non_head<std::allocator<std::_List_node<mu2::JoinRankManager::RankInfo,void *> > >

; 1519 :         _Node::_Freenode0(_Al, _My_data._Myhead);

  00043	48 8b 44 24 28	 mov	 rax, QWORD PTR _My_data$[rsp]
  00048	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0004b	48 89 44 24 50	 mov	 QWORD PTR _Ptr$[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 723  :             _STD _Deallocate<_New_alignof<value_type>>(_Ptr, sizeof(value_type) * _Count);

  00050	b8 01 00 00 00	 mov	 eax, 1
  00055	48 6b c0 18	 imul	 rax, rax, 24
  00059	48 89 44 24 20	 mov	 QWORD PTR _Bytes$[rsp], rax
  0005e	48 8b 44 24 50	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00063	48 89 44 24 30	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  00068	48 81 7c 24 20
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  00071	72 10		 jb	 SHORT $LN90@Tidy

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  00073	48 8d 54 24 20	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  00078	48 8d 4c 24 30	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  0007d	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  00082	90		 npad	 1
$LN90@Tidy:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  00083	48 8b 54 24 20	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  00088	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  0008d	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00092	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 1520 :     }

  00093	48 83 c4 68	 add	 rsp, 104		; 00000068H
  00097	c3		 ret	 0
?_Tidy@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@AEAAXXZ ENDP ; std::list<mu2::JoinRankManager::RankInfo,std::allocator<mu2::JoinRankManager::RankInfo> >::_Tidy
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
;	COMDAT ?_Unchecked_erase@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@AEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@2@QEAU32@@Z
_TEXT	SEGMENT
_Bytes$ = 32
_Result$ = 40
_Ptr$ = 48
$T1 = 56
$T2 = 64
$T3 = 72
this$ = 96
_Pnode$ = 104
?_Unchecked_erase@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@AEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@2@QEAU32@@Z PROC ; std::list<mu2::JoinRankManager::RankInfo,std::allocator<mu2::JoinRankManager::RankInfo> >::_Unchecked_erase, COMDAT

; 1437 :     _Nodeptr _Unchecked_erase(const _Nodeptr _Pnode) noexcept { // erase element at _Pnode

$LN61:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 1438 :         const auto _Result = _Pnode->_Next;

  0000e	48 8b 44 24 68	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00013	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00016	48 89 44 24 28	 mov	 QWORD PTR _Result$[rsp], rax

; 1439 :         _Mypair._Myval2._Orphan_ptr2(_Pnode);
; 1440 :         --_Mypair._Myval2._Mysize;

  0001b	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
  00020	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00024	48 ff c8	 dec	 rax
  00027	48 8b 4c 24 60	 mov	 rcx, QWORD PTR this$[rsp]
  0002c	48 89 41 08	 mov	 QWORD PTR [rcx+8], rax

; 1441 :         _Pnode->_Prev->_Next = _Result;

  00030	48 8b 44 24 68	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00035	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  00039	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Result$[rsp]
  0003e	48 89 08	 mov	 QWORD PTR [rax], rcx

; 1442 :         _Result->_Prev       = _Pnode->_Prev;

  00041	48 8b 44 24 28	 mov	 rax, QWORD PTR _Result$[rsp]
  00046	48 8b 4c 24 68	 mov	 rcx, QWORD PTR _Pnode$[rsp]
  0004b	48 8b 49 08	 mov	 rcx, QWORD PTR [rcx+8]
  0004f	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 1863 :         return _Mypair._Get_first();

  00053	48 8b 44 24 60	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  00058	48 89 44 24 38	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 1863 :         return _Mypair._Get_first();

  0005d	48 8b 44 24 38	 mov	 rax, QWORD PTR $T1[rsp]
  00062	48 89 44 24 40	 mov	 QWORD PTR $T2[rsp], rax

; 317  :         allocator_traits<_Alnode>::destroy(_Al, _STD addressof(_Ptr->_Myval));

  00067	48 8b 44 24 68	 mov	 rax, QWORD PTR _Pnode$[rsp]
  0006c	48 83 c0 10	 add	 rax, 16
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00070	48 89 44 24 48	 mov	 QWORD PTR $T3[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 723  :             _STD _Deallocate<_New_alignof<value_type>>(_Ptr, sizeof(value_type) * _Count);

  00075	b8 01 00 00 00	 mov	 eax, 1
  0007a	48 6b c0 18	 imul	 rax, rax, 24
  0007e	48 89 44 24 20	 mov	 QWORD PTR _Bytes$[rsp], rax
  00083	48 8b 44 24 68	 mov	 rax, QWORD PTR _Pnode$[rsp]
  00088	48 89 44 24 30	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  0008d	48 81 7c 24 20
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  00096	72 10		 jb	 SHORT $LN52@Unchecked_

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  00098	48 8d 54 24 20	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  0009d	48 8d 4c 24 30	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  000a2	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  000a7	90		 npad	 1
$LN52@Unchecked_:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  000a8	48 8b 54 24 20	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  000ad	48 8b 4c 24 30	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  000b2	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  000b7	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 1444 :         return _Result;

  000b8	48 8b 44 24 28	 mov	 rax, QWORD PTR _Result$[rsp]

; 1445 :     }

  000bd	48 83 c4 58	 add	 rsp, 88			; 00000058H
  000c1	c3		 ret	 0
?_Unchecked_erase@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@AEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@2@QEAU32@@Z ENDP ; std::list<mu2::JoinRankManager::RankInfo,std::allocator<mu2::JoinRankManager::RankInfo> >::_Unchecked_erase
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
;	COMDAT ?erase@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@QEAA?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@URankInfo@JoinRankManager@mu2@@@std@@@std@@@2@V?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@URankInfo@JoinRankManager@mu2@@@std@@@std@@@2@@Z
_TEXT	SEGMENT
_Bytes$ = 32
this$ = 40
_Ptr$ = 48
_Ptr$ = 56
$T1 = 64
$T2 = 72
_Result$ = 80
$T3 = 88
$T4 = 96
$T5 = 104
this$ = 128
__$ReturnUdt$ = 136
_Where$ = 144
?erase@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@QEAA?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@URankInfo@JoinRankManager@mu2@@@std@@@std@@@2@V?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@URankInfo@JoinRankManager@mu2@@@std@@@std@@@2@@Z PROC ; std::list<mu2::JoinRankManager::RankInfo,std::allocator<mu2::JoinRankManager::RankInfo> >::erase, COMDAT

; 1427 :     iterator erase(const const_iterator _Where) noexcept /* strengthened */ {

$LN96:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 78	 sub	 rsp, 120		; 00000078H

; 1428 : #if _ITERATOR_DEBUG_LEVEL == 2
; 1429 :         _STL_VERIFY(_Where._Getcont() == _STD addressof(_Mypair._Myval2), "list erase iterator outside range");
; 1430 : #endif // _ITERATOR_DEBUG_LEVEL == 2
; 1431 :         const auto _Result = _Where._Ptr->_Next;

  00013	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR _Where$[rsp]
  0001b	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0001e	48 89 44 24 50	 mov	 QWORD PTR _Result$[rsp], rax

; 1432 :         _Node::_Freenode(_Getal(), _Mypair._Myval2._Unlinknode(_Where._Ptr));

  00023	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0002b	48 89 44 24 28	 mov	 QWORD PTR this$[rsp], rax

; 405  :         _Pnode->_Prev->_Next = _Pnode->_Next;

  00030	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR _Where$[rsp]
  00038	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0003c	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR _Where$[rsp]
  00044	48 8b 09	 mov	 rcx, QWORD PTR [rcx]
  00047	48 89 08	 mov	 QWORD PTR [rax], rcx

; 406  :         _Pnode->_Next->_Prev = _Pnode->_Prev;

  0004a	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR _Where$[rsp]
  00052	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00055	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR _Where$[rsp]
  0005d	48 8b 49 08	 mov	 rcx, QWORD PTR [rcx+8]
  00061	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 407  :         --_Mysize;

  00065	48 8b 44 24 28	 mov	 rax, QWORD PTR this$[rsp]
  0006a	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0006e	48 ff c8	 dec	 rax
  00071	48 8b 4c 24 28	 mov	 rcx, QWORD PTR this$[rsp]
  00076	48 89 41 08	 mov	 QWORD PTR [rcx+8], rax

; 408  :         return _Pnode;

  0007a	48 8b 84 24 90
	00 00 00	 mov	 rax, QWORD PTR _Where$[rsp]
  00082	48 89 44 24 48	 mov	 QWORD PTR $T2[rsp], rax

; 1863 :         return _Mypair._Get_first();

  00087	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 1539 :         return *this;

  0008f	48 89 44 24 40	 mov	 QWORD PTR $T1[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 1863 :         return _Mypair._Get_first();

  00094	48 8b 44 24 40	 mov	 rax, QWORD PTR $T1[rsp]
  00099	48 89 44 24 58	 mov	 QWORD PTR $T3[rsp], rax

; 1432 :         _Node::_Freenode(_Getal(), _Mypair._Myval2._Unlinknode(_Where._Ptr));

  0009e	48 8b 44 24 48	 mov	 rax, QWORD PTR $T2[rsp]
  000a3	48 89 44 24 30	 mov	 QWORD PTR _Ptr$[rsp], rax

; 317  :         allocator_traits<_Alnode>::destroy(_Al, _STD addressof(_Ptr->_Myval));

  000a8	48 8b 44 24 30	 mov	 rax, QWORD PTR _Ptr$[rsp]
  000ad	48 83 c0 10	 add	 rax, 16
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  000b1	48 89 44 24 60	 mov	 QWORD PTR $T4[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory

; 723  :             _STD _Deallocate<_New_alignof<value_type>>(_Ptr, sizeof(value_type) * _Count);

  000b6	b8 01 00 00 00	 mov	 eax, 1
  000bb	48 6b c0 18	 imul	 rax, rax, 24
  000bf	48 89 44 24 20	 mov	 QWORD PTR _Bytes$[rsp], rax
  000c4	48 8b 44 24 30	 mov	 rax, QWORD PTR _Ptr$[rsp]
  000c9	48 89 44 24 38	 mov	 QWORD PTR _Ptr$[rsp], rax

; 284  :         if (_Bytes >= _Big_allocation_threshold) {

  000ce	48 81 7c 24 20
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  000d7	72 10		 jb	 SHORT $LN57@erase

; 285  :             // boost the alignment of big allocations to help autovectorization
; 286  :             _Adjust_manually_vector_aligned(_Ptr, _Bytes);

  000d9	48 8d 54 24 20	 lea	 rdx, QWORD PTR _Bytes$[rsp]
  000de	48 8d 4c 24 38	 lea	 rcx, QWORD PTR _Ptr$[rsp]
  000e3	e8 00 00 00 00	 call	 ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ; std::_Adjust_manually_vector_aligned
  000e8	90		 npad	 1
$LN57@erase:

; 287  :         }
; 288  : #endif // defined(_M_IX86) || defined(_M_X64)
; 289  :         ::operator delete(_Ptr, _Bytes);

  000e9	48 8b 54 24 20	 mov	 rdx, QWORD PTR _Bytes$[rsp]
  000ee	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr$[rsp]
  000f3	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  000f8	90		 npad	 1
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 1137 :         return iterator(_Where, _STD addressof(_Mypair._Myval2));

  000f9	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR this$[rsp]
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\type_traits

; 1525 :     return __builtin_addressof(_Val);

  00101	48 89 44 24 68	 mov	 QWORD PTR $T5[rsp], rax
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list

; 37   :     _List_unchecked_const_iterator(_Nodeptr _Pnode, const _Mylist* _Plist) noexcept : _Ptr(_Pnode) {

  00106	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]
  0010e	48 8b 4c 24 50	 mov	 rcx, QWORD PTR _Result$[rsp]
  00113	48 89 08	 mov	 QWORD PTR [rax], rcx

; 1433 :         return _Make_iter(_Result);

  00116	48 8b 84 24 88
	00 00 00	 mov	 rax, QWORD PTR __$ReturnUdt$[rsp]

; 1434 :     }

  0011e	48 83 c4 78	 add	 rsp, 120		; 00000078H
  00122	c3		 ret	 0
?erase@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@QEAA?AV?$_List_iterator@V?$_List_val@U?$_List_simple_types@URankInfo@JoinRankManager@mu2@@@std@@@std@@@2@V?$_List_const_iterator@V?$_List_val@U?$_List_simple_types@URankInfo@JoinRankManager@mu2@@@std@@@std@@@2@@Z ENDP ; std::list<mu2::JoinRankManager::RankInfo,std::allocator<mu2::JoinRankManager::RankInfo> >::erase
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\list
;	COMDAT ??1?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@QEAA@XZ PROC ; std::list<mu2::JoinRankManager::RankInfo,std::allocator<mu2::JoinRankManager::RankInfo> >::~list<mu2::JoinRankManager::RankInfo,std::allocator<mu2::JoinRankManager::RankInfo> >, COMDAT

; 1060 :     ~list() noexcept {

$LN104:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 1061 :         _Tidy();

  00009	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0000e	e8 00 00 00 00	 call	 ?_Tidy@?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@AEAAXXZ ; std::list<mu2::JoinRankManager::RankInfo,std::allocator<mu2::JoinRankManager::RankInfo> >::_Tidy
  00013	90		 npad	 1

; 1062 : #if _ITERATOR_DEBUG_LEVEL != 0 // TRANSITION, ABI
; 1063 :         auto&& _Alproxy = _GET_PROXY_ALLOCATOR(_Alnode, _Getal());
; 1064 :         _Delete_plain_internal(_Alproxy, _Mypair._Myval2._Myproxy);
; 1065 : #endif // _ITERATOR_DEBUG_LEVEL != 0
; 1066 :     }

  00014	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00018	c3		 ret	 0
??1?$list@URankInfo@JoinRankManager@mu2@@V?$allocator@URankInfo@JoinRankManager@mu2@@@std@@@std@@QEAA@XZ ENDP ; std::list<mu2::JoinRankManager::RankInfo,std::allocator<mu2::JoinRankManager::RankInfo> >::~list<mu2::JoinRankManager::RankInfo,std::allocator<mu2::JoinRankManager::RankInfo> >
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?allocate@?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@QEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@2@_K@Z
_TEXT	SEGMENT
_Overflow_is_possible$1 = 32
_Bytes$ = 40
$T2 = 48
$T3 = 56
$T4 = 64
_Max_possible$5 = 72
this$ = 96
_Count$ = 104
?allocate@?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@QEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@2@_K@Z PROC ; std::allocator<std::_List_node<mu2::JoinRankManager::RankInfo,void *> >::allocate, COMDAT

; 988  :     _NODISCARD_RAW_PTR_ALLOC _CONSTEXPR20 __declspec(allocator) _Ty* allocate(_CRT_GUARDOVERFLOW const size_t _Count) {

$LN13:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 113  :     constexpr bool _Overflow_is_possible = _Ty_size > 1;

  0000e	c6 44 24 20 01	 mov	 BYTE PTR _Overflow_is_possible$1[rsp], 1

; 114  : 
; 115  :     if constexpr (_Overflow_is_possible) {
; 116  :         constexpr size_t _Max_possible = static_cast<size_t>(-1) / _Ty_size;

  00013	48 b8 aa aa aa
	aa aa aa aa 0a	 mov	 rax, 768614336404564650	; 0aaaaaaaaaaaaaaaH
  0001d	48 89 44 24 48	 mov	 QWORD PTR _Max_possible$5[rsp], rax

; 117  :         if (_Count > _Max_possible) {

  00022	48 b8 aa aa aa
	aa aa aa aa 0a	 mov	 rax, 768614336404564650	; 0aaaaaaaaaaaaaaaH
  0002c	48 39 44 24 68	 cmp	 QWORD PTR _Count$[rsp], rax
  00031	76 06		 jbe	 SHORT $LN4@allocate

; 118  :             _Throw_bad_array_new_length(); // multiply overflow

  00033	e8 00 00 00 00	 call	 ?_Throw_bad_array_new_length@std@@YAXXZ ; std::_Throw_bad_array_new_length
  00038	90		 npad	 1
$LN4@allocate:

; 119  :         }
; 120  :     }
; 121  : 
; 122  :     return _Count * _Ty_size;

  00039	48 6b 44 24 68
	18		 imul	 rax, QWORD PTR _Count$[rsp], 24
  0003f	48 89 44 24 38	 mov	 QWORD PTR $T3[rsp], rax

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00044	48 8b 44 24 38	 mov	 rax, QWORD PTR $T3[rsp]
  00049	48 89 44 24 28	 mov	 QWORD PTR _Bytes$[rsp], rax

; 227  :     if (_Bytes == 0) {

  0004e	48 83 7c 24 28
	00		 cmp	 QWORD PTR _Bytes$[rsp], 0
  00054	75 0b		 jne	 SHORT $LN8@allocate

; 228  :         return nullptr;

  00056	48 c7 44 24 30
	00 00 00 00	 mov	 QWORD PTR $T2[rsp], 0
  0005f	eb 35		 jmp	 SHORT $LN7@allocate
$LN8@allocate:

; 229  :     }
; 230  : 
; 231  : #if _HAS_CXX20 // TRANSITION, GH-1532
; 232  :     if (_STD is_constant_evaluated()) {
; 233  :         return _Traits::_Allocate(_Bytes);
; 234  :     }
; 235  : #endif // _HAS_CXX20
; 236  : 
; 237  : #ifdef __cpp_aligned_new
; 238  :     if constexpr (_Align > __STDCPP_DEFAULT_NEW_ALIGNMENT__) {
; 239  :         size_t _Passed_align = _Align;
; 240  : #if defined(_M_IX86) || defined(_M_X64)
; 241  :         if (_Bytes >= _Big_allocation_threshold) {
; 242  :             // boost the alignment of big allocations to help autovectorization
; 243  :             _Passed_align = (_STD max)(_Align, _Big_allocation_alignment);
; 244  :         }
; 245  : #endif // defined(_M_IX86) || defined(_M_X64)
; 246  :         return _Traits::_Allocate_aligned(_Bytes, _Passed_align);
; 247  :     } else
; 248  : #endif // defined(__cpp_aligned_new)
; 249  :     {
; 250  : #if defined(_M_IX86) || defined(_M_X64)
; 251  :         if (_Bytes >= _Big_allocation_threshold) {

  00061	48 81 7c 24 28
	00 10 00 00	 cmp	 QWORD PTR _Bytes$[rsp], 4096 ; 00001000H
  0006a	72 11		 jb	 SHORT $LN9@allocate

; 252  :             // boost the alignment of big allocations to help autovectorization
; 253  :             return _Allocate_manually_vector_aligned<_Traits>(_Bytes);

  0006c	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00071	e8 00 00 00 00	 call	 ??$_Allocate_manually_vector_aligned@U_Default_allocate_traits@std@@@std@@YAPEAX_K@Z ; std::_Allocate_manually_vector_aligned<std::_Default_allocate_traits>
  00076	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
  0007b	eb 19		 jmp	 SHORT $LN7@allocate
$LN9@allocate:

; 136  :         return ::operator new(_Bytes);

  0007d	48 8b 4c 24 28	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  00082	e8 00 00 00 00	 call	 ??2@YAPEAX_K@Z		; operator new
  00087	48 89 44 24 40	 mov	 QWORD PTR $T4[rsp], rax

; 256  :         return _Traits::_Allocate(_Bytes);

  0008c	48 8b 44 24 40	 mov	 rax, QWORD PTR $T4[rsp]
  00091	48 89 44 24 30	 mov	 QWORD PTR $T2[rsp], rax
$LN7@allocate:

; 989  :         static_assert(sizeof(value_type) > 0, "value_type must be complete before calling allocate.");
; 990  :         return static_cast<_Ty*>(_STD _Allocate<_New_alignof<_Ty>>(_Get_size_of_n<sizeof(_Ty)>(_Count)));

  00096	48 8b 44 24 30	 mov	 rax, QWORD PTR $T2[rsp]
$LN6@allocate:

; 991  :     }

  0009b	48 83 c4 58	 add	 rsp, 88			; 00000058H
  0009f	c3		 ret	 0
?allocate@?$allocator@U?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@std@@@std@@QEAAPEAU?$_List_node@URankInfo@JoinRankManager@mu2@@PEAX@2@_K@Z ENDP ; std::allocator<std::_List_node<mu2::JoinRankManager::RankInfo,void *> >::allocate
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??_G?$ISingleton@VJoinRankManager@mu2@@@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_G?$ISingleton@VJoinRankManager@mu2@@@mu2@@UEAAPEAXI@Z PROC ; mu2::ISingleton<mu2::JoinRankManager>::`scalar deleting destructor', COMDAT
$LN10:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 80   : 	virtual~ISingleton() {}

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$ISingleton@VJoinRankManager@mu2@@@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx
  0001c	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00020	83 e0 01	 and	 eax, 1
  00023	85 c0		 test	 eax, eax
  00025	74 10		 je	 SHORT $LN2@scalar
  00027	ba 08 00 00 00	 mov	 edx, 8
  0002c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00031	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00036	90		 npad	 1
$LN2@scalar:
  00037	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003c	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00040	c3		 ret	 0
??_G?$ISingleton@VJoinRankManager@mu2@@@mu2@@UEAAPEAXI@Z ENDP ; mu2::ISingleton<mu2::JoinRankManager>::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??1?$ISingleton@VJoinRankManager@mu2@@@mu2@@UEAA@XZ
_TEXT	SEGMENT
this$ = 8
??1?$ISingleton@VJoinRankManager@mu2@@@mu2@@UEAA@XZ PROC ; mu2::ISingleton<mu2::JoinRankManager>::~ISingleton<mu2::JoinRankManager>, COMDAT

; 80   : 	virtual~ISingleton() {}

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0000a	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$ISingleton@VJoinRankManager@mu2@@@mu2@@6B@
  00011	48 89 08	 mov	 QWORD PTR [rax], rcx
  00014	c3		 ret	 0
??1?$ISingleton@VJoinRankManager@mu2@@@mu2@@UEAA@XZ ENDP ; mu2::ISingleton<mu2::JoinRankManager>::~ISingleton<mu2::JoinRankManager>
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z
_TEXT	SEGMENT
_Back_shift$ = 48
_Ptr_container$ = 56
_Ptr_user$ = 64
_Min_back_shift$ = 72
_Ptr$ = 96
_Bytes$ = 104
?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z PROC ; std::_Adjust_manually_vector_aligned, COMDAT

; 200  : inline void _Adjust_manually_vector_aligned(void*& _Ptr, size_t& _Bytes) {

$LN5:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 201  :     // adjust parameters from _Allocate_manually_vector_aligned to pass to operator delete
; 202  :     _Bytes += _Non_user_size;

  0000e	48 8b 44 24 68	 mov	 rax, QWORD PTR _Bytes$[rsp]
  00013	48 8b 00	 mov	 rax, QWORD PTR [rax]
  00016	48 83 c0 27	 add	 rax, 39			; 00000027H
  0001a	48 8b 4c 24 68	 mov	 rcx, QWORD PTR _Bytes$[rsp]
  0001f	48 89 01	 mov	 QWORD PTR [rcx], rax

; 203  : 
; 204  :     const uintptr_t* const _Ptr_user = static_cast<uintptr_t*>(_Ptr);

  00022	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00027	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0002a	48 89 44 24 40	 mov	 QWORD PTR _Ptr_user$[rsp], rax

; 205  :     const uintptr_t _Ptr_container   = _Ptr_user[-1];

  0002f	b8 08 00 00 00	 mov	 eax, 8
  00034	48 6b c0 ff	 imul	 rax, rax, -1
  00038	48 8b 4c 24 40	 mov	 rcx, QWORD PTR _Ptr_user$[rsp]
  0003d	48 8b 04 01	 mov	 rax, QWORD PTR [rcx+rax]
  00041	48 89 44 24 38	 mov	 QWORD PTR _Ptr_container$[rsp], rax

; 206  : 
; 207  :     // If the following asserts, it likely means that we are performing
; 208  :     // an aligned delete on memory coming from an unaligned allocation.
; 209  :     _STL_ASSERT(_Ptr_user[-2] == _Big_allocation_sentinel, "invalid argument");
; 210  : 
; 211  :     // Extra paranoia on aligned allocation/deallocation; ensure _Ptr_container is
; 212  :     // in range [_Min_back_shift, _Non_user_size]
; 213  : #ifdef _DEBUG
; 214  :     constexpr uintptr_t _Min_back_shift = 2 * sizeof(void*);
; 215  : #else // ^^^ defined(_DEBUG) / !defined(_DEBUG) vvv
; 216  :     constexpr uintptr_t _Min_back_shift = sizeof(void*);

  00046	48 c7 44 24 48
	08 00 00 00	 mov	 QWORD PTR _Min_back_shift$[rsp], 8

; 217  : #endif // ^^^ !defined(_DEBUG) ^^^
; 218  :     const uintptr_t _Back_shift = reinterpret_cast<uintptr_t>(_Ptr) - _Ptr_container;

  0004f	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00054	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr_container$[rsp]
  00059	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0005c	48 2b c1	 sub	 rax, rcx
  0005f	48 89 44 24 30	 mov	 QWORD PTR _Back_shift$[rsp], rax

; 219  :     _STL_VERIFY(_Back_shift >= _Min_back_shift && _Back_shift <= _Non_user_size, "invalid argument");

  00064	48 83 7c 24 30
	08		 cmp	 QWORD PTR _Back_shift$[rsp], 8
  0006a	72 08		 jb	 SHORT $LN3@Adjust_man
  0006c	48 83 7c 24 30
	27		 cmp	 QWORD PTR _Back_shift$[rsp], 39 ; 00000027H
  00072	76 19		 jbe	 SHORT $LN2@Adjust_man
$LN3@Adjust_man:
  00074	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR [rsp+32], 0
  0007d	45 33 c9	 xor	 r9d, r9d
  00080	45 33 c0	 xor	 r8d, r8d
  00083	33 d2		 xor	 edx, edx
  00085	33 c9		 xor	 ecx, ecx
  00087	e8 00 00 00 00	 call	 _invoke_watson
  0008c	90		 npad	 1
$LN2@Adjust_man:

; 220  :     _Ptr = reinterpret_cast<void*>(_Ptr_container);

  0008d	48 8b 44 24 60	 mov	 rax, QWORD PTR _Ptr$[rsp]
  00092	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Ptr_container$[rsp]
  00097	48 89 08	 mov	 QWORD PTR [rax], rcx
$LN4@Adjust_man:

; 221  : }

  0009a	48 83 c4 58	 add	 rsp, 88			; 00000058H
  0009e	c3		 ret	 0
?_Adjust_manually_vector_aligned@std@@YAXAEAPEAXAEA_K@Z ENDP ; std::_Adjust_manually_vector_aligned
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory
;	COMDAT ?_Throw_bad_array_new_length@std@@YAXXZ
_TEXT	SEGMENT
$T1 = 32
?_Throw_bad_array_new_length@std@@YAXXZ PROC		; std::_Throw_bad_array_new_length, COMDAT

; 107  : [[noreturn]] inline void _Throw_bad_array_new_length() {

$LN3:
  00000	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 108  :     _THROW(bad_array_new_length{});

  00004	48 8d 4c 24 20	 lea	 rcx, QWORD PTR $T1[rsp]
  00009	e8 00 00 00 00	 call	 ??0bad_array_new_length@std@@QEAA@XZ ; std::bad_array_new_length::bad_array_new_length
  0000e	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:_TI3?AVbad_array_new_length@std@@
  00015	48 8d 4c 24 20	 lea	 rcx, QWORD PTR $T1[rsp]
  0001a	e8 00 00 00 00	 call	 _CxxThrowException
  0001f	90		 npad	 1
$LN2@Throw_bad_:

; 109  : }

  00020	48 83 c4 48	 add	 rsp, 72			; 00000048H
  00024	c3		 ret	 0
?_Throw_bad_array_new_length@std@@YAXXZ ENDP		; std::_Throw_bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gbad_array_new_length@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gbad_array_new_length@std@@UEAAPEAXI@Z PROC		; std::bad_array_new_length::`scalar deleting destructor', COMDAT
$LN20:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gbad_array_new_length@std@@UEAAPEAXI@Z ENDP		; std::bad_array_new_length::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??0bad_array_new_length@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
__that$ = 56
??0bad_array_new_length@std@@QEAA@AEBV01@@Z PROC	; std::bad_array_new_length::bad_array_new_length, COMDAT
$LN14:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000e	48 8b 54 24 38	 mov	 rdx, QWORD PTR __that$[rsp]
  00013	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00018	e8 00 00 00 00	 call	 ??0bad_alloc@std@@QEAA@AEBV01@@Z
  0001d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00022	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_array_new_length@std@@6B@
  00029	48 89 08	 mov	 QWORD PTR [rax], rcx
  0002c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00031	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00035	c3		 ret	 0
??0bad_array_new_length@std@@QEAA@AEBV01@@Z ENDP	; std::bad_array_new_length::bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??1bad_array_new_length@std@@UEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1bad_array_new_length@std@@UEAA@XZ PROC		; std::bad_array_new_length::~bad_array_new_length, COMDAT
$LN14:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  00018	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 83 c0 08	 add	 rax, 8
  00021	48 8b c8	 mov	 rcx, rax
  00024	e8 00 00 00 00	 call	 __std_exception_destroy
  00029	90		 npad	 1
  0002a	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0002e	c3		 ret	 0
??1bad_array_new_length@std@@UEAA@XZ ENDP		; std::bad_array_new_length::~bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0bad_array_new_length@std@@QEAA@XZ
_TEXT	SEGMENT
this$ = 16
??0bad_array_new_length@std@@QEAA@XZ PROC		; std::bad_array_new_length::bad_array_new_length, COMDAT

; 144  :     {

$LN14:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	57		 push	 rdi

; 67   :     {

  00006	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0000b	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00012	48 89 08	 mov	 QWORD PTR [rax], rcx

; 66   :         : _Data()

  00015	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 83 c0 08	 add	 rax, 8
  0001e	48 8b f8	 mov	 rdi, rax
  00021	33 c0		 xor	 eax, eax
  00023	b9 10 00 00 00	 mov	 ecx, 16
  00028	f3 aa		 rep stosb

; 68   :         _Data._What = _Message;

  0002a	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0002f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_C@_0BF@KINCDENJ@bad?5array?5new?5length@
  00036	48 89 48 08	 mov	 QWORD PTR [rax+8], rcx

; 133  :     {

  0003a	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0003f	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_alloc@std@@6B@
  00046	48 89 08	 mov	 QWORD PTR [rax], rcx

; 144  :     {

  00049	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_array_new_length@std@@6B@
  00055	48 89 08	 mov	 QWORD PTR [rax], rcx

; 145  :     }

  00058	48 8b 44 24 10	 mov	 rax, QWORD PTR this$[rsp]
  0005d	5f		 pop	 rdi
  0005e	c3		 ret	 0
??0bad_array_new_length@std@@QEAA@XZ ENDP		; std::bad_array_new_length::bad_array_new_length
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gbad_alloc@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gbad_alloc@std@@UEAAPEAXI@Z PROC			; std::bad_alloc::`scalar deleting destructor', COMDAT
$LN15:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gbad_alloc@std@@UEAAPEAXI@Z ENDP			; std::bad_alloc::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0bad_alloc@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
__that$ = 56
??0bad_alloc@std@@QEAA@AEBV01@@Z PROC			; std::bad_alloc::bad_alloc, COMDAT
$LN9:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 20	 sub	 rsp, 32			; 00000020H

; 73   :     {

  0000f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  0001b	48 89 08	 mov	 QWORD PTR [rax], rcx

; 72   :         : _Data()

  0001e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 83 c0 08	 add	 rax, 8
  00027	48 8b f8	 mov	 rdi, rax
  0002a	33 c0		 xor	 eax, eax
  0002c	b9 10 00 00 00	 mov	 ecx, 16
  00031	f3 aa		 rep stosb

; 74   :         __std_exception_copy(&_Other._Data, &_Data);

  00033	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 83 c0 08	 add	 rax, 8
  0003c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR __that$[rsp]
  00041	48 83 c1 08	 add	 rcx, 8
  00045	48 8b d0	 mov	 rdx, rax
  00048	e8 00 00 00 00	 call	 __std_exception_copy
  0004d	90		 npad	 1
  0004e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7bad_alloc@std@@6B@
  0005a	48 89 08	 mov	 QWORD PTR [rax], rcx
  0005d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00062	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00066	5f		 pop	 rdi
  00067	c3		 ret	 0
??0bad_alloc@std@@QEAA@AEBV01@@Z ENDP			; std::bad_alloc::bad_alloc
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??_Gexception@std@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_Gexception@std@@UEAAPEAXI@Z PROC			; std::exception::`scalar deleting destructor', COMDAT
$LN10:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 90   :     {

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx

; 91   :         __std_exception_destroy(&_Data);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00021	48 83 c0 08	 add	 rax, 8
  00025	48 8b c8	 mov	 rcx, rax
  00028	e8 00 00 00 00	 call	 __std_exception_destroy
  0002d	90		 npad	 1
  0002e	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00032	83 e0 01	 and	 eax, 1
  00035	85 c0		 test	 eax, eax
  00037	74 10		 je	 SHORT $LN2@scalar
  00039	ba 18 00 00 00	 mov	 edx, 24
  0003e	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00043	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00048	90		 npad	 1
$LN2@scalar:
  00049	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0004e	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00052	c3		 ret	 0
??_Gexception@std@@UEAAPEAXI@Z ENDP			; std::exception::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ?what@exception@std@@UEBAPEBDXZ
_TEXT	SEGMENT
tv69 = 0
this$ = 32
?what@exception@std@@UEBAPEBDXZ PROC			; std::exception::what, COMDAT

; 95   :     {

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 18	 sub	 rsp, 24

; 96   :         return _Data._What ? _Data._What : "Unknown exception";

  00009	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00013	74 0f		 je	 SHORT $LN3@what
  00015	48 8b 44 24 20	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 8b 40 08	 mov	 rax, QWORD PTR [rax+8]
  0001e	48 89 04 24	 mov	 QWORD PTR tv69[rsp], rax
  00022	eb 0b		 jmp	 SHORT $LN4@what
$LN3@what:
  00024	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_0BC@EOODALEL@Unknown?5exception@
  0002b	48 89 04 24	 mov	 QWORD PTR tv69[rsp], rax
$LN4@what:
  0002f	48 8b 04 24	 mov	 rax, QWORD PTR tv69[rsp]

; 97   :     }

  00033	48 83 c4 18	 add	 rsp, 24
  00037	c3		 ret	 0
?what@exception@std@@UEBAPEBDXZ ENDP			; std::exception::what
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_exception.h
;	COMDAT ??0exception@std@@QEAA@AEBV01@@Z
_TEXT	SEGMENT
this$ = 48
_Other$ = 56
??0exception@std@@QEAA@AEBV01@@Z PROC			; std::exception::exception, COMDAT

; 73   :     {

$LN4:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 83 ec 20	 sub	 rsp, 32			; 00000020H
  0000f	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00014	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7exception@std@@6B@
  0001b	48 89 08	 mov	 QWORD PTR [rax], rcx

; 72   :         : _Data()

  0001e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00023	48 83 c0 08	 add	 rax, 8
  00027	48 8b f8	 mov	 rdi, rax
  0002a	33 c0		 xor	 eax, eax
  0002c	b9 10 00 00 00	 mov	 ecx, 16
  00031	f3 aa		 rep stosb

; 74   :         __std_exception_copy(&_Other._Data, &_Data);

  00033	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00038	48 83 c0 08	 add	 rax, 8
  0003c	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _Other$[rsp]
  00041	48 83 c1 08	 add	 rcx, 8
  00045	48 8b d0	 mov	 rdx, rax
  00048	e8 00 00 00 00	 call	 __std_exception_copy
  0004d	90		 npad	 1

; 75   :     }

  0004e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00053	48 83 c4 20	 add	 rsp, 32			; 00000020H
  00057	5f		 pop	 rdi
  00058	c3		 ret	 0
??0exception@std@@QEAA@AEBV01@@Z ENDP			; std::exception::exception
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\JoinRankManager.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
