﻿#include "stdafx.h"
#include <Backend/Zone/ZoneServer.h>
#include <Backend/Zone/World/MissionMap/AirshipDefenceProcessor.h>
#include <Backend/Zone/Action/ActionNpc.h>
#include <Backend/Zone/Action/ActionNpcAggro.h>
#include <Backend/Zone/Action/ActionPortal.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerSeason.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerRevive.h>
#ifdef __Patch_ImprintedMemory_by_raylee_181128
#include <Backend/Zone/Action/ActionAchievement.h>
#endif
#include <Backend/Zone/Action/ActionDailyMission.h>
#include <Backend/Zone/Action/ActionNpcSkill.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerEventBuff.h>
#include <Backend/Zone/System/SpawnSystem.h>
#include <Backend/Zone/System/DropSystem.h>
#include <Backend/Zone/System/ReviveSystem.h>

namespace mu2
{
	AirshipDefenceProcessor::AirshipDefenceProcessor(EntitySectorController* entity)
		: SectorProcessor(entity, SectorProcessorType::AIRSHIP_DEFENCE)
		, m_sector(nullptr)
		, m_dispatcher(this)
		, m_eventFuncs()
		, m_isNewSpawnedWave(false)
	{
		m_eventFuncs[DungeonEventType::CHECK_IN] = &AirshipDefenceProcessor::onCheckInUser;
		m_eventFuncs[DungeonEventType::LOAD_SYNC_COMPLETED] = &AirshipDefenceProcessor::onLoadSyncCompleted;
		m_eventFuncs[DungeonEventType::CHANGE_MAP] = &AirshipDefenceProcessor::onGiveRewardAndExit;
		m_eventFuncs[DungeonEventType::REWARD_RECEIVE] = &AirshipDefenceProcessor::onGiveRewardAndExit;
		m_eventFuncs[DungeonEventType::CHECK_OUT] = &AirshipDefenceProcessor::onGiveRewardAndExit;

		SetHandlerLoopback(NULL, EReqAirshipDefenceGameGiveUp, AirshipDefenceProcessor::onReqAirshipDefenceGameGiveUp, false);
	}


	AirshipDefenceProcessor::~AirshipDefenceProcessor()
	{
	}

	Bool AirshipDefenceProcessor::isNewSpawnedWave()
	{
		return m_isNewSpawnedWave;
	}

	void AirshipDefenceProcessor::resetNewSpawnedWave()
	{
		m_isNewSpawnedWave = false;
	}


	void AirshipDefenceProcessor::registerLua()
	{
		MU2_ASSERT(m_L);

		using namespace lua_util;

		class_add<AirshipDefenceProcessor>(m_L, "AirshipDefence");
		class_def<AirshipDefenceProcessor>(m_L, "is_new_spawned_wave", &AirshipDefenceProcessor::isNewSpawnedWave);
		class_def<AirshipDefenceProcessor>(m_L, "reset_new_spawned_wave", &AirshipDefenceProcessor::resetNewSpawnedWave);
		
		lua_util::set(m_L, "airshipdefence", this);
	}



	ErrorJoin::Error AirshipDefenceProcessor::SetupDungeonInfo()
	{
		// 변수 초기화
		m_completedWaves.clear();
		m_rewardWaveNo = 0;
		m_rewardStageNo = 0;

		m_states[EState::STATE_JOIN][EStateFunc::FUNC_ENTER] = std::bind(&AirshipDefenceProcessor::enterJoinState, this);
		m_states[EState::STATE_JOIN][EStateFunc::FUNC_UPDATE] = std::bind(&AirshipDefenceProcessor::processJoinState, this);
		m_states[EState::STATE_JOIN][EStateFunc::FUNC_EXIT] = std::bind(&AirshipDefenceProcessor::exitJoinState, this);

		m_states[EState::STATE_READY][EStateFunc::FUNC_ENTER] = std::bind(&AirshipDefenceProcessor::enterReadyState, this);
		m_states[EState::STATE_READY][EStateFunc::FUNC_UPDATE] = std::bind(&AirshipDefenceProcessor::processReadyState, this);
		m_states[EState::STATE_READY][EStateFunc::FUNC_EXIT] = std::bind(&AirshipDefenceProcessor::exitReadyState, this);

		m_states[EState::STATE_PLAY][EStateFunc::FUNC_ENTER] = std::bind(&AirshipDefenceProcessor::enterPlayState, this);
		m_states[EState::STATE_PLAY][EStateFunc::FUNC_UPDATE] = std::bind(&AirshipDefenceProcessor::processPlayState, this);
		m_states[EState::STATE_PLAY][EStateFunc::FUNC_EXIT] = std::bind(&AirshipDefenceProcessor::exitPlayState, this);

		m_states[EState::STATE_REWARD][EStateFunc::FUNC_ENTER] = std::bind(&AirshipDefenceProcessor::enterRewardState, this);
		m_states[EState::STATE_REWARD][EStateFunc::FUNC_UPDATE] = std::bind(&AirshipDefenceProcessor::updateRewardState, this);
		m_states[EState::STATE_REWARD][EStateFunc::FUNC_EXIT] = std::bind(&AirshipDefenceProcessor::exitRewardState, this);

		ActionSectorController* act = GetEntityAction(GetParent());
		VERIFY_RETURN(act, ErrorJoin::E_FAILED);

		m_sector = act->GetSector();
		VERIFY_RETURN(m_sector, ErrorJoin::GetSectorFailed);

		m_L = act->GetLuaVM();
		MU2_ASSERT(m_L);

		registerLua();

		// 상태 세팅
		m_eState = STATE_NONE;
		changeNextState();

		return ErrorJoin::SUCCESS;
	}

	void AirshipDefenceProcessor::DestroyDungeon()
	{
		// 마지막 상태 종료 처리
		m_states[m_eState][FUNC_EXIT]();
	}


	void AirshipDefenceProcessor::ProcessEvent(DungeonEventType::Enum eventType, EntityUnit* owner, EntityUnit* target, UInt32 v1, UInt32 v2)
	{
		VERIFY_RETURN(eventType < m_eventFuncs.size(), );
		VERIFY_RETURN(GetParent() && GetParent()->IsValid(), );
		VALID_DO(owner, AllowNull);

		auto func = m_eventFuncs[eventType];
		VALID_RETURN(func, );

		(this->*(func))(owner, target, v1, v2);
	}

	void	AirshipDefenceProcessor::Update()
	{
		if (m_eState < EState::STATE_MAX)
		{
			m_states[m_eState][FUNC_UPDATE]();
		}
	}

	void AirshipDefenceProcessor::OnProcessEvent(EventPtr& e)
	{
		if (!m_dispatcher.Dispatch(e))
		{
			MU2_WARN_LOG(LogCategory::CONTENTS, "AirshipDefenceProcessor::OnProcessEvent > unhanlded event(%s)", e->GetEventname());
		}
	}
	void AirshipDefenceProcessor::onReqAirshipDefenceGameGiveUp(EventPtr& e)
	{
		auto SendError = [&e](ErrorGame::Error error)
		{
			EResAirshipDefenceGameGiveUp* res = new EResAirshipDefenceGameGiveUp;
			res->error = error;
			SERVER.Send(e->GetIdxAsSessionKey(), EventPtr(res));
		};


		if (m_eState != EState::STATE_PLAY)
		{
			SendError(ErrorGame::E_CANNT_USE_OUT_OF_PLAY);
			return;
		}

		VectorPlayer players;
		m_sector->GetPlayers(players);

		if (players.size() > 1)
		{
			SendError(ErrorGame::E_CANNT_USE_WITH_MULTI_PLAYER);
			return;
		}

		EntityPlayer* player = m_sector->FindPlayerByCharId(e->GetKey());
		if (player == nullptr)
		{
			SendError(ErrorGame::E_ERROR);
			return;
		}

		m_resultType = ENtfAirshipDefenceRewardInfo::ResultType::GIVE_UP;
		changeNextState();
	}

	void AirshipDefenceProcessor::onCheckInUser(EntityUnit* owner, EntityUnit* target, UInt32 v1, UInt32 v2)
	{
		UNREFERENCED_PARAMETER(target);

		VERIFY_RETURN(owner && owner->IsValid() && owner->IsPlayer(), );

		EntityPlayer* player = static_cast<EntityPlayer*>(owner);

		auto found = m_mapUserInfos.find(player->GetId());
		if (found == m_mapUserInfos.cend())
		{
			UserInfo user;
			user.makeReward = false; 
			user.recvReward = false;			
			m_mapUserInfos.emplace(player->GetId(), user);
		}

		if (m_eState == EState::STATE_READY)
		{
			// 남은 시간을 알려준다.
			ENtfAirshipDefenceReadyTime* ntf = NEW ENtfAirshipDefenceReadyTime;
			Tick present = m_stateStart.Elapsed();
			if (present < SCRIPTS.GetAirshipDefenceRule()->readyTime)
			{
				ntf->remainMiliseconds = SCRIPTS.GetAirshipDefenceRule()->readyTime - present;
			}
			else
			{
				ntf->remainMiliseconds = 0;
			}
			SERVER.SendToClient(player, EventPtr(ntf));
		}
	}

	void AirshipDefenceProcessor::onLoadSyncCompleted(EntityUnit* owner, EntityUnit* target, UInt32 v1, UInt32 v2)
	{
		if (m_eState == EState::STATE_JOIN)
		{
			changeNextState();
		}
	}
	void AirshipDefenceProcessor::onGiveRewardAndExit(EntityUnit* receiver, EntityUnit* target, UInt32 requestStae, UInt32 v2)
	{
		VERIFY_RETURN(receiver && receiver->IsPlayer(), );

		EntityPlayer* recvPlayer = static_cast<EntityPlayer*>(receiver);

		// 죽어있는 플레이어는 살려준다.
		if (recvPlayer->IsDead())
		{
			auto actPlayerRevive = recvPlayer->GetAction<ActionPlayerRevive>();
			VALID_RETURN(actPlayerRevive, );

			auto actAbility = recvPlayer->GetAction<ActionAbility>();
			VALID_RETURN(actAbility, );

			actAbility->SetPercentHp(100);
			actAbility->SetPercentMp(100);
			{
				theReviveSystem.ProcessReviveResult(GameReviveType::CurPosRevive, recvPlayer);
			}
		}

		auto& found = m_mapUserInfos.find(recvPlayer->GetId());
		VERIFY_RETURN(found != m_mapUserInfos.end(), );

		if (found->second.recvReward != true)
		{
			if (found->second.makeReward == true)
			{
				m_reward.Reward(recvPlayer);
				found->second.recvReward = true;
			}
		}
		
		PVEMissionmapRewardState::Enum rewardState = static_cast<PVEMissionmapRewardState::Enum>(requestStae);

		if (PVEMissionmapRewardState::RECEIVE_AND_EXIT == rewardState)
		{
			recvPlayer->GetPortalAction().WarpBeginPosition();
		}
	}
	void AirshipDefenceProcessor::changeNextState()
	{
		int eState = m_eState + 1;
		VALID_RETURN(eState != EState::STATE_MAX, );

		if (m_eState != STATE_NONE)
		{
			m_states[m_eState][FUNC_EXIT]();
		}

		m_eState = static_cast<EState>(eState);
		m_states[m_eState][FUNC_ENTER]();
	}

	// 종료된 wave 처리 : 완료 stageUpdate, 종료된 waveUpdate, 실패 / 성공 확인.
	void AirshipDefenceProcessor::checkAndUpdateWave()
	{
		// NPC가 남아있는 wave를 구한다.
		VectorEntity npcEntities;
		m_sector->GetEntities(EntityTypes::NPC, npcEntities);

		std::set<UInt16> remainedNpcWaves; // npc가 남아있는 wave
		for (auto& entity : npcEntities)
		{
			ActionNpc* actNpc = GetEntityAction(entity);
			if (actNpc->GetSpawnerSectorValue() > 0)
			{
				remainedNpcWaves.insert(static_cast<UInt16>(actNpc->GetSpawnerSectorValue()));
			}
		}

		UInt32 activateWaveCount = 0;

		Tick currentElapsedTick = m_stateStart.Elapsed() + m_earlyProcessTick;
		Tick nextWaveTime = 0xffffffff;

		Bool missionClear = true;
		for (auto& iter : m_mapWaves)
		{
			completionCheckAndRewardStageUpdate(iter.second, remainedNpcWaves);
			if (false == iter.second.isComplete)
			{
				missionClear = false;
				if (true == iter.second.isStart)
				{
					// 활성화된 웨이브 수
					++activateWaveCount;
				}
			}
			// 스폰 시간 단축용 계산
			if (iter.second.isStart == false)
			{
				if (currentElapsedTick >= iter.second.activeTime)
				{
					nextWaveTime = 0;
				}
				else if (iter.second.activeTime - currentElapsedTick < nextWaveTime)
				{
					nextWaveTime = iter.second.activeTime - currentElapsedTick;
				}
			}
		}

		if (activateWaveCount == 0 && nextWaveTime != 0xffffffff && nextWaveTime > 0)
		{
			m_earlyProcessTick += nextWaveTime;
			processLogMessage(L"total skip delay time. total = %d, skip = %d milisec", m_earlyProcessTick, nextWaveTime);
		}

		if (missionClear == true || remainedNpcWaves.size() >= 4)
		{
			m_resultType = missionClear ? ENtfAirshipDefenceRewardInfo::ResultType::SUCCESS : ENtfAirshipDefenceRewardInfo::ResultType::STACKED_WAVE;
			changeNextState();
		}
	}

	void AirshipDefenceProcessor::completeWave(UInt16 waveNo)
	{
		// 다음 웨이브가 이전에 완료된 경우도 있을 수 있다.
		m_completedWaves.insert(waveNo);

		ENtfAirshipDefenceCompleteWave* ntf = new ENtfAirshipDefenceCompleteWave;
		ntf->waveNo = waveNo;
		broadcastEvent(EventPtr(ntf));

		//auto found = m_completedWaves.find(m_rewardWaveNo + 1);
		//while (found != m_completedWaves.end())
		//{
		//	m_rewardWaveNo = *found;
		//	processLogMessage(L"complete wave %d .!", m_rewardWaveNo);
		//	found = m_completedWaves.find(m_rewardWaveNo + 1);
		//}
	}

	void AirshipDefenceProcessor::completionCheckAndRewardStageUpdate(WaveUnit& waveUnit, std::set<UInt16>& remainedNpcWaves)
	{
		VALID_RETURN(false == waveUnit.isComplete && true == waveUnit.isStart, );

		// 진행중인 웨이브 중 시작이 되었고, 종료가 되지 않았는지 판단한다.		
		VectorEntity volumeSpawnEntities;
		m_sector->GetEntities(EntityTypes::CONTROL_VOLUME, volumeSpawnEntities);

		for (auto& entity : volumeSpawnEntities)
		{
			AttributeVolumeSpawn* attrVolume = GetEntityAttribute(entity);
			if (attrVolume->sectorValue == waveUnit.no)
			{
				for (int arrayOffset = 0; arrayOffset < Limits::MAX_NPC_REGEN; ++arrayOffset)
				{
					if (attrVolume->npcs[arrayOffset].spawnedTotalCount < attrVolume->npcs[arrayOffset].unitCount)
					{	
						return; // 볼륨에서 아직 모든 npc를 소환하지 않았다.
					}
				}

				auto found = remainedNpcWaves.find(waveUnit.no);
				if (found != remainedNpcWaves.end())
				{	
					return; // 소환된 npc가 아직 남아 있다.
				}				
			}
		}

		// 웨이브 클리어
		waveUnit.isComplete = true;
		remainedNpcWaves.erase(waveUnit.no);

		completeWave(waveUnit.no);		
	}

	// engine  기능 : 스킬 시전, 체력 회복
	void AirshipDefenceProcessor::updateEngine()
	{
		Entity4Zone* engineEntity = m_sector->FindEntity(m_engineUnit.entityId);

		EntityNpc* engine = static_cast<EntityNpc*>(engineEntity);
		if (engine && engine->IsValid() && engine->IsDead())
		{
			m_resultType = ENtfAirshipDefenceRewardInfo::ResultType::DESTROYED_ENGINE;
			changeNextState();
			return;
		}

		// 엔진을 사용할 수 있는 지정 웨이브 스킬을 시전한다.
		if (m_engineUnit.skillId > 0 && m_engineUnit.cooltimeTimer.Elapsed() > m_engineUnit.cooltime )
		{
			ActionNpcSkill* actNpcSkil = GetEntityAction(engine);
			auto elem = SCRIPTS.GetSkillInfoScript(m_engineUnit.skillId);
			if (elem && actNpcSkil)
			{
				actNpcSkil->SelectSkill(m_engineUnit.skillId);
				engine->GetHsm().Tran(STATE_NPC_SKILL_FIRE);

				processLogMessage(L"engine cast skill %d", m_engineUnit.skillId);
			}

			// 스킬 시전
			m_engineUnit.cooltimeTimer.Reset();			
		}		
	}

	// 활성 가능한 wave 시작
	void AirshipDefenceProcessor::checkAndActiveWave()
	{
		Tick	elapsed = m_stateStart.Elapsed();
		Tick	nextTime1st = 0;
		Tick	nextTime2st = 0;

		std::vector<UInt16> awakeWaves;
		auto iter = m_vecWaveTimes.begin();
		while (iter != m_vecWaveTimes.end())
		{
			if (elapsed + m_earlyProcessTick >= (*iter).time)
			{
				awakeWaves.emplace_back((*iter).no);
				iter = m_vecWaveTimes.erase(iter);
			}
			else
			{
				if (nextTime1st == 0)
				{
					nextTime1st = (*iter).time - (elapsed + m_earlyProcessTick);
				}
				else if( nextTime2st == 0)
				{
					nextTime2st = (*iter).time - (elapsed + m_earlyProcessTick);
				}
				if (nextTime1st == nextTime2st)
				{
					nextTime2st = 0;
				}

				if (nextTime2st > 0)
				{
					break;
				}
				++iter;
			}
		}
		
		for (auto& waveNo : awakeWaves)
		{
			auto& found = m_mapWaves.find(waveNo);
			VERIFY_RETURN(found != m_mapWaves.end(), );

			WaveUnit& waveUnit = found->second;

			VERIFY_RETURN(waveUnit.isStart != true && (elapsed + m_earlyProcessTick >= waveUnit.activeTime), );

			const AirshipDefence::WaveUnitElem* elem = SCRIPTS.GetAirshipDefenceWaveUnitElem(m_difficultyLevel, waveNo);
			VERIFY_RETURN(elem != nullptr, );

			VERIFY_RETURN(theSpawnSystem.SpawnVolumeSpawnWithLayer(m_sector, elem->spawnLayer, 0), );

			activeSpawnVolumeLayer(static_cast<UInt16>(elem->spawnLayer), waveNo);
			m_isNewSpawnedWave = true;
			if (m_rewardWaveNo < waveNo)
			{
				m_rewardWaveNo = waveNo;
				m_rewardStageNo = SCRIPTS.GetAirshipDefenceRewardStageNoFromWaveNo(m_difficultyLevel, m_rewardWaveNo);
			}
			processLogMessage(L"wave %d start!.. next 1st = %d, next 2nd = %d, earyTick =%d", waveNo, nextTime1st, nextTime2st, m_earlyProcessTick);
			waveUnit.isStart = true;

			auto engineSkillElem = SCRIPTS.GetAirshipDefenceEngineSkill(m_difficultyLevel, waveNo);
			m_engineUnit.skillId = engineSkillElem->skill.index;
			m_engineUnit.cooltime = engineSkillElem->skill.cooltime;

			// 웨이브가 시작되고, 
			ENtfAirshipDefenceStartWave* ntf = new ENtfAirshipDefenceStartWave;
			ntf->waveNo = waveNo;
			ntf->stageNo = elem->stageNo;
			ntf->nextWaveTime1st = nextTime1st;
			ntf->nextWaveTime2nd = nextTime2st;
			broadcastEvent(EventPtr(ntf));
		}
	}


	Bool AirshipDefenceProcessor::setupDifficulty(DifficultyLevel difficultyLevel, UInt16 startPartyCount)
	{
		// 섹터의 난이도 세팅
		m_difficultyLevel = difficultyLevel;
		m_startPartyCount = startPartyCount;

		// 난이도에 해당 하는 WaveData 생성
		m_mapWaves.clear();
		m_vecWaveTimes.clear();
		AirshipDefence::WaveUnitElemVec waveElems;
		SCRIPTS.GetAirshipDefenceWaveElems(m_difficultyLevel, waveElems);
		for (auto elem : waveElems)
		{
			WaveUnit waveUnit;
			waveUnit.no = elem.key.units.waveNo;
			waveUnit.activeTime = GetRandBetween(elem.delay.min, elem.delay.max);
			waveUnit.isStart = false;
			m_mapWaves.emplace(WaveUnitMap::value_type(waveUnit.no, waveUnit));

			WaveTime waveTime;
			waveTime.no = waveUnit.no;
			waveTime.time = waveUnit.activeTime;
			m_vecWaveTimes.emplace_back(waveTime);
		}

		std::sort(m_vecWaveTimes.begin(), m_vecWaveTimes.end(), [](const WaveTime& lhs, const WaveTime& rhs)
			{ return lhs.time < rhs.time; });

		// 전역 SkipTimeSetting
		m_earlyProcessTick = 0;
		m_resultType = ENtfAirshipDefenceRewardInfo::ResultType::NONE;

		m_engineUnit.cooltimeTimer.Reset();
		m_engineUnit.skillId = 0;

		return true;
	}
	
	void AirshipDefenceProcessor::enterJoinState()
	{
		m_stateStart.Reset();
	}
	void AirshipDefenceProcessor::processJoinState()
	{
		// 바로 다음 으로 넘긴다.
		if (m_stateStart.Elapsed() > JOIN_DELAY_TICK_TIME)
		{
			changeNextState();
		}
	}

	void AirshipDefenceProcessor::exitJoinState()
	{

	}
	
	void AirshipDefenceProcessor::enterReadyState()
	{
		ActionSectorController* action = GetEntityAction(GetParent());
		m_sector = action->GetSector();

		// Join exit 에서 던전 레벨 최종 세팅 ( 파티인원 난이도는 어느게 맞는건지 모르겠네 )
		setupDifficulty(m_sector->GetDifficulty(), static_cast<UInt16>(m_sector->GetEntranceUserCount()));

		// 엔진은 기본적으로 소환되기 때문에...
		
		ENtfAirshipDefenceReadyTime* ntf = new ENtfAirshipDefenceReadyTime;
		ntf->remainMiliseconds = SCRIPTS.GetAirshipDefenceRule()->readyTime;
		broadcastEvent(EventPtr(ntf));

		m_stateStart.Reset();

		processLogMessage(L"ready start!.");
	}
	void AirshipDefenceProcessor::processReadyState()
	{
		if (m_stateStart.Elapsed() > SCRIPTS.GetAirshipDefenceRule()->readyTime)
		{
			changeNextState();
		}
	}
	void AirshipDefenceProcessor::exitReadyState()
	{
		auto engindIndex = SCRIPTS.GetAirshipDefenceEngineNpcIndex(m_difficultyLevel, m_startPartyCount);
		VERIFY_RETURN(engindIndex, );

		VectorNpc npcs;
		m_sector->GetNpcs(npcs);

		for (auto& npc : npcs)
		{
			if (npc->GetNpcIndex() == engindIndex)
			{
				m_engineUnit.entityId = npc->GetId().GetUnique();
			}
		}
	}

	void AirshipDefenceProcessor::activeSpawnVolumeLayer(IndexLayer layer, UInt16 waveNo)
	{
		EntityControlVolume* controlVolume;
		do 
		{
			controlVolume = m_sector->FindControlVolumeByCond([&](const SectorEntityMap::value_type& v)
			{
				if (v.second->HasAttribute(ATTRIBUTE_SPAWN_VOLUME) == false)
				{
					return false;
				}

				AttributeVolumeSpawn* attr = GetEntityAttribute(v.second);
				if (nullptr == attr)
				{
					return false;
				}

				if (attr->layer == layer && attr->disabled == true)
				{
					attr->disabled = false;
					attr->sectorValue = waveNo;
					return true;
				}

				return false;
			});
		} while (controlVolume != nullptr);
	}

	void AirshipDefenceProcessor::deactiveAllSpawnVolumeLayer()
	{
		// 모든 layer의 스폰볼륨을 중지시킨다.
		EntityControlVolume* controlVolume;
		do
		{
			controlVolume = m_sector->FindControlVolumeByCond([&](const SectorEntityMap::value_type& v)
			{
				if (v.second->HasAttribute(ATTRIBUTE_SPAWN_VOLUME) == false)
				{
					return false;
				}

				AttributeVolumeSpawn* attr = GetEntityAttribute(v.second);
				if (nullptr == attr)
				{
					return false;
				}

				if (attr->disabled == false && attr->layer > 0)
				{
					attr->disabled = true;
					return true;
				}

				return false;
			});

		} while (controlVolume != nullptr);
	}

	void AirshipDefenceProcessor::enterPlayState()
	{
		m_stateStart.Reset();

		processLogMessage(L"play start!.");

	}
	void AirshipDefenceProcessor::processPlayState()
	{
		checkAndUpdateWave();
		updateEngine();
		checkAndActiveWave();
	}
	void AirshipDefenceProcessor::exitPlayState()
	{
		m_clearTime = m_stateStart.Elapsed(); // 플레이 시간
		// 만약 실패라면
		deactiveAllSpawnVolumeLayer();
		VectorPlayer players;
		m_sector->GetPlayers(players);
		for (auto player : players)
		{	
			player->SetMyTeam(TeamType::Enum::TEAM_A);
		}

		VectorNpc npcs;
		m_sector->GetNpcs(npcs);
		for (auto npc : npcs)
		{	
			npc->SetMyTeam(TeamType::Enum::TEAM_A);
		}

		makeResult();
	}

	void AirshipDefenceProcessor::makeResult()
	{
		//m_rewardStageNo = SCRIPTS.GetAirshipDefenceRewardStageNoFromWaveNo(m_difficultyLevel, m_rewardWaveNo);

		// 공통적으로 지급해야할것 또는 만들어야 할것
		VectorPlayer players;
		m_sector->GetPlayers(players);

		for (auto i = players.begin(); i != players.end(); ++i)
		{
			EntityPlayer* player = (*i);
			
			updateSeasonMission(player);

#ifdef __Patch_ImprintedMemory_by_raylee_181128
			updateAchieveMission(player);
#endif

			makeReward(player);
		}

		if (m_rewardStageNo > 0)
		{
			ENtfUpdateDungeonLogEndlessStage * ntfLog = NEW ENtfUpdateDungeonLogEndlessStage;
			ntfLog->actionLogGroupKey = m_sector->GetActionLogGroupKey();
			ntfLog->stage = m_rewardStageNo;
			SERVER.SendToOnlyWorldServer(EventPtr(ntfLog));
		}
	}

	void AirshipDefenceProcessor::makeReward(EntityPlayer* player)
	{	
		auto& found = m_mapUserInfos.find(player->GetId());
		VERIFY_RETURN(found != m_mapUserInfos.end(), );
		VERIFY_RETURN(found->second.makeReward == false, );

		setReward(player, true);

		found->second.makeReward = true;
		found->second.recvReward = false;
	}

	void AirshipDefenceProcessor::setReward(EntityPlayer* player, Bool notify)
	{
		ENtfAirshipDefenceRewardInfo* ntf = nullptr;
		if (notify)
		{
			ntf = NEW ENtfAirshipDefenceRewardInfo;
			ntf->zen = 0;
			ntf->exp = 0;
			ntf->soulExp = 0;

			ntf->resultType = m_resultType;
			ntf->clearTime = m_clearTime;
			ntf->rewardStageNo = m_rewardStageNo;
		}

		auto setupReward = [&](MissionMapRewardInfo& rewardInfo, const AirshipDefence::RewardElem* rewardElem)
		{
			if (rewardElem != nullptr)
			{
				MissionmapRewardParam param;
				param.itemIndex = 0;
				param.dropIndex = rewardElem->itemIndex;

				ItemPtr rewardItem(nullptr);
				theDropSystem.DropFromMissionClear(player, param, __out rewardItem);

				if (rewardItem)
				{
					rewardInfo.items.push_back(rewardItem);
					if (notify)
					{
						ntf->rewardItems.push_back(*rewardItem);
						ntf->slotTypes.push_back(static_cast<Byte>(rewardElem->slotType));
					}
				}
			}
		};
				
		if (m_rewardStageNo > 0)
		{
			MissionMapRewardInfo reward;
			reward.mail.senderName = L"Msg_Mailbox_Airship_Defence_SENDER";
			reward.mail.subject = L"Msg_Mailbox_Airship_Defence_SUBJECT";
			reward.mail.content = L"Msg_Mailbox_Airship_Defence_CONTENT";

			for (UInt16 i = 1; i <= m_rewardStageNo; ++i)
			{
				auto rewardElem = SCRIPTS.GetAirshipDefenceRewardElem(m_difficultyLevel, i);
				setupReward(reward, rewardElem);
			}
			if( m_resultType == ENtfAirshipDefenceRewardInfo::ResultType::SUCCESS)
			{
				ENtfUpdateDungeonLogMissionComplete* ntfComplete = NEW ENtfUpdateDungeonLogMissionComplete;
				ntfComplete->actionLogGroupKey = m_sector->GetActionLogGroupKey();
				SERVER.SendToOnlyWorldServer(EventPtr(ntfComplete));

				auto vecElem = SCRIPTS.GetAirshipDefenceAllClearRewardElem(m_difficultyLevel);
				if (vecElem != nullptr)
				{
					for (auto& elem : *vecElem)
					{
						setupReward(reward, &elem);
					}
				}
			}
			m_reward.AddReward(player, reward);
		}

		if (notify)
		{
			SERVER.SendToClient(player, EventPtr(ntf));
		}

		ActionPlayerDailyMission* actionDailyMission = GetEntityAction(player);
		if (actionDailyMission != nullptr)
		{
			actionDailyMission->NofityAirshipDefenceClear(m_difficultyLevel, m_rewardStageNo);
		}
		player->GetPlayerEventBuffAction().AirshipDefenceClearEvent( m_difficultyLevel, m_rewardStageNo );
	}

	void AirshipDefenceProcessor::updateSeasonMission(EntityPlayer* player)
	{
		SeasonMissionEventAirShipDefenceClearCount seasonEvent(m_rewardStageNo, m_difficultyLevel);
		ActionPlayerSeason * actionSeason = GetEntityAction(player);
		if (nullptr != actionSeason)
		{
			actionSeason->Event(&seasonEvent);
		}
	}

#ifdef __Patch_ImprintedMemory_by_raylee_181128
	void AirshipDefenceProcessor::updateAchieveMission(EntityPlayer* player)
	{
		AchievementAirshipDefenceClearEvent achieveEvent(player, m_rewardStageNo);
		ActionPlayerAchievement * act = GetEntityAction(player);
		if (act)
			act->OnEvent(&achieveEvent);
	}
#endif

	void AirshipDefenceProcessor::enterRewardState()
	{
		VERIFY_DO(m_resultType != ENtfAirshipDefenceRewardInfo::ResultType::NONE, MustErrorCheck);
		processLogMessage(L"reward start!.");
		
		m_stateStart.Reset();
	}
	void AirshipDefenceProcessor::updateRewardState()
	{
		if (m_stateStart.Elapsed() > REWARD_AND_EXIT_TICK_TIME)
		{
			ActionSectorController* act = GetEntityAction(GetParent());
			if (act != nullptr)
			{
				act->DestroyDungeon();
			}

			m_stateStart.Reset();
		}
	}
	void AirshipDefenceProcessor::exitRewardState()
	{

	}

	void AirshipDefenceProcessor::processLogMessage(WChar* msg, ...)
	{
		VERIFY_RETURN(m_sector, );

		WChar logMsg[1024] = { 0 };

		va_list args;
		va_start(args, msg);
		_vsnwprintf_s(logMsg, 1000, 1000, msg, args);
		va_end(args);

		MU2_TRACE_LOG(LogCategory::CONTENTS,L"AirshipDefenceProcessor> execution(%d) : %s", m_sector->GetExecId().GetUnique(), logMsg);
	}

	void AirshipDefenceProcessor::broadcastEvent(EventPtr& e)
	{
		VERIFY_RETURN(m_sector, );

		VectorPlayer players;
		m_sector->GetPlayers(players);
		for (auto& player : players)
		{
			SERVER.SendToClient(player, e);
		}
	}
}
