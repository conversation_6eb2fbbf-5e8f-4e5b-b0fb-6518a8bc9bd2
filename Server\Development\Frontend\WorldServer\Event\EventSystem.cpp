﻿#include "stdafx.h"

#include <Frontend/WorldServer/Event/EventSystem.h>

namespace mu2
{
	GameEventSystem::GameEventSystem() :
		m_updateTimer()
		, m_advantureEventId(0)
	{
		m_typeCount.fill(0);
	}

	GameEventSystem::~GameEventSystem()
	{

	}

	void GameEventSystem::InitGameEvent()
	{
		DateTime nowDate = DateTime::GetPresentTime();

		const auto vecEvent = SCRIPTS.GetGameEvent();

		for (auto iter : vecEvent)
		{
			const auto elem = SCRIPTS.GetGameEventElem( iter );
			VALID_DO(elem, continue);

			if (elem->startDate > nowDate)
			{
				GameEventSchedule info;
				info.eventId = iter;
				info.time = elem->startDate;
				m_readyEventqueue.emplace( info );
				m_readyEvents.emplace( iter );
			}
			else if (elem->startDate <= nowDate && nowDate < elem->endDate)
			{
				GameEventSchedule info;
				info.eventId = iter;
				info.time = elem->endDate;
				m_eventqueue.emplace( info );
				m_events.emplace( iter );

				//! 월드 이벤트를 등록해 놓아야, DB 에 요청해서 얻어올 수 있다.
				if (elem->eventType == GameEventElem::BOSS_KILL)
				{
					AddWUEvent(iter);
				}

				if (elem->eventPopup == GameEventElem::POPUP_ON)
				{
					AddPopupEvent(iter);
				}

				if (elem->eventType == GameEventElem::ADVANTURE_NOTE)
				{
					EnableAdvantureNote(iter);
				}

				AddType(elem->eventType);
			}
			else
			{
				m_completeEvent.emplace( iter );
			}
		}
	}

	void GameEventSystem::UpdateWUEventCnt(Index32 eid, Int64 cnt, Bool force)
	{
		// 여러 월드에서 오는 패킷이므로, 순서가 바뀌어서 cnt 값이 더 작을 수 있다. 
		auto itr = m_WUEvent.find(eid);
		if (itr != m_WUEvent.end())
		{
			if (force)
			{
				itr->second.count = cnt;
			}
			else
			{
				if (itr->second.count < cnt)
					itr->second.count = cnt;
			}
		}
	}

	void GameEventSystem::AddWUEvent(Index32 eid)
	{
		auto itr = m_WUEvent.find(eid);
		if (itr == m_WUEvent.end())
			m_WUEvent.emplace(std::make_pair(eid, WorldUnitedEvent(eid, 0)));
	}

	void GameEventSystem::AddPopupEvent(Index32 eid)
	{
		m_popupEvent.push_back(eid);
	}

	Int64 GameEventSystem::GetWUEventCnt(Index32 eid)
	{
		Int64 cnt = -1;
		auto itr = m_WUEvent.find(eid);
		if (itr != m_WUEvent.end())
		{
			cnt = itr->second.count;
		}
		return cnt;
	}

	void GameEventSystem::GetPopupEvent(std::vector<Index32>& vec)
	{
		vec.assign(m_popupEvent.begin(), m_popupEvent.end());
	}

	void GameEventSystem::Update()
	{
		SIMPLE_TIMER_CHECK( m_updateTimer, 1000 * 60 );

		updateStartEvent();
		updateEndEvent();
	}

	void GameEventSystem::updateStartEvent()
	{
		VALID_RETURN( m_readyEventqueue.size(), );

		GameEventSchedule queue;
		DateTime nowDate = DateTime::GetPresentTime();
		std::vector<Index32> startEvents;

		while (m_readyEventqueue.size())
		{
			queue = m_readyEventqueue.top();

			VALID_DO( nowDate > queue.time, break );

			m_readyEvents.erase( queue.eventId );

			startEvents.push_back( queue.eventId );
			m_readyEventqueue.pop();

			const auto elem = SCRIPTS.GetGameEventElem( queue.eventId );
			VALID_DO( elem, continue );

			queue.time = elem->endDate;
			m_eventqueue.push( queue );


			//! 월드 이벤트를 등록해 놓아야, DB 에 요청해서 얻어올 수 있다.
			if (elem->eventType == GameEventElem::BOSS_KILL)
			{
				AddWUEvent( queue.eventId );
			}

			if (elem->eventPopup == GameEventElem::POPUP_ON)
			{
				AddPopupEvent( queue.eventId );
			}

			if (elem->eventType == GameEventElem::ADVANTURE_NOTE)
			{
				EnableAdvantureNote( queue.eventId );
			}

			AddType( elem->eventType );
		}

		VALID_RETURN( startEvents.size(), );

		ENtfGameEventStart* req = new ENtfGameEventStart;
		req->vecEvents = std::move( startEvents );
		SERVER.SendToAllZoneInMyWorld( EventPtr( req ) );
	}

	void GameEventSystem::updateEndEvent()
	{
		VALID_RETURN( m_eventqueue.size(), );

		GameEventSchedule queue;
		DateTime nowDate = DateTime::GetPresentTime();

		std::vector<Index32> endEvents;

		while (m_eventqueue.size())
		{
			queue = m_eventqueue.top();
			VALID_DO( nowDate > queue.time, break );

			m_completeEvent.emplace( queue.eventId );
			m_events.erase( queue.eventId );

			m_WUEvent.erase( queue.eventId );

			auto itr = m_popupEvent.begin();
			while (itr != m_popupEvent.end())
			{
				if (*itr == queue.eventId)
				{
					m_popupEvent.erase( itr );
					break;
				}

				++itr;
			}


			const auto elem = SCRIPTS.GetGameEventElem( queue.eventId );
			VALID_DO( elem, continue );

			if (elem->eventType == GameEventElem::ADVANTURE_NOTE)
			{
				DisbleAdvantureNote();
			}

			SubType( elem->eventType );

			endEvents.push_back( queue.eventId );
			m_eventqueue.pop();
		}

		VALID_RETURN( endEvents.size(), );

		ENtfGameEventEnd* req = new ENtfGameEventEnd;
		req->vecEvents = std::move( endEvents );
		SERVER.SendToAllZoneInMyWorld( EventPtr( req ) );
	}

	void GameEventSystem::SendEventInfoToZone( ServerId serverId )
	{
		ENtfGameEventList *req = new ENtfGameEventList;

		for (auto eventIter : m_events)
		{
			req->eventLists.push_back( eventIter );
		}

		for (auto completeEvent : m_completeEvent)
		{
			req->completeEvents.push_back( completeEvent );
		}

		for (auto bossKill : m_WUEvent)
		{
			req->bossKillEvents.push_back(bossKill.first);
		}

		req->advantureEvent = m_advantureEventId;

		SERVER.SendToServerByServerId( serverId, EventPtr( req ) );
	}

	void GameEventSystem::ReqWUEventInfo()
	{
		//! 
		for (auto eitr : m_WUEvent)
		{
			EReqDbGetWorldUnitedEventValue * req = new EReqDbGetWorldUnitedEventValue;
			req->eventIndex = eitr.first;

			SERVER.SendToDb(EventPtr(req));
		}
	}
};