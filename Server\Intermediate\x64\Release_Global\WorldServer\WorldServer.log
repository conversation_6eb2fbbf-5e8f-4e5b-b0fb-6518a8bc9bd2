﻿  stdafx.cpp
  AchievementHandler.cpp
  AchievementJobLimitAchievementCache.cpp
  AchievementManager.cpp
  AppChat.cpp
  AttributePlayer.cpp
  BurningStaminaContinetInfo.cpp
  BurningStaminaHandler.cpp
  BurningStaminaInfo.cpp
  BurningStaminaManager.cpp
  BurningStaminaTimeInfo.cpp
  BurningStaminaUserInfo.cpp
  Channel.cpp
  ChannelDominion.cpp
  ChannelFieldpoint.cpp
  ChannelKnightageFieldRaid.cpp
  ChannelGroup.cpp
  ChannelManager.cpp
  ChannelNull.cpp
  ChatHandler.cpp
  ColosseumHandler.cpp
  CommunityFriend.cpp
  CommunitySearchChar.cpp
  CommunityCharBlock.cpp
  CommunityTeacherAndStudent.cpp
  ContentsDeactivator.cpp
  DailyComparer.cpp
  DailyMissionHandler.cpp
  DailyMissionManager.cpp
  DailyTimeConnectionManager.cpp
  Entity4WorldServer.cpp
  Dominion.cpp
  DominionManager.cpp
  EntityParty.cpp
  EntityPlayer.cpp
  EventSchedule.cpp
  EventScheduleManager.cpp
  EventSystem.cpp
  PlayerReturnEventAction.cpp
  ExecInstanceBase.cpp
  Execution.cpp
  ExecutionGroup.cpp
  ExecutionNull.cpp
  WorldFcsAsyncReceiverAuth.cpp
  FieldPointForwarder.cpp
  FieldPointHandler.cpp
  FieldPointManager.cpp
  FieldPointProcessor.cpp
  Instance.cpp
  InstanceColloseumPvp33.cpp
  InstanceGroup.cpp
  InstanceMultisage.cpp
  InstanceNormal.cpp
  InstanceNull.cpp
  InstancePartyInfo.cpp
  InstancePvp.cpp
  InstanceSinglestage.cpp
  InstanceTournament.cpp
  InstanceWorldcross.cpp
  JoinZoneContext.cpp
  JoinZoneContextBase.cpp
  JoinZoneContextChangeChannel.cpp
  JoinZoneContextChangeMap.cpp
  JoinZoneContextCheckIn.cpp
  JoinZoneContextLogout.cpp
  JoinZoneContextTutorial.cpp
  JoinZoneContextWarpMap.cpp
  KnightageFieldRaidScheduler.cpp
  KnightagePaymentScheduler.cpp
  LiberationManager.cpp
  LuckyMonsterHandler.cpp
F:\Release_Branch\Server\Development\Frontend\WorldServer\Knightage\KnightageFieldRaidScheduler.cpp(84,2): error C2220: 以下警告被视为错误
  LuckyMonsterManager.cpp
  (编译源文件“/Knightage/KnightageFieldRaidScheduler.cpp”)
  
F:\Release_Branch\Server\Development\Frontend\WorldServer\Knightage\KnightageFieldRaidScheduler.cpp(84,2): warning C4840: 将类 "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t>>" 作为可变参数函数的参数的不可移植用法
  (编译源文件“/Knightage/KnightageFieldRaidScheduler.cpp”)
      F:\Release_Branch\Server\Development\Frontend\WorldServer\Knightage\KnightageFieldRaidScheduler.cpp(84,2):
      "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t>>::basic_string" 不常用
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(716,5):
      参见“std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t>>::basic_string”的声明
      F:\Release_Branch\Server\Development\Frontend\WorldServer\Knightage\KnightageFieldRaidScheduler.cpp(84,2):
      不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(541,7):
      参见“std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t>>”的声明
  
F:\Release_Branch\Server\Development\Frontend\WorldServer\Liberation\LiberationManager.cpp(772,137): error C2220: 以下警告被视为错误
  (编译源文件“/Liberation/LiberationManager.cpp”)
  
F:\Release_Branch\Server\Development\Frontend\WorldServer\Liberation\LiberationManager.cpp(772,137): warning C4840: 将类 "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t>>" 作为可变参数函数的参数的不可移植用法
  (编译源文件“/Liberation/LiberationManager.cpp”)
      F:\Release_Branch\Server\Development\Frontend\WorldServer\Liberation\LiberationManager.cpp(772,137):
      "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t>>::basic_string" 不常用
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(716,5):
      参见“std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t>>::basic_string”的声明
      F:\Release_Branch\Server\Development\Frontend\WorldServer\Liberation\LiberationManager.cpp(772,137):
      不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(541,7):
      参见“std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t>>”的声明
  
  ManagerInvasionContext.cpp
  ManagerJoinZoneContext.cpp
  PlayerMembershipAction.cpp
  MissionMapJoinManager.cpp
  MopupHandler.cpp
  MopupManager.cpp
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm(5802,64): error C2220: 以下警告被视为错误
  (编译源文件“/LuckyMonster/LuckyMonsterManager.cpp”)
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm(5802,64): warning C4244: “参数”: 从“std::_Random_shuffle1::_Diff”转换到“Int32”，可能丢失数据
  (编译源文件“/LuckyMonster/LuckyMonsterManager.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm(5802,64):
      模板实例化上下文(最早的实例化上下文)为
          F:\Release_Branch\Server\Development\Frontend\WorldServer\LuckyMonster\LuckyMonsterManager.cpp(149,9):
          查看对正在编译的函数 模板 实例化“void std::random_shuffle<std::_Vector_iterator<std::_Vector_val<std::_Simple_types<_Ty>>>,mu2::LuckyMonsterManager::OnTimerSpawnJob::<lambda_ae8f59b85309793c4bf77f60f4adf43f>>(_RanIt,_RanIt,_RngFn &&)”的引用
          with
          [
              _Ty=mu2::LuckyMonsterManager::SectorInfo,
              _RanIt=std::_Vector_iterator<std::_Vector_val<std::_Simple_types<mu2::LuckyMonsterManager::SectorInfo>>>,
              _RngFn=mu2::LuckyMonsterManager::OnTimerSpawnJob::<lambda_ae8f59b85309793c4bf77f60f4adf43f>
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm(5877,10):
          查看对正在编译的函数 模板 实例化“void std::_Random_shuffle1<_RanIt,_RngFn>(_RanIt,_RanIt,_RngFn &)”的引用
          with
          [
              _RanIt=std::_Vector_iterator<std::_Vector_val<std::_Simple_types<mu2::LuckyMonsterManager::SectorInfo>>>,
              _RngFn=mu2::LuckyMonsterManager::OnTimerSpawnJob::<lambda_ae8f59b85309793c4bf77f60f4adf43f>
          ]
  
  OhrdorTreasureHunt.cpp
  PCRoomBillingEventLoopBackHandler.cpp
  PlayerDailyLoginAction.cpp
  DailyLoginHandler.cpp
  DailyLoginManager.cpp
  DamageMeterHandler.cpp
  DamageMeterManager.cpp
  DungeonLog.cpp
  DungeonLogManager.cpp
  GlobalGameConfigInfo.cpp
  GlobalGameConfigManager.cpp
  InvasionAction.cpp
  JoinRankTimeCalculator.cpp
  KISSApiWrapper.cpp
  Knightage.cpp
  KnightageAbility.cpp
  KnightageHandler.cpp
  KnightageLoader.cpp
  KnightageManager.cpp
  KnightageMember.cpp
  KnightageSaveChecker.cpp
  PlayerKnightageAction.cpp
  MonitorWorld.cpp
  PlayerDailyMissionAction.cpp
  PlayerDailyTimeConnectionAction.cpp
  PlayerTeacherAndStudentAction.cpp
  PVPFieldSchedule.cpp
F:\Release_Branch\Server\Development\Frontend\WorldServer\Knightage\KnightageHandler.cpp(1664,5): error C2220: 以下警告被视为错误
  (编译源文件“/Knightage/KnightageHandler.cpp”)
  
F:\Release_Branch\Server\Development\Frontend\WorldServer\Knightage\KnightageHandler.cpp(1664,5): warning C4840: 将类 "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t>>" 作为可变参数函数的参数的不可移植用法
  (编译源文件“/Knightage/KnightageHandler.cpp”)
      F:\Release_Branch\Server\Development\Frontend\WorldServer\Knightage\KnightageHandler.cpp(1664,5):
      "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t>>::basic_string" 不常用
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(716,5):
      参见“std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t>>::basic_string”的声明
      F:\Release_Branch\Server\Development\Frontend\WorldServer\Knightage\KnightageHandler.cpp(1664,5):
      不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(541,7):
      参见“std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t>>”的声明
  
  QuestSystem.cpp
  RaidHandler.cpp
  RaidManager.cpp
  TournamentRaid.cpp
  Ranking.cpp
  ReLoginGetFcsAuthInfoHandler.cpp
  SeasonHandler.cpp
  SwitchInvasion.cpp
  SwitchNonInvasion.cpp
  SeasonManager.cpp
  Tournament.cpp
  TournamentManager.cpp
  WOPSAutoHackDetectionEvent.cpp
  WOPSAutoHackRemoveDebuffEvent.cpp
  WOPSChatBlockCharacterEvent.cpp
  WOPSCmdKnightage.cpp
  WOPSCommandItemEnchantDiscount.cpp
  WOPSEditKnightageTrophyPoint.cpp
  WOPSExchangeItemEvent.cpp
  WOPSKickAccountEvent.cpp
  WOPSKickCharacterEvent.cpp
  WOPSLoginBlockAccountEvent.cpp
  WOPSMinigameRewardEvent.cpp
  WOPSMopupEvent.cpp
  WOPSNoticeInstantEvent.cpp
  WOPSNoticeReservationEvent.cpp
  WOPSWorldCloseEvent.cpp
  WOPSWorldOpenEvent.cpp
  BaseItemTranscation.cpp
  KnightageTranscation.cpp
  PlayerWShopAction.cpp
  TaskItemTranscation.cpp
  WShop.cpp
  WShopBillingEventHandler.cpp
  WShopInventoryEventHandler.cpp
  WShopJewelEventHandler.cpp
  WShopJob.cpp
  WShopJobManager.cpp
  WShopStatusEventHandler.cpp
  WShopTask.cpp
  WShopTaskChargeJewel.cpp
  WShopTaskCheckBalance.cpp
  WShopTaskGift.cpp
  WShopTaskInventory.cpp
  WShopTaskItemTranscation.cpp
  WShopTaskMopupPurchaseJewel.cpp
  WShopTaskPickup.cpp
  WShopTaskPickupTradeJewel.cpp
  WShopTaskPurchase.cpp
  WShopTaskPurchaseCharacSlot.cpp
  WShopTaskPurchaseJewel.cpp
  WShopTaskTradeJewel.cpp
  ZoneLoadBalancer.cpp
  MultistageDungeonHandler.cpp
  MultistageDungeonManager.cpp
  PartyDungeonManager.cpp
  ExecutionManager.cpp
  CommunityHandler.cpp
  GloryRankManager.cpp
  GloryRankHandler.cpp
  GMCommandSystem.cpp
  InstanceManager.cpp
  JoinRankManager.cpp
  JoinWaitHandler.cpp
  main.cpp
  MissionMapHandler.cpp
  PartyAction.cpp
  PartyEntityFactoryImpl.cpp
  PartyEntityManager.cpp
  PartyHandler.cpp
F:\Release_Branch\Server\Development\Frontend\WorldServer\GMCommandSystem.cpp(1244,79): error C2220: 以下警告被视为错误
  (编译源文件“/GMCommandSystem.cpp”)
  
F:\Release_Branch\Server\Development\Frontend\WorldServer\GMCommandSystem.cpp(1244,79): warning C4840: 将类 "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t>>" 作为可变参数函数的参数的不可移植用法
  (编译源文件“/GMCommandSystem.cpp”)
      F:\Release_Branch\Server\Development\Frontend\WorldServer\GMCommandSystem.cpp(1244,79):
      "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t>>::basic_string" 不常用
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(716,5):
      参见“std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t>>::basic_string”的声明
      F:\Release_Branch\Server\Development\Frontend\WorldServer\GMCommandSystem.cpp(1244,79):
      不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(541,7):
      参见“std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t>>”的声明
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm(3799,24): warning C4244: “=”: 从“int”转换到“wchar_t”，可能丢失数据
  (编译源文件“/GMCommandSystem.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\algorithm(3799,24):
      模板实例化上下文(最早的实例化上下文)为
          F:\Release_Branch\Server\Development\Frontend\WorldServer\GMCommandSystem.cpp(3212,7):
          查看对正在编译的函数 模板 实例化“_OutIt std::transform<std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,std::_String_iterator<std::_String_val<std::_Simple_types<_Elem>>>,int(__cdecl *)(int)>(const _InIt,const _InIt,_OutIt,_Fn)”的引用
          with
          [
              _OutIt=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Elem=wchar_t,
              _InIt=std::_String_iterator<std::_String_val<std::_Simple_types<wchar_t>>>,
              _Fn=int (__cdecl *)(int)
          ]
  
  PartySearchManager.cpp
  PCBillAdapterLoader.cpp
  PCRoomBillingEventHandler.cpp
  PCRoomBillingManager.cpp
  PlayerBlockAction.cpp
  PlayerChatPenaltyAction.cpp
  PlayerDamageMeterAction.cpp
  PlayerFriendAction.cpp
  PlayerHandler.cpp
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,16): error C2220: 以下警告被视为错误
  (编译源文件“/PlayerChatPenaltyAction.cpp”)
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,16): warning C4244: “初始化”: 从“_Ty”转换到“_Ty1”，可能丢失数据
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,16): warning C4244:         with
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,16): warning C4244:         [
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,16): warning C4244:             _Ty=mu2::PlayerChatPenaltyAction::<unnamed-enum-REGION>
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,16): warning C4244:         ]
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,16): warning C4244:         and
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,16): warning C4244:         [
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,16): warning C4244:             _Ty1=const Byte
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,16): warning C4244:         ]
  (编译源文件“/PlayerChatPenaltyAction.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(303,16):
      模板实例化上下文(最早的实例化上下文)为
          F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerChatPenaltyAction.cpp(242,17):
          查看对正在编译的函数 模板 实例化“std::pair<const Byte,UInt32>::pair<mu2::PlayerChatPenaltyAction::<unnamed-enum-REGION>,int,0>(std::pair<mu2::PlayerChatPenaltyAction::<unnamed-enum-REGION>,int> &&) noexcept”的引用
              F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerChatPenaltyAction.cpp(241,2):
              请参阅 "mu2::PlayerChatPenaltyAction::CheckChatTypeTick" 中对 "std::pair<const Byte,UInt32>::pair" 的第一个引用
  
  PlayerJoinAction.cpp
  PlayerMissionmapAction.cpp
  PlayerPartyAction.cpp
  PlayerSearchPartyAction.cpp
  PlayerPortalAction.cpp
  PlayerServantAction.cpp
F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerHandler.cpp(1033,2): error C2220: 以下警告被视为错误
  (编译源文件“/PlayerHandler.cpp”)
  
F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerHandler.cpp(1033,2): warning C4840: 将类 "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t>>" 作为可变参数函数的参数的不可移植用法
  (编译源文件“/PlayerHandler.cpp”)
      F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerHandler.cpp(1033,2):
      "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t>>::basic_string" 不常用
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(716,5):
      参见“std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t>>::basic_string”的声明
      F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerHandler.cpp(1033,2):
      不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(541,7):
      参见“std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t>>”的声明
  
F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerHandler.cpp(2312,3): warning C4840: 将类 "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t>>" 作为可变参数函数的参数的不可移植用法
  (编译源文件“/PlayerHandler.cpp”)
      F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerHandler.cpp(2312,3):
      "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t>>::basic_string" 不常用
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(716,5):
      参见“std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t>>::basic_string”的声明
      F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerHandler.cpp(2312,3):
      不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(541,7):
      参见“std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t>>”的声明
  
F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerHandler.cpp(2342,3): warning C4840: 将类 "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t>>" 作为可变参数函数的参数的不可移植用法
  (编译源文件“/PlayerHandler.cpp”)
      F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerHandler.cpp(2342,3):
      "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t>>::basic_string" 不常用
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(716,5):
      参见“std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t>>::basic_string”的声明
      F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerHandler.cpp(2342,3):
      不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(541,7):
      参见“std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t>>”的声明
  
  PlayerServantHandler.cpp
  PlayerStateJoinExecution.cpp
  PlayerStateWait.cpp
  PlayerStateZone.cpp
  PlayerSearchCharAction.cpp
  DungeonMatchHandler.cpp
  DungeonMatchManager.cpp
  DungeonMatchRoom.cpp
  PlayerRankingAction.cpp
  RankingHandler.cpp
  RankingManager.cpp
  EntityManager.cpp
  PlayerLobbyAction.cpp
  PlayerStateLobby.cpp
  SyncSystem.cpp
  WOPSEvent.cpp
F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerStateLobby.cpp(226,3): error C2220: 以下警告被视为错误
  (编译源文件“/PlayerStateLobby.cpp”)
  
F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerStateLobby.cpp(226,3): warning C4840: 将类 "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t>>" 作为可变参数函数的参数的不可移植用法
  (编译源文件“/PlayerStateLobby.cpp”)
      F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerStateLobby.cpp(226,3):
      "std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t>>::basic_string" 不常用
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(716,5):
      参见“std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t>>::basic_string”的声明
      F:\Release_Branch\Server\Development\Frontend\WorldServer\PlayerStateLobby.cpp(226,3):
      不会调用构造函数和析构函数；该类的位副本将作为参数进行传递
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xstring(541,7):
      参见“std::basic_string<wchar_t,std::char_traits<wchar_t>,std::allocator<wchar_t>>”的声明
  
  WOPSGoldenTimeBuffEvent.cpp
  WOPSGoldenTimeGiftEvent.cpp
  WOPSEventNotifier.cpp
  WOPSHandler.cpp
  WOPSEventManager.cpp
  WorldEntityFactoryImpl.cpp
  WorldHandler.cpp
  WorldRunner.cpp
  WorldServer.cpp
  Unknown compiler version - please run the configure tests and report the results
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): error C2220: 以下警告被视为错误
  (编译源文件“/WorldServer.cpp”)
  
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244: “初始化”: 从“_Ty”转换到“_Ty2”，可能丢失数据
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244:         with
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244:         [
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244:             _Ty=unsigned int
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244:         ]
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244:         and
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244:         [
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244:             _Ty2=Byte
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54): warning C4244:         ]
  (编译源文件“/WorldServer.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\utility(274,54):
      模板实例化上下文(最早的实例化上下文)为
          F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldServer.cpp(1474,31):
          查看对正在编译的函数 模板 实例化“std::pair<std::_Tree_iterator<std::_Tree_val<std::_Tree_simple_types<std::pair<const UInt32,Byte>>>>,bool> std::_Tree<std::_Tmap_traits<_Kty,_Ty,_Pr,_Alloc,false>>::emplace<_Ty1&,UInt32>(_Ty1 &,UInt32 &&)”的引用
          with
          [
              _Kty=UInt32,
              _Ty=Byte,
              _Pr=std::less<UInt32>,
              _Alloc=std::allocator<std::pair<const UInt32,Byte>>,
              _Ty1=const mu2::PortalUseCountGroupType::Enum
          ]
              F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldServer.cpp(1474,38):
              请参阅 "mu2::WorldServer::RequestToDbPortalGroupRecoveryEnterCount" 中对 "std::_Tree<std::_Tmap_traits<_Kty,_Ty,_Pr,_Alloc,false>>::emplace" 的第一个引用
          with
          [
              _Kty=UInt32,
              _Ty=Byte,
              _Pr=std::less<UInt32>,
              _Alloc=std::allocator<std::pair<const UInt32,Byte>>
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree(1048,30):
          查看对正在编译的函数 模板 实例化“std::pair<std::_Tree_node<std::pair<const UInt32,Byte>,std::_Default_allocator_traits<_Alloc>::void_pointer> *,bool> std::_Tree<std::_Tmap_traits<_Kty,_Ty,_Pr,_Alloc,false>>::_Emplace<_Ty1&,unsigned int>(_Ty1 &,unsigned int &&)”的引用
          with
          [
              _Alloc=std::allocator<std::pair<const UInt32,Byte>>,
              _Kty=UInt32,
              _Ty=Byte,
              _Pr=std::less<UInt32>,
              _Ty1=const mu2::PortalUseCountGroupType::Enum
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree(1026,46):
          查看对正在编译的函数 模板 实例化“std::_Tree_temp_node<std::allocator<std::_Tree_node<std::pair<const UInt32,Byte>,std::_Default_allocator_traits<_Alloc>::void_pointer>>>::_Tree_temp_node<_Ty1&,_Ty>(_Alnode &,std::_Tree_node<std::pair<const UInt32,Byte>,std::_Default_allocator_traits<_Alloc>::void_pointer> *,_Ty1 &,_Ty &&)”的引用
          with
          [
              _Alloc=std::allocator<std::pair<const UInt32,Byte>>,
              _Ty1=const mu2::PortalUseCountGroupType::Enum,
              _Ty=unsigned int,
              _Alnode=std::allocator<std::_Tree_node<std::pair<const UInt32,Byte>,std::_Default_allocator_traits<std::allocator<std::pair<const UInt32,Byte>>>::void_pointer>>
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xtree(830,25):
          查看对正在编译的函数 模板 实例化“void std::_Default_allocator_traits<_Alloc>::construct<_Ty,_Ty1&,unsigned int>(_Alloc &,_Objty *const ,_Ty1 &,unsigned int &&)”的引用
          with
          [
              _Alloc=std::allocator<std::_Tree_node<std::pair<const UInt32,Byte>,std::_Default_allocator_traits<std::allocator<std::pair<const UInt32,Byte>>>::void_pointer>>,
              _Ty=std::pair<const UInt32,Byte>,
              _Ty1=const mu2::PortalUseCountGroupType::Enum,
              _Objty=std::pair<const UInt32,Byte>
          ]
          C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\xmemory(732,82):
          查看对正在编译的函数 模板 实例化“std::pair<const UInt32,Byte>::pair<_Ty1&,_Ty,0>(_Other1,_Other2 &&) noexcept”的引用
          with
          [
              _Ty1=const mu2::PortalUseCountGroupType::Enum,
              _Ty=unsigned int,
              _Other1=const mu2::PortalUseCountGroupType::Enum &,
              _Other2=unsigned int
          ]
  
