#pragma once

// 너무 많아서 허용
#pragma warning(disable: 4100)
// 조건식이 상수입니다.
#pragma warning(disable: 4127)

//! 
#define NOT_SELECTED_TARGET_ID 0

#pragma message("[ZoneServer System Include Start]")
#include "targetver.h"

#include <winsock2.h>
#include <windows.h>

#undef min
#undef max

#include <conio.h>
#include <Stdio.h>
#include <tchar.h>
#include <Stdlib.h>
#include <Strsafe.h>
#include <iostream>
#include <Numeric>
#include <deque>
#include <unordered_map>
#include <map>
#include <array>
#include <regex>
#include <vector>
#include <fstream>
#include <String>
#include <random>
#include <limits>
#include <atomic>
#include <concrt.h>
#include <time.h>
#include <chrono>
#include <Bitset>
#include <memory>
#include <iterator>
#include <ppl.h>
#include <algorithm>
#include <NEW>
#include <unordered_set>
#include <concurrent_unordered_map.h>
#include <thread>

extern "C" {
#include <lua.h>
#include <lauxlib.h>
#include <lualib.h>
};

#include <lua.hpp>
#pragma message("[ZoneServer System Include End]")
#include <Vendor/libcapt/source/captGenerator.h>
#include <Vendor/libcapt/source/captFontFile.h>

//====================================================================
// Framework
//====================================================================
#pragma message("[ZoneServer Framework Include Start]")
#include <Framework/Core/Mu2Profiler.h>
#include <Framework/Core/Assertion.h>
#include <Framework/Core/DefineTypes.h>
#include <Framework/Core/FunctionalTest.h>
#include <Framework/Core/Logger.h>
#include <Framework/Core/Pool.h>
#include <Framework/Core/SimpleTimer.h>
#include <Framework/Core/FineTick.h>
#include <Framework/Core/DateTime.h>
#include <Framework/Core/Atomic.h>
#include <Framework/Core/Lock.h>
#include <Framework/Core/Prerequisites.h>
#include <Framework/Core/StandardNewDeleteForms.h>
#include <Framework/Core/StringUtil.h>
#include <Framework/Core/Singleton.h>
#include <Framework/Core/MemoryAllocatorConfig.h>
#include <Framework/Core/Profiler.h>
#include <Framework/Core/Thread.h>
#include <Framework/Core/Noncopyable.h>
#include <Framework/Core/CrashHandler.h>
#include <Framework/Core/Counter.h>
#include <Framework/Core/DateTimeCorrector.h>
#include <Framework/Core/ModifiableValue.h>
#include <Framework/Core/Delegate.hpp>
#include <Framework/Math/Vector3.h>
#include <Framework/Math/Vector2.h>
#include <Framework/Math/Random.h>
#include <Framework/Math/Capsule.h>
#include <Framework/Math/myMath.h>
#include <Framework/Math/Capsule.h>
#include <Framework/Math/LineSegment3.h>
#include <Framework/Math/Sphere.h>
#include <Framework/Math/Matrix44.h>
#include <Framework/Math/Box2D/Box2D.h>
#include <Framework/Math/AABB.h>
#include <Framework/Math/Rect.h>
#include <Framework/Entity/TypeDef.h>
#include <Framework/Entity/EntityFactory.h>
#include <Framework/Entity/EntityFactoryImpl.h>
#include <Framework/Entity/Osm.h>
#include <Framework/Entity/State.h>
#include <Framework/Entity/Entity.h>
#include <Framework/Entity/Attribute.h>
#include <Framework/Entity/View.h>
#include <Framework/Entity/Action.h>
#include <Framework/Net/Common/Event.h>
#include <Framework/Net/Common/PacketStream.h>
#include <Framework/Net/Common/ModifierSkipper.h>
#include <Framework/Net/Common/EventCallBack.h>
#include <Framework/Net/Common/Dispatcher.h>
#include <Framework/Net/Server/Server.h>
#include <Framework/RealWorld/TraverseLogicData.h>
#include <Framework/RealWorld/Interface/IAgent.h>
#include <Framework/RealWorld/Interface/ISector.h>
#include <Framework/RealWorld/Interface/IRealworld.h>
#include <Framework/RealWorld/Navigation/RealWorldNavi.h>

#pragma message("[ZoneServer Framework Include End]")
//====================================================================
// Shared
//====================================================================
#pragma message("[ZoneServer Shared Include Start]")
#include <Shared/Config/Config.h>
#include <Shared/Protocol/PreProcessorDefine.h>
#include <Shared/Protocol/ProtocolRanges.h>
#include <Shared/TypeDef.h>
#include <Shared/Version.h>
#include <Shared/ResetHelper.h>
#include <Shared/LogCode.h>	
#include <Shared/LogMessage/LogDbSubInfo.h>
#include <Shared/Extensions/StringExtension.h>
#include <Shared/ItemGrowExpCalculator.h>


//====================================================================
// Shared 테이블 스크립트 
//====================================================================

#include <Shared/AsyncJob.h>
#include <Shared/ItemSelector.h>
#include <Shared/SystemMessage.h>
#include <Shared/Scripts/CsvReader.h>
#include <Shared/Scripts/CsvWriter.h>
#include <Shared/Scripts/XmlReader.h>
#include <Shared/Scripts/Exception.h>
#include <Shared/Scripts/BaseTable.h>
#include <Shared/Scripts/ScriptEnum.h>
#include <Shared/Scripts/EffectKeyToken.h>
#include <Shared/Scripts/Xml/tinyxml.h>
#include <Shared/Scripts/ObjectScript.h>
#include <Shared/Scripts/GlobalScript.h>
#include <Shared/Scripts/SkillInfoScript.h>
#include <Shared/Scripts/NpcInfoScript.h>
#include <Shared/Scripts/KnightsNpcScript.h>
#include <Shared/Scripts/WorldInfoScript.h>
#include <Shared/Scripts/WorldScript.h>
#include <Shared/Scripts/AccountLevelScript.h>
#include <Shared/Scripts/PvpRankScript.h>
#include <Shared/Scripts/BattlePvpAbilityCorrectScript.h>
#include <Shared/Scripts/ItemExtractScript.h>
#include <Shared/Scripts/ItemCustomizingCostScript.h>
#include <Shared/Scripts/KillComboScript.h>
#include <Shared/Scripts/PublicParameters.h>
#include <Shared/Scripts/TutorialScript.h>
#include <Shared/Scripts/ItemInfoScript.h>
#include <Shared/Scripts/MakingFormulaScript.h>
#include <Shared/Scripts/StorageInfoScript.h>
#include <Shared/Scripts/SkillChangerScript.h>
#include <Shared/Scripts/SpecialSkillScript.h>
#include <Shared/Scripts/AchievementScript.h>
#include <Shared/Scripts/MailIndexScript.h>
#include <Shared/Scripts/AdvScript.h>
#include <Shared/Scripts/MissionHeroScript.h>
#include <Shared/Scripts/RankingInfoScript.h>
#include <Shared/Scripts/SellScript.h>
#include <Shared/Scripts/SetItemDivisionScript.h>
#include <Shared/Scripts/NewCharacterQuickSlotInfoScript.h>
#include <Shared/Scripts/ItemOptionScript.h>
#include <Shared/Scripts/ScriptDefineForServer.h>
#include <Shared/Scripts/MissionMapMatchingScript.h>
#include <Shared/Scripts/ItemRandomOptionScript.h>
#include <Shared/Scripts/ItemUpgradeScript.h>
#include <Shared/Scripts/QuestScript.h>
#include <Shared/Scripts/ConstantsEffectKeyLogic.h>
#include <Shared/Scripts/ScriptLogMessage.h>
#include <Shared/Scripts/ZoneInstanceScript.h>
#include <Shared/Scripts/ItemConfigScript.h>
#include <Shared/Scripts/ItemEnchantScript.h>
#include <Shared/Scripts/ZoneDropControlScript.h>
#include <Shared/Scripts/PortalInfoScript.h>
#include <Shared/Scripts/MissionHeroScript.h>
#include <Shared/Scripts/EndlessTowerScript.h>
#include <Shared/Scripts/BloodCastleScript.h>
#include <Shared/Scripts/ContinentScript.h>
#include <Shared/Scripts/SetItemAbilityScript.h>
#include <Shared/Scripts/NavigationLabPortListScript.h>
#include <Shared/Scripts/KnightageInfoXmlScript.h>
#include <Shared/Scripts/KnightageAltarInfoScript.h>
#include <Shared/Scripts/KnightageLevelInfoScript.h>
#include <Shared/Scripts/ConstantsEffectKeyLogic.h>
#include <Shared/Scripts/ItemExchangeScript.h>
#include <Shared/Scripts/LuckyMonsterScript.h>
#include <Shared/Scripts/AbilityMaxScript.h>
#include <Shared/Scripts/SeasonScript.h>
#include <Shared/Scripts/NetherWorldScript.h>
#include <Shared/Scripts/PVPMissionMapScript.h>
#include <Shared/Scripts/PetScript.h>
#include <Shared/Scripts/ItemOptionChangerScript.h>
#include <Shared/Scripts/ItemOpenScript.h>
#include <Shared/Scripts/QuestMissionDecoratorScript.h>
#include <Shared/Scripts/ArtifactScript.h>
#include <Shared/Scripts/PrivateStorageScript.h>
#include <Shared/Scripts/DominionScript.h>
#include <Shared/Scripts/TalismanScript.h>
#include <Shared/Scripts/EmotionBoxScript.h>
#include <Shared/Scripts/MarbleScript.h>
#include <Shared/Scripts/GameEvent.h>
#include <Shared/Scripts/ChargingSkillInfoScript.h>
#include <Shared/Scripts/GuideSystemScript.h>
#include <Shared/Scripts/LoginCheckScript.h>
#include <Shared/Scripts/ChaosCastleScript.h>
#include <Shared/Scripts/DropConditionScript.h>
#include <Shared/Scripts/ItemRuneScript.h>
#ifdef __patch_PVP_Field_Renewal_by_eunseok_2019_02_22
#include <Shared/Scripts/MissionMapScheduleScript.h>
#endif 

#ifdef TUROTIAL_SKIP_by_cheolhoon_180605
#include <Shared/Scripts/TutorialSkipScript.h>
#endif

#ifdef __Reincarnation__jason_180628__
#include <Shared/Scripts/KeyValue.h>
#include <Shared/Scripts/ReincarnationScript.h>
#endif

#ifdef __Patch_EquipmentItem_Repair_Cost_by_jaekwan_180904
#include <Shared/Scripts/ItemRepairCostScript.h>
#endif

#ifdef __Renewal_Additional_Grade_Transfer_by_kangms_181001
#include <Shared/Scripts/AdditionalGradeTransferScript.h>
#endif//__Renewal_Additional_Grade_Transfer_by_kangms_181001

#ifdef __Patch_Talismanbox_Ticket_Reward_System_by_ch_181224
#include <Shared/Scripts/TalismanBoxTicketRewardScript.h>
#endif

#ifdef __Patch_Legend_Of_Bingo_by_jaekwan_20181126
#include <Shared/Scripts/LegendOfBingoScript.h>
#endif

#ifdef __Patch_Knightage_Renewal_by_eunseok_20190128
#include <Shared/Scripts/KnightageMonsterRaidScript.h>
#endif 

#ifdef __Patch_Add_Quest_Schedule_by_ch_20190313
#include <Shared/Scripts/QuestScheduleScript.h>
#endif // __Patch_Add_Quest_Schedule_by_ch_20190313

#ifdef __Patch_SetItem_Renewal_by_kangms_2019_4_10
#include <Shared/Scripts/SetCardScript.h>
#endif//__Patch_SetItem_Renewal_by_kangms_2019_4_10

#ifdef __Patch_Mercler_Secret_Shop_by_ch_2019_04_24
#include <Shared/Scripts/MerclerSecretShopSellScript.h>
#include <Shared/Scripts/MerclerSecretShopBonusScript.h>
#endif // __Patch_Mercler_Secret_Shop_by_ch_2019_04_24

#ifdef __Patch_Teacher_And_Student_System_by_ch_20190604
#include <Shared/Scripts/TeacherAndStudentScript.h>
#endif // __Patch_Teacher_And_Student_System_by_ch_20190604

//====================================================================
// Shared protocol
//====================================================================


#include <Shared/Protocol/Common.h>
#include <Shared/Protocol/Mu2Limits.h>
#include <Shared/Protocol/ErrorCodes.h>
#include <Shared/Protocol/EntityTypes.h>
#include <Shared/Protocol/ObjectType.h>
#include <Shared/Protocol/ExecutionZoneId.h>
#include <Shared/Protocol/WorldDataTypes.h>
#include <Shared/Protocol/EventWShop.h>
#include <Shared/Protocol/DirectRelayEventRegist.h>

#include <Shared/Protocol/Common/ConstantsItem.h>
#include <Shared/Protocol/Common/ConstantsEquip.h>
#include <Shared/Protocol/Common/ConstantsLobby.h>
#include <Shared/Protocol/Common/ConstantsDungeon.h>
#include <Shared/Protocol/COmmon/ConstantsFunction.h>
#include <Shared/Protocol/Common/StructuresKnightage.h>
#include <Shared/Protocol/Common/ConstantsExchange.h>
#include <Shared/Protocol/Common/StructuresWShop.h>
#include <Shared/Protocol/Common/StructuresItem.h>
#include <shared/Protocol/Common/StructuresPetInfo.h>
#include <Shared/Protocol/Common/StructuresGameEvent.h>
#include <Shared/Protocol/Common/StructuresKnightage.h>
#include <Shared/Protocol/Common/StructuresExchange.h>
#include <Shared/Protocol/Common/StructuresMarble.h>

#ifdef __Patch_SetItem_Renewal_by_kangms_2019_4_10
#include <Shared/Protocol/Common/StructuresSetCard.h>
#endif//__Patch_SetItem_Renewal_by_kangms_2019_4_10

#include <Shared/Protocol/ProtocolItem.h>
#include <Shared/Protocol/ProtocolGame.h>
#include <Shared/Protocol/ProtocolComm.h>
#include <Shared/Protocol/ProtocolLobby.h>
#include <Shared/Protocol/ProtocolDaily.h>
#include <Shared/Protocol/ProtocolPet.h>
#include <Shared/Protocol/ProtocolMissionmap.h>
#include <Shared/Protocol/ProtocolKnightage.h>
#include <Shared/Protocol/ProtocolMail.h>
#include <Shared/Protocol/ProtocolQuest.h>
#include <Shared/Protocol/ProtocolQuickslot.h>
#include <Shared/Protocol/ProtocolChat.h>
#include <Shared/Protocol/ProtocolParty.h>
#include <Shared/Protocol/ProtocolArtifact.h>
#include <Shared/Protocol/ProtocolWShop.h>
#include <Shared/Protocol/ProtocolItemMaking.h>
#include <Shared/Protocol/ProtocolAchievement.h>
#include <Shared/Protocol/ProtocolBattle.h>
#include <Shared/Protocol/ProtocolRaid.h>
#include <Shared/Protocol/ProtocolFieldPoint.h>
#include <Shared/Protocol/ProtocolTalisman.h>
#include <Shared/Protocol/ProtocolMarble.h>
#include <Shared/Protocol/ProtocolNoticePopUp.h>

#ifdef __Patch_SetItem_Renewal_by_kangms_2019_4_10
#include <Shared/Protocol/ProtocolSetCard.h>
#endif//__Patch_SetItem_Renewal_by_kangms_2019_4_10

#pragma message("[ZoneServer Shared Include End]")
//====================================================================
// Mu2Common
//====================================================================

#pragma message("[ZoneServer Common Include Start]")
#include <Mu2Common/ServerCommon.h>
#include <Mu2Common/ServerCommonMacro.h>
#include <Mu2Common/LocalConfig.h>
#include <Mu2Common/MonitorServerCommon.h>
#include <Mu2Common/ScriptDataSetter.h>

#include <Mu2Common/Protocol/EventLoopbackBase.h>
#include <Mu2Common/Protocol/EventApp.h>
#include <Mu2Common/Protocol/ConstantsItem.h>
#include <Mu2Common/Protocol/ProtocolCenter.h>
#include <Mu2Common/Protocol/ProtocolServerCommon.h>
#include <Mu2Common/Protocol/ProtocolDb.h>
#include <Mu2Common/Protocol/ProtocolApp.h>
#include <Mu2Common/Protocol/ProtocolBridge.h>
#include <Mu2Common/Protocol/ProtocolLog.h>
#include <Mu2Common/Protocol/ProtocolCharJoinZoneLoad.h>

#pragma message("[ZoneServer Common Include End]")
//====================================================================
// single instance macro 
//====================================================================

#define SERVER							(*((ZoneServer*)Server::Instance))
#define theZoneHandler					(SERVER.GetZoneHandler())
#define theRealworld					(SERVER.GetRealWorldNavi())
#define theZoneServerConfiguration		(ZoneServerConfiguration::GetInstance())
#define theSpawnSystem					(SpawnSystem::GetInstance())
#define theDropSystem					(DropSystem::GetInstance())
#define theItemRewardSystem				(ItemRewardSystem::GetInstance())
#define theReviveSystem					(ReviveSystem::GetInstance())
#define theGMCommandSystem				(GMCommandSystem::GetInstance())
#define thePortalSystem					(PortalSystem::GetInstance())
#define theInstanceDungeonSystem		(InstanceDungeonSystem::GetInstance())
#define theLogicEffectSystem			(LogicEffectSystem::GetInstance())
#define theStatueSystem					(StatueSystem::GetInstance())
#define theQuestSystem					(QuestSystem::GetInstance())
#define theLuckyMonsterSystem			(LuckyMonsterSpawnSystem::GetInstance())
#define theNpcConditionManager			(NpcConditionManager::GetInstance())
#define theFieldKnightageManager		(FieldKnightageManager::GetInstance())
#define theNpcStateFactory				(NpcStateFactory::GetInstance())
#define theLogicEffectProcessor			(LogicEffectProcessor::GetInstance())
#define theEntityCollector				(EntityCollector::GetInstance())
#define theZoneCastReporter				(ZoneCastReporter::GetInstance())
#define theSectorGridManager			(SectorGridManager::GetInstance())
#define theEventSystem					(GameEventSystem::GetInstance())
#define theSeasonSystem					(SeasonSystem::GetInstance())
#define theCaptureSystem				(CaptureSystem::GetInstance())
#define theMarbleSystem					(MarbleSystem::GetInstance())
#define theAchievementChecker			(AchievementConditionChecker::GetInstance())
#define theItemRuneSystem				(ItemRuneSystem::GetInstance())
#define theNotifierGameContents			(NotifierGameContents::GetInstance())

#ifdef FOR_GLOBAL_JAPAN 
#define theContentsDeactivator          (*SERVER.m_ContentsDeactivator)
#endif

#define theMarbleSystem					(MarbleSystem::GetInstance())
#ifdef __Patch_Legend_Of_Bingo_by_jaekwan_20181126
#define theBingoManager					(BingoManagerSystem::GetInstance())
#endif // __Patch_Legend_Of_Bingo_by_jaekwan_20181126

//====================================================================
// handler macro
//====================================================================

#define SetHandler(listener,EVENT,onFn,logging)								\
	SERVER.SetEventListener(EVENT::eventProtocol(), listener, logging);		\
	m_dispatcher.Register(EVENT::eventProtocol(), &onFn);

#define SetHandlerLoopback(listener,EVENT,onFn,logging)						\
	m_dispatcher.Register(EVENT::eventProtocol(), &onFn);

#define SetHandler4Sector(listener,EVENT,onFn)								\
	SetHandler(listener, EVENT, onFn, false);								\
	m_sectorEvent.insert(EVENT::eventProtocol());

#define SetHandler4SectorLoopback(listener,EVENT,onFn)						\
	m_dispatcher.Register(EVENT::eventProtocol(), &onFn);					\
	m_sectorEvent.insert(EVENT::eventProtocol());



//====================================================================
// etc
//====================================================================

template<class T>
void deallocate(T* ptr, UInt32 reason)
{
	delete ptr;
}

#define MAX( a, b ) (((a) > (b)) ? (a) : (b))
#define MIN( a, b ) (((a) < (b)) ? (a) : (b))


#define CRITICAL_LOG
//============================================================================
//  Release, Debug 적용
//============================================================================
#define WORLD_CLUSTER_LOG	0
#define _DEBUG_LOG	

//----------------------------------------------------------------------------

//============================================================================
#if 1
//----------------------------------------------------------------------------
//----------------------------------------------------------------------------
#ifdef _DEBUG
//----------------------------------------------------------------------------
//#define USE_NED_MALLOC
//#define USE_NED_MALLOC_DEBUG
//#define MEASURE_PERFORMANCE
//#define USE_CHECK_TEMPLATE

//----------------------------------------------------------------------------
#endif //! _DEBUG
//============================================================================


//============================================================================
#elif defined( __DV__ )
//----------------------------------------------------------------------------
//! Release

//----------------------------------------------------------------------------
#ifdef _DEBUG
//----------------------------------------------------------------------------
//! 
#endif //! _DEBUG
//============================================================================
#endif 


template <typename T>
struct IntegerHasher
{
	size_t operator () (const T& value) const
	{
		return static_cast<size_t>(value);
	}
};

template <typename T>
struct PointerHasher
{
	size_t operator () (const T& value) const
	{
		return reinterpret_cast<size_t>(value);
	}
};



//====================================================================
// Zone
//====================================================================

#include <Backend/Zone/EventSetter.h>
#include <Backend/Zone/Configure.h>
#include <Backend/Zone/Common.h>

#include <Backend/Zone/AIScriptLogic/LuaException.h>
#include <Backend/Zone/AIScriptLogic/LuaUtil.h>
#include <Backend/Zone/AIScriptLogic/SectorLuaScript.h>


#include <Backend/Zone/World/Script/ZoneLevelScriptTable.h>
#include <Backend/Zone/World/Script/ZoneLevelScript.h>
#include <Backend/Zone/World/Script/DynamicTagvolumeScript.h>
#include <Backend/Zone/World/Script/IRTouchVolumeScript.h>
#include <Backend/Zone/World/Script/VolumeScript.h>
#include <Backend/Zone/World/Script/VolumeSpawnScript.h>
#include <Backend/Zone/World/Script/ObjectVolumeScript.h>
#include <Backend/Zone/World/Script/PathVolumeScript.h>
#include <Backend/zone/World/Script/QuestVolumeScript.h>
#include <Backend/Zone/World/Script/QuestHelpVolumeScript.h>
#ifdef __Patch_WebOfGod_by_jason_20181213
#include <Shared/Scripts/WebOfGodScript.h>
#endif

#include <Backend/Zone/Transaction/MoneyProcedure.h>
#include <Backend/Zone/Transaction/DBTransaction.h>
#include <Backend/Zone/Transaction/ItemProcedure.h>

#include <Backend/Zone/Element/Item/EquipItemAbilityModifier.h>
#include <Backend/Zone/Element/Item/Item.h>
#include <Backend/Zone/Element/Item/UseItem.h>
#include <Backend/Zone/Element/Item/InvenSlot.h>
#include <Backend/Zone/Element/Item/ItemBag.h>
#include <Backend/Zone/Element/Item/ItemFactory.h>
#include <Backend/Zone/Element/Item/TradeItem.h>
#include <Backend/Zone/Element/Item/ItemAmplifaction.h>
#include <Backend/Zone/Element/Item/UseItemLimit.h>
#include <Backend/Zone/Element/Item/ItemRune.h>
#include <Backend/Zone/Element/Ability/AbilityCalculator.h>
#include <Backend/Zone/Element/Ability/NpcAbilityCalculator.h>
#include <Backend/Zone/Element/Mail/Mail.h>
#include <Backend/Zone/Element/Mail/MailBox.h>
#include <Backend/Zone/Element/Pet/Pet.h>
#include <Backend/Zone/Element/Pet/PetManager.h>
#include <Backend/Zone/Element/Skill/Skill.h>
#include <Backend/Zone/Element/Skill/SkillControl.h>
#ifdef __Reincarnation__jason_180628__
#else
#include <Backend/Zone/Element/Skill/SpecialBlock.h>
#endif
#include <Backend/Zone/Element/Skill/TimeCorrector.h>
#include <Backend/Zone/Element/Skill/SkillLinker.h>
#include <Backend/Zone/Element/Skill/SkillCoolLineManager.h>
#include <Backend/Zone/Element/Skill/SkillCastChecker.h>
#include <Backend/Zone/Element/Skill/PassiveCondition.h>
#include <Backend/Zone/Element/Skill/Passivity.h>
#include <Backend/Zone/Element/Skill/Passive/PassiveCondAbilityUpdate.h>
#include <Backend/Zone/Element/Skill/Passive/PassiveCondCastSkill.h>
#include <Backend/Zone/Element/Skill/Passive/PassiveCondCheckRangeEnemy.h>
#include <Backend/Zone/Element/Skill/Passive/PassiveCondGetProperty.h>
#include <Backend/Zone/Element/Skill/Passive/PassiveCondHitMe.h>
#include <Backend/Zone/Element/Skill/Passive/PassiveCondHitTarget.h>
#include <Backend/Zone/Element/Skill/Passive/PassiveCondLevelUp.h>
#include <Backend/Zone/Element/Skill/Passive/PassiveCondNonBattleState.h>
#include <Backend/Zone/Element/Skill/Passive/PassiveCondQuestComplete.h>
#include <Backend/Zone/Element/Skill/Passive/PassiveCondRiding.h>
#include <Backend/Zone/Element/Skill/Passive/PassiveCondShieldBlock.h>
#include <Backend/Zone/Element/Skill/Passive/PassiveCondHasBuff.h>

#ifdef NEW_BLACKPHANTOM_SKILL_KEYLOGIC_by_kangms_180618
#include <Backend/Zone/Element/Skill/Passive/PassiveCondEssenceChange.h>
#endif//NEW_BLACKPHANTOM_SKILL_KEYLOGIC_by_kangms_180618

#include <Backend/Zone/Element/Skill/SkillCastChecker.h>
#include <Backend/Zone/Element/Achievement/Achievement.h>
#include <Backend/Zone/Element/Achievement/AchievementEvent.h>
#include <Backend/Zone/Element/Achievement/LimitedBannerEvent.h>
#include <Backend/Zone/Element/Survey/Survey.h>
#include <Backend/Zone/Element/Survey/SurveyEvent.h>
#include <Backend/Zone/Element/Season/SeasonMissionEvent.h>
#include <Backend/Zone/Element/Season/SeasonMission.h>
#include <Backend/Zone/Element/Quest/CheckMissionCompleteChecker.h>

#include <Backend/Zone/Transaction/ItemTransaction.h>
#include <Backend/Zone/Transaction/ItemTransaction/ItemGambleTransaction.h>
#include <Backend/Zone/Transaction/ItemTransaction/ItemSkillRuneTransaction.h>
#include <Backend/Zone/Transaction/ItemTransaction/ItemChangeSkillRuneTransaction.h>
#include <Backend/Zone/Transaction/ItemTransaction/ItemRecoveryEntranceTransaction.h>
#include <Backend/Zone/Transaction/ItemTransaction/ItemPortalStoneTransaction.h>
#include <Backend/Zone/Transaction/ItemTransaction/ItemPetTransaction.h>
#include <Backend/Zone/Transaction/ItemTransaction/ItemMegaphoneTransaction.h>
#include <Backend/Zone/Transaction/ItemTransaction/ItemEnchantTransaction.h>
#include <Backend/Zone/Transaction/ItemTransaction/ItemNameChangeTransaction.h>
#include <Backend/Zone/Transaction/ItemTransaction/ItemUseTransaction.h>
#include <Backend/Zone/Transaction/ItemTransaction/ItemMembershipTransaction.h>
#include <Backend/Zone/Transaction/ItemTransaction/ItemGameEventTranscation.h>
#ifdef __Patch_Legend_Of_Bingo_by_jaekwan_20181126
#include <Backend/Zone/Transaction/ItemTransaction/BingoSlotExchangeTransaction.h>
#endif // __Patch_Legend_Of_Bingo_by_jaekwan_20181126


#include <Backend/Zone/Attribute/AttributeDroppedItem.h>
#include <Backend/Zone/Attribute/AttributeEffectVolume.h>
#include <Backend/Zone/Attribute/AttributeEntity.h>
#include <Backend/Zone/Attribute/AttributeIRSpawn.h>
#include <Backend/Zone/Attribute/AttributeNpc.h>
#include <Backend/Zone/Attribute/AttributeNpcScript.h>
#include <Backend/Zone/Attribute/AttributeParty.h>
#include <Backend/Zone/Attribute/AttributePlayer.h>
#include <Backend/Zone/Attribute/AttributePosition.h>
#include <Backend/Zone/Attribute/AttributeSkill.h>
#include <Backend/Zone/Attribute/AttributePortal.h>

#include <Backend/Zone/Attribute/AttributeVolumeSpawn.h>

#include <Backend/Zone/View/ViewDroppedItem.h>
#include <Backend/Zone/View/ViewEntity.h>
#include <Backend/Zone/View/ViewShop.h>

#include <Backend/Zone/Action/ActionCommon/Action4Zone.h>
#include <Backend/Zone/Action/ActionCommon/ActionAbility.h>
#include <Backend/Zone/Action/ActionCommon/ActionCognition.h>
#include <Backend/Zone/Action/ActionCommon/ActionSkillControl.h>
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerAbility.h>
#include <Backend/Zone/Action/ActionNpc/ActionNpcAbility.h>
#include <Backend/Zone/Action/ActionDroppedItem/ActionDroppedCognition.h>
#ifdef __Renewal_Dungeon_Entrance_Cost_by_jason_20190114
#include <Backend/Zone/Action/SectorItemUseChecker.h>
#endif
#ifdef __Patch_Lua_InstanceGroup_Param_by_jason_20190109
#include <Backend/Zone/World/InstanceSectorGroup.h>
#endif
#include <Backend/Zone/World/MapEventScript.h>
#include <Backend/Zone/World/SectorSystem/SharedQuestSystem.h>
#include <Backend/Zone/World/SectorSystem/FieldPointSector.h>
#include <Backend/Zone/World/GameConfig/ZoneGameConfig.h>
#include <Backend/Zone/World/PerformanceLodDirector.h>
#include <Backend/Zone/World/SectorSequencer.h>
#include <Backend/Zone/World/Gcell.h>
#include <Backend/Zone/World/Grid.h>
#include <Backend/Zone/World/Sector.h>
#include <Backend/Zone/SectorProcessor.h>
#include <Backend/Zone/World/MissionMap/MissionMapReward.h>
#include <Backend/Zone/World/SectorGridManager.h>


#include <Backend/Zone/SectorCollidedHelper.h>

#include <Backend/Zone/Action/ActionSectorController.h>
#include <Backend/Zone/Action/ActionSector.h>
#include <Backend/Zone/Action/ActionPetManage.h>

#include <Backend/Zone/LoopbackEvent/EventLoopback.h>

#include <Backend/Zone/System/LogicEffectSystem/LogicEffect.h>

#ifdef	__Patch_Limited_Banner_Shop_robinhwp
#include <Backend/Zone/Action/ActionPlayer/ActionPlayerLimitedBannerGoods.h>
#endif	__Patch_Limited_Banner_Shop_robinhwp

#define TOURNAMENT

#ifdef _DEBUG
//#include <vld.h>
#endif
