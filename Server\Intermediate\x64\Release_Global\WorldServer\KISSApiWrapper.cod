; Listing generated by Microsoft (R) Optimizing Compiler Version 19.44.35209.0 

include listing.inc

INCLUDELIB LIBCMT
INCLUDELIB OLDNAMES

PUBLIC	?__empty_global_delete@@YAXPEAX@Z		; __empty_global_delete
PUBLIC	?__empty_global_delete@@YAXPEAX_K@Z		; __empty_global_delete
PUBLIC	__local_stdio_printf_options
PUBLIC	?StringCchCopyA@@YAJPEAD_KPEBD@Z		; StringCchCopyA
PUBLIC	?StringCchCopyW@@YAJPEA_W_KPEB_W@Z		; StringCchCopyW
PUBLIC	?StringCchPrintfW@@YAJPEA_W_KPEB_WZZ		; StringCchPrintfW
PUBLIC	??_G?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@UEAAPEAXI@Z ; mu2::ISingleton<mu2::CKISSApiWrapper>::`scalar deleting destructor'
PUBL<PERSON>	??1CKISSApiWrapper@mu2@@UEAA@XZ			; mu2::CKISSApiWrapper::~CKISSApiWrapper
PUBLIC	?Init@CKISSApiWrapper@mu2@@UEAA_NPEAX@Z		; mu2::CKISSApiWrapper::Init
PUBLIC	?UnInit@CKISSApiWrapper@mu2@@UEAA_NXZ		; mu2::CKISSApiWrapper::UnInit
PUBLIC	?Open@CKISSApiWrapper@mu2@@QEAAIPEB_W@Z		; mu2::CKISSApiWrapper::Open
PUBLIC	?Close@CKISSApiWrapper@mu2@@QEAAXXZ		; mu2::CKISSApiWrapper::Close
PUBLIC	?IsOpened@CKISSApiWrapper@mu2@@QEBA_NXZ		; mu2::CKISSApiWrapper::IsOpened
PUBLIC	?NotifyNumber@CKISSApiWrapper@mu2@@QEAAKQEAU_Notify_Info_Num@@@Z ; mu2::CKISSApiWrapper::NotifyNumber
PUBLIC	?NotifyStringA@CKISSApiWrapper@mu2@@QEAAKQEAU_Notify_Info_StrA@@@Z ; mu2::CKISSApiWrapper::NotifyStringA
PUBLIC	?NotifyStringW@CKISSApiWrapper@mu2@@QEAAKQEAU_Notify_Info_StrW@@@Z ; mu2::CKISSApiWrapper::NotifyStringW
PUBLIC	?NotifyHeartbeat@CKISSApiWrapper@mu2@@QEAAKXZ	; mu2::CKISSApiWrapper::NotifyHeartbeat
PUBLIC	?NotifyConcurrentUserCount@CKISSApiWrapper@mu2@@QEAAKKM@Z ; mu2::CKISSApiWrapper::NotifyConcurrentUserCount
PUBLIC	?GetNumber@CKISSApiWrapper@mu2@@QEAAKPEAU_Notify_Info_Num@@@Z ; mu2::CKISSApiWrapper::GetNumber
PUBLIC	?GetStringW@CKISSApiWrapper@mu2@@QEAAKPEAU_Notify_Info_StrW@@@Z ; mu2::CKISSApiWrapper::GetStringW
PUBLIC	??0CKISSApiWrapper@mu2@@AEAA@XZ			; mu2::CKISSApiWrapper::CKISSApiWrapper
PUBLIC	?LoadModule@CKISSApiWrapper@mu2@@AEAAKPEB_W@Z	; mu2::CKISSApiWrapper::LoadModule
PUBLIC	?UnloadModule@CKISSApiWrapper@mu2@@AEAAXXZ	; mu2::CKISSApiWrapper::UnloadModule
PUBLIC	?GetMainHandle@mu2@@YAPEAU_Main_Handle@@PEAVCKISSApiWrapper@1@@Z ; mu2::GetMainHandle
PUBLIC	??_GCKISSApiWrapper@mu2@@UEAAPEAXI@Z		; mu2::CKISSApiWrapper::`scalar deleting destructor'
PUBLIC	?InitNotifyInfoNumber@mu2@@YAXPEAU_Notify_Info_Num@@HMMMK@Z ; mu2::InitNotifyInfoNumber
PUBLIC	?InitNotifyInfoStringA@mu2@@YAXPEAU_Notify_Info_StrA@@HPEBD@Z ; mu2::InitNotifyInfoStringA
PUBLIC	?InitNotifyInfoStringW@mu2@@YAXPEAU_Notify_Info_StrW@@HPEB_W@Z ; mu2::InitNotifyInfoStringW
PUBLIC	?ConvertNotifyInfoStringAtoW@mu2@@YAXQEAU_Notify_Info_StrA@@PEAU_Notify_Info_StrW@@@Z ; mu2::ConvertNotifyInfoStringAtoW
PUBLIC	?_OptionsStorage@?1??__local_stdio_printf_options@@9@4_KA ; `__local_stdio_printf_options'::`2'::_OptionsStorage
PUBLIC	??_7?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@6B@ ; mu2::ISingleton<mu2::CKISSApiWrapper>::`vftable'
PUBLIC	??_7CKISSApiWrapper@mu2@@6B@			; mu2::CKISSApiWrapper::`vftable'
PUBLIC	??_C@_1CA@FHOGKDPA@?$AAK?$AAI?$AAS?$AAS?$AAA?$AAp?$AAi?$AA_?$AAx?$AA6?$AA4?$AA?4?$AAd?$AAl?$AAl@ ; `string'
PUBLIC	??_C@_1M@DFKENGJN@?$AA?$CF?$AAs?$AA?2?$AA?$CF?$AAs@ ; `string'
PUBLIC	??_C@_15GANGMFKL@?$AA?$CF?$AAs@			; `string'
PUBLIC	??_C@_09CBLADDOO@GetHandle@			; `string'
PUBLIC	??_R4CKISSApiWrapper@mu2@@6B@			; mu2::CKISSApiWrapper::`RTTI Complete Object Locator'
PUBLIC	??_R0?AVCKISSApiWrapper@mu2@@@8			; mu2::CKISSApiWrapper `RTTI Type Descriptor'
PUBLIC	??_R3CKISSApiWrapper@mu2@@8			; mu2::CKISSApiWrapper::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2CKISSApiWrapper@mu2@@8			; mu2::CKISSApiWrapper::`RTTI Base Class Array'
PUBLIC	??_R1A@?0A@EA@CKISSApiWrapper@mu2@@8		; mu2::CKISSApiWrapper::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R1A@?0A@EA@?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@8 ; mu2::ISingleton<mu2::CKISSApiWrapper>::`RTTI Base Class Descriptor at (0,-1,0,64)'
PUBLIC	??_R0?AV?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@@8 ; mu2::ISingleton<mu2::CKISSApiWrapper> `RTTI Type Descriptor'
PUBLIC	??_R3?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@8	; mu2::ISingleton<mu2::CKISSApiWrapper>::`RTTI Class Hierarchy Descriptor'
PUBLIC	??_R2?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@8	; mu2::ISingleton<mu2::CKISSApiWrapper>::`RTTI Base Class Array'
PUBLIC	??_R4?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@6B@ ; mu2::ISingleton<mu2::CKISSApiWrapper>::`RTTI Complete Object Locator'
PUBLIC	__real@c7c34f80
EXTRN	_purecall:PROC
EXTRN	??3@YAXPEAX_K@Z:PROC				; operator delete
EXTRN	memset:PROC
EXTRN	__imp_GetLastError:PROC
EXTRN	__imp_GetLocalTime:PROC
EXTRN	__imp_FreeLibrary:PROC
EXTRN	__imp_GetProcAddress:PROC
EXTRN	__imp_LoadLibraryW:PROC
EXTRN	__imp_MultiByteToWideChar:PROC
EXTRN	__stdio_common_vswprintf:PROC
EXTRN	??_E?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@UEAAPEAXI@Z:PROC ; mu2::ISingleton<mu2::CKISSApiWrapper>::`vector deleting destructor'
EXTRN	??_ECKISSApiWrapper@mu2@@UEAAPEAXI@Z:PROC	; mu2::CKISSApiWrapper::`vector deleting destructor'
EXTRN	__CxxFrameHandler4:PROC
EXTRN	__GSHandlerCheck:PROC
EXTRN	__security_check_cookie:PROC
EXTRN	??_7type_info@@6B@:BYTE				; type_info::`vftable'
EXTRN	?Instance@Server@mu2@@2PEAV12@EA:QWORD		; mu2::Server::Instance
EXTRN	__security_cookie:QWORD
EXTRN	_fltused:DWORD
;	COMDAT ?_OptionsStorage@?1??__local_stdio_printf_options@@9@4_KA
_BSS	SEGMENT
?_OptionsStorage@?1??__local_stdio_printf_options@@9@4_KA DQ 01H DUP (?) ; `__local_stdio_printf_options'::`2'::_OptionsStorage
_BSS	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?StringCopyWorkerA@@YAJPEAD_KPEA_KPEBD1@Z DD imagerel ?StringCopyWorkerA@@YAJPEAD_KPEA_KPEBD1@Z
	DD	imagerel ?StringCopyWorkerA@@YAJPEAD_KPEA_KPEBD1@Z+228
	DD	imagerel $unwind$?StringCopyWorkerA@@YAJPEAD_KPEA_KPEBD1@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?StringCopyWorkerW@@YAJPEA_W_KPEA_KPEB_W1@Z DD imagerel ?StringCopyWorkerW@@YAJPEA_W_KPEA_KPEB_W1@Z
	DD	imagerel ?StringCopyWorkerW@@YAJPEA_W_KPEA_KPEB_W1@Z+234
	DD	imagerel $unwind$?StringCopyWorkerW@@YAJPEA_W_KPEA_KPEB_W1@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?StringVPrintfWorkerW@@YAJPEA_W_KPEA_KPEB_WPEAD@Z DD imagerel ?StringVPrintfWorkerW@@YAJPEA_W_KPEA_KPEB_WPEAD@Z
	DD	imagerel ?StringVPrintfWorkerW@@YAJPEA_W_KPEA_KPEB_WPEAD@Z+334
	DD	imagerel $unwind$?StringVPrintfWorkerW@@YAJPEA_W_KPEA_KPEB_WPEAD@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?StringCchCopyA@@YAJPEAD_KPEBD@Z DD imagerel $LN16
	DD	imagerel $LN16+140
	DD	imagerel $unwind$?StringCchCopyA@@YAJPEAD_KPEBD@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?StringCchCopyW@@YAJPEA_W_KPEB_W@Z DD imagerel $LN16
	DD	imagerel $LN16+142
	DD	imagerel $unwind$?StringCchCopyW@@YAJPEA_W_KPEB_W@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?StringCchPrintfW@@YAJPEA_W_KPEB_WZZ DD imagerel $LN24
	DD	imagerel $LN24+167
	DD	imagerel $unwind$?StringCchPrintfW@@YAJPEA_W_KPEB_WZZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_G?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@UEAAPEAXI@Z DD imagerel $LN10
	DD	imagerel $LN10+65
	DD	imagerel $unwind$??_G?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??1CKISSApiWrapper@mu2@@UEAA@XZ DD imagerel $LN9
	DD	imagerel $LN9+55
	DD	imagerel $unwind$??1CKISSApiWrapper@mu2@@UEAA@XZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?UnInit@CKISSApiWrapper@mu2@@UEAA_NXZ DD imagerel $LN6
	DD	imagerel $LN6+62
	DD	imagerel $unwind$?UnInit@CKISSApiWrapper@mu2@@UEAA_NXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Open@CKISSApiWrapper@mu2@@QEAAIPEB_W@Z DD imagerel $LN6
	DD	imagerel $LN6+96
	DD	imagerel $unwind$?Open@CKISSApiWrapper@mu2@@QEAAIPEB_W@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?Close@CKISSApiWrapper@mu2@@QEAAXXZ DD imagerel $LN5
	DD	imagerel $LN5+66
	DD	imagerel $unwind$?Close@CKISSApiWrapper@mu2@@QEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?IsOpened@CKISSApiWrapper@mu2@@QEBA_NXZ DD imagerel $LN5
	DD	imagerel $LN5+58
	DD	imagerel $unwind$?IsOpened@CKISSApiWrapper@mu2@@QEBA_NXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?NotifyNumber@CKISSApiWrapper@mu2@@QEAAKQEAU_Notify_Info_Num@@@Z DD imagerel $LN5
	DD	imagerel $LN5+71
	DD	imagerel $unwind$?NotifyNumber@CKISSApiWrapper@mu2@@QEAAKQEAU_Notify_Info_Num@@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?NotifyStringA@CKISSApiWrapper@mu2@@QEAAKQEAU_Notify_Info_StrA@@@Z DD imagerel $LN5
	DD	imagerel $LN5+138
	DD	imagerel $unwind$?NotifyStringA@CKISSApiWrapper@mu2@@QEAAKQEAU_Notify_Info_StrA@@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?NotifyStringW@CKISSApiWrapper@mu2@@QEAAKQEAU_Notify_Info_StrW@@@Z DD imagerel $LN5
	DD	imagerel $LN5+71
	DD	imagerel $unwind$?NotifyStringW@CKISSApiWrapper@mu2@@QEAAKQEAU_Notify_Info_StrW@@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?NotifyHeartbeat@CKISSApiWrapper@mu2@@QEAAKXZ DD imagerel $LN5
	DD	imagerel $LN5+142
	DD	imagerel $unwind$?NotifyHeartbeat@CKISSApiWrapper@mu2@@QEAAKXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?NotifyConcurrentUserCount@CKISSApiWrapper@mu2@@QEAAKKM@Z DD imagerel $LN5
	DD	imagerel $LN5+158
	DD	imagerel $unwind$?NotifyConcurrentUserCount@CKISSApiWrapper@mu2@@QEAAKKM@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?GetNumber@CKISSApiWrapper@mu2@@QEAAKPEAU_Notify_Info_Num@@@Z DD imagerel $LN5
	DD	imagerel $LN5+71
	DD	imagerel $unwind$?GetNumber@CKISSApiWrapper@mu2@@QEAAKPEAU_Notify_Info_Num@@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?GetStringW@CKISSApiWrapper@mu2@@QEAAKPEAU_Notify_Info_StrW@@@Z DD imagerel $LN5
	DD	imagerel $LN5+71
	DD	imagerel $unwind$?GetStringW@CKISSApiWrapper@mu2@@QEAAKPEAU_Notify_Info_StrW@@@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?LoadModule@CKISSApiWrapper@mu2@@AEAAKPEB_W@Z DD imagerel $LN8
	DD	imagerel $LN8+338
	DD	imagerel $unwind$?LoadModule@CKISSApiWrapper@mu2@@AEAAKPEB_W@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?UnloadModule@CKISSApiWrapper@mu2@@AEAAXXZ DD imagerel $LN4
	DD	imagerel $LN4+80
	DD	imagerel $unwind$?UnloadModule@CKISSApiWrapper@mu2@@AEAAXXZ
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$??_GCKISSApiWrapper@mu2@@UEAAPEAXI@Z DD imagerel $LN5
	DD	imagerel $LN5+60
	DD	imagerel $unwind$??_GCKISSApiWrapper@mu2@@UEAAPEAXI@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?InitNotifyInfoNumber@mu2@@YAXPEAU_Notify_Info_Num@@HMMMK@Z DD imagerel $LN4
	DD	imagerel $LN4+126
	DD	imagerel $unwind$?InitNotifyInfoNumber@mu2@@YAXPEAU_Notify_Info_Num@@HMMMK@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?InitNotifyInfoStringA@mu2@@YAXPEAU_Notify_Info_StrA@@HPEBD@Z DD imagerel $LN19
	DD	imagerel $LN19+87
	DD	imagerel $unwind$?InitNotifyInfoStringA@mu2@@YAXPEAU_Notify_Info_StrA@@HPEBD@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?InitNotifyInfoStringW@mu2@@YAXPEAU_Notify_Info_StrW@@HPEB_W@Z DD imagerel $LN19
	DD	imagerel $LN19+87
	DD	imagerel $unwind$?InitNotifyInfoStringW@mu2@@YAXPEAU_Notify_Info_StrW@@HPEB_W@Z
pdata	ENDS
;	COMDAT pdata
pdata	SEGMENT
$pdata$?ConvertNotifyInfoStringAtoW@mu2@@YAXQEAU_Notify_Info_StrA@@PEAU_Notify_Info_StrW@@@Z DD imagerel $LN7
	DD	imagerel $LN7+151
	DD	imagerel $unwind$?ConvertNotifyInfoStringAtoW@mu2@@YAXQEAU_Notify_Info_StrA@@PEAU_Notify_Info_StrW@@@Z
pdata	ENDS
;	COMDAT __real@c7c34f80
CONST	SEGMENT
__real@c7c34f80 DD 0c7c34f80r			; -99999
CONST	ENDS
;	COMDAT ??_R4?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@6B@
rdata$r	SEGMENT
??_R4?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@6B@ DD 01H ; mu2::ISingleton<mu2::CKISSApiWrapper>::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AV?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@@8
	DD	imagerel ??_R3?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@8
	DD	imagerel ??_R4?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_R2?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@8
rdata$r	SEGMENT
??_R2?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@8 DD imagerel ??_R1A@?0A@EA@?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@8 ; mu2::ISingleton<mu2::CKISSApiWrapper>::`RTTI Base Class Array'
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@8
rdata$r	SEGMENT
??_R3?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@8 DD 00H	; mu2::ISingleton<mu2::CKISSApiWrapper>::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	01H
	DD	imagerel ??_R2?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AV?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@@8
data$rs	SEGMENT
??_R0?AV?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::ISingleton<mu2::CKISSApiWrapper> `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AV?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R1A@?0A@EA@?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@8 DD imagerel ??_R0?AV?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@@8 ; mu2::ISingleton<mu2::CKISSApiWrapper>::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	00H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R1A@?0A@EA@CKISSApiWrapper@mu2@@8
rdata$r	SEGMENT
??_R1A@?0A@EA@CKISSApiWrapper@mu2@@8 DD imagerel ??_R0?AVCKISSApiWrapper@mu2@@@8 ; mu2::CKISSApiWrapper::`RTTI Base Class Descriptor at (0,-1,0,64)'
	DD	01H
	DD	00H
	DD	0ffffffffH
	DD	00H
	DD	040H
	DD	imagerel ??_R3CKISSApiWrapper@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R2CKISSApiWrapper@mu2@@8
rdata$r	SEGMENT
??_R2CKISSApiWrapper@mu2@@8 DD imagerel ??_R1A@?0A@EA@CKISSApiWrapper@mu2@@8 ; mu2::CKISSApiWrapper::`RTTI Base Class Array'
	DD	imagerel ??_R1A@?0A@EA@?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@8
	ORG $+3
rdata$r	ENDS
;	COMDAT ??_R3CKISSApiWrapper@mu2@@8
rdata$r	SEGMENT
??_R3CKISSApiWrapper@mu2@@8 DD 00H			; mu2::CKISSApiWrapper::`RTTI Class Hierarchy Descriptor'
	DD	00H
	DD	02H
	DD	imagerel ??_R2CKISSApiWrapper@mu2@@8
rdata$r	ENDS
;	COMDAT ??_R0?AVCKISSApiWrapper@mu2@@@8
data$rs	SEGMENT
??_R0?AVCKISSApiWrapper@mu2@@@8 DQ FLAT:??_7type_info@@6B@ ; mu2::CKISSApiWrapper `RTTI Type Descriptor'
	DQ	0000000000000000H
	DB	'.?AVCKISSApiWrapper@mu2@@', 00H
data$rs	ENDS
;	COMDAT ??_R4CKISSApiWrapper@mu2@@6B@
rdata$r	SEGMENT
??_R4CKISSApiWrapper@mu2@@6B@ DD 01H			; mu2::CKISSApiWrapper::`RTTI Complete Object Locator'
	DD	00H
	DD	00H
	DD	imagerel ??_R0?AVCKISSApiWrapper@mu2@@@8
	DD	imagerel ??_R3CKISSApiWrapper@mu2@@8
	DD	imagerel ??_R4CKISSApiWrapper@mu2@@6B@
rdata$r	ENDS
;	COMDAT ??_C@_09CBLADDOO@GetHandle@
CONST	SEGMENT
??_C@_09CBLADDOO@GetHandle@ DB 'GetHandle', 00H		; `string'
CONST	ENDS
;	COMDAT ??_C@_15GANGMFKL@?$AA?$CF?$AAs@
CONST	SEGMENT
??_C@_15GANGMFKL@?$AA?$CF?$AAs@ DB '%', 00H, 's', 00H, 00H, 00H ; `string'
CONST	ENDS
;	COMDAT ??_C@_1M@DFKENGJN@?$AA?$CF?$AAs?$AA?2?$AA?$CF?$AAs@
CONST	SEGMENT
??_C@_1M@DFKENGJN@?$AA?$CF?$AAs?$AA?2?$AA?$CF?$AAs@ DB '%', 00H, 's', 00H
	DB	'\', 00H, '%', 00H, 's', 00H, 00H, 00H	; `string'
CONST	ENDS
;	COMDAT ??_C@_1CA@FHOGKDPA@?$AAK?$AAI?$AAS?$AAS?$AAA?$AAp?$AAi?$AA_?$AAx?$AA6?$AA4?$AA?4?$AAd?$AAl?$AAl@
CONST	SEGMENT
??_C@_1CA@FHOGKDPA@?$AAK?$AAI?$AAS?$AAS?$AAA?$AAp?$AAi?$AA_?$AAx?$AA6?$AA4?$AA?4?$AAd?$AAl?$AAl@ DB 'K'
	DB	00H, 'I', 00H, 'S', 00H, 'S', 00H, 'A', 00H, 'p', 00H, 'i', 00H
	DB	'_', 00H, 'x', 00H, '6', 00H, '4', 00H, '.', 00H, 'd', 00H, 'l'
	DB	00H, 'l', 00H, 00H, 00H			; `string'
CONST	ENDS
;	COMDAT ??_7CKISSApiWrapper@mu2@@6B@
CONST	SEGMENT
??_7CKISSApiWrapper@mu2@@6B@ DQ FLAT:??_R4CKISSApiWrapper@mu2@@6B@ ; mu2::CKISSApiWrapper::`vftable'
	DQ	FLAT:?Init@CKISSApiWrapper@mu2@@UEAA_NPEAX@Z
	DQ	FLAT:?UnInit@CKISSApiWrapper@mu2@@UEAA_NXZ
	DQ	FLAT:??_ECKISSApiWrapper@mu2@@UEAAPEAXI@Z
CONST	ENDS
;	COMDAT ??_7?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@6B@
CONST	SEGMENT
??_7?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@6B@ DQ FLAT:??_R4?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@6B@ ; mu2::ISingleton<mu2::CKISSApiWrapper>::`vftable'
	DQ	FLAT:_purecall
	DQ	FLAT:_purecall
	DQ	FLAT:??_E?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@UEAAPEAXI@Z
CONST	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?ConvertNotifyInfoStringAtoW@mu2@@YAXQEAU_Notify_Info_StrA@@PEAU_Notify_Info_StrW@@@Z DD 031001H
	DD	0700c6210H
	DD	0600bH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?InitNotifyInfoStringW@mu2@@YAXPEAU_Notify_Info_StrW@@HPEB_W@Z DD 011201H
	DD	04212H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?InitNotifyInfoStringA@mu2@@YAXPEAU_Notify_Info_StrA@@HPEBD@Z DD 011201H
	DD	04212H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?InitNotifyInfoNumber@mu2@@YAXPEAU_Notify_Info_Num@@HMMMK@Z DD 011901H
	DD	04219H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_GCKISSApiWrapper@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?UnloadModule@CKISSApiWrapper@mu2@@AEAAXXZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DW	01cH
	DW	0139H
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?LoadModule@CKISSApiWrapper@mu2@@AEAAKPEB_W@Z DD 032419H
	DD	08c0112H
	DD	0700bH
	DD	imagerel __GSHandlerCheck
	DD	0450H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?GetStringW@CKISSApiWrapper@mu2@@QEAAKPEAU_Notify_Info_StrW@@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?GetNumber@CKISSApiWrapper@mu2@@QEAAKPEAU_Notify_Info_Num@@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DB	01dH
	DB	08cH
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?NotifyConcurrentUserCount@CKISSApiWrapper@mu2@@QEAAKKM@Z DD 012219H
	DD	0c213H
	DD	imagerel __GSHandlerCheck
	DD	058H
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DB	013H
	DB	07cH
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?NotifyHeartbeat@CKISSApiWrapper@mu2@@QEAAKXZ DD 011819H
	DD	0c209H
	DD	imagerel __GSHandlerCheck
	DD	058H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?NotifyStringW@CKISSApiWrapper@mu2@@QEAAKQEAU_Notify_Info_StrW@@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT voltbl
voltbl	SEGMENT
_volmd	DB	01bH
	DB	072H
voltbl	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?NotifyStringA@CKISSApiWrapper@mu2@@QEAAKQEAU_Notify_Info_StrA@@@Z DD 022319H
	DD	08b0111H
	DD	imagerel __GSHandlerCheck
	DD	0440H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?NotifyNumber@CKISSApiWrapper@mu2@@QEAAKQEAU_Notify_Info_Num@@@Z DD 010e01H
	DD	0420eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?IsOpened@CKISSApiWrapper@mu2@@QEBA_NXZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Close@CKISSApiWrapper@mu2@@QEAAXXZ DD 010901H
	DD	04209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?Open@CKISSApiWrapper@mu2@@QEAAIPEB_W@Z DD 010e01H
	DD	0620eH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?UnInit@CKISSApiWrapper@mu2@@UEAA_NXZ DD 010901H
	DD	06209H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$ip2state$??1CKISSApiWrapper@mu2@@UEAA@XZ DB 02H
	DB	00H
	DB	00H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$cppxdata$??1CKISSApiWrapper@mu2@@UEAA@XZ DB 060H
	DD	imagerel $ip2state$??1CKISSApiWrapper@mu2@@UEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??1CKISSApiWrapper@mu2@@UEAA@XZ DD 010919H
	DD	04209H
	DD	imagerel __CxxFrameHandler4
	DD	imagerel $cppxdata$??1CKISSApiWrapper@mu2@@UEAA@XZ
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$??_G?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@UEAAPEAXI@Z DD 010d01H
	DD	0420dH
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?StringCchPrintfW@@YAJPEA_W_KPEB_WZZ DD 011801H
	DD	0a218H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?StringCchCopyW@@YAJPEA_W_KPEB_W@Z DD 011301H
	DD	08213H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?StringCchCopyA@@YAJPEAD_KPEBD@Z DD 011301H
	DD	08213H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?StringVPrintfWorkerW@@YAJPEA_W_KPEA_KPEB_WPEAD@Z DD 011801H
	DD	0c218H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?StringCopyWorkerW@@YAJPEA_W_KPEA_KPEB_W1@Z DD 011801H
	DD	02218H
xdata	ENDS
;	COMDAT xdata
xdata	SEGMENT
$unwind$?StringCopyWorkerA@@YAJPEAD_KPEA_KPEBD1@Z DD 011801H
	DD	02218H
xdata	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\KISS\KISSApiWrapper.cpp
; File F:\Release_Branch\Shared\ResetHelper.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\KISS\KISSApiWrapper.cpp
;	COMDAT ?ConvertNotifyInfoStringAtoW@mu2@@YAXQEAU_Notify_Info_StrA@@PEAU_Notify_Info_StrW@@@Z
_TEXT	SEGMENT
_src$ = 80
_dst$ = 88
?ConvertNotifyInfoStringAtoW@mu2@@YAXQEAU_Notify_Info_StrA@@PEAU_Notify_Info_StrW@@@Z PROC ; mu2::ConvertNotifyInfoStringAtoW, COMDAT

; 262  : 	{

$LN7:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	56		 push	 rsi
  0000b	57		 push	 rdi
  0000c	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 263  : 		if (NULL == _src || NULL == _dst)

  00010	48 83 7c 24 50
	00		 cmp	 QWORD PTR _src$[rsp], 0
  00016	74 08		 je	 SHORT $LN3@ConvertNot
  00018	48 83 7c 24 58
	00		 cmp	 QWORD PTR _dst$[rsp], 0
  0001e	75 02		 jne	 SHORT $LN2@ConvertNot
$LN3@ConvertNot:

; 264  : 			return;

  00020	eb 6e		 jmp	 SHORT $LN1@ConvertNot
$LN2@ConvertNot:

; 265  : 
; 266  : 		_dst->registered_time = _src->registered_time;

  00022	48 8b 7c 24 58	 mov	 rdi, QWORD PTR _dst$[rsp]
  00027	48 8b 74 24 50	 mov	 rsi, QWORD PTR _src$[rsp]
  0002c	b9 10 00 00 00	 mov	 ecx, 16
  00031	f3 a4		 rep movsb

; 267  : 		_dst->activity_id = _src->activity_id;

  00033	48 8b 44 24 58	 mov	 rax, QWORD PTR _dst$[rsp]
  00038	48 8b 4c 24 50	 mov	 rcx, QWORD PTR _src$[rsp]
  0003d	8b 49 10	 mov	 ecx, DWORD PTR [rcx+16]
  00040	89 48 10	 mov	 DWORD PTR [rax+16], ecx

; 268  : 
; 269  : 		mu2::ResetHelper::Reset(_dst->content);

  00043	48 8b 44 24 58	 mov	 rax, QWORD PTR _dst$[rsp]
  00048	48 83 c0 14	 add	 rax, 20
; File F:\Release_Branch\Shared\ResetHelper.h

; 24   : 			ZeroMemory(arr, sizeof(T)*SIZE);

  0004c	41 b8 02 04 00
	00		 mov	 r8d, 1026		; 00000402H
  00052	33 d2		 xor	 edx, edx
  00054	48 8b c8	 mov	 rcx, rax
  00057	e8 00 00 00 00	 call	 memset
  0005c	90		 npad	 1
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\KISS\KISSApiWrapper.cpp

; 270  : 		::MultiByteToWideChar(CP_ACP, 0, _src->content, -1, _dst->content, _countof(_dst->content));

  0005d	48 8b 44 24 58	 mov	 rax, QWORD PTR _dst$[rsp]
  00062	48 83 c0 14	 add	 rax, 20
  00066	48 8b 4c 24 50	 mov	 rcx, QWORD PTR _src$[rsp]
  0006b	48 83 c1 14	 add	 rcx, 20
  0006f	c7 44 24 28 01
	02 00 00	 mov	 DWORD PTR [rsp+40], 513	; 00000201H
  00077	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  0007c	41 b9 ff ff ff
	ff		 mov	 r9d, -1
  00082	4c 8b c1	 mov	 r8, rcx
  00085	33 d2		 xor	 edx, edx
  00087	33 c9		 xor	 ecx, ecx
  00089	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_MultiByteToWideChar
  0008f	90		 npad	 1
$LN1@ConvertNot:

; 271  : 	}

  00090	48 83 c4 38	 add	 rsp, 56			; 00000038H
  00094	5f		 pop	 rdi
  00095	5e		 pop	 rsi
  00096	c3		 ret	 0
?ConvertNotifyInfoStringAtoW@mu2@@YAXQEAU_Notify_Info_StrA@@PEAU_Notify_Info_StrW@@@Z ENDP ; mu2::ConvertNotifyInfoStringAtoW
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\KISS\KISSApiWrapper.cpp
;	COMDAT ?InitNotifyInfoStringW@mu2@@YAXPEAU_Notify_Info_StrW@@HPEB_W@Z
_TEXT	SEGMENT
_lpInfo$ = 48
_nActivityId$ = 56
_lpszString$ = 64
?InitNotifyInfoStringW@mu2@@YAXPEAU_Notify_Info_StrW@@HPEB_W@Z PROC ; mu2::InitNotifyInfoStringW, COMDAT

; 250  : 	{

$LN19:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00009	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000e	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 251  : 		if (NULL == _lpInfo)

  00012	48 83 7c 24 30
	00		 cmp	 QWORD PTR _lpInfo$[rsp], 0
  00018	75 02		 jne	 SHORT $LN2@InitNotify

; 252  : 			return;

  0001a	eb 36		 jmp	 SHORT $LN1@InitNotify
$LN2@InitNotify:

; 253  : 
; 254  : 		::GetLocalTime(&_lpInfo->registered_time);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR _lpInfo$[rsp]
  00021	48 8b c8	 mov	 rcx, rax
  00024	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetLocalTime

; 255  : 
; 256  : 		_lpInfo->activity_id = _nActivityId;

  0002a	48 8b 44 24 30	 mov	 rax, QWORD PTR _lpInfo$[rsp]
  0002f	8b 4c 24 38	 mov	 ecx, DWORD PTR _nActivityId$[rsp]
  00033	89 48 10	 mov	 DWORD PTR [rax+16], ecx

; 257  : 
; 258  : 		StringCchCopyW(_lpInfo->content, sizeof(_lpInfo->content), _lpszString);

  00036	48 8b 44 24 30	 mov	 rax, QWORD PTR _lpInfo$[rsp]
  0003b	48 83 c0 14	 add	 rax, 20
  0003f	4c 8b 44 24 40	 mov	 r8, QWORD PTR _lpszString$[rsp]
  00044	ba 02 04 00 00	 mov	 edx, 1026		; 00000402H
  00049	48 8b c8	 mov	 rcx, rax
  0004c	e8 00 00 00 00	 call	 ?StringCchCopyW@@YAJPEA_W_KPEB_W@Z ; StringCchCopyW
  00051	90		 npad	 1
$LN1@InitNotify:

; 259  : 	}

  00052	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00056	c3		 ret	 0
?InitNotifyInfoStringW@mu2@@YAXPEAU_Notify_Info_StrW@@HPEB_W@Z ENDP ; mu2::InitNotifyInfoStringW
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\KISS\KISSApiWrapper.cpp
;	COMDAT ?InitNotifyInfoStringA@mu2@@YAXPEAU_Notify_Info_StrA@@HPEBD@Z
_TEXT	SEGMENT
_lpInfo$ = 48
_nActivityId$ = 56
_lpszString$ = 64
?InitNotifyInfoStringA@mu2@@YAXPEAU_Notify_Info_StrA@@HPEBD@Z PROC ; mu2::InitNotifyInfoStringA, COMDAT

; 238  : 	{

$LN19:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00009	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000e	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 239  : 		if (NULL == _lpInfo)

  00012	48 83 7c 24 30
	00		 cmp	 QWORD PTR _lpInfo$[rsp], 0
  00018	75 02		 jne	 SHORT $LN2@InitNotify

; 240  : 			return;

  0001a	eb 36		 jmp	 SHORT $LN1@InitNotify
$LN2@InitNotify:

; 241  : 
; 242  : 		::GetLocalTime(&_lpInfo->registered_time);

  0001c	48 8b 44 24 30	 mov	 rax, QWORD PTR _lpInfo$[rsp]
  00021	48 8b c8	 mov	 rcx, rax
  00024	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetLocalTime

; 243  : 
; 244  : 		_lpInfo->activity_id = _nActivityId;

  0002a	48 8b 44 24 30	 mov	 rax, QWORD PTR _lpInfo$[rsp]
  0002f	8b 4c 24 38	 mov	 ecx, DWORD PTR _nActivityId$[rsp]
  00033	89 48 10	 mov	 DWORD PTR [rax+16], ecx

; 245  : 
; 246  : 		StringCchCopyA(_lpInfo->content, sizeof(_lpInfo->content), _lpszString);

  00036	48 8b 44 24 30	 mov	 rax, QWORD PTR _lpInfo$[rsp]
  0003b	48 83 c0 14	 add	 rax, 20
  0003f	4c 8b 44 24 40	 mov	 r8, QWORD PTR _lpszString$[rsp]
  00044	ba 01 02 00 00	 mov	 edx, 513		; 00000201H
  00049	48 8b c8	 mov	 rcx, rax
  0004c	e8 00 00 00 00	 call	 ?StringCchCopyA@@YAJPEAD_KPEBD@Z ; StringCchCopyA
  00051	90		 npad	 1
$LN1@InitNotify:

; 247  : 	}

  00052	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00056	c3		 ret	 0
?InitNotifyInfoStringA@mu2@@YAXPEAU_Notify_Info_StrA@@HPEBD@Z ENDP ; mu2::InitNotifyInfoStringA
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\KISS\KISSApiWrapper.cpp
;	COMDAT ?InitNotifyInfoNumber@mu2@@YAXPEAU_Notify_Info_Num@@HMMMK@Z
_TEXT	SEGMENT
_lpInfo$ = 48
_nActivityId$ = 56
_fValue$ = 64
_fMin$ = 72
_fMax$ = 80
_dwPortNo$ = 88
?InitNotifyInfoNumber@mu2@@YAXPEAU_Notify_Info_Num@@HMMMK@Z PROC ; mu2::InitNotifyInfoNumber, COMDAT

; 224  : 	{

$LN4:
  00000	f3 0f 11 5c 24
	20		 movss	 DWORD PTR [rsp+32], xmm3
  00006	f3 0f 11 54 24
	18		 movss	 DWORD PTR [rsp+24], xmm2
  0000c	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00010	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00015	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 225  : 		if (NULL == _lpInfo)

  00019	48 83 7c 24 30
	00		 cmp	 QWORD PTR _lpInfo$[rsp], 0
  0001f	75 02		 jne	 SHORT $LN2@InitNotify

; 226  : 			return;

  00021	eb 56		 jmp	 SHORT $LN1@InitNotify
$LN2@InitNotify:

; 227  : 
; 228  : 		::GetLocalTime(&_lpInfo->registered_time);

  00023	48 8b 44 24 30	 mov	 rax, QWORD PTR _lpInfo$[rsp]
  00028	48 8b c8	 mov	 rcx, rax
  0002b	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetLocalTime

; 229  : 
; 230  : 		_lpInfo->activity_id = _nActivityId;

  00031	48 8b 44 24 30	 mov	 rax, QWORD PTR _lpInfo$[rsp]
  00036	8b 4c 24 38	 mov	 ecx, DWORD PTR _nActivityId$[rsp]
  0003a	89 48 10	 mov	 DWORD PTR [rax+16], ecx

; 231  : 		_lpInfo->max_val = _fMax;

  0003d	48 8b 44 24 30	 mov	 rax, QWORD PTR _lpInfo$[rsp]
  00042	f3 0f 10 44 24
	50		 movss	 xmm0, DWORD PTR _fMax$[rsp]
  00048	f3 0f 11 40 18	 movss	 DWORD PTR [rax+24], xmm0

; 232  : 		_lpInfo->min_val = _fMin;

  0004d	48 8b 44 24 30	 mov	 rax, QWORD PTR _lpInfo$[rsp]
  00052	f3 0f 10 44 24
	48		 movss	 xmm0, DWORD PTR _fMin$[rsp]
  00058	f3 0f 11 40 1c	 movss	 DWORD PTR [rax+28], xmm0

; 233  : 		_lpInfo->val = _fValue;

  0005d	48 8b 44 24 30	 mov	 rax, QWORD PTR _lpInfo$[rsp]
  00062	f3 0f 10 44 24
	40		 movss	 xmm0, DWORD PTR _fValue$[rsp]
  00068	f3 0f 11 40 20	 movss	 DWORD PTR [rax+32], xmm0

; 234  : 		_lpInfo->port_no = _dwPortNo;

  0006d	48 8b 44 24 30	 mov	 rax, QWORD PTR _lpInfo$[rsp]
  00072	8b 4c 24 58	 mov	 ecx, DWORD PTR _dwPortNo$[rsp]
  00076	89 48 14	 mov	 DWORD PTR [rax+20], ecx
$LN1@InitNotify:

; 235  : 	}

  00079	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0007d	c3		 ret	 0
?InitNotifyInfoNumber@mu2@@YAXPEAU_Notify_Info_Num@@HMMMK@Z ENDP ; mu2::InitNotifyInfoNumber
_TEXT	ENDS
; Function compile flags: /Odtp
;	COMDAT ??_GCKISSApiWrapper@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_GCKISSApiWrapper@mu2@@UEAAPEAXI@Z PROC		; mu2::CKISSApiWrapper::`scalar deleting destructor', COMDAT
$LN5:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  0000d	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00012	e8 00 00 00 00	 call	 ??1CKISSApiWrapper@mu2@@UEAA@XZ ; mu2::CKISSApiWrapper::~CKISSApiWrapper
  00017	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  0001b	83 e0 01	 and	 eax, 1
  0001e	85 c0		 test	 eax, eax
  00020	74 10		 je	 SHORT $LN2@scalar
  00022	ba 20 00 00 00	 mov	 edx, 32			; 00000020H
  00027	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0002c	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00031	90		 npad	 1
$LN2@scalar:
  00032	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00037	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0003b	c3		 ret	 0
??_GCKISSApiWrapper@mu2@@UEAAPEAXI@Z ENDP		; mu2::CKISSApiWrapper::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\KISS\KISSApiWrapper.cpp
;	COMDAT ?GetMainHandle@mu2@@YAPEAU_Main_Handle@@PEAVCKISSApiWrapper@1@@Z
_TEXT	SEGMENT
pAPIWrapper$ = 8
?GetMainHandle@mu2@@YAPEAU_Main_Handle@@PEAVCKISSApiWrapper@1@@Z PROC ; mu2::GetMainHandle, COMDAT

; 274  : 	{

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 275  : 		if (NULL == pAPIWrapper)

  00005	48 83 7c 24 08
	00		 cmp	 QWORD PTR pAPIWrapper$[rsp], 0
  0000b	75 04		 jne	 SHORT $LN2@GetMainHan

; 276  : 			return NULL;

  0000d	33 c0		 xor	 eax, eax
  0000f	eb 09		 jmp	 SHORT $LN1@GetMainHan
$LN2@GetMainHan:

; 277  : 
; 278  : 		return pAPIWrapper->m_pMainHandle;

  00011	48 8b 44 24 08	 mov	 rax, QWORD PTR pAPIWrapper$[rsp]
  00016	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
$LN1@GetMainHan:

; 279  : 	}

  0001a	c3		 ret	 0
?GetMainHandle@mu2@@YAPEAU_Main_Handle@@PEAVCKISSApiWrapper@1@@Z ENDP ; mu2::GetMainHandle
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\KISS\KISSApiWrapper.cpp
;	COMDAT ?UnloadModule@CKISSApiWrapper@mu2@@AEAAXXZ
_TEXT	SEGMENT
this$ = 48
?UnloadModule@CKISSApiWrapper@mu2@@AEAAXXZ PROC		; mu2::CKISSApiWrapper::UnloadModule, COMDAT

; 86   : 	{

$LN4:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 87   : 		if (NULL != m_hLib)

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00013	74 36		 je	 SHORT $LN2@UnloadModu

; 88   : 		{
; 89   : 			::FreeLibrary(m_hLib);

  00015	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 8b 48 08	 mov	 rcx, QWORD PTR [rax+8]
  0001e	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_FreeLibrary

; 90   : 
; 91   : 			m_hLib = NULL;

  00024	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00029	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0

; 92   : 			m_pMainHandle = NULL;

  00031	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00036	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 93   : 			m_fpGetHandle = NULL;

  0003e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00043	48 c7 40 18 00
	00 00 00	 mov	 QWORD PTR [rax+24], 0
$LN2@UnloadModu:

; 94   : 		}
; 95   : 	}

  0004b	48 83 c4 28	 add	 rsp, 40			; 00000028H
  0004f	c3		 ret	 0
?UnloadModule@CKISSApiWrapper@mu2@@AEAAXXZ ENDP		; mu2::CKISSApiWrapper::UnloadModule
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\KISS\KISSApiWrapper.cpp
;	COMDAT ?LoadModule@CKISSApiWrapper@mu2@@AEAAKPEB_W@Z
_TEXT	SEGMENT
dwErr$1 = 48
szLibPath$ = 64
__$ArrayPad$ = 1104
this$ = 1136
_lpszPath$ = 1144
?LoadModule@CKISSApiWrapper@mu2@@AEAAKPEB_W@Z PROC	; mu2::CKISSApiWrapper::LoadModule, COMDAT

; 53   : 	{

$LN8:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	57		 push	 rdi
  0000b	48 81 ec 60 04
	00 00		 sub	 rsp, 1120		; 00000460H
  00012	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR __security_cookie
  00019	48 33 c4	 xor	 rax, rsp
  0001c	48 89 84 24 50
	04 00 00	 mov	 QWORD PTR __$ArrayPad$[rsp], rax

; 54   : 		if (NULL != m_hLib)

  00024	48 8b 84 24 70
	04 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0002c	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00031	74 07		 je	 SHORT $LN2@LoadModule

; 55   : 			return ERROR_SUCCESS;

  00033	33 c0		 xor	 eax, eax
  00035	e9 ff 00 00 00	 jmp	 $LN1@LoadModule
$LN2@LoadModule:

; 56   : 
; 57   : 		TCHAR szLibPath[MAX_PATH * 2] = { 0, };

  0003a	48 8d 44 24 40	 lea	 rax, QWORD PTR szLibPath$[rsp]
  0003f	48 8b f8	 mov	 rdi, rax
  00042	33 c0		 xor	 eax, eax
  00044	b9 10 04 00 00	 mov	 ecx, 1040		; 00000410H
  00049	f3 aa		 rep stosb

; 58   : 
; 59   : 		if (NULL != _lpszPath)

  0004b	48 83 bc 24 78
	04 00 00 00	 cmp	 QWORD PTR _lpszPath$[rsp], 0
  00054	74 2d		 je	 SHORT $LN3@LoadModule

; 60   : 			StringCchPrintf(szLibPath, MAX_PATH * 2, _T("%s\\%s"), _lpszPath, KISS_API_MODULE_NAME);

  00056	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:??_C@_1CA@FHOGKDPA@?$AAK?$AAI?$AAS?$AAS?$AAA?$AAp?$AAi?$AA_?$AAx?$AA6?$AA4?$AA?4?$AAd?$AAl?$AAl@
  0005d	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  00062	4c 8b 8c 24 78
	04 00 00	 mov	 r9, QWORD PTR _lpszPath$[rsp]
  0006a	4c 8d 05 00 00
	00 00		 lea	 r8, OFFSET FLAT:??_C@_1M@DFKENGJN@?$AA?$CF?$AAs?$AA?2?$AA?$CF?$AAs@
  00071	ba 08 02 00 00	 mov	 edx, 520		; 00000208H
  00076	48 8d 4c 24 40	 lea	 rcx, QWORD PTR szLibPath$[rsp]
  0007b	e8 00 00 00 00	 call	 ?StringCchPrintfW@@YAJPEA_W_KPEB_WZZ ; StringCchPrintfW
  00080	90		 npad	 1
  00081	eb 1e		 jmp	 SHORT $LN4@LoadModule
$LN3@LoadModule:

; 61   : 		else
; 62   : 			StringCchPrintf(szLibPath, MAX_PATH * 2, _T("%s"), KISS_API_MODULE_NAME);

  00083	4c 8d 0d 00 00
	00 00		 lea	 r9, OFFSET FLAT:??_C@_1CA@FHOGKDPA@?$AAK?$AAI?$AAS?$AAS?$AAA?$AAp?$AAi?$AA_?$AAx?$AA6?$AA4?$AA?4?$AAd?$AAl?$AAl@
  0008a	4c 8d 05 00 00
	00 00		 lea	 r8, OFFSET FLAT:??_C@_15GANGMFKL@?$AA?$CF?$AAs@
  00091	ba 08 02 00 00	 mov	 edx, 520		; 00000208H
  00096	48 8d 4c 24 40	 lea	 rcx, QWORD PTR szLibPath$[rsp]
  0009b	e8 00 00 00 00	 call	 ?StringCchPrintfW@@YAJPEA_W_KPEB_WZZ ; StringCchPrintfW
  000a0	90		 npad	 1
$LN4@LoadModule:

; 63   : 
; 64   : 		m_hLib = ::LoadLibrary(szLibPath);

  000a1	48 8d 4c 24 40	 lea	 rcx, QWORD PTR szLibPath$[rsp]
  000a6	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_LoadLibraryW
  000ac	48 8b 8c 24 70
	04 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  000b4	48 89 41 08	 mov	 QWORD PTR [rcx+8], rax

; 65   : 
; 66   : 		if (NULL == m_hLib)

  000b8	48 8b 84 24 70
	04 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000c0	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  000c5	75 08		 jne	 SHORT $LN5@LoadModule

; 67   : 			return ::GetLastError();

  000c7	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetLastError
  000cd	eb 6a		 jmp	 SHORT $LN1@LoadModule
$LN5@LoadModule:

; 68   : 
; 69   : 		m_fpGetHandle = (LPFN_GET_HANDLE)::GetProcAddress(m_hLib, "GetHandle");

  000cf	48 8d 15 00 00
	00 00		 lea	 rdx, OFFSET FLAT:??_C@_09CBLADDOO@GetHandle@
  000d6	48 8b 84 24 70
	04 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000de	48 8b 48 08	 mov	 rcx, QWORD PTR [rax+8]
  000e2	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetProcAddress
  000e8	48 8b 8c 24 70
	04 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  000f0	48 89 41 18	 mov	 QWORD PTR [rcx+24], rax

; 70   : 
; 71   : 		if (NULL == m_fpGetHandle)

  000f4	48 8b 84 24 70
	04 00 00	 mov	 rax, QWORD PTR this$[rsp]
  000fc	48 83 78 18 00	 cmp	 QWORD PTR [rax+24], 0
  00101	75 1d		 jne	 SHORT $LN6@LoadModule

; 72   : 		{
; 73   : 			DWORD dwErr = ::GetLastError();

  00103	ff 15 00 00 00
	00		 call	 QWORD PTR __imp_GetLastError
  00109	89 44 24 30	 mov	 DWORD PTR dwErr$1[rsp], eax

; 74   : 
; 75   : 			UnloadModule();

  0010d	48 8b 8c 24 70
	04 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00115	e8 00 00 00 00	 call	 ?UnloadModule@CKISSApiWrapper@mu2@@AEAAXXZ ; mu2::CKISSApiWrapper::UnloadModule

; 76   : 
; 77   : 			return dwErr;

  0011a	8b 44 24 30	 mov	 eax, DWORD PTR dwErr$1[rsp]
  0011e	eb 19		 jmp	 SHORT $LN1@LoadModule
$LN6@LoadModule:

; 78   : 		}
; 79   : 
; 80   : 		m_pMainHandle = m_fpGetHandle();

  00120	48 8b 84 24 70
	04 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00128	ff 50 18	 call	 QWORD PTR [rax+24]
  0012b	48 8b 8c 24 70
	04 00 00	 mov	 rcx, QWORD PTR this$[rsp]
  00133	48 89 41 10	 mov	 QWORD PTR [rcx+16], rax

; 81   : 
; 82   : 		return ERROR_SUCCESS;

  00137	33 c0		 xor	 eax, eax
$LN1@LoadModule:

; 83   : 	}

  00139	48 8b 8c 24 50
	04 00 00	 mov	 rcx, QWORD PTR __$ArrayPad$[rsp]
  00141	48 33 cc	 xor	 rcx, rsp
  00144	e8 00 00 00 00	 call	 __security_check_cookie
  00149	48 81 c4 60 04
	00 00		 add	 rsp, 1120		; 00000460H
  00150	5f		 pop	 rdi
  00151	c3		 ret	 0
?LoadModule@CKISSApiWrapper@mu2@@AEAAKPEB_W@Z ENDP	; mu2::CKISSApiWrapper::LoadModule
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\KISS\KISSApiWrapper.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\KISS\KISSApiWrapper.cpp
;	COMDAT ??0CKISSApiWrapper@mu2@@AEAA@XZ
_TEXT	SEGMENT
this$ = 8
??0CKISSApiWrapper@mu2@@AEAA@XZ PROC			; mu2::CKISSApiWrapper::CKISSApiWrapper, COMDAT

; 28   : 	{

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 84   : 	ISingleton() {}	

  00005	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0000a	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@6B@
  00011	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\KISS\KISSApiWrapper.cpp

; 28   : 	{

  00014	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00019	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7CKISSApiWrapper@mu2@@6B@
  00020	48 89 08	 mov	 QWORD PTR [rax], rcx

; 25   : 		: m_hLib(NULL)

  00023	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00028	48 c7 40 08 00
	00 00 00	 mov	 QWORD PTR [rax+8], 0

; 26   : 		, m_pMainHandle(NULL)

  00030	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00035	48 c7 40 10 00
	00 00 00	 mov	 QWORD PTR [rax+16], 0

; 27   : 		, m_fpGetHandle(NULL)

  0003d	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  00042	48 c7 40 18 00
	00 00 00	 mov	 QWORD PTR [rax+24], 0

; 29   : 	}

  0004a	48 8b 44 24 08	 mov	 rax, QWORD PTR this$[rsp]
  0004f	c3		 ret	 0
??0CKISSApiWrapper@mu2@@AEAA@XZ ENDP			; mu2::CKISSApiWrapper::CKISSApiWrapper
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\KISS\KISSApiWrapper.cpp
;	COMDAT ?GetStringW@CKISSApiWrapper@mu2@@QEAAKPEAU_Notify_Info_StrW@@@Z
_TEXT	SEGMENT
this$ = 48
_lpInfo$ = 56
?GetStringW@CKISSApiWrapper@mu2@@QEAAKPEAU_Notify_Info_StrW@@@Z PROC ; mu2::CKISSApiWrapper::GetStringW, COMDAT

; 211  : 	{

$LN5:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 212  : 		if (NULL == m_pMainHandle ||

  0000e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00013	48 83 78 10 00	 cmp	 QWORD PTR [rax+16], 0
  00018	74 10		 je	 SHORT $LN3@GetStringW
  0001a	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001f	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00023	48 83 78 40 00	 cmp	 QWORD PTR [rax+64], 0
  00028	75 07		 jne	 SHORT $LN2@GetStringW
$LN3@GetStringW:

; 213  : 			NULL == m_pMainHandle->lpfnGetStringW)
; 214  : 		{
; 215  : 			return ERROR_INVALID_HANDLE;

  0002a	b8 06 00 00 00	 mov	 eax, 6
  0002f	eb 11		 jmp	 SHORT $LN1@GetStringW
$LN2@GetStringW:

; 216  : 		}
; 217  : 
; 218  : 		return m_pMainHandle->lpfnGetStringW(_lpInfo);

  00031	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00036	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0003a	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _lpInfo$[rsp]
  0003f	ff 50 40	 call	 QWORD PTR [rax+64]
$LN1@GetStringW:

; 219  : 	}

  00042	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00046	c3		 ret	 0
?GetStringW@CKISSApiWrapper@mu2@@QEAAKPEAU_Notify_Info_StrW@@@Z ENDP ; mu2::CKISSApiWrapper::GetStringW
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\KISS\KISSApiWrapper.cpp
;	COMDAT ?GetNumber@CKISSApiWrapper@mu2@@QEAAKPEAU_Notify_Info_Num@@@Z
_TEXT	SEGMENT
this$ = 48
_lpInfo$ = 56
?GetNumber@CKISSApiWrapper@mu2@@QEAAKPEAU_Notify_Info_Num@@@Z PROC ; mu2::CKISSApiWrapper::GetNumber, COMDAT

; 200  : 	{

$LN5:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 201  : 		if (NULL == m_pMainHandle ||

  0000e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00013	48 83 78 10 00	 cmp	 QWORD PTR [rax+16], 0
  00018	74 10		 je	 SHORT $LN3@GetNumber
  0001a	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001f	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00023	48 83 78 38 00	 cmp	 QWORD PTR [rax+56], 0
  00028	75 07		 jne	 SHORT $LN2@GetNumber
$LN3@GetNumber:

; 202  : 			NULL == m_pMainHandle->lpfnGetNumber)
; 203  : 		{
; 204  : 			return ERROR_INVALID_HANDLE;

  0002a	b8 06 00 00 00	 mov	 eax, 6
  0002f	eb 11		 jmp	 SHORT $LN1@GetNumber
$LN2@GetNumber:

; 205  : 		}
; 206  : 
; 207  : 		return m_pMainHandle->lpfnGetNumber(_lpInfo);

  00031	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00036	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0003a	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _lpInfo$[rsp]
  0003f	ff 50 38	 call	 QWORD PTR [rax+56]
$LN1@GetNumber:

; 208  : 	}

  00042	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00046	c3		 ret	 0
?GetNumber@CKISSApiWrapper@mu2@@QEAAKPEAU_Notify_Info_Num@@@Z ENDP ; mu2::CKISSApiWrapper::GetNumber
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\KISS\KISSApiWrapper.cpp
;	COMDAT ?NotifyConcurrentUserCount@CKISSApiWrapper@mu2@@QEAAKKM@Z
_TEXT	SEGMENT
info$ = 48
__$ArrayPad$ = 88
this$ = 112
_dwPortNo$ = 120
_fVal$ = 128
?NotifyConcurrentUserCount@CKISSApiWrapper@mu2@@QEAAKKM@Z PROC ; mu2::CKISSApiWrapper::NotifyConcurrentUserCount, COMDAT

; 186  : 	{

$LN5:
  00000	f3 0f 11 54 24
	18		 movss	 DWORD PTR [rsp+24], xmm2
  00006	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 68	 sub	 rsp, 104		; 00000068H
  00013	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR __security_cookie
  0001a	48 33 c4	 xor	 rax, rsp
  0001d	48 89 44 24 58	 mov	 QWORD PTR __$ArrayPad$[rsp], rax

; 187  : 		if (NULL == m_pMainHandle ||

  00022	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  00027	48 83 78 10 00	 cmp	 QWORD PTR [rax+16], 0
  0002c	74 10		 je	 SHORT $LN3@NotifyConc
  0002e	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  00033	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00037	48 83 78 20 00	 cmp	 QWORD PTR [rax+32], 0
  0003c	75 07		 jne	 SHORT $LN2@NotifyConc
$LN3@NotifyConc:

; 188  : 			NULL == m_pMainHandle->lpfnNotifyNumber)
; 189  : 		{
; 190  : 			return ERROR_INVALID_HANDLE;

  0003e	b8 06 00 00 00	 mov	 eax, 6
  00043	eb 47		 jmp	 SHORT $LN1@NotifyConc
$LN2@NotifyConc:

; 191  : 		}
; 192  : 
; 193  : 		NOTIFY_INFO_NUM info;
; 194  : 		InitNotifyInfoNumber(&info, KAC_CONC_USER_COUNT, _fVal, MEANINGLESS_VALUE, MEANINGLESS_VALUE, _dwPortNo);

  00045	8b 44 24 78	 mov	 eax, DWORD PTR _dwPortNo$[rsp]
  00049	89 44 24 28	 mov	 DWORD PTR [rsp+40], eax
  0004d	f3 0f 10 05 00
	00 00 00	 movss	 xmm0, DWORD PTR __real@c7c34f80
  00055	f3 0f 11 44 24
	20		 movss	 DWORD PTR [rsp+32], xmm0
  0005b	f3 0f 10 1d 00
	00 00 00	 movss	 xmm3, DWORD PTR __real@c7c34f80
  00063	f3 0f 10 94 24
	80 00 00 00	 movss	 xmm2, DWORD PTR _fVal$[rsp]
  0006c	ba 02 00 00 00	 mov	 edx, 2
  00071	48 8d 4c 24 30	 lea	 rcx, QWORD PTR info$[rsp]
  00076	e8 00 00 00 00	 call	 ?InitNotifyInfoNumber@mu2@@YAXPEAU_Notify_Info_Num@@HMMMK@Z ; mu2::InitNotifyInfoNumber

; 195  : 
; 196  : 		return m_pMainHandle->lpfnNotifyNumber(&info);

  0007b	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  00080	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00084	48 8d 4c 24 30	 lea	 rcx, QWORD PTR info$[rsp]
  00089	ff 50 20	 call	 QWORD PTR [rax+32]
$LN1@NotifyConc:

; 197  : 	}

  0008c	48 8b 4c 24 58	 mov	 rcx, QWORD PTR __$ArrayPad$[rsp]
  00091	48 33 cc	 xor	 rcx, rsp
  00094	e8 00 00 00 00	 call	 __security_check_cookie
  00099	48 83 c4 68	 add	 rsp, 104		; 00000068H
  0009d	c3		 ret	 0
?NotifyConcurrentUserCount@CKISSApiWrapper@mu2@@QEAAKKM@Z ENDP ; mu2::CKISSApiWrapper::NotifyConcurrentUserCount
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\KISS\KISSApiWrapper.cpp
;	COMDAT ?NotifyHeartbeat@CKISSApiWrapper@mu2@@QEAAKXZ
_TEXT	SEGMENT
info$ = 48
__$ArrayPad$ = 88
this$ = 112
?NotifyHeartbeat@CKISSApiWrapper@mu2@@QEAAKXZ PROC	; mu2::CKISSApiWrapper::NotifyHeartbeat, COMDAT

; 172  : 	{

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 68	 sub	 rsp, 104		; 00000068H
  00009	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR __security_cookie
  00010	48 33 c4	 xor	 rax, rsp
  00013	48 89 44 24 58	 mov	 QWORD PTR __$ArrayPad$[rsp], rax

; 173  : 		if (NULL == m_pMainHandle ||

  00018	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  0001d	48 83 78 10 00	 cmp	 QWORD PTR [rax+16], 0
  00022	74 10		 je	 SHORT $LN3@NotifyHear
  00024	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  00029	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0002d	48 83 78 20 00	 cmp	 QWORD PTR [rax+32], 0
  00032	75 07		 jne	 SHORT $LN2@NotifyHear
$LN3@NotifyHear:

; 174  : 			NULL == m_pMainHandle->lpfnNotifyNumber)
; 175  : 		{
; 176  : 			return ERROR_INVALID_HANDLE;

  00034	b8 06 00 00 00	 mov	 eax, 6
  00039	eb 41		 jmp	 SHORT $LN1@NotifyHear
$LN2@NotifyHear:

; 177  : 		}
; 178  : 
; 179  : 		NOTIFY_INFO_NUM info;
; 180  : 		InitNotifyInfoNumber(&info, KAC_HEART_BEAT, 0);

  0003b	c7 44 24 28 00
	00 00 00	 mov	 DWORD PTR [rsp+40], 0
  00043	f3 0f 10 05 00
	00 00 00	 movss	 xmm0, DWORD PTR __real@c7c34f80
  0004b	f3 0f 11 44 24
	20		 movss	 DWORD PTR [rsp+32], xmm0
  00051	f3 0f 10 1d 00
	00 00 00	 movss	 xmm3, DWORD PTR __real@c7c34f80
  00059	0f 57 d2	 xorps	 xmm2, xmm2
  0005c	ba 01 00 00 00	 mov	 edx, 1
  00061	48 8d 4c 24 30	 lea	 rcx, QWORD PTR info$[rsp]
  00066	e8 00 00 00 00	 call	 ?InitNotifyInfoNumber@mu2@@YAXPEAU_Notify_Info_Num@@HMMMK@Z ; mu2::InitNotifyInfoNumber

; 181  : 
; 182  : 		return m_pMainHandle->lpfnNotifyNumber(&info);

  0006b	48 8b 44 24 70	 mov	 rax, QWORD PTR this$[rsp]
  00070	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00074	48 8d 4c 24 30	 lea	 rcx, QWORD PTR info$[rsp]
  00079	ff 50 20	 call	 QWORD PTR [rax+32]
$LN1@NotifyHear:

; 183  : 	}

  0007c	48 8b 4c 24 58	 mov	 rcx, QWORD PTR __$ArrayPad$[rsp]
  00081	48 33 cc	 xor	 rcx, rsp
  00084	e8 00 00 00 00	 call	 __security_check_cookie
  00089	48 83 c4 68	 add	 rsp, 104		; 00000068H
  0008d	c3		 ret	 0
?NotifyHeartbeat@CKISSApiWrapper@mu2@@QEAAKXZ ENDP	; mu2::CKISSApiWrapper::NotifyHeartbeat
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\KISS\KISSApiWrapper.cpp
;	COMDAT ?NotifyStringW@CKISSApiWrapper@mu2@@QEAAKQEAU_Notify_Info_StrW@@@Z
_TEXT	SEGMENT
this$ = 48
_lpInfo$ = 56
?NotifyStringW@CKISSApiWrapper@mu2@@QEAAKQEAU_Notify_Info_StrW@@@Z PROC ; mu2::CKISSApiWrapper::NotifyStringW, COMDAT

; 161  : 	{

$LN5:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 162  : 		if (NULL == m_pMainHandle ||

  0000e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00013	48 83 78 10 00	 cmp	 QWORD PTR [rax+16], 0
  00018	74 10		 je	 SHORT $LN3@NotifyStri
  0001a	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001f	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00023	48 83 78 30 00	 cmp	 QWORD PTR [rax+48], 0
  00028	75 07		 jne	 SHORT $LN2@NotifyStri
$LN3@NotifyStri:

; 163  : 			NULL == m_pMainHandle->lpfnNotifyStringW)
; 164  : 		{
; 165  : 			return ERROR_INVALID_HANDLE;

  0002a	b8 06 00 00 00	 mov	 eax, 6
  0002f	eb 11		 jmp	 SHORT $LN1@NotifyStri
$LN2@NotifyStri:

; 166  : 		}
; 167  : 
; 168  : 		return m_pMainHandle->lpfnNotifyStringW(_lpInfo);

  00031	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00036	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0003a	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _lpInfo$[rsp]
  0003f	ff 50 30	 call	 QWORD PTR [rax+48]
$LN1@NotifyStri:

; 169  : 	}

  00042	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00046	c3		 ret	 0
?NotifyStringW@CKISSApiWrapper@mu2@@QEAAKQEAU_Notify_Info_StrW@@@Z ENDP ; mu2::CKISSApiWrapper::NotifyStringW
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\KISS\KISSApiWrapper.cpp
;	COMDAT ?NotifyStringA@CKISSApiWrapper@mu2@@QEAAKQEAU_Notify_Info_StrA@@@Z
_TEXT	SEGMENT
info$ = 32
__$ArrayPad$ = 1088
this$ = 1120
_lpInfo$ = 1128
?NotifyStringA@CKISSApiWrapper@mu2@@QEAAKQEAU_Notify_Info_StrA@@@Z PROC ; mu2::CKISSApiWrapper::NotifyStringA, COMDAT

; 147  : 	{

$LN5:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 81 ec 58 04
	00 00		 sub	 rsp, 1112		; 00000458H
  00011	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR __security_cookie
  00018	48 33 c4	 xor	 rax, rsp
  0001b	48 89 84 24 40
	04 00 00	 mov	 QWORD PTR __$ArrayPad$[rsp], rax

; 148  : 		if (NULL == m_pMainHandle ||

  00023	48 8b 84 24 60
	04 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0002b	48 83 78 10 00	 cmp	 QWORD PTR [rax+16], 0
  00030	74 13		 je	 SHORT $LN3@NotifyStri
  00032	48 8b 84 24 60
	04 00 00	 mov	 rax, QWORD PTR this$[rsp]
  0003a	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0003e	48 83 78 30 00	 cmp	 QWORD PTR [rax+48], 0
  00043	75 07		 jne	 SHORT $LN2@NotifyStri
$LN3@NotifyStri:

; 149  : 			NULL == m_pMainHandle->lpfnNotifyStringW)
; 150  : 		{
; 151  : 			return ERROR_INVALID_HANDLE;

  00045	b8 06 00 00 00	 mov	 eax, 6
  0004a	eb 26		 jmp	 SHORT $LN1@NotifyStri
$LN2@NotifyStri:

; 152  : 		}
; 153  : 
; 154  : 		NOTIFY_INFO_STRW info;
; 155  : 		ConvertNotifyInfoStringAtoW(_lpInfo, &info);

  0004c	48 8d 54 24 20	 lea	 rdx, QWORD PTR info$[rsp]
  00051	48 8b 8c 24 68
	04 00 00	 mov	 rcx, QWORD PTR _lpInfo$[rsp]
  00059	e8 00 00 00 00	 call	 ?ConvertNotifyInfoStringAtoW@mu2@@YAXQEAU_Notify_Info_StrA@@PEAU_Notify_Info_StrW@@@Z ; mu2::ConvertNotifyInfoStringAtoW

; 156  : 
; 157  : 		return m_pMainHandle->lpfnNotifyStringW(&info);

  0005e	48 8b 84 24 60
	04 00 00	 mov	 rax, QWORD PTR this$[rsp]
  00066	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0006a	48 8d 4c 24 20	 lea	 rcx, QWORD PTR info$[rsp]
  0006f	ff 50 30	 call	 QWORD PTR [rax+48]
$LN1@NotifyStri:

; 158  : 	}

  00072	48 8b 8c 24 40
	04 00 00	 mov	 rcx, QWORD PTR __$ArrayPad$[rsp]
  0007a	48 33 cc	 xor	 rcx, rsp
  0007d	e8 00 00 00 00	 call	 __security_check_cookie
  00082	48 81 c4 58 04
	00 00		 add	 rsp, 1112		; 00000458H
  00089	c3		 ret	 0
?NotifyStringA@CKISSApiWrapper@mu2@@QEAAKQEAU_Notify_Info_StrA@@@Z ENDP ; mu2::CKISSApiWrapper::NotifyStringA
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\KISS\KISSApiWrapper.cpp
;	COMDAT ?NotifyNumber@CKISSApiWrapper@mu2@@QEAAKQEAU_Notify_Info_Num@@@Z
_TEXT	SEGMENT
this$ = 48
_lpInfo$ = 56
?NotifyNumber@CKISSApiWrapper@mu2@@QEAAKQEAU_Notify_Info_Num@@@Z PROC ; mu2::CKISSApiWrapper::NotifyNumber, COMDAT

; 136  : 	{

$LN5:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 137  : 		if (NULL == m_pMainHandle ||

  0000e	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00013	48 83 78 10 00	 cmp	 QWORD PTR [rax+16], 0
  00018	74 10		 je	 SHORT $LN3@NotifyNumb
  0001a	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001f	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00023	48 83 78 20 00	 cmp	 QWORD PTR [rax+32], 0
  00028	75 07		 jne	 SHORT $LN2@NotifyNumb
$LN3@NotifyNumb:

; 138  : 			NULL == m_pMainHandle->lpfnNotifyNumber)
; 139  : 		{
; 140  : 			return ERROR_INVALID_HANDLE;

  0002a	b8 06 00 00 00	 mov	 eax, 6
  0002f	eb 11		 jmp	 SHORT $LN1@NotifyNumb
$LN2@NotifyNumb:

; 141  : 		}
; 142  : 
; 143  : 		return m_pMainHandle->lpfnNotifyNumber(_lpInfo);

  00031	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00036	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0003a	48 8b 4c 24 38	 mov	 rcx, QWORD PTR _lpInfo$[rsp]
  0003f	ff 50 20	 call	 QWORD PTR [rax+32]
$LN1@NotifyNumb:

; 144  : 	}

  00042	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00046	c3		 ret	 0
?NotifyNumber@CKISSApiWrapper@mu2@@QEAAKQEAU_Notify_Info_Num@@@Z ENDP ; mu2::CKISSApiWrapper::NotifyNumber
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\KISS\KISSApiWrapper.cpp
;	COMDAT ?IsOpened@CKISSApiWrapper@mu2@@QEBA_NXZ
_TEXT	SEGMENT
this$ = 48
?IsOpened@CKISSApiWrapper@mu2@@QEBA_NXZ PROC		; mu2::CKISSApiWrapper::IsOpened, COMDAT

; 125  : 	{

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 126  : 		if (NULL == m_pMainHandle ||

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 78 10 00	 cmp	 QWORD PTR [rax+16], 0
  00013	74 10		 je	 SHORT $LN3@IsOpened
  00015	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0001e	48 83 78 18 00	 cmp	 QWORD PTR [rax+24], 0
  00023	75 04		 jne	 SHORT $LN2@IsOpened
$LN3@IsOpened:

; 127  : 			NULL == m_pMainHandle->lpfnIsOpened)
; 128  : 		{
; 129  : 			return false;

  00025	32 c0		 xor	 al, al
  00027	eb 0c		 jmp	 SHORT $LN1@IsOpened
$LN2@IsOpened:

; 130  : 		}
; 131  : 
; 132  : 		return m_pMainHandle->lpfnIsOpened();

  00029	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0002e	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00032	ff 50 18	 call	 QWORD PTR [rax+24]
$LN1@IsOpened:

; 133  : 	}

  00035	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00039	c3		 ret	 0
?IsOpened@CKISSApiWrapper@mu2@@QEBA_NXZ ENDP		; mu2::CKISSApiWrapper::IsOpened
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\KISS\KISSApiWrapper.cpp
;	COMDAT ?Close@CKISSApiWrapper@mu2@@QEAAXXZ
_TEXT	SEGMENT
this$ = 48
?Close@CKISSApiWrapper@mu2@@QEAAXXZ PROC		; mu2::CKISSApiWrapper::Close, COMDAT

; 114  : 	{

$LN5:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 115  : 		if (NULL != m_pMainHandle)

  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 83 78 10 00	 cmp	 QWORD PTR [rax+16], 0
  00013	74 1d		 je	 SHORT $LN2@Close

; 116  : 		{
; 117  : 			if (NULL != m_pMainHandle->lpfnClose)

  00015	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0001a	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0001e	48 83 78 08 00	 cmp	 QWORD PTR [rax+8], 0
  00023	74 0d		 je	 SHORT $LN3@Close

; 118  : 				m_pMainHandle->lpfnClose();

  00025	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0002a	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  0002e	ff 50 08	 call	 QWORD PTR [rax+8]
  00031	90		 npad	 1
$LN3@Close:
$LN2@Close:

; 119  : 		}
; 120  : 
; 121  : 		UnloadModule();

  00032	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00037	e8 00 00 00 00	 call	 ?UnloadModule@CKISSApiWrapper@mu2@@AEAAXXZ ; mu2::CKISSApiWrapper::UnloadModule
  0003c	90		 npad	 1

; 122  : 	}

  0003d	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00041	c3		 ret	 0
?Close@CKISSApiWrapper@mu2@@QEAAXXZ ENDP		; mu2::CKISSApiWrapper::Close
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\KISS\KISSApiWrapper.cpp
;	COMDAT ?Open@CKISSApiWrapper@mu2@@QEAAIPEB_W@Z
_TEXT	SEGMENT
dwRet$ = 32
this$ = 64
_lpszPath$ = 72
?Open@CKISSApiWrapper@mu2@@QEAAIPEB_W@Z PROC		; mu2::CKISSApiWrapper::Open, COMDAT

; 98   : 	{

$LN6:
  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	48 83 ec 38	 sub	 rsp, 56			; 00000038H

; 99   : 		DWORD dwRet = LoadModule(_lpszPath);

  0000e	48 8b 54 24 48	 mov	 rdx, QWORD PTR _lpszPath$[rsp]
  00013	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00018	e8 00 00 00 00	 call	 ?LoadModule@CKISSApiWrapper@mu2@@AEAAKPEB_W@Z ; mu2::CKISSApiWrapper::LoadModule
  0001d	89 44 24 20	 mov	 DWORD PTR dwRet$[rsp], eax

; 100  : 
; 101  : 		if (ERROR_SUCCESS != dwRet)

  00021	83 7c 24 20 00	 cmp	 DWORD PTR dwRet$[rsp], 0
  00026	74 06		 je	 SHORT $LN2@Open

; 102  : 			return dwRet;

  00028	8b 44 24 20	 mov	 eax, DWORD PTR dwRet$[rsp]
  0002c	eb 2d		 jmp	 SHORT $LN1@Open
$LN2@Open:

; 103  : 
; 104  : 		if (NULL == m_pMainHandle ||

  0002e	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00033	48 83 78 10 00	 cmp	 QWORD PTR [rax+16], 0
  00038	74 0f		 je	 SHORT $LN4@Open
  0003a	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  0003f	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00043	48 83 38 00	 cmp	 QWORD PTR [rax], 0
  00047	75 07		 jne	 SHORT $LN3@Open
$LN4@Open:

; 105  : 			NULL == m_pMainHandle->lpfnOpen)
; 106  : 		{
; 107  : 			return ERROR_INVALID_HANDLE;

  00049	b8 06 00 00 00	 mov	 eax, 6
  0004e	eb 0b		 jmp	 SHORT $LN1@Open
$LN3@Open:

; 108  : 		}
; 109  : 
; 110  : 		return m_pMainHandle->lpfnOpen();

  00050	48 8b 44 24 40	 mov	 rax, QWORD PTR this$[rsp]
  00055	48 8b 40 10	 mov	 rax, QWORD PTR [rax+16]
  00059	ff 10		 call	 QWORD PTR [rax]
$LN1@Open:

; 111  : 	}

  0005b	48 83 c4 38	 add	 rsp, 56			; 00000038H
  0005f	c3		 ret	 0
?Open@CKISSApiWrapper@mu2@@QEAAIPEB_W@Z ENDP		; mu2::CKISSApiWrapper::Open
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\KISS\KISSApiWrapper.cpp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldServer.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\KISS\KISSApiWrapper.cpp
;	COMDAT ?UnInit@CKISSApiWrapper@mu2@@UEAA_NXZ
_TEXT	SEGMENT
$T1 = 32
this$ = 64
?UnInit@CKISSApiWrapper@mu2@@UEAA_NXZ PROC		; mu2::CKISSApiWrapper::UnInit, COMDAT

; 43   : 	{

$LN6:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 38	 sub	 rsp, 56			; 00000038H
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\WorldServer.h

; 115  : 	const WorldInformation& GetWorldInformation() const {return m_worldInfo; }

  00009	48 8b 05 00 00
	00 00		 mov	 rax, QWORD PTR ?Instance@Server@mu2@@2PEAV12@EA ; mu2::Server::Instance
  00010	48 05 00 04 00
	00		 add	 rax, 1024		; 00000400H
  00016	48 89 44 24 20	 mov	 QWORD PTR $T1[rsp], rax
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\KISS\KISSApiWrapper.cpp

; 44   : 		if (SERVER.GetWorldInformation().UseKiss == true)

  0001b	48 8b 44 24 20	 mov	 rax, QWORD PTR $T1[rsp]
  00020	0f b6 80 12 01
	00 00		 movzx	 eax, BYTE PTR [rax+274]
  00027	83 f8 01	 cmp	 eax, 1
  0002a	75 0b		 jne	 SHORT $LN2@UnInit

; 45   : 		{
; 46   : 			Close();

  0002c	48 8b 4c 24 40	 mov	 rcx, QWORD PTR this$[rsp]
  00031	e8 00 00 00 00	 call	 ?Close@CKISSApiWrapper@mu2@@QEAAXXZ ; mu2::CKISSApiWrapper::Close
  00036	90		 npad	 1
$LN2@UnInit:

; 47   : 		}
; 48   : 
; 49   : 		return true;

  00037	b0 01		 mov	 al, 1

; 50   : 	}

  00039	48 83 c4 38	 add	 rsp, 56			; 00000038H
  0003d	c3		 ret	 0
?UnInit@CKISSApiWrapper@mu2@@UEAA_NXZ ENDP		; mu2::CKISSApiWrapper::UnInit
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\KISS\KISSApiWrapper.cpp
;	COMDAT ?Init@CKISSApiWrapper@mu2@@UEAA_NPEAX@Z
_TEXT	SEGMENT
this$ = 8
param$ = 16
?Init@CKISSApiWrapper@mu2@@UEAA_NPEAX@Z PROC		; mu2::CKISSApiWrapper::Init, COMDAT

; 37   : 	{		

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx

; 38   : 		UNREFERENCED_PARAMETER(param);
; 39   : 		return true;

  0000a	b0 01		 mov	 al, 1

; 40   : 	}

  0000c	c3		 ret	 0
?Init@CKISSApiWrapper@mu2@@UEAA_NPEAX@Z ENDP		; mu2::CKISSApiWrapper::Init
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\KISS\KISSApiWrapper.cpp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\KISS\KISSApiWrapper.cpp
;	COMDAT ??1CKISSApiWrapper@mu2@@UEAA@XZ
_TEXT	SEGMENT
this$ = 48
??1CKISSApiWrapper@mu2@@UEAA@XZ PROC			; mu2::CKISSApiWrapper::~CKISSApiWrapper, COMDAT

; 32   : 	{

$LN9:
  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	48 83 ec 28	 sub	 rsp, 40			; 00000028H
  00009	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0000e	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7CKISSApiWrapper@mu2@@6B@
  00015	48 89 08	 mov	 QWORD PTR [rax], rcx

; 33   : 		Close();

  00018	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  0001d	e8 00 00 00 00	 call	 ?Close@CKISSApiWrapper@mu2@@QEAAXXZ ; mu2::CKISSApiWrapper::Close
  00022	90		 npad	 1
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h

; 80   : 	virtual~ISingleton() {}

  00023	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00028	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@6B@
  0002f	48 89 08	 mov	 QWORD PTR [rax], rcx
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\KISS\KISSApiWrapper.cpp

; 34   : 	}

  00032	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00036	c3		 ret	 0
??1CKISSApiWrapper@mu2@@UEAA@XZ ENDP			; mu2::CKISSApiWrapper::~CKISSApiWrapper
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Framework\Core\Singleton.h
;	COMDAT ??_G?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@UEAAPEAXI@Z
_TEXT	SEGMENT
this$ = 48
__flags$ = 56
??_G?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@UEAAPEAXI@Z PROC ; mu2::ISingleton<mu2::CKISSApiWrapper>::`scalar deleting destructor', COMDAT
$LN10:
  00000	89 54 24 10	 mov	 DWORD PTR [rsp+16], edx
  00004	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00009	48 83 ec 28	 sub	 rsp, 40			; 00000028H

; 80   : 	virtual~ISingleton() {}

  0000d	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  00012	48 8d 0d 00 00
	00 00		 lea	 rcx, OFFSET FLAT:??_7?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@6B@
  00019	48 89 08	 mov	 QWORD PTR [rax], rcx
  0001c	8b 44 24 38	 mov	 eax, DWORD PTR __flags$[rsp]
  00020	83 e0 01	 and	 eax, 1
  00023	85 c0		 test	 eax, eax
  00025	74 10		 je	 SHORT $LN2@scalar
  00027	ba 08 00 00 00	 mov	 edx, 8
  0002c	48 8b 4c 24 30	 mov	 rcx, QWORD PTR this$[rsp]
  00031	e8 00 00 00 00	 call	 ??3@YAXPEAX_K@Z		; operator delete
  00036	90		 npad	 1
$LN2@scalar:
  00037	48 8b 44 24 30	 mov	 rax, QWORD PTR this$[rsp]
  0003c	48 83 c4 28	 add	 rsp, 40			; 00000028H
  00040	c3		 ret	 0
??_G?$ISingleton@VCKISSApiWrapper@mu2@@@mu2@@UEAAPEAXI@Z ENDP ; mu2::ISingleton<mu2::CKISSApiWrapper>::`scalar deleting destructor'
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\strsafe.h
;	COMDAT ?StringCchPrintfW@@YAJPEA_W_KPEB_WZZ
_TEXT	SEGMENT
hr$ = 48
hr$1 = 52
$T2 = 56
argList$3 = 64
pszDest$ = 96
cchDest$ = 104
pszFormat$ = 112
?StringCchPrintfW@@YAJPEA_W_KPEB_WZZ PROC		; StringCchPrintfW, COMDAT

; 5050 : {

$LN24:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	4c 89 4c 24 20	 mov	 QWORD PTR [rsp+32], r9
  00014	48 83 ec 58	 sub	 rsp, 88			; 00000058H

; 9721 :     HRESULT hr = S_OK;

  00018	c7 44 24 34 00
	00 00 00	 mov	 DWORD PTR hr$1[rsp], 0

; 9722 : 
; 9723 :     if ((cchDest == 0) || (cchDest > cchMax))

  00020	48 83 7c 24 68
	00		 cmp	 QWORD PTR cchDest$[rsp], 0
  00026	74 0b		 je	 SHORT $LN8@StringCchP
  00028	48 81 7c 24 68
	ff ff ff 7f	 cmp	 QWORD PTR cchDest$[rsp], 2147483647 ; 7fffffffH
  00031	76 08		 jbe	 SHORT $LN7@StringCchP
$LN8@StringCchP:

; 9724 :     {
; 9725 :         hr = STRSAFE_E_INVALID_PARAMETER;

  00033	c7 44 24 34 57
	00 07 80	 mov	 DWORD PTR hr$1[rsp], -2147024809 ; ffffffff80070057H
$LN7@StringCchP:

; 9726 :     }
; 9727 : 
; 9728 :     return hr;

  0003b	8b 44 24 34	 mov	 eax, DWORD PTR hr$1[rsp]
  0003f	89 44 24 38	 mov	 DWORD PTR $T2[rsp], eax

; 5051 :     HRESULT hr;
; 5052 : 
; 5053 :     hr = StringValidateDestW(pszDest, cchDest, STRSAFE_MAX_CCH);

  00043	8b 44 24 38	 mov	 eax, DWORD PTR $T2[rsp]
  00047	89 44 24 30	 mov	 DWORD PTR hr$[rsp], eax

; 5054 : 
; 5055 :     if (SUCCEEDED(hr))

  0004b	83 7c 24 30 00	 cmp	 DWORD PTR hr$[rsp], 0
  00050	7c 3a		 jl	 SHORT $LN2@StringCchP

; 5056 :     {
; 5057 :         va_list argList;
; 5058 : 
; 5059 :         va_start(argList, pszFormat);

  00052	48 8d 44 24 78	 lea	 rax, QWORD PTR pszFormat$[rsp+8]
  00057	48 89 44 24 40	 mov	 QWORD PTR argList$3[rsp], rax

; 5060 : 
; 5061 :         hr = StringVPrintfWorkerW(pszDest,

  0005c	48 8b 44 24 40	 mov	 rax, QWORD PTR argList$3[rsp]
  00061	48 89 44 24 20	 mov	 QWORD PTR [rsp+32], rax
  00066	4c 8b 4c 24 70	 mov	 r9, QWORD PTR pszFormat$[rsp]
  0006b	45 33 c0	 xor	 r8d, r8d
  0006e	48 8b 54 24 68	 mov	 rdx, QWORD PTR cchDest$[rsp]
  00073	48 8b 4c 24 60	 mov	 rcx, QWORD PTR pszDest$[rsp]
  00078	e8 00 00 00 00	 call	 ?StringVPrintfWorkerW@@YAJPEA_W_KPEA_KPEB_WPEAD@Z ; StringVPrintfWorkerW
  0007d	89 44 24 30	 mov	 DWORD PTR hr$[rsp], eax

; 5062 :                 cchDest,
; 5063 :                 NULL,
; 5064 :                 pszFormat,
; 5065 :                 argList);
; 5066 : 
; 5067 :         va_end(argList);

  00081	48 c7 44 24 40
	00 00 00 00	 mov	 QWORD PTR argList$3[rsp], 0

; 5068 :     }

  0008a	eb 12		 jmp	 SHORT $LN3@StringCchP
$LN2@StringCchP:

; 5069 :     else if (cchDest > 0)

  0008c	48 83 7c 24 68
	00		 cmp	 QWORD PTR cchDest$[rsp], 0
  00092	76 0a		 jbe	 SHORT $LN4@StringCchP

; 5070 :     {
; 5071 :         *pszDest = '\0';

  00094	33 c0		 xor	 eax, eax
  00096	48 8b 4c 24 60	 mov	 rcx, QWORD PTR pszDest$[rsp]
  0009b	66 89 01	 mov	 WORD PTR [rcx], ax
$LN4@StringCchP:
$LN3@StringCchP:

; 5072 :     }
; 5073 : 
; 5074 :     return hr;

  0009e	8b 44 24 30	 mov	 eax, DWORD PTR hr$[rsp]

; 5075 : }

  000a2	48 83 c4 58	 add	 rsp, 88			; 00000058H
  000a6	c3		 ret	 0
?StringCchPrintfW@@YAJPEA_W_KPEB_WZZ ENDP		; StringCchPrintfW
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\strsafe.h
;	COMDAT ?StringCchCopyW@@YAJPEA_W_KPEB_W@Z
_TEXT	SEGMENT
hr$ = 48
hr$1 = 52
$T2 = 56
pszDest$ = 80
cchDest$ = 88
pszSrc$ = 96
?StringCchCopyW@@YAJPEA_W_KPEB_W@Z PROC			; StringCchCopyW, COMDAT

; 570  : {

$LN16:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 9721 :     HRESULT hr = S_OK;

  00013	c7 44 24 34 00
	00 00 00	 mov	 DWORD PTR hr$1[rsp], 0

; 9722 : 
; 9723 :     if ((cchDest == 0) || (cchDest > cchMax))

  0001b	48 83 7c 24 58
	00		 cmp	 QWORD PTR cchDest$[rsp], 0
  00021	74 0b		 je	 SHORT $LN8@StringCchC
  00023	48 81 7c 24 58
	ff ff ff 7f	 cmp	 QWORD PTR cchDest$[rsp], 2147483647 ; 7fffffffH
  0002c	76 08		 jbe	 SHORT $LN7@StringCchC
$LN8@StringCchC:

; 9724 :     {
; 9725 :         hr = STRSAFE_E_INVALID_PARAMETER;

  0002e	c7 44 24 34 57
	00 07 80	 mov	 DWORD PTR hr$1[rsp], -2147024809 ; ffffffff80070057H
$LN7@StringCchC:

; 9726 :     }
; 9727 : 
; 9728 :     return hr;

  00036	8b 44 24 34	 mov	 eax, DWORD PTR hr$1[rsp]
  0003a	89 44 24 38	 mov	 DWORD PTR $T2[rsp], eax

; 571  :     HRESULT hr;
; 572  : 
; 573  :     hr = StringValidateDestW(pszDest, cchDest, STRSAFE_MAX_CCH);

  0003e	8b 44 24 38	 mov	 eax, DWORD PTR $T2[rsp]
  00042	89 44 24 30	 mov	 DWORD PTR hr$[rsp], eax

; 574  : 
; 575  :     if (SUCCEEDED(hr))

  00046	83 7c 24 30 00	 cmp	 DWORD PTR hr$[rsp], 0
  0004b	7c 26		 jl	 SHORT $LN2@StringCchC

; 576  :     {
; 577  :         hr = StringCopyWorkerW(pszDest,

  0004d	48 c7 44 24 20
	fe ff ff 7f	 mov	 QWORD PTR [rsp+32], 2147483646 ; 7ffffffeH
  00056	4c 8b 4c 24 60	 mov	 r9, QWORD PTR pszSrc$[rsp]
  0005b	45 33 c0	 xor	 r8d, r8d
  0005e	48 8b 54 24 58	 mov	 rdx, QWORD PTR cchDest$[rsp]
  00063	48 8b 4c 24 50	 mov	 rcx, QWORD PTR pszDest$[rsp]
  00068	e8 00 00 00 00	 call	 ?StringCopyWorkerW@@YAJPEA_W_KPEA_KPEB_W1@Z ; StringCopyWorkerW
  0006d	89 44 24 30	 mov	 DWORD PTR hr$[rsp], eax

; 578  :                 cchDest,
; 579  :                 NULL,
; 580  :                 pszSrc,
; 581  :                 STRSAFE_MAX_LENGTH);
; 582  :     }

  00071	eb 12		 jmp	 SHORT $LN3@StringCchC
$LN2@StringCchC:

; 583  :     else if (cchDest > 0)

  00073	48 83 7c 24 58
	00		 cmp	 QWORD PTR cchDest$[rsp], 0
  00079	76 0a		 jbe	 SHORT $LN4@StringCchC

; 584  :     {
; 585  :         *pszDest = L'\0';

  0007b	33 c0		 xor	 eax, eax
  0007d	48 8b 4c 24 50	 mov	 rcx, QWORD PTR pszDest$[rsp]
  00082	66 89 01	 mov	 WORD PTR [rcx], ax
$LN4@StringCchC:
$LN3@StringCchC:

; 586  :     }
; 587  : 
; 588  :     return hr;

  00085	8b 44 24 30	 mov	 eax, DWORD PTR hr$[rsp]

; 589  : }

  00089	48 83 c4 48	 add	 rsp, 72			; 00000048H
  0008d	c3		 ret	 0
?StringCchCopyW@@YAJPEA_W_KPEB_W@Z ENDP			; StringCchCopyW
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\strsafe.h
;	COMDAT ?StringCchCopyA@@YAJPEAD_KPEBD@Z
_TEXT	SEGMENT
hr$ = 48
hr$1 = 52
$T2 = 56
pszDest$ = 80
cchDest$ = 88
pszSrc$ = 96
?StringCchCopyA@@YAJPEAD_KPEBD@Z PROC			; StringCchCopyA, COMDAT

; 542  : {

$LN16:
  00000	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  00005	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000a	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000f	48 83 ec 48	 sub	 rsp, 72			; 00000048H

; 9667 :     HRESULT hr = S_OK;

  00013	c7 44 24 34 00
	00 00 00	 mov	 DWORD PTR hr$1[rsp], 0

; 9668 : 
; 9669 :     if ((cchDest == 0) || (cchDest > cchMax))

  0001b	48 83 7c 24 58
	00		 cmp	 QWORD PTR cchDest$[rsp], 0
  00021	74 0b		 je	 SHORT $LN8@StringCchC
  00023	48 81 7c 24 58
	ff ff ff 7f	 cmp	 QWORD PTR cchDest$[rsp], 2147483647 ; 7fffffffH
  0002c	76 08		 jbe	 SHORT $LN7@StringCchC
$LN8@StringCchC:

; 9670 :     {
; 9671 :         hr = STRSAFE_E_INVALID_PARAMETER;

  0002e	c7 44 24 34 57
	00 07 80	 mov	 DWORD PTR hr$1[rsp], -2147024809 ; ffffffff80070057H
$LN7@StringCchC:

; 9672 :     }
; 9673 : 
; 9674 :     return hr;

  00036	8b 44 24 34	 mov	 eax, DWORD PTR hr$1[rsp]
  0003a	89 44 24 38	 mov	 DWORD PTR $T2[rsp], eax

; 543  :     HRESULT hr;
; 544  : 
; 545  :     hr = StringValidateDestA(pszDest, cchDest, STRSAFE_MAX_CCH);

  0003e	8b 44 24 38	 mov	 eax, DWORD PTR $T2[rsp]
  00042	89 44 24 30	 mov	 DWORD PTR hr$[rsp], eax

; 546  : 
; 547  :     if (SUCCEEDED(hr))

  00046	83 7c 24 30 00	 cmp	 DWORD PTR hr$[rsp], 0
  0004b	7c 26		 jl	 SHORT $LN2@StringCchC

; 548  :     {
; 549  :         hr = StringCopyWorkerA(pszDest,

  0004d	48 c7 44 24 20
	fe ff ff 7f	 mov	 QWORD PTR [rsp+32], 2147483646 ; 7ffffffeH
  00056	4c 8b 4c 24 60	 mov	 r9, QWORD PTR pszSrc$[rsp]
  0005b	45 33 c0	 xor	 r8d, r8d
  0005e	48 8b 54 24 58	 mov	 rdx, QWORD PTR cchDest$[rsp]
  00063	48 8b 4c 24 50	 mov	 rcx, QWORD PTR pszDest$[rsp]
  00068	e8 00 00 00 00	 call	 ?StringCopyWorkerA@@YAJPEAD_KPEA_KPEBD1@Z ; StringCopyWorkerA
  0006d	89 44 24 30	 mov	 DWORD PTR hr$[rsp], eax

; 550  :                 cchDest,
; 551  :                 NULL,
; 552  :                 pszSrc,
; 553  :                 STRSAFE_MAX_LENGTH);
; 554  :     }

  00071	eb 10		 jmp	 SHORT $LN3@StringCchC
$LN2@StringCchC:

; 555  :     else if (cchDest > 0)

  00073	48 83 7c 24 58
	00		 cmp	 QWORD PTR cchDest$[rsp], 0
  00079	76 08		 jbe	 SHORT $LN4@StringCchC

; 556  :     {
; 557  :         *pszDest = '\0';

  0007b	48 8b 44 24 50	 mov	 rax, QWORD PTR pszDest$[rsp]
  00080	c6 00 00	 mov	 BYTE PTR [rax], 0
$LN4@StringCchC:
$LN3@StringCchC:

; 558  :     }
; 559  : 
; 560  :     return hr;

  00083	8b 44 24 30	 mov	 eax, DWORD PTR hr$[rsp]

; 561  : }

  00087	48 83 c4 48	 add	 rsp, 72			; 00000048H
  0008b	c3		 ret	 0
?StringCchCopyA@@YAJPEAD_KPEBD@Z ENDP			; StringCchCopyA
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\strsafe.h
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_wstdio.h
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\strsafe.h
;	COMDAT ?StringVPrintfWorkerW@@YAJPEA_W_KPEA_KPEB_WPEAD@Z
_TEXT	SEGMENT
iRet$ = 48
_Result$1 = 52
tv129 = 56
hr$ = 60
$T2 = 64
$T3 = 68
cchMax$ = 72
cchNewDestLength$ = 80
pszDest$ = 112
cchDest$ = 120
pcchNewDestLength$ = 128
pszFormat$ = 136
argList$ = 144
?StringVPrintfWorkerW@@YAJPEA_W_KPEA_KPEB_WPEAD@Z PROC	; StringVPrintfWorkerW, COMDAT

; 10128: {

  00000	4c 89 4c 24 20	 mov	 QWORD PTR [rsp+32], r9
  00005	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	48 83 ec 68	 sub	 rsp, 104		; 00000068H

; 10129:     HRESULT hr = S_OK;

  00018	c7 44 24 3c 00
	00 00 00	 mov	 DWORD PTR hr$[rsp], 0

; 10130:     int iRet;
; 10131:     size_t cchMax;
; 10132:     size_t cchNewDestLength = 0;

  00020	48 c7 44 24 50
	00 00 00 00	 mov	 QWORD PTR cchNewDestLength$[rsp], 0

; 10133: 
; 10134:     // leave the last space for the null terminator
; 10135:     cchMax = cchDest - 1;

  00029	48 8b 44 24 78	 mov	 rax, QWORD PTR cchDest$[rsp]
  0002e	48 ff c8	 dec	 rax
  00031	48 89 44 24 48	 mov	 QWORD PTR cchMax$[rsp], rax
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_wstdio.h

; 1062 :         int const _Result = __stdio_common_vswprintf(

  00036	e8 00 00 00 00	 call	 __local_stdio_printf_options
  0003b	48 8b 00	 mov	 rax, QWORD PTR [rax]
  0003e	48 83 c8 01	 or	 rax, 1
  00042	48 8b 8c 24 90
	00 00 00	 mov	 rcx, QWORD PTR argList$[rsp]
  0004a	48 89 4c 24 28	 mov	 QWORD PTR [rsp+40], rcx
  0004f	48 c7 44 24 20
	00 00 00 00	 mov	 QWORD PTR [rsp+32], 0
  00058	4c 8b 8c 24 88
	00 00 00	 mov	 r9, QWORD PTR pszFormat$[rsp]
  00060	4c 8b 44 24 48	 mov	 r8, QWORD PTR cchMax$[rsp]
  00065	48 8b 54 24 70	 mov	 rdx, QWORD PTR pszDest$[rsp]
  0006a	48 8b c8	 mov	 rcx, rax
  0006d	e8 00 00 00 00	 call	 __stdio_common_vswprintf
  00072	89 44 24 34	 mov	 DWORD PTR _Result$1[rsp], eax

; 1063 :             _CRT_INTERNAL_LOCAL_PRINTF_OPTIONS | _CRT_INTERNAL_PRINTF_LEGACY_VSPRINTF_NULL_TERMINATION,
; 1064 :             _Buffer, _BufferCount, _Format, _Locale, _ArgList);
; 1065 : 
; 1066 :         return _Result < 0 ? -1 : _Result;

  00076	83 7c 24 34 00	 cmp	 DWORD PTR _Result$1[rsp], 0
  0007b	7d 0a		 jge	 SHORT $LN13@StringVPri
  0007d	c7 44 24 38 ff
	ff ff ff	 mov	 DWORD PTR tv129[rsp], -1
  00085	eb 08		 jmp	 SHORT $LN14@StringVPri
$LN13@StringVPri:
  00087	8b 44 24 34	 mov	 eax, DWORD PTR _Result$1[rsp]
  0008b	89 44 24 38	 mov	 DWORD PTR tv129[rsp], eax
$LN14@StringVPri:
  0008f	8b 44 24 38	 mov	 eax, DWORD PTR tv129[rsp]
  00093	89 44 24 40	 mov	 DWORD PTR $T2[rsp], eax

; 1130 :         return _vsnwprintf_l(_Buffer, _BufferCount, _Format, NULL, _ArgList);

  00097	8b 44 24 40	 mov	 eax, DWORD PTR $T2[rsp]
  0009b	89 44 24 44	 mov	 DWORD PTR $T3[rsp], eax
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\strsafe.h

; 10142:     iRet = _vsnwprintf(pszDest, cchMax, pszFormat, argList);

  0009f	8b 44 24 44	 mov	 eax, DWORD PTR $T3[rsp]
  000a3	89 44 24 30	 mov	 DWORD PTR iRet$[rsp], eax

; 10143: #pragma warning(pop)
; 10144: #endif
; 10145:     // ASSERT((iRet < 0) || (((size_t)iRet) <= cchMax));
; 10146: 
; 10147:     if ((iRet < 0) || (((size_t)iRet) > cchMax))

  000a7	83 7c 24 30 00	 cmp	 DWORD PTR iRet$[rsp], 0
  000ac	7c 0c		 jl	 SHORT $LN4@StringVPri
  000ae	48 63 44 24 30	 movsxd	 rax, DWORD PTR iRet$[rsp]
  000b3	48 3b 44 24 48	 cmp	 rax, QWORD PTR cchMax$[rsp]
  000b8	76 31		 jbe	 SHORT $LN2@StringVPri
$LN4@StringVPri:

; 10148:     {
; 10149:         // need to null terminate the string
; 10150:         pszDest += cchMax;

  000ba	48 8b 44 24 70	 mov	 rax, QWORD PTR pszDest$[rsp]
  000bf	48 8b 4c 24 48	 mov	 rcx, QWORD PTR cchMax$[rsp]
  000c4	48 8d 04 48	 lea	 rax, QWORD PTR [rax+rcx*2]
  000c8	48 89 44 24 70	 mov	 QWORD PTR pszDest$[rsp], rax

; 10151:         *pszDest = L'\0';

  000cd	33 c0		 xor	 eax, eax
  000cf	48 8b 4c 24 70	 mov	 rcx, QWORD PTR pszDest$[rsp]
  000d4	66 89 01	 mov	 WORD PTR [rcx], ax

; 10152: 
; 10153:         cchNewDestLength = cchMax;

  000d7	48 8b 44 24 48	 mov	 rax, QWORD PTR cchMax$[rsp]
  000dc	48 89 44 24 50	 mov	 QWORD PTR cchNewDestLength$[rsp], rax

; 10154: 
; 10155:         // we have truncated pszDest
; 10156:         hr = STRSAFE_E_INSUFFICIENT_BUFFER;

  000e1	c7 44 24 3c 7a
	00 07 80	 mov	 DWORD PTR hr$[rsp], -2147024774 ; ffffffff8007007aH

; 10157:     }

  000e9	eb 3f		 jmp	 SHORT $LN3@StringVPri
$LN2@StringVPri:

; 10158:     else if (((size_t)iRet) == cchMax)

  000eb	48 63 44 24 30	 movsxd	 rax, DWORD PTR iRet$[rsp]
  000f0	48 3b 44 24 48	 cmp	 rax, QWORD PTR cchMax$[rsp]
  000f5	75 29		 jne	 SHORT $LN5@StringVPri

; 10159:     {
; 10160:         // need to null terminate the string
; 10161:         pszDest += cchMax;

  000f7	48 8b 44 24 70	 mov	 rax, QWORD PTR pszDest$[rsp]
  000fc	48 8b 4c 24 48	 mov	 rcx, QWORD PTR cchMax$[rsp]
  00101	48 8d 04 48	 lea	 rax, QWORD PTR [rax+rcx*2]
  00105	48 89 44 24 70	 mov	 QWORD PTR pszDest$[rsp], rax

; 10162:         *pszDest = L'\0';

  0010a	33 c0		 xor	 eax, eax
  0010c	48 8b 4c 24 70	 mov	 rcx, QWORD PTR pszDest$[rsp]
  00111	66 89 01	 mov	 WORD PTR [rcx], ax

; 10163: 
; 10164:         cchNewDestLength = cchMax;

  00114	48 8b 44 24 48	 mov	 rax, QWORD PTR cchMax$[rsp]
  00119	48 89 44 24 50	 mov	 QWORD PTR cchNewDestLength$[rsp], rax

; 10165:     }

  0011e	eb 0a		 jmp	 SHORT $LN6@StringVPri
$LN5@StringVPri:

; 10166:     else
; 10167:     {
; 10168:         cchNewDestLength = (size_t)iRet;

  00120	48 63 44 24 30	 movsxd	 rax, DWORD PTR iRet$[rsp]
  00125	48 89 44 24 50	 mov	 QWORD PTR cchNewDestLength$[rsp], rax
$LN6@StringVPri:
$LN3@StringVPri:

; 10169:     }
; 10170: 
; 10171:     if (pcchNewDestLength)

  0012a	48 83 bc 24 80
	00 00 00 00	 cmp	 QWORD PTR pcchNewDestLength$[rsp], 0
  00133	74 10		 je	 SHORT $LN7@StringVPri

; 10172:     {
; 10173:         *pcchNewDestLength = cchNewDestLength;

  00135	48 8b 84 24 80
	00 00 00	 mov	 rax, QWORD PTR pcchNewDestLength$[rsp]
  0013d	48 8b 4c 24 50	 mov	 rcx, QWORD PTR cchNewDestLength$[rsp]
  00142	48 89 08	 mov	 QWORD PTR [rax], rcx
$LN7@StringVPri:

; 10174:     }
; 10175: 
; 10176:     return hr;

  00145	8b 44 24 3c	 mov	 eax, DWORD PTR hr$[rsp]

; 10177: }

  00149	48 83 c4 68	 add	 rsp, 104		; 00000068H
  0014d	c3		 ret	 0
?StringVPrintfWorkerW@@YAJPEA_W_KPEA_KPEB_WPEAD@Z ENDP	; StringVPrintfWorkerW
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\strsafe.h
;	COMDAT ?StringCopyWorkerW@@YAJPEA_W_KPEA_KPEB_W1@Z
_TEXT	SEGMENT
hr$ = 0
cchNewDestLength$ = 8
pszDest$ = 32
cchDest$ = 40
pcchNewDestLength$ = 48
pszSrc$ = 56
cchToCopy$ = 64
?StringCopyWorkerW@@YAJPEA_W_KPEA_KPEB_W1@Z PROC	; StringCopyWorkerW, COMDAT

; 9953 : {

  00000	4c 89 4c 24 20	 mov	 QWORD PTR [rsp+32], r9
  00005	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	48 83 ec 18	 sub	 rsp, 24

; 9954 :     HRESULT hr = S_OK;

  00018	c7 04 24 00 00
	00 00		 mov	 DWORD PTR hr$[rsp], 0

; 9955 :     size_t cchNewDestLength = 0;

  0001f	48 c7 44 24 08
	00 00 00 00	 mov	 QWORD PTR cchNewDestLength$[rsp], 0
$LN2@StringCopy:

; 9956 : 
; 9957 :     // ASSERT(cchDest != 0);
; 9958 : 
; 9959 :     while (cchDest && cchToCopy && (*pszSrc != L'\0'))

  00028	48 83 7c 24 28
	00		 cmp	 QWORD PTR cchDest$[rsp], 0
  0002e	74 69		 je	 SHORT $LN3@StringCopy
  00030	48 83 7c 24 40
	00		 cmp	 QWORD PTR cchToCopy$[rsp], 0
  00036	74 61		 je	 SHORT $LN3@StringCopy
  00038	48 8b 44 24 38	 mov	 rax, QWORD PTR pszSrc$[rsp]
  0003d	0f b7 00	 movzx	 eax, WORD PTR [rax]
  00040	85 c0		 test	 eax, eax
  00042	74 55		 je	 SHORT $LN3@StringCopy

; 9960 :     {
; 9961 :         *pszDest++ = *pszSrc++;

  00044	48 8b 44 24 20	 mov	 rax, QWORD PTR pszDest$[rsp]
  00049	48 8b 4c 24 38	 mov	 rcx, QWORD PTR pszSrc$[rsp]
  0004e	0f b7 09	 movzx	 ecx, WORD PTR [rcx]
  00051	66 89 08	 mov	 WORD PTR [rax], cx
  00054	48 8b 44 24 20	 mov	 rax, QWORD PTR pszDest$[rsp]
  00059	48 83 c0 02	 add	 rax, 2
  0005d	48 89 44 24 20	 mov	 QWORD PTR pszDest$[rsp], rax
  00062	48 8b 44 24 38	 mov	 rax, QWORD PTR pszSrc$[rsp]
  00067	48 83 c0 02	 add	 rax, 2
  0006b	48 89 44 24 38	 mov	 QWORD PTR pszSrc$[rsp], rax

; 9962 :         cchDest--;

  00070	48 8b 44 24 28	 mov	 rax, QWORD PTR cchDest$[rsp]
  00075	48 ff c8	 dec	 rax
  00078	48 89 44 24 28	 mov	 QWORD PTR cchDest$[rsp], rax

; 9963 :         cchToCopy--;

  0007d	48 8b 44 24 40	 mov	 rax, QWORD PTR cchToCopy$[rsp]
  00082	48 ff c8	 dec	 rax
  00085	48 89 44 24 40	 mov	 QWORD PTR cchToCopy$[rsp], rax

; 9964 : 
; 9965 :         cchNewDestLength++;

  0008a	48 8b 44 24 08	 mov	 rax, QWORD PTR cchNewDestLength$[rsp]
  0008f	48 ff c0	 inc	 rax
  00092	48 89 44 24 08	 mov	 QWORD PTR cchNewDestLength$[rsp], rax

; 9966 :     }

  00097	eb 8f		 jmp	 SHORT $LN2@StringCopy
$LN3@StringCopy:

; 9967 : 
; 9968 :     if (cchDest == 0)

  00099	48 83 7c 24 28
	00		 cmp	 QWORD PTR cchDest$[rsp], 0
  0009f	75 22		 jne	 SHORT $LN4@StringCopy

; 9969 :     {
; 9970 :         // we are going to truncate pszDest
; 9971 :         pszDest--;

  000a1	48 8b 44 24 20	 mov	 rax, QWORD PTR pszDest$[rsp]
  000a6	48 83 e8 02	 sub	 rax, 2
  000aa	48 89 44 24 20	 mov	 QWORD PTR pszDest$[rsp], rax

; 9972 :         cchNewDestLength--;

  000af	48 8b 44 24 08	 mov	 rax, QWORD PTR cchNewDestLength$[rsp]
  000b4	48 ff c8	 dec	 rax
  000b7	48 89 44 24 08	 mov	 QWORD PTR cchNewDestLength$[rsp], rax

; 9973 : 
; 9974 :         hr = STRSAFE_E_INSUFFICIENT_BUFFER;

  000bc	c7 04 24 7a 00
	07 80		 mov	 DWORD PTR hr$[rsp], -2147024774 ; ffffffff8007007aH
$LN4@StringCopy:

; 9975 :     }
; 9976 : 
; 9977 :     *pszDest = L'\0';

  000c3	33 c0		 xor	 eax, eax
  000c5	48 8b 4c 24 20	 mov	 rcx, QWORD PTR pszDest$[rsp]
  000ca	66 89 01	 mov	 WORD PTR [rcx], ax

; 9978 : 
; 9979 :     if (pcchNewDestLength)

  000cd	48 83 7c 24 30
	00		 cmp	 QWORD PTR pcchNewDestLength$[rsp], 0
  000d3	74 0d		 je	 SHORT $LN5@StringCopy

; 9980 :     {
; 9981 :         *pcchNewDestLength = cchNewDestLength;

  000d5	48 8b 44 24 30	 mov	 rax, QWORD PTR pcchNewDestLength$[rsp]
  000da	48 8b 4c 24 08	 mov	 rcx, QWORD PTR cchNewDestLength$[rsp]
  000df	48 89 08	 mov	 QWORD PTR [rax], rcx
$LN5@StringCopy:

; 9982 :     }
; 9983 : 
; 9984 :     return hr;

  000e2	8b 04 24	 mov	 eax, DWORD PTR hr$[rsp]

; 9985 : }

  000e5	48 83 c4 18	 add	 rsp, 24
  000e9	c3		 ret	 0
?StringCopyWorkerW@@YAJPEA_W_KPEA_KPEB_W1@Z ENDP	; StringCopyWorkerW
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\shared\strsafe.h
;	COMDAT ?StringCopyWorkerA@@YAJPEAD_KPEA_KPEBD1@Z
_TEXT	SEGMENT
hr$ = 0
cchNewDestLength$ = 8
pszDest$ = 32
cchDest$ = 40
pcchNewDestLength$ = 48
pszSrc$ = 56
cchToCopy$ = 64
?StringCopyWorkerA@@YAJPEAD_KPEA_KPEBD1@Z PROC		; StringCopyWorkerA, COMDAT

; 9910 : {

  00000	4c 89 4c 24 20	 mov	 QWORD PTR [rsp+32], r9
  00005	4c 89 44 24 18	 mov	 QWORD PTR [rsp+24], r8
  0000a	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  0000f	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00014	48 83 ec 18	 sub	 rsp, 24

; 9911 :     HRESULT hr = S_OK;

  00018	c7 04 24 00 00
	00 00		 mov	 DWORD PTR hr$[rsp], 0

; 9912 :     size_t cchNewDestLength = 0;

  0001f	48 c7 44 24 08
	00 00 00 00	 mov	 QWORD PTR cchNewDestLength$[rsp], 0
$LN2@StringCopy:

; 9913 : 
; 9914 :     // ASSERT(cchDest != 0);
; 9915 : 
; 9916 :     while (cchDest && cchToCopy && (*pszSrc != '\0'))

  00028	48 83 7c 24 28
	00		 cmp	 QWORD PTR cchDest$[rsp], 0
  0002e	74 66		 je	 SHORT $LN3@StringCopy
  00030	48 83 7c 24 40
	00		 cmp	 QWORD PTR cchToCopy$[rsp], 0
  00036	74 5e		 je	 SHORT $LN3@StringCopy
  00038	48 8b 44 24 38	 mov	 rax, QWORD PTR pszSrc$[rsp]
  0003d	0f be 00	 movsx	 eax, BYTE PTR [rax]
  00040	85 c0		 test	 eax, eax
  00042	74 52		 je	 SHORT $LN3@StringCopy

; 9917 :     {
; 9918 :         *pszDest++ = *pszSrc++;

  00044	48 8b 44 24 20	 mov	 rax, QWORD PTR pszDest$[rsp]
  00049	48 8b 4c 24 38	 mov	 rcx, QWORD PTR pszSrc$[rsp]
  0004e	0f b6 09	 movzx	 ecx, BYTE PTR [rcx]
  00051	88 08		 mov	 BYTE PTR [rax], cl
  00053	48 8b 44 24 20	 mov	 rax, QWORD PTR pszDest$[rsp]
  00058	48 ff c0	 inc	 rax
  0005b	48 89 44 24 20	 mov	 QWORD PTR pszDest$[rsp], rax
  00060	48 8b 44 24 38	 mov	 rax, QWORD PTR pszSrc$[rsp]
  00065	48 ff c0	 inc	 rax
  00068	48 89 44 24 38	 mov	 QWORD PTR pszSrc$[rsp], rax

; 9919 :         cchDest--;

  0006d	48 8b 44 24 28	 mov	 rax, QWORD PTR cchDest$[rsp]
  00072	48 ff c8	 dec	 rax
  00075	48 89 44 24 28	 mov	 QWORD PTR cchDest$[rsp], rax

; 9920 :         cchToCopy--;

  0007a	48 8b 44 24 40	 mov	 rax, QWORD PTR cchToCopy$[rsp]
  0007f	48 ff c8	 dec	 rax
  00082	48 89 44 24 40	 mov	 QWORD PTR cchToCopy$[rsp], rax

; 9921 : 
; 9922 :         cchNewDestLength++;

  00087	48 8b 44 24 08	 mov	 rax, QWORD PTR cchNewDestLength$[rsp]
  0008c	48 ff c0	 inc	 rax
  0008f	48 89 44 24 08	 mov	 QWORD PTR cchNewDestLength$[rsp], rax

; 9923 :     }

  00094	eb 92		 jmp	 SHORT $LN2@StringCopy
$LN3@StringCopy:

; 9924 : 
; 9925 :     if (cchDest == 0)

  00096	48 83 7c 24 28
	00		 cmp	 QWORD PTR cchDest$[rsp], 0
  0009c	75 21		 jne	 SHORT $LN4@StringCopy

; 9926 :     {
; 9927 :         // we are going to truncate pszDest
; 9928 :         pszDest--;

  0009e	48 8b 44 24 20	 mov	 rax, QWORD PTR pszDest$[rsp]
  000a3	48 ff c8	 dec	 rax
  000a6	48 89 44 24 20	 mov	 QWORD PTR pszDest$[rsp], rax

; 9929 :         cchNewDestLength--;

  000ab	48 8b 44 24 08	 mov	 rax, QWORD PTR cchNewDestLength$[rsp]
  000b0	48 ff c8	 dec	 rax
  000b3	48 89 44 24 08	 mov	 QWORD PTR cchNewDestLength$[rsp], rax

; 9930 : 
; 9931 :         hr = STRSAFE_E_INSUFFICIENT_BUFFER;

  000b8	c7 04 24 7a 00
	07 80		 mov	 DWORD PTR hr$[rsp], -2147024774 ; ffffffff8007007aH
$LN4@StringCopy:

; 9932 :     }
; 9933 : 
; 9934 :     *pszDest = '\0';

  000bf	48 8b 44 24 20	 mov	 rax, QWORD PTR pszDest$[rsp]
  000c4	c6 00 00	 mov	 BYTE PTR [rax], 0

; 9935 : 
; 9936 :     if (pcchNewDestLength)

  000c7	48 83 7c 24 30
	00		 cmp	 QWORD PTR pcchNewDestLength$[rsp], 0
  000cd	74 0d		 je	 SHORT $LN5@StringCopy

; 9937 :     {
; 9938 :         *pcchNewDestLength = cchNewDestLength;

  000cf	48 8b 44 24 30	 mov	 rax, QWORD PTR pcchNewDestLength$[rsp]
  000d4	48 8b 4c 24 08	 mov	 rcx, QWORD PTR cchNewDestLength$[rsp]
  000d9	48 89 08	 mov	 QWORD PTR [rax], rcx
$LN5@StringCopy:

; 9939 :     }
; 9940 : 
; 9941 :     return hr;

  000dc	8b 04 24	 mov	 eax, DWORD PTR hr$[rsp]

; 9942 : }

  000df	48 83 c4 18	 add	 rsp, 24
  000e3	c3		 ret	 0
?StringCopyWorkerA@@YAJPEAD_KPEA_KPEBD1@Z ENDP		; StringCopyWorkerA
_TEXT	ENDS
; Function compile flags: /Odtp
; File C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\ucrt\corecrt_stdio_config.h
;	COMDAT __local_stdio_printf_options
_TEXT	SEGMENT
__local_stdio_printf_options PROC			; COMDAT

; 91   :         static unsigned __int64 _OptionsStorage;
; 92   :         return &_OptionsStorage;

  00000	48 8d 05 00 00
	00 00		 lea	 rax, OFFSET FLAT:?_OptionsStorage@?1??__local_stdio_printf_options@@9@4_KA ; `__local_stdio_printf_options'::`2'::_OptionsStorage

; 93   :     }

  00007	c3		 ret	 0
__local_stdio_printf_options ENDP
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\KISS\KISSApiWrapper.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX_K@Z
_TEXT	SEGMENT
__formal$ = 8
__formal$ = 16
?__empty_global_delete@@YAXPEAX_K@Z PROC		; __empty_global_delete, COMDAT

  00000	48 89 54 24 10	 mov	 QWORD PTR [rsp+16], rdx
  00005	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  0000a	c3		 ret	 0
?__empty_global_delete@@YAXPEAX_K@Z ENDP		; __empty_global_delete
_TEXT	ENDS
; Function compile flags: /Odtp
; File F:\Release_Branch\Server\Development\Frontend\WorldServer\KISS\KISSApiWrapper.cpp
;	COMDAT ?__empty_global_delete@@YAXPEAX@Z
_TEXT	SEGMENT
__formal$ = 8
?__empty_global_delete@@YAXPEAX@Z PROC			; __empty_global_delete, COMDAT

  00000	48 89 4c 24 08	 mov	 QWORD PTR [rsp+8], rcx
  00005	c3		 ret	 0
?__empty_global_delete@@YAXPEAX@Z ENDP			; __empty_global_delete
_TEXT	ENDS
END
